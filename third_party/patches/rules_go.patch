diff --git proto/def.bzl proto/def.bzl
index b3fad3d3..d1b64835 100644
--- proto/def.bzl
+++ proto/def.bzl
@@ -140,9 +140,13 @@ def _go_proto_library_impl(ctx):
             importpath = go.importpath,
         ))

+    importpath = None
+    if ctx.attr.overrideimportpath:
+        importpath = ctx.attr.overrideimportpath
     go_info = new_go_info(
         go,
         ctx.attr,
+        importpath = importpath,
         resolver = _proto_library_to_source,
         generated_srcs = go_srcs,
         coverage_instrumented = False,
@@ -182,6 +186,7 @@ go_proto_library = rule(
         "importpath": attr.string(),
         "importmap": attr.string(),
         "importpath_aliases": attr.string_list(),  # experimental, undocumented
+        "overrideimportpath": attr.string(),
         "embed": attr.label_list(providers = [GoInfo]),
         "gc_goopts": attr.string_list(),
         "compiler": attr.label(providers = [GoProtoCompiler]),