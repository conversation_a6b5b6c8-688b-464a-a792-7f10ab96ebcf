[package]
name = "ginepro"
version = "0.8.2"
edition = "2021"
description = "A client-side gRPC channel implementation for tonic"
repository = "https://github.com/TrueLayer/ginepro"
license = "MIT OR Apache-2.0"
keywords = ["gRPC", "tonic", "channel", "load", "balancer"]
categories = ["asynchronous", "web-programming"]

[dependencies]
anyhow = "1"
async-trait = { workspace = true }
http = { workspace = true }
thiserror = "1"
tokio = { workspace = true }
tonic = { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
hickory-resolver = { version = "0.24", features = ["tokio-runtime"] }
