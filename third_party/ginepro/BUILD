load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_library")

rust_library(
    name = "ginepro",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    format = False,  # TODO: get tools:format to find this directory
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//visibility:public"],
    deps = all_crate_deps(
        normal = True,
    ),
)
