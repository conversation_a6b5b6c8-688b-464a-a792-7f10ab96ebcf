"""Tests for the Forger prompt formatter."""

import pytest
from textwrap import dedent

from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.prompt_format_smart_paste.forger_prompt_formatter import (
    ForgerPromptFormatter,
    ForgerSmartPasteTokenApportionment,
)
from base.prompt_format_chat.smart_paste_prompt_formatter import (
    SmartPastePromptInput,
)
from base.prompt_format_chat.prompt_formatter import Exceed<PERSON>ontextLength
from base.prompt_format.common import Exchange


@pytest.fixture()
def prompt_formatter():
    """Returns a forger prompt formatter."""
    token_apportionment = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=512,
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
    )
    tokenizer = StarCoder2Tokenizer()
    return ForgerPromptFormatter(tokenizer, token_apportionment)


@pytest.fixture()
def fuzzy_noprefix_prompt_formatter():
    """Returns a forger prompt formatter with fuzzy search enabled and no prefix."""
    token_apportionment = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=0,  # No prefix
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
        enable_fuzzy_search=True,
    )
    tokenizer = StarCoder2Tokenizer()
    return ForgerPromptFormatter(tokenizer, token_apportionment)


def test_forger_prompt_formatter_example_without_instruction(prompt_formatter):
    """This is a simple sanity check to catch obvious bugs in prompt formatting."""
    example_input = SmartPastePromptInput(
        path="src/example.py",
        prefix='def aggregate(a, b):\n    """this is a docstring."""\n',
        selected_code="    some_function(a, b)\n",
        suffix="    return aggregated_output\n",
        code_block="another_function(a, b)\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/example.py",
        target_file_content=dedent("""\
            def aggregate(a, b):
                \"\"\"this is a docstring.\"\"\"
                some_function(a, b)
                return aggregated_output
        """),
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/example.py
        <fim_prefix>
        def aggregate(a, b):
            \"\"\"this is a docstring.\"\"\"
        <|nearby_prefix|>    some_function(a, b)
        <|nearby_suffix|>    return aggregated_output

        <fim_suffix>
        another_function(a, b)

        <fim_middle>
        """)
    assert prompt_formatter.tokenizer.detokenize(prompt.tokens) == expected_prompt
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 4
    assert prompt.used_lines == [
        "def aggregate(a, b):\n",
        '    """this is a docstring."""\n',
        "    some_function(a, b)\n",
        "    return aggregated_output\n",
    ]


def test_forger_prompt_formatter_example_without_instruction_2(prompt_formatter):
    """This is a simple sanity check to catch obvious bugs in prompt formatting."""
    example_input = SmartPastePromptInput(
        path="src/example.py",
        prefix='def aggregate(a, b):\n    """this is a docstring."""\n',
        selected_code="    some_function(a, b)",
        suffix="\n    return aggregated_output\n",
        code_block="another_function(a, b)\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/example.py",
        target_file_content=dedent("""\
            def aggregate(a, b):
                \"\"\"this is a docstring.\"\"\"
                some_function(a, b)
                return aggregated_output
        """),
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/example.py
        <fim_prefix>
        def aggregate(a, b):
            \"\"\"this is a docstring.\"\"\"
        <|nearby_prefix|>    some_function(a, b)
        <|nearby_suffix|>    return aggregated_output

        <fim_suffix>
        another_function(a, b)

        <fim_middle>
        """)
    assert prompt_formatter.tokenizer.detokenize(prompt.tokens) == expected_prompt
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 4
    assert prompt.used_lines == [
        "def aggregate(a, b):\n",
        '    """this is a docstring."""\n',
        "    some_function(a, b)\n",
        "    return aggregated_output\n",
    ]


def test_forger_prompt_formatter_example_with_instruction(
    prompt_formatter,
):
    """This is a simple sanity check to catch obvious bugs in prompt formatting."""
    example_input = SmartPastePromptInput(
        path="src/example.py",
        prefix='def aggregate(a, b):\n    """this is a docstring."""\n',
        selected_code="    some_function(a, b)\n",
        suffix="    return aggregated_output\n",
        code_block="another_function(a, b)\n",
        chat_history=[
            Exchange(
                request_message="Replace some_function with another_function.",
                response_text="",
            ),
        ],
        retrieved_chunks=[],
        target_path="src/example.py",
        target_file_content=dedent("""\
            def aggregate(a, b):
                \"\"\"this is a docstring.\"\"\"
                some_function(a, b)
                return aggregated_output
        """),
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/example.py
        <pr>Replace some_function with another_function.
        <fim_prefix>
        def aggregate(a, b):
            \"\"\"this is a docstring.\"\"\"
        <|nearby_prefix|>    some_function(a, b)
        <|nearby_suffix|>    return aggregated_output

        <fim_suffix>
        another_function(a, b)

        <fim_middle>
        """)
    assert prompt_formatter.tokenizer.detokenize(prompt.tokens) == expected_prompt
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 4
    assert prompt.used_lines == [
        "def aggregate(a, b):\n",
        '    """this is a docstring."""\n',
        "    some_function(a, b)\n",
        "    return aggregated_output\n",
    ]


def test_forger_prompt_formatter_different_target_path(prompt_formatter):
    """Test when target path is different from the original path."""
    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix='def original_function():\n    """Original docstring."""\n',
        selected_code="    print('Original code')\n",
        suffix="    return None\n",
        code_block="print('New code to be inserted')\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/target.py",
        target_file_content=dedent("""\
            def target_function():
                \"\"\"Target docstring.\"\"\"
                print('Target code')
                return True"""),
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/target.py
        <fim_prefix>
        def target_function():
            \"\"\"Target docstring.\"\"\"
            print('Target code')
            return True
        <fim_suffix>
        print('New code to be inserted')

        <fim_middle>
        """)
    assert prompt_formatter.tokenizer.detokenize(prompt.tokens) == expected_prompt
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 4
    assert prompt.used_lines == [
        "def target_function():\n",
        '    """Target docstring."""\n',
        "    print('Target code')\n",
        "    return True",
    ]


def test_forger_prompt_formatter_different_target_path_and_content(prompt_formatter):
    """Test when target path is different and file content has changed."""
    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix='def original_function():\n    """Original docstring."""\n',
        selected_code="    print('Original code')\n",
        suffix="    return None\n",
        code_block="print('New code to be inserted')\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/target.py",
        target_file_content=dedent("""\
            def modified_function():
                \"\"\"Modified docstring.\"\"\"
                print('Modified code')
                return False"""),
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/target.py
        <fim_prefix>
        def modified_function():
            \"\"\"Modified docstring.\"\"\"
            print('Modified code')
            return False
        <fim_suffix>
        print('New code to be inserted')

        <fim_middle>
        """)
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert (
        actual_prompt == expected_prompt
    ), f"\nExpected:\n{expected_prompt}\n\nActual:\n{actual_prompt}"
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 4
    assert prompt.used_lines == [
        "def modified_function():\n",
        '    """Modified docstring."""\n',
        "    print('Modified code')\n",
        "    return False",
    ]


def test_forger_prompt_formatter_with_long_chat_history(prompt_formatter):
    """Test with a long chat history to ensure it's truncated correctly."""
    long_chat_history = [
        Exchange(request_message=f"Message {i}", response_text="Response")
        for i in range(10)
    ]
    example_input = SmartPastePromptInput(
        path="src/long_chat.py",
        prefix="def long_chat_function():\n",
        selected_code="    print('Selected code')\n",
        suffix="    return\n",
        code_block="print('New code')\n",
        chat_history=long_chat_history,
        retrieved_chunks=[],
        target_path="src/long_chat.py",
        target_file_content="def long_chat_function():\n    print('Selected code')\n    return\n",
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert "<pr>Message 9" in actual_prompt
    assert "<pr>Message 0" not in actual_prompt
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 3
    assert prompt.used_lines == [
        "def long_chat_function():\n",
        "    print('Selected code')\n",
        "    return\n",
    ]


def test_forger_prompt_formatter_with_unicode_and_special_characters(prompt_formatter):
    """Test handling of Unicode and special characters in the code."""
    example_input = SmartPastePromptInput(
        path="src/unicode_test.py",
        prefix='def unicode_function():\n    print("こんにちは")\n',
        selected_code='    print("🌍")\n',
        suffix='    print("\\n\\t\\r")\n',
        code_block='print("Новый код")\n',
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/unicode_test.py",
        target_file_content='def unicode_function():\n    print("こんにちは")\n    print("🌍")\n    print("\\n\\t\\r")\n',
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/unicode_test.py
        <fim_prefix>
        def unicode_function():
            print("こんにちは")
        <|nearby_prefix|>    print("🌍")
        <|nearby_suffix|>    print("\\n\\t\\r")

        <fim_suffix>
        print("Новый код")

        <fim_middle>
        """)
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert (
        actual_prompt == expected_prompt
    ), f"\nExpected:\n{expected_prompt}\n\nActual:\n{actual_prompt}"
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 4
    assert prompt.used_lines == [
        "def unicode_function():\n",
        '    print("こんにちは")\n',
        '    print("🌍")\n',
        '    print("\\n\\t\\r")\n',
    ]


def test_forger_prompt_formatter_with_empty_target_file(prompt_formatter):
    """Test when the target file is empty."""
    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix='def original_function():\n    """Original docstring."""\n',
        selected_code="    print('Original code')\n",
        suffix="    return None\n",
        code_block="print('New code to be inserted')\n",
        chat_history=[
            Exchange(
                request_message="Replace some_function with another_function.",
                response_text="",
            ),
        ],
        retrieved_chunks=[],
        target_path="src/empty_target.py",
        target_file_content="",
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/empty_target.py
        <pr>Replace some_function with another_function.
        <fim_prefix>

        <fim_suffix>
        print('New code to be inserted')

        <fim_middle>
        """)
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert (
        actual_prompt == expected_prompt
    ), f"\nExpected:\n{expected_prompt}\n\nActual:\n{actual_prompt}"
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 0
    assert prompt.used_lines == []


def test_forger_prompt_formatter_with_empty_target_file_and_prefix(prompt_formatter):
    """Test when the target file is empty and there's a prefix in the input."""
    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix="",
        selected_code="",
        suffix="",
        code_block="print('New code to be inserted')\n",
        chat_history=[
            Exchange(
                request_message="Replace some_function with another_function.",
                response_text="",
            ),
        ],
        retrieved_chunks=[],
        target_path="src/empty_target_with_prefix.py",
        target_file_content="",
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/empty_target_with_prefix.py
        <pr>Replace some_function with another_function.
        <fim_prefix>

        <fim_suffix>
        print('New code to be inserted')

        <fim_middle>
        """)
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert (
        actual_prompt == expected_prompt
    ), f"\nExpected:\n{expected_prompt}\n\nActual:\n{actual_prompt}"
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 0
    assert prompt.used_lines == []


def test_forger_prompt_formatter_with_windows_line_endings(prompt_formatter):
    """Test handling of Windows (CRLF) line endings in the target file."""
    windows_content = (
        "def windows_function():\r\n    print('Hello')\r\n    return True\r\n"
    )
    example_input = SmartPastePromptInput(
        path="src/windows.py",
        prefix="def original_function():\n",
        selected_code="    print('Original')\n",
        suffix="    return None\n",
        code_block="print('New code')\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/windows.py",
        target_file_content=windows_content,
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)

    # Check if line endings were normalized in the prompt
    expected_prompt = (
        "<file_sep>src/windows.py\n"
        "<fim_prefix>\n"
        "def windows_function():\n"
        "    print('Hello')\n"
        "    return True\n"
        "\n"
        "<fim_suffix>\n"
        "print('New code')\n"
        "\n"
        "<fim_middle>\n"
    )
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert (
        "\r\n" not in actual_prompt
    ), "Windows line endings (CRLF) found in actual prompt"
    assert actual_prompt == expected_prompt

    # Verify original line ending type was preserved
    assert prompt.original_line_ending == "CRLF"

    # Verify line counts are correct despite different line endings
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 3
    assert prompt.used_lines == [
        "def windows_function():\n",
        "    print('Hello')\n",
        "    return True\n",
    ]


def test_forger_prompt_formatter_with_mixed_line_endings(prompt_formatter):
    """Test handling of mixed line endings in the target file."""
    mixed_content = "def mixed_function():\n    print('Unix')\r\n    return True\n"
    example_input = SmartPastePromptInput(
        path="src/mixed.py",
        prefix="def prefix_func():\r\n",  # Windows
        selected_code="    print('Selected')\n",  # Unix
        suffix="    return None\r\n",  # Windows
        code_block="print('New code')\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/mixed.py",
        target_file_content=mixed_content,
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)

    # Check if line endings were normalized in the prompt
    expected_prompt = (
        "<file_sep>src/mixed.py\n"
        "<fim_prefix>\n"
        "def mixed_function():\n"
        "    print('Unix')\n"
        "    return True\n"
        "\n"
        "<fim_suffix>\n"
        "print('New code')\n"
        "\n"
        "<fim_middle>\n"
    )
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)

    # Verify no Windows line endings in the actual prompt
    assert (
        "\r\n" not in actual_prompt
    ), "Windows line endings (CRLF) found in actual prompt"

    assert (
        actual_prompt == expected_prompt
    ), f"\nExpected:\n{expected_prompt}\n\nActual:\n{actual_prompt}"

    # Since the file has mixed line endings but contains at least one CRLF,
    # we consider it a Windows-style file
    assert prompt.original_line_ending == "CRLF"

    # Verify line counts are correct
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 3
    assert prompt.used_lines == [
        "def mixed_function():\n",
        "    print('Unix')\n",
        "    return True\n",
    ]


def test_forger_prompt_formatter_with_unix_line_endings(prompt_formatter):
    """Test handling of Unix (LF) line endings in the target file."""
    unix_content = "def unix_function():\n    print('Unix')\n    return True\n"
    example_input = SmartPastePromptInput(
        path="src/unix.py",
        prefix="def prefix_func():\n",
        selected_code="    print('Selected')\n",
        suffix="    return None\n",
        code_block="print('New code')\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/unix.py",
        target_file_content=unix_content,
        prefix_begin=0,
        suffix_end=0,
    )
    prompt = prompt_formatter.format_prompt(example_input)

    # Check if line endings remained as Unix style
    expected_prompt = (
        "<file_sep>src/unix.py\n"
        "<fim_prefix>\n"
        "def unix_function():\n"
        "    print('Unix')\n"
        "    return True\n"
        "\n"
        "<fim_suffix>\n"
        "print('New code')\n"
        "\n"
        "<fim_middle>\n"
    )
    actual_prompt = prompt_formatter.tokenizer.detokenize(prompt.tokens)

    assert (
        actual_prompt == expected_prompt
    ), f"\nExpected:\n{expected_prompt}\n\nActual:\n{actual_prompt}"

    # Verify original line ending type was preserved
    assert prompt.original_line_ending == "LF"

    # Verify line counts
    assert prompt.start_line_number == 0
    assert prompt.end_line_number == 3
    assert prompt.used_lines == [
        "def unix_function():\n",
        "    print('Unix')\n",
        "    return True\n",
    ]


def test_fuzzy_locate_basic(fuzzy_noprefix_prompt_formatter):
    """Test basic fuzzy search functionality."""
    target_content = dedent("""\
        def example():
            print("Hello")
            return True

        def another():
            print("World")
            return False
    """)

    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix="",
        selected_code="",
        suffix="",
        code_block=dedent("""\
            def another():
                print("World")
                return True
        """),
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/target.py",
        target_file_content=target_content,
        prefix_begin=0,
        suffix_end=0,
    )

    prompt = fuzzy_noprefix_prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/target.py
        <fim_prefix>
        def another():
            print("World")
            return False

        <fim_suffix>
        def another():
            print("World")
            return True

        <fim_middle>
        """)
    actual_prompt = fuzzy_noprefix_prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert actual_prompt == expected_prompt


def test_fuzzy_locate_with_different_indentation(fuzzy_noprefix_prompt_formatter):
    """Test fuzzy search with different indentation levels."""
    target_content = dedent("""\
        class Example:
            def method(self):
                if True:
                    print("Deep")
                    return True
    """)

    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix="",
        selected_code="",
        suffix="",
        code_block=dedent("""\
            if True:
                print("Deep")
                return False
        """),
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/target.py",
        target_file_content=target_content,
        prefix_begin=0,
        suffix_end=0,
    )

    prompt = fuzzy_noprefix_prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/target.py
        <fim_prefix>
                if True:
                    print("Deep")
                    return True

        <fim_suffix>
        if True:
            print("Deep")
            return False

        <fim_middle>
        """)
    actual_prompt = fuzzy_noprefix_prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert actual_prompt == expected_prompt


def test_fuzzy_locate_with_unicode(fuzzy_noprefix_prompt_formatter):
    """Test fuzzy search with Unicode content."""
    target_content = dedent("""\
        def greet():
            print("Hello")

        def приветствие():
            print("Привет")
            return "🌍"

        def sayōnara():
            print("さようなら")
    """)

    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix="",
        selected_code="",
        suffix="",
        code_block=dedent("""\
            def приветствие():
                return None
        """),
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/target.py",
        target_file_content=target_content,
        prefix_begin=0,
        suffix_end=0,
    )

    prompt = fuzzy_noprefix_prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/target.py
        <fim_prefix>
        def приветствие():
            print("Привет")
            return "🌍"

        def sayōnara():
            print("さようなら")

        <fim_suffix>
        def приветствие():
            return None

        <fim_middle>
        """)
    actual_prompt = fuzzy_noprefix_prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert actual_prompt == expected_prompt


def test_fuzzy_locate_no_match(fuzzy_noprefix_prompt_formatter):
    """Test fuzzy search when there's no match."""
    target_content = dedent("""\
        def example():
            print("Hello")
            return True
    """)

    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix="",
        selected_code="",
        suffix="",
        code_block=dedent("""\
            def non_existent():
                print("Not here")
                return None
        """),
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/target.py",
        target_file_content=target_content,
        prefix_begin=0,
        suffix_end=0,
    )

    prompt = fuzzy_noprefix_prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/target.py
        <fim_prefix>
        def example():
            print("Hello")
            return True

        <fim_suffix>
        def non_existent():
            print("Not here")
            return None

        <fim_middle>
        """)
    actual_prompt = fuzzy_noprefix_prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert actual_prompt == expected_prompt


def test_fuzzy_locate_multiple_similar_matches(fuzzy_noprefix_prompt_formatter):
    """Test fuzzy search with multiple similar potential matches."""
    target_content = dedent("""\
        def process_data(data):
            return data + 1

        def process_list(items):
            return [x + 1 for x in items]

        def process_dict(d):
            return {k: v + 1 for k, v in d.items()}
    """)

    example_input = SmartPastePromptInput(
        path="src/original.py",
        prefix="",
        selected_code="",
        suffix="",
        code_block=dedent("""\
            def process_list(items):
                return [x + 1 for x in items]
        """),
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/target.py",
        target_file_content=target_content,
        prefix_begin=0,
        suffix_end=0,
    )

    prompt = fuzzy_noprefix_prompt_formatter.format_prompt(example_input)
    expected_prompt = dedent("""\
        <file_sep>src/target.py
        <fim_prefix>
        def process_list(items):
            return [x + 1 for x in items]

        def process_dict(d):
            return {k: v + 1 for k, v in d.items()}

        <fim_suffix>
        def process_list(items):
            return [x + 1 for x in items]

        <fim_middle>
        """)
    actual_prompt = fuzzy_noprefix_prompt_formatter.tokenizer.detokenize(prompt.tokens)
    assert actual_prompt == expected_prompt


def test_forger_prompt_formatter_drop_user_instruction():
    """Test that drop_user_instruction in token apportionment works as intended."""
    # Create two prompt formatters with different drop_user_instruction settings
    token_apportionment_with_instruction = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=512,
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
        drop_user_instruction=False,
    )
    token_apportionment_without_instruction = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=512,
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
        drop_user_instruction=True,
    )

    tokenizer = StarCoder2Tokenizer()
    formatter_with_instruction = ForgerPromptFormatter(
        tokenizer, token_apportionment_with_instruction
    )
    formatter_without_instruction = ForgerPromptFormatter(
        tokenizer, token_apportionment_without_instruction
    )

    # Create a test input with chat history
    example_input = SmartPastePromptInput(
        path="src/test.py",
        prefix="def test_function():\n",
        selected_code="    print('test')\n",
        suffix="    return True\n",
        code_block="print('new code')\n",
        chat_history=[
            Exchange(
                request_message="Replace the print statement.",
                response_text="",
            ),
        ],
        retrieved_chunks=[],
        target_path="src/test.py",
        target_file_content="def test_function():\n    print('test')\n    return True\n",
        prefix_begin=0,
        suffix_end=0,
    )

    # Get prompts from both formatters
    prompt_with_instruction = formatter_with_instruction.format_prompt(example_input)
    prompt_without_instruction = formatter_without_instruction.format_prompt(
        example_input
    )

    # The prompt with instruction should contain the instruction
    actual_with_instruction = formatter_with_instruction.tokenizer.detokenize(
        prompt_with_instruction.tokens
    )
    assert "<pr>Replace the print statement." in actual_with_instruction

    # The prompt without instruction should not contain the instruction
    actual_without_instruction = formatter_without_instruction.tokenizer.detokenize(
        prompt_without_instruction.tokens
    )
    assert "<pr>Replace the print statement." not in actual_without_instruction

    # Both prompts should contain the rest of the content
    for prompt in [actual_with_instruction, actual_without_instruction]:
        assert "<file_sep>src/test.py" in prompt
        assert "def test_function():" in prompt
        assert "print('test')" in prompt
        assert "return True" in prompt
        assert "print('new code')" in prompt


def test_forger_prompt_formatter_suggested_edit_type():
    """Test that suggested_edit_type in token apportionment works as intended."""
    # Create two prompt formatters with different suggested_edit_type settings
    token_apportionment_codeblock = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=512,
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
        suggested_edit_type="codeblock",
    )
    token_apportionment_response = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=512,
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
        suggested_edit_type="response",
    )

    tokenizer = StarCoder2Tokenizer()
    formatter_codeblock = ForgerPromptFormatter(
        tokenizer, token_apportionment_codeblock
    )
    formatter_response = ForgerPromptFormatter(tokenizer, token_apportionment_response)

    # Create a test input with chat history containing a response with code
    example_input = SmartPastePromptInput(
        path="src/test.py",
        prefix="def test_function():\n",
        selected_code="    print('test')\n",
        suffix="    return True\n",
        code_block="print('direct code block')\n",
        chat_history=[
            Exchange(
                request_message="Replace the print statement.",
                response_text="""Here's the implementation:
```python
print('code from chat response')
```
This will print the message.""",
            ),
        ],
        retrieved_chunks=[],
        target_path="src/test.py",
        target_file_content="def test_function():\n    print('test')\n    return True\n",
        prefix_begin=0,
        suffix_end=0,
    )

    # Get prompts from both formatters
    prompt_codeblock = formatter_codeblock.format_prompt(example_input)
    prompt_response = formatter_response.format_prompt(example_input)

    # Get the detokenized prompts
    actual_codeblock = formatter_codeblock.tokenizer.detokenize(prompt_codeblock.tokens)
    actual_response = formatter_response.tokenizer.detokenize(prompt_response.tokens)

    # Both prompts should contain the common content
    for prompt in [actual_codeblock, actual_response]:
        assert "<file_sep>src/test.py" in prompt
        assert "def test_function():" in prompt
        assert "print('test')" in prompt
        assert "return True" in prompt

    # Verify that the code blocks are different between the two prompts
    assert "print('direct code block')" in actual_codeblock
    assert "print('code from chat response')" in actual_response

    # Test with an invalid suggested_edit_type
    token_apportionment_invalid = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=512,
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
        suggested_edit_type="invalid",
    )
    formatter_invalid = ForgerPromptFormatter(tokenizer, token_apportionment_invalid)
    with pytest.raises(ValueError, match="Invalid suggested_edit_type: invalid"):
        formatter_invalid.format_prompt(example_input)


def test_forger_prompt_formatter_token_budget_handling():
    """Test that token budget is properly handled and distributed."""
    # Create a formatter with very limited token budgets
    token_apportionment = ForgerSmartPasteTokenApportionment(
        path_len=10,
        prefix_len=20,
        suffix_len=20,
        max_prompt_len=100,
        message_len=10,
    )
    tokenizer = StarCoder2Tokenizer()
    formatter = ForgerPromptFormatter(tokenizer, token_apportionment)

    # Create a test input with content that would exceed the budgets
    example_input = SmartPastePromptInput(
        path="src/very/long/path/that/exceeds/budget/test.py",
        prefix='def very_long_prefix_function():\n    """Very long docstring that should be truncated."""\n',
        selected_code="    print('Selected code')\n",
        suffix="    # Very long suffix comment that should also be truncated\n    return True\n",
        code_block="print('New code')\n",
        chat_history=[
            Exchange(
                request_message="This is a very long instruction that should be truncated by the token budget limits.",
                response_text="",
            ),
        ],
        retrieved_chunks=[],
        target_path="src/test.py",
        target_file_content=dedent("""\
            def very_long_prefix_function():
                \"\"\"Very long docstring that should be truncated.\"\"\"
                print('Selected code')
                # Very long suffix comment that should also be truncated
                return True
        """),
        prefix_begin=0,
        suffix_end=0,
    )

    prompt = formatter.format_prompt(example_input)
    actual_prompt = formatter.tokenizer.detokenize(prompt.tokens)

    # Verify that the total token count is within budget
    assert len(prompt.tokens) <= token_apportionment.max_prompt_len

    # Verify that path was truncated
    assert "src/test.py" in actual_prompt
    assert "src/very/long/path/that/exceeds/budget/test.py" not in actual_prompt

    # Verify that instruction was truncated
    assert "This is a very" in actual_prompt
    assert "should be truncated by the token budget limits" not in actual_prompt


def test_forger_prompt_formatter_exceeding_context_length():
    """Test that appropriate error is raised when context length is exceeded."""
    # Create a formatter with very small max_prompt_len
    token_apportionment = ForgerSmartPasteTokenApportionment(
        path_len=10,  # Very small limits
        prefix_len=10,
        suffix_len=10,
        max_prompt_len=20,  # Extremely small limit
        message_len=10,
    )
    tokenizer = StarCoder2Tokenizer()
    formatter = ForgerPromptFormatter(tokenizer, token_apportionment)

    # Create a test input with content that would definitely exceed the small limit
    long_code = (
        "    print('This is a very long line of code that will definitely exceed the token limit')\n"
        * 5
    )
    example_input = SmartPastePromptInput(
        path="src/test.py",
        prefix='def test_function():\n    """Very long docstring."""\n' + long_code,
        selected_code=long_code,  # Add long selected code
        suffix=long_code + "    return True\n",
        code_block="print('New code')\n",
        chat_history=[],
        retrieved_chunks=[],
        target_path="src/test.py",
        target_file_content=dedent(
            """\
            def test_function():
                \"\"\"Very long docstring.\"\"\"
            """
            + long_code * 3
            + "    return True\n"
        ),
        prefix_begin=0,
        suffix_end=0,
    )

    # Verify that ExceedContextLength is raised
    with pytest.raises(ExceedContextLength):
        formatter.format_prompt(example_input)


def test_forger_prompt_formatter_response_with_stripped_metadata():
    """Test that response_with_stripped_metadata edit type works correctly."""
    token_apportionment = ForgerSmartPasteTokenApportionment(
        path_len=256,
        prefix_len=512,
        suffix_len=512,
        max_prompt_len=4096,
        message_len=64,
        suggested_edit_type="response_with_stripped_metadata",
    )
    tokenizer = StarCoder2Tokenizer()
    formatter = ForgerPromptFormatter(tokenizer, token_apportionment)

    example_input = SmartPastePromptInput(
        path="src/test.py",
        prefix="def test_function():\n",
        selected_code="    print('test')\n",
        suffix="    return True\n",
        code_block="print('direct code block')\n",
        chat_history=[
            Exchange(
                request_message="Replace the print statement.",
                response_text="""Here's the implementation:
```python
print('code from chat response')  # With comment
```
This will print the message.""",
            ),
        ],
        retrieved_chunks=[],
        target_path="src/test.py",
        target_file_content="def test_function():\n    print('test')\n    return True\n",
        prefix_begin=0,
        suffix_end=0,
    )

    prompt = formatter.format_prompt(example_input)
    actual_prompt = formatter.tokenizer.detokenize(prompt.tokens)

    # Verify that code block is included without metadata and surrounding text
    assert "print('code from chat response')" in actual_prompt
    assert "# With comment" in actual_prompt
    assert "<fim_suffix>" in actual_prompt
    assert "print('code from chat response')  # With comment" in actual_prompt
