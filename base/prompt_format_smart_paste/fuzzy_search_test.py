"""Tests for fuzzy_search module."""

from base.prompt_format_smart_paste.fuzzy_search import (
    fast_hash,
    compute_lcs,
    fuzzy_locate_snippet,
)


def normalize_line_endings(text: str) -> str:
    """Normalize line endings to LF."""
    return text.replace("\r\n", "\n")


def test_fast_hash():
    """Test the fast_hash function."""
    # Test basic hashing
    assert fast_hash("hello") != fast_hash("world")
    assert fast_hash("hello") == fast_hash("hello")

    # Test with leading whitespace
    assert fast_hash("  hello", ignore_leading_whitespace=False) != fast_hash("hello")
    assert fast_hash("  hello", ignore_leading_whitespace=True) == fast_hash("hello")
    assert fast_hash("\thello", ignore_leading_whitespace=True) == fast_hash("hello")

    # Test empty string
    assert fast_hash("") == 0x811C9DC5  # FNV offset basis


def test_compute_lcs():
    """Test the compute_lcs function."""
    # Test exact match
    doc = [1, 2, 3, 4]
    pattern = [1, 2, 3, 4]
    max_lcs, min_sub, end_idx = compute_lcs(doc, pattern)
    assert max_lcs == 4
    assert min_sub == 4
    assert end_idx == 4

    # Test subsequence match
    doc = [1, 2, 3, 4, 5]
    pattern = [2, 4]
    max_lcs, min_sub, end_idx = compute_lcs(doc, pattern)
    assert max_lcs == 2
    assert min_sub == 3
    assert end_idx == 4

    # Test no match
    doc = [1, 2, 3]
    pattern = [4, 5, 6]
    max_lcs, min_sub, end_idx = compute_lcs(doc, pattern)
    assert max_lcs == 0


def test_fuzzy_locate_snippet():
    """Test the fuzzy_locate_snippet function."""
    # Test exact match
    file_content = "def hello():\n    print('hello')\n    return True"
    pattern = "def hello():\n    print('hello')"
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result == (0, 1)

    # Test match with different indentation
    file_content = "def hello():\n    print('hello')\n    return True"
    pattern = "def hello():\n  print('hello')"  # Different indentation
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result == (0, 1)

    # Test match in middle of file
    file_content = "# Header\ndef hello():\n    print('hello')\n# Footer"
    pattern = "def hello():\n    print('hello')"
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result == (1, 2)

    # Test no match
    file_content = "def hello():\n    print('hello')"
    pattern = "def goodbye():\n    print('goodbye')"
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is None

    # Test empty pattern
    file_content = "def hello():\n    print('hello')"
    pattern = ""
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is None

    # Test empty file
    file_content = ""
    pattern = "def hello()"
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is None


def test_fuzzy_locate_snippet_with_complex_code():
    """Test fuzzy_locate_snippet with more complex code examples."""
    file_content = '''
def calculate_sum(a: int, b: int) -> int:
    """Calculate the sum of two integers."""
    result = a + b
    return result

def multiply(x: int, y: int) -> int:
    """Multiply two integers."""
    return x * y

class Calculator:
    def __init__(self):
        self.history = []

    def add(self, a: int, b: int) -> int:
        result = calculate_sum(a, b)
        self.history.append(result)
        return result
'''.strip()

    # Test finding a function with its docstring
    pattern = '''
def multiply(x: int, y: int) -> int:
    """Multiply two integers."""
    return x * y
'''.strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None
    start, end = result
    assert file_content.split("\n")[start : end + 1] == pattern.split("\n")

    # Test finding a class method with different indentation
    pattern = """
  def add(self, a: int, b: int) -> int:
      result = calculate_sum(a, b)
      self.history.append(result)
      return result
""".strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None


def test_fuzzy_locate_snippet_with_mixed_line_endings():
    """Test fuzzy_locate_snippet with mixed line endings (CRLF and LF)."""
    # Create content with mixed line endings
    file_content = "def test1():\r\n    pass\n\ndef test2():\r\n    return True"

    # Test with LF pattern - just the return line
    pattern = "    return True"
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None
    start, end = result
    assert normalize_line_endings(
        "\n".join(file_content.split("\n")[start : end + 1])
    ) == normalize_line_endings(pattern)

    # Test with CRLF pattern - just the pass line
    pattern = "    pass"
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None
    start, end = result
    assert normalize_line_endings(
        "\n".join(file_content.split("\n")[start : end + 1])
    ) == normalize_line_endings(pattern)


def test_fuzzy_locate_snippet_with_comments_and_blanks():
    """Test fuzzy_locate_snippet with comments and blank lines."""
    file_content = """
# This is a header comment

def process_data(data: list) -> list:
    # Internal processing
    result = []

    # Process each item
    for item in data:
        result.append(item * 2)

    return result  # Return processed data

# End of function
""".strip()

    # Test finding code with surrounding comments
    pattern = """
    # Process each item
    for item in data:
        result.append(item * 2)

    return result  # Return processed data
""".strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None

    # Test finding just the core code without comments
    pattern = """
    for item in data:
        result.append(item * 2)
""".strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None


def test_fuzzy_locate_snippet_with_unicode():
    """Test fuzzy_locate_snippet with Unicode characters."""
    file_content = '''
def calculate_π():
    """Calculate π using the Leibniz formula."""
    π = 0
    for i in range(1000):
        π += (-1)**i / (2*i + 1)
    return 4 * π

def print_greeting():
    print("Hello, 世界!")
    print("Здравствуй, мир!")
'''.strip()

    # Test finding function with Unicode in name and content
    pattern = '''
def calculate_π():
    """Calculate π using the Leibniz formula."""
    π = 0
'''.strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None

    # Test finding multilingual content
    pattern = """
    print("Hello, 世界!")
    print("Здравствуй, мир!")
""".strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None


def test_fuzzy_locate_snippet_with_partial_matches():
    """Test fuzzy_locate_snippet with partial and overlapping matches."""
    file_content = """
def process(x):
    if x > 0:
        return x + 1
    if x < 0:
        return x - 1
    return x

def process_list(items):
    if len(items) > 0:
        return [x + 1 for x in items]
    return items
""".strip()

    # Test finding similar but different code blocks
    pattern = """
    if x > 0:
        return x + 1
""".strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None

    # Test with pattern that has multiple potential matches
    pattern = """
    return x
""".strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None


def test_fuzzy_locate_snippet_with_long_content():
    """Test fuzzy_locate_snippet with very long content."""
    # Generate a long file content with repeated patterns
    repeated_block = '''
    def function_{i}(x: int) -> int:
        """Function number {i}."""
        result = x + {i}
        return result

'''.strip()

    file_content = "\n\n".join(repeated_block.format(i=i) for i in range(1000))

    # Test finding a specific function in the middle
    pattern = '''
    def function_500(x: int) -> int:
        """Function number 500."""
        result = x + 500
        return result
'''.strip()
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None

    # Test finding a pattern that appears multiple times but with unique context
    pattern = '''        """Function number 42."""
        result = x + 42'''
    result = fuzzy_locate_snippet(file_content, pattern)
    assert result is not None
