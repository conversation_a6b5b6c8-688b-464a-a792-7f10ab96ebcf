import re
from typing import List, <PERSON><PERSON>, Dict, Optional


def remove_markdown_metadata(text: str) -> str:
    """
    Remove codeblock metadata from the input text.

    Handles cases like:
    "```python path=a/b/c.py mode=EDIT\n" -> "```python"
    "````cpp path=x/y/z.cpp mode=EDIT\r\n" -> "````cpp"
    "``` path=a/b/c.txt mode=EDIT\n" -> "```"

    Ensures that:
    - Codeblocks can start with either 3 or 4 backticks
    - Neither path nor mode can contain \r or \n
    - The pattern can end with either \n or \r\n

    Args:
        text (str): The input text containing codeblocks with metadata.

    Returns:
        str: The text with codeblock metadata removed.
    """
    pattern = r"(```|````)(\w*) path=[^\r\n]+ mode=[^\r\n]+[\r]?\n"
    return re.sub(pattern, r"\1\2\n", text)


def remove_xml_metadata(text: str) -> str:
    """
    Remove XML-like metadata from the input text.

    Removes metadata in the format:
    <augment_code_snippet path="..." mode="...">
    ```language

    Also removes lines containing "</augment_code_snippet>".

    Args:
        text (str): The input text containing XML-like metadata.

    Returns:
        str: The text with XML-like metadata removed, leaving only the code block.
    """
    # Remove opening XML-like metadata
    pattern = r"<augment_code_snippet[^>]*>\s*```"
    text = re.sub(pattern, "```", text)

    # Remove lines containing "</augment_code_snippet>"
    pattern = r"^\s*</augment_code_snippet>\s*$"
    text = re.sub(pattern, "", text, flags=re.MULTILINE)

    return text


def strip_codeblock_metadata(text: str) -> str:
    return remove_markdown_metadata(remove_xml_metadata(text))
