load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "fuzzy_search",
    srcs = [
        "fuzzy_search.py",
    ],
    visibility = ["//visibility:public"],
)

pytest_test(
    name = "fuzzy_search_test",
    srcs = ["fuzzy_search_test.py"],
    deps = [
        ":fuzzy_search",
    ],
)

py_library(
    name = "utils",
    srcs = [
        "utils.py",
    ],
    visibility = ["//visibility:public"],
)

pytest_test(
    name = "utils_test",
    srcs = ["utils_test.py"],
    deps = [
        ":utils",
    ],
)

py_library(
    name = "forger_prompt_formatter",
    srcs = [
        "forger_prompt_formatter.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":fuzzy_search",
        ":utils",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:smart_paste_prompt_formatter",
    ],
)

pytest_test(
    name = "forger_prompt_formatter_test",
    timeout = "short",
    srcs = ["forger_prompt_formatter_test.py"],
    deps = [
        ":forger_prompt_formatter",
    ],
)
