"""Functions for fuzzy locating code snippets in text."""

import sys
from typing import TypedDict


class LCSEntry(TypedDict):
    """Type for entries in the LCS computation matrix."""

    lcs_length: int
    min_substring_length: int


def fast_hash(text: str, ignore_leading_whitespace: bool = False) -> int:
    """Compute a fast hash of the input string using the FNV-1a algorithm.

    This function implements a non-cryptographic hash function that is
    designed for speed while still providing a good distribution of hash
    values. It's particularly useful for hash table implementations and
    quick string comparisons.

    The FNV-1a algorithm is chosen for its simplicity and efficiency in
    software implementations, especially for shorter strings typically
    found in source code lines.

    Note: This function is not suitable for cryptographic purposes.

    Args:
        text: The input string to be hashed.
        ignore_leading_whitespace: If true, leading whitespace will be ignored when computing the hash.

    Returns:
        A 32-bit unsigned integer representing the hash of the input string.
    """
    hash_val = 0x811C9DC5  # FNV offset basis
    start_index = len(text) - len(text.lstrip()) if ignore_leading_whitespace else 0
    for i in range(start_index, len(text)):
        hash_val ^= ord(text[i])
        hash_val *= 0x01000193  # FNV prime
        hash_val &= 0xFFFFFFFF  # Keep 32 bits
    return hash_val


def compute_lcs(document: list[int], pattern: list[int]) -> tuple[int, int, int]:
    """Compute the Longest Common Subsequence (LCS) between two arrays of numbers.

    Args:
        document: An array of numbers representing the document content.
        pattern: An array of numbers representing the pattern to match.

    Returns:
        A tuple containing:
        - The length of the LCS
        - The minimum substring length
        - The end index of the match in the document content
    """
    n = len(document)
    m = len(pattern)

    # Initialize previous and current rows
    previous_row: list[LCSEntry] = [
        LCSEntry(lcs_length=0, min_substring_length=sys.maxsize) for _ in range(m + 1)
    ]
    current_row: list[LCSEntry] = [
        LCSEntry(lcs_length=0, min_substring_length=sys.maxsize) for _ in range(m + 1)
    ]
    previous_row[0]["min_substring_length"] = 0

    max_lcs_length = 0
    min_substring_length = sys.maxsize
    end_index = -1

    for i in range(1, n + 1):
        current_row[0] = LCSEntry(lcs_length=0, min_substring_length=0)

        for j in range(1, m + 1):
            if document[i - 1] == pattern[j - 1]:
                lcs_length = previous_row[j - 1]["lcs_length"] + 1
                min_sub_length = previous_row[j - 1]["min_substring_length"] + 1
                current_row[j] = LCSEntry(
                    lcs_length=lcs_length, min_substring_length=min_sub_length
                )
            else:
                from_top = previous_row[j]
                from_left = current_row[j - 1]

                if from_top["lcs_length"] > from_left["lcs_length"]:
                    lcs_length = from_top["lcs_length"]
                    min_sub_length = from_top["min_substring_length"] + 1
                    current_row[j] = LCSEntry(
                        lcs_length=lcs_length, min_substring_length=min_sub_length
                    )
                elif from_top["lcs_length"] < from_left["lcs_length"]:
                    lcs_length = from_left["lcs_length"]
                    min_sub_length = from_left["min_substring_length"]
                    current_row[j] = LCSEntry(
                        lcs_length=lcs_length, min_substring_length=min_sub_length
                    )
                else:
                    lcs_length = from_top["lcs_length"]
                    min_sub_length = int(
                        min(
                            from_top["min_substring_length"] + 1,
                            from_left["min_substring_length"],
                        )
                    )
                    current_row[j] = LCSEntry(
                        lcs_length=lcs_length, min_substring_length=min_sub_length
                    )

            if j == m:
                current = current_row[j]
                if current["lcs_length"] > max_lcs_length or (
                    current["lcs_length"] == max_lcs_length
                    and current["min_substring_length"] < min_substring_length
                ):
                    max_lcs_length = current["lcs_length"]
                    min_substring_length = current["min_substring_length"]
                    end_index = i

        previous_row, current_row = current_row, previous_row

    return max_lcs_length, min_substring_length, end_index


def fuzzy_locate_snippet(file_content: str, pattern: str) -> tuple[int, int] | None:
    """Locate the shortest range in the file content that contains the maximum
    longest common subsequence (LCS) with the provided pattern.

    This function treats the file content and pattern as sequences of lines.
    Each line is hashed using the fast_hash function, and these hashes are used
    for comparison instead of direct string matching. This approach can
    significantly improve performance for large files or patterns.

    The function handles cases where the snippet might have different indentation
    levels compared to the original file content. This is particularly useful
    when dealing with code snippets returned by chat or other AI systems, which
    may not preserve the original indentation. To account for this, the function
    performs two searches:
    1. Without ignoring indentation (preserving original whitespace)
    2. Ignoring leading whitespace (to match snippets with different indentation)

    The function prioritizes matches found without ignoring indentation, but
    falls back to the indentation-ignoring match if no exact match is found.
    This approach ensures the most accurate match while still being flexible
    enough to handle indentation differences.

    Args:
        file_content: The content of the file, represented as a string where each line is separated by '\n'.
        pattern: The pattern to match, represented as a string where each line is separated by '\n'.

    Returns:
        A tuple containing the start and end indices of the range in the file content,
        or None if no match is found. The indices are zero-based and inclusive.
    """
    file_lines = file_content.split("\n")
    pattern_lines = pattern.strip().split("\n")

    def compute_hashes_and_lcs(ignore_leading_whitespace: bool) -> tuple[int, int, int]:
        file_hashes = [
            fast_hash(line, ignore_leading_whitespace) for line in file_lines
        ]
        pattern_hashes = [
            fast_hash(line, ignore_leading_whitespace) for line in pattern_lines
        ]
        return compute_lcs(file_hashes, pattern_hashes)

    # Try both options: with and without ignoring indentation
    result_without_ignore_indentation = compute_hashes_and_lcs(False)
    result_with_ignore_indentation = compute_hashes_and_lcs(True)

    # Choose the result, giving priority to the one without ignoring indentation
    if result_without_ignore_indentation[0] >= result_with_ignore_indentation[0]:
        max_lcs_length, min_substring_length, end_index = (
            result_without_ignore_indentation
        )
    else:
        max_lcs_length, min_substring_length, end_index = result_with_ignore_indentation

    if max_lcs_length == 0:
        return None

    start_index = end_index - min_substring_length
    end_index_inclusive = end_index - 1

    return start_index, end_index_inclusive
