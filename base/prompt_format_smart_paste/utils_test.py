import pytest
from base.prompt_format_smart_paste.utils import (
    strip_codeblock_metadata,
    remove_xml_metadata,
    remove_markdown_metadata,
)


@pytest.mark.parametrize(
    "test_id, input_text, expected_output",
    [
        (
            "triple_backticks_python",
            "```python path=a/b/c.py mode=EDIT\ndef hello():\n    print('Hello')\n```",
            "```python\ndef hello():\n    print('Hello')\n```",
        ),
        (
            "quadruple_backticks_python",
            "````python path=a/b/c.py mode=EDIT\ndef hello():\n    print('Hello')\n````",
            "````python\ndef hello():\n    print('Hello')\n````",
        ),
        (
            "triple_backticks_no_lang",
            "``` path=a/b/c.txt mode=EDIT\nplain text\n```",
            "```\nplain text\n```",
        ),
        (
            "quadruple_backticks_no_lang",
            "```` path=a/b/c.txt mode=EDIT\nplain text\n````",
            "````\nplain text\n````",
        ),
        (
            "triple_backticks_crlf_ending",
            "```cpp path=x/y/z.cpp mode=EDIT\r\nint main() {}\r\n```",
            "```cpp\nint main() {}\r\n```",
        ),
        (
            "quadruple_backticks_crlf_ending",
            "````cpp path=x/y/z.cpp mode=EDIT\r\nint main() {}\r\n````",
            "````cpp\nint main() {}\r\n````",
        ),
        (
            "triple_backticks_preserve_internal_newlines",
            "```js path=script.js mode=EDIT\nfunction f() {\n  return 1;\n}\n```",
            "```js\nfunction f() {\n  return 1;\n}\n```",
        ),
        (
            "quadruple_backticks_preserve_internal_newlines",
            "````js path=script.js mode=EDIT\nfunction f() {\n  return 1;\n}\n````",
            "````js\nfunction f() {\n  return 1;\n}\n````",
        ),
        (
            "mixed_backticks_multiple_codeblocks",
            "```python path=a.py mode=EDIT\ndef a(): pass\n```\ntext\n````js path=b.js mode=EDIT\nfunction b() {}\n````",
            "```python\ndef a(): pass\n```\ntext\n````js\nfunction b() {}\n````",
        ),
        (
            "triple_backticks_empty_codeblock",
            "```python path=empty.py mode=EDIT\n```",
            "```python\n```",
        ),
        (
            "quadruple_backticks_empty_codeblock",
            "````python path=empty.py mode=EDIT\n````",
            "````python\n````",
        ),
        (
            "triple_backticks_no_metadata_preserved",
            "```python\ndef no_metadata(): pass\n```",
            "```python\ndef no_metadata(): pass\n```",
        ),
        (
            "quadruple_backticks_no_metadata_preserved",
            "````python\ndef no_metadata(): pass\n````",
            "````python\ndef no_metadata(): pass\n````",
        ),
        (
            "triple_backticks_complex_path",
            "```c path=/usr/local/include/complex/path.h mode=EDIT\n#include <stdio.h>\n```",
            "```c\n#include <stdio.h>\n```",
        ),
        (
            "quadruple_backticks_complex_path",
            "````c path=/usr/local/include/complex/path.h mode=EDIT\n#include <stdio.h>\n````",
            "````c\n#include <stdio.h>\n````",
        ),
        (
            "triple_backticks_special_chars_in_path",
            "```ruby path=!@#$%^&*()_+.rb mode=EDIT\nputs 'special'\n```",
            "```ruby\nputs 'special'\n```",
        ),
        (
            "quadruple_backticks_special_chars_in_path",
            "````ruby path=!@#$%^&*()_+.rb mode=EDIT\nputs 'special'\n````",
            "````ruby\nputs 'special'\n````",
        ),
    ],
)
def test_remove_markdown_metadata(test_id, input_text, expected_output):
    assert (
        remove_markdown_metadata(input_text) == expected_output
    ), f"Failed on test case: {test_id}"


def test_remove_markdown_metadata_no_change():
    text_without_metadata = "This is a text without any codeblock metadata."
    assert remove_markdown_metadata(text_without_metadata) == text_without_metadata


def test_remove_markdown_metadata_mixed_content():
    mixed_content = """
    Some text before
    ```python path=test.py mode=EDIT
    def hello():
        print("Hello, world!")
    ```
    Some text in between
    ````
    This block has no metadata
    ````
    Some text after
    """
    expected_output = """
    Some text before
    ```python
    def hello():
        print("Hello, world!")
    ```
    Some text in between
    ````
    This block has no metadata
    ````
    Some text after
    """
    assert remove_markdown_metadata(mixed_content) == expected_output


def test_strip_xml_metadata_no_change():
    text_without_metadata = "This is a text without any XML metadata."
    assert remove_xml_metadata(text_without_metadata) == text_without_metadata


def test_strip_codeblock_metadata_1():
    mixed_content = """
Certainly! I'll fix the typo "extenstions" to "extensions" in the selected part of the file. Here's the corrected version:

<augment_code_snippet path="Examples/Examples.playground/Pages/00-ToC.xcplaygroundpage/Contents.swift" mode="EDIT">
```swift
/*:
 ## SwifterSwift Examples

 SwifterSwift is a library of **over 500 properties and methods**, designed to extend Swift's functionality and productivity, staying faithful to the original API design guidelines.

 You can find examples of some extensions and try them out in this playground:

 * [SwiftStdlib extensions](01-SwiftStdlibExtensions)
 * [Foundation extensions](02-FoundationExtensions)
 * [UIKit extensions](03-UIKitExtensions)
 * [CoreGraphics extensions](04-CoreGraphicsExtensions)
 * [Try yourself](05-TryYourself)

 **Make sure you build the SwifterSwift project before trying to run the playgrounds.**

*/
```
</augment_code_snippet>

I've corrected the typo from "extenstions" to "extensions" in the selected part of the file. The rest of the content remains unchanged. This edit should fix the spelling error in the file `Examples/Examples.playground/Pages/00-ToC.xcplaygroundpage/Contents.swift`.
    """
    expected_output = """
Certainly! I'll fix the typo "extenstions" to "extensions" in the selected part of the file. Here's the corrected version:

```swift
/*:
 ## SwifterSwift Examples

 SwifterSwift is a library of **over 500 properties and methods**, designed to extend Swift's functionality and productivity, staying faithful to the original API design guidelines.

 You can find examples of some extensions and try them out in this playground:

 * [SwiftStdlib extensions](01-SwiftStdlibExtensions)
 * [Foundation extensions](02-FoundationExtensions)
 * [UIKit extensions](03-UIKitExtensions)
 * [CoreGraphics extensions](04-CoreGraphicsExtensions)
 * [Try yourself](05-TryYourself)

 **Make sure you build the SwifterSwift project before trying to run the playgrounds.**

*/
```

I've corrected the typo from "extenstions" to "extensions" in the selected part of the file. The rest of the content remains unchanged. This edit should fix the spelling error in the file `Examples/Examples.playground/Pages/00-ToC.xcplaygroundpage/Contents.swift`.
    """
    assert strip_codeblock_metadata(mixed_content) == expected_output


def test_strip_codeblock_metadata_2():
    mixed_content = """
Certainly! I'll make the requested changes to replace the command in the relevant sections. Here's the updated version of the file with the changes:

<augment_code_snippet path="docs/services/blobdb/migrate-fs-to-s3.md" mode="EDIT">
```markdown
# Migrate from File System backend to an S3 compatible backend

This can be to Riak CS, Minio, S3 or any S3 compatible service.

## Ensure that the S3 endpoint is up and accessible
If you are running the service locally make sure it is fully setup. If you are using S3 ensure that
you have configured the correct access to allow connections from the IPs where CommCare is running.

## Send new writes to the S3 endpoint

1. Update settings in `environments/<env>/public.yml`:
  - `localsettings.BLOB_DB_MIGRATING_FROM_FS_TO_S3: True`
  - `s3_blob_db_enabled: yes`
  - `s3_blob_db_url: "<Endpoint URL e.g. https://s3.amazonaws.com>"`
  - `s3_blob_db_s3_bucket: '<bucket name>'`
2. Add/update settings in `environments/<env>/vault.yml`
   - `secrets.S3_ACCESS_KEY`
   - `secrets.S3_SECRET_KEY`
3. Deploy localsettings
    ```bash
    commcare-cloud <env> update-config
    ```
4. Restart CommCare services
   ```bash
   commcare-cloud <env> fab restart_services
   ```

{% include_relative _blobdb_backfill.md %}

## Flip to just the new backend
1. Make sure you've run the steps above to move all data to the new backend.
2. Update `environments/<env>/public.yml`
   - Remove `localsettings.BLOB_DB_MIGRATING_FROM_FS_TO_S3`
3. Deploy localsettings with
   ```
   cchq <env> update-config
   ```
4. Restart CommCare services
   ```bash
   commcare-cloud <env> fab restart_services
   ```
```
</augment_code_snippet>

I've made the following changes:

1. In the "Send new writes to the S3 endpoint" section, step 4:
   Changed `commcare-cloud <env> service commcare resetart` to `commcare-cloud <env> fab restart_services`

2. In the "Flip to just the new backend" section, step 4:
   Changed `commcare-cloud <env> service commcare resetart` to `commcare-cloud <env> fab restart_services`

These changes replace the restart command with the new one you specified in both relevant sections of the document. The rest of the content remains unchanged.
    """
    expected_output = """
Certainly! I'll make the requested changes to replace the command in the relevant sections. Here's the updated version of the file with the changes:

```markdown
# Migrate from File System backend to an S3 compatible backend

This can be to Riak CS, Minio, S3 or any S3 compatible service.

## Ensure that the S3 endpoint is up and accessible
If you are running the service locally make sure it is fully setup. If you are using S3 ensure that
you have configured the correct access to allow connections from the IPs where CommCare is running.

## Send new writes to the S3 endpoint

1. Update settings in `environments/<env>/public.yml`:
  - `localsettings.BLOB_DB_MIGRATING_FROM_FS_TO_S3: True`
  - `s3_blob_db_enabled: yes`
  - `s3_blob_db_url: "<Endpoint URL e.g. https://s3.amazonaws.com>"`
  - `s3_blob_db_s3_bucket: '<bucket name>'`
2. Add/update settings in `environments/<env>/vault.yml`
   - `secrets.S3_ACCESS_KEY`
   - `secrets.S3_SECRET_KEY`
3. Deploy localsettings
    ```bash
    commcare-cloud <env> update-config
    ```
4. Restart CommCare services
   ```bash
   commcare-cloud <env> fab restart_services
   ```

{% include_relative _blobdb_backfill.md %}

## Flip to just the new backend
1. Make sure you've run the steps above to move all data to the new backend.
2. Update `environments/<env>/public.yml`
   - Remove `localsettings.BLOB_DB_MIGRATING_FROM_FS_TO_S3`
3. Deploy localsettings with
   ```
   cchq <env> update-config
   ```
4. Restart CommCare services
   ```bash
   commcare-cloud <env> fab restart_services
   ```
```

I've made the following changes:

1. In the "Send new writes to the S3 endpoint" section, step 4:
   Changed `commcare-cloud <env> service commcare resetart` to `commcare-cloud <env> fab restart_services`

2. In the "Flip to just the new backend" section, step 4:
   Changed `commcare-cloud <env> service commcare resetart` to `commcare-cloud <env> fab restart_services`

These changes replace the restart command with the new one you specified in both relevant sections of the document. The rest of the content remains unchanged.
    """
    assert strip_codeblock_metadata(mixed_content) == expected_output


def test_strip_codeblock_metadata_3():
    mixed_content = """
Certainly! I'll update the `get_bias_value` function to use `onnx_graph.has_tensor` and `onnx_graph.get_tensor_value` instead of initializers. Here's the updated version:

<augment_code_snippet path="nncf/onnx/graph/node_utils.py" mode="EDIT">
```python
def get_bias_value(node_with_bias: NNCFNode, model: onnx.ModelProto) -> np.ndarray:
    \"\"\"
    Returns the bias tensor for the biased node.

    :param node_with_bias: The node that corresponds to the operation with bias.
    :param model: The model that contains this operation.
    :return: The bias value that is applied to the output tensor of the node's operation.
    \"\"\"
    onnx_graph = ONNXGraph(model)
    onnx_node = onnx_graph.get_node_by_name(node_with_bias.node_name)
    bias_port_id = onnx_graph.get_bias_tensor_port_id(onnx_node)
    bias_input_name = onnx_node.input[bias_port_id]
    if onnx_graph.has_tensor(bias_input_name):
        return onnx_graph.get_tensor_value(bias_input_name)
    node = onnx_graph.get_nodes_by_output(bias_input_name)[0]
    metatype = ONNX_OPERATION_METATYPES.get_operator_metatype_by_op_name(node.op_type)
    if metatype == ONNXIdentityMetatype:
        if onnx_graph.has_tensor(node.input[0]):
            return onnx_graph.get_tensor_value(node.input[0])
    raise RuntimeError("Could not find the bias value of the node")
```
</augment_code_snippet>

In this updated version, I've made the following changes:

1. Replaced `onnx_graph.has_initializer(bias_input_name)` with `onnx_graph.has_tensor(bias_input_name)`.
2. Replaced `onnx_graph.get_initializers_value(bias_input_name)` with `onnx_graph.get_tensor_value(bias_input_name)`.
3. In the `ONNXIdentityMetatype` case, I've added a check using `onnx_graph.has_tensor(node.input[0])` before calling `onnx_graph.get_tensor_value(node.input[0])`.

These changes should make the function use the more general `has_tensor` and `get_tensor_value` methods instead of the initializer-specific methods. This approach should work for both initializers and other tensor types in the ONNX graph.
"""
    expected_output = """
Certainly! I'll update the `get_bias_value` function to use `onnx_graph.has_tensor` and `onnx_graph.get_tensor_value` instead of initializers. Here's the updated version:

```python
def get_bias_value(node_with_bias: NNCFNode, model: onnx.ModelProto) -> np.ndarray:
    \"\"\"
    Returns the bias tensor for the biased node.

    :param node_with_bias: The node that corresponds to the operation with bias.
    :param model: The model that contains this operation.
    :return: The bias value that is applied to the output tensor of the node's operation.
    \"\"\"
    onnx_graph = ONNXGraph(model)
    onnx_node = onnx_graph.get_node_by_name(node_with_bias.node_name)
    bias_port_id = onnx_graph.get_bias_tensor_port_id(onnx_node)
    bias_input_name = onnx_node.input[bias_port_id]
    if onnx_graph.has_tensor(bias_input_name):
        return onnx_graph.get_tensor_value(bias_input_name)
    node = onnx_graph.get_nodes_by_output(bias_input_name)[0]
    metatype = ONNX_OPERATION_METATYPES.get_operator_metatype_by_op_name(node.op_type)
    if metatype == ONNXIdentityMetatype:
        if onnx_graph.has_tensor(node.input[0]):
            return onnx_graph.get_tensor_value(node.input[0])
    raise RuntimeError("Could not find the bias value of the node")
```

In this updated version, I've made the following changes:

1. Replaced `onnx_graph.has_initializer(bias_input_name)` with `onnx_graph.has_tensor(bias_input_name)`.
2. Replaced `onnx_graph.get_initializers_value(bias_input_name)` with `onnx_graph.get_tensor_value(bias_input_name)`.
3. In the `ONNXIdentityMetatype` case, I've added a check using `onnx_graph.has_tensor(node.input[0])` before calling `onnx_graph.get_tensor_value(node.input[0])`.

These changes should make the function use the more general `has_tensor` and `get_tensor_value` methods instead of the initializer-specific methods. This approach should work for both initializers and other tensor types in the ONNX graph.
"""
    assert strip_codeblock_metadata(mixed_content) == expected_output


def test_strip_codeblock_metadata_4():
    mixed_content = """
Certainly! I'll make the changes you requested to the `src/components/BaseLineChart.vue` file. I'll change the `id` prop to required and refactor `chartOptions` to a computed property. Here's the modified code:

```vue path=src/components/BaseLineChart.vue mode=EDIT
<script>
import { Line } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, PointElement, LineElement, CategoryScale, LinearScale } from 'chart.js';

ChartJS.register(Title, Tooltip, Legend, LineElement, PointElement, CategoryScale, LinearScale);

export default {
  name: 'BarChart',
  components: { Line },
  props: {
    id: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    // Array of string labels for the X axis
    labels: {
      type: Array,
      required: true,
    },
    // Array of dataset objects for chartjs
    // https://www.chartjs.org/docs/latest/general/data-structures.html#primitive
    dataSets: {
      type: Array,
      required: true,
    },
  },
  computed: {
    chartData() {
      return {
        labels: this.labels,
        datasets: this.dataSets,
      };
    },
    chartOptions() {
      return {
        responsive: true,
        plugins: {
          title: {
            text: this.title,
            display: !!this.title,
          },
        },
      };
    },
  },
};
</script>
```

Here are the changes made:

1. Changed the `id` prop to be required:
   ```javascript
   id: {
     type: String,
     required: true,
   },
   ```

2. Removed the `data()` function and moved `chartOptions` to a computed property:
   ```javascript
   computed: {
     chartData() {
       // ... (unchanged)
     },
     chartOptions() {
       return {
         responsive: true,
         plugins: {
           title: {
             text: this.title,
             display: !!this.title,
           },
         },
       };
     },
   },
   ```

These changes make the `id` prop required and move the `chartOptions` to a computed property, which can be more reactive to changes in the component's data.
"""
    expected_output = """
Certainly! I'll make the changes you requested to the `src/components/BaseLineChart.vue` file. I'll change the `id` prop to required and refactor `chartOptions` to a computed property. Here's the modified code:

```vue
<script>
import { Line } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, PointElement, LineElement, CategoryScale, LinearScale } from 'chart.js';

ChartJS.register(Title, Tooltip, Legend, LineElement, PointElement, CategoryScale, LinearScale);

export default {
  name: 'BarChart',
  components: { Line },
  props: {
    id: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    // Array of string labels for the X axis
    labels: {
      type: Array,
      required: true,
    },
    // Array of dataset objects for chartjs
    // https://www.chartjs.org/docs/latest/general/data-structures.html#primitive
    dataSets: {
      type: Array,
      required: true,
    },
  },
  computed: {
    chartData() {
      return {
        labels: this.labels,
        datasets: this.dataSets,
      };
    },
    chartOptions() {
      return {
        responsive: true,
        plugins: {
          title: {
            text: this.title,
            display: !!this.title,
          },
        },
      };
    },
  },
};
</script>
```

Here are the changes made:

1. Changed the `id` prop to be required:
   ```javascript
   id: {
     type: String,
     required: true,
   },
   ```

2. Removed the `data()` function and moved `chartOptions` to a computed property:
   ```javascript
   computed: {
     chartData() {
       // ... (unchanged)
     },
     chartOptions() {
       return {
         responsive: true,
         plugins: {
           title: {
             text: this.title,
             display: !!this.title,
           },
         },
       };
     },
   },
   ```

These changes make the `id` prop required and move the `chartOptions` to a computed property, which can be more reactive to changes in the component's data.
"""
    assert strip_codeblock_metadata(mixed_content) == expected_output
