"""Classes that are used to build the prompts for the smart paste model."""

from dataclasses import dataclass
import dataclasses
from enum import Enum

from dataclasses_json import dataclass_json

from base.prompt_format.common import (
    LineEnding,
    detect_line_ending,
    get_request_message_as_text,
    get_response_message_as_text,
    normalize_line_endings,
)
from base.tokenizers.tokenizer import NextEditGenSpecialTokens, Tokenizer
from base.prompt_format_chat.prompt_formatter import (
    ExceedContextLength,
)
from base.prompt_format_chat.smart_paste_prompt_formatter import (
    SmartPastePromptInput,
)
from base.prompt_format_smart_paste.fuzzy_search import fuzzy_locate_snippet
from base.prompt_format_smart_paste.utils import strip_codeblock_metadata


@dataclass_json
@dataclass(frozen=True)
class ForgerSmartPasteTokenApportionment:
    """Stores the apportionment of the tokens for the code chat model."""

    path_len: int
    """The number of tokens of the path to include."""

    message_len: int
    """The number of tokens of the message to include."""

    prefix_len: int
    """The number of tokens of the prefix to include."""

    suffix_len: int
    """The number of tokens of the suffix to include."""

    max_prompt_len: int
    """The number of tokens of the total prompt length, which will be used to compute the budget for retrieved chunks."""

    adjust_newline_from_suffix_to_selected_code: bool = True
    """Whether to adjust the newline from suffix to selected code."""

    enable_fuzzy_search: bool = False
    """Whether to use fuzzy search to locate code block in the target file."""

    drop_user_instruction: bool = False
    """Whether to drop the user instruction from the chat history."""

    suggested_edit_type: str = "codeblock"
    """Source of the instruction: "codeblock" or "response"."""


@dataclass(frozen=True)
class ForgerSmartPastePromptOutput:
    """The set of outputs used for constructing smart paste prompts."""

    tokens: list[int]
    """The tokenized prompt."""

    unused_prefix: str
    """The unused prefix."""

    unused_suffix: str
    """The unused suffix."""

    used_lines: list[str]
    """The used lines."""

    start_line_number: int
    """The start line number of the selected code."""

    end_line_number: int
    """The end line number of the selected code (not inclusive.)"""

    original_line_ending: LineEnding
    """The original line ending type of the target file content."""


class ForgerPromptFormatter:
    """The Forget smart paste prompt formatter."""

    def __init__(
        self,
        tokenizer: Tokenizer,
        token_apportionment: ForgerSmartPasteTokenApportionment,
    ):
        self.tokenizer = tokenizer
        self.token_apportionment = token_apportionment
        special_tokens = self.tokenizer.special_tokens
        if not isinstance(special_tokens, NextEditGenSpecialTokens):
            raise ValueError(
                f"Expected NextEditGenSpecialTokens, but got {type(special_tokens)}. "
                f"The provided tokenizer type is {type(self.tokenizer)}"
            )
        self.special_tokens = special_tokens

    def format_prompt(
        self, prompt_input: SmartPastePromptInput, force_fuzzy_search: bool = False
    ) -> ForgerSmartPastePromptOutput:
        """Build a tokenized prompt from the given input.

        Args:
            prompt_input: SmartPastePromptInput object describing the input (prefix, selected_code, suffix, path)

        Returns:
            SmartPastePromptOutput object containing the tokenized prompt
        """
        enable_fuzzy_search = (
            self.token_apportionment.enable_fuzzy_search or force_fuzzy_search
        )

        original_line_ending = detect_line_ending(prompt_input.target_file_content)
        normalized_target_content = normalize_line_endings(
            prompt_input.target_file_content
        )

        # Create a new prompt_input with normalized content
        prompt_input = dataclasses.replace(
            prompt_input,
            target_file_content=normalized_target_content,
            prefix=normalize_line_endings(prompt_input.prefix),
            selected_code=normalize_line_endings(prompt_input.selected_code),
            suffix=normalize_line_endings(prompt_input.suffix),
        )

        # The originally selected code provides valuable context, so we try to preserve it.
        # We determine whether to use the provided path, prefix, selected code, and suffix by checking:
        # 1. If the target file has changed.
        # 2. If the code_block is being applied to a different file.
        # If neither case is true, we use the original information when formatting the prompt.

        current_file_content = (
            prompt_input.prefix + prompt_input.selected_code + prompt_input.suffix
        )

        if (
            prompt_input.target_path == prompt_input.path
            and prompt_input.target_file_content == current_file_content
        ):
            path = prompt_input.path
            prefix = prompt_input.prefix
            selected_code = prompt_input.selected_code
            suffix = prompt_input.suffix

            # There is a mismatch between production and research in
            # whether or not selected code ends with a new line.
            # The model is trained on this newline being a part of
            # the selected code. Therefore, we need to add it if it's missing.
            if (
                self.token_apportionment.adjust_newline_from_suffix_to_selected_code
                and len(suffix) > 0
                and suffix[0] == "\n"
            ):
                if len(selected_code) == 0:
                    prefix += "\n"
                    suffix = suffix[1:]
                elif selected_code[-1] != "\n":
                    selected_code += "\n"
                    suffix = suffix[1:]
        else:
            path = prompt_input.target_path

            # Try to locate the code block in the target file using fuzzy search
            if (
                enable_fuzzy_search
                and prompt_input.code_block
                and prompt_input.target_file_content
            ):
                result = fuzzy_locate_snippet(
                    prompt_input.target_file_content, prompt_input.code_block
                )
                if result is not None:
                    start_idx, _ = result
                    lines = prompt_input.target_file_content.splitlines(keepends=True)
                    prefix = "".join(lines[:start_idx])
                    selected_code = ""
                    suffix = "".join(lines[start_idx:])
                else:
                    prefix = ""
                    selected_code = ""
                    suffix = prompt_input.target_file_content
            else:
                prefix = ""
                selected_code = ""
                suffix = prompt_input.target_file_content

        newline_id = self.special_tokens.newline
        tokenized_path = self.tokenizer.tokenize_safe(path)
        tokenized_path = tokenized_path[: self.token_apportionment.path_len]

        chat_history = list(prompt_input.chat_history)

        if self.token_apportionment.suggested_edit_type == "response":
            assert len(chat_history) > 0
            suggested_edit = get_response_message_as_text(
                chat_history[-1].response_text
            )
        elif (
            self.token_apportionment.suggested_edit_type
            == "response_with_stripped_metadata"
        ):
            assert len(chat_history) > 0
            suggested_edit = get_response_message_as_text(
                chat_history[-1].response_text
            )
            suggested_edit = strip_codeblock_metadata(suggested_edit)
        elif self.token_apportionment.suggested_edit_type == "codeblock":
            suggested_edit = prompt_input.code_block
        else:
            raise ValueError(
                f"Invalid suggested_edit_type: {self.token_apportionment.suggested_edit_type}"
            )

        tokenized_suggested_edit = self.tokenizer.tokenize_safe(suggested_edit)

        if len(chat_history) > 0 and not self.token_apportionment.drop_user_instruction:
            # We assume the last message in the chat history is the original user instruction.
            instruction = get_request_message_as_text(chat_history[-1].request_message)
            tokenized_instruction = self.tokenizer.tokenize_safe(instruction)
            tokenized_instruction = tokenized_instruction[
                : self.token_apportionment.message_len
            ]
            tokenized_instruction = [
                newline_id,
                self.special_tokens.instruction,
            ] + tokenized_instruction
        else:
            tokenized_instruction = []

        remaining_budget = (
            self.token_apportionment.max_prompt_len
            - len(tokenized_suggested_edit)
            - len(tokenized_path)
            - len(tokenized_instruction)
            - 13  # the number of special tokens
        )

        prefix_lines = prefix.splitlines(keepends=True)
        suffix_lines = suffix.splitlines(keepends=True)
        selected_code_lines = selected_code.splitlines(keepends=True)
        lines = prefix_lines + selected_code_lines + suffix_lines
        line_token_counts = [len(self.tokenizer.tokenize_safe(line)) for line in lines]

        used_lines = set()

        prefix_line_ids = list(reversed(range(len(prefix_lines))))
        selected_code_line_ids = list(
            range(len(prefix_lines), len(prefix_lines) + len(selected_code_lines))
        )
        suffix_line_ids = list(
            range(
                len(prefix_lines) + len(selected_code_lines),
                len(prefix_lines) + len(selected_code_lines) + len(suffix_lines),
            )
        )

        n_selected_code_tokens = 0
        # Include all selected code lines
        for index in selected_code_line_ids:
            remaining_budget -= line_token_counts[index]
            n_selected_code_tokens += line_token_counts[index]
            used_lines.add(index)

        if remaining_budget < 0:
            raise ExceedContextLength(
                f"Prompt length even with empty prefix and suffix "
                "exceeds maximum prompt length "
                f"{self.token_apportionment.max_prompt_len}. "
                f"Selected code length: {n_selected_code_tokens}, "
                f"suggested edit length: {len(tokenized_suggested_edit)}, "
                f"filename length: {len(tokenized_path)}, "
                f"instruction length: {len(tokenized_instruction)}"
            )

        # Include prefix tokens -- the first attempt
        prefix_tokens = 0
        prefix_ratio = self.token_apportionment.prefix_len / (
            self.token_apportionment.prefix_len + self.token_apportionment.suffix_len
        )
        prefix_budget = min(
            self.token_apportionment.prefix_len, round(remaining_budget * prefix_ratio)
        )
        for index in prefix_line_ids:
            if (
                prefix_tokens + line_token_counts[index] > prefix_budget
                or line_token_counts[index] > remaining_budget
            ):
                break
            remaining_budget -= line_token_counts[index]
            prefix_tokens += line_token_counts[index]
            used_lines.add(index)

        # Include suffix tokens
        suffix_tokens = 0
        for index in suffix_line_ids:
            if (
                suffix_tokens + line_token_counts[index]
                > self.token_apportionment.suffix_len
                or line_token_counts[index] > remaining_budget
            ):
                break
            remaining_budget -= line_token_counts[index]
            suffix_tokens += line_token_counts[index]
            used_lines.add(index)

        # Use the remaining budget on prefix tokens
        for index in prefix_line_ids:
            if index in used_lines:
                continue
            if (
                prefix_tokens + line_token_counts[index]
                > self.token_apportionment.prefix_len
                or line_token_counts[index] > remaining_budget
            ):
                break
            remaining_budget -= line_token_counts[index]
            prefix_tokens += line_token_counts[index]
            used_lines.add(index)

        if len(used_lines) == 0:
            start_index = 0
            end_index = 0
        else:
            start_index = min(used_lines)
            end_index = max(used_lines) + 1

        unused_prefix = "".join(lines[:start_index])
        unused_suffix = "".join(lines[end_index:])

        prefix_line_ids_set = set(prefix_line_ids)
        suffix_line_ids_set = set(suffix_line_ids)
        selected_code_line_ids_set = set(selected_code_line_ids)
        tokens, tokens_without_selected_code_highlight = [], []

        actual_prefix, actual_suffix, actual_selected_code = "", "", ""

        for index in range(start_index, end_index):
            tokens_without_selected_code_highlight.extend(
                self.tokenizer.tokenize_safe(lines[index])
            )
            if index in prefix_line_ids_set:
                actual_prefix += lines[index]
            elif index in suffix_line_ids_set:
                actual_suffix += lines[index]
            elif index in selected_code_line_ids_set:
                assert len(prompt_input.selected_code) > 0
                actual_selected_code += lines[index]
            else:
                raise ValueError(f"Unknown line index {index}")

        tokens = self.tokenizer.tokenize_safe(actual_prefix)
        if len(actual_selected_code) > 0:
            tokens += [self.special_tokens.nearby_prefix]
            tokens += self.tokenizer.tokenize_safe(actual_selected_code)
            tokens += [self.special_tokens.nearby_suffix]
        tokens += self.tokenizer.tokenize_safe(actual_suffix)

        prompt_tokens = (
            [self.special_tokens.filename]
            + tokenized_path
            + tokenized_instruction
            + [newline_id, self.special_tokens.fim_prefix, newline_id]
            + tokens
            + [newline_id, self.special_tokens.fim_suffix, newline_id]
            + tokenized_suggested_edit
            + [newline_id, self.special_tokens.fim_middle, newline_id]
        )

        if len(prompt_tokens) > self.token_apportionment.max_prompt_len:
            raise ExceedContextLength(
                f"The prompt contains {len(prompt_tokens)} tokens, which "
                f"exceed the max limit of "
                f"{self.token_apportionment.max_prompt_len} tokens."
            )

        return ForgerSmartPastePromptOutput(
            tokens=prompt_tokens,
            unused_prefix=unused_prefix,
            unused_suffix=unused_suffix,
            used_lines=lines[start_index:end_index],
            start_line_number=start_index,
            end_line_number=end_index,
            original_line_ending=original_line_ending,
        )
