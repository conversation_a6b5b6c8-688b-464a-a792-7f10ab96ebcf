from base.next_edit_filter.rule_based_filters import is_deleting_imports


def test_is_deleting_imports_python():
    before_text = """import os
from pathlib import Path
import sys

def main():
    print('hello')
"""

    after_text = """from pathlib import Path
import sys

def main():
    print('hello')
"""

    assert is_deleting_imports("test.py", before_text, after_text) is True


def test_is_deleting_imports_no_deletion():
    before_text = """import os
from pathlib import Path

def main():
    print('hello')
"""

    after_text = """import os
from pathlib import Path

def main():
    print('goodbye')
"""

    assert is_deleting_imports("test.py", before_text, after_text) is False


def test_is_deleting_imports_unknown_language():
    before_text = """import something
other content
"""

    after_text = "other content"

    assert is_deleting_imports("test.unknown", before_text, after_text) is False


def test_is_deleting_imports_typescript():
    before_text = """import { foo } from './foo';
import { bar } from './bar';
const x = foo() + bar();
"""

    after_text = """import { foo } from './foo';
const x = foo();
"""

    assert is_deleting_imports("test.ts", before_text, after_text) is True


def test_is_deleting_imports_partial_line():
    before_text = """import os, sys, pathlib
x = 1
"""

    after_text = """import os, pathlib
x = 1
"""

    # Should return True since `sys` is deleted
    assert is_deleting_imports("test.py", before_text, after_text) is True


def test_is_deleting_imports_modification():
    before_text = """import package1
x = 1
"""

    after_text = """import package2
x = 1
"""

    # Modification should not be considered as deletion
    assert is_deleting_imports("test.py", before_text, after_text) is False
