"""Tests for extract_next_edit_filter_features.

pytest base/next_edit_filter/extract_next_edit_filter_features_test.py
"""

import pytest

from base.next_edit_filter.extract_next_edit_filter_features import (
    _token_splitlines,
    FeatureExtractorV1,
)
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
import numpy as np

TOKENIZER = StarCoder2Tokenizer()


# fmt: off
# pylint: disable=line-too-long
@pytest.mark.parametrize(
    "token_ids, expected_plus_lines, expected_minus_lines",
    [
        (   # <|has_change|>12+def _get_stream_processor() -> ClaudeStreamProcessorV2:
            # +    return ClaudeStreamProcessorV2()
            # +
            # +
            # 19+
            # 20-
            # +def process_stream(processor: ClaudeStreamProcessorV2, texts: List[str]) -> List[StreamProcessorOutput]:
            # 24-
            # +    responses = create_responses(texts)
            # 28-
            # +def assert_stream_output(processor: ClaudeStreamProcessorV2, input_text: str, expected_output: List[StreamProcessorOutput]):
            # 37-
            # 38-
            # 39-
            # 40-
            # 41-
            # <|endoftext|>
            [49174, 54, 55, 48, 610, 634, 390, 100, 2255, 100, 10093, 365, 984, 23979, 814, 1880, 6872, 91, 55, 63, 353, 494, 283, 461, 23979, 814, 1880, 6872, 91, 55, 365, 353, 494, 353, 494, 222, 54, 62, 48, 222, 55, 53, 50, 353, 494, 610, 2183, 100, 2255, 45, 10093, 63, 23979, 814, 1880, 6872, 91, 55, 49, 31807, 63, 1701, 96, 484, 1156, 984, 1701, 96, 1880, 6872, 2319, 2805, 222, 55, 57, 50, 353, 494, 283, 14829, 299, 1506, 100, 18248, 45, 21438, 46, 222, 55, 61, 50, 353, 494, 610, 1217, 100, 2255, 100, 2051, 45, 10093, 63, 23979, 814, 1880, 6872, 91, 55, 49, 1533, 100, 897, 63, 615, 49, 2816, 100, 2051, 63, 1701, 96, 1880, 6872, 2319, 10879, 222, 56, 60, 50, 222, 56, 61, 50, 222, 56, 62, 50, 222, 57, 53, 50, 222, 57, 54, 50, 222, 0],
            8,
            8,
        )
    ],
)
# pylint: enable=line-too-long
# fmt: on
def test_token_splitlines(
    token_ids: list[int], expected_plus_lines: int, expected_minus_lines: int
):
    log_probs = [0] * len(token_ids)
    text, offset = TOKENIZER.detokenize_with_offsets(token_ids)
    plus_lines, minus_lines = _token_splitlines(log_probs, token_ids, text, offset)
    assert len(plus_lines) == expected_plus_lines
    assert len(minus_lines) == expected_minus_lines


def test_extract_next_edit_filter_features_v1():
    # This example is identical to the first example in the test_token_splitlines test.
    token_ids =[49174, 54, 55, 48, 610, 634, 390, 100, 2255, 100, 10093, 365, 984, 23979, 814, 1880, 6872, 91, 55, 63, 353, 494, 283, 461, 23979, 814, 1880, 6872, 91, 55, 365, 353, 494, 353, 494, 222, 54, 62, 48, 222, 55, 53, 50, 353, 494, 610, 2183, 100, 2255, 45, 10093, 63, 23979, 814, 1880, 6872, 91, 55, 49, 31807, 63, 1701, 96, 484, 1156, 984, 1701, 96, 1880, 6872, 2319, 2805, 222, 55, 57, 50, 353, 494, 283, 14829, 299, 1506, 100, 18248, 45, 21438, 46, 222, 55, 61, 50, 353, 494, 610, 1217, 100, 2255, 100, 2051, 45, 10093, 63, 23979, 814, 1880, 6872, 91, 55, 49, 1533, 100, 897, 63, 615, 49, 2816, 100, 2051, 63, 1701, 96, 1880, 6872, 2319, 10879, 222, 56, 60, 50, 222, 56, 61, 50, 222, 56, 62, 50, 222, 57, 53, 50, 222, 57, 54, 50, 222, 0]
    log_probs = [0] * len(token_ids)
    log_probs[0] = np.log(0.9)
    log_probs[1] = np.log(0.1)


    feature_extractor = FeatureExtractorV1(tokenizer=TOKENIZER)
    features = feature_extractor.extract_features(
        log_probs=log_probs,
        token_ids=token_ids,
        prompt_token_ids=[],
    )
    assert features["has_change_prob"] == pytest.approx(0.9, 1e-6)
    assert features["plus_mean"] == pytest.approx(0.979099, 1e-6)
    assert features["minus_mean"] == pytest.approx(1.0, 1e-6)
    assert features["num_insert_lines"] == 8
    assert features["num_delete_lines"] == 8
