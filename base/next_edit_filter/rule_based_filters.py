from pathlib import Path
from typing import Sequence

from base.caching.lru_cache import lru_cache
from base.diff_utils import changes, str_diff
from base.diff_utils.diff_utils import File
from base.diff_utils.str_diff import (
    DeletedSpan,
    DiffSpan,
    NoopSpan,
    precise_char_diff,
)
from base.languages.language_guesser import guess_language
from base.ranges.line_map import LineMap
from base.ranges.range_types import CharRange
from base.static_analysis.import_finder import find_all_import_lines


def is_deleting_imports(path: Path | str, before_text: str, after_text: str) -> bool:
    """Check if the change is deleting any imports."""
    if before_text == after_text:
        return False
    lang = guess_language(path)
    if lang is None:
        # unknown language, we don't know how to parse imports
        return False
    before_import_lines = set(find_all_import_lines(before_text, lang))
    lmap = _cached_line_map(before_text)
    diff = precise_char_diff(before_text, after_text)
    deleted_lines = set[int]()
    for span, crange in zip(diff.spans, diff.span_ranges_in_before):
        if not isinstance(span, DeletedSpan):
            continue
        if not span.deleted.strip():
            # ignore whitespace-only changes
            continue
        start_line = lmap.get_line_number(crange.start)
        stop_line = lmap.get_line_number(crange.stop)
        deleted_lines.update(range(start_line, stop_line + 1))
    return bool(deleted_lines.intersection(before_import_lines))


def is_exact_undo_recent_changes(
    recent_changes: Sequence[changes.Changed[File]],
    selection_range: CharRange,
    existing_code: str,
    suggested_code: str,
    current_path: str,
) -> bool:
    """Checks if the suggested change exactly undoes any recent change.

    Args:
        recent_changes: the recent file changes to check against.
        selection_range: the selection range in the current file.
        existing_code: the existing code in selection range.
        suggested_code: the suggested code to replace the existing code.
        current_path: the file path of the selected file.
    """
    # First collect all file modifications for the current file.
    # We can safely ignore added files since they cannot be undone and have
    # no effect on the range mapping logic.
    same_file_changes = [
        _cached_precise_line_diff(change.before.contents, change.after.contents)
        for change in recent_changes
        if isinstance(change, changes.Modified)
        and change.get_later().path == current_path
    ]
    if not same_file_changes:
        return False
    # Then collect all diff spans that touch the selection range.
    spans_to_check = list[tuple[DiffSpan, CharRange]]()
    for i, diff in enumerate(same_file_changes):
        for span, after_range in zip(diff.spans, diff.span_ranges_in_after):
            if isinstance(span, NoopSpan):
                continue
            # Iteratively map the span's after range into the current file.
            # Note that this mapping may get less precise if a change's range gets
            # overwritten by a later change.
            for next_diff in same_file_changes[i + 1 :]:
                after_range = next_diff.before_range_to_after(after_range)
            if after_range.touches(selection_range):
                spans_to_check.append((span, after_range))
    # Sort spans by start position.
    spans_to_check.sort(key=lambda x: x[1].start)
    # Check if the suggestion spans exactly undoes some previous spans.
    # Note that we also record the span ranges in the current version of the file.
    suggestion_diff = _cached_precise_line_diff(existing_code, suggested_code)
    suggestion_spans_with_range = list(
        zip(
            suggestion_diff.spans,
            [
                e.shifted(selection_range.start)
                for e in suggestion_diff.span_ranges_in_before
            ],
        )
    )
    return is_exact_undo_prev_spans(
        spans_to_check,
        suggestion_spans_with_range,
    )


def is_exact_undo_prev_spans(
    prev_spans_ranges: Sequence[tuple[DiffSpan, CharRange]],
    suggestion_spans_ranges: Sequence[tuple[DiffSpan, CharRange]],
) -> bool:
    """Checks if the suggested change exactly undoes some previous change.

    For example, if prev_spans contains three spans, span 1, span 2, span 3, and the
    suggested change contains 2 line spans, span a and span b:
    It is an exact undo if:
        1. span a reverts span 1; span b revert span 2.
        2. span a reverts span 2; span b revert span 3.
        3. span a reverts span 1; span b revert span 3.
    It is NOT an exact undo if:
        1. span a reverts span 3; span b revert span 1.
        2. span a reverts span 1; span b doesn't revert any span.

    Note that we only consider a span an undo of another span if their ranges match
    exactly (in the current version of the file).
    """
    changed_parts = [
        (span, span_range)
        for span, span_range in suggestion_spans_ranges
        if not isinstance(span, NoopSpan)
    ]
    if not changed_parts:
        return False
    pointer = 0
    for span, span_range in changed_parts:
        span_has_match = False
        while pointer < len(prev_spans_ranges):
            prev_span, prev_range = prev_spans_ranges[pointer]
            if prev_range == span_range and prev_span == span.inverted():
                span_has_match = True
                break
            pointer += 1
        if not span_has_match:
            return False
    # all spans found a match, so it is an exact undo.
    return True


def is_suggestion_touching_recent_changes(
    current_text: str,
    previous_text: str,
    suggested_text: str,
    max_span_pairs_to_check: int = 500,
) -> bool:
    """Check if diff(current, suggested) touches diff(previous, current)."""
    prev_edit = _cached_precise_char_diff(previous_text, current_text)
    suggestion_edit = _cached_precise_char_diff(current_text, suggested_text)

    # Get ranges in current text for both diffs
    prev_ranges = [
        r
        for span, r in zip(prev_edit.spans, prev_edit.span_ranges_in_after)
        if not isinstance(span, NoopSpan)
    ]
    suggestion_ranges = [
        r
        for span, r in zip(suggestion_edit.spans, suggestion_edit.span_ranges_in_before)
        if not isinstance(span, NoopSpan)
    ]
    if len(prev_ranges) * len(suggestion_ranges) > max_span_pairs_to_check:
        # give up, just return False
        return False

    # Check if any ranges from the two diffs touch each other
    for prev_range in prev_ranges:
        for suggestion_range in suggestion_ranges:
            if prev_range.touches(suggestion_range):
                return True

    return False


@lru_cache(maxsize=32)
def _cached_line_map(text: str) -> LineMap:
    return LineMap(text)


@lru_cache(maxsize=128)
def _cached_precise_line_diff(before: str, after: str) -> str_diff.StrDiff:
    # we cache because the diffs in the context rarely change and precise line diff can
    # be expensive to compute
    return str_diff.precise_line_diff(before, after)


@lru_cache(maxsize=128)
def _cached_precise_char_diff(before: str, after: str) -> str_diff.StrDiff:
    # we cache because the diffs in the context rarely change and precise line diff can
    # be expensive to compute
    return str_diff.precise_char_diff(before, after)
