"""Feature extraction for next edit filter."""

import collections
from typing import Mapping, Protocol, Sequence, TypedDict, Union, cast

import numpy as np

from base.tokenizers import Tokenizer, NextEditGenSpecialTokens

Number = Union[int, float]


class NextEditFeatureExtractorProtocol(Protocol):
    """Feature extractor Protocol for next edit filter."""

    @classmethod
    def get_features_name(cls) -> list[str]:
        """Return the feature names."""
        ...

    @classmethod
    def extract_features(
        cls,
        log_probs: Sequence[float],
        token_ids: Sequence[int],
        prompt_token_ids: Sequence[int],
    ) -> Mapping[str, Number]:
        """Extract features from log_probs, token_ids, and prompt."""
        ...


class FeaturesV1(TypedDict):
    """Feature names and types for `FeatureExtractorV1`."""

    has_change_prob: float
    """Probability of <has_change> token."""
    plus_mean: float
    """Average probability of plus lines."""
    minus_mean: float
    """Average probability of minus lines."""
    plus_token_prob_min: float
    """Minimum token probability of plus lines."""
    plus_token_prob_median: float
    """Median token probability of plus lines."""
    plus_token_prob_max: float
    """Maximum token probability of plus lines."""
    minus_token_prob_min: float
    """Minimum token probability of minus lines."""
    minus_token_prob_median: float
    """Median token probability of minus lines."""
    minus_token_prob_max: float
    """Maximum token probability of minus lines."""
    num_insert_lines: float
    """Total mumber of plus lines."""
    num_delete_lines: float
    """Total mumber of minus lines."""
    num_lines_delta: float
    """Difference of number of plus and minus lines."""
    plus_moving_average_median: float
    """Median moving average of plus lines."""
    minus_moving_average_median: float
    """Median moving average of minus lines."""
    num_recent_insert_lines: float
    """Number of lines starting with + in recent changes."""
    num_recent_delete_lines: float
    """Number of lines starting with - in recent changes."""
    num_recent_lines_delta: float
    """Difference of number of lines starting with + and - in recent changes."""
    max_contiguous_insert: float
    """Maximum number of contiguous lines starting with + in recent changes."""
    max_contiguous_delete: float
    """Maximum number of contiguous lines starting with - in recent changes."""
    number_changed_lines: int
    """Number of changed lines."""


class FeatureExtractorV1:
    """Feature extractor V1 for next edit filter."""

    def __init__(self, tokenizer: Tokenizer[NextEditGenSpecialTokens] | None):
        self.tokenizer = tokenizer

    def set_tokenizer(self, tokenizer: Tokenizer[NextEditGenSpecialTokens]):
        """Set the value of tokenizer."""
        self.tokenizer = tokenizer

    @classmethod
    def get_features_name(cls) -> list[str]:
        """Return the feature names."""
        return list(FeaturesV1.__annotations__.keys())

    def extract_features(
        self,
        log_probs: Sequence[float],
        token_ids: Sequence[int],
        prompt_token_ids: Sequence[int],
    ) -> dict[str, Number]:
        """Extract features from log_probs, token_ids, and prompt."""
        if self.tokenizer is None:
            raise ValueError("Tokenizer is not set.")
        # NOTE: Pyright raises a useless type warning.
        probs = np.exp(list(log_probs))
        text, offset = self.tokenizer.detokenize_with_offsets(token_ids)

        plus_lines, minus_lines = _token_splitlines(log_probs, token_ids, text, offset)
        plus_log_probs = [lp for line in plus_lines for lp in line[0]]
        minus_log_probs = [lp for line in minus_lines for lp in line[0]]
        plus_probs = np.exp(plus_log_probs)
        minus_probs = np.exp(minus_log_probs)
        window_size = min(3, len(log_probs))

        kernel = np.ones(window_size) / window_size

        edit_lines = recent_edit_lines(prompt_token_ids, self.tokenizer)

        return cast(
            dict[str, Number],
            FeaturesV1(
                has_change_prob=probs[0],
                plus_mean=np.exp(np.mean(plus_log_probs)) if plus_log_probs else 0,
                minus_mean=np.exp(np.mean(minus_log_probs)) if minus_log_probs else 0,
                plus_token_prob_min=np.min(plus_probs) if plus_probs.size > 0 else 0,
                plus_token_prob_median=cast(float, np.median(plus_probs))
                if plus_probs.size > 0
                else 0,
                plus_token_prob_max=np.max(plus_probs) if plus_probs.size > 0 else 0,
                minus_token_prob_min=np.min(minus_probs) if minus_probs.size > 0 else 0,
                minus_token_prob_median=cast(float, np.median(minus_probs))
                if minus_probs.size > 0
                else 0,
                minus_token_prob_max=np.max(minus_probs) if minus_probs.size > 0 else 0,
                num_insert_lines=float(len(plus_lines)),
                num_delete_lines=float(len(minus_lines)),
                num_lines_delta=float(len(plus_lines) - len(minus_lines)),
                plus_moving_average_median=(
                    np.exp(np.median(np.convolve(plus_log_probs, kernel, mode="valid")))
                    if plus_log_probs
                    else 0
                ),
                minus_moving_average_median=(
                    np.exp(
                        np.median(np.convolve(minus_log_probs, kernel, mode="valid"))
                    )
                    if minus_log_probs
                    else 0
                ),
                num_recent_insert_lines=float(edit_lines["insert"]),
                num_recent_delete_lines=float(edit_lines["delete"]),
                num_recent_lines_delta=float(
                    edit_lines["insert"] - edit_lines["delete"]
                ),
                max_contiguous_insert=float(edit_lines["max_contiguous_insert"]),
                max_contiguous_delete=float(edit_lines["max_contiguous_delete"]),
                number_changed_lines=len(plus_lines) + len(minus_lines),
            ),
        )


def _token_splitlines(
    log_probs: Sequence[float],
    token_ids: Sequence[int],
    text: str,
    offset: Sequence[int],
):
    """Split log_probs and token_ids into plus and minus lines.

    Args:
        log_probs: Log probabilities of the response tokens.
        token_ids: Token IDs from the response.
        text: The text of the response.
        offset: The character offsets of the response.

    Returns:
        A tuple of plus and minus lines.
    """
    prev = 0
    plus_lines = []
    minus_lines = []
    found_plus, found_minus = False, False
    for idx, _ in enumerate(offset):
        if idx == len(offset) - 1:
            token = text[offset[idx] :]
        else:
            token = text[offset[idx] : offset[idx + 1]]
        if "+" in token:
            found_plus = True
        elif "-" in token:
            found_minus = True
        if "\n" in token:
            if found_plus:
                plus_lines.append(
                    (log_probs[prev : idx + 1], token_ids[prev : idx + 1])
                )
            elif found_minus:
                minus_lines.append(
                    (log_probs[prev : idx + 1], token_ids[prev : idx + 1])
                )
            prev = idx + 1
            found_plus, found_minus = False, False
    if token_ids[prev:] != [0]:
        if found_plus:
            plus_lines.append((log_probs[prev:], token_ids[prev:]))
        elif found_minus:
            minus_lines.append((log_probs[prev:], token_ids[prev:]))
    return plus_lines, minus_lines


def recent_edit_lines(
    token_ids: Sequence[int], tokenizer: Tokenizer[NextEditGenSpecialTokens]
) -> collections.Counter:
    """Count number of lines starting with + and - in prompt."""
    # Decodes the diff sections.
    try:
        left = token_ids.index(tokenizer.special_tokens.diff_section)
        right = token_ids.index(tokenizer.special_tokens.retrieval_section)
    except ValueError:
        # The diff section is not found.
        return collections.Counter()

    prompt = tokenizer.detokenize(token_ids[left + 1 : right])
    counter = collections.Counter()
    max_contiguous_inserts = 0
    max_contiguous_deletes = 0
    current_contiguous_inserts = 0
    current_contiguous_deletes = 0
    for line in prompt.splitlines():
        if line.startswith("+") and not line.startswith("+++"):
            current_contiguous_inserts += 1
            current_contiguous_deletes = 0
            max_contiguous_inserts = max(
                max_contiguous_inserts, current_contiguous_inserts
            )
            counter["insert"] += 1
        elif line.startswith("-") and not line.startswith("---"):
            current_contiguous_deletes += 1
            current_contiguous_inserts = 0
            max_contiguous_deletes = max(
                max_contiguous_deletes, current_contiguous_deletes
            )
            counter["delete"] += 1
    counter["max_contiguous_insert"] = max_contiguous_inserts
    counter["max_contiguous_delete"] = max_contiguous_deletes
    return counter
