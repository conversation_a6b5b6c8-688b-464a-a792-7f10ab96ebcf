"""An extension of an Any-valued dict that supports safely retrieving values by type.

Example of usage:
```python
from base.collections.typesafe_dict import TypesafeDict

# Creating the dict.
component_dict = TypesafeDict({
    "tokenizer": RogueTokenizer(...),
    "prompt_formatter": <PERSON><PERSON>rom<PERSON><PERSON><PERSON>atter(...),
})

# The following all type-check and runs correctly.
tokenizer: Tokenizer = component_dict.get_with_type("tokenizer", Tokenizer)
rogue_tokenizer: RogueTokenizer = component_dict.get_with_type(
    "tokenizer", RogueTokenizer
)

# The following will raise a TypeError at runtime.
tokenizer: Tokenizer = component_dict.get_with_type("prompt_formatter", Tokenizer)
```
"""

import collections
import functools
import logging
import typing
from typing import Optional, TypeVar, overload

T = TypeVar("T")

# Sentinel value for missing values.
_MISSING = object()


@functools.lru_cache(maxsize=1024)
def _log_type_warning(generic_type: type, origin_type: type):
    """Log a warning once when a generic type is used."""

    logging.warning(
        "TypesafeDict cannot safely check generic type %s. Checking against the base "
        "type %s. (This warning will only be logged once per type pair).",
        generic_type,
        origin_type,
    )


def _unpack_optional(cls: type) -> Optional[type]:
    """If `cls` is of type Optional[T], then return T, otherwise return None."""
    if typing.get_origin(cls) == typing.Union:
        sub_types = [
            sub_type
            for sub_type in typing.get_args(cls)
            if sub_type != type(None)  # noqa
        ]
        if len(sub_types) == 1:
            return sub_types[0]
    return None


class TypesafeDict(collections.UserDict):
    """A simple extension of dict that checks a value's type when getting it.

    Note that TypesafeDict is *not* a subclass of `dict`.
    """

    @overload
    def get_with_type(self, key: str, typ: type[T]) -> T: ...

    @overload
    def get_with_type(self, key: str, typ: type[T], default: None) -> Optional[T]: ...

    @overload
    def get_with_type(self, key: str, typ: type[T], default: T) -> T: ...

    def get_with_type(self, key: str, typ: type, default=_MISSING):
        """Fetch a key while verifying its type.

        NOTE(Special handling of Optional types): It is common to want to store a value
        or None. There are two ways to indicate to `TypesafeDict` that a None value is
        acceptable:

        1. A default value of `None` is provided.
        2. The type constraint `typ` is `Optional[T]`.

        Args:
            key: The key to fetch.
            typ: The type to check against. While most generic types with generic
                arguments cannot be checked, this function handles the common case of
                optional types. See the note above.
            default: The default value to return if the key is missing.

        Returns:
            The value of the key, or the default if the key is missing.

        Raises:
            KeyError: If the key is missing (and no default was given).
            TypeError: If the key has a value of a different type.
        """
        if key not in self:
            if default is _MISSING:
                raise KeyError(key)
            return default

        is_none_allowed = (typ == type(None)) or default is None  # type: ignore
        if (typ_ := typing.get_origin(typ)) is None:
            # The type isn't generic.
            pass
        elif (optional_typ_ := _unpack_optional(typ)) is not None:
            # The type is an Optional[T], which we handle.
            typ = optional_typ_
            is_none_allowed = True
        elif typing.get_args(typ):
            # The type is some other generic type. We will warn the user that we don't
            # support generic types, and then use the base type.
            _log_type_warning(typ, typ_)
            typ = typ_

        value = self[key]
        # Special case for None values.
        if value is None and is_none_allowed:
            return value
        elif not isinstance(self[key], typ):
            raise TypeError(f"{key} has a value of type {type(self[key])}, not {typ}.")
        return value
