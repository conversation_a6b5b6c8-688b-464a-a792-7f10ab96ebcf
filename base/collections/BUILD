load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "typesafe_dict",
    srcs = [
        "typesafe_dict.py",
    ],
    visibility = ["//visibility:public"],
)

pytest_test(
    name = "typesafe_dict_test",
    srcs = ["typesafe_dict_test.py"],
    deps = [":typesafe_dict"],
)

py_library(
    name = "typesafe_container",
    srcs = [
        "typesafe_container.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":typesafe_dict",
        requirement("typing_inspect"),
    ],
)

pytest_test(
    name = "typesafe_container_test",
    srcs = ["typesafe_container_test.py"],
    deps = [":typesafe_container"],
)
