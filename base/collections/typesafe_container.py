"""A type that allows a typesafe access to a value.

Example of usage:
```python
from base.collections.typesafe_container import TypesafeContainer

@dataclass
class Foo:
    value: str


@dataclass
class Bar:
    value: str

c = TypesafeContainer()
c.set(Foo("a"))
f = c.get(Foo)
assert f is not None
assert f.value == "a"
b = c.get(Bar)
assert b is None

c.set(Bar("b"))
b = c.get(Bar)
assert b is not None
assert b.value == "b"
```

"""

import functools
from typing import TypeVar
import typing

from typing_inspect import (
    is_callable_type,
    is_generic_type,
    is_optional_type,
    is_union_type,
)

from base.collections.typesafe_dict import TypesafeDict

T = TypeVar("T")


@functools.lru_cache(maxsize=1024)
def _is_supported_type(typ: type):
    return not (
        is_generic_type(typ)
        or is_union_type(typ)
        or is_optional_type(typ)
        or is_callable_type(typ)
    )


def _get_key(typ: type) -> str:
    """Get the key for a type."""
    if not _is_supported_type(typ):
        raise NotImplementedError("Generic types are not supported.")
    return typ.__qualname__


class TypesafeContainer:
    """A simple extension of dict that checks a value's type when getting it.

    Note that TypesafeContainer is *not* a subclass of `dict`.
    """

    def __init__(self, value: object | None = None):
        self._dict = TypesafeDict()
        if value is not None:
            self.set(value)

    def set(self, value: object) -> None:
        """Set a key while verifying its type.

        Args:
            key: The key to set.
            value: The value to set.
        """
        key = _get_key(type(value))
        self._dict[key] = value

    def get(self, typ: type, default: T | None = None) -> T | None:
        """Fetch a key depending on the type.

        Args:
            key: The key to fetch.
            default: The default value to return if the key is missing.

        Returns:
            The value of the key, or the default if the key is missing.
        """
        key = _get_key(typ)
        if key not in self._dict:
            return default
        if not isinstance(self._dict[key], typ):
            raise TypeError(
                f"{key} has a value of type {type(self._dict[key])}, not {typ}."
            )
        return self._dict[key]

    def __eq__(self, value: object) -> bool:
        if not isinstance(value, TypesafeContainer):
            return False
        return self._dict == value._dict

    def __hash__(self):
        raise TypeError(f"unhashable type: '{self.__class__.__name__}'")


class FrozenTypesafeContainer:
    """A simple extension of dict that checks a value's type when getting it.

    Note that FrozenTypesafeContainer is *not* a subclass of `dict`.
    """

    def __init__(self, values: typing.Iterable[object] | None = None):
        self._dict = TypesafeDict()
        if values:
            for value in values:
                self._set(value)

    def _set(self, value: object) -> None:
        """Set a key while verifying its type.

        Args:
            key: The key to set.
            value: The value to set.
        """
        assert len(self._dict) == 0
        key = _get_key(type(value))
        self._dict[key] = value

    def get(self, typ: type, default: T | None = None) -> T | None:
        """Fetch a key depending on the type.

        Args:
            key: The key to fetch.
            default: The default value to return if the key is missing.

        Returns:
            The value of the key, or the default if the key is missing.
        """
        key = _get_key(typ)
        if key not in self._dict:
            return default
        if not isinstance(self._dict[key], typ):
            raise TypeError(
                f"{key} has a value of type {type(self._dict[key])}, not {typ}."
            )
        return self._dict[key]

    def __eq__(self, value: object) -> bool:
        if not isinstance(value, FrozenTypesafeContainer):
            return False
        return self._dict == value._dict

    def __hash__(self):
        return hash(tuple(self._dict.values()))
