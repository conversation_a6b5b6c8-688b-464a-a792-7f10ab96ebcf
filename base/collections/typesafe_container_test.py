import typing
from dataclasses import dataclass

import pytest

from base.collections.typesafe_container import (
    FrozenTypesafeContainer,
    TypesafeContainer,
)


@dataclass(frozen=True)
class Foo:
    """A simple dataclass for testing."""

    value: str


@dataclass(frozen=True)
class Bar:
    """A simple dataclass for testing."""

    value: str


def test_get_works():
    """Test the basic functionality of TypesafeContainer."""
    c = TypesafeContainer()
    c.set(Foo("a"))
    f = c.get(Foo)
    assert f is not None
    assert f.value == "a"
    b = c.get(Bar)
    assert b is None

    c.set(Bar("b"))
    b = c.get(Bar)
    assert b is not None
    assert b.value == "b"


def test_constructor():
    """Test the basic functionality of TypesafeContainer."""
    c = TypesafeContainer(Foo("a"))
    f = c.get(Foo)
    assert f is not None
    assert f.value == "a"


def test_eq():
    """Test the basic functionality of TypesafeContainer."""
    c = TypesafeContainer(Foo("a"))
    c2 = TypesafeContainer(Foo("a"))
    assert c == c2
    c3 = TypesafeContainer(Bar("a"))
    assert c != c3


def test_update():
    """Test the basic functionality of TypesafeContainer."""
    c = TypesafeContainer()
    c.set(Foo("a"))
    f = c.get(Foo)
    assert f is not None
    assert f.value == "a"

    c.set(Foo("b"))
    f = c.get(Foo)
    assert f is not None
    assert f.value == "b"


def test_native():
    """Test the basic functionality of TypesafeContainer."""
    c = TypesafeContainer()
    c.set("a")
    c.set(1)

    f = c.get(str)
    assert f is not None
    assert f == "a"

    f = c.get(int)
    assert f is not None
    assert f == 1


T = typing.TypeVar("T")


class GenericType(typing.Generic[T]):
    """A derived class."""

    def __init__(self, value: T):
        self.value = value


def test_get_with_generic_types():
    """Test the basic functionality of TypesafeContainer."""
    c = TypesafeContainer()
    with pytest.raises(NotImplementedError):
        c.set(GenericType[str]("a"))

    with pytest.raises(NotImplementedError):
        c.get(GenericType[str])


def test_others():
    c = TypesafeContainer()
    c.set(1)
    c.set(1.0)
    c.set(True)
    c.set([1, 2, 3])
    c.set({"a": 1, "b": 2})

    assert c.get(int) == 1
    assert c.get(float) == 1.0
    assert c.get(bool) is True
    assert c.get(list) == [1, 2, 3]
    assert c.get(dict) == {"a": 1, "b": 2}


def test_null():
    c = TypesafeContainer()

    with pytest.raises(NotImplementedError):
        c.get(type(None))

    with pytest.raises(NotImplementedError):
        c.set(None)


def test_tuple():
    c = TypesafeContainer()
    c.set((1, 2, 3))
    c.set((1, 2, "3"))
    assert c.get(tuple) == (1, 2, "3")


def test_callable():
    c = TypesafeContainer()
    c.set(lambda x: x)
    assert c.get(type(lambda x: x))


def test_frozen_get_works():
    """Test the basic functionality of FrozenTypesafeContainer."""
    c = FrozenTypesafeContainer([Foo("a")])
    f = c.get(Foo)
    assert f is not None
    assert f.value == "a"
    b = c.get(Bar)
    assert b is None


def test_frozen_native():
    """Test the basic functionality of FrozenTypesafeContainer."""
    c = FrozenTypesafeContainer("a")
    f = c.get(str)
    assert f is not None
    assert f == "a"

    f = c.get(int)
    assert f is None


def test_frozen_others():
    FrozenTypesafeContainer([1])
    FrozenTypesafeContainer([1.0])
    FrozenTypesafeContainer([True])
    FrozenTypesafeContainer([[1, 2, 3]])
    FrozenTypesafeContainer([{"a": 1, "b": 2}])


def test_frozen_null():
    FrozenTypesafeContainer()


def test_frozen_tuple():
    c = FrozenTypesafeContainer([(1, 2, "3")])
    assert c.get(tuple) == (1, 2, "3")


def test_frozen_eq():
    """Test the basic functionality of TypesafeContainer."""
    c = FrozenTypesafeContainer([Foo("a")])
    c2 = FrozenTypesafeContainer([Foo("a")])
    assert c == c2
    c3 = FrozenTypesafeContainer([Bar("a")])
    assert c != c3


def test_frozen_hash():
    """Test the basic functionality of TypesafeContainer."""
    c = FrozenTypesafeContainer([Foo("a")])
    c2 = FrozenTypesafeContainer([Foo("a")])
    assert hash(c) == hash(c2)

    c3 = FrozenTypesafeContainer([Bar("b")])
    assert hash(c) != hash(c3)
