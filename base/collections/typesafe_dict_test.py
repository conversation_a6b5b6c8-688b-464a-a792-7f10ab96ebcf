"""Tests for base.collections.typesafe_dict."""

import logging
import logging.handlers
import typing

import pytest

from base.collections.typesafe_dict import TypesafeDict


class BaseType:
    """A base class."""

    pass


class DerivedType(BaseType):
    """A derived class."""

    pass


T = typing.TypeVar("T")


class GenericType(typing.Generic[T]):
    """A derived class."""


def test_get_works():
    """Test the basic functionality of TypesafeDict."""
    dct = TypesafeDict(
        {
            "a": 1,
            "b": "hello",
            "c": 1.0,
            "d": [1, 2, 3],
            "e": {"a": 1, "b": 2},
            "f": None,
            "base": BaseType(),
            "derived": DerivedType(),
        }
    )

    assert dct.get_with_type("a", int) == 1
    assert dct.get_with_type("b", str) == "hello"
    assert dct.get_with_type("c", float) == 1.0
    assert dct.get_with_type("d", list) == [1, 2, 3]
    assert dct.get_with_type("e", dict) == {"a": 1, "b": 2}
    assert dct.get_with_type("f", type(None)) is None
    # Here, we are checking that the following calls don't exception.
    assert dct.get_with_type("base", BaseType) is not None
    assert dct.get_with_type("derived", DerivedType) is not None
    assert dct.get_with_type("derived", BaseType) is not None


def test_get_with_optional_type():
    """Test getting `None` if the default hints `None` is allowed."""
    # Asserting how Python handles `Optional[T]` for an abundance of caution.
    assert typing.Optional[int] == typing.Union[int, type(None)]

    dct = TypesafeDict({"a": None})

    with pytest.raises(TypeError):
        # Raises a TypeError because `a` is None.
        dct.get_with_type("a", int)

    with pytest.raises(KeyError):
        # Raises a KeyError because `b` doesn't exist and no default was given.
        dct.get_with_type("b", typing.Optional[int])

    # Returns None because the type hint allows for it.
    assert dct.get_with_type("a", typing.Optional[int]) == None
    # Returns None because the default value hints it is allowed.
    assert dct.get_with_type("a", int, None) == None
    # Returns None because its the default value.
    assert dct.get_with_type("b", int, None) == None


def test_get_with_missing_key():
    """Test the basic functionality of TypesafeDict."""
    dct = TypesafeDict({})

    with pytest.raises(KeyError):
        dct.get_with_type("c", str)

    assert dct.get_with_type("c", str, "default") == "default"


def test_get_with_wrong_type():
    """Test the basic functionality of TypesafeDict."""
    dct = TypesafeDict(
        {
            "a": 1,
            "c": BaseType(),
        }
    )

    with pytest.raises(TypeError):
        a: str = dct.get_with_type("a", str)  # noqa

    with pytest.raises(TypeError):
        c: DerivedType = dct.get_with_type("c", DerivedType)  # noqa


def test_get_with_generic_types():
    """Test the basic functionality of TypesafeDict."""
    dct = TypesafeDict({"a": 1, "c": [1], "d": GenericType[int]()})

    assert dct.get_with_type("c", list) == [1]
    assert dct.get_with_type("c", list[int]) == [1]
    assert dct.get_with_type("d", GenericType) == dct["d"]
    assert dct.get_with_type("d", GenericType[int]) == dct["d"]
    # We can't check the type of the list elements, just that it's a list.
    assert dct.get_with_type("d", GenericType[BaseType]) == dct["d"]

    # We still check generic types against their base class.
    with pytest.raises(TypeError):
        c = dct.get_with_type("c", GenericType[int])  # noqa


def test_get_with_generic_types_logs():
    # Add a memory handler
    log_capture = logging.handlers.MemoryHandler(capacity=10, target=None)
    logger = logging.getLogger()
    logger.addHandler(log_capture)

    dct = TypesafeDict({"c": [1]})

    # Log the type check.
    dct.get_with_type("c", list[GenericType])
    log_capture.flush()
    assert len(log_capture.buffer) == 1
    # But only log it once.
    dct.get_with_type("c", list[GenericType])
    log_capture.flush()
    assert len(log_capture.buffer) == 1

    # Log a different type check.
    dct.get_with_type("c", list[float])
    log_capture.flush()
    assert len(log_capture.buffer) == 2

    # Clean up by removing the handler
    logger.removeHandler(log_capture)
