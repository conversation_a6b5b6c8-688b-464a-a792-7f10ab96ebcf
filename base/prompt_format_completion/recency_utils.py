"""Utility functions for recency-based retrieval processing."""

from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Union

import structlog

from base.languages.language_guesser import guess_language
from base.prompt_format.recency_info import ReplacementText, ViewedContentEvent
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.retrieval.chunking.smart_chunking import SmartChunker

log = structlog.get_logger()


class TimestampState(Enum):
    """Represents the state of a file timestamp when it cannot be determined."""

    UNDETERMINED = "undetermined"


def get_latest_file_timestamps(
    replacement_texts: list[ReplacementText],
) -> dict[str, Union[datetime, TimestampState]]:
    """
    Get the latest timestamp for each file mentioned in replacement texts.

    If there is a replacement text that touches a file, but the text does not have a timestamp,
    then we do not have enough information to determine the timestamp, so we mark the file as None.

    Args:
        replacement_texts: List of ReplacementText objects

    Returns:
        Dictionary mapping file paths to their latest timestamps (as datetime objects)
        Files with any replacement text that has no timestamp get None (representing undetermined time)
    """
    file_timestamps = {}

    for replacement in replacement_texts:
        file_path = replacement.path

        # If any replacement text has missing timestamp, mark the file as undetermined
        if replacement.timestamp is None:
            file_timestamps[file_path] = TimestampState.UNDETERMINED
        else:
            if (
                file_path in file_timestamps
                and file_timestamps[file_path] is TimestampState.UNDETERMINED
            ):
                continue  # File already has undetermined timestamp, keep it

            # For each file, keep the maximum timestamp across all replacement texts
            if file_path not in file_timestamps:
                file_timestamps[file_path] = replacement.timestamp
            else:
                file_timestamps[file_path] = max(
                    file_timestamps[file_path], replacement.timestamp
                )

    return file_timestamps


def filter_stale_viewed_content(
    viewed_contents: list[ViewedContentEvent],
    file_timestamps: dict[str, datetime | TimestampState],
    buffer_seconds: float = 60.0,  # 1 minute buffer
) -> list[ViewedContentEvent]:
    """
    Filter out viewed content that is stale based on file modification timestamps.

    Args:
        viewed_contents: List of ViewedContentEvent objects
        file_timestamps: Dictionary mapping file paths to their latest timestamps
        buffer_seconds: Buffer time in seconds to add to file timestamps

    Returns:
        Filtered list of ViewedContentEvent objects
    """
    filtered_content = []

    for viewed_content in viewed_contents:
        file_path = viewed_content.path

        # Keep viewed content if the file was not edited
        if file_path not in file_timestamps:
            filtered_content.append(viewed_content)
        else:
            latest_file_timestamp = file_timestamps[file_path]
            # Filter out viewed content from files with undetermined timestamp (content may be stale)
            if latest_file_timestamp is TimestampState.UNDETERMINED:
                continue

            # Only keep viewed content if it's newer than the cutoff
            cutoff_timestamp = latest_file_timestamp + timedelta(seconds=buffer_seconds)
            if viewed_content.timestamp >= cutoff_timestamp:
                filtered_content.append(viewed_content)

    return filtered_content


def deduplicate_viewed_content_against_replacements(
    viewed_contents: list[ViewedContentEvent],
    replacement_texts: list[ReplacementText],
) -> list[ViewedContentEvent]:
    """
    Remove viewed content that overlaps with replacement text.

    Args:
        viewed_contents: List of ViewedContentEvent objects
        replacement_texts: List of ReplacementText objects

    Returns:
        Filtered list of ViewedContentEvent objects with no overlaps to replacement text
    """
    if not viewed_contents or not replacement_texts:
        return viewed_contents

    grouped_replacement_text: dict[str, list[ReplacementText]] = {}
    for replacement in replacement_texts:
        if replacement.path not in grouped_replacement_text:
            grouped_replacement_text[replacement.path] = []
        grouped_replacement_text[replacement.path].append(replacement)

    filtered_viewed_contents = []
    for viewed_content in viewed_contents:
        file_path = viewed_content.path
        viewed_range = CharRange(viewed_content.char_start, viewed_content.char_end)

        has_overlap = False
        for replacement in grouped_replacement_text.get(file_path, []):
            replacement_range = CharRange(replacement.char_start, replacement.char_end)
            if viewed_range.overlaps(replacement_range):
                log.debug(
                    f"Filtering viewed content overlapping with replacement."
                    f"viewed chars={viewed_content.char_start}-{viewed_content.char_end}, "
                    f"replacement chars={replacement.char_start}-{replacement.char_end}"
                )
                has_overlap = True
                break

        if not has_overlap:
            filtered_viewed_contents.append(viewed_content)

    return filtered_viewed_contents


def limit_viewed_content_chunk_size(
    viewed_contents: list[ViewedContentEvent],
    max_chunk_size: int = 1024,
    target_line_ratio: float = 0.5,
) -> list[ViewedContentEvent]:
    """
    Limit the size of viewed content chunks using smart chunking.

    For chunks larger than max_chunk_size, this function:
    1. Uses SmartChunker to intelligently split content into chunks
    2. Selects a chunk that contains the line at the specified ratio position
    3. Preserves line boundaries and code structure

    Args:
        viewed_contents: List of ViewedContentEvent objects
        max_chunk_size: Maximum allowed character size for chunks (default: 1024)
        target_line_ratio: Ratio (0.0-1.0) indicating which line position to target
                          (e.g., 0.33 = 33% through the content, 0.5 = middle)

    Returns:
        List of ViewedContentEvent objects with content limited to max_chunk_size
    """
    if not viewed_contents:
        return viewed_contents

    result = []
    chunker = SmartChunker(max_chunk_chars=max_chunk_size)

    for viewed_content in viewed_contents:
        if len(viewed_content.visible_content) <= max_chunk_size:
            # Content is already within limit
            result.append(viewed_content)
            continue

        # Content is too large, use smart chunking
        chunks = chunker.split_chunks(
            viewed_content.visible_content, lang=guess_language(viewed_content.path)
        )

        if len(chunks) == 0:
            log.warning(
                "Failed to chunk content for viewed content in recency chunking"
            )
            continue

        # Select a chunk that contains lines from the specified ratio position
        if len(chunks) == 1:
            selected_chunk = chunks[0]
        else:
            # Calculate the target line position based on the specified ratio
            total_lines = viewed_content.line_end - viewed_content.line_start + 1
            target_line_offset = max(
                0, min(int(total_lines * target_line_ratio), total_lines - 1)
            )

            # Find the chunk that contains or is closest to the target line
            best_chunk_idx = 0
            best_distance = float("inf")

            for i, chunk in enumerate(chunks):
                chunk_start_line = chunk.line_offset
                chunk_end_line = chunk.line_offset + chunk.length_in_lines - 1

                # Calculate distance from target line to this chunk
                if target_line_offset < chunk_start_line:
                    distance = chunk_start_line - target_line_offset
                elif target_line_offset > chunk_end_line:
                    distance = target_line_offset - chunk_end_line
                else:
                    distance = 0  # Target line is within this chunk

                if distance <= best_distance:
                    best_distance = distance
                    best_chunk_idx = i

            selected_chunk = chunks[best_chunk_idx]

        # Calculate new line and character ranges based on the selected chunk
        new_line_start = viewed_content.line_start + selected_chunk.line_offset
        new_line_end = new_line_start + selected_chunk.length_in_lines - 1
        new_char_start = viewed_content.char_start + selected_chunk.char_offset
        new_char_end = new_char_start + len(selected_chunk.text)

        truncated_viewed_content = ViewedContentEvent(
            path=viewed_content.path,
            file_blob_name=viewed_content.file_blob_name,
            visible_content=selected_chunk.text,
            line_start=new_line_start,
            line_end=new_line_end,
            char_start=new_char_start,
            char_end=new_char_end,
            timestamp=viewed_content.timestamp,
        )
        result.append(truncated_viewed_content)

    return result
