"""The simple Elden prompt formatter removed all special tokens except for fim_prefix, fim_middle, and fim_suffix.

NOTE(Xuanyi): I also remove the caching optimization to exclude all possible factors that can affect the model performance.
The overall structure of this prompt formatter is:
- recency chunks
- other chunks
- signature chunks
- path
- prefix
- suffix
We use nature language to separate the different components, and only
- add fim_prefix at the beginning of the prompt
- add fim_middle after prefix
- add fim_suffix at the end of the prompt
"""

import dataclasses
import logging
from collections import defaultdict

import dataclasses_json

from base.prompt_format.common import PromptChunk
from base.prompt_format.util import (
    concatenate_retrieved_chunks,
    head_n,
    prompt_chunks_by_origin,
    trailing_n,
)
from base.prompt_format_completion.overlap import filter_visible_chunks_by_content
from base.prompt_format_completion.prompt_formatter import (
    CompletionPromptFormatter,
    PromptFormatterOutput,
    PromptInput,
    TokenList,
)
from base.prompt_format_completion.token_apportionment import (
    TokenApportionment,
)
from base.tokenizers.tokenizer import RagSpecialTokens, Tokenizer


@dataclasses.dataclass
class SimpleEldenPromptFormatterConfig(dataclasses_json.DataClassJsonMixin):
    """The configuration for the simple Elden prompt formatter."""

    max_prompt_length: int
    """The maximum number of tokens in the prompt."""

    token_config: TokenApportionment
    """The token apportionment configuration."""

    per_retriever_max_tokens: dict[str, int] = dataclasses.field(default_factory=dict)
    """The maximal number of tokens that should be used for each retriever, if specified."""

    filter_visible_chunks_by_content: bool = True
    """Whether to filter retrieved chunks by content."""

    version: str = "v1.0"
    """The prompt formatter version."""

    def __post_init__(self):
        if self.max_prompt_length < 0:
            raise ValueError(f"{self.max_prompt_length=} must be non-negative.")
        for retriever, budget in self.per_retriever_max_tokens.items():
            assert (
                budget >= 0
            ), f"The retrieval budget for {retriever} must be non-negative."


class SimpleEldenPromptFormatter(CompletionPromptFormatter):
    """The prompter for the Elden models."""

    def __init__(
        self,
        config: SimpleEldenPromptFormatterConfig,
        tokenizer: Tokenizer,
    ):
        self.config = config
        self.signature_chunk_origin = "signature_retriever"
        self.tokenizer = tokenizer

        # Checking for concrete types here provides static type checking with
        # runtime protection.
        special_tokens = self.tokenizer.special_tokens
        if not isinstance(special_tokens, RagSpecialTokens):
            raise ValueError(
                f"Can't use the given tokenizer with EnderPromptFormatter: {type(self.tokenizer)}."
                f"{special_tokens=} must have RagSpecialTokens."
            )

        # We use tuple here to make them immutable.
        self.ret_start = tuple(tokenizer.tokenize_safe("# Retrieved Chunks Start:\n"))
        self.ret_end = tuple(tokenizer.tokenize_safe("\n# Retrieved Chunks Finish.\n"))
        self.sig_start = tuple(tokenizer.tokenize_safe("# Signature Chunks Start:\n"))
        self.sig_end = tuple(tokenizer.tokenize_safe("\n# Signature Chunks Finish.\n"))
        self.sig_delimiter = (special_tokens.newline, special_tokens.newline)

        self.fim_prefix = (special_tokens.fim_prefix,)
        self.fim_suffix = (special_tokens.fim_suffix,)
        self.fim_middle = (special_tokens.fim_middle,)
        self.special_file_token = special_tokens.filename
        self.filename_tokens = tuple(tokenizer.tokenize_safe("file path: "))
        self.newline = (special_tokens.newline,)
        self.begin_sequence = special_tokens.begin_sequence

    def _tokenize_signature_chunk(self, doc: PromptChunk) -> TokenList:
        return self.tokenizer.tokenize_safe(doc.text)

    def _tokenize_retrieval_chunk(self, doc: PromptChunk) -> TokenList:
        result: list[int] = []
        result += list(self.filename_tokens)
        result += trailing_n(
            self.tokenizer.tokenize_safe(doc.path),
            self.config.token_config.path_len,
        )
        result += self.newline
        result += self.tokenizer.tokenize_safe(doc.text)
        result += self.newline
        return result

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        r"""Format prompt for Ender models.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            max_output_token_count: The maximal number of tokens that the current completion request should generate.

        Returns:
            A prompt of length at most self.seq_length - max_output_token_count, in tokens.
        """
        path_tokens = self.tokenizer.tokenize_safe(prompt_input.path)
        raw_prefix_tokens = self.tokenizer.tokenize_safe(prompt_input.prefix)
        raw_suffix_tokens = self.tokenizer.tokenize_safe(prompt_input.suffix)

        num_preference_tokens = (
            len(self.begin_sequence)
            + len(self.fim_prefix)
            + len(self.fim_middle)
            + len(self.fim_suffix)
            + len(self.ret_start)
            + len(self.ret_end)
            + len(self.sig_start)
            + len(self.sig_end)
        )

        token_counts = self.config.token_config
        max_prompt_length = self.config.max_prompt_length
        logging.info("Token allocation: %s", token_counts)

        if self.config.version == "v1.0":
            all_path_tokens = (
                list(self.filename_tokens)
                + trailing_n(path_tokens, token_counts.path_len)
                + list(self.newline)
            )
        elif self.config.version == "v2.0":
            all_path_tokens = (
                [self.special_file_token]
                + list(self.filename_tokens)
                + trailing_n(path_tokens, token_counts.path_len)
                + list(self.newline)
            )
        else:
            raise ValueError(f"Unknown version: {self.config.version}")
        logging.info(
            "Used %d sentinel tokens, %d path tokens",
            num_preference_tokens,
            len(all_path_tokens),
        )

        all_prefix_tokens = trailing_n(raw_prefix_tokens, token_counts.prefix_len)
        all_suffix_tokens = head_n(raw_suffix_tokens, token_counts.suffix_len)

        ret_chunks = list(prompt_input.retrieved_chunks)
        if self.config.filter_visible_chunks_by_content:
            prefix_chars = self.tokenizer.detokenize(all_prefix_tokens)
            suffix_chars = self.tokenizer.detokenize(all_suffix_tokens)
            kept_ret_chunks = list(
                filter_visible_chunks_by_content(
                    path=prompt_input.path,
                    prompt_prefix=prefix_chars,
                    prompt_suffix=suffix_chars,
                    chunks=ret_chunks,
                )
            )
        else:
            kept_ret_chunks = ret_chunks
        logging.info(
            "Kept %d / %d retrieval chunk tokens",
            len(kept_ret_chunks),
            len(ret_chunks),
        )

        # materialize the chunks so we can treat each origin separately
        retrieved_chunks_dict = defaultdict(
            list, prompt_chunks_by_origin(kept_ret_chunks)
        )

        # Signature Retrieval Section
        signature_budget = min(
            self.config.per_retriever_max_tokens.get(self.signature_chunk_origin, 0),
            token_counts.retrieval_len,
        )
        assert signature_budget >= 0, f"{signature_budget=}"
        signature_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(
                self._tokenize_signature_chunk(chunk)
                for chunk in retrieved_chunks_dict[self.signature_chunk_origin]
            ),
            separator_tokens=self.sig_delimiter,
            max_total_tokens=signature_budget,
        )
        all_signature_tokens = (
            list(self.sig_start) + signature_tokens + list(self.sig_end)
        )
        logging.info(
            "Used %d / %d signature chunk tokens",
            len(all_signature_tokens),
            signature_budget,
        )

        # Recency + Other Retrieval Section
        retrieval_budget = min(
            max_prompt_length
            - len(self.begin_sequence)
            - len(self.fim_prefix)
            - len(all_signature_tokens)
            - len(all_path_tokens)
            - len(all_prefix_tokens)
            - len(self.fim_suffix)
            - len(all_suffix_tokens)
            - len(self.fim_middle)
            - len(self.ret_start)
            - len(self.ret_end),
            token_counts.retrieval_len - len(signature_tokens),
        )
        assert retrieval_budget >= 0, f"{retrieval_budget=}/{max_prompt_length=}"

        recency_retrieval_budget = min(
            self.config.per_retriever_max_tokens.get("recency_retriever", 0),
            retrieval_budget,
        )

        recency_chunks = [chunk for chunk in retrieved_chunks_dict["recency_retriever"]]
        included_recency_chunk_indices = set()
        recency_retrieval_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(
                self._tokenize_retrieval_chunk(chunk) for chunk in recency_chunks
            ),
            separator_tokens=[],
            max_total_tokens=recency_retrieval_budget,
            used_indices_output=included_recency_chunk_indices,
        )
        if any(
            recency_chunks[idx].unique_id is None
            for idx in included_recency_chunk_indices
        ):
            logging.warning("Recency chunks are missing unique IDs: cannot deduplicate")
        recency_chunk_ids = {
            recency_chunks[idx].unique_id
            for idx in included_recency_chunk_indices
            if recency_chunks[idx].unique_id is not None
        }
        logging.info(
            "Used %d / %d / %d recency chunk tokens from %d recency chunks",
            len(recency_retrieval_tokens),
            recency_retrieval_budget,
            retrieval_budget,
            len(recency_chunks),
        )
        other_retrieval_budget = max(
            0, retrieval_budget - len(recency_retrieval_tokens)
        )

        # all other dense chunks
        other_chunks: list[PromptChunk] = []
        for origin, chunks in retrieved_chunks_dict.items():
            # skip chunks for origins that were already processed
            if origin not in [self.signature_chunk_origin, "recency_retriever"]:
                # skip chunks that were already included as recency chunks
                other_chunks.extend(
                    [
                        chunk
                        for chunk in chunks
                        if chunk.unique_id not in recency_chunk_ids
                    ]
                )

        other_retrieval_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(
                self._tokenize_retrieval_chunk(chunk) for chunk in other_chunks
            ),
            separator_tokens=[],
            max_total_tokens=other_retrieval_budget,
        )
        logging.info(
            "Used %d / %d other chunk tokens",
            len(other_retrieval_tokens),
            other_retrieval_budget,
        )
        all_retrieval_tokens: TokenList = (
            list(self.ret_start)
            + recency_retrieval_tokens
            + other_retrieval_tokens
            + list(self.ret_end)
        )
        tokens = list(self.begin_sequence)
        if self.config.version == "v1.0":
            tokens += (
                list(self.fim_prefix)
                + all_retrieval_tokens
                + all_signature_tokens
                + all_path_tokens
                + all_prefix_tokens
                + list(self.fim_suffix)
                + all_suffix_tokens
                + list(self.fim_middle)
            )
        elif self.config.version == "v2.0":
            tokens += (
                all_retrieval_tokens
                + all_signature_tokens
                + all_path_tokens
                + list(self.fim_prefix)
                + all_prefix_tokens
                + list(self.fim_suffix)
                + all_suffix_tokens
                + list(self.fim_middle)
            )
        else:
            raise ValueError(f"Unknown version {self.config.version}")

        logging.info(
            "Used %d / %d tokens for prompt; reserving %d for output",
            len(tokens),
            max_prompt_length,
            max_output_token_count,
        )
        assert len(tokens) <= max_prompt_length
        return PromptFormatterOutput([tokens])
