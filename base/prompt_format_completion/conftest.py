"""Contains shared fixtures for prompt formatting unit tests."""

from typing import Iterable

import pytest

from base.prompt_format_completion import PromptChunk, PromptInput


@pytest.fixture()
def example_input():
    """Returns an example prompt input."""
    return PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(),
    )


@pytest.fixture()
def example_chunks() -> Iterable[PromptChunk]:
    """Returns examples.

    The chunks are assumed to be ordered in high to low scoring.
    """
    return (  # tuple to make it harder to modify accidentally
        PromptChunk(
            text="# You can aggregate\n# with a maxing\n# function.\n",
            path="src/bar.py",
        ),
        PromptChunk(
            text="# You can aggregate\n# with a pooling function.\n",
            path="src/foo.py",
        ),
    )


@pytest.fixture()
def example_chunks_no_newline() -> Iterable[PromptChunk]:
    """Returns example chunk where the text doesn't end on a newline.

    That can e.g. happen at the end of a file.

    The chunks are assumed to be ordered in high to low scoring.
    """
    return [PromptChunk(text="def aggregate(a,b):\n    while", path="src/foo.py")]
