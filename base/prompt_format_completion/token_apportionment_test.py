"""Module containing code related to the token apportionment."""

from base.prompt_format_completion.token_apportionment import (
    TokenApportionment,
    apportion_context_budget,
)


def test_apportion_context_budget_with_sufficient_budget():
    actual = apportion_context_budget(
        max_content_length=1000,
        input_fraction=0.5,
        prefix_fraction=0.75,
        path_prefix=True,
        max_path_tokens=20,
        # Request
        num_preference_tokens=4,
        num_path_tokens=10,
        num_prefix_tokens=10,
        num_suffix_tokens=10,
        num_path_sep_tokens=1,
        num_suffix_sep_tokens=1,
    )
    expected = TokenApportionment(
        path_len=10,
        prefix_len=10,
        suffix_len=10,
        retrieval_len=1000 - (4 + 10 + 10 + 10 + 1 + 1),
    )
    assert actual == expected


def test_apportion_context_budget_with_insufficient_budget():
    actual = apportion_context_budget(
        max_content_length=30,
        input_fraction=0.5,
        prefix_fraction=0.75,
        path_prefix=True,
        max_path_tokens=20,
        # Request
        num_preference_tokens=4,
        num_path_tokens=10,
        num_prefix_tokens=10,
        num_suffix_tokens=10,
        num_path_sep_tokens=1,
        num_suffix_sep_tokens=1,
    )
    expected = TokenApportionment(
        path_len=10,
        prefix_len=4,
        suffix_len=0,
        retrieval_len=11,
    )
    assert actual == expected
