load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "token_apportionment",
    srcs = [
        "token_apportionment.py",
    ],
    visibility = [
        # delete
        "//base/prompt_format_retrieve:__subpackages__",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

pytest_test(
    name = "token_apportionment_test",
    srcs = [
        "token_apportionment_test.py",
    ],
    deps = [
        ":token_apportionment",
    ],
)

py_library(
    name = "stateless_caching",
    srcs = [
        "stateless_caching.py",
    ],
    deps = [
        "//base/ranges",
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "stateful_caching",
    srcs = [
        "stateful_caching.py",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "overlap",
    srcs = [
        "overlap.py",
    ],
    visibility = [
        "//services/lib/retrieval:__subpackages__",
    ],
    deps = [
        "//base/prompt_format:common",
        "//base/prompt_format:recency_info",
        "//base/ranges",
        requirement("dataclasses_json"),
        requirement("prometheus-client"),
    ],
)

py_library(
    name = "recency_utils",
    srcs = [
        "recency_utils.py",
    ],
    visibility = [
        "//services/lib/retrieval:__subpackages__",
    ],
    deps = [
        "//base/prompt_format:recency_info",
        "//base/ranges:range_types",
        "//base/retrieval/chunking",
        requirement("structlog"),
    ],
)

pytest_test(
    name = "overlap_test",
    srcs = ["overlap_test.py"],
    deps = [
        ":overlap",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format:recency_info",
        "//base/ranges",
    ],
)

pytest_test(
    name = "stateless_caching_test",
    srcs = ["stateless_caching_test.py"],
    deps = [
        ":stateless_caching",
        "//base/ranges",
    ],
)

pytest_test(
    name = "recency_utils_test",
    srcs = ["recency_utils_test.py"],
    deps = [
        ":recency_utils",
    ],
)

py_library(
    name = "prompt_formatter",
    srcs = [
        "prompt_formatter.py",
    ],
    deps = [
        ":token_apportionment",
        "//base/prompt_format:common",
        "//base/python/au_itertools:reusable_iterable",
    ],
)

py_library(
    name = "codegen_prompt_formatter",
    srcs = [
        "codegen_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        ":token_apportionment",
        "//base/prompt_format:util",
        "//base/tokenizers",
    ],
)

py_library(
    name = "conftest",
    srcs = [
        "conftest.py",
    ],
    deps = [
        ":prompt_format_completion",
        "//base/tokenizers",
        requirement("pytest"),
    ],
)

pytest_test(
    name = "codegen_prompt_formatter_test",
    srcs = ["codegen_prompt_formatter_test.py"],
    deps = [
        ":codegen_prompt_formatter",
        ":conftest",
    ],
)

py_library(
    name = "indiana_prompt_formatter",
    srcs = [
        "indiana_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        ":token_apportionment",
        "//base/prompt_format:util",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "indiana_prompt_formatter_test",
    srcs = ["indiana_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":indiana_prompt_formatter",
    ],
)

py_library(
    name = "starcoder_prompt_formatter",
    srcs = [
        "starcoder_prompt_formatter.py",
    ],
    deps = [
        ":overlap",
        ":prompt_formatter",
        ":token_apportionment",
        "//base/languages",
        "//base/prompt_format:util",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "starcoder_prompt_formatter_test",
    srcs = ["starcoder_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":starcoder_prompt_formatter",
    ],
)

py_library(
    name = "rogue_prompt_formatter",
    srcs = [
        "rogue_prompt_formatter.py",
    ],
    deps = [
        ":overlap",
        ":prompt_formatter",
        ":token_apportionment",
        "//base/prompt_format:util",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "rogue_prompt_formatter_test",
    srcs = ["rogue_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":rogue_prompt_formatter",
    ],
)

py_library(
    name = "rogue_sl_prompt_formatter",
    srcs = [
        "rogue_sl_prompt_formatter.py",
    ],
    deps = [
        ":overlap",
        ":prompt_formatter",
        ":stateless_caching",
        ":token_apportionment",
        "//base/prompt_format:util",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "rogue_sl_prompt_formatter_test",
    srcs = ["rogue_sl_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":rogue_sl_prompt_formatter",
        ":stateless_caching",
    ],
)

py_library(
    name = "ender_prompt_formatter",
    srcs = [
        "ender_prompt_formatter.py",
    ],
    deps = [
        ":overlap",
        ":prompt_formatter",
        ":stateful_caching",
        ":stateless_caching",
        ":token_apportionment",
        "//base/diff_utils",
        "//base/prompt_format:util",
        "//base/static_analysis:signature_utils",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "ender_prompt_formatter_test",
    srcs = ["ender_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":ender_prompt_formatter",
        ":stateless_caching",
    ],
)

py_library(
    name = "simple_elden_prompt_formatter",
    srcs = [
        "simple_elden_prompt_formatter.py",
    ],
    deps = [
        ":overlap",
        ":prompt_formatter",
        ":token_apportionment",
        "//base/prompt_format:util",
        "//base/static_analysis:signature_utils",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "simple_elden_prompt_formatter_test",
    srcs = ["simple_elden_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":simple_elden_prompt_formatter",
        ":stateless_caching",
    ],
)

py_library(
    name = "prompt_format_completion",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":codegen_prompt_formatter",
        ":ender_prompt_formatter",
        ":indiana_prompt_formatter",
        ":rogue_prompt_formatter",
        ":rogue_sl_prompt_formatter",
        ":simple_elden_prompt_formatter",
        ":starcoder_prompt_formatter",
    ],
)
