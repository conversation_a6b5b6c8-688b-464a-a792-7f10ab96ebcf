"""The prompt formatter for Rogue models with stateless caching."""

import logging
import typing
from collections import defaultdict
from dataclasses import dataclass
from typing import Optional

import dataclasses_json

from base.prompt_format.common import PromptChunk
from base.prompt_format.util import (
    concatenate_retrieved_chunks,
    head_n,
    prompt_chunks_by_origin,
    trailing_n,
    update_retrieval_budget,
)
from base.prompt_format_completion.overlap import filter_visible_chunks_by_content
from base.prompt_format_completion.prompt_formatter import (
    CompletionPromptFormatter,
    PromptFormatterOutput,
    PromptInput,
    TokenList,
)
from base.prompt_format_completion.stateless_caching import (
    StatelessCachingConfig,
    quantize_by_char_len,
    segment_prefix_stateless,
)
from base.prompt_format_completion.token_apportionment import (
    TokenApportionmentConfig,
    apportion_context_budget,
)
from base.tokenizers.tokenizer import (
    RagSpecialTokens,
    Tokenizer,
)

Component = typing.Literal[
    "preamble",
    "path",
    "prefix",
    "suffix",
    "retrieval",
    "nearby_suffix",
    "nearby_prefix",
]
ALL_COMPONENTS: tuple[Component, ...] = typing.get_args(Component)


@dataclass
class RogueSLPromptFormatterConfig(dataclasses_json.DataClassJsonMixin):
    """The configuration for the RogueSLPromptFormatter."""

    stateless_caching_config: StatelessCachingConfig
    """The stateless caching configuration."""

    component_order: tuple[str, ...] = (
        "preamble",
        "path",
        "prefix",
        "suffix",
        "retrieval",
    )
    """The order of components."""

    use_far_prefix_token: bool = False
    """Whether to use far prefix token id for far prefix and main prefix ids for the nearby prefix."""

    filter_visible_chunks_by_content: bool = True
    """Whether to filter chunks by content."""

    def __post_init__(self):
        # check the components; we cannot directly use the Component Literal type because
        # it is not compatible with the dataclass_json library.
        for component in self.component_order:
            if component not in ALL_COMPONENTS:
                raise ValueError(
                    f"Invalid component name {component}. Must be one of {ALL_COMPONENTS}"
                )

    def get_components(self) -> tuple[Component, ...]:
        return self.component_order  # type: ignore


class RogueSLPromptFormatter(CompletionPromptFormatter):
    """The prompter for Rogue models with stateless caching."""

    def __init__(
        self,
        apportionment_config: TokenApportionmentConfig,
        prompt_formatter_config: RogueSLPromptFormatterConfig,
        tokenizer: Tokenizer,
    ):
        self._apportionment_config = apportionment_config
        # Keeping around a dictionary version of this budget.
        self.per_retriever_max_tokens = apportionment_config.per_retriever_max_tokens

        self._stateless_caching_config = (
            prompt_formatter_config.stateless_caching_config
        )
        self.component_order: tuple[Component, ...] = (
            prompt_formatter_config.get_components()
        )
        self.tokenizer = tokenizer

        use_far_prefix_token = prompt_formatter_config.use_far_prefix_token
        if use_far_prefix_token and "nearby_prefix" not in self.component_order:
            raise ValueError(
                "Can't use far prefix token without nearby prefix in component order."
            )

        # Checking for concrete types here provides static type checking with
        # runtime protection.
        special_tokens = self.tokenizer.special_tokens
        if not isinstance(special_tokens, RagSpecialTokens):
            raise ValueError(
                f"Can't use the given tokenizer with RogueSLPromptFormatter: {type(self.tokenizer)}."
                f" Expected RagSpecialTokens, but got {type(special_tokens)}."
            )

        self.filter_visible_chunks_by_content = (
            prompt_formatter_config.filter_visible_chunks_by_content
        )

        self.ret_prefix = [special_tokens.retrieval_section]
        self.ret_start = [special_tokens.ret_start]
        self.ret_body = [special_tokens.ret_body]
        self.filename_tokens = [special_tokens.filename]
        self.newline = [special_tokens.newline]
        self.nearby_prefix = [
            (
                special_tokens.fim_prefix
                if use_far_prefix_token
                else special_tokens.nearby_prefix
            )
        ]
        self.fim_prefix = [
            (
                special_tokens.far_prefix
                if use_far_prefix_token
                else special_tokens.fim_prefix
            )
        ]
        self.fim_suffix = [special_tokens.fim_suffix]
        self.fim_middle = [special_tokens.fim_middle]

        self.preamble = list(special_tokens.begin_sequence)

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        r"""Format prompt for Rogue models.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            max_output_token_count: The maximal number of tokens that the current completion request should generate.

        Returns:
            A prompt of length at most self.seq_length - max_output_token_count, in tokens.

        Prompt structure (newlines are explicitly shown):
            == Path Component ==
            <filename>{path}\n
            == Prefix Component ==
            <fim_prefix>{prefix}
            == Suffix Component ==
            <fim_suffix>{suffix}
            == Retrieval Component ==
            <|retrieval_section|>
            (<|ret-start|><filename>{path}<|ret-body|>{content})*
            == Nearby Prefix Component ==
            <|nearby_prefix|>{nearby_prefix}
            == End of components ==
            <fix_middle>

        This is an example with retrieval:

            <filename>src/example.py
            <fim_prefix>def
            <fim_suffix>
            return aggregated_output
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <|nearby_prefix|> aggregate(a,b):
            <fim_middle>
        """

        component_tokens = defaultdict[Component, TokenList](TokenList)
        max_prompt_tokens = (
            self._apportionment_config.max_content_len - max_output_token_count
        )

        path_tokens = self.tokenizer.tokenize_safe(prompt_input.path)

        prefix_chars = prompt_input.prefix
        prefix_char_offset = prompt_input.prefix_begin

        suffix_chars = prompt_input.suffix

        # If necessary, truncate the prefix in characters
        quantize_char_len = self._stateless_caching_config.quantize_char_len
        if quantize_char_len > 0:
            prefix_chars = quantize_by_char_len(
                prefix_chars=prefix_chars,
                prefix_char_offset=prefix_char_offset,
                quantize_char_len=quantize_char_len,
            )

        raw_prefix_tokens = self.tokenizer.tokenize_safe(prefix_chars)
        raw_suffix_tokens = self.tokenizer.tokenize_safe(prompt_input.suffix)

        # - len(preamble) for the preamble tokens.
        # - 1 for the fim_prefix token.
        # - 1 for the retrieval section token.
        # - 1 for the fim_middle token.
        # - up to 2 for the nearby prefix/suffix tokens.
        # fim suffix and path newline token are separate inputs to apportionment
        num_preference_tokens = (
            int(len(self.preamble))
            + 3
            + int(self._stateless_caching_config.nearby_prefix_token_len > 0)
            + int(self._stateless_caching_config.nearby_suffix_token_len > 0)
        )

        nearby_prefix_token_len = self._stateless_caching_config.nearby_prefix_token_len
        nearby_prefix_token_overlap = (
            self._stateless_caching_config.nearby_prefix_token_overlap
        )

        token_counts = apportion_context_budget(
            max_content_length=max_prompt_tokens,
            input_fraction=self._apportionment_config.input_fraction,
            prefix_fraction=self._apportionment_config.prefix_fraction,
            max_path_tokens=self._apportionment_config.max_path_tokens,
            path_prefix=True,
            num_preference_tokens=num_preference_tokens,
            num_path_tokens=len(path_tokens),
            num_prefix_tokens=len(raw_prefix_tokens) + nearby_prefix_token_overlap,
            num_suffix_tokens=len(raw_suffix_tokens),
            num_path_sep_tokens=2,  # filename and newline
            num_suffix_sep_tokens=1,
        )

        # DeepSeekCoder wants a BOS token at the prompt start, for example
        component_tokens["preamble"] += self.preamble

        if prompt_input.path:
            component_tokens["path"] += self.filename_tokens
            component_tokens["path"] += trailing_n(path_tokens, token_counts.path_len)
            component_tokens["path"] += self.newline
        quantize_token_len = self._stateless_caching_config.quantize_token_len

        prefix_range, nearby_prefix_range = segment_prefix_stateless(
            raw_prefix_token_len=len(raw_prefix_tokens),
            max_prompt_prefix_tokens=token_counts.prefix_len,
            nearby_prefix_token_len=nearby_prefix_token_len,
            nearby_prefix_token_overlap=nearby_prefix_token_overlap,
            quantize_token_len=quantize_token_len,
        )

        if (nearby_prefix_token_len > 0) != ("nearby_prefix" in self.component_order):
            raise ValueError(
                "Nearby prefix should be in component order iff nearby prefix len > 0"
            )

        nearby_prefix_tokens = []
        if nearby_prefix_range is not None:
            nearby_prefix_tokens = raw_prefix_tokens[
                nearby_prefix_range.start : nearby_prefix_range.stop
            ]
            component_tokens["nearby_prefix"] = list(self.nearby_prefix)
            component_tokens["nearby_prefix"] += nearby_prefix_tokens

        component_tokens["prefix"] += self.fim_prefix
        prefix_tokens = raw_prefix_tokens[prefix_range.start : prefix_range.stop]
        component_tokens["prefix"] += prefix_tokens

        # always add fim_suffix token
        component_tokens["suffix"] += self.fim_suffix
        suffix_tokens = head_n(raw_suffix_tokens, token_counts.suffix_len)
        component_tokens["suffix"] += suffix_tokens

        # Retrieval section

        def tokenize_retrieval_chunk(doc: PromptChunk) -> TokenList:
            result = []
            result += self.ret_start
            result += self.filename_tokens
            result += trailing_n(
                self.tokenizer.tokenize_safe(doc.path),
                self._apportionment_config.max_path_tokens,
            )
            result += self.ret_body
            result += self.tokenizer.tokenize_safe(doc.text)
            return result

        retrieval_budget = token_counts.retrieval_len
        # Extra check to make sure we don't overrun the prompt length
        retrieval_budget = update_retrieval_budget(
            retrieval_budget=retrieval_budget,
            max_prompt_tokens=max_prompt_tokens,
            component_tokens=component_tokens,
        )

        # the <|retrieval_section|> token is there even if there is no retrieved chunk
        component_tokens["retrieval"] += self.ret_prefix

        ret_chunks = prompt_input.retrieved_chunks

        if self.filter_visible_chunks_by_content:
            if nearby_prefix_range is not None:
                total_prefix_tokens = raw_prefix_tokens[
                    prefix_range.start : nearby_prefix_range.stop
                ]
            else:
                total_prefix_tokens = raw_prefix_tokens[
                    prefix_range.start : prefix_range.stop
                ]
            prefix_len = len(self.tokenizer.detokenize(total_prefix_tokens))
            prefix_chars = prefix_chars[len(prefix_chars) - prefix_len :]

            suffix_len = len(self.tokenizer.detokenize(suffix_tokens))
            suffix_chars = suffix_chars[:suffix_len]

            # Filter chunks that are fully contained in the prompt range by content.
            ret_chunks = filter_visible_chunks_by_content(
                path=prompt_input.path,
                prompt_prefix=prefix_chars,
                prompt_suffix=suffix_chars,
                chunks=ret_chunks,
            )

        retrieved_chunks_dict = defaultdict(list, prompt_chunks_by_origin(ret_chunks))

        # recency chunks
        recency_retrieval_budget = min(
            self._apportionment_config.per_retriever_max_tokens.get(
                "recency_retriever", 0
            ),
            retrieval_budget,
        )

        recency_chunks = list(retrieved_chunks_dict["recency_retriever"])
        included_recency_chunk_indices = set()
        recency_retrieval_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(
                tokenize_retrieval_chunk(chunk) for chunk in recency_chunks
            ),
            separator_tokens=[],
            max_total_tokens=recency_retrieval_budget,
            used_indices_output=included_recency_chunk_indices,
        )
        if any(
            recency_chunks[idx].unique_id is None
            for idx in included_recency_chunk_indices
        ):
            logging.warning("Recency chunks are missing unique IDs: cannot deduplicate")
        recency_chunk_ids = {
            recency_chunks[idx].unique_id
            for idx in included_recency_chunk_indices
            if recency_chunks[idx].unique_id is not None
        }
        component_tokens["retrieval"] += recency_retrieval_tokens
        retrieval_budget = max(0, retrieval_budget - len(recency_retrieval_tokens))
        logging.info(
            "Used %d/%d recency tokens",
            len(recency_retrieval_tokens),
            recency_retrieval_budget,
        )

        # recency chunks - git diff
        recency_retrieval_git_diff_budget = min(
            self._apportionment_config.per_retriever_max_tokens.get(
                "recency_retriever_git_diff", 0
            ),
            retrieval_budget,
        )

        recency_git_diff_chunks = [
            chunk
            for chunk in retrieved_chunks_dict["recency_retriever_git_diff"]
            if chunk.unique_id not in recency_chunk_ids
        ]
        included_recency_git_diff_chunk_indices = set()
        recency_retrieval_git_diff_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(
                tokenize_retrieval_chunk(chunk) for chunk in recency_git_diff_chunks
            ),
            separator_tokens=[],
            max_total_tokens=recency_retrieval_git_diff_budget,
            used_indices_output=included_recency_git_diff_chunk_indices,
        )
        # We no longer warn if recency chunks are missing unique IDs, because
        # they currently use a different chunking mechanism and are not critical to
        # deduplicate
        recency_git_diff_chunk_ids = {
            recency_git_diff_chunks[idx].unique_id
            for idx in included_recency_git_diff_chunk_indices
            if recency_git_diff_chunks[idx].unique_id is not None
        }
        component_tokens["retrieval"] += recency_retrieval_git_diff_tokens
        retrieval_budget = max(
            0, retrieval_budget - len(recency_retrieval_git_diff_tokens)
        )
        logging.info(
            "Used %d/%d recency git diff tokens",
            len(recency_retrieval_git_diff_tokens),
            recency_retrieval_git_diff_budget,
        )

        # dense retrieval chunks: the remaining retrieval_budget will be used
        # for these, capped by the dense retriever's budget if specified
        if "dense_retriever" in self.per_retriever_max_tokens:
            retrieval_budget = min(
                retrieval_budget, self.per_retriever_max_tokens["dense_retriever"]
            )

        remaining_chunks: list[PromptChunk] = []
        for origin, chunks in retrieved_chunks_dict.items():
            # skip chunks for origins that were already processed
            if origin != "recency_retriever" and origin != "recency_retriever_git_diff":
                # skip chunks that were already included as recency chunks
                remaining_chunks.extend(
                    [
                        chunk
                        for chunk in chunks
                        if chunk.unique_id not in recency_chunk_ids
                        and chunk.unique_id not in recency_git_diff_chunk_ids
                    ]
                )

        retrieval_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=(
                tokenize_retrieval_chunk(chunk) for chunk in remaining_chunks
            ),
            separator_tokens=[],
            max_total_tokens=retrieval_budget,
        )
        component_tokens["retrieval"] += retrieval_tokens
        logging.info(
            "Used %d/%d dense tokens",
            len(retrieval_tokens),
            retrieval_budget,
        )

        tokens = []
        for component in self.component_order:
            tokens += component_tokens[component]

        # Always add the FIM middle token.
        tokens += self.fim_middle
        if len(tokens) > max_prompt_tokens:
            raise ValueError(
                f"Prompt too long: {len(tokens)} tokens, max {max_prompt_tokens}"
            )
        return PromptFormatterOutput([tokens])
