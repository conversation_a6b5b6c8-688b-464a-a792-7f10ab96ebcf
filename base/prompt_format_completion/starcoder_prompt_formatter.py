"""Different kinds of prompt formatter."""

import typing
from typing import Optional
from dataclasses import dataclass

import dataclasses_json

from base import languages
from base.prompt_format.util import concatenate_retrieved_chunks, head_n, trailing_n
from base.prompt_format_completion.prompt_formatter import (
    Prompt<PERSON>hunk,
    CompletionPromptFormatter,
    PromptFormatterOutput,
    PromptInput,
    TokenList,
)
from base.prompt_format_completion.overlap import filter_visible_chunks_by_content
from base.prompt_format_completion.token_apportionment import (
    TokenApportionmentConfig,
    apportion_context_budget,
)
from base.tokenizers import StarCoderSpecialTokens, Tokenizer


@dataclass
class StarCoderPromptFormatterConfig(dataclasses_json.DataClassJsonMixin):
    """The configuration for the StarCoderPromptFormatter."""

    filter_visible_chunks_by_content: bool = True


class StarCoderPromptFormatter(CompletionPromptFormatter):
    """The prompter for the StarCoder model."""

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        prompt_formatter_config: Optional[StarCoderPromptFormatterConfig] = None,
        language_guesser: Optional[languages.LanguageGuesser] = None,
    ):
        if not language_guesser:
            language_guesser = languages.default_language_guesser()
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=1.0,
                prefix_fraction=0.5,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.language_guesser = language_guesser

        special_tokens = typing.cast(
            StarCoderSpecialTokens, self.tokenizer.special_tokens
        )
        self.fim_prefix = [special_tokens.fim_prefix]
        self.fim_suffix = [special_tokens.fim_suffix]
        self.fim_middle = [special_tokens.fim_middle]

        self.retrieval_prefix_str = (
            "Here are some relevant code fragments from other files of the repo:"
        )
        self.retrieval_suffix_str = "Current source file:"
        self.sep_line_str = "-" * 50
        self.chunk_prefix_str = "The below code fragment can be found in:"

        self.filter_visible_chunks_by_content = (
            (prompt_formatter_config.filter_visible_chunks_by_content)
            if prompt_formatter_config
            else True
        )

    def format_prompt(
        self, prompt_input: PromptInput, max_output_token_count: int
    ) -> PromptFormatterOutput:
        """Format prompt for StarCoder.

        Retrieval not supported.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            max_output_token_count: The maximal number of tokens that the current completion request should generate.

        Returns:
            A prompt of length at most self.seq_length - max_output_token_count, in tokens.

        Example Prompt:
        <fim_prefix>prefix<fim_suffix>suffix<fix_middle>

        With retrieval, a prompt-based format is used. The chunks are
        prefixed by a a comment in the programming language used (or at least guessed).
        This is an example:

            <fim_prefix># Here are some relevant code fragments from other files of the repo:
            # --------------------------------------------------
            # The below code fragment can be found in:
            # src/foo.py
            # --------------------------------------------------
            # def aggregate(a,b):
            #     while--------------------------------------------------
            # Current source file:
            # src/example.py
            # --------------------------------------------------
            def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>
        """
        # Try hard to resolve the language of this file.
        lang = languages.check_language(
            prompt_input.lang or self.language_guesser.get_language(prompt_input.path)
        )
        comment_prefix = self.language_guesser.get_comment_prefix(lang)

        prefix_chars = prompt_input.prefix
        suffix_chars = prompt_input.suffix

        tokenized_prefix = self.tokenizer.tokenize_safe(prefix_chars)
        tokenized_suffix = self.tokenizer.tokenize_safe(suffix_chars)

        token_counts = apportion_context_budget(
            max_content_length=self.apportionment_config.max_content_len
            - max_output_token_count,
            input_fraction=self.apportionment_config.input_fraction,
            prefix_fraction=self.apportionment_config.prefix_fraction,
            max_path_tokens=self.apportionment_config.max_path_tokens,
            path_prefix=False,
            num_preference_tokens=0,
            num_path_tokens=0,
            num_prefix_tokens=len(tokenized_prefix),
            num_suffix_tokens=len(tokenized_suffix),
        )

        tokens: TokenList = []
        tokens += self.fim_prefix

        prefix_tokens = trailing_n(tokenized_prefix, token_counts.prefix_len)

        if not prompt_input.suffix or token_counts.suffix_len == 0:
            suffix_tokens = []

            context_tokens = (
                trailing_n(tokenized_prefix, token_counts.prefix_len)
                + self.fim_suffix
                + self.fim_middle
            )
        else:
            suffix_tokens = head_n(tokenized_suffix, token_counts.suffix_len)
            context_tokens = (
                trailing_n(tokenized_prefix, token_counts.prefix_len)
                + self.fim_suffix
                + head_n(tokenized_suffix, token_counts.suffix_len)
                + self.fim_middle
            )

        max_content_length = (
            self.apportionment_config.max_content_len - max_output_token_count
        )
        retrieval_len = max_content_length - len(tokens) - len(context_tokens)

        if comment_prefix is not None:

            def tokenize_chunk(doc: PromptChunk) -> TokenList:
                text = f"{self.chunk_prefix_str}\n{doc.path}\n{self.sep_line_str}\n{doc.text}{self.sep_line_str}"
                result = []
                for line in text.split("\n"):
                    result.append(comment_prefix)
                    result.append(line)
                    result.append("\n")
                result = "".join(result)
                return self.tokenizer.tokenize_safe(result)

            ret_chunks = prompt_input.retrieved_chunks

            if self.filter_visible_chunks_by_content:
                prefix_len = len(self.tokenizer.detokenize(prefix_tokens))
                prefix_chars = prefix_chars[len(prefix_chars) - prefix_len :]

                suffix_len = len(self.tokenizer.detokenize(suffix_tokens))
                suffix_chars = suffix_chars[:suffix_len]

                # Filter chunks that are fully contained in the prompt range by content.
                ret_chunks = filter_visible_chunks_by_content(
                    path=prompt_input.path,
                    prompt_prefix=prefix_chars,
                    prompt_suffix=suffix_chars,
                    chunks=ret_chunks,
                )

            retrieval_tokens = concatenate_retrieved_chunks(
                retrieved_chunks=map(tokenize_chunk, ret_chunks),  # type: ignore
                separator_tokens=[],
                max_total_tokens=retrieval_len,
            )

            if retrieval_tokens:
                retrieval_prefix_text = f"{comment_prefix}{self.retrieval_prefix_str}\n{comment_prefix}{self.sep_line_str}\n"
                tokens += self.tokenizer.tokenize_safe(retrieval_prefix_text)
                tokens += retrieval_tokens
                retrieval_suffix_text = f"{comment_prefix}{self.retrieval_suffix_str}\n{comment_prefix}{prompt_input.path}\n{comment_prefix}{self.sep_line_str}\n"
                tokens += self.tokenizer.tokenize_safe(retrieval_suffix_text)

        tokens += context_tokens
        return PromptFormatterOutput([tokens])
