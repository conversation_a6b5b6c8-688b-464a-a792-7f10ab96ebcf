"""Tests for the codegen prompt formatter."""

from base import tokenizers
from base.prompt_format_completion import PromptInput
from base.prompt_format_completion.codegen_prompt_formatter import (
    CodegenPromptFormatter,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig


def test_codegen_prompt_formatter_basic(example_input, example_chunks):
    """This is a simple sanity check to catch obvious bugs in CodeGen's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = CodegenPromptFormatter(config, tokenizer)

    example_input.retrieved_chunks = example_chunks
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    expected_prompt = "def aggregate(a,b):\n"
    assert prompt == expected_prompt


def test_codegen_prompt_formatter_length_only_prefix(example_input: PromptInput):
    """This test checks the generation when the available prompt input exceeds the available number of tokens so that only parts of the prefix fit."""
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=30, input_fraction=0.5, prefix_fraction=0.75, max_path_tokens=20
    )
    prompter = CodegenPromptFormatter(config, tokenizer)

    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=24
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)
    assert len(prompt_tokens) == 6
    expected_prompt = "(a,b):\n"
    assert prompt == expected_prompt


def test_codegen_prompt_formatter_empty_prefix_and_suffix():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = CodegenPromptFormatter(config, tokenizer)

    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )

    prompt_tokens = prompter.format_prompt(
        test_prompt_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    expected_prompt = ""
    assert prompt == expected_prompt
