import logging
from dataclasses import dataclass
from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class StatefulCachingConfig:
    """The stateful caching configuration

    Without stateful caching, prompt formatters will generally select retrieved
    chunks based on their rank in the input, and format them in rank order
    (increasing or decreasing). Stateful caching enables prompt formatters to
    record history of chunks included in past prompts, and use this history to
    select chunks for the current prompt.

    There are three levels of aggressiveness in stateful caching, where more aggression
    means larger deviations from the retrieval ranking of chunks for the sake of
    prompt consistency across requests.

    1. Chunks are selected from the current retrieved chunks only based upon rank,
    then re-ordered if that would extend the prefix shared with previous prompts.

    2. Some number of chunks may be selected from the current retrieved chunks
    without regard to their rank if they would extend the prefix which is shared
    with previous prompts.

    3. Some number of chunks may be selected from the cached state which are not
    in the current retrieval if they would extend the prefix which is shared
    with previous prompts. Such chunks must still be known to be within the
    user's workspace, which results in some rules. Here are some examples, but
    there may be more to consider as prompt formatters evolve:
        - chunks must be within Blobs for the current request
        - recency chunks not in the current retrieval may not be included
    """

    enabled: bool = False

    low_rank_chunk_fraction: float = 0.0
    """Maximal fraction of retrieval budget that can be used for chunks with low
    rank or which are not in the current retrieval."""

    un_retrieved_chunk_fraction: float = 0.0
    """Maximal fraction of retrieval budget that can be used for chunks which
    are not in the current retrieval."""

    def __post_init__(self):
        assert self.low_rank_chunk_fraction >= 0
        assert self.un_retrieved_chunk_fraction >= 0
        if self.un_retrieved_chunk_fraction > self.low_rank_chunk_fraction:
            logging.warning(
                "Un-retrieved cached chunk fraction cannot exceed low-rank cached chunk fraction. Clamping."
            )
            self.un_retrieved_chunk_fraction = self.low_rank_chunk_fraction
