"""Tests the behavior of the stateless caching logic."""

import pytest

from base.prompt_format_completion.stateless_caching import (
    quantize_by_char_len,
    segment_prefix_stateless,
    segment_suffix_stateless,
)
from base.ranges import TokenRange


@pytest.mark.parametrize(
    "prefix_char_offset, quantize_char_len, expected",
    [
        (0, 0, "abc"),  # no quantization
        (0, 2, "abc"),  # quantize but no offset should have no effect
        (3, 2, "bc"),  # quantize and offset should truncate
        (3, 10, ""),  # quantize and large offset should completely remove prefix
    ],
)
def test_quantize_by_char(prefix_char_offset, quantize_char_len, expected):
    quantized_chars = quantize_by_char_len(
        prefix_chars="abc",
        prefix_char_offset=prefix_char_offset,
        quantize_char_len=quantize_char_len,
    )
    assert quantized_chars == expected


@pytest.mark.parametrize(
    "raw_prefix_token_len, max_prompt_prefix_tokens, nearby_prefix_token_len, nearby_prefix_token_overlap, quantize_token_len, expected_prefix_range, expected_nearby_prefix_range",
    [
        (10, 100, 0, 0, 0, TokenRange(0, 10), None),  # no quant/nearby prefix
        (10, 5, 0, 0, 0, TokenRange(5, 10), None),  # limit prefix
        (10, 100, 5, 1, 0, TokenRange(0, 6), TokenRange(5, 10)),  # nearby prefix
        (
            2,
            100,
            5,
            1,
            0,
            TokenRange(0, 1),
            TokenRange(0, 2),
        ),  # nearby prefix small file
        (10, 5, 0, 0, 2, TokenRange(6, 10), None),  # quantize prefix
        (10, 100, 0, 0, 3, TokenRange(0, 10), None),  # quantize prefix no truncate
        (11, 9, 4, 1, 2, TokenRange(4, 9), TokenRange(8, 11)),  # nearby quantize prefix
        (
            11,
            100,
            100,
            1,
            2,
            TokenRange(0, 1),
            TokenRange(0, 11),
        ),  # nearby quantize prefix no truncate
    ],
)
def test_segment_prefix_stateless(
    raw_prefix_token_len,
    max_prompt_prefix_tokens,
    nearby_prefix_token_len,
    nearby_prefix_token_overlap,
    quantize_token_len,
    expected_prefix_range,
    expected_nearby_prefix_range,
):
    prefix_range, nearby_prefix_range = segment_prefix_stateless(
        raw_prefix_token_len=raw_prefix_token_len,
        max_prompt_prefix_tokens=max_prompt_prefix_tokens,
        nearby_prefix_token_len=nearby_prefix_token_len,
        nearby_prefix_token_overlap=nearby_prefix_token_overlap,
        quantize_token_len=quantize_token_len,
    )

    assert prefix_range == expected_prefix_range
    assert nearby_prefix_range == expected_nearby_prefix_range


@pytest.mark.parametrize(
    "raw_suffix_token_len, max_prompt_suffix_tokens, nearby_suffix_token_len, nearby_suffix_token_overlap, quantize_token_len, expected_suffix_range, expected_nearby_suffix_range",
    [
        (10, 100, 0, 0, 0, TokenRange(0, 10), None),  # no quant/nearby suffix
        (10, 5, 0, 0, 0, TokenRange(0, 5), None),  # limit suffix
        (10, 100, 5, 1, 0, TokenRange(4, 10), TokenRange(0, 5)),  # nearby suffix
        (
            2,
            100,
            5,
            1,
            0,
            TokenRange(1, 2),
            TokenRange(0, 2),
        ),  # nearby suffix small file
        (10, 5, 0, 0, 2, TokenRange(0, 4), None),  # quantize suffix
        (10, 100, 0, 0, 3, TokenRange(0, 10), None),  # quantize suffix no truncate
        (11, 9, 4, 1, 2, TokenRange(2, 7), TokenRange(0, 3)),  # nearby quantize suffix
        (
            11,
            100,
            100,
            1,
            2,
            TokenRange(10, 11),
            TokenRange(0, 11),
        ),  # nearby quantize suffix no truncate
    ],
)
def test_segment_suffix_stateless(
    raw_suffix_token_len,
    max_prompt_suffix_tokens,
    nearby_suffix_token_len,
    nearby_suffix_token_overlap,
    quantize_token_len,
    expected_suffix_range,
    expected_nearby_suffix_range,
):
    suffix_range, nearby_suffix_range = segment_suffix_stateless(
        raw_suffix_token_len=raw_suffix_token_len,
        max_prompt_suffix_tokens=max_prompt_suffix_tokens,
        nearby_suffix_token_len=nearby_suffix_token_len,
        nearby_suffix_token_overlap=nearby_suffix_token_overlap,
        quantize_token_len=quantize_token_len,
    )

    assert suffix_range == expected_suffix_range
    assert nearby_suffix_range == expected_nearby_suffix_range
