"""Module containing prompt formatting logic for different models."""

import logging
from typing import Any

from base.prompt_format_completion.codegen_prompt_formatter import (
    CodegenPromptFormatter,
)
from base.prompt_format_completion.ender_prompt_formatter import (
    Ender<PERSON>rom<PERSON><PERSON>ormatter,
    EnderPromptFormatterConfig,
)
from base.prompt_format_completion.indiana_prompt_formatter import (
    IndianaPromptFormatter,
)
from base.prompt_format_completion.prompt_formatter import (
    CompletionPromptFormatter,
    PromptCache,
    PromptChunk,
    PromptFormatterOutput,
    PromptInput,
    TokenList,
)
from base.prompt_format_completion.rogue_prompt_formatter import RoguePromptFormatter
from base.prompt_format_completion.rogue_sl_prompt_formatter import (
    RogueSLPromptFormatter,
    RogueSLPromptFormatterConfig,
)
from base.prompt_format_completion.simple_elden_prompt_formatter import (
    SimpleEldenPromptFormatter,
    SimpleEldenPromptFormatterConfig,
)
from base.prompt_format_completion.starcoder_prompt_formatter import (
    StarCoderPromptFormatter,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.tokenizers.tokenizer import Tokenizer


def get_completion_prompt_formatter_by_name(
    name: str,
    tokenizer: Tokenizer,
    apportionment_config: TokenApportionmentConfig | dict[str, Any] | None = None,
    prompt_formatter_config: dict[str, Any] | None = None,
) -> CompletionPromptFormatter:
    """Returns the prompt formatter by name.

    Args:
        name: name of the formatter
        tokenizer: instance of the tokenizer to use.
        apportionment_config: Hints for the apportionment of tokens during the prompt formatting. If not set, a default is used.
        prompt_formatter_config: optional additional configuration for the prompt formatter.

    If there is no formatter with the given name, an exception is thrown.
    """
    # TODO(): refactor to feed same config to all prompt formatters and allow instantiate by name
    if isinstance(apportionment_config, dict):
        apportionment_config = TokenApportionmentConfig.schema().load(
            apportionment_config
        )
    if name == "starcoder":
        return StarCoderPromptFormatter(apportionment_config, tokenizer)
    elif name == "rogue":
        return RoguePromptFormatter(apportionment_config, tokenizer)
    elif name == "rogue_sl":
        if apportionment_config is None:
            raise ValueError(
                "Can't create Rogue SL prompt formatter without apportionment config."
            )
        if prompt_formatter_config is None:
            raise ValueError(
                "Can't create Rogue SL prompt formatter without formatter config."
            )
        parsed_prompt_formatter_config = RogueSLPromptFormatterConfig.schema().load(
            prompt_formatter_config
        )
        return RogueSLPromptFormatter(
            apportionment_config,
            parsed_prompt_formatter_config,
            tokenizer,
        )
    elif name == "codegen":
        return CodegenPromptFormatter(apportionment_config, tokenizer)
    elif name == "indiana":
        return IndianaPromptFormatter(apportionment_config, tokenizer)
    elif name == "ender":
        if prompt_formatter_config is None:
            raise ValueError(
                "Can't create Ender prompt formatter without formatter config."
            )
        parsed_prompt_formatter_config = EnderPromptFormatterConfig.schema().load(
            prompt_formatter_config
        )
        if (
            apportionment_config is None
            and parsed_prompt_formatter_config.token_budget is None
        ):
            raise ValueError(
                "Can't create Ender prompt formatter without either apportionment_config or token_budget."
            )
        return EnderPromptFormatter(
            apportionment_config=apportionment_config,
            prompt_formatter_config=parsed_prompt_formatter_config,
            tokenizer=tokenizer,
        )
    elif name == "simple_elden":
        assert (
            apportionment_config is None
        ), "simple_elden does not need the apportionment config"
        if prompt_formatter_config is None:
            raise ValueError(
                "Can't create simple elden prompt formatter without formatter config."
            )
        parsed_prompt_formatter_config = SimpleEldenPromptFormatterConfig.schema().load(
            prompt_formatter_config
        )
        return SimpleEldenPromptFormatter(
            config=parsed_prompt_formatter_config,
            tokenizer=tokenizer,
        )
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError("Invalid prompt formatter name")
