"""Tests for the StarCoder prompt formatter."""

from typing import Iterable

from base import tokenizers
from base.prompt_format_completion import Prompt<PERSON>hunk, PromptInput
from base.prompt_format_completion.starcoder_prompt_formatter import (
    StarCoderPromptFormatter,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig


def test_starcoder_prompt_formatter_basic(example_input: PromptInput):
    """This is a simple sanity check to catch obvious bugs in Starcoder's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = StarCoderPromptFormatter(config, tokenizer)

    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = tokenizer.detokenize(prompt_tokens)

    expected_prompt = """<fim_prefix>def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt


def test_starcoder_prompt_formatter_empty_suffix(example_input: PromptInput):
    """Verifies the formatting with an empty suffix."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = StarCoderPromptFormatter(config, tokenizer)

    example_input.suffix = ""
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    expected_prompt = """<fim_prefix>def aggregate(a,b):
<fim_suffix><fim_middle>"""
    assert prompt == expected_prompt


def test_starcoder_prompt_formatter_empty_prefix_and_suffix():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = StarCoderPromptFormatter(config, tokenizer)

    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )

    prompt_tokens = prompter.format_prompt(
        test_prompt_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    expected_prompt = """<fim_prefix><fim_suffix><fim_middle>"""
    assert prompt == expected_prompt


def test_starcoder_prompt_formatter_retrieval(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """Tests the basic behavior with retrieved chunks."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = StarCoderPromptFormatter(config, tokenizer)

    example_input.retrieved_chunks = example_chunks
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = tokenizer.detokenize(prompt_tokens)

    expected_prompt = """<fim_prefix># Here are some relevant code fragments from other files of the repo:
# --------------------------------------------------
# The below code fragment can be found in:
# src/foo.py
# --------------------------------------------------
# # You can aggregate
# # with a pooling function.
# --------------------------------------------------
# The below code fragment can be found in:
# src/bar.py
# --------------------------------------------------
# # You can aggregate
# # with a maxing
# # function.
# --------------------------------------------------
# Current source file:
# src/example.py
# --------------------------------------------------
def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt


def test_starcoder_prompt_formatter_retrieval_no_newline(
    example_input: PromptInput, example_chunks_no_newline: Iterable[PromptChunk]
):
    """Tests the basic behavior with retrieved chunks where the chunks to not end on a newline."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = StarCoderPromptFormatter(config, tokenizer)

    example_input.retrieved_chunks = example_chunks_no_newline
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = tokenizer.detokenize(prompt_tokens)

    # the while and the separator line are joined together.
    # this replicates a behavior (I would say a bug) from the research prompt formatter.
    expected_prompt = """<fim_prefix># Here are some relevant code fragments from other files of the repo:
# --------------------------------------------------
# The below code fragment can be found in:
# src/foo.py
# --------------------------------------------------
# def aggregate(a,b):
#     while--------------------------------------------------
# Current source file:
# src/example.py
# --------------------------------------------------
def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt


def test_starcoder_prompt_formatter_retrieval_missing_language(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """Tests the  behavior with retrieved chunks when the programming language is not supported."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = StarCoderPromptFormatter(config, tokenizer)

    # A programming language we never hope to support.
    example_input.path = "src/foo.bf"
    example_input.retrieved_chunks = example_chunks
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = tokenizer.detokenize(prompt_tokens)
    print(f"'{prompt}'")

    expected_prompt = """<fim_prefix>def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt


def test_starcoder_prompt_formatter_retrieval_content_len_limit(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """Tests the basic behavior with retrieved chunks with a limited content length."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = StarCoderPromptFormatter(config, tokenizer)
    example_input.retrieved_chunks = example_chunks
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = tokenizer.detokenize(prompt_tokens)

    expected_prompt = """<fim_prefix># Here are some relevant code fragments from other files of the repo:
# --------------------------------------------------
# The below code fragment can be found in:
# src/foo.py
# --------------------------------------------------
# # You can aggregate
# # with a pooling function.
# --------------------------------------------------
# The below code fragment can be found in:
# src/bar.py
# --------------------------------------------------
# # You can aggregate
# # with a maxing
# # function.
# --------------------------------------------------
# Current source file:
# src/example.py
# --------------------------------------------------
def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt
