"""Prompt formatter for plain codegen models."""

from typing import Op<PERSON>

from base.prompt_format_completion.prompt_formatter import (
    CompletionPromptFormatter,
    PromptFormatterOutput,
    PromptInput,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.tokenizers.tokenizer import Tokenizer


class CodegenPromptFormatter(CompletionPromptFormatter):
    """The prompter for the CodeGen model."""

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=1.0,
                prefix_fraction=1.0,
                max_path_tokens=0,
            )

        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        """Create a simple prompt.

        The prompt does not include retrieved chunks or the suffix.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            max_output_token_count: The maximal number of tokens that the current completion request should generate.

        Returns:
            A prompt of length at most self.seq_length - max_generated_tokens, in tokens.

        Example Prompt:
        <local prefix>
        """
        assert max_output_token_count <= self.apportionment_config.max_content_len

        max_prompt_tokens = (
            self.apportionment_config.max_content_len - max_output_token_count
        )
        prefix_tokens = self.tokenizer.tokenize_safe(prompt_input.prefix)
        chunked_prefix_tokens = prefix_tokens[-max_prompt_tokens:]

        final_prompt_tokens = chunked_prefix_tokens
        return PromptFormatterOutput([final_prompt_tokens])
