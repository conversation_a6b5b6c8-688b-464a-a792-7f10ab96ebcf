"""Different kinds of prompt formatter."""

import typing
from typing import Optional
from typing_extensions import deprecated

from base.prompt_format.util import concatenate_retrieved_chunks, head_n, trailing_n
from base.prompt_format_completion.prompt_formatter import (
    PromptChunk,
    CompletionPromptFormatter,
    PromptFormatterOutput,
    PromptInput,
    TokenList,
)
from base.prompt_format_completion.token_apportionment import (
    TokenApportionment,
    TokenApportionmentConfig,
    apportion_context_budget,
)
from base.tokenizers import CodeGenSpecialTokens, Tokenizer


@deprecated("Use another prompt formatter.")
class IndianaPromptFormatter(CompletionPromptFormatter):
    """The prompter for the Indiana model."""

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=0.5,
                prefix_fraction=0.5,
                max_path_tokens=1000,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer

        self.separator_tokens: list[int] = self.tokenizer.tokenize_safe("\n")
        self.special_tokens = typing.cast(
            CodeGenSpecialTokens, tokenizer.special_tokens
        )
        self.separator_for_retrieved_chunks = [self.special_tokens.end_of_doc]
        self.separator_for_fim = [self.special_tokens.fim_sep]
        self.inference_preference_token_ids = [
            self.special_tokens.start_of_key,
            self.special_tokens.pref_repo_large,
            self.special_tokens.pref_repo_stars_high,
        ]
        self.separator_tokens: list[int] = self.tokenizer.tokenize_safe("\n")

    def _get_apportionment(
        self, prompt_input: PromptInput, max_output_token_count
    ) -> TokenApportionment:
        return apportion_context_budget(
            max_content_length=self.apportionment_config.max_content_len
            - max_output_token_count,
            input_fraction=self.apportionment_config.input_fraction,
            prefix_fraction=self.apportionment_config.prefix_fraction,
            path_prefix=True,
            max_path_tokens=self.apportionment_config.max_path_tokens,
            num_preference_tokens=len(self.inference_preference_token_ids),
            num_path_tokens=len(self.tokenizer.tokenize_safe(prompt_input.path)),
            num_prefix_tokens=len(self.tokenizer.tokenize_safe(prompt_input.prefix)),
            num_suffix_tokens=len(self.tokenizer.tokenize_safe(prompt_input.suffix)),
        )

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        """Create a simple prompt, with <|ret-endofdoc|>-separated chunks and optional FIM support.

        The prompt optionally includes retrieved chunks.
        Chunks are ordered from lowest to highest scoring, so that the
        most similar chunks appear closest to the original prompt (ie latest in the prompt).


        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            max_output_token_count: The maximal number of tokens that the current completion request should generate.

        Returns:
            A prompt of length at most self.seq_length - max_generated_tokens, in tokens.

        The prompt optionally includes retrieve chunks
        Chunks are ordered from lowest-scoring to highest-scoring, so that the
        most similar chunks appear closest to the original prompt.

        Example Prompt:
        <|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>
        foo.py
        def a(..)
            ...
        <|ret-endofdoc|>bar.py
        def b(..)
            ...
        <|ret-endofdoc|>car.py
        def c(..)
            ...
        <|ret-endofdoc|>current.py
        <prefix><|fim-sep|><suffix>
        """

        token_counts = self._get_apportionment(prompt_input, max_output_token_count)

        def tokenize_chunk(doc: PromptChunk) -> TokenList:
            doc_text = f"{doc.path}\n{doc.text}"
            doc_tokens = self.tokenizer.tokenize_safe(doc_text)
            doc_tokens.append(self.special_tokens.end_of_doc)
            return doc_tokens

        retrieval_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=map(tokenize_chunk, prompt_input.retrieved_chunks),
            separator_tokens=[],
            max_total_tokens=token_counts.retrieval_len,
        )

        tokens = (
            self.inference_preference_token_ids
            + retrieval_tokens
            + (
                trailing_n(
                    self.tokenizer.tokenize_safe(prompt_input.path),
                    token_counts.path_len,
                )
                if prompt_input.path
                else []
            )
            + self.separator_tokens
            + head_n(
                self.tokenizer.tokenize_safe(prompt_input.suffix),
                token_counts.suffix_len,
            )
            + (self.separator_for_fim if token_counts.suffix_len else [])
            + trailing_n(
                self.tokenizer.tokenize_safe(prompt_input.prefix),
                token_counts.prefix_len,
            )
        )
        return PromptFormatterOutput([tokens])
