"""Tests the behavior of the simple elden prompt formatter.

bazel test //base/prompt_format_completion:simple_elden_prompt_formatter_test
"""

import logging

import pytest

from base import tokenizers
from base.prompt_format.common import PromptChunk
from base.prompt_format_completion import PromptInput
from base.prompt_format_completion.simple_elden_prompt_formatter import (
    SimpleEldenPromptFormatter,
    SimpleEldenPromptFormatterConfig,
)
from base.prompt_format_completion.token_apportionment import TokenApportionment


def _get_prompt_formatter(
    config: SimpleEldenPromptFormatterConfig | None = None,
    tokenizer_name: str = "deepseek_coder_v2",
):
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    if config is None:
        config = SimpleEldenPromptFormatterConfig(
            max_prompt_length=2048,
            token_config=TokenApportionment(
                path_len=20,
                prefix_len=512,
                suffix_len=512,
                retrieval_len=128,
            ),
            per_retriever_max_tokens={
                "signature_retriever": 100,
                "dense_retriever": 100,
            },
        )
    prompter = SimpleEldenPromptFormatter(config, tokenizer)
    return prompter, tokenizer


def _format(
    prompt_input: PromptInput,
    config: SimpleEldenPromptFormatterConfig | None = None,
    max_output_token_count: int = 64,
    tokenizer_name: str = "deepseek_coder_v2",
):
    prompter, tokenizer = _get_prompt_formatter(
        config=config,
        tokenizer_name=tokenizer_name,
    )
    prompt_tokens = prompter.format_prompt(
        prompt_input, max_output_token_count=max_output_token_count
    ).tokens()
    prompt = tokenizer.detokenize(prompt_tokens)
    logging.info("Generated %d tokens for prompt", len(prompt_tokens))
    logging.info("Prompt: %s", prompt)
    return prompt


def test_format_prompt_basic():
    """This is a simple sanity check to catch obvious bugs in prompt formatting."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(example_input)

    expected_prompt = """<｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:

# Retrieved Chunks Finish.
# Signature Chunks Start:

# Signature Chunks Finish.
file path: src/example.py
def aggregate(a,b):
<｜fim▁hole｜>
return aggregated_output
<｜fim▁end｜>"""
    assert prompt == expected_prompt


def test_format_prompt_empty_suffix():
    """Verifies the formatting with an empty suffix."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(example_input)

    expected_prompt = """<｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:

# Retrieved Chunks Finish.
# Signature Chunks Start:

# Signature Chunks Finish.
file path: src/example.py
def aggregate(a,b):
<｜fim▁hole｜><｜fim▁end｜>"""
    assert prompt == expected_prompt


def test_format_prompt_empty_prefix_and_suffix():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(test_prompt_input)

    expected_prompt = """<｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:

# Retrieved Chunks Finish.
# Signature Chunks Start:

# Signature Chunks Finish.
file path: src/example.py
<｜fim▁hole｜><｜fim▁end｜>"""
    assert prompt == expected_prompt


def test_format_prompt_limited_path():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
        ),
    )
    prompt = _format(
        test_prompt_input,
        config=SimpleEldenPromptFormatterConfig(
            max_prompt_length=256,
            token_config=TokenApportionment(
                path_len=3,
                prefix_len=96,
                suffix_len=96,
                retrieval_len=48,
            ),
        ),
    )

    expected_prompt = """<｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:
file path: bar.py
# You can aggregate
# with a maxing
# function.


# Retrieved Chunks Finish.
# Signature Chunks Start:

# Signature Chunks Finish.
file path: example.py
<｜fim▁hole｜><｜fim▁end｜>"""
    assert prompt == expected_prompt


def test_format_prompt_with_recency_chunks_but_no_recency_budget():
    """Makes sure that recency chunks without a budget do not affect the outcome.

    Addresses the following situation: recency chunks that are also dense chunks
    should be included in the prompt (and not filtered out as duplicates) when
    the recency budget is 0 (and in general when they are not included as
    recent chunks).
    """
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="recency_retriever",
            ),
        ),
    )
    prompt = _format(
        test_prompt_input,
        config=SimpleEldenPromptFormatterConfig(
            max_prompt_length=128,
            token_config=TokenApportionment(
                path_len=3,
                prefix_len=96,
                suffix_len=96,
                retrieval_len=64,
            ),
            per_retriever_max_tokens={
                "recency_retriever": 0,
            },
        ),
    )

    expected_prompt = """<｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:
file path: bar.py
# You can aggregate
# with a maxing
# function.


# Retrieved Chunks Finish.
# Signature Chunks Start:

# Signature Chunks Finish.
file path: example.py
<｜fim▁hole｜><｜fim▁end｜>"""
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_recency():
    """Tests the basic behavior with retrieved chunks."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
                path="src/bar.py",
                unique_id="1",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# src/foo.py\nclass Foo:\n  def do_foo(self): pass",
                path="src/foo.py",
                unique_id="2",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="3",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
                unique_id="4",
                origin="",  # intentionally unspecified.
            ),
            PromptChunk(
                text="# A recent chunk",
                path="src/recent.py",
                unique_id="5",
                origin="recency_retriever",
            ),
            # This chunk was retrieved by both the recency and dense retrievers,
            # and it should only show up once
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="3",
                origin="recency_retriever",
            ),
        ),
    )
    prompt = _format(
        example_input,
        config=SimpleEldenPromptFormatterConfig(
            max_prompt_length=2048,
            token_config=TokenApportionment(
                path_len=20,
                prefix_len=512,
                suffix_len=512,
                retrieval_len=768,
            ),
            per_retriever_max_tokens={
                "signature_retriever": 100,
                "dense_retriever": 200,
                "recency_retriever": 100,
            },
        ),
    )
    expected_prompt = """<｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:
file path: src/bar.py
# You can aggregate
# with a maxing
# function.

file path: src/recent.py
# A recent chunk
file path: src/foo.py
# You can aggregate
# with a pooling function.

# Retrieved Chunks Finish.
# Signature Chunks Start:
# src/foo.py\nclass Foo:\n  def do_foo(self): pass

# src/bar.py\nclass Bar:\n  def do_bar(self): pass
# Signature Chunks Finish.
file path: src/example.py
def aggregate(a,b):
<｜fim▁hole｜>
return aggregated_output
<｜fim▁end｜>"""
    assert prompt == expected_prompt


@pytest.mark.parametrize("version", ["v1.0", "v2.0"])
def test_overlap_detection(version: str):
    """Tests overlap detection with retrieved chunks."""

    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="return aggregated_output\n",
        prefix_begin=3,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="1",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="class Example:\n  def aggregate(a,b):\n",
                path="src/example.py",
                unique_id="2",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/example.py",
                unique_id="3",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="aggregated_output = a+b\n",
                path="src/example.py",
                unique_id="4",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/code.py",
                unique_id="5",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="def new_aggregate(a,b):\n",
                path="src/example.py",
                unique_id="6",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="aggregate",
                path="src/example.py",
                unique_id="7",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return",
                path="src/example.py",
                unique_id="8",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="(a,b):\nreturn",
                path="src/example.py",
                unique_id="9",
                origin="dense_retriever",
            ),
        ),
    )

    config = SimpleEldenPromptFormatterConfig(
        max_prompt_length=192,
        token_config=TokenApportionment(
            path_len=5,
            prefix_len=64,
            suffix_len=64,
            retrieval_len=80,
        ),
        per_retriever_max_tokens={
            "signature_retriever": 25,
            "dense_retriever": 50,
            "recency_retriever": 50,
        },
        filter_visible_chunks_by_content=True,
        version=version,
    )

    prompt = _format(example_input, config)

    if version == "v1.0":
        expected_prompt = "".join(
            [
                "<｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:\n",
                "file path: src/example.py\n",
                "aggregated_output = a+b\n\n",
                "file path: src/code.py\n",
                "return aggregated_output\n\n",
                "file path: src/bar.py\n",
                "# You can aggregate\n# with a maxing\n# function.\n\n",
                "\n# Retrieved Chunks Finish.\n",
                "# Signature Chunks Start:\n",
                "\n# Signature Chunks Finish.\n",
                "file path: src/example.py\n",
                "def aggregate(a,b):\n"
                "<｜fim▁hole｜>return aggregated_output\n<｜fim▁end｜>",
            ]
        )
    else:
        expected_prompt = "".join(
            [
                "<｜begin▁of▁sentence｜># Retrieved Chunks Start:\n",
                "file path: src/example.py\n",
                "aggregated_output = a+b\n\n",
                "file path: src/code.py\n",
                "return aggregated_output\n\n",
                "file path: src/bar.py\n",
                "# You can aggregate\n# with a maxing\n# function.\n\n",
                "\n# Retrieved Chunks Finish.\n",
                "# Signature Chunks Start:\n",
                "\n# Signature Chunks Finish.\n",
                "<|filename|>file path: src/example.py\n",
                "<｜fim▁begin｜>def aggregate(a,b):\n"
                "<｜fim▁hole｜>return aggregated_output\n<｜fim▁end｜>",
            ]
        )
    assert prompt == expected_prompt
