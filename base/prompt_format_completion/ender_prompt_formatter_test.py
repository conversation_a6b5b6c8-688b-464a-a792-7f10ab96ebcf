"""Tests the behavior of the ender prompt formatter.

bazel test //base/prompt_format_completion:ender_prompt_formatter_test
"""

import logging
import re
from typing import Iterable, Optional

import pytest

from base import tokenizers
from base.prompt_format.common import PromptChunk
from base.prompt_format_completion import PromptCache, PromptInput
from base.prompt_format_completion.ender_prompt_formatter import (
    EnderPromptFormatter,
    EnderPromptFormatterConfig,
    TokenBudget,
)
from base.prompt_format_completion.stateful_caching import StatefulCachingConfig
from base.prompt_format_completion.stateless_caching import StatelessCachingConfig
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.tokenizers.tokenizer import Tokenizer


def _get_ender_prompt_formatter(
    apportionment_config: Optional[TokenApportionmentConfig] = None,
    prompt_formatter_config: Optional[EnderPromptFormatterConfig] = None,
    tokenizer_name: str = "starcoder",
    max_tokens_diff_retriever: int = 100,
):
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    if not apportionment_config:
        apportionment_config = TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "signature_retriever": 100,
                "dense_retriever": 100,
                "diff_retriever": max_tokens_diff_retriever,
            },
        )
    if not prompt_formatter_config:
        prompt_formatter_config = EnderPromptFormatterConfig()
    prompter = EnderPromptFormatter(
        apportionment_config, prompt_formatter_config, tokenizer
    )
    return prompter, tokenizer


def _format(
    prompt_input: PromptInput,
    prompt_cache: PromptCache = PromptCache(),
    apportionment_config: TokenApportionmentConfig | None = None,
    prompt_formatter_config: EnderPromptFormatterConfig | None = None,
    max_output_token_count: int = 64,
    tokenizer_name: str = "starcoder",
    max_tokens_diff_retriever: int = 100,
):
    prompter, tokenizer = _get_ender_prompt_formatter(
        apportionment_config=apportionment_config,
        prompt_formatter_config=prompt_formatter_config,
        tokenizer_name=tokenizer_name,
        max_tokens_diff_retriever=max_tokens_diff_retriever,
    )

    output, cache = prompter.format_prompt_with_cache(
        prompt_input,
        max_output_token_count=max_output_token_count,
        prompt_cache=prompt_cache,
        invalid_blobs=[],
    )
    prompt_tokens = output.tokens()
    prompt = tokenizer.detokenize(prompt_tokens)
    logging.info("Generated %d tokens for prompt", len(prompt_tokens))
    logging.info("Prompt: %s", prompt)
    return prompt


def test_parse_caching_config():
    parsed = EnderPromptFormatterConfig.schema().load({})
    assert parsed.stateless_caching_config == StatelessCachingConfig()
    assert parsed.stateful_caching_config == StatefulCachingConfig()

    parsed = EnderPromptFormatterConfig.schema().load(
        {
            "stateless_caching_config": {
                "nearby_prefix_token_len": 512,
            },
            "stateful_caching_config": {
                "enabled": True,
                "low_rank_chunk_fraction": 10.0,
            },
        }
    )
    assert parsed.stateless_caching_config.nearby_prefix_token_len == 512
    assert parsed.stateful_caching_config.enabled
    assert parsed.stateful_caching_config.low_rank_chunk_fraction == 10.0


def test_format_prompt_basic():
    """This is a simple sanity check to catch obvious bugs in prompt formatting."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(example_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            """<fim_suffix>
return aggregated_output\n""",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_basic_sc2():
    """This is a simple sanity check to catch obvious bugs in prompt formatting with StarCoder2 tokenizer."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(example_input, tokenizer_name="starcoder2")

    expected_prompt = "".join(
        [
            "<file_sep>src/example.py\n",
            "<pr_base>",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            """<fim_suffix>
return aggregated_output\n""",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_basic_diff_component():
    """Test that we include the diff section if the tokenizer supports it and the component order has diff."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        component_order=("path", "prefix", "retrieval", "signature", "diff", "suffix")
    )
    prompt = _format(
        example_input,
        prompt_formatter_config=ender_prompt_formatter_config,
        tokenizer_name="qwen25coder",
    )
    expected_prompt = "".join(
        [
            "<|file_sep|>src/example.py\n",
            "<|fim_prefix|>def aggregate(a,b):\n",
            "<|retrieval_section|>",
            "<|sig_begin|>\n",
            "\n<|sig_end|>",
            "<|diff_section|>\n",
            "\n",
            """<|fim_suffix|>
return aggregated_output\n""",
            "<|fim_middle|>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_basic_no_diff_component():
    """Test that we do not include the diff section if the tokenizer supports it and the component order does not have diff."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        component_order=("path", "prefix", "retrieval", "signature", "suffix")
    )
    prompt = _format(
        example_input,
        prompt_formatter_config=ender_prompt_formatter_config,
        tokenizer_name="qwen25coder",
    )
    expected_prompt = "".join(
        [
            "<|file_sep|>src/example.py\n",
            "<|fim_prefix|>def aggregate(a,b):\n",
            "<|retrieval_section|>",
            "<|sig_begin|>\n",
            "\n<|sig_end|>",
            """<|fim_suffix|>
return aggregated_output\n""",
            "<|fim_middle|>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_empty_suffix():
    """Verifies the formatting with an empty suffix."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(example_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_empty_prefix_and_suffix():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(test_prompt_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>",
            "<fim_suffix>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_limited_path():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
        ),
    )
    prompt = _format(
        test_prompt_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=128,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=3,
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>",
            "<fim_suffix>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_with_diff_chunks_with_token_budget():
    """Test that we include the diff section and that we correctly cut off older chunks if we are over budget.
    Additionally checks to make sure the path header is not duplicated."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# Most recent chunk\n",
                path="+++ b/src/example.py\n",
                origin="diff_retriever",
            ),
            PromptChunk(
                text="# Second recent chunk\n",
                path="+++ b/src/example.py\n",
                origin="diff_retriever",
            ),
            PromptChunk(
                text="# Third recent chunk\n",
                path="+++ b/src/example_2.py\n",
                origin="diff_retriever",
            ),
            PromptChunk(
                text="# Oldest chunk. This is a very long chunk that should be cut off due to budget.\n",
                path="+++ b/src/example_2.py\n",
                origin="diff_retriever",
            ),
        ),
    )
    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        component_order=("path", "prefix", "retrieval", "signature", "diff", "suffix")
    )
    prompt = _format(
        example_input,
        prompt_formatter_config=ender_prompt_formatter_config,
        tokenizer_name="qwen25coder",
        max_tokens_diff_retriever=40,
    )
    expected_prompt = "".join(
        [
            "<|file_sep|>src/example.py\n",
            "<|fim_prefix|>def aggregate(a,b):\n",
            "<|retrieval_section|>",
            "<|sig_begin|>\n",
            "\n<|sig_end|>",
            "<|diff_section|>\n",
            "+++ b/src/example_2.py\n",
            "# Third recent chunk\n",
            "+++ b/src/example.py\n",
            "# Second recent chunk\n",
            "# Most recent chunk\n",
            "\n",
            """<|fim_suffix|>
return aggregated_output\n""",
            "<|fim_middle|>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_diff_and_limit():
    """Tests the behavior with diff chunks and a limited budget.

    Diff chunks have a higher priority than other chunks and may use up the budget.
    """

    retrieved_chunks = [
        PromptChunk(
            text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            path="src/bar.py",
            origin="signature_retriever",
        ),
        PromptChunk(
            text="# src/foo.py\nclass Foo:\n  def do_foo(self): pass",
            path="src/foo.py",
            origin="signature_retriever",
        ),
        PromptChunk(
            text="# Short text\n",
            path="src/bar.py",
            origin="dense_retriever",
        ),
        PromptChunk(
            text="# A recent chunk with some long text",
            path="src/recent.py",
            origin="recency_retriever",
        ),
    ]
    diff_chunks = [
        PromptChunk(
            text="# A recent diff chunk that is quite long and should result in other chunks being dropped.\n",
            path="+++ b/src/example.py\n",
            origin="diff_retriever",
        )
    ]

    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        component_order=("path", "prefix", "retrieval", "signature", "diff", "suffix")
    )

    def format_helper(chunks):
        example_input = PromptInput(
            path="src/example.py",
            prefix="def aggregate(a,b):\n",
            suffix="\nreturn aggregated_output\n",
            prefix_begin=0,
            retrieved_chunks=(*chunks,),
        )

        prompt = _format(
            example_input,
            apportionment_config=TokenApportionmentConfig(
                max_content_len=128,
                input_fraction=0.7,
                prefix_fraction=0.75,
                max_path_tokens=20,
                per_retriever_max_tokens={
                    "signature_retriever": 20,
                    "dense_retriever": 10,
                    "recency_retriever": 5,
                    "diff_retriever": 40,
                },
            ),
            prompt_formatter_config=ender_prompt_formatter_config,
            tokenizer_name="qwen25coder",
        )

        return prompt

    expected_prompt_without_diff_chunk = "".join(
        [
            "<|file_sep|>src/example.py\n",
            "<|fim_prefix|>def aggregate(a,b):\n" "<|retrieval_section|>",
            "<|ret_start|><|file_sep|>src/bar.py<|ret_body|># Short text\n",
            "<|sig_begin|>\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig_end|>",
            "<|diff_section|>\n",
            "\n",
            """<|fim_suffix|>
return aggregated_output\n""",
            "<|fim_middle|>",
        ]
    )
    assert format_helper(retrieved_chunks) == expected_prompt_without_diff_chunk

    # The prompt should include the diff chunk but remove the signature chunk due to budget constraints.
    expected_prompt_with_diff_chunk = "".join(
        [
            "<|file_sep|>src/example.py\n",
            "<|fim_prefix|>def aggregate(a,b):\n" "<|retrieval_section|>",
            "<|ret_start|><|file_sep|>src/bar.py<|ret_body|># Short text\n",
            "<|sig_begin|>\n",
            "\n<|sig_end|>",
            "<|diff_section|>\n",
            "+++ b/src/example.py\n",
            "# A recent diff chunk that is quite long and should result in other chunks being dropped.\n",
            "\n",
            """<|fim_suffix|>
return aggregated_output\n""",
            "<|fim_middle|>",
        ]
    )
    assert (
        format_helper(retrieved_chunks + diff_chunks) == expected_prompt_with_diff_chunk
    )


def test_format_prompt_with_viewed_content_chunks():
    """Test that we include viewed content chunks in the retrieval section."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# Recently viewed content\n",
                path="src/viewed.py",
                origin="recency_retriever_viewed_content",
            ),
            PromptChunk(
                text="# Another viewed chunk\n",
                path="src/viewed2.py",
                origin="recency_retriever_viewed_content",
            ),
        ),
    )
    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "recency_retriever_viewed_content": 100,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/viewed2.py",
            "<|ret-body|># Another viewed chunk\n",
            "<|ret-start|><filename>src/viewed.py",
            "<|ret-body|># Recently viewed content\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_with_viewed_content_and_token_budget():
    """Test that viewed content chunks respect token budget limits."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# Short viewed content\n",
                path="src/viewed1.py",
                origin="recency_retriever_viewed_content",
            ),
            PromptChunk(
                text="# This is a very long viewed content chunk that should be cut off due to budget constraints and not appear in the final prompt.\n",
                path="src/viewed2.py",
                origin="recency_retriever_viewed_content",
            ),
        ),
    )
    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=128,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "recency_retriever_viewed_content": 15,  # Limited budget
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/viewed1.py",
            "<|ret-body|># Short viewed content\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_with_viewed_content_and_other_retrievers():
    """Test that viewed content chunks work alongside other retrievers with budget constraints."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
                path="src/bar.py",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# Dense retrieval chunk\n",
                path="src/dense.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# Recent chunk\n",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# Viewed content chunk\n",
                path="src/viewed.py",
                origin="recency_retriever_viewed_content",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "signature_retriever": 100,
                "dense_retriever": 100,
                "recency_retriever": 100,
                "recency_retriever_viewed_content": 100,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            # Recent chunks go first (reversed order)
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># Recent chunk\n",
            # Viewed content chunks come next (reversed order)
            "<|ret-start|><filename>src/viewed.py",
            "<|ret-body|># Viewed content chunk\n",
            # Other chunks come last (reversed order)
            "<|ret-start|><filename>src/dense.py",
            "<|ret-body|># Dense retrieval chunk\n",
            "<|sig-begin|>\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_exact_budget():
    """Tests the behavior with diff chunks disabled and an exact budget (making sure we use all the budget)"""

    retrieved_chunks = iter(
        [
            # Needs 15 tokens.
            PromptChunk(
                text="# src/bar2.py\nclass Bar2: x = 22",
                path="src/bar2.py",
                origin="signature_retriever",
            ),
            # Needs 10 tokens.
            PromptChunk(
                text="# Short text\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            # Needs 12 tokens.
            PromptChunk(
                text="# A recent chunk2\n",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            # Should not appear in the prompt as we are over budget
            PromptChunk(
                text="# A recent chunk with some long text",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            # Should not appear in the prompt as diff component is not present
            PromptChunk(
                text="# A recent diff chunk.\n",
                path="+++ b/src/example.py\n",
                origin="diff_retriever",
            ),
        ]
    )

    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        component_order=("path", "prefix", "retrieval", "signature", "suffix")
    )

    example_input = PromptInput(
        path="src/example.py",  # 3 tokens
        prefix="def aggregate(a,b):\n",  # 5 tokens
        suffix="\nreturn aggregated_output\n",  # 5 tokens
        prefix_begin=0,
        retrieved_chunks=retrieved_chunks,
    )

    # We have exactly 37 tokens for retrieval: 60 (max content length) - 16 (5 prefix, 5 suffix, 3 path, 2 path sep, 1 suffix sep) - 7 (num_preference_tokens) = 37 (for retrieval)
    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=60,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=3,
            per_retriever_max_tokens={
                "signature_retriever": 20,  # Used 15/20 tokens
                "dense_retriever": 10,  # Used 10/10 tokens
                "recency_retriever": 20,  # Used 12/20 tokens
                "diff_retriever": 10,  # Used 0/10 tokens as diff component is not present
            },
        ),
        prompt_formatter_config=ender_prompt_formatter_config,
        max_output_token_count=0,
        tokenizer_name="qwen25coder",
    )

    expected_prompt = "".join(
        [
            "<|file_sep|>src/example.py\n",  # 5 tokens
            "<|fim_prefix|>def aggregate(a,b):\n",  # 6 tokens
            "<|retrieval_section|>",  # 1 token
            "<|ret_start|><|file_sep|>/recent.py<|ret_body|># A recent chunk2\n",  # 12 tokens
            "<|ret_start|><|file_sep|>src/bar.py<|ret_body|># Short text\n",  # 10 tokens
            "<|sig_begin|>\n",  # 2 tokens
            "# src/bar2.py\nclass Bar2: x = 22",  # 15 tokens
            "\n<|sig_end|>",  # 2 tokens
            "<|fim_suffix|>\nreturn aggregated_output\n",  # 6 tokens
            "<|fim_middle|>",  # 1 token
        ]
    )
    tokenizer = tokenizers.create_tokenizer_by_name("qwen25coder")
    num_tokens_in_prompt = len(tokenizer.tokenize_unsafe(expected_prompt))
    assert prompt == expected_prompt
    assert num_tokens_in_prompt == 60


def test_format_prompt_with_recency_chunks_but_no_recency_budget():
    """Makes sure that recency chunks without a budget do not affect the outcome.

    Addresses the following situation: recency chunks that are also dense chunks
    should be included in the prompt (and not filtered out as duplicates) when
    the recency budget is 0 (and in general when they are not included as
    recent chunks).
    """
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="recency_retriever",
            ),
        ),
    )
    prompt = _format(
        test_prompt_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=128,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=3,
            per_retriever_max_tokens={
                "recency_retriever": 0,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>",
            "<fim_suffix>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_with_viewed_content_chunks_but_no_budget():
    """Makes sure that viewed content chunks without a budget are not included in the prompt."""
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="recency_retriever_viewed_content",
            ),
        ),
    )
    prompt = _format(
        test_prompt_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=256,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=3,
            per_retriever_max_tokens={
                "recency_retriever": 100,
                "signature_retriever": 100,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>",
            "<fim_suffix>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_recency():
    """Tests the basic behavior with retrieved chunks."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
                path="src/bar.py",
                unique_id="1",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# src/foo.py\nclass Foo:\n  def do_foo(self): pass",
                path="src/foo.py",
                unique_id="2",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="3",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
                unique_id="4",
                origin="",  # intentionally unspecified.
            ),
            PromptChunk(
                text="# A recent chunk",
                path="src/recent.py",
                unique_id="5",
                origin="recency_retriever",
            ),
            # This chunk was retrieved by both the recency and dense retrievers,
            # and it should only show up once
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="3",
                origin="recency_retriever",
            ),
        ),
    )
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
        per_retriever_max_tokens={
            "signature_retriever": 100,
            "dense_retriever": 200,
            "recency_retriever": 100,
        },
    )
    prompt = _format(example_input, apportionment_config=config)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            # NOTE(guy): recent chunks go first
            # This is the duplicate chunk: it should only show up once
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk",
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<|sig-begin|>\n",
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "# src/foo.py\nclass Foo:\n  def do_foo(self): pass\n\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval():
    """Tests the basic behavior with retrieved chunks."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
                path="src/bar.py",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# src/foo.py\nclass Foo:\n  def do_foo(self): pass",
                path="src/foo.py",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
                origin="",  # intentionally unspecified.
            ),
            # Should not appear in the prompt
            PromptChunk(
                text="# A recent chunk",
                path="src/recent.py",
                origin="recency_retriever",
            ),
        ),
    )
    prompt = _format(example_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "# src/foo.py\nclass Foo:\n  def do_foo(self): pass\n\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_limit():
    """Tests the basic behavior with retrieved chunks."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
                path="src/bar.py",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# src/foo.py\nclass Foo:\n  def do_foo(self): pass",
                path="src/foo.py",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
                origin="",  # intentionally unspecified.
            ),
            PromptChunk(
                text="# A recent chunk",
                path="src/recent.py",
                origin="recency_retriever",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=128,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "signature_retriever": 20,
                "dense_retriever": 10,
                "recency_retriever": 2,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|sig-begin|>\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=148,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "signature_retriever": 20,
                "dense_retriever": 25,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_token_budget():
    """Tests the basic behavior with retrieved chunks."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
                path="src/bar.py",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# src/foo.py\nclass Foo:\n  def do_foo(self): pass",
                path="src/foo.py",
                origin="signature_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
                origin="",  # intentionally unspecified.
            ),
            PromptChunk(
                text="# A recent chunk",
                path="src/recent.py",
                origin="recency_retriever",
            ),
        ),
    )

    tokenizer = tokenizers.create_tokenizer_by_name("starcoder2")

    prompt_formatter = EnderPromptFormatter(
        None,
        EnderPromptFormatterConfig(
            token_budget=TokenBudget(
                max_prompt_length=64,
                path_len=20,
                prefix_len=34,
                suffix_len=10,
                retrieval_len=100,
                per_retriever_max_tokens={
                    "signature_retriever": 20,
                    "dense_retriever": 10,
                    "recency_retriever": 2,
                },
            )
        ),
        tokenizer=tokenizer,
    )
    prompt_output = prompt_formatter.format_prompt(
        example_input, max_output_token_count=64
    )
    prompt = tokenizer.detokenize(prompt_output.tokens())

    expected_prompt = "".join(
        [
            "<file_sep>src/example.py\n",
            "<pr_base>",
            "<|sig-begin|>\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt

    prompt_formatter = EnderPromptFormatter(
        None,
        EnderPromptFormatterConfig(
            token_budget=TokenBudget(
                max_prompt_length=84,
                path_len=20,
                prefix_len=44,
                suffix_len=14,
                retrieval_len=100,
                per_retriever_max_tokens={
                    "signature_retriever": 20,
                    "dense_retriever": 25,
                },
            )
        ),
        tokenizer=tokenizer,
    )
    prompt_output = prompt_formatter.format_prompt(
        example_input, max_output_token_count=64
    )
    prompt = tokenizer.detokenize(prompt_output.tokens())

    expected_prompt = "".join(
        [
            "<file_sep>src/example.py\n",
            "<pr_base>",
            "<pr_file><file_sep>src/bar.py",
            "<pr_base_code># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            "# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_limit_and_extra_budget():
    """Tests the basic behavior with retrieved chunks."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            # Needs 19 tokens.
            PromptChunk(
                text="# src/bar.py\nclass Bar:\n  def do_bar(self): pass",
                path="src/bar.py",
                origin="signature_retriever",
            ),
            # Needs 21 tokens.
            PromptChunk(
                text="# src/foo.py\nclass Foo:\n  def do_foo(self): pass",
                path="src/foo.py",
                origin="signature_retriever",
            ),
            # Needs 12 tokens.
            PromptChunk(
                text="# Short text\n",
                path="src/bar.py",
                origin="dense_retriever",
            ),
            # Should not appear in the prompt
            PromptChunk(
                text="# A recent chunk with some long text",
                path="src/recent.py",
                origin="recency_retriever",
            ),
        ),
    )

    prompt_without_extra = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=128,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "signature_retriever": 20,
                "dense_retriever": 10,
                "recency_retriever": 5,
            },
        ),
    )

    prompt_with_extra = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=128,
            input_fraction=0.7,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "signature_retriever": 20,
                "dense_retriever": 10,
                "recency_retriever": 5,
            },
            extra_generation_budget=10,
        ),
    )

    assert len(prompt_without_extra) > len(prompt_with_extra)


def test_prompt_formatter_retrieval_with_recency():
    """Tests retrieval with recency."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
            ),
            PromptChunk(
                text="# A recent chunk.",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# Another recent chunk.",
                path="src/recent2.py",
                origin="recency_retriever",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "recency_retriever": 200,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/recent2.py",
            "<|ret-body|># Another recent chunk.",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk.",
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_prompt_formatter_retrieval_with_recency_and_limit():
    """Tests retrieval with recency and limited budgets."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# A funny hunk.",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# A really long chunk\n" * 2000,
                path="src/foo.py",
            ),
            PromptChunk(
                text="# A recent chunk.",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# Another recent chunk.",
                path="src/recent2.py",
                origin="recency_retriever",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                # just enough to include one chunk but not the other
                "recency_retriever": 15,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># A funny hunk.",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_prompt_formatter_retrieval_with_recency_and_limit2():
    """Tests the basic behavior with retrieved chunks."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
            ),
            PromptChunk(
                text="# A recent chunk.",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# Another recent chunk.",
                path="src/recent2.py",
                origin="recency_retriever",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                # just enough to include one chunk but not the other
                "recency_retriever": 15,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk.",
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


class RetrievalKit:
    def __init__(self):
        self.next_id = 0

    def make_unique_chunks(self, count: int, origin: str = ""):
        self.next_id += count
        return list(
            PromptChunk(
                text=f"# Chunk {i}\n",
                path=f"src/chunk{i}.py",
                blob_name=f"blob{i}",
                origin=origin,
            )
            for i in range(self.next_id - count, self.next_id)
        )

    @staticmethod
    def chunk_paths_from_prompt(prompt: str):
        pat = re.compile(r"<\|ret-start\|><filename>(.*?)<\|ret-body\|>")
        return [m.group(1) for m in re.finditer(pat, prompt)]


def test_stateful_caching_no_cache():
    """Perhaps not necessary to assert this ordering change, but the order of
    chunks does differ when enabling caching, even with no cache. So here's
    a demonstration."""

    ret_kit = RetrievalKit()
    dense_chunks = ret_kit.make_unique_chunks(5, "dense_retriever")
    recency_chunks = ret_kit.make_unique_chunks(5, "recency_retriever")

    input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=dense_chunks + recency_chunks,
    )

    apportionment = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.1,
        prefix_fraction=0.75,
        max_path_tokens=20,
        per_retriever_max_tokens={
            "recency_retriever": 300,
        },
    )

    prompt_no_caching = _format(
        input,
        apportionment_config=apportionment,
        prompt_formatter_config=EnderPromptFormatterConfig(),
    )

    prompt_with_caching = _format(
        input,
        apportionment_config=apportionment,
        prompt_formatter_config=EnderPromptFormatterConfig(
            stateful_caching_config=StatefulCachingConfig(enabled=True)
        ),
    )

    expected = [c.path for c in dense_chunks + recency_chunks]
    assert ret_kit.chunk_paths_from_prompt(prompt_with_caching) == expected
    assert ret_kit.chunk_paths_from_prompt(prompt_no_caching) == list(
        reversed(expected)
    )


def test_stateful_caching_sanity():
    ret_kit = RetrievalKit()
    dense_chunks = ret_kit.make_unique_chunks(5, "dense_retriever")
    recency_chunks = ret_kit.make_unique_chunks(5, "recency_retriever")

    input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=dense_chunks + recency_chunks,
    )

    apportionment = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.1,
        prefix_fraction=0.75,
        max_path_tokens=20,
        per_retriever_max_tokens={
            "recency_retriever": 300,
        },
    )

    prompt_no_cache = _format(
        input,
        apportionment_config=apportionment,
        prompt_formatter_config=EnderPromptFormatterConfig(
            stateful_caching_config=StatefulCachingConfig(enabled=True)
        ),
    )

    prompt_with_cache = _format(
        input,
        prompt_cache=PromptCache(
            path=input.path,
            prompt_ordered_chunks=[dense_chunks[-1], recency_chunks[-1]],
        ),
        apportionment_config=apportionment,
        prompt_formatter_config=EnderPromptFormatterConfig(
            stateful_caching_config=StatefulCachingConfig(enabled=True)
        ),
    )

    expected = [c.path for c in dense_chunks + recency_chunks]
    assert ret_kit.chunk_paths_from_prompt(prompt_no_cache) == expected
    expected = [
        c.path
        for c in (
            dense_chunks[-1:]
            + recency_chunks[-1:]
            + dense_chunks[:-1]
            + recency_chunks[:-1]
        )
    ]
    assert ret_kit.chunk_paths_from_prompt(prompt_with_cache) == expected

    # Changing the path results in ignoring the cached chunks if path is before retrieval
    # in the component order
    prompt_with_cache_path_change = _format(
        input,
        prompt_cache=PromptCache(
            path=input.path + "/suffix",
            prompt_ordered_chunks=[dense_chunks[-1], recency_chunks[-1]],
        ),
        apportionment_config=apportionment,
        prompt_formatter_config=EnderPromptFormatterConfig(
            stateful_caching_config=StatefulCachingConfig(enabled=True)
        ),
    )
    assert prompt_no_cache == prompt_with_cache_path_change


def test_format_nearby_prefix():
    """Test of nearby prefix logic."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=4,
        nearby_prefix_token_overlap=1,
    )

    component_order = (
        "path",
        "prefix",
        "suffix",
        "signature",
        "retrieval",
        "nearby_prefix",
    )

    prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
    )

    prompt = _format(
        example_input,
        prompt_formatter_config=prompt_formatter_config,
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|far_prefix|>def aggregate(a,",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<|retrieval_section|>",
            "<fim_prefix>,b):\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_nearby_component_order_present_assert():
    """Test for assert nearby prefix in component order if nonzero."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=4,
    )

    # We should have nearby_prefix in component order
    component_order = ("path", "prefix", "suffix", "signature", "retrieval")

    prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
    )

    with pytest.raises(ValueError):
        _ = _format(
            example_input,
            prompt_formatter_config=prompt_formatter_config,
        )


def test_nearby_nonzero_assert():
    """Test for assert nearby prefix nonzero if in component order."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    # This config should have nearby_prefix nonzero
    stateless_config = StatelessCachingConfig()

    component_order = ("path", "prefix", "suffix", "retrieval", "nearby_prefix")

    prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
    )

    with pytest.raises(ValueError):
        _ = _format(
            example_input,
            prompt_formatter_config=prompt_formatter_config,
        )


def test_prompt_formatter_token_quant():
    """Test that quantization logic works."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=5,
        nearby_prefix_token_overlap=1,
        quantize_token_len=5,
    )

    component_order = (
        "path",
        "prefix",
        "suffix",
        "signature",
        "retrieval",
        "nearby_prefix",
    )

    prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
    )

    prompt = _format(
        example_input,
        prompt_formatter_config=prompt_formatter_config,
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|far_prefix|>def aggregate(a,b",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<|retrieval_section|>",
            "<fim_prefix>b):\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_char_quant():
    """Test of outer character quantization logic."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=4,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=5,
        nearby_prefix_token_overlap=1,
        quantize_token_len=5,
        quantize_char_len=7,
    )
    component_order = (
        "path",
        "prefix",
        "suffix",
        "signature",
        "retrieval",
        "nearby_prefix",
    )
    prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
    )

    prompt = _format(
        example_input,
        prompt_formatter_config=prompt_formatter_config,
    )

    # We expect the character quantizaton to truncate at a multiple of 7 characters from
    # the beginning of the 'true' prefix. We set prefix_begin to 4, so effectively we
    # expect it to remove 3 characters from our actual prefix.
    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|far_prefix|> aggregate(a,b):",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|sig-begin|>\n",
            "\n<|sig-end|>",
            "<|retrieval_section|>",
            "<fim_prefix>):\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def overlap_from_start(prompt1, prompt2):
    for idx, char in enumerate(prompt1):
        if char != prompt2[idx]:
            return idx
    return len(prompt1)


def test_typing_caching():
    quantize_token_len = 3
    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=6,
        nearby_prefix_token_overlap=1,
        quantize_token_len=quantize_token_len,
    )
    component_order = (
        "path",
        "prefix",
        "suffix",
        "signature",
        "retrieval",
        "nearby_prefix",
    )
    prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
    )

    starting_prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    model_input = PromptInput(
        path="src/example.py",
        prefix=starting_prefix,
        suffix=suffix,
        prefix_begin=4,
        retrieved_chunks=(),
    )

    num_comparisons = 9
    num_hits = 0

    last_prompt = _format(
        model_input,
        prompt_formatter_config=prompt_formatter_config,
    )

    single_token_chars = "showMessageDialog"

    for idx in range(num_comparisons):
        # Mimic typing in token space by repeatedly appending chars that combine to
        # single token
        model_input.prefix = starting_prefix + (idx + 1) * single_token_chars
        prompt = _format(
            model_input,
            prompt_formatter_config=prompt_formatter_config,
        )
        overlap = overlap_from_start(prompt, last_prompt)
        if overlap / len(last_prompt) > 0.9:
            num_hits += 1
        last_prompt = prompt

    # We expect to get a cache miss every context_quant_token_len tokens
    expected_hit_rato = (quantize_token_len - 1) / quantize_token_len
    actual_hit_ratio = num_hits / num_comparisons

    assert actual_hit_ratio == expected_hit_rato


def test_overlap_detection():
    """Tests overlap detection with retrieved chunks."""

    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="return aggregated_output\n",
        prefix_begin=3,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="1",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="class Example:\n  def aggregate(a,b):\n",
                path="src/example.py",
                unique_id="2",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/example.py",
                unique_id="3",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="aggregated_output = a+b\n",
                path="src/example.py",
                unique_id="4",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/code.py",
                unique_id="5",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="def new_aggregate(a,b):\n",
                path="src/example.py",
                unique_id="6",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="aggregate",
                path="src/example.py",
                unique_id="7",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return",
                path="src/example.py",
                unique_id="8",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="(a,b):\nreturn",
                path="src/example.py",
                unique_id="9",
                origin="dense_retriever",
            ),
        ),
    )
    config = TokenApportionmentConfig(
        max_content_len=192,
        input_fraction=0.15,
        prefix_fraction=0.7,
        max_path_tokens=5,
        per_retriever_max_tokens={
            "signature_retriever": 25,
            "dense_retriever": 50,
            "recency_retriever": 50,
        },
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=11,
        nearby_prefix_token_overlap=1,
        quantize_token_len=11,
        quantize_char_len=7,
    )

    component_order = (
        "path",
        "prefix",
        "suffix",
        "signature",
        "retrieval",
        "nearby_prefix",
    )
    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
        filter_visible_chunks_by_content=True,
    )

    prompt = _format(
        example_input,
        apportionment_config=config,
        prompt_formatter_config=ender_prompt_formatter_config,
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|far_prefix|>aggregate<fim_suffix>return aggregated_<|sig-begin|>\n\n",
            "<|sig-end|><|retrieval_section|>",
            # NOTE(guy): recent chunks go first
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "<|ret-start|><filename>src/example.py",
            "<|ret-body|>aggregated_output = a+b\n",
            "<|ret-start|><filename>src/code.py",
            "<|ret-body|>return aggregated_output\n",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_prefix>aggregate(a,b):\n",
            "<fim_middle>",
        ]
    )

    assert prompt == expected_prompt


def test_min_prefix_suffix_len():
    """Tests overlap detection with retrieved chunks."""

    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="return aggregated_output\n",
        prefix_begin=3,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="1",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="class Example:\n  def aggregate(a,b):\n",
                path="src/example.py",
                unique_id="2",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/example.py",
                unique_id="3",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="aggregated_output = a+b\n",
                path="src/example.py",
                unique_id="4",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/code.py",
                unique_id="5",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="def new_aggregate(a,b):\n",
                path="src/example.py",
                unique_id="6",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="aggregate",
                path="src/example.py",
                unique_id="7",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return",
                path="src/example.py",
                unique_id="8",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="(a,b):\nreturn",
                path="src/example.py",
                unique_id="9",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="aggregate(a,b):\nre",
                path="src/example.py",
                unique_id="10",
                origin="recency_retriever",
            ),
        ),
    )
    config = TokenApportionmentConfig(
        max_content_len=192,
        input_fraction=0.15,
        prefix_fraction=0.7,
        max_path_tokens=5,
        per_retriever_max_tokens={
            "signature_retriever": 25,
            "dense_retriever": 50,
            "recency_retriever": 50,
        },
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=11,
        nearby_prefix_token_overlap=1,
        quantize_token_len=11,
        quantize_char_len=7,
    )

    component_order = (
        "path",
        "prefix",
        "suffix",
        "signature",
        "retrieval",
        "nearby_prefix",
    )
    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=stateless_config,
        component_order=component_order,
        filter_visible_chunks_by_content=True,
        min_prefix_suffix_len=22,
    )

    prompt = _format(
        example_input,
        apportionment_config=config,
        prompt_formatter_config=ender_prompt_formatter_config,
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|far_prefix|>aggregate<fim_suffix>return aggregated_<|sig-begin|>\n\n",
            "<|sig-end|><|retrieval_section|>",
            # NOTE(guy): recent chunks go first
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "<|ret-start|><filename>src/example.py",
            "<|ret-body|>aggregated_output = a+b\n",
            "<|ret-start|><filename>src/example.py",
            "<|ret-body|>return aggregated_output\n",
            "<|ret-start|><filename>src/example.py",
            "<|ret-body|>class Example:\n  def aggregate(a,b):\n",
            "<|ret-start|><filename>src/code.py",
            "<|ret-body|>return aggregated_output\n",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_prefix>aggregate(a,b):\n",
            "<fim_middle>",
        ]
    )

    assert prompt == expected_prompt
