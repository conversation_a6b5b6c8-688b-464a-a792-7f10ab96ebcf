"""Unit test for the overlap module."""

import copy
import dataclasses
import uuid
from functools import partial

import pytest

from base.prompt_format.chunk_origin import Chunk<PERSON><PERSON><PERSON>, ChunkOriginValues
from base.prompt_format.common import PromptChunk
from base.prompt_format.recency_info import RecencyInfo, ReplacementText
from base.prompt_format_completion.overlap import (
    filter_visible_chunks_by_content,
    find_first_overlap,
    modified_chunks_filter,
    partial_overlap_predicate,
)


def test_filter_visible_chunks_by_content():
    """Tests when there is content overlap by content."""

    CHUNK_CONTENT = "Hallo Erde"
    PATH = "foo.py"
    chunk = PromptChunk(
        text=CHUNK_CONTENT,
        path=PATH,
        unique_id="1",
        origin="",
        char_start=20,
        char_end=20 + len(CHUNK_CONTENT),
        blob_name="",
        header="",
    )

    def run_filter_visible_chunks(
        current_file: str, prompt_prefix: str, prompt_suffix: str, chunk
    ):
        return list(
            filter_visible_chunks_by_content(
                path=current_file,
                prompt_prefix=prompt_prefix,
                prompt_suffix=prompt_suffix,
                chunks=[chunk],
            )
        )

    # Content overlap
    filter_by_content = partial(
        run_filter_visible_chunks, current_file=PATH, chunk=chunk
    )

    chunk_part_1, chunk_part_2 = (
        CHUNK_CONTENT[: len(CHUNK_CONTENT) // 2],
        CHUNK_CONTENT[len(CHUNK_CONTENT) // 2 :],
    )
    random_str = str(uuid.uuid4())

    assert (
        filter_by_content(
            prompt_prefix="Some content before " + chunk_part_1,
            prompt_suffix=chunk_part_2 + " Some content after",
        )
        == []
    ), "Chunk is in prompt filters"
    assert (
        filter_by_content(
            prompt_prefix="Some content before " + CHUNK_CONTENT, prompt_suffix=""
        )
        == []
    ), "Chunk is contained in prefix and suffix is empty"
    assert (
        filter_by_content(
            prompt_prefix="", prompt_suffix="Some content before " + CHUNK_CONTENT
        )
        == []
    ), "Chunk is contained in suffix and prefix is empty"
    assert (
        filter_by_content(prompt_prefix=chunk_part_1, prompt_suffix=random_str) == []
    ), "prefix in chunk filters"
    assert (
        filter_by_content(prompt_prefix=random_str, prompt_suffix=chunk_part_1) == []
    ), "suffix in chunk filters"

    assert filter_by_content(prompt_prefix=random_str, prompt_suffix=random_str) == [
        chunk
    ], "no content overlap does not filter"
    assert filter_by_content(prompt_prefix="", prompt_suffix=random_str) == [
        chunk
    ], "empty prefix is not filtered"
    assert filter_by_content(prompt_prefix=random_str, prompt_suffix="") == [
        chunk
    ], "empty suffix is not filtered"

    empty_chunk = dataclasses.replace(copy.deepcopy(chunk), text="")
    assert filter_by_content(
        prompt_prefix=random_str, prompt_suffix=random_str, chunk=empty_chunk
    ) == [empty_chunk], "empty chunk text is not filtered"

    # Different file
    assert filter_by_content(
        current_file="not_" + PATH, prompt_prefix="", prompt_suffix=""
    ) == [chunk], "different file will not filter"

    # Empty path
    assert filter_by_content(
        current_file="", prompt_prefix=chunk_part_1, prompt_suffix=chunk_part_2
    ) == [chunk], "empty path will not filter"


class ChunkFactory:
    def __init__(self, blob_name: str):
        self.blob_name = blob_name
        self.counter = 0

    def to_chunk(
        self,
        char_start,
        char_end,
        origin: str = ChunkOrigin.DENSE_RETRIEVER.value,
    ) -> PromptChunk:
        self.counter += 1
        return PromptChunk(
            text="%s-%s" % (self.blob_name, self.counter),
            path=self.blob_name,
            char_start=char_start,
            char_end=char_end,
            blob_name=self.blob_name,
            origin=origin,
        )

    def to_replacement_text(self, char_start, char_end) -> ReplacementText:
        self.counter += 1
        return ReplacementText(
            char_start=char_start,
            char_end=char_end,
            blob_name=self.blob_name,
            path=self.blob_name,
            replacement_text="%s-%s" % (self.blob_name, self.counter),
            present_in_blob=False,
        )


chunk_factory = ChunkFactory("blob1")


@pytest.mark.parametrize(
    "chunk1, chunk2, result, test_case",
    [
        (
            chunk_factory.to_chunk(0, 10),
            chunk_factory.to_chunk(10, 20),
            False,
            "adjacent",
        ),
        (
            chunk_factory.to_chunk(0, 10),
            chunk_factory.to_chunk(9, 20),
            True,
            "right",
        ),
        (
            chunk_factory.to_chunk(10, 20),
            chunk_factory.to_chunk(0, 11),
            True,
            "left",
        ),
        (
            chunk_factory.to_chunk(10, 20),
            chunk_factory.to_chunk(10, 20),
            True,
            "perfect_overlap",
        ),
        (
            chunk_factory.to_chunk(10, 20),
            chunk_factory.to_chunk(11, 19),
            True,
            "contains",
        ),
        (
            chunk_factory.to_chunk(11, 19),
            chunk_factory.to_chunk(10, 20),
            True,
            "contained",
        ),
        (
            chunk_factory.to_chunk(10, 20),
            ChunkFactory("blob2").to_chunk(10, 20),
            False,
            "different_blob",
        ),
    ],
)
def test_partial_overlap_predicate(chunk1, chunk2, result, test_case):
    assert partial_overlap_predicate(chunk1, chunk2) == result, test_case


def test_find_first_overlap():
    overlap = chunk_factory.to_chunk(9, 20)
    assert (
        find_first_overlap(
            chunk_factory.to_chunk(0, 10),
            [
                chunk_factory.to_chunk(10, 20),
                chunk_factory.to_chunk(10, 20),
                overlap,
            ],
            partial_overlap_predicate,
        )
        == overlap
    )

    assert (
        find_first_overlap(
            chunk_factory.to_chunk(0, 10),
            [
                chunk_factory.to_chunk(10, 20),
                chunk_factory.to_chunk(10, 20),
                chunk_factory.to_chunk(10, 20),
            ],
            partial_overlap_predicate,
        )
        is None
    )


def run_modified_chunks_filter(
    chunks_to_filter: list[PromptChunk],
    chunks_to_keep: list[ReplacementText],
):
    return list(
        modified_chunks_filter(
            chunks_to_filter,
            RecencyInfo(
                tab_switch_events=[], git_diff_info=[], recent_changes=chunks_to_keep
            ),
            ChunkOriginValues,
            [ChunkOrigin.RECENCY_RETRIEVER.value],
        )
    )


def test_filter_overlapping_sanity():
    blob1_chunk_factory = ChunkFactory("blob1")
    blob2_chunk_factory = ChunkFactory("blob2")
    blob3_chunk_factory = ChunkFactory("blob2")
    blob1_chunk_0_10 = blob1_chunk_factory.to_chunk(0, 10)
    blob2_chunk_5_15 = blob2_chunk_factory.to_chunk(5, 15)
    blob3_chunk_12_20 = blob3_chunk_factory.to_chunk(12, 20)

    # sanity 2
    assert run_modified_chunks_filter(
        [
            blob1_chunk_0_10,
            blob2_chunk_5_15,
            blob3_chunk_12_20,
        ],
        [
            blob1_chunk_factory.to_replacement_text(8, 18),
            blob2_chunk_factory.to_replacement_text(20, 25),
        ],
    ) == [
        blob2_chunk_5_15,
        blob3_chunk_12_20,
    ]


def test_modified_chunks_filter_no_overlap():
    """Tests when there is overlap."""
    blob1_chunk_factory = ChunkFactory("blob1")

    # sanity 1
    chunk1 = blob1_chunk_factory.to_chunk(0, 10)
    chunk2 = blob1_chunk_factory.to_replacement_text(11, 20)
    assert run_modified_chunks_filter(
        [chunk1],
        [chunk2],
    ) == [chunk1]


def test_modified_chunks_filter_complete_overlap():
    """Tests when there is overlap."""
    blob1_chunk_factory = ChunkFactory("blob1")

    # sanity 1
    chunk1 = blob1_chunk_factory.to_chunk(0, 10)
    chunk2 = blob1_chunk_factory.to_replacement_text(0, 10)
    assert (
        run_modified_chunks_filter(
            [chunk1],
            [chunk2],
        )
        == []
    )


def test_modified_chunks_filter_all_overlap():
    blob1_chunk_factory = ChunkFactory("blob1")
    # all overlap cases
    assert (
        run_modified_chunks_filter(
            [
                blob1_chunk_factory.to_chunk(10, 20),  # complete overlap
                blob1_chunk_factory.to_chunk(30, 40),  # to the left half overlap
                blob1_chunk_factory.to_chunk(50, 60),  # to the right half overlap
            ],
            [
                blob1_chunk_factory.to_replacement_text(10, 20),  # complete overlap
                blob1_chunk_factory.to_replacement_text(
                    35, 45
                ),  # to the left half overlap
                blob1_chunk_factory.to_replacement_text(
                    45, 55
                ),  # to the right half overlap
            ],
        )
        == []
    )


def test_modified_chunks_filter_all_non_overlap():
    blob1_chunk_factory = ChunkFactory("blob1")
    # all not overlapping cases
    no_overlap_chunk = blob1_chunk_factory.to_chunk(10, 20)
    assert run_modified_chunks_filter(
        [
            no_overlap_chunk,
        ],
        [
            blob1_chunk_factory.to_replacement_text(0, 10),  # just on the right
            blob1_chunk_factory.to_replacement_text(20, 30),  # just on the right
        ],
    ) == [no_overlap_chunk]


def test_modified_chunks_filter_unknown_origin():
    blob1_chunk_factory = ChunkFactory("blob1")
    # all not overlapping cases
    no_overlap_chunk = blob1_chunk_factory.to_chunk(10, 20, origin="unknown")
    assert run_modified_chunks_filter(
        [
            no_overlap_chunk,
        ],
        [
            blob1_chunk_factory.to_replacement_text(10, 20),  # complete overlap
        ],
    ) == [no_overlap_chunk]


def test_modified_chunks_filter_recency_origin():
    blob1_chunk_factory = ChunkFactory("blob1")
    # all not overlapping cases
    no_overlap_chunk = blob1_chunk_factory.to_chunk(
        10, 20, origin=ChunkOrigin.RECENCY_RETRIEVER.value
    )
    assert run_modified_chunks_filter(
        [
            no_overlap_chunk,
        ],
        [
            blob1_chunk_factory.to_replacement_text(10, 20),  # complete overlap
        ],
    ) == [no_overlap_chunk]
