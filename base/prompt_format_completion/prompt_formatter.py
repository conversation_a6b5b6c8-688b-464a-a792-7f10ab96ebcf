"""Classes that are used to build the prompts for the code completion model."""

from dataclasses import dataclass, field
from typing import Iterable, Optional, Protocol, Sequence, Tuple

from dataclasses_json import dataclass_json

from base.prompt_format.common import (
    PromptChunk,
    PromptFormatterOutput,
)

TokenList = list[int]


@dataclass
class PromptInput:
    """The set of inputs used for constructing the code completion model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.
    """

    prefix: str
    """The content before the cursor, when we do completion or the complete document for embeddings."""

    suffix: str
    """The content after the cursor."""

    prefix_begin: int
    """The character position of the start of the prefix in the original file."""

    path: str
    """The file path."""

    retrieved_chunks: Iterable[PromptChunk]
    """The retrieved chunks, sorted from high to low relevancy."""

    lang: Optional[str] = None
    """The optional hint about the programming language of the prefix and suffix."""


@dataclass_json()
@dataclass(frozen=True)
class PromptCache:
    """Representation of information included in a formatted prompt

    Pass back into future prompt formatting calls to enable the formatter to construct
    prompts which share context prefix with past prompts.

    Notes:
    A formatter should always produce a valid prompt for the model per the
    PromptInput with or without a PromptCache.

    It is generally assumed that a PromptCache will be passed back into the
    same formatter that produced it, or to a formatter of the same type
    configured in the same manner (e.g. token apportionment). Attempts to replay
    completion with PromptCache to a different formatter may or may not produce
    a prompt consistent with the originally observed prompt.
    """

    path: str = ""
    prompt_ordered_chunks: Sequence[PromptChunk] = field(default_factory=list)


class CompletionPromptFormatter(Protocol):
    """The Prompt Formatter protocol."""

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        """Build tokenized prompt.

        Args:
            prompt_input: PromptInput object describing input (prefix, suffix, path)
            max_output_token_count: The maximal number of output tokens that should be generated.

        Returns:
            prompt as list of tokens
        """
        raise NotImplementedError()

    def supports_caching(self) -> bool:
        """Whether the formatter supports caching.

        Essentially, this is whether the format_prompt_with_cache method will
        meaningfully utilize the PromptCache argument and return value.
        """
        return False

    def format_prompt_with_cache(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
        prompt_cache: PromptCache,
        invalid_blobs: Sequence[bytes],
    ) -> Tuple[PromptFormatterOutput, PromptCache]:
        """Build tokenized prompt given input and cache.

        Args:
            prompt_input: PromptInput object describing input (prefix, suffix, path)
            max_output_token_count: The maximal number of output tokens that should be generated.
            prompt_cache: PromptCache object produced by a previous call to format_prompt_with_cache.
            invalid_blobs: List of blobs which were valid when the PromptCache was produced, but which are no longer valid.

        Returns:
            prompt as list of tokens
            An instance of PromptCache to be passed back into the formatter for future prompts.
        """
        return self.format_prompt(prompt_input, max_output_token_count), PromptCache()
