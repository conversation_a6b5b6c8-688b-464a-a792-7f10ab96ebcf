"""The prompt formatter for Rogue models."""

import typing
from typing import Op<PERSON>
from dataclasses import dataclass

import dataclasses_json

from base.prompt_format.util import concatenate_retrieved_chunks, head_n, trailing_n
from base.prompt_format_completion.prompt_formatter import (
    PromptChunk,
    CompletionPromptFormatter,
    PromptFormatterOutput,
    PromptInput,
    TokenList,
)
from base.prompt_format_completion.overlap import filter_visible_chunks_by_content
from base.prompt_format_completion.token_apportionment import (
    TokenApportionmentConfig,
    apportion_context_budget,
)
from base.tokenizers import RogueSpecialTokens, Tokenizer


@dataclass
class RoguePromptFormatterConfig(dataclasses_json.DataClassJsonMixin):
    """The configuration for the RoguePromptFormatter."""

    filter_visible_chunks_by_content: bool = True


class RoguePromptFormatter(CompletionPromptFormatter):
    """The prompter for Rogue models."""

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        prompt_formatter_config: Optional[RoguePromptFormatterConfig] = None,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1000 * 1000,
                input_fraction=1.0,
                prefix_fraction=0.5,
                max_path_tokens=25,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer

        special_tokens = typing.cast(RogueSpecialTokens, self.tokenizer.special_tokens)
        self.ret_prefix = [special_tokens.retrieval_section]
        self.ret_start = [special_tokens.ret_start]
        self.ret_body = [special_tokens.ret_body]
        self.prefix_body = [special_tokens.prefix_body]
        self.filename_tokens = [special_tokens.filename]
        self.fim_prefix = [special_tokens.fim_prefix]
        self.fim_suffix = [special_tokens.fim_suffix]
        self.fim_middle = [special_tokens.fim_middle]

        self.filter_visible_chunks_by_content = (
            (prompt_formatter_config.filter_visible_chunks_by_content)
            if prompt_formatter_config
            else True
        )

    def format_prompt(
        self,
        prompt_input: PromptInput,
        max_output_token_count: int,
    ) -> PromptFormatterOutput:
        """Format prompt for Rogue models.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            max_output_token_count: The maximal number of tokens that the current completion request should generate.

        Returns:
            A prompt of length at most self.seq_length - max_output_token_count, in tokens.

        Example Prompt:
        <|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|>prefix<fim_suffix>suffix<fix_middle>

        This is an example with retrieval:

            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>

        """

        prefix_chars = prompt_input.prefix
        suffix_chars = prompt_input.suffix

        tokenized_path = self.tokenizer.tokenize_safe(prompt_input.path)
        tokenized_prefix = self.tokenizer.tokenize_safe(prefix_chars)
        tokenized_suffix = self.tokenizer.tokenize_safe(suffix_chars)

        token_counts = apportion_context_budget(
            max_content_length=self.apportionment_config.max_content_len
            - max_output_token_count,
            input_fraction=self.apportionment_config.input_fraction,
            prefix_fraction=self.apportionment_config.prefix_fraction,
            max_path_tokens=self.apportionment_config.max_path_tokens,
            path_prefix=True,
            # This number is 4 because:
            # - 1 for the retrieval section token.
            # - 1 for the fim_prefix token.
            # - 1 for the prefix_body token.
            # - 1 for the fim_middle token.
            num_preference_tokens=4,  # not really preference, but static overhead tokens
            num_path_tokens=len(tokenized_path),
            num_prefix_tokens=len(tokenized_prefix),
            num_suffix_tokens=len(tokenized_suffix),
        )

        tokens: TokenList = []
        # the <|retrieval_section|> token is there even if there is no retrieved chunk
        tokens += self.ret_prefix

        context_tokens = []
        context_tokens += self.fim_prefix
        if prompt_input.path:
            context_tokens += self.filename_tokens
            context_tokens += trailing_n(tokenized_path, token_counts.path_len)
        context_tokens += self.prefix_body

        prefix_tokens = trailing_n(tokenized_prefix, token_counts.prefix_len)
        context_tokens += prefix_tokens

        context_tokens += self.fim_suffix  # always add fim_suffix token
        if prompt_input.suffix and token_counts.suffix_len > 0:
            suffix_tokens = head_n(tokenized_suffix, token_counts.suffix_len)
            context_tokens += suffix_tokens
        else:
            suffix_tokens = []

        context_tokens += self.fim_middle

        ret_chunks = prompt_input.retrieved_chunks

        if self.filter_visible_chunks_by_content:
            prefix_len = len(self.tokenizer.detokenize(prefix_tokens))
            prefix_chars = prefix_chars[len(prefix_chars) - prefix_len :]

            suffix_len = len(self.tokenizer.detokenize(suffix_tokens))
            suffix_chars = suffix_chars[:suffix_len]

            # Filter chunks that are fully contained in the prompt range by content.
            ret_chunks = filter_visible_chunks_by_content(
                path=prompt_input.path,
                prompt_prefix=prefix_chars,
                prompt_suffix=suffix_chars,
                chunks=ret_chunks,
            )

        max_content_length = (
            self.apportionment_config.max_content_len - max_output_token_count
        )
        retrieval_len = max_content_length - len(tokens) - len(context_tokens)

        def tokenize_chunk(doc: PromptChunk) -> TokenList:
            result = []
            result += self.ret_start
            result += self.filename_tokens
            result += trailing_n(
                self.tokenizer.tokenize_safe(doc.path),
                self.apportionment_config.max_path_tokens,
            )
            result += self.ret_body
            result += self.tokenizer.tokenize_safe(doc.text)
            return result

        retrieval_tokens = concatenate_retrieved_chunks(
            retrieved_chunks=map(tokenize_chunk, ret_chunks),
            separator_tokens=[],
            max_total_tokens=retrieval_len,
        )

        if retrieval_tokens:
            tokens += retrieval_tokens

        tokens += context_tokens
        return PromptFormatterOutput([tokens])
