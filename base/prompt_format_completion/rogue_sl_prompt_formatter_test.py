"""Tests the behavior of the rogue stateless caching prompt formatter."""

import dataclasses
import json
from typing import Optional

import marshmallow
import pytest

from base import tokenizers
from base.prompt_format_completion import (
    PromptChunk,
    PromptInput,
    get_completion_prompt_formatter_by_name,
)
from base.prompt_format_completion.rogue_sl_prompt_formatter import (
    Component,
    RogueSLPromptFormatterConfig,
    update_retrieval_budget,
)
from base.prompt_format_completion.stateless_caching import StatelessCachingConfig
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig

DEFAULT_APPORTIONMENT_CONFIG = TokenApportionmentConfig(
    max_content_len=2048,
    input_fraction=0.5,
    prefix_fraction=0.75,
    max_path_tokens=20,
)


def _get_rogue_sl_prompt_formatter(
    tokenizer_name="starcoder",
    apportionment_config: Optional[TokenApportionmentConfig] = None,
    stateless_config: Optional[StatelessCachingConfig] = None,
    component_order: Optional[tuple[Component, ...]] = None,
    use_far_prefix_token: bool = False,
    filter_visible_chunks_by_content: bool = False,
):
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    if apportionment_config is None:
        # clone it
        apportionment_config = dataclasses.replace(DEFAULT_APPORTIONMENT_CONFIG)
    if stateless_config is None:
        stateless_config = StatelessCachingConfig()

    if component_order is None:
        component_order = (
            "preamble",
            "path",
            "prefix",
            "suffix",
            "retrieval",
        )
    prompt_formatter_config = {
        "stateless_caching_config": stateless_config.to_dict(),
        "component_order": component_order,
        "use_far_prefix_token": use_far_prefix_token,
        "filter_visible_chunks_by_content": filter_visible_chunks_by_content,
    }
    prompter = get_completion_prompt_formatter_by_name(
        "rogue_sl",
        tokenizer,
        apportionment_config,
        prompt_formatter_config,
    )

    return prompter, tokenizer


def _format(
    prompt_input: PromptInput,
    tokenizer_name="rogue",
    apportionment_config: Optional[TokenApportionmentConfig] = None,
    stateless_config: Optional[StatelessCachingConfig] = None,
    component_order: Optional[tuple[Component, ...]] = None,
    use_far_prefix_token: bool = False,
    max_output_token_count: int = 64,
    filter_visible_chunks_by_content: bool = False,
):
    prompter, tokenizer = _get_rogue_sl_prompt_formatter(
        tokenizer_name=tokenizer_name,
        apportionment_config=apportionment_config,
        stateless_config=stateless_config,
        component_order=component_order,
        use_far_prefix_token=use_far_prefix_token,
        filter_visible_chunks_by_content=filter_visible_chunks_by_content,
    )

    prompt_tokens = prompter.format_prompt(
        prompt_input, max_output_token_count=max_output_token_count
    ).tokens()
    print(len(prompt_tokens), prompt_tokens)
    prompt = tokenizer.detokenize(prompt_tokens)
    print("prompt", prompt)
    return prompt


def test_parse_roguesl_prompt_formatter_config():
    """Test parsing a valid rogue_sl prompt formatter config from json."""
    prompt_formatter_config_json = {
        "stateless_caching_config": {
            "nearby_prefix_token_len": 10,
            "nearby_prefix_token_overlap": 10,
        },
        "component_order": ("path", "prefix", "suffix", "retrieval"),
        "use_far_prefix_token": False,
    }
    jsons = json.dumps(prompt_formatter_config_json)
    prompt_formatter_config = RogueSLPromptFormatterConfig.from_json(jsons)
    assert (
        prompt_formatter_config.stateless_caching_config.nearby_prefix_token_len == 10
    )
    assert (
        prompt_formatter_config.stateless_caching_config.nearby_prefix_token_overlap
        == 10
    )


def test_invalid_roguesl_prompt_formatter_config_raises():
    """Test parsing an invalid rogue_sl prompt formatter config from json."""
    prompt_formatter_config_json = {
        "stateless_caching_config": {
            "nearby_prefix_token_len": 10,
            "nearby_prefix_token_overlap": 10,
            "invalid_key": "invalid_value",  # OFFENDING KEY
        },
        "component_order": ("path", "prefix", "suffix", "retrieval"),
        "use_far_prefix_token": False,
    }
    with pytest.raises(marshmallow.exceptions.ValidationError):
        get_completion_prompt_formatter_by_name(
            "rogue_sl",
            tokenizers.create_tokenizer_by_name("starcoder"),
            TokenApportionmentConfig(
                max_content_len=2048,
                input_fraction=0.5,
                prefix_fraction=0.75,
                max_path_tokens=20,
            ),
            prompt_formatter_config_json,
        )


def test_roguesl_prompt_formatter_basic():
    """Test prompt formatting without nearby_prefix/suffix."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(example_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_empty_prefix_and_suffix():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    prefix = ""
    suffix = ""
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(test_prompt_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>",
            "<fim_suffix>",
            "<|retrieval_section|>",
            "<fim_middle>",
        ]
    )

    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_empty_suffix():
    """Verifies the formatting with an empty suffix."""
    prefix = "def aggregate(a,b):\n"
    suffix = ""
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(example_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>",
            "<|retrieval_section|>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_limited_path():
    """Verifies the formatting with limited path length."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    apportionment_config = TokenApportionmentConfig(
        max_content_len=128,
        input_fraction=0.7,
        prefix_fraction=0.75,
        max_path_tokens=3,
    )
    prompt = _format(example_input, apportionment_config=apportionment_config)

    expected_prompt = "".join(
        [
            "<filename>example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_retrieval():
    """Tests the basic behavior with retrieved chunks."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
            ),
        ),
    )

    prompt = _format(example_input)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_retrieval_with_limit():
    """Tests the basic behavior with retrieved chunks."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
            ),
        ),
    )

    apportionment_config = TokenApportionmentConfig(
        max_content_len=128,
        input_fraction=0.7,
        prefix_fraction=0.75,
        max_path_tokens=20,
        per_retriever_max_tokens={"dense_retriever": 30},
    )
    prompt = _format(example_input, apportionment_config=apportionment_config)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_retrieval_with_recency():
    """Tests retrieval with recency."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
            ),
            PromptChunk(
                text="# A recent chunk.",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# Another recent chunk.",
                path="src/recent2.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# A git diff chunk.",
                path="src/gitdiff.py",
                origin="recency_retriever_git_diff",
            ),
            PromptChunk(
                text="# Another git diff chunk.",
                path="src/gitdiff2.py",
                origin="recency_retriever_git_diff",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                "recency_retriever": 200,
                "recency_retriever_git_diff": 200,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/recent2.py",
            "<|ret-body|># Another recent chunk.",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk.",
            "<|ret-start|><filename>src/gitdiff2.py",
            "<|ret-body|># Another git diff chunk.",
            "<|ret-start|><filename>src/gitdiff.py",
            "<|ret-body|># A git diff chunk.",
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_retrieval_with_recency_and_limit():
    """Tests retrieval with recency and limited budgets."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# A funny hunk.",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# A really long chunk\n" * 2000,
                path="src/foo.py",
            ),
            PromptChunk(
                text="# A recent chunk.",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# Another recent chunk.",
                path="src/recent2.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# A git diff chunk.",
                path="src/gitdiff.py",
                origin="recency_retriever_git_diff",
            ),
            PromptChunk(
                text="# Another git diff chunk.",
                path="src/gitdiff2.py",
                origin="recency_retriever_git_diff",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                # just enough to include one chunk but not the other
                "recency_retriever": 15,
                "recency_retriever_git_diff": 15,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk.",
            "<|ret-start|><filename>src/gitdiff.py",
            "<|ret-body|># A git diff chunk.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># A funny hunk.",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_retrieval_with_recency_and_limit2():
    """Tests the basic behavior with retrieved chunks."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
            ),
            PromptChunk(
                text="# A recent chunk.",
                path="src/recent.py",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="# Another recent chunk.",
                path="src/recent2.py",
                origin="recency_retriever",
            ),
        ),
    )

    prompt = _format(
        example_input,
        apportionment_config=TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
            per_retriever_max_tokens={
                # just enough to include one chunk but not the other
                "recency_retriever": 15,
            },
        ),
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk.",
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_format_prompt_retrieval_with_recency_and_overlapping_chunks():
    """Tests the basic behavior with chunks that are retrieved by multiple retrievers."""
    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        prefix_begin=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="3",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.",
                path="src/foo.py",
                unique_id="4",
                origin="",  # intentionally unspecified.
            ),
            PromptChunk(
                text="# A recent chunk",
                path="src/recent.py",
                unique_id="5",
                origin="recency_retriever",
            ),
            # This chunk was retrieved by both the recency and dense retrievers,
            # and it should only show up once
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="3",
                origin="recency_retriever",
            ),
        ),
    )
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
        per_retriever_max_tokens={
            "signature_retriever": 100,
            "dense_retriever": 200,
            "recency_retriever": 100,
        },
    )
    prompt = _format(example_input, tokenizer_name="rogue", apportionment_config=config)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            # NOTE(guy): recent chunks go first
            # This is the duplicate chunk: it should only show up once
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<|ret-start|><filename>src/recent.py",
            "<|ret-body|># A recent chunk",
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "<|ret-start|><filename>src/foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_nearby_prefix():
    """Test of nearby prefix logic."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=4,
        nearby_prefix_token_overlap=1,
    )

    component_order = ("path", "prefix", "suffix", "retrieval", "nearby_prefix")

    prompt = _format(
        example_input,
        stateless_config=stateless_config,
        component_order=component_order,
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|nearby_prefix|>,b):\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_nearby_component_order_present_assert():
    """Test for assert nearby prefix in component order if nonzero."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=4,
    )

    # We should have nearby_prefix in component order
    component_order = ("path", "prefix", "suffix", "retrieval")

    with pytest.raises(ValueError):
        _ = _format(
            example_input,
            stateless_config=stateless_config,
            component_order=component_order,
            use_far_prefix_token=True,
        )


def test_nearby_nonzero_assert():
    """Test for assert nearby prefix nonzero if in component order."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    # This config should have nearby_prefix nonzero
    stateless_config = StatelessCachingConfig()

    component_order = ("path", "prefix", "suffix", "retrieval", "nearby_prefix")

    with pytest.raises(ValueError):
        _ = _format(
            example_input,
            stateless_config=stateless_config,
            component_order=component_order,
        )


def test_roguesl_prompt_formatter_token_quant():
    """Test of quantization logic works."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=5,
        nearby_prefix_token_overlap=1,
        quantize_token_len=5,
    )

    component_order = ("path", "prefix", "suffix", "retrieval", "nearby_prefix")

    prompt = _format(
        example_input,
        stateless_config=stateless_config,
        component_order=component_order,
    )

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|nearby_prefix|>b):\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_char_quant():
    """Test of outer character quantization logic."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=4,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=5,
        nearby_prefix_token_overlap=1,
        quantize_token_len=5,
        quantize_char_len=7,
    )

    component_order = ("path", "prefix", "suffix", "retrieval", "nearby_prefix")

    prompt = _format(
        example_input,
        stateless_config=stateless_config,
        component_order=component_order,
    )

    # We expect the character quantizaton to truncate at a multiple of 7 characters from
    # the beginning of the 'true' prefix. We set prefix_begin to 4, so effectively we
    # expect it to remove 3 characters from our actual prefix.
    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix> aggregate(a,b):",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|nearby_prefix|>):\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def overlap_from_start(prompt1, prompt2):
    for idx, char in enumerate(prompt1):
        if char != prompt2[idx]:
            return idx
    return len(prompt1)


def test_typing_caching():
    quantize_token_len = 3
    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=6,
        nearby_prefix_token_overlap=1,
        quantize_token_len=quantize_token_len,
    )

    component_order = ("path", "prefix", "suffix", "retrieval", "nearby_prefix")

    starting_prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    model_input = PromptInput(
        path="src/example.py",
        prefix=starting_prefix,
        suffix=suffix,
        prefix_begin=4,
        retrieved_chunks=(),
    )

    num_comparisons = 9
    num_hits = 0

    last_prompt = _format(
        model_input,
        stateless_config=stateless_config,
        component_order=component_order,
    )

    single_token_chars = "showMessageDialog"

    for idx in range(num_comparisons):
        # Mimic typing in token space by repeatedly appending chars that combine to
        # single token
        model_input.prefix = starting_prefix + (idx + 1) * single_token_chars
        prompt = _format(
            model_input,
            stateless_config=stateless_config,
            component_order=component_order,
        )
        overlap = overlap_from_start(prompt, last_prompt)
        if overlap / len(last_prompt) > 0.9:
            num_hits += 1
        last_prompt = prompt

    # We expect to get a cache miss every context_quant_token_len tokens
    expected_hit_rato = (quantize_token_len - 1) / quantize_token_len
    actual_hit_ratio = num_hits / num_comparisons

    assert actual_hit_ratio == expected_hit_rato


def test_update_retrieval_budget():
    """Test that we can update the retrieval budget."""
    component_tokens = {
        "retrieval": [1, 2, 3, 4, 5],
        "prefix": [1, 2, 3, 4, 5],
        "suffix": [1, 2, 3, 4, 5],
        "nearby_prefix": [1, 2, 3, 4, 5],
    }
    max_prompt_tokens = 30
    retrieval_budget = 100
    new_retrieval_budget = update_retrieval_budget(
        retrieval_budget=retrieval_budget,
        max_prompt_tokens=max_prompt_tokens,
        component_tokens=component_tokens,
    )
    assert new_retrieval_budget == 8


def test_far_tokens():
    """Test prompt when using far tokens."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=10,
        nearby_prefix_token_overlap=10,
    )

    component_order = (
        "path",
        "prefix",
        "suffix",
        "retrieval",
        "nearby_prefix",
    )

    prompt = _format(
        example_input,
        stateless_config=stateless_config,
        component_order=component_order,
        use_far_prefix_token=True,
    )
    print("prompt:", prompt)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|far_prefix|>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt


def test_rogue_sl_overlap_filter():
    """Tests overlap detection with retrieved chunks."""

    example_input = PromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="return aggregated_output\n",
        prefix_begin=3,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                unique_id="1",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="class Example:\n  def aggregate(a,b):\n",
                path="src/example.py",
                unique_id="2",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/example.py",
                unique_id="3",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="aggregated_output = a+b\n",
                path="src/example.py",
                unique_id="4",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return aggregated_output\n",
                path="src/code.py",
                unique_id="5",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="def new_aggregate(a,b):\n",
                path="src/example.py",
                unique_id="6",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="aggregate",
                path="src/example.py",
                unique_id="7",
                origin="recency_retriever",
            ),
            PromptChunk(
                text="return",
                path="src/example.py",
                unique_id="8",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="(a,b):\nreturn",
                path="src/example.py",
                unique_id="9",
                origin="dense_retriever",
            ),
        ),
    )
    config = TokenApportionmentConfig(
        max_content_len=192,
        input_fraction=0.15,
        prefix_fraction=0.7,
        max_path_tokens=5,
        per_retriever_max_tokens={
            "signature_retriever": 25,
            "dense_retriever": 50,
            "recency_retriever": 50,
        },
    )

    stateless_config = StatelessCachingConfig(
        nearby_prefix_token_len=11,
        nearby_prefix_token_overlap=1,
        quantize_token_len=11,
        quantize_char_len=7,
    )

    component_order = (
        "path",
        "prefix",
        "suffix",
        "retrieval",
        "nearby_prefix",
    )

    prompt = _format(
        example_input,
        apportionment_config=config,
        stateless_config=stateless_config,
        component_order=component_order,
        use_far_prefix_token=True,
        filter_visible_chunks_by_content=True,
    )
    print("prompt:", prompt)

    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<|far_prefix|>aggregate<fim_suffix>return aggregated_<|retrieval_section|>",
            # NOTE(guy): recent chunks go first
            # NOTE(arun): the ordering of the chunks is intentionally reversed.
            "<|ret-start|><filename>src/example.py",
            "<|ret-body|>aggregated_output = a+b\n",
            "<|ret-start|><filename>src/code.py",
            "<|ret-body|>return aggregated_output\n",
            "<|ret-start|><filename>src/bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_prefix>aggregate(a,b):\n",
            "<fim_middle>",
        ]
    )

    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_with_deepseek():
    """Test prompt formatting with DeepseekCoder."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )

    expected_prompt = "".join(
        [
            "<｜begin▁of▁sentence｜>",
            "<filename>src/example.py\n",
            "<｜fim▁begin｜>def aggregate(a,b):\n",
            "<｜fim▁hole｜>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<｜fim▁end｜>",
        ]
    )
    prompt = _format(example_input, tokenizer_name="deepseek_coder_base")
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_attempt_with_starcoder():
    """Test prompt formatting with starcoder."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )
    expected_prompt = "".join(
        [
            "<filename>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<fim_middle>",
        ]
    )
    prompt = _format(example_input, tokenizer_name="starcoder")
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_attempt_with_starcoder2():
    """Test prompt formatting with StarCoder2."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )
    expected_prompt = "".join(
        [
            "<file_sep>src/example.py\n",
            "<fim_prefix>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<pr_base>",
            "<fim_middle>",
        ]
    )
    prompt = _format(example_input, tokenizer_name="starcoder2")
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_attempt_with_dsv2():
    """Test prompt formatting with DeepSeek V2."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )
    expected_prompt = "".join(
        [
            "<｜begin▁of▁sentence｜>",
            "<|filename|>src/example.py\n",
            "<｜fim▁begin｜>def aggregate(a,b):\n",
            "<｜fim▁hole｜>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<｜fim▁end｜>",
        ]
    )
    prompt = _format(example_input, tokenizer_name="deepseek_coder_v2")
    assert prompt == expected_prompt


def test_roguesl_prompt_formatter_attempt_with_llama3():
    """Test prompt formatting with llama3."""
    prefix = "def aggregate(a,b):\n"
    suffix = "\nreturn aggregated_output\n"
    example_input = PromptInput(
        path="src/example.py",
        prefix=prefix,
        suffix=suffix,
        prefix_begin=0,
        retrieved_chunks=(),
    )
    expected_prompt = "".join(
        [
            "<|begin_of_text|>",
            "<filename>src/example.py\n",
            "<|fim_prefix|>def aggregate(a,b):\n",
            "<|fim_suffix|>\nreturn aggregated_output\n",
            "<|retrieval_section|>",
            "<|fim_middle|>",
        ]
    )
    for name in ["llama3_base", "llama3_instruct"]:
        prompt = _format(example_input, tokenizer_name=name)
        assert prompt == expected_prompt
