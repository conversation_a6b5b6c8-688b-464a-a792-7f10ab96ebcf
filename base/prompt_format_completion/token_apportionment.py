"""Module containing code related to the token apportionment."""

from dataclasses import dataclass, field

import dataclasses_json


@dataclass
class TokenApportionmentConfig(dataclasses_json.DataClassJsonMixin):
    """The token apportionment configuration contains the basic config.

    This class is usually the input into (advanced) prompt formatter,
    which the prompt formatter should take as a hint on the expected
    prompt
    """

    # the maximal size of the prompt (after any potential generation of tokens)
    max_content_len: int

    # the fraction of the prompt for the input (prefix/path/suffix) as opposed
    # to retrieved chunks.
    # the prompt formatter should take this value as a hint
    input_fraction: float

    # A hint on the fraction of the input that should be apportioned to the prefix.
    # The remainder is used for suffix and path
    prefix_fraction: float

    # the maximal number of tokens that should be included for any path
    max_path_tokens: int

    # the maximal number of tokens that should be used for each retriever, if specified.
    # NOTE(arun): Unfortunately, dataclasses_json doesn't support deserializing Mapping
    # or Sequence types. Till this bug is fixed, please treat this field as read-only.
    per_retriever_max_tokens: dict[str, int] = field(default_factory=dict)

    extra_generation_budget: int = 0
    """Extra budget during generation used e.g., for tool-use."""

    def __post_init__(self):
        assert self.max_content_len > 0
        assert self.input_fraction >= 0.0
        assert self.prefix_fraction >= 0.0
        for retriever, budget in self.per_retriever_max_tokens.items():
            assert (
                budget >= 0
            ), f"The retrieval budget for {retriever} must be non-negative."


@dataclass
class TokenApportionment(dataclasses_json.DataClassJsonMixin):
    """Stores the apportionment of the tokens."""

    path_len: int
    """The number of tokens of the path to include."""

    prefix_len: int
    """The number of tokens of the prefix to include."""

    retrieval_len: int
    """The number of tokens from retrieved chunks to include."""

    suffix_len: int
    """The number of tokens of the suffix to include."""

    def __post_init__(self):
        if self.path_len < 0:
            raise ValueError(f"{self.path_len} must be non-negative.")
        if self.prefix_len < 0:
            raise ValueError(f"{self.prefix_len=} must be non-negative.")
        if self.suffix_len < 0:
            raise ValueError(f"{self.suffix_len=} must be non-negative.")
        if self.retrieval_len < 0:
            raise ValueError(f"{self.retrieval_len=} must be non-negative.")


# TODO(arun): It seems like many of these arguments could be rolled into other ones,
# e.g. `path_prefix` could be simply `num_path_tokens > 0`, and `num_path_sep_tokens`
# could be included in `num_path_tokens`.
def apportion_context_budget(
    max_content_length: int,
    input_fraction: float,
    prefix_fraction: float,
    path_prefix: bool,
    max_path_tokens: int,
    num_preference_tokens: int,
    num_path_tokens: int,
    num_prefix_tokens: int,
    num_suffix_tokens: int,
    num_path_sep_tokens: int = 1,
    num_suffix_sep_tokens: int = 1,
) -> TokenApportionment:
    """Chooses the token length for each component of the context.

    Parameters
    ----------
    max_content_length : int
      Max number of tokens for the entire context (aka our total "budget").
    input_fraction : float
      The fraction of the context that should be given to user input (path, prefix,
      suffix). The remainder is given to preference tokens and retrieved chunks.
    prefix_fraction : float
      The fraction of the input budget that should be used for the prefix, as opposed
      to the suffix.
    path_prefix : bool
      Indicated whether the context should include the file path.
    max_path_tokens : int
      The maximum number of tokens from the file path that should be included in the
      context.
    fim_supported : bool
      Indicates whether the context should be formatted for use with fill-in-the-middle.
    num_preference_tokens : int
      The number of preference tokens. These are static overhead.
    num_path_tokens : int
      Token length of file path.
    num_prefix_tokens : int
      Token length of the user's prefix.
    num_suffix_tokens : int
      Token length of the user's suffix.
    num_path_sep_tokens : int
      Number of tokens in path separator.
    num_suffix_sep_tokens : int
      Number of tokens in fim separator.

    Returns
    -------
    A TokenApportionment indicating the token budget for each component of the context:
    * path_len: the file path
    * prefix_len: prefix
    * retrieval_len: retrieved chunks
    * suffix_len: suffix
    """
    if not path_prefix:
        num_path_tokens = 0
        num_path_sep_tokens = 0
    num_path_tokens = min(num_path_tokens, max_path_tokens)

    input_budget = int(max_content_length * input_fraction)

    # If the entire (truncated) path, plus its separator, fits within the input budget,
    # take it all. Otherwise don't take any of it.
    path_allotment = (
        num_path_tokens if num_path_tokens + num_path_sep_tokens <= input_budget else 0
    )
    path_sep_allotment = num_path_sep_tokens if path_allotment else 0

    # Of the remaining input budget, give at least prefix_fraction of it to
    # the prefix and the remainder to the suffix. If the suffix can't use all of the
    # remainder, give the difference to the prefix.
    prefix_suffix_budget = input_budget - (path_allotment + path_sep_allotment)
    prefix_min = min(num_prefix_tokens, int(prefix_suffix_budget * prefix_fraction))

    suffix_allotment = min(
        num_suffix_tokens + num_suffix_sep_tokens, prefix_suffix_budget - prefix_min
    )
    if suffix_allotment <= num_suffix_sep_tokens:
        # No room for any suffix tokens.
        suffix_allotment = 0
        suffix_sep_allotment = 0
    else:
        # Divide allotment between suffix and separator.
        suffix_allotment -= num_suffix_sep_tokens
        suffix_sep_allotment = num_suffix_sep_tokens

    prefix_allotment = min(
        num_prefix_tokens,
        prefix_suffix_budget - (suffix_allotment + suffix_sep_allotment),
    )

    input_allotment = sum(
        [
            path_allotment,
            path_sep_allotment,
            prefix_allotment,
            suffix_allotment,
            suffix_sep_allotment,
        ]
    )
    assert input_allotment <= input_budget

    # If the preference tokens fit within the retrieval-side budget take them all,
    # otherwise don't take anything.
    retrieval_plus_preference_budget = max_content_length - input_allotment
    preference_allotment = (
        num_preference_tokens
        if num_preference_tokens <= retrieval_plus_preference_budget
        else 0
    )

    # Retrieval gets whatever is left.
    retrieval_allotment = retrieval_plus_preference_budget - preference_allotment

    assert (
        sum([input_allotment, preference_allotment, retrieval_allotment])
        <= max_content_length
    )

    # Further subdivide the retrieval allotment by retriever
    return TokenApportionment(
        path_len=path_allotment,
        prefix_len=prefix_allotment,
        retrieval_len=retrieval_allotment,
        suffix_len=suffix_allotment,
    )
