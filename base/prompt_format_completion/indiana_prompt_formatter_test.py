"""Tests for the codegen indiana prompt formatter."""

from typing import Iterable

from base import tokenizers
from base.prompt_format_completion import PromptChunk, PromptInput
from base.prompt_format_completion.indiana_prompt_formatter import (
    IndianaPromptFormatter,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig


def test_codegen_prompt_formatter_basic(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """This is a simple sanity check to catch obvious bugs in Indiana's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = IndianaPromptFormatter(config, tokenizer)

    example_input.retrieved_chunks = example_chunks
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    # note the three preferences tokens.
    # the chunks are ordered from low to high with the highest relevance nearest to the prompt
    expected_prompt = """<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>src/foo.py
# You can aggregate
# with a pooling function.
<|ret-endofdoc|>src/bar.py
# You can aggregate
# with a maxing
# function.
<|ret-endofdoc|>src/example.py

return aggregated_output
<|fim-sep|>def aggregate(a,b):
"""
    assert prompt == expected_prompt


def test_codegen_indiana_prompt_formatter_partial_retrieval(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """Tests the behavior of the indiana prompt formatter if only parts of the retrieved chunks fit into the prompt.

    In particular, the most relevant chunks should be included and less relevant chunks should be skipped.
    """
    tokenizer = tokenizers.create_tokenizer_by_name("fim")

    # it contains bar.py and not foo.py because bar.py is of higher relevancy
    expected_prompt = """<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>src/bar.py
# You can aggregate
# with a maxing
# function.
<|ret-endofdoc|>src/example.py

return aggregated_output
<|fim-sep|>def aggregate(a,b):
"""
    expected_prompt_token_count = len(tokenizer.tokenize_unsafe(expected_prompt))

    config = TokenApportionmentConfig(
        max_content_len=expected_prompt_token_count + 64 + 10,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = IndianaPromptFormatter(config, tokenizer)

    example_input.retrieved_chunks = example_chunks
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)
    assert prompt == expected_prompt


def test_codegen_indiana_prompt_formatter_no_retrieval(example_input: PromptInput):
    """Verifies the formatting without retrieval."""
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = IndianaPromptFormatter(config, tokenizer)

    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)
    # note that the expected prompt does not contain a <ret-endofdoc> token
    expected_prompt = """<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>src/example.py

return aggregated_output
<|fim-sep|>def aggregate(a,b):
"""
    assert prompt == expected_prompt


def test_codegen_indiana_prompt_formatter_empty_suffix(example_input: PromptInput):
    """Verifies the formatting with an empty suffix."""
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = IndianaPromptFormatter(config, tokenizer)

    example_input.suffix = ""
    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    # note that the expected prompt does not contain a <ret-endofdoc> token. There is also
    # no fim-sep token when there is no suffix.
    expected_prompt = """<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>src/example.py
def aggregate(a,b):
"""
    assert prompt == expected_prompt


def test_codegen_indiana_prompt_formatter_length(example_input: PromptInput):
    """This test checks the generation when the available prompt input exceeds the available number of tokens so that only parts of the prefix fit."""
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=54, input_fraction=0.5, prefix_fraction=0.75, max_path_tokens=20
    )
    prompter = IndianaPromptFormatter(config, tokenizer)

    prompt_tokens = prompter.format_prompt(
        example_input, max_output_token_count=24
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    # note this contains the suffix of the prefix and the prefix of the suffix. It removed
    # all retrieved chunks because one of them fit.
    expected_prompt = """<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>src/example.py

return<|fim-sep|>(a,b):
"""
    assert prompt == expected_prompt


def test_codegen_indiana_prompt_formatter_empty_prefix_and_suffix():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0.5,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    prompter = IndianaPromptFormatter(config, tokenizer)

    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )

    prompt_tokens = prompter.format_prompt(
        test_prompt_input, max_output_token_count=64
    ).tokens()
    prompt = prompter.tokenizer.detokenize(prompt_tokens)

    expected_prompt = """<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>src/example.py\n"""
    assert prompt == expected_prompt
