"""Tests for recency_utils.py.

bazel test //base/prompt_format_completion:recency_utils_test
"""

from datetime import datetime, timedelta

import pytest

from base.prompt_format.recency_info import ReplacementText, ViewedContentEvent
from base.prompt_format_completion.recency_utils import (
    TimestampState,
    deduplicate_viewed_content_against_replacements,
    filter_stale_viewed_content,
    get_latest_file_timestamps,
    limit_viewed_content_chunk_size,
)


def create_replacement_text(
    path: str,
    blob_name: str = "test_blob",
    char_start: int = 0,
    char_end: int = 10,
    replacement_text: str = "test content",
    timestamp: datetime | None = None,
) -> ReplacementText:
    """Helper to create ReplacementText instances for testing."""
    return ReplacementText(
        blob_name=blob_name,
        path=path,
        char_start=char_start,
        char_end=char_end,
        replacement_text=replacement_text,
        present_in_blob=False,
        timestamp=timestamp,
    )


def create_viewed_content(
    path: str,
    timestamp: datetime,
    char_start: int = 0,
    char_end: int = 100,
    visible_content: str = "viewed content",
    line_start: int = 1,
    line_end: int = 10,
) -> ViewedContentEvent:
    """Helper to create ViewedContentEvent instances for testing."""

    return ViewedContentEvent(
        path=path,
        file_blob_name="test_blob",
        visible_content=visible_content,
        line_start=line_start,
        line_end=line_end,
        char_start=char_start,
        char_end=char_end,
        timestamp=timestamp,
    )


class TestGetLatestFileTimestamps:
    """Tests for get_latest_file_timestamps function."""

    def test_empty_list(self):
        """Test with empty replacement texts list."""
        result = get_latest_file_timestamps([])
        assert result == {}

    def test_same_file_multiple_timestamps(self):
        """Test with same file having multiple timestamps - should keep latest."""
        timestamp1 = datetime(2023, 1, 1, 12, 0, 0)
        timestamp2 = datetime(2023, 1, 2, 12, 0, 0)  # Later timestamp
        replacements = [
            create_replacement_text("file1.py", timestamp=timestamp1),
            create_replacement_text("file1.py", timestamp=timestamp2),
            create_replacement_text("file2.py", timestamp=timestamp1),
            create_replacement_text("file3.py", timestamp=None),
        ]

        result = get_latest_file_timestamps(replacements)
        assert result == {
            "file1.py": timestamp2,
            "file2.py": timestamp1,
            "file3.py": TimestampState.UNDETERMINED,
        }

    def test_same_file_with_and_without_timestamp(self):
        """Test with same file having both timestamped and non-timestamped replacements."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        replacements = [
            create_replacement_text("file1.py", timestamp=timestamp),
            create_replacement_text("file1.py", timestamp=None),
        ]

        result = get_latest_file_timestamps(replacements)
        assert result == {"file1.py": TimestampState.UNDETERMINED}

    def test_same_file_none_first_then_timestamp(self):
        """Test with same file having None timestamp first, then valid timestamp."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        replacements = [
            create_replacement_text("file1.py", timestamp=None),
            create_replacement_text("file1.py", timestamp=timestamp),
        ]

        result = get_latest_file_timestamps(replacements)
        assert result == {"file1.py": TimestampState.UNDETERMINED}


class TestFilterStaleViewedContent:
    """Tests for filter_stale_viewed_content function."""

    def test_empty_viewed_content(self):
        """Test with empty viewed content list."""
        result = filter_stale_viewed_content([], {})
        assert result == []

    def test_mixed_files_and_timestamps(self):
        """Test with mix of files with different timestamp states."""
        base_time = datetime(2023, 1, 1, 12, 0, 0)
        viewed_content = [
            create_viewed_content(
                "file1.py", base_time + timedelta(minutes=2)
            ),  # Should keep - not in timestamps
            create_viewed_content(
                "file2.py", base_time + timedelta(minutes=2)
            ),  # Should filter - undetermined
            create_viewed_content(
                "file3.py", base_time + timedelta(minutes=2)
            ),  # Should keep - newer than cutoff
            create_viewed_content(
                "file4.py", base_time
            ),  # Should filter - older than cutoff
        ]
        file_timestamps = {
            "file2.py": TimestampState.UNDETERMINED,
            "file3.py": base_time,
            "file4.py": base_time,
        }

        result = filter_stale_viewed_content(
            viewed_content, file_timestamps, buffer_seconds=60.0
        )
        assert len(result) == 2
        assert result[0].path == "file1.py"
        assert result[1].path == "file3.py"


class TestDeduplicateViewedContentAgainstReplacements:
    """Tests for deduplicate_viewed_content_against_replacements function."""

    def test_empty_inputs(self):
        """Test with empty inputs."""
        assert deduplicate_viewed_content_against_replacements([], []) == []

        viewed_content = [create_viewed_content("file1.py", datetime.now())]
        assert (
            deduplicate_viewed_content_against_replacements(viewed_content, [])
            == viewed_content
        )

        replacements = [create_replacement_text("file1.py")]
        assert deduplicate_viewed_content_against_replacements([], replacements) == []

    def test_no_overlap_different_files(self):
        """Test viewed content and replacements in different files - should keep all."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        viewed_content = [
            create_viewed_content("file1.py", timestamp, char_start=0, char_end=100)
        ]
        replacements = [create_replacement_text("file2.py", char_start=0, char_end=50)]

        result = deduplicate_viewed_content_against_replacements(
            viewed_content, replacements
        )
        assert result == viewed_content

    def test_no_overlap_same_file(self):
        """Test viewed content and replacements in same file but no overlap - should keep."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        viewed_content = [
            create_viewed_content("file1.py", timestamp, char_start=0, char_end=50)
        ]
        replacements = [
            create_replacement_text("file1.py", char_start=100, char_end=150)
        ]

        result = deduplicate_viewed_content_against_replacements(
            viewed_content, replacements
        )
        assert result == viewed_content

    def test_complete_overlap(self):
        """Test viewed content completely overlapped by replacement - should filter out."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        viewed_content = [
            create_viewed_content("file1.py", timestamp, char_start=10, char_end=50)
        ]
        replacements = [create_replacement_text("file1.py", char_start=0, char_end=100)]

        result = deduplicate_viewed_content_against_replacements(
            viewed_content, replacements
        )
        assert result == []

    def test_partial_overlap(self):
        """Test viewed content partially overlapped by replacement - should filter out."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        viewed_content = [
            create_viewed_content("file1.py", timestamp, char_start=0, char_end=100)
        ]
        replacements = [
            create_replacement_text("file1.py", char_start=50, char_end=150)
        ]

        result = deduplicate_viewed_content_against_replacements(
            viewed_content, replacements
        )
        assert result == []

    def test_multiple_viewed_content_mixed_overlap(self):
        """Test multiple viewed content with mixed overlap scenarios."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        viewed_content = [
            create_viewed_content(
                "file1.py", timestamp, char_start=0, char_end=50
            ),  # No overlap
            create_viewed_content(
                "file1.py", timestamp, char_start=75, char_end=125
            ),  # Overlaps
            create_viewed_content(
                "file2.py", timestamp, char_start=0, char_end=100
            ),  # Different file
        ]
        replacements = [
            create_replacement_text("file1.py", char_start=100, char_end=150)
        ]

        result = deduplicate_viewed_content_against_replacements(
            viewed_content, replacements
        )
        assert len(result) == 2
        assert result[0].path == "file1.py" and result[0].char_start == 0
        assert result[1].path == "file2.py"

    def test_multiple_replacements_same_file(self):
        """Test multiple replacements in same file."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        viewed_content = [
            create_viewed_content("file1.py", timestamp, char_start=25, char_end=75)
        ]
        replacements = [
            create_replacement_text("file1.py", char_start=0, char_end=50),  # Overlaps
            create_replacement_text(
                "file1.py", char_start=100, char_end=150
            ),  # No overlap
        ]

        result = deduplicate_viewed_content_against_replacements(
            viewed_content, replacements
        )
        assert result == []  # Should be filtered due to first replacement overlap


class TestLimitViewedContentChunkSize:
    """Test cases for limit_viewed_content_chunk_size function."""

    def test_empty_input(self):
        """Test with empty viewed content list."""
        result = limit_viewed_content_chunk_size([])
        assert result == []

    def test_content_within_limit(self):
        """Test content that's already within the size limit."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        short_content = "short content1\nshort content2\nshort content3\n"
        viewed_content = [
            create_viewed_content(
                "file1.py",
                timestamp,
                visible_content=short_content,
                line_start=1,
                line_end=3,
            )
        ]

        result = limit_viewed_content_chunk_size(viewed_content, max_chunk_size=1024)

        assert len(result) == 1
        assert result == viewed_content

    def test_multiline_content_with_very_long_lines(self):
        """Test case with very long lines that exceed the chunk size."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        # Create content where each line is very long (exceeds max_chunk_size)
        lines = ["x" * 500 for _ in range(10)]
        long_content = "\n".join(lines)
        viewed_content = [
            create_viewed_content(
                "file1.py",
                timestamp,
                visible_content=long_content,
                line_start=1,
                line_end=10,
            )
        ]

        result = limit_viewed_content_chunk_size(viewed_content, max_chunk_size=300)

        assert len(result) == 0

    def test_smart_chunker_with_multiple_chunks(self):
        """Test smart chunker behavior when content creates multiple chunks."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        # Create content that will definitely create multiple chunks
        # Each function is separated by empty lines, which are good break points
        content = """def function1():
    print("This is function 1")
    return 1

def function2():
    print("This is function 2")
    return 2

def function3():
    print("This is function 3")
    return 3

def function4():
    print("This is function 4")
    return 4

def function5():
    print("This is function 5")
    return 5"""

        start_line = 353
        total_lines = content.count("\n")
        end_line = start_line + total_lines
        viewed_content = [
            create_viewed_content(
                "file1.py",
                timestamp,
                visible_content=content,
                line_start=start_line,
                line_end=end_line,
            )
        ]

        result = limit_viewed_content_chunk_size(
            viewed_content, max_chunk_size=100, target_line_ratio=0.33
        )

        assert len(result) == 1
        assert len(result[0].visible_content) <= 100

        expected_function = "function2"  # 1/3 of the content should be function 2
        expected_lines = (start_line + 3, start_line + 6)

        assert expected_function in result[0].visible_content
        actual_lines = (result[0].line_start, result[0].line_end)
        assert (
            actual_lines == expected_lines
        ), f"Expected {expected_lines}, got {actual_lines}"

    def test_target_line_ratio_parameter(self):
        """Test that the target_line_ratio parameter works correctly."""
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        # Create content with 20 functions, each 5 lines (total 100 lines)
        functions = []
        for i in range(20):
            functions.append(f"""def function_{i}():
    '''Function {i} documentation'''
    print("This is function {i}")
    return {i}
""")

        content = "\n".join(functions)
        viewed_content = [
            create_viewed_content(
                "file1.py",
                timestamp,
                visible_content=content,
                line_start=1,
                line_end=100,
            )
        ]

        # Test different ratios
        test_cases = [
            (0.1, "function_0", "function_1"),  # 10% should be near the beginning
            (0.5, "function_9", "function_10"),  # 50% should be in the middle
            (0.8, "function_15", "function_16"),  # 80% should be near the end
        ]

        for ratio, expected_func1, expected_func2 in test_cases:
            result = limit_viewed_content_chunk_size(
                viewed_content, max_chunk_size=300, target_line_ratio=ratio
            )

            assert len(result) == 1
            # Smart chunker may slightly exceed the limit to respect code boundaries
            assert len(result[0].visible_content) <= 350

            # Should contain functions from the expected range
            selected_content = result[0].visible_content
            contains_expected = (
                expected_func1 in selected_content or expected_func2 in selected_content
            )
            assert contains_expected, f"Ratio {ratio} should contain {expected_func1} or {expected_func2}, but got: {selected_content[:100]}..."
