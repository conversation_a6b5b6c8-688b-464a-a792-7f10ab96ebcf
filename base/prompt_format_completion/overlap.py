"""Common prompt format completion module"""

import logging
from functools import partial
from typing import Callable, Iterable, Optional, Protocol, Sequence, TypeVar

from prometheus_client import Counter

from base.prompt_format.common import PromptChunk
from base.prompt_format.recency_info import RecencyInfo, ReplacementText
from base.ranges.range_types import Char<PERSON>ange

_overlap_counter = Counter(
    "au_completion_overlap_counter",
    "Counts the number of retrieved chunk that are dropped due to overlaps.",
    ["overlap_reason"],
)

_recency_overlap_filter_counter = Counter(
    "au_recency_overlap_filter_counter",
    "Counts the number of retrieved chunk that are dropped through recency overlap filter.",
)

_recency_overlap_kept_counter = Counter(
    "au_recency_overlap_keep_counter",
    "Counts the number of retrieved chunk that are kept through recency overlap filter.",
)

logger = logging.getLogger(__name__)


def filter_visible_chunks_by_content(
    path: str,
    prompt_prefix: str,
    prompt_suffix: str,
    chunks: Iterable[PromptChunk],
    min_prefix_suffix_len: Optional[int] = None,
) -> Iterable[PromptChunk]:
    """Remove any chunk that's fully contained in the prompt range.

    Args:
        path: The path of the current file.
        prompt_range: The character range of the prompt in the current file.
        chunks: The chunks to be filtered.
    """

    if not path:
        logger.warning("Path is empty, skipping overlap check.")
        yield from chunks
        return

    prompt_prefix_and_suffix = prompt_prefix + prompt_suffix

    if min_prefix_suffix_len is not None:
        if len(prompt_prefix) >= min_prefix_suffix_len:
            new_prompt_prefix = prompt_prefix
        else:
            new_prompt_prefix = (
                prompt_prefix
                + prompt_suffix[: min_prefix_suffix_len - len(prompt_prefix)]
            )

        if len(prompt_suffix) >= min_prefix_suffix_len:
            new_prompt_suffix = prompt_suffix
        else:
            new_prompt_suffix = (
                prompt_prefix[-(min_prefix_suffix_len - len(prompt_suffix)) :]
                + prompt_suffix
            )

        prompt_prefix = new_prompt_prefix
        prompt_suffix = new_prompt_suffix

    for chunk in chunks:
        if chunk.path != path:
            yield chunk
            continue

        chunk_in_prompt = (
            prompt_prefix_and_suffix
            and chunk.text
            and chunk.text in prompt_prefix_and_suffix
        )

        prefix_in_chunk = prompt_prefix and prompt_prefix in chunk.text
        suffix_in_chunk = prompt_suffix and prompt_suffix in chunk.text

        # There are 3 special locations in the current doc: prefix_begin, cursor, and suffix_end.
        # If a chunk overlaps with cursor, then it either
        #   - overlaps with prefix_begin (=> it fully contains prefix),
        #   - or suffix_end (=> it fully contains suffix),
        #   - or neither (=> it is fully contained in prefix+suffix).
        # So the following condition should be sufficient to do the filtering: `prefix in chunk or suffix in chunk or chunk in (prefix+suffix)`."""
        if chunk_in_prompt or prefix_in_chunk or suffix_in_chunk:
            message = "Drop prompt chunk %s (id=%s) from %s since its content overlaps with cursor."
            logger.debug(message, chunk.blob_name, chunk.unique_id, chunk.origin)
            _overlap_counter.labels(overlap_reason="prompt_format_content").inc()
            continue

        yield chunk


class OverlapChunk(Protocol):
    @property
    def blob_name(self) -> str | None: ...
    @property
    def char_start(self) -> int: ...

    """Inclusive. Offset in UTF-8 characters where the region represented by this chunk starts."""

    @property
    def char_end(self) -> int: ...

    """Exclusive. Offset in UTF-8 characters where the region represented by this chunk ends."""


def partial_overlap_predicate(chunk1: OverlapChunk, chunk2: OverlapChunk) -> bool:
    """Check if the two chunks partially overlap."""

    return chunk1.blob_name == chunk2.blob_name and CharRange(
        chunk1.char_start, chunk1.char_end
    ).overlaps(CharRange(chunk2.char_start, chunk2.char_end))


def find_first_overlap(
    retrieved_chunk: OverlapChunk,
    overlap_chunks: Iterable[OverlapChunk],
    predicate: Callable[[OverlapChunk, OverlapChunk], bool],
) -> OverlapChunk | None:
    """Check if the retrieved chunk overlaps with any chunk from the list. Returns the overlapping chunk if found or None."""
    return next(
        filter(
            partial(
                predicate,
                retrieved_chunk,
            ),
            overlap_chunks,
        ),
        None,
    )


class ModifiedChunk(OverlapChunk, Protocol):
    @property
    def origin(self) -> str: ...


M = TypeVar("M", bound=ModifiedChunk)


def modified_chunks_filter(
    chunks: Iterable[M],
    recency_info: RecencyInfo,
    origins: Sequence[str],
    skip_origins: Sequence[str],
) -> Iterable[M]:
    """Remove any chunk that is outdated - identified by unindexed recent change on the blob.

    Args:
        chunks: The chunks to be filtered.
        recency_info: The recency info.
        origins: Chunk origins to apply filter (warn origin if not in this list).
        skip_origins: Chunk origins within origin to skip.
    """

    # Group recent user changes by file ID for faster lookup
    grouped_replacement_text: dict[str, list[ReplacementText]] = {}
    for recent_change in recency_info.recent_changes:
        recently_changed_blob = recent_change.blob_name
        if recently_changed_blob is None:
            continue
        if recent_change.present_in_blob:  # already indexed. chunk will be up to date.
            continue
        if recently_changed_blob not in grouped_replacement_text:
            grouped_replacement_text[recently_changed_blob] = []
        grouped_replacement_text[recently_changed_blob].append(recent_change)

    # Iterate through possibly outdated code snippets
    for retrieved_chunk in chunks:
        if retrieved_chunk.origin in skip_origins:
            _recency_overlap_kept_counter.inc()
            yield retrieved_chunk
            continue

        if retrieved_chunk.origin not in origins:
            logger.warning("Unknown chunk origin: %s", retrieved_chunk.origin)
            _recency_overlap_kept_counter.inc()
            yield retrieved_chunk
            continue

        overlap: OverlapChunk | None = None
        retrieved_blob_name = retrieved_chunk.blob_name
        if retrieved_blob_name in grouped_replacement_text:
            overlap = find_first_overlap(
                retrieved_chunk=retrieved_chunk,
                overlap_chunks=grouped_replacement_text[retrieved_blob_name],
                predicate=partial_overlap_predicate,
            )

        if overlap is None:
            _recency_overlap_kept_counter.inc()
            yield retrieved_chunk
            continue
        else:
            _recency_overlap_filter_counter.inc()
