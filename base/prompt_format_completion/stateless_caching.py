"""Classes and functions for stateless caching."""

import math
from dataclasses import dataclass
from typing import Optional

from dataclasses_json import dataclass_json

from base.ranges import TokenRange


@dataclass_json
@dataclass
class StatelessCachingConfig:
    """The stateless caching configuration."""

    # maximal size of the nearby prefix in tokens
    nearby_prefix_token_len: int = 0

    # size of the overlap between nearby prefix and far prefix
    nearby_prefix_token_overlap: int = 0

    # maximal size of the nearby suffix in tokens
    nearby_suffix_token_len: int = 0

    # size of the overlap between nearby suffix and far suffix
    nearby_suffix_token_overlap: int = 0

    # Quantize the near/far prefix and suffix to multiple of this number of tokens from beginning/end of file.
    quantize_token_len: int = 0

    # Quantize the prefix to a multiple of this number of characters in case we do not possess the entire prefix
    quantize_char_len: int = 0

    def __post_init__(self):
        assert self.nearby_prefix_token_len >= 0
        assert self.nearby_prefix_token_overlap >= 0
        assert self.nearby_suffix_token_len >= 0
        assert self.nearby_suffix_token_overlap >= 0
        assert self.quantize_token_len >= 0
        assert self.quantize_char_len >= 0


def quantize_by_char_len(
    prefix_chars: str, prefix_char_offset: int, quantize_char_len: int
) -> str:
    """Quantize prefix to a multiple of quantize_char_len."""

    if quantize_char_len <= 0:
        return prefix_chars

    prefix_start_char_pos_in_file = prefix_char_offset
    prefix_start_char_pos_in_file = (
        math.ceil(prefix_start_char_pos_in_file / quantize_char_len) * quantize_char_len
    )
    prefix_start_char_pos = prefix_start_char_pos_in_file - prefix_char_offset
    prefix_chars = prefix_chars[prefix_start_char_pos:]
    return prefix_chars


def segment_prefix_stateless(
    raw_prefix_token_len: int,
    max_prompt_prefix_tokens: int,
    nearby_prefix_token_len: int,
    nearby_prefix_token_overlap: int,
    quantize_token_len: int,
) -> tuple[TokenRange, Optional[TokenRange]]:
    """Segment prefix into prefix and nearby prefix, truncate, and quantize.

    Args:
        raw_prefix_token_len: The length of the prefix in tokens.
        max_prompt_prefix_tokens: The maximum number of prefix tokens in the prompt including
            prefix and nearby prefix.
        nearby_prefix_token_len: The max length of the nearby prefix in tokens.
        nearby_prefix_token_overlap: The number of tokens in nearby prefix that are also in far prefix.

    Returns:
        The token ranges of the prefix and nearby prefix.
    """

    prefix_end_pos = raw_prefix_token_len
    nearby_prefix_range = None
    far_prefix_budget = max_prompt_prefix_tokens

    if nearby_prefix_token_len > 0:
        nearby_prefix_start_pos = max(
            raw_prefix_token_len - nearby_prefix_token_len,
            raw_prefix_token_len
            - max_prompt_prefix_tokens,  # nearby prefix can't be longer than max total prefix
            0,  # nearby prefix can't be negative
        )

        # Quantize nearby prefix start position to enable caching
        if quantize_token_len > 0:
            # Our nearby prefix section size should be a multiple of quantizaton
            # size to ensure nearby and far prefix update at the same position
            assert nearby_prefix_token_len % quantize_token_len == 0
            nearby_prefix_start_pos = (
                math.ceil(nearby_prefix_start_pos / quantize_token_len)
                * quantize_token_len
            )

        nearby_prefix_end_pos = raw_prefix_token_len
        nearby_prefix_range = TokenRange(
            start=nearby_prefix_start_pos, stop=nearby_prefix_end_pos
        )
        far_prefix_budget -= max(nearby_prefix_end_pos - nearby_prefix_start_pos, 0)

        prefix_end_pos = min(
            prefix_end_pos,
            nearby_prefix_start_pos + nearby_prefix_token_overlap,
        )

    prefix_start_pos = max(prefix_end_pos - far_prefix_budget, 0)
    if quantize_token_len > 0:
        prefix_start_pos = (
            math.ceil(prefix_start_pos / quantize_token_len) * quantize_token_len
        )

    prefix_range = TokenRange(start=prefix_start_pos, stop=prefix_end_pos)

    return (
        prefix_range,
        nearby_prefix_range,
    )


def segment_suffix_stateless(
    raw_suffix_token_len: int,
    max_prompt_suffix_tokens: int,
    nearby_suffix_token_len: int,
    nearby_suffix_token_overlap: int,
    quantize_token_len: int,
) -> tuple[TokenRange, Optional[TokenRange]]:
    """Segment suffix into suffix and nearby suffix, truncate, and quantize.

    Args:
        raw_suffix_token_len: The length of the raw suffix in tokens.
        max_prompt_suffix_tokens: The maximum number of suffix tokens in the prompt including
            suffix and nearby suffix.
        nearby_suffix_token_len: The max length of the nearby suffix in tokens.
        nearby_suffix_token_overlap: The number of tokens in nearby suffix that are also in far suffix.

    Returns:
        The token ranges of the suffix and nearby suffix.
    """

    suffix_start_pos = 0

    nearby_suffix_range = None
    far_suffix_budget = max_prompt_suffix_tokens
    if nearby_suffix_token_len > 0:
        nearby_suffix_end_pos = min(
            nearby_suffix_token_len,
            raw_suffix_token_len,  # nearby suffix can't be longer than raw suffix
            max_prompt_suffix_tokens,  # nearby suffix can't be longer than max prompt suffix
        )

        # Quantize nearby prefix start position to enable caching
        if quantize_token_len > 0:
            # Our nearby prefix section size should be a multiple of quantizaton
            # size to ensure nearby and far prefix update at the same position
            assert nearby_suffix_token_len % quantize_token_len == 0
            nearby_dist_from_end = raw_suffix_token_len - nearby_suffix_end_pos
            nearby_dist_from_end = (
                math.ceil(nearby_dist_from_end / quantize_token_len)
                * quantize_token_len
            )
            nearby_suffix_end_pos = max(raw_suffix_token_len - nearby_dist_from_end, 0)

        nearby_suffix_range = TokenRange(
            start=suffix_start_pos, stop=nearby_suffix_end_pos
        )
        far_suffix_budget -= nearby_suffix_end_pos - suffix_start_pos

        suffix_start_pos = max(
            suffix_start_pos,
            nearby_suffix_end_pos - nearby_suffix_token_overlap,
        )

    suffix_end_pos = min(suffix_start_pos + far_suffix_budget, raw_suffix_token_len)

    if quantize_token_len > 0:
        dist_from_end = raw_suffix_token_len - suffix_end_pos
        dist_from_end = (
            math.ceil(dist_from_end / quantize_token_len) * quantize_token_len
        )
        suffix_end_pos = max(raw_suffix_token_len - dist_from_end, 0)

    suffix_range = TokenRange(start=suffix_start_pos, stop=suffix_end_pos)

    return (
        suffix_range,
        nearby_suffix_range,
    )
