load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "prompt_format_router",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":pleasehold_prompt_formatter",
    ],
)

py_library(
    name = "pleasehold_prompt_formatter",
    srcs = [
        "pleasehold_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:structured_binks_prompt_formatter",
        "//base/prompt_format_chat:tokenized_qwen_prompt_formatter",
        "//base/prompt_format_chat/lib:string_formatter",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/tokenizers:qwen25coder_tokenizer",
    ],
)

pytest_test(
    name = "pleasehold_prompt_formatter_test",
    srcs = ["pleasehold_prompt_formatter_test.py"],
    deps = [
        ":pleasehold_prompt_formatter",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers:qwen25coder_tokenizer",
    ],
)
