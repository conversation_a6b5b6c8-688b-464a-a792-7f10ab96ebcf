"""The module formats tokenized prompts for PleaseHold chat models."""

import copy
import dataclasses
from typing import Optional

from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.prompt_format_chat.lib.string_formatter import StringFormatter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    TokenizedChatPromptFormatter,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.tokenized_qwen_prompt_formatter import (
    StructToTokensQwenPromptFormatter,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer

BUFFER_SIZE_FOR_SPEC_TOKENS = 100

SYSTEM_PROMPT_TEMPLATE = """\
Your task is to analyze the last message in the conversation and classify it along multiple dimensions.
Here are the external libraries available: {docsets}.

1. Is it a general programming question, edit request, question about current file, codebase, or overview request?
2. Does the query require information from specific files?
3. Is the query about an external library from the list above?

Output exactly three lines:

Line 1: "0" for general question, "1" for edit request, "2" for question about current file, "3" for codebase question, "4" for overview question.
Line 2: relevant file(s) or blank if none.
Line 3: library name from the list if applicable, otherwise "none"."""


def get_pleasehold_system_prompt(token_counter, docsets: list[str]) -> StringFormatter:
    """Get the system prompt formatter for PleaseHold."""
    return StringFormatter(
        SYSTEM_PROMPT_TEMPLATE.format(docsets=",".join(docsets)),
        token_counter=token_counter,
    )


class PleaseHoldPromptFormatter(TokenizedChatPromptFormatter):
    """The class formats tokenized prompts for the PleaseHold chat model.

    For prompt structure of the Qwen models, see:
    https://huggingface.co/Qwen/Qwen2.5-Coder-32B-Instruct

    Under the hood, it uses base/prompt_format_chat/structured_binks_prompt_formatter.py
    to format the prompt, and then tokenizes it, adding special token delimiters where needed.

    Here is how we tokenize the prompt:

    <|im_start|>system
    [insert system prompt]<|im_end|>
    [for conversation turn:]
        <|im_start|>user
        [insert user message]<|im_end|>
        <|im_start|>assistant
        [insert assistant message]<|im_end|>
    <|im_start|>user
    [insert user message]<|im_end|>
    <|im_start|>assistant
    """

    def __init__(
        self,
        tokenizer: Qwen25CoderTokenizer,
        token_apportionment: Optional[ChatTokenApportionment] = None,
        docsets: Optional[list[str]] = None,
    ):
        self.tokenizer = tokenizer
        self.token_counter = TokenizerBasedTokenCounter(tokenizer)
        self.docsets = docsets if docsets is not None else []
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = ChatTokenApportionment(
                path_len=256,
                prefix_len=1024 * 2,
                chat_history_len=1024 * 4,
                suffix_len=1024 * 2,
                retrieval_len=-1,  # unlimited retrieval
                max_prompt_len=12288,  # 12k for prompt
                # Fields unused by default
                retrieval_len_per_each_user_guided_file=0,
                retrieval_len_for_user_guided=0,
                recent_changes_len=0,
                # Deprecated fields
                message_len=-1,
                selected_code_len=-1,
            )

        assert (
            token_apportionment.message_len == -1
        ), "The message length should be -1 because it is a deprecated field not used in this formatter."
        assert (
            token_apportionment.selected_code_len == -1
        ), "The selected code length should be -1 because it is a deprecated field not used in this formatter."

        self.tokenized_prompt_formatter = StructToTokensQwenPromptFormatter(tokenizer)
        # Add a buffer of BUFFER_SIZE_FOR_SPEC_TOKENS tokens for spec
        # tokens. It's okay if we go a little over it.
        structured_token_apportionment = copy.deepcopy(token_apportionment)
        structured_token_apportionment = dataclasses.replace(
            structured_token_apportionment,
            max_prompt_len=token_apportionment.max_prompt_len
            - self.tokenized_prompt_formatter.reserved_token_budget,
        )
        # Required for the interface even if we don't use it.
        self.token_apportionment = token_apportionment

        docsets = [docset.replace("docset://", "") for docset in self.docsets]
        self.structured_prompt_formatter = StructuredBinksPromptFormatter.create(
            token_counter=self.token_counter,
            token_apportionment=token_apportionment,
            system_prompt_factory=lambda tc: get_pleasehold_system_prompt(tc, docsets),
            retrieval_section_version=2,
        )

    def format_prompt(self, prompt_input: ChatPromptInput) -> TokenizedChatPromptOutput:
        """Format prompt for PleaseHold chat model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """
        structured_prompt = self.structured_prompt_formatter.format_prompt(prompt_input)
        return self.tokenized_prompt_formatter.format_prompt(structured_prompt)
