"""Tiktoken-based tokenizer for StarCoder2."""

import json
import pathlib
from pathlib import Path
from types import MappingProxyType
from typing import Mapping, Sequence, Tuple

from base.tokenizers import starcoder_common
from base.tokenizers.core_bpe import CoreBPE
from base.tokenizers.tokenizer import (
    REGISTRY,
    NextEditGenSpecialTokens,
    Tokenizer,
)


def _read_special(special_token_path: Path) -> list[Tuple[str, int]]:
    with special_token_path.open(encoding="utf-8") as token_file:
        tokens = json.load(token_file)
    return tokens


class StarCoder2SpecialTokens(NextEditGenSpecialTokens):
    """Special tokens for the RAG finetuned StarCoder2 model."""

    def __init__(self, tokenizer: Tokenizer):
        super().__init__()
        vocab = tokenizer.vocab

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<pr_base>"]
        self.ret_start: int = vocab[b"<pr_file>"]
        self.ret_body: int = vocab[b"<pr_base_code>"]
        self.prefix_body: int = vocab[b"<|prefix-body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig-lookup|>"]
        self.sig_begin: int = vocab[b"<|sig-begin|>"]
        self.sig_end: int = vocab[b"<|sig-end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]

        # StarCoder2 Special Tokens
        self.padding: int = vocab[b"<|padding|>"]
        self.eos: int = vocab[b"<|endoftext|>"]
        self.fim_prefix: int = vocab[b"<fim_prefix>"]
        self.fim_middle: int = vocab[b"<fim_middle>"]
        self.fim_suffix: int = vocab[b"<fim_suffix>"]
        self.fim_pad: int = vocab[b"<fim_pad>"]
        self.reponame: int = vocab[b"<repo_name>"]
        self.filename: int = vocab[b"<file_sep>"]
        self.issue_start: int = vocab[b"<issue_start>"]
        self.issue_comment: int = vocab[b"<issue_comment>"]
        self.issue_closed: int = vocab[b"<issue_closed>"]
        self.jupyter_start: int = vocab[b"<jupyter_start>"]
        self.jupyter_text: int = vocab[b"<jupyter_text>"]
        self.jupyter_code: int = vocab[b"<jupyter_code>"]
        self.jupyter_output: int = vocab[b"<jupyter_output>"]
        self.jupyter_script: int = vocab[b"<jupyter_script>"]
        self.empty_output: int = vocab[b"<empty_output>"]
        self.code_to_intermediate: int = vocab[b"<code_to_intermediate>"]
        self.intermediate_to_code: int = vocab[b"<intermediate_to_code>"]
        self.pr: int = vocab[b"<pr>"]
        self.pr_status: int = vocab[b"<pr_status>"]
        self.pr_is_merged: int = vocab[b"<pr_is_merged>"]
        self.pr_base: int = vocab[b"<pr_base>"]
        self.pr_file: int = vocab[b"<pr_file>"]
        self.pr_base_code: int = vocab[b"<pr_base_code>"]
        self.pr_diff: int = vocab[b"<pr_diff>"]
        self.pr_diff_hunk: int = vocab[b"<pr_diff_hunk>"]
        self.pr_comment: int = vocab[b"<pr_comment>"]
        self.pr_event_id: int = vocab[b"<pr_event_id>"]
        self.pr_review: int = vocab[b"<pr_review>"]
        self.pr_review_state: int = vocab[b"<pr_review_state>"]
        self.pr_review_comment: int = vocab[b"<pr_review_comment>"]
        self.pr_in_reply_to_review_id: int = vocab[b"<pr_in_reply_to_review_id>"]
        self.pr_in_reply_to_comment_id: int = vocab[b"<pr_in_reply_to_comment_id>"]
        self.pr_diff_hunk_comment_line: int = vocab[b"<pr_diff_hunk_comment_line>"]
        self.name: int = vocab[b"<NAME>"]
        self.email: int = vocab[b"<EMAIL>"]
        self.key: int = vocab[b"<KEY>"]
        self.password: int = vocab[b"<PASSWORD>"]
        self.newline: int = vocab[b"\n"]

        # Next Edit Tokens
        self.instruction: int = self.pr
        self.selected_code: int = vocab[b"<|selected_code|>"]
        self.diff_section: int = self.pr_diff
        self.diff_hunk: int = vocab[b"<|diff_hunk|>"]
        self.has_change: int = vocab[b"<|has_change|>"]
        self.no_change: int = vocab[b"<|no_change|>"]

        # Preference Token and Reward Token
        self.good: int = vocab[b"<|good_example|>"]
        self.bad: int = vocab[b"<|bad_example|>"]
        self.reward_signal: int = vocab[b"<|reward-signal|>"]

        self.begin_sequence = ()


class StarCoder2Tokenizer(Tokenizer):
    """Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    _vocab: dict[bytes, int]

    def __init__(self):
        """Initialize the tokenizer from the JSON vocab file."""
        vocab = starcoder_common.read_vocab("starcoder2_vocab.json")

        special_token_file = (
            pathlib.Path(__file__).parent / "starcoder2_special_tokens.json"
        )
        special_tokens_factory = StarCoder2SpecialTokens

        special_tokens = dict(_read_special(special_token_file))
        vocab.update_special(special_tokens)

        self.core_bpe = CoreBPE(
            vocab.normal_encode_map,
            vocab.special_encode_map,
            starcoder_common.GPT2_PAT_STR,
        )

        self._vocab = dict(vocab.normal_encode_map)
        self._vocab.update({k.encode(): v for k, v in vocab.special_encode_map.items()})

        self._vocab_size = vocab.max_token_id + 1

        self._special_set = set(vocab.special_encode_map.keys())
        self._special_tokens = special_tokens_factory(self)

    @property
    def vocab_size(self):
        return self._vocab_size

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return MappingProxyType(self._vocab)

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=set())

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self._special_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> StarCoder2SpecialTokens:
        """Returns the special tokens for starcoder."""
        return self._special_tokens


REGISTRY.add("starcoder2", StarCoder2Tokenizer)
