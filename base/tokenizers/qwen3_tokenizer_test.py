"""Tests for the Qwen3 tokenizer."""

from base.tokenizers.qwen3_tokenizer import Qwen3Tokenizer
from base.tokenizers.tokenizer import R<PERSON><PERSON>TR<PERSON>


def test_qwen3_tokenizer_basic():
    """Test basic functionality of the Qwen3 tokenizer."""
    tokenizer = Qwen3Tokenizer()

    # Test basic tokenization
    tokens = tokenizer.tokenize_unsafe("Hello, world!")
    assert len(tokens) > 0

    # Test detokenization
    text = tokenizer.detokenize(tokens)
    assert text == "Hello, world!"

    # Test special tokens
    special_tokens = tokenizer.special_tokens
    assert special_tokens.eos is not None
    assert special_tokens.im_start is not None
    assert special_tokens.im_end is not None

    # Test new Qwen3-specific tokens
    assert special_tokens.tool_call is not None
    assert special_tokens.tool_call_end is not None
    assert special_tokens.tool_response is not None
    assert special_tokens.tool_response_end is not None
    assert special_tokens.think is not None
    assert special_tokens.think_end is not None


def test_qwen3_tokenizer_chat_format():
    """Test chat format tokenization for Qwen3."""
    tokenizer = Qwen3Tokenizer()

    # Test chat format with system, user, and assistant messages
    chat_text = "<|im_start|>system\nYou are <PERSON>wen<PERSON>, a helpful AI assistant.<|im_end|>\n<|im_start|>user\nHello, how are you?<|im_end|>\n<|im_start|>assistant\nI'm doing well, thank you for asking!<|im_end|>"

    tokens = tokenizer.tokenize_unsafe(chat_text)
    assert len(tokens) > 0

    # Test detokenization of chat format
    decoded_text = tokenizer.detokenize(tokens)
    assert decoded_text == chat_text


def test_qwen3_tokenizer_code():
    """Test code tokenization for Qwen3."""
    tokenizer = Qwen3Tokenizer()

    # Test Python code tokenization
    code = """def hello_world():
    print("Hello, world!")
    return 0
"""

    tokens = tokenizer.tokenize_unsafe(code)
    assert len(tokens) > 0

    # Test detokenization of code
    decoded_code = tokenizer.detokenize(tokens)
    assert decoded_code == code


def test_qwen3_tokenizer_thinking():
    """Test thinking format tokenization for Qwen3."""
    tokenizer = Qwen3Tokenizer()

    # Test thinking format
    thinking_text = "<|im_start|>assistant\n<think>\nLet me think about this problem step by step.\n1. First, I need to understand the question.\n2. Then, I'll formulate an answer.\n</think>\n\nBased on my analysis, the answer is 42.<|im_end|>"

    tokens = tokenizer.tokenize_unsafe(thinking_text)
    assert len(tokens) > 0

    # Test detokenization of thinking format
    decoded_text = tokenizer.detokenize(tokens)

    # Check that the key parts are present, even if the exact format might differ
    assert "<|im_start|>assistant" in decoded_text
    assert "Let me think about this problem step by step" in decoded_text
    assert "First, I need to understand the question" in decoded_text
    assert "Then, I'll formulate an answer" in decoded_text
    assert "Based on my analysis, the answer is 42" in decoded_text
    assert "<|im_end|>" in decoded_text


def test_qwen3_tokenizer_tool_calls():
    """Test tool calls format tokenization for Qwen3."""
    tokenizer = Qwen3Tokenizer()

    # Test tool calls format
    tool_call_text = '<|im_start|>assistant\nTo answer your question, I need to search for some information.\n<tool_call>\n{"name": "web_search", "arguments": {"query": "current weather in San Francisco"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{"temperature": 68, "condition": "Partly Cloudy"}\n</tool_response><|im_end|>'

    tokens = tokenizer.tokenize_unsafe(tool_call_text)
    assert len(tokens) > 0

    # Test detokenization of tool calls format
    decoded_text = tokenizer.detokenize(tokens)

    # Check that the key parts are present, even if the exact format might differ
    assert "<|im_start|>assistant" in decoded_text
    assert (
        "To answer your question, I need to search for some information" in decoded_text
    )
    assert "<tool_call>" in decoded_text
    assert '"name": "web_search"' in decoded_text
    assert '"query": "current weather in San Francisco"' in decoded_text
    assert "<|im_start|>user" in decoded_text
    assert "<tool_response>" in decoded_text
    assert '"temperature": 68' in decoded_text
    assert '"condition": "Partly Cloudy"' in decoded_text
    assert "<|im_end|>" in decoded_text


def test_qwen3_tokenizer_registry():
    """Test that the Qwen3 tokenizer is properly registered."""
    # Check that the tokenizer is registered with the name "qwen3"
    assert "qwen3" in dict(REGISTRY)

    # Get the tokenizer from the registry
    tokenizer_class = REGISTRY.get("qwen3")
    assert tokenizer_class is not None

    # Create an instance of the tokenizer
    tokenizer = tokenizer_class()
    assert isinstance(tokenizer, Qwen3Tokenizer)
