"""Tests for DeepSeek-Coder-Instruct tokenizer."""

import pytest

from base.tokenizers.deepseek_tokenizer import (
    DeepSeekCoderBaseTokenizer,
    DeepSeekCoderInstructTokenizer,
    DeepSeekCoderSpecialTokens,
)

TOKENIZER_LIST = [DeepSeekCoderInstructTokenizer, DeepSeekCoderBaseTokenizer]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_basic(tokenizer_cls):
    """Sanity test for DeepSeek-Coder-Instruct tokenizer."""
    t = tokenizer_cls()
    text = "void quicksort(std::vector<int> & i)"
    tokens = t.tokenize_unsafe(text)
    assert tokens == [
        4563,  # void
        445,  # _qu
        6388,  # icks
        439,  # ort
        7,  # (
        8387,  # std
        1161,  # ::
        10930,  # vector
        27,  # <
        569,  # int
        29,  # >
        573,  # _&
        460,  # _i
        8,  # )
    ]
    actual_text = t.detokenize(tokens)
    assert actual_text == text
    # Test the vocab size
    assert t.vocab_size == 32047


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_the_unsafe_call(tokenizer_cls):
    """Test the tokenize_unsafe function."""
    t = tokenizer_cls()
    # Test the build-in special tokens from pre-trained DeepSeek-Coder
    assert t.tokenize_unsafe("õ") == [32000]
    assert t.tokenize_unsafe("÷") == [32001]
    assert t.tokenize_unsafe("<｜end▁of▁sentence｜>") == [32014]
    assert t.tokenize_unsafe("<｜fim▁hole｜>") == [32015]
    assert t.tokenize_unsafe("<｜fim▁begin｜>") == [32016]
    assert t.tokenize_unsafe("<｜fim▁end｜>") == [32017]
    assert t.tokenize_unsafe("<pad>") == [32018]
    assert t.tokenize_unsafe("<|User|>") == [32019]
    assert t.tokenize_unsafe("<|Assistant|>") == [32020]
    assert t.tokenize_unsafe("<|EOT|>") == [32021]
    # Test the in-house added special tokens
    assert t.tokenize_unsafe("<|skip|>") == [32022]
    assert t.tokenize_unsafe("<filename>") == [32034]
    assert t.tokenize_unsafe("<|far_suffix|>") == [32036]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_the_safe_call(tokenizer_cls):
    """Test the tokenize_safe function."""
    t = tokenizer_cls()
    # Test the build-in special tokens from pre-trained DeepSeek-Coder
    assert t.tokenize_safe("õ") == [125, 113]
    assert t.tokenize_safe("÷") == [125, 115]
    assert t.tokenize_safe("<｜end▁of▁sentence｜>") == [
        27,
        169,
        121,
        237,
        408,
        11028,
        210,
        990,
        11028,
        210,
        18119,
        720,
        169,
        121,
        237,
        29,
    ]
    assert t.tokenize_safe("<｜fim▁hole｜>") == [
        27,
        169,
        121,
        237,
        69,
        308,
        11028,
        210,
        19496,
        169,
        121,
        237,
        29,
    ]
    assert t.tokenize_safe("<｜fim▁begin｜>") == [
        27,
        169,
        121,
        237,
        69,
        308,
        11028,
        210,
        946,
        169,
        121,
        237,
        29,
    ]
    assert t.tokenize_safe("<｜fim▁end｜>") == [
        27,
        169,
        121,
        237,
        69,
        308,
        11028,
        210,
        408,
        169,
        121,
        237,
        29,
    ]
    assert t.tokenize_safe("<pad>") == [27, 8836, 29]
    assert t.tokenize_safe("<|User|>") == [27, 91, 5719, 91, 29]
    assert t.tokenize_safe("<|Assistant|>") == [27, 91, 5618, 15481, 91, 29]
    assert t.tokenize_safe("<|EOT|>") == [27, 91, 36, 2778, 91, 29]
    # Test the in-house added special tokens
    assert t.tokenize_safe("<|skip|>") == [27, 91, 7465, 91, 29]
    assert t.tokenize_safe("<|far_suffix|>") == [27, 91, 18601, 62, 18374, 681, 91, 29]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_normal_cases(tokenizer_cls):
    """Test cases, where tokenize_unsafe and tokenize_safe lead to the same results."""
    t = tokenizer_cls()
    f1, f2 = t.tokenize_safe, t.tokenize_unsafe
    # Test some random cases
    assert f1("99000") == f2("99000") == [24, 24, 15, 15, 15]
    assert f1(" ") == f2(" ") == [207]
    assert f1("  ") == f2("  ") == [243]
    assert f1("   ") == f2("   ") == [315]
    assert f1("\t") == f2("\t") == [184]
    assert f1("\n") == f2("\n") == [185]
    assert f1("0 0") == f2("0 0") == [15, 207, 15]
    assert t.detokenize(f1("0 0")) == t.detokenize(f2("0 0")) == "0 0"
    assert f1("I love you") == f2("I love you") == [40, 2113, 340]
    assert f1("  print()") == f2("  print()") == [207, 3628, 822]
    assert f1("      print()") == f2("      print()") == [730, 3628, 822]
    assert f1("\tprint()") == f2("\tprint()") == [184, 4128, 822]
    assert f1("\n") + f1("Prefix:\n```\n") == f1("\nPrefix:\n```\n")
    assert f1("\n```\n\n") + f1("Suffix:\n```\n") == f1("\n```\n\nSuffix:\n```\n")
    assert f1("\n```\n\n") + f1("Selected Code:\n```\n") == f1(
        "\n```\n\nSelected Code:\n```\n"
    )


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_reverse_generation(tokenizer_cls):
    """Test the reverse generation."""
    t = tokenizer_cls()
    token_ids = [5009, 12794, 25591, 1601, 2864, 7972]
    for token in token_ids:
        assert t.tokenize_safe(t.detokenize([token])) == [token]
    texts = ["I love snowboarding", "0 0 0 0 0 1 2", "153289"]
    for text in texts:
        assert t.detokenize(t.tokenize_safe(text)) == text
        assert t.detokenize(t.tokenize_unsafe(text)) == text


def test_special_tokens_instruct():
    """Test special tokens API for Instruct tokenizer."""
    t = DeepSeekCoderInstructTokenizer()
    specials = t.special_tokens
    assert isinstance(specials, DeepSeekCoderSpecialTokens)
    assert specials.begin_sequence == (32013,)
    assert specials.eos == 32021


def test_special_tokens_base():
    """Test special tokens API for Base tokenizer."""
    t = DeepSeekCoderBaseTokenizer()
    specials = t.special_tokens
    assert isinstance(specials, DeepSeekCoderSpecialTokens)
    assert specials.begin_sequence == (32013,)
    assert specials.eos == 32014
