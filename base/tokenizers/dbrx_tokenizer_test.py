"""Tests for DBRX-Instruct tokenizer."""

import pytest

from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer

TOKENIZER_LIST = [DBRXInstructTokenizer]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_basic(tokenizer_cls):
    """Sanity test for DBRX-Instruct tokenizer."""
    t = tokenizer_cls()
    pair_of_text_and_tokens = [
        ("how are you", [5269, 527, 499]),
        ("董宣毅", [164, 239, 96, 8676, 96, 31075, 227]),
        (
            "void quicksort(std::vector<int> & i)",
            [1019, 4062, 7003, 5305, 487, 3295, 4252, 29, 612, 602, 8],
        ),
        (
            "if a == 1:\n    print(a)\nelse:\n\t\tprint(a)\n",
            [
                333,
                264,
                624,
                220,
                16,
                512,
                262,
                1194,
                2948,
                340,
                1531,
                512,
                197,
                7045,
                2948,
                340,
            ],
        ),
        (
            """\
def foo():
    if a == 1:
        print(a, arg ="foo")
    return a
""",
            [
                755,
                15586,
                4019,
                262,
                422,
                264,
                624,
                220,
                16,
                512,
                286,
                1194,
                2948,
                11,
                1417,
                17689,
                8134,
                1158,
                262,
                471,
                264,
                198,
            ],
        ),
        ("1", [16]),
        ("11", [806]),
        ("111", [5037]),
        ("1111", [5037, 16]),
        ("11111111111", [5037, 5037, 5037, 806]),
    ]
    for text, expected_tokens in pair_of_text_and_tokens:
        tokens = t.tokenize_safe(text)
        assert tokens == expected_tokens
        actual_text = t.detokenize(tokens)
        assert actual_text == text
    # Test the vocab size
    assert t.vocab_size == 100280 + 15


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_the_unsafe_call(tokenizer_cls):
    """Test the tokenize_unsafe function."""
    t = tokenizer_cls()
    # Test the build-in special tokens from pre-trained DBRX-Coder
    assert t.tokenize_unsafe("<|fim_prefix|>") == [100258]
    assert t.tokenize_unsafe("<|fim_middle|>") == [100259]
    # Test the in-house added special tokens
    assert t.tokenize_unsafe("<|skip|>") == [100280]
    assert t.tokenize_unsafe("<filename>") == [100292]
    assert t.tokenize_unsafe("<|far_prefix|>") == [100293]
    assert t.tokenize_unsafe("<|far_suffix|>") == [100294]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_the_safe_call(tokenizer_cls):
    """Test the tokenize_safe function."""
    t = tokenizer_cls()
    # Test the build-in special tokens from DBRX
    assert t.tokenize_safe("<|endoftext|>") == [27, 91, 8862, 728, 428, 91, 29]
    # Test the in-house added special tokens
    assert t.tokenize_safe("<|skip|>") == [27, 91, 21247, 91, 29]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_normal_cases(tokenizer_cls):
    """Test cases, where tokenize_unsafe and tokenize_safe lead to the same results."""
    t = tokenizer_cls()
    f1, f2 = t.tokenize_safe, t.tokenize_unsafe
    # Test some random cases
    assert f1("99000") == f2("99000") == [19146, 410]
    assert f1(" ") == f2(" ") == [220]
    assert f1("0 0") == f2("0 0") == [15, 220, 15]
    assert t.detokenize(f1("0 0")) == t.detokenize(f2("0 0")) == "0 0"
    assert f1("\n") + f1("Prefix:\n```\n") == f1("\nPrefix:\n```\n")
    assert f1("\n```\n\n") + f1("Suffix:\n```\n") == f1("\n```\n\nSuffix:\n```\n")
    assert f1("\n```\n\n") + f1("Selected Code:\n```\n") == f1(
        "\n```\n\nSelected Code:\n```\n"
    )


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_reverse_generation(tokenizer_cls):
    """Test the reverse generation."""
    t = tokenizer_cls()
    token_ids = [5009, 12794, 25591, 1601, 2864, 7972]
    for token in token_ids:
        assert t.tokenize_safe(t.detokenize([token])) == [token]
    texts = ["I love snowboarding", "0 0 0 0 0 1 2", "153289"]
    for text in texts:
        assert t.detokenize(t.tokenize_safe(text)) == text
        assert t.detokenize(t.tokenize_unsafe(text)) == text
