"""Module containing a tiktoken-based tokenizer implementation using the codegen vocabulary."""

import json
import pathlib
from typing import Mapping, Tuple, Sequence

from base.tokenizers import data_gym, tokenizer
from base.tokenizers.core_bpe import CoreBPE


def _read_vocab() -> Mapping[bytes, int]:
    rank_to_intbyte = [b for b in range(2**8) if chr(b).isprintable() and chr(b) != " "]

    data_gym_byte_to_byte = {chr(b): b for b in rank_to_intbyte}
    n = 0
    for b in range(2**8):
        if b not in rank_to_intbyte:
            rank_to_intbyte.append(b)
            data_gym_byte_to_byte[chr(2**8 + n)] = b
            n += 1
    assert len(rank_to_intbyte) == 2**8

    decode_data_gym = data_gym.DataGymDecoder().decode

    # gpt2-merges.txt contains the merges
    module_dir = pathlib.Path(__file__).parent
    vocab_bpe_contents = pathlib.Path(module_dir, "gpt2-merges.txt").read_text(
        encoding="utf-8"
    )
    bpe_merges = [
        tuple(merge_str.split()) for merge_str in vocab_bpe_contents.split("\n")[1:-1]
    ]

    # add the single byte tokens
    bpe_ranks = {bytes([b]): i for i, b in enumerate(rank_to_intbyte)}
    # add the merged tokens
    n = len(bpe_ranks)
    for first, second in bpe_merges:
        bpe_ranks[decode_data_gym(first) + decode_data_gym(second)] = n
        n += 1

    return bpe_ranks


def _read_special() -> list[Tuple[str, int]]:
    module_dir = pathlib.Path(__file__).parent
    with pathlib.Path(module_dir, "codegen_special_tokens.json").open(
        encoding="utf-8"
    ) as token_file:
        tokens = json.load(token_file)
    return tokens


# Source: https://github.com/openai/tiktoken/pull/239
# The pattern in the original GPT-2 release is:
# r""'s|'t|'re|'ve|'m|'ll|'d| ?[\p{L}]+| ?[\p{N}]+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+"""
# This is equivalent, but executes faster:
GPT2_PAT_STR = r"""'(?:[sdmt]|ll|ve|re)| ?\p{L}++| ?\p{N}++| ?[^\s\p{L}\p{N}]++|\s++$|\s+(?!\S)|\s"""

# end of text
END_OF_TEXT = "<|endoftext|>"


class CodeGenSpecialTokens(tokenizer.RetrievalSpecialTokens):
    """Special tokens for the CodeGen model."""

    def __init__(self, tok: tokenizer.Tokenizer):
        super().__init__()
        vocab = tok.vocab
        self.eos: int = vocab[b"<|endoftext|>"]
        self.bos: int = vocab[b"<|endoftext|>"]
        self.fim_sep: int = vocab[b"<|fim-sep|>"]
        self.fim_eos: int = vocab[b"<|fim-eos|>"]
        self.fim_prefix: int = vocab[b"<|fim-prefix|>"]
        self.fim_middle: int = vocab[b"<|fim-mid|>"]
        self.fim_suffix: int = vocab[b"<|fim-suffix|>"]
        self.start_of_key: int = vocab[b"<|startofsequence|>"]
        self.pref_repo_large: int = vocab[b"<|pref-repo-large|>"]
        self.pref_repo_stars_high: int = vocab[b"<|pref-repo-stars-high|>"]
        self.end_of_doc: int = vocab[b"<|ret-endofdoc|>"]
        self.end_of_key: int = vocab[b"<|ret-endofkey|>"]
        self.end_of_query: int = vocab[b"<|ret-endofquery|>"]
        self.padding: int = vocab[b"<|padding|>"]
        self.instruction: int = vocab[b"<|instruction|>"]
        self.selected_code: int = vocab[b"<|selected-code|>"]
        self.begin_sequence: tuple[int, ...] = ()


class TiktokenCodeGenTokenizer(tokenizer.Tokenizer):
    """Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    def __init__(self):
        """Creates a new tokenizers.

        There is a codegen tokenizers on HG. This is using the gpt2 tokenizer
        with modifications as the gptneox code does.

        If used for the first time it will download the tokenizer information from
        HuggingFace.
        """
        mergeable_ranks = _read_vocab()
        whitespaces = [" " * n for n in reversed(range(2, 32))]
        tabs = ["\t" * n for n in reversed(range(2, 10))]
        special_tokens = [END_OF_TEXT] + whitespaces + tabs

        start_index = max(mergeable_ranks.values()) + 1
        special_tokens = {w: start_index + i for i, w in enumerate(special_tokens)}

        # Add user-specified special tokens.
        # We can't assume these are simply appended to the end.
        special_tokens_data = _read_special()
        special_tokens.update({tok[0]: tok[1] for tok in special_tokens_data})

        self.core_bpe = CoreBPE(mergeable_ranks, special_tokens, GPT2_PAT_STR)

        self._vocab = {b: v for (b, v) in mergeable_ranks.items()}
        special_tokens_vocab = list(
            [(t[0].encode(), t[1]) for t in special_tokens.items()]
        )
        self._vocab.update(special_tokens_vocab)

        self.special_tokens_set = set(special_tokens.keys())
        # the whitespace tokens are special tokens from tiktokens perspective, but there are
        # not control tokens we want to filter out. User input should use these tokens.
        self.whitespace_special_token_set = set(
            token for token in special_tokens if not token.startswith("<|")
        )
        self._special_tokens = CodeGenSpecialTokens(self)

    @property
    def vocab_size(self):
        return len(self._vocab)

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return self._vocab

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(
            text, allowed_special=self.whitespace_special_token_set
        )

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self.special_tokens_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        # clean_up_tokenization_spaces=False instructs the tokenizer to
        # not postprocess the detokenized string. By default, the tokenizer
        # will make replacements like " 's" -> "'s".
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> tokenizer.SpecialTokens:
        return self._special_tokens


tokenizer.REGISTRY.add("fim", TiktokenCodeGenTokenizer)
tokenizer.REGISTRY.add("codegen", TiktokenCodeGenTokenizer)
