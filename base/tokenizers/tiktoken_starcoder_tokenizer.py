"""Module containing a tiktoken-based tokenizer implementation using the StarCoder vocabulary."""

import json
import pathlib
from pathlib import Path
from types import MappingProxyType
from typing import Mapping, Sequence, Tuple

from base.tokenizers import starcoder_common
from base.tokenizers.core_bpe import CoreBPE
from base.tokenizers.tokenizer import (
    REGISTRY,
    NextEditGenSpecialTokens,
    RerankerSpecialTokens,
    RetrievalSpecialTokens,
    Tokenizer,
)


def _read_special(special_token_path: Path) -> list[Tuple[str, int]]:
    with special_token_path.open(encoding="utf-8") as token_file:
        tokens = json.load(token_file)
    return tokens


class StarCoderSpecialTokens(
    RerankerSpecialTokens,
    RetrievalSpecialTokens,
    NextEditGenSpecialTokens,
):
    """Special tokens for the RAG finetuned StarCoder model."""

    def __init__(self, tokenizer: Tokenizer):
        super().__init__()
        vocab = tokenizer.vocab

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<|retrieval_section|>"]
        self.ret_start: int = vocab[b"<|ret-start|>"]
        self.ret_body: int = vocab[b"<|ret-body|>"]
        self.prefix_body: int = vocab[b"<|prefix-body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig-lookup|>"]
        self.sig_begin: int = vocab[b"<|sig-begin|>"]
        self.sig_end: int = vocab[b"<|sig-end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]

        # Starcoder Special Tokens
        self.padding: int = vocab[b"<|padding|>"]
        self.eos: int = vocab[b"<|endoftext|>"]
        self.fim_prefix: int = vocab[b"<fim_prefix>"]
        self.fim_middle: int = vocab[b"<fim_middle>"]
        self.fim_suffix: int = vocab[b"<fim_suffix>"]
        self.fim_pad: int = vocab[b"<fim_pad>"]
        self.filename: int = vocab[b"<filename>"]
        self.gh_stars: int = vocab[b"<gh_stars>"]
        self.issue_start: int = vocab[b"<issue_start>"]
        self.issue_comment: int = vocab[b"<issue_comment>"]
        self.issue_closed: int = vocab[b"<issue_closed>"]
        self.jupyter_start: int = vocab[b"<jupyter_start>"]
        self.jupyter_text: int = vocab[b"<jupyter_text>"]
        self.jupyter_code: int = vocab[b"<jupyter_code>"]
        self.jupyter_output: int = vocab[b"<jupyter_output>"]
        self.empty_output: int = vocab[b"<empty_output>"]
        self.commit_before: int = vocab[b"<commit_before>"]
        self.commit_msg: int = vocab[b"<commit_msg>"]
        self.commit_after: int = vocab[b"<commit_after>"]
        self.reponame: int = vocab[b"<reponame>"]
        self.newline: int = vocab[b"\n"]

        # Ethanol Special Tokens for retrieval
        self.start_of_key: int = vocab[b"<|startofsequence|>"]
        self.end_of_query: int = vocab[b"<|ret-endofquery|>"]
        self.end_of_key: int = vocab[b"<|ret-endofkey|>"]

        # Next Edit tokens
        self.instruction: int = vocab[b"<|instruction|>"]
        self.selected_code: int = vocab[b"<|selected_code|>"]
        self.diff_section: int = vocab[b"<|diff_section|>"]
        self.diff_hunk: int = vocab[b"<|diff_hunk|>"]
        self.has_change: int = vocab[b"<|has_change|>"]
        self.no_change: int = vocab[b"<|no_change|>"]

        # Reranker
        self.chunk_prediction: int = vocab[b"<|chunk_prediction|>"]

        self.begin_sequence = ()


class StarCoderTokenizer(Tokenizer):
    """Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    _vocab: dict[bytes, int]

    def __init__(self):
        """Initialize the tokenizer from the JSON vocab file."""
        vocab = starcoder_common.read_vocab("starcoder_vocab.json")

        special_token_file = (
            pathlib.Path(__file__).parent / "starcoder_special_tokens.json"
        )
        special_tokens_factory = StarCoderSpecialTokens

        special_tokens = dict(_read_special(special_token_file))
        vocab.update_special(special_tokens)

        self.core_bpe = CoreBPE(
            vocab.normal_encode_map,
            vocab.special_encode_map,
            starcoder_common.GPT2_PAT_STR,
        )

        self._vocab = dict(vocab.normal_encode_map)
        self._vocab.update({k.encode(): v for k, v in vocab.special_encode_map.items()})

        self._vocab_size = vocab.max_token_id + 1

        self._special_set = set(vocab.special_encode_map.keys())
        self._special_tokens = special_tokens_factory(self)

    @property
    def vocab_size(self):
        return self._vocab_size

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return MappingProxyType(self._vocab)

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=set())

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self._special_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> StarCoderSpecialTokens:
        """Returns the special tokens for starcoder."""
        return self._special_tokens


# the alias below is for backward compatibility
TiktokenStarCoderTokenizer = StarCoderTokenizer

REGISTRY.add("starcoder", StarCoderTokenizer)
