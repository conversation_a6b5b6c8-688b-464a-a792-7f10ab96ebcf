"""Test utilities for tokenizer-related tests."""

from typing import Sequence

from base.tokenizers.tokenizer import Tokenizer, SpecialTokens


class TestSpecialTokens(SpecialTokens):
    """Special tokens implementation for testing."""

    def __init__(self):
        self.padding = -1
        self.eos = -2


class MultiTokenTestTokenizer(Tokenizer):
    """Test tokenizer that handles specific multi-token characters.

    This tokenizer is designed to test multi-token character handling by:
    1. Treating ⌘ (Mac command key) as a two-token character (1000, 1001)
    2. Treating 覉 as a two-token character (2000, 2001)
    3. Handling ASCII characters as single tokens
    """

    TEST_CHARS = {
        "⌘": (1000, 1001),  # Mac command key
        "覉": (2000, 2001),  # Random high unicode character
    }

    def __init__(self):
        self._special_tokens = TestSpecialTokens()
        # Build inverse mapping for detokenization
        self._char_from_tokens = {
            tokens: char for char, tokens in self.TEST_CHARS.items()
        }

    def tokenize_safe(self, text: str) -> list[int]:
        result = []
        for char in text:
            if char in self.TEST_CHARS:
                result.extend(self.TEST_CHARS[char])
            else:
                # Regular ASCII characters
                result.append(ord(char))
        return result

    def tokenize_unsafe(self, text: str) -> list[int]:
        return self.tokenize_safe(text)  # Same for testing

    def detokenize(self, token_ids: Sequence[int]) -> str:
        result = ""
        i = 0
        while i < len(token_ids):
            # Check for multi-token characters
            found_multi_token = False
            for first_token, second_token in self._char_from_tokens.keys():
                if (
                    i + 1 < len(token_ids)
                    and token_ids[i] == first_token
                    and token_ids[i + 1] == second_token
                ):
                    result += self._char_from_tokens[(first_token, second_token)]
                    i += 2
                    found_multi_token = True
                    break

            if not found_multi_token:
                # Handle single tokens
                if token_ids[i] < 128:  # ASCII
                    result += chr(token_ids[i])
                else:
                    result += "\ufffd"  # Replacement char for unknown/incomplete
                i += 1
        return result

    @property
    def special_tokens(self) -> SpecialTokens:
        return self._special_tokens

    @property
    def vocab_size(self) -> int:
        return 128 + len(self.TEST_CHARS) * 2  # ASCII + multi-token chars

    @property
    def vocab(self):
        # Not needed for testing but required by interface
        return {}
