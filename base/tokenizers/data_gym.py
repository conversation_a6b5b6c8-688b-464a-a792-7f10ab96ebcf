"""Data gym decoder."""


# vocab.json is encoded using data gym encoding. What is data gym encoding?
# Why do we need it?
#
# The job of a vocab is to map bytes (not unicode characters) to integers.
# Text files are made up of unicode characters. We need a mapping from bytes
# to unicode characters to represent bytes in simple text files. That's not UTF-8
# UTF-8 is a mapping from unicode characters to bytes. Hence data gym encoding.
#
# Data gym encoding is an encoding of a byte stream as unicode characters.
# Bytes in the 0-255 range corresponding to printable unicode characters are
# mapped to unicode characters 0-255. A byte with value 64 is mapped to unicode
# character 64 (the @ character) and byte 252 is mapped to unicode character 252 (ü).
#
# Bytes in the 0-255 that are whitespace or control sequences or unprintable are
# mapped to unicode characters [256, 256 + 68) (there are about 68 such characters).
# For example, byte with value 0 becomes unicode character 256 and byte with value
# 32 becomes unicode character 288 (Ġ).
class DataGymDecoder:
    """A data-gym decoder from unicode characters to bytes."""

    def __init__(self):
        self._data_gym_decode: dict[str, int] = {}
        remapped = 256
        for byte in range(2**8):
            if chr(byte).isprintable() and byte != 32:
                self._data_gym_decode[chr(byte)] = byte
            else:
                self._data_gym_decode[chr(remapped)] = byte
                remapped += 1

    def decode(self, value: str) -> bytes:
        """Decodes a data-gym encoded string into a bytes object.

        Args:
            value: the data-gym encoded string.

        Returns:
            The decoded bytes.
        """
        try:
            return bytes(self._data_gym_decode[ch] for ch in value)
        except KeyError as exc:
            raise ValueError("Invalid character in input") from exc
