"""Tests for the Qwen-2.5-Coder tokenizer.

bazel test //base/tokenizers:qwen25coder_tokenizer_test
"""

import time

import pytest

from base import feature_flags
from base.tokenizers import core_bpe
from base.tokenizers.qwen25coder_tokenizer import (
    Qwen25CoderSpecialTokens,
    Qwen25CoderTokenizer,
)


@pytest.fixture()
def feature_flags_context():
    yield from feature_flags.feature_flag_fixture()


def test_special_tokens():
    """Test special tokens API for Qwen-2.5-Coder tokenizer."""
    t = Qwen25CoderTokenizer()
    specials = t.special_tokens
    assert isinstance(specials, Qwen25CoderSpecialTokens)
    assert specials.begin_sequence == ()
    assert specials.eos == 151643
    assert specials.fim_prefix == 151659
    assert specials.fim_middle == 151660
    assert specials.fim_suffix == 151661
    assert specials.fim_pad == 151662
    assert specials.reponame == 151663
    assert specials.filename == 151664
    assert specials.padding == 151665
    assert specials.skip == 151666
    assert specials.pause == 151667
    assert specials.retrieval_section == 151668
    assert specials.ret_start == 151669
    assert specials.ret_body == 151670
    assert specials.prefix_body == 151671
    assert specials.nearby_prefix == 151672
    assert specials.nearby_suffix == 151673
    assert specials.sig_lookup == 151674
    assert specials.sig_begin == 151675
    assert specials.sig_end == 151676
    assert specials.signature_section == 151677
    assert specials.far_prefix == 151678
    assert specials.far_suffix == 151679
    assert specials.instruction == 151680
    assert specials.selected_code == 151681
    assert specials.diff_section == 151682
    assert specials.diff_hunk == 151683
    assert specials.has_change == 151684
    assert specials.no_change == 151685
    assert specials.good == 151686
    assert specials.bad == 151687
    assert specials.start_of_key == 151688
    assert specials.end_of_query == 151689
    assert specials.end_of_key == 151690
    assert specials.reward_signal == 151691


def test_basic():
    """Sanity test for Qwen-2.5-Coder tokenizer."""
    t = Qwen25CoderTokenizer()
    # Test the vocab size
    assert t.vocab_size == 151692


@pytest.mark.parametrize(
    "text, expected_tokens",
    [
        pytest.param("123", [16, 17, 18], id="123"),
        pytest.param("董宣毅", [100453, 99576, 101511], id="董宣毅"),
        pytest.param(" ", [220], id="space"),
        pytest.param("  ", [256], id="double space"),
        pytest.param("   ", [262], id="triple space"),
        pytest.param("    ", [257], id="quadruple space"),
        pytest.param("\n", [198], id="newline"),
        pytest.param("\n\n", [271], id="newline"),
    ],
)
def test_simple_case(text: str, expected_tokens: list[int]):
    t = Qwen25CoderTokenizer()
    tokens = t.tokenize_unsafe(text)
    assert tokens == expected_tokens
    actual_text = t.detokenize(tokens)
    assert actual_text == text


@pytest.mark.parametrize(
    "text, safe_tokens, unsafe_tokens",
    [
        pytest.param("<|skip|>", [27, 91, 20599, 91, 29], [151666], id="<|skip|>"),
        pytest.param("<|pause|>", [27, 91, 27448, 91, 29], [151667], id="<|pause|>"),
        pytest.param("i love u", [72, 2948, 575], [72, 2948, 575], id="iou"),
        pytest.param(
            "<|file_sep|>src/file.py",
            [27, 91, 1192, 54775, 91, 29, 3548, 23903, 7197],
            [151664, 3548, 23903, 7197],
            id="<|file_sep|>src/file.py",
        ),
    ],
)
def test_safe_unsafe_calls(text: str, safe_tokens: list[int], unsafe_tokens: list[int]):
    """Test the tokenize_unsafe and tokenize_safe functions."""
    t = Qwen25CoderTokenizer()
    assert t.tokenize_safe(text) == safe_tokens
    assert t.tokenize_unsafe(text) == unsafe_tokens
    assert t.detokenize(safe_tokens) == text
    assert t.detokenize(unsafe_tokens) == text


@pytest.mark.parametrize("use_regex_pretokenizer", [True, False])
def test_qwen25coder_tokenizer_performance(
    feature_flags_context, use_regex_pretokenizer
):
    """Runs the tokenizer on a large text and times it."""

    feature_flags_context.set_flag(
        core_bpe._FORCE_REGEX_PRETOKENIZER, use_regex_pretokenizer
    )

    tokenizer = Qwen25CoderTokenizer()
    test_text = (
        "void quicksort(std::vector<int> & i) \n  for (int i = 0; i < n; i++) { \n    // do something \n  } \n}"
        * 1000
    )
    print("Running tokenizer on large text...")
    start = time.time()
    for _ in range(10):
        tokenizer.tokenize_safe(test_text)
    end = time.time()
    total = end - start
    print(f"Tokenization took {total:.2f} seconds")
