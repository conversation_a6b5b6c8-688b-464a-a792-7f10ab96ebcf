"""Common modules used in StarCoderTokenizer and StarCoder2Tokenizer."""

import json
import pathlib

from typing import Mapping
from dataclasses import dataclass

from base.tokenizers import data_gym

# Source: https://github.com/openai/tiktoken/pull/239
# The pattern in the original GPT-2 release is:
# r""'s|'t|'re|'ve|'m|'ll|'d| ?[\p{L}]+| ?[\p{N}]+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+"""
# This is equivalent, but executes faster:
GPT2_PAT_STR = r"""'(?:[sdmt]|ll|ve|re)| ?\p{L}++| ?\p{N}++| ?[^\s\p{L}\p{N}]++|\s++$|\s+(?!\S)|\s"""
"""Pattern string used to pre-tokenize the input.

This is the same pattern string used by HuggingFace tokenizer which uses its `use_regex = True` flag.

This is what this regex magic does:

`'s|'t|'re|'ve|'m|'ll|'d`: Matches common English contractions.
` ?\p{L}+`: Matches optional space character followed by one or more Unicode letters (\p{L}+).
` ?\p{N}+`: Matches optional space character followed by one or more digits.
` ?[^\s\p{L}\p{N}]+`: Matches optional space character followed by anything other than digits or unicode letters (so punctuation, etc.).
`\s+(?!\S)`: Matches whitespace that isn't followed by a non-whitespace character.
`\s+`: Matches any remaining whitespace.
"""


@dataclass
class Vocab:
    """The result of parsing vocab.json."""

    normal_encode_map: Mapping[bytes, int]
    special_encode_map: Mapping[str, int]
    max_token_id: int

    def update_special(self, special: Mapping[str, int]):
        """Update the vocab with extra special tokens."""
        assert special
        assert all(token_id > self.max_token_id for token_id in special.values())
        assert not any(s in self.special_encode_map for s in special)
        assert not any(
            s in list(self.special_encode_map.values()) for s in special.values()
        )
        assert not any(
            s in list(self.normal_encode_map.values()) for s in special.values()
        )
        new_special = dict(self.special_encode_map)
        new_special.update(special)
        self.special_encode_map = new_special
        self.max_token_id = max(token_id for token_id in special.values())


def read_vocab(file_name: str | pathlib.Path) -> Vocab:
    # Byte sequences corresponding to tokens are data gym in vocab.json
    decoder = data_gym.DataGymDecoder()

    decode_data_gym = decoder.decode

    # vocab_bpe contains the merges along with associated ranks
    module_dir = pathlib.Path(__file__).parent
    with pathlib.Path(module_dir, file_name).open(encoding="utf-8") as vocab_file:
        vocab_bpe_contents = json.load(vocab_file)
        bpe_ranks = {}
        special_ranks = {}
        max_token_id = -1
        for gym, token_id in vocab_bpe_contents.items():
            max_token_id = max(max_token_id, token_id)
            if len(gym) > 6 and gym[0] == "<" and gym[-1] == ">":
                assert decode_data_gym(gym).decode() == gym
                special_ranks[gym] = token_id
            else:
                bpe_ranks[decode_data_gym(gym)] = token_id

    return Vocab(
        normal_encode_map=bpe_ranks,
        special_encode_map=special_ranks,
        max_token_id=max_token_id,
    )
