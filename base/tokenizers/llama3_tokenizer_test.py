"""Tests for Llama3-Base and Llama3-Instruct tokenizer."""

import pytest

from base.tokenizers.llama3_tokenizer import (
    Llama3BaseTokenizer,
    Llama3InstructTokenizer,
    Llama3SpecialTokens,
)

TOKENIZER_LIST = [Llama3InstructTokenizer, Llama3BaseTokenizer]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_basic(tokenizer_cls):
    """Sanity test for Llama3-Instruct tokenizer."""
    t = tokenizer_cls()
    text = "void quicksort(std::vector<int> & i)"
    tokens = t.tokenize_safe(text)
    assert tokens == [
        1019,  # void
        4062,  # _quick
        7003,  # sort
        5305,  # (std
        487,  # ::
        3295,  # vector
        4252,  # <int
        29,  # >
        612,  # _&
        602,  # _i
        8,  # )
    ]
    actual_text = t.detokenize(tokens)
    assert actual_text == text
    # Test the vocab size
    assert t.vocab_size == 128038


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_the_unsafe_call(tokenizer_cls):
    """Test the tokenize_unsafe function."""
    t = tokenizer_cls()
    # Test the build-in special tokens from pre-trained Llama3
    assert t.tokenize_unsafe("<|begin_of_text|>") == [128000]
    assert t.tokenize_unsafe("<|end_of_text|>") == [128001]
    assert t.tokenize_unsafe("<|start_header_id|>") == [128006]
    assert t.tokenize_unsafe("<|end_header_id|>") == [128007]
    assert t.tokenize_unsafe("<|eot_id|>") == [128009]
    # Test the in-house added special tokens
    assert t.tokenize_unsafe("<|skip|>") == [128010]
    assert t.tokenize_unsafe("<|pause|>") == [128011]
    assert t.tokenize_unsafe("<|fim_prefix|>") == [128012]
    assert t.tokenize_unsafe("<|fim_suffix|>") == [128013]
    assert t.tokenize_unsafe("<|fim_middle|>") == [128014]
    assert t.tokenize_unsafe("<|retrieval_section|>") == [128015]
    assert t.tokenize_unsafe("<|ret-start|>") == [128016]
    assert t.tokenize_unsafe("<|ret-body|>") == [128017]
    assert t.tokenize_unsafe("<|prefix-body|>") == [128018]
    assert t.tokenize_unsafe("<|nearby_prefix|>") == [128019]
    assert t.tokenize_unsafe("<|sig-lookup|>") == [128020]
    assert t.tokenize_unsafe("<|sig-begin|>") == [128021]
    assert t.tokenize_unsafe("<|sig-end|>") == [128022]
    assert t.tokenize_unsafe("<|nearby_suffix|>") == [128023]
    assert t.tokenize_unsafe("<|signature_section|>") == [128024]
    assert t.tokenize_unsafe("<filename>") == [128025]
    assert t.tokenize_unsafe("<|far_prefix|>") == [128026]
    assert t.tokenize_unsafe("<|far_suffix|>") == [128027]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_the_safe_call(tokenizer_cls):
    """Test the tokenize_safe function."""
    t = tokenizer_cls()
    # Test the build-in special tokens from pre-trained Llama3
    assert t.tokenize_safe("<|begin_of_text|>") == [
        27,
        91,
        7413,
        3659,
        4424,
        91,
        29,
    ]
    assert t.tokenize_safe("<|end_of_text|>") == [27, 91, 408, 3659, 4424, 91, 29]
    assert t.tokenize_safe("<|start_header_id|>") == [27, 91, 2527, 8932, 851, 91, 29]
    assert t.tokenize_safe("<|end_header_id|>") == [27, 91, 408, 8932, 851, 91, 29]
    assert t.tokenize_safe("<|eot_id|>") == [27, 91, 68, 354, 851, 91, 29]
    # Test the in-house added special tokens
    assert t.tokenize_safe("<|skip|>") == [27, 91, 21247, 91, 29]
    assert t.tokenize_safe("<|far_suffix|>") == [27, 91, 24470, 38251, 91, 29]


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_normal_cases(tokenizer_cls):
    """Test cases, where tokenize_unsafe and tokenize_safe lead to the same results."""
    t = tokenizer_cls()
    f1, f2 = t.tokenize_safe, t.tokenize_unsafe
    # Test some random cases
    assert f1("99000") == f2("99000") == [19146, 410]
    assert f1(" ") == f2(" ") == [220]
    assert f1("  ") == f2("  ") == [256]
    assert f1("   ") == f2("   ") == [262]
    assert f1("\t") == f2("\t") == [197]
    assert f1("\n") == f2("\n") == [198]
    assert f1("0 0") == f2("0 0") == [15, 220, 15]
    assert t.detokenize(f1("0 0")) == t.detokenize(f2("0 0")) == "0 0"
    assert f1("I love you") == f2("I love you") == [40, 3021, 499]
    assert f1("  print()") == f2("  print()") == [220, 1194, 368]
    assert f1("      print()") == f2("      print()") == [415, 1194, 368]
    assert f1("\tprint()") == f2("\tprint()") == [7045, 368]
    assert f1("\n") + f1("Prefix:\n```\n") == f1("\nPrefix:\n```\n")
    assert f1("\n```\n\n") + f1("Suffix:\n```\n") == f1("\n```\n\nSuffix:\n```\n")
    assert f1("\n```\n\n") + f1("Selected Code:\n```\n") == f1(
        "\n```\n\nSelected Code:\n```\n"
    )


@pytest.mark.parametrize("tokenizer_cls", TOKENIZER_LIST)
def test_reverse_generation(tokenizer_cls):
    """Test the reverse generation."""
    t = tokenizer_cls()
    token_ids = [5009, 12794, 25591, 1601, 2864, 7972]
    for token in token_ids:
        assert t.tokenize_safe(t.detokenize([token])) == [token]
    texts = ["I love snowboarding", "0 0 0 0 0 1 2", "153289"]
    for text in texts:
        assert t.detokenize(t.tokenize_safe(text)) == text
        assert t.detokenize(t.tokenize_unsafe(text)) == text


def test_special_tokens_instruct():
    """Test special tokens API for Instruct tokenizer."""
    t = Llama3InstructTokenizer()
    specials = t.special_tokens
    assert isinstance(specials, Llama3SpecialTokens)
    assert specials.begin_sequence == (128000,)
    assert specials.eos == 128009


def test_special_tokens_base():
    """Test special tokens API for Base tokenizer."""
    t = Llama3BaseTokenizer()
    specials = t.special_tokens
    assert isinstance(specials, Llama3SpecialTokens)
    assert specials.begin_sequence == (128000,)
    assert specials.eos == 128001
