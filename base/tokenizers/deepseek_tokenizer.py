"""Module containing a tiktoken-based tokenizer implementation using the DeepSeek Coder Instruct vocabulary."""

import enum
import json
import pathlib
from typing import Mapping, Sequence

from base.tokenizers import data_gym
from base.tokenizers.core_bpe import CoreBPE
from base.tokenizers.tokenizer import (
    REGISTRY,
    NextEditGenSpecialTokens,
    Tokenizer,
    RetrievalSpecialTokens,
)


class MODEL_TYPE(enum.Enum):
    """The mode of the tokenizer.

    This represents the model type of the tokenizer.
    """

    INSTRUCT = "instruct"
    BASE = "base"


class DeepSeekCoderSpecialTokens(RetrievalSpecialTokens, NextEditGenSpecialTokens):
    """Special tokens for the DeepSeek-Coder-Instruct model."""

    def __init__(self, tokenizer: Tokenizer, model_type: MODEL_TYPE):
        super().__init__()
        vocab = tokenizer.vocab

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<|retrieval_section|>"]
        self.ret_start: int = vocab[b"<|ret-start|>"]
        self.ret_body: int = vocab[b"<|ret-body|>"]
        self.prefix_body: int = vocab[b"<|prefix-body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig-lookup|>"]
        self.sig_begin: int = vocab[b"<|sig-begin|>"]
        self.sig_end: int = vocab[b"<|sig-end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]

        # Built-in special tokens from pre-trained DeepSeek-Coder
        self.begin_sequence: tuple[int] = (vocab["<｜begin▁of▁sentence｜>".encode()],)
        # The Base and Instruct tokenizers differ in the eod_token.
        if model_type == MODEL_TYPE.BASE:
            self.eod_token: int = vocab["<｜end▁of▁sentence｜>".encode()]
        else:
            self.eod_token: int = vocab[b"<|EOT|>"]
        self.fim_prefix: int = vocab["<｜fim▁begin｜>".encode()]
        self.fim_suffix: int = vocab["<｜fim▁hole｜>".encode()]
        self.fim_middle: int = vocab["<｜fim▁end｜>".encode()]
        self.newline: int = vocab[b"\n"]
        # Fine-tuning used the following token as padding
        self.padding: int = vocab["<｜end▁of▁sentence｜>".encode()]
        self.filename: int = vocab[b"<filename>"]
        # For retrieval
        self.start_of_key: int = vocab[b"<|startofsequence|>"]
        self.end_of_query: int = vocab[b"<|ret-endofquery|>"]
        self.end_of_key: int = vocab[b"<|ret-endofkey|>"]

        self.eos = self.eod_token

        # Next-edit special tokens
        self.instruction: int = vocab[b"<|instruction|>"]
        self.selected_code: int = vocab[b"<|selected_code|>"]
        self.diff_section: int = vocab[b"<|diff_section|>"]
        self.diff_hunk: int = vocab[b"<|diff_hunk|>"]
        self.has_change: int = vocab[b"<|has_change|>"]
        self.no_change: int = vocab[b"<|no_change|>"]
        self.diff_file: int = self.ret_start

        self.chunk_prediction: int = vocab[b"<|chunk_prediction|>"]


def _get_additional_special_tokens():
    return [
        "<|skip|>",
        "<|pause|>",
        "<|retrieval_section|>",
        "<|ret-start|>",
        "<|ret-body|>",
        "<|prefix-body|>",
        "<|nearby_prefix|>",
        "<|sig-lookup|>",
        "<|sig-begin|>",
        "<|sig-end|>",
        "<|nearby_suffix|>",
        "<|signature_section|>",
        "<filename>",
        "<|far_prefix|>",
        "<|far_suffix|>",
        "<|startofsequence|>",
        "<|ret-endofquery|>",
        "<|ret-endofkey|>",
        # next-edit special tokens
        "<|instruction|>",
        "<|selected_code|>",
        "<|diff_section|>",
        "<|diff_hunk|>",
        "<|has_change|>",
        "<|no_change|>",
        "<|chunk_prediction|>",
    ]


def _read_vocab():
    """Read the vocab file."""
    module_dir = pathlib.Path(__file__).parent
    vocab_file = pathlib.Path(module_dir, "deepseek_coder_instruct_tokenizer.json")
    assert vocab_file.exists() and vocab_file.is_file(), f"{vocab_file} does not exist."
    with vocab_file.open(encoding="utf-8") as vocab_file:
        all_contents = json.load(vocab_file)
        vocab: dict[str, int] = all_contents["model"]["vocab"]
        special_tokens: dict[str, int] = {
            x["content"]: x["id"] for x in all_contents["added_tokens"]
        }
        set_of_vocab_ids = set(vocab.values())
        set_of_all_ids = set(vocab.values()) | set(special_tokens.values())
        assert len(set_of_vocab_ids) == len(vocab)
        assert len(set_of_all_ids) == len(vocab) + len(special_tokens)
        assert 0 in set_of_vocab_ids and len(set_of_vocab_ids) - 1 in set_of_vocab_ids
        assert 0 in set_of_all_ids and len(set_of_all_ids) - 1 in set_of_all_ids
    # Update the special tokens
    for token in _get_additional_special_tokens():
        special_tokens[token] = len(vocab) + len(special_tokens)
    # Byte sequences corresponding to tokens are data gym in vocab.json
    decoder = data_gym.DataGymDecoder()
    decode_data_gym = decoder.decode
    bpe_ranks: dict[bytes, int] = {}
    for gym, token_id in vocab.items():
        bpe_ranks[decode_data_gym(gym)] = token_id
    special_ranks: dict[str, int] = {}
    for token, token_id in special_tokens.items():
        assert token not in special_ranks
        special_ranks[token] = token_id
    return bpe_ranks, special_ranks


PAT_STR = (
    r"""[\r\n]|\s?\p{L}+|\s?\p{P}+|[一-龥ࠀ-一가-퟿]+|\p{N}"""
    r"""|([^\s\p{L}\p{P}\p{N}一-龥ࠀ-一가-퟿]|\s(?!\p{L}|\p{P}))+"""
)
r"""Each disjunction (|) corresponds to the pre-tokenizer sequence used by DeepSeek:

1. Split on `[\r\n]`        (new lines)
2. Split on `\s?\p{L}+`     (optional space and "alphabetic" characters in Unicode)
3. Split on `\s?\p{P}+`     (optional space and punctuation)
4. Split on [一-龥ࠀ-一가-퟿]+ (East Asian character sets)
5. Split on `\p{N}`         (individual digits)
6. Everything else.

The last disjunction is complex because previous groups include optional whitespace ---
the [^\s\p{L}\p{P}\p{N}一-龥ࠀ-一가-퟿] part captures any bytes not previously matched,
while the `\s(?!\p{L}|\p{P})` part captures any whitespace that is not followed by a
letter or punctuation character.
"""


class _DeepSeekCoderTokenizer(Tokenizer):
    """Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    def __init__(self, model_type: MODEL_TYPE):
        """Initialize the tokenizer from the JSON vocab file."""
        basic_vocab, special_tokens = _read_vocab()
        self.core_bpe = CoreBPE(basic_vocab, special_tokens, PAT_STR)

        self._vocab_bytes2id = {b: v for (b, v) in basic_vocab.items()}
        # Note(Xuanyi): We need to use both .encode() and decode_data_gym() to handle the special tokens.
        # These 6 special tokens ý, À, ø, ú, ü, and ö lead to some subtle bugs in HF tokenizer, e.g.,
        # tokenizer.tokenize(tokenizer.detokenize([1601])) == 32007 in the HF DeepSeek tokenizer.
        # Here, CoreBPE is doing the right thing.
        # However, when we compute self._vocab_bytes2id, which is mainly used for the vocab_size and analysis,
        # ý, À, ø, ú, ü, and ö collide with some existing vocabs when using .encode(); the specical char ｜ in other specical tokens
        # failed to be retrived in decode_data_gym(). Thus, we used a mix of .encode() and decode_data_gym()
        # to handle this complicated scenarios.

        decoder = data_gym.DataGymDecoder()
        decode_data_gym = decoder.decode
        for token, _id in special_tokens.items():
            cur_bytes = token.encode()
            if cur_bytes in self._vocab_bytes2id:
                cur_bytes = decode_data_gym(token)
            if cur_bytes in self._vocab_bytes2id:
                raise ValueError(
                    f"The token {token} ({cur_bytes}) is already in the vocab as"
                    f" {self._vocab_bytes2id[cur_bytes]}. Please check the vocab file."
                )
            self._vocab_bytes2id[cur_bytes] = _id
        self._raw_basic_vocab = basic_vocab
        self._raw_special_tokens = special_tokens
        self._special_set = set(special_tokens.keys())
        self._special_tokens = DeepSeekCoderSpecialTokens(self, model_type)

    @property
    def vocab_size(self):
        return len(self._vocab_bytes2id)

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return self._vocab_bytes2id

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=set())

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self._special_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> DeepSeekCoderSpecialTokens:
        """Returns the special tokens for deepseek-coder-*."""
        return self._special_tokens


class DeepSeekCoderBaseTokenizer(_DeepSeekCoderTokenizer):
    """Tokenizer for the DeepSeek-Coder-Base models.

    Differs from the Instruct Tokenizer in that the eod_token is different.
    """

    def __init__(self):
        super().__init__(MODEL_TYPE.BASE)


class DeepSeekCoderInstructTokenizer(_DeepSeekCoderTokenizer):
    """Tokenizer for the DeepSeek-Coder-Instruct models.

    Differs from the Base Tokenizer in that the eod_token is different.
    """

    def __init__(self):
        super().__init__(MODEL_TYPE.INSTRUCT)


REGISTRY.add("deepseek_coder_base", DeepSeekCoderBaseTokenizer)
REGISTRY.add("deepseek_coder_instruct", DeepSeekCoderInstructTokenizer)
