"""Test cases for data_gym.py."""

import pytest

from base.tokenizers import data_gym


def test_decode():
    """Check that every character is correctly decoded."""

    decode_array: list[tuple[str, int]] = [
        ("Ā", 0x0),
        ("ā", 0x1),
        ("Ă", 0x2),
        ("ă", 0x3),
        ("Ą", 0x4),
        ("ą", 0x5),
        ("Ć", 0x6),
        ("ć", 0x7),
        ("Ĉ", 0x8),
        ("ĉ", 0x9),
        ("Ċ", 0xA),
        ("ċ", 0xB),
        ("Č", 0xC),
        ("č", 0xD),
        ("Ď", 0xE),
        ("ď", 0xF),
        ("Đ", 0x10),
        ("đ", 0x11),
        ("Ē", 0x12),
        ("ē", 0x13),
        ("Ĕ", 0x14),
        ("ĕ", 0x15),
        ("Ė", 0x16),
        ("ė", 0x17),
        ("Ę", 0x18),
        ("ę", 0x19),
        ("Ě", 0x1A),
        ("ě", 0x1B),
        ("Ĝ", 0x1C),
        ("ĝ", 0x1D),
        ("Ğ", 0x1E),
        ("ğ", 0x1F),
        ("Ġ", 0x20),
        ("!", 0x21),
        ('"', 0x22),
        ("#", 0x23),
        ("$", 0x24),
        ("%", 0x25),
        ("&", 0x26),
        ("'", 0x27),
        ("(", 0x28),
        (")", 0x29),
        ("*", 0x2A),
        ("+", 0x2B),
        (",", 0x2C),
        ("-", 0x2D),
        (".", 0x2E),
        ("/", 0x2F),
        ("0", 0x30),
        ("1", 0x31),
        ("2", 0x32),
        ("3", 0x33),
        ("4", 0x34),
        ("5", 0x35),
        ("6", 0x36),
        ("7", 0x37),
        ("8", 0x38),
        ("9", 0x39),
        (":", 0x3A),
        (";", 0x3B),
        ("<", 0x3C),
        ("=", 0x3D),
        (">", 0x3E),
        ("?", 0x3F),
        ("@", 0x40),
        ("A", 0x41),
        ("B", 0x42),
        ("C", 0x43),
        ("D", 0x44),
        ("E", 0x45),
        ("F", 0x46),
        ("G", 0x47),
        ("H", 0x48),
        ("I", 0x49),
        ("J", 0x4A),
        ("K", 0x4B),
        ("L", 0x4C),
        ("M", 0x4D),
        ("N", 0x4E),
        ("O", 0x4F),
        ("P", 0x50),
        ("Q", 0x51),
        ("R", 0x52),
        ("S", 0x53),
        ("T", 0x54),
        ("U", 0x55),
        ("V", 0x56),
        ("W", 0x57),
        ("X", 0x58),
        ("Y", 0x59),
        ("Z", 0x5A),
        ("[", 0x5B),
        ("\\", 0x5C),
        ("]", 0x5D),
        ("^", 0x5E),
        ("_", 0x5F),
        ("`", 0x60),
        ("a", 0x61),
        ("b", 0x62),
        ("c", 0x63),
        ("d", 0x64),
        ("e", 0x65),
        ("f", 0x66),
        ("g", 0x67),
        ("h", 0x68),
        ("i", 0x69),
        ("j", 0x6A),
        ("k", 0x6B),
        ("l", 0x6C),
        ("m", 0x6D),
        ("n", 0x6E),
        ("o", 0x6F),
        ("p", 0x70),
        ("q", 0x71),
        ("r", 0x72),
        ("s", 0x73),
        ("t", 0x74),
        ("u", 0x75),
        ("v", 0x76),
        ("w", 0x77),
        ("x", 0x78),
        ("y", 0x79),
        ("z", 0x7A),
        ("{", 0x7B),
        ("|", 0x7C),
        ("}", 0x7D),
        ("~", 0x7E),
        ("ġ", 0x7F),
        ("Ģ", 0x80),
        ("ģ", 0x81),
        ("Ĥ", 0x82),
        ("ĥ", 0x83),
        ("Ħ", 0x84),
        ("ħ", 0x85),
        ("Ĩ", 0x86),
        ("ĩ", 0x87),
        ("Ī", 0x88),
        ("ī", 0x89),
        ("Ĭ", 0x8A),
        ("ĭ", 0x8B),
        ("Į", 0x8C),
        ("į", 0x8D),
        ("İ", 0x8E),
        ("ı", 0x8F),
        ("Ĳ", 0x90),
        ("ĳ", 0x91),
        ("Ĵ", 0x92),
        ("ĵ", 0x93),
        ("Ķ", 0x94),
        ("ķ", 0x95),
        ("ĸ", 0x96),
        ("Ĺ", 0x97),
        ("ĺ", 0x98),
        ("Ļ", 0x99),
        ("ļ", 0x9A),
        ("Ľ", 0x9B),
        ("ľ", 0x9C),
        ("Ŀ", 0x9D),
        ("ŀ", 0x9E),
        ("Ł", 0x9F),
        ("ł", 0xA0),
        ("¡", 0xA1),
        ("¢", 0xA2),
        ("£", 0xA3),
        ("¤", 0xA4),
        ("¥", 0xA5),
        ("¦", 0xA6),
        ("§", 0xA7),
        ("¨", 0xA8),
        ("©", 0xA9),
        ("ª", 0xAA),
        ("«", 0xAB),
        ("¬", 0xAC),
        ("Ń", 0xAD),
        ("®", 0xAE),
        ("¯", 0xAF),
        ("°", 0xB0),
        ("±", 0xB1),
        ("²", 0xB2),
        ("³", 0xB3),
        ("´", 0xB4),
        ("µ", 0xB5),
        ("¶", 0xB6),
        ("·", 0xB7),
        ("¸", 0xB8),
        ("¹", 0xB9),
        ("º", 0xBA),
        ("»", 0xBB),
        ("¼", 0xBC),
        ("½", 0xBD),
        ("¾", 0xBE),
        ("¿", 0xBF),
        ("À", 0xC0),
        ("Á", 0xC1),
        ("Â", 0xC2),
        ("Ã", 0xC3),
        ("Ä", 0xC4),
        ("Å", 0xC5),
        ("Æ", 0xC6),
        ("Ç", 0xC7),
        ("È", 0xC8),
        ("É", 0xC9),
        ("Ê", 0xCA),
        ("Ë", 0xCB),
        ("Ì", 0xCC),
        ("Í", 0xCD),
        ("Î", 0xCE),
        ("Ï", 0xCF),
        ("Ð", 0xD0),
        ("Ñ", 0xD1),
        ("Ò", 0xD2),
        ("Ó", 0xD3),
        ("Ô", 0xD4),
        ("Õ", 0xD5),
        ("Ö", 0xD6),
        ("×", 0xD7),
        ("Ø", 0xD8),
        ("Ù", 0xD9),
        ("Ú", 0xDA),
        ("Û", 0xDB),
        ("Ü", 0xDC),
        ("Ý", 0xDD),
        ("Þ", 0xDE),
        ("ß", 0xDF),
        ("à", 0xE0),
        ("á", 0xE1),
        ("â", 0xE2),
        ("ã", 0xE3),
        ("ä", 0xE4),
        ("å", 0xE5),
        ("æ", 0xE6),
        ("ç", 0xE7),
        ("è", 0xE8),
        ("é", 0xE9),
        ("ê", 0xEA),
        ("ë", 0xEB),
        ("ì", 0xEC),
        ("í", 0xED),
        ("î", 0xEE),
        ("ï", 0xEF),
        ("ð", 0xF0),
        ("ñ", 0xF1),
        ("ò", 0xF2),
        ("ó", 0xF3),
        ("ô", 0xF4),
        ("õ", 0xF5),
        ("ö", 0xF6),
        ("÷", 0xF7),
        ("ø", 0xF8),
        ("ù", 0xF9),
        ("ú", 0xFA),
        ("û", 0xFB),
        ("ü", 0xFC),
        ("ý", 0xFD),
        ("þ", 0xFE),
        ("ÿ", 0xFF),
    ]

    decoder = data_gym.DataGymDecoder()
    for e in decode_array:
        assert decoder.decode(e[0])[0] == e[1]


def test_decode_empty():
    """Test decoding an empty string."""

    assert data_gym.DataGymDecoder().decode("") == b""


def test_decode_multiple_characters():
    """Test decoding multiple characters."""

    assert data_gym.DataGymDecoder().decode("@@@") == b"@@@"


def test_decode_invalid_character():
    """Test decoding invalid characters."""

    with pytest.raises(ValueError):
        data_gym.DataGymDecoder().decode("   ")
