"""Test for the code_tokenizers module."""

from typing import Generator

from base.tokenizers import (
    CodeGenSpecialTokens,
    create_tokenizer_by_name,
    list_tokenizers,
)


def test_basic_encoding():
    """Tests basic decoding and encoding of fim."""
    t = create_tokenizer_by_name("fim")
    text = "void quicksort(std::vector<int> & i)"
    tokens = t.tokenize_safe(text)
    assert tokens == [
        19382,
        627,
        3378,
        419,
        7,
        19282,
        3712,
        31364,
        27,
        600,
        29,
        1222,
        1312,
        8,
    ]
    actual_text = t.detokenize(tokens)
    assert actual_text == text


def test_fim():
    """Test the fim token handling."""
    t = create_tokenizer_by_name("fim")

    # a change to the token ids is an incompatible change
    assert t.vocab[b"<|fim-sep|>"] == 50295
    assert t.vocab[b"<|fim-eos|>"] == 50296


def test_tokenize_detokenize_is_identity():
    """Ensure that text = detokenize(tokenize(text)).

    By default, the HF tokenizer will do some postprocessing on detokenized
    strings that removes whitespace, which means that
    detokenize(tokenize(text)) != text.
    """
    texts = [
        "return 'slurp'",
        "filename = 'requirements.txt'",
    ]
    t = create_tokenizer_by_name("fim")

    for text in texts:
        actual = t.detokenize(t.tokenize_safe(text))
        assert text == actual


def _get_tokens(t1) -> Generator[str, None, None]:
    token_indices = t1.vocab.values()
    # iterate over all tokens
    for idx in token_indices:
        t1_s = t1.detokenize([idx])
        yield t1_s


def test_vocab_special_tokens():
    """Tests the special tokens handling.

    Some tokens, like end of document, are special - they are only inserted into the token stream (i.e. the
    stream of integers). While they may be rendered in string form for human readability, that string form
    when parsed should not yield the special token - it should yield multiple tokens.

    This test asserts that parsing the string representation of a special token yields multiple tokens.
    Rather than just test the special tokens, the test tries to parse the string representation of every token.
    Tokens whose strings representation doesn't start with '<|' are assumed not to be special tokens and ignored.
    """
    t = create_tokenizer_by_name("fim")

    count = 0
    for token_string in _get_tokens(t):
        tokens = t.tokenize_safe(token_string)
        if len(tokens) > 1:
            # this is a special token in the vocab
            count += 1
        else:
            # it is a single token, there should be <| in the text
            assert not token_string.startswith("<|")
    # ensure that we detected special tokens
    assert count


def test_special_api():
    """Exercise special tokens API for codegen."""
    specials = create_tokenizer_by_name("fim").special_tokens
    assert isinstance(specials, CodeGenSpecialTokens)
    assert specials.eos == 50256
    assert specials.fim_eos == 50296
    assert specials.fim_middle == 50325
    assert specials.fim_prefix == 50327
    assert specials.fim_suffix == 50326


def test_all_tokenizers_decode_multi_token_toreplacement_character():
    """
    Ensure that all tokenizers decode multi-token characters to the replacement character.
    This is important for current and future tokenizers because chat streaming relies on
    replacement tokens to detect cases where more buffering is needed before detokenizing
    """
    MULTI_TOKEN_CHARACTER = (
        "\u8989"  # This is a unicode character that all tokenizers encode as multitoken
    )
    for tokenizer_name in list_tokenizers():
        t = create_tokenizer_by_name(tokenizer_name)
        tokens = t.tokenize_safe(MULTI_TOKEN_CHARACTER)
        assert (
            len(tokens) > 1
        ), f"The tokenizer {tokenizer_name} doesn't tokenize {MULTI_TOKEN_CHARACTER} to multiple tokens, find alternative unicode character"
        assert (
            t.detokenize(tokens[:1]) == "\ufffd"
        ), f"Tokenizer {tokenizer_name} doesn't decode multi-token tokens to replacement character"
