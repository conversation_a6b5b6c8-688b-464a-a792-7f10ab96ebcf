"""Utilities for handling token streams."""

from typing import Iterator, Sequence

from base.tokenizers.tokenizer import Tokenizer


def detokenize_stream(
    token_iterator: Iterator[Sequence[int]],
    tokenizer: Tokenizer,
    end_token_ids: set[int] | None = None,
) -> Iterator[str]:
    """Detokenizes a stream of tokens, properly handling multi-token characters.

    This is a general utility that can be used by any host that needs to stream
    tokens and properly handle multi-token characters.

    Args:
        token_iterator: Iterator yielding sequences of token IDs
        tokenizer: The tokenizer to use for detokenization
        end_token_ids: Optional set of token IDs that indicate end of stream

    Yields:
        Detokenized text strings, ensuring multi-token characters are properly reconstructed
    """
    pending_tokens = []

    for tokens in token_iterator:
        if end_token_ids is not None:
            # Filter out end tokens if specified
            tokens = [t for t in tokens if t not in end_token_ids]

        # Add new tokens to pending tokens
        pending_tokens.extend(tokens)

        # Detokenize pending tokens to check for incomplete characters
        output_text = tokenizer.detokenize(pending_tokens)

        # If text ends with replacement character, we need more tokens
        if output_text.endswith("\ufffd"):
            continue

        # Output is complete, yield it and reset pending tokens
        yield output_text
        pending_tokens = []

    # Handle any remaining tokens at the end of the stream
    if pending_tokens:
        yield tokenizer.detokenize(pending_tokens)
