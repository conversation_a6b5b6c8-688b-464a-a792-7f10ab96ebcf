"""Tests the behavior for the rogue tokenizer."""

import pytest

from base.tokenizers import rogue_tokenizer


def test_basic():
    """Sanity test for rogue tokenizer."""
    t = rogue_tokenizer.create()

    text = "void quicksort(std::vector<int> & i)"
    tokens = t.tokenize_safe(text)
    assert tokens == [
        1117,  # void
        8271,  # _quick
        3323,  # sort
        26,  # (
        1530,  # std
        403,  # ::
        2402,  # vector
        46,  # <
        410,  # int
        48,  # >
        549,  # _&
        595,  # _i
        27,  # )
    ]
    actual_text = t.detokenize(tokens)
    assert actual_text == text


def test_special():
    """Special tokens detokenize but don't tokenize."""
    t = rogue_tokenizer.create()
    text = t.detokenize([0])
    assert text == "<|endoftext|>"
    tokens = t.tokenize_safe(text)
    assert len(tokens) > 1


def test_java_generic_wildcard():
    """Tests that the java generic wildcard <?> is treated as a normal token.

    The token text looks very much like other special tokens.
    """
    t = rogue_tokenizer.create()
    tokens = t.tokenize_safe("<?>")
    assert tokens == [11674]


def test_special_roundtrip():
    """Test tokenize_unsafe works."""
    t = rogue_tokenizer.create()
    text = t.detokenize([0])
    assert text == "<|endoftext|>"
    tokens = t.tokenize_unsafe(text)
    assert tokens == [0]


def test_check_decode():
    """Check that every token id can be decoded."""
    t = rogue_tokenizer.create()
    for i in range(t.vocab_size):
        text = t.detokenize([i])
        assert text


@pytest.mark.parametrize(
    "special_token, unsafe, safe",
    [
        ("<filename>", [5], [46, 3607, 48]),
        ("<|retrieval_section|>", [49155], [46, 110, 35063, 695, 81, 1906, 28318]),
        ("<|ret-start|>", [49156], [46, 110, 2094, 31, 1366, 28318]),
        ("<|ret-body|>", [49157], [46, 110, 2094, 31, 1548, 28318]),
        ("<|sig-lookup|>", [49160], [46, 110, 4994, 31, 10122, 28318]),
        ("<|sig-begin|>", [49161], [46, 110, 4994, 31, 2409, 28318]),
        ("<|sig-end|>", [49162], [46, 110, 4994, 31, 416, 28318]),
        ("<|signature_section|>", [49164], [46, 110, 9569, 81, 1906, 28318]),
    ],
)
def test_special_tokens(special_token: str, unsafe: list[int], safe: list[int]):
    """Test the behavior with the Ender special tokens."""
    t = rogue_tokenizer.create()
    tokens = t.tokenize_unsafe(special_token)
    assert tokens == unsafe
    tokens = t.tokenize_safe(special_token)
    assert tokens == safe


def test_special_api():
    """Test special tokens API."""
    specials = rogue_tokenizer.create().special_tokens
    assert isinstance(specials, rogue_tokenizer.RogueSpecialTokens)
    assert specials.eos == 0
    assert specials.fim_middle == 2
    assert specials.newline == 203
    assert specials.pause == 49154
    assert specials.sig_lookup == 49160
    assert specials.sig_begin == 49161
    assert specials.sig_end == 49162
    assert specials.signature_section == 49164
