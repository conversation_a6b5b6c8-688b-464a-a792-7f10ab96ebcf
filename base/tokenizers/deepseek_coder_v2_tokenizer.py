"""Module containing a tiktoken-based tokenizer implementation using the DeepSeek Coder V2 vocabulary.

The major difference between DeepSeek-Coder-V1 tokenizer and DeepSeek-Coder-V2 tokenizer is:
- different vocab files
- the pre-tokenization pattern
- In DeepSeek-Coder-V2, Base and Instruct are the same, however, in DeepSeek-Coder-V1, Base and Instruct are slightly different.

Remember that the DeepSeek-Coder-V2 model is pre-trained with a bos token at the head of all data. So when u use DeepSeek-Coder-V2 tokenizer,
it is highly possible you need to add a bos token at the head of the prompt tokens.
"""

import json
import pathlib
from typing import Mapping, Sequence

from base.tokenizers import data_gym
from base.tokenizers.core_bpe import CoreBPE
from base.tokenizers.tokenizer import (
    REGISTRY,
    RagSpecialTokens,
    RetrievalSpecialTokens,
    Tokenizer,
)


class DeepSeekCoderV2SpecialTokens(RetrievalSpecialTokens, RagSpecialTokens):
    """Special tokens for the DeepSeek-Coder-V2 Base/Instruct model."""

    def __init__(self, tokenizer: Tokenizer):
        super().__init__()
        vocab = tokenizer.vocab

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<|retrieval_section|>"]
        self.ret_start: int = vocab[b"<|ret-start|>"]
        self.ret_body: int = vocab[b"<|ret-body|>"]
        self.prefix_body: int = vocab[b"<|prefix-body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig-lookup|>"]
        self.sig_begin: int = vocab[b"<|sig-begin|>"]
        self.sig_end: int = vocab[b"<|sig-end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]

        self.eod_token: int = vocab["<｜end▁of▁sentence｜>".encode()]
        self.fim_prefix: int = vocab["<｜fim▁begin｜>".encode()]
        self.fim_suffix: int = vocab["<｜fim▁hole｜>".encode()]
        self.fim_middle: int = vocab["<｜fim▁end｜>".encode()]
        self.newline: int = vocab[b"\n"]
        self.padding: int = vocab["<｜end▁of▁sentence｜>".encode()]
        self.filename: int = vocab[b"<|filename|>"]

        # Built-in special tokens from pre-trained DeepSeek-Coder
        self.begin_sequence: tuple[int] = (vocab["<｜begin▁of▁sentence｜>".encode()],)

        # Special tokens needed for retrieval model
        self.start_of_key: int = vocab[b"<|startofsequence|>"]
        self.end_of_query: int = vocab[b"<|ret-endofquery|>"]
        self.end_of_key: int = vocab[b"<|ret-endofkey|>"]

        self.eos = self.eod_token


def _get_additional_special_tokens():
    return [
        "<|skip|>",
        "<|pause|>",
        "<|retrieval_section|>",
        "<|ret-start|>",
        "<|ret-body|>",
        "<|prefix-body|>",
        "<|nearby_prefix|>",
        "<|nearby_suffix|>",
        "<|sig-lookup|>",
        "<|sig-begin|>",
        "<|sig-end|>",
        "<|signature_section|>",
        "<|filename|>",
        "<|far_prefix|>",
        "<|far_suffix|>",
        "<|startofsequence|>",
        "<|ret-endofquery|>",
        "<|ret-endofkey|>",
    ]


def _read_vocab():
    """Read the vocab file."""
    module_dir = pathlib.Path(__file__).parent
    vocab_file = pathlib.Path(module_dir, "deepseek_coder_v2_base_vocab.json")
    assert vocab_file.exists() and vocab_file.is_file(), f"{vocab_file} does not exist."
    with vocab_file.open(encoding="utf-8") as vocab_file:
        all_contents = json.load(vocab_file)
        vocab: dict[str, int] = all_contents["model"]["vocab"]
        special_tokens: dict[str, int] = {
            x["content"]: x["id"] for x in all_contents["added_tokens"]
        }
        set_of_vocab_ids = set(vocab.values())
        set_of_all_ids = set(vocab.values()) | set(special_tokens.values())
        assert len(set_of_vocab_ids) == len(vocab)
        assert len(set_of_all_ids) == len(vocab) + len(special_tokens)
        assert 0 in set_of_vocab_ids and len(set_of_vocab_ids) - 1 in set_of_vocab_ids
        assert 0 in set_of_all_ids and len(set_of_all_ids) - 1 in set_of_all_ids
    # Update the special tokens
    for token in _get_additional_special_tokens():
        special_tokens[token] = len(vocab) + len(special_tokens)
    # Byte sequences corresponding to tokens are data gym in vocab.json
    decoder = data_gym.DataGymDecoder()
    decode_data_gym = decoder.decode
    bpe_ranks: dict[bytes, int] = {}
    for gym, token_id in vocab.items():
        bpe_ranks[decode_data_gym(gym)] = token_id
    special_ranks: dict[str, int] = {}
    for token, token_id in special_tokens.items():
        assert token not in special_ranks
        special_ranks[token] = token_id
    return bpe_ranks, special_ranks


# NOTE(Xuanyi): we should be very very careful about this as DeepSeek-Coder-V2 has a complicated pre-tokenization pattern.
# HF library and OpenAI's tiktoken library have a different pre-tokenization logic.
# Here, I manually translate the HF logic into the regex pattern accepted by tiktoken.
# The HF logic can be found in /mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct/tokenizer.json.
PAT_STR = (
    r"""[\r\n]"""
    r"""|\s?[A-Za-zµÀ-ÖØ-öø-ƺƼ-ƿǄ-ʓʕ-ʯͰ-ͳͶͷͻ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-ՖႠ-ჅᎠ-Ᏽᏸ-ᏽᲐ-ᲺᲽ-Ჿᴀ-ᴫᵫ-ᵷᵹ-ᶚḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℴℹℼ-ℿⅅ-ⅉⅎↃↄⰀ-ⱻⱾ-ⳤⳫ-ⳮⳲⳳꙀ-ꙭꚀ-ꚛꜢ-ꝯꝱ-ꞇꞋ-ꞎꭰ-ꮿﬀ-ﬆﬓ-ﬗＡ-Ｚａ-ｚ𐐀-𐑏𐒰-𐓓𐓘-𐓻𐲀-𐲲𐳀-𐳲𑢠-𑣟𞤀-𞥃]+"""
    r"""|\s?[!-/:-~！-／：-～‘-‟　-。]+"""
    r"""|\s+$"""
    r"""|[一-龥ࠀ-一가-퟿]+"""
    r"""|\p{N}"""
    r"""|([^\s\p{L}\p{P}\p{N}一-龥ࠀ-一가-퟿]|\s(?!\p{L}|\p{P}))+"""
)
r"""Each disjunction (|) corresponds to the pre-tokenizer sequence used by DeepSeek-Coder-V2:

1. Split on `[\r\n]`        (new lines)
2. Split on `\s?[A-Za-zµÀ-ÖØ-öø-ƺƼ-ƿǄ-ʓʕ-ʯͰ-ͳͶͷͻ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-ՖႠ-ჅᎠ-Ᏽᏸ-ᏽᲐ-ᲺᲽ-Ჿᴀ-ᴫᵫ-ᵷᵹ-ᶚḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℴℹℼ-ℿⅅ-ⅉⅎↃↄⰀ-ⱻⱾ-ⳤⳫ-ⳮⳲⳳꙀ-ꙭꚀ-ꚛꜢ-ꝯꝱ-ꞇꞋ-ꞎꭰ-ꮿﬀ-ﬆﬓ-ﬗＡ-Ｚａ-ｚ𐐀-𐑏𐒰-𐓓𐓘-𐓻𐲀-𐲲𐳀-𐳲𑢠-𑣟𞤀-𞥃]+`
    (optional space and a sequence of alphabetic characters from various scripts and symbol sets)
3. Split on `\s+$`     (space and punctuation)
4. Split on [一-龥ࠀ-一가-퟿]+ (East Asian character sets)
5. Split on `\p{N}`         (individual digits)
6. Everything else.

The last disjunction is complex because previous groups include optional whitespace ---
the [^\s\p{L}\p{P}\p{N}一-龥ࠀ-一가-퟿] part captures any bytes not previously matched,
while the `\s(?!\p{L}|\p{P})` part captures any whitespace that is not followed by a
letter or punctuation character.
"""


class DeepSeekCoderV2Tokenizer(Tokenizer):
    """Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    def __init__(self):
        """Initialize the tokenizer from the JSON vocab file."""
        basic_vocab, special_tokens = _read_vocab()
        self.core_bpe = CoreBPE(basic_vocab, special_tokens, PAT_STR)

        self._vocab_bytes2id = {b: v for (b, v) in basic_vocab.items()}
        for token, _id in special_tokens.items():
            cur_bytes = token.encode()
            if cur_bytes in self._vocab_bytes2id:
                raise ValueError(
                    f"The token {token} ({cur_bytes}) is already in the vocab as"
                    f" {self._vocab_bytes2id[cur_bytes]}. Please check the vocab file."
                )
            self._vocab_bytes2id[cur_bytes] = _id
        self._raw_basic_vocab = basic_vocab
        self._raw_special_tokens = special_tokens
        self._special_set = set(special_tokens.keys())
        self._special_tokens = DeepSeekCoderV2SpecialTokens(self)

    @property
    def vocab_size(self):
        return len(self._vocab_bytes2id)

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return self._vocab_bytes2id

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=set())

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self._special_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> DeepSeekCoderV2SpecialTokens:
        """Returns the special tokens for deepseek-coder-*."""
        return self._special_tokens


REGISTRY.add("deepseek_coder_v2", DeepSeekCoderV2Tokenizer)
