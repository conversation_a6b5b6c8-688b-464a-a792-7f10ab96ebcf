"""Tests for StarCode tokenizer implemented using TikToken."""

from concurrent.futures import ProcessPoolExecutor

import pytest
from base.tokenizers.tiktoken_starcoder_tokenizer import (
    StarCoderSpecialTokens,
    TiktokenStarCoderTokenizer,
)


def test_basic():
    """Sanity test for StarCoder tokenizer."""
    t = TiktokenStarCoderTokenizer()
    text = "void quicksort(std::vector<int> & i)"
    tokens = t.tokenize_safe(text)
    assert tokens == [
        1117,  # void
        8271,  # _quick
        3323,  # sort
        26,  # (
        1530,  # std
        403,  # ::
        2402,  # vector
        46,  # <
        410,  # int
        48,  # >
        549,  # _&
        595,  # _i
        27,  # )
    ]
    actual_text = t.detokenize(tokens)
    assert actual_text == text


def test_java_generic_wildcard():
    """Tests that the java generic wildcard <?> is treated as a normal token.

    The token text looks very much like other special tokens.
    """
    t = TiktokenStarCoderTokenizer()
    tokens = t.tokenize_safe("<?>")
    assert tokens == [11674]


def test_special():
    """Special tokens detokenize but don't tokenize."""
    t = TiktokenStarCoderTokenizer()
    text = t.detokenize([0])
    assert text == "<|endoftext|>"
    tokens = t.tokenize_safe(text)
    assert len(tokens) > 1


def test_special_roundtrip():
    """Test tokenize_unsafe works."""
    t = TiktokenStarCoderTokenizer()
    text = t.detokenize([0])
    assert text == "<|endoftext|>"
    tokens = t.tokenize_unsafe(text)
    assert tokens == [0]


def test_special_tokens():
    """Test special tokens API."""
    t = TiktokenStarCoderTokenizer()
    specials = t.special_tokens
    assert isinstance(specials, StarCoderSpecialTokens)
    assert specials.eos == 0
    assert specials.fim_middle == 2


# Example character starting with 0xf1, which is not in the starcoder vocab
EXAMPLE_CHAR = b"\xf1\xb2\xbf\xb7"


@pytest.mark.parametrize(
    "input_bytes",
    [
        pytest.param(EXAMPLE_CHAR, id="v0"),
        pytest.param(b"ABC" + EXAMPLE_CHAR, id="v1"),
        pytest.param(EXAMPLE_CHAR + b"EDF", id="v2"),
        pytest.param(b"ABC" + EXAMPLE_CHAR + b"EDF", id="v3"),
    ],
)
def test_missing_byte(input_bytes: bytes):
    """Test that missing bytes are handled gracefully.

    The missing first bytes in the starcoder vocab is:
        {192, 193, 241, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255}

    192 (C0) & 193 (C1) are not valid first bytes in 2-byte UTF-8 encoding.
    245-255 (F5-FF) are not valid bytes in 4-byte UTF-8 encoding.

    Only 241 (F1) needs to be handled and tested at this point.
    """
    t = TiktokenStarCoderTokenizer()
    text = input_bytes.decode("utf-8")
    tokens = t.tokenize_safe(text)

    # UTF-8 uses U+FFFD as the official REPLACEMENT CHARACTER
    replacement = chr(0xFFFD) * (len(EXAMPLE_CHAR) - 1)

    assert t.detokenize(tokens) == text.replace(
        EXAMPLE_CHAR.decode("utf-8"), replacement
    )


@pytest.mark.parametrize(
    "input_str, expected_offsets",
    [
        pytest.param("hello world!", [0, 5, 11], id="hello_world"),
        pytest.param(
            "void quicksort(std::vector<int> & i)",
            [
                0,  # void
                4,  # _quick
                10,  # sort
                14,  # (
                15,  # std
                18,  # ::
                20,  # vector
                26,  # <
                27,  # int
                30,  # >
                31,  # _&
                33,  # _i
                35,  # )
            ],
            id="quicksort",
        ),
        pytest.param(
            "a😃b𐐷",
            [
                0,  # a
                1,  # 😃 - first 3 bytes
                1,  # 😃 - last byte
                2,  # b
                3,  # 𐐷 - 1st byte
                3,  # 𐐷 - 2nd byte
                3,  # 𐐷 - 3rd byte
                3,  # 𐐷 - 4th byte
            ],
            id="unicode",
        ),
    ],
)
def test_decode_with_offsets(input_str: str, expected_offsets: list[int]):
    tokenizer = TiktokenStarCoderTokenizer()
    tokens = tokenizer.tokenize_safe(input_str)
    output_str, offsets = tokenizer.detokenize_with_offsets(tokens)
    assert output_str == input_str
    assert offsets == expected_offsets


def test_decode_with_offsets_malformed_input():
    input_str = "a😃b𐐷"
    tokenizer = TiktokenStarCoderTokenizer()
    tokens = tokenizer.tokenize_safe(input_str)
    # tokens has the following layout:
    # [a, 😃.1, 😃.2, b, 𐐷.1, 𐐷.2, 𐐷.3, 𐐷.4]
    # Drop out the two latter unicode tokens.
    assert len(tokens) == 8
    tokens = tokens[:2] + tokens[3:7]
    output_str, offsets = tokenizer.detokenize_with_offsets(tokens)
    # 0xFFFD is the Unicode replacement character.
    assert output_str == "a\ufffdb\ufffd"
    assert offsets == [
        0,  # a
        1,  # 😃 - first 3 bytes -- converted to \uFFFD
        1,  # b  - includes previous, incomplete character
        3,  # 𐐷 - 1st byte
        3,  # 𐐷 - 2nd byte
        3,  # 𐐷 - 3rd byte
    ]


def _tokenize(tokenizer: TiktokenStarCoderTokenizer, text: str):
    return tokenizer.tokenize_safe(text)


def test_tokenizer_supports_multiprocessing():
    tokenizer = TiktokenStarCoderTokenizer()

    texts = [
        "hello world!",
        "goodbye world!",
    ]

    expected = [_tokenize(tokenizer, text) for text in texts]

    with ProcessPoolExecutor(max_workers=1) as executor:
        actual = list(executor.map(_tokenize, [tokenizer] * len(texts), texts))

    assert expected == actual
