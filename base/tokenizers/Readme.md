# tokenizers

The code in this directory should work both in bazel sandboxes as well as outside of them.

## In this directory

- The tokenizer interface (see `tokenizer.Tokenizer`) and a factory (see `create_tokenizer_by_name`)
- Various implementations of the tokenizer based on TikToken

## Our tokenizers have a vocabulary bound to them

Some libraries separate out of the tokenizer algorithm from the vocabulary. This library combines the two.

The name of a tokenizer implies a vocabulary.

## Our tokenizers have a special token class bound to them

Most tokenizers' special token class should be compatible with the `RagSpecialTokens` protocol, since it defines all the necessary special tokens for our up-to-date completion model.

Depands on the need, some tokenizers' special token class is also compatible with the `RetrievalSpecialTokens` protocol, a subset special tokens used by our retrieval model.

## What is a tokenizer?

A tokenizer is used to present an input array of bytes to a model in larger chunks than just single bytes.

Tokenizers have a fixed vocabulary that convert sequence of bytes to integers. For example, the bytes of the ASCII string "apple" (0x61 0x70 0x70 0x6c 0x65) could be mapped to 4921.
