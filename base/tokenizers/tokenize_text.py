"""Simple tool to convert text into tokens."""

import argparse

import base.tokenizers


def main():
    """Main entry function."""
    parser = argparse.ArgumentParser()
    parser.add_argument("input")
    parser.add_argument("--tokenizer-name")
    parser.add_argument(
        "--generate-special-tokens",
        default=False,
        action="store_true",
        help="Tokenize special strings like <|endoftext|> as special tokens",
    )
    args = parser.parse_args()

    tokenizer = base.tokenizers.create_tokenizer_by_name(args.tokenizer_name)
    if args.generate_special_tokens:
        tokens = tokenizer.tokenize_unsafe(args.input)
    else:
        tokens = tokenizer.tokenize_safe(args.input)
    print(",".join(str(t) for t in tokens))


if __name__ == "__main__":
    main()
