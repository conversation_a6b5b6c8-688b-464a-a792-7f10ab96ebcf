"""Tiktoken-based tokenizer for Qwen3."""

import json
import pathlib
from pathlib import Path
from typing import Mapping, Sequence, Tuple

from base.tokenizers import data_gym
from base.tokenizers.core_bpe import CoreBPE
from base.tokenizers.tokenizer import (
    REGISTRY,
    NextEditGenSpecialTokens,
    RetrievalSpecialTokens,
    Tokenizer,
)


def _read_special(special_token_path: Path) -> list[Tuple[str, int]]:
    with special_token_path.open(encoding="utf-8") as token_file:
        tokens = json.load(token_file)
    return tokens


class Qwen3SpecialTokens(NextEditGenSpecialTokens, RetrievalSpecialTokens):
    """Special tokens for the Qwen3 model and Qwen3 Embedding 0.6B model."""

    def __init__(self, tokenizer: Tokenizer):
        super().__init__()
        vocab = tokenizer.vocab

        # Generic special tokens
        self.padding: int = vocab[b"<|padding|>"]
        self.newline: int = vocab[b"\n"]

        # Chat Special Tokens
        self.im_start: int = 151644  # <|im_start|>
        self.im_end: int = 151645  # <|im_end|>

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<|retrieval_section|>"]
        self.ret_start: int = vocab[b"<|ret_start|>"]
        self.ret_body: int = vocab[b"<|ret_body|>"]
        self.prefix_body: int = vocab[b"<|prefix_body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig_lookup|>"]
        self.sig_begin: int = vocab[b"<|sig_begin|>"]
        self.sig_end: int = vocab[b"<|sig_end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]

        # Qwen3 Special Tokens
        self.eos: int = vocab[b"<|endoftext|>"]
        self.fim_prefix: int = vocab[b"<|fim_prefix|>"]
        self.fim_middle: int = vocab[b"<|fim_middle|>"]
        self.fim_suffix: int = vocab[b"<|fim_suffix|>"]
        self.fim_pad: int = vocab[b"<|fim_pad|>"]
        self.reponame: int = vocab[b"<|repo_name|>"]
        self.filename: int = vocab[b"<|file_sep|>"]
        self.tool_call: int = vocab[b"<tool_call>"]
        self.tool_call_end: int = vocab[b"</tool_call>"]
        self.tool_response: int = vocab[b"<tool_response>"]
        self.tool_response_end: int = vocab[b"</tool_response>"]
        self.think: int = vocab[b"<think>"]
        self.think_end: int = vocab[b"</think>"]

        # Next Edit Tokens
        self.instruction: int = vocab[b"<|instruction|>"]
        self.selected_code: int = vocab[b"<|selected_code|>"]
        self.diff_section: int = vocab[b"<|diff_section|>"]
        self.diff_hunk: int = vocab[b"<|diff_hunk|>"]
        self.has_change: int = vocab[b"<|has_change|>"]
        self.no_change: int = vocab[b"<|no_change|>"]

        # Retrieval Special Tokens
        self.start_of_key: int = vocab[b"<|ret_startofkey|>"]
        self.end_of_query: int = vocab[b"<|ret_endofquery|>"]
        self.end_of_key: int = vocab[b"<|ret_endofkey|>"]

        # Preference Token
        self.good: int = vocab[b"<|good_example|>"]
        self.bad: int = vocab[b"<|bad_example|>"]
        self.reward_signal: int = vocab[b"<|reward-signal|>"]

        self.begin_sequence = ()


def _get_additional_special_tokens():
    return [
        "<|padding|>",
        "<|skip|>",
        "<|pause|>",
        "<|retrieval_section|>",
        "<|ret_start|>",
        "<|ret_body|>",
        "<|prefix_body|>",
        "<|nearby_prefix|>",
        "<|nearby_suffix|>",
        "<|sig_lookup|>",
        "<|sig_begin|>",
        "<|sig_end|>",
        "<|signature_section|>",
        "<|far_prefix|>",
        "<|far_suffix|>",
        "<|instruction|>",
        "<|selected_code|>",
        "<|diff_section|>",
        "<|diff_hunk|>",
        "<|has_change|>",
        "<|no_change|>",
        "<|good_example|>",
        "<|bad_example|>",
        "<|ret_startofkey|>",
        "<|ret_endofquery|>",
        "<|ret_endofkey|>",
        "<|reward-signal|>",
    ]


def _read_vocab():
    """Read the vocab file."""
    module_dir = pathlib.Path(__file__).parent
    vocab_file = pathlib.Path(module_dir, "qwen3_vocab.json")
    assert vocab_file.exists() and vocab_file.is_file(), f"{vocab_file} does not exist."
    with vocab_file.open(encoding="utf-8") as vocab_file:
        all_contents = json.load(vocab_file)
        vocab: dict[str, int] = all_contents["model"]["vocab"]
        special_tokens: dict[str, int] = {
            x["content"]: x["id"] for x in all_contents["added_tokens"]
        }
        set_of_vocab_ids = set(vocab.values())
        set_of_all_ids = set(vocab.values()) | set(special_tokens.values())
        assert len(set_of_vocab_ids) == len(vocab)
        assert len(set_of_all_ids) == len(vocab) + len(special_tokens)
        assert 0 in set_of_vocab_ids and len(set_of_vocab_ids) - 1 in set_of_vocab_ids
        assert 0 in set_of_all_ids and len(set_of_all_ids) - 1 in set_of_all_ids
    # Update the special tokens
    for token in _get_additional_special_tokens():
        special_tokens[token] = len(vocab) + len(special_tokens)
    # Byte sequences corresponding to tokens are data gym in vocab.json
    decoder = data_gym.DataGymDecoder()
    decode_data_gym = decoder.decode
    bpe_ranks: dict[bytes, int] = {}
    for gym, token_id in vocab.items():
        bpe_ranks[decode_data_gym(gym)] = token_id
    special_ranks: dict[str, int] = {}
    for token, token_id in special_tokens.items():
        assert token not in special_ranks
        special_ranks[token] = token_id
    return bpe_ranks, special_ranks


# Copied from https://huggingface.co/Qwen/Qwen3-32B/blob/main/tokenizer.json
PAT_STR = r"""(?i:'s|'t|'re|'ve|'m|'ll|'d)|[^\r\n\p{L}\p{N}]?\p{L}+|\p{N}| ?[^\s\p{L}\p{N}]+[\r\n]*|\s*[\r\n]+|\s+(?!\S)|\s+"""


class Qwen3Tokenizer(Tokenizer):
    """Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    _vocab: dict[bytes, int]

    def __init__(self):
        """Initialize the tokenizer from the JSON vocab file."""
        basic_vocab, special_tokens = _read_vocab()
        self.core_bpe = CoreBPE(basic_vocab, special_tokens, PAT_STR)

        self._vocab_bytes2id = {b: v for (b, v) in basic_vocab.items()}
        for token, _id in special_tokens.items():
            cur_bytes = token.encode()
            if cur_bytes in self._vocab_bytes2id:
                raise ValueError(
                    f"The token {token} ({cur_bytes}) is already in the vocab as"
                    f" {self._vocab_bytes2id[cur_bytes]}. Please check the vocab file."
                )
            self._vocab_bytes2id[cur_bytes] = _id
        self._raw_basic_vocab = basic_vocab
        self._raw_special_tokens = special_tokens
        self._special_set = set(special_tokens.keys())
        self._special_tokens = Qwen3SpecialTokens(self)

    @property
    def vocab_size(self):
        return len(self._vocab_bytes2id)

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return self._vocab_bytes2id

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=set())

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self._special_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> Qwen3SpecialTokens:
        """Returns the special tokens for Qwen3."""
        return self._special_tokens


REGISTRY.add("qwen3", Qwen3Tokenizer)


class Qwen3EmbeddingSpecialTokens(NextEditGenSpecialTokens, RetrievalSpecialTokens):
    """Special tokens for the Qwen3 Embedding 4B/8B model."""

    def __init__(self, tokenizer: Tokenizer):
        super().__init__()
        vocab = tokenizer.vocab

        # Generic special tokens
        self.padding: int = vocab[b"<|padding|>"]
        self.newline: int = vocab[b"\n"]

        # Chat Special Tokens
        self.im_start: int = 151644  # <|im_start|>
        self.im_end: int = 151645  # <|im_end|>

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<|retrieval_section|>"]
        self.ret_start: int = vocab[b"<|ret_start|>"]
        self.ret_body: int = vocab[b"<|ret_body|>"]
        self.prefix_body: int = vocab[b"<|prefix_body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig_lookup|>"]
        self.sig_begin: int = vocab[b"<|sig_begin|>"]
        self.sig_end: int = vocab[b"<|sig_end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]

        # Qwen3 Special Tokens
        self.eos: int = vocab[b"<|endoftext|>"]
        self.fim_prefix: int = vocab[b"<|fim_prefix|>"]
        self.fim_middle: int = vocab[b"<|fim_middle|>"]
        self.fim_suffix: int = vocab[b"<|fim_suffix|>"]
        self.fim_pad: int = vocab[b"<|fim_pad|>"]
        self.reponame: int = vocab[b"<|repo_name|>"]
        self.filename: int = vocab[b"<|file_sep|>"]
        self.tool_call: int = vocab[b"<tool_call>"]
        self.tool_call_end: int = vocab[b"</tool_call>"]

        # Next Edit Tokens
        self.instruction: int = vocab[b"<|instruction|>"]
        self.selected_code: int = vocab[b"<|selected_code|>"]
        self.diff_section: int = vocab[b"<|diff_section|>"]
        self.diff_hunk: int = vocab[b"<|diff_hunk|>"]
        self.has_change: int = vocab[b"<|has_change|>"]
        self.no_change: int = vocab[b"<|no_change|>"]

        # Retrieval Special Tokens
        self.start_of_key: int = vocab[b"<|ret_startofkey|>"]
        self.end_of_query: int = vocab[b"<|ret_endofquery|>"]
        self.end_of_key: int = vocab[b"<|ret_endofkey|>"]

        # Preference Token
        self.good: int = vocab[b"<|good_example|>"]
        self.bad: int = vocab[b"<|bad_example|>"]
        self.reward_signal: int = vocab[b"<|reward-signal|>"]

        self.begin_sequence = ()


def _read_embedding_vocab(vocab_filename: str):
    """Read the vocab file for embedding models."""
    module_dir = pathlib.Path(__file__).parent
    vocab_file = pathlib.Path(module_dir, vocab_filename)
    assert vocab_file.exists() and vocab_file.is_file(), f"{vocab_file} does not exist."
    with vocab_file.open(encoding="utf-8") as vocab_file:
        all_contents = json.load(vocab_file)
        vocab: dict[str, int] = all_contents["model"]["vocab"]
        special_tokens: dict[str, int] = {
            x["content"]: x["id"] for x in all_contents["added_tokens"]
        }
        set_of_vocab_ids = set(vocab.values())
        set_of_all_ids = set(vocab.values()) | set(special_tokens.values())
        assert len(set_of_vocab_ids) == len(vocab)
        assert len(set_of_all_ids) == len(vocab) + len(special_tokens)
        assert 0 in set_of_vocab_ids and len(set_of_vocab_ids) - 1 in set_of_vocab_ids
        assert 0 in set_of_all_ids and len(set_of_all_ids) - 1 in set_of_all_ids
    # Update the special tokens
    for token in _get_additional_special_tokens():
        special_tokens[token] = len(vocab) + len(special_tokens)
    # Byte sequences corresponding to tokens are data gym in vocab.json
    decoder = data_gym.DataGymDecoder()
    decode_data_gym = decoder.decode
    bpe_ranks: dict[bytes, int] = {}
    for gym, token_id in vocab.items():
        bpe_ranks[decode_data_gym(gym)] = token_id
    special_ranks: dict[str, int] = {}
    for token, token_id in special_tokens.items():
        assert token not in special_ranks
        special_ranks[token] = token_id
    return bpe_ranks, special_ranks


class Qwen3EmbeddingTokenizer(Qwen3Tokenizer):
    """Unified tokenizer for all Qwen3-Embedding models.

    Supports both 0.6B (with 26 special tokens) and 4B/8B (with 22 special tokens) variants.
    The main differences between variants:
    - 0.6B: Uses full Qwen3SpecialTokens (includes think/tool_response tokens)
    - 4B/8B: Uses reduced Qwen3EmbeddingSpecialTokens (missing 4 tokens)

    Important: Qwen3-Embedding models extract embeddings from the final <|endoftext|>
    token position. The tokenizer automatically appends this token to match the
    model's training format. Do NOT remove this token before feeding to the model.

    The models use left-padding for batch processing, ensuring all EOS tokens
    align at the rightmost position for consistent embedding extraction.
    """

    def __init__(self, model_variant: str = "4b"):
        """Initialize the tokenizer for the specified model variant.

        Args:
            model_variant: One of "0.6b", "4b", "8b". Defaults to "4b".
        """
        # Configuration based on model variant
        if model_variant == "0.6b":
            vocab_file = "qwen3_embedding_0.6b_vocab.json"
            special_tokens_class = Qwen3SpecialTokens
        elif model_variant in ["4b", "8b"]:
            vocab_file = "qwen3_embedding_vocab.json"
            special_tokens_class = Qwen3EmbeddingSpecialTokens
        else:
            raise ValueError(
                f"Unknown model variant: {model_variant}. Must be one of: 0.6b, 4b, 8b"
            )

        # Load vocab for the specific variant
        basic_vocab, special_tokens = _read_embedding_vocab(vocab_file)
        self.core_bpe = CoreBPE(basic_vocab, special_tokens, PAT_STR)

        # Common initialization code
        self._vocab_bytes2id = {b: v for (b, v) in basic_vocab.items()}
        for token, _id in special_tokens.items():
            cur_bytes = token.encode()
            if cur_bytes in self._vocab_bytes2id:
                raise ValueError(
                    f"The token {token} ({cur_bytes}) is already in the vocab as"
                    f" {self._vocab_bytes2id[cur_bytes]}. Please check the vocab file."
                )
            self._vocab_bytes2id[cur_bytes] = _id
        self._raw_basic_vocab = basic_vocab
        self._raw_special_tokens = special_tokens
        self._special_set = set(special_tokens.keys())
        self._special_tokens = special_tokens_class(self)
        self._auto_add_eos = True  # All embedding models add EOS automatically
        self._model_variant = model_variant

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens.

        For embedding models, automatically appends <|endoftext|> token.
        This is REQUIRED - the model extracts embeddings from the EOS token position.

        Note: detokenize() will include this token in the output.
        """
        tokens = super().tokenize_safe(text)
        if self._auto_add_eos and tokens and tokens[-1] != self._special_tokens.eos:
            tokens.append(self._special_tokens.eos)
        return tokens

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens.

        For embedding models, automatically appends <|endoftext|> token.
        This is REQUIRED - the model extracts embeddings from the EOS token position.

        Note: detokenize() will include this token in the output.
        """
        tokens = super().tokenize_unsafe(text)
        if self._auto_add_eos and tokens and tokens[-1] != self._special_tokens.eos:
            tokens.append(self._special_tokens.eos)
        return tokens

    def strip_eos_token(self, token_ids: list[int]) -> list[int]:
        """Remove trailing EOS token if present.

        WARNING: Do NOT use this before feeding tokens to the embedding model!
        The model requires the EOS token to extract embeddings properly.

        This method is only useful for text reconstruction when you want to
        recover the original text without the automatically added <|endoftext|>.

        Example:
            tokens = tokenizer.tokenize_safe("Hello")  # Adds EOS
            # For model: use tokens directly (with EOS)
            # For text recovery: use strip_eos_token first
            original_text = tokenizer.detokenize(tokenizer.strip_eos_token(tokens))
        """
        if token_ids and token_ids[-1] == self._special_tokens.eos:
            return token_ids[:-1]
        return token_ids

    @property
    def special_tokens(self) -> Qwen3SpecialTokens:
        """Returns the special tokens for this embedding model variant."""
        return self._special_tokens  # type: ignore[return-value]


# Register all tokenizer variants using the unified tokenizer
REGISTRY.add("qwen3-embedding-0.6b", lambda: Qwen3EmbeddingTokenizer("0.6b"))
REGISTRY.add("qwen3-embedding-4b", lambda: Qwen3EmbeddingTokenizer("4b"))
REGISTRY.add("qwen3-embedding-8b", lambda: Qwen3EmbeddingTokenizer("8b"))
REGISTRY.add(
    "qwen3-embedding", lambda: Qwen3EmbeddingTokenizer("4b")
)  # Default alias for 4B
