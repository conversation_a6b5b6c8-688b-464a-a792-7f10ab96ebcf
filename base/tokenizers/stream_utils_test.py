"""Tests for token stream utilities."""

import pytest

from base.tokenizers.stream_utils import detokenize_stream
from base.tokenizers.test_utils import MultiTokenTestTokenizer


def test_detokenize_stream_basic():
    """Test basic streaming functionality."""
    tokenizer = MultiTokenTestTokenizer()
    tokens = [[65, 66], [67, 68]]  # "AB", "CD"

    result = list(detokenize_stream(iter(tokens), tokenizer))
    assert result == ["AB", "CD"]


def test_detokenize_stream_with_end_tokens():
    """Test streaming with end tokens."""
    tokenizer = MultiTokenTestTokenizer()
    tokens = [[65, -2, 66], [67, -2]]  # "A<|eos|>B", "C<|eos|>"

    result = list(detokenize_stream(iter(tokens), tokenizer, end_token_ids={-2}))
    assert result == ["AB", "C"]


def test_detokenize_stream_multi_token_chars():
    """Test handling of multi-token characters."""
    TEST_STRING = "The Mac command key is ⌘, and a random high unicode character is 覉"
    tokenizer = MultiTokenTestTokenizer()

    # Split the test string into chunks to simulate streaming
    tokens = [tokenizer.tokenize_safe(TEST_STRING[:10])]
    tokens.append(tokenizer.tokenize_safe(TEST_STRING[10:20]))
    tokens.append(tokenizer.tokenize_safe(TEST_STRING[20:]))

    result = "".join(detokenize_stream(iter(tokens), tokenizer))
    assert "⌘" in result
    assert "覉" in result
    assert "\ufffd" not in result  # No replacement characters in final output
