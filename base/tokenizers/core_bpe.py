from collections.abc import Mapping, Sequence
from dataclasses import dataclass

from base import feature_flags
from base.tokenizers.tiktoken import CoreBPE as _CoreBPE

_FORCE_REGEX_PRETOKENIZER = feature_flags.BoolFlag("force_regex_pretokenizer", False)


@dataclass
class CoreBPE:
    """A thin wrapper around tiktoken's CoreBPE library.

    This wrapper provides handles serialization so that tokenizers can be shared between
    processes. As a bonus, it provides some type hints as well.
    """

    def __init__(
        self,
        encoder: Mapping[bytes, int],
        special_tokens_encoder: Mapping[str, int],
        pattern: str,
    ):
        """Create the BPE implementation.

        Args:
            encoder: A mapping from bytes to token id for the encoder.
            special_tokens_encoder: A mapping from _strings_ to token ids for the
                encoder.
            pattern: The regex pattern used to split heuristically text before
                tokenization.
        """
        self.encoder = encoder
        self.special_tokens_encoder = special_tokens_encoder
        self.pattern = pattern
        self._core_bpe = _CoreBPE(
            self.encoder,
            self.special_tokens_encoder,
            self.pattern,
        )

    def __getstate__(self) -> dict:
        """Get the serializable state of the tokenizer. Used in __setstate__."""
        return {
            "encoder": self.encoder,
            "special_tokens_encoder": self.special_tokens_encoder,
            "pattern": self.pattern,
        }

    def __setstate__(self, state: dict):
        self.encoder = state["encoder"]
        self.special_tokens_encoder = state["special_tokens_encoder"]
        self.pattern = state["pattern"]
        self._core_bpe = _CoreBPE(
            self.encoder,
            self.special_tokens_encoder,
            self.pattern,
        )

    def encode(self, text: str, allowed_special: set[str]) -> list[int]:
        """Encode a text into a set of tokens.

        Args:
            text: The text to encode.
            allowed_special: A set of special tokens that are allowed to be
                encoded.

        Returns:
            Token ids for the encoded text.
        """
        context = feature_flags.get_global_context()
        return self._core_bpe.encode(
            text,
            allowed_special,
            _FORCE_REGEX_PRETOKENIZER.get(context),
        )

    def decode_bytes(self, tokens: Sequence[int]) -> bytes:
        """Decode a list of token ids to bytes.

        Args:
            tokens: The list of token ids to decode.

        Returns:
            The decoded tokens in _bytes_.
        """
        return self._core_bpe.decode_bytes(tokens)

    def decode_with_offsets(self, tokens: Sequence[int]) -> tuple[str, list[int]]:
        """Decode a list of tokens into a string with start character offsets.

        Args:
            tokens: The list of token ids to decode.

        Returns:
            A tuple with the decoded text as a UTF-8 string, and the start
            character offsets for each token id. For token ids that represent part of a
            UTF-8 character, the offset is the start of that character.
        """
        return self._core_bpe.decode_with_offsets(tokens)
