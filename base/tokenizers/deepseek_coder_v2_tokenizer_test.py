"""Tests for the DeepSeek-Coder-V2 tokenizer."""

import pytest

from base.tokenizers.deepseek_coder_v2_tokenizer import (
    DeepSeekCoderV2SpecialTokens,
    DeepSeekCoderV2Tokenizer,
)


def test_special_tokens_instruct():
    """Test special tokens API for DeepSeek-Coder-V2 tokenizer."""
    t = DeepSeekCoderV2Tokenizer()
    specials = t.special_tokens
    assert isinstance(specials, DeepSeekCoderV2SpecialTokens)
    assert specials.begin_sequence == (100000,)
    assert specials.fim_prefix == 100003
    assert specials.fim_middle == 100004
    assert specials.fim_suffix == 100002
    assert specials.eos == 100001
    assert specials.padding == 100001
    assert specials.newline == 185
    assert specials.skip == 100018
    assert specials.pause == 100019
    assert specials.retrieval_section == 100020
    assert specials.ret_start == 100021
    assert specials.ret_body == 100022
    assert specials.prefix_body == 100023
    assert specials.nearby_prefix == 100024
    assert specials.nearby_suffix == 100025
    assert specials.sig_lookup == 100026
    assert specials.sig_begin == 100027
    assert specials.sig_end == 100028
    assert specials.signature_section == 100029
    assert specials.filename == 100030
    assert specials.far_prefix == 100031
    assert specials.far_suffix == 100032
    assert specials.start_of_key == 100033
    assert specials.end_of_query == 100034
    assert specials.end_of_key == 100035


def test_basic():
    """Sanity test for DeepSeek-Coder-V2 tokenizer."""
    t = DeepSeekCoderV2Tokenizer()
    # Test the vocab size
    assert t.vocab_size == 100036


@pytest.mark.parametrize(
    "text, expected_tokens",
    [
        pytest.param("123", [16, 17, 18], id="123"),
        pytest.param("董宣毅", [14514, 7126, 32751], id="董宣毅"),
        pytest.param(" ", [207], id="space"),
        pytest.param("  ", [243], id="double space"),
        pytest.param("   ", [300], id="triple space"),
        pytest.param("    ", [251], id="quadruple space"),
        pytest.param("\n", [185], id="newline"),
        pytest.param("\n\n", [185, 185], id="newline"),
    ],
)
def test_simple_case(text: str, expected_tokens: list[int]):
    t = DeepSeekCoderV2Tokenizer()
    tokens = t.tokenize_unsafe(text)
    assert tokens == expected_tokens
    actual_text = t.detokenize(tokens)
    assert actual_text == text


@pytest.mark.parametrize(
    "text, safe_tokens, unsafe_tokens",
    [
        pytest.param("<|skip|>", [27, 91, 7463, 66325], [100018], id="<|skip|>"),
        pytest.param("<|pause|>", [27, 91, 42980, 66325], [100019], id="<|pause|>"),
        pytest.param("i love u", [72, 2126, 2644], [72, 2126, 2644], id="iou"),
        pytest.param(
            "<|filename|>src/file.py",
            [27, 91, 17396, 66325, 7362, 14, 2810, 13, 4027],
            [100030, 7362, 14, 2810, 13, 4027],
            id="<|filename|>src/file.py",
        ),
    ],
)
def test_safe_unsafe_calls(text: str, safe_tokens: list[int], unsafe_tokens: list[int]):
    """Test the tokenize_unsafe and tokenize_safe functions."""
    t = DeepSeekCoderV2Tokenizer()
    assert t.tokenize_safe(text) == safe_tokens
    assert t.tokenize_unsafe(text) == unsafe_tokens
    assert t.detokenize(safe_tokens) == text
    assert t.detokenize(unsafe_tokens) == text
