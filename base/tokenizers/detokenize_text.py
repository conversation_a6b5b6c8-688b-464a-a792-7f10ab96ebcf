"""Simple tool to convert tokens in text."""

import argparse
import base.tokenizers


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("input")
    parser.add_argument("-s", "--separator", default=",")
    parser.add_argument("--tokenizer-name")

    args = parser.parse_args()

    input_tokens = [
        int(t.strip()) for t in args.input.split(args.separator) if t.strip()
    ]

    tokenizer = base.tokenizers.create_tokenizer_by_name(args.tokenizer_name)
    print(tokenizer.detokenize(input_tokens))


if __name__ == "__main__":
    main()
