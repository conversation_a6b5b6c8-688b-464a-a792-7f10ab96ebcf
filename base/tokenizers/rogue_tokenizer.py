"""Tokenizers for rogue model."""

from base.tokenizers import tiktoken_starcoder_tokenizer, tokenizer

# Keeping this name since other parts of the code depend it.
RogueSpecialTokens = tiktoken_starcoder_tokenizer.StarCoderSpecialTokens


def create() -> tokenizer.Tokenizer:
    """Create a tokenizer with the Rogue vocabulary."""
    return tiktoken_starcoder_tokenizer.TiktokenStarCoderTokenizer()


tokenizer.REGISTRY.add("rogue", create)
