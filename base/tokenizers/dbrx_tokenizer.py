"""Module containing a tiktoken-based tokenizer implementation using the DBRX Instruct vocabulary."""

import json
import pathlib
from typing import Mapping, Sequence

from base.tokenizers import data_gym
from base.tokenizers.core_bpe import CoreBPE
from base.tokenizers.tokenizer import REGISTRY, RagSpecialTokens, Tokenizer


class DBRXSpecialTokens(RagSpecialTokens):
    """Special tokens for the DBRX-Instruct model."""

    def __init__(self, tokenizer: Tokenizer):
        super().__init__()
        vocab = tokenizer.vocab

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<|retrieval_section|>"]
        self.ret_start: int = vocab[b"<|ret-start|>"]
        self.ret_body: int = vocab[b"<|ret-body|>"]
        self.prefix_body: int = vocab[b"<|prefix-body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig-lookup|>"]
        self.sig_begin: int = vocab[b"<|sig-begin|>"]
        self.sig_end: int = vocab[b"<|sig-end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]
        self.filename: int = vocab[b"<filename>"]
        # New line token is so commonly used, we add it to the special tokens
        self.newline: int = vocab[b"\n"]
        # Built-in special tokens from DBRX Instruct models
        self.eos: int = vocab[b"<|endoftext|>"]
        self.fim_prefix: int = vocab[b"<|fim_prefix|>"]  # should be 100258
        self.fim_middle: int = vocab[b"<|fim_middle|>"]  # should be 100259
        self.fim_suffix: int = vocab[b"<|fim_suffix|>"]  # should be 100260
        self.endofprompt: int = vocab[b"<|endofprompt|>"]  # should be 100276
        self.padding: int = vocab[b"<|pad|>"]  # should be 100277
        self.im_start: int = vocab[b"<|im_start|>"]  # should be 100278
        self.im_end: int = vocab[b"<|im_end|>"]  # should be 100279

        self.begin_sequence = ()


def _get_additional_special_tokens():
    return [
        "<|skip|>",
        "<|pause|>",
        "<|retrieval_section|>",
        "<|ret-start|>",
        "<|ret-body|>",
        "<|prefix-body|>",
        "<|nearby_prefix|>",
        "<|sig-lookup|>",
        "<|sig-begin|>",
        "<|sig-end|>",
        "<|nearby_suffix|>",
        "<|signature_section|>",
        "<filename>",
        "<|far_prefix|>",
        "<|far_suffix|>",
    ]


def _read_vocab():
    """Read the vocab file."""
    module_dir = pathlib.Path(__file__).parent
    vocab_file = pathlib.Path(module_dir, "dbrx_instruct_vocab.json")
    assert vocab_file.exists() and vocab_file.is_file(), f"{vocab_file} does not exist."
    with vocab_file.open(encoding="utf-8") as vocab_file:
        all_contents = json.load(vocab_file)
        vocab: dict[str, int] = all_contents["model"]["vocab"]
        special_tokens: dict[str, int] = {
            x["content"]: x["id"] for x in all_contents["added_tokens"]
        }
        # Some special tokens are also in the vocab, and we need to pop them out
        for special_token, special_token_id in special_tokens.items():
            if special_token in vocab:
                assert special_token_id == vocab[special_token]
                vocab.pop(special_token)
        set_of_vocab_ids = set(vocab.values())
        set_of_special_ids = set(special_tokens.values())
        set_of_all_ids = set_of_vocab_ids | set_of_special_ids
        assert len(set_of_vocab_ids) == len(vocab)
        assert len(set_of_all_ids) == len(vocab) + len(special_tokens)
        for token_id in range(len(vocab)):
            assert token_id in set_of_vocab_ids
        for token_id in range(len(set_of_all_ids)):
            assert token_id in set_of_all_ids
        for token_id in range(len(vocab), len(set_of_all_ids)):
            assert token_id in set_of_special_ids
    # Add our in-house special tokens
    for idx, token in enumerate(_get_additional_special_tokens()):
        special_tokens[token] = len(set_of_all_ids) + idx
    # Byte sequences corresponding to tokens are data gym in vocab.json
    decoder = data_gym.DataGymDecoder()
    decode_data_gym = decoder.decode
    bpe_ranks: dict[bytes, int] = {}
    for gym, token_id in vocab.items():
        bpe_ranks[decode_data_gym(gym)] = token_id
    special_ranks: dict[str, int] = {}
    for token, token_id in special_tokens.items():
        assert token not in special_ranks
        special_ranks[token] = token_id
    return bpe_ranks, special_ranks


class DBRXInstructTokenizer(Tokenizer):
    """DBRX Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    RE_PATTERN_STR = r"""'(?i:[sdmt]|ll|ve|re)|[^\r\n\p{L}\p{N}]?+\p{L}+|\p{N}{1,3}| ?[^\s\p{L}\p{N}]++[\r\n]*|\s*[\r\n]|\s+(?!\S)|\s+"""
    # Note(Xuanyi): this is the GPT-4 tokenizer pattern borrowed from
    # https://github.com/openai/tiktoken/blob/1b9faf2779855124f05174adf1383e53689ed94b/tiktoken_ext/openai_public.py#L83-L88.

    def __init__(self):
        """Initialize the tokenizer from the JSON vocab file."""
        basic_vocab, special_tokens = _read_vocab()
        self.core_bpe = CoreBPE(basic_vocab, special_tokens, self.RE_PATTERN_STR)

        self._vocab_bytes2id = {b: v for (b, v) in basic_vocab.items()}
        for token, _id in special_tokens.items():
            assert (
                token.encode() not in self._vocab_bytes2id
            ), f"{token} is already in the vocab."
            self._vocab_bytes2id[token.encode()] = _id
        self._raw_basic_vocab = basic_vocab
        self._raw_special_tokens = special_tokens
        self._special_set = set(special_tokens.keys())
        self._special_tokens = DBRXSpecialTokens(self)

    @property
    def vocab_size(self):
        return len(self._vocab_bytes2id)

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return self._vocab_bytes2id

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=set())

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self._special_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> DBRXSpecialTokens:
        """Returns the special tokens for dbrx-*."""
        return self._special_tokens


REGISTRY.add("dbrx_instruct", DBRXInstructTokenizer)
