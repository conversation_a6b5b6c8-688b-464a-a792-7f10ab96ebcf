load("@rules_proto//proto:defs.bzl", "proto_library")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library", "py_proto_library")

# Proto library for memory definitions
proto_library(
    name = "memory_proto",
    srcs = ["memory.proto"],
    visibility = BASE_VISIBILITY,
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

# Python proto bindings
py_proto_library(
    name = "memory_py_proto",
    protos = [":memory_proto"],
    visibility = BASE_VISIBILITY,
)

# Core memory engine library
py_library(
    name = "core",
    srcs = [
        "core.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        ":memory_py_proto",
        "//base/prompt_format:common",
    ],
)

# Main memory engine library
py_library(
    name = "memory_engine",
    srcs = [
        "__init__.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        ":core",
        ":memory_py_proto",
    ],
)
