"""The core interfaces for the memory engine."""

from collections.abc import Sequence
from typing import Protocol, TypeVar

from base.memory_engine.memory_pb2 import Memory as MemoryProto
from base.prompt_format.common import Exchange

T = TypeVar("T", contravariant=True)


class MemoryIndex(Protocol):
    """A memory index provides a CRUD interface over a memory datastore."""

    def add(self, memories: Sequence[MemoryProto]) -> None:
        """Add a sequence of memories to the index."""
        raise NotImplementedError()

    def remove(self, memory_ids: Sequence[str]) -> None:
        """Remove a sequence of memories from the index."""
        raise NotImplementedError()

    def get(self, memory_ids: Sequence[str]) -> Sequence[MemoryProto]:
        """Get a sequence of memories from the index."""
        raise NotImplementedError()

    def query(self, query: str) -> Sequence[MemoryProto]:
        """Query the index for a sequence of memories."""
        raise NotImplementedError()

    def update(self, memories: Sequence[MemoryProto]) -> None:
        """Update a sequence of memories in the index."""
        raise NotImplementedError()


class MemoryExtractor(Protocol[T]):
    """A memory extractor extracts memories from a given input."""

    def extract(self, input: T) -> Sequence[MemoryProto]:
        return []


class MemorySearcher(Protocol):
    """A memory searcher provides a search interface over a memory datastore."""

    def search(
        self, query: str, chat_history: Sequence[Exchange]
    ) -> Sequence[MemoryProto]:
        raise NotImplementedError()
