// Protobuf definition for a memory.
// WARNING: This proto is not yet stable -- no data should be stored on disk atm.

syntax = "proto3";

package memory_engine;

import "google/protobuf/timestamp.proto";

message Memory {
  // UUID for this memory.
  string id = 1;

  // Describe a general preference or rule that will be useful
  // when performing tasks in the future. (e.g., "use _min_version instead of
  // _enabled for client-side feature flags.")
  string content = 2;

  // Describes the context in which the memory is relevant.
  MemoryContext context = 3;

  // Where the memory originated from.
  oneof origin {
    // Has conversation ID, userID etc.
    ConversationOrigin conversation_origin = 4;
    // Future: PROrigin, CodebaseOrigin, etc.
  }

  // When the memory was created.
  google.protobuf.Timestamp created_at = 5;
  // When the memory was last updated.
  google.protobuf.Timestamp last_updated_at = 6;
  // When the memory "expires".
  google.protobuf.Timestamp expires_at = 7;

  // Features for this memory.
  MemoryFeatures features = 8;

  // Describes how this memory was used.
  repeated MemoryUse uses = 9;

  // If set, this memory has been subsumed by the given memory.
  string subsumed_by_id = 10;
}

message MemoryContext {
  // Describes the context in which the memory is relevant (e.g.,
  // when implementing feature flags, when running tests, when working on the
  // content manager, etc.)
  string scope = 1;

  // If non-empty, which paths this memory is relevant in.
  repeated string paths = 2;
}

message MemoryFeatures {
  // How often was this memory retrieved?
  int32 retrieved_count = 1;
  // How often was this memory found useful by the model?
  int32 used_count = 2;
  // How often the memory was rated positively by a user.
  int32 rating_positive = 3;
  // How often the memory was rated negatively by a user.
  int32 rating_negative = 4;
}

message MemoryUse {
  string memory_id = 1;
  string conversation_id = 2;
  bool was_useful = 3;
  // TODO: Add more fields regarding user_id, etc.
}

message ConversationOrigin {
  string conversation_id = 1;
  string request_id = 2;
  // TODO: Add more fields regarding user_id, etc.
}
