# Memory engine

Implements the core functionality of Augment's memory engine: a system that extracts and
maintains a collection of "memories" from experiences of the agent.

# Concepts

## MemoryIndex

- Provides a CRUD like interface over an underlying datastore with methods to
    - Create a new memory.
    - Retrieve memories given a text query.
    - Update memories.
    - Delete memories.
- There is a refrence in-memory implementation for prototyping and testing purposes.
- The production implementation is yet to be implemented and will be in `services`.

## MemoryExtractor

- Responsible for extracting and updating memories from a source.
- Each source will require custom prompting to extract memories and we will have
  multiple extractor implementations.
- When extracting memories, the index may be queried to see if there are duplicates or
  if existing memories need to be updated / deprecated.
- In practice, the extractor will be called asynchronously, perhaps through a pub/sub queue.

## MemoryRetrieval

- Responsible for ranking and filtering memories retrieved from the index based on
  relevance to context.
- The ranker includes heuristics for whether to consider a memory at all. One heuristic
  is to only consider memories relevant to a user; another is to also include memories
  that were relevant to >1 user (”team memories”).
