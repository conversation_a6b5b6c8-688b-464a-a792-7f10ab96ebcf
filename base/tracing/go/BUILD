load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "tracing_go",
    srcs = ["init.go"],
    importpath = "github.com/augmentcode/augment/base/tracing/go",
    visibility = BASE_VISIBILITY,
    deps = [
        "@com_github_rs_zerolog//log",
        "@io_opentelemetry_go_otel//:otel",
        "@io_opentelemetry_go_otel//propagation",
        "@io_opentelemetry_go_otel_exporters_otlp_otlptrace_otlptracegrpc//:otlptracegrpc",
        "@io_opentelemetry_go_otel_sdk//trace",
    ],
)
