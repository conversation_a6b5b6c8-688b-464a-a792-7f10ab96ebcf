package tracing

import (
	"context"
	"os"

	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/trace"
)

/*
 * This function sets up the OpenTelemetry environment for this process, and
 * returns a function that can be used to shutdown the provider.
 *
 * The most common way to set the pod up for this is to:
 * 1) Use telemetry-lib.jsonnet to add the right env variables
 * 2) Call `tracingShutdown := tracing.Init(); defer tracingShutdown()`
 * 3) Use `grpc.StatsHandler(otelgrpc.NewServerHandler()` when creating a gRPC server to
 *    automatically add spans on every RPC.
 */
func Init() func() {
	if !isTracingEnabled() {
		// No tracing enabled, so don't do anything
		return func() {}
	}

	option := otlptracegrpc.WithInsecure()
	exporter, err := otlptracegrpc.New(context.Background(),
		option)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating OTLP exporter")
	}
	tc := propagation.TraceContext{}
	otel.SetTextMapPropagator(tc)

	// Create trace provider with OTLP exporter
	tp := trace.NewTracerProvider(
		trace.WithBatcher(exporter),
	)

	// Register the trace provider as the global provider
	otel.SetTracerProvider(tp)
	return func() {
		if err := tp.Shutdown(context.Background()); err != nil {
			log.Error().Err(err).Msg("Error shutting down OTLP exporter")
		}
	}
}

func isTracingEnabled() bool {
	endpoint, exists := os.LookupEnv("OTEL_EXPORTER_OTLP_ENDPOINT")
	return exists && endpoint != ""
}
