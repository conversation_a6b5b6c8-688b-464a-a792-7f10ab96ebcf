load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

py_library(
    name = "cache",
    srcs = ["cache.py"],
    visibility = ["//visibility:public"],
)

py_library(
    name = "cache_metrics",
    srcs = ["cache_metrics.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":cache",
        requirement("prometheus_client"),
    ],
)

py_library(
    name = "lru_cache",
    srcs = ["lru_cache.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":cache",
        requirement("lru-dict"),
        requirement("pympler"),
        requirement("structlog"),
    ],
)

pytest_test(
    name = "lru_cache_test",
    srcs = ["lru_cache_test.py"],
    deps = [
        ":lru_cache",
    ],
)
