"""An abstract cache interface that supports batch queries."""

from __future__ import annotations

import typing

K = typing.TypeVar("K", contravariant=True)
V = typing.TypeVar("V", covariant=True)
P = typing.ParamSpec("P")


class InsertStats(typing.NamedTuple):
    """Statistics about the insert operation."""

    insertion_count: int
    """Number of entries we inserted during this call."""
    skip_count: int
    """Number of entries we skipped inserting during this call because of size constraints."""
    eviction_count: int
    """Number of entries we evicted during this call."""
    cache_size: int
    """The current size of the cache.

    Note that the units depend on the size function used by the cache; it could be bytes
    or entries.
    """
    entries: int
    """Number of entries currently stored in the cache."""


InsertListener = typing.Callable[[InsertStats], None]
"""Called when new items are inserted into the cache (once per batch)."""


class LookupStats(typing.NamedTuple):
    """Statistics about the lookup operation."""

    hits_count: int
    """Number of entries we found in the cache during this call."""
    misses_count: int
    """Number of entries we missed in the cache during this call."""


LookupListener = typing.Callable[[LookupStats], None]
"""Called when items are queried from the cache (once per batch)."""


class Cache(typing.Protocol[K, V, P]):
    """Interface for a generic cache."""

    def get(
        self, keys: typing.Iterable[K], *args: P.args, **kwargs: P.kwargs
    ) -> typing.Iterable[V | None]:
        """Retrieve content for a list of keys.

        Args:
            keys: a list of keys.
            args: arguments to pass to the get request.
            kwargs: keyword arguments to pass to the get request.

        Returns:
            a list of content corresponding to `keys` or None if content for that key
            could not be retrieved. The order of the elements returned must match those
            in `keys`.
        """
        raise NotImplementedError()

    def set_insert_listener(self, listener: InsertListener | None = None):
        """Set a listener for when new items are inserted into the cache.

        If `listener` is None, any existing listener will be removed.
        """
        raise NotImplementedError()

    def set_lookup_listener(self, listener: LookupListener | None = None):
        """Set a listener for when items are queried from the cache.

        If `listener` is None, any existing listener will be removed.
        """
        raise NotImplementedError()
