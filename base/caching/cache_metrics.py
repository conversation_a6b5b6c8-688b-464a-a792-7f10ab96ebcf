"""A helper to log metrics when using a content cache.

Usage:
>>> from base.caching.cache import Cache
>>> from base.cacing.cache_metrics import CacheMetrics
>>> _file_cache_metrics = CacheMetrics("au_host_file_cache", "File cache")
>>> ...
>>> def run():
>>>     file_cache: Cache = ...
>>>     _file_cache_metrics.setup_listeners(file_cache)
"""

from __future__ import annotations

from prometheus_client import Counter, Gauge

from base.caching.cache import Cache, InsertStats, LookupStats


class CacheMetrics:
    """Metrics for a cache."""

    def __init__(self, prefix: str, name: str):
        self.inserts_counter = Counter(f"{prefix}_inserts", f"{name} insertions")
        self.skips_counter = Counter(f"{prefix}_skips", f"{name} skipped insertions")
        self.deletes_counter = Counter(f"{prefix}_deletes", f"{name} deletions")
        self.lookups_counter = Counter(f"{prefix}_lookups", f"{name} lookups")
        self.hits_counter = Counter(f"{prefix}_hits", f"{name} cache hits")
        self.misses_counter = Counter(f"{prefix}_misses", f"{name} cache misses")
        self.memory_gauge = Gauge(f"{prefix}_memory", f"{name} memory size")
        self.entry_gauge = Gauge(f"{prefix}_entries", f"{name} entry count")

    def _on_insert(self, stats: InsertStats):
        self.inserts_counter.inc(stats.insertion_count)
        self.skips_counter.inc(stats.skip_count)
        self.deletes_counter.inc(stats.eviction_count)
        self.memory_gauge.set(stats.cache_size)
        self.entry_gauge.set(stats.entries)

    def _on_lookup(self, stats: LookupStats):
        self.lookups_counter.inc(stats.hits_count + stats.misses_count)
        self.hits_counter.inc(stats.hits_count)
        self.misses_counter.inc(stats.misses_count)

    def setup_listeners(self, cache: Cache):
        cache.set_insert_listener(self._on_insert)
        cache.set_lookup_listener(self._on_lookup)
