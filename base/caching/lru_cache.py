"""An LRU cache supporting batch queries.

The main goal of this implementation is to provide a fixed size cache that supports
batched requests to e.g. the content manager or GCS.

A simpler solution could use `functools.lru_cache`, but that does not allow for batched
requests. In addition, this implementation uses the `lru-dict` library, which implements
the LRU in C and is significantly faster than the Python implementation.

Usage:
>>> from base.caching.lru_cache import LRUCache
>>> def get_missing(keys):
>>>     return [value(key) for key in keys]
>>> cache = LRUCache(get_missing, max_size_bytes=1024**3)
>>> cache.get(["key1", "key2"])
"""

from __future__ import annotations

import typing
from typing import Callable, TypeVar, overload
from threading import RLock

import lru
import structlog
from pympler import asizeof
import functools

from base.caching import cache

log = structlog.get_logger()


K = typing.TypeVar("K", contravariant=True)
V = typing.TypeVar("V", covariant=True)
P = typing.ParamSpec("P")


def measure_bytes(key, value) -> int:
    """Measures the size in bytes.

    Note that we only account for the key and value pairs being stored in the cache and
    not the overhead of the cache (e.g. the LRU linked list, etc.).
    Consider this to be a reasonable estimate of the memory requirements of the cache;
    if you want something more precise, I suggest using another programming language.

    Also be warned that estimating the size of Python objects is expensive if they are
    deeply nested.

    NOTE(arun): For Python 3.11+, asizeof.asizeof no longer gives deterministic results
    for non-primitive objects (reported memory size goes down over multiple calls).
    """
    return asizeof.asizeof(key) + asizeof.asizeof(value)


def measure_count(key, value) -> int:
    """Measures the size in entries."""
    del key, value
    return 1


class GetMissingFn(typing.Protocol[K, V, P]):
    def __call__(
        self, keys: typing.Iterable[K], *args: P.args, **kwargs: P.kwargs
    ) -> typing.Iterable[V | None]:
        raise NotImplementedError()


class LRUCache(cache.Cache[K, V, P]):
    """LRU cache that supports batch operations.

    Design considerations:
    - The (e.g. memory) footprint of the cache should well defined and constrained.
    - When inserting a batch of items, we greedily insert as many elements as
      possible. NOTE(arun): This is a somewhat arbitrary heuristic that should
      be re-evaluated given data.
    - We should support some form of metrics logging for users of the cache.

    Only the public methods of this class are thread-safe.
    """

    def __init__(
        self,
        get_missing_fn: GetMissingFn[K, V, P],
        max_size: int,
        max_elem_size: int | None = None,
        cache_missing_keys: bool = False,
        name: str = "LRUCache",
        size_fn: typing.Callable[[K, V | None], int] = measure_bytes,
    ):
        """Create a new LRU cache.

        Args:
            get_missing_fn: a function that takes a list of keys and returns the
                corresponding objects. Returns None if the key could not be found.
            max_size: the maximum size of the cache. Note that when
            max_elem_size: the maximum size of an element we'll store in the
                cache. If an element is larger than this, we won't store it because
                it would evict too many things from the cache.
                If None, use `max_size` as a default.
            cache_missing_keys: if True, then missing entries (i.e. `get_missing_fn`
                returns None for the key) will be cached, surpressing subsequent
                attempts to retrieve the key from the backing store. Suppression of all
                subsequent attempts is not guaranteed because these cache entries may be
                evicted.

                If cache_missing_keys is False, missing entries are not cached and
                subsequent attempts to retrieve them from the cache will attempt to
                retrieve them from the backing store.
            name: the name of the cache.
            size_fn: a function that measures the size of a key and value.
                This function must always return a number >= 1.
        """
        # NOTE(arun): Storing these as private variables as some users subclass
        # LRUCache and implement a `_get_missing` function.
        # TODO(arun): We should probably replace these subclasses with functions.
        self.__get_missing = get_missing_fn
        self.__size_fn = size_fn

        self._cache = lru.LRU(size=max_size)
        self._max_size = max_size
        self._max_elem_size = max_elem_size or max_size
        self._current_size = 0
        self._cache_missing_keys = cache_missing_keys
        self._name = name
        self.logging_fn = log.info

        # Listeners for metrics.
        self._insert_listener: cache.InsertListener | None = None
        self._lookup_listener: cache.LookupListener | None = None

        self._lock = RLock()

    def set_insert_listener(self, listener: cache.InsertListener | None = None):
        """Set a listener for when new items are inserted into the cache.

        If `listener` is None, any existing listener will be removed.
        """
        with self._lock:
            self._insert_listener = listener

    def set_lookup_listener(self, listener: cache.LookupListener | None = None):
        """Set a listener for when items are queried from the cache.

        If `listener` is None, any existing listener will be removed.
        """
        with self._lock:
            self._lookup_listener = listener

    def _insert(
        self, key_values: typing.Iterable[tuple[K, V | None]]
    ) -> cache.InsertStats:
        """Try to insert the keys into the cache (not thread-safe).

        Keys must be unique.
        """
        # Sort the elements by their size so we can greedily insert them.
        key_value_sizes = [
            (
                key,
                value,
                self.__size_fn(key, value),
            )
            for key, value in key_values
            # NOTE(arun): It is possible for another thread to have inserted some
            # missing keys while we were waiting. We just filter out these keys.
            # NOTE(jeff): This doesn't bump the key in the LRU (which is good).
            if key not in self._cache
        ]
        key_value_sizes.sort(key=lambda kvs: kvs[-1])
        total_size = sum(size for _, _, size in key_value_sizes)
        self.logging_fn(
            f"{self._name}: Trying to inserting {len(key_value_sizes)} keys (total size {total_size}).",
        )

        # Next, get the total amount of space we need.
        required_size = 0
        # And the cutoff of the number of elements we'll add.
        cutoff_idx = 0
        for key, value, entry_size in key_value_sizes:
            if (entry_size > self._max_elem_size) or (
                required_size + entry_size > self._max_size
            ):
                # Stop adding elements once the entry is too big or we don't have enough
                # space to store more objects.
                break
            required_size += entry_size
            cutoff_idx += 1

        self.logging_fn(
            f"{self._name}: Needs {required_size} units to insert {cutoff_idx} entries.",
        )

        # Free up space for the elements we want to add.
        evictions = 0
        freed_size = 0
        while self._current_size + required_size > self._max_size:
            _, (_, evicted_size) = self._cache.popitem(least_recent=True)
            freed_size += evicted_size
            self._current_size -= evicted_size
            evictions += 1
        self.logging_fn(
            f"{self._name}: Evicted {evictions} entries to free {freed_size} units."
        )

        # Finally, add the elements
        for key, value, size in key_value_sizes[:cutoff_idx]:
            # NOTE(arun): We store the sizes of the keys and values to avoid
            # re-measuring them and to work around an issue where `measure_bytes` can
            # give different results over time.
            # NOTE(jeff): It is important here that all keys are added and nothing is
            # evicted, so that the current_size is correct. Hence, keys must be unique.
            # Also, the cache internally evicts at number of entries, whereas we cap
            # via the provided size_fn, so size_fn must always returns a number >= 1.
            self._cache[key] = (value, size)
        self._current_size += required_size

        return cache.InsertStats(
            insertion_count=cutoff_idx,
            skip_count=len(key_value_sizes) - cutoff_idx,
            eviction_count=evictions,
            cache_size=self._current_size,
            entries=len(self._cache),
        )

    def get(
        self, keys: typing.Iterable[K], *args: P.args, **kwargs: P.kwargs
    ) -> typing.Iterable[V | None]:
        """Retrieve content for a list of keys.

        Args:
            keys: a list of keys.
            context: the context to passed to the missing_fn

        Returns:
            a list of content corresponding to `keys` or None if content for that key
            could not be retrieved. The order of the elements returned must match those
            in `keys`.
        """
        if not isinstance(keys, list):
            # Make sure that we can re-iterate through keys.
            keys = list(keys)

        with self._lock:
            # Try to retrieve what we can from the cache.
            # Note that some of these items may have been evicted by the time we return.
            response: dict[K, V | None] = {
                key: self._cache[key][0] for key in keys if key in self._cache
            }
            # Get the missing keys to retrieve in a single batch.
            missing = [key for key in keys if key not in self._cache]
            # Remove duplicates. Unique keys is required as a precondition to insert.
            missing = list(dict.fromkeys(missing))

        if self._lookup_listener:
            self._lookup_listener(
                cache.LookupStats(hits_count=len(response), misses_count=len(missing))
            )

        if missing:
            self.logging_fn(f"{self._name}: Retrieving {len(missing)} missing entries.")
            found = self.__get_missing(missing, *args, **kwargs)
            to_update = [
                (key, value)
                for key, value in zip(missing, found)
                # If cache_missing_keys, also update keys with None values.
                # This will surpress future attempts to retrieve the key from the
                # backing store until this key is evicted.
                if value is not None or self._cache_missing_keys
            ]
            response.update(to_update)
            self.logging_fn(
                f"{self._name}: Found {len(to_update)} missing entries; {len(missing) - len(to_update)} still missing."
            )

            with self._lock:
                insert_stats = self._insert(to_update)

            if self._insert_listener:
                self._insert_listener(insert_stats)

        return [response.get(key) for key in keys]


_F = TypeVar("_F", bound=Callable)


# this signature is for the syntax
# @lru_cache(...)
# def func(...)
@overload
def lru_cache(maxsize: int = 128, typed: bool = False) -> Callable[[_F], _F]: ...


# this signature is for the syntax
# @lru_cache   <--- no parenthesis
# def func(...)
@overload
def lru_cache(f: _F) -> _F: ...


def lru_cache(*args, **kwargs):  # type: ignore
    """A wrapper of `functools.lru_cache` with fixed type annotations."""
    return functools.lru_cache(*args, **kwargs)
