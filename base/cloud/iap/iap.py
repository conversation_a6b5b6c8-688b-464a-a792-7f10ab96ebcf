"""IAP JWT verification."""

import json
import logging
import pathlib
import typing

import jwt


class IapJwtVerifier:
    """Class to verify IAP JWTs."""

    def __init__(
        self,
        jwks_data: dict,
        audience_prefix: list[str],
        options: dict[str, typing.Any] | None = None,
    ):
        self.jwks_data = jwks_data
        self.audience_prefix = audience_prefix
        self.options = {}
        self.options.setdefault("verify_aud", False)
        if options:
            self.options.update(options)

    @classmethod
    def create(
        cls, audience_prefix: list[str], options: dict[str, typing.Any] | None = None
    ):
        jwks_data_path = pathlib.Path("base/cloud/iap/public_iap_certs.json")
        jwks_data = json.loads(jwks_data_path.read_text(encoding="utf-8"))
        return cls(jwks_data, audience_prefix, options)

    def verify(self, iap_jwt_token: str) -> str | None:
        """Verify the IAP JWT.

        Args:
            iap_jwt_token: The IAP JWT to verify.
        Returns:
            True if the IAP JWT is valid, False otherwise.
        """
        if not iap_jwt_token:
            return None
        try:
            unverified_header = jwt.get_unverified_header(iap_jwt_token)
            key_id = unverified_header["kid"]
            key = self.jwks_data.get(key_id)
            if not key:
                return None

            decoded_token = jwt.decode(
                iap_jwt_token,
                key,
                algorithms=["ES256"],
                audience=self.audience_prefix,
                options=self.options,
            )
            logging.debug("Decoded token: %s", decoded_token)
            if decoded_token["iss"] != "https://cloud.google.com/iap":
                return None
            if not any(
                decoded_token["aud"].startswith(prefix)
                for prefix in self.audience_prefix
            ):
                return None
            logging.info("Verified IAP JWT: %s", decoded_token)

            return decoded_token["email"]
        except jwt.exceptions.PyJWTError as e:
            logging.exception(e)
            logging.error("Failed to verify IAP JWT")
            return None
