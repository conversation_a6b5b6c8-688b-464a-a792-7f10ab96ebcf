# IAP Verifier

Helper libraries to verify GCP IAP JWT tokens.

NOTE: Internal web interfaces like Genie and Support give us JWTs in a `X-Goog-IAP-JWT-Assertion` header that includes a hard-to-predict namespace-dependent suffix for the aud claim.
We only validate the "audience prefix", i.e. the part of the aud claim before this suffix. Standard JWT libraries don't support this, so we disable their aud checks and roll our own validation.

## Rust

### Usage

Add this to your `Cargo.toml`:

```toml
[dependencies]
iap = { path = "../../../base/cloud/iap" }
```

### Example

```rust
use iap::GcpIapVerifier;

let iap_verifier = GcpIapVerifierImpl::new(vec![]).unwrap();
let iap_token = "xxx";
match iap_verifier.verify(iap_token) {
    Ok(email) => println!("Verified email: {}", email),
    Err(e) => println!("Error: {}", e),
}
```
