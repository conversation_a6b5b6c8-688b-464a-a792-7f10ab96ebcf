"""Unit tests for the IAP JWT verifier."""

import logging

from base.cloud.iap import iap

logging.basicConfig(level=logging.DEBUG)


def test_iap_jwt_verifier_expired():
    """Test that the IAP JWT verifier rejects expired tokens."""
    aud = "/projects/*************/global/backendServices/8319514039615982897"  # pragma: allowlist secret
    iap_verifier = iap.IapJwtVerifier.create([aud])
    iap_token = "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImRfUG1rZyJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wJ8qwwcpwZEeAtrj5dE3ngdo05V7H4uxINgJL7wxpUqoPKCkWIKet7qteS1BLraWcINIQWLtKQ8JQoB4dOFXWQ"  # pragma: allowlist secret
    assert not iap_verifier.verify(iap_token)
