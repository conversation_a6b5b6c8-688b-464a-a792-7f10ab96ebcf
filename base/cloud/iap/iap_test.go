package iap

import (
	"os"
	"testing"
	"time"

	"github.com/MicahParks/jwkset"
	jwt "github.com/golang-jwt/jwt/v5"
)

func TestIapJwtVerifierExpired(t *testing.T) {
	aud := "/projects/*************/global/backendServices/8319514039615982897"
	iapVerifier, err := New([]string{aud})
	if err != nil {
		t.Fatal(err)
	}
	iapToken := "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImRfUG1rZyJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wJ8qwwcpwZEeAtrj5dE3ngdo05V7H4uxINgJL7wxpUqoPKCkWIKet7qteS1BLraWcINIQWLtKQ8JQoB4dOFXWQ" // pragma: allowlist secret
	_, err = iapVerifier.Verify(iapToken)
	if err == nil {
		t.Fatal(err)
	}
}

func TestNew(t *testing.T) {
	iapVerifier, err := New([]string{"/projects/*************/global/backendServices/8319514039615982897"})
	if err != nil {
		t.Fatal(err)
	}
	if iapVerifier == nil {
		t.Fatal("iapVerifier is nil")
	}
}

type IapClaims struct {
	jwt.RegisteredClaims
	Email string `json:"email"`
}

func TestVerify(t *testing.T) {
	signingMethod := jwt.GetSigningMethod("ES256")
	keyfile, err := os.ReadFile("test_data/ec256-cert-private.pem")
	if err != nil {
		t.Fatal(err)
	}
	key, err := jwt.ParseECPrivateKeyFromPEM(keyfile)
	if err != nil {
		t.Fatal(err)
	}
	jwkKey, err := jwkset.NewJWKFromKey(key, jwkset.JWKOptions{})
	if err != nil {
		t.Fatal(err)
	}

	claims := &IapClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(1 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),

			Audience: []string{"/projects/*************/global/backendServices/8319514039615982897/17"},
		},
		Email: "<EMAIL>",
	}
	unsignedToken := jwt.NewWithClaims(signingMethod, claims)
	unsignedToken.Header[jwkset.HeaderKID] = "abc"
	ss, err := unsignedToken.SignedString(jwkKey.Key())
	if err != nil {
		t.Fatal(err)
	}

	publicKey, err := os.ReadFile("test_data/ec256-cert-public.pem")
	if err != nil {
		t.Fatal(err)
	}
	// decode utf-8 publicKey
	publicKeyString := string(publicKey)

	kidToPublicKey := map[string]string{
		"abc": publicKeyString,
	}
	iapVerifier := iapVerifierImpl{
		kidToPublicKey: kidToPublicKey,
		audiencePrefix: []string{"/projects/*************/global/backendServices/8319514039615982897"},
	}

	userName, err := iapVerifier.Verify(ss)
	if err != nil {
		t.Fatal(err)
	}
	if userName != "<EMAIL>" {
		t.Fatal("userName <NAME_EMAIL>")
	}
}
