use jsonwebtoken::{decode, decode_header, <PERSON>gorithm, <PERSON><PERSON><PERSON><PERSON><PERSON>, Valida<PERSON>};
use std::collections::BTreeMap;

#[derive(Debug, thiserror::Error)]
pub enum IapError {
    #[error("unknown error")]
    Unknown,

    #[error("invalid token")]
    InvalidToken,
}

/// trait to verify GCP IAP JWTs
pub trait GcpIapVerifier {
    /// Verifies the IAP JWT and returns the email address of the user.
    fn verify(&self, iap_jwt_token: &str) -> std::result::Result<String, IapError>;
}

#[derive(Debug, Clone)]
pub struct GcpIapVerifierImpl {
    kid_to_public_key: BTreeMap<String, String>,
    audience_prefix: Vec<String>,
}

/// Implements the IAP JWT verifier for GCP
impl GcpIapVerifierImpl {
    /// Create a new IAP JWT verifier
    pub fn new(audience_prefix: Vec<String>) -> Result<Self, Box<dyn std::error::Error>> {
        if audience_prefix.is_empty() {
            return Err(Box::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "No audience prefix provided",
            )));
        }
        // read contents of public_iap_certs.json
        let contents =
            std::fs::read_to_string("base/cloud/iap/public_iap_certs.json").map_err(|e| {
                tracing::warn!("Error loading public_iap_certs.json: {:?}", e);
                e
            })?;
        // load contents into jwt_data
        let kid_to_public_key = serde_json::from_str(&contents)?;
        Ok(GcpIapVerifierImpl {
            kid_to_public_key,
            audience_prefix,
        })
    }

    fn verify_with_kid(
        &self,
        kid: &str,
        iap_jwt_token: &str,
    ) -> std::result::Result<String, IapError> {
        match self.kid_to_public_key.get(kid) {
            None => {
                tracing::error!("Invalid JWT token or invalid kid: {:?}", kid);
                Err(IapError::Unknown)
            }
            Some(secret) => {
                let algorithm = Algorithm::ES256;
                let mut validation = Validation::new(algorithm);
                validation.validate_aud = false; // we validate the audience manually
                let key = DecodingKey::from_ec_pem(secret.as_ref()).map_err(|e| {
                    tracing::warn!("Error creating key: {:?}", e);
                    IapError::Unknown
                })?;

                match decode::<Claims>(iap_jwt_token, &key, &validation) {
                    Ok(token_data) => {
                        tracing::debug!("Token decoded successfully: {:?}", token_data.claims);
                        for prefix in &self.audience_prefix {
                            if token_data.claims.aud.starts_with(prefix) {
                                return Ok(token_data.claims.email);
                            }
                        }
                        tracing::warn!("Invalid audience in token: {:?}", token_data.claims.aud);
                        Err(IapError::InvalidToken)
                    }
                    Err(e) => match e.kind() {
                        jsonwebtoken::errors::ErrorKind::InvalidSignature => {
                            tracing::warn!("Invalid token: {:?}", e);
                            Err(IapError::InvalidToken)
                        }
                        jsonwebtoken::errors::ErrorKind::ExpiredSignature => {
                            tracing::warn!("Expired token: {:?}", e);
                            Err(IapError::InvalidToken)
                        }
                        _ => {
                            tracing::warn!("Error decoding token: {:?}", e);
                            Err(IapError::Unknown)
                        }
                    },
                }
            }
        }
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
struct Claims {
    sub: String,
    email: String,
    aud: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    exp: Option<u64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    iat: Option<u64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    nbf: Option<u64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    iss: Option<String>,
}

impl GcpIapVerifier for GcpIapVerifierImpl {
    fn verify(&self, iap_jwt_token: &str) -> std::result::Result<String, IapError> {
        match decode_header(iap_jwt_token) {
            Err(e) => {
                tracing::warn!("Error decoding token: {:?}", e);
                Err(IapError::Unknown)
            }
            Ok(header) => match header.kid {
                None => {
                    tracing::warn!("Error decoding token: {:?}", header.kid);
                    Err(IapError::Unknown)
                }
                Some(kid) => self.verify_with_kid(kid.as_str(), iap_jwt_token),
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use jsonwebtoken::{encode, EncodingKey, Header};

    use super::*;

    #[test]
    fn test_iap_jwt_verifier_success() {
        // println!("{:?}", std::fs::read_dir(".").unwrap().map(|x| x.unwrap().path()).collect::<Vec<_>>());
        // Read the private key for signing
        let private_key =
            std::fs::read_to_string("base/cloud/iap/test_data/ec256-cert-private.pem")
                .expect("Failed to read private key file");

        // Read the public key for verification
        let public_key = std::fs::read_to_string("base/cloud/iap/test_data/ec256-cert-public.pem")
            .expect("Failed to read public key file");

        let audience = "/projects/1035750215372/global/backendServices/8319514039615982897";
        let test_email = "<EMAIL>";

        // Create claims for the JWT with required fields
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .expect("Time went backwards")
            .as_secs();

        let claims = Claims {
            sub: "test-subject".to_string(),
            email: test_email.to_string(),
            aud: audience.to_string(),
            exp: Some(now + 3600), // Expires in 1 hour
            iat: Some(now),        // Issued at now
            nbf: Some(now),        // Not valid before now
            iss: Some("https://cloud.google.com/iap".to_string()),
        };

        // Create a header with the kid
        let mut header = Header::new(Algorithm::ES256);
        header.kid = Some("abc".to_string());

        // Create the JWT token
        let encoding_key = EncodingKey::from_ec_pem(private_key.as_bytes())
            .expect("Failed to create encoding key");
        let token = encode(&header, &claims, &encoding_key).expect("Failed to create JWT token");

        // Create a map of kid to public key
        let mut kid_to_public_key = BTreeMap::new();
        kid_to_public_key.insert("abc".to_string(), public_key);

        // Create the verifier
        let iap_verifier = GcpIapVerifierImpl {
            kid_to_public_key,
            audience_prefix: vec![audience.to_string()],
        };

        // Verify the token
        let result = iap_verifier.verify(&token);

        // Assert that verification succeeded and returned the expected email
        assert!(
            result.is_ok(),
            "Token verification failed: {:?}",
            result.err()
        );
        assert_eq!(result.unwrap(), test_email);
    }

    #[test]
    fn test_iap_jwt_verifier_bad_public_key() {
        // println!("{:?}", std::fs::read_dir(".").unwrap().map(|x| x.unwrap().path()).collect::<Vec<_>>());
        // Read the private key for signing
        let private_key =
            std::fs::read_to_string("base/cloud/iap/test_data/ec256-cert-private.pem")
                .expect("Failed to read private key file");

        // Read the public key for verification
        let public_key = std::fs::read_to_string("base/cloud/iap/test_data/ec256-cert-public.pem")
            .expect("Failed to read public key file");

        let audience_prefix = "/projects/1035750215372/global/backendServices/";
        let test_email = "<EMAIL>";

        // Create claims for the JWT with required fields
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .expect("Time went backwards")
            .as_secs();

        let claims = Claims {
            sub: "test-subject".to_string(),
            email: test_email.to_string(),
            aud: format!("{}{}", audience_prefix, "8319514039615982897"),
            exp: Some(now + 3600), // Expires in 1 hour
            iat: Some(now),        // Issued at now
            nbf: Some(now),        // Not valid before now
            iss: Some("https://cloud.google.com/iap".to_string()),
        };

        // Create a header with the kid
        let mut header = Header::new(Algorithm::ES256);
        header.kid = Some("abc".to_string());

        // Create the JWT token
        let encoding_key = EncodingKey::from_ec_pem(private_key.as_bytes())
            .expect("Failed to create encoding key");
        let token = encode(&header, &claims, &encoding_key).expect("Failed to create JWT token");

        // Create a map of kid to public key
        let mut kid_to_public_key = BTreeMap::new();
        kid_to_public_key.insert("abc".to_string(), public_key.replace('1', "2"));

        // Create the verifier
        let iap_verifier = GcpIapVerifierImpl {
            kid_to_public_key,
            audience_prefix: vec![audience_prefix.to_string()],
        };

        // Verify the token
        let result = iap_verifier.verify(&token);

        // Assert that verification succeeded and returned the expected email
        assert!(result.is_err());
    }

    #[test]
    fn test_iap_jwt_verifier_bad_audience() {
        // println!("{:?}", std::fs::read_dir(".").unwrap().map(|x| x.unwrap().path()).collect::<Vec<_>>());
        // Read the private key for signing
        let private_key =
            std::fs::read_to_string("base/cloud/iap/test_data/ec256-cert-private.pem")
                .expect("Failed to read private key file");

        // Read the public key for verification
        let public_key = std::fs::read_to_string("base/cloud/iap/test_data/ec256-cert-public.pem")
            .expect("Failed to read public key file");

        let audience_prefix = "/projects/1035750215372/global/backendServices/";
        let test_email = "<EMAIL>";

        // Create claims for the JWT with required fields
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .expect("Time went backwards")
            .as_secs();

        let claims = Claims {
            sub: "test-subject".to_string(),
            email: test_email.to_string(),
            aud: format!("{}{}", audience_prefix, "8319514039615982897"),
            exp: Some(now + 3600), // Expires in 1 hour
            iat: Some(now),        // Issued at now
            nbf: Some(now),        // Not valid before now
            iss: Some("https://cloud.google.com/iap".to_string()),
        };

        // Create a header with the kid
        let mut header = Header::new(Algorithm::ES256);
        header.kid = Some("abc".to_string());

        // Create the JWT token
        let encoding_key = EncodingKey::from_ec_pem(private_key.as_bytes())
            .expect("Failed to create encoding key");
        let token = encode(&header, &claims, &encoding_key).expect("Failed to create JWT token");

        // Create a map of kid to public key
        let mut kid_to_public_key = BTreeMap::new();
        kid_to_public_key.insert("abc".to_string(), public_key);

        // Create the verifier
        let iap_verifier = GcpIapVerifierImpl {
            kid_to_public_key,
            audience_prefix: vec![audience_prefix.to_string().replace('1', "2")],
        };

        // Verify the token
        let result = iap_verifier.verify(&token);

        // Assert that verification succeeded and returned the expected email
        assert!(result.is_err());
    }
}
