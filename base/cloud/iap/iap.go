package iap

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	jwt "github.com/golang-jwt/jwt/v5"
	"github.com/rs/zerolog/log"
)

type IapVerifier interface {
	Verify(iapJwtToken string) (email string, err error)
}

type iapVerifierImpl struct {
	kidToPublicKey map[string]string
	audiencePrefix []string
}

type JwtHeader struct {
	Kid string `json:"kid"`
}

func (iapVerifier *iapVerifierImpl) Verify(iapJwtToken string) (email string, err error) {
	validation := jwt.MapClaims{}
	validation["aud"] = iapVerifier.audiencePrefix

	token, err := jwt.ParseWithClaims(iapJwtToken, validation, func(token *jwt.Token) (interface{}, error) {
		keyID, ok := token.Header["kid"].(string)
		if !ok {
			log.Error().Msgf("Missing kid in token")
			return nil, fmt.Errorf("Invalid token")
		}
		publicKey, ok := iapVerifier.kidToPublicKey[keyID]
		if !ok {
			log.Error().Msgf("Invalid JWT token or invalid kid: %s", keyID)
			return "", fmt.Errorf("Invalid token")
		}
		i, err := jwt.ParseECPublicKeyFromPEM([]byte(publicKey))
		if err != nil {
			log.Error().Err(err).Msg("Error parsing public key")
			return nil, err
		}
		return i, nil
	})
	if err != nil {
		log.Error().Err(err).Msg("Error parsing token")
		return "", fmt.Errorf("Invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "", fmt.Errorf("Invalid token")
	}

	email = claims["email"].(string)
	return email, nil
}

func newWithPath(path string, audiencePrefix []string) (iapVerifier IapVerifier, err error) {
	// load kid json from path
	content, err := os.ReadFile(path)
	if err != nil {
		return
	}
	var kidToPublicKey map[string]string
	err = json.Unmarshal(content, &kidToPublicKey)
	if err != nil {
		return
	}

	iapVerifier = &iapVerifierImpl{
		kidToPublicKey: kidToPublicKey,
		audiencePrefix: audiencePrefix,
	}

	err = nil
	return
}

func New(audiencePrefix []string) (iapVerifier IapVerifier, err error) {
	rd := os.Getenv("RUNFILES_DIR")
	p := filepath.Join(rd, "base/cloud/iap/public_iap_certs.json")
	iapVerifier, err = newWithPath(p, audiencePrefix)
	if err != nil {
		// For some reason this is the path in unit tests?
		p = filepath.Join(rd, "_main/base/cloud/iap/public_iap_certs.json")
		iapVerifier, err = newWithPath(p, audiencePrefix)
	}
	return
}

type MockIapVerifier struct {
	Email string
}

func (iapVerifier *MockIapVerifier) Verify(iapJwtToken string) (email string, err error) {
	return iapVerifier.Email, nil
}
