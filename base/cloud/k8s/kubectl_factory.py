"""Factory for kubectl obje ts."""

import pathlib
import typing

from base.cloud.k8s import kubectl


class KubectlFactory(typing.Protocol):
    """Factory for kubectl commands."""

    def __call__(self, cloud: str) -> kubectl.Kubectl:
        """Returns a kubectl command for the given cloud."""
        raise NotImplementedError()


class NullKubectlFactory(KubectlFactory):
    """Factory for kubectl commands."""

    def __call__(self, cloud: str) -> kubectl.NullKubectl:
        del cloud
        return kubectl.NullKubectl()


class KubectlFactoryImpl(KubectlFactory):
    """Factory for KubectlImpl commands."""

    def __init__(self, kube_config_file: pathlib.Path | None):
        self.kube_config_file = kube_config_file

    def __call__(self, cloud: str) -> kubectl.Kubectl:
        return kubectl.KubectlImpl(cloud, self.kube_config_file)


def create_kubectl_factory(kube_config_file: pathlib.Path | None) -> KubectlFactory:
    return KubectlFactoryImpl(kube_config_file)
