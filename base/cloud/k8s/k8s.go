package k8s

// CloudContexts is a map that holds cloud environment names as keys
// and their corresponding Kubernetes contexts as values.
var CloudContexts = map[string]string{
	"GCP_US_CENTRAL1_DEV":        "gke_system-services-dev_us-central1_us-central1-dev",
	"GCP_US_CENTRAL1_PROD":       "gke_system-services-prod_us-central1_us-central1-prod",
	"GCP_US_CENTRAL1_GSC_PROD":   "gke_system-services-prod-gsc_us-central1_prod-gsc",
	"GCP_EU_WEST4_PROD":          "gke_system-services-prod_europe-west4_eu-west4-prod",
	"GCP_AGENT_US_CENTRAL1_PROD": "gke_agent-sandbox-prod_us-central1_gcp-prod-agent0",
	"GCP_AGENT_EU_WEST4_PROD":    "gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0",
}

// GetContextForCloud returns the Kubernetes context associated with a given cloud environment name.
// If the cloud environment is not found, it returns an empty string and a boolean value of false.
func GetContextForCloud(cloudName string) (string, bool) {
	context, exists := CloudContexts[cloudName]
	return context, exists
}

// GetClouds returns a slice of all cloud environment names available in the CloudContexts map.
func GetClouds() []string {
	clouds := make([]string, 0, len(CloudContexts))
	for cloud := range CloudContexts {
		clouds = append(clouds, cloud)
	}
	return clouds
}
