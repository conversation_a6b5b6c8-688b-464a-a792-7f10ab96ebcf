"""Library to run kubectl commands."""

from contextlib import contextmanager
import os
import re
import json
import logging
import pathlib
import subprocess
import tempfile
import typing
from dataclasses import dataclass
from typing import Iterator

import yaml

from base.python.cloud import cloud as cloud_lib
from base.cloud.k8s.kubectl_diff_helper import split_objects_in_new_namespaces

_KUBECTL_BIN = "../k8s_binary/file/kubectl"

# all supported kinds by kubectl
# add new kinds here if you want to use kubectl
#
# If a non-namespaces kind is added, you must also change `is_namespaced_kind`
SUPPORTED_KINDS = [
    "ArtifactRegistryRepository",
    "BackendConfig",
    "BigQueryDataset",
    "BigQueryDataset",
    "BigQueryJob",
    "BigQueryTable",
    "BigtableGCPolicy",
    "BigtableInstance",
    "BigtableTable",
    "Certificate",
    "ClusterIssuer",
    "ClusterRole",
    "ClusterRoleBinding",
    "ClusterPodMonitoring",
    "ClusterRules",
    "CloudIdentityGroup",
    "CloudIdentityMembership",
    "ComputeProjectMetadata",
    "ComputeNetworkPeering",
    "ComputeSSLPolicy",
    "ComputeSecurityPolicy",
    "ComputeSubnetwork",
    "ConfigConnector",
    "ConfigConnectorContext",
    "ConfigMap",
    "ContainerNodePool",
    "ControllerResource",
    "CronJob",
    "CustomResourceDefinition",
    "DaemonSet",
    "DNSManagedZone",
    "DNSRecordSet",
    "Deployment",
    "FilestoreInstance",
    "HorizontalPodAutoscaler",
    "FrontendConfig",
    "IAMCustomRole",
    "IAMPartialPolicy",
    "IAMPolicy",
    "IAMPolicyMember",
    "IAMServiceAccount",
    "Ingress",
    "Ingress",
    "IngressClass",
    "Issuer",
    "KMSKeyRing",
    "Job",
    "LoggingLogBucket",
    "LoggingLogSink",
    "MonitoringAlertPolicy",
    "MutatingWebhookConfiguration",
    "Namespace",
    "OperatorConfig",
    "PersistentVolume",
    "PersistentVolumeClaim",
    "Pod",
    "PodDisruptionBudget",
    "PodMonitoring",
    "PriorityClass",
    "PubSubSubscription",
    "PubSubTopic",
    "ReplicaSet",
    "Role",
    "Rules",
    "RoleBinding",
    "ScaledObject",
    "Secret",
    "SecretProviderClass",
    "SealedSecret",
    "Service",
    "ServiceAccount",
    "ServiceAttachment",
    "SpannerInstance",
    "SpannerDatabase",
    "StatefulSet",
    "StorageBucket",
    "StorageClass",
    # transformation key was misnamed. It is TransformationKey in resource definitions, but Transformation-Key in the API
    "Transformation-Key",
    "Tenant",
    "WebhookTenantMapping",
    "ValidatingWebhookConfiguration",
]
SUPPORTED_KINDS_SET = set(SUPPORTED_KINDS)

# In dry run mode, we usually don't have permissions to view these kinds of
# objects, so they should probably be skipped
DRY_RUN_SKIP_KINDS_SET = set(
    [
        "Secret",
    ]
)


def is_valid_kind(kind: str) -> bool:
    """Checks if a kind is a valid kind."""
    return kind in SUPPORTED_KINDS or kind == "TransformationKey"


def is_namespaced_kind(kind: str) -> bool:
    """Checks if a kind is a namespaced resource."""
    return kind not in [
        "ClusterIssuer",
        "ClusterPodMonitoring",
        "ClusterRole",
        "ClusterRoleBinding",
        "ClusterRules",
        "ConfigConnector",
        "ControllerResource",
        "CustomResourceDefinition",
        "IngressClass",
        "MonitoringAlertPolicy",
        "MutatingWebhookConfiguration",
        "Namespace",
        "PersistentVolume",
        "PriorityClass",
        "StorageClass",
        "ValidatingWebhookConfiguration",
    ]


class KubectlException(Exception):
    """Exception raised when a command fails."""

    def __init__(
        self,
        msg,
        stdout: str | None = None,
        stderr: str | None = None,
        command: list[str] | None = None,
    ):
        """Initializes the exception.

        Args:
          msg: The message to print.
          stdout: The stdout of the command.
          stderr: The stderr of the command.
        """
        super().__init__(msg)
        self.msg = msg
        self.stdout = stdout
        self.stderr = stderr
        self.command = command

    def __str__(self):
        return f"Command {' '.join(self.command or [])} failed: {self.stderr}"


@dataclass(frozen=True)
class KubectlOutput:
    """Represents the output of a kubectl command."""

    command: list[str] | None
    stdout: str
    stderr: str
    returncode: int


def _run_kubectl(
    args: list[str],
    cloud: str,
    kube_config_file: pathlib.Path | None,
    check: bool = True,
    stdin: str | None = None,
    capture_output: bool = True,
) -> KubectlOutput:
    """Run a kubectl command.

    Args:
      args: The arguments to run.
      check: Whether to check the return code.

    Returns:
      The stdout and stderr of the command.
    """
    cmd = [_KUBECTL_BIN]
    context = cloud_lib.get_context(cloud)
    # set config is a config is request and if there is a kube_config file
    # is there is no kube config file, we have to use whatever the in-cluster
    # context is
    if context and kube_config_file:
        cmd += ["--context", context]
    if kube_config_file:
        cmd += ["--kubeconfig", str(kube_config_file)]
    cmd += args
    logging.debug("cmd %s", cmd)
    p = subprocess.run(
        cmd,
        input=stdin,
        capture_output=capture_output,
        encoding="utf-8",
        check=False,
    )

    if check and p.returncode != 0:
        raise KubectlException(
            "kubectl call failed", p.stdout, p.stderr, [str(c) for c in cmd]
        )
    return KubectlOutput(
        command=cmd, stdout=p.stdout, stderr=p.stderr, returncode=p.returncode
    )


@dataclass(frozen=True)
class KubeObject:
    """Represents a Kubernetes object."""

    api_version: str
    kind: str
    name: str
    namespace: str | None


class Kubectl(typing.Protocol):
    """Protocol for a kubectl command."""

    @property
    def kube_config_file(self) -> pathlib.Path | None:
        """Returns the kube config file to use or None if no kube config file was found."""
        raise NotImplementedError()

    def apply(
        self,
        config_file: pathlib.Path,
        check: bool = True,
        extra_args: typing.Sequence[str] | None = None,
    ) -> KubectlOutput:
        """Applies a config file.

        Args:
          config_file: The config file to apply.
          check: Whether to check the return code.
          extra_args: Extra arguments to pass to the kubectl command.

        Returns:
          The stdout and stderr of the command.
        """
        raise NotImplementedError()

    def delete(
        self,
        config_file: pathlib.Path,
        check: bool = True,
        extra_args: typing.Sequence[str] | None = None,
    ) -> KubectlOutput:
        """Deletes a config file.

        Args:
          config_file: The config file to delete.
          check: Whether to check the return code.
          extra_args: Extra arguments to pass to the kubectl command.

        Returns:
          The stdout and stderr of the command.
        """
        raise NotImplementedError()

    def diff(self, config_file: pathlib.Path, check: bool = True) -> KubectlOutput:
        """Returns the diff between the current config and the config file.

        Args:
          config_file: The config file to diff.
          check: Whether to check the return code.

        Returns:
          The stdout and stderr of the command.
        """
        raise NotImplementedError()

    def delete_object(self, obj: KubeObject, check: bool = True) -> KubectlOutput:
        """Deletes a Kubernetes object.

        If the object doesn't exist, that is not an error.

        Args:
          obj: The object to delete.
          check: Whether to check the return code.

        Returns:
          The stdout and stderr of the command.
        """
        raise NotImplementedError()

    def find_object(self, obj: KubeObject) -> bool:
        """Checks if an object exists.

        Args:
          obj: The object to check.

        Returns:
          True if the object exists, False otherwise.
        """
        raise NotImplementedError()

    def get_object(self, kind: str, name: str, namespace: str) -> typing.Any:
        """Gets an object.

        Args:
          api_version: The api version of the object.
          kind: The kind of the object.
          name: The name of the object.
          namespace: The namespace of the object.

        Returns:
          The object.
        """
        raise NotImplementedError()

    def list(self, kind: str, namespace: str | None) -> list[typing.Any]:
        """Lists a kind of object.

        Args:
          kind: The kind of object to list.

        Returns:
          The objects.
        """
        raise NotImplementedError()

    def debug_pod(
        self, pod_name: str, container: str, image: str, namespace: str | None
    ) -> KubectlOutput:
        """Creates a debug pod.

        Args:
          pod_name: The name of the pod to debug.
          container: The name of the container within pod to debug.
          image: The image to use for the debug pod.
          namespace: The namespace to search in.
        """
        raise NotImplementedError()

    def find_by_label(
        self,
        namespace: str,
        label_key: str,
        label_value: str,
        kinds: typing.Sequence[str] | None = None,
    ) -> typing.Iterable[KubeObject]:
        """Finds objects by label.

        Args:
          namespace: The namespace to search in.
          label_key: The label key to search for.
          label_value: The label value to search for.
          kinds: The kinds to search for.

        Returns:
          The objects that match the label.
        """
        raise NotImplementedError()

    def run(self, args: "list[str]", check: bool = True) -> KubectlOutput:
        """Runs a kubectl command.

        Args:
          args: The arguments to run.
          check: Whether to check the return code.

        Returns:
          The stdout and stderr of the command.
        """
        raise NotImplementedError

    @contextmanager
    def port_forward(
        self, namespace: str, pod_or_service: str, remote_port: int, local_port: int
    ) -> typing.Generator[int, None, None]:
        """Port forwards a pod.

        Args:
          pod: The pod to port forward. If it is a service, it should be prefixed by 'service/'.
          local_port: The local port to forward to.
          remote_port: The remote port to forward to.

        Yields:
          The local port.
        """
        raise NotImplementedError()


class KubectlImpl(Kubectl):
    """Implementation of the kubectl command."""

    def __init__(
        self,
        cloud: str,
        kube_config_file: pathlib.Path | None,
    ):
        self.cloud = cloud
        self._kube_config_file = kube_config_file
        if self.kube_config_file and not self.kube_config_file.exists():
            raise ValueError(
                f"Kube config file '{self.kube_config_file}' does not exist"
            )

    @property
    def kube_config_file(self) -> pathlib.Path | None:
        return self._kube_config_file

    def run(self, args: list[str], check: bool = True) -> KubectlOutput:
        return _run_kubectl(
            args,
            cloud=self.cloud,
            check=check,
            kube_config_file=self.kube_config_file,
        )

    def apply(
        self,
        config_file: pathlib.Path,
        check: bool = True,
        extra_args: typing.Sequence[str] | None = None,
    ) -> KubectlOutput:
        args = ["apply", "-f", str(config_file)]
        if extra_args:
            args += extra_args
        return _run_kubectl(
            args,
            cloud=self.cloud,
            check=check,
            kube_config_file=self.kube_config_file,
        )

    def delete(
        self,
        config_file: pathlib.Path,
        check: bool = True,
        extra_args: typing.Sequence[str] | None = None,
    ) -> KubectlOutput:
        args = ["delete", "-f", str(config_file), "--ignore-not-found=true"]
        if extra_args:
            args += extra_args
        return _run_kubectl(
            args,
            cloud=self.cloud,
            check=check,
            kube_config_file=self.kube_config_file,
        )

    def _list_namespaces(self) -> list[str]:
        """Lists all namespaces."""
        return [ns["metadata"]["name"] for ns in self.list("namespace", namespace=None)]

    def diff(self, config_file: pathlib.Path, check: bool = True) -> KubectlOutput:
        existing_namespaces = self._list_namespaces()
        split_output = split_objects_in_new_namespaces(
            list(yaml.safe_load_all(config_file.read_text())), existing_namespaces
        )

        with tempfile.NamedTemporaryFile(mode="w") as f:
            f.write(yaml.safe_dump_all(split_output.rest_of_objects))
            f.flush()

            ret = _run_kubectl(
                ["diff", "-f", str(f.name), "--show-managed-fields=false"],
                cloud=self.cloud,
                check=check,
                kube_config_file=self.kube_config_file,
            )

        if split_output.new_namespace_objects:
            new_objs_as_yaml = yaml.safe_dump_all(split_output.new_namespace_objects)
            new_stdout = ret.stdout + "\n"
            for line in new_objs_as_yaml.splitlines(True):
                new_stdout += f"+{line}"

            return KubectlOutput(
                command=ret.command,
                stdout=new_stdout,
                stderr=ret.stderr,
                returncode=ret.returncode,
            )

        return ret

    def delete_object(self, obj: KubeObject, check: bool = True) -> KubectlOutput:
        obj_to_delete = json.dumps(
            {
                "apiVersion": obj.api_version,
                "kind": obj.kind,
                "metadata": {
                    "name": obj.name,
                    "namespace": obj.namespace,
                },
            }
        )

        return _run_kubectl(
            [
                "delete",
                "-f",
                "-",
                "--ignore-not-found=true",
            ],
            cloud=self.cloud,
            kube_config_file=self.kube_config_file,
            check=check,
            stdin=obj_to_delete,
        )

    def find_object(self, obj: KubeObject) -> bool:
        try:
            # Adjust for a historical naming discrepancy on our TransformationKey CRD
            kind = "Transformation-Key" if obj.kind == "TransformationKey" else obj.kind
            args = ["get", kind, obj.name]
            if obj.namespace:
                args += ["-n", obj.namespace]
            _run_kubectl(
                args,
                cloud=self.cloud,
                kube_config_file=self.kube_config_file,
                check=True,
            )
            return True
        except KubectlException as ex:
            if ex.stderr and "Error from server (NotFound)" in ex.stderr:
                return False
            else:
                raise

    def list(self, kind: str, namespace: str | None) -> list[typing.Any]:
        """Lists a kind of object.

        Args:
          kind: The kind of object to list.

        Returns:
          The objects.
        """
        cmd = ["get", kind, "-o", "json"]
        if namespace:
            cmd += ["-n", namespace]
        o = _run_kubectl(
            cmd,
            cloud=self.cloud,
            kube_config_file=self.kube_config_file,
            check=True,
        )
        return json.loads(o.stdout)["items"]

    def get_object(self, kind: str, name: str, namespace: str):
        cmd = ["get", kind, "-o", "json", name]
        if namespace:
            cmd += ["-n", namespace]
        o = _run_kubectl(
            cmd,
            cloud=self.cloud,
            kube_config_file=self.kube_config_file,
            check=True,
        )
        return json.loads(o.stdout)

    def debug_pod(
        self, pod_name: str, container: str, image: str, namespace: str | None
    ) -> KubectlOutput:
        cmd = ["debug"]
        if namespace:
            cmd += ["-n", namespace]
        cmd += [
            "-it",
            "--profile=general",
            pod_name,
            "--target",
            container,
            "--image",
            image,
        ]
        return _run_kubectl(
            cmd,
            cloud=self.cloud,
            kube_config_file=self.kube_config_file,
            check=False,
            capture_output=False,
        )

    def find_by_label(
        self,
        namespace: str,
        label_key: str,
        label_value: str,
        kinds: typing.Sequence[str] | None = None,
    ) -> Iterator[KubeObject]:
        # . needs to be escaped, particularly for labels like
        # app.kubernetes.io/name
        label_key = label_key.replace(".", "\\.")

        if kinds is None:
            kinds = SUPPORTED_KINDS
        cmd = [
            "get",
            ",".join(kinds),
            "-n",
            namespace,
            "--show-managed-fields=false",
            "-o",
            "jsonpath-as-json={.items[?(@.metadata.labels."
            + label_key
            + '=="'
            + label_value
            + '")]}',
        ]
        result = _run_kubectl(
            cmd,
            cloud=self.cloud,
            kube_config_file=self.kube_config_file,
            check=True,
        )
        data = json.loads(result.stdout)
        for item in data:
            yield KubeObject(
                api_version=item["apiVersion"],
                kind=item["kind"],
                name=item["metadata"]["name"],
                namespace=item["metadata"].get("namespace"),
            )

    @contextmanager
    def port_forward(
        self, namespace: str, pod_or_service: str, remote_port: int, local_port: int
    ):
        """Create a port forwarding to a given pod or service."""
        assert namespace
        assert pod_or_service
        assert remote_port
        cmd = [_KUBECTL_BIN]
        context = cloud_lib.get_context(self.cloud)
        # set config is a config is request and if there is a kube_config file
        # is there is no kube config file, we have to use whatever the in-cluster
        # context is
        if context and self.kube_config_file:
            cmd += ["--context", context]
        if self.kube_config_file:
            cmd += ["--kubeconfig", str(self.kube_config_file)]

        cmd += [
            "port-forward",
            "--namespace",
            namespace,
            pod_or_service,
            f"{local_port}:{remote_port}",
        ]
        with subprocess.Popen(cmd, stdout=subprocess.PIPE) as p:
            assert p.stdout
            # wait for kubectl port-forward to be ready
            line = p.stdout.readline()
            while line:
                if line.startswith(b"Forwarding"):
                    break
                line = p.stdout.readline()
            try:
                yield local_port
            finally:
                p.kill()


class NullKubectl(Kubectl):
    """Null implementation of kubectl."""

    @property
    def kube_config_file(self) -> pathlib.Path | None:
        return None

    def apply(
        self,
        config_file: pathlib.Path,
        check: bool = True,
        extra_args: typing.Sequence[str] | None = None,
    ):
        return KubectlOutput(None, "", "", 0)

    def delete(
        self,
        config_file: pathlib.Path,
        check: bool = True,
        extra_args: typing.Sequence[str] | None = None,
    ) -> KubectlOutput:
        return KubectlOutput(None, "", "", 0)

    def diff(self, config_file: pathlib.Path, check: bool = True) -> KubectlOutput:
        return KubectlOutput(None, "", "", 0)

    def delete_object(self, obj: KubeObject, check: bool = True) -> KubectlOutput:
        return KubectlOutput(None, "", "", 0)

    def find_object(self, obj: KubeObject) -> bool:
        return False

    def get_object(self, kind: str, name: str, namespace: str) -> typing.Any | None:
        return None

    def debug_pod(
        self, pod_name: str, container: str, image: str, namespace: str | None
    ) -> KubectlOutput:
        return KubectlOutput(None, "", "", 0)

    def list(self, kind: str, namespace: str | None) -> list[typing.Any]:
        return []

    def find_by_label(
        self,
        namespace: str,
        label_key: str,
        label_value: str,
        kinds: typing.Sequence[str] | None = None,
    ) -> Iterator[KubeObject]:
        return iter([])

    def run(self, args: "list[str]", check: bool = True) -> KubectlOutput:
        return KubectlOutput(None, "", "", 0)

    @contextmanager
    def port_forward(
        self, namespace: str, pod_or_service: str, remote_port: int, local_port: int
    ) -> typing.Generator[int, None, None]:
        yield local_port


def create_kubectl(
    cloud: str,
    kube_config_file: pathlib.Path | None,
) -> Kubectl:
    """Returns a kubectl command for the given cloud.

    Args:
      cloud: The cloud to create the command for.
      kube_config_file: The kube config file to use.

    Returns:
      The kubectl command.
    """
    return KubectlImpl(cloud, kube_config_file)


def contains_error(stderr: str) -> bool:
    """Returns whether the stderr contains an error

    Args:
      stderr: The stderr to check.

    Returns:
      Whether the stderr contains a diff error.
    """
    for line in stderr.splitlines():
        if not line:
            continue
        if re.match(r"^Warning:.*", line):
            # if a line starts with "Warning" it is not an error
            continue
        return True
    return False


def is_immutable_job(stderr: str) -> bool:
    """Returns whether the stderr contains an error about an immutable job

    Args:
      stderr: The stderr to check.

    Returns:
      Whether the stderr contains an error about trying to update an immutable job

    This is a hack, but is also easier than any other solutions I could think of
    """
    lines = stderr.strip().splitlines()
    if len(lines) == 1:
        if re.match(
            r'^The Job "[a-zA-Z0-9-]+" is invalid: spec.template: Invalid value.*',
            lines[0],
        ) and re.match(r".*field is immutable$", lines[0]):
            return True
    return False


def get_dev_namespace():
    """Returns the dev namespace for the current user."""
    if "BUILD_USER_NAMESPACE" in os.environ:
        return os.environ["BUILD_USER_NAMESPACE"]
    with pathlib.Path.home().joinpath(".augment", "user.json").open(
        encoding="utf-8"
    ) as user_file:
        user_data = json.load(user_file)
        user_name = user_data["name"]
        return f"dev-{user_name}"
