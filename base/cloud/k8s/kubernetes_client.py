"""Helper class to interact with the Kubernetes API."""

import pathlib
import typing

import kubernetes
from kubernetes import client, config
from kubernetes.client import AppsV1Api, BatchV1Api, CoreV1Api, CustomObjectsApi

from base.python.cloud import cloud as cloud_lib


class KubernetesClient(typing.Protocol):
    """Helper class to interact with the Kubernetes API."""

    def login(self, cloud: str):
        """Login or re-login into kubernetes."""
        raise NotImplementedError()

    def get_apps_api(self, cloud: str) -> AppsV1Api:
        """Return the apps api for the given cloud."""
        raise NotImplementedError()

    def get_core_api(self, cloud: str) -> CoreV1Api:
        """Return the core api for the given cloud."""
        raise NotImplementedError()

    def get_batch_api(self, cloud: str) -> BatchV1Api:
        """Return the batch api for the given cloud."""
        raise NotImplementedError()

    def get_custom_api(self, cloud: str) -> CustomObjectsApi:
        """Return the custom api for the given cloud."""
        raise NotImplementedError()


def _get_api_config(
    kube_config: pathlib.Path | None, context: str | None
) -> client.ApiClient | None:
    """Return the api config for the given context."""
    if not kube_config:
        return None
    contexts, active_context = config.list_kube_config_contexts(str(kube_config))
    assert contexts, "Cannot find any context in kube config"
    contexts = [context["name"] for context in contexts]  # type: ignore
    active_index = contexts.index(active_context["name"])
    if context:
        active_index = contexts.index(context)
        assert active_index >= 0

    return config.new_client_from_config(
        config_file=str(kube_config), context=contexts[active_index]
    )


class KubernetesClientImpl(KubernetesClient):
    """Implementation of the KubernetesClient."""

    def __init__(self, kube_config_file: pathlib.Path | None):
        self.kube_config_file = kube_config_file
        if self.kube_config_file and not self.kube_config_file.exists():
            raise ValueError(f"Kube config file {self.kube_config_file} does not exist")

    def login(self, cloud: str):
        """Login or re-login into kubernetes."""
        context = cloud_lib.get_context(cloud)
        if not context:
            raise ValueError(f"Unknown cloud {cloud}")
        if not self.kube_config_file:
            kubernetes.config.load_incluster_config()  # type: ignore
        else:
            kubernetes.config.kube_config.load_kube_config(
                config_file=str(self.kube_config_file), context=context
            )  # type: ignore

    def get_apps_api(self, cloud: str) -> AppsV1Api:
        context = cloud_lib.get_context(cloud)
        if not context:
            raise ValueError(f"Unknown cloud {cloud}")
        v1 = AppsV1Api(
            _get_api_config(
                kube_config=self.kube_config_file if self.kube_config_file else None,
                context=context,
            )
        )
        return v1

    def get_core_api(self, cloud: str) -> CoreV1Api:
        context = cloud_lib.get_context(cloud)
        if not context:
            raise ValueError(f"Unknown cloud {cloud}")
        v1 = CoreV1Api(
            _get_api_config(
                kube_config=self.kube_config_file if self.kube_config_file else None,
                context=context,
            )
        )
        return v1

    def get_batch_api(self, cloud: str) -> BatchV1Api:
        context = cloud_lib.get_context(cloud)
        if not context:
            raise ValueError(f"Unknown cloud {cloud}")
        v1 = BatchV1Api(
            _get_api_config(
                kube_config=self.kube_config_file if self.kube_config_file else None,
                context=context,
            )
        )
        return v1

    def get_custom_api(self, cloud: str) -> CustomObjectsApi:
        context = cloud_lib.get_context(cloud)
        if not context:
            raise ValueError(f"Unknown cloud {cloud}")
        v1 = CustomObjectsApi(
            _get_api_config(
                kube_config=self.kube_config_file if self.kube_config_file else None,
                context=context,
            )
        )
        return v1


def create_kubernetes_client(kube_config_file: pathlib.Path | None) -> KubernetesClient:
    """Create a KubernetesClient.

    Args:
      kube_config_file: The kube config file to use. If None, the in-cluster config is used.

    Returns:
      The KubernetesClient.
    """
    return KubernetesClientImpl(kube_config_file)
