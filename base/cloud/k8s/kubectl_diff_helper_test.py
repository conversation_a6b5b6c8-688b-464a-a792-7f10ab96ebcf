from base.cloud.k8s import kubectl_diff_helper


def test_kubectl_diff_helper_splits_object_in_unknown_namespace():
    existing_namespaces = ["ns1"]

    config = [
        {
            "kind": "ServiceAccount",
            "metadata": {
                "name": "sa-ns1",
                "namespace": "ns1",
            },
        },
        {
            "kind": "ServiceAccount",
            "metadata": {
                "name": "sa-ns2",
                "namespace": "ns2",
            },
        },
        {
            "kind": "ClusterRole",
            "metadata": {
                "name": "some-cluster-role",
            },
        },
    ]

    split_output = kubectl_diff_helper.split_objects_in_new_namespaces(
        config, existing_namespaces
    )

    assert split_output.new_namespace_objects == [config[1]]
    assert split_output.rest_of_objects == [config[0], config[2]]
