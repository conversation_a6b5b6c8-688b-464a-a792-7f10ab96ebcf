"""Helper functions to interact with the docker registry."""

import pathlib
import json


def prepare_docker_credential_helper(auth_helper: str = "gcr"):
    """Ensure that the right docker push authentication helper is called.

    Args:
        auth_helper: The authentication helper to use. A tool with the name 'docker-credential-helper-${auth_helper}'
            must be available in the PATH.
    """
    pathlib.Path.home().joinpath(".docker").mkdir(exist_ok=True)
    docker_config_path = pathlib.Path.home().joinpath(".docker", "config.json")
    if docker_config_path.exists():
        docker_config = json.loads(docker_config_path.read_text(encoding="utf-8"))
    else:
        docker_config = {}
    if "credHelpers" not in docker_config:
        docker_config["credHelpers"] = {}
    for pkg_dev in [
        "us-central1-docker.pkg.dev",
        "europe-west4-docker.pkg.dev",
        "asia-southeast1-docker.pkg.dev",
    ]:
        if pkg_dev not in docker_config["credHelpers"]:
            docker_config["credHelpers"][pkg_dev] = auth_helper
    pathlib.Path.home().joinpath(".docker", "config.json").write_text(
        json.dumps(docker_config, indent=2),
        encoding="utf-8",
    )
