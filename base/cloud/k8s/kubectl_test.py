"""Tests for the kubectl library."""

from base.cloud.k8s import kubectl


def test_contains_error():
    stderr = r"""Warning: kubectl apply should be used on resource created by either kubectl create --save-config or kubectl apply
"""
    assert not kubectl.contains_error(stderr)

    stderr = r"""Warning: kubectl apply should be used on resource created by either kubectl create --save-config or kubectl apply
error: error when retrieving current configuration of:
Resource: "apps/v1, Resource=deployments", GroupVersionKind: "apps/v1, Kind=Deployment"
Name: "slack-bot", Namespace: "devtools"
from server for: "/tmp/MERGED-438954232/apps.v1.Deployment.devtools.slack-bot.yaml": resource name may not be empty
"""
    assert kubectl.contains_error(stderr)


def test_contains_error_empty():
    stderr = ""
    assert not kubectl.contains_error(stderr)
