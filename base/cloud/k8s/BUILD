load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

go_library(
    name = "k8s_go",
    srcs = ["k8s.go"],
    importpath = "github.com/augmentcode/augment/base/cloud/k8s",
    visibility = BASE_VISIBILITY,
)

py_library(
    name = "docker",
    srcs = [
        "docker.py",
    ],
    visibility = BASE_VISIBILITY,
)

py_library(
    name = "kubectl",
    srcs = [
        "kubectl.py",
    ],
    data = [
        "@k8s_binary//file:kubectl",
    ],
    visibility = BASE_VISIBILITY + ["//base/python/k8s_test_helper:__subpackages__"],
    deps = [
        ":kubectl_diff_helper",
        "//base/python/cloud",
        requirement("pyyaml"),
    ],
)

py_library(
    name = "kubectl_factory",
    srcs = [
        "kubectl_factory.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        ":kubectl",
    ],
)

py_library(
    name = "fake_kubectl",
    testonly = True,
    srcs = [
        "fake_kubectl.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        ":kubectl",
        ":kubectl_factory",
    ],
)

py_library(
    name = "kubernetes_client",
    srcs = [
        "kubernetes_client.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/python/cloud",
        requirement("kubernetes"),
    ],
)

py_library(
    name = "kubectl_diff_helper",
    srcs = [
        "kubectl_diff_helper.py",
    ],
    visibility = ["//visibility:private"],
)

pytest_test(
    name = "kubectl_test",
    srcs = ["kubectl_test.py"],
    deps = [
        ":kubectl",
    ],
)

pytest_test(
    name = "kubectl_diff_helper_test",
    srcs = ["kubectl_diff_helper_test.py"],
    deps = [
        ":kubectl_diff_helper",
    ],
)
