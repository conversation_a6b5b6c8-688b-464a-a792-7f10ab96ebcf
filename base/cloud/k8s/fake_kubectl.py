"""Fake kubectl implementation.

This is used for testing.
"""

import pathlib
import typing
from dataclasses import dataclass

import yaml

from base.cloud.k8s import kubectl, kubectl_factory


@dataclass
class KubectlCall:
    """Represents a kubectl call."""

    method: str
    config_file: pathlib.Path | None
    config_file_content: str | None


class FakeKubectl(kubectl.Kubectl):
    """Fake kubectl implementation."""

    def __init__(self):
        self.calls: list[KubectlCall] = []
        self.delete_objects: list[kubectl.KubeObject] = []
        self.objects = []

    def __repr__(self):
        return f"FakeKubectl({self.objects}, {self.calls}, {self.delete_objects})"

    def _match(self, o1, o2) -> bool:
        return (
            o1["kind"].lower() == o2["kind"].lower()
            and o1["metadata"]["name"] == o2["metadata"]["name"]
            and o1["metadata"]["namespace"] == o2["metadata"]["namespace"]
        )

    @property
    def kube_config_file(self) -> pathlib.Path | None:
        return None

    def apply(
        self,
        config_file: pathlib.Path,
        check: bool = True,
        extra_args: typing.Sequence[str] | None = None,
    ) -> kubectl.KubectlOutput:
        del extra_args
        del check
        content = config_file.read_text()
        c = yaml.safe_load_all(content)
        for obj in c:
            self.objects.append(obj)
        self.calls.append(KubectlCall("apply", config_file, content))
        return kubectl.KubectlOutput(None, "", "", 0)

    def delete(
        self, config_file: pathlib.Path, check: bool = True
    ) -> kubectl.KubectlOutput:
        content = config_file.read_text()
        c = yaml.safe_load_all(content)
        for obj in c:
            self.objects = [o for o in self.objects if not self._match(o, obj)]

        self.calls.append(KubectlCall("delete", config_file, content))
        return kubectl.KubectlOutput(None, "", "", 0)

    def diff(
        self, config_file: pathlib.Path, check: bool = True
    ) -> kubectl.KubectlOutput:
        content = config_file.read_text()
        self.calls.append(KubectlCall("diff", config_file, content))
        return kubectl.KubectlOutput(None, "", "", 0)

    def delete_object(
        self, obj: kubectl.KubeObject, check: bool = True
    ) -> kubectl.KubectlOutput:
        self.delete_objects.append(obj)
        self.objects = [
            o
            for o in self.objects
            if o["kind"] != obj.kind
            or o["metadata"]["name"] != obj.name
            or o["metadata"]["namespace"] != obj.namespace
            or o["apiVersion"] != obj.api_version
        ]

        return kubectl.KubectlOutput(None, "", "", 0)

    def find_by_label(
        self,
        namespace: str,
        label_key: str,
        label_value: str,
        kinds: typing.Sequence[str] | None = None,
    ) -> typing.Iterable[kubectl.KubeObject]:
        """Finds objects by label."""
        if kinds is None:
            kind_set = set(k.lower() for k in kubectl.SUPPORTED_KINDS)
        else:
            kind_set = set(k.lower() for k in kinds)
        for o in self.objects:
            if (
                o["kind"].lower() in kind_set
                and o["metadata"]["namespace"] == namespace
                and label_key in o["metadata"]["labels"]
                and o["metadata"]["labels"][label_key] == label_value
            ):
                yield kubectl.KubeObject(
                    o["apiVersion"],
                    o["kind"],
                    o["metadata"]["name"],
                    o["metadata"]["namespace"],
                )


class FakeKubectlFactory(kubectl_factory.KubectlFactory):
    """Factory for kubectl commands."""

    def __init__(self):
        self.kubectl = FakeKubectl()

    def __call__(self, cloud: str) -> kubectl.Kubectl:
        return self.kubectl
