from dataclasses import dataclass
from typing import Any, Sequence


ParsedK8SObject = dict[str, Any]


@dataclass
class SplitOutput:
    new_namespace_objects: list[ParsedK8SObject]
    rest_of_objects: list[ParsedK8SObject]


def split_objects_in_new_namespaces(
    config: Sequence[ParsedK8SObject], existing_namespaces: Sequence[str]
) -> SplitOutput:
    """Splits out namespaced objects not related to existing namespaces. kubectl diff
    errors out on those.

    Args:
      config: list of k8s objects
      existing_namespaces: The existing namespaces.

    Returns:
      SplitOutput with new_namespace_objects and rest_of_objects.
    """
    new_namespace_objects = []
    rest_of_objects = []
    for obj in config:
        namespace = obj.get("metadata", {}).get("namespace", "")
        if namespace and namespace not in existing_namespaces:
            new_namespace_objects.append(obj)
            continue

        rest_of_objects.append(obj)

    return SplitOutput(new_namespace_objects, rest_of_objects)
