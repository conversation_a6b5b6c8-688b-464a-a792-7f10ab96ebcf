"""General logging setup for console applications."""

import logging
import os
import sys


def setup_console_logging(debug: bool = False, add_timestamp: bool = True):
    """Setup a default format for logging to be used outside of containers."""
    if not debug:
        debug = "LOG_DEBUG" in os.environ
    logging.basicConfig(
        level=logging.DEBUG if debug else logging.INFO,
        stream=sys.stdout,
        format="%(asctime)s %(levelname)s %(message)s"
        if add_timestamp
        else "%(message)s",
    )
