load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

npm_link_all_packages()

go_library(
    name = "logging_go",
    srcs = ["logging.go"],
    importpath = "github.com/augmentcode/augment/base/logging",
    visibility = BASE_VISIBILITY,
    deps = [
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
    ],
)

py_library(
    name = "struct_logging",
    srcs = ["struct_logging.py"],
    visibility = BASE_VISIBILITY,
    deps = [
        requirement("structlog"),
        "//third_party/structlog_gcp",
    ],
)

py_library(
    name = "console_logging",
    srcs = ["console_logging.py"],
    visibility = BASE_VISIBILITY + ["//deploy:__subpackages__"],
)

py_library(
    name = "secret_logging",
    srcs = ["secret_logging.py"],
    visibility = ["//visibility:public"],
)

pytest_test(
    name = "struct_logging_test",
    srcs = ["struct_logging_test.py"],
    deps = [":struct_logging"],
)

rust_library(
    name = "struct_logging_rs",
    srcs = ["struct_logging.rs"],
    aliases = aliases(),
    crate_name = "struct_logging",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//third_party/tracing-stackdriver",
    ],
)

rust_test(
    name = "struct_logging_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":struct_logging_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//visibility:public"],
)

ts_project(
    name = "ts_logging",
    srcs = ["logging.ts"],
    allow_js = True,
    composite = True,
    declaration = True,
    incremental = True,
    out_dir = "ts_dist",
    resolve_json_module = True,
    ts_build_info_file = "ts_dist/.tsbuildinfo",
    tsconfig = ":tsconfig",
    visibility = ["//visibility:public"],
    deps = [
        ":node_modules/@augment-internal/systemenv",
        ":node_modules/@types/node",
        ":node_modules/winston",
    ],
)

js_library(
    name = "pkg",
    srcs = [
        "package.json",
        ":ts_logging",
    ],
    visibility = ["//visibility:public"],
)
