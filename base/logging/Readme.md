# Base Logging

This directory contains shared infrastructure for logging for Python and Rust.
The package should be called `//base/logging`, but that is conflicting with the `logging` package.

## Structured Logging

`:struct_logging` contains a default setup of structured logging that is correctly parsed by logging infrastructure.
It should be the default logging for Python containers running in Kubernetes.

`:struct_logging_rs` contains a similar default setup for Rust.

## Trace integration

Integration of logging and tracing is currently only available for Rust. The current integration is based on uploading
logs using the OpenTelemetry protocol, rather than annotating existing log lines with the needed span information. As a result,
Rust users of this library currently send log lines to two places: stdout and an OpenTelemetry server.

We are thinking about annotating our structured logs with span information. This approach would integrate more seamlessly
with Python and Go logging. For Rust, this would get rid of the need to upload log lines to OpenTelemetry.

This requires a tracing system that is integrating with a logging system, e.g. Google Cloud Trace which integrates with
Google Cloud Logging. If the two systems are not integrated, then log lines need to be uploaded to the trace system to
appear in the traces.
