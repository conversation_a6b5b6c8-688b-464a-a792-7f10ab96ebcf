[package]
name = "struct_logging"
version = "0.1.0"
edition = "2021"

[lib]
name = "struct_logging"
path = "struct_logging.rs"

[dependencies]
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
opentelemetry_sdk =  { workspace = true, features = ["rt-tokio", "rt-async-std"] }
tracing-log = { workspace = true }
opentelemetry =  { workspace = true }
opentelemetry-otlp =  { workspace = true }
tracing-stackdriver = { path = "../../third_party/tracing-stackdriver" }
tracing-opentelemetry = { workspace = true }
