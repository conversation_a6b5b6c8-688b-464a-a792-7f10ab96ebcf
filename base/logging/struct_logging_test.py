"""Contains tests for struct_logging module."""

import contextlib
import io
import json
import logging

import structlog

from base.logging.struct_logging import setup_struct_logging


def test_struct_logging_basic():
    """Contains a basic test with logging module usage."""
    stderr = io.StringIO()
    stdout = io.StringIO()
    with contextlib.redirect_stderr(stderr), contextlib.redirect_stdout(stdout):
        setup_struct_logging()

        log = logging.getLogger("log")
        log.info("Hello")
    assert not stdout.getvalue()
    output = stderr.getvalue()
    data = json.loads(output)
    assert "time" in data
    assert "message" in data
    assert data["message"] == "Hello"
    assert data["severity"] == "INFO"
    assert "logging.googleapis.com/sourceLocation" in data


def test_struct_logging_with_format():
    """Contains a test with logging module usage with formatting."""
    stderr = io.StringIO()
    stdout = io.StringIO()
    with contextlib.redirect_stderr(stderr), contextlib.redirect_stdout(stdout):
        setup_struct_logging()

        log = logging.getLogger("log")
        log.info("Hello %s", "World")
    assert not stdout.getvalue()
    output = stderr.getvalue()
    data = json.loads(output)
    assert "time" in data
    assert "message" in data
    assert data["message"] == "Hello World"
    assert data["severity"] == "INFO"
    assert "logging.googleapis.com/sourceLocation" in data


def test_struct_logging_exception():
    """Contains a basic test with logging module usage."""
    stderr = io.StringIO()
    stdout = io.StringIO()
    with contextlib.redirect_stderr(stderr), contextlib.redirect_stdout(stdout):
        setup_struct_logging()

        try:
            raise ValueError("foo")
        except ValueError as e:
            log = logging.getLogger("log")
            log.exception(e)
    assert not stdout.getvalue()
    output = stderr.getvalue()
    data = json.loads(output)
    assert "time" in data
    assert "message" in data
    assert data["message"] == "foo"
    assert data["severity"] == "CRITICAL"
    assert "logging.googleapis.com/sourceLocation" in data


def test_struct_logging_structlog():
    """Contains a basic test with structlog module usage."""
    stderr = io.StringIO()
    stdout = io.StringIO()
    with contextlib.redirect_stderr(stderr), contextlib.redirect_stdout(stdout):
        setup_struct_logging()

        log = structlog.get_logger("log")
        log.info("Hello", user="bob")
    assert not stdout.getvalue()
    output = stderr.getvalue()
    data = json.loads(output)
    assert "time" in data
    assert "message" in data
    assert data["message"] == "Hello"
    assert data["severity"] == "INFO"
    assert "logging.googleapis.com/sourceLocation" in data
    # TODO figure out user should be rendered


def test_struct_logging_structlog_with_context():
    """Contains a basic test with structlog module usage."""
    stderr = io.StringIO()
    stdout = io.StringIO()
    with contextlib.redirect_stderr(stderr), contextlib.redirect_stdout(stdout):
        setup_struct_logging()

        log = structlog.get_logger("log")
        log = log.bind(user="bob")
        log.info("Hello")
    assert not stdout.getvalue()
    output = stderr.getvalue()
    data = json.loads(output)
    assert "time" in data
    assert "message" in data
    assert data["message"] == "Hello"
    assert data["severity"] == "INFO"
