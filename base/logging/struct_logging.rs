use opentelemetry::trace::TracerProvider;

use tracing::dispatcher::set_global_default;
use tracing_log::LogTracer;
use tracing_subscriber::{prelude::__tracing_subscriber_SubscriberExt, EnvFilter, Registry};

pub fn setup_struct_logging() -> Result<(), Box<dyn std::error::Error>> {
    LogTracer::init()?;
    // Configure logging level using RUST_LOG variable
    // If RUST_LOG not present or misparses, use LOG_DEBUG.
    let env_filter = EnvFilter::try_from_default_env().unwrap_or_else(|_| {
        EnvFilter::new(match std::option_env!("LOG_DEBUG") {
            None => "info",
            Some(_) => "debug",
        })
    });

    // Various formats exist for propagating tracing info in HTTP headers (inc. gRPC which uses HTTP headers)
    // Let's use the W3C standard Trace Context.
    //
    // This call to set_text_map_propagator is only useful if we write code elsewhere that calls
    // get_text_map_propagator (e.g. in the code that makes RPC calls using Tonic). The hope is that as libraries
    // evolve, they will call get_text_map_propagator instead of us.
    //
    // The default propagator for OpenTelemetry is a no-op, presumably to minimize accidental information
    // leakage.
    opentelemetry::global::set_text_map_propagator(
        opentelemetry_sdk::propagation::TraceContextPropagator::new(),
    );

    let otlp_exporter = opentelemetry_otlp::SpanExporter::builder()
        .with_tonic()
        .build()?;

    // Tonic implies gRPC transport for OpenTelemetry rather than HTTP
    //
    // Batching means the spans will be uploaded in the background
    let tracer_provider = opentelemetry_sdk::trace::TracerProvider::builder()
        .with_batch_exporter(otlp_exporter, opentelemetry_sdk::runtime::Tokio)
        .build();

    let tracer = tracer_provider.tracer("");

    let telemetry = tracing_opentelemetry::layer().with_tracer(tracer);

    let stackdriver = tracing_stackdriver::layer(); // writes to std::io::Stdout

    let subscriber = Registry::default()
        .with(env_filter)
        .with(stackdriver)
        .with(telemetry);
    set_global_default(subscriber.into())?;
    Ok(())
}
