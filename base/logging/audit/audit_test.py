import pytest
import json
from base.logging.audit.audit import <PERSON>t<PERSON>og<PERSON>, Request


@pytest.fixture
def audit_logger():
    return AuditLogger()


def test_write_audit_log(audit_logger, capsys):
    user_id = "user-123"
    tenant_name = "tenant-123"
    resource = Request(request_id="request-123")
    message = "This is a test audit log"

    audit_logger.write_audit_log(
        user_id,
        "INTERNAL_IAP",
        tenant_name,
        resource,
        message,
    )
    captured = capsys.readouterr()
    output_json = json.loads(captured.out)

    assert output_json["@type"] == "type.eng.augmentcode.com/AuditLog"
    assert output_json["authenticationInfo"]["principal"] == user_id
    assert output_json["authenticationInfo"]["principal"] == user_id
    assert output_json["tenant"]["name"] == tenant_name
    assert output_json["resource"]["@type"] == "type.eng.augmentcode.com/Request"
    assert output_json["resource"]["name"] == "request_id"
    assert output_json["resource"]["value"] == "request-123"
    assert output_json["message"] == message


def test_write_audit_log_no_tenant_name(audit_logger, capsys):
    user_id = "user-123"
    resource = Request(request_id="request-123")
    message = "This is a test audit log"

    audit_logger.write_audit_log(
        user_id,
        "INTERNAL_IAP",
        None,
        resource,
        message,
    )
    captured = capsys.readouterr()
    output_json = json.loads(captured.out)

    assert output_json["@type"] == "type.eng.augmentcode.com/AuditLog"
    assert output_json["authenticationInfo"]["principal"] == user_id
    assert "tenant" not in output_json
    assert output_json["resource"]["@type"] == "type.eng.augmentcode.com/Request"
    assert output_json["resource"]["name"] == "request_id"
    assert output_json["resource"]["value"] == "request-123"
    assert output_json["message"] == message


def test_write_audit_log_no_resource(audit_logger, capsys):
    user_id = "user-123"
    tenant_name = "tenant-123"
    message = "This is a test audit log"

    audit_logger.write_audit_log(
        user_id,
        "INTERNAL_IAP",
        tenant_name,
        None,
        message,
    )
    captured = capsys.readouterr()
    output_json = json.loads(captured.out)

    assert output_json["@type"] == "type.eng.augmentcode.com/AuditLog"
    assert output_json["authenticationInfo"]["principal"] == user_id
    assert output_json["authenticationInfo"]["principal"] == user_id
    assert output_json["tenant"]["name"] == tenant_name
    assert "resource" not in output_json
    assert output_json["message"] == message


def test_write_audit_log_no_message(audit_logger, capsys):
    user_id = "user-123"
    tenant_name = "tenant-123"
    resource = Request(request_id="request-123")

    audit_logger.write_audit_log(
        user_id,
        "INTERNAL_IAP",
        tenant_name,
        resource,
        None,
    )
    captured = capsys.readouterr()
    output_json = json.loads(captured.out)

    assert output_json["@type"] == "type.eng.augmentcode.com/AuditLog"
    assert output_json["authenticationInfo"]["principal"] == user_id
    assert output_json["tenant"]["name"] == tenant_name
    assert output_json["resource"]["@type"] == "type.eng.augmentcode.com/Request"
    assert output_json["resource"]["name"] == "request_id"
    assert output_json["resource"]["value"] == "request-123"
    assert "message" not in output_json
