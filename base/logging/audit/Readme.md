# Audit Logging

This is a special log that is used to track access to resticted resources.

A specially formatted log entry is written that can be used to track access to resources.

## Rust

```rust
use base::logging::audit::AuditLogger;
use base::logging::audit::Resource;

let audit_logger = AuditLogger::new();
audit_logger.write_audit_log(
    "user-123",
    Some("tenant-123"),
    Some(Resource::Request {
        request_id: "request-123".to_string(),
    }),
    Some("This is a test audit log"),
);
```

## Python

```python
from base.logging.audit.audit import AuditLogger, Request

audit_logger = AuditLogger()
audit_logger.write_audit_log(
    user_id="user-123",
    tenant_name="tenant-123",
    resource=Request(request_id="request-123"),
    message="This is a test audit log",
)
```
