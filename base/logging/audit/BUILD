load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "audit_py",
    srcs = ["audit.py"],
    visibility = BASE_VISIBILITY,
    deps = [
        requirement("pydantic"),
    ],
)

pytest_test(
    name = "audit_test",
    srcs = ["audit_test.py"],
    deps = [":audit_py"],
)

rust_library(
    name = "audit_rs",
    srcs = ["audit.rs"],
    aliases = aliases(),
    crate_name = "audit",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ),
)

rust_test(
    name = "audit_test_rs",
    crate = ":audit_rs",
)

go_library(
    name = "audit_go",
    srcs = [
        "audit.go",
    ],
    importpath = "github.com/augmentcode/augment/base/logging/audit",
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/proto/redact",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "audit_go_test",
    srcs = ["audit_test.go"],
    embed = [
        ":audit_go",
    ],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)
