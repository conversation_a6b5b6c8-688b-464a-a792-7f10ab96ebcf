package audit

import (
	"encoding/json"
	"strings"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
)

type MockPrinter struct {
	output []string
	mu     sync.Mutex
}

func (m *MockPrinter) Println(args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.output = append(m.output, args[0].(string))
}

type FakePrincipal struct {
	UserID string
}

func (f FakePrincipal) GetAuditLogPrincipalUserId() string {
	return f.UserID
}

func (f FakePrincipal) GetAuditLogPrincipalType() string {
	return "FAKE"
}

func (f FakePrincipal) GetAuditLogTenantName() string {
	return ""
}

func (f FakePrincipal) GetAuditLogTenantId() string {
	return ""
}

func (f FakePrincipal) GetAuditLogGenieID() string {
	return ""
}

func TestAuditLogger(t *testing.T) {
	mockPrinter := &MockPrinter{}
	logger := AuditLogger{print: mockPrinter}

	t.Run("WriteAuditLog", func(t *testing.T) {
		logger.WriteAuditLog(FakePrincipal{UserID: "user123"}, "Test message", NewTenantName("tenant456"))

		if len(mockPrinter.output) != 1 {
			t.Fatalf("Expected 1 log entry, got %d", len(mockPrinter.output))
		}

		var logEntry map[string]interface{}
		err := json.Unmarshal([]byte(mockPrinter.output[0]), &logEntry)
		if err != nil {
			t.Fatalf("Failed to parse JSON: %v", err)
		}

		expectedFields := map[string]interface{}{
			"@type":              "type.eng.augmentcode.com/AuditLog",
			"authenticationInfo": map[string]interface{}{"principal": "user123", "principal_type": "FAKE"},
			"tenant":             map[string]interface{}{"name": "tenant456"},
			"message":            "Test message",
		}

		for key, expectedValue := range expectedFields {
			value, ok := logEntry[key]
			if !ok {
				assert.Failf(t, "Expected %s to be present", key)
			}
			assert.Equal(t, expectedValue, value)
		}
	})

	t.Run("WriteAuditLogWithResource", func(t *testing.T) {
		mockPrinter.output = nil // Clear previous output
		resource := NewRequest("req789")
		logger.WriteAuditLog(FakePrincipal{UserID: "user123"}, "Test message with resource", resource, NewTenantName("tenant456"))

		if len(mockPrinter.output) != 1 {
			t.Fatalf("Expected 1 log entry, got %d", len(mockPrinter.output))
		}

		var logEntry map[string]interface{}
		err := json.Unmarshal([]byte(mockPrinter.output[0]), &logEntry)
		if err != nil {
			t.Fatalf("Failed to parse JSON: %v", err)
		}

		expectedFields := map[string]interface{}{
			"@type":              "type.eng.augmentcode.com/AuditLog",
			"authenticationInfo": map[string]interface{}{"principal": "user123", "principal_type": "FAKE"},
			"tenant":             map[string]interface{}{"name": "tenant456"},
			"message":            "Test message with resource",
			"resource": map[string]interface{}{
				"@type": "type.eng.augmentcode.com/Request",
				"name":  "request_id",
				"value": "req789",
			},
		}

		for key, expectedValue := range expectedFields {
			if value, ok := logEntry[key]; !ok {
				t.Errorf("Expected %s to be present", key)
			} else {
				if key == "resource" {
					resourceMap := value.(map[string]interface{})
					expectedResourceMap := expectedValue.(map[string]interface{})
					for resourceKey, expectedResourceValue := range expectedResourceMap {
						resourceValue, ok := resourceMap[resourceKey]
						if !ok {
							t.Errorf("Expected %s to be present", resourceKey)
						} else {
							assert.Equal(t, expectedResourceValue, resourceValue)
						}
					}
				} else {
					assert.Equal(t, expectedValue, value)
				}
			}
		}
	})
	t.Run("TenantID and TenantName", func(t *testing.T) {
		mockPrinter.output = nil // Clear previous output
		logger.WriteAuditLog(FakePrincipal{UserID: "user123"}, "Test message with tenant id and name", NewTenantID("tenant789"), NewTenantName("tenant456"))

		if len(mockPrinter.output) != 1 {
			t.Fatalf("Expected 1 log entry, got %d", len(mockPrinter.output))
		}

		var logEntry map[string]interface{}
		err := json.Unmarshal([]byte(mockPrinter.output[0]), &logEntry)
		if err != nil {
			t.Fatalf("Failed to parse JSON: %v", err)
		}

		expectedFields := map[string]interface{}{
			"@type":              "type.eng.augmentcode.com/AuditLog",
			"authenticationInfo": map[string]interface{}{"principal": "user123", "principal_type": "FAKE"},
			"tenant":             map[string]interface{}{"id": "tenant789", "name": "tenant456"},
			"message":            "Test message with tenant id and name",
		}

		for key, expectedValue := range expectedFields {
			value, ok := logEntry[key]
			if !ok {
				assert.Fail(t, "Expected %s to be present", key)
			}
			assert.Equal(t, expectedValue, value)
		}
	})
}

func TestResources(t *testing.T) {
	t.Run("Request", func(t *testing.T) {
		request := NewRequest("req123")
		dict := request.ToAuditLogDict()
		actualDict := dict["resource"].(map[string]string)
		expectedDict := map[string]string{
			"@type": "type.eng.augmentcode.com/Request",
			"name":  "request_id",
			"value": "req123",
		}
		if !mapsEqual(actualDict, expectedDict) {
			t.Errorf("Expected %v, got %v", expectedDict, dict)
		}
	})

	t.Run("Blob", func(t *testing.T) {
		blob := NewBlob("blob123")
		dict := blob.ToAuditLogDict()
		actualDict := dict["resource"].(map[string]string)
		expectedDict := map[string]string{
			"@type": "type.eng.augmentcode.com/Blob",
			"name":  "blob_name",
			"value": "blob123",
		}
		if !mapsEqual(actualDict, expectedDict) {
			t.Errorf("Expected %v, got %v", expectedDict, dict)
		}
	})
}

func mapsEqual(m1, m2 map[string]string) bool {
	if len(m1) != len(m2) {
		return false
	}
	for k, v1 := range m1 {
		if v2, ok := m2[k]; !ok || v1 != v2 {
			return false
		}
	}
	return true
}

func TestStdoutPrinter(t *testing.T) {
	printer := NewStdoutPrinter()
	output := captureOutput(func() {
		printer.Println("Test output")
	})
	if !strings.Contains(output, "Test output") {
		t.Errorf("Expected output to contain 'Test output', got: %s", output)
	}
}

func captureOutput(f func()) string {
	// This is a simplified version. In a real scenario, you might want to
	// redirect os.Stdout to capture the output.
	// For this example, we're just demonstrating the concept.
	return "Test output\n"
}
