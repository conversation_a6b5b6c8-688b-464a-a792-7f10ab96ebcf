package audit

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"

	"github.com/augmentcode/augment/base/proto/redact"
	"google.golang.org/protobuf/proto"
)

type Printer interface {
	Println(args ...interface{})
}

type StdoutPrinter struct {
	output *sync.Mutex
}

func NewStdoutPrinter() StdoutPrinter {
	return StdoutPrinter{
		output: &sync.Mutex{},
	}
}

func (p StdoutPrinter) Println(args ...interface{}) {
	fmt.Println(args...)
	os.Stdout.Sync()
}

// AuditLogger is a logger that writes audit logs
type AuditLogger struct {
	print Printer
	env   string
}

func NewDefaultAuditLogger() *AuditLogger {
	env := os.Getenv("POD_ENV")
	return &AuditLogger{
		print: NewStdoutPrinter(),
		env:   env,
	}
}

// the principal is the user or service accessing the resource
// usually via the auth claims available. AuthClaims implements this interface
type AuditLogPrincipal interface {
	GetAuditLogPrincipalUserId() string
	GetAuditLogPrincipalType() string
	GetAuditLogTenantName() string
	GetAuditLogTenantId() string
	GetAuditLogGenieID() string
}

type DirectAuditLogPrincipal struct {
	UserId     string
	UserType   string
	TenantName string
	TenantId   string
	GenieID    string
}

func (p DirectAuditLogPrincipal) GetAuditLogPrincipalUserId() string {
	return p.UserId
}

func (p DirectAuditLogPrincipal) GetAuditLogPrincipalType() string {
	return p.UserType
}

func (p DirectAuditLogPrincipal) GetAuditLogTenantName() string {
	return p.TenantName
}

func (p DirectAuditLogPrincipal) GetAuditLogTenantId() string {
	return p.TenantId
}

func (p DirectAuditLogPrincipal) GetAuditLogGenieID() string {
	return p.GenieID
}

// extras are additional information to add to the audit log
type AuditLogExtra interface {
	ToAuditLogDict() map[string]interface{}
}

// Add the tenant id to an audit log, if the operation in the context of a tenant
//
// # Example
//
// auditLogger.WriteAuditLog(
//
//	authClaims,
//	"Migrate populate IDP user mappings",
//	NewTenantID(tenantId)
//
// )
type TenantID struct {
	ID string
}

func NewTenantID(id string) TenantID {
	return TenantID{
		ID: id,
	}
}

func (t TenantID) ToAuditLogDict() map[string]interface{} {
	return map[string]interface{}{
		"tenant": map[string]interface{}{
			"id": t.ID,
		},
	}
}

// Add the tenant name to an audit log, if the operation in the context of a tenant
//
// # Example
//
// auditLogger.WriteAuditLog(
//
//	authClaims,
//	"Migrate populate IDP user mappings",
//	NewTenantName(tenantName)
//
// )
type TenantName struct {
	Name string
}

func NewTenantName(name string) TenantName {
	return TenantName{
		Name: name,
	}
}

func (t TenantName) ToAuditLogDict() map[string]interface{} {
	return map[string]interface{}{
		"tenant": map[string]interface{}{
			"name": t.Name,
		},
	}
}

// DeepMerge recursively merges src into dst.
// If a key exists in both maps:
// - If both values are maps, they are recursively merged.
// - Otherwise, the value from src overwrites the value in dst.
// If a key exists only in src, it's added to dst.
func deepMerge(dst, src map[string]interface{}) map[string]interface{} {
	if dst == nil {
		dst = make(map[string]interface{})
	}

	for key, srcVal := range src {
		if dstVal, ok := dst[key]; ok {
			// Key exists in both maps
			if dstMap, isDstMap := dstVal.(map[string]interface{}); isDstMap {
				if srcMap, isSrcMap := srcVal.(map[string]interface{}); isSrcMap {
					// Both are maps, recurse
					dst[key] = deepMerge(dstMap, srcMap)
				} else {
					// dst is a map, src is not - src overwrites
					dst[key] = srcVal
				}
			} else {
				// dst is not a map (or is nil), src overwrites
				dst[key] = srcVal
			}
		} else {
			// Key only exists in src, add it to dst
			dst[key] = srcVal
		}
	}
	return dst
}

// Writes an audit log record
//
// user_id is the user id of the user accessing the request, e.g. the IAP user for support or the user id for a user accessing their own data
// user_id_type is the type of user id, e.g. INTERNAL_IAP for support users or AUGMENT for normal users
// tenant_name is the name of the tenant the user is accessing
// message is a human readable message for the log
//
// Prefer using WriteAuditLogWithResource if there is a specific resource being accessed
func (l *AuditLogger) WriteAuditLog(principal AuditLogPrincipal, message string, extras ...AuditLogExtra) {
	ai := map[string]interface{}{
		"principal":      principal.GetAuditLogPrincipalUserId(),
		"principal_type": principal.GetAuditLogPrincipalType(),
	}
	if principal.GetAuditLogGenieID() != "" {
		ai["genie_id"] = principal.GetAuditLogGenieID()
	}
	data := map[string]interface{}{
		"@type":              "type.eng.augmentcode.com/AuditLog",
		"authenticationInfo": ai,
	}
	if l.env != "" {
		data["env"] = l.env
	}
	if message != "" {
		data["message"] = message
	}
	if principal.GetAuditLogTenantName() != "" || principal.GetAuditLogTenantId() != "" {
		data["tenant"] = map[string]interface{}{}
		if principal.GetAuditLogTenantName() != "" {
			data["tenant"].(map[string]interface{})["name"] = principal.GetAuditLogTenantName()
		}
		if principal.GetAuditLogTenantId() != "" {
			data["tenant"].(map[string]interface{})["id"] = principal.GetAuditLogTenantId()
		}
	}

	for _, extra := range extras {
		if extra == nil {
			continue
		}
		d := extra.ToAuditLogDict()
		data = deepMerge(data, d)
	}
	json_data, _ := json.Marshal(data)
	l.print.Println(string(json_data))
}

type ProtoRequest struct {
	msg proto.Message
}

func NewProtoRequest(msg proto.Message) ProtoRequest {
	return ProtoRequest{
		msg: msg,
	}
}

func (p ProtoRequest) ToAuditLogDict() map[string]interface{} {
	if p.msg == nil {
		return nil
	}
	r, _ := redact.ToRedactJSON(p.msg)
	return map[string]interface{}{
		"request": r,
	}
}

type ProtoResponse struct {
	msg proto.Message
}

func NewProtoResponse(msg proto.Message) ProtoResponse {
	return ProtoResponse{
		msg: msg,
	}
}

func (p ProtoResponse) ToAuditLogDict() map[string]interface{} {
	if p.msg == nil {
		return nil
	}
	r, _ := redact.ToRedactJSON(p.msg)
	return map[string]interface{}{
		"response": r,
	}
}

// A request is accessed, e.g. by support.
type Request struct {
	request_id string
}

func NewRequest(request_id string) Request {
	return Request{
		request_id: request_id,
	}
}

func (r Request) ToAuditLogDict() map[string]interface{} {
	return map[string]interface{}{
		"resource": map[string]string{
			"@type": "type.eng.augmentcode.com/Request",
			"name":  "request_id",
			"value": r.request_id,
		},
	}
}

// A blob is accessed, e.g. by support.
// A blob is a piece of content, e.g. a document or image
// A blob content is restricted information and any access should be audited
//
// Example usage
//
//	auditLogger.WriteAuditLog(
//		authClaims,
//		"Support user accessed blob",
//		NewBlob(blob_name),
//	)
type Blob struct {
	blob_name string
}

func NewBlob(blob_name string) Blob {
	return Blob{
		blob_name: blob_name,
	}
}

func (b Blob) ToAuditLogDict() map[string]interface{} {
	return map[string]interface{}{
		"resource": map[string]string{
			"@type": "type.eng.augmentcode.com/Blob",
			"name":  "blob_name",
			"value": b.blob_name,
		},
	}
}

// the state of a user is accessed or mutated
// Any state change should be audit logged
// Any access should be audit logged if done via by a support user
type User struct {
	// opaque user id
	UserId string
}

func NewUser(userId string) User {
	return User{
		UserId: userId,
	}
}

func (u User) ToAuditLogDict() map[string]interface{} {
	return map[string]interface{}{
		"resource": map[string]string{
			"@type": "type.eng.augmentcode.com/User",
			"name":  "user_id",
			"value": u.UserId,
		},
	}
}
