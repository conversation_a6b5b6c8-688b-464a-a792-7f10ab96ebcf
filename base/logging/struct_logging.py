"""Module to setup structured logging using structlog.

The logging is based on the logfmt format and can be correctly parsed by the K8S promtail infrastructure (see `//tools/monitoring`).
It should be the default logging for Python containers running in Kubernetes.
"""

import logging
import os

import structlog

from third_party import structlog_gcp


class PasthroughRenderer:
    """A renderer that just passes through the message."""

    def __call__(self, _, __, event_dict):
        return event_dict


class TraitletsMessageFilter(logging.Filter):
    def filter(self, record: logging.LogRecord):
        # Filter out the specific message about invalid notebook JSON
        if (
            "Notebook JSON is invalid" in record.getMessage()
            and "site-packages/nbformat" in record.pathname
        ):
            record.msg = "SUPPRESSED: Notebook JSON is invalid"
            record.args = ()
        return True


# au-8782 - prevent nbformat from logging sensitive information.
traitlets_logger = logging.getLogger("traitlets")
traitlets_logger.addFilter(TraitletsMessageFilter())


def setup_struct_logging(debug: bool = False):
    """Setup the structured logging in a way to also capture standard logging."""

    if not debug:
        debug = "LOG_DEBUG" in os.environ

    processors = [
        structlog.contextvars.merge_contextvars
    ] + structlog_gcp.build_processors()
    wrapper = None if debug else structlog.make_filtering_bound_logger(logging.INFO)

    structlog.configure(
        processors=processors
        + [
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=wrapper,
        cache_logger_on_first_use=True,
    )

    formatter = structlog.stdlib.ProcessorFormatter(
        foreign_pre_chain=processors,
        processors=[
            structlog.stdlib.ProcessorFormatter.remove_processors_meta,
            structlog.processors.JSONRenderer(),
        ],
    )

    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    root_logger = logging.getLogger()
    root_logger.addHandler(handler)
    if debug:
        root_logger.setLevel(logging.DEBUG)
    else:
        root_logger.setLevel(logging.INFO)
