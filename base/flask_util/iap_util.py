"""IAP JWT verification utils for Flask apps."""

import json
import logging
from functools import wraps

from flask import Response, current_app, request, Request


def extract_user(req: Request) -> str | None:
    """Returns the current user.

    Only trust the output if the endpoint is annotated with @iap_jwt_verified.
    """

    gcp_email = req.headers.get("X-Goog-Authenticated-User-Email")
    if (
        gcp_email
        and gcp_email.endswith("@augmentcode.com")
        and gcp_email.startswith("accounts.google.com:")
    ):
        return gcp_email.rpartition("@augmentcode.com")[0][
            len("accounts.google.com:") :
        ]
    # not authenticated
    return None


def iap_jwt_verified(original_function):
    """Decorator that checks if the IAP JWT is valid.

    Assumes that the current app has an iap_verifier attribute.
    """

    @wraps(original_function)
    def wrapper(*args, **kwargs):
        if (
            "IAP_JWT_VERIFIER_DISABLED" in current_app.config
            and current_app.config["IAP_JWT_VERIFIER_DISABLED"]
        ):
            logging.info("IAP JWT verification disabled")
            return original_function(*args, **kwargs)

        assertion = request.headers.get("X-Goog-IAP-JWT-Assertion")
        if not assertion:
            logging.info("No JWT assertion found - sending 401")
            return Response(
                json.dumps({"code": 401}),  # type: ignore
                status=401,
                mimetype="application/json",
            )
        iap_verifier = current_app.iap_verifier  # type: ignore
        if not iap_verifier.verify(assertion):
            logging.info("JWT assertion verify failed - check public keys and audience")
            return Response(
                json.dumps({"code": 401}),  # type: ignore
                status=401,
                mimetype="application/json",
            )
        return original_function(*args, **kwargs)

    return wrapper
