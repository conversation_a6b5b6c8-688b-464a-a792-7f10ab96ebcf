"""gRPC utils for Flask apps."""

import json
import logging
from functools import wraps

import grpc
from flask import Response


def _grpc_code_to_http_error(code: grpc.StatusCode) -> int:
    """Converts a gRPC status code to an HTTP error code."""
    return {
        grpc.StatusCode.INVALID_ARGUMENT: 400,
        grpc.StatusCode.UNAUTHENTICATED: 401,
        grpc.StatusCode.UNAVAILABLE: 503,
        grpc.StatusCode.DEADLINE_EXCEEDED: 504,
        grpc.StatusCode.UNKNOWN: 500,
        grpc.StatusCode.RESOURCE_EXHAUSTED: 413,
        grpc.StatusCode.CANCELLED: 499,
        grpc.StatusCode.FAILED_PRECONDITION: 400,
        grpc.StatusCode.ABORTED: 400,
        grpc.StatusCode.OUT_OF_RANGE: 400,
        grpc.StatusCode.UNIMPLEMENTED: 501,
        grpc.StatusCode.INTERNAL: 500,
        grpc.StatusCode.OK: 200,
        grpc.StatusCode.NOT_FOUND: 404,
        grpc.StatusCode.PERMISSION_DENIED: 403,
    }.get(code, 500)


def grpc_error_wrap(original_function):
    """Decorator that wraps gRPC errors."""

    @wraps(original_function)
    def wrapper(*args, **kwargs):
        try:
            return original_function(*args, **kwargs)
        except grpc.RpcError as rpc_error:
            http_error_code = _grpc_code_to_http_error(rpc_error.code())  # type: ignore
            return Response(
                json.dumps({"code": http_error_code, "message": rpc_error.details()}),  # type: ignore
                status=http_error_code,
                mimetype="application/json",
            )
        except Exception as e:
            logging.exception("Unexpected error: %s", e)
            return Response(
                json.dumps({"code": 500}),  # type: ignore
                status=500,
                mimetype="application/json",
            )

    return wrapper
