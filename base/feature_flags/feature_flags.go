package featureflags

// Package featureflags provides a client library for feature flags
//
// A feature flag is a boolean, integer, or string value that can be changed
// during the execution of a program.
//
// Usage:
// h, err := featureflags.NewFeatureFlagHandleFromFile("path/to/sdk_key")
//
// flag, err := h.BindContext("key", "value")
//
// myFlag = StringFlag("my_flag", "default_value")
// f, err := myFlag.Get(flag)

import (
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/launchdarkly/go-sdk-common/v3/ldcontext"
	"github.com/launchdarkly/go-sdk-common/v3/ldlog"
	ld "github.com/launchdarkly/go-server-sdk/v7"
	"github.com/launchdarkly/go-server-sdk/v7/interfaces"
	"github.com/launchdarkly/go-server-sdk/v7/ldcomponents"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// FeatureFlagHandle is the interface for getting feature flags
type FeatureFlagHandle interface {
	// Get the value of a string feature flag
	GetString(name string, defaultValue string) (string, error)

	// Get the value of a boolean feature flag
	GetBool(name string, defaultValue bool) (bool, error)

	// Get the value of an integer feature flag
	GetInt(name string, defaultValue int) (int, error)

	// Get the value of a float feature flag
	GetFloat(name string, defaultValue float64) (float64, error)

	// Bind a context to the feature flag
	BindContext(name string, value string) (FeatureFlagHandle, error)
}

// A new string feature flag
type StringFlag struct {
	name         string
	defaultValue string
}

func NewStringFlag(name string, defaultValue string) *StringFlag {
	return &StringFlag{
		name:         name,
		defaultValue: defaultValue,
	}
}

// Get the value of the string feature flag
func (f *StringFlag) Get(handle FeatureFlagHandle) (string, error) {
	return handle.GetString(f.name, f.defaultValue)
}

// A new boolean feature flag
type BoolFlag struct {
	name         string
	defaultValue bool
}

// LaunchDarkly's logger redirect interface requires loggers that implement the
// stdlib log interface (Println and Printf). Unfortunately just passing
// the zerolog Logger directly isn't great as zerolog defaults all Println
// and Printf calls to be at `DEBUG` log level, so even if zerolog is raising
// errors we won't see them.
//
// Instead we implement a bunch of proxy loggers that just call the appropriate
// zerolog level, as LaunchDarkly allows setting the logger for every launchdarkly
// log level.
type DebugLogger struct {
	logger *zerolog.Logger
}

func (l *DebugLogger) Println(args ...interface{}) {
	l.logger.Debug().Msg(fmt.Sprint(args...))
}

func (l *DebugLogger) Printf(format string, v ...interface{}) {
	l.logger.Debug().Msgf(format, v...)
}

type InfoLogger struct {
	logger *zerolog.Logger
}

func (l *InfoLogger) Println(args ...interface{}) {
	l.logger.Info().Msg(fmt.Sprint(args...))
}

func (l *InfoLogger) Printf(format string, v ...interface{}) {
	l.logger.Info().Msgf(format, v...)
}

type WarnLogger struct {
	logger *zerolog.Logger
}

func (l *WarnLogger) Println(args ...interface{}) {
	l.logger.Warn().Msg(fmt.Sprint(args...))
}

func (l *WarnLogger) Printf(format string, v ...interface{}) {
	l.logger.Warn().Msgf(format, v...)
}

type ErrorLogger struct {
	logger *zerolog.Logger
}

func (l *ErrorLogger) Println(args ...interface{}) {
	l.logger.Error().Msg(fmt.Sprint(args...))
}

func (l *ErrorLogger) Printf(format string, v ...interface{}) {
	l.logger.Error().Msgf(format, v...)
}

// Setup launch darkly to log through Zerolog. Zerolog's global logger
// must have been initialized and configured before calling this function.
func setupLaunchDarklyLoggingConfig(config *ld.Config) {
	// See https://launchdarkly.com/docs/sdk/features/logging#expand-go-code-sample
	loggers := ldlog.NewDefaultLoggers()

	// By default if we miss a log level we should escalate it.
	loggers.SetBaseLogger(&ErrorLogger{logger: &log.Logger})
	loggers.SetBaseLoggerForLevel(ldlog.Debug, &DebugLogger{logger: &log.Logger})
	loggers.SetBaseLoggerForLevel(ldlog.Info, &InfoLogger{logger: &log.Logger})
	loggers.SetBaseLoggerForLevel(ldlog.Warn, &WarnLogger{logger: &log.Logger})
	loggers.SetBaseLoggerForLevel(ldlog.Error, &ErrorLogger{logger: &log.Logger})

	config.Logging = ldcomponents.Logging().
		Loggers(loggers).
		MinLevel(ldlog.Info)
}

func NewBoolFlag(name string, defaultValue bool) *BoolFlag {
	return &BoolFlag{
		name:         name,
		defaultValue: defaultValue,
	}
}

// Get the value of the boolean feature flag
func (f *BoolFlag) Get(handle FeatureFlagHandle) (bool, error) {
	return handle.GetBool(f.name, f.defaultValue)
}

// A new integer feature flag
type IntFlag struct {
	name         string
	defaultValue int
}

func NewIntFlag(name string, defaultValue int) *IntFlag {
	return &IntFlag{
		name:         name,
		defaultValue: defaultValue,
	}
}

// Get the value of the integer feature flag
func (f *IntFlag) Get(handle FeatureFlagHandle) (int, error) {
	return handle.GetInt(f.name, f.defaultValue)
}

// A new float feature flag
type FloatFlag struct {
	name         string
	defaultValue float64
}

func NewFloatFlag(name string, defaultValue float64) *FloatFlag {
	return &FloatFlag{
		name:         name,
		defaultValue: defaultValue,
	}
}

// Get the value of the float feature flag
func (f *FloatFlag) Get(handle FeatureFlagHandle) (float64, error) {
	return handle.GetFloat(f.name, f.defaultValue)
}

// FeatureFlagHandleImpl is the implementation of FeatureFlagHandle
// using the launch darkly client
type FeatureFlagHandleImpl struct {
	client  *ld.LDClient
	context ldcontext.Context
}

func NewFeatureFlagHandleFromFile(path string, customEndpoint string) (FeatureFlagHandle, error) {
	sdkKey, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	return NewFeatureFlagHandle(string(sdkKey), customEndpoint)
}

// NewFeatureFlagHandle creates a new FeatureFlagHandleImpl
func NewFeatureFlagHandle(sdkKey string, customEndpoint string) (FeatureFlagHandle, error) {
	var client *ld.LDClient
	var err error
	var config ld.Config

	// Make LaunchDarkly log through structured Zerolog. Zerolog should be configured before
	// this!
	setupLaunchDarklyLoggingConfig(&config)

	if customEndpoint != "" {
		config.ServiceEndpoints = interfaces.ServiceEndpoints{
			Streaming: customEndpoint,
			Polling:   customEndpoint,
			Events:    customEndpoint,
		}
		client, err = ld.MakeCustomClient(sdkKey, config, time.Second*60)
	} else {
		client, err = ld.MakeCustomClient(sdkKey, config, time.Second*60)
	}
	if err != nil {
		return nil, err
	}

	podName := os.Getenv("POD_NAME")
	podNamespace := os.Getenv("POD_NAMESPACE")
	podCloud, ok := os.LookupEnv("POD_CLOUD")
	if !ok {
		log.Warn().Msg("Missing POD_CLOUD environment variable")
		podCloud = ""
	}
	podEnv, ok := os.LookupEnv("POD_ENV")
	if !ok {
		log.Warn().Msg("Missing POD_ENV environment variable")
		podEnv = ""
	}

	builder := ldcontext.NewBuilder(podNamespace)
	builder.Kind("namespace")
	builder.SetString("pod_name", podName)
	builder.SetString("cloud", podCloud)
	builder.SetString("env", podEnv)
	context := builder.Build()
	return FeatureFlagHandleImpl{
		client:  client,
		context: context,
	}, nil
}

func (f FeatureFlagHandleImpl) GetString(name string, defaultValue string) (string, error) {
	return f.client.StringVariation(name, f.context, defaultValue)
}

func (f FeatureFlagHandleImpl) GetBool(name string, defaultValue bool) (bool, error) {
	return f.client.BoolVariation(name, f.context, defaultValue)
}

func (f FeatureFlagHandleImpl) GetInt(name string, defaultValue int) (int, error) {
	return f.client.IntVariation(name, f.context, defaultValue)
}

func (f FeatureFlagHandleImpl) GetFloat(name string, defaultValue float64) (float64, error) {
	return f.client.Float64Variation(name, f.context, defaultValue)
}

func (f FeatureFlagHandleImpl) BindContext(name string, value string) (FeatureFlagHandle, error) {
	builder := ldcontext.NewBuilderFromContext(f.context)
	builder.SetString(name, value)
	context := builder.Build()
	return FeatureFlagHandleImpl{
		client:  f.client,
		context: context,
	}, nil
}

// LocalFeatureFlagHandler is the implementation of FeatureFlagHandle
// using a local map
type LocalFeatureFlagHandler struct {
	mutex sync.Mutex
	flags map[string]interface{}
}

func NewLocalFeatureFlagHandler() *LocalFeatureFlagHandler {
	return &LocalFeatureFlagHandler{
		mutex: sync.Mutex{},
		flags: make(map[string]interface{}),
	}
}

func (f *LocalFeatureFlagHandler) Set(name string, value interface{}) {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	f.flags[name] = value
}

func (f *LocalFeatureFlagHandler) GetString(name string, defaultValue string) (string, error) {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	v, ok := f.flags[name].(string)
	if !ok {
		return defaultValue, nil
	}
	return v, nil
}

func (f *LocalFeatureFlagHandler) GetBool(name string, defaultValue bool) (bool, error) {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	v, ok := f.flags[name].(bool)
	if !ok {
		return defaultValue, nil
	}
	return v, nil
}

func (f *LocalFeatureFlagHandler) GetInt(name string, defaultValue int) (int, error) {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	v, ok := f.flags[name].(int)
	if !ok {
		return defaultValue, nil
	}
	return v, nil
}

func (f *LocalFeatureFlagHandler) GetFloat(name string, defaultValue float64) (float64, error) {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	v, ok := f.flags[name].(float64)
	if !ok {
		return defaultValue, nil
	}
	return v, nil
}

func (f *LocalFeatureFlagHandler) BindContext(name string, value string) (FeatureFlagHandle, error) {
	return f, nil
}
