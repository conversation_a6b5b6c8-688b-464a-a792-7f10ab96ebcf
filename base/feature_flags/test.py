"""Tests for base.feature_flags."""

import pytest

from base.feature_flags import Bool<PERSON>lag, feature_flag_fixture, Context


@pytest.fixture
def feature_flags():
    yield from feature_flag_fixture()


def test_feature_flags_bool_get(feature_flags):
    """Sanity test for base.feature_flags.BoolFlag."""
    flag = BoolFlag("print_hello_world", False)
    context = Context._default()
    assert flag.get(context) is False


def test_feature_flags(feature_flags):
    """Sanity test for base.feature_flags.BoolFlag."""
    flag = <PERSON>olFlag("print_hello_world", False)
    feature_flags.set_flag(flag, True)
    context = Context._default()
    assert flag.get(context) is True
