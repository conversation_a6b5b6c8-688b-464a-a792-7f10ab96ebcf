"""Tests feature_flags environment variable that implicitly initializes the global context for unit tests."""

import pytest

from base import feature_flags

# The test modifies the environment variable and the global context.
# Please don't extend this file to keep the mess at a minimum.


def test_feature_flags_implicit_defaults_for_tests():
    """Test that the environment variable is not set and the global context is not initialized."""
    feature_flags._ALLOW_UNINITIALIZED_LAUNCHDARKLY_IN_TESTS = False
    with pytest.raises(Exception):
        feature_flags.BoolFlag("test_flag", False).get(
            feature_flags.get_global_context()
        )

    feature_flags._ALLOW_UNINITIALIZED_LAUNCHDARKLY_IN_TESTS = True
    feature_flags.BoolFlag("test_flag", False).get(
        feature_flags.get_global_context()
    )  # should not raise
