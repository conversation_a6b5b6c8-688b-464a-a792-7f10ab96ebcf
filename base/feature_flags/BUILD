load("@crates//:defs.bzl", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

rust_library(
    name = "feature_flags_rs",
    srcs = glob(["src/**/*.rs"]),
    crate_name = "feature_flags",
    edition = "2021",
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ),
)

rust_test(
    name = "feature_flags_rs_test",
    srcs = glob(["**/*.rs"]),
    deps = all_crate_deps(
        normal = True,
    ),
)

py_library(
    name = "feature_flags_py",
    srcs = ["__init__.py"],
    visibility = BASE_VISIBILITY + ["//base/tokenizers:__subpackages__"],
    deps = [
        requirement("launchdarkly-server-sdk"),
    ],
)

pytest_test(
    name = "feature_flags_python_test",
    srcs = ["test.py"],
    deps = [
        ":feature_flags_py",
    ],
)

go_library(
    name = "feature_flags_go",
    srcs = ["feature_flags.go"],
    importpath = "github.com/augmentcode/augment/base/feature_flags",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_launchdarkly_go_sdk_common_v3//ldcontext",
        "@com_github_launchdarkly_go_sdk_common_v3//ldlog",
        "@com_github_launchdarkly_go_server_sdk_v7//:go-server-sdk",
        "@com_github_launchdarkly_go_server_sdk_v7//interfaces",
        "@com_github_launchdarkly_go_server_sdk_v7//ldcomponents",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
    ],
)

pytest_test(
    name = "feature_flags_envvar_test",
    srcs = ["feature_flags_envvar_test.py"],
    deps = [
        ":feature_flags_py",
    ],
)
