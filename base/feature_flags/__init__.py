"""Feature flags library.

This library provides classes for boolean, integer, and string feature flags.

The library integrates with the LaunchDarkly SDK to provide dynamic updates while
a process is running.

Example usage:

    from base import feature_flags

    _USE_V2_ALGORITHM = feature_flags.BoolFlag("use_v2_algorithm", False)

    def my_function():
        context = feature_flags.get_global_context()
        if _USE_V2_ALGORITHM.get(context):
            ...
        else:
            ...

    # Somewhere early in the `main` of the program
    base.feature_flags.Context.setup(pathlib.Path("path/to/sdk_key"))

The library also allows feature flag values to be set locally for unit tests.

Example usage for unit tests:

    from base import feature_flags

    @pytest.fixture()
    def feature_flags_context() -> Generator[feature_flags.LocalFeatureFlagSetter, None, None]:
        yield from feature_flags.feature_flag_fixture()

    @pytest.mark.parametrize("v2_algorithm", [True, False])
    def test_my_function(feature_flags_context, v2_algorithm):
        feature_flags_context.set_flag(_USE_V2_ALGORITHM, v2_algorithm)
        ...
"""

import logging
import os
import pathlib
import sys
import typing

import ldclient
import ldclient.integrations.test_data
import ldclient.interfaces

_DID_LOG_ABOUT_IMPLICT_DEFAULTS = False
_ALLOW_UNINITIALIZED_LAUNCHDARKLY_IN_TESTS = True


class Context:
    """A LaunchDarkly context."""

    def __init__(
        self, context: ldclient.Context, name: str, attributes: list[tuple[str, str]]
    ) -> None:
        self._context = context
        self._name = name
        self._attributes = attributes

    @classmethod
    def _default(cls):
        """Create a default context.

        This should only be used by this module, not by real users or unit
        tests.
        """
        context = ldclient.Context.builder("default").build()
        return cls(context, "default", [])

    @classmethod
    def setup(
        cls, sdk_key_path: pathlib.Path | None, custom_endpoint: str | None = None
    ):
        """Setup the feature flags library.

        This will launch background threads. Background threads do not
        survive multiprocessing or forking. Also, forking with background threads
        can cause stability issues. So:
            - don't call setup until after you've forked everything
            - use multiprocessing forkserver if possible and have it
            start the forkserver before you call setup

        Background: forking a threaded process is problematic, even in C programs, because
        background threads may hold locks that are never released in the child.
        These locks need not be visible in the program - they may exist inside
        of libc.

        Args:
            sdk_key_path: Path to file containing LaunchDarkly SDK key.
            custom_endpoint: Custom endpoint to use for LaunchDarkly.

        Returns:
            A context.

        Raises:
            RuntimeError: If the LaunchDarkly client did not initialize.
            RuntimeError: If the POD_NAME environment variable is not set.
            RuntimeError: If the POD_NAMESPACE environment variable is not set.
        """
        if sdk_key_path is None:
            ldclient.set_config(ldclient.Config(sdk_key="offline", offline=True))
            inst = cls(ldclient.Context.builder("offline").build(), "offline", [])
            return inst
        if custom_endpoint is not None:
            ldclient.set_config(
                ldclient.Config(
                    sdk_key=sdk_key_path.read_text().strip(),
                    base_uri=custom_endpoint,
                    events_uri=custom_endpoint,
                    stream_uri=custom_endpoint,
                )
            )
        else:
            ldclient.set_config(
                ldclient.Config(sdk_key=sdk_key_path.read_text().strip())
            )
        if not ldclient.get().is_initialized():
            raise RuntimeError("LaunchDarkly client did not initialize")

        if "POD_NAME" not in os.environ:
            raise RuntimeError("POD_NAME environment variable not set")

        if "POD_NAMESPACE" not in os.environ:
            raise RuntimeError("POD_NAMESPACE environment variable not set")

        pod_cloud = os.environ.get("POD_CLOUD", "")
        if not pod_cloud:
            logging.warning("Missing POD_CLOUD environment variable")
        pod_env = os.environ.get("POD_ENV", "")
        if not pod_env:
            logging.warning("Missing POD_ENV environment variable")

        context = (
            ldclient.Context.builder(os.environ["POD_NAMESPACE"])
            .kind("namespace")
            .set("pod_name", os.environ["POD_NAME"])
            .set("cloud", pod_cloud)
            .set("env", pod_env)
            .build()
        )

        inst = cls(
            context,
            os.environ["POD_NAMESPACE"],
            [
                ("pod_name", os.environ["POD_NAME"]),
                ("cloud", pod_cloud),
                ("env", pod_env),
            ],
        )

        logging.info(
            "Dynamic feature flags initialized %s",
            _CONNECTIVITY_TEST_FLAG.get(inst),
        )
        return inst

    def lookup(self, name: str, default: typing.Any):
        """Lookup a feature flag.

        Args:
            name: Name of the feature flag.
            default: Default value to return if the flag is not found.

        Returns:
            The value of the flag.
        """
        global _DID_LOG_ABOUT_IMPLICT_DEFAULTS
        requires_initialization = None
        try:
            return ldclient.get().variation(name, self._context, default=default)
        except Exception as e:  # pylint: disable=broad-except
            # Skip adding the note if we're in a test environment
            requires_initialization = os.environ.get(
                "DYNAMIC_FEATURE_FLAGS_REQUIRE_INITIALIZATION", "false"
            )
            if (
                requires_initialization == "true"
                or not _ALLOW_UNINITIALIZED_LAUNCHDARKLY_IN_TESTS
            ):
                e.add_note(
                    "Dynamic feature flags not initialized. This is okay for tests and "
                    "for research code. "
                    "If this is a production service, then you need to call "
                    "`base.feature_flags.Context.setup(pathlib.Path('path/to/sdk_key'))` "
                    "before using feature flags."
                )
                raise
        # For unit tests, initialize defaults and retry.
        # This avoids having to add the unit_test_setup() call to any test file
        # that depends on a library using feature flags.
        if not _DID_LOG_ABOUT_IMPLICT_DEFAULTS:
            _DID_LOG_ABOUT_IMPLICT_DEFAULTS = True
            logging.info(
                "Feature flags not initialized. Okay for tests and research code. Using "
                "default values. If this message is logged for a production service, this "
                "is a bug as it risks misconfiguration. requires_initialization=%s",
                requires_initialization,
            )
        return default

    def bind_attribute(
        self,
        extra_attribute_name: str,
        extra_attribute_value: str,
    ):
        """Bind an attribute to the context.

        Args:
            context_name: Name of the context.
            extra_attribute_name: Name of the attribute.
            extra_attribute_value: Value of the attribute.

        Returns:
            A new context with the attribute bound.
        """
        builder = ldclient.Context.builder(self._name)
        builder.kind("namespace")
        attributes = self._attributes + [(extra_attribute_name, extra_attribute_value)]
        for name, value in attributes:
            builder.set(name, value)
        context = builder.build()

        return Context(context, self._name, attributes)


class BoolFlag:
    """A boolean feature flag."""

    def __init__(self, name: str, default: bool):
        self.name = name
        self.default = default

    def get(self, context: Context) -> bool:
        """Get the value of the flag."""
        val = context.lookup(self.name, self.default)
        if isinstance(val, bool):
            return val
        raise RuntimeError(f"Feature flag {self.name} is not a boolean")

    def set_default(self, default: bool) -> None:
        """Set the default value of the flag.

        Do not use this in unit tests. Instead use feature_flags fixture.
        """
        self.default = default


class StringFlag:
    """A string feature flag."""

    def __init__(self, name: str, default: str):
        self.name = name
        self.default = default

    def get(self, context: Context) -> str:
        """Get the value of the flag."""
        val = context.lookup(self.name, self.default)
        if isinstance(val, str):
            return val
        raise RuntimeError(f"Feature flag {self.name} is not a string")

    def set_default(self, default: str) -> None:
        """Set the default value of the flag.

        Do not use this in unit tests. Instead use feature_flags fixture.
        """
        self.default = default


class FloatFlag:
    """A float feature flag."""

    def __init__(self, name: str, default: float):
        self.name = name
        self.default = default

    def get(self, context: Context) -> float:
        """Get the value of the flag."""
        val = context.lookup(self.name, self.default)
        if isinstance(val, float) or isinstance(val, int):
            return val
        raise RuntimeError(f"Feature flag {self.name} is not a float")

    def set_default(self, default: float) -> None:
        """Set the default value of the flag.

        Do not use this in unit tests. Instead use feature_flags fixture.
        """
        self.default = default


class IntFlag:
    """An integer feature flag."""

    def __init__(self, name: str, default: int):
        self.name = name
        self.default = default

    def get(self, context: Context) -> int:
        """Get the value of the flag."""
        val = context.lookup(self.name, self.default)
        if isinstance(val, int):
            return val
        raise RuntimeError(f"Feature flag {self.name} is not an integer")

    def set_default(self, default: int) -> None:
        """Set the default value of the flag.

        Do not use this in unit tests. Instead use feature_flags fixture.
        """
        self.default = default


_CONNECTIVITY_TEST_FLAG = BoolFlag("connectivity_test_flag", False)


_GLOBAL_CONTEXT = Context._default()


def get_global_context() -> Context:
    """Get the global context."""
    return _GLOBAL_CONTEXT


def set_global_context(context: Context) -> None:
    """Set the global context.

    Do not use this in unit tests. Instead use feature_flags fixture.
    """
    global _GLOBAL_CONTEXT  # pylint: disable=global-statement
    _GLOBAL_CONTEXT = context


class LocalFeatureFlagSetter:
    """A class for setting the value of feature flags locally."""

    def __init__(self):
        self.test_data_source = ldclient.integrations.test_data.TestData.data_source()

    def set_flag(
        self,
        flag: typing.Union[BoolFlag, StringFlag, IntFlag, FloatFlag],
        flag_value: typing.Union[bool, str, int, float],
    ) -> None:
        """Set the value of a feature flag.

        Only works with test data source. Meant for unit tests.

        Args:
            flag: The flag to set.
            flag_value: The value to set the flag to.
        """
        assert isinstance(flag_value, type(flag.default))

        self.test_data_source.update(
            ldclient.integrations.test_data.FlagBuilder(flag.name)
            .on(True)
            .value_for_all(flag_value)
        )


def unit_test_setup() -> LocalFeatureFlagSetter:
    """Enable using the default feature flags.

    This is only for unit tests. And any unit test that modifies feature flags
    should use the fixtures below.

    Calling this at the beginning of the file will avoid exceptions of the form:
        `Exception: set_config was not called`
    """
    ret = LocalFeatureFlagSetter()
    ldclient.set_config(
        ldclient.Config(
            sdk_key="Bogus",
            send_events=False,
            update_processor_class=ret.test_data_source,  # type: ignore
        )
    )
    return ret


def feature_flag_fixture() -> typing.Generator[LocalFeatureFlagSetter, None, None]:
    """Fixture for overriding feature flags in unit tests.

    Example usage:

    @pytest.fixture()
    def feature_flags_context() -> Generator[feature_flags.LocalFeatureFlagSetter, None, None]:
        yield from feature_flags.feature_flag_fixture()

    @pytest.mark.parametrize("v2_algorithm", [True, False])
    def test_my_function(feature_flags_context, v2_algorithm):
        feature_flags_context.set_flag(_USE_V2_ALGORITHM, v2_algorithm)
        ...

    Yields:
        Object with set_flag method for setting flag.
    """
    yield unit_test_setup()
    # Helps prevent a feature flag set in a first test from leaking to a second test,
    # esp. if the second test doesn't use feature_flag_fixture.
    unit_test_setup()
