//! Dynamic feature flag library
//!
//! A dynamic feature flag is one whose value can change during the execution of the program.
//! One use of dynamic feature flag is to turn on a new feature without restarting the program.
//!
//! This library support boolean, 64-bit integer, and string feature flags.
//!
//! Some libraries allow you to return different values for different users, e.g. to enable
//! different features for different users. To support this, the library allows the caller to provide
//! a context, which includes a series of key-value pairs. This library does not currently support
//! contexts specified at feature flag evaluation time, though there is some limited support for
//! providing context when the library is initialized.
//!
//! This library integrates with the LaunchDarkly SDK to provide their set of features when
//! evaluating feature flags. In addition, this library also allows
//! feature flag values to be set locally. Locally set feature flags can be useful in testing
//! and cases of connectivity loss with the feature flag server.
//!
//! = Example
//!
//! Here is a feature flag named "enable_v2" with the default value of false.
//!
//! ```
//! pub const V2_ENABLE: feature_flags::BoolFlag = feature_flags::BoolFlag::new("enable_v2", false);
//! ```
//!
//! Here is how to create a feature flag service and use it to evaluate the feature flag:
//!
//! ```
//! let flag_service = feature_flags::setup("my_app", "0.1.0", sdk_key_path, None);
//!
//! if V2_ENABLE.get_from(&flags_service) {
//!     ... do V2 stuff
//! } else {
//!     ... do V1 stuff
//! }
//! ```
//!
//! Here is how to write a test that tests a specific value for the feature flag:
//!
//! ```
//! mod tests {
//!
//!      pub fn test_v2_on()
//!      {
//!         let flag_service = feature_flags::setup_local();
//!         V2_ENABLE.set_local(&flag_service, true);
//!         // Trigger code
//!      }
//!
//!      pub fn test_v2_off()
//!      {
//!         let flag_service = feature_flags::setup_local();
//!         V2_ENABLE.set_local(&flag_service, false);
//!         // Trigger code
//!      }
//!
//! }
//! ```
//!
//! For out-of-process tests, the feature flag library can parse JSON configuration to set
//! feature flag default values (see `populate_from_hashmap`). The `populate_from_hashmap`
//! function takes a registry of feature flags, so that it can do some sanity and type checks.
//! Here is how to add a flag to a registry:
//!
//! ```
//!     let registry = feature_flags::new_registry();
//!     SAMPLE_FLAG.register(&registry).unwrap();
//! ```
//!
//! The `populate_from_hashmap` function also takes a `HashMap<String, serde_json::Value>`.
//!
//! = Design notes
//!
//! Why is the feature_flag service not a global like tracing, logging, etc.?
//!
//! Because unit tests run concurrently in Rust and different unit tests may need different
//! values. Also reduces the chance that variables overridden in one unit test will affect
//! a subsequent unit test.
//!
//! Why `get_from` and `set_local` on the flags themselves rather use methods on the service?
//!
//! Rust doesn't have function overloading so that would require get_bool/get_string/get_int
//! at the call site. This saves having to specify the type at the call site.
//!
//! Why a separate registry?
//!
//! I was following Prometheus which has a metric registry. But it's less compelling here.
//! In theory, the same registry could be used with multiple feature flag services.
//!
//! Why return an error if registering the same flag twice?
//!
//! It was easy to code. We might want to only disallow registering a flag twice if the type
//! or default value are different.

use launchdarkly_server_sdk::ServiceEndpointsBuilder;
use serde_json::Value;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::Duration;

pub struct BoolFlag {
    pub name: &'static str,
    pub default: bool,
}

impl BoolFlag {
    pub const fn new(name: &'static str, default: bool) -> Self {
        Self { name, default }
    }

    pub fn get_from(&self, service: &FeatureFlagsServiceHandle) -> bool {
        service.lookup_bool(self)
    }

    pub fn set_local(&self, service: &FeatureFlagsServiceHandle, value: bool) {
        service.set_local_bool(self.name, value)
    }

    pub fn register(&self, registry: &RegistryHandle) -> Result<(), Box<dyn std::error::Error>> {
        registry.register_bool(self)
    }
}

pub struct StringFlag {
    pub name: &'static str,
    pub default: &'static str,
}

impl StringFlag {
    pub const fn new(name: &'static str, default: &'static str) -> Self {
        Self { name, default }
    }

    pub fn get_from(&self, service: &FeatureFlagsServiceHandle) -> String {
        service.lookup_string(self)
    }

    pub fn set_local(&self, service: &FeatureFlagsServiceHandle, value: &str) {
        service.set_local_string(self.name, value)
    }

    pub fn register(&self, registry: &RegistryHandle) -> Result<(), Box<dyn std::error::Error>> {
        registry.register_string(self)
    }
}

pub struct IntFlag {
    pub name: &'static str,
    pub default: i64,
}

impl IntFlag {
    pub const fn new(name: &'static str, default: i64) -> Self {
        Self { name, default }
    }

    pub fn get_from(&self, service: &FeatureFlagsServiceHandle) -> i64 {
        service.lookup_i64(self)
    }

    pub fn set_local(&self, service: &FeatureFlagsServiceHandle, value: i64) {
        service.set_local_i64(self.name, value)
    }

    pub fn register(&self, registry: &RegistryHandle) -> Result<(), Box<dyn std::error::Error>> {
        registry.register_int(self)
    }
}

pub struct FloatFlag {
    pub name: &'static str,
    pub default: f64,
}

impl FloatFlag {
    pub const fn new(name: &'static str, default: f64) -> Self {
        Self { name, default }
    }

    pub fn get_from(&self, service: &FeatureFlagsServiceHandle) -> f64 {
        service.lookup_f64(self)
    }

    pub fn set_local(&self, service: &FeatureFlagsServiceHandle, value: f64) {
        service.set_local_f64(self.name, value)
    }

    pub fn register(&self, registry: &RegistryHandle) -> Result<(), Box<dyn std::error::Error>> {
        registry.register_float(self)
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum FeatureFlagTypes {
    Bool,
    String,
    Int,
    Float,
}

#[derive(Debug, Clone, PartialEq)]
enum FeatureFlagContents {
    Bool(bool),
    String(Box<str>),
    Int(i64),
    Float(f64),
}

// A feature flag service
//
// Lookup the value of the flag for a given context. Returns the default
// value if another value can't be found (e.g. no contact with the feature flag server).
// That is, there is no concept of a value that is not present.
pub trait FeatureFlagsService {
    fn lookup_bool(&self, flag: &BoolFlag) -> bool;
    fn lookup_i64(&self, flag: &IntFlag) -> i64;
    fn lookup_string(&self, flag: &StringFlag) -> String;
    fn lookup_f64(&self, flag: &FloatFlag) -> f64;

    fn set_local_bool(&self, name: &str, value: bool);
    fn set_local_string(&self, name: &str, value: &str);
    fn set_local_i64(&self, name: &str, value: i64);
    fn set_local_f64(&self, name: &str, value: f64);

    /// add an attribute to the context
    fn bind_attribute(
        &self,
        extra_attribute_name: &str,
        extra_attribute_value: &str,
    ) -> Result<FeatureFlagsServiceHandle, Box<dyn std::error::Error>>;
}

// Maybe this should be its own struct but let's run with this for now
pub type FeatureFlagsServiceHandle = Arc<dyn FeatureFlagsService + Send + Sync>;

// A trait for looking up a feature flag
pub trait FeatureFlagProvider<T, TRef> {
    fn lookup(&self, name: &'static str, default_value: TRef) -> T;
}

// implementations of FeatureFlagProvider
impl FeatureFlagProvider<bool, bool> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: bool) -> bool {
        let flag = BoolFlag::new(name, default_value);
        self.lookup_bool(&flag)
    }
}

impl FeatureFlagProvider<String, &'static str> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: &'static str) -> String {
        let flag = StringFlag::new(name, default_value);
        self.lookup_string(&flag)
    }
}

impl FeatureFlagProvider<i64, i64> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: i64) -> i64 {
        let flag = IntFlag::new(name, default_value);
        self.lookup_i64(&flag)
    }
}

impl FeatureFlagProvider<f64, f64> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: f64) -> f64 {
        let flag = FloatFlag::new(name, default_value);
        self.lookup_f64(&flag)
    }
}

impl FeatureFlagProvider<Option<bool>, bool> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: bool) -> Option<bool> {
        let flag = BoolFlag::new(name, default_value);
        Some(self.lookup_bool(&flag))
    }
}

impl FeatureFlagProvider<Option<String>, &'static str> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: &'static str) -> Option<String> {
        let flag = StringFlag::new(name, default_value);
        Some(self.lookup_string(&flag))
    }
}

impl FeatureFlagProvider<Option<i64>, i64> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: i64) -> Option<i64> {
        let flag = IntFlag::new(name, default_value);
        Some(self.lookup_i64(&flag))
    }
}

impl FeatureFlagProvider<Option<f64>, f64> for FeatureFlagsServiceHandle {
    fn lookup(&self, name: &'static str, default_value: f64) -> Option<f64> {
        let flag = FloatFlag::new(name, default_value);
        Some(self.lookup_f64(&flag))
    }
}

struct LocalFeatureFlagsService {
    feature_flags: Mutex<std::collections::HashMap<Box<str>, FeatureFlagContents>>,
}

impl LocalFeatureFlagsService {
    fn new() -> Self {
        LocalFeatureFlagsService {
            feature_flags: Mutex::new(std::collections::HashMap::new()),
        }
    }
}

impl FeatureFlagsService for LocalFeatureFlagsService {
    fn bind_attribute(
        &self,
        _extra_attribute_name: &str,
        _extra_attribute_value: &str,
    ) -> Result<FeatureFlagsServiceHandle, Box<dyn std::error::Error>> {
        let map = self.feature_flags.lock().unwrap();
        Ok(Arc::new(LocalFeatureFlagsService {
            feature_flags: Mutex::new(map.clone()),
        }))
    }

    fn lookup_bool(&self, flag: &BoolFlag) -> bool {
        match self.feature_flags.lock().unwrap().get(flag.name) {
            Some(FeatureFlagContents::Bool(value)) => *value,
            Some(_) => {
                tracing::warn!("Feature flag {} is not a boolean", flag.name);
                flag.default
            }
            None => flag.default,
        }
    }

    fn lookup_string(&self, flag: &StringFlag) -> String {
        match self.feature_flags.lock().unwrap().get(flag.name) {
            Some(FeatureFlagContents::String(value)) => value.to_string(),
            Some(_) => {
                tracing::warn!("Feature flag {} is not a string", flag.name);
                flag.default.into()
            }
            None => flag.default.into(),
        }
    }

    fn lookup_i64(&self, flag: &IntFlag) -> i64 {
        match self.feature_flags.lock().unwrap().get(flag.name) {
            Some(FeatureFlagContents::Int(value)) => *value,
            Some(_) => {
                tracing::warn!("Feature flag {} is not an integer", flag.name);
                flag.default
            }
            None => flag.default,
        }
    }

    fn lookup_f64(&self, flag: &FloatFlag) -> f64 {
        match self.feature_flags.lock().unwrap().get(flag.name) {
            Some(FeatureFlagContents::Float(value)) => *value,
            Some(_) => {
                tracing::warn!("Feature flag {} is not a float", flag.name);
                flag.default
            }
            None => flag.default,
        }
    }

    fn set_local_bool(&self, name: &str, value: bool) {
        self.feature_flags
            .lock()
            .unwrap()
            .insert(name.into(), FeatureFlagContents::Bool(value));
    }

    fn set_local_string(&self, name: &str, value: &str) {
        self.feature_flags
            .lock()
            .unwrap()
            .insert(name.into(), FeatureFlagContents::String(value.into()));
    }

    fn set_local_i64(&self, name: &str, value: i64) {
        self.feature_flags
            .lock()
            .unwrap()
            .insert(name.into(), FeatureFlagContents::Int(value));
    }

    fn set_local_f64(&self, name: &str, value: f64) {
        self.feature_flags
            .lock()
            .unwrap()
            .insert(name.into(), FeatureFlagContents::Float(value));
    }
}

// Eventually this Launch Darkly implementation should live in a separate file.
struct LaunchDarklyFeatureFlagsService {
    client: Arc<launchdarkly_server_sdk::Client>,
    context: launchdarkly_server_sdk::Context,
    default_feature_flags: Arc<LocalFeatureFlagsService>,
    attributes: Vec<(String, String)>,
}

impl LaunchDarklyFeatureFlagsService {
    async fn try_new(
        application_id: &str,
        application_version: &str,
        ld_config: launchdarkly_server_sdk::ConfigBuilder,
    ) -> Result<FeatureFlagsServiceHandle, Box<dyn std::error::Error>> {
        let mut application_info = launchdarkly_server_sdk::ApplicationInfo::new();

        application_info
            .application_identifier(application_id)
            .application_version(application_version);

        let ld_config = ld_config
            .application_info(application_info)
            .build()
            .map_err(|e| {
                std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Invalid launch darkly config: {}", e),
                )
            })?;

        let ld_client = launchdarkly_server_sdk::Client::build(ld_config)?;
        ld_client.start_with_default_executor();

        if !ld_client
            .wait_for_initialization(Duration::from_secs(60))
            .await
            .unwrap_or_default()
        {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                "Failed to initialize launch darkly client",
            )
            .into());
        }

        let pod_name = std::env::var("POD_NAME")?;
        let pod_namespace = std::env::var("POD_NAMESPACE")?;
        let pod_cloud = match std::env::var("POD_CLOUD") {
            Ok(value) => value,
            Err(_) => {
                tracing::warn!("Missing POD_CLOUD environment variable");
                "".to_string()
            }
        };
        let pod_env = match std::env::var("POD_ENV") {
            Ok(value) => value,
            Err(_) => {
                tracing::warn!("Missing POD_ENV environment variable");
                "".to_string()
            }
        };

        let context = launchdarkly_server_sdk::ContextBuilder::new(pod_namespace.clone())
            .kind("namespace")
            .set_string("pod_name", pod_name.clone())
            .set_string("cloud", pod_cloud.clone())
            .set_string("env", pod_env.clone())
            .build()
            .map_err(|e| {
                std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Failed to create launch darkly context: {}", e),
                )
            })?;

        Ok(std::sync::Arc::new(LaunchDarklyFeatureFlagsService {
            client: Arc::new(ld_client),
            context,
            default_feature_flags: Arc::new(LocalFeatureFlagsService::new()),
            attributes: vec![
                ("pod_name".into(), pod_name),
                ("cloud".into(), pod_cloud),
                ("env".into(), pod_env),
            ],
        }))
    }
}

impl FeatureFlagsService for LaunchDarklyFeatureFlagsService {
    fn bind_attribute(
        &self,
        extra_attribute_name: &str,
        extra_attribute_value: &str,
    ) -> Result<FeatureFlagsServiceHandle, Box<dyn std::error::Error>> {
        let pod_namespace = std::env::var("POD_NAMESPACE")?;
        let mut attributes = self.attributes.clone();
        attributes.push((extra_attribute_name.into(), extra_attribute_value.into()));

        let mut builder = launchdarkly_server_sdk::ContextBuilder::new(pod_namespace);
        builder.kind("namespace");

        for (name, value) in &attributes {
            builder.set_string(name, value);
        }

        let context = builder.build().map_err(|e| {
            std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Failed to create launch darkly context: {}", e),
            )
        })?;

        Ok(Arc::new(LaunchDarklyFeatureFlagsService {
            client: self.client.clone(),
            context,
            default_feature_flags: self.default_feature_flags.clone(),
            attributes,
        }))
    }

    fn lookup_bool(&self, flag: &BoolFlag) -> bool {
        self.client.bool_variation(
            &self.context,
            flag.name,
            self.default_feature_flags.lookup_bool(flag),
        )
    }

    fn lookup_string(&self, flag: &StringFlag) -> String {
        self.client.str_variation(
            &self.context,
            flag.name,
            self.default_feature_flags.lookup_string(flag),
        )
    }

    fn lookup_i64(&self, flag: &IntFlag) -> i64 {
        self.client.int_variation(
            &self.context,
            flag.name,
            self.default_feature_flags.lookup_i64(flag),
        )
    }

    fn lookup_f64(&self, flag: &FloatFlag) -> f64 {
        self.client.float_variation(
            &self.context,
            flag.name,
            self.default_feature_flags.lookup_f64(flag),
        )
    }

    fn set_local_bool(&self, flag: &str, value: bool) {
        self.default_feature_flags.set_local_bool(flag, value)
    }

    fn set_local_string(&self, flag: &str, value: &str) {
        self.default_feature_flags.set_local_string(flag, value)
    }

    fn set_local_i64(&self, flag: &str, value: i64) {
        self.default_feature_flags.set_local_i64(flag, value)
    }

    fn set_local_f64(&self, flag: &str, value: f64) {
        self.default_feature_flags.set_local_f64(flag, value)
    }
}

fn launch_darkly_sdk_key_from_file(
    path: &std::path::PathBuf,
) -> Result<String, Box<dyn std::error::Error>> {
    std::fs::read_to_string(path)?
        .lines()
        .next()
        .map(|sdk_key| sdk_key.trim().to_owned())
        .ok_or(std::io::Error::new(std::io::ErrorKind::Other, "Empty sdk key file").into())
}

fn launch_darkly_config_builder_from_file(
    path: &std::path::PathBuf,
) -> Result<launchdarkly_server_sdk::ConfigBuilder, Box<dyn std::error::Error>> {
    Ok(launchdarkly_server_sdk::ConfigBuilder::new(
        &launch_darkly_sdk_key_from_file(path)?,
    ))
}

pub async fn setup(
    application_id: &str,
    application_version: &str,
    sdk_key_path: Option<&std::path::PathBuf>,
    custom_endpoint: Option<&str>,
) -> FeatureFlagsServiceHandle {
    let ld_config = match (custom_endpoint, sdk_key_path) {
        (Some(endpoint), Some(path)) => {
            let mut ld_config =
                launch_darkly_config_builder_from_file(path).expect("Invalid sdk key file");
            ld_config = ld_config.service_endpoints(
                ServiceEndpointsBuilder::new()
                    .streaming_base_url(endpoint)
                    .polling_base_url(endpoint)
                    .events_base_url(endpoint),
            );
            ld_config
        }
        (None, Some(path)) => {
            launch_darkly_config_builder_from_file(path).expect("Invalid sdk key file")
        }
        _ => return setup_local(),
    };

    LaunchDarklyFeatureFlagsService::try_new(application_id, application_version, ld_config)
        .await
        .expect("LaunchDarkly failed to start")
}

pub fn setup_local() -> FeatureFlagsServiceHandle {
    Arc::new(LocalFeatureFlagsService::new())
}

pub trait Registry {
    fn register_bool(&self, flag: &BoolFlag) -> Result<(), Box<dyn std::error::Error>>;
    fn register_string(&self, flag: &StringFlag) -> Result<(), Box<dyn std::error::Error>>;
    fn register_int(&self, flag: &IntFlag) -> Result<(), Box<dyn std::error::Error>>;
    fn register_float(&self, flag: &FloatFlag) -> Result<(), Box<dyn std::error::Error>>;

    fn lookup_flag(&self, name: &str) -> Option<FeatureFlagTypes>;
}

pub type RegistryHandle = std::sync::Arc<dyn Registry + Sync + Send>;

pub struct LocalRegistry {
    flags: Mutex<HashMap<String, FeatureFlagTypes>>,
}

impl LocalRegistry {
    fn new() -> Self {
        Self {
            flags: Mutex::new(HashMap::new()),
        }
    }

    fn register(
        &self,
        name: &str,
        flag: FeatureFlagTypes,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut map = self.flags.lock().unwrap();

        if map.contains_key(name) {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Flag {} already registered", name),
            )
            .into());
        }
        map.insert(name.to_string(), flag);
        Ok(())
    }
}

impl Registry for LocalRegistry {
    fn register_bool(&self, flag: &BoolFlag) -> Result<(), Box<dyn std::error::Error>> {
        self.register(flag.name, FeatureFlagTypes::Bool)
    }

    fn register_string(&self, flag: &StringFlag) -> Result<(), Box<dyn std::error::Error>> {
        self.register(flag.name, FeatureFlagTypes::String)
    }

    fn register_int(&self, flag: &IntFlag) -> Result<(), Box<dyn std::error::Error>> {
        self.register(flag.name, FeatureFlagTypes::Int)
    }

    fn register_float(&self, flag: &FloatFlag) -> Result<(), Box<dyn std::error::Error>> {
        self.register(flag.name, FeatureFlagTypes::Float)
    }

    fn lookup_flag(&self, name: &str) -> Option<FeatureFlagTypes> {
        self.flags.lock().unwrap().get(name).cloned()
    }
}

pub fn populate_from_hashmap(
    feature_flags: &FeatureFlagsServiceHandle,
    flags_map: &HashMap<String, Value>,
    registry: &RegistryHandle,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut errors: Vec<String> = vec![];

    for (name, value) in flags_map {
        match registry.lookup_flag(name) {
            Some(flag) => match flag {
                FeatureFlagTypes::Bool => match value {
                    Value::Bool(value) => feature_flags.set_local_bool(name, *value),
                    _ => errors.push(format!("Flag {} must be true or false, is {}", name, value)),
                },
                FeatureFlagTypes::String => match value {
                    Value::String(value) => feature_flags.set_local_string(name, value),
                    _ => errors.push(format!("Flag {} must be a string, is {}", name, value)),
                },
                FeatureFlagTypes::Int => match value {
                    Value::Number(value) => {
                        if let Some(value) = value.as_i64() {
                            feature_flags.set_local_i64(name, value)
                        } else {
                            errors.push(format!(
                                "Flag {} failed integer conversion, is {}",
                                name, value
                            ))
                        }
                    }
                    _ => errors.push(format!("Flag {} must be an integer, is {}", name, value)),
                },
                FeatureFlagTypes::Float => match value {
                    Value::Number(value) => {
                        if let Some(value) = value.as_f64() {
                            feature_flags.set_local_f64(name, value)
                        } else {
                            errors.push(format!(
                                "Flag {} failed float conversion, is {}",
                                name, value
                            ))
                        }
                    }
                    _ => errors.push(format!("Flag {} must be a float, is {}", name, value)),
                },
            },
            None => {
                errors.push(format!("Flag {} not found in registry", name));
            }
        }
    }

    if !errors.is_empty() {
        return Err(errors.join("\n").into());
    }

    Ok(())
}

pub fn new_registry() -> RegistryHandle {
    Arc::new(LocalRegistry::new())
}

#[cfg(test)]
mod tests {
    use serde_json::json;

    use super::*;

    #[test]
    fn test_flag_new() {
        let flag_bool = BoolFlag::new("b", true);
        assert_eq!(flag_bool.name, "b");
        assert!(flag_bool.default);
    }

    #[test]
    fn test_feature_flags_get_from() {
        let flag_bool = BoolFlag::new("print_hello_world", false);

        let feature_flags = setup_local();

        assert!(!flag_bool.get_from(&feature_flags));
        assert_eq!(StringFlag::new("a", "").get_from(&feature_flags), "");
        assert_eq!(IntFlag::new("b", 12).get_from(&feature_flags), 12);
        assert_eq!(FloatFlag::new("c", 12.0).get_from(&feature_flags), 12.0);
    }

    #[test]
    fn test_feature_flags_bool_set() {
        let flag_bool = BoolFlag::new("print_hello_world", false);

        let feature_flags = setup_local();

        flag_bool.set_local(&feature_flags, true);
        assert!(flag_bool.get_from(&feature_flags));
    }

    #[test]
    fn test_feature_flags_string_set() {
        let flag_string = StringFlag::new("a", "");

        let feature_flags = setup_local();

        flag_string.set_local(&feature_flags, "hello");
        assert_eq!(flag_string.get_from(&feature_flags), "hello");
    }

    #[test]
    fn test_feature_flags_int_set() {
        let flag_int = IntFlag::new("b", 12);

        let feature_flags = setup_local();

        flag_int.set_local(&feature_flags, 13);
        assert_eq!(flag_int.get_from(&feature_flags), 13);
    }

    #[test]
    fn test_feature_flags_float_set() {
        let flag_float = FloatFlag::new("c", 12.0);

        let feature_flags = setup_local();

        flag_float.set_local(&feature_flags, 13.0);
        assert_eq!(flag_float.get_from(&feature_flags), 13.0);
    }

    #[test]
    fn test_registry_register() {
        let flag_bool = BoolFlag::new("print_hello_world", false);
        let flag_string = StringFlag::new("a", "");
        let flag_int = IntFlag::new("b", 12);
        let flag_float = FloatFlag::new("c", 12.0);

        let registry = new_registry();

        flag_bool.register(&registry).unwrap();
        flag_string.register(&registry).unwrap();
        flag_int.register(&registry).unwrap();
        flag_float.register(&registry).unwrap();

        assert_eq!(
            registry.lookup_flag("print_hello_world"),
            Some(FeatureFlagTypes::Bool)
        );
        assert_eq!(registry.lookup_flag("a"), Some(FeatureFlagTypes::String));
        assert_eq!(registry.lookup_flag("b"), Some(FeatureFlagTypes::Int));
        assert_eq!(registry.lookup_flag("c"), Some(FeatureFlagTypes::Float));
    }

    #[test]
    fn test_registry_register_duplicate() {
        let flag_bool = BoolFlag::new("print_hello_world", false);
        let flag_string = StringFlag::new("a", "");
        let flag_int = IntFlag::new("b", 12);
        let flag_float = FloatFlag::new("c", 12.0);

        let registry = new_registry();

        flag_bool.register(&registry).unwrap();
        flag_string.register(&registry).unwrap();
        flag_int.register(&registry).unwrap();
        flag_float.register(&registry).unwrap();

        assert!(flag_bool.register(&registry).is_err());
        assert!(flag_string.register(&registry).is_err());
        assert!(flag_int.register(&registry).is_err());
        assert!(flag_float.register(&registry).is_err());
    }

    #[test]
    fn test_feature_flags_set_from_hashmap() {
        let flag_bool = BoolFlag::new("print_hello_world", false);
        let flag_string = StringFlag::new("a", "");
        let flag_int = IntFlag::new("b", 12);
        let flag_float = FloatFlag::new("c", 12.0);

        let feature_flags = setup_local();
        let registry = new_registry();

        flag_bool.register(&registry).unwrap();
        flag_string.register(&registry).unwrap();
        flag_int.register(&registry).unwrap();
        flag_float.register(&registry).unwrap();

        assert!(populate_from_hashmap(
            &feature_flags,
            &HashMap::from([
                ("print_hello_world".to_string(), json!(true)),
                ("a".to_string(), json!("hello")),
                ("b".to_string(), json!(13)),
                ("c".to_string(), json!(13.0)),
            ]),
            &registry,
        )
        .is_ok());

        assert!(flag_bool.get_from(&feature_flags));
        assert_eq!(flag_string.get_from(&feature_flags), "hello");
        assert_eq!(flag_int.get_from(&feature_flags), 13);
        assert_eq!(flag_float.get_from(&feature_flags), 13.0);
    }

    #[test]
    fn test_feature_flags_set_from_hashmap_errors() {
        let flag_bool = BoolFlag::new("print_hello_world", false);
        let flag_string = StringFlag::new("a", "");
        let flag_int = IntFlag::new("b", 12);
        let flag_float = FloatFlag::new("c", 12.0);

        let feature_flags = setup_local();
        let registry = new_registry();

        flag_bool.register(&registry).unwrap();
        flag_string.register(&registry).unwrap();
        flag_int.register(&registry).unwrap();
        flag_float.register(&registry).unwrap();

        assert!(populate_from_hashmap(
            &feature_flags,
            &HashMap::from([("not_exist".to_string(), json!(13))]),
            &registry,
        )
        .is_err());

        assert!(populate_from_hashmap(
            &feature_flags,
            &HashMap::from([("print_hello_world".to_string(), json!(13))]),
            &registry,
        )
        .is_err());

        assert!(populate_from_hashmap(
            &feature_flags,
            &HashMap::from([("a".to_string(), json!(1))]),
            &registry,
        )
        .is_err());

        assert!(populate_from_hashmap(
            &feature_flags,
            &HashMap::from([("b".to_string(), json!("hello"))]),
            &registry,
        )
        .is_err());

        assert!(populate_from_hashmap(
            &feature_flags,
            &HashMap::from([("c".to_string(), json!("hello"))]),
            &registry,
        )
        .is_err());
    }
}
