// the tensor proto provides a convenient way to store
// tensor information. The format
// is similar to the NVIDIA triton/kserve API grpc format.
// However, this is not tightly coupled to the kserve format
// itself.
syntax = "proto3";

package tensor;

message Tensor {
  // The triton tensor data type.
  string datatype = 1;

  // The tensor shape.
  repeated int64 shape = 2;

  // the raw content
  //
  // redact: technically not every tensor might be restricted information, but it is
  // the most common case.
  bytes contents = 3 [debug_redact = true];
}
