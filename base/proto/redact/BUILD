load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_library", "go_proto_library", "go_test")

go_library(
    name = "redact",
    srcs = ["redact.go"],
    importpath = "github.com/augmentcode/augment/base/proto/redact",
    visibility = ["//visibility:public"],
    deps = [
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//reflect/protopath",
        "@org_golang_google_protobuf//reflect/protorange",
        "@org_golang_google_protobuf//reflect/protoreflect",
        "@org_golang_google_protobuf//types/descriptorpb",
    ],
)

proto_library(
    name = "redact_test_proto",
    srcs = ["redact_test.proto"],
)

go_proto_library(
    name = "redact_test_go_proto",
    importpath = "github.com/augmentcode/augment/base/proto/redact/test",
    proto = ":redact_test_proto",
)

go_test(
    name = "redact_test",
    srcs = ["redact_test.go"],
    embed = [":redact"],
    deps = [
        ":redact_test_go_proto",
        "@com_github_stretchr_testify//assert",
    ],
)
