syntax = "proto3";

enum State {
  NOT_SET = 0;
  SET = 1;
}

message TestMessage {
  string username = 1;
  string password = 2 [debug_redact = true];
  repeated string secrets = 3 [debug_redact = true];
  map<string, string> api_keys = 4 [debug_redact = true];
  NestedMessage nested = 5;
  NestedMessage nest_redacted = 9 [debug_redact = true];
  bytes bytes = 6 [debug_redact = true];
  repeated int32 ints = 7 [debug_redact = true];
  repeated float floats = 8 [debug_redact = true];
  State state = 10 [debug_redact = true];

  message NestedMessage {
    string confidential = 1 [debug_redact = true];
    string public_info = 2;
  }
}
