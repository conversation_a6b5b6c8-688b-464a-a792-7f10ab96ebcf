package redact

import (
	"encoding/json"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protopath"
	"google.golang.org/protobuf/reflect/protorange"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/descriptorpb"
)

func isAnyRedacted(p protopath.Values) bool {
	for i := range p.Len() {
		step := p.Index(i)
		fd := step.Step.FieldDescriptor()
		if fd == nil {
			continue
		}
		opts := fd.Options().(*descriptorpb.FieldOptions)
		if opts == nil || opts.DebugRedact == nil || !*opts.DebugRedact {
			continue
		}
		return true
	}
	return false
}

func redactString(p protopath.Values, last protopath.Step) {
	s := "[REDACTED]"

	// Store the redacted string back into the message.
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfString(s))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfString(s))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfString(s))
	}
}

func redactBytes(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfBytes([]byte{}))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfBytes([]byte{}))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfBytes([]byte{}))
	}
}

func redactInt32(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfInt32(0))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfInt32(0))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfInt32(0))
	}
}

func redactInt64(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfInt64(0))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfInt64(0))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfInt64(0))
	}
}

func redactUint32(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfUint32(0))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfUint32(0))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfUint32(0))
	}
}

func redactUint64(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfUint64(0))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfUint64(0))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfUint64(0))
	}
}

func redactFloat32(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfFloat32(0))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfFloat32(0))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfFloat32(0))
	}
}

func redactFloat64(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfFloat64(0))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfFloat64(0))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfFloat64(0))
	}
}

func redactEnum(p protopath.Values, last protopath.Step) {
	beforeLast := p.Index(-2)
	switch last.Kind() {
	case protopath.FieldAccessStep:
		m := beforeLast.Value.Message()
		fd := last.FieldDescriptor()
		m.Set(fd, protoreflect.ValueOfEnum(0))
	case protopath.ListIndexStep:
		ls := beforeLast.Value.List()
		i := last.ListIndex()
		ls.Set(i, protoreflect.ValueOfEnum(0))
	case protopath.MapIndexStep:
		ms := beforeLast.Value.Map()
		k := last.MapIndex()
		ms.Set(k, protoreflect.ValueOfEnum(0))
	}
}

// RedactMessage modifies a proto message by redacting it.
//
// Fields that are marked debug_redact or when any of its parent messages is marked debug_redact will be replaced with "[REDACTED]"
func RedactMessage(msg proto.Message) {
	protorange.Range(msg.ProtoReflect(), func(p protopath.Values) error {
		last := p.Index(-1)
		value := last.Value.Interface()
		switch value.(type) {
		case string:
			if !isAnyRedacted(p) {
				return nil
			}
			redactString(p, last.Step)
		case []byte:
			if !isAnyRedacted(p) {
				return nil
			}
			redactBytes(p, last.Step)
		case int32:
			if !isAnyRedacted(p) {
				return nil
			}
			redactInt32(p, last.Step)
		case int64:
			if !isAnyRedacted(p) {
				return nil
			}
			redactInt64(p, last.Step)
		case uint32:
			if !isAnyRedacted(p) {
				return nil
			}
			redactUint32(p, last.Step)
		case uint64:
			if !isAnyRedacted(p) {
				return nil
			}
			redactUint64(p, last.Step)
		case float32:
			if !isAnyRedacted(p) {
				return nil
			}
			redactFloat32(p, last.Step)
		case float64:
			if !isAnyRedacted(p) {
				return nil
			}
			redactFloat64(p, last.Step)
		case protoreflect.EnumNumber:
			if !isAnyRedacted(p) {
				return nil
			}
			redactEnum(p, last.Step)
		}
		return nil
	})
}

// ToRedactString generates a redacted string representation of a Protobuf message.
//
// Fields that are marked debug_redact or when any of its parent messages is marked debug_redact will be replaced with "[REDACTED]"
func ToRedactString(msg proto.Message) string {
	if msg == nil {
		return "<nil>"
	}

	// clone m
	m := proto.Clone(msg)
	RedactMessage(m)
	mo := protojson.MarshalOptions{
		Multiline:     false,
		UseProtoNames: true,
	}
	return mo.Format(m)
}

func ToRedactJSON(msg proto.Message) (map[string]interface{}, error) {
	if msg == nil {
		return nil, nil
	}

	// clone m
	m := proto.Clone(msg)
	RedactMessage(m)
	mo := protojson.MarshalOptions{
		Multiline:     false,
		UseProtoNames: true,
	}
	s, err := mo.Marshal(m)
	if err != nil {
		return nil, err
	}
	r := map[string]interface{}{}
	err = json.Unmarshal(s, &r)
	if err != nil {
		return nil, err
	}
	return r, nil
}
