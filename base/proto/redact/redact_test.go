package redact

import (
	"strings"
	"testing"

	testproto "github.com/augmentcode/augment/base/proto/redact/test"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
)

// TestToRedactString tests the ToRedactString function with various Protobuf messages.
func TestToRedactString(t *testing.T) {
	tests := []struct {
		name         string
		inputMessage proto.Message
		expected     string
	}{
		{
			name: "simple message with redact",
			inputMessage: &testproto.TestMessage{
				Username: "john_doe",
				Password: "super_secret",
			},
			expected: "{\"username\":\"john_doe\",\"password\":\"[REDACTED]\"}",
		},
		{
			name:         "nil",
			inputMessage: nil,
			expected:     "<nil>",
		},
		{
			name: "message with list",
			inputMessage: &testproto.TestMessage{
				Secrets: []string{"secret1", "secret2"},
			},
			expected: "{\"secrets\":[\"[REDACTED]\",\"[REDACTED]\"]}",
		},
		{
			name: "message with map",
			inputMessage: &testproto.TestMessage{
				ApiKeys: map[string]string{
					"key1": "value1",
					"key2": "value2",
				},
			},
			expected: "{\"api_keys\":{\"key1\":\"[REDACTED]\",\"key2\":\"[REDACTED]\"}}",
		},
		{
			name: "nested message with redact",
			inputMessage: &testproto.TestMessage{
				Nested: &testproto.TestMessage_NestedMessage{
					Confidential: "hidden",
					PublicInfo:   "visible",
				},
			},
			expected: "{\"nested\":{\"confidential\":\"[REDACTED]\",\"public_info\":\"visible\"}}",
		},
		{
			name: "message with bytes",
			inputMessage: &testproto.TestMessage{
				Bytes: []byte("bytes"),
			},
			expected: "{}",
		},
		{
			name: "message with ints",
			inputMessage: &testproto.TestMessage{
				Ints: []int32{1, 2, 3},
			},
			expected: "{\"ints\":[0,0,0]}",
		},
		{
			name: "message with floats",
			inputMessage: &testproto.TestMessage{
				Floats: []float32{1.1, 2.2, 3.3},
			},
			expected: "{\"floats\":[0,0,0]}",
		},
		{
			name: "message with nested message",
			inputMessage: &testproto.TestMessage{
				NestRedacted: &testproto.TestMessage_NestedMessage{
					Confidential: "hidden",
					PublicInfo:   "visible",
				},
			},
			expected: "{\"nest_redacted\":{\"confidential\":\"[REDACTED]\",\"public_info\":\"[REDACTED]\"}}",
		},
		{
			name: "message with enum",
			inputMessage: &testproto.TestMessage{
				State: testproto.State_SET,
			},
			expected: "{}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToRedactString(tt.inputMessage)
			assert.Equal(t, tt.expected, strings.ReplaceAll(result, " ", ""))
		})
	}
}
