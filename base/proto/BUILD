load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_proto_library")
load("//tools/bzl:python.bzl", "py_library", "py_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "tensor_proto",
    srcs = ["tensor.proto"],
    visibility = BASE_VISIBILITY,
)

py_proto_library(
    name = "tensor_py_proto",
    protos = [":tensor_proto"],
    visibility = BASE_VISIBILITY,
)

go_proto_library(
    name = "tensor_go_proto",
    importpath = "github.com/augmentcode/augment/base/proto/tensor",
    proto = ":tensor_proto",
    visibility = BASE_VISIBILITY,
)

py_library(
    name = "tensor",
    srcs = ["tensor.py"],
    visibility = BASE_VISIBILITY,
    deps = [
        ":tensor_py_proto",
        ":tensor_utils",
        requirement("numpy"),
    ],
)

py_library(
    name = "tensor_utils",
    srcs = ["tensor_utils.py"],
    deps = [
        requirement("numpy"),
    ],
)

pytest_test(
    name = "tensor_test",
    srcs = ["tensor_test.py"],
    deps = [":tensor"],
)
