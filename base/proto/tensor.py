"""Helper functions to store/load numpy arrays into and from protobuf tensor objects."""

import numpy as np

from base.proto import tensor_pb2
from base.proto.tensor_utils import (
    deserialize_bf16_tensor,
    deserialize_bytes_tensor,
    np_to_triton_dtype,
    serialize_bf16_tensor,
    serialize_byte_tensor,
    triton_to_np_dtype,
)

_BYTES_DATA_TYPE_STR = "BYTES"
_BF16_DATA_TYPE_STR = "BF16"


def to_tensor(input_data: np.ndarray) -> tensor_pb2.Tensor:
    """Move numpy input data to a protobuf tensor.

    The protobuf tensor object can be used to send tensor information
    via protobuf messages.
    """
    tensor = tensor_pb2.Tensor()
    dtype = np_to_triton_dtype(input_data.dtype)
    assert dtype
    tensor.datatype = dtype
    tensor.shape.extend(input_data.shape)
    if tensor.datatype == _BYTES_DATA_TYPE_STR:
        serialized_output = serialize_byte_tensor(input_data)
        if serialized_output.size > 0:
            tensor.contents = serialized_output.item()
        else:
            tensor.contents = b""
    elif tensor.datatype == _BF16_DATA_TYPE_STR:
        serialized_output = serialize_bf16_tensor(input_data)
        if serialized_output.size > 0:
            tensor.contents = serialized_output.item()
        else:
            tensor.contents = b""
    else:
        tensor.contents = input_data.tobytes()

    return tensor


def to_numpy(tensor: tensor_pb2.Tensor) -> np.ndarray:
    """Transforms a protobuf tensor object into the matching numpy array."""
    if tensor.datatype == _BYTES_DATA_TYPE_STR:
        # String results contain a 4-byte string length
        # followed by the actual string characters. Hence,
        # need to decode the raw bytes to convert into
        # array elements.
        np_array = deserialize_bytes_tensor(tensor.contents)
    elif tensor.datatype == _BF16_DATA_TYPE_STR:
        np_array = deserialize_bf16_tensor(tensor.contents)
    else:
        np_array = np.frombuffer(
            tensor.contents, dtype=triton_to_np_dtype(tensor.datatype)
        )
    np_array = np_array.reshape(tensor.shape)
    return np_array
