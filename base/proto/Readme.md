# base protobufs

> We have yet to make the files in this directory work with the infrastructure in `research/`.

Protobuf definitions and support code used in different
services of the production infrastructure.

## Services

Various services might want to share protobuf types, e.g. because they have related API definitions.
These types aren't designed for reuse by models or research code.

## Tensor

The `tensor.proto` contains a protobuf message that makes it convenient to transfer tensor
information in protobuf messages.

The package contains the protobuf definition as well as Python utilizes for reading and writing it.
