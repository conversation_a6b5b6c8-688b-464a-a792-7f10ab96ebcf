"""Common dataclasses shared across next edit prompt formatters."""

from collections.abc import Sequence
from dataclasses import dataclass

from dataclasses_json import dataclass_json

from base.diff_utils.changes import Changed
from base.diff_utils.diff_utils import File
from base.ranges.range_types import CharRange


@dataclass(frozen=True)
class NextEditPromptInput:
    """Common input for the next edit prompt formatters."""

    current_file: File
    """The current file."""

    # TODO(arun): Deprecate this field in favor of `current_file.path`.
    @property
    def current_path(self) -> str:
        """Path of the current file."""
        return self.current_file.path

    # TODO(arun): Deprecate this field in favor of `current_file.contents`.
    @property
    def current_code(self) -> str:
        """Content of the current file."""
        return self.current_file.contents

    edit_region: CharRange
    """The character range of the selected edit region in `current_code`."""

    instruction: str
    """An description of the current task/commit/PR. Empty if not provided."""

    recent_changes: Sequence[Changed[File]]
    """All changes since the last commit/PR, sorted from oldest to newest."""

    @property
    def prefix(self) -> str:
        """The code corresponding to the edit region."""
        return self.current_code[: self.edit_region.start]

    @property
    def suffix(self) -> str:
        """The code corresponding to the edit region."""
        return self.current_code[self.edit_region.stop :]

    @property
    def selected_code(self) -> str:
        """The code corresponding to the edit region."""
        return self.current_code[self.edit_region.to_slice()]


@dataclass_json
@dataclass(kw_only=True)
class NextEditFormatterConfig:
    """Common configuration for the next edit prompt formatters."""

    diff_context_lines: int = 5
    """The number of hunk context lines to show in the diff section."""

    max_prompt_tokens: int = 6800
    """The maximum number of tokens used by the input prompt."""

    filter_duplicated_file_paths: bool = True
    """Whether to filter out duplicated file paths in the diff."""

    use_smart_header_diff: bool = True
    """Whether to use smart header in the diff.

    Checkpoints trained with PromptConfig.VERSION < 1.11 should disable this.
    """
