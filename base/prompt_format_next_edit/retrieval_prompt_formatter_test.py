import pytest

from base.diff_utils.changes import Modified
from base.diff_utils.diff_formatter import File
from base.prompt_format_next_edit.retrieval_prompt_formatter import (
    EditGenRetrievalPromptInput,
    EditGenRetrievalQueryFormatterConfig,
    EditGenRetrievalQueryPromptFormatter,
    SectionBudgetsDict,
)
from base.ranges.range_types import CharRang<PERSON>, LineRange
from base.test_utils.testing_utils import assert_str_eq
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tiktoken_starcoder_tokenizer import StarCoderTokenizer
from base.tokenizers.tokenizer import (
    NextEditGenSpecialTokens,
    RetrievalSpecialTokens,
    Tokenizer,
)

OLD_CODE = """\
def ex_func1(a,b):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b
"""

NEW_CODE = """\
def ex_func1(a: int, b: int):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b

def new_func():
    return 1
"""


@pytest.mark.parametrize(
    "tokenizer_name",
    [
        "starcoder",
    ],
)
class TestEditGenRetrievalQueryPromptFormatter:
    @pytest.fixture
    def valid_input(self) -> EditGenRetrievalPromptInput:
        return EditGenRetrievalPromptInput(
            current_file=File("src/examples.py", NEW_CODE),
            edit_region=CharRange(
                NEW_CODE.index("def ex_func2(a,b):\n"),
                NEW_CODE.index("def ex_func3(a,b):\n"),
            ),
            instruction="fix bugs",
            recent_changes=[
                Modified(
                    before=File(path="src/examples.py", contents=OLD_CODE),
                    after=File(path="src/examples.py", contents=NEW_CODE),
                )
            ],
        )

    @pytest.fixture
    def tokenizer(self, tokenizer_name: str):
        return create_tokenizer_by_name(tokenizer_name)

    def test_post_init_validates_section_budgets(self, tokenizer: Tokenizer):
        with pytest.raises(ValueError):
            EditGenRetrievalQueryPromptFormatter(
                tokenizer=tokenizer,
                config=EditGenRetrievalQueryFormatterConfig(
                    max_prompt_tokens=7 - 1,
                    section_budgets=SectionBudgetsDict(
                        suffix_tks=0,
                        selected_tks=0,
                        prefix_tks=0,
                        diff_tks=0,
                        filename_tks=0,
                        instruction_tks=0,
                    ),
                ),
            )

        with pytest.raises(ValueError):
            EditGenRetrievalQueryPromptFormatter(
                tokenizer=tokenizer,
                config=EditGenRetrievalQueryFormatterConfig(
                    max_prompt_tokens=7 + 6 - 1,
                    section_budgets=SectionBudgetsDict(
                        suffix_tks=1,
                        selected_tks=1,
                        prefix_tks=1,
                        diff_tks=1,
                        filename_tks=1,
                        instruction_tks=1,
                    ),
                ),
            )

    def test_format_prompt_default_budget(
        self, valid_input: EditGenRetrievalPromptInput, tokenizer: Tokenizer
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )

        expected_tokens = [
            special_tokens.diff_section,
            *tokenizer.tokenize_safe("""\
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
@@ -8,1 +8,4 @@
@def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            special_tokens.instruction,
            *tokenizer.tokenize_safe("fix bugs"),
            special_tokens.filename,
            *tokenizer.tokenize_safe("src/examples.py"),
            special_tokens.fim_prefix,
            *tokenizer.tokenize_safe("""\
def ex_func1(a: int, b: int):
    return a+b

"""),
            special_tokens.selected_code,
            *tokenizer.tokenize_safe("""\
def ex_func2(a,b):
    return a-b

"""),
            special_tokens.fim_suffix,
            *tokenizer.tokenize_safe("""\
def ex_func3(a,b):
    return a*b

def new_func():
    return 1
"""),
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            EditGenRetrievalQueryPromptFormatter(
                tokenizer=tokenizer,
                config=EditGenRetrievalQueryFormatterConfig(
                    diff_context_lines=1,
                ),
            )
            .format_prompt(valid_input)
            .tokens()
        )

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."

    def test_format_prompt_short_budget(
        self, valid_input: EditGenRetrievalPromptInput, tokenizer: Tokenizer
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )

        expected_tokens = [
            special_tokens.diff_section,
            # NOTE(arun): we truncate the top-most hunk because we're out of tokens.
            *tokenizer.tokenize_safe("""\
+++ src/examples.py
@@ -8,1 +8,4 @@
@def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            # NOTE(arun): we preserve the instruction and path.
            special_tokens.instruction,
            *tokenizer.tokenize_safe("fix bugs"),
            special_tokens.filename,
            *tokenizer.tokenize_safe("src/examples.py"),
            special_tokens.fim_prefix,
            *tokenizer.tokenize_safe("""\
def ex_func1(a: int, b: int):
    return a+b

"""),
            special_tokens.selected_code,
            *tokenizer.tokenize_safe("""\
def ex_func2(a,b):
    return a-b

"""),
            special_tokens.fim_suffix,
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            EditGenRetrievalQueryPromptFormatter(
                tokenizer=tokenizer,
                config=EditGenRetrievalQueryFormatterConfig(
                    # The full prompt fits in 171 tokens.
                    max_prompt_tokens=100,
                    diff_context_lines=1,
                    section_budgets=SectionBudgetsDict(
                        suffix_tks=0,
                        selected_tks=0,
                        prefix_tks=0,
                        diff_tks=0,
                        filename_tks=0,
                        instruction_tks=0,
                    ),
                ),
            )
            .format_prompt(valid_input)
            .tokens()
        )
        assert len(actual_tokens) <= 100

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."
