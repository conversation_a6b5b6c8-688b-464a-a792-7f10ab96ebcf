"""Prompt formatter for next edit diff descriptions."""

from base.diff_utils.diff_utils import File
from base.prompt_format_next_edit.description_prompt_formatter import (
    EditDescriptionPromptInput,
    RavenDescribePromptFormatter,
)
from base.ranges.line_map import LineMap
from base.ranges.range_types import LineRange
from base.test_utils.testing_utils import assert_str_eq
from base.tokenizers import create_tokenizer_by_name


def test_raven_describe_format_input():
    # Test end-to-end formatting of a small example.
    current_code = """\
import pathlib

file = pathlib.Path("foo")
lines = 0
for x in file.open():
    print(x)
    lines += 1
"""
    lmap = LineMap(current_code)
    # select line 2, 3, 4
    edit_region = lmap.lrange_to_crange(LineRange(2, 5))

    input = EditDescriptionPromptInput(
        current_file=File("src/current.py", current_code),
        edit_region=edit_region,
        instruction="fix bugs",
        recent_changes=[],
        replacement_text="""\
file = pathlib.Path("foo")
lines = 1
for x in file.open():
""",
    )

    prompt_formatter = RavenDescribePromptFormatter(
        tokenizer=create_tokenizer_by_name("llama3_instruct"),
        config=RavenDescribePromptFormatter.Config(
            max_prompt_tokens=1000,
        ),
    )

    actual = prompt_formatter.format_input(input)
    assert_str_eq(actual.system_prompt or "", prompt_formatter.system_prompt)
    assert_str_eq(
        actual.message,
        r"""Here is the code change delimited by triple backticks:
```
--- src/current.py
+++ src/current.py
@@ -1,7 +1,7 @@
 import pathlib
 
 file = pathlib.Path("foo")
-lines = 0
+lines = 1
 for x in file.open():
     print(x)
     lines += 1

```
ONLY respond with the description.
""",
    )
    assert actual.chat_history == []
    assert actual.retrieved_chunks_in_prompt == []
