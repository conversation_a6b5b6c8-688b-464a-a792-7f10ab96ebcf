"""Prompt formatter for next edit generation retrieval."""

from dataclasses import dataclass, field
from pathlib import Path
from typing import (
    Callable,
    Literal,
    TypedDict,
    cast,
)

from dataclasses_json import dataclass_json

from base.diff_utils.diff_formatter import (
    format_file_changes_with_ranges,
    format_tokenized_hunks,
    tokenize_diff_hunks,
    truncate_diff_hunk_tokens,
)
from base.languages.language_guesser import guess_language
from base.prompt_format.common import PromptFormatterOutput, TokenList
from base.prompt_format.util import CascadeTruncatingStrategy, head_n, trailing_n
from base.prompt_format_next_edit.common import (
    NextEditFormatterConfig,
    NextEditPromptInput,
)
from base.prompt_format_retrieve.prompt_formatter import (
    RetrieverPromptFormatter,
)
from base.tokenizers import Tokenizer
from base.tokenizers.tokenizer import NextEditGenSpecialTokens, RetrievalSpecialTokens


@dataclass(frozen=True)
class EditGenRetrievalPromptInput(NextEditPromptInput):
    """Input to the EditGenRetrievalPromptFormatter."""

    # NOTE(arun): Any retrieval specific inputs go here.
    pass


PromptSectionName = Literal["instruction", "filename", "diff", "selection"]

default_prompt_section_order = (
    "diff",
    "instruction",
    "filename",
    "selection",
)
"""A default order of prompt sections that is cache-friendly."""


class SectionBudgetsDict(TypedDict):
    """The token budgets for different prompt sections.

    Note that latter sections have higher priorities when running out of tokens.
    """

    suffix_tks: int
    selected_tks: int
    prefix_tks: int
    diff_tks: int
    filename_tks: int
    instruction_tks: int


def default_section_budgets() -> SectionBudgetsDict:
    """Default token budgets for prompt sections."""
    # Total is 3_500.
    return SectionBudgetsDict(
        suffix_tks=500,
        selected_tks=250,
        prefix_tks=500,
        diff_tks=2000,
        filename_tks=50,
        instruction_tks=200,
    )


def default_diff_filter(path: Path) -> bool:
    """Filter out diffs coming from certain file paths."""
    lang = guess_language(str(path))
    # Filter out notebook diffs since they contain more than user code
    return lang is not None and path.suffix != ".ipynb"


@dataclass_json
@dataclass
class EditGenRetrievalQueryFormatterConfig(NextEditFormatterConfig):
    """The configurable parameters for the EditGenPromptFormatter."""

    section_order: tuple[PromptSectionName, ...] = default_prompt_section_order

    section_budgets: SectionBudgetsDict = field(default_factory=default_section_budgets)
    """The token budgets for different input prompt sections."""


@dataclass
class EditGenRetrievalQueryPromptFormatter(
    RetrieverPromptFormatter[EditGenRetrievalPromptInput]
):
    """Prompt formatter for edit generation."""

    input_type = EditGenRetrievalPromptInput

    tokenizer: Tokenizer
    """The tokenizer to use, must have NextEditSpecialTokens."""

    config: EditGenRetrievalQueryFormatterConfig = field(
        default_factory=EditGenRetrievalQueryFormatterConfig
    )
    """The configurable parameters for the EditGenPromptFormatter."""

    diff_filter: Callable[[Path], bool] = default_diff_filter
    """Filter out the diffs coming from certain file paths."""

    def __post_init__(self):
        special_tokens = self.tokenizer.special_tokens
        if not isinstance(special_tokens, NextEditGenSpecialTokens):
            raise ValueError(
                f"Expected NextEditGenSpecialTokens, but got {type(special_tokens)}. "
                f"The provided tokenizer type is {type(self.tokenizer)}"
            )
        if not isinstance(special_tokens, RetrievalSpecialTokens):
            raise ValueError(
                f"Expected RetrievalSpecialTokens, but got {type(special_tokens)}. "
                f"The provided tokenizer type is {type(self.tokenizer)}"
            )
        self.special_tokens = special_tokens

        budgets = cast(dict[str, int], self.config.section_budgets)
        sections_total = sum(budgets.values())
        self._reserved_tokens = (
            1  # for diff_section_id
            + 1  # for instruction_id
            + 1  # for filename_id
            + 3  # for fim_prefix, selected_code, fim_suffix
            + 1  # for ret_end_of_query
        )
        limit = self.config.max_prompt_tokens - self._reserved_tokens
        if sections_total > limit:
            raise ValueError(f"{sections_total=} > {limit=}.")

    def format_prompt(
        self, prompt_input: EditGenRetrievalPromptInput
    ) -> PromptFormatterOutput:
        """Format the input into a token sequence.

        The prompt with default section order follows the following format
        (newlines are explicitly shown):
        ```
        <|diff_section|>{diff hunks formatted with `format_file_changes`}
        <|instruction|>{instruction}
        <filename>{current_path}
        <fim_prefix>{current code before edit_region}
        <selected-code>{current code in edit_region}
        <fim-suffix>{current code after edit_region}
        <ret-end-of-query>
        ```

        See also: `format_retrieval_chunks` and `format_file_changes`.
        """
        config = self.config
        tkn = self.tokenizer

        instruction_tks = tkn.tokenize_safe(prompt_input.instruction)
        filename_tks = tkn.tokenize_safe(prompt_input.current_path)
        diff_hunks = format_file_changes_with_ranges(
            prompt_input.recent_changes,
            diff_context_lines=config.diff_context_lines,
            diff_filter=self.diff_filter,
        )
        t_hunks = tokenize_diff_hunks(
            diff_hunks,
            # Use an overestimate of the budget here -- we'll get an updated budget and
            # truncate it later. This is an optimization step to reduce the number of
            # hunks we tokenize.
            config.max_prompt_tokens,
            tkn,
            deduplicate_identical_paths=config.filter_duplicated_file_paths,
        )
        # We roll the selected code into the prefix.
        prefix_tks = tkn.tokenize_safe(prompt_input.prefix)
        selected_tks = tkn.tokenize_safe(prompt_input.selected_code)
        suffix_tks = tkn.tokenize_safe(prompt_input.suffix)

        truncation_strategy = CascadeTruncatingStrategy(
            total_budget=config.max_prompt_tokens - self._reserved_tokens,
            section_budgets=cast(dict[str, int], dict(config.section_budgets)),
        )
        section_sizes = truncation_strategy.compute_section_sizes(
            {
                "selected_tks": len(selected_tks),
                "instruction_tks": len(instruction_tks),
                "diff_tks": sum(hunk.num_tokens() for hunk in t_hunks),
                "filename_tks": len(filename_tks),
                "prefix_tks": len(prefix_tks),
                "suffix_tks": len(suffix_tks),
            }
        )
        filename_tks = trailing_n(filename_tks, section_sizes["filename_tks"])
        # TODO(jiayi): use quantized truncation for diffs to better handle eviction
        # Use the last N tokens of the diff to bias towards recent changes.
        t_hunks = truncate_diff_hunk_tokens(t_hunks, section_sizes["diff_tks"])
        diff_tks = format_tokenized_hunks(t_hunks)
        prefix_tks = trailing_n(prefix_tks, section_sizes["prefix_tks"])
        selected_tks = head_n(selected_tks, section_sizes["selected_tks"])
        suffix_tks = head_n(suffix_tks, section_sizes["suffix_tks"])
        instruction_tks = head_n(instruction_tks, section_sizes["instruction_tks"])

        section_tokens: dict[PromptSectionName, TokenList] = {
            "instruction": [
                self.special_tokens.instruction,
                *instruction_tks,
            ],
            "filename": [
                self.special_tokens.filename,
                *filename_tks,
            ],
            "diff": [
                self.special_tokens.diff_section,
                *diff_tks,
            ],
            "selection": [
                self.special_tokens.fim_prefix,
                *prefix_tks,
                self.special_tokens.selected_code,
                *selected_tks,
                self.special_tokens.fim_suffix,
                *suffix_tks,
            ],
        }

        input_prompt = TokenList()
        for section_name in config.section_order:
            input_prompt += section_tokens[section_name]
            assert (
                len(input_prompt) <= config.max_prompt_tokens
            ), f"Output too long after {section_name=}: {len(input_prompt)=} > {config.max_prompt_tokens=}"

        input_prompt += [self.special_tokens.end_of_query]
        return PromptFormatterOutput([input_prompt])
