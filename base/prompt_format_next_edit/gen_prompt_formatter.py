"""Prompt formatter for next edit generation."""

import copy
from enum import Enum
import logging
import math
import re
import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import (
    Callable,
    Sequence,
    TypedDict,
    TypeVar,
    assert_never,
    cast,
)

from dataclasses_json import dataclass_json

from base.diff_utils.changes import Modified
from base.diff_utils.diff_formatter import (
    format_file_changes_with_ranges,
    format_tokenized_hunks,
    tokenize_diff_hunks,
    truncate_diff_hunk_tokens,
)
from base.diff_utils.str_diff import (
    AddedSpan,
    DeletedSpan,
    ModSpan,
    NoopSpan,
    precise_line_diff,
)
from base.languages.language_guesser import guess_language
from base.languages.languages import LanguageId
from base.prompt_format.common import PromptChunk, TokenList
from base.prompt_format.util import (
    CascadeTruncatingStrategy,
    head_n,
    join_tokens,
    trailing_n,
)
from base.prompt_format_next_edit.common import (
    NextEditFormatterConfig,
    NextEditPromptInput,
)
from base.ranges.line_map import LineMap
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.retrieval.chunking.smart_chunking import LineChunkContents, SmartChunker
from base.tokenizers import Tokenizer
from base.tokenizers.tokenizer import NextEditGenSpecialTokens

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class EditGenPromptInput(NextEditPromptInput):
    """Input to the EditGenPromptFormatter."""

    retrieval_chunks: Sequence[PromptChunk]
    """The retrieved chunks, sorted from high to low relevancy."""


@dataclass
class EditGenPromptTiming:
    """Timings for steps of the EditGenPromptFormatter."""

    tokenize_s: float = 0.0
    detokenize_s: float = 0.0
    format_diffs_s: float = 0.0
    other_s: float = 0.0


@dataclass
class EditGenPromptOutput:
    """Output of the EditGenPromptFormatter."""

    tokens: TokenList
    """The tokenized prompt."""

    diff_tokens: TokenList
    """The tokenized diff section.

    This is used for logging and analysis.
    """

    timing: EditGenPromptTiming
    """Timing information for the formatting process."""


@dataclass
class EditGenOutput:
    """The output of edit generation.

    Includes everything we want the model to predict.
    """

    replacement: str
    """The new code to replace the selected region."""

    changed: bool
    """Whether the updated code is different from the selected code."""


@dataclass
class DecodedEditGenOutput(EditGenOutput):
    truncation_char: int | None
    """The character offset in `replacement` where the truncation happens.

    This is None if the model output is not truncated.
    """


class PromptSectionName(str, Enum):
    INSTRUCTION = "instruction"
    FILENAME = "filename"
    RETRIEVAL = "retrieval"
    DIFF = "diff"
    SELECTION = "selection"


default_prompt_section_order = (
    PromptSectionName.DIFF,
    PromptSectionName.INSTRUCTION,
    PromptSectionName.RETRIEVAL,
    PromptSectionName.FILENAME,
    PromptSectionName.SELECTION,
)
"""A default order of prompt sections that is cache-friendly."""


class SectionBudgetsDict(TypedDict):
    """The token budgets for different prompt sections.

    Note that latter sections have higher priorities when running out of tokens.
    """

    suffix_tks: int
    prefix_tks: int
    diff_tks: int
    filename_tks: int
    instruction_tks: int
    retrieval_tks: int


def default_section_budgets() -> SectionBudgetsDict:
    """Default token budgets for prompt sections.

    Total budget: 6450.
    Prefix + suffix: 2600.
    """
    return SectionBudgetsDict(
        suffix_tks=800,
        prefix_tks=1800,
        diff_tks=2400,
        filename_tks=50,
        instruction_tks=200,
        retrieval_tks=1200,
    )


def section_budgets_10k() -> SectionBudgetsDict:
    """Default token budgets for prompt sections.

    Total budget: 10,000.
    Prefix + suffix: 4000.
    """
    return SectionBudgetsDict(
        suffix_tks=1200,
        prefix_tks=2800,
        diff_tks=3700,
        filename_tks=100,
        instruction_tks=200,
        retrieval_tks=2000,
    )


def section_budgets_13k() -> SectionBudgetsDict:
    """Default token budgets for prompt sections.

    Total budget: 13,000.
    Prefix + suffix: 5000.
    """
    return SectionBudgetsDict(
        suffix_tks=1600,
        prefix_tks=3400,
        diff_tks=5000,
        filename_tks=100,
        instruction_tks=400,
        retrieval_tks=2500,
    )


def section_budgets_reranker() -> SectionBudgetsDict:
    """Default token budgets for prompt sections.

    Total budget: 5300.
    Prefix + suffix: 3000.
    """
    return SectionBudgetsDict(
        suffix_tks=1000,
        prefix_tks=2000,
        diff_tks=2000,
        filename_tks=100,
        instruction_tks=200,
        retrieval_tks=0,
    )


def default_diff_filter(path: Path) -> bool:
    """Filter out diffs coming from certain file paths."""
    lang = guess_language(str(path))
    # Filter out notebook diffs since they contain more than user code
    return lang is not None and path.suffix != ".ipynb"


MASKED_ZERO_TOKEN = -(2**31)


def mask_token(token_id: int) -> int:
    """Encode a masked token id as a negative value."""
    if token_id == 0:
        return MASKED_ZERO_TOKEN
    else:
        return -token_id


@dataclass_json
@dataclass
class EditGenFormatterConfig(NextEditFormatterConfig):
    """The configurable parameters for the EditGenPromptFormatter."""

    use_diff_based_output: bool = True
    """Whether the model uses the diff-based output format.

    If False, the model directly outputs the replacement text.
    Note that when this is True, the selected area will be annotated with
    (relative) line numbers.
    """

    diff_chunk_size_chars: int | None = 800
    """The maximal chunk size (in characters) for the diff-based output format.

    Pause tokens will be inserted between the diff chunks.
    Note that this parameter is only used at training time.
    """

    output_instruction: bool = True
    """Whether to use a prompt format that supports generating the instruction.

    Checkpoints trained with PromptConfig.VERSION <= 1.7 should disable this.
    """

    add_loss_mask: bool = False
    """If true, will use negative values for tokens whose loss should be masked.

    This should only be turned on when generating training data.
    """

    section_order: tuple[PromptSectionName, ...] = default_prompt_section_order

    section_budgets: SectionBudgetsDict = field(default_factory=default_section_budgets)
    """The token budgets for different input prompt sections."""

    filter_retrieval_overlap_ratio: float = 0.9
    """Threshold of overlap ratio between retrieved chunks and diff hunks.

    If the overlap ratio is greater than this threshold, the chunk will be filtered out.
    """

    truncate_instructions_tail: bool = True
    """If true, truncate from the tail of instructions."""


@dataclass
class EditGenPromptFormatter:
    """Prompt formatter for edit generation."""

    tokenizer: Tokenizer
    """The tokenizer to use, must have NextEditSpecialTokens."""

    config: EditGenFormatterConfig = field(default_factory=EditGenFormatterConfig)
    """The configurable parameters for the EditGenPromptFormatter."""

    diff_filter: Callable[[Path], bool] = default_diff_filter
    """Filter out the diffs coming from certain file paths."""

    def __post_init__(self):
        special_tokens = self.tokenizer.special_tokens
        if not isinstance(special_tokens, NextEditGenSpecialTokens):
            raise ValueError(
                f"Expected NextEditGenSpecialTokens, but got {type(special_tokens)}. "
                f"The provided tokenizer type is {type(self.tokenizer)}"
            )
        self.special_tokens = special_tokens

        budgets = cast(dict[str, int], self.config.section_budgets)
        sections_total = sum(budgets.values())
        # we reserve 100 tokens for fixed texts like `[[Begin Replacement]]`.
        self._reserved_tokens: int = 100
        limit = self.config.max_prompt_tokens - self._reserved_tokens
        if sections_total > limit:
            raise ValueError(f"{sections_total=} > {limit=}.")

    def format_input_prompt(self, input: EditGenPromptInput) -> EditGenPromptOutput:  # noqa
        """Format the input into a token sequence.

        The prompt with default section order follows the following format:
        (Whitespaces are explicitly shown as escapes)
        ```
        <|diff_section|>\n
        {diff hunks formatted with `format_file_changes`}\n
        <|instruction|> {instruction} <pause>\n
        <|retrieval_section|>\n
        {retrieval chunks formatted with `format_retrieval_chunks`}\n
        <filename> {current_path}\n
        <fim_prefix>
        {current code before edit_region}
        [[To Replace]]\n
        {current code in edit_region}\n
        [[Begin Replacement]]\n
        <fim-suffix>\n
        [[End Replacement]]\n
        {current code after edit_region}\n
        <fim-middle>
        ```

        See also: `format_retrieval_chunks` and `format_file_changes`.
        """
        config = self.config

        def mask_tks(prompt: TokenList) -> TokenList:
            """If self.add_loss_mask is True, negate the prompt tokens."""
            if config.add_loss_mask:
                return [mask_token(x) for x in prompt]
            else:
                return prompt

        def mask_tk(tk: int) -> int:
            """If self.add_loss_mask is True, negate the token."""
            if config.add_loss_mask:
                return mask_token(tk)
            else:
                return tk

        tkn = self.tokenizer
        pause_id = self.special_tokens.pause
        newline_id = self.special_tokens.newline

        section_budgets = config.section_budgets

        prev_time = time.time()
        timings = EditGenPromptTiming()

        instruction_tks = tkn.tokenize_safe(input.instruction)
        next_time = time.time()
        timings.tokenize_s += next_time - prev_time
        prev_time = next_time

        if config.output_instruction:
            instruction_tks.append(pause_id)
        # We truncate the instruction using a static
        # limit to avoid leaking any length hint information since we are teaching
        # the model to generate it.
        if config.truncate_instructions_tail:
            instruction_tks = head_n(
                instruction_tks, section_budgets["instruction_tks"]
            )
        else:
            instruction_tks = trailing_n(
                instruction_tks, section_budgets["instruction_tks"]
            )
        next_time = time.time()
        timings.other_s += next_time - prev_time
        prev_time = next_time

        current_path_tks = tkn.tokenize_safe(input.current_path)
        next_time = time.time()
        timings.tokenize_s += next_time - prev_time
        prev_time = next_time

        prefix, suffix = self.format_selected_code(
            input.current_code,
            input.edit_region,
            use_diff_based_output=config.use_diff_based_output,
        )
        next_time = time.time()
        timings.other_s += next_time - prev_time
        prev_time = next_time

        prefix_tks = tkn.tokenize_safe(prefix)
        suffix_tks = tkn.tokenize_safe(suffix)
        next_time = time.time()
        timings.tokenize_s += next_time - prev_time
        prev_time = next_time

        # Truncate prefix and suffix separately because they are not cache-friendly and
        # we thus don't want them to grow too large when the other sections are small.
        prefix_suffix_sizes = CascadeTruncatingStrategy(
            total_budget=section_budgets["prefix_tks"] + section_budgets["suffix_tks"],
            section_budgets={
                "suffix_tks": section_budgets["suffix_tks"],
                "prefix_tks": section_budgets["prefix_tks"],
            },
        ).compute_section_sizes(
            {
                # truncate suffix before truncating prefix
                "suffix_tks": len(suffix_tks),
                "prefix_tks": len(prefix_tks),
            }
        )
        prefix_tks = trailing_n(prefix_tks, prefix_suffix_sizes["prefix_tks"])
        suffix_tks = head_n(suffix_tks, prefix_suffix_sizes["suffix_tks"])

        # Determine the retrieval section budget
        retrieval_size = config.section_budgets["retrieval_tks"]
        if not input.retrieval_chunks:
            # as a special case, we don't reserve for retrieval if there are no chunks
            retrieval_size = 0

        # Then use the remaining budget to truncate other sections.
        # (We truncate the retrieval section later because we want to filter out
        # chunks that fully appear in the prefix or suffix, so we cannot truncate
        # the retrieval section along with other sections.)
        remaining_budget = (
            config.max_prompt_tokens
            - self._reserved_tokens
            - retrieval_size
            - section_budgets["instruction_tks"]
            - len(prefix_tks)
            - len(suffix_tks)
        )
        next_time = time.time()
        timings.other_s += next_time - prev_time
        prev_time = next_time

        all_diff_hunks = format_file_changes_with_ranges(
            input.recent_changes,
            diff_context_lines=config.diff_context_lines,
            diff_filter=self.diff_filter,
            use_smart_header=config.use_smart_header_diff,
        )
        next_time = time.time()
        timings.format_diffs_s += next_time - prev_time
        prev_time = next_time

        t_hunks = tokenize_diff_hunks(
            all_diff_hunks,
            # Use an overestimate of the budget here -- we'll get an updated budget and
            # truncate it later. This is an optimization step to reduce the number of
            # hunks we tokenize.
            remaining_budget,
            tkn,
            deduplicate_identical_paths=config.filter_duplicated_file_paths,
        )
        next_time = time.time()
        timings.tokenize_s += next_time - prev_time
        prev_time = next_time

        remaining_sizes = CascadeTruncatingStrategy(
            total_budget=remaining_budget,
            section_budgets={
                "diff_tks": section_budgets["diff_tks"],
                "filename_tks": section_budgets["filename_tks"],
            },
        ).compute_section_sizes(
            {
                "diff_tks": sum(hunk.num_tokens() for hunk in t_hunks),
                "filename_tks": len(current_path_tks),
            }
        )

        current_path_tks = trailing_n(current_path_tks, remaining_sizes["filename_tks"])
        t_hunks = truncate_diff_hunk_tokens(t_hunks, remaining_sizes["diff_tks"])
        diff_tks = format_tokenized_hunks(t_hunks)
        next_time = time.time()
        timings.other_s += next_time - prev_time
        prev_time = next_time

        # Now filter, format, and truncate the retrieval chunks.
        # We filter out a chunk if it (1) overlaps with the selected region,
        # (2) appears fully in the prefix, or (3) appears fully in the suffix.
        truncated_prompt = tkn.detokenize(prefix_tks + suffix_tks)
        next_time = time.time()
        timings.detokenize_s += next_time - prev_time
        prev_time = next_time

        bad_texts = (
            self.BEGIN_REPLACEMENT,
            self.END_REPLACEMENT,
            self.BEGIN_SELECTION,
        )
        filtered_chunks = list[PromptChunk]()
        for chunk in input.retrieval_chunks:
            if (chunk.path == input.current_path) and (
                chunk.crange.intersect(input.edit_region) is not None
                or chunk.text in truncated_prompt
            ):
                continue
            # Filter out any chunk that mostly appears in the diff.
            if any(
                h.hunk.after_path == chunk.path
                and h.hunk.after_crange is not None
                and containment_fraction(outer=h.hunk.after_crange, inner=chunk.crange)
                >= config.filter_retrieval_overlap_ratio
                for h in t_hunks
            ):
                continue
            # Filter out any chunk containing bad texts.
            if any(bad_text in chunk.text for bad_text in bad_texts):
                continue
            filtered_chunks.append(chunk)
        next_time = time.time()
        timings.other_s += next_time - prev_time
        prev_time = next_time

        retrieval_tks = self.format_retrieval_chunks(
            self.tokenizer, self.special_tokens, filtered_chunks
        )
        next_time = time.time()
        timings.tokenize_s += next_time - prev_time
        prev_time = next_time

        retrieval_tks = head_n(retrieval_tks, retrieval_size)
        has_instruction = bool(input.instruction.strip())
        if config.output_instruction and not has_instruction:
            # we don't add the instruction section in this case to avoid teaching
            # the model to output empty instructions
            instruction_section = []
        else:
            if not config.output_instruction or not diff_tks:
                # when there's no diff, don't teach the model to generate instruction
                instruction_tks = mask_tks(instruction_tks)
            instruction_section = [
                mask_tk(self.special_tokens.instruction),
                *instruction_tks,
                mask_tk(newline_id),
            ]

        section_tokens: dict[PromptSectionName, TokenList] = {
            PromptSectionName.INSTRUCTION: instruction_section,
            PromptSectionName.FILENAME: mask_tks(
                [
                    self.special_tokens.filename,
                    *current_path_tks,
                    newline_id,
                ]
            ),
            PromptSectionName.RETRIEVAL: mask_tks(
                [
                    self.special_tokens.retrieval_section,
                    newline_id,
                    *retrieval_tks,
                    newline_id,
                ]
            ),
            PromptSectionName.DIFF: mask_tks(
                [
                    self.special_tokens.diff_section,
                    newline_id,
                    *diff_tks,
                    newline_id,
                ]
            ),
            PromptSectionName.SELECTION: mask_tks(
                [
                    self.special_tokens.fim_prefix,
                    *prefix_tks,
                    self.special_tokens.fim_suffix,
                    *suffix_tks,
                    newline_id,
                    self.special_tokens.fim_middle,
                ]
            ),
        }

        input_prompt = TokenList()
        for section_name in config.section_order:
            input_prompt += section_tokens[section_name]
            assert (
                len(input_prompt) <= config.max_prompt_tokens
            ), f"{len(input_prompt)=} > {config.max_prompt_tokens=} after {section_name=}"
        next_time = time.time()
        timings.other_s += next_time - prev_time
        prev_time = next_time

        return EditGenPromptOutput(input_prompt, diff_tokens=diff_tks, timing=timings)

    @classmethod
    def format_selected_code(
        cls,
        current_code: str,
        edit_region: CharRange,
        use_diff_based_output: bool,
    ) -> tuple[str, str]:
        """Format the selected code as a prefix and suffix string."""
        before_selected = current_code[: edit_region.start]
        selected = current_code[edit_region.to_slice()]
        if use_diff_based_output:
            selected = encode_model_diff_input(selected)
        after_selected = current_code[edit_region.stop :]
        prefix = (
            f"{before_selected}{cls.BEGIN_SELECTION}\n"
            f"{selected}\n{cls.BEGIN_REPLACEMENT}\n"
        )
        suffix = f"\n{cls.END_REPLACEMENT}\n{after_selected}"
        return prefix, suffix

    BEGIN_SELECTION = "[[To Replace]]"
    BEGIN_REPLACEMENT = "[[Begin Replacement]]"
    END_REPLACEMENT = "[[End Replacement]]"

    @classmethod
    def format_retrieval_chunks(
        cls,
        tokenizer: Tokenizer,
        special_tokens: NextEditGenSpecialTokens,
        chunks: Sequence[PromptChunk],
    ) -> TokenList:
        """Format each retrieval chunk as follows:
        (Newlines are explicitly shown as escapes)
        ```
        <|ret-start|>
        {chunk.path}\n
        {chunk.header}
        <|ret-body|>
        {chunk.text}\n
        ```
        """
        prompt = TokenList()
        newline_id = tokenizer.tokenize_safe("\n")[0]
        for chunk in chunks:
            chunk_prompt: TokenList = [
                special_tokens.ret_start,
                *tokenizer.tokenize_safe(chunk.path),
                newline_id,
                *tokenizer.tokenize_safe(chunk.header),
                # non-empty header already ends with a newline, so no need to add here
                special_tokens.ret_body,
                *tokenizer.tokenize_safe(chunk.text),
                newline_id,
            ]
            prompt += chunk_prompt
        return prompt

    def format_output_prompt(
        self,
        selected_code: str,
        output: EditGenOutput,
        lang: LanguageId | None,
    ) -> TokenList:
        """Format the output into a token sequence."""
        tkn = self.tokenizer
        no_change_id = self.special_tokens.no_change
        eod_id = self.special_tokens.eos

        if not output.changed:
            return [no_change_id, eod_id]

        if self.config.use_diff_based_output:
            # The model should output the diff
            diff_chunks = encode_model_diff_output(
                selected_code,
                output.replacement,
                max_pause_chars=self.config.diff_chunk_size_chars,
                lang=lang,
            )
            output_tokens = join_tokens(
                (tkn.tokenize_safe(chunk) for chunk in diff_chunks),
                [self.special_tokens.pause],
            )
        else:
            # The model should output the replacement text directly
            output_tokens = tkn.tokenize_safe(output.replacement)

        return [self.special_tokens.has_change, *output_tokens, eod_id]

    def decode_output_tokens(
        self,
        output_tokens: TokenList,
        selected_code: str,
    ) -> DecodedEditGenOutput:
        """Decode the model output into an EditGenOutput.

        This method will raise a warning if the last output token is not EOD, unless
        only one token is output (in which case we assume it is a reranking request).
        """
        tkn = self.tokenizer
        truncated = False
        truncation_char = None
        # remove all pause tokens
        pause_id = self.special_tokens.pause
        output_tokens = [token for token in output_tokens if token != pause_id]
        # now check if the output is truncated
        if output_tokens[-1] == self.special_tokens.eos:
            output_tokens = output_tokens[:-1]
        elif len(output_tokens) == 1:
            # assuming this is for reranking request
            pass
        else:
            # Last token is not EOD, diffs are likely truncated.
            truncated = True

        if output_tokens[0] == self.special_tokens.has_change:
            if not self.config.use_diff_based_output:
                replacement = tkn.detokenize(output_tokens[1:])
                if truncated:
                    truncation_char = len(replacement)
                return DecodedEditGenOutput(
                    replacement=replacement,
                    changed=True,
                    truncation_char=truncation_char,
                )
            # convert diff into replacement text
            diff_str = tkn.detokenize(output_tokens[1:])
            if truncated:
                diff_str = cleanup_truncated_model_diff(diff_str)
            input_str = encode_model_diff_input(selected_code)
            decoded = decode_model_diff(input_str, diff_str)
            if truncated:
                truncation_char = decoded.last_changed_char_in_after
            return DecodedEditGenOutput(
                replacement=decoded.after,
                changed=True,
                truncation_char=truncation_char,
            )
        else:
            if output_tokens[0] != self.special_tokens.no_change:
                logger.error(f"First token (id={output_tokens[0]}) is not NO_CHANGE")
            return DecodedEditGenOutput(
                replacement=selected_code,
                changed=False,
                truncation_char=None,
            )


def cleanup_truncated_model_diff(diff_str: str) -> str:
    """Cleanup a truncated model diff string.

    This is done by removing the last line that doesn't end with a newline.
    """
    if diff_str.endswith("\n"):
        return diff_str
    truncated_str = diff_str[: diff_str.rfind("\n") + 1]
    n_removed = len(diff_str) - len(truncated_str)
    logger.warning(
        f"Truncating diff string to the last newline, removed {n_removed} chars"
    )
    return truncated_str


_StrOrNone = TypeVar("_StrOrNone", str, None)


@dataclass(frozen=True)
class DecodedChange(Modified[str]):
    last_changed_char_in_after: int


MODEL_DIFF_INPUT_PATTERN = re.compile(r"(\s*\d+)\|(.*)", re.DOTALL)
MODEL_DIFF_OUTPUT_PATTERN = re.compile(r"(\s*\d+|\s+)([\+\-\|].*)", re.DOTALL)


def decode_model_diff(input_str: str, diff_str: str) -> DecodedChange:
    """Decode a string modification from an input string and a diff string.

    The input to this function should be the output of `encode_model_diff`.

    Args:
        input_str: The input string.
        diff_str: The diff string.

    Returns:
        The decoded change.
    """
    # remove the line numbers to form the `before_text`
    input_lines = list[str]()
    for line in input_str.splitlines(keepends=True):
        match = MODEL_DIFF_INPUT_PATTERN.match(line)
        if match is None:
            raise ValueError(f"Invalid input line: {repr(line)}")
        input_lines.append(match.group(2))
    before_text = "".join(input_lines)

    after_line_lists = [[line] for line in input_lines]
    # need an empty line 0 since line numbers are 1-based
    after_line_lists.insert(0, [])
    last_touched_line = 0
    # we then modify the line lists above according to the diff
    for line in diff_str.splitlines(keepends=True):
        match = MODEL_DIFF_OUTPUT_PATTERN.match(line)
        if match is None:
            logger.warning(f"Invalid diff line: {line=}\n{diff_str}")
            break
        line_num_part = match.group(1)
        if line_num_part.isspace():
            # an empty line part means this applies to the same as the previous line
            line_num = last_touched_line
        else:
            line_num = int(line_num_part)
        if not (0 <= line_num < len(after_line_lists)):
            logger.warning(f"Invalid diff line: {line=}\n{diff_str}")
            break
        line_content = match.group(2)
        if line_content.startswith("-") and after_line_lists[line_num]:
            after_line_lists[line_num].pop()
        elif line_content.startswith("+"):
            after_line_lists[line_num].append(line_content[1:])
        elif line_content.startswith("|") and after_line_lists[line_num]:
            after_line_lists[line_num].pop()
            after_line_lists[line_num].append(line_content[1:])
        else:
            logger.warning(f"Invalid diff line: {line=}\n{diff_str}")
            break
        last_touched_line = line_num

    last_changed_char_in_after = sum(
        len(line)
        for lines in after_line_lists[: last_touched_line + 1]
        for line in lines
    )

    # flatten the line lists to get the after_text
    after_text = "".join(line for line_list in after_line_lists for line in line_list)
    return DecodedChange(before_text, after_text, last_changed_char_in_after)


def encode_model_diff_input(before: str) -> str:
    """Annotate `before` with line numbers.

    For example, if we have the following before

    ```
    line1
    line2
    line3
    line4  # comment
    line5
    ```

    Then the result will be

    ```
    1|line1
    2|line2
    3|line3
    4|line4  # comment
    5|line5
    ```
    """
    before_lines = before.splitlines(keepends=True)
    # number of digits to use for showing line numbers
    digits = 1 if not before_lines else 1 + math.floor(math.log10(len(before_lines)))

    return "".join(f"{i+1:{digits}}|{line}" for i, line in enumerate(before_lines))


def _encode_model_diff_output_lines(
    before: str,
    after: str,
) -> list[tuple[str, LineRange, LineRange]]:
    """Encode a string change as a list of (diff_line, before_range, after_range).

    The diff lines, when joined together, can be applied to `before` via\
    the `decode_model_diff` function to reconstruct `after`.

    For example, if we have before:

    ```
    line1
    line2
    line3
    line4  # to delete
    line5
    ```

    and after:

    ```
    prepended
    line1
    modified
    line3
    line5
    appended
    ```

    then the encoded diff string will be

    ```
    0+prepended
    2|modified
    4-
    5+appended
    ```

    Which indicates that we should append "prepended" at input line 0, modify line2 into modified,
    delete line 4, and append "appended" at line 5. Note that the first line in the
    input is marked as line 1, and line 0 is a special line number used to prepend a
    line before line 1.

    We only use | to indicate single-line modifications. If there are multiple
    before or after lines in a ModSpan, our line-diff algorithm was unable to determine which before lines map to
    which after lines. In this case, implicitly imposing a mapping with vertical bars does not make sense,
    and we leave the region as a combination of additions and deletions. See example below:

    after:
    ```
    prepended
    line1
    modified
    modified
    line5
    appended
    ```

    Assuming the line diff algorithm cannot decide the mapping and gives us a multi-line modification span, the
    encoded diff string will be:


    ```
    0+prepended
    2-
    3-
     +modified
     +modified
    4-
    5+appended
    ```
    """

    before_lines = before.splitlines(keepends=True)
    # number of digits to use for showing line numbers
    digits = 1 if not before_lines else 1 + math.floor(math.log10(len(before_lines)))

    str_diff = precise_line_diff(before, after)

    outputs = list[tuple[str, LineRange, LineRange]]()
    prev_line = -1

    def line_marker(n: int) -> str:
        if n == prev_line:
            return " " * digits
        else:
            return f"{n:{digits}}"

    def record_deletion(start_line: int, stop_line: int, after_start_line: int):
        nonlocal prev_line
        after_lrange = LineRange(after_start_line, after_start_line)
        for i in range(start_line, stop_line):
            before_lrange = LineRange(i, i + 1)
            outputs.append((f"{line_marker(i+1)}-\n", before_lrange, after_lrange))
            prev_line = i + 1

    def record_addition(start_line: int, added: str, after_start_line: int):
        nonlocal prev_line
        inserted_lines = added.splitlines(keepends=True)
        before_lrange = LineRange(start_line, start_line)
        for i, line in enumerate(inserted_lines):
            after_lrange = LineRange(after_start_line + i, after_start_line + i + 1)
            out = (f"{line_marker(start_line)}+{line}", before_lrange, after_lrange)
            outputs.append(out)
            prev_line = start_line

    def record_modification(
        after_text: str,
        before_start_line: int,
        before_stop_line: int,
        after_start_line: int,
        after_stop_line: int,
    ):
        before_lrange = LineRange(before_start_line, before_stop_line)
        after_lrange = LineRange(after_start_line, after_stop_line)

        # special case with a bar for single-line modifications
        if len(before_lrange) == 1 and len(after_lrange) == 1:
            nonlocal prev_line
            outputs.append(
                (
                    f"{line_marker(before_lrange.stop)}|{after_text}",
                    before_lrange,
                    after_lrange,
                )
            )
            prev_line = before_lrange.stop
            return

        # fall back to deletion + addition in the case of multi-line modifications
        record_deletion(before_start_line, before_stop_line, after_start_line)
        record_addition(before_stop_line, after_text, after_start_line)

    before_lmap = LineMap(before)
    after_lmap = LineMap(after)

    for (before_range, after_range), span in zip(
        str_diff.span_ranges(), str_diff.spans
    ):
        before_start_line = before_lmap.get_line_number(before_range.start)
        before_stop_line = before_lmap.get_line_number(before_range.stop)
        after_start_line = after_lmap.get_line_number(after_range.start)
        after_stop_line = after_lmap.get_line_number(after_range.stop)

        if isinstance(span, NoopSpan):
            continue
        elif isinstance(span, AddedSpan):
            record_addition(before_start_line, span.after, after_start_line)
        elif isinstance(span, DeletedSpan):
            record_deletion(before_start_line, before_stop_line, after_start_line)
        elif isinstance(span, ModSpan):
            record_modification(
                span.after,
                before_start_line,
                before_stop_line,
                after_start_line,
                after_stop_line,
            )
        else:
            assert_never(span)

    return outputs


def encode_model_diff_output(
    before: str,
    after: str,
    max_pause_chars: int | None = None,
    lang: LanguageId | None = None,
) -> list[str]:
    """Encode a string modification as a list of diff strings, such that

    1. Each diff string is at most `max_pause_chars` long (if specified).
    2. The returned diff strings, when joined together, can be applied to `before` via\
    the `decode_model_diff` function to reconstruct `after`.

    See `_encode_model_diff_output_lines` for more details about the diff format.

    ### How we break large diffs into smaller ones
    (which is used to teach the model to output pause tokens)
    1. We break `after` down into multiple smart chunks, starting from the first
    changed line in `after`. Each smart chunk is less than `max_pause_chars` long.
    2. We then group together all diff lines whose after range is inside the same
    smart chunk.
    3. Finally, we opportunistically merge adjacent groups if their total size is still
    less than `max_pause_chars`.
    """
    output_lines = _encode_model_diff_output_lines(before, after)
    if not output_lines:
        return []

    # compute smart chunks in `after`, starting from the first changed line
    first_changed_line_in_after = output_lines[0][2].start
    after_lines = after.splitlines(keepends=True)
    changed_after = "".join(after_lines[first_changed_line_in_after:])
    after_chunks: list[LineChunkContents] = []
    if max_pause_chars is not None:
        after_chunks = SmartChunker(
            max_chunk_chars=max_pause_chars, max_headers=0
        ).split_chunks(changed_after, lang)
    if not after_chunks:
        # need at least one chunk for the code below to work
        after_chunks = [
            LineChunkContents(changed_after, 0, 0, len(changed_after.splitlines()))
        ]

    # the list of pause spans in the output
    pause_spans = list[str]()
    # the output lines for the current pause span
    pause_span_lines = list[str]()

    current_chunk_id = 0
    for diff_line, _, after_lrange in output_lines:
        # because the offsets in the smart chunks are relative to the first changed
        # line, we adjust diff_line's stop accordingly
        line_stop = after_lrange.stop - first_changed_line_in_after
        chunk_stop = after_chunks[current_chunk_id].lrange().stop
        if max_pause_chars and line_stop > chunk_stop:
            # end the current pause span
            pause_span_diff = "".join(pause_span_lines)
            if pause_span_diff:
                pause_spans.append(pause_span_diff)
            pause_span_lines = []
            # find the next output chunk
            for i in range(current_chunk_id + 1, len(after_chunks)):
                if after_chunks[i].lrange().stop >= line_stop:
                    current_chunk_id = i
                    break

        pause_span_lines.append(diff_line)

    if pause_span_lines:
        pause_spans.append("".join(pause_span_lines))

    def compute_diff_size(diff_str: str) -> int:
        return len(diff_str.replace(" ", ""))

    # merge pause_spans opportunistically
    merged_spans = list[str]()
    for span in pause_spans:
        if not span:
            continue
        if (
            merged_spans
            and max_pause_chars
            and compute_diff_size(merged_spans[-1]) + compute_diff_size(span)
            <= max_pause_chars
        ):
            merged_spans[-1] += span
        else:
            merged_spans.append(span)

    return merged_spans


def fix_truncation_newline(decoded: DecodedEditGenOutput) -> DecodedEditGenOutput:
    """Insert a newline to the truncation site if it's missing one."""
    replacement = decoded.replacement
    truncation_char = decoded.truncation_char
    if truncation_char is None:
        # no truncation, no need to fix
        return decoded
    if truncation_char > 0 and replacement[truncation_char - 1] == "\n":
        # there's already a newline at the truncation site, so no need to fix
        return decoded
    logger.warning("Truncation site is missing a newline, fixing it.")
    replacement = replacement[:truncation_char] + "\n" + replacement[truncation_char:]
    return DecodedEditGenOutput(
        replacement=replacement,
        truncation_char=truncation_char,
        changed=decoded.changed,
    )


def equal_modulo_spaces(text1: str, text2: str) -> bool:
    """Return True if the two strings are equal except for whitespaces."""
    text1 = re.sub(r"\s+", "", text1)
    text2 = re.sub(r"\s+", "", text2)
    return text1 == text2


def containment_fraction(outer: CharRange, inner: CharRange) -> float:
    """Compute the fraction of inner contained in outer."""
    if not (overlap := outer.intersect(inner)):
        return 0.0
    return len(overlap) / len(inner)
