import pytest

from base.diff_utils.changes import Modified
from base.diff_utils.diff_formatter import File
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
    NextEditLocationQueryFormatter,
)
from base.ranges.range_types import Char<PERSON><PERSON><PERSON>
from base.test_utils.testing_utils import assert_str_eq
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import (
    NextEditGenSpecialTokens,
    RetrievalSpecialTokens,
    Tokenizer,
)

OLD_CODE = """\
def ex_func1(a,b):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b
"""

NEW_CODE = """\
def ex_func1(a: int, b: int):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b

def new_func():
    return 1
"""


@pytest.mark.parametrize(
    "tokenizer_name",
    [
        "starcoder",
    ],
)
class TestNextEditLocationQueryPromptFormatter:
    @pytest.fixture
    def valid_input(self) -> LocalizationNextEditPromptInput:
        return LocalizationNextEditPromptInput(
            current_file=File("src/examples.py", NEW_CODE),
            edit_region=CharRange(
                NEW_CODE.index("def ex_func2(a,b):\n"),
                NEW_CODE.index("def ex_func3(a,b):\n"),
            ),
            instruction="fix bugs",
            recent_changes=[
                Modified(
                    before=File(path="src/examples.py", contents=OLD_CODE),
                    after=File(path="src/examples.py", contents=NEW_CODE),
                )
            ],
        )

    @pytest.fixture
    def valid_input_long_instruction(self) -> LocalizationNextEditPromptInput:
        return LocalizationNextEditPromptInput(
            current_file=File("src/examples.py", NEW_CODE),
            edit_region=CharRange(
                NEW_CODE.index("def ex_func2(a,b):\n"),
                NEW_CODE.index("def ex_func3(a,b):\n"),
            ),
            instruction="fix bugs or do something else? many options",
            recent_changes=[
                Modified(
                    before=File(path="src/examples.py", contents=OLD_CODE),
                    after=File(path="src/examples.py", contents=NEW_CODE),
                )
            ],
        )

    @pytest.fixture
    def valid_input_long_instruction_with_cmd_output(
        self,
    ) -> LocalizationNextEditPromptInput:
        return LocalizationNextEditPromptInput(
            current_file=File("src/examples.py", NEW_CODE),
            edit_region=CharRange(
                NEW_CODE.index("def ex_func2(a,b):\n"),
                NEW_CODE.index("def ex_func3(a,b):\n"),
            ),
            instruction="fix bugs or do something else? many options",
            recent_changes=[
                Modified(
                    before=File(path="src/examples.py", contents=OLD_CODE),
                    after=File(path="src/examples.py", contents=NEW_CODE),
                )
            ],
            command_output="here are some failures-pytest blah blah pytest",
        )

    @pytest.fixture
    def tokenizer(self, tokenizer_name: str):
        return create_tokenizer_by_name(tokenizer_name)

    def test_post_init_validates_section_budgets(self, tokenizer: Tokenizer):
        with pytest.raises(ValueError):
            NextEditLocationQueryFormatter(
                tokenizer=tokenizer,
                config=NextEditLocationQueryFormatter.Config(
                    max_prompt_tokens=5,
                    max_instruction_tokens=5,
                ),
            )

    def test_format_prompt_default_budget(
        self, valid_input: LocalizationNextEditPromptInput, tokenizer: Tokenizer
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )

        expected_tokens = [
            *tokenizer.tokenize_safe("fix bugs"),
            special_tokens.instruction,
            *tokenizer.tokenize_safe("""\
--- src/examples.py
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
@@ -8,1 +8,4 @@ def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            NextEditLocationQueryFormatter(
                tokenizer=tokenizer,
                config=NextEditLocationQueryFormatter.Config(
                    diff_context_lines=1,
                ),
            )
            .format_prompt(valid_input)
            .tokens()
        )

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."

    def test_format_prompt_short_budget(
        self, valid_input: LocalizationNextEditPromptInput, tokenizer: Tokenizer
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )

        expected_tokens = [
            *tokenizer.tokenize_safe("fix"),
            special_tokens.instruction,
            *tokenizer.tokenize_safe("""\
--- src/examples.py
+++ src/examples.py
@@ -8,1 +8,4 @@ def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            NextEditLocationQueryFormatter(
                tokenizer=tokenizer,
                config=NextEditLocationQueryFormatter.Config(
                    # The full prompt fits in 171 tokens.
                    max_prompt_tokens=70,
                    max_instruction_tokens=1,
                    diff_context_lines=1,
                ),
            )
            .format_prompt(valid_input)
            .tokens()
        )
        print(actual_tokens)
        print(len(actual_tokens))

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."

    def test_format_prompt_diff_budget(
        self, valid_input: LocalizationNextEditPromptInput, tokenizer: Tokenizer
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )

        expected_tokens = [
            *tokenizer.tokenize_safe("fix")[:1],
            special_tokens.instruction,
            *tokenizer.tokenize_safe("""\
--- src/examples.py
+++ src/examples.py
@@ -8,1 +8,4 @@ def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            NextEditLocationQueryFormatter(
                tokenizer=tokenizer,
                config=NextEditLocationQueryFormatter.Config(
                    max_prompt_tokens=150,
                    max_instruction_tokens=1,
                    diff_context_lines=1,
                    max_diff_tokens=70,
                ),
            )
            .format_prompt(valid_input)
            .tokens()
        )

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."

    def test_long_instruction(
        self,
        valid_input_long_instruction: LocalizationNextEditPromptInput,
        tokenizer: Tokenizer,
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )
        expected_tokens = [
            *tokenizer.tokenize_safe("fix bugs or do something else? many options")[:2],
            special_tokens.instruction,
            *tokenizer.tokenize_safe("""\
--- src/examples.py
+++ src/examples.py
@@ -8,1 +8,4 @@ def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            NextEditLocationQueryFormatter(
                tokenizer=tokenizer,
                config=NextEditLocationQueryFormatter.Config(
                    max_prompt_tokens=150,
                    max_instruction_tokens=2,
                    diff_context_lines=1,
                    max_diff_tokens=70,
                ),
            )
            .format_prompt(valid_input_long_instruction)
            .tokens()
        )

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."

    def test_truncate_instructions_tail(
        self,
        valid_input_long_instruction: LocalizationNextEditPromptInput,
        tokenizer: Tokenizer,
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )
        expected_tokens = [
            *tokenizer.tokenize_safe("fix bugs or do something else? many options")[
                -2:
            ],
            special_tokens.instruction,
            *tokenizer.tokenize_safe("""\
--- src/examples.py
+++ src/examples.py
@@ -8,1 +8,4 @@ def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            NextEditLocationQueryFormatter(
                tokenizer=tokenizer,
                config=NextEditLocationQueryFormatter.Config(
                    max_prompt_tokens=150,
                    max_instruction_tokens=2,
                    diff_context_lines=1,
                    max_diff_tokens=70,
                    truncate_instructions_tail=False,
                ),
            )
            .format_prompt(valid_input_long_instruction)
            .tokens()
        )

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."

    def test_command_output(
        self,
        valid_input_long_instruction_with_cmd_output: LocalizationNextEditPromptInput,
        tokenizer: Tokenizer,
    ):
        special_tokens = tokenizer.special_tokens
        assert isinstance(special_tokens, NextEditGenSpecialTokens) and isinstance(
            special_tokens, RetrievalSpecialTokens
        )
        expected_tokens = [
            *tokenizer.tokenize_safe("fix bugs or do something else? many options")[:2],
            special_tokens.instruction,
            *tokenizer.tokenize_safe("here are some failures-pytest blah blah pytest")[
                -3:
            ],
            special_tokens.far_prefix,
            *tokenizer.tokenize_safe("""\
--- src/examples.py
+++ src/examples.py
@@ -8,1 +8,4 @@ def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""),
            special_tokens.end_of_query,
        ]
        actual_tokens = (
            NextEditLocationQueryFormatter(
                tokenizer=tokenizer,
                config=NextEditLocationQueryFormatter.Config(
                    max_prompt_tokens=150,
                    max_instruction_tokens=2,
                    diff_context_lines=1,
                    max_diff_tokens=70,
                    max_command_output_tokens=3,
                ),
            )
            .format_prompt(valid_input_long_instruction_with_cmd_output)
            .tokens()
        )

        if actual_tokens != expected_tokens:
            assert_str_eq(
                tokenizer.detokenize(actual_tokens),
                tokenizer.detokenize(expected_tokens),
            )
            assert False, "Token sequences failed to match."
