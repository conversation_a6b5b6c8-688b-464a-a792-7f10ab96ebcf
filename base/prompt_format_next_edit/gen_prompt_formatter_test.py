from __future__ import annotations

import dataclasses
import pdb
from random import Random

import pytest

from base.diff_utils.changes import Modified
from base.diff_utils.diff_formatter import File
from base.prompt_format.common import PromptChunk
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    EditGenOutput,
    EditGenPromptFormatter,
    EditGenPromptInput,
    PromptSectionName,
    SectionBudgetsDict,
    cleanup_truncated_model_diff,
    decode_model_diff,
    default_prompt_section_order,
    encode_model_diff_input,
    encode_model_diff_output,
)
from base.ranges.line_map import LineMap
from base.ranges.range_types import <PERSON>r<PERSON><PERSON><PERSON>, LineRange
from base.retrieval.chunking.line_based_chunking import split_line_chunks
from base.test_utils.testing_utils import (
    assert_eq,
    assert_str_eq,
    random_str,
    run_property_test,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderBaseTokenizer
from base.tokenizers.llama3_tokenizer import Llama3BaseTokenizer
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.tokenizers.tiktoken_starcoder_tokenizer import TiktokenStarCoderTokenizer
from base.tokenizers.tokenizer import NextEditGenSpecialTokens, Tokenizer

StarCoderTokenizer = TiktokenStarCoderTokenizer


def test_formatting_selected_code():
    current_code = """\
import pathlib

file = pathlib.Path("foo")
lines = 0
for x in file.open():
    print(x)
    lines += 1\
"""
    lmap = LineMap(current_code)
    # select line 2, 3, 4
    edit_region = lmap.lrange_to_crange(LineRange(2, 5))

    # The logic is the same for StarCoder1 and StarCoder2, so just test one.
    prefix, suffix = EditGenPromptFormatter.format_selected_code(
        current_code, edit_region, use_diff_based_output=False
    )
    actual_prompt = "<fim_prefix>" + prefix + "<fim_suffix>" + suffix
    expected_prompt = """\
<fim_prefix>import pathlib

[[To Replace]]
file = pathlib.Path("foo")
lines = 0
for x in file.open():

[[Begin Replacement]]
<fim_suffix>
[[End Replacement]]
    print(x)
    lines += 1\
"""
    assert_str_eq(actual_prompt, expected_prompt)

    # now select the last 3 lines
    edit_region = lmap.lrange_to_crange(LineRange(4, 7))
    prefix, suffix = EditGenPromptFormatter.format_selected_code(
        current_code, edit_region, use_diff_based_output=False
    )
    actual_prompt = "<fim_prefix>" + prefix + "<fim_suffix>" + suffix
    expected_prompt = """\
<fim_prefix>import pathlib

file = pathlib.Path("foo")
lines = 0
[[To Replace]]
for x in file.open():
    print(x)
    lines += 1
[[Begin Replacement]]
<fim_suffix>
[[End Replacement]]
"""
    assert_str_eq(actual_prompt, expected_prompt)

    # now test use_diff_based_output=True
    prefix, suffix = EditGenPromptFormatter.format_selected_code(
        current_code, edit_region, use_diff_based_output=True
    )
    actual_prompt = "<fim_prefix>" + prefix + "<fim_suffix>" + suffix
    expected_prompt = """\
<fim_prefix>import pathlib

file = pathlib.Path("foo")
lines = 0
[[To Replace]]
1|for x in file.open():
2|    print(x)
3|    lines += 1
[[Begin Replacement]]
<fim_suffix>
[[End Replacement]]
"""
    assert_str_eq(actual_prompt, expected_prompt)


@pytest.mark.parametrize(
    "tokenizer_type",
    [
        StarCoderTokenizer,
        StarCoder2Tokenizer,
        Llama3BaseTokenizer,
        DeepSeekCoderBaseTokenizer,
    ],
)
def test_formatting_output(tokenizer_type):
    old_code = """\
def ex_func1(a,b):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b
"""

    new_code = """\
def ex_func1(a: int, b: int):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b

def new_func():
    return 1
"""
    output = EditGenOutput(
        changed=True,
        replacement=new_code,
    )
    tkn = tokenizer_type()
    assert isinstance(tkn, Tokenizer)
    # check that we have all the required special tokens
    special_tokens = tkn.special_tokens
    assert isinstance(special_tokens, NextEditGenSpecialTokens)

    formatter = EditGenPromptFormatter(
        tkn, config=EditGenFormatterConfig(use_diff_based_output=False)
    )
    actual_prompt = tkn.detokenize(
        formatter.format_output_prompt(old_code, output, lang=None)
    )
    expected_prompt = tkn.detokenize(
        [
            special_tokens.has_change,
            *tkn.tokenize_safe(new_code),
            special_tokens.eos,
        ]
    )
    assert_str_eq(actual_prompt, expected_prompt)

    has_change_token = tkn.detokenize([special_tokens.has_change])
    eos_token = tkn.detokenize([special_tokens.eos])
    pause_token = tkn.detokenize([special_tokens.pause])

    # test outputting diff
    formatter = EditGenPromptFormatter(
        tkn,
        config=EditGenFormatterConfig(use_diff_based_output=True),
    )
    actual_prompt = tkn.detokenize(
        formatter.format_output_prompt(old_code, output, lang=None)
    )
    expected_prompt = f"""\
{has_change_token}1|def ex_func1(a: int, b: int):
8+
 +def new_func():
 +    return 1
{eos_token}\
"""
    assert_str_eq(actual_prompt, expected_prompt)

    # test compact diff with pause
    formatter = EditGenPromptFormatter(
        tkn,
        config=EditGenFormatterConfig(
            use_diff_based_output=True, diff_chunk_size_chars=50
        ),
    )
    actual_prompt = tkn.detokenize(
        formatter.format_output_prompt(old_code, output, lang=None)
    )
    expected_prompt = f"""\
{has_change_token}1|def ex_func1(a: int, b: int):
{pause_token}8+
 +def new_func():
 +    return 1
{eos_token}\
"""
    assert_str_eq(actual_prompt, expected_prompt)


def test_prefix_suffix_budget():
    # Test that the prefix and suffix sections use a static budget.

    dummy_line = "a" * 50 + "\n"
    dummy_code = dummy_line * 1000

    prompt_input = EditGenPromptInput(
        current_file=File("src/current.py", dummy_code),
        edit_region=CharRange(0, len(dummy_line) * 20),
        instruction="",
        recent_changes=[],
        retrieval_chunks=[],
    )

    tokenizer = StarCoder2Tokenizer()

    dummy_code_len_tokens = len(tokenizer.tokenize_safe(dummy_code))

    formatter = EditGenPromptFormatter(
        tokenizer,
        config=EditGenFormatterConfig(
            max_prompt_tokens=dummy_code_len_tokens,
            section_budgets=SectionBudgetsDict(
                prefix_tks=dummy_code_len_tokens // 10,
                suffix_tks=dummy_code_len_tokens // 10,
                diff_tks=1000,
                filename_tks=100,
                instruction_tks=100,
                retrieval_tks=1000,
            ),
        ),
    )
    prompt_tokens = formatter.format_input_prompt(prompt_input).tokens

    # verify that even though there's more space in the total budget, the final prompt
    # is much shorter than the total budget due to limited prefix and suffix.
    assert formatter.config.max_prompt_tokens > dummy_code_len_tokens // 5 + 100
    assert len(prompt_tokens) < dummy_code_len_tokens // 5 + 100


def test_formatting_end_to_end():
    # Test end-to-end formatting of a small example.
    current_code = """\
import pathlib

file = pathlib.Path("foo")
lines = 0
for x in file.open():
    print(x)
    lines += 1
"""
    lmap = LineMap(current_code)
    # select line 2, 3, 4
    edit_region = lmap.lrange_to_crange(LineRange(2, 5))

    retrieval_doc_code = """\
def ex_func1(a,b):
    return a+b

def ex_func2(a,b):
    return a-b
"""
    retrieval_chunks = [
        PromptChunk(
            text=c.text,
            path="src/examples.py",
            char_start=c.crange().start,
            char_end=c.crange().stop,
        )
        for c in split_line_chunks(
            retrieval_doc_code,
            chunk_max_lines=3,
            chunk_max_chars=1_000_000,
            overlap_lines=0,
        )
    ]

    retrieval_code_updated = """\
def ex_func1(a: int, b: int):
    return a+b

def ex_func2(a,b):
    return a-b

def new_func():
    return 1
"""

    file_changes = [
        Modified(
            before=File(path="src/examples.py", contents=retrieval_doc_code),
            after=File(path="src/examples.py", contents=retrieval_code_updated),
        )
    ]

    input = EditGenPromptInput(  # noqa
        current_file=File("src/current.py", current_code),
        edit_region=edit_region,
        instruction="fix bugs",
        recent_changes=file_changes,
        retrieval_chunks=retrieval_chunks,
    )

    # Test on StarCoder2
    expected_legacy_prompt = """\
<pr>fix bugs
<file_sep>src/current.py
<pr_base>
<pr_file>src/examples.py
<pr_base_code>def ex_func2(a,b):
    return a-b


<pr_diff>
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
@@ -5,1 +5,4 @@
@def ex_func2(a,b):
     return a-b
+
+def new_func():
+    return 1

<fim_prefix>import pathlib

[[To Replace]]
file = pathlib.Path("foo")
lines = 0
for x in file.open():

[[Begin Replacement]]
<fim_suffix>
[[End Replacement]]
    print(x)
    lines += 1

<fim_middle>\
"""

    legacy_prompt_section_order = (
        PromptSectionName.INSTRUCTION,
        PromptSectionName.FILENAME,
        PromptSectionName.RETRIEVAL,
        PromptSectionName.DIFF,
        PromptSectionName.SELECTION,
    )

    tokenizer = StarCoder2Tokenizer()
    formatter = EditGenPromptFormatter(
        tokenizer,
        config=EditGenFormatterConfig(
            diff_context_lines=1,
            section_order=legacy_prompt_section_order,
            output_instruction=False,
            use_diff_based_output=False,
        ),
    )

    actual_prompt = tokenizer.detokenize(formatter.format_input_prompt(input).tokens)
    print(actual_prompt)
    assert_str_eq(actual_prompt, expected_legacy_prompt)

    expected_default_prompt = """\
<pr_diff>
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
@@ -5,1 +5,4 @@
@def ex_func2(a,b):
     return a-b
+
+def new_func():
+    return 1

<pr>fix bugs<|pause|>
<pr_base>
<pr_file>src/examples.py
<pr_base_code>def ex_func2(a,b):
    return a-b


<file_sep>src/current.py
<fim_prefix>import pathlib

[[To Replace]]
file = pathlib.Path("foo")
lines = 0
for x in file.open():

[[Begin Replacement]]
<fim_suffix>
[[End Replacement]]
    print(x)
    lines += 1

<fim_middle>\
"""
    formatter.config.section_order = default_prompt_section_order
    formatter.config.output_instruction = True
    actual_prompt = tokenizer.detokenize(formatter.format_input_prompt(input).tokens)
    print(actual_prompt)
    assert_str_eq(actual_prompt, expected_default_prompt)

    # test formatting with empty instruction
    # in this case, the instruction section should be dropped
    input = dataclasses.replace(input, instruction="")
    actual_prompt = tokenizer.detokenize(formatter.format_input_prompt(input).tokens)
    assert "<pr>" not in actual_prompt
    assert "fix bugs" not in actual_prompt
    assert "<|pause|>" not in actual_prompt

    # For older formatter with output_instruction=False, we should keep the instruction
    # section to be backward compatible.
    formatter.config.output_instruction = False
    actual_prompt = tokenizer.detokenize(formatter.format_input_prompt(input).tokens)
    assert "<pr>\n" in actual_prompt
    assert "fix bugs" not in actual_prompt
    assert "<|pause|>" not in actual_prompt


def test_diff_encoding_examples():
    # Test the examples in the docstring of encode_model_diff.
    input_str = """\
line1
line2
line3
line4  # to delete
line5
"""
    output_str = """\
prepended
line1
modified
line3
line5
appended
"""
    model_input = encode_model_diff_input(input_str)
    expected_input = """\
1|line1
2|line2
3|line3
4|line4  # to delete
5|line5
"""
    assert_str_eq(model_input, expected_input)

    [model_output] = encode_model_diff_output(
        input_str, output_str, max_pause_chars=None
    )

    expected_output = """\
0+prepended
2|modified
4-
5+appended
"""
    assert_str_eq(model_output, expected_output)

    # using a very large max_pause_chars is same as not having the limit
    diff_outputs = encode_model_diff_output(input_str, output_str, max_pause_chars=100)
    assert_eq(len(diff_outputs), 1)
    assert_str_eq(diff_outputs[0], expected_output)

    output_str2 = """\
prepended
line1
modified
modified
line4
line5
appended
"""

    [model_output] = encode_model_diff_output(
        input_str, output_str2, max_pause_chars=None
    )

    expected_output = """\
0+prepended
1+modified
 +modified
2|line4
3-
4-
5+appended
"""

    assert_str_eq(model_output, expected_output)
    # using a limit of 16 will break the output into two parts
    diff_outputs = encode_model_diff_output(input_str, output_str, max_pause_chars=25)

    expected_output1 = """\
0+prepended
2|modified
"""
    expected_output2 = """\
4-
5+appended
"""
    assert_str_eq(diff_outputs[0], expected_output1)
    assert_str_eq(diff_outputs[1], expected_output2)
    assert_eq(len(diff_outputs), 2)

    # now test the quality of the pauses
    old_code = """\
big_list = [
    {
        "key1": "value1",
        "key2": "value2",
    },
]
"""

    new_code = """\
big_list = [
    {
        "key1": "new_value1",
        "key2": "new_value2",
    },
    {
        "key3": "new_value3",
        "key4": "new_value4",
    },
    {
        "key5": "new_value5",
        "key6": "new_value6",
    },
]
"""
    # use a chunk size that only fits in a single list entry
    diff_outputs = encode_model_diff_output(
        old_code, new_code, max_pause_chars=50, lang="Python"
    )
    assert_eq(len(diff_outputs), 3)
    expected_output1 = """\
3|        "key1": "new_value1",
4|        "key2": "new_value2",
 +    },
"""
    expected_output2 = """\
 +    {
 +        "key3": "new_value3",
 +        "key4": "new_value4",
 +    },
"""
    expected_output3 = """\
 +    {
 +        "key5": "new_value5",
 +        "key6": "new_value6",
"""

    assert_str_eq(diff_outputs[0], expected_output1)
    assert_str_eq(diff_outputs[1], expected_output2)
    assert_str_eq(diff_outputs[2], expected_output3)

    # use a chunk size that fits in the first two list entries
    diff_outputs = encode_model_diff_output(
        old_code, new_code, max_pause_chars=100, lang="Python"
    )
    assert_eq(len(diff_outputs), 2)
    expected_output1 = """\
3|        "key1": "new_value1",
4|        "key2": "new_value2",
 +    },
 +    {
 +        "key3": "new_value3",
 +        "key4": "new_value4",
 +    },
"""
    expected_output2 = """\
 +    {
 +        "key5": "new_value5",
 +        "key6": "new_value6",
"""

    assert_str_eq(diff_outputs[0], expected_output1)
    assert_str_eq(diff_outputs[1], expected_output2)


def test_diff_decoding():
    before_str = """\
line1
line2
line3
line4
line5
"""
    after_str = """\
prepended
line1
modified
line3
line5
appended
"""

    input_str = """\
1|line1
2|line2
3|line3
4|line4
5|line5
"""

    normal_output_str_compact = """\
0+prepended
2|modified
4-
5+appended
"""
    reconstructed = decode_model_diff(input_str, normal_output_str_compact)
    assert_str_eq(reconstructed.before, before_str)
    assert_str_eq(reconstructed.after, after_str)
    assert_eq(reconstructed.last_changed_char_in_after, len(after_str))

    # a diff string that is truncated at the end
    truncated_out_str = """\
0+prepended
2
"""

    truncated_after_str = """\
prepended
line1
line2
line3
line4
line5
"""

    reconstructed = decode_model_diff(input_str, truncated_out_str)
    assert_str_eq(reconstructed.before, before_str)
    assert_str_eq(reconstructed.after, truncated_after_str)
    assert_eq(reconstructed.last_changed_char_in_after, len("prepended\n"))

    truncated_out_str2 = """\
0+prepended
2|modi\
"""
    truncated_after_str2 = """\
prepended
line1
modiline3
line4
line5
"""
    reconstructed = decode_model_diff(input_str, truncated_out_str2)
    assert_str_eq(reconstructed.before, before_str)
    assert_str_eq(reconstructed.after, truncated_after_str2)
    assert_eq(reconstructed.last_changed_char_in_after, len("prepended\nline1\nmodi"))

    # a diff string that contains a bad line number
    bad_index_out_str = """\
0+prepended
6-
4+inserted
"""
    bad_index_after_str = """\
prepended
line1
line2
line3
line4
line5
"""
    reconstructed = decode_model_diff(input_str, bad_index_out_str)
    assert_str_eq(reconstructed.before, before_str)
    assert_str_eq(reconstructed.after, bad_index_after_str)
    assert_eq(reconstructed.last_changed_char_in_after, len("prepended\n"))

    # a diff string that tries to delete the same line twice
    bad_delete_out_str = """\
0+prepended
2-
2-
"""
    bad_delete_after_str = """\
prepended
line1
line3
line4
line5
"""
    reconstructed = decode_model_diff(input_str, bad_delete_out_str)
    assert_str_eq(reconstructed.before, before_str)
    assert_str_eq(reconstructed.after, bad_delete_after_str)
    assert_eq(reconstructed.last_changed_char_in_after, len("prepended\nline1\n"))


def test_diff_encoding_decoding_randomized():
    # Composing encode_model_diff and decode_model_diff should be identity.

    def test(rng: Random, before: str | None = None, after: str | None = None):
        if before is None:
            before = random_str(rng)
        if after is None:
            after = random_str(rng)
        input_str = encode_model_diff_input(before)
        diff_chunks = encode_model_diff_output(before, after, max_pause_chars=None)
        diff_str = "".join(diff_chunks)
        before1, after1 = decode_model_diff(input_str, diff_str).before_after
        assert_str_eq(before1, before)
        assert_str_eq(after1, after)

    run_property_test(test)

    # extra cases
    rng = Random(123)
    for n_lines in range(200):
        # no newline at the end
        input_str = "\n".join(["abc"] * n_lines)
        test(rng, before=input_str)
        # has newline at the end
        test(rng, before=input_str + "\n")


@pytest.mark.parametrize("use_diff_based_output", [True, False])
def test_formatting_output_prompt_randomized(use_diff_based_output: bool):
    # Test the formatting of the output prompt.

    tokenizer = StarCoderTokenizer()
    formatter = EditGenPromptFormatter(
        tokenizer,
        config=EditGenFormatterConfig(use_diff_based_output=use_diff_based_output),
    )

    def test(rng: Random):
        before = random_str(rng)
        after = random_str(rng)
        output = EditGenOutput(changed=before != after, replacement=after)

        # formatting output prompt and then decoding it should give back
        # the original output
        output_tokens = formatter.format_output_prompt(before, output, lang=None)
        decoded = formatter.decode_output_tokens(output_tokens, before)
        assert_eq(decoded.changed, output.changed)
        assert_str_eq(decoded.replacement, output.replacement)
        assert decoded.truncation_char is None

    run_property_test(test)


def test_cleanup_truncated_model_diff():
    # Test the cleanup of a truncated model diff string.

    complete_diff = """\
0+prepended
2|modified
4-
5+appended
"""
    assert_str_eq(cleanup_truncated_model_diff(complete_diff), complete_diff)

    incomplete_diff = complete_diff + "6|abc"  # does not end with a newline
    assert_str_eq(cleanup_truncated_model_diff(incomplete_diff), complete_diff)


def test_formatting_output_prompt_truncation():
    # Test the formatting of the output prompt.

    dummy_line = "a" * 50 + "\n"
    dummy_code = dummy_line * 1000

    prompt_input = EditGenPromptInput(
        current_file=File("src/current.py", dummy_code),
        edit_region=CharRange(0, len(dummy_line) * 20),
        instruction="hi" * 10_000 + "magic_number=123",
        recent_changes=[],
        retrieval_chunks=[],
    )

    tokenizer = StarCoder2Tokenizer()

    dummy_code_len_tokens = len(tokenizer.tokenize_safe(dummy_code))

    formatter = EditGenPromptFormatter(
        tokenizer,
        config=EditGenFormatterConfig(
            max_prompt_tokens=dummy_code_len_tokens,
            section_budgets=SectionBudgetsDict(
                prefix_tks=dummy_code_len_tokens // 10,
                suffix_tks=dummy_code_len_tokens // 10,
                diff_tks=1000,
                filename_tks=100,
                instruction_tks=100,
                retrieval_tks=1000,
            ),
        ),
    )
    prompt_tokens = formatter.format_input_prompt(prompt_input).tokens
    prompt = tokenizer.detokenize(prompt_tokens)
    assert "magic_number=123" not in prompt

    formatter = EditGenPromptFormatter(
        tokenizer,
        config=EditGenFormatterConfig(
            max_prompt_tokens=dummy_code_len_tokens,
            section_budgets=SectionBudgetsDict(
                prefix_tks=dummy_code_len_tokens // 10,
                suffix_tks=dummy_code_len_tokens // 10,
                diff_tks=1000,
                filename_tks=100,
                instruction_tks=100,
                retrieval_tks=1000,
            ),
            truncate_instructions_tail=False,
        ),
    )
    prompt_tokens = formatter.format_input_prompt(prompt_input).tokens
    prompt = tokenizer.detokenize(prompt_tokens)
    assert "magic_number=123" in prompt
