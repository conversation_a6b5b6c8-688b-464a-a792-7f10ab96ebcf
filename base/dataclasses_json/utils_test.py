"""Unit tests for dataclasses_json.utils."""

from dataclasses import dataclass
from pathlib import Path
from typing import (
    Literal,
    cast,
    get_args,
)

import pytest
from dataclasses_json import DataClassJsonMixin
from marshmallow import ValidationError

from base.dataclasses_json.utils import (
    literal_field,
    register_literal_type,
    setup_dataclasses_json,
)

Language = Literal["python", "java", "cpp"]


@dataclass
class DataWithLanguage(DataClassJsonMixin):
    lang: Language = literal_field(Language)
    maybe_lang: Language | None = literal_field(Language, default=None)


def test_literal_field():
    """Test that literal_field() works as expected."""

    assert DataWithLanguage.schema().load(
        {
            "lang": "python",
            "maybe_lang": None,
        }
    ) == DataWithLanguage(lang="python", maybe_lang=None)


def test_literal_field_validates_invalid_literals():
    """Test that literal_field() works as expected."""
    with pytest.raises(ValidationError):
        DataWithLanguage.schema().load(
            {
                "lang": "python",
                "maybe_lang": "javascript",
            }
        )


def test_setup_dataclasses_json_supports_path():
    """Test that setup_dataclasses_json() works as expected."""
    setup_dataclasses_json()

    @dataclass
    class TestClassWithPath(DataClassJsonMixin):
        path: Path

    assert TestClassWithPath.schema().load(
        {
            "path": "/tmp/foo",
        }
    ) == TestClassWithPath(path=Path("/tmp/foo"))
    assert TestClassWithPath.schema().dump(
        TestClassWithPath(path=Path("/tmp/foo"))
    ) == {"path": "/tmp/foo"}


def test_setup_dataclasses_json_supports_optional_path():
    """Test that setup_dataclasses_json() works as expected."""
    setup_dataclasses_json()

    @dataclass
    class TestClassWithPath(DataClassJsonMixin):
        path: Path | None

    assert TestClassWithPath.schema().load(
        {
            "path": None,
        }
    ) == TestClassWithPath(path=None)

    assert TestClassWithPath.schema().dump(TestClassWithPath(path=None)) == {
        "path": None
    }


Colors = Literal["red", "green", "blue"]


@dataclass
class DataWithColor(DataClassJsonMixin):
    color: Colors


def test_register_literal_type():
    """Test that setup_dataclasses_json() works as expected."""
    register_literal_type(Colors)

    dct = {"color": "blue"}
    obj = DataWithColor(color="blue")
    assert DataWithColor.schema().load(dct) == obj
    assert DataWithColor.schema().dump(obj) == dct


def test_register_literal_type_validates():
    """Test that setup_dataclasses_json() works as expected."""
    register_literal_type(Colors)

    dct = {"color": "purple"}
    with pytest.raises(ValidationError):
        DataWithColor.schema().load(dct)
