"""Build file for dataclasses_json."""

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

package(default_visibility = ["//visibility:public"])

py_library(
    name = "utils",
    srcs = ["utils.py"],
    deps = [
        requirement("dataclasses-json"),
    ],
)

pytest_test(
    name = "utils_test",
    srcs = ["utils_test.py"],
    deps = [":utils"],
)

py_library(
    name = "dataclasses_json",
    srcs = ["__init__.py"],
    deps = [
        ":utils",
    ],
)
