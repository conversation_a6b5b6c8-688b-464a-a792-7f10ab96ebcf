# Because this takes no arguments, this is a simple way of only running this function
# once.
import dataclasses
import functools
import logging
import typing
from pathlib import Path
from typing import Any, Literal, cast

import dataclasses_json
from marshmallow import ValidationError
from marshmallow import fields as mm_fields

logger = logging.getLogger(__name__)


# NOTE(arun): <PERSON><PERSON> calling this function to make sure it is only ever called
# once per process.
@functools.cache
def setup_dataclasses_json():
    """Setup dataclasses_json to handle commonly used types.

    This function should be run before any dataclass is converted to JSON.
    """

    dataclasses_json.global_config.encoders[Path] = str
    dataclasses_json.global_config.decoders[Path] = Path
    dataclasses_json.global_config.mm_fields[Path] = mm_fields.String()
    # NOTE(arun): This is a hack to make Optional[Path] or Path | None work, but the
    # pylance doesn't like it.
    dataclasses_json.global_config.encoders[Path | None] = str  # type: ignore
    dataclasses_json.global_config.decoders[Path | None] = Path  # type: ignore
    dataclasses_json.global_config.mm_fields[Path | None] = mm_fields.String(  # type: ignore
        allow_none=True
    )


# NOTE(arun): The below routines support Literal fields. Per-se this would be obviated
# if and when https://github.com/lidatong/dataclasses-json/pull/534 is upstreamed.


def _decode_literal_field(literal_typ, literal_values: list[str], value: str) -> str:
    """Validate that a value is valid for a Literal field."""
    if value not in literal_values:
        raise ValidationError(f"Value {value} is not one of {literal_typ}")
    return value


@functools.cache
def register_literal_type(literal_typ, allow_none: bool = False):
    """Register a Literal type with dataclasses_json.

    Note that this method registers the Literal globally. See `literal_field` for a more
    targeted implementation.

    Args:
        literal_typ: The Literal type to register.
        allow_none: If True, allow None as a valid value.
    """
    if typing.get_origin(literal_typ) is not Literal or not all(
        isinstance(arg, str) for arg in typing.get_args(literal_typ)
    ):
        raise TypeError(f"{literal_typ=} must be a Literal type with string arguments.")
    literal_values = list[str](typing.get_args(literal_typ))

    dataclasses_json.global_config.encoders[literal_typ] = str
    dataclasses_json.global_config.decoders[literal_typ] = functools.partial(
        _decode_literal_field,
        literal_typ,
        literal_values,
    )
    dataclasses_json.global_config.mm_fields[literal_typ] = mm_fields.String(
        allow_none=allow_none
    )


T = typing.TypeVar("T")


def literal_field(
    literal_typ,
    default=dataclasses.MISSING,
):
    """Create a dataclass field for a `Literal`.

    Example:
    >>> Language = Literal["python", "java", "cpp"]
    ... @dataclass
    ... class DataWithLanguage(DataClassJsonMixin):
    ...     lang: Language = literal_field(Language)
    ...     maybe_lang: Language | None = literal_field(Language, default=None)

    Args:
        literal_typ: The Literal type for the field.
        default: The default value for the field.

    Returns:
        A field configured to validate against the Literal type values.
    """
    if typing.get_origin(literal_typ) is not Literal or not all(
        isinstance(arg, str) for arg in typing.get_args(literal_typ)
    ):
        raise TypeError(f"{literal_typ=} must be a Literal type with string arguments.")

    literal_values = list[str](typing.get_args(literal_typ))

    if (
        default is not dataclasses.MISSING
        and default is not None
        and default not in literal_values
    ):
        raise ValueError(f"{default=} must be one of {literal_values=} or None.")

    return dataclasses.field(
        default=cast(Any, default),
        metadata=dataclasses_json.config(
            encoder=str,
            decoder=functools.partial(
                _decode_literal_field,
                literal_typ,
                literal_values,
            ),
            mm_field=mm_fields.String(allow_none=default is None),
        ),
    )
