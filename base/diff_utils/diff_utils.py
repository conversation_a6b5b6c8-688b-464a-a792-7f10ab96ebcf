"""Utilities for dealing with diffs."""

import re
from dataclasses import dataclass
from difflib import Sequence<PERSON>atch<PERSON>
from enum import IntEnum
from functools import cached_property
from typing import Callable, Iterable, Sequence, assert_never

from dataclasses_json import dataclass_json

from base.blob_names.python.blob_names import get_blob_name
from base.caching.lru_cache import lru_cache
from base.languages.language_guesser import guess_language
from base.ranges.line_map import LineMap, get_line_break
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.static_analysis.smart_header import assign_line_headers


@dataclass_json
@dataclass(frozen=True)
class File:
    """A text file."""

    path: str
    """The path to the file."""

    contents: str
    """The contents of the file."""

    @cached_property
    def blob_name(self) -> str:
        """The blob name of the file."""
        return get_blob_name(self.path, self.contents.encode("utf8"))

    def __post_init__(self):
        # Runtime type check To avoid errors around str vs. Path, which happened
        # because RepoChange uses Path while Repository uses str.
        if not isinstance(self.path, str):
            raise TypeError(f"Expected str, got {type(self.path)}")
        if not isinstance(self.contents, str):
            raise TypeError(f"Expected str, got {type(self.contents)}")


@dataclass(frozen=True)
class DiffHunk:
    """Represents a diff hunk."""

    text: str
    """The text of the hunk."""

    before_path: str
    """The path of the before file.

    Will be '/dev/null' if the file didn't exist before.
    """

    after_path: str
    """The path of the after file.

    Will be '/dev/null' if the file doesn't exist after.
    """

    before_lrange: LineRange
    """The line range of the hunk in the before file."""

    after_lrange: LineRange
    """The line range of the hunk in the after file."""

    before_crange: CharRange
    """The char range of the hunk in the before file."""

    after_crange: CharRange
    """The char range of the hunk in the after file."""

    def text_with_header(self, deduplicate_identical_paths: bool = False) -> str:
        """The text of the hunk with the file path header."""
        path_text = self.path_header(deduplicate_identical_paths)
        return path_text + self.text

    def path_header(self, deduplicate_identical_paths: bool = False) -> str:
        """The text of the hunk with the file path header."""
        return show_path_change(
            self.before_path, self.after_path, deduplicate_identical_paths
        )


def compute_file_diff(
    before_file: File | None,
    after_file: File | None,
    use_smart_header: bool = True,
    same_line_smart_header: bool = False,
    max_smart_header_chars: int = 200,
    num_context_lines: int = 3,
    deduplicate_identical_paths: bool = False,
) -> str:
    """Compute a unified diff string, optionally with smart header hunk heading.

    Args:
        before_file: The file before the change.
        after_file: The file after the change.
        num_context_lines: Number of context lines to include in a hunk. Hunks\
            whose context lines overlap get merged.
        use_smart_header: Whether to show smart header in the diff.
    """
    hunk_texts = (
        hunk.text
        for hunk in compute_file_diff_hunks(
            before_file,
            after_file,
            use_smart_header,
            same_line_smart_header,
            num_context_lines=num_context_lines,
            max_smart_header_chars=max_smart_header_chars,
        )
    )
    file_path_change = show_path_change(
        before_file.path if before_file else "/dev/null",
        after_file.path if after_file else "/dev/null",
        deduplicate_identical_paths,
    )
    return file_path_change + "".join(hunk_texts)


def show_path_change(
    before_path: str, after_path: str, deduplicate_identical_paths: bool
) -> str:
    """The text of the hunk with the file path header."""
    if deduplicate_identical_paths and before_path == after_path:
        return f"+++ {after_path}\n"
    else:
        return f"--- {before_path}\n+++ {after_path}\n"


@lru_cache(maxsize=512)
def compute_file_diff_hunks(
    before_file: File | None,
    after_file: File | None,
    use_smart_header: bool = True,
    same_line_smart_header: bool = False,
    num_context_lines: int = 3,
    max_smart_header_chars: int = 200,
) -> Sequence[DiffHunk]:
    """Compute a unified diff string, optionally with smart header hunk heading.

    Args:
        before_file: The file before the change.
        after_file: The file after the change.
        num_context_lines: Number of context lines to include in a hunk.
        use_smart_header: Whether to show smart header in the diff.
            When enabled, it analyzes the code structure to add contextual headers above each hunk,
            showing the enclosing code blocks (like class/function definitions) up to 3 levels deep.
            These headers are prefixed with '@' and help identify where in the code structure
            the changes occur. If disabled, only shows basic hunk location information.
        same_line_smart_header: Whether to show one smart header on the same line\
            as the hunk header. Only works when use_smart_header is False.
    """

    def truncate_header(header_line: str):
        if len(header_line) <= max_smart_header_chars:
            return header_line
        else:
            return header_line[: max(0, max_smart_header_chars - 4)] + "...\n"

    before_lines = before_file.contents.splitlines(keepends=True) if before_file else []
    after_lines = after_file.contents.splitlines(keepends=True) if after_file else []
    before_path = before_file.path if before_file else "/dev/null"
    after_path = after_file.path if after_file else "/dev/null"
    if use_smart_header:
        lang = guess_language(before_path)
        header_assignment = assign_line_headers(before_lines, lang, max_depth=3)

        def get_header(line: int) -> str:
            header_stack = header_assignment[line]
            if not header_stack:
                return ""
            # show each header on a separate line starting with `@`
            headers = "".join(
                "@" + truncate_header(before_lines[header.line_number])
                for header in header_stack
            )
            return "\n" + headers
    elif same_line_smart_header:
        lang = guess_language(before_path)
        header_assignment = assign_line_headers(before_lines, lang, max_depth=1)

        def get_header(line: int) -> str:
            header_stack = header_assignment[line]
            if not header_stack:
                return ""
            header = before_lines[header_stack[0].line_number].strip()[:80]
            return f" {header}"
    else:

        def get_header(line: int) -> str:
            return _find_hunk_heading_text(line, before_lines)

    return tuple(
        _generate_unified_diff_hunks(
            before_lines,
            after_lines,
            before_path,
            after_path,
            get_header,
            n=num_context_lines,
        )
    )


# TODO(arun): Include an ignore whitespace flag with `-wB` semantics.
def _generate_unified_diff_hunks(
    before_lines: Sequence[str],
    after_lines: Sequence[str],
    before_path: str,
    after_path: str,
    get_header: Callable[[int], str],
    n: int,
) -> Sequence[DiffHunk]:
    """Generate unified diff hunks from two sequences of lines using difflib.
    Supports hunk headings.
    Supports adding context lines.
    Argument naming aligns with the difflib.unified_diff() function.

    Args:
        before_lines: The original sequence of lines.
        after_lines: The modified sequence of lines.
        before_path: The original file name.
        after_path: The modified file name.
        get_header: A function that takes a line number in the original file and\
            returns the hunk header.
        n: The number of context lines to include in a hunk. Hunks whose context lines
            overlap get merged.
    """

    # Pass lines as sequence directly into LineMap. This is more efficient than passing in
    # a single string and is safe because the callsite generates these with keepends=True.
    before_lmap = LineMap(before_lines)
    after_lmap = LineMap(after_lines)

    change_groups = list(
        SequenceMatcher(None, before_lines, after_lines).get_grouped_opcodes(n)
    )

    diff_hunks = list[DiffHunk]()
    for group in change_groups:
        first, last = group[0], group[-1]
        before_lrange = LineRange(first[1], last[2])
        after_lrange = LineRange(first[3], last[4])
        hunk_heading = get_header(first[1]) if before_lines else ""

        diff_lines: list[UnifiedDiffLines] = []
        for tag, i1, i2, j1, j2 in group:
            if tag == "equal":
                diff_lines.append(
                    UnifiedDiffLines(before_lines[i1:i2], UnifiedDiffLineType.COMMON)
                )
                continue
            if tag in {"replace", "delete"}:
                diff_lines.append(
                    UnifiedDiffLines(before_lines[i1:i2], UnifiedDiffLineType.DELETION)
                )
            if tag in {"replace", "insert"}:
                diff_lines.append(
                    UnifiedDiffLines(after_lines[j1:j2], UnifiedDiffLineType.ADDITION)
                )

        hunk_text = _format_unified_diff_hunk(
            before_lrange, after_lrange, hunk_heading, diff_lines
        )

        hunk = DiffHunk(
            hunk_text,
            before_path,
            after_path,
            before_lrange,
            after_lrange,
            before_lmap.lrange_to_crange(before_lrange),
            after_lmap.lrange_to_crange(after_lrange),
        )
        diff_hunks.append(hunk)

    if not diff_hunks and before_path != after_path:
        # keep the renaming info using an empty hunk
        diff_hunks.append(
            DiffHunk(
                "",
                before_path,
                after_path,
                LineRange(0, 0),
                LineRange(0, 0),
                CharRange(0, 0),
                CharRange(0, 0),
            )
        )
    return diff_hunks


def _show_line(line: str) -> str:
    if not get_line_break(line):
        return line + "\n\\ No newline at end of file\n"
    else:
        return line


class UnifiedDiffLineType(IntEnum):
    COMMON = 0
    DELETION = 1
    ADDITION = 2


@dataclass
class UnifiedDiffLines:
    lines: Sequence[str]
    type: UnifiedDiffLineType


@dataclass
class UnifiedDiffHunk:
    """Represents a unified diff hunk.

    NOTE(jeff): The class design here is similar to unidiff.Hunk.
    """

    before_lrange: LineRange
    after_lrange: LineRange
    hunk_heading: str
    lines: Sequence[UnifiedDiffLines]

    def format_to_str(self) -> str:
        return _format_unified_diff_hunk(
            self.before_lrange, self.after_lrange, self.hunk_heading, self.lines
        )

    def validate(self) -> bool:
        """Check that the hunk is well-formed."""
        # NOTE(jeff): currently just used for tests.
        if not self.lines:
            return False

        if self.hunk_heading and not self.hunk_heading.endswith("\n"):
            return False

        if self.before_lrange.stop - self.before_lrange.start != sum(
            len(diff_line.lines)
            for diff_line in self.lines
            if diff_line.type != UnifiedDiffLineType.ADDITION
        ):
            return False

        if self.after_lrange.stop - self.after_lrange.start != sum(
            len(diff_line.lines)
            for diff_line in self.lines
            if diff_line.type != UnifiedDiffLineType.DELETION
        ):
            return False

        return True


def _format_unified_diff_hunk(
    before_lrange: LineRange,
    after_lrange: LineRange,
    hunk_heading: str,
    diff_lines: Sequence[UnifiedDiffLines],
) -> str:
    """Format a unified diff hunk."""
    hunk_lines = [
        "@@ -{} +{} @@{}".format(
            _format_range_unified(before_lrange.start, before_lrange.stop),
            _format_range_unified(after_lrange.start, after_lrange.stop),
            hunk_heading or "\n",
        )
    ]
    for diff_line in diff_lines:
        for line in diff_line.lines:
            if diff_line.type == UnifiedDiffLineType.COMMON:
                hunk_lines.append(" " + _show_line(line))
            elif diff_line.type == UnifiedDiffLineType.DELETION:
                hunk_lines.append("-" + _show_line(line))
            elif diff_line.type == UnifiedDiffLineType.ADDITION:
                hunk_lines.append("+" + _show_line(line))
            else:
                assert_never(diff_line.type)

    return "".join(hunk_lines)


_HEADING_PATTERN = re.compile(r"^[a-zA-Z_$]")


def _find_hunk_heading_text(from_line: int, lines: Sequence[str]) -> str:
    """Find the text of the hunk heading.

    Args:
        from_line: The line number of the first line of the hunk.
        lines: The lines of the file.

    Returns:
        The text of the hunk heading, or None if it could not be found.
    """
    for i in reversed(range(from_line)):
        if _HEADING_PATTERN.match(lines[i]):
            return " " + lines[i]
    return ""


def _format_range_unified(start: int, stop: int) -> str:
    'Convert range to the "ed" format'
    # Per the diff spec at http://www.unix.org/single_unix_specification/
    beginning = start + 1  # lines start numbering with one
    length = stop - start
    if not length:
        beginning -= 1  # empty ranges begin at line just before the range
    return "{},{}".format(beginning, length)
