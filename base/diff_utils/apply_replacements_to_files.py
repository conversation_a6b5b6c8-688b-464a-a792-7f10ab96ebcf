"""Library for reconstructing files from recent changes."""

from dataclasses import dataclass
from enum import Enum
from typing import Iterable, Optional, Sequence

from base.blob_names.python.blob_names import BlobN<PERSON>, FilePath
from base.diff_utils.diff_utils import File
from base.logging.secret_logging import <PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.common import groupby, replace_str
from base.datasets.recency_info import ReplacementText


class FileReplacementErrorType(Enum):
    PATH_MISMATCH = "path_mismatch"
    INCONSISTENT_BLOB_NAME = "inconsistent_blob_name"
    MISSING_EXPECTED_BLOB_NAME = "missing_expected_blob_name"
    RECONSTRUCTION_MISMATCH = "reconstruction_mismatch"
    EDIT_EVENTS_FAILURE = "edit_events_failure"  # temporary


@dataclass
class FileReplacementError:
    """Error information returned when applying replacements to a file fails."""

    file_blob_name: BlobName
    file_path: FilePath
    error_type: FileReplacementErrorType


def _apply_replacements_to_file(
    file_before_replacements: File,
    replacements_in_blob: list[ReplacementText],
):
    # apply the replacements
    file_content_after_replacements = replace_str(
        file_before_replacements.contents,
        [(x.crange, x.replacement_text) for x in replacements_in_blob],
    )

    # create the after file
    return File(file_before_replacements.path, file_content_after_replacements)


def check_replacements_for_errors(
    replacements: Sequence[ReplacementText],
    file_before_replacements: File,
    expected_blob_name_after_replacements: BlobName,
) -> Optional[FileReplacementError]:
    """Check if the replacements have correct path and expected blob name for applying to the provided file.

    Args:
        replacements: the replacements to check.
        file_before_replacements: the file replacements should be applied to.
        expected_blob_name_after_replacements: the expected blob name after applying the replacements.

    Returns:
        The first error if incorrect replacements is found, None otherwise.
    """

    if not expected_blob_name_after_replacements:
        return FileReplacementError(
            file_blob_name=file_before_replacements.blob_name,
            file_path=file_before_replacements.path,
            error_type=FileReplacementErrorType.MISSING_EXPECTED_BLOB_NAME,
        )

    replacement_error: FileReplacementError | None = None
    for replacement in replacements:
        if replacement.path != file_before_replacements.path:
            replacement_error = FileReplacementError(
                file_blob_name=file_before_replacements.blob_name,
                file_path=file_before_replacements.path,
                error_type=FileReplacementErrorType.PATH_MISMATCH,
            )
            break
        if replacement.expected_blob_name != expected_blob_name_after_replacements:
            replacement_error = FileReplacementError(
                file_blob_name=file_before_replacements.blob_name,
                file_path=file_before_replacements.path,
                error_type=FileReplacementErrorType.INCONSISTENT_BLOB_NAME,
            )
            break

    return replacement_error


def check_file_for_error(
    file_after_replacements: File,
    file_before_replacements: File,
    expected_blob_name_after_replacements: BlobName,
) -> Optional[FileReplacementError]:
    """Check if the file has correct path and expected blob name for applying replacements to it.

    Args:
        file_after_replacements: the file replacements should be applied to.
        file_before_replacements: the file replacements should be applied to.
        expected_blob_name_after_replacements: the expected blob name after applying the replacements.

    Returns:
        The first error if incorrect replacements is found, None otherwise.
    """

    # If reconstruction doesn't match expectations, yield an error
    if file_after_replacements.blob_name != expected_blob_name_after_replacements:
        return FileReplacementError(
            # the errors should have have the blob name and path of the file we tried applying replacements to
            file_blob_name=file_before_replacements.blob_name,
            file_path=file_before_replacements.path,
            error_type=FileReplacementErrorType.RECONSTRUCTION_MISMATCH,
        )

    return None


def apply_replacements_to_files(
    replacements: Sequence[ReplacementText],
    files_before_replacements: list[File],
    safe_logger: SecretLogger,
    skip_error_checks: bool = False,
) -> Iterable[File | FileReplacementError]:
    """Apply the replacements to the files.

    Args:
        replacements: the replacements to apply.
        files_before_replacements: the files replacements should be applied to.
        safe_logger: Note: sensitive information must use the secret_info/secret_warn/secret_error methods.
        skip_error_checks: if True, do not validate blob names.

    Returns:
        An iterator over files with replacements applied, or an error if applying replacements failed.

    Note:
        Only files with replacements will be returned.

    """

    # Filter out recent changes that have already been applied.
    pending_replacements = tuple(filter(lambda x: not x.present_in_blob, replacements))

    if len(pending_replacements) != len(replacements):
        safe_logger.warn(
            f"Trying to apply {len(replacements) - len(pending_replacements)} already applied replacements"
        )

    if not pending_replacements:
        safe_logger.warn("No pending replacements to apply")
        return iter(())

    safe_logger.info(f"Processing {len(pending_replacements)} recent changes")

    # Group recent changes by blob name so we can apply them all at once.
    grouped_replacements: dict[BlobName, list[ReplacementText]] = groupby(
        pending_replacements, lambda x: x.blob_name
    )

    # filter for files with pending replacements
    pending_blob_names = {x.blob_name for x in pending_replacements}
    files_with_pending_replacements = [
        x for x in files_before_replacements if x.blob_name in pending_blob_names
    ]

    safe_logger.info(f"Created {len(grouped_replacements)} groups of recent changes")

    if len(grouped_replacements) != len(files_with_pending_replacements):
        safe_logger.warn(
            f"Number of groups of recent changes ({len(grouped_replacements)}) does not match number of files ({len(files_with_pending_replacements)})"
        )
        grouped_replacement_set: set[BlobName] = set(grouped_replacements.keys())
        files_with_pending_replacements_set: set[BlobName] = {
            x.blob_name for x in files_with_pending_replacements
        }
        missing_replacements = (
            files_with_pending_replacements_set - grouped_replacement_set
        )
        extra_replacements = (
            grouped_replacement_set - files_with_pending_replacements_set
        )
        safe_logger.secret_warn(f"Missing replacements for {missing_replacements}")
        safe_logger.secret_warn(f"Extra replacements for {extra_replacements}")

    for file_before_replacements in files_with_pending_replacements:
        replacements_in_blob = grouped_replacements.get(
            file_before_replacements.blob_name, None
        )
        if not replacements_in_blob:
            safe_logger.warn(
                f"No replacements for {file_before_replacements.blob_name}"
            )
            continue

        safe_logger.secret_info(
            f"Applying replacements to {file_before_replacements.path}"
        )

        expected_blob_name_after_replacements = replacements_in_blob[
            0
        ].expected_blob_name

        # skip errors checks if requested
        if skip_error_checks:
            # apply the replacements
            file_after_replacements = _apply_replacements_to_file(
                file_before_replacements, replacements_in_blob
            )

            yield file_after_replacements
            continue

        # check the replacements for errors
        if replacement_error := check_replacements_for_errors(
            replacements_in_blob,
            file_before_replacements,
            expected_blob_name_after_replacements,
        ):
            yield replacement_error
            continue

        # apply the replacements
        file_after_replacements = _apply_replacements_to_file(
            file_before_replacements, replacements_in_blob
        )

        # check the reconstructed file for errors
        if replacement_error := check_file_for_error(
            file_after_replacements,
            file_before_replacements,
            expected_blob_name_after_replacements,
        ):
            yield replacement_error
            continue

        yield file_after_replacements
