"""
Usage: pytest base/diff_utils/diff_formatter_completions_test.py
"""

from base.prompt_format.common import PromptChunk
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.diff_utils.diff_formatter_completions import (
    tokenize_prompt_chunks,
    format_tokenized_prompt_chunks,
    TokenizedPromptChunk,
)


def test_tokenize_prompt_chunks():
    prompt_chunks = [
        PromptChunk(  # 43 tokens
            text="""\
@@ -8,1 +8,4 @@
@def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
""",
            path="+++ src/examples.py\n",
        ),
        PromptChunk(  # 51 tokens
            text="""\
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
""",
            path="+++ src/examples.py\n",
        ),
    ]

    tokenizer = StarCoder2Tokenizer()

    # Use infinite budget to get everything.
    t_chunks = tokenize_prompt_chunks(
        prompt_chunks, diff_token_budget=1_000_000, tokenizer=tokenizer
    )

    assert len(t_chunks) == 2
    assert len(t_chunks[0].body_tokens) == 43
    assert len(t_chunks[1].body_tokens) == 51
    assert tokenizer.detokenize(t_chunks[0].body_tokens) == prompt_chunks[0].text
    assert tokenizer.detokenize(t_chunks[1].body_tokens) == prompt_chunks[1].text

    # With a reduced budget, get just the first hunk.
    reduced_budget = t_chunks[0].num_tokens()
    actual_hunks = tokenize_prompt_chunks(
        prompt_chunks, diff_token_budget=reduced_budget, tokenizer=tokenizer
    )
    assert actual_hunks == t_chunks[:1]

    # if the budget is one token less, then we get no hunks left
    reduced_budget = t_chunks[0].num_tokens() - 1
    actual_hunks = tokenize_prompt_chunks(
        prompt_chunks, diff_token_budget=reduced_budget, tokenizer=tokenizer
    )
    assert actual_hunks == []


def test_format_tokenized_prompt_chunks():
    # Header tokens are not duplicated, so the second header is included
    tokenized_prompt_chunks = [
        TokenizedPromptChunk(header_tokens=[1, 2, 3], body_tokens=[4, 5, 6]),
        TokenizedPromptChunk(header_tokens=[7, 8, 9], body_tokens=[10, 11, 12]),
    ]
    formatted_tokens = format_tokenized_prompt_chunks(tokenized_prompt_chunks)
    assert formatted_tokens == [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]

    # Header tokens are duplicated, so the second header is not included
    tokenized_prompt_chunks = [
        TokenizedPromptChunk(header_tokens=[1, 2, 3], body_tokens=[4, 5, 6]),
        TokenizedPromptChunk(header_tokens=[1, 2, 3], body_tokens=[10, 11, 12]),
    ]
    formatted_tokens = format_tokenized_prompt_chunks(tokenized_prompt_chunks)
    assert formatted_tokens == [1, 2, 3, 4, 5, 6, 10, 11, 12]
