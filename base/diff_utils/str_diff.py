"""A minimal class for representing character-level (or line-level) diffs.

See `research/notebooks/str_diff_tutorial.ipynb` for example usages.
"""

from __future__ import annotations

import bisect
from collections import defaultdict
import difflib
from dataclasses import dataclass
from functools import cached_property
import re
from typing import Iterable, Sequence, Union

from base.ranges.line_map import LineMap
from base.ranges.range_types import CharRange


@dataclass(frozen=True)
class StrDiff:
    """Represents a character-level diff between two strings.

    See `research/notebooks/strdiff_tutorial.ipynb` for example usages.
    """

    spans: tuple[DiffSpan, ...]
    """Stores all changes as a sequence of DiffSpans."""

    @staticmethod
    def empty() -> StrDiff:
        """Return an empty diff."""
        return StrDiff(())

    def get_before(self) -> str:
        """Construct the old version of the text from this diff."""
        return "".join(op.before for op in self.spans)

    def get_after(self) -> str:
        """Construct the new version of the text from this diff."""
        return "".join(op.after for op in self.spans)

    @cached_property
    def inverted(self) -> StrDiff:
        """Return the inverse of this diff."""
        new_ops = tuple(op.inverted() for op in self.spans)
        return StrDiff(new_ops)

    @cached_property
    def span_ranges_in_after(self) -> Sequence[CharRange]:
        """Return the ranges of each span in the after version of the text."""
        offset = 0
        result = list[CharRange]()
        for span in self.spans:
            size = len(span.after)
            result.append(CharRange(offset, offset + size))
            offset += size
        return tuple(result)

    @cached_property
    def span_ranges_in_before(self) -> Sequence[CharRange]:
        """Return the ranges of each span in the before version of the text."""
        offset = 0
        result = list[CharRange]()
        for span in self.spans:
            size = len(span.before)
            result.append(CharRange(offset, offset + size))
            offset += size
        return tuple(result)

    def span_ranges(self) -> list[tuple[CharRange, CharRange]]:
        """Return the diff span ranges in both versions of the text.
        Each tuple returned is before followed by after.
        """
        before_spans = self.span_ranges_in_before
        after_spans = self.span_ranges_in_after
        # These are guaranteed to be the same length.
        return list(zip(before_spans, after_spans, strict=True))

    @cached_property
    def _before_range_starts(self) -> Sequence[int]:
        return tuple(r.start for r in self.span_ranges_in_before)

    @cached_property
    def _before_range_stops(self) -> Sequence[int]:
        return tuple(r.stop for r in self.span_ranges_in_before)

    def before_range_to_after(self, crange: CharRange) -> CharRange:
        """Map a char range in before version to a char range in after version.

        We conservatively maps to the largest possible range given by the diff.
        """
        if not self.spans:
            return crange
        before_range_starts = self._before_range_starts
        before_range_stops = self._before_range_stops
        before_range_all = CharRange(before_range_starts[0], before_range_stops[-1])
        if not before_range_all.contains(crange):
            raise ValueError(f"{crange} not contained in {before_range_all}")

        # Compute the beginning of the mapped range:
        # First find the last span containing crange.start.
        # this is the last span whose start is <= crange.start
        idx1 = bisect.bisect_right(before_range_starts, crange.start) - 1
        # now if this span is adjacent to an empty span, conservatively include these
        # empty spans into the mapped range
        while idx1 > 0:
            prev_span = self.span_ranges_in_before[idx1 - 1]
            if prev_span.stop == crange.start and prev_span.is_point():
                idx1 -= 1
            else:
                break
        if isinstance(self.spans[idx1], NoopSpan):
            # can map to an exact location within
            offset = crange.start - self.span_ranges_in_before[idx1].start
            start = self.span_ranges_in_after[idx1].start + offset
        else:
            # otherwise, map to that span's beginning
            start = self.span_ranges_in_after[idx1].start

        # Compute the stop of the mapped range:
        # First find the first span containing crange.stop.
        # this is the first span whose stop is >= crange.stop
        idx2 = bisect.bisect_left(before_range_stops, crange.stop)
        # now if this span is adjacent to an empty span, conservatively include these
        # empty spans into the mapped range
        while idx2 < len(self.spans) - 1:
            next_span = self.span_ranges_in_before[idx2 + 1]
            if next_span.start == crange.stop and next_span.is_point():
                idx2 += 1
            else:
                break
        span2 = self.spans[idx2]
        if isinstance(span2, NoopSpan):
            # can map to an exact location within
            offset = crange.stop - self.span_ranges_in_before[idx2].start
            stop = self.span_ranges_in_after[idx2].start + offset
        else:
            # otherwise, maps to the latest possible location
            stop = self.span_ranges_in_after[idx2].stop
        return CharRange(start, stop)

    def after_range_to_before(self, crange: CharRange) -> CharRange:
        """Map a char range in after version to a char range in before version."""
        return self.inverted.before_range_to_after(crange)

    def group_into_hunks(self, context_lines: int) -> StrDiff:
        """Build a new diff that merges nearby changes together.

        Args:
            context_lines: spans whose line distance is less than or equal to this
                number will be merged together.
        """

        def merge_spans(spans: Sequence[DiffSpan]) -> DiffSpan:
            assert spans
            if len(spans) == 1:
                return spans[0]

            old_segs = list[str]()
            new_segs = list[str]()
            for span in spans:
                old_segs.append(span.before)
                new_segs.append(span.after)
            old_code = "".join(old_segs)
            new_code = "".join(new_segs)
            if old_code == "":
                return AddedSpan(new_code)
            elif new_code == "":
                return DeletedSpan(old_code)
            else:
                return ModSpan(old_code, new_code)

        new_code = self.get_after()
        lmap_after = LineMap(new_code)
        all_spans = self.spans
        # First let's find out all changed spans that should be grouped together
        # while ignoring the NoopSpans.
        # Each merge group stores a list of span ids.
        merge_groups = list[list[int]]()
        current_group = list[int]()
        group_end_line: int | None = None

        for span_id, (op, op_range) in enumerate(
            zip(all_spans, self.span_ranges_in_after)
        ):
            # we ignore any NoopSpans since they don't affect grouping
            if isinstance(op, NoopSpan):
                continue
            start_line = lmap_after.get_line_number(op_range.start)
            if (
                group_end_line is not None
                and abs(group_end_line - start_line) <= context_lines
            ):
                # add this change to the current group
                current_group.append(span_id)
            else:
                # start a new group
                if current_group:
                    merge_groups.append(current_group)
                current_group = [span_id]
            group_end_line = lmap_after.get_line_number(op_range.stop)
        if current_group:
            merge_groups.append(current_group)

        # Now build a new str diff.
        # Note that we need to preserve any unmerged NoopSpan in this process.
        new_spans = list[DiffSpan]()
        next_span_id = 0
        for group in merge_groups:
            start_id = group[0]
            # add leftover spans in between groups
            new_spans.extend(all_spans[next_span_id:start_id])
            # add a newly merged span
            merged_span = merge_spans(all_spans[start_id : group[-1] + 1])
            new_spans.append(merged_span)
            next_span_id = group[-1] + 1
        new_spans.extend(all_spans[next_span_id:])
        return StrDiff(tuple(new_spans))

    def __repr__(self) -> str:
        ops_str = ", ".join(repr(op) for op in self.spans)
        return f"StrDiff({ops_str})"

    def __str__(self) -> str:
        if not self.spans:
            return "StrDiff()"
        ops_str = "".join(
            f"\t{before_range}->{after_range}, {repr(span)},\n"
            for span, before_range, after_range in zip(
                self.spans, self.span_ranges_in_before, self.span_ranges_in_after
            )
        )
        return f"StrDiff(\n{ops_str})"


def line_diff(before: str, after: str) -> StrDiff:
    """Take a line diff using difflib's SequenceMatcher."""
    before_lines = before.splitlines(keepends=True)
    after_lines = after.splitlines(keepends=True)
    cruncher = difflib.SequenceMatcher(None, before_lines, after_lines)
    spans = list[DiffSpan]()
    for tag, i1, i2, j1, j2 in cruncher.get_opcodes():
        if tag == "equal":
            spans.append(NoopSpan("".join(before_lines[i1:i2])))
        elif tag == "replace":
            spans.append(
                ModSpan("".join(before_lines[i1:i2]), "".join(after_lines[j1:j2]))
            )
        elif tag == "delete":
            spans.append(DeletedSpan("".join(before_lines[i1:i2])))
        elif tag == "insert":
            spans.append(AddedSpan("".join(after_lines[j1:j2])))
        else:
            raise AssertionError(f"Unknown tag: {tag}")
    return StrDiff(tuple(spans))


def precise_line_diff(before: str, after: str) -> StrDiff:
    """A more precise but also slower version of line_diff.

    This algorithm will try to break up large modification spans into smaller ones
    based on line similarity. The algorithm is adapted from difflib's _fancy_replace
    method in the Differ class.
    """
    before_lines = before.splitlines(keepends=True)
    after_lines = after.splitlines(keepends=True)
    cruncher = difflib.SequenceMatcher(difflib.IS_LINE_JUNK, before_lines, after_lines)
    spans = list[DiffSpan]()
    for tag, i1, i2, j1, j2 in cruncher.get_opcodes():
        if tag == "equal":
            spans.append(NoopSpan("".join(before_lines[i1:i2])))
        elif tag == "replace":
            try:
                inner_spans = _SimilarityDiffer(
                    before_lines, after_lines
                ).get_line_diff(i1, i2, j1, j2)
                spans.extend(inner_spans)
            except RecursionError:
                spans.append(
                    ModSpan("".join(before_lines[i1:i2]), "".join(after_lines[j1:j2]))
                )
        elif tag == "delete":
            spans.append(DeletedSpan("".join(before_lines[i1:i2])))
        elif tag == "insert":
            spans.append(AddedSpan("".join(after_lines[j1:j2])))
        else:
            raise AssertionError(f"Unknown tag: {tag}")
    return StrDiff(tuple(spans))


def precise_char_diff(before: str, after: str) -> StrDiff:
    """Take a character diff using SequenceMatcher and precise_line_diff."""
    spans = list[DiffSpan]()
    matcher = difflib.SequenceMatcher(difflib.IS_CHARACTER_JUNK)
    for span in precise_line_diff(before, after).spans:
        if not isinstance(span, ModSpan):
            spans.append(span)
            continue
        before, after = span.before, span.after
        matcher.set_seqs(before, after)
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == "equal":
                spans.append(NoopSpan(before[i1:i2]))
            elif tag == "replace":
                spans.append(ModSpan(before[i1:i2], after[j1:j2]))
            elif tag == "delete":
                spans.append(DeletedSpan(before[i1:i2]))
            elif tag == "insert":
                spans.append(AddedSpan(after[j1:j2]))
            else:
                raise AssertionError(f"Unknown tag: {tag}")
    spans = cleanup_diff_spans(spans)
    return StrDiff(tuple(spans))


def cleanup_diff_spans(
    spans: Sequence[DiffSpan], min_noop_size: int = 3
) -> Sequence[DiffSpan]:
    """Clean up the diff spans by merging some adjacent spans."""
    if len(spans) <= 1:
        return spans

    # step 1: merge all adjacent Noop spans
    prev_span = spans[0]
    new_spans = [prev_span]
    for next_span in spans[1:]:
        match prev_span, next_span:
            case NoopSpan(text1), NoopSpan(text2):
                new_spans[-1] = NoopSpan(text1 + text2)
            case _:
                new_spans.append(next_span)
        prev_span = new_spans[-1]
    spans = new_spans

    # step 2: merge neighboring spans that are separated by a tiny Noop unless the
    # noop span contains a newline.
    if len(spans) <= 2:
        return spans
    new_spans = list[DiffSpan]()
    for span3 in spans:
        match new_spans:
            case [*_, span1, NoopSpan(sep)] if (
                len(sep) < min_noop_size and "\n" not in sep
            ):
                mod_span = ModSpan(
                    span1.before + sep + span3.before,
                    span1.after + sep + span3.after,
                )
                new_spans.pop()
                new_spans[-1] = mod_span
            case _:
                new_spans.append(span3)
    return new_spans


def align_spans_to_word_boundaries(
    spans: Sequence[DiffSpan],
) -> Sequence[DiffSpan]:
    """Expand spans to align with word boundaries."""

    def get_alphanumeric_starting_characters(s: str) -> str:
        match = re.match(r"[^\W_]+", s)
        return s[: match.end()] if match else ""

    def starts_with_alphanumeric(s: str) -> bool:
        return get_alphanumeric_starting_characters(s) != ""

    expanded_spans: list[DiffSpan] = [NoopSpan(""), *spans, NoopSpan("")]

    for i in range(len(spans)):
        prev_span, current_span, next_span = (
            expanded_spans[i],
            expanded_spans[i + 1],
            expanded_spans[i + 2],
        )
        if isinstance(current_span, NoopSpan):
            continue

        if isinstance(prev_span, NoopSpan) and isinstance(next_span, NoopSpan):
            prev_string = prev_span.text
            next_string = next_span.text

            # get prefix and suffix from surrounding spans
            prefix = get_alphanumeric_starting_characters(prev_string[::-1])[::-1]
            suffix = get_alphanumeric_starting_characters(next_string)
            if prefix == "" and suffix == "":
                continue  # no alphanumeric characters to add

            expanded_before = ""
            expanded_after = ""

            # add the prefix iff current span starts with an alphanumeric character
            if starts_with_alphanumeric(
                current_span.before
            ) or starts_with_alphanumeric(current_span.after):
                # prefix must be dropped from previous span
                expanded_spans[i] = NoopSpan(prev_string.removesuffix(prefix))
                expanded_before += prefix
                expanded_after += prefix

            # current text must also be part of the expansion
            expanded_before += current_span.before
            expanded_after += current_span.after

            # add the suffix iff current span ends with an alphanumeric character
            if starts_with_alphanumeric(
                current_span.before[::-1]
            ) or starts_with_alphanumeric(current_span.after[::-1]):
                # suffix must be dropped from next span
                expanded_spans[i + 2] = NoopSpan(next_string.removeprefix(suffix))
                expanded_before += suffix
                expanded_after += suffix

            # If we have added something, we need to update the current span
            if (
                expanded_before != current_span.before
                or expanded_after != current_span.after
            ):
                expanded_spans[i + 1] = ModSpan(expanded_before, expanded_after)

    return tuple(
        expanded_spans[1:-1]
    )  # the spans in a StrDiff object are stored as a tuple, so we return a tuple here as well


def combine_change_spans(spans: Sequence[DiffSpan]) -> Sequence[DiffSpan]:
    """Combine everything between first and last change span into a single span."""

    indices = [i for i, span in enumerate(spans) if not isinstance(span, NoopSpan)]
    if len(indices) < 2:  # no spans to combine
        return spans

    first, last = indices[0], indices[-1]

    spans_to_combine: Sequence[DiffSpan] = spans[first : last + 1]

    before_text = ""
    after_text = ""
    for span in spans_to_combine:
        before_text += span.before
        after_text += span.after

    # any combination will result in a modspan
    new_span = ModSpan(before_text, after_text)

    combined_spans = [*spans[:first], new_span, *spans[last + 1 :]]

    return tuple(combined_spans)  # keep span sequences consistent with StrDiff


def combine_spans_on_same_line(
    spans: Sequence[DiffSpan],
) -> Sequence[DiffSpan]:
    """Combine spans on the same line into single long span."""

    line_num = 0
    line_num_to_span_list: dict[int, list[DiffSpan]] = defaultdict(list)
    for span in spans:
        if "\n" in span.after or "\n" in span.before:
            line_num += 1  # put this span at a separate line
            line_num_to_span_list[line_num].append(span)
            line_num += 1  # next span should go on the next line
        else:
            line_num_to_span_list[line_num].append(span)

    # combine spans for each list in the dict
    combined_spans: list[DiffSpan] = []
    for line_num in sorted(line_num_to_span_list.keys()):
        spans_list = line_num_to_span_list[line_num]
        combined_spans.extend(combine_change_spans(spans_list))

    return tuple(combined_spans)  # keep span sequences consistent with StrDiff


class _SimilarityDiffer:
    """An adaptation of difflib's Differ class.

    This implementation is mostly copied from difflib's Differ._fancy_replace method.
    """

    before_lines: Sequence[str]
    after_lines: Sequence[str]
    max_look_ahead: int = 100
    """The max number of lines we look ahead to search for similar pairs.

    The search time will be quadratic in this value, so we cap this value to avoid
    very long running times. This affects the trade-off between quality and speed.
    """

    def __init__(self, before_lines: Sequence[str], after_lines: Sequence[str]):
        self.before_lines = before_lines
        self.after_lines = after_lines
        self._cruncher = difflib.SequenceMatcher(difflib.IS_CHARACTER_JUNK)

    def get_line_diff(
        self, alo: int, ahi: int, blo: int, bhi: int
    ) -> Iterable[DiffSpan]:
        """Adapted from difflib's `_fancy_replace` method.

        `_fancy_replace` yields an iterable of diff strings, here we changed it to
        yield an iterable of DiffSpan. We also bound the worse-case running time
        using the `self.max_look_ahead` limit.
        """
        a = self.before_lines
        b = self.after_lines
        cruncher = self._cruncher

        # don't synch up unless the lines have a similarity score of at
        # least cutoff; best_ratio tracks the best score seen so far
        best_ratio, cutoff = 0.74, 0.75

        eqi, eqj = None, None  # 1st indices of equal lines (if any)
        best_i, best_j = -1, -1

        # search for the pair that matches best without being identical
        # (identical lines must be junk lines, & we don't want to synch up
        # on junk -- unless we have to)
        for j in range(blo, min(bhi, blo + self.max_look_ahead)):
            bj = b[j]
            cruncher.set_seq2(bj)
            for i in range(alo, min(ahi, alo + self.max_look_ahead)):
                ai = a[i]
                if ai == bj:
                    if eqi is None:
                        eqi, eqj = i, j
                    continue
                cruncher.set_seq1(ai)
                # computing similarity is expensive, so use the quick
                # upper bounds first -- have seen this speed up messy
                # compares by a factor of 3.
                # note that ratio() is only expensive to compute the first
                # time it's called on a sequence pair; the expensive part
                # of the computation is cached by cruncher
                if (
                    cruncher.real_quick_ratio() > best_ratio
                    and cruncher.quick_ratio() > best_ratio
                    and cruncher.ratio() > best_ratio
                ):
                    best_ratio, best_i, best_j = cruncher.ratio(), i, j

        if best_ratio < cutoff:
            # no non-identical "pretty close" pair
            if eqi is None or eqj is None:
                # no identical pair either -- treat it as a straight replace
                yield ModSpan("".join(a[alo:ahi]), "".join(b[blo:bhi]))
                return
            # no close pair, but an identical pair -- synch up on that
            best_i, best_j, best_ratio = eqi, eqj, 1.0
        else:
            # there's a close pair, so forget the identical pair (if any)
            eqi = None

        # a[best_i] very similar to b[best_j]; eqi is None iff they're not
        # identical

        # pump out diffs from before the synch point
        yield from self._spans_helper(alo, best_i, blo, best_j)

        # do intraline marking on the synch pair
        aelt, belt = a[best_i], b[best_j]
        if eqi is None:
            sync_span = ModSpan(aelt, belt)
        else:
            sync_span = NoopSpan(aelt)
        yield sync_span

        # pump out diffs from after the synch point
        yield from self._spans_helper(best_i + 1, ahi, best_j + 1, bhi)

    def _spans_helper(
        self, alo: int, ahi: int, blo: int, bhi: int
    ) -> Iterable[DiffSpan]:
        """Adapted from difflib's _fancy_helper method."""
        if alo < ahi:
            if blo < bhi:
                yield from self.get_line_diff(alo, ahi, blo, bhi)
            else:
                yield DeletedSpan("".join(self.before_lines[alo:ahi]))
        elif blo < bhi:
            yield AddedSpan("".join(self.after_lines[blo:bhi]))
        return ()


@dataclass(frozen=True)
class AddedSpan:
    """Indicates that a string has been inserted."""

    inserted: str

    def __repr__(self) -> str:
        return f"AddedSpan({self.inserted!r})"

    @property
    def before(self) -> str:
        return ""

    @property
    def after(self) -> str:
        return self.inserted

    def inverted(self) -> DeletedSpan:
        return DeletedSpan(self.inserted)


@dataclass(frozen=True)
class DeletedSpan:
    """Indicates that a string has been deleted."""

    deleted: str

    def __repr__(self) -> str:
        return f"DeletedSpan({self.deleted!r})"

    @property
    def before(self) -> str:
        return self.deleted

    @property
    def after(self) -> str:
        return ""

    def inverted(self) -> AddedSpan:
        return AddedSpan(self.deleted)


@dataclass(frozen=True)
class ModSpan:
    """Indicates that a string has been modified."""

    before: str
    after: str

    def __repr__(self) -> str:
        return f"ModSpan(before={self.before!r}, after={self.after!r})"

    def inverted(self) -> ModSpan:
        return ModSpan(self.after, self.before)


@dataclass(frozen=True)
class NoopSpan:
    """Indicates that a string has not changed."""

    text: str

    def __repr__(self) -> str:
        return f"NoopSpan({self.text!r})"

    @property
    def before(self) -> str:
        return self.text

    @property
    def after(self) -> str:
        return self.text

    def inverted(self) -> NoopSpan:
        return self


DiffSpan = Union[AddedSpan, DeletedSpan, ModSpan, NoopSpan]
"""A span in a StrDiff."""
