from base.diff_utils.git_conflict_utils import (
    contains_git_conflict_marker,
    CONF_BEGIN,
    CONF_MID,
    CONF_END,
)


class TestGitConflictMarkerDetection:
    def test_conflict_present(self):
        text = f"""This is some text.
{CONF_BEGIN} HEAD
Your changes here.
{CONF_MID}
Changes from the other branch.
{CONF_END} feature-branch
End of text."""
        assert contains_git_conflict_marker(text)

    def test_no_conflict(self):
        text = """This is some text.
No conflict markers here.
End of text."""
        assert not contains_git_conflict_marker(text)

    def test_incomplete_conflict(self):
        text = f"""This is some text.
{CONF_BEGIN} HEAD
Your changes here.
End of text."""
        assert not contains_git_conflict_marker(text)

    def test_similar_but_not_conflict(self):
        text = """This is some text.
Here is a comparison operator: x << y
And another one: x >> y
End of text."""
        assert not contains_git_conflict_marker(text)

    def test_commented_conflict_markers(self):
        text = f"""# This is a comment in code
# {CONF_BEGIN} HEAD
# Your changes here.
# {CONF_MID}
# Changes from the other branch.
# {CONF_END} feature-branch
print("End of script.")"""
        assert not contains_git_conflict_marker(text)

    def test_conflict_markers_as_strings(self):
        text = f"""print("{CONF_BEGIN} HEAD")
print("No conflict here.")
print("{CONF_MID}")
print("{CONF_END} feature-branch")"""
        assert not contains_git_conflict_marker(text)

    def test_conflict_markers_broken_sequence(self):
        text = f"""This is some text.
{CONF_BEGIN} HEAD
Your changes here.
{CONF_END} feature-branch
End of text."""
        assert not contains_git_conflict_marker(text)

    def test_conflict_markers_with_additional_characters(self):
        text = f"""This is some text.
{CONF_BEGIN} HEAD some extra text
Your changes here.
{CONF_MID}
Changes from the other branch.
{CONF_END} feature-branch some extra text
End of text."""
        assert contains_git_conflict_marker(text)
