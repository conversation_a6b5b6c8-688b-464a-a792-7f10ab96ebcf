"""Utils for working with fine-grained edit events."""

from dataclasses import dataclass
from functools import cached_property
import re
from typing import Callable, Mapping, Sequence
from enum import Enum

import dataclasses_json

from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File, compute_file_diff
from base.ranges.range_types import <PERSON>r<PERSON>ang<PERSON>
from base.static_analysis.common import assert_eq, groupby, replace_str
from base.blob_names.python.blob_names import get_blob_name
from base.logging.secret_logging import SecretLogger
from base.blob_names.python.blob_names import FilePath

FileContents = str


class ReplayEditErrorType(Enum):
    BEFORE_TEXT_MISMATCH_SINGLE = "before_text_mismatch_single"
    BEFORE_TEXT_MISMATCH_MULTI = "before_text_mismatch_multi"
    OVERLAPPING_EDITS = "overlapping_edits"


class ReplayEditError(Exception):
    """Raised when replaying edits failed."""

    def __init__(self, message: str, error_type: ReplayEditErrorType):
        super().__init__(message)
        self.error_type = error_type


@dataclass(frozen=True)
class SingleEdit(dataclasses_json.DataClassJsonMixin):
    """A single contiguous text edit."""

    before_start: int
    """The start character offset of the edit in the before version of the file."""

    before_text: str
    """The text that was removed in the before version of the file."""

    after_start: int
    """The start character offset of the edit in the after version of the file."""

    after_text: str
    """The text that was inserted in the after version of the file."""

    def reversed(self) -> "SingleEdit":
        """Get the reversed edit step."""
        return SingleEdit(
            self.after_start,
            self.after_text,
            self.before_start,
            self.before_text,
        )

    def shifted(self, offset: int) -> "SingleEdit":
        """Shift the edit by the given offset."""
        return SingleEdit(
            self.before_start + offset,
            self.before_text,
            self.after_start + offset,
            self.after_text,
        )

    def try_swap(self, next: "SingleEdit") -> tuple["SingleEdit", "SingleEdit"] | None:
        """Swap with the next edit temporally if possible.

        This assumes no other edits have happened in-between.
        """
        if self.after_crange().overlaps(next.before_crange()):
            # cannot swap if the two events overlap
            return None
        if self.after_stop <= next.before_start:
            # `self` is located before `next`, so `next`'s offset depends on whether
            # `self` happens before or after `next`
            self_net_change = len(self.after_text) - len(self.before_text)
            return next.shifted(-self_net_change), self
        else:
            # `next` is located before `self`, so `self`'s offset depends on whether
            # `next` happens before or after `self`
            next_net_change = len(next.after_text) - len(next.before_text)
            return next, self.shifted(next_net_change)

    def try_merge(self, next: "SingleEdit") -> "SingleEdit | None":
        """Try to merge this edit with the next edit.

        Returns None if the two edits cannot be merged.
        """
        if self.after_crange() == next.before_crange():
            # the next event overrides the current event
            return SingleEdit(
                before_start=self.before_start,
                after_start=self.after_start,
                before_text=self.before_text,
                after_text=next.after_text,
            )
        elif (
            next.is_deletion()
            and next.before_start >= self.after_start
            and next.before_stop <= self.after_stop
        ):
            # the next event is a deletion fully inside the first event's after range.
            new_after_text = (
                self.after_text[: next.before_start - self.after_start]
                + self.after_text[next.before_stop - self.after_start :]
            )
            return SingleEdit(
                before_start=self.before_start,
                after_start=self.after_start,
                before_text=self.before_text,
                after_text=new_after_text,
            )
        elif self.after_stop == next.before_start:
            # the next event starts right after this one.
            return SingleEdit(
                before_start=self.before_start,
                after_start=self.after_start,
                before_text=self.before_text + next.before_text,
                after_text=self.after_text + next.after_text,
            )
        elif next.before_stop == self.after_start:
            # the next event ends right before this one.
            return SingleEdit(
                before_start=next.before_start,
                after_start=next.after_start,
                before_text=next.before_text + self.before_text,
                after_text=next.after_text + self.after_text,
            )
        return None

    def before_crange(self) -> CharRange:
        return CharRange(self.before_start, self.before_stop)

    def after_crange(self) -> CharRange:
        return CharRange(self.after_start, self.after_stop)

    @cached_property
    def before_stop(self) -> int:
        return self.before_start + len(self.before_text)

    @cached_property
    def after_stop(self) -> int:
        return self.after_start + len(self.after_text)

    def is_insertion(self) -> bool:
        return self.before_text == ""

    def is_deletion(self) -> bool:
        return self.after_text == ""

    def __repr__(self) -> str:
        if self.before_start == self.after_start:
            loc_str = str(self.before_start)
        else:
            loc_str = f"{self.before_start}, {self.after_start}"
        if self.is_insertion():
            return f"SingleEdit.Insertion({loc_str}, {repr(self.after_text)})"
        if self.is_deletion():
            return f"SingleEdit.Deletion({loc_str}, {repr(self.before_text)})"
        return f"SingleEdit.Replacement({loc_str}, {repr(self.before_text)}, {repr(self.after_text)})"

    @staticmethod
    def Insertion(start: int, text: str) -> "SingleEdit":
        return SingleEdit(start, "", start, text)

    @staticmethod
    def Deletion(start: int, text: str) -> "SingleEdit":
        return SingleEdit(start, text, start, "")

    @staticmethod
    def Replacement(start: int, before_text: str, after_text: str) -> "SingleEdit":
        return SingleEdit(start, before_text, start, after_text)


@dataclass(frozen=True)
class SingleFileEdit(SingleEdit):
    """A single edit with a file path."""

    before_path: str | None
    after_path: str | None


MultiEdits = Sequence[SingleEdit]
"""An edit event that involves multiple edits at the same time.

Note that these edits are interpreted to be happening at the same time (e.g.,
they can come from an IDE multi-cursor edit), so they need to be applied in parallel.
"""


@dataclass
class ApplyEditsResult:
    """The result of applying edits to a string."""

    text: str
    """The string after applying the edits."""

    snapshots: Sequence[str]
    """The intermediate states of the string after applying each edit.

    The first snapshot is the original string, and the last snapshot is the final
    string after applying all applicable edits.
    """

    unapplied_edits: Sequence[MultiEdits]
    """The edits that were not applied because of an error."""

    error: ReplayEditError | None
    """The error that occurred when applying the edits."""

    def show_snapshots(self) -> str:
        """Show the snapshots as a sequence of diffs.

        This is useful for debugging, but is extremely slow (~15ms per snapshot pair), so it is not intended for production use.
        """
        deltas = list[str]()
        for i, (before, after) in enumerate(zip(self.snapshots, self.snapshots[1:])):
            diff = compute_file_diff(
                File(f"step {i}", before),
                File(f"step {i+1}", after),
                use_smart_header=True,
            )
            deltas.append(str(diff))
        return "\n".join(deltas)


def apply_edits(before_text: str, edits: Sequence[MultiEdits]) -> ApplyEditsResult:
    """Apply a sequence of edit events to a string."""
    text = before_text
    snapshots = [text]
    for i, steps in enumerate(edits):
        if len(steps) == 1:
            maybe_error = _apply_single_edit(text, steps[0])
        else:
            maybe_error = _apply_multi_edits(text, steps)
        if isinstance(maybe_error, ReplayEditError):
            return ApplyEditsResult(
                text=text,
                snapshots=snapshots,
                unapplied_edits=edits[i:],
                error=maybe_error,
            )
        text = maybe_error
        snapshots.append(text)
    return ApplyEditsResult(
        text=text, snapshots=snapshots, unapplied_edits=[], error=None
    )


def reverse_apply_edits(
    after_text: str, edits: Sequence[MultiEdits]
) -> ApplyEditsResult:
    """Reversely apply a sequence of edit events to a string."""
    rev_edits = [tuple(step.reversed() for step in event) for event in edits]
    rev_edits.reverse()
    return apply_edits(after_text, rev_edits)


@dataclass(frozen=True)
class GranularEditEvent(dataclasses_json.DataClassJsonMixin):
    """The edit event sent from the front end."""

    path: str
    """The path of the file being modified."""

    before_blob_name: str
    """The blob name of the file before the edit."""

    after_blob_name: str
    """The blob name of the file after the edit."""

    edits: MultiEdits
    """The edits in this event, assumed to happen in parallel."""

    def __str__(self):
        if len(self.edits) > 1:
            return f"GranularEditEvent(path={self.path}, edits={str(self.edits)})"
        else:
            return f"GranularEditEvent(path={self.path}, edit={str(self.edits[0])})"

    @cached_property
    def changedChars(self) -> int:
        """The total number of characters changed by this event."""
        return sum(len(edit.before_text) + len(edit.after_text) for edit in self.edits)

    def has_change(self) -> bool:
        """Check if this event has any change."""
        return any(edit.before_text != edit.after_text for edit in self.edits)


def collapse_edit_events(
    events: Sequence[GranularEditEvent],
) -> list[GranularEditEvent]:
    """Remove events whose net effect is no change."""
    blob_to_first_occurrence = {
        event.before_blob_name: t for t, event in reversed(list(enumerate(events)))
    }
    skipped = set[tuple[str, int]]()  # (file_name, event_index)
    kept = list[GranularEditEvent]()
    for t, event in reversed(list(enumerate(events))):
        if (event.path, t) in skipped:
            continue
        first_occurrence = blob_to_first_occurrence.get(event.after_blob_name)
        if first_occurrence is not None and first_occurrence <= t:
            # skip all events since the first occurrence of the blob name
            skipped.update((event.path, i) for i in range(first_occurrence, t))
        else:
            kept.append(event)
    return list(reversed(kept))


def truncate_edit_events(
    edit_events: Sequence[GranularEditEvent],
    max_total_changed_chars: int,
    is_source_file: Callable[[FilePath], bool],
    logger: SecretLogger,
) -> list[GranularEditEvent]:
    """Truncate and filter edit events to the max changed chars.

    Args:
        edit_events: the edit events to be truncated, assumed to be sorted by time.
        max_total_changed_chars: the sum of the size of all edit events will be\
            smaller than this limit.
        is_source_file: a function that returns whether a given path is a source file.\
            Only source file events will be included in the results.
        logger: a secret logger.

    Returns:
        The truncated edit events.
    """
    num_original_events = len(edit_events)
    edit_events = collapse_edit_events(edit_events)
    if len(edit_events) < num_original_events:
        logger.info(
            f"Collapsed {num_original_events - len(edit_events)} cancelling events."
        )
    kept = list[GranularEditEvent]()
    kept_event_chars = 0
    for event in reversed(edit_events):
        if not is_source_file(event.path):
            continue
        if len(event.edits) > 1:
            if CharRange.any_overlaps(edit.before_crange() for edit in event.edits):
                logger.warn(
                    "Truncating at a bad multi-edit event with overlapping ranges:"
                )
                logger.secret_warn(f"{event.edits=}")
                break
        event_size = event.changedChars
        if event_size > max_total_changed_chars:
            before_file = File("before_text", event.edits[0].before_text)
            after_file = File("after_text", event.edits[0].after_text)
            edit_diff = compute_file_diff(
                before_file, after_file, use_smart_header=False
            )
            logger.info(f"Got a huge edit {event_size=}:")
            logger.secret_info(
                f"Event={str(event)}\n" f"Diff of the first edit: {edit_diff}"
            )
        if kept_event_chars + event_size > max_total_changed_chars:
            break
        kept.append(event)
        kept_event_chars += event_size
    logger.info(
        f"Kept {len(kept)} out of {len(edit_events)} edit events. {kept_event_chars=}"
    )
    assert kept_event_chars <= max_total_changed_chars
    return list(reversed(kept))


def group_edit_events(
    edit_events: Sequence[GranularEditEvent],
    big_event_lines: int,
    group_sizes: Sequence[int],
    logger: SecretLogger,
) -> list[list[GranularEditEvent]]:
    """Group edit events into multiple event groups.

    We break the event timeline into at most len(group_sizes) + 1 groups.

    (The events in group 1 are the most recent.)

    earlier ------------------| .... | -----------------> | ----------------> later
               group n + 1    | .... |       group 2      |       group 1

    Group i can contain at most group_sizes[i] events, with the exception that
    group n + 1 can contain unlimited number of events.
    However, if there is a "big event" (i.e., an event that adds more than
    `big_event_lines` lines), the big event will immediately end the current group
    and be put into group i + 1. For example, if group 2 were to contain 3 events [e1, e2, e3],
    and e2 is a big event, then group 2 will only contain [e3], and e1 and e2 will
    be pushed into group 3 or later. This ensures that the big event e2 doesn't
    "mask out" the later events in the same group. This "big event split" rule can only
    be triggered at most once.

    This function returns all groups in chronological order, i.e.,
    [group 3, group 2, group 1]. The events in each group are also sorted in
    chronological order.

    When counting the number of events, any event containing only whitespaces or
    braces are not counted.

    Args:
        edit_events: the edit events to be grouped, assumed to be sorted by time.
        big_event_lines: events that add more than this number of lines will be\
            considered as the big event defined above.
        logger: a secret logger.
        group_sizes: the maximum number of events in each group.

    Returns:
        The grouped edit events.
    """

    def is_whitespace_or_braces(text: str) -> bool:
        pattern = r"^[\s()\[\]{}]*$"
        return bool(re.match(pattern, text))

    def is_whitespace_or_braces_event(event: GranularEditEvent) -> bool:
        return all(
            is_whitespace_or_braces(edit.before_text)
            and is_whitespace_or_braces(edit.after_text)
            for edit in event.edits
        )

    groups = list[list[GranularEditEvent]]()
    cur_group = list[GranularEditEvent]()
    cur_group_size = 0
    big_event_seen = False
    # note that we process the edit events in reverse order, so in the comments
    # below, "latest" events refers to the ones that are processed first
    for event in reversed(edit_events):
        # If this edit is too large, end the current group so that the big edit will be
        # put into the previous group. We use max instead of sum since this tries to
        # capture big copy-paste events, which will reflect as large amount of lines in
        # each single edit.
        if (
            not big_event_seen
            and len(groups) < len(group_sizes)
            and (
                num_edit_lines := (
                    max(len(e.after_text.splitlines()) for e in event.edits)
                    if event.edits
                    else 0
                )
            )
            >= big_event_lines
        ):
            logger.info(
                f"Break the group at large edit event ({num_edit_lines} lines):"
            )
            logger.secret_info(f"{event=}")
            if cur_group:
                groups.append(cur_group)
            cur_group = list[GranularEditEvent]()
            cur_group_size = 0
            big_event_seen = True
        event_size = 0 if is_whitespace_or_braces_event(event) else 1

        # Check if the group size would exceed the limit.
        if (
            len(groups) < len(group_sizes)
            and cur_group_size + event_size > group_sizes[len(groups)]
        ):
            groups.append(cur_group)
            cur_group = list[GranularEditEvent]()
            cur_group_size = 0

        cur_group.append(event)
        cur_group_size += event_size

    if cur_group:
        groups.append(cur_group)

    total_events = sum(len(group) for group in groups)
    assert_eq(total_events, len(edit_events))

    # remove empty groups
    groups = [group for group in groups if group]
    logger.info(f"Grouped {total_events} edit events into {len(groups)} groups.")

    # fix events order
    for group in groups:
        group.reverse()
    groups.reverse()

    assert len(groups) <= len(group_sizes) + 1, f"{len(groups)=}, {group_sizes=}"
    return groups


def reorder_merge_events(
    events: Sequence[GranularEditEvent], last_n: int = 50, lookback_n: int = 10
) -> list[GranularEditEvent]:
    """Reorder and merge the `last_n` events with the previous events if possible.

    Returns:
        The reordered events. Note that some events may have unknown blob names,
        which will be marked as "?".
    """
    # drop all no-op events
    events = [e for e in events if e.has_change()]

    # below will at most merge the last n events
    for i in reversed(range(last_n)):
        slice_stop = max(0, len(events) - i)
        ids = slice(max(0, slice_stop - lookback_n), slice_stop)
        if not ids:
            continue
        new_events = _try_reorder_merge_last_event(events[ids])
        if new_events is None:
            continue
        events = events[: ids.start] + new_events + events[ids.stop :]

    return events


def _try_reorder_merge_last_event(
    events: Sequence[GranularEditEvent],
) -> list[GranularEditEvent] | None:
    """Try to merge the last edit event with an earlier event.

    This algorithm works by taking the last event in the timeline and repeatedly
    checking if it can be merged with the previous event. If it cannot, try to instead
    swap it with the previous event. This process repeats until (1) the event has
    been successfully merged with an earlier event, or (2) the event cannot be
    merged or swapped with the previous event.

    Returns:
        A new sequence of events, or None if failed to merge the last event.
    """
    events = list(events)
    if len(events) <= 1 or len(events[-1].edits) != 1:
        return None

    last_event = events[-1]
    path = last_event.path
    for i in reversed(range(0, len(events) - 1)):
        prev_event = events[i]
        if prev_event.path != path:
            # always safe to swap with an event from another file
            events[i], events[i + 1] = last_event, prev_event
            continue
        if len(prev_event.edits) != 1:
            # cannot proceed if the previous event has multiple edits
            break
        prev_edit = prev_event.edits[0]
        # try to merge this with the previous event
        merged_edit = prev_edit.try_merge(last_event.edits[0])
        if merged_edit is not None:
            merged_event = GranularEditEvent(
                path,
                before_blob_name=prev_event.before_blob_name,
                after_blob_name=last_event.after_blob_name,
                edits=[merged_edit],
            )
            events[i] = merged_event
            del events[i + 1]
            if merged_edit.before_text == merged_edit.after_text:
                # merged event is no change, so drop it as well
                del events[i]
            return events

        # failed to merge, try to swap instead
        swapped_edits = prev_edit.try_swap(last_event.edits[0])
        if swapped_edits is None:
            # cannot swap, giving up
            return None
        new_before_edit, new_after_edit = swapped_edits
        last_event = events[i] = GranularEditEvent(
            path,
            before_blob_name=prev_event.before_blob_name,
            after_blob_name="?",
            edits=[new_before_edit],
        )
        events[i + 1] = GranularEditEvent(
            path,
            before_blob_name="?",
            after_blob_name=last_event.after_blob_name,
            edits=[new_after_edit],
        )
    # no merge happened, giving up
    return None


def _edit_events_to_file_changes(
    edit_events: Sequence[GranularEditEvent],
    path_to_text_after_events: Mapping[FilePath, FileContents],
    logger: SecretLogger,
) -> list[Modified[File]]:
    """Convert granular edit events into a list of Modified[File].

    Edit events are reversely applied to the final text to obtain a list
    of file modifications. The list is sorted by last modified time of each file.

    Args:
        edit_events: the edit events to be converted.
        path_to_text_after_events: map from file path to file text after all edit_events.
            The content of the file is expected to match the content after the last edit event at that path.
            If the content does not match, an error message will be logged.
        logger: a secret logger.

    Returns:
        A list of Modified[File] objects.
    """
    path_to_events = groupby(edit_events, lambda event: event.path)
    path_to_file_change = dict[str, Modified[File]]()

    for path, events in path_to_events.items():
        if not events:
            continue

        # get the expected file content after all edits
        expected_content_after_all_edit_events = path_to_text_after_events.get(path)
        if expected_content_after_all_edit_events is None:
            logger.warn("Expected file content after all edits missing: ")
            logger.secret_warn(f"{path=}, {events[-1]=}")
            continue

        expected_blob_name_after_all_edit_events = get_blob_name(
            path, expected_content_after_all_edit_events
        )

        # check that last event after blob name matches the expected_file.blob_name
        if expected_blob_name_after_all_edit_events != events[-1].after_blob_name:
            logger.warn(
                f"After blob name of the last edit event doesn't match the expected blob name after all edits: "
                f"{expected_blob_name_after_all_edit_events=}, {events[-1].after_blob_name=}"
            )
            continue

        # reversely apply the edit events to get before file state
        before_text_result = reverse_apply_edits(
            expected_content_after_all_edit_events, [event.edits for event in events]
        )

        # check for errors when reversely applying the edit events
        if before_text_result.error:
            unapplied_str = "\n".join(
                f"  {i}:" + str(event)
                for i, event in enumerate(before_text_result.unapplied_edits)
            )
            n_unapplied = len(before_text_result.unapplied_edits)
            logger.warn("Failed to reversely apply edit events for file:")
            logger.secret_warn(
                f"path={path}\n"
                f"Error: {before_text_result.error}\n"
                f"{n_unapplied} Unapplied Events: {unapplied_str}\n"
            )

        path_to_file_change[path] = Modified(
            before=File(path, before_text_result.text),
            after=File(path, expected_content_after_all_edit_events),
        )

    # map each path to the index of the last edit event for that path
    path_to_last_edit_event_index = {
        event.path: t for t, event in enumerate(edit_events)
    }

    # sort the changed paths by the index of their last edit event (ascending)
    paths_sorted_by_mod_time = sorted(
        path_to_file_change.keys(), key=lambda path: path_to_last_edit_event_index[path]
    )

    # the file with the last edit event (i.e. the most recently modified file) will be last in the list
    return [path_to_file_change[path] for path in paths_sorted_by_mod_time]


@dataclass
class ReconstructedFileChangeGroups:
    changes: list[Modified[File]]
    error_messages: list[str]


def grouped_events_to_file_changes(
    grouped_events: Sequence[Sequence[GranularEditEvent]],
    path_to_content_after_all_edits: Mapping[FilePath, FileContents],
    logger: SecretLogger,
) -> ReconstructedFileChangeGroups:
    """Convert grouped edit events into ReconstructedFileChangeGroups.

    Args:
        grouped_events: the grouped edit events.
        path_to_content_after_all_edits: map from file path to file contents.
            The content of the file is expected to match the content after the last edit event at that path.
            If the content does not match, an error message will be logged.
        logger: a secret logger.

    Returns:
        A ReconstructedFileChangeGroups object.

    """
    path_to_content = dict(path_to_content_after_all_edits)
    reconstructed_files = dict[FilePath, str]()

    file_change_groups = list[list[Modified[File]]]()

    # reversely process each event group
    for events in reversed(grouped_events):
        changes = _edit_events_to_file_changes(events, path_to_content, logger)
        if not changes:
            continue
        file_change_groups.append(changes)

        # the content needs to be after all edits in the current group
        # setting the content to be the before content of the current group achieves this
        for change in changes:
            before_text = change.before.contents
            # not all entries in `path_to_content` are files that have been changed,
            # so we record the two dictionaries separately
            path_to_content[change.before.path] = before_text
            reconstructed_files[change.before.path] = before_text

    # convert to the original order
    changed_files = [
        change for group in reversed(file_change_groups) for change in group
    ]

    # check that the reconstructed file blob names match the before blob names recorded
    # in the edit events.
    path_to_before_blob_expected = {
        event.path: event.before_blob_name
        for group in reversed(grouped_events)
        for event in reversed(group)
    }
    bad_files = list[str]()
    for path, text in reconstructed_files.items():
        if path not in path_to_before_blob_expected:
            logger.warn("Expected blob name missing unexpectedly:")
            logger.secret_warn(f"{path=}")
            continue
        before_blob_expected = path_to_before_blob_expected[path]
        if before_blob_expected == "?":
            # cannot verify
            logger.warn("Cannot verify blob name:")
            logger.secret_warn(f"{path=}")
            continue
        actual_blob = get_blob_name(path, text)
        if actual_blob != before_blob_expected:
            bad_files.append(f"  {path}: {actual_blob} != {before_blob_expected}")
    if bad_files:
        logger.warn(
            f"{len(bad_files)} reconstructed files do not have expected blob names:"
        )
        logger.secret_warn("\n".join(bad_files))

    return ReconstructedFileChangeGroups(changed_files, bad_files)


@dataclass
class SquashableEdits:
    """Containing edit events ready to be squashed into a list of Changed Files."""

    edit_events: list[GranularEditEvent]
    """The edit events to be reversely applied to the current files."""

    path_to_current_content: dict[FilePath, str]
    """Map the path to the current content of the files mentioned by the edits."""

    def convert_edit_events_to_modified_files(
        self,
        safe_logger: SecretLogger,
        is_source_file: Callable[[FilePath], bool] = lambda _: True,
        max_total_changed_chars: int = 5000,
        big_event_lines: int = 8,
        group_sizes: Sequence[int] = (1,),
    ) -> Sequence[Modified[File]]:
        """Convert the edit events to a sequence of modified files.

        Args:
            edit_events: the recorded edit events from the front end.
                Assumed to be sorted by time with the oldest event first in the list.

            path_to_expected_content: a dictionary mapping a path to the expected content at that path.
                The content of the file is expected to match the content after the last edit event at that path.

            safe_logger: a secret logger.

        Returns:
            A sequence of Modified[File] objects with the before and after content of the files touched by the edit events.

        """

        # truncate the edit events, dropping the oldest events (first in the list) if necessary
        filtered_events = truncate_edit_events(
            edit_events=self.edit_events,
            max_total_changed_chars=max_total_changed_chars,
            is_source_file=is_source_file,
            logger=safe_logger,
        )

        # group the edit events into multiple groups
        # the group with the oldest edit events comes first in the list
        # within each group, edit events are also sorted from oldest to newest
        groupped_events = group_edit_events(
            filtered_events,
            group_sizes=group_sizes,
            big_event_lines=big_event_lines,
            logger=safe_logger,
        )

        # sorted from least recently modified to most recently modified
        recently_changed_files = grouped_events_to_file_changes(
            groupped_events,
            self.path_to_current_content,
            logger=safe_logger,
        ).changes

        return recently_changed_files


def _apply_single_edit(text: str, step: SingleEdit) -> str | ReplayEditError:
    """Apply a single edit step to a string."""
    found_text = text[step.before_start : step.before_stop]
    if found_text != step.before_text:
        return ReplayEditError(
            f"{found_text=} != {step.before_text=}, {step=}",
            ReplayEditErrorType.BEFORE_TEXT_MISMATCH_SINGLE,
        )
    return text[: step.before_start] + step.after_text + text[step.before_stop :]


def _apply_multi_edits(text: str, steps: MultiEdits) -> str | ReplayEditError:
    """Apply multiple edit steps in parallel to a string."""
    if not steps:
        return text
    for step in steps:
        found_text = text[step.before_start : step.before_stop]
        if found_text != step.before_text:
            return ReplayEditError(
                f"{found_text=} != {step.before_text=}, {step=}",
                ReplayEditErrorType.BEFORE_TEXT_MISMATCH_MULTI,
            )
    replacements = [
        (CharRange(step.before_start, step.before_stop), step.after_text)
        for step in steps
    ]
    if CharRange.any_overlaps(r for r, _ in replacements):
        return ReplayEditError(
            f"Simultaneous edits with overlapping ranges: {steps}",
            ReplayEditErrorType.OVERLAPPING_EDITS,
        )
    return replace_str(text, replacements)
