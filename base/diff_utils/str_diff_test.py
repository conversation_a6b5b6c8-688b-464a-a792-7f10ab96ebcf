"""Unit tests for the str_diff module."""

from functools import partial
from random import Random
from typing import Callable

import pytest

from base.diff_utils.str_diff import (
    AddedSpan,
    DeletedSpan,
    ModSpan,
    NoopSpan,
    StrDiff,
    align_spans_to_word_boundaries,
    combine_change_spans,
    combine_spans_on_same_line,
    line_diff,
    precise_char_diff,
    precise_line_diff,
)
from base.ranges.line_map import LineMap
from base.ranges.range_types import <PERSON>rRang<PERSON>
from base.test_utils.testing_utils import (
    assert_eq,
    assert_str_eq,
    error_context,
    random_str,
    run_property_test,
)

DiffFn = Callable[[str, str], StrDiff]


class StrDiffTestsBase:
    """Implements tests for various StrDiff builder implementations.

    They are invoked in a parameterized fashion either here or in resesearch.
    """

    def test_reconstruction(self, alg: DiffFn):
        """Test that we can reconstruct the original texts from the diff."""

        def test(rng: Random):
            text1 = random_str(rng)
            text2 = random_str(rng)
            diff = alg(text1, text2)
            assert_str_eq(diff.get_before(), text1)
            assert_str_eq(diff.get_after(), text2)

        run_property_test(test)

    def test_invert(self, alg: DiffFn):
        """Test that we can invert a diff."""

        def test(rng: Random):
            text1 = random_str(rng)
            text2 = random_str(rng)
            diff = alg(text1, text2)
            inv_diff = diff.inverted
            assert_str_eq(inv_diff.get_before(), text2)
            assert_str_eq(inv_diff.get_after(), text1)

        run_property_test(test)

    def test_span_ranges_in_after(self, alg: DiffFn):
        """Test that the computed after range is correct."""

        def test(rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            after_ranges = diff.span_ranges_in_after
            for span, after_range in zip(diff.spans, after_ranges):
                assert_str_eq(span.after, text_after[after_range.to_slice()])

        run_property_test(test)

    def test_before_to_after_range_mapping(self, alg: DiffFn):
        """Test various properties of the before-to-after mapping."""

        def test(rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            for span, before_span_range, after_span_range in zip(
                diff.spans, diff.span_ranges_in_before, diff.span_ranges_in_after
            ):
                if isinstance(span, NoopSpan):
                    # Test that each position within a noop span is mapped to another
                    # exact location.
                    # We don't test the boundary locations since those might
                    # not have a uniquely mapped location if adjacent to a change.
                    for offset in range(1, len(before_span_range)):
                        before_point = CharRange.point(before_span_range.start + offset)
                        mapped_point = diff.before_range_to_after(before_point)
                        expected = CharRange.point(after_span_range.start + offset)
                        assert_eq(mapped_point, expected)
                else:
                    # Test that each modifed span is mapped to potentially larger range
                    # (the range gets larger if there are ambiguous arrangements like an
                    # addition right next to a deletion)
                    mapped_range = diff.before_range_to_after(before_span_range)
                    if not mapped_range.contains(after_span_range):
                        raise AssertionError(
                            f"{mapped_range=}, {after_span_range=}, {span=}"
                        )

        run_property_test(test)

    def test_after_to_before_range_mapping(self, alg: DiffFn):
        """Test additional properties of the after-to-before mapping."""

        def test(rng: Random):
            # ** Property 1 **
            # after-to-before mapping should be equivalent to before-to-after mapping
            # with an inverted diff.
            text_before = random_str(rng)
            text_after = random_str(rng)
            # sample a random range inside text_after
            rand_start = rng.randint(0, len(text_after))
            rand_stop = rng.randint(rand_start, len(text_after))
            after_range = CharRange(rand_start, rand_stop)
            diff = alg(text_before, text_after)
            inv_diff = diff.inverted
            actual = diff.after_range_to_before(after_range)
            expected = inv_diff.before_range_to_after(after_range)
            assert_eq(actual, expected)

            # ** Property 2 **
            # Map then unmap should always give a maybe larger range.
            rand_start = rng.randint(0, len(text_before))
            rand_stop = rng.randint(rand_start, len(text_before))
            before_range = CharRange(rand_start, rand_stop)
            after_range = diff.before_range_to_after(before_range)
            remapped = diff.after_range_to_before(after_range)
            assert remapped.contains(
                before_range
            ), f"{remapped} not contained in {before_range}"

        run_property_test(test)

    def test_map_unmap_is_idempotent(self, alg: DiffFn):
        """Test that map-then-unmap is an idempotent operation.

        This ensures that map-then-unmap can be used to extend a character range
        such that its before range and after range can be unambiguously mapped
        to each other. This property is used by `EditGenSampler`.
        """

        # The precise_linediff algorithm is not idempotent since it can produce additions
        # and deletions right next to modifications
        if alg == precise_line_diff:
            return

        def test(rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            # sample a random range inside text_before
            rand_start = rng.randint(0, len(text_before))
            rand_stop = rng.randint(rand_start, len(text_before))
            before_range = CharRange(rand_start, rand_stop)
            after_range = diff.before_range_to_after(before_range)
            before_range1 = diff.after_range_to_before(after_range)
            after_range1 = diff.before_range_to_after(before_range1)
            with error_context(
                f"diff={diff}\n"
                f"{before_range=}, {after_range=}, {before_range1=}, {after_range1=}"
            ):
                assert_eq(after_range1, after_range)

        run_property_test(test)

    def test_group_into_hunks(self, alg: DiffFn):
        """Test that we correctly group nearby changes into hunks."""

        def test(context_lines: int, skip_grouping: bool, rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            # this should merge nearby changes into larger spans
            if not skip_grouping:
                # property 2 below should fail if we skip this line
                diff = diff.group_into_hunks(context_lines)
            # Property 1: the new diff matches the original strings
            assert_str_eq(diff.get_before(), text_before)
            assert_str_eq(diff.get_after(), text_after)

            # Property 2: after grouping, adjacent changes are more than context_lines apart
            change_ranges = [
                crange
                for span, crange in zip(diff.spans, diff.span_ranges_in_after)
                if not isinstance(span, NoopSpan)
            ]
            lmap = LineMap(text_after)
            for prev_range, next_range in zip(change_ranges, change_ranges[1:]):
                prev_line = lmap.get_line_number(prev_range.stop)
                next_line = lmap.get_line_number(next_range.start)
                # adjacent changes should be more than context_lines apart
                if abs(prev_line - next_line) <= context_lines:
                    raise AssertionError(
                        f"Changes should be more than {context_lines} lines apart."
                        f"Got diff:\n{str(diff)}"
                    )

        for context_lines in range(0, 3):
            # check that the test passes if we group_by_hunks
            skip_grouping = False
            run_property_test(partial(test, context_lines, skip_grouping))

            if context_lines == 0 and alg == line_diff:
                # linediff with context_lines=0 is a no-op, so we skip below
                continue
            # then double check that the test fails if we skip grouping
            with pytest.raises(AssertionError):
                skip_grouping = True
                run_property_test(partial(test, context_lines, skip_grouping))

    def test_no_adjacent_noop_spans(self, alg: DiffFn):
        """Test that there are no adjacent noop spans in the result."""

        def test(rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            for prev_span, next_span in zip(diff.spans, diff.spans[1:]):
                if isinstance(prev_span, NoopSpan) and isinstance(next_span, NoopSpan):
                    raise AssertionError(
                        f"Adjacent noop spans: {prev_span}, {next_span}"
                    )

        run_property_test(test)

    def test_no_tiny_noop_spans(self, alg: DiffFn):
        """Test that modifications spans are not separated by tiny noop spans.

        (Unless the noop span contains a newline)
        """

        def test(rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            for span1, span2, span3 in zip(diff.spans, diff.spans[1:], diff.spans[2:]):
                if (
                    isinstance(span2, NoopSpan)
                    and len(span2.text) < 3
                    and "\n" not in span2.text
                ):
                    raise AssertionError(f"Tiny noop span: {span1}, {span2}, {span3}")

        run_property_test(test)

    def test_align_spans_reconstruction(self, alg: DiffFn):
        def test(rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            # this should merge nearby changes into larger spans
            expanded_spans = tuple(align_spans_to_word_boundaries(diff.spans))
            new_diff = StrDiff(expanded_spans)
            # Property 1: the new diff matches the original strings
            assert_str_eq(new_diff.get_before(), text_before)
            assert_str_eq(new_diff.get_after(), text_after)

        run_property_test(test)

    def test_combine_spans_reconstruction(self, alg: DiffFn):
        def test(rng: Random):
            text_before = random_str(rng)
            text_after = random_str(rng)
            diff = alg(text_before, text_after)
            # this should merge nearby changes into larger spans
            expanded_spans = tuple(combine_change_spans(diff.spans))
            new_diff = StrDiff(expanded_spans)
            # Property 1: the new diff matches the original strings
            assert_str_eq(new_diff.get_before(), text_before)
            assert_str_eq(new_diff.get_after(), text_after)

        run_property_test(test)

    def test_align_start_mod_span_to_word_boundaries(self, alg: DiffFn):
        example_diff_spans = (
            ModSpan("Tru", "Fals"),
            NoopSpan("e\n"),
        )
        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)
        for span in expanded_spans:
            match span:
                case NoopSpan(text):
                    assert text == "\n"
                case ModSpan(before, after):
                    assert before == "True"
                    assert after == "False"

    def test_align_middle_mod_span_to_word_boundaries(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("A"),
            ModSpan("nn", "p"),
            NoopSpan("a"),
        )

        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)
        for span in expanded_spans:
            match span:
                case NoopSpan(text):
                    assert text == ""
                case ModSpan(before, after):
                    assert before == "Anna"
                    assert after == "Apa"

    def test_align_end_mod_span_to_word_boundaries(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("E"),
            ModSpan("dvin", "lsa"),
        )
        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)
        for span in expanded_spans:
            match span:
                case NoopSpan(text):
                    assert text == ""
                case ModSpan(before, after):
                    assert before == "Edvin"
                    assert after == "Elsa"

    def test_align_spans_multiple_spans(self, alg: DiffFn):
        example_diff_spans = (
            ModSpan("Fals", "Tru"),
            NoopSpan("e"),
            ModSpan("Fals", "Tru"),
            NoopSpan("e"),
            ModSpan("Fals", "Tru"),
            NoopSpan("e"),
        )

        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)

        for span in expanded_spans:
            match span:
                case NoopSpan(text):
                    assert text == ""
                case ModSpan(before, after):
                    assert before == "False"
                    assert after == "True"

    def test_align_spans_stop_at_underscore(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("test_under"),
            ModSpan("whelming", "score"),
            NoopSpan("_behaviour"),
        )

        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)

        assert expanded_spans == (
            NoopSpan("test_"),
            ModSpan("underwhelming", "underscore"),
            NoopSpan("_behaviour"),
        )

    def test_align_spans_stop_at_newline(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("First line\nsec"),
            ModSpan("ond", "ure"),
            NoopSpan("\n"),
        )

        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)

        assert expanded_spans == (
            NoopSpan("First line\n"),
            ModSpan("second", "secure"),
            NoopSpan("\n"),
        )

    def test_align_spans_stop_at_mixed_punctuation(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("We think that E"),
            ModSpan("dvin", "lsa"),
            NoopSpan("(is th.e"),
            ModSpan("worst", "best"),
        )

        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)

        assert expanded_spans == (
            NoopSpan("We think that "),
            ModSpan("Edvin", "Elsa"),
            NoopSpan("(is th."),
            ModSpan("eworst", "ebest"),
        )

    def test_align_add_spans(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("We are"),
            AddedSpan("n't"),
            NoopSpan(" family"),
        )

        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)

        assert expanded_spans == (
            NoopSpan("We "),
            ModSpan("are", "aren't"),
            NoopSpan(" family"),
        )

    def test_align_delete_spans(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("Can (word_inside_"),
            DeletedSpan("paren"),
            NoopSpan("thesis) be aligned?"),
        )

        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)

        assert expanded_spans == (
            NoopSpan("Can (word_inside_"),
            ModSpan("parenthesis", "thesis"),
            NoopSpan(") be aligned?"),
        )

    def test_combine_two_add_spans(self, alg: DiffFn):
        example_diff_spans = (
            NoopSpan("We are"),
            AddedSpan("n't"),
            NoopSpan(" family"),
            AddedSpan("!"),
        )
        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)
        combined_spans = combine_change_spans(expanded_spans)

        assert combined_spans == (
            NoopSpan("We "),
            ModSpan("are family", "aren't family!"),
        )

    def test_combine_spans_separated_by_line(self, alg: DiffFn):
        example_diff_spans = (
            AddedSpan("first "),
            NoopSpan("second\n"),
            AddedSpan("third "),
            NoopSpan("fourth"),
            AddedSpan("!"),
        )
        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)
        combined_spans = combine_spans_on_same_line(expanded_spans)

        assert combined_spans == (
            AddedSpan("first "),
            NoopSpan("second\n"),
            ModSpan("fourth", "third fourth!"),
        )

    def test_combine_spans_separated_by_multiple_lines(self, alg: DiffFn):
        example_diff_spans = (
            AddedSpan("first "),
            NoopSpan("second\nthird\nfourth\n"),
            AddedSpan("fifth "),
            NoopSpan("sixth"),
            AddedSpan("!"),
        )
        expanded_spans = align_spans_to_word_boundaries(example_diff_spans)
        combined_spans = combine_spans_on_same_line(expanded_spans)

        assert combined_spans == (
            AddedSpan("first "),
            NoopSpan("second\nthird\nfourth\n"),
            ModSpan("sixth", "fifth sixth!"),
        )


@pytest.mark.parametrize("alg", [line_diff, precise_line_diff, precise_char_diff])
class TestStrDiff(StrDiffTestsBase):
    """This tests the diff algorithms defined in base."""


def test_consecutive_modifications_mapping():
    """Test that consecutive modifications are correctly mapped."""
    mod_spans = (
        ModSpan("a", "b"),
        ModSpan("b", "c"),
        ModSpan("c", "d"),
        ModSpan("d", "e"),
    )
    diff = StrDiff(mod_spans)
    for span, before_range, after_range in zip(
        diff.spans, diff.span_ranges_in_before, diff.span_ranges_in_after
    ):
        with error_context(f"{span=}, {before_range=}, {after_range=}"):
            assert_eq(diff.before_range_to_after(before_range), after_range)
            assert_eq(diff.after_range_to_before(after_range), before_range)
