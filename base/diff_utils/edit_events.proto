syntax = "proto3";
package base.diff_utils;

// SingleEdit represents a single edit operation within a file.
// It captures the before and after state of a specific text change.
message SingleEdit {
  // The starting character position of the edit in the original text.
  uint32 before_start = 1;

  // The text that was present before the edit.
  string before_text = 2;

  // The starting character position where the new text is inserted.
  uint32 after_start = 3;

  // The new text that replaces the 'before_text'.
  string after_text = 4;
}

// GranularEditEvent represents a more detailed edit event in a specific file.
// It provides information about the file path, blob names before and after the edit,
// and a list of individual edits that occurred.
message GranularEditEvent {
  // The path of the file that was edited.
  string path = 1;

  // The name of the blob that was present before the edit.
  string before_blob_name = 2;

  // The name of the blob that was present after the edit.
  string after_blob_name = 3;

  // A list of individual edits that make up this edit event.
  repeated SingleEdit edits = 4;
}
