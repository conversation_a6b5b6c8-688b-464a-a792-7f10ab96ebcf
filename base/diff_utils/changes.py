"""Utility class to represent generic changes."""

from __future__ import annotations

from dataclasses import dataclass
from typing import Callable, Generic, TypeVar, Union, assert_never

T = TypeVar("T", covariant=True)
U = TypeVar("U", covariant=True)


@dataclass(frozen=True)
class Added(Generic[T]):
    """Represents a value that has been added."""

    after: T

    def map(self, f: Callable[[T], U]) -> Added[U]:
        return Added(f(self.after))

    def get_later(self) -> T:
        return self.after


@dataclass(frozen=True)
class Deleted(Generic[T]):
    """Represents a value that has been removed."""

    before: T

    def map(self, f: Callable[[T], U]) -> Deleted[U]:
        return Deleted(f(self.before))

    def get_later(self) -> T:
        return self.before


@dataclass(frozen=True)
class Modified(Generic[T]):
    """Represents a value that has been changed."""

    before: T
    after: T

    def map(self, f: Callable[[T], U]) -> Modified[U]:
        return Modified(f(self.before), f(self.after))

    @property
    def before_after(self) -> tuple[T, T]:
        """Return the before and after values."""
        return self.before, self.after

    def get_later(self) -> T:
        return self.after


@dataclass(frozen=True)
class Unchanged(Generic[T]):
    """Represents a value that has not been changed."""

    value: T

    @property
    def before(self) -> T:
        return self.value

    @property
    def after(self) -> T:
        return self.value

    def map(self, f: Callable[[T], U]) -> Unchanged[U]:
        return Unchanged(f(self.value))

    def get_later(self) -> T:
        return self.value


MaybeChanged = Union[Added[T], Deleted[T], Modified[T], Unchanged[T]]
"""Represents a value that may have changed."""

Changed = Union[Added[T], Deleted[T], Modified[T]]
"""Represents an addition, deletion, or modification to a value."""


def get_before(change: MaybeChanged[T]) -> T | None:
    """Get the value before the change or None."""
    if isinstance(change, Added):
        return None
    else:
        return change.before


def get_after(change: MaybeChanged[T]) -> T | None:
    """Get the value after the change or None."""
    if isinstance(change, Deleted):
        return None
    else:
        return change.after


def reverse_changed(change: Changed[T]) -> Changed[T]:
    match change:
        case Added(after):
            return Deleted(after)
        case Deleted(before):
            return Added(before)
        case Modified(before, after):
            return Modified(after, before)
        case _:
            assert_never(change)


def reverse_maybe_changed(change: MaybeChanged[T]) -> MaybeChanged[T]:
    match change:
        case Unchanged(value):
            return Unchanged(value)
        case _:
            return reverse_changed(change)
