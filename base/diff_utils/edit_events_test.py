from typing import Sequence
from base.diff_utils.edit_events import (
    GranularEditEvent,
    ReplayEditError,
    MultiEdits,
    SingleEdit,
    apply_edits,
    collapse_edit_events,
    group_edit_events,
    grouped_events_to_file_changes,
    reorder_merge_events,
    reverse_apply_edits,
    _edit_events_to_file_changes,
)
from base.blob_names.python.blob_names import get_blob_name

from base.logging.secret_logging import UnsafeLogger

event_logger = UnsafeLogger()


def to_granular_edit_events(
    path: str,
    contents: str,
    edits: Sequence[MultiEdits | SingleEdit],
) -> list[GranularEditEvent]:
    """Convert a list of edits to a list of granular edit events."""
    if not edits:
        return []
    results = list[GranularEditEvent]()
    blob_name = get_blob_name(path, contents)
    for edit in edits:
        if isinstance(edit, SingleEdit):
            edit = [edit]
        applied_result = apply_edits(contents, [edit])
        if applied_result.error:
            raise ReplayEditError(
                f"Failed to apply edits: {applied_result.error}\n"
                f"Edits: {edit}\n"
                f"Contents: {repr(contents)}\n",
                applied_result.error.error_type,
            )
        after_contents = applied_result.text
        after_blob_name = get_blob_name(path, after_contents)
        results.append(GranularEditEvent(path, blob_name, after_blob_name, edit))
        blob_name = after_blob_name
        contents = after_contents
    return results


def check_edits(before: str, edits: Sequence[MultiEdits], after: str):
    # test forward application
    assert apply_edits(before, edits).text == after

    # test backward application
    assert reverse_apply_edits(after, edits).text == before


def test_apply_edits_basic_cases():
    # single edits
    check_edits("abc", [[SingleEdit(0, "a", 0, "x")]], "xbc")
    check_edits("abc", [[SingleEdit(1, "b", 1, "x")]], "axc")
    check_edits("abc", [[SingleEdit(2, "c", 2, "x")]], "abx")

    # single insertion
    check_edits("abc", [[SingleEdit(0, "", 0, "x")]], "xabc")
    check_edits("abc", [[SingleEdit(1, "", 1, "x")]], "axbc")
    check_edits("abc", [[SingleEdit(2, "", 2, "x")]], "abxc")
    check_edits("abc", [[SingleEdit(3, "", 3, "x")]], "abcx")

    # single deletion
    check_edits("abc", [[SingleEdit(0, "a", 0, "")]], "bc")
    check_edits("abc", [[SingleEdit(1, "b", 1, "")]], "ac")
    check_edits("abc", [[SingleEdit(2, "c", 2, "")]], "ab")

    # parallel edits
    check_edits(
        "abc",
        [[SingleEdit(0, "a", 0, "xy"), SingleEdit(2, "c", 3, "z")]],
        "xybz",
    )

    # sequential edits
    check_edits(
        "abc",
        [
            [SingleEdit(0, "a", 0, "x")],
            [SingleEdit(1, "b", 1, "y")],
            [SingleEdit(2, "c", 2, "z")],
        ],
        "xyz",
    )

    check_edits(
        "abc",
        [
            [SingleEdit(0, "a", 0, "xx")],  # xxbc
            [SingleEdit(1, "x", 1, "yyy")],  # xyyybc
            [SingleEdit(3, "y", 3, "zzz")],  # xyyzzzbc
        ],
        "xyyzzzbc",
    )


def get_example_edits() -> list[GranularEditEvent]:
    path = "example.py"
    code = """\
def fib(x):
    pass
"""
    edit1_start = len("def fib(x")
    edit1 = [SingleEdit(edit1_start, "", edit1_start, ": int")]

    edit2_start = len("def fib(x: int)")
    edit2 = [SingleEdit(edit2_start, ":", edit2_start, " -> int:")]

    edit3_start = len("def fib(x: int) -> int:\n    ")
    edit3 = [SingleEdit(edit3_start, "pass", edit3_start, "return")]

    edit4_start = edit3_start + len("return") - len("pass")
    edit4 = [SingleEdit(edit4_start, "", edit4_start, " fib(x - 1)")]

    edit5_start = edit4_start + len(" ")
    edit5 = [
        SingleEdit(
            edit5_start, "fib(x - 1)", edit5_start, "(fib(x - 1) \n    + fib(x - 2))"
        )
    ]

    edit6_start = len("def fib")
    edit6 = [SingleEdit(edit6_start, "", edit6_start, "onacci")]

    return to_granular_edit_events(
        path,
        code,
        [edit1, edit2, edit3, edit4, edit5, edit6],
    )


def test_group_edit_events():
    events = get_example_edits()
    groups = group_edit_events(
        events,
        big_event_lines=10,
        group_sizes=[4, 100],
        logger=event_logger,
    )
    assert groups[0] == events[0:2]
    assert groups[1] == events[2:6]
    assert len(groups) == 2

    groups = group_edit_events(
        events,
        big_event_lines=10,
        group_sizes=[3, 2],
        logger=event_logger,
    )
    assert groups[0] == events[0:1]
    assert groups[1] == events[1:3]
    assert groups[2] == events[3:6]
    assert len(groups) == 3

    groups = group_edit_events(
        events,
        big_event_lines=2,  # edit 5 exceeds this limit
        group_sizes=[5, 100],
        logger=event_logger,
    )
    assert groups[0] == events[0:5]
    assert groups[1] == events[5:6]
    assert len(groups) == 2

    big_event = GranularEditEvent(
        "example.py",
        "blob1",
        "blob2",
        [SingleEdit.Insertion(0, "a\nb\nc\n")],
    )

    whitespace_event = GranularEditEvent(
        "example.py",
        "blob2",
        "blob3",
        [SingleEdit.Insertion(0, " ([])\n")],  # only whitespace and parenthesis
    )

    groups = group_edit_events(
        events + [big_event] + events + [whitespace_event],
        big_event_lines=3,
        group_sizes=[3, 100],
        logger=event_logger,
    )
    assert groups[0] == events + [big_event]
    assert groups[1] == events[:3]
    # the last group contains that extra whitespace event because it is not counted
    # toward the `n_recent_events` limit
    assert groups[2] == events[3:] + [whitespace_event]
    assert len(groups) == 3


def test_group_edit_events_whitespaces():
    # Check that whitespace-only edit events don't count toward the group size limit.
    path = "example.py"
    code = """\
def fib(x):
    pass
"""
    edit1_start = len("def fib(x")
    edit1 = [SingleEdit(edit1_start, "", edit1_start, ":int")]

    edit2_start = len("def fib(x")
    edit2 = [SingleEdit(edit2_start, "", edit2_start, " ")]  # whitespace-only edit

    edit3_start = len("def fib(x: int):")
    edit3 = [
        SingleEdit(edit3_start, "\n", edit3_start, "\t \n")
    ]  # whitespace-only edit

    edit4_start = 0
    edit4 = [SingleEdit(edit4_start, "", edit4_start, "# comment1\n")]

    edit5_start = 0
    edit5 = [SingleEdit(edit5_start, "# comment1", edit5_start, "# new comment")]

    edit_events = to_granular_edit_events(
        path,
        code,
        [edit1, edit2, edit3, edit4, edit5],
    )

    groups = group_edit_events(
        edit_events,
        big_event_lines=10,
        group_sizes=[1, 1],
        logger=event_logger,
    )
    assert groups[-1] == edit_events[4:5]
    assert groups[-2] == edit_events[1:4]
    assert groups[-3] == edit_events[0:1]
    assert len(groups) == 3


def test_collapse_edit_events():
    # case 1: no events in the example should be collapsed
    events = get_example_edits()
    assert len(events) == 6
    collapsed = collapse_edit_events(events)
    assert len(collapsed) == 6

    # case 2: same file events
    events = [
        GranularEditEvent(
            "example.py",
            "blob1",
            "blob2",
            [],
        ),  # should keep
        GranularEditEvent(
            "example.py",
            "blob2",
            "blob3",
            [],
        ),  # should drop
        GranularEditEvent(
            "example.py",
            "blob3",
            "blob4",
            [],
        ),  # should drop
        GranularEditEvent(
            "example.py",
            "blob4",
            "blob2",
            [],
        ),  # should drop
        GranularEditEvent(
            "example.py",
            "blob2",
            "blob8",
            [],
        ),
    ]
    collapsed = collapse_edit_events(events)
    assert collapsed == [
        GranularEditEvent(
            "example.py",
            "blob1",
            "blob2",
            [],
        ),
        GranularEditEvent(
            "example.py",
            "blob2",
            "blob8",
            [],
        ),
    ]

    # case 3: different file events
    events = [
        GranularEditEvent(
            "file1.py",
            "blob1-1",
            "blob1-2",
            [],
        ),  # should drop
        GranularEditEvent(
            "file2.py",
            "blob2-1",
            "blob2-2",
            [],
        ),  # should drop
        GranularEditEvent(
            "file1.py",
            "blob1-2",
            "blob1-3",
            [],
        ),  # should drop
        GranularEditEvent(
            "file2.py",
            "blob2-2",
            "blob2-3",
            [],
        ),  # should drop
        GranularEditEvent(
            "file1.py",
            "blob1-3",
            "blob1-1",
            [],
        ),  # should drop
        GranularEditEvent(
            "file2.py",
            "blob2-3",
            "blob2-1",
            [],
        ),  # should drop
        GranularEditEvent(
            "file2.py",
            "blob2-1",
            "blob2-8",
            [],
        ),  # should keep
    ]
    collapsed = collapse_edit_events(events)
    assert collapsed == [
        GranularEditEvent(
            "file2.py",
            "blob2-1",
            "blob2-8",
            [],
        ),
    ]


def test_event_swap():
    # case 1: cannot swap
    edit1 = SingleEdit.Insertion(0, "abc")
    edit2 = SingleEdit.Deletion(1, "b")
    assert edit1.try_swap(edit2) is None

    # case 2: swap two insertions
    edit1 = SingleEdit.Insertion(0, "abc")
    edit2 = SingleEdit.Insertion(0, "xyz")
    assert edit1.try_swap(edit2) == (
        SingleEdit.Insertion(0, "xyz"),
        SingleEdit.Insertion(3, "abc"),
    )

    # case 3: swap two deletions
    edit1 = SingleEdit.Deletion(0, "abc")
    edit2 = SingleEdit.Deletion(0, "xyz")
    assert edit1.try_swap(edit2) == (
        SingleEdit.Deletion(3, "xyz"),
        SingleEdit.Deletion(0, "abc"),
    )

    # case 4: swap an insertion and a deletion
    edit1 = SingleEdit.Insertion(0, "abc")
    edit2 = SingleEdit.Deletion(3, "xyz")
    assert edit1.try_swap(edit2) == (
        SingleEdit.Deletion(0, "xyz"),
        SingleEdit.Insertion(0, "abc"),
    )


class TestEventMerge:
    def test_merge_adjacent_events(self):
        add_event1 = SingleEdit.Insertion(3, "hello")
        add_event2 = SingleEdit.Insertion(8, " world")

        # add_event2 is right-adjacent to add_event1
        assert add_event1.try_merge(add_event2) == SingleEdit.Insertion(
            3, "hello world"
        )

        # replace_event is left-adjacent to add_event1
        replace_event = SingleEdit.Replacement(0, "is ", "I say: ")
        assert add_event1.try_merge(replace_event) == SingleEdit.Replacement(
            0, "is ", "I say: hello"
        )

    def test_merge_in_range_deletion(self):
        # in-range deletion will be merged as a special case
        add_event = SingleEdit.Insertion(3, "hallo")
        delete_event = SingleEdit.Deletion(4, "a")
        assert add_event.try_merge(delete_event) == SingleEdit.Insertion(3, "hllo")

    def test_not_merge_non_adjacent_events(self):
        add_event = SingleEdit.Insertion(3, "hallo")
        replace_event = SingleEdit.Replacement(4, "a", "eee")
        assert add_event.try_merge(replace_event) is None

    def test_merge_override_event(self):
        add_event = SingleEdit.Insertion(3, "hello")
        replace_event = SingleEdit.Replacement(3, "hello", "goodbye")
        assert add_event.try_merge(replace_event) == SingleEdit.Insertion(3, "goodbye")


def get_back_forth_edits() -> list[GranularEditEvent]:
    path = "example.py"
    code = """\
def func1(x):
    pass

def func2(a):
    pass
"""
    # edit 1: insert a comma after `x`
    edit1_start = len("def func1(x")
    edit1 = [SingleEdit.Insertion(edit1_start, ", ")]

    # edit 2: insert param `b` after `a`
    edit2_start = len("""def func1(x, ):
    pass

def func2(a""")
    edit2 = [SingleEdit.Insertion(edit2_start, ", b")]

    # edit 3: insert a comment before `def func1`
    edit3 = [SingleEdit.Insertion(0, "# a test\n")]

    # edit 4: remove the comma after `x`
    edit4_start = len("# a test\ndef func1(x")
    edit4 = [SingleEdit.Deletion(edit4_start, ",")]

    return to_granular_edit_events(
        path,
        code,
        [edit1, edit2, edit3, edit4],
    )


def test_reorder_merge_back_and_forth_events():
    # when the last event can be merged with an earlier event, it should be merged
    events = get_back_forth_edits()
    reordered_events = reorder_merge_events(events)

    # new edit 1: insert a whitespace after `x` (since this event got merged)
    new_edit1_start = len("def func1(x")
    new_edit1 = SingleEdit.Insertion(new_edit1_start, " ")

    # new edit 2: insert param `b` after `a`
    new_edit2_start = len("""def func1(x ):
    pass

def func2(a""")
    new_edit2 = SingleEdit.Insertion(new_edit2_start, ", b")

    # new edit 3: insert a comment before `def func1`
    new_edit3 = SingleEdit.Insertion(0, "# a test\n")

    reordered_edits = [event.edits for event in reordered_events]
    assert reordered_edits == [
        [new_edit1],
        [new_edit2],
        [new_edit3],
    ]


def test_reorder_merge_no_op():
    # if the last event cannot merged, it should be left as is
    event1 = GranularEditEvent(
        "example.py",
        "blob1",
        "blob2",
        [SingleEdit.Insertion(0, "abc")],
    )
    # event2 is not adjacent to event1
    event2 = GranularEditEvent(
        "example.py",
        "blob2",
        "blob3",
        [SingleEdit.Insertion(5, "xyz")],
    )
    reordered = reorder_merge_events([event1, event2])
    assert reordered == [event1, event2]


def test_reorder_merge_consecutive_deletions():
    # if there are two consecutive deletions, they should be merged together
    event1 = GranularEditEvent(
        "example.py",
        "blob1",
        "blob2",
        [SingleEdit.Insertion(0, "abc")],
    )
    event2 = GranularEditEvent(
        "example.py",
        "blob2",
        "blob3",
        [SingleEdit.Deletion(3, "d")],
    )
    event3 = GranularEditEvent(
        "example.py",
        "blob3",
        "blob4",
        [SingleEdit.Deletion(3, "e")],
    )

    reordered = reorder_merge_events([event1, event2, event3])
    assert reordered == [
        GranularEditEvent(
            "example.py",
            "blob1",
            "blob4",
            [SingleEdit.Replacement(0, "de", "abc")],
        ),
    ]


def test_reorder_merge_canceling_events():
    # event 1 and 4 can be merged together
    event1 = GranularEditEvent(
        "example.py",
        "blob1",
        "blob2",
        [SingleEdit.Insertion(0, "abc")],
    )
    event4 = GranularEditEvent(
        "example.py",
        "blob2",
        "blob3",
        [SingleEdit.Insertion(3, "def")],
    )

    # event 2 and 3 will cancel each other out
    event2 = GranularEditEvent(
        "example.py",
        "blob1",
        "blob4",
        [SingleEdit.Insertion(1, "xyz")],
    )
    event3 = GranularEditEvent(
        "example.py",
        "blob4",
        "blob1",
        [SingleEdit.Deletion(1, "xyz")],
    )

    reordered = reorder_merge_events([event1, event2, event3, event4])
    assert reordered == [
        GranularEditEvent(
            "example.py",
            "blob1",
            "blob3",
            [SingleEdit.Insertion(0, "abcdef")],
        ),
    ]


def test_edit_events_to_file_changes():
    events = to_granular_edit_events(
        "example.py",
        "kkkremoved123456",
        [
            SingleEdit.Insertion(0, "abc"),
            SingleEdit.Replacement(3, "kkk", "def"),
            SingleEdit.Deletion(6, "removed"),
        ],
    )
    current_file_texts = {
        "example.py": "abcdef123456",
    }
    rst = _edit_events_to_file_changes(events, current_file_texts, logger=event_logger)
    assert len(rst) == 1
    # Applying `event.edits` to `before.contents` should result in `after.contents`.
    assert rst[0].before.contents == "kkkremoved123456"
    assert rst[0].after.contents == current_file_texts["example.py"]

    # if the blob name of the last event doesn't match the current file, then we cannot
    # apply these events
    last_event = events[-1]
    events[-1] = GranularEditEvent(
        "example.py",
        before_blob_name=last_event.before_blob_name,
        after_blob_name="won't match",
        edits=last_event.edits,
    )
    rst = _edit_events_to_file_changes(events, current_file_texts, logger=event_logger)
    assert len(rst) == 0


def test_groupped_events_to_file_changes():
    events = to_granular_edit_events(
        "example.py",
        "original",
        [
            [SingleEdit.Insertion(0, "hello")],
            [SingleEdit.Replacement(len("hello"), "origi", "fictio")],
            [SingleEdit.Insertion(len("hello"), ": ")],
        ],
    )
    current_file_texts = {
        "example.py": "hello: fictional",
    }
    # first test everything as a single group
    file_changes = grouped_events_to_file_changes(
        [events], current_file_texts, logger=event_logger
    )
    assert not file_changes.error_messages
    [file_change] = file_changes.changes
    assert file_change.before.contents == "original"
    assert file_change.after.contents == "hello: fictional"

    # now test each edit as an individual group
    file_changes = grouped_events_to_file_changes(
        [[e] for e in events], current_file_texts, logger=event_logger
    )
    assert not file_changes.error_messages
    [change1, change2, change3] = file_changes.changes
    assert change1.before.contents == "original"
    assert change1.after.contents == "hellooriginal"
    assert change2.before.contents == "hellooriginal"
    assert change2.after.contents == "hellofictional"
    assert change3.before.contents == "hellofictional"
    assert change3.after.contents == "hello: fictional"
