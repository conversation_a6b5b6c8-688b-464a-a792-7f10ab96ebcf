load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    data = [
        ":edit_events_py_proto",
    ],
    visibility = ["//base:__subpackages__"],
)

proto_library(
    name = "edit_events_proto",
    srcs = ["edit_events.proto"],
    visibility = ["//visibility:public"],
)

py_grpc_library(
    name = "edit_events_py_proto",
    protos = [":edit_events_proto"],
    visibility = ["//visibility:public"],
)

py_library(
    name = "proto_wrapper",
    srcs = ["proto_wrapper.py"],
    visibility = [
        "//base:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":diff_utils",
        ":edit_events_py_proto",
        "//base/static_analysis:proto_convertor",
    ],
)

go_proto_library(
    name = "edit_events_go_proto",
    importpath = "github.com/augmentcode/augment/base/diff_utils/proto",
    proto = ":edit_events_proto",
    visibility = ["//visibility:public"],
)

py_library(
    name = "diff_utils",
    srcs = [
        "apply_replacements_to_files.py",
        "changes.py",
        "diff_formatter.py",
        "diff_formatter_completions.py",
        "diff_utils.py",
        "edit_events.py",
        "git_conflict_utils.py",
        "retriever_util_completion.py",
        "str_diff.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/caching:lru_cache",
        "//base/datasets:recency_info",
        "//base/languages:language_guesser",
        "//base/logging:secret_logging",
        "//base/prompt_format:common",
        "//base/prompt_format:util",
        "//base/ranges",
        "//base/static_analysis:common",
        "//base/static_analysis:smart_header",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "str_diff_test",
    srcs = [
        "str_diff_test.py",
    ],
    deps = [
        ":diff_utils",
        "//base/test_utils:testing_utils",
    ],
)

pytest_test(
    name = "diff_utils_test",
    srcs = [
        "diff_utils_test.py",
    ],
    deps = [
        ":diff_utils",
        "//base/test_utils:testing_utils",
    ],
)

pytest_test(
    name = "diff_formatter_test",
    srcs = [
        "diff_formatter_test.py",
    ],
    deps = [
        ":diff_utils",
        "//base/test_utils:testing_utils",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "diff_formatter_completions_test",
    srcs = [
        "diff_formatter_completions_test.py",
    ],
    deps = [
        ":diff_utils",
        "//base/test_utils:testing_utils",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "edit_events_test",
    srcs = [
        "edit_events_test.py",
    ],
    deps = [
        ":diff_utils",
    ],
)

pytest_test(
    name = "git_conflict_utils_test",
    srcs = [
        "git_conflict_utils_test.py",
    ],
    deps = [
        ":diff_utils",
    ],
)

pytest_test(
    name = "apply_replacements_to_files_test",
    srcs = [
        "apply_replacements_to_files_test.py",
    ],
    deps = [
        ":diff_utils",
    ],
)

ts_proto_library(
    name = "edit_events_ts_proto",
    node_modules = "//:node_modules",
    proto = ":edit_events_proto",
    visibility = ["//visibility:public"],
)
