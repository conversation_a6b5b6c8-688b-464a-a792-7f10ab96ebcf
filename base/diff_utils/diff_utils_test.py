import pytest

from base.diff_utils.changes import Changed
from base.diff_utils.diff_utils import (
    File,
    UnifiedDiffHunk,
    UnifiedDiffLines,
    UnifiedDiffLineType,
    compute_file_diff,
)
from base.ranges.range_types import LineRange
from base.test_utils.testing_utils import assert_str_eq


def test_compute_file_diff_modified():
    before_code = """\
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        line 4
        line 5
        return line 6
"""

    after_code = """\
# header
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        newline
        line 4
        line 5
        return line 6
# footer
"""

    before_file = File(path="example.py", contents=before_code)
    after_file = File(path="example.py", contents=after_code)
    diff = compute_file_diff(
        before_file, after_file, use_smart_header=False, num_context_lines=1
    )
    expected_basic_header = """\
--- example.py
+++ example.py
@@ -1,1 +1,2 @@
+# header
 class SomeClass:
@@ -11,2 +12,3 @@ class SomeClass:
         line 3
+        newline
         line 4
@@ -14,1 +16,2 @@ class SomeClass:
         return line 6
+# footer
"""
    assert_str_eq(diff, expected_basic_header)


def test_compute_file_diff_smart_header():
    before_code = """\
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        line 4
        return line 5
"""

    after_code = """\
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        newline
        line 4
        return line 5
"""

    before_file = File(path="example.py", contents=before_code)
    after_file = File(path="example.py", contents=after_code)
    diff = compute_file_diff(before_file, after_file, use_smart_header=True)

    expected_smart_header = """\
--- example.py
+++ example.py
@@ -9,5 +9,6 @@
@class SomeClass:
@    def some_method(x):
         line 1
         line 2
         line 3
+        newline
         line 4
         return line 5
"""
    diff = compute_file_diff(before_file, after_file, use_smart_header=True)
    assert_str_eq(diff, expected_smart_header)

    # if we increase the context lines to 5, then the class should become the header
    # again
    diff = compute_file_diff(
        before_file, after_file, use_smart_header=True, num_context_lines=5
    )
    expected_smart_header_ctx5 = """\
--- example.py
+++ example.py
@@ -7,7 +7,8 @@
@class SomeClass:
     def some_method(x):
         '''some method doc string'''
         line 1
         line 2
         line 3
+        newline
         line 4
         return line 5
"""
    assert_str_eq(diff, expected_smart_header_ctx5)

    # if we reduce the smart header size limit, we should see truncation
    diff = compute_file_diff(
        before_file, after_file, use_smart_header=True, max_smart_header_chars=14
    )
    expected_smart_header_truncated = """\
--- example.py
+++ example.py
@@ -9,5 +9,6 @@
@class Some...
@    def so...
         line 1
         line 2
         line 3
+        newline
         line 4
         return line 5
"""
    assert_str_eq(diff, expected_smart_header_truncated)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_added(use_smart_header: bool):
    added_file = File(path="added.txt", contents="class Added:\n    pass\n")
    diff = compute_file_diff(None, added_file, use_smart_header)
    expected_diff = """\
--- /dev/null
+++ added.txt
@@ -0,0 +1,2 @@
+class Added:
+    pass
"""
    assert_str_eq(diff, expected_diff)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_remove(use_smart_header: bool):
    removed_file = File(path="removed.txt", contents="class Removed:\n    pass\n")
    diff = compute_file_diff(removed_file, None, use_smart_header)
    expected_diff = """\
--- removed.txt
+++ /dev/null
@@ -1,2 +0,0 @@
-class Removed:
-    pass
"""
    assert_str_eq(diff, expected_diff)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_rename(use_smart_header: bool):
    before = File(path="before.txt", contents="before\n")
    after = File(path="after.txt", contents="before\n")
    diff = compute_file_diff(before, after, use_smart_header)
    expected_diff = """\
--- before.txt
+++ after.txt
"""
    assert_str_eq(diff, expected_diff)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_rename_and_change(use_smart_header: bool):
    before = File(path="before.txt", contents="before\n")
    after = File(path="after.txt", contents="after\n")
    diff = compute_file_diff(before, after, use_smart_header)
    expected_diff = """\
--- before.txt
+++ after.txt
@@ -1,1 +1,1 @@
-before
+after
"""
    assert_str_eq(diff, expected_diff)


def _line_type_from_str(line_type: str) -> UnifiedDiffLineType:
    if line_type == " ":
        return UnifiedDiffLineType.COMMON
    elif line_type == "-":
        return UnifiedDiffLineType.DELETION
    elif line_type == "+":
        return UnifiedDiffLineType.ADDITION
    else:
        raise ValueError(f"Unknown line type: {line_type}")


def _unified_diff_hunk(
    before_lrange: tuple[int, int],
    after_lrange: tuple[int, int],
    hunk_heading: str,
    diff_lines: list[tuple[str, str]],
) -> UnifiedDiffHunk:
    hunk = UnifiedDiffHunk(
        before_lrange=LineRange(*before_lrange),
        after_lrange=LineRange(*after_lrange),
        hunk_heading=hunk_heading,
        lines=[
            UnifiedDiffLines(
                lines.splitlines(keepends=True), _line_type_from_str(line_type)
            )
            for lines, line_type in diff_lines
        ],
    )
    assert hunk.validate()
    return hunk


@pytest.mark.parametrize(
    "hunk, expected",
    [
        pytest.param(
            _unified_diff_hunk((0, 2), (0, 2), "", [("line 1\nline 2\n", " ")]),
            """\
@@ -1,2 +1,2 @@
 line 1
 line 2
""",
            id="common",
        ),
        pytest.param(
            _unified_diff_hunk(
                (0, 2), (0, 1), "", [("line 1\n", " "), ("line 2\n", "-")]
            ),
            """\
@@ -1,2 +1,1 @@
 line 1
-line 2
""",
            id="deletion",
        ),
        pytest.param(
            _unified_diff_hunk(
                (0, 1), (0, 2), "", [("line 1\n", " "), ("line 2\n", "+")]
            ),
            """\
@@ -1,1 +1,2 @@
 line 1
+line 2
""",
            id="addition",
        ),
        pytest.param(
            _unified_diff_hunk(
                (0, 2),
                (0, 2),
                "",
                [("line 1\n", " "), ("line 2\n", "-"), ("line 3\n", "+")],
            ),
            """\
@@ -1,2 +1,2 @@
 line 1
-line 2
+line 3
""",
            id="replace",
        ),
        pytest.param(
            _unified_diff_hunk(
                (0, 2), (0, 2), " heading\n", [("line 1\nline 2\n", " ")]
            ),
            """\
@@ -1,2 +1,2 @@ heading
 line 1
 line 2
""",
            id="heading",
        ),
        pytest.param(
            _unified_diff_hunk((0, 1), (0, 1), "", [("line 1", " ")]),
            """\
@@ -1,1 +1,1 @@
 line 1
\\ No newline at end of file
""",
            id="no_newline",
        ),
    ],
)
def test_unified_diff_hunk_format_to_str(hunk: UnifiedDiffHunk, expected: str):
    assert hunk.format_to_str() == expected
