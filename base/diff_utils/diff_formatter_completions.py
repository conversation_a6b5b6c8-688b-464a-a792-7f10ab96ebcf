from typing import Sequence
from base.tokenizers.tokenizer import Tokenizer, RagSpecialTokens
from base.prompt_format.common import PromptChunk, TokenList
from dataclasses import dataclass


@dataclass
class TokenizedPromptChunk:
    header_tokens: TokenList
    """The tokens for the file header."""
    body_tokens: TokenList
    """The tokens for the diff body."""

    def num_tokens(self) -> int:
        return len(self.header_tokens) + len(self.body_tokens)


def tokenize_prompt_chunks(
    prompt_chunks: Sequence[PromptChunk],
    diff_token_budget: int,
    tokenizer: Tokenizer,
) -> list[TokenizedPromptChunk]:
    """Tokenize prompt chunks to fit the a given token budget.

    All chunks will be formatted with a file path header.
    Note that the path is actually a path header (constructed via show_path_change) that shows the path change and is assumed to end with a \n.
    """
    assert isinstance(tokenizer.special_tokens, RagSpecialTokens)
    result = list[TokenizedPromptChunk]()
    tkns_used = 0
    for chunk in prompt_chunks:
        body_text = chunk.text
        path_header_text = chunk.path
        body_tks = tokenizer.tokenize_safe(body_text)
        path_header_tks = tokenizer.tokenize_safe(path_header_text)
        token_usage = len(path_header_tks) + len(body_tks)

        if tkns_used + token_usage > diff_token_budget:
            # skip this hunk and break
            break

        result.append(
            TokenizedPromptChunk(header_tokens=path_header_tks, body_tokens=body_tks)
        )

        tkns_used += token_usage
    assert (
        tkns_used <= diff_token_budget
    ), f"Token budget exceeded: {tkns_used} > {diff_token_budget}"
    return result


def format_tokenized_prompt_chunks(
    tokenized_chunks: Sequence[TokenizedPromptChunk],
) -> TokenList:
    """Convert tokenized prompt chunks into tokens while removing duplicated path headers."""
    result = TokenList()
    prev_header = None
    for t_chunk in tokenized_chunks:
        if t_chunk.header_tokens != prev_header:
            result += t_chunk.header_tokens
        result += t_chunk.body_tokens
        prev_header = t_chunk.header_tokens
    return result
