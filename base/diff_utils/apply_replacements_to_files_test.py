"""Tests for apply_replacements_to_files."""

from base.diff_utils.apply_replacements_to_files import (
    apply_replacements_to_files,
)
from base.diff_utils.diff_utils import File
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.datasets.recency_info import ReplacementText
from base.logging.secret_logging import UnsafeLogger
import logging

test_logger = UnsafeLogger(logging.getLogger(__name__))


def test_apply_replacements_to_files_positive():
    """Test that recent changes are processed correctly."""
    old_file = File(path="path1", contents="current code")
    new_file = File(path="path1", contents="new code")

    changes = [
        # this change will replace the entire file from old to new
        ReplacementText(
            blob_name=old_file.blob_name,
            path=old_file.path,
            crange=CharRange(0, len(old_file.contents)),
            replacement_text=new_file.contents,
            present_in_blob=False,
            expected_blob_name=new_file.blob_name,
        ),
    ]
    files_after_replacements = list(
        apply_replacements_to_files(changes, [old_file], test_logger)
    )
    assert len(files_after_replacements) == 1
    assert isinstance(files_after_replacements[0], File)
    assert files_after_replacements[0].path == "path1"
    assert files_after_replacements[0].contents == "new code"


def test_apply_replacements_to_files_negative():
    """Test that recent changes will not get returned when blob name doesn't match."""
    old_file = File(path="path1", contents="current code")
    new_file = File(path="path1", contents="new code")

    changes = [
        # this change is bad because the range doesn't fully replace the old file
        ReplacementText(
            blob_name=old_file.blob_name,
            path=old_file.path,
            crange=CharRange(0, len(old_file.contents) - 2),
            replacement_text=new_file.contents,
            present_in_blob=False,
            expected_blob_name=new_file.blob_name,
        ),
        # this change is also bad because it mentions a nonexistent blob name
        ReplacementText(
            blob_name="not existed",
            path=old_file.path,
            crange=CharRange(0, len(old_file.contents)),
            replacement_text=new_file.contents,
            present_in_blob=False,
            expected_blob_name=new_file.blob_name,
        ),
    ]
    files_after_replacements = apply_replacements_to_files(
        changes, [old_file], test_logger
    )
    for file_or_error in files_after_replacements:
        assert not isinstance(file_or_error, File)
