from dataclasses import dataclass
from typing import Sequence, Dict, Mapping
from abc import ABC, abstractmethod
from base.logging.secret_logging import <PERSON><PERSON><PERSON><PERSON>
from base.diff_utils.edit_events import GranularEditEvent, SquashableEdits
from base.datasets.recency_info import ReplacementText
from base.blob_names.python.blob_names import B<PERSON>b<PERSON><PERSON>, FilePath
from base.diff_utils.diff_utils import File
from base.diff_utils.apply_replacements_to_files import (
    apply_replacements_to_files,
    FileReplacementError,
)


@dataclass
class EditEventConstructionInput:
    """Inputs required to construct edit events for completions."""

    active_file_path: str
    active_file_prefix: str
    active_file_suffix: str
    recent_changes: Sequence[ReplacementText]
    edit_events: list[GranularEditEvent]

    @property
    def active_file(self) -> File:
        """Construct the active file from the input."""
        return File(
            path=self.active_file_path,
            contents=self.active_file_prefix + self.active_file_suffix,
        )


class FileContentProvider(ABC):
    """Abstract base class for file retrieval strategies."""

    @abstractmethod
    def retrieve_files(
        self,
        paths: Dict[B<PERSON><PERSON>Name, FilePath],
        expected: bool,
    ) -> Dict[BlobName, File | None]:
        """Retrieve files based on blob names and paths."""
        pass


def filter_replacement_text(
    replacements: Sequence[ReplacementText],
) -> Sequence[ReplacementText]:
    """Filter out recent changes that are already present in the blob or are chat recency messages."""
    replacements = tuple(filter(lambda x: not x.present_in_blob, replacements))
    # if blob and path are both empty, drop the replacement as it is a chat recency message
    replacements = tuple(
        filter(lambda x: not (x.blob_name == "" and x.path == ""), replacements)
    )
    # NOTE(pranay): I kept this line of code for a future reminder,
    # but right now it is safer to attempt to reconstruct the active file as we do not ensure that the active file contains the entire file.
    # replacements = tuple(filter(lambda x: x.path != active_file.path, replacements))
    return replacements


def get_files_before_replacement(
    replacements: Sequence[ReplacementText],
    file_content_provider: FileContentProvider,
    expected: bool,
) -> list[File]:
    """Get the files before the replacements."""
    blob_name_to_path = {r.blob_name: r.path for r in replacements}
    blob_name_to_file = file_content_provider.retrieve_files(
        paths=blob_name_to_path, expected=expected
    )
    files_before_replacements = [
        file for file in blob_name_to_file.values() if file is not None
    ]
    return files_before_replacements


def apply_replacements(
    replacements: Sequence[ReplacementText],
    files_before_replacements: list[File],
    safe_logger: SecretLogger,
) -> tuple[list[File], list[FileReplacementError]]:
    """Apply the replacements to the files before replacements."""

    reconstructed_files: list[File] = []
    replacement_error_files: list[FileReplacementError] = []

    for file_or_error in apply_replacements_to_files(
        replacements,
        files_before_replacements,
        safe_logger,
    ):
        if isinstance(file_or_error, File):
            reconstructed_files.append(file_or_error)
        elif isinstance(file_or_error, FileReplacementError):
            replacement_error_files.append(file_or_error)

    return reconstructed_files, replacement_error_files


def _get_files_from_blob_names(
    blob_name_to_file_path: Mapping[BlobName, FilePath],
    reconstructed_files: dict[BlobName, File],
    file_content_provider: FileContentProvider,
    safe_logger: SecretLogger,
) -> dict[BlobName, File | None]:
    """Get files from blob names.
    For each blob name, attempts to first get from reconstructed files, then from cache. If both fail, logs an error and returns None

    Args:
        blob_names: the blob names to get files for.

    Returns:
        A list of files for the given blob names.
    """
    # TODO(pranay): expected=False is fine for now, but we should only report blob_missing if the blob_name is in the blob_names list.
    blob_name_to_file = {}
    remaining_blob_names_to_path = {}
    for blob_name, path in blob_name_to_file_path.items():
        # Attempt 1: Try to get blob name from reconstructed files
        file = reconstructed_files.get(blob_name)
        if file:
            blob_name_to_file[blob_name] = file
        else:
            remaining_blob_names_to_path[blob_name] = path

    # Attempt 2: Try to get blob name from indexed files for remaining blob names
    remaining_blob_name_to_file = file_content_provider.retrieve_files(
        paths=remaining_blob_names_to_path,
        expected=False,
    )
    blob_name_to_file.update(remaining_blob_name_to_file)

    # If both attempts fail, log error
    for blob_name, file in blob_name_to_file.items():
        if file is None:
            safe_logger.warn(
                f"Could not find reconstructed after blob - missing recent changes since indexed {blob_name=}"
            )
            safe_logger.secret_warn(f"{blob_name_to_file_path[blob_name]=}")

    return blob_name_to_file


def get_squashable_edits(
    edit_events: list[GranularEditEvent],
    reconstructed_files: dict[BlobName, File],
    file_content_provider: FileContentProvider,
    safe_logger: SecretLogger,
) -> "SquashableEdits":
    """Create SquashableEdits from edit events and their current content.
    Note that the primary logic is copied from `next_edit_handler.py`.
    """

    # events are sorted from oldest to newest
    # this will map the path to the newest blob name for that path
    path_to_current_blob: dict[FilePath, BlobName] = {
        event.path: event.after_blob_name for event in edit_events
    }
    current_blob_to_path: dict[BlobName, FilePath] = {
        v: k for k, v in path_to_current_blob.items()
    }
    expected_blob_count = len(current_blob_to_path)

    blob_to_file = _get_files_from_blob_names(
        current_blob_to_path,
        reconstructed_files,
        file_content_provider,
        safe_logger=safe_logger,
    )

    path_to_current_content: dict[FilePath, str] = {
        path: file.contents
        for path, blob_name in path_to_current_blob.items()
        if (file := blob_to_file.get(blob_name))
    }
    found_blob_count = len(path_to_current_content)

    safe_logger.info(
        f"Found {found_blob_count} / {expected_blob_count} up-to-date files for edit events."
    )

    return SquashableEdits(
        edit_events=edit_events,
        path_to_current_content=path_to_current_content,
    )
