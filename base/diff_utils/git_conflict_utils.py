"""
Utils for detecting Git conflict markers in text.
"""

CONF_BEGIN = "<<<<<<< "
CONF_MID = "======="
CONF_END = ">>>>>>> "


def contains_git_conflict_marker(text: str):
    """
    Returns wether the given text contains Git conflict markers.
    """
    lines = text.splitlines()
    i = 0
    while i < len(lines):
        if lines[i].startswith(CONF_BEGIN):
            # Look for '======='
            j = i + 1
            while j < len(lines) and not lines[j].startswith(CONF_MID):
                j += 1
            if j == len(lines):
                # '=======' not found
                return False
            # Look for '>>>>>>> '
            k = j + 1
            while k < len(lines) and not lines[k].startswith(CONF_END):
                k += 1
            if k == len(lines):
                # '>>>>>>> ' not found
                return False
            return True
        i += 1
    return False
