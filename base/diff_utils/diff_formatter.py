"""Utilities to format a list of file changes into tokens."""

from dataclasses import dataclass
from pathlib import Path
from typing import Callable, Sequence

from base.diff_utils.changes import Changed, get_after, get_before
from base.diff_utils.diff_utils import (
    DiffHunk,
    File,
    compute_file_diff,
    compute_file_diff_hunks,
)
from base.languages.language_guesser import guess_language
from base.prompt_format.common import TokenList
from base.tokenizers.tokenizer import RagSpecialTokens, Tokenizer


def _default_diff_filter(path: Path) -> bool:
    """Filter out diffs coming from certain file paths."""
    return (
        # Filter out diffs from files with unknown languages -- this removes e.g.
        # package lock files and data files.
        guess_language(str(path).lower()) is not None
        # Filter out notebook diffs since they contain more than user code.
        and path.suffix != ".ipynb"
        and not str(path).endswith(".min.js")
    )


def format_file_changes(
    changes: Sequence[Changed[File]],
    diff_context_lines: int,
    diff_filter: Callable[[Path], bool] = _default_diff_filter,
    deduplicate_identical_paths: bool = True,
    use_smart_header: bool = True,
) -> str:
    """Format a list of file changes as a diff prompt string.

    See `format_file_changes_with_ranges`; this function drops the character ranges.

    Args:
        changes: The list of file changes.
        diff_context_lines: The number of context lines to show in the diff.
        diff_filter: Filter out the diffs coming from certain file paths.
        deduplicate_identical_paths: Whether to remove duplicated file paths from\
            the diff. With this enabled, instead of showing\
            `--- src/file.py\\n+++ src/file.py`, we only show `+++ src/file.py`.
        use_smart_header: Whether show smart header in the diff.
    """
    diff_hunk_texts = list[str]()
    for change in changes:
        if (before := get_before(change)) and not diff_filter(Path(before.path)):
            continue
        if (after := get_after(change)) and not diff_filter(Path(after.path)):
            continue

        diff_hunk_texts.append(
            compute_file_diff(
                before_file=before,
                after_file=after,
                use_smart_header=use_smart_header,
                num_context_lines=diff_context_lines,
                deduplicate_identical_paths=deduplicate_identical_paths,
            )
        )

    return "\n".join(diff_hunk_texts)


def format_file_changes_with_ranges(
    changes: Sequence[Changed[File]],
    diff_context_lines: int,
    diff_filter: Callable[[Path], bool] = _default_diff_filter,
    use_smart_header: bool = True,
) -> list[Sequence[DiffHunk]]:
    """Format multiple file changes as sequence of diff hunks.

    Args:
        changes: The list of file changes.
        diff_context_lines: The number of context lines to show in the diff.
        diff_filter: Filter out the diffs coming from certain file paths.
        use_smart_header: Whether show smart header in the diff.

    Returns:
        A list of DiffHunk sequences, where each sequence corresponds to a file change.
    """

    diff_hunks = list[Sequence[DiffHunk]]()
    for change in changes:
        if (before := get_before(change)) and not diff_filter(Path(before.path)):
            continue
        if (after := get_after(change)) and not diff_filter(Path(after.path)):
            continue

        file_hunks = compute_file_diff_hunks(
            before_file=before,
            after_file=after,
            use_smart_header=use_smart_header,
            num_context_lines=diff_context_lines,
        )

        diff_hunks.append(file_hunks)

    return diff_hunks


@dataclass
class TokenizedDiffHunk:
    hunk: DiffHunk
    header_tokens: TokenList
    """The tokens for the file header."""
    body_tokens: TokenList
    """The tokens for the diff body."""
    change_id: int
    """The change id of the hunk. Hunks with the same id are simultaneous."""

    def num_tokens(self) -> int:
        return len(self.header_tokens) + len(self.body_tokens)


def tokenize_diff_hunks(
    diff_hunks: Sequence[Sequence[DiffHunk]],
    diff_token_budget: int,
    tokenizer: Tokenizer,
    deduplicate_identical_paths: bool = True,
) -> list[TokenizedDiffHunk]:
    """Tokenize diff hunks to fit the a given token budget.

    All hunks will be formatted with a file path header.
    """
    assert isinstance(tokenizer.special_tokens, RagSpecialTokens)
    hunks_with_id = [
        (hunk, t)
        for t, hunks in enumerate(diff_hunks)
        for hunk in hunks
        # Filter out empty hunks
        if hunk.text.strip()
    ]

    result = list[TokenizedDiffHunk]()
    tkns_used = 0
    for hunk, change_id in reversed(hunks_with_id):
        body_text = hunk.text
        header_text = hunk.path_header(deduplicate_identical_paths)
        body_tks = tokenizer.tokenize_safe(body_text)
        header_tks = tokenizer.tokenize_safe(header_text)
        token_usage = len(header_tks) + len(body_tks)

        if tkns_used + token_usage > diff_token_budget:
            # skip this hunk and break
            break

        result.append(TokenizedDiffHunk(hunk, header_tks, body_tks, change_id))
        tkns_used += token_usage
    assert (
        tkns_used <= diff_token_budget
    ), f"Token budget exceeded: {tkns_used} > {diff_token_budget}"
    return list(reversed(result))


def truncate_diff_hunk_tokens(
    t_hunks: Sequence[TokenizedDiffHunk],
    diff_token_budget: int,
) -> list[TokenizedDiffHunk]:
    """Truncate diff hunks and their tokens to fit within the token budget."""
    result = list[TokenizedDiffHunk]()
    tkns_used = 0
    for hunk in reversed(t_hunks):
        if tkns_used + hunk.num_tokens() > diff_token_budget:
            break
        result.append(hunk)
        tkns_used += hunk.num_tokens()
    assert (
        tkns_used <= diff_token_budget
    ), f"Token budget exceeded: {tkns_used} > {diff_token_budget}"
    return list(reversed(result))


def format_tokenized_hunks(
    tokenized_hunks: Sequence[TokenizedDiffHunk],
) -> TokenList:
    """Convert tokenized hunks into tokens while removing duplicated path headers."""
    result = TokenList()
    prev_path: tuple[str, str, int] | None = None
    for t_hunk in tokenized_hunks:
        hunk = t_hunk.hunk
        if (hunk.before_path, hunk.after_path, t_hunk.change_id) != prev_path:
            result += t_hunk.header_tokens
        result += t_hunk.body_tokens
        prev_path = (hunk.before_path, hunk.after_path, t_hunk.change_id)
    return result
