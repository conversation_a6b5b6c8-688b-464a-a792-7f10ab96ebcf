from base.diff_utils import edit_events_pb2
from base.diff_utils.edit_events import GranularEditEvent, SingleEdit
from base.static_analysis.proto_convertor import (
    DataClassJsonConvertorSpec,
    ProtoConvertorGroup,
)

group = ProtoConvertorGroup(edit_events_pb2)

group.add_convertor_by_spec(DataClassJsonConvertorSpec(SingleEdit))
group.add_convertor_by_spec(DataClassJsonConvertorSpec(GranularEditEvent))

to_proto = group.to_proto
from_proto = group.from_proto
