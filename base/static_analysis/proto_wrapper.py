"""Wrapper around proto objects and corresponding dataclasses."""

from __future__ import annotations

from collections import defaultdict
from functools import cache
from pathlib import PosixPath

from base.ranges import IntRange

# pylint: disable-next=no-name-in-module
from base.static_analysis import signature_pb2  # type: ignore
from base.static_analysis.proto_convertor import (
    ProtoConvertor,
    ProtoConvertorGroup,
    RecursiveConvertorSpec,
    serialize_dict_as_lists,
)
from base.static_analysis.signature_index import (
    BasicUsageReranker,
    FileSummaryWithSignatures,
    SignatureQueryState,
)
from base.static_analysis.signature_utils import FileSignatureInfo, SymbolSignature
from base.static_analysis.usage_analysis import (
    FileCharRange,
    FileSummary,
    LocalUsageAnalysis,
    ScopeTreeStructure,
    SymbolDefinition,
    SymbolDefinitionId,
    SymbolNameUsage,
    VarOccurrence,
)


@cache
def _str_to_path(s: str) -> PosixPath:
    return PosixPath(s)


class PathConvertor(ProtoConvertor[PosixPath]):
    """Protobuf convertor for PosixPath."""

    py_class = PosixPath
    proto_class = signature_pb2.Path

    def to_proto(self, obj: PosixPath):
        return signature_pb2.Path(value=str(obj))

    def from_proto(self, pb) -> PosixPath:
        return _str_to_path(pb.value)


class IntRangeConvertor(ProtoConvertor[IntRange]):
    """Protobuf convertor for IntRange."""

    py_class = IntRange
    proto_class = signature_pb2.IntRange

    def to_proto(self, obj: IntRange):
        return signature_pb2.IntRange(start=obj.start, stop=obj.stop)

    def from_proto(self, pb: signature_pb2.IntRange) -> IntRange:
        return IntRange(pb.start, pb.stop)


group = ProtoConvertorGroup(signature_pb2)

group.add_convertor(PathConvertor())
group.add_convertor(IntRangeConvertor())
group.add_default_convertor(FileCharRange)
group.add_convertor_by_spec(
    RecursiveConvertorSpec[SymbolNameUsage](
        py_class=SymbolNameUsage,
        proto_extra_fields={"def_range", "has_def_range"},
        get_proto_extra_fields=lambda obj: {
            "def_range": obj.def_range,
            "has_def_range": obj.def_range is not None,
        },
        py_dropped_fields={"def_range"},
        get_python_dropped_fields=lambda d: {
            "def_range": d["def_range"] if d["has_def_range"] else None,
        },
    )
)
group.add_default_convertor(SymbolDefinitionId)
group.add_convertor_by_spec(
    RecursiveConvertorSpec[SymbolDefinition](
        py_class=SymbolDefinition,
        proto_extra_fields={"parent_id", "has_parent"},
        get_proto_extra_fields=lambda obj: {
            "parent_id": obj.parent_id,
            "has_parent": obj.parent_id is not None,
        },
        py_dropped_fields={"parent_id"},
        get_python_dropped_fields=lambda d: {
            "parent_id": d["parent_id"] if d["has_parent"] else None,
        },
    )
)
group.add_convertor_by_spec(
    RecursiveConvertorSpec[VarOccurrence](
        py_class=VarOccurrence,
        proto_extra_fields={"ranges"},
        get_proto_extra_fields=lambda obj: {"ranges": list(obj.ranges)},
        py_dropped_fields={"ranges"},
        get_python_dropped_fields=lambda d: {"ranges": tuple(d["ranges"])},
    )
)
group.add_convertor_by_spec(
    RecursiveConvertorSpec[LocalUsageAnalysis](
        py_class=LocalUsageAnalysis,
        proto_extra_fields={"name_usages", "var_occurrences"},
        get_proto_extra_fields=lambda obj: {
            "name_usages": list(obj.name_usages),
            "var_occurrences": list(obj.var_occurrences),
        },
        py_dropped_fields={"name_usages", "var_occurrences"},
        get_python_dropped_fields=lambda d: {
            "name_usages": set(d["name_usages"]),
            "var_occurrences": set(d["var_occurrences"]),
        },
    )
)
group.add_default_convertor(ScopeTreeStructure)
group.add_default_convertor(FileSummary)
group.add_default_convertor(SymbolSignature)
group.add_convertor_by_spec(
    RecursiveConvertorSpec[FileSignatureInfo](
        py_class=FileSignatureInfo,
        proto_extra_fields={
            "symbol_signatures_keys",
            "symbol_signatures_values",
        },
        get_proto_extra_fields=lambda obj: serialize_dict_as_lists(
            obj.symbol_signatures, "symbol_signatures"
        ),
        py_dropped_fields={"symbol_signatures"},
        get_python_dropped_fields=lambda d: {
            "symbol_signatures": dict(
                zip(d["symbol_signatures_keys"], d["symbol_signatures_values"])
            )
        },
    )
)
group.add_default_convertor(FileSummaryWithSignatures)
group.add_default_convertor(SignatureQueryState)
group.add_convertor_by_spec(
    RecursiveConvertorSpec[BasicUsageReranker](
        py_class=BasicUsageReranker,
        proto_extra_fields={
            "file_bonuses_keys",
            "file_bonuses_values",
            "def_bonuses_keys",
            "def_bonuses_values",
            "modules_recorded",
        },
        get_proto_extra_fields=lambda obj: {
            **serialize_dict_as_lists(obj.file_bonuses, "file_bonuses"),
            **serialize_dict_as_lists(obj.def_bonuses, "def_bonuses"),
            "modules_recorded": sorted(obj.modules_recorded),
        },
        py_dropped_fields={"file_bonuses", "def_bonuses", "modules_recorded"},
        get_python_dropped_fields=lambda d: {
            "file_bonuses": defaultdict(
                float, zip(d["file_bonuses_keys"], d["file_bonuses_values"])
            ),
            "def_bonuses": defaultdict(
                float, zip(d["def_bonuses_keys"], d["def_bonuses_values"])
            ),
            "modules_recorded": set(d["modules_recorded"]),
        },
    )
)

to_proto = group.to_proto
from_proto = group.from_proto
