"""Utilities to convert between Python and protobuf classes."""

from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Callable, Generic, Mapping, TypeVar, get_type_hints

# These are the message types used for UPB based protobuf messages.
# pylint: disable=no-name-in-module
from google._upb._message import RepeatedCompositeContainer, RepeatedScalarContainer

# These are the message types used for default messages (and empty ones...).
from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer,
)

PyT = TypeVar("PyT")


class ProtoConvertor(Generic[PyT], ABC):
    """A convertor between a Python and protobuf class.

    Please note that `PyT` is invariant, which means that the convertor for
    a type `A` cannot be used for its subtypes `B`. (e.g., `B` may
    contain additional fields that are not present in `A`, and it's not safe
    to only store the fields that are present in `A` into the protobuf value.)
    """

    py_class: type[PyT]
    """The Python class."""

    proto_class: type
    """The protobuf class."""

    @abstractmethod
    def to_proto(self, obj: PyT) -> Any:
        """Convert the Python class to the proto class."""

    @abstractmethod
    def from_proto(self, pb: Any) -> PyT:
        """Convert the proto class to the Python class."""


class ProtoConvertorError(Exception):
    """This is thrown when a convertor fails."""


class ProtoConvertorGroup:
    """A group of (mutually recursive) proto convertors.

    This class implements the `to_proto` and `from_proto` method that recursively
    calls the registered convertors to convert between Python and protobuf values.
    To use this class correctly, it's important to maintain a one-to-one mapping
    between each *runtime* Python type and the corresponding protobuf type. For
    example, by default this class converts between Python `list` and protobuf
    `repeated`, so if your Python data class contains a `set`, you'll need
    to use a customized convertor that avoids mapping the `set` value to a `repeated`.

    ## Example
        ```
        group = ProtoConvertorGroup(proto_module1)
        group.add_default_convertor(IntRange)
        group.add_convertor_by_spec(
            RecursiveConvertorSpec[SomeUserDefinedClass](...)
        )

        py_val = SomeUserDefinedClass(...)
        proto_v = group.to_proto(py_val)
        assert group.from_proto(proto_v) == py_val

        # new define a new group for a different protobuf module
        # and make it inherit convertors from the previous group
        new_group = ProtoConvertorGroup(proto_module2, group)
        ...
        ```
    For more realistic examples, see base/static_analysis/proto_wrapper.py.
    """

    def __init__(
        self, proto_module: Any, extend_group: ProtoConvertorGroup | None = None
    ):
        """Initialize the group for a given protobuf module.

        Args:
            proto_module: Protobuf module for which new convertors will be registered.
            extend_group: The group to extend. If provided, this group will inherit
                all convertors from the extended group.
        """
        self.proto_module = proto_module
        if extend_group:
            self._py_to_proto = extend_group._py_to_proto
            self._proto_to_py = extend_group._proto_to_py
        else:
            self._py_to_proto = {}
            self._proto_to_py = {}

    def add_convertor(self, convertor: ProtoConvertor) -> None:
        """Register a proto convertor into the group."""
        if convertor.py_class in self._py_to_proto:
            raise ProtoConvertorError(
                f"Duplicate proto convertor for {convertor.py_class}."
            )
        if convertor.proto_class in self._proto_to_py:
            raise ProtoConvertorError(
                f"Duplicate proto convertor for {convertor.proto_class}."
            )
        self._py_to_proto[convertor.py_class] = convertor.to_proto
        self._proto_to_py[convertor.proto_class] = convertor.from_proto

    def add_default_convertor(
        self, py_class: type, proto_class: type | None = None
    ) -> None:
        """Register a default convertor for a given Python and proto class.

        This assumes that the Python and proto classes have the same fields.
        """
        self.add_convertor(_DefaultRecursiveConvertor(self, py_class, proto_class))

    def add_convertor_by_spec(self, spec: RecursiveConvertorSpec) -> None:
        """Register a recursive convertor for a given Python and proto class.

        The spec is used to override the default conversion between fields.
        """
        self.add_convertor(_RecursiveConvertor(self, spec))

    def to_proto(self, x: Any) -> Any:
        """Convert the Python value to the proto value using registered convertors."""
        if isinstance(x, _BasicTypes):  # basic types
            return x
        elif conv_f := self._py_to_proto.get(type(x)):
            return conv_f(x)
        elif isinstance(x, list):
            return [self.to_proto(y) for y in x]
        else:
            raise ProtoConvertorError(f"Cannot convert {type(x)} to proto.")

    def from_proto(self, pb: Any, py_type: type[PyT] | Any = Any) -> PyT:
        """Convert the proto value to a Python value using registered convertors.

        If `py_type` is provided, will assert that the converted value is of
        the given type.
        """
        if isinstance(pb, _BasicTypes):
            result = pb
        elif (conv_f := self._proto_to_py.get(type(pb))) is not None:
            result = conv_f(pb)
        elif isinstance(pb, _ListlikeProtoTypes):
            # only convert list by default to maintain a one-to-one mapping
            result = [self.from_proto(y) for y in pb]
        else:
            raise ProtoConvertorError(f"Cannot convert {type(pb)} to Python.")
        return result  # type: ignore[return-value]


NoneType = type(None)
_BasicTypes = (int, str, bool, float, NoneType)
_ListlikeProtoTypes = (
    RepeatedScalarFieldContainer,
    RepeatedCompositeFieldContainer,
    RepeatedScalarContainer,
    RepeatedCompositeContainer,
    list,
)


def serialize_dict_as_lists(
    d: Mapping, entry_name: str, sort_keys: bool = True
) -> dict[str, list]:
    """Serialize a dict as a list of keys and a list of values."""
    if sort_keys:
        keys = sorted(d.keys())
    else:
        keys = list(d.keys())
    return {
        f"{entry_name}_keys": keys,
        f"{entry_name}_values": [d[k] for k in keys],
    }


@dataclass
class RecursiveConvertorSpec(Generic[PyT]):
    """Defines a recursive convertor for a given Python and proto class."""

    py_class: type[PyT]
    """The Python class of the convertor."""

    proto_extra_fields: set[str]
    """The extra proto fields to be created during `to_proto`.

    This should match the keys returned by `get_proto_extra_fields`.
    """

    get_proto_extra_fields: Callable[[PyT], dict[str, Any]]
    """Should return additional key-to-Python-value pairs to be stored into protobuf.

    Note that these extra fields will be recursively converted into the
    proto values.

    For example, if your class has a `dict` value in attribute `x`, you can
    convert `x` into a key list and a value list by returning
    `{"x_keys": list(x.keys()), "x_values": list(x.values())}`.
    """

    py_dropped_fields: set[str]
    """The Python fields to be dropped during `to_proto`.

    This should match the keys returned by `get_python_dropped_fields`.
    """

    get_python_dropped_fields: Callable[[dict[str, Any]], dict[str, Any]]
    """Should reconstruct the dropped Python fields.

    Note that the input dict contains Python values that are already recursively
    converted from proto values.

    Following the example in `get_proto_extra_fields`'s docstring above, you should
    re-assemble the `dict` from the two lists by returning
    `{"x": dict(zip(d["x_keys"], d["x_values"]))}`.
    """

    proto_class: type | None = None
    """The protobuf class. If None, is inferred from the Python class's name.

    e.g., if the Python class is `Foo`, the proto class is `protomodule.Foo`.
    """


class DataClassJsonConvertorSpec(RecursiveConvertorSpec):
    def __init__(self, py_class: type[PyT], proto_class: type | None = None):
        super().__init__(
            py_class=py_class,
            proto_extra_fields=set(),
            get_proto_extra_fields=lambda _: {},
            py_dropped_fields={"dataclass_json_config"},
            get_python_dropped_fields=lambda _: {},
            proto_class=proto_class,
        )


class _RecursiveConvertor(ProtoConvertor[PyT]):
    """A recursive convertor for a given Python and proto class."""

    def __init__(
        self,
        group: ProtoConvertorGroup,
        spec: RecursiveConvertorSpec[PyT],
    ):
        self.group = group
        self.py_class = py_class = spec.py_class

        proto_class = spec.proto_class
        if proto_class is None:
            proto_class = getattr(group.proto_module, self.py_class.__name__)
        self.proto_class = proto_class

        self._py_attrs = set(get_type_hints(py_class).keys())
        self._py_default_attrs = self._py_attrs - spec.py_dropped_fields
        self._proto_attrs = (
            self._py_attrs - spec.py_dropped_fields
        ) | spec.proto_extra_fields
        self._proto_override_attrs = spec.proto_extra_fields
        self._to_proto_overrides = spec.get_proto_extra_fields
        self._from_proto_overrides = spec.get_python_dropped_fields

    def to_proto(self, obj: PyT) -> Any:
        """Convert the Python class to a proto class."""
        assert isinstance(obj, self.py_class), f"{type(obj)=}, {self.py_class=}"
        to_proto = self.group.to_proto
        default_converted = {
            k: to_proto(getattr(obj, k)) for k in self._py_default_attrs
        }
        override_converted = {
            k: to_proto(v) for k, v in self._to_proto_overrides(obj).items()
        }
        return self.proto_class(**default_converted, **override_converted)

    def from_proto(self, pb: Any) -> PyT:
        """Convert the proto class to a Python class."""
        assert isinstance(pb, self.proto_class), f"{type(pb)=}, {self.proto_class=}"
        from_proto = self.group.from_proto
        all_converted = {k: from_proto(getattr(pb, k)) for k in self._proto_attrs}
        overrides = self._from_proto_overrides(all_converted)
        default_converted = {k: all_converted[k] for k in self._py_default_attrs}
        return self.py_class(**default_converted, **overrides)


class _DefaultRecursiveConvertor(ProtoConvertor[PyT]):
    """A recursive convertor assuming matching fields between Python and protobuf."""

    def __init__(
        self,
        group: ProtoConvertorGroup,
        py_class: type[PyT],
        proto_class: type | None = None,
    ):
        self.group = group
        self.py_class = py_class
        if proto_class is None:
            proto_class = getattr(group.proto_module, self.py_class.__name__)
            assert proto_class is not None
        self.proto_class = proto_class

        self._py_attrs = tuple(set(get_type_hints(py_class).keys()))

    def to_proto(self, obj: PyT) -> Any:
        """Convert the Python class to a proto class."""
        assert isinstance(obj, self.py_class), f"{type(obj)=}, {self.py_class=}"
        to_proto = self.group.to_proto
        converted = {k: to_proto(getattr(obj, k)) for k in self._py_attrs}
        return self.proto_class(**converted)

    def from_proto(self, pb: Any) -> PyT:
        """Convert the proto class to a Python class."""
        assert isinstance(pb, self.proto_class), f"{type(pb)=}, {self.proto_class=}"
        from_proto = self.group.from_proto
        converted = {k: from_proto(getattr(pb, k)) for k in self._py_attrs}
        return self.py_class(**converted)
