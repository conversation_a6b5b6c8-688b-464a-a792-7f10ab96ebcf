"""Tests for import_finder."""

from base.static_analysis.import_finder import find_all_import_lines


python_positive_examples = """\
import foo
import numpy as np
from foo import bar
import foo.bar.baz
import foo # ends with comment
import     foo

import (
    package1,
    package2,
    package3
)

def foo():
    import bar  # nested import
    return x
"""

python_negative_examples = """\
import_fun(1)
import-1
using a
"has an import"
importing a
#include <foo>
# import foo
"""


def test_for_python():
    pos_example_lines = python_positive_examples.splitlines()
    not_import_lines = {"", ")", "def foo():", "return x"}
    expected_positive_lines = [
        (i, line)
        for i, line in enumerate(python_positive_examples.splitlines())
        if line.strip() not in not_import_lines
    ]
    found_lines = [
        (i, pos_example_lines[i])
        for i in find_all_import_lines(python_positive_examples, "Python")
    ]
    assert found_lines == expected_positive_lines

    found_lines = find_all_import_lines(python_negative_examples, "Python")
    assert found_lines == []


cpp_positive_examples = """\
#include <foo>
#include "foo.h"
#include<foo/bar>
using namespace std;
using std::cout;
"""

cpp_negative_examples = """\
"has an include"
#define FOO 1
"""


def test_for_cpp():
    pos_example_lines = cpp_positive_examples.splitlines()
    expected_pos_example_lines = list(enumerate(pos_example_lines))
    found_lines = [
        (i, pos_example_lines[i])
        for i in find_all_import_lines(cpp_positive_examples, "C++")
    ]
    assert found_lines == expected_pos_example_lines

    neg_example_lines = cpp_negative_examples.splitlines()
    found_lines = [
        (i, neg_example_lines[i])
        for i in find_all_import_lines(cpp_negative_examples, "C++")
    ]
    assert found_lines == []


yaml_negative_examples = """\
edit_timeout_ms:
    sync: true
    description: The timeout (in ms) for edit requests made by api-proxy.
    default_return_value: 120001
    envs:
        production:
            rules:
            -   return_value: 120000
        test:
            rules:
            -   return_value: 800
"""


def test_for_yaml():
    negative_lines = yaml_negative_examples.splitlines()
    found_lines = [
        (i, negative_lines[i])
        for i in find_all_import_lines(yaml_negative_examples, "YAML")
    ]
    assert found_lines == []


racket_positive_examples = """\
(require racket/base)
(require (for-syntax racket/base))
(require (prefix "my-module.rkt" my-))
"""

racket_negative_examples = """\
(require-item (foo) 1)
(define (bar) 2)
"""


def test_for_racket():
    pos_example_lines = racket_positive_examples.splitlines()
    expected_pos_example_lines = list(
        enumerate(pos_example_lines)
    )  # all lines are import lines
    found_lines = [
        (i, pos_example_lines[i])
        for i in find_all_import_lines(racket_positive_examples, "Racket")
    ]
    assert found_lines == expected_pos_example_lines

    neg_example_lines = racket_negative_examples.splitlines()
    found_lines = [
        (i, neg_example_lines[i])
        for i in find_all_import_lines(racket_negative_examples, "Racket")
    ]
    assert found_lines == []
