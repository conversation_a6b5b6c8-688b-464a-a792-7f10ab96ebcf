load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_library", "py_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "common",
    srcs = ["common.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/languages",
        "//base/ranges",
        requirement("tree-sitter"),
        requirement("tree-sitter-language-pack"),
    ],
)

pytest_test(
    name = "common_test",
    size = "small",
    srcs = ["common_test.py"],
    deps = [
        ":common",
        "//base/ranges",
        "//base/test_utils:testing_utils",
        requirement("tree-sitter"),
        requirement("tree-sitter-language-pack"),
    ],
)

py_library(
    name = "parsing",
    srcs = ["parsing.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        "//base/ranges",
        requirement("tree-sitter"),
        requirement("tree-sitter-language-pack"),
    ],
)

pytest_test(
    name = "parsing_test",
    size = "small",
    srcs = ["parsing_test.py"],
    data = [":testdata"],
    deps = [
        ":common_test",
        ":parsing",
    ],
)

py_library(
    name = "usage_analysis",
    srcs = [
        "_usage_supports.py",
        "_variable_supports.py",
        "usage_analysis.py",
    ],
    data = ["//typings:stubs"],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        ":parsing",
        "//base/ranges",
        requirement("tree-sitter"),
        requirement("tree-sitter-language-pack"),
    ],
)

pytest_test(
    name = "usage_analysis_test",
    size = "small",
    srcs = ["usage_analysis_test.py"],
    data = [":testdata"],
    deps = [":usage_analysis"],
)

py_library(
    name = "signature_utils",
    srcs = ["signature_utils.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        ":parsing",
        ":usage_analysis",
        "//base/ranges",
    ],
)

pytest_test(
    name = "signature_utils_test",
    size = "small",
    srcs = ["signature_utils_test.py"],
    deps = [
        ":common",
        ":signature_utils",
        ":usage_analysis",
    ],
)

py_library(
    name = "signature_index",
    srcs = ["signature_index.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        ":signature_utils",
        ":usage_analysis",
        "//base/ranges",
        requirement("intervaltree"),
    ],
)

pytest_test(
    name = "signature_index_test",
    size = "small",
    srcs = ["signature_index_test.py"],
    data = [":testdata"],
    deps = [
        ":common",
        ":common_test",
        ":signature_index",
        ":signature_utils",
        ":usage_analysis",
        "//base/ranges",
    ],
)

py_library(
    name = "signature_analysis",
    srcs = ["signature_analysis.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        ":parsing",
        ":signature_index",
        ":signature_utils",
        ":usage_analysis",
    ],
)

filegroup(
    name = "testdata",
    srcs = glob(["testdata/**"]),
    visibility = ["//visibility:public"],
)

pytest_test(
    name = "signature_analysis_test",
    size = "small",
    srcs = ["signature_analysis_test.py"],
    data = [":testdata"],
    deps = [
        ":signature_analysis",
    ],
)

proto_library(
    name = "signature_proto",
    srcs = ["signature.proto"],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "signature_py_proto",
    protos = [":signature_proto"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    data = [":signature_py_proto"],
    visibility = ["//base:__subpackages__"],
)

py_library(
    name = "proto_convertor",
    srcs = [
        "proto_convertor.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "proto_convertor_test",
    srcs = ["proto_convertor_test.py"],
    deps = [
        ":proto_convertor",
        ":signature_py_proto",
        ":usage_analysis",
        "//base/ranges",
        requirement("protobuf"),
    ],
)

py_library(
    name = "proto_wrapper",
    srcs = [
        "proto_wrapper.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":proto_convertor",
        ":signature_index",
        ":signature_py_proto",
        ":signature_utils",
        ":usage_analysis",
        "//base/ranges",
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "proto_wrapper_test",
    srcs = ["proto_wrapper_test.py"],
    deps = [
        ":proto_wrapper",
        ":signature_analysis",
        ":signature_index",
        ":signature_utils",
        ":usage_analysis",
        "//base/ranges",
        requirement("protobuf"),
    ],
)

py_library(
    name = "indentation_utils",
    srcs = ["indentation_utils.py"],
    visibility = ["//visibility:public"],
)

py_library(
    name = "smart_header",
    srcs = ["smart_header.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":indentation_utils",
        "//base/languages",
    ],
)

pytest_test(
    name = "smart_header_test",
    srcs = ["smart_header_test.py"],
    deps = [
        ":common",
        ":smart_header",
    ],
)

py_library(
    name = "import_finder",
    srcs = ["import_finder.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":indentation_utils",
        "//base/languages",
    ],
)

pytest_test(
    name = "import_finder_test",
    srcs = ["import_finder_test.py"],
    deps = [
        ":import_finder",
    ],
)
