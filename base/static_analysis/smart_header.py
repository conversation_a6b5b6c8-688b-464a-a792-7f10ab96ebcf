"""Lightweight heuristics to assign headers to each line in a file.

It works by looking for lines that start with certain language-specific keywords,
and any subsequent lines that are more indented are assigned to the same header.
"""

from functools import cache
import re

from typing import Callable, Mapping, NamedTuple, Sequence

from base.static_analysis.indentation_utils import compute_line_indentations
from base.languages.languages import LanguageId


class LineHeader(NamedTuple):
    line_number: int
    indent_level: int


# Note: we require lines to be `list` and not `Sequence` since `str` is considered
# as `Sequence[str]`.
def assign_line_headers(
    lines: list[str],
    lang: LanguageId | None,
    max_depth: int = 3,
) -> list[Sequence[LineHeader]]:
    """Maps each line to a sequence of headers for that line.

    Args:
        lines: the lines to be analyzed.
        lang: the language of the code. If not specified or the language is not\
            supported, a basic indentation-based heuristic will be used.
        max_depth: the maximum depth of the header stack. Inner headers will be ignored.
    """
    support = (
        HEADER_SUPPORTS.get(lang, generic_header_support)
        if lang
        else generic_header_support
    )

    @cache
    def is_not_header_junk(line: str) -> bool:
        return not support.is_header_junk(line)

    indent_levels = compute_line_indentations(lines)
    stripped_lines = [line.strip() for line in lines]

    class LineInfo(NamedTuple):
        headers: tuple[LineHeader, ...]
        """The headers that this line may have."""
        indent_level: int
        """The indentation level of this line."""
        is_not_header_junk: bool
        """Whether this line is not a header junk line."""

    # First pass:
    # We scan through the file from top to bottom, looking for header lines.
    # Any indented lines immediately following a header line are assigned to that
    # header stack. This pass may be over-assigning some heads to more lines than
    # necessary, which will get fixed in the second pass.
    header_stack: tuple[LineHeader, ...] = ()
    line_info_list = list[LineInfo]()
    for i, line_stripped in enumerate(stripped_lines):
        if header_stack:
            if is_not_header_junk(line_stripped):
                # pop any header defined on a higher or equal indentation
                indent_threshold = indent_levels[i]
            else:
                # if this is a header junk, only pop higher indentation headers
                indent_threshold = indent_levels[i] + 1

            while header_stack and header_stack[-1].indent_level >= indent_threshold:
                header_stack = header_stack[:-1]

        info = LineInfo(
            headers=header_stack,
            indent_level=indent_levels[i],
            is_not_header_junk=is_not_header_junk(line_stripped),
        )
        line_info_list.append(info)

        if (
            len(header_stack) < max_depth
            and support.is_header_begin(line_stripped)
            and is_not_header_junk(line_stripped)
        ):
            header_stack += (LineHeader(i, indent_levels[i]),)

    # Second pass:
    # Fix any over-assigned headers.
    # This requires removing any header this is not followed by a non-junk occurrence.
    final_assignments = list[Sequence[LineHeader]]()
    non_junk_occurrences = set[int]()
    recorded_ids = set[int]()

    def record_non_junk_occurrence(headers: tuple[LineHeader, ...]):
        # we use object id to optimize the recording performance
        if id(headers) in recorded_ids:
            return
        for header in headers:
            non_junk_occurrences.add(header.line_number)
        recorded_ids.add(id(headers))

    for i in reversed(range(len(line_info_list))):
        info = line_info_list[i]
        new_headers = info.headers
        while (
            new_headers
            and new_headers[-1].indent_level >= info.indent_level
            and new_headers[-1].line_number not in non_junk_occurrences
        ):
            # this is an over-assigned header, pop it.
            new_headers = new_headers[:-1]
        final_assignments.append(new_headers)
        if info.is_not_header_junk:
            record_non_junk_occurrence(new_headers)

    final_assignments.reverse()

    return final_assignments


def show_line_headers(code: str, lang: LanguageId | None) -> str:
    """Visualize the smart header assignment as a string."""
    lines = code.splitlines(keepends=True)
    assignment = assign_line_headers(lines, lang)
    outputs = list[str]()
    outputs.append("    innermost header | code\n")
    outputs.append("---------------------+------" + "-" * 60 + "\n")
    for line, headers in zip(lines, assignment):
        header_line = lines[headers[-1].line_number] if headers else ""
        header_line = header_line.strip()[:20]
        if len(header_line) < 20:
            header_line += " " * (20 - len(header_line))
        if line.isspace():
            outputs.append(f"{header_line} |\n")
        else:
            outputs.append(f"{header_line} | {line}")
    return "".join(outputs)


class HeaderSupport(NamedTuple):
    is_header_begin: Callable[[str], bool]
    """Returns whether the given line is the beginning of a header.

    It's ok for this function to have high recall and low precision since this is used
    in combination with the indentation heuristic.
    """

    is_header_junk: Callable[[str], bool]
    """Returns whether the given line is a header junk line.

    A header junk line will not cause the header stack to be popped even if it has a
    lower indentation level. e.g., the last line in the code below is a header junk line:
    ```
    def foo(
        arg1, arg2,
    ):
    ```.

    It's ok for this function to have high recall and low precision since this is used
    in combination with the indentation heuristic.
    """


_if_pattern = r"if(\(|\s)"
_else_pattern = r"(}|\s|^)else({|\s)"
_while_pattern = r"while(\(|\s)"
_for_pattern = r"for(\(|\s)"
_try_pattern = r"try(\(|\s)"
_catch_pattern = r"(}|^|\s)catch(\(|\s)"
_return_pattern = r"return\s"
_common_stmt_pattern = (
    f"({_if_pattern})|({_else_pattern})|({_while_pattern})|({_for_pattern})|"
    f"({_try_pattern})|({_catch_pattern})|({_return_pattern})"
)


def has_keyword(text: str, keyword: str) -> bool:
    return text.startswith(keyword + " ") or (f" {keyword} " in text)


def generic_is_header(line: str) -> bool:
    # note that any line matching `generic_is_header_junk` will automatically not be
    # considered as a header
    return not re.match(_common_stmt_pattern, line)


def generic_is_header_junk(line: str) -> bool:
    if line in ("begin", "end"):
        return True
    if line.startswith((")", "}", "]")) and line.endswith(("(", "{", "[")):
        return True
    # if it's a short line consists of only special characters
    return len(line) < 6 and bool(re.match(r"^[^\w]*$", line))


generic_header_support = HeaderSupport(
    is_header_begin=generic_is_header, is_header_junk=generic_is_header_junk
)


def is_python_header(line: str):
    return bool(re.match(r"^(async\s)?(def|class)\s", line))


def is_python_header_junk(line: str):
    text = line.split("#")[0].strip()
    return not text or (text.startswith(")") and text.endswith(":"))


def is_java_header(line: str):
    text = line.split("//")[0].strip()
    return has_keyword(text, "class") or bool(
        re.match(r"^(public|private|protected|static)\s", text)
    )


def is_java_header_junk(line: str):
    text = line.split("//")[0].strip()
    return generic_is_header_junk(text)


def is_cpp_header(line: str) -> bool:
    text = line.split("//")[0].strip()
    if has_keyword(text, "class"):
        return True
    # cpp function syntax is too complex, so we instead focus on negative patterns
    # to maintain high recall.
    if text.startswith("#") or text.endswith(";"):
        return False
    return generic_is_header(text)


def is_cpp_header_junk(line: str) -> bool:
    return is_java_header_junk(line)


def is_go_header(line: str):
    text = line.split("//")[0].strip()
    return bool(re.match(r"^(func|type)\s", text)) or bool(
        re.match(r"struct\s?{$", text)
    )


def is_go_header_junk(line: str) -> bool:
    return is_java_header_junk(line)


def is_ts_header(line: str):
    text = line.split("//")[0].strip()
    modifier_pattern = r"^(public|private|protected|static)\s"
    return bool(
        re.match(r"^(export\s)?(function|class|interface|type)\s", text)
    ) or bool(re.match(rf"({modifier_pattern})|(constructor\()", text))


def is_ts_header_junk(line: str) -> bool:
    return is_java_header_junk(line)


def is_rust_header(line: str):
    rust_keywords = r"^(pub|impl|mod|enum|struct|trait|fn|static|extern|async|type)\s"
    text = line.split("//")[0].strip()
    return bool(re.match(rust_keywords, text))


def is_rust_header_junk(line: str) -> bool:
    return is_java_header_junk(line)


def is_ruby_header(line: str):
    return bool(re.match(r"^(def|class|module)\s", line))


def is_ruby_header_junk(line: str):
    text = line.split("#")[0].strip()
    return generic_is_header_junk(text)


def is_swift_header(line: str):
    if has_keyword(line, "class"):
        return True
    swift_scopes = (
        r"class|struct|enum|protocol|extension|func|subscript|associatedtype|operator"
    )
    swift_access_modifiers = r"public|private|internal|fileprivate|open"
    return bool(
        re.match(rf"^({swift_access_modifiers}|{swift_scopes})\s", line)
    ) or line.startswith(("init(", "deinit {"))


def is_swift_header_junk(line: str):
    return is_java_header_junk(line)


# Below is the list of langauges with more precise smart header support.
HEADER_SUPPORTS: Mapping[LanguageId, HeaderSupport] = {
    "Python": HeaderSupport(is_python_header, is_python_header_junk),
    "Java": HeaderSupport(is_java_header, is_java_header_junk),
    "C++": HeaderSupport(is_cpp_header, is_cpp_header_junk),
    "C": HeaderSupport(is_cpp_header, is_cpp_header_junk),
    "JavaScript": HeaderSupport(is_ts_header, is_ts_header_junk),
    "TypeScript": HeaderSupport(is_ts_header, is_ts_header_junk),
    "Go": HeaderSupport(is_go_header, is_go_header_junk),
    "Rust": HeaderSupport(is_rust_header, is_rust_header_junk),
    "Ruby": HeaderSupport(is_ruby_header, is_ruby_header_junk),
    "Swift": HeaderSupport(is_swift_header, is_swift_header_junk),
}
