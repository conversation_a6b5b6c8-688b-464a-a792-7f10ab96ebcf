"""Utilities to extract symbol signatures."""

from __future__ import annotations

import logging
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path
from typing import Literal, Mapping, NamedTuple, Optional, Sequence, get_args

from typing_extensions import TypeGuard, assert_never

from base.ranges import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.static_analysis.common import (
    LanguageID,
    assert_eq,
    groupby,
    shorten_str,
    shorten_str_detailed,
)
from base.static_analysis.parsing import (
    FileTypeNotSupportedError,
    SrcScope,
    SrcSpan,
    get_scope_by_range,
)
from base.static_analysis.usage_analysis import (
    FileSummary,
    ParsedFile,
    SymbolDefinition,
    SymbolDefinitionId,
)


class SymbolSignature(NamedTuple):
    """The signature of a codebase symbol, used for signature-based retrieval."""

    text: str
    """The text of the signature."""

    path: Path
    """Points to the file in which the signature is defined."""

    crange: CharRange
    """The char range of the signature in its source file."""

    lrange: LineRange
    """The line range of the signature in its source file."""


@dataclass
class FileSignatureInfo:
    """Holds the signature-related info of a file.

    This can be constructed by calling `SignaturePrinter.get_signature_info`.
    """

    symbol_signatures: Mapping[SymbolDefinitionId, SymbolSignature]
    """The signatures of all symbols in the file."""

    module_signature: SymbolSignature
    """The signature of the module."""


@dataclass(frozen=True)
class ScopeId:
    """Sufficient information to reference a scope."""

    name: str
    """The name of file, class, or function."""
    parent: Optional[ScopeId]
    """The scope id of the parent scope if this is not the root."""


SignatureSupportedLanguageID = Literal[
    "python", "java", "javascript", "typescript", "go", "rust"
]
"""Languages for which we can generate signatures."""


def is_signature_supported_lang(
    lang: LanguageID,
) -> TypeGuard[SignatureSupportedLanguageID]:
    """Return whether we support generating signatures for the given language."""
    return lang in get_args(SignatureSupportedLanguageID)


@dataclass
class SignaturePrinter:
    """Used to turn codebase symbols into signature strings."""

    max_docstr_chars: int = 0
    """The max number of characters to show in each docstring.
    0 means no docstring in signatures."""

    max_attributes_chars: int = 1000
    """The max number of total characters to show for all attribute in a class."""

    max_variable_chars: int = 200
    """The max number of characters of each variable definition."""

    max_method_chars: int = 200
    """The max number of characters of each method signature.

    This only has effect when `show_full_method_signatures` is True.
    """

    max_sig_file_path_chars: int = 60
    """The max number of characters to show from the file path."""

    show_full_method_signatures: bool = False
    """Whether to show the full method signatures in classes.

    When this is True, `no_class_member_signatures` will also be enabled.
    """

    hide_equals_ellipsis: bool = False
    """Whether to hide the ` = ...` in variable signatures."""

    always_show_private: bool = False
    """Whether to always show private symbols.

    If set to True, this overrides the `show_private` parameter.
    """

    @property
    def no_class_member_signatures(self) -> bool:
        """Whether to replace class member signatures with the class's signature."""
        return self.show_full_method_signatures

    def get_signature_info(
        self, summary: FileSummary, pfile: ParsedFile, show_private: bool
    ) -> FileSignatureInfo:
        """Get the FileSignatureInfo of a given file."""
        if self.always_show_private:
            show_private = True
        lang = summary.lang
        if not is_signature_supported_lang(lang):
            raise FileTypeNotSupportedError(f"No signature support for {lang}.")
        id_to_symb = {s.id: s for s in summary.definitions}
        # map from parent name to children
        # Note that this will combine children for things with the same name.
        children_map = defaultdict[ScopeId, list[SymbolDefinition]](list)
        sig_context = _SignatureContext(
            pfile, lang, id_to_symb, children_map, show_private
        )
        for s in summary.definitions:
            if s.parent_id is not None:
                children_map[sig_context.get_scope_id(s.parent_id)].append(s)

        # Remove duplicated class symbols for languages like Rust
        unique_symbs = list[SymbolDefinition]()
        class_scopes = set[ScopeId]()
        for s in summary.definitions:
            if s.kind != "class":
                unique_symbs.append(s)
                continue
            scope_id = sig_context.get_scope_id(s.id)
            if scope_id not in class_scopes:
                class_scopes.add(scope_id)
                unique_symbs.append(s)

        # map each symbol to its representative
        if self.no_class_member_signatures:
            # class members are represented by their parent
            representatives = dict[SymbolDefinitionId, SymbolDefinition]()
            for s in unique_symbs:
                if s.parent_id and (s.kind != "class"):
                    representatives[s.id] = sig_context.id_to_symb[s.parent_id]
                else:
                    representatives[s.id] = s
        else:
            # each symbol represents itself
            representatives = {s.id: s for s in unique_symbs}

        _scope_id_to_signature = dict[ScopeId, "SymbolSignature | None"]()

        def get_symbol_signature(s: SymbolDefinition) -> SymbolSignature | None:
            sid = sig_context.get_scope_id(s.id)
            if sid in _scope_id_to_signature:
                return _scope_id_to_signature[sid]
            if s.kind == "variable":
                sig = self._get_variable_signature(s, sig_context)
            else:
                sig = self._get_scope_signature(s, sig_context)
            _scope_id_to_signature[sid] = sig
            return sig

        signatures = {
            sid: sig
            for sid, rep in representatives.items()
            if (sig := get_symbol_signature(rep))
        }
        file_sig = self._get_module_signature(summary, sig_context)
        return FileSignatureInfo(signatures, file_sig)

    def _get_scope_signature(
        self,
        symb: SymbolDefinition,
        context: _SignatureContext,
    ) -> SymbolSignature | None:
        """Get the signature of the given symbol (if any)."""
        if symb.kind == "variable":
            raise ValueError(f"Should not be called for variables: {symb}")
        pfile = context.pfile
        assert_eq(symb.path, pfile.path)

        scopes = get_scope_by_range(pfile.scope_tree, symb.full_crange)
        if not scopes:
            logging.warning("Failed to get scope for symbol.")
            return None
        scope = scopes[0]
        if scope.range != symb.full_crange:
            logging.warning(
                "Scope range mismatch: %s, %s.\n%s, %s.",
                symb.full_crange,
                scope.range,
                symb,
                scope,
            )
            return None

        return self._get_scope_signature_impl(symb, scope, context)

    def _get_module_signature(
        self,
        summary: FileSummary,
        context: _SignatureContext,
    ) -> SymbolSignature:
        """Get the signature that represents a module."""
        top_defs = [
            s
            for s in summary.definitions
            if s.parent_id is None
            if should_show_def(s, context)
        ]
        kind2defs = groupby(top_defs, lambda s: s.kind)
        segs = [f"In file: {summary.path}"]
        for symb_type in sorted(kind2defs.keys()):
            # Rust can have multiple class blocks (e.g., def and multiple impls)
            # so we deduplicate here.
            elems = ", ".join(dict.fromkeys(s.name for s in kind2defs[symb_type]))
            segs.append(symb_type.upper() + ": " + elems)
        file_range = CharRange(0, summary.size_chars)
        line_range = LineRange(0, summary.size_lines)
        return SymbolSignature(
            "\n".join(segs), path=summary.path, crange=file_range, lrange=line_range
        )

    def _get_variable_signature(
        self, symb: SymbolDefinition, context: _SignatureContext
    ) -> SymbolSignature:
        """Get the signature of a variable definition."""
        pfile = context.pfile
        sig_path_marker = self._get_signature_path_marker(symb.path, symb, context)
        code = pfile.code[symb.full_crange.to_slice()]
        code = _normalize_signature(
            shorten_str(code, max_len=self.max_variable_chars, omit_mode="right"),
            lang=context.lang,
        )
        return SymbolSignature(
            "\n".join((sig_path_marker, code)),
            path=pfile.path,
            crange=symb.full_crange,
            lrange=pfile.lmap.crange_to_lrange(symb.full_crange),
        )

    def _get_scope_signature_impl(
        self,
        symb: SymbolDefinition,
        scope: SrcScope,
        context: _SignatureContext,
    ) -> SymbolSignature | None:
        lang: SignatureSupportedLanguageID = context.lang

        def add_prefix_and_docstr(sections: list[str], prefix: str, docstr: str):
            if lang == "python":
                # add scope prefix as the main signature
                # e.g., `def foo(x: int) -> str:`
                sections.append(_normalize_signature(prefix, lang))
                if docstr:
                    # also add the doc string, e.g., `"""Doc string."""`
                    sections.append(docstr)
            else:
                if docstr:
                    # add doc string
                    sections.append(docstr)
                # add the scope prefix as the main signature
                # e.g., `public int fib(int n) {`
                sections.append(_normalize_signature(prefix, lang))

        def get_method_signature(v: SymbolDefinition):
            """Get the signature of a method."""
            prefix = _normalize_signature(
                context.pfile.code[
                    v.prefix_crange.start : v.prefix_crange.stop
                ].rstrip(),
                lang,
            )
            prefix, shortened = shorten_str_detailed(
                prefix, max_len=self.max_method_chars, omit_mode="right"
            )
            # Don't add a suffix if we already shortened it.
            if shortened:
                return prefix
            return append_method_constructor_suffix(prefix)

        def get_constructor_signature(c: SrcScope):
            """Get the signature of a constructor."""
            prefix = _normalize_signature(c.prefix.code, lang)
            return append_method_constructor_suffix(prefix)

        def append_method_constructor_suffix(prefix: str):
            """Appends the method/constructor suffix."""
            if lang == "python":
                return prefix + " ..."
            elif lang in ("java", "javascript", "typescript", "go", "rust"):
                # We only want to insert the suffix for definitions, not declarations.
                if prefix.endswith("{"):
                    return prefix + "}"
                return prefix
            else:
                assert_never(lang)

        prefix = scope.prefix.code
        docstr = self._normalize_docstr(scope.docstr.code)
        sig_path_marker = self._get_signature_path_marker(symb.path, symb, context)
        sections = [sig_path_marker]
        if symb.kind == "function":
            add_prefix_and_docstr(sections, prefix, docstr)
            if (
                scope.children
                and isinstance(body := scope.children[-1], SrcSpan)
                and (lines := body.code.splitlines())
                and ("return " in lines[-1])
            ):
                # add last line of body if it contains "return"
                sections.append(lines[-1])

        elif symb.kind == "class":
            if lang == "python":
                constructor_name = "__init__"
            elif lang in ("javascript", "typescript"):
                constructor_name = "constructor"
            elif lang == "java":
                constructor_name = scope.name
            elif lang in ("go", "rust"):
                # Go and Rust don't have constructors
                constructor_name = None
            else:
                assert_never(lang)

            scope_id = context.get_scope_id(symb.id)
            children = context.children_map.get(scope_id, ())

            add_prefix_and_docstr(sections, prefix, docstr)
            attributes = "; ".join(
                c.variable_summary
                if not self.hide_equals_ellipsis
                else c.variable_summary.removesuffix(" = ...")
                for c in children
                if c.kind == "variable" and should_show_def(c, context)
            )
            if attributes:
                # show all attributes on a single line
                # e.g., `    # attributes: x: int; y: int=3`
                attr_str = shorten_str(
                    attributes, max_len=self.max_attributes_chars, omit_mode="right"
                )
                sections.append("attributes: " + attr_str)
            if not self.show_full_method_signatures:
                method_list = "; ".join(
                    name
                    for c in children
                    if c.kind == "function"
                    and (name := c.simple_name) != constructor_name
                    and should_show_def(c, context)
                )
                if method_list:
                    # show all method names on a single line
                    # e.g., `    # methods: fib; bar`
                    sections.append("methods: " + method_list)
            innerclasses = [
                c for c in children if c.kind == "class" and should_show_def(c, context)
            ]
            if innerclasses:
                # Rust can have multiple class blocks (e.g., def and multiple impls),
                # so we deduplicate here.
                innerclass_names = list(
                    dict.fromkeys([c.simple_name for c in innerclasses])
                )
                sections.append("innerclasses: " + "; ".join(innerclass_names))

            method_signatures = list[str]()

            # add constructor signatures
            method_signatures.extend(
                get_constructor_signature(c)
                for c in scope.children
                if isinstance(c, SrcScope) and c.name == constructor_name
            )
            if self.show_full_method_signatures:
                # add method signatures
                method_signatures.extend(
                    get_method_signature(c)
                    for c in children
                    if c.kind == "function"
                    and c.simple_name != constructor_name
                    and should_show_def(c, context)
                )
            if method_signatures:
                sections.append("\n\n".join(method_signatures))

        elif symb.kind == "variable":
            raise AssertionError("Should not be called for variables.")
        else:
            assert_never(symb.kind)

        if lang != "python":
            suffix_sig = _normalize_signature(scope.suffix.code, lang)
            # In Rust, unit structs have no suffix, so don't add an empty line.
            if suffix_sig:
                sections.append(suffix_sig)  # close the scope for a nicer signature

        return SymbolSignature(
            "\n".join(sections),
            path=symb.path,
            crange=symb.full_crange,
            lrange=context.pfile.lmap.crange_to_lrange(symb.full_crange),
        )

    def _normalize_docstr(self, docstr: str):
        if self.max_docstr_chars == 0:
            return ""
        docstr = docstr.strip("\n").rstrip()
        shortened = shorten_str(
            docstr, max_len=self.max_docstr_chars, omit_mode="right"
        )
        return shortened

    def _get_signature_path_marker(
        self,
        file: Path,
        symb: SymbolDefinition,
        context: _SignatureContext,
    ):
        """Return a string used to indicate the src location of a signature."""
        file_path = f"from: {shorten_str(str(file), self.max_sig_file_path_chars, omit_mode='left')}"
        parent_scopes = list[str]()
        while symb.parent_id:
            symb = context.id_to_symb[symb.parent_id]
            parent_scopes.append(symb.simple_name)

        scope_path = ".".join(reversed(parent_scopes))
        return f"{file_path}/{scope_path}" if scope_path else file_path


def _normalize_signature(sig_text: str, lang: LanguageID) -> str:
    """Normalize the given signature text."""
    # Currently this simply strips all comment lines.
    comment_marker = "#" if lang == "python" else "//"
    lines = sig_text.splitlines(keepends=True)
    lines = [s for s in lines if not s.strip().startswith(comment_marker)]
    return "".join(lines).strip("\n")


def should_show_def(
    v: SymbolDefinition,
    context: _SignatureContext,
):
    """Whether to show a variable in the signature."""
    if context.show_private:
        return True
    if context.lang == "python":
        return not _is_private_python_name(v.simple_name)
    elif context.lang in ("java", "javascript", "typescript"):
        # For variables we check the summary.
        # For classes/methods we check the prefix.
        prefix = context.pfile.code[v.prefix_crange.start : v.prefix_crange.stop]
        return "private " not in (
            v.variable_summary if v.kind == "variable" else prefix
        )
    elif context.lang == "go":
        return (
            not v.name
            or v.name[0].isupper()
            or (len(v.name) > 1 and v.name[0] == "." and v.name[1].isupper())
        )
    elif context.lang == "rust":
        return True
    else:
        assert_never(context.lang)


def _is_private_python_name(name: str) -> bool:
    """Return True if the given name is a private name in python."""
    return name.startswith("__")


@dataclass
class _SignatureContext:
    """File information needed for constructing symbol signatures."""

    pfile: ParsedFile
    """The parsed file."""
    lang: SignatureSupportedLanguageID
    """The language of the file.

    This should be equivalent to pfile.lang but with a stronger type.
    """
    id_to_symb: Mapping[SymbolDefinitionId, SymbolDefinition]
    """The mapping from symbol ids to symbols."""
    children_map: Mapping[ScopeId, Sequence[SymbolDefinition]]
    """The mapping from scope ids to their children."""
    show_private: bool
    """Whether to show private symbols."""

    def __post_init__(self):
        assert_eq(self.lang, self.pfile.lang)
        self._scope_id_cache = dict[SymbolDefinitionId, ScopeId]()

    def get_scope_id(self, symb_id: SymbolDefinitionId) -> ScopeId:
        """Get the ScopeId of the given symbol."""
        if symb_id in self._scope_id_cache:
            return self._scope_id_cache[symb_id]
        symb = self.id_to_symb[symb_id]
        if symb.parent_id is None:
            parent_sid = None
        else:
            parent_sid = self.get_scope_id(symb.parent_id)
        result = ScopeId(symb.name, parent_sid)
        self._scope_id_cache[symb_id] = result
        return result
