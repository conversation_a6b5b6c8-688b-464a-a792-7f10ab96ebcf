"""Interface for base.static_analysis in services."""

from __future__ import annotations

import dataclasses
from pathlib import Path
from typing import Protocol

from typing_extensions import Self, override

from base.languages import LanguageId
from base.static_analysis import (
    common,
    parsing,
    signature_index,
    signature_utils,
    usage_analysis,
)

# We reexport this exception.
# pylint:disable-next=unused-import
from base.static_analysis.parsing import ParsingFailedError  # noqa
from base.static_analysis.parsing import FileTypeNotSupportedError
from base.static_analysis.signature_index import FileSummaryWithSignatures
from base.static_analysis.usage_analysis import FileSummary


class SignatureAnalysisProtocol(Protocol):
    """Interface representing everything we need for a signature analysis."""

    def get_summary(self) -> FileSummary:
        """Returns a summary of a file."""
        raise NotImplementedError()

    def get_summary_with_signatures(self) -> FileSummaryWithSignatures:
        """Returns the signatures of a file."""
        raise NotImplementedError()

    @classmethod
    def create(
        cls, content: str, path: Path, language: LanguageId | None = None
    ) -> Self:
        """Returns a static analyzer for the given file.

        Raises:
            FileTypeNotSupportedError: If the file type is not supported.
            ParsingFailedError: If the file could not be parsed.
        """
        raise NotImplementedError()


@dataclasses.dataclass
class SignatureAnalysis(SignatureAnalysisProtocol):
    """Our default static analyzer implementation based on tree-sitter."""

    _pfile: usage_analysis.ParsedFile

    @override
    @classmethod
    def create(
        cls, content: str, path: Path, language: LanguageId | None = None
    ) -> Self:
        language_ = language and common.convert_to_static_analysis_language(language)
        if language_ is None:
            raise FileTypeNotSupportedError(language)
        robust_parser = parsing.ScopeTreeParser(parse_errored_root=True)
        pfile = usage_analysis.ParsedFile.parse(path, language_, content, robust_parser)
        return cls(pfile)

    @override
    def get_summary(self) -> FileSummary:
        return FileSummary.from_pfile(self._pfile)

    @override
    def get_summary_with_signatures(self) -> signature_index.FileSummaryWithSignatures:
        # TODO(AU-2116): Make this configurable.
        return signature_index.FileSummaryWithSignatures.from_pfile(
            self._pfile,
            signature_utils.SignaturePrinter(show_full_method_signatures=True),
        )
