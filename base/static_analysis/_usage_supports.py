"""This file contains the implementation of usage supports for different languages.

These implementations are used by `static_analysis.usage_analysis.py`.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Collection, Iterable, Mapping, Union

import tree_sitter as ts

from base.static_analysis.common import LanguageID, decode_bytes
from base.static_analysis.parsing import FieldName, TsNodeType, _get_field_by_address


@dataclass(frozen=True)
class _AttrAccessDef:
    """Defines how to get the attribute and receiver of an attribute access.

    An access is assumed to have the shape: [receiver].[attribute]
    """

    attribute: FieldName
    """A tree-sitter field name used to get the attribute node from the access node."""

    receiver: FieldName | None
    """A tree-sitter field name used to get the receiver node from the access node."""


@dataclass(frozen=True)
class _LocalDef:
    """Represents a local definition."""

    name: ts.Node
    """The node that introduces the name of the local definition."""

    for_outer_scope: bool = False
    """Whether this definition is visible in the outer scope."""


@dataclass(frozen=True)
class _ImportedModule:
    """Represents a module import."""

    alias: str | None
    """A local name this module is aliased to."""

    module_name: ts.Node
    """The name of the module being imported."""


@dataclass(frozen=True)
class _ImportedDef:
    """Represents a definition imported from a module."""

    alias: str | None
    """The local aliased name of the imported name."""

    global_name: ts.Node
    """The original name of the imported definition."""

    module_name: ts.Node
    """The module in which this def is originally defined."""

    can_be_module: bool
    """Whether this definition can potentially be another module.
    In some languages like Python, this can be true; in other languages like
    TypeScript, this must be false.
    """


_DefSource = Union[_LocalDef, _ImportedDef, _ImportedModule]
"""The known source of a visible definition."""


class _UsageSupport(ABC):
    """Support a generic usage analysis algorithm for a given langauge."""

    attr_access_defs: Mapping[TsNodeType, _AttrAccessDef]
    """Used to identify and get the attribute name from tree-sitter nodes."""

    usage_identifier_types: Collection[str] = ("identifier",)
    """The node types of identifiers that should be considered as usages."""

    this_keywords: Collection[str] = ()
    """The keywords that should be considered as `this` in the language.

    We can report more precise usages for `this.f` accesses.
    """

    has_implicit_attr_access: bool = False
    """Whether the language supports implicit attribute access.

    Most langauges use explicit attribute access, e.g., `self.f` or `this.x`.
    However, some languages like Java support implicit attribute access, e.g., a
    local name `f` may be treated as `this.f`.
    """

    max_rec_depth: int = 200
    """The maximum depth of the tree to traverse."""

    max_num_nodes: int = 100_000
    """The maximum number of nodes to traverse."""

    @abstractmethod
    def find_definitions(self, node: ts.Node) -> Iterable[_DefSource]:
        """Return the visible definitions introduced by the given node.

        Note: this function will be run on every node, so it should not return
        anything that its children node will also return.
        """
        return ()

    @abstractmethod
    def find_non_usage_nodes(self, node: ts.Node) -> Iterable[ts.Node]:
        """Return a list of nodes to be ignored in the second usage analysis pass.

        This should be things such as identifiers of the keyword arguments or
        private variable names.
        Note: this function will be run on every node, so it should not return
        anything that its children node will also return.
        """
        return ()

    @abstractmethod
    def parse_module_path(self, module_name: str) -> Path:
        """Turn language-specific module name into a path.

        If the returned path starts with `..`, it is interpreted as relative to
        the current file; otherwise, it is interpreted as relative to the project root.
        """


# ---------------------------------------------------------------------------
# ------------------- Supports for different langauges  ---------------------


class _PythonUsageSupport(_UsageSupport):
    attr_access_defs = {
        "attribute": _AttrAccessDef(attribute="attribute", receiver="object"),
    }

    _ScopeTypes = ("function_definition", "class_definition", "lambda")
    this_keywords = ("self",)

    def find_definitions(self, node: ts.Node) -> Iterable[_DefSource]:
        def get_lhs_vars(lhs: ts.Node) -> Iterable[_LocalDef]:
            if lhs.type == "identifier":
                yield _LocalDef(lhs)
            elif lhs.type in ("pattern_list", "tuple_pattern"):
                for c in lhs.children:
                    if c.type == "identifier":
                        yield _LocalDef(c)

        node_type = node.type
        if node_type in ("assignment", "for_statement", "for_in_clause"):
            # the lhs of an assignment statement
            if left := node.child_by_field_name("left"):
                yield from get_lhs_vars(left)
        elif node_type == "named_expression":
            # the lhs of a walrus expression
            if name := node.child_by_field_name("name"):
                yield from get_lhs_vars(name)
        elif node_type == "with_item":
            # the 'as' part of a with clause.
            if (
                alias := (
                    node.child_by_field_name("alias")
                    or _get_field_by_address(node, ("value", "alias", 0))
                )
            ) and alias.type == "identifier":
                yield _LocalDef(alias)
        elif node_type in self._ScopeTypes:
            # the name and parameter list of function definitions
            if params := node.child_by_field_name("parameters"):
                # model each parameter as a local def
                for child in params.children:
                    if child.type == "identifier":
                        yield _LocalDef(child)
                    elif child.type == "typed_parameter" and child.children:
                        yield _LocalDef(child.children[0])
                    elif child.type == "default_parameter":
                        if name := child.child_by_field_name("name"):
                            yield _LocalDef(name)
                    elif (
                        child.type in ("list_splat_pattern", "dictionary_splat_pattern")
                        and child.named_children
                    ):
                        yield _LocalDef(child.named_children[0])
            if name := node.child_by_field_name("name"):
                yield _LocalDef(name, for_outer_scope=True)
        elif node_type == "import_statement":
            for c in node.children:
                # e.g., `import foo` (tree-sitter will parse `foo` as a dotted_name)
                if c.type == "dotted_name":
                    yield _ImportedModule(None, c)
                # e.g., `import foo as bar`
                if (
                    c.type == "aliased_import"
                    and (module := c.child_by_field_name("name"))
                    and (alias := c.child_by_field_name("alias"))
                ):
                    alias = decode_bytes(alias.text)
                    yield _ImportedModule(alias, module)
        elif node_type == "import_from_statement":
            module = node.child_by_field_name("module_name")
            if module is None:
                return
            for x in node.children_by_field_name("name"):
                # e.g., `from foo import a`
                if x.type == "dotted_name":
                    # there will not be any dots in this "dotted_name"
                    yield _ImportedDef(None, x, module, can_be_module=True)
                # e.g., `from foo import a as b`
                elif (
                    x.type == "aliased_import"
                    and (name := x.child_by_field_name("name"))
                    and (alias := x.child_by_field_name("alias"))
                ):
                    alias = decode_bytes(alias.text)
                    yield _ImportedDef(alias, name, module, can_be_module=True)
            # e.g., `from foo import *`
            if (
                len(node.named_children) == 2
                and node.named_children[1].type == "wildcard_import"
            ):
                yield _ImportedModule(None, module)

    def find_non_usage_nodes(self, node: ts.Node) -> Iterable[ts.Node]:
        node_type = node.type
        if node_type == "identifier":
            if node.text.startswith(b"__"):
                yield node
        elif node_type == "keyword_argument":
            if name := node.child_by_field_name("name"):
                yield name
        elif node_type == "import_statement":
            yield node
        elif node_type == "import_from_statement":
            yield node

    def parse_module_path(self, module_name: str) -> Path:
        # Examples:
        # '.' -> '../__init__'
        # '..a.b' -> '../../a/b'
        # 'a.b' -> 'a/b'
        if module_name.startswith("."):
            # count how many leading dots
            dot_count = 0
            for c in module_name:
                if c == ".":
                    dot_count += 1
                else:
                    break
            remaining = module_name[dot_count:]
            if not remaining:
                remaining = "__init__"
            return Path(*([".."] * dot_count), *remaining.split("."))
        else:
            return Path(*module_name.split("."))


class _JavaUsageSupport(_UsageSupport):
    attr_access_defs = {
        "field_access": _AttrAccessDef(attribute="field", receiver="object"),
        "method_invocation": _AttrAccessDef(attribute="name", receiver="object"),
        "scoped_type_identifier": _AttrAccessDef(attribute=1, receiver=0),
    }

    _ScopeTypes = ("method_declaration", "constructor_declaration", "class_declaration")
    usage_identifier_types = ("identifier", "type_identifier", "this")
    this_keywords = ("this",)
    has_implicit_attr_access = True

    def find_definitions(self, node: ts.Node) -> Iterable[_DefSource]:
        def get_local_vars(target: ts.Node) -> Iterable[_LocalDef]:
            name = target.child_by_field_name("name")
            if name and name.type == "identifier":
                yield _LocalDef(name)

        node_type = node.type
        if node_type in self._ScopeTypes:
            # function parameters introduce local variables
            if params := node.child_by_field_name("parameters"):
                for child in params.children:
                    if child.type == "formal_parameter":
                        yield _LocalDef(child.children[1])
            if name := node.child_by_field_name("name"):
                yield _LocalDef(name, for_outer_scope=True)
        elif node_type == "variable_declarator":
            # local variables introduced by variable declaration statements
            # e.g., `int x = 1``
            yield from get_local_vars(node)
        elif node_type in (
            "enhanced_for_statement",
            "catch_formal_parameter",
            "resource",
        ):
            # `for (int num : numbers) {...}`
            # `catch (Exception e) {...}`
            # `try (InputStream in = new FileInputStream(file)) {...}`
            yield from get_local_vars(node)
        elif node_type == "import_declaration":
            if (
                node.named_child_count == 1
                and (child := node.named_children[0]).type == "scoped_identifier"
            ):
                # `import java.util.List;`
                scope = child.child_by_field_name("scope")
                name = child.child_by_field_name("name")
                if scope and name:
                    yield _ImportedDef(None, name, scope, can_be_module=False)
            elif (
                node.named_child_count == 2
                and (name := node.named_children[0]).type == "identifier"
            ):
                # `import foo.*;`
                yield _ImportedModule(None, name)

    def find_non_usage_nodes(self, node: ts.Node) -> Iterable[ts.Node]:
        if node.type == "import_declaration":
            yield node

    def parse_module_path(self, module_name: str) -> Path:
        return Path(*module_name.split("."))


class _CppUsageSupport(_UsageSupport):
    attr_access_defs = {
        "field_expression": _AttrAccessDef(attribute="field", receiver="argument"),
    }
    usage_identifier_types = (
        "identifier",
        "type_identifier",
        "field_identifier",
        "this",
    )
    this_keywords = ("this",)
    has_implicit_attr_access = True

    def find_definitions(self, node: ts.Node) -> Iterable[_LocalDef | _ImportedDef]:
        def get_declarator(node: ts.Node) -> ts.Node | None:
            if (
                name := node.child_by_field_name("declarator")
            ) and name.type in self.usage_identifier_types:
                return name

        if node.type == "function_declarator":
            if params := node.child_by_field_name("parameters"):
                for child in params.children:
                    if child.type == "parameter_declaration":
                        if name := get_declarator(child):
                            yield _LocalDef(name)

            if name := get_declarator(node):
                yield _LocalDef(name, for_outer_scope=True)
        elif node.type == "class_specifier":
            if name := node.child_by_field_name("name"):
                yield _LocalDef(name, for_outer_scope=True)

    def find_non_usage_nodes(self, node: ts.Node) -> Iterable[ts.Node]:
        return ()

    def parse_module_path(self, module_name: str) -> Path:
        return Path(*module_name.split("/")).with_suffix("")


class _TypescriptUsageSupport(_UsageSupport):
    attr_access_defs = {
        "member_expression": _AttrAccessDef(attribute="property", receiver="object"),
    }
    usage_identifier_types = ("identifier", "type_identifier", "this")
    this_keywords = ("this",)

    _ScopeTypes = (
        "interface_declaration",
        "abstract_class_declaration",
        "class_declaration",
        "function_declaration",
        "method_definition",
        "arrow_function",
    )

    def find_definitions(self, node: ts.Node) -> Iterable[_DefSource]:
        def get_lhs_vars(lhs: ts.Node) -> Iterable[_LocalDef]:
            if lhs.type == "identifier":
                yield _LocalDef(lhs)
            elif lhs.type == "object_pattern":
                for c in lhs.named_children:
                    if c.type == "pair_pattern" and (
                        v := c.child_by_field_name("value")
                    ):
                        yield _LocalDef(v)

        if node.type == "variable_declarator":
            if name := node.child_by_field_name("name"):
                yield from get_lhs_vars(name)
        elif node.type in self._ScopeTypes:
            if params := node.child_by_field_name("parameters"):
                for child in params.named_children:
                    # TypeScript function arg is differently structured than JavaScript
                    name = child.child_by_field_name("pattern")
                    if name and name.type == "identifier":
                        yield _LocalDef(name)
            if name := node.child_by_field_name("name"):
                yield _LocalDef(name, for_outer_scope=True)
        elif node.type == "import_statement":
            if not (
                (module_name := node.child_by_field_name("source"))
                and "string" in module_name.type
            ):
                return
            clauses = tuple(
                c
                for n in node.named_children
                if n.type == "import_clause"
                for c in n.named_children
            )
            if not clauses:
                yield _ImportedModule(None, module_name)
                return
            for clause in clauses:
                # default import: import foo from "bar"
                if clause.type == "identifier":
                    # TODO: handle TypeScript default imports properly
                    yield _ImportedModule(None, module_name)
                elif clause.type == "named_imports":
                    for c in clause.named_children:
                        name = c.child_by_field_name("name")
                        if name is None:
                            continue
                        alias = c.child_by_field_name("alias")
                        if alias:
                            alias = decode_bytes(alias.text)
                        yield _ImportedDef(
                            alias, name, module_name, can_be_module=False
                        )
                elif clause.type == "namespace_import" and clause.named_children:
                    alias = decode_bytes(clause.named_children[0].text)
                    yield _ImportedModule(alias, module_name)

        elif node.type == "switch_case":
            if value := node.child_by_field_name("value"):
                yield from get_lhs_vars(value)
        elif node.type == "catch_clause":
            if param := node.child_by_field_name("parameter"):
                yield from get_lhs_vars(param)

    def find_non_usage_nodes(self, node: ts.Node) -> Iterable[ts.Node]:
        if node.type == "pair_pattern":
            if name := node.child_by_field_name("key"):
                yield name
        elif node.type == "import_statement":
            yield node

    def parse_module_path(self, module_name: str) -> Path:
        """Given the text of a module name, break it into name segments."""
        # e.g., if the import statement is `import "./foo/bar"`
        # we will have module_name = "./foo/bar"
        # we want to build `Path("../foo/bar")` from it
        module_name = module_name.strip("\"'")
        if module_name.startswith("."):
            # count how many leading dots
            dot_count = 0
            for c in module_name:
                if c == ".":
                    dot_count += 1
                else:
                    break
            remaining = module_name[dot_count:]
            return Path(*([".."] * dot_count), *remaining.split("/"))
        else:
            return Path(*module_name.split("/"))


class _GoUsageSupport(_UsageSupport):
    attr_access_defs = {
        "selector_expression": _AttrAccessDef(attribute="field", receiver="operand"),
    }
    usage_identifier_types = ("identifier", "type_identifier")

    # We treat type_identifiers with these names as non-usage nodes.
    go_common_types = {
        b"int",
        b"bool",
        b"string",
        b"int8",
        b"int16",
        b"int32",
        b"float32",
        b"float64",
        b"uint",
        b"complex64",
    }

    def find_definitions(self, node: ts.Node) -> Iterable[_DefSource]:
        def get_spec_vars(lhs: ts.Node) -> Iterable[_LocalDef]:
            for c in lhs.children_by_field_name("name"):
                if c.type == "identifier":
                    yield _LocalDef(c)

        def get_expr_list_vars(lhs: ts.Node) -> Iterable[_LocalDef]:
            for c in lhs.named_children:
                if c.type == "identifier":
                    yield _LocalDef(c)

        # e.g., `var x, y int = 1, 2`
        if node.type in ("var_declaration", "const_declaration"):
            for spec in node.named_children:
                yield from get_spec_vars(spec)
        # e.g., `x, y := 1, 2`
        elif node.type == "short_var_declaration":
            if left := node.child_by_field_name("left"):
                yield from get_expr_list_vars(left)
        # e.g., `for key, value := range ...`
        elif node.type == "range_clause":
            if left := node.child_by_field_name("left"):
                yield from get_expr_list_vars(left)
        # e.g., `func foo(x, y int) {...}`
        elif node.type in ("function_declaration", "method_declaration", "type_spec"):
            if params := node.child_by_field_name("parameters"):
                for param in params.children:
                    yield from get_spec_vars(param)
            # the receiver part of a method is a parameter
            # e.g., we should return `x` in `func (x Foo) method() {...}`
            rec = node.child_by_field_name("receiver")
            if rec and rec.type == "parameter_list":
                for param in rec.children:
                    yield from get_spec_vars(param)
            if name := node.child_by_field_name("name"):
                yield _LocalDef(name, for_outer_scope=True)
        # e.g., `import foo_alias "foo"`
        elif node.type == "import_spec":
            module_name = node.child_by_field_name("path")
            if module_name is None:
                return
            alias = node.child_by_field_name("name")
            if alias:
                alias = decode_bytes(alias.text)
            elif parts := self.parse_module_path(decode_bytes(module_name.text)).parts:
                # in go, the last part of the module path becomes a local alias
                alias = parts[-1]
            yield _ImportedModule(alias, module_name)

    def find_non_usage_nodes(self, node: ts.Node) -> Iterable[ts.Node]:
        node_type = node.type
        if node_type == "type_identifier":
            if node.text in self.go_common_types:
                yield node
        elif node_type == "import_spec":
            if alias := node.child_by_field_name("name"):
                yield alias

    def parse_module_path(self, module_name: str) -> Path:
        # e.g., if the import statement is `import "foo/bar"`
        # we will have module_name = "foo/bar"
        # we want to build the path `foo/bar` from it
        module_name = module_name.strip("\"'")
        segments = (seg for seg in module_name.split("/") if seg)
        return Path(*segments)


class _RustUsageSupport(_UsageSupport):
    attr_access_defs = {
        "field_expression": _AttrAccessDef(attribute="field", receiver="value"),
        "scoped_identifier": _AttrAccessDef(attribute="name", receiver="path"),
        "scoped_type_identifier": _AttrAccessDef(attribute="name", receiver="path"),
    }
    usage_identifier_types = {
        "identifier",
        "type_identifier",
        "field_identifier",
        "shorthand_field_identifier",
        "self",
    }
    this_keywords = ("self",)

    _typed_pattern_types = {
        "tuple_struct_pattern",
        "struct_pattern",
    }
    """Pattern nodes whose first child is the type node."""

    _untyped_pattern_types = {
        "tuple_pattern",
        "slice_pattern",
        "field_pattern",
        "captured_pattern",
    }
    """Pattern nodes without a type node."""

    def find_definitions(self, node: ts.Node) -> Iterable[_DefSource]:
        def get_lhs_vars(node: ts.Node) -> Iterable[_LocalDef]:
            if node.type in self.usage_identifier_types:
                yield _LocalDef(node)

        if node.type in ("const_item", "static_item", "field_declaration"):
            if name := node.child_by_field_name("name"):
                yield from get_lhs_vars(name)
        elif node.type in self._untyped_pattern_types:
            for child in node.named_children:
                yield from get_lhs_vars(child)
        elif node.type in self._typed_pattern_types:
            # e.g., Foo5(x3, y3)
            # skip the first children, which is the type node `Foo`
            for child in node.named_children[1:]:
                yield from get_lhs_vars(child)
        elif node.type in ("function_item", "struct_item", "enum_item", "trait_item"):
            if name := node.child_by_field_name("name"):
                yield _LocalDef(name, for_outer_scope=True)
        elif node.type == "use_declaration" and node.named_child_count > 0:
            child = node.named_children[0]
            if child.type in "scoped_identifier":
                # `use std::collections::HashMap;`
                path = child.child_by_field_name("path")
                name = child.child_by_field_name("name")
                if path and name:
                    yield _ImportedDef(None, name, path, can_be_module=False)
            elif child.type in "scoped_use_list":
                # `use std::collections::{HashMap, HashSet};`
                if path := child.child_by_field_name("path"):
                    if name_list := child.child_by_field_name("list"):
                        for name in name_list.named_children:
                            yield _ImportedDef(None, name, path, can_be_module=False)
            elif child.type == "use_wildcard" and child.child_count > 0:
                # `use foo.*;`
                yield _ImportedModule(None, child.children[0])

        # sometimes identifier nodes are used as patterns directly
        if (
            pattern_child := node.child_by_field_name("pattern")
        ) and pattern_child.type in self.usage_identifier_types:
            yield _LocalDef(pattern_child)

    def find_non_usage_nodes(self, node: ts.Node) -> Iterable[ts.Node]:
        if node.type == "field_pattern":
            if name := node.child_by_field_name("name"):
                yield name
        elif node.type == "use_declaration":
            yield node

    def parse_module_path(self, module_name: str) -> Path:
        return Path(*module_name.split("::"))


all_usage_supports: dict[LanguageID, _UsageSupport] = {
    "python": _PythonUsageSupport(),
    "java": _JavaUsageSupport(),
    "cpp": _CppUsageSupport(),
    "typescript": _TypescriptUsageSupport(),
    "javascript": _TypescriptUsageSupport(),
    "go": _GoUsageSupport(),
    "rust": _RustUsageSupport(),
}
