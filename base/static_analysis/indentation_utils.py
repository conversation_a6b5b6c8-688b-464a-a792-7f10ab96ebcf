"""Utilities for computing indentation levels."""


def compute_line_indentations(lines: list[str]) -> list[int]:
    """Compute the indentation level of each line in a document.

    Notes:
    - An empty line's indentation level is set to that of the next non-empty line.
    - A tab will be considered like it's a single space.
    - We require lines to be a list and not a Sequence to prevent people from\
        accidentally passing in a string since a string is a sequence of string.
    """

    indent_levels = list[int]()
    # first pass, only compute on non-empty lines
    for line in lines:
        if line.isspace():
            indent_levels.append(-1)  # delay the computation
        else:
            indent_levels.append(len(line) - len(line.lstrip()))

    # second pass, assign indentation to empty lines
    next_indentation = 0
    for i in reversed(range(len(lines))):
        if indent_levels[i] == -1:
            indent_levels[i] = next_indentation
        else:
            next_indentation = indent_levels[i]

    return indent_levels
