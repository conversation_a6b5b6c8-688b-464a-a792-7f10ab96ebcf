"""Utilities to identify variables and class fields."""

from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
import itertools
from typing import TYPE_CHECKING, Iterable

import tree_sitter as ts
from typing_extensions import final, override

from base.ranges.range_types import <PERSON><PERSON>Rang<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.common import (
    LanguageID,
    decode_bytes,
    shorten_str,
    tsnode_to_crange,
)
from base.static_analysis.parsing import _node_text

if TYPE_CHECKING:
    from base.static_analysis.usage_analysis import ParsedFile


@dataclass(frozen=True)
class VariableDef:
    """The definition of a globally visible variable."""

    name: str
    """The name of the definition (function name, class name, etc.)."""

    crange: CharRange
    """The char range of the definition in its source file."""

    name_crange: CharRange
    """The char range of the definition's name identifier in its source file."""

    type_annotation: str
    """The type annotation associated with this definition."""

    rhs: str
    """The right-hand side of the definition."""

    modifiers: str
    """The modifiers associated with this definition."""


class _VariableSupport(ABC):
    max_rec_depth: int = 100
    """The maximum depth of the tree to traverse."""

    max_rhs_chars: int = 6
    """Max number of characters to show in the RHS of a variable summary."""

    max_type_chars: int = 40
    """Max number of characters to show in the type annotation of a variable summary."""

    max_num_vars: int = 1_000
    """Max number of variables to find."""

    @final
    def find_variables(self, pfile: ParsedFile) -> Iterable[VariableDef]:
        """Returns fields and global variables defined in the file (up to a maximum number)."""
        return itertools.islice(
            self.find_variables_impl(pfile),
            self.max_num_vars,
        )

    @abstractmethod
    def find_variables_impl(self, pfile: ParsedFile) -> Iterable[VariableDef]:
        """Returns all fields and global variables defined in the file."""

    @abstractmethod
    def get_variable_summary(self, var_def: VariableDef) -> str:
        """Returns a short summary of a variable definition.

        This typically includes the variable name, visibility modifiers, type
        annotation, and default value.
        """

    def _shorten_type(self, type_text: str) -> str:
        return shorten_str(type_text, self.max_type_chars, "right")


class _PythonVariableSupport(_VariableSupport):
    @override
    def find_variables_impl(self, pfile: ParsedFile) -> Iterable[VariableDef]:
        bmap = pfile.bmap
        max_depth = self.max_rec_depth

        def recursion(
            node: ts.Node, in_class: bool, in_function: bool, depth: int
        ) -> Iterable[VariableDef]:
            if node.type == "assignment":
                # get the LHS
                if left := node.child_by_field_name("left"):
                    name_nodes = list[ts.Node]()
                    # e.g., `x: int = 1`
                    if left.type == "identifier" and not in_function:
                        name_nodes = [left]
                    # e.g., `self.x = 1`
                    elif (
                        in_class
                        and left.type == "attribute"
                        # NOTE: technically `self` doesn't have to be named as 'self',
                        # but this is simple and probably works well enough in practice.
                        and (_node_text(left.child_by_field_name("object")) == "self")
                        and (attr_node := left.child_by_field_name("attribute"))
                    ):
                        name_nodes = [attr_node]
                    # e.g., `x, y = 1, 2`
                    elif left.type == "pattern_list" and not in_function:
                        name_nodes = [
                            n for n in left.children if n.type == "identifier"
                        ]
                    full_range = None
                    type_annotation = None
                    rhs = None
                    for name_node in name_nodes:
                        name = decode_bytes(name_node.text)
                        name_range = tsnode_to_crange(name_node, bmap)
                        if not full_range:
                            full_range = tsnode_to_crange(node, bmap)
                        if not type_annotation:
                            type_annotation = _node_text(
                                node.child_by_field_name("type")
                            )
                        if not rhs:
                            # The rhs is tricky for multiple assigns, so ignore it.
                            rhs = (
                                _node_text(node.child_by_field_name("right"))
                                if len(name_nodes) == 1
                                else "..."
                            )
                        yield VariableDef(
                            name,
                            full_range,
                            name_range,
                            type_annotation,
                            rhs,
                            modifiers="",
                        )

            elif node.type == "function_definition" and (
                name := _node_text(node.child_by_field_name("name"))
            ):
                if name not in ("__init__", "__post_init__"):
                    return  # skip non-constructor functions
                in_function = True
            elif node.type == "class_definition":
                in_class = True
            # we need to traverse all named children since Python can create class
            # fields inside nested syntactic structures, e.g.,
            # with context():
            #    self.new_attribute = 1
            if depth > max_depth:
                return
            for child in node.named_children:
                yield from recursion(
                    child,
                    in_class,
                    in_function,
                    depth + 1,
                )

        return recursion(pfile.ts_tree.root_node, False, False, 0)

    def get_variable_summary(self, var_def: VariableDef) -> str:
        annotation = var_def.name
        if ty := var_def.type_annotation:
            annotation += f": {self._shorten_type(ty)}"
        if var_def.rhs:
            if len(var_def.rhs) <= 6:
                annotation += f" = {var_def.rhs}"
            else:
                annotation += " = ..."
        return annotation


class _JavaVariableSupport(_VariableSupport):
    @override
    def find_variables_impl(self, pfile: ParsedFile) -> Iterable[VariableDef]:
        bmap = pfile.bmap
        max_depth = self.max_rec_depth

        def recursion(node: ts.Node, depth: int) -> Iterable[VariableDef]:
            # e.g., `public int age = 1;` or `public int x, y = 1;`
            if node.type == "field_declaration":
                type_annotation = None
                # get the `age = 1` part
                for assign in node.children_by_field_name("declarator"):
                    # the `age` part
                    name_node = assign.child_by_field_name("name")
                    # the `1` part
                    rhs_node = assign.child_by_field_name("value")
                    # the `public` part
                    modifier_text = ""
                    if (
                        modifier_node := node.named_child(0)  # type: ignore
                    ) and modifier_node.type == "modifiers":
                        modifier_text = self._simplify_modifiers(
                            _node_text(modifier_node)
                        )

                    if name_node and name_node.type == "identifier":
                        name = decode_bytes(name_node.text)
                        name_range = tsnode_to_crange(name_node, bmap)
                        full_range = tsnode_to_crange(node, bmap)
                        if not type_annotation:
                            # the `int` part
                            type_annotation = _node_text(
                                node.child_by_field_name("type")
                            )
                        yield VariableDef(
                            name,
                            full_range,
                            name_range,
                            type_annotation,
                            rhs=_node_text(rhs_node),
                            modifiers=modifier_text,
                        )
            elif node.type == "enum_body":
                for child in node.children:
                    if child.type == "enum_constant":
                        if name := child.child_by_field_name("name"):
                            yield VariableDef(
                                _node_text(name),
                                tsnode_to_crange(child, bmap),
                                name_crange=tsnode_to_crange(name, bmap),
                                type_annotation="",
                                rhs="",
                                modifiers="",
                            )
            elif node.type == "method_declaration":
                # cannot declare fields inside methods, so no need to recurse
                return
            if depth > max_depth:
                return
            for child in node.named_children:
                yield from recursion(child, depth + 1)

        return recursion(pfile.ts_tree.root_node, 0)

    def _simplify_modifiers(self, modifier_text: str) -> str:
        if modifier_text == "public":
            return ""
        return modifier_text.replace("public ", "")

    def get_variable_summary(self, var_def: VariableDef) -> str:
        annotation = f"{var_def.name}"
        if ty := self._shorten_type(var_def.type_annotation):
            annotation = f"{ty} {annotation}"
        if var_def.modifiers:
            annotation = f"{var_def.modifiers} {annotation}"
        if var_def.rhs:
            if len(var_def.rhs) <= 6:
                annotation += f" = {var_def.rhs}"
            else:
                annotation += " = ..."
        return annotation


class _TypeScriptVariableSupport(_VariableSupport):
    @override
    def find_variables_impl(self, pfile: ParsedFile) -> Iterable[VariableDef]:
        bmap = pfile.bmap
        max_depth = self.max_rec_depth

        def get_modifier(assign_node: ts.Node, name_node: ts.Node) -> str:
            modifier_brange = ByteRange(assign_node.start_byte, name_node.start_byte)
            modifier_crange = bmap.brange_to_crange(modifier_brange)
            modifier_text = pfile.code[modifier_crange.to_slice()]
            return modifier_text.replace("public ", "")

        def recursion(node: ts.Node, depth: int) -> Iterable[VariableDef]:
            # e.g., `public static color: string = "red";`
            if node.type in ("public_field_definition", "property_signature"):
                # the `color` part
                name_node = node.child_by_field_name("name")
                # the `: string` part
                type_node = node.child_by_field_name("type")
                # the `"red"` part
                rhs_node = node.child_by_field_name("value")

                if name_node:
                    yield VariableDef(
                        _node_text(name_node),
                        tsnode_to_crange(node, bmap),
                        name_crange=tsnode_to_crange(name_node, bmap),
                        type_annotation=_node_text(type_node),
                        rhs=_node_text(rhs_node),
                        modifiers=get_modifier(node, name_node),
                    )
            # e.g., `const color: string = "red";`
            elif node.type == "lexical_declaration":
                if (
                    declarator := node.named_child(0)  # type: ignore
                ) and declarator.type == "variable_declarator":
                    # the `color` part
                    name_node = declarator.child_by_field_name("name")
                    # the `"red"` part
                    rhs_node = declarator.child_by_field_name("value")
                    # the `: string` part
                    type_node = declarator.child_by_field_name("type")
                    if not name_node:
                        return
                    yield VariableDef(
                        _node_text(name_node),
                        tsnode_to_crange(node, bmap),
                        name_crange=tsnode_to_crange(name_node, bmap),
                        type_annotation=_node_text(type_node),
                        rhs=_node_text(rhs_node),
                        modifiers=get_modifier(node, name_node),
                    )
            elif node.type == "enum_body":
                for child in node.children:
                    if child.type == "property_identifier":
                        yield VariableDef(
                            _node_text(child),
                            tsnode_to_crange(child, bmap),
                            name_crange=tsnode_to_crange(child, bmap),
                            type_annotation="",
                            rhs="",
                            modifiers="",
                        )
                    elif child.type == "enum_assignment":
                        if name := child.child_by_field_name("name"):
                            yield VariableDef(
                                _node_text(name),
                                tsnode_to_crange(child, bmap),
                                name_crange=tsnode_to_crange(name, bmap),
                                type_annotation="",
                                rhs=_node_text(child.child_by_field_name("value")),
                                modifiers="",
                            )
            elif node.type == "method_definition":
                # cannot declare fields inside methods, so no need to recurse
                return

            if depth > max_depth:
                return
            for child in node.named_children:
                yield from recursion(child, depth + 1)

        return recursion(pfile.ts_tree.root_node, 0)

    def get_variable_summary(self, var_def: VariableDef) -> str:
        ty = self._shorten_type(var_def.type_annotation)
        annotation = f"{var_def.name}{ty}"
        if var_def.modifiers:
            annotation = f"{var_def.modifiers}{annotation}"
        if var_def.rhs:
            if len(var_def.rhs) <= 6:
                annotation += f" = {var_def.rhs}"
            else:
                annotation += " = ..."
        return annotation


class _GoVariableSupport(_VariableSupport):
    @override
    def find_variables_impl(self, pfile: ParsedFile) -> Iterable[VariableDef]:
        bmap = pfile.bmap
        max_depth = self.max_rec_depth

        def recursion(node: ts.Node, depth: int) -> Iterable[VariableDef]:
            # e.g., `var foo, bar int = 1`
            if node.type in ("const_spec", "var_spec"):
                if node.parent is None or node.parent.type not in (
                    "const_declaration",
                    "var_declaration",
                ):
                    return
                assign_range = None
                type_annotation = None
                rhs = None
                for name in node.children_by_field_name("name"):
                    if (name_str := _node_text(name)) == ",":
                        # for unknown reason this can happen
                        continue
                    if not assign_range:
                        # If there are too many siblings the parent's range would be
                        # too large, so we just use the current node's range.
                        assign_range = tsnode_to_crange(node, bmap)
                    if not type_annotation:
                        type_annotation = _node_text(node.child_by_field_name("type"))
                    if not rhs:
                        rhs = _node_text(node.child_by_field_name("value"))
                    yield VariableDef(
                        name_str,
                        assign_range,
                        name_crange=tsnode_to_crange(name, bmap),
                        type_annotation=type_annotation,
                        rhs=rhs,
                        modifiers="",
                    )
            # e.g., `x, y := 1, 2`
            elif node.type == "short_var_declaration":
                if (left := node.child_by_field_name("left")) is None or (
                    right := node.child_by_field_name("right")
                ) is None:
                    return

                rhs_info = "..." if len(right.named_children) > 1 else _node_text(right)
                full_crange = tsnode_to_crange(node, bmap)
                for name in left.named_children:
                    if name.type != "identifier":
                        continue
                    yield VariableDef(
                        _node_text(name),
                        full_crange,
                        name_crange=tsnode_to_crange(name, bmap),
                        type_annotation="",
                        rhs=rhs_info,
                        modifiers="",
                    )
            # e.g., the `x y int` part in `type Foo struct { x y int }`
            elif node.type == "field_declaration":
                full_crange = tsnode_to_crange(node, bmap)
                type_annotation = _node_text(node.child_by_field_name("type"))
                for name in node.children_by_field_name("name"):
                    yield VariableDef(
                        _node_text(name),
                        full_crange,
                        name_crange=tsnode_to_crange(name, bmap),
                        type_annotation=type_annotation,
                        rhs="",
                        modifiers="",
                    )
            elif node.type in ("function_declaration", "method_declaration"):
                # do not recurse since we only report global variables
                return
            if depth > max_depth:
                return
            for child in node.named_children:
                yield from recursion(child, depth + 1)

        return recursion(pfile.ts_tree.root_node, 0)

    def get_variable_summary(self, var_def: VariableDef) -> str:
        ty = self._shorten_type(var_def.type_annotation)
        annotation = f"{var_def.name} {ty}"
        assert var_def.modifiers == ""
        if var_def.rhs:
            if len(var_def.rhs) <= 6:
                annotation += f" = {var_def.rhs}"
            else:
                annotation += " = ..."
        return annotation


class _RustVariableSupport(_VariableSupport):
    @override
    def find_variables_impl(self, pfile: ParsedFile) -> Iterable[VariableDef]:
        bmap = pfile.bmap
        max_depth = self.max_rec_depth

        def recursion(node: ts.Node, depth: int) -> Iterable[VariableDef]:
            # e.g., `const x: i32 = 42`
            if node.type in ("const_item", "static_item"):
                assign_range = tsnode_to_crange(node, bmap)
                if name := node.child_by_field_name("name"):
                    modifiers = ""
                    if node.type == "static_item":
                        modifiers = "static "
                    if node.type == "const_item":
                        modifiers = "const "
                    yield VariableDef(
                        _node_text(name),
                        assign_range,
                        name_crange=tsnode_to_crange(name, bmap),
                        type_annotation=_node_text(node.child_by_field_name("type")),
                        rhs=_node_text(node.child_by_field_name("value")),
                        modifiers=modifiers,
                    )
            # e.g., the `x: i32` part in `struct Foo { x: i32 }`
            elif node.type == "field_declaration_list":
                for field in node.children:
                    if field.type != "field_declaration":
                        continue
                    if name := field.child_by_field_name("name"):
                        yield VariableDef(
                            _node_text(name),
                            crange=tsnode_to_crange(field, bmap),
                            name_crange=tsnode_to_crange(name, bmap),
                            type_annotation=_node_text(
                                field.child_by_field_name("type")
                            ),
                            rhs="",
                            # Visiblity modifiers are unnamed siblings of the name.
                            modifiers=" ".join(
                                [
                                    decode_bytes(n.text)
                                    for n in field.children
                                    if n.type == "visibility_modifier"
                                ]
                            ),
                        )
            # e.g., the `i32, i32` part in `struct Foo(i32, i32))`
            elif node.type == "ordered_field_declaration_list":
                # The types that we want are the `type` children of `node`.
                # Unfortunately, they are intermixed as siblings with visiblity modifiers as well as comments.
                # So we extract just the types but then walk all children to look for visibility modifiers.
                types = node.children_by_field_name("type")
                index = 0
                modifiers = ""
                for child in node.named_children:
                    if child.type == "visibility_modifier":
                        modifiers = _node_text(child)
                    elif index < len(types) and child == types[index]:
                        yield VariableDef(
                            # Tuple struct fields have types but no names and are accessed by their index.
                            str(index),
                            crange=tsnode_to_crange(child, bmap),
                            name_crange=tsnode_to_crange(child, bmap),
                            type_annotation=_node_text(child),
                            rhs="",
                            modifiers=modifiers,
                        )
                        index += 1
                        modifiers = ""
            elif node.type in ("function_item"):
                # do not recurse since we only report global variables
                return
            if depth > max_depth:
                return
            for child in node.named_children:
                yield from recursion(child, depth + 1)

        return recursion(pfile.ts_tree.root_node, 0)

    def get_variable_summary(self, var_def: VariableDef) -> str:
        annotation = var_def.name
        if ty := self._shorten_type(var_def.type_annotation):
            annotation = f"{annotation}: {ty}"
        if var_def.modifiers:
            annotation = f"{var_def.modifiers} {annotation}"
        if var_def.rhs:
            if len(var_def.rhs) <= 6:
                annotation += f" = {var_def.rhs}"
            else:
                annotation += " = ..."
        return annotation


AllVariableSupports: dict[LanguageID, _VariableSupport] = {
    "python": _PythonVariableSupport(),
    "java": _JavaVariableSupport(),
    "typescript": _TypeScriptVariableSupport(),
    "javascript": _TypeScriptVariableSupport(),
    "go": _GoVariableSupport(),
    "rust": _RustVariableSupport(),
}
