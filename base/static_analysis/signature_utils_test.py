"""Tests for signature utilities."""

from __future__ import annotations

from pathlib import Path
from textwrap import dedent

from base.static_analysis.common import assert_str_eq, guess_lang_from_fp
from base.static_analysis.signature_utils import FileSignatureInfo, SignaturePrinter
from base.static_analysis.usage_analysis import FileSummary, ParsedFile

# flake8: noqa

# leading whitespaces below are intentional to test that we correctly handle
# byte offsets
example_python_code = '''

@dataclass
class SignaturePrinter:
    """Used to turn codebase symbols into signature strings."""

    max_docstr_chars: int = 60
    """The max number of characters to show for each docstring."""

    def __post_init__(self):
        self.added_attr = 123

    def get_signature_info(
        self, summary: FileSummary, pfile: ParsedFile
    ) -> FileSignatureInfo:
        # not a doc string
        file_sig = self._get_module_signature(summary)
        signatures = {
            symb: sig
            for symb in summary.definitions
            if (sig := self._get_symbol_signature(symb, pfile)) is not None
        }
        return FileSignatureInfo(signatures, file_sig)

    class Inner:
        inner_var: str
        __private_var: str

    def _no_return_method(self, x: int):
        x *= 2
        x += 1

GlobalPrinter = SignaturePrinter()
__private_global_var = "private"

def some_global_function(x: str) -> str:
    """A global function."""
    x += "A"
    x += "B"
    return sig_code.strip()

def __private_function():
    pass
class __private_class:
    pass

global1, global2 = 1, 2
'''


def test_python_signatures():
    """Test the formatting of Python signatures."""
    # set large limits to avoid truncation
    sig_name_to_text, sig_info = _get_sig_name_to_text(example_python_code, "foo.py")
    sig_name_to_full_signatures, _ = _get_sig_name_to_text(
        example_python_code, "foo.py", show_full_method_signatures=True
    )
    sig_name_to_text_private, sig_info_private = _get_sig_name_to_text(
        example_python_code, "foo.py", show_private=True
    )
    assert set(sig_name_to_text) == {
        "SignaturePrinter",
        "GlobalPrinter",
        "__private_global_var",
        ".Inner",
        ".inner_var",
        ".__private_var",
        "some_global_function",
        ".__post_init__",
        ".max_docstr_chars",
        ".added_attr",
        ".get_signature_info",
        "._no_return_method",
        "__private_function",
        "__private_class",
        "global1",
        "global2",
    }

    # class signature lists all attributes and methods
    assert_str_eq(
        sig_name_to_text["SignaturePrinter"],
        dedent(
            '''\
            from: foo.py
            @dataclass
            class SignaturePrinter:
                """Used to turn codebase symbols into signature strings."""
            attributes: max_docstr_chars: int = 60; added_attr = 123
            methods: get_signature_info; _no_return_method
            innerclasses: Inner'''
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures["SignaturePrinter"],
        dedent(
            '''\
            from: foo.py
            @dataclass
            class SignaturePrinter:
                """Used to turn codebase symbols into signature strings."""
            attributes: max_docstr_chars: int = 60; added_attr = 123
            innerclasses: Inner
                def get_signature_info(
                    self, summary: FileSummary, pfile: ParsedFile
                ) -> FileSignatureInfo: ...

                def _no_return_method(self, x: int): ...'''
        ),
    )
    assert_str_eq(
        sig_name_to_text_private["SignaturePrinter"],
        dedent(
            '''\
            from: foo.py
            @dataclass
            class SignaturePrinter:
                """Used to turn codebase symbols into signature strings."""
            attributes: max_docstr_chars: int = 60; added_attr = 123
            methods: __post_init__; get_signature_info; _no_return_method
            innerclasses: Inner'''
        ),
    )

    # global function signature is not indented and has the last line if it's a return
    assert_str_eq(
        sig_name_to_text["some_global_function"],
        dedent(
            '''\
            from: foo.py
            def some_global_function(x: str) -> str:
                """A global function."""
                return sig_code.strip()'''
        ),
    )

    # full signature for global function is the same
    assert_str_eq(
        sig_name_to_text["some_global_function"],
        sig_name_to_full_signatures["some_global_function"],
    )

    # method signature is indented and has class name in its path
    assert_str_eq(
        sig_name_to_text[".get_signature_info"],
        dedent(
            """\
            from: foo.py/SignaturePrinter
                def get_signature_info(
                    self, summary: FileSummary, pfile: ParsedFile
                ) -> FileSignatureInfo:
                    return FileSignatureInfo(signatures, file_sig)"""
        ),
    )

    # full signature for method points to the parent class's signature
    assert_str_eq(
        sig_name_to_full_signatures[".get_signature_info"],
        sig_name_to_full_signatures["SignaturePrinter"],
    )

    # no return statement in the method signature
    assert_str_eq(
        sig_name_to_text["._no_return_method"],
        dedent(
            """\
            from: foo.py/SignaturePrinter
                def _no_return_method(self, x: int):"""
        ),
    )

    # global variable signature shows the defining statements
    assert_str_eq(
        sig_name_to_text["GlobalPrinter"],
        dedent(
            """\
            from: foo.py
            GlobalPrinter = SignaturePrinter()"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["GlobalPrinter"],
        sig_name_to_full_signatures["GlobalPrinter"],
    )

    # class attribute signature shows the defining statements
    assert_str_eq(
        sig_name_to_text[".max_docstr_chars"],
        dedent(
            """\
            from: foo.py/SignaturePrinter
            max_docstr_chars: int = 60"""
        ),
    )

    assert_str_eq(
        sig_name_to_full_signatures[".max_docstr_chars"],
        sig_name_to_full_signatures["SignaturePrinter"],
    )

    assert_str_eq(
        sig_name_to_text[".added_attr"],
        dedent(
            """\
            from: foo.py/SignaturePrinter
            self.added_attr = 123"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["global1"],
        dedent(
            """\
            from: foo.py
            global1, global2 = 1, 2"""
        ),
    )
    assert_str_eq(
        sig_name_to_text["global2"],
        dedent(
            """\
            from: foo.py
            global1, global2 = 1, 2"""
        ),
    )

    # module signature lists all classes and functions
    expected_module_sig = dedent(
        """\
        In file: foo.py
        CLASS: SignaturePrinter
        FUNCTION: some_global_function
        VARIABLE: GlobalPrinter, global1, global2"""
    )
    assert_str_eq(sig_info.module_signature.text, expected_module_sig)
    assert_str_eq(
        sig_info_private.module_signature.text,
        dedent(
            """\
        In file: foo.py
        CLASS: SignaturePrinter, __private_class
        FUNCTION: some_global_function, __private_function
        VARIABLE: GlobalPrinter, __private_global_var, global1, global2"""
        ),
    )


python_big_type_example = """

class A:
    big_type_var: BigType1111111111111111111111111111111111111111111111 = 1

"""


def test_big_type_annotation():
    """Test that oversized type annotations will be shortened."""
    sig_name_to_text, _ = _get_sig_name_to_text(python_big_type_example, "foo.py")
    assert_str_eq(
        sig_name_to_text["A"],
        dedent(
            """\
            from: foo.py
            class A:
            attributes: big_type_var: BigType111111111111111111111111111111111[...] = 1"""
        ),
    )


python_big_rhs_example = """
class A:
    big_rhs_var: int = 123456789
"""


def test_big_rhs():
    """Test that oversized RHS will be omitted."""
    sig_name_to_text, _ = _get_sig_name_to_text(python_big_rhs_example, "foo.py")
    sig_name_to_text_hide_equals_ellipsis, _ = _get_sig_name_to_text(
        python_big_rhs_example, "foo.py", hide_equals_ellipsis=True
    )
    assert_str_eq(
        sig_name_to_text["A"],
        dedent(
            """\
            from: foo.py
            class A:
            attributes: big_rhs_var: int = ..."""
        ),
    )
    assert_str_eq(
        sig_name_to_text_hide_equals_ellipsis["A"],
        dedent(
            """\
            from: foo.py
            class A:
            attributes: big_rhs_var: int"""
        ),
    )


python_python_constructor_example = """
class A:
    field: int
    def __init__(self):
        self.field = 123456789
    def method(self):
        pass
"""


def test_python_constructor():
    """Test that oversized RHS will be omitted."""
    sig_name_to_text, _ = _get_sig_name_to_text(
        python_python_constructor_example, "foo.py"
    )
    sig_name_to_text_with_full_signatures, _ = _get_sig_name_to_text(
        python_python_constructor_example, "foo.py", show_full_method_signatures=True
    )
    assert_str_eq(
        sig_name_to_text["A"],
        dedent(
            """\
            from: foo.py
            class A:
            attributes: field: int
            methods: method
                def __init__(self): ..."""
        ),
    )
    assert_str_eq(
        sig_name_to_text_with_full_signatures["A"],
        dedent(
            """\
            from: foo.py
            class A:
            attributes: field: int
                def __init__(self): ...

                def method(self): ..."""
        ),
    )


python_python_path_example = """
class A:
    field: int
    def method(self):
        pass
"""


def test_python_path():
    """Test full paths are displayed."""
    sig_name_to_text, sig_info = _get_sig_name_to_text(
        python_python_path_example, "dir/file.py"
    )

    assert_str_eq(
        sig_info.module_signature.text,
        dedent(
            """\
            In file: dir/file.py
            CLASS: A"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["A"],
        dedent(
            """\
            from: dir/file.py
            class A:
            attributes: field: int
            methods: method"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".field"],
        dedent(
            """\
            from: dir/file.py/A
            field: int"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".method"],
        dedent(
            """\
            from: dir/file.py/A
                def method(self):"""
        ),
    )


python_python_private_example = """
class A:
    public_field: int
    __private_field: int
    def public_method(self):
        pass
    def __private_method(self):
        pass
"""


def test_python_private():
    """Test that private fields and variables are shown only when requested."""
    sig_name_to_text, _ = _get_sig_name_to_text(python_python_private_example, "foo.py")
    sig_name_to_text_with_private, _ = _get_sig_name_to_text(
        python_python_private_example, "foo.py", show_private=True
    )

    assert_str_eq(
        sig_name_to_text["A"],
        dedent(
            """\
            from: foo.py
            class A:
            attributes: public_field: int
            methods: public_method"""
        ),
    )
    assert_str_eq(
        sig_name_to_text_with_private["A"],
        dedent(
            """\
            from: foo.py
            class A:
            attributes: public_field: int; __private_field: int
            methods: public_method; __private_method"""
        ),
    )


example_java_code = """

import java.util.*;

public class Main {
    public static final long ROOT_PID = 0L; // root process id
    public static final long MAX_PID = (long) Math.pow(2, 32);
    private static int priv_var = 5;

    /* System doc string */
    static class System {
        public List<Record> Database = new ArrayList<>();
        Map<Long, List<Long>> Relationship = new HashMap<>();

        public System() {
            Database.add(new Record(ROOT_PID, 0, true));
            Relationship.put(ROOT_PID, new ArrayList<>());
        }

        public System(boolean b) {
            Database.add(new Record(ROOT_PID, 0, b));
            Relationship.put(ROOT_PID, new ArrayList<>());
        }

        public List<Long> getChildren(long id) {
            return Relationship.get(id);
        }

        private void privateInnerMethod() {}

        static class Inner {
            int field1;
            int f1a, f1b;
            void method1() {}
        }

        private static class PrivateInner {}
    }

    static class Inner {
        int field2;
        void method2() {}
    }

    public enum Enum {
        E1, E2(42), E3
    }
} # trailing comment
"""


def test_java_signatures():
    """Test the formatting of Java signatures."""
    # set large limits to avoid truncation
    sig_name_to_text, sig_info = _get_sig_name_to_text(example_java_code, "Main.java")
    sig_name_to_text_with_full_signatures, _ = _get_sig_name_to_text(
        example_java_code, "Main.java", show_full_method_signatures=True
    )
    sig_name_to_text_with_private, _ = _get_sig_name_to_text(
        example_java_code, "Main.java", show_private=True
    )
    sig_name_to_text_with_always_private, _ = _get_sig_name_to_text(
        example_java_code, "Main.java", always_show_private=True
    )

    assert set(sig_name_to_text) == {
        "Main",
        ".ROOT_PID",
        ".MAX_PID",
        ".priv_var",
        ".System",
        ".Database",
        ".Inner",
        ".Relationship",
        ".field1",
        ".f1a",
        ".f1b",
        ".field2",
        ".getChildren",
        ".privateInnerMethod",
        ".PrivateInner",
        ".method1",
        ".method2",
        ".Enum",
        ".E1",
        ".E2",
        ".E3",
    }

    assert_str_eq(
        sig_name_to_text["Main"],
        dedent(
            """\
            from: Main.java
            public class Main {
            attributes: static final long ROOT_PID = 0L; static final long MAX_PID = ...
            innerclasses: System; Inner; Enum
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".ROOT_PID"],
        dedent(
            """\
            from: Main.java/Main
            public static final long ROOT_PID = 0L;"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".MAX_PID"],
        dedent(
            """\
            from: Main.java/Main
            public static final long MAX_PID = (long) Math.pow(2, 32);"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".System"],
        dedent(
            """\
            from: Main.java/Main
                /* System doc string */
                static class System {
            attributes: List<Record> Database = ...; Map<Long, List<Long>> Relationship = ...
            methods: getChildren
            innerclasses: Inner
                    public System() {}

                    public System(boolean b) {}
                }"""
        ),
    )
    assert_str_eq(
        sig_name_to_text_with_full_signatures[".System"],
        dedent(
            """\
            from: Main.java/Main
                /* System doc string */
                static class System {
            attributes: List<Record> Database = ...; Map<Long, List<Long>> Relationship = ...
            innerclasses: Inner
                    public System() {}

                    public System(boolean b) {}

                    public List<Long> getChildren(long id) {}
                }"""
        ),
    )
    assert_str_eq(
        sig_name_to_text_with_private[".System"],
        dedent(
            """\
            from: Main.java/Main
                /* System doc string */
                static class System {
            attributes: List<Record> Database = ...; Map<Long, List<Long>> Relationship = ...
            methods: getChildren; privateInnerMethod
            innerclasses: Inner; PrivateInner
                    public System() {}

                    public System(boolean b) {}
                }"""
        ),
    )
    assert_str_eq(
        sig_name_to_text_with_always_private[".System"],
        sig_name_to_text_with_private[".System"],
    )

    assert_str_eq(
        sig_name_to_text[".Database"],
        dedent(
            """\
            from: Main.java/Main.System
            public List<Record> Database = new ArrayList<>();"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".getChildren"],
        dedent(
            """\
            from: Main.java/Main.System
                    public List<Long> getChildren(long id) {
                        return Relationship.get(id);
                    }"""
        ),
    )

    # We should see only the first Inner class, not a combination of the two.
    assert_str_eq(
        sig_name_to_text[".Inner"],
        dedent(
            """\
            from: Main.java/Main.System
                    static class Inner {
            attributes: int field1; int f1a; int f1b
            methods: method1
                    }"""
        ),
    )
    assert_str_eq(
        sig_name_to_text_with_full_signatures[".Inner"],
        dedent(
            """\
            from: Main.java/Main.System
                    static class Inner {
            attributes: int field1; int f1a; int f1b
                        void method1() {}
                    }"""
        ),
    )

    # FIXME: this module signature is not very informative
    # For java we might need to use all classes from the package
    assert_str_eq(
        sig_info.module_signature.text,
        dedent(
            """\
            In file: Main.java
            CLASS: Main"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".Enum"],
        dedent(
            """\
            from: Main.java/Main
                public enum Enum {
            attributes: E1; E2; E3
                }"""
        ),
    )


example_typescript_code = """\


let global1 = 1.2;

/* class doc string */
export class Usage {
    public color: string;
    static PI: number = 3.14;
    private priv_var: number = 5;
    public use(x: A) {
        A.PI = global1;
        x.color = (x.year + x.age).toString();
        return 1;
    }

    private foo() {}

    constructor(color: string) {
        this.color = color;
    }
}

/* function doc string */
function main(bar) {
    bar.df(5);
}

export namespace n1 {
    export class Usage {
        public inner_field: number;
        public inner_method() {}
    }
}

namespace n2 {
    export class Usage {
        public inner_field: number;
        public inner_method() {}
    }
}

export enum ImAnEnum {
    VariantA = 1,
    VariantB,
    VariantC,
}

export type ImAType = {
    typeField: number;
};

type TypeAlias = number;

export interface ImAnInterface {
    interfaceField: number;
}
"""


def test_typescript_signatures():
    """Test the formatting of TypeScript signatures."""

    sig_name_to_text, sig_info = _get_sig_name_to_text(
        example_typescript_code, "example.ts"
    )
    sig_name_to_full_signatures, _ = _get_sig_name_to_text(
        example_typescript_code, "example.ts", show_full_method_signatures=True
    )
    assert set(sig_name_to_text) == {
        "global1",
        "Usage",
        ".color",
        ".PI",
        ".priv_var",
        ".use",
        ".foo",
        ".constructor",
        "main",
        "n1",
        "n2",
        ".Usage",
        ".inner_field",
        ".inner_method",
        "ImAnEnum",
        ".VariantA",
        ".VariantB",
        ".VariantC",
        "ImAType",
        ".typeField",
        "TypeAlias",
        "ImAnInterface",
        ".interfaceField",
    }

    assert_str_eq(
        sig_info.module_signature.text,
        dedent(
            """\
            In file: example.ts
            CLASS: Usage, n1, n2, ImAnEnum, ImAType, TypeAlias, ImAnInterface
            FUNCTION: main
            VARIABLE: global1"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["global1"],
        dedent(
            """\
            from: example.ts
            let global1 = 1.2;"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Usage"],
        dedent(
            """\
            from: example.ts
            /* class doc string */
            export class Usage {
            attributes: color: string; static PI: number = 3.14
            methods: use
                constructor(color: string) {}
            }"""
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures["Usage"],
        dedent(
            """\
            from: example.ts
            /* class doc string */
            export class Usage {
            attributes: color: string; static PI: number = 3.14
                constructor(color: string) {}

                public use(x: A) {}
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".Usage"],
        dedent(
            """\
            from: example.ts/n1
                export class Usage {
            attributes: inner_field: number
            methods: inner_method
                }"""
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures[".Usage"],
        dedent(
            """\
            from: example.ts/n1
                export class Usage {
            attributes: inner_field: number
                    public inner_method() {}
                }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".color"],
        dedent(
            """\
            from: example.ts/Usage
            public color: string"""
        ),
    )

    # in the full signature case, members share the same signature as the class
    assert_str_eq(
        sig_name_to_full_signatures[".color"],
        sig_name_to_full_signatures["Usage"],
    )

    assert_str_eq(
        sig_name_to_text[".use"],
        dedent(
            """\
            from: example.ts/Usage
                public use(x: A) {
                    return 1;
                }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["main"],
        dedent(
            """\
            from: example.ts
            /* function doc string */
            function main(bar) {
            }"""
        ),
    )
    assert_str_eq(
        sig_name_to_text["ImAnEnum"],
        dedent(
            """\
            from: example.ts
            export enum ImAnEnum {
            attributes: VariantA = 1; VariantB; VariantC
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["ImAType"],
        dedent(
            """\
            from: example.ts
            export type ImAType = {
            attributes: typeField: number
            };"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["TypeAlias"],
        dedent(
            """\
            from: example.ts
            type TypeAlias =
            ;"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["ImAnInterface"],
        dedent(
            """\
            from: example.ts
            export interface ImAnInterface {
            attributes: interfaceField: number
            }"""
        ),
    )


example_go_code = """\


package main

import "fmt"

/* main doc string */
func main() {
    return 1
}

type Usage struct {
    color string
    year, age int
    PI float
}

var Global1, global2 int = 1

func (u Usage) Use(x A) {
    x.PI = global1;
    u.color = (x.year + x.age).toString();
    inner = 2;  # this should not trigger usage
}

func (u Usage) private() {}

type Interface interface {
    Use(x A)
}

const (
    A = 1
    B
    C
)
"""


def test_go_signatures():
    """Test the formatting of Go signatures."""

    sig_name_to_text, sig_info = _get_sig_name_to_text(example_go_code, "example.go")
    sig_name_to_full_signatures, _ = _get_sig_name_to_text(
        example_go_code, "example.go", show_full_method_signatures=True
    )
    sig_name_to_text_private, sig_info_private = _get_sig_name_to_text(
        example_go_code, "example.go", show_private=True
    )
    assert set(sig_name_to_text) == {
        "main",
        "Usage",
        ".color",
        ".year",
        ".age",
        ".PI",
        "Global1",
        "global2",
        ".Use",
        ".private",
        "Interface",
        "A",
        "B",
        "C",
    }

    assert_str_eq(
        sig_info.module_signature.text,
        dedent(
            """\
            In file: example.go
            CLASS: Usage, Interface
            VARIABLE: Global1, A, B, C"""
        ),
    )
    assert_str_eq(
        sig_info_private.module_signature.text,
        dedent(
            """\
            In file: example.go
            CLASS: Usage, Interface
            FUNCTION: main
            VARIABLE: Global1, global2, A, B, C"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["main"],
        dedent(
            """\
            from: example.go
            /* main doc string */
            func main() {
                return 1
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".Use"],
        dedent(
            """\
            from: example.go/Usage
            func (u Usage) Use(x A) {
            }"""
        ),
    )

    # in the full signature case, members share the same signature as the class
    assert_str_eq(
        sig_name_to_full_signatures[".Use"],
        sig_name_to_full_signatures["Usage"],
    )

    assert_str_eq(
        sig_name_to_text["Usage"],
        dedent(
            """\
            from: example.go
            type Usage struct {
            attributes: PI float
            methods: Use
            }"""
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures["Usage"],
        dedent(
            """\
            from: example.go
            type Usage struct {
            attributes: PI float
            func (u Usage) Use(x A) {}
            }"""
        ),
    )
    assert_str_eq(
        sig_name_to_text_private["Usage"],
        dedent(
            """\
            from: example.go
            type Usage struct {
            attributes: color string; year int; age int; PI float
            methods: Use; private
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Interface"],
        dedent(
            """\
            from: example.go
            type Interface interface {
            methods: Use
            }"""
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures["Interface"],
        dedent(
            """\
            from: example.go
            type Interface interface {
                Use(x A)
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".color"],
        dedent(
            """\
            from: example.go/Usage
            color string"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Global1"],
        dedent(
            """\
            from: example.go
            Global1, global2 int = 1"""
        ),
    )


example_rust_code = """\

// main doc string
fn main() -> i32 {
    return 1
}

struct Usage {
    color: String,
    year: i32,
    age: i32,
    pub PI: f32,
}

static Global1: i32 = 1;
static mut global2: i32 = 1;

impl Usage {
    fn use(x: A) {
        x.PI = global1;
        u.color = (x.year + x.age).to_string();
        inner = 2;  # this should not trigger usage
    }
}

mod Inner {
    struct Usage {
        inner_usage_field: i32,
    }
    impl Usage {
        fn inner_usage_method() {}
    }
}

struct Pair(
    pub i32,  # comment
    i32
);

enum Color { pub Red, Green, Blue }

struct Unit;

enum Complex {
    VariantUnit,
    VariantPair(i32, i32),
    VariantStruct { E1: i32, E2: i32, E3: i32 },
}

trait Shape {
    fn area(&self) -> f32;
}
"""


def test_rust_signatures():
    """Test the formatting of Rust signatures."""

    sig_name_to_text, sig_info = _get_sig_name_to_text(
        example_rust_code, "example.rs", unary_symbols={"Usage"}
    )
    sig_name_to_full_signatures, _ = _get_sig_name_to_text(
        example_rust_code,
        "example.rs",
        unary_symbols={"Usage"},
        show_full_method_signatures=True,
    )
    assert set(sig_name_to_text) == {
        "main",
        "Usage",
        ".color",
        ".year",
        ".age",
        ".PI",
        "Global1",
        "global2",
        ".use",
        "Inner",
        ".Usage",
        ".inner_usage_field",
        ".inner_usage_method",
        "Pair",
        ".0",
        ".1",
        "Color",
        ".Red",
        ".Green",
        ".Blue",
        "Unit",
        "Complex",
        ".VariantUnit",
        ".VariantPair",
        ".VariantStruct",
        ".E1",
        ".E2",
        ".E3",
        "Shape",
        ".area",
    }

    assert_str_eq(
        sig_info.module_signature.text,
        dedent(
            """\
            In file: example.rs
            CLASS: Usage, Inner, Pair, Color, Unit, Complex, Shape
            FUNCTION: main
            VARIABLE: Global1, global2"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["main"],
        dedent(
            """\
            from: example.rs
            // main doc string
            fn main() -> i32 {
                return 1
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".use"],
        dedent(
            """\
            from: example.rs/Usage
                fn use(x: A) {
                }"""
        ),
    )

    # in the full signature case, members share the same signature as the class
    assert_str_eq(
        sig_name_to_full_signatures[".use"],
        sig_name_to_full_signatures["Usage"],
    )

    assert_str_eq(
        sig_name_to_text["Usage"],
        dedent(
            """\
            from: example.rs
            struct Usage {
            attributes: color: String; year: i32; age: i32; pub PI: f32
            methods: use
            }"""
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures["Usage"],
        dedent(
            """\
            from: example.rs
            struct Usage {
            attributes: color: String; year: i32; age: i32; pub PI: f32
                fn use(x: A) {}
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Inner"],
        dedent(
            """\
            from: example.rs
            mod Inner {
            innerclasses: Usage
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".Usage"],
        dedent(
            """\
            from: example.rs/Inner
                struct Usage {
            attributes: inner_usage_field: i32
            methods: inner_usage_method
                }"""
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures[".Usage"],
        dedent(
            """\
            from: example.rs/Inner
                struct Usage {
            attributes: inner_usage_field: i32
                    fn inner_usage_method() {}
                }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".color"],
        dedent(
            """\
            from: example.rs/Usage
            color: String"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Global1"],
        dedent(
            """\
            from: example.rs
            static Global1: i32 = 1;"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Pair"],
        dedent(
            """\
            from: example.rs
            struct Pair(
            attributes: pub 0: i32; 1: i32
            );"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Color"],
        dedent(
            """\
            from: example.rs
            enum Color {
            innerclasses: Red; Green; Blue
             }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Unit"],
        dedent(
            """\
            from: example.rs
            struct Unit;"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Complex"],
        dedent(
            """\
            from: example.rs
            enum Complex {
            innerclasses: VariantUnit; VariantPair; VariantStruct
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".VariantUnit"],
        dedent(
            """\
            from: example.rs/Complex
                VariantUnit"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".VariantPair"],
        dedent(
            """\
            from: example.rs/Complex
                VariantPair(
            attributes: 0: i32; 1: i32
            )"""
        ),
    )

    assert_str_eq(
        sig_name_to_text[".VariantStruct"],
        dedent(
            """\
            from: example.rs/Complex
                VariantStruct {
            attributes: E1: i32; E2: i32; E3: i32
             }"""
        ),
    )

    assert_str_eq(
        sig_name_to_text["Shape"],
        dedent(
            """\
            from: example.rs
            trait Shape {
            methods: area
            }"""
        ),
    )
    assert_str_eq(
        sig_name_to_full_signatures["Shape"],
        dedent(
            """\
            from: example.rs
            trait Shape {
                fn area(&self) -> f32;
            }"""
        ),
    )

    assert_str_eq(
        sig_name_to_full_signatures[".area"],
        sig_name_to_full_signatures["Shape"],
    )


def _get_sig_name_to_text(
    code: str,
    file_name: Path | str,
    show_full_method_signatures: bool = False,
    show_private: bool = False,
    always_show_private: bool = False,
    hide_equals_ellipsis: bool = False,
    unary_symbols: set[str] = set(),
) -> tuple[dict[str, str], FileSignatureInfo]:
    """Get a map from signature name to signature text, along with signature_info.

    When a name appears multiple times, only the first occurrence is kept.

    An assertion is thrown if any symbol in `unary_symbols` appears multiple times.
    """
    printer = SignaturePrinter(
        max_docstr_chars=500,
        max_attributes_chars=1000,
        show_full_method_signatures=show_full_method_signatures,
        hide_equals_ellipsis=hide_equals_ellipsis,
        always_show_private=always_show_private,
    )
    if isinstance(file_name, str):
        file_name = Path(file_name)
    lang = guess_lang_from_fp(file_name)
    assert lang is not None
    pfile = ParsedFile.parse(file_name, lang, code)
    summary = FileSummary.from_pfile(pfile)
    sig_info = printer.get_signature_info(summary, pfile, show_private)
    id_to_def = {d.id: d for d in summary.definitions}
    sig_name_to_text = dict[str, str]()
    for symb_id, sig in sig_info.symbol_signatures.items():
        symb_name = id_to_def[symb_id].name
        # keep the first element of each name
        if symb_name not in sig_name_to_text:
            sig_name_to_text[symb_name] = sig.text
        elif symb_name in unary_symbols:
            raise AssertionError(
                f"Should not have multiple definitions for {symb_name}."
            )

    return sig_name_to_text, sig_info
