"""Implements SignatureIndex, used by signature retrieval."""

from __future__ import annotations

import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Collection, Literal, Mapping, Protocol, Sequence, TypeVar

from intervaltree import IntervalTree
from typing_extensions import assert_never, override

from base.ranges import Char<PERSON>ange
from base.static_analysis.common import LanguageID, groupby, shorten_str
from base.static_analysis.parsing import FileTypeNotSupportedError
from base.static_analysis.signature_utils import (
    FileSignatureInfo,
    SignaturePrinter,
    SymbolSignature,
)
from base.static_analysis.usage_analysis import (
    BasicUsageReranker,
    FileSummary,
    LocalUsageAnalysis,
    ModulePath,
    ParsedFile,
    ScopeTreeStructure,
    SymbolDefinition,
    SymbolDefinitionId,
    SymbolName,
    SymbolNameUsage,
    UsageIndex,
)

UsageToSignaturesMap = Mapping[SymbolNameUsage, Sequence[SymbolSignature]]
"""Maps each name usage to a sequence of signatures."""


@dataclass
class FileSignatureUsages:
    """Holds signature usages of context and the middle, as well as the selector."""

    ctx_signatures: UsageToSignaturesMap
    """The signature usages of the context."""

    middle_signatures: UsageToSignaturesMap
    """The signature usages of the middle."""


@dataclass
class ContextSignatureResult:
    """Represents the result of a context signature query."""

    ctx_signatures: UsageToSignaturesMap
    """A dictionary of usages of the context."""

    state: SignatureQueryState
    """The query state for subsequent inline queries."""

    metrics: SignatureQueryMetrics
    """Metrics for the operation."""


@dataclass
class SignatureQueryState:
    """Represents the state needed for follow-up inline queries in the same context."""

    file_path: Path
    """Path of the file being queried."""
    lang: LanguageID
    """Language id used for the current file."""
    est_prompt_range: CharRange
    """Estimated range of the prompt."""
    reranker: BasicUsageReranker
    """Reranking statistics."""


@dataclass
class SignatureQueryMetrics:
    """Metrics for a signature query."""

    skipped_usages: Sequence[tuple[str, Sequence[str]]]
    """An ordered list of (name, signatures) tuples for each skipped usage."""

    usage_distances: Sequence[tuple[str, Sequence[float]]]
    """A list of (name, distance) tuples for each usage, from closest to farthest."""

    @staticmethod
    def empty() -> SignatureQueryMetrics:
        """Return an empty metrics object."""
        return SignatureQueryMetrics([], [])

    def __str__(self):
        lines = [
            ("==" * 10 + "Signature query metrics" + "==" * 10),
            "Usage distances:",
            "".join(f"\n  {name}: {dist}" for name, dist in self.usage_distances),
            "Skipped usages:",
            "".join(
                f"\n  {name} ({len(sigs)} signatures):"
                f" {[shorten_str(s, max_len=100, omit_mode='right') for s in sigs]}"
                for name, sigs in self.skipped_usages
            ),
        ]
        return "\n".join(lines)


@dataclass
class FileSummaryWithSignatures:
    """Holds the local summary of a file, along with its signatures."""

    summary: FileSummary
    signature_info: FileSignatureInfo

    @property
    def path(self) -> Path:
        """The path of the file."""
        return self.summary.path

    @property
    def lang(self) -> LanguageID:
        """The language id of the file."""
        return self.summary.lang

    @staticmethod
    def from_pfile(
        pfile: ParsedFile, sig_printer: SignaturePrinter, show_private: bool = False
    ):
        """Construct a FileSummaryWithSignatures from a ParsedFile."""
        summary = FileSummary.from_pfile(pfile)
        sig_info = sig_printer.get_signature_info(summary, pfile, show_private)
        return FileSummaryWithSignatures(summary, sig_info)


QueryStateT = TypeVar("QueryStateT")
"""Represents the state needed to resolve a future queries in a session."""


class SignatureIndexProtocol(Protocol[QueryStateT]):
    """Interface for definition indexes."""

    def add_file(self, summary: FileSummaryWithSignatures):
        """Add the definitions and usages to the index.

        Raises:
            KeyError: If `summary.path` already exists in the index.
        """
        raise NotImplementedError()

    def remove_file(self, path: Path) -> bool:
        """Remove any definitions from `path` in the index.

        Returns:
            True if the file was removed, False otherwise.
        """
        raise NotImplementedError()

    def size_files(self):
        """Return the size of the index in number of files."""
        raise NotImplementedError()

    def get_context_signatures(
        self, summary: FileSummary, cursor_location: int, prompt_range: CharRange
    ) -> ContextSignatureResult:
        """Get the signature usages around the cursor.

        Args:
            summary: the file summary of the current file.
            cursor_location: the character location of the cursor. Usages closest to\
                the cursor are returned first.
            prompt_range: the range of the prompt, should contain cursor_location.\
                Any signature fully contained in this range will be filtered.

        Returns:
            A dataclass containing the signatures, the query state for subsequent\
                inline queries, and metrics for the query.
        """
        raise NotImplementedError()

    def inline_signature_query(
        self, symbol_name: str, state: QueryStateT
    ) -> tuple[Sequence[SymbolSignature], QueryStateT]:
        """Query for definitions of the given symbol.

        Args:
            symbol_name: A single symbol to query.
            state: If provided, the state to use for the query, typically from
                previous queries to the index.

        Returns:
            A list of definitions for the given symbol, and an updated context.
        """
        raise NotImplementedError()


UsageDistanceMetricName = Literal["plain", "var_occurrence", "tree"]
"""Metrics used to measure the distance between a use site and the cursor.

- plain: simply count the number of characters between the usage and the cursor.
- var_occurrence: the distance between all occurrences of a variable are reduced,\
    meaning that if one occurrence is close to the cursor, all other occurrences will\
    be treated as if they are closer to the cursor as well.
- tree: count the distance between two nodes in the scope tree.
"""


@dataclass
class SignatureIndex(SignatureIndexProtocol[SignatureQueryState]):
    """An index used for signature retrieval."""

    use_ctx_signatures: bool = True
    """Whether to add the signatures of the context calls."""

    use_cursor_signatures: bool = True
    """Whether to add signatures that contain the cursor."""

    top_k_sigs: int = 3
    """Max number of signatures to show for each call site."""

    max_ctx_signature_chars: int = 6000
    """Max number of characters of all signatures in the context."""

    index_groups: Sequence[Collection[LanguageID]] = field(
        default_factory=lambda: (
            {"python"},
            {"typescript"},
            {"javascript"},
            {"java"},
            {"go"},
            {"rust"},
        )
    )
    """The groups of languages that can be indexed by this Index.

    Cross-language signature retrieval happens within each language group.
    """

    usage_distance_metric: UsageDistanceMetricName = "plain"
    """The metric used to measure the distance between a use site and the cursor."""

    verbose: bool = True
    """Whether to print out additional messages."""

    def __post_init__(self):
        self._usage_indexes = dict[LanguageID, UsageIndex]()
        for lang_group in self.index_groups:
            index = UsageIndex()
            for lang in lang_group:
                self._usage_indexes[lang] = index
        self._id_to_signature = dict[SymbolDefinitionId, SymbolSignature]()
        self._module_signature_map = dict[ModulePath, SymbolSignature]()
        self._file_signatures_map = dict[Path, FileSignatureInfo]()
        self._language_map = dict[Path, LanguageID]()

    def symbol_counts(self) -> dict[LanguageID, int]:
        """Return the number of symbols indexed (if > 0) for each language."""
        counts = {
            lang: count
            for lang, uindex in self._usage_indexes.items()
            if (count := len(uindex.symbol_map)) > 0
        }
        return dict(sorted(counts.items(), key=lambda x: x[1], reverse=True))

    def size_files(self):
        """Return the number of files indexed."""
        return len(self._language_map)

    @property
    def supported_langs(self) -> Collection[LanguageID]:
        """The languages that can be indexed by this Index."""
        return {lang for group in self.index_groups for lang in group}

    @override
    def get_context_signatures(
        self,
        summary: FileSummary,
        cursor_location: int,
        prompt_range: CharRange,
    ) -> ContextSignatureResult:
        # The two classes below are used to mix two types of usages together so that we
        # can sort usages by distance to the middle while still know the usage types.
        @dataclass
        class _DefUse:
            defs: Sequence[SymbolDefinition]

        @dataclass
        class _ModuleUse:
            modules: Sequence[ModulePath]

        if summary.lang not in self.supported_langs:
            raise FileTypeNotSupportedError(f"Unsupported lang: {summary.lang}")

        path = summary.path
        uindex = self._usage_indexes[summary.lang]
        usage_analysis = uindex.resolve_file_usages(summary, report_declarations=False)

        def def_filter(symb: SymbolDefinition) -> bool:
            """Filter out definitions that are completely in the prompt range."""
            return not (symb.path == path and prompt_range.contains(symb.full_crange))

        def_uses = [
            (usage, _DefUse(tuple(filter(def_filter, defs))))
            for usage, defs in usage_analysis.site2defs.items()
        ]
        module_uses = [
            (usage, _ModuleUse(module_names))
            for usage, module_names in usage_analysis.site2modules.items()
        ]

        if self.use_cursor_signatures:
            # add same-file signatures that contain the cursor location
            cursor_point = CharRange.point(cursor_location)
            containing_defs = [
                d
                for d in summary.definitions
                if d.full_crange.contains(cursor_point) and def_filter(d)
            ]
            containing_defs.sort(key=lambda d: len(d.full_crange))
            containing_defs = groupby(containing_defs, keyfunc=lambda d: d.name)
            containing_uses = [
                (SymbolNameUsage(name, cursor_point, "declare"), _DefUse(defs))
                for name, defs in containing_defs.items()
            ]
            # prepend containing definitions, from the smallest to the largest
            def_uses = containing_uses + def_uses

            if not prompt_range.contains(CharRange(0, summary.size_chars)):
                module_use_name = "@" + summary.path.with_suffix("").name
                module_uses.insert(
                    0,
                    (
                        SymbolNameUsage(module_use_name, cursor_point, "declare"),
                        _ModuleUse([summary.path]),
                    ),
                )

        all_usages = def_uses + module_uses
        usage_distances = dict[str, tuple[float, ...]]()

        if self.use_ctx_signatures:
            all_sites = {u for u, _ in all_usages}
            distances = self.compute_site_distances(
                all_sites, cursor_location, prompt_range, summary
            )

            # Python sorts preserving the original order of tied elements
            ctx_usages = sorted(
                all_usages,
                key=lambda x: distances[x[0]],
            )
            for u, _ in ctx_usages:
                if u.name not in usage_distances:
                    usage_distances[u.name] = distances[u]
        else:
            ctx_usages = []

        used_chars = 0  # number of characters used by context signatures
        names_seen = set[SymbolName]()
        signatures_seen = set[SymbolSignature]()
        ctx_sigs = dict[SymbolNameUsage, Sequence[SymbolSignature]]()
        skipped_usages = dict[str, Sequence[str]]()
        sig_separators = 2  # we separate signatures using 2 newlines

        for u, values in ctx_usages:
            if u.name in names_seen or u.name in skipped_usages:
                continue
            if isinstance(values, _DefUse):
                new_sigs = self._get_symb_top_signatures(values.defs)
            else:
                new_sigs = self._get_module_signatures(values.modules)
            # remove signatures that are already reported
            new_sigs = [sig for sig in new_sigs if sig not in signatures_seen]
            if not new_sigs:
                continue

            sigs_size = sum(len(sig.text) + sig_separators for sig in new_sigs)
            if used_chars + sigs_size > self.max_ctx_signature_chars:
                # If this signature is too long, skip it and try the next.
                # TODO: We might instead want to show a shortened version of it.
                skipped_usages[u.name] = [sig.text for sig in new_sigs]
                continue
            used_chars += sigs_size
            names_seen.add(u.name)
            ctx_sigs[u] = new_sigs
            signatures_seen.update(new_sigs)

        # check that all signatures in the context do not exceed the set budget
        used_chars = sum(
            len(sig.text) + sig_separators for sigs in ctx_sigs.values() for sig in sigs
        )
        assert (
            used_chars <= self.max_ctx_signature_chars
        ), f"{used_chars=}, {self.max_ctx_signature_chars=}"

        sig_query_state = SignatureQueryState(
            file_path=path,
            lang=summary.lang,
            est_prompt_range=prompt_range,
            reranker=usage_analysis.reranker,
        )

        metrics = SignatureQueryMetrics(
            skipped_usages=list(skipped_usages.items()),
            usage_distances=list(usage_distances.items()),
        )

        return ContextSignatureResult(ctx_sigs, sig_query_state, metrics)

    @override
    def inline_signature_query(
        self, symbol_name: SymbolName, state: SignatureQueryState
    ) -> tuple[Sequence[SymbolSignature], SignatureQueryState]:
        """Return top signatures for a symbol name query, along with a new state."""

        def def_filter(symb: SymbolDefinition) -> bool:
            """Do not include definitions that are already in the prompt."""
            return not (
                symb.path == state.file_path
                and state.est_prompt_range.contains(symb.full_crange)
            )

        uindex = self._usage_indexes[state.lang]
        is_module = symbol_name.startswith("@")  # module name starts with @
        if is_module:
            logging.warning("Inline lookup of module names not supported.")
            defs = []
        else:
            defs = uindex.resolve_def_name(symbol_name, state.reranker)
        state.reranker.record_def_bonus(defs)
        defs = tuple(filter(def_filter, defs))
        return self._get_symb_top_signatures(defs), state

    @override
    def add_file(
        self,
        summary: FileSummaryWithSignatures,
    ) -> bool:
        """Update the index with a new or modified file.

        Return whether the file was successfully added.
        """
        if summary.lang not in self.supported_langs:
            if summary.lang and self.verbose:
                logging.warning("Skip adding unsupported lang: %s", summary.lang)
            return False
        path = summary.path
        self._usage_indexes[summary.lang].update_file(summary.summary)
        self._file_signatures_map[summary.path] = summary.signature_info
        self._id_to_signature.update(summary.signature_info.symbol_signatures)
        self._module_signature_map[path] = summary.signature_info.module_signature
        self._language_map[path] = summary.lang
        return True

    @override
    def remove_file(self, path: Path) -> bool:
        """Remove the file with the given path from the index.

        Return whether the file was successfully removed.
        """
        if (lang := self._language_map.get(path)) is None:
            return False
        self._usage_indexes[lang].remove_file(path)
        if sig_info := self._file_signatures_map.pop(path, None):
            for symb_id in sig_info.symbol_signatures.keys():
                self._id_to_signature.pop(symb_id)
        del self._module_signature_map[path]
        del self._language_map[path]
        return True

    def remove_all(self) -> None:
        """Remove all files."""
        for path in list(self._language_map.keys()):
            self.remove_file(path)

    def update_file(
        self,
        file: FileSummaryWithSignatures,
    ) -> bool:
        """Update the index with a new or modified file.

        Return whether the file was successfully added.
        """
        self.remove_file(file.path)
        return self.add_file(file)

    def _get_symb_top_signatures(
        self, symbs: Sequence[SymbolDefinition]
    ) -> Sequence[SymbolSignature]:
        """Truncate the symbol list to top-k and remove duplicates.

        The list is assumed to be sorted by relevance, from high to low.
        """

        # we use the dict below to remove duplicates
        text_to_sig = dict[str, SymbolSignature]()
        for symb in symbs:
            if len(text_to_sig) >= self.top_k_sigs:
                break
            signature = self._id_to_signature.get(symb.id)
            if signature and signature.text not in text_to_sig:
                text_to_sig[signature.text] = signature
        return tuple(text_to_sig.values())

    def _get_module_signatures(
        self, modules: Sequence[ModulePath]
    ) -> Sequence[SymbolSignature]:
        return tuple(
            sig
            for module in modules[: self.top_k_sigs]
            if (sig := self._module_signature_map.get(module))
        )

    def compute_site_distances(
        self,
        usages: Collection[SymbolNameUsage],
        cursor: int,
        prompt_range: CharRange,
        summary: FileSummary,
    ) -> Mapping[SymbolNameUsage, tuple[float, ...]]:
        """Return the distances between usage sites and the cursor."""
        cursor_range = CharRange.point(cursor)
        if self.usage_distance_metric == "plain":
            return {u: (u.use_site.distance(cursor_range),) for u in usages}
        elif self.usage_distance_metric == "var_occurrence":
            return _occurrence_based_site_distances(
                usages, cursor, summary.local_analysis
            )
        elif self.usage_distance_metric == "tree":
            return {
                u: (
                    # First, prefer use sites that are in the prompt.
                    not prompt_range.contains(u.use_site),
                    # Second, compute the tree distance.
                    _get_tree_distance_to_cursor(
                        summary.scope_structure, u.use_site, cursor_range
                    ),
                    # Finally, break ties by using the plain distance.
                    u.use_site.distance(cursor_range),
                )
                for u in usages
            }
        else:
            assert_never(self.usage_distance_metric)


def _occurrence_based_site_distances(
    usages: Collection[SymbolNameUsage], cursor: int, analysis: LocalUsageAnalysis
) -> Mapping[SymbolNameUsage, tuple[float]]:
    """Allow 'tunneling' through variable occurrences to reduce distance to cursor.

    This function calculates the modified distances between the cursor and different
    API use sites by considering the possibility of "tunneling" through two occurrences
    of the same local variable to reduce the distance. We use the following three
    distances to compute the cost of traveling through a "tunnel":

    1. The entrance distance: This is the distance from an API usage site to a nearby\
        occurrence of a local variable.
    2. The tunnel distance: This is the distance between the "entrance" and "exit" of\
        the tunnel. The "exit" is the occurrence of the same local variable that is\
        closest to the cursor. The tunnel distance is computed as the difference\
        between the cursor distances of the entrance and the exit.
    3. The exit distance: This is the distance from the "exit" of the tunnel to the\
        cursor.

    The total cost of traveling through a tunnel is the sum of the entrance distance,
    the tunnel distance multiplied by a cost factor, and the exit distance. For each
    API usage site, we consider both the tunneling and non-tunneling cursor distances
    and return the minimum of the two.
    """

    cursor_range = CharRange.point(cursor)
    # tunnels whose exit-to-cursor distance greater than this are not considered
    max_exit_cursor_dis = 200

    # tunnels whose entrance-to-cursor distance greater than this are not considered
    max_entrance_cursor_dis = 2000

    def cursor_distance(crange: CharRange) -> float:
        # Note that any distance before the cursor is made cheaper to encourage
        # looking up more things before the cursor.
        if crange.stop <= cursor:
            return crange.distance(cursor_range) * 0.5
        else:
            return crange.distance(cursor_range)

    usage_tree = IntervalTree.from_tuples(
        # filter out empty ranges since they are not supported by intervaltree
        (site.start, site.stop, u)
        for u in usages
        if (site := u.use_site) and cursor_distance(site) <= max_entrance_cursor_dis
    )

    def get_intersection_sites(target_range: CharRange) -> list[SymbolNameUsage]:
        """Return all usages that intersect the given range."""
        return [
            u.data for u in usage_tree.overlap(target_range.start, target_range.stop)
        ]

    # maintains the cost of traveling to the cursor
    # initialize this with the plain distance to the cursor
    usage_costs = {u: (cursor_distance(u.use_site),) for u in usages}

    # usages within this many characters to a variable can take the tunnel
    # we keep this number small since we mostly care about usages of the form
    # `var.attr`, `var: Type`, or `int x =`.
    entrance_size = 4
    # the cost multiplier for traveling through a tunnel
    # this is kept small and mostly used to break ties
    tunnel_cost_factor = 0.01

    # The nested loops below have a time complexity of O(n_var_occurs * entrance_size)
    # So it's a linear-time algorithm when entrance_size is fixed.
    for vo in analysis.var_occurrences:
        # we will use the closest occurrence of the variable as the tunnel exit
        exit_cursor_dis = min(cursor_distance(vr) for vr in vo.ranges)
        if exit_cursor_dis > max_exit_cursor_dis:
            continue
        for var_range in vo.ranges:
            # the distance between tunnel entrance and exit
            tunnel_length = cursor_distance(var_range) - exit_cursor_dis

            entrance_range = CharRange(
                var_range.start - entrance_size, var_range.stop + entrance_size
            )

            for u in get_intersection_sites(entrance_range):
                # the cost of traveling through the tunnel
                usage_entrance_dis = u.use_site.distance(var_range)
                tunneling_cost = (
                    usage_entrance_dis
                    + tunnel_length * tunnel_cost_factor
                    + exit_cursor_dis
                )
                usage_costs[u] = (min(usage_costs[u][0], tunneling_cost),)

    return usage_costs


def _get_tree_distance_to_cursor(
    scope_tree: ScopeTreeStructure,
    r1: CharRange,
    r2: CharRange,
) -> int:
    """Compute the distance in the scope tree between the two ranges."""

    def get_path_for_range(r: CharRange, cur: ScopeTreeStructure) -> list[int]:
        result = list[int]()

        def recurse(cur: ScopeTreeStructure):
            """Get the path (as list of child indices) from the given scope to the given range."""
            for i, child in enumerate(cur.children):
                if child.range.contains(r):
                    result.append(i)
                    recurse(child)
                    return

        recurse(cur)
        return result

    r1_path = get_path_for_range(r1, scope_tree)
    r2_path = get_path_for_range(r2, scope_tree)
    # Compute the shared prefix of the two paths.
    i = 0
    while i < len(r1_path) and i < len(r2_path) and r1_path[i] == r2_path[i]:
        i += 1
    # Compute the tree distance.
    return len(r1_path) - i + len(r2_path) - i
