from base.static_analysis.common import assert_str_eq
from base.static_analysis.smart_header import (
    generic_is_header,
    generic_is_header_junk,
    is_cpp_header,
    is_cpp_header_junk,
    is_java_header,
    is_java_header_junk,
    is_python_header,
    is_python_header_junk,
    is_ruby_header,
    is_ruby_header_junk,
    is_rust_header_junk,
    is_swift_header,
    is_swift_header_junk,
    is_ts_header,
    is_ts_header_junk,
    is_rust_header,
    show_line_headers,
)


def test_generic_header():
    positive_examples = [
        "protected function configureSessionIDRegex()",
        "public void configureSessionIDRegex() {",
        "int void configureSessionIDRegex(",
        "function adder(x)",
    ]

    negative_examples = [
        "{",
        "(",
        "begin",
        "end",
        "if (some_condition) {",
        "for (int i = 0; i < 10; i++) {",
        "while (some_condition) {",
        "try {",
        "catch (e) {",
        "return (",
        "}, {",
        "return {a: 1}",
    ]
    for example in positive_examples:
        assert not generic_is_header_junk(example)
        assert generic_is_header(example)

    for example in negative_examples:
        assert generic_is_header_junk(example) or not generic_is_header(example)


PYTHON_HEADER_EXAMPLES = [
    "def foo(x):",
    "def foo(x): # comment",
    "def foo(",
    "async def foo(x):",
    "class A:",
    "class A(B):",
    "class A(B",
]


def test_python_header():
    positive_examples = PYTHON_HEADER_EXAMPLES
    negative_examples = ["@dataclass(frozen=True)", "definition=1", "if x < 1:"]
    for example in positive_examples:
        assert is_python_header(example)

    for example in negative_examples:
        assert not is_python_header(example)


def test_python_header_junk():
    assert is_python_header_junk("):")
    assert is_python_header_junk(") -> int:")
    assert is_python_header_junk(") -> int: # comment")
    assert is_python_header_junk("# just comments")

    for header in PYTHON_HEADER_EXAMPLES:
        assert not is_python_header_junk(header)


JAVA_HEADER_EXAMPLES = [
    "public class A {",
    "public class A extends B {",
    "public class A extends B",
    "class A <T>{",
    "static void main(int x) {",
    "protected void foo(",
    "static <T> void fromArrayToCollection(T[] a, Collection<T> c) {",
]


def test_java_header():
    positive_examples = JAVA_HEADER_EXAMPLES

    negative_examples = [
        "def foo()",
        "@override",
        "if (some_condition)",
    ]
    for example in positive_examples:
        assert is_java_header(example)

    for example in negative_examples:
        assert not is_java_header(example)


def test_java_header_junk():
    assert is_java_header_junk(") {")
    assert is_java_header_junk("){")
    assert is_java_header_junk("} else {")
    assert is_java_header_junk("// just comments")

    for header in JAVA_HEADER_EXAMPLES:
        assert not is_java_header_junk(header)


CPP_HEADER_EXAMPLES = [
    "int foo(int x) {",
    "int foo(int x) { // comment",
    "int foo1(int y)",
    "c10::intrusive_ptr<Backend> SimpleNCCLBackend::createSimpleNCCLBackend(",
    "SimpleNCCLBackend::SimpleNCCLBackend(const c10::intrusive_ptr<::c10d::Store>& store,"
    "template <typename T> void foo(T x) {",
    "class A {",
    "class A : public B {",
    "class A : public B",
    "template <typename T> class A {",
]


def test_cpp_header():
    positive_examples = CPP_HEADER_EXAMPLES

    negative_examples = [
        "while (some_condition) {",
        "#include <iostream>",
        "if (some_condition)",
        "if !(1 < 2)",
        "int x = 1;",
    ]

    for example in positive_examples:
        assert is_cpp_header(example)

    for example in negative_examples:
        assert not is_cpp_header(example)


def test_cpp_header_junk():
    assert is_cpp_header_junk(") {")
    assert is_cpp_header_junk("){")
    assert is_cpp_header_junk("} else {")
    assert is_cpp_header_junk("// just comments")

    for header in CPP_HEADER_EXAMPLES:
        assert not is_cpp_header_junk(header)


TS_HEADER_EXAMPLES = [
    "function foo(",
    "export function foo(x) {",
    "export function foo(x: string) { // comment",
    "export class A {",
    "constructor(",
    "public getSessionId(): string {",
    "protected async callApi<T extends {} | void>(",
    "export interface APIServer {",
    "export type BlobNameSearchForm = {",
    "class APIServerImpl implements APIServer {" "export class A extends B",
]


def test_ts_header():
    positive_examples = TS_HEADER_EXAMPLES

    negative_examples = [
        "try {",
        "} catch (e) {",
        "if (!response.ok) {",
    ]
    for example in positive_examples:
        assert is_ts_header(example)

    for example in negative_examples:
        assert not is_ts_header(example)


def test_ts_header_junk():
    assert is_ts_header_junk(") {")
    assert is_ts_header_junk("){")
    assert is_ts_header_junk(") => {")
    assert is_ts_header_junk("): number {")
    assert is_ts_header_junk("} else {")
    assert is_ts_header_junk("// just comments")

    for header in TS_HEADER_EXAMPLES:
        assert not is_ts_header_junk(header)


RUST_HEADER_EXAMPLES = [
    "fn foo(",
    "fn foo(x: i32) -> i32 {",
    "fn foo(&self, x: i32) -> i32 { // comment",
    "pub struct A {",
    "impl GcpIapVerifier for GcpIapVerifierImpl {",
    "mod tests {",
    "enum IapError",
    "pub trait GcpIapVerifier {",
]


def test_rust_header():
    positive_examples = RUST_HEADER_EXAMPLES
    negative_examples = [
        "while (some_condition) {",
        "#include <iostream>",
        "#[derive(Debug, Clone)]",
        "if (some_condition)",
        "/// Some comments",
        "if audience_prefix.is_empty() {",
        "return Err(Box::new(std::io::Error::new(",
    ]

    for example in positive_examples:
        assert is_rust_header(example)

    for example in negative_examples:
        assert not is_rust_header(example)


def test_rust_header_junk():
    assert is_rust_header_junk(") {")
    assert is_rust_header_junk("){")
    assert is_rust_header_junk("} else {")
    assert is_rust_header_junk("// just comments")

    for header in RUST_HEADER_EXAMPLES:
        assert not is_rust_header_junk(header)


RUBY_HEADER_EXAMPLES = [
    "def foo(x)",
    "def foo(x): # comment",
    "def foo(",
    "def foo",
    "class A",
    "class Bar < Foo",
]


def test_ruby_header():
    positive_examples = RUBY_HEADER_EXAMPLES

    negative_examples = [
        "if x < 1: # comment",
        "alias old_new new",
        "@initialized || false",
    ]
    for example in positive_examples:
        assert is_ruby_header(example)

    for example in negative_examples:
        assert not is_ruby_header(example)


def test_ruby_header_junk():
    assert is_ruby_header_junk("# just comments")

    for header in RUBY_HEADER_EXAMPLES:
        assert not is_ruby_header_junk(header)


SWIFT_HEADER_EXAMPLES = [
    "func foo(",
    "func foo(x: String) -> String {",
    "func foo(x: String) -> String { // comment",
    "class A {",
    "class A : B {",
    "class A : B",
    "struct A {",
    "init(length: Double) {",
    "open class SomeOpenClass {",
    "deinit {",
]


def test_swift_header():
    positive_examples = SWIFT_HEADER_EXAMPLES

    negative_examples = [
        "while (some_condition) {",
        "#include <iostream>",
        "if (some_condition)",
        "var x = 1;",
    ]

    for example in positive_examples:
        assert is_swift_header(example)

    for example in negative_examples:
        assert not is_swift_header(example)


def test_swift_header_junk():
    assert is_swift_header_junk(") {")
    assert is_swift_header_junk("){")
    assert is_swift_header_junk("} else {")
    assert is_swift_header_junk("// just comments")

    for header in SWIFT_HEADER_EXAMPLES:
        assert not is_swift_header_junk(header)


def test_example_assignments():
    rust_code = """\
use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
use std::collections::BTreeMap;

#[derive(Debug, thiserror::Error)]
pub enum IapError {
    #[error("unknown error")]
    Unknown,

    #[error("invalid token")]
    InvalidToken,
}

/// trait to verify GCP IAP JWTs
pub trait GcpIapVerifier {
    /// Verifies the IAP JWT and returns the email address of the user.
    fn verify(&self, iap_jwt_token: &str) -> std::result::Result<String, IapError>;
}

#[derive(Debug, Clone)]
pub struct GcpIapVerifierImpl {
    kid_to_public_key: BTreeMap<String, String>,
    audience_prefix: Vec<String>,
}

/// Implements the IAP JWT verifier for GCP
impl GcpIapVerifierImpl {
    /// Create a new IAP JWT verifier
    pub fn new(audience_prefix: Vec<String>) -> Result<Self, Box<dyn std::error::Error>> {
        if audience_prefix.is_empty() {
            return Err(Box::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "No audience prefix provided",
            )));
        }
        // read contents of public_iap_certs.json
        let contents =
            std::fs::read_to_string("base/cloud/iap/public_iap_certs.json").map_err(|e| {
                tracing::warn!("Error loading public_iap_certs.json: {:?}", e);
                e
            })?;
        // load contents into jwt_data
        let kid_to_public_key = serde_json::from_str(&contents)?;
        Ok(GcpIapVerifierImpl {
            kid_to_public_key,
            audience_prefix,
        })
    }
}

fn main(
    arg1: String,
    arg2: String,
){
    println!("Hello, world!");
}

"""

    expected_rust_assignment = """\
    innermost header | code
---------------------+------------------------------------------------------------------
                     | use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
                     | use std::collections::BTreeMap;
                     |
                     | #[derive(Debug, thiserror::Error)]
                     | pub enum IapError {
pub enum IapError {  |     #[error("unknown error")]
pub enum IapError {  |     Unknown,
pub enum IapError {  |
pub enum IapError {  |     #[error("invalid token")]
pub enum IapError {  |     InvalidToken,
                     | }
                     |
                     | /// trait to verify GCP IAP JWTs
                     | pub trait GcpIapVerifier {
pub trait GcpIapVeri |     /// Verifies the IAP JWT and returns the email address of the user.
pub trait GcpIapVeri |     fn verify(&self, iap_jwt_token: &str) -> std::result::Result<String, IapError>;
                     | }
                     |
                     | #[derive(Debug, Clone)]
                     | pub struct GcpIapVerifierImpl {
pub struct GcpIapVer |     kid_to_public_key: BTreeMap<String, String>,
pub struct GcpIapVer |     audience_prefix: Vec<String>,
                     | }
                     |
                     | /// Implements the IAP JWT verifier for GCP
                     | impl GcpIapVerifierImpl {
impl GcpIapVerifierI |     /// Create a new IAP JWT verifier
impl GcpIapVerifierI |     pub fn new(audience_prefix: Vec<String>) -> Result<Self, Box<dyn std::error::Error>> {
pub fn new(audience_ |         if audience_prefix.is_empty() {
pub fn new(audience_ |             return Err(Box::new(std::io::Error::new(
pub fn new(audience_ |                 std::io::ErrorKind::NotFound,
pub fn new(audience_ |                 "No audience prefix provided",
pub fn new(audience_ |             )));
pub fn new(audience_ |         }
pub fn new(audience_ |         // read contents of public_iap_certs.json
pub fn new(audience_ |         let contents =
pub fn new(audience_ |             std::fs::read_to_string("base/cloud/iap/public_iap_certs.json").map_err(|e| {
pub fn new(audience_ |                 tracing::warn!("Error loading public_iap_certs.json: {:?}", e);
pub fn new(audience_ |                 e
pub fn new(audience_ |             })?;
pub fn new(audience_ |         // load contents into jwt_data
pub fn new(audience_ |         let kid_to_public_key = serde_json::from_str(&contents)?;
pub fn new(audience_ |         Ok(GcpIapVerifierImpl {
pub fn new(audience_ |             kid_to_public_key,
pub fn new(audience_ |             audience_prefix,
pub fn new(audience_ |         })
impl GcpIapVerifierI |     }
                     | }
                     |
                     | fn main(
fn main(             |     arg1: String,
fn main(             |     arg2: String,
fn main(             | ){
fn main(             |     println!("Hello, world!");
                     | }
                     |
"""

    assignment_str = show_line_headers(rust_code, "Rust")
    assert_str_eq(assignment_str, expected_rust_assignment)

    expected_generic_assignment = """\
    innermost header | code
---------------------+------------------------------------------------------------------
                     | use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
                     | use std::collections::BTreeMap;
                     |
                     | #[derive(Debug, thiserror::Error)]
                     | pub enum IapError {
pub enum IapError {  |     #[error("unknown error")]
pub enum IapError {  |     Unknown,
pub enum IapError {  |
pub enum IapError {  |     #[error("invalid token")]
pub enum IapError {  |     InvalidToken,
                     | }
                     |
                     | /// trait to verify GCP IAP JWTs
                     | pub trait GcpIapVerifier {
pub trait GcpIapVeri |     /// Verifies the IAP JWT and returns the email address of the user.
pub trait GcpIapVeri |     fn verify(&self, iap_jwt_token: &str) -> std::result::Result<String, IapError>;
                     | }
                     |
                     | #[derive(Debug, Clone)]
                     | pub struct GcpIapVerifierImpl {
pub struct GcpIapVer |     kid_to_public_key: BTreeMap<String, String>,
pub struct GcpIapVer |     audience_prefix: Vec<String>,
                     | }
                     |
                     | /// Implements the IAP JWT verifier for GCP
                     | impl GcpIapVerifierImpl {
impl GcpIapVerifierI |     /// Create a new IAP JWT verifier
impl GcpIapVerifierI |     pub fn new(audience_prefix: Vec<String>) -> Result<Self, Box<dyn std::error::Error>> {
pub fn new(audience_ |         if audience_prefix.is_empty() {
pub fn new(audience_ |             return Err(Box::new(std::io::Error::new(
pub fn new(audience_ |                 std::io::ErrorKind::NotFound,
pub fn new(audience_ |                 "No audience prefix provided",
pub fn new(audience_ |             )));
pub fn new(audience_ |         }
pub fn new(audience_ |         // read contents of public_iap_certs.json
pub fn new(audience_ |         let contents =
let contents =       |             std::fs::read_to_string("base/cloud/iap/public_iap_certs.json").map_err(|e| {
let contents =       |                 tracing::warn!("Error loading public_iap_certs.json: {:?}", e);
let contents =       |                 e
let contents =       |             })?;
pub fn new(audience_ |         // load contents into jwt_data
pub fn new(audience_ |         let kid_to_public_key = serde_json::from_str(&contents)?;
pub fn new(audience_ |         Ok(GcpIapVerifierImpl {
Ok(GcpIapVerifierImp |             kid_to_public_key,
Ok(GcpIapVerifierImp |             audience_prefix,
pub fn new(audience_ |         })
impl GcpIapVerifierI |     }
                     | }
                     |
                     | fn main(
fn main(             |     arg1: String,
fn main(             |     arg2: String,
fn main(             | ){
fn main(             |     println!("Hello, world!");
                     | }
                     |
"""

    generic_assignment_str = show_line_headers(rust_code, None)
    assert_str_eq(generic_assignment_str, expected_generic_assignment)
