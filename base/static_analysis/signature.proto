syntax = "proto3";

// NOTE(arun): The names of all the messages below correspond to their dataclasses in
// the `base.static_analysis` package; please keep them in sync.
// Only add comments to message fields that require special translation.

// Corresponds to `pathlib.PosixPath`.
message Path {
  string value = 1 [debug_redact = true];
}

// Corresponds to `base.ranges.range_types.CharRange`.
message IntRange {
  int32 start = 1;

  int32 stop = 2;
}

// Corresponds to `base.static_analysis.usage_analysis.FileCharRange`.
message FileCharRange {
  Path path = 1 [debug_redact = true];

  IntRange crange = 2;
}

// Corresponds to `base.static_analysis.usage_analysis.SymbolNameUsage`.
message SymbolNameUsage {
  string name = 1 [debug_redact = true];

  IntRange use_site = 2;

  string kind = 3;

  optional FileCharRange def_range = 4;

  bool has_def_range = 5;
}

// Corresponds to `base.static_analysis.usage_analysis.SymbolDefinitionId`.
message SymbolDefinitionId {
  // NOTE(arun): We previously stored path and range fields here, but it was expensive
  // to deserialize. Instead, we just store this information as a string.
  reserved 1, 2, 3;

  string id = 4;
}

// Corresponds to `base.static_analysis.usage_analysis.SymbolDefinition`.
message SymbolDefinition {
  string name = 1 [debug_redact = true];

  Path path = 2 [debug_redact = true];

  IntRange full_crange = 3;

  IntRange name_crange = 4;

  IntRange prefix_crange = 5;

  bool in_class = 6;

  optional SymbolDefinitionId parent_id = 7;

  bool has_parent = 8;

  string kind = 9;

  string variable_summary = 10 [debug_redact = true];
}

// Corresponds to `base.static_analysis.usage_analysis.VarOccurrence`.
message VarOccurrence {
  string name = 1 [debug_redact = true];
  // this is a set
  repeated IntRange ranges = 2;
}

// Corresponds to `base.static_analysis.usage_analysis.LocalUsageAnalysis`.
message LocalUsageAnalysis {
  // this is a set
  repeated SymbolNameUsage name_usages = 1;

  // this is a set
  repeated VarOccurrence var_occurrences = 2;
}

// Corresponds to `base.static_analysis.usage_analysis.ScopeTreeStructure`.
message ScopeTreeStructure {
  IntRange range = 1;

  repeated ScopeTreeStructure children = 2;
}

// Corresponds to `base.static_analysis.usage_analysis.FileSummary`.
message FileSummary {
  string lang = 1;

  Path path = 2 [debug_redact = true];

  int32 size_chars = 3;

  int32 size_lines = 4;

  repeated SymbolDefinition definitions = 5;

  LocalUsageAnalysis local_analysis = 6;

  ScopeTreeStructure scope_structure = 7;
}

// Corresponds to `base.static_analysis.signature_index.SymbolSignature`.
message SymbolSignature {
  string text = 1 [debug_redact = true];

  Path path = 2 [debug_redact = true];

  IntRange crange = 3;

  IntRange lrange = 4;
}

// Corresponds to `base.static_analysis.signature_index.FileSignatureInfo`.
message FileSignatureInfo {
  // the dictionary keys of symbol_signatures
  repeated SymbolDefinitionId symbol_signatures_keys = 1;

  // The dictionary values of symbol_signatures.
  repeated SymbolSignature symbol_signatures_values = 2;

  SymbolSignature module_signature = 3;
}

// Corresponds to `base.static_analysis.signature_index.FileSummaryWithSignatures`.
message FileSummaryWithSignatures {
  FileSummary summary = 1;

  FileSignatureInfo signature_info = 2;
}

// Corresponds to `base.static_analysis.signature_index.SignatureQueryState`.
message SignatureQueryState {
  Path file_path = 1 [debug_redact = true];

  string lang = 2;

  IntRange est_prompt_range = 3;

  BasicUsageReranker reranker = 4;
}

// Corresponds to `base.static_analysis.usage_analysis.BasicUsageReranker`.
message BasicUsageReranker {
  Path current_file = 1 [debug_redact = true];

  // dictionary keys
  repeated Path file_bonuses_keys = 2 [debug_redact = true];

  // dictionary values
  repeated float file_bonuses_values = 3;

  // dictionary keys
  repeated SymbolDefinitionId def_bonuses_keys = 4;

  // dictionary values
  repeated float def_bonuses_values = 5;

  // this is a set
  repeated Path modules_recorded = 6 [debug_redact = true];
}
