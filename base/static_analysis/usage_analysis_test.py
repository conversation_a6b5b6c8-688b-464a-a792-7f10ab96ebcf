"""Unit tests for usage analysis.

pytest base/static_analysis/usage_analysis_test.py
"""

from __future__ import annotations

import itertools
import textwrap
from dataclasses import dataclass
from pathlib import Path
from typing import Collection, Mapping, Sequence

from base.static_analysis._usage_supports import _PythonUsageSupport
import pytest

from base.ranges.line_map import LnCol
from base.static_analysis._variable_supports import AllVariableSupports
from base.static_analysis.common import (
    LanguageID,
    assert_eq,
    decode_bytes,
    iter_ts_nodes_via_bfs,
    tsnode_to_crange,
)
from base.static_analysis.parsing import show_ts_node
from base.static_analysis.usage_analysis import (
    GlobalUsageAnalysis,
    LocalUsageAnalysis,
    ParsedFile,
    SymbolDefinition,
    SymbolName,
    UsageIndex,
    all_usage_supports,
    path_distance,
)

_TEST_DATA_ROOT = Path(__file__).parent / "testdata"

# pylint: disable=missing-function-docstring
# pylint: disable=protected-access
# flake8: noqa


def parse_and_index(lang: LanguageID, files: Mapping[Path, str]):
    """Parse all files and build a UsageIndex."""
    all_files = [ParsedFile.parse(path, lang, code) for path, code in files.items()]
    index = UsageIndex.from_files(all_files)
    return all_files, index


@dataclass
class HasUsage:
    """An assertion about a symbol usage."""

    symbol_name: str
    def_site_files: Collection[Path]

    def __repr__(self):
        files = {str(x) for x in self.def_site_files}
        return f"HasUsage({self.symbol_name}, {files})"


def check_usages(
    usages: GlobalUsageAnalysis,
    *assertions: HasUsage,
    exclude_declarations: bool = True,
):
    """Check that the resolved usages match the given assertions.

    Args:
        usages: The usages to check.
        assertions: The expected usages, sorted by (use site, name).
        exclude_declarations: Whether to exclude declaration usages from the check.
    """
    site2defs = usages.site2defs
    if exclude_declarations:
        site2defs = {u: v for u, v in site2defs.items() if not u.kind == "declare"}
    actual_usages = [(u, v) for u, v in site2defs.items()]
    # sort the usages by use site and name
    actual_usages.sort(key=lambda x: (x[0].use_site, x[0].name))
    for i, expect in enumerate(assertions):
        actual = actual_usages[i]
        if actual is None:
            raise AssertionError(
                f"Missing usage [{i}] (for {expect.symbol_name}), "
                f"expected symbols from files: {expect.def_site_files}\n"
                f"Actual symbols from files: {list(actual_usages)}"
            )
        assert_eq(actual[0].name, expect.symbol_name, lambda: f"{actual_usages=}")
        assert_eq(
            {x.path for x in actual[1]},
            set(expect.def_site_files),
            lambda: f"{actual_usages=}",
        )

    if len(assertions) != len(actual_usages):
        raise AssertionError(
            f"Incorrect number of usages.\n"
            f"Expected: {list(assertions)}\n"
            f"Actual usages: {actual_usages}"
        )


def check_refs(
    code: str,
    lang: LanguageID,
    ref_name_counts: Mapping[str, int],
    local_ref_name_counts: Mapping[str, int] | None = None,
    file_path: str = "test_file.txt",
):
    """Check that the code reference the given names."""
    pfile = ParsedFile.parse(Path(file_path), lang, code)
    refs = LocalUsageAnalysis.from_pfile(pfile).name_usages
    refs = list(sorted(refs, key=lambda x: x[1]))
    actual_counts = dict[str, int]()
    actual_local_counts = dict[str, int]()
    for u in refs:
        target = actual_counts if u.def_range is None else actual_local_counts
        target[u.name] = target.get(u.name, 0) + 1
    actual = list(sorted(actual_counts.items()))
    expected = list(sorted(ref_name_counts.items()))
    assert_eq(
        actual,
        expected,
        lambda: f"refs: {refs}\n ts_tree: {show_ts_node(pfile.ts_tree.root_node, max_level=10)}",
    )
    if local_ref_name_counts is not None:
        actual_local = list(sorted(actual_local_counts.items()))
        expected_local = list(sorted(local_ref_name_counts.items()))
        assert_eq(
            actual_local,
            expected_local,
            lambda: f"refs: {refs}\n ts_tree: {show_ts_node(pfile.ts_tree.root_node, max_level=10)}",
        )


def check_var_occurrences(
    code: str,
    lang: LanguageID,
    var_occurrences: Collection[tuple[SymbolName, Collection[LnCol]]],
    ignore_names: Collection[str] = (),
    file_path: str = "test_file.txt",
):
    """Check that the code reference the given names."""
    pfile = ParsedFile.parse(Path(file_path), lang, code)
    lmap = pfile.lmap

    actual = {
        (vo.name, tuple(lmap.get_line_column(x.start) for x in vo.ranges))
        for vo in LocalUsageAnalysis.from_pfile(pfile).var_occurrences
        if vo.name not in ignore_names
    }
    expected = {
        (name, tuple(sorted(locs)))
        for name, locs in var_occurrences
        if name not in ignore_names
    }

    if actual != expected:
        extra_elems = actual.difference(expected)
        missing_elems = expected.difference(actual)
        raise AssertionError(
            f"Expected: {sorted(expected)}\n"
            f"Extra: {sorted(extra_elems)}\n"
            f"Missing: {sorted(missing_elems)}"
        )


def check_module_usages(
    usages: GlobalUsageAnalysis, module_use_counts: Mapping[str, int]
):
    """Check that the code reference the given names."""
    actual_counts = dict[str, int]()
    for modules in usages.site2modules.values():
        for module in modules:
            actual_counts[str(module)] = actual_counts.get(str(module), 0) + 1

    assert_eq(
        list(sorted(actual_counts.items())),
        list(sorted(module_use_counts.items())),
        lambda: f"{usages.site2modules=}",
    )


def test_no_usage():
    """Below should have not usage reported."""
    file1 = """
    def foo(x, y: int):
        return x + y
    """
    parsed, index = parse_and_index("python", {Path("file1.py"): file1})

    # file 1 should have no usages
    check_usages(index.resolve_file_usages(parsed[0]))


def test_local_shadowing():
    """Local variables should be able to shadow global definition defined elsewhere."""
    file1 = """
    def bar():
        pass

    def double(x):
        # first double
        return 2 * x

    def baz():
        pass
    """

    file2 = """
    def double(x):
        # second double
        return 2 * x

    def main(bar):
        # use second double but not bar
        double(bar)
    """

    file3 = """
    bar(double(1))
    """

    file4 = """
    import file1
    def baz():
        pass
    file1.baz()
    """

    parsed, index = parse_and_index(
        "python",
        {
            Path("file1.py"): file1,
            Path("file2.py"): file2,
            Path("file3.py"): file3,
            Path("file4.py"): file4,
        },
    )

    # file 1 should have no usages
    check_usages(index.resolve_file_usages(parsed[0]))

    # file 2 should only use `double` from file 2.
    usages = index.resolve_file_usages(parsed[1])
    check_usages(usages, HasUsage("double", {Path("file2.py")}))

    # file 3 uses both `bar` and `double`.
    usages = index.resolve_file_usages(parsed[2])
    check_usages(
        usages,
        HasUsage("bar", {Path("file1.py")}),
        HasUsage("double", {Path("file1.py"), Path("file2.py")}),
    )

    # file 4 should use `baz` from file 1 and file 4.
    usages = index.resolve_file_usages(parsed[3])
    check_usages(usages, HasUsage("baz", {Path("file1.py"), Path("file4.py")}))


def test_type_annotation_usages():
    file1 = """
    class A:
        pass

    class B:
        pass

    class C:
        pass
    """

    file2 = """
    from file1 import A, B

    def use(a: A, y) -> B:
        pass
    """

    file3 = """
    class Foo:
        bar: C
        def use(self, a: list[A], y) -> B:
            pass
    """

    parsed, index = parse_and_index(
        "python",
        {Path("file1.py"): file1, Path("file2.py"): file2, Path("file3.py"): file3},
    )

    check_usages(index.resolve_file_usages(parsed[0]))

    usages = index.resolve_file_usages(parsed[1])
    check_usages(
        usages,
        HasUsage("A", {Path("file1.py")}),
        HasUsage("B", {Path("file1.py")}),
        HasUsage("A", {Path("file1.py")}),
        HasUsage("B", {Path("file1.py")}),
    )

    usages = index.resolve_file_usages(parsed[2])
    check_usages(
        usages,
        HasUsage("C", {Path("file1.py")}),
        HasUsage("A", {Path("file1.py")}),
        HasUsage("B", {Path("file1.py")}),
    )


def test_variable_usages():
    """Variables and fields defined in one place can be used elsewhere."""
    defs_code = """
    global1 = 1

    class Foo:
        foo1: int

        def __init__(self):
            self.foo2 = 1
    """

    uses_code = """

    from defs import global1

    def use(foo):
        return foo.foo1 + foo.foo2 + global1
    """

    parsed, index = parse_and_index(
        "python", {Path("defs.py"): defs_code, Path("uses.py"): uses_code}
    )
    check_usages(
        index.resolve_file_usages(parsed[1]),
        HasUsage("global1", {Path("defs.py")}),
        HasUsage(".foo1", {Path("defs.py")}),
        HasUsage(".foo2", {Path("defs.py")}),
        HasUsage("global1", {Path("defs.py")}),
    )


def test_class_methods():
    file1 = """
    class Foo:
        def bar(self, x):
            ...

        def double(self, x):
            # first double
            f = self.bar
            return f(x)
    """

    file2 = """
    class Bar:
        def double(self, x):
            # second double
            return 2 * x

    def main(bar):
        bar.double(5)
    """

    parsed, index = parse_and_index(
        "python", {Path("file1.py"): file1, Path("file2.py"): file2}
    )

    # file 1 should use `.bar`
    check_usages(
        index.resolve_file_usages(parsed[0]), HasUsage(".bar", {Path("file1.py")})
    )

    # file 2 should use both `.double` methods.
    usages = index.resolve_file_usages(parsed[1])
    check_usages(usages, HasUsage(".double", {Path("file1.py"), Path("file2.py")}))

    file3 = """\
    class Child(Bar):
        child_attr: int

        def double(self, x):
            # third double
            return 2 * x
    """
    parsed, index = parse_and_index(
        "python",
        {Path("file1.py"): file1, Path("file2.py"): file2, Path("file3.py"): file3},
    )

    # check declaration usages
    usages = index.resolve_file_usages(parsed[2])
    check_usages(
        usages,
        HasUsage("Bar", {Path("file2.py")}),
        # declaration
        HasUsage(".child_attr", {Path("file3.py")}),
        # declaration
        HasUsage(".double", {Path("file1.py"), Path("file2.py"), Path("file3.py")}),
        exclude_declarations=False,
    )


def test_python_var_occurrences():
    test_code = """\
def test(result, y: int):
    result.x = y + 1
    return f(result)

def f():
    loc = test(1, 1)
    return loc.x

result = 1 # a different result var
"""
    check_var_occurrences(
        test_code,
        "python",
        [
            ("result", {LnCol(0, 9), LnCol(1, 4), LnCol(2, 13)}),
            ("y", {LnCol(0, 17), LnCol(1, 15)}),
            ("test", {LnCol(0, 4), LnCol(5, 10)}),
            ("f", {LnCol(2, 11), LnCol(4, 4)}),
            ("loc", {LnCol(5, 4), LnCol(6, 11)}),
        ],
    )

    field_test_code = """\
class Foo: # ln0
    x: Bar # ln 1
    def __init__(self, x): # ln2
        self.x = Bar() # ln3
# ln4
    def method1(self): # ln5
        self.y = 1 # ln6
        return self.x.bar(self.z) # ln7
# ln8
    def method2(): # ln9
        return self.method1(self.y) # ln10
"""

    check_var_occurrences(
        field_test_code,
        "python",
        [
            (".x", {LnCol(1, 4), LnCol(3, 13), LnCol(7, 20)}),
            (".method1", {LnCol(5, 8), LnCol(10, 20)}),
            (".y", {LnCol(6, 13), LnCol(10, 33)}),
        ],
        ignore_names={"self"},
    )


def test_java_var_occurrences():
    test_code = """\
class Main {                      // [0]
    int x;

    public void main(int y) {
        x = 1;
        this.y = y;               // [5]
        this.inherited = 1;
    }

    public Inner getInherited() {
        return this.inherited + y; // [10]
    }

    int y;

    public class Inner { }         // [15]
}

"""
    # note that because Java supports implicit filed access, we report some
    # field occurrences twice with and without the dot in name.
    check_var_occurrences(
        test_code,
        "java",
        [
            (".x", {LnCol(1, 8), LnCol(4, 8)}),
            ("x", {LnCol(1, 8), LnCol(4, 8)}),
            ("y", {LnCol(3, 25), LnCol(5, 17)}),
            (".inherited", {LnCol(6, 13), LnCol(10, 20)}),
            (".y", {LnCol(5, 13), LnCol(10, 32), LnCol(13, 8)}),
            ("y", {LnCol(5, 13), LnCol(10, 32), LnCol(13, 8)}),
            (".Inner", {LnCol(9, 11), LnCol(15, 17)}),
            ("Inner", {LnCol(9, 11), LnCol(15, 17)}),
        ],
    )


def test_python_same_file_usages():
    """Test that usages of definitions from the same file are correctly resolved."""
    test_code = """\
Alias = Literal["a", "b"]

class Foo:
    pass

def f(x):
    return x

def usage(x: Alias):
    return f(Foo(x))
"""
    check_refs(
        test_code,
        "python",
        ref_name_counts={"Literal": 1},
        local_ref_name_counts={
            "Alias": 1,
            "Foo": 1,
            "f": 1,
        },
    )


def test_python_same_class_usages():
    """Test that usages of definitions from the same class are correctly resolved."""
    test_code = """\
class Test:
    x: int
    y = 1

    def f(self):
        return self.x

    def usage(self):
        return self.f(self.y)
"""
    check_refs(
        test_code,
        "python",
        ref_name_counts={"int": 1},
        local_ref_name_counts={
            ".x": 1,
            ".y": 1,
            ".f": 1,
        },
    )


def test_python_module_usages():
    file1 = """
    def f1():
        pass

    def f2():
        pass

    class C1:
        def f1(self):
            pass
    """

    file2 = """
    from file1 import p1
    import file3

    p1.f1()
    p1.C1()
    """

    file3 = """
    import file1 as f1
    import file3

    def main():
        (a + b).f1()
        f1.f2()
        f1.y
    """

    parsed, index = parse_and_index(
        "python",
        {Path("file1.py"): file1, Path("file2.py"): file2, Path("file3.py"): file3},
    )
    pfile1, pfile2, pfile3 = parsed

    # file 1 should have no usages
    check_usages(index.resolve_file_usages(pfile1))

    check_refs(
        file2,
        pfile2.lang,
        {
            "@file1": 1,
            "@file3": 1,
            "@file1/p1": 3,
            "p1": 3,
            "f1": 1,
            ".f1": 1,
            "C1": 1,
            ".C1": 1,
        },
    )

    # file 2 should have usages from `p1`
    # we cannot tell whether p1.f1() is a function or a method call from local analysis
    # alone, so we report both usages.
    check_usages(
        index.resolve_file_usages(pfile2),
        HasUsage(".f1", {Path("file1.py")}),
        HasUsage("f1", {Path("file1.py")}),
        HasUsage("C1", {Path("file1.py")}),
    )

    check_module_usages(
        index.resolve_file_usages(pfile2), {"file1.py": 1, "file3.py": 1}
    )

    check_usages(
        index.resolve_file_usages(pfile3),
        HasUsage(".f1", {Path("file1.py")}),
        HasUsage("f2", {Path("file1.py")}),
    )

    # file1.py used 3 times: once in the import stmt, and twice later
    # should not have a file usage pointing to itself
    check_module_usages(index.resolve_file_usages(pfile3), {"file1.py": 3})

    # test module resolution
    file1 = "def foo(): pass"
    file2 = "def foo(): pass"
    file3 = "def foo(): pass"
    file4 = """
    from extra.augment import foo
    """
    parsed, index = parse_and_index(
        "python",
        {
            Path("foo.py"): file1,
            Path("augment/foo.py"): file2,
            Path("bar/foo.py"): file3,
            Path("root/use.py"): file4,
        },
    )

    pfile1, pfile2, pfile3, pfile4 = parsed

    # the best matching file should be used
    check_module_usages(index.resolve_file_usages(pfile4), {"augment/foo.py": 1})

    file5 = "def foo(): pass"
    pfile5 = ParsedFile.parse(Path("src/augment/foo.py"), "python", file5)
    index.update_file(pfile5)

    # now there should be two matches
    check_module_usages(
        index.resolve_file_usages(pfile4),
        {"augment/foo.py": 1, "src/augment/foo.py": 1},
    )

    parse_module_path = all_usage_supports["python"].parse_module_path
    assert parse_module_path(".") == Path("../__init__")
    assert parse_module_path("..a.b") == Path("../../a/b")
    assert parse_module_path("a.b") == Path("a/b")

    # test relative imports
    file1 = "def foo(): pass"
    file2 = "def foo(): pass"
    file3 = """
    from .file1 import foo
    from ..file2 import foo
    from . import *
    """

    check_refs(
        file3,
        "python",
        {
            "@src/file1": 1,
            "@src/file1/foo": 1,
            "@file2": 1,
            "@file2/foo": 1,
            "@src/__init__": 1,
            "foo": 2,
        },
        file_path="src/file3.py",
    )

    parsed, index = parse_and_index(
        "python",
        {
            Path("src/file1.py"): file1,
            Path("file2.py"): file2,
            Path("src/file3.py"): file3,
            Path("file1.py"): file1,
            Path("src/__init__.py"): file1,
        },
    )

    check_module_usages(
        index.resolve_file_usages(parsed[2]),
        {"src/file1.py": 1, "file2.py": 1, "src/__init__.py": 1},
    )

    # test that invalid relative imports won't crash
    check_refs(
        """from ...file1 import foo""",
        "python",
        {"@file1": 1, "@file1/foo": 1, "foo": 1},
        file_path="test.py",
    )


def test_low_level_apis_for_parsedfile():
    """Evaluate the apis of ParsedFile."""
    file_str = textwrap.dedent(
        """\
    import tree_sitter as ts

    class A:
        ts_tree: ts.Tree

        @property
        def ts_nodes(self) -> list[ts.Node]:
            '''Collect all the tree-sitter nodes via BFS.'''
            all_nodes = []
            queue = collections.deque([self.ts_tree.root_node])
            while queue:
                node = queue.popleft()
                queue.extend(node.children)
                all_nodes.append(node)
            return all_nodes"""
    )
    assert len(file_str.split("\n")) == 15
    parsed = ParsedFile.parse(Path("test.py"), "python", file_str)

    for node in iter_ts_nodes_via_bfs(parsed.ts_tree):
        if node.type in ("module", "class_definition"):
            continue
        node_text = decode_bytes(node.text)
        crange = tsnode_to_crange(node, parsed.bmap)
        crange_text: str = parsed.code[crange.start : crange.stop]
        error_message = (
            f"node type: {node.type}"
            f"\nnode text: {node_text}"
            f"\nnode: {node}"
            f"\ncrange: {crange}"
            f"\ncrange text: {crange_text}"
        )
        if "import tree_sitter as ts" in decode_bytes(node.text):
            assert parsed.lmap.get_line_number(crange.start) == 0, error_message
            assert parsed.lmap.get_line_column(crange.start) == (0, 0), error_message
        if "ts_tree: ts.Tree" in decode_bytes(node.text):
            assert parsed.lmap.get_line_number(crange.start) == 3, error_message
            assert parsed.lmap.get_line_column(crange.start) == (3, 4), error_message


def test_chain_access():
    file1 = """
    class A:
        def foo(self):
            ...

        def bar(self):
            ...

    a.foo().bar()
    """
    parsed, index = parse_and_index("python", {Path("file1.py"): file1})

    check_usages(
        index.resolve_file_usages(parsed[0]),
        HasUsage(".foo", {Path("file1.py")}),
        HasUsage(".bar", {Path("file1.py")}),
    )


def test_updates():
    file1 = """
    class A:
        def bar(self, ):
            pass

        def double(self, x):
            # first double
            return 2 * x
    """

    file2 = """
    a.double(5)
    """

    parsed, index = parse_and_index(
        "python", {Path("file1.py"): file1, Path("file2.py"): file2}
    )

    # file 2 should have only a single usage
    check_usages(
        index.resolve_file_usages(parsed[1]), HasUsage(".double", {Path("file1.py")})
    )

    # add a new definition of `double` in file 2
    file2 = """
    class B:
        def double(self, x):
            # second double
            return 2 * x
    #
    a.double(5)
    """
    pfile2 = ParsedFile.parse(Path("file2.py"), "python", file2)
    index.update_file(pfile2)

    check_usages(
        index.resolve_file_usages(pfile2),
        HasUsage(".double", {Path("file1.py"), Path("file2.py")}),
    )

    index.remove_file(Path("file1.py"))

    check_usages(
        index.resolve_file_usages(pfile2),
        HasUsage(".double", {Path("file2.py")}),
    )


def test_default_arguments():
    file1 = """
    x = 42
    args = 43
    kwargs = 44
    """

    file2 = """
    def foo(x = 137, *args, **kwargs):
      pass
    """
    parsed, index = parse_and_index(
        "python", {Path("file1.py"): file1, Path("file2.py"): file2}
    )

    # file 1 should have no usages
    check_usages(index.resolve_file_usages(parsed[0]))

    # file 2 should not count parameters as usages
    check_usages(index.resolve_file_usages(parsed[1]))


BASE_DIR = Path(__file__).parent.parent


def test_from_files():
    """Building UsageIndex in batch should match building it sequentially."""
    all_files = list((BASE_DIR / "static_analysis").glob("**/*.py"))
    pfiles = [ParsedFile.parse(f, "python") for f in all_files]

    index1 = UsageIndex.from_files(pfiles)
    index2 = UsageIndex()
    for f in pfiles:
        index2.update_file(f)

    assert index1.symbol_map == index2.symbol_map


def test_usage_ranking():
    file1 = """
    class A:
        def foo():
            pass
    """
    file2 = """
    class B:
        def foo():
            pass
    """
    file3 = """
    class C:
        def foo():
            pass
    """
    use_file = """
    A().foo()
    """
    [use_file, *_], index = parse_and_index(
        "python",
        {
            Path("use.py"): use_file,
            Path("file1.py"): file1,
            Path("file2.py"): file2,
            Path("file3.py"): file3,
        },
    )
    usages = index.resolve_file_usages(use_file)
    name2defs: Mapping[str, Sequence[SymbolDefinition]] = usages.name_to_defs()

    # test there should be 3 defs for the foo usage
    assert len(name2defs[".foo"]) == 3

    # test that the first foo usage should come from file1
    assert name2defs[".foo"][0].path == Path("file1.py")

    use2_file = """
    from file3 import C

    def use(c):
        return c.foo()
    """
    use2_file = ParsedFile.parse(Path("use2.py"), "python", use2_file)
    index.update_file(use2_file)

    usages = index.resolve_file_usages(use2_file)
    name2defs = usages.name_to_defs()

    # still only 3 defs for the foo usage
    assert len(name2defs[".foo"]) == 3
    # because of the import, the first foo usage should come from file3
    assert name2defs[".foo"][0].path == Path("file3.py")


# -----------------------------------------------------------------------------
# Now similarly test other languages.


class TestJava:
    def test_local_var_refs(self):
        local_var_test = textwrap.dedent(
            """
            int x = 1;
            try (Reader reader = new Reader()) {}
            catch (Exception ex) {};
            int[] numbers = {1, 2, 3};
            for (int num : numbers) {
                num++;
            }
            """
        )
        # all local variables should not be referenced
        check_refs(local_var_test, "java", {"Reader": 2, "Exception": 1})

    def test_import_refs(self):
        # FIXME: support `import static math.PI` style imports
        import_test = textwrap.dedent(
            """
            import foo.*;
            import java.util.List;

            foo.Math.sqrt(1);
            List<Integer> list = new bar.ArrayList<Integer>();
            """
        )

        check_refs(
            import_test,
            "java",
            {
                "@foo": 2,
                "@java/util": 1,
                "List": 2,
                "Math": 1,
                ".sqrt": 1,
                "Integer": 2,
                "bar": 1,
                "ArrayList": 1,
                ".ArrayList": 1,
            },
        )

    def test_method_refs(self):
        method_test = textwrap.dedent(
            """
            public class StrictPerf {
                Flowable source;

                @Benchmark
                public void internal(Blackhole bh) {
                    source.subscribe();
                }
            }
            """
        )
        check_refs(
            method_test,
            "java",
            {"Flowable": 1, "Benchmark": 1, "Blackhole": 1, ".subscribe": 1},
        )

    def test_inner_class_refs(self):
        testcase = textwrap.dedent(
            """
            class Foo {
                void f() {
                    f(new Bar()).foo();
                }

                class Bar {
                    void foo() {}
                }
            }
            """
        )

        check_refs(testcase, "java", {".f": 1, ".foo": 1})

    def test_basic_usages(self):
        file1 = """
        class A {
            void foo() {}

            void bar() {}

            int double(int x) {
                return 2 * x;
            }
        }
        """

        file2 = """
        class B {
            int double(int x) {
                return 2 * x;
            }

            public static void main(int bar) {
                this.double(bar);
                bar.foo;
            }
        }
        """

        parsed, index = parse_and_index(
            "java", {Path("file1.java"): file1, Path("file2.java"): file2}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should have only `double` usage
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage(".double", {Path("file2.java")}),
            HasUsage(".foo", {Path("file1.java")}),
        )

    def test_variable_usages(self):
        def_file = """\
        class A {
            string color;
            int year = 1995;
            private int age;
            public static double PI = 3.14;
        }
        """

        use_file = """\
        class Usage {
            int u_field;
            public void use(A x) {
                x.color = x.year + x.age;
                return u_field + A.PI:
            }
        }
        """

        parsed, index = parse_and_index(
            "java", {Path("def.java"): def_file, Path("use.java"): use_file}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should use all variables from file 1
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("A", {Path("def.java")}),
            HasUsage(".color", {Path("def.java")}),
            HasUsage(".year", {Path("def.java")}),
            HasUsage(".age", {Path("def.java")}),
            HasUsage(".u_field", {Path("use.java")}),
            HasUsage("A", {Path("def.java")}),
            HasUsage(".PI", {Path("def.java")}),
        )

    def test_inner_class_usages(self):
        def_file = """\
        class Outer {
            int out_v;
            int x;
            class Inner {
                int inner_v;
                void method(int x) {
                    return out_v + inner_v + x;
                }
            }
        }
        """

        use_file = """\
        class Usage {
            public void use(Outer.Inner thing) {
            }
        }
        """

        parsed, index = parse_and_index(
            "java", {Path("def.java"): def_file, Path("use.java"): use_file}
        )

        # file 1 should have no usages
        check_usages(
            index.resolve_file_usages(parsed[0]),
            HasUsage(".out_v", {Path("def.java")}),
            HasUsage(".inner_v", {Path("def.java")}),
        )

        # file 2 use Inner.
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("Outer", {Path("def.java")}),
            HasUsage(".Inner", {Path("def.java")}),
        )


class TestCPP:
    def test_local_shadowing(self):
        file1 = """
        int bar(){ };

        int df(int x) {
            return 2 * x;
        };
        """

        file2 = """
        int main(int bar) {
            df(bar);
        };
        """

        parsed, index = parse_and_index(
            "cpp", {Path("file1.cpp"): file1, Path("file2.cpp"): file2}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should have only a single usage
        usages = index.resolve_file_usages(parsed[1])
        check_usages(usages, HasUsage("df", {Path("file1.cpp")}))

    def test_class_methods(self):
        file1 = """
        class Foo {
            float bar(int x) {
                return 1
            }

            int df(int x) {
                int f = self.bar
                return f(x);
            };
        };
        """

        file2 = """
        class Bar {
            int df(int x) {
                return 2 * x;
            }

            int bar_use() {
                this.df(1);
            };
        };

        int main(int bar) {
            bar.df(5);
        };
        """

        parsed, index = parse_and_index(
            "cpp", {Path("file1.cpp"): file1, Path("file2.cpp"): file2}
        )

        # file 1 should use `.bar`
        check_usages(
            index.resolve_file_usages(parsed[0]), HasUsage(".bar", {Path("file1.cpp")})
        )

        # file 2 uses the same-file `.df` first and then  both `.df` methods.
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage(
                ".df", {Path("file1.cpp"), Path("file2.cpp")}
            ),  # FIXME(jiayi): this should be just file2.cpp
            HasUsage(".df", {Path("file1.cpp"), Path("file2.cpp")}),
        )


class TestJavaScriptAndTypeScript:
    def test_local_shadowing(self):
        file1 = """
        function bar() {}

        function df(x) {
            return 2 * x;
        }
        """

        file2 = """
        function main(bar) {
            df(bar);
        }
        """

        for lang in ("javascript", "typescript"):
            parsed, index = parse_and_index(
                lang, {Path("file1.js"): file1, Path("file2.js"): file2}
            )

            # file 1 should have no usages
            check_usages(index.resolve_file_usages(parsed[0]))

            # file 2 should have only a single usage
            usages = index.resolve_file_usages(parsed[1])
            check_usages(usages, HasUsage("df", {Path("file1.js")}))

    def test_class_methods(self):
        file1 = """
        class Foo {
            bar(x) {
                return 1
            }

            df(x) {
                let f = this.bar
                return f(x);
            }
        }
        """

        file2 = """
        class Bar {
            df(x) {
                return 2 * x;
            }
        }

        function main(bar) {
            bar.df(5);
        }
        """

        for lang in ("javascript", "typescript"):
            parsed, index = parse_and_index(
                lang, {Path("file1.js"): file1, Path("file2.js"): file2}
            )

            # file 1 should use `.bar`
            check_usages(
                index.resolve_file_usages(parsed[0]),
                HasUsage(".bar", {Path("file1.js")}),
            )

            # file 2 should use both `.df` methods.
            usages = index.resolve_file_usages(parsed[1])
            check_usages(usages, HasUsage(".df", {Path("file1.js"), Path("file2.js")}))

    def test_exported_class(self):
        file1 = """
        /**
        * Doc string
        */
        export class ExtensionDisabled extends Error {
            constructor() {
                super('Augment extension has been disabled');
            }
        };
        """

        file2 = """
        v = new ExtensionDisabled;
        """

        parsed, index = parse_and_index(
            "typescript", {Path("file1.ts"): file1, Path("file2.ts"): file2}
        )

        # file 1 should use `.bar`
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should use both `.df` methods.
        usages = index.resolve_file_usages(parsed[1])
        check_usages(usages, HasUsage("ExtensionDisabled", {Path("file1.ts")}))

    def test_local_refs(self):
        local_var_test = textwrap.dedent(
            """
            const x = 1;
            let {key1: val1} = some_object;
            let {key2: val2}: SomeObject = some_object;

            for (let for_var: number = 0; for_var < 5; for_var++) { }
            switch (x) {
                case case_var: {
                }
            }

            let add = (a1: number, a2: number?): number => {
                return a1 + a2;
            }
            try {
            } catch (exception) {
            }
            """
        )
        # all local names above should not be reported
        check_refs(
            local_var_test,
            "typescript",
            {"some_object": 2, "SomeObject": 1},
        )

        import_test = textwrap.dedent(
            r"""
            import * as math from './math';
            import myFunction from './myFunction';
            import { add, sub as subtract } from "./add_sub";
            import 'my\\module';
            import defaultExp, { Component } from '@angular/core';

            add(1, 2)
            subtract(1, 2)
            math.good()
            """
        )

        check_refs(
            import_test,
            "typescript",
            {
                "@add_sub": 1,
                "@math": 2,
                r"@my\\module": 1,
                "@@angular/core": 1,
                "@myFunction": 1,
                "Component": 1,
                "add": 2,
                "sub": 2,
                "good": 1,
            },
        )

    def test_variable_usages(self):
        def_file = """\
        class A {
            public color: string;
            public readonly year: number;
            age?: number;
            static PI: number = 3.14;
        }

        let global1 = 1.2;
        """

        use_file = """\
        class Usage {
            public use(x: A) {
                A.PI = global1;
                x.color = (x.year + x.age).toString();
            }
        }
        """

        parsed, index = parse_and_index(
            "typescript", {Path("def.ts"): def_file, Path("use.ts"): use_file}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should use all variables from file 1
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("A", {Path("def.ts")}),
            HasUsage("A", {Path("def.ts")}),
            HasUsage(".PI", {Path("def.ts")}),
            HasUsage("global1", {Path("def.ts")}),
            HasUsage(".color", {Path("def.ts")}),
            HasUsage(".year", {Path("def.ts")}),
            HasUsage(".age", {Path("def.ts")}),
        )


class TestGo:
    def test_local_refs(self):
        local_var_test = textwrap.dedent(
            """
            const x int = 1
            var y, z int = 2

            func main(param1, param2 int, param3 bool) {
                v, w := 1
                for i := 0; i < 10; i++ {
                    w += i
                }
                for key, value := range map[string]int{"one": 1, "two": 2} { }
            }
            """
        )
        # all local names above should not be reported
        check_refs(
            local_var_test,
            "go",
            {},
        )

        import_test = textwrap.dedent(
            """
            import "fmt"
            import os_alias "os"
            import "example.com/ex"

            fmt.Println("Hello, world!")
            os_alias.Exit(0)
            ex.Run()
            """
        )

        check_refs(
            import_test,
            "go",
            {
                "@fmt": 2,
                "@os": 2,
                "Println": 1,
                "Exit": 1,
                "@example.com/ex": 2,
                "Run": 1,
            },
        )

        type_annotation_test = textwrap.dedent(
            """
            func (f Foo) bar(x Bar) {
                const x, y Bar = 1
            }
            """
        )
        check_refs(
            type_annotation_test,
            "go",
            {"Foo": 1, "Bar": 2},
        )

    def test_local_shadowing(self):
        file1 = """
        func bar() {}

        func df(x int) {
            return 2 * x;
        }
        """

        file2 = """
        func main(bar int) {
            df(bar);
        }
        """

        parsed, index = parse_and_index(
            "go", {Path("file1.go"): file1, Path("file2.go"): file2}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should have only a single usage
        usages = index.resolve_file_usages(parsed[1])
        check_usages(usages, HasUsage("df", {Path("file1.go")}))

    def test_class_methods(self):
        file1 = """
        type Foo struct {
            x int
        }

        func (f Foo) bar(x int) { }

        func (f Foo) df(x int) {
            g = f.bar
            g(x);
        }
        """

        file2 = """
        type Bar struct {
            Foo
        }

        func (b Bar) df(x int) {
            return 2 * x;
        }

        func main(bar Bar) {
            bar.df(5);
        }
        """

        parsed, index = parse_and_index(
            "go", {Path("file1.go"): file1, Path("file2.go"): file2}
        )

        # file 1 should use `.bar`
        check_usages(
            index.resolve_file_usages(parsed[0]),
            HasUsage("Foo", {Path("file1.go")}),
            HasUsage("Foo", {Path("file1.go")}),
            HasUsage(".bar", {Path("file1.go")}),
        )

        # file 2 should use both `.df` methods.
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("Foo", {Path("file1.go")}),
            HasUsage("Bar", {Path("file2.go")}),
            HasUsage("Bar", {Path("file2.go")}),
            HasUsage(".df", {Path("file1.go"), Path("file2.go")}),
        )

    def test_variable_usages(self):
        def_file = """\
        type A struct {
            color string
            year, age int
            PI float
        }

        var global1, global2 int = 1

        func (f Foo) bar(x int) {
            const inner = 1;
        }
        """

        use_file = """\
        func (f Usage) df(x A) {
            x.PI = global1;
            f.color = (x.year + x.age).toString();
            inner = 2;  # this should not trigger usage
        }
        """

        parsed, index = parse_and_index(
            "go", {Path("def.go"): def_file, Path("use.go"): use_file}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should use all variables from file 1
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("A", {Path("def.go")}),
            HasUsage(".PI", {Path("def.go")}),
            HasUsage("global1", {Path("def.go")}),
            HasUsage(".color", {Path("def.go")}),
            HasUsage(".year", {Path("def.go")}),
            HasUsage(".age", {Path("def.go")}),
        )


class TestRust:
    def test_local_var_refs(self):
        local_var_test = textwrap.dedent(
            """
            fn foo(x: i32, y: i32) -> i32 {
                let z = x + y;
                let (t1, t2) = (x, y);
                let [l1, l2] = [1, 2];
                if let Some(Foo(p1), Bar(p2)) = Some(z) {}
                match Some(z) {
                    Some(a) => 1,
                }
                return z;
            }
            """
        )
        check_refs(local_var_test, "rust", {"Some": 4, "Foo": 1, "Bar": 1})

    def test_uses_var_refs(self):
        local_var_test = textwrap.dedent(
            """
            use std::time::Duration;
            use std::collections::{HashMap, HashSet};

            let map = HashMap::new();
            map.insert(1, 2);
            """
        )
        check_refs(
            local_var_test,
            "rust",
            {
                "@std/time": 1,
                "Duration": 1,
                "@std/collections": 1,
                "HashMap": 2,
                "HashSet": 1,
                ".new": 1,
                ".insert": 1,
            },
        )

    def test_method_refs(self):
        method_test = textwrap.dedent(
            """
            struct StrictPerf {
                source: Flowable
            }
            impl StrictPerf {
                fn internal(self, bh: Blackhole) {
                    self.source.subscribe();
                }
            }
            """
        )
        check_refs(
            method_test,
            "rust",
            # TODO: .source should ideally be resolved as a local usage
            {"Flowable": 1, "Blackhole": 1, ".source": 1, ".subscribe": 1},
        )

    def test_destructure_refs(self):
        method_test = textwrap.dedent(
            """
            struct Foo {
                x: i32,
                y: i32,
            }
            fn bar(f: Foo) -> i32 {
                let Foo{ x: a, y } = f;
                return a + y;
            }
            """
        )
        # all variables are local
        check_refs(method_test, "rust", {}, local_ref_name_counts={"Foo": 2})

    def test_nested_pattern_matching(self):
        """Test that deeply nested pattern matching is handled correctly."""
        test_code = """\
fn foo() -> i32 {
    match obj {
        (a, (b, Vec{x: c, y: (d, e)})) => 1,
    }
    // below should not trigger usages since they are all local vars
    return a + b + c + d + e;
}
"""
        check_refs(test_code, "rust", {"obj": 1, "Vec": 1})

    def test_at_refs(self):
        method_test = textwrap.dedent(
            """
            fn foo(x: i32) -> i32 {
                let c = match x {
                    y @ 1..=10 => y,
                    _ => 0,
                }
                return c;
            }
            """
        )
        # `y` is a local variable and should not be reported.
        check_refs(
            method_test,
            "rust",
            {},
        )

    def test_basic_usages(self):
        file1 = """
        struct A {}
        impl A {
            fn foo(self) {}
            fn bar(self) {}
            fn double(x: i32) -> i32 {
                return 2 * x;
            }
        }
        """

        file2 = """
        struct B {}
        impl B {
            fn double(x: i32) -> i32 {
                return 2 * x;
            }
            fn main(self, bar: i32) {
                self.double();
                self.foo();
            }
        }
        """

        parsed, index = parse_and_index(
            "rust", {Path("file1.rs"): file1, Path("file2.rs"): file2}
        )

        # file 1 has only usage of A referenced by impl
        check_usages(
            index.resolve_file_usages(parsed[0]),
            HasUsage("A", {Path("file1.rs")}),
        )

        # file 2 usages
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("B", {Path("file2.rs")}),
            HasUsage(".double", {Path("file2.rs")}),
            HasUsage(".foo", {Path("file1.rs")}),
        )

    def test_local_shadowing(self):
        file1 = """
        fn bar() {}

        fn df(x: i32) -> {
            return 2 * x;
        }
        """

        file2 = """
        fn main(bar: i32) {
            df(bar);
        }
        """

        parsed, index = parse_and_index(
            "rust", {Path("file1.rs"): file1, Path("file2.rs"): file2}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should have only a single usage
        usages = index.resolve_file_usages(parsed[1])
        check_usages(usages, HasUsage("df", {Path("file1.rs")}))

    def test_variable_usages(self):
        def_file = """\
        const PI: f64 = 3.14;
        static E: f64 = 2.718;
        struct A {
            color: String,
            year: i32,
            age: i32,
        }
        """

        use_file = """\
        struct Usage {}
        impl Usage {
            fn use(x: A) {
                PI;
                E;
                x.color = x.year + x.age;
            }
        }
        """

        parsed, index = parse_and_index(
            "rust", {Path("def.rs"): def_file, Path("use.rs"): use_file}
        )

        # file 1 should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # file 2 should use all variables from file 1
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("Usage", {Path("use.rs")}),
            HasUsage("A", {Path("def.rs")}),
            HasUsage("PI", {Path("def.rs")}),
            HasUsage("E", {Path("def.rs")}),
            HasUsage(".color", {Path("def.rs")}),
            HasUsage(".year", {Path("def.rs")}),
            HasUsage(".age", {Path("def.rs")}),
        )

    def test_structs_enums(self):
        def_file = textwrap.dedent(
            """
            struct Foo1 {
                x: i32,
                y: i32,
            }

            struct Foo2(i32, i32);

            enum Foo3 { Variant1, Variant2 }

            struct Foo4;

            enum Foo5 {
                Variant51,
                Variant52 { x2: i32, y2: i32 },
                Variant53(i32, i32),
            }

            trait Foo6 {
                fn foo6(&self) -> i32;
            }
            """
        )

        use_file1 = textwrap.dedent(
            """
            fn foo(f1: Foo1, f2: Foo2, f3: Foo3, f4: Foo4, f5: Foo5, f6: &Foo6) -> i32 {
                return f1.x + f1.y + f2.0 + f2.1 + match f3 {
                    Foo3::Variant1(x) => 1,
                    Foo3::Variant2(y) => 2,
                } + f6.foo6()
            }
            """
        )

        use_file2 = textwrap.dedent(
            """
            fn bar(Foo5 f5) -> i32 {
                return match f5 {
                    Foo5::Variant51 => 1,
                    Foo5::Variant52{ x2, y2: 0 } => x2 + y2,
                    Foo5::Variant53(x3, y3) => x3 + y3,
                };
            }
            """
        )

        parsed, index = parse_and_index(
            "rust",
            {
                Path("def.rs"): def_file,
                Path("use1.rs"): use_file1,
                Path("use2.rs"): use_file2,
            },
        )

        # def.rs should have no usages
        check_usages(index.resolve_file_usages(parsed[0]))

        # test usages in use1.rs
        usages = index.resolve_file_usages(parsed[1])
        check_usages(
            usages,
            HasUsage("Foo1", {Path("def.rs")}),
            HasUsage("Foo2", {Path("def.rs")}),
            HasUsage("Foo3", {Path("def.rs")}),
            HasUsage("Foo4", {Path("def.rs")}),
            HasUsage("Foo5", {Path("def.rs")}),
            HasUsage("Foo6", {Path("def.rs")}),
            HasUsage(".x", {Path("def.rs")}),
            HasUsage(".y", {Path("def.rs")}),
            HasUsage(".0", {Path("def.rs")}),
            HasUsage(".1", {Path("def.rs")}),
            HasUsage("Foo3", {Path("def.rs")}),
            HasUsage(".Variant1", {Path("def.rs")}),
            HasUsage("Foo3", {Path("def.rs")}),
            HasUsage(".Variant2", {Path("def.rs")}),
            HasUsage(".foo6", {Path("def.rs")}),
        )

        # test usages in use2.rs
        usages = index.resolve_file_usages(parsed[2])
        check_usages(
            usages,
            HasUsage("Foo5", {Path("def.rs")}),
            HasUsage("Foo5", {Path("def.rs")}),
            HasUsage(".Variant51", {Path("def.rs")}),
            HasUsage("Foo5", {Path("def.rs")}),
            HasUsage(".Variant52", {Path("def.rs")}),
            HasUsage("Foo5", {Path("def.rs")}),
            HasUsage(".Variant53", {Path("def.rs")}),
        )


def test_python_referenced_names():
    shadow_example = textwrap.dedent(
        """
        def params(arg1, arg2):
            use(arg1)

        def assignment():
            use(not_shadowed)
            shadowed = 1
            use(shadowed)

        def with_example():
            with use("a") as with_var:
                use(with_var)
        """
    )
    check_refs(shadow_example, "python", {"use": 5, "not_shadowed": 1})

    # keyword args and __methods__ should not be considered
    keyword_example = textwrap.dedent(
        """
        from a import use, unused
        use(x, key=y).__str__()
        """
    )
    check_refs(
        keyword_example,
        "python",
        {"@a": 1, "@a/use": 2, "@a/unused": 1, "use": 2, "unused": 1, "x": 1, "y": 1},
    )

    import_and_usage_example = textwrap.dedent(
        """
        from e.f import g1, g2 as h2

        g1()
        h2()
        """
    )
    check_refs(
        import_and_usage_example,
        "python",
        {"@e/f": 1, "@e/f/g1": 2, "@e/f/g2": 2, "g1": 2, "g2": 2},
    )

    import_example1 = textwrap.dedent(
        """
        import a.b

        a.f1  # use f1
        a.b.f2  # use f2
        """
    )
    check_refs(
        import_example1,
        "python",
        {
            "@a/b": 2,
            "@a": 1,
            "f1": 1,
            "f2": 1,
        },
    )

    import_example2 = textwrap.dedent(
        """
        import a.b.c
        from e.f import g1, g2 as h2
        """
    )
    check_refs(
        import_example2,
        "python",
        {
            "@a/b/c": 1,
            "@e/f": 1,
            "@e/f/g1": 1,
            "@e/f/g2": 1,
            "g1": 1,
            "g2": 1,
        },
    )

    local_vars_example = textwrap.dedent(
        """
        for x in range(10):
            f(x)

        ys = {z for y in range(x) if z := y * y}

        (u, v) = f(x)
        """
    )

    check_refs(local_vars_example, "python", {"f": 2, "range": 2})


def test_deep_recursion():
    """Test that deeply nested calls are properly capped by depth."""
    rec_depth = 10_000
    rec_example = "f(" * rec_depth + "x" + ")" * rec_depth
    for lang, support in all_usage_supports.items():
        pfile = ParsedFile.parse(Path("test"), lang, rec_example)
        refs = LocalUsageAnalysis.from_pfile(pfile).name_usages
        for ref in refs:
            assert ref.name == "f" or ref.name == ".f"
        assert len(refs) <= 2 * support.max_rec_depth


def test_path_distance():
    assert path_distance(Path(), Path()) == 0
    assert path_distance(Path(), Path("a/b.py")) == 2
    assert path_distance(Path("/a/b/c.py"), Path("/a/b/c.py")) == 0
    assert path_distance(Path("/a/b/"), Path("/a/b/c.py")) == 1
    assert path_distance(Path("/a/b/d.py"), Path("/a/b/c.py")) == 2
    assert path_distance(Path("/a/b/c.py"), Path("/a/x/y.py")) == 4
    assert path_distance(Path("/a/b/c.py"), Path("/x/y.py")) == 5


class TestAttributeSupports:
    def test_python_attributes(self):
        python_example = textwrap.dedent(
            """\
            global1 = 5

            class A:
                def __init__(self):
                    self.field1 = 1
                    self.field2: str = 2

                def foo(self):
                    # doesn't count as attribute
                    self.temp = 1

                class Inner:
                    def __init__(self):
                        self.field3: int = 3

                field3: int
                field4 = 4

            global1: int = 6

            def f():
                local1 = 5
            """
        )
        pfile = ParsedFile.parse(Path("test.py"), "python", python_example)
        variables = [
            (a.name, a.type_annotation, a.rhs)
            for a in AllVariableSupports["python"].find_variables(pfile)
        ]

        # (name, type, rhs)
        assert variables == [
            ("global1", "", "5"),
            ("field1", "", "1"),
            ("field2", "str", "2"),
            ("field3", "int", "3"),
            ("field3", "int", ""),
            ("field4", "", "4"),
            ("global1", "int", "6"),
        ]


# TODO(AU-1350): Avoid failure by setting a maximum recursion depth.
@pytest.mark.xfail(
    raises=RecursionError, reason="Parser generates too big a tree to recurse through."
)
def test_deeply_nested_file():
    example = Path(_TEST_DATA_ROOT / "deep_recursion.cpp")
    parse_and_index("cpp", {Path("file1.cpp"): example.read_text()})


def test_wide_many_constants():
    def build_code(n: int) -> str:
        code = ""
        for i in range(n):
            code += f"\nx{i} = 42"
        return code

    index = parse_and_index("python", {Path("file1.py"): build_code(10000)})[1]
    assert len(index.symbol_map) == 1_000


def test_wide_many_functions():
    def build_fns(n: int) -> str:
        code = ""
        for i in range(n):
            code += f"\ndef fn{i}(): pass"
        return code

    index = parse_and_index("python", {Path("file1.py"): build_fns(10000)})[1]
    # The file doesn't have a symbol.
    assert len(index.symbol_map) == 999


def test_wide_many_methods():
    def build_fns(n: int) -> str:
        code = "class C:"
        for i in range(n):
            code += f"\n  def fn{i}(): pass"
        return code

    index = parse_and_index("python", {Path("file1.py"): build_fns(10000)})[1]
    # The file doesn't have a symbol.
    assert len(index.symbol_map) == 999


def test_wide_many_assigns():
    def build_fns(n: int) -> str:
        code = ""
        for i in range(n):
            code += f"\nimport x{i}"
        return code

    _PythonUsageSupport.max_num_nodes = 1000
    pfile = ParsedFile.parse(Path("file1.py"), "python", build_fns(2000))
    lua = LocalUsageAnalysis.from_pfile(pfile)
    assert len(lua.name_usages) <= 1000


@pytest.mark.parametrize(
    "i",
    [1, 2, 10, 100],
)
def test_golang_many_constants(i: int):
    def build_code(n: int) -> str:
        code = "const (\n"
        for i in range(n):
            code += f"  x{i} = 42\n"
        code += ")\n"
        return code

    pfile = ParsedFile.parse(Path("file1.go"), "go", build_code(i))
    ranges = [a.crange for a in AllVariableSupports["go"].find_variables(pfile)]
    assert len(ranges) == i
    # The ranges should not overlap.
    for x, y in itertools.pairwise(ranges):
        assert x.stop < y.start
