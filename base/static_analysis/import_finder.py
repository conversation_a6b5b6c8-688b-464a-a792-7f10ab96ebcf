"""Lightweight heuristics to find import lines in a file.

It works by looking for lines that start with certain language-specific keywords,
and any subsequent lines that are more indented are also considered to be part of the
import statement.
"""

import re
from typing import AbstractSet, Mapping, NamedTuple

from base.static_analysis.indentation_utils import compute_line_indentations
from base.languages.languages import LanguageId

GENERIC_KEYWORDS: AbstractSet[str] = {"import"}
KEYWORDS_BY_LANG: Mapping[LanguageId, AbstractSet[str]] = {
    "Astro": {
        "import",
        "@import",
    },  # Astro uses a similar syntax to JavaScript for components
    "C": {"#include"},  # C uses preprocessor directives for including files
    "C++": {"#include", "using", "import"},  # C++ includes and namespace declarations
    "CSharp": {"using"},  # C# uses using for namespaces and external modules
    "Cuda": {"#include"},  # Similar to C/C++ as it's based on C++
    "Go": {"import"},  # Go uses a straightforward import statement
    "HTML": set(),  # HTML does not have import statements in the traditional sense; uses <link>, <script>
    "Java": {"import"},  # Java uses the import keyword
    "JavaScript": {"import"},  # Modern JavaScript uses the import statement
    "JavaScript JSX": {"import"},  # Same as JavaScript
    "Jsonnet": {"import"},  # Jsonnet uses import for including libraries
    "Kotlin": {"import"},  # Kotlin uses import similar to Java
    "Lua": {"require"},  # Lua uses require to include modules
    "Markdown": set(),  # Markdown does not have import statements
    "PHP": {"require", "include"},  # PHP uses require and include
    "Perl": {"use", "require"},  # Perl uses use and require
    "Plain Text": set(),  # Plain text files do not support import statements
    "Protobuf": {"import"},  # Protocol buffers definition files use import
    "Python": {"import", "from"},  # Python's import statements
    "R": {
        "library",
        "require",
        "import",
    },  # R uses library or require to include packages
    "Racket": {
        "(require"
    },  # Racket uses require for modules. Note the leading parenthesis.
    "Ruby": {"require_relative", "require", "load"},  # Ruby uses require or load
    "Rust": {"use", "mod", "extern crate"},  # Rust uses use and extern crate
    "SQL": set(),  # SQL does not have a standard import mechanism
    "Scala": {"import"},  # Scala uses import similar to Java
    "Shell": {"source", "."},  # Shell scripts use source to include other scripts
    "Svelte": {"import"},  # Svelte components use import like in JavaScript
    "Swift": {"import"},  # Swift uses import to include modules
    "TypeScript": {"import"},  # TypeScript uses the same import syntax as JavaScript
    "TypeScript JSX": {"import"},  # Same as TypeScript
    "XML": set(),  # XML does not have import statements; uses schemas or XSLT includes
    "YAML": set(),  # YAML does not have import statements
    "JSON": set(),  # JSON files do not support import statements
    "Dart": {"import"},  # Dart uses import for libraries
    "CSS": {"@import"},  # CSS uses @import to include other stylesheets
    "Clojure": {
        "(require",
        "(:require",
        "(use",
        "(:use",
        "(import",
        "(:import",
    },  # Clojure uses require, use, or import for namespaces
    "Visual Basic": {"Imports"},  # Visual Basic uses Imports to include namespaces
    "TeX": {"\\usepackage"},  # TeX/LaTeX uses \usepackage for including packages
}


def find_all_import_lines(code: str, lang: LanguageId) -> list[int]:
    """Find all lines that are likely to be in an import statement."""

    class Ancestor(NamedTuple):
        line_number: int
        indent_level: int
        is_import: bool

    keywords = KEYWORDS_BY_LANG.get(lang, GENERIC_KEYWORDS)
    if not keywords:
        return []
    keyword_regex = _keywords_to_regex(keywords)
    lines = code.splitlines(keepends=True)
    indent_levels = compute_line_indentations(lines)
    stripped_lines = [line.strip() for line in lines]

    # We scan through the file from top to bottom, looking for lines that starts with
    # the import keywords. Any indented lines immediately following an import are also
    # considered as part of the import.

    import_lines = list[int]()
    ancestor_stack = list[Ancestor]()
    for i, line in enumerate(stripped_lines):
        # pop any ancestor that doesn't have a lower indentation
        while ancestor_stack and ancestor_stack[-1].indent_level >= indent_levels[i]:
            ancestor_stack.pop()

        ancestor_is_import = bool(ancestor_stack) and ancestor_stack[-1].is_import
        this_is_import = ancestor_is_import or bool(re.match(keyword_regex, line))
        if this_is_import:
            import_lines.append(i)

        # try to add the current line as the ancestor
        if not ancestor_stack or indent_levels[i] <= ancestor_stack[-1].indent_level:
            ancestor_stack.append(Ancestor(i, indent_levels[i], this_is_import))

    return import_lines


def _keywords_to_regex(keywords: AbstractSet[str]) -> str:
    """Build a regex that checks if a line begins with any of the given keywords."""
    cases = "|".join(re.escape(keyword) for keyword in keywords)
    return rf"^({cases})[^a-zA-Z\-_]"
