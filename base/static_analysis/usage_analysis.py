"""Utils to analyze (potential) usages within an entire project.

## The `UsageIndex` Class

The core of our analysis utilities is the `UsageIndex` class, which maintains a
mapping of names to definitions (`SymbolDefinition`) for efficient callee/usee lookups.

`index.resolve_file_usages` returns a `GlobalUsageAnalysis` object that contains
a mapping from each callsite to a list of potential definitions.  To support fast
updates, `UsageIndex` also has the `update_file` and `remove_file` methods.

See `research/notebooks/usage_analysis_tutorial.ipynb` for a comprehensive guide.
"""

from __future__ import annotations

import dataclasses
import pprint
from collections import defaultdict
from dataclasses import dataclass, field
from functools import cached_property
from pathlib import Path
from typing import Collection, Literal, Mapping, NamedTuple, Optional, Sequence

import tree_sitter as ts
from typing_extensions import assert_never

from base.ranges import CharRange
from base.static_analysis._usage_supports import (
    _AttrAccessDef,
    _DefSource,
    _ImportedDef,
    _ImportedModule,
    _LocalDef,
    all_usage_supports,
)
from base.static_analysis._variable_supports import AllVariableSupports
from base.static_analysis.common import LanguageID, decode_bytes, get_first, groupby
from base.static_analysis.parsing import (
    FileTypeNotSupportedError,
    ScopeKind,
    ScopeOrSpan,
    ScopeParsedFile,
    SrcScope,
    SrcSpan,
    _get_field_by_name,
    get_scope_by_range,
)
from base.static_analysis.parsing import _scope_tree_supports as scopers

# ---------------------------------------------------------------------------
# ------------------------------ Public API ---------------------------------

ModulePath = Path
"""The path of the module relative to the root, including file extension."""

Callsite = CharRange
"""Represent the source code range where a symbol is used."""

SymbolName = str
"""The name of a symbol usage.

The name follows the following syntax:
    - Global definitions' names are reported as is.
    - Class definitions' names are prefixed with a dot (`.`).
    - Module names are prefixed with an at sign (`@`).
"""

DefinitionKind = Literal["variable", "function", "class"]
"""The kind of a definition tracked by usage analysis."""

ParsedFile = ScopeParsedFile


class FileCharRange(NamedTuple):
    """A character range inside a file."""

    path: Path
    """The path of the file."""

    crange: CharRange
    """The character range in the file."""


class SymbolNameUsage(NamedTuple):
    """An (unresolved) symbol name usage."""

    name: SymbolName
    """The name of the symbol being used.

    The name follows the following syntax:
    - Global definitions' names are reported as is.
    - Class definitions' names are prefixed with a dot (`.`).
    - Module names are prefixed with an at sign (`@`).
    """

    use_site: CharRange
    """The character range of the use site."""

    kind: Literal["ref", "declare"] = "ref"
    """Whether this usage is from a name reference or name declaration."""

    def_range: Optional[FileCharRange] = None
    """The path and character range of the definition if we know it precisely.

    Note that this may be a subrange of the entire range of the definition,
    not the whole thing.
    """


class SymbolDefinitionId(NamedTuple):
    """Sufficient information to reference a symbol definition.

    Note that when a single statement defines multiple symbols, these multiple symbols
    will share the same `full_crange` but have different names.
    """

    id: str
    """Unique string identifier for this definition."""

    @staticmethod
    def build(
        path: Path, name: SymbolName, full_crange: CharRange
    ) -> SymbolDefinitionId:
        """Build a unique identifier for a symbol definition."""
        return SymbolDefinitionId(f"{name}@{path}:{full_crange}")


@dataclass(frozen=True)
class SymbolDefinition:
    """A named definition in the codebase, used for usage analysis.

    A definition can be either a variable, function, or class.
    """

    name: SymbolName
    """The name of the definition (function name, class name, etc.)."""

    path: Path
    """The path to the file in which this is defined."""

    full_crange: CharRange
    """The char range of the entire definition in its source file."""

    name_crange: CharRange
    """The char range of the name identifier in its source file."""

    prefix_crange: CharRange
    """The definition's prefix char range in its source file.
    This is currently not used but may become useful in the future for ahead-of-time
    inline lookups.
    """

    in_class: bool
    """Whether this definition is a class member.

    Note that if the parent definition cannot be resolved, it's possible for this to
    be True while parent_id being None.
    """

    parent_id: Optional[SymbolDefinitionId]
    """The syntactic or semantic parent of this definition.

    e.g., in Go, methods have their target class as their semantic parent.
    """

    kind: DefinitionKind
    """The type of this definition."""

    variable_summary: str
    """A short summary of a variable definition.

    This typically includes the variable name, visibility modifiers, type
    annotation, and default value, etc.
    """

    def __repr__(self) -> str:
        return f"{self.kind}[{self.name}]@{self.path}:{self.full_crange.start}"

    def __eq__(self, other: SymbolDefinition) -> bool:
        """We redefine equality to improve performance."""

        assert isinstance(other, SymbolDefinition)
        return (
            self.name == other.name
            and self.full_crange == other.full_crange
            and self.path == other.path
        )

    def __hash__(self):
        return hash((self.name, self.full_crange, self.path))

    @cached_property
    def id(self) -> SymbolDefinitionId:
        """Return a unique identifier for this definition."""
        return SymbolDefinitionId.build(self.path, self.name, self.full_crange)

    @cached_property
    def simple_name(self) -> str:
        """Return the name of this definition without any leading dot."""
        return self.name[1:] if self.in_class else self.name

    @staticmethod
    def get_definitions(pfile: ParsedFile) -> Sequence[SymbolDefinition]:
        """Return a sequence of `CodeBaseSymbol`s defined in this file.

        For now, this just relies on the scope tree implementation.
        But we can switch to more fine-grained symbol definitions in the future.
        """
        all_defs = list[SymbolDefinition]()
        # we keep this mapping to resolve the target class of a method (if needed)
        name_to_classes = defaultdict[str, list[SymbolDefinition]](list)
        # records the unresolved parent names (if any) for each definition
        unresolved_parents = dict[SymbolDefinitionId, str]()

        def add_scope_defs(scope: SrcScope, parent_id: SymbolDefinitionId | None):
            symbol_type: DefinitionKind | None = None
            if scope.kind == "function":
                symbol_type = "function"
            elif scope.kind == "class":
                symbol_type = "class"
            this_def = None
            if symbol_type is not None:
                in_class = parent_id is not None
                if scope.parent_name_override:
                    parent_id = None
                    in_class = True
                this_def = SymbolDefinition(
                    "." + scope.name if in_class else scope.name,
                    pfile.path,
                    full_crange=scope.range,
                    name_crange=scope.name_range,
                    prefix_crange=scope.prefix.range,
                    in_class=in_class,
                    parent_id=parent_id,
                    kind=symbol_type,
                    variable_summary="",
                )
                all_defs.append(this_def)
                if scope.parent_name_override:
                    unresolved_parents[this_def.id] = scope.parent_name_override
                if symbol_type == "class":
                    name_to_classes[scope.name].append(this_def)

            new_parent_id = this_def.id if this_def else parent_id
            for child in scope.children:
                if isinstance(child, SrcScope):
                    add_scope_defs(child, parent_id=new_parent_id)

        def get_parent_class_crange(crange: CharRange) -> CharRange | None:
            """Return the crange of the smallest class containing the given crange."""
            scopes = get_scope_by_range(pfile.scope_tree, crange)
            return get_first(s.range for s in scopes if s.kind == "class")

        def add_variable_defs():
            range_to_def = {d.full_crange: d for d in all_defs}
            support = AllVariableSupports.get(pfile.lang)
            if support is None:
                return
            all_vars = list(support.find_variables(pfile))
            grouped_vars = groupby(
                all_vars, lambda a: (a.name, get_parent_class_crange(a.crange))
            )
            for (name, parent_range), var_group in grouped_vars.items():
                # if there are multiple definitions
                # use the first definition with a type annotation
                var_group.sort(
                    key=lambda a: (a.type_annotation is None, a.crange.start)
                )
                var_def = var_group[0]
                if parent_range is not None:
                    parent_id = range_to_def[parent_range].id
                else:
                    parent_id = None

                this_def = SymbolDefinition(
                    "." + name if parent_id else name,
                    pfile.path,
                    full_crange=var_def.crange,
                    name_crange=var_def.name_crange,
                    prefix_crange=var_def.name_crange,
                    in_class=parent_id is not None,
                    parent_id=parent_id,
                    kind="variable",
                    variable_summary=support.get_variable_summary(var_def),
                )
                all_defs.append(this_def)

        def resolve_parent_ids(
            all_defs: list[SymbolDefinition],
        ) -> list[SymbolDefinition]:
            """Resolve parent ids for all definitions."""
            results = list[SymbolDefinition]()
            for def_ in all_defs:
                def_id = def_.id
                if parent_name := unresolved_parents.get(def_id):
                    parent_class_defs = name_to_classes.get(parent_name)
                    if parent_class_defs:
                        # Use the first matched class to break potential ties.
                        parent_id = parent_class_defs[0].id
                    else:
                        # If cannot resolve the parent class
                        parent_id = None
                    def_ = dataclasses.replace(def_, parent_id=parent_id)
                results.append(def_)
            return results

        add_scope_defs(pfile.scope_tree, parent_id=None)
        add_variable_defs()
        all_defs = resolve_parent_ids(all_defs)
        # sort definitions by their ranges
        all_defs.sort(key=lambda x: x.full_crange)
        return all_defs


@dataclass
class GlobalUsageAnalysis:
    """Holds the usage analysis result of a single source file."""

    site2defs: Mapping[SymbolNameUsage, Sequence[SymbolDefinition]]
    """Maps each use site to a sorted list of potential definitions."""

    site2modules: Mapping[SymbolNameUsage, Sequence[ModulePath]]
    """Maps each import site to a set of potential module usages."""

    reranker: BasicUsageReranker
    """The reranker used to score the definitions."""

    def name_to_defs(self) -> Mapping[SymbolName, Sequence[SymbolDefinition]]:
        """Return a mapping from used names to resolved definitions."""
        return {u.name: defs for u, defs in self.site2defs.items()}


@dataclass
class UsageIndex:
    """Holds indexed information about a codebase needed for usage analysis.

    Use `UsageIndex()` or `UsageIndex.from_files()` to build a new index and
    `index.resolve_file_usages` to analyze the usages within a given file. Usages to
    variables, functions, and classes are reported.

    When a file changes, call `index.update_file` or `index.remove_file` accordingly.

    The UsageIndex logic is langauge agnostic, but when there are multiple programming
    langauges used in a single project, we may want to build multiple usage indices,
    one for each group of languages that are used together (e.g., C and C++
    should probably share a single index to enable cross-language analysis, whereas
    JavaScript and TypeScript should probably have separate indices since such
    JavaScript files are ususally generated from TypeScript).
    """

    symbol_map: dict[str, set[SymbolDefinition]] = field(default_factory=dict)
    """Mapping from name to set of symbols with that name."""

    file2defs: dict[Path, set[SymbolDefinition]] = field(default_factory=dict)
    """Mapping from file to set of symbols defined in that file."""

    file2summaries: dict[Path, FileSummary] = field(default_factory=dict)
    """Mapping from file to FileSummary."""

    def __post_init__(self):
        # Map from file name (without extension) to set of files.
        # this is used for resolving module usages
        simp_name_to_files = groupby(
            self.file2defs.keys(), lambda x: x.with_suffix("").name
        )
        self._simp_name_to_files = {k: set(v) for k, v in simp_name_to_files.items()}

    def clear(self) -> None:
        """Remove all content of this index."""
        self.symbol_map.clear()
        self.file2defs.clear()
        self.file2summaries.clear()
        self._simp_name_to_files.clear()

    @staticmethod
    def from_files(
        pfiles: Collection[ParsedFile] | Collection[FileSummary],
    ) -> UsageIndex:
        """Build a UsageIndex from a collection of files."""

        index = UsageIndex()
        for pfile in pfiles:
            index.update_file(pfile)

        return index

    def update_file(self, file: ParsedFile | FileSummary) -> None:
        """Update the index with a new or modified file."""
        if isinstance(file, ParsedFile):
            file = FileSummary.from_pfile(file)

        if file.path in self.file2defs:
            self.remove_file(file.path)

        new_symbols = set(file.definitions)

        self.file2defs[file.path] = new_symbols
        self.file2summaries[file.path] = file
        for symb in new_symbols:
            self.symbol_map.setdefault(symb.name, set()).add(symb)

        simple_name = file.path.with_suffix("").name
        self._simp_name_to_files.setdefault(simple_name, set()).add(file.path)

    def remove_file(self, path: Path) -> None:
        """Remove the file with the given path from the index."""
        if path not in self.file2summaries:
            return
        self.file2summaries.pop(path)
        old_symbols = self.file2defs.pop(path)
        for symb in old_symbols:
            matched = self.symbol_map[symb.name]
            matched.remove(symb)
            if not matched:
                del self.symbol_map[symb.name]

        file_set = self._simp_name_to_files.get(path.with_suffix("").name, set())
        if path in file_set:
            file_set.remove(path)

    def resolve_def_name(
        self, name: SymbolName, reranker: BasicUsageReranker
    ) -> Sequence[SymbolDefinition]:
        """Resolve the symbol name to a ranked list of definitions, from high to low.

        Note: Most users should call `resolve_file_usages` instead.
        """

        symbols = self.symbol_map.get(name)
        if not symbols:
            return []
        if not name.startswith("."):
            same_file_symbols = {s for s in symbols if s.path == reranker.current_file}
            if same_file_symbols:
                # for non-class usages, always return the same file symbols
                symbols = same_file_symbols
        return sorted(symbols, key=reranker.score_def, reverse=True)

    def resolve_file_usages(
        self,
        file: ParsedFile | FileSummary,
        keep_unresolved: bool = False,
        report_declarations: bool = True,
    ) -> GlobalUsageAnalysis:
        """Resolve the usages of a file as sorted global usages.

        Args:
            file: The file to analyze.
            keep_unresolved: Whether unresolved local usages are kept in the result.
            report_declarations: Whether to report class member declarations as usages.
        """
        if isinstance(file, ParsedFile):
            file = FileSummary.from_pfile(file)

        def score_module(import_name: Sequence[str], candidate_name: Sequence[str]):
            """Score the candidate module name based on the import name."""
            matched = 0
            for x, y in zip(reversed(import_name), reversed(candidate_name)):
                if x == y:
                    matched += 1
                else:
                    break
            return matched

        def resolve_module(module_name: str) -> set[ModulePath]:
            """Resolve module name to a set of files (excluding current file)."""
            assert module_name.startswith("@")
            import_segs = module_name[1:].split("/")
            if not import_segs:
                return set()
            # any file whose name matches the last name seg will be considered
            candidates = tuple(self._simp_name_to_files.get(import_segs[-1], set()))
            if not candidates:
                return set()
            # TODO (Jiayi): remove the two ignores below once Pyright version is
            # updated in production
            scores = tuple(
                score_module(import_segs, path.with_suffix("").parts)  # type: ignore
                for path in candidates
            )
            best_score = max(scores)
            if len(import_segs) >= 2 and best_score < 2:
                return set()
            modules = {
                path
                for path, score in zip(candidates, scores)
                if score == best_score and path != file.path
            }
            return modules  # type: ignore

        def resolve_symbol(name: SymbolName) -> set[SymbolDefinition]:
            """Resolve symbol name to a set of definitions."""
            symbols = self.symbol_map.get(name)
            if not symbols:
                return set()
            return symbols

        def resolve_exact_usage(
            usage: SymbolNameUsage, symbols: list[SymbolDefinition]
        ) -> list[SymbolDefinition]:
            """Resolve the given set of symbols to be more exact for the given usage, if possible."""
            if usage.def_range:
                exact_symbols = [
                    s
                    for s in symbols
                    if s.path == usage.def_range.path
                    and s.full_crange.contains(usage.def_range.crange)
                ]
                if exact_symbols:
                    return exact_symbols
            return symbols

        declaration_usages = set[SymbolNameUsage]()
        if report_declarations:
            declaration_usages = {
                SymbolNameUsage(d.name, d.name_crange, kind="declare")
                for d in file.definitions
                # we only report declaration usages for class members to better
                # support inheritance usages
                if d.in_class
            }

        local_analysis = file.local_analysis
        all_usages = local_analysis.name_usages | declaration_usages
        all_usages = {u for u in all_usages if u.name}
        resolved_symbols = {
            name: list(resolve_symbol(name))
            for u in all_usages
            if not (name := u.name).startswith("@")
        }
        resolved_modules = {
            name: list(resolve_module(name))
            for u in all_usages
            if (name := u.name).startswith("@")
        }

        # we skip local usages when doing co-occurrence reranking
        usages_to_record = {
            u.name: resolved_symbols[u.name]
            for u in local_analysis.name_usages
            if u.def_range is None and u.name in resolved_symbols
        }
        reranker = BasicUsageReranker(file.path)
        reranker.record_context_bonus(
            resolved_names=usages_to_record,
            resolved_modules=resolved_modules,
        )

        for _, symbols in usages_to_record.items():
            # sort symbols by their scores
            symbols.sort(key=reranker.score_def, reverse=True)

        site2modules = dict[SymbolNameUsage, Sequence[ModulePath]]()
        site2defs = dict[SymbolNameUsage, Sequence[SymbolDefinition]]()
        # record usages by callsite locations
        for u in sorted(all_usages, key=lambda x: x[1]):
            if u.name.startswith("@"):
                modules = resolved_modules[u.name]
                if keep_unresolved or modules:
                    site2modules[u] = modules
            else:
                symbols = resolved_symbols[u.name]
                if keep_unresolved or symbols:
                    site2defs[u] = resolve_exact_usage(u, symbols)

        return GlobalUsageAnalysis(site2defs, site2modules, reranker)

    def pprint(self, **pprint_args):
        """Pretty print the index."""
        pprint.pprint(
            {
                "symbol_map": self.symbol_map,
            },
            sort_dicts=False,
            **pprint_args,
        )


@dataclass
class FileSummary:
    """Holds the local summary of a file."""

    lang: LanguageID
    """The language of the file."""

    path: Path
    """The path of the file."""

    size_chars: int
    """The number of characters in the file."""

    size_lines: int
    """The number of lines in the file."""

    definitions: Sequence[SymbolDefinition]
    """All definitions in the file."""

    local_analysis: LocalUsageAnalysis
    """Local usage analysis of the file."""

    scope_structure: ScopeTreeStructure
    """The structural scope tree of the file."""

    @staticmethod
    def from_pfile(pfile: ParsedFile) -> FileSummary:
        """Construct a FileSummary from a ParsedFile."""
        return FileSummary(
            lang=pfile.lang,
            path=pfile.path,
            size_chars=len(pfile.code),
            size_lines=len(pfile.code.splitlines()),
            definitions=SymbolDefinition.get_definitions(pfile),
            local_analysis=LocalUsageAnalysis.from_pfile(pfile),
            scope_structure=ScopeTreeStructure.from_srcscope(pfile.scope_tree),
        )


@dataclass(frozen=True)
class VarOccurrence:
    """Records all occurrences of a variable."""

    name: SymbolName
    """The name of the variable."""

    ranges: tuple[CharRange, ...]
    """The ranges of all occurrences, sorted by starting position."""


@dataclass(frozen=True)
class LocalUsageAnalysis:
    """Holds the result of local usage analysis of a file.

    Only references to global names are reported.
    """

    name_usages: set[SymbolNameUsage]
    """Stores all symbol name usages."""

    var_occurrences: set[VarOccurrence]
    """Stores all occurrences of each variable.

    This includes all local variables and same-class attribute accesses.
    Note that since this is currently only used for compute the tunneling cost in
    signature index, only variables with at least 2 occurrences are reported.
    """

    @staticmethod
    def from_pfile(pfile: ParsedFile) -> LocalUsageAnalysis:
        """Construct a LocalUsageAnalysis from a ParsedFile."""
        return local_usage_analysis(pfile)


_NodeId = tuple[int, int]
"""Identifies a node using its byte range."""


def get_node_id(node: ts.Node) -> _NodeId:
    return (node.start_byte, node.end_byte)


def local_usage_analysis(pfile: ParsedFile) -> LocalUsageAnalysis:
    """Perform local usage analysis on a given file.

    Note: currently, we only support analyzing the entire file.
    """
    if pfile.lang not in scopers:
        raise FileTypeNotSupportedError(f"Scoping support for {pfile.lang} is missing.")
    scoper = scopers[pfile.lang]
    if pfile.lang not in all_usage_supports:
        raise FileTypeNotSupportedError(
            f"Usage analysis for {pfile.lang} not supported."
        )
    support = all_usage_supports[pfile.lang]
    bmap = pfile.bmap
    current_path = pfile.path
    has_implicit_attr_access = support.has_implicit_attr_access

    def get_crange(node: ts.Node):
        start = bmap.byte_to_char(node.start_byte)
        stop = bmap.byte_to_char(node.end_byte)
        return CharRange(start, stop)

    def norm_module_path(module_name: str) -> Sequence[str]:
        """Convert the module name into path segments relative to project root."""
        parts = support.parse_module_path(module_name).parts
        if parts and parts[0] == "..":
            results = list(current_path.parts)
        else:
            results = list[str]()
        for part in parts:
            if part == "..":
                if results:
                    results.pop()
            else:
                results.append(part)
        return results

    # maps each local names to the scopes in which it was introduced
    locals2scopes: dict[str, dict[_NodeId, _DefSource]] = dict()
    # maps each scope to its kind
    scope_to_kind: dict[_NodeId, ScopeKind] = dict()
    # nodes that do not introduce any usages
    non_usage_nodes = set[_NodeId]()
    # the visible package namespaces
    namespace = _PackageNamespace((), {})
    # records all non-local references
    name_usages: set[SymbolNameUsage] = set()
    # maps (variable_name, def_scope) to its occurrences
    var_occurrence_map = dict[tuple[SymbolName, _NodeId], set[CharRange]]()

    root_node = pfile.ts_tree.root_node
    root_scope = get_node_id(root_node)
    max_depth = support.max_rec_depth
    max_num_nodes = support.max_num_nodes

    def record_local_names(node: ts.Node, scope: ts.Node, depth: int):
        """The first pass: record all local defs and their defined scopes and types."""
        nonlocal cur_num_nodes
        cur_num_nodes += 1
        if depth > max_depth or cur_num_nodes > max_num_nodes:
            return

        outer_scope = scope
        if scope_spec := scoper.scope_types.get(node.type):
            # record the scope kind
            scope_to_kind[get_node_id(node)] = scope_spec.kind
            # update the current scope
            scope = node

        for ldef in support.find_definitions(node):
            if isinstance(ldef, _LocalDef):
                if ldef.for_outer_scope:
                    scope_id = get_node_id(outer_scope)
                else:
                    scope_id = get_node_id(scope)
                # TODO: handles cases where class members can be defined outside of
                # the class scope. (e.g., Rust and Golang allows this type of defs)
                name = decode_bytes(ldef.name.text)
                if scope_to_kind.get(scope_id) == "class":
                    # If is being defined inside a class scope
                    if has_implicit_attr_access:
                        lookup_names = ("." + name, name)
                    else:
                        lookup_names = ("." + name,)
                else:
                    lookup_names = (name,)

                # unify the occurrence sets under all names
                occurrences = var_occurrence_map.setdefault(
                    (lookup_names[0], scope_id), set()
                )
                for lname in lookup_names[1:]:
                    other_set = var_occurrence_map.setdefault((lname, scope_id), set())
                    if other_set is not occurrences:
                        occurrences.update(other_set)
                        var_occurrence_map[(lname, scope_id)] = occurrences
                occurrences.add(get_crange(ldef.name))

                for lname in lookup_names:
                    # record the local definition
                    locals2scopes.setdefault(lname, dict())[scope_id] = ldef

                # avoid recording usages for this def site
                non_usage_nodes.add(get_node_id(ldef.name))
            elif isinstance(ldef, (_ImportedDef, _ImportedModule)):
                local_name = None
                if alias := ldef.alias:
                    local_name = alias
                elif isinstance(ldef, _ImportedDef):
                    local_name = decode_bytes(ldef.global_name.text)
                if local_name:
                    scopes = locals2scopes.setdefault(local_name, dict())
                    scopes[get_node_id(scope)] = ldef
                module_path = norm_module_path(decode_bytes(ldef.module_name.text))
                namespace.add_child(module_path)
                # add usages for the import statement since they will not be
                # resolved in the second pass
                if isinstance(ldef, _ImportedDef):
                    # if it's a definition
                    glob_name = decode_bytes(ldef.global_name.text)
                    name_usages.add(
                        SymbolNameUsage(glob_name, get_crange(ldef.global_name))
                    )
                    if ldef.can_be_module:
                        # if it's a module
                        mname = "@" + "/".join((*module_path, glob_name))
                        name_usages.add(
                            SymbolNameUsage(mname, get_crange(ldef.global_name))
                        )
                name_usages.add(
                    SymbolNameUsage(
                        "@" + "/".join(module_path), get_crange(ldef.module_name)
                    )
                )

        for n in support.find_non_usage_nodes(node):
            non_usage_nodes.add(get_node_id(n))

        for c in node.named_children:
            record_local_names(c, scope, depth + 1)

    class _MaybePackage:
        """Denote a potentially unknown package.

        This is used as the return type of `record_usages` in addition to
        `_PackageNamespace` since we uses different logic to handle the two cases.
        When the result is `_PackageNamespace`, we know for sure that it's resolving
        to a package and can hence more precisely tracking subpackage usages. When
        the result is `_MaybePackage`, we don't know for sure that it's resolving to a
        package and hence we need to use more conservative logic.
        """

    class _ThisKeyword:
        """Denote the `this` keyword."""

    def record_usages(
        node: ts.Node, parent_scopes: tuple[_NodeId, ...], depth: int
    ) -> _ThisKeyword | _PackageNamespace | _MaybePackage | _DefSource | None:
        """The second pass: resolve and record usages.

        returns what the node resolves to, one of the following
        - `_PackageNamespace`: A known package
        - `_MaybePackage`: An unknown package
        - `_DefSource`: A local definition, imported module, or imported definition
        - `None`: something else (like an expression)
        """

        def record_identifier_usage(
            node: ts.Node,
        ) -> _ThisKeyword | _PackageNamespace | _MaybePackage | _DefSource:
            used_name = decode_bytes(node.text)
            if used_name in support.this_keywords:
                return _ThisKeyword()
            # find the newest scope in which the name is defined
            if (def_scopes := locals2scopes.get(used_name)) and (
                def_scope := next(
                    (ps for ps in parent_scopes if ps in def_scopes), None
                )
            ):
                ldef = def_scopes[def_scope]
                if isinstance(ldef, _ImportedDef):
                    # if it's a definition
                    global_name = decode_bytes(ldef.global_name.text)
                    name_usages.add(SymbolNameUsage(global_name, get_crange(node)))
                    if ldef.can_be_module:
                        # if it's a module
                        module_path = norm_module_path(
                            decode_bytes(ldef.module_name.text)
                        )
                        mname = "@" + "/".join((*module_path, global_name))
                        name_usages.add(SymbolNameUsage(mname, get_crange(node)))
                elif isinstance(ldef, _ImportedModule):
                    module_name = "/".join(
                        norm_module_path(decode_bytes(ldef.module_name.text))
                    )
                    name_usages.add(
                        SymbolNameUsage("@" + module_name, get_crange(node))
                    )
                elif isinstance(ldef, _LocalDef):
                    report_name = None
                    if def_scope == root_scope:
                        report_name = decode_bytes(ldef.name.text)
                    elif scope_to_kind.get(def_scope) == "class":
                        report_name = "." + decode_bytes(ldef.name.text)
                    if report_name:
                        name_usages.add(
                            SymbolNameUsage(
                                report_name,
                                get_crange(node),
                                def_range=FileCharRange(
                                    pfile.path, get_crange(ldef.name)
                                ),
                            )
                        )
                    var_occurrence_map.get((used_name, def_scope), set()).add(
                        get_crange(node)
                    )
                else:
                    assert_never(ldef)
                return ldef
            # check if the name is an imported package name
            elif ns := namespace.children.get(used_name):
                return ns
            else:
                # otherwise, assume is a global name reference
                name_usages.add(SymbolNameUsage(used_name, get_crange(node)))
                return _MaybePackage()

        def record_access_usage(node: ts.Node, access_def: _AttrAccessDef, depth: int):
            # record the receiver usages (The LHS of the dot)
            receiver_src = None
            if (receiver_field_name := access_def.receiver) is not None:
                if receiver_node := _get_field_by_name(node, receiver_field_name):
                    receiver_src = record_usages(
                        receiver_node, parent_scopes, depth + 1
                    )

            attr_node = _get_field_by_name(node, access_def.attribute)
            if not attr_node or get_node_id(attr_node) in non_usage_nodes:
                # return if somehow the access (RHS of the dot) is missing
                return

            attr = decode_bytes(attr_node.text)
            if isinstance(receiver_src, _ThisKeyword):
                dot_name = "." + attr
                # get the containing class
                class_scope = next(
                    (ps for ps in parent_scopes if scope_to_kind.get(ps) == "class"),
                    None,
                )
                def_range = None
                if class_scope:
                    occurrences = var_occurrence_map.setdefault(
                        (dot_name, class_scope), set()
                    )
                    occurrences.add(get_crange(attr_node))
                    if (
                        ldef := locals2scopes.get(dot_name, {}).get(class_scope)
                    ) and isinstance(ldef, _LocalDef):
                        # report a local usage to the matching class member
                        def_range = FileCharRange(pfile.path, get_crange(ldef.name))
                name_usages.add(
                    SymbolNameUsage(
                        dot_name, get_crange(attr_node), def_range=def_range
                    )
                )
            elif isinstance(receiver_src, _PackageNamespace):
                # if access leads to a subspace, don't generate any usages
                if subpackage := receiver_src.children.get(attr):
                    return subpackage
                # generate a module usage for receiver and a global usage for attribute
                name_usages.add(
                    SymbolNameUsage(
                        "@" + "/".join(receiver_src.full_name),
                        get_crange(attr_node),
                    )
                )
                name_usages.add(SymbolNameUsage(attr, get_crange(attr_node)))
            elif isinstance(receiver_src, _ImportedModule):
                name_usages.add(SymbolNameUsage(attr, get_crange(attr_node)))
                return _MaybePackage()
            elif isinstance(receiver_src, _ImportedDef):
                if receiver_src.can_be_module:
                    name_usages.add(SymbolNameUsage(attr, get_crange(attr_node)))
                name_usages.add(SymbolNameUsage("." + attr, get_crange(attr_node)))
                return _MaybePackage()
            elif isinstance(receiver_src, _MaybePackage):
                name_usages.add(SymbolNameUsage(attr, get_crange(attr_node)))
                name_usages.add(SymbolNameUsage("." + attr, get_crange(attr_node)))
                return _MaybePackage()
            elif isinstance(receiver_src, _LocalDef) or receiver_src is None:
                name_usages.add(SymbolNameUsage("." + attr, get_crange(attr_node)))
            else:
                assert_never(receiver_src)

        nonlocal cur_num_nodes
        cur_num_nodes += 1
        if cur_num_nodes > max_num_nodes:
            return
        if get_node_id(node) in non_usage_nodes:
            return
        elif node.type in support.usage_identifier_types:
            return record_identifier_usage(node)
        elif access_def := support.attr_access_defs.get(node.type):
            return record_access_usage(node, access_def, depth)
        elif depth <= max_depth:
            if node.type in scoper.scope_types:
                # update scope for children
                parent_scopes = (get_node_id(node), *parent_scopes)
            for child in node.named_children:
                record_usages(child, parent_scopes, depth + 1)

    cur_num_nodes = 0
    record_local_names(root_node, root_node, 0)
    cur_num_nodes = 0
    record_usages(root_node, (), 0)

    var_occurrences = {
        VarOccurrence(name, tuple(sorted(ranges)))
        for (name, _), ranges in var_occurrence_map.items()
        if len(ranges) > 1
    }

    return LocalUsageAnalysis(name_usages, var_occurrences)


@dataclass
class BasicUsageReranker:
    """A reranker based on basic co-occurrence heuristics."""

    current_file: Path
    """The path of the current file being analyzed."""

    file_bonuses: defaultdict[Path, float] = field(
        default_factory=lambda: defaultdict(float)
    )
    """Stores the relevance of each file to the current file."""

    def_bonuses: defaultdict[SymbolDefinitionId, float] = field(
        default_factory=lambda: defaultdict(float)
    )
    """Stores the relevance of each definition to the current file."""

    modules_recorded: set[ModulePath] = field(default_factory=set)
    """Stores the modules that have been recorded."""

    def __post_init__(self):
        assert isinstance(self.file_bonuses, defaultdict), f"{type(self.file_bonuses)=}"
        assert isinstance(self.def_bonuses, defaultdict), f"{type(self.def_bonuses)=}"
        self._path_dis_cache: dict[Path, int] = {}

    def record_context_bonus(
        self,
        resolved_names: Mapping[str, Collection[SymbolDefinition]],
        resolved_modules: Mapping[str, Collection[ModulePath]],
    ):
        """Record the bonuses for the usages in the current context."""
        for _, defs in resolved_names.items():
            self.record_def_bonus(defs)
        for _, modules in resolved_modules.items():
            self._record_module_bonus(modules)

    def score_def(self, d: SymbolDefinition) -> float:
        """Return how relevant a def is to the current context."""
        def_path = d.path
        path_dis = self._path_dis_cache.get(def_path)
        if path_dis is None:
            path_dis = path_distance(def_path, self.current_file)
            self._path_dis_cache[def_path] = path_dis
        bonus = self.file_bonuses[def_path] + self.def_bonuses[d.id]
        if pid := d.parent_id:
            bonus += self.def_bonuses[pid] / 2
        return bonus - path_dis

    def record_def_bonus(self, defs: Collection[SymbolDefinition]):
        """Given all defs for a single call site, record the file usages."""
        if not defs:
            return
        score = 5 / len(defs)
        for d in defs:
            self.file_bonuses[d.path] += score / 2
            self.def_bonuses[d.id] += score
            if pid := d.parent_id:
                self.def_bonuses[pid] += score / 2

    def _record_module_bonus(self, modules: Collection[ModulePath]):
        """Given all modules for a single call site, record the module usages."""
        if not modules:
            return
        score = 10 / len(modules)
        for m in modules:
            if m not in self.modules_recorded:
                self.file_bonuses[m] += score
                self.modules_recorded.add(m)


def path_distance(path1: Path, path2: Path) -> int:
    """Return the structural distance between two file paths."""
    # NOTE(jiayi): We don't use `Path.relative_to` here since it doesn't work when
    # one path is not a subpath of another. We don't use `os.path.relpath` since it
    # calls `os.getcwd()`.
    if path1 == path2:
        return 0
    parts1 = path1.parts
    parts2 = path2.parts
    n_common = 0
    for a, b in zip(parts1, parts2):
        if a == b:
            n_common += 1
        else:
            break
    left1 = len(parts1) - n_common
    left2 = len(parts2) - n_common
    return left1 + left2


@dataclass
class _PackageNamespace:
    """Store the sub-package structure."""

    full_name: tuple[str, ...]
    """The full package name as a sequence of segments."""

    children: dict[str, _PackageNamespace]
    """The subnamespaces."""

    def add_child(self, name_chain: Sequence[str]) -> None:
        current = self
        for name in name_chain:
            if name not in current.children:
                full_name = current.full_name + (name,)
                current.children[name] = _PackageNamespace(full_name, {})
            current = current.children[name]


@dataclass(frozen=True)
class ScopeTreeStructure:
    """A scope tree that only contains ranges.

    This is simply the `range` member of the corresponding `SrcScope`.
    """

    range: CharRange
    """The byte range of this scope."""

    children: Sequence[ScopeTreeStructure]
    """The children of this scope."""

    @staticmethod
    def from_srcscope(tree: ScopeOrSpan) -> ScopeTreeStructure:
        if isinstance(tree, SrcScope):
            return ScopeTreeStructure(
                range=tree.range,
                children=list(map(ScopeTreeStructure.from_srcscope, tree.children)),
            )
        elif isinstance(tree, SrcSpan):
            return ScopeTreeStructure(range=tree.range, children=[])
