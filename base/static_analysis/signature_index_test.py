"""Tests for signature index."""

from pathlib import Path
from typing import Sequence

import pytest

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.common import LanguageID, assert_eq
from base.static_analysis.signature_index import (
    FileSummaryWithSignatures,
    SignatureIndex,
    SignatureIndexProtocol,
    _get_tree_distance_to_cursor,
    _occurrence_based_site_distances,
)
from base.static_analysis.signature_utils import SignaturePrinter
from base.static_analysis.usage_analysis import (
    FileSummary,
    LocalUsageAnalysis,
    ParsedFile,
    SymbolNameUsage,
)
from base.test_utils.testing_utils import error_context

_TEST_DATA_ROOT = Path(__file__).parent / "testdata"
_TEST_DATA = {path.name: path for path in _TEST_DATA_ROOT.glob("*.py")}


@pytest.fixture
def summaries() -> dict[str, FileSummaryWithSignatures]:
    return {
        path: FileSummaryWithSignatures.from_pfile(
            ParsedFile.parse(Path(path), "python", file_path.read_text()),
            SignaturePrinter(),
        )
        for path, file_path in _TEST_DATA.items()
    }


@pytest.fixture
def index(summaries: dict[str, FileSummaryWithSignatures]) -> SignatureIndexProtocol:
    index = SignatureIndex()
    for summary in summaries.values():
        index.add_file(summary)
    return index


def test_supported_langauges():
    """Basic tests to check that signature index works with all supported languages."""

    examples: dict[LanguageID, dict[str, str]] = {
        "python": {
            "foo.py": "def foo(): bar()",
            "bar.py": "def bar(): foo()",
        },
        "java": {
            "foo.java": "class foo { public static void foo() { bar.bar() } }",
            "bar.java": "class bar { public static void bar() { foo.foo() } }",
        },
        "typescript": {
            "foo.ts": "function foo() { bar() }",
            "bar.ts": "function bar() { foo() }",
        },
        "javascript": {
            "foo.js": "function foo() { bar() }",
            "bar.js": "function bar() { foo() }",
        },
        "go": {"foo.go": "func foo() { bar() }", "bar.go": "func bar() { foo() }"},
        "cpp": {"foo.cpp": "void foo() { bar() }", "bar.cpp": "void bar() { foo() }"},
        "rust": {"foo.rs": "fn foo() { bar() }", "bar.rs": "fn bar() { foo() }"},
    }

    index = SignatureIndex(verbose=True)
    sig_printer = SignaturePrinter()
    summary_map = dict[Path, FileSummary]()
    # Only check the subset of languages that are supported by signature index
    for lang in index.supported_langs:
        for path, code in examples[lang].items():
            path = Path(path)
            fs = FileSummaryWithSignatures.from_pfile(
                ParsedFile.parse(path, lang, code),
                sig_printer,
            )
            index.update_file(fs)
            summary_map[path] = fs.summary

    # check that the index is not empty
    assert index.symbol_counts()

    for path, summary in summary_map.items():
        result = index.get_context_signatures(
            summary, cursor_location=0, prompt_range=CharRange(0, summary.size_chars)
        )
        # the context usages should be nonempty
        assert result.ctx_signatures

        # inline queries should only return signatures from other files
        with error_context(f"foo inline query, {path=}"):
            foo_sigs, q_state = index.inline_signature_query("foo", result.state)
            assert len(foo_sigs) == int(not path.name.startswith("foo"))

        with error_context(f"bar inline query, {path=}"):
            bar_sigs, q_state = index.inline_signature_query("bar", result.state)
            assert len(bar_sigs) == int(not path.name.startswith("bar"))

    # now remove all the indexed files
    for path, summary in summary_map.items():
        index.remove_file(path)

    # check that the index is now empty
    assert not index.symbol_counts()
    assert not index.size_files()

    # also test internal correctness below
    assert not index._language_map  # pylint: disable=protected-access
    assert not index._id_to_signature  # pylint: disable=protected-access
    assert not index._file_signatures_map  # pylint: disable=protected-access
    assert not index._module_signature_map  # pylint: disable=protected-access

    # remove a non-existing file should return False
    assert not index.remove_file(Path("not_existing.py"))


def build_signature_index(name2file: dict[str, str], lang: LanguageID = "python"):
    """Build a signature index from a set of files."""
    index = SignatureIndex(verbose=True)
    summary_map = dict[str, FileSummary]()
    for path, code in name2file.items():
        fs = FileSummaryWithSignatures.from_pfile(
            ParsedFile.parse(Path(path), lang, code),
            SignaturePrinter(),
        )
        index.update_file(fs)
        summary_map[path] = fs.summary
    return index, summary_map


# Test the public interface for a signature index language.
# NOTE(arun): We're taking some shortcuts to test the signatures using their ranges or
# the text. A more elegant design would be to implement a `SignatureMatcher` class that
# specifies if we are matching a module signature or a definition signature.


@pytest.mark.parametrize(
    "target_path, expected_signature_ranges",
    [
        (
            "sort_people_test.py",
            [
                # Return the module signatures for the imported files.
                (Path("sort_people.py"), CharRange(0, 391)),
                (Path("people.py"), CharRange(0, 174)),
            ],
        ),
    ],
)
def test_get_context_signatures(
    summaries: dict[str, FileSummaryWithSignatures],
    index: SignatureIndex,
    target_path: str,
    expected_signature_ranges: list[tuple[Path, CharRange]],
):
    """Tests .get_context_signatures() on examples from the test data directory."""
    # For now, this test only support Python.
    # Get local results.
    target_summary = summaries[target_path]
    result = index.get_context_signatures(
        target_summary.summary,
        # Cursor at end of file.
        target_summary.summary.size_chars,
        prompt_range=CharRange(0, target_summary.summary.size_chars),
    )
    signature_ranges = [
        (sig.path, sig.crange)
        for sigs in result.ctx_signatures.values()
        for sig in sigs
    ]
    assert signature_ranges == expected_signature_ranges


def test_get_same_file_signatures():
    """Test that we can retrieve the signatures defined in the same file."""
    test_code = """\
Alias = Literal["a", "b"]

class Foo:
    pass

def f(x):
    return x

def usage(x: Alias):
    return f(Foo(x))
"""
    index, summaries = build_signature_index({"test.py": test_code})
    result = index.get_context_signatures(
        summaries["test.py"],
        # Cursor at end of file.
        summaries["test.py"].size_chars,
        # use an empty prompt range so we don't drop any same-file signatures
        prompt_range=CharRange.point(len(test_code)),
    )
    actual_names = [u.name for u in result.ctx_signatures.keys()]
    assert actual_names == ["usage", "@test", "Foo", "f", "Alias"]


# The relevant portion of the scope tree is:
# file(name='distance.py', prefix='', docstr='', suffix='') @ 0:388
#     class(name='SomeClass', prefix='class SomeClass(SomeParentClass):\n', docstr='', suffix='') @ 161:388
#         span(code='  field1: F1Type\n  field2: F2Type\n\n') @ 195:230
#         function(name='method1', prefix='  def method1(self):\n', docstr='', suffix='\n') @ 230:272
#             span(code='    self.methodp0()\n') @ 251:271
#         function(name='method2', prefix='  def method2(self):\n', docstr='', suffix='\n') @ 272:314
#             span(code='    self.methodp1()\n') @ 293:313
#         function(name='method_n', prefix='  def method_n(self):\n', docstr='', suffix='') @ 314:388
#             span(code='    self.methodp2()\n    # cursor below:\n\n    return\n') @ 336:388
@pytest.mark.parametrize(
    "target_path, expected_signature_use_ranges",
    [
        (
            "distance.py",
            [
                CharRange(376, 376),  # Cursor (line 28)
                CharRange(345, 353),  # methodp2 (line 26)
                CharRange(177, 192),  # SomeParentClass (line 15)
                CharRange(222, 228),  # F2Type (line 17)
                CharRange(205, 211),  # F1Type (line 16)
                CharRange(302, 310),  # methodp1 (line 23)
                CharRange(260, 268),  # methodp0 (line 20)
            ],
        ),
    ],
)
def test_get_context_signatures_tree_distance_no_budgets(
    summaries: dict[str, FileSummaryWithSignatures],
    index: SignatureIndex,
    target_path: str,
    expected_signature_use_ranges: list[CharRange],
):
    """Tests .get_context_signatures() on examples from the test data directory."""
    # For now, this test only support Python.
    # We want to test the tree distances, so enable them.
    index.usage_distance_metric = "tree"
    # Get local results.
    target_summary = summaries[target_path]
    ctx_signatures = index.get_context_signatures(
        target_summary.summary,
        # The cursor is just below the relevant comment in the test.
        376,
        # Set an empty prompt so we get everything in the context.
        prompt_range=CharRange(376, 376),
    ).ctx_signatures
    # Remove duplicates from the result, as multiple usages have the same range.
    signature_use_ranges = list(
        dict.fromkeys(sig.use_site for sig in ctx_signatures.keys())
    )
    assert signature_use_ranges == expected_signature_use_ranges


# The relevant portion of the scope tree is:
# file(name='distance.py', prefix='', docstr='', suffix='') @ 0:388
#     class(name='SomeClass', prefix='class SomeClass(SomeParentClass):\n', docstr='', suffix='') @ 161:388
#         span(code='  field1: F1Type\n  field2: F2Type\n\n') @ 195:230
#         function(name='method1', prefix='  def method1(self):\n', docstr='', suffix='\n') @ 230:272
#             span(code='    self.methodp0()\n') @ 251:271
#         function(name='method2', prefix='  def method2(self):\n', docstr='', suffix='\n') @ 272:314
#             span(code='    self.methodp1()\n') @ 293:313
#         function(name='method_n', prefix='  def method_n(self):\n', docstr='', suffix='') @ 314:388
#             span(code='    self.methodp2()\n    # cursor below:\n\n    return\n') @ 336:388
@pytest.mark.parametrize(
    "target_path, expected_signature_use_ranges",
    [
        (
            "distance.py",
            [
                CharRange(376, 376),  # Cursor (line 28)
                CharRange(345, 353),  # methodp2 (line 26)
                CharRange(302, 310),  # methodp1 (line 23)
                CharRange(177, 192),  # SomeParentClass (line 15)
                CharRange(222, 228),  # F2Type (line 17)
                CharRange(205, 211),  # F1Type (line 16)
                CharRange(260, 268),  # methodp0 (line 20)
            ],
        ),
    ],
)
def test_get_context_signatures_tree_distance_with_budgets(
    summaries: dict[str, FileSummaryWithSignatures],
    index: SignatureIndex,
    target_path: str,
    expected_signature_use_ranges: list[CharRange],
):
    """Tests .get_context_signatures() on examples from the test data directory."""
    # For now, this test only support Python.
    # We want to test the tree distances, so enable them.
    index.usage_distance_metric = "tree"
    # Get local results.
    target_summary = summaries[target_path]
    ctx_signatures = index.get_context_signatures(
        target_summary.summary,
        # The cursor is just below the relevant comment in the test.
        376,
        # Set the prefix to be 100 characters so some things fit inside it.
        prompt_range=CharRange(376 - 100, 376),
    ).ctx_signatures
    # Remove duplicates from the result, as multiple usages have the same range.
    signature_use_ranges = list(
        dict.fromkeys(sig.use_site for sig in ctx_signatures.keys())
    )
    assert signature_use_ranges == expected_signature_use_ranges


@pytest.mark.parametrize(
    "target_path, signature_tokens, expected_skipped",
    [
        ("sort_people_test.py", 1000, []),
        (
            "sort_people_test.py",
            1,
            [
                (
                    "@sort_people",
                    [
                        "In file: sort_people.py\nFUNCTION: sort_by_age, test_sort_by_age"
                    ],
                ),
                ("@people", ["In file: people.py\nCLASS: Person"]),
            ],
        ),
    ],
)
def test_get_context_signatures_metrics(
    summaries: dict[str, FileSummaryWithSignatures],
    index: SignatureIndex,
    target_path: str,
    signature_tokens: int,
    expected_skipped: Sequence[tuple[str, Sequence[str]]],
):
    """Tests .get_context_signatures()'s metrics on examples from the test data directory."""
    # For now, this test only support Python.
    # Get local results.
    index.max_ctx_signature_chars = signature_tokens
    target_summary = summaries[target_path]
    result = index.get_context_signatures(
        target_summary.summary,
        # Cursor at end of file.
        target_summary.summary.size_chars,
        prompt_range=CharRange(0, target_summary.summary.size_chars),
    )
    assert result.metrics.skipped_usages == expected_skipped


@pytest.mark.parametrize(
    "target_path, symbol_name, expected_signature_ranges",
    [
        (
            "sort_people_test.py",
            "sort_by_age",
            [
                # Return the module signatures for the imported files.
                (Path("sort_people.py"), CharRange(98, 162)),
            ],
        ),
        ("sort_people_test.py", "undefined_symbol", []),
    ],
)
def test_inline_signature_query(
    summaries: dict[str, FileSummaryWithSignatures],
    index: SignatureIndex,
    target_path: str,
    symbol_name: str,
    expected_signature_ranges: list[tuple[Path, CharRange]],
):
    """Tests .inline_signature_query() on examples from the test data directory."""
    # For now, this test only support Python.
    # Get local results.
    target_summary = summaries[target_path]
    result = index.get_context_signatures(
        target_summary.summary,
        # Cursor at end of file.
        target_summary.summary.size_chars,
        prompt_range=CharRange(0, target_summary.summary.size_chars),
    )
    sigs, _ = index.inline_signature_query(
        symbol_name,
        result.state,
    )
    signature_ranges = [(sig.path, sig.crange) for sig in sigs]
    assert signature_ranges == expected_signature_ranges


@pytest.mark.parametrize(
    "target_path, max_ctx_signature_chars, expected_signature_ranges",
    [
        (
            "sort_people_test.py",
            40,
            [
                (Path("people.py"), CharRange(0, 174)),
            ],
        ),
        (
            "sort_people_test.py",
            70,
            [
                (Path("sort_people.py"), CharRange(0, 391)),
            ],
        ),
        (
            "sort_people_test.py",
            1000,
            [
                (Path("sort_people.py"), CharRange(0, 391)),
                (Path("people.py"), CharRange(0, 174)),
            ],
        ),
    ],
)
def test_long_signature(
    summaries: dict[str, FileSummaryWithSignatures],
    index: SignatureIndex,
    target_path: str,
    max_ctx_signature_chars: int,
    expected_signature_ranges: list[tuple[Path, CharRange]],
):
    """Tests .get_context_signatures() on examples from the test data directory."""
    # For now, this test only support Python.
    index.max_ctx_signature_chars = max_ctx_signature_chars
    # Get local results.
    target_summary = summaries[target_path]
    result = index.get_context_signatures(
        target_summary.summary,
        # Cursor at end of file.
        target_summary.summary.size_chars,
        prompt_range=CharRange(0, target_summary.summary.size_chars),
    )
    signature_ranges = [
        (sig.path, sig.crange)
        for sigs in result.ctx_signatures.values()
        for sig in sigs
    ]
    assert signature_ranges == expected_signature_ranges


def test_occurrence_based_site_distances():
    """Tests the occurrence-based site distances correctness."""
    example_code = """\
counter: Counter = 1
foo(counter)

def main(result, y):
    counter.add(y + 1)
    y.other_method1()
    y.other_method2()
    return counter
"""

    sites = [
        SymbolNameUsage("Counter", CharRange(9, 16)),
        SymbolNameUsage("foo", CharRange(21, 24)),
        SymbolNameUsage(".add", CharRange(68, 71)),
        SymbolNameUsage(".other_method1", CharRange(85, 98)),
        SymbolNameUsage(".other_method2", CharRange(107, 120)),
    ]

    cursor_loc = len(example_code)
    local_analysis = LocalUsageAnalysis.from_pfile(
        ParsedFile.parse(Path("test"), "python", example_code)
    )
    distances = _occurrence_based_site_distances(sites, cursor_loc, local_analysis)
    sorted_names = [
        u.name for u in sorted(distances.keys(), key=lambda x: distances[x])
    ]
    # usages near `counter` occurrences should be reported first
    assert_eq(
        sorted_names,
        [
            ".add",
            "foo",
            "Counter",
            ".other_method2",
            ".other_method1",
        ],
    )


# The relevant portion of the scope tree is:
# file(name='distance.py', prefix='', docstr='', suffix='') @ 0:388
#     class(name='SomeClass', prefix='class SomeClass(SomeParentClass):\n', docstr='', suffix='') @ 161:388
#         span(code='  field1: F1Type\n  field2: F2Type\n\n') @ 195:230
#         function(name='method1', prefix='  def method1(self):\n', docstr='', suffix='\n') @ 230:272
#             span(code='    self.methodp0()\n') @ 251:271
#         function(name='method2', prefix='  def method2(self):\n', docstr='', suffix='\n') @ 272:314
#             span(code='    self.methodp1()\n') @ 293:313
#         function(name='method_n', prefix='  def method_n(self):\n', docstr='', suffix='') @ 314:388
#             span(code='    self.methodp2()\n    # cursor below:\n\n    return\n') @ 336:388
@pytest.mark.parametrize(
    "target_path, range1, range2, expected_distance",
    [
        (
            "distance.py",
            CharRange(205, 211),  # F1Type (line 16)
            CharRange(222, 228),  # F2Type (line 17)
            0,
        ),
        (
            "distance.py",
            CharRange(205, 211),  # F1Type (line 16)
            CharRange(177, 192),  # SomeParentClass (line 15)
            1,
        ),
        (
            "distance.py",
            CharRange(345, 353),  # methodp2 (line 26)
            CharRange(205, 211),  # F1Type (line 16)
            3,
        ),
    ],
)
def test_tree_distance(
    summaries: dict[str, FileSummaryWithSignatures],
    target_path: str,
    range1: CharRange,
    range2: CharRange,
    expected_distance: int,
):
    """Unit test for get_tree_distance_to_cursor()."""
    target_summary = summaries[target_path]
    scope_tree = target_summary.summary.scope_structure
    assert (
        _get_tree_distance_to_cursor(
            scope_tree,
            range1,
            range2,
        )
        == expected_distance
    )
