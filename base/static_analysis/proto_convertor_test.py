"""Unit tests for proto convertor."""

from pathlib import PosixPath

import pytest

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, IntRange
from base.static_analysis import signature_pb2 as proto_module  # type: ignore
from base.static_analysis.proto_convertor import (
    ProtoConvertor,
    ProtoConvertorError,
    ProtoConvertorGroup,
    RecursiveConvertorSpec,
)
from base.static_analysis.usage_analysis import (
    FileCharRange,
    LocalUsageAnalysis,
    SymbolNameUsage,
)


class PathConvertor(ProtoConvertor[PosixPath]):
    """Protobuf convertor for PosixPath."""

    py_class = PosixPath
    proto_class = proto_module.Path

    def to_proto(self, obj: PosixPath):
        return proto_module.Path(value=str(obj))

    def from_proto(self, pb) -> PosixPath:
        return PosixPath(pb.value)


@pytest.mark.parametrize(
    "basic_value",
    [
        1,
        1.2,
        "abc",
        None,
        [1, 2, 3],
        [1, "a", None],
    ],
)
def test_basic_proto_conversion(basic_value):
    """Test that the proto group is able to convert to and from some basic types."""
    group = ProtoConvertorGroup(None)  # don't need a proto module here
    assert group.from_proto(group.to_proto(basic_value)) == basic_value


def test_default_proto_convertor():
    """Test that add_default_convertor works."""
    group = ProtoConvertorGroup(proto_module)
    # By default, we cannot convert IntRange
    ex_range = IntRange(1, 2)
    with pytest.raises(ProtoConvertorError):
        group.to_proto(ex_range)

    # But we can add a default convertor
    group.add_default_convertor(IntRange)

    assert group.from_proto(group.to_proto(ex_range)) == ex_range


def test_add_convertor_by_spec():
    """Test that add_convertor_by_spec works."""
    group = ProtoConvertorGroup(proto_module)
    group.add_convertor_by_spec(
        # a funny convertor that swaps start and stop
        RecursiveConvertorSpec[IntRange](
            py_class=IntRange,
            proto_extra_fields={"start", "stop"},
            get_proto_extra_fields=lambda obj: {"start": obj.stop, "stop": obj.start},
            py_dropped_fields={"start", "stop"},
            get_python_dropped_fields=lambda d: {
                "start": d["stop"],
                "stop": d["start"],
            },
        )
    )
    ex_range = IntRange(1, 2)
    assert group.from_proto(group.to_proto(ex_range)) == ex_range


def test_extending_convertor_group():
    """Test that we can extend a convertor group."""
    group1 = ProtoConvertorGroup(proto_module)
    group1.add_default_convertor(IntRange)

    group2 = ProtoConvertorGroup(proto_module, group1)

    # We can convert IntRange by inheriting from group1
    ex_range = IntRange(2, 3)
    assert group2.from_proto(group2.to_proto(ex_range)) == ex_range

    # We can also convert SymbolNameUsage, which requires IntRange convertor.
    group2.add_convertor(PathConvertor())
    group2.add_default_convertor(FileCharRange)
    group2.add_convertor_by_spec(
        RecursiveConvertorSpec[SymbolNameUsage](
            py_class=SymbolNameUsage,
            proto_extra_fields={"def_range", "has_def_range"},
            get_proto_extra_fields=lambda obj: {
                "def_range": obj.def_range,
                "has_def_range": obj.def_range is not None,
            },
            py_dropped_fields={"def_range"},
            get_python_dropped_fields=lambda d: {
                "def_range": d["def_range"] if d["has_def_range"] else None,
            },
        )
    )
    py_val = SymbolNameUsage("abc", IntRange(1, 2))
    assert group2.from_proto(group2.to_proto(py_val)) == py_val


def test_repeated_field_convertor_group():
    """Test that we convert something with a repeated field."""
    group = ProtoConvertorGroup(proto_module)
    group.add_convertor(PathConvertor())
    group.add_default_convertor(IntRange)
    group.add_default_convertor(FileCharRange)
    group.add_convertor_by_spec(
        RecursiveConvertorSpec[SymbolNameUsage](
            py_class=SymbolNameUsage,
            proto_extra_fields={"def_range", "has_def_range"},
            get_proto_extra_fields=lambda obj: {
                "def_range": obj.def_range,
                "has_def_range": obj.def_range is not None,
            },
            py_dropped_fields={"def_range"},
            get_python_dropped_fields=lambda d: {
                "def_range": d["def_range"] if d["has_def_range"] else None,
            },
        )
    )
    # Local usage analysis basically contains a list of SymbolNameUsage.
    group.add_convertor_by_spec(
        RecursiveConvertorSpec[LocalUsageAnalysis](
            py_class=LocalUsageAnalysis,
            proto_extra_fields={"name_usages", "var_occurrences"},
            get_proto_extra_fields=lambda obj: {
                "name_usages": list(obj.name_usages),
                "var_occurrences": list(obj.var_occurrences),
            },
            py_dropped_fields={"name_usages", "var_occurrences"},
            get_python_dropped_fields=lambda d: {
                "name_usages": set(d["name_usages"]),
                "var_occurrences": set(d["var_occurrences"]),
            },
        )
    )

    # Test that we can convert the empty message without error.
    assert isinstance(
        group.from_proto(proto_module.LocalUsageAnalysis()), LocalUsageAnalysis
    )

    original = LocalUsageAnalysis(
        name_usages={
            SymbolNameUsage("a", use_site=CharRange(0, 10)),
            SymbolNameUsage("b", use_site=CharRange(0, 10)),
        },
        var_occurrences=set(),
    )

    pb = group.to_proto(original)
    converted = group.from_proto(pb)
    assert original == converted
