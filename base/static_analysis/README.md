## Augment Static Analysis Utilities

### Add a new language
When adding a new programming langauge, you may need to implement the following components:
- Map the extension to the language id: base/static_analysis/common.py
- Scope tree parsing: base/static_analysis/parsing.py
- Usage analysis:
    - base/static_analysis/_usage_supports.py
    - base/static_analysis/_variable_supports.py
- Signature formatting: base/static_analysis/signature_utils.py
- signature indexing: base/static_analysis/signature_index.py
- Import noising: research/fim/import_noising.py
- Heuristic import finder: research/static_analysis/import_finder.py

Depending on your use cases, not all of the above components are needed.
