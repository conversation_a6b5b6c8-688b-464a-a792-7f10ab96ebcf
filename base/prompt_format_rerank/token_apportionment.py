"""Module containing code related to the reranker token apportionment."""

from dataclasses import dataclass, field

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class ChatRerankerTokenApportionmentConfig:
    """The token apportionment configuration contains the basic config.

    This class is usually the input into (advanced) prompt formatter,
    which the prompt formatter should take as a hint on the expected
    prompt

    See note in RerankerConfig in services/completion_host/single_model_server/retriever_factory.py
    about the purpose of the seq_len_per_batch_elem and batch_size fields.
    """

    max_dialogue_tokens: int
    """The maximum number of tokens to include from the dialogue."""

    seq_len_per_batch_elem: int
    """The sequence length per batch element."""

    batch_size: int
    """The batch size to use."""

    def __post_init__(self):
        assert self.max_dialogue_tokens > 0
        assert self.seq_len_per_batch_elem > 0
        assert self.batch_size > 0
