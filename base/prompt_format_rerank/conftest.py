"""Contains shared fixtures for prompt formatting unit tests."""

from typing import Iterable

import pytest

from base.prompt_format.common import PromptChunk
from base.prompt_format_chat.prompt_formatter import Exchange
from base.prompt_format_rerank.prompt_formatter import ChatRerankerPromptInput


@pytest.fixture()
def example_input():
    """Returns an example prompt input."""
    return ChatRerankerPromptInput(
        message="Do we have a framework for managing endpoints?",
        dialogue_history=[
            Exchange(
                request_message="Where can I find the unit test for our login endpoint?",
                response_text="You can find this endpoint in the file src/login.py.",
            ),
        ],
        candidate_chunks=[
            PromptChunk(
                text="# This is the login endpoint. (part 1)",
                path="src/login.py",
            ),
            PromptChunk(
                text="# This is the logout endpoint.",
                path="src/logout.py",
            ),
            PromptChunk(
                text="# This is the signup endpoint.",
                path="src/signup.py",
            ),
            PromptChunk(
                text="# This is the login endpoint. (part 2)",
                path="src/login.py",
            ),
        ],
    )


@pytest.fixture()
def longchat_example_input():
    """Returns an example prompt input."""
    return ChatRerankerPromptInput(
        message="Do we have a framework for managing endpoints?",
        dialogue_history=[
            Exchange(
                request_message="1Where can I find the unit test for our login endpoint?",
                response_text="1You can find this endpoint in the file src/login.py.",
            ),
            Exchange(
                request_message="2Where can I find the unit test for our login endpoint?",
                response_text="2You can find this endpoint in the file src/login.py.",
            ),
            Exchange(
                request_message="3Where can I find the unit test for our login endpoint?",
                response_text="3You can find this endpoint in the file src/login.py.",
            ),
        ],
        candidate_chunks=[
            PromptChunk(
                text="# This is the login endpoint. (part 1)",
                path="src/login.py",
            ),
            PromptChunk(
                text="# This is the logout endpoint.",
                path="src/logout.py",
            ),
            PromptChunk(
                text="# This is the signup endpoint.",
                path="src/signup.py",
            ),
            PromptChunk(
                text="# This is the login endpoint. (part 2)",
                path="src/login.py",
            ),
        ],
    )


@pytest.fixture()
def nohistory_example_input():
    """Returns an example prompt input with no dialogue history."""
    return ChatRerankerPromptInput(
        message="Do we have a framework for managing endpoints?",
        dialogue_history=[],
        candidate_chunks=[
            PromptChunk(
                text="# This is the login endpoint. (part 1)",
                path="src/login.py",
            ),
            PromptChunk(
                text="# This is the logout endpoint.",
                path="src/logout.py",
            ),
            PromptChunk(
                text="# This is the signup endpoint.",
                path="src/signup.py",
            ),
            PromptChunk(
                text="# This is the login endpoint. (part 2)",
                path="src/login.py",
            ),
        ],
    )
