"""Prompt formatter to format prompts for Ethanol embedding keys and queries."""

from collections import deque
import copy
from typing import Optional, Sequence

from base.prompt_format.common import PromptChunk, TokenList
from base.prompt_format_rerank.prompt_formatter import (
    ChatR<PERSON>kerPromptInput,
    RerankerPromptFormatter,
    RerankerPromptFormatterOutput,
)
from base.prompt_format_rerank.token_apportionment import (
    ChatRerankerTokenApportionmentConfig,
)
from base.tokenizers import RerankerSpecialTokens, Tokenizer


class DarkChatanolFormatter(RerankerPromptFormatter[ChatRerankerPromptInput]):
    """Formatter which enables both chunk expansion and reranker."""

    input_type = ChatRerankerPromptInput

    def __init__(
        self,
        apportionment_config: Optional[ChatRerankerTokenApportionmentConfig],
        tokenizer: Tokenizer,
        single_turn_is_special: bool = False,
    ):
        if not apportionment_config:
            apportionment_config = ChatRerankerTokenApportionmentConfig(
                max_dialogue_tokens=1024,
                seq_len_per_batch_elem=8192,
                batch_size=1,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        assert isinstance(tokenizer.special_tokens, RerankerSpecialTokens)
        self.special_tokens = tokenizer.special_tokens
        self.single_turn_is_special = single_turn_is_special

    def format_prompt(
        self,
        prompt_input: ChatRerankerPromptInput,
    ) -> RerankerPromptFormatterOutput:
        """Returns list of tokenized prompts.

        Adds chunks to batches in round robin fashion.
        """
        max_prompt_tokens = self.apportionment_config.seq_len_per_batch_elem
        initial_prompt = self._format_initial_prompt(
            prompt_input,
            max_prompt_tokens,
        )
        assert initial_prompt is not None
        # Each item in the queue is a list of chunk indices and a partial prompt
        partial_prompts_queue = deque(
            [([], initial_prompt) for _ in range(self.apportionment_config.batch_size)]
        )

        # Once prompts are done processing they are popped from partial_prompts_queue
        # and dumped into these lists.
        final_prompts: list[list[int]] = []
        final_chunk_indices: list[list[int]] = []

        # Add chunks in round robin
        chunklist_iter = deque(enumerate(prompt_input.candidate_chunks))
        while len(chunklist_iter) > 0 and len(partial_prompts_queue) > 0:
            chunk_idx, chunk = chunklist_iter.popleft()
            cur_chunk_indices, cur_prompt = partial_prompts_queue.popleft()
            maybe_new_prompt = self._append_chunk_to_prompt_or_none(
                cur_prompt, chunk, max_prompt_tokens
            )

            # If maybe_new_prompt is None that means we can't add the chunk to the prompt.
            if maybe_new_prompt is not None:
                # If able to add chunk to prompt, then the prompt can be processed again
                # and the chunk is done processing.
                partial_prompts_queue.append(
                    (cur_chunk_indices + [chunk_idx], maybe_new_prompt)
                )

            else:
                # Otherwise, the prompt (and its chunk indices list) is done processing
                final_prompts.append(cur_prompt)
                final_chunk_indices.append(cur_chunk_indices)

                # but the chunk can be processed again.
                chunklist_iter.appendleft((chunk_idx, chunk))

        # eject remaining items in queue if they exist
        for cur_chunk_indices, cur_prompt in partial_prompts_queue:
            final_prompts.append(cur_prompt)
            final_chunk_indices.append(cur_chunk_indices)

        assert (
            len(final_prompts) == self.apportionment_config.batch_size
        ), f"Expected {self.apportionment_config.batch_size} prompts, but got {len(final_prompts)}"
        return RerankerPromptFormatterOutput(
            batched_token_lists=final_prompts, batched_chunk_indices=final_chunk_indices
        )

    def _format_initial_prompt(
        self,
        prompt_input: ChatRerankerPromptInput,
        max_prompt_tokens: int,
    ) -> list[int]:
        """Format the query part of the tokenized prompt."""

        tokenize = self.tokenizer.tokenize_safe

        remaining_token_budget = max_prompt_tokens
        remaining_token_budget -= 1  # for eoq token

        # Add dialogue history, starting with current user message
        is_single_turn = len(prompt_input.dialogue_history) == 0
        message_prefix = (
            ""
            if is_single_turn and self.single_turn_is_special
            else "### Instruction:\n"
        )
        tokenized_prompt = tokenize(f"{message_prefix}{prompt_input.message}")[
            :remaining_token_budget
        ]
        remaining_token_budget -= len(tokenized_prompt)
        assert remaining_token_budget >= 0

        for msg_pair in reversed(prompt_input.dialogue_history):
            msg_pair_toks = tokenize(
                f"### Instruction:\n{msg_pair.request_message}\n"
            ) + tokenize(f"### Response:\n{msg_pair.response_text}\n")

            if (
                len(tokenized_prompt) + len(msg_pair_toks)
                > self.apportionment_config.max_dialogue_tokens
            ):
                break

            if len(tokenized_prompt) > remaining_token_budget:
                break

            tokenized_prompt = msg_pair_toks + tokenized_prompt
            remaining_token_budget -= len(msg_pair_toks)

        # 1 unit already deducted from remaining_token_budget above for eoq token
        tokenized_prompt += [self.special_tokens.end_of_query]
        assert remaining_token_budget >= 0

        return tokenized_prompt

    def _append_chunk_to_prompt_or_none(
        self,
        initial_prompt: list[int],
        chunk: PromptChunk,
        max_prompt_tokens: int,
    ) -> list[int] | None:
        """Returns tokenized prompt with chunk formatted and appended, or None if max_prompt_tokens would be exceeded."""
        tokenize = self.tokenizer.tokenize_safe

        chunk_toks = (
            tokenize(chunk.path)
            + [self.special_tokens.fim_prefix]
            + tokenize(chunk.text)
            + [self.special_tokens.chunk_prediction]
        )
        new_prompt = initial_prompt + chunk_toks

        return new_prompt if len(new_prompt) <= max_prompt_tokens else None
