"""Classes that are used to build the prompts for the retriever model."""

from dataclasses import dataclass
from typing import Iterable, Protocol, Sequence, TypeVar

from base.prompt_format.common import PromptChunk, TokenList
from base.prompt_format_chat.prompt_formatter import Exchange
from base.python.au_itertools import reusable_iterable
from base.tokenizers import Tokenizer


@dataclass(frozen=True)
class ChatRerankerPromptInput:
    """The set of inputs used for constructing the reranker model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.
    """

    message: str
    """The user's current message."""

    dialogue_history: Sequence[Exchange]
    """The dialog history."""

    candidate_chunks: Sequence[PromptChunk]
    """The candidate chunks."""


@dataclass(frozen=True)
class RerankerPromptFormatterOutput:
    """The output of the reranker prompt formatter.

    This is very similar to the PromptFormatterOutput in base/prompt_format/common.py,
    but it returns a list of output sequences instead of just one. This accomodates the batched reranking
    paradigm. It also removes the streaming complexity because we don't use it in the reranker currently.

    Finally, we also pass back the indices of the chunks that were used in each prompt. This helps
    us figure out what score corresponds to what chunk in the reranker.
    """

    batched_token_lists: Sequence[TokenList]
    """The tokenized prompts."""

    batched_chunk_indices: Sequence[Sequence[int]]
    """The indices of the chunks that were used in each prompt."""


# Convert to union type once we have more prmopt input types here.
InputT = TypeVar("InputT", bound=ChatRerankerPromptInput)


class RerankerPromptFormatter(Protocol[InputT]):
    """The Reranker Prompt Formatter protocol.

    Because rerankers are used horizontally across task models, e.g. completion, chat,
    etc., they have a generic input type.
    """

    input_type: type[InputT]
    """The type of the input. Used for dynamic type checking."""

    tokenizer: Tokenizer
    """The tokenizer used to tokenize the prompt."""

    def format_prompt(
        self,
        prompt_input: InputT,
    ) -> RerankerPromptFormatterOutput:
        """Build tokenized prompts, returned as a batch in case there are multiple prompts.

        Args:
            prompt_input: RerankerPromptInput object describing input
        Returns:
            prompt as list of tokens lists
        """
        raise NotImplementedError()
