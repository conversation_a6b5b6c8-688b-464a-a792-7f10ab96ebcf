"""Tests for the Dark Chatanol prompt formatter."""

from base import tokenizers
from base.prompt_format_rerank.dark_chatanol import DarkChatanolFormatter
from base.prompt_format_rerank.prompt_formatter import Chat<PERSON><PERSON>kerPromptInput
from base.prompt_format_rerank.token_apportionment import (
    ChatRerankerTokenApportionmentConfig,
)


def test_dark_chatanol_prompt_formatter_basic(example_input: ChatRerankerPromptInput):
    """This is a simple sanity check to catch obvious bugs in Dark Chatanol's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = ChatRerankerTokenApportionmentConfig(
        max_dialogue_tokens=1024,
        seq_len_per_batch_elem=8192,
        batch_size=1,
    )
    prompter = DarkChatanolFormatter(config, tokenizer)

    assert len(prompter.format_prompt(example_input).batched_token_lists) == 1
    assert len(prompter.format_prompt(example_input).batched_chunk_indices) == 1
    prompt_tokens = prompter.format_prompt(example_input).batched_token_lists[0]
    chunk_indices = prompter.format_prompt(example_input).batched_chunk_indices[0]
    prompt = tokenizer.detokenize(prompt_tokens)

    expected_prompt = """### Instruction:
Where can I find the unit test for our login endpoint?
### Response:
You can find this endpoint in the file src/login.py.
### Instruction:
Do we have a framework for managing endpoints?<|ret-endofquery|>src/login.py<fim_prefix># This is the login endpoint. (part 1)<|chunk_prediction|>src/logout.py<fim_prefix># This is the logout endpoint.<|chunk_prediction|>src/signup.py<fim_prefix># This is the signup endpoint.<|chunk_prediction|>src/login.py<fim_prefix># This is the login endpoint. (part 2)<|chunk_prediction|>"""
    assert prompt == expected_prompt

    expected_chunk_indices = [0, 1, 2, 3]
    assert chunk_indices == expected_chunk_indices


def test_dark_chatanol_prompt_formatter_longchat(
    longchat_example_input: ChatRerankerPromptInput,
):
    """This checks correct use of long dialogue history in Dark Chatanol's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = ChatRerankerTokenApportionmentConfig(
        max_dialogue_tokens=1024,
        seq_len_per_batch_elem=8192,
        batch_size=1,
    )
    prompter = DarkChatanolFormatter(config, tokenizer)

    assert len(prompter.format_prompt(longchat_example_input).batched_token_lists) == 1
    assert (
        len(prompter.format_prompt(longchat_example_input).batched_chunk_indices) == 1
    )
    prompt_tokens = prompter.format_prompt(longchat_example_input).batched_token_lists[
        0
    ]
    chunk_indices = prompter.format_prompt(
        longchat_example_input
    ).batched_chunk_indices[0]
    prompt = tokenizer.detokenize(prompt_tokens)

    expected_prompt = """### Instruction:
1Where can I find the unit test for our login endpoint?
### Response:
1You can find this endpoint in the file src/login.py.
### Instruction:
2Where can I find the unit test for our login endpoint?
### Response:
2You can find this endpoint in the file src/login.py.
### Instruction:
3Where can I find the unit test for our login endpoint?
### Response:
3You can find this endpoint in the file src/login.py.
### Instruction:
Do we have a framework for managing endpoints?<|ret-endofquery|>src/login.py<fim_prefix># This is the login endpoint. (part 1)<|chunk_prediction|>src/logout.py<fim_prefix># This is the logout endpoint.<|chunk_prediction|>src/signup.py<fim_prefix># This is the signup endpoint.<|chunk_prediction|>src/login.py<fim_prefix># This is the login endpoint. (part 2)<|chunk_prediction|>"""
    assert prompt == expected_prompt

    expected_chunk_indices = [0, 1, 2, 3]
    assert chunk_indices == expected_chunk_indices


def test_dark_chatanol_prompt_formatter_nohistory(
    nohistory_example_input: ChatRerankerPromptInput,
):
    """This checks correct use of lack of dialogue history in Dark Chatanol's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = ChatRerankerTokenApportionmentConfig(
        max_dialogue_tokens=1024,
        seq_len_per_batch_elem=8192,
        batch_size=1,
    )
    prompter = DarkChatanolFormatter(config, tokenizer)

    assert len(prompter.format_prompt(nohistory_example_input).batched_token_lists) == 1
    assert (
        len(prompter.format_prompt(nohistory_example_input).batched_chunk_indices) == 1
    )
    prompt_tokens = prompter.format_prompt(nohistory_example_input).batched_token_lists[
        0
    ]
    chunk_indices = prompter.format_prompt(
        nohistory_example_input
    ).batched_chunk_indices[0]
    prompt = tokenizer.detokenize(prompt_tokens)

    expected_prompt = """### Instruction:
Do we have a framework for managing endpoints?<|ret-endofquery|>src/login.py<fim_prefix># This is the login endpoint. (part 1)<|chunk_prediction|>src/logout.py<fim_prefix># This is the logout endpoint.<|chunk_prediction|>src/signup.py<fim_prefix># This is the signup endpoint.<|chunk_prediction|>src/login.py<fim_prefix># This is the login endpoint. (part 2)<|chunk_prediction|>"""
    assert prompt == expected_prompt

    expected_chunk_indices = [0, 1, 2, 3]
    assert chunk_indices == expected_chunk_indices


def test_dark_chatanol_prompt_formatter_batching(
    example_input: ChatRerankerPromptInput,
):
    tokenizer = tokenizers.create_tokenizer_by_name("starcoder")
    config = ChatRerankerTokenApportionmentConfig(
        max_dialogue_tokens=20,
        seq_len_per_batch_elem=50,
        batch_size=2,
    )
    prompter = DarkChatanolFormatter(config, tokenizer)

    assert len(prompter.format_prompt(example_input).batched_token_lists) == 2
    assert len(prompter.format_prompt(example_input).batched_chunk_indices) == 2
    prompt_tokens_ls = prompter.format_prompt(example_input).batched_token_lists
    chunk_indices_ls = prompter.format_prompt(example_input).batched_chunk_indices
    prompt_ls = [tokenizer.detokenize(itm) for itm in prompt_tokens_ls]

    assert prompt_ls == [
        "### Instruction:\nDo we have a framework for managing endpoints?<|ret-endofquery|>src/login.py<fim_prefix># This is the login endpoint. (part 1)<|chunk_prediction|>src/signup.py<fim_prefix># This is the signup endpoint.<|chunk_prediction|>",
        "### Instruction:\nDo we have a framework for managing endpoints?<|ret-endofquery|>src/logout.py<fim_prefix># This is the logout endpoint.<|chunk_prediction|>src/login.py<fim_prefix># This is the login endpoint. (part 2)<|chunk_prediction|>",
    ]

    assert chunk_indices_ls == [[0, 2], [1, 3]]
