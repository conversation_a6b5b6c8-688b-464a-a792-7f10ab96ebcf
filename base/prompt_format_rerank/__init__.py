"""Module containing prompt formatting logic for different retrieval models."""

import logging
from typing import Any, Optional

from base.prompt_format_rerank.dark_chatanol import DarkChatanolFormatter
from base.prompt_format_rerank.prompt_formatter import (  # noqa: F401; Re-exported
    ChatRerankerPromptInput,
    RerankerPromptFormatter,
)
from base.prompt_format_rerank.token_apportionment import (
    ChatRerankerTokenApportionmentConfig,
)
from base.tokenizers.tokenizer import Tokenizer


def get_reranker_prompt_formatter_by_name(
    name: str,
    tokenizer: Tokenizer,
    apportionment_config: Optional[ChatRerankerTokenApportionmentConfig] = None,
) -> RerankerPromptFormatter:
    """Returns the prompt formatter by name.

    Args:
        name: name of the formatter
        tokenizer: instance of the tokenizer to use.
        apportionment_config: Hints for the apportionment of tokens during the prompt formatting. If not set, a default is used.
        prompt_formatter_config: optional additional configuration for the prompt formatter.

    If there is no formatter with the given name, an exception is thrown.
    """

    # No prompt formatters defined yet so this is always true.
    if name == "dark-chatanol1":
        return DarkChatanolFormatter(apportionment_config, tokenizer)
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError(f"Invalid prompt formatter name: {name}.")
