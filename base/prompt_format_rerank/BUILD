load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "token_apportionment",
    srcs = [
        "token_apportionment.py",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "conftest",
    srcs = [
        "conftest.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/prompt_format:common",
        "//base/tokenizers",
        requirement("pytest"),
    ],
)

py_library(
    name = "dark_chatanol_prompt_formatter",
    srcs = [
        "dark_chatanol.py",
    ],
    deps = [
        ":prompt_formatter",
        ":token_apportionment",
        "//base/languages",
        "//base/prompt_format:util",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "dark_chatanol_prompt_formatter_test",
    srcs = [
        "dark_chatanol_test.py",
    ],
    deps = [
        ":conftest",
        ":dark_chatanol_prompt_formatter",
        ":prompt_formatter",
        ":token_apportionment",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

py_library(
    name = "prompt_formatter",
    srcs = [
        "prompt_formatter.py",
    ],
    deps = [
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "prompt_format_rerank",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":dark_chatanol_prompt_formatter",
        ":prompt_formatter",
        ":token_apportionment",
        "//base/tokenizers",
    ],
)
