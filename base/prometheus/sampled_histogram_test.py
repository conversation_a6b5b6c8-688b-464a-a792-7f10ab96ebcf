"""Tests for sampled histogram."""

import prometheus_client  # type: ignore

from base.prometheus import sampled_histogram


def _extract_from_prometheus_metrics(
    prometheus_metrics_str: list[str], metric_name: str
) -> str:
    for line in prometheus_metrics_str:
        if line.startswith(metric_name):
            return line.split()[1]
    raise ValueError(f"Metric {metric_name} not found in {prometheus_metrics_str}")


def _extract_bucket_values(
    prometheus_metrics_str: list[str],
    metric_name: str,
    labels: list[tuple[str, str]],
    buckets: list[float],
) -> list[str]:
    results = []
    label_str = ",".join(f'{k}="{v}"' for k, v in labels)
    for bucket in buckets:
        results.append(
            _extract_from_prometheus_metrics(
                prometheus_metrics_str,
                f'{metric_name}_bucket{{{label_str},le="{float(bucket)}"}}',
            )
        )
    return results


def test_sampled_histogram():
    """Test sampled histogram."""
    histogram = sampled_histogram.SampledHistogram(
        "test_sampled_histogram",
        description="Test sampled histogram",
        interval_seconds=0.01,
        labels=["label1", "label2"],
        buckets=[0.0, 1.0, 2.0],
    )
    histogram.register_callback(lambda: 1.0, ["label_value1", "label_value2"])
    histogram._update_metric()
    recorded_value = _extract_from_prometheus_metrics(
        prometheus_client.generate_latest().decode("utf8").split("\n"),
        'test_sampled_histogram_sum{label1="label_value1",label2="label_value2"}',
    )
    assert recorded_value == "1.0"
    buckets = _extract_bucket_values(
        prometheus_client.generate_latest().decode("utf8").split("\n"),
        "test_sampled_histogram",
        labels=[("label1", "label_value1"), ("label2", "label_value2")],
        buckets=[0.0, 1.0, 2.0],
    )
    assert buckets == ["0.0", "1.0", "1.0"]
    histogram.stop()


def test_sampled_counter_starts_at_zero():
    """Test sampled counter."""
    counter = sampled_histogram.SampledCounter(
        "test_sampled_counter0",
        description="Test sampled counter",
        interval_seconds=0.01,
        labels=["label1", "label2"],
        buckets=[0.0, 1.0, 2.0],
    )
    counter.set_labels(["label_value1", "label_value2"])
    recorded_value = _extract_from_prometheus_metrics(
        prometheus_client.generate_latest().decode("utf8").split("\n"),
        'test_sampled_counter0_sum{label1="label_value1",label2="label_value2"}',
    )
    assert recorded_value == "0.0"

    counter._sampled_histogram._update_metric()
    recorded_value = _extract_from_prometheus_metrics(
        prometheus_client.generate_latest().decode("utf8").split("\n"),
        'test_sampled_counter0_sum{label1="label_value1",label2="label_value2"}',
    )
    assert recorded_value == "0.0"


def test_sampled_counter_increments():
    """Test sampled counter."""
    counter = sampled_histogram.SampledCounter(
        "test_sampled_counter1",
        description="Test sampled counter",
        interval_seconds=0.01,
        labels=["label1", "label2"],
        buckets=[0.0, 1.0, 2.0],
    )
    counter.set_labels(["label_value1", "label_value2"])

    with counter.inc():
        counter._sampled_histogram._update_metric()

    buckets = _extract_bucket_values(
        prometheus_client.generate_latest().decode("utf8").split("\n"),
        "test_sampled_counter1",
        labels=[("label1", "label_value1"), ("label2", "label_value2")],
        buckets=[0.0, 1.0, 2.0],
    )
    assert buckets == ["0.0", "1.0", "1.0"]


def test_sampled_counter_decrements():
    """Test sampled counter."""
    counter = sampled_histogram.SampledCounter(
        "test_sampled_counter2",
        description="Test sampled counter",
        interval_seconds=0.01,
        labels=["label1", "label2"],
        buckets=[0.0, 1.0, 2.0],
    )
    counter.set_labels(["label_value1", "label_value2"])

    with counter.inc():
        pass

    counter._sampled_histogram._update_metric()  # outside of the context manager

    buckets = _extract_bucket_values(
        prometheus_client.generate_latest().decode("utf8").split("\n"),
        "test_sampled_counter2",
        labels=[("label1", "label_value1"), ("label2", "label_value2")],
        buckets=[0.0, 1.0, 2.0],
    )
    assert buckets == ["1.0", "1.0", "1.0"]
