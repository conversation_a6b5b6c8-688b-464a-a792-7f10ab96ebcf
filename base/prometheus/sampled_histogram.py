"""Helper to monitor a value as a histogram in prometheus."""

import logging
import threading
import time
from dataclasses import dataclass
from typing import Callable, Sequence

import prometheus_client


class SampledHistogram:
    """Sample a value in regular intervals and monitor it as a histogram in prometheus."""

    def __init__(
        self,
        name: str,
        description: str,
        *,
        interval_seconds: float,
        labels: Sequence[str] = tuple(),
        buckets: Sequence[float],
    ):
        """Initialize the SampledHistogram.

        Args:
            name: The name of the metric.
            description: The description of the metric.
            interval_seconds: The interval in seconds between samples.
            labels: The labels to attach to the metric.
            buckets: The buckets to use for the histogram.
        """
        self._name = name
        self._description = description
        self._interval_seconds = interval_seconds
        self._labels = labels
        self._buckets = buckets

        self._last_sample_time = time.time()
        self._thread = None
        self._shutdown = False
        self._histogram = prometheus_client.Histogram(
            name,
            description,
            self._labels,
            buckets=self._buckets,
        )

        self._callbacks: dict[
            tuple[str, ...], tuple[prometheus_client.Histogram, Callable]
        ] = {}
        self._lock = threading.Lock()  # protects _callabacks

    def register_callback(
        self, callback: Callable[[], float], labels: Sequence[str] = tuple()
    ):
        """Register a callback with the labels.

        Args:
            callback: The callback to get the value.
            labels: The labels under which this callback is registered. We can have at most one callback per label set.
        """
        if len(labels) != len(self._labels):
            raise ValueError(
                f"Expected {len(self._labels)} labels, got {len(labels)} instead."
            )
        labels = tuple(labels)
        with self._lock:
            if labels in self._callbacks:
                logging.warning(
                    "Overwriting callback for labels %s. This is not recommended outside of tests.",
                    labels,
                )
            self._callbacks[labels] = (
                self._histogram.labels(*labels),
                callback,
            )

    def start(self):
        """Start sampling the value."""
        with self._lock:
            self._thread = threading.Thread(
                target=self._run, name=f"SampledHistogram_{self._name}", daemon=True
            )
            self._thread.start()

    def _run(self):
        """Run the sampling loop."""
        while not self._shutdown:
            current_time = time.time()
            if current_time - self._last_sample_time >= self._interval_seconds:
                self._last_sample_time = current_time
                self._update_metric()
                time.sleep(self._interval_seconds)
            else:
                time.sleep(
                    self._last_sample_time + self._interval_seconds - current_time
                )
        if self._thread is not None:
            logging.info("Stopping sampling thread %s", self._thread.name)

    def _update_metric(self):
        """Update the metric with the given value."""
        with self._lock:
            for _label, (histogram, callback) in self._callbacks.items():
                histogram.observe(callback())

    def stop(self):
        """Stop sampling the value."""
        self._shutdown = True


class ThreadSafeCounter:
    """A thread-safe counter."""

    def __init__(self):
        self._lock = threading.Lock()
        self._counter = 0

    def inc(self, amount: int = 1):
        """Increment the counter by the given amount."""
        with self._lock:
            self._counter += amount

    def dec(self, amount: int = 1):
        """Decrement the counter by the given amount."""
        with self._lock:
            self._counter -= amount

    def get(self) -> int:
        """Get the current value of the counter."""
        with self._lock:
            return self._counter


@dataclass(frozen=True)
class _CounterContext:
    """Context manager for a counter."""

    counter: ThreadSafeCounter
    amount: int

    def __enter__(self):
        self.counter.inc(self.amount)
        return self

    def __exit__(self, *args):
        self.counter.dec(self.amount)


class SampledCounter:
    """Sample a value in regular intervals and monitor it as a histogram in prometheus.

    Implemented as a wrapper around a SampledHistogram.

    Sample usage:

        sampled_counter = SampledCounter(
            "test_sampled_counter",
            description="Test sampled counter",
            interval_seconds=0.01,
            labels=["label1", "label2"],
            buckets=[0.0, 1.0, 2.0],
        )

        sampled_counter.set_labels(["value1", "value2"])
        sampled_counter.start()
        with sampled_counter.inc():
            # while this code is running, the counter samples are 1 higher than before.

        sampled_counter.stop()
    """

    def __init__(
        self,
        name: str,
        description: str,
        *,
        interval_seconds: float,
        labels: Sequence[str] = tuple(),
        buckets: Sequence[float],
    ):
        """Initialize the queue monitor.

        Args:
            name: The name of the metric.
            description: The description of the metric.
            interval_seconds: The interval in seconds between samples.
            labels: The labels to attach to the metric.
            buckets: The buckets to use for the histogram.
        """
        self._sampled_histogram = SampledHistogram(
            name,
            description,
            interval_seconds=interval_seconds,
            labels=labels,
            buckets=buckets,
        )
        self._name = name
        self._counter = ThreadSafeCounter()

        self._labels = None

    def set_labels(self, labels: Sequence[str]):
        """Set the labels to the given value."""
        if self._labels is not None:
            if self._labels != labels:
                raise ValueError("Labels can only be set once.")
        self._labels = labels
        self._sampled_histogram.register_callback(self._counter.get, labels)

    def start(self):
        """Start sampling the value."""
        self._sampled_histogram.start()

    def stop(self):
        """Stop sampling the value."""
        self._sampled_histogram.stop()

    def inc(self, amount: int = 1):
        """Increment the counter by the given amount."""
        return _CounterContext(self._counter, amount)
