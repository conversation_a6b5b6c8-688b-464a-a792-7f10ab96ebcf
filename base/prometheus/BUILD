load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "sampled_histogram",
    srcs = ["sampled_histogram.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "sampled_histogram_test",
    srcs = ["sampled_histogram_test.py"],
    deps = [
        ":sampled_histogram",
        requirement("prometheus-client"),
    ],
)
