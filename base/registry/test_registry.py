"""Test for the registry module."""

import pytest

import base.registry as registry


class TestInterface:
    """Interface used to test registry."""


class Implementation1(TestInterface):
    """First implementation of interface used to test registry."""


class Implementation2(TestInterface):
    """Second implementation of interface used to test registry."""


def test_basic():
    """Test the basic functions of the registry."""
    reg: registry.Registry[TestInterface] = registry.Registry()

    impl1 = Implementation1()
    impl2 = Implementation2()

    reg.add("impl1", impl1)
    reg.add("impl2", impl2)

    assert reg.get("impl1") == impl1
    assert reg.get("impl2") == impl2

    with pytest.raises(AssertionError):
        reg.add("impl1", impl1)

    entries = {}
    for name, impl in reg:
        entries[name] = impl

    assert entries["impl1"] == impl1
    assert entries["impl2"] == impl2
    assert len(entries) == 2
