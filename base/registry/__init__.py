"""Registry for fetching objects by name."""

import threading
from typing import <PERSON>, Generic, Iterator, <PERSON><PERSON>, TypeVar

T = TypeVar("T")


class Registry(Generic[T]):
    """Fetch objects by name."""

    def __init__(self):
        self.lock = threading.Lock()
        self._registry: dict[str, T] = {}

    def add(self, name: str, obj: T) -> None:
        """Register an object by name."""
        with self.lock:
            assert name not in self._registry, f"{name} already registered."
            self._registry[name] = obj

    def get(self, name: str) -> T:
        """Fetch an object by name."""
        with self.lock:
            if name not in self._registry:
                raise ValueError(
                    f"{name} not registered, please find all registered names as {list(self._registry.keys())}."
                )
            return self._registry[name]

    def __iter__(self) -> Iterator[Tuple[str, T]]:
        """Iterate over the registered objects."""
        with self.lock:
            return iter(list(self._registry.items()))
