# registry

Configuration files have strings in them (e.g. the name of a tokenizer or prompt formatter or model). How do we translate those strings into Python objects? One way is a registry.

A registry is a thread-safe table that maps strings to Python objects.

This Python module provides a registry implementation but does not instantiate it. It is up to user of the Python module to instantiate a registry.

Often a registry is used to store a factory that will create instances of an object. This is the case with tokenizers and prompt formatters.

## Usage

```
# widget.py

import base.registry

WidgetFactory = Callable[[], Widget]
REGISTRY : Registry[WidgetFactory] = base.registry.Registry()

def create_widget(w: str):
    return REGISTRY.get(w)()

# awesome_widget.py

class AwesomeWidget(Widget):
    def __init__(self):
        pass

REGISTRY.add("awesome", AwesomeWidget)


```
