use std::io::Error;
use std::io::ErrorKind;

use bytes::Bytes;
use half::f16;
use nom::IResult;

use std::io::Result;

pub mod f16x16_simd;
pub mod f32x8_simd;

use crate::f16x16_simd::f16x16;
use crate::f16x16_simd::f16x16_LENGTH;
use crate::f16x16_simd::to_slice;
use crate::f32x8_simd::f32x8;
use crate::f32x8_simd::f32x8_LENGTH;
use crate::f32x8_simd::to_f32_array;

// numpy parsing based on https://github.com/potocpav/npy-rs/blob/master/src/header.rs
// License: MIT (see https://docs.rs/crate/npy/latest)
// but fixed to support f16 and tensor shapes
mod parser {
    use nom::*;
    use std::collections::HashMap;

    #[derive(PartialEq, Eq, Debug, Clone)]
    pub enum Value {
        String(String),
        Integer(i64),
        Bool(bool),
        List(Vec<Value>),
        Map(HashMap<String, Value>),
    }

    pub fn parse_header(bs: &[u8]) -> IResult<&[u8], Value> {
        header(bs)
    }

    named!(pub header<Value>,
        do_parse!(
            tag!(&[0x93u8]) >>
            tag!(b"NUMPY") >>
            tag!(&[0x01u8, 0x00]) >>
            hdr: length_value!(le_u16, item) >>
            (hdr)
        )
    );

    named!(pub integer<Value>,
        map!(
            map_res!(
                map_res!(
                    ws!(digit),
                    ::std::str::from_utf8
                ),
                ::std::str::FromStr::from_str
            ),
            Value::Integer
        )
    );

    named!(pub boolean<Value>,
        ws!(alt!(
            tag!("True") => { |_| Value::Bool(true) } |
            tag!("False") => { |_| Value::Bool(false) }
        ))
    );

    named!(pub string<Value>,
        map!(
            map!(
                map_res!(
                    ws!(alt!(
                        delimited!(tag!("\""),
                            is_not_s!("\""),
                            tag!("\"")) |
                        delimited!(tag!("\'"),
                            is_not_s!("\'"),
                            tag!("\'"))
                        )),
                    ::std::str::from_utf8
                ),
                |s: &str| s.to_string()
            ),
            Value::String
        )
    );

    named!(pub item<Value>, alt!(integer | boolean | string | list | map));

    named!(pub list<Value>,
        map!(
            ws!(alt!(
                delimited!(tag!("["),
                    terminated!(separated_list!(tag!(","), item), alt!(tag!(",") | tag!(""))),
                    tag!("]")) |
                delimited!(tag!("("),
                    terminated!(separated_list!(tag!(","), item), alt!(tag!(",") | tag!(""))),
                    tag!(")"))
            )),
            Value::List
        )
    );

    named!(pub map<Value>,
        map!(
            ws!(
                delimited!(tag!("{"),
                    terminated!(separated_list!(tag!(","),
                        separated_pair!(map_opt!(string, |it| match it { Value::String(s) => Some(s), _ => None }), tag!(":"), item)
                    ), alt!(tag!(",") | tag!(""))),
                    tag!("}"))
            ),
            |v: Vec<_>| Value::Map(v.into_iter().collect())
        )
    );
}

pub trait TensorType {
    fn data_type() -> &'static str;

    fn to_bytes(&self) -> Vec<u8>;

    fn to_floats(&self) -> Vec<f32>;
}

impl TensorType for f16 {
    fn data_type() -> &'static str {
        "FP16"
    }

    fn to_bytes(&self) -> Vec<u8> {
        self.to_bits().to_le_bytes().to_vec()
    }

    fn to_floats(&self) -> Vec<f32> {
        vec![f32::from(*self)]
    }
}

impl TensorType for f32 {
    fn data_type() -> &'static str {
        "FP32"
    }

    fn to_bytes(&self) -> Vec<u8> {
        self.to_bits().to_le_bytes().to_vec()
    }

    fn to_floats(&self) -> Vec<f32> {
        vec![*self]
    }
}

impl TensorType for f32x8 {
    fn data_type() -> &'static str {
        "FP32"
    }

    fn to_bytes(&self) -> Vec<u8> {
        let v = to_f32_array(*self);
        v.iter()
            .flat_map(|q| q.to_bits().to_le_bytes().to_vec())
            .collect()
    }

    fn to_floats(&self) -> Vec<f32> {
        to_f32_array(*self).to_vec()
    }
}

impl TensorType for f16x16 {
    fn data_type() -> &'static str {
        "FP16"
    }

    fn to_bytes(&self) -> Vec<u8> {
        let v = to_slice(*self);
        v.iter()
            .flat_map(|q| q.to_bits().to_le_bytes().to_vec())
            .collect()
    }

    fn to_floats(&self) -> Vec<f32> {
        to_slice(*self).iter().map(|v| f32::from(*v)).collect()
    }
}

impl FromNumpy<f32x8> for f32x8 {
    fn from_numpy_tensor(data: &[u8], shape: Vec<usize>) -> NumpyTensor<f32x8> {
        let f32x8_data = data
            .chunks(2 * f32x8_LENGTH)
            .map(|c| {
                let vc = c
                    .chunks(2)
                    .map(|c| f16::from_bits(u16::from_le_bytes([c[0], c[1]])))
                    .collect::<Vec<f16>>();
                crate::f32x8_simd::from_slice(&vc)
            })
            .collect::<Vec<f32x8>>();
        NumpyTensor::new(f32x8_data, shape, f32x8_LENGTH)
    }
}

impl FromNumpy<f16x16> for f16x16 {
    fn from_numpy_tensor(data: &[u8], shape: Vec<usize>) -> NumpyTensor<f16x16> {
        let f16x16_data = data
            .chunks(2 * f16x16_LENGTH)
            .map(|c| {
                let vc = c
                    .chunks(2)
                    .map(|c| f16::from_bits(u16::from_le_bytes([c[0], c[1]])))
                    .collect::<Vec<f16>>();
                crate::f16x16_simd::from_slice(&vc)
            })
            .collect::<Vec<f16x16>>();
        NumpyTensor::new(f16x16_data, shape, f16x16_LENGTH)
    }
}

/// A numpy tensor
#[derive(Clone)]
pub struct NumpyTensor<T: TensorType> {
    pub data: Vec<T>,
    pub shape: Vec<usize>,
    pub shape_multiplier: usize,
}

/// An iterator over a numpy tensor
pub struct NumpyTensorIterator<'a, T: TensorType> {
    data: &'a NumpyTensor<T>,
    index: usize,
}

impl<T: TensorType + FromNumpy<T>> NumpyTensor<T> {
    pub fn size(&self) -> usize {
        self.shape[0]
    }

    pub fn size_bytes(&self) -> usize {
        self.data.len() * std::mem::size_of::<T>()
    }

    pub fn new(data: Vec<T>, shape: Vec<usize>, shape_multiplier: usize) -> NumpyTensor<T> {
        NumpyTensor {
            data,
            shape,
            shape_multiplier,
        }
    }

    pub fn iter(&self) -> NumpyTensorIterator<'_, T> {
        NumpyTensorIterator {
            data: self,
            index: 0,
        }
    }

    pub fn to_floats(&self) -> Vec<f32> {
        self.data.iter().flat_map(|v| v.to_floats()).collect()
    }
}

impl<'a, T: TensorType> Iterator for NumpyTensorIterator<'a, T> {
    type Item = &'a [T];

    fn next(&mut self) -> Option<Self::Item> {
        if self.index >= self.data.shape[0] {
            None
        } else {
            let item = &self.data.data[self.index
                * (self.data.shape[1] / self.data.shape_multiplier)
                ..(self.index + 1) * self.data.shape[1] / self.data.shape_multiplier];
            self.index += 1;
            Some(item)
        }
    }
}

pub trait FromNumpy<T: TensorType> {
    fn from_numpy_tensor(data: &[u8], shape: Vec<usize>) -> NumpyTensor<T>;
}

impl FromNumpy<f16> for f16 {
    fn from_numpy_tensor(data: &[u8], shape: Vec<usize>) -> NumpyTensor<f16> {
        let f16_data = data
            .chunks(2)
            .map(|c| f16::from_bits(u16::from_le_bytes([c[0], c[1]])))
            .collect::<Vec<f16>>();
        NumpyTensor::new(f16_data, shape, 1)
    }
}

impl FromNumpy<f32> for f32 {
    fn from_numpy_tensor(data: &[u8], shape: Vec<usize>) -> NumpyTensor<f32> {
        let f32_data = data
            .chunks(2)
            .map(|c| f32::from(f16::from_bits(u16::from_le_bytes([c[0], c[1]]))))
            .collect::<Vec<f32>>();
        NumpyTensor::new(f32_data, shape, 1)
    }
}

/// Read a numpy tensor from a byte slice
///
/// The function only supports f16 tensors.
pub fn read_npy<T: FromNumpy<T> + TensorType>(bytes: Bytes) -> std::io::Result<NumpyTensor<T>> {
    let (data, header) = match parser::parse_header(&bytes) {
        IResult::Done(data, header) => Ok((data, header)),
        IResult::Incomplete(needed) => {
            Err(Error::new(ErrorKind::InvalidData, format!("{:?}", needed)))
        }
        IResult::Error(err) => Err(Error::new(ErrorKind::InvalidData, format!("{:?}", err))),
    }?;

    let ns: Vec<usize> = if let crate::parser::Value::Map(ref map) = header {
        if let Some(crate::parser::Value::List(l)) = map.get("shape") {
            let result = l
                .iter()
                .map(|i| {
                    if let crate::parser::Value::Integer(n) = i {
                        Ok(*n as usize)
                    } else {
                        Err(Error::new(ErrorKind::InvalidData, format!("{:?}", i)))
                    }
                })
                .collect::<Result<Vec<usize>>>()?;
            Some(result)
        } else {
            None
        }
    } else {
        None
    }
    .ok_or_else(|| {
        Error::new(
            ErrorKind::InvalidData,
            "\'shape\' field is not present or invalid.",
        )
    })?;

    if let crate::parser::Value::Map(ref map) = header {
        match map.get("descr") {
            Some(crate::parser::Value::String(s)) => {
                if s != "<f2" {
                    None
                } else {
                    Some(())
                }
            }
            _ => None,
        }
    } else {
        None
    }
    .ok_or_else(|| Error::new(ErrorKind::InvalidData, "Invalid dtype: not f16"))?;

    Ok(T::from_numpy_tensor(data, ns))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::f32x8_simd::f32x8_lowestf32;
    use half::f16;

    pub fn get_test_data_path(path: &str) -> std::path::PathBuf {
        use std::env;
        use std::fs;
        use std::path::PathBuf;

        let p = match env::var("BAZEL_TEST") {
            // test data is always available from the current working directory
            Ok(_) => path.into(),
            Err(_) => {
                // in cargo, the CARGO_MANIFEST_DIR environment variable is used to find the repo root
                let mut manifest_path = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
                manifest_path.push("../../..");
                manifest_path.push(path);
                manifest_path
            }
        };
        if let Err(e) = fs::metadata(&p) {
            panic!("Test data not found: {} error={:?}", p.to_str().unwrap(), e);
        }
        p
    }

    #[test]
    fn test_read_npy() {
        let content = std::fs::read(get_test_data_path(
            "base/rust/numpy/test_data/tk1_sk1_hello.npy",
        ))
        .unwrap();
        let bytes = Bytes::from(content);
        let tensor = read_npy::<f16>(bytes).unwrap();
        assert_eq!(tensor.shape, vec![4, 80]);
        assert_eq!(tensor.data.len(), 320);
        assert_eq!(tensor.data[0], f16::from_f32(0.343));
        assert_eq!(tensor.data[319], f16::from_f32(1.2266));
    }

    #[test]
    fn test_read_npy_as_f32() {
        let content = std::fs::read(get_test_data_path(
            "base/rust/numpy/test_data/tk1_sk1_hello.npy",
        ))
        .unwrap();
        let bytes = Bytes::from(content);
        let tensor = read_npy::<f32>(bytes).unwrap();
        assert_eq!(tensor.shape, vec![4, 80]);
        assert_eq!(tensor.data.len(), 320);
        assert_eq!(tensor.data[0], 0.34301758);
        assert_eq!(tensor.data[319], 1.2265625);
    }

    #[test]
    fn test_read_npy_as_f32x8() {
        let content = std::fs::read(get_test_data_path(
            "base/rust/numpy/test_data/tk1_sk1_hello.npy",
        ))
        .unwrap();
        let bytes = Bytes::from(content);
        let tensor = read_npy::<f32x8>(bytes).unwrap();
        assert_eq!(tensor.shape, vec![4, 80]);
        assert_eq!(tensor.data.len(), 40);
        assert_eq!(f32x8_lowestf32(tensor.data[0]), 0.34301758);
        assert_eq!(f32x8_lowestf32(tensor.data[39]), 0.3852539);

        let mut count = 0;
        for (i, e) in tensor.iter().enumerate() {
            assert_eq!(e.len(), 10);
            count += 1;
            if i == 0 {
                assert_eq!(f32x8_lowestf32(e[0]), 0.34301758);
            } else if i == 1 {
                assert_eq!(f32x8_lowestf32(e[0]), 1.4091797);
            }
        }
        assert_eq!(count, 4);
    }
}
