use std::arch::x86_64::*;

use half::f16;

#[allow(non_camel_case_types)]
pub type f32x8 = __m256;
#[allow(non_upper_case_globals)]
pub const f32x8_LENGTH: usize = 8;

#[inline]
#[cfg(test)] // only used in tests for now
#[allow(clippy::too_many_arguments)]
pub fn f32x8_set(v: f32, w: f32, x: f32, y: f32, z: f32, a: f32, b: f32, c: f32) -> f32x8 {
    unsafe { _mm256_set_ps(c, b, a, z, y, x, w, v) }
}

/// Return a 256-bit vector containing 8 infinity values of f32
#[inline]
#[cfg(test)] // only used in tests for now
pub fn f32x8_infty() -> f32x8 {
    unsafe { _mm256_set1_ps(f32::INFINITY) }
}

/// Return a 256-bit vector containing 8 0.0 values of f32
#[inline]
pub fn f32x8_nil() -> f32x8 {
    unsafe { _mm256_set1_ps(0.0_f32) }
}

#[inline]
pub fn f32x8_add(v: f32x8, w: f32x8) -> f32x8 {
    unsafe { _mm256_add_ps(v, w) }
}

#[inline]
pub fn f32x8_mul(v: f32x8, w: f32x8) -> f32x8 {
    unsafe { _mm256_mul_ps(v, w) }
}

/// Extract the lowest 32 bits of a 256-bit vector as a float
#[inline]
#[cfg(test)] // only used in tests for now
pub fn f32x8_lowestf32(v: f32x8) -> f32 {
    unsafe { _mm256_cvtss_f32(v) }
}

/// Return a 256-bit vector containing the elements of a slice
///
/// it doesn't take ownership of the slice
#[inline]
pub fn from_slice(s: &[f16]) -> f32x8 {
    assert_eq!(s.len(), f32x8_LENGTH);
    unsafe {
        _mm256_set_ps(
            s[7].into(),
            s[6].into(),
            s[5].into(),
            s[4].into(),
            s[3].into(),
            s[2].into(),
            s[1].into(),
            s[0].into(),
        )
    }
}

/// Return the sum of the elements of a 256-bit vector
#[inline]
pub fn f32x8_sum(v: f32x8) -> f32 {
    unsafe {
        let sum1 = _mm256_hadd_ps(v, v);
        let sum2 = _mm256_hadd_ps(sum1, sum1);
        let sum3 = _mm256_extractf128_ps(sum2, 1);
        let sum4 = _mm_add_ss(_mm256_castps256_ps128(sum2), sum3);
        _mm_cvtss_f32(sum4)
    }
}

/// Return the elements of a 256-bit vector as an array of 8 of f32
#[inline]
pub fn to_f32_array(v: f32x8) -> [f32; f32x8_LENGTH] {
    unsafe { std::mem::transmute(v) }
}

/// Prefetch the data at the given pointer
///
/// # Safety
/// The pointer is assumed to be valid for reads.
#[inline]
#[allow(dead_code)]
pub unsafe fn f32x8_prefetch(p: *const f32x8, offset: isize) {
    unsafe { _mm_prefetch(p.offset(offset) as *const i8, _MM_HINT_T0) }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_f32x8_infty() {
        let v = f32x8_infty();
        assert_eq!(f32x8_lowestf32(v), f32::INFINITY);
    }

    #[test]
    fn test_f32x8_nil() {
        let v = f32x8_nil();
        assert_eq!(f32x8_lowestf32(v), 0.0);
    }

    #[test]
    fn test_f32x8_from_slice() {
        let v_f32 = [10.0, 12.0, 14.0, 16.0, 18.0, 20.0, 22.0, 24.0];
        let v_f16 = v_f32.iter().map(|q| f16::from_f32(*q)).collect::<Vec<_>>();
        let v_f32x8 = from_slice(&v_f16);
        let r = to_f32_array(v_f32x8);
        assert_eq!(r, v_f32);
    }

    #[test]
    fn test_f32x8_add() {
        let v = f32x8_set(1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0);
        let w = f32x8_set(9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0);
        let v2 = f32x8_add(v, w);
        let r = to_f32_array(v2);
        assert_eq!(r, [10.0, 12.0, 14.0, 16.0, 18.0, 20.0, 22.0, 24.0]);

        let v = f32x8_infty();
        let w = f32x8_infty();
        let v2 = f32x8_add(v, w);
        let r = to_f32_array(v2);
        assert_eq!(
            r,
            [
                f32::INFINITY,
                f32::INFINITY,
                f32::INFINITY,
                f32::INFINITY,
                f32::INFINITY,
                f32::INFINITY,
                f32::INFINITY,
                f32::INFINITY
            ]
        );
    }

    #[test]
    fn test_f32x8_mul() {
        let v = f32x8_set(2.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0);
        let w = f32x8_set(9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0);
        let v2 = f32x8_mul(v, w);
        let r = to_f32_array(v2);
        assert_eq!(r, [18.0, 20.0, 33.0, 48.0, 65.0, 84.0, 105.0, 128.0]);
    }

    #[test]
    fn test_f32x8_sum() {
        let v = f32x8_set(2.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0);
        let v2 = f32x8_sum(v);
        assert_eq!(v2, 37.0);
    }
}
