use half::f16;

use std::arch::asm;
use std::arch::x86_64::*; // Intel SIMD intrinsic mappings

#[allow(non_camel_case_types)]
pub type f16x16 = __m256i;
#[allow(non_upper_case_globals)]
pub const f16x16_LENGTH: usize = 16;

pub fn is_f16x16_supported() -> bool {
    use raw_cpuid::{native_cpuid::CpuIdReaderNative, CpuIdReader};
    let cpuid_reader = CpuIdReaderNative {};
    // check avx512-fp16 bit: https://en.wikipedia.org/wiki/CPUID#EAX=7,_ECX=0:_Extended_Features
    let extended_features_raw = cpuid_reader.cpuid2(7, 0);
    extended_features_raw.edx & (1 << 23) != 0
}

#[inline]
#[allow(dead_code)]
pub fn f16x16_nil() -> f16x16 {
    unsafe { std::mem::transmute([f16::from_f32(0.0); f16x16_LENGTH]) }
}

#[inline]
pub fn from_slice(s: &[f16]) -> f16x16 {
    assert_eq!(s.len(), f16x16_LENGTH);
    let arr: [f16; f16x16_LENGTH] = [
        s[0], s[1], s[2], s[3], s[4], s[5], s[6], s[7], s[8], s[9], s[10], s[11], s[12], s[13],
        s[14], s[15],
    ];
    unsafe { std::mem::transmute(arr) }
}

#[inline]
pub fn to_slice(v: f16x16) -> [f16; f16x16_LENGTH] {
    unsafe { std::mem::transmute(v) }
}

#[inline]
#[cfg(test)] // only used in tests for now
#[target_feature(enable = "avx")]
unsafe fn f16x16_mul(a: f16x16, b: f16x16) -> f16x16 {
    let dst: f16x16;
    asm!(
        "vmulph {0}, {1}, {2}",
        out(ymm_reg) dst,
        in(ymm_reg) a,
        in(ymm_reg) b,
        options(pure, nomem, nostack),
    );
    dst
}

#[inline]
#[cfg(test)] // only used in tests for now
#[target_feature(enable = "avx")]
unsafe fn f16x16_add(a: f16x16, b: f16x16) -> f16x16 {
    let dst: f16x16;
    asm!(
        "vaddph {0}, {1}, {2}",
        out(ymm_reg) dst,
        in(ymm_reg) a,
        in(ymm_reg) b,
        options(pure, nomem, nostack),
    );
    dst
}

/// Multiply and add, writing the result to dst
///
/// # Safety
/// The two inputs are assumed to be valid f16x16 vectors, and dst is assumed to point to a valid f16x16
/// location.
#[inline]
#[target_feature(enable = "avx")]
pub unsafe fn f16x16_mul_add(a: f16x16, b: f16x16, dst: *mut f16x16) {
    asm!(
        "vfmadd231ph {0}, {1}, {2}",
        inout(ymm_reg) *dst,
        in(ymm_reg) a,
        in(ymm_reg) b,
        options(pure, nomem, nostack),
    );
}

/// Sums the 16 f16 values in the input vector using AVX intrinsics.
///
/// # Safety
/// This function is unsafe because it uses AVX, AVX512F, and AVX512FP16 intrinsics.
/// The caller must ensure that the CPU supports these features.
/// The code is compiled with target features enabled, but calling this on a CPU
/// without support will lead to an illegal instruction error.
/// Use `is_f16x16_supported()` to check for CPU support before calling this function.

#[inline]
pub fn f16x16_sum(a: f16x16) -> f32 {
    unsafe {
        // Convert 16xf16 into 2x 8xf32 vectors
        // a is __m256i. _mm256_cvtph_ps takes __m128i (lower 8 f16s) and returns __m256 (8 f32s)
        let lower_f16x8: __m128i = _mm256_castsi256_si128(a); // Extract lower 128 bits (8 x f16) - equivalent to _mm256_extracti128_si256(a, 0)
        let upper_f16x8: __m128i = _mm256_extracti128_si256(a, 1); // Extract upper 128 bits (8 x f16)

        let v_f32x8_lower: __m256 = _mm256_cvtph_ps(lower_f16x8); // Convert 8xf16 to 8xf32
        let v_f32x8_upper: __m256 = _mm256_cvtph_ps(upper_f16x8); // Convert 8xf16 to 8xf32

        // Sum each 8xf32 vector. This is similar to f32x8_sum
        // Sum lower 8xf32
        let sum1_lower = _mm256_hadd_ps(v_f32x8_lower, v_f32x8_lower);
        let sum2_lower = _mm256_hadd_ps(sum1_lower, sum1_lower);
        let sum3_lower = _mm256_extractf128_ps(sum2_lower, 1); // Extract upper 128 bits of sum2_lower
        let sum4_lower = _mm_add_ss(_mm256_castps256_ps128(sum2_lower), sum3_lower); // Add lower 128 of sum2_lower and sum3_lower
        let scalar_sum_lower: f32 = _mm_cvtss_f32(sum4_lower);

        // Sum upper 8xf32
        let sum1_upper = _mm256_hadd_ps(v_f32x8_upper, v_f32x8_upper);
        let sum2_upper = _mm256_hadd_ps(sum1_upper, sum1_upper);
        let sum3_upper = _mm256_extractf128_ps(sum2_upper, 1);
        let sum4_upper = _mm_add_ss(_mm256_castps256_ps128(sum2_upper), sum3_upper);
        let scalar_sum_upper: f32 = _mm_cvtss_f32(sum4_upper);

        scalar_sum_lower + scalar_sum_upper
    }
}

/// Prefetch the data at the given pointer
///
/// # Safety
/// The pointer is assumed to be valid for reads.
#[inline]
#[allow(dead_code)]
pub unsafe fn f16x16_prefetch(p: *const f16x16, offset: isize) {
    unsafe { _mm_prefetch(p.offset(offset) as *const i8, _MM_HINT_T0) }
}

#[cfg(test)]
mod tests {
    use super::*;

    // TODO: make these tests skip rather than pass on non-Sapphire-Rapids CPUs?
    #[test]
    fn test_add() {
        if !is_f16x16_supported() {
            return;
        }
        let a = [f16::from_f32(2.0); 16];
        let b = [f16::from_f32(3.0); 16];
        let c = unsafe { f16x16_add(from_slice(&a), from_slice(&b)) };
        let d = to_slice(c);
        assert_eq!(d, [f16::from_f32(5.0); 16]);
    }

    #[test]
    fn test_mul() {
        if !is_f16x16_supported() {
            return;
        }
        let a = [f16::from_f32(2.0); 16];
        let b = [f16::from_f32(3.0); 16];
        let c = unsafe { f16x16_mul(from_slice(&a), from_slice(&b)) };
        let d = to_slice(c);
        assert_eq!(d, [f16::from_f32(6.0); 16]);
    }

    #[test]
    fn test_mul_add() {
        if !is_f16x16_supported() {
            return;
        }
        let a = [f16::from_f32(2.0); 16];
        let b = [f16::from_f32(3.0); 16];
        let c = [f16::from_f32(10.0); 16];
        let mut cc = from_slice(&c);
        unsafe { f16x16_mul_add(from_slice(&a), from_slice(&b), &mut cc) };
        let d = to_slice(cc);
        assert_eq!(d, [f16::from_f32(16.0); 16]);
    }

    #[test]
    fn test_precision() {
        // f16 has 11 bits of precision, i.e. somewhere between 3 and 4 significant digits
        let expected = 9.87654 * 0.321 + 0.12345678;
        assert_eq!(expected, 3.293826);
        assert_eq!(f32::from(f16::from_f32(expected)), 3.2929688);

        if !is_f16x16_supported() {
            return;
        }

        let a = [f16::from_f32(9.87654); 16];
        let b = [f16::from_f32(0.321); 16];
        let c = [f16::from_f32(0.12345678); 16];
        let mut cc = from_slice(&c);
        unsafe { f16x16_mul_add(from_slice(&a), from_slice(&b), &mut cc) };
        let d = to_slice(cc);
        // The add-multiply itself is fully precision preserving, at least for these values
        assert_eq!(d, [f16::from_f32(expected); 16]);
    }

    #[test]
    fn test_sum() {
        if !is_f16x16_supported() {
            return;
        }
        let a = [f16::from_f32(5.0); 16];
        let b = f16x16_sum(from_slice(&a));
        assert_eq!(b, 80.0);
    }

    #[test]
    fn test_sum_sequential() {
        if !is_f16x16_supported() {
            return;
        }
        // Test with values 0.0 through 15.0
        let a = [
            f16::from_f32(0.0),
            f16::from_f32(1.0),
            f16::from_f32(2.0),
            f16::from_f32(3.0),
            f16::from_f32(4.0),
            f16::from_f32(5.0),
            f16::from_f32(6.0),
            f16::from_f32(7.0),
            f16::from_f32(8.0),
            f16::from_f32(9.0),
            f16::from_f32(10.0),
            f16::from_f32(11.0),
            f16::from_f32(12.0),
            f16::from_f32(13.0),
            f16::from_f32(14.0),
            f16::from_f32(15.0),
        ];
        let b = f16x16_sum(from_slice(&a));
        assert_eq!(b, 120.0); // Sum of 0+1+2+...+15 = 120
    }

    #[test]
    fn test_sum_precision() {
        if !is_f16x16_supported() {
            return;
        }
        // Test with fractions
        let a = [
            f16::from_f32(0.01),
            f16::from_f32(1.01),
            f16::from_f32(2.01),
            f16::from_f32(0.01),
            f16::from_f32(1.01),
            f16::from_f32(2.01),
            f16::from_f32(0.01),
            f16::from_f32(1.01),
            f16::from_f32(2.01),
            f16::from_f32(0.01),
            f16::from_f32(1.01),
            f16::from_f32(2.01),
            f16::from_f32(0.01),
            f16::from_f32(1.01),
            f16::from_f32(2.01),
            f16::from_f32(0.01),
        ];
        let b = f16x16_sum(from_slice(&a));
        // Sum should be close to 15.16
        assert!((15.155..=15.165).contains(&b));
    }
}
