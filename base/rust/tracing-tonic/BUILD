load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:rust.bzl", "rust_library")

rust_library(
    name = "tracing-tonic",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//third_party/ginepro",
    ],
)
