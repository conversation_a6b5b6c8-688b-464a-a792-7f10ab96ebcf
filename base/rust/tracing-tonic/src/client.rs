use ginepro::LoadBalancedChannel;
use http::Request;
use opentelemetry_http::HeaderInjector;
use pin_project_lite::pin_project;
use std::future::Future;
use std::pin::Pin;
use std::task::{Context, Poll};
use tonic::body::BoxBody;
use tonic::transport::Channel;
use tower::Service;
use tracing::Span;
use tracing_opentelemetry::OpenTelemetrySpanExt;

#[derive(Clone)]
enum InnerChannel {
    Channel(Channel),
    LoadBalancedChannel(LoadBalancedChannel),
}

// A Service is responsible for taking an HTTP request and returning an HTTP response.
// The TracingService delegates most of that responsibility to its inner channel, but
// adds some tracing before the request is sent and after the response is received.
//
// Since Service is part of an async framework, it actually returns a Future that
// returns an HTTP response.
#[derive(Clone)]
pub struct TracingService {
    // TODO: make this more generic by wrapping a Service, rather than this
    // enum of a Channel or LoadBalancedChannel
    inner: InnerChannel,
}

impl TracingService {
    pub fn new(channel: Channel) -> Self {
        TracingService {
            inner: InnerChannel::Channel(channel),
        }
    }
    pub fn new_load_balanced(load_balanced_channel: LoadBalancedChannel) -> Self {
        TracingService {
            inner: InnerChannel::LoadBalancedChannel(load_balanced_channel),
        }
    }
}

type Response = http::Response<BoxBody>;
type Error = tower::BoxError;

impl Service<Request<BoxBody>> for TracingService {
    type Response = Response;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Response, Error>> + Send>>;

    // Are we ready to accept a new HTTP request?
    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        // We're ready if our inner channel is ready
        match self.inner {
            InnerChannel::Channel(ref mut channel) => channel.poll_ready(cx).map_err(Into::into),
            InnerChannel::LoadBalancedChannel(ref mut channel) => {
                channel.poll_ready(cx).map_err(Into::into)
            }
        }
    }

    fn call(&mut self, req: Request<BoxBody>) -> Self::Future {
        let mut req = req;

        // Create span
        let span = tracing::info_span!("tonic",
            otel.name = %format!("gRPC {}", req.uri()),
            otel.kind = "client");

        opentelemetry::global::get_text_map_propagator(|propagator| {
            propagator.inject_context(
                &tracing::Span::current().context(),
                &mut HeaderInjector(req.headers_mut()),
            );
        });

        let fut = match self.inner {
            InnerChannel::Channel(ref mut channel) => span.in_scope(|| channel.call(req)),
            InnerChannel::LoadBalancedChannel(ref mut channel) => {
                span.in_scope(|| channel.call(req))
            }
        };

        // Wrap the future returned by the inner channel in our future, which
        // includes state that we're interested in.
        Box::pin(SpanFuture { inner: fut, span })
    }
}

pin_project! {
    // SpanFuture is responsible for holding onto the span
    // until the inner future finishes. Eventually, it will also
    // look at the response and add attributes to the span (e.g.
    // HTTP status code)
    pub struct SpanFuture<F> {
        #[pin]
        inner: F,
        span: Span,
    }
}

impl<F> Future for SpanFuture<F>
where
    // The specificity of having to write tonic::transport::Error feels fragile.
    // If a user places another service between TracingService and the channel,
    // the inner future might return a different error (e.g. the more generic Tower::BoxError)
    F: Future<Output = Result<Response, tonic::transport::Error>>,
{
    type Output = Result<Response, Error>;

    fn poll(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Self::Output> {
        let this = self.project();

        // Enter the span again while we poll our inner future. I believe this will extend
        // the end time of the span to be the end of this scope.
        this.span.in_scope(|| match this.inner.poll(cx) {
            Poll::Pending => Poll::<Self::Output>::Pending,
            Poll::Ready(outcome) => Poll::<Self::Output>::Ready(outcome.map_err(Into::into)),
        })
    }
}
