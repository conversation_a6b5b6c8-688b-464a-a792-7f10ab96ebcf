use http::Request;
use tracing::Span;
use tracing_opentelemetry::OpenTelemetrySpanExt;

pub fn trace_fn(req: &Request<()>) -> Span {
    // Extract the request_id if present
    let request_id = match req.headers().get("x-request-id") {
        Some(value) => value.to_str().unwrap_or_else(|_| {
            tracing::error!("Parse error x-request-id");
            ""
        }),
        None => "",
    };

    let span = tracing::info_span!(
        "tonic",
        otel.name = %req.uri().path(),
        otel.kind = "server",
        request_id = %request_id);

    span.set_parent(opentelemetry::global::get_text_map_propagator(|prop| {
        prop.extract(&opentelemetry_http::HeaderExtractor(req.headers()))
    }));

    span
}
