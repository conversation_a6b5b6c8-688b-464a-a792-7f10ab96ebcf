[package]
name = "tracing-tonic"
version = "0.1.0"
edition = "2021"

[dependencies]
futures-util = { workspace = true }
ginepro = { workspace = true }
http =  { workspace = true }
opentelemetry = { workspace = true }
opentelemetry-http =  { workspace = true }
opentelemetry_sdk =  { workspace = true }
pin-project-lite =  { workspace = true }
tonic = { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
tracing-opentelemetry = { workspace = true }
