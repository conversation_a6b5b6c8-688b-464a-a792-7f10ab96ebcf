load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library")

go_library(
    name = "leadership",
    srcs = ["leadership.go"],
    importpath = "github.com/augmentcode/augment/base/go/leadership",
    visibility = BASE_VISIBILITY,
    deps = [
        "@com_github_rs_zerolog//log",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/leaderelection",
        "@io_k8s_client_go//tools/leaderelection/resourcelock",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
