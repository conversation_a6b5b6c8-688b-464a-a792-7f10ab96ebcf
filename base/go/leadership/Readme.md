# Leadership

Kubernetes-based Leadership election.

## Example

```
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lockLib = import 'deploy/common/lock-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local appName = 'github-state';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );
  local lock = lockLib.createLock(namespace=namespace, appName=appName, serviceAccountName=serviceAccount.name);
  local config = {
    namespace: namespace,
    lock_name: lock.name,
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    lock.objects,
  ])
```
