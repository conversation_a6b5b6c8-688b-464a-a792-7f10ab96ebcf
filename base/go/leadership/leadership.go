package leadership

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Leadership interface {
	Run(ctx context.Context) error
	WaitForLeadership(ctx context.Context) error
}

type leadership struct {
	lockName   string
	namespace  string
	identity   string
	isLeaderCh chan struct{}
}

func New(lockName string, namespace string, identity string) Leadership {
	isLeaderCh := make(chan struct{})
	return &leadership{
		lockName:   lockName,
		namespace:  namespace,
		identity:   identity,
		isLeaderCh: isLeaderCh,
	}
}

func (l *leadership) WaitForLeadership(ctx context.Context) error {
	select {
	case <-l.isLeaderCh:
		return nil
	case <-ctx.Done():
		return status.Error(codes.Canceled, "Context canceled")
	}
}

func (l *leadership) Run(ctx context.Context) error {
	kubeConfig, err := rest.InClusterConfig()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get in-cluster config")
	}

	clientset, err := kubernetes.NewForConfig(kubeConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Kubernetes client")
	}

	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      l.lockName,
			Namespace: l.namespace,
		},
		Client: clientset.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: l.identity,
		},
	}

	leaderElectionConfig := leaderelection.LeaderElectionConfig{
		Lock:          lock,
		LeaseDuration: 15 * time.Second,
		RenewDeadline: 10 * time.Second,
		RetryPeriod:   2 * time.Second,
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				log.Info().Msg("Elected leader")
				l.isLeaderCh <- struct{}{}
			},
			OnStoppedLeading: func() {
				close(l.isLeaderCh)
			},
			OnNewLeader: func(leaderIdentity string) {
				if leaderIdentity == l.identity {
					// This is the current leader
					return
				}
				log.Info().Msgf("New leader elected: %s", leaderIdentity)
			},
		},
	}

	leaderElector, err := leaderelection.NewLeaderElector(leaderElectionConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create leader elector")
	}
	go leaderElector.Run(ctx)
	return nil
}
