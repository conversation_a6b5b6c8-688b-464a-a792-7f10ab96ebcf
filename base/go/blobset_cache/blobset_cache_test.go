package blobset_cache

import (
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/stretchr/testify/assert"
)

// Test basic cache setup and get/set operations
func TestBlobSetCache(t *testing.T) {
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Initial allocation should be 0
	assert.Equal(t, 0, cache.MemorySizeInBytes())

	tenantID := "tenant1"
	setID := "set1"
	key := BlobSetKey{TenantID: tenantID, SetID: setID}

	// Create proper blob names with 32-byte SHA-256 hashes
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
		blob_names.GetBlobName("path/to/file3.txt", []byte("content of file 3")),
	}

	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	// Memory usage should be non-zero now
	assert.Greater(t, cache.MemorySizeInBytes(), 0)

	retrievedBlobNames, err := cache.GetBlobSet(key)
	assert.NoError(t, err)
	assert.Equal(t, len(blobNames), len(retrievedBlobNames))

	for i, blobName := range blobNames {
		assert.Equal(t, string(blobName), string(retrievedBlobNames[i]))
	}

	assert.Equal(t, 1, cache.NumEntries())

	cache.DeleteBlobSet(key)
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err, "Expected error after deleting the blob set")
	assert.ErrorIs(t, err, ErrEntryNotFound, "Expected ErrEntryNotFound error")
	assert.Equal(t, 0, cache.NumEntries(), "Cache should be empty after deletion")

	// Test deleting a non-existent key
	nonExistentKey := BlobSetKey{TenantID: "nonexistent", SetID: "nonexistent"}
	err = cache.DeleteBlobSet(nonExistentKey)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound, "Deleting non-existent entry expected ErrEntryNotFound error")
	_, err = cache.GetBlobSet(nonExistentKey)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound, "Getting non-existent entry expected ErrEntryNotFound error")
}

// Test the eviction callback setup and invocation
func TestEvictionCallback(t *testing.T) {
	var evictedKey BlobSetKey
	var evictedBlobNames []blob_names.BlobName
	evictionCalled := false

	onEvict := func(key BlobSetKey, blobNames []blob_names.BlobName) {
		evictedKey = key
		evictedBlobNames = blobNames
		evictionCalled = true
	}

	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Millisecond /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Millisecond /*onEvict*/, onEvict)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	tenantID := "tenant1"
	setID := "set1"
	key := BlobSetKey{TenantID: tenantID, SetID: setID}

	// Create proper blob names with 32-byte SHA-256 hashes
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
	}

	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	retrievedBlobNames, err := cache.GetBlobSet(key)
	assert.NoError(t, err)
	assert.Equal(t, len(blobNames), len(retrievedBlobNames))

	// Wait for the TTL to expire and the cleanup to run
	time.Sleep(20 * time.Millisecond)

	// Try to get the data again, it should be evicted
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err)

	// Verify the eviction callback was called with the correct data
	assert.True(t, evictionCalled, "Eviction callback should have been called")
	assert.Equal(t, tenantID, evictedKey.TenantID)
	assert.Equal(t, setID, evictedKey.SetID)
	assert.Equal(t, len(blobNames), len(evictedBlobNames))
	for i, blobName := range blobNames {
		assert.Equal(t, string(blobName), string(evictedBlobNames[i]))
	}
}

// TestMemorySizeCalculation tests that the memory size calculation is accurate
func TestMemorySizeCalculation(t *testing.T) {
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 10,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Create blob names with known hex string lengths
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
	}

	// Verify each blob name is 64 characters (hex-encoded SHA-256)
	for _, blobName := range blobNames {
		assert.Equal(t, 64, len(string(blobName)), "Blob name should be 64 hex characters")
	}

	key := BlobSetKey{TenantID: "tenant1", SetID: "set1"}
	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	memoryUsage := cache.MemorySizeInBytes()

	// Expected calculation with raw array storage:
	// - 64 bytes for the blob data (2 blob names * 32 bytes each)
	// - 24 bytes for the slice header
	// - Total: 88 bytes
	expectedSize := (2 * 32) + 24
	assert.Equal(t, expectedSize, memoryUsage,
		"Memory usage should match expected calculation: %d bytes", expectedSize)
}

// TestLRUEviction tests that the least recently used entries are evicted when the cache is full
func TestLRUEviction(t *testing.T) {
	// Create a cache with 1MB limit
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Create a large number of entries to fill the cache
	// Each blob is 32 bytes, so we need many entries to exceed the 1MB limit
	// Using 10k blobs per set to fill the cache faster
	numBlobs := 10000
	for i := 0; i < 10; i++ {
		key := BlobSetKey{TenantID: "tenant1", SetID: fmt.Sprintf("set%d", i)}
		blobNames := make([]blob_names.BlobName, numBlobs)
		for j := 0; j < numBlobs; j++ {
			blobNames[j] = blob_names.GetBlobName(
				fmt.Sprintf("path/to/file%d_%d.txt", i, j),
				[]byte(fmt.Sprintf("content of file %d_%d", i, j)),
			)
		}
		err = cache.SetBlobSet(key, blobNames)
		assert.NoError(t, err)
	}

	// The cache should have evicted some entries to stay under the memory limit
	assert.Less(t, cache.NumEntries(), 10)
	assert.LessOrEqual(t, cache.MemorySizeInBytes(), 1024*1024)

	// The most recently used entry should still be in the cache
	key := BlobSetKey{TenantID: "tenant1", SetID: fmt.Sprintf("set%d", 9)}
	_, err = cache.GetBlobSet(key)
	assert.NoError(t, err)

	// The least recently used entry should have been evicted
	key = BlobSetKey{TenantID: "tenant1", SetID: fmt.Sprintf("set%d", 0)}
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)
}

// TestBlobsetCacheErrorIs tests the Is() method of BlobsetCacheError
func TestBlobsetCacheErrorIs(t *testing.T) {
	// Test that two errors with the same Code but different messages match
	err1 := &CacheError{Code: ErrCodeEntryNotFound, Msg: "test error 1"}
	err2 := &CacheError{Code: ErrCodeEntryNotFound, Msg: "test error 2"}

	// These should match because they have the same Code, even with different messages
	assert.True(t, errors.Is(err1, err2), "Errors with same Code should match")

	// Test that two errors with different Code values don't match
	err3 := &CacheError{Code: ErrCodeUnknown, Msg: "test error"}
	assert.False(t, errors.Is(err1, err3), "Errors with different Code should not match")
}

// TestBlobSetCacheStats tests the Stats method of BlobSetCache
func TestBlobSetCacheStats(t *testing.T) {
	// Create a new cache
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Initial stats should be zero
	stats := cache.Stats()
	assert.Equal(t, int64(0), stats.Hits, "Initial hits should be zero")
	assert.Equal(t, int64(0), stats.Misses, "Initial misses should be zero")

	// Create a key and blob names
	key1 := BlobSetKey{TenantID: "tenant1", SetID: "set1"}
	key2 := BlobSetKey{TenantID: "tenant1", SetID: "set2"}
	nonExistentKey := BlobSetKey{TenantID: "nonexistent", SetID: "nonexistent"}

	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
	}

	// Try to get a non-existent key - should increment misses
	_, err = cache.GetBlobSet(nonExistentKey)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)

	stats = cache.Stats()
	assert.Equal(t, int64(0), stats.Hits, "Hits should still be zero")
	assert.Equal(t, int64(1), stats.Misses, "Misses should be incremented to 1")

	// Add an entry
	err = cache.SetBlobSet(key1, blobNames)
	assert.NoError(t, err)

	// Get the entry - should increment hits
	_, err = cache.GetBlobSet(key1)
	assert.NoError(t, err)

	stats = cache.Stats()
	assert.Equal(t, int64(1), stats.Hits, "Hits should be incremented to 1")
	assert.Equal(t, int64(1), stats.Misses, "Misses should still be 1")

	// Get the entry again - should increment hits again
	_, err = cache.GetBlobSet(key1)
	assert.NoError(t, err)

	stats = cache.Stats()
	assert.Equal(t, int64(2), stats.Hits, "Hits should be incremented to 2")
	assert.Equal(t, int64(1), stats.Misses, "Misses should still be 1")

	// Try to get another non-existent key - should increment misses
	_, err = cache.GetBlobSet(key2)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)

	stats = cache.Stats()
	assert.Equal(t, int64(2), stats.Hits, "Hits should still be 2")
	assert.Equal(t, int64(2), stats.Misses, "Misses should be incremented to 2")

	// Add the second entry
	err = cache.SetBlobSet(key2, blobNames)
	assert.NoError(t, err)

	// Get both entries - should increment hits twice
	_, err = cache.GetBlobSet(key1)
	assert.NoError(t, err)
	_, err = cache.GetBlobSet(key2)
	assert.NoError(t, err)

	stats = cache.Stats()
	assert.Equal(t, int64(4), stats.Hits, "Hits should be incremented to 4")
	assert.Equal(t, int64(2), stats.Misses, "Misses should still be 2")

	// Delete an entry and try to get it - should increment misses
	err = cache.DeleteBlobSet(key1)
	assert.NoError(t, err)
	_, err = cache.GetBlobSet(key1)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)

	stats = cache.Stats()
	assert.Equal(t, int64(4), stats.Hits, "Hits should still be 4")
	assert.Equal(t, int64(3), stats.Misses, "Misses should be incremented to 3")
}

// TestBlobNameSerialization tests the round-trip conversion of blobName to raw bytes and back
func TestBlobNameSerialization(t *testing.T) {
	tests := []struct {
		name        string
		description string
		blobName    blob_names.BlobName
		expectError bool
	}{
		{
			name:        "ValidSHA256Hash",
			description: "Standard 64-character hex SHA-256 hash",
			blobName:    blob_names.GetBlobName("path/to/file.txt", []byte("content")),
			expectError: false,
		},
		{
			name:        "AnotherValidHash",
			description: "Different valid SHA-256 hash",
			blobName:    blob_names.GetBlobName("different/path.txt", []byte("different content")),
			expectError: false,
		},
		{
			name:        "EmptyPathAndContent",
			description: "Hash of empty path and content",
			blobName:    blob_names.GetBlobName("", []byte("")),
			expectError: false,
		},
		{
			name:        "LongPathAndContent",
			description: "Hash with very long path and content",
			blobName:    blob_names.GetBlobName(strings.Repeat("very/long/path/", 100), []byte(strings.Repeat("very long content ", 1000))),
			expectError: false,
		},
		{
			name:        "SpecialCharacters",
			description: "Hash with special characters in path and content",
			blobName:    blob_names.GetBlobName("path/with/特殊字符/and/émojis/🚀.txt", []byte("content with special chars: ñáéíóú and symbols: @#$%^&*()")),
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Verify the blob name is a valid 64-character hex string
			blobNameStr := string(tt.blobName)
			assert.Equal(t, 64, len(blobNameStr), "BlobName should be 64 hex characters")

			// Test round-trip conversion: blobName → raw → blobName
			rawName, err := blobNameToRaw(tt.blobName)
			if tt.expectError {
				assert.Error(t, err, "Expected error for test case: %s", tt.description)
				return
			}
			assert.NoError(t, err, "blobNameToRaw should not error for valid input: %s", tt.description)

			// Verify the raw name is exactly 32 bytes
			assert.Equal(t, 32, len(rawName), "RawBlobName should be exactly 32 bytes")

			// Convert back to blob name
			convertedBlobName := rawToBlobName(rawName)

			// Verify the round-trip conversion preserves the original data exactly
			assert.Equal(t, tt.blobName, convertedBlobName, "Round-trip conversion should preserve original blobName exactly: %s", tt.description)
			assert.Equal(t, blobNameStr, string(convertedBlobName), "String representation should match exactly: %s", tt.description)
		})
	}
}

// TestBlobNameSerializationErrorHandling tests error cases for invalid blob names
func TestBlobNameSerializationErrorHandling(t *testing.T) {
	errorTests := []struct {
		name        string
		description string
		blobName    blob_names.BlobName
	}{
		{
			name:        "InvalidHexCharacters",
			description: "Blob name with invalid hex characters",
			blobName:    blob_names.BlobName("invalid_hex_characters_xyz123"),
		},
		{
			name:        "TooShort",
			description: "Blob name that's too short",
			blobName:    blob_names.BlobName("abc123"),
		},
		{
			name:        "TooLong",
			description: "Blob name that's too long",
			blobName:    blob_names.BlobName("a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890abcdef"),
		},
		{
			name:        "EmptyString",
			description: "Empty blob name",
			blobName:    blob_names.BlobName(""),
		},
		{
			name:        "OddLength",
			description: "Blob name with odd number of characters",
			blobName:    blob_names.BlobName("a1b2c3d4e5f6789012345678901234567890123456789012345678901234567"),
		},
	}

	for _, tt := range errorTests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := blobNameToRaw(tt.blobName)
			assert.Error(t, err, "Expected error for invalid blob name: %s", tt.description)
		})
	}
}

// TestBatchBlobNameSerialization tests the batch conversion functions
func TestBatchBlobNameSerialization(t *testing.T) {
	// Create a variety of blob names for testing
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("file1.txt", []byte("content1")),
		blob_names.GetBlobName("file2.txt", []byte("content2")),
		blob_names.GetBlobName("file3.txt", []byte("content3")),
		blob_names.GetBlobName("", []byte("")), // Edge case: empty path and content
		blob_names.GetBlobName("special/path/with/unicode/🚀.txt", []byte("unicode content: ñáéíóú")),
	}

	t.Run("BatchRoundTripConversion", func(t *testing.T) {
		// Test batch conversion: []blobName → []raw → []blobName
		rawNames, err := convertBlobNamesToRaw(blobNames)
		assert.NoError(t, err, "convertBlobNamesToRaw should not error for valid inputs")
		assert.Equal(t, len(blobNames), len(rawNames), "Raw names slice should have same length as input")

		// Verify each raw name is exactly 32 bytes
		for i, rawName := range rawNames {
			assert.Equal(t, 32, len(rawName), "RawBlobName[%d] should be exactly 32 bytes", i)
		}

		// Convert back to blob names
		convertedBlobNames := convertRawToBlobNames(rawNames)
		assert.Equal(t, len(blobNames), len(convertedBlobNames), "Converted blob names slice should have same length as input")

		// Verify each blob name matches exactly
		for i, originalBlobName := range blobNames {
			assert.Equal(t, originalBlobName, convertedBlobNames[i], "Blob name[%d] should match exactly after round-trip conversion", i)
			assert.Equal(t, string(originalBlobName), string(convertedBlobNames[i]), "String representation should match exactly for blob name[%d]", i)
		}
	})

	t.Run("EmptySlice", func(t *testing.T) {
		// Test with empty slice
		emptyBlobNames := []blob_names.BlobName{}
		rawNames, err := convertBlobNamesToRaw(emptyBlobNames)
		assert.NoError(t, err, "convertBlobNamesToRaw should handle empty slice")
		assert.Equal(t, 0, len(rawNames), "Raw names slice should be empty")

		convertedBlobNames := convertRawToBlobNames(rawNames)
		assert.Equal(t, 0, len(convertedBlobNames), "Converted blob names slice should be empty")
	})

	t.Run("SingleElement", func(t *testing.T) {
		// Test with single element
		singleBlobName := []blob_names.BlobName{blob_names.GetBlobName("single.txt", []byte("single content"))}
		rawNames, err := convertBlobNamesToRaw(singleBlobName)
		assert.NoError(t, err, "convertBlobNamesToRaw should handle single element")
		assert.Equal(t, 1, len(rawNames), "Raw names slice should have one element")

		convertedBlobNames := convertRawToBlobNames(rawNames)
		assert.Equal(t, 1, len(convertedBlobNames), "Converted blob names slice should have one element")
		assert.Equal(t, singleBlobName[0], convertedBlobNames[0], "Single blob name should match exactly")
	})

	t.Run("BatchErrorHandling", func(t *testing.T) {
		// Test batch conversion with one invalid blob name
		mixedBlobNames := []blob_names.BlobName{
			blob_names.GetBlobName("valid1.txt", []byte("content1")),
			blob_names.BlobName("invalid_hex_xyz"), // Invalid hex
			blob_names.GetBlobName("valid2.txt", []byte("content2")),
		}

		_, err := convertBlobNamesToRaw(mixedBlobNames)
		assert.Error(t, err, "convertBlobNamesToRaw should error when any blob name is invalid")
	})
}

// TestRawBlobNameDataIntegrity tests that the raw binary data is preserved correctly
func TestRawBlobNameDataIntegrity(t *testing.T) {
	// Create a known blob name and verify the raw bytes match expected values
	blobName := blob_names.GetBlobName("test.txt", []byte("test content"))

	// Convert to raw
	rawName, err := blobNameToRaw(blobName)
	assert.NoError(t, err, "blobNameToRaw should not error")

	// Verify the raw bytes can be decoded back to the same hex string
	expectedHex := string(blobName)
	actualHex := string(rawToBlobName(rawName))
	assert.Equal(t, expectedHex, actualHex, "Raw bytes should decode to the same hex string")

	// Verify the raw bytes are exactly what we expect from hex decoding
	expectedBytes, err := blob_names.DecodeHexBlobName(blobName)
	assert.NoError(t, err, "DecodeHexBlobName should not error for valid blob name")
	assert.Equal(t, expectedBytes, rawName[:], "Raw bytes should match hex-decoded bytes")
}
