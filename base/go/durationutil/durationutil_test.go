package durationutil

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestDuration(t *testing.T) {
	var d JSONDuration
	assert.NoError(t, d.<PERSON><PERSON>([]byte(`"1h"`)))
	assert.Equal(t, time.Hour, d.ToDuration())
	assert.NoError(t, d.<PERSON>([]byte(`"10m"`)))
	assert.Equal(t, 10*time.Minute, d.ToDuration())
	assert.NoError(t, d.<PERSON>mar<PERSON>([]byte(`"7s"`)))
	assert.Equal(t, 7*time.Second, d.ToDuration())
}

// Check that durations need an explicit unit.
func TestDurationNoUnit(t *testing.T) {
	var d JSONDuration
	assert.Error(t, d.Unmarshal<PERSON>([]byte(`"1"`)))
}
