load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "durationutil",
    srcs = ["durationutil.go"],
    importpath = "github.com/augmentcode/augment/base/go/durationutil",
    visibility = BASE_VISIBILITY,
    deps = [],
)

go_test(
    name = "durationutil_test",
    srcs = ["durationutil_test.go"],
    embed = [":durationutil"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)
