package clock

import "time"

type Clock interface {
	Now() time.Time
	Since(time.Time) time.Duration
}

// Clock with real timing that just calls standard `time` functions.
type RealClock struct{}

func NewRealClock() Clock {
	return &RealClock{}
}

func (c *RealClock) Now() time.Time {
	return time.Now()
}

func (c *RealClock) Since(t time.Time) time.Duration {
	return time.Since(t)
}

// Mock Clock with a configurable time.
type MockClock struct {
	MockNow time.Time
}

func NewMockClock(now time.Time) Clock {
	return &MockClock{MockNow: now}
}

func (c *MockClock) Now() time.Time {
	return c.MockNow
}

func (c *MockClock) Since(t time.Time) time.Duration {
	return c.MockNow.Sub(t)
}
