package secretstring

import (
	"fmt"
)

type SecretString struct {
	inner string
}

// Check that we implement common interfaces for printing values
var (
	_ fmt.Stringer   = &SecretString{}
	_ fmt.GoStringer = &SecretString{}
)

func (ss SecretString) String() string {
	panic("***")
}

func (ss SecretString) GoString() string {
	panic("***")
}

func (ss SecretString) Expose() string {
	return ss.inner
}

func New(s string) SecretString {
	return SecretString{inner: s}
}
