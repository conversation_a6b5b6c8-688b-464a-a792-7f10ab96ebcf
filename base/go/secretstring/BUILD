load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "secretstring_go",
    srcs = ["secretstring.go"],
    importpath = "github.com/augmentcode/augment/base/go/secretstring",
    visibility = BASE_VISIBILITY,
    deps = [],
)

go_test(
    name = "secretstring_go_test",
    srcs = ["secretstring_test.go"],
    embed = [":secretstring_go"],
    deps = [
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
    ],
)
