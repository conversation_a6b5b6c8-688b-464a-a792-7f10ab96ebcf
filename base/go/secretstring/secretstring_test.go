package secretstring

import (
	"encoding/json"
	"fmt"
	"strings"
	"testing"
)

func TestSecretString(t *testing.T) {
	ss := New("test")
	if ss.Expose() != "test" {
		t.<PERSON><PERSON>("Expose() should return the inner string")
	}
}

func TestSecretStringNegative(t *testing.T) {
	failureCases := []func(SecretString) string{
		func(ss SecretString) string {
			return fmt.Sprintf(" %s", ss)
		},
		func(ss SecretString) string {
			return fmt.Sprintf(" %v", ss)
		},
		func(ss SecretString) string {
			return fmt.Sprintf(" %+v", ss)
		},
		func(ss SecretString) string {
			return fmt.Sprintf(" %#v", ss)
		},
		func(ss SecretString) string {
			out, err := json.<PERSON>(ss)
			if err != nil {
				t.<PERSON><PERSON><PERSON>("Error marshalling: %v", err)
			}
			return string(out)
		},
	}

	ss := New("mysecret")
	for i, f := range failureCases {
		func() {
			out := f(ss)
			if strings.Contains(string(out), "mysecret") {
				t.<PERSON>("Output of case %d contains the secret: %q", i, out)
			}
		}()
	}
}
