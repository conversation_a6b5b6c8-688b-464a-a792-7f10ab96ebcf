load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//visibility:public"],
)

ts_project(
    name = "ts_systemenv",
    srcs = ["systemenv.ts"],
    allow_js = True,
    composite = True,
    declaration = True,
    incremental = True,
    out_dir = "ts_dist",
    resolve_json_module = True,
    ts_build_info_file = "ts_dist/.tsbuildinfo",
    tsconfig = ":tsconfig",
    visibility = ["//visibility:public"],
    deps = [
        ":node_modules/@types/node",
    ],
)

js_library(
    name = "pkg",
    srcs = [
        "package.json",
        ":ts_systemenv",
    ],
    visibility = ["//visibility:public"],
)
