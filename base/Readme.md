# Base

The `base` directory contains code that is shared between many different services, tools, and applications.

Some code in `base` is shared with the research infrastructure:
- `augment_client`
- `fastforward`
- `languages`
- `prompt_format`
- `registry`
- `tokenizers`
- `blob_names/python`
- `retrieval`
- `caching`

Production infrastructure relies on bazel, while the research infrastructure does
not. Shared code must run with bazel, but should not assume it is running inside a
bazel sandbox. Among other things, this means:
  - The current working directory could be anywhere
  - Files generated by a bazel build (e.g. a shared library for a native Python
    module) need to be copied from the bazel build output directory to some
    location where they can be found (currently into the source tree).
  - To do this, please run the command `bazel run -c opt //base:install`.


When contributing to `base`, please:
- Keep the code quality high. This code will have to be read by many people.
- Add reviewers who are familiar with the production infrastructure to the PR.
- Never add sensitive information to logs.


## Tips and conventions

- Prefer a more concrete package if applicable. `//base` shouldn't be a dumping ground
  for everything.
- Consider all users of a unit of code before making changes.
- Use small targets to avoid pulling in more dependencies than needed.
- Prefer small files that expose few public classes; you can always list the full
  library in its `__init__.py`.
- Provide tests: As the code is shared between different users, quality is critical.
- Prefer `pytest` conventions and `pytest` fixtures for your tests.
- Use `logging`. Do not use structlog (`structlog.get_logger()`), as it may cause issues
  in `research`.
