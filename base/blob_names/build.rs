// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::PathBuf};

fn get_base_dir() -> PathBuf {
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../").canonicalize().unwrap();
    root
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let root = get_base_dir();

    let blob_names_path = root.join("base/blob_names/blob_names.proto");
    tonic_build::configure()
        .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")
        .compile_protos(&[&blob_names_path], &[blob_names_path.parent().unwrap()])?;

    Ok(())
}
