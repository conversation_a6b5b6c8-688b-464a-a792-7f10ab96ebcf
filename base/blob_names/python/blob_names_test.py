from base.blob_names.python import blob_names
import itertools
import pytest

Blobs = blob_names.Blobs


# Behavior of Blobs is generally independent of the baseline checkpoint id
# unless they differ between two Blobs objects being compared. So all tests
# should work with or without a defined baseline checkpoint id.
@pytest.fixture(scope="module", params=[None, "some_baseline"])
def baseline(request):
    yield request.param


def blist(*blob_names):
    return Blobs.from_fake_blob_names(blob_names).added


def test_baseline_change(baseline):
    b1 = Blobs(baseline_checkpoint_id=baseline)
    b2 = Blobs(baseline_checkpoint_id="some_other_baseline")
    assert isinstance(blob_names.compute_diff(b1, b2), blob_names.BlobsBaselineDiff)
    assert isinstance(blob_names.compute_diff(b2, b1), blob_names.BlobsBaselineDiff)


def test_identity(baseline):
    empty = Blobs(baseline_checkpoint_id=baseline)
    blobs = Blobs(
        baseline_checkpoint_id=baseline,
        added=blist("blob1", "blob2"),
        deleted=blist("blob3", "blob4"),
    )
    for case in [empty, blobs]:
        diff = blob_names.compute_diff(case, case)
        assert isinstance(diff, blob_names.BlobsDiff)
        assert diff.unique_to_rhs == []
        assert diff.unique_to_lhs == []


def test_add(baseline):
    orig = Blobs(baseline_checkpoint_id=baseline)
    new = Blobs(baseline_checkpoint_id=baseline, added=blist("blob1", "blob2"))
    diff = blob_names.compute_diff(orig, new)
    assert isinstance(diff, blob_names.BlobsDiff)
    assert diff.unique_to_rhs == new.added
    assert diff.unique_to_lhs == []


def test_delete(baseline):
    orig = Blobs(baseline_checkpoint_id=baseline)
    new = Blobs(baseline_checkpoint_id=baseline, deleted=blist("blob1", "blob2"))
    diff = blob_names.compute_diff(orig, new)
    assert isinstance(diff, blob_names.BlobsDiff)
    assert diff.unique_to_rhs == []
    assert diff.unique_to_lhs == new.deleted


def test_un_add(baseline):
    orig = Blobs(baseline_checkpoint_id=baseline, added=blist("blob1"))
    new = Blobs(baseline_checkpoint_id=baseline)
    diff = blob_names.compute_diff(orig, new)
    assert isinstance(diff, blob_names.BlobsDiff)
    assert diff.unique_to_rhs == []
    assert diff.unique_to_lhs == orig.added


def test_un_delete(baseline):
    orig = Blobs(baseline_checkpoint_id=baseline, deleted=blist("blob1"))
    new = Blobs(baseline_checkpoint_id=baseline)
    diff = blob_names.compute_diff(orig, new)
    assert isinstance(diff, blob_names.BlobsDiff)
    assert diff.unique_to_rhs == orig.deleted
    assert diff.unique_to_lhs == []


def test_combination(baseline):
    orig = Blobs(
        baseline_checkpoint_id=baseline,
        added=blist("blob1", "blob2"),
        deleted=blist("blob3", "blob4"),
    )
    # Add blob5, un-add blob2
    # Delete blob6, un-delete blob3
    new = Blobs(
        baseline_checkpoint_id=baseline,
        added=blist("blob1", "blob5"),
        deleted=blist("blob4", "blob6"),
    )
    diff = blob_names.compute_diff(orig, new)
    assert isinstance(diff, blob_names.BlobsDiff)
    assert diff.unique_to_rhs, blist("blob5" == "blob3")
    assert diff.unique_to_lhs, blist("blob2" == "blob6")


def test_subtract():
    # Cases above won't give good coverage of modes of sequence exhaustion
    # in _subtract
    sub = blob_names._subtract
    lists: list[tuple[bytes, ...]] = [tuple()]
    for i in range(1, 6):
        lists.extend(itertools.combinations([b"a", b"b", b"c", b"d", b"e"], i))
    for a, b in itertools.combinations(lists, 2):
        assert list(sub(a, b)) == sorted(list(set(a) - set(b)))
        assert list(sub(b, a)) == sorted(list(set(b) - set(a)))
