load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "blob_names",
    srcs = ["blob_names.py"],
    visibility = ["//visibility:public"],
    deps = [
        requirement("structlog"),
        "//base/blob_names:blob_names_py_proto",
    ],
)

pytest_test(
    name = "blob_names_test",
    srcs = ["blob_names_test.py"],
    deps = [
        ":blob_names",
    ],
)

py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        ":blob_names",
    ],
)
