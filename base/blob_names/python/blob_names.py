"""Blob name calculation."""

import dataclasses
import hashlib
import heapq
import typing
import structlog

from base.blob_names import blob_names_pb2

logger = structlog.get_logger()

BlobName = str
"""Type alias for blob name."""

FilePath = str
"""Type alias for file path."""


def get_blob_name(path: str, contents: bytes | str) -> BlobName:
    """Calculate the blob name for the given path name and contents."""
    hasher = hashlib.sha256()
    hasher.update(path.encode("utf-8"))
    if isinstance(contents, str):
        contents = contents.encode("utf-8")
    hasher.update(contents)
    return hasher.hexdigest()


def is_sorted(blob_names: typing.Sequence[bytes]) -> bool:
    """Returns true if the given list of blob names is sorted."""
    return all(blob_names[i] <= blob_names[i + 1] for i in range(len(blob_names) - 1))


def encode_blob_name(blob_name_hex: BlobName) -> bytes:
    """Convert a blob name from hex to bytes."""
    return bytes.fromhex(blob_name_hex)


def decode_blob_name(blob_name_bytes: bytes) -> BlobName:
    """Convert a blob name from bytes to hex."""
    return blob_name_bytes.hex()


@dataclasses.dataclass
class Blobs:
    """Class containing the blob names in delta format.

    This class mirrors the Blobs proto definition.
    """

    baseline_checkpoint_id: typing.Optional[str] = None
    added: typing.Sequence[bytes] = ()
    deleted: typing.Sequence[bytes] = ()

    def is_empty(self) -> bool:
        """Returns true if the blobs object is empty."""
        return self.baseline_checkpoint_id is None and len(self.added) == 0

    @staticmethod
    def from_fake_blob_names(blob_names: typing.Sequence[str]) -> "Blobs":
        """Convenience function to convert a list of fake string blob_names to a Blobs object.

        Mainly used by tests to convert raw strings ("blob1", "foo") to hex
        encoded strings then call from_blob_names() below
        """
        blob_names_hex = [x.encode("utf-8").hex() for x in blob_names]
        return Blobs.from_blob_names(blob_names_hex)

    @staticmethod
    def from_blob_names(blob_names_hex: typing.Sequence[BlobName]) -> "Blobs":
        """Convenience function to convert a list of hex blob_names to a Blobs object.

        Mainly used to convert from the old blob_names hex format to the bytes
        format used by the Blobs object.
        """
        blob_names_hex = sorted(blob_names_hex)
        return Blobs(
            baseline_checkpoint_id=None,
            added=list(map(bytes.fromhex, blob_names_hex)),
            deleted=[],
        )

    @staticmethod
    def from_proto(proto: blob_names_pb2.Blobs) -> "Blobs":
        """Convenience function to convert a Blobs proto to a Blobs object."""
        if not is_sorted(proto.added):
            logger.error("Added blob names are not sorted")
            raise ValueError("Added blob names are not sorted")
        if not is_sorted(proto.deleted):
            logger.error("Deleted blob names are not sorted")
            raise ValueError("Deleted blob names are not sorted")

        # Make sure to check if baseline_checkpoint_id exists, since if it
        # is not set it will default to "". source:
        # https://protobuf.dev/getting-started/pythontutorial/#protocol-format
        baseline_checkpoint_id = (
            proto.baseline_checkpoint_id
            if proto.HasField("baseline_checkpoint_id")
            else None
        )
        return Blobs(
            baseline_checkpoint_id=baseline_checkpoint_id,
            added=proto.added,
            deleted=proto.deleted,
        )

    def to_proto(self) -> blob_names_pb2.Blobs:
        """Convenience function to convert a Blobs object to a Blobs proto."""
        if not is_sorted(self.added):
            logger.error("Added blob names are not sorted")
            raise ValueError("Added blob names are not sorted")
        if not is_sorted(self.deleted):
            logger.error("Deleted blob names are not sorted")
            raise ValueError("Deleted blob names are not sorted")

        return blob_names_pb2.Blobs(
            baseline_checkpoint_id=self.baseline_checkpoint_id,
            added=self.added,
            deleted=self.deleted,
        )


@dataclasses.dataclass
class BlobsDiff:
    """Representation of the difference between two Blobs objects that share the same
    baseline checkpoint. Relative to one of the Blobs objects, not to a baseline!
    """

    unique_to_lhs: typing.Sequence[bytes] = ()
    unique_to_rhs: typing.Sequence[bytes] = ()


class BlobsBaselineDiff:
    """Sentinel result when two Blobs objects have different baselines."""

    pass


def compute_diff(
    left: Blobs, right: Blobs
) -> typing.Union[BlobsDiff, BlobsBaselineDiff]:
    if left.baseline_checkpoint_id != right.baseline_checkpoint_id:
        return BlobsBaselineDiff()
    # Iterate each sequence at least once and at most twice, because merge(subtract, subtract) is easy to write
    rhs_only_added = _subtract(right.added, left.added)
    lhs_only_added = _subtract(left.added, right.added)
    rhs_only_deleted = _subtract(right.deleted, left.deleted)
    lhs_only_deleted = _subtract(left.deleted, right.deleted)
    return BlobsDiff(
        unique_to_lhs=list(heapq.merge(lhs_only_added, rhs_only_deleted)),
        unique_to_rhs=list(heapq.merge(rhs_only_added, lhs_only_deleted)),
    )


def _subtract(
    sorted_a: typing.Iterable[bytes], sorted_b: typing.Iterable[bytes]
) -> typing.Iterable[bytes]:
    iter_a = iter(sorted_a)
    try:
        a = next(iter_a)
    except StopIteration:
        return
    for b in sorted_b:
        while a <= b:
            if a < b:
                yield a
            try:
                a = next(iter_a)
            except StopIteration:
                return
    yield a
    yield from iter_a
