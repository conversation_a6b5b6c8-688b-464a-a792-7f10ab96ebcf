"""Utility for blob names."""

import argparse
import pathlib

from base.blob_names.python.blob_names import get_blob_name


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--path", type=str, help="Overrides the pathname to use")
    parser.add_argument(
        "--file", type=pathlib.Path, help="Path to a file containing the content"
    )
    parser.add_argument("--content", help="Content to use")

    args = parser.parse_args()

    if not args.file and not args.content:
        print("Must specify --file or --content")
        return
    if args.file and args.content:
        print("Must specify only one of --file or --content")
        return

    if args.content:
        if args.path is None:
            print("Must specify --path when using --content")
            return
        path: str = args.path
        contents: bytes = args.content.encode()
    else:
        if args.path is not None:
            path = args.path
        else:
            path = str(args.file)
        contents = args.file.read_bytes()
    print(get_blob_name(path, contents))


if __name__ == "__main__":
    main()
