syntax = "proto3";
package base.blob_names;

// Specifies a set of blobs using a checkpoint id as a baseline
// collection, along with changes to the baseline.
//
// The blob name is a general concept:
// A blob name is a sha256 in binary format of the path (optional) and content when stored as 'bytes'
// Often, it is stored as string where it is a sha256 in hex format of the path (optional) and content.
message Blobs {
  // Name that represents a set of blobs on the server. Unset if there
  // is no baseline checkpoint.
  optional string baseline_checkpoint_id = 1;

  // List of blob names to add to the baseline checkpoint.
  //
  // name of blob objects that the client believes the server has for the given model
  //
  // Each name is a sha256 in binary format of the path (optional) and content.

  repeated bytes added = 2;

  // List of blob names to remove from the baseline checkpoint.
  repeated bytes deleted = 3;
}

// representation of a blob name either in hex or binary format
message BlobName {
  oneof encoding {
    // sha256 in binary format of the path (optional) and content
    // if sent in protobuf JSON, be sure to use base64 encoding
    bytes name = 1;

    // sha256 in hex format of the path (optional) and content
    string name_hex = 2;
  }
}
