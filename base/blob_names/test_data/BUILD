load("@aspect_bazel_lib//lib:copy_to_bin.bzl", "copy_to_bin")

filegroup(
    name = "blob-name-test-data",
    srcs = [
        "blob-0",
        "blob-0.name",
    ],
    visibility = ["//base/blob_names:__subpackages__"],
)

copy_to_bin(
    name = "blob-name-test-data-js",
    srcs = [":blob-name-test-data"],
    visibility = [
        "//clients/sidecar/libs:__subpackages__",
        "//clients/vscode:__subpackages__",
    ],
)
