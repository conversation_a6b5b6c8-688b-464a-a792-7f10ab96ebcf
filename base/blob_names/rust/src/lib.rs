use secrecy::{ExposeSecret, SecretString, SecretVec};
use serde::Serialize;
use sha2::{Digest, Sha256};

pub const BLOB_DIGEST_LEN: usize = 32;

// TODO(aswin,brandon): replace use of this and util::B<PERSON>b<PERSON><PERSON> in content manager with
// blob_names::BlobName, then make this function private
// This crate's BlobName is backed by the binary digest, while BlobName in content
// manager stores the hex string; keep this function for now to avoid bringing both
// names into a single scope.
pub fn get_blob_name(path_name: &SecretString, content: &SecretVec<u8>) -> String {
    String::from(&BlobName::new_from_contents(path_name, content))
}

// This is a small wrapper around a Sha256 result to be used for blob names,
// and can be converted to a hex string as needed or when formatting.
#[derive(Default, <PERSON>lone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct BlobName {
    digest: [u8; BLOB_DIGEST_LEN],
}

impl BlobName {
    // Create a new BlobName from a string.
    pub fn new(name: &str) -> Result<Self, tonic::Status> {
        let mut res = Self {
            digest: Default::default(),
        };
        hex::decode_to_slice(name, res.digest.as_mut_slice()).map_err(|e| {
            tracing::warn!("Invalid blob name: {}", e);
            tonic::Status::invalid_argument("Invalid blob name")
        })?;
        Ok(res)
    }

    // Create a new BlobName from a byte array.
    // The byte array must be 32 bytes and is a binary encoding of the blob name.
    pub fn from_bytes(bytes: &[u8]) -> Result<Self, tonic::Status> {
        Ok(Self {
            digest: bytes
                .try_into()
                .map_err(|_| tonic::Status::invalid_argument("Blob name invalid length"))?,
        })
    }

    // Create a new BlobName from a path name and contents.
    pub fn new_from_contents(path_name: &SecretString, content: &SecretVec<u8>) -> Self {
        let mut hasher = Sha256::new();
        hasher.update(path_name.expose_secret().as_bytes());
        hasher.update(content.expose_secret());
        Self {
            digest: hasher.finalize().into(),
        }
    }

    pub fn as_bytes(&self) -> &[u8; BLOB_DIGEST_LEN] {
        &self.digest
    }
}

impl std::fmt::Display for BlobName {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        // hex lib doesn't give easy access to iterator of chars;
        // This means to_string() may allocate two Strings, depending on how
        // good the optimizer is
        write!(f, "{}", hex::encode(self.digest))
    }
}

impl std::fmt::Debug for BlobName {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "BlobName({})", hex::encode(self.digest))
    }
}

impl std::convert::TryFrom<&str> for BlobName {
    type Error = tonic::Status;

    fn try_from(s: &str) -> Result<Self, Self::Error> {
        Self::new(s)
    }
}

// Cheaper conversion to String than Display
// Debatable whether we should hint at the internal representation by
// requiring this conversion to use a method named "encode_hex"
impl std::convert::From<&BlobName> for String {
    fn from(blob_name: &BlobName) -> Self {
        hex::encode(blob_name.digest)
    }
}

#[derive(Default, Serialize, Clone, PartialEq, Eq)]
pub struct SortedBlobNameBytesVec {
    blob_names: Vec<Vec<u8>>,
}

impl SortedBlobNameBytesVec {
    // Create a new sorted vector of Blob Names, in bytes.
    pub fn new(blob_names: Vec<Vec<u8>>) -> Self {
        // It's very possible that this vector is already sorted - but we don't
        // want to crash if it's not, so go ahead and sort it anyway to be safe.
        // The sort() implementation should be fast if the vector is already
        // sorted, so it shouldn't add too much overhead.
        let mut sorted_blob_names = blob_names.clone();
        sorted_blob_names.sort();
        Self {
            blob_names: sorted_blob_names,
        }
    }

    pub fn as_vec(&self) -> &Vec<Vec<u8>> {
        &self.blob_names
    }
}

#[cfg(test)]
mod tests {
    use crate::{get_blob_name, BlobName};
    use secrecy::{SecretString, SecretVec};
    use sha2::{Digest, Sha256};
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    use std::path::PathBuf;
    use std::{env, fs};

    // Location of the repo root relative to this crate (the dir containing Cargo.toml)
    const REPO_ROOT: &str = "../../..";

    // Path name of the sample blob relative to the repo root. This path is used to open
    // the blob file and is also an input to the blob name computation (hence it is important
    // that it be a path that is relative to the repo root).
    const BLOB_PATH: &str = "base/blob_names/test_data/blob-0";

    // Path name of the file containing the sample blob's name, relative to the repo root.
    const EXPECTED_BLOBNAME_PATH: &str = "base/blob_names/test_data/blob-0.name";

    fn get_test_data_path(path: &str) -> PathBuf {
        match env::var("BAZEL_TEST") {
            // test data is always available from the current working directory
            Ok(_) => path.into(),
            Err(_) => {
                // in cargo, the CARGO_MANIFEST_DIR environment variable is used to find the repo root
                let mut manifest_path = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
                manifest_path.push(REPO_ROOT);
                manifest_path.push(path);
                manifest_path
            }
        }
    }

    #[test]
    fn test_blob_names() {
        // Read the sample blob and compute its name
        let contents = fs::read(get_test_data_path(BLOB_PATH)).unwrap();
        let blob_name = get_blob_name(
            &SecretString::new(BLOB_PATH.to_string()),
            &SecretVec::new(contents),
        );

        // Read the expected blob name
        let expected_blob_name = fs::read_to_string(get_test_data_path(EXPECTED_BLOBNAME_PATH))
            .unwrap()
            .trim()
            .to_string();

        assert_eq!(blob_name, expected_blob_name);
    }

    #[test]
    fn test_blob_name() {
        let mut blob_name_str = hex::encode(Sha256::digest(b"hello world"));
        BlobName::new(&blob_name_str).expect("valid blob name should pass");

        blob_name_str.push('0');
        BlobName::new(&blob_name_str).expect_err("long blob name should fail");
        blob_name_str.pop();
        blob_name_str.pop();
        BlobName::new(&blob_name_str).expect_err("short blob name should fail");
        blob_name_str.push('g');
        BlobName::new(&blob_name_str).expect_err("invalid blob name should fail");
    }

    #[test]
    fn test_blob_name_from_bytes() {
        let mut input_bytes = vec![
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
            24, 25, 26, 27, 28, 29, 30, 31,
        ];
        let blob_name = BlobName::from_bytes(&input_bytes).expect("blob name should be 32 bytes");
        assert_eq!(
            String::from(&blob_name),
            "000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f"
        );

        input_bytes.pop();
        BlobName::from_bytes(&input_bytes).expect_err("blob name should be 32 bytes");
        input_bytes.push(255);
        BlobName::from_bytes(&input_bytes).expect("blob name should be 32 bytes");
        input_bytes.push(255);
        BlobName::from_bytes(&input_bytes).expect_err("blob name should be 32 bytes");
    }

    #[test]
    fn test_blob_name_from_contents() {
        let contents = fs::read(get_test_data_path(BLOB_PATH)).unwrap();
        let blob_name = BlobName::new_from_contents(
            &SecretString::new(BLOB_PATH.to_string()),
            &SecretVec::new(contents),
        );

        let expected_blob_name = fs::read_to_string(get_test_data_path(EXPECTED_BLOBNAME_PATH))
            .unwrap()
            .trim()
            .to_string();

        assert_eq!(String::from(&blob_name), expected_blob_name);
        assert_eq!(format!("{}", blob_name), expected_blob_name);
        assert_eq!(
            format!("{:?}", blob_name),
            format!("BlobName({})", expected_blob_name)
        );
    }

    #[test]
    fn test_blob_names_to_bytes() {
        let blob_name =
            BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401d")
                .expect("blob name should be valid");
        println!("blob name bytes: {:?}", blob_name.digest);

        // invalid blob name
        BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401z")
            .expect_err("blob name invalid; should fail to construct");
    }

    #[test]
    fn test_eq() {
        let blob_name =
            BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401d")
                .unwrap();
        let blob_name_2 =
            BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401d")
                .unwrap();
        assert_eq!(blob_name, blob_name_2);

        let blob_name_3 =
            BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401e")
                .unwrap();
        assert_ne!(blob_name, blob_name_3);
    }

    fn hash<T: Hash>(t: &T) -> u64 {
        let mut s = DefaultHasher::new();
        t.hash(&mut s);
        s.finish()
    }

    #[test]
    fn test_hash() {
        let blob_name =
            BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401d")
                .unwrap();
        let blob_name_2 =
            BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401d")
                .unwrap();
        assert_eq!(hash::<BlobName>(&blob_name), hash::<BlobName>(&blob_name_2));

        let blob_name_3 =
            BlobName::new("a85228551f39a77bcf7a892eec8eba3542be14dbc282ca093e1ee1cfe767401e")
                .unwrap();
        assert_ne!(hash::<BlobName>(&blob_name), hash::<BlobName>(&blob_name_3));
    }
}
