load("@crates//:defs.bzl", "all_crate_deps")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

rust_library(
    name = "blob_names",
    srcs = glob(["src/**/*.rs"]),
    edition = "2021",
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ),
)

rust_test(
    name = "test_blob_names",
    crate = ":blob_names",
    data = (
        "//base/blob_names/test_data:blob-name-test-data",
    ),
    deps = all_crate_deps(
        normal = True,
    ),
)
