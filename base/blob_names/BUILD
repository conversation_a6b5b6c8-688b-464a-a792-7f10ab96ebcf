load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_library", "go_proto_library", "go_test")
load("//tools/bzl:python.bzl", "py_proto_library")
load("//tools/bzl:rust.bzl", "rust_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    data = [
        ":blob_names_py_proto",
    ],
    visibility = ["//base:__subpackages__"],
)

proto_library(
    name = "blob_names_proto",
    srcs = ["blob_names.proto"],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

py_proto_library(
    name = "blob_names_py_proto",
    protos = [":blob_names_proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "blob_names_go_proto",
    importpath = "github.com/augmentcode/augment/base/blob_names/proto",
    proto = ":blob_names_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "blob_names_go",
    srcs = [
        "blob_names.go",
    ],
    importpath = "github.com/augmentcode/augment/base/blob_names",
    visibility = ["//visibility:public"],
    deps = [
        ":blob_names_go_proto",
    ],
)

go_test(
    name = "blob_names_go_test",
    srcs = [
        "blob_names_test.go",
    ],
    data = [
        "//base/blob_names/test_data:blob-name-test-data",
    ],
    embed = [":blob_names_go"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)

ts_proto_library(
    name = "blob_names_ts_proto",
    node_modules = "//:node_modules",
    proto = ":blob_names_proto",
    visibility = ["//visibility:public"],
)

rust_library(
    name = "blob_names_rs_proto",
    srcs = ["blob_names_proto.rs"],
    aliases = aliases(),
    crate_name = "blob_names_rs_proto",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":blob_names_rs_proto_gen",
    ],
)

cargo_build_script(
    name = "blob_names_rs_proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        ":blob_names_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)
