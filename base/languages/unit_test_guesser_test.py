"""Unit test file for the unit test guesser.

bazel test //base/languages:unit_test_guesser_test

TODO: add more unit test for all languages.
"""

import copy

from base.languages.languages import LanguageId, Languages
from base.languages.unit_test_guesser import (
    is_unit_test,
    default_unit_test_guesser,
)


def test_coverage():
    """Test that the patterns cover all languages."""
    guesser = default_unit_test_guesser()
    assert guesser.patterns_by_lang.keys() == set(Languages) | {None}


def test_python():
    """Test the is_unit_test function for Python."""
    unit_test_paths = [
        "augment/base/static_analysis/signature_utils_test.py",
        "augment/base/static_analysis/test_signature_utils.py",
        "tests/pyright/pyright_example.py",
        "a/tests/x.py",
        "a/unit-test/x.py",
        "test.py",
        "FooTest.py",
        "Alphassembly/assembler/run_tests.py",
        "test/mirrors.py",
        "TestFoo.py",
        "my-test.py",
        "conftest.py",
    ]
    for x in copy.deepcopy(unit_test_paths):
        unit_test_paths.append(f"/home/<USER>/{x}")

    non_unit_test_paths = [
        "augment/base/static_analysis/signature_utils.cpp",
        "/home/<USER>/augment/base/languages/unit_test_guesser.py",
        "augment/tested.py",
        "tests/pyright/pyright_examplepy",
        "load_test/foo.py",
        "OrderService/queue_tESt_receiver.py",
    ]
    for path in unit_test_paths:
        assert is_unit_test(path)
    for path in non_unit_test_paths:
        assert not is_unit_test(path)


def test_cpp():
    """Test the is_unit_test function for C++."""
    unit_test_paths = [
        "test.cpp",
        "Test.h",
        "ATest.c++",
        "Tests.c++",
        "src/messaging/tests/MessageTest.cpp",
        "src/messaging/tests/TestExchange.cpp",
    ]
    for x in copy.deepcopy(unit_test_paths):
        unit_test_paths.append(f"/home/<USER>/{x}")

    non_unit_test_paths = ["ErrorCategory.cpp", "HelloWorld.cxx", "tested.cpp"]
    for path in unit_test_paths:
        assert is_unit_test(path)
    for path in non_unit_test_paths:
        assert not is_unit_test(path)


def test_csharp():
    """Test the is_unit_test function for C++."""
    unit_test_paths = [
        "Assets/ZimGui/Demo/ZimGUITest.cs",
    ]

    non_unit_test_paths = ["ErrorCategory.cs"]
    for path in unit_test_paths:
        assert is_unit_test(path)
    for path in non_unit_test_paths:
        assert not is_unit_test(path)


def test_java():
    """Test the is_unit_test function for Java."""
    unit_test_paths = [
        "gpu_test/src/main/java/com/juziml/content/gpu_test/GpuTestAct.java",
        "gpu_test/GpuTestCurlAnimView.java",
    ]
    non_unit_test_paths = [
        "teST.java",
    ]
    for path in unit_test_paths:
        assert is_unit_test(path)
    for path in non_unit_test_paths:
        assert not is_unit_test(path)


def test_typescript():
    """Test the is_unit_test function for TypeScript."""
    unit_test_paths = [
        "a/test.ts",
        "foo.test.ts",
        "src/test-base.ts",
        "src/test_base.ts",
        "src/__test__/helper.ts",
        "A.Tests/yolo.ts",
        "src/internal/pitch-detector/__tests__/pitch-detector.spec.ts",
        "src/commands/generateTest/generateTestAction.ts",
    ]
    for x in copy.deepcopy(unit_test_paths):
        unit_test_paths.append(f"/home/<USER>/{x}")

    non_unit_test_paths = [
        "ErrorCategory.ts",
        "HelloWorld.tsx",
        "tested.ts",
        "src/latesthits.ts",
    ]
    for path in unit_test_paths:
        assert is_unit_test(path)
    for path in non_unit_test_paths:
        assert not is_unit_test(path)


def test_markdown():
    """Test the is_unit_test function for Markdown."""
    unit_test_paths = ["tests/README.md", "a/tests/intro.md"]
    non_unit_test_paths = [
        "a/test.md",
        "foo.test.md",
    ]
    for path in unit_test_paths:
        assert is_unit_test(path)
    for path in non_unit_test_paths:
        assert not is_unit_test(path)
