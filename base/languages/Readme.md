# languages

Code for:
- types for supported and understood languages.
- figuring out the programming language of a file based on the extension or VS.Code
  language type.
- how to write inline comments for each language.

## Usage

```python
from typing import assert_never, assert_type
from base import languages

def do_thing(unsafe_language: str, code: str):
    # check_language makes sure your language is a canonical language string.
    assert languages.check_language(unsafe_language), (
        f"{unsafe_language}? Never heard of it. Try one of {languages.Languages}"
    )
    # the type checker now knows it's a canonical language id.
    assert_type(languages, languages.LanguageId)
    # Do thing with language

def format_prompt_as_comment(path: str, prompt: str):
    # Utility to guess language from a path.
    language = languages.guess_language(path)
    # Another utility to guess a comment prefix that will work.
    comment_prefix = languages.guess_comment_prefix(language)
    if comment_prefix is None:
        return None
    return "".join(
        comment_prefix + line
        for line in prompt.splitlines(keepends=True)
    )

# SupportedLanguageId is a type literal for all the first-party languages we support.
def do_language_specific_thing(language: languages.SupportedLanguageId)
    # Type checker makes sure you don't test for "cpp" by mistake.
    if language == "C++":
        # Do C++ thing
    elif language == "Python":
        # Do Python thing
    ...
    else:
        # Type checker makes sure you support all the languages
        assert_never()
```
