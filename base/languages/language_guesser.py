"""Module to guess properties of the programming language.

Usage:
    >>> from base.languages import guess_language, guess_comment_prefix
    >>> assert guess_language("/path/to/foo.cpp") == "C++"
    >>> assert guess_comment_prefix("C++") == "// "
    >>> assert guess_comment_prefix("/path/to/foo.cpp") == "// "
"""

import functools
import pathlib
import typing
from dataclasses import dataclass

from base.languages.languages import (
    LanguageId,
    LanguageInfo,
    MultiLineComment,
    default_languages,
)


class LanguageGuesser(typing.Protocol):
    """Protocol to guess properties of a programming language for a file."""

    def get_language(self, path: pathlib.Path | str | None) -> LanguageId | None:
        """Returns a guess of the programming language for the given the path.

        Returns None if unable to guess the language.
        """
        raise NotImplementedError()

    def get_comment_prefix(self, lang: LanguageId | None) -> str | None:
        """Returns a string that make the remaining of the line a comment.

        This is programming language dependent.
        Returns None if lang is None or if there is no good answer (e.g. in CSS).
        """
        raise NotImplementedError()

    def get_multi_line_comments(
        self, lang: LanguageId | None
    ) -> list[MultiLineComment]:
        """Returns a list of possible ways to make multiline comments.

        This is programming language dependent.
        Returns empty if lang is None or if there is no good answer.
        """
        raise NotImplementedError()


@dataclass(frozen=True)
class LanguageMapLanguageGuesser(LanguageGuesser):
    """Guesses language propoerties based on a static language mapping."""

    extension_map: dict[str, LanguageId]
    """Maps from file extension to language."""

    prefix_map: dict[LanguageId, str]
    """Maps from language to comment prefix."""

    multi_line_comments: dict[LanguageId, list[MultiLineComment]]
    """Maps from language to multi-line comments."""

    @staticmethod
    def from_languages(
        languages: dict[LanguageId, LanguageInfo],
    ) -> "LanguageMapLanguageGuesser":
        """Creates a language guesser from json data."""
        extension_map: dict[str, LanguageId] = {}
        prefix_map: dict[LanguageId, str] = {}
        multi_line_comments: dict[LanguageId, list[MultiLineComment]] = {}

        for lang, lang_info in languages.items():
            for extension in lang_info.extensions:
                extension_map[extension] = lang
            prefix_map[lang] = lang_info.comment_prefix
            multi_line_comments[lang] = lang_info.multi_line_comments

        # Note: C and C++ overlap in that both commonly use ".h" files.
        # We're going to give priority to C++.
        if ".h" in extension_map:
            extension_map[".h"] = "C++"
        return LanguageMapLanguageGuesser(
            extension_map, prefix_map, multi_line_comments
        )

    def get_language(self, path: pathlib.Path | str | None) -> LanguageId | None:
        if path is None:
            return None
        return self.extension_map.get(pathlib.Path(path).suffix)

    def get_comment_prefix(self, lang: LanguageId | None) -> str | None:
        return lang and self.prefix_map.get(lang)

    def get_multi_line_comments(
        self, lang: LanguageId | None
    ) -> list[MultiLineComment]:
        if lang is None:
            return []

        return self.multi_line_comments.get(lang, [])


@functools.cache
def default_language_guesser() -> LanguageGuesser:
    """Get the default language guesser."""
    return LanguageMapLanguageGuesser.from_languages(default_languages())


def guess_language(path: str | pathlib.Path | None) -> LanguageId | None:
    """Guess the language using the path."""
    return default_language_guesser().get_language(path)


def guess_comment_prefix(lang: LanguageId | None) -> str | None:
    """Guess the comment prefix for the language."""
    return default_language_guesser().get_comment_prefix(lang)
