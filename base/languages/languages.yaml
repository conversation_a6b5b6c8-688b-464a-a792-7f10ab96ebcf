# canonical information about programming languages
# where possible, references https://github.com/microsoft/vscode/tree/main/extensions
# NOTE(arun): When changing this list, please update the corresponding list of language
# names in `base/languages/languages.py`.
# NOTE(jeff): go-jsonnet has a bug decoding YAML when a certain string, s = '-' * 3,
# is in the file. It will then misinterpret the file as an array.
# Unfortunately the fix is not in 0.20.0. Luckily our tests will catch this.
# See: https://github.com/google/go-jsonnet/issues/673.
{
  C:
    # https://github.com/microsoft/vscode/blob/main/extensions/cpp/package.json
    {
      name: 'C',
      vscode_name: 'c',
      extensions: [
        '.c',
        '.cats',
        '.h',
        '.idc',
        '.w',
      ],
      # we use C99 comment. The goal is to find a comment until the end of the line or more general
      # a way to create a comment that cannot be "broken" by the commented out text, /* style comments
      # make that hard.
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  'C++':
    # https://github.com/microsoft/vscode/blob/main/extensions/cpp/package.json
    {
      name: 'C++',
      vscode_name: 'cpp',
      extensions: [
        '.cpp',
        '.c++',
        '.cc',
        '.cp',
        '.cxx',
        '.h',  # note that the extensions list is NOT (and cannot be) mutally exclusive
        '.h++',
        '.hh',
        '.hpp',
        '.hxx',
        '.inc',
        '.inl',
        '.ipp',
        '.tcc',
        '.tpp',
      ],
      comment_prefix: '// ',  # https://en.cppreference.com/w/cpp/comment
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Go:
    # https://github.com/microsoft/vscode/blob/main/extensions/go/package.json
    {
      name: 'Go',
      vscode_name: 'go',
      extensions: [
        '.go',
      ],
      comment_prefix: '// ',  # https://go.dev/doc/effective_go#commentary
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Java:
    # https://github.com/microsoft/vscode/blob/main/extensions/java/package.json
    {
      name: 'Java',
      vscode_name: 'java',
      extensions: [
        '.java',
      ],
      comment_prefix: '// ',  # https://www.oracle.com/java/technologies/javase/codeconventions-comments.html
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  JavaScript:
    # https://github.com/microsoft/vscode/blob/main/extensions/javascript/package.json
    {
      name: 'JavaScript',
      vscode_name: 'javascript',
      extensions: [
        '.js',
        '._js',
        '.bones',
        '.es',
        '.es6',
        '.frag',
        '.gs',
        '.jake',
        '.jsb',
        '.jscad',
        '.jsfl',
        '.jsm',
        '.jss',
        '.njs',
        '.pac',
        '.sjs',
        '.ssjs',
        '.sublime-build',
        '.sublime-commands',
        '.sublime-completions',
        '.sublime-keymap',
        '.sublime-macro',
        '.sublime-menu',
        '.sublime-mousemap',
        '.sublime-project',
        '.sublime-settings',
        '.sublime-theme',
        '.sublime-workspace',
        '.sublime_metrics',
        '.sublime_session',
        '.xsjs',
        '.xsjslib',
      ],
      comment_prefix: '// ',  # https://www.w3schools.com/js/js_comments.asp
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  TypeScript:
    # https://github.com/microsoft/vscode/blob/main/extensions/typescript-basics/package.json
    {
      name: 'TypeScript',
      vscode_name: 'typescript',
      extensions: [
        '.ts',
        '.tsx',
      ],
      # https://tsdoc.org/
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  'TypeScript JSX':
    # https://github.com/microsoft/vscode/blob/main/extensions/typescript-basics/package.json
    {
      name: 'TypeScript JSX',
      vscode_name: 'typescriptreact',
      extensions: [
        '.tsx',
      ],
      # https://tsdoc.org/. Note - have to escape into JS
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  'JavaScript JSX':
    {
      name: 'JavaScript JSX',
      vscode_name: 'javascriptreact',
      extensions: [
        '.jsx',
      ],
      # https://stackoverflow.com/questions/30766441/how-to-use-comments-in-react
      # Note - have to escape into JS
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  HTML:
    # https://github.com/microsoft/vscode/blob/main/extensions/html/package.json
    {
      name: 'HTML',
      vscode_name: 'html',
      extensions: [
        '.html',
        '.htm',
        '.xhtml',
      ],
      # https://www.w3schools.com/html/html_comments.asp
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '<!--',
          suffix: '-->',
        },
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Rust:
    # https://github.com/microsoft/vscode/blob/main/extensions/rust/package.json
    {
      name: 'Rust',
      vscode_name: 'rust',
      extensions: [
        '.rs',
      ],
      # https://doc.rust-lang.org/book/ch03-04-comments.html
      # https://doc.rust-lang.org/reference/comments.html
      # NOTE: `/*` is allowed but discouraged for documentation, preferring `///`
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Python:
    # https://github.com/microsoft/vscode/blob/main/extensions/python/package.json
    {
      name: 'Python',
      vscode_name: 'python',
      extensions: [
        '.py',
        '.bzl',
        '.cgi',
        '.fcgi',
        '.gyp',
        '.lmi',
        '.pyde',
        '.pyp',
        '.pyt',
        '.pyw',
        '.rpy',
        '.tac',
        '.wsgi',
        '.xpy',
        '.ipynb',
      ],
      # https://docs.python.org/3/reference/lexical_analysis.html#comments
      comment_prefix: '# ',
      # NOTE: multi-line comments may also be strings
      multi_line_comments: [
        {
          prefix: '"""',
          suffix: '"""',
        },
        {
          prefix: "'''",
          suffix: "'''",
        },
      ],
    },
  CSharp:
    # https://github.com/microsoft/vscode/blob/main/extensions/csharp/package.json
    {
      name: 'C#',
      vscode_name: 'csharp',
      extensions: [
        '.cs',
        '.cake',
        '.csx',
      ],
      # https://learn.microsoft.com/en-us/dotnet/csharp/language-reference/tokens/comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Cuda:
    # https://github.com/microsoft/vscode/blob/main/extensions/cpp/package.json
    {
      name: 'Cuda',
      vscode_name: 'cuda-cpp',
      extensions: [
        '.cu',
        '.cuh',
      ],
      # https://docs.nvidia.com/cuda/cuda-c-programming-guide/index.html
      # Just C++
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Shell:
    # https://github.com/microsoft/vscode/blob/main/extensions/shellscript/package.json
    {
      name: 'Shell',
      vscode_name: 'shellscript',
      extensions: [
        '.sh',
        '.bash',
        '.zsh',
        '.bashrc',
        '.bash_profile',
        '.bash_aliases',
      ],
      # https://www.gnu.org/software/bash/manual/bash.html#Comments
      # NOTE: No standardized way to do multi-line comments.
      comment_prefix: '# ',
      multi_line_comments: [],
    },
  Perl:
    # https://github.com/microsoft/vscode/blob/main/extensions/perl/package.json
    {
      name: 'perl',
      vscode_name: 'perl',
      extensions: [
        '.pl',
      ],
      # https://perldoc.perl.org/perlsyn
      # https://perldoc.perl.org/perlpodspec
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '\n=pod',
          suffix: '=cut\n',
        },
        {
          prefix: '\n=head',
          suffix: '=cut\n',
        },
        {
          prefix: '\n=begin',
          suffix: '=cut\n',
        },
        {
          prefix: '\n=for',
          suffix: '=cut\n',
        },
      ],

    },
  PHP:
    # https://github.com/microsoft/vscode/blob/main/extensions/php/package.json
    {
      name: 'PHP',
      vscode_name: 'php',
      extensions: [
        '.php',
        '.php3',
        '.php4',
        '.php5',
        '.phps',
        '.phtml',
      ],
      # https://www.php.net/manual/en/language.basic-syntax.comments.php
      # TODO: '//' is also allowed, and seems more popular.
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Lua:
    # https://github.com/microsoft/vscode/blob/main/extensions/lua/package.json
    {
      name: 'Lua',
      vscode_name: 'lua',
      extensions: [
        '.lua',
      ],
      # https://www.lua.org/pil/1.3.html
      # NOTE: s = `"-"+"--[["` is commonly used to uncomment a block comment,
      # but we ignore that case.
      comment_prefix: '-- ',
      multi_line_comments: [
        {
          prefix: '--[[',
          suffix: ']]',
        },
      ],

    },
  Swift:
    # https://github.com/microsoft/vscode/blob/main/extensions/swift/package.json
    {
      name: 'Swift',
      vscode_name: 'swift',
      extensions: [
        '.swift',
        '.SWIFT',
      ],
      comment_prefix: '// ',
      # https://docs.swift.org/swift-book/documentation/the-swift-programming-language/thebasics/#Comments
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  R:
    # https://github.com/microsoft/vscode/blob/main/extensions/r/package.json
    {
      name: 'R',
      vscode_name: 'r',
      extensions: [
        '.r',
        '.rhistory',
        '.rprofile',
        '.rt',
        '.rds',
        '.rdata',
        '.rda',
      ],
      # https://cran.r-project.org/doc/manuals/r-release/R-lang.html#Comments
      # NOTE: No standardized way to do multi-line comments.
      comment_prefix: '# ',
      multi_line_comments: [],
    },
  Ruby:
    # https://github.com/microsoft/vscode/blob/main/extensions/ruby/package.json
    {
      name: 'Ruby',
      vscode_name: 'ruby',
      extensions: [
        '.rb',
        '.rbx',
        '.rjs',
        '.gemspec',
      ],
      # https://docs.ruby-lang.org/en/master/syntax/comments_rdoc.html
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '\n=begin',
          suffix: '\n=end',
        },
      ],
    },
  Racket:
    # No official identifier
    {
      name: 'Racket',
      vscode_name: 'racket',
      extensions: [
        '.rkt',
      ],
      # https://docs.racket-lang.org/style/Choosing_the_Right_Construct.html#(part._.Comments)
      comment_prefix: '; ',
      multi_line_comments: [
        {
          prefix: '#|',
          suffix: '|#',
        },
      ],
    },
  Scala:
    # No official identifier
    {
      name: 'Scala',
      vscode_name: 'scala',
      extensions: [
        '.scala',
        '.sc',
      ],
      # https://docs.scala-lang.org/overviews/scala-book/preliminaries.html#comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Strato:
    # No official identifier
    {
      # For X/Twitter
      name: 'Strato',
      vscode_name: 'strato',
      extensions: [
        '.strato',
      ],
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Kotlin:
    # No official identifier
    {
      name: 'Kotlin',
      vscode_name: 'kotlin',
      extensions: [
        '.kt',
        '.kts',
      ],
      # https://kotlinlang.org/docs/basic-syntax.html#comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Svelte:
    # https://github.com/sveltejs/language-tools/blob/master/packages/svelte-vscode/package.json
    {
      name: 'Svelte',
      vscode_name: 'svelte',
      extensions: [
        '.svelte',
      ],
      # https://svelte.dev/docs/basic-markup#comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '<!--',
          suffix: '-->',
        },
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Astro:
    # No official identifier
    {
      name: 'Astro',
      vscode_name: 'astro',
      extensions: [
        '.astro',
      ],
      # https://docs.astro.build/en/basics/astro-syntax/#comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '<!--',
          suffix: '-->',
        },
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Jsonnet:
    # https://github.com/grafana/vscode-jsonnet/blob/main/package.json
    {
      name: 'Jsonnet',
      vscode_name: 'jsonnet',
      extensions: [
        '.jsonnet',
        '.libsonnet',
      ],
      # https://jsonnet.org/ref/spec.html#lexing
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],

    },
  Protobuf:
    # No official identifier
    {
      name: 'Protobuf',
      vscode_name: 'proto3',
      extensions: [
        '.proto',
      ],
      # https://protobuf.com/docs/language-spec#whitespace-and-comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Markdown:
    # https://github.com/microsoft/vscode/blob/main/extensions/markdown-basics/package.json
    {
      name: 'Markdown',
      vscode_name: 'markdown',
      extensions: [
        '.md',
        '.mdwn',
        '.mdown',
        '.markdown',
        '.markdn',
        '.mdtxt',
        '.mdtext',
        '.workbook',
      ],
      # https://spec.commonmark.org/0.31.2/#html-comment
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '<!--',
          suffix: '-->',
        },
      ],
    },
  'Plain Text':
    # No official identifier
    {
      name: 'Plain Text',
      vscode_name: 'plaintext',
      extensions: [
        '.txt',
        '',
      ],
      comment_prefix: '// ',
      multi_line_comments: [],
    },
  SQL:
    # https://github.com/microsoft/vscode/blob/main/extensions/sql/package.json
    {
      name: 'SQL',
      vscode_name: 'sql',
      extensions: [
        '.sql',
      ],
      # https://www.w3schools.com/sql/sql_comments.asp
      comment_prefix: '-- ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  XML:
    # https://github.com/microsoft/vscode/blob/main/extensions/xml/package.json
    {
      name: 'XML',
      vscode_name: 'xml',
      extensions: [
        '.xml',
      ],
      # https://www.w3.org/TR/xml/#sec-comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '<!--',
          suffix: '-->',
        },
      ],
    },
  YAML:
    # https://github.com/microsoft/vscode/blob/main/extensions/yaml/package.json
    {
      name: 'YAML',
      vscode_name: 'yaml',
      extensions: [
        '.yaml',
        '.yml',
        '.llm',
      ],
      # https://yaml.org/spec/1.2.2/#66-comments
      comment_prefix: '# ',
      multi_line_comments: [],
    },
  JSON:
    # https://github.com/microsoft/vscode/blob/main/extensions/json/package.json
    {
      name: 'JSON',
      vscode_name: 'json',
      extensions: [
        '.json',
      ],
      # NOTE: JSON doesn't allow comments, but some extensions exist
      # that allow JavaScript style commenting.
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Dart:
    # https://github.com/microsoft/vscode/blob/main/extensions/dart/package.json
    {
      name: 'Dart',
      vscode_name: 'dart',
      extensions: [
        '.dart',
      ],
      # https://dart.dev/language/comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  CSS:
    # https://github.com/microsoft/vscode/blob/main/extensions/css/package.json
    {
      name: 'CSS',
      vscode_name: 'css',
      extensions: [
        '.css',
        '.scss',
        '.sass',
        '.less',
        '.module.css',
      ],
      # https://developer.mozilla.org/en-US/docs/Web/CSS/Comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Clojure:
    # https://github.com/microsoft/vscode/blob/main/extensions/clojure/package.json
    {
      name: 'Clojure',
      vscode_name: 'clojure',
      extensions: [
        '.clj',
        '.cljs',
        '.cljc',
        '.cljx',
      ],
      # https://clojure.org/reference/reader#_comment
      # NOTE: No standardized way to do multi-line comments.
      comment_prefix: '; ',
      multi_line_comments: [],
    },
  'Visual Basic':
    # https://github.com/microsoft/vscode/blob/main/extensions/vb/package.json
    {
      name: 'VisualBasic',
      vscode_name: 'vb',
      extensions: [
        '.vb',
        '.vba',
      ],
      # https://learn.microsoft.com/en-us/dotnet/visual-basic/programming-guide/program-structure/comments-in-code
      # TODO: comment_prefix should be "' "
      # NOTE: No standardized way to do multi-line comments.
      comment_prefix: '// ',
      multi_line_comments: [],
    },
  TeX:
    # https://github.com/microsoft/vscode/blob/main/extensions/latex/package.json
    {
      name: 'Tex',
      vscode_name: 'tex',
      extensions: [
        '.tex',
      ],
      # https://tex.stackexchange.com/questions/17816/commenting-out-large-sections
      # TODO: comment_prefix should be "% "
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '\\begin{comment}',
          suffix: '\\end{comment}',
        },
      ],
    },
  ERB:
    # No official identifier
    {
      name: 'ERB',
      vscode_name: 'erb',
      extensions: [
        '.erb',
      ],
      # https://docs.ruby-lang.org/en/2.3.0/ERB.html#class-ERB-label-Recognized+Tags
      comment_prefix: '<%#',
      # https://stackoverflow.com/questions/3127644/block-comments-in-html-erb-templates-in-rails
      multi_line_comments: [
        # Ruby
        {
          prefix: '<%#',
          suffix: '%>',
        },
        # HTML
        {
          prefix: '<!--',
          suffix: '-->',
        },
        # JS
        {
          prefix: '/*',
          suffix: '*/',
        }
      ],
    },
  TOML:
    # No official identifier
    {
      name: 'TOML',
      vscode_name: 'toml',
      extensions: [
        '.toml',
      ],
      # https://toml.io/en/v1.0.0#comment
      comment_prefix: '# ',
      multi_line_comments: [],
    },
  Dockerfile:
    # https://github.com/microsoft/vscode/blob/main/extensions/docker/package.json
    {
      name: 'Dockerfile',
      vscode_name: 'dockerfile',
      extensions: [
        '.dockerfile',
        '.dockerignore',
      ],
      # https://docs.docker.com/reference/dockerfile/#format
      comment_prefix: '# ',
      multi_line_comments: [],
    },
  INI:
    # https://github.com/microsoft/vscode/blob/main/extensions/ini/package.json
    {
      name: 'INI',
      vscode_name: 'ini',
      extensions: [
        '.ini',
        '.cfg',
        '.prefs',
        '.pro',
        '.properties',
      ],
      # https://github.com/graniticio/inifile?tab=readme-ov-file#comment-lines
      comment_prefix: '; ',
      multi_line_comments: [],
    },
  Verilog:
    # No official identifier
    {
      name: 'Verilog',
      vscode_name: 'verilog',
      extensions: [
        '.v',
        '.vh',
        '.sv',
        '.svh',
      ],
      # https://docs.amd.com/r/en-US/ug901-vivado-synthesis/Verilog-Meta-Comment-Support
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  PowerShell:
    # https://github.com/microsoft/vscode/blob/main/extensions/powershell/package.json
    {
      name: 'PowerShell',
      vscode_name: 'powershell',
      extensions: [
        '.ps1',
        '.psm1',
        '.psd1',
        '.ps1xml',
      ],
      # https://learn.microsoft.com/en-us/powershell/scripting/developer/help/syntax-of-comment-based-help?view=powershell-7.4#syntax-diagram
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '<#',
          suffix: '#>',
        },
      ],
    },
  Smarty:
    # No official identifier
    {
      name: 'Smarty',
      vscode_name: 'smarty',
      extensions: [
        '.tpl',
      ],
      # https://www.smarty.net/docsv2/en/language.basic.syntax.tpl#language.syntax.comments
      comment_prefix: '{* ',
      multi_line_comments: [
        {
          prefix: '{*',
          suffix: '*}',
        },
        # HTML style
        {
          prefix: '<!--',
          suffix: '-->',
        },
      ],
    },
  Gradle:
    # No official identifier
    {
      name: 'Gradle',
      vscode_name: 'gradle',
      extensions: [
        '.gradle',
        '.gradle.kts',
      ],
      # https://docs.gradle.org/current/userguide/writing_build_scripts.html
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  HTTP:
    # No official identifier
    {
      name: 'HTTP',
      vscode_name: 'http',
      extensions: [
        '.http',
      ],
      # https://stackoverflow.com/questions/56812140/multiline-comments-in-http
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '<!--',
          suffix: '-->',
        }
      ],
    },
  Arduino:
    # No official identifier
    {
      name: 'Arduino',
      vscode_name: 'arduino',
      extensions: [
        '.ino',
      ],
      # https://www.arduino.cc/reference/en/language/structure/further-syntax/blockcomment/
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Limbo:
    # No official identifier
    {
      name: 'Limbo',
      vscode_name: 'limbo',
      extensions: [
        '.b',
      ],
      # https://doc.cat-v.org/inferno/4th_edition/limbo_language/limbo
      comment_prefix: '# ',
      multi_line_comments: [],
    },
  JSON5:
    # No official identifier
    {
      name: 'JSON5',
      vscode_name: 'json5',
      extensions: [
        '.json5',
      ],
      # https://spec.json5.org/#comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Gas:
    # No official identifier
    {
      name: 'Gas',
      vscode_name: 'gas',
      extensions: [
        '.s',
      ],
      # https://en.wikibooks.org/wiki/X86_Assembly/Comments#:~:text=A%20comment%20is%20a%20piece,comments%20possibly%20spanning%20multiple%20lines
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  TCL:
    # No official identifier
    {
      name: 'TCL',
      vscode_name: 'tcl',
      extensions: [
        '.tcl',
      ],
      # https://wiki.tcl-lang.org/page/comment
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: 'if 0 {',
          suffix: '}',
        },
      ],
    },
  Makefile:
    # https://github.com/microsoft/vscode/blob/main/extensions/make/package.json
    {
      name: 'Makefile',
      vscode_name: 'makefile',
      extensions: [
        '.mk',
        '.mak',
        '.make',
        '.makefile',
        '.Makefile',
      ],
      # https://www.gnu.org/software/make/manual/html_node/Makefile-Contents.html
      comment_prefix: '# ',
      multi_line_comments: [],
    },
  Cmake:
    # No official identifier
    {
      name: 'CMake',
      vscode_name: 'cmake',
      extensions: [
        '.cmake',
      ],
      # https://cmake.org/cmake/help/v3.1/manual/cmake-language.7.html#bracket-comment
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '#[[',
          suffix: ']]',
        },
      ],
    },
  Groovy:
    # https://github.com/microsoft/vscode/blob/main/extensions/groovy/package.json#L24-L28
    {
      name: 'Groovy',
      vscode_name: 'groovy',
      extensions: [
        '.groovy',
        '.gvy',
        '.jenkinsfile',
        '.nf',
        '.gtpl',
        '.gvy',
      ],
      # https://groovy-lang.org/syntax.html
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Vue:
    # https://github.com/vuejs/language-tools/blob/master/extensions/vscode/package.json
    {
      name: 'Vue',
      vscode_name: 'vue',
      extensions: [
        '.vue',
      ],
      # https://stackoverflow.com/questions/41228833/how-to-comment-code-in-a-vue-js-file
      comment_prefix: '// ',
      multi_line_comments: [
        # JS
        {
          prefix: '/*',
          suffix: '*/',
        },
        # HTML
        {
          prefix: '<!--',
          suffix: '-->',
        },
      ],
    },
  Objective-C:
    # https://github.com/microsoft/vscode/blob/main/extensions/objective-c/package.json
    {
      name: 'Objective-C',
      vscode_name: 'objective-c',
      extensions: [
        '.m',
        '.mm',
      ],
      # https://google.github.io/styleguide/objcguide.html#comments
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Nix:
    # No official identifier
    {
      name: 'Nix',
      vscode_name: 'nix',
      extensions: [
        '.nix',
      ],
      # https://nix.dev/manual/nix/2.18/language/constructs
      comment_prefix: '# ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  ANTLR:
    # No official identifier
    {
      name: 'ANTLR',
      vscode_name: 'antlr',
      extensions: [
        '.g4',
      ],
      # https://github.com/antlr/antlr4/blob/dev/doc/lexicon.md
      comment_prefix: '// ',
      multi_line_comments: [
        {
          prefix: '/*',
          suffix: '*/',
        },
      ],
    },
  Jinja:
    # No official identifier
    {
      name: 'Jinja',
      vscode_name: 'jinja',
      extensions: [
        '.jinja',
        '.jinja2',
        '.mustache',
      ],
      # https://jinja.palletsprojects.com/en/3.1.x/templates/
      comment_prefix: '{# ',
      multi_line_comments: [
        {
          prefix: '{#',
          suffix: '#}',
        },
        # HTML style
        {
          prefix: '<!--',
          suffix: '-->',
        },
      ],
    },
  Haskell:
    #  https://github.com/haskell/vscode-haskell/blob/master/package.json
    {
      name: 'Haskell',
      vscode_name: 'haskell',
      extensions: [
        '.hs',
        '.lhs',
      ],
      # https://wiki.haskell.org/wikiupload/b/b2/QuickReference.pdf
      comment_prefix: '-- ',
      multi_line_comments: [
        {
          prefix: '{-',
          suffix: '-}',
        },
      ],
    },
  HAML:
    # No official identifier
    {
      name: 'HAML',
      vscode_name: 'haml',
      extensions: [
        '.haml',
      ],
      # https://haml.info/docs/yardoc/file.REFERENCE.html
      comment_prefix: '-# ',
      multi_line_comments: [],
    },
}
