{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "import logging\n", "from collections import Counter\n", "from pathlib import Path\n", "from random import Random\n", "from dataclasses import dataclass, field, asdict, astuple\n", "from tqdm import tqdm\n", "from typing import cast\n", "\n", "from colorama import Fore, Style\n", "\n", "from pyspark.sql import functions as sparkF\n", "from pyspark.sql import SparkSession\n", "\n", "import tree_sitter as ts\n", "\n", "from base.languages.languages import LanguageId, to_vscode_name, JSON_DATA\n", "from base.languages.language_guesser import guess_language, LanguageMapLanguageGuesser\n", "from base.languages.unit_test_guesser import is_unit_test\n", "from base.languages.comment_guesser import (\n", "    default_comment_guesser,\n", "    guess_is_comment,\n", "    guess_is_single_line_comment,\n", "    guess_is_multi_line_comment,\n", ")\n", "from base.static_analysis.parsing import (\n", "    TsParsedFile,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    get_nodes_of_types,\n", ")\n", "from base.static_analysis.common import convert_to_static_analysis_language, LanguageID\n", "from base.ranges.byte_map import ByteMap\n", "from base.ranges.range_types import ByteRange, CharRange\n", "\n", "from research.data.spark import k8s_session, get_local_session"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = \"s3a://the-stack-processed/by-repo-3\"\n", "LIMIT_REPOS = 1000\n", "SEED = 42\n", "\n", "# Save the random subset locally\n", "CACHE_FILE = None\n", "CACHE_FILE = \"/mnt/efs/augment/user/jeff/stack/repos_1k_seed_42.parquet\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get random data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if CACHE_FILE is not None:\n", "    CACHE_FILE = Path(CACHE_FILE)\n", "\n", "if CACHE_FILE is None or not CACHE_FILE.exists():\n", "    print(\"reading from s3\")\n", "    spark = k8s_session(max_workers=100)\n", "    df = spark.read.parquet(DATA_PATH)\n", "    fraction = LIMIT_REPOS / df.count()\n", "    df_random_n = df.sample(withReplacement=False, fraction=fraction, seed=SEED).limit(LIMIT_REPOS)\n", "    df = df_random_n\n", "    if CACHE_FILE is not None:\n", "        df.write.save(str(CACHE_FILE), format=\"parquet\")\n", "    data = df.collect()\n", "else:\n", "    print(f\"reading from cached file: {CACHE_FILE}\")\n", "    spark = get_local_session()\n", "    df = spark.read.parquet(str(CACHE_FILE))\n", "    data = df.collect()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len([file for datum in data for file in datum.file_list])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guess Language"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# guess_language mismatches\n", "Counter(\n", "    [\n", "        (vscode_name, file.langpart)\n", "        for datum in data\n", "        for file in cast(list, datum.file_list)\n", "        if (vscode_name := to_vscode_name(guess_language(file.max_stars_repo_path)))\n", "        != file.langpart\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guess Unit Test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def naive_is_unit_test(path: str) -> bool:\n", "    return \"spec\" in path.lower() or \"test\" in path.lower()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def naive_strict_is_unit_test(path: str) -> bool:\n", "    return (\n", "        \"/test_\" in path\n", "        or \"_test.\" in path\n", "        or \"/test-\" in path\n", "        or \"-test.\" in path\n", "        or \".test.\" in path\n", "        or \"/test/\" in path\n", "        or \"/test.\" in path\n", "        or \"/tests/\" in path\n", "        or \"/Test\" in path\n", "        or \"Test.\" in path\n", "        or \".spec.\" in path\n", "        or \"/spec/\" in path\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Count the three methods.\n", "Counter(\n", "    [\n", "        (\n", "            naive_strict_is_unit_test(file.max_stars_repo_path),\n", "            is_unit_test(file.max_stars_repo_path),\n", "            naive_is_unit_test(file.max_stars_repo_path),\n", "        )\n", "        for datum in data\n", "        for file in datum.file_list\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print all mismatches\n", "for datum in data:\n", "    for file in datum.file_list:\n", "        loose = naive_is_unit_test(file.max_stars_repo_path)\n", "        actual = is_unit_test(file.max_stars_repo_path)\n", "        strict = naive_strict_is_unit_test(file.max_stars_repo_path)\n", "        if (strict, actual, loose) not in {(True, True, True), (False, False, False)}:\n", "            print(strict, actual, loose)\n", "            print(file.max_stars_repo_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guess comment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_all_comments(\n", "    code: str,\n", "    lang: LanguageID,\n", "    path: Path,\n", "    parser: ScopeTreeParser = GlobalParser,\n", "    types: tuple = (\"comment\", \"line_comment\", \"block_comment\"),\n", ") -> list[<PERSON><PERSON><PERSON><PERSON><PERSON>]:\n", "    \"\"\"Find all comments in the given file and return their ranges.\n", "\n", "    Similar to the one in static_analysis, but this one also includes\n", "    line and block comments.\n", "    \"\"\"\n", "    tree = parser.parse_ts_tree(code, lang=lang, path=path)\n", "    comment = get_nodes_of_types(tree.root_node, types)\n", "    bmap = ByteMap(code)\n", "    return [\n", "        bmap.brange_to_crange(ByteRange(node.start_byte, node.end_byte))\n", "        for node in comment\n", "    ]\n", "\n", "\n", "def find_all_nodes(\n", "    code: str, lang: LanguageID, path: Path, parser: ScopeTreeParser = GlobalParser\n", "):\n", "    tree = parser.parse_ts_tree(code, lang=lang, path=path)\n", "    cursor = tree.walk()\n", "\n", "    visited_children = False\n", "    while True:\n", "        if not visited_children:\n", "            yield cursor.node\n", "            if not cursor.goto_first_child():\n", "                visited_children = True\n", "        elif cursor.goto_next_sibling():\n", "            visited_children = False\n", "        elif not cursor.goto_parent():\n", "            break\n", "\n", "\n", "# For some reason, the signature_analysis.py lib is missing a bunch\n", "# See convert_to_static_analysis_language\n", "BASE_LANG_ID_MAP: dict[LanguageId, LanguageID] = {\n", "    \"Python\": \"python\",\n", "    \"Java\": \"java\",\n", "    \"C++\": \"cpp\",\n", "    \"JavaScript\": \"javascript\",\n", "    \"TypeScript\": \"typescript\",\n", "    \"Go\": \"go\",\n", "    \"Rust\": \"rust\",\n", "    # Add more langs:\n", "    \"JavaScript JSX\": \"javascript\",\n", "    \"TypeScript JSX\": \"typescript\",\n", "    \"CSharp\": \"c_sharp\",\n", "    \"PHP\": \"php\",\n", "    \"HTML\": \"html\",\n", "    \"Shell\": \"bash\",\n", "    \"Scala\": \"scala\",\n", "    \"Ruby\": \"ruby\",\n", "    \"Lua\": \"lua\",\n", "    \"SQL\": \"sql\",\n", "    \"Kotlin\": \"kotlin\",\n", "    # The markdown parser seems to segfault somehow.\n", "    # \"Markdown\": \"markdown\",\n", "}\n", "\n", "\n", "def to_static_analysis_language(lang: LanguageId) -> LanguageID | None:\n", "    return BASE_LANG_ID_MAP.get(lang)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.languages.comment_guesser import CommentGuesser\n", "\n", "\n", "@dataclass\n", "class CompareCommentResult:\n", "    char_index: int\n", "    inconclusive: bool\n", "    tree_sitter_is_comment: bool\n", "    base_guess_is_comment: bool\n", "\n", "\n", "@dataclass\n", "class CompareCommentFileResult:\n", "    repo_index: int\n", "    file_index: int\n", "    lang: LanguageId | None = None\n", "    static_lang: LanguageID | None = None\n", "    results: list[CompareCommentResult] = field(default_factory=list)\n", "\n", "\n", "def get_file(data: list, file_result: CompareCommentFileResult):\n", "    return data[file_result.repo_index].file_list[file_result.file_index]\n", "\n", "\n", "def get_static_analysis_comments(content: str, static_lang: LanguageID, path: Path):\n", "    comment_indices = set()\n", "    inconclusive_indices = set()\n", "    for crange in find_all_comments(content, static_lang, path):\n", "        for i in crange:\n", "            comment_indices.add(i + 1)\n", "        # Mark as inconclusive if at the start or end of tree sitter comment\n", "        inconclusive_indices.add(crange.start + 1)\n", "        inconclusive_indices.add(crange.start + 2)\n", "        inconclusive_indices.add(crange.stop - 1)\n", "\n", "    # Some languages use the string type as comments\n", "    if static_lang in {\"python\"}:\n", "        for crange in find_all_comments(content, static_lang, path, types=(\"string\",)):\n", "            for i in crange:\n", "                inconclusive_indices.add(i + 1)\n", "    return comment_indices, inconclusive_indices\n", "\n", "\n", "def compare_comment_guesser(\n", "    file,\n", "    repo_index: int,\n", "    file_index: int,\n", "    rng: Random,\n", "    sample_char_rate: int = 1024,\n", "    prefix_max_chars=4096,\n", "    suffix_max_chars=4096,\n", "    comment_guesser: CommentGuesser = default_comment_guesser(),\n", ") -> CompareCommentFileResult:\n", "    path = file.max_stars_repo_path\n", "    lang = guess_language(path)\n", "    if lang is None:\n", "        return CompareCommentFileResult(repo_index, file_index)\n", "    static_lang = to_static_analysis_language(lang)\n", "    if static_lang is None:\n", "        return CompareCommentFileResult(repo_index, file_index, lang=lang)\n", "\n", "    comment_indices, inconclusive_indices = get_static_analysis_comments(\n", "        file.content, static_lang, Path(path)\n", "    )\n", "\n", "    indices = rng.sample(\n", "        range(len(file.content) + 1),\n", "        k=max((len(file.content) + 1) // sample_char_rate, 1),\n", "    )\n", "    indices = sorted(indices)\n", "    results: list[CompareCommentResult] = []\n", "    for index in indices:\n", "        prefix = file.content[:index]\n", "        suffix = file.content[index:][:suffix_max_chars]\n", "        if prefix_max_chars > 0:\n", "            prefix = prefix[-prefix_max_chars:]\n", "        if suffix_max_chars > 0:\n", "            suffix = suffix[:suffix_max_chars]\n", "        is_comment = comment_guesser.is_comment(prefix, suffix, path)\n", "        results.append(\n", "            CompareCommentResult(\n", "                char_index=index,\n", "                inconclusive=index in inconclusive_indices,\n", "                tree_sitter_is_comment=index in comment_indices,\n", "                base_guess_is_comment=is_comment,\n", "            )\n", "        )\n", "    return CompareCommentFileResult(\n", "        repo_index,\n", "        file_index,\n", "        lang=lang,\n", "        static_lang=static_lang,\n", "        results=results,\n", "    )\n", "\n", "\n", "def compare_all_comment_guesser(\n", "    data: list,\n", "    repo_limit: int = 0,\n", "    seed=42,\n", "    **kwargs,\n", "):\n", "    repo_limit = repo_limit or len(data)\n", "    rng = Random(seed)\n", "\n", "    file_results: list[CompareCommentFileResult] = []\n", "    for repo_index, datum in tqdm(list(enumerate(data))[:repo_limit]):\n", "        for file_index, file in enumerate(datum.file_list):\n", "            file_results.append(\n", "                compare_comment_guesser(file, repo_index, file_index, rng, **kwargs)\n", "            )\n", "    return file_results\n", "\n", "\n", "def print_comparisons(\n", "    file_result: CompareCommentFileResult,\n", "    content: str,\n", "    color_maps: dict = {\n", "        (True, True): Fore.GREEN,\n", "        (True, False): Fore.RED,\n", "        (<PERSON><PERSON><PERSON>, <PERSON>): Fore.YELLOW,\n", "        (<PERSON><PERSON><PERSON>, <PERSON>als<PERSON>): Fore.BLUE,\n", "    },\n", "):\n", "    indices = [result.char_index for result in file_result.results]\n", "    split_file = []\n", "    split_file.append(content[0 : indices[0]])\n", "    for i in range(len(indices)):\n", "        result = file_result.results[i]\n", "        if result.inconclusive:\n", "            split_file.append(Fore.MAGENTA)\n", "        else:\n", "            split_file.append(\n", "                color_maps[\n", "                    (result.tree_sitter_is_comment, result.base_guess_is_comment)\n", "                ]\n", "            )\n", "        split_file.append(content[indices[i] : indices[i] + 1])\n", "        split_file.append(Style.RESET_ALL)\n", "        if i < len(indices) - 1:\n", "            split_file.append(content[indices[i] + 1 : indices[i + 1]])\n", "    split_file.append(content[indices[-1] + 1 :])\n", "\n", "    print(\"=\" * 80)\n", "    print(\"\".join(split_file))\n", "    print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Co<PERSON><PERSON> vs tree-sitter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different language settings\n", "# json_data = copy.deepcopy(JSON_DATA)\n", "# for lang in json_data:\n", "#     json_data[lang][\"comment_prefix\"] = json_data[lang][\"comment_prefix\"].rstrip()\n", "# json_data[\"PHP\"][\"comment_prefix\"] = \"# \"\n", "# json_data[\"HTML\"][\"comment_prefix\"] = \"// \""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_results = compare_all_comment_guesser(\n", "    data,\n", "    # comment_guesser=CommentGuesser(\n", "    #     LanguageMapLanguageGuesser.from_json(json_data), max_prefix_char_count=1024 * 1024\n", "    # ),\n", "    repo_limit=0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(\n", "    [\n", "        Path(\n", "            data[file_result.repo_index]\n", "            .file_list[file_result.file_index]\n", "            .max_stars_repo_path\n", "        ).suffix\n", "        for file_result in all_results\n", "        if file_result.lang is None or file_result.static_lang is None\n", "    ]\n", ").most_common(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter([result.inconclusive for file_result in all_results for result in file_result.results])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter([(result.inconclusive, result.tree_sitter_is_comment, result.base_guess_is_comment) for file_result in all_results for result in file_result.results])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sorted(Counter(\n", "    [\n", "        (\n", "            get_file(data, file_result).langpart,\n", "            result.tree_sitter_is_comment,\n", "            result.base_guess_is_comment,\n", "        )\n", "        for file_result in all_results\n", "        for result in file_result.results\n", "        if not result.inconclusive\n", "    ]\n", ").items())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for file_result in all_results:\n", "    if file_result.lang is None:\n", "        continue\n", "    if file_result.static_lang is None:\n", "        continue\n", "    # Only print files with conclusive wrong results, filter by lang\n", "    if not any(not result.inconclusive and result.tree_sitter_is_comment != result.base_guess_is_comment for result in file_result.results):\n", "        continue\n", "    if file_result.static_lang not in {\"java\"}:\n", "        continue\n", "    \n", "    file = get_file(data, file_result)\n", "    print(file_result.repo_index, file_result.file_index, file.max_stars_repo_path)\n", "    print([astuple(result) for result in file_result.results])\n", "    print_comparisons(file_result, file.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Debug comment file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["debug_repo_index = 998\n", "debug_file_index = 57"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["debug_file = data[debug_repo_index].file_list[debug_file_index]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["debug_result = compare_comment_guesser(debug_file, debug_repo_index, debug_file_index, Random(42), sample_char_rate=64)\n", "print_comparisons(debug_result, debug_file.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[\n", "    (debug_file.content[r.to_slice()], r.to_slice())\n", "    for r in find_all_comments(\n", "        debug_file.content,\n", "        \"html\",\n", "        Path(debug_file.max_stars_repo_path),\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[\n", "    (node.type, node.start_byte, node.end_byte)\n", "    for node in find_all_nodes(\n", "        debug_file.content, \"html\", Path(debug_file.max_stars_repo_path)\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}