"""Canonical names for programming languages."""

import functools
import json
import os
import pathlib
import typing
from dataclasses import dataclass

import yaml
from dataclasses_json import dataclass_json

# TODO(arun,guy): We should migrate this to be an enum.
# This list should correspond to the languages in `languages.yaml`.
LanguageId = typing.Literal[
    "Astro",
    "C",
    "C++",
    "CSharp",
    "Cuda",
    "Go",
    "HTML",
    "Java",
    "JavaScript",
    "JavaScript JSX",
    "Jsonnet",
    "Kotlin",
    "Lua",
    "Markdown",
    "PHP",
    "Perl",
    "Plain Text",
    "Protobuf",
    "Python",
    "R",
    "Racket",
    "Ruby",
    "Rust",
    "SQL",
    "Scala",
    "Shell",
    "Svelte",
    "Swift",
    "TypeScript",
    "TypeScript JSX",
    "XML",
    "YAML",
    "JSON",
    "Dart",
    "CSS",
    "Clojure",
    "Visual Basic",
    "TeX",
    "ERB",
    "TOML",
    "Dockerfile",
    "INI",
    "Verilog",
    "PowerShell",
    "Smarty",
    "<PERSON>rad<PERSON>",
    "<PERSON>TT<PERSON>",
    "<PERSON><PERSON>uino",
    "Limbo",
    "JSON5",
    "Gas",
    "T<PERSON>",
    "Makefile",
    "Cmake",
    "Groovy",
    "Vue",
    "Objective-C",
    "Nix",
    "ANTLR",
    "Jinja",
    "Haskell",
    "HAML",
    "Strato",
]
"""A literal type with canonical names for programming languages."""

Languages: tuple[LanguageId, ...] = typing.get_args(LanguageId)
"""A list of all canonical programming language names."""


def check_language(possible_language: str | None) -> LanguageId | None:
    """Check if `possible_language` is one of the canonical programming languages.

    This function supports a None argument to make the common case of optional chaining
    less onerous.
    """
    if possible_language and possible_language in Languages:
        return typing.cast(LanguageId, possible_language)
    else:
        return None


@dataclass_json
@dataclass(frozen=True)
class MultiLineComment:
    """Represents a multi-line comment."""

    prefix: str
    """The prefix of the multi-line comment."""

    suffix: str
    """The suffix of the multi-line comment."""


@dataclass_json
@dataclass(frozen=True)
class LanguageInfo:
    name: str
    """The name of the programming language (may not match LanguageId)."""

    vscode_name: str
    """The vscode language identifier of the programming language.

    See https://code.visualstudio.com/docs/languages/identifiers
    """

    extensions: list[str]
    """The file extensions that are associated with this language."""

    comment_prefix: str
    """The comment prefix for this language."""

    multi_line_comments: list[MultiLineComment]
    """The multi-line comments for this language."""


@functools.cache
def default_languages() -> dict[LanguageId, LanguageInfo]:
    """Constructs the default language infos as a singleton."""

    with pathlib.Path(os.path.dirname(__file__), "languages.yaml").open(
        encoding="utf-8"
    ) as json_file:
        json_data = yaml.safe_load(json_file)
    assert json_data.keys() == set(Languages)

    lang_infos: dict[LanguageId, LanguageInfo] = {}
    for lang, lang_data in json_data.items():
        lang_id = check_language(lang)
        assert lang_id is not None
        assert lang_id == lang
        lang_info = LanguageInfo.schema().load(lang_data)  # type: ignore
        lang_infos[lang_id] = lang_info
    return lang_infos


def to_vscode_name(language: LanguageId | None) -> str | None:
    """Convert a language name to the corresponding VSCode name.

    This function supports a None argument to make the common case of optional chaining
    less onerous.
    """
    if language is None:
        return None

    return default_languages()[language].vscode_name
