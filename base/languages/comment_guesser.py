"""Module to guess whether the cursor is in a comment."""

from dataclasses import dataclass
from pathlib import Path
import functools

from base.languages.languages import LanguageId
from base.languages.language_guesser import (
    LanguageGuesser,
    MultiLineComment,
    default_language_guesser,
)


@dataclass(frozen=True)
class CommentGuesser:
    """Guesses whether the cursor is in a comment."""

    language_guesser: LanguageGuesser
    """The language guesser to use."""

    max_prefix_char_count: int = 2048
    """A lower bound for when the prefix gets truncated.
    It can be less than the actual number, but shouldn't be greater.
    """

    def is_single_line_comment(
        self, prefix: str, suffix: str, path: Path | str | None
    ) -> bool:
        """Returns whether the cursor is in a single line comment."""
        lang = self.language_guesser.get_language(path)
        comment_prefix = self.language_guesser.get_comment_prefix(lang)
        # If we don't know the language, don't guess
        if comment_prefix is None:
            return False

        # NOTE: this is a trick to handle all cases, including input ending in a
        # new line and the empty string. Unlike get_last_n_lines, in the case of
        # "foo\nbar\n", we want to get "", not "bar\n".
        last_line = (prefix + ".").splitlines(keepends=True)[-1][:-1]
        return comment_prefix in last_line

    def _is_multi_line_comment_helper(
        self,
        prefix: str,
        suffix: str,
        lang: LanguageId | None,
        multi_line_comment: MultiLineComment,
    ) -> bool:
        comment_prefix = multi_line_comment.prefix
        comment_suffix = multi_line_comment.suffix
        if comment_prefix == comment_suffix:
            if len(prefix) < self.max_prefix_char_count:
                return prefix.count(comment_prefix) % 2 == 1
            else:
                # First, return False if there is no comment strings
                if prefix.count(comment_prefix) == 0:
                    return False

                # NOTE: If truncation is possible, we don't do a simple count.
                # Instead, we determine the alignment by counting lines between each
                # comment prefix, and assuming code has more lines.
                # We don't do this for short strings, because then count is more
                # reliable.
                sections = prefix.split(comment_prefix)
                totals = [0, 0]
                for i, section in enumerate(sections):
                    totals[i % 2] += section.count("\n")
                last_index = len(sections) - 1
                return totals[last_index % 2] <= totals[(last_index + 1) % 2]

        else:
            # NOTE: This might miss cases where nested comments are allowed, and the
            # comment_suffix only terminates the inner comment, but those should be rare.
            return prefix.rfind(comment_prefix) > prefix.rfind(comment_suffix)

    def is_multi_line_comment(
        self, prefix: str, suffix: str, path: Path | str | None
    ) -> bool:
        """Returns whether the cursor is in a multi line comment."""
        lang = self.language_guesser.get_language(path)
        multi_line_comments = self.language_guesser.get_multi_line_comments(lang)
        for multi_line_comment in multi_line_comments:
            if self._is_multi_line_comment_helper(
                prefix, suffix, lang, multi_line_comment
            ):
                return True
        return False

    def is_comment(self, prefix: str, suffix: str, path: Path | str | None) -> bool:
        """Returns whether the cursor is in a comment."""
        return self.is_single_line_comment(
            prefix, suffix, path
        ) or self.is_multi_line_comment(prefix, suffix, path)


@functools.cache
def default_comment_guesser() -> CommentGuesser:
    """Get the default comment guesser."""
    return CommentGuesser(default_language_guesser())


def guess_is_comment(prefix: str, suffix: str, path: Path | str | None) -> bool:
    """Guess whether the cursor is in a comment."""
    return default_comment_guesser().is_comment(prefix, suffix, path)


def guess_is_single_line_comment(
    prefix: str, suffix: str, path: Path | str | None
) -> bool:
    """Guess whether the cursor is in a single line comment."""
    return default_comment_guesser().is_single_line_comment(prefix, suffix, path)


def guess_is_multi_line_comment(
    prefix: str, suffix: str, path: Path | str | None
) -> bool:
    """Guess whether the cursor is in a multi line comment."""
    return default_comment_guesser().is_multi_line_comment(prefix, suffix, path)
