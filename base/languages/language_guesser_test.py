"""Unit tests for the language guesser."""

from __future__ import annotations

import pytest

from base.languages.language_guesser import (
    default_language_guesser,
    guess_comment_prefix,
    guess_language,
)
from base.languages.languages import LanguageId

_TEST_EXTENSIONS: list[tuple[str, LanguageId | None]] = [
    # C
    ("foo.c", "C"),
    # NOTE(arun): we prefer C++ over C by default.
    ("foo.h", "C++"),
    # C++
    ("foo.cpp", "C++"),
    ("foo.c++", "C++"),
    ("foo.cc", "C++"),
    ("foo.cxx", "C++"),
    ("foo.h", "C++"),
    ("foo.h++", "C++"),
    ("foo.hpp", "C++"),
    ("foo.hxx", "C++"),
    # Go
    ("foo.go", "Go"),
    # Java
    ("foo.java", "Java"),
    # JavaScript
    ("foo.js", "JavaScript"),
    # TypeScript
    ("foo.ts", "TypeScript"),
    # Rust
    ("foo.rs", "Rust"),
    # Python
    ("foo.py", "Python"),
    # Scala
    ("foo.scala", "Scala"),
    # Others
    ("foo.exe", None),
]


@pytest.mark.parametrize("path, expected", _TEST_EXTENSIONS)
def test_language_map_get_language(path: str, expected: LanguageId | None):
    """Tests the comment prefix guesser."""
    language_guesser = default_language_guesser()
    assert language_guesser.get_language(path) == expected


@pytest.mark.parametrize("path, expected", _TEST_EXTENSIONS)
def test_guess_language(path: str, expected: LanguageId | None):
    """Tests the comment prefix guesser."""
    assert guess_language(path) == expected


_TEST_COMMENT_PREFIXES: list[tuple[LanguageId | None, str | None]] = [
    ("C", "// "),
    ("C++", "// "),
    ("Python", "# "),
    ("JavaScript", "// "),
    ("Go", "// "),
    ("Java", "// "),
    ("Rust", "// "),
    ("TypeScript", "// "),
]


@pytest.mark.parametrize("language, expected", _TEST_COMMENT_PREFIXES)
def test_language_map_get_comment_prefix(
    language: LanguageId | None, expected: str | None
):
    """Tests the comment prefix guesser."""
    comment_prefix_guesser = default_language_guesser()
    assert comment_prefix_guesser.get_comment_prefix(language) == expected


@pytest.mark.parametrize("language, expected", _TEST_COMMENT_PREFIXES)
def test_guess_comment_prefix(language: LanguageId | None, expected: str | None):
    """Tests the comment prefix guesser."""
    assert guess_comment_prefix(language) == expected
