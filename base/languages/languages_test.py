"""Tests for base.languages.languages."""

import re

import pytest

from base.languages.languages import Languages, check_language, to_vscode_name


def test_languages_are_distinct():
    assert len(Languages) == len(set(Languages))


@pytest.mark.parametrize("language", Languages)
def test_check_language_understood(language: str):
    assert check_language(language) == language


@pytest.mark.parametrize("language", Languages)
def test_to_vscode_name_exists(language: str):
    lang_id = check_language(language)
    assert lang_id is not None
    name = to_vscode_name(lang_id)
    assert name is not None

    # Check that the name only contains lowercase alphanumeric characters and dashes.
    assert re.fullmatch(r"[-a-z0-9]+", name)


@pytest.mark.parametrize(
    "language,expected",
    [
        ("C++", "cpp"),
        ("Python", "python"),
        ("Java", "java"),
        ("JavaScript", "javascript"),
        ("JavaScript JSX", "javascriptreact"),
        (None, None),
    ],
)
def test_to_vscode_name(language: str, expected: str):
    lang_id = check_language(language)
    assert to_vscode_name(lang_id) == expected
