"""Module to guess whether a file is a unit test.

TODO: we currently only specified patterns for Python, C++, Java, TypeScript, and Markdown.
For other languages, we use a generic pattern that is not perfect. Over the time, we should
gradually improve the regular expression for each language.
"""

import functools
import re
from collections.abc import Collection
from dataclasses import dataclass

from base.languages.language_guesser import guess_language
from base.languages.languages import (
    LanguageId,
    LanguageInfo,
    default_languages,
)


@dataclass(frozen=True)
class UnitTestGuesser:
    """Guesses whether a file is a unit test."""

    patterns_by_lang: dict[LanguageId | None, Collection[str]]

    def is_unit_test(self, path: str) -> bool:
        """Returns whether the file is a unit test."""
        language = guess_language(path)
        if language not in self.patterns_by_lang:
            return False
        patterns = self.patterns_by_lang[language]
        return any(re.match(pattern, path) for pattern in patterns)


# NOTE: This is a heuristic and may not be perfect, we will continue to refine it.
# Currently, we prioritize the precision of the pattern over the recall.
# If you find something missing or inperfection, please let add a test case in unit_test_guesser_test.py and then update this list.
#
# Overall, we try to design the regex pattern as general pattern + specific pattern for each language.
TEST_FOLDER_PATTERN = r"(^(test|tests)/|.*/(test|tests)/|.*/[a-z0-9]+(_|-)(test|tests)/)|.*/test(_|-)[a-z0-9]+|.*/Test[A-Z0-9]+/"
CAMEL_CASE_FILE_PATTERN = (
    r"([a-zA-Z0-9_-]*(Test|Tests)|[a-zA-Z0-9_-]*(Test|Tests)[A-Z0-9]+[a-zA-Z0-9_-]*)"
)
SNAKE_CASE_FILE_PATTERN = (
    r"(test|tests|(test|tests)(-|_)[a-zA-Z0-9_-]*|[a-zA-Z0-9_-]*(-|_)(test|tests))"
)


def _make_patterns_by_lang(
    languages: dict[LanguageId, LanguageInfo],
) -> dict[LanguageId | None, Collection[str]]:
    """Returns patterns for each language."""

    lang_to_extension_group = {
        lang: "|".join([re.escape(ext) for ext in lang_info.extensions])
        for lang, lang_info in languages.items()
    }
    patterns_by_lang: dict[LanguageId | None, Collection[str]] = {
        "Python": (
            rf"{TEST_FOLDER_PATTERN}.*({lang_to_extension_group['Python']})$",
            rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({lang_to_extension_group['Python']})$",
            rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({lang_to_extension_group['Python']})$",
            rf"(^|.*/)conftest({lang_to_extension_group['Python']})$",
        ),
        "C++": (
            rf"{TEST_FOLDER_PATTERN}.*({lang_to_extension_group['C++']})$",
            rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({lang_to_extension_group['C++']})$",
            rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({lang_to_extension_group['C++']})$",
        ),
        "Java": (
            rf"{TEST_FOLDER_PATTERN}.*({lang_to_extension_group['Java']})$",
            rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({lang_to_extension_group['Java']})$",
            rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({lang_to_extension_group['Java']})$",
        ),
        "TypeScript": (
            rf"{TEST_FOLDER_PATTERN}.*({lang_to_extension_group['TypeScript']})$",
            rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({lang_to_extension_group['TypeScript']})$",
            rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({lang_to_extension_group['TypeScript']})$",
            rf"(^|.*/)([a-zA-Z0-9]*\.(Test|Tests)/|(Test|Tests)\.[a-zA-Z0-9]*/).*({lang_to_extension_group['TypeScript']})$",
            rf".*/(__tests__|__test__)/.*({lang_to_extension_group['TypeScript']})$",
            rf"(^|.*/)[a-zA-Z0-9]+.test({lang_to_extension_group['TypeScript']})$",
        ),
        "Markdown": (
            rf"{TEST_FOLDER_PATTERN}.*({lang_to_extension_group['Markdown']})$",
            rf"{TEST_FOLDER_PATTERN}(README|readme)$",
        ),
        None: (
            "tests/.*\\..*",
            "(.*_test\\..*|test.*)\\..*",
        ),
    }
    # Auto create patterns for other languages -- not perfect but better than nothing.
    # Note: we don't create patterns for "Plain Text" because it is not a language.
    for lang in languages:
        if lang in ["Plain Text"]:
            patterns_by_lang[lang] = ()
        elif lang not in patterns_by_lang:
            patterns_by_lang[lang] = (
                rf"{TEST_FOLDER_PATTERN}.*({lang_to_extension_group[lang]})$",
                rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({lang_to_extension_group[lang]})$",
                rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({lang_to_extension_group[lang]})$",
            )
    return patterns_by_lang


@functools.cache
def default_unit_test_guesser() -> UnitTestGuesser:
    """Returns the default unit test guesser."""
    return UnitTestGuesser(_make_patterns_by_lang(default_languages()))


def is_unit_test(path: str) -> bool:
    """Returns whether the file is a unit test via the default guesser."""
    return default_unit_test_guesser().is_unit_test(path)
