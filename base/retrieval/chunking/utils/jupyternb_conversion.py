"""Convert ipynb files to scripts."""

import logging
import re

import nbformat
import pandas as pd
from concurrent.futures import <PERSON>ecuto<PERSON>, ThreadPoolExecutor
from nbformat.validator import NotebookValidationError

from base.retrieval.chunking.utils.markdown_conversion import (
    remove_data_urls,
    robust_conversion,
)

logger = logging.getLogger(__name__)


SUPPORTED_LANG = ["python", "julia", "r"]
"""Supported languages.

These must match exactly the language names in the notebook metadata.

Luckily all 3 languages share the same comment syntax: #

If we add more languages in the future we will have to make this a bit more
complex.
"""


# Regular expression to match ANSI escape codes
# These can exist in output cells and be used to manipulate coloring,
# flashing or other effects.  For the most part they are added
# by jupyter on traceback and error messages to make them standout.
# We remove them here.
ANSI_ESCAPE = re.compile(
    r"""
    \x1B  # ESC
    (?:   # 7-bit C1 Fe (except CSI)
        [@-Z\\-_]
    |     # or [ for CSI, followed by a control sequence
        \[
        [0-?]*  # Parameter bytes
        [ -/]*  # Intermediate bytes
        [@-~]   # Final byte
    )
""",
    re.VERBOSE,
)


class PythonScriptExporter:
    """Converts a notebook to a Python script.

    Similar to the built-in ScriptExporter but allow some
    filtering and cleanup of output cells, and additional filtering
    to remove large encoded data in both code and markdown blocks.

    For each code and markdown block, we
    - Remove all base64 encoded data and pictures in both HTML and markdown.
    - Remove cells with too large max word length and average word length
    - Remove code cells with lines that are too long
    - Format output as comments
    - Add comments to indicate cell execution order and denote cell boundary

    Additional meta info from comments:
    - `# %%` to denote cell boundary which is recognized by VSCode.
    - `# In[n]:` to denote cell execution order and `# Out[n]:` to denote
    cell output formatted as comments.
    - `# Error traceback:` to denote tracebacks.
    - `# ...` to denote output being truncated.
    """

    WORD_SPLIT_PATTERN = re.compile(r"\W+")

    @staticmethod
    def check_size(
        source: str,
        limit: int = 10000,
        max_word_len: int = 64,
        max_avg_word_len: int = 16,
        max_line_len: int = 1000,
        max_avg_line_len: int = 200,
    ) -> bool:
        """Check if the source is too large.

        Checking total size, average word length, max word length,
        average line length and max line length.

        Returns:
            True if the source pass the check.
        """
        if len(source) > limit:
            return False
        words = re.split(PythonScriptExporter.WORD_SPLIT_PATTERN, source)
        if not words:
            return False
        lengths = [len(w) for w in words]
        if max(lengths) >= max_word_len:
            return False
        if sum(lengths) / len(lengths) >= max_avg_word_len:
            return False
        # Check on average line length and max line length
        line_lengths = [len(line) for line in source.splitlines()]
        average_line_length = sum(line_lengths) / len(line_lengths)
        if average_line_length > max_avg_line_len:
            return False
        max_line_length = max(line_lengths)
        if max_line_length > max_line_len:
            return False
        return True

    def from_notebook_node(
        self,
        nb,
        resources=None,
        max_output_length: int = 500,
        max_output_per_cell: int = 2,
        **kw,
    ):
        """Convert a notebook to a Python script.

        Args:
            nb: The notebook object.
            resources: Additional resources used during conversion.
            **kw: Ignored.
        """
        executor = ThreadPoolExecutor(max_workers=1)
        body = self._format_code(
            executor, nb.cells, max_output_length, max_output_per_cell
        )
        return body, resources

    def _format_output(
        self,
        executor: Executor,
        outputs: list,
        max_output_length: int,
        max_output_per_cell: int,
    ):
        """Format output as comments."""
        n_outputs = 0
        code = []
        for output in outputs:
            # Check if the output contains traceback
            # In which case these are printed out as comments
            if "traceback" in output:
                code.append("# Error traceback:")
                tb = ANSI_ESCAPE.sub("", "\n".join(output["traceback"])).splitlines()
                traceback_len = 0
                for line in tb:
                    traceback_len += len(line) + 3
                    if traceback_len > max_output_length:
                        code.append("# ...")
                        break
                    code.append("# " + line)
                # Skip the rest of the output
                break
            # Check if the output contains pure text or other form of data
            if "text" in output:
                output_text = "".join(output["text"])
                if len(output_text) > max_output_length:
                    output_text = (
                        output_text[:max_output_length].rsplit("\n", 1)[0] + "\n..."
                    )
                for line in output_text.splitlines():
                    code.append("# " + line)
                n_outputs += 1
                if n_outputs >= max_output_per_cell:
                    break
            elif "data" in output:
                # Check if the data is almost readable text format
                for fmt in ("text/plain", "text/markdown", "text/latex"):
                    if fmt in output.data:
                        output_str = output.data[fmt]
                        break
                else:
                    # If it is HTML, parse to markdown and print it out
                    if "text/html" in output.data:
                        output_str = robust_conversion(
                            output.data["text/html"], executor, keep_links=False
                        )
                    # Anything else cannot be displayed.
                    elif any(
                        fmt in output.data
                        for fmt in [
                            "image/png",
                            "image/jpeg",
                            "image/svg+xml",
                        ]
                    ):
                        continue
                    else:
                        logger.warning(
                            "Unknown output type: %s",
                            str(output.data.keys()),
                        )
                        continue

                if len(output_str) > max_output_length:
                    output_str = (
                        output_str[:max_output_length].rsplit("\n", 1)[0] + "\n..."
                    )
                for line in output_str.splitlines():
                    code.append("# " + line)
                n_outputs += 1
                if n_outputs >= max_output_per_cell:
                    break
        return code

    def _format_code(
        self,
        executor: Executor,
        input_cells: list,
        max_output_length: int,
        max_output_per_cell: int,
        max_cell_size: int = 10000,
        use_output: bool = False,
    ):
        """Format non-code cells and output as comments.

        Output that contains non-text data are removed.
        Large ouput are truncated.
        """
        code = []
        for cell in input_cells:
            # Add delimiter. This is the standard cell delimiter in VSCode,
            # Spyder and other editors.
            if code:
                code.append("# %%")

            # Extract execution count and print that out as comments
            execution_count = cell.get("execution_count", None)
            if execution_count is not None:
                code.append(f"# In[{execution_count}]:")
            if cell.cell_type == "markdown":
                # Format markdown as comments
                source = remove_data_urls(cell.source)
                if not self.check_size(source, max_cell_size):
                    continue
                for line in source.splitlines():
                    code.append("# " + line)
            elif cell.cell_type == "code":
                # Skip large code cells.  Usually this means non-code
                if not self.check_size(cell.source, max_cell_size):
                    continue
                code.append(cell.source)
                if cell.outputs and use_output:
                    # print out output cell count;  This will be masked out later
                    if execution_count is not None:
                        code.append(f"# Out[{execution_count}]:")
                    code.extend(
                        self._format_output(
                            executor,
                            cell.outputs,
                            max_output_length,
                            max_output_per_cell,
                        )
                    )
        return "\n".join(code)


def convert_file(
    content: str,
    max_output_length: int = 500,
    supported_lang=SUPPORTED_LANG,
    nb_version: int = 4,
) -> str | None:
    exporter = PythonScriptExporter()

    try:
        nb = nbformat.reads(content, as_version=nb_version)
        _, nb = nbformat.validator.normalize(nb, version=nb_version)
        nbformat.validate(nb, version=nb_version)
    except NotebookValidationError as e:
        logger.warning("Notebook validation error: %s", e.message)
        return None

    if nb.metadata.kernelspec.language not in supported_lang:
        # Skip this one
        return None

    script, _ = exporter.from_notebook_node(nb, max_output_length=max_output_length)

    return script


def convert_files(
    content: pd.DataFrame,
    max_output_length: int = 500,
    supported_lang: list[str] = SUPPORTED_LANG,
    input_column: str = "content",
    output_column: str = "converted-script",
    nb_version: int = 4,
) -> pd.DataFrame:
    """Convert a set of notebooks to a Python script.

    Args:
        content: A pandas dataframe containing the content of the notebooks.
        max_output_length: Maximum length of output to include.
        supported_lang: List of supported languages.
        column_name: Name of the column to store the converted script.
    """

    output = []
    for text in content[input_column]:
        try:
            file = convert_file(text, max_output_length, supported_lang, nb_version)
            if not file:
                continue
            output.append(file)
        except Exception as e:
            logger.debug("Exception occurred in convert_files: %s", e)
            continue

    return pd.DataFrame({output_column: output})
