import abc


@abc.abstractmethod
class HTMLToMarkdownConverter(abc.ABC):
    """Converts HTML to markdown."""

    def convert(self, html: str) -> str:
        """Converts HTML to markdown."""
        raise NotImplementedError


class MarkdownifyConverter(HTMLToMarkdownConverter):
    """Uses markdownify to convert HTML to markdown."""

    def convert(self, html: str) -> str:
        """Converts HTML to markdown."""
        from markdownify import markdownify as md

        return md(html)
