"""Utilities for converting html to markdown.

Two different tiers of solutions are provide for a balance of speed
and robustness. Markdownify is more featured and is under the MIT license
but is slower. We also add a very trivial converter that ignores all markup
logics except code quotation blocks, since these are really the only ones
that are extremely important.
"""

import html
import json
import re
import uuid
from concurrent.futures import Executor

from typing import Any, Iterable

from bs4 import BeautifulSoup, Tag, Comment

from base.retrieval.chunking.utils.html_to_md_conversion import (
    HTMLToMarkdownConverter,
    MarkdownifyConverter,
)


def remove_data_urls(markdown_text: str) -> str:
    """Remove data embedded in markdown.

    One can embed images, text or even apps in a markdown / HTML documents
    through data:xxxx url conventions.  Here remove those
    because they are bad and can be very large.
    """
    # Regex pattern to match any type of data URL for images
    data_url_pattern = r"!\[.*?\]\(data:[^;]+;base64,[^\)]+\)"

    # Replace the matched data URLs with an empty string or placeholder
    cleaned_text = re.sub(data_url_pattern, "![Image Removed]", markdown_text)

    # Markdown might also contain HTML with embedded image, data or URLs.  Remove those too.
    base64_pattern = (
        r'(<[^>]+(?:src|data|href|style)=["\'])data:[^;]+;base64,[^"\']+(["\'][^>]*>)'
    )
    cleaned_text = re.sub(base64_pattern, r"\1\2", cleaned_text)

    return cleaned_text


def simple_conversion(html: str) -> str:
    """Convert html to markdown.

    This is a simple conversion that strips html to keep only text,
    except for <pre> and <code> blocks. <pre> is replaced with triple backticks
    and <code> is replaced with single backticks.

    Args:
        html (str): The html to convert.

    Returns:
        str: The converted markdown.
    """
    # first parse html.  use the fast version
    soup = BeautifulSoup(
        html, "html.parser"
    )  # there are a few cases where lxml is not correct, even though it is faster
    # any tag inside <pre> is directly stripped.
    for pre in soup.find_all("pre"):
        for code in pre.find_all(True):
            code.unwrap()
    # code blocks outside of pre is converted to backtick
    for code in soup.find_all("code"):
        content = str(code.encode_contents().decode("utf-8"))
        code.replace_with("`" + content + "`")
    # Replace all <pre> contents with placeholders before we
    # completely strip out all text
    pre_dict = {}
    for pre in soup.find_all("pre"):
        random_str = str(uuid.uuid4())[:8]
        pre_name = f"__PRE_PLHDR_{len(pre_dict)}_{random_str}__"
        pre_dict[pre_name] = str(pre.encode_contents().decode("utf-8"))
        pre.replace_with(pre_name)

    # strip out all HTML markups
    # Replace <br> with a newline character
    for br in soup.find_all("br"):
        br.replace_with("\n")

    # Replace <p> with double newline (optional, depends on how you want to handle paragraphs)
    for p in soup.find_all("p"):
        p.insert_before("\n\n")

    text = soup.get_text()
    for key, value in pre_dict.items():
        text = text.replace(key, value)
    return text


def convert_html_to_md(
    html: str,
    executor: Executor,
    markdown_converter: Iterable[HTMLToMarkdownConverter] = (MarkdownifyConverter(),),
    timeout_ms=1000,
) -> str:
    """Convert html to markdown.

    # First try markdownify. If it fails, try our own conversion, which strips
    # html to keep only text, except for <pre> and <code> blocks.

    Args:
        html (str): The html to convert.
        timeout_ms (int): Timeout for each step in milliseconds. Maximum runtime is 3x timeout_ms.

    Returns:
        str: The converted markdown.
    """
    # Try markdownify
    for converter in markdown_converter:
        try:
            future = executor.submit(converter.convert, html)
            return future.result(timeout=timeout_ms / 1000)
        except Exception as e:
            print(f"Failed to convert html to markdown with converter: {e}")
    # If this does not work, try our own conversion
    try:
        future = executor.submit(simple_conversion, html)
        return future.result(timeout=timeout_ms / 1000)
    except Exception as e:
        print(f"Failed to convert html to markdown with simple_conversion: {e}")
        return ""


def retrieve_text_from_schema(html_text: str):
    """Extract page data following schema.org and OG convention if they exist."""
    soup = BeautifulSoup(html_text, "html.parser")
    script_tag = soup.find("script", type="application/ld+json")
    if not script_tag or not script_tag.text:
        return
    json_data: dict[str, Any] = json.loads(script_tag.text)
    title = json_data.get("headline")
    description = json_data.get("description")

    # Check for full text fields based on type
    article_body = (
        json_data.get("articleBody")
        or json_data.get("text")
        or json_data.get("reviewBody")
        or json_data.get("recipeInstructions")
    )

    if not article_body:
        return

    # Fallback to HTML meta and title tags if JSON-LD is not available
    if not title:
        title = soup.title.string if soup.title else "No title found."

    if not description:
        meta_description = soup.find("meta", attrs={"name": "description"})
        if meta_description:
            description = meta_description.find("content")

    return {
        "title": html.unescape(title or ""),
        "description": html.unescape(str(description) or ""),
        "article_body": html.unescape(article_body or ""),
    }


DISPLAY_NONE_RE = re.compile(r"\bdisplay\s*:\s*none\b", re.IGNORECASE)


def _has_display_none_style(element):
    if not isinstance(element, Tag) or not element.get("style", ""):
        return False
    style_attr = element["style"]
    if isinstance(style_attr, list):
        style_attr = " ".join(style_attr)
    return DISPLAY_NONE_RE.search(style_attr) is not None


def cleanup(text: str, no_repeat_links: bool = True, keep_links: bool = True) -> str:
    """Common normalization of HTML pages.

    Do a few common tasks that we almost always want to do for a code related page:

    - if <article> or <main> tag exists, only keep the contents in those.
    - strip out navigation bars, headers, footers, sidebars, script nodes.
    - wrap code blocks in <pre> tags. See note.
    - remove nodes that have a class name header*, footer*, sidebar*
    - remove adds
    - remove <i> and <b> tags that enclose no text
    - remove links on empty ranges and spaces.
    - remove repeated links.

    Note: Often ReadTheDocs, sphinx or g4g would use CSS instead of pre tags to
    denote a code block.  This causes a large amount of <code> nodes being
    rendered, messing up the source code inside.

    For obvious reasons this is harmful to model performance.

    Here we identify common patterns of those nodes and wrap them in <pre> tags.
    """

    try:
        soup = BeautifulSoup(text, "html5lib")
    except Exception:
        print("Failed to parse html")
        # This is ill-formed HTML
        return ""

    # Remove comments
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()

    # Remove elements that are not part of the main content
    for tag in soup(["nav", "header", "footer", "aside", "script", "template"]):
        tag.decompose()
    # If <article> tag exists, only keep the contents in those.
    for tag_name in ["article", "main", "body"]:
        if soup.find(tag_name):
            articles = [
                article
                for article in soup.find_all(tag_name)
                if not article.find_parent(tag_name)
            ]
            # Create a new soup object for the filtered content
            new_soup = BeautifulSoup("<html><body></body></html>", "html5lib")

            # Add the top-level articles to the new soup
            for article in articles:
                if isinstance(new_soup.body, Tag):
                    new_soup.body.append(article)
            soup = new_soup
            break

    # Remove data in tags
    for tag in soup.find_all(True):
        fields = "src", "data", "href", "style"
        for field in fields:
            if tag.has_attr(field) and tag[field].startswith("data:"):
                tag.unwrap()
                break

    # Remove interactive elements such as buttons, select, etc
    for tag in soup.find_all(
        ["button", "select", "option", "input", "form", "tool-tip", "action-menu"]
    ):
        tag.decompose()

    # remove columns that are of the gutter class
    for gutter_column in soup.find_all(["div", "tr", "td"], class_="gutter"):
        gutter_column.decompose()

    # Convert <textarea> to <pre>
    for textarea in soup.find_all("textarea"):
        pre_node = soup.new_tag("pre")
        if textarea.string is None:
            textarea.decompose()
            continue
        pre_node.string = textarea.string
        textarea.replace_with(pre_node)

    # Wrap code blocks in <pre> tags if not already wrapped
    # These general have a `code` or `highlight` class
    for code in soup.find_all(
        class_=re.compile(
            r"fragment|doxygen-awesome-fragment-wrapper|code|highlight|codeblock|code-content"
        )
    ):
        # This block must contain as least 2 <code> tags as children
        # and it must not be already wrapped in <pre>
        if (
            len(code.find_all("code")) < 2
            and not code.find(class_="blob-code-inner")
            and not code.find(class_="lineno")
        ) or code.find_parent("pre"):
            continue
        code.wrap(soup.new_tag("pre"))

    # Convert unwrapped code blocks to <pre> if it contains many lines
    for code in soup.find_all("code"):
        # unwrap all <span> inside code
        for span in code.find_all("span"):
            span.replace_with(span.text)

        if code.find_parent("pre"):
            continue
        if len(code.get_text().splitlines()) == 1:
            continue
        code.wrap(soup.new_tag("pre"))

    for line_number in soup.find_all(
        ["span", "div", "td"],
        class_=re.compile(r"line-number$|lineno$|linenos$|linenodiv$"),
    ):
        line_number.decompose()

    # Remove all tables in code blocks
    for pre_block in soup.find_all("pre"):
        # unwrap all <span> inside code
        for span in pre_block.find_all("span"):
            span.replace_with(span.text)

        for table in pre_block.find_all("table"):
            raw_content = ""
            for tr in table.find_all("tr"):
                for td in tr.find_all("td"):
                    raw_content += td.decode_contents()
                raw_content += "\n"
            new_content = BeautifulSoup(raw_content, "html.parser")
            table.replace_with(new_content)

    # don't decompose an element if class contains the following
    no_decompose = "body", "grid", "content", "code"

    # Remove nodes that have a class name header*, footer*, sidebar*
    blacklist = [
        "footer",
        "sidebar",
        "btn-group",
        "article-bottom",
        "article_bottom",
        "article-pgnavi",
        "side-widget",
        "suspended-panel",
        "discussion_panel",
        "dropdown",
        "three_dot_dropdown",
        "toc",
        "nav",
        "menu",
        "menu2",
        "button",
        "navbar",
        "btn",
        "sidesection",
        "google-ads",
        "modal",
        "reward",
        "search",
        "tag",
        "btn",
        "flash-warn",
        "_attribution",
    ]
    bl_string = "|".join(blacklist)
    pattern = re.compile(f"^({bl_string})|({bl_string})$|^(header)$")
    for tag in soup.find_all(
        [
            "div",
            "a",
            "span",
            "p",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "li",
            "ul",
            "ol",
            "section",
        ],
        class_=pattern,
    ):
        if (
            tag.attrs
            and tag["class"]
            and any(keyword in c for c in tag["class"] for keyword in no_decompose)
        ):
            continue
        tag.decompose()

    # Remove ads
    combined_selector = (
        # Login info
        "#login-link, .login-link, "
        # Trademarks, and copyright notices
        "#trademark, .trademark, "
        "#notice, .notice, "
        '[class^="copyright"], [class^="trademark"], [class^="notices-"], '
        '[id^="copyright"], [id^="trademark"], [id^="notices-"], '
        # menus headers footers
        "#gra1, .gra1,"
        "#header, .header, #register-box, .register-box, #register-Box, .register-Box, "
        '[id^="stealth-"], [class^="stealth-"], '
        '[id$="footer"], [id^="footer"], '
        '[id$="menu"], [id^="menu"], '
        '[id$="sidebar"], [id^="sidebar"], '
        '[id~="nav-bar"], [class~="nav-bar"], '
        '[id~="navbtn"], [class~="navbtn"], '
        '[id$="-nav"], [id^="nav-"], '
        '[id~="-navigation-"], [class~="-navigation-"], '
        '[id="mobile-no-show"], [class="mobile-no-show"], '
        '[class^="button"], [class$="button"], '
        ".pagetop, #pagetop, .pagebottom, #pagebottom, "
        "#related-secion, .related-secion, "
        ".tags, .web-tags, .mentions, .related-section, .cikJG, .lang, "
        ".evaluate-box, #evaluate-box, "
        ".breadcrumbs, #breadcrumbs, .breadcrumbs-container, #breadcrumbs-container, "
        ".breadcrumb, #breadcrumb, "
        ".outdated, "
        # Cookies
        '[id="CookieNotification"], [class="CookieNotification"], '
        '[id$="-disclaimer"], [id^="disclaimer-"], '
        # Ads
        "#ad, #ads, #advertisement, #sponsor, #sponsored, #promo, "
        ".ad, .ads, .advertisement, .sponsor, .sponsored, .promo, "
        "#ad-container, .ad-container, "
        "#ad-box, .ad-box, "
        "#ad-slot, .ad-slot, "
        "#ad-banner, .ad-banner, "
        "#ad-unit, .ad-unit, "
        "#ad-wrap, .ad-wrap, "
        "#ad-content, .ad-content, "
        "#ad-section, .ad-section, "
        "#google_ads_iframe, #google-ads-frame, #google-ad-right, "
        # class name starts or ends with ads
        '[class^="ads-"], [class$="-ads"], '
        # Google add flags
        '[id^="GFG_AD_"], [class^="GFG_AD_"]'
    )

    for element in soup.select(combined_selector):
        if element is None:
            continue
        if element.attrs is not None and element.get("class"):
            if any(keyword in c for c in element["class"] for keyword in no_decompose):
                continue
        element.decompose()

    # remove small elements
    for small in soup.find_all("small"):
        small.decompose()

    # Remove labels for input elements
    for label in soup.find_all("label"):
        label.decompose()

    # Find all <i>, <b> and <a> tags that enclose no text
    for tag in soup.find_all(["i", "b", "a"]):
        # Check if the tag is empty or contains only whitespace
        if not tag.get_text(strip=True):
            tag.unwrap()

    # Remove images
    for img in soup.find_all("img"):
        img.decompose()

    # remove "source" links
    for link in soup.find_all("a", class_="src"):
        if (link.get_text(strip=True) or "").lower() == "source":
            link.decompose()
    for link in soup.find_all("a", class_="anchor"):
        if link.get_text(strip=True) == "§":
            link.decompose()
    for since_version in soup.find_all("span", class_="since"):
        since_version.decompose()
    for empty_span in soup.find_all(
        "span", class_=re.compile("^(rightside)$|^(out-of-band)$")
    ):
        if empty_span.get_text(strip=True) == "·":
            empty_span.decompose()

    # remove hidden summary blocks
    for summary in soup.find_all("summary", class_="hideme"):
        summary.decompose()

    # any hidden elements that do not contain code or pre are removed
    for hidden in soup.find_all(_has_display_none_style):
        if hidden.find("code") is None and hidden.find("pre") is None:
            hidden.decompose()

    if not keep_links:
        remove_links(soup)
    elif no_repeat_links:
        known_links = set()
        for link in soup.find_all("a"):
            if "href" not in link.attrs or link["href"] in known_links:
                link.unwrap()
            else:
                known_links.add(link["href"])

    cleaned_text = str(soup)

    return cleaned_text


def replace_excessive_newlines(markdown_text):
    # Replace 3 or more newlines with exactly 2 newlines
    cleaned_text = re.sub(r"^\s+\n", "\n", markdown_text, flags=re.MULTILINE)
    cleaned_text = re.sub(r"\n{3,}", "\n\n", cleaned_text)
    return cleaned_text


def remove_links(soup: BeautifulSoup):
    """Remove links that are empty or contain only whitespace."""
    for link in soup.find_all("a"):
        link.unwrap()


def robust_conversion(
    html_text: str,
    executor: Executor,
    timeout_ms: int = 1000,
    cleanup_html: bool = True,
    keep_links: bool = True,
    use_meta_text: bool = False,
    markdown_converter: Iterable[HTMLToMarkdownConverter] = (MarkdownifyConverter(),),
) -> str:
    """Convert html to markdown.

    This is convert_html_to_md with error handling and invalid utf-8 handling,
    and also removes invalid elements such as ads, menu and sidebar.

    Also removes encoded data urls and excessive newlines.

    use_meta_text will not always return the correct whitespaces, so it should
    only be used when we have too much garbage such as ads.

    Args:
        html (str): The html to convert.
        timeout_ms (int): Timeout in milliseconds.
        cleanup_html (bool): Whether to cleanup the html before converting.
        keep_links (bool): Whether to keep links.
        use_meta_text (bool): Whether to use meta description if no schema.org data is available.

    Returns:
        str: The converted markdown.
    """

    if use_meta_text:
        data = retrieve_text_from_schema(html_text)
        if data:
            return f'# {data["title"]}\n\n{data["description"]}\n\n{data["article_body"]}\n'

    if cleanup_html:
        html_text = cleanup(html_text, keep_links=keep_links)
    try:
        converted = convert_html_to_md(
            html_text,
            executor,
            markdown_converter=markdown_converter,
            timeout_ms=timeout_ms,
        )
    except Exception as e:  # pylint: disable=broad-except
        print(f"Failed to convert answer body to text: {e}")
        converted = html_text
    if not converted:
        return ""

    converted = replace_excessive_newlines(converted)
    converted = remove_data_urls(converted)

    # Replace all non-ascii characters with their unicode escape
    return "".join(
        f"[\\u{ord(c):04x}]" if "\ud800" <= c <= "\udfff" else c for c in converted
    )
