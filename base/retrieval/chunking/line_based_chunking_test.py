"""Tests for the line-based chunking logic."""

from __future__ import annotations


import difflib
from random import Random
from textwrap import dedent
from typing import Sequence

import pytest

from base.retrieval.chunking.line_based_chunking import (
    LineChunkContents,
    split_line_chunks,
)


def test_chunking_no_overlap():
    """Basic test cases."""
    text = dedent(
        """
        a
        b
        c
        dd
        ee
        ff
        ggg
        hhh
        iii
        jjjj
    """
    ).strip()
    chunks = split_and_validate(
        text, chunk_max_lines=3, chunk_max_chars=1000, overlap_lines=0
    )

    assert len(chunks) == 4
    assert chunks[0].text == "a\nb\nc\n"
    assert chunks[1].text == "dd\nee\nff\n"
    assert chunks[2].text == "ggg\nhhh\niii\n"
    assert chunks[3].text == "jjjj"

    assert chunks[0].char_offset == 0
    assert chunks[0].line_offset == 0
    assert chunks[0].length_in_lines == 3

    assert chunks[1].char_offset == text.index("d")
    assert chunks[1].line_offset == 3
    assert chunks[1].length_in_lines == 3

    assert chunks[2].char_offset == text.index("ggg")
    assert chunks[2].line_offset == 6
    assert chunks[2].length_in_lines == 3

    assert chunks[3].char_offset == text.index("jjjj")
    assert chunks[3].line_offset == 9
    assert chunks[3].length_in_lines == 1


def test_chunking_with_overlap():
    text = dedent(
        """
        a
        b
        c
        dd
        ee
        ff
        ggg
        hhh
        iii
        jjjj
    """
    ).strip()

    # test with overlap_lines = 1
    chunks = split_and_validate(
        text, chunk_max_lines=5, chunk_max_chars=1000, overlap_lines=1
    )

    assert len(chunks) == 3
    assert chunks[0].text == "a\nb\nc\ndd\nee\n"
    assert chunks[1].text == "ee\nff\nggg\nhhh\niii\n"
    assert chunks[2].text == "iii\njjjj"

    # test with overlap_lines = 2
    chunks = split_and_validate(
        text, chunk_max_lines=5, chunk_max_chars=1000, overlap_lines=2
    )

    assert len(chunks) == 3
    assert chunks[0].text == "a\nb\nc\ndd\nee\n"
    assert chunks[1].text == "dd\nee\nff\nggg\nhhh\n"
    assert chunks[2].text == "ggg\nhhh\niii\njjjj"


def test_chunking_max_chars_limit():
    """Test the effect of hitting the chunk_max_chars limit."""
    text = dedent(
        """
        a
        b
        c
        dd
        ee
        ff
        ggg
        hhh
        iii
        jjjj
    """
    ).strip()

    chunks = split_and_validate(
        text, chunk_max_lines=1000, chunk_max_chars=8, overlap_lines=0
    )

    assert len(chunks) == 5
    assert chunks[0].text == "a\nb\nc\n"
    assert chunks[1].text == "dd\nee\n"
    assert chunks[2].text == "ff\nggg\n"
    assert chunks[3].text == "hhh\niii\n"
    assert chunks[4].text == "jjjj"

    chunks = split_and_validate(
        text, chunk_max_lines=1000, chunk_max_chars=8, overlap_lines=1
    )
    assert len(chunks) == 7
    assert chunks[0].text == "a\nb\nc\n"
    assert chunks[1].text == "c\ndd\nee\n"
    assert chunks[2].text == "ee\nff\n"
    assert chunks[3].text == "ff\nggg\n"
    assert chunks[4].text == "ggg\nhhh\n"
    assert chunks[5].text == "hhh\niii\n"
    assert chunks[6].text == "iii\njjjj"


def test_chunking_max_line_width_limit():
    """Test the effect of hitting the max_line_width limit."""
    text = dedent(
        """
        a
        b
        c
        123456789
        ee
    """
    ).strip()

    # set the max_line_width to only 8 should cause no chunks being emitted
    chunks = split_line_chunks(
        text,
        chunk_max_lines=1000,
        chunk_max_chars=1000,
        overlap_lines=0,
        max_line_width=8,
    )
    chunks = list(chunks)
    assert chunks == []


@pytest.mark.parametrize("overlap_lines", [0, 1, 2, 3, 4, 5, 6])
def test_chunking_empty_text(overlap_lines: int):
    """Test the behavior on an empty text."""
    chunks = split_and_validate(
        "", chunk_max_lines=20, chunk_max_chars=1000, overlap_lines=overlap_lines
    )
    assert len(chunks) == 1
    assert chunks[0].text == ""
    assert chunks[0].char_offset == 0
    assert chunks[0].line_offset == 0


def test_chunking_random_string():
    """Validating the chunking logic on many random strings."""
    rng = Random(42)
    n_cases = 5000
    for i in range(n_cases):
        text = random_str(rng, max_len=min(i, 1000))
        overlap_lines = rng.randint(0, 7)
        chunk_max_lines = rng.randint(1, 7)
        chunk_max_chars = rng.randint(1, 500)
        try:
            split_and_validate(
                text,
                chunk_max_lines=chunk_max_lines,
                chunk_max_chars=chunk_max_chars,
                overlap_lines=overlap_lines,
            )
        except Exception:
            print(
                f"Failed on case {i}:",
                f"{overlap_lines=}, {chunk_max_chars=}, {chunk_max_lines=}",
                f"text: {repr(text)}",
            )
            raise


def split_and_validate(
    text: str,
    chunk_max_lines: int,
    chunk_max_chars: int,
    overlap_lines: int,
    max_line_width: int = 2048,
) -> list[LineChunkContents]:
    """Run `split_line_chunks` and then validate the output before returning."""
    chunks = split_line_chunks(
        text,
        chunk_max_lines=chunk_max_lines,
        chunk_max_chars=chunk_max_chars,
        overlap_lines=overlap_lines,
        max_line_width=max_line_width,
    )
    chunks = list(chunks)
    validate_chunks(
        chunks,
        text,
        overlap_lines=overlap_lines,
        chunk_max_lines=chunk_max_lines,
        chunk_max_chars=chunk_max_chars,
    )
    return chunks


def validate_chunks(
    chunks: Sequence[LineChunkContents],
    text: str,
    overlap_lines: int,
    chunk_max_lines: int,
    chunk_max_chars: int,
):
    """Check `chunks` against `text` for the following properties:
    1. The concatenation of all chunks (minus overlaps) yields the original text
    2. text[chunk.character_range] == chunk.text
    3. "".join(lines[chunk.line_range]) == chunk.text
    4. len(chunk.text) <= chunk_max_chars unless chunk has only a single line
    5. len(chunk.text.splitlines()) <= chunk_max_lines
    """

    # check property 1

    if overlap_lines == 0:
        # just do the simple concatenation
        chunk_texts = [chunk.text for chunk in chunks]
        assert_str_eq("".join(chunk_texts), text)
    else:
        # use character ranges to remove the overlaps
        chunks_minus_overlap: list[str] = []
        last_chunk: LineChunkContents | None = None

        for chunk in chunks:
            if last_chunk is None:
                chunks_minus_overlap.append(chunk.text)
            else:
                # remove leading part that overlaps with the previous chunk
                overlap = last_chunk.crange().intersect(chunk.crange())
                assert overlap is not None
                chunks_minus_overlap.append(chunk.text[len(overlap) :])
            last_chunk = chunk

        assert_str_eq("".join(chunks_minus_overlap), text)

    # check property 2
    for chunk in chunks:
        text_at_range = text[chunk.char_offset : chunk.char_offset + len(chunk.text)]
        assert_str_eq(text_at_range, chunk.text)

    # check property 3
    all_lines = text.splitlines(keepends=True)
    for chunk in chunks:
        actual_lines = all_lines[
            chunk.line_offset : chunk.line_offset + chunk.length_in_lines
        ]
        assert_str_eq("".join(actual_lines), chunk.text)

    for chunk in chunks:
        chunk_lines = chunk.text.splitlines(keepends=True)
        # check property 4
        assert len(chunk.text) <= chunk_max_chars or len(chunk_lines) <= 1

        # check property 5
        assert len(chunk_lines) <= chunk_max_lines


def random_char(rng: Random) -> str:
    """Return a random character for testing."""
    if rng.random() < 0.3:
        return rng.choice([" ", "\n", "\t", "\r"])
    # return a random visible character
    return rng.choice(["a", "b", "c", "♺", "😃", ":", "?"])


def random_str(rng: Random, max_len: int) -> str:
    """Return a random string for testing."""
    n = rng.randint(0, max_len)
    return "".join(random_char(rng) for _ in range(n))


def assert_str_eq(actual: str, expect: str):
    """Assert that two strings x and y are equal.

    If not equal, will raise an `AssertionError` containing a diff of the two strings.
    """
    if actual != expect:
        diff = show_str_diff(expect, actual)
        raise AssertionError(f"Strings are not equal, diff shown below:\n{diff}")


def show_str_diff(old: str, new: str) -> str:
    """Show the diff between two strings."""
    difflines = difflib.ndiff(
        old.splitlines(keepends=True), new.splitlines(keepends=True)
    )
    return "".join(difflines)
