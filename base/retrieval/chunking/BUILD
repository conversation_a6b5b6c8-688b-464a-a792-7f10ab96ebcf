load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "line_based_chunking",
    srcs = ["line_based_chunking.py"],
    visibility = ["//base/prompt_format_chat/lib:__subpackages__"],
    deps = [
        "//base/ranges",
    ],
)

pytest_test(
    name = "line_based_chunking_test",
    srcs = ["line_based_chunking_test.py"],
    deps = [
        ":line_based_chunking",
        "//base/ranges",
    ],
)

py_library(
    name = "smart_chunking",
    srcs = ["smart_chunking.py"],
    deps = [
        ":line_based_chunking",
        "//base/languages",
        "//base/ranges",
        "//base/static_analysis:indentation_utils",
        "//base/static_analysis:smart_header",
    ],
)

pytest_test(
    name = "smart_chunking_test",
    srcs = ["smart_chunking_test.py"],
    deps = [
        ":smart_chunking",
        "//base/static_analysis:common",
        "//base/test_utils:testing_utils",
    ],
)

py_library(
    name = "chunking",
    srcs = [
        "__init__.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":line_based_chunking",
        ":smart_chunking",
    ],
)
