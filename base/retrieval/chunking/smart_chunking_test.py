from random import Random
from textwrap import dedent

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.common import assert_eq, assert_str_eq
from base.test_utils.testing_utils import error_context, random_str, run_property_test
from base.retrieval.chunking.smart_chunking import (
    SmartChunker,
    expand_point_by_smart_chunks,
    smart_chunk_size,
)


def test_return_single_chunk():
    # If the entire file can be fit into a single chunk, it should be returned as a single chunk.

    def test_case(rng: Random):
        text = random_str(rng, max_len=600)
        with error_context(f"text: {repr(text)}"):
            chunker = SmartChunker(max_chunk_chars=len(text) + 1)
            chunks = chunker.split_chunks(text, None)
            if text:
                assert_eq(len(chunks), 1)
                assert_str_eq(chunks[0].text, text)

    run_property_test(test_case, trials=10_000)


def test_chunks_valid():
    # When no line is too long, all chunks joining together should give back
    # the original text.

    def test_case(rng: Random):
        text = random_str(rng, max_len=800)
        # filter away long lines
        lines = text.splitlines(keepends=True)
        max_chunk_chars = rng.randint(40, 200)
        lines = [line for line in lines if smart_chunk_size(line) < max_chunk_chars]
        text = "".join(lines)
        with error_context(f"text: {repr(text)}"):
            chunker = SmartChunker(max_chunk_chars=max_chunk_chars)
            chunks = chunker.split_chunks(text, None)
            reconstruct = "".join(chunk.text for chunk in chunks)
            assert_str_eq(reconstruct, text)
            for chunk in chunks:
                assert smart_chunk_size(chunk.text) <= max_chunk_chars

    run_property_test(test_case, trials=10_000)


def test_skip_long_lines():
    # any lines longer than max_chunk_size should be skipped
    example = dedent(
        """\
        ab
        a very long line
        a very long line
        ab
        ab
        ab
        """
    )
    # Note that each newline counts as a single character, so we need
    # max_chunk_size=6 to fit in 2 lines
    chunker = SmartChunker(max_chunk_chars=6)
    chunks = chunker.split_chunks(example, None)
    chunk_texts = [chunk.text for chunk in chunks]
    expected_chunks = ["ab\n", "ab\nab\n", "ab\n"]
    assert_eq(chunk_texts, expected_chunks)


def test_truncate_long_headers():
    example = """\
class a_very_long_header:
    def method_1():
        body_line_1

    def method2():
        body_line_2
"""
    chunker = SmartChunker(max_chunk_chars=60, max_header_chars=1000)
    chunks = chunker.split_chunks(example, "Python")
    chunk_texts = [chunk.text for chunk in chunks]
    expected_chunks = [
        """\
class a_very_long_header:
    def method_1():
        body_line_1
""",
        """\

    def method2():
        body_line_2
""",
    ]
    assert_eq(chunk_texts, expected_chunks)
    header_texts = [chunk.header for chunk in chunks]
    expected_headers = ["", "class a_very_long_header:\n"]
    assert_eq(header_texts, expected_headers)

    chunker.max_header_chars = 10
    chunks = chunker.split_chunks(example, "Python")
    header_texts = [chunk.header for chunk in chunks]
    expected_headers = ["", "class a_ve\n"]
    assert_eq(header_texts, expected_headers)


def test_merge_adjacent():
    # The first phase will break between "b" and "c".
    # But the second phase should merge them.
    example = dedent(
        """\
        a
          b

        c

        12345
        """
    )
    # Note that each newline counts as a single character.
    chunker = SmartChunker(max_chunk_chars=8)
    chunks = chunker.split_chunks(example, None)
    chunk_texts = [chunk.text for chunk in chunks]
    expected_chunks = ["a\n  b\n\nc\n", "\n12345\n"]
    assert_eq(chunk_texts, expected_chunks)


def test_no_merge_with_dropped_line():
    # Test that chunks aren't merged when separated by a dropped long line
    example = dedent(
        """\
        a
          b
        very_long_line_that_exceeds_max_chunk_chars
        c
        d
        """
    )
    # Set max_chunk_chars small enough to drop the long line
    chunker = SmartChunker(max_chunk_chars=20)
    chunks = chunker.split_chunks(example, None)
    chunk_texts = [chunk.text for chunk in chunks]
    # Should have 2 chunks - the long line is dropped and creates a gap
    # between line ranges, preventing merging
    expected_chunks = ["a\n  b\n", "c\nd\n"]
    assert_eq(chunk_texts, expected_chunks)

    # Verify the line ranges aren't adjacent
    assert chunks[0].lrange().stop != chunks[1].line_offset


def test_point_expansion_to_full_doc():
    # when the entire document is smaller than the smart chunk limit, the expansion
    # logic should always expand it to the full document.

    def test_case(rng: Random):
        max_text_len = rng.randint(0, 1000)
        text = random_str(rng, max_len=max_text_len)
        point = rng.randint(0, len(text))
        expanded = expand_point_by_smart_chunks(text, point, chunk_size=len(text) + 1)
        assert_eq(expanded, CharRange(0, len(text)))

    run_property_test(test_case)


def test_point_expansion_cases():
    text = "123456789\nabcdefg\n"
    chunk_size = 10  # this limit will break `text` into two chunks
    assert len(SmartChunker(chunk_size).split_chunks(text, None)) == 2

    def expand(point: int):
        return expand_point_by_smart_chunks(text, point, chunk_size)

    assert expand(0) == CharRange(0, 10)
    assert expand(5) == CharRange(0, 10)
    assert expand(9) == CharRange(0, 10)
    assert expand(10) == CharRange(10, 18)
    assert expand(11) == CharRange(10, 18)
    assert expand(18) == CharRange(10, 18)
