"""Line chunking utils to be shared between research and prod."""

from functools import cached_property
from typing import Iterable, NamedTuple

from base.ranges.range_types import CharRange


class LineChunkContents(NamedTuple):
    """The core contents of a line chunk."""

    text: str
    line_offset: int
    char_offset: int
    length_in_lines: int
    header: str = ""

    def crange(self) -> CharRange:
        return <PERSON><PERSON><PERSON><PERSON><PERSON>(self.char_offset, self.char_offset + len(self.text))

    def lrange(self) -> CharRange:
        return Char<PERSON>ange(self.line_offset, self.line_offset + self.length_in_lines)


def split_line_chunks(
    text: str,
    chunk_max_lines: int,
    chunk_max_chars: int,
    overlap_lines: int,
    max_line_width: int = 2048,
) -> Iterable[LineChunkContents]:
    """Split given text into line chunks.

    - A new chunk is formed if a chunk reaches chunk_max_lines (in lines) or
    chunk_max_chars (in characters).
    - A line is never broken into two chunks even if the chunk is larger than
    chunk_max_chars, so max_chunk_size is a hint, not a hard limit.
    - This behavior is Unicode-safe as it only splits around newlines.

    Args:
        text: Text to split.
        chunk_max_lines: Maximum number of lines in a chunk.
        chunk_max_chars: Maximum number of characters in a chunk.
        overlap_lines: Number of overlapping lines between adjacent chunks.
        max_line_width: If the text contain any line longer than this limit, no chunks\
            will be emitted.
    """
    if overlap_lines < 0:
        raise ValueError(f"overlap_lines must be non-negative, got {overlap_lines}")

    all_lines = text.splitlines(keepends=True)
    if any(len(line) > max_line_width for line in all_lines):
        return

    chunk_lines: list[str] = []
    chunk_chars = 0
    chunk_line_start = 0
    chunk_char_start = 0

    next_line_id = 0
    while next_line_id < len(all_lines):
        next_line = all_lines[next_line_id]
        # check if including the next line would exceed the limits
        if chunk_lines and (
            len(chunk_lines) + 1 > chunk_max_lines
            or chunk_chars + len(next_line) > chunk_max_chars
        ):
            # cannot include next_line, so yield the current chunk
            chunk_text = "".join(chunk_lines)
            yield LineChunkContents(
                chunk_text,
                line_offset=chunk_line_start,
                char_offset=chunk_char_start,
                length_in_lines=len(chunk_lines),
            )
            # then advance to the next chunk start location
            # note that we at least advance by 1 line to keep making progress
            lines_to_advance = max(1, len(chunk_lines) - overlap_lines)
            chars_to_advance = sum(len(line) for line in chunk_lines[:lines_to_advance])
            chunk_line_start += lines_to_advance
            chunk_char_start += chars_to_advance
            chunk_lines = []
            chunk_chars = 0
            next_line_id = chunk_line_start
        else:
            # can include next_line, so add it to the current chunk
            chunk_lines.append(next_line)
            chunk_chars += len(next_line)
            next_line_id += 1

    # the second condition below ensures that we still return 1 chunk for empty docs
    # since it can help to show the model that a document exists even if it is empty.
    if chunk_lines or chunk_line_start == 0:
        chunk_text = "".join(chunk_lines)
        yield LineChunkContents(
            chunk_text,
            line_offset=chunk_line_start,
            char_offset=chunk_char_start,
            length_in_lines=len(chunk_lines),
        )
