"""Heuristics to break up code into chunks based on indentations and newlines."""

import re
from dataclasses import dataclass
from textwrap import indent

from base.languages.languages import LanguageId
from base.ranges.range_types import <PERSON>r<PERSON><PERSON><PERSON>
from base.retrieval.chunking.line_based_chunking import LineChunkContents
from base.static_analysis.indentation_utils import compute_line_indentations
from base.static_analysis.smart_header import assign_line_headers


@dataclass
class SmartChunker:
    """Heuristically break up text into chunks using indentations and empty lines."""

    max_chunk_chars: int
    """Max length of a chunk in characters.

    Any leading or trailing whitespaces in each line are ignored when counting.
    The algorithm will try to greedily break at the most natural break points while
    trying to maximize the chunk size as much as possible.
    Any line longer than this limit will end the current chunk and be skipped.
    """

    max_headers: int = 3
    """The maximum number of headers to include in the chunk.

    Deeper headers will be ignored. Setting this to 0 will disable smart headers.
    """

    max_header_chars: int = 150
    """The maximum number of characters in each header."""

    def split_chunks(
        self, text: str, lang: LanguageId | None
    ) -> list[LineChunkContents]:
        """Split text into chunks using indentations and empty lines.

        This algorithm greedily finds the next chunk by first include as many lines as
        possible under the size limit, then backtracking to an empty line with the
        lowest indentation level. When there is a tie, it prefers the one that causes an
        indentation drop, and if there's still a tie, it breaks at the last such line.
        """

        all_lines = text.splitlines(keepends=True)

        line_to_headers = None
        if self.max_headers > 0:
            line_to_headers = assign_line_headers(
                all_lines, lang, max_depth=self.max_headers
            )

        smart_line_sizes = [_smart_line_size(line) for line in all_lines]
        # compute the indentation level of each line
        indent_levels = compute_line_indentations(all_lines)
        # find all lines that caused a drop in indentation level
        is_indent_drop = [
            i != 0 and indent_levels[i] < indent_levels[i - 1]
            for i in range(len(indent_levels))
        ]
        # find all empty lines
        is_empty_line = [line.strip() == "" for line in all_lines]
        # find all lines that contain only "closing" characters
        closing_pattern = r"(,|;|}|]|\))+"
        is_closing_line = [
            re.fullmatch(closing_pattern, line.strip()) is not None
            for line in all_lines
        ]
        # we may break at any closing line or any empty line that is not preceded by
        # another empty line
        is_break_point = [
            i > 0
            and (is_closing_line[i] or (is_empty_line[i] and not is_empty_line[i - 1]))
            for i in range(len(all_lines))
        ]

        def find_next(line_offset: int, char_offset: int) -> LineChunkContents | None:
            """Greedily find the next chunk starting from the given location."""
            if line_offset >= len(all_lines):
                return None
            assert smart_line_sizes[line_offset] <= self.max_chunk_chars

            def make_chunk(end_line: int):
                chunk_text = "".join(all_lines[line_offset:end_line])
                headers = line_to_headers[line_offset] if line_to_headers else ()
                header_text = "".join(
                    all_lines[h.line_number][: self.max_header_chars].rstrip() + "\n"
                    for h in headers
                )
                return LineChunkContents(
                    text=chunk_text,
                    line_offset=line_offset,
                    char_offset=char_offset,
                    length_in_lines=end_line - line_offset,
                    header=header_text,
                )

            # First, find the largest chunk that can fit under the size limit
            smart_size = 0
            end_line = line_offset
            for i in range(line_offset, len(all_lines)):
                new_size = smart_size + smart_line_sizes[i]
                if new_size > self.max_chunk_chars:
                    break
                end_line = i + 1
                smart_size = new_size
            assert end_line > line_offset, f"{line_offset=}, {end_line=}, {smart_size=}"
            assert smart_size > 0, f"{line_offset=}, {end_line=}, {smart_size=}"

            # If this is all that's left, return as a single chunk now.
            if end_line == len(all_lines):
                return make_chunk(end_line)

            # Otherwise, search for the best break point that maximizes the tuple below
            # (-indent_level, is_indent_drop, line_number).
            def sort_key(i: int):
                return (
                    -indent_levels[i],
                    is_indent_drop[i] or is_indent_drop[i - 1],
                    i,
                )

            break_points = {
                i for i in range(line_offset + 1, end_line) if is_break_point[i]
            }
            if break_points:
                end_line = max(break_points, key=sort_key)
            if is_closing_line[end_line]:
                # let's include the closing line in the current chunk as well
                end_line += 1

            return make_chunk(end_line)

        def get_smart_line_size(chunk: LineChunkContents) -> int:
            return sum(smart_line_sizes[i] for i in chunk.lrange())

        char_offset = 0
        line_offset = 0
        all_chunks = list[LineChunkContents]()
        while line_offset < len(all_lines):
            if smart_line_sizes[line_offset] > self.max_chunk_chars:
                # skip this super long line
                line_size = len(all_lines[line_offset])
                line_offset += 1
                char_offset += line_size
                continue
            chunk = find_next(line_offset, char_offset)
            if chunk is None:
                break
            # try to merge this chunk with the previous one if possible
            if (
                all_chunks
                and (prev_chunk := all_chunks[-1]).lrange().stop == chunk.line_offset
                and get_smart_line_size(prev_chunk) + get_smart_line_size(chunk)
                <= self.max_chunk_chars
            ):
                all_chunks[-1] = LineChunkContents(
                    text=prev_chunk.text + chunk.text,
                    line_offset=prev_chunk.line_offset,
                    char_offset=prev_chunk.char_offset,
                    length_in_lines=prev_chunk.length_in_lines + chunk.length_in_lines,
                    header=prev_chunk.header,
                )
            else:
                all_chunks.append(chunk)
            line_offset = chunk.line_offset + chunk.length_in_lines
            char_offset = chunk.char_offset + len(chunk.text)
        return all_chunks

    def print_split(self, text: str, lang: LanguageId | None) -> None:
        """Print the split chunks for debugging."""
        indent_levels = compute_line_indentations(text.splitlines(keepends=True))
        for i, chunk in enumerate(self.split_chunks(text, lang)):
            smart_size = sum(
                _smart_line_size(line) for line in chunk.text.splitlines(keepends=True)
            )
            title = f" Chunk {i}, lines={chunk.length_in_lines}, size={len(chunk.text)}, {smart_size=} "
            print("~=" * 20 + title + "~=" * 20)
            print("indent | code")
            print("-------+------" + "-" * 60)
            print(indent(chunk.header, prefix="       @ "), end="")
            chunk_lines = chunk.text.splitlines(keepends=False)
            for line, indent_level in zip(
                chunk_lines, indent_levels[chunk.line_offset :]
            ):
                print(f"{indent_level:6d} | {line}")


def expand_point_by_smart_chunks(text: str, point: int, chunk_size: int) -> CharRange:
    """Expand an insertion point into a character range using smart chunks.

    When the insertion point is right in between two chunks, the second chunk's range
    will be returned (since the first chunk always ends with a newline).
    """
    chunker = SmartChunker(max_chunk_chars=chunk_size, max_headers=0)
    chunks = chunker.split_chunks(text, None)
    point_range = CharRange.point(point)
    touched_ranges = [
        chunk.crange() for chunk in chunks if chunk.crange().touches(point_range)
    ]
    match touched_ranges:
        case [crange]:
            return crange
        case [_, crange2]:
            return crange2
        case _:
            return point_range


def _smart_line_size(line: str) -> int:
    """Count the number of non-leading/trailing characters in a line.

    This better correlates with token counts than `len(line)`.
    """
    return len(line.strip()) + 1


def smart_chunk_size(text: str) -> int:
    """Count the total number of non-leading/trailing characters in each line.

    This function is meant to be used for testing.
    """
    return sum(_smart_line_size(line) for line in text.splitlines(keepends=True))
