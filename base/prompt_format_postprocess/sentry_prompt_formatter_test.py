"""Tests for the Sentry prompt formatter."""

from textwrap import dedent

import pytest

from base.prompt_format.common import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ChatResultNodeType
from base.prompt_format_postprocess.sentry_prompt_formatter import (
    SentryPromptFormatter,
    SentryPromptInput,
    SentryTokenApportionment,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer


@pytest.fixture
def tokenizer():
    return Qwen25CoderTokenizer()


@pytest.fixture
def token_apportionment():
    return SentryTokenApportionment(
        max_prompt_len=8192,  # 8k for prompt
    )


@pytest.fixture
def formatter(tokenizer, token_apportionment):
    return SentryPromptFormatter(
        tokenizer=tokenizer,
        token_apportionment=token_apportionment,
    )


def test_format_prompt_basic(formatter, tokenizer):
    """Test basic prompt formatting with minimal input."""
    prompt_input = SentryPromptInput(
        chat_response=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="Hello, how can I help you?",
            )
        ],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    expected_output = dedent("""
<|fim_prefix|>
Hello, how can I help you?
<|fim_middle|>
""").lstrip()

    assert text == expected_output


def test_format_prompt_with_newlines(formatter, tokenizer):
    """Test prompt formatting with text containing newlines."""
    prompt_input = SentryPromptInput(
        chat_response=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="Line 1\nLine 2\nLine 3",
            )
        ],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    expected_output = dedent("""
<|fim_prefix|>
Line 1
Line 2
Line 3
<|fim_middle|>
""").lstrip()

    assert text == expected_output


def test_format_prompt_exceeds_max_length(formatter, tokenizer):
    """Test that prompt is truncated when it exceeds max length."""
    # Create a very long message that would exceed max length
    long_message = "x" * 20000
    prompt_input = SentryPromptInput(
        chat_response=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.RAW_RESPONSE,
                content=long_message,
            )
        ],
    )
    output = formatter.format_prompt(prompt_input)

    # The exact truncation point may vary, but we can verify the structure
    assert len(output.tokens) <= formatter.token_apportionment.max_prompt_len
    text = tokenizer.detokenize(output.tokens)
    assert text.startswith("<|fim_prefix|>\n")
    assert text.endswith("\n<|fim_middle|>\n")
