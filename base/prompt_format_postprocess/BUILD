load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "prompt_format_postprocess",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":sentry_prompt_formatter",
    ],
)

py_library(
    name = "sentry_prompt_formatter",
    srcs = [
        "sentry_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:structured_binks_prompt_formatter",
        "//base/prompt_format_chat:tokenized_qwen_prompt_formatter",
        "//base/prompt_format_chat/lib:string_formatter",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/tokenizers:qwen25coder_tokenizer",
    ],
)

pytest_test(
    name = "sentry_prompt_formatter_test",
    srcs = ["sentry_prompt_formatter_test.py"],
    deps = [
        ":sentry_prompt_formatter",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers:qwen25coder_tokenizer",
    ],
)
