"""The module formats tokenized prompts for Sentry post-processing models."""

from dataclasses import dataclass

from dataclasses_json import dataclass_json

from base.prompt_format.common import (
    ResponseMessage,
    get_response_text_parts,
)
from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    TokenizedChatPromptOutput,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer


@dataclass(frozen=True)
class SentryPromptInput:
    """The class represents the input for the Sentry post-processing model."""

    chat_response: ResponseMessage
    """The response message from the chat model."""


@dataclass_json
@dataclass(frozen=True)
class SentryTokenApportionment:
    """Stores the apportionment of the tokens for the Sentry post-processing model."""

    max_prompt_len: int
    """The number of tokens of the total prompt length, which will be used to compute the budget for retrieved chunks."""


_DEFAULT_SENTRY_TOKEN_APPORTMENT = SentryTokenApportionment(
    max_prompt_len=8192,
)


class SentryPromptFormatter:
    """The class formats tokenized prompts for the Sentry post-processing model.

    For prompt structure of the Qwen models, see:
    https://huggingface.co/Qwen/Qwen2.5-Coder-32B-Instruct

    Here is how we tokenize the prompt:

    <|fim_prefix|>
    [chat response]
    <|fim_middle|>
    """

    def __init__(
        self,
        tokenizer: Qwen25CoderTokenizer,
        token_apportionment: SentryTokenApportionment | None = None,
    ):
        self.tokenizer = tokenizer
        self.token_counter = TokenizerBasedTokenCounter(tokenizer)
        self.token_apportionment = (
            token_apportionment or _DEFAULT_SENTRY_TOKEN_APPORTMENT
        )

    def format_prompt(
        self, prompt_input: SentryPromptInput
    ) -> TokenizedChatPromptOutput:
        """Format prompt for Sentry post-processing model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """

        response_text = get_response_text_parts(prompt_input.chat_response)
        response_tokens = self.tokenizer.tokenize_safe(response_text)
        tokens = (
            [
                self.tokenizer.special_tokens.fim_prefix,
                self.tokenizer.special_tokens.newline,
            ]
            + response_tokens
            + [
                self.tokenizer.special_tokens.newline,
                self.tokenizer.special_tokens.fim_middle,
                self.tokenizer.special_tokens.newline,
            ]
        )
        return TokenizedChatPromptOutput(
            tokens=tokens,
            retrieved_chunks_in_prompt=[],
        )
