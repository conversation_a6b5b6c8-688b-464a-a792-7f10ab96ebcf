"""Unit tests for the reusable iterable."""

import pytest

from base.python.au_itertools.reusable_iterable import ReusableIterable

COMMON_INPUTS = [
    [],
    [1],
    [1, 2, 3],
    [1, 2, 3, 4, 5],
    [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
]


@pytest.mark.parametrize(
    "elements",
    COMMON_INPUTS,
)
def test_reusable_iterable(elements: list[int]):
    """Test the reusable iterable."""
    reusable = ReusableIterable(elements)
    assert list(reusable.get_iterable()) == elements


@pytest.mark.parametrize(
    "elements",
    COMMON_INPUTS,
)
def test_reusable_iterable_two_times(elements: list[int]):
    """Test the reusable iterable."""
    reusable = ReusableIterable(elements)
    assert list(reusable.get_iterable()) == elements
    assert list(reusable.get_iterable()) == elements


@pytest.mark.parametrize(
    "elements",
    COMMON_INPUTS,
)
def test_reusable_iterable_two_times_late_eval(elements: list[int]):
    """Test the reusable iterable."""
    reusable = ReusableIterable(elements)
    it1 = reusable.get_iterable()
    it2 = reusable.get_iterable()
    assert list(it1) == elements
    assert list(it2) == elements


@pytest.mark.parametrize(
    "elements",
    COMMON_INPUTS,
)
def test_reusable_iterable_second_time_empty(elements: list[int]):
    """Test the reusable iterable."""
    reusable = ReusableIterable(elements)
    it1 = reusable.get_iterable()
    assert list(it1) == elements
    assert list(it1) == []


def test__iter__():
    """Test the reusable iterable."""
    reusable = ReusableIterable(range(10))
    assert list(reusable) == list(range(10))
    assert list(reusable) == list(range(10))  # Second time should return the same.


def test_iterator():
    """Test the reusable iterable."""
    reusable = ReusableIterable(range(10))
    it1 = iter(reusable)
    it2 = iter(reusable)
    assert list(it1) == list(range(10))
    assert list(it2) == list(range(10))
    assert list(it2) == []
