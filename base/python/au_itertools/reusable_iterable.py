"""ReusableIterable."""

import threading
from typing import Generic, Iterable, TypeVar

T = TypeVar("T")


class ReusableIterable(Generic[T]):
    """An iterable that can be iterated over multiple times.

    This class addresses the problem that iterables can be used only once:
    ```
    >>> it = iter(range(10))
    >>> list(it)
    [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    >>> list(it)
    []
    ```

    Closely related to itertools.tee, but supports an unbounded
    number of iterators while storing the data only once.

    This class is thread-safe.
    """

    def __init__(self, iterable: Iterable[T]):
        self._iterator = iter(iterable)
        self._buffer: list[T] = []
        self._lock = threading.Lock()

    def get_iterable(self) -> Iterable[T]:
        """Returns the iterable."""
        i = 0
        while True:
            # We could make the lock more fine-grained and only lock the else
            # branch, but this would complicate the code.
            with self._lock:
                if i < len(self._buffer):
                    item = self._buffer[i]
                else:
                    # This thread has reached the end of the buffer, so we
                    # attempt to fetch a new item.

                    try:
                        # Note that next returns StopIteration, even if called
                        # multiple times after the iterator stopped.
                        item = next(self._iterator)
                        self._buffer.append(item)
                    except StopIteration:
                        break

            i += 1
            # Yield happens outside of the lock, as otherwise one thread might
            # get blocked, while another thread is waiting for the yield.
            yield item

    def __iter__(self) -> Iterable[T]:
        return self.get_iterable()
