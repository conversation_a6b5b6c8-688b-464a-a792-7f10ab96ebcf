load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "health_check",
    srcs = [
        "health_check.py",
    ],
    visibility = BASE_VISIBILITY + ["//base/python:__subpackages__"],
    deps = [
        ":client_options",
        requirement("grpcio"),
        requirement("protobuf"),
        requirement("grpcio-health-checking"),
    ],
)

py_library(
    name = "client_options",
    srcs = [
        "client_options.py",
    ],
    visibility = BASE_VISIBILITY,
)
