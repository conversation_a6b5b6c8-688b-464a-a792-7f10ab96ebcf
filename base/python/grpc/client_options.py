"""Module for shared grpc client options."""

from dataclasses import dataclass
from typing import Any, Sequence

OptionsList = Sequence[tuple[str, Any]]


@dataclass
class GrpcClientOptions:
    """Options for the grpc client."""

    load_balancing: bool = False


def create(
    options: GrpcClientOptions | OptionsList | None = None,
    extra_options: OptionsList = (),
) -> list[tuple[str, Any]]:
    """Return the grpc client options."""
    grpc_options: dict[str, Any] = {}

    if isinstance(options, GrpcClientOptions):
        if options.load_balancing:
            grpc_options["grpc.lb_policy_name"] = "round_robin"

    # This also seems to set TCP_USER_TIMEOUT to 20000. It does
    # nothing to configure TCP keepalives. Go sets TCP_USER_TIMEOUT
    # to 20000 by default.
    grpc_options["grpc.keepalive_timeout_ms"] = 20000
    # Anything less than 5 minutes and many gRPC servers will tell you
    # to GOAWAY and ENHANCE_YOUR_CALM.
    grpc_options["grpc.keepalive_time_ms"] = 300000

    if isinstance(options, list):
        for k, v in options:
            grpc_options[k] = v

    for k, v in extra_options:
        grpc_options[k] = v

    return list(grpc_options.items())


def get_grpc_client_options(
    options: GrpcClientOptions | None = None,
) -> list[tuple[str, Any]]:
    """Return the grpc client options."""
    return create(options)
