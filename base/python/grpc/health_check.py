"""Module to simplify calling the grpc health check service."""

from __future__ import annotations

import logging
from typing import Optional

import grpc
from grpc_health.v1 import health_pb2, health_pb2_grpc

import base.python.grpc.client_options as client_options


class HealthChecker:
    """Class to call the rpc health check service."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        self.endpoint = endpoint
        self.options = options
        self.stub = setup_stub(endpoint, credentials, options=options)

    def check(self, service_name: str) -> health_pb2.HealthCheckResponse.ServingStatus:  # type: ignore
        """Returns the serving status for a given service."""
        request = health_pb2.HealthCheckRequest(service=service_name)  # type: ignore
        resp = self.stub.Check(request)
        return resp.status

    def is_serving(self, service_name: str) -> bool:
        """Returns the serving status for a given service."""
        request = health_pb2.HealthCheckRequest(service=service_name)  # type: ignore
        resp = self.stub.Check(request)
        return resp.status == health_pb2.HealthCheckResponse.SERVING  # type: ignore


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> health_pb2_grpc.HealthStub:
    """Setup the client stub for the health check."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint,
            options=client_options.create(options),
        )
    else:
        channel = grpc.secure_channel(
            endpoint,
            credentials,
            options=client_options.create(options),
        )
    stub = health_pb2_grpc.HealthStub(channel)
    return stub
