load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "traced_threadpool",
    srcs = [
        "traced_threadpool.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/logging:struct_logging",
        requirement("opentelemetry-api"),
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "traced_threadpool_test",
    srcs = ["traced_threadpool_test.py"],
    deps = [
        ":traced_threadpool",
    ],
)
