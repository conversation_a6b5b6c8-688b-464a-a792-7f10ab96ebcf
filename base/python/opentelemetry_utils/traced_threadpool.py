"""Threadpool that will pass traced context into sub tasks."""

from concurrent.futures.thread import Thr<PERSON><PERSON>oolExecutor
from typing import Callable

import structlog
from opentelemetry import context as opentelemetry_context


def with_context(fn: Callable):
    """Wrap a function with opentelemetry context (for <PERSON><PERSON><PERSON> tracing) and structlog context."""

    otel_context = opentelemetry_context.get_current()
    structlog_context = structlog.contextvars.get_contextvars()

    def wrapper(*args, **kwargs):
        # This wrapper function is called in each thread of the thread pool.
        # It will attach the context from the parent thread.
        if otel_context:
            opentelemetry_context.attach(otel_context)
        if structlog_context:
            structlog.contextvars.bind_contextvars(**structlog_context)
        return fn(*args, **kwargs)

    return wrapper


class TracedThreadPoolExecutor(ThreadPoolExecutor):
    """Implementation of `ThreadPoolExecutor` that will pass context into sub tasks."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def submit(self, fn, *args, **kwargs):  # pylint: disable=arguments-differ
        """Submit a new task to the thread pool."""
        return super().submit(
            with_context(fn),
            *args,
            **kwargs,
        )
