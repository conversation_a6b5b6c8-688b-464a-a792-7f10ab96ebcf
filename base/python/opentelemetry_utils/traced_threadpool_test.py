"""Tests for the traced threadpool module."""

import pytest
import structlog
from opentelemetry import context as otel_context

from base.python.opentelemetry_utils.traced_threadpool import TracedThreadPoolExecutor


def test_tracing_threadpool_smoketest():
    """Smoketest."""
    with TracedThreadPoolExecutor() as pool:
        assert pool.submit(lambda: 3).result() == 3


def test_tracing_threadpool_with_context():
    """Check if context is passed into the thread."""
    ctx = otel_context.set_value("foo", "bar")
    tok = otel_context.attach(ctx)

    def check_context():
        assert otel_context.get_value("foo") == "bar"
        return "ok"

    with TracedThreadPoolExecutor() as pool:
        result = pool.submit(check_context).result()
        assert result == "ok"
    otel_context.detach(tok)  # cleanup


def test_raises_without_context():
    """Check if context is passed into the thread."""
    # if this fails, the previous test didn't clean up properly
    assert otel_context.get_value("foo") is None

    def check_context():
        assert otel_context.get_value("foo") == "bar"

    with TracedThreadPoolExecutor() as pool:
        with pytest.raises(AssertionError):
            pool.submit(check_context).result()


def test_empty_structlog_context():
    """Check if context is passed into the thread."""
    with TracedThreadPoolExecutor() as pool:
        assert (
            pool.submit(lambda: structlog.contextvars.get_contextvars()).result() == {}
        )


def test_tracing_threadpool_with_structlog():
    """Check if context is passed into the thread."""
    structlog.contextvars.bind_contextvars(foo="bar")
    ctx = structlog.contextvars.get_contextvars()
    with TracedThreadPoolExecutor() as pool:
        assert (
            pool.submit(lambda: structlog.contextvars.get_contextvars()).result() == ctx
        )
