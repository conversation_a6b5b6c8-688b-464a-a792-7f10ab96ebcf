"""Module containing utility functions for numpy."""

import io

import numpy


def numpy_to_bytes(a: numpy.ndarray) -> bytes:
    """Save a numpy array into bytes using the numpy on-disk format."""
    bin_stream = io.BytesIO()
    numpy.save(bin_stream, a)
    return bin_stream.getvalue()


def numpy_from_bytes(content: bytes) -> numpy.ndarray:
    """Creates a numpy array from a byte array in the numpy on-disk format.

    The byte array can either be an object created by numpy_to_bytes or
    the content of `numpy.save` loaded into memory.
    """
    bin_stream = io.BytesIO(content)
    return numpy.load(bin_stream)
