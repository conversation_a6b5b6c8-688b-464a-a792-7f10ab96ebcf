"""Tests for the numpy_utils module."""

import io
import os

import numpy

from base.python.numpy_utils.numpy_utils import numpy_from_bytes, numpy_to_bytes


def test_save_numpy_in_memory():
    """Tests saving a tensor to the numpy format."""
    # generate random generator with seed provided by <PERSON>zel
    rng = numpy.random.default_rng(int(os.environ.get("TEST_RANDOM_SEED", "0")))
    test_data = rng.random([1, 1024])
    bin_data = numpy_to_bytes(test_data)

    # ensure that numpy understands the format
    actual = numpy.load(io.BytesIO(bin_data))
    assert numpy.all(actual == test_data)

    # tests the loading function, we should get back to the same content
    actual = numpy_from_bytes(bin_data)
    assert numpy.all(actual == test_data)
