"""Cloud detection."""

from typing import Optional

import requests


def get_gcp_zone() -> str:
    """Returns the GCP zone."""
    try:
        response = requests.get(
            "http://metadata.google.internal/computeMetadata/v1/instance/zone",
            timeout=10,
            headers={"Metadata-Flavor": "Google"},
        )
        if response.ok:
            return response.text
    except requests.RequestException:
        pass
    return ""


def is_gcp() -> bool:
    """Returns true iff the machine is in a GCP VM in a services project."""
    zone = get_gcp_zone()
    return (
        "projects/1035750215372/zones/us-central1" in zone
        or "projects/835723878709/zones/us-central1" in zone
    )


def is_gcp_research() -> bool:
    """Returns true iff the machine is in a GCP VM in a research project."""
    zone = get_gcp_zone()
    return zone in (
        "projects/670297456363/zones/us-central1-a",  # research
    )


def get_default_cloud() -> Optional[str]:
    """Returns the cloud provider."""
    if is_gcp() or is_gcp_research():
        return "GCP_US_CENTRAL1_DEV"
    return None


# mapping from cloud to Kubernetes context name
#
# We assume that the context is configured in the ~/.kube/config file.
#
# See https://kubernetes.io/docs/tasks/access-application-cluster/configure-access-multiple-clusters/
# for access to multiple clusters/contexts.
_CLOUD_TO_CONTEXT = {
    "GCP_US_CENTRAL1_DEV": "gke_system-services-dev_us-central1_us-central1-dev",
    "GCP_US_CENTRAL1_PROD": "gke_system-services-prod_us-central1_us-central1-prod",
    "GCP_US_CENTRAL1_GSC_PROD": "gke_system-services-prod-gsc_us-central1_prod-gsc",
    "GCP_EU_WEST4_PROD": "gke_system-services-prod_europe-west4_eu-west4-prod",
    "GCP_AGENT_US_CENTRAL1_PROD": "gke_agent-sandbox-prod_us-central1_gcp-prod-agent0",
    "GCP_AGENT_EU_WEST4_PROD": "gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0",
}


def get_cloud_list(gcp_only: bool = True) -> list:
    """Returns the list of clouds.

    Args:
        gcp_only: If true, only return GCP clouds.

        Returns:
            A list of cloud names.
    """
    return list(c for c in _CLOUD_TO_CONTEXT if not gcp_only or c.startswith("GCP"))


def get_context(cloud: str) -> Optional[str]:
    """Returns the Kubernetes context for a given cloud.

    Returns:
        A Kubernetes context name or None
    """
    return _CLOUD_TO_CONTEXT.get(cloud.upper())
