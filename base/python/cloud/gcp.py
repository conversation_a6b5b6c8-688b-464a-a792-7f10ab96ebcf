"""Helper functions for pods and tools running on GCP."""

import requests


def get_active_gcp_service_account() -> str | None:
    """Returns the service account email for the active GCP VM or GKE pod."""
    try:
        resp = requests.get(
            "http://169.254.169.254/computeMetadata/v1/instance/service-accounts/default/email",
            headers={"Metadata-Flavor": "Google"},
            timeout=10,
        )
        if resp.ok:
            return resp.text
    except requests.ConnectTimeout:
        # if we run outside a GCP VM, we'll get a timeout
        pass
    return None
