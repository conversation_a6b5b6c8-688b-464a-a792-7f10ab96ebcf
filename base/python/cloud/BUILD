load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "cloud",
    srcs = [
        "cloud.py",
    ],
    visibility = BASE_VISIBILITY + ["//base/python/k8s_test_helper:__subpackages__"],
    deps = [
        requirement("requests"),
    ],
)

pytest_test(
    name = "cloud_test",
    srcs = ["cloud_test.py"],
    deps = [
        ":cloud",
    ],
)

py_library(
    name = "gcp",
    srcs = [
        "gcp.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        requirement("requests"),
    ],
)

py_library(
    name = "env_info",
    srcs = [
        "env_info.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        ":gcp",
    ],
)
