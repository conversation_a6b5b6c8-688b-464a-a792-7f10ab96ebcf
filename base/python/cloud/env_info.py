"""Print the environment information."""

import logging
import os
import pathlib
import subprocess

from base.python.cloud import gcp


def print_env_info():
    """Print the environment information.

    Prints information about the container environment to the logs.
    """

    # Notes:
    # - this call should not fail under any circumstances.
    # - do not print env variables that may contain secrets.

    logging.info("Environment Info")
    logging.info("User %s:%s", os.getuid(), os.getegid())
    logging.info("Cwd %s", os.getcwd())
    logging.info("Home %s", pathlib.Path().home())
    try:
        r = subprocess.run(["df"], check=False, capture_output=True, encoding="utf-8")
        logging.info("df %s", r.stdout)
    except FileNotFoundError:
        logging.info("df not found")
    try:
        r = subprocess.run(
            ["nvidia-smi"], check=False, capture_output=True, encoding="utf-8"
        )
        logging.info("nvidia-smi %s", r.stdout)
    except FileNotFoundError:
        logging.info("nvidia-smi not found")
    logging.info("Service Account %s", gcp.get_active_gcp_service_account())
