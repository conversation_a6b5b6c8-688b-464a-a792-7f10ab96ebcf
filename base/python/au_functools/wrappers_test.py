from dataclasses import dataclass

import pytest

from base.python.au_functools.wrappers import method_wrap


@dataclass
class Client:
    """Simulates a client that fails every other call."""

    calls: int = 0

    def repeat_str_n_times(self, s: str, n: int) -> str:
        self.calls += 1
        success = self.calls % 2 == 0
        return s * n if success else ""


@dataclass
class RetryingClient:
    """Retries calls to the first client."""

    client: Client
    retries: int

    @method_wrap(Client.repeat_str_n_times)
    def repeat_str_n_times(self, *args, **kwargs) -> str | None:
        for retry in range(self.retries):
            if result := self.client.repeat_str_n_times(*args, **kwargs):
                return result
        return None


def test_method_wrap_extra_args():
    retrying_client = RetryingClient(Client(), retries=2)
    with pytest.raises(TypeError):
        # This is the motivating example. Without method_wrap, the following line
        # would pass type checking, but fail at runtime. With method_wrap,
        # `reportCallIssue` gets reported by pyright.
        retrying_client.repeat_str_n_times("a", n=4, bar="b")  # pyright: ignore[reportCallIssue]


def test_method_wrap_mixed_args():
    retrying_client = RetryingClient(Client(), retries=2)
    assert retrying_client.repeat_str_n_times("a", n=4) == "aaaa"


def test_method_wrap_none_return():
    retrying_client = RetryingClient(Client(), retries=1)
    assert retrying_client.repeat_str_n_times("a", n=4) is None


def test_method_wrap_kwargs():
    retrying_client = RetryingClient(Client(), retries=2)
    assert retrying_client.repeat_str_n_times(s="a", n=4) == "aaaa"
