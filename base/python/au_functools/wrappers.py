"""Wrappers."""

from typing import Any, TypeVar, Concatenate, ParamSpec, Callable


P = ParamSpec("P")
T = TypeVar("T")
S = TypeVar("S")


def method_wrap(
    callee: Callable[Concatenate[Any, P], T],
) -> Callable[[Callable[Concatenate[Any, P], S]], Callable[Concatenate[Any, P], S]]:
    """A decorator for typehinting a class method accepting (*args, **kwargs) that
    calls another method (possibly in a different class) with the exact same args.

    For example, consider a method `Client.foo(s: str, n: int)` which is called by
    `RetryingClient.foo(*args, **kwargs)`. The call `retrying_client.foo(bar="a")`
    without this decorator would pass type checking despite `Client.foo` not accepting
    `bar`. With the decorator, we could catch this before runtime:
    ```
    class Client:
        def foo(self, s: str, n: int) -> str:
            ...

    class RetryingClient:
        client: Client
        retries: int

        @method_wrap(Client.repeat_str_n_times)
        def foo(self, *args, **kwargs) -> str | None
            for retry in range(self.retries):
                if result := self.client.foo(*args, **kwargs):
                    return result
            return None
    ```
    """

    def wrapper(
        caller: Callable[Concatenate[Any, P], S],
    ) -> Callable[Concatenate[Any, P], S]:
        return caller

    return wrapper
