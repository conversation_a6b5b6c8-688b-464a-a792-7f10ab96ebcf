# broadcaster

100% Pure Python, dependencies only on standard library.

A thread wakeup primitive for multiple waiters and multiple notifiers.

Each waiter subscribes to the broadcaster and uses the
subscriber to wait.

Subscribers that call wait() block until they are notified or timeout.
In addition, if the broadcaster was notified while a subscriber was
not blocked in wait(),  then the next call to wait() by that subscriber
will not block.

It may help to think of the broadcaster as a `threading.Event` that works correctly with multiple waiters.
