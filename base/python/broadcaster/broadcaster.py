"""A wakeup primitive for multiple waiters that can be notified multiple times."""

import threading


class Subscriber:
    """One of the waiters."""

    broadcaster: "Broadcaster"
    counter: int = 0

    def __init__(self, broadcaster: "Broadcaster", counter):
        self.broadcaster = broadcaster
        self.counter = counter

    def wait(self, timeout=None) -> bool:
        """Wait on the broadcaster, maybe.

        Args:
            timeout - roughly how long to wait

        Returns:
            False on timeout, True otherwise

        Waits on the broadcaster:
            - unless the broadcaster has been notified since we last
              waited, or if we have never waited, since this object
              was returned by subscribe().
            - until the broadcaster is notified or timeout occurs
        """
        with self.broadcaster.condition:
            while True:
                if self.counter != self.broadcaster.counter:
                    self.counter = self.broadcaster.counter
                    return True

                if not self.broadcaster.condition.wait(timeout=timeout):
                    return False


class Broadcaster:
    """A wakeup primitive for multiple waiters and multiple notifiers.

    Each waiter subscribes to the broadcaster and uses the
    subscriber to wait.

    Subscribers that call wait() block until they are notified or timeout.
    In addition, if the broadcaster was notified while a subscriber was
    not blocked in wait(),  then the next call to wait() by that subscriber
    will not block.
    """

    condition: threading.Condition
    counter: int = 0

    def __init__(self):
        self.condition = threading.Condition()

    def subscribe(self) -> Subscriber:
        """Create a new subscriber."""
        local_counter = 0
        with self.condition:
            local_counter = self.counter

        return Subscriber(self, local_counter)

    def notify_all(self):
        """Wakeup all subscribers."""
        with self.condition:
            self.counter += 1
            self.condition.notify_all()
