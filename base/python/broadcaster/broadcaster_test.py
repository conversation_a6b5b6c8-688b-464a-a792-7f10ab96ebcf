"""Unit tests for broadcaster."""

import broadcaster


def test_broadcaster_wakes():
    """After notify_all, a subscriber doesn't sleep."""
    caster = broadcaster.Broadcaster()
    sub1 = caster.subscribe()

    assert not sub1.wait(0)
    assert not sub1.wait(0)

    caster.notify_all()

    assert sub1.wait(0)
    assert not sub1.wait(0)

    caster.notify_all()

    assert sub1.wait(0)
    assert not sub1.wait(0)


def test_independent_subscribers():
    """Test that subscriber state are independent."""
    caster = broadcaster.Broadcaster()
    sub1 = caster.subscribe()

    assert not sub1.wait(0)

    caster.notify_all()

    sub2 = caster.subscribe()

    assert sub1.wait(0)
    assert not sub2.wait(0)

    caster.notify_all()

    assert sub2.wait(0)
    assert not sub2.wait(0)

    assert sub1.wait(0)
    assert not sub1.wait(0)
