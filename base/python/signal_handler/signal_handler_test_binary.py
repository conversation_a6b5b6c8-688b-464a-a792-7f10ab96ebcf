"""Test binary to test the signal handler code."""

import logging
import os
import sys
from pathlib import Path

from base.python.signal_handler.signal_handler import GracefulSignalHandler


def main():
    """Main entry function."""
    handler = GracefulSignalHandler()
    logging.basicConfig(level=logging.INFO)

    logging.info("PID: %s", os.getpid())
    Path(sys.argv[1]).write_text(f"{os.getpid()}", encoding="utf-8")

    handler.get_shutdown_event().wait()
    logging.info("End")


if __name__ == "__main__":
    main()
