"""A module containing standard signal handlers for python code."""

import os
import signal
import sys
import threading
import traceback


class _StandardSignalHandlers:
    """Class to install standard signal handlers."""

    def __init__(self, sigterm_graceful: bool):
        self.orig_sigint_handler = signal.signal(
            signal.SIGINT, self._on_sigint_exit_quickly
        )
        if sigterm_graceful:
            self.orig_sigterm_handler = signal.signal(
                signal.SIGTERM, self._on_sigterm_exit_gracefully
            )
        else:
            self.orig_sigterm_handler = signal.signal(
                signal.SIGTERM, self._on_sigterm_exit_quickly
            )

        self._shutdown_event = threading.Event()

    def get_shutdown_event(self) -> threading.Event:
        """Returns the sigterm event."""
        return self._shutdown_event

    def _on_sigint_exit_quickly(self, signum, frame):
        print(
            "captured signal SIGINT",
            flush=True,
            file=sys.stderr,
        )
        traceback.print_stack(frame)

        os._exit(1)

    def _on_sigterm_exit_quickly(self, signum, frame):
        print(
            "captured signal SIGTERM",
            flush=True,
            file=sys.stderr,
        )

        os._exit(1)

    def _on_sigterm_exit_gracefully(self, signum, frame):
        print(
            "captured signal SIGTERM",
            flush=True,
            file=sys.stdout,
        )
        self._shutdown_event.set()


class StandardSignalHandler:
    """A standard signal handler"""

    def __init__(self):
        self._handler = _StandardSignalHandlers(sigterm_graceful=False)


class GracefulSignalHandler(_StandardSignalHandlers):
    """A signal handler that exits gracefully."""

    def __init__(self):
        super().__init__(sigterm_graceful=True)
