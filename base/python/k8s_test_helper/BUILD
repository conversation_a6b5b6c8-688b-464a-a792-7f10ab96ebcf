load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "k8s_test_helper",
    srcs = ["k8s_test_helper.py"],
    data = [
        "//deploy/tenants/namespace_configs:namespace-configs",
        "@k8s_binary//file:kubectl",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/cloud/k8s:kubectl",
        "//base/python/cloud",
        "//base/python/grpc:health_check",
        requirement("google-cloud-bigtable"),
        requirement("grpcio"),
        requirement("kubernetes"),
        requirement("pyyaml"),
    ],
)

py_library(
    name = "k8s_resource",
    srcs = ["k8s_resource.py"],
    visibility = BASE_VISIBILITY,
    deps = [
        ":k8s_test_helper",
        requirement("pyyaml"),
    ],
)
