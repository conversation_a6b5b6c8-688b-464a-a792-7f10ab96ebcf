"""Module to help with AWS K8S resources."""

import yaml
from yaml import SafeLoader

from base.python.k8s_test_helper.k8s_test_helper import DeployInfo, Resolve


def _make_keys_capital(obj):
    """Make all keys start with an upper case character.

    The reason is that the way to define DynamoDb tables in the kubernetes deployment is with
    lower case, but the boto3 create_table call requires the same names, but with upper case.
    """
    if isinstance(obj, dict):
        result = {}
        for key, val in obj.items():
            result[f"{key[0].upper()}{key[1:]}"] = _make_keys_capital(val)
        return result
    if isinstance(obj, list):
        result = []
        for key in obj:
            result.append(_make_keys_capital(key))
        return result
    return obj


def table_infos(resolve: Resolve):
    """Generates dynamodb table information as extracted from the deployment.

    Generates:
    - dicts with a dict per table. The output can be passed to boto3's create_table call to
      create the table.
    """
    objects = list(yaml.load_all(resolve.resolve, Loader=SafeLoader))
    for obj in objects:
        if obj and obj.get("kind") == "Table":
            del obj["spec"]["timeToLive"]  # not supported by create_table
            table = _make_keys_capital(obj["spec"])
            yield table


def bucket_infos(deploy_info: DeployInfo):
    """Generates to return Bucket information as extracted from the deployment."""
    for resolve in deploy_info.resolves:
        objects = list(yaml.load_all(resolve.resolve, Loader=SafeLoader))
        for obj in objects:
            if obj and obj.get("kind") == "Bucket":
                bucket = _make_keys_capital(obj["spec"])
                yield bucket
