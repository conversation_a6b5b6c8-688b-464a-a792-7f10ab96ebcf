load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

# TODO(arun): Remove once we move prompt formatters into this directory.
PROMPT_FORMATTERS = [
    "//base/augment_client:__subpackages__",
    "//base/diff_utils:__subpackages__",
    "//base/memory_engine:__subpackages__",
    "//base/prompt_format_chat:__subpackages__",
    "//base/prompt_format_completion:__subpackages__",
    "//base/prompt_format_edit:__subpackages__",
    "//base/prompt_format_next_edit:__subpackages__",
    "//base/prompt_format_retrieve:__subpackages__",
    "//base/prompt_format_rerank:__subpackages__",
    "//base/prompt_format_router:__subpackages__",
    "//base/prompt_format_postprocess:__subpackages__",
    "//base/third_party_clients:__subpackages__",
    "//services/chat_host/server:__subpackages__",
    "//services/edit_host/server:__pkg__",
    "//services/thirdparty_proxy:__subpackages__",
    "//models/retrieval/chunking:__pkg__",
]

py_library(
    name = "chunk_origin",
    srcs = [
        "chunk_origin.py",
    ],
    visibility = ["//visibility:public"],
)

py_library(
    name = "common",
    srcs = [
        "common.py",
    ],
    visibility = PROMPT_FORMATTERS,
    deps = [
        "//base/python/au_itertools:reusable_iterable",
        "//base/ranges",
    ],
)

pytest_test(
    name = "common_test",
    srcs = ["common_test.py"],
    deps = [
        ":common",
    ],
)

py_library(
    name = "recency_info",
    srcs = [
        "recency_info.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        requirement("dataclasses_json"),
        "//base/ranges",
    ],
)

py_library(
    name = "util",
    srcs = [
        "util.py",
    ],
    visibility = PROMPT_FORMATTERS,
    deps = [
        ":common",
    ],
)

pytest_test(
    name = "util_test",
    srcs = ["util_test.py"],
    deps = [
        ":util",
    ],
)
