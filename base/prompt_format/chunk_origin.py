from enum import Enum


class ChunkOrigin(Enum):
    """The origin of a chunk."""

    DENSE_RETRIEVER = "dense_retriever"
    RECENCY_RETRIEVER = "recency_retriever"
    RECENCY_RETRIEVER_GIT_DIFF = "recency_retriever_git_diff"
    RECENCY_RETRIEVER_VIEWED_CONTENT = "recency_retriever_viewed_content"
    SIGNATURE_RETRIEVER = "signature_retriever"
    USER_GUIDED_RETRIEVER = "user_guided_retriever"
    DENSE_SIGNATURE = "dense_signature"
    LOCATION_RETRIEVER = "location_retriever"
    DIFF_RETRIEVER = "diff_retriever"

    @classmethod
    def list(cls):
        return list(map(lambda c: c.value, cls))


ChunkOriginValues = ChunkOrigin.list()
