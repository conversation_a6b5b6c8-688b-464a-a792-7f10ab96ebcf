"""Common dataclasses shared across prompt formatters."""

import dataclasses
import json
import logging
from collections.abc import Iterable
from dataclasses import dataclass
from enum import Enum, IntEnum
from functools import cached_property
from typing import Any, List, Optional, Union

import dataclasses_json

from base.python.au_itertools import reusable_iterable
from base.ranges import CharRange

logger = logging.getLogger(__name__)

TokenList = list[int]


class RuleType(IntEnum):
    ALWAYS_ATTACHED = 0
    MANUAL = 1
    AGENT_REQUESTED = 2


@dataclass(frozen=True)
class Rule:
    type: RuleType

    path: str

    content: str

    description: Optional[str] = None


class PersonaType(IntEnum):
    """The persona type that the AI assistant should adopt for a chat turn."""

    DEFAULT = 0
    """The default persona, which is an expert software engineer."""

    PROTOTYPER = 1
    """The prototyper persona, which is an expert software engineer that is
    focused on building new web apps."""

    BRAINSTORM = 2
    """The brainstorm persona, which is an expert software engineer that is
    focused on planning and brainstorming solutions."""

    REVIEWER = 3
    """The reviewer persona, which is an expert software engineer that is
    focused on reviewing code changes and identifying potential issues."""


class LineEnding(str, Enum):
    """Line ending types used in text files."""

    CRLF = "CRLF"  # Windows-style
    LF = "LF"  # Unix-style


def detect_line_ending(content: str) -> LineEnding:
    """Detect whether the content uses CRLF or LF line endings."""
    if "\r\n" in content:
        return LineEnding.CRLF
    return LineEnding.LF


def normalize_line_endings(content: str) -> str:
    """Convert Windows line endings to Unix line endings."""
    return content.replace("\r\n", "\n")


@dataclass(frozen=True)
class DocumentationMetadata(dataclasses_json.DataClassJsonMixin):
    """The documentation metadata for the prompt formatter.

    Added to the PromptInput metadata if the chunk is a documentation chunk
    """

    source_id: str
    """The external source ID of the docset the chunk comes from."""

    name: str
    """The name of the docset the chunk comes from."""

    page_id: str
    """The page id identifying which chunks should be grouped together."""

    headers: list[str]
    """
    The list of all section titles each chunk belongs in. Displayed in the prompt before the text of the chunk.
    If chunks are merged, the headers will also be merged to not repeat the same ones.
    """


# This used to be a protocol and not a dataclass, but: https://colab.research.google.com/drive/1qqpEEGLR87UdCg_fau2Bj3m9YlozQd69?usp=sharing
# In short, class fields are automatically inherited and the protocol does not do what
# we intuitively expect.
@dataclass(frozen=True)
class PromptChunk(dataclasses_json.DataClassJsonMixin):
    """Information about a retrieval chunk that the prompt formatter requires."""

    text: str
    """The text of the chunk."""

    path: str
    """The path of the file containing this chunk."""

    unique_id: Optional[str] = None
    """An optional unique chunk ID that can be used for deduplication."""

    origin: str = ""
    """A origin (e.g. retrieval system) for the chunk."""

    char_start: int = -1
    """Offset in UTF-8 characters where the region represented by this chunk starts."""

    char_end: int = -1
    """Offset in UTF-8 characters where the region represented by this chunk ends."""

    blob_name: str = ""
    """The blob name of the file containing this chunk."""

    header: str = ""
    """The header of the chunk."""

    documentation_metadata: DocumentationMetadata | None = None
    """Optional additional metadata for the chunk if the chunk is a documentation chunk."""

    line_start: int = -1
    """Offset in lines where the region represented by this chunk starts.

    This metadata isn't used in comparisons, etc.
    """

    line_end: int = -1
    """Offset in lines where the region represented by this chunk ends.

    This metadata isn't used in comparisons, etc.
    """

    @property
    def crange(self) -> CharRange:
        """The character range of the chunk."""
        return CharRange(self.char_start, self.char_end)

    def __lt__(self, other):
        if self.documentation_metadata is None:

            def _create_cmp_tuple(c: PromptChunk) -> tuple:
                return (c.blob_name, c.path, c.char_start, c.char_end, c.origin)

            return _create_cmp_tuple(self) < _create_cmp_tuple(other)
        else:
            # TODO: This additional complexity is a result of our PromptChunk design likely needing
            # some refactoring work. We will revisit this after launch and explore how to simplify things

            def _create_cmp_tuple_docs(c: PromptChunk) -> tuple:
                assert c.documentation_metadata is not None
                return (
                    c.documentation_metadata.page_id,
                    c.char_start,
                    c.char_end,
                    c.origin,
                )

            return _create_cmp_tuple_docs(self) < _create_cmp_tuple_docs(other)


class PromptFormatterOutput:
    """The output of the prompt formatter.

    It can used as a list of tokens or as a list of token lists. Returning
    a list of token lists is useful for the case where the prompt is split into
    multiple chunks and want to process the early chunks in the inference host
    while the later chunks are still being prepared by the retrievers and prompt
    formatter.

    Example usage:
    ```python
    def format_prompt(prompt_input: PromptInput) -> PromptOutput:

    # do some work
    early_tokens = ...

    # start some asynchronous work
    ... = executor.submit(...)

    def get_tokens() -> Iterable[TokenList]:
        yield early_tokens

        for chunk in retrieval_chunks:
             yield format(chunk)

        # async cleanup
        ... = executor.submit(...)

    return PromptFormatterOutput(get_tokens())
    ```
    """

    def __init__(self, token_sublists: Iterable[TokenList]):
        # The tokens of the prompt, possibly split into chunks.
        self.token_sublists = reusable_iterable.ReusableIterable(token_sublists)

    def tokens(self) -> TokenList:
        """Returns the tokens of the output."""
        accum = []
        for token_sublist in self.token_sublists.get_iterable():
            accum.extend(token_sublist)
        return accum


class ChatRequestNodeType(IntEnum):
    TEXT = 0
    """User message text."""

    TOOL_RESULT = 1
    """Result of a tool use."""

    IMAGE = 2
    """An image(Default format: PNG)."""

    IDE_STATE = 4
    """IDE state information."""

    EDIT_EVENTS = 5
    """User edits information."""


class ImageFormatType(IntEnum):
    """Supported image formats for chat requests."""

    IMAGE_FORMAT_UNSPECIFIED = 0
    PNG = 1
    JPEG = 2
    GIF = 3
    WEBP = 4


@dataclass
class ChatRequestText(dataclasses_json.DataClassJsonMixin):
    content: str
    """Content of the node."""


@dataclass
class ChatRequestImage(dataclasses_json.DataClassJsonMixin):
    """Class storing image content for image nodes."""

    image_data: str
    """Base64 encoded image data."""

    format: ImageFormatType = ImageFormatType.IMAGE_FORMAT_UNSPECIFIED
    """Format of the image data."""


class ToolResultContentNodeType(IntEnum):
    """Type of content node for tool results."""

    CONTENT_TYPE_UNSPECIFIED = 0
    """Unspecified content type."""

    CONTENT_TEXT = 1
    """Text content."""

    CONTENT_IMAGE = 2
    """Image content."""


@dataclass
class ToolResultContentNode(dataclasses_json.DataClassJsonMixin):
    """Class storing a content node for tool results - can be text or image."""

    type: int
    """Type of the node."""

    text_content: str | None = None
    """Text content of the node."""

    image_content: ChatRequestImage | None = None
    """Image content of the node."""


@dataclass
class ChatRequestToolResult(dataclasses_json.DataClassJsonMixin):
    tool_use_id: str
    """Tool use id of the node."""

    content: str
    """Content of the node (ignored when content_nodes is present)."""

    is_error: bool
    """Is the tool use an error."""

    request_id: str | None = None
    """Remote request which generated the result, if tool implemented by Augment"""

    content_nodes: list[ToolResultContentNode] | None = None
    """List of content nodes (text or images). If present, takes precedence over content field."""


@dataclass
class WorkspaceFolderInfo(dataclasses_json.DataClassJsonMixin):
    repository_root: str
    """The root directory of the current repository. This is the first ancestor of folder
    root that is a git directory or has an `.augmentroot` file."""

    folder_root: str
    """The directory of the "workspace" the user has opened."""


@dataclass
class TerminalInfo(dataclasses_json.DataClassJsonMixin):
    terminal_id: int
    """Unique id of the terminal."""

    current_working_directory: str
    """The current working directory of the terminal."""


@dataclass
class ChatRequestIdeState(dataclasses_json.DataClassJsonMixin):
    workspace_folders: list[WorkspaceFolderInfo]
    """The root directory of the current repository. This is the first ancestor of folder
    root that is a git directory or has an `.augmentroot` file."""

    workspace_folders_unchanged: bool
    """Whether the workspace folders have changed since the last request."""

    current_terminal: TerminalInfo | None = None
    """The current terminal."""


@dataclass
class ChatRequestSingleEdit(dataclasses_json.DataClassJsonMixin):
    """Class storing a single edit event."""

    before_line_start: int
    """The starting line of the edit in the original text."""

    before_text: str
    """The text that was present before the edit."""

    after_line_start: int
    """The starting line where the new text is inserted."""

    after_text: str
    """The new text that replaces the 'before_text'."""


@dataclass
class ChatRequestFileEdit(dataclasses_json.DataClassJsonMixin):
    """Class storing a file edit event."""

    path: str
    """Path of the file that was edited."""

    before_blob_name: str | None = None
    """Name of the blob before the edit."""

    after_blob_name: str | None = None
    """Name of the blob after the edit."""

    edits: list[ChatRequestSingleEdit] = dataclasses.field(default_factory=list)
    """List of edits in the event."""


class EditEventSource(IntEnum):
    """Source of the edit events."""

    UNSPECIFIED = 0
    """Unspecified source."""

    USER_EDIT = 1
    """Edit performed by the user."""

    CHECKPOINT_REVERT = 2
    """Edit performed by reverting to a checkpoint."""


@dataclass
class ChatRequestEditEvents(dataclasses_json.DataClassJsonMixin):
    """Class storing edit events."""

    edit_events: list[ChatRequestFileEdit] = dataclasses.field(default_factory=list)
    """List of edit events."""

    source: EditEventSource = EditEventSource.UNSPECIFIED
    """Source of the edit events."""


@dataclass
class ChatRequestNode:
    """Class storing a single node for a structured chat request."""

    id: int
    """Unique id of the node [currently unused]."""

    type: ChatRequestNodeType
    """Type of the node."""

    text_node: ChatRequestText | None
    """Text node of the node."""

    tool_result_node: ChatRequestToolResult | None
    """Tool use node of the node."""

    image_node: ChatRequestImage | None = None
    """Image node of the node."""

    ide_state_node: ChatRequestIdeState | None = None
    """IDE state node of the node."""

    edit_events_node: ChatRequestEditEvents | None = None
    """Edit events node of the node."""


class ChatResultNodeType(IntEnum):
    RAW_RESPONSE = 0
    """The raw response from the model."""

    SUGGESTED_QUESTIONS = 1
    """Our guess of what user will ask next."""

    MAIN_TEXT_FINISHED = 2
    """Indication that streaming of the main response finished"""

    WORKSPACE_FILE_CHUNKS = 3
    """Workspace file chunks used in prompt"""

    RELEVANT_SOURCES = 4
    """Sources that were useful to generate the response"""

    TOOL_USE = 5
    """Tool use requested by the AI model."""

    FINAL_PARAMETERS = 6
    """Final parameters used for the request.
    Note: this was added for use in regression_test.ipynb and is NOT part of the
    Chat rpc API or public API. We should (probably) avoid additions like this in
    the future. I have reserved the value `6` in the proto definition.
    """

    TOOL_USE_START = 7
    """Tool use requested by the AI model."""

    THINKING = 8
    """Thinking/reasoning content from the model."""


@dataclass
class ChatResultToolUse(dataclasses_json.DataClassJsonMixin):
    """Class storing additional content for tool use nodes."""

    name: str
    """Name of the tool."""

    input: dict[str, Any]
    """Input to the tool."""

    tool_use_id: str
    """Tool use id of the node."""

    is_partial: bool = False
    """Whether this is a partial tool use response."""


class StopReason(IntEnum):
    REASON_UNSPECIFIED = 0
    """The stop reason is unspecified"""

    END_TURN = 1
    """The model has reached a natural stopping point or stop sequence"""

    MAX_TOKENS = 2
    """Generation hit max token limit set by client or server"""

    TOOL_USE_REQUESTED = 3
    """The model has requested one or more tool uses"""

    # Additional reasons generated by other APIs we might consider
    # SAFETY - Content potentially contains safety violations
    # RECITATION - Content potentially violates copyright
    # MALFORMED_FUNCTION_CALL - Today we have the client detect this


@dataclass
class ChatResultNode(dataclasses_json.DataClassJsonMixin):
    """Class storing a single node for a structured chat response."""

    id: int
    """Unique id of the node. [currently unused]"""

    type: ChatResultNodeType
    """Type of the node."""

    content: str
    """Content of the node."""

    tool_use: ChatResultToolUse | None = None
    """Additional content for tool use nodes."""

    final_parameters: dict[str, Any] | None = None
    """Final parameters used for the request."""


@dataclass(frozen=True)
class ToolDefinition(dataclasses_json.DataClassJsonMixin):
    name: str
    description: str
    input_schema_json: str

    @cached_property
    def input_schema(self) -> dict[str, Any]:
        try:
            return json.loads(self.input_schema_json)
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse input schema for tool {self.name}")
            return {}

    @property
    def properties(self) -> dict[str, Any]:
        return self.input_schema.get("properties", {})


RequestMessage = Union[str, List[ChatRequestNode]]
ResponseMessage = Union[str, List[ChatResultNode]]


def get_request_text_parts(
    request_message: RequestMessage,
) -> str:
    """Extracts text parts from a request message

    If there are no text parts, returns empty string
    """
    if isinstance(request_message, str):
        return request_message

    # Assert that request_message is iterable
    # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
    assert iter(request_message) is not None

    request_message_text = ""
    for node in request_message:
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            assert node.tool_result_node is None
            request_message_text += node.text_node.content

    return request_message_text


def get_response_text_parts(
    response_message: ResponseMessage,
) -> str:
    """Extracts text parts from a response message

    If there are no text parts, returns empty string
    """
    if isinstance(response_message, str):
        return response_message

    # Assert that response_message is iterable
    # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
    assert iter(response_message) is not None

    response_message_text = ""
    for node in response_message:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            response_message_text += node.content

    return response_message_text


def is_request_message_tool_result(request_message: RequestMessage) -> bool:
    """Returns True if the request message contains a tool result node."""
    if isinstance(request_message, str):
        return False

    return any(node.type == ChatRequestNodeType.TOOL_RESULT for node in request_message)


def inject_as_first_text_node_in_request_message(
    request_message: RequestMessage, text_content: str | None
) -> RequestMessage:
    """Injects the text content as the first text node in the request message."""
    if not text_content:
        return request_message

    if isinstance(request_message, str):
        return "\n".join([text_content, request_message])

    nodes = list(request_message)
    # Default to 1 if nodes is empty
    new_id = max((node.id for node in nodes), default=1) + 1

    # We'll insert the node before the first TEXT node or the end if there is none.
    insertion_idx = next(
        (i for (i, node) in enumerate(nodes) if node.type == ChatRequestNodeType.TEXT),
        len(nodes),
    )
    has_text_node = insertion_idx < len(nodes)
    join_str = "\n" if has_text_node else ""

    nodes.insert(
        insertion_idx,
        ChatRequestNode(
            id=new_id,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content=text_content + join_str),
            tool_result_node=None,
        ),
    )
    return nodes


def get_request_message_as_text_always(request_message: RequestMessage) -> str:
    """Try to convert a simple request message to a string representation.

    Skip non-text nodes instead of returning None. if there is no text node, return ""
    """
    if isinstance(request_message, str):
        return request_message

    # Extract text content without failing on non-text nodes
    request_message_text = ""
    for node in request_message:
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            request_message_text += node.text_node.content

    return request_message_text


def try_get_request_message_as_text(request_message: RequestMessage) -> str | None:
    """Try to convert a simple request message to a string representation.

    If this is a structured request message (e.g., tool result or something
    we support in the future), try_get_request_message_as_text returns None.
    """
    if isinstance(request_message, str):
        return request_message

    # Assert that request_message is iterable
    # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
    assert iter(request_message) is not None

    request_message_text = ""
    for node in request_message:
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            assert node.tool_result_node is None
            request_message_text += node.text_node.content
        else:
            # This message cannot be fully represented as text.
            return None

    return request_message_text


def get_request_message_as_text(request_message: RequestMessage) -> str:
    """Convert a simple request message to a string representation.

    If this is a structured request message (e.g., tool result or something
    we support in the future), get_request_message_as_text raises a ValueError.
    """
    text = try_get_request_message_as_text(request_message)
    if text is None:
        raise ValueError("request_message expected to be a text node.")
    return text


def try_get_response_message_as_text(response_message: ResponseMessage) -> str:
    """Converts a response message to its text representation.

    Args:
        response_message: Either a string or a list of ChatResultNode objects.

    Returns:
        str: The concatenated text content of all RAW_RESPONSE nodes if response_message
            is a list of nodes, or the original string if response_message is a string.
    """
    if isinstance(response_message, str):
        return response_message

    text = ""
    for node in response_message:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            text += node.content

    return text


def get_response_message_as_text(response_message: ResponseMessage) -> str:
    """Convert a simple response message to a string representation.

    If this is a structured response message (e.g., tool use or something
    we support in the future), get_response_message_as_text raises a ValueError.
    """
    if isinstance(response_message, str):
        return response_message

    if (
        len(response_message) == 1
        and response_message[0].type == ChatResultNodeType.RAW_RESPONSE
    ):
        assert response_message[0].content is not None
        return response_message[0].content

    raise ValueError("response_message expected to be a text node.")


# This class is used in both in prompt_formatter_chat and prompt_formatter_retrieve,
# so we put it here in the common library.
@dataclass(frozen=True)
class Exchange:
    """Represents a single exchange between the user and the model."""

    request_message: RequestMessage
    """The user's message"""

    response_text: ResponseMessage
    """The response message from the model"""

    request_id: Optional[str] = None

    def __repr__(self):
        return (
            f"{self.__class__.__name__}(            "
            f" request_message={self.request_message!r},            "
            f" response_text={self.response_text!r},            "
            f" request_id={self.request_id!r},            )"
        )

    @property
    def response_nodes(self) -> Iterable[ChatResultNode]:
        """Returns the response nodes of the exchange."""
        if isinstance(self.response_text, str):
            return []
        return self.response_text

    @property
    def request_nodes(self) -> Iterable[ChatRequestNode]:
        """Returns the request nodes of the exchange."""
        if isinstance(self.request_message, str):
            return []
        return self.request_message

    def tool_use_ids(self) -> list[str]:
        """Returns the tool use ids of the exchange."""
        return [
            node.tool_use.tool_use_id
            for node in self.response_nodes
            if node.type == ChatResultNodeType.TOOL_USE and node.tool_use is not None
        ]

    def tool_result_ids(self) -> list[str]:
        """Returns the tool result ids of the exchange."""
        return [
            node.tool_result_node.tool_use_id
            for node in self.request_nodes
            if node.type == ChatRequestNodeType.TOOL_RESULT
            and node.tool_result_node is not None
        ]
