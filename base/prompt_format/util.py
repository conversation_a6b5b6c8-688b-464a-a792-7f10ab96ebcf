"""The miscellaneous functions and classes for prompting, etc."""

import collections
import logging
from dataclasses import dataclass
from typing import Any, Iterable, Mapping, Optional, Sequence, TypeVar

from base.prompt_format.common import PromptChunk, TokenList

TokenT = TypeVar("TokenT")


def concatenate_retrieved_chunks(
    retrieved_chunks: Iterable[list[TokenT]],
    separator_tokens: Sequence[TokenT],
    max_total_tokens: int,
    reverse_chunks: bool = True,
    used_indices_output: Optional[set[int]] = None,
) -> list[TokenT]:
    """Concatenate a list of token-sequences.

    Args:
        retrieved_chunks: The retrieved chunks, sorted from high to low relevancy.
        separator_tokens: a sequence of tokens added in between each of the retrieved
            chunks.
        max_total_tokens: the maximum number of tokens for the final concat result,
          -1 means infinity value.
        reverse_chunks: if true, then will reverse the order of the retrieved chunks
            before concatenation. This puts the most relevant chunks at the end of the
            sequence, closer to generation.
        used_indices_output: If not None, then will be populated with the indices of
            the chunks that were used.

    Returns:
        The concatenated result as a list of tokens. The result might not include all
        chunks, but each chunk is used completely or not at all. If the maximal size is
        exceeded, the chunks are truncated from the front. The ordering of chunks is
        maintained.
    """
    assert max_total_tokens >= 0
    if max_total_tokens == 0:
        return []

    index = 0
    concat_tokens = collections.deque[TokenT]()
    for index, cur_tokens in enumerate(retrieved_chunks):
        if max_total_tokens >= 0 and (
            len(concat_tokens) + len(cur_tokens) + (index > 0) * len(separator_tokens)
            > max_total_tokens
        ):
            # NOTE(arun): We are intentionally ignoring any potentially smaller chunks
            # that appear after this one. Most chunks have similar sizes, so it's
            # unlikely to help and we don't want to introduce potentially bad chunks
            # near the of the sequence.
            logging.info(
                "Used %d / %d tokens to add %d retrieval chunks. "
                "Next candidate chunk would need %d tokens.",
                len(concat_tokens),
                max_total_tokens,
                index,
                len(cur_tokens) + (index > 0) * len(separator_tokens),
            )
            break

        if index > 0:
            if reverse_chunks:
                concat_tokens.extendleft(separator_tokens)
            else:
                concat_tokens.extend(separator_tokens)
        if reverse_chunks:
            concat_tokens.extendleft(reversed(cur_tokens))
        else:
            concat_tokens.extend(cur_tokens)

        if used_indices_output is not None:
            used_indices_output.add(index)
    else:
        logging.info(
            "Used %d / %d tokens to add all %d retrieval chunks.",
            len(concat_tokens),
            max_total_tokens,
            index + 1,
        )
    return list(concat_tokens)


def update_retrieval_budget(
    retrieval_budget: int,
    max_prompt_tokens: int,
    component_tokens: dict[Any, TokenList],
    additional_tokens: int = 0,
) -> int:
    current_prompt_tokens = (
        sum(len(x) for x in component_tokens.values()) + 2
    )  # fim_middle and retrieval body tokens
    new_retrieval_budget = max_prompt_tokens - current_prompt_tokens - additional_tokens
    retrieval_budget = min(retrieval_budget, new_retrieval_budget)
    # Log if we need to fix the retrieval budget
    if new_retrieval_budget < retrieval_budget:
        component_length_msg = "Component lengths: " + "; ".join(
            f"{k}: {len(v)}" for k, v in component_tokens.items()
        )
        budget_log_msg = (
            "Prompt too long for retrieval budget: original budget: %d, correct budget: %d\n"
            % (
                retrieval_budget,
                new_retrieval_budget,
            )
        )
        log_msg = budget_log_msg + component_length_msg
        logging.warning(log_msg)
    return new_retrieval_budget


@dataclass
class CascadeTruncatingStrategy:
    """A truncating strategy that truncates each sections in turn.

    Given a list of sections, their respective sizes and minimal budgets, if the total\
    size exceeds the total budget, will truncate each section in order until\
    the budget is met. No section will be truncated to be smaller than its minimal\
    budget.
    This means that each section is allowed to use any extra token budget available\
    in the prompt, and the last section has the highest priority since other sections\
    are always truncated first.
    """

    total_budget: int
    """The total budget for the prompt."""
    section_budgets: dict[str, int]
    """The minimal budget for each section. First section will be truncated first."""

    def __post_init__(self):
        for section, budget in self.section_budgets.items():
            if budget < 0:
                raise ValueError(f"{section=}, {budget=}")
        sections_sum = sum(self.section_budgets.values())
        if sections_sum > self.total_budget:
            raise ValueError(f"{sections_sum=} > {self.total_budget=}")

    def compute_section_sizes(self, section_sizes: Mapping[str, int]) -> dict[str, int]:
        """Compute the truncated sizes for each section to meet the budget."""
        new_sizes = {s: section_sizes[s] for s in self.section_budgets}
        to_truncate = sum(new_sizes.values()) - self.total_budget
        for section, budget in self.section_budgets.items():
            if to_truncate <= 0:
                break
            section_size = new_sizes[section]
            truncation = min(max(0, section_size - budget), to_truncate)
            to_truncate -= truncation
            new_sizes[section] -= truncation
            assert new_sizes[section] >= 0, f"{new_sizes=}, {section=}, {self=}"
        assert to_truncate <= 0, f"{to_truncate=}, {new_sizes=}, {self=}"
        assert sum(new_sizes.values()) <= self.total_budget, f"{new_sizes=}, {self=}"
        return new_sizes


def trailing_n(tokens: list[TokenT], n: int) -> list[TokenT]:
    """Return the last up-to-N tokens of the given `tokens`.

    Note that `tokens[-n:]` is not correct for n==0, and we need to support the case
    where n > len(tokens), hence `tokens[max(len(tokens) - n, 0) :]`.
    """
    return tokens[max(len(tokens) - n, 0) :]


def head_n(tokens: list[TokenT], n: int) -> list[TokenT]:
    """Return the first up-to-N tokens of the given `tokens`."""
    return tokens[:n]


def join_tokens(
    tokens_iter: Iterable[Sequence[TokenT]], sep: Sequence[TokenT]
) -> list[TokenT]:
    """Joins a list of prompts with a separator prompt."""
    result = list[TokenT]()
    for i, prompt in enumerate(tokens_iter):
        if i > 0:
            result.extend(sep)
        result.extend(prompt)
    return result


def prompt_chunks_by_origin(
    chunks: Iterable[PromptChunk],
) -> Mapping[str, Sequence[PromptChunk]]:
    """Convert chunks to a map from origin to chunks coming from that origin."""
    chunks_dict: dict[str, list[PromptChunk]] = {}
    for chunk in chunks:
        if chunk.origin not in chunks_dict:
            chunks_dict[chunk.origin] = []
        chunks_dict[chunk.origin].append(chunk)
    return chunks_dict
