"""Unit tests for core/misc.py."""

from typing import Type<PERSON>ar

from base.prompt_format.common import PromptChunk
from base.prompt_format.util import (
    concatenate_retrieved_chunks,
    head_n,
    prompt_chunks_by_origin,
    trailing_n,
)

T = TypeVar("T")


def test_separator_location():
    """Test append_separator_at_start/end."""
    list_of_tokens = [
        ["example_doc.py\n", "# You can aggregate\nwith a pooling"],
        ["example_doc.py\n", "# You can aggregate\n# with a maxing"],
    ]
    prompt = concatenate_retrieved_chunks(
        retrieved_chunks=list_of_tokens,
        separator_tokens=["<|ret-endofdoc|>"],
        max_total_tokens=1000,
    )
    expected_prompt = """example_doc.py
# You can aggregate
# with a maxing<|ret-endofdoc|>example_doc.py
# You can aggregate
with a pooling"""
    assert "".join(prompt) == expected_prompt


def test_control_max_tokens():
    """Test with constrained max_total_tokens."""
    prompt_tokens = concatenate_retrieved_chunks(
        retrieved_chunks=[[1]] * 1000,
        separator_tokens=[2],
        max_total_tokens=5,
    )
    assert prompt_tokens == [1, 2, 1, 2, 1]


def test_used_indices_output():
    """Test used_indices_output argument."""
    used_indices = set()
    prompt_tokens = concatenate_retrieved_chunks(
        retrieved_chunks=[[1, 2, 3], [4, 5, 6], [7, 8, 9], [10, 11]],
        separator_tokens=[],
        max_total_tokens=7,
        used_indices_output=used_indices,
    )
    assert prompt_tokens == [4, 5, 6, 1, 2, 3]
    assert used_indices == {0, 1}

    prompt_tokens = concatenate_retrieved_chunks(
        retrieved_chunks=[[1, 2, 3], [4, 5, 6], [7, 8, 9], [10, 11]],
        separator_tokens=[],
        max_total_tokens=6,
        reverse_chunks=False,
        used_indices_output=used_indices,
    )
    assert prompt_tokens == [1, 2, 3, 4, 5, 6]
    assert used_indices == {0, 1}


def test_reverse_chunks():
    """Test append_separator_at_start/end."""
    list_of_tokens = [
        ["example_doc.py\n", "# You can aggregate\nwith a pooling"],
        ["example_doc.py\n", "# You can aggregate\n# with a maxing"],
    ]
    prompt = concatenate_retrieved_chunks(
        retrieved_chunks=list_of_tokens,
        separator_tokens=["<|ret-endofdoc|>"],
        max_total_tokens=1000,
        reverse_chunks=False,
    )
    expected_prompt = """example_doc.py
# You can aggregate
with a pooling<|ret-endofdoc|>example_doc.py
# You can aggregate
# with a maxing"""
    assert "".join(prompt) == expected_prompt


def test_trailing_n_zero():
    """Test if trailing_n successfully truncates when n is zero."""
    tokens = [3, 5, 2, 4]
    trailing_tokens = trailing_n(tokens, 0)
    assert trailing_tokens == []


def test_trailing_n_smaller():
    """Test if trailing_n successfully truncates when n is smaller than token len."""
    tokens = [3, 5, 2, 4]
    trailing_tokens = trailing_n(tokens, 2)
    assert trailing_tokens == [2, 4]


def test_trailing_n_equal():
    """Test if trailing_n successfully truncates when n is equal to token len."""
    tokens = [3, 5, 2, 4]
    trailing_tokens = trailing_n(tokens, 4)
    assert trailing_tokens == [3, 5, 2, 4]


def test_trailing_n_larger():
    """Test if trailing_n successfully truncates when n is equal to token len."""
    tokens = [3, 5, 2, 4]
    trailing_tokens = trailing_n(tokens, 6)
    assert trailing_tokens == [3, 5, 2, 4]


def test_head_n_zero():
    """Test if head_n successfully truncates when n is equal to zero."""
    tokens = [3, 5, 2, 4]
    head_tokens = head_n(tokens, 0)
    assert head_tokens == []


def test_head_n_smaller():
    """Test if head_n successfully truncates when n is smaller than token len."""
    tokens = [3, 5, 2, 4]
    head_tokens = head_n(tokens, 2)
    assert head_tokens == [3, 5]


def test_head_n_equal():
    """Test if head_n successfully truncates when n is equal to token len."""
    tokens = [3, 5, 2, 4]
    head_tokens = head_n(tokens, 4)
    assert head_tokens == [3, 5, 2, 4]


def test_head_n_larger():
    """Test if head_n successfully truncates when n is larger than token len."""
    tokens = [3, 5, 2, 4]
    head_tokens = head_n(tokens, 6)
    assert head_tokens == [3, 5, 2, 4]


def test_prompt_chunks_as_dict():
    """Test prompt_chunks_as_dict."""
    chunks = [
        PromptChunk(text="foo", path="foo.py", origin=""),
        PromptChunk(text="bar", path="bar.py", origin=""),
        PromptChunk(text="xyz", path="xyz.py", origin="dense_retriever"),
        PromptChunk(text="abc", path="abc.py", origin="signature_retriever"),
    ]

    chunks_dict = prompt_chunks_by_origin(chunks)
    assert chunks_dict == {
        "": [chunks[0], chunks[1]],
        "dense_retriever": [chunks[2]],
        "signature_retriever": [chunks[3]],
    }
