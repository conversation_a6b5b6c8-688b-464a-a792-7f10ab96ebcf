"""Feature extractor for the low quality filter."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import ClassVar, List, Sequence, TypedDict, Type
import numpy as np

import base.tokenizers


def split_at_last_newline(text: str) -> tuple[str, str]:
    """
    Split the last newline in the string. The newline character is not included in the result. If there is no newline, the result is ("", text).

    Args:
        text: The string to split.

    Returns:
        A tuple of the text before the last newline and the text after the last newline.
    """

    # A \n at the end of the string is not considered a newline, so we need to search for \n.
    last_newline_index = text.rfind("\n")
    if last_newline_index == -1:
        return "", text
    return text[:last_newline_index], text[last_newline_index + 1 :]


def extract_non_empty_lines(text: str) -> list[str]:
    """
    Extract the non-empty lines from the string.

    Args:
        text: The string to split.

    Returns:
        A list of non-empty lines.
    """
    return [line.strip() for line in text.splitlines() if line.strip()]


class CompletionFilterFeaturesBase(TypedDict):
    """Base class for completion filter features."""

    pass


class FeatureExtractorBase(ABC):
    @classmethod
    @abstractmethod
    def get_features_type(cls) -> Type[CompletionFilterFeaturesBase]:
        pass

    @classmethod
    @abstractmethod
    def extract_completion_filter_features(
        cls,
        token_ids: Sequence[int],
        log_probs: Sequence[float],
        tokenizer: base.tokenizers.Tokenizer,
        prefix: str,
        path: str,
    ) -> CompletionFilterFeaturesBase:
        pass

    @classmethod
    def get_feature_names(cls) -> list[str]:
        """The feature names."""
        return list(cls.get_features_type().__annotations__.keys())


class CompletionFilterFeaturesV1(CompletionFilterFeaturesBase):
    token_prob_min: float
    token_prob_median: float
    token_prob_max: float
    leading_average_1: float
    leading_average_3: float
    leading_average_5: float
    moving_average_min: float
    moving_average_median: float
    moving_average_max: float
    mean: float
    num_lines: float
    token_diversity: float


class FeatureExtractorV1(FeatureExtractorBase):
    """Feature extractor V1 for the low quality filter. Do NOT change the semantics of these features without retraining the model."""

    @classmethod
    def get_features_type(cls) -> Type[CompletionFilterFeaturesBase]:
        return CompletionFilterFeaturesV1

    @classmethod
    def extract_completion_filter_features(
        cls,
        token_ids: Sequence[int],
        log_probs: Sequence[float],
        tokenizer: base.tokenizers.Tokenizer,
        prefix: str = "",
        path: str = "",
    ) -> CompletionFilterFeaturesBase:
        del prefix, path
        # NOTE: Feature generation needs to be compatible with the XGBoost saved model.
        # NOTE: Pyright raises a useless type warning.
        probs = np.exp(log_probs)  # type: ignore
        window_size = min(3, len(log_probs))
        kernel = np.ones(window_size) / window_size
        moving_average = np.convolve(log_probs, kernel, mode="valid")  # type: ignore
        completion = tokenizer.detokenize(token_ids)
        features = {
            # Token feature
            "token_prob_min": np.min(probs),
            "token_prob_median": np.median(probs),
            "token_prob_max": np.max(probs),
            # Subsequence feature
            "leading_average_1": probs[0],
            "leading_average_3": np.mean(probs[:3]),
            "leading_average_5": np.mean(probs[:5]),
            # Moving average
            "moving_average_min": np.min(moving_average),
            "moving_average_median": np.median(moving_average),
            "moving_average_max": np.max(moving_average),
            # Average
            "mean": np.exp(np.mean(log_probs)),  # type: ignore
            # Characteristics of the completion
            "num_lines": np.sum(["\n" in t for t in completion]),
            "token_diversity": len(token_ids) / len(set(token_ids)),
        }
        return cls.get_features_type()(**features)


class CompletionFilterFeaturesV2(CompletionFilterFeaturesBase):
    token_prob_min: float
    token_prob_median: float
    token_prob_max: float
    first_token_low: float
    num_very_low_prob_tokens: float
    num_low_prob_tokens: float
    low_prob_token_ratio: float
    high_prob_token_ratio: float
    leading_average_1: float
    leading_average_3: float
    leading_average_5: float
    moving_average_min: float
    moving_average_median: float
    moving_average_max: float
    mean: float
    num_lines: int
    token_diversity: float
    num_complexities: int
    num_spaces: int
    num_chars: int
    num_numbers: int
    is_comment: int
    completion_geq_1_duplicate_line: int
    completion_geq_4_duplicate_line: int
    any_completion_line_matches_last_prefix_line: int
    first_long_non_print_line_matches_recent_prefix: int
    # One-hot encoding so model treats each file type as a distinct category, 'is_misc' captures all other file types
    is_py: int
    is_ipynb: int
    is_ts: int
    is_tsx: int
    is_go: int
    is_rs: int
    is_proto: int
    is_yaml: int
    is_json: int
    is_java: int
    is_misc: int


class FeatureExtractorV2(FeatureExtractorBase):
    """Feature extractor V2 for the low quality filter. Do NOT change the semantics of these features without retraining the model."""

    EXTENSIONS: ClassVar[List[str]] = [
        "py",
        "ipynb",
        "ts",
        "tsx",
        "go",
        "rs",
        "proto",
        "yaml",
        "json",
        "java",
    ]

    @classmethod
    def get_features_type(cls) -> Type[CompletionFilterFeaturesBase]:
        return CompletionFilterFeaturesV2

    @classmethod
    def extract_completion_filter_features(
        cls,
        token_ids: Sequence[int],
        log_probs: Sequence[float],
        tokenizer: base.tokenizers.Tokenizer,
        prefix: str,
        path: str,
    ) -> CompletionFilterFeaturesBase:
        # NOTE: Feature generation needs to be compatible with the XGBoost saved model.
        # NOTE: Pyright raises a useless type warning.
        probs = np.exp(log_probs)  # type: ignore
        window_size = min(3, len(log_probs))
        kernel = np.ones(window_size) / window_size
        moving_average = np.convolve(log_probs, kernel, mode="valid")  # type: ignore
        completion = tokenizer.detokenize(token_ids)

        # Extract the full code line for the completion, including any partial line from the prefix.
        # If completion starts with newline, treat the completion as starting on the new line. Otherwise, include the last prefix line.
        if completion.startswith("\n"):
            completion_line_to_completion_end = extract_non_empty_lines(completion)
            prefix_start_to_prefix_end = extract_non_empty_lines(prefix)
        else:
            # Split the prefix at the last newline.
            text_before_last_newline, text_after_last_newline = split_at_last_newline(
                prefix
            )
            completion_line_to_completion_end = extract_non_empty_lines(
                text_after_last_newline + completion
            )
            prefix_start_to_prefix_end = extract_non_empty_lines(
                text_before_last_newline
            )

        # At most, we only want to look at the last 5 lines of the prefix
        prefix_start_to_prefix_end = prefix_start_to_prefix_end[-5:]

        # Whether or not any of the completion lines match the last line of the prefix
        any_completion_line_matches_last_prefix_line = False
        if completion_line_to_completion_end and prefix_start_to_prefix_end:
            for completion_line in completion_line_to_completion_end:
                if len(completion_line) > 2:
                    if completion_line == prefix_start_to_prefix_end[-1]:
                        any_completion_line_matches_last_prefix_line = True
                        break

        # Whether or not the first long, non-print line of the completion is in the last 5 lines of the prefix
        # It is likely that if a completion matches some line in the prefix, it should be filtered out. However, print statements may be correctly duplicated and thus confuse the feature.
        # These long, non-print lines are more likely to produce a meaningful feature as they are unlikely to be intended for duplication.
        first_long_non_print_line_matches_recent_prefix = False
        if completion_line_to_completion_end and prefix_start_to_prefix_end:
            for completion_line in completion_line_to_completion_end:
                if len(completion_line) >= 10 and "print" not in completion_line:
                    if completion_line in prefix_start_to_prefix_end:
                        first_long_non_print_line_matches_recent_prefix = True
                    break

        # Whether or not the completion has duplicate lines
        completion_geq_1_duplicate_line = False
        completion_geq_4_duplicate_line = False
        if completion_line_to_completion_end:
            larger_completion_lines = [
                line for line in completion_line_to_completion_end if len(line) >= 3
            ]
            num_completion_lines = len(larger_completion_lines)
            num_unique_completion_lines = len(set(larger_completion_lines))
            if num_completion_lines > num_unique_completion_lines:
                completion_geq_1_duplicate_line = True
            if num_completion_lines - num_unique_completion_lines >= 4:
                completion_geq_4_duplicate_line = True

        # Whether or not the first line of the completion is a comment
        is_comment = False
        if completion_line_to_completion_end:
            prefix_to_completion_line = completion_line_to_completion_end[0]
            comment_prefixes = ["#", "//", "/*", "*/"]
            if any(
                prefix_to_completion_line.startswith(prefix)
                for prefix in comment_prefixes
            ):
                is_comment = True

        # get file extension, ignoring the leading dot
        path_extension = Path(path).suffix[1:]
        extension_features = {
            f"is_{ext}": int(path_extension == ext) for ext in cls.EXTENSIONS
        }

        features = {
            # Token feature
            "token_prob_min": np.min(probs),
            "token_prob_median": np.median(probs),
            "token_prob_max": np.max(probs),
            "first_token_low": 1 if probs[0] < 0.25 else 0,
            "num_very_low_prob_tokens": (probs < 0.15).sum(),
            "num_low_prob_tokens": (probs < 0.5).sum(),
            "low_prob_token_ratio": np.sum(probs < 0.5) / len(probs),
            "high_prob_token_ratio": np.sum(probs > 0.95) / len(probs),
            # Subsequence feature
            "leading_average_1": probs[0],
            "leading_average_3": np.mean(probs[:3]),
            "leading_average_5": np.mean(probs[:5]),
            # Moving average
            "moving_average_min": np.min(moving_average),
            "moving_average_median": np.median(moving_average),
            "moving_average_max": np.max(moving_average),
            # Average
            "mean": np.exp(np.mean(log_probs)),  # type: ignore
            # Characteristics of the completion
            "num_lines": len(completion_line_to_completion_end),
            "token_diversity": len(token_ids) / len(set(token_ids)),
            "num_complexities": sum(
                completion.count(c) for c in [".", "(", ")", "[", "]", "{", "}"]
            ),
            "num_spaces": completion.strip().count(" "),
            "num_chars": len(completion.strip()),
            "num_numbers": sum(completion.count(str(i)) for i in range(10)),
            "is_comment": int(is_comment),
            "completion_geq_1_duplicate_line": int(completion_geq_1_duplicate_line),
            "completion_geq_4_duplicate_line": int(completion_geq_4_duplicate_line),
            # Characteristics of the prefix
            "any_completion_line_matches_last_prefix_line": int(
                any_completion_line_matches_last_prefix_line
            ),
            "first_long_non_print_line_matches_recent_prefix": int(
                first_long_non_print_line_matches_recent_prefix
            ),
            # File extension type
            **extension_features,
            "is_misc": int(path_extension not in cls.EXTENSIONS),
        }

        return cls.get_features_type()(**features)


class FeatureExtractorFactory:
    @classmethod
    def create(cls, version: str) -> type[FeatureExtractorBase]:
        """Create a feature extractor by version."""
        if version not in cls.versions():
            raise ValueError(f"Unknown feature extractor version: {version}")
        return cls.versions()[version]

    @classmethod
    def versions(cls) -> dict[str, type[FeatureExtractorBase]]:
        return {
            "feature_extractor_v1": FeatureExtractorV1,
            "feature_extractor_v2": FeatureExtractorV2,
        }
