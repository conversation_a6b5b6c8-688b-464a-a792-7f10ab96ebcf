"""Tests for extract_completion_filter_features.

pytest base/completion_filter/extract_completion_filter_features_test.py
"""

import pytest

from typing import cast
import numpy as np
import base.tokenizers
from base.completion_filter.extract_completion_filter_features import (
    FeatureExtractorFactory,
    split_at_last_newline,
    extract_non_empty_lines,
    CompletionFilterFeaturesV2,
)


@pytest.mark.parametrize(
    "prefix, expected_text_before, expected_text_after",
    [
        ("line1", "", "line1"),
        ("line1\n   line2", "line1", "   line2"),
        ("line1\n   line2\n   line3", "line1\n   line2", "   line3"),
        ("line1\n", "line1", ""),
        ("line1\n\n", "line1\n", ""),
        ("\n", "", ""),
        ("\n line1", "", " line1"),
        ("", "", ""),
    ],
)
def test_split_at_last_newline(
    prefix: str, expected_text_before: str, expected_text_after: str
):
    assert split_at_last_newline(prefix) == (
        expected_text_before,
        expected_text_after,
    )


@pytest.mark.parametrize(
    "text, expected",
    [
        ("line1", ["line1"]),
        ("line1\n   line2", ["line1", "line2"]),
        (" line1\n   line2\n   line3  ", ["line1", "line2", "line3"]),
        ("line1\n", ["line1"]),
        ("line1\n    \n", ["line1"]),
        ("\n", []),
        ("", []),
    ],
)
def test_extract_non_empty_lines(text: str, expected: list[str]):
    assert extract_non_empty_lines(text) == expected


def test_extract_version_exception():
    with pytest.raises(ValueError):
        FeatureExtractorFactory.create("feature_extractor_does_not_exist")


def test_extract_completion_filter_features_v1():
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder2")
    feature_extractor = FeatureExtractorFactory.create("feature_extractor_v1")
    features = feature_extractor.extract_completion_filter_features(
        token_ids=[1, 1, 1, 4, 5],
        log_probs=np.log([0.1, 0.2, 0.3, 0.4, 0.5], dtype=np.float32).tolist(),
        tokenizer=tokenizer,
        prefix="def foo():",
        path="services/completion_host/single_model_server/post_processing_test.py",
    )
    expected = {
        # Token feature
        "token_prob_min": 0.1,
        "token_prob_median": 0.3,
        "token_prob_max": 0.5,
        # Subsequence feature
        "leading_average_1": 0.1,
        "leading_average_3": 0.2,
        "leading_average_5": 0.3,
        # Moving average
        "moving_average_min": -1.7053319,
        "moving_average_median": -1.243233816211397,
        "moving_average_max": -0.9378035722533453,
        # Average
        "mean": 0.2605171084697352,
        # Characteristics of the completion
        "num_lines": 0,
        "token_diversity": 1.666667,
    }
    assert list(features.keys()) == feature_extractor.get_feature_names()
    assert list(expected.keys()) == feature_extractor.get_feature_names()
    for key in features:
        assert features[key] == pytest.approx(expected[key], 1e-6), key


def test_extract_completion_filter_features_v2():
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder")
    feature_extractor = FeatureExtractorFactory.create("feature_extractor_v2")
    features = feature_extractor.extract_completion_filter_features(
        token_ids=[1, 1, 1, 4, 5],
        log_probs=np.log([0.1, 0.2, 0.3, 0.4, 0.5], dtype=np.float32).tolist(),
        tokenizer=tokenizer,
        prefix="def foo():",
        path="services/completion_host/single_model_server/post_processing_test.py",
    )
    expected = {
        # Token feature
        "token_prob_min": 0.1,
        "token_prob_median": 0.3,
        "token_prob_max": 0.5,
        "first_token_low": 1,
        "num_very_low_prob_tokens": 1,
        "num_low_prob_tokens": 5,
        "low_prob_token_ratio": 1.0,
        "high_prob_token_ratio": 0.0,
        # Subsequence feature
        "leading_average_1": 0.1,
        "leading_average_3": 0.2,
        "leading_average_5": 0.3,
        # Moving average
        "moving_average_min": -1.7053319,
        "moving_average_median": -1.243233816211397,
        "moving_average_max": -0.9378035722533453,
        # Average
        "mean": 0.2605171084697352,
        # Characteristics of the completion
        "num_lines": 1,
        "token_diversity": 1.666667,
        "num_complexities": 0,
        "num_spaces": 0,
        "num_chars": 55,
        "num_numbers": 0,
        "is_comment": 0,
        "completion_geq_1_duplicate_line": 0,
        "completion_geq_4_duplicate_line": 0,
        # Characteristics of the prefix
        "any_completion_line_matches_last_prefix_line": 0,
        "first_long_non_print_line_matches_recent_prefix": 0,
        # File extension type
        "is_py": 1,
        "is_ipynb": 0,
        "is_ts": 0,
        "is_tsx": 0,
        "is_go": 0,
        "is_rs": 0,
        "is_proto": 0,
        "is_yaml": 0,
        "is_json": 0,
        "is_java": 0,
        "is_misc": 0,
    }
    assert list(features.keys()) == feature_extractor.get_feature_names()
    assert list(expected.keys()) == feature_extractor.get_feature_names()
    for key in features:
        assert features[key] == pytest.approx(expected[key], 1e-6), key


@pytest.mark.parametrize(
    "completion, completion_geq_1_duplicate_line, completion_geq_4_duplicate_line",
    [
        ("line1\nline2\nline3", 0, 0),
        ("line1\nline2\nline2", 1, 0),
        ("line1\nline1\nline2", 1, 0),
        ("line1\nline2\nline2\nline2\nline2\nline2\nline2\nline2\nline2\nline2", 1, 1),
    ],
)
def test_completion_duplicate_lines_features(
    completion: str,
    completion_geq_1_duplicate_line: int,
    completion_geq_4_duplicate_line: int,
):
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder2")
    feature_extractor = FeatureExtractorFactory.create("feature_extractor_v2")
    token_ids = tokenizer.tokenize_safe(completion)
    features = feature_extractor.extract_completion_filter_features(
        token_ids=token_ids,
        log_probs=np.log([0.1] * len(token_ids), dtype=np.float32).tolist(),
        tokenizer=tokenizer,
        prefix="def foo():\n",
        path="services/completion_host/single_model_server/post_processing_test.py",
    )
    features_v2 = cast(CompletionFilterFeaturesV2, features)
    assert (
        features_v2["completion_geq_1_duplicate_line"]
        == completion_geq_1_duplicate_line
    )
    assert (
        features_v2["completion_geq_4_duplicate_line"]
        == completion_geq_4_duplicate_line
    )


@pytest.mark.parametrize(
    "prefix, completion, any_completion_line_matches_last_prefix_line, first_long_non_print_line_matches_recent_prefix",
    [
        ("line1\nline2\nline3", "\nline3", 1, 0),
        ("line1\nline2\nline3\n", "line3", 1, 0),
        ("line1\nline2\nline3\nli", "ne3", 1, 0),
        ("line1\nline2\nline3\nli", "ne2", 0, 0),
        ("line1\nline2\nline3\n", "line1\nline2\nline3", 1, 0),
        ("line1\nline2\nline3\n", "line1\nline2\nline2", 0, 0),
        ("line1\nline2\nline3\nli", "ne1\nline2\nline2", 0, 0),
        ("linelineline1\nline2\nline3\n", "linelineline1\nline2", 0, 1),
        ("linelineline1\nline2\nline3\n", "linelineline1\nline2\nline3", 1, 1),
        (
            "linelineline1\nline2\nline3\n",
            "line1line2line3\nlinelineline1\nline2\nline3",
            1,
            0,
        ),
    ],
)
def test_completion_prefix_features(
    prefix: str,
    completion: str,
    any_completion_line_matches_last_prefix_line: int,
    first_long_non_print_line_matches_recent_prefix: int,
):
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder2")
    feature_extractor = FeatureExtractorFactory.create("feature_extractor_v2")
    token_ids = tokenizer.tokenize_safe(completion)
    features = feature_extractor.extract_completion_filter_features(
        token_ids=token_ids,
        log_probs=np.log([0.1] * len(token_ids), dtype=np.float32).tolist(),
        tokenizer=tokenizer,
        prefix=prefix,
        path="services/completion_host/single_model_server/post_processing_test.py",
    )
    features_v2 = cast(CompletionFilterFeaturesV2, features)
    assert (
        features_v2["any_completion_line_matches_last_prefix_line"]
        == any_completion_line_matches_last_prefix_line
    )
    assert (
        features_v2["first_long_non_print_line_matches_recent_prefix"]
        == first_long_non_print_line_matches_recent_prefix
    )
