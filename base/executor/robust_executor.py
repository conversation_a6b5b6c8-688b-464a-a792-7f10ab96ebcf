"""A process-based executor robust to timeouts and segfaults.

This executor is based on `concurrent.futures.ProcessPoolExecutor`; it enforces a
timeout by killing the worker process and recreating the executor.

Usage:
>>> from base.executor.robust_executor import RobustExecutor
>>> with RobustExecutor(timeout_s=1) as executor:
>>>     executor.run(fn, *args, **kwargs)
>>> # OR (if you need to manage the executor across scope boundaries):
>>> executor = RobustExecutor(timeout_s=1)
>>> executor.run(fn, *args, **kwargs)
>>> executor.shutdown()

The recommended approach for a process-local variable is simply @functools.cache:

>>> from functools import cache
>>> @cache
>>> def y() -> int:
>>>     print("computing y...")
>>>     return 1
>>> def process(x: int) -> int:
>>>     return x + y()

Some environments (i.e., Spark) share state across forked processes. The `RobustExecutor`
is inherently hard to share across processes, so this commit implements a compromise:
create a new executor on the new process with the same initializer, etc.

This will prevent your code from "failing", but is also sub-optimal in
terms of cache performance, etc. Please avoid if possible.
"""

import ctypes
import logging
import multiprocessing
import os
from concurrent.futures import BrokenExecutor, ProcessPoolExecutor
from multiprocessing import get_context
from multiprocessing.sharedctypes import Synchronized
from typing import Callable, ParamSpec, TypeVar, cast

logger = logging.getLogger(__name__)


def _setup_process(
    pid: Synchronized, initializer: Callable | None = None, initargs: tuple = ()
):
    with pid.get_lock():
        if pid.value != -1:
            # NOTE(arun): If this exception is ever raised, the executor raises a
            # BrokenExecutor which handled in the `RobustExecutor` by bringing down the
            # executor, clearing `pid.value` and starting a new one. This should fix the
            # problem.
            raise RuntimeError(
                f"Worker {pid.value} already setup. This is likely a bug."
            )
        pid.value = os.getpid()
        logger.info("Creating worker process %d", pid.value)

    if initializer is not None:
        initializer(*initargs)


P = ParamSpec("P")
RetT = TypeVar("RetT")

DEFAULT_MP_CONTEXT = "fork"


class RobustExecutor:
    """A robust executor that handles timeouts and segfaults.

    This executor is based on `concurrent.futures.ProcessPoolExecutor`; it enforces a
    timeout by killing the worker process and recreating the executor.

    This executor can work in a multi-threaded program as long as the executor's methods
    are only called from one thread at a time.
    """

    def __init__(
        self,
        initializer=None,
        initargs=(),
        timeout_s: float | None = None,
        mp_context: str = DEFAULT_MP_CONTEXT,
    ):
        self._initializer = initializer
        self._initargs = initargs
        self._timeout_s = timeout_s
        self._mp_context = mp_context
        self._context = get_context(mp_context)
        self._pid_mutex = cast(Synchronized, self._context.Value(ctypes.c_int, -1))
        self._executor = ProcessPoolExecutor(
            max_workers=1,
            initializer=_setup_process,
            initargs=(self._pid_mutex, self._initializer, self._initargs),
        )

    def _cleanup_executor(self):
        # Kill the worker process if it's still going. This will be a no-op when the
        # process dies because of a segfault or OOM.

        # Reset the PID and create a new executor.
        with self._pid_mutex.get_lock():
            worker_processes = [
                p
                for p in multiprocessing.active_children()
                if p.pid == self._pid_mutex.value
            ]
            self._pid_mutex.value = -1

        if worker_processes:
            logger.info("Killing failed worker process %d", worker_processes[0].pid)
            worker_processes[0].kill()

        self._executor = ProcessPoolExecutor(
            max_workers=1,
            initializer=_setup_process,
            initargs=(self._pid_mutex, self._initializer, self._initargs),
        )

    def run(self, fn: Callable[P, RetT], *args: P.args, **kwargs: P.kwargs) -> RetT:
        """Run a function in a subprocess.

        Args:
            fn: The function to run.
            *args: The arguments to the function.
            **kwargs: The keyword arguments to the function.

        Returns:
            The return value of the function, if any.

        Warning:
            This method is not thread-safe.
        """
        try:
            return self._executor.submit(fn, *args, **kwargs).result(
                timeout=self._timeout_s
            )
        except TimeoutError:
            self._cleanup_executor()
            raise
        except BrokenExecutor as e:
            # Clean up the executor and re-raise the exception.
            self._cleanup_executor()
            raise RuntimeError("Worker process died unexpectedly.") from e

    def shutdown(self):
        """Shutdown the executor.

        This method is not thread-safe.
        """
        self._executor.shutdown(wait=True)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.shutdown()

    def __getstate__(self) -> dict:
        """Get the serializable state of this executor. Used in __setstate__."""
        return {
            "initializer": self._initializer,
            "initargs": self._initargs,
            "timeout_s": self._timeout_s,
            "mp_context": self._mp_context,
        }

    def __setstate__(self, state: dict):
        logger.warning(
            "An executor is being shared across processes. Creating a new executor, "
            "with empty caches. Please avoid this if possible."
        )

        self._initializer = state["initializer"]
        self._initargs = state["initargs"]
        self._timeout_s = state["timeout_s"]
        self._context = get_context(state["mp_context"])
        self._pid_mutex = cast(Synchronized, self._context.Value(ctypes.c_int, -1))
        self._executor = ProcessPoolExecutor(
            max_workers=1,
            initializer=_setup_process,
            initargs=(self._pid_mutex, self._initializer, self._initargs),
        )
