load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

py_library(
    name = "robust_executor",
    srcs = [
        "robust_executor.py",
    ],
    visibility = BASE_VISIBILITY,
    deps = [],
)

pytest_test(
    name = "robust_executor_test",
    size = "small",
    srcs = ["robust_executor_test.py"],
    deps = [
        ":robust_executor",
    ],
)

py_library(
    name = "submit_blocking_executor",
    srcs = [
        "submit_blocking_executor.py",
    ],
    visibility = BASE_VISIBILITY,
)
