"""Tests for robust_executor."""

import ctypes
import functools
import time
from concurrent.futures import ProcessPoolExecutor
from multiprocessing import get_context

import pytest

from base.executor.robust_executor import RobustExecutor


@functools.cache
def create_state():
    """Create a state that is shared within a process."""
    return {"n_calls": 0}


def process(x: int) -> int:
    """Compute a value."""
    state = create_state()
    state["n_calls"] += 1
    return x + state["n_calls"]


def fail() -> int:
    """A reliable way to fail with a catchable exception."""
    raise ValueError()


def segfault() -> int:
    """A reliable way to segfault: access a pointer an invalid address."""
    return int(ctypes.c_int.from_address(0xDEADBEEF))


def test_run():
    with RobustExecutor() as executor:
        for i in range(10):
            assert executor.run(process, 9) == i + 10

    # We create another executor to test that it sees fresh state.
    with RobustExecutor() as executor:
        for i in range(10):
            assert executor.run(process, 9) == i + 10


def test_run_with_exceptions():
    with RobustExecutor() as executor:
        with pytest.raises(ValueError):
            executor.run(fail)
        # We can still run another function after an exception.
        assert executor.run(process, 0) == 1


def test_run_with_timeouts():
    with RobustExecutor(timeout_s=0.1) as executor:
        # This should not timeout.
        executor.run(time.sleep, 0.01)

        # This will timeout.
        with pytest.raises(TimeoutError):
            executor.run(time.sleep, 0.2)

        # We can still run another function afterwards.
        assert executor.run(process, 0) == 1


def test_run_with_segfaults():
    # longer timeout as a coredump might take a while to be written.
    with RobustExecutor(timeout_s=10.0) as executor:
        # We can still run another function afterwards.
        assert executor.run(process, 0) == 1

        with pytest.raises(RuntimeError):
            executor.run(segfault)

        # We can still run another function afterwards, though the state is reset.
        assert executor.run(process, 0) == 1


def test_run_with_forking():
    executor = RobustExecutor()

    with ProcessPoolExecutor(max_workers=1, mp_context=get_context("fork")) as pool:
        results = list(pool.map(executor.run, [process] * 10, [9] * 10))
        # NOTE(arun): The all 10s result here may be counter intuitive, but is a result
        # of the fact that the executor is created anew in each forked process.
        assert results == [10] * 10
