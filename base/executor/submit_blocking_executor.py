"""An executor that submits tasks to a thread pool and blocks if max_workers tasks are running

This allows flow control to be applied to the number of tasks running at any given time.
"""

from concurrent.futures import ThreadPoolExecutor, Executor
from threading import BoundedSemaphore


class SubmitBlockingExecutor(Executor):
    """An executor that blocks in `submit` if all workers are busy running tasks."""

    def __init__(self, max_workers: int):
        """Create a new executor."""
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._semaphore = BoundedSemaphore(max_workers)

    def _run(self, fn, *args, **kwargs):
        try:
            return fn(*args, **kwargs)
        finally:
            self._semaphore.release()

    def submit(self, fn, *args, **kwargs):
        """Submit a new task to the thread pool.

        Submit only returns if less than max_workers tasks are running.
        """
        self._semaphore.acquire()
        return self._executor.submit(self._run, fn, *args, **kwargs)

    def map(self, fn, *iterables, **kwargs):
        """Submit a new task to the thread pool."""
        raise NotImplementedError("SubmitBlockingExecutor.map is not implemented.")

    def shutdown(self, wait: bool = True, *, cancel_futures: bool = False):
        """Shutdown the executor."""
        self._executor.shutdown(wait=wait, cancel_futures=cancel_futures)
