package augmentclient

import (
	"fmt"
	"net/http"
	"time"

	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

const (
	ClientMeterName  string = "augmentclient"
	DefaultTimeUnits string = "ms"
)

// This file provides a set of utilities for creating OpenTelemetry metrics.
// These metrics are collected at the API boundary, where the HTTP requests
// are sent and received. To do this, we implement an HTTP RoundTripper
// that wraps the existing transport and adds metrics.
//
// One example of how to use this is in `tools/load_test/telemetry/telemetry.go`
type MeteredTransport struct {
	// A meter is a logical container for instruments. Each meter corresponds to
	// a specific instrumented library. In this case, the meter created represents
	// the container for instruments measuring the Augment Golang client.
	metric.Meter
	// Transport wrapped with metering. We call the `RoundTrip` method on this
	// transport and track metrics before sending the request and after the response
	// is received.
	base http.RoundTripper
	// A reference to the client that created this transport.
	client *Client

	// === Instruments ===
	// Round-trip time latency for each request-respond pair
	rttLatency metric.Float64Counter
}

func NewMeteredTransport(client *Client, base http.RoundTripper) http.RoundTripper {
	// Initialize the wrapper transport
	t := &MeteredTransport{
		Meter:  otel.GetMeterProvider().Meter(ClientMeterName),
		base:   base,
		client: client,
	}

	// Create the instruments for measuring telemetry data
	var err error
	t.rttLatency, err = t.Meter.Float64Counter(
		"augmentclient.http.client.duration",
		metric.WithDescription(
			fmt.Sprintf("Round-trip time latency for requests in %s", DefaultTimeUnits),
		),
		metric.WithUnit(DefaultTimeUnits),
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create `augmentclient.http.client.duration` instrument ")
		return base
	}

	return t
}

// Implement the RoundTripper interface. We perform measurement setup before the RoundTrip
// is executed (e.g., latency measurement start time), and construct + populate the telemetry
// data after the RoundTrip is complete.
func (t *MeteredTransport) RoundTrip(req *http.Request) (res *http.Response, err error) {
	var startTime time.Time

	// Measure the latency once the RoundTrip has finished
	defer func() {
		latencyMs := float64(time.Since(startTime)) / float64(time.Millisecond)

		// Get arguments for instruments. All instruments share the same attribute set
		// (independent variables), so we only need to retrieve it once
		ctx := req.Context()
		attrSet := t.GetHttpReqResAttrSet(req, res)
		withAttrSet := metric.WithAttributeSet(attrSet)

		// Add measurements to instruments
		t.rttLatency.Add(ctx, latencyMs, withAttrSet)
	}()

	// Assign to these variables for use in defer
	startTime = time.Now()
	res, err = t.base.RoundTrip(req)
	return res, err
}

// Get all of the independent variables, or OTel attributes, for a given HTTP request/response
func (t *MeteredTransport) GetHttpReqResAttrSet(req *http.Request, res *http.Response) attribute.Set {
	attrs := []attribute.KeyValue{
		attribute.String("augmentclient.session_id", req.Header.Get("x-request-session-id")),
		attribute.String("augmentclient.request_id", req.Header.Get("x-request-id")),
		attribute.String("augmentclient.http.request.method", req.Method),
		attribute.String("augmentclient.http.request.path", req.URL.Path),
		attribute.String("augmentclient.name", t.client.Name),
	}
	if res != nil {
		attrs = append(attrs, attribute.Int64("augmentclient.http.response.status_code", int64(res.StatusCode)))
	}
	return attribute.NewSet(attrs...)
}
