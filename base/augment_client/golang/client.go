package augmentclient

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	publicpb "github.com/augmentcode/augment/services/api_proxy/public_api"
	"github.com/google/uuid"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// Syntactic sugar for unwrapping the protobuf reflective type from the
// `DoProto` method for use in the client-facing functions
func unwrapHttpProto[T proto.Message](
	msg proto.Message, err error, res *http.Response,
) (T, error, *http.Response) {
	if err != nil {
		var t T // Instantiate a default value of the response type
		return t, err, res
	}
	return msg.(T), err, res
}

// ===========================
// Public API Client Functions
// ===========================
// The below functions are the public API for the client. They are wrappers around the
// `DoProto` method that provide a more user-friendly interface. See `public_api.proto`
// for the specific gRPCs that are exposed, their endpoints, and their request/response types.
//
// If an HTTP response is returned with a non-200 status code, then error will be non-nil.
//
// Note that the final return value for each function is the original HTTP response, with
// the body already read. This is useful for debugging or in case there is special handling
// of errors. For most consumers of this API, there is no need to use the last value.
//
// responseMsg, err, _ := client.BatchUpload(requestMsg)
//
// Example usage:
//  c = client.New(
//	    "https://dogfood.api.augmentcode.com",
//	    []rune("AUGMENT_TOKEN"),
//	    client.WithTimeout(time.Duration(60) * time.Second),
//	)
//  requestMsg := &publicpb.BatchUploadRequest{}
//  responseMsg, err, _ := c.BatchUpload(requestMsg)

type Client struct {
	// A user-defined name for the client. If not set, will default to `client-{sessionID}`.
	// Note that this is only used for client-side metrics, metadata, and logging. It is not
	// safe to use this for anything else, such as client-server interactions
	Name string

	url *url.URL
	// Avoid accidentally printing plaintext token until we have secret strings
	token []rune
	// Timeout for an individual request.
	timeout time.Duration
	// Maximum number of times to retry a request.
	maxRetries int
	// Minimum backoff between retries.
	retryMinBackoff time.Duration
	// Maximum backoff between retries (backoff increases expontentially).
	retryMaxBackoff time.Duration
	sessionId       uuid.UUID
	userAgent       string

	// The HTTP client used to send requests.
	httpClient *http.Client

	// Methods
	DoProto func(endpoint string, pbMsg proto.Message) (proto.Message, error, *http.Response)
}

const DefaultUserAgent = "api_proxy_client/0 (Golang)"

type ClientOptions func(*Client)

func New(apiBaseUrl string, token []rune, options ...ClientOptions) *Client {
	u, _ := url.Parse(apiBaseUrl)
	sessionId := uuid.New()
	client := &Client{
		Name:            fmt.Sprintf("client-%s", sessionId),
		url:             u,
		token:           token,
		timeout:         time.Duration(60) * time.Second,
		maxRetries:      0, // Disable retries by default.
		retryMinBackoff: time.Duration(10) * time.Millisecond,
		retryMaxBackoff: time.Duration(1) * time.Second,
		sessionId:       sessionId,
		userAgent:       DefaultUserAgent,
		httpClient:      nil, // Create a new retry client later
	}
	for _, option := range options {
		option(client)
	}

	// Modifications to the client before returning a new instance
	client.url.Path = strings.TrimSuffix(client.url.Path, "/")
	client.DoProto = client._DoProto

	// Additional modifications to a client that come by default
	// Here, we configure the retries in the base client
	withRetryClient()(client)
	// Metrics need to be added after retrys are configured so measurements
	// gathered are not duplicated across retries of the same request
	withMetricsTransport()(client)
	return client
}

func (c *Client) BatchUpload(msg *publicpb.BatchUploadRequest) (*publicpb.BatchUploadResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.BatchUploadResponse](c.DoProto("batch-upload", msg))
}

func (c *Client) Chat(msg *publicpb.ChatRequest) (*publicpb.ChatResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ChatResponse](c.DoProto("chat", msg))
}

func (c *Client) ChatStream(msg *publicpb.ChatRequest) (*publicpb.ChatResponse, error, *http.Response) {
	// TODO: Handle stream proto message
	return unwrapHttpProto[*publicpb.ChatResponse](c.DoProto("chat-stream", msg))
}

func (c *Client) GenerateCommitMessageStream(msg *publicpb.GenerateCommitMessageRequest) (*publicpb.GenerateCommitMessageResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GenerateCommitMessageResponse](c.DoProto("generate-commit-message-stream", msg))
}

func (c *Client) InstructionStream(msg *publicpb.InstructionRequest) (*publicpb.InstructionResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.InstructionResponse](c.DoProto("instruction-stream", msg))
}

func (c *Client) SmartPasteStream(msg *publicpb.InstructionRequest) (*publicpb.InstructionResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.InstructionResponse](c.DoProto("smart-paste-stream", msg))
}

func (c *Client) CheckpointBlobs(msg *publicpb.CheckpointBlobsRequest) (*publicpb.CheckpointBlobsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CheckpointBlobsResponse](c.DoProto("checkpoint-blobs", msg))
}

func (c *Client) Completion(msg *publicpb.CompletionRequest) (*publicpb.CompletionResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CompletionResponse](c.DoProto("completion", msg))
}

func (c *Client) ResolveCompletionsRpc(msg *publicpb.ResolveCompletions) (*publicpb.CompletionResolution, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CompletionResolution](c.DoProto("resolve-completions", msg))
}

func (c *Client) Edit(msg *publicpb.EditRequest) (*publicpb.EditResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.EditResponse](c.DoProto("edit", msg))
}

func (c *Client) NextEditStream(msg *publicpb.NextEditRequest) (*publicpb.NextEditResponse, error, *http.Response) {
	// TODO: Handle stream proto message
	return unwrapHttpProto[*publicpb.NextEditResponse](c.DoProto("next-edit-stream", msg))
}

func (c *Client) NextEditSessionEvent(msg *publicpb.NextEditSessionEventBatch) (error, *http.Response) {
	_, err, res := c.DoProto("record-next-edit-session-event", msg)
	return err, res
}

func (c *Client) OnboardingSessionEvent(msg *publicpb.OnboardingSessionEventBatch) (error, *http.Response) {
	_, err, res := c.DoProto("record-onboarding-session-event", msg)
	return err, res
}

func (c *Client) ResolveNextEdit(msg *publicpb.NextEditResolutionBatch) (error, *http.Response) {
	_, err, res := c.DoProto("resolve-next-edit", msg)
	return err, res
}

func (c *Client) ResolveEdit(msg *publicpb.EditResolution) (error, *http.Response) {
	_, err, res := c.DoProto("resolve-edit", msg)
	return err, res
}

func (c *Client) ResolveInstruction(msg *publicpb.InstructionResolution) (error, *http.Response) {
	_, err, res := c.DoProto("resolve-instruction", msg)
	return err, res
}

func (c *Client) ResolveSmartPaste(msg *publicpb.SmartPasteResolution) (error, *http.Response) {
	_, err, res := c.DoProto("resolve-smart-paste", msg)
	return err, res
}

func (c *Client) RecordPreferenceSample(msg *publicpb.PreferenceSample) (error, *http.Response) {
	_, err, res := c.DoProto("record-preference-sample", msg)
	return err, res
}

func (c *Client) FindMissing(msg *publicpb.FindMissingRequest) (*publicpb.FindMissingResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.FindMissingResponse](c.DoProto("find-missing", msg))
}

func (c *Client) GetModels() (*publicpb.GetModelsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetModelsResponse](c.DoProto("get-models", &publicpb.GetModelsRequest{}))
}

func (c *Client) Memorize(msg *publicpb.MemorizeRequest) (*publicpb.MemorizeResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.MemorizeResponse](c.DoProto("memorize", msg))
}

func (c *Client) ReportError(msg *publicpb.ReportErrorRequest) (error, *http.Response) {
	_, err, res := c.DoProto("report-error", msg)
	return err, res
}

func (c *Client) ReportClientMetrics(msg *publicpb.ClientMetricsRequest) (error, *http.Response) {
	_, err, res := c.DoProto("client-metrics", msg)
	return err, res
}

func (c *Client) ReportFeatureVector(msg *publicpb.ReportFeatureVectorRequest) (error, *http.Response) {
	_, err, res := c.DoProto("report-feature-vector", msg)
	return err, res
}

func (c *Client) SendCompletionFeedback(msg *publicpb.CompletionFeedback) (error, *http.Response) {
	_, err, res := c.DoProto("completion-feedback", msg)
	return err, res
}

func (c *Client) SendChatFeedback(msg *publicpb.ChatFeedback) (error, *http.Response) {
	_, err, res := c.DoProto("chat-feedback", msg)
	return err, res
}

func (c *Client) SendNextEditFeedback(msg *publicpb.NextEditFeedback) (error, *http.Response) {
	_, err, res := c.DoProto("next-edit-feedback", msg)
	return err, res
}

func (c *Client) ListExternalSourceTypes() (*publicpb.ListExternalSourceTypesResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListExternalSourceTypesResponse](c.DoProto("list-external-source-types", &publicpb.ListExternalSourceTypesRequest{}))
}

func (c *Client) SearchExternalSources(msg *publicpb.SearchExternalSourcesRequest) (*publicpb.SearchExternalSourcesResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.SearchExternalSourcesResponse](c.DoProto("search-external-sources", msg))
}

func (c *Client) GetImplicitExternalSources(msg *publicpb.GetImplicitExternalSourcesRequest) (*publicpb.GetImplicitExternalSourcesResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetImplicitExternalSourcesResponse](c.DoProto("get-implicit-external-sources", msg))
}

func (c *Client) SendClientCompletionTimeline(msg *publicpb.ClientCompletionTimelineRequest) (error, *http.Response) {
	_, err, res := c.DoProto("client-completion-timelines", msg)
	return err, res
}

// User Secrets methods

func (c *Client) UpsertUserSecret(msg *publicpb.UpsertUserSecretRequest) (*publicpb.UpsertUserSecretResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.UpsertUserSecretResponse](c.DoProto("user-secrets/upsert", msg))
}

func (c *Client) GetUserSecret(msg *publicpb.GetUserSecretRequest) (*publicpb.GetUserSecretResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetUserSecretResponse](c.DoProto("user-secrets/get", msg))
}

func (c *Client) ListUserSecrets(msg *publicpb.ListUserSecretsRequest) (*publicpb.ListUserSecretsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListUserSecretsResponse](c.DoProto("user-secrets/list", msg))
}

func (c *Client) DeleteUserSecret(msg *publicpb.DeleteUserSecretRequest) (*publicpb.DeleteUserSecretResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.DeleteUserSecretResponse](c.DoProto("user-secrets/delete", msg))
}

func (c *Client) SaveChat(msg *publicpb.SaveChatRequest) (*publicpb.SaveChatResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.SaveChatResponse](c.DoProto("save-chat", msg))
}

func (c *Client) RecordRequestEvents(msg *publicpb.RecordRequestEventsRequest) (error, *http.Response) {
	_, err, res := c.DoProto("record-request-events", msg)
	return err, res
}

func (c *Client) RecordSessionEvents(msg *publicpb.RecordSessionEventsRequest) (error, *http.Response) {
	_, err, res := c.DoProto("record-session-events", msg)
	return err, res
}

func (c *Client) LLMGenerate(msg *publicpb.LLMGenerateRequest) (*publicpb.LLMGenerateResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.LLMGenerateResponse](c.DoProto("agents/llm-generate", msg))
}

func (c *Client) CodebaseRetrieval(msg *publicpb.CodebaseRetrievalRequest) (*publicpb.CodebaseRetrievalResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CodebaseRetrievalResponse](c.DoProto("agents/codebase-retrieval", msg))
}

func (c *Client) EditFile(msg *publicpb.EditFileRequest) (*publicpb.EditFileResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.EditFileResponse](c.DoProto("agents/edit-file", msg))
}

func (c *Client) ListRemoteTools(msg *publicpb.ListRemoteToolsRequest) (*publicpb.ListRemoteToolsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListRemoteToolsResponse](c.DoProto("agents/list-remote-tools", msg))
}

func (c *Client) RunRemoteTool(msg *publicpb.RunRemoteToolRequest) (*publicpb.RunRemoteToolResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.RunRemoteToolResponse](c.DoProto("agents/run-remote-tool", msg))
}

func (c *Client) CheckToolSafety(msg *publicpb.CheckToolSafetyRequest) (*publicpb.CheckToolSafetyResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CheckToolSafetyResponse](c.DoProto("agents/check-tool-safety", msg))
}

func (c *Client) RevokeToolAccess(msg *publicpb.RevokeToolAccessRequest) (*publicpb.RevokeToolAccessResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.RevokeToolAccessResponse](c.DoProto("agents/revoke-tool-access", msg))
}

func (c *Client) TestToolConnection(msg *publicpb.TestToolConnectionRequest) (*publicpb.TestToolConnectionResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.TestToolConnectionResponse](c.DoProto("agents/test-tool-connection", msg))
}

func (c *Client) CreateRemoteAgent(msg *publicpb.CreateRemoteAgentRequest) (*publicpb.CreateRemoteAgentResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CreateRemoteAgentResponse](c.DoProto("remote-agents/create", msg))
}

func (c *Client) DeleteRemoteAgent(msg *publicpb.DeleteRemoteAgentRequest) (*publicpb.DeleteRemoteAgentResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.DeleteRemoteAgentResponse](c.DoProto("remote-agents/delete", msg))
}

func (c *Client) ListRemoteAgents(msg *publicpb.ListRemoteAgentsRequest) (*publicpb.ListRemoteAgentsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListRemoteAgentsResponse](c.DoProto("remote-agents/list", msg))
}

func (c *Client) ListRemoteAgentsStream(msg *publicpb.ListRemoteAgentsStreamRequest) (*publicpb.ListRemoteAgentsStreamResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListRemoteAgentsStreamResponse](c.DoProto("remote-agents/list-stream", msg))
}

func (c *Client) GetRemoteAgentChatHistory(msg *publicpb.GetRemoteAgentChatHistoryRequest) (*publicpb.GetRemoteAgentChatHistoryResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetRemoteAgentChatHistoryResponse](c.DoProto("remote-agents/get-chat-history", msg))
}

func (c *Client) GetRemoteAgentHistoryStream(msg *publicpb.GetRemoteAgentHistoryStreamRequest) (*publicpb.GetRemoteAgentHistoryStreamResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetRemoteAgentHistoryStreamResponse](c.DoProto("remote-agents/agent-history-stream", msg))
}

func (c *Client) RemoteAgentChat(msg *publicpb.RemoteAgentChatRequest) (*publicpb.RemoteAgentChatResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.RemoteAgentChatResponse](c.DoProto("remote-agents/chat", msg))
}

func (c *Client) InterruptRemoteAgent(msg *publicpb.InterruptRemoteAgentRequest) (*publicpb.InterruptRemoteAgentResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.InterruptRemoteAgentResponse](c.DoProto("remote-agents/interrupt", msg))
}

func (c *Client) RemoteAgentAddSSHKey(msg *publicpb.RemoteAgentAddSSHKeyRequest) (*publicpb.RemoteAgentAddSSHKeyResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.RemoteAgentAddSSHKeyResponse](c.DoProto("remote-agents/add-ssh-key", msg))
}

func (c *Client) ResumeRemoteAgent(msg *publicpb.RemoteAgentResumeRequest) (error, *http.Response) {
	_, err, res := c.DoProto("remote-agents/resume", msg)
	return err, res
}

func (c *Client) ResumeHintRemoteAgent(msg *publicpb.RemoteAgentResumeHintRequest) (error, *http.Response) {
	_, err, res := c.DoProto("remote-agents/resume-hint", msg)
	return err, res
}

func (c *Client) PauseRemoteAgent(msg *publicpb.RemoteAgentPauseRequest) (error, *http.Response) {
	_, err, res := c.DoProto("remote-agents/pause", msg)
	return err, res
}

func (c *Client) UpdateRemoteAgent(msg *publicpb.UpdateRemoteAgentRequest) (*publicpb.UpdateRemoteAgentResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.UpdateRemoteAgentResponse](c.DoProto("remote-agents/update", msg))
}

func (c *Client) RemoteAgentGenerateSummary(msg *publicpb.RemoteAgentGenerateSummaryRequest) (*publicpb.RemoteAgentGenerateSummaryResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.RemoteAgentGenerateSummaryResponse](c.DoProto("remote-agents/generate-summary", msg))
}

func (c *Client) ListGithubSetupScripts(msg *publicpb.ListGithubSetupScriptsRequest) (*publicpb.ListGithubSetupScriptsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListGithubSetupScriptsResponse](c.DoProto("remote-agents/list-github-setup-scripts", msg))
}

func (c *Client) ReadGithubSetupScript(msg *publicpb.ReadGithubSetupScriptRequest) (*publicpb.ReadGithubSetupScriptResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ReadGithubSetupScriptResponse](c.DoProto("remote-agents/read-github-setup-script", msg))
}

func (c *Client) RemoteAgentWorkspaceLogs(msg *publicpb.RemoteAgentWorkspaceLogsRequest) (*publicpb.RemoteAgentWorkspaceLogsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.RemoteAgentWorkspaceLogsResponse](c.DoProto("remote-agents/logs", msg))
}

func (c *Client) AgentWorkspaceReportStatus(msg *publicpb.AgentWorkspaceReportStatusRequest) (*publicpb.AgentWorkspaceReportStatusResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.AgentWorkspaceReportStatusResponse](c.DoProto("agent-workspace/report-status", msg))
}

func (c *Client) AgentWorkspaceReportChatHistory(msg *publicpb.AgentWorkspaceReportChatHistoryRequest) (*publicpb.AgentWorkspaceReportChatHistoryResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.AgentWorkspaceReportChatHistoryResponse](c.DoProto("agent-workspace/report-chat-history", msg))
}

func (c *Client) AgentWorkspacePollUpdate(msg *publicpb.AgentWorkspacePollUpdateRequest) (*publicpb.AgentWorkspacePollUpdateResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.AgentWorkspacePollUpdateResponse](c.DoProto("agent-workspace/poll-update", msg))
}

func (c *Client) AgentWorkspaceStream(msg *publicpb.AgentWorkspaceStreamRequest) (*publicpb.AgentWorkspaceStreamResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.AgentWorkspaceStreamResponse](c.DoProto("agent-workspace/stream", msg))
}

func (c *Client) AgentWorkspaceReportSetupLogs(msg *publicpb.AgentWorkspaceReportSetupLogsRequest) (*publicpb.AgentWorkspaceReportSetupLogsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.AgentWorkspaceReportSetupLogsResponse](c.DoProto("agent-workspace/report-setup-logs", msg))
}

func (c *Client) GetSubscriptionInfo(msg *publicpb.GetSubscriptionInfoRequest) (*publicpb.GetSubscriptionInfoResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetSubscriptionInfoResponse](c.DoProto("subscription-info", msg))
}

func (c *Client) RevokeCurrentUserTokens(msg *publicpb.RevokeCurrentUserTokensRequest) (*publicpb.RevokeCurrentUserTokensResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.RevokeCurrentUserTokensResponse](c.DoProto("revoke-current-user-tokens", msg))
}

// GitHub related methods

func (c *Client) ListGithubReposForAuthenticatedUser(msg *publicpb.ListGithubReposForAuthenticatedUserRequest) (*publicpb.ListGithubReposForAuthenticatedUserResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListGithubReposForAuthenticatedUserResponse](c.DoProto("github/list-repos", msg))
}

func (c *Client) IsUserGithubConfigured(msg *publicpb.IsUserGithubConfiguredRequest) (*publicpb.IsUserGithubConfiguredResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.IsUserGithubConfiguredResponse](c.DoProto("github/is-user-configured", msg))
}

func (c *Client) ListGithubRepoBranches(msg *publicpb.ListGithubRepoBranchesRequest) (*publicpb.ListGithubRepoBranchesResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListGithubRepoBranchesResponse](c.DoProto("github/list-branches", msg))
}

func (c *Client) CreatePullRequest(msg *publicpb.CreatePullRequestRequest) (*publicpb.CreatePullRequestResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CreatePullRequestResponse](c.DoProto("github/create-pull-request", msg))
}

func (c *Client) GetGithubRepo(msg *publicpb.GetGithubRepoRequest) (*publicpb.GetGithubRepoResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetGithubRepoResponse](c.DoProto("github/get-repo", msg))
}

// ===========================
// Remote Agent Actions (Triggers)
// ===========================

func (c *Client) CreateTrigger(msg *publicpb.CreateTriggerRequest) (*publicpb.CreateTriggerResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.CreateTriggerResponse](c.DoProto("remote-agent-actions/triggers/create", msg))
}

func (c *Client) ListTriggers(msg *publicpb.ListTriggersRequest) (*publicpb.ListTriggersResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListTriggersResponse](c.DoProto("remote-agent-actions/triggers/list", msg))
}

func (c *Client) UpdateTrigger(msg *publicpb.UpdateTriggerRequest) (*publicpb.UpdateTriggerResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.UpdateTriggerResponse](c.DoProto("remote-agent-actions/triggers/update", msg))
}

func (c *Client) DeleteTrigger(msg *publicpb.DeleteTriggerRequest) (*publicpb.DeleteTriggerResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.DeleteTriggerResponse](c.DoProto("remote-agent-actions/triggers/delete", msg))
}

func (c *Client) GetTriggerExecutions(msg *publicpb.GetTriggerExecutionsRequest) (*publicpb.GetTriggerExecutionsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetTriggerExecutionsResponse](c.DoProto("remote-agent-actions/triggers/executions", msg))
}

func (c *Client) GetMatchingEntities(msg *publicpb.GetMatchingEntitiesRequest) (*publicpb.GetMatchingEntitiesResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetMatchingEntitiesResponse](c.DoProto("remote-agent-actions/triggers/matching-entities", msg))
}

func (c *Client) DismissEntity(msg *publicpb.DismissEntityRequest) (*publicpb.DismissEntityResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.DismissEntityResponse](c.DoProto("remote-agent-actions/triggers/dismiss-entity", msg))
}

func (c *Client) ExecuteTriggerManually(msg *publicpb.ExecuteTriggerManuallyRequest) (*publicpb.ExecuteTriggerManuallyResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ExecuteTriggerManuallyResponse](c.DoProto("remote-agent-actions/triggers/execute-manually", msg))
}

func (c *Client) ExecuteManualAgent(msg *publicpb.ExecuteManualAgentRequest) (*publicpb.ExecuteManualAgentResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ExecuteManualAgentResponse](c.DoProto("remote-agent-actions/execute-manual-agent", msg))
}

func (c *Client) GetEntityDetails(msg *publicpb.GetEntityDetailsRequest) (*publicpb.GetEntityDetailsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.GetEntityDetailsResponse](c.DoProto("remote-agent-actions/get-entity-details", msg))
}

func (c *Client) ListPullRequestFiles(msg *publicpb.ListPullRequestFilesRequest) (*publicpb.ListPullRequestFilesResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ListPullRequestFilesResponse](c.DoProto("remote-agent-actions/list-pr-files", msg))
}

func (c *Client) ReadNotifications(msg *publicpb.ReadNotificationsRequest) (*publicpb.ReadNotificationsResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.ReadNotificationsResponse](c.DoProto("notifications/read", msg))
}

func (c *Client) MarkNotificationAsRead(msg *publicpb.MarkNotificationAsReadRequest) (*publicpb.MarkNotificationAsReadResponse, error, *http.Response) {
	return unwrapHttpProto[*publicpb.MarkNotificationAsReadResponse](c.DoProto("notifications/mark-as-read", msg))
}

func WithName(name string) ClientOptions {
	return func(client *Client) {
		client.Name = name
	}
}

func WithUrl(u string) ClientOptions {
	return func(client *Client) {
		apiBaseUrl, _ := url.Parse(u)
		client.url = apiBaseUrl
	}
}

func WithToken(token []rune) ClientOptions {
	return func(client *Client) {
		client.token = token
	}
}

func WithTimeout(timeout time.Duration) ClientOptions {
	return func(client *Client) {
		client.timeout = timeout
	}
}

func WithMaxRetries(maxRetries int) ClientOptions {
	return func(client *Client) {
		client.maxRetries = maxRetries
	}
}

func WithRetryMinBackoff(retryMinBackoff time.Duration) ClientOptions {
	return func(client *Client) {
		client.retryMinBackoff = retryMinBackoff
	}
}

func WithRetryMaxBackoff(retryMaxBackoff time.Duration) ClientOptions {
	return func(client *Client) {
		client.retryMaxBackoff = retryMaxBackoff
	}
}

func WithUserAgent(userAgent string) ClientOptions {
	return func(client *Client) {
		client.userAgent = userAgent
	}
}

func WithSessionId(sessionId uuid.UUID) ClientOptions {
	return func(client *Client) {
		client.sessionId = sessionId
	}
}

func WithSessionIdString(sessionId string) ClientOptions {
	return func(client *Client) {
		client.sessionId, _ = uuid.Parse(sessionId)
	}
}

func withRetryClient() ClientOptions {
	return func(c *Client) {
		// Retrying with the standard HTTP client is hard because it closes the request body for you. We
		// use go-retryablehttp to get around this.
		// The default backoff policy is an exponential backoff.
		// The default retry policy retries connection and server errors. Implementation is here:
		// https://github.com/hashicorp/go-retryablehttp/blob/main/client.go#L484
		retryClient := retryablehttp.NewClient()
		retryClient.HTTPClient.Timeout = c.timeout
		retryClient.RetryMax = c.maxRetries
		retryClient.RetryWaitMin = c.retryMinBackoff
		retryClient.RetryWaitMax = c.retryMaxBackoff
		// PassthroughErrorHandler returns the result of the underlying http.Do request without any
		// additional processing. Without this, we wouldn't get the response body back from requests
		// that resulted in non-200 status codes.
		retryClient.ErrorHandler = retryablehttp.PassthroughErrorHandler
		// The default logging behavior is very verbose. Override it to just log when something is being
		// retried.
		retryClient.Logger = nil
		retryClient.RequestLogHook = func(_ retryablehttp.Logger, req *http.Request, attempt int) {
			if attempt > 0 {
				log.Debug().Msgf(
					"Retrying %s %s request (attempt %d/%d)",
					req.Method, req.URL.String(), attempt+1, c.maxRetries+1,
				)
			}
		}
		c.httpClient = retryClient.StandardClient()
	}
}

// The metrics transport will be a no-op if the OpenTelemetry API is not dependency-injected
// by the application running this client. This prevents performance issues when the client is
// used in a non-OpenTelemetry environment.
//
// For an example of how to initialize the OpenTelemetry API by dependency-injecting from the SDK,
// see `tools/load_test/telemetry/telemetry.go`
func withMetricsTransport() ClientOptions {
	return func(c *Client) {
		// Wrap existing transport in a new one that adds OpenTelemetry headers and metrics
		c.httpClient.Transport = NewMeteredTransport(c, c.httpClient.Transport)
	}
}

// ============================
// Lower-level public functions
// ============================
//
// The below functions are exposed in case finer-grained control is needed
// than the RPC wrapper functions above provide. The lifecycle from here is:
//
// 0. (On executable startup in `apireflect.go`) Discover the endpoints and their types
// 1. Construct an HTTP request from a protobuf message (reflective type)
// 2. Send the HTTP request
// 3. Read the HTTP response into a protobuf message (reflective type)
//
// Example use case: generation of the HTTP request from protobuf happens
//     separately from the sending of the HTTP request itself.

// Turns a protobuf message for a specific endpoint into an HTTP request.
// The reflective type of the message is inferred from the endpoint
// discovery process that happens on startup in `apireflect.go`.
func (c *Client) ProtoToRequest(endpoint string, msg proto.Message) (*http.Request, error) {
	endpoint = formatEndpoint(endpoint)

	// Validate that the endpoint is being passed the right message type
	info, ok := endpointRegistry[endpoint]
	if !ok {
		return nil, fmt.Errorf("attempting to access undiscovered endpoint %s", endpoint)
	}
	if err := checkProtoMessageType(info.reqT.Descriptor(), msg); err != nil {
		return nil, fmt.Errorf("invalid request type for endpoint %s: %s", endpoint, err)
	}

	// Construct the endpoint URL
	endpointUrl := url.URL{
		Scheme: c.url.Scheme,
		Host:   c.url.Host,
		Path:   c.url.Path + "/" + endpoint,
	}

	// Turn the proto message into a json string
	json, err := protojson.MarshalOptions{
		// This needs to be true for requests with no added or deleted blobs, because api-proxy's
		// JSON parsing treats them as required.
		EmitUnpopulated: true,
		// api-proxy's JSON parsing expects snake_case.
		UseProtoNames: true,
	}.Marshal(msg)
	if err != nil {
		return nil, err
	}

	// Construct an HTTP POST request
	req, err := http.NewRequest(info.method, endpointUrl.String(), bytes.NewBuffer(json))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-request-session-id", c.sessionId.String())
	req.Header.Set("x-request-id", uuid.New().String())
	req.Header.Set("x-api-version", "1")
	req.Header.Set("Authorization", "Bearer "+string(c.token))

	return req, nil
}

// Turns an HTTP response into a protobuf message. The reflective type of the message
// is inferred from the endpoint discovery process that happens on startup
// in `apireflect.go`.
func (c *Client) ResponseToProto(res *http.Response) (proto.Message, error) {
	defer res.Body.Close()
	// Attempt to parse out an endpoint by removing the client's API base path
	endpoint := formatEndpoint(strings.TrimPrefix(res.Request.URL.Path, c.url.Path))
	info, ok := endpointRegistry[endpoint]
	if !ok {
		return nil, fmt.Errorf("attempting to access undiscovered endpoint %s", endpoint)
	}

	msg := info.resT.New().Interface()

	// Read the response. We check for 200 after this because the
	// payload will contain additional information about potential errors
	resBody, err := io.ReadAll(res.Body)
	if err != nil { // Reading response body errored
		return msg, fmt.Errorf("failed to read response body: %s", err)
	} else if res.StatusCode != 200 { // Non-200 response
		return msg, fmt.Errorf(
			"%s %s failed with status code %d: %s",
			res.Request.Method, res.Request.URL, res.StatusCode, string(resBody),
		)
	}

	// Deserialize the response body into a proto message
	if (protojson.UnmarshalOptions{
		AllowPartial: true,
	}.Unmarshal(resBody, msg) != nil) {
		return nil, fmt.Errorf("failed to parse response body: %s", resBody)
	}

	return msg, nil
}

// Sends an HTTP request and returns the response. This is a wrapper around
// the `Do` method on the client's HTTP client.
func (c *Client) DoHttp(req *http.Request) (*http.Response, error) {
	return c.httpClient.Do(req)
}

// Sends a protobuf message to an endpoint as JSON and returns the response
// parsed into a protobuf message of reflective type. Note that for compiler
// level type safety, the protobuf message must be of the correct type for the
// endpoint and requires a type assertion after this.
func (c *Client) _DoProto(endpoint string, pb_msg proto.Message) (proto.Message, error, *http.Response) {
	req, err := c.ProtoToRequest(endpoint, pb_msg)
	if err != nil {
		return nil, err, nil
	}

	res, err := c.DoHttp(req)
	if err != nil {
		return nil, err, nil
	}

	protoRes, err := c.ResponseToProto(res)
	if err != nil {
		return nil, err, res
	}

	return protoRes, nil, res
}
