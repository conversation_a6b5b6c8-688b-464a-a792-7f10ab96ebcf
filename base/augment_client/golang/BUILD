load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

OTEL_DEPS = [
    "@io_opentelemetry_go_otel//:otel",
    "@io_opentelemetry_go_otel//attribute",
    "@io_opentelemetry_go_otel_metric//:metric",
]

go_library(
    name = "client_go",
    srcs = [
        "apireflect.go",
        "client.go",
        "metrics.go",
    ],
    importpath = "github.com/augmentcode/augment/base/augmentclient",
    visibility = [
        "//tools/load_test:__subpackages__",
    ],
    deps = [
        "//services/api_proxy:public_api_go_proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_hashicorp_go_retryablehttp//:go-retryablehttp",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_genproto_googleapis_api//annotations:go_default_library",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//reflect/protoreflect",
        "@org_golang_google_protobuf//reflect/protoregistry",
    ] + OTEL_DEPS,
)

go_test(
    name = "client_go_test",
    srcs = ["client_test.go"],
    embed = [
        ":client_go",
    ],
    importpath = "github.com/augmentcode/augment/base/augmentclient",
    deps = [
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_protobuf//types/known/emptypb",
    ],
)
