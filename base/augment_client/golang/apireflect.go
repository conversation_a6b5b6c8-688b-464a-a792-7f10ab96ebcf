package augmentclient

import (
	"fmt"
	"strings"

	public_api "github.com/augmentcode/augment/services/api_proxy/public_api"
	"github.com/rs/zerolog/log"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
)

// Removes leading and trailing slashes from an endpoint
func formatEndpoint(endpoint string) string {
	return strings.Trim(endpoint, "/")
}

// Stores information about an endpoint to use for message serialization
// and deserialization to/from HTTP+REST+JSON requests
type endpointInfo struct {
	endpoint string                   // REST endpoint on top of base API path, without leading/trailing slashes
	method   string                   // HTTP method (GET, POST, etc.)
	grpcName string                   // Name of the gRPC method, e.g. `BatchUpload`
	reqT     protoreflect.MessageType // The type of the request message
	resT     protoreflect.MessageType // The type of the response message
}

// Extract the `google.api.http` proto annotation from a method descriptor
func rpcMethodToHttpRule(methodDesc protoreflect.MethodDescriptor) (protoreflect.Message, error) {
	var httpRule protoreflect.Message = nil

	// Iterate through all options on the method descriptor
	// Early return if we find the http rule
	methodDesc.Options().ProtoReflect().Range(
		func(k protoreflect.FieldDescriptor, v protoreflect.Value) bool {
			if k.FullName() == "google.api.http" {
				httpRule = v.Message()
				return false
			}
			return true
		},
	)

	// If we did not find the http rule, return an error
	if httpRule == nil || !httpRule.IsValid() {
		return nil, fmt.Errorf("no http rule found for method %s", methodDesc.FullName())
	}
	return httpRule, nil
}

// Parses out the endpoint information from a method descriptor
func rpcMethodToEndpoint(methodDesc protoreflect.MethodDescriptor) (method, endpoint string, err error) {
	log.Printf("Parsing method %s", methodDesc.FullName())
	// Parse out http rule from the method descriptor
	httpRule, err := rpcMethodToHttpRule(methodDesc)
	if err != nil {
		return "", "", err
	}

	// Get protobuf descriptors for http rule
	httpDesc := httpRule.Descriptor()
	patternDesc := httpRule.WhichOneof(httpDesc.Oneofs().ByName("pattern"))

	// Get protobuf values for the options
	method = strings.ToUpper(string(patternDesc.Name()))
	endpoint = httpRule.Get(patternDesc).String()

	return method, endpoint, nil
}

// Iterates through an endpointInfo map and prints discovered type information
func logDiscoveredEndpoints(endpointToInfo map[string]endpointInfo) {
	for endpoint, info := range endpointToInfo {
		log.Printf(
			"Discovered endpoint %s %s (%s) => %s",
			info.method,
			endpoint,
			info.reqT.Descriptor().FullName(),
			info.resT.Descriptor().FullName(),
		)
	}
}

// Parses through the public api generated proto using reflection to discover
// all the services and RPC methods. We store these in a map to use for reflection
// later
func parseReflectiveEndpointInfo() (
	endpointToInfo map[string]endpointInfo,
	incompleteRpcMethods map[string]interface{},
) {
	// Below is the file descriptor generated for the public_api.proto file.
	// If the file is moved, renamed, etc., this reference to the generated golang
	// proto code will be changed. To find the relevant reference, generate the golang
	// proto stubs. This should be something like:
	//
	//     `bazel run //services/api_proxy:generate_<name>_stubs`
	//
	fDesc := public_api.File_services_api_proxy_public_api_proto
	svcDescs := fDesc.Services()

	endpointToInfo = make(map[string]endpointInfo)
	incompleteRpcMethods = make(map[string]interface{})
	// Iterate through all service definitions
	for i := range svcDescs.Len() {
		svcDesc := svcDescs.Get(i)
		log.Printf("Parsing service %s", svcDesc.FullName())

		// Iterate through all the methods in the service descriptor to see which ones
		// have HTTP endpoint annotations
		for j := range svcDesc.Methods().Len() {
			methodDesc := svcDesc.Methods().Get(j)

			// Retrieve endpoint information
			method, endpoint, err := rpcMethodToEndpoint(methodDesc)
			if err != nil {
				log.Printf("Skipping method %s: %s", methodDesc.FullName(), err)
				incompleteRpcMethods[string(methodDesc.FullName())] = nil
				continue
			}
			endpoint = formatEndpoint(endpoint)
			inTDesc, outTDesc := methodDesc.Input(), methodDesc.Output()

			// Retrieve the protoreflect.MessageType for the input and output types
			inT, err := protoregistry.GlobalTypes.FindMessageByName(inTDesc.FullName())
			outT, err := protoregistry.GlobalTypes.FindMessageByName(outTDesc.FullName())
			if err != nil {
				log.Printf("Could not find input/output types for method %s: %s", methodDesc.FullName(), err)
				continue
			}

			log.Printf("Discovered endpoint %s %s (%s) => %s", method, endpoint, inTDesc.FullName(), outTDesc.FullName())
			endpointToInfo[endpoint] = endpointInfo{
				endpoint: endpoint,
				method:   method,
				grpcName: string(methodDesc.Name()),
				reqT:     inT,
				resT:     outT,
			}
		}
	}

	logDiscoveredEndpoints(endpointToInfo)
	return endpointToInfo, incompleteRpcMethods
}

// Validates that a message descriptor and a proto message have the same reflective type
func checkProtoMessageType(desc protoreflect.MessageDescriptor, msg proto.Message) error {
	msgDesc := msg.ProtoReflect().Descriptor()

	// Fully-qualified names of protos are unique
	if msgDesc.FullName() != desc.FullName() {
		return fmt.Errorf("invalid proto message type. Expected %s, got %s",
			msgDesc.FullName(), desc.FullName())
	}
	return nil
}

var endpointRegistry, incompleteRpcMethods = parseReflectiveEndpointInfo()
