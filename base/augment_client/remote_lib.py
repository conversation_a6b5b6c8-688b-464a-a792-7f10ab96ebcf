import hashlib
import logging
import os
import time
from dataclasses import dataclass, field
from itertools import islice
from pathlib import Path, PurePath
from typing import Collection, Iterable, Iterator, Sequence, TypeVar

from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    BlobsJson,
    ClientException,
    GetModelsResponse,
    Language,
    Model,
    UploadContent,
)
from base.blob_names.python.blob_names import get_blob_name
from base.python.au_functools.wrappers import method_wrap

logger = logging.getLogger(__name__)

DocumentId = str


def hash_model_name(model_name: str) -> str:
    """Hash the model name using SHA256."""
    return hashlib.sha256(model_name.encode("utf-8")).hexdigest()


def get_model_info(
    get_models_response: GetModelsResponse, model_name: str | None
) -> Model:
    """Gets the model info by name from the response.

    Returns the default model if model name is None.
    """

    # Validate that all models have the same max blob size, since we assume
    # that a generic default model config works for completions, edits, etc.
    models = get_models_response.models
    assert models, "No models found from backend."
    logger.debug("Found remote models: %s", models)

    # The default model specified by the model-config is (a) currently
    # incorrect, (b) applies only for a completion model, and (c) will be
    # removed in the future. Completion models use the preifx/suffix size hints
    # contained within the config, while Edit models do not. For now, we'll
    # create a default model config that looks like the one specified by the
    # model-config, but without a name and with a large prefix/suffix size. The
    # completion manager is expected to adjust these sizes based on responses
    # from the backend.
    # TODO(jeff): completion_server.py rejects requests over max_prefix_char_count,
    # which is 3 * max_context_length. 10 * 1024 sits between the largest current
    # suggested_prefix_char_count and the smallest max_prefix_char_count
    # (even among deprecated models). In the future, this should be a system-constrained
    # (e.g. network/memory) limit, not a model-constrained limit.
    max_char_count = 10 * 1024
    if not model_name:
        return Model(
            name="",
            suggested_prefix_char_count=max_char_count,
            suggested_suffix_char_count=max_char_count,
        )

    # If we've specified a model name, then we should find it
    # NOTE(jeff): currently get_model returns a list of hashed names.
    # This may break in the future. For now, we match by hash or name,
    # and return the info by name always.
    for model_info in models:
        if (
            model_name == model_info.name
            or hash_model_name(model_name) == model_info.name
        ):
            return Model(
                name=model_name,
                suggested_prefix_char_count=model_info.suggested_prefix_char_count,
                suggested_suffix_char_count=model_info.suggested_suffix_char_count,
            )

    raise ValueError(
        f'Model "{model_name}" not found (hash: {hash_model_name(model_name)}). '
        f"Available models: {get_models_response.models}"
    )


@dataclass(frozen=True)
class BlobInfo:
    """Representation of a blob with useful information."""

    doc_id: str
    blob: UploadContent
    extension: str
    num_content_bytes: int


def _doc_to_blob_info(doc: UploadContent) -> BlobInfo:
    """Convert a document to a blob info."""

    doc_id = get_blob_name(doc.path_name, doc.content.encode("utf8"))
    extension = PurePath(doc.path_name).suffix.lower()
    num_content_bytes = len(doc.content.encode("utf-8"))
    return BlobInfo(
        doc_id,
        doc,
        extension,
        num_content_bytes,
    )


T = TypeVar("T")


def batched(items: Iterable[T], n: int) -> Iterator[Sequence[T]]:
    "Batch data into tuples of length n. The last batch may be shorter."
    # batched('ABCDEFG', 3) --> ABC DEF G
    if n < 1:
        raise ValueError("n must be at least one")
    it = iter(items)
    while batch := tuple(islice(it, n)):
        yield batch


def _batch_blobs(
    blobs: list[BlobInfo], max_batch_content_bytes: int, max_batch_items: int
) -> list[list[BlobInfo]]:
    """Groups blobs into batches according to the given limits.

    Args:
        blobs: The blobs to batch
        max_batch_content_bytes: The maximum total content size in a batch
        max_batch_items: The maximum number of blobs in a batch

    Returns:
        A list of non-empty batches, each of which is a list of blobs.
    """
    batches = []
    batch = []
    num_bytes = 0

    for blob_info in blobs:
        if (
            num_bytes + blob_info.num_content_bytes >= max_batch_content_bytes
            or len(batch) >= max_batch_items
        ):
            assert len(batch) > 0
            batches.append(batch)
            batch = []
            num_bytes = 0
        batch.append(blob_info)
        num_bytes += blob_info.num_content_bytes

    # Add last batch to batches
    if len(batch) > 0:
        batches.append(batch)

    return batches


@dataclass(frozen=True)
class AddDocsResponse:
    """Response from add_docs with successful and filtered documents."""

    successful_blobs: list[BlobInfo]
    """The list of documents that were successfully added."""
    filtered_by_id: list[BlobInfo]
    """The list of documents whose id has already been added."""
    filtered_by_extension: list[BlobInfo]
    """The list of documents that were filtered out by extension."""
    filtered_by_size: list[BlobInfo]
    """The list of documents that were filtered out by size."""
    nonindexed_blob_names: list[str]
    """The list of blob names that are not indexed.

    If `warn_on_indexing_timeout` is False (default), all blob names are expected to be
    indexed and this list will be empty.
    """


def _exclude_ids(blobs: list[BlobInfo], ids: set[str]):
    good_blobs = [blob for blob in blobs if blob.doc_id not in ids]
    bad_blobs = [blob for blob in blobs if blob.doc_id in ids]
    return good_blobs, bad_blobs


def _include_extensions(blobs: list[BlobInfo], extensions: set[str]):
    good_blobs = [blob for blob in blobs if blob.extension in extensions]
    bad_blobs = [blob for blob in blobs if blob.extension not in extensions]
    return good_blobs, bad_blobs


def _filter_by_max_size(blobs: list[BlobInfo], max_blob_content_bytes: int):
    good_blobs = [
        blob for blob in blobs if blob.num_content_bytes <= max_blob_content_bytes
    ]
    bad_blobs = [
        blob for blob in blobs if blob.num_content_bytes > max_blob_content_bytes
    ]
    return good_blobs, bad_blobs


@dataclass(frozen=True)
class RemoteRetrieverConfig:
    """Client-side config settings for the retriever."""

    wait_indexing_retry_sleep_secs: float = 5.0
    """Seconds to sleep between tries waiting for indexing to complete."""
    wait_indexing_retry_count: int = 12
    """Number of retries waiting for indexing to complete."""
    max_batch_content_bytes: int = 1024 * 1024
    """Maximum size of a batch upload request in bytes."""
    max_batch_items: int = 1000
    """Maximum number of items in a batch upload request."""
    disable_wait_indexing: bool = False
    """If true, do not wait for indexing to complete."""
    warn_on_indexing_timeout: bool = False
    """If true, skip errors and instead log warnings if indexing takes too long."""
    disable_extension_filtering: bool = False
    """If true, do not filter by extension."""
    max_find_missing_items: int = 1000
    """Maximum number of blobs to find missing in a single batch.

    Note: A blob name is 64 bytes, and the max size of a request to the
    api_proxy is 2MB, so the upper bound on items is ~32K.

    TODO(AU-2314): The content manager throws an error if the find_missing
    request is greater than ~10,000 blob names.
    """
    max_checkpoint_items: int = 10_000
    """Maximum number of blob names to checkpoint in a single batch.

    Note: A blob name is 64 bytes, and the max size of a request to the
    api_proxy is 2MB, so the upper bound on items is ~32K.
    """
    max_working_set_items: int = 10_000
    """Maximum number of blob names in the working set before checkpointing.

    The working set count will affect the message size for completions and
    instructions, since the working set is included in each request.
    """
    disable_retrieval: bool = False
    """Whether to disable retrieval."""


# TODO(jeff): Unit test this and completion manager via a fake client.
@dataclass
class RemoteRetriever:
    """A helper class for managing a retriever state with a remote system."""

    client: AugmentClient
    """The client to use for the retriever."""
    model_info: Model
    """"The model information for the retriever."""
    config: RemoteRetrieverConfig
    """The configuration for the retriever."""
    max_upload_size_bytes: int
    """The maximum size of a document that should be uploaded for retrieval."""
    languages: list[Language] | None = None
    """Optional list of supported languages for the retriever."""

    def __post_init__(self):
        self._doc_ids = set()
        self._blob_names: set[str] = set()

        if self.languages is not None:
            self._extensions = {
                ext for lang in self.languages for ext in lang.extensions
            }
        else:
            self._extensions = None

        # Checkpointing state consists of the last checkpoint id and the set of blob names
        # that were checkpointed.
        self._checkpoint_id = None
        self._checkpointed_blob_names: set[str] = set()

    def get_blobs(self, blob_names: Collection[str]) -> BlobsJson:
        """Return the given blob names in the compact delta form.

        The results are intended to be used as an input for completion
        or edit requests.
        """
        blob_names = set(blob_names).intersection(self._blob_names)
        if len(blob_names) < self.config.max_working_set_items:
            logger.debug(
                "working set is small, not checkpointing: %d < %d",
                len(blob_names),
                self.config.max_working_set_items,
            )
            return BlobsJson(
                checkpoint_id=None,
                added_blobs=list(blob_names),
                deleted_blobs=[],
            )
        else:
            added = list(blob_names - self._checkpointed_blob_names)
            deleted = list(self._checkpointed_blob_names - blob_names)

            if len(added) + len(deleted) < self.config.max_working_set_items:
                logger.debug(
                    "working set is large but below limit: %d + %d < %d",
                    len(added),
                    len(deleted),
                    self.config.max_working_set_items,
                )

                return BlobsJson(
                    checkpoint_id=self._checkpoint_id,
                    added_blobs=added,
                    deleted_blobs=deleted,
                )
            else:
                logger.debug(
                    "working set is large and above limit: %d + %d >= %d",
                    len(added),
                    len(deleted),
                    self.config.max_working_set_items,
                )

                self._update_checkpoint(blob_names)
                added = list(blob_names - self._checkpointed_blob_names)
                deleted = list(self._checkpointed_blob_names - blob_names)
                return BlobsJson(
                    checkpoint_id=self._checkpoint_id,
                    added_blobs=added,
                    deleted_blobs=deleted,
                )

    def _wait_for_indexing(self, blob_names: list[str]) -> list[str]:
        """Wait for indexing to complete, retrying if necessary."""
        if self.config.disable_wait_indexing:
            return []

        all_nonindexed_blob_names = []
        for blob_name_batch in batched(blob_names, self.config.max_find_missing_items):
            for retry in range(self.config.wait_indexing_retry_count + 1):
                # A bit awkward that find_missing requires model_name.
                resp = self.client.find_missing(self.model_info.name, blob_name_batch)
                logger.debug(
                    "called find_missing on %d blob names, response: %s",
                    len(blob_name_batch),
                    resp,
                )

                if resp.unknown_memory_names:
                    raise ValueError(
                        f"Unknown memory names: {resp.unknown_memory_names}"
                    )
                if not resp.nonindexed_blob_names:
                    break

                if retry == self.config.wait_indexing_retry_count:
                    # Reaches last retry.
                    if self.config.warn_on_indexing_timeout:
                        logger.warning(
                            "Nonindexed blob names: %s",
                            resp.nonindexed_blob_names,
                        )
                        all_nonindexed_blob_names.extend(resp.nonindexed_blob_names)
                    else:
                        raise ValueError(
                            f"Nonindexed blob names: {resp.nonindexed_blob_names}"
                        )
                else:
                    time.sleep(self.config.wait_indexing_retry_sleep_secs)
        return all_nonindexed_blob_names

    def _update_checkpoint(self, blob_names: Collection[str]):
        """Checkpoint the current blob names if the working set is large enough."""
        # The number of blobs we maintain in the working set will affect the
        # message size for each completion and instruction request, so we want
        # to checkpoint the working set once it exceeds a certain limit. For
        # evaluation tasks, there is not typically any churn in the working set.

        batch_size = self.config.max_checkpoint_items
        max_working_set_size = self.config.max_working_set_items

        # Compute additions (elements in new_names but not in old_names), and
        # deletions (elements in old_names but not in new_names)
        blob_names_set = set(blob_names)
        additions = list(blob_names_set - self._checkpointed_blob_names)
        deletions = list(self._checkpointed_blob_names - blob_names_set)

        if len(blob_names) < len(additions) + len(deletions):
            logger.debug(
                "creating new checkpoint is cheaper than reusing: %d < %d + %d",
                len(blob_names),
                len(additions),
                len(deletions),
            )

            self._checkpoint_id = None
            self._checkpointed_blob_names.clear()
            additions = list(blob_names)
            deletions = []

        while len(additions) + len(deletions) >= max_working_set_size:
            additions_batch, additions = additions[:batch_size], additions[batch_size:]
            rem = batch_size - len(additions_batch)
            deletions_batch, deletions = deletions[:rem], deletions[rem:]

            logger.info(
                "creating new checkpoint: %s, add %s, delete %d",
                self._checkpoint_id,
                len(additions_batch),
                len(deletions_batch),
            )
            blobs = BlobsJson(self._checkpoint_id, additions_batch, deletions_batch)
            self._checkpoint_id = self.client.checkpoint_blobs(blobs)
            self._checkpointed_blob_names.update(additions_batch)
            self._checkpointed_blob_names.difference_update(deletions_batch)

        return

    def get_num_docs(self) -> int:
        """Gets the number of docs.

        Note that this method uses the doc_id to distinguish unique documents,
        and not the blob_name.
        """
        return len(self._doc_ids)

    def get_doc_ids(self) -> set[DocumentId]:
        """Return the set of indexed documents ids."""
        return self._doc_ids

    def add_docs(self, docs: Collection[UploadContent]) -> AddDocsResponse:
        """Validate and add documents to the remote system.

        This method waits for indexing to complete so that the documents are available
        for retrieval. The added docs are returned, mostly for logging purposes.
        """
        if self.config.disable_retrieval:
            return AddDocsResponse([], [], [], [], [])

        # Filter out invalid documents
        blobs = [_doc_to_blob_info(doc) for doc in docs]
        blobs, filtered_by_id = _exclude_ids(blobs, self._doc_ids)
        if self.config.disable_extension_filtering or self._extensions is None:
            filtered_by_extension = []
        else:
            blobs, filtered_by_extension = _include_extensions(blobs, self._extensions)

        if self.max_upload_size_bytes > self.config.max_batch_content_bytes:
            raise ValueError(
                f"max_memorize_size_bytes ({self.max_upload_size_bytes}) "
                f"should be less than or equal to max_batch_content_bytes "
                f"({self.config.max_batch_content_bytes})"
            )
        blobs, filtered_by_size = _filter_by_max_size(blobs, self.max_upload_size_bytes)

        # Batch and upload the blobs
        batches = _batch_blobs(
            blobs, self.config.max_batch_content_bytes, self.config.max_batch_items
        )
        for batch in batches:
            blob_batch = [blob_info.blob for blob_info in batch]
            blob_names = self.client.batch_upload(blob_batch)
            if len(blob_names) != len(batch):
                raise ValueError(
                    f"Unexpected number of blob names: {len(blob_names)} vs {len(batch)}"
                )
            # The blob names should be the same as the doc ids, unless there is a bug.
            if blob_names != [blob_info.doc_id for blob_info in batch]:
                for blob_name, blob_info in zip(blob_names, batch):
                    if blob_name != blob_info.doc_id:
                        logger.error(
                            "Unexpected blob name: %s vs %s",
                            blob_info.doc_id,
                            blob_name,
                        )
                raise ValueError("Blob name mismatch. Check log for more info.")

            self._blob_names.update(blob_names)
            self._doc_ids.update([blob_info.doc_id for blob_info in batch])

        # Wait for the newly added docs to be indexed.
        new_blob_names = [x.doc_id for x in blobs]
        nonindexed_blob_names = self._wait_for_indexing(new_blob_names)

        return AddDocsResponse(
            successful_blobs=blobs,
            filtered_by_id=filtered_by_id,
            filtered_by_extension=filtered_by_extension,
            filtered_by_size=filtered_by_size,
            nonindexed_blob_names=nonindexed_blob_names,
        )

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        """Remove documents from the retriever."""
        self._blob_names.difference_update(doc_ids)
        self._doc_ids.difference_update(doc_ids)

    def clear_docs(self):
        """Clear any stored documents from the retriever."""

        self._doc_ids.clear()
        self._blob_names.clear()

        # clear checkpointing
        self._checkpoint_id = None
        self._checkpointed_blob_names.clear()


@dataclass(frozen=True)
class RemoteCompletionConfig:
    """Client-side config settings for the completion system."""

    max_completion_steps: int = 1
    """Maximum number of steps to use for completion."""
    warn_on_unknown_blobs: bool = False
    """If True, skip errors and instead log warnings for unknown blobs."""
    disable_recency: bool = False
    """If true, do not use recency info."""
    retry_sleep_secs: float = 1.0
    """Seconds to sleep between tries when encountering unknown blobs,
    e.g. due to cache timeouts, or certain error codes."""
    retry_count: int = 0
    """Number of retries when encountering a client error or unknown blobs."""
    retryable_error_codes: list[int] = field(default_factory=list)
    """List of HTTP error codes that should be retried."""


@dataclass(frozen=True)
class RemoteCompletionResult:
    """The result of a remote completion."""

    generated_text: str
    """The generated text."""
    generated_text_per_step: list[str]
    """The generated text for each step."""
    request_ids: list[str]
    """The request IDs for each step."""


@dataclass(frozen=True)
class RemoteCompletionInput:
    """The raw input from a completion request."""

    prefix: str
    path: str
    suffix: str
    cursor_position: int
    recency_info: dict | None = None
    edit_events: list[dict] | None = None


@dataclass
class RemoteCompletionManager:
    """A helper class for managing completion calls with a remote system."""

    model_client: AugmentModelClient
    """The client to use for the remote system."""

    model_info: Model
    """The model information from the remote system."""

    config: RemoteCompletionConfig
    """The configuration for the remote system."""

    @method_wrap(AugmentModelClient.complete)
    def single_generate(self, *args, **kwargs):
        """Generate a single completion, retries on unknown blobs."""

        for retry in range(self.config.retry_count + 1):
            try:
                completion = self.model_client.complete(*args, **kwargs)
            except ClientException as e:
                if e.response.status_code not in self.config.retryable_error_codes:
                    raise e

                if retry == self.config.retry_count:
                    raise e

                time.sleep(self.config.retry_sleep_secs)
                continue

            if completion.unknown_memory_names:
                if retry == self.config.retry_count:
                    if self.config.warn_on_unknown_blobs:
                        logger.warning(
                            "Unknown memory names: %s", completion.unknown_memory_names
                        )
                        return completion
                    else:
                        # Fail fast rather than possibly return an inaccurate generation.
                        raise ValueError(
                            f"Unknown blob names: {completion.unknown_memory_names}"
                        )
                else:
                    time.sleep(self.config.retry_sleep_secs)
                    continue

            return completion

        raise AssertionError("Unreachable code")

    def generate(
        self, model_input: RemoteCompletionInput, blobs: BlobsJson
    ) -> RemoteCompletionResult:
        """Generate a completion for the given model input."""
        assert self.model_info is not None
        assert self.model_client is not None

        prefix = model_input.prefix[
            max(
                0, len(model_input.prefix) - self.model_info.suggested_prefix_char_count
            ) :
        ]
        suffix = model_input.suffix[: self.model_info.suggested_suffix_char_count]
        cursor_position = model_input.cursor_position
        recency_info = (
            model_input.recency_info if not self.config.disable_recency else None
        )

        prefix_begin = cursor_position - len(prefix)
        suffix_end = cursor_position + len(suffix)

        # NOTE: Given pause tokens, we may need to issue multiple completion requests.
        # However, there is no way to distinguish between a pause and a stop,
        # and models may generate past a stop, so results beyond one completion request
        # may not be apples-to-apples with fim_gen_mode=evaluation.
        total_completion = ""
        completions = []
        request_ids = []
        for _ in range(self.config.max_completion_steps):
            completion = self.single_generate(
                prompt=prefix,
                path=model_input.path,
                suffix=suffix,
                cursor_position=cursor_position,
                prefix_begin=prefix_begin,
                suffix_end=suffix_end,
                blobs=blobs,
                recency_info=recency_info,
                edit_events=model_input.edit_events,
            )
            if completion.checkpoint_not_found:
                raise ValueError(f"Checkpoint not found: {blobs}")

            if (
                self.model_info.suggested_prefix_char_count
                != completion.suggested_prefix_char_count
                or self.model_info.suggested_suffix_char_count
                != completion.suggested_suffix_char_count
            ):
                if self.model_info.name:
                    # If we've specified the name, then we do not expect these values to change
                    raise ValueError(
                        "The model's suggested prefix/suffix size differs from the response: "
                        f"{completion.suggested_prefix_char_count=} {completion.suggested_suffix_char_count=} vs "
                        f"{self.model_info.suggested_prefix_char_count=} {self.model_info.suggested_suffix_char_count=}"
                    )
                else:
                    # If we're using the default model, then it's possible the suggested values change.
                    logger.warning(
                        "Updating the model's suggested prefix/suffix size to match the backend response:"
                        f" from {self.model_info.suggested_prefix_char_count=} {self.model_info.suggested_suffix_char_count=}"
                        f" to {completion.suggested_prefix_char_count=} {completion.suggested_suffix_char_count=}"
                    )
                    self.model_info.suggested_prefix_char_count = (
                        completion.suggested_prefix_char_count
                    )
                    self.model_info.suggested_suffix_char_count = (
                        completion.suggested_suffix_char_count
                    )

            # Add completion to results
            total_completion = total_completion + completion.text
            completions.append(completion.text)
            request_ids.append(str(completion.request_id))

            # Exit if the completion was empty.
            if completion.text == "":
                break

            # Update cursor position and prefix.
            cursor_position += len(completion.text)
            prefix = prefix + completion.text
            prefix = prefix[-self.model_info.suggested_prefix_char_count :]

        return RemoteCompletionResult(
            generated_text=total_completion,
            generated_text_per_step=completions,
            request_ids=request_ids,
        )


@dataclass(frozen=True)
class AugmentClientConfig:
    """Config settings for AugmentClient."""

    url: str
    """The URL of the remote system."""
    api_token: str | None = None
    """The API token to use for authentication."""
    api_token_env_var: str = "AUGMENT_TOKEN"
    """The API token environment variable."""
    api_token_path: str = "~/.config/augment/api_token"
    """The API token path, if not in the environment variable."""
    api_token_path_backup: str = "/run/determined/secrets/eval-token/token"
    """The second API token path, if not in the first path."""
    user_agent = "Augment-EvalHarness/0 (Regression Testing)"
    """The user agent to use for requests to the remote system.
    Be careful changing this, as it is used to categorize requests."""
    timeout: int = 60
    """The timeout for requests to the remote system in seconds."""
    retry_count: int = 2
    """Number of times to retry if the first request fails."""
    retry_sleep: float = 0.1
    """Time to sleep between retries in seconds."""


def get_augment_client(config: AugmentClientConfig) -> AugmentClient:
    """Returns an AugmentClient."""
    api_token = config.api_token
    if not api_token:
        api_token = os.getenv(config.api_token_env_var)
    if not api_token:
        path = Path(config.api_token_path)
        path = path.expanduser()
        if path.exists():
            api_token = path.read_text(encoding="utf-8").strip()
    if not api_token:
        path = Path(config.api_token_path_backup)
        path = path.expanduser()
        if path.exists():
            api_token = path.read_text(encoding="utf-8")

    if not api_token:
        raise ValueError("API Token not found")

    return AugmentClient(
        url=config.url,
        token=api_token,
        timeout=config.timeout,
        retry_count=config.retry_count,
        retry_sleep=config.retry_sleep,
        user_agent=config.user_agent,
    )
