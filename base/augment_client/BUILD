load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "py_test")

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = [
        "//base/augment_client:__subpackages__",
        "//base/datasets:__subpackages__",
        "//base/regression_testing:__subpackages__",
        "//experimental:__subpackages__",
        "//services:__subpackages__",
        "//tools/bazel_runner/review_edit_bot:__subpackages__",
    ],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/prompt_format:common",
        "//services/api_proxy:public_api_py_proto",
        requirement("marshmallow"),
        requirement("dataclasses_json"),
        requirement("requests"),
        requirement("python-dateutil"),
    ],
)

py_library(
    name = "remote_lib",
    srcs = ["remote_lib.py"],
    visibility = [
        "//base/augment_client:__subpackages__",
        # the model servers remote completion system should be used for services, but
        # the cceval test is doing that. Make it visible for now.
        "//services/test:__subpackages__",
    ],
    deps = [
        ":client",
        "//base/blob_names/python:blob_names",
        "//base/python/au_functools:wrappers",
        requirement("requests"),
        requirement("python-dateutil"),
    ],
)

py_library(
    name = "remote_completion_system",
    srcs = ["remote_completion_system.py"],
    visibility = [
        "//base/augment_client:__subpackages__",
        # the model servers remote completion system should be used for services, but
        # the cceval test is doing that. Make it visible for now.
        "//services/test:__subpackages__",
    ],
    deps = [
        ":client",
        ":remote_lib",
    ],
)

py_test(
    name = "test_streaming_with_spaces",
    srcs = ["test_streaming_with_spaces.py"],
    deps = [
        ":client",
        requirement("pytest"),
        requirement("requests"),
    ],
)
