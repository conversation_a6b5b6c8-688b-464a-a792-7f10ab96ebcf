"""Tests for augment_client streaming functionality with spaces before JSON payloads."""

import time
import threading
import http.server
import socketserver
import socket

from base.augment_client.client import AugmentClient


class StreamingHTTPHandler(http.server.BaseHTTPRequestHandler):
    """HTTP handler that streams predefined content"""

    stream_content = ""

    def do_POST(self):
        # Read the request body (though we don't use it in tests)
        content_length = int(self.headers.get("Content-Length", 0))
        if content_length > 0:
            self.rfile.read(content_length)

        # Send response headers
        self.send_response(200)
        self.send_header("Content-Type", "application/json")
        content_bytes = self.stream_content.encode("utf-8")
        self.send_header("Content-Length", str(len(content_bytes)))
        self.end_headers()

        # Stream the content byte by byte
        for byte in content_bytes:
            self.wfile.write(bytes([byte]))
            self.wfile.flush()
            time.sleep(0.001)

    def log_message(self, format, *args):
        pass


class StreamingTestServer:
    """Test server that streams content with spaces and heartbeats."""

    def __init__(self, content: str):
        self.content = content
        self.server = None
        self.thread = None
        self.port = None

    def __enter__(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(("", 0))
            self.port = s.getsockname()[1]

        # Set the content to stream
        StreamingHTTPHandler.stream_content = self.content

        # Create and start the server
        self.server = socketserver.TCPServer(("", self.port), StreamingHTTPHandler)
        self.thread = threading.Thread(target=self.server.serve_forever)
        self.thread.daemon = True
        self.thread.start()

        # Give the server a moment to start
        time.sleep(0.1)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.server:
            self.server.shutdown()
            self.server.server_close()
        if self.thread:
            self.thread.join(timeout=1)

    def get_url(self):
        return f"http://localhost:{self.port}"

    def set_content(self, content: str):
        self.content = content
        StreamingHTTPHandler.stream_content = content


def test_chat_stream_handles_spaces_before_json():
    """Test that chat_stream handles spaces before JSON payloads correctly."""
    # Create response content with spaces before JSON
    # Server may also send a number of heartbeats prior to the end of stream, so
    # test that as well
    response_content = "\n".join(
        [
            '   {"text": "Hello", "unknown_blob_names": [], "checkpoint_not_found": false}',
            '     {"text": " world", "unknown_blob_names": [], "checkpoint_not_found": false}',
            '{"text": "!", "unknown_blob_names": [], "checkpoint_not_found": false}',
            ' {"text": " How are you?", "unknown_blob_names": [], "checkpoint_not_found": false}',
            "    ",
        ]
    )

    # Use the test server as a context manager
    with StreamingTestServer(response_content) as server:
        # Create a real client pointing to the test server
        augment_client = AugmentClient(url=server.get_url(), token="test-token")
        client = augment_client.client_for_model("test-model")

        # Call chat_stream with the real client
        results = list(
            client.chat_stream(
                selected_code="",
                message="test message",
                prefix="",
                suffix="",
                path="test.py",
                chat_history=[],
                warn_on_parse_error=False,
            )
        )

    # Should capture all JSON lines, including those with leading spaces
    assert len(results) == 4
    assert results[0].text == "Hello"
    assert results[1].text == " world"
    assert results[2].text == "!"
    assert results[3].text == " How are you?"


def test_instruction_stream_handles_spaces_before_json():
    """Test that instruction_stream handles spaces before JSON payloads correctly."""
    response_content = "\n".join(
        [
            '   {"text": "First instruction", "unknown_blob_names": [], "checkpoint_not_found": false}',
            '     {"text": " Second instruction", "unknown_blob_names": [], "checkpoint_not_found": false}',
            '{"text": "Third instruction", "unknown_blob_names": [], "checkpoint_not_found": false}',
            ' {"text": "Fourth instruction", "unknown_blob_names": [], "checkpoint_not_found": false}',
            '    {"text": "Fifth instruction", "unknown_blob_names": [], "checkpoint_not_found": false}',
            "           ",
        ]
    )

    # Use the test server as a context manager
    with StreamingTestServer(response_content) as server:
        # Create a real client pointing to the test server
        augment_client = AugmentClient(url=server.get_url(), token="test-token")
        client = augment_client.client_for_model("test-model")

        results = list(
            client.instruction_stream(
                selected_text="test text",
                instruction="test instruction",
                prefix="",
                suffix="",
                path="test.py",
                warn_on_parse_error=False,
            )
        )

    assert len(results) == 5
    assert results[0].text == "First instruction"
    assert results[1].text == " Second instruction"
    assert results[2].text == "Third instruction"
    assert results[3].text == "Fourth instruction"
    assert results[4].text == "Fifth instruction"


def test_next_edit_stream_handles_spaces_before_json():
    """Test that next_edit_stream handles spaces before JSON payloads correctly."""
    response_content = "\n".join(
        [
            '   {"next_edit": {"text": "Edit 1"}, "unknown_blob_names": [], "checkpoint_not_found": false}',
            '     {"next_edit": {"text": " Edit 2"}, "unknown_blob_names": [], "checkpoint_not_found": false}',
            '    {"next_edit": {"text": "Edit 3"}, "unknown_blob_names": [], "checkpoint_not_found": false}',
            '{"next_edit": {"text": "Edit 4"}, "unknown_blob_names": [], "checkpoint_not_found": false}',
            '    {"next_edit": {"text": "Edit 5"}, "unknown_blob_names": [], "checkpoint_not_found": false}',
            "     ",
        ]
    )

    # Use the test server as a context manager
    with StreamingTestServer(response_content) as server:
        # Create a real client pointing to the test server
        augment_client = AugmentClient(url=server.get_url(), token="test-token")
        client = augment_client.client_for_model("test-model")

        results = list(
            client.next_edit_stream(
                mode="test",
                scope="test",
                instruction="test edit",
                prefix="",
                suffix="",
                selected_text="test",
                path="test.py",
                warn_on_parse_error=False,
            )
        )

    assert len(results) == 5
    assert results[0].next_edit is not None and results[0].next_edit["text"] == "Edit 1"
    assert (
        results[1].next_edit is not None and results[1].next_edit["text"] == " Edit 2"
    )
    assert results[2].next_edit is not None and results[2].next_edit["text"] == "Edit 3"
    assert results[3].next_edit is not None and results[3].next_edit["text"] == "Edit 4"
    assert results[4].next_edit is not None and results[4].next_edit["text"] == "Edit 5"
