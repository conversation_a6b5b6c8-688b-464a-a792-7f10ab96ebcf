from typing import get_args

from base.prompt_format_chat import (
    PromptFormatterName,
    get_structured_chat_prompt_formatter_by_name,
    get_token_counter_by_prompt_formatter_name,
)
from base.prompt_format_chat.prompt_formatter import StructuredChatPrompt<PERSON><PERSON>atter


def test_get_token_counter_by_prompt_formatter_name():
    for name in get_args(PromptFormatterName):
        token_counter = get_token_counter_by_prompt_formatter_name(name)
        assert token_counter is not None  # TokenCounter is not runtime checkable


def test_get_chat_prompt_formatter_by_name():
    for name in get_args(PromptFormatterName):
        formatter = get_structured_chat_prompt_formatter_by_name(name, None)
        # StructuredChatPromptFormatter is not runtime checkable
        assert formatter is not None
