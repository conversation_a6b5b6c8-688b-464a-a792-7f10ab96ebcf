"""Tests for the TokenizedQwenPromptFormatter."""

import pytest

from base.prompt_format.common import (
    ChatR<PERSON>ultN<PERSON>,
    ChatResultNodeType,
    ChatResultToolUse,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    Exchange,
    PromptChunk,
    ToolDefinition,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatTokenApportionment,
    StructuredChatPromptOutput,
)
from base.prompt_format_chat.tokenized_qwen_prompt_formatter import (
    StructToTokensQwenPromptFormatter,
)
from base.test_utils.testing_utils import assert_str_eq
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer

token_apportionment_with_retrieval = ChatTokenApportionment(
    path_len=256,
    prefix_len=1024,
    chat_history_len=2048,
    suffix_len=1024,
    retrieval_len=-1,  # unlimited retrieval
    retrieval_len_per_each_user_guided_file=2000,
    retrieval_len_for_user_guided=3000,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    selected_code_len=-1,  # This field is deprecated!
    message_len=-1,  # This field is deprecated!
)


@pytest.fixture
def prompter():
    return StructToTokensQwenPromptFormatter(Qwen25CoderTokenizer())


def test_with_no_history(prompter: StructToTokensQwenPromptFormatter):
    """This is a simple sanity check to catch obvious bugs in the Qwen's prompt formatting."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="(system_prompt)",
            message="(message)",
            chat_history=[],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
(system_prompt)<|im_end|>
<|im_start|>user
(message)<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_history(prompter: StructToTokensQwenPromptFormatter):
    """This is a simple sanity check to catch obvious bugs in the Qwen's prompt formatting."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="(system_prompt)",
            message="(message)",
            chat_history=[
                Exchange(
                    request_message="(message1)",
                    response_text="(response1)",
                    request_id=None,
                ),
                Exchange(
                    request_message="(message2)",
                    response_text="(response2)",
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[
                PromptChunk(
                    text="(retrieved_chunk1)",
                    path="src/bar.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/bar.py",
                    origin="dense_retriever",
                ),
                PromptChunk(
                    text="(retrieved_chunk2)",
                    path="src/foo.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/foo.py",
                    origin="dense_retriever",
                ),
            ],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
(system_prompt)<|im_end|>
<|im_start|>user
(message1)<|im_end|>
<|im_start|>assistant
(response1)<|im_end|>
<|im_start|>user
(message2)<|im_end|>
<|im_start|>assistant
(response2)<|im_end|>
<|im_start|>user
(message)<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_empty_system_prompt(prompter: StructToTokensQwenPromptFormatter):
    """Test formatting with an empty system prompt."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="",
            message="test message",
            chat_history=[],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
<|im_end|>
<|im_start|>user
test message<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_multiline_messages(prompter: StructToTokensQwenPromptFormatter):
    """Test formatting with multiline messages."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="System\nPrompt\nWith\nNewlines",
            message="User\nMessage\nWith\nNewlines",
            chat_history=[
                Exchange(
                    request_message="Request\nWith\nNewlines",
                    response_text="Response\nWith\nNewlines",
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
System
Prompt
With
Newlines<|im_end|>
<|im_start|>user
Request
With
Newlines<|im_end|>
<|im_start|>assistant
Response
With
Newlines<|im_end|>
<|im_start|>user
User
Message
With
Newlines<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_special_characters(prompter: StructToTokensQwenPromptFormatter):
    """Test formatting with special characters."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="System prompt with *special* <characters>",
            message="Message with !@#$%^&*()_+ chars",
            chat_history=[
                Exchange(
                    request_message="Request with <|special|> tokens",
                    response_text="Response with {special} chars",
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
System prompt with *special* <characters><|im_end|>
<|im_start|>user
Request with <|special|> tokens<|im_end|>
<|im_start|>assistant
Response with {special} chars<|im_end|>
<|im_start|>user
Message with !@#$%^&*()_+ chars<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_long_messages(prompter: StructToTokensQwenPromptFormatter):
    """Test formatting with very long messages."""
    long_text = "This is a very long message. " * 50
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt=long_text,
            message=long_text,
            chat_history=[
                Exchange(
                    request_message=long_text,
                    response_text=long_text,
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        f"""<|im_start|>system
{long_text}<|im_end|>
<|im_start|>user
{long_text}<|im_end|>
<|im_start|>assistant
{long_text}<|im_end|>
<|im_start|>user
{long_text}<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_empty_messages(prompter: StructToTokensQwenPromptFormatter):
    """Test formatting with empty messages."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="system",
            message="",
            chat_history=[
                Exchange(
                    request_message="",
                    response_text="",
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
system<|im_end|>
<|im_start|>user
<|im_end|>
<|im_start|>assistant
<|im_end|>
<|im_start|>user
<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_unicode_characters(prompter: StructToTokensQwenPromptFormatter):
    """Test formatting with Unicode characters."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="System with Unicode: 你好世界 🌍",
            message="Message with emojis: 👋 🚀 ✨",
            chat_history=[
                Exchange(
                    request_message="Request with Unicode: こんにちは 🌸",
                    response_text="Response with Unicode: Привет 🎉",
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens

    # Only check the structure, not the exact emoji representation
    actual_text = prompter.tokenizer.detokenize(actual_prompt_tokens)
    assert "<|im_start|>system\n" in actual_text
    assert "System with Unicode: 你好世界" in actual_text
    assert "<|im_end|>\n<|im_start|>user\n" in actual_text
    assert "Request with Unicode: こんにちは" in actual_text
    assert "<|im_end|>\n<|im_start|>assistant\n" in actual_text
    assert "Response with Unicode: Привет" in actual_text
    assert "Message with emojis: 👋" in actual_text


def test_with_tool_call_and_response(prompter: StructToTokensQwenPromptFormatter):
    """Test formatting with tool calls and responses in chat history."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="You are a helpful assistant.",
            message="what are your suggestions for optimizations?",
            chat_history=[
                Exchange(
                    request_message="what time is it?",
                    response_text=[
                        ChatResultNode(
                            id=1,
                            type=ChatResultNodeType.TOOL_USE,
                            content="",
                            tool_use=ChatResultToolUse(
                                name="clock", input={}, tool_use_id="clock_1"
                            ),
                        ),
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="Let me quickly check the time.",
                            tool_use=None,
                        ),
                    ],
                    request_id="request_1",
                ),
                Exchange(
                    request_message=[
                        ChatRequestNode(
                            id=1,
                            type=ChatRequestNodeType.TOOL_RESULT,
                            text_node=None,
                            tool_result_node=ChatRequestToolResult(
                                tool_use_id="clock_1", content="12PM", is_error=True
                            ),
                            image_node=None,
                        )
                    ],
                    response_text=[
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="it's 12PM",
                            tool_use=None,
                        )
                    ],
                    request_id="request_2",
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens

    # The expected output format based on the actual implementation
    expected_prompt = """<|im_start|>system
You are a helpful assistant.<|im_end|>
<|im_start|>user
what time is it?<|im_end|>
<|im_start|>assistant
<tool_call>
{"name": "clock", "arguments": {}}
</tool_call>
Let me quickly check the time.<|im_end|>
<|im_start|>user
<tool_response>
12PM
</tool_response><|im_end|>
<|im_start|>assistant
it's 12PM<|im_end|>
<|im_start|>user
what are your suggestions for optimizations?<|im_end|>
<|im_start|>assistant
"""

    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(expected_prompt)

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_multiple_tool_call_and_response(
    prompter: StructToTokensQwenPromptFormatter,
):
    """Test formatting with tool calls and responses in chat history."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="You are a helpful assistant.",
            message="what are your suggestions for optimizations?",
            chat_history=[
                Exchange(
                    request_message="what time is it in US and Europe?",
                    response_text=[
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="Let me quickly check the time.",
                            tool_use=None,
                        ),
                        ChatResultNode(
                            id=1,
                            type=ChatResultNodeType.TOOL_USE,
                            content="",
                            tool_use=ChatResultToolUse(
                                name="clock",
                                input={"location": "us"},
                                tool_use_id="clock_us_1",
                            ),
                        ),
                        ChatResultNode(
                            id=2,
                            type=ChatResultNodeType.TOOL_USE,
                            content="",
                            tool_use=ChatResultToolUse(
                                name="clock",
                                input={"location": "europe"},
                                tool_use_id="clock_europe_1",
                            ),
                        ),
                    ],
                    request_id="request_1",
                ),
                Exchange(
                    request_message=[
                        ChatRequestNode(
                            id=3,
                            type=ChatRequestNodeType.TOOL_RESULT,
                            text_node=None,
                            tool_result_node=ChatRequestToolResult(
                                tool_use_id="clock_us_1", content="12PM", is_error=True
                            ),
                            image_node=None,
                        ),
                        ChatRequestNode(
                            id=3,
                            type=ChatRequestNodeType.TOOL_RESULT,
                            text_node=None,
                            tool_result_node=ChatRequestToolResult(
                                tool_use_id="clock_europe_1",
                                content="5AM",
                                is_error=True,
                            ),
                            image_node=None,
                        ),
                    ],
                    response_text=[
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="it's 12PM in US and 5AM in Europe.",
                            tool_use=None,
                        )
                    ],
                    request_id="request_2",
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens

    # The expected output format based on the actual implementation
    expected_prompt = """<|im_start|>system
You are a helpful assistant.<|im_end|>
<|im_start|>user
what time is it in US and Europe?<|im_end|>
<|im_start|>assistant
Let me quickly check the time.
<tool_call>
{"name": "clock", "arguments": {"location": "us"}}
</tool_call>
<tool_call>
{"name": "clock", "arguments": {"location": "europe"}}
</tool_call><|im_end|>
<|im_start|>user
<tool_response>
12PM
</tool_response>
<tool_response>
5AM
</tool_response><|im_end|>
<|im_start|>assistant
it's 12PM in US and 5AM in Europe.<|im_end|>
<|im_start|>user
what are your suggestions for optimizations?<|im_end|>
<|im_start|>assistant
"""

    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(expected_prompt)

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_complex_tool_calls_and_responses(
    prompter: StructToTokensQwenPromptFormatter,
):
    """Test formatting with complex tool calls and responses in chat history."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="You are a helpful assistant.",
            message="what are your suggestions for optimizations?",
            chat_history=[
                Exchange(
                    request_message=" the mdodel is gpt-4o. that is a valid model name when is your cuttof? that model has been areound a long time. /web",
                    response_text=[
                        ChatResultNode(
                            id=1,
                            type=ChatResultNodeType.TOOL_USE,
                            content="",
                            tool_use=ChatResultToolUse(
                                name="web-fetch",
                                input={
                                    "url": "https://platform.openai.com/docs/models"
                                },
                                tool_use_id="toolu_vrtx_01T4t4nfAv1DHXHxvL8uvhbr",
                            ),
                        ),
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="Let me check the model information using the web-fetch tool.\n",
                            tool_use=None,
                        ),
                    ],
                    request_id="24fb06b1-7e9e-4fc2-8390-f9b4d142ba14",
                ),
                Exchange(
                    request_message=[
                        ChatRequestNode(
                            id=1,
                            type=ChatRequestNodeType.TOOL_RESULT,
                            text_node=None,
                            tool_result_node=ChatRequestToolResult(
                                tool_use_id="toolu_vrtx_01T4t4nfAv1DHXHxvL8uvhbr",
                                content="Tool result omitted due to token budget constraints.",
                                is_error=True,
                            ),
                            image_node=None,
                        )
                    ],
                    response_text=[
                        ChatResultNode(
                            id=1,
                            type=ChatResultNodeType.TOOL_USE,
                            content="",
                            tool_use=ChatResultToolUse(
                                name="web-fetch",
                                input={
                                    "url": "https://openai.com/blog/new-models-and-developer-products-announced-at-devday"
                                },
                                tool_use_id="toolu_vrtx_015Hc3sv9V69cpViuqjwhVKY",
                            ),
                        ),
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="\n\nLet me try another source.\n",
                            tool_use=None,
                        ),
                    ],
                    request_id="4e7267cc-c80d-4d93-91a5-5cd305504a45",
                ),
                Exchange(
                    request_message=[
                        ChatRequestNode(
                            id=1,
                            type=ChatRequestNodeType.TOOL_RESULT,
                            text_node=None,
                            tool_result_node=ChatRequestToolResult(
                                tool_use_id="toolu_vrtx_015Hc3sv9V69cpViuqjwhVKY",
                                content="Tool result omitted due to token budget constraints.",
                                is_error=True,
                            ),
                            image_node=None,
                        )
                    ],
                    response_text=[
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="\n\nI apologize for the failed web fetches. You are correct - `gpt-4o` is a valid model name. I was working with outdated information. The chat workflow with the OpenAI custom agent should work as implemented. The key components are:\n\n1. The chat route in `app/api/chat/route.ts` correctly uses `gpt-4o`\n2. The custom agent implementation in `hooks/use-chat-context.ts` maintains chat history\n3. The OpenAI Assistants API integration is properly configured\n\nWould you like me to help optimize any specific part of the chat workflow?\n",
                            tool_use=None,
                        )
                    ],
                    request_id="c97041d1-9c70-460a-bed9-d48653dc3699",
                ),
            ],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens

    # The expected output format based on the actual output
    expected_prompt = """<|im_start|>system
You are a helpful assistant.<|im_end|>
<|im_start|>user
 the mdodel is gpt-4o. that is a valid model name when is your cuttof? that model has been areound a long time. /web<|im_end|>
<|im_start|>assistant
<tool_call>
{"name": "web-fetch", "arguments": {"url": "https://platform.openai.com/docs/models"}}
</tool_call>
Let me check the model information using the web-fetch tool.
<|im_end|>
<|im_start|>user
<tool_response>
Tool result omitted due to token budget constraints.
</tool_response><|im_end|>
<|im_start|>assistant
<tool_call>
{"name": "web-fetch", "arguments": {"url": "https://openai.com/blog/new-models-and-developer-products-announced-at-devday"}}
</tool_call>


Let me try another source.
<|im_end|>
<|im_start|>user
<tool_response>
Tool result omitted due to token budget constraints.
</tool_response><|im_end|>
<|im_start|>assistant


I apologize for the failed web fetches. You are correct - `gpt-4o` is a valid model name. I was working with outdated information. The chat workflow with the OpenAI custom agent should work as implemented. The key components are:

1. The chat route in `app/api/chat/route.ts` correctly uses `gpt-4o`
2. The custom agent implementation in `hooks/use-chat-context.ts` maintains chat history
3. The OpenAI Assistants API integration is properly configured

Would you like me to help optimize any specific part of the chat workflow?
<|im_end|>
<|im_start|>user
what are your suggestions for optimizations?<|im_end|>
<|im_start|>assistant
"""

    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(expected_prompt)

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_complex_tool_calls_and_responses_2(
    prompter: StructToTokensQwenPromptFormatter,
):
    """Another test formatting with complex tool calls and responses in chat history."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="You are a helpful assistant.",
            message='In selenium how do I find a link that says "Sign in" instead of a button',
            chat_history=[
                Exchange(
                    request_message="Why is pge-bill-processor not working as a script",
                    response_text=[
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="Looking at your `pyproject.toml` and `generated_code.py`, there are a couple of issues with the script configuration:",
                            tool_use=None,
                        )
                    ],
                    request_id="7bb40ad2-506c-4edc-9a4b-82ef8be976c1",
                ),
                Exchange(
                    request_message="Can you check again. It is still not working and I thought I made the necessary edits",
                    response_text=[
                        ChatResultNode(
                            id=1,
                            type=ChatResultNodeType.TOOL_USE,
                            content="",
                            tool_use=ChatResultToolUse(
                                name="codebase-retrieval",
                                input={
                                    "information_request": "Look for the main module structure, specifically looking at the file structure and module naming to determine how the script should be configured."
                                },
                                tool_use_id="toolu_vrtx_01Endh4UKgvjRueuLrZNVvUy",
                            ),
                        ),
                        ChatResultNode(
                            id=0,
                            type=ChatResultNodeType.RAW_RESPONSE,
                            content="Let me check the codebase again.\n",
                            tool_use=None,
                        ),
                    ],
                    request_id="95f694f6-cdcd-456a-9ae6-5a58ae24de22",
                ),
                Exchange(
                    request_message=[
                        ChatRequestNode(
                            id=1,
                            type=ChatRequestNodeType.TOOL_RESULT,
                            text_node=None,
                            tool_result_node=ChatRequestToolResult(
                                tool_use_id="toolu_vrtx_01Endh4UKgvjRueuLrZNVvUy",
                                content="Cancelled by user.",
                                is_error=True,
                            ),
                            image_node=None,
                        )
                    ],
                    response_text="OK.",
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[],
            retrieval_as_tool=False,
            tools=None,
        )
    ).tokens

    # The expected output format will be filled in later
    expected_prompt = """\
<|im_start|>system
You are a helpful assistant.<|im_end|>
<|im_start|>user
Why is pge-bill-processor not working as a script<|im_end|>
<|im_start|>assistant
Looking at your `pyproject.toml` and `generated_code.py`, there are a couple of issues with the script configuration:<|im_end|>
<|im_start|>user
Can you check again. It is still not working and I thought I made the necessary edits<|im_end|>
<|im_start|>assistant
<tool_call>
{"name": "codebase-retrieval", "arguments": {"information_request": "Look for the main module structure, specifically looking at the file structure and module naming to determine how the script should be configured."}}
</tool_call>
Let me check the codebase again.
<|im_end|>
<|im_start|>user
<tool_response>
Cancelled by user.
</tool_response><|im_end|>
<|im_start|>assistant
OK.<|im_end|>
<|im_start|>user
In selenium how do I find a link that says "Sign in" instead of a button<|im_end|>
<|im_start|>assistant
"""

    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(expected_prompt)

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_tool_definitions(prompter: StructToTokensQwenPromptFormatter):
    """This is a simple sanity check to catch obvious bugs in the Qwen's prompt formatting."""
    tool_definitions = [
        ToolDefinition(
            name="current_time",
            description="Get the current local time as a string.",
            input_schema_json="{}",
        ),
        ToolDefinition(
            name="multiply",
            description="A function that multiplies two numbers",
            input_schema_json='{"type": "object", "properties": {"a": {"type": "number", "description": "The first number to multiply"}, "b": {"type": "number", "description": "The second number to multiply"}}, "required": ["a", "b"]}',
        ),
    ]
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="(system_prompt)",
            message="(message)",
            chat_history=[],
            retrieved_chunks_in_prompt=[],
            tool_definitions=tool_definitions,
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
(system_prompt)

# Tools

You may call one or more functions to assist with the user query.

You are provided with function signatures within <tools></tools> XML tags:
<tools>
{"type": "function", "function": {"name": "current_time", "description": "Get the current local time as a string.", "parameters": {"type": "object", "properties": {}}}}
{"type": "function", "function": {"name": "multiply", "description": "A function that multiplies two numbers", "parameters": {"type": "object", "properties": {"a": {"type": "number", "description": "The first number to multiply"}, "b": {"type": "number", "description": "The second number to multiply"}}, "required": ["a", "b"]}}}
</tools>

For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:
<tool_call>
{"name": <function-name>, "arguments": <args-json-object>}
</tool_call><|im_end|>
<|im_start|>user
(message)<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )


def test_with_tool_with_nested_definitions(prompter: StructToTokensQwenPromptFormatter):
    """This is a simple sanity check to catch obvious bugs in the Qwen's prompt formatting."""
    tool_definitions = [
        ToolDefinition(
            name="current_time",
            description="Get the current local time as a string.",
            input_schema_json="{}",
        ),
        ToolDefinition(
            name="file_editor",
            description="Tool for viewing and editing files",
            input_schema_json='{"type": "object", "properties": {"command": {"type": "string", "enum": ["view", "create", "edit"], "description": "The command to run"}, "path": {"type": "string", "description": "Path to file or directory"}, "options": {"type": "object", "properties": {"content": {"type": "string", "description": "File content for create/edit commands"}, "line_range": {"type": "array", "items": {"type": "integer"}, "description": "Line range for view command"}}}}, "required": ["command", "path"]}',
        ),
    ]
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="(system_prompt)",
            message="(message)",
            chat_history=[],
            retrieved_chunks_in_prompt=[],
            tool_definitions=tool_definitions,
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|im_start|>system
(system_prompt)

# Tools

You may call one or more functions to assist with the user query.

You are provided with function signatures within <tools></tools> XML tags:
<tools>
{"type": "function", "function": {"name": "current_time", "description": "Get the current local time as a string.", "parameters": {"type": "object", "properties": {}}}}
{"type": "function", "function": {"name": "file_editor", "description": "Tool for viewing and editing files", "parameters": {"type": "object", "properties": {"command": {"type": "string", "enum": ["view", "create", "edit"], "description": "The command to run"}, "path": {"type": "string", "description": "Path to file or directory"}, "options": {"type": "object", "properties": {"content": {"type": "string", "description": "File content for create/edit commands"}, "line_range": {"type": "array", "items": {"type": "integer"}, "description": "Line range for view command"}}}}, "required": ["command", "path"]}}}
</tools>

For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:
<tool_call>
{"name": <function-name>, "arguments": <args-json-object>}
</tool_call><|im_end|>
<|im_start|>user
(message)<|im_end|>
<|im_start|>assistant
"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )
