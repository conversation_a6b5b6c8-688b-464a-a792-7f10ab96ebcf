"""Class for formatting and tokenizing strings."""

import re
from collections.abc import Mapping
from typing import Generic, TypedDict, TypeVar, cast

import jinja2

from base.prompt_format_chat.lib.token_counter import TokenCounter

PLACEHOLDER_PATTERN = r"({[^}]+}|[^{}]+)"


def split_template(template: str) -> list[str]:
    """Splits a given template string into placeholders and plain text segments.

    This function uses a regex pattern to identify and separate text enclosed in curly
    braces (placeholders) and text outside of curly braces (regular text). It returns
    all segments as a list. For simplicity, this function assumes that curly braces are
    used exclusively to denote placeholders and are not part of regular text.

    Examples:
    - Input: "Hello, {name}! Your balance is {balance}."
    - Output: ["Hello, ", "{name}", "! Your balance is ", "{balance}", "."]

    Args:
        template (str): The template string to split.

    Returns:
        list[str]: A list of placeholders and text segments from the template.
    """
    return re.findall(PLACEHOLDER_PATTERN, template)


class StringFormatter:
    """A class like str.format that tokenizes results, assuming placeholders are strings."""

    def __init__(
        self,
        template: str,
        token_counter: TokenCounter,
    ):
        self.token_counter = token_counter
        parts = split_template(template)
        self.parts_or_placeholders: list[str] = parts

    def format(self, values: Mapping[str, str]) -> str:
        """Formats a string with placeholders replaced by strings"""
        result: str = ""
        for part in self.parts_or_placeholders:
            if part.startswith("{") and part.endswith("}"):
                key = part[1:-1]
                if key not in values:
                    raise ValueError(f"Missing value for placeholder {key}")
                else:
                    result += values[key]
            else:
                result += part
        return result

    def format_and_count_tokens(
        self, values: Mapping[str, str], values_token_counts: dict[str, int] = {}
    ) -> int:
        """Formats string with placeholders and returns total token count."""
        result: int = 0
        for part in self.parts_or_placeholders:
            if part.startswith("{") and part.endswith("}"):
                key = part[1:-1]
                if key not in values:
                    raise ValueError(f"Missing value for placeholder {key}")
                else:
                    result += (
                        values_token_counts[key]
                        if key in values_token_counts
                        else self.token_counter.count_tokens(values[key])
                    )
            else:
                result += self.token_counter.count_tokens(part)
        return result


# NOTE(arun): The intermediate class is required to appease Python's type system.
# See: https://stackoverflow.com/questions/78518728/how-to-specify-a-generic-over-typeddict/78520322#78520322
class JinjaValues(TypedDict):
    """Base class for values used in Jinja templates."""

    pass


ValuesT = TypeVar("ValuesT", bound=JinjaValues)


class JinjaFormatter(Generic[ValuesT]):
    """A class that converts strings based on Jinja templates."""

    def __init__(
        self,
        jinja_template: jinja2.Template,
        token_counter: TokenCounter,
        default_values: ValuesT | None = None,
    ):
        self.token_counter = token_counter
        self.jinja_template = jinja_template
        self.default_values = default_values

    def format(self, values: ValuesT) -> str:
        """Formats a string with placeholders replaced by strings"""
        if self.default_values:
            values = cast(ValuesT, {**self.default_values, **values})
        return self.jinja_template.render(values).strip()

    def format_and_count_tokens(self, values: ValuesT) -> int:
        if self.default_values:
            values = cast(ValuesT, {**self.default_values, **values})
        return self.token_counter.count_tokens(self.format(values))
