"""Test the FormattedFile class."""

import pytest

from base.prompt_format.common import PromptChunk
from base.prompt_format_chat.lib.formatted_file_v2 import FormattedFileV2
from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.retrieval.chunking.line_based_chunking import split_line_chunks
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer

DUMMY_PATH = "file.txt"


@pytest.fixture(name="formatted_file")
def fixture_formatted_file():
    """Setup the formatted file with llama3 tokenizer."""
    tokenizer = Llama3InstructTokenizer()
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    return FormattedFileV2(token_counter=token_counter)


def test_get_tokens_empty(formatted_file: FormattedFileV2):
    """Test empty FormattedFile."""
    assert formatted_file.get_file_str_and_tok_ct() == (
        "...",
        formatted_file.token_counter.count_tokens("..."),
    )
    assert len(formatted_file) == formatted_file.token_counter.count_tokens("...")


def test_add_chunk(formatted_file: FormattedFileV2):
    """Test adding a single chunk."""
    chunk = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_consecutive_chunks(formatted_file):
    """Test adding multiple chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=12, char_end=20, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_consecutive_chunks_reversed_order(formatted_file):
    """Test adding multiple chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=12, char_end=20, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk1)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_consecutive_chunks_non_zero_start(formatted_file):
    """Test adding chunks with non-zero start."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=5, char_end=17, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=17, char_end=25, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "...\nHello World!\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_non_consecutive_chunks_non_zero_start(formatted_file):
    """Test adding chunks with non-zero start."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=5, char_end=17, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=20, char_end=25, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "...\nHello World!\n...\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_overlapping_chunks(formatted_file):
    """Test adding overlapping chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="Foo Bar!\nAfter foo bar!\n", char_start=13, char_end=37, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)

    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct

    formatted_file = formatted_file.add_chunk(chunk3)

    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_overlapping_chunk_extension(formatted_file):
    """Test adding overlapping chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="World!\nFoo Bar!\nAfter foo bar!\n",
        char_start=6,
        char_end=37,
        path=DUMMY_PATH,
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk3)

    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_overlapping_chunk_extension_out_of_order_insertion(formatted_file):
    """Test adding overlapping chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="World!\nFoo Bar!\nAfter foo bar!\n",
        char_start=6,
        char_end=37,
        path=DUMMY_PATH,
    )
    formatted_file = formatted_file.add_chunk(chunk3)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk1)

    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_overlapping_chunks_out_order_insertion(formatted_file):
    """Test adding overlapping chunks but out of order insertion."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="Foo Bar!\nAfter foo bar!\n", char_start=13, char_end=37, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk3)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk1)

    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_add_chunks_with_empty_text(formatted_file):
    """Test adding chunks with empty text."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="", char_start=13, char_end=13, path=DUMMY_PATH)
    chunk3 = PromptChunk(text="Foo Bar!\n", char_start=13, char_end=22, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk3)

    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nFoo Bar!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_add_chunks_with_same_text(formatted_file):
    """Test adding chunks with the same text."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)

    file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\n..."
    expected_tok_ct = formatted_file.token_counter.count_tokens(file_str)
    assert tok_ct == expected_tok_ct
    assert len(formatted_file) == expected_tok_ct


def test_add_line_numbers_single_chunk(formatted_file):
    """Test adding line numbers to a single chunk."""
    chunk = PromptChunk(
        text="Hello World!\nSecond line\n",
        char_start=0,
        char_end=24,
        path=DUMMY_PATH,
        line_start=0,
        line_end=2,
    )
    formatted_file = formatted_file.add_chunk(chunk)

    # Test without line numbers (default)
    file_str, _ = formatted_file.get_file_str_and_tok_ct()
    assert file_str == "Hello World!\nSecond line\n..."

    # Test with line numbers
    file_str, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers=True)
    assert file_str == "     1\tHello World!\n     2\tSecond line\n..."


def test_add_line_numbers_consecutive_chunks(formatted_file):
    """Test adding line numbers to consecutive chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n",
        char_start=0,
        char_end=13,
        path=DUMMY_PATH,
        line_start=0,
        line_end=1,
    )
    chunk2 = PromptChunk(
        text="Second line\n",
        char_start=13,
        char_end=25,
        path=DUMMY_PATH,
        line_start=1,
        line_end=2,
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)

    # Test with line numbers
    file_str, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers=True)
    assert file_str == "     1\tHello World!\n     2\tSecond line\n..."


def test_add_line_numbers_non_consecutive_chunks(formatted_file):
    """Test adding line numbers to non-consecutive chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n",
        char_start=0,
        char_end=13,
        path=DUMMY_PATH,
        line_start=0,
        line_end=1,
    )
    # Gap between chunks (char 13-20 missing)
    chunk2 = PromptChunk(
        text="Third line\n",
        char_start=20,
        char_end=31,
        path=DUMMY_PATH,
        line_start=3,
        line_end=4,
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)

    # Test with line numbers
    file_str, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers=True)
    # Line numbers should increment for the gap
    assert file_str == "     1\tHello World!\n...\n     4\tThird line\n..."


def test_add_line_numbers_non_zero_start(formatted_file):
    """Test adding line numbers to chunks with non-zero start."""
    chunk = PromptChunk(
        text="Hello World!\n",
        char_start=100,
        char_end=113,
        path=DUMMY_PATH,
        line_start=3,
        line_end=4,
    )
    formatted_file = formatted_file.add_chunk(chunk)

    # Test with line numbers
    file_str, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers=True)
    assert "...\n     4\tHello World!\n..." in file_str


def test_add_line_numbers_overlapping_chunks(formatted_file):
    """Test adding line numbers to overlapping chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\nSecond line\n",
        char_start=0,
        char_end=25,
        path=DUMMY_PATH,
        line_start=0,
        line_end=2,
    )
    chunk2 = PromptChunk(
        text="Second line\nThird line\n",
        char_start=13,
        char_end=36,
        path=DUMMY_PATH,
        line_start=1,
        line_end=3,
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)

    # Test with line numbers
    file_str, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers=True)
    # The implementation adds an empty line for overlapping chunks
    assert (
        file_str == "     1\tHello World!\n     2\tSecond line\n     3\tThird line\n..."
    )


def test_actual_file_example_with_line_numbers(formatted_file: FormattedFileV2):
    """Test the actual file example with line numbers."""

    path = "/path/to/test_file.py"
    raw_chunks = list(
        split_line_chunks(
            TEST_FILE,
            chunk_max_lines=5,
            chunk_max_chars=100,
            overlap_lines=0,
        )
    )

    prompt_chunks: list[PromptChunk] = []
    for raw_chunk in raw_chunks:
        prompt_chunks.append(
            PromptChunk(
                text=raw_chunk.text,
                path=path,
                char_start=raw_chunk.char_offset,
                char_end=raw_chunk.char_offset + len(raw_chunk.text),
                line_start=raw_chunk.line_offset,
                line_end=raw_chunk.line_offset + raw_chunk.length_in_lines,
            )
        )

    # Now join all the chunks with formatted file
    for prompt_chunk in prompt_chunks:
        formatted_file = formatted_file.add_chunk(prompt_chunk)

    # Test with line numbers
    output, _ = formatted_file.get_file_str_and_tok_ct(add_line_numbers=True)

    # Verify line numbers are present and sequential
    lines = output.split("\n")
    line_numbers = []
    for line in lines:
        if "\t" in line and not line.startswith("..."):
            line_number_str = line.split("\t")[0].strip()
            if line_number_str.isdigit():
                line_numbers.append(int(line_number_str))

    # Check that line numbers are sequential
    for i in range(1, len(line_numbers)):
        assert (
            line_numbers[i] == line_numbers[i - 1] + 1
        ), f"Line numbers not sequential: {line_numbers[i - 1]} followed by {line_numbers[i]}"


TEST_FILE = '''
"""Module containing token counter classes."""

import functools
import typing

from base.tokenizers.tokenizer import Tokenizer


class TokenCounter(typing.Protocol):
    """A protocol for counting tokens."""

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        """Count the number of tokens in the prompt."""
        raise NotImplementedError()


class TokenizerBasedTokenCounter(TokenCounter):
    """A token counter that uses a tokenizer."""

    def __init__(self, tokenizer: Tokenizer):
        self.tokenizer = tokenizer

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        return len(self.tokenizer.tokenize_safe(prompt_chars))
'''


def test_actual_file_example(formatted_file: FormattedFileV2):
    """Test the actual file example."""

    path = "/path/to/test_file.py"
    raw_chunks = list(
        split_line_chunks(
            TEST_FILE,
            chunk_max_lines=5,
            chunk_max_chars=100,
            overlap_lines=0,
        )
    )

    prompt_chunks: list[PromptChunk] = []
    for raw_chunk in raw_chunks:
        prompt_chunks.append(
            PromptChunk(
                text=raw_chunk.text,
                path=path,
                char_start=raw_chunk.char_offset,
                char_end=raw_chunk.char_offset + len(raw_chunk.text),
            )
        )
    assert len(prompt_chunks) == 9

    # Delete some random chunk in the middle of the file from the list
    del prompt_chunks[3]

    expected_test_lines = [
        prompt_chunks[0].text,
        prompt_chunks[1].text,
        prompt_chunks[2].text,
        "...\n",
        prompt_chunks[3].text,
        prompt_chunks[4].text,
        prompt_chunks[5].text,
        prompt_chunks[6].text,
        prompt_chunks[7].text,
        "...",
    ]
    expected_test = "".join(expected_test_lines)
    expected_test_tok_ct = sum(
        [formatted_file.token_counter.count_tokens(txt) for txt in expected_test_lines]
    )

    # Join two chunks to complicate things further
    new_chunk_4 = PromptChunk(
        text=prompt_chunks[3].text + prompt_chunks[4].text,
        path=path,
        char_start=prompt_chunks[3].char_start,
        char_end=prompt_chunks[4].char_start + len(prompt_chunks[4].text),
    )
    prompt_chunks[3] = new_chunk_4
    del prompt_chunks[4]

    # Now join all the chunks with formatted file and confirm output looks right.
    for prompt_chunk in prompt_chunks:
        formatted_file = formatted_file.add_chunk(prompt_chunk)

    output, tok_ct = formatted_file.get_file_str_and_tok_ct()
    assert output == expected_test
    assert tok_ct == expected_test_tok_ct
