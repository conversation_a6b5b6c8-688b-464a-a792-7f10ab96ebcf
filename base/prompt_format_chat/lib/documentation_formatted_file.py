"""Implements a FormattedFile that combines retrieved documentation chunks from the same file."""

import bisect
import dataclasses

from base.prompt_format_chat.lib.abstract_formatted_file import AbstractFormattedFile
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.prompt_format_chat.prompt_formatter import PromptChunk


@dataclasses.dataclass(frozen=True)
class DocumentationFormattedFile(AbstractFormattedFile):
    """
    A class to format chunks from the same file for documentation chunks.

    Assumes no overlap for documentation chunks.
    """

    token_counter: TokenCounter
    sorted_chunks: tuple[PromptChunk, ...] = dataclasses.field(default_factory=tuple)

    def get_file_str_and_tok_ct(
        self, add_line_numbers: bool = False
    ) -> tuple[str, int]:
        """Returns the formatted string of contents for the file (including pre-pending headers) and the number of tokens."""
        del add_line_numbers
        section_separator = "...\n"
        separator_tok_ct = self.token_counter.count_tokens(section_separator)
        new_line = "\n"
        new_line_tok_ct = self.token_counter.count_tokens(new_line)
        file_str = ""
        total_tok_ct = 0
        prev_chunk_char_end = 0
        prev_chunk_char_start = 0
        headers_used = []
        for chunk in self.sorted_chunks:
            assert chunk.documentation_metadata is not None

            # confirm sorted invariant
            assert prev_chunk_char_start <= chunk.char_start
            prev_chunk_char_start = chunk.char_start

            # Add tokens for the chunk
            if prev_chunk_char_end <= chunk.char_start:
                chunk_tok_ct = self._get_chunk_tk_count(
                    chunk.text, chunk.char_start, chunk.char_end, chunk.path
                )
                if prev_chunk_char_end < chunk.char_start:
                    # The separator is a stand-in for the missing code
                    file_str += section_separator
                    total_tok_ct += separator_tok_ct

                # Add headers that haven't been added already

                new_headers = [
                    chunk_header
                    for chunk_header in chunk.documentation_metadata.headers
                    if chunk_header not in headers_used
                ]
                for h in new_headers:
                    file_str += h
                    total_tok_ct += self.token_counter.count_tokens(h)
                    file_str += new_line
                    total_tok_ct += new_line_tok_ct
                headers_used.extend(new_headers)

                file_str += chunk.text
                total_tok_ct += chunk_tok_ct
                prev_chunk_char_end = chunk.char_end

            elif prev_chunk_char_end >= chunk.char_end:
                # Nothing to add if the chunk is entirely contained in the previous chunk
                continue

        # We don't know whether we have included an entire file,
        # so we add a separator to ensure proper formatting.
        file_str += "..."
        total_tok_ct += self.token_counter.count_tokens("...")
        return file_str, total_tok_ct

    def add_chunk(self, chunk: PromptChunk) -> "DocumentationFormattedFile":
        """Add a chunk to this file."""
        index = bisect.bisect_left(self.sorted_chunks, chunk)
        return DocumentationFormattedFile(
            token_counter=self.token_counter,
            sorted_chunks=(
                self.sorted_chunks[:index] + (chunk,) + self.sorted_chunks[index:]
            ),
        )

    def __getitem__(self, index: int) -> int:
        """Get the token at the given index."""
        # NOTE: We add this method to make the class compatible
        # we Sequence[int] protocol for `StringFormatter` class
        # in the `base/prompt_format_chat/tokenized_string_formatter.py` file.
        # However, `StringFormatter` only uses __len__ and __iter__ method,
        # so we are not implementing __getitem__ for now.
        raise NotImplementedError()

    def get_docset_name(self) -> str:
        """Get the name of the docset."""
        if not self.sorted_chunks[0].documentation_metadata:
            return ""
        return self.sorted_chunks[0].documentation_metadata.name
