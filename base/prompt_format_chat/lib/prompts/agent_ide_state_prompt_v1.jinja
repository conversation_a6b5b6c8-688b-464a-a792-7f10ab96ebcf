{% set show_workspace_msg = first_message or workspace_folders_changed %}
{% set show_terminal_msg = (first_message or current_terminal_changed) and current_terminal %}
{% if show_workspace_msg or show_terminal_msg %}
<supervisor>
{% endif %}
{% if show_workspace_msg %}
    {% if workspace_folders %}
        {% if first_message %}
The user's workspace is opened at `{{workspace_folders[0].folder_root}}`.
        {% else %}
The user changed workspace directories. It is now opened at `{{workspace_folders[0].folder_root}}`.
        {% endif %}
When the user mentions a path, it is probably relative to the workspace directory.
The user's workspace is part of a repository that is currently rooted at `{{workspace_folders[0].repository_root}}`.
        {% if repo_relative_tools %}
Use the repository root directory to resolve relative paths supplied to the following tools: {{repo_relative_tools | join(", ")}}.
        {% endif %}
        {% if launch_process_tool_name %}
The repository root directory will be the current working directory when launching processes using the `{{launch_process_tool_name}}` tool with `wait=false`.
        {% endif %}
    {% else %}
The user does not have any workspace directories open.
When the user mentions a path, it is probably relative to their home directory.
        {% if launch_process_tool_name %}
Their home directory will be the current working directory when launching processes using the `{{launch_process_tool_name}}` tool with `wait=false`.
        {% endif %}
    {% endif %}
{% endif %}
{% if show_terminal_msg %}
    {% if first_message %}
The interactive terminal's current working directory is `{{current_terminal.current_working_directory}}`.
    {% else %}
The interactive terminal's current working directory has changed and is now `{{current_terminal.current_working_directory}}`.
    {% endif %}
        {% if launch_process_tool_name %}
This is the current working directory used when launching processes using the `{{launch_process_tool_name}}` tool with `wait=true`.
        {% endif %}
{% endif %}
{% if show_workspace_msg or show_terminal_msg %}

This information may or may not be relevant to the user's current request.
Don't repeat this information to the user.
</supervisor>
{% endif %}
