{% set show_checkpoint_revert = checkpoint_reverts | length > 0 %}
{% set show_user_edits = user_edits | length > 0 %}
{% if show_checkpoint_revert or show_user_edits %}
<supervisor>
{% if show_checkpoint_revert %}
The user reverted previously made changes to the following files:
    {% for edit_event in checkpoint_reverts %}
- {{edit_event.path}}
        {%if edit_event.diff %}
    - Diff attached:
<diff>
{{edit_event.diff}}
</diff>
        {% elif edit_event.hunk_ranges %}
            {% for hunk_range in edit_event.hunk_ranges %}
    - Lines {{hunk_range.before_lrange.start}}-{{hunk_range.before_lrange.stop}} deleted, {{hunk_range.after_lrange.start}}-{{hunk_range.after_lrange.stop}} added.
            {% endfor %}
        {% else %}
    - Diff omitted.
        {% endif %}
    {% endfor %}
    {% if num_additional_checkpoint_reverts > 0 %}
... and {{num_additional_checkpoint_reverts}} more files.
    {% endif %}
Before editing these files again, make sure you read them to know what they look like now.
{% endif %}
{% if show_user_edits %}
The user manually made changes to the following files:
    {% for edit_event in user_edits %}
- {{edit_event.path}}
        {%if edit_event.diff %}
    - Diff attached:
<diff>
{{edit_event.diff}}
</diff>
        {% elif edit_event.hunk_ranges %}
            {% for hunk_range in edit_event.hunk_ranges %}
    - Lines {{hunk_range.before_lrange.start}}-{{hunk_range.before_lrange.stop}} deleted, {{hunk_range.after_lrange.start}}-{{hunk_range.after_lrange.stop}} added.
            {% endfor %}
        {% else %}
    - Diff omitted.
        {% endif %}
    {% endfor %}
    {% if num_additional_user_edits > 0 %}
... and {{num_additional_user_edits}} more files.
    {% endif %}
{% endif %}

This information may or may not be relevant to the user's current request.
Don't repeat this information to the user.
</supervisor>
{% endif %}
