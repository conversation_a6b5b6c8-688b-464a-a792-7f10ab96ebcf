import pytest

from base import feature_flags
from base.prompt_format.common import Rule, RuleType
from base.prompt_format_chat.lib.rules_prompt_builder import (
    build_custom_prompt,
    WORKSPACE_GUIDELINES_LENGTH_LIMIT,
)
from base.prompt_format_chat.prompt_formatter import ChatPromptInput


@pytest.fixture
def feature_flags_context():
    """Fixture for feature flags testing."""
    yield from feature_flags.feature_flag_fixture()


def _make_chat_prompt_input(workspace_guidelines, user_guidelines, rules=None):
    if rules is None:
        rules = []
    return ChatPromptInput(
        message="",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=-1,
        suffix_end=-1,
        retrieved_chunks=[],
        context_code_exchange_request_id=None,
        recent_changes=None,
        user_guided_blobs=[],
        external_source_ids=[],
        workspace_guidelines=workspace_guidelines,
        user_guidelines=user_guidelines,
        rules=rules,
    )


def test_build_custom_prompt_for_workspace():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines="Follow PEP 8 guidelines.", user_guidelines=None
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    expected = """

Additional workspace rules located in .augment-guidelines:
Follow PEP 8 guidelines.


"""
    assert result == expected


def test_build_custom_prompt_for_user():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines="Use camelCase for variable names.",
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    expected = """

Additional user rules:
Use camelCase for variable names.

"""
    assert result == expected


def test_build_custom_prompt_for_workspace_and_user():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines="Follow PEP 8 guidelines.",
        user_guidelines="Use camelCase for variable names.",
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    expected = """

Additional workspace rules located in .augment-guidelines:
Follow PEP 8 guidelines.


Additional user rules:
Use camelCase for variable names.

"""
    assert result == expected


def test_build_custom_prompt_for_none():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines=None,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    assert result == ""


def test_build_custom_prompt_with_rules():
    """Test that rules are included when the feature flag is enabled by default."""
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule1.txt",
            content="Use 4 spaces for indentation.",
        ),
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule2.txt",
            content="Use double quotes for strings.",
        ),
        Rule(
            type=RuleType.MANUAL,
            path="path/to/manual_rule.txt",
            content="Use f-strings for string formatting.",
        ),
    ]
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines=None,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines,
        prompt_input.workspace_guidelines,
        rules=prompt_input.rules,
    )
    expected = """


Additional rule located in path/to/rule1.txt:
Use 4 spaces for indentation.



Additional rule located in path/to/rule2.txt:
Use double quotes for strings.



Additional rule located in path/to/manual_rule.txt:
Use f-strings for string formatting.



"""
    assert result == expected


def test_workspace_guidelines_and_rules_limit_disabled(feature_flags_context):
    """Test truncation behavior when both workspace guidelines and rules are present."""
    # Set feature flag to 200 characters to test truncation
    feature_flags_context.set_flag(WORKSPACE_GUIDELINES_LENGTH_LIMIT, 0)

    workspace_guidelines = "Follow coding standards"  # 23 characters
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule1.txt",
            content="Use descriptive variable names.",
        ),
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule2.txt",
            content="Add comments for complex logic.",
        ),
    ]

    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=workspace_guidelines,
        user_guidelines=None,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines,
        prompt_input.workspace_guidelines,
        rules=prompt_input.rules,
    )

    # When limit is disabled (0), no truncation should occur
    expected = """


Additional rule located in path/to/rule1.txt:
Use descriptive variable names.



Additional rule located in path/to/rule2.txt:
Add comments for complex logic.


Additional workspace rules located in .augment-guidelines:
Follow coding standards


"""

    assert result == expected


def test_workspace_guidelines_length_limit_with_rules(feature_flags_context):
    """Test truncation behavior when both workspace guidelines and rules are present."""
    # Set feature flag to 200 characters to test truncation
    feature_flags_context.set_flag(WORKSPACE_GUIDELINES_LENGTH_LIMIT, 200)

    workspace_guidelines = "Follow coding standards"  # 23 characters
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule1.txt",
            content="Use descriptive variable names.",
        ),
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule2.txt",
            content="Add comments for complex logic.",
        ),
    ]

    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=workspace_guidelines,
        user_guidelines=None,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines,
        prompt_input.workspace_guidelines,
        rules=prompt_input.rules,
    )

    # With our new truncation logic, complete sections are cut off when limit is exceeded
    # The first two rules fit within 200 characters, but adding workspace guidelines would exceed it
    expected = """


Additional rule located in path/to/rule1.txt:
Use descriptive variable names.



Additional rule located in path/to/rule2.txt:
Add comments for complex logic.



"""
    assert result == expected


def test_workspace_guidelines_length_limit_with_rules_no_truncation_needed(
    feature_flags_context,
):
    """Test truncation behavior when both workspace guidelines and rules are present."""
    # Set feature flag to 200 characters to test truncation
    feature_flags_context.set_flag(WORKSPACE_GUIDELINES_LENGTH_LIMIT, 300)

    workspace_guidelines = "Follow coding standards"  # 23 characters
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule1.txt",
            content="Use descriptive variable names.",
        ),
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule2.txt",
            content="Add comments for complex logic.",
        ),
    ]

    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=workspace_guidelines,
        user_guidelines=None,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines,
        prompt_input.workspace_guidelines,
        rules=prompt_input.rules,
    )

    # With limit of 300, no truncation should occur
    expected = """


Additional rule located in path/to/rule1.txt:
Use descriptive variable names.



Additional rule located in path/to/rule2.txt:
Add comments for complex logic.


Additional workspace rules located in .augment-guidelines:
Follow coding standards


"""

    assert result == expected


def test_combination_guidelines_truncation(feature_flags_context):
    """Test that user guidelines are not affected by workspace guidelines length limit."""
    # Set feature flag to 50 characters
    feature_flags_context.set_flag(WORKSPACE_GUIDELINES_LENGTH_LIMIT, 200)

    workspace_guidelines = (
        "Long workspace guidelines that will be truncated" * 10
    )  # Will be truncated
    user_guidelines = "User guidelines"  # Should not be truncated
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule1.txt",
            content="Use descriptive variable names.",
        ),
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule2.txt",
            content="Add comments for complex logic.",
        ),
    ]

    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=workspace_guidelines,
        user_guidelines=user_guidelines,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines,
        prompt_input.workspace_guidelines,
        rules=prompt_input.rules,
    )

    # With limit of 50 characters, no sections can fit, so only user guidelines remain
    # (user guidelines are not affected by workspace guidelines length limit)
    expected = """


Additional rule located in path/to/rule1.txt:
Use descriptive variable names.



Additional rule located in path/to/rule2.txt:
Add comments for complex logic.



Additional user rules:
User guidelines

"""
    assert result == expected


def test_build_custom_prompt_with_agent_requested_rule_with_description():
    """Test that AGENT_REQUESTED rules with descriptions are formatted correctly."""
    rules = [
        Rule(
            type=RuleType.AGENT_REQUESTED,
            path="path/to/agent_rule.txt",
            content="This content should not be used for AGENT_REQUESTED rules.",
            description="user wants to implement a REST API",
        ),
    ]
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines=None,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines,
        prompt_input.workspace_guidelines,
        rules=prompt_input.rules,
    )
    expected = """

Additional agent requested rules:
If the user prompt matches the description "user wants to implement a REST API", read the file located in path/to/agent_rule.txt


"""
    assert result == expected


def test_build_custom_prompt_with_mixed_rule_types():
    """Test that different rule types are formatted correctly when mixed together."""
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/always_rule.txt",
            content="Always use type hints.",
        ),
        Rule(
            type=RuleType.MANUAL,
            path="path/to/manual_rule.txt",
            content="Use descriptive variable names.",
        ),
        Rule(
            type=RuleType.AGENT_REQUESTED,
            path="path/to/agent_rule.txt",
            content="This content is ignored for AGENT_REQUESTED rules.",
            description="user is working with database operations",
        ),
        Rule(
            type=RuleType.AGENT_REQUESTED,
            path="path/to/joke.txt",
            content="End response with a joke.",
            description="tell a joke",
        ),
    ]
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines=None,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines,
        prompt_input.workspace_guidelines,
        rules=prompt_input.rules,
    )
    expected = """

Additional agent requested rules:
If the user prompt matches the description "user is working with database operations", read the file located in path/to/agent_rule.txt
If the user prompt matches the description "tell a joke", read the file located in path/to/joke.txt


Additional rule located in path/to/always_rule.txt:
Always use type hints.



Additional rule located in path/to/manual_rule.txt:
Use descriptive variable names.



"""
    assert result == expected
