"""Builds the prompt based on user-provided rules."""

from dataclasses import dataclass

import structlog

from base import feature_flags
from base.prompt_format.common import PersonaType, Rule, RuleType

logger = structlog.get_logger(__name__)

# Feature flag for maximum length of workspace guidelines in characters
# Default to 0 which means no limit
WORKSPACE_GUIDELINES_LENGTH_LIMIT = feature_flags.IntFlag(
    "workspace_guidelines_length_limit", 0
)


class RulesProcessor:
    """Main class for processing rules and guidelines into formatted sections."""

    def __init__(self, length_limit: int = 0):
        self.length_limit = length_limit

    def _format_agent_requested_rule(self, rule: Rule) -> str:
        return f'If the user prompt matches the description "{rule.description}", read the file located in {rule.path}'

    def _format_standard_rule(self, rule: Rule) -> str:
        return f"""
Additional rule located in {rule.path}:
{rule.content}
"""

    def _format_workspace_guidelines(self, workspace_guidelines: str) -> str:
        return f"""\
Additional workspace rules located in .augment-guidelines:
{workspace_guidelines}"""

    def _format_rules(self, rules: list[Rule]) -> list[str]:
        """Format a list of rules into sections."""
        sections = []

        # Separate agent-requested rules from others
        agent_requested_rules = [r for r in rules if r.type == RuleType.AGENT_REQUESTED]
        remaining_rules = [r for r in rules if r.type != RuleType.AGENT_REQUESTED]

        # Create a singular section with all agent_requested_rules
        if agent_requested_rules:
            agent_requested_prompt = "Additional agent requested rules:"

            for rule in agent_requested_rules:
                agent_requested_prompt += "\n" + self._format_agent_requested_rule(rule)
            sections.append(agent_requested_prompt)

        # Format remaining rules
        for rule in remaining_rules:
            sections.append(self._format_standard_rule(rule))

        return sections

    def _apply_length_limit(self, sections: list[str]) -> tuple[list[str], bool]:
        """Apply length limit to sections if configured.

        Returns:
            Tuple of (included_sections, truncated)
        """
        if self.length_limit <= 0:
            return sections, False

        included_sections = []
        current_length = 0

        for section in sections:
            # Calculate length if we add this section (including separator)
            separator_length = len("\n\n") if included_sections else 0
            section_length = len(section) + separator_length

            # Stop adding sections once we would exceed the limit
            if current_length + section_length > self.length_limit:
                break

            included_sections.append(section)
            current_length += section_length

        truncated = len(included_sections) < len(sections)
        if truncated:
            logger.info(
                "Workspace guidelines truncated by cutting off complete sections",
                original_parts_count=len(sections),
                included_parts_count=len(included_sections),
                limit=self.length_limit,
            )

        return included_sections, truncated

    def process_rules_and_guidelines(
        self, workspace_guidelines: str | None = None, rules: list[Rule] | None = None
    ) -> tuple[list[str], bool]:
        """Process rules and workspace guidelines into formatted sections.

        Returns:
            Tuple of (sections, truncated)
        """
        if not workspace_guidelines and not rules:
            return [], False

        sections = []

        # Process rules first
        if rules:
            rule_sections = self._format_rules(rules)
            sections.extend(rule_sections)

        # Process workspace guidelines
        if workspace_guidelines:
            workspace_section = self._format_workspace_guidelines(workspace_guidelines)
            sections.append(workspace_section)

        # Apply length limit
        return self._apply_length_limit(sections)


@dataclass
class Persona:
    persona_type: PersonaType
    name: str
    prompt: str


DEFAULT_PERSONA = Persona(
    persona_type=PersonaType.DEFAULT,
    name="",
    prompt="",
)

PROTOTYPER_PERSONA = Persona(
    persona_type=PersonaType.PROTOTYPER,
    name="Prototyper",
    prompt="""\
# Recommendations when building new web apps
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with `vite` or `next.js`.
- Initialize the project using a CLI initialization tool, instead of writing from scratch.
- For database and auth, a good default option is to use Supabase.
- Before using `open-browser` to show the user the app, use `curl` with the `launch-process` tool to access the website and `read-process` to check for errors.
- Modern frameworks like Next.js have hot reload, so the user can see the changes without a refresh. You should therefore avoid calling `open-browser` more than once on the same URL.""",
)


def get_persona(persona_type: PersonaType) -> Persona:
    """Get the persona prompt for the given persona.

    Args:
        persona: The persona to get the prompt for.

    Returns:
        The persona prompt.
    """
    if persona_type == PersonaType.DEFAULT:
        return DEFAULT_PERSONA
    elif persona_type == PersonaType.PROTOTYPER:
        return PROTOTYPER_PERSONA
    else:
        raise ValueError(f"Unknown persona: {persona_type}")


def get_formatted_rules_prompt(
    workspace_guidelines: str | None = None,
    rules: list[Rule] | None = None,
    length_limit: int = 0,
) -> str:
    """
    Get formatted rules prompt using the RulesProcessor.

    Args:
        workspace_guidelines: Optional workspace guidelines content
        rules: Optional list of rules to format
        length_limit: Maximum length limit for the prompt (0 means no limit)

    Returns:
        Formatted rules prompt string
    """
    processor = RulesProcessor(length_limit=length_limit)

    sections, truncated = processor.process_rules_and_guidelines(
        workspace_guidelines, rules
    )

    if not sections:
        return ""

    # Join all sections together
    return "\n\n" + "\n\n".join(sections) + "\n"


def build_custom_prompt(
    user_guidelines: str | None,
    workspace_guidelines: str | None,
    persona_type: PersonaType = PersonaType.DEFAULT,
    rules: list[Rule] | None = None,
) -> str:
    """Builds custom prompt."""
    custom_prompt = ""

    if persona_type != PersonaType.DEFAULT:
        custom_prompt += "\n\n"
        custom_prompt += get_persona(persona_type).prompt
        custom_prompt += "\n"

    """Handle workspace guidelines and rules"""
    context = feature_flags.get_global_context()
    custom_prompt += get_formatted_rules_prompt(
        workspace_guidelines,
        rules,
        length_limit=WORKSPACE_GUIDELINES_LENGTH_LIMIT.get(context),
    )

    if user_guidelines:
        custom_prompt += "\n\n"
        custom_prompt += """\
Additional user rules:
"""
        custom_prompt += user_guidelines

    if custom_prompt:
        custom_prompt += "\n\n"

    return custom_prompt
