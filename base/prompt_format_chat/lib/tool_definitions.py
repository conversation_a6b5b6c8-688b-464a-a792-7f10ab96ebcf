"""Standard first-party tool definitions for the client."""

import json

from base.prompt_format.common import ToolDefinition

# These are versioned standard tool definitions for tools in the client.
# The appropriate versions of these tools will be selected based on feature flags.
_STANDARD_TOOL_DEFINTIONS = {
    "git-commit-retrieval@20250623": ToolDefinition(
        name="git-commit-retrieval",
        description="""\
This tool is Augment's context engine with git commit history awareness. It:
1. Takes in a natural language description of the code you are looking for;
2. Uses the git commit history as the only context for retrieval;
3. Otherwise functions like the standard codebase-retrieval tool.\
""",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "information_request": {
                        "type": "string",
                        "description": "A description of the information you need.",
                    },
                },
                "required": ["information_request"],
            }
        ),
    ),
    # TODO(arun): move more tool descriptions here.
}


def _key(tool: ToolDefinition):
    """Returns a key for a tool definition."""
    parts = [tool.name]
    for prop_name, prop in sorted(tool.properties.items(), key=lambda x: x[0]):
        parts.append(prop_name)
        parts.append(prop.get("type", "undefined"))

    return ",".join(parts)


def get_standard_definitions() -> dict[str, ToolDefinition]:
    """Returns a dictionary of tool descriptions for the current feature flag context."""
    standard_defns = {}

    def _add(tool: ToolDefinition):
        standard_defns[_key(tool)] = tool

    _add(_STANDARD_TOOL_DEFINTIONS["git-commit-retrieval@20250623"])

    return standard_defns


def standardize_tool_definitions(
    tool_definitions: list[ToolDefinition],
) -> list[ToolDefinition]:
    """Returns a list of tool definitions with standardized versions."""
    standard_defns = get_standard_definitions()
    standard_defn_names = {tool.name for tool in standard_defns.values()}

    # Replace tool definitions with standardized versions if they have the same key.
    return [
        # NOTE(arun): To avoid parsing JSON for every single tool, we only check
        # for tools that share a name with a standardized version.
        standard_defns.get(_key(tool), tool)
        if tool.name in standard_defn_names
        else tool
        for tool in tool_definitions
    ]
