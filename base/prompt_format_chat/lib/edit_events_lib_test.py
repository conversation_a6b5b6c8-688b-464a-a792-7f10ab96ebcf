"""Tests for edit_events_lib.py."""

import dataclasses
from unittest.mock import Mock

import jinja2
import pytest

from base.prompt_format.common import (
    ChatRequestEditEvents,
    ChatRequestFileEdit,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestSingleEdit,
    ChatRequestText,
    ChatRequestToolResult,
    EditEventSource,
    Exchange,
)
from base.prompt_format_chat.lib.edit_events_lib import (
    EditEventsState,
    inject_edit_events,
    is_user_message,
)
from base.prompt_format_chat.lib.system_prompts import (
    EditEventInfo,
    DiffHunkLineRanges,
    EditEventsFormatter,
    EditEventsInfo,
)
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.ranges.range_types import LineRange


class TrivialTokenCounter(TokenCounter):
    """Simple token counter for testing."""

    def count_tokens(self, prompt_chars: str) -> int:
        return len(prompt_chars)


@pytest.fixture
def mock_edit_events_formatter() -> EditEventsFormatter:
    token_counter = TrivialTokenCounter()
    return EditEventsFormatter(
        jinja2.Template(
            "{% if (checkpoint_reverts | length > 0) %}"
            "{{ checkpoint_reverts | length }}\n"
            "{{ num_additional_checkpoint_reverts }}\n"
            "{% endif %}"
            "{% if (user_edits | length > 0) %}"
            "{{ user_edits | length }}\n"
            "{{ num_additional_user_edits }}\n"
            "{% endif %}",
            lstrip_blocks=True,
            trim_blocks=True,
        ),
        token_counter=token_counter,
    )


def _text_node(content: str, node_id: int = 1) -> ChatRequestNode:
    """Helper to create a text node."""
    return ChatRequestNode(
        id=node_id,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content=content),
        tool_result_node=None,
    )


def _tool_result_node(
    tool_use_id: str, content: str, node_id: int = 2
) -> ChatRequestNode:
    """Helper to create a tool result node."""
    return ChatRequestNode(
        id=node_id,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id=tool_use_id,
            content=content,
            is_error=False,
        ),
    )


def _edit_events_node(
    file_edits: list[ChatRequestFileEdit],
    source: EditEventSource = EditEventSource.USER_EDIT,
    node_id: int = 3,
) -> ChatRequestNode:
    """Helper to create an edit events node."""
    return ChatRequestNode(
        id=node_id,
        type=ChatRequestNodeType.EDIT_EVENTS,
        text_node=None,
        tool_result_node=None,
        edit_events_node=ChatRequestEditEvents(
            edit_events=file_edits,
            source=source,
        ),
    )


def _user_edit_node(
    file_edits: list[ChatRequestFileEdit], node_id: int = 3
) -> ChatRequestNode:
    """Helper to create an edit events node."""
    return _edit_events_node(file_edits, EditEventSource.USER_EDIT, node_id)


def _checkpoint_revert_node(
    file_edits: list[ChatRequestFileEdit], node_id: int = 3
) -> ChatRequestNode:
    """Helper to create an edit events node."""
    return _edit_events_node(file_edits, EditEventSource.CHECKPOINT_REVERT, node_id)


def _file_edit(path: str, num_edits: int = 1) -> ChatRequestFileEdit:
    """Helper to create a file edit."""
    edits = [
        ChatRequestSingleEdit(
            before_line_start=i,
            before_text=f"old text {i}\n",
            after_line_start=i,
            after_text=f"new text {i}\n",
        )
        for i in range(num_edits)
    ]
    return ChatRequestFileEdit(path=path, edits=edits)


def _edit_event_info(
    path: str, num_hunks: int = 1, num_diffs: int = 1
) -> EditEventInfo:
    hunk_ranges = (
        [
            DiffHunkLineRanges(
                before_lrange=LineRange(i, i + 1), after_lrange=LineRange(i, i + 1)
            )
            for i in range(num_hunks)
        ]
        if num_hunks > 0
        else None
    )
    diff = (
        "\n".join(
            [
                f"@@ -{i + 1},1 +{i + 1},1 @@\n-old text {i}\n+new text {i}\n"
                for i in range(num_diffs)
            ]
        )
        if num_diffs > 0
        else None
    )
    return EditEventInfo(path=path, hunk_ranges=hunk_ranges, diff=diff)


@pytest.mark.parametrize(
    "state,expected_info",
    [
        pytest.param(
            EditEventsState(),
            {
                "checkpoint_reverts": [],
                "num_additional_checkpoint_reverts": 0,
                "user_edits": [],
                "num_additional_user_edits": 0,
            },
            id="empty_state",
        ),
        pytest.param(
            EditEventsState(
                checkpoint_reverts={"file1.py": _edit_event_info("file1.py")},
                num_additional_checkpoint_reverts=2,
                user_edits={"file2.py": _edit_event_info("file2.py")},
                num_additional_user_edits=3,
            ),
            {
                "checkpoint_reverts": [_edit_event_info("file1.py")],
                "num_additional_checkpoint_reverts": 2,
                "user_edits": [_edit_event_info("file2.py")],
                "num_additional_user_edits": 3,
            },
            id="non_empty_state",
        ),
    ],
)
def test_flush_edit_events_info(state: EditEventsState, expected_info: EditEventsInfo):
    """Test flush_edit_events_info with various states."""
    result = state.flush_edit_events_info()

    assert result == expected_info
    assert state == EditEventsState()


@pytest.mark.parametrize(
    "state,input_message,expected_result,expected_state",
    [
        pytest.param(
            EditEventsState(),
            "Hello world",
            "Hello world",
            EditEventsState(),
            id="string_message",
        ),
        pytest.param(
            EditEventsState(),
            [_text_node("Hello world")],
            [_text_node("Hello world")],
            EditEventsState(),
            id="structured_message",
        ),
        pytest.param(
            EditEventsState(),
            [_user_edit_node([_file_edit("test.py")])],
            [],
            EditEventsState(
                user_edits={"test.py": _edit_event_info("test.py")},
                num_additional_user_edits=0,
            ),
            id="user_edit",
        ),
    ],
)
def test_extract_from_request_message(
    state, input_message, expected_result, expected_state
):
    """Test extract_from_request_message with various inputs."""
    result = state.extract_from_request_message(input_message)

    assert result == expected_result
    assert state == expected_state


@pytest.mark.parametrize(
    "state,exchange,expected_result,expected_state",
    [
        pytest.param(
            EditEventsState(),
            Exchange(request_message="Hello world", response_text="Hi there"),
            Exchange(request_message="Hello world", response_text="Hi there"),
            EditEventsState(),
            id="string_message",
        ),
        pytest.param(
            EditEventsState(),
            Exchange(
                request_message=[_text_node("Hello world")], response_text="Hi there"
            ),
            Exchange(
                request_message=[_text_node("Hello world")], response_text="Hi there"
            ),
            EditEventsState(),
            id="structured_message",
        ),
    ],
)
def test_extract_from_exchange(state, exchange, expected_result, expected_state):
    """Test extract_from_exchange, simple cases"""

    result = state.extract_from_exchange(exchange)

    assert result == expected_result
    assert state == expected_state


@pytest.mark.parametrize(
    "state,nodes,expected_nodes,expected_state",
    [
        pytest.param(
            EditEventsState(),
            [_text_node("Hello"), _user_edit_node([_file_edit("test.py")])],
            [_text_node("Hello")],
            EditEventsState(user_edits={"test.py": _edit_event_info("test.py")}),
            id="user_edit",
        ),
        pytest.param(
            EditEventsState(),
            [_checkpoint_revert_node([_file_edit("test.py")])],
            [],
            EditEventsState(
                checkpoint_reverts={"test.py": _edit_event_info("test.py")}
            ),
            id="checkpoint_revert",
        ),
        pytest.param(
            EditEventsState(path_char_limit=5),
            [_user_edit_node([_file_edit("a" * 10)])],
            [],
            EditEventsState(num_additional_user_edits=1, path_char_limit=5),
            id="path_char_limit",
        ),
        pytest.param(
            EditEventsState(
                checkpoint_reverts={"test.py": _edit_event_info("test.py")},
                items_limit=1,
            ),
            [_checkpoint_revert_node([_file_edit("test2.py")])],
            [],
            EditEventsState(
                checkpoint_reverts={"test.py": _edit_event_info("test.py")},
                num_additional_checkpoint_reverts=1,
                items_limit=1,
            ),
            id="items_limit",
        ),
        pytest.param(
            EditEventsState(),
            [_edit_events_node([_file_edit("test.py")], EditEventSource.UNSPECIFIED)],
            [],
            EditEventsState(),
            id="unspecified_source",
        ),
        pytest.param(
            EditEventsState(user_edits={"test.py": _edit_event_info("test.py")}),
            [_user_edit_node([_file_edit("test.py")])],
            [],
            EditEventsState(user_edits={"test.py": _edit_event_info("test.py")}),
            id="same_file",
        ),
        pytest.param(
            EditEventsState(hunk_limit=1),
            [_user_edit_node([_file_edit("test.py", num_edits=2)])],
            [],
            EditEventsState(
                hunk_limit=1,
                user_edits={
                    "test.py": _edit_event_info("test.py", num_hunks=0, num_diffs=0)
                },
            ),
            id="hunk_limit",
        ),
        pytest.param(
            EditEventsState(diff_char_limit=10),
            [_user_edit_node([_file_edit("test.py", num_edits=2)])],
            [],
            EditEventsState(
                diff_char_limit=10,
                user_edits={
                    "test.py": _edit_event_info("test.py", num_hunks=2, num_diffs=0)
                },
            ),
            id="diff_limit",
        ),
    ],
)
def test_extract_from_exchange_nodes(state, nodes, expected_nodes, expected_state):
    """Test extract_from_exchange method with various scenarios."""
    if not expected_nodes:
        response_text = []
    else:
        response_text = "Hi there"

    exchange = Exchange(request_message=nodes, response_text=response_text)
    result = state.extract_from_exchange(exchange)

    assert state == expected_state
    if expected_nodes:
        assert result is not None
        assert result.request_message == expected_nodes
        assert result.response_text == "Hi there"
    else:
        assert result is None


@pytest.mark.parametrize(
    "input_message,expected",
    [
        ("Hello world", True),
        ("", False),
        ([], False),
        ([_text_node("Hello"), _text_node("World")], True),
        ([_text_node("Hello"), _tool_result_node("tool_1", "result")], False),
        ([_checkpoint_revert_node([_file_edit("test.py")])], False),
        ([_checkpoint_revert_node([_file_edit("test.py")]), _text_node("Hello")], True),
        (Exchange("Hello world", "Hi there"), True),
        (Exchange([_tool_result_node("tool_1", "result")], "Hi there"), False),
    ],
)
def test_is_user_message(input_message, expected):
    """Test is_user_message with various inputs."""
    result = is_user_message(input_message)

    assert result == expected


@pytest.mark.parametrize(
    "chat_history,request_message,expected_history,expected_message,expected_tokens",
    [
        pytest.param([], "Hello", [], "Hello", 0, id="empty_history"),
        pytest.param(
            [Exchange([_text_node("a"), _user_edit_node([_file_edit("x")])], "b")],
            [_text_node("c"), _user_edit_node([_file_edit("y")])],
            [Exchange([_text_node("1\n0\n", node_id=2), _text_node("a")], "b")],
            [_text_node("1\n0\n", node_id=2), _text_node("c")],
            6,
            id="basic",
        ),
        pytest.param(
            [
                Exchange([_user_edit_node([_file_edit("x")])], []),
                Exchange(
                    [_checkpoint_revert_node([_file_edit("y"), _file_edit("z")])], []
                ),
            ],
            "e",
            [],
            "2\n0\n1\n0\ne",
            7,
            id="multiple",
        ),
        pytest.param(
            [
                Exchange([_user_edit_node([_file_edit("x"), _file_edit("y")])], []),
                Exchange([_tool_result_node("tool_1", "result")], []),
                Exchange(
                    [_text_node("a"), _checkpoint_revert_node([_file_edit("x")])], "b"
                ),
            ],
            "d",
            [
                Exchange([_tool_result_node("tool_1", "result")], []),
                Exchange([_text_node("1\n0\n2\n0\n", node_id=2), _text_node("a")], "b"),
            ],
            "d",
            7,
            id="tool_results",
        ),
    ],
)
def test_inject_edit_events(
    chat_history,
    request_message,
    expected_history,
    expected_message,
    expected_tokens,
    mock_edit_events_formatter,
):
    """Test inject_edit_events with various scenarios."""
    token_counter = TrivialTokenCounter()

    updated_history, updated_message, token_count = inject_edit_events(
        chat_history=chat_history,
        request_message=request_message,
        token_counter=token_counter,
        edit_events_prompt=mock_edit_events_formatter,
    )

    assert updated_history == expected_history
    assert updated_message == expected_message
    assert token_count == expected_tokens
