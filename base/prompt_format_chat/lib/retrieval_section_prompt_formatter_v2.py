"""Prompt formatter to format section with retrieved chunks."""

import uuid
from collections import OrderedDict
from enum import Enum
from typing import Iterable

from base.prompt_format.common import (
    ChatRequestN<PERSON>,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatR<PERSON>ultNodeType,
    ChatResultToolUse,
    Exchange,
)
from base.prompt_format_chat.lib.abstract_formatted_file import AbstractFormattedFile
from base.prompt_format_chat.lib.documentation_formatted_file import (
    DocumentationFormattedFile,
)
from base.prompt_format_chat.lib.formatted_file_v2 import FormattedFileV2
from base.prompt_format_chat.lib.string_formatter import StringFormatter
from base.prompt_format_chat.lib.token_counter import Token<PERSON>ounter
from base.prompt_format_chat.lib.truncation_utils import (
    last_approx_tokens,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    PromptChunk,
    filter_overlapping_retrieved_chunks_v2,
)

# NOTE: The ChunkOrigin enum is currently defined in
# services/completion_host/single_model_server/enums.py,
# but we cannot import it here. As a temporary workaround,
# we are using string constants  for user-guided and dense retriever origins.
# If a chunk does not originate from one of these,
# an error will be raised.
USER_GUIDED_RETRIEVER_ORIGIN = "user_guided_retriever"
DENSE_RETRIEVER_ORIGIN = "dense_retriever"

USER_GUIDED_TEMPLATE = """\
I currently have the file `{path}` open, and I am actively working on it. Here is an excerpt from it:

```
{content}
```

"""

USER_GUIDED_TEMPLATE_V3 = """\
I currently have the file `{path}` open, and I am actively working on it. Here is an excerpt from it:

<augment_code_snippet path="{path}" mode="EXCERPT">
````
{content}
````
</augment_code_snippet>

"""

USER_GUIDED_TEMPLATE_V4 = """\
The user currently has the file `{path}` open and is actively working on it. Here is an excerpt from it:

```
{content}
```

"""

GENERAL_TEMPLATE = """\
Here is an excerpt from the file `{path}`:

```
{content}
```

"""

GENERAL_TEMPLATE_V3 = """\
Here is an excerpt from the file `{path}`:

<augment_code_snippet path="{path}" mode="EXCERPT">
````
{content}
````
</augment_code_snippet>

"""

DOCSET_TEMPLATE = """\
Here is an excerpt from the documentation for `{path}`:

```
{content}
```

"""

DOCSET_TEMPLATE_V3 = """\
Here is an excerpt from the documentation for `{path}`:

<augment_code_snippet path="" mode="EXCERPT">
````
{content}
````
</augment_code_snippet>

"""

RETRIEVAL_RESPONSE_MESSAGE = "Understood. I'll refer to the excerpts for context, and ignore them for general questions."

RETRIEVAL_RESPONSE_MESSAGE_V6 = """\
Understood.
1. I will refer to the excerpts for context, and ignore them for general questions.
2. I understand that the retrieved chunks are up-to-date and I should rely on them instead of codeblocks in the subsequent messages to understand the current status of the codebase, despite this message appearing to be the first in the conversation.
3. If I cannot find the relevant code in the previous messages, it means the retrieval did not returen the relevant code, but I still have access to the whole codebase and I should maintain that position.
"""

HEADER = ""

HEADER_V6 = """\
Below are the retrieval result provided by Augment, the best codebase context engine in the world.
1. Augment has a real-time index of the codebase, so the results are up-to-date as of the newest message in this conversation thread, despite this message appearing as the first message in the thread. (This message has been retroactively updated to the moment where the user sent you the newest message in the thread.) You should rely on the retrieval results in this message, instead of codeblocks in subsequent messges (except selected code, which is at least as fresh if not fresher than this message's content) to understand the current state of the codebase.
2. Despite being the best context engine in the world, Augment is not perfect. When you fail to find relevant code in this message, it means your search failed to return the relevant code. If you have access to a `codebsase-retrieval` tool, you can use it to retry the search. If you do not have access to that tool, you can try asking the user to help you by `@`ing the relevant file(s), while clearing explaining that a search did not return the expected code, avoiding an impression that you did not have access to the codebase.

"""

OTHER_RETRIEVAL_HEADER = "Below are some relevant files from my project.\n\n"

OTHER_RETRIEVAL_HEADER_V4 = "Below are some relevant files from the user's project.\n\n"


def get_header(version: int = 2) -> str:
    if version in {2, 3, 4}:
        return HEADER
    elif version == 6:
        return HEADER_V6
    else:
        raise ValueError(f"Unknown retrieval section version: {version}")


def get_other_retrieval_header(version: int = 2) -> str:
    if version in {2, 3}:
        return OTHER_RETRIEVAL_HEADER
    elif version in {4, 6}:
        return OTHER_RETRIEVAL_HEADER_V4
    else:
        raise ValueError(f"Unknown retrieval section version: {version}")


def get_retrieval_response_message(version: int = 2) -> str:
    if version in {2, 3, 4}:
        return RETRIEVAL_RESPONSE_MESSAGE
    elif version == 6:
        return RETRIEVAL_RESPONSE_MESSAGE_V6
    else:
        raise ValueError(f"Unknown retrieval section version: {version}")


class RetrievalSectionType(Enum):
    USER_GUIDED = "user_guided"
    GENERAL = "general"
    DOCSET = "docset"


class RetrievalSectionPromptFormatterV2:
    """Formats retrieval sections using provided tokenizer and templates."""

    PATH_PLACEHOLDER = "path"
    CONTENT_PLACEHOLDER = "content"

    def __init__(
        self,
        single_file_template: dict[RetrievalSectionType, str],
        token_counter: TokenCounter,
    ):
        """Initialize with token counter, template, max path tokens, and budget."""
        self.token_counter = token_counter
        self.single_file_formatter = dict[RetrievalSectionType, StringFormatter]()
        for section_type, template in single_file_template.items():
            self.single_file_formatter[section_type] = StringFormatter(
                template,
                token_counter=self.token_counter,
            )

    def _get_chunk_type(self, chunk: PromptChunk) -> RetrievalSectionType:
        if chunk.documentation_metadata:
            return RetrievalSectionType.DOCSET
        elif chunk.origin == USER_GUIDED_RETRIEVER_ORIGIN:
            return RetrievalSectionType.USER_GUIDED
        else:
            return RetrievalSectionType.GENERAL

    def _format_and_count_tokens(
        self,
        path: str,
        formatted_file: AbstractFormattedFile,
        retrieval_section_type: RetrievalSectionType,
    ) -> int:
        """Format a single file and count its tokens."""
        file_str, tok_ct = formatted_file.get_file_str_and_tok_ct()
        # use docset name instead of path for documentation chunks
        if isinstance(formatted_file, DocumentationFormattedFile):
            path = formatted_file.get_docset_name()

        return self.single_file_formatter[
            retrieval_section_type
        ].format_and_count_tokens(
            values={
                self.PATH_PLACEHOLDER: path,
                self.CONTENT_PLACEHOLDER: file_str,
            },
            values_token_counts={self.CONTENT_PLACEHOLDER: tok_ct},
        )

    def format(
        self,
        chunks: Iterable[PromptChunk],
        max_path_tokens: int,
        max_total_tokens: int,
        max_external_context_tokens: int = 0,
        overflow_external_context: bool = False,
    ) -> tuple[str, list[PromptChunk]]:
        """Format the retrieval section with the given chunks.

        Args:
            chunks: The chunks to format.
            max_path_tokens: The maximum number of tokens to use for the path.
            max_total_tokens: The maximum number of tokens to use for the retrieval section.
            max_external_context_tokens: The number of tokens of the explicit external context to include that is allowed to overflow.
                Overflow will only happen if there are more relevant in-repo chunks that has taken the retrieval budget,
                and the amount of explicit external context is less than this number.
            overflow_external_context: Whether to overflow the external context on top of the existing total budget.

        Returns:
            The formatted retrieval section, and the chunks that were used.

        Note:
        - Input chunks are presumed to already be sorted by score.
        - Two modes for handling external context exists:
        Legacy mode (overflow_external_context=False):
            - The total budget is the limit imposed on both workspace and external contents.
            - We simply reply on the scores to keep the relatively more relevant chunks in the prompt, whether they are in-repo or external.
            - This is the old behavior of claude v3 chat models.
        Overflow mode (overflow_external_context=True):
            - The total budget is the limit imposed on workspace contents only.
            - The external context budget is the limit imposed on external contents, which is allowed to overflow beyond the limit.
            - The total prompt may exceed the total context limit if there are many relevant chunks from both repo and external sources.
            - Workspace chunks are never displaced by external context chunks.  We get the same workspace chunks as when there are no external context.
            - External chunks are cut-off based on score to make sure that all external chunks in the prompt is at least as relevant as the lest
              relevant workspace chunk.
            - This is the new behavior of claude v3 overflow chat models.
        """

        workspace_tokens = 0
        external_context_tokens = 0
        chunk_ids: dict[str, str] = {}
        chunk_types: dict[str, RetrievalSectionType] = {}
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()

        # Whenever the workspace file is full, we stop adding external context chunks too.
        # This is to prevent completely irrelevant external context chunks from being added.
        # ====  overflow mode ====
        # because in overflow mode, the external context
        # is a separate budget on top of the normal budget, we want to keep it as small as
        # possible and include only the most relevant chunks.
        # Therefore here we enforce that all external chunks included in the prompt must be more
        # relevant than any workspace chunk that was excluded from the prompt.
        # So, upon the first workspace chunk that is not included, we set workspace_file_full to
        # True and after this point no external context chunks will be added.
        # ==== legacy mode ====
        # in legacy mode the above effect is also True but it is trivially achieved because
        # both types of chunks are in the same total budget, so this variable is not needed.
        workspace_file_full: bool = False

        for chunk in chunks:
            chunk_type = self._get_chunk_type(chunk)
            is_workspace_file = chunk_type != RetrievalSectionType.DOCSET

            # Group chunks together by blob name if it exists
            chunk_id = chunk.blob_name
            if chunk_id == "":
                # Group docset chunks by page id
                if chunk.documentation_metadata:
                    chunk_id = chunk.documentation_metadata.page_id
                # If a chunk has an empty blob name and no metadata, we assume
                # we don't want to merge it with any other chunks.
                # To this end, we generate a unique id.
                else:
                    chunk_id = str(uuid.uuid4())
                    assert chunk_id not in chunk_ids

            if chunk_id not in chunk_ids:
                chunk_ids[chunk_id] = last_approx_tokens(
                    chunk.path, max_path_tokens, self.token_counter
                )
                chunk_types[chunk_id] = chunk_type

            if chunk_id in formatted_files:
                old_formatted_file = formatted_files[chunk_id]
                old_n_tokens = self._format_and_count_tokens(
                    chunk_ids[chunk_id], old_formatted_file, chunk_type
                )
            else:
                old_formatted_file = (
                    FormattedFileV2(self.token_counter)
                    if is_workspace_file
                    else DocumentationFormattedFile(self.token_counter)
                )
                old_n_tokens = 0

            # Attempt to add the chunk to the formatted file noncommitally
            new_formatted_file = old_formatted_file.add_chunk(chunk)
            new_n_tokens = self._format_and_count_tokens(
                chunk_ids[chunk_id], new_formatted_file, chunk_type
            )

            # For workspace files, simply check total budget
            if is_workspace_file:
                if workspace_tokens + new_n_tokens - old_n_tokens <= max_total_tokens:
                    workspace_tokens += new_n_tokens - old_n_tokens
                    formatted_files[chunk_id] = new_formatted_file
                else:
                    workspace_file_full = True
            # For external context, legacy and overflow mode behaves a bit different.
            elif not overflow_external_context:
                # In legacy mode, both workspace and external context are added to the total budget.
                # external context has a separate maximum budget.
                if (
                    workspace_tokens + new_n_tokens - old_n_tokens <= max_total_tokens
                    and external_context_tokens + new_n_tokens - old_n_tokens
                    <= max_external_context_tokens
                ):
                    workspace_tokens += new_n_tokens - old_n_tokens
                    external_context_tokens += new_n_tokens - old_n_tokens
                    formatted_files[chunk_id] = new_formatted_file
            # In overflow mode, external context is not added to the total budget, and can go beyond
            # the total budget; however, they are cut off if the workspace chunks started to be cut off.
            elif (
                not workspace_file_full
                and external_context_tokens + new_n_tokens - old_n_tokens
                <= max_external_context_tokens
            ):
                external_context_tokens += new_n_tokens - old_n_tokens
                formatted_files[chunk_id] = new_formatted_file

        section_str, prompt_chunks = "", []
        for k, formatted_file in formatted_files.items():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            path = chunk_ids[k]
            # use docset name instead of path for documentation chunks
            if isinstance(formatted_file, DocumentationFormattedFile):
                path = formatted_file.get_docset_name()
            section_str += self.single_file_formatter[chunk_types[k]].format(
                values={
                    self.PATH_PLACEHOLDER: path,
                    self.CONTENT_PLACEHOLDER: file_str,
                },
            )
            prompt_chunks.extend(formatted_files[k].get_chunks())

        return section_str, prompt_chunks


def sanity_check_retrieved_chunks(retrieved_chunks: list[PromptChunk]):
    """Sanity check retrieval origin."""
    for chunk in retrieved_chunks:
        if chunk.origin not in {
            USER_GUIDED_RETRIEVER_ORIGIN,
            DENSE_RETRIEVER_ORIGIN,
        }:
            raise ValueError(
                f"Unknown chunk origin: {chunk.origin}. "
                f"Must be one of {USER_GUIDED_RETRIEVER_ORIGIN} "
                f"or {DENSE_RETRIEVER_ORIGIN}."
            )


class RetrievalSectionBuilder:
    """Builds the retrieval section."""

    def __init__(
        self,
        token_counter: TokenCounter,
        token_apportionment: ChatTokenApportionment,
        retrieval_formatter: RetrievalSectionPromptFormatterV2,
        retrieval_response_message: str,
        version: int = 2,
    ):
        self.token_counter = token_counter
        self.token_apportionment = token_apportionment
        self.retrieval_formatter = retrieval_formatter
        self.retrieval_response_message = retrieval_response_message
        self.version = version

    def _get_user_guided_budget(
        self,
        token_budget: int,
        n_user_guided_files: int,
        header_token_count: int,
    ):
        user_guided_budget = token_budget - header_token_count
        if self.token_apportionment.retrieval_len_per_each_user_guided_file >= 0:
            # We scale the budget of the user guided retrieval proportionally
            # to the number of files user attached.
            user_guided_budget = min(
                user_guided_budget,
                self.token_apportionment.retrieval_len_per_each_user_guided_file
                * n_user_guided_files,
            )
        if self.token_apportionment.retrieval_len_for_user_guided >= 0:
            user_guided_budget = min(
                user_guided_budget,
                self.token_apportionment.retrieval_len_for_user_guided,
            )
        return user_guided_budget

    def get_retrieval_section(
        self,
        prompt_input: ChatPromptInput,
        clipped_prefix: str,
        clipped_suffix: str,
        token_budget: int,
    ) -> tuple[str, list[PromptChunk]]:
        """Get the tokens for retrieved chunks."""
        retrieved_chunks = list(prompt_input.retrieved_chunks)
        sanity_check_retrieved_chunks(retrieved_chunks)

        # Estimate the number of files user has attached.
        n_user_guided_files = len(
            {
                chunk.blob_name
                for chunk in retrieved_chunks
                if chunk.origin == USER_GUIDED_RETRIEVER_ORIGIN
            }
        )

        # TODO(yury): We need to merge chunks and prefix/suffix together
        # instead of simply filtering the overlapping chunk.
        # Will address that in the follow up PR.
        filtered_retrieved_chunks = filter_overlapping_retrieved_chunks_v2(
            prompt_input,
            clipped_prefix=clipped_prefix,
            clipped_suffix=clipped_suffix,
            retrieved_chunks=prompt_input.retrieved_chunks,
        )
        filtered_retrieved_chunks = list(filtered_retrieved_chunks)

        # First, we include chunks from user-guided retrieval.
        filtered_user_guided_chunks = [
            chunk
            for chunk in filtered_retrieved_chunks
            if chunk.origin == USER_GUIDED_RETRIEVER_ORIGIN
        ]

        header = get_header(version=self.version)
        header_token_count = self.token_counter.count_tokens(header)

        user_guided_budget = self._get_user_guided_budget(
            token_budget, n_user_guided_files, header_token_count
        )
        user_guided_str, user_guided_chunks_in_prompt = self.retrieval_formatter.format(
            filtered_user_guided_chunks,
            self.token_apportionment.path_len,
            user_guided_budget,
        )

        # We avoid including user-guided files in the general retrieval section
        # to prevent problems. The issue is that different indexers might give us
        # different versions of the same file, which could mess up the results if
        # we merge them.
        # TODO(yury): check if the user-guided and general retrievals are
        # the same by comparing blob names. But this is extra work, so we'll
        # leave that for later.
        user_guided_files = {chunk.path for chunk in user_guided_chunks_in_prompt}

        # Second, we include chunks from the rest of the repository.
        other_retrieval_chunks = []
        for chunk in filtered_retrieved_chunks:
            if (
                chunk.origin != USER_GUIDED_RETRIEVER_ORIGIN
                and chunk.path not in user_guided_files
            ):
                other_retrieval_chunks.append(chunk)

        other_retrieval_header = get_other_retrieval_header(version=self.version)

        other_budget = (
            token_budget
            - header_token_count
            - self.token_counter.count_tokens(user_guided_str)
            - self.token_counter.count_tokens(other_retrieval_header)
        )
        if self.token_apportionment.retrieval_len >= 0:
            other_budget = min(other_budget, self.token_apportionment.retrieval_len)

        if prompt_input.external_source_ids:
            external_budget = self.token_apportionment.explicit_external_context_len
        else:
            external_budget = self.token_apportionment.implicit_external_context_len

        other_retrieval_str, other_retrieved_chunks_in_prompt = (
            self.retrieval_formatter.format(
                other_retrieval_chunks,
                self.token_apportionment.path_len,
                other_budget,
                external_budget,
                overflow_external_context=self.token_apportionment.overflow_external_context,
            )
        )

        if len(other_retrieved_chunks_in_prompt) > 0:
            other_retrieval_str = other_retrieval_header + other_retrieval_str
        else:
            other_retrieval_str = ""

        # We first include the user-guided section for better caching.
        retrieval_str = header + user_guided_str + other_retrieval_str
        retrieved_chunks_in_prompt = (
            user_guided_chunks_in_prompt + other_retrieved_chunks_in_prompt
        )

        if len(retrieved_chunks_in_prompt) == 0:
            # We were not able to add any retrieved chunks to the prompt.
            # So there's no need to add the retrieval section to the prompt.
            return "", []

        return retrieval_str, retrieved_chunks_in_prompt

    def get_retrieval_section_as_exchanges(
        self,
        prompt_input: ChatPromptInput,
        clipped_prefix: str,
        clipped_suffix: str,
        token_budget: int,
    ) -> tuple[list[Exchange], list[PromptChunk]]:
        response_token_ct = self.token_counter.count_tokens(
            self.retrieval_response_message
        )
        retrieval_str, retrieved_chunks_in_prompt = self.get_retrieval_section(
            prompt_input,
            clipped_prefix,
            clipped_suffix,
            token_budget - response_token_ct,
        )
        if retrieval_str != "":
            if self.token_apportionment.retrieval_as_tool:
                tool_use_id = str(uuid.uuid4())
                tool_use_parameters = {
                    "information_request": "Find relevant code in the code base."
                }
                exchanges = [
                    Exchange(
                        request_message=[],
                        response_text=[
                            ChatResultNode(
                                0,
                                ChatResultNodeType.TOOL_USE,
                                "",
                                ChatResultToolUse(
                                    "codebase-retrieval",
                                    tool_use_parameters,
                                    tool_use_id,
                                ),
                            )
                        ],
                    ),
                    Exchange(
                        request_message=[
                            ChatRequestNode(
                                0,
                                ChatRequestNodeType.TOOL_RESULT,
                                None,
                                ChatRequestToolResult(
                                    tool_use_id,
                                    retrieval_str,
                                    False,
                                ),
                            )
                        ],
                        response_text=[],
                    ),
                ]
            else:
                exchanges = [
                    Exchange(
                        request_message=retrieval_str,
                        response_text=self.retrieval_response_message,
                    )
                ]
        else:
            exchanges = []
            assert (
                len(retrieved_chunks_in_prompt) == 0
            ), f"Retrieved chunks should empty but found {len(retrieved_chunks_in_prompt)}"

        return exchanges, retrieved_chunks_in_prompt


def get_binks_retrieval_section_bulder(
    token_counter: TokenCounter,
    token_apportionment: ChatTokenApportionment,
    version: int = 2,
) -> RetrievalSectionBuilder:
    if version == 2:
        templates = {
            RetrievalSectionType.USER_GUIDED: USER_GUIDED_TEMPLATE,
            RetrievalSectionType.GENERAL: GENERAL_TEMPLATE,
            RetrievalSectionType.DOCSET: DOCSET_TEMPLATE,
        }
    elif version == 3:
        templates = {
            RetrievalSectionType.USER_GUIDED: USER_GUIDED_TEMPLATE_V3,
            RetrievalSectionType.GENERAL: GENERAL_TEMPLATE_V3,
            RetrievalSectionType.DOCSET: DOCSET_TEMPLATE_V3,
        }
    elif version in {4, 6}:
        templates = {
            RetrievalSectionType.USER_GUIDED: USER_GUIDED_TEMPLATE_V4,
            RetrievalSectionType.GENERAL: GENERAL_TEMPLATE,
            RetrievalSectionType.DOCSET: DOCSET_TEMPLATE,
        }
    else:
        raise ValueError(f"Unknown retrieval section version: {version}")
    retrieval_formatter = RetrievalSectionPromptFormatterV2(templates, token_counter)

    return RetrievalSectionBuilder(
        token_counter,
        token_apportionment,
        retrieval_formatter,
        get_retrieval_response_message(version=version),
        version=version,
    )
