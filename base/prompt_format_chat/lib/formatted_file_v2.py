"""Implements a FormattedFile that combines retrieved chunks from the same file."""

import bisect
import dataclasses

from base.prompt_format_chat.lib.abstract_formatted_file import AbstractFormattedFile
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.prompt_format_chat.prompt_formatter import PromptChunk


@dataclasses.dataclass(frozen=True)
class FormattedFileV2(AbstractFormattedFile):
    """A class to format chunks from the same file.

    The implementation focuses on efficiency, avoiding redundant tokenization
    and minimizing unnecessary copying of objects.

    Note: Currently, the class assumes that chunks do not overlap. This will be
    corrected in a future update.
    """

    token_counter: TokenCounter
    sorted_chunks: tuple[PromptChunk, ...] = dataclasses.field(default_factory=tuple)

    def get_file_str_and_tok_ct(
        self, add_line_numbers: bool = False
    ) -> tuple[str, int]:
        separator = "...\n"
        separator_tok_ct = self.token_counter.count_tokens(separator)
        file_str = ""
        total_tok_ct = 0
        prev_chunk_char_end = 0
        prev_chunk_char_start = 0

        for chunk in self.sorted_chunks:
            # confirm sorted invariant
            assert prev_chunk_char_start <= chunk.char_start
            prev_chunk_char_start = chunk.char_start

            # Add tokens for the chunk
            if prev_chunk_char_end <= chunk.char_start:
                chunk_tok_ct = self._get_chunk_tk_count(
                    chunk.text, chunk.char_start, chunk.char_end, chunk.path
                )
                if prev_chunk_char_end < chunk.char_start:
                    # The separator is a stand-in for the missing code
                    file_str += separator
                    total_tok_ct += separator_tok_ct

                # Add the chunk text with or without line numbers
                if add_line_numbers and chunk.line_start >= 0:
                    file_str += _add_line_numbers(chunk.text, chunk.line_start)
                else:
                    file_str += chunk.text

                total_tok_ct += chunk_tok_ct
                prev_chunk_char_end = chunk.char_end
            elif prev_chunk_char_end >= chunk.char_end:
                # Nothing to add if the chunk is entirely contained in the previous chunk
                continue
            else:
                # If the chunk is not entirely contained in the previous chunk,
                # we just add the part that is not overlapping
                num_overlapping_chars = prev_chunk_char_end - chunk.char_start
                non_overlapping_text = chunk.text[num_overlapping_chars:]
                chunk_tok_ct = self._get_chunk_tk_count(
                    non_overlapping_text,
                    prev_chunk_char_end,
                    chunk.char_end,
                    chunk.path,
                )

                # Add the non-overlapping part with or without line numbers
                if add_line_numbers and chunk.line_start >= 0:
                    # Count how many lines are in the overlapping part to adjust the line number
                    lines_in_overlap = chunk.text[:num_overlapping_chars].count("\n")
                    file_str += _add_line_numbers(
                        non_overlapping_text, chunk.line_start + lines_in_overlap
                    )
                else:
                    file_str += non_overlapping_text

                total_tok_ct += chunk_tok_ct
                prev_chunk_char_end = chunk.char_end

        # We don't know whether we have included an entire file,
        # so we add a separator to ensure proper formatting.
        file_str += "..."
        total_tok_ct += self.token_counter.count_tokens("...")
        return file_str, total_tok_ct

    def add_chunk(self, chunk: PromptChunk) -> "FormattedFileV2":
        index = bisect.bisect_left(self.sorted_chunks, chunk)
        return FormattedFileV2(
            token_counter=self.token_counter,
            sorted_chunks=(
                self.sorted_chunks[:index] + (chunk,) + self.sorted_chunks[index:]
            ),
        )

    def __getitem__(self, index: int) -> int:
        # NOTE: We add this method to make the class compatible
        # we Sequence[int] protocol for `StringFormatter` class
        # in the `base/prompt_format_chat/tokenized_string_formatter.py` file.
        # However, `StringFormatter` only uses __len__ and __iter__ method,
        # so we are not implementing __getitem__ for now.
        raise NotImplementedError()


def _add_line_numbers(text: str, start_line_number: int = 0, rpadding: int = 6) -> str:
    """Add line numbers to each line of text."""
    lines = text.splitlines(keepends=True)
    result = ""
    for i, line in enumerate(lines):
        line_number = start_line_number + i + 1  # 1-based line number
        result += f"{str(line_number).rjust(rpadding)}\t{line}"
    return result
