"""Abstract class for a FormattedFile. Each type of context has its own implementation."""

import dataclasses
from abc import abstractmethod
from typing import Protocol

from cachetools import cached
from cachetools.keys import hashkey

from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.prompt_format_chat.prompt_formatter import PromptChunk


@dataclasses.dataclass(frozen=True)
class AbstractFormattedFile(Protocol):
    """A class to format chunks from the same file.

    This class simplifies formatting the prompt while managing token limits,
    especially when dealing with multiple, potentially overlapping chunks.

    FormattedFile formats the text first, then counts the tokens to accurately
    determine the token count.

    The class allows you to add new chunks and immediately see how they affect
    the total number of tokens. If the increase is too significant, it's easy
    to undo changes, thanks to the class's immutable design; adding a chunk
    returns a new instance of the class.
    """

    token_counter: TokenCounter
    sorted_chunks: tuple[PromptChunk, ...] = dataclasses.field(default_factory=tuple)

    @abstractmethod
    def get_file_str_and_tok_ct(
        self, add_line_numbers: bool = False
    ) -> tuple[str, int]:
        """Returns the formatted string of contents for the file and the number of tokens.

        Args:
            add_line_numbers: If True, add line numbers to each line of the output.
        """
        raise NotImplementedError()

    @abstractmethod
    def add_chunk(self, chunk: PromptChunk) -> "AbstractFormattedFile":
        """Add a chunk to this file."""
        raise NotImplementedError()

    @cached(
        cache={},
        key=lambda self, text, chunk_char_start, chunk_char_end, chunk_path: hashkey(
            chunk_char_start, chunk_char_end, chunk_path
        ),
    )
    def _get_chunk_tk_count(
        self, text, chunk_char_start: int, chunk_char_end: int, chunk_path: str
    ) -> int:
        """Cache for tokenization."""
        return self.token_counter.count_tokens(text)

    def __len__(self) -> int:
        """Get the total number of tokens in this file."""
        # Using `True` as an over-estimate.
        return self.get_file_str_and_tok_ct(add_line_numbers=True)[1]

    def __getitem__(self, index: int) -> int:
        """Get the token at the given index."""
        # NOTE: We add this method to make the class compatible
        # we Sequence[int] protocol for `StringFormatter` class
        # in the `base/prompt_format_chat/tokenized_string_formatter.py` file.
        # However, `StringFormatter` only uses __len__ and __iter__ method,
        # so we are not implementing __getitem__ for now.
        raise NotImplementedError()

    def get_chunks(self) -> list[PromptChunk]:
        """Get the chunks for this file."""
        return [chunk for chunk in self.sorted_chunks]
