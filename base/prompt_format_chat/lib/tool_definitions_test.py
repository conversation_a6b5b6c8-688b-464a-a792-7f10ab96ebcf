"""Tests for tool_definitions.py."""

import json

import pytest

from base.prompt_format.common import ToolDefinition
from base.prompt_format_chat.lib.tool_definitions import (
    _STANDARD_TOOL_DEFINTIONS,
    _key,
    get_standard_definitions,
    standardize_tool_definitions,
)


class TestToolDefinitionsKey:
    """Tests for the _key function."""

    def test_key_with_simple_tool(self):
        """Test _key function with a simple tool definition."""
        tool = ToolDefinition(
            name="test-tool",
            description="A test tool",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {
                        "param1": {"type": "string"},
                        "param2": {"type": "number"},
                    },
                    "required": ["param1"],
                }
            ),
        )

        expected_key = "test-tool,param1,string,param2,number"
        assert _key(tool) == expected_key

    def test_key_with_no_properties(self):
        """Test _key function with a tool that has no properties."""
        tool = ToolDefinition(
            name="simple-tool",
            description="A simple tool",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {},
                }
            ),
        )

        expected_key = "simple-tool"
        assert _key(tool) == expected_key

    def test_key_sorting_properties(self):
        """Test that _key sorts properties alphabetically."""
        tool = ToolDefinition(
            name="sorting-tool",
            description="A tool to test sorting",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {
                        "zebra": {"type": "string"},
                        "alpha": {"type": "string"},
                        "beta": {"type": "string"},
                    },
                }
            ),
        )

        expected_key = "sorting-tool,alpha,string,beta,string,zebra,string"
        assert _key(tool) == expected_key


class TestStandardizeToolDefinitions:
    """Tests for standardize_tool_definitions function."""

    def test_standardize_empty_list(self):
        """Test standardizing an empty list of tool definitions."""
        result = standardize_tool_definitions([])
        assert result == []

    def test_standardize_with_no_matching_tools(self):
        """Test standardizing tools that don't match any standard definitions."""
        custom_tool = ToolDefinition(
            name="custom-tool",
            description="A custom tool",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {"param": {"type": "string"}},
                    "required": ["param"],
                }
            ),
        )

        result = standardize_tool_definitions([custom_tool])
        assert len(result) == 1
        assert result[0] == custom_tool

    def test_standardize_with_matching_tool(self):
        """Test standardizing a tool that matches a standard definition."""
        # Create a tool with the same key as the git-commit-retrieval tool
        custom_git_tool = ToolDefinition(
            name="git-commit-retrieval",
            description="Custom description",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {
                        "information_request": {"type": "string"},
                    },
                    "required": ["information_request"],
                }
            ),
        )
        standard_git_tool = _STANDARD_TOOL_DEFINTIONS["git-commit-retrieval@20250623"]

        result = standardize_tool_definitions([custom_git_tool])
        assert len(result) == 1

        # Should be replaced with the standard definition
        standardized_tool = result[0]
        assert standardized_tool.name == "git-commit-retrieval"
        assert standardized_tool == standard_git_tool

    def test_standardize_mixed_tools(self):
        """Test standardizing a mix of standard and custom tools."""
        custom_git_tool = ToolDefinition(
            name="git-commit-retrieval",
            description="Custom description",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {
                        "information_request": {"type": "string"},
                    },
                    "required": ["information_request"],
                }
            ),
        )

        custom_tool = ToolDefinition(
            name="custom-tool",
            description="A custom tool",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {"param": {"type": "string"}},
                    "required": ["param"],
                }
            ),
        )

        standard_git_tool = _STANDARD_TOOL_DEFINTIONS["git-commit-retrieval@20250623"]

        result = standardize_tool_definitions([custom_git_tool, custom_tool])
        assert len(result) == 2

        # First tool should be standardized
        assert result[0] == standard_git_tool
        # Second tool should remain unchanged
        assert result[1] == custom_tool

    def test_standardize_with_different_properties_same_name(self):
        """Test that tools with same name but different properties are not standardized."""
        different_git_tool = ToolDefinition(
            name="git-commit-retrieval",
            description="Custom description",
            input_schema_json=json.dumps(
                {
                    "type": "object",
                    "properties": {
                        "different_param": {"type": "string"},
                    },
                    "required": ["different_param"],
                }
            ),
        )

        result = standardize_tool_definitions([different_git_tool])
        assert len(result) == 1

        # Should NOT be replaced because the key is different
        assert result[0] == different_git_tool
        assert result[0].description == "Custom description"
