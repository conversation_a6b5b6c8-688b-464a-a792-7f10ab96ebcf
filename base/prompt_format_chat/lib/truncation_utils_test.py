"""Tests for truncation utils."""

import functools

from base.prompt_format_chat.lib.token_counter import Token<PERSON><PERSON>nter
from base.prompt_format_chat.lib.truncation_utils import (
    head_n_lines,
    last_approx_tokens,
    trailing_n_lines,
)


class MockTokenCounter(TokenCounter):
    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        return len(prompt_chars) * 2


def test_trailing_n_lines():
    token_counter = MockTokenCounter()
    text = """\
This is line 1.
This is line 2.
This is line 3.\
"""
    num_toks_per_line = [
        token_counter.count_tokens(line) for line in text.splitlines(keepends=True)
    ]

    # First don't truncate at all
    # - surplus max_toks
    max_toks = sum(num_toks_per_line)
    clipped_text = trailing_n_lines(text, max_toks * 10, token_counter)
    assert clipped_text == text
    # - exact max_toks
    max_toks = sum(num_toks_per_line)
    clipped_text = trailing_n_lines(text, max_toks, token_counter)
    assert clipped_text == text

    # Then truncate to 2 lines
    # - surplus max_toks
    max_toks = num_toks_per_line[-1] + num_toks_per_line[-2] + 2
    clipped_text = trailing_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 2.\nThis is line 3."
    # - exact max_toks
    max_toks = num_toks_per_line[-1] + num_toks_per_line[-2]
    clipped_text = trailing_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 2.\nThis is line 3."

    # Then truncate to 1 line
    # - surplus max_toks
    max_toks = num_toks_per_line[-1] + 3
    clipped_text = trailing_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 3."
    # - exact max_toks
    max_toks = num_toks_per_line[-1]
    clipped_text = trailing_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 3."

    # Then truncate to 0 lines
    # - surplus max_toks
    max_toks = 3
    clipped_text = trailing_n_lines(text, max_toks, token_counter)
    assert clipped_text == ""
    # - exact max_toks
    max_toks = 0
    clipped_text = trailing_n_lines(text, max_toks, token_counter)
    assert clipped_text == ""


def test_head_n_lines():
    token_counter = MockTokenCounter()
    text = """\
This is line 1.
This is line 2.
This is line 3.\
"""
    num_toks_per_line = [
        token_counter.count_tokens(line) for line in text.splitlines(keepends=True)
    ]

    # First don't truncate at all
    # - surplus max_toks
    max_toks = sum(num_toks_per_line)
    clipped_text = head_n_lines(text, max_toks * 10, token_counter)
    assert clipped_text == text
    # - exact max_toks
    max_toks = sum(num_toks_per_line)
    clipped_text = head_n_lines(text, max_toks, token_counter)
    assert clipped_text == text

    # Then truncate to 2 lines
    # - surplus max_toks
    max_toks = num_toks_per_line[0] + num_toks_per_line[1] + 2
    clipped_text = head_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 1.\nThis is line 2.\n"
    # - exact max_toks
    max_toks = num_toks_per_line[0] + num_toks_per_line[1]
    clipped_text = head_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 1.\nThis is line 2.\n"

    # Then truncate to 1 line
    # - surplus max_toks
    max_toks = num_toks_per_line[0] + 3
    clipped_text = head_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 1.\n"
    # - exact max_toks
    max_toks = num_toks_per_line[0]
    clipped_text = head_n_lines(text, max_toks, token_counter)
    assert clipped_text == "This is line 1.\n"

    # Then truncate to 0 lines
    # - surplus max_toks
    max_toks = 3
    clipped_text = head_n_lines(text, max_toks, token_counter)
    assert clipped_text == ""
    # - exact max_toks
    max_toks = 0
    clipped_text = head_n_lines(text, max_toks, token_counter)
    assert clipped_text == ""


def test_last_approx_tokens():
    token_counter = MockTokenCounter()
    text = "This is a test sentence with multiple tokens."

    # Multiple buckets but can fit all of text
    max_toks = 250
    result = last_approx_tokens(text, max_toks, token_counter, granularity=5)
    assert result == text

    # Multiple buckets but can only fit part of text
    max_toks = 50
    result = last_approx_tokens(text, max_toks, token_counter, granularity=5)
    assert result == "nce with multiple tokens."

    # Test with less than granularity number of chars
    text = "Short"
    result = last_approx_tokens(text, max_toks, token_counter, granularity=5)
    assert result == "Short"

    # Test with an empty text
    text = ""
    result = last_approx_tokens(text, max_toks, token_counter, granularity=5)
    assert result == ""
