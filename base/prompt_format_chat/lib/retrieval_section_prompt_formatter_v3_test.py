"""Tests for the retrieval section prompt formatter."""

import dataclasses
from textwrap import dedent
from unittest.mock import Mock

import pytest

from base.prompt_format.common import (
    DocumentationMetadata,
    PromptChunk,
)
from base.prompt_format_chat.lib.retrieval_section_prompt_formatter_v3 import (
    RetrievalSectionBuilder,
    RetrievalSectionPromptFormatterV3,
    RetrievalSectionType,
)
from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChatTokenApportionment,
    TokenList,
)
from base.tokenizers.tokenizer import Tokenizer


def tokenize_safe(text: str) -> TokenList:
    """Mock tokenize a text string into ASCII codes."""
    return [ord(char) for char in text]


def detokenize(tokens: TokenList) -> str:
    """Mock detokenize a list of ASCII codes back to a string."""
    return "".join(chr(token) for token in tokens)


@pytest.fixture(name="formatter")
def fixture_formatter():
    """Setup the formatter."""
    general_template = dedent("""\
        Here is an excerpt from the file `{path}`:

        ```
        {content}
        ```

        """)

    user_guided_template = dedent("""\
        I have file `{path}` open with its content:

        ```
        {content}
        ```

        """)

    docset_template = dedent("""\
        Here is an excerpt from the documentation for `{path}`:

        ```
        {content}
        ```

        """)

    # Mocking the Tokenizer interface
    tokenizer_mock = Mock(spec=Tokenizer)
    tokenizer_mock.tokenize_safe.side_effect = tokenize_safe
    tokenizer_mock.detokenize.side_effect = detokenize
    token_counter = TokenizerBasedTokenCounter(tokenizer_mock)

    formatter = RetrievalSectionPromptFormatterV3(
        {
            RetrievalSectionType.USER_GUIDED: user_guided_template,
            RetrievalSectionType.GENERAL: general_template,
            RetrievalSectionType.DOCSET: docset_template,
        },
        token_counter,
    )
    return formatter


def test_format_with_a_single_chunk(formatter: RetrievalSectionPromptFormatterV3):
    """Test formatting with a single chunk."""
    chunk = PromptChunk(
        text="This is a test chunk.\n",
        path="file1.txt",
        char_start=0,
        char_end=22,
        blob_name="file1.txt",
        unique_id="file1.txt:1",
        origin="user_guided_retriever",
    )

    sec_str, prompt_chunks = formatter.format(
        [chunk], max_path_tokens=10, max_total_tokens=100
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {"file1.txt:1"}
    expected_text = dedent("""\
        I have file `file1.txt` open with its content:

        ```
        This is a test chunk.
        ...
        ```

        """)

    assert sec_str == expected_text


def test_format_with_multiple_chunks(formatter):
    """Test formatting with multiple chunks."""
    chunk1 = PromptChunk(
        text="small\n",
        path="file1.txt",
        char_start=0,
        char_end=6,
        blob_name="file1.txt",
        unique_id="file1.txt:1",
        origin="user_guided_retriever",
    )
    chunk2 = PromptChunk(
        text="This is the second chunk.\n",
        path="file1.txt",
        char_start=6,
        char_end=32,
        blob_name="file1.txt",
        unique_id="file1.txt:2",
        origin="user_guided_retriever",
    )

    # Test case: budget is enough for both chunks.
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=200
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
        "file1.txt:2",
    }
    expected_text = dedent("""\
        I have file `file1.txt` open with its content:

        ```
        small
        This is the second chunk.
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is enough for a single chunk only
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=94
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:2",
    }
    expected_text = dedent("""\
        I have file `file1.txt` open with its content:

        ```
        ...
        This is the second chunk.
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is enough for the first chunk
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=90
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
    }
    expected_text = dedent("""\
        I have file `file1.txt` open with its content:

        ```
        small
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is not enough for any chunks
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=60
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == set()
    expected_text = ""
    assert sec_str == expected_text


def test_format_with_multiple_files(formatter):
    """Test formatting with multiple files."""
    chunk1 = PromptChunk(
        text="small.\n",
        path="file1.txt",
        char_start=0,
        char_end=7,
        blob_name="file1.txt",
        unique_id="file1.txt:1",
        origin="user_guided_retriever",
    )
    chunk2 = PromptChunk(
        text="This chunk is fairly long.\n",
        path="file2.txt",
        char_start=22,
        char_end=49,
        blob_name="file2.txt",
        unique_id="file2.txt:1",
        origin="user_guided_retriever",
    )

    # Test case: budget is enough for both chunks.
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=200
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
        "file2.txt:1",
    }
    expected_text = dedent("""\
        I have file `file2.txt` open with its content:

        ```
        ...
        This chunk is fairly long.
        ...
        ```

        I have file `file1.txt` open with its content:

        ```
        small.
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is enough only for a single chunk
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=100
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file2.txt:1",
    }
    expected_text = dedent("""\
        I have file `file2.txt` open with its content:

        ```
        ...
        This chunk is fairly long.
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is enough only for the shortest chunk.
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=90
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
    }
    expected_text = dedent("""\
        I have file `file1.txt` open with its content:

        ```
        small.
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is not enough for any chunks.
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=60
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == set()
    expected_text = ""
    assert sec_str == expected_text


def test_non_empty_basic(
    formatter: RetrievalSectionPromptFormatterV3, example_basic_input
):
    """Test non-empty basic."""
    chunk1 = PromptChunk(
        text="small.\n",
        path="file1.txt",
        char_start=0,
        char_end=7,
        blob_name="file1.txt",
        unique_id="file1.txt:1",
        origin="user_guided_retriever",
    )
    chunk2 = PromptChunk(
        text="This chunk is fairly long.\n",
        path="file2.txt",
        char_start=22,
        char_end=49,
        blob_name="file2.txt",
        unique_id="file2.txt:1",
        origin="dense_retriever",
    )
    chunk3 = PromptChunk(
        text="More from this file.\n",
        path="file2.txt",
        char_start=49,
        char_end=76,
        blob_name="file2.txt",
        unique_id="file2.txt:2",
        origin="dense_retriever",
    )

    builder = RetrievalSectionBuilder(
        token_counter=formatter.token_counter,
        token_apportionment=ChatTokenApportionment(
            max_prompt_len=750,
            path_len=10,
            message_len=100,
            prefix_len=100,
            selected_code_len=100,
            chat_history_len=100,
            suffix_len=100,
            retrieval_len_per_each_user_guided_file=2000,
            retrieval_len_for_user_guided=-1,
            retrieval_len=750,
        ),
        retrieval_formatter=formatter,
        retrieval_response_message="test response.",
    )

    example_basic_input = dataclasses.replace(
        example_basic_input, retrieved_chunks=[chunk1, chunk2, chunk3]
    )
    exchanges, _ = builder.get_retrieval_section_as_exchanges(
        prompt_input=example_basic_input,
        clipped_prefix="",
        clipped_suffix="",
    )
    assert len(exchanges) == 1
    assert (
        exchanges[0].request_message
        == """\
I have file `file1.txt` open with its content:

```
small.
...
```

Below are some relevant files from my project.

Here is an excerpt from the file `file2.txt`:

```
...
This chunk is fairly long.
More from this file.
...
```

"""
    )
    assert exchanges[0].response_text == "test response."


def test_empty_str_builder(
    formatter: RetrievalSectionPromptFormatterV3, example_basic_input
):
    """Test builder with empty string."""
    builder = RetrievalSectionBuilder(
        token_counter=formatter.token_counter,
        token_apportionment=ChatTokenApportionment(
            max_prompt_len=100,
            path_len=10,
            message_len=0,
            prefix_len=0,
            selected_code_len=0,
            chat_history_len=0,
            suffix_len=0,
            retrieval_len=0,
        ),
        retrieval_formatter=formatter,
        retrieval_response_message="test response.",
    )

    exchanges, _ = builder.get_retrieval_section_as_exchanges(
        prompt_input=example_basic_input,
        clipped_prefix="",
        clipped_suffix="",
    )
    assert len(exchanges) == 0


def test_format_with_multiple_files_empty_blob_name(formatter):
    """Test formatting with multiple files."""
    chunk1 = PromptChunk(
        text="small.\n",
        path="file1.txt",
        char_start=0,
        char_end=7,
        blob_name="",
        unique_id="file1.txt:1",
        origin="user_guided_retriever",
    )
    chunk2 = PromptChunk(
        text="This chunk is fairly long.\n",
        path="file2.txt",
        char_start=22,
        char_end=49,
        blob_name="",
        unique_id="file2.txt:1",
        origin="user_guided_retriever",
    )

    # Test case: budget is enough for both chunks.
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=200
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
        "file2.txt:1",
    }
    expected_text = dedent("""\
        I have file `file2.txt` open with its content:

        ```
        ...
        This chunk is fairly long.
        ...
        ```

        I have file `file1.txt` open with its content:

        ```
        small.
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is enough only for a single chunk
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1], max_path_tokens=10, max_total_tokens=100
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file2.txt:1",
    }
    expected_text = dedent("""\
        I have file `file2.txt` open with its content:

        ```
        ...
        This chunk is fairly long.
        ...
        ```

        """)
    assert sec_str == expected_text


def test_format_with_docset_metadata_different_pages(
    formatter: RetrievalSectionPromptFormatterV3, example_basic_input
):
    """Test docset chunks with page numbers and empty blob names."""
    chunk1 = PromptChunk(
        text="small.\n",
        path="file1.txt",
        char_start=0,
        char_end=7,
        blob_name="",
        unique_id="file1.txt:1",
        origin="dense_retriever",
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git", name="Git", page_id="1", headers=[]
        ),
    )
    chunk2 = PromptChunk(
        text="This chunk is fairly long.\n",
        path="file2.txt",
        char_start=22,
        char_end=49,
        blob_name="",
        unique_id="file2.txt:1",
        origin="dense_retriever",
        documentation_metadata=(
            DocumentationMetadata(
                source_id="docset://Git", name="Git", page_id="2", headers=[]
            )
        ),
    )
    chunk3 = PromptChunk(
        text="More from this file.\n",
        path="file2.txt",
        char_start=49,
        char_end=76,
        blob_name="",
        unique_id="file2.txt:2",
        origin="dense_retriever",
        documentation_metadata=(
            DocumentationMetadata(
                source_id="docset://Git", name="Git", page_id="2", headers=[]
            )
        ),
    )

    builder = RetrievalSectionBuilder(
        token_counter=formatter.token_counter,
        token_apportionment=ChatTokenApportionment(
            max_prompt_len=750,
            path_len=10,
            message_len=100,
            prefix_len=100,
            selected_code_len=100,
            chat_history_len=100,
            suffix_len=100,
            retrieval_len_per_each_user_guided_file=2000,
            retrieval_len_for_user_guided=-1,
            retrieval_len=750,
        ),
        retrieval_formatter=formatter,
        retrieval_response_message="test response.",
    )

    example_basic_input = dataclasses.replace(
        example_basic_input, retrieved_chunks=[chunk1, chunk2, chunk3]
    )
    exchanges, _ = builder.get_retrieval_section_as_exchanges(
        prompt_input=example_basic_input,
        clipped_prefix="",
        clipped_suffix="",
    )
    assert len(exchanges) == 1
    expected_text = dedent("""\
Below are some relevant files from my project.

Here is an excerpt from the documentation for `Git`:

```
small.
...
```

Here is an excerpt from the documentation for `Git`:

```
...
This chunk is fairly long.
More from this file.
...
```

""")
    assert exchanges[0].request_message == expected_text
    assert exchanges[0].response_text == "test response."


def test_format_with_multiple_docset_chunks_with_headers(formatter):
    """Test formatting with multiple docset chunks with headers."""
    chunk1 = PromptChunk(
        text="small\n",
        path="file1.txt",
        char_start=0,
        char_end=6,
        blob_name="",
        unique_id="file1.txt:1",
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header2"],
        ),
    )
    chunk2 = PromptChunk(
        text="This is the second chunk that is now going to be longer.\n",
        path="file1.txt",
        char_start=6,
        char_end=62,
        blob_name="",
        unique_id="file1.txt:2",
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header3"],
        ),
    )

    # Test case: budget is enough for both chunks.
    sec_str, prompt_chunks = formatter.format(
        [chunk1, chunk2],
        max_path_tokens=10,
        max_total_tokens=400,
        max_external_context_tokens=400,
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
        "file1.txt:2",
    }
    expected_text = dedent("""\
        Here is an excerpt from the documentation for `Git`:

        ```
        #Header1
        ##Header2
        small
        ##Header3
        This is the second chunk that is now going to be longer.
        ...
        ```

        """)

    assert sec_str == expected_text

    # Test case: budget is enough for a single chunk only
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1],
        max_path_tokens=10,
        max_total_tokens=150,
        max_external_context_tokens=150,
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:2",
    }
    expected_text = dedent("""\
        Here is an excerpt from the documentation for `Git`:

        ```
        ...
        #Header1
        ##Header3
        This is the second chunk that is now going to be longer.
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is enough for the first chunk
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1],
        max_path_tokens=10,
        max_total_tokens=100,
        max_external_context_tokens=100,
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
    }
    expected_text = dedent("""\
        Here is an excerpt from the documentation for `Git`:

        ```
        #Header1
        ##Header2
        small
        ...
        ```

        """)
    assert sec_str == expected_text

    # Test case: budget is not enough for any chunks
    sec_str, prompt_chunks = formatter.format(
        [chunk2, chunk1],
        max_path_tokens=10,
        max_total_tokens=60,
        max_external_context_tokens=60,
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == set()
    expected_text = ""
    assert sec_str == expected_text


# Docsets can get into a state where their paths are different due to
# size limits of uploading blobs, but they came from the same page id
# and therefore should be merged together.
def test_format_docsets_different_path_same_pages(formatter):
    """Test formatting with multiple docset chunks with headers."""
    chunk1 = PromptChunk(
        text="small\n",
        path="file1.txt",
        char_start=0,
        char_end=6,
        blob_name="",
        unique_id="file1.txt:1",
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header2"],
        ),
    )
    chunk2 = PromptChunk(
        text="This is the second chunk that is now going to be longer.\n",
        path="file2.txt",
        char_start=6,
        char_end=62,
        blob_name="",
        unique_id="file1.txt:2",
        documentation_metadata=DocumentationMetadata(
            source_id="docset://Git",
            name="Git",
            page_id="1",
            headers=["#Header1", "##Header3"],
        ),
    )

    sec_str, prompt_chunks = formatter.format(
        [chunk1, chunk2],
        max_path_tokens=10,
        max_total_tokens=400,
        max_external_context_tokens=400,
    )
    assert {chunk.unique_id for chunk in prompt_chunks} == {
        "file1.txt:1",
        "file1.txt:2",
    }
    expected_text = dedent("""\
        Here is an excerpt from the documentation for `Git`:

        ```
        #Header1
        ##Header2
        small
        ##Header3
        This is the second chunk that is now going to be longer.
        ...
        ```

        """)

    assert sec_str == expected_text
