"""Tests for the StringFormatter class."""

from unittest.mock import Mock

from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
import pytest

from base.tokenizers.tokenizer import Tokenizer
from base.prompt_format_chat.lib.string_formatter import (
    StringFormatter,
    split_template,
)


def test_split_template_simple_placeholders():
    """Tests splitting a template with simple placeholders."""
    template = "Hello {name}"
    expected = ["Hello ", "{name}"]
    assert split_template(template) == expected


def test_split_template_multiple_placeholders():
    """Tests splitting a template with multiple placeholders."""
    template = "{greeting}, {name}! Welcome to {place}."
    expected = ["{greeting}", ", ", "{name}", "! Welcome to ", "{place}", "."]
    assert split_template(template) == expected


def test_split_template_no_placeholders():
    """Tests splitting a template with no placeholders."""
    template = "Just plain text."
    expected = ["Just plain text."]
    assert split_template(template) == expected


def test_split_template_adjacent_placeholders():
    """Tests splitting a template with adjacent placeholders."""
    template = "{first}{second}"
    expected = ["{first}", "{second}"]
    assert split_template(template) == expected


def test_split_template_text_between_placeholders():
    """Tests splitting a template with text between placeholders."""
    template = "{greeting}, this is {name}."
    expected = ["{greeting}", ", this is ", "{name}", "."]
    assert split_template(template) == expected


def test_split_template_placeholder_at_start():
    """Tests splitting a template with a placeholder at the start."""
    template = "{start} text continues."
    expected = ["{start}", " text continues."]
    assert split_template(template) == expected


def test_split_template_placeholder_at_end():
    """Tests splitting a template with a placeholder at the end."""
    template = "Ends with {end}"
    expected = ["Ends with ", "{end}"]
    assert split_template(template) == expected


def test_split_template_empty_string():
    """Tests splitting an empty template string."""
    template = ""
    expected = []
    assert split_template(template) == expected


def test_split_template_only_placeholders():
    """Tests splitting a template with only placeholders."""
    template = "{only}{placeholders}{here}"
    expected = ["{only}", "{placeholders}", "{here}"]
    assert split_template(template) == expected


def test_split_template_mixed_content():
    """Tests splitting a template with mixed content."""
    template = "Start {middle} end {last}"
    expected = ["Start ", "{middle}", " end ", "{last}"]
    assert split_template(template) == expected


# Define a simple token mapping for testing
TOKEN_MAPPING = {
    "Hello ": [1, 2],
    "John": [3],
    ", welcome to ": [4, 5],
    "Wonderland": [6],
    "!": [7],
    "File content: ": [8, 9],
    "This": [10],
    "is": [11],
    "a": [12],
    "file.": [13],
    " ": [15],  # Added for single space
    "name": [16],
    "place": [17],
    ", ": [18],
    "Value ": [19],
}


def mock_tokenize_safe(text):
    """Mock tokenize a text string by finding the longest matching substring in a predefined vocabulary."""
    tokens = []
    i = 0
    while i < len(text):
        longest_match = None
        longest_token = []
        # Try to find the longest match starting at position i
        for j in range(i + 1, len(text) + 1):
            substring = text[i:j]
            if substring in TOKEN_MAPPING:
                longest_match = j
                longest_token = TOKEN_MAPPING[substring]
        if longest_match is not None:
            tokens.extend(longest_token)
            i = longest_match  # Move the index past the end of the longest match
        else:
            i += 1  # Move to the next character if no match found
    return tokens


def test_tokenized_string_formatter_init():
    """Tests initialization of the formatter with simple placeholders."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello {name}, welcome to {place}!"
    formatter = StringFormatter(template, token_counter)
    expected_parts = [
        "Hello ",
        "{name}",
        ", welcome to ",
        "{place}",
        "!",
    ]
    assert formatter.parts_or_placeholders == expected_parts


def test_tokenized_string_formatter_format():
    """Tests formatting with simple placeholders."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello {name}, welcome to {place}!"
    formatter = StringFormatter(template, token_counter)
    values = {"name": "John", "place": "Wonderland"}
    result = formatter.format(values)
    expected_result = "Hello John, welcome to Wonderland!"  # Combined str
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(
        tokenizer.tokenize_safe(expected_result)
    )


def test_tokenized_string_formatter_format_missing_value():
    """Tests formatting with a missing value for a placeholder."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello {name}, welcome to {place}!"
    formatter = StringFormatter(template, token_counter)
    values = {"name": "John"}
    with pytest.raises(ValueError) as excinfo:
        formatter.format(values)
    assert "Missing value for placeholder place" in str(excinfo.value)
    with pytest.raises(ValueError) as excinfo:
        formatter.format_and_count_tokens(values)
    assert "Missing value for placeholder place" in str(excinfo.value)


def test_tokenized_string_formatter_format_and_count_tokens():
    """Tests formatting and counting total tokens."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello {name}, welcome to {place}!"
    formatter = StringFormatter(template, token_counter)
    values = {"name": "John", "place": "Wonderland"}
    result = formatter.format(values)
    expected_result = "Hello John, welcome to Wonderland!"  # Combined tokenized list
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(
        tokenizer.tokenize_safe(expected_result)
    )


def test_tokenized_string_formatter_empty_placeholder():
    """Tests formatting with an empty placeholder {}."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello {}!"
    formatter = StringFormatter(template, token_counter)
    values = {"": ""}  # Empty placeholder should map to an empty list
    result = formatter.format(values)
    expected_result = "Hello !"
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(
        tokenizer.tokenize_safe(expected_result)
    )


def test_tokenized_string_formatter_placeholder_with_space():
    """Tests formatting with a placeholder containing a space { }."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello { }!"
    formatter = StringFormatter(template, token_counter)
    values = {" ": " "}  # Placeholder with space
    result = formatter.format(values)
    expected_result = "Hello  !"
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(
        tokenizer.tokenize_safe(expected_result)
    )


def test_tokenized_string_formatter_repeated_placeholders():
    """Tests formatting with repeated placeholders."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello {name}, {name}!"
    formatter = StringFormatter(template, token_counter)
    values = {"name": "John"}
    result = formatter.format(values)
    expected_result = "Hello John, John!"  # Repeated "name" placeholder
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(
        tokenizer.tokenize_safe(expected_result)
    )


def test_tokenized_string_formatter_placeholder_with_numbers():
    """Tests formatting with a placeholder containing numbers {123}."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Value {123}"
    formatter = StringFormatter(template, token_counter)
    values = {"123": "place"}  # Placeholder with numbers
    result = formatter.format(values)
    expected_result = "Value place"
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(
        tokenizer.tokenize_safe(expected_result)
    )


def test_tokenized_string_formatter_non_existent_placeholder():
    """Tests formatting with a non-existent placeholder."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    template = "Hello {name}, welcome to {non_existent}!"
    formatter = StringFormatter(template, token_counter)
    values = {"name": "John", "place": "Wonderland"}  # Missing "non_existent"
    with pytest.raises(ValueError) as excinfo:
        formatter.format(values)
    assert "Missing value for placeholder non_existent" in str(excinfo.value)
    with pytest.raises(ValueError) as excinfo:
        formatter.format_and_count_tokens(values)
    assert "Missing value for placeholder non_existent" in str(excinfo.value)
