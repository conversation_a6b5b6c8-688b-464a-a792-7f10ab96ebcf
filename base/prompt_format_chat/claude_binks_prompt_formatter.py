import dataclasses

from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    StructuredChatPromptFormatter,
    StructuredChatPromptOutput,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)

claude_current_message_schema = """
Here is a question I have:
<augment-question>{question}</augment-question>

Please respond to the question in <augment-answer> tags. Also,
list the filepaths of citations you used in your answer, and put each filepath
inside a separate pair of <augment-citation> tags. Also, when referencing classes, functions,
variables, or files in answer, always wrap them in backticks (``), eg `TheCuriousClass`.
"""


class ClaudeBinksChatPromptFormatter(StructuredChatPromptFormatter):
    """The Gemini prompt formatter for the code chat."""

    def __init__(
        self,
        token_apportionment: ChatTokenApportionment | None = None,
    ):
        self.token_counter = ClaudeTokenCounter()
        self.structured_binks_prompt_formatter = StructuredBinksPromptFormatter.create(
            self.token_counter,
            token_apportionment,
        )
        self.token_apportionment = (
            self.structured_binks_prompt_formatter.token_apportionment
        )

    def format_prompt(
        self,
        prompt_input: ChatPromptInput,
    ) -> StructuredChatPromptOutput:
        """Format prompt for Gemini.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            StructuredChatPromptOutput object
        """

        new_message = claude_current_message_schema.format(
            question=prompt_input.message
        )
        prompt_input = dataclasses.replace(prompt_input, message=new_message)

        return self.structured_binks_prompt_formatter.format_prompt(prompt_input)
