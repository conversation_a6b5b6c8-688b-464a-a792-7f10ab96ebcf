"""Prompt formatter for smart paste."""

from dataclasses import dataclass
from typing import Literal, Iterable, Sequence

from dataclasses_json import dataclass_json
from base.prompt_format.common import Exchange, LineEnding, PromptChunk
from base.prompt_format_chat.lib.token_counter import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.prompt_format_chat.lib.truncation_utils import last_approx_tokens
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    ExceedContextLength,
)

SMART_PASTE_SYSTEM_PROMPT = """\
You are highly advanced and intelligent AI code assistant.
Your task is to carefully apply changes to a file based on the conversation history and the user's instructions.
"""


@dataclass_json
@dataclass(frozen=True)
class SmartPasteTokenApportionment:
    """Stores the apportionment of the tokens for the code chat model."""

    path_len: int
    """The number of tokens of the path to include."""

    prefix_len: int
    """The number of tokens of the prefix to include."""

    suffix_len: int
    """The number of tokens of the suffix to include."""

    chat_history_len: int
    """The total number of tokens of the conversation history to include. -1 means no limit."""

    target_file_path_len: int
    """The number of tokens of the target file path to include."""

    target_file_content_len: int
    """The maximum number of tokens of the target file content allowed."""

    max_prompt_len: int
    """The number of tokens of the total prompt length, which will be used to compute the budget for retrieved chunks."""

    def __post_init__(self):
        assert self.path_len > 0
        assert self.max_prompt_len > 0
        assert (
            self.max_prompt_len
            >= self.path_len
            + self.prefix_len
            + self.suffix_len
            + self.chat_history_len
            + self.target_file_path_len
            + self.target_file_content_len
        )


@dataclass(frozen=True)
class SmartPastePromptInput:
    """The set of inputs used for constructing the smart paste prompts.

    `prefix_begin`, `suffix_end` and `file_path` are required to filter overlapping retrieval chunks.
    """

    path: str
    """The file path."""

    prefix: str
    """The content before the selection."""

    selected_code: str
    """The content that the user selected."""

    code_block: str
    """ The code block to paste into the file."""

    suffix: str
    """The content after the selection, where its end location is at the cursor."""

    chat_history: Iterable[Exchange]
    """The conversation history as a list of request_message/response_text pairs."""

    prefix_begin: int
    """The offset in UTF-8 characters where prefix begins, relative to the beginning of file."""

    suffix_end: int
    """The offset in UTF-8 characters where suffix ends, relative to the beginning of file."""

    retrieved_chunks: Iterable[PromptChunk]
    """The retrieved chunks, sorted from high to low relevancy."""

    target_path: str
    """The path of the file to paste the code into."""

    target_file_content: str
    """The content of the file to paste the code into."""

    context_code_exchange_request_id: str | None = None
    """Request ID if the exchange to which the context code should be added.
        "new" if context code should be added to current user message.
    """

    def __repr__(self):
        attributes = ",\n  ".join(
            f"{key} = {value!r}" for key, value in vars(self).items()
        )
        return f"{self.__class__.__name__}(\n  {attributes}\n)"


@dataclass(frozen=True)
class SmartPastePromptOutput:
    """The set of outputs used for constructing smart paste prompts."""

    system_prompt: str | None
    """The system prompt."""

    chat_history: Iterable[Exchange]
    """The truncated conversation history as a list of request_message/response_text pairs."""

    message: str
    """The generated prompt message to the model to perform smart paste."""

    tools: Sequence[Literal["replace_text"]] | None = None

    prefill: str | None = None
    """Text to "prefill" model response. So model is continuing generation from it."""

    original_line_ending: LineEnding | None = None
    """The original line ending type of the target file content."""


class SmartPastePromptFormatter:
    """A prompt formatter for smart paste"""

    def __init__(
        self,
        token_counter: TokenCounter,
        token_apportionment: SmartPasteTokenApportionment,
    ):
        self.token_counter = token_counter
        self.token_apportionment = token_apportionment

    def _put_xml_numbers(self, lines: list[str], start_line_offset: int = 0):
        for i, line in enumerate(lines):
            cur_number = i + 1 + start_line_offset
            yield f"<line number={cur_number}>{line.rstrip()}</line number={cur_number}>\n"

    def _format_xml_code(
        self,
        code: str,
    ) -> str:
        code_lines = code.splitlines(keepends=True)

        code_n_xml = "".join(self._put_xml_numbers(code_lines))
        return code_n_xml

    def _format_regular_numbered_code(
        self,
        code: str,
    ) -> str:
        numbered_lines = []
        for i, line in enumerate(code.splitlines(True)):
            numbered_lines.append(f"{i+1:04d}: {line}")
        return "".join(numbered_lines)

    def _format_smart_paste_message(
        self, prompt_input: SmartPastePromptInput, with_pure_additions: bool
    ):
        clipped_target_path = last_approx_tokens(
            prompt_input.target_path,
            self.token_apportionment.target_file_path_len,
            self.token_counter,
        )

        numbered_code = self._format_xml_code(prompt_input.target_file_content)
        message = f"""Great! Now please, apply changes that you demonstrated in this codeblock:
<changes_to_apply>
```
{prompt_input.code_block}
```
</changes_to_apply>

to this file :

<file path="{clipped_target_path}">
{numbered_code.rstrip()}
</file>
"""
        if with_pure_additions:
            message += """
To modify the file, please use git conflict markers format. I.e. return single or multiple git conflict markers in the following format:
<<<<<<< original BRIEFLY
...
======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]
...
>>>>>>> updated FULL

When you need to insert new class(es) or function(s) after line X use:
<<<<<<< original BRIEFLY
...
======= [after line(s) <line number=X>...content of line X...</line number=X>, <line number=X+1>...content of line X+1...</line number=X+1>,...,<line number=X+k>...content of line X+k...</line number=X+k>]
...
>>>>>>> updated FULL

- `updated` section should contain modified `original` code.
- Return only git conflict markers and nothing else.
- Split large changes into multiple smaller ones, and merge changes if they are close.
- Make sure to always use disjoint line ranges for every conflict marker.
- Line numbers in markers should ALWAYS be as in file, WITHOUT accounting for markers happened above.
- For middle line use EXACTLY this format: `======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]`. Exactly 2 lines (X and Y) have to be specified here. Or `======= [after line(s) <line number=X>...content of line X...</line number=X>, <line number=X+1>...content of line X+1...</line number=X+1>,...,<line number=X+k>...content of line X+k...</line number=X+k>]`.
- Always preserve indentation style of the original file. If indentation style of file and codeblock differ, always prefer file indentation style.
- To save time, please write code in `original` schematically and VERY briefly, use inline comments to indicate skipped and omitted parts.
- But ALWAYS write `updated` as the FULL range X - Y from original file. Expand any shortenings that are in `original`, NEVER copy them.
- `updated` section should rewrite PRECISELY lines from X to Y.
"""
        else:
            message += """
To modify the file, please use git conflict markers format. I.e. return single or multiple git conflict markers in the following format:
<<<<<<< original BRIEFLY
...
======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]
...
>>>>>>> updated FULL

- `updated` section should contain modified `original` code.
- Return only git conflict markers and nothing else.
- Split large changes into multiple smaller ones and write them in top-to-bottom order.
- Make sure to always use disjoint line ranges for every conflict marker.
- For middle line use EXACTLY this format: `======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]`. Exactly 2 lines (X and Y) have to be specified here.
- Always preserve indentation style of the original file. If indentation style of file and codeblock differ, always prefer file indentation style.
- To save time, please write code in `original` schematically and VERY briefly, use inline comments to indicate skipped and omitted parts.
- But ALWAYS write `updated` as the FULL range X - Y from original file. Expand any shortenings that are in `original`, NEVER copy them.
- `updated` section should rewrite PRECISELY lines from X to Y.
"""
        return message

    def _format_history(
        self,
        prompt_input: SmartPastePromptInput,
    ) -> Iterable[Exchange]:
        """Format history for smart pasting, including selected code, retrieval"""

        # Currently using binks chat formatter to format history with code - also verifies token budget
        chat_formatter = StructuredBinksPromptFormatter.create(
            token_counter=self.token_counter,
            token_apportionment=ChatTokenApportionment(
                path_len=self.token_apportionment.path_len,
                prefix_len=self.token_apportionment.prefix_len,
                chat_history_len=self.token_apportionment.chat_history_len,
                suffix_len=self.token_apportionment.suffix_len,
                retrieval_len=0,
                max_prompt_len=self.token_apportionment.max_prompt_len,
                # Deprecated fields
                message_len=-1,
                selected_code_len=-1,
            ),
        )
        chat_prompt_input = ChatPromptInput(
            message="",
            path=prompt_input.path,
            prefix=prompt_input.prefix,
            selected_code=prompt_input.selected_code,
            suffix=prompt_input.suffix,
            chat_history=prompt_input.chat_history,
            prefix_begin=prompt_input.prefix_begin,
            suffix_end=prompt_input.suffix_end,
            retrieved_chunks=prompt_input.retrieved_chunks,
            context_code_exchange_request_id=prompt_input.context_code_exchange_request_id,
        )
        chat_prompt_output = chat_formatter.format_prompt(chat_prompt_input)
        return chat_prompt_output.chat_history

    def _verify_total_prompt_budget(
        self, system_prompt: str, message: str, chat_history: Iterable[Exchange]
    ) -> None:
        """Verify that the prompt is within the total budget"""
        max_prompt_len = int(self.token_apportionment.max_prompt_len * 0.975)

        system_prompt_tokens = self.token_counter.count_tokens(system_prompt)
        message_tokens = self.token_counter.count_tokens(message)
        stringified_chat_history = "\n\n".join(
            [
                f"user: {str(item.request_message)}\nassistant: {str(item.response_text)}"
                for item in chat_history
            ]
        )
        chat_history_tokens = self.token_counter.count_tokens(stringified_chat_history)
        total_len = system_prompt_tokens + message_tokens + chat_history_tokens
        if total_len > max_prompt_len:
            raise ExceedContextLength(
                f"Prompt length {total_len} exceeds maximum {max_prompt_len}."
            )

    def _verify_fields_budget(
        self,
        prompt_input: SmartPastePromptInput,
    ) -> None:
        """
        Verify that the fields in the prompt are within the total budget
        Only need to verify fields that are not verified in the chat formatter
        as long as we use it
        """
        target_file_tokens = self.token_counter.count_tokens(
            prompt_input.target_file_content
        )
        max_target_file_len = self.token_apportionment.target_file_content_len
        if target_file_tokens > max_target_file_len:
            raise ExceedContextLength(
                f"Target file content length {target_file_tokens} exceeds maximum {max_target_file_len}"
            )

    def format_prompt(
        self, prompt_input: SmartPastePromptInput, with_pure_additions: bool = False
    ) -> SmartPastePromptOutput:
        self._verify_fields_budget(prompt_input)
        message = self._format_smart_paste_message(prompt_input, with_pure_additions)
        chat_history = self._format_history(prompt_input)
        system_prompt = SMART_PASTE_SYSTEM_PROMPT
        self._verify_total_prompt_budget(system_prompt, message, chat_history)

        return SmartPastePromptOutput(
            message=message,
            system_prompt=system_prompt,
            chat_history=chat_history,
            tools=[],
            prefill="Here are the changes applied using git conflict markers:\n\n\n<<<<<<< original BRIEFLY",
        )
