"""Prompt formatters for commit message generation."""

from base.prompt_format_chat.lib.system_prompts import (
    get_commit_message_system_prompt,
    get_commit_message_trailing_instructions,
)
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChangedFileStats,
    ChatPromptFormatter,
    ChatPromptInput,
    GenerateCommitMessageTokenApportionment,
    PerTypeChangedFileStats,
    StructuredChatPromptOutput,
)

# Mapping of single-letter change type to its imperative form for prompting.
CHANGE_TYPE_IMPERATIVES = {
    "A": "Added",
    "C": "Copied",
    "D": "Deleted",
    "M": "Modified",
    "R": "Renamed",
    "U": "Changed (unmerged)",
    "X": "Changed (unknown change type)",
    "B": "Changed (pairing broken)",
}


class GenerateCommitMessagePromptFormatter(
    ChatPromptFormatter[StructuredChatPromptOutput]
):
    """A standalone prompt formatter for commit message generation."""

    FRONT_FILE_LIMIT_PER_CHANGE_TYPE: int = 5
    """The number of files to keep in the beginning after midddle truncation.

    The files are ordred lexicographically. Truncation is necessary to stay in the token
    budget. Middle truncation is important for the model to understand the overall
    changes by seeing the beginning and the end.
    """
    BACK_FILE_LIMIT_PER_CHANGE_TYPE: int = 5
    """The number of files to keep in the end after middle truncation.

    The files are ordred lexicographically. Truncation is necessary to stay in the token
    budget. Middle truncation is important for the model to understand the overall
    changes by seeing the beginning and the end.
    """

    def __init__(
        self,
        token_counter: TokenCounter,
        token_apportionment: GenerateCommitMessageTokenApportionment,
    ):
        self.token_counter = token_counter
        self.token_apportionment = token_apportionment

        self.system_prompt = get_commit_message_system_prompt()
        self.trailing_instructions = get_commit_message_trailing_instructions()

    def _format_change_type_stats(
        self,
        stats: PerTypeChangedFileStats,
        change_type: str,
    ):
        """Format the stats of a single change type.

        This function:
        - Returns an empty string if there are no files of this change type.
        - Lists the total number of files of this change type in the beginning of the
          prompt.
        - Lists the numbers of insertions and deletions for each file and its path (
          including the old path if the file is renamed or copied.)
        - Truncates the file list in the middle if there are too many files.

        Args:
            stats: The stats of a single change type.
            change_type: The change type.

        Returns:
            The formatted string of the stats.
        """
        imperative = CHANGE_TYPE_IMPERATIVES[change_type]
        if change_type in {"A", "D", "M", "U", "X", "B"}:
            template = "    {change_type} +{insertions} -{deletions} {path}\n"
        elif change_type in {"C", "R"}:
            template = (
                "    {change_type} +{insertions} -{deletions} {old_path} -> {path}\n"
            )
        else:
            raise ValueError(f"Unsupported change type: {change_type}")

        summary = ""
        if stats.changed_file_count > 0:
            summary += f"{imperative} {stats.changed_file_count} files:\n"
            if (
                stats.changed_file_count
                <= self.FRONT_FILE_LIMIT_PER_CHANGE_TYPE
                + self.BACK_FILE_LIMIT_PER_CHANGE_TYPE
            ):
                for per_file_change_stats in stats.per_file_change_stats_head:
                    summary += template.format(
                        change_type=change_type,
                        insertions=per_file_change_stats.insertion_count,
                        deletions=per_file_change_stats.deletion_count,
                        path=per_file_change_stats.path,
                        old_path=per_file_change_stats.old_path,
                    )
            else:
                for per_file_change_stats in stats.per_file_change_stats_head[
                    : self.FRONT_FILE_LIMIT_PER_CHANGE_TYPE
                ]:
                    summary += template.format(
                        change_type=change_type,
                        insertions=per_file_change_stats.insertion_count,
                        deletions=per_file_change_stats.deletion_count,
                        path=per_file_change_stats.path,
                        old_path=per_file_change_stats.old_path,
                    )
                summary += "    ...\n"
                for per_file_change_stats in stats.per_file_change_stats_tail[
                    -self.BACK_FILE_LIMIT_PER_CHANGE_TYPE :
                ]:
                    summary += template.format(
                        change_type=change_type,
                        insertions=per_file_change_stats.insertion_count,
                        deletions=per_file_change_stats.deletion_count,
                        path=per_file_change_stats.path,
                        old_path=per_file_change_stats.old_path,
                    )
        return summary

    def get_changed_files_prompt(self, changed_file_stats: ChangedFileStats):
        """Get the prompt for the changed files summary.

        This function adds a header and then formats the stats of each change type.

        Args:
            changed_file_stats: The stats of the changed files.

        Returns:
            The prompt for the changed files summary.
        """
        summary = "The commit contains the following changes:\n"
        for stats, change_type in [
            (changed_file_stats.modified_file_stats, "M"),
            (changed_file_stats.added_file_stats, "A"),
            (changed_file_stats.deleted_file_stats, "D"),
            (changed_file_stats.renamed_file_stats, "R"),
            (changed_file_stats.copied_file_stats, "C"),
            (changed_file_stats.unmerged_file_stats, "U"),
            (changed_file_stats.unknown_file_stats, "X"),
            (changed_file_stats.broken_file_stats, "B"),
        ]:
            summary += self._format_change_type_stats(stats, change_type)
        summary += "\n\n"
        return summary

    def get_diff_prompt(self, diff: str, token_budget: int):
        """Get the prompt for the diff.

        This function adds a header and a footer and then formats the diff.

        Args:
            diff: The diff.
            token_budget: The token budget for the diff.

        Returns:
            The prompt for the diff.
        """
        prompt = ""
        header = "Below are the per-file diffs of the commit:\n\n"
        token_budget -= self.token_counter.count_tokens(header)
        footer = "\n\n\n"
        token_budget -= self.token_counter.count_tokens(footer)
        core_diff_prompt = self.token_counter.truncate_to_budget(
            diff,
            token_budget,
        )
        prompt += header
        prompt += core_diff_prompt
        prompt += footer
        return prompt

    def get_relevant_message_prompt(
        self, relevant_commit_messages: list[str], token_budget: int
    ):
        """Get the prompt for the relevant commit messages.

        This function adds a header and then formats the relevant commit messages.

        Since relevant commit messages are immediate preceding the current changes, each
        relevant commit message is prefixed with a label indicating its position in the
        commit history relative to `HEAD`.

        Args:
            relevant_commit_messages: The relevant commit messages.
            token_budget: The token budget.

        Returns:
            The prompt for the relevant commit messages.
        """
        if not relevant_commit_messages:
            return ""

        prompt = ""
        header = (
            "Below are the immediately preceding commit messages by the current commit author."
            " They might be relevant for understanding the context and intent of the current commit."
            " If the messages indicate a stack style and the current diffs look relevant, increment the stack counter by 1.\n\n"
        )
        prompt += header
        token_budget -= self.token_counter.count_tokens(header)
        if token_budget < 0:
            return ""

        for i, relevant_commit_message in enumerate(relevant_commit_messages):
            label = "HEAD" if i == 0 else f"HEAD~{i}"
            current_prompt = f"Commit message for {label}:\n"
            current_prompt += f"{relevant_commit_message}\n\n\n"
            token_budget -= self.token_counter.count_tokens(current_prompt)
            if token_budget < 0:
                break
            prompt += current_prompt

        return prompt

    def get_example_message_prompt(
        self,
        example_commit_messages: list[str],
        token_budget: int,
    ) -> str:
        """Get the prompt for the example commit messages.

        This function adds a header and a footer and then formats the example commit
        messages. Each example commit message is prefixed with its index in the example
        list.

        Args:
            example_commit_messages: The example commit messages.
            token_budget: The token budget.

        Returns:
            The prompt for the example commit messages.
        """
        if not example_commit_messages:
            return ""

        prompt = ""
        # TODO(zhuoran): When we upgrade to similar commit messages, update header
        header = "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n\n\n"

        prompt += header
        token_budget -= self.token_counter.count_tokens(header)
        footer = "Above are all the commit message examples.\n\n\n"
        token_budget -= self.token_counter.count_tokens(footer)
        if token_budget < 0:
            return ""

        for i, commit_message in enumerate(example_commit_messages):
            current_prompt = f"Commit message example {i}:\n"
            current_prompt += f"{commit_message}\n\n\n"
            current_token_count = self.token_counter.count_tokens(current_prompt)
            if current_token_count > token_budget:
                break
            prompt += current_prompt
            token_budget -= current_token_count

        prompt += footer
        return prompt

    def format_prompt(self, prompt_input: ChatPromptInput):
        """Formats a commit message generation prompt.

        The prompt is formatted as follows:
        1. The system prompt.
        2. The user message:
            2.1. The changed files summary, if the diff length exceeds the threshold.
            2.2. The diff, truncated at the the character level to the budget.
            2.3. The relevant commit messages, if any.
            2.4. The example commit messages, truncated at the the message level to the
                 budget.
            2.5. The trailing instructions.

        Args:
            prompt_input: The prompt input.

        Returns:
            The formatted prompt.
        """
        assert prompt_input.diff
        assert prompt_input.changed_file_stats
        assert isinstance(
            self.token_apportionment, GenerateCommitMessageTokenApportionment
        )
        assert not prompt_input.message
        assert not prompt_input.chat_history
        assert not prompt_input.retrieved_chunks

        current_budget = self.token_apportionment.max_prompt_len

        # Pre-count trailing instructions tokens
        trailing_instruction_token_count = self.token_counter.count_tokens(
            self.trailing_instructions
        )
        current_budget -= trailing_instruction_token_count

        # Changed files prompt
        changed_files_prompt = ""
        changed_files_token_count = 0
        print(
            f"{len(prompt_input.diff.splitlines())=}, {self.token_apportionment.changed_files_summary_line_threshold=}"
        )
        if (
            len(prompt_input.diff.splitlines())
            > self.token_apportionment.changed_files_summary_line_threshold
        ):
            print("Adding changed files prompt")
            changed_files_prompt = self.get_changed_files_prompt(
                prompt_input.changed_file_stats
            )
            changed_files_token_count = self.token_counter.count_tokens(
                changed_files_prompt
            )
            current_budget -= changed_files_token_count

        # Diff prompt
        diff_prompt = self.get_diff_prompt(
            prompt_input.diff,
            min(
                # diff_len includes both the diff and the changed files summary
                self.token_apportionment.diff_len - changed_files_token_count,
                current_budget,
            ),
        )
        diff_token_count = self.token_counter.count_tokens(diff_prompt)
        current_budget -= diff_token_count

        # Relevant message prompt
        relevant_message_prompt = self.get_relevant_message_prompt(
            prompt_input.relevant_commit_messages,
            token_budget=min(
                self.token_apportionment.relevant_message_len, current_budget
            ),
        )
        relevant_message_token_count = self.token_counter.count_tokens(
            relevant_message_prompt
        )
        current_budget -= relevant_message_token_count

        # Example message prompt
        example_message_prompt = self.get_example_message_prompt(
            prompt_input.example_commit_messages,
            token_budget=min(
                self.token_apportionment.commit_message_len
                - relevant_message_token_count,
                current_budget,
            ),
        )

        message = (
            changed_files_prompt
            + diff_prompt
            + relevant_message_prompt
            + example_message_prompt
            + self.trailing_instructions
        )
        return StructuredChatPromptOutput(
            system_prompt=self.system_prompt,
            message=message,
            chat_history=[],
            retrieved_chunks_in_prompt=[],
            tools=[],
            tool_definitions=prompt_input.tool_definitions or [],
        )
