"""Sanity tests for commit message generation prompt formatters."""

from base.prompt_format_chat.generate_commit_message_prompt_formatter import (
    GenerateCommitMessagePromptFormatter,
)
from base.prompt_format_chat.lib.system_prompts import get_commit_message_system_prompt
from base.prompt_format_chat.lib.token_counter import Rough<PERSON><PERSON><PERSON>ounter
from base.prompt_format_chat.prompt_formatter import (
    ChangedFileStats,
    ChatPromptInput,
    GenerateCommitMessageTokenApportionment,
    PerFileChangeStats,
    PerTypeChangedFileStats,
)
from base.static_analysis.common import assert_str_eq


def test_generate_commit_message_prompt_formatter():
    """Sanity check for commit message generation prompt formatter."""
    token_counter = RoughTokenCounter()
    token_apportionment = GenerateCommitMessageTokenApportionment(
        path_len=0,
        message_len=0,
        chat_history_len=0,
        prefix_len=0,
        selected_code_len=0,
        suffix_len=0,
        changed_files_summary_line_threshold=900,
        diff_len=9_216,
        commit_message_len=3_072,
        relevant_message_len=1_024,
        max_prompt_len=12_288,
    )
    formatter = GenerateCommitMessagePromptFormatter(token_counter, token_apportionment)

    prompt_input = ChatPromptInput(
        message="",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        changed_file_stats=ChangedFileStats(),
        diff="""DIFF HERE""",
        relevant_commit_messages=[],
        example_commit_messages=["Example commit message 0"],
    )

    expected_message = """Below are the per-file diffs of the commit:

DIFF HERE


Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:


Commit message example 0:
Example commit message 0


Above are all the commit message examples.


Do not include boilerplate text like "Here is the commit message:" in your response.."""

    # We care that the commit message diff and the commit generation prompt are present.
    prompt_output = formatter.format_prompt(prompt_input)
    assert_str_eq(
        prompt_output.message,
        expected_message,
        lambda: "Prompt output message does not match expected message",
    )
    assert prompt_output.system_prompt == get_commit_message_system_prompt()


def test_generate_commit_message_prompt_formatter_v2_no_example():
    """Sanity check for commit message generation prompt formatter V2."""
    token_counter = RoughTokenCounter()
    token_apportionment = GenerateCommitMessageTokenApportionment(
        path_len=0,
        message_len=0,
        chat_history_len=0,
        prefix_len=0,
        selected_code_len=0,
        suffix_len=0,
        changed_files_summary_line_threshold=900,
        diff_len=9_216,
        commit_message_len=3_072,
        relevant_message_len=1_024,
        max_prompt_len=12_288,
    )
    formatter = GenerateCommitMessagePromptFormatter(token_counter, token_apportionment)

    prompt_input = ChatPromptInput(
        message="",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        changed_file_stats=ChangedFileStats(),
        diff="""DIFF HERE""",
        relevant_commit_messages=[],
        example_commit_messages=[],
    )

    expected_message = """Below are the per-file diffs of the commit:

DIFF HERE


Do not include boilerplate text like "Here is the commit message:" in your response.."""

    # We care that the commit message diff and the commit generation prompt are present.
    prompt_output = formatter.format_prompt(prompt_input)
    assert_str_eq(
        prompt_output.message,
        expected_message,
        lambda: "Prompt output message does not match expected message",
    )
    assert prompt_output.system_prompt == get_commit_message_system_prompt()


def test_generate_commit_message_prompt_formatter_v2_relevant_commits():
    """Sanity check for commit message generation prompt formatter V2."""
    token_counter = RoughTokenCounter()
    token_apportionment = GenerateCommitMessageTokenApportionment(
        path_len=0,
        message_len=0,
        chat_history_len=0,
        prefix_len=0,
        selected_code_len=0,
        suffix_len=0,
        changed_files_summary_line_threshold=900,
        diff_len=9_216,
        commit_message_len=3_072,
        relevant_message_len=1_024,
        max_prompt_len=12_288,
    )
    formatter = GenerateCommitMessagePromptFormatter(token_counter, token_apportionment)

    prompt_input = ChatPromptInput(
        message="",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        changed_file_stats=ChangedFileStats(),
        diff="""DIFF HERE""",
        relevant_commit_messages=[
            "Relevant commit message 0",
            "Relevant commit message 1",
        ],
        example_commit_messages=["Example commit message 0"],
    )

    expected_message = """Below are the per-file diffs of the commit:

DIFF HERE


Below are the immediately preceding commit messages by the current commit author. They might be relevant for understanding the context and intent of the current commit. If the messages indicate a stack style and the current diffs look relevant, increment the stack counter by 1.

Commit message for HEAD:
Relevant commit message 0


Commit message for HEAD~1:
Relevant commit message 1


Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:


Commit message example 0:
Example commit message 0


Above are all the commit message examples.


Do not include boilerplate text like "Here is the commit message:" in your response.."""

    # We care that the commit message diff and the commit generation prompt are present.
    prompt_output = formatter.format_prompt(prompt_input)
    assert_str_eq(
        prompt_output.message,
        expected_message,
        lambda: "Prompt output message does not match expected message",
    )
    assert prompt_output.system_prompt == get_commit_message_system_prompt()


def test_generate_commit_message_prompt_formatter_v2_changed_file_stats():
    """Sanity check for commit message generation prompt formatter V2."""
    token_counter = RoughTokenCounter()
    token_apportionment = GenerateCommitMessageTokenApportionment(
        path_len=0,
        message_len=0,
        chat_history_len=0,
        prefix_len=0,
        selected_code_len=0,
        suffix_len=0,
        changed_files_summary_line_threshold=0,
        diff_len=9_216,
        commit_message_len=3_072,
        relevant_message_len=1_024,
        max_prompt_len=12_288,
    )
    formatter = GenerateCommitMessagePromptFormatter(token_counter, token_apportionment)

    prompt_input = ChatPromptInput(
        message="",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        changed_file_stats=ChangedFileStats(
            added_file_stats=PerTypeChangedFileStats(
                changed_file_count=1,
                per_file_change_stats_head=[
                    PerFileChangeStats(
                        path="added_file0.py",
                        insertion_count=16,
                        deletion_count=0,
                        old_path="",
                    )
                ],
            ),
            modified_file_stats=PerTypeChangedFileStats(
                changed_file_count=15,
                per_file_change_stats_head=[
                    PerFileChangeStats(
                        path=f"modified_file{i}.py",
                        insertion_count=i + 1,
                        deletion_count=i,
                        old_path="",
                    )
                    for i in range(5)
                ],
                per_file_change_stats_tail=[
                    PerFileChangeStats(
                        path=f"modified_file{i}.py",
                        insertion_count=i + 1,
                        deletion_count=i,
                        old_path="",
                    )
                    for i in range(10, 15)
                ],
            ),
        ),
        diff="""DIFF HERE""",
        relevant_commit_messages=[],
        example_commit_messages=["Example commit message 0"],
    )

    expected_message = """The commit contains the following changes:
Modified 15 files:
    M +1 -0 modified_file0.py
    M +2 -1 modified_file1.py
    M +3 -2 modified_file2.py
    M +4 -3 modified_file3.py
    M +5 -4 modified_file4.py
    ...
    M +11 -10 modified_file10.py
    M +12 -11 modified_file11.py
    M +13 -12 modified_file12.py
    M +14 -13 modified_file13.py
    M +15 -14 modified_file14.py
Added 1 files:
    A +16 -0 added_file0.py


Below are the per-file diffs of the commit:

DIFF HERE


Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:


Commit message example 0:
Example commit message 0


Above are all the commit message examples.


Do not include boilerplate text like "Here is the commit message:" in your response.."""

    # We care that the commit message diff and the commit generation prompt are present.
    prompt_output = formatter.format_prompt(prompt_input)
    assert_str_eq(
        prompt_output.message,
        expected_message,
        lambda: "Prompt output message does not match expected message",
    )
    assert prompt_output.system_prompt == get_commit_message_system_prompt()


def test_generate_commit_message_prompt_formatter_v2_realistic():
    """Sanity check for commit message generation prompt formatter V2."""
    token_counter = RoughTokenCounter()
    token_apportionment = GenerateCommitMessageTokenApportionment(
        path_len=0,
        message_len=0,
        chat_history_len=0,
        prefix_len=0,
        selected_code_len=0,
        suffix_len=0,
        changed_files_summary_line_threshold=900,
        diff_len=9_216,
        commit_message_len=3_072,
        relevant_message_len=1_024,
        max_prompt_len=12_288,
    )
    formatter = GenerateCommitMessagePromptFormatter(token_counter, token_apportionment)

    prompt_input = ChatPromptInput(
        message="",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        changed_file_stats=ChangedFileStats(),
        diff="""diff --git a/clients/vscode/src/augment-api.ts b/clients/vscode/src/augment-api.ts
index 72c9b6a3c..84db8f99f 100644
--- a/clients/vscode/src/augment-api.ts
+++ b/clients/vscode/src/augment-api.ts
@@ -1784,7 +1784,10 @@ class APIServerImpl implements APIServer {
         pathName?: string,
         language?: string,
         sessionId?: string,
-        disableAutoExternalSources?: boolean
+        disableAutoExternalSources?: boolean,
+        changedFileStats?: ChangedFileStats,
+        diff?: string,
+        generateCommitMessageData?: GenerateCommitMessageData
     ): Promise<AsyncIterable<ChatResult>> {
         const config = this._configListener.config;
         const payload: ChatPayload = {
diff --git a/clients/vscode/src/commands/generate-commit-message.ts b/clients/vscode/src/commands/generate-commit-message.ts
index 16ca5ab53..f9425731a 100644
--- a/clients/vscode/src/commands/generate-commit-message.ts
:...skipping...
diff --git a/clients/vscode/src/augment-api.ts b/clients/vscode/src/augment-api.ts
index 72c9b6a3c..84db8f99f 100644
--- a/clients/vscode/src/augment-api.ts
+++ b/clients/vscode/src/augment-api.ts
@@ -1784,7 +1784,10 @@ class APIServerImpl implements APIServer {
         pathName?: string,
         language?: string,
         sessionId?: string,
-        disableAutoExternalSources?: boolean
+        disableAutoExternalSources?: boolean,
+        changedFileStats?: ChangedFileStats,
+        diff?: string,
+        generateCommitMessageData?: GenerateCommitMessageData
     ): Promise<AsyncIterable<ChatResult>> {
         const config = this._configListener.config;
         const payload: ChatPayload = {""",
        relevant_commit_messages=[],
        example_commit_messages=["Example commit message 0"],
    )

    expected_message = """Below are the per-file diffs of the commit:

diff --git a/clients/vscode/src/augment-api.ts b/clients/vscode/src/augment-api.ts
index 72c9b6a3c..84db8f99f 100644
--- a/clients/vscode/src/augment-api.ts
+++ b/clients/vscode/src/augment-api.ts
@@ -1784,7 +1784,10 @@ class APIServerImpl implements APIServer {
         pathName?: string,
         language?: string,
         sessionId?: string,
-        disableAutoExternalSources?: boolean
+        disableAutoExternalSources?: boolean,
+        changedFileStats?: ChangedFileStats,
+        diff?: string,
+        generateCommitMessageData?: GenerateCommitMessageData
     ): Promise<AsyncIterable<ChatResult>> {
         const config = this._configListener.config;
         const payload: ChatPayload = {
diff --git a/clients/vscode/src/commands/generate-commit-message.ts b/clients/vscode/src/commands/generate-commit-message.ts
index 16ca5ab53..f9425731a 100644
--- a/clients/vscode/src/commands/generate-commit-message.ts
:...skipping...
diff --git a/clients/vscode/src/augment-api.ts b/clients/vscode/src/augment-api.ts
index 72c9b6a3c..84db8f99f 100644
--- a/clients/vscode/src/augment-api.ts
+++ b/clients/vscode/src/augment-api.ts
@@ -1784,7 +1784,10 @@ class APIServerImpl implements APIServer {
         pathName?: string,
         language?: string,
         sessionId?: string,
-        disableAutoExternalSources?: boolean
+        disableAutoExternalSources?: boolean,
+        changedFileStats?: ChangedFileStats,
+        diff?: string,
+        generateCommitMessageData?: GenerateCommitMessageData
     ): Promise<AsyncIterable<ChatResult>> {
         const config = this._configListener.config;
         const payload: ChatPayload = {


Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:


Commit message example 0:
Example commit message 0


Above are all the commit message examples.


Do not include boilerplate text like "Here is the commit message:" in your response.."""

    # We care that the commit message diff and the commit generation prompt are present.
    prompt_output = formatter.format_prompt(prompt_input)
    assert_str_eq(
        prompt_output.message,
        expected_message,
        lambda: "Prompt output message does not match expected message",
    )
    assert prompt_output.system_prompt == get_commit_message_system_prompt()
