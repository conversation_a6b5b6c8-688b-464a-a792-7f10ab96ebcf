import dataclasses
import datetime
import logging
from dataclasses import dataclass
from typing import Callable

import base.feature_flags
from base.prompt_format.common import (
    ChatRe<PERSON>Node,
    ChatRequestNodeType,
    ChatRequestText,
    inject_as_first_text_node_in_request_message,
)
from base.prompt_format_chat.agent_prompt_usage_metrics import (
    PromptMetrics,
    record_prompt_metrics,
)
from base.prompt_format_chat.lib.chat_history_builder import (
    format_agent_history,
    inject_selected_code_into_chat_history,
    postprocess_chat_history_tool_use,
    sanitize_known_client_faults,
)
from base.prompt_format_chat.lib.edit_events_lib import inject_edit_events
from base.prompt_format_chat.lib.ide_state_utils import (
    get_latest_ide_state,
    inject_ide_state_in_request_message,
    inject_ide_state_into_history,
)
from base.prompt_format_chat.lib.rules_prompt_builder import build_custom_prompt
from base.prompt_format_chat.lib.selected_code_prompt_formatter_v2 import (
    SelectedCodePromptFormatterV2,
)
from base.prompt_format_chat.lib.string_formatter import <PERSON><PERSON><PERSON><PERSON>atter
from base.prompt_format_chat.lib.system_prompts import (
    AgentSystemPromptFormatter,
    EditEventsFormatter,
    IdeStateFormatter,
    get_agent_ide_state_prompt_formatter_v1,
    get_agent_system_prompt_formatter_v1,
    get_edit_events_prompt_formatter_v1,
    get_edit_events_prompt_formatter_v2,
    get_supervisor_prompt_on_every_turn,
)
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.prompt_format_chat.lib.tool_definitions import standardize_tool_definitions
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    ExceedContextLength,
    StructuredChatPromptFormatter,
    StructuredChatPromptOutput,
)

logger = logging.getLogger(__name__)

_default_token_apportionent = ChatTokenApportionment(
    path_len=256,
    prefix_len=1024 * 2,
    chat_history_len=0,
    suffix_len=1024 * 2,
    retrieval_len=0,
    max_prompt_len=1024 * 200,  # 200k model
    token_budget_to_trigger_truncation=1024 * 120,  # 120k truncation budget
    # Deprecated fields
    message_len=-1,
    selected_code_len=8192,
)


_USE_IDE_STATE_IN_PROMPT = base.feature_flags.BoolFlag(
    "agents_use_ide_state_in_prompt", default=False
)
_ADD_SUPERVISOR_PROMPT_EVERY_TURN = base.feature_flags.BoolFlag(
    "agents_add_supervisor_prompt_every_turn", default=False
)
_ADD_SUPERVISOR_PROMPT_TO_PREFILL_EVERY_TURN = base.feature_flags.BoolFlag(
    "agents_add_supervisor_prompt_to_prefill_every_turn", default=False
)
_ADD_SYSTEM_PROMPT_TO_PREFILL_EVERY_TURN = base.feature_flags.BoolFlag(
    "agents_add_system_prompt_to_prefill_every_turn", default=False
)
_ADD_DATE_TO_SYSTEM_PROMPT = base.feature_flags.BoolFlag(
    "agents_add_date_to_system_prompt", default=False
)
_USE_EDIT_EVENTS_IN_PROMPT = base.feature_flags.BoolFlag(
    "agents_use_edit_events_in_prompt", default=False
)
_EDIT_EVENTS_VERSION = base.feature_flags.StringFlag(
    "agents_edit_events_version", default=""
)


@dataclass
class EditEventsFormatterProvider:
    token_counter: TokenCounter
    formatters: dict[str, EditEventsFormatter]
    default_formatter: EditEventsFormatter
    version_flag: base.feature_flags.StringFlag

    def get(self) -> EditEventsFormatter | None:
        version = self.version_flag.get(base.feature_flags.get_global_context())
        return self.formatters.get(version, self.default_formatter)

    @classmethod
    def create(cls, token_counter: TokenCounter):
        formatters = {
            "v0": None,
            "v1": get_edit_events_prompt_formatter_v1(token_counter),
            "v2": get_edit_events_prompt_formatter_v2(token_counter),
        }
        default_formatter = formatters["v1"]
        return EditEventsFormatterProvider(
            token_counter,
            formatters,
            default_formatter,
            _EDIT_EVENTS_VERSION,
        )


class StructuredBinksAgentPromptFormatter(StructuredChatPromptFormatter):
    """The class formats prompts for the chat model, with an agent system prompt."""

    def __init__(
        self,
        token_counter: TokenCounter,
        system_prompt: AgentSystemPromptFormatter,
        selected_code_formatter: SelectedCodePromptFormatterV2,
        token_apportionment: ChatTokenApportionment,
        model_name: str,
        creator: str,
        ide_state_prompt: IdeStateFormatter | None = None,
        edit_events_prompt_provider: EditEventsFormatterProvider | None = None,
        supervisor_prompt_on_every_turn: str = "",
        use_fixed_budget_selected_code: bool = False,
    ):
        self.token_counter = token_counter
        self.token_apportionment = token_apportionment
        self.system_prompt_formatter = system_prompt
        self.selected_code_formatter = selected_code_formatter
        self.model_name = model_name
        self.creator = creator
        self.ide_state_prompt = ide_state_prompt
        self.edit_events_prompt_provider = edit_events_prompt_provider
        self.supervisor_prompt_on_every_turn = supervisor_prompt_on_every_turn
        self.use_fixed_budget_selected_code = use_fixed_budget_selected_code

    def format_prompt(
        self, prompt_input: ChatPromptInput
    ) -> StructuredChatPromptOutput:
        """Format prompt for Binks Llama3-based code chat model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """
        use_ide_state_in_prompt = (
            self.ide_state_prompt is not None
            and _USE_IDE_STATE_IN_PROMPT.get(base.feature_flags.get_global_context())
        )

        add_supervisor_prompt_on_every_turn = (
            self.supervisor_prompt_on_every_turn != ""
            and _ADD_SUPERVISOR_PROMPT_EVERY_TURN.get(
                base.feature_flags.get_global_context()
            )
        )

        add_supervisor_prompt_to_prefill_on_every_turn = (
            self.supervisor_prompt_on_every_turn != ""
            and _ADD_SUPERVISOR_PROMPT_TO_PREFILL_EVERY_TURN.get(
                base.feature_flags.get_global_context()
            )
        )

        add_system_prompt_to_prefill_on_every_turn = (
            _ADD_SYSTEM_PROMPT_TO_PREFILL_EVERY_TURN.get(
                base.feature_flags.get_global_context()
            )
        )

        add_date_to_system_prompt = _ADD_DATE_TO_SYSTEM_PROMPT.get(
            base.feature_flags.get_global_context()
        )

        use_edit_events_in_prompt = _USE_EDIT_EVENTS_IN_PROMPT.get(
            base.feature_flags.get_global_context()
        )

        max_prompt_len = int(0.975 * self.token_apportionment.max_prompt_len)
        prompt_metrics = PromptMetrics(formatter_type="binks_agent")

        # First, get the system prompt

        # Unlike in the non-agent binks formatter, we won't be increasing max_prompt_len
        # based upon the length of this custom prompt. Long user/workspace guidelines will
        # eat into the history/tool result budget.
        custom_guidelines = build_custom_prompt(
            prompt_input.user_guidelines,
            prompt_input.workspace_guidelines,
            prompt_input.persona_type,
            prompt_input.rules if hasattr(prompt_input, "rules") else None,
        )

        # Note: not all versions of system_prompt incorporate all of the provided fields
        system_prompt = self.system_prompt_formatter.format(
            {
                "model_name": self.model_name,
                "creator": self.creator,
                "memories": prompt_input.memories or "",
                "tasklist": prompt_input.tasklist or "",
                "formatted_custom_guidelines": custom_guidelines,
                "tools": [tool.name for tool in (prompt_input.tool_definitions or [])],
                # TODO(arun): We really should get the date from the client.
                "current_date": datetime.datetime.now().strftime("%Y-%m-%d")
                if add_date_to_system_prompt
                else "",
            }
        )

        # Next, get the current message, always as a node list.
        cur_message = (
            [
                ChatRequestNode(
                    id=0,
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(content=prompt_input.message),
                    tool_result_node=None,
                )
            ]
            if isinstance(prompt_input.message, str)
            else prompt_input.message
        )

        if add_supervisor_prompt_on_every_turn:
            cur_message = inject_as_first_text_node_in_request_message(
                cur_message, self.supervisor_prompt_on_every_turn
            )

        chat_history = list(prompt_input.chat_history)

        # Inject edit events before clipping so that we budget tokens properly
        if use_edit_events_in_prompt:
            edit_events_prompt = (
                self.edit_events_prompt_provider.get()
                if self.edit_events_prompt_provider
                else None
            )
            chat_history, cur_message, edit_events_token_ct = inject_edit_events(
                chat_history,
                cur_message,
                self.token_counter,
                edit_events_prompt,
            )

        assert isinstance(cur_message, list)
        # First remove any IDE state nodes from the current message.
        cur_message = [
            node for node in cur_message if node.type != ChatRequestNodeType.IDE_STATE
        ]
        ide_state_info = get_latest_ide_state(prompt_input)
        has_ide_state = ide_state_info["workspace_folders"] is not None
        if has_ide_state and use_ide_state_in_prompt:
            assert self.ide_state_prompt is not None
            # Estimate the number of tokens for the ide state prompt -- we'll make an
            # overestimate by always treating this as a "first message", where
            # everything changes and adding some buffer. The actual tokens used is quite
            # small (~200), so this isn't too inefficient.
            ide_state_info["first_message"] = True
            estimated_ide_state_token_ct = (
                self.ide_state_prompt.format_and_count_tokens(ide_state_info)
            )
        else:
            estimated_ide_state_token_ct = 0

        system_prompt_tokens = self.token_counter.count_tokens(system_prompt)
        prompt_metrics.system_prompt_tokens = system_prompt_tokens
        cur_message_tokens = self.token_counter.count_tokens_in_request(cur_message)
        prompt_metrics.cur_message_tokens = cur_message_tokens
        cur_message_tool_result_tokens = self.token_counter.count_tokens_in_request(
            cur_message, node_type=ChatRequestNodeType.TOOL_RESULT
        )
        prompt_metrics.tool_result_tokens = cur_message_tool_result_tokens

        assert cur_message_tokens >= cur_message_tool_result_tokens

        # Compute the token budget remaining after system prompt and message
        token_budget = (
            max_prompt_len
            - system_prompt_tokens
            - (cur_message_tokens - cur_message_tool_result_tokens)
            - estimated_ide_state_token_ct
        )

        # We'll account for tool results budget separately.
        tool_results_token_budget = self.token_apportionment.tool_results_len

        # If we are already out of budget after just the system prompt and the current
        # message even before any chat history, raise an error
        if token_budget < 0:
            raise ExceedContextLength(
                f"The prompt is too long, message length: {cur_message_tokens} "
                f"system prompt length: {system_prompt_tokens} "
                f"max_prompt_len: {max_prompt_len})."
            )

        # If we are already out of budget for tool results, raise an error
        if tool_results_token_budget < 0:
            raise ExceedContextLength(
                f"The prompt is too long, tool results length: {cur_message_tool_result_tokens} "
                f"tool results budget: {self.token_apportionment.tool_results_len} "
                f"max_prompt_len: {max_prompt_len})."
            )

        # Format selected code if present
        selected_code_section = ""
        if token_budget > 0:
            selected_code_section, _, _ = self.selected_code_formatter.format(
                prompt_input,
                self.token_apportionment.selected_code_len
                if self.use_fixed_budget_selected_code
                else token_budget,
            )
            # AU-7372: If the selected code is injected into history, this is double-counting
            # If it's injected into the current message, then it's appropriately accounted
            # for by decrementing here.
            if selected_code_section:
                selected_code_tokens = self.token_counter.count_tokens(
                    selected_code_section
                )
                token_budget -= selected_code_tokens
                prompt_metrics.selected_code_tokens = selected_code_tokens

        clipped_chat_history = sanitize_known_client_faults(
            chat_history, prompt_input.message
        )
        if selected_code_section:
            cur_message, clipped_chat_history = inject_selected_code_into_chat_history(
                cur_message,
                clipped_chat_history,
                selected_code_section,
                prompt_input.context_code_exchange_request_id,
                self.selected_code_formatter.selected_code_response_message,
            )

        # Next grab clipped chat history
        if token_budget > 0:
            original_length = len(clipped_chat_history)
            clipped_chat_history, chat_token_ct = format_agent_history(
                chat_history=clipped_chat_history,
                token_counter=self.token_counter,
                token_budget=self.token_apportionment.token_budget_to_trigger_truncation,
                ide_state_prompt=(
                    self.ide_state_prompt if use_ide_state_in_prompt else None
                ),
                tool_definitions=prompt_input.tool_definitions,
            )
            token_budget -= chat_token_ct
            prompt_metrics.chat_history_tokens = chat_token_ct
            if clipped_turns := (original_length - len(clipped_chat_history)):
                logger.info("Truncated %d turns", clipped_turns)

        # Now actually handle the IDE state.
        if has_ide_state and not use_ide_state_in_prompt:
            logger.info(
                "Not using IDE state in prompt, but has_ide_state is True. Dropping IDE state nodes"
            )
            # If we don't have a prompt or are using empty text, we'll just drop the ide
            # state node.
            cur_message = inject_ide_state_in_request_message(cur_message, "")
            clipped_chat_history, _ = inject_ide_state_into_history(
                clipped_chat_history, self.token_counter
            )
        elif has_ide_state and use_ide_state_in_prompt:
            assert self.ide_state_prompt is not None
            ide_state_info = get_latest_ide_state(
                dataclasses.replace(
                    prompt_input,
                    chat_history=clipped_chat_history,
                )
            )
            # IMPORTANT(arun): do NOT log workspace folders or current terminal directly
            # as that would leak personal information.
            logger.info(
                "IDE state summary: "
                + f"is_first_message={ide_state_info['first_message']}, "
                + f"workspace_folders_changed={ide_state_info['workspace_folders_changed']}, "
                + f"workspace_folders_nonempty={bool(ide_state_info['workspace_folders'])}, "
                + f"current_terminal_set={bool(ide_state_info['current_terminal'])}, "
                + f"current_terminal_changed={ide_state_info['current_terminal_changed']}"
            )

            ide_state_text = self.ide_state_prompt.format(ide_state_info)
            actual_ide_state_token_ct = self.token_counter.count_tokens(ide_state_text)
            token_budget += actual_ide_state_token_ct - estimated_ide_state_token_ct
            cur_message = inject_ide_state_in_request_message(
                cur_message, ide_state_text
            )

            clipped_chat_history, actual_ide_state_history_ct = (
                inject_ide_state_into_history(
                    clipped_chat_history,
                    self.token_counter,
                    self.ide_state_prompt,
                    prompt_input.tool_definitions,
                )
            )
            token_budget += actual_ide_state_history_ct

        clipped_chat_history = postprocess_chat_history_tool_use(
            clipped_chat_history,
            cur_message,
            self.token_counter,
            tool_results_token_budget,
        )

        prefill_parts = []
        if add_system_prompt_to_prefill_on_every_turn:
            prefill_parts.append(f"""
<supervisor>
SYSTEM PROMPT REMINDER:
{system_prompt}
</supervisor>""")
        if add_supervisor_prompt_to_prefill_on_every_turn:
            prefill_parts.append(self.supervisor_prompt_on_every_turn)

        prefill = "\n".join(prefill_parts).strip() if prefill_parts else None

        tool_definitions = standardize_tool_definitions(
            prompt_input.tool_definitions or []
        )

        # Record metrics
        logger.info("Prompt metrics: %s", prompt_metrics)
        record_prompt_metrics(prompt_metrics)

        return StructuredChatPromptOutput(
            system_prompt=system_prompt,
            chat_history=clipped_chat_history,
            message=cur_message,
            retrieved_chunks_in_prompt=[],
            retrieval_as_tool=False,
            prefill=prefill,
            tool_definitions=tool_definitions,
        )

    @classmethod
    def create(
        cls,
        token_counter: TokenCounter,
        model_name: str,
        creator: str,
        token_apportionment: ChatTokenApportionment | None = None,
        system_prompt_factory: Callable[
            [TokenCounter], AgentSystemPromptFormatter
        ] = get_agent_system_prompt_formatter_v1,
        retrieval_section_version: int = 5,
        ide_state_prompt_factory: Callable[
            [TokenCounter], JinjaFormatter
        ] = get_agent_ide_state_prompt_formatter_v1,
        supervisor_prompt_on_every_turn: str = get_supervisor_prompt_on_every_turn(),
        use_fixed_budget_selected_code: bool = False,
    ):
        """Factory method for creating instances of `StructuredBinksAgentPromptFormatter`.

        Args:
            token_counter: The token counter.
            model_name: The name of the model.
            creator: The creator of the model.
            token_apportionment: The token apportionment.
            system_prompt_factory: The system prompt factory.

        Returns:
            An instance of `StructuredBinksAgentPromptFormatter`.
        """
        if token_apportionment is None:
            token_apportionment = _default_token_apportionent
        system_prompt = system_prompt_factory(token_counter)
        selected_code_formatter = SelectedCodePromptFormatterV2(
            token_counter=token_counter,
            max_path_tokens=token_apportionment.path_len,
            max_prefix_tokens=token_apportionment.prefix_len,
            max_suffix_tokens=token_apportionment.suffix_len,
            version=retrieval_section_version,
        )
        ide_state_prompt = ide_state_prompt_factory(token_counter)
        edit_events_prompt_provider = EditEventsFormatterProvider.create(token_counter)
        if use_fixed_budget_selected_code:
            assert token_apportionment.selected_code_len > 0, (
                "If use_fixed_budget_selected_code is True, then "
                "token_apportionment.selected_code_len must be set and greater than 0."
            )
        return StructuredBinksAgentPromptFormatter(
            token_counter,
            system_prompt,
            selected_code_formatter,
            token_apportionment,
            model_name,
            creator,
            ide_state_prompt,
            edit_events_prompt_provider,
            supervisor_prompt_on_every_turn,
            use_fixed_budget_selected_code,
        )
