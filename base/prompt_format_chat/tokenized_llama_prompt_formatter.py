from textwrap import dedent

from base.prompt_format.common import (
    get_request_message_as_text,
    get_response_message_as_text,
)

from base.prompt_format_chat.prompt_formatter import (
    StructToTokensPromptFormatter,
    StructuredChatPromptOutput,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.tokenized_string_formatter import TokenizedStringFormatter
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer

BUFFER_SIZE_FOR_SPEC_TOKENS = 100


class StructToTokensLlama3PromptFormatter(StructToTokensPromptFormatter):
    """The class formats tokenized prompts for Llama3-based chat models.

    For prompt structure of the Llama3 models, see:
    https://llama.meta.com/docs/model-cards-and-prompt-formats/meta-llama-3

    It renders StructuredChatPromptOutput into tokens, adding special token delimiters
    where needed as follows:.

    <|begin_of_text|><|start_header_id|>system<|end_header_id|>

    [insert system prompt]<|eot_id|>\
    [for conversation turn:]
        <|start_header_id|>user<|end_header_id|>

        [insert user message]<|eot_id|>\
        <|start_header_id|>assistant<|end_header_id|>

        [insert assistant message]<|eot_id|>\
    <|start_header_id|>user<|end_header_id|>

    [insert user message]<|eot_id|>\
    <|start_header_id|>assistant<|end_header_id|>
    """

    def __init__(
        self,
        tokenizer: Llama3InstructTokenizer,
    ):
        assert isinstance(
            tokenizer, Llama3InstructTokenizer
        ), f"{tokenizer=} must be a Llama3InstructTokenizer."
        self.tokenizer = tokenizer

        self.special_tokens_values = {
            "bos_token": list(self.tokenizer.special_tokens.begin_sequence),
            "eod_token": [self.tokenizer.special_tokens.eod_token],
            "start_header": [self.tokenizer.special_tokens.start_header_id],
            "end_header": [self.tokenizer.special_tokens.end_header_id],
        }

        self.system_prompt_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {bos_token}{start_header}system{end_header}

            {system_prompt}{eod_token}"""),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )
        self.chat_history_exchange_message_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {start_header}user{end_header}

            {user_message}{eod_token}{start_header}assistant{end_header}

            {assistant_message}{eod_token}"""),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )
        self.cur_message_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {start_header}user{end_header}

            {user_message}{eod_token}{start_header}assistant{end_header}

            """),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )

    @property
    def reserved_token_budget(self) -> int:
        return BUFFER_SIZE_FOR_SPEC_TOKENS

    def format_prompt(
        self, prompt_input: StructuredChatPromptOutput
    ) -> TokenizedChatPromptOutput:
        """Format prompt for Binks Llama3-based code chat model.

        Args:
            prompt_input: A structured chat prompt.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """
        flattened_prompt: list[int] = self.system_prompt_formatter.format(
            values={
                "system_prompt": self.tokenizer.tokenize_safe(
                    prompt_input.system_prompt or ""
                )
            }
        )

        for message in prompt_input.chat_history:
            flattened_prompt += self.chat_history_exchange_message_formatter.format(
                values={
                    "user_message": self.tokenizer.tokenize_safe(
                        get_request_message_as_text(message.request_message)
                    ),
                    "assistant_message": self.tokenizer.tokenize_safe(
                        get_response_message_as_text(message.response_text)
                    ),
                }
            )

        flattened_prompt += self.cur_message_formatter.format(
            values={
                "user_message": self.tokenizer.tokenize_safe(
                    get_request_message_as_text(prompt_input.message)
                )
            }
        )

        return TokenizedChatPromptOutput(
            flattened_prompt, prompt_input.retrieved_chunks_in_prompt
        )
