"""Class for formatting and tokenizing strings."""

from collections.abc import Mapping
import re
from typing import Optional, Sequence

from base.prompt_format_chat.prompt_formatter import (
    TokenList,
)
from base.tokenizers.tokenizer import Tokenizer


PLACEHOLDER_PATTERN = r"({[^}]+}|[^{}]+)"


def split_template(template: str) -> list[str]:
    """Splits a given template string into placeholders and plain text segments.

    This function uses a regex pattern to identify and separate text enclosed in curly
    braces (placeholders) and text outside of curly braces (regular text). It returns
    all segments as a list. For simplicity, this function assumes that curly braces are
    used exclusively to denote placeholders and are not part of regular text.

    Examples:
    - Input: "Hello, {name}! Your balance is {balance}."
    - Output: ["Hello, ", "{name}", "! Your balance is ", "{balance}", "."]

    Args:
        template (str): The template string to split.

    Returns:
        list[str]: A list of placeholders and text segments from the template.
    """
    return re.findall(PLACEHOLDER_PATTERN, template)


class TokenizedStringFormatter:
    """A class like str.format that tokenizes results, assuming placeholders are token lists."""

    def __init__(
        self,
        template: str,
        tokenizer: Tokenizer,
        default_values: Optional[Mapping[str, Sequence[int]]] = None,
    ):
        self.tokenizer = tokenizer
        parts = split_template(template)
        self.tokenized_parts_or_placeholders: list[TokenList | str] = [
            part[1:-1]
            if part.startswith("{") and part.endswith("}")
            else tokenizer.tokenize_safe(part)
            for part in parts
        ]
        self.default_values = default_values or {}

    def format(self, values: Mapping[str, Sequence[int]]) -> TokenList:
        """Formats a string with placeholders replaced by tokens or files."""
        result: TokenList = []
        for part in self.tokenized_parts_or_placeholders:
            if isinstance(part, str):
                if part not in values:
                    if part in self.default_values:
                        result.extend(self.default_values[part])
                    else:
                        raise ValueError(f"Missing value for placeholder {part}")
                else:
                    result.extend(values[part])
            else:
                result.extend(part)
        return result

    def format_and_count_tokens(self, values: Mapping[str, Sequence[int]]) -> int:
        """Formats string with placeholders and returns total token count."""
        result: int = 0
        for part in self.tokenized_parts_or_placeholders:
            if isinstance(part, str):
                if part not in values:
                    if part in self.default_values:
                        result += len(self.default_values[part])
                    else:
                        raise ValueError(f"Missing value for placeholder {part}")
                else:
                    result += len(values[part])
            else:
                result += len(part)
        return result
