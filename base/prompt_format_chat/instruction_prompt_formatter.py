"""Prompt formatter for instruction."""

from dataclasses import dataclass
import dataclasses
from typing import Literal, Iterable, Sequence, Tuple

from dataclasses_json import dataclass_json
from base.prompt_format.common import (
    Exchange,
    LineEnding,
    PromptChunk,
    detect_line_ending,
    normalize_line_endings,
)
from base.prompt_format_chat.lib.rules_prompt_builder import build_custom_prompt
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    ExceedContextLength,
)

INSTRUCTION_SYSTEM_PROMPT = """\
You are highly advanced and intelligent AI code assistant.
Your task is to edit a file based on user's instructions.
"""


@dataclass_json
@dataclass(frozen=True)
class InstructionTokenApportionment:
    """Stores the apportionment of the tokens for the code chat model."""

    path_len: int
    """The number of tokens of the path to include."""

    prefix_len: int
    """The number of tokens of the prefix to include."""

    suffix_len: int
    """The number of tokens of the suffix to include."""

    chat_history_len: int
    """The total number of tokens of the conversation history to include. -1 means no limit."""

    retrieval_len: int
    """The number of tokens from retrieved chunks to include. -1 means no limit."""

    max_prompt_len: int
    """The number of tokens of the total prompt length, which will be used to compute the budget for retrieved chunks."""

    def __post_init__(self):
        assert self.path_len > 0
        assert self.max_prompt_len > 0
        effective_retrieval_len = self.retrieval_len if self.retrieval_len >= 0 else 0
        assert (
            self.max_prompt_len
            >= self.path_len
            + self.prefix_len
            + self.suffix_len
            + self.chat_history_len
            + effective_retrieval_len
        )


@dataclass(frozen=True)
class InstructionPromptInput:
    """The set of inputs used for constructing the instruction prompt.

    `prefix_begin`, `suffix_end` and `file_path` are required to filter overlapping retrieval chunks.
    """

    path: str
    """The file path."""

    prefix: str
    """The content before the selection."""

    selected_code: str
    """The content that the user selected."""

    instruction: str
    """ The instruction to the model."""

    suffix: str
    """The content after the selection, where its end location is at the cursor."""

    chat_history: Iterable[Exchange]
    """The conversation history as a list of request_message/response_text pairs."""

    prefix_begin: int
    """The offset in UTF-8 characters where prefix begins, relative to the beginning of file."""

    suffix_end: int
    """The offset in UTF-8 characters where suffix ends, relative to the beginning of file."""

    retrieved_chunks: Iterable[PromptChunk]
    """The retrieved chunks, sorted from high to low relevancy."""

    user_guidelines: str | None
    """Additional system prompt specified by the user."""

    workspace_guidelines: str | None
    """Additional system prompt specified by the workspace."""

    def __repr__(self):
        attributes = ",\n  ".join(
            f"{key} = {value!r}" for key, value in vars(self).items()
        )
        return f"{self.__class__.__name__}(\n  {attributes}\n)"


@dataclass(frozen=True)
class InstructionPromptOutput:
    """The set of outputs used for constructing instruction prompts."""

    system_prompt: str | None
    """The system prompt."""

    chat_history: Iterable[Exchange]
    """The truncated conversation history as a list of request_message/response_text pairs."""

    message: str
    """The generated prompt message to the model to perform instruction."""

    retrieved_chunks_in_prompt: Iterable[PromptChunk]
    """The retrieved chunks that are finally used in the prompt (e.g., after the potential filtering logic)."""

    tools: Sequence[Literal["replace_text"]] | None = None

    prefill: str | None = None
    """Text to "prefill" model response. So model is continuing generation from it."""

    selection_line_range: Tuple[int, int] | None = None
    """The line number range where the selected code starts and ends."""

    original_line_ending: LineEnding | None = None
    """The original line ending type of the selected code."""


class InstructionPromptFormatterV2:
    """A prompt formatter for code instructions"""

    def __init__(
        self,
        token_counter: TokenCounter,
        token_apportionment: InstructionTokenApportionment,
    ):
        self.token_counter = token_counter
        self.token_apportionment = token_apportionment

    def _format_code(self, prefix: str, selected_code: str, suffix: str) -> str:
        code = "```\n"
        if prefix:
            code += f"{prefix}"
        code += f"<highlighted_code>\n{selected_code}</highlighted_code>\n"
        if suffix:
            code += f"{suffix}"
        code += "```"

        return code

    def _format_example_output_and_prefill(
        self, prefix: str, selected_code: str, suffix: str
    ) -> Tuple[str, str]:
        context_prefix = "".join(prefix.splitlines(keepends=True)[-5:])
        context_suffix = "".join(suffix.splitlines(keepends=True)[:5])
        example_output = "```\n"
        if context_prefix:
            example_output += f"{context_prefix}"
        example_output += """<<<<<<< original
...
=======
...
>>>>>>> updated
"""
        if context_suffix:
            example_output += f"{context_suffix}"
        example_output += "```"

        prefill = """Here's the edited code with the requested format:
```
"""
        if context_prefix:
            prefill += f"{context_prefix}"
        prefill += f"""<<<<<<< original
{selected_code}======="""

        return example_output, prefill

    def _format_instruction_message_and_prefill(
        self,
        prompt_input: InstructionPromptInput,
    ) -> Tuple[str, str]:
        prefix = prompt_input.prefix
        selected_code = prompt_input.selected_code
        suffix = prompt_input.suffix

        # Prompt formatting assumes that all lines end with \n
        if len(prefix) > 0 and not prefix.endswith("\n"):
            prefix += "\n"
        if not selected_code.endswith("\n"):
            selected_code += "\n"
        if len(suffix) > 0 and not suffix.endswith("\n"):
            suffix += "\n"

        example_output, prefill = self._format_example_output_and_prefill(
            prefix, selected_code, suffix
        )

        message = f"""I have opened a file `{prompt_input.path}` and highlighted a part of the code (enclosed in <highlighted_code> tag):
<file path="{prompt_input.path}">
{self._format_code(prefix, selected_code, suffix)}
</file>

Please, rewrite the highlighted region according to the following instruction:
<instruction>
{prompt_input.instruction}
</instruction>

Put couple lines of context before and after the highlighted region.

Use this output format:
{example_output}
"""
        return message, prefill

    def _format_history(
        self,
        prompt_input: InstructionPromptInput,
    ) -> Tuple[Iterable[Exchange], Iterable[PromptChunk]]:
        """Format history for smart pasting, including selected code, retrieval"""

        # Currently using binks chat formatter to format history with code, retrievals - also verifies token budget
        chat_formatter = StructuredBinksPromptFormatter.create(
            token_counter=self.token_counter,
            token_apportionment=ChatTokenApportionment(
                path_len=self.token_apportionment.path_len,
                prefix_len=self.token_apportionment.prefix_len,
                chat_history_len=self.token_apportionment.chat_history_len,
                suffix_len=self.token_apportionment.suffix_len,
                max_prompt_len=self.token_apportionment.max_prompt_len,
                retrieval_len=self.token_apportionment.retrieval_len,
                # Deprecated fields
                message_len=-1,
                selected_code_len=-1,
            ),
        )
        chat_prompt_input = ChatPromptInput(
            message="",
            path=prompt_input.path,
            prefix=prompt_input.prefix,
            selected_code=prompt_input.selected_code,
            suffix=prompt_input.suffix,
            chat_history=prompt_input.chat_history,
            prefix_begin=prompt_input.prefix_begin,
            suffix_end=prompt_input.suffix_end,
            retrieved_chunks=prompt_input.retrieved_chunks,
            # Prevent injection of selected code to history, we format it ourselves as part of the message
            context_code_exchange_request_id="new",
        )
        chat_prompt_output = chat_formatter.format_prompt(chat_prompt_input)
        return (
            chat_prompt_output.chat_history,
            chat_prompt_output.retrieved_chunks_in_prompt,
        )

    def _verify_total_prompt_budget(
        self, system_prompt: str, message: str, chat_history: Iterable[Exchange]
    ) -> None:
        """Verify that the prompt is within the total budget"""
        max_prompt_len = int(self.token_apportionment.max_prompt_len * 0.975)

        system_prompt_tokens = self.token_counter.count_tokens(system_prompt)
        message_tokens = self.token_counter.count_tokens(message)
        stringified_chat_history = "\n\n".join(
            [
                f"user: {str(item.request_message)}\nassistant: {str(item.response_text)}"
                for item in chat_history
            ]
        )
        chat_history_tokens = self.token_counter.count_tokens(stringified_chat_history)
        total_len = system_prompt_tokens + message_tokens + chat_history_tokens
        if total_len > max_prompt_len:
            raise ExceedContextLength(
                f"Prompt length {total_len} exceeds maximum {max_prompt_len}."
            )

    def format_prompt(
        self,
        prompt_input: InstructionPromptInput,
    ) -> InstructionPromptOutput:
        """
        Format the prompt for based on the selected code and the last chat history exchange.
        """
        line_ending = detect_line_ending(prompt_input.selected_code)
        prompt_input = dataclasses.replace(
            prompt_input,
            prefix=normalize_line_endings(prompt_input.prefix),
            selected_code=normalize_line_endings(prompt_input.selected_code),
            suffix=normalize_line_endings(prompt_input.suffix),
        )

        message, prefill = self._format_instruction_message_and_prefill(prompt_input)
        chat_history, retrieved_chunks_in_prompt = self._format_history(prompt_input)
        system_prompt = INSTRUCTION_SYSTEM_PROMPT
        custom_prompt = build_custom_prompt(
            prompt_input.user_guidelines, prompt_input.workspace_guidelines
        )
        system_prompt += custom_prompt
        self._verify_total_prompt_budget(system_prompt, message, chat_history)

        start_line_number = len(prompt_input.prefix.splitlines(True)) + 1
        end_line_number = start_line_number + len(
            prompt_input.selected_code.splitlines(True)
        )

        return InstructionPromptOutput(
            message=message,
            system_prompt=system_prompt,
            chat_history=chat_history,
            retrieved_chunks_in_prompt=retrieved_chunks_in_prompt,
            prefill=prefill,
            selection_line_range=(start_line_number, end_line_number),
            original_line_ending=line_ending,
        )
