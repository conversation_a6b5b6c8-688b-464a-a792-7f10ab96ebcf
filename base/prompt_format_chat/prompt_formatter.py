"""Classes that are used to build the prompts for the code chat model."""

import typing
from dataclasses import dataclass, field
from typing import Iterable, Optional, Sequence

from dataclasses_json import dataclass_json

from base.diff_utils.changes import Changed
from base.diff_utils.diff_utils import File
from base.prompt_format.common import (
    Exchange,
    PersonaType,
    PromptChunk,
    RequestMessage,
    TokenList,
    ToolDefinition,
    Rule,
)
from base.tokenizers.tokenizer import Tokenizer


class ExceedContextLength(Exception):
    """Raised when some fields exceed the maximum allowed length."""

    def __init__(self, message: str = "Exceed the context length"):
        self.message = message
        super().__init__(self.message)


@dataclass_json
@dataclass(frozen=True)
class ChatTokenApportionment:
    """Stores the apportionment of the tokens for the code chat model."""

    path_len: int
    """The number of tokens of the path to include."""

    message_len: int
    """The number of tokens of the message to include."""

    chat_history_len: int
    """The total number of tokens of the conversation history to include. -1 means no limit."""

    prefix_len: int
    """The number of tokens of the prefix to include."""

    selected_code_len: int
    """The number of tokens of the selected_code to include."""

    suffix_len: int
    """The number of tokens of the suffix to include."""

    max_prompt_len: int
    """The number of tokens of the total prompt length, which will be used to compute the budget for retrieved chunks."""

    retrieval_len_per_each_user_guided_file: int = 0
    """The number of tokens for user guided retrieval will be `retrieval_len_per_each_user_guided_file` x number of files user attached."""

    retrieval_len_for_user_guided: int = 0
    """The number of tokens for user guided retrieval, a restriction in addition to `retrieval_len_per_each_user_guided_file`."""

    retrieval_len: int = 0
    """The number of tokens from retrieved chunks to include. -1 means no limit."""

    recent_changes_len: int = 0
    """The number of tokens from recent changes to include. -1 means no limit."""

    inject_current_file_into_retrievals: bool = False
    """Whether to place the current file among retrieved chunks."""

    implicit_external_context_len: int = 4096
    """The number of tokens of the implicit external context to include."""

    explicit_external_context_len: int = 4096
    """The number of tokens of the explicit external context to include."""

    overflow_external_context: bool = False
    """Whether to overflow the external context on top of the existing total budget."""

    tool_results_len: int = 0
    """The number of tokens of tool results that can be included."""

    retrieval_as_tool: bool = False
    """Whether to render retrieval as a tool call.

    If True, render the retrieval section as a tool call. If False, render the retrieval
    section as a user message.
    """

    token_budget_to_trigger_truncation: int = 120_000
    """The number of hitory tokens that will trigger truncation of the prompt."""

    def __post_init__(self):
        assert self.path_len > 0
        assert self.max_prompt_len > 0
        assert (
            self.max_prompt_len
            >= self.path_len
            + self.message_len
            + max(
                self.selected_code_len, self.prefix_len + self.suffix_len
            )  # We use either selected code or prefix/suffix, not both
            + self.chat_history_len
        )


@dataclass_json
@dataclass(frozen=True)
class GenerateCommitMessageTokenApportionment(ChatTokenApportionment):
    """Stores the apportionment of the tokens for commit message generation."""

    explicit_external_context_len: int = 0
    """The number of tokens of the explicit external context to include.

    Not in use for commit message generation so overiding the default value to 0.
    """

    implicit_external_context_len: int = 0
    """The number of tokens of the implicit external context to include.

    Not in use for commit message generation so overiding the default value to 0.
    """

    changed_files_summary_line_threshold: int = 0
    """Threshold for changed line count for inclusion of the changed files summary.

    If the number of changed lines exceeds this threshold, include the changed files
    summary.
    """

    diff_len: int = 0
    """Token budget for current diffs."""

    commit_message_len: int = 0
    """Token budget for commit messages.

    Commit messages include:
    - Example commit messages from the repository to illustrate the commit message
      conventions.
    - Relevant commit messages that are immediately preceding and by the same author, to
      inform the model about the context of the current commit.
    """

    relevant_message_len: int = 0
    """Token sub-budget for relevant messages within commit messages.

    Relevant messages are commit messages that are immediately preceding and by the same
    author, to inform the model about the context of the current commit.
    """

    max_prompt_len: int = 0
    """Total token budget for the prompt."""

    def __post_init__(self):
        assert self.max_prompt_len > 0
        assert self.max_prompt_len >= self.commit_message_len + self.diff_len
        assert self.commit_message_len >= self.relevant_message_len

        assert self.path_len == 0
        assert self.message_len == 0
        assert self.chat_history_len == 0
        assert self.prefix_len == 0
        assert self.selected_code_len == 0
        assert self.suffix_len == 0
        assert self.retrieval_len_per_each_user_guided_file == 0
        assert self.retrieval_len_for_user_guided == 0
        assert self.retrieval_len == 0
        assert self.recent_changes_len == 0
        assert not self.inject_current_file_into_retrievals
        assert self.implicit_external_context_len == 0
        assert self.explicit_external_context_len == 0
        assert not self.overflow_external_context


@dataclass_json
@dataclass(frozen=True)
class SlackbotMessageTokenApportionment(ChatTokenApportionment):
    """Stores the apportionment of the tokens for a slackbot chat request."""

    max_glean_len: int = 0
    """The number of tokens total from Glean documents to include."""

    max_glean_doc_len: int = 0
    """The number of tokens from each Glean document to include."""

    max_glean_leftover_len: int = 0
    """The number of tokens from Glean documents that are not the top ranked to include."""

    max_glean_snippet_char_len: int = 0
    """The number of characters from each Glean document to include in leftover section."""

    explicit_external_context_len: int = 0
    """The number of tokens of the explicit external context to include.
    Not in use for the slack prompt so overiding the default value to 0.
    """

    implicit_external_context_len: int = 0
    """The number of tokens of the implicit external context to include.
    Not in use for the slack prompt so overiding the default value to 0.
    """

    def __post_init__(self):
        assert self.max_prompt_len > 0
        assert self.max_prompt_len > self.max_glean_len
        assert self.max_glean_len >= self.max_glean_doc_len

        # none of these should be used for slackbot
        assert self.message_len == 0
        assert self.prefix_len == 0
        assert self.selected_code_len == 0
        assert self.suffix_len == 0
        assert self.retrieval_len_per_each_user_guided_file == 0
        assert self.retrieval_len_for_user_guided == 0
        assert self.recent_changes_len == 0
        assert not self.inject_current_file_into_retrievals
        assert not self.overflow_external_context


@dataclass(frozen=True)
class PerFileChangeStats:
    """Stats of a changed file for a repository diff."""

    path: str
    """Path of the file."""

    insertion_count: int
    """Number of insertions in the file."""

    deletion_count: int
    """Number of deletions in the file."""

    old_path: str
    """Path of the old file, if the file is renamed or copied."""

    def __str__(self):
        string = f"+{self.insertion_count} -{self.deletion_count} {self.path}"
        if self.old_path:
            string += f" -> {self.old_path}"
        return string


@dataclass(frozen=True)
class PerTypeChangedFileStats:
    """Stats of changed files of a certain type for a repository diff."""

    changed_file_count: int = 0
    """Number of changed files of this type."""

    per_file_change_stats_head: list[PerFileChangeStats] = field(default_factory=list)
    """Stats for the first changed files of this type.

    It is necessary to split into head and tail to reduce frontend delay in getting the
    stats for large diffs. Showing the model head and tail helps the model understand
    the overall change a bit better.
    """

    per_file_change_stats_tail: list[PerFileChangeStats] = field(default_factory=list)
    """Stats for the last changed files of this type.

    It is necessary to split into head and tail to reduce frontend delay in getting the
    stats for large diffs. Showing the model head and tail helps the model understand
    the overall change a bit better.
    """

    def __post_init__(self):
        assert self.changed_file_count >= len(self.per_file_change_stats_head) + len(
            self.per_file_change_stats_tail
        )

    def __str__(self):
        string = f"{self.changed_file_count} files"
        for per_file_change_stats in self.per_file_change_stats_head:
            string += f"\n    {str(per_file_change_stats)}"
        if self.per_file_change_stats_tail:
            string += "\n    ..."
            for per_file_change_stats in self.per_file_change_stats_tail:
                string += f"\n    {str(per_file_change_stats)}"
        return string


@dataclass(frozen=True)
class ChangedFileStats:
    """Stats of changed files for a repository diff."""

    added_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of added files."""

    broken_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of broken files."""

    copied_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of copied files."""

    deleted_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of deleted files."""

    modified_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of modified files."""

    renamed_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of renamed files."""

    unmerged_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of unmerged files."""

    unknown_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of unknown files."""

    def __str__(self):
        string = "ChangedFileStats:"
        for change_type, stats in [
            ("A", self.added_file_stats),
            ("C", self.copied_file_stats),
            ("D", self.deleted_file_stats),
            ("M", self.modified_file_stats),
            ("R", self.renamed_file_stats),
            ("U", self.unmerged_file_stats),
            ("X", self.unknown_file_stats),
            ("B", self.broken_file_stats),
        ]:
            if stats.changed_file_count == 0:
                continue
            string += f"\n    {change_type}: {stats}"
        return string


@dataclass(frozen=True)
class ChatPromptInput:
    """The set of inputs used for constructing the code chat model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.

    `prefix_begin`, `suffix_end` and `file_path` are required to filter overlapping retrieval chunks.
    """

    message: RequestMessage
    """The user's message about how to chat the selected code based on context."""

    path: str
    """The file path."""

    prefix: str
    """The content before the selection."""

    selected_code: str
    """The content that the user selected."""

    suffix: str
    """The content after the selection, where its end location is at the cursor."""

    chat_history: typing.Iterable[Exchange]
    """The conversation history as a list of request_message/response_text pairs."""

    prefix_begin: int
    """The offset in UTF-8 characters where prefix begins, relative to the beginning of file."""

    suffix_end: int
    """The offset in UTF-8 characters where suffix ends, relative to the beginning of file."""

    retrieved_chunks: typing.Iterable[PromptChunk]
    """The retrieved chunks, sorted from high to low relevancy."""

    context_code_exchange_request_id: typing.Optional[str] = None
    """Request ID if the exchange to which the context code should be added.
        "new" if context code should be added to current user message.
    """

    recent_changes: Optional[Sequence[Changed[File]]] = None
    """All changes since the last commit/PR, sorted from oldest to newest."""

    user_guided_blobs: list[str] = field(default_factory=list)
    """The blob names to focus retrieval on provided by the user."""

    external_source_ids: list[str] = field(default_factory=list)
    """The external source ids to focus retrieval on provided by the user."""

    changed_file_stats: Optional[ChangedFileStats] = None
    """Stats of changed files for the repository diff."""

    diff: Optional[str] = None
    """Current diff of the repository.

    It might be working directory, staging, or amend diff.
    """

    """Prompt inputs specific to commit message generation."""

    relevant_commit_messages: list[str] = field(default_factory=list)
    """Relevant commit messages that inform the model about current commit's context."""

    example_commit_messages: list[str] = field(default_factory=list)
    """Commit messages in the repository illustrating the commit message conventions."""

    workspace_guidelines: str | None = None
    """Additional system prompt specified by the workspace."""

    user_guidelines: Optional[str] = None
    """Additional system prompt specified by the user."""

    memories: Optional[str] = None
    """Agent memories to be included in the prompt."""

    tasklist: Optional[str] = None
    """Agent task list to be included in the prompt."""

    tool_definitions: list[ToolDefinition] | None = None
    """Tool definitions available during this chat turn."""

    persona_type: PersonaType = PersonaType.DEFAULT
    """The persona that the AI assistant should adopt for this chat turn."""

    rules: list[Rule] = field(default_factory=list)

    def __repr__(self):
        attributes = ",\n  ".join(
            f"{key} = {value!r}" for key, value in vars(self).items()
        )
        return f"{self.__class__.__name__}(\n  {attributes}\n)"


@dataclass(frozen=True)
class TokenizedChatPromptOutput:
    """The set of outputs used for constructing the code chat model prompts."""

    tokens: TokenList
    """The tokenized prompt."""

    # TODO(markus): We should avoid having fields like this one. The purpose of this output is
    # to log something to request insight. Instead we should pass a callback into the prompt formatter
    # in its constructor, and call it with the retrieved chunks.
    retrieved_chunks_in_prompt: typing.Iterable[PromptChunk]
    """The retrieved chunks that are finally used in the prompt (e.g., after the potential filtering logic)."""

    def workspace_file_chunks(self) -> list[PromptChunk]:
        """Get the chunks that come from files in the workspace."""
        # Exclude chunks from docsets
        return [
            chunk
            for chunk in self.retrieved_chunks_in_prompt
            if chunk.documentation_metadata is None
        ]


@dataclass(frozen=True)
class StructuredChatPromptOutput:
    """The set of outputs used for constructing the code chat model prompts."""

    system_prompt: str | None
    """The system prompt."""

    chat_history: typing.Iterable[Exchange]
    """The truncated conversation history as a list of request_message/response_text pairs."""

    message: RequestMessage
    """The user's message about how to chat the selected code based on context."""

    # TODO(markus): We should avoid having fields like this one. The purpose of this output is
    # to log something to request insight. Instead we should pass a callback into the prompt formatter
    # in its constructor, and call it with the retrieved chunks.
    retrieved_chunks_in_prompt: typing.Iterable[PromptChunk]
    """The retrieved chunks that are finally used in the prompt (e.g., after the potential filtering logic)."""

    retrieval_as_tool: bool = False
    """Whether the retrieved chunks should be rendered as a tool call.

    If True, the retrieved chunks are rendered as a tool call. If False, the retrieved
    chunks are rendered as a user message.
    """

    tool_definitions: list[ToolDefinition] = field(default_factory=list)

    tools: Sequence[str] | None = None

    prefill: str | None = None
    """Text to "prefill" model response. So model is continuing generation from it."""

    def workspace_file_chunks(self) -> list[PromptChunk]:
        """Get the chunks that come from files in the workspace."""
        # Exclude chunks from docsets
        return [
            chunk
            for chunk in self.retrieved_chunks_in_prompt
            if chunk.documentation_metadata is None
        ]


T = typing.TypeVar(
    "T", StructuredChatPromptOutput, TokenizedChatPromptOutput, covariant=True
)


class ChatPromptFormatter(typing.Protocol[T]):
    """The ChatPromptFormatter protocol for the code chat model."""

    token_apportionment: ChatTokenApportionment

    def format_prompt(self, prompt_input: ChatPromptInput) -> T:
        """Build tokenized prompt.

        Args:
            prompt_input: ChatPromptInput object describing input (prefix, selected_code, suffix, message, path)

        Returns:
            prompt
        """
        raise NotImplementedError()


StructuredChatPromptFormatter = ChatPromptFormatter[StructuredChatPromptOutput]
TokenizedChatPromptFormatter = ChatPromptFormatter[TokenizedChatPromptOutput]


# TODO(arun): We should refactor this class out into a common file as it is intended to
# be task-agnostic.
class StructToTokensPromptFormatter(typing.Protocol):
    """The protocol to convert structured prompts to tokens.

    This formatter is intended to be task-agnostic and responsible for converting
    structured prompts into tokenized prompts.

    See `StructuredChatPromptFormatter` for a prompt formatter that renders chat prompts
    into a StructuredChatPromptOutput.
    """

    tokenizer: Tokenizer
    """The tokenizer used by the prompt formatter."""

    def format_prompt(
        self, prompt_input: StructuredChatPromptOutput
    ) -> TokenizedChatPromptOutput:
        """Build tokenized prompt.

        Args:
            prompt_input: StructuredChatPromptInput object describing input
              (system prompt, messages, etc.)

        Returns:
            Tokenized prompt.
        """
        raise NotImplementedError()


def filter_overlapping_retrieved_chunks(
    prompt_input: ChatPromptInput,
    tokenizer: Tokenizer,
    prefix_tokens: TokenList,
    suffix_tokens: TokenList,
    retrieved_chunks: Iterable[PromptChunk],
) -> Iterable[PromptChunk]:
    """Takes an array of PromptChunk and returns only ones that do not overlap with text used in prompt formatting.

    This method is supposed to be used in `format_prompt` method, right after you computed `prefix_tokens` and
    `suffix_tokens` that will be used in prompt.

    Args:
        prompt_input: Preferably the one that `format_prompt` received
            (i.e. without modifications to `.prefix`, `.suffix`, `prefix_begin` and `suffix_end`).
        tokenizer: The one used in `format_prompt`.
        prefix_tokens: Full list of prefix tokens that directly go to prompt.
        suffix_tokens: Same as prefix_tokens, but for suffix.
        retrieved_chunks: Array of PromptChunk to be filtered.
            PromptChunk.char_start and PromptChunk.char_end have to be correctly set.

    Returns:
        Iterable of filtered PromptChunk.
    """

    unused_prefix_len = len(prompt_input.prefix) - len(
        tokenizer.detokenize(prefix_tokens)
    )
    unused_suffix_len = len(prompt_input.suffix) - len(
        tokenizer.detokenize(suffix_tokens)
    )

    effective_prefix_begin = prompt_input.prefix_begin + unused_prefix_len
    effective_suffix_end = prompt_input.suffix_end - unused_suffix_len

    for chunk in retrieved_chunks:
        assert (
            0 <= chunk.char_start <= chunk.char_end
        ), "Chunk is expected to have valid char_start and char_end."
        is_different_file = chunk.path != prompt_input.path
        is_ranges_non_intersect = min(chunk.char_end, effective_suffix_end) <= max(
            chunk.char_start, effective_prefix_begin
        )
        if is_different_file or is_ranges_non_intersect:
            yield chunk


def filter_overlapping_retrieved_chunks_v2(
    prompt_input: ChatPromptInput,
    clipped_prefix: str,
    clipped_suffix: str,
    retrieved_chunks: Iterable[PromptChunk],
) -> Iterable[PromptChunk]:
    """Takes an array of PromptChunk and returns only ones that do not overlap with text used in prompt formatting.

    This util is the same as filter_overlapping_retrieved_chunks but it accepts char-based input instead of
    token-based input.

    This method is supposed to be used in `format_prompt` method, right after you computed `prefix_tokens` and
    `suffix_tokens` that will be used in prompt.

    Args:
        prompt_input: Preferably the one that `format_prompt` received
            (i.e. without modifications to `.prefix`, `.suffix`, `prefix_begin` and `suffix_end`).
        clipped_prefix: Full prefix that directly goes to prompt.
        clippeed_suffix: Same as clipped_prefix, but for suffix.
        retrieved_chunks: Array of PromptChunk to be filtered.
            PromptChunk.char_start and PromptChunk.char_end have to be correctly set.

    Returns:
        Iterable of filtered PromptChunk.
    """

    unused_prefix_len = len(prompt_input.prefix) - len(clipped_prefix)
    unused_suffix_len = len(prompt_input.suffix) - len(clipped_suffix)

    effective_prefix_begin = prompt_input.prefix_begin + unused_prefix_len
    effective_suffix_end = prompt_input.suffix_end - unused_suffix_len

    for chunk in retrieved_chunks:
        assert (
            0 <= chunk.char_start <= chunk.char_end
        ), "Chunk is expected to have valid char_start and char_end."
        is_different_file = chunk.path != prompt_input.path
        is_ranges_non_intersect = min(chunk.char_end, effective_suffix_end) <= max(
            chunk.char_start, effective_prefix_begin
        )
        if is_different_file or is_ranges_non_intersect:
            yield chunk
