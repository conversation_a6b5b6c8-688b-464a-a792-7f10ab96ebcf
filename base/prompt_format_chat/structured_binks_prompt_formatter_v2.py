"""The Binks prompt formatter V2 for the code chat."""

import dataclasses
from typing import Callable, Iterable, Optional

from base.prompt_format.common import (
    ChatRequestNodeType,
    Exchange,
    get_request_message_as_text,
)
from base.prompt_format_chat.lib.chat_history_builder import (
    format_chat_history,
    inject_selected_code_into_chat_history,
    postprocess_chat_history_tool_use,
)
from base.prompt_format_chat.lib.retrieval_section_prompt_formatter_v3 import (
    RetrievalSectionBuilder,
    get_binks_retrieval_section_bulder,
)
from base.prompt_format_chat.lib.rules_prompt_builder import build_custom_prompt
from base.prompt_format_chat.lib.selected_code_prompt_formatter_v2 import (
    SelectedCodePromptFormatterV2,
)
from base.prompt_format_chat.lib.string_formatter import StringFormatter
from base.prompt_format_chat.lib.system_prompts import get_binks_system_prompt_formatter
from base.prompt_format_chat.lib.token_counter import <PERSON><PERSON><PERSON>ounter
from base.prompt_format_chat.prompt_formatter import (
    Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ChatPromptInput,
    ChatTokenApportionment,
    ExceedContextLength,
    StructuredChatPromptOutput,
)

NEAR_INFINITY = 1_000_000_000

_default_token_apportionment_v2 = ChatTokenApportionment(
    path_len=256,
    prefix_len=2000,
    suffix_len=2000,
    chat_history_len=8000,
    retrieval_len=8000,
    retrieval_len_per_each_user_guided_file=6000,
    retrieval_len_for_user_guided=12000,
    tool_results_len=32000,
    max_prompt_len=180000,
    message_len=-1,
    selected_code_len=-1,
    retrieval_as_tool=False,
    inject_current_file_into_retrievals=False,
    token_budget_to_trigger_truncation=180000,
)


def split_short_term_chat_history(
    chat_history: Iterable[Exchange],
) -> tuple[list[Exchange], list[Exchange]]:
    """Split chat history into short-term and earlier parts.

    Short-term history includes the most recent exchange and any tool result exchanges
    that follow it, while earlier history includes older exchanges.

    Args:
        chat_history: The full chat history to split

    Returns:
        A tuple of (short_term_history, earlier_history)
    """
    short_term_history = []  # Currently just the previous round
    earlier_history = list(chat_history)

    for exchange in reversed(earlier_history):
        short_term_history.insert(0, exchange)
        if isinstance(exchange.request_message, str):
            is_user_message = True
        else:
            is_user_message = any(
                getattr(node, "node_type", None) != ChatRequestNodeType.TOOL_RESULT
                for node in exchange.request_message
            )
        if is_user_message:
            break
    earlier_history = earlier_history[: -len(short_term_history)]

    return earlier_history, short_term_history


class StructuredBinksPromptFormatterV2(ChatPromptFormatter[StructuredChatPromptOutput]):
    """The class formats prompts for the Binks chat model with V2 token allocation.

    This formatter implements the new design with individual component limits rather than
    a shared soft cap, and focuses on avoiding rejections due to long inputs.

    Prompt Structure:
    - System Prompt
    - Chat History (with fixed budget)
    - Retrieval (with fixed budget)
    - Current message and selection (uncapped, but with hard limit)

    Token Budget Allocation Policy:
    - Fixed budgets for system prompt, prefix/suffix, chat history, and retrieval
    - Uncapped components (current message, selection) fill the remainder up to the hard limit
    - If the total exceeds the hard limit, the current message is truncated
    """

    def __init__(
        self,
        token_counter: TokenCounter,
        system_prompt: StringFormatter,
        retrieval_section_builder: RetrievalSectionBuilder,
        selected_code_formatter: SelectedCodePromptFormatterV2,
        token_apportionment: Optional[ChatTokenApportionment] = None,
    ):
        self.token_counter = token_counter

        if token_apportionment is None:
            token_apportionment = _default_token_apportionment_v2

        assert (
            token_apportionment.message_len == -1
        ), "The message length should be -1 because it is a deprecated field not used in this formatter."
        assert (
            token_apportionment.selected_code_len == -1
        ), "The selected code length should be -1 because it is a deprecated field not used in this formatter."

        self.token_apportionment = token_apportionment
        self.selected_code_formatter = selected_code_formatter
        self.system_prompt_formatter = system_prompt
        self.retrieval_section_builder = retrieval_section_builder

    def format_prompt(
        self, prompt_input: ChatPromptInput
    ) -> StructuredChatPromptOutput:
        """Format prompt for Binks Llama3-based code chat model with V2 token allocation.

        Key differences from V1:
        - Fixed budgets for components instead of shared soft cap
        - Uncapped current message and selection
        - Hard limit on total prompt length
        - Immediate previous exchange is considered part of current message

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.token_apportionment.max_prompt_len, in tokens.
        """

        system_prompt = self.system_prompt_formatter.format({})
        guideline_prompt = build_custom_prompt(
            prompt_input.user_guidelines,
            prompt_input.workspace_guidelines,
            rules=prompt_input.rules if hasattr(prompt_input, "rules") else None,
        )
        system_prompt += guideline_prompt
        system_prompt_tokens = self.token_counter.count_tokens(system_prompt)

        current_message = prompt_input.message
        current_message_token_count = self.token_counter.count_tokens_in_request(
            current_message
        )
        cur_message_tool_result_token_count = (
            self.token_counter.count_tokens_in_request(
                current_message, node_type=ChatRequestNodeType.TOOL_RESULT
            )
        )
        cur_message_image_token_count = self.token_counter.count_tokens_in_request(
            current_message, node_type=ChatRequestNodeType.IMAGE
        )
        assert (
            current_message_token_count
            >= cur_message_tool_result_token_count + cur_message_image_token_count
        )

        tool_results_token_budget = (
            self.token_apportionment.tool_results_len
            - cur_message_tool_result_token_count
        )
        if tool_results_token_budget < 0:
            raise ExceedContextLength(
                f"Tool results length {cur_message_tool_result_token_count} exceeds maximum tool results length "
                f"{self.token_apportionment.tool_results_len}."
            )

        chat_history_budget = self.token_apportionment.chat_history_len

        earlier_history, short_term_history = split_short_term_chat_history(
            prompt_input.chat_history
        )
        clipped_earlier_history, _ = format_chat_history(
            chat_history=earlier_history,
            token_counter=self.token_counter,
            token_budget=chat_history_budget,
        )
        clipped_history = clipped_earlier_history + short_term_history
        clipped_history = postprocess_chat_history_tool_use(
            clipped_history,
            current_message,
            self.token_counter,
            tool_results_token_budget,
        )
        clipped_earlier_history, short_term_history = split_short_term_chat_history(
            clipped_history
        )
        short_term_history_token_count = (
            self.token_counter.count_tokens_in_chat_history(short_term_history)
        )

        selected_code_section, clipped_prefix, clipped_suffix = (
            self.selected_code_formatter.format(
                prompt_input,
                NEAR_INFINITY,
            )
        )
        selected_code_token_count = self.token_counter.count_tokens(
            selected_code_section
        )

        uncapped_section_token_count = (
            current_message_token_count
            + selected_code_token_count
            + short_term_history_token_count
        )
        if uncapped_section_token_count > self.token_apportionment.max_prompt_len:
            raise ExceedContextLength(
                f"The combined length ({uncapped_section_token_count}) of"
                f" your current message ({current_message_token_count}),"
                f" previous message ({short_term_history_token_count}),"
                f" and code selection ({selected_code_token_count})"
                f" exceeds the limit of {self.token_apportionment.max_prompt_len}."
            )

        selected_code_is_empty = len(prompt_input.selected_code) == 0
        inject_selected_code_section_into_retrieveds = (
            self.token_apportionment.inject_current_file_into_retrievals
            and selected_code_is_empty
            and len(selected_code_section) > 0
        )
        if not inject_selected_code_section_into_retrieveds:
            if selected_code_is_empty:
                context_code_exchange_request_id = None
            else:
                context_code_exchange_request_id = (
                    prompt_input.context_code_exchange_request_id
                )
            current_message, clipped_history = inject_selected_code_into_chat_history(
                current_message,
                clipped_history,
                selected_code_section,
                context_code_exchange_request_id,
                self.selected_code_formatter.selected_code_response_message,
            )

        retrieval_exchanges, retrieved_chunks_in_prompt = (
            self.retrieval_section_builder.get_retrieval_section_as_exchanges(
                prompt_input,
                clipped_prefix,
                clipped_suffix,
            )
        )
        if inject_selected_code_section_into_retrieveds:
            retrieval_exchanges = self._inject_selected_code_into_retrieval(
                retrieval_exchanges, selected_code_section
            )
        clipped_history = retrieval_exchanges + clipped_history

        current_message_token_count = self.token_counter.count_tokens_in_request(
            current_message
        )

        chat_history_token_count = self.token_counter.count_tokens_in_chat_history(
            clipped_history
        )
        total_tokens = (
            system_prompt_tokens
            + current_message_token_count
            + chat_history_token_count
        )

        if total_tokens > self.token_apportionment.max_prompt_len:
            excess_tokens = total_tokens - self.token_apportionment.max_prompt_len
            raise ExceedContextLength(
                f"The prompt exceeds the maximum length of {self.token_apportionment.max_prompt_len} tokens. "
                f"Total tokens: {total_tokens}. \n"
                f"Current message: {current_message_token_count} tokens\n"
                f"Immediate conversation context: {short_term_history_token_count} tokens\n"
                f"Older chat history: {chat_history_token_count - short_term_history_token_count} tokens\n"
                f"System prompt: {system_prompt_tokens} tokens\n"
                f"The total content is {excess_tokens} tokens too long. "
                f"Please reduce the length of your message or simplify your conversation."
            )

        return StructuredChatPromptOutput(
            system_prompt=system_prompt,
            chat_history=clipped_history,
            message=current_message,
            retrieved_chunks_in_prompt=retrieved_chunks_in_prompt,
            retrieval_as_tool=self.token_apportionment.retrieval_as_tool,
            tool_definitions=prompt_input.tool_definitions or [],
        )

    def _inject_selected_code_into_retrieval(
        self, retrieval_exchanges: list[Exchange], selected_code_section: str
    ) -> list[Exchange]:
        """Inject selected code section into retrieval exchanges.

        This method handles three cases:
        1. No retrieval exchanges: Create a new exchange with the selected code
        2. One retrieval exchange: Append selected code to the existing message
        3. Two retrieval exchanges: Append selected code to the tool result content

        Args:
            retrieval_exchanges: The original retrieval exchanges
            selected_code_section: The selected code to inject

        Returns:
            Updated retrieval exchanges with selected code injected

        Raises:
            ValueError: If there are more than 2 retrieval exchanges
        """
        if len(retrieval_exchanges) == 0:
            return [
                Exchange(
                    request_message=selected_code_section,
                    response_text=self.retrieval_section_builder.retrieval_response_message,
                )
            ]
        elif len(retrieval_exchanges) == 1:
            retrieval_exchange = retrieval_exchanges[0]
            message_text = get_request_message_as_text(
                retrieval_exchange.request_message
            )
            request_message_with_current_file = message_text + selected_code_section
            return [
                dataclasses.replace(
                    retrieval_exchange,
                    request_message=request_message_with_current_file,
                )
            ]
        elif len(retrieval_exchanges) == 2:
            retrieval_call_exchange, retrieval_result_exchange = retrieval_exchanges
            assert isinstance(retrieval_result_exchange.request_message, list)
            assert retrieval_result_exchange.request_message[0].tool_result_node
            message_text = retrieval_result_exchange.request_message[
                0
            ].tool_result_node.content
            request_message_with_current_file = message_text + selected_code_section
            return [
                retrieval_call_exchange,
                dataclasses.replace(
                    retrieval_result_exchange,
                    request_message=[
                        dataclasses.replace(
                            retrieval_result_exchange.request_message[0],
                            tool_result_node=dataclasses.replace(
                                retrieval_result_exchange.request_message[
                                    0
                                ].tool_result_node,
                                content=request_message_with_current_file,
                            ),
                        )
                    ],
                ),
            ]
        else:
            raise ValueError(
                f"There could only be 0, 1, or 2 retrieval exchanges, but got"
                f" {len(retrieval_exchanges)}."
            )

    @classmethod
    def create(
        cls,
        token_counter: TokenCounter,
        token_apportionment: ChatTokenApportionment | None = None,
        system_prompt_factory: Callable[
            [TokenCounter], StringFormatter
        ] = get_binks_system_prompt_formatter,
        retrieval_section_version: int = 2,
    ):
        if token_apportionment is None:
            token_apportionment = _default_token_apportionment_v2
        system_prompt = system_prompt_factory(token_counter)
        retrieval_section_builder = get_binks_retrieval_section_bulder(
            token_counter=token_counter,
            token_apportionment=token_apportionment,
            version=retrieval_section_version,
        )
        selected_code_formatter = SelectedCodePromptFormatterV2(
            token_counter=token_counter,
            max_path_tokens=token_apportionment.path_len,
            max_prefix_tokens=token_apportionment.prefix_len,
            max_suffix_tokens=token_apportionment.suffix_len,
            version=retrieval_section_version,
        )
        return StructuredBinksPromptFormatterV2(
            token_counter=token_counter,
            system_prompt=system_prompt,
            retrieval_section_builder=retrieval_section_builder,
            selected_code_formatter=selected_code_formatter,
            token_apportionment=token_apportionment,
        )
