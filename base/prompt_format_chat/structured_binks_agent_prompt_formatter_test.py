import dataclasses
import datetime
from typing import Any
from unittest.mock import Mock

import jinja2
import pytest

import base.feature_flags
from base import tokenizers
from base.prompt_format.common import (
    ChatRequestEditEvents,
    ChatRequestFileEdit,
    ChatRequestIdeState,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestSingleEdit,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    EditEventSource,
    Exchange,
    TerminalInfo,
    ToolDefinition,
    WorkspaceFolderInfo,
)
from base.prompt_format_chat.lib.string_formatter import <PERSON><PERSON><PERSON><PERSON>atter
from base.prompt_format_chat.lib.system_prompts import (
    get_agent_system_prompt_formatter_v6,
)
from base.prompt_format_chat.lib.token_counter import (
    TokenCounter,
    TokenizerBasedTokenCounter,
)
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from base.prompt_format_chat.prompt_formatter import (
    Chat<PERSON>romptInput,
    Chat<PERSON><PERSON>Apportionment,
    ExceedContextLength,
)
from base.prompt_format_chat.structured_binks_agent_prompt_formatter import (
    _ADD_DATE_TO_SYSTEM_PROMPT,
    _ADD_SUPERVISOR_PROMPT_EVERY_TURN,
    _ADD_SUPERVISOR_PROMPT_TO_PREFILL_EVERY_TURN,
    _ADD_SYSTEM_PROMPT_TO_PREFILL_EVERY_TURN,
    _EDIT_EVENTS_VERSION,
    _USE_EDIT_EVENTS_IN_PROMPT,
    _USE_IDE_STATE_IN_PROMPT,
    StructuredBinksAgentPromptFormatter,
)
from base.third_party_clients.mock_anthropic_client import MockAnthropicClient

"""
Capabilities I want to test:

- We format memories into the prompt as we would expect
- New truncation logic works as we would expect, logically (format_agent_history correctly drops history, messages, tool calls as needed)

"""


# TODO(Devang): Move this to conftest.py
def test_structured_binks_agent_prompt_formatter():
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
    )
    prompt_input = ChatPromptInput(
        message="What is the meaning of life?",
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )
    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt is not None
    assert "I like to eat pizza." in prompt.system_prompt


def test_structured_binks_agent_prompt_formatter_single_turn_tool_call():
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
    )
    chat_history = [
        Exchange(
            request_message="What is the meaning of life?",
            response_text=[
                ChatResultNode(
                    id=0,
                    type=ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=ChatResultToolUse(
                        name="ask_for_codebase_snippets",
                        input={"description": "meaning of life"},
                        tool_use_id="toolu_123",
                    ),
                ),
                ChatResultNode(
                    id=1,
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content="Searching the codebase for the meaning of life.",
                    tool_use=None,
                ),
            ],
            request_id="request_id1",
        )
    ]

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="toolu_123",
                    content="The meaning of life is 42.",
                    is_error=False,
                ),
            )
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=chat_history,
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )
    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt is not None
    assert prompt.chat_history == chat_history


def test_structured_binks_agent_prompt_formatter_system_prompt_inputs():
    # System prompt AgentSystemPromptFormatters expect a dict of values to format
    # The shared prompt formatter must make all those values available
    expected_keys = set(
        [
            "model_name",
            "creator",
            "memories",
            "formatted_custom_guidelines",
            "tasklist",
            "tools",
            "current_date",
        ]
    )
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    mock_system_prompt = Mock()
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        system_prompt_factory=lambda token_counter: mock_system_prompt,
    )
    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="toolu_123",
                    content="The meaning of life is 42.",
                    is_error=False,
                ),
            )
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="The build system is bazel",
        user_guidelines="Always use camelCase for variable names.",
        workspace_guidelines="Always use snake_case for variable names.",
        tool_definitions=[
            ToolDefinition(
                name="test-tool",
                description="A test tool",
                input_schema_json="{}",
            )
        ],
    )
    mock_system_prompt.format.return_value = "system prompt"

    prompter.format_prompt(prompt_input)

    # Verify that the system prompt formatter was called with all required fields
    assert mock_system_prompt.format.call_count == 1
    inputs = mock_system_prompt.format.call_args[0][0]
    assert set(inputs.keys()) == expected_keys
    assert inputs["model_name"] == "Claude 3.7 Sonnet"
    assert inputs["creator"] == "Anthropic"
    assert inputs["memories"] == prompt_input.memories
    assert inputs["tools"] == ["test-tool"]
    assert prompt_input.user_guidelines in inputs["formatted_custom_guidelines"]
    assert prompt_input.workspace_guidelines in inputs["formatted_custom_guidelines"]


SIMPLE_IDE_STATE_PROMPT = """\
{% if first_message %}
First message.
{% if launch_process_tool_name %}
{{launch_process_tool_name}}
{% endif %}
{% if repo_relative_tools %}
{{repo_relative_tools | join(', ')}}
{% endif %}
{% endif %}
{% if workspace_folders_changed %}
I've changed directories and am currently working out of the `{{workspace_folders[0].folder_root}}` directory.
My project is currently rooted at `{{workspace_folders[0].repository_root}}`.
{% endif %}
{% if current_terminal_changed %}
My terminal's current working directory has changed and is now `{{current_terminal.current_working_directory}}`.
{% endif %}
"""


@pytest.fixture
def feature_flags():
    yield from base.feature_flags.feature_flag_fixture()


def test_structured_binks_agent_prompt_formatter_ide_state_when_enabled(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        ide_state_prompt_factory=lambda token_counter: JinjaFormatter(
            jinja2.Template(
                SIMPLE_IDE_STATE_PROMPT, lstrip_blocks=True, trim_blocks=True
            ),
            token_counter=token_counter,
        ),
    )
    feature_flags.set_flag(_USE_IDE_STATE_IN_PROMPT, True)

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(
                    content="Uh yes, please. What is the question of life?",
                ),
                tool_result_node=None,
            ),
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.IDE_STATE,
                text_node=None,
                tool_result_node=None,
                ide_state_node=ChatRequestIdeState(
                    workspace_folders=[
                        WorkspaceFolderInfo(
                            folder_root="/home/<USER>/repo/folder",
                            repository_root="/home/<USER>/repo",
                        )
                    ],
                    workspace_folders_unchanged=True,
                    current_terminal=TerminalInfo(
                        terminal_id=0,
                        current_working_directory="/home/<USER>/repo/folder/subfolder",
                    ),
                ),
            ),
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(
                            content="What is the answer to the ultimate question of life, universe and everything?",
                        ),
                        tool_result_node=None,
                    ),
                    ChatRequestNode(
                        id=2,
                        type=ChatRequestNodeType.IDE_STATE,
                        text_node=None,
                        tool_result_node=None,
                        ide_state_node=ChatRequestIdeState(
                            workspace_folders=[
                                WorkspaceFolderInfo(
                                    folder_root="/home/<USER>/repo/folder",
                                    repository_root="/home/<USER>/repo",
                                )
                            ],
                            workspace_folders_unchanged=False,
                            current_terminal=TerminalInfo(
                                terminal_id=0,
                                current_working_directory="/home/<USER>/repo/folder",
                            ),
                        ),
                    ),
                ],
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="42.",
                        tool_use=None,
                    ),
                ],
                request_id="request_id1",
            ),
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(
                            content="What? Why is it 42?",
                        ),
                        tool_result_node=None,
                    ),
                    # Unchanged IDE state!
                    ChatRequestNode(
                        id=2,
                        type=ChatRequestNodeType.IDE_STATE,
                        text_node=None,
                        tool_result_node=None,
                        ide_state_node=ChatRequestIdeState(
                            # Because unchanged is true, this information is optional, but
                            # we want to test that it is correctly handled.
                            workspace_folders=[
                                WorkspaceFolderInfo(
                                    folder_root="/home/<USER>/repo/folder",
                                    repository_root="/home/<USER>/repo",
                                )
                            ],
                            workspace_folders_unchanged=False,
                            current_terminal=TerminalInfo(
                                terminal_id=0,
                                current_working_directory="/home/<USER>/repo/folder",
                            ),
                        ),
                    ),
                ],
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="It is the answer. Do you want to know what the question is?",
                        tool_use=None,
                    ),
                ],
                request_id="request_id2",
            ),
        ],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
        # Listing some default tools
        tool_definitions=[
            ToolDefinition(
                name="launch-process",
                description="",
                input_schema_json="",
            ),
            ToolDefinition(
                name="codebase-retrieval",
                description="",
                input_schema_json="",
            ),
        ],
    )
    # Representing these nodes as dicts for easier comparison.
    input_history_messages = [
        {
            "request_nodes": list(exchange.request_nodes),
            "response_nodes": list(exchange.response_nodes),
        }
        for exchange in prompt_input.chat_history
    ]

    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt is not None
    assert prompt.message == [
        ChatRequestNode(
            id=2,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(
                content="""\
My terminal's current working directory has changed and is now `/home/<USER>/repo/folder/subfolder`.
"""
            ),
            tool_result_node=None,
        ),
        prompt_input.message[0],
    ]
    assert prompt.chat_history == [
        Exchange(
            request_message=[
                # The IdeState node is rendered as text.
                ChatRequestNode(
                    id=2,
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(
                        content="""\
First message.
launch-process
codebase-retrieval
I've changed directories and am currently working out of the `/home/<USER>/repo/folder` directory.
My project is currently rooted at `/home/<USER>/repo`.
My terminal's current working directory has changed and is now `/home/<USER>/repo/folder`.
"""
                    ),
                    tool_result_node=None,
                ),
                input_history_messages[0]["request_nodes"][0],
            ],
            response_text=input_history_messages[0]["response_nodes"],
            request_id="request_id1",
        ),
        Exchange(
            request_message=[
                input_history_messages[1]["request_nodes"][0],
                # We don't need to render another node for IDE state.
            ],
            response_text=input_history_messages[1]["response_nodes"],
            request_id="request_id2",
        ),
    ]


def test_structured_binks_agent_prompt_formatter_ide_state_when_disabled(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        ide_state_prompt_factory=lambda token_counter: JinjaFormatter(
            jinja2.Template(
                SIMPLE_IDE_STATE_PROMPT, lstrip_blocks=True, trim_blocks=True
            ),
            token_counter=token_counter,
        ),
    )
    feature_flags.set_flag(_USE_IDE_STATE_IN_PROMPT, False)

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(
                    content="Uh yes, please. What is the question of life?",
                ),
                tool_result_node=None,
            ),
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.IDE_STATE,
                text_node=None,
                tool_result_node=None,
                ide_state_node=ChatRequestIdeState(
                    workspace_folders=[
                        WorkspaceFolderInfo(
                            folder_root="/home/<USER>/repo/folder",
                            repository_root="/home/<USER>/repo",
                        )
                    ],
                    workspace_folders_unchanged=True,
                    current_terminal=TerminalInfo(
                        terminal_id=0,
                        current_working_directory="/home/<USER>/repo/folder/subfolder",
                    ),
                ),
            ),
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(
                            content="What is the answer to the ultimate question of life, universe and everything?",
                        ),
                        tool_result_node=None,
                    ),
                    ChatRequestNode(
                        id=2,
                        type=ChatRequestNodeType.IDE_STATE,
                        text_node=None,
                        tool_result_node=None,
                        ide_state_node=ChatRequestIdeState(
                            workspace_folders=[
                                WorkspaceFolderInfo(
                                    folder_root="/home/<USER>/repo/folder",
                                    repository_root="/home/<USER>/repo",
                                )
                            ],
                            workspace_folders_unchanged=False,
                            current_terminal=TerminalInfo(
                                terminal_id=1,
                                current_working_directory="/home/<USER>/repo/folder",
                            ),
                        ),
                    ),
                ],
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="42.",
                        tool_use=None,
                    ),
                ],
                request_id="request_id1",
            ),
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(
                            content="What? Why is it 42?",
                        ),
                        tool_result_node=None,
                    ),
                    # Unchanged IDE state!
                    ChatRequestNode(
                        id=2,
                        type=ChatRequestNodeType.IDE_STATE,
                        text_node=None,
                        tool_result_node=None,
                        ide_state_node=ChatRequestIdeState(
                            # Because unchanged is true, this information is optional, but
                            # we want to test that it is correctly handled.
                            workspace_folders=[
                                WorkspaceFolderInfo(
                                    folder_root="/home/<USER>/repo/folder",
                                    repository_root="/home/<USER>/repo",
                                )
                            ],
                            workspace_folders_unchanged=True,
                            current_terminal=TerminalInfo(
                                terminal_id=2,
                                current_working_directory="/home/<USER>/repo/folder",
                            ),
                        ),
                    ),
                ],
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="It is the answer. Do you want to know what the question is?",
                        tool_use=None,
                    ),
                ],
                request_id="request_id2",
            ),
        ],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
        # Listing some default tools
        tool_definitions=[
            ToolDefinition(
                name="launch-process",
                description="",
                input_schema_json="",
            ),
            ToolDefinition(
                name="codebase-retrieval",
                description="",
                input_schema_json="",
            ),
        ],
    )
    # Representing these nodes as dicts for easier comparison.
    input_history_messages = [
        {
            "request_nodes": list(exchange.request_nodes),
            "response_nodes": list(exchange.response_nodes),
        }
        for exchange in prompt_input.chat_history
    ]

    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt is not None
    assert prompt.message == [
        prompt_input.message[0],
    ]
    assert prompt.chat_history == [
        Exchange(
            request_message=[
                input_history_messages[0]["request_nodes"][0],
            ],
            response_text=input_history_messages[0]["response_nodes"],
            request_id="request_id1",
        ),
        Exchange(
            request_message=[
                input_history_messages[1]["request_nodes"][0],
            ],
            response_text=input_history_messages[1]["response_nodes"],
            request_id="request_id2",
        ),
    ]


def test_structured_binks_agent_prompt_formatter_ide_state_doesnt_break_tool_use(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        ide_state_prompt_factory=lambda token_counter: JinjaFormatter(
            jinja2.Template(
                SIMPLE_IDE_STATE_PROMPT, lstrip_blocks=True, trim_blocks=True
            ),
            token_counter=token_counter,
        ),
    )
    feature_flags.set_flag(_USE_IDE_STATE_IN_PROMPT, True)

    ide_state_node = ChatRequestIdeState(
        workspace_folders=[
            WorkspaceFolderInfo(
                folder_root="/home/<USER>/repo/folder",
                repository_root="/home/<USER>/repo",
            )
        ],
        workspace_folders_unchanged=True,
        current_terminal=TerminalInfo(
            terminal_id=0,
            current_working_directory="/home/<USER>/repo/folder",
        ),
    )

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="tool_use_1",
                    content="ls output",
                    is_error=False,
                ),
            ),
            ChatRequestNode(
                id=2,
                type=ChatRequestNodeType.IDE_STATE,
                text_node=None,
                tool_result_node=None,
                ide_state_node=dataclasses.replace(
                    ide_state_node,
                    current_terminal=TerminalInfo(
                        terminal_id=0,
                        current_working_directory="/home/<USER>/repo/folder/foo",
                    ),
                ),
            ),
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(
                            content="run cd foo && ls",
                        ),
                        tool_result_node=None,
                    ),
                    ChatRequestNode(
                        id=2,
                        type=ChatRequestNodeType.IDE_STATE,
                        text_node=None,
                        tool_result_node=None,
                        ide_state_node=ide_state_node,
                    ),
                ],
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="42.",
                        tool_use=None,
                    ),
                    ChatResultNode(
                        id=2,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="launch-process",
                            input={"command": "cd foo && ls"},
                            tool_use_id="tool_use_1",
                        ),
                    ),
                ],
                request_id="request_id1",
            )
        ],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        # Listing some default tools
        tool_definitions=[
            ToolDefinition(
                name="launch-process",
                description="",
                input_schema_json="",
            ),
            ToolDefinition(
                name="codebase-retrieval",
                description="",
                input_schema_json="",
            ),
        ],
    )
    # Representing these nodes as dicts for easier comparison.
    input_history_messages = [
        {
            "request_nodes": list(exchange.request_nodes),
            "response_nodes": list(exchange.response_nodes),
        }
        for exchange in prompt_input.chat_history
    ]

    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt is not None
    assert prompt.message == [
        # We shouldn't appear before tool results!
        prompt_input.message[0],
        ChatRequestNode(
            id=2,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(
                content="""\
My terminal's current working directory has changed and is now `/home/<USER>/repo/folder/foo`.\
"""
            ),
            tool_result_node=None,
        ),
    ]
    assert prompt.chat_history == [
        Exchange(
            request_message=[
                # The IdeState node is rendered as text.
                ChatRequestNode(
                    id=2,
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(
                        content="""\
First message.
launch-process
codebase-retrieval
I've changed directories and am currently working out of the `/home/<USER>/repo/folder` directory.
My project is currently rooted at `/home/<USER>/repo`.
My terminal's current working directory has changed and is now `/home/<USER>/repo/folder`.
"""
                    ),
                    tool_result_node=None,
                ),
                input_history_messages[0]["request_nodes"][0],
            ],
            response_text=input_history_messages[0]["response_nodes"],
            request_id="request_id1",
        ),
    ]


def test_structured_binks_agent_prompt_formatter_supervisor_prompt_every_turn(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    supervisor_prompt = "<supervisor>This is a supervisor prompt</supervisor>"
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        supervisor_prompt_on_every_turn=supervisor_prompt,
    )
    feature_flags.set_flag(_ADD_SUPERVISOR_PROMPT_EVERY_TURN, True)

    prompt_input = ChatPromptInput(
        message="What is the meaning of life?",
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message="What is the answer to the ultimate question of life, universe and everything?",
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="42.",
                        tool_use=None,
                    )
                ],
                request_id="request_id1",
            ),
        ],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )

    prompt = prompter.format_prompt(prompt_input)

    # Check that supervisor prompt is injected into the current message
    assert isinstance(prompt.message, list)
    assert len(prompt.message) == 2
    assert prompt.message[0].type == ChatRequestNodeType.TEXT
    assert prompt.message[0].text_node is not None
    assert supervisor_prompt in prompt.message[0].text_node.content

    # Check that supervisor prompt is NOT injected into chat history
    chat_history_list = list(prompt.chat_history)
    assert len(chat_history_list) == 1
    history_request = chat_history_list[0].request_message
    # The request_message can be a string or a list depending on the implementation
    if isinstance(history_request, list):
        assert len(history_request) == 1
        assert history_request[0].type == ChatRequestNodeType.TEXT
        assert history_request[0].text_node is not None
        assert supervisor_prompt not in history_request[0].text_node.content
    else:
        # If it's a string, the supervisor prompt should be prepended
        assert isinstance(history_request, str)
        assert supervisor_prompt not in history_request


def test_structured_binks_agent_prompt_formatter_supervisor_prompt_doesnt_break_tool_use(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    supervisor_prompt = "<supervisor>This is a supervisor prompt</supervisor>"
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        supervisor_prompt_on_every_turn=supervisor_prompt,
    )
    feature_flags.set_flag(_ADD_SUPERVISOR_PROMPT_EVERY_TURN, True)

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="tool_use_1",
                    content="ls output",
                    is_error=False,
                ),
            ),
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(
                            content="run ls",
                        ),
                        tool_result_node=None,
                    ),
                ],
                response_text=[
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="Let me run ls for you.",
                        tool_use=None,
                    ),
                    ChatResultNode(
                        id=2,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="launch-process",
                            input={"command": "ls"},
                            tool_use_id="tool_use_1",
                        ),
                    ),
                ],
                request_id="request_id1",
            )
        ],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        # Listing some default tools
        tool_definitions=[
            ToolDefinition(
                name="launch-process",
                description="",
                input_schema_json="",
            ),
            ToolDefinition(
                name="codebase-retrieval",
                description="",
                input_schema_json="",
            ),
        ],
    )
    # Representing these nodes as dicts for easier comparison.
    input_history_messages = [
        {
            "request_nodes": list(exchange.request_nodes),
            "response_nodes": list(exchange.response_nodes),
        }
        for exchange in prompt_input.chat_history
    ]

    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt is not None

    # We shouldn't appear before tool results!
    assert isinstance(prompt.message, list)
    assert len(prompt.message) == 2
    assert prompt.message[0] == prompt_input.message[0]
    assert prompt.message[1].type == ChatRequestNodeType.TEXT
    assert prompt.message[1].text_node is not None
    assert supervisor_prompt in prompt.message[1].text_node.content

    # Check that supervisor prompt is not injected into chat history
    chat_history_list = list(prompt.chat_history)
    assert len(chat_history_list) == 1
    history_request = chat_history_list[0].request_message
    assert isinstance(history_request, list)
    assert len(history_request) == 1
    assert history_request[0].type == ChatRequestNodeType.TEXT
    assert history_request[0].text_node is not None
    assert supervisor_prompt not in history_request[0].text_node.content

    # Verify response nodes are preserved
    assert (
        chat_history_list[0].response_text
        == input_history_messages[0]["response_nodes"]
    )


def test_structured_binks_agent_prompt_formatter_supervisor_prompt_to_prefill_every_turn(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    supervisor_prompt = "<supervisor>This is a supervisor prompt</supervisor>"
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        supervisor_prompt_on_every_turn=supervisor_prompt,
    )
    feature_flags.set_flag(_ADD_SUPERVISOR_PROMPT_TO_PREFILL_EVERY_TURN, True)
    # Also enable adding supervisor prompt to message for this test
    feature_flags.set_flag(_ADD_SUPERVISOR_PROMPT_EVERY_TURN, True)

    prompt_input = ChatPromptInput(
        message="What is the meaning of life?",
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )

    prompt = prompter.format_prompt(prompt_input)

    # Check that supervisor prompt is added to prefill
    assert prompt.prefill == supervisor_prompt

    # Check that supervisor prompt is also injected into the current message
    assert isinstance(prompt.message, list)
    assert len(prompt.message) == 2
    assert prompt.message[0].type == ChatRequestNodeType.TEXT
    assert prompt.message[0].text_node is not None
    assert supervisor_prompt in prompt.message[0].text_node.content


def test_structured_binks_agent_prompt_formatter_system_prompt_to_prefill_every_turn(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    supervisor_prompt = "<supervisor>This is a supervisor prompt</supervisor>"
    mock_system_prompt = Mock()
    mock_system_prompt.format.return_value = "This is the system prompt"

    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        system_prompt_factory=lambda _: mock_system_prompt,
        supervisor_prompt_on_every_turn=supervisor_prompt,
    )
    feature_flags.set_flag(_ADD_SYSTEM_PROMPT_TO_PREFILL_EVERY_TURN, True)

    prompt_input = ChatPromptInput(
        message="What is the meaning of life?",
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )

    prompt = prompter.format_prompt(prompt_input)

    # Check that system prompt is added to prefill
    expected_prefill = """<supervisor>
SYSTEM PROMPT REMINDER:
This is the system prompt
</supervisor>"""
    assert prompt.prefill == expected_prefill


def test_structured_binks_agent_prompt_formatter_both_prompts_to_prefill_every_turn(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    supervisor_prompt = "<supervisor>This is a supervisor prompt</supervisor>"
    mock_system_prompt = Mock()
    mock_system_prompt.format.return_value = "This is the system prompt"

    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        system_prompt_factory=lambda _: mock_system_prompt,
        supervisor_prompt_on_every_turn=supervisor_prompt,
    )
    feature_flags.set_flag(_ADD_SUPERVISOR_PROMPT_TO_PREFILL_EVERY_TURN, True)
    feature_flags.set_flag(_ADD_SYSTEM_PROMPT_TO_PREFILL_EVERY_TURN, True)

    prompt_input = ChatPromptInput(
        message="What is the meaning of life?",
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )

    prompt = prompter.format_prompt(prompt_input)

    # Check that both prompts are added to prefill
    system_prompt_part = """<supervisor>
SYSTEM PROMPT REMINDER:
This is the system prompt
</supervisor>"""
    assert prompt.prefill is not None
    assert system_prompt_part in prompt.prefill
    assert supervisor_prompt in prompt.prefill


def test_structured_binks_agent_prompt_formatter_prefill_doesnt_break_tool_use(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    supervisor_prompt = "<supervisor>This is a supervisor prompt</supervisor>"
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        supervisor_prompt_on_every_turn=supervisor_prompt,
    )
    feature_flags.set_flag(_ADD_SUPERVISOR_PROMPT_TO_PREFILL_EVERY_TURN, True)
    # Also enable adding supervisor prompt to message for this test
    feature_flags.set_flag(_ADD_SUPERVISOR_PROMPT_EVERY_TURN, True)

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="tool_use_1",
                    content="ls output",
                    is_error=False,
                ),
            ),
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )

    prompt = prompter.format_prompt(prompt_input)

    # Check that prefill is set correctly
    assert prompt.prefill == supervisor_prompt

    # Check that tool result is preserved in the message
    assert isinstance(prompt.message, list)
    assert len(prompt.message) == 2
    assert prompt.message[0] == prompt_input.message[0]
    assert prompt.message[1].type == ChatRequestNodeType.TEXT
    assert prompt.message[1].text_node is not None
    assert supervisor_prompt in prompt.message[1].text_node.content


def test_selected_code_wont_throw_error_for_agent_selected_code_formatter_when_no_fixed_budget():
    """
    Test that when we use the agent selected code prompt formatter, the selected code
    doesn't throw error when it's over budget (selected_code_len # of tokens) if we
    don't have a fixed budget for selected code.
    """
    # Set up a token counter
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    # Set small selected code len to make it easier to test clipping
    selected_code_len = 80
    # Create the structured formatter with our selected code formatter
    token_apportionment = ChatTokenApportionment(
        prefix_len=0,
        suffix_len=0,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=selected_code_len,
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        use_fixed_budget_selected_code=False,
    )
    # Create selected code that will exceed the token budget
    selected_code = "\n".join(
        [f"line {i} I'm just a long line from a long family." for i in range(20)]
    )
    # Create a prompt input with empty prefix and suffix, but with selected code
    # that exceeds the token budget
    prompt_input = ChatPromptInput(
        message="What does this code do?",
        path="src/example.py",
        prefix="",
        suffix="",
        selected_code=selected_code,
        chat_history=[],
        prefix_begin=-1,
        suffix_end=-1,
        retrieved_chunks=(),
    )
    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt is not None


def test_selected_code_throws_error_for_agent_selected_code_formatter_when_over_fixed_budget():
    """
    Test that when we use the agent selected code prompt formatter, the selected code
    throws error when it's over budget (selected_code_len # of tokens)
    """
    # Set up a token counter
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    # Set small selected code len to make it easier to test clipping
    selected_code_len = 80
    # Create the structured formatter with our selected code formatter
    token_apportionment = ChatTokenApportionment(
        prefix_len=0,
        suffix_len=0,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=selected_code_len,
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        use_fixed_budget_selected_code=True,
    )
    # Create selected code that will exceed the token budget
    selected_code = "\n".join(
        [f"line {i} I'm just a long line from a long family." for i in range(20)]
    )
    # Create a prompt input with empty prefix and suffix, but with selected code
    # that exceeds the token budget
    prompt_input = ChatPromptInput(
        message="What does this code do?",
        path="src/example.py",
        prefix="",
        suffix="",
        selected_code=selected_code,
        chat_history=[],
        prefix_begin=-1,
        suffix_end=-1,
        retrieved_chunks=(),
    )
    with pytest.raises(ExceedContextLength):
        prompter.format_prompt(prompt_input)


def test_structured_binks_agent_prompt_formatter_adds_date_to_system_prompt(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude Sonnet 4",
        creator="Anthropic",
        token_apportionment=token_apportionment,
        system_prompt_factory=get_agent_system_prompt_formatter_v6,
    )

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="tool_use_1",
                    content="ls output",
                    is_error=False,
                ),
            ),
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )

    today = datetime.datetime.now().strftime("%Y-%m-%d")
    feature_flags.set_flag(_ADD_DATE_TO_SYSTEM_PROMPT, False)
    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt and today not in prompt.system_prompt
    feature_flags.set_flag(_ADD_DATE_TO_SYSTEM_PROMPT, True)
    prompt = prompter.format_prompt(prompt_input)
    assert prompt.system_prompt and today in prompt.system_prompt


def _text(id: int, content: str = "foo"):
    return ChatRequestNode(
        id=id,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content=content),
        tool_result_node=None,
    )


def _raw_response(id: int, content: str = "bar"):
    return ChatResultNode(
        id=id,
        type=ChatResultNodeType.RAW_RESPONSE,
        content=content,
        tool_use=None,
    )


def _edit_events(
    id: int,
    path: str,
    source: EditEventSource = EditEventSource.USER_EDIT,
    before_start: int = 5,
    after_start: int = 5,
):
    return ChatRequestNode(
        id=id,
        type=ChatRequestNodeType.EDIT_EVENTS,
        text_node=None,
        tool_result_node=None,
        edit_events_node=ChatRequestEditEvents(
            edit_events=[
                ChatRequestFileEdit(
                    path=path,
                    before_blob_name=f"blob_before_{path}",
                    after_blob_name=f"blob_after_{path}",
                    edits=[
                        ChatRequestSingleEdit(
                            before_line_start=before_start,
                            before_text=f"foo{before_start}\n",
                            after_line_start=after_start,
                            after_text=f"bar{after_start}\n",
                        )
                    ],
                )
            ],
            source=source,
        ),
    )


def _user_edits(id: int, path: str, before_start: int = 5, after_start: int = 5):
    return _edit_events(id, path, EditEventSource.USER_EDIT, before_start, after_start)


def _checkpoint_revert(id: int, path: str, before_start: int = 5, after_start: int = 5):
    return _edit_events(
        id, path, EditEventSource.CHECKPOINT_REVERT, before_start, after_start
    )


def test_structured_binks_agent_prompt_formatter_edit_events_when_enabled(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
):
    """Test that edit events are processed and injected into the prompt when the feature flag is enabled."""
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
    )
    feature_flags.set_flag(_USE_EDIT_EVENTS_IN_PROMPT, True)
    feature_flags.set_flag(_EDIT_EVENTS_VERSION, "v2")

    prompt_input = ChatPromptInput(
        message=[
            _text(1, "Please review my changes."),
            _checkpoint_revert(2, "src/utils.py"),
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=[_user_edits(1, "src/main.py")],
                response_text=[],
                request_id="request_id1",
            ),
            Exchange(
                request_message=[_text(1, "I made some changes.")],
                response_text=[_raw_response(1, "I see your changes.")],
                request_id="request_id2",
            ),
        ],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        memories="I like to eat pizza.",
    )

    prompt = prompter.format_prompt(prompt_input)

    assert prompt.system_prompt is not None

    # Check that message has user edits
    assert isinstance(prompt.message, list)
    assert len(prompt.message) == 2
    assert prompt.message[1] == _text(1, "Please review my changes.")
    assert prompt.message[0].type == ChatRequestNodeType.TEXT
    assert prompt.message[0].text_node is not None
    assert "reverted" in prompt.message[0].text_node.content
    assert "src/utils.py" in prompt.message[0].text_node.content
    assert "@@ -6,1 +6,1 @@" in prompt.message[0].text_node.content
    assert "-foo5" in prompt.message[0].text_node.content
    assert "+bar5" in prompt.message[0].text_node.content

    # Check that chat history has edit events processed
    chat_history_list = list(prompt.chat_history)
    assert len(chat_history_list) == 1
    history_request = chat_history_list[0].request_message
    assert isinstance(history_request, list)
    assert len(history_request) == 2
    assert history_request[1] == _text(1, "I made some changes.")
    assert history_request[0].type == ChatRequestNodeType.TEXT
    assert history_request[0].text_node is not None
    assert "made changes" in history_request[0].text_node.content
    assert "src/main.py" in history_request[0].text_node.content
    assert "@@ -6,1 +6,1 @@" in history_request[0].text_node.content
    assert "-foo5" in history_request[0].text_node.content
    assert "+bar5" in history_request[0].text_node.content


@pytest.mark.parametrize(
    "flag_overrides,prompt_input,assert_indices",
    [
        pytest.param(
            [(_USE_EDIT_EVENTS_IN_PROMPT, True), (_EDIT_EVENTS_VERSION, "v2")],
            ChatPromptInput(
                message=[
                    _text(1, "Please review my changes."),
                    _checkpoint_revert(2, "src/utils.py"),
                ],
                path="test/path.py",
                prefix="",
                selected_code="",
                suffix="",
                chat_history=[
                    Exchange(
                        request_message=[_user_edits(1, "src/main.py")],
                        response_text=[],
                        request_id="request_id1",
                    ),
                    Exchange(
                        request_message=[_text(1, "I made some changes.")],
                        response_text=[_raw_response(1, "I see your changes.")],
                        request_id="request_id2",
                    ),
                ],
                prefix_begin=0,
                suffix_end=100,
                retrieved_chunks=[],
                memories="I like to eat pizza.",
            ),
            [1, 2],
            id="edit_events",
        )
    ],
)
def test_structured_binks_agent_prompt_formatter_prefix_sharing(
    feature_flags: base.feature_flags.LocalFeatureFlagSetter,
    flag_overrides: list[tuple[Any, bool]],
    prompt_input: ChatPromptInput,
    assert_indices: list[int],
):
    token_counter = ClaudeTokenCounter()
    token_apportionment = ChatTokenApportionment(
        prefix_len=1024 * 2,
        suffix_len=1024 * 2,
        path_len=256,
        message_len=-1,  # Deprecated field
        selected_code_len=-1,  # Deprecated field
        chat_history_len=1024 * 60,  # Included in max_prompt_len
        retrieval_len_per_each_user_guided_file=3000,
        retrieval_len_for_user_guided=8000,
        retrieval_len=0,  # No retrieval for agent mode
        max_prompt_len=1024 * 190,
        inject_current_file_into_retrievals=True,
        token_budget_to_trigger_truncation=1024 * 120,
        tool_results_len=1024 * 120,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksAgentPromptFormatter.create(
        token_counter,
        model_name="Claude 3.7 Sonnet",
        creator="Anthropic",
        token_apportionment=token_apportionment,
    )
    for flag, value in flag_overrides:
        feature_flags.set_flag(flag, value)

    mock_client = MockAnthropicClient(
        stream=[],
        model_name="fake-model",
        temperature=0.7,
        max_output_tokens=1024,
    )

    chat_history = list(prompt_input.chat_history)
    prev_messages = []
    for i in assert_indices:
        past_chat_history = chat_history[:i]
        past_prompt_input = dataclasses.replace(
            prompt_input, chat_history=past_chat_history
        )
        if i < len(chat_history):
            past_prompt_input = dataclasses.replace(
                past_prompt_input,
                message=chat_history[i].request_message,
            )
        prompt = prompter.format_prompt(past_prompt_input)
        responses = mock_client.generate_response_stream(
            model_caller="test",
            cur_message=prompt.message,
            chat_history=list(prompt.chat_history),
            system_prompt=prompt.system_prompt,
            tools=[],
            tool_definitions=[],
            tool_choice=None,
            prefill=prompt.prefill,
            yield_final_parameters=True,
        )

        responses = list(responses)
        assert len(responses) == 1
        response = responses[0]
        assert response.final_parameters is not None
        final_params = response.final_parameters
        messages = final_params["messages"]
        assert prev_messages == messages[: len(prev_messages)]
        prev_messages = messages
