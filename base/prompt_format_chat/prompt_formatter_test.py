"""Tests for ChatPromptFormatter."""

from base.prompt_format.common import PromptChunk
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    filter_overlapping_retrieved_chunks,
    filter_overlapping_retrieved_chunks_v2,
)
from base.tokenizers import create_tokenizer_by_name


def get_chunk(char_start, char_end, path):
    return PromptChunk(
        text="a" * (char_end - char_start),
        path=path,
        char_start=char_start,
        char_end=char_end,
    )


def test_filter_overlapping_retrieved_chunks():
    prompt_input = ChatPromptInput(
        path="/p1",
        prefix="(unused prefix) def some_function(a,b):\n",
        suffix="return a+b\n (unused suffix)",
        prefix_begin=10,
        suffix_end=101,
        selected_code="call_some_function(a,b)\n",
        chat_history=[],
        message="Change something",
        retrieved_chunks=(),
    )
    tokenizer = create_tokenizer_by_name("deepseek_coder_instruct")

    # effective indices: prefix_begin=25, suffix_end=85
    effective_prefix = " def some_function(a,b):\n"
    effective_suffix = "return a+b\n"

    prefix_tokens = tokenizer.tokenize_safe(effective_prefix)
    suffix_tokens = tokenizer.tokenize_safe(effective_suffix)

    chunks = [
        get_chunk(0, 10, "/p1"),
        get_chunk(10, 25, "/p1"),
        get_chunk(10, 40, "/p1"),
        get_chunk(30, 40, "/p1"),
        get_chunk(50, 85, "/p1"),
        get_chunk(70, 100, "/p1"),
        get_chunk(85, 100, "/p1"),
        get_chunk(90, 100, "/p1"),
        get_chunk(10, 40, "/p2"),
        get_chunk(30, 40, "/p2"),
        get_chunk(50, 85, "/p2"),
    ]

    filtered_chunks = filter_overlapping_retrieved_chunks(
        prompt_input, tokenizer, prefix_tokens, suffix_tokens, chunks
    )

    assert set(filtered_chunks) == {chunks[i] for i in [0, 1, 6, 7, 8, 9, 10]}


# Empty chunks can be created by the chunker (e.g. from an empty file) and need to be allowed.
def test_filter_overlapping_retrieved_chunks_allows_empty_chunk():
    prompt_input = ChatPromptInput(
        path="/p1",
        prefix="",
        suffix="",
        prefix_begin=0,
        suffix_end=0,
        selected_code="",
        chat_history=[],
        message="Change something",
        retrieved_chunks=(),
    )
    tokenizer = create_tokenizer_by_name("deepseek_coder_instruct")

    effective_prefix = ""
    effective_suffix = ""

    prefix_tokens = tokenizer.tokenize_safe(effective_prefix)
    suffix_tokens = tokenizer.tokenize_safe(effective_suffix)

    chunks = [
        get_chunk(0, 0, "/p1"),
    ]

    filtered_chunks = filter_overlapping_retrieved_chunks(
        prompt_input, tokenizer, prefix_tokens, suffix_tokens, chunks
    )

    assert set(filtered_chunks) == {chunks[0]}


def test_filter_overlapping_retrieved_chunks_v2():
    prompt_input = ChatPromptInput(
        path="/p1",
        prefix="(unused prefix) def some_function(a,b):\n",
        suffix="return a+b\n (unused suffix)",
        prefix_begin=10,
        suffix_end=101,
        selected_code="call_some_function(a,b)\n",
        chat_history=[],
        message="Change something",
        retrieved_chunks=(),
    )

    # effective indices: prefix_begin=25, suffix_end=85
    effective_prefix = " def some_function(a,b):\n"
    effective_suffix = "return a+b\n"

    chunks = [
        get_chunk(0, 10, "/p1"),
        get_chunk(10, 25, "/p1"),
        get_chunk(10, 40, "/p1"),
        get_chunk(30, 40, "/p1"),
        get_chunk(50, 85, "/p1"),
        get_chunk(70, 100, "/p1"),
        get_chunk(85, 100, "/p1"),
        get_chunk(90, 100, "/p1"),
        get_chunk(10, 40, "/p2"),
        get_chunk(30, 40, "/p2"),
        get_chunk(50, 85, "/p2"),
    ]

    filtered_chunks = filter_overlapping_retrieved_chunks_v2(
        prompt_input, effective_prefix, effective_suffix, chunks
    )

    assert set(filtered_chunks) == {chunks[i] for i in [0, 1, 6, 7, 8, 9, 10]}


# Empty chunks can be created by the chunker (e.g. from an empty file) and need to be allowed.
def test_filter_overlapping_retrieved_chunks_v2_allows_empty_chunk():
    prompt_input = ChatPromptInput(
        path="/p1",
        prefix="",
        suffix="",
        prefix_begin=0,
        suffix_end=0,
        selected_code="",
        chat_history=[],
        message="Change something",
        retrieved_chunks=(),
    )

    effective_prefix = ""
    effective_suffix = ""

    chunks = [
        get_chunk(0, 0, "/p1"),
    ]

    filtered_chunks = filter_overlapping_retrieved_chunks_v2(
        prompt_input, effective_prefix, effective_suffix, chunks
    )

    assert set(filtered_chunks) == {chunks[0]}
