"""Contains shared fixtures for the code chat prompt formatting unit tests."""

import pytest

from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    Exchange,
    PromptChunk,
)


@pytest.fixture()
def example_basic_input():
    """Returns an example prompt input."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code="",
        suffix="print(x)\n",
        message="fix bugs",
        chat_history=[],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
        external_source_ids=["docset://Git"],
    )


@pytest.fixture()
def example_retrieval_input():
    """Returns an example prompt input with retrieved chunks."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code=r"""file = pathlib.Path("foo")
for x in file.open():""",
        suffix="    print(x)\n",
        message="fix bugs",
        chat_history=[],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ),
    )


@pytest.fixture()
def example_retrieval_input_no_selected_code():
    """Returns an example prompt input with retrieved chunks and no selected code."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code="",
        suffix="import os\n",
        message="fix bugs",
        chat_history=[],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ),
    )


@pytest.fixture()
def example_retrieval_no_code_input():
    """Returns an example prompt input with retrieved chunks and no code."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="",
        selected_code="",
        suffix="",
        message="What kind of aggregate functions do we have?",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ),
    )


@pytest.fixture()
def example_history_input():
    """Returns prompt input with history and no code."""

    exchange1 = Exchange(
        "What functions are there in this file?",
        "This file has one function, some_function(a,b)",
    )
    exchange2 = Exchange(
        "Is this code valid?",
        "No, the function some_function is missing an implementation.",
    )

    return ChatPromptInput(
        path="src/example.py",
        prefix="",
        selected_code="",
        suffix="",
        message="How could we make this code run?",
        chat_history=[exchange1, exchange2],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
    )


@pytest.fixture()
def example_user_guided_retrieval():
    """Returns an example prompt input with retrieved chunks and no code."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="",
        selected_code="",
        suffix="",
        message="What kind of aggregate functions do we have?",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="x = 5\ny = 2\nprint(x + y)\n",
                path="src/sum.py",
                char_start=0,
                char_end=10,
                blob_name="src/sum.py",
                origin="user_guided_retriever",
            ),
            PromptChunk(
                text=(
                    "numbers = [1, 2, 3, 4, 5]\n"
                    "squared_numbers = [n ** 2 for n in numbers]\n"
                    "print(squared_numbers)\n"
                ),
                path="src/squared.py",
                char_start=0,
                char_end=20,
                blob_name="src/squared.py",
                origin="user_guided_retriever",
            ),
        ),
    )


@pytest.fixture()
def example_user_guided_retrieval_same_file_in_diff_retrievals():
    """Returns an example prompt input with retrieved chunks and no code."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="",
        selected_code="",
        suffix="",
        message="What kind of aggregate functions do we have?",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="x = 5\ny = 2\nprint(x + y)\n",
                path="src/sum.py",
                char_start=0,
                char_end=10,
                blob_name="src/sum.py",
                origin="user_guided_retriever",
            ),
            PromptChunk(
                text=(
                    "numbers = [1, 2, 3, 4, 5]\n"
                    "squared_numbers = [n ** 2 for n in numbers]\n"
                    "print(squared_numbers)\n"
                ),
                path="src/squared.py",
                char_start=0,
                char_end=20,
                blob_name="src/squared.py",
                origin="user_guided_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py:v2",
                origin="user_guided_retriever",
            ),
        ),
    )


@pytest.fixture()
def example_only_user_guided_retrieval():
    """Returns an example prompt input with only user-guided retrieved."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="",
        selected_code="",
        suffix="",
        message="What kind of aggregate functions do we have?",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=(
            PromptChunk(
                text="x = 5\ny = 2\nprint(x + y)\n",
                path="src/sum.py",
                char_start=0,
                char_end=10,
                blob_name="src/sum.py",
                origin="user_guided_retriever",
            ),
            PromptChunk(
                text=(
                    "numbers = [1, 2, 3, 4, 5]\n"
                    "squared_numbers = [n ** 2 for n in numbers]\n"
                    "print(squared_numbers)\n"
                ),
                path="src/squared.py",
                char_start=0,
                char_end=20,
                blob_name="src/squared.py",
                origin="user_guided_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py:v2",
                origin="user_guided_retriever",
            ),
        ),
    )


@pytest.fixture()
def example_empty_chunks():
    """Returns an example prompt input with empty chunks."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="",
        selected_code="",
        suffix="",
        message="What kind of aggregate functions do we have?",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=(
            PromptChunk(
                text="",
                path="src/bar.py",
                char_start=0,
                char_end=0,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ),
    )


@pytest.fixture()
def example_selected_code_in_history():
    """Returns prompt input with selected code that originates in previous exchange."""

    exchange1 = Exchange("Hey, Augment!", "Hey!", "request_id1")

    exchange2 = Exchange(
        "Tell me about this function", "This function that does ...", "request_id2"
    )
    exchange3 = Exchange("You sure?", "Yes, I am.", "request_id3")

    return ChatPromptInput(
        path="/path/to/file.py",
        prefix="some_prefix\n",
        selected_code="some_function(a,b)",
        suffix="some_suffix",
        message="How could we make this code run?",
        chat_history=[exchange1, exchange2, exchange3],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
        context_code_exchange_request_id="request_id2",
    )


@pytest.fixture()
def example_new_selected_code():
    """Returns prompt input with selected code different from previous exchange."""

    exchange1 = Exchange(
        "Hey, Augment!",
        "Hey!",
        "request_id1",
    )

    return ChatPromptInput(
        path="/path/to/file.py",
        prefix="some_prefix\n",
        selected_code="some_function(a,b)",
        suffix="some_suffix",
        message="How could we make this code run?",
        chat_history=[exchange1],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
        context_code_exchange_request_id="new",
    )


@pytest.fixture()
def example_recent_changes() -> ChatPromptInput:
    recent_changes = [
        Modified(
            File("file1.py", "line 1\nline 2\n"),
            File("file1.py", "new line 1\nline 2\n"),
        )
    ]

    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code="",
        suffix="print(x)\n",
        message="fix bugs",
        chat_history=[],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
        recent_changes=recent_changes,
    )


@pytest.fixture()
def example_long_code_input():
    """Returns an example prompt input with a message that is too long."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code=10_000 * "a ",
        suffix="    print(x)\n",
        message="fix bugs",
        chat_history=[],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
    )


@pytest.fixture()
def example_long_message_input():
    """Returns an example prompt input with a message that is too long."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code="def aggregate(a,b):\n",
        suffix="    print(x)\n",
        message=10_000 * "a ",
        chat_history=[],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
    )


@pytest.fixture()
def example_long_chat_history_input():
    """Returns an example prompt input with 4 chat history entries, where the very first one has a very long tool result."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code="def aggregate(a,b):\n",
        suffix="    print(x)\n",
        message="fix bugs",
        chat_history=[
            Exchange(
                request_message="What is the meaning of life?",
                response_text=[
                    ChatResultNode(
                        id=0,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="ask_for_codebase_snippets",
                            input={"description": "meaning of life"},
                            tool_use_id="toolu_123",
                        ),
                    )
                ],
                request_id="request_id1",
            ),
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=0,
                        type=ChatRequestNodeType.TOOL_RESULT,
                        text_node=None,
                        tool_result_node=ChatRequestToolResult(
                            tool_use_id="toolu_123",
                            content="The meaning of life is 42." * 1_000,
                            is_error=False,
                        ),
                    ),
                ],
                response_text="It appears that the meaning of life is 42.",
                request_id="request_id2",
            ),
            Exchange(
                "What is the meaning of life?",
                "The meaning of life is 42.",
                "request_id3",
            ),
            Exchange(
                "What is the meaning of life?",
                "The meaning of life is 42.",
                "request_id4",
            ),
            Exchange(
                "What is the meaning of life?",
                "The meaning of life is 42.",
                "request_id5",
            ),
        ],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
    )


@pytest.fixture()
def example_long_chat_history_input_with_placeholder_tool_result():
    """Returns an example prompt input with 4 chat history entries, where the very first one has a very long tool result."""
    return ChatPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code="def aggregate(a,b):\n",
        suffix="    print(x)\n",
        message="fix bugs",
        chat_history=[
            Exchange(
                request_message="What is the meaning of life?",
                response_text=[
                    ChatResultNode(
                        id=0,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="ask_for_codebase_snippets",
                            input={"description": "meaning of life"},
                            tool_use_id="toolu_123",
                        ),
                    )
                ],
                request_id="request_id1",
            ),
            Exchange(
                request_message=[
                    ChatRequestNode(
                        id=0,
                        type=ChatRequestNodeType.TOOL_RESULT,
                        text_node=None,
                        tool_result_node=ChatRequestToolResult(
                            tool_use_id="toolu_123",
                            content="[Truncated...re-run tool if you need to see output again.]",
                            is_error=False,
                        ),
                    ),
                ],
                response_text="It appears that the meaning of life is 42.",
                request_id="request_id2",
            ),
            Exchange(
                "What is the meaning of life?",
                "The meaning of life is 42.",
                "request_id3",
            ),
            Exchange(
                "What is the meaning of life?",
                "The meaning of life is 42.",
                "request_id4",
            ),
            Exchange(
                "What is the meaning of life?",
                "The meaning of life is 42.",
                "request_id5",
            ),
        ],
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
    )
