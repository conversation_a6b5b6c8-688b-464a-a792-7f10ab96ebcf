"""Tests for the TokenizedStringFormatter class."""

from unittest.mock import Mock

import pytest

from base.tokenizers.tokenizer import Tokenizer
from base.prompt_format_chat.tokenized_string_formatter import (
    TokenizedStringFormatter,
    split_template,
)


def test_split_template_simple_placeholders():
    """Tests splitting a template with simple placeholders."""
    template = "Hello {name}"
    expected = ["Hello ", "{name}"]
    assert split_template(template) == expected


def test_split_template_multiple_placeholders():
    """Tests splitting a template with multiple placeholders."""
    template = "{greeting}, {name}! Welcome to {place}."
    expected = ["{greeting}", ", ", "{name}", "! Welcome to ", "{place}", "."]
    assert split_template(template) == expected


def test_split_template_no_placeholders():
    """Tests splitting a template with no placeholders."""
    template = "Just plain text."
    expected = ["Just plain text."]
    assert split_template(template) == expected


def test_split_template_adjacent_placeholders():
    """Tests splitting a template with adjacent placeholders."""
    template = "{first}{second}"
    expected = ["{first}", "{second}"]
    assert split_template(template) == expected


def test_split_template_text_between_placeholders():
    """Tests splitting a template with text between placeholders."""
    template = "{greeting}, this is {name}."
    expected = ["{greeting}", ", this is ", "{name}", "."]
    assert split_template(template) == expected


def test_split_template_placeholder_at_start():
    """Tests splitting a template with a placeholder at the start."""
    template = "{start} text continues."
    expected = ["{start}", " text continues."]
    assert split_template(template) == expected


def test_split_template_placeholder_at_end():
    """Tests splitting a template with a placeholder at the end."""
    template = "Ends with {end}"
    expected = ["Ends with ", "{end}"]
    assert split_template(template) == expected


def test_split_template_empty_string():
    """Tests splitting an empty template string."""
    template = ""
    expected = []
    assert split_template(template) == expected


def test_split_template_only_placeholders():
    """Tests splitting a template with only placeholders."""
    template = "{only}{placeholders}{here}"
    expected = ["{only}", "{placeholders}", "{here}"]
    assert split_template(template) == expected


def test_split_template_mixed_content():
    """Tests splitting a template with mixed content."""
    template = "Start {middle} end {last}"
    expected = ["Start ", "{middle}", " end ", "{last}"]
    assert split_template(template) == expected


# Define a simple token mapping for testing
TOKEN_MAPPING = {
    "Hello ": [1, 2],
    "John": [3],
    ", welcome to ": [4, 5],
    "Wonderland": [6],
    "!": [7],
    "File content: ": [8, 9],
    "This": [10],
    "is": [11],
    "a": [12],
    "file.": [13],
    " ": [15],  # Added for single space
    "name": [16],
    "place": [17],
    ", ": [18],
    "Value ": [19],
}


def mock_tokenize_safe(text):
    """Mock tokenize a text string by finding the longest matching substring in a predefined vocabulary."""
    tokens = []
    i = 0
    while i < len(text):
        longest_match = None
        longest_token = []
        # Try to find the longest match starting at position i
        for j in range(i + 1, len(text) + 1):
            substring = text[i:j]
            if substring in TOKEN_MAPPING:
                longest_match = j
                longest_token = TOKEN_MAPPING[substring]
        if longest_match is not None:
            tokens.extend(longest_token)
            i = longest_match  # Move the index past the end of the longest match
        else:
            i += 1  # Move to the next character if no match found
    return tokens


def test_tokenized_string_formatter_init():
    """Tests initialization of the formatter with simple placeholders."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello {name}, welcome to {place}!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    expected_parts = [
        [1, 2],  # "Hello "
        "name",
        [4, 5],  # ", welcome to "
        "place",
        [7],  # "!"
    ]
    assert formatter.tokenized_parts_or_placeholders == expected_parts


def test_tokenized_string_formatter_format():
    """Tests formatting with simple placeholders."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello {name}, welcome to {place}!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {"name": [3], "place": [6]}
    result = formatter.format(values)
    expected_result = [1, 2, 3, 4, 5, 6, 7]  # Combined tokenized list
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(expected_result)


def test_tokenized_string_formatter_format_missing_value():
    """Tests formatting with a missing value for a placeholder."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello {name}, welcome to {place}!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {"name": [3]}
    with pytest.raises(ValueError) as excinfo:
        formatter.format(values)
    assert "Missing value for placeholder place" in str(excinfo.value)
    with pytest.raises(ValueError) as excinfo:
        formatter.format_and_count_tokens(values)
    assert "Missing value for placeholder place" in str(excinfo.value)


def test_tokenized_string_formatter_format_and_count_tokens():
    """Tests formatting and counting total tokens."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello {name}, welcome to {place}!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {"name": [3], "place": [6]}
    result = formatter.format(values)
    expected_result = [1, 2, 3, 4, 5, 6, 7]  # Combined tokenized list
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(expected_result)


def test_tokenized_string_formatter_empty_placeholder():
    """Tests formatting with an empty placeholder {}."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello {}!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {"": []}  # Empty placeholder should map to an empty list
    result = formatter.format(values)
    expected_result = [1, 2, 7]
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(expected_result)


def test_tokenized_string_formatter_placeholder_with_space():
    """Tests formatting with a placeholder containing a space { }."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello { }!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {" ": [15]}  # Placeholder with space
    result = formatter.format(values)
    expected_result = [1, 2, 15, 7]
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(expected_result)


def test_tokenized_string_formatter_repeated_placeholders():
    """Tests formatting with repeated placeholders."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello {name}, {name}!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {"name": [3]}
    result = formatter.format(values)
    expected_result = [1, 2, 3, 18, 3, 7]  # Repeated "name" placeholder
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(expected_result)


def test_tokenized_string_formatter_placeholder_with_numbers():
    """Tests formatting with a placeholder containing numbers {123}."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Value {123}"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {"123": [17]}  # Placeholder with numbers
    result = formatter.format(values)
    expected_result = [19, 17]
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(expected_result)


def test_tokenized_string_formatter_non_existent_placeholder():
    """Tests formatting with a non-existent placeholder."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Hello {name}, welcome to {non_existent}!"
    formatter = TokenizedStringFormatter(template, tokenizer)
    values = {"name": [3], "place": [6]}  # Missing "non_existent"
    with pytest.raises(ValueError) as excinfo:
        formatter.format(values)
    assert "Missing value for placeholder non_existent" in str(excinfo.value)
    with pytest.raises(ValueError) as excinfo:
        formatter.format_and_count_tokens(values)
    assert "Missing value for placeholder non_existent" in str(excinfo.value)


def test_format_with_default_values_used():
    """Ensure the formatter uses default values when no explicit values are provided."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "File content: {content}"
    default_values = {"content": [10, 11, 12, 13]}  # Default tokenized content
    formatter = TokenizedStringFormatter(
        template, tokenizer, default_values=default_values
    )
    result = formatter.format({})
    expected_result = [
        8,
        9,
        10,
        11,
        12,
        13,
    ]  # "File content: " followed by default content
    assert result == expected_result
    assert formatter.format_and_count_tokens({}) == len(expected_result)


def test_format_with_value_overriding_default():
    """Verify that explicitly provided values override default values."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "Value {file}"
    default_values = {"file": [13]}  # Default "file."
    formatter = TokenizedStringFormatter(
        template, tokenizer, default_values=default_values
    )
    values = {"file": [10, 11, 12]}  # Providing "This"
    result = formatter.format(values)
    expected_result = [19, 10, 11, 12]
    assert result == expected_result
    assert formatter.format_and_count_tokens(values) == len(expected_result)


def test_format_all_defaults():
    """Check if the formatter correctly uses all default values when no values are provided."""
    tokenizer = Mock(spec=Tokenizer)
    tokenizer.tokenize_safe.side_effect = mock_tokenize_safe
    template = "{greeting}, {name}!"
    default_values = {"greeting": [1, 2], "name": [3]}  # "Hello" and "John"
    formatter = TokenizedStringFormatter(
        template, tokenizer, default_values=default_values
    )
    result = formatter.format({})
    expected_result = [1, 2, 18, 3, 7]  # Expected sequence of tokens
    assert result == expected_result
    assert formatter.format_and_count_tokens({}) == len(expected_result)
