"""Tests for the TokenizedLlama3PromptFormatter."""

import pytest

from base.prompt_format.common import Exchange, PromptChunk
from base.prompt_format_chat.prompt_formatter import (
    ChatTokenApportionment,
    StructuredChatPromptOutput,
)
from base.prompt_format_chat.tokenized_llama_prompt_formatter import (
    StructToTokensLlama3PromptFormatter,
)
from base.test_utils.testing_utils import assert_str_eq
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer

token_apportionment_with_retrieval = ChatTokenApportionment(
    path_len=256,
    prefix_len=1024,
    chat_history_len=2048,
    suffix_len=1024,
    retrieval_len=-1,  # unlimited retrieval
    retrieval_len_per_each_user_guided_file=2000,
    retrieval_len_for_user_guided=3000,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    selected_code_len=-1,  # This field is deprecated!
    message_len=-1,  # This field is deprecated!
)


@pytest.fixture
def prompter():
    return StructToTokensLlama3PromptFormatter(Llama3InstructTokenizer())


def test_with_no_history(prompter: StructToTokensLlama3PromptFormatter):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="(system_prompt)",
            message="(message)",
            chat_history=[],
            retrieved_chunks_in_prompt=[],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

(system_prompt)<|eot_id|><|start_header_id|>user<|end_header_id|>

(message)<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )
    assert actual_prompt_tokens == expected_prompt_tokens


def test_with_history(prompter: StructToTokensLlama3PromptFormatter):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    actual_prompt_tokens = prompter.format_prompt(
        StructuredChatPromptOutput(
            system_prompt="(system_prompt)",
            message="(message)",
            chat_history=[
                Exchange(
                    request_message="(message1)",
                    response_text="(response1)",
                    request_id=None,
                ),
                Exchange(
                    request_message="(message2)",
                    response_text="(response2)",
                    request_id=None,
                ),
            ],
            retrieved_chunks_in_prompt=[
                PromptChunk(
                    text="(retrieved_chunk1)",
                    path="src/bar.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/bar.py",
                    origin="dense_retriever",
                ),
                PromptChunk(
                    text="(retrieved_chunk2)",
                    path="src/foo.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/foo.py",
                    origin="dense_retriever",
                ),
            ],
        )
    ).tokens
    expected_prompt_tokens = prompter.tokenizer.tokenize_unsafe(
        """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

(system_prompt)<|eot_id|><|start_header_id|>user<|end_header_id|>

(message1)<|eot_id|><|start_header_id|>assistant<|end_header_id|>

(response1)<|eot_id|><|start_header_id|>user<|end_header_id|>

(message2)<|eot_id|><|start_header_id|>assistant<|end_header_id|>

(response2)<|eot_id|><|start_header_id|>user<|end_header_id|>

(message)<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    )

    assert_str_eq(
        prompter.tokenizer.detokenize(actual_prompt_tokens),
        prompter.tokenizer.detokenize(expected_prompt_tokens),
    )
    assert actual_prompt_tokens == expected_prompt_tokens
