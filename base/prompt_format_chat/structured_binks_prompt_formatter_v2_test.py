"""Tests for the Binks prompt formatter V2.

pytest base/prompt_format_chat/structured_binks_prompt_formatter_v2_test.py
"""

import dataclasses

import pytest

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    PersonaType,
    Rule,
    RuleType,
)
from base.prompt_format_chat.lib.string_formatter import StringFormatter
from base.prompt_format_chat.lib.token_counter import (
    RoughTokenCounter,
    TokenCounter,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    PromptChunk,
)
from base.prompt_format_chat.structured_binks_prompt_formatter_v2 import (
    StructuredBinksPromptFormatterV2,
    split_short_term_chat_history,
)

TOKEN_APPORTIONMENT = ChatTokenApportionment(
    path_len=256,
    prefix_len=2_048,
    suffix_len=2_048,
    chat_history_len=8_192,
    retrieval_len=8_192,
    retrieval_len_per_each_user_guided_file=6_144,
    retrieval_len_for_user_guided=12_288,
    max_prompt_len=184_320,
    tool_results_len=32_768,
    inject_current_file_into_retrievals=True,
    # Deprecated fields
    message_len=-1,
    selected_code_len=-1,
)

RETRIEVED_CHUNKS = [
    PromptChunk(
        text=f"Retrieved chunk {i}" + "\n".join([f"Line {j}" for j in range(19)]),
        path=f"foo/bar/src{i}.py",
        char_start=i,
        char_end=i + 20,
        blob_name=f"foo/bar/src{i}.py",
        origin="dense_retriever",
    )
    for i in range(1000)  # Reduced from 100_000 to 1000
]


SYSTEM_PROMPT = "System prompt"

USER_GUIDELINES = "User guidelines start, User guidelines end"

USER_GUIDELINES_LONG = "User guidelines start" + "A" * 768 * 3 + "User guidelines end"

WORKSPACE_GUIDELINES = "Workspace guidelines start, Workspace guidelines end"

WORKSPACE_GUIDELINES_LONG = (
    "Workspace guidelines start" + "A" * 28_000 * 3 + "Workspace guidelines end"
)


def _get_system_prompt(token_counter: TokenCounter) -> StringFormatter:
    return StringFormatter(
        SYSTEM_PROMPT,
        token_counter=token_counter,
    )


def _get_user_guided_retrieved_chunks():
    """Returns a list of user-guided retrieval chunks.

    Creates 10 files, each with 100 non-overlapping chunks.
    """
    user_guided_chunks = []

    for file_idx in range(10):  # Reduced from 100 to 10 files
        file_path = f"foo/bar/ug{file_idx}.py"
        file_blob_name = f"ug{file_idx}.py"

        for chunk_idx in range(100):  # Reduced from 1000 to 100 chunks per file
            # Calculate non-overlapping line ranges
            start_line = chunk_idx * 20
            end_line = start_line + 19

            chunk_text = f"User-guided chunk {file_idx}_{chunk_idx}\n" + "\n".join(
                [f"Line {j}" for j in range(start_line, end_line + 1)]
            )

            chunk = PromptChunk(
                text=chunk_text,
                path=file_path,
                char_start=start_line,
                char_end=end_line + 20,  # Add some buffer for the text
                blob_name=file_blob_name,
                origin="user_guided_retriever",
            )

            user_guided_chunks.append(chunk)

    return user_guided_chunks


def _get_user_guided_retrieved_chunks_single_file():
    """Returns a list of user-guided retrieval chunks.

    Creates 1 file with 100 non-overlapping chunks.
    """
    user_guided_chunks = []

    file_path = "foo/bar/ug.py"
    file_blob_name = "ug.py"

    for chunk_idx in range(100):  # Reduced from 1000 to 100 chunks per file
        # Calculate non-overlapping line ranges
        start_line = chunk_idx * 20
        end_line = start_line + 19

        chunk_text = f"User-guided chunk {chunk_idx}\n" + "\n".join(
            [f"Line {j}" for j in range(start_line, end_line + 1)]
        )

        chunk = PromptChunk(
            text=chunk_text,
            path=file_path,
            char_start=start_line,
            char_end=end_line + 20,  # Add some buffer for the text
            blob_name=file_blob_name,
            origin="user_guided_retriever",
        )

        user_guided_chunks.append(chunk)

    return user_guided_chunks


def _get_chat_history_with_tool_calls():
    """Returns a chat history with 1,000 rounds of user messages.

    Each user message is followed by 10 rounds of tool calls and a final response.
    """
    chat_history = []

    for user_round in range(1000):
        for tool_round in range(10):
            if tool_round == 0:
                request_message = f"User message {user_round}"
            else:
                request_message = [
                    ChatRequestNode(
                        id=tool_round,
                        type=ChatRequestNodeType.TOOL_RESULT,
                        text_node=None,
                        tool_result_node=ChatRequestToolResult(
                            tool_use_id=f"tool_use_{user_round}_{tool_round - 1}",
                            content=f"Tool result for round {tool_round - 1}",
                            is_error=False,
                        ),
                    )
                ]

            if tool_round == 9:
                response_test = f"Assistant response {user_round}"
            else:
                response_test = [
                    ChatResultNode(
                        id=tool_round,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="tool_name",
                            input={"tool_param": "tool_input"},
                            tool_use_id=f"tool_use_{user_round}_{tool_round}",
                        ),
                    )
                ]

            exchange = Exchange(
                request_message=request_message,
                response_text=response_test,
            )
            chat_history.append(exchange)

    return chat_history


def _get_chat_history_with_long_tool_results():
    """Returns a chat history with a few rounds of user messages and long tool results.

    This function creates a chat history with two tool calls that have much longer results
    (1000 additional characters) to test token budget handling for large tool results.
    """
    chat_history = []

    # Create a long string of 1000 'A' characters to append to tool results
    long_content = "A" * 1000

    # Create just 2 rounds with long tool results to test token budget handling
    for user_round in range(2):
        for tool_round in range(5):
            if tool_round == 0:
                request_message = f"User message with long tool results {user_round}"
            else:
                # For the first round of each user message, add the long content
                extra_content = long_content if tool_round == 1 else ""
                request_message = [
                    ChatRequestNode(
                        id=tool_round,
                        type=ChatRequestNodeType.TOOL_RESULT,
                        text_node=None,
                        tool_result_node=ChatRequestToolResult(
                            tool_use_id=f"long_tool_use_{user_round}_{tool_round - 1}",
                            content=f"Tool result for round {tool_round - 1} with extra content: {extra_content}",
                            is_error=False,
                        ),
                    )
                ]

            if tool_round == 4:
                response_test = (
                    f"Assistant response for long tool result test {user_round}"
                )
            else:
                response_test = [
                    ChatResultNode(
                        id=tool_round,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="tool_name",
                            input={"tool_param": "tool_input for long result test"},
                            tool_use_id=f"long_tool_use_{user_round}_{tool_round}",
                        ),
                    )
                ]

            exchange = Exchange(
                request_message=request_message,
                response_text=response_test,
            )
            chat_history.append(exchange)

    return chat_history


def _get_chat_history():
    """Returns a chat history with 10,000 rounds of user messages."""
    chat_history = []

    for user_round in range(10_000):
        request_message = f"User message {user_round}"
        response_test = f"Assistant response {user_round}"
        exchange = Exchange(
            request_message=request_message,
            response_text=response_test,
        )
        chat_history.append(exchange)

    return chat_history


CHAT_PROMPT_INPUT = ChatPromptInput(
    message="Current user message",
    path="foo/bar/src.py",
    prefix="Prefix",
    selected_code="",
    suffix="Suffix",
    chat_history=_get_chat_history(),
    prefix_begin=0,
    suffix_end=12,
    retrieved_chunks=RETRIEVED_CHUNKS,
    external_source_ids=[],
    workspace_guidelines=None,
    user_guidelines=None,
)


def test_structured_binks_prompt_formatter_v2():
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )
    prompt = prompt_formatter.format_prompt(CHAT_PROMPT_INPUT)

    assert prompt.system_prompt == SYSTEM_PROMPT
    assert prompt.message == "Current user message"

    chat_history = list(prompt.chat_history)
    assert len(chat_history) > 500
    assert chat_history[-1].request_message == "User message 9999"
    assert chat_history[-1].response_text == "Assistant response 9999"

    retrieval_message = chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    assert "foo/bar/src.py" in retrieval_message
    assert "foo/bar/src0.py" in retrieval_message
    assert "foo/bar/src12.py" in retrieval_message
    assert "foo/bar/src16.py" in retrieval_message
    assert "Prefix" in retrieval_message
    assert "Suffix" in retrieval_message

    # Check that the retrieval message is at least 90% of its budget length
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    min_expected_tokens = int(TOKEN_APPORTIONMENT.retrieval_len * 0.9)
    assert retrieval_message_tokens >= min_expected_tokens, (
        f"Retrieval message token count {retrieval_message_tokens} is less than 90% of "
        f"the budget {min_expected_tokens} (90% of {TOKEN_APPORTIONMENT.retrieval_len})"
    )

    # Check that the retrieval message doesn't exceed its budget length
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_structured_binks_prompt_formatter_v2_with_tools():
    """Tests the formatter with chat history containing tool calls and results.

    This test verifies:
    1. Tools in chat history are preserved
    2. Tool results don't exceed the token budget
    3. Tool use/result pairs are properly maintained
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with tool calls
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        chat_history=_get_chat_history_with_tool_calls(),
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert prompt.system_prompt == SYSTEM_PROMPT
    assert prompt.message == "Current user message"

    chat_history = list(prompt.chat_history)

    # Verify tools exist in the chat history
    tool_uses_found = False
    tool_results_found = False
    tool_result_tokens = 0
    tool_result_contents = []

    for exchange in chat_history:
        # Check for tool results in request messages
        if isinstance(exchange.request_message, list):
            for node in exchange.request_message:
                if node.type == ChatRequestNodeType.TOOL_RESULT:
                    tool_results_found = True
                    # Collect non-omission tool results
                    if not node.tool_result_node.content.startswith(
                        "<Tool result omitted>"
                    ):
                        tool_result_tokens += token_counter.count_tokens(
                            node.tool_result_node.content
                        )
                        tool_result_contents.append(node.tool_result_node.content)

        # Check for tool uses in response messages
        if isinstance(exchange.response_text, list):
            for node in exchange.response_text:
                if node.type == ChatResultNodeType.TOOL_USE:
                    tool_uses_found = True

    # Verify tools were found in the chat history
    assert tool_uses_found, "No tool uses found in chat history"
    assert tool_results_found, "No tool results found in chat history"

    # Verify tool results don't exceed the token budget
    assert (
        tool_result_tokens <= TOKEN_APPORTIONMENT.tool_results_len
    ), f"Tool results exceed token budget: {tool_result_tokens} > {TOKEN_APPORTIONMENT.tool_results_len}"

    # Verify that each user round has the expected structure
    # (user message followed by tool calls and a final response)
    user_rounds_found = set()
    for exchange in chat_history:
        if isinstance(
            exchange.request_message, str
        ) and exchange.request_message.startswith("User message "):
            try:
                round_num = int(exchange.request_message.split()[-1])
                user_rounds_found.add(round_num)
            except (ValueError, IndexError):
                pass

    # We should have some user rounds in the history (exact number depends on token limits)
    assert len(user_rounds_found) > 0, "No user rounds found in chat history"

    # Check that the retrieval message is at least 90% of its budget length
    retrieval_message = chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    min_expected_tokens = int(TOKEN_APPORTIONMENT.retrieval_len * 0.9)
    assert retrieval_message_tokens >= min_expected_tokens, (
        f"Retrieval message token count {retrieval_message_tokens} is less than 90% of "
        f"the budget {min_expected_tokens} (90% of {TOKEN_APPORTIONMENT.retrieval_len})"
    )

    # Check that the retrieval message doesn't exceed its budget length
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_structured_binks_prompt_formatter_v2_with_long_tool_results():
    """Tests the formatter with chat history containing long tool results.

    This test verifies:
    1. Long tool results are properly handled within the token budget
    2. Tool results don't exceed the token budget even with large content
    3. The formatter correctly processes chat history with large tool results
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with long tool results
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        chat_history=_get_chat_history_with_long_tool_results(),
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert prompt.system_prompt == SYSTEM_PROMPT
    assert prompt.message == "Current user message"

    chat_history = list(prompt.chat_history)

    # Verify long tool results are handled properly
    tool_results_found = False
    tool_result_tokens = 0
    long_tool_results_found = 0

    for exchange in chat_history:
        # Check for tool results in request messages
        if isinstance(exchange.request_message, list):
            for node in exchange.request_message:
                if node.type == ChatRequestNodeType.TOOL_RESULT:
                    tool_results_found = True
                    # Count tokens for this tool result
                    result_tokens = token_counter.count_tokens(
                        node.tool_result_node.content
                    )
                    tool_result_tokens += result_tokens

                    # Check if this is one of our long tool results
                    if "AAAAA" in node.tool_result_node.content:
                        long_tool_results_found += 1

    # Verify tool results were found
    assert tool_results_found, "No tool results found in chat history"

    # Verify tool results don't exceed the token budget
    assert (
        tool_result_tokens <= TOKEN_APPORTIONMENT.tool_results_len
    ), f"Tool results exceed token budget: {tool_result_tokens} > {TOKEN_APPORTIONMENT.tool_results_len}"

    # Verify that at least one of our long tool results was included
    # (depending on token budget, some might be omitted)
    assert long_tool_results_found >= 0, "No long tool results found in chat history"

    # Check that the retrieval message is at least 90% of its budget length
    retrieval_message = chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    min_expected_tokens = int(TOKEN_APPORTIONMENT.retrieval_len * 0.9)
    assert retrieval_message_tokens >= min_expected_tokens, (
        f"Retrieval message token count {retrieval_message_tokens} is less than 90% of "
        f"the budget {min_expected_tokens} (90% of {TOKEN_APPORTIONMENT.retrieval_len})"
    )

    # Check that the retrieval message doesn't exceed its budget length
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_short_user_guidelines_only():
    """Tests that short user guidelines are properly included in the system prompt."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with short user guidelines
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        user_guidelines=USER_GUIDELINES,
        workspace_guidelines=None,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert isinstance(prompt.system_prompt, str)
    assert "User guidelines start" in prompt.system_prompt
    assert "User guidelines end" in prompt.system_prompt
    assert "Workspace guidelines start" not in prompt.system_prompt
    assert "Workspace guidelines end" not in prompt.system_prompt


def test_short_workspace_guidelines_only():
    """Tests that short workspace guidelines are properly included in the system prompt."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with short workspace guidelines
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        user_guidelines=None,
        workspace_guidelines=WORKSPACE_GUIDELINES,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert isinstance(prompt.system_prompt, str)
    assert "User guidelines start" not in prompt.system_prompt
    assert "User guidelines end" not in prompt.system_prompt
    assert "Workspace guidelines start" in prompt.system_prompt
    assert "Workspace guidelines end" in prompt.system_prompt


def test_short_user_and_workspace_guidelines():
    """Tests that both short user and workspace guidelines are properly included in the system prompt."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with both short user and workspace guidelines
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        user_guidelines=USER_GUIDELINES,
        workspace_guidelines=WORKSPACE_GUIDELINES,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert isinstance(prompt.system_prompt, str)
    assert "User guidelines start" in prompt.system_prompt
    assert "User guidelines end" in prompt.system_prompt
    assert "Workspace guidelines start" in prompt.system_prompt
    assert "Workspace guidelines end" in prompt.system_prompt


def test_long_user_guidelines_only():
    """Tests that long user guidelines are properly included in the system prompt."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with long user guidelines
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        user_guidelines=USER_GUIDELINES_LONG,
        workspace_guidelines=None,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert isinstance(prompt.system_prompt, str)
    assert "User guidelines start" in prompt.system_prompt
    assert "User guidelines end" in prompt.system_prompt
    assert "Workspace guidelines start" not in prompt.system_prompt
    assert "Workspace guidelines end" not in prompt.system_prompt


def test_long_workspace_guidelines_only():
    """Tests that long workspace guidelines are properly included in the system prompt."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with long workspace guidelines
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        user_guidelines=None,
        workspace_guidelines=WORKSPACE_GUIDELINES_LONG,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert isinstance(prompt.system_prompt, str)
    assert "User guidelines start" not in prompt.system_prompt
    assert "User guidelines end" not in prompt.system_prompt
    assert "Workspace guidelines start" in prompt.system_prompt
    assert "Workspace guidelines end" in prompt.system_prompt


def test_long_user_and_workspace_guidelines():
    """Tests that both long user and workspace guidelines are properly included in the system prompt."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a chat prompt input with both long user and workspace guidelines
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        user_guidelines=USER_GUIDELINES_LONG,
        workspace_guidelines=WORKSPACE_GUIDELINES_LONG,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert isinstance(prompt.system_prompt, str)
    assert "User guidelines start" in prompt.system_prompt
    assert "User guidelines end" in prompt.system_prompt
    assert "Workspace guidelines start" in prompt.system_prompt
    assert "Workspace guidelines end" in prompt.system_prompt


def test_user_guided_retrieval():
    """Tests the user-guided retrieval mechanism.

    This test verifies:
    1. General retrieval chunks come first, followed by user-guided chunks
    2. User-guided chunks are included despite general chunks being ranked higher
    3. The retrieval length per user-guided file threshold is respected
    4. The overall retrieval for user-guided threshold is respected
    5. At least two separate files are included in the user guidance section
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Get user-guided chunks
    user_guided_chunks = _get_user_guided_retrieved_chunks()

    # Combine general and user-guided chunks
    combined_chunks = RETRIEVED_CHUNKS + user_guided_chunks

    # Create a chat prompt input with combined chunks
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        retrieved_chunks=combined_chunks,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # Get the retrieval message from the chat history
    chat_history = list(prompt.chat_history)
    retrieval_message = chat_history[0].request_message

    # Verify retrieval message contains content from both general and user-guided chunks
    assert (
        "Retrieved chunk" in retrieval_message
    ), "General retrieval chunks not found in retrieval message"
    assert (
        "User-guided chunk" in retrieval_message
    ), "User-guided chunks not found in retrieval message"

    assert (
        token_counter.count_tokens_in_request(retrieval_message)
        <= TOKEN_APPORTIONMENT.retrieval_len
        + TOKEN_APPORTIONMENT.retrieval_len_for_user_guided
    )


def test_user_guided_retrieval_single_file():
    """Tests the user-guided retrieval mechanism when there is only one user-guided file.

    This test verifies:
    1. All user-guided chunks from the single file are included
    2. The retrieval length per user-guided file threshold is respected
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Get user-guided chunks from a single file
    user_guided_chunks = _get_user_guided_retrieved_chunks_single_file()

    # Combine general and user-guided chunks
    combined_chunks = RETRIEVED_CHUNKS + user_guided_chunks

    # Create a chat prompt input with combined chunks
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        retrieved_chunks=combined_chunks,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # Get the retrieval message from the chat history
    chat_history = list(prompt.chat_history)
    retrieval_message = chat_history[0].request_message

    # Verify retrieval message contains content from both general and user-guided chunks
    assert (
        "Retrieved chunk" in retrieval_message
    ), "General retrieval chunks not found in retrieval message"
    assert (
        "User-guided chunk" in retrieval_message
    ), "User-guided chunks not found in retrieval message"

    assert (
        token_counter.count_tokens_in_request(retrieval_message)
        <= TOKEN_APPORTIONMENT.retrieval_len
        + TOKEN_APPORTIONMENT.retrieval_len_per_each_user_guided_file
    )


def _create_large_text(prefix=""):
    """Helper function to create a text with approximately 30K tokens."""
    return f"{prefix}" + "A" * 100000


def _verify_token_count(token_counter, text, min_tokens, max_tokens):
    """Helper function to verify token count is in the expected range."""
    token_count = token_counter.count_tokens(text)
    assert (
        token_count >= min_tokens
    ), f"Token count {token_count} is less than {min_tokens} tokens"
    assert (
        token_count <= max_tokens
    ), f"Token count {token_count} is more than {max_tokens} tokens"
    return token_count


def _get_chat_history_with_large_previous_message():
    """Returns a chat history with a large (30K token) previous message."""
    chat_history = []

    # Create a large message for the previous exchange
    large_message = _create_large_text("This is a large previous message: ")

    # Add a few normal exchanges first
    for i in range(3):
        exchange = Exchange(
            request_message=f"Normal user message {i}",
            response_text=f"Normal assistant response {i}",
        )
        chat_history.append(exchange)

    # Add the large message exchange
    exchange = Exchange(
        request_message=large_message,
        response_text="Assistant response to large message",
    )
    chat_history.append(exchange)

    return chat_history


def test_large_current_message():
    """Tests that the formatter can handle a 30K token current message.

    This test verifies:
    1. A 30K token current message is fully included in the prompt output
    2. The formatter doesn't truncate or omit any part of the large message
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a message with approximately 30K tokens
    large_message = _create_large_text("This is a large message: ")

    # Verify the message is approximately 30K tokens
    message_token_count = _verify_token_count(
        token_counter, large_message, 25000, 35000
    )

    # Create a chat prompt input with the large message
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        message=large_message,
    )

    # Format the prompt
    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # Verify the entire message is included in the output
    assert (
        prompt.message == large_message
    ), "The large message was not fully included in the prompt output"

    # Verify the token count of the message in the output matches our input
    output_message_token_count = token_counter.count_tokens(prompt.message)
    assert (
        output_message_token_count == message_token_count
    ), f"Output message token count {output_message_token_count} does not match input message token count {message_token_count}"

    # Check that the retrieval message doesn't exceed its budget length
    chat_history = list(prompt.chat_history)
    retrieval_message = chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_large_previous_message():
    """Tests that the formatter can handle a 30K token previous message.

    This test verifies:
    1. A 30K token previous message is fully included in the prompt output
    2. The formatter doesn't truncate or omit any part of the large previous message
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Get chat history with a large previous message
    chat_history = _get_chat_history_with_large_previous_message()

    # Verify the large message is approximately 30K tokens
    large_message = chat_history[-1].request_message
    _verify_token_count(token_counter, large_message, 25000, 35000)

    # Create a chat prompt input with the chat history containing the large message
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        chat_history=chat_history,
        message="Current user message after large previous message",
    )

    # Format the prompt
    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # Verify the large previous message is included in the output
    found_large_message = False
    for exchange in prompt.chat_history:
        if exchange.request_message == large_message:
            found_large_message = True
            break

    assert (
        found_large_message
    ), "The large previous message was not found in the chat history"

    # Check that the retrieval message doesn't exceed its budget length
    retrieval_message = chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_large_selected_code():
    """Tests that the formatter can handle a 30K token selected code section.

    This test verifies:
    1. A 30K token selected code section is fully included in the prompt output
    2. The formatter doesn't truncate or omit any part of the large selected code
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a large selected code with approximately 30K tokens
    large_selected_code = _create_large_text(
        "// This is a large selected code section:\n"
    )

    # Verify the selected code is approximately 30K tokens
    selected_code_token_count = _verify_token_count(
        token_counter, large_selected_code, 25000, 35000
    )

    # Create a chat prompt input with the large selected code
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        message="Current user message with large selected code",
        selected_code=large_selected_code,
        context_code_exchange_request_id="new",  # Mark as newly selected code
    )

    # Format the prompt
    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # The selected code should be prepended to the current message
    assert (
        large_selected_code in prompt.message
    ), "The large selected code was not included in the prompt output"

    # Verify the token count of the message (which includes the selected code) is as expected
    output_message_token_count = token_counter.count_tokens(prompt.message)
    expected_token_count = selected_code_token_count + token_counter.count_tokens(
        "Current user message with large selected code"
    )
    assert (
        output_message_token_count >= expected_token_count * 0.95
    ), f"Output message token count {output_message_token_count} is significantly less than expected {expected_token_count}"

    # Check that the retrieval message doesn't exceed its budget length
    chat_history = list(prompt.chat_history)
    retrieval_message = chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_large_current_and_previous_message():
    """Tests that the formatter can handle both 30K token current and previous messages.

    This test verifies:
    1. Both large messages are fully included in the prompt output
    2. The formatter doesn't truncate or omit any part of either large message
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Get chat history with a large previous message
    chat_history = _get_chat_history_with_large_previous_message()

    # Create a large current message
    large_current_message = _create_large_text("This is a large current message: ")

    # Verify token counts
    large_previous_message = chat_history[-1].request_message
    _verify_token_count(token_counter, large_previous_message, 25000, 35000)
    _verify_token_count(token_counter, large_current_message, 25000, 35000)

    # Create a chat prompt input with both large messages
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        chat_history=chat_history,
        message=large_current_message,
    )

    # Format the prompt
    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # Verify the large current message is included in the output
    assert (
        prompt.message == large_current_message
    ), "The large current message was not fully included in the prompt output"

    # Verify the large previous message is included in the output
    found_large_message = False
    for exchange in prompt.chat_history:
        if exchange.request_message == large_previous_message:
            found_large_message = True
            break

    assert (
        found_large_message
    ), "The large previous message was not found in the chat history"

    # Check that the retrieval message doesn't exceed its budget length
    retrieval_message = prompt.chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_large_current_message_and_selected_code():
    """Tests that the formatter can handle both 30K token current message and selected code.

    This test verifies:
    1. Both large components are fully included in the prompt output
    2. The formatter doesn't truncate or omit any part of either large component
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create a large current message
    large_current_message = _create_large_text("This is a large current message: ")

    # Create a large selected code
    large_selected_code = _create_large_text(
        "// This is a large selected code section:\n"
    )

    # Verify token counts
    _verify_token_count(token_counter, large_current_message, 25000, 35000)
    _verify_token_count(token_counter, large_selected_code, 25000, 35000)

    # Create a chat prompt input with both large components
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        message=large_current_message,
        selected_code=large_selected_code,
        context_code_exchange_request_id="new",  # Mark as newly selected code
    )

    # Format the prompt
    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # The selected code should be prepended to the current message
    assert (
        large_selected_code in prompt.message
    ), "The large selected code was not included in the prompt output"
    assert (
        large_current_message in prompt.message
    ), "The large current message was not fully included in the prompt output"

    # Check that the retrieval message doesn't exceed its budget length
    chat_history = list(prompt.chat_history)
    retrieval_message = chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_large_previous_message_and_selected_code():
    """Tests that the formatter can handle both 30K token previous message and selected code.

    This test verifies:
    1. Both large components are fully included in the prompt output
    2. The formatter doesn't truncate or omit any part of either large component
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Get chat history with a large previous message
    chat_history = _get_chat_history_with_large_previous_message()

    # Create a large selected code
    large_selected_code = _create_large_text(
        "// This is a large selected code section:\n"
    )

    # Verify token counts
    large_previous_message = chat_history[-1].request_message
    _verify_token_count(token_counter, large_previous_message, 25000, 35000)
    _verify_token_count(token_counter, large_selected_code, 25000, 35000)

    # Create a chat prompt input with both large components
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        chat_history=chat_history,
        message="Current user message with large selected code",
        selected_code=large_selected_code,
        context_code_exchange_request_id="new",  # Mark as newly selected code
    )

    # Format the prompt
    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # The selected code should be prepended to the current message
    assert (
        large_selected_code in prompt.message
    ), "The large selected code was not included in the prompt output"

    # Verify the large previous message is included in the output
    found_large_message = False
    for exchange in prompt.chat_history:
        if exchange.request_message == large_previous_message:
            found_large_message = True
            break

    assert (
        found_large_message
    ), "The large previous message was not found in the chat history"

    # Check that the retrieval message doesn't exceed its budget length
    retrieval_message = prompt.chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


def test_all_large_components():
    """Tests that the formatter can handle all three 30K token components.

    This test verifies:
    1. All three large components (current message, previous message, selected code) are fully included
    2. The formatter doesn't truncate or omit any part of any large component
    """
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Get chat history with a large previous message
    chat_history = _get_chat_history_with_large_previous_message()

    # Create a large current message
    large_current_message = _create_large_text("This is a large current message: ")

    # Create a large selected code
    large_selected_code = _create_large_text(
        "// This is a large selected code section:\n"
    )

    # Verify token counts
    large_previous_message = chat_history[-1].request_message
    _verify_token_count(token_counter, large_previous_message, 25000, 35000)
    _verify_token_count(token_counter, large_current_message, 25000, 35000)
    _verify_token_count(token_counter, large_selected_code, 25000, 35000)

    # Create a chat prompt input with all three large components
    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        chat_history=chat_history,
        message=large_current_message,
        selected_code=large_selected_code,
        context_code_exchange_request_id="new",  # Mark as newly selected code
    )

    # Format the prompt
    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    # The selected code should be prepended to the current message
    assert (
        large_selected_code in prompt.message
    ), "The large selected code was not included in the prompt output"
    assert (
        large_current_message in prompt.message
    ), "The large current message was not fully included in the prompt output"

    # Verify the large previous message is included in the output
    found_large_message = False
    for exchange in prompt.chat_history:
        if exchange.request_message == large_previous_message:
            found_large_message = True
            break

    assert (
        found_large_message
    ), "The large previous message was not found in the chat history"

    # Check that the retrieval message doesn't exceed its budget length
    retrieval_message = prompt.chat_history[0].request_message
    assert isinstance(retrieval_message, str)
    retrieval_message_tokens = token_counter.count_tokens(retrieval_message)
    assert retrieval_message_tokens <= TOKEN_APPORTIONMENT.retrieval_len, (
        f"Retrieval message token count {retrieval_message_tokens} exceeds "
        f"the budget {TOKEN_APPORTIONMENT.retrieval_len}"
    )


# Tests for split_short_term_chat_history function


def test_split_short_term_chat_history_empty():
    """Test splitting an empty chat history."""
    chat_history = []
    earlier, short_term = split_short_term_chat_history(chat_history)

    assert earlier == [], "Earlier history should be empty"
    assert short_term == [], "Short-term history should be empty"


def test_split_short_term_chat_history_single_exchange():
    """Test splitting a chat history with a single exchange."""
    chat_history = [
        Exchange(
            request_message="User message",
            response_text="Assistant response",
        )
    ]

    earlier, short_term = split_short_term_chat_history(chat_history)

    assert earlier == [], "Earlier history should be empty"
    assert len(short_term) == 1, "Short-term history should have one exchange"
    assert short_term[0].request_message == "User message"
    assert short_term[0].response_text == "Assistant response"


def test_split_short_term_chat_history_multiple_exchanges():
    """Test splitting a chat history with multiple exchanges."""
    chat_history = [
        Exchange(
            request_message="User message 1",
            response_text="Assistant response 1",
        ),
        Exchange(
            request_message="User message 2",
            response_text="Assistant response 2",
        ),
        Exchange(
            request_message="User message 3",
            response_text="Assistant response 3",
        ),
    ]

    earlier, short_term = split_short_term_chat_history(chat_history)

    assert len(earlier) == 2, "Earlier history should have two exchanges"
    assert earlier[0].request_message == "User message 1"
    assert earlier[1].request_message == "User message 2"

    assert len(short_term) == 1, "Short-term history should have one exchange"
    assert short_term[0].request_message == "User message 3"


def test_split_short_term_chat_history_with_tool_results():
    """Test splitting a chat history with tool results following a user message."""
    chat_history = [
        Exchange(
            request_message="User message 1",
            response_text="Assistant response 1",
        ),
        Exchange(
            request_message="User message 2",
            response_text=[  # Tool use in response
                ChatResultNode(
                    id=1,
                    type=ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=ChatResultToolUse(
                        name="tool_name",
                        input={"param": "value"},
                        tool_use_id="tool_1",
                    ),
                )
            ],
        ),
        Exchange(
            request_message=[  # Tool result in request
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool_1",
                        content="Tool result content",
                        is_error=False,
                    ),
                )
            ],
            response_text="Assistant final response",
        ),
    ]

    earlier, short_term = split_short_term_chat_history(chat_history)

    assert len(earlier) == 2, "Earlier history should have two exchanges"
    assert earlier[0].request_message == "User message 1"
    assert earlier[1].request_message == "User message 2"

    assert len(short_term) == 1, "Short-term history should have one exchange"
    assert isinstance(
        short_term[0].request_message, list
    ), "Short-term exchange should have a list request message"
    assert short_term[0].request_message[0].type == ChatRequestNodeType.TOOL_RESULT


def test_split_short_term_chat_history_multiple_tool_results():
    """Test splitting a chat history with multiple tool results following a user message."""
    chat_history = [
        Exchange(
            request_message="Earlier user message",
            response_text="Earlier assistant response",
        ),
        Exchange(
            request_message="User message",
            response_text=[  # First tool use
                ChatResultNode(
                    id=1,
                    type=ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=ChatResultToolUse(
                        name="tool_1",
                        input={"param": "value1"},
                        tool_use_id="tool_use_1",
                    ),
                )
            ],
        ),
        Exchange(
            request_message=[  # First tool result
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool_use_1",
                        content="Tool 1 result",
                        is_error=False,
                    ),
                )
            ],
            response_text=[  # Second tool use
                ChatResultNode(
                    id=2,
                    type=ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=ChatResultToolUse(
                        name="tool_2",
                        input={"param": "value2"},
                        tool_use_id="tool_use_2",
                    ),
                )
            ],
        ),
        Exchange(
            request_message=[  # Second tool result
                ChatRequestNode(
                    id=2,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool_use_2",
                        content="Tool 2 result",
                        is_error=False,
                    ),
                )
            ],
            response_text="Final assistant response",
        ),
    ]

    earlier, short_term = split_short_term_chat_history(chat_history)

    assert len(earlier) == 3, "Earlier history should have three exchanges"
    assert earlier[0].request_message == "Earlier user message"
    assert earlier[1].request_message == "User message"
    assert isinstance(
        earlier[2].request_message, list
    ), "Third earlier exchange should have a list request message"
    assert earlier[2].request_message[0].type == ChatRequestNodeType.TOOL_RESULT

    assert len(short_term) == 1, "Short-term history should have one exchange"
    assert isinstance(
        short_term[0].request_message, list
    ), "Short-term exchange should have a list request message"
    assert short_term[0].request_message[0].type == ChatRequestNodeType.TOOL_RESULT


def test_split_short_term_chat_history_mixed_message_types():
    """Test splitting a chat history with mixed message types."""
    chat_history = [
        Exchange(
            request_message="User message 1",
            response_text="Assistant response 1",
        ),
        Exchange(
            request_message=[  # Non-tool result list message
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TEXT,
                    text_node="Text message",
                    tool_result_node=None,
                )
            ],
            response_text="Assistant response 2",
        ),
        Exchange(
            request_message="User message 3",
            response_text="Assistant response 3",
        ),
    ]

    earlier, short_term = split_short_term_chat_history(chat_history)

    assert len(earlier) == 2, "Earlier history should have two exchanges"
    assert earlier[0].request_message == "User message 1"
    assert isinstance(
        earlier[1].request_message, list
    ), "Second earlier exchange should have a list request message"

    assert len(short_term) == 1, "Short-term history should have one exchange"
    assert short_term[0].request_message == "User message 3"


def test_structured_binks_prompt_formatter_v2_with_rules():
    """Test that rules are properly included in the system prompt."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create test rules
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="rules/coding_style.md",
            content="Use 4 spaces for indentation.",
        ),
        Rule(
            type=RuleType.MANUAL,
            path="rules/naming.md",
            content="Use camelCase for variable names.",
        ),
        Rule(
            type=RuleType.AGENT_REQUESTED,
            path="rules/database.md",
            content="This content is ignored for AGENT_REQUESTED rules.",
            description="user is working with database operations",
        ),
    ]

    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        user_guidelines="Use descriptive variable names.",
        workspace_guidelines="Follow PEP 8 guidelines.",
        persona_type=PersonaType.DEFAULT,
        rules=rules,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert prompt.system_prompt

    # Check that all rule types are included
    assert "Use 4 spaces for indentation." in prompt.system_prompt
    assert "Use camelCase for variable names." in prompt.system_prompt
    assert "user is working with database operations" in prompt.system_prompt

    # Check that rules are properly formatted
    assert "Additional rule located in rules/coding_style.md:" in prompt.system_prompt
    assert "Additional rule located in rules/naming.md:" in prompt.system_prompt
    assert "Additional agent requested rules:" in prompt.system_prompt

    # Check that user and workspace guidelines are still included
    assert "Use descriptive variable names." in prompt.system_prompt
    assert "Follow PEP 8 guidelines." in prompt.system_prompt


def test_structured_binks_prompt_formatter_v2_with_rules_and_no_guidelines():
    """Test that rules work even without user/workspace guidelines."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="rules/style.md",
            content="Always use type hints.",
        ),
    ]

    chat_prompt_input = dataclasses.replace(
        CHAT_PROMPT_INPUT,
        rules=rules,
    )

    prompt = prompt_formatter.format_prompt(chat_prompt_input)

    assert prompt.system_prompt
    assert "Always use type hints." in prompt.system_prompt
    assert "Additional rule located in rules/style.md:" in prompt.system_prompt


def test_structured_binks_prompt_formatter_v2_backward_compatibility():
    """Test that the formatter works without rules field (backward compatibility)."""
    token_counter = RoughTokenCounter()
    prompt_formatter = StructuredBinksPromptFormatterV2.create(
        token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
    )

    # Create input without rules field (simulating older code)
    chat_prompt_input_without_rules = ChatPromptInput(
        message="Test message",
        path="test/path.py",
        prefix="def test_function():\n    ",
        selected_code="pass",
        suffix="\n\nprint('Test')",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        user_guidelines="Use descriptive variable names.",
        workspace_guidelines="Follow PEP 8 guidelines.",
        persona_type=PersonaType.DEFAULT,
        # Note: no rules field
    )

    # This should not raise an error
    prompt = prompt_formatter.format_prompt(chat_prompt_input_without_rules)

    assert prompt.system_prompt
    assert "Use descriptive variable names." in prompt.system_prompt
    assert "Follow PEP 8 guidelines." in prompt.system_prompt
