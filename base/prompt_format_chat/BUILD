load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "conftest",
    srcs = [
        "conftest.py",
    ],
    visibility = [
        "//base/prompt_format_chat/legacy_binks:__subpackages__",
        "//base/prompt_format_chat/lib:__subpackages__",
        "//base/prompt_format_router:__subpackages__",
    ],
    deps = [
        ":prompt_formatter",
        "//base/tokenizers",
        requirement("pytest"),
    ],
)

pytest_test(
    name = "prompt_format_chat_test",
    srcs = ["prompt_format_chat_test.py"],
    deps = [
        ":prompt_format_chat",
    ],
)

py_library(
    name = "prompt_formatter",
    srcs = [
        "prompt_formatter.py",
    ],
    visibility = [
        # TODO: delete
        "//base/prompt_format_rerank:__subpackages__",
        "//base/prompt_format_chat/lib:__subpackages__",
        "//base/prompt_format_chat/legacy_binks:__subpackages__",
        "//base/prompt_format_smart_paste:__subpackages__",
        "//base/prompt_format_router:__subpackages__",
        "//base/prompt_format_postprocess:__subpackages__",
        "//services/chat_host/server/prompt_format:__pkg__",
    ],
    deps = [
        "//base/diff_utils",
        "//base/prompt_format:common",
        "//base/tokenizers",
        requirement("dataclasses_json"),
    ],
)

pytest_test(
    name = "prompt_formatter_test",
    srcs = ["prompt_formatter_test.py"],
    deps = [
        ":prompt_formatter",
    ],
)

py_library(
    name = "dsv2_binks_prompt_formatter",
    srcs = [
        "dsv2_binks_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        ":structured_binks_prompt_formatter",
        ":tokenized_string_formatter",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "dsv2_binks_prompt_formatter_test",
    srcs = ["dsv2_binks_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":dsv2_binks_prompt_formatter",
        ":prompt_formatter",
    ],
)

py_library(
    name = "tokenized_string_formatter",
    srcs = [
        "tokenized_string_formatter.py",
    ],
    visibility = [
        "//base/prompt_format_chat/legacy_binks:__subpackages__",
    ],
    deps = [
        ":prompt_formatter",
        "//base/prompt_format:common",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "tokenized_string_formatter_test",
    srcs = ["tokenized_string_formatter_test.py"],
    deps = [
        ":prompt_formatter",
        ":tokenized_string_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "structured_binks_prompt_formatter",
    srcs = [
        "structured_binks_prompt_formatter.py",
    ],
    visibility = [
        "//base/prompt_format_postprocess:__subpackages__",
        "//base/prompt_format_router:__subpackages__",
        "//services/chat_host/server/prompt_format:__pkg__",
    ],
    deps = [
        ":prompt_formatter",
        "//base/prompt_format:util",
        "//base/prompt_format_chat/lib:chat_history_builder",
        "//base/prompt_format_chat/lib:retrieval_section_prompt_formatter_v2",
        "//base/prompt_format_chat/lib:rules_prompt_builder",
        "//base/prompt_format_chat/lib:selected_code_prompt_formatter_v2",
        "//base/prompt_format_chat/lib:system_prompts",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "structured_binks_prompt_formatter_v2",
    srcs = [
        "structured_binks_prompt_formatter_v2.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/prompt_format:util",
        "//base/prompt_format_chat/lib:chat_history_builder",
        "//base/prompt_format_chat/lib:retrieval_section_prompt_formatter_v3",
        "//base/prompt_format_chat/lib:rules_prompt_builder",
        "//base/prompt_format_chat/lib:selected_code_prompt_formatter_v2",
        "//base/prompt_format_chat/lib:system_prompts",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "agent_prompt_usage_metrics",
    srcs = [
        "agent_prompt_usage_metrics.py",
    ],
    deps = [
        requirement("prometheus_client"),
    ],
)

py_library(
    name = "structured_binks_agent_prompt_formatter",
    srcs = [
        "structured_binks_agent_prompt_formatter.py",
    ],
    deps = [
        ":agent_prompt_usage_metrics",
        ":prompt_formatter",
        "//base/prompt_format_chat/lib:chat_history_builder",
        "//base/prompt_format_chat/lib:edit_events_lib",
        "//base/prompt_format_chat/lib:ide_state_utils",
        "//base/prompt_format_chat/lib:rules_prompt_builder",
        "//base/prompt_format_chat/lib:selected_code_prompt_formatter_v2",
        "//base/prompt_format_chat/lib:system_prompts",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/prompt_format_chat/lib:tool_definitions",
    ],
)

pytest_test(
    name = "structured_binks_prompt_formatter_test",
    srcs = ["structured_binks_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":prompt_formatter",
        ":structured_binks_prompt_formatter",
    ],
)

pytest_test(
    name = "structured_binks_prompt_formatter_v2_test",
    srcs = ["structured_binks_prompt_formatter_v2_test.py"],
    deps = [
        ":conftest",
        ":prompt_formatter",
        ":structured_binks_prompt_formatter_v2",
    ],
)

pytest_test(
    name = "structured_binks_agent_prompt_formatter_test",
    srcs = ["structured_binks_agent_prompt_formatter_test.py"],
    deps = [
        ":agent_prompt_usage_metrics",
        ":prompt_formatter",
        ":structured_binks_agent_prompt_formatter",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/prompt_format_chat/lib:token_counter_claude",
        "//base/third_party_clients:mock_anthropic_client",
        "//base/tokenizers",
    ],
)

py_library(
    name = "tokenized_llama_binks_prompt_formatter",
    srcs = [
        "tokenized_llama_binks_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        ":structured_binks_prompt_formatter",
        ":tokenized_llama_prompt_formatter",
        ":tokenized_string_formatter",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "tokenized_llama_binks_prompt_formatter_test",
    srcs = ["tokenized_llama_binks_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":prompt_formatter",
        ":tokenized_llama_binks_prompt_formatter",
        "//base/diff_utils",
        "//base/tokenizers",
    ],
)

py_library(
    name = "gemini_binks_prompt_formatter",
    srcs = [
        "gemini_binks_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        ":structured_binks_prompt_formatter",
        "//base/prompt_format:common",
        "//base/prompt_format_chat/lib:token_counter_vertex",
        "//base/tokenizers",
    ],
)

py_library(
    name = "claude_binks_prompt_formatter",
    srcs = [
        "claude_binks_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        ":structured_binks_prompt_formatter",
        "//base/prompt_format:common",
        "//base/prompt_format_chat/lib:token_counter_claude",
        "//base/tokenizers",
    ],
)

py_library(
    name = "generate_commit_message_prompt_formatter",
    srcs = [
        "generate_commit_message_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/prompt_format_chat/lib:system_prompts",
        "//base/tokenizers",
    ],
)

py_library(
    name = "smart_paste_prompt_formatter",
    srcs = [
        "smart_paste_prompt_formatter.py",
    ],
    visibility = [
        "//base/prompt_format_smart_paste:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":prompt_formatter",
        ":structured_binks_prompt_formatter",
    ],
)

pytest_test(
    name = "smart_paste_prompt_formatter_test",
    srcs = ["smart_paste_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":prompt_formatter",
        ":smart_paste_prompt_formatter",
        "//base/prompt_format_chat/lib:token_counter_claude",
    ],
)

py_library(
    name = "instruction_prompt_formatter",
    srcs = [
        "instruction_prompt_formatter.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":prompt_formatter",
        ":structured_binks_prompt_formatter",
    ],
)

pytest_test(
    name = "instruction_prompt_formatter_test",
    srcs = ["instruction_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":instruction_prompt_formatter",
        ":prompt_formatter",
        "//base/prompt_format_chat/lib:token_counter_claude",
    ],
)

pytest_test(
    name = "gemini_binks_prompt_formatter_test",
    srcs = ["gemini_binks_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":gemini_binks_prompt_formatter",
    ],
)

pytest_test(
    name = "generate_commit_message_prompt_formatter_test",
    srcs = ["generate_commit_message_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":generate_commit_message_prompt_formatter",
        ":prompt_formatter",
        ":tokenized_llama_binks_prompt_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "prompt_format_chat",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":claude_binks_prompt_formatter",
        ":dsv2_binks_prompt_formatter",
        ":gemini_binks_prompt_formatter",
        ":generate_commit_message_prompt_formatter",
        ":prompt_formatter",
        ":structured_binks_agent_prompt_formatter",
        ":structured_binks_prompt_formatter",
        ":structured_binks_prompt_formatter_v2",
        ":tokenized_llama_binks_prompt_formatter",
        "//base/prompt_format_chat/legacy_binks:binks_llama3_prompt_formatter",
        "//base/prompt_format_chat/legacy_binks:binks_prompt_formatter",
        "//base/prompt_format_chat/lib:token_counter_claude",
        "//base/third_party_clients/token_counter",
    ],
)

py_library(
    name = "tokenized_llama_prompt_formatter",
    srcs = [
        "tokenized_llama_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        ":tokenized_string_formatter",
        "//base/prompt_format_chat/lib:token_counter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "tokenized_llama_prompt_formatter_test",
    srcs = ["tokenized_llama_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":prompt_formatter",
        ":tokenized_llama_prompt_formatter",
        "//base/diff_utils",
        "//base/test_utils:testing_utils",
        "//base/tokenizers",
    ],
)

py_library(
    name = "tokenized_qwen_prompt_formatter",
    srcs = [
        "tokenized_qwen_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":prompt_formatter",
        ":tokenized_string_formatter",
        "//base/prompt_format:common",
        "//base/tokenizers:qwen25coder_tokenizer",
    ],
)

pytest_test(
    name = "tokenized_qwen_prompt_formatter_test",
    srcs = ["tokenized_qwen_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":prompt_formatter",
        ":tokenized_qwen_prompt_formatter",
        "//base/test_utils:testing_utils",
        "//base/tokenizers:qwen25coder_tokenizer",
    ],
)
