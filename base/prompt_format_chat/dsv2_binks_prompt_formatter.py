import copy
import dataclasses
from textwrap import dedent
from typing import Optional

from base.prompt_format.common import (
    get_request_message_as_text,
    get_response_message_as_text,
)
from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    TokenizedChatPromptFormatter,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.tokenized_string_formatter import TokenizedStringFormatter
from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer

BUFFER_SIZE_FOR_SPEC_TOKENS = 100


class DeepSeekCoderV2BinksChatPromptFormatter(TokenizedChatPromptFormatter):
    """The class formats tokenized prompts for the Binks DeepSeekCoderV2-based chat model.

    Under the hood, it uses base/prompt_format_chat/structured_binks_prompt_formatter.py
    to format the prompt, and then tokenizes it, adding special token delimiters where needed.

    Below is how we tokenize the prompt. All new lines and spaces are explicit. Whitespace for
    readability only:

    ==============================================================
    <｜begin▁of▁sentence｜>
    [if system prompt:]
        [insert system prompt]\n\n
    [for conversation turn:]
        User:[space][insert user message]\n\n
        Assistant:[space][insert assistant message]<|end_of_sentence|>
    User:[space][insert user message]\n\n
    Assistant:[end of prompt, no space, no additional tokens]
    ==============================================================
    """

    def __init__(
        self,
        tokenizer: DeepSeekCoderV2Tokenizer,
        token_apportionment: Optional[ChatTokenApportionment] = None,
    ):
        self.tokenizer = tokenizer
        self.token_counter = TokenizerBasedTokenCounter(tokenizer)
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = ChatTokenApportionment(
                path_len=256,
                prefix_len=1536,
                chat_history_len=1536,
                suffix_len=1024,
                max_prompt_len=16384 - 4096,  # 4096 represents the max output tokens
                # Deprecated fields
                message_len=-1,
                selected_code_len=-1,
            )
        self.token_apportionment = token_apportionment

        # Add a buffer of BUFFER_SIZE_FOR_SPEC_TOKENS tokens for spec
        # tokens. It's okay if we go a little over it.
        structured_token_apportionment = copy.deepcopy(token_apportionment)
        structured_token_apportionment = dataclasses.replace(
            structured_token_apportionment,
            max_prompt_len=token_apportionment.max_prompt_len
            - BUFFER_SIZE_FOR_SPEC_TOKENS,
        )

        self.structured_prompt_formatter = StructuredBinksPromptFormatter.create(
            self.token_counter,
            token_apportionment,
        )

        assert (
            token_apportionment.message_len == -1
        ), "The message length should be -1 because it is a deprecate field not used in this formatter."
        assert (
            token_apportionment.selected_code_len == -1
        ), "The selected code length should be -1 because it is a deprecate field not used in this formatter."

        self.token_apportionment = token_apportionment

        self.special_tokens_values = {
            "bos_token": list(self.tokenizer.special_tokens.begin_sequence),
            "eod_token": [self.tokenizer.special_tokens.eod_token],
        }

        self.system_prompt_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {system_prompt}

            """),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )
        self.chat_history_exchange_message_formatter = TokenizedStringFormatter(
            template=dedent("""\
            User: {user_message}

            Assistant: {assistant_message}{eod_token}"""),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )
        self.cur_message_formatter = TokenizedStringFormatter(
            template=dedent("""\
            User: {user_message}

            Assistant:"""),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )

    def format_prompt(self, prompt_input: ChatPromptInput) -> TokenizedChatPromptOutput:
        """Format prompt for Binks DeepSeekCoderV2-based code chat model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """
        structured_prompt = self.structured_prompt_formatter.format_prompt(prompt_input)

        flattened_prompt: list[int] = list(self.tokenizer.special_tokens.begin_sequence)

        if structured_prompt.system_prompt:
            flattened_prompt.extend(
                self.system_prompt_formatter.format(
                    values={
                        "system_prompt": self.tokenizer.tokenize_safe(
                            structured_prompt.system_prompt or ""
                        )
                    }
                )
            )

        for message in structured_prompt.chat_history:
            flattened_prompt.extend(
                self.chat_history_exchange_message_formatter.format(
                    values={
                        "user_message": self.tokenizer.tokenize_safe(
                            get_request_message_as_text(message.request_message)
                        ),
                        "assistant_message": self.tokenizer.tokenize_safe(
                            get_response_message_as_text(message.response_text)
                        ),
                    }
                )
            )

        flattened_prompt.extend(
            self.cur_message_formatter.format(
                values={
                    "user_message": self.tokenizer.tokenize_safe(
                        get_request_message_as_text(structured_prompt.message)
                    )
                }
            )
        )

        return TokenizedChatPromptOutput(
            flattened_prompt, structured_prompt.retrieved_chunks_in_prompt
        )
