"""Module containing prompt formatting logic for different code chat models."""

import logging
import typing
from typing import Literal

from base.prompt_format_chat.claude_binks_prompt_formatter import (
    ClaudeBinksChatPromptFormatter,
)
from base.prompt_format_chat.dsv2_binks_prompt_formatter import (
    DeepSeekCoderV2BinksChatPromptFormatter,
)
from base.prompt_format_chat.gemini_binks_prompt_formatter import (
    GeminiBinksChatPromptFormatter,
)
from base.prompt_format_chat.generate_commit_message_prompt_formatter import (
    GenerateCommitMessagePromptFormatter,
)
from base.prompt_format_chat.legacy_binks.binks_llama3_prompt_formatter import (
    BinksLlama3ChatPromptFormatter,
)
from base.prompt_format_chat.legacy_binks.binks_prompt_formatter import (
    BinksChatPromptFormatter,
)
from base.prompt_format_chat.lib.system_prompts import (
    get_agent_memories_prompt_formatter_v1,
    get_agent_system_prompt_formatter_task_list_v1,
    get_agent_system_prompt_formatter_v1,
    get_agent_system_prompt_formatter_v2,
    get_agent_system_prompt_formatter_v4,
    get_agent_system_prompt_formatter_v6,
    get_agent_system_prompt_formatter_v7,
    get_agent_system_prompt_formatter_gpt5_v1,
    get_agent_system_prompt_formatter_v8,
    get_agent_system_prompt_formatter_v9,
    get_binks_with_codeblocks_xml_system_prompt_formatter,
    get_claude_prompt_formatter_v8,
    get_claude_prompt_formatter_v11,
    get_claude_prompt_formatter_v12,
    get_claude_prompt_formatter_v14,
    get_claude_prompt_formatter_v15,
    get_claude_with_codeblocks_xml_and_suggested_questions_system_prompt_formatter,
    get_claude_with_codeblocks_xml_system_prompt_formatter,
    get_openai_prompt_formatter_v3,
    get_openai_prompt_formatter_v4,
    get_openai_prompt_formatter_v5,
    get_simple_binks_system_prompt_formatter,
)
from base.prompt_format_chat.lib.token_counter import (
    RoughTokenCounter,
    TokenCounter,
    TokenizerBasedTokenCounter,
)
from base.prompt_format_chat.lib.token_counter_claude import ClaudeTokenCounter
from base.prompt_format_chat.lib.token_counter_vertex import VertexTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptFormatter,
    ChatTokenApportionment,
    GenerateCommitMessageTokenApportionment,
    StructToTokensPromptFormatter,
    StructuredChatPromptFormatter,
)
from base.prompt_format_chat.structured_binks_agent_prompt_formatter import (
    StructuredBinksAgentPromptFormatter,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.structured_binks_prompt_formatter_v2 import (
    StructuredBinksPromptFormatterV2,
)
from base.prompt_format_chat.tokenized_llama_binks_prompt_formatter import (
    TokenizedLlama3BinksPromptFormatter,
)
from base.prompt_format_chat.tokenized_llama_prompt_formatter import (
    StructToTokensLlama3PromptFormatter,
)
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer
from base.tokenizers.tokenizer import Tokenizer

PromptFormatterName = Literal[
    "structured-binks-gemini-1.5-pro-001",
    "structured-binks-gemini-1.5-flash-001",
    "binks-gemini-2.0-flash-exp",
    "binks-gemini-2.0-flash-exp-v2",
    "binks-gemini-v9",
    "binks-grok-v3",
    "binks-grok-v4",
    "structured-binks-claude",
    "binks-openai",
    "binks-openai-v2",
    "binks-openai-v3-o1",
    "binks-openai-v3-o1-mini",
    "binks-openai-v4-o1",
    "binks-openai-v4-o1-mini",
    "binks-openai-v5-o1",
    "binks-openai-v5-o1-mini",
    "binks-openai-v6-gpt4-1",
    "binks-openai-v6-o3",
    "binks-kimi-k2",
    "binks-openai-v6-o4-mini",
    "binks-openai-v7-gpt5",
    "binks-deepseek-v3-v1",
    "binks-deepseek-r1-v1",
    "binks-deepseek-r1-v2",
    "binks-qwen2-5-coder-v1",
    "binks-qwq-v1",
    "binks-qwq-v2",
    "binks-claude-v2",
    "binks-claude-v3",
    "binks-claude-v4",
    "binks-claude-v7",
    "binks-claude-v8",
    "binks-claude-v11",
    "binks-claude-v11-1",
    "binks-claude-v12",
    "binks-claude-v12-1",
    "binks-claude-v13",
    "binks-claude-v14",
    "binks-claude-v15",
    "binks-claude-v16",
    "binks-claude-v17",
    "binks-claude-v18",
    "agent-binks-claude-v1",
    "agent-binks-claude-v2",
    "agent-binks-claude-v3",
    "agent-binks-claude-v4",
    "agent-binks-claude-v5",
    "agent-binks-claude-v6",
    "agent-binks-claude-v7",
    "agent-binks-claude-v8",
    "agent-binks-claude-v9",
    "agent-binks-memories-v1",
    "agent-binks-claude-opus-v5",
    "agent-binks-claude-opus-v7",
    "agent-binks-claude-opus-v8",
    "agent-binks-gemini",
    "agent-binks-gemini-v7",
    "agent-binks-gemini-v8",
    "agent-binks-gemini-flash-v7",
    "agent-binks-gemini-flash-v8",
    "agent-binks-gpt4-1-v7",
    "agent-binks-gpt4-1-v8",
    "agent-binks-gpt5-v7",
    "agent-binks-o3-v7",
    "agent-binks-o3-v8",
    "agent-binks-o4-mini-v7",
    "agent-binks-o4-mini-v8",
    "agent-binks-grok-swe-v7",
    "agent-binks-grok-swe-v8",
    "agent-binks-grok3-5-code-v7",
    "agent-binks-grok3-5-code-v8",
    "agent-binks-grok4-v7",
    "agent-binks-grok4-v8",
    "agent-binks-kimi-k2-v7",
    "agent-binks-qwen3-coder-v7",
    "structured_binks_llama3_instruct",
    "simple-binks",
    "agent-binks-task-list-v1",
]


def get_token_counter_by_prompt_formatter_name(
    name: PromptFormatterName,
) -> TokenCounter:
    """Returns the token counter by prompt formatter name."""
    if name == "structured_binks_llama3_instruct":
        tokenizer = create_tokenizer_by_name("llama3_instruct")
        return TokenizerBasedTokenCounter(tokenizer)
    elif name == "structured-binks-gemini-1.5-pro-001":
        return VertexTokenCounter("gemini-1.5-pro-001")
    elif name == "structured-binks-gemini-1.5-flash-001":
        return VertexTokenCounter("gemini-1.5-flash-001")
    elif (
        name == "simple-binks"
        or name.startswith("binks-gemini")
        or name.startswith("agent-binks-gemini")
    ):
        return VertexTokenCounter("gemini-1.5-flash-001")
    elif (
        name
        in {
            "structured-binks-claude",
            "agent-binks-task-list-v1",
        }
        or name.startswith("binks-claude")
        or name.startswith("agent-binks-claude")
    ):
        return ClaudeTokenCounter()
    elif name == "binks-kimi-k2" or name == "agent-binks-kimi-k2-v7":
        # Kmi-k2 uses the same tokenizer as OpenAI models
        return RoughTokenCounter()
    elif name == "agent-binks-qwen3-coder-v7":
        return RoughTokenCounter()
    else:
        return RoughTokenCounter()


def get_structured_chat_prompt_formatter_by_name(
    name: PromptFormatterName,
    token_apportionment: typing.Optional[ChatTokenApportionment] = None,
) -> StructuredChatPromptFormatter:
    """Returns the prompt formatter by name."""
    token_counter = get_token_counter_by_prompt_formatter_name(name)
    if name == "structured-binks-gemini-1.5-pro-001":
        return GeminiBinksChatPromptFormatter("gemini-1.5-pro-001", token_apportionment)
    elif name == "structured-binks-gemini-1.5-flash-001":
        return GeminiBinksChatPromptFormatter(
            "gemini-1.5-flash-001", token_apportionment
        )
    elif name == "binks-gemini-2.0-flash-exp":
        return GeminiBinksChatPromptFormatter(
            "gemini-2.0-flash-exp", token_apportionment
        )
    elif name == "binks-gemini-2.0-flash-exp-v2":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_binks_with_codeblocks_xml_system_prompt_formatter,
        )
    elif name == "binks-gemini-v9":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Gemini 2.5 Pro",
                creator="Google",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-grok-v3":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Grok SWE",
                creator="xAI",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-grok-v4":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Grok 4",
                creator="xAI",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "structured-binks-claude":
        return StructuredBinksPromptFormatter.create(token_counter, token_apportionment)
    elif name == "binks-claude-v2":
        return ClaudeBinksChatPromptFormatter(token_apportionment)
    elif name == "binks-claude-v3":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_binks_with_codeblocks_xml_system_prompt_formatter,
        )
    elif name == "binks-claude-v4":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_with_codeblocks_xml_system_prompt_formatter,
        )
    elif name == "binks-claude-v7":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_with_codeblocks_xml_and_suggested_questions_system_prompt_formatter,
        )
    elif name == "binks-claude-v8":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_prompt_formatter_v8,
        )
    elif name == "binks-claude-v11":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_prompt_formatter_v11,
            retrieval_section_version=3,
        )
    elif name == "binks-claude-v11-1":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_prompt_formatter_v11,
            retrieval_section_version=2,
        )
    elif name == "binks-claude-v13":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_prompt_formatter_v11,
            retrieval_section_version=4,
        )
    elif name == "binks-claude-v12":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_prompt_formatter_v12,
            retrieval_section_version=3,
        )
    elif name == "binks-claude-v12-1":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_claude_prompt_formatter_v12,
            retrieval_section_version=2,
        )
    elif name == "binks-claude-v14":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v14(
                model_name="Claude 3.7 Sonnet",
                creator="Anthropic",
                token_counter=token_counter,
            ),
            retrieval_section_version=2,
        )
    elif name == "binks-claude-v15":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Claude 3.7 Sonnet",
                creator="Anthropic",
                token_counter=token_counter,
            ),
            retrieval_section_version=2,
        )
    elif name == "binks-claude-v16":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Claude 3.7 Sonnet",
                creator="Anthropic",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-claude-v17":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Claude 3.7 Sonnet",
                creator="Anthropic",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-claude-v18":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Claude Sonnet 4",
                creator="Anthropic",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-claude-opus-v19":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="Claude Opus 4.1",
                creator="Anthropic",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "agent-binks-claude-v1":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude 3.7 Sonnet",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v1,
            retrieval_section_version=5,
        )
    elif name == "agent-binks-claude-v2":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude 3.7 Sonnet",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v2,
            retrieval_section_version=5,
        )
    elif name == "agent-binks-claude-v3":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude 3.7 Sonnet",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v2,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-v4":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="latest Claude Sonnet",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v4,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-v5":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Sonnet 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v4,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-v6":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Sonnet 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v6,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Sonnet 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Sonnet 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-memories-v1":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Sonnet 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_memories_prompt_formatter_v1,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-v9":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Sonnet 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v9,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-opus-v5":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Opus 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v4,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-opus-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Opus 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-opus-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Opus 4",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-claude-opus-v9":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude Opus 4.1",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-gemini":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Gemini 2.5 Pro",
            creator="Google",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v2,
            retrieval_section_version=5,
        )
    elif name == "agent-binks-gemini-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Gemini 2.5 Pro",
            creator="Google",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-gemini-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Gemini 2.5 Pro",
            creator="Google",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-gemini-flash-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Gemini 2.5 Flash",
            creator="Google",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-gemini-flash-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Gemini 2.5 Flash",
            creator="Google",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-gpt4-1-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="GPT 4.1",
            creator="OpenAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-gpt4-1-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="GPT 4.1",
            creator="OpenAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-gpt5-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="GPT 5",
            creator="OpenAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_gpt5_v1,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-o3-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="o3",
            creator="OpenAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-o3-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="o3",
            creator="OpenAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-o4-mini-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="o4 mini",
            creator="OpenAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-o4-mini-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="o4 mini",
            creator="OpenAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-grok-swe-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Grok SWE",
            creator="xAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-grok-swe-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Grok SWE",
            creator="xAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-grok3-5-code-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Grok 3.5 Code",
            creator="xAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-grok3-5-code-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Grok 3.5 Code",
            creator="xAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-grok4-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Grok 4",
            creator="xAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-grok4-v8":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Grok 4",
            creator="xAI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v8,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-kimi-k2-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Kimi K2",
            creator="Moonshot AI",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-qwen3-coder-v7":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Qwen3 Coder",
            creator="Alibaba",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_v7,
            retrieval_section_version=5,
            use_fixed_budget_selected_code=True,
        )
    elif name == "agent-binks-task-list-v1":
        return StructuredBinksAgentPromptFormatter.create(
            token_counter,
            model_name="Claude 3.7 Sonnet",
            creator="Anthropic",
            token_apportionment=token_apportionment,
            system_prompt_factory=get_agent_system_prompt_formatter_task_list_v1,
            retrieval_section_version=5,
        )
    elif name == "binks-openai":
        return StructuredBinksPromptFormatter.create(token_counter, token_apportionment)
    elif name == "binks-openai-v2":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_binks_with_codeblocks_xml_system_prompt_formatter,
        )
    elif name == "binks-openai-v3-o1":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v3(
                model_name="o1", creator="OpenAI", token_counter=token_counter
            ),
        )
    elif name == "binks-openai-v3-o1-mini":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v3(
                model_name="o1 mini", creator="OpenAI", token_counter=token_counter
            ),
        )
    elif name == "binks-openai-v3-o3-mini":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v3(
                model_name="o3 mini", creator="OpenAI", token_counter=token_counter
            ),
        )
    elif name == "binks-openai-v4-o1":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v4(
                model_name="o1", creator="OpenAI", token_counter=token_counter
            ),
        )
    elif name == "binks-openai-v4-o1-mini":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v4(
                model_name="o1 mini", creator="OpenAI", token_counter=token_counter
            ),
        )
    elif name == "binks-openai-v5-o1":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v5(
                model_name="o1", creator="OpenAI", token_counter=token_counter
            ),
        )
    elif name == "binks-openai-v5-o1-mini":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v5(
                model_name="o1 mini", creator="OpenAI", token_counter=token_counter
            ),
        )
    elif name == "binks-openai-v6-gpt4-1":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="GPT-4.1",
                creator="OpenAI",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-openai-v6-o3":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="o3",
                creator="OpenAI",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-openai-v6-o4-mini":
        return StructuredBinksPromptFormatterV2.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_claude_prompt_formatter_v15(
                model_name="o4 mini",
                creator="OpenAI",
                token_counter=token_counter,
            ),
            retrieval_section_version=6,
        )
    elif name == "binks-openai-v7-gpt5":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_binks_with_codeblocks_xml_system_prompt_formatter,
        )
    elif name == "binks-kimi-k2":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_binks_with_codeblocks_xml_system_prompt_formatter,
        )
    elif name == "binks-deepseek-v3-v1":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v3(
                model_name="DeepSeek-V3",
                creator="DeepSeek",
                token_counter=token_counter,
            ),
        )
    elif name in {"binks-deepseek-r1-v1", "binks-deepseek-r1-v2"}:
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v3(
                model_name="DeepSeek-R1",
                creator="DeepSeek",
                token_counter=token_counter,
            ),
        )
    elif name == "binks-qwen2-5-coder-v1":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v3(
                model_name="Qwen 2.5 Coder",
                creator="Alibaba",
                token_counter=token_counter,
            ),
        )
    elif name in {"binks-qwq-v1", "binks-qwq-v2"}:
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=lambda token_counter: get_openai_prompt_formatter_v3(
                model_name="QwQ",
                creator="Alibaba",
                token_counter=token_counter,
            ),
        )
    elif name == "structured_binks_llama3_instruct":
        return StructuredBinksPromptFormatter.create(token_counter, token_apportionment)
    elif name == "simple-binks":
        return StructuredBinksPromptFormatter.create(
            token_counter,
            token_apportionment,
            system_prompt_factory=get_simple_binks_system_prompt_formatter,
        )
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError("Invalid prompt formatter name")


def get_chat_prompt_formatter_by_name(
    name: str,
    tokenizer: Tokenizer,
    token_apportionment: typing.Optional[ChatTokenApportionment] = None,
) -> ChatPromptFormatter:
    """Returns the prompt formatter by name.

    Args:
        name: name of the formatter
        tokenizer: instance of the tokenizer to use.
        token_apportionment: The exact token apportionment of tokens during the prompt formatting. If not set, a default is used.

    If there is no formatter with the given name, an exception is thrown.
    """
    if name == "binks_dsv2":
        assert isinstance(tokenizer, DeepSeekCoderV2Tokenizer), type(tokenizer)
        return DeepSeekCoderV2BinksChatPromptFormatter(tokenizer, token_apportionment)
    elif name == "binks_legacy":
        assert isinstance(tokenizer, DeepSeekCoderInstructTokenizer), type(tokenizer)
        return BinksChatPromptFormatter(tokenizer, token_apportionment)
    elif name == "binks_llama3_legacy":
        assert isinstance(tokenizer, Llama3InstructTokenizer), type(tokenizer)
        return BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)
    elif name == "binks_llama3_tokenized":
        assert isinstance(tokenizer, Llama3InstructTokenizer), type(tokenizer)
        return TokenizedLlama3BinksPromptFormatter(tokenizer, token_apportionment)
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError("Invalid prompt formatter name")


def get_generate_commit_message_prompt_formatter(
    token_counter: TokenCounter | None = None,
    token_apportionment: GenerateCommitMessageTokenApportionment | None = None,
) -> ChatPromptFormatter | None:
    """Returns the commit message prompt formatter."""
    if not token_counter or not token_apportionment:
        return None
    return GenerateCommitMessagePromptFormatter(
        token_counter=token_counter,
        token_apportionment=token_apportionment,
    )


# TODO(arun): We should refactor this class out into a common file as it is intended to
# be task-agnostic.
def get_struct_to_tokens_prompt_formatter_by_name(
    name: str,
    tokenizer: Tokenizer,
) -> StructToTokensPromptFormatter:
    """Returns the prompt formatter by name.

    Args:
        name: name of the formatter
        tokenizer: instance of the tokenizer to use.

    If there is no formatter with the given name, an exception is thrown.
    """
    if name == "llama3":
        assert isinstance(tokenizer, Llama3InstructTokenizer), type(tokenizer)
        return StructToTokensLlama3PromptFormatter(tokenizer)
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError("Invalid prompt formatter name")
