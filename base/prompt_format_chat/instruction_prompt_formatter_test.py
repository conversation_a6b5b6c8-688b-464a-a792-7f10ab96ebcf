"""
Sanity test for instruction prompt formatter.
"""

import pytest
from base.prompt_format.common import Exchange, LineEnding, PromptChunk
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON>ounter
from base.prompt_format_chat.instruction_prompt_formatter import (
    InstructionPromptFormatterV2,
    InstructionPromptInput,
    InstructionTokenApportionment,
)
from base.prompt_format_chat.prompt_formatter import ExceedContextLength

TEN_TOKEN_LINE = "This line should be just about 10 tokens!"
TEN_TOKEN_EXCHANGE = [
    Exchange(
        request_message="Exchange of about",
        response_text="10 tokens",
    )
]


@pytest.mark.parametrize("newline", ["\n", "\r\n"])
def test_instruction_prompt_formatter_v2_w_retrieval(newline):
    """Tests the basic behaviour with retrieved chunks."""
    token_counter = ClaudeTokenCounter()
    instruction_token_apportionment = InstructionTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=1024 * 4,
        suffix_len=1024,
        retrieval_len=1024 * 4,
        max_prompt_len=1024 * 16,
    )

    prompt_formatter = InstructionPromptFormatterV2(
        token_counter, instruction_token_apportionment
    )

    retrieved_chunks = [
        PromptChunk(
            text="# You can aggregate\n# with a maxing\n# function.\n",
            path="src/bar.py",
            char_start=0,
            char_end=10,
            blob_name="src/bar.py",
            origin="dense_retriever",
        ),
        PromptChunk(
            text="# You can aggregate\n# with a pooling function.\n",
            path="src/foo.py",
            char_start=0,
            char_end=10,
            blob_name="src/foo.py",
            origin="dense_retriever",
        ),
    ]
    prompt_input = InstructionPromptInput(
        path="src/example_from.py",
        prefix="from typing import List\n\n".replace("\n", newline),
        suffix="\ndef irrelevant():\n    pass\n".replace("\n", newline),
        selected_code="def quicksort(arr: List[int]) -> List[int]:\n    pass\n".replace(
            "\n", newline
        ),
        instruction="Implement the selected function",
        chat_history=[],
        prefix_begin=0,
        suffix_end=999,
        retrieved_chunks=retrieved_chunks,
        user_guidelines="",
        workspace_guidelines="",
    )

    prompt_output = prompt_formatter.format_prompt(prompt_input)

    assert prompt_output.tools is None
    # Retrieval injected into a new entry in history
    chat_history = list(prompt_output.chat_history)
    assert len(chat_history) == 1
    assert "excerpt from the file" in chat_history[0].request_message
    assert "src/foo.py" in chat_history[0].request_message
    assert "src/bar.py" in chat_history[0].request_message
    assert "You can aggregate" in chat_history[0].request_message
    assert prompt_output.retrieved_chunks_in_prompt == retrieved_chunks
    full_prompt = """I have opened a file `src/example_from.py` and highlighted a part of the code (enclosed in <highlighted_code> tag):
<file path="src/example_from.py">
```
from typing import List

<highlighted_code>
def quicksort(arr: List[int]) -> List[int]:
    pass
</highlighted_code>

def irrelevant():
    pass
```
</file>

Please, rewrite the highlighted region according to the following instruction:
<instruction>
Implement the selected function
</instruction>

Put couple lines of context before and after the highlighted region.

Use this output format:
```
from typing import List

<<<<<<< original
...
=======
...
>>>>>>> updated

def irrelevant():
    pass
```
"""
    assert prompt_output.message == full_prompt
    prefill = """Here's the edited code with the requested format:
```
from typing import List

<<<<<<< original
def quicksort(arr: List[int]) -> List[int]:
    pass
======="""
    assert prompt_output.prefill == prefill

    line_ending = LineEnding.LF if newline == "\n" else LineEnding.CRLF
    assert prompt_output.original_line_ending == line_ending
