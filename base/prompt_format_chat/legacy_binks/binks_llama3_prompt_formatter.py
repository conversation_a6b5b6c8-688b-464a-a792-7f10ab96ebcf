"""The Binks prompt formatter for the code chat."""

from typing import Optional
from textwrap import dedent

from base.prompt_format.common import (
    get_request_message_as_text,
    get_response_message_as_text,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    TokenizedChatPromptFormatter,
    TokenizedChatPromptOutput,
    ChatTokenApportionment,
    Exceed<PERSON>ontext<PERSON>eng<PERSON>,
    Prompt<PERSON>hunk,
    TokenList,
    filter_overlapping_retrieved_chunks,
)
from base.diff_utils.diff_formatter import format_file_changes
from base.prompt_format.util import head_n, trailing_n
from base.prompt_format_chat.tokenized_string_formatter import TokenizedStringFormatter
from base.prompt_format_chat.legacy_binks.retrieval_section_prompt_formatter import (
    RetrievalSectionPromptFormatter,
)
from base.prompt_format_chat.legacy_binks.selected_code_prompt_formatter import (
    SelectedCodePromptFormatter,
)
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer


# NOTE: The ChunkOrigin enum is currently defined in
# services/completion_host/single_model_server/enums.py,
# but we cannot import it here. As a temporary workaround,
# we are using string constants  for user-guided and dense retriever origins.
# If a chunk does not originate from one of these,
# an error will be raised.
USER_GUIDED_RETRIEVER_ORIGIN = "user_guided_retriever"
DENSE_RETRIEVER_ORIGIN = "dense_retriever"


class BinksLlama3ChatPromptFormatter(TokenizedChatPromptFormatter):
    """The class formats prompts for the Binks Llama3-based chat model.

    For prompt structure of the Llama3 models, see:
    https://llama.meta.com/docs/model-cards-and-prompt-formats/meta-llama-3

    Prompt Structure:
    - System Prompt:
        a. System Prompt Header
        b. Retrievals (as needed)
        c. Context code (choose one):
            - Selected Code (if provided, no length limits)
            - Prefix and Suffix
        d. System Prompt Footer
    - Chat History (prioritized based on length limits)
    - Message (included fully, without restrictions)

    Token Budget Allocation Policy:
    - 'Message' and 'Selected Code' are always fully included.
    If their combined length exceeds limits, the operation will fail rather than truncate.
    - A fixed budget is set for prefixes and suffixes.
    - Remaining tokens are first allocated to 'chat_history'.
    If a non-negative limit is set in 'token_apportionment.chat_history_len',
    it will not exceed this value.
    - After 'chat_history', tokens go to 'retrievals'.
    These are filled up to 'max_prompt_tokens' or 'token_apportionment.retrieval_len',
    if non-negative.

    Example of a formatted prompt:
    <|begin_of_text|><|start_header_id|>system<|end_header_id|>

    You are Augment, an AI code assistant.
    Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

    Below are some relevant files from the developer's project.

    Here is the file `src/bar.py`:

    ```
    # You can aggregate
    # with a maxing
    # function.
    ...
    ```

    Here is the file `src/foo.py`:

    ```
    ...
    # You can aggregate
    # with a pooling function.
    ...
    ```

    The developer has file `src/example.py` open in VSCode. Here is the full file:

    ```
    file = pathlib.Path("foo")
    for x in file.open():
    ```

    When answering the developer's questions, please follow these guidelines:

    - Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
    - When referencing a file in your response, always include the FULL file path.
    - If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

    What functions are there in this file?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

    This file has one function, some_function(a,b)<|eot_id|><|start_header_id|>user<|end_header_id|>

    Is this code valid?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

    No, the function some_function is missing an implementation.<|eot_id|><|start_header_id|>user<|end_header_id|>

    How could we make this code run?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

    """

    def __init__(
        self,
        tokenizer: Llama3InstructTokenizer,
        token_apportionment: Optional[ChatTokenApportionment] = None,
    ):
        self.tokenizer = tokenizer
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = ChatTokenApportionment(
                path_len=256,
                message_len=0,
                prefix_len=1024,
                selected_code_len=0,
                chat_history_len=2048,
                suffix_len=1024,
                retrieval_len=0,
                recent_changes_len=0,
                max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
            )
        self.token_apportionment = token_apportionment
        self.selected_code_formatter = SelectedCodePromptFormatter(
            tokenizer=tokenizer,
            max_path_tokens=token_apportionment.path_len,
            max_prefix_tokens=token_apportionment.prefix_len,
            max_suffix_tokens=token_apportionment.suffix_len,
        )

        special_tokens_values = {
            "bos_token": list(self.tokenizer.special_tokens.begin_sequence),
            "eod_token": [self.tokenizer.special_tokens.eod_token],
            "start_header": [self.tokenizer.special_tokens.start_header_id],
            "end_header": [self.tokenizer.special_tokens.end_header_id],
        }

        self.system_prompt_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {bos_token}{start_header}system{end_header}

            You are Augment, an AI code assistant.
            Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

            When answering the developer's questions, please follow these guidelines:

            - Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
            - When referencing a file in your response, always include the FULL file path.
            - If the provided files are not enough to answer a question, politely ask the user to reformulate their question.{eod_token}"""
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )
        self.retrieval_header_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {start_header}user{end_header}

            """
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )
        self.general_retrieval_formatter = RetrievalSectionPromptFormatter(
            dedent(
                """\
                Here is an excerpt from the file `{path}`:

                ```
                {content}
                ```

                """
            ),
            tokenizer=tokenizer,
        )
        self.user_guided_retrieval_formatter = RetrievalSectionPromptFormatter(
            dedent(
                """\
                I currently have the file `{path}` open, and I am actively working on it. Here is an excerpt from it:

                ```
                {content}
                ```

                """
            ),
            tokenizer=tokenizer,
        )
        self.retrieval_footer_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {eod_token}{start_header}assistant{end_header}

            Understood. I'll refer to the excerpts for context, and ignore them for general questions.{eod_token}"""
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )
        self.selected_code_header_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {start_header}user{end_header}

            """
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )
        self.selected_code_footer_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {eod_token}{start_header}assistant{end_header}

            Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.{eod_token}"""
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )
        self.exchange_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {start_header}user{end_header}{context_code}

            {request_message}{eod_token}{start_header}assistant{end_header}

            {response_text}{eod_token}"""
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values | {"context_code": []},
        )
        self.message_header_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {start_header}user{end_header}

            """
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )
        self.message_footer_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {message}{eod_token}{start_header}assistant{end_header}

            """
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )
        self.recent_changes_formatter = TokenizedStringFormatter(
            dedent(
                """\
            {start_header}user{end_header}

            I made the following changes to the code:

            ```
            {changes}```
            {eod_token}{start_header}assistant{end_header}

            Thanks, I'll take the changes into account in my responses.
            {eod_token}"""
            ),
            tokenizer=tokenizer,
            default_values=special_tokens_values,
        )

    def _format_chat_history(
        self,
        prompt_input: ChatPromptInput,
        token_budget: int,
        context_code: Optional[TokenList] = None,
    ) -> tuple[TokenList, bool]:
        """Formats and tokenizes the chat history.
        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.
            token_budget: the total token budget allowed.
            context_code: the tokens for the context (selected code).
                `prompt_input.context_code_exchange_request_id` is a request ID of the exchange to which the context code
                should be added.
        Returns: tuple[
                0. List of tokens,
                1. bool: whether the context code was successfully added to the respective exchange.
            ]
        """
        # Take recent exchanges without going over the total token count allowed
        history_tokens_list: list[TokenList] = []
        total_tokens_len = 0

        reversed_history_list = list(
            reversed([exchange for exchange in prompt_input.chat_history])
        )

        context_succesfully_added = False  # Whether the context code was successfully added to the respective exchange.
        for exchange in reversed_history_list:
            if (
                exchange.request_id is not None
                and exchange.request_id == prompt_input.context_code_exchange_request_id
                and context_code is not None
            ):
                cur_context_code = context_code
            else:
                cur_context_code = []
            exchange_tokens = self.exchange_formatter.format(
                {
                    "request_message": self.tokenizer.tokenize_safe(
                        get_request_message_as_text(exchange.request_message)
                    ),
                    "response_text": self.tokenizer.tokenize_safe(
                        get_response_message_as_text(exchange.response_text)
                    ),
                    "context_code": cur_context_code,
                }
            )

            total_tokens_with_exchange_len = total_tokens_len + len(exchange_tokens)
            if total_tokens_with_exchange_len > token_budget:
                break
            if len(cur_context_code) > 0:
                context_succesfully_added = True
            total_tokens_len = total_tokens_with_exchange_len
            history_tokens_list.append(exchange_tokens)

        # Reverse the order of the history
        history_tokens: TokenList = []
        for exchange_tokens in reversed(history_tokens_list):
            history_tokens.extend(exchange_tokens)
        return history_tokens, context_succesfully_added

    def _format_recent_changes(
        self,
        prompt_input: ChatPromptInput,
        token_budget: int,
    ) -> TokenList:
        if not prompt_input.recent_changes or token_budget <= 0:
            return []

        changes_tokens: TokenList = []

        # Tokenize each change separately until we run out of budget
        for change in prompt_input.recent_changes:
            change_str = format_file_changes(
                [change],
                diff_context_lines=5,
            )
            new_change_tokens = self.tokenizer.tokenize_safe(change_str)
            num_exchange_tokens = self.recent_changes_formatter.format_and_count_tokens(
                {
                    "changes": changes_tokens + new_change_tokens,
                }
            )
            if num_exchange_tokens > token_budget:
                break
            changes_tokens.extend(new_change_tokens)

        exchange_tokens = self.recent_changes_formatter.format(
            {
                "changes": changes_tokens,
            }
        )

        if len(exchange_tokens) > token_budget:
            raise ExceedContextLength(
                f"Recent changes exceed maximum length {token_budget}, has {len(exchange_tokens)} tokens."
            )

        return exchange_tokens

    def _sanity_check_retrieved_chunks(self, retrieved_chunks: list[PromptChunk]):
        """Sanity check retrieval origin."""
        for chunk in retrieved_chunks:
            if chunk.origin not in {
                USER_GUIDED_RETRIEVER_ORIGIN,
                DENSE_RETRIEVER_ORIGIN,
            }:
                raise ValueError(
                    f"Unknown chunk origin: {chunk.origin}. "
                    f"Must be one of {USER_GUIDED_RETRIEVER_ORIGIN} "
                    f"or {DENSE_RETRIEVER_ORIGIN}."
                )

    def _format_retrieval(
        self,
        prompt_input: ChatPromptInput,
        clipped_prefix_tokens: TokenList,
        clipped_suffix_tokens: TokenList,
        token_budget: int,
    ) -> tuple[TokenList, list[PromptChunk]]:
        """Get the tokens for retrieved chunks."""
        retrieved_chunks = list(prompt_input.retrieved_chunks)
        self._sanity_check_retrieved_chunks(retrieved_chunks)

        retrieval_header = self.retrieval_header_formatter.format({})
        retrieval_footer = self.retrieval_footer_formatter.format({})

        # Estimate the number of files user has attached.
        n_user_guided_files = len(
            {
                chunk.blob_name
                for chunk in retrieved_chunks
                if chunk.origin == USER_GUIDED_RETRIEVER_ORIGIN
            }
        )

        # TODO(yury): We need to merge chunks and prefix/suffix together
        # instead of simply filtering the overlapping chunk.
        # Will address that in the follow up PR.
        filtered_retrieved_chunks = filter_overlapping_retrieved_chunks(
            prompt_input,
            self.tokenizer,
            prefix_tokens=clipped_prefix_tokens,
            suffix_tokens=clipped_suffix_tokens,
            retrieved_chunks=prompt_input.retrieved_chunks,
        )
        filtered_retrieved_chunks = list(filtered_retrieved_chunks)

        # First, we include chunks from user-guided retrieval.
        filtered_user_guided_chunks = [
            chunk
            for chunk in filtered_retrieved_chunks
            if chunk.origin == USER_GUIDED_RETRIEVER_ORIGIN
        ]

        user_guided_budget = (
            token_budget - len(retrieval_header) - len(retrieval_footer)
        )
        if self.token_apportionment.retrieval_len_per_each_user_guided_file >= 0:
            # We scale the budget of the user guided retrieval proportionally
            # to the number of files user attached.
            user_guided_budget = min(
                user_guided_budget,
                self.token_apportionment.retrieval_len_per_each_user_guided_file
                * n_user_guided_files,
            )
        if self.token_apportionment.retrieval_len_for_user_guided >= 0:
            user_guided_budget = min(
                user_guided_budget,
                self.token_apportionment.retrieval_len_for_user_guided,
            )

        user_guided_tokens, user_guided_chunks_in_prompt = (
            self.user_guided_retrieval_formatter.format(
                filtered_user_guided_chunks,
                self.token_apportionment.path_len,
                user_guided_budget,
            )
        )

        # We avoid including user-guided files in the general retrieval section
        # to prevent problems. The issue is that different indexers might give us
        # different versions of the same file, which could mess up the results if
        # we merge them.
        # TODO(yury): check if the user-guided and general retrievals are
        # the same by comparing blob names. But this is extra work, so we'll
        # leave that for later.
        user_guided_files = {chunk.path for chunk in user_guided_chunks_in_prompt}

        # Second, we include chunks from the rest of the repository.
        other_retrieval_chunks = []
        for chunk in filtered_retrieved_chunks:
            if (
                chunk.origin != USER_GUIDED_RETRIEVER_ORIGIN
                and chunk.path not in user_guided_files
            ):
                other_retrieval_chunks.append(chunk)

        other_retrieval_header = self.tokenizer.tokenize_safe(
            "Below are some relevant files from my project.\n\n"
        )

        other_budget = (
            token_budget
            - len(other_retrieval_header)
            - len(user_guided_tokens)
            - len(retrieval_header)
            - len(retrieval_footer)
        )
        if self.token_apportionment.retrieval_len >= 0:
            other_budget = min(other_budget, self.token_apportionment.retrieval_len)

        other_retrieval_tokens, other_retrieved_chunks_in_prompt = (
            self.general_retrieval_formatter.format(
                other_retrieval_chunks,
                self.token_apportionment.path_len,
                other_budget,
            )
        )

        if len(other_retrieved_chunks_in_prompt) > 0:
            other_retrieval_tokens = other_retrieval_header + other_retrieval_tokens
        else:
            other_retrieval_tokens = []

        # We first include the user-guided section for better caching.
        retrieval_tokens = (
            retrieval_header
            + user_guided_tokens
            + other_retrieval_tokens
            + retrieval_footer
        )
        retrieved_chunks_in_prompt = (
            user_guided_chunks_in_prompt + other_retrieved_chunks_in_prompt
        )

        if len(retrieved_chunks_in_prompt) == 0:
            # We were not able to add any retrieved chunks to the prompt.
            # So there's no need to add the retrieval section to the prompt.
            return [], []

        return retrieval_tokens, retrieved_chunks_in_prompt

    def _format_selected_code(
        self, prompt_input: ChatPromptInput, token_budget: int
    ) -> tuple[TokenList, TokenList, TokenList]:
        """Get the tokens for selected code."""
        if len(prompt_input.selected_code) == 0:
            return [], [], []

        selected_code_header = self.selected_code_header_formatter.format({})
        selected_code_footer = self.selected_code_footer_formatter.format({})
        context_code_budget = (
            token_budget - len(selected_code_header) - len(selected_code_footer)
        )

        context_code, clipped_prefix_tokens, clipped_suffix_tokens = (
            self.selected_code_formatter.format(
                path=prompt_input.path,
                prefix=prompt_input.prefix,
                suffix=prompt_input.suffix,
                selected_code=prompt_input.selected_code,
                total_token_budget=context_code_budget,
            )
        )
        return (
            context_code,
            clipped_prefix_tokens,
            clipped_suffix_tokens,
        )

    def format_prompt(self, prompt_input: ChatPromptInput) -> TokenizedChatPromptOutput:
        """Format prompt for Binks Llama3-based code chat model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """

        # First, we tokenize parts of the input that are not limited by budget.
        system_prompt = self.system_prompt_formatter.format({})

        message_and_history, clipped_prefix_tokens, clipped_suffix_tokens = (
            self._format_message_and_history(
                prompt_input,
                self.token_apportionment.max_prompt_len - len(system_prompt),
            )
        )

        recent_changes_budget = (
            self.token_apportionment.max_prompt_len
            - len(system_prompt)
            - len(message_and_history)
        )

        recent_changes = self._format_recent_changes(
            prompt_input,
            min(recent_changes_budget, self.token_apportionment.recent_changes_len),
        )

        retrieval_budget = recent_changes_budget - len(recent_changes)
        del recent_changes_budget

        retrieval, retrieved_chunks_in_prompt = self._format_retrieval(
            prompt_input,
            clipped_prefix_tokens=clipped_prefix_tokens,
            clipped_suffix_tokens=clipped_suffix_tokens,
            token_budget=retrieval_budget,
        )

        final_tokens = system_prompt + retrieval + recent_changes + message_and_history

        if len(final_tokens) > self.token_apportionment.max_prompt_len:
            raise ExceedContextLength(
                f"Prompt length {len(final_tokens)} exceeds maximum prompt length "
                f"{self.token_apportionment.max_prompt_len}."
            )

        return TokenizedChatPromptOutput(final_tokens, retrieved_chunks_in_prompt)

    def _format_message_and_history(
        self,
        prompt_input: ChatPromptInput,
        token_budget: int,
    ) -> tuple[TokenList, TokenList, TokenList]:
        """Formats message and history.
        Also, selected code, which goes into the message, history or as a separate conversation turn.
        """
        message_header = self.message_header_formatter.format({})
        message_footer = self.message_footer_formatter.format(
            {
                "message": self.tokenizer.tokenize_safe(
                    get_request_message_as_text(prompt_input.message)
                )
            }
        )
        context_code_budget = token_budget - len(message_header) - len(message_footer)
        context_code, clipped_prefix_tokens, clipped_suffix_tokens = (
            self._format_selected_code(prompt_input, context_code_budget)
        )

        if prompt_input.context_code_exchange_request_id is None:
            # Old clients do not send the context_code_exchange_request_id, so for backwards compatibility,
            # we use the legacy format. I.e. Context code goes into a separate conversation turn.
            message_and_history = self._format_message_and_history_legacy(
                prompt_input, token_budget, message_header, message_footer, context_code
            )
        elif len(prompt_input.selected_code) == 0:
            # No selected code, so everything simple
            message_and_history = self._format_message_and_history_no_context(
                prompt_input,
                token_budget,
                message_header,
                message_footer,
            )
        elif prompt_input.context_code_exchange_request_id == "new":
            # `context_code_exchange_request_id` == "new" means that context code is brand new
            # (i.e. user selected it in the current conversation turn). So we include it in the message.
            message_and_history = (
                self._format_message_and_history_with_context_inside_message(
                    prompt_input,
                    token_budget,
                    message_header,
                    message_footer,
                    context_code,
                )
            )
        else:
            # Context code is from previous exchange, so we try putting it in the history.
            # BUT, if it doesn't fit, we put it as a separate conversation turn.
            message_and_history = (
                self._format_message_and_history_with_context_inside_history(
                    prompt_input,
                    token_budget,
                    message_header,
                    message_footer,
                    context_code,
                )
            )

        return message_and_history, clipped_prefix_tokens, clipped_suffix_tokens

    def _format_message_and_history_legacy(
        self,
        prompt_input: ChatPromptInput,
        token_budget: int,
        message_header: TokenList,
        message_footer: TokenList,
        context_code: TokenList,
    ) -> TokenList:
        """Always put selected code in a separate conversation turn."""
        if len(context_code) > 0:
            selected_code_header = self.selected_code_header_formatter.format({})
            selected_code_footer = self.selected_code_footer_formatter.format({})
            context_code = selected_code_header + context_code + selected_code_footer

        chat_history_budget = (
            token_budget - len(message_header) - len(message_footer) - len(context_code)
        )
        if self.token_apportionment.chat_history_len >= 0:
            chat_history_budget = min(
                chat_history_budget, self.token_apportionment.chat_history_len
            )
        chat_history, _ = self._format_chat_history(prompt_input, chat_history_budget)

        return context_code + chat_history + message_header + message_footer

    def _format_message_and_history_no_context(
        self,
        prompt_input: ChatPromptInput,
        token_budget: int,
        message_header: TokenList,
        message_footer: TokenList,
    ) -> TokenList:
        chat_history_budget = token_budget - len(message_header) - len(message_footer)

        if self.token_apportionment.chat_history_len >= 0:
            chat_history_budget = min(
                chat_history_budget, self.token_apportionment.chat_history_len
            )
        chat_history, _ = self._format_chat_history(prompt_input, chat_history_budget)

        return chat_history + message_header + message_footer

    def _format_message_and_history_with_context_inside_message(
        self,
        prompt_input: ChatPromptInput,
        token_budget: int,
        message_header: TokenList,
        message_footer: TokenList,
        context_code: TokenList,
    ) -> TokenList:
        """Put selected code inside the message."""
        chat_history_budget = (
            token_budget - len(message_header) - len(message_footer) - len(context_code)
        )
        if self.token_apportionment.chat_history_len >= 0:
            chat_history_budget = min(
                chat_history_budget, self.token_apportionment.chat_history_len
            )
        chat_history, _ = self._format_chat_history(prompt_input, chat_history_budget)

        return chat_history + message_header + context_code + message_footer

    def _format_message_and_history_with_context_inside_history(
        self,
        prompt_input: ChatPromptInput,
        token_budget: int,
        message_header: TokenList,
        message_footer: TokenList,
        context_code: TokenList,
    ) -> TokenList:
        """Try putting selected code in the history. If it doesn't fit, put it in a separate conversation turn."""
        selected_code_header = self.selected_code_header_formatter.format({})
        selected_code_footer = self.selected_code_footer_formatter.format({})
        context_code_as_conversation_turn = (
            selected_code_header + context_code + selected_code_footer
        )

        chat_history_budget = token_budget - len(message_header) - len(message_footer)
        if self.token_apportionment.chat_history_len >= 0:
            chat_history_budget = min(
                chat_history_budget, self.token_apportionment.chat_history_len
            ) + len(context_code)
        chat_history, context_succesfully_added = self._format_chat_history(
            prompt_input,
            chat_history_budget,
            context_code,
        )
        if context_succesfully_added:
            return chat_history + message_header + message_footer

        chat_history_budget = (
            token_budget
            - len(message_header)
            - len(message_footer)
            - len(context_code_as_conversation_turn)
        )
        if self.token_apportionment.chat_history_len >= 0:
            chat_history_budget = min(
                chat_history_budget, self.token_apportionment.chat_history_len
            )
        chat_history, _ = self._format_chat_history(prompt_input, chat_history_budget)

        return (
            context_code_as_conversation_turn
            + chat_history
            + message_header
            + message_footer
        )
