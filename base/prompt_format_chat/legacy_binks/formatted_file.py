"""Implements a FormattedFile that combines retrieved chunks from the same file."""

from typing import Iterator
from cachetools import cached
from cachetools.keys import hashkey

import bisect
import dataclasses
from base.prompt_format_chat.prompt_formatter import (
    PromptChunk,
    TokenList,
)
from base.tokenizers.tokenizer import Tokenizer


@dataclasses.dataclass(frozen=True)
class FormattedFile:
    """A class to format and tokenize chunks from the same file.

    This class simplifies formatting the prompt while managing token limits,
    especially when dealing with multiple, potentially overlapping chunks.

    FormattedFile formats the text first, then counts the tokens to accurately
    determine the token count.

    The class allows you to add new chunks and immediately see how they affect
    the total number of tokens. If the increase is too significant, it's easy
    to undo changes, thanks to the class's immutable design; adding a chunk
    returns a new instance of the class.

    The implementation focuses on efficiency, avoiding redundant tokenization
    and minimizing unnecessary copying of objects.

    Note: Currently, the class assumes that chunks do not overlap. This will be
    corrected in a future update.
    """

    tokenizer: Tokenizer
    sorted_chunks: tuple[PromptChunk, ...] = dataclasses.field(default_factory=tuple)

    def get_tokens(self) -> TokenList:
        """Get the tokens for this file."""
        separator = self.tokenizer.tokenize_safe("...\n")
        tokens = []
        prev_chunk_char_end = 0
        prev_chunk_char_start = 0
        for chunk in self.sorted_chunks:
            # confirm sorted invariant
            assert prev_chunk_char_start <= chunk.char_start
            prev_chunk_char_start = chunk.char_start

            # Add tokens for the chunk
            if prev_chunk_char_end <= chunk.char_start:
                chunk_toks = self._get_chunk_tokens_from_cache(
                    chunk.text, chunk.char_start, chunk.char_end, chunk.path
                )
                if prev_chunk_char_end < chunk.char_start:
                    # The separator is a stand-in for the missing code
                    tokens.extend(separator)
                tokens.extend(chunk_toks)
                prev_chunk_char_end = chunk.char_end
            elif prev_chunk_char_end >= chunk.char_end:
                # Nothing to add if the chunk is entirely contained in the previous chunk
                continue
            else:
                # If the chunk is not entirely contained in the previous chunk,
                # we just add the part that is not overlapping
                num_overlapping_chars = prev_chunk_char_end - chunk.char_start
                chunk_toks = self._get_chunk_tokens_from_cache(
                    chunk.text[num_overlapping_chars:],
                    prev_chunk_char_end,
                    chunk.char_end,
                    chunk.path,
                )
                tokens.extend(chunk_toks)
                prev_chunk_char_end = chunk.char_end

        # We don't know whether we have included an entire file,
        # so we add a separator to ensure proper formatting.
        tokens.extend(self.tokenizer.tokenize_safe("..."))
        return tokens

    @cached(
        cache={},
        key=lambda self, text, chunk_char_start, chunk_char_end, chunk_path: hashkey(
            chunk_char_start, chunk_char_end, chunk_path
        ),
    )
    def _get_chunk_tokens_from_cache(
        self, text, chunk_char_start: int, chunk_char_end: int, chunk_path: str
    ) -> list[int]:
        """Cache for tokenization."""
        return self.tokenizer.tokenize_safe(text)

    def __len__(self) -> int:
        """Get the total number of tokens in this file."""
        return len(self.get_tokens())

    def __getitem__(self, index: int) -> int:
        """Get the token at the given index."""
        # NOTE: We add this method to make the class compatible
        # we Sequence[int] protocol for `TokenizedStringFormatter` class
        # in the `base/prompt_format_chat/tokenized_string_formatter.py` file.
        # However, `TokenizedStringFormatter` only uses __len__ and __iter__ method,
        # so we are not implementing __getitem__ for now.
        raise NotImplementedError()

    def __iter__(self) -> Iterator[int]:
        """Get the tokens for this file."""
        return iter(self.get_tokens())

    def get_chunks(self) -> list[PromptChunk]:
        """Get the chunks for this file."""
        return [chunk for chunk in self.sorted_chunks]

    def add_chunk(self, chunk: PromptChunk) -> "FormattedFile":
        """Add a chunk to this file."""
        index = bisect.bisect_left(self.sorted_chunks, chunk)
        return FormattedFile(
            tokenizer=self.tokenizer,
            sorted_chunks=(
                self.sorted_chunks[:index] + (chunk,) + self.sorted_chunks[index:]
            ),
        )
