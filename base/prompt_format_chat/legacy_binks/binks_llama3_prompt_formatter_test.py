"""Tests for the Binks prompt formatter.

pytest base/prompt_format_chat/binks_llama3_prompt_formatter_test.py
"""

from dataclasses import replace

from base import tokenizers
from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File
from base.prompt_format_chat.legacy_binks.binks_llama3_prompt_formatter import (
    BinksLlama3ChatPromptFormatter,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
)
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer

token_apportionment_with_retrieval = ChatTokenApportionment(
    path_len=256,
    message_len=0,
    prefix_len=1024,
    selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
    chat_history_len=2048,
    suffix_len=1024,
    retrieval_len=-1,  # unlimited retrieval
    retrieval_len_per_each_user_guided_file=2000,
    retrieval_len_for_user_guided=3000,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
)


def test_binks_with_history_no_code(example_history_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_history_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

What functions are there in this file?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

This file has one function, some_function(a,b)<|eot_id|><|start_header_id|>user<|end_header_id|>

Is this code valid?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

No, the function some_function is missing an implementation.<|eot_id|><|start_header_id|>user<|end_header_id|>

How could we make this code run?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_binks_prefix_suffix(example_basic_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_basic_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

fix bugs<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_binks_w_retrieval(example_retrieval_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(
        tokenizer, token_apportionment_with_retrieval
    )

    prompt_output = prompter.format_prompt(example_retrieval_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

I have the file `src/example.py` open and has selected part of the code.

Here is the full file:

```
import pathlib
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
    print(x)

```

Here is the selected code:

```
file = pathlib.Path("foo")
for x in file.open():
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.<|eot_id|><|start_header_id|>user<|end_header_id|>

fix bugs<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_binks_w_retrieval_without_retrieval_budget(
    example_retrieval_input: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(tokenizer)

    prompt_output = prompter.format_prompt(example_retrieval_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I have the file `src/example.py` open and has selected part of the code.

Here is the full file:

```
import pathlib
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
    print(x)

```

Here is the selected code:

```
file = pathlib.Path("foo")
for x in file.open():
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.<|eot_id|><|start_header_id|>user<|end_header_id|>

fix bugs<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_binks_w_retrieval_no_code(example_retrieval_no_code_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(
        tokenizer, token_apportionment_with_retrieval
    )

    prompt_output = prompter.format_prompt(example_retrieval_no_code_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    assert prompt == expected_prompt


def test_user_guided_retrieval(
    example_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(
        tokenizer, token_apportionment_with_retrieval
    )

    prompt_output = prompter.format_prompt(example_user_guided_retrieval)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_user_guided_retrieval_same_file_in_diff_retrievals(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(
        tokenizer, token_apportionment_with_retrieval
    )

    prompt_output = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

I currently have the file `src/foo.py` open, and I am actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    assert prompt == expected_prompt


def test_empty_chunks(
    example_empty_chunks: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(
        tokenizer, token_apportionment_with_retrieval
    )

    prompt_output = prompter.format_prompt(example_empty_chunks)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    assert prompt == expected_prompt


def test_example_only_user_guided_retrieval(
    example_only_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = BinksLlama3ChatPromptFormatter(
        tokenizer, token_apportionment_with_retrieval
    )

    prompt_output = prompter.format_prompt(example_only_user_guided_retrieval)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

I currently have the file `src/foo.py` open, and I am actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    assert prompt == expected_prompt


def test_example_only_user_guided_retrieval_no_ug_total_budget(
    example_only_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")

    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    prompt_output = prompter.format_prompt(example_only_user_guided_retrieval)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    assert prompt == expected_prompt


def test_user_guided_retrieval_no_ug_total_budget(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    prompt_output = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    assert prompt == expected_prompt


def test_user_guided_retrieval_no_regular_retrieval_budget(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    prompt_output = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

I currently have the file `src/foo.py` open, and I am actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

What kind of aggregate functions do we have?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
    assert prompt == expected_prompt


def test_selected_code_in_history(example_selected_code_in_history: ChatPromptInput):
    """Tests the case selected code was highlighted on a previous exchange."""

    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    prompt_output = prompter.format_prompt(example_selected_code_in_history)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Hey, Augment!<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Hey!<|eot_id|><|start_header_id|>user<|end_header_id|>I have the file `/path/to/file.py` open and has selected part of the code.

Here is the full file:

```
some_prefix
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
some_suffix
```

Here is the selected code:

```
some_function(a,b)
```



Tell me about this function<|eot_id|><|start_header_id|>assistant<|end_header_id|>

This function that does ...<|eot_id|><|start_header_id|>user<|end_header_id|>

You sure?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Yes, I am.<|eot_id|><|start_header_id|>user<|end_header_id|>

How could we make this code run?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_selected_code_in_message(example_new_selected_code: ChatPromptInput):
    """When selected code is different from previous exchange, it should be in the message."""

    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=100,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    prompt_output = prompter.format_prompt(example_new_selected_code)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Hey, Augment!<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Hey!<|eot_id|><|start_header_id|>user<|end_header_id|>

I have the file `/path/to/file.py` open and has selected part of the code.

Here is the full file:

```
some_prefix
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
some_suffix
```

Here is the selected code:

```
some_function(a,b)
```

How could we make this code run?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_selected_code_in_separate_conversation_turn(
    example_selected_code_in_history: ChatPromptInput,
):
    """When exchange with selected code doesn't fit into history, it should be in a separate conversation turn before history."""

    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=20,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    prompt_output = prompter.format_prompt(example_selected_code_in_history)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I have the file `/path/to/file.py` open and has selected part of the code.

Here is the full file:

```
some_prefix
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
some_suffix
```

Here is the selected code:

```
some_function(a,b)
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.<|eot_id|><|start_header_id|>user<|end_header_id|>

You sure?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Yes, I am.<|eot_id|><|start_header_id|>user<|end_header_id|>

How could we make this code run?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_binks_w_recent_changes(
    example_recent_changes: ChatPromptInput,
):
    """Test formatting with recent changes."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        recent_changes_len=2000,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    prompt_output = prompter.format_prompt(example_recent_changes)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """\
<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I made the following changes to the code:

```
+++ file1.py
@@ -1,2 +1,2 @@
-line 1
+new line 1
 line 2
```
<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Thanks, I'll take the changes into account in my responses.
<|eot_id|><|start_header_id|>user<|end_header_id|>

fix bugs<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt


def test_binks_w_recent_changes_with_budget_truncation(
    example_recent_changes: ChatPromptInput,
):
    """Test formatting with recent changes."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        message_len=0,
        prefix_len=1024,
        selected_code_len=0,  # not used by the BinksLlama3ChatPromptFormatter
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        recent_changes_len=100,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    )
    prompter = BinksLlama3ChatPromptFormatter(tokenizer, token_apportionment)

    assert example_recent_changes.recent_changes
    new_recent_changes = list(example_recent_changes.recent_changes) + [
        Modified(
            File("file2.py", "line 1\nline 2\n"),
            File("file2.py", "new line 1\nline 2\n"),
        ),
        # A very long change that should be truncated
        Modified(
            File("file3.py", "line 1\nline 2\n"),
            File("file3.py", "line 1\nline 2\n" * 1000),
        ),
    ]

    example_recent_changes = replace(
        example_recent_changes, recent_changes=new_recent_changes
    )

    prompt_output = prompter.format_prompt(example_recent_changes)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """\
<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I made the following changes to the code:

```
+++ file1.py
@@ -1,2 +1,2 @@
-line 1
+new line 1
 line 2
+++ file2.py
@@ -1,2 +1,2 @@
-line 1
+new line 1
 line 2
```
<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Thanks, I'll take the changes into account in my responses.
<|eot_id|><|start_header_id|>user<|end_header_id|>

fix bugs<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

    assert prompt == expected_prompt
