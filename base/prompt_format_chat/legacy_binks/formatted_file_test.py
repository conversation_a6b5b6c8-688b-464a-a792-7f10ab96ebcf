"""Test the FormattedFile class."""

import pytest
from base.prompt_format_chat.legacy_binks.formatted_file import FormattedFile
from base.prompt_format.common import PromptChunk
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer


DUMMY_PATH = "file.txt"


@pytest.fixture(name="formatted_file")
def fixture_formatted_file():
    """Setup the formatted file with llama3 tokenizer."""
    tokenizer = Llama3InstructTokenizer()
    return FormattedFile(tokenizer=tokenizer)


def test_get_tokens_empty(formatted_file):
    """Test empty FormattedFile."""
    assert formatted_file.tokenizer.detokenize(formatted_file.get_tokens()) == "..."
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_add_chunk(formatted_file):
    """Test adding a single chunk."""
    chunk = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk)
    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\n..."
    )
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_consecutive_chunks(formatted_file):
    """Test adding multiple chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=12, char_end=20, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\n..."
    )
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_consecutive_chunks_reversed_order(formatted_file):
    """Test adding multiple chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=12, char_end=20, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk1)
    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\n..."
    )
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_consecutive_chunks_non_zero_start(formatted_file):
    """Test adding chunks with non-zero start."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=5, char_end=17, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=17, char_end=25, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "...\nHello World!\nFoo Bar!\n..."
    )
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_non_consecutive_chunks_non_zero_start(formatted_file):
    """Test adding chunks with non-zero start."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=5, char_end=17, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="Foo Bar!\n", char_start=20, char_end=25, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "...\nHello World!\n...\nFoo Bar!\n..."
    )
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_overlapping_chunks(formatted_file):
    """Test adding overlapping chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="Foo Bar!\nAfter foo bar!\n", char_start=13, char_end=37, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)

    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\n..."
    ), formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
    assert len(formatted_file) == len(formatted_file.get_tokens())

    formatted_file = formatted_file.add_chunk(chunk3)

    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    ), formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_overlapping_chunk_extension(formatted_file):
    """Test adding overlapping chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="World!\nFoo Bar!\nAfter foo bar!\n",
        char_start=6,
        char_end=37,
        path=DUMMY_PATH,
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk3)

    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    ), formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_overlapping_chunk_extension_out_of_order_insertion(formatted_file):
    """Test adding overlapping chunks."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="World!\nFoo Bar!\nAfter foo bar!\n",
        char_start=6,
        char_end=37,
        path=DUMMY_PATH,
    )
    formatted_file = formatted_file.add_chunk(chunk3)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk1)

    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    ), formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_overlapping_chunks_out_order_insertion(formatted_file):
    """Test adding overlapping chunks but out of order insertion."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="World!\nFoo Bar!\n", char_start=6, char_end=22, path=DUMMY_PATH
    )
    chunk3 = PromptChunk(
        text="Foo Bar!\nAfter foo bar!\n", char_start=13, char_end=37, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk3)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk1)

    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\nAfter foo bar!\n..."
    ), formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_add_chunks_with_empty_text(formatted_file):
    """Test adding chunks with empty text."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=13, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(text="", char_start=13, char_end=13, path=DUMMY_PATH)
    chunk3 = PromptChunk(text="Foo Bar!\n", char_start=13, char_end=22, path=DUMMY_PATH)
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    formatted_file = formatted_file.add_chunk(chunk3)
    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\nFoo Bar!\n..."
    )
    assert len(formatted_file) == len(formatted_file.get_tokens())


def test_add_chunks_with_same_text(formatted_file):
    """Test adding chunks with the same text."""
    chunk1 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    chunk2 = PromptChunk(
        text="Hello World!\n", char_start=0, char_end=12, path=DUMMY_PATH
    )
    formatted_file = formatted_file.add_chunk(chunk1)
    formatted_file = formatted_file.add_chunk(chunk2)
    assert (
        formatted_file.tokenizer.detokenize(formatted_file.get_tokens())
        == "Hello World!\n..."
    )
    assert len(formatted_file) == len(formatted_file.get_tokens())
