"""Tests for the Binks DBRX-based prompt formatter."""

from base import tokenizers
from base.prompt_format_chat.legacy_binks.binks_dbrx_prompt_formatter import (
    BinksDbrxChatPromptFormatter,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
)
from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer

token_apportionment_with_retrieval = ChatTokenApportionment(
    path_len=256,
    message_len=0,
    prefix_len=1024,
    selected_code_len=0,
    chat_history_len=2048,
    suffix_len=1024,
    retrieval_len=2048,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
)


def test_binks_with_history_no_code(example_history_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("dbrx_instruct")
    if not isinstance(tokenizer, DBRXInstructTokenizer):
        raise ValueError("Tokenizer must be a DBRXInstructTokenizer.")
    prompter = BinksDbrxChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_history_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|im_start|>system
You are Augment, an AI code assistant integrated into the VSCode IDE.
Your role is to help software developers by answering their questions related to code and general software engineering.

The developer has file `src/example.py` open in VSCode. The content of this file is

```

```


When answering the developer's questions, please follow these guidelines:

- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.
- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|im_end|>
<|im_start|>user
What functions are there in this file?<|im_end|>
assistant
This file has one function, some_function(a,b)<|im_end|>
<|im_start|>user
Is this code valid?<|im_end|>
assistant
No, the function some_function is missing an implementation.<|im_end|>
<|im_end|>user
How could we make this code run?<|im_end|>
<|im_start|>assistant
"""

    assert prompt == expected_prompt


def test_binks_prefix_suffix(example_basic_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("dbrx_instruct")
    if not isinstance(tokenizer, DBRXInstructTokenizer):
        raise ValueError("Tokenizer must be a DBRXInstructTokenizer.")
    prompter = BinksDbrxChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_basic_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<|im_start|>system
You are Augment, an AI code assistant integrated into the VSCode IDE.
Your role is to help software developers by answering their questions related to code and general software engineering.

The developer has file `src/example.py` open in VSCode. The content of this file is

```
import pathlib
print(x)

```


When answering the developer's questions, please follow these guidelines:

- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.
- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|im_end|>
<|im_end|>user
fix bugs<|im_end|>
<|im_start|>assistant
"""

    assert prompt == expected_prompt
