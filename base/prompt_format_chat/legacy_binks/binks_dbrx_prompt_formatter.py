"""The Binks prompt formatter for the code chat."""

import typing

from base.prompt_format.common import (
    get_request_message_as_text,
    get_response_message_as_text,
)
from base.prompt_format_chat.prompt_formatter import (
    Chat<PERSON>romptFormatter,
    ChatPromptInput,
    ChatTokenApportionment,
    ExceedContextLength,
    Exchange,
    TokenList,
    TokenizedChatPromptFormatter,
    TokenizedChatPromptOutput,
)
from base.prompt_format.util import head_n, trailing_n
from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer


class BinksDbrxChatPromptFormatter(TokenizedChatPromptFormatter):
    """The class formats prompts for the Binks DBRX-based chat model.

    For prompt structure of DBRX instruct, see: https://huggingface.co/databricks/dbrx-instruct


    Prompt Structure:
    - System Prompt:
        a. System Prompt Header
        b. Retrievals (currently not supported)
        c. Context code (choose one):
            - Selected Code (if provided, no length limits)
            - Prefix and Suffix
        d. System Prompt Footer
    - Chat History (prioritized based on length limits)
    - Message (included fully, without restrictions)"""

    static_system_prompt: typing.Optional[str] = None
    """The static system prompt. If None, we will dynamically generate the system prompt, otherwise we will use the static system prompt."""

    def __init__(
        self,
        tokenizer: DBRXInstructTokenizer,
        token_apportionment: typing.Optional[ChatTokenApportionment] = None,
    ):
        self.tokenizer = tokenizer
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = ChatTokenApportionment(
                path_len=256,
                message_len=0,
                prefix_len=1024,
                selected_code_len=0,
                chat_history_len=2048,
                suffix_len=1024,
                retrieval_len=0,
                max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
            )
        self.token_apportionment = token_apportionment

    def _format_message(self, prompt_input: ChatPromptInput) -> TokenList:
        """Get the tokens for the message."""
        message_tokens = (
            [self.tokenizer.special_tokens.im_end]
            + self.tokenizer.tokenize_safe("user\n")
            + self.tokenizer.tokenize_safe(
                get_request_message_as_text(prompt_input.message)
            )
            + [self.tokenizer.special_tokens.im_end]
            + [self.tokenizer.special_tokens.newline]
            + [
                self.tokenizer.special_tokens.im_start,
            ]
            + self.tokenizer.tokenize_safe("assistant\n")
        )
        return message_tokens

    def _format_context_code(self, prompt_input: ChatPromptInput) -> TokenList:
        """Get the tokens for currently open file or selected code."""
        token_counts, tokenizer = self.token_apportionment, self.tokenizer
        tokenize = tokenizer.tokenize_safe

        clipped_path_tokens = trailing_n(
            tokenize(prompt_input.path), token_counts.path_len
        )
        clipped_prefix_tokens = trailing_n(
            tokenize(prompt_input.prefix), token_counts.prefix_len
        )
        clipped_suffix_tokens = head_n(
            tokenize(prompt_input.suffix), token_counts.suffix_len
        )
        # NOTE: We do not limit number of tokens in the selected code.
        # If the selected code is long, we then want to sacrifice other parts
        # (like chat history) in order to fit the selected code into the prompt.
        selected_code_tokens = tokenize(prompt_input.selected_code)

        if len(selected_code_tokens) > 0:
            # If selected code is not empty then we only pass the selected code
            # to the model without prefix or suffix. This way the model can focus
            # only on the selected code.
            relevant_code_tokens = selected_code_tokens
        elif len(clipped_prefix_tokens + clipped_suffix_tokens) > 0:
            relevant_code_tokens = clipped_prefix_tokens + clipped_suffix_tokens
        else:
            relevant_code_tokens: TokenList = []

        context_code_tokens = (
            self.tokenizer.tokenize_safe("The developer has file `")
            + clipped_path_tokens
            + self.tokenizer.tokenize_safe(
                "` open in VSCode. The content of this file is\n"
            )
            + self.tokenizer.tokenize_safe("\n```\n")
            + relevant_code_tokens
            + self.tokenizer.tokenize_safe("\n```\n\n")
        )
        return context_code_tokens

    def _format_exchange(self, exchange: Exchange) -> TokenList:
        """Get the tokens for a single exchange."""
        return (
            [self.tokenizer.special_tokens.im_start]
            + self.tokenizer.tokenize_safe("user")
            + [self.tokenizer.special_tokens.newline]
            + self.tokenizer.tokenize_safe(
                get_request_message_as_text(exchange.request_message)
            )
            + [self.tokenizer.special_tokens.im_end]
            + [self.tokenizer.special_tokens.newline]
            + self.tokenizer.tokenize_safe("assistant")
            + [self.tokenizer.special_tokens.newline]
            + self.tokenizer.tokenize_safe(
                get_response_message_as_text(exchange.response_text)
            )
            + [self.tokenizer.special_tokens.im_end]
            + [self.tokenizer.special_tokens.newline]
        )

    def _format_chat_history(
        self, prompt_input: ChatPromptInput, token_budget: int
    ) -> TokenList:
        """Take recent exchanges without going over the total token count allowed."""
        history_tokens_list: list[TokenList] = []
        total_tokens_len = 0

        reversed_history_list = list(
            reversed([exchange for exchange in prompt_input.chat_history])
        )

        for exchange in reversed_history_list:
            exchange_tokens = self._format_exchange(exchange)

            total_tokens_with_exchange_len = total_tokens_len + len(exchange_tokens)
            if total_tokens_with_exchange_len > token_budget:
                break
            total_tokens_len = total_tokens_with_exchange_len
            history_tokens_list.append(exchange_tokens)

        # Reverse the order of the history
        history_tokens: TokenList = []
        for exchange_tokens in reversed(history_tokens_list):
            history_tokens.extend(exchange_tokens)
        return history_tokens

    def _format_system_tokens(self, prompt_input: ChatPromptInput) -> TokenList:
        """Get the tokens for the system prompt."""
        if self.static_system_prompt is not None:
            system_prompt_tokens = self.tokenizer.tokenize_safe(
                self.static_system_prompt
            )
        else:
            context_code = self._format_context_code(prompt_input)
            system_prompt_tokens = (
                self.tokenizer.tokenize_safe(
                    r"""You are Augment, an AI code assistant integrated into the VSCode IDE.
Your role is to help software developers by answering their questions related to code and general software engineering.

"""
                )
                + context_code
                + self.tokenizer.tokenize_safe(
                    r"""
When answering the developer's questions, please follow these guidelines:

- Use Markdown formatting to make your responses more readable, including code blocks, bullet lists, and other relevant formatting, but avoid using Markdown headers.
- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question."""
                )
            )
        final_system_prompt_tokens = (
            [self.tokenizer.special_tokens.im_start]
            + self.tokenizer.tokenize_safe("system")
            + [self.tokenizer.special_tokens.newline]
            + system_prompt_tokens
            + [self.tokenizer.special_tokens.im_end]
            + [self.tokenizer.special_tokens.newline]
        )
        return final_system_prompt_tokens

    def format_prompt(self, prompt_input: ChatPromptInput) -> TokenizedChatPromptOutput:
        """Format prompt for Binks DBRX-based code chat model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """

        system_tokens = self._format_system_tokens(prompt_input)
        message_tokens = self._format_message(prompt_input)

        chat_history_budget = (
            self.token_apportionment.max_prompt_len
            - len(system_tokens)
            - len(message_tokens)
        )
        if self.token_apportionment.chat_history_len >= 0:
            chat_history_budget = min(
                chat_history_budget, self.token_apportionment.chat_history_len
            )
        chat_history_tokens = self._format_chat_history(
            prompt_input, chat_history_budget
        )

        final_tokens = system_tokens + chat_history_tokens + message_tokens

        if len(final_tokens) > self.token_apportionment.max_prompt_len:
            raise ExceedContextLength(
                f"Prompt length {len(final_tokens)} exceeds maximum prompt length "
                f"{self.token_apportionment.max_prompt_len}."
            )

        return TokenizedChatPromptOutput(final_tokens, [])
