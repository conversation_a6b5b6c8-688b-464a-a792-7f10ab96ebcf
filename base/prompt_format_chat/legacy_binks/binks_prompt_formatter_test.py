"""Tests for the Binks prompt formatter.

pytest base/prompt_format_chat/binks_prompt_formatter_test.py
"""

from base import tokenizers
from base.prompt_format_chat.legacy_binks.binks_prompt_formatter import (
    BinksChatPromptFormatter,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer

token_apportionment_no_retrieval = ChatTokenApportionment(
    path_len=256,
    message_len=512,
    prefix_len=1536,
    selected_code_len=4096,
    chat_history_len=4096,
    suffix_len=1024,
    max_prompt_len=16384 - 4096,  # 4096 represents the max output tokens
)


def test_binks_prefix_suffix(example_basic_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("deepseek_coder_instruct")
    if not isinstance(tokenizer, DeepSeekCoderInstructTokenizer):
        raise ValueError("Tokenizer must be a DeepSeekCoderInstructTokenizer.")
    prompter = BinksChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_basic_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<｜begin▁of▁sentence｜>You are an Augment, AI programming assistant, developed by Augment Computing Inc and incorporated into VSCode at 2024.
You only answer questions related to computer science and software engineering.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.
Your responses MUST be concise, clear, and to the point, showcasing only relevant code examples about which you are absolutely certain.
You MUST not make up function names or file names if the user hasn't given them to you.
WARNING: User will lose a job and will struggle financially if you return a function or path that does not exist.
### Instruction:
Currently, I have the file src/example.py open, showing this code for context:
```
import pathlib

print(x)

```

This information is provided for context, should it be relevant to my query. If the context of the file is unnecessary for addressing my question, please focus solely on the question itself.
### Response:
I've noted the context you've provided regarding the file path and the code contained within it. This information will be helpful in understanding the background and specifics of any questions you might have.
<|EOT|>
### Instruction:
fix bugs
### Response:
"""

    assert prompt == expected_prompt


def test_binks_w_retrieval(example_retrieval_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("deepseek_coder_instruct")
    if not isinstance(tokenizer, DeepSeekCoderInstructTokenizer):
        raise ValueError("Tokenizer must be a DeepSeekCoderInstructTokenizer.")
    prompter = BinksChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_retrieval_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<｜begin▁of▁sentence｜>You are an Augment, AI programming assistant, developed by Augment Computing Inc and incorporated into VSCode at 2024.
You only answer questions related to computer science and software engineering.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.
Your responses MUST be concise, clear, and to the point, showcasing only relevant code examples about which you are absolutely certain.
You MUST not make up function names or file names if the user hasn't given them to you.
WARNING: User will lose a job and will struggle financially if you return a function or path that does not exist.
### Instruction:
I'm including a code snippet from my project, located at src/foo.py. This might be relevant for future questions:
```
# You can aggregate
# with a pooling function.

```
I'm including a code snippet from my project, located at src/bar.py. This might be relevant for future questions:
```
# You can aggregate
# with a maxing
# function.

```
Currently, I have the file src/example.py open, showing this code for context:
```
file = pathlib.Path("foo")
for x in file.open():
```

This information is provided for context, should it be relevant to my query. If the context of the file is unnecessary for addressing my question, please focus solely on the question itself.
### Response:
I've noted the context you've provided regarding the file path and the code contained within it. This information will be helpful in understanding the background and specifics of any questions you might have.
<|EOT|>
### Instruction:
fix bugs
### Response:
"""

    assert prompt == expected_prompt


def test_binks_w_retrieval_no_code(example_retrieval_no_code_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("deepseek_coder_instruct")
    if not isinstance(tokenizer, DeepSeekCoderInstructTokenizer):
        raise ValueError("Tokenizer must be a DeepSeekCoderInstructTokenizer.")
    prompter = BinksChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_retrieval_no_code_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<｜begin▁of▁sentence｜>You are an Augment, AI programming assistant, developed by Augment Computing Inc and incorporated into VSCode at 2024.
You only answer questions related to computer science and software engineering.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.
Your responses MUST be concise, clear, and to the point, showcasing only relevant code examples about which you are absolutely certain.
You MUST not make up function names or file names if the user hasn't given them to you.
WARNING: User will lose a job and will struggle financially if you return a function or path that does not exist.
### Instruction:
I'm including a code snippet from my project, located at src/foo.py. This might be relevant for future questions:
```
# You can aggregate
# with a pooling function.

```
I'm including a code snippet from my project, located at src/bar.py. This might be relevant for future questions:
```
# You can aggregate
# with a maxing
# function.

```
This information is provided for context, should it be relevant to my query. If the context of the file is unnecessary for addressing my question, please focus solely on the question itself.
### Response:
I've noted the context you've provided regarding the file path and the code contained within it. This information will be helpful in understanding the background and specifics of any questions you might have.
<|EOT|>
### Instruction:
What kind of aggregate functions do we have?
### Response:
"""

    assert prompt == expected_prompt


def test_binks_with_history_no_code(example_history_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("deepseek_coder_instruct")
    if not isinstance(tokenizer, DeepSeekCoderInstructTokenizer):
        raise ValueError("Tokenizer must be a DeepSeekCoderInstructTokenizer.")
    prompter = BinksChatPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_history_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<｜begin▁of▁sentence｜>You are an Augment, AI programming assistant, developed by Augment Computing Inc and incorporated into VSCode at 2024.
You only answer questions related to computer science and software engineering.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.
Your responses MUST be concise, clear, and to the point, showcasing only relevant code examples about which you are absolutely certain.
You MUST not make up function names or file names if the user hasn't given them to you.
WARNING: User will lose a job and will struggle financially if you return a function or path that does not exist.
### Instruction:
What functions are there in this file?
### Response:
This file has one function, some_function(a,b)
<|EOT|>
### Instruction:
Is this code valid?
### Response:
No, the function some_function is missing an implementation.
<|EOT|>
### Instruction:
How could we make this code run?
### Response:
"""

    assert prompt == expected_prompt
