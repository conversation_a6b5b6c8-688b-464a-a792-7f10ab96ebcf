load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "binks_prompt_formatter",
    srcs = [
        "binks_prompt_formatter.py",
    ],
    visibility = ["//base/prompt_format_chat:__subpackages__"],
    deps = [
        "//base/prompt_format:util",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "binks_prompt_formatter_test",
    srcs = ["binks_prompt_formatter_test.py"],
    deps = [
        ":binks_prompt_formatter",
        "//base/prompt_format_chat:conftest",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

py_library(
    name = "formatted_file",
    srcs = [
        "formatted_file.py",
    ],
    deps = [
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
        requirement("cachetools"),
    ],
)

pytest_test(
    name = "formatted_file_test",
    srcs = ["formatted_file_test.py"],
    deps = [
        ":formatted_file",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "retrieval_section_prompt_formatter",
    srcs = [
        "retrieval_section_prompt_formatter.py",
    ],
    deps = [
        ":formatted_file",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:tokenized_string_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "retrieval_section_prompt_formatter_test",
    srcs = ["retrieval_section_prompt_formatter_test.py"],
    deps = [
        ":retrieval_section_prompt_formatter",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "selected_code_prompt_formatter",
    srcs = [
        "selected_code_prompt_formatter.py",
    ],
    deps = [
        ":formatted_file",
        "//base/prompt_format:util",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:tokenized_string_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "selected_code_prompt_formatter_test",
    srcs = ["selected_code_prompt_formatter_test.py"],
    deps = [
        ":selected_code_prompt_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "binks_llama3_prompt_formatter",
    srcs = [
        "binks_llama3_prompt_formatter.py",
    ],
    visibility = ["//base/prompt_format_chat:__subpackages__"],
    deps = [
        ":retrieval_section_prompt_formatter",
        ":selected_code_prompt_formatter",
        "//base/prompt_format:util",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:tokenized_string_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "binks_llama3_prompt_formatter_test",
    srcs = ["binks_llama3_prompt_formatter_test.py"],
    deps = [
        ":binks_llama3_prompt_formatter",
        "//base/prompt_format_chat:conftest",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

py_library(
    name = "binks_dbrx_prompt_formatter",
    srcs = [
        "binks_dbrx_prompt_formatter.py",
    ],
    visibility = ["//base/prompt_format_chat:__subpackages__"],
    deps = [
        "//base/prompt_format:util",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "binks_dbrx_prompt_formatter_test",
    srcs = ["binks_dbrx_prompt_formatter_test.py"],
    deps = [
        ":binks_dbrx_prompt_formatter",
        "//base/prompt_format_chat:conftest",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)
