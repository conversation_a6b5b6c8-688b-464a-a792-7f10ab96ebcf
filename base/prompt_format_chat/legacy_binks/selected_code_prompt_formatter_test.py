"""Tests for the selected code prompt formatter.

pytest base/prompt_format_chat/selected_code_prompt_formatter_test.py
"""

from base import tokenizers
from base.prompt_format_chat.legacy_binks.selected_code_prompt_formatter import (
    SelectedCodePromptFormatter,
    compute_prefix_suffix_length,
)
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer


def test_compute_prefix_suffix_length():
    """Tests the compute_prefix_suffix_length function."""

    # Cases where we don't clip prefix or suffix
    assert compute_prefix_suffix_length(10, 10, 11, 11, 1000) == (10, 10)
    assert compute_prefix_suffix_length(10, 10, 10, 10, 20) == (10, 10)
    assert compute_prefix_suffix_length(9, 10, 10, 10, 20) == (9, 10)
    assert compute_prefix_suffix_length(9, 0, 10, 10, 20) == (9, 0)

    # Simple clipping by max_prefix_tokens/max_suffix_tokens
    assert compute_prefix_suffix_length(10, 10, 9, 10, 1000) == (9, 10)
    assert compute_prefix_suffix_length(15, 10, 10, 10, 20) == (10, 10)
    assert compute_prefix_suffix_length(10, 15, 10, 10, 20) == (10, 10)

    # Clipping by total_token_budget
    assert compute_prefix_suffix_length(10, 10, 10, 10, 15) == (7, 8)
    assert compute_prefix_suffix_length(10, 10, 10, 10, 12) == (6, 6)

    # Clipping by total_token_budget, inbalanced prefix/suffix
    assert compute_prefix_suffix_length(10, 6, 10, 10, 15) == (9, 6)
    assert compute_prefix_suffix_length(7, 10, 10, 10, 15) == (7, 8)

    # Edge cases
    assert compute_prefix_suffix_length(0, 0, 0, 0, 0) == (0, 0)
    assert compute_prefix_suffix_length(10, 10, 0, 0, 0) == (0, 0)
    assert compute_prefix_suffix_length(1, 1, 1, 1, 1) == (0, 1)
    assert compute_prefix_suffix_length(1, 1, 1, 1, 2) == (1, 1)
    assert compute_prefix_suffix_length(0, 0, 1, 1, 1) == (0, 0)
    assert compute_prefix_suffix_length(0, 0, 0, 0, 1) == (0, 0)

    # Large values
    assert compute_prefix_suffix_length(1000, 1000, 1000, 1000, 2000) == (1000, 1000)
    assert compute_prefix_suffix_length(1000, 1000, 1000, 1000, 1500) == (750, 750)


def test_format_code_without_selected_code():
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = SelectedCodePromptFormatter(tokenizer, 1024, 1024, 1024)

    path = "src/example.py"
    prefix = 'import pathlib\nfile = pathlib.Path("foo")\n'
    suffix = "for x in file.open():\n    print(x)\n"
    selected_code = ""
    total_token_budget = 512

    prompt_output, _, _ = prompter.format(
        path, prefix, suffix, selected_code, total_token_budget
    )
    prompt = tokenizer.detokenize(prompt_output)

    expected_prompt = """I have the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
file = pathlib.Path("foo")
for x in file.open():
    print(x)

```

"""

    assert prompt == expected_prompt


def test_format_code_without_selected_code_tight_budget():
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = SelectedCodePromptFormatter(tokenizer, 1024, 1024, 1024)

    path = "src/example.py"
    prefix = 'import pathlib\nfile = pathlib.Path("foo")\n'
    suffix = "for x in file.open():\n    print(x)\n"
    selected_code = ""
    total_token_budget = 30

    prompt_output, _, _ = prompter.format(
        path, prefix, suffix, selected_code, total_token_budget
    )
    prompt = tokenizer.detokenize(prompt_output)

    expected_prompt = """I have the file `src/example.py` open. Here is an excerpt from the file:

```
("foo")
for x in
```

"""

    assert prompt == expected_prompt


def test_format_with_selected_code_simple_case():
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = SelectedCodePromptFormatter(tokenizer, 1024, 1024, 1024)

    path = "src/example.py"
    prefix = 'import pathlib\nfile = pathlib.Path("foo")\n'
    suffix = "for x in file.open():\n    print(x)\n"
    selected_code = "TEST"

    total_token_budget = 512

    prompt_output, _, _ = prompter.format(
        path, prefix, suffix, selected_code, total_token_budget
    )
    prompt = tokenizer.detokenize(prompt_output)

    expected_prompt = """I have the file `src/example.py` open and has selected part of the code.

Here is the full file:

```
import pathlib
file = pathlib.Path("foo")
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
for x in file.open():
    print(x)

```

Here is the selected code:

```
TEST
```

"""

    assert prompt == expected_prompt


def test_format_with_selected_code_tight_budget():
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = SelectedCodePromptFormatter(tokenizer, 1024, 1024, 1024)

    path = "src/example.py"
    prefix = 'import pathlib\nfile = pathlib.Path("foo")\n'
    suffix = "for x in file.open():\n    print(x)\n"
    selected_code = "\n".join(
        [
            "line {} I'm just a long line from a long family.".format(i)
            for i in range(10)
        ]
    )

    total_token_budget = 202

    prompt_output, _, _ = prompter.format(
        path, prefix, suffix, selected_code, total_token_budget
    )
    prompt = tokenizer.detokenize(prompt_output)

    expected_prompt = """I have the file `src/example.py` open and has selected part of the code.

Here is the full file:

```
")
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
for x
```

Here is the selected code:

```
line 0 I'm just a long line from a long family.
line 1 I'm just a long line from a long family.
line 2 I'm just a long line from a long family.
line 3 I'm just a long line from a long family.
line 4 I'm just a long line from a long family.
line 5 I'm just a long line from a long family.
line 6 I'm just a long line from a long family.
line 7 I'm just a long line from a long family.
line 8 I'm just a long line from a long family.
line 9 I'm just a long line from a long family.
```

"""

    assert prompt == expected_prompt


def test_format_with_only_selected_code():
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    prompter = SelectedCodePromptFormatter(tokenizer, 1024, 1024, 1024)

    path = "src/example.py"
    prefix = ""
    suffix = ""
    selected_code = "TEST"

    total_token_budget = 512

    prompt_output, _, _ = prompter.format(
        path, prefix, suffix, selected_code, total_token_budget
    )
    prompt = tokenizer.detokenize(prompt_output)

    expected_prompt = """I have the file `src/example.py` open and selected all of code within the file:

```
TEST
```

"""

    assert prompt == expected_prompt
