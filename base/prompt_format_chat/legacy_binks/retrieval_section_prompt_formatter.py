"""Prompt formatter to format section with retrieved chunks."""

from collections import OrderedDict
from typing import cast, Iterable, Sequence

from base.prompt_format_chat.prompt_formatter import Prompt<PERSON>hunk, TokenList
from base.tokenizers.tokenizer import Tokenizer
from base.prompt_format_chat.legacy_binks.formatted_file import FormattedFile
from base.prompt_format_chat.tokenized_string_formatter import TokenizedStringFormatter


class RetrievalSectionPromptFormatter:
    """Formats retrieval sections using provided tokenizer and templates."""

    PATH_PLACEHOLDER = "path"
    CONTENT_PLACEHOLDER = "content"

    def __init__(
        self,
        single_file_template: str,
        tokenizer: Tokenizer,
    ):
        """Initialize with tokenizer, template, max path tokens, and budget."""
        self.tokenizer = tokenizer
        self.single_file_formatter = TokenizedStringFormatter(
            template=single_file_template,
            tokenizer=tokenizer,
        )

    def _format_and_count_tokens(
        self, path: TokenList, formatted_file: FormattedFile
    ) -> int:
        """Format a single file and count its tokens."""
        return self.single_file_formatter.format_and_count_tokens(
            {
                self.PATH_PLACEHOLDER: path,
                self.CONTENT_PLACEHOLDER: cast(Sequence[int], formatted_file),
            }
        )

    def format(
        self, chunks: Iterable[PromptChunk], max_path_tokens: int, max_total_tokens: int
    ) -> tuple[TokenList, list[PromptChunk]]:
        """Format the retrieval section with the given chunks."""
        total_tokens = 0
        paths: dict[str, TokenList] = {}
        formatted_files: dict[str, FormattedFile] = OrderedDict()
        for chunk in chunks:
            if chunk.blob_name not in paths:
                paths[chunk.blob_name] = self.tokenizer.tokenize_safe(chunk.path)[
                    :max_path_tokens
                ]

            if chunk.blob_name in formatted_files:
                old_formatted_file = formatted_files[chunk.blob_name]
                old_n_tokens = self._format_and_count_tokens(
                    paths[chunk.blob_name], old_formatted_file
                )
            else:
                old_formatted_file = FormattedFile(self.tokenizer)
                old_n_tokens = 0

            new_formatted_file = old_formatted_file.add_chunk(chunk)
            new_n_tokens = self._format_and_count_tokens(
                paths[chunk.blob_name], new_formatted_file
            )
            if total_tokens + new_n_tokens - old_n_tokens <= max_total_tokens:
                total_tokens += new_n_tokens - old_n_tokens
                formatted_files[chunk.blob_name] = new_formatted_file

        tokens, prompt_chunks = [], []
        for k, formatted_file in formatted_files.items():
            tokens.extend(
                self.single_file_formatter.format(
                    {
                        self.PATH_PLACEHOLDER: paths[k],
                        self.CONTENT_PLACEHOLDER: cast(Sequence[int], formatted_file),
                    }
                )
            )
            prompt_chunks.extend(formatted_files[k].get_chunks())

        if len(tokens) > 0:
            assert len(tokens) <= max_total_tokens, len(tokens)

        return tokens, prompt_chunks
