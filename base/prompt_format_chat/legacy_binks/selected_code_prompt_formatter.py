"""The prompt formatter for the selected code and the current file section."""

from typing import Optional
from textwrap import dedent

from base.prompt_format_chat.prompt_formatter import (
    ExceedContextLength,
    TokenList,
)
from base.prompt_format.util import head_n, trailing_n
from base.prompt_format_chat.tokenized_string_formatter import TokenizedStringFormatter
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer


def compute_prefix_suffix_length(
    n_prefix_tokens: int,
    n_suffix_tokens: int,
    max_prefix_tokens: int,
    max_suffix_tokens: int,
    total_token_budget: int,
) -> tuple[int, int]:
    """Compute the number of prefix and suffix tokens to use."""
    if n_prefix_tokens < 0:
        raise ValueError("n_prefix_tokens must be non-negative")
    if n_suffix_tokens < 0:
        raise ValueError("n_suffix_tokens must be non-negative")
    if max_prefix_tokens < 0:
        raise ValueError("max_prefix_tokens must be non-negative")
    if max_suffix_tokens < 0:
        raise ValueError("max_suffix_tokens must be non-negative")
    if total_token_budget < 0:
        raise ValueError("total_token_budget must be non-negative")

    n_prefix_tokens = min(n_prefix_tokens, max_prefix_tokens)
    n_suffix_tokens = min(n_suffix_tokens, max_suffix_tokens)

    if n_prefix_tokens + n_suffix_tokens > total_token_budget:
        # We are out of budget even after clipping prefix and suffix tokens.
        # We need to adjust the prefix and suffix tokens further to fit within the budget.
        # First, we allocate half of the remaining budget to the prefix tokens
        n_prefix_tokens_adjusted = min(n_prefix_tokens, total_token_budget // 2)
        # Second, allocate the remaining budget to the suffix tokens
        n_suffix_tokens = min(
            n_suffix_tokens, total_token_budget - n_prefix_tokens_adjusted
        )
        # Finally, if there wasn't too many suffix tokens,
        # we can reallocate the remaining budget back to the prefix tokens.
        n_prefix_tokens = min(n_prefix_tokens, total_token_budget - n_suffix_tokens)

    return n_prefix_tokens, n_suffix_tokens


class SelectedCodePromptFormatter:
    """The class formats the selected code prompt."""

    def __init__(
        self,
        tokenizer: Llama3InstructTokenizer,
        max_path_tokens: int,
        max_prefix_tokens: int,
        max_suffix_tokens: int,
    ):
        self.tokenizer = tokenizer
        self.max_path_tokens = max_path_tokens
        self.max_prefix_tokens = max_prefix_tokens
        self.max_suffix_tokens = max_suffix_tokens

        if max_path_tokens < 0:
            raise ValueError(
                f"max_path_tokens must be non-negative, but got {max_path_tokens}."
            )

        if max_prefix_tokens < 0 or max_suffix_tokens < 0:
            raise ValueError(
                f"max_prefix_tokens and max_suffix_tokens must be non-negative, "
                f"but got {max_prefix_tokens} and {max_suffix_tokens}."
            )

        self.no_selected_code_formatter = TokenizedStringFormatter(
            dedent(
                """\
            I have the file `{path}` open. Here is an excerpt from the file:

            ```
            {prefix}{suffix}
            ```

            """
            ),
            tokenizer=tokenizer,
        )
        self.whole_file_is_selected_formatter = TokenizedStringFormatter(
            dedent(
                """\
            I have the file `{path}` open and selected all of code within the file:

            ```
            {selected_code}
            ```

            """
            ),
            tokenizer=tokenizer,
        )
        self.selected_code_formatter = TokenizedStringFormatter(
            dedent(
                """\
            I have the file `{path}` open and has selected part of the code.

            Here is the full file:

            ```
            {prefix}[START SELECTED REGION]
            ...
            [selected code goes here]
            ...
            [END SELECTED REGION]
            {suffix}
            ```

            Here is the selected code:

            ```
            {selected_code}
            ```

            """,
            ),
            tokenizer=tokenizer,
        )

    def tokenize(self, text: str) -> TokenList:
        """Tokenize the text."""
        return self.tokenizer.tokenize_safe(text)

    def _compute_prefix_suffix_length(
        self, n_prefix_tokens: int, n_suffix_tokens: int, total_token_budget: int
    ) -> tuple[int, int]:
        """Compute the number of prefix and suffix tokens to use."""
        return compute_prefix_suffix_length(
            n_prefix_tokens=n_prefix_tokens,
            n_suffix_tokens=n_suffix_tokens,
            max_prefix_tokens=self.max_prefix_tokens,
            max_suffix_tokens=self.max_suffix_tokens,
            total_token_budget=total_token_budget,
        )

    def format_only_selected_code(
        self,
        path: str,
        selected_code: str,
        total_token_budget: int,
    ) -> tuple[TokenList, TokenList, TokenList]:
        """Format the current file where ALL of the code is selected."""

        clipped_path_tokens = trailing_n(self.tokenize(path), self.max_path_tokens)

        # NOTE: We do not limit number of tokens in the selected code.
        # If the selected code is long, we then want to sacrifice other parts
        # (like chat history) in order to fit the selected code into the prompt.
        selected_code_tokens = self.tokenize(selected_code)

        if len(selected_code_tokens) == 0:
            raise ValueError("The function assumes selected code is not empty.")

        prompt = self.whole_file_is_selected_formatter.format(
            {"path": clipped_path_tokens, "selected_code": selected_code_tokens}
        )

        if len(prompt) > total_token_budget:
            raise ExceedContextLength(
                f"Prompt length {len(prompt)} exceeds maximum prompt length "
                f"{total_token_budget}."
            )

        return prompt, [], []

    def format_with_selected_code(
        self,
        path: str,
        prefix: str,
        suffix: str,
        selected_code: str,
        total_token_budget: int,
    ) -> tuple[TokenList, TokenList, TokenList]:
        """Format the current file where some code is selected."""

        clipped_path_tokens = trailing_n(self.tokenize(path), self.max_path_tokens)

        # NOTE: We do not limit number of tokens in the selected code.
        # If the selected code is long, we then want to sacrifice other parts
        # (like chat history) in order to fit the selected code into the prompt.
        selected_code_tokens = self.tokenize(selected_code)

        if len(selected_code_tokens) == 0:
            raise ValueError("The function assumes selected code is not empty.")

        empty_content_prompt_length = self.selected_code_formatter.format(
            {
                "path": clipped_path_tokens,
                "selected_code": selected_code_tokens,
                "prefix": [],
                "suffix": [],
            }
        )
        remaining_budget = total_token_budget - len(empty_content_prompt_length)
        if remaining_budget < 0:
            raise ExceedContextLength(
                f"Prompt length even with empty prefix and suffix ({empty_content_prompt_length}) "
                "exceeds maximum prompt length "
                f"{total_token_budget}."
            )

        tokenized_prefix = self.tokenize(prefix)
        tokenized_suffix = self.tokenize(suffix)

        n_prefix_tokens, n_suffix_tokens = self._compute_prefix_suffix_length(
            len(tokenized_prefix), len(tokenized_suffix), remaining_budget
        )

        clipped_prefix_tokens = trailing_n(tokenized_prefix, n_prefix_tokens)
        clipped_suffix_tokens = head_n(tokenized_suffix, n_suffix_tokens)

        prompt = self.selected_code_formatter.format(
            {
                "path": clipped_path_tokens,
                "selected_code": selected_code_tokens,
                "prefix": clipped_prefix_tokens,
                "suffix": clipped_suffix_tokens,
            }
        )

        if len(prompt) > total_token_budget:
            raise ExceedContextLength(
                f"Prompt length {len(prompt)} exceeds maximum prompt length "
                f"{total_token_budget}."
            )

        return prompt, clipped_prefix_tokens, clipped_suffix_tokens

    def format_without_selected_code(
        self,
        path: str,
        prefix: str,
        suffix: str,
        total_token_budget: int,
    ) -> tuple[TokenList, TokenList, TokenList]:
        """Format the current file where no code is selected."""
        clipped_path_tokens = trailing_n(self.tokenize(path), self.max_path_tokens)

        empty_content_prompt_length = (
            self.no_selected_code_formatter.format_and_count_tokens(
                {"path": clipped_path_tokens, "prefix": [], "suffix": []}
            )
        )

        remaining_budget = total_token_budget - empty_content_prompt_length

        if remaining_budget < 0:
            raise ExceedContextLength(
                f"Prompt length even with empty prefix and suffix ({empty_content_prompt_length}) "
                "exceeds maximum prompt length "
                f"{total_token_budget}."
            )

        tokenized_prefix = self.tokenize(prefix)
        tokenized_suffix = self.tokenize(suffix)

        n_prefix_tokens, n_suffix_tokens = self._compute_prefix_suffix_length(
            len(tokenized_prefix), len(tokenized_suffix), remaining_budget
        )

        clipped_prefix_tokens = trailing_n(tokenized_prefix, n_prefix_tokens)
        clipped_suffix_tokens = head_n(tokenized_suffix, n_suffix_tokens)

        prompt = self.no_selected_code_formatter.format(
            {
                "path": clipped_path_tokens,
                "prefix": clipped_prefix_tokens,
                "suffix": clipped_suffix_tokens,
            }
        )

        if len(prompt) > total_token_budget:
            raise ExceedContextLength(
                f"Prompt length {len(prompt)} exceeds maximum prompt length "
                f"{total_token_budget}."
            )
        return prompt, clipped_prefix_tokens, clipped_suffix_tokens

    def format(
        self,
        path: str,
        prefix: str,
        suffix: str,
        selected_code: str,
        total_token_budget: int,
    ) -> tuple[TokenList, TokenList, TokenList]:
        """Format the current file."""
        if len(selected_code) == 0:
            if len(prefix) == 0 and len(suffix) == 0:
                return [], [], []
            else:
                return self.format_without_selected_code(
                    path, prefix, suffix, total_token_budget
                )
        else:
            if len(prefix) == 0 and len(suffix) == 0:
                return self.format_only_selected_code(
                    path, selected_code, total_token_budget
                )
            else:
                return self.format_with_selected_code(
                    path, prefix, suffix, selected_code, total_token_budget
                )
