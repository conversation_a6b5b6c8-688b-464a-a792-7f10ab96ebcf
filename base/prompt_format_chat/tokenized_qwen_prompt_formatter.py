"""Tokenized prompt formatter for Qwen-based chat models."""

import json
from textwrap import dedent

from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNodeType,
    ChatResultToolUse,
    RequestMessage,
    ResponseMessage,
    ToolDefinition,
)
from base.prompt_format_chat.prompt_formatter import (
    StructToTokensPromptFormatter,
    StructuredChatPromptOutput,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.tokenized_string_formatter import TokenizedStringFormatter
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer

BUFFER_SIZE_FOR_SPEC_TOKENS = 100


class StructToTokensQwenPromptFormatter(StructToTokensPromptFormatter):
    """Formats tokenized prompts for Qwen-based chat models.

    Format: <|im_start|>system\n[system prompt]<|im_end|>
            <|im_start|>user\n[user message]<|im_end|>
            <|im_start|>assistant\n[assistant message]<|im_end|>
    """

    def __init__(self, tokenizer: Qwen25CoderTokenizer):
        assert isinstance(
            tokenizer, Qwen25CoderTokenizer
        ), f"{tokenizer=} must be a Qwen25CoderTokenizer."
        self.tokenizer = tokenizer
        self.special_tokens_values = {
            "im_start": [self.tokenizer.special_tokens.im_start],
            "im_end": [self.tokenizer.special_tokens.im_end],
        }

        self.system_prompt_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {im_start}system
            {system_prompt}{im_end}
            """),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )
        self.user_message_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {im_start}user
            {user_message}{im_end}
            """),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )
        self.assistant_message_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {im_start}assistant
            {assistant_message}{im_end}
            """),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )
        self.cur_message_formatter = TokenizedStringFormatter(
            template=dedent("""\
            {im_start}user
            {user_message}{im_end}
            {im_start}assistant
            """),
            tokenizer=self.tokenizer,
            default_values=self.special_tokens_values,
        )

    @property
    def reserved_token_budget(self) -> int:
        return BUFFER_SIZE_FOR_SPEC_TOKENS

    def format_request_message(self, request_message: RequestMessage) -> str:
        """Format a request message (string or structured)."""
        if isinstance(request_message, str):
            return request_message

        message_parts, tool_responses = [], []
        for node in request_message:
            if node.type == ChatRequestNodeType.TEXT and node.text_node is not None:
                message_parts.append(node.text_node.content)
            elif (
                node.type == ChatRequestNodeType.TOOL_RESULT
                and node.tool_result_node is not None
            ):
                tool_responses.append(
                    f"<tool_response>\n{node.tool_result_node.content}\n</tool_response>"
                )

        result = "".join(message_parts)
        if tool_responses:
            result += "\n".join(tool_responses)
        return result

    def _format_tool_call(self, tool_use: ChatResultToolUse) -> str:
        """Format a tool call into the expected string format."""
        return f'<tool_call>\n{{"name": "{tool_use.name}", "arguments": {json.dumps(tool_use.input)}}}\n</tool_call>'

    def format_response(self, response: ResponseMessage) -> list[int]:
        """Format response (string or structured)."""
        if isinstance(response, str):
            return self.assistant_message_formatter.format(
                values={"assistant_message": self.tokenizer.tokenize_safe(response)}
            )

        result = []
        for node in response:
            if node.type == ChatResultNodeType.RAW_RESPONSE and node.content:
                result.append(node.content)
            elif node.type == ChatResultNodeType.TOOL_USE and node.tool_use is not None:
                result.append(self._format_tool_call(node.tool_use))

        result = self.assistant_message_formatter.format(
            values={
                "assistant_message": self.tokenizer.tokenize_safe("\n".join(result))
            }
        )
        return result

    def _format_tool_definitions(self, tool_definitions: list[ToolDefinition]) -> str:
        """Format tool definitions into the expected format for Qwen."""
        if not tool_definitions:
            return ""

        tools_json = []
        for tool in tool_definitions:
            # Format each tool as a JSON object in the expected format
            if tool.input_schema_json == "{}":
                # Empty schema case
                tool_json = {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": {"type": "object", "properties": {}},
                    },
                }
            else:
                # Parse the schema and format it correctly
                parameters = json.loads(tool.input_schema_json)
                tool_json = {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": parameters,
                    },
                }
            tools_json.append(json.dumps(tool_json))

        tools_section = dedent("""

        # Tools

        You may call one or more functions to assist with the user query.

        You are provided with function signatures within <tools></tools> XML tags:
        <tools>
        {0}
        </tools>

        For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:
        <tool_call>
        {{"name": <function-name>, "arguments": <args-json-object>}}
        </tool_call>""")

        return tools_section.format("\n".join(tools_json))

    def format_prompt(
        self, prompt_input: StructuredChatPromptOutput
    ) -> TokenizedChatPromptOutput:
        """Format prompt for Qwen-based code chat model."""
        # Format system prompt with tool definitions if present
        system_prompt_text = prompt_input.system_prompt or ""
        if prompt_input.tool_definitions:
            system_prompt_text += self._format_tool_definitions(
                prompt_input.tool_definitions
            )

        # System prompt
        flattened_prompt = self.system_prompt_formatter.format(
            values={"system_prompt": self.tokenizer.tokenize_safe(system_prompt_text)}
        )

        # Chat history
        for message in prompt_input.chat_history:
            user_msg = self.format_request_message(message.request_message)
            flattened_prompt += self.user_message_formatter.format(
                values={"user_message": self.tokenizer.tokenize_safe(user_msg)}
            )
            flattened_prompt += self.format_response(message.response_text)

        # Current message
        current_msg = self.format_request_message(prompt_input.message)
        flattened_prompt += self.cur_message_formatter.format(
            values={"user_message": self.tokenizer.tokenize_safe(current_msg)}
        )

        return TokenizedChatPromptOutput(
            flattened_prompt, prompt_input.retrieved_chunks_in_prompt
        )
