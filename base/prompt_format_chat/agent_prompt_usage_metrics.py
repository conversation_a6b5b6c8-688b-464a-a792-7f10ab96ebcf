"""Prometheus metrics for prompt formatting."""

from dataclasses import dataclass
from prometheus_client import Histogram

# Define buckets for token count histograms
# These buckets cover a wide range of token counts from small to very large
TOKEN_COUNT_BUCKETS = [
    0,
    10,
    20,
    40,
    80,
    160,
    320,
    640,
    1280,
    2560,
    5120,
    10240,
    20480,
    40960,
    81920,
    163840,
    327680,
]

SYSTEM_PROMPT_TOKENS = Histogram(
    "au_agent_prompt_formatter_system_prompt_tokens",
    "Number of tokens in the system prompt",
    ["formatter_type"],
    buckets=TOKEN_COUNT_BUCKETS,
)

CUR_MESSAGE_TOKENS = Histogram(
    "au_agent_prompt_formatter_cur_message_tokens",
    "Number of tokens in the current message (excluding tool results)",
    ["formatter_type"],
    buckets=TOKEN_COUNT_BUCKETS,
)

TOOL_RESULT_TOKENS = Histogram(
    "au_agent_prompt_formatter_tool_result_tokens",
    "Number of tokens in tool results",
    ["formatter_type"],
    buckets=TOKEN_COUNT_BUCKETS,
)

SELECTED_CODE_TOKENS = Histogram(
    "au_agent_prompt_formatter_selected_code_tokens",
    "Number of tokens in selected code",
    ["formatter_type"],
    buckets=TOKEN_COUNT_BUCKETS,
)

CHAT_HISTORY_TOKENS = Histogram(
    "au_agent_prompt_formatter_chat_history_tokens",
    "Number of tokens in chat history",
    ["formatter_type"],
    buckets=TOKEN_COUNT_BUCKETS,
)

TOTAL_PROMPT_TOKENS = Histogram(
    "au_agent_prompt_formatter_total_tokens",
    "Total number of tokens in the formatted prompt",
    ["formatter_type"],
    buckets=TOKEN_COUNT_BUCKETS,
)


@dataclass
class PromptMetrics:
    formatter_type: str
    system_prompt_tokens: int = 0
    cur_message_tokens: int = 0
    tool_result_tokens: int = 0
    selected_code_tokens: int = 0
    chat_history_tokens: int = 0


def record_prompt_metrics(
    prompt_metrics: PromptMetrics,
) -> None:
    SYSTEM_PROMPT_TOKENS.labels(prompt_metrics.formatter_type).observe(
        prompt_metrics.system_prompt_tokens
    )
    CUR_MESSAGE_TOKENS.labels(prompt_metrics.formatter_type).observe(
        prompt_metrics.cur_message_tokens
    )
    TOOL_RESULT_TOKENS.labels(prompt_metrics.formatter_type).observe(
        prompt_metrics.tool_result_tokens
    )
    SELECTED_CODE_TOKENS.labels(prompt_metrics.formatter_type).observe(
        prompt_metrics.selected_code_tokens
    )
    CHAT_HISTORY_TOKENS.labels(prompt_metrics.formatter_type).observe(
        prompt_metrics.chat_history_tokens
    )
    TOTAL_PROMPT_TOKENS.labels(prompt_metrics.formatter_type).observe(
        prompt_metrics.system_prompt_tokens
        + prompt_metrics.cur_message_tokens
        + prompt_metrics.tool_result_tokens
        + prompt_metrics.selected_code_tokens
        + prompt_metrics.chat_history_tokens
    )
