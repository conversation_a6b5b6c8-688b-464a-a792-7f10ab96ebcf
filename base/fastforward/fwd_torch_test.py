"""Tests for base.fastforward.fwd_torch."""

import torch

from base.fastforward import fwd_torch


def test_getitem():
    tensor = torch.arange(100).reshape(5, -1)
    logits = fwd_torch.TorchLogits2D(tensor)
    torch.testing.assert_close(logits[2:3].checked_cast(torch.Tensor), tensor[2:3])
    torch.testing.assert_close(logits[None].checked_cast(torch.Tensor), tensor)

    embedding = fwd_torch.TorchEmbedding(tensor)
    torch.testing.assert_close(embedding[2:3].checked_cast(torch.Tensor), tensor[2:3])
    torch.testing.assert_close(embedding[None].checked_cast(torch.Tensor), tensor)


def test_len():
    tensor = torch.arange(100).reshape(5, -1)
    logits = fwd_torch.TorchLogits2D(tensor)
    assert len(logits) == 5
    embedding = fwd_torch.TorchEmbedding(tensor)
    assert len(embedding) == 5
