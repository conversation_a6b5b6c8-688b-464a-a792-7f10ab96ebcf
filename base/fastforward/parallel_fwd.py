"""Tools to run fastforward models with parallelism in inference.

This is a fork of parallel.py that bakes in assumptions and optimizations that
the generic utility made inconvenient, primarily the caching of the attention
object to avoid pickling it and sending it over the wire every time.
Refactoring it into a wrapper around parallel.py so they can share
implementation again is a TODO.
"""

# For some reason, the use of `|` does not work without the future import. This is probably
# because of mp.SimpleQueue is a function instead of a class.
from __future__ import annotations

from base.fastforward.cached_attention import Attention
import base.fastforward.fwd as fwd
from base.fastforward.parallel import ParallelRunnerException, _init_process
import _thread
import dataclasses
import queue
import sys
import threading
from typing import Any, Callable, Dict, Generic, Sequence, TypeVar

import torch
import torch.distributed as dist
import torch.multiprocessing as mp

# TODO:
# - figure out simulating multiple devices for testing


@dataclasses.dataclass(frozen=True)
class _RunnerArgs:
    """Arguments for the child processes."""

    tokens: Sequence[int]
    attn: Attention | None
    idx: int

    alive_probe: bool = False
    """If the child is called with the alive_probe set to true,
    it must answer immediately with _RunnerResult with both fields set to None.
    """


@dataclasses.dataclass(frozen=True)
class _RunnerResult:
    """Results from the child processes."""

    result: fwd.ModelOutput | None
    exception: Exception | None


def _run_child(
    process_idx: int,
    num_processes: int,
    args_queue: mp.JoinableQueue,
    kill_signal_queue: mp.SimpleQueue | None,
    result_queue: mp.SimpleQueue,
    init_fns: list[Callable[..., fwd.ForwardStepFn]],
    init_args: list[dict[str, Any]],
    verbose: bool = False,
):
    """Start the processing loop on a child process.

    Listens to calls via the queue. The function that the arguments are passed
      to is created via init_fn(**init_args).

    Args:
        process_idx: Index of this process.
        num_processes: Number of processes in this group.
        args_queue: to be used to receive arguments for the next call from the main process.
        kill_signal_queue: to be used to receive kill signals from the main process.
        result_queue: to be used to send results back to the main process.
        init_fn: Factory function that creates a function. Is called in the
            current process and all child processes. Can be used to load models.
        init_args: dictionary of keyword arguments passed to init_fn.
        verbose: If true, print occasional sign of life messages.
    """
    _init_process(process_idx=process_idx, num_processes=num_processes)

    if kill_signal_queue is not None:
        print(
            f"{process_idx=}: Creating thread to listen to the kill signal.", flush=True
        )

        def kill_listener():
            kill_signal_queue.get()
            print(f"{process_idx=}: Received kill signal. Exiting.", flush=True)
            _thread.interrupt_main()  # raises KeyboardInterrupt in the main thread

        kill_thread = threading.Thread(target=kill_listener)
        kill_thread.start()

    print(f"{process_idx=}: Calling given init_fn.", flush=True)
    run_fns = []
    for init_fn, args in zip(init_fns, init_args):
        print(f"{process_idx=}: Calling an init_fn.", flush=True)
        run_fns.append(
            init_fn(**args, process_idx=process_idx, num_processes=num_processes)
        )
        print(f"{process_idx=}: Called an init_fn.", flush=True)

    del init_args
    print(f"{process_idx=}: Starting listening loop.", flush=True)

    stashed_attn = None
    try:
        while True:
            # loop around queue.get with timeout to get occasional sign of life in the logs.
            while True:
                try:
                    packed_args = args_queue.get(
                        timeout=5.0
                    )  # raises queue.Empty on timeout
                    # We are using a JoinableQueue; task_done() signals to the sending
                    # process that the object was received, which is checked by the
                    # receiving process via join().
                    args_queue.task_done()
                    assert isinstance(packed_args, _RunnerArgs)
                    break  # break out of the loop waiting for new inputs
                except queue.Empty:
                    if verbose:
                        print(
                            f"{process_idx=}: No inputs received. Process is waiting for new inputs.",
                            flush=True,
                        )
            if packed_args.alive_probe:
                print(
                    f"{process_idx=}: Received alive probe.",
                    flush=True,
                )
                result_queue.put(_RunnerResult(None, None))
                continue

            attn = packed_args.attn
            if attn is None:
                assert stashed_attn is not None
                attn = stashed_attn
            else:
                stashed_attn = attn
            try:
                result = run_fns[packed_args.idx](packed_args.tokens, attn)
            except Exception as e:  # pylint: disable=broad-exception-caught
                print(
                    f"Exception while running the function in the child process: {e}. {process_idx=}",
                    flush=True,
                )
                if process_idx == 0:
                    # TODO(markus): improve error reporting to the main process. Currently, we
                    # only report the exceptions from process 0 to the main process.
                    result_queue.put(_RunnerResult(None, e))
                raise
            del packed_args  # no need to keep args around
            if process_idx == 0:
                result_queue.put(_RunnerResult(result, None))
    except KeyboardInterrupt:
        print(f"{process_idx=}: Received KeyboardInterrupt. Exiting.", flush=True)
        # free up the process group so that we can initialize it again later.
        dist.barrier()
        dist.destroy_process_group()
        print(f"{process_idx=}: Destroyed process group. Exiting.", flush=True)
        sys.exit(0)


class ParallelForwardRunner:
    """Runs a function in multiple processes in parallel.

    For an example, study the use of compute_sum in parallel_test.py.
    """

    def __init__(self, num_processes: int):
        self.num_processes = num_processes  # world_size
        self._processes = []  # to store running processes
        self._queues = []
        self._kill_signal_queues = []
        self._recv_queues = []
        self._current_attn_id: str = "NOT YET SET"
        if num_processes > torch.cuda.device_count():
            raise RuntimeError(
                f"Only {torch.cuda.device_count()} GPUs found. Cannot instantiate {num_processes} processes."
            )
        if num_processes < 1:
            raise ValueError(f"{num_processes=} is invalid.")

    def __del__(self):
        """Ensures that all processes are shut down as the decorated function dies."""
        if self._processes is not None:
            for idx, _ in enumerate(self._processes):
                print(f"Terminating process {idx}.")
                self._kill_signal_queues[idx].put(None)
            for idx, _ in enumerate(self._processes):
                self._processes[idx].terminate()
                self._processes[idx].join()

    def _wait_for_initialization_complete(self):
        print("Probing for alive signal...")
        for q in self._queues:
            q.put(_RunnerArgs([], None, 0, alive_probe=True))
        print("Listening for alive signal response...")
        for idx, q in enumerate(self._recv_queues):
            result = q.get()
            if result.exception is not None:
                raise ParallelRunnerException from result.exception
            if result.result is not None:
                raise RuntimeError(f"Process {idx} did not answer alive signal.")
        print("Parallel initialization complete.", flush=True)

    def initialize(
        self,
        init_fns: list[Callable[..., fwd.ForwardStepFn]],
        init_args: list[dict[str, Any]],
        predicate: Callable[[Sequence[int], Attention], int] = lambda tokens, attn: 0,
    ) -> fwd.ForwardStepFn:
        """Initializes model parallel function.

        Args:
            init_fn: Function that is called each process with init_args.
                init_fn must have the following the arguments process_idx
                and num_processes.
            init_args: Dictionary of keyword arguments passed to init_fn.

        Returns:
            A function that will be executed in each process with arguments
            P whenever the function is called in the main process with
            arguments P.
        """
        print("Initialize multiprocessing...")
        if self._processes:
            raise RuntimeError("ParallelRunner already initialized.")

        ctx = mp.get_context("spawn")
        self._recv_queues = [ctx.SimpleQueue() for _ in range(self.num_processes)]
        for process_idx in range(self.num_processes):
            send_q = ctx.JoinableQueue()
            kill_signal_queue = ctx.SimpleQueue()
            p = ctx.Process(
                target=_run_child,
                args=(
                    process_idx,
                    self.num_processes,
                    send_q,
                    kill_signal_queue,
                    self._recv_queues[process_idx],
                    init_fns,
                    init_args,
                ),
                # NOTE: We cannot use daemonic processes because some
                # libraries (e.g. torch.compile) use child processes
                # themselves, which does not work for daemonic processes. We
                # need to find another way to shut down child processes when the
                # main process dies.
                daemon=False,
            )
            p.start()
            self._processes.append(p)
            self._queues.append(send_q)
            self._kill_signal_queues.append(kill_signal_queue)

        def wrapped_fn(tokens: Sequence[int], attn: Attention) -> fwd.ModelOutput:
            should_send_attn = attn.get_id() != self._current_attn_id
            if should_send_attn:
                self._current_attn_id = attn.get_id()
            else:
                # Need to synchronize if we don't resend the attention, since we no longer get the
                # tensor ordering constraints that torch multiprocessing generates.
                torch.cuda.synchronize()

            assert self._queues is not None
            for q in self._queues:
                q.put(
                    _RunnerArgs(
                        tokens,
                        attn if should_send_attn else None,
                        predicate(tokens, attn),
                    ),
                    block=True,
                )

            # Wait for all arguments to be pickled because Python is allowed to pickle
            # in a background thread. Because this function only blocks on process 0, if
            # process 0 finishes sooner than the rest, then the parent process could execute
            # some code that modifies the arguments in parallel with the pickling process.
            # One could argue that process 0 needs to synchronize with other processes to
            # create its result. However, because tensors operations can happen asynchronously to the
            # CPU, we are not sure that this synchronization must happen before a tensor
            # is returned from process 0.
            #
            # The implementation actually waits for the remote side to have received
            # and acknowledged the object, which is a superset of waiting for the pickle to
            # complete
            for q in self._queues:
                q.join()

            # we only gather the result from process 0
            result = self._recv_queues[0].get()

            if result.exception is not None:
                raise ParallelRunnerException from result.exception
            return result.result

        self._wait_for_initialization_complete()

        return wrapped_fn
