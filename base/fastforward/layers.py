"""Implementation of key layers used the models.

PAIRED BARRIERS: For fastforward all reduce, GPUs must
synchronize before an input buffer is reused. Otherwise,
one GPU might still be reading the input buffer.

Rather than explicitly synchronize with a barrier, the all reduce
that happens on attention is synchronized by the all reduce that happens
at the end of MLP. The all reduce that happens at the end of MLP is
synchronized by the all reduce that happens at the next layer's attention.

To accomplish this, we require two all reduce kits, one for attention and one for MLP.

More formally, label the allreduce calls attn_i and mlp_i for i \in [0...n-1] (n layers of the model)

The synchronization to re-use input buffers safely is as follows:
- Each mlp_i is guarded by the barrier at attn_i
- Each attn_i, i > 0 is guarded by mlp_{i-1}
  In the base case, attn_0 is guarded by mlp_{n-1} of the previous iteration
- For the last layer (where there is no next step, only the result of GPU 0 matters.
  If other GPUs calculate the wrong value, it will be discarded.
"""

import functools
import logging
from typing import Callable, Optional, Tuple, Union

import torch
import torch.distributed as dist
import torch.nn as nn
import torch.nn.functional as F

from base.fastforward import all_reduce, cached_attention, torch_utils
from base.fastforward.parallel import ParallelContext

Device = Union[torch.device, str]
SplitHeadModes = cached_attention.SplitHeadModes
logger = logging.getLogger(__name__)


def _layernorm(
    w: torch.Tensor,
    b: Optional[torch.Tensor] = None,
    eps: float = 1e-6,
):
    state_dict = {"weight": w}
    if b is not None:
        state_dict["bias"] = b
    return torch_utils.init_with_weights(
        nn.LayerNorm,
        state_dict,
        w.shape[0],
        eps=eps,
    )


def _linear(
    w: torch.Tensor,
    b: Optional[torch.Tensor] = None,
) -> nn.Linear:
    """Convert a weight and bias to a Linear layer."""
    # NOTE(arun): cuBLAS likes it when the weights are transposed.
    state_dict = {"weight": w}
    if b is not None:
        state_dict["bias"] = b

    return torch_utils.init_with_weights(
        nn.Linear,
        state_dict,
        w.shape[1],
        w.shape[0],
        bias=b is not None,
    )


class QKV(nn.Module):
    """Implementation of the QKV pre-attention calculation with CUDA graph capture."""

    def __init__(
        self,
        weight_input_layernorm: torch.Tensor,
        weight_attention_query: torch.Tensor,
        weight_attention_key_value: torch.Tensor,
        bias_input_layernorm: torch.Tensor,
        bias_attention_query: torch.Tensor,
        bias_attention_key_value: torch.Tensor,
        layernorm_eps: float,
        process_idx: int = 0,
    ):
        """Constructs a new MLP object."""
        super().__init__()

        self.input_layernorm = _layernorm(
            weight_input_layernorm, bias_input_layernorm, eps=layernorm_eps
        )

        self.linear_qkv = _linear(
            w=torch.cat([weight_attention_query, weight_attention_key_value], dim=0),
            b=torch.cat([bias_attention_query, bias_attention_key_value], dim=0),
        )
        assert self.linear_qkv.bias.dtype == torch.float16
        self.process_idx = process_idx

    @torch.inference_mode()
    def forward(self, emb: torch.Tensor) -> torch.Tensor:
        with torch_utils.ProfilingContext("qkv"):
            emb_ln1 = self.input_layernorm(emb)
            out = self.linear_qkv(emb_ln1)
        assert len(out.shape) == 2
        return out


class AttnOutMLP(nn.Module):
    """Attention output projection, attention residual, layer norm, and MLP."""

    def __init__(
        self,
        weight_dense_h_to_4h: torch.Tensor,
        weight_dense_4h_to_h: torch.Tensor,
        weight_attention_dense: torch.Tensor,
        weight_post_attention_layernorm: torch.Tensor,
        activation_fn: Callable[[torch.Tensor], torch.Tensor],
        bias_dense_h_to_4h: torch.Tensor,
        bias_dense_4h_to_h: torch.Tensor,
        bias_attention_dense: torch.Tensor,
        bias_post_attention_layernorm: torch.Tensor,
        layernorm_eps: float,
        process_idx: int = 0,
        num_processes: int = 1,
        reduce: bool = True,
    ):
        """Construct a new MLP object.

        Args:
            weight_dense_h_to_4h: weights of shape (h, 4h)
            weight_dense_4h_to_h: weights of shape (4h, h)
            weight_attention_dense: weights of shape (h, h), output projection of the attention operation
            weight_post_attention_layernorm: weights of shape (h,), layer norm applied pre MLP
            activation_fn: activation function to use
            bias_dense_h_to_4h: shape (4 * h,)
            bias_dense_4h_to_h: shape (h,)
            bias_attention_dense: shape (h,)
            bias_post_attention_layernorm: shape (h,)
            process_idx: rank of the inference process
            num_processes: number of inference processes
            reduce: Whether to reduce-add in case of model parallel execution.
                Ignored if run on a single device. Can be helpful for tests.
        """
        super().__init__()

        shard = functools.partial(
            torch_utils.shard, process_idx=process_idx, num_processes=num_processes
        )
        self.linear_hto4h = _linear(
            shard(weight_dense_h_to_4h, axis=0), shard(bias_dense_h_to_4h, axis=0)
        )
        self.linear_4htoh = _linear(
            shard(weight_dense_4h_to_h, axis=1),
            # This is a tricky case: we only want to add the bias in one of the processes.
            # Otherwise we would add the bias multiple times. The other options here are
            # to move the bias out of the linear operation and handle it explicitly, or
            # to shard the bias as well. There is no clear advantage of either of these.
            bias_dense_4h_to_h if process_idx == 0 else None,
        )
        self.bias_dense_4h_to_h = bias_dense_4h_to_h
        self.linear_attention_dense = _linear(
            weight_attention_dense,
            bias_attention_dense,
        )
        self.post_attention_layernorm = _layernorm(
            weight_post_attention_layernorm,
            bias_post_attention_layernorm,
            eps=layernorm_eps,
        )
        self.activation_fn = activation_fn
        self.process_idx = process_idx
        self.num_processes = num_processes
        self.reduce = reduce

    @torch.inference_mode()
    def forward(self, emb: torch.Tensor, attn: torch.Tensor) -> torch.Tensor:
        with torch_utils.ProfilingContext("attnout"):
            emb += self.linear_attention_dense(attn)
        with torch_utils.ProfilingContext("mlp"):
            emb_ln2 = self.post_attention_layernorm(emb)
            intermediate = self.linear_hto4h(emb_ln2)
            intermediate = self.activation_fn(intermediate)
            out = self.linear_4htoh(intermediate)
            if self.num_processes > 1 and self.reduce:
                dist.all_reduce(out, op=dist.ReduceOp.SUM)
            emb += out
        return emb


class CrossLayer(nn.Module):
    """Everything between two attention calls; spans two layers."""

    def __init__(
        self,
        weight_dense_h_to_4h: torch.Tensor,
        weight_dense_4h_to_h: torch.Tensor,
        weight_attention_dense: torch.Tensor,
        weight_post_attention_layernorm: torch.Tensor,
        activation_fn: Callable[[torch.Tensor], torch.Tensor],
        bias_dense_h_to_4h: torch.Tensor,
        bias_dense_4h_to_h: torch.Tensor,
        bias_attention_dense: torch.Tensor,
        bias_post_attention_layernorm: torch.Tensor,
        #
        weight_input_layernorm: torch.Tensor,
        bias_input_layernorm: torch.Tensor,
        weight_attention_query: torch.Tensor,
        bias_attention_query: torch.Tensor,
        weight_attention_key_value: torch.Tensor,
        bias_attention_key_value: torch.Tensor,
        #
        layernorm_eps: float,
        #
        process_idx: int = 0,
        num_processes: int = 1,
        reduce: bool = True,
    ):
        """Constructs a new MLP object.

        Args:
            weight_dense_h_to_4h: weights of shape (h, 4h)
            weight_dense_4h_to_h: weights of shape (4h, h)
            weight_attention_dense: weights of shape (h, h), output projection of the attention operation
            weight_post_attention_layernorm: weights of shape (h,), layer norm applied pre MLP
            activation_fn: activation function to use
            bias_dense_h_to_4h: shape (4 * h,)
            bias_dense_4h_to_h: shape (h,)
            bias_attention_dense: shape (h,)
            bias_post_attention_layernorm: shape (h,)

            weight_input_layernorm: Weight for the attention layer norm
            bias_input_layernorm: Bias for the attention layer norm
            weight_attention_query: Query projection
            bias_attention_query: Bias for the query projection
            weight_attention_key_value: Key-value projection
            bias_attention_key_value: Bias for the key-value projection

            process_idx: rank of the inference process
            num_processes: number of inference processes
            reduce: Whether to reduce-add in case of model parallel execution.
                Ignored if run on a single device.
        """
        super().__init__()

        self.mlp = AttnOutMLP(
            weight_dense_h_to_4h=weight_dense_h_to_4h,
            weight_dense_4h_to_h=weight_dense_4h_to_h,
            bias_dense_h_to_4h=bias_dense_h_to_4h,
            bias_dense_4h_to_h=bias_dense_4h_to_h,
            weight_attention_dense=weight_attention_dense,
            weight_post_attention_layernorm=weight_post_attention_layernorm,
            bias_attention_dense=bias_attention_dense,
            bias_post_attention_layernorm=bias_post_attention_layernorm,
            activation_fn=activation_fn,
            layernorm_eps=layernorm_eps,
            process_idx=process_idx,
            num_processes=num_processes,
            reduce=reduce,
        )
        self.qkv = QKV(
            weight_input_layernorm=weight_input_layernorm,
            weight_attention_query=weight_attention_query,
            weight_attention_key_value=weight_attention_key_value,
            bias_input_layernorm=bias_input_layernorm,
            bias_attention_query=bias_attention_query,
            bias_attention_key_value=bias_attention_key_value,
            layernorm_eps=layernorm_eps,
            process_idx=process_idx,
        )

    @torch.inference_mode()
    def forward(
        self, emb: torch.Tensor, attn: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        with torch_utils.ProfilingContext("cross"):
            emb = self.mlp(emb, attn)
            qkv_out = self.qkv(emb)
        return emb, qkv_out


def _initial_embeddings(
    word_embeddings: torch.Tensor,
    pos_embeddings: torch.Tensor,
    tokens: torch.Tensor,
    positions: torch.Tensor,
):
    """Compute the input + position embedding projection.

    NOTE(arun): `torch.compile` compiles this into a single kernel for us.
    """
    return word_embeddings[tokens] + pos_embeddings[positions]


class InitialEmbeddings(nn.Module):
    """Initial embedding layer (wraps around word and position embeddings)."""

    def __init__(
        self,
        vocab_size: int = 51200,
        positions_size: int = 8192,
        embedding_dim: int = 2048,
        device: Device = "cuda",
    ):
        super().__init__()

        self.word_embeddings = nn.Parameter(
            torch.zeros(vocab_size, embedding_dim, device=device, dtype=torch.float16),
            requires_grad=False,
        )
        self.position_embeddings = nn.Parameter(
            torch.zeros(
                positions_size, embedding_dim, device=device, dtype=torch.float16
            ),
            requires_grad=False,
        )

    @classmethod
    def from_weights(
        cls,
        word_embeddings: torch.Tensor,
        position_embeddings: torch.Tensor,
    ):
        """Load initial embeddings from the weights in a checkpoint."""

        vocab_size, embedding_dim = word_embeddings.shape
        positions_size, embedding_dim_ = position_embeddings.shape
        assert (
            embedding_dim == embedding_dim_
        ), f"Word embeddings ({embedding_dim}) don't match position embeddings ({embedding_dim_})."
        return torch_utils.init_with_weights(
            cls,
            {
                "word_embeddings": word_embeddings,
                "position_embeddings": position_embeddings,
            },
            vocab_size,
            positions_size,
            embedding_dim,
        )

    @torch.inference_mode()
    def forward(self, tokens: torch.Tensor, positions: torch.Tensor) -> torch.Tensor:
        return _initial_embeddings(
            word_embeddings=self.word_embeddings,
            pos_embeddings=self.position_embeddings,
            tokens=tokens.to(positions.device),
            positions=positions,
        )


class RmsNorm(nn.Module):
    """Root-Mean-Square normalization."""

    def __init__(
        self,
        inp_dim: int,
        eps: float = 1e-6,
        in_place: bool = False,
        cast_to_fp32: bool = False,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
    ):
        super().__init__()
        self._device = device

        self._inp_dim = inp_dim
        self._in_place = in_place
        self._eps = eps
        self._cast_to_fp32 = cast_to_fp32

        weight = torch.zeros(inp_dim, dtype=dtype, device=device)
        self.weight = nn.Parameter(weight, requires_grad=False)

    @torch.inference_mode()
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        original_dtype = x.dtype
        if self._cast_to_fp32:
            x = x.to(torch.float32)
        n = x.square().mean(dim=-1, keepdim=True).add_(self._eps).rsqrt_()
        x = x.mul_(n) if self._in_place else x.mul(n)
        x = x.mul_(self.weight)
        x = x.to(
            original_dtype
        )  # cast back to fp16 AFTER multiplying by weight for more numerical stability
        return x


class WordEmbeddings(nn.Module):
    """Word embeddings for LLaMA, DeepSeek, and DBRX."""

    def __init__(
        self,
        vocab_size: int,
        emb_dim: int,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
    ):
        super().__init__()
        self._device = device
        self._vocab_size = vocab_size
        self._emb_dim = emb_dim
        self.word_embs = nn.Parameter(
            torch.zeros(vocab_size, emb_dim, device=device, dtype=dtype),
            requires_grad=False,
        )

    @torch.inference_mode()
    def forward(self, tokens: torch.Tensor) -> torch.Tensor:
        return F.embedding(tokens, self.word_embs)


class LlamaAttention(nn.Module):
    """Projection into heads -> QKV -> projection back -> residual.

    NOTE(Xuanyi): this module is used for LLAMA, DeepSeek, DBRX, and StarCoder2.
        The DBRX used the clip_qkv of 8.0, and others did not use clip_qkv.

    NOTE(hieu): on tensor parallelism.

    We shard the attention heads (KV or Q) if required. Here are the logics:

        1. If split_head_mode == KV_HEADS:
            (this is probably the case for GQA or MHA)

            Each device holds (num_heads_kv // num_processes) KV-heads,
            along with their corresponding Q-heads.

        2. If split_head_mode == Q_PER_HEADS:
            (this is probably the case for MQA or GQA with few heads)

            Each device holds all KV-heads, along with a contiguous block
            of (num_heads_q // num_processes) Q-heads.

            For example, if there are 2 devices, then:
                - let q = num_heads_q // num_processes
                - device 0 holds heads 0, 1, ..., q-1
                - device 1 holds heads q, q+1, ..., 2q-1

        In both cases, qkv.weight and out.weight will be sharded accordingly,
        so that exactly final reduce_sum is required at the end to guarantee
        correctness.

        The sharding logic needs to be implemented manually if a user of this
        class wants to convert the weights from a non-tensor-parallelism
        version. We baked this weight sharding logic into:

            fwd_llama.py::_generate_step_fn::_weight_fn

    NOTE: QK Normalization Support

    This class now supports optional QK normalization (as used in Qwen3 models).
    When use_qk_norm=True, RMSNorm is applied to Q and K tensors after QKV projection
    but before attention computation. The V tensor remains unchanged.
    """

    def __init__(
        self,
        emb_dim: int,
        num_heads_q: int,
        num_heads_kv: int,
        head_dim: int,
        use_bias: bool = False,
        qkv_only_bias: bool = False,
        split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT,
        clip_qkv: Optional[float] = None,
        use_qk_norm: bool = False,
        qk_norm_eps: float | None = None,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        all_reduce_kit: all_reduce.AllReduceKit | None = None,
    ):
        super().__init__()
        if use_bias and qkv_only_bias:
            raise ValueError("Cannot use both use_bias and qkv_only_bias.")

        self._device = device
        self._parallel_ctx = parallel_ctx
        self._use_tensor_parallelism = (
            split_head_mode != SplitHeadModes.NO_SPLIT and parallel_ctx.tp_size > 1
        )
        self._split_head_mode = split_head_mode
        self._is_tp_rank_0 = parallel_ctx.tp_rank == 0
        self._clip_qkv = clip_qkv
        self._use_qk_norm = use_qk_norm
        self._head_dim = head_dim

        if num_heads_q % num_heads_kv:
            raise ValueError(f"{num_heads_q=} is not divisible by {num_heads_kv=}.")

        if self._use_tensor_parallelism:
            num_heads_q, num_heads_kv = cached_attention.split_heads(
                num_heads_q, num_heads_kv, parallel_ctx.cfg, split_head_mode
            )

        # Store local head counts (after tensor parallelism adjustment)
        self._num_heads_q = num_heads_q
        self._num_heads_kv = num_heads_kv

        self.qkv = nn.Linear(
            in_features=emb_dim,
            out_features=(num_heads_q + 2 * num_heads_kv) * head_dim,
            bias=use_bias or qkv_only_bias,
            device=device,
            dtype=dtype,
        )

        # Initialize QK normalization layers if enabled
        if self._use_qk_norm:
            assert qk_norm_eps is not None, "qk_norm_eps must be specified."
            self.q_norm = RmsNorm(
                inp_dim=head_dim,
                eps=qk_norm_eps,
                dtype=dtype,
                cast_to_fp32=True,
                device=device,
            )
            self.k_norm = RmsNorm(
                inp_dim=head_dim,
                eps=qk_norm_eps,
                dtype=dtype,
                cast_to_fp32=True,
                device=device,
            )

        # NOTE(add-output-bias): there is an all_reduce of the output activations when TP is
        # enabled and split_head_mode _isn't_ NO_SPLIT. In this case, only rank 0 adds the output
        # bias, since the other ranks will pick it up from the all_reduce. BUT in the NO_SPLIT
        # case, there is no all_reduce, so every rank needs to add the output bias.
        my_rank_should_add_output_bias = (
            not self._use_tensor_parallelism or self._is_tp_rank_0
        )
        self.out = nn.Linear(
            in_features=num_heads_q * head_dim,
            out_features=emb_dim,
            bias=use_bias and my_rank_should_add_output_bias,
            device=device,
            dtype=dtype,
        )

        self._all_reduce_kit = all_reduce_kit

    @torch.inference_mode()
    def forward(
        self, inputs: torch.Tensor, attn: cached_attention.Attention, layer_idx: int
    ) -> torch.Tensor:
        x = inputs
        x = self.qkv(x)
        if self._clip_qkv is not None:
            x = x.clamp(-self._clip_qkv, self._clip_qkv)

        # Apply QK normalization if enabled
        if self._use_qk_norm:
            x = self._apply_qk_norm(x)

        x = attn(qkv=x, layer_idx=layer_idx, parallel_ctx=self._parallel_ctx)
        x = self.out(x)
        if self._use_tensor_parallelism:
            if self._all_reduce_kit is not None:
                self._all_reduce_kit.get_input_tensor(x.shape, x.dtype).copy_(x)
                self._all_reduce_kit.all_reduce(out=x)
            else:
                dist.all_reduce(
                    x, op=dist.ReduceOp.SUM, group=self._parallel_ctx.tp_group
                )
        return x

    def _apply_qk_norm(self, qkv: torch.Tensor) -> torch.Tensor:
        """Apply RMS normalization to Q and K components of QKV tensor.

        The QKV tensor uses a grouped layout where for each KV head group:
        [Q heads for this KV group, K head, V head]

        This implementation uses in-place operations to minimize memory bandwidth
        by avoiding split/concatenation operations. This is safe in FastForward
        since autograd is not used during inference.

        Performance optimization rationale:
        Since FastForward is inference-only and doesn't require autograd, we can use
        in-place operations without worrying about breaking gradient computation. This
        enables a more efficient implementation:

        1. No clone() needed - saves memory bandwidth by avoiding defensive copies
        2. narrow() creates lightweight views without data movement (vs split() which
           may create new tensors depending on memory layout)
        3. copy_() writes normalized values directly back to original memory locations
        4. Eliminates torch.cat() which would require allocating new memory for the
           entire QKV tensor and copying all data (including unchanged V components)

        Args:
            qkv: Combined QKV tensor of shape [seq_len, (num_heads_q + 2 * num_heads_kv) * head_dim]

        Returns:
            QKV tensor with normalized Q and K components (modified in-place)
        """
        seq_len = qkv.shape[0]

        # Handle empty input
        if seq_len == 0:
            # Log only from process 0 to avoid spam in multi-GPU setups
            if self._parallel_ctx.process_idx == 0:
                logger.debug("QK normalization received empty sequence (seq_len=0).")
            return qkv

        # Calculate queries per KV head
        queries_per_kv_head = self._num_heads_q // self._num_heads_kv

        # Reshape to grouped format: [seq_len, num_heads_kv, queries_per_kv_head + 2, head_dim]
        qkv_grouped = qkv.view(
            seq_len, self._num_heads_kv, queries_per_kv_head + 2, self._head_dim
        )

        # Use narrow to create views of Q and K portions without splitting
        # narrow(dim, start, length) creates a view without copying data
        # This avoids the overhead of split() and later cat() operations

        # Apply normalization in-place using narrow (creates views without copying)
        q_portion = qkv_grouped.narrow(2, 0, queries_per_kv_head)
        k_portion = qkv_grouped.narrow(2, queries_per_kv_head, 1)

        # V head remains untouched at index queries_per_kv_head + 1

        # Normalize and write back in-place
        q_portion.copy_(
            self.q_norm(q_portion.reshape(-1, self._head_dim)).view_as(q_portion)
        )
        k_portion.copy_(
            self.k_norm(k_portion.reshape(-1, self._head_dim)).view_as(k_portion)
        )

        # Return the input tensor which has been modified in-place
        return qkv


def swiglu(x: torch.Tensor) -> torch.Tensor:
    """In-place SwiGLU.

    We use this instead of two separate matmuls to match the fused
    implementation in FP8. This makes checkpoint quantizations much easier.

    Args:
        x: `num_tokens, 2 * mlp_dim`.

    Returns:
        Computes `SILU(x[:, :mlp_dim]) * x[:, mlp_dim:]`.
    """
    act_func, identity = x.chunk(2, dim=1)
    return F.silu(act_func).mul_(identity)


class LlamaSwiGLU(nn.Module):
    """RMSNorm -> silu(F_1(x)) * F_3(x) -> projection back -> residual."""

    _all_reduce_kit: all_reduce.AllReduceKit | None = None

    def __init__(
        self,
        emb_dim: int,
        mlp_dim: int,
        use_bias: bool = False,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        all_reduce_kit: all_reduce.AllReduceKit | None = None,
    ):
        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx
        self._is_tp_rank_0 = parallel_ctx.tp_rank == 0

        if mlp_dim % parallel_ctx.tp_size:
            raise ValueError(f"{mlp_dim=} is not divisible by {parallel_ctx.tp_size=}.")

        mlp_dim_per_device = mlp_dim // parallel_ctx.tp_size

        self.expand = nn.Linear(
            in_features=emb_dim,
            out_features=mlp_dim_per_device * 2,
            bias=use_bias,
            device=device,
            dtype=dtype,
        )

        self.shrink = nn.Linear(
            in_features=mlp_dim_per_device,
            out_features=emb_dim,
            bias=use_bias and self._is_tp_rank_0,
            device=device,
            dtype=dtype,
        )

        self._all_reduce_kit = all_reduce_kit

    @torch.inference_mode()
    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        x = self.expand(inputs)
        x = swiglu(x)
        x = self.shrink(x)
        if self._parallel_ctx.tp_size > 1:
            if self._all_reduce_kit is not None:
                self._all_reduce_kit.get_input_tensor(x.shape, x.dtype).copy_(x)
                self._all_reduce_kit.all_reduce(out=x)
            else:
                dist.all_reduce(
                    x, op=dist.ReduceOp.SUM, group=self._parallel_ctx.tp_group
                )
        return x


class LlamaTransformerBlock(nn.Module):
    """Computes: input -> norm -> attn -> residual -> norm -> swiglu -> residual."""

    def __init__(
        self,
        emb_dim: int,
        num_heads_q: int,
        num_heads_kv: int,
        head_dim: int,
        mlp_dim: int,
        norm_eps: float = 1e-5,
        split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT,
        use_bias: bool = False,
        qkv_only_bias: bool = False,
        use_qk_norm: bool = False,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
        ffn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
    ):
        assert (ffn_all_reduce_kit is None) == (attn_all_reduce_kit is None), (
            "ffn_all_reduce_kit and attn_all_reduce_kit must be both None or both "
            "not None. See PAIRED BARRIERS in layers.py."
        )
        assert (ffn_all_reduce_kit is None) or (
            attn_all_reduce_kit != ffn_all_reduce_kit
        ), "Need different kits for attn and ffn. See PAIRED BARRIERS in layers.py."
        if use_bias and qkv_only_bias:
            raise ValueError("Cannot use both use_bias and qkv_only_bias.")

        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx

        self.attn_norm = RmsNorm(
            inp_dim=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            cast_to_fp32=True,
            device=device,
        )
        self.attn = LlamaAttention(
            emb_dim=emb_dim,
            num_heads_q=num_heads_q,
            num_heads_kv=num_heads_kv,
            head_dim=head_dim,
            use_bias=use_bias,
            qkv_only_bias=qkv_only_bias,
            split_head_mode=split_head_mode,
            use_qk_norm=use_qk_norm,
            qk_norm_eps=norm_eps,
            dtype=dtype,
            device=device,
            parallel_ctx=parallel_ctx,
            all_reduce_kit=attn_all_reduce_kit,
        )

        self.ffn_norm = RmsNorm(
            inp_dim=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            cast_to_fp32=True,
            device=device,
        )
        self.ffn = LlamaSwiGLU(
            emb_dim=emb_dim,
            mlp_dim=mlp_dim,
            use_bias=use_bias,
            dtype=dtype,
            device=device,
            parallel_ctx=parallel_ctx,
            all_reduce_kit=ffn_all_reduce_kit,
        )

    @torch.inference_mode()
    def forward(
        self,
        inputs: torch.Tensor,
        attn: cached_attention.Attention,
        layer_idx: int,
    ) -> torch.Tensor:
        with torch_utils.ProfilingContext(f"layer_{layer_idx}"):
            x = inputs

            with torch_utils.ProfilingContext(f"attention_{layer_idx}"):
                x = self.attn(self.attn_norm(x), attn=attn, layer_idx=layer_idx).add_(x)

            with torch_utils.ProfilingContext(f"ffn_{layer_idx}"):
                x = self.ffn(self.ffn_norm(x)).add_(x)

        return x


class DbrxFFN(nn.Module):
    """The Dbrx FFN layer, which is a mixture of multiple GLU layers with a router."""

    def __init__(
        self,
        emb_dim: int,
        mlp_dim: int,
        moe_num_experts: int,
        moe_top_k: int,
        moe_normalize_expert_weights: Optional[float] = None,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
    ):
        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx
        # These two parameters will be used in the forward pass.
        self._moe_top_k = moe_top_k
        self._moe_normalize_expert_weights = moe_normalize_expert_weights
        self._moe_num_experts = moe_num_experts
        if mlp_dim % parallel_ctx.tp_size:
            raise ValueError(f"{mlp_dim=} is not divisible by {parallel_ctx.tp_size=}.")
        # Note(Xuanyi): as the first simple version, we shard each expert in the tensor parallelism fashion.
        # In addition, we duplicate the expert to make it easier to implement the routing.
        # moe_normalize_expert_weights is a tricky parameter to set.
        # In the official DBRX implementation, it has default values of None and 1.0 in different places.
        # I tried my best to figure the correct value -> we should use 1.0, but worth to be confirmed.
        self.router = nn.Linear(
            emb_dim, moe_num_experts, bias=False, device=device, dtype=dtype
        )
        mlp_dim_per_device = mlp_dim // parallel_ctx.tp_size
        self.w1 = nn.Parameter(
            torch.zeros(
                moe_num_experts, emb_dim, mlp_dim_per_device, device=device, dtype=dtype
            ),
            requires_grad=False,
        )
        self.v1 = nn.Parameter(
            torch.zeros(
                moe_num_experts, emb_dim, mlp_dim_per_device, device=device, dtype=dtype
            ),
            requires_grad=False,
        )
        self.w2 = nn.Parameter(
            torch.zeros(
                moe_num_experts, mlp_dim_per_device, emb_dim, device=device, dtype=dtype
            ),
            requires_grad=False,
        )

    @torch.inference_mode()
    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        """The forward pass of the Dbrx FFN layer, where the inputs' shape is expected to be (seq_len, emd_dim)."""
        # Compute the router.
        router_logits = self.router(inputs)  # Shape: (seq_len, #experts)
        router_weights = F.softmax(router_logits, dim=-1, dtype=torch.float32)
        top_weights, top_experts = torch.topk(router_weights, self._moe_top_k, dim=-1)
        if self._moe_normalize_expert_weights is not None:
            top_weights = top_weights / torch.norm(  # (seq_len, #experts)
                top_weights,
                p=self._moe_normalize_expert_weights,
                dim=-1,
                keepdim=True,
            )
        router_weights: torch.Tensor = router_weights.to(inputs.dtype)
        top_weights: torch.Tensor = top_weights.to(inputs.dtype)
        # Compute the mixture of GLU layers.
        outputs = torch.zeros_like(inputs, dtype=inputs.dtype)
        expert_mask = nn.functional.one_hot(
            top_experts, num_classes=self._moe_num_experts
        ).permute(2, 1, 0)
        # expert_mask.shape == (#experts, top_k, seq_len)
        w1_chunked = self.w1.chunk(self._moe_num_experts, dim=0)
        w2_chunked = self.w2.chunk(self._moe_num_experts, dim=0)
        v1_chunked = self.v1.chunk(self._moe_num_experts, dim=0)
        w1_chunked = [w1.squeeze(0) for w1 in w1_chunked]
        w2_chunked = [w2.squeeze(0) for w2 in w2_chunked]
        v1_chunked = [v1.squeeze(0) for v1 in v1_chunked]
        for expert_idx in range(self._moe_num_experts):
            # NOTE(Xuanyi): Both of topk_idx and token_idx are (\hat{seq_len}, ).
            # As each process keeps the same router, we can use the same topk_idx for all processes.
            # And thus if one skipped an expert due to token_idx.numel() == 0, all processes should also skip that expert.
            topk_idx, token_idx = torch.nonzero(expert_mask[expert_idx], as_tuple=True)
            if token_idx.numel() == 0:
                print(f"The expert_idx={expert_idx} find no token_idx, continue")
                continue
            expert_tokens = inputs[token_idx]
            # Compute the GLU layer.
            gate_proj = F.silu(torch.matmul(expert_tokens, w1_chunked[expert_idx]))
            up_proj = torch.matmul(expert_tokens, v1_chunked[expert_idx])
            down_proj = torch.matmul(gate_proj * up_proj, w2_chunked[expert_idx])
            if self._parallel_ctx.tp_size > 1:  # sync for tensor parallelism
                dist.all_reduce(
                    down_proj, op=dist.ReduceOp.SUM, group=self._parallel_ctx.tp_group
                )
            expert_out = down_proj * top_weights[token_idx, topk_idx, None]
            outputs.index_add_(0, token_idx, expert_out)
        return outputs


class DbrxTransformerBlock(nn.Module):
    """Computes: input -> norm -> attn -> residual -> norm -> swiglu -> residual."""

    def __init__(
        self,
        emb_dim: int,
        num_heads_q: int,  # These 4 parameters are for Attention.
        num_heads_kv: int,
        head_dim: int,
        split_head_mode: SplitHeadModes,
        clip_qkv: Optional[float],
        mlp_dim: int,  # These 4 parameters are for MoE-FFN.
        moe_num_experts: int,
        moe_top_k: int,
        moe_normalize_expert_weights: Optional[float],
        norm_eps: float = 1e-5,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
    ):
        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx

        self.attn_norm = nn.LayerNorm(
            normalized_shape=emb_dim,
            eps=norm_eps,
            bias=False,
            dtype=dtype,
            device=device,
        )
        self.attn = LlamaAttention(
            emb_dim=emb_dim,
            num_heads_q=num_heads_q,
            num_heads_kv=num_heads_kv,
            head_dim=head_dim,
            use_bias=False,
            split_head_mode=split_head_mode,
            clip_qkv=clip_qkv,
            dtype=dtype,
            device=device,
            parallel_ctx=parallel_ctx,
        )

        self.ffn_norm = nn.LayerNorm(
            normalized_shape=emb_dim,
            eps=norm_eps,
            bias=False,
            dtype=dtype,
            device=device,
        )
        self.ffn = DbrxFFN(
            emb_dim=emb_dim,
            mlp_dim=mlp_dim,
            moe_num_experts=moe_num_experts,
            moe_top_k=moe_top_k,
            moe_normalize_expert_weights=moe_normalize_expert_weights,
            dtype=dtype,
            device=device,
            parallel_ctx=parallel_ctx,
        )

    @torch.inference_mode()
    def forward(
        self,
        inputs: torch.Tensor,
        attn: cached_attention.Attention,
        layer_idx: int,
    ) -> torch.Tensor:
        with torch_utils.ProfilingContext(f"layer_{layer_idx}"):
            x = inputs

            with torch_utils.ProfilingContext(f"attention_{layer_idx}"):
                residual = x
                x = self.attn_norm(x)
                x = self.attn(x, attn=attn, layer_idx=layer_idx)
                x.add_(residual)

            with torch_utils.ProfilingContext(f"ffn_{layer_idx}"):
                residual = x
                x = self.ffn_norm(x)
                x = self.ffn(x)
                x.add_(residual)
        return x


class StarCoder2FFN(nn.Module):
    """Expand -> gelu(F_1(x)) -> projection back."""

    def __init__(
        self,
        emb_dim: int,
        mlp_dim: int,
        use_bias: bool = False,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        all_reduce_kit: all_reduce.AllReduceKit | None = None,
    ):
        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx
        self._is_tp_rank_0 = parallel_ctx.tp_rank == 0
        self._all_reduce_kit = all_reduce_kit

        if mlp_dim % parallel_ctx.tp_size:
            raise ValueError(f"{mlp_dim=} is not divisible by {parallel_ctx.tp_size=}.")

        mlp_dim_per_device = mlp_dim // parallel_ctx.tp_size

        self.expand = nn.Linear(
            in_features=emb_dim,
            out_features=mlp_dim_per_device,
            bias=use_bias,
            device=device,
            dtype=dtype,
        )

        self.shrink = nn.Linear(
            in_features=mlp_dim_per_device,
            out_features=emb_dim,
            bias=use_bias and self._is_tp_rank_0,
            device=device,
            dtype=dtype,
        )

    @torch.inference_mode()
    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        x = self.expand(inputs)
        x = F.gelu(x, approximate="tanh")
        x = self.shrink(x)
        if self._parallel_ctx.tp_size > 1:
            if self._all_reduce_kit is not None:
                self._all_reduce_kit.get_input_tensor(x.shape, x.dtype).copy_(x)
                self._all_reduce_kit.all_reduce(out=x)
            else:
                dist.all_reduce(
                    x, op=dist.ReduceOp.SUM, group=self._parallel_ctx.tp_group
                )
        return x


class StarCoder2TransformerBlock(nn.Module):
    """Computes: input -> norm -> attn -> residual -> norm -> swiglu -> residual."""

    def __init__(
        self,
        emb_dim: int,
        num_heads_q: int,
        num_heads_kv: int,
        head_dim: int,
        mlp_dim: int,
        norm_eps: float = 1e-5,
        split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT,
        use_bias: bool = False,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
        ffn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
    ):
        assert (ffn_all_reduce_kit is None) == (attn_all_reduce_kit is None), (
            "ffn_all_reduce_kit and attn_all_reduce_kit must be both None or both "
            "not None. See PAIRED BARRIERS in layers.py."
        )
        assert (ffn_all_reduce_kit is None) or (
            attn_all_reduce_kit != ffn_all_reduce_kit
        ), "Need different kits for attn and ffn. See PAIRED BARRIERS in layers.py."

        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx

        self.attn_norm = nn.LayerNorm(
            normalized_shape=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            device=device,
        )
        self.attn = LlamaAttention(
            emb_dim=emb_dim,
            num_heads_q=num_heads_q,
            num_heads_kv=num_heads_kv,
            head_dim=head_dim,
            use_bias=use_bias,
            split_head_mode=split_head_mode,
            dtype=dtype,
            device=device,
            parallel_ctx=parallel_ctx,
            all_reduce_kit=attn_all_reduce_kit,
        )

        self.ffn_norm = nn.LayerNorm(
            normalized_shape=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            device=device,
        )
        self.ffn = StarCoder2FFN(
            emb_dim=emb_dim,
            mlp_dim=mlp_dim,
            use_bias=use_bias,
            dtype=dtype,
            device=device,
            parallel_ctx=parallel_ctx,
            all_reduce_kit=ffn_all_reduce_kit,
        )

    @torch.inference_mode()
    def forward(
        self,
        inputs: torch.Tensor,
        attn: cached_attention.Attention,
        layer_idx: int,
    ) -> torch.Tensor:
        with torch_utils.ProfilingContext(f"layer_{layer_idx}"):
            x = inputs

            with torch_utils.ProfilingContext(f"attention_{layer_idx}"):
                x = self.attn(self.attn_norm(x), attn=attn, layer_idx=layer_idx).add_(x)

            with torch_utils.ProfilingContext(f"ffn_{layer_idx}"):
                x = self.ffn(self.ffn_norm(x)).add_(x)

        return x
