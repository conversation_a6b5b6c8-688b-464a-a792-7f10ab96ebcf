"""Tests for base.fastforward.compiled_attention_utils."""

from typing import Sequence

import pytest
import torch

# pylint: disable=no-name-in-module
from base.fastforward.compiled_attention_utils import (
    fused_q_contiguous_kv_copy,
    multirequest_flash_copy_cache_metadata,
    multirequest_flash_copy_in_queries,
    multirequest_flash_copy_out_results,
)

# pylint: enable=no-name-in-module


def ref_copy_queries_in(
    q: torch.Tensor,
    q_buf: torch.Tensor,
    cumulative_seqlens: torch.Tensor,
):
    nrequests = cumulative_seqlens.size(0) - 1
    for req in range(nrequests):
        buf_seqlen = q_buf.size(1)
        start = cumulative_seqlens[req]
        end = cumulative_seqlens[req + 1]
        this_seqlen = end - start
        assert this_seqlen <= buf_seqlen
        q_buf[req, buf_seqlen - this_seqlen :].copy_(q[start:end])


def ref_copy_results_out(
    q_buf: torch.Tensor,
    result: torch.Tensor,
    cumulative_seqlens: torch.Tensor,
):
    nrequests = cumulative_seqlens.size(0) - 1
    buf_seqlen = q_buf.size(1)
    for req in range(nrequests):
        this_seqlen = cumulative_seqlens[req + 1] - cumulative_seqlens[req]
        assert this_seqlen <= buf_seqlen
        result[cumulative_seqlens[req] : cumulative_seqlens[req + 1], ...].copy_(
            q_buf[req, buf_seqlen - this_seqlen :, ...]
        )


def ref_copy_metadata(
    cumulative_seqlens: torch.Tensor,
    cache_idxs: torch.Tensor,
    cache_locs: torch.Tensor,
    cache_batch_idxs_buf: torch.Tensor,
    cache_seqlens_buf: torch.Tensor,
):
    nrequests = cumulative_seqlens.size(0) - 1
    for req in range(nrequests):
        this_req_len = cumulative_seqlens[req + 1] - cumulative_seqlens[req]
        if this_req_len > 0:
            cache_batch_idxs_buf[req] = cache_idxs[cumulative_seqlens[req + 1] - 1]
            cache_seqlens_buf[req] = cache_locs[cumulative_seqlens[req + 1] - 1]
        else:
            cache_batch_idxs_buf[req] = 0
            cache_seqlens_buf[req] = 0


# Helper function for creating random round structures.
# Args:
#   round_size: the total number of tokens in the round
#   num_requests: the total number of requests in the round
#   num_empty_requests: how many of those requests are padding
#   num_padding_tokens: how many padding tokens are there in the round
#   seed: fixed random seed
def random_round_cumulative_seqlens(
    round_size: int,
    num_requests: int,
    num_empty_requests: int,
    num_padding_tokens: int,
    seed: int = 1234,
) -> torch.Tensor:
    torch.manual_seed(seed)
    num_actual_tokens = round_size - num_padding_tokens
    num_actual_requests = num_requests - num_empty_requests
    # Here we compute the breakpoint indices for the requests:
    # - Take random permutation of all indices and slice off the first num_actual_requests
    request_breakpoints = torch.sort(
        torch.randperm(num_actual_tokens)[:num_actual_requests]
    ).values
    # - Shift everything over s.t. you reach the desired `num_actual_tokens`
    request_breakpoints += num_actual_tokens - request_breakpoints[-1]
    cumulative_seqlens = torch.cat(
        (
            torch.tensor([0]),  # Dummy 0 at the start
            request_breakpoints,
            torch.tensor([num_actual_tokens] * num_empty_requests),  # Padding requests
        ),
        dim=0,
    )
    return cumulative_seqlens.to(device="cuda", dtype=torch.int32)


@pytest.mark.parametrize("nheads", [1, 7, 56])
@pytest.mark.parametrize("round_size", [1, 32, 1536])
@pytest.mark.parametrize("num_padding_tokens", [0, 1, 17])
@pytest.mark.parametrize("num_padding_requests", [0, 1, 3])
@pytest.mark.parametrize("qbuf_seqlen_factor", [1, 2])
@pytest.mark.parametrize("dtype", [torch.float16, torch.bfloat16, torch.float8_e4m3fn])
@pytest.mark.parametrize("num_requests", [1, 4])
@pytest.mark.parametrize("seed", [0, 1, 2])
def test_multirequest_flash_copy_in_and_out_queries(
    nheads: int,
    round_size: int,
    num_padding_tokens: int,
    num_padding_requests: int,
    qbuf_seqlen_factor: int,
    dtype: torch.dtype,
    num_requests: int,
    seed: int,
    headdim: int = 128,
):
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)

    if num_padding_tokens >= round_size:
        # Skip cases where there are too many padding tokens
        pytest.skip("Too many padding tokens")

    if num_requests <= num_padding_requests:
        # Skip cases where there are too many padding requests
        pytest.skip("Too many padding requests")

    if (num_requests + num_padding_tokens) > round_size:
        # Skip cases where padding tokens overflow
        pytest.skip("Too many padding tokens")

    qbuf_seqlen = min(2048, round_size * qbuf_seqlen_factor)
    cumulative_seqlens = random_round_cumulative_seqlens(
        round_size, num_requests, num_padding_requests, num_padding_tokens
    )
    torch.manual_seed(seed + 1234 + num_requests)

    # First, check copy_in_queries
    q = torch.randn(round_size, nheads, headdim, device="cuda").to(dtype)
    q_buf = torch.zeros(
        num_requests, qbuf_seqlen, nheads, headdim, device="cuda", dtype=dtype
    )
    q_buf_ref = torch.zeros_like(q_buf)
    ref_copy_queries_in(q, q_buf_ref, cumulative_seqlens)
    multirequest_flash_copy_in_queries(q, q_buf, cumulative_seqlens)
    torch.testing.assert_close(q_buf, q_buf_ref)

    # Now check copy_out_results
    q_buf = torch.randn(num_requests, qbuf_seqlen, nheads, headdim, device="cuda").to(
        dtype
    )
    result = torch.zeros(round_size, nheads, headdim, device="cuda", dtype=dtype)
    result_ref = torch.zeros_like(result)
    ref_copy_results_out(q_buf, result_ref, cumulative_seqlens)
    multirequest_flash_copy_out_results(q_buf, result, cumulative_seqlens, False)
    torch.testing.assert_close(result, result_ref)


@pytest.mark.parametrize("nheads", [1, 56])
@pytest.mark.parametrize("round_size", [4])
@pytest.mark.parametrize(
    "qbuf_seqlen, cumulative_seqlens",
    [
        (4, [0, 1]),
        (4, [0, 2]),
        (4, [0, 3]),
        (4, [0, 4]),
    ],
)
def test_multirequest_flash_copy_out_queries_write_trailing_zeros(
    nheads: int,
    round_size: int,
    qbuf_seqlen: int,
    cumulative_seqlens: Sequence[int],
    headdim: int = 8,
    dtype: torch.dtype = torch.float16,
):
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=400)
    cumulative_seqlens_tensor = torch.tensor(
        cumulative_seqlens, device="cuda", dtype=torch.int32
    )
    num_requests = cumulative_seqlens_tensor.size(0) - 1
    # Now check copy_out_results
    q_buf = torch.randn(
        num_requests, qbuf_seqlen, nheads, headdim, device="cuda", dtype=dtype
    )
    result_ref = torch.zeros(round_size, nheads, headdim, device="cuda", dtype=dtype)
    ref_copy_results_out(q_buf, result_ref, cumulative_seqlens_tensor)

    result = torch.zeros_like(result_ref)
    result -= 100  # check below makes sure this will be overwritten on copy out
    multirequest_flash_copy_out_results(q_buf, result, cumulative_seqlens_tensor, True)
    torch.testing.assert_close(result, result_ref)


def test_multirequest_flash_copy_in_queries(
    round_size: int = 8,
    cumulative_seqlens: torch.Tensor = torch.tensor(
        [6, 8, 8, 8], device="cuda", dtype=torch.int32
    ),
    nheads: int = 1,
    headdim: int = 8,
    qbuf_seqlen: int = 2,
    dtype: torch.dtype = torch.float16,
    num_requests: int = 3,
    seed: int = 1234,
):
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)
    torch.manual_seed(seed)

    q = torch.randn(round_size, nheads, headdim, device="cuda", dtype=dtype)
    q_buf = torch.zeros(
        num_requests, qbuf_seqlen, nheads, headdim, device="cuda", dtype=dtype
    )
    q_buf_ref = torch.zeros_like(q_buf)
    ref_copy_queries_in(q, q_buf_ref, cumulative_seqlens)
    multirequest_flash_copy_in_queries(q, q_buf, cumulative_seqlens)
    torch.testing.assert_close(q_buf, q_buf_ref)


@pytest.mark.parametrize("round_size", [2**k for k in range(12)])
@pytest.mark.parametrize("num_padding_tokens", [0, 1, 17])
@pytest.mark.parametrize("num_padding_requests", [0, 1, 7])
def test_multirequest_flash_copy_cache_metadata(
    round_size: int,
    num_padding_tokens: int,
    num_padding_requests: int,
):
    if num_padding_tokens >= round_size:
        pytest.skip("Too many padding tokens")
    for num_requests in [1, round_size // 2, round_size - 1, round_size]:
        if num_requests <= num_padding_requests:
            continue
        if (num_requests + num_padding_tokens) > round_size:
            continue
        if num_requests > 32:
            continue
        cumulative_seqlens = random_round_cumulative_seqlens(
            round_size, num_requests, num_padding_requests, num_padding_tokens
        )
        torch.manual_seed(1234 + num_requests)

        cache_idxs = torch.randint(
            low=0,
            high=100,
            size=(round_size,),
            dtype=torch.int32,
            device="cuda",
        )
        cache_locs = torch.randint(
            low=0,
            high=100,
            size=(round_size,),
            dtype=torch.int32,
            device="cuda",
        )

        cache_batch_idxs_buf = torch.zeros(
            num_requests, dtype=torch.int32, device="cuda"
        )
        cache_batch_idxs_buf_ref = torch.zeros_like(cache_batch_idxs_buf)
        cache_seqlens_buf = torch.zeros(num_requests, dtype=torch.int32, device="cuda")
        cache_seqlens_buf_ref = torch.zeros_like(cache_seqlens_buf)

        ref_copy_metadata(
            cumulative_seqlens,
            cache_idxs,
            cache_locs,
            cache_batch_idxs_buf_ref,
            cache_seqlens_buf_ref,
        )
        multirequest_flash_copy_cache_metadata(
            cumulative_seqlens,
            cache_idxs,
            cache_locs,
            cache_batch_idxs_buf,
            cache_seqlens_buf,
        )
        torch.testing.assert_close(cache_batch_idxs_buf, cache_batch_idxs_buf_ref)
        torch.testing.assert_close(cache_seqlens_buf, cache_seqlens_buf_ref)
        torch.testing.assert_close(cache_seqlens_buf, cache_seqlens_buf_ref)


@pytest.mark.parametrize("ntokens", [1, 27, 313])
@pytest.mark.parametrize("nheads_kv", [1, 8])
@pytest.mark.parametrize("q_per_kv", [1, 7])
@pytest.mark.parametrize("headdim", [64, 128])
@pytest.mark.parametrize("dtype", [torch.bfloat16, torch.float16])
@pytest.mark.parametrize("ncaches", [1, 8])
def test_fused_q_contiguous_kv_copy(
    ntokens: int,
    nheads_kv: int,
    q_per_kv: int,
    headdim: int,
    dtype: torch.dtype,
    ncaches: int,
    seqlen: int = 4096,
):
    torch.manual_seed(1234)
    torch.cuda.manual_seed(1234)
    qkv = torch.randn(
        (ntokens, (2 + q_per_kv) * nheads_kv * headdim), dtype=dtype, device="cuda"
    ).view(ntokens, nheads_kv, 2 + q_per_kv, headdim)
    q, k, v = qkv.split([q_per_kv, 1, 1], dim=2)
    q = q.view(ntokens, nheads_kv, q_per_kv, headdim)
    k = k.view(ntokens, nheads_kv, headdim)
    v = v.view(ntokens, nheads_kv, headdim)
    k_cache = torch.zeros(
        (ncaches, seqlen, nheads_kv, headdim), dtype=dtype, device="cuda"
    )
    v_cache = torch.zeros_like(k_cache)

    kv_pos_idxs = torch.randperm(seqlen, dtype=torch.int32, device="cuda")[:ntokens]
    kv_cache_idxs = torch.randint(
        0, ncaches, (ntokens,), dtype=torch.int32, device="cuda"
    )

    q_result = fused_q_contiguous_kv_copy(
        q, k, v, kv_pos_idxs, k_cache, v_cache, kv_cache_idxs
    )
    # q should be the same (and contiguous)
    assert q_result.is_contiguous()
    assert torch.allclose(q_result, q)
    # k and v should be written to the cache with zeros everywhere else
    assert torch.allclose(k_cache.sum(), k.sum())
    assert torch.allclose(v_cache.sum(), v.sum())
    for token in range(ntokens):
        cache_idx = kv_cache_idxs[token].item()
        kv_pos_idx = kv_pos_idxs[token].item()
        assert torch.allclose(k_cache[cache_idx, kv_pos_idx, ...], k[token, ...])
        assert torch.allclose(v_cache[cache_idx, kv_pos_idx, ...], v[token, ...])
