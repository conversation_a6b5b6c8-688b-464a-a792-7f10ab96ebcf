"""Utility functions to run with CUDA graphs."""

import dataclasses
import enum
import itertools
import logging
import time
import uuid
from typing import Callable, Protocol, Sequence, TypeVar

import torch
from immutabledict import immutabledict
from typing_extensions import deprecated

logger = logging.getLogger()


NUM_GRAPH_CAPTURES = 0


TensorCallableReturnType = TypeVar(
    "TensorCallableReturnType", torch.Tensor, Sequence[torch.Tensor]
)

# NOTE(arun): Unfortunately, it's quite hard to write a simple type constraint that
#  covers all functions with Tensor only inputes and outputs, so we union type here.
_TensorCallable1 = Callable[[torch.Tensor], TensorCallableReturnType]
_TensorCallable2 = Callable[[torch.Tensor, torch.Tensor], TensorCallableReturnType]
_TensorCallable3 = Callable[
    [torch.Tensor, torch.Tensor, torch.Tensor], TensorCallableReturnType
]
_TensorCallable4 = Callable[
    [torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor], TensorCallableReturnType
]


class _TensorCallable(Protocol):  # TODO(markus): Why do we need this?
    def __call__(
        self, *args: torch.Tensor, **kwargs: torch.Tensor
    ) -> TensorCallableReturnType: ...


TensorCallable = TypeVar(
    "TensorCallable",
    _TensorCallable1,
    _TensorCallable2,
    _TensorCallable3,
    _TensorCallable4,
    _TensorCallable,
)


def _get_device(*args: torch.Tensor, **kwargs: torch.Tensor) -> torch.device:
    device = None
    for t in itertools.chain(args, kwargs.values()):
        if device is None:
            device = t.device
        elif t.device != device:
            raise ValueError(
                f"All inputs must be on the same device. Found {t} on {t.device} "
                f"instead of {device}."
            )
    if device is None:
        raise ValueError("No arguments found.")
    return device


# Layer of indirection so that all calls to this library go through GraphedFunction
# so we don't have to touch all call sites.
@deprecated("Use `GraphedFunction` instead.")
def make_graphed_callable(
    fn: TensorCallable,
    static_args: list[torch.Tensor] | None = None,
    static_kwargs: dict[str, torch.Tensor] | None = None,
    static_returns: list[torch.Tensor] | None = None,
    max_batch_size: int | None = None,
    batch_sizes: Sequence[int] | None = None,
    pool=None,
) -> TensorCallable:
    assert static_args is None
    assert static_kwargs is None
    assert static_returns is None
    return GraphedFunction(  # type: ignore
        fn,
        max_batch_size=max_batch_size,
        batch_sizes=batch_sizes,
        pool=pool,
    )


class OnDataPtrChange(enum.Enum):
    """What to do when the data pointer of an argument changes."""

    # Copy the data from the input to the graphed function to the pre-allocated
    # tensor.
    # If the data pointer matches the pre-allocated tensor, we do nothing.
    COPY = 1
    # Raise an error if the data pointer changes.
    FAIL = 2
    # Capture a new graph using the input tensor's data ptr. Only supported by
    # GraphedFunctionRecapturable. Is not supported by GraphedFunction.
    # Incompatible with batch_dim and pre_alloc_tensor in ArgumentSpec.
    RECAPTURE = 3


class ResetBehavior(enum.Enum):
    """Controls the behavior of an input tensor when the inputs have to be reset.

    The inputs of the function are reset multiple times during the dummy calls
    needed for warmup and graph capturing.
    """

    # Default: Reset the graph after each dummy call.
    # This is the default behavior and is the safest.
    # However, it requires creating a copy of the inputs,
    # which can be expensive.
    RESET_ON_CALL = 1
    # Do not reset the graph. This is unsafe, but can be
    # useful for attention objects where we do not want
    # to create a copy of the key and value caches.
    IGNORE = 2


@dataclasses.dataclass(frozen=True)
class ArgumentSpec:
    batch_dim: int | None = None
    """The batch dimension of the argument, if any."""
    pre_alloc_tensor: torch.Tensor | None = None
    """A pre-allocated tensor to use for this argument. If None, we allocate a tensor of
    the correct size and type. Populate this field if you want to reuse the same tensor
    for multiple calls to the graph (e.g. to save memory)."""
    on_data_ptr_change: OnDataPtrChange = OnDataPtrChange.COPY
    """What to do when the data pointer of the argument changes between calls."""
    reset_behavior: ResetBehavior = ResetBehavior.RESET_ON_CALL
    """Whether to reset the graph after dummy calls. Dummy calls are calls needed for
    graphing the function. See self._create_graph."""


@dataclasses.dataclass(frozen=True)
class OutputSpec:
    batch_dim: int | None = 0  # default 0 to support legacy assumptions


def _arg_name(index: int) -> str:
    return f"__arg_{index}"


T = TypeVar("T")


def _combine_args(*args: T, **kwargs: T) -> dict[str, T]:
    """Combine args and kwargs into a single dict.

    This is helpful to process args and kwargs in a uniform way.
    """
    result = dict(kwargs)
    for i, arg in enumerate(args):
        name = _arg_name(i)
        if name in result:
            raise ValueError(f"Illegal name in kwargs: {name}")
        result[name] = arg
    return result


def _extract_args(
    kwargs: dict[str, T],
) -> tuple[list[T], dict[str, T]]:
    """The inverse of _combine_args."""
    num_args = 0
    orig_kwargs = {}
    for name, arg in kwargs.items():
        if name.startswith("__arg_"):
            num_args += 1
        else:
            orig_kwargs[name] = arg

    # We extract the positional args in a second pass
    # so we don't have to rely on the order of items in kwargs.
    args = []
    for i in range(num_args):
        name = _arg_name(i)
        try:
            arg = kwargs[name]
        except KeyError:
            raise ValueError(
                f"Missing argument {name}, available: {list(kwargs.keys())}"
            )
        args.append(arg)
    return args, orig_kwargs


def tensor_slice(tensor: torch.Tensor, dim: int, slice_in_dim: slice):
    """Slice a tensor along a given dimension.

    For dim == 0, this is equivalent to `tensor[slice_in_dim]`, and
    for dim == 1, this is equivalent to `tensor[:, slice_in_dim]`.

    Args:
        tensor: The tensor to slice.
        dim: The dimension to slice along.
        slice_in_dim: The slice to take along the given dimension.

    Returns:
        A tensor with the same shape as `tensor` but with the given dimension
        sliced.
    """
    assert dim >= 0 and dim < tensor.ndim
    return tensor[(slice(None),) * dim + (slice_in_dim,)]


@dataclasses.dataclass(frozen=True)
class _CapturedGraph:
    """Internal representation of a captured graph; is entangled with a GraphedFunction object."""

    gaph_id: str
    graph: torch.cuda.CUDAGraph
    outputs: Sequence[torch.Tensor]

    def replay(self) -> Sequence[torch.Tensor]:
        self.graph.replay()
        return self.outputs


def default_synch_fn():
    torch.cuda.synchronize()


class GraphedFunction:
    """Creates a CUDA graph-ed version of `fn`.

    Loosely based on `torch.cuda.make_graphed_callables`, which doesn't support fwd-only
    callables or variable batch sizes.

    Note that this function makes a few strong assumptions about `fn`:
        - All arguments to `fn` are tensors with `batch_size` as their first dimension.
        - `fn` is always called with the same signature (args, kwargs) as when the graph
          was constructed.
        - If `fn` returns one of its input tensors (by reference), we assume the
          intended behavior is simply to reuse the memory of that tensor for efficiency.
          Internally, make_graphed_callable allocates static inputs and outputs for use
          with the graph, and in this situation, we do not allocate additional memory
          for the output, but also we do *not* copy the results back into the original
          tensor. Thus if you were to do:
          >>> def iadd(x, y):
          >>>   x += y
          >>>   return x
          >>> assert iadd(x, y) is x
          >>> graphed_iadd = make_graphed_callable(iadd)
          >>> assert graphed_iadd(x, y) is not x
          >>> assert torch.allclose(graphed_iadd(x, y), iadd(x, y))

    Args:
        fn: The function mapping tensors to tensors.
        max_batch_size: The largest batch size supported by this graph. We use this
            batch size to allocate memory for inputs and outputs. All inputs and outputs
            are assumed to have their batch size in the first dimension.
        argument_specs: A list of ArgumentSpec objects, one for each positional argument to
            the function to be graphed. Anymissing argument is assumed to have a batch
            dimension and they are all copied on each call. Specify this field if you want
            to reuse the same tensor for multiple calls to the graph.
        output_spec: A list of OutputSpec objects, one for each output of the function to be graphed.
            If None, we assume that the output is a tensor with the same batch size as the input and
            batch dim is 0.
        kw_argument_specs: A dict of ArgumentSpec objects for the keyword arguments.
        pool: Optional memory pool for cuda graphs.
        safe_outputs: If True, we will copy the results before returning the
            tensor. This guarantees that subsequent calls to the graphed function
            will not modify the output.
        batch_sizes: Optional list of allowed batch sizes. If provided, we will
            pre-allocate graphs for these batch sizes and fail for any other batch
            size.
        synch_fn: Optional function to call before and after each graph is captured.
        process_idx: Optional process index for multi-process training.
        num_processes: Optional number of processes for multi-process training.
        wait_to_capture_nccl_graph: Optional flag to wait 30 seconds for graph capture in multi-gpu settings.
            This is a workaround for a bug in the NCCL watchdog that can be dropped after we upgrade to
            PyTorch 2.2.
    """

    def __init__(
        self,
        fn: TensorCallable,
        max_batch_size: int | None = None,
        argument_specs: Sequence[ArgumentSpec] | None = None,
        kw_argument_specs: dict[str, ArgumentSpec] | None = None,
        output_spec: Sequence[OutputSpec] | None = None,
        pool=None,
        safe_outputs: bool = True,
        batch_sizes: Sequence[int] | None = None,
        synch_fn: Callable[[], None] = default_synch_fn,
        process_idx: int = 0,
        num_processes: int = 1,
        wait_to_capture_nccl_graph: bool = True,
    ):
        """Creates a CUDA graph-ed version of `fn`."""
        # We reserve a tensor of maximal batch size for each argument:
        argument_specs = argument_specs or []
        kw_argument_specs = kw_argument_specs or {}
        combined_specs: dict[str, ArgumentSpec] = _combine_args(
            *argument_specs, **kw_argument_specs
        )
        self._argument_specs_orig = combined_specs
        self._output_spec: Sequence[OutputSpec] = output_spec or []
        self._is_single_output = None

        self._argument_specs = dict(self._argument_specs_orig)
        self._static_outputs = None
        self._capture_allowed = True
        self._pre_allocation_done = False
        self._graph_cache: dict[int, _CapturedGraph] = {}
        self._num_validated_calls = 0

        self._pool = pool
        self._fn = fn
        self._safe_outputs = safe_outputs
        self._batch_sizes: Sequence[int] | None = batch_sizes
        self._synch_fn = synch_fn
        self._process_idx = process_idx
        self._num_processes = num_processes
        self._wait_to_capture_nccl_graph = wait_to_capture_nccl_graph
        if (max_batch_size is None and self._batch_sizes is None) or (
            max_batch_size is not None and self._batch_sizes is not None
        ):
            raise ValueError(
                f"GraphedFunction was initialized with {max_batch_size=} "
                f"and {batch_sizes=}. "
                "Must specify either max_batch_size or batch_sizes. "
                "If you want to support variable batch sizes, "
                "you must specify max_batch_size. Variable batch sizes "
                "can cause unbounded memory usage and latency spikes."
            )
        if self._batch_sizes is not None:
            self._batch_sizes = tuple(sorted(self._batch_sizes))
        self._max_batch_size = max_batch_size or max(self._batch_sizes)  # type: ignore
        self._validate_specs()

    def set_capture_allowed(self, capture_allowed: bool):
        self._capture_allowed = capture_allowed

    def _validate_specs(self):
        # Validate that the pre-allocated tensors are of the correct size.
        specs = self._argument_specs_orig
        for name, spec in specs.items():
            if spec.batch_dim is None or spec.pre_alloc_tensor is None:
                # All checks assume that both ate not None.
                continue
            if spec.batch_dim >= spec.pre_alloc_tensor.ndim:
                raise ValueError(
                    f"Argument {name} has batch dimension {spec.batch_dim}, but "
                    f"pre-allocated tensor has only {spec.pre_alloc_tensor.ndim} dimensions."
                )
            if spec.pre_alloc_tensor.shape[spec.batch_dim] != self._max_batch_size:
                raise ValueError(
                    f"Argument {name} has batch dimension {spec.batch_dim}, but "
                    f"pre-allocated tensor has size {spec.pre_alloc_tensor.shape[spec.batch_dim]} "
                    f"instead of {self._max_batch_size}."
                )
            if spec.on_data_ptr_change is OnDataPtrChange.RECAPTURE:
                raise NotImplementedError(
                    f"Argument {name} is specified to recapture the graph on data pointer change, "
                    f"but this is not yet implemented. Use GraphedFunctionRecapturable instead."
                )

    def _allocate_static_inputs(
        self,
        args: dict[str, torch.Tensor],
    ):
        assert not self._pre_allocation_done
        self._pre_allocation_done = True

        print("Allocating static inputs for graph.", flush=True)

        # Pre-allocate tensors and complete argument specs
        for name, arg in args.items():
            if name not in self._argument_specs:
                logger.debug(
                    "Using default ArgumentSpec for argument %s with shape %s",
                    name,
                    arg.shape,
                )
                # legacy assumption: by default all inputs have a batch dim and it is 0
                self._argument_specs[name] = ArgumentSpec(batch_dim=0)

            spec = self._argument_specs[name]
            if spec.pre_alloc_tensor is None:
                shape = list(arg.shape)
                if spec.batch_dim is not None:
                    shape[spec.batch_dim] = self._max_batch_size
                full_batch_arg = torch.zeros(
                    shape,
                    dtype=arg.dtype,
                    device=arg.device,
                )
                spec: ArgumentSpec = dataclasses.replace(
                    spec, pre_alloc_tensor=full_batch_arg
                )
                logger.debug(
                    "cuda_graphs: Allocating tensor for argument %s with shape %s.",
                    name,
                    full_batch_arg.shape,
                )
                self._argument_specs[name] = spec

            pre_allocated = spec.pre_alloc_tensor
            assert (
                pre_allocated is not None
            ), f"Argument {name} has ArgumentSpec, but was not found on call."
            if spec.batch_dim is not None:
                assert pre_allocated.shape[spec.batch_dim] == self._max_batch_size, (
                    f"Argument {name} has batch dimension {spec.batch_dim}, shape {pre_allocated.shape=}, but "
                    f"expected {self._max_batch_size}."
                )
            torch.cuda.synchronize()

    def _allocate_static_outputs(self, outputs: Sequence[torch.Tensor] | torch.Tensor):
        """Create static outputs for the function."""
        if self._static_outputs is not None:
            return
        if not isinstance(outputs, Sequence):
            self._is_single_output = True
            outputs = (outputs,)
        static_outputs = []
        if not self._output_spec:
            self._output_spec = [OutputSpec() for _ in range(len(outputs))]
        for out, spec in zip(outputs, self._output_spec):
            out_shape = list(out.shape)
            if spec.batch_dim is not None:
                out_shape[spec.batch_dim] = self._max_batch_size
            static_out = torch.zeros(
                out_shape,
                dtype=out.dtype,
                device="cuda",
            )
            static_outputs.append(static_out)
        self._static_outputs = tuple(static_outputs)

    def _write_static_outputs(
        self, outputs: Sequence[torch.Tensor] | torch.Tensor, batch_size: int
    ):
        """Write static outputs for the function."""
        assert self._static_outputs is not None
        if not isinstance(outputs, Sequence):
            outputs = (outputs,)
        outputs_views_to_store = []
        for out, static_out, spec in zip(
            outputs, self._static_outputs, self._output_spec
        ):
            if spec.batch_dim is None:
                target_tensor = out
            else:
                assert out.shape[spec.batch_dim] == batch_size, (
                    f"Output {out} has batch dimension {spec.batch_dim}, but "
                    f"static output has only {static_out.ndim} dimensions."
                )
                target_tensor = tensor_slice(
                    static_out, spec.batch_dim, slice(batch_size)
                )
            target_tensor.copy_(out)
            outputs_views_to_store.append(target_tensor)
        return outputs_views_to_store

    def _backup_static_inputs(self) -> dict[str, torch.Tensor]:
        backup = {}
        behavior_for_logging = {
            name: (spec.reset_behavior, spec.on_data_ptr_change)
            for name, spec in self._argument_specs.items()
        }
        logger.info(
            "process_idx=%d: Backing up inputs before graphing. Argument reset behavior: %s",
            self._process_idx,
            behavior_for_logging,
        )
        for name, spec in self._argument_specs.items():
            assert spec.pre_alloc_tensor is not None
            if spec.reset_behavior is ResetBehavior.IGNORE:
                pass
            elif spec.reset_behavior is ResetBehavior.RESET_ON_CALL:
                backup[name] = spec.pre_alloc_tensor.clone()
            else:
                raise ValueError(
                    f"Unknown reset behavior {spec.reset_behavior} for argument {name}."
                )
        return backup

    def _restore_static_inputs(self, backup: dict[str, torch.Tensor]):
        for name, arg in backup.items():
            spec = self._argument_specs[name]
            # print(f"Restoring {name} to {arg}")
            assert spec.pre_alloc_tensor is not None
            spec.pre_alloc_tensor.copy_(arg)
        print(f"{self._process_idx=}: Finished restoring static inputs.", flush=True)

    def _create_graph(self, batch_size: int) -> _CapturedGraph:
        global NUM_GRAPH_CAPTURES
        assert self._pre_allocation_done
        assert batch_size <= self._max_batch_size
        assert self._capture_allowed
        print("Backing up static inputs for graph.", flush=True)
        inputs_backup = self._backup_static_inputs()

        print(
            f"{self._process_idx=}: Warming up graph for batch size {batch_size}.",
            flush=True,
        )
        all_args = {}
        for key, value in self._argument_specs.items():
            t = value.pre_alloc_tensor
            assert t is not None
            if value.batch_dim is not None:
                # For most cases the batch dim is 0, so we can just slice the tensor like this:
                # t = t[:batch_size]
                # This is the general case:
                t = tensor_slice(t, value.batch_dim, slice(batch_size))
            all_args[key] = t
        args, kwargs = _extract_args(all_args)
        print(
            f"{self._process_idx=}: Capturing graph for batch size {batch_size}.",
            flush=True,
        )
        device = _get_device(*args, **kwargs)

        graph = torch.cuda.CUDAGraph()
        warmup_stream: torch.cuda.Stream = torch.cuda.Stream(device=device)  # type:ignore
        warmup_stream.wait_stream(torch.cuda.current_stream())

        with torch.cuda.stream(warmup_stream):
            for warmup_round in range(3):
                print(
                    f"{self._process_idx=}: Cuda graph internal warmup round {warmup_round}",
                    flush=True,
                )
                self._synch_fn()
                print(
                    f"{self._process_idx=}: Cuda graph internal warmup round {warmup_round}; synchronized.",
                    flush=True,
                )
                outputs = self._fn(*args, **kwargs)
                print(
                    f"{self._process_idx=}: Cuda graph internal warmup round {warmup_round}; finished.",
                    flush=True,
                )
                self._allocate_static_outputs(outputs)
                outputs_views_to_store = self._write_static_outputs(outputs, batch_size)
                self._restore_static_inputs(inputs_backup)
        assert self._static_outputs is not None

        capture_stream: torch.cuda.Stream = torch.cuda.Stream(device=device)  # type:ignore
        capture_stream.wait_stream(capture_stream)
        capture_stream.wait_stream(warmup_stream)
        self._synch_fn()
        if self._num_processes > 1 and self._wait_to_capture_nccl_graph:
            # TODO(markus): sleep ensures that the nccl watchdog is not interrupting
            # the cuda graph capture process. This is a temporary workaround until we can
            # upgrade to PyTorch 2.2.
            time.sleep(2.0)
        with torch.cuda.graph(graph, stream=capture_stream, pool=self._pool):
            outputs = self._fn(*args, **kwargs)
            self._write_static_outputs(outputs, batch_size)

        torch.cuda.current_stream().wait_stream(capture_stream)
        self._synch_fn()
        print(
            f"{self._process_idx=}: Finished capturing graph no {NUM_GRAPH_CAPTURES} for batch size {batch_size} on device {device}",
            flush=True,
        )
        NUM_GRAPH_CAPTURES += 1
        self._restore_static_inputs(inputs_backup)

        return _CapturedGraph(uuid.uuid4().hex, graph, outputs_views_to_store)  # type: ignore

    def _get_batch_size(self, args: dict[str, torch.Tensor]) -> int:
        args_batch_sizes = {}
        for name, arg in args.items():
            spec = self._argument_specs[name]
            if spec.batch_dim is not None:
                if self._num_validated_calls >= 10:
                    # slightly unsafe way to get the batch size in case
                    # the different arguments have different batch sizes.
                    # But we save 5us per step.
                    return arg.shape[spec.batch_dim]
                args_batch_sizes[name] = arg.shape[spec.batch_dim]

        if len(set(args_batch_sizes.values())) > 1:
            raise ValueError(
                f"All arguments must have the same batch size. Found {args_batch_sizes}."
            )
        if len(args_batch_sizes) == 0:
            batch_size = min(self._batch_sizes or [1])
        else:
            batch_size = next(iter(args_batch_sizes.values()))
        return batch_size

    def _validate_args(self, args: dict[str, torch.Tensor]):
        # only validate on the first 10 calls to save 5us per step
        if self._num_validated_calls < 10:
            self._num_validated_calls += 1
        else:
            return
        for arg_name in args:
            if arg_name not in self._argument_specs:
                raise ValueError(
                    f"Argument {arg_name} is not statically allocated. "
                    f"Statically allocated arguments are: {self._argument_specs.keys()}"
                )
        for arg_name in self._argument_specs:
            if arg_name not in args:
                raise ValueError(
                    f"Argument {arg_name} is statically allocated, but was not found in the call."
                )

        # check data pointers
        for arg_name, arg in args.items():
            spec = self._argument_specs[arg_name]
            assert spec.pre_alloc_tensor is not None
            if spec.on_data_ptr_change is OnDataPtrChange.FAIL:
                if spec.pre_alloc_tensor.data_ptr() != arg.data_ptr():
                    raise ValueError(
                        f"Argument {arg_name} specifies to check data pointers, but data pointers do not match."
                    )

    def _write_static_inputs(self, args: dict[str, torch.Tensor], batch_size: int):
        # Copy data to pre-allocated input tensors

        for key, spec in self._argument_specs.items():
            value = spec.pre_alloc_tensor
            assert value is not None
            if value.data_ptr() != args[key].data_ptr():
                if spec.batch_dim is not None:
                    target_tensor = tensor_slice(
                        value, spec.batch_dim, slice(batch_size)
                    )
                else:
                    target_tensor = value
                target_tensor.copy_(args[key], non_blocking=True)

    def __call__(
        self, *args: torch.Tensor, **kwargs: torch.Tensor
    ) -> TensorCallableReturnType:
        torch.cuda.nvtx.range_push("GraphedFunction setup")
        combined_args = _combine_args(*args, **kwargs)
        if not self._pre_allocation_done:
            self._allocate_static_inputs(combined_args)

        self._validate_args(combined_args)
        batch_size = self._get_batch_size(combined_args)

        torch.cuda.nvtx.range_push("GraphedFunction write static inputs")
        self._write_static_inputs(combined_args, batch_size)
        torch.cuda.nvtx.range_pop()

        assert batch_size <= self._max_batch_size
        if batch_size not in self._graph_cache:
            if self._batch_sizes is not None and batch_size not in self._batch_sizes:
                raise ValueError(
                    f"Batch size {batch_size} is not allowed. Allowed batch sizes are: {self._batch_sizes}."
                )
            if not self._capture_allowed:
                raise RuntimeError(
                    "Capturing is not allowed. This can happen when a list of batch sizes is specified "
                    "on construction, and the current batch size is not in the list."
                )
            logger.info("Creating graph for batch size %d", batch_size)
            if self._batch_sizes is None:
                batch_sizes = [batch_size]
            else:  # self._batch_sizes is not None
                batch_sizes = self._batch_sizes
            # largest batch sizes first; this should help with reusing allocated memory
            for batch_size_to_graph in sorted(batch_sizes, reverse=True):
                self._graph_cache[batch_size_to_graph] = self._create_graph(
                    batch_size_to_graph
                )
            if self._batch_sizes is not None:
                self.set_capture_allowed(False)  # no need to capture again

        torch.cuda.nvtx.range_pop()
        out = self._graph_cache[batch_size].replay()
        if self._safe_outputs:
            out: Sequence[torch.Tensor] = tuple(o.clone() for o in out)

        if self._is_single_output:
            return out[0]  # type: ignore
        else:
            return out  # type: ignore


DataPtrType = int


@dataclasses.dataclass(frozen=True)
class _RecaptureKey:
    """These items determine a unique graph in a graph pool."""

    data_ptrs: immutabledict[str, DataPtrType] = (
        immutabledict()
    )  # frozen to be hashable
    """Maps argument names to data pointers."""


class GraphedFunctionRecapturable:
    """A version of GraphedFunction that supports recapturing on data pointer change.

    ===Context:
    A single CUDAGraph assumes fixed data pointers for its input and output. In
    GraphableFunction we copy input from the caller into a pre-allocated tensor, but
    there are situations where this is wasteful, for example, copying key-value
    caches. This abstraction instead creates new CUDAGraphs for these types of inputs.

    ===Usage:
    Use OnGraphPtrChange.RECAPTURE to specify that a graph should be recaptured when the
    data pointer of an argument changes.

    This class is implemented as a wrapper around GraphedFunction.

    This is useful for functions that have stateful behavior in a large tensor that
    should never be copied on a call, but there might be multiple state tensors that
    the function is called with over time. We want to capture a separate graph per such
    tensor.
    """

    def __init__(
        self,
        fn: TensorCallable,
        max_batch_size: int | None = None,
        argument_specs: list[ArgumentSpec] | None = None,
        kw_argument_specs: dict[str, ArgumentSpec] | None = None,
        output_spec: Sequence[OutputSpec] | None = None,
        pool=None,
        safe_outputs: bool = True,
        batch_sizes: Sequence[int] | None = None,
        synch_fn: Callable[[], None] = default_synch_fn,
        process_idx: int = 0,
        num_processes: int = 1,
        wait_to_capture_nccl_graph: bool = True,
    ):
        self._captured_graphs: dict[_RecaptureKey, GraphedFunction] = {}

        argument_specs = argument_specs or []
        kw_argument_specs = kw_argument_specs or {}
        combined_specs = _combine_args(*argument_specs, **kw_argument_specs)
        self._recapture_on_data_ptr_change_for_kwargs = [
            name
            for name, spec in combined_specs.items()
            if spec.on_data_ptr_change is OnDataPtrChange.RECAPTURE
        ]

        # args for new graphed functions
        self._fn = fn
        self._max_batch_size = max_batch_size
        self._argument_specs_orig = combined_specs
        self._output_spec = output_spec
        self._pool = pool
        self._safe_outputs = safe_outputs
        self._batch_sizes = batch_sizes
        self._synch_fn = synch_fn
        self._process_idx = process_idx
        self._num_processes = num_processes
        self._wait_to_capture_nccl_graph = wait_to_capture_nccl_graph

        self._validate_specs()

        self._capture_allowed = True

    def set_capture_allowed(self, capture_allowed: bool):
        self._capture_allowed = capture_allowed
        for graph in self._captured_graphs.values():
            graph.set_capture_allowed(capture_allowed)

    def _validate_specs(self):
        # if a kwarg is specified to recapture on data pointer change, it cannot have a pre-allocated tensor
        # and cannot have a batch dimension.
        for name, spec in self._argument_specs_orig.items():
            if spec.on_data_ptr_change is OnDataPtrChange.RECAPTURE:
                if spec.pre_alloc_tensor is not None:
                    raise ValueError(
                        f"Argument {name} is specified to recapture the graph on data pointer change, "
                        f"but has a pre-allocated tensor. This is not allowed."
                    )
                if spec.batch_dim is not None:
                    raise ValueError(
                        f"Argument {name} is specified to recapture the graph on data pointer change, "
                        f"but has a batch dimension. This is not allowed."
                    )

    def _create_graph(self, combined_args: dict[str, torch.Tensor]) -> GraphedFunction:
        """We remove RECAPTURE from specs, replace them with the new input tensors, and create a new graph."""
        if not self._capture_allowed:
            raise RuntimeError(
                "Capture is not allowed. This can be controlled by calls to self.set_capture_allowed."
            )
        new_specs = self._argument_specs_orig.copy()
        for name in self._recapture_on_data_ptr_change_for_kwargs:
            input_tensor = combined_args[name]
            new_specs[name] = dataclasses.replace(
                new_specs[name],
                pre_alloc_tensor=input_tensor,
                on_data_ptr_change=OnDataPtrChange.FAIL,
            )
            assert new_specs[name].batch_dim is None
        arg_specs, kwarg_specs = _extract_args(new_specs)
        return GraphedFunction(
            fn=self._fn,
            max_batch_size=self._max_batch_size,
            argument_specs=arg_specs,
            kw_argument_specs=kwarg_specs,
            output_spec=self._output_spec,
            pool=self._pool,
            safe_outputs=self._safe_outputs,
            batch_sizes=self._batch_sizes,
            synch_fn=self._synch_fn,
            process_idx=self._process_idx,
            num_processes=self._num_processes,
            wait_to_capture_nccl_graph=self._wait_to_capture_nccl_graph,
        )

    def __call__(
        self, *args: torch.Tensor, **kwargs: torch.Tensor
    ) -> TensorCallableReturnType:
        torch.cuda.nvtx.range_push("GraphedFunctionRecapturable setup")
        combined_args = _combine_args(*args, **kwargs)
        data_ptrs = {}
        for kwarg_name in self._recapture_on_data_ptr_change_for_kwargs:
            if kwarg_name in combined_args:
                data_ptrs[kwarg_name] = combined_args[kwarg_name].data_ptr()
        graph_key = _RecaptureKey(data_ptrs=immutabledict(data_ptrs))
        if graph_key not in self._captured_graphs:
            print(
                f"{self._process_idx=}: GraphedFunctionRecapturable: Capturing graph for {graph_key} on device {self._process_idx}",
                flush=True,
            )
            self._captured_graphs[graph_key] = self._create_graph(combined_args)
        torch.cuda.nvtx.range_pop()
        return self._captured_graphs[graph_key](*args, **kwargs)
