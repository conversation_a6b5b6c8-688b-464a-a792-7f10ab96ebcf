# Fast Forward

This is the code that runs the models. It is here to be shared between the
research code and the production code. The code should only depend on external
dependencies (i.e. python packages) that are available in both environments,
libraries in base, and libraries in third-party.

## Fast Forward Models

Each model-relaated files should sit in their own files, such as `llama` and `starcoder`.
This benefits to isolate the model-related code and make it easier to maintain. For example,
if we want to delete a model, we can simply delete its folder without touching the rest of the code.

## Profiling

We hope the profiling code to be model agnostic (although it is currently not T.T) so that the profiling codes can be reused across models. Thus, we put all the profiling code under the `profiling` folder.
In addition, the current profiling code is relatively experimental, we should improve it as we go.
