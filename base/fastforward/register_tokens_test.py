"""Helper function for MultiCacheAttention.register_tokens_get_positions.

This function is used to register tokens and get their position indices.
It is called before the attention is called for the first time in the current round.
"""

import pytest
import torch

from base.fastforward import register_tokens


@pytest.mark.parametrize(
    "tokens,cache_idxs,positions,expected_pos",
    [
        pytest.param(
            [11, 12, 13, 14, 15, 16, 17, 18, 19, 20],  # tokens
            [3, 3, 3, 1, 1, 1, 1, 2, 2, 0],  # cache_idxs
            [0, 2, 0, 1, 0],  # positions
            [1, 2, 3, 2, 3, 4, 5, 0, 1, 0],  # expected_pos
            id="canonical_example",
        ),
        pytest.param(
            [11, 12, 13, 14],  # tokens
            [1, 1, 1, 1],  # cache_idxs
            [0, 13, 0, 0, 0],  # positions
            [13, 14, 15, 16],  # expected_pos; shifted by 13
            id="single_request",
        ),
        pytest.param(
            [11, 12, 13, 14],  # tokens
            [2, 2, 2, 2],  # cache_idxs
            [11, 13, 0, 14, 15],  # positions; used cache is empty
            [0, 1, 2, 3],  # expected_pos
            id="single_request_empty_cache",
        ),
        pytest.param(
            [11, 12, 13, 14, 15],  # tokens
            [1, 0, 0, 0, 0],  # cache_idxs
            [0, 10, 0, 0, 0],  # positions
            [10, 0, 0, 0, 0],  # expected_pos
            id="single_token_with_padding",
        ),
        # The next case is not a requirement for the interface,
        # and we should not rely on it, but it is a good sanity check
        # that we want the kernel version to pass as well.
        pytest.param(
            [11, 12, 13, 14, 15],  # tokens
            [1, 0, 1, 0, 0],  # cache_idxs
            [0, 7, 0, 0, 0],  # positions
            [7, 0, 8, 0, 0],  # expected_pos
            id="request_is_split",
        ),
    ],
)
def test_register_tokens_positions_output(tokens, cache_idxs, positions, expected_pos):
    num_caches = 5
    device = "cuda"
    device_idx = 0
    tokens_cache = torch.empty(
        (5, len(tokens) + 1000), dtype=torch.int32, device="cuda"
    ).fill_(-1)
    tokens = torch.tensor(tokens, dtype=torch.int32, device="cuda")
    cache_idxs = torch.tensor(cache_idxs, dtype=torch.int32, device="cuda")
    positions = torch.tensor(positions, dtype=torch.int32, device="cuda")
    new_pos = register_tokens.register_tokens_core(
        tokens=tokens,
        cache_idxs=cache_idxs,
        positions=positions,
        tokens_cache=tokens_cache,
        num_caches=num_caches
        - 1,  # because the function doesn't count the padding cache,
        padding_cache_idx=0,
        padding_cache_pos=0,
        device=device,
        device_idx=device_idx,
    )
    assert new_pos.tolist() == expected_pos


def test_register_tokens_edge_case():
    num_caches = 64  # Assuming this based on the length of the positions tensor
    device = "cuda"
    device_idx = 0

    tokens = torch.zeros(32, dtype=torch.int32, device=device)
    cache_idxs = torch.zeros(32, dtype=torch.int32, device=device)
    positions = torch.zeros(num_caches + 1, dtype=torch.int32, device=device)
    tokens_cache = torch.full(
        (num_caches + 1, 1000), -1, dtype=torch.int32, device=device
    )

    padding_cache_idx = 0
    padding_cache_pos = 0

    # create a new copy of the tokens_cache tensor for the reference implementation
    tokens_cache_ref = tokens_cache.clone()
    positions_ref = positions.clone()
    cache_idxs_ref = cache_idxs.clone()
    tokens_ref = tokens.clone()

    new_pos = register_tokens.register_tokens_core(
        tokens=tokens,
        cache_idxs=cache_idxs,
        positions=positions,
        tokens_cache=tokens_cache,
        num_caches=num_caches,
        padding_cache_idx=padding_cache_idx,
        padding_cache_pos=padding_cache_pos,
        device=device,
        device_idx=device_idx,
        use_kernel=True,  # Ensure we're using the CUDA kernel
    )

    new_pos_ref = register_tokens.register_tokens_core(
        tokens=tokens_ref,
        cache_idxs=cache_idxs_ref,
        positions=positions_ref,
        tokens_cache=tokens_cache_ref,
        num_caches=num_caches,
        padding_cache_idx=padding_cache_idx,
        padding_cache_pos=padding_cache_pos,
        device=device,
        device_idx=device_idx,
        use_kernel=False,
    )

    # Check that new_pos is correctly calculated
    assert torch.all(new_pos == new_pos_ref)

    # Check that tokens_cache is correctly updated
    assert torch.all(tokens_cache == tokens_cache_ref)


@pytest.mark.parametrize("seed", [0, 1, 2, 3, 4, 5, 6, 7])  # 8 attempts per shape
@pytest.mark.parametrize(
    "num_tokens, num_caches, max_seq_len",
    [
        (32, 4, 1024),  # Basic case
        (1, 4, 1024),  # Single token
        (128, 8, 2048),  # Larger input, different padding cache
        (64, 16, 512),  # More caches, shorter sequences
        (256, 2, 4096),  # Many tokens, few caches
        (1024, 32, 8192),  # Stress test with large inputs
        (0, 4, 1024),  # Empty input
        (32, 1, 1024),  # Single cache
        (32, 4, 32),  # Minimum sequence length
        (8192, 2, 8192 * 2),  # Long sequences
        (16384, 4, 16384 + 8),
    ],
)
def test_register_tokens_kernel_vs_pytorch(num_tokens, num_caches, max_seq_len, seed):
    device = torch.device("cuda")
    torch.manual_seed(seed)

    # Generate random input data
    tokens = torch.randint(0, 50000, (num_tokens,), dtype=torch.int32, device=device)
    cache_idxs = torch.randint(
        1, num_caches + 1, (num_tokens,), dtype=torch.int32, device=device
    )
    positions = torch.randint(
        0,
        max_seq_len - num_tokens + 1,
        (num_caches + 1,),
        dtype=torch.int32,
        device=device,
    )
    tokens_cache = torch.full(
        (num_caches + 1, max_seq_len), -1, dtype=torch.int32, device=device
    )

    # Create copies for pytorch version
    tokens_ref = tokens.clone()
    cache_idxs_ref = cache_idxs.clone()
    positions_ref = positions.clone()
    tokens_cache_ref = tokens_cache.clone()

    # Run kernel version
    new_pos_kernel = register_tokens.register_tokens_core(
        tokens=tokens,
        cache_idxs=cache_idxs,
        positions=positions,
        tokens_cache=tokens_cache,
        num_caches=num_caches,
        padding_cache_idx=0,
        padding_cache_pos=0,
        device=device,
        device_idx=0,
        use_kernel=True,
    )

    # Run pytorch version
    new_pos_ref = register_tokens.register_tokens_core(
        tokens=tokens_ref,
        cache_idxs=cache_idxs_ref,
        positions=positions_ref,
        tokens_cache=tokens_cache_ref,
        num_caches=num_caches,
        padding_cache_idx=0,
        padding_cache_pos=0,
        device=device,
        device_idx=0,
        use_kernel=False,
    )

    # Compare results
    torch.testing.assert_close(new_pos_kernel, new_pos_ref, rtol=0, atol=0)
    torch.testing.assert_close(tokens_cache, tokens_cache_ref, rtol=0, atol=0)
