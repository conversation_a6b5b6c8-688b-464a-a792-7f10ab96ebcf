"""Tests for parallel processing."""

import functools
from typing import Callable

import torch
import torch.distributed as dist

from base.fastforward import parallel, torch_utils

# TODOs:
# - report exceptions, at least from process 0


def compute_sum(input_tensor, process_idx: int = 0, num_processes: int = 1):
    print(f"{process_idx=} called")
    input_tensor = torch_utils.shard(
        input_tensor, axis=0, process_idx=process_idx, num_processes=num_processes
    ).to(device=f"cuda:{process_idx}")
    local_sum = torch.sum(input_tensor)
    if num_processes > 1:
        dist.reduce(local_sum, dst=0, op=dist.ReduceOp.SUM)
    return local_sum


# This function is being called on each device.
# It must be top-level function, or else it cannot be referenced by multiprocessing
def compute_sum_factory(
    process_idx: int, num_processes: int
) -> Callable[[torch.Tensor], torch.Tensor]:
    return functools.partial(
        compute_sum, process_idx=process_idx, num_processes=num_processes
    )


def test_parallel_result():
    mp_sum = parallel.ParallelRunner(num_processes=2).initialize(compute_sum_factory)

    print("Test initialized parallel runner", flush=True)

    input_tensor = torch.arange(100, device="cuda")

    # returns 4950
    main_process_result = compute_sum(input_tensor)
    print(f"{main_process_result=}", flush=True)
    mp_result = mp_sum(input_tensor)
    print(f"{mp_result=}", flush=True)

    torch.testing.assert_close(main_process_result, mp_result)
    torch.cuda.synchronize()


def test_parallel_multiple_calls():
    """Model parallel functions can be called multiple times."""
    mp_sum = parallel.ParallelRunner(num_processes=2).initialize(compute_sum_factory)

    input_tensor = torch.arange(100, device="cuda")
    print(f"{mp_sum(input_tensor)=}")
    print(f"{mp_sum(input_tensor)=}")
    input_tensor = torch.arange(2, device="cuda")
    print(f"{mp_sum(input_tensor)=}")
    torch.cuda.synchronize()


def test_parallel_multiple_functions():
    """Model parallel wrapper can be used multiple times; smoke test only."""
    mp1 = parallel.ParallelRunner(num_processes=2)
    mp_sum = mp1.initialize(compute_sum_factory)
    input_tensor = torch.arange(100, device="cuda")
    print(f"{mp_sum(input_tensor)=}")

    # TODO: this part below should work, but doesn't
    # reset the torch.distributed package so that a new process group can be created again.
    # del mp1 does not work, as the object's deletion is apparently delayed.
    mp1.__del__()
    mp2 = parallel.ParallelRunner(num_processes=2)
    mp_sum_2 = mp2.initialize(compute_sum_factory)
    print(f"{mp_sum_2(input_tensor)=}")
    torch.cuda.synchronize()
