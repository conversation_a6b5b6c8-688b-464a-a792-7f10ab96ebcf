"""Tests of cached attention involving multiple GPUs."""

from typing import Optional, <PERSON>ple

import pytest
import torch

from base.fastforward import cached_attention, parallel, torch_utils
from base.fastforward.cached_attention import SplitHeadModes


# test similar to layers_multigpu_test
def _random_weights(shape: Tuple[int, ...]) -> torch.Tensor:
    return torch.randn(shape, dtype=torch.float16, device="cuda")


def _generate_attention_forward(
    num_layers: int,
    process_idx: int,
    num_processes: int,
    split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT,
    num_heads: Optional[int] = None,
    queries_per_head: Optional[int] = None,
    head_dim: Optional[int] = None,
):
    parallel_ctx = parallel.ParallelContext(
        parallel.ParallelConfig.from_legacy_config(
            num_processes, use_sequence_parallel=False
        ),
        process_idx,
    )

    def fn(qkv: torch.Tensor, attn: cached_attention.BasicAttention):
        # NOTE: qkv is [seq_len, num_heads_kv * ((queries_per_head + 2) * head_dim)].
        if split_head_mode == SplitHeadModes.KV_HEADS:
            qkv = torch_utils.shard(
                qkv, axis=1, process_idx=process_idx, num_processes=num_processes
            )
        elif split_head_mode == SplitHeadModes.Q_PER_HEADS:
            assert num_heads is not None
            assert queries_per_head is not None
            assert head_dim is not None
            qkv = qkv.view(-1, num_heads, (queries_per_head + 2) * head_dim)
            q, kv = qkv.split([queries_per_head * head_dim, 2 * head_dim], dim=2)
            q = torch_utils.shard(
                q, axis=2, process_idx=process_idx, num_processes=num_processes
            )
            qkv = torch.concat([q, kv], dim=2).reshape(q.size(0), -1).contiguous()

        torch.cuda.synchronize()
        qkv = qkv.to(f"cuda:{process_idx}")
        seq_len = qkv.shape[0]
        assert attn._mc_attn._parallel_config.num_processes == num_processes
        attn.register_tokens_get_positions(
            torch.arange(seq_len, dtype=torch.int32, device="cuda"),
            process_idx=process_idx,
        )
        for layer_idx in range(num_layers):
            result = attn(qkv, layer_idx=layer_idx, parallel_ctx=parallel_ctx)
        torch.cuda.synchronize()
        if split_head_mode != SplitHeadModes.NO_SPLIT:
            gather_list = [torch.zeros_like(result) for _ in range(num_processes)]
            torch.distributed.all_gather(gather_list, result)
            result = torch.cat(gather_list, dim=1)
        return result

    return fn


@pytest.mark.parametrize(
    "split_head_mode, multiquery",
    (
        pytest.param(SplitHeadModes.NO_SPLIT, False, id="0"),
        pytest.param(SplitHeadModes.NO_SPLIT, True, id="1"),
        pytest.param(SplitHeadModes.KV_HEADS, False, id="2"),
        pytest.param(SplitHeadModes.Q_PER_HEADS, True, id="3"),
    ),
)
def test_cached_attention_multigpu(split_head_mode: SplitHeadModes, multiquery: bool):
    """Test that cached attention produces identical results when running on 2 GPUs."""
    seq_len = 128
    max_len = 1024
    if multiquery:
        queries_per_head = 16
        num_heads = 1
    else:
        queries_per_head = 1
        num_heads = 16
    qkv_dim = 64
    num_layers = 2

    attn_single = cached_attention.BasicAttention(
        mc_attn=cached_attention.MultiCacheAttentionImplementation(
            num_layers=num_layers,
            num_heads=num_heads,
            queries_per_head=queries_per_head,
            max_len=max_len,
            head_dim=qkv_dim,
            num_caches=1,
            parallel_config=parallel.ParallelConfig.single_process(),
        )
    )
    attn_multi = cached_attention.BasicAttention(
        mc_attn=cached_attention.MultiCacheAttentionImplementation(
            num_layers=num_layers,
            num_heads=num_heads,
            queries_per_head=queries_per_head,
            max_len=max_len,
            head_dim=qkv_dim,
            num_caches=1,
            parallel_config=parallel.ParallelConfig.from_legacy_config(
                num_processes=2, use_sequence_parallel=False
            ),
            split_head_mode=split_head_mode,
        )
    )

    single_attn_fn = _generate_attention_forward(
        num_layers=num_layers, process_idx=0, num_processes=1
    )
    runner = parallel.ParallelRunner(num_processes=2)
    multi_attn_fn = runner.initialize(
        init_fn=_generate_attention_forward,
        num_layers=num_layers,
        split_head_mode=split_head_mode,
        num_heads=num_heads,
        queries_per_head=queries_per_head,
        head_dim=qkv_dim,
    )

    qkv = _random_weights((seq_len, (queries_per_head + 2) * num_heads * qkv_dim))
    single_result = single_attn_fn(qkv, attn_single)
    multi_result = multi_attn_fn(qkv, attn_multi)
    assert torch.allclose(single_result, multi_result)
    assert (
        int(attn_single._mc_attn.get_fill_length(cache_idx=0, process_idx=0).item())
        == int(attn_multi._mc_attn.get_fill_length(cache_idx=0, process_idx=0).item())
        == seq_len
    )

    # Another step with a partially filled cache
    qkv2 = _random_weights((seq_len, (queries_per_head + 2) * num_heads * qkv_dim))
    single_result2 = single_attn_fn(qkv2, attn_single)
    multi_result2 = multi_attn_fn(qkv2, attn_multi)
    assert torch.allclose(single_result2, multi_result2)
    assert (
        int(attn_single._mc_attn.get_fill_length(cache_idx=0, process_idx=0).item())
        == int(attn_multi._mc_attn.get_fill_length(cache_idx=0, process_idx=0).item())
        == seq_len * 2
    )
