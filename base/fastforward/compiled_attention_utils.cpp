#include <torch/extension.h>

namespace compiled_attention_utils {

// Forward declaration of cuda entrypoints
void multirequest_flash_copy_queries_cuda(torch::Tensor& q_src, torch::Tensor& qbuf_dst,
                                          torch::Tensor& cumulative_seqlens,
                                          bool is_copy_out_to_result, bool write_trailing_zeros);

void multirequest_flash_copy_cache_metadata_cuda(torch::Tensor& cumulative_seqlens,
                                                 torch::Tensor& cache_idxs,
                                                 torch::Tensor& cache_locs,
                                                 torch::Tensor& cache_batch_idxs_dst,
                                                 torch::Tensor& cache_seqlens_dst);

void split_cumulative_pos_for_sequence_parallel_cuda(torch::Tensor& cumulative_pos,
                                                     int num_tokens_per_device, int process_idx,
                                                     torch::Tensor& split);

void register_tokens_cuda(torch::Tensor& tokens, torch::Tensor& cache_idxs,
                          torch::Tensor& positions, std::optional<torch::Tensor> tokens_cache,
                          int padding_cache_idx, torch::Tensor& new_pos);

torch::Tensor fused_q_contiguous_kv_copy_cuda(torch::Tensor& q, torch::Tensor& k, torch::Tensor& v,
                                              torch::Tensor& kv_pos_idxs, torch::Tensor& k_dest,
                                              torch::Tensor& v_dest, torch::Tensor& kv_cache_idxs);

void check_input(torch::Tensor& x, const char* name, int ndims_expected) {
    TORCH_CHECK(x.dim() == ndims_expected, name, " must be ", ndims_expected, "-dimensional. Got ",
                x.dim());
    TORCH_CHECK(x.is_contiguous(), name, " must be contiguous");
    TORCH_CHECK(x.device().is_cuda(), name, " must be on a GPU");
}

void check_noncontiguous_input(torch::Tensor& x, const char* name, int ndims_expected) {
    TORCH_CHECK(x.dim() == ndims_expected, name, " must be ", ndims_expected, "-dimensional. Got ",
                x.dim());
    TORCH_CHECK(x.device().is_cuda(), name, " must be on a GPU");
}

void check_query_shapes_and_dtypes(torch::Tensor& q, torch::Tensor& q_buf,
                                   torch::Tensor& cumulative_seqlens) {
    TORCH_CHECK(q.size(1) == q_buf.size(2) && q.size(2) == q_buf.size(3));
    TORCH_CHECK(q_buf.size(0) == cumulative_seqlens.size(0) - 1);
    TORCH_CHECK(q.dtype() == q_buf.dtype());
    TORCH_CHECK(cumulative_seqlens.dtype() == torch::kInt32);
}

// Fused kernel for copying the Q array into the multirequest input buffer.
// For the `i`th request in `q_src`:
// - Let start = cumulative_seqlens[i] and end = cumulative_seqlens[i+1]
// - Read the request from `q_src[start:end, ...]
// - Write it into the `i`th row of `qbuf_dst` _with right alignment_. That is:
//   - Let this_seqlen = (end - start)
//   - Write to `qbuf_dst[i, buf_seqlen - this_seqlen, ...]
// NOTE: see `compiled_attention_utils_test.py` for a reference implementation
void multirequest_flash_copy_in_queries(
    torch::Tensor& q_src,              // (round_size, nheads, headdim)
    torch::Tensor& qbuf_dst,           // (nreqs, buf_seqlen, nheads, headdim)
    torch::Tensor& cumulative_seqlens  // (nreqs+1, )
) {
    // Sanity check inputs
    check_input(q_src, "q_src", 3);
    check_input(qbuf_dst, "qbuf_dst", 4);
    check_input(cumulative_seqlens, "cumulative_seqlens", 1);
    check_query_shapes_and_dtypes(q_src, qbuf_dst, cumulative_seqlens);
    multirequest_flash_copy_queries_cuda(q_src, qbuf_dst, cumulative_seqlens, false, false);
}

// Inverse of `multirequest_flash_copy_out_queries` above:
// - Read the result from `qbuf_src[i, buf_seqlen - this_seqlen, ...]`
// - Write to `q_dst[start:end, ...]`
void multirequest_flash_copy_out_results(
    torch::Tensor& qbuf_src,            // (nreqs, buf_seqlen, nheads, headdim)
    torch::Tensor& q_dst,               // (round_size, nheads, headdim)
    torch::Tensor& cumulative_seqlens,  // (nreqs+1, )
    bool write_trailing_zeros) {
    check_input(qbuf_src, "qbuf_src", 4);
    check_input(q_dst, "q_dst", 3);
    check_input(cumulative_seqlens, "cumulative_seqlens", 1);
    check_query_shapes_and_dtypes(q_dst, qbuf_src, cumulative_seqlens);
    if (write_trailing_zeros) {
        int nreqs = cumulative_seqlens.size(0) - 1;
        TORCH_CHECK(nreqs == 1);
        int buf_seqlen = qbuf_src.size(1);
        int round_size = q_dst.size(0);
        // This is to avoid complexity around blockIdx.y needing to be either buf_seqlen or
        // round_size.
        TORCH_CHECK(buf_seqlen == round_size);
    }
    multirequest_flash_copy_queries_cuda(q_dst, qbuf_src, cumulative_seqlens, true,
                                         write_trailing_zeros);
}

// A simple kernel to convert `cache_idxs` and `cache_locs` (as produced by cached_attention)
// into the batch idxs and seqlens expect by flash attention. Flash attention expects:
// - batch_idxs[i] is the row of the kv-cache for the `i`th batch "row" of the input
//   - We can get this by taking `cache_idxs[j]` for any j corresponding to the `i`th request
//   - In practice, we take the last token -- cache_idxs[end - 1] where end = cumulative_seqlens[i]
// - seqlens[i] is the length of the valid kv-cache for the `i`th batch of the input
//   - We can get this by taking the largest `cache_loc` for the `i`th request
//   - In practice, it's the same: cache_locs[end - 1] where end = cumulative_seqlens[i]
// NOTE: it is possible for the round to contain _empty_ requests. For any empty request, we set
//       both the resulting batch_idxs and seqlen to 0 so that no attention occurs at all.
void multirequest_flash_copy_cache_metadata(torch::Tensor& cumulative_seqlens,    // (nreqs+1, )
                                            torch::Tensor& cache_idxs,            // (round_size, )
                                            torch::Tensor& cache_locs,            // (round_size, )
                                            torch::Tensor& cache_batch_idxs_dst,  // (nreqs, )
                                            torch::Tensor& cache_seqlens_dst) {   // (nreqs, )
    check_input(cumulative_seqlens, "cumulative_seqlens", 1);
    check_input(cache_idxs, "cache_idxs", 1);
    check_input(cache_locs, "cache_locs", 1);
    check_input(cache_batch_idxs_dst, "cache_batch_idxs_dst", 1);
    check_input(cache_seqlens_dst, "cache_seqlens_dst", 1);
    TORCH_CHECK(cache_idxs.size(0) == cache_locs.size(0));
    TORCH_CHECK(cache_batch_idxs_dst.size(0) == cache_seqlens_dst.size(0));
    TORCH_CHECK(cumulative_seqlens.size(0) == cache_seqlens_dst.size(0) + 1);

    multirequest_flash_copy_cache_metadata_cuda(cumulative_seqlens, cache_idxs, cache_locs,
                                                cache_batch_idxs_dst, cache_seqlens_dst);
}

torch::Tensor split_cumulative_pos_for_sequence_parallel(torch::Tensor& cumulative_pos,
                                                         int num_tokens_per_device,
                                                         int process_idx) {
    check_input(cumulative_pos, "cumulative_pos", 1);
    TORCH_CHECK(cumulative_pos.size(0) >= 2);
    TORCH_CHECK(cumulative_pos.dtype() == torch::kInt32);
    TORCH_CHECK(cumulative_pos.device().is_cuda());
    TORCH_CHECK(num_tokens_per_device > 0);
    TORCH_CHECK(process_idx >= 0);
    torch::Tensor split = torch::empty_like(cumulative_pos);
    split_cumulative_pos_for_sequence_parallel_cuda(cumulative_pos, num_tokens_per_device,
                                                    process_idx, split);
    return split;
}

torch::Tensor register_tokens_cpp(torch::Tensor& tokens, torch::Tensor& cache_idxs,
                                  torch::Tensor& positions,
                                  std::optional<torch::Tensor> tokens_cache,
                                  int padding_cache_idx) {
    check_input(tokens, "tokens", 1);
    check_input(cache_idxs, "cache_idxs", 1);
    check_input(positions, "positions", 1);
    if (tokens_cache.has_value()) {
        check_input(tokens_cache.value(), "tokens_cache", 2);
    }

    TORCH_CHECK(tokens.dtype() == torch::kInt32);
    TORCH_CHECK(cache_idxs.dtype() == torch::kInt32);
    TORCH_CHECK(positions.dtype() == torch::kInt32);
    if (tokens_cache.has_value()) {
        TORCH_CHECK(tokens_cache.value().dtype() == torch::kInt32);
    }

    TORCH_CHECK(tokens.device() == cache_idxs.device());
    TORCH_CHECK(tokens.device() == positions.device());
    // tokens_cache device should be 0 if it exists

    TORCH_CHECK(tokens.size(0) == cache_idxs.size(0));
    if (tokens_cache.has_value()) {
        TORCH_CHECK(tokens_cache.value().size(0) == positions.size(0));
        TORCH_CHECK(tokens_cache.value().size(1) >= tokens.size(0));

        // Padding cache idx needs to be valid indices into tokens_cache.
        TORCH_CHECK(padding_cache_idx >= 0);
        TORCH_CHECK(tokens_cache.value().size(0) > padding_cache_idx);
    }

    torch::Tensor new_pos = torch::empty_like(tokens);
    register_tokens_cuda(tokens, cache_idxs, positions, tokens_cache, padding_cache_idx, new_pos);
    return new_pos;
}

// A kernel to prepare the q/k/v activations for self-attention when _not_ using rotary embeddings.
// This kernel does two things at once:
// 1. Copy the `q` activations to a contiguous tensor. (This is the return value.)
// 2. Copy the `k` and `v` activations into the KV caches at the given positions.
// Returns: the contiguous `q` activations.
torch::Tensor fused_q_contiguous_kv_copy(
    torch::Tensor& q,             // (ntokens, nheads_kv, q_per_kv, headdim)
    torch::Tensor& k,             // (ntokens, nheads_kv, headdim)
    torch::Tensor& v,             // (ntokens, nheads_kv, headdim)
    torch::Tensor& kv_pos_idxs,   // (ntokens, )
    torch::Tensor& k_dest,        // (ncaches, seqlen, nheads_kv, headdim)
    torch::Tensor& v_dest,        // (ncaches, seqlen, nheads_kv, headdim)
    torch::Tensor& kv_cache_idxs  // (ntokens, )
) {
    check_noncontiguous_input(q, "q", 4);
    check_noncontiguous_input(k, "k", 3);
    check_noncontiguous_input(v, "v", 3);
    check_input(kv_pos_idxs, "kv_pos_idxs", 1);
    check_input(k_dest, "k_dest", 4);
    check_input(v_dest, "v_dest", 4);
    check_input(kv_cache_idxs, "kv_cache_idxs", 1);
    // Check for matching token counts
    TORCH_CHECK(q.size(0) == k.size(0) && q.size(0) == v.size(0));
    TORCH_CHECK(q.size(0) == kv_pos_idxs.size(0) && q.size(0) == kv_cache_idxs.size(0));
    // Check for matching head counts
    TORCH_CHECK(k.size(1) == q.size(1) && k.size(1) == v.size(1));
    // Check matching headdims
    TORCH_CHECK(k.size(2) == q.size(3) && k.size(2) == v.size(2));
    TORCH_CHECK(q.size(3) == k_dest.size(3) && q.size(3) == v_dest.size(3));
    return fused_q_contiguous_kv_copy_cuda(q, k, v, kv_pos_idxs, k_dest, v_dest, kv_cache_idxs);
}

}  // namespace compiled_attention_utils

PYBIND11_MODULE(compiled_attention_utils, m) {
    m.def("multirequest_flash_copy_in_queries",
          &compiled_attention_utils::multirequest_flash_copy_in_queries,
          "Kernel to copy in queries for multirequest flash attention.");
    m.def("multirequest_flash_copy_out_results",
          &compiled_attention_utils::multirequest_flash_copy_out_results,
          "Kernel to copy out results from multirequest flash attention.");
    m.def("multirequest_flash_copy_cache_metadata",
          &compiled_attention_utils::multirequest_flash_copy_cache_metadata,
          "Kernel to copy cache metadata for multirequest flash attention.");
    m.def("split_cumulative_pos_for_sequence_parallel",
          &compiled_attention_utils::split_cumulative_pos_for_sequence_parallel,
          "Kernel to split cumulative position tensor for sequence parallelism.");
    m.def("register_tokens_cpp", &compiled_attention_utils::register_tokens_cpp,
          "Kernel to register tokens for cached attention.");
    m.def("fused_q_contiguous_kv_copy", &compiled_attention_utils::fused_q_contiguous_kv_copy,
          "Kernel to copy Q into a contiguous tensor and K/V into the KV cache.");
}
