"""Fastforward implementation for DBRX architectures."""

import logging
from typing import Sequence

import torch
import torch.nn as nn

from base.fastforward import fwd, fwd_torch, positional_embeddings
from base.fastforward.cached_attention import (
    Attention,
    MultiCacheAttention,
    MultiCacheAttentionImplementation,
    SplitHeadModes,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.dbrx import model_specs
from base.fastforward.layers import DbrxTransformerBlock, Device, WordEmbeddings
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.parallel_fwd import ParallelForwardRunner

Tensor = torch.Tensor


class Dbrx(nn.Module):
    """DBRX model: https://www.databricks.com/blog/introducing-dbrx-new-state-art-open-llm."""

    def __init__(
        self,
        ms: model_specs.DbrxModelSpec,
        dtype: torch.dtype = torch.bfloat16,
        device: Device = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
    ):
        super().__init__()
        self._dtype = dtype
        self._device = device
        self._process_idx = process_idx
        self._parallel_ctx = ParallelContext(
            ParallelConfig.from_legacy_config(num_processes, False), process_idx
        )

        self.embs = WordEmbeddings(
            vocab_size=ms.vocab_size,
            emb_dim=ms.emb_dim,
            dtype=dtype,
            device=device,
        )

        self.layers = nn.ModuleList()
        for _ in range(ms.num_layers):
            self.layers.append(
                DbrxTransformerBlock(
                    emb_dim=ms.emb_dim,
                    num_heads_q=ms.num_heads_q,
                    num_heads_kv=ms.num_heads_kv,
                    head_dim=ms.head_dim,
                    split_head_mode=ms.attn_split_head_mode,
                    clip_qkv=ms.attn_clip_qkv,
                    mlp_dim=ms.mlp_hidden_dim,
                    moe_num_experts=ms.moe_num_experts,
                    moe_top_k=ms.moe_top_k,
                    moe_normalize_expert_weights=ms.moe_normalize_expert_weights,
                    dtype=dtype,
                    device=device,
                    parallel_ctx=self._parallel_ctx,
                )
            )

        self.final_layer_norm = nn.LayerNorm(
            normalized_shape=ms.emb_dim,
            eps=ms.norm_eps,
            bias=False,
            dtype=dtype,
            device=device,
        )

        self.score = nn.Linear(
            in_features=ms.emb_dim,
            out_features=ms.vocab_size,
            bias=False,
            device=device,
            dtype=dtype,
        )

    @property
    def dtype(self) -> torch.dtype:
        return self._dtype

    @torch.no_grad()
    def forward(self, tokens: Sequence[int], attn: Attention) -> fwd.ModelOutput:
        tokens_on_device = torch.tensor(tokens, dtype=torch.int32, device=self._device)

        attn.register_tokens_get_positions(
            tokens_on_device, process_idx=self._process_idx
        )
        x = self.embs(tokens_on_device)
        for layer_idx, layer in enumerate(self.layers):
            x = layer(x, attn=attn, layer_idx=layer_idx)

        x = self.final_layer_norm(x)
        x = self.score(x)
        return fwd_torch.TorchLogits2D(x)


class DbrxAttentionFactory(fwd.AttentionFactory):
    """Creates attention caches for DBRX models."""

    # TODO (hieu): if we switch to *only* using DBRX models, we should think
    # about never using AttentionFactory.__call__ again. Then, we should change
    # the logic of DBRX models to directly call MultiCacheAttention, whose
    # interface is much richer than Attention.

    def __init__(
        self,
        ms: model_specs.DbrxModelSpec,
        num_processes: int = 1,
        dtype: torch.dtype = torch.bfloat16,
        max_requests_in_round: int | None = None,
        small_request_max_seqlen: int | None = None,
    ):
        """The AttentionFactory for DBRX models.

        Args:
            ms: the specific model_specs.DbrxModelSpec or similar model spec.
            num_processes: for tensor parallelism.
            dtype: the dtype of the `qkv` tensor that will be passed to Attention
                objects created by this factory. we also use this `dtype` to construct
                this object's KV-caches.
        """
        self._ms = ms
        self._parallel_config = ParallelConfig.from_legacy_config(num_processes, False)
        self._dtype = dtype
        self._max_requests_in_round = max_requests_in_round
        self._small_request_max_seqlen = small_request_max_seqlen

    def create_cache_pool(
        self,
        max_length: int,
        num_attention_caches: int,
    ) -> MultiCacheAttention:
        assert (
            self._ms.max_position_embeddings
            == self._ms.unscaled_max_position_embeddings
        )
        assert self._ms.rotary_scaling_factor == 1.0
        rotary_config = positional_embeddings.RotaryConfig(
            rotary_ratio=self._ms.rotary_pct,
            rotary_theta=self._ms.rotary_theta,
            max_position_embeddings=self._ms.max_position_embeddings,
            rotary_interleave=self._ms.rotary_interleave,
        )
        mc_attn = MultiCacheAttentionImplementation(
            num_caches=num_attention_caches,
            num_layers=self._ms.num_layers,
            num_heads=self._ms.num_heads,
            queries_per_head=self._ms.num_queries_per_head,
            max_len=max_length,
            dtype=self._dtype,
            head_dim=self._ms.head_dim,
            parallel_config=self._parallel_config,
            split_head_mode=self._ms.attn_split_head_mode,
            rotary_config=rotary_config,
            max_requests_in_round=self._max_requests_in_round,
            small_request_max_seqlen=self._small_request_max_seqlen,
        )
        return mc_attn


########################################################
# Weight sharding functions while loading checkpoints. #
########################################################


def _shard_efficient_attn_qkv_weight(
    w: torch.Tensor,
    num_heads_q: int,
    num_heads_kv: int,
    head_dim: int,
    attn_split_head_mode: SplitHeadModes,
    process_idx: int,
    num_processes: int,
) -> torch.Tensor:
    """Shard the Attention layer's qkv.weight."""
    assert num_processes > 1, "Should not call this function with num_processes=1."
    emb_dim = w.numel() // ((num_heads_q + 2 * num_heads_kv) * head_dim)
    assert w.numel() == emb_dim * ((num_heads_q + 2 * num_heads_kv) * head_dim)
    num_queries_per_head = num_heads_q // num_heads_kv
    assert num_heads_q == num_heads_kv * num_queries_per_head

    w = w.view(num_heads_kv, (num_queries_per_head + 2) * head_dim, emb_dim)

    if attn_split_head_mode == SplitHeadModes.KV_HEADS:
        # Typically used in MHA or GQA
        # Each device holds (num_heads_kv // num_processes) KV-heads,
        # along with their corresponding Q-heads.
        w = w.chunk(num_processes, dim=0)[process_idx].reshape(-1, emb_dim)
    elif attn_split_head_mode == SplitHeadModes.Q_PER_HEADS:
        # Typically used in MQA or GQA with few KV-heads
        # Each device holds all num_heads_kv  KV-heads, along with their
        # corresponding block of Q-heads.
        wq, w_kv = w.split([num_queries_per_head * head_dim, 2 * head_dim], dim=1)
        wq = wq.chunk(num_processes, dim=1)[process_idx]
        w = torch.concat(
            [
                wq.reshape(-1, emb_dim),
                w_kv.reshape(-1, emb_dim),
            ],
            dim=0,
        ).view(-1, emb_dim)

    return w


def _generate_step_fn(
    ms: model_specs.DbrxModelSpec,
    dtype: torch.dtype = torch.bfloat16,
    batch_sizes: Sequence[int] | None = None,
    process_idx: int = 0,
    num_processes: int = 1,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
) -> fwd.ForwardStepFn:
    logging.info("Loading a DBRX model %s onto %d processes.", ms.name, num_processes)
    logging.debug(
        "ms: %s, max_batch_size: %s, process_idx: %s, num_processes: %s",
        ms,
        batch_sizes,
        process_idx,
        num_processes,
    )
    assert not auto_capture_graphs, "Not implemented yet."
    assert batch_sizes is None, "Not implemented yet."

    if torch.cuda.is_available():
        device = f"cuda:{process_idx}"
    else:
        device = "cpu"
        logging.warning("CUDA is not available, using CPU instead.")
        assert num_processes == 1 and process_idx == 0
        assert not auto_capture_graphs, "CPU does not support auto_capture_graphs."

    model = Dbrx(
        ms=ms,
        dtype=dtype,
        device=device,
        process_idx=process_idx,
        num_processes=num_processes,
    )

    if load_checkpoint_weights:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        # Load non-transformer-block layers and they do not need shard.
        loaded_unique_keys = set()
        weights = save_load.load_weights(
            ms.checkpoint_path,
            require_patterns=(r"^embs\.", r"^final_layer_norm\.", r"^score\."),
            target_sha256=ms.checkpoint_sha256,
        )
        loaded_unique_keys.update(weights.keys())
        model.load_state_dict(weights, strict=False)
        del weights
        for ilayer in range(ms.num_layers):
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            weights = save_load.load_weights(
                ms.checkpoint_path,
                require_patterns=(rf"^layers\.{ilayer}\.",),
                target_sha256=ms.checkpoint_sha256,
            )
            # Shard the DbrxFFN (MoE) layer weights
            if num_processes > 1:
                weights[f"layers.{ilayer}.ffn.v1"] = weights[
                    f"layers.{ilayer}.ffn.v1"
                ].chunk(num_processes, dim=-1)[process_idx]
                weights[f"layers.{ilayer}.ffn.w1"] = weights[
                    f"layers.{ilayer}.ffn.w1"
                ].chunk(num_processes, dim=-1)[process_idx]
                weights[f"layers.{ilayer}.ffn.w2"] = weights[
                    f"layers.{ilayer}.ffn.w2"
                ].chunk(num_processes, dim=1)[process_idx]
            # Shard the Attention weights.
            if num_processes > 1 and ms.attn_split_head_mode != SplitHeadModes.NO_SPLIT:
                weights[f"layers.{ilayer}.attn.out.weight"] = weights[
                    f"layers.{ilayer}.attn.out.weight"
                ].chunk(num_processes, dim=1)[process_idx]
                weights[f"layers.{ilayer}.attn.qkv.weight"] = (
                    _shard_efficient_attn_qkv_weight(
                        weights[f"layers.{ilayer}.attn.qkv.weight"],
                        num_heads_q=ms.num_heads_q,
                        num_heads_kv=ms.num_heads_kv,
                        head_dim=ms.head_dim,
                        attn_split_head_mode=ms.attn_split_head_mode,
                        process_idx=process_idx,
                        num_processes=num_processes,
                    )
                )
            loaded_unique_keys.update(weights.keys())
            model.load_state_dict(weights, strict=False)
            del weights
            if process_idx == 0:
                logging.info(f"Finish loading weights for layer {ilayer}.")
        assert (
            len(loaded_unique_keys) == len(model.state_dict().keys())
        ), f"The following keys are not found in the model: {set(model.state_dict().keys()) - loaded_unique_keys}"

    return model


def generate_step_fn(
    ms: model_specs.DbrxModelSpec,
    num_processes: int = 1,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        ms: Model spec.
        num_processes: Number of GPUs to use.
        load_checkpoint_weights: load the weights into the step_fn. should be `True`
            by default. only set to `False` for testing, and if so, make sure you
            know what you are doing.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
    """
    if num_processes <= 0:
        raise ValueError(f"{num_processes=} is invalid.")

    if num_processes == 1:
        return _generate_step_fn(
            ms=ms,
            batch_sizes=batch_sizes,
            load_checkpoint_weights=load_checkpoint_weights,
            auto_capture_graphs=auto_capture_graphs,
        )
    else:
        mp = ParallelForwardRunner(num_processes=num_processes)
        return mp.initialize(
            [_generate_step_fn],
            [
                {
                    "ms": ms,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                }
            ],
        )
