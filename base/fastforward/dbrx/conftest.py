"""conftest.py is a special file that pytest will automatically load."""

import pytest
from torch._subclasses.fake_tensor import FakeTensorMode

from base.fastforward import fwd
from base.fastforward.dbrx import fwd_dbrx, model_specs


@pytest.fixture(scope="module")
def dbrx_132b_fp16_fake_fixture():
    """Load a fake DBRX model to use for basic tests."""
    ms = model_specs.get_model_spec(
        model_name="dbrx-132b",
        checkpoint_path="",
    )
    fake_mode = FakeTensorMode()
    with fake_mode:
        return fwd_dbrx.Dbrx(ms=ms)


@pytest.fixture(scope="module")
def dbrx_nano_base_fp16_fixture() -> (
    tuple[model_specs.DbrxModelSpec, fwd.ForwardStepFn, fwd.AttentionFactory]
):
    """Load the weights of a dbrx-nano model to use for all tests."""
    ms = model_specs.get_model_spec(
        model_name="dbrx-nano",
        checkpoint_path="/mnt/efs/augment/checkpoints/databricks/dbrx-nano-ffw",
        checkpoint_sha256="637f62c811cd347686ebb03bd732c824afd613b0d5bee0c881274ebfdcd3189a",
    )
    step_fn = fwd_dbrx.generate_step_fn(ms, auto_capture_graphs=False, batch_sizes=None)
    attn_factory = fwd_dbrx.DbrxAttentionFactory(ms=ms)
    return ms, step_fn, attn_factory
