"""Unit Tests for ffw_dbrx."""

import torch


def test_parameters(dbrx_132b_fp16_fake_fixture):
    """Ensure that the large model can initialize."""
    model = dbrx_132b_fp16_fake_fixture
    total = 0
    for p in model.parameters():
        total += p.numel()
    assert total == 131_596_523_520


def test_generate_wo_cuda_graph(dbrx_nano_base_fp16_fixture):
    """Ensure that the model generates the correct outputs."""
    ms, step_fn, attn_factory = dbrx_nano_base_fp16_fixture
    assert ms.name == "dbrx-nano", "this test only runs for dbrx-nano."
    attn = attn_factory(128)
    attn.reset()

    inp = [35, 12, 4, 53, 6, 304, 220]
    output_tokens = []
    for _ in range(5):
        logits = step_fn(list(inp), attn).checked_cast(torch.Tensor)
        next_tok = int(logits[-1].argmax().item())
        output_tokens.append(next_tok)
        inp = [next_tok]
    # This is collected by running this nano model offline.
    # The weights of this nano model is randomly generated but we then fix the weights for unit test
    # to make sure nothing breaks.
    assert output_tokens == [193, 179, 377, 375, 664]
