"""ModelSpecs for Starcoder and Starcoder2 models."""

import dataclasses
import json
import logging
from pathlib import Path

from base.fastforward.cached_attention import SplitHeadModes
from base.fastforward.fwd import ModelSpec


@dataclasses.dataclass
class StarCoder2ModelSpec(ModelSpec):
    """ModelSpec for StarCoder2 models. Needs to specify num_queries_per_had for GQA."""

    num_heads_q: int = 0
    """Number of attention Q heads."""

    num_heads_kv: int = 0
    """Number of attention KV heads."""

    mlp_hidden_dim: int = 0
    """Typically 4 * self.emb_dim."""

    attn_split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT
    """Tensor parallelism in attention layers. None means to not use."""

    def __post_init__(self):
        super().__post_init__()
        assert self.num_heads_q > 0
        assert self.num_heads_kv > 0
        assert self.mlp_hidden_dim > 0

        assert self.num_heads_q % self.num_heads_kv == 0
        assert self.num_heads_q == self.num_heads
        assert self.mlp_hidden_dim == 4 * self.emb_dim


_STARCODER_MODEL_SPECS: dict[str, ModelSpec] = {
    "starcoder-1b": ModelSpec(
        name="starcoder-1b",
        checkpoint_path="",
        emb_dim=2048,
        num_layers=24,
        num_heads=16,  # this is num_heads_q. StarCoder1 models are MQA.
        head_dim=128,
        rotary_scaling_factor=1.0,
        rotary_pct=0.0,
        vocab_size=51200,
        norm_eps=1e-05,
        max_position_embeddings=8192,
    ),
}


_STARCODER2_MODEL_SPECS: dict[str, StarCoder2ModelSpec] = {
    "starcoder2-100m": StarCoder2ModelSpec(
        name="starcoder2-100m",
        checkpoint_path="",
        emb_dim=1024,
        mlp_hidden_dim=4 * 1024,
        num_layers=12,
        num_heads_kv=1,
        num_heads_q=8,
        num_heads=8,  # TODO(hieu): delete after fixing attention head names
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_scaling_factor=1.0,
        rotary_theta=1e6,
        rotary_pct=1.0,
        vocab_size=49176,
        norm_eps=1e-05,
        max_position_embeddings=4096,
        unscaled_max_position_embeddings=4096,
    ),
    "starcoder2-3b": StarCoder2ModelSpec(
        name="starcoder2-3b",
        checkpoint_path="",
        emb_dim=3072,
        mlp_hidden_dim=4 * 3072,
        num_layers=30,
        num_heads_kv=2,
        num_heads_q=24,
        num_heads=24,  # TODO(hieu): delete after fixing attention head names
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_scaling_factor=1.0,
        rotary_theta=999999.4420358813,
        rotary_pct=1.0,
        vocab_size=49152,
        norm_eps=1e-05,
        max_position_embeddings=2048,
        unscaled_max_position_embeddings=2048,
    ),
    "starcoder2-7b": StarCoder2ModelSpec(
        name="starcoder2-7b",
        checkpoint_path="",
        emb_dim=4608,
        mlp_hidden_dim=4 * 4608,
        num_layers=32,
        num_heads_kv=4,
        num_heads_q=36,
        num_heads=36,  # TODO(hieu): delete after fixing attention head names
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_scaling_factor=1.0,
        rotary_theta=1000000.0,
        rotary_pct=1.0,
        vocab_size=49152,
        norm_eps=1e-05,
        max_position_embeddings=4096,
        unscaled_max_position_embeddings=4096,
    ),
    "starcoder2-15b": StarCoder2ModelSpec(
        name="starcoder2-15b",
        checkpoint_path="",
        emb_dim=6144,
        mlp_hidden_dim=4 * 6144,
        num_layers=40,
        num_heads_kv=4,
        num_heads_q=48,
        num_heads=48,  # TODO(hieu): delete after fixing attention head names
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_scaling_factor=1.0,
        rotary_theta=100000.0,
        rotary_pct=1.0,
        vocab_size=49152,
        norm_eps=1e-05,
        max_position_embeddings=7936,
        unscaled_max_position_embeddings=7936,
    ),
}


def get_starcoder_model_spec(
    model_name: str,
    checkpoint_path: Path | str | None = None,
    checkpoint_sha256: str | None = None,
) -> ModelSpec:
    ms = _STARCODER_MODEL_SPECS[model_name]
    ms = dataclasses.replace(ms)
    if checkpoint_path is not None:
        if not isinstance(checkpoint_path, str):
            checkpoint_path = str(checkpoint_path.absolute())
        ms.checkpoint_path = checkpoint_path
    if checkpoint_sha256 is not None:
        ms.checkpoint_sha256 = checkpoint_sha256
    return ms


def get_starcoder2_model_spec(
    model_name: str,
    checkpoint_path: Path | str | None = None,
    checkpoint_sha256: str | None = None,
) -> StarCoder2ModelSpec:
    ms = _STARCODER2_MODEL_SPECS[model_name]
    ms = dataclasses.replace(ms)
    if checkpoint_path is not None:
        if not isinstance(checkpoint_path, str):
            checkpoint_path = str(checkpoint_path.absolute())
        ms.checkpoint_path = checkpoint_path
    if checkpoint_sha256 is not None:
        ms.checkpoint_sha256 = checkpoint_sha256
    return ms


def starcoder2_model_spec_from_checkpoint(ckpt_path: Path | str) -> StarCoder2ModelSpec:
    """Get a StarCoder2ModelSpec from a checkpoint path."""
    ckpt_path = Path(ckpt_path)
    params_path = ckpt_path / "params.json"
    params_dict: dict = json.loads(params_path.read_text())

    # TODO: we should save version numbers to the config json and load them differently
    # depending on the config version.
    # below is for backward compatibility
    params_dict.pop("rotary_cast_sincos_as_input", None)
    params_dict.pop("uses_yarn", None)

    spec = StarCoder2ModelSpec(**params_dict)
    spec.checkpoint_path = str(ckpt_path)
    if isinstance(spec.attn_split_head_mode, int):
        # convert the int back to enum
        spec.attn_split_head_mode = SplitHeadModes(spec.attn_split_head_mode)
    if spec.max_position_embeddings < 7936:
        logging.warning(
            "Most StarCoder 2 models in Augment uses a `max_position_embeddings`"
            " of 7936 or 8192. The current value %d is suspiciously small."
            " Meanwhile, many older checkpoint files have incorrectly low values."
            " Please double check.",
            spec.max_position_embeddings,
        )
    return spec
