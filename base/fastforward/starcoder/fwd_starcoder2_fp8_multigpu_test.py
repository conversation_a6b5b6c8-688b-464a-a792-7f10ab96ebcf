"""Tests for StarCoder2."""

import pytest
import torch

from base.fastforward import fwd_model_test_utils, fwd_utils
from base.fastforward.all_reduce import AllReduceImpl
from base.fastforward.cached_attention import AttentionImpl, SplitHeadModes
from base.fastforward.parallel import ParallelConfig
from base.fastforward.starcoder import fwd_starcoder2, fwd_starcoder2_fp8, model_specs


def test_generate(starcoder2_100m_fp8_multigpu_fixture, starcoder2_test_data):
    step_fn, attn_factory, max_round_size = starcoder2_100m_fp8_multigpu_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=starcoder2_test_data["inputs"],
        target_sequence=starcoder2_test_data["outputs"],
        allowed_mismatches=1,
        extra_kv_len=max_round_size,  # for padding
    )


def test_sequence_parallel(
    starcoder2_100m_fp8_sequence_parallel_fixture, starcoder2_test_data
):
    step_fn, attn_factory, max_round_size = (
        starcoder2_100m_fp8_sequence_parallel_fixture
    )
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=starcoder2_test_data["inputs"],
        target_sequence=starcoder2_test_data["outputs"],
        extra_kv_len=max_round_size,  # for padding
    )


def test_dynamic_sequence_parallel(
    starcoder2_100m_fp8_dynamic_sequence_parallel_fixture, starcoder2_test_data
):
    step_fn, attn_factory, max_round_size = (
        starcoder2_100m_fp8_dynamic_sequence_parallel_fixture
    )
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=starcoder2_test_data["inputs"],
        target_sequence=starcoder2_test_data["outputs"],
        extra_kv_len=max_round_size,  # for padding
    )


@pytest.mark.parametrize("pre_attention_kernel_fusion", [True, False])
@pytest.mark.parametrize(
    "all_reduce_impl", [AllReduceImpl.NCCL, AllReduceImpl.FASTFORWARD]
)
@pytest.mark.parametrize("length", [1, 17])
def test_compare_sequence_parallel_logits(
    starcoder2_100m_fp8_model_spec_fixture,
    starcoder2_100m_fp8_fixture,
    starcoder2_test_data,
    pre_attention_kernel_fusion,
    all_reduce_impl,
    length,
):
    input_tokens = starcoder2_test_data["inputs"][:length]

    step_fn_single_gpu, attn_factory_single_gpu, _ = starcoder2_100m_fp8_fixture
    # NOTE: re-use same attention impl between the single- and multi-gpu models
    attention_impl = attn_factory_single_gpu._attention_impl  # pylint: disable=protected-access

    ms = starcoder2_100m_fp8_model_spec_fixture
    parallel_config = ParallelConfig.from_legacy_config(2, True)
    step_fn_seq_par = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
        parallel_config=parallel_config,
        all_reduce_impl=all_reduce_impl,
    )
    padded_step_fn_seq_par = fwd_utils.pad_and_step(
        step_fn_seq_par, round_sizes=[16, 512]
    )
    attn_factory_seq_par = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        attention_impl=attention_impl,
        pre_attention_kernel_fusion=pre_attention_kernel_fusion,
        parallel_config=parallel_config,
    )

    # check logits are close
    fwd_model_test_utils.check_logits_are_close(
        step_fn_1=step_fn_single_gpu,
        step_fn_2=padded_step_fn_seq_par,
        attn_factory_1=attn_factory_single_gpu,
        attn_factory_2=attn_factory_seq_par,
        test_tokens=input_tokens,
        extra_kv_len=4096,
    )

    del (
        step_fn_seq_par
    )  # tear down parallel runner so that the next test can use nccl again


@pytest.mark.parametrize(
    "all_reduce_impl", [AllReduceImpl.NCCL, AllReduceImpl.FASTFORWARD]
)
@pytest.mark.parametrize("length", [1, 17])
def test_compare_dynamic_sequence_parallel_logits(
    starcoder2_100m_fp8_model_spec_fixture,
    starcoder2_100m_fp8_fixture,
    starcoder2_test_data,
    all_reduce_impl,
    length,
):
    input_tokens = starcoder2_test_data["inputs"][:length]

    step_fn_single_gpu, attn_factory_single_gpu, _ = starcoder2_100m_fp8_fixture
    # NOTE: re-use same attention impl between the single- and multi-gpu models
    attention_impl = attn_factory_single_gpu._attention_impl  # pylint: disable=protected-access

    ms = starcoder2_100m_fp8_model_spec_fixture
    num_processes = 2
    sp_parallel_config = ParallelConfig.from_legacy_config(num_processes, True)
    tp_parallel_config = ParallelConfig.from_legacy_config(num_processes, False)
    step_fn_dyn_seq_par = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
        parallel_config=sp_parallel_config,
        all_reduce_impl=all_reduce_impl,
        small_round_parallel_config=tp_parallel_config,
        small_round_token_cutoff=16,
    )
    padded_step_fn_dyn_seq_par = fwd_utils.pad_and_step(
        step_fn_dyn_seq_par, round_sizes=[16, 512]
    )
    attn_factory_dyn_seq_par = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        parallel_config=sp_parallel_config,
        attention_impl=attention_impl,
        pre_attention_kernel_fusion=True,
    )

    # check logits are close
    fwd_model_test_utils.check_logits_are_close(
        step_fn_1=step_fn_single_gpu,
        step_fn_2=padded_step_fn_dyn_seq_par,
        attn_factory_1=attn_factory_single_gpu,
        attn_factory_2=attn_factory_dyn_seq_par,
        test_tokens=input_tokens,
        extra_kv_len=4096,
    )

    del (
        step_fn_dyn_seq_par
    )  # tear down parallel runner so that the next test can use nccl again


# Test that checks each head-split mode (2-way tensor parallel) and compares with single-gpu.
@pytest.mark.parametrize("split_head_mode", SplitHeadModes)
def test_compare_split_head_mode_logits(starcoder2_test_data, split_head_mode):
    round_sizes = [16, 512]
    max_seqlen = 8192

    # Note we need to use the 3B model, not the 100M model, since we need >1 kv-head.
    ms = model_specs.get_starcoder2_model_spec(
        model_name="starcoder2-3b",
        checkpoint_path="/mnt/efs/augment/checkpoints/starcoder2/fastforward/starcoder2-3b-fp8",
        checkpoint_sha256="d333787ae408b2fdd8d3d2f9a7e3d0f5c283853b60711ea781ef66a1451126c6",
    )
    ms.attn_split_head_mode = split_head_mode
    toks = [tok for tok in starcoder2_test_data["inputs"] if tok < ms.vocab_size]

    # Construct a 1-GPU model and run forward
    step_1gpu = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
        batch_sizes=round_sizes,
        all_reduce_impl=AllReduceImpl.FASTFORWARD,
    )
    step_1gpu = fwd_utils.pad_and_step(step_1gpu, round_sizes=round_sizes)
    attn_factory_1gpu = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        attention_impl=AttentionImpl.MULTI_REQUEST_FLASH,
    )
    attn_1gpu = attn_factory_1gpu(max_seqlen)
    logits_1gpu = step_1gpu(toks, attn_1gpu).checked_cast(torch.Tensor)

    # Construct a 2-GPU model and run forward
    parallel_config_2gpu = ParallelConfig(num_processes=2, sp_size=1, tp_size=2)
    step_2gpu = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
        parallel_config=parallel_config_2gpu,
        batch_sizes=round_sizes,
        all_reduce_impl=AllReduceImpl.FASTFORWARD,
    )
    step_2gpu = fwd_utils.pad_and_step(step_2gpu, round_sizes=round_sizes)
    attn_factory_2gpu = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        parallel_config=parallel_config_2gpu,
        attention_impl=AttentionImpl.MULTI_REQUEST_FLASH,
    )
    attn_2gpu = attn_factory_2gpu(max_seqlen)
    logits_2gpu = step_2gpu(toks, attn_2gpu).checked_cast(torch.Tensor)

    # Compare the greedy tokens generated by the two models
    toks_1gpu = torch.argmax(logits_1gpu, dim=-1)
    toks_2gpu = torch.argmax(logits_2gpu, dim=-1)
    match_rate = (toks_1gpu == toks_2gpu).sum().item() / toks_1gpu.numel()
    # Empirically, I see >98% match rate for 1000s of tokens. This adds a small margin.
    assert match_rate > 0.975
