"""An implementation of the StarCoder forward pass without torch.nn.

This code here assumes the checkpoint format of StarCoder that has been
integrated into our gpt-neox codebase.
"""

import logging
from typing import Sequence

import torch
import torch.nn as nn

from base.fastforward import (
    cached_attention,
    cuda_graphs_attention,
    fwd,
    fwd_torch,
    torch_utils,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.layers import InitialEmbeddings
from base.fastforward.layers_fp8 import QKV, AttnOutMLP, CrossLayer
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.starcoder import fwd_starcoder

SC_KNOWN_SEQUENCE = fwd_starcoder.SC_KNOWN_SEQUENCE


class StarCoder(nn.Module):
    """StarCoder model."""

    def __init__(
        self,
        model_spec: fwd.ModelSpec,
        auto_capture_graphs: bool = False,
        batch_sizes: Sequence[int] | None = None,
        device: torch.device | str = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
        output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
    ):
        super().__init__()
        assert (
            process_idx == 0 and num_processes == 1
        ), "Only single GPU is supported for now."
        self.model_spec = model_spec
        self._parallel_ctx = ParallelContext(
            ParallelConfig.from_legacy_config(num_processes, False), process_idx
        )
        self._auto_capture_graphs = auto_capture_graphs

        self.initial_embeddings = InitialEmbeddings(
            vocab_size=model_spec.vocab_size,
            positions_size=model_spec.max_position_embeddings,
            embedding_dim=model_spec.emb_dim,
            device=device,
        )
        self.first_qkv = QKV(
            model_spec.num_heads,
            model_spec.head_dim,
            model_spec.emb_dim,
            layernorm_eps=model_spec.norm_eps,
            device=device,
        )
        # NOTE: fp8 only supports F.gelu, not the tanh approximation.
        self.final_mlp = AttnOutMLP(
            model_spec.emb_dim, layernorm_eps=model_spec.norm_eps, device=device
        )
        self.cross_layers = nn.ModuleList(
            [
                CrossLayer(
                    model_spec.num_heads,
                    model_spec.head_dim,
                    model_spec.emb_dim,
                    layernorm_eps=model_spec.norm_eps,
                    device=device,
                    process_idx=process_idx,
                    num_processes=num_processes,
                )
                for _ in range(model_spec.num_layers - 1)
            ]
        )
        self.final_layernorm = nn.LayerNorm(
            model_spec.emb_dim,
            eps=model_spec.norm_eps,
            device=device,
            dtype=torch.float16,
        )

        self.output_type = output_type
        if output_type == fwd.OutputTensorType.VOCAB_LOGITS:
            self.final_projection = nn.Linear(
                model_spec.emb_dim,
                model_spec.vocab_size,
                bias=False,
                device=device,
                dtype=torch.float16,
            )
        else:
            assert output_type == fwd.OutputTensorType.EMBEDDING
            assert model_spec.output_projection_dim is not None
            self.final_projection = nn.Linear(
                model_spec.emb_dim,
                model_spec.output_projection_dim,
                bias=True,
                device=device,
                dtype=torch.float16,
            )

        if self._auto_capture_graphs:
            if batch_sizes is None:
                raise ValueError(
                    "batch_sizes must be specified if auto_capture_graphs is True."
                )
            self._apply_transformer_layers = (
                cuda_graphs_attention.GraphedEmbForwardPass(
                    step_fn=self._emb_step,
                    batch_sizes=batch_sizes,
                )
            )
        else:
            self._apply_transformer_layers = self._emb_step

    def _emb_step(
        self, emb: torch.Tensor, attn: cached_attention.Attention
    ) -> torch.Tensor:
        qkv_result = self.first_qkv(emb)
        attn_result = attn(qkv_result, layer_idx=0, parallel_ctx=self._parallel_ctx)

        # Cross layers
        for cross_layer_idx, layer in enumerate(self.cross_layers):
            attn_layer_idx = cross_layer_idx + 1
            emb, qkv_result = layer(emb, attn_result)
            attn_result = attn(
                qkv_result, layer_idx=attn_layer_idx, parallel_ctx=self._parallel_ctx
            )

        # Final layer
        emb = self.final_mlp(emb, attn_result)  # type: ignore
        emb = self.final_layernorm(emb)
        return emb

    @torch.no_grad()
    def forward(
        self,
        tokens: Sequence[int],
        attn: cached_attention.Attention,
    ) -> fwd.ModelOutput:
        tokens_tensor = torch.tensor(tokens, dtype=torch.int32, device="cuda")

        batch_size = tokens_tensor.numel()
        if (batch_size % 8) != 0:
            raise ValueError(
                f"Batch size must be a multiple of 8, got {batch_size}. "
                "You can use fwd_utils.PaddedStepFunction to wrap the step "
                "function and enjoy automatic padding."
            )
        cache_pos = attn.register_tokens_get_positions(
            tokens_tensor, process_idx=self._parallel_ctx.process_idx
        )

        # First layer
        with torch_utils.ProfilingContext("step"):
            emb = self.initial_embeddings.position_embeddings[cache_pos].add_(
                self.initial_embeddings.word_embeddings[tokens_tensor]
            )
            emb = self._apply_transformer_layers(emb, attn)
            scores = self.final_projection(emb)

        if self.output_type == fwd.OutputTensorType.EMBEDDING:
            return fwd_torch.TorchEmbedding(scores)
        else:
            return fwd_torch.TorchLogits2D(scores)


def _load_weights(
    model_spec: fwd.ModelSpec,
    device: torch.device | str = "cuda",
) -> dict[str, torch.Tensor]:
    """Load the model weights from a checkpoint."""
    logging.info("Loading StarCoder model from %s", model_spec.checkpoint_path)

    if model_spec.checkpoint_path.endswith(".pth"):
        logging.warning(
            "Loading StarCoder model from a legacy checkpoint. "
            "This is deprecated and will be removed in the future."
        )
        return torch.load(model_spec.checkpoint_path, map_location=device)

    return save_load.load_weights(
        path=model_spec.checkpoint_path,
        device=device,
        target_sha256=model_spec.checkpoint_sha256,
    )


def _loaded_weights_are_valid(
    loaded_weights: set[str],
    model_weights: set[str],
) -> bool:
    # Exact match is always valid
    if loaded_weights == model_weights:
        return True
    # Unassigned keys are never valid (this should be caught beforehand)
    if len(loaded_weights - model_weights) > 0:
        return False
    missing_keys = model_weights - loaded_weights
    output_scale_keys = set(k for k in model_weights if k.endswith(".output_scale"))
    # Valid case: missing exactly the output scale params
    if missing_keys == output_scale_keys:
        return True
    return False


def generate_step_fn(
    model_spec: fwd.ModelSpec,
    num_processes: int = 1,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        model_spec: Model spec.
        num_processes: Number of GPUs to use.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
    """
    assert num_processes == 1, "Only single GPU is supported for now."
    device = torch.device("cuda")
    model = StarCoder(
        model_spec=model_spec,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
        device=device,
        output_type=output_type,
    )
    state_dict = _load_weights(model_spec, device=device)
    load_result = model.load_state_dict(state_dict, strict=False)
    if load_result.unexpected_keys:
        raise RuntimeError(f"Unexpected keys: {load_result.unexpected_keys}.")
    loaded_weights = set(state_dict.keys())
    model_weights = set(model.state_dict().keys())
    if not _loaded_weights_are_valid(loaded_weights, model_weights):
        raise RuntimeError(
            f"Invalid set of loaded weights.\n{loaded_weights=}.\n{model_weights=}"
        )

    return model


# Alias for compatibility with clients that import from this (fp8) module.
StarcoderAttentionFactory = fwd_starcoder.StarcoderAttentionFactory
