"""An implementation of the StarCoder forward pass without torch.nn.

This code here assumes the checkpoint format of StarCoder that has been
integrated into our gpt-neox codebase.
"""

import functools
import logging
from concurrent import futures
from pathlib import Path
from typing import Sequence, Union

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

from base.fastforward import (
    cached_attention,
    cuda_graphs_attention,
    fwd,
    fwd_torch,
    parallel_fwd,
    torch_utils,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.layers import (
    QKV,
    AttnOutMLP,
    CrossLayer,
    InitialEmbeddings,
    _layernorm,
    _linear,
)
from base.fastforward.parallel import ParallelConfig, ParallelContext

WEIGHT_DTYPE = torch.float16


# For StarCoder models, the following sequence tokenizes into:
# """def hello_world():\n\tprint("Hello World!")\n\nif __name__ == "__main__":"""
SC_KNOWN_SEQUENCE = [
    589,
    17964,
    81,
    5860,
    2262,
    203,
    202,
    1216,
    440,
    8279,
    10896,
    15981,
    203,
    203,
    325,
    1156,
    426,
    505,
    610,
    9602,
    1831,
    18018,
]


def number_of_weights(weights: dict[str, torch.Tensor]) -> str:
    num_weights = 0
    for w in weights.values():
        num_weights += np.prod(w.size())  # type: ignore
    return f"{num_weights / 1_000_000_000:.2f}b weights"


def get_state_dict_from_pipeline_files(
    load_dir: Path,
    num_transformer_layers: int,
    output_type: fwd.OutputTensorType,
    target_device: Union[torch.device, str] = "cuda",
):
    """Load the pipeline model states from a deepspeed checkpoint and put them in a simple state_dict."""
    sd = {}

    layer = torch.load(
        load_dir / "layer_00-model_00-model_states.pt", map_location=target_device
    )
    sd["embed.dummy"] = layer["dummy"]
    sd["embed.word_embeddings.weight"] = layer["word_embeddings.weight"]
    sd["position_embeddings.weight"] = layer["position_embeddings.weight"]

    # load layers in parallel to speed up startup time
    def load_layer(i: int):
        result_dict = dict()

        layer_dict = torch.load(
            load_dir / f"layer_{i+2:02d}-model_00-model_states.pt",
            map_location=target_device,
        )

        for k, v in result_dict.items():
            result_dict[k] = v.to(WEIGHT_DTYPE)
            del v

        logging.info("Loading StarCoder layer %s", i)
        for key in [
            "input_layernorm.weight",
            "input_layernorm.bias",
            "attention.query.weight",
            "attention.query.bias",
            "attention.key_value.weight",
            "attention.key_value.bias",
            "attention.dense.weight",
            "attention.dense.bias",
            "post_attention_layernorm.weight",
            "post_attention_layernorm.bias",
            "mlp.dense_h_to_4h.weight",
            "mlp.dense_h_to_4h.bias",
            "mlp.dense_4h_to_h.weight",
            "mlp.dense_4h_to_h.bias",
        ]:
            assert key in layer_dict, (key, layer_dict.keys())
            # the starcoder checkpoint is in fp32. On small machines, we run
            # out of memory, so convert as soon as possible
            result_dict[f"transformer_layers.{i}.{key}"] = layer_dict[key]

        return result_dict

    with futures.ThreadPoolExecutor(max_workers=10) as pool:
        jobs = pool.map(load_layer, range(num_transformer_layers))
        for result in jobs:
            sd.update(result)

    layer = torch.load(
        load_dir / f"layer_{num_transformer_layers + 3:02d}-model_00-model_states.pt",
        map_location=target_device,
    )
    sd["final_norm.weight"] = layer["norm.weight"]
    sd["final_norm.bias"] = layer["norm.bias"]
    layer = torch.load(
        load_dir / f"layer_{num_transformer_layers + 4:02d}-model_00-model_states.pt",
        map_location=target_device,
    )

    if output_type == fwd.OutputTensorType.VOCAB_LOGITS:
        if "final_linear.weight" not in layer:
            raise ValueError(
                "The checkpoint must contain a final linear layer: 'final_linear.weight'. Currently, we don't support StarEthanol checkpoints without this layer."
            )
        sd["final_projection.final_linear.weight"] = layer["final_linear.weight"]
        if "final_linear.bias" in layer:
            sd["final_projection.final_linear.bias"] = layer["final_linear.bias"]
    elif output_type == fwd.OutputTensorType.EMBEDDING:
        if "projection.weight" not in layer:
            raise ValueError(
                "The checkpoint must contain an embedding projection layer: 'projection.weight'. Currently, we don't support StarEthanol checkpoints without this layer."
            )
        sd["embedding_projection.weight"] = layer["projection.weight"]
        if "projection.bias" in layer:
            sd["embedding_projection.bias"] = layer["projection.bias"]

    for k, v in sd.items():
        sd[k] = v.to(WEIGHT_DTYPE)

    logging.info("Done loading model.")
    logging.info("Weights %s", number_of_weights(sd))
    return sd


def _layer_weights(weights: dict, layer_idx: int) -> dict:
    """Extracts the weights for a given transformer layer."""
    d = {}
    for k in weights.keys():
        layer_prefix = f"transformer_layers.{layer_idx}."
        if k.startswith(layer_prefix):
            suffix = k[len(layer_prefix) :]
            d[suffix] = weights[k]
    return d


class StarCoder(nn.Module):
    """StarCoder model."""

    def __init__(
        self,
        sd: dict,
        num_layers: int,
        layernorm_eps: float,
        process_idx: int = 0,
        num_processes: int = 1,
        output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
        auto_capture_graphs: bool = False,
        batch_sizes: Sequence[int] | None = None,
    ):
        super().__init__()
        self._parallel_ctx = ParallelContext(
            ParallelConfig.from_legacy_config(num_processes, False), process_idx
        )
        self._auto_capture_graphs = auto_capture_graphs

        word_embeddings = sd["embed.word_embeddings.weight"]
        position_embeddings = sd["position_embeddings.weight"]
        if "embed.dummy" in sd:
            word_embeddings *= sd["embed.dummy"]
            position_embeddings *= sd["embed.dummy"]
        self.initial_embeddings = InitialEmbeddings.from_weights(
            word_embeddings=word_embeddings,
            position_embeddings=position_embeddings,
        )

        w = _layer_weights(sd, 0)
        self.first_qkv = QKV(
            weight_input_layernorm=w["input_layernorm.weight"],
            weight_attention_query=w["attention.query.weight"],
            weight_attention_key_value=w["attention.key_value.weight"],
            bias_input_layernorm=w["input_layernorm.bias"],
            bias_attention_query=w["attention.query.bias"],
            bias_attention_key_value=w["attention.key_value.bias"],
            layernorm_eps=layernorm_eps,
            process_idx=process_idx,
        )

        w = _layer_weights(sd, num_layers - 1)
        self.final_mlp = AttnOutMLP(
            weight_dense_h_to_4h=w["mlp.dense_h_to_4h.weight"],
            weight_dense_4h_to_h=w["mlp.dense_4h_to_h.weight"],
            bias_dense_h_to_4h=w["mlp.dense_h_to_4h.bias"],
            bias_dense_4h_to_h=w["mlp.dense_4h_to_h.bias"],
            weight_attention_dense=w["attention.dense.weight"],
            weight_post_attention_layernorm=w["post_attention_layernorm.weight"],
            bias_attention_dense=w["attention.dense.bias"],
            bias_post_attention_layernorm=w["post_attention_layernorm.bias"],
            activation_fn=functools.partial(F.gelu, approximate="tanh"),
            layernorm_eps=layernorm_eps,
            process_idx=process_idx,
            num_processes=num_processes,
        )

        self.cross_layers = nn.ModuleList()
        for layer_idx in range(num_layers - 1):
            w = _layer_weights(sd, layer_idx)
            w_next = _layer_weights(sd, layer_idx + 1)
            self.cross_layers.append(
                CrossLayer(
                    weight_dense_h_to_4h=w["mlp.dense_h_to_4h.weight"],
                    weight_dense_4h_to_h=w["mlp.dense_4h_to_h.weight"],
                    bias_dense_h_to_4h=w["mlp.dense_h_to_4h.bias"],
                    bias_dense_4h_to_h=w["mlp.dense_4h_to_h.bias"],
                    weight_attention_dense=w["attention.dense.weight"],
                    weight_post_attention_layernorm=w[
                        "post_attention_layernorm.weight"
                    ],
                    bias_attention_dense=w["attention.dense.bias"],
                    bias_post_attention_layernorm=w["post_attention_layernorm.bias"],
                    activation_fn=functools.partial(F.gelu, approximate="tanh"),
                    #
                    weight_input_layernorm=w_next["input_layernorm.weight"],
                    weight_attention_query=w_next["attention.query.weight"],
                    weight_attention_key_value=w_next["attention.key_value.weight"],
                    bias_input_layernorm=w_next["input_layernorm.bias"],
                    bias_attention_query=w_next["attention.query.bias"],
                    bias_attention_key_value=w_next["attention.key_value.bias"],
                    #
                    layernorm_eps=layernorm_eps,
                    #
                    process_idx=process_idx,
                    num_processes=num_processes,
                )
            )

        self.final_layernorm = _layernorm(
            sd["final_norm.weight"], b=sd["final_norm.bias"], eps=layernorm_eps
        )
        self.final_projection = lambda x: x
        if output_type == fwd.OutputTensorType.VOCAB_LOGITS:
            self.final_projection = _linear(sd["final_projection.final_linear.weight"])
        elif fwd.OutputTensorType.EMBEDDING and "embedding_projection.weight" in sd:
            self.final_projection = _linear(
                sd["embedding_projection.weight"],
                b=sd["embedding_projection.bias"]
                if "embedding_projection.bias" in sd
                else None,
            )
        else:
            raise ValueError(f"Invalid output type {output_type}.")
        self._output_type = output_type

        if self._auto_capture_graphs:
            if batch_sizes is None:
                raise ValueError(
                    "batch_sizes must be specified if auto_capture_graphs is True."
                )
            self._apply_transformer_layers = (
                cuda_graphs_attention.GraphedEmbForwardPass(
                    step_fn=self._emb_step,
                    process_idx=process_idx,
                    num_processes=num_processes,
                    batch_sizes=batch_sizes,
                )
            )
        else:
            self._apply_transformer_layers = self._emb_step

    def _emb_step(
        self, emb: torch.Tensor, attn: cached_attention.Attention
    ) -> torch.Tensor:
        # First layer
        qkv_result = self.first_qkv(emb)
        attn_result = attn(qkv_result, layer_idx=0, parallel_ctx=self._parallel_ctx)
        # Cross layers
        for layer_idx, layer in enumerate(self.cross_layers):
            emb, qkv_result = layer(emb, attn_result)
            attn_result = attn(
                qkv_result, layer_idx=layer_idx + 1, parallel_ctx=self._parallel_ctx
            )
        # Final layer
        emb = self.final_mlp(emb, attn_result)  # type: ignore
        emb = self.final_layernorm(emb)
        return emb

    @torch.inference_mode()
    def forward(
        self,
        tokens: Sequence[int],
        attn: cached_attention.Attention,
    ) -> fwd.ModelOutput:
        # If the input it is not a torch.Tensor, turn it into a torch.Tensor.
        tokens_tensor = torch.tensor(
            tokens, dtype=torch.int32, device=f"cuda:{self._parallel_ctx.process_idx}"
        )

        # Then run the rest as normal. All followed tensors will be on correct devices
        cache_pos = attn.register_tokens_get_positions(
            tokens_tensor, process_idx=self._parallel_ctx.process_idx
        )

        with torch_utils.ProfilingContext("step"):
            emb = self.initial_embeddings.forward(tokens_tensor, cache_pos)
            emb = self._apply_transformer_layers(emb, attn)
            logits_or_embedding = self.final_projection(emb)
        if self._output_type == fwd.OutputTensorType.EMBEDDING:
            return fwd_torch.TorchEmbedding(logits_or_embedding)
        else:
            return fwd_torch.TorchLogits2D(logits_or_embedding)


class StarcoderAttentionFactory(fwd.AttentionFactory):
    """Creates attention caches for starcoder models."""

    def __init__(
        self,
        model_spec: fwd.ModelSpec,
        num_processes: int = 1,
        attention_impl: cached_attention.AttentionImpl = cached_attention.AttentionImpl.BATCHED_FLASH,
        pre_attention_kernel_fusion: bool = True,
        use_register_tokens_kernel: bool = False,
        max_requests_in_round: int | None = None,
        max_large_requests_in_round: int = 1,
        small_request_max_seqlen: int | None = None,
    ):
        self.model_spec = model_spec
        self.parallel_config = ParallelConfig.from_legacy_config(
            num_processes, use_sequence_parallel=False
        )
        self.attention_impl = attention_impl
        self.pre_attention_kernel_fusion = pre_attention_kernel_fusion
        self.use_register_tokens_kernel = use_register_tokens_kernel
        self.max_requests_in_round = max_requests_in_round
        self.max_large_requests_in_round = max_large_requests_in_round
        self.small_request_max_seqlen = small_request_max_seqlen

    def create_cache_pool(
        self, max_length: int, num_attention_caches: int
    ) -> cached_attention.MultiCacheAttention:
        return cached_attention.MultiCacheAttentionImplementation(
            num_caches=num_attention_caches,
            num_layers=self.model_spec.num_layers,
            num_heads=1,
            queries_per_head=self.model_spec.num_heads,
            max_len=max_length,
            head_dim=self.model_spec.head_dim,
            parallel_config=self.parallel_config,
            attention_impl=self.attention_impl,
            max_requests_in_round=self.max_requests_in_round,
            max_large_requests_in_round=self.max_large_requests_in_round,
            small_request_max_seqlen=self.small_request_max_seqlen,
            pre_attention_kernel_fusion=self.pre_attention_kernel_fusion,
            use_register_tokens_kernel=self.use_register_tokens_kernel,
        )


def _load_weights(
    model_spec: fwd.ModelSpec,
    output_type: fwd.OutputTensorType,
    device: torch.device | str = "cuda",
) -> dict[str, torch.Tensor]:
    """Load the model weights from a checkpoint."""
    logging.info("Loading StarCoder model from %s", model_spec.checkpoint_path)

    if save_load.try_get_version(model_spec.checkpoint_path) is None:
        logging.warning(
            "Loading StarCoder model from a legacy checkpoint. "
            "This is deprecated and will be removed in the future."
        )
        return get_state_dict_from_pipeline_files(
            Path(model_spec.checkpoint_path),
            model_spec.num_layers,
            output_type=output_type,
            target_device=device,
        )

    return save_load.load_weights(
        path=model_spec.checkpoint_path,
        device=device,
        target_sha256=model_spec.checkpoint_sha256,
    )


def _generate_step_fn(
    model_spec: fwd.ModelSpec,
    process_idx: int = 0,
    num_processes: int = 1,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    logging.info("Loading model with StarCoder architecture.")
    logging.debug(
        "model_spec: %s, batch_sizes: %s, process_idx: %s, num_processes: %s, auto_capture_graphs: %s",
        model_spec,
        batch_sizes,
        process_idx,
        num_processes,
        auto_capture_graphs,
    )

    device = torch.device(f"cuda:{process_idx}")
    sd = _load_weights(model_spec, output_type=output_type, device=device)

    model = StarCoder(
        sd,
        num_layers=model_spec.num_layers,
        layernorm_eps=model_spec.norm_eps,
        process_idx=process_idx,
        num_processes=num_processes,
        output_type=output_type,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
    )

    return model


def generate_step_fn(
    model_spec: fwd.ModelSpec,
    num_processes: int = 1,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        model_spec: Model spec.
        num_processes: Number of GPUs to use.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
        output_type: return type of the model. See `OutputTensorType`.
    """
    assert num_processes > 0

    logging.info("Using base.fastforward")

    if num_processes == 1:
        return _generate_step_fn(
            model_spec,
            auto_capture_graphs=auto_capture_graphs,
            batch_sizes=batch_sizes,
            output_type=output_type,
        )
    else:
        mp = parallel_fwd.ParallelForwardRunner(num_processes=num_processes)
        return mp.initialize(
            [_generate_step_fn],
            [
                {
                    "model_spec": model_spec,
                    "auto_capture_graphs": auto_capture_graphs,
                    "batch_sizes": batch_sizes,
                    "output_type": output_type,
                }
            ],
        )
