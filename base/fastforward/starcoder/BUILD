load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        ":fwd_starcoder",
        ":fwd_starcoder2",
        ":fwd_starcoder2_fp8",
        ":fwd_starcoder_fp8",
        ":model_specs",
        ":sample_data",
        "//base/fastforward:conftest",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_utils",
        requirement("pytest"),
        requirement("torch"),
    ],
)

py_library(
    name = "sample_data",
    srcs = ["sample_data.py"],
    data = [
        ":fwd_starcoder2_sample_data.json",
    ],
    visibility = ["//visibility:public"],
)

py_library(
    name = "fwd_starcoder",
    srcs = ["fwd_starcoder.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers",
        "//base/fastforward:parallel_fwd",
        "//base/fastforward:torch_utils",
        "//base/fastforward/checkpoints:save_load",
        requirement("torch"),
        requirement("numpy"),
    ],
)

pytest_test(
    name = "fwd_starcoder_test",
    size = "medium",
    srcs = [
        "fwd_starcoder_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        ":fwd_starcoder",
        "//base/fastforward:conftest",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/starcoder:conftest",
        requirement("torch"),
    ],
)

py_library(
    name = "model_specs",
    srcs = ["model_specs.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
    ],
)

pytest_test(
    name = "model_specs_test",
    srcs = ["model_specs_test.py"],
    deps = [
        ":model_specs",
    ],
)

py_library(
    name = "fwd_starcoder2",
    srcs = ["fwd_starcoder2.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":model_specs",
        "//base/fastforward:all_reduce",
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers",
        "//base/fastforward:parallel_fwd",
        "//base/fastforward:torch_utils",
        "//base/fastforward/checkpoints:save_load",
        requirement("torch"),
        requirement("numpy"),
    ],
)

pytest_test(
    name = "fwd_starcoder2_test",
    size = "medium",
    srcs = [
        "fwd_starcoder2_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        ":conftest",
        "//base/fastforward:conftest",
        "//base/fastforward:fwd_model_test_utils",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_starcoder2_multigpu_test",
    size = "medium",
    srcs = [
        "fwd_starcoder2_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":conftest",
        "//base/fastforward:conftest",
        "//base/fastforward:fwd_model_test_utils",
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_starcoder2_fp8",
    srcs = ["fwd_starcoder2_fp8.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":fwd_starcoder2",
        "//base/fastforward:all_reduce",
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs_attention",
        "//base/fastforward:fp8",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers_fp8",
        "//base/fastforward:parallel",
        "//base/fastforward:parallel_fwd",
        "//base/fastforward:torch_utils",
        "//base/fastforward/checkpoints:save_load",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_starcoder2_fp8_test",
    size = "medium",
    srcs = [
        "fwd_starcoder2_fp8_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":conftest",
        "//base/fastforward:fwd_model_test_utils",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_starcoder2_fp8_test_h100_kernels",
    size = "medium",
    srcs = [
        "fwd_starcoder2_fp8_test.py",
    ],
    args = ["--enable-h100-kernels"],
    tags = [
        "gpu",
        "large-gpu",
    ],
    deps = [
        ":conftest",
        "//base/fastforward:fwd_model_test_utils",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_starcoder2_fp8_multigpu_test",
    size = "large",
    srcs = [
        "fwd_starcoder2_fp8_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":conftest",
        "//base/fastforward:all_reduce",
        "//base/fastforward:fwd_model_test_utils",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_starcoder_multigpu_test",
    size = "small",
    srcs = [
        "fwd_starcoder_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":fwd_starcoder",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward:parallel",
        "//base/fastforward/starcoder:conftest",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_starcoder_multigpu_cuda_graph_test",
    size = "small",
    srcs = [
        "fwd_starcoder_multigpu_cuda_graph_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":fwd_starcoder",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_utils",
        "//base/fastforward:parallel",
        "//base/fastforward/starcoder:conftest",
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_starcoder_fp8",
    srcs = ["fwd_starcoder_fp8.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":fwd_starcoder",
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers",
        "//base/fastforward:layers_fp8",
        "//base/fastforward:torch_utils",
        "//base/fastforward/checkpoints:save_load",
        requirement("numpy"),
        requirement("torch"),
        requirement("nvtx"),
    ],
)

pytest_test(
    name = "fwd_starcoder_fp8_test",
    srcs = [
        "fwd_starcoder_fp8_test.py",
    ],
    tags = [
        "exclusive",  # prevents an OOM, because too many concurrent tests are running
        "gpu",
    ],
    deps = [
        ":fwd_starcoder_fp8",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/starcoder:conftest",
        requirement("torch"),
    ],
)
