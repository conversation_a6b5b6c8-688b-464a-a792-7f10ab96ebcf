"""Tests for StarCoder2."""

from base.fastforward import fwd_model_test_utils


def test_generate_bf16(starcoder2_100m_bf16_fixture, starcoder2_test_data):
    step_fn, attn_factory = starcoder2_100m_bf16_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=starcoder2_test_data["inputs"],
        target_sequence=starcoder2_test_data["outputs"],
        allowed_mismatches=1,
    )


def test_generate_graphed(starcoder2_100m_bf16_graphed_fixture, starcoder2_test_data):
    step_fn, attn_factory = starcoder2_100m_bf16_graphed_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=starcoder2_test_data["inputs"],
        target_sequence=starcoder2_test_data["outputs"],
        allowed_mismatches=1,
        extra_kv_len=32,  # to account for cuda graph capturing
    )
