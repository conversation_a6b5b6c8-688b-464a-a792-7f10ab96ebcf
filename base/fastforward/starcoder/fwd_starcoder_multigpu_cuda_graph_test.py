"""Test for the starcoder model."""

import torch

from base.fastforward import fwd_utils
from base.fastforward.starcoder import fwd_starcoder


def test_generate(starcoder_1b_fp16_multigpu_fixture):
    step_fn, attn_factory = starcoder_1b_fp16_multigpu_fixture
    step_fn = fwd_utils.pad_and_step(
        step_fn, [16]
    )  # just to avoid repeated cuda graph creation
    attn = attn_factory(128)

    tokens = [589, 17964, 81, 5860, 2262, 203]

    model_inputs = list(tokens)
    for _ in range(16):  # decode
        logits = step_fn(model_inputs, attn).checked_cast(torch.Tensor)
        next_tokens = torch.argmax(logits, dim=-1)
        next_token = int(next_tokens[-1])
        model_inputs = [next_token]
        tokens.append(int(next_token))

    print(f"{tokens=}")
    # """def hello_world():\n\tprint("Hello World!")\n\nif __name__ == "__main__":"""
    assert tokens == list(fwd_starcoder.SC_KNOWN_SEQUENCE)
