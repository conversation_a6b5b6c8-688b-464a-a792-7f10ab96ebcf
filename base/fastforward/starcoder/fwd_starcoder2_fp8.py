"""StarCoder2 architectures in FP8."""

import logging
from typing import Sequence

import torch
import torch.nn as nn

from base.fastforward import (
    all_reduce,
    cuda_graphs_attention,
    fwd,
    fwd_torch,
    parallel,
    parallel_fwd,
)
from base.fastforward.cached_attention import Attention
from base.fastforward.checkpoints import save_load
from base.fastforward.fp8 import FP8LayerNorm, FP8Linear
from base.fastforward.layers import WordEmbeddings
from base.fastforward.layers_fp8 import StarCoder2TransformerBlock
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.starcoder.fwd_starcoder2 import StarCoder2ModelSpec, shard_weights

Device = torch.device | str


class StarCoder2(nn.Module):
    """Entire StarCoder2 architecture in FP8."""

    def __init__(
        self,
        ms: StarCoder2ModelSpec,
        dtype: torch.dtype = torch.bfloat16,
        device: Device = "cuda",
        process_idx: int = 0,
        parallel_config: ParallelConfig = ParallelConfig.single_process(),
        auto_capture_graphs: bool = False,
        batch_sizes: Sequence[int] | None = None,
        all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
    ):
        super().__init__()
        self._device = device
        self._process_idx = process_idx
        self._parallel_ctx = ParallelContext(parallel_config, process_idx)
        self._dtype = dtype
        self._auto_capture_graphs = auto_capture_graphs

        # Default values for custom communicators are to return `None`.
        # - There are 2*num_layers all_reduce calls (one for attn, one for mlp).
        # - There are num_layers all_gather calls (one per attn).
        all_reduce_kits = all_reduce.DoubleBufferedCommunicator(
            ms.num_layers * 2,
            lambda: None,
        )
        all_gather_kits = all_reduce.DoubleBufferedCommunicator(
            ms.num_layers,
            lambda: None,
        )

        if all_reduce_impl.is_fastforward():
            # max tokens per round
            max_tokens = max(batch_sizes) if batch_sizes is not None else 4096

            if self._parallel_ctx.tp_size > 1:
                all_reduce_kits = all_reduce.DoubleBufferedCommunicator(
                    ms.num_layers * 2,
                    all_reduce.AllReduceKit,
                    ms.emb_dim * max_tokens,
                    dtype,
                    process_group=self._parallel_ctx.tp_group,
                    input_is_fp8=all_reduce_impl
                    == all_reduce.AllReduceImpl.FASTFORWARD_FP8,
                )
            if self._parallel_ctx.sp_size > 1:
                all_gather_kits = all_reduce.DoubleBufferedCommunicator(
                    ms.num_layers,
                    all_reduce.AllGatherKit,
                    input_max_numel=max_tokens * ms.num_heads_kv * ms.head_dim,
                    dtype=dtype,
                    process_group=self._parallel_ctx.sp_group,
                )

        self.embs = WordEmbeddings(
            vocab_size=ms.vocab_size,
            emb_dim=ms.emb_dim,
            dtype=dtype,
            device=device,
        )
        logging.info("create emb.")

        self.layers = nn.ModuleList()
        for layer_idx in range(ms.num_layers):
            self.layers.append(
                StarCoder2TransformerBlock(
                    emb_dim=ms.emb_dim,
                    num_heads_q=ms.num_heads_q,
                    num_heads_kv=ms.num_heads_kv,
                    head_dim=ms.head_dim,
                    split_head_mode=ms.attn_split_head_mode,
                    mlp_dim=ms.mlp_hidden_dim,
                    norm_eps=ms.norm_eps,
                    use_bias=True,
                    dtype=dtype,
                    device=device,
                    parallel_ctx=self._parallel_ctx,
                    ffn_all_reduce_kit=all_reduce_kits.get(2 * layer_idx),
                    attn_all_reduce_kit=all_reduce_kits.get(2 * layer_idx + 1),
                    attn_all_gather_kit=all_gather_kits.get(layer_idx),
                )
            )
        all_reduce_kits.verify_num_calls()
        all_gather_kits.verify_num_calls()

        logging.info("create finalnorm.")
        self.final_norm = FP8LayerNorm(
            normalized_shape=ms.emb_dim,
            eps=ms.norm_eps,
            dtype=dtype,
            device=device,
        )

        logging.info("create score_proj.")
        self.score = FP8Linear(
            in_features=ms.emb_dim,
            out_features=ms.vocab_size,
            bias=False,
            device=device,
            dtype=dtype,
        )
        if self._auto_capture_graphs:
            if batch_sizes is None:
                raise ValueError(
                    "batch_sizes must be specified if auto_capture_graphs is True."
                )
            self._apply_transformer_layers = (
                cuda_graphs_attention.GraphedEmbForwardPass(
                    step_fn=self._emb_step,
                    process_idx=self._parallel_ctx.process_idx,
                    num_processes=self._parallel_ctx.num_processes,
                    batch_sizes=batch_sizes,
                )
            )
        else:
            self._apply_transformer_layers = self._emb_step

    def _emb_step(self, tokens: torch.Tensor, attn: Attention) -> torch.Tensor:
        emb = self.embs(tokens).to(self._dtype)
        if self._parallel_ctx.sp_size > 1:
            emb = parallel.split_tensor_for_sequence_parallelism(
                emb,
                parallel_ctx=self._parallel_ctx,
            )
        for layer_idx, layer in enumerate(self.layers):
            emb = layer(emb, attn=attn, layer_idx=layer_idx)
        if self._parallel_ctx.sp_size > 1:
            emb = parallel.gather_sequence_parallel(emb, self._parallel_ctx)
        return emb

    @torch.inference_mode()
    def forward(self, tokens: Sequence[int], attn: Attention) -> fwd.ModelOutput:
        tokens_tensor = torch.tensor(tokens, dtype=torch.int32, device=self._device)
        attn.register_tokens_get_positions(
            tokens_tensor, process_idx=self._parallel_ctx.process_idx
        )
        x = self._apply_transformer_layers(tokens_tensor, attn)
        x = self.final_norm(x, output_fp8_meta=self.score.input_fp8_meta)
        x = self.score(x)
        return fwd_torch.TorchLogits2D(x)


def _loaded_weights_are_valid(
    loaded_weights: set[str],
    model_weights: set[str],
) -> bool:
    # Exact match is always valid
    if loaded_weights == model_weights:
        return True
    # Unassigned keys are never valid (this should be caught beforehand)
    if len(loaded_weights - model_weights) > 0:
        return False
    # Compute the missing keys and check if they are a valid case
    missing_keys = model_weights - loaded_weights
    attn_scale_keys = set(k for k in model_weights if k.endswith(".attn_qkv_scales"))
    output_scale_keys = set(k for k in model_weights if k.endswith(".output_scale"))
    # Valid cases are either or both of attn_ and output_scale_keys missing:
    if (
        missing_keys == attn_scale_keys | output_scale_keys
        or missing_keys == attn_scale_keys
        or missing_keys == output_scale_keys
    ):
        return True
    return False


def _generate_step_fn(
    ms: StarCoder2ModelSpec,
    parallel_config: ParallelConfig,
    dtype: torch.dtype = torch.bfloat16,
    batch_sizes: Sequence[int] | None = None,
    process_idx: int = 0,
    num_processes: int = 1,
    num_layers_per_load: int = 20,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
) -> fwd.ForwardStepFn:
    logging.info(
        "Loading a StarCoder2 model %s onto %d processes.", ms.name, num_processes
    )
    logging.debug(
        "ms: %s, batch_sizes: %s, process_idx: %s, num_processes: %s, parallel_config: %s",
        ms,
        batch_sizes,
        process_idx,
        num_processes,
        parallel_config,
    )
    if parallel_config.num_processes != num_processes:
        raise ValueError(
            f"parallel_config.num_processes={parallel_config.num_processes} does not match num_processes={num_processes}"
        )

    device = f"cuda:{process_idx}"
    model = StarCoder2(
        ms=ms,
        dtype=dtype,
        device=device,
        process_idx=process_idx,
        parallel_config=parallel_config,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
        all_reduce_impl=all_reduce_impl,
    )

    if load_checkpoint_weights:
        torch.cuda.empty_cache()
        loaded_weights = set()
        weights = save_load.load_weights(
            path=ms.checkpoint_path,
            require_patterns=(r"^embs\.", r"^final_norm\.", r"^score\."),
            target_sha256=ms.checkpoint_sha256,
        )
        weights = shard_weights(
            weights=weights,
            ms=ms,
            device=device,
            process_idx=process_idx,
            parallel_config=parallel_config,
        )
        loaded_weights.update(weights.keys())
        ret = model.load_state_dict(weights, strict=False)
        if ret.unexpected_keys:
            raise RuntimeError(f"Unexpected keys: {ret.unexpected_keys}.")
        del weights

        for block_idx in range(
            (ms.num_layers + num_layers_per_load - 1) // num_layers_per_load
        ):
            torch.cuda.empty_cache()
            layer_idxs = [
                rf"^layers\.{num_layers_per_load * block_idx + i}\."
                for i in range(num_layers_per_load)
                if num_layers_per_load * block_idx + i < ms.num_layers
            ]
            weights = save_load.load_weights(
                path=ms.checkpoint_path,
                require_patterns=tuple(layer_idxs),
                target_sha256=ms.checkpoint_sha256,
            )
            weights = shard_weights(
                weights=weights,
                ms=ms,
                device=device,
                process_idx=process_idx,
                parallel_config=parallel_config,
            )
            loaded_weights.update(weights.keys())
            ret = model.load_state_dict(weights, strict=False)
            if ret.unexpected_keys:
                raise RuntimeError(f"Unexpected keys: {ret.unexpected_keys}.")
            del weights

        # Sanity check to ensure that we loaded the correct model weights.
        model_weights = set(model.state_dict().keys())
        if not _loaded_weights_are_valid(loaded_weights, model_weights):
            raise RuntimeError(
                f"Invalid set of loaded weights.\n{loaded_weights=}.\n{model_weights=}"
            )

    return model


def generate_step_fn(
    ms: StarCoder2ModelSpec,
    parallel_config: ParallelConfig = ParallelConfig.single_process(),
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
    small_round_parallel_config: ParallelConfig | None = None,
    small_round_token_cutoff: int = 0,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        ms: Model spec.
        num_processes: Number of GPUs to use.
        load_checkpoint_weights: load the weights into the step_fn. should be `True`
            by default. only set to `False` for testing, and if so, make sure you
            know what you are doing.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
        sequence_parallel: Whether to use sequence parallel. If False, all parallelism
            will be used for model parallelism.
    """
    logging.info("Using base.fastforward")

    if parallel_config.num_processes == 1:
        return _generate_step_fn(
            ms=ms,
            parallel_config=parallel_config,
            batch_sizes=batch_sizes,
            load_checkpoint_weights=load_checkpoint_weights,
            auto_capture_graphs=auto_capture_graphs,
            all_reduce_impl=all_reduce_impl,
        )
    elif small_round_parallel_config is not None and small_round_token_cutoff > 0:
        assert (
            small_round_parallel_config.num_processes == parallel_config.num_processes
        )
        mp = parallel_fwd.ParallelForwardRunner(
            num_processes=parallel_config.num_processes,
        )
        return mp.initialize(
            [_generate_step_fn, _generate_step_fn],
            [
                {
                    "ms": ms,
                    "parallel_config": small_round_parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                },
                {
                    "ms": ms,
                    "parallel_config": parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                },
            ],
            lambda tokens, attn: 0 if len(tokens) <= small_round_token_cutoff else 1,
        )
    else:
        mp = parallel_fwd.ParallelForwardRunner(
            num_processes=parallel_config.num_processes,
        )
        return mp.initialize(
            [_generate_step_fn],
            [
                {
                    "ms": ms,
                    "parallel_config": parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                },
            ],
        )
