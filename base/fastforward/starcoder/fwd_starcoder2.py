"""An implementation of the StarCoder2 forward pass."""

import logging
from typing import Optional, Sequence, Union

import torch
import torch.nn as nn

from base.fastforward import (
    all_reduce,
    cached_attention,
    cuda_graphs_attention,
    fwd_torch,
    positional_embeddings,
)
from base.fastforward.cached_attention import (
    Attention,
    AttentionImpl,
    MultiCacheAttention,
    MultiCacheAttentionImplementation,
    SplitHeadModes,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.fwd import AttentionFactory, ForwardStepFn, ModelOutput
from base.fastforward.layers import Device, StarCoder2TransformerBlock, WordEmbeddings
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.parallel_fwd import ParallelForwardRunner
from base.fastforward.starcoder import model_specs

StarCoder2ModelSpec = model_specs.StarCoder2ModelSpec


class StarCoder2(nn.Module):
    """StarCoder2 model."""

    def __init__(
        self,
        ms: StarCoder2ModelSpec,
        dtype: torch.dtype = torch.bfloat16,
        device: Device = "cuda",
        process_idx: int = 0,
        parallel_config: ParallelConfig = ParallelConfig.single_process(),
        auto_capture_graphs: bool = False,
        batch_sizes: Sequence[int] | None = None,
        all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
    ):
        super().__init__()
        self._dtype = dtype
        self._device = device
        if parallel_config.sp_size > 1:
            raise ValueError("Sequence parallel unsupported for 16-bit StarCoder2.")
        self._parallel_ctx = ParallelContext(parallel_config, process_idx)
        self._auto_capture_graphs = auto_capture_graphs

        self.embs = WordEmbeddings(
            vocab_size=ms.vocab_size,
            emb_dim=ms.emb_dim,
            dtype=dtype,
            device=device,
        )

        ffn_all_reduce_kit: all_reduce.AllReduceKit | None = None
        attn_all_reduce_kit: all_reduce.AllReduceKit | None = None

        if parallel_config.tp_size > 1:
            if all_reduce_impl == all_reduce.AllReduceImpl.FASTFORWARD:
                # max tokens per round
                max_tokens = max(batch_sizes) if batch_sizes is not None else 4096

                ffn_all_reduce_kit = all_reduce.AllReduceKit(
                    ms.emb_dim * max_tokens, dtype, self._parallel_ctx.tp_group
                )
                attn_all_reduce_kit = all_reduce.AllReduceKit(
                    ms.emb_dim * max_tokens * 2,
                    dtype,
                    self._parallel_ctx.tp_group,
                )
            elif all_reduce_impl == all_reduce.AllReduceImpl.FASTFORWARD_FP8:
                raise ValueError("FP8 all_reduce requires an fp8 model.")

        self.layers = nn.ModuleList()
        for _ in range(ms.num_layers):
            self.layers.append(
                StarCoder2TransformerBlock(
                    emb_dim=ms.emb_dim,
                    num_heads_q=ms.num_heads_q,
                    num_heads_kv=ms.num_heads_kv,
                    head_dim=ms.head_dim,
                    split_head_mode=ms.attn_split_head_mode,
                    mlp_dim=ms.mlp_hidden_dim,
                    norm_eps=ms.norm_eps,
                    use_bias=True,
                    dtype=dtype,
                    device=device,
                    parallel_ctx=self._parallel_ctx,
                    attn_all_reduce_kit=attn_all_reduce_kit,
                    ffn_all_reduce_kit=ffn_all_reduce_kit,
                )
            )

        self.final_norm = nn.LayerNorm(
            normalized_shape=ms.emb_dim,
            eps=ms.norm_eps,
            dtype=dtype,
            device=device,
        )

        self.score = nn.Linear(
            in_features=ms.emb_dim,
            out_features=ms.vocab_size,
            bias=False,
            device=device,
            dtype=dtype,
        )

        if self._auto_capture_graphs:
            if batch_sizes is None:
                raise ValueError(
                    "batch_sizes must be specified if auto_capture_graphs is True."
                )
            self._apply_transformer_layers = (
                cuda_graphs_attention.GraphedEmbForwardPass(
                    step_fn=self._emb_step,
                    process_idx=process_idx,
                    num_processes=parallel_config.num_processes,
                    batch_sizes=batch_sizes,
                )
            )
        else:
            self._apply_transformer_layers = self._emb_step

    def _emb_step(self, emb: torch.Tensor, attn: Attention) -> torch.Tensor:
        for layer_idx, layer in enumerate(self.layers):
            emb = layer(emb, attn=attn, layer_idx=layer_idx)
        return emb

    @torch.inference_mode()
    def forward(self, tokens: Sequence[int], attn: Attention) -> ModelOutput:
        tokens_tensor = torch.tensor(tokens, dtype=torch.int32, device=self._device)
        attn.register_tokens_get_positions(
            tokens_tensor, process_idx=self._parallel_ctx.process_idx
        )
        x = self.embs(tokens_tensor)
        x = self._apply_transformer_layers(x, attn)
        x = self.final_norm(x)
        x = self.score(x)
        return fwd_torch.TorchLogits2D(x)


class StarCoder2AttentionFactory(AttentionFactory):
    """Creates attention caches for StarCoder2 models."""

    def __init__(
        self,
        ms: StarCoder2ModelSpec,
        parallel_config: ParallelConfig = ParallelConfig.single_process(),
        dtype: torch.dtype = torch.bfloat16,
        attention_impl: AttentionImpl = AttentionImpl.BATCHED_FLASH,
        pre_attention_kernel_fusion: bool = True,
        capture_attn_maxes_for_quantization: bool = False,
        use_register_tokens_kernel: bool = False,
        small_request_max_seqlen: int | None = None,
        max_requests_in_round: int | None = None,
        max_large_requests_in_round: int = 1,
    ):
        """The AttentionFactory for StarCoder2 models.

        Args:
            ms: the specific StarCoder2ModelSpec.
            parallel_config: configure the parallelism of the attention cache.
            dtype: the dtype of the `qkv` tensor that will be passed to Attention
                objects created by this factory. we also use this `dtype` to construct
                this object's KV-caches.
            attention_impl: the implementation of the attention.
            pre_attention_kernel_fusion: whether to fuse the pre-attention kernel.
            capture_attn_maxes_for_quantization: whether to capture the maxes of
                the attention logits for quantization.
            use_register_tokens_kernel: whether to use the register tokens kernel.
            small_request_max_seqlen: maximum sequence length for small requests.
            max_requests_in_round: minimum round size for multirequest attention.
        """
        self._ms = ms
        self._parallel_config = parallel_config
        # Dynamically adjust the split head mode to "downgrade" KV_HEADS models running in sequence
        # parallel mode to NO_SPLIT. (Sequence parallelism with Q_PER_HEADS is supported but the
        # support is experimental, so for now we don't change model behavior there. In the medium
        # term we want to get rid of dynamically changing this at all and just set it correctly in
        # the model spec at deploy time. Then this code can just assert compatibility.)
        self._split_head_mode = (
            cached_attention.SplitHeadModes.NO_SPLIT
            if parallel_config.sp_size > 1
            # TODO(carl): re-enable support for Q-split in sequence parallel mode (for dynamic SP)
            # and ms.attn_split_head_mode == cached_attention.SplitHeadModes.KV_HEADS
            else ms.attn_split_head_mode
        )
        self._dtype = dtype
        self._attention_impl = attention_impl
        self._pre_attention_kernel_fusion = pre_attention_kernel_fusion
        self._capture_attn_maxes_for_quantization = capture_attn_maxes_for_quantization
        self._use_register_tokens_kernel = use_register_tokens_kernel
        self._small_request_max_seqlen = small_request_max_seqlen
        self._max_requests_in_round = max_requests_in_round
        self._max_large_requests_in_round = max_large_requests_in_round

    def __call__(self, max_length: int) -> cached_attention.BasicAttention:
        """Overriding the legacy interface, to pass in the number of processes."""
        mc_attn = self.create_cache_pool(max_length=max_length, num_attention_caches=1)
        return cached_attention.BasicAttention(
            mc_attn, num_processes=self._parallel_config.num_processes
        )

    def create_cache_pool(
        self, max_length: int, num_attention_caches: int
    ) -> MultiCacheAttention:
        assert self._ms.rotary_extension_method in [
            "deepseek_v1",
            "no_extension",
        ]  # deepseek_v1 is the legacy default
        assert self._ms.rotary_scaling_factor == 1.0, self._ms.rotary_scaling_factor
        rotary_config = positional_embeddings.RotaryConfig(
            rotary_ratio=self._ms.rotary_pct,
            rotary_theta=self._ms.rotary_theta,
            max_position_embeddings=self._ms.max_position_embeddings,
            rotary_interleave=self._ms.rotary_interleave,
        )
        mc_attn = MultiCacheAttentionImplementation(
            num_caches=num_attention_caches,
            num_layers=self._ms.num_layers,
            num_heads=self._ms.num_heads_kv,
            queries_per_head=self._ms.num_heads_q // self._ms.num_heads_kv,
            max_len=max_length,
            dtype=self._dtype,
            head_dim=self._ms.head_dim,
            parallel_config=self._parallel_config,
            split_head_mode=self._split_head_mode,
            attention_impl=self._attention_impl,
            pre_attention_kernel_fusion=self._pre_attention_kernel_fusion,
            rotary_config=rotary_config,
            capture_attn_maxes_for_quantization=self._capture_attn_maxes_for_quantization,
            use_register_tokens_kernel=self._use_register_tokens_kernel,
            small_request_max_seqlen=self._small_request_max_seqlen,
            max_requests_in_round=self._max_requests_in_round,
            max_large_requests_in_round=self._max_large_requests_in_round,
        )
        return mc_attn


########################################################
# Weight sharding functions while loading checkpoints. #
########################################################


def _shard_attn_qkv_weight(
    name: str,
    w: torch.Tensor,
    ms: StarCoder2ModelSpec,
    tp_rank: int,
    tp_size: int,
) -> torch.Tensor:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    if not name.endswith("qkv.weight"):
        return w

    if ms.attn_split_head_mode == SplitHeadModes.NO_SPLIT:
        return w

    num_heads_q = ms.num_heads_q
    num_heads_kv = ms.num_heads_kv
    head_dim = ms.head_dim

    emb_dim = w.numel() // ((num_heads_q + 2 * num_heads_kv) * head_dim)
    num_queries_per_head = num_heads_q // num_heads_kv

    w = w.view(num_heads_kv, (num_queries_per_head + 2) * head_dim, emb_dim)

    if ms.attn_split_head_mode == SplitHeadModes.KV_HEADS:
        # Typically used in MHA or GQA
        # Each device holds (num_heads_kv // tp_size) KV-heads,
        # along with their corresponding Q-heads.
        w = w.chunk(tp_size, dim=0)[tp_rank].reshape(-1, emb_dim)
    elif ms.attn_split_head_mode == SplitHeadModes.Q_PER_HEADS:
        # Typically used in MQA or GQA with few KV-heads
        # Each device holds all num_heads_kv KV-heads, along with their
        # corresponding block of Q-heads.
        wq, w_kv = w.split([num_queries_per_head * head_dim, 2 * head_dim], dim=1)
        wq = wq.chunk(tp_size, dim=1)[tp_rank]
        w = torch.cat(
            [wq, w_kv],
            dim=1,
        ).reshape(-1, emb_dim)

    return w


def _shard_attn_qkv_bias(
    name: str,
    w: torch.Tensor,
    ms: StarCoder2ModelSpec,
    tp_rank: int,
    tp_size: int,
) -> torch.Tensor:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    if not name.endswith("qkv.bias"):
        return w

    if ms.attn_split_head_mode == SplitHeadModes.NO_SPLIT:
        return w

    num_heads_q = ms.num_heads_q
    num_heads_kv = ms.num_heads_kv
    head_dim = ms.head_dim

    num_queries_per_head = num_heads_q // num_heads_kv

    w = w.view(num_heads_kv, (num_queries_per_head + 2) * head_dim)

    if ms.attn_split_head_mode == SplitHeadModes.KV_HEADS:
        # Typically used in MHA or GQA
        # Each device holds (num_heads_kv // tp_size) KV-heads,
        # along with their corresponding Q-heads.
        w = w.chunk(tp_size, dim=0)[tp_rank].reshape(-1)
    elif ms.attn_split_head_mode == SplitHeadModes.Q_PER_HEADS:
        # Typically used in MQA or GQA with few KV-heads
        # Each device holds all num_heads_kv KV-heads, along with their
        # corresponding block of Q-heads.
        wq, w_kv = w.split([num_queries_per_head * head_dim, 2 * head_dim], dim=1)
        wq = wq.chunk(tp_size, dim=1)[tp_rank]
        w = torch.cat([wq, w_kv], dim=1).reshape(-1)

    return w


def _shard_attn_out_weight(
    name: str,
    w: torch.Tensor,
    ms: StarCoder2ModelSpec,
    tp_rank: int,
    tp_size: int,
) -> torch.Tensor:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    if not name.endswith("out.weight"):
        return w

    if ms.attn_split_head_mode == SplitHeadModes.NO_SPLIT:
        return w
    elif ms.attn_split_head_mode == SplitHeadModes.KV_HEADS:
        return w.chunk(tp_size, dim=1)[tp_rank]
    else:
        assert ms.attn_split_head_mode == SplitHeadModes.Q_PER_HEADS
        assert ms.num_heads_q % ms.num_heads_kv == 0
        queries_per_kvhead = ms.num_heads_q // ms.num_heads_kv
        assert queries_per_kvhead % tp_size == 0
        w_kv_groups = w.chunk(ms.num_heads_kv, dim=1)
        return torch.cat(
            tuple(group.chunk(tp_size, dim=1)[tp_rank] for group in w_kv_groups), dim=1
        ).contiguous()


def _shard_ffn_expand_weight(
    name: str, w: torch.Tensor, tp_rank: int = 0, tp_size: int = 1
) -> torch.Tensor:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    if name.endswith("expand.weight"):
        return w.chunk(tp_size, dim=0)[tp_rank].contiguous()

    return w


def _shard_ffn_expand_bias(
    name: str, w: torch.Tensor, tp_rank: int = 0, tp_size: int = 1
) -> torch.Tensor:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    if name.endswith("expand.bias"):
        return w.chunk(tp_size, dim=0)[tp_rank].contiguous()

    return w


def _shard_ffn_shrink_weight(
    name: str, w: torch.Tensor, tp_rank: int = 0, tp_size: int = 1
) -> torch.Tensor:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    if name.endswith("shrink.weight"):
        return w.chunk(tp_size, dim=1)[tp_rank]

    return w


@torch.no_grad
def _shard(
    name: str,
    w: torch.Tensor,
    device: Union[torch.device, str] = "cpu",
    dtype: Optional[torch.dtype] = None,
    ms: Optional[StarCoder2ModelSpec] = None,
    process_idx: int = 0,
    parallel_config: ParallelConfig = ParallelConfig.single_process(),
) -> torch.Tensor:
    """How to "sneak-in" what we want to do with a weight tensor while loading.

    Args:
        name: the weight's name in the model. we can rely on the name to specify what
            we want to do with different tensors.
        w_as_bytes: the serialized tensor bytes read by `safetensor` from a file.
        device: where the tensor will be moved to immediately after loading.
        dtype: the tensor will be converted to this dtype after loading. if `None`,
            the type is kept as it is loaded from files.
        ms: the model spec, which is used as the config to shard certain weights, such
            as the attention weights. if the attention layer is not sharded, one does
            not need to pass `ms` to this function call.
        process_idx: if using multiple GPUs, specify which process is this method in.
        num_processes: if using multiple GPUs, specify total number of processes.
        sequence_parallel: whether to use sequence parallelism.
    """
    if parallel_config.tp_size == 1:
        return w

    tp_rank = parallel_config.process_idx_to_tp_rank(process_idx)
    tp_size = parallel_config.tp_size
    w = _shard_ffn_expand_weight(name=name, w=w, tp_rank=tp_rank, tp_size=tp_size)

    w = _shard_ffn_expand_bias(name=name, w=w, tp_rank=tp_rank, tp_size=tp_size)

    w = _shard_ffn_shrink_weight(name=name, w=w, tp_rank=tp_rank, tp_size=tp_size)

    if ms is not None:
        w = _shard_attn_qkv_weight(
            name=name, w=w, ms=ms, tp_rank=tp_rank, tp_size=tp_size
        )

        w = _shard_attn_qkv_bias(
            name=name, w=w, ms=ms, tp_rank=tp_rank, tp_size=tp_size
        )

        w = _shard_attn_out_weight(
            name=name, w=w, ms=ms, tp_rank=tp_rank, tp_size=tp_size
        )

    return w.detach().to(dtype=dtype, device=device)


def shard_weights(
    weights: dict[str, torch.Tensor],
    device: Union[torch.device, str] = "cpu",
    dtype: Optional[torch.dtype] = None,
    ms: Optional[StarCoder2ModelSpec] = None,
    process_idx: int = 0,
    parallel_config: ParallelConfig = ParallelConfig.single_process(),
) -> dict[str, torch.Tensor]:
    """Sharding weights for a model."""
    weights = {
        name: _shard(name, w, device, dtype, ms, process_idx, parallel_config)
        for name, w in weights.items()
    }
    if parallel_config.process_idx_to_tp_rank(process_idx) > 0:
        # Only TP rank 0 needs to keep the bias weights.
        ignored_bias_suffixes = [".ffn.shrink.bias"]
        # Special case: the output bias is needed by all TP ranks in the NO_SPLIT case, so we
        # filter only in the other cases. See NOTE(add-output-bias) in layers.py.
        if ms is None or ms.attn_split_head_mode != SplitHeadModes.NO_SPLIT:
            ignored_bias_suffixes.append(".attn.out.bias")
        weights = {
            name: w
            for name, w in weights.items()
            if not any(name.endswith(s) for s in ignored_bias_suffixes)
        }
    return weights


def _generate_step_fn(
    ms: StarCoder2ModelSpec,
    parallel_config: ParallelConfig,
    dtype: torch.dtype = torch.bfloat16,
    batch_sizes: Sequence[int] | None = None,
    process_idx: int = 0,
    num_processes: int = 1,
    num_layers_per_load: int = 20,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
) -> ForwardStepFn:
    logging.info(
        "Loading a StarCoder2 model %s onto %d processes.", ms.name, num_processes
    )
    logging.debug(
        "ms: %s, batch_sizes: %s, process_idx: %s, num_processes: %s",
        ms,
        batch_sizes,
        process_idx,
        num_processes,
    )
    if parallel_config.num_processes != num_processes:
        raise ValueError(
            f"parallel_config.num_processes={parallel_config.num_processes} does not match num_processes={num_processes}"
        )

    # TODO: load only the weights that we need for this process.
    device = f"cuda:{process_idx}"
    model = StarCoder2(
        ms=ms,
        dtype=dtype,
        device=device,
        process_idx=process_idx,
        parallel_config=parallel_config,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
        all_reduce_impl=all_reduce_impl,
    )

    if load_checkpoint_weights:
        torch.cuda.empty_cache()
        num_loaded_weights = 0
        weights = save_load.load_weights(
            path=ms.checkpoint_path,
            require_patterns=(r"^embs\.", r"^final_norm\.", r"^score\."),
            target_sha256=ms.checkpoint_sha256,
        )
        weights = shard_weights(
            weights=weights,
            ms=ms,
            device=device,
            process_idx=process_idx,
            parallel_config=parallel_config,
        )
        ret = model.load_state_dict(weights, strict=False)
        if ret.unexpected_keys:
            raise RuntimeError(f"Unexpected keys: {ret.unexpected_keys}.")
        num_loaded_weights += len(weights)
        del weights

        for block_idx in range(
            (ms.num_layers + num_layers_per_load - 1) // num_layers_per_load
        ):
            torch.cuda.empty_cache()
            layer_idxs = [
                rf"^layers\.{num_layers_per_load * block_idx + i}\."
                for i in range(num_layers_per_load)
                if num_layers_per_load * block_idx + i < ms.num_layers
            ]
            weights = save_load.load_weights(
                path=ms.checkpoint_path,
                require_patterns=tuple(layer_idxs),
                target_sha256=ms.checkpoint_sha256,
            )
            weights = shard_weights(
                weights=weights,
                ms=ms,
                device=device,
                process_idx=process_idx,
                parallel_config=parallel_config,
            )
            ret = model.load_state_dict(weights, strict=False)
            if ret.unexpected_keys:
                raise RuntimeError(f"Unexpected keys: {ret.unexpected_keys}.")
            num_loaded_weights += len(weights)
            del weights

        # Sanity check to ensure that we loaded the correct model weights.
        # The checking logics ensure two things:
        #   1. We never missed any weights.
        #   2. In total, the number of weight tensors that we loaded equals the
        #      number of weight tensors in the model.

        num_model_weights = len(list(model.parameters()))
        if num_model_weights != num_loaded_weights:
            raise RuntimeError(
                f"Loaded {num_loaded_weights} weights, but the model has {num_model_weights} weights."
            )

    return model


def generate_step_fn(
    ms: StarCoder2ModelSpec,
    num_processes: int = 1,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
) -> ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        ms: Model spec.
        num_processes: Number of GPUs to use.
        load_checkpoint_weights: load the weights into the step_fn. should be `True`
            by default. only set to `False` for testing, and if so, make sure you
            know what you are doing.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
    """
    logging.info("Using base.fastforward")

    parallel_config = ParallelConfig.from_legacy_config(num_processes, False)
    if parallel_config.num_processes == 1:
        return _generate_step_fn(
            ms=ms,
            parallel_config=parallel_config,
            batch_sizes=batch_sizes,
            load_checkpoint_weights=load_checkpoint_weights,
            auto_capture_graphs=auto_capture_graphs,
        )
    else:
        mp = ParallelForwardRunner(num_processes=parallel_config.num_processes)
        return mp.initialize(
            [_generate_step_fn],
            [
                {
                    "ms": ms,
                    "parallel_config": parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                }
            ],
        )
