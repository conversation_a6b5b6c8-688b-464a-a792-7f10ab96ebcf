"""Test for the starcoder model."""

from base.fastforward import fwd_model_test_utils
from base.fastforward.starcoder import fwd_starcoder


def test_generate(starcoder_1b_fp16_multigpu_fixture):
    step_fn, attn_factory = starcoder_1b_fp16_multigpu_fixture

    try:
        fwd_model_test_utils.check_if_model_generates_target_sequence(
            step_fn,
            attn_factory,
            given_prefix=fwd_starcoder.SC_KNOWN_SEQUENCE[:6],
            target_sequence=fwd_starcoder.SC_KNOWN_SEQUENCE[6:],
        )
    finally:
        del step_fn
