from base.fastforward.starcoder.model_specs import starcoder2_model_spec_from_checkpoint


def test_starcoder2_spec_loading(tmp_path):
    """Tests that we can load a StarCoder2 model spec from params.json.

    This check is for testing that any changes we made to the loading logic should
    be backward compatible.
    """

    example_json = """\
{
  "attn_split_head_mode": 1,
  "beta_fast": 32,
  "beta_slow": 1,
  "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-gen/S1.13.1-R1.2_no_retrieval_synth_instruct-P1.10.1_context12-gh_pr_train_repartitioned-starcoder2_15b",
  "checkpoint_sha256": null,
  "emb_dim": 6144,
  "head_dim": 128,
  "max_position_embeddings": 7936,
  "mlp_hidden_dim": 24576,
  "name": "starcoder2-15b",
  "norm_eps": 1e-05,
  "num_heads": 48,
  "num_heads_kv": 4,
  "num_heads_q": 48,
  "num_layers": 40,
  "rotary_cast_sincos_as_input": false,
  "rotary_interleave": true,
  "rotary_pct": 1.0,
  "rotary_scaling_factor": 1.0,
  "rotary_theta": 100000.0,
  "unscaled_max_position_embeddings": 7936,
  "uses_yarn": false,
  "vocab_size": 51200
}
"""
    example_json_path = tmp_path / "params.json"
    example_json_path.write_text(example_json)
    spec = starcoder2_model_spec_from_checkpoint(example_json_path.parent)
    assert spec.name == "starcoder2-15b"
    assert spec.vocab_size == 51200
