"""conftest.py is a special file that pytest will automatically load."""

from typing import Generator, Sequence

import pytest
import torch

from base.fastforward import fwd, fwd_utils
from base.fastforward.all_reduce import ALL_ALL_REDUCE_IMPLS, AllReduceImpl
from base.fastforward.cached_attention import (
    ALL_ATTN_IMPLS_NO_FA3,
    ALL_ATTN_IMPLS_WITH_FP8,
    AttentionImpl,
)
from base.fastforward.parallel import ParallelConfig
from base.fastforward.starcoder import (
    fwd_starcoder,
    fwd_starcoder2,
    fwd_starcoder2_fp8,
    fwd_starcoder_fp8,
    model_specs,
    sample_data,
)

# NOTE: the `starcoder_1b_fp16_fixture` and `starcoder_1b_fp16_graphed_fixture` come from
# base/fastforward/conftest.py, since they are used by tests in the `base/fastforward` scope as
# well. This works because conftest files are processed hierarchically.


@pytest.fixture(scope="module")
def starcoder_1b_fp16_graphed_padded_fixture(starcoder_1b_fp16_graphed_fixture):
    """Creates the forward and attention factory for starcoderbase 1B."""
    step_fn, attn_factory, batch_sizes = starcoder_1b_fp16_graphed_fixture
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=batch_sizes)
    yield step_fn, attn_factory


def load_starcoder_1b_fp8(
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    attention_impl: AttentionImpl = AttentionImpl.BATCHED_FLASH,
    use_fused_kernels: bool = False,
) -> tuple[fwd.ForwardStepFn, fwd.AttentionFactory]:
    """Loads the model and attention factory for starcoderbase 1B."""
    model_spec = fwd_utils.get_model_spec_from_neox_checkpoint(
        "/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint_fp8.pth_v2",
    )
    model_spec.checkpoint_sha256 = (
        "4b92739d5b78a81fd3ce66a4229818191aab3e64973e80e883cbcee6de10dc62"
    )

    return (
        fwd_starcoder_fp8.generate_step_fn(
            model_spec,
            auto_capture_graphs=auto_capture_graphs,
            batch_sizes=batch_sizes,
        ),
        fwd_starcoder_fp8.StarcoderAttentionFactory(
            model_spec,
            attention_impl=attention_impl,
            pre_attention_kernel_fusion=use_fused_kernels,
            use_register_tokens_kernel=use_fused_kernels,
        ),
    )


@pytest.fixture(
    scope="module",
    params=[
        (AttentionImpl.BATCHED_FLASH, False),
        (AttentionImpl.MULTI_REQUEST_FLASH, True),
    ],
)
def starcoder_1b_fp8_fixture(
    request,
) -> Generator[tuple[fwd.ForwardStepFn, fwd.AttentionFactory], None, None]:
    """Creates the forward and attention factory for starcoderbase 1B."""
    yield load_starcoder_1b_fp8(
        attention_impl=request.param[0], use_fused_kernels=request.param[1]
    )


@pytest.fixture(
    scope="module",
    params=[
        (AttentionImpl.BATCHED_FLASH, False),
        (AttentionImpl.MULTI_REQUEST_FLASH, True),
    ],
)
def starcoder_1b_fp8_graphed_fixture(
    request,
) -> Generator[tuple[fwd.ForwardStepFn, fwd.AttentionFactory], None, None]:
    """Creates the forward and attention factory for starcoderbase 1B."""
    batch_sizes = [8, 16, 32, 64, 128]
    step_fn, attn_factory = load_starcoder_1b_fp8(
        auto_capture_graphs=True,
        batch_sizes=batch_sizes,
        attention_impl=request.param[0],
        use_fused_kernels=request.param[1],
    )
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=batch_sizes)
    yield step_fn, attn_factory


@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_NO_FA3)
def starcoder_1b_fp16_multigpu_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    ms = fwd_utils.get_model_spec_from_neox_checkpoint(
        "/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint_v2"
    )
    ms.checkpoint_sha256 = (
        "8a26c397f2c233ec1c0cac92e66b85def17f8322bc0f53f84c6233fc57c6a220"
    )
    step_fn = fwd_starcoder.generate_step_fn(
        ms,
        num_processes=2,
        auto_capture_graphs=False,
    )
    attn_factory = fwd_starcoder.StarcoderAttentionFactory(
        ms, num_processes=2, attention_impl=request.param
    )
    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(scope="module")
def starcoder2_test_data():
    yield sample_data.load_sample_data()


@pytest.fixture(scope="module")
def starcoder2_100m_bf16_model_spec_fixture():
    yield model_specs.get_starcoder2_model_spec(
        model_name="starcoder2-100m",
        checkpoint_path=(
            "/mnt/efs/augment/checkpoints/starcoder2/fastforward/starcoder2-100m"
        ),
        checkpoint_sha256="0b569e9e819001145de6eb2276f1f5b12946d8dbf16a63b030a14438e2d9f420",
    )


@pytest.fixture(scope="module")
def starcoder2_100m_bf16_fixture(starcoder2_100m_bf16_model_spec_fixture):
    """Load the weights of a StarCoder2-100M model to use for all tests."""
    ms = starcoder2_100m_bf16_model_spec_fixture
    step_fn = fwd_starcoder2.generate_step_fn(ms=ms)
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms, pre_attention_kernel_fusion=True
    )

    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(scope="module")
def starcoder2_100m_bf16_graphed_fixture(
    starcoder2_test_data, starcoder2_100m_bf16_model_spec_fixture
):
    """Load the weights of a StarCoder2-100M model to use for all tests."""
    ms = starcoder2_100m_bf16_model_spec_fixture

    step_fn = fwd_starcoder2.generate_step_fn(
        ms=ms,
        auto_capture_graphs=True,
        batch_sizes=[1, len(starcoder2_test_data["inputs"])],
    )
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms, pre_attention_kernel_fusion=True
    )

    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(scope="module", params=ALL_ALL_REDUCE_IMPLS)
def starcoder2_100m_bf16_multigpu_fixture(
    request, starcoder2_100m_bf16_model_spec_fixture
):
    """Load the weights of a StarCoder2 model to use for all tests."""
    if request.param == AllReduceImpl.FASTFORWARD_FP8:
        pytest.skip(
            "Skipping FP8 all_reduce since it is not supported for fp16 models."
        )
    ms = starcoder2_100m_bf16_model_spec_fixture
    step_fn = fwd_starcoder2.generate_step_fn(
        ms=ms, num_processes=2, all_reduce_impl=request.param
    )
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        parallel_config=ParallelConfig.from_legacy_config(2, False),
        pre_attention_kernel_fusion=True,
    )

    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(scope="module")
def starcoder2_100m_fp8_model_spec_fixture():
    yield model_specs.get_starcoder2_model_spec(
        model_name="starcoder2-100m",
        checkpoint_path=(
            "/mnt/efs/augment/checkpoints/starcoder2/fastforward/starcoder2-100m-fp8"
        ),
        checkpoint_sha256="cb1fd229691e0c5ddc64f59524d37d57f8fdc6311bdd26685bb7a5b62363547e",
    )


@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_WITH_FP8)
def starcoder2_100m_fp8_fixture(request, starcoder2_100m_fp8_model_spec_fixture):
    """Load the weights of a StarCoder2-3B model to use for all tests."""
    if request.param.is_flash_v3() and not request.config.getoption(
        "--enable-h100-kernels"
    ):
        pytest.skip("Skipping FA3 since --enable-h100-kernels was not passed.")
    ms = starcoder2_100m_fp8_model_spec_fixture

    step_fn = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
    )
    round_sizes = [8, 512]
    step_fn = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms, pre_attention_kernel_fusion=True, attention_impl=request.param
    )

    yield step_fn, attn_factory, max(round_sizes)
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_WITH_FP8)
def starcoder2_100m_fp8_graphed_fixture(
    request, starcoder2_100m_fp8_model_spec_fixture
):
    """Load the weights of a StarCoder2-3B model to use for all tests."""
    if request.param.is_flash_v3() and not request.config.getoption(
        "--enable-h100-kernels"
    ):
        pytest.skip("Skipping FA3 since --enable-h100-kernels was not passed.")
    ms = starcoder2_100m_fp8_model_spec_fixture

    round_sizes = [8, 512]
    step_fn = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
        auto_capture_graphs=True,
        batch_sizes=round_sizes,
    )
    step_fn = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms, pre_attention_kernel_fusion=True, attention_impl=request.param
    )

    yield step_fn, attn_factory, max(round_sizes)
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


# We cannot keep the fixture around with scope module, as multi-GPU test modules (= test files)
# that have multiple tests with different multi gpu models will fail, as we cannot load
# multiple models or else NCCL complains.
@pytest.fixture(scope="function", params=ALL_ALL_REDUCE_IMPLS)
def starcoder2_100m_fp8_multigpu_fixture(
    request, starcoder2_100m_fp8_model_spec_fixture
):
    """Load the weights of a StarCoder2 model to use for all tests."""
    ms = starcoder2_100m_fp8_model_spec_fixture
    parallel_config = ParallelConfig.from_legacy_config(2, False)
    step_fn = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms, parallel_config=parallel_config, all_reduce_impl=request.param
    )
    round_sizes = [8, 512]
    padded_step_fn = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms, parallel_config=parallel_config, pre_attention_kernel_fusion=True
    )

    try:
        yield padded_step_fn, attn_factory, max(round_sizes)
    finally:
        del padded_step_fn
        del step_fn
        del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(
    scope="function",
    params=[
        (AttentionImpl.BATCHED_FLASH, AllReduceImpl.NCCL),
        (AttentionImpl.MULTI_REQUEST_FLASH, AllReduceImpl.NCCL),
        (AttentionImpl.MULTI_REQUEST_FLASH, AllReduceImpl.FASTFORWARD),
    ],
)
def starcoder2_100m_fp8_sequence_parallel_fixture(
    request,
    starcoder2_100m_fp8_model_spec_fixture,
):
    """Load the weights of a StarCoder2 model to use for all tests."""
    attn_impl = request.param[0]
    all_reduce_impl = request.param[1]
    ms = starcoder2_100m_fp8_model_spec_fixture
    parallel_config = ParallelConfig.from_legacy_config(2, True)
    step_fn = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
        parallel_config=parallel_config,
        all_reduce_impl=all_reduce_impl,
    )
    round_sizes = [
        16,
        512,
    ]  # need 16, so that after splitting the round we are left with 8, which is the lowest supported round size for fp8
    padded_step_fn = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=attn_impl,
        pre_attention_kernel_fusion=False,
    )
    try:
        yield padded_step_fn, attn_factory, max(round_sizes)
    finally:
        del padded_step_fn
        del step_fn
        del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(
    scope="function",
    params=[
        (AttentionImpl.BATCHED_FLASH, AllReduceImpl.NCCL),
        (AttentionImpl.MULTI_REQUEST_FLASH, AllReduceImpl.NCCL),
        (AttentionImpl.MULTI_REQUEST_FLASH, AllReduceImpl.FASTFORWARD),
    ],
)
def starcoder2_100m_fp8_dynamic_sequence_parallel_fixture(
    request,
    starcoder2_100m_fp8_model_spec_fixture,
):
    """Load the weights of a StarCoder2 model to use for all tests."""
    attn_impl = request.param[0]
    all_reduce_impl = request.param[1]
    ms = starcoder2_100m_fp8_model_spec_fixture
    sp_config = ParallelConfig.from_legacy_config(2, True)
    tp_config = ParallelConfig.from_legacy_config(2, False)
    step_fn = fwd_starcoder2_fp8.generate_step_fn(
        ms=ms,
        parallel_config=sp_config,
        all_reduce_impl=all_reduce_impl,
        small_round_parallel_config=tp_config,
        small_round_token_cutoff=16,
    )
    round_sizes = [
        16,
        512,
    ]  # need 16, so that after splitting the round we are left with 8, which is the lowest supported round size for fp8
    padded_step_fn = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    attn_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        parallel_config=sp_config,
        attention_impl=attn_impl,
        pre_attention_kernel_fusion=False,
    )
    try:
        yield padded_step_fn, attn_factory, max(round_sizes)
    finally:
        del padded_step_fn
        del step_fn
        del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors
