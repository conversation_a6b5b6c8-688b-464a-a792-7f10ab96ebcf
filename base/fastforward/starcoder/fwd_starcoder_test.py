"""Test for the starcoder model."""

import pytest
import torch

from base.fastforward import (
    cached_attention,
    fwd,
    fwd_model_test_utils,
    fwd_utils,
)
from base.fastforward.conftest import load_starcoder_1b_fp16
from base.fastforward.starcoder import fwd_starcoder

SC_KNOWN_SEQUENCE = fwd_starcoder.SC_KNOWN_SEQUENCE
# Additionally, for StarCoder-1B, conditioning on the first 6 tokens from the
# list above and taking argmax for each successive step will result in the re
SC_PREFIX_INDEX = 6


def _check_if_model_logits_on_target_sequence(
    step_fn: fwd.ForwardStepFn, attn_factory: fwd.AttentionFactory, kv_len=128
):
    attn = attn_factory(max_length=kv_len)

    tokens = list(SC_KNOWN_SEQUENCE)

    logits = step_fn(list(SC_KNOWN_SEQUENCE), attn).checked_cast(torch.Tensor)
    maxes = torch.argmax(logits, dim=-1).to(dtype=torch.int32)
    tokens_to_match = tokens[SC_PREFIX_INDEX:]
    assert tokens_to_match == maxes[SC_PREFIX_INDEX - 1 : -1].tolist()


def test_logits_fp16_default(starcoder_1b_fp16_fixture):
    step_fn, attn_factory = starcoder_1b_fp16_fixture
    _check_if_model_logits_on_target_sequence(step_fn, attn_factory)


def test_logits_fp16_graphed(starcoder_1b_fp16_graphed_padded_fixture):
    step_fn, attn_factory = starcoder_1b_fp16_graphed_padded_fixture
    _check_if_model_logits_on_target_sequence(step_fn, attn_factory)


def test_generate_fp16(starcoder_1b_fp16_fixture):
    step_fn, attn_factory = starcoder_1b_fp16_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=SC_KNOWN_SEQUENCE[:SC_PREFIX_INDEX],
        target_sequence=SC_KNOWN_SEQUENCE[SC_PREFIX_INDEX:],
    )


def test_generate_fp16_graphed(starcoder_1b_fp16_graphed_padded_fixture):
    step_fn, attn_factory = starcoder_1b_fp16_graphed_padded_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=SC_KNOWN_SEQUENCE[:SC_PREFIX_INDEX],
        target_sequence=SC_KNOWN_SEQUENCE[SC_PREFIX_INDEX:],
        extra_kv_len=32,  # to account for cuda graph capturing
    )


@pytest.mark.parametrize("attention_impl", cached_attention.ALL_ATTN_IMPLS_NO_FA3)
def test_num_graph_captures(attention_impl):
    step_fn, attn_factory = load_starcoder_1b_fp16(
        use_parallel=False,
        auto_capture_graphs=True,
        batch_sizes=[8, 16, 32],
        attention_impl=attention_impl,
    )

    unpadded_step_fn = step_fn

    def get_num_captures():
        """Returns the number of graph captures since the beginning of this test function."""
        return len(unpadded_step_fn._apply_transformer_layers._graphed_step_fn_by_attn)

    step_fn = fwd_utils.PaddedStepFunction(step_fn, [8])

    assert get_num_captures() == 0
    attn = attn_factory(max_length=128)
    assert get_num_captures() == 0

    step_fn([1, 2, 3], attn)
    attn.reset()
    # This access to the private attribute is a hack relying on the particular
    # way starcoder wraps the step function in the cuda graph capturing mechanism.
    assert get_num_captures() == 1

    step_fn([0], attn)
    attn.reset()
    # Graphs are currently captured independent from the batch size:
    assert get_num_captures() == 1

    # Regular use does not trigger a new capture either:
    step_fn([1, 2, 3], attn)
    attn.reset()
    step_fn([0], attn)
    attn.reset()
    assert get_num_captures() == 1

    # Capturing is still possible after regular use:
    attn_2 = attn_factory(max_length=128)
    step_fn([1, 2, 3], attn_2)
    attn_2.reset()
    assert get_num_captures() == 2

    # Use with old cache does not trigger a new capture.
    step_fn([1, 2, 3], attn)
    step_fn([0], attn)
    assert get_num_captures() == 2


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="one"),
        pytest.param([1, 2], id="two"),
        pytest.param([1, 2, 3], id="three"),
        pytest.param(SC_KNOWN_SEQUENCE, id="known_sequence"),
    ],
)
def test_batched_equals_sequential(starcoder_1b_fp16_fixture, prompt: list[int]):
    step_fn, attn_factory = starcoder_1b_fp16_fixture
    fwd_model_test_utils.check_batched_equals_sequential(step_fn, attn_factory, prompt)


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="one"),
        pytest.param([1, 2], id="two"),
        pytest.param([1, 2, 3], id="three"),
        pytest.param(SC_KNOWN_SEQUENCE, id="known_sequence"),
    ],
)
def test_batched_equals_sequential_graphed(
    starcoder_1b_fp16_graphed_padded_fixture, prompt: list[int]
):
    step_fn, attn_factory = starcoder_1b_fp16_graphed_padded_fixture
    fwd_model_test_utils.check_batched_equals_sequential(
        step_fn,
        attn_factory,
        prompt,
        extra_kv_len=32,  # to account for cuda graph capturing
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param(
            [1],
            id="dummy",
        ),
        pytest.param(SC_KNOWN_SEQUENCE, id="known_seq"),
    ],
)
def test_logits_are_close(
    starcoder_1b_fp16_fixture, starcoder_1b_fp16_graphed_padded_fixture, prompt
):
    step_fn_fp16, attn_factory_fp16 = starcoder_1b_fp16_fixture
    step_fn_fp16_graphed, attn_factory_fp16_graphed = (
        starcoder_1b_fp16_graphed_padded_fixture
    )
    fwd_model_test_utils.check_logits_are_close(
        step_fn_fp16,
        step_fn_fp16_graphed,
        attn_factory_fp16,
        attn_factory_fp16_graphed,
        prompt,
        extra_kv_len=32,  # to account for cuda graph capturing
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1, 2, 3], id="dummy"),
        pytest.param(SC_KNOWN_SEQUENCE, id="known_seq"),
    ],
)
def test_same_outputs_across_runs(starcoder_1b_fp16_fixture, prompt: list[int]):
    """Asserts that the model behaves the same across different runs.

    This test runs the model twice on the same input, but with different
    attention caches. The point is to guarantee that the model itself has no mutable
    state.
    """
    step_fn, attn_factory = starcoder_1b_fp16_fixture

    attn_1 = attn_factory(128)
    attn_2 = attn_factory(128)

    logits_1 = step_fn(prompt, attn_1).checked_cast(torch.Tensor)
    logits_2 = step_fn(prompt, attn_2).checked_cast(torch.Tensor)

    torch.testing.assert_close(logits_1, logits_2)


def test_embedding():
    """Tests StarCoder embedding."""
    ms = fwd_utils.get_model_spec_from_neox_checkpoint(
        "/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000_v2"
    )
    ms.checkpoint_sha256 = (
        "21848ba8bbc07ec1552f8b42aac78db793cd77e6844208d47020cbbf7e718634"
    )
    step_fn = fwd_starcoder.generate_step_fn(
        ms, output_type=fwd.OutputTensorType.EMBEDDING
    )
    attn_factory = fwd_starcoder.StarcoderAttentionFactory(ms)
    attn = attn_factory(max_length=128)
    logits = step_fn(list(SC_KNOWN_SEQUENCE), attn).checked_cast(torch.Tensor)
    assert list(logits.shape) == [22, 512]
