"""A dummy language model designed for testing.

This model includes attention layer that does nothing
and a step function that always returns the same value.
"""

from typing import Sequence

import torch

from base.fastforward import all_reduce, cached_attention, fwd, fwd_torch
from base.fastforward.parallel import ParallelContext

Tensor = torch.Tensor


class DummyAttentionState(cached_attention.MultiCacheAttention):
    """Dummy attention layer that does almost nothing."""

    def __init__(self, num_caches: int = 1):
        self._num_caches = num_caches
        self._tokens: list[list[int]] = [[] for _ in range(num_caches + 1)]

    def __call__(
        self,
        qkv: Tensor,
        cache_idxs: Tensor,
        cache_pos: Tensor,
        layer_idx: int,
        cumulative_round_pos: torch.Tensor | None = None,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: Tensor | None = None,
    ) -> Tensor:
        return torch.zeros((1, 1), dtype=torch.int32)

    def register_tokens_get_positions(
        self, tokens: Tensor, cache_idxs: Tensor, process_idx: int
    ) -> Tensor:
        del process_idx
        cache_idxs = cache_idxs.cpu().numpy().tolist()
        tokens = tokens.cpu().numpy().tolist()
        assert len(cache_idxs) == len(tokens), f"{len(cache_idxs)=} != {len(tokens)=}"

        positions = []
        idx_cnts = [len(t) for t in self._tokens]
        for token_as_tensor, cache_idx in zip(tokens, cache_idxs):
            token = int(token_as_tensor)
            if cache_idx == self.padding_cache_idx:
                positions.append(self.padding_cache_pos)
            else:
                positions.append(idx_cnts[cache_idx])
                idx_cnts[cache_idx] += 1
            self._tokens[cache_idx].append(token)
        return torch.tensor(positions, dtype=torch.int32)

    def get_fill_length(self, cache_idx: int, process_idx: int) -> Tensor:
        del process_idx
        return torch.tensor([len(self._tokens[cache_idx])], dtype=torch.int32)

    def reset(
        self,
        *,
        cache_idx: int,
        to_position: int | None = None,
        by_positions: int | None = None,
    ):
        if to_position is None and by_positions is None:
            to_position = 0
        if to_position is not None and by_positions is not None:
            raise ValueError(
                f"Only one of to_position and by_positions can be specified. "
                f"Got to_position={to_position} and by_positions={by_positions}."
            )
        if by_positions is not None:
            if by_positions < 0:
                raise ValueError(f"{by_positions=} < 0")
            current_position = int(self.get_fill_length(cache_idx=0, process_idx=0))
            to_position = current_position - by_positions
        assert to_position is not None
        assert to_position <= len(
            self._tokens[cache_idx]
        ), f"{to_position=} but {len(self._tokens[cache_idx])=}"
        self._tokens[cache_idx] = self._tokens[cache_idx][:to_position]

    def get_tokens(self, cache_idx: int = 0) -> Tensor:
        tokens = torch.tensor(self._tokens[cache_idx], dtype=torch.int32)
        return tokens

    def get_tensors_for_cuda_graph(
        self, process_idx: int | None = None
    ) -> dict[str, Tensor]:
        assert not process_idx, process_idx
        return {
            "tokens": torch.tensor(self._tokens, dtype=torch.int32, device="cpu"),
        }

    @property
    def num_caches(self) -> int:
        return self._num_caches

    @property
    def padding_cache_idx(self) -> int:
        return 0

    @property
    def padding_cache_pos(self) -> int:
        return 0

    def get_devices(self) -> Sequence[torch.device]:
        return [torch.device("cpu")]

    def get_device_idx_from_process_idx(self, process_idx: int | None) -> int:
        assert not process_idx, process_idx
        return 0

    def get_device_from_process_idx(self, process_idx: int | None) -> torch.device:
        assert not process_idx, process_idx
        return torch.device("cpu")

    def get_id(self) -> str:
        return "DummyAttentionState"

    @property
    def captured_attn_maxes(self) -> dict[int, Tensor]:
        raise NotImplementedError

    def get_max_requests_in_round(self) -> int | None:
        return None

    def get_small_request_max_seqlen(self) -> int | None:
        return None


class DummyAttentionFactory(fwd.AttentionFactory):
    """Creates DummyAttentionState objects."""

    def create_cache_pool(
        self, max_length: int, num_attention_caches: int
    ) -> cached_attention.MultiCacheAttention:
        del max_length
        return DummyAttentionState(
            num_caches=num_attention_caches,
        )


def get_dummy_attention_factory() -> fwd.AttentionFactory:
    return DummyAttentionFactory()


class ConstantStepFn:
    """Always return logits for the same value."""

    def __init__(self, vocab_size: int, value: int):
        self.vocab_size = vocab_size
        self.value = value

    def __call__(
        self, tokens: Sequence[int], attn: cached_attention.Attention
    ) -> fwd.ModelOutput:
        """Implements fwd.ForwardStepFn."""
        tokens_tensor: Tensor = torch.tensor(tokens, device="cpu")

        _ = attn.register_tokens_get_positions(tokens_tensor, process_idx=0)
        logits = torch.zeros(size=(len(tokens), self.vocab_size), dtype=torch.float16)
        for index in range(len(tokens)):
            logits[index, self.value] = 100.0
        return fwd_torch.TorchLogits2D(logits)


def generate_constant_step_fn(
    vocab_size: int,
    value: int,
    num_processes: int = 1,
    auto_capture_graphs: bool = True,
) -> fwd.ForwardStepFn:
    del auto_capture_graphs
    step_fn: fwd.ForwardStepFn = ConstantStepFn(vocab_size, value)
    assert num_processes > 0
    return step_fn
