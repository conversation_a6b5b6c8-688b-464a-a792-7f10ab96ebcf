"""Tests for base.fastforward.fp8."""

import unittest

import torch
import torch.nn as nn

from base.fastforward import fp8


def test_to_fp8():
    # Taking example from https://docs.nvidia.com/deeplearning/transformer-engine/user-guide/examples/fp8_primer.html
    # Except that it's wrong --- the number below, 0.4062 is closer than 0.375!
    x = torch.tensor(0.395264).cuda()
    ret = fp8.to_fp8(x, 1.0, "e4m3")
    assert ret.shape == ()
    assert torch.allclose(
        ret,
        # Integer: 45, Float: 0.4062
        torch.tensor(0b00101101, dtype=torch.uint8, device=ret.device),
    )


def make_linear(device="cuda"):
    linear = nn.Linear(16, 8, device=device)
    linear.load_state_dict(
        {
            "weight": torch.tensor(
                [
                    [0.625, 0.125],
                    [0.725, -3.795],
                ]
            )
            # Tiling this tensor to expand a 2x2 matrix to a 8x16 one.
            .repeat(4, 8),
            "bias": torch.tensor(
                [
                    0.625,
                    0.125,
                ]
            ).repeat(4),
        }
    )
    return linear


def test_from_fp8():
    # Integer: 45, Float: 0.4062
    x = torch.tensor(0b00101101, dtype=torch.uint8).cuda()
    ret = fp8.from_fp8(x, 1.0, torch.float16)
    assert ret.shape == ()
    assert torch.allclose(
        ret, torch.tensor(0.4062, dtype=torch.float16, device=ret.device)
    )


def test_convert_linear_to_fp8():
    linear = make_linear()
    # NOTE(arun): Setting input and output per-channel amaxes to 1 for testing.
    input_amaxes = torch.ones(linear.in_features).to(linear.weight)
    fp8_linear = fp8.convert_linear_to_fp8(linear, input_amaxes, input_amaxes)

    assert fp8_linear.weight.shape == (8, 16)
    assert fp8_linear.weight.is_contiguous()
    assert fp8_linear.bias is not None and fp8_linear.bias.shape == (8,)
    assert torch.allclose(
        fp8_linear.weight.data[:2, :2],
        torch.tensor(
            [
                [98, 80],
                [100, 247],
            ]
        ).to(fp8_linear.weight),
    )
    assert torch.allclose(
        fp8_linear.bias.data,
        linear.bias.data,
    )
    assert torch.allclose(fp8_linear.weight_scale.data, torch.tensor([64.0]))
    assert torch.allclose(fp8_linear.input_scale.data, torch.tensor([256.0]))


def test_convert_linear_to_fp8_with_scale():
    linear = make_linear()
    # NOTE(arun): Setting input per-channel amaxes to be a wide range -- this actually
    # only will change the value of the input scale.
    input_amaxes = torch.exp(
        torch.arange(linear.in_features) - linear.in_features // 2
    ).cuda()
    output_amaxes = torch.ones(linear.out_features).to(linear.weight)  # Unused
    fp8_linear = fp8.convert_linear_to_fp8(
        linear, input_amaxes, output_amaxes, preserve_input=True
    )

    assert fp8_linear.weight.shape == (8, 16)
    assert fp8_linear.weight.is_contiguous()
    assert fp8_linear.bias is not None and fp8_linear.bias.shape == (8,)
    # NOTE(arun): the weights and weight scale should remain unchanged.
    assert torch.allclose(
        fp8_linear.weight.data[:2, :2],
        torch.tensor(
            [
                [98, 80],
                [100, 247],
            ]
        ).to(fp8_linear.weight),
    )
    assert torch.allclose(
        fp8_linear.bias.data,
        linear.bias.data,
    )
    assert torch.allclose(fp8_linear.weight_scale.data, torch.tensor([64.0]))
    assert torch.allclose(fp8_linear.input_scale.data, torch.tensor([0.25]))


def test_convert_linear_from_fp8():
    linear = make_linear()
    # NOTE(arun): Setting input and output per-channel amaxes to 1 for testing.
    input_amaxes = torch.ones(linear.in_features).to(linear.weight)
    fp8_linear = fp8.convert_linear_to_fp8(linear, input_amaxes, input_amaxes)
    linear_ = fp8.convert_linear_from_fp8(fp8_linear, linear.weight.dtype)

    err = (linear.weight.data[:2, :2] - linear_.weight.data[:2, :2]).norm()
    print(f"Quantization error (weights): {err:0.3f}")

    assert torch.allclose(
        linear_.weight.data[:2, :2],
        torch.tensor(
            [
                [0.6250, 0.1250],
                [0.7500, -3.7500],
            ]
        ).to(linear_.weight),
        atol=1e-4,
    )
    assert torch.allclose(
        linear_.bias.data,
        linear.bias.data,
    )


class FP8LinearTest(unittest.TestCase):
    """Tests for base.fastforward.fp8.FP8Linear."""

    def test_create(self):
        linear = fp8.FP8Linear(16, 8)
        assert linear.in_features == 16
        assert linear.out_features == 8
        assert linear.weight.dtype == torch.uint8
        assert linear.bias is not None
        assert linear.bias.dtype == torch.float16
        assert linear.weight_scale == 1.0
        assert linear.input_scale == 1.0
        assert linear.weight.shape == (8, 16)
        assert linear.weight.is_contiguous()

    def test_forward(self):
        with torch.random.fork_rng():
            torch.random.manual_seed(0)
            linear = nn.Linear(16, 8, dtype=torch.float16).cuda()
            inputs = torch.randn((8, 16), dtype=torch.float16).cuda()

        input_amaxes = inputs.abs().amax(dim=0)
        output_amaxes = torch.ones(linear.out_features).to(linear.weight)  # Unused
        fp8_linear = fp8.convert_linear_to_fp8(
            linear, input_amaxes, output_amaxes, preserve_input=True
        )

        outputs = linear(inputs)
        outputs_ = fp8_linear(inputs)

        err = (outputs - outputs_).norm()
        print(f"Quantization error (activations): {err:0.3f}")
        assert torch.allclose(
            outputs,
            outputs_,
            # NOTE(arun): this is quite high, but not uncommon with quantization.
            atol=1e-1,
        )
