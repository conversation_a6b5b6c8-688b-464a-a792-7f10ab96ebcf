"""Tools to run models with tensor parallelism in inference."""

# For some reason, the use of `|` does not work without the future import. This is probably
# because of mp.SimpleQueue is a function instead of a class.
from __future__ import annotations

import _thread
import dataclasses
import logging
import os
import queue
import sys
import threading
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Callable, Dict, Generic, Sequence, TypeVar
from torch.distributed.distributed_c10d import ProcessGroup
from torch.distributed.device_mesh import DeviceMesh

import structlog
import torch
import torch.distributed as dist
import torch.multiprocessing as mp

# TODO:
# - figure out simulating multiple devices for testing


class ParallelRunnerException(Exception):
    """Exception raised by the ParallelRunner."""


def _init_process(
    process_idx: int,
    num_processes: int,
    backend: str = "nccl",
    ip_address: str = "127.0.0.1",
    port: int = 29700,
):
    """Initialize the distributed environment."""
    print(f"{process_idx=}: Calling _init_process.", flush=True)
    if num_processes < 1:
        raise ValueError(f"{num_processes=} is invalid.")
    if process_idx < 0 or process_idx >= num_processes:
        raise ValueError(f"{process_idx=} is invalid.")
    structlog.contextvars.bind_contextvars(process_idx=str(process_idx))
    os.environ["TORCH_NCCL_ASYNC_ERROR_HANDLING"] = "0"
    # NOTE: https://discuss.pytorch.org/t/cuda-allocation-lifetime-for-inputs-to-distributed-all-reduce/191573
    os.environ["TORCH_NCCL_AVOID_RECORD_STREAMS"] = "1"
    # NOTE: this env var is _essential_ for good NCCL performance with cuda graphs.
    # See this discussion: https://discuss.pytorch.org/t/unexplained-gaps-in-execution-before-nccl-operations-when-using-cuda-graphs/197818
    # That public post has a microbenchmark showing 5-10us of overhead w/o this
    # env var, but in fastforward we saw upwards of 40us.
    # Documentation (not at all clear): https://docs.nvidia.com/deeplearning/nccl/user-guide/docs/env.html#nccl-graph-mixing-support
    os.environ["NCCL_GRAPH_MIXING_SUPPORT"] = "0"
    options = dist.ProcessGroupNCCL.Options()
    options.is_high_priority_stream = True  # type: ignore
    # pylint: disable=protected-access
    options._timeout = timedelta(seconds=10)  # type: ignore
    # pylint: enable=protected-access
    logging.info(
        "Initializing Process %d with backend %s, ip_address %s, port %d.",
        process_idx,
        backend,
        ip_address,
        port,
    )
    dist.init_process_group(
        backend,
        init_method=f"tcp://{ip_address}:{port}",
        rank=process_idx,
        world_size=num_processes,
        timeout=timedelta(seconds=10),
        pg_options=options,
    )
    torch.cuda.set_device(process_idx)
    print(f"Process {process_idx} initialized.", flush=True)


@dataclasses.dataclass(frozen=True)
class _RunnerArgs:
    """Arguments for the child processes."""

    args: Sequence[Any]
    kwargs: Dict[str, Any]

    alive_probe: bool = False
    """If the child is called with the alive_probe set to true,
    it must answer immediately with _RunnerResult with the flag
    alive_probe_response set to True.
    """


@dataclasses.dataclass(frozen=True)
class _RunnerResult:
    """Results from the child processes."""

    result: Any
    exception: Exception | None

    alive_probe_response: bool = False
    """See _RunnerArgs.alive_probe."""


def _run_child(
    process_idx: int,
    num_processes: int,
    args_queue: mp.JoinableQueue,
    kill_signal_queue: mp.SimpleQueue | None,
    result_queue: mp.SimpleQueue,
    on_subprocess_start_fn: Callable[[], None],
    init_fn: Callable[..., Callable[..., Any]],
    init_args: Dict[str, Any] | None = None,
    verbose: bool = False,
):
    """Start the processing loop on a child process.

    Listens to calls via the queue. The function that the arguments are passed
      to is created via init_fn(**init_args).

    Args:
        process_idx: Index of this process.
        num_processes: Number of processes in this group.
        args_queue: to be used to receive arguments for the next call from the main process.
        kill_signal_queue: to be used to receive kill signals from the main process.
        result_queue: to be used to send results back to the main process.
        on_subprocess_start_fn: A function that is called directly when creating
            the subprocess, e.g. to setup logging.
        init_fn: Factory function that creates a function. Is called in the
            current process and all child processes. Can be used to load models.
        init_args: dictionary of keyword arguments passed to init_fn.
        verbose: If true, print occasional sign of life messages.
    """
    if init_args is None:
        init_args = {}
    on_subprocess_start_fn()
    _init_process(process_idx=process_idx, num_processes=num_processes)

    if kill_signal_queue is not None:
        print(
            f"{process_idx=}: Creating thread to listen to the kill signal.", flush=True
        )

        def kill_listener():
            kill_signal_queue.get()
            print(f"{process_idx=}: Received kill signal. Exiting.", flush=True)
            _thread.interrupt_main()  # raises KeyboardInterrupt in the main thread

        kill_thread = threading.Thread(target=kill_listener)
        kill_thread.start()

    print(f"{process_idx=}: Calling given init_fn.", flush=True)
    run_fn = init_fn(**init_args, process_idx=process_idx, num_processes=num_processes)
    del init_args
    print(f"{process_idx=}: Starting listening loop.", flush=True)

    try:
        while True:
            # loop around queue.get with timeout to get occasional sign of life in the logs.
            while True:
                try:
                    packed_args = args_queue.get(
                        timeout=5.0
                    )  # raises queue.Empty on timeout
                    # We are using a JoinableQueue; task_done() signals to the sending
                    # process that the object was received, which is checked by the
                    # receiving process via join().
                    args_queue.task_done()
                    assert isinstance(packed_args, _RunnerArgs)
                    break  # break out of the loop waiting for new inputs
                except queue.Empty:
                    if verbose:
                        print(
                            f"{process_idx=}: No inputs received. Process is waiting for new inputs.",
                            flush=True,
                        )
            if packed_args.alive_probe:
                print(
                    f"{process_idx=}: Received alive probe.",
                    flush=True,
                )
                result_queue.put(_RunnerResult(True, None, alive_probe_response=True))
                continue

            # args and kwargs are available
            assert (
                "process_idx" not in packed_args.kwargs
            ), "process_idx and num_processes are passed in via the ParallelRunner wrapper."
            assert "num_processes" not in packed_args.kwargs
            try:
                result = run_fn(*packed_args.args, **packed_args.kwargs)
            except Exception as e:  # pylint: disable=broad-exception-caught
                print(
                    f"Exception while running the function in the child process: {e}. {process_idx=}",
                    flush=True,
                )
                if process_idx == 0:
                    # TODO(markus): improve error reporting to the main process. Currently, we
                    # only report the exceptions from process 0 to the main process.
                    result_queue.put(_RunnerResult(None, e))
                raise
            del packed_args  # no need to keep args around
            if process_idx == 0:
                result_queue.put(_RunnerResult(result, None))
    except KeyboardInterrupt:
        print(f"{process_idx=}: Received KeyboardInterrupt. Exiting.", flush=True)
        # free up the process group so that we can initialize it again later.
        dist.barrier()
        dist.destroy_process_group()
        print(f"{process_idx=}: Destroyed process group. Exiting.", flush=True)
        sys.exit(0)


T = TypeVar("T")


def _empty_fn():
    """Empty function that is used as on_subprocess_start_fn.

    Used instead of lambda: None as lambda: None cannot be pickled
    """
    pass


class ParallelRunner(Generic[T]):
    """Runs a function in multiple processes in parallel.

    For an example, study the use of compute_sum in parallel_test.py.
    """

    def __init__(
        self, num_processes: int, on_subprocess_start_fn: Callable[[], None] = _empty_fn
    ):
        self.num_processes = num_processes  # world_size
        self._processes = []  # to store running processes
        self._queues = []
        self._kill_signal_queues = []
        self._recv_queues = []
        self._on_subprocess_start_fn = on_subprocess_start_fn
        if num_processes > torch.cuda.device_count():
            raise RuntimeError(
                f"Only {torch.cuda.device_count()} GPUs found. Cannot instantiate {num_processes} processes."
            )
        if num_processes < 1:
            raise ValueError(f"{num_processes=} is invalid.")

    def __del__(self):
        """Ensures that all processes are shut down as the decorated function dies."""
        if self._processes is not None:
            for idx, _ in enumerate(self._processes):
                print(f"Terminating process {idx}.")
                self._kill_signal_queues[idx].put(None)
            for idx, _ in enumerate(self._processes):
                self._processes[idx].terminate()
                self._processes[idx].join()

    def __enter__(self):
        """Allows the use of the ParallelRunner as a context manager.

        This is mostly useful for testing where exceptions can otherwise
        cause the test to hang until the timeout.
        """
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.__del__()

    def _wait_for_initialization_complete(self):
        print("Probing for alive signal...")
        for q in self._queues:
            q.put(_RunnerArgs((), {}, alive_probe=True))
        print("Listening for alive signal response...")
        for idx, q in enumerate(self._recv_queues):
            result = q.get()
            if result.exception is not None:
                raise ParallelRunnerException from result.exception
            if not result.alive_probe_response:
                raise RuntimeError(f"Process {idx} did not answer alive signal.")
        print("Parallel initialization complete.", flush=True)

    def initialize(
        self, init_fn: Callable[..., Callable[..., T]], **init_args
    ) -> Callable[..., T]:
        """Initializes model parallel function.

        Args:
            init_fn: Function that is called each process with init_args.
                init_fn must have the following the arguments process_idx
                and num_processes.
            init_args: Dictionary of keyword arguments passed to init_fn.

        Returns:
            A function that will be executed in each process with arguments
            P whenever the function is called in the main process with
            arguments P.
        """
        print("Initialize multiprocessing...")
        if self._processes:
            raise RuntimeError("ParallelRunner already initialized.")

        ctx = mp.get_context("spawn")
        self._recv_queues = [ctx.SimpleQueue() for _ in range(self.num_processes)]
        for process_idx in range(self.num_processes):
            send_q = ctx.JoinableQueue()
            kill_signal_queue = ctx.SimpleQueue()
            p = ctx.Process(
                target=_run_child,
                args=(
                    process_idx,
                    self.num_processes,
                    send_q,
                    kill_signal_queue,
                    self._recv_queues[process_idx],
                    self._on_subprocess_start_fn,
                    init_fn,
                    init_args,
                ),
                # NOTE: We cannot use daemonic processes because some
                # libraries (e.g. torch.compile) use child processes
                # themselves, which does not work for daemonic processes. We
                # need to find another way to shut down child processes when the
                # main process dies.
                daemon=False,
            )
            p.start()
            self._processes.append(p)
            self._queues.append(send_q)
            self._kill_signal_queues.append(kill_signal_queue)

        def wrapped_fn(*args, **kwargs) -> T:
            assert self._queues is not None
            for q in self._queues:
                q.put(_RunnerArgs(args, kwargs), block=True)

            # Wait for all arguments to be pickled because Python is allowed to pickle
            # in a background thread. Because this function only blocks on process 0, if
            # process 0 finishes sooner than the rest, then the parent process could execute
            # some code that modifies args/kwargs in parallel with the pickling process.
            # One could argue that process 0 needs to synchronize with other processes to
            # create its result. However, because tensors operations can happen asynchronously to the
            # CPU, we are not sure that this synchronization must happen before a tensor
            # is returned from process 0.
            #
            # The implementation actually waits for the remote side to have received
            # and acknowledged the object, which is a superset of waiting for the pickle to
            # complete
            for q in self._queues:
                q.join()

            # we only gather the result from process 0
            result = self._recv_queues[0].get()

            if result.exception is not None:
                raise ParallelRunnerException from result.exception
            return result.result

        self._wait_for_initialization_complete()

        return wrapped_fn


def _validate_process_idx(process_idx: int, num_processes: int):
    if num_processes < 1:
        raise ValueError(f"{num_processes=} is invalid.")
    if process_idx < 0 or process_idx >= num_processes:
        raise ValueError(f"{process_idx=} is invalid.")


def split_tokens_for_sequence_parallelism(
    tokens: Sequence[int], process_idx: int, num_processes: int
) -> Sequence[int]:
    """Split tokens for sequence parallelism.

    Splits the tokens into `num_processes` chunks, and returns the chunk for
    `process_idx`.
    """
    _validate_process_idx(process_idx, num_processes)
    assert len(tokens) > 0
    if len(tokens) % num_processes != 0:
        raise ValueError(
            f"Cannot split {len(tokens)=} tokens into {num_processes=} chunks."
        )
    if num_processes == 1:
        return tokens
    else:
        chunk_size = len(tokens) // num_processes
        return tokens[process_idx * chunk_size : (process_idx + 1) * chunk_size]


def split_tensor_for_sequence_parallelism(
    tensor: torch.Tensor, parallel_ctx: ParallelContext
) -> torch.Tensor:
    """Split tensor for sequence parallelism.

    Splits the tensor into `num_processes` chunks along the first dimension, and returns the chunk for
    `process_idx`.
    """
    assert len(tensor.shape) > 0
    if tensor.shape[0] % parallel_ctx.sp_size != 0:
        raise ValueError(
            f"Cannot split {tensor.shape[0]=} tokens into {parallel_ctx.sp_size=} chunks."
        )
    if parallel_ctx.sp_size == 1:
        return tensor
    else:
        chunk_size = tensor.shape[0] // parallel_ctx.sp_size
        return tensor[
            parallel_ctx.sp_rank * chunk_size : (parallel_ctx.sp_rank + 1) * chunk_size,
            ...,
        ]


def gather_sequence_parallel(
    tensor: torch.Tensor, parallel_ctx: ParallelContext
) -> torch.Tensor:
    """Collects tensors from all processes and concatenates them along the first dimension."""
    tensor = tensor.contiguous()  # nccl needs contiguous tensors as input
    local_batch, rest_shape = tensor.shape[0], tensor.shape[1:]
    result = torch.empty(
        (local_batch * parallel_ctx.sp_size, *rest_shape),
        device=tensor.device,
        dtype=tensor.dtype,
    )
    assert (
        parallel_ctx.process_idx == torch.cuda.current_device()
    ), f"Multi-gpu communication needs to assume that {parallel_ctx.process_idx=} is {torch.cuda.current_device()=}."
    dist.all_gather_into_tensor(result, tensor, group=parallel_ctx.sp_group)
    return result


@dataclasses.dataclass(frozen=True)
class ParallelConfig:
    num_processes: int
    tp_size: int
    sp_size: int

    def __post_init__(self):
        if self.num_processes < 1:
            raise ValueError(f"{self.num_processes=} is invalid.")
        if self.tp_size * self.sp_size != self.num_processes:
            raise ValueError(
                f"{self.tp_size=} * {self.sp_size=} != {self.num_processes=}"
            )

    # NOTE(manual-parallel-mapping): ideally, these next two methods exist, since they are
    # redundant with using `tp_rank` and `sp_rank` from a construted ParallelContext object. But,
    # during things like weight loading, you need to reason about your TP/SP rank before you know
    # distributed initialization, so you cannot construct a ParallelContext. In general, you should
    # avoid using these methods except when necessary.
    # These calculations assume exactly two parallel dims (tp, sp) with sp as the "inner" dim.
    def process_idx_to_tp_rank(self, process_idx: int) -> int:
        return process_idx // self.sp_size

    def process_idx_to_sp_rank(self, process_idx: int) -> int:
        return process_idx % self.sp_size

    @classmethod
    def single_process(cls):
        return cls(num_processes=1, tp_size=1, sp_size=1)

    @classmethod
    def from_legacy_config(cls, num_processes: int, use_sequence_parallel: bool):
        if use_sequence_parallel:
            return cls(num_processes=num_processes, tp_size=1, sp_size=num_processes)
        else:
            return cls(num_processes=num_processes, tp_size=num_processes, sp_size=1)


class ParallelContext:
    def __init__(self, cfg: ParallelConfig, process_idx: int):
        self.cfg = cfg
        self.process_idx = process_idx
        if cfg.num_processes == 1:
            assert process_idx == 0
            self.tp_rank = 0
            self.sp_rank = 0
            self._tp_group = None
            self._sp_group = None
            self._device_mesh = None
            return

        if not dist.is_initialized():
            raise RuntimeError("torch.distributed is not initialized.")
        if dist.get_world_size() != cfg.num_processes:
            raise RuntimeError(
                f"distributed world size {dist.get_world_size()} != {cfg.num_processes}"
            )
        if torch.cuda.current_device() != self.process_idx:
            raise RuntimeError(
                f"{torch.cuda.current_device()=} doesn't match {self.process_idx=}. This is probably an error."
            )
        if dist.get_rank() != self.process_idx:
            raise RuntimeError(
                f"{dist.get_rank()} doesn't match {self.process_idx=}. This is probably an error."
            )

        self._device_mesh = DeviceMesh(
            device_type="cuda",
            mesh=torch.arange(cfg.num_processes).view(cfg.tp_size, cfg.sp_size),
            mesh_dim_names=("tp", "sp"),
        )
        self._tp_group = self._device_mesh.get_group("tp")
        self.tp_rank = dist.get_rank(group=self._tp_group)  # type: ignore
        self._sp_group = self._device_mesh.get_group("sp")
        self.sp_rank = dist.get_rank(group=self._sp_group)  # type: ignore
        # Check that the manually computed ranks are consistent with the true ones.
        # See NOTE(manual-parallel-mapping) above for more context.
        assert (
            self.tp_rank == cfg.process_idx_to_tp_rank(self.process_idx)
        ), f"({self.process_idx=}) {self.tp_rank=} != {cfg.process_idx_to_tp_rank(self.process_idx)=}"
        assert (
            self.sp_rank == cfg.process_idx_to_sp_rank(self.process_idx)
        ), f"({self.process_idx=}) {self.sp_rank=} != {cfg.process_idx_to_sp_rank(self.process_idx)=}"

    @property
    def num_processes(self):
        return self.cfg.num_processes

    @property
    def tp_size(self):
        return self.cfg.tp_size

    @property
    def tp_group(self) -> ProcessGroup:
        if self._tp_group is None:
            raise RuntimeError("Cannot access tp_group for single-process.")
        return self._tp_group  # type: ignore

    @property
    def sp_size(self):
        return self.cfg.sp_size

    @property
    def sp_group(self) -> ProcessGroup:
        if self._sp_group is None:
            raise RuntimeError("Cannot access sp_group for single-process.")
        return self._sp_group  # type: ignore

    @classmethod
    def single_process(cls):
        return cls(ParallelConfig.single_process(), 0)
