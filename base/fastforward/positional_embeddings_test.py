"""Tests for RoPE embeddings."""

from typing import Sequence

import numpy as np
import pytest
import torch

from base.fastforward import positional_embeddings, positional_embeddings_test_utils

DEVICE = "cuda"


@pytest.mark.parametrize(
    "inp_size, offset, max_seq_length, rotary_interleave, rotary_ndims, dtype",
    [
        pytest.param((3, 4, 2, 16), 5, 21, True, 16, torch.float32, id="q"),
        pytest.param((3, 4, 16), 10, 21, True, 16, torch.float32, id="k"),
    ],
)
def test_dim_preserving(
    inp_size: tuple[int],  # seq_len, ..., hidden_dim
    offset: int,
    max_seq_length: int,
    rotary_interleave: bool,
    rotary_ndims: int,
    dtype: torch.dtype,
):
    """Rope should **not** alter the input size."""
    head_dim = inp_size[-1]
    config = positional_embeddings.RotaryConfig(
        rotary_ratio=rotary_ndims / head_dim,
        rotary_theta=1e5,
        max_position_embeddings=max_seq_length,
        rotary_interleave=rotary_interleave,
    )

    rotary = positional_embeddings.RotaryEmbedding(
        dim=inp_size[-1],
        config=config,
        precision=dtype,
        device=DEVICE,
    )

    x = torch.randn(inp_size, dtype=dtype, device=DEVICE)
    cos, sin = rotary(x, seq_dim=0)
    assert cos.is_contiguous()
    assert sin.is_contiguous()

    y_pt = positional_embeddings._apply_rotary_pos_emb_interleave(x, cos, sin, offset)

    assert y_pt.size() == x.size()


@pytest.mark.parametrize(
    ",".join(
        [
            "rope_dim",
            "max_seq_len",
            "head_dim",
            "rope_interleave",
            "seq_len",
            "base",
            "precision",
            "device",
        ]
    ),
    [
        pytest.param(8, 13, 8, False, 5, 1e4, torch.float32, "cuda", id="dev"),
        pytest.param(8, 14, 8, True, 5, 1e4, torch.float32, "cuda", id="dev_inter"),
        pytest.param(4, 13, 8, False, 10, 1e4, torch.float32, "cuda", id="dev_part"),
        pytest.param(
            4, 14, 8, True, 10, 1e4, torch.float32, "cuda", id="dev_part_inter"
        ),
        pytest.param(64, 128, 64, False, 19, 1e4, torch.float32, "cuda", id="large"),
        pytest.param(
            64, 128, 64, True, 21, 1e4, torch.float32, "cuda", id="large_inter"
        ),
        pytest.param(
            64, 128, 96, False, 11, 1e4, torch.float32, "cuda", id="large_part"
        ),
        pytest.param(
            64, 128, 96, True, 17, 1e4, torch.float32, "cuda", id="large_part_inter"
        ),
    ],
)
def test_rope(
    rope_dim: int,
    max_seq_len: int,
    head_dim: int,
    rope_interleave: bool,
    seq_len: int,
    base: float,
    precision: torch.dtype,
    device: str,
):
    """Tests that the outputs of the new ROPE match those of the old ROPE."""
    np.random.seed(31415)
    torch.random.manual_seed(31415)

    config = positional_embeddings.RotaryConfig(
        rotary_ratio=rope_dim / head_dim,
        rotary_theta=base,
        max_position_embeddings=max_seq_len,
        rotary_interleave=rope_interleave,
    )

    new_emb = positional_embeddings.RopeEmbedding(
        head_dim=head_dim,
        config=config,
        precision=precision,
        device=device,
    )

    ref_emb = positional_embeddings.RotaryEmbedding(
        dim=rope_dim,
        config=config,
        precision=precision,
        device=device,
    )

    torch.testing.assert_close(new_emb.cos_cached.view(-1), ref_emb.cos_cached.view(-1))

    torch.testing.assert_close(
        ref_emb.sin_cached.view(-1).abs(), ref_emb.sin_cached.view(-1).abs()
    )

    num_heads = np.random.randint(0, 5)
    offset = np.random.randint(0, max_seq_len - seq_len)

    q = torch.randn(
        size=(seq_len, num_heads, 1, head_dim), dtype=precision, device=device
    )
    k = torch.randn(size=(seq_len, num_heads, head_dim), dtype=precision, device=device)

    offset_tensor = torch.scalar_tensor(offset, dtype=torch.int32)
    q_new = new_emb.forward(q.clone(), offset_tensor)
    k_new = new_emb.forward(k.clone(), offset_tensor)

    try:
        q_ref, k_ref = ref_emb.embed(q.clone(), k.clone(), offset)
        torch.testing.assert_close(q_ref.view(-1), q_new.view(-1))
        torch.testing.assert_close(k_ref.view(-1), k_new.view(-1))
    except RuntimeError:
        print("Reference FAILED!")


@pytest.mark.parametrize("ntokens", [1, 12])
@pytest.mark.parametrize("queries_per_kv_head", [1, 8])
@pytest.mark.parametrize("rotary_pct", [0.75, 1.0])
def test_fused_rope(ntokens: int, queries_per_kv_head: int, rotary_pct: float):
    """Test for the fused rope implementation."""

    torch.cuda.manual_seed(1234 + ntokens + queries_per_kv_head)
    n_kv_heads = 16
    head_dim = 64
    max_seq_len = 4096
    base = 10000.0

    q = torch.randn(
        (ntokens, n_kv_heads, queries_per_kv_head, head_dim),
        dtype=torch.float32,
        device="cuda",
    )
    k = torch.randn((ntokens, n_kv_heads, head_dim), dtype=torch.float32, device="cuda")

    config = positional_embeddings.RotaryConfig(
        rotary_ratio=rotary_pct,
        rotary_theta=base,
        max_position_embeddings=max_seq_len,
    )

    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=head_dim,
        max_seq_len=max_seq_len,
        config=config,
    )

    # First point of comparison: our unfused rope
    unfused_rope = positional_embeddings.RopeEmbedding(
        head_dim=head_dim,
        config=config,
    )

    # Indexed-based comparison with unfused rope
    pos = torch.randint(0, max_seq_len, (ntokens,), dtype=torch.int32, device="cuda")
    q_ref = unfused_rope(q, pos=pos)
    k_ref = unfused_rope(k, pos=pos)
    q_fused = fused_rope(q, pos=pos)
    k_fused = fused_rope(k, pos=pos)
    torch.testing.assert_close(q_ref, q_fused, rtol=1e-3, atol=1e-3)
    torch.testing.assert_close(k_ref, k_fused, rtol=1e-3, atol=1e-3)

    # (3) Self-consistent of fused rope in fp32, fp16, bfloat
    for dtype, amax in [(torch.float16, 0.005), (torch.bfloat16, 0.05)]:
        q16 = fused_rope(q.to(dtype), pos=pos)
        k16 = fused_rope(k.to(dtype), pos=pos)
        assert ((q_fused - q16.to(torch.float32)).abs().max()) < amax
        assert ((k_fused - k16.to(torch.float32)).abs().max()) < amax


@pytest.mark.parametrize("rotary_interleave", [True, False])
def test_fused_rope_v2(rotary_interleave: bool):
    """Compare the fused rope implementation with a simple but inefficient calculation."""
    dim = 4
    seq = 1
    config = positional_embeddings.RotaryConfig(
        rotary_ratio=1.0,
        rotary_theta=1e5,
        max_position_embeddings=seq,
        rotary_interleave=rotary_interleave,
    )
    module = positional_embeddings.FusedRotaryEmbedding(dim, seq, config=config)
    module.freqs_cos = torch.tensor([[0.3, 0.4]], device="cuda")
    module.freqs_sin = torch.sqrt(1.0 - module.freqs_cos**2)
    inputs = torch.rand(1, 1, 4, device="cuda")
    output = module(inputs, pos=torch.tensor([0], dtype=torch.int32, device="cuda"))
    if rotary_interleave:
        x_1 = (
            inputs[0, 0, 0] * module.freqs_cos[0, 0]
            - inputs[0, 0, 1] * module.freqs_sin[0, 0]
        )
        y_1 = (
            inputs[0, 0, 0] * module.freqs_sin[0, 0]
            + inputs[0, 0, 1] * module.freqs_cos[0, 0]
        )
        assert (output[0, 0, 0] - x_1).abs().max().item() < 1e-5
        assert (output[0, 0, 1] - y_1).abs().max().item() < 1e-5
    else:
        x_1 = (
            inputs[0, 0, 0] * module.freqs_cos[0, 0]
            - inputs[0, 0, 2] * module.freqs_sin[0, 0]
        )
        y_1 = (
            inputs[0, 0, 0] * module.freqs_sin[0, 0]
            + inputs[0, 0, 2] * module.freqs_cos[0, 0]
        )
        assert (output[0, 0, 0] - x_1).abs().max().item() < 1e-5
        assert (output[0, 0, 2] - y_1).abs().max().item() < 1e-5


def test_fused_rope_non_contiguous():
    ntokens = 12
    queries_per_kv_head = 1
    rotary_pct = 0.75
    torch.cuda.manual_seed(1234 + ntokens + queries_per_kv_head)
    n_kv_heads = 16
    head_dim = 64
    max_seq_len = 4096
    base = 10000.0

    q_full = torch.randn(
        (ntokens, n_kv_heads, 3 * queries_per_kv_head, head_dim),
        dtype=torch.float32,
        device="cuda",
    )
    q_non_contig = q_full[:, :, :queries_per_kv_head, :]
    q_contig = q_non_contig.contiguous()
    assert not q_non_contig.is_contiguous()
    assert q_contig.is_contiguous()

    config = positional_embeddings.RotaryConfig(
        rotary_ratio=rotary_pct,
        rotary_theta=base,
        max_position_embeddings=max_seq_len,
    )

    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=head_dim,
        max_seq_len=max_seq_len,
        config=config,
    )

    pos = torch.randint(0, max_seq_len, (ntokens,), dtype=torch.int32, device="cuda")
    y = fused_rope(q_non_contig, pos=pos)
    y_contig = fused_rope(q_contig, pos=pos)
    torch.testing.assert_close(y, y_contig)


def _rotary_qkv_reference(
    rot, q, q_pos, k, kv_pos, k_dest, v, v_dest, kv_cache_idxs, qkv_scales
):
    """Reference implementation of rotary qkv."""
    assert q.ndim == 4
    assert q_pos.ndim == 1
    assert kv_pos.ndim == 1
    assert kv_cache_idxs.ndim == 1
    assert kv_cache_idxs.shape[0] == k.shape[0]
    assert q.shape[0] == q_pos.shape[0]
    assert k.shape[0] == kv_pos.shape[0]
    assert k.shape == v.shape
    assert k_dest.shape == v_dest.shape
    assert k.shape[1:] == k_dest.shape[2:]
    assert v.shape[1:] == v_dest.shape[2:]

    q = rot(x=q, pos=q_pos)
    k = rot(x=k, pos=kv_pos)
    if qkv_scales is not None:
        q = q * qkv_scales[0]
        k = k * qkv_scales[1]
        v = v * qkv_scales[2]
    assert k_dest.dtype == v_dest.dtype
    # See NOTE(fp8-uint8) in cached_attention.py.
    if k_dest.dtype == torch.float8_e4m3fn:
        k_dest.view(torch.uint8)[kv_cache_idxs, kv_pos] = k.to(
            torch.float8_e4m3fn
        ).view(torch.uint8)
        v_dest.view(torch.uint8)[kv_cache_idxs, kv_pos] = v.to(
            torch.float8_e4m3fn
        ).view(torch.uint8)
    else:
        k_dest[kv_cache_idxs, kv_pos] = k
        v_dest[kv_cache_idxs, kv_pos] = v
    return q, k, v, k_dest, v_dest


@pytest.mark.parametrize("non_contiguous_inputs", [True, False])
@pytest.mark.parametrize("n_kv_heads", [1, 8])
@pytest.mark.parametrize("queries_per_kv_head", [1, 7, 8])
@pytest.mark.parametrize(
    "input_dtype,kv_cache_dtype",
    [
        (torch.float32, torch.float32),
        (torch.bfloat16, torch.bfloat16),
        (torch.float16, torch.float16),
        (torch.bfloat16, torch.float8_e4m3fn),  # fp8 case
    ],
)
@pytest.mark.parametrize(
    "tokens, cache_idxs, pos",
    [
        pytest.param([1, 2, 3, 3], [0, 0, 0, 0], [0, 1, 2, 3], id="dummy"),
        pytest.param([10, 11], [0, 0], [0, 1], id="med"),
        pytest.param(
            [1, 2, 3, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
            id="large",
        ),
        # multi request
        pytest.param([1, 2, 3, 3], [0, 0, 1, 1], [0, 1, 0, 1], id="multi_request"),
    ],
)
@pytest.mark.parametrize(
    "qkv_scales",
    [
        None,
        torch.tensor([1.0, 1.0, 1.0], dtype=torch.float32, device="cuda"),
        torch.tensor([0.5, 2.0, 4.0], dtype=torch.float32, device="cuda"),
    ],
)
def test_compiled_rotary_qkv(
    tokens: Sequence[int],
    cache_idxs: Sequence[int],
    pos: Sequence[int],
    input_dtype: torch.dtype,
    kv_cache_dtype: torch.dtype,
    queries_per_kv_head: int,
    n_kv_heads: int,
    non_contiguous_inputs: bool,
    qkv_scales: torch.Tensor | None,
    head_dim: int = 128,
):
    """Test the compiled rotary qkv kernel."""
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)
    torch.cuda.manual_seed(1234)
    ntokens = len(tokens)
    if non_contiguous_inputs:
        qkv = torch.randn(
            (ntokens, n_kv_heads, queries_per_kv_head + 2, head_dim),
            dtype=input_dtype,
            device="cuda",
        )
        q = qkv[:, :, :queries_per_kv_head, :]
        k = qkv[:, :, queries_per_kv_head, :]
        v = qkv[:, :, queries_per_kv_head + 1, :]
        assert q.is_contiguous() is False
        assert k.is_contiguous() is False
        assert v.is_contiguous() is False
    else:
        q = torch.randn(
            (ntokens, n_kv_heads, queries_per_kv_head, head_dim),
            dtype=input_dtype,
            device="cuda",
        )
        k = torch.randn(
            (ntokens, n_kv_heads, head_dim), dtype=input_dtype, device="cuda"
        )
        v = torch.randn(
            (ntokens, n_kv_heads, head_dim), dtype=input_dtype, device="cuda"
        )
    cache_len = max(pos) + 1
    pos_tensor = torch.tensor(pos, dtype=torch.int32, device="cuda")
    num_cache_idxs = max(cache_idxs) + 1
    k_dest = torch.zeros(
        (num_cache_idxs, cache_len, n_kv_heads, head_dim),
        dtype=kv_cache_dtype,
        device="cuda",
    )
    v_dest = torch.zeros(
        (num_cache_idxs, cache_len, n_kv_heads, head_dim),
        dtype=kv_cache_dtype,
        device="cuda",
    )
    kv_cache_idxs = torch.tensor(cache_idxs, dtype=torch.int32, device="cuda")

    config = positional_embeddings.RotaryConfig(
        rotary_ratio=1.0,
        rotary_theta=2.0,
        max_position_embeddings=4096,
    )
    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=head_dim,
        max_seq_len=4096,
        config=config,
    )
    q_ref, _, v_ref, k_dest_ref, v_dest_ref = _rotary_qkv_reference(
        rot=fused_rope,
        q=q.clone(),
        q_pos=pos_tensor.clone(),
        k=k.clone(),
        kv_pos=pos_tensor.clone(),
        k_dest=k_dest.clone(),
        v=v.clone(),
        v_dest=v_dest.clone(),
        kv_cache_idxs=kv_cache_idxs.clone(),
        qkv_scales=qkv_scales,
    )
    q_out = fused_rope.rotary_qkv(
        q=q,
        pos_q=pos_tensor,
        k=k,
        pos_kv=pos_tensor,
        k_dest=k_dest,
        v=v,
        v_dest=v_dest,
        kv_cache_idxs=kv_cache_idxs,
        qkv_scales=qkv_scales,
    )
    assert q_out.is_contiguous()
    # Two things here:
    # - We double-cast q_ref so it gets converted to fp8 just like q_out.
    # - In the fp8 case, Q/K (which get rotated) can be slightly different. A "small" difference
    #   in fp8 is large in relative terms, so the tolerance need to be much looser.
    if kv_cache_dtype == torch.float8_e4m3fn:
        q_ref = q_ref.to(torch.float8_e4m3fn).to(input_dtype)
        q_out = q_out.to(input_dtype)
        qk_tol = 1e-1
    else:
        qk_tol = 1e-3
    torch.testing.assert_close(q_ref.to(q_out.dtype), q_out, atol=qk_tol, rtol=qk_tol)
    # k/v_dest comparison needs cast to input_dtype to avoid fp8 comparison (which fails).
    torch.testing.assert_close(
        k_dest_ref.to(input_dtype), k_dest.to(input_dtype), atol=qk_tol, rtol=qk_tol
    )
    # v comparison can use tighter tolerance since it should be bitwise identical even in fp8 case.
    torch.testing.assert_close(
        v_dest_ref.to(input_dtype), v_dest.to(input_dtype), atol=1e-3, rtol=1e-3
    )
    # Note: the original `v` is scaled only in `v_dest`, not here. So we manually scale for
    # comparison purposes.
    if qkv_scales is not None:
        torch.testing.assert_close(v_ref, v * qkv_scales[2], atol=1e-3, rtol=1e-3)
    else:
        torch.testing.assert_close(v_ref, v, atol=1e-3, rtol=1e-3)
    torch.cuda.synchronize()


@pytest.mark.parametrize("q_offset", [0, 1, 9, 10, 11])
@pytest.mark.parametrize("kv_offset", [0, 1, 9, 10, 11])
def test_compiled_rotary_qkv_with_different_q_pos_kv_pos(q_offset: int, kv_offset: int):
    """Test the compiled rotary qkv kernel with different pos for q and k."""
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)
    torch.cuda.manual_seed(1234)
    ntokens_q = 10
    ntokens_kv = 20
    cache_len = 4096
    assert ntokens_q + q_offset <= cache_len
    assert ntokens_kv + kv_offset <= cache_len
    q = torch.randn((ntokens_q, 1, 1, 128), dtype=torch.float32, device="cuda")
    k = torch.randn((ntokens_kv, 1, 128), dtype=torch.float32, device="cuda")
    v = torch.randn((ntokens_kv, 1, 128), dtype=torch.float32, device="cuda")
    q_pos = (
        torch.tensor(list(range(ntokens_q)), dtype=torch.int32, device="cuda")
        + q_offset
    )
    kv_pos = (
        torch.tensor(list(range(ntokens_kv)), dtype=torch.int32, device="cuda")
        + kv_offset
    )
    k_dest = torch.zeros((1, cache_len, 1, 128), dtype=torch.float32, device="cuda")
    v_dest = torch.zeros((1, cache_len, 1, 128), dtype=torch.float32, device="cuda")
    kv_cache_idxs = torch.tensor([0] * ntokens_kv, dtype=torch.int32, device="cuda")

    config = positional_embeddings.RotaryConfig(
        rotary_ratio=1.0, rotary_theta=2.0, max_position_embeddings=4096
    )
    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=128,
        max_seq_len=4096,
        config=config,
    )
    q_ref, _, _, k_dest_ref, v_dest_ref = _rotary_qkv_reference(
        rot=fused_rope,
        q=q.clone(),
        q_pos=q_pos.clone(),
        k=k.clone(),
        kv_pos=kv_pos.clone(),
        k_dest=k_dest.clone(),
        v=v.clone(),
        v_dest=v_dest.clone(),
        kv_cache_idxs=kv_cache_idxs.clone(),
        qkv_scales=None,
    )
    q_out = fused_rope.rotary_qkv(
        q=q,
        pos_q=q_pos,
        k=k,
        pos_kv=kv_pos,
        k_dest=k_dest,
        v=v,
        v_dest=v_dest,
        kv_cache_idxs=kv_cache_idxs,
        qkv_scales=None,
    )
    assert q_out.is_contiguous()
    torch.testing.assert_close(q_ref, q_out, atol=1e-3, rtol=1e-3)
    torch.testing.assert_close(v_dest_ref, v_dest, atol=1e-3, rtol=1e-3)
    torch.testing.assert_close(k_dest_ref, k_dest, atol=1e-3, rtol=1e-3)
    torch.cuda.synchronize()


@pytest.mark.parametrize("beta_slow", [1, 2])
@pytest.mark.parametrize("beta_fast", [16, 32])
@pytest.mark.parametrize("base", [1e6, 1e7, 3e7])
@pytest.mark.parametrize("original_max_seq_len", [1024])
@pytest.mark.parametrize("max_seq_len", [2048])
@pytest.mark.parametrize("dim", [10_000])
def test_yarn(
    dim: int,
    max_seq_len: int,
    original_max_seq_len: int,
    base: int,
    beta_fast: int,
    beta_slow: int,
):
    """Test the compiled rotary layer against the official YaRN implementation."""
    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=dim,
        max_seq_len=max_seq_len,
        config=positional_embeddings.RotaryConfig(
            rotary_ratio=1.0,
            rotary_theta=base,
            max_position_embeddings=max_seq_len,
            ext_config=positional_embeddings.YaRNExtensionConfig(
                unscaled_max_position_embeddings=original_max_seq_len,
                beta_fast=beta_fast,
                beta_slow=beta_slow,
                rotary_scaling_factor=max_seq_len / original_max_seq_len,
                mscale=1.0,
            ),
            rotary_interleave=False,
        ),
        device="cuda",
    )
    cos = fused_rope.freqs_cos
    sin = fused_rope.freqs_sin

    attn_factor = 1
    extrapolation_factor = 1

    yarn_ref = positional_embeddings_test_utils.LlamaDynamicYaRNScaledRotaryEmbedding(
        dim=dim,
        max_position_embeddings=max_seq_len,
        base=base,
        original_max_position_embeddings=original_max_seq_len,
        extrapolation_factor=extrapolation_factor,
        attn_factor=attn_factor,  # type: ignore
        beta_fast=beta_fast,
        beta_slow=beta_slow,
        finetuned=True,
        device="cuda",
    )

    cos_ref = yarn_ref.cos_cached[0, 0, :, : dim // 2]
    sin_ref = yarn_ref.sin_cached[0, 0, :, : dim // 2]

    assert cos.shape == cos_ref.shape
    assert sin.shape == sin_ref.shape
    assert (cos == cos_ref).all(), f"{(cos - cos_ref).abs().max():.2e}"
    assert (sin == sin_ref).all(), f"{(sin - sin_ref).abs().max():.2e}"


def test_llama3_1():
    """Test that the LLaMa 3.1 RoPE implementation matches the offcial reference."""
    head_dim = 16
    rotary_theta = 500_000
    max_position_embeddings = 131_072
    rotary_scaling_factor = 8.0  # LLaMa 3.1 official implementation hardcoded 8.0

    config = positional_embeddings.RotaryConfig(
        rotary_ratio=1.0,
        rotary_theta=rotary_theta,
        max_position_embeddings=max_position_embeddings,
        ext_config=positional_embeddings.Llama31ExtensionConfig(
            rotary_scaling_factor=rotary_scaling_factor,
        ),
    )
    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=head_dim,
        max_seq_len=max_position_embeddings,
        config=config,
    )
    cos_yarn = fused_rope.freqs_cos
    sin_yarn = fused_rope.freqs_sin

    cos_ref = torch.zeros_like(cos_yarn)
    sin_ref = torch.zeros_like(sin_yarn)

    cos_ref = [
        None,
        torch.tensor(
            [0.5403, 0.9813, 0.9993, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000],
            dtype=cos_yarn.dtype,
            device=cos_yarn.device,
        ),
    ]
    sin_ref = [
        None,
        torch.tensor(
            [
                8.4147e-01,
                1.9271e-01,
                3.7597e-02,
                7.2926e-03,
                5.2485e-04,
                3.4281e-05,
                0.0000e00,
                0.0000e00,
            ],
            dtype=sin_yarn.dtype,
            device=sin_yarn.device,
        ),
    ]

    assert torch.allclose(cos_ref[1], cos_yarn[1], atol=1e-4), (
        "cos",
        (cos_yarn[1] - cos_ref[1]).abs().max(),
    )
    assert torch.allclose(sin_ref[1], sin_yarn[1], atol=1e-4), (
        "sin",
        (sin_yarn[1] - sin_ref[1]).abs().max(),
    )


@pytest.mark.parametrize("head_dim", [512, 1024])
def test_llama3_1_against_reference(head_dim: int):
    """Test that the LLaMa 3.1 RoPE implementation matches the offcial reference."""
    rotary_theta = 500_000
    max_position_embeddings = 131_072
    rotary_scaling_factor = 8.0

    config = positional_embeddings.RotaryConfig(
        rotary_ratio=1.0,
        rotary_theta=rotary_theta,
        max_position_embeddings=max_position_embeddings,
        ext_config=positional_embeddings.Llama31ExtensionConfig(
            rotary_scaling_factor=rotary_scaling_factor,
        ),
    )
    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=head_dim,
        max_seq_len=max_position_embeddings,
        config=config,
    )
    cos_yarn = fused_rope.freqs_cos
    sin_yarn = fused_rope.freqs_sin

    freqs_cis_ref = positional_embeddings_test_utils.ref_llama3_1_precompute_freqs_cis(
        head_dim,
        max_position_embeddings,
        theta=rotary_theta,
        use_scaled=True,
    )
    cos_ref = freqs_cis_ref.real.cuda()
    sin_ref = freqs_cis_ref.imag.cuda()

    assert (cos_yarn == cos_ref).all(), f"{(cos_yarn - cos_ref).abs().max():.2e}"
    assert (sin_yarn == sin_ref).all(), f"{(sin_yarn - sin_ref).abs().max():.2e}"


@pytest.mark.parametrize("rotary_interleave", [True, False])
@pytest.mark.parametrize("rotary_scaling_factor", [1.0, 2.0])
@pytest.mark.parametrize("max_position_embeddings", [1024, 2048])
@pytest.mark.parametrize("rotary_theta", [1e5, 1e7, 3e7])
@pytest.mark.parametrize("rotary_ratio", [0.5, 1.0])
@pytest.mark.parametrize("head_dim", [1024, 2048])
def test_fused_rope_against_legacy(
    head_dim: int,
    rotary_ratio: float,
    rotary_theta: float,
    max_position_embeddings: int,
    rotary_scaling_factor: float,
    rotary_interleave: bool,
):
    """Test that the backup implementation matches the original."""
    config = positional_embeddings.RotaryConfig(
        rotary_ratio=rotary_ratio,
        rotary_theta=rotary_theta,
        max_position_embeddings=max_position_embeddings,
        rotary_interleave=rotary_interleave,
        ext_config=positional_embeddings.DeepSeekV1ExtensionConfig(
            rotary_scaling_factor=rotary_scaling_factor,
        ),
    )
    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=head_dim,
        max_seq_len=max_position_embeddings,
        config=config,
    )
    legacy_config = positional_embeddings_test_utils.LegacyRotaryConfig(
        rotary_ratio=rotary_ratio,
        rotary_theta=rotary_theta,
        max_position_embeddings=max_position_embeddings,
        rotary_scaling_factor=rotary_scaling_factor,
        rotary_interleave=rotary_interleave,
    )
    legacy_rope = positional_embeddings_test_utils.LegacyFusedRotaryEmbedding(
        head_dim=head_dim,
        config=legacy_config,
    )
    assert (fused_rope.freqs_cos == legacy_rope.freqs_cos).all()
    assert (fused_rope.freqs_sin == legacy_rope.freqs_sin).all()


def test_yarn_against_fixed_values():
    """Test the compiled rotary layer against the HF YaRN's implementation."""
    # We use exactly the same hyper-parameters as DeepSeek-Coder-V2-Lite
    # and test it against a manually runned HF's implementation.
    fused_rope = positional_embeddings.FusedRotaryEmbedding(
        head_dim=64 * 3,
        max_seq_len=5,
        config=positional_embeddings.RotaryConfig(
            rotary_ratio=1 / 3.0 + 1e-7,
            rotary_theta=10000,
            max_position_embeddings=163840,
            ext_config=positional_embeddings.YaRNExtensionConfig(
                rotary_scaling_factor=40.0,
                unscaled_max_position_embeddings=4096,
                beta_fast=32,
                beta_slow=1,
                mscale=0.707,
            ),
        ),
    )
    assert torch.allclose(
        fused_rope.freqs_cos[0],
        torch.zeros_like(fused_rope.freqs_cos[0])
        + positional_embeddings.get_yarn_temperature_scaling_factor(40.0, 0.707),
    )
    assert torch.allclose(
        fused_rope.freqs_sin[0], torch.zeros_like(fused_rope.freqs_sin[0])
    )
    assert torch.allclose(
        fused_rope.freqs_cos[1],
        torch.tensor(
            [
                0.6812,
                0.9226,
                1.0667,
                1.1504,
                1.1983,
                1.2255,
                1.2409,
                1.2496,
                1.2545,
                1.2573,
                1.2588,
                1.2598,
                1.2603,
                1.2606,
                1.2607,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
                1.2608,
            ],
            device=fused_rope.freqs_cos.device,
        ),
        rtol=1e-4,
        atol=1e-4,
    )
    assert torch.allclose(
        fused_rope.freqs_sin[1],
        torch.tensor(
            [
                1.0609e00,
                8.5931e-01,
                6.7222e-01,
                5.1606e-01,
                3.9209e-01,
                2.9619e-01,
                2.2303e-01,
                1.6763e-01,
                1.2587e-01,
                9.4458e-02,
                7.0863e-02,
                4.9167e-02,
                3.3885e-02,
                2.3170e-02,
                1.5694e-02,
                1.0508e-02,
                6.9344e-03,
                4.4910e-03,
                2.8360e-03,
                1.7279e-03,
                9.9675e-04,
                5.2322e-04,
                2.2421e-04,
                4.2033e-05,
                3.1520e-05,
                2.3637e-05,
                1.7725e-05,
                1.3292e-05,
                9.9675e-06,
                7.4746e-06,
                5.6051e-06,
                4.2033e-06,
            ],
            device=fused_rope.freqs_sin.device,
        ),
        rtol=1e-4,
        atol=1e-4,
    )
