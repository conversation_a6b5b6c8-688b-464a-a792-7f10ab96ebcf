"""Miscellaneous utilities to help users of fastforward."""

from pathlib import Path
from typing import Sequence

import torch
import torch.nn.functional as F
import yaml

from base.fastforward import batching, cached_attention, fwd, fwd_torch
from base.fastforward.checkpoints.impl.manifest import CkptManifest


def get_model_spec_from_neox_checkpoint(checkpoint_path: Path | str) -> fwd.ModelSpec:
    """Get a ModelSpec from a NeoXCheckpoint YAML."""
    if isinstance(checkpoint_path, str):
        checkpoint_path = Path(checkpoint_path)

    with (checkpoint_path.parent / "config.yml").open() as fstream:
        cfg = yaml.safe_load(fstream)
        cfg = {key.replace("-", "_"): value for key, value in cfg.items()}

    norm_type = cfg.get("norm", "layernorm")
    if norm_type not in ["layernorm", "rmsnorm", "scalenorm"]:
        raise ValueError(f"Unsupported norm type: {norm_type}")
    if norm_type == "layernorm":
        norm_eps = cfg.get("layernorm_epsilon", 1e-5)
    elif norm_type == "rmsnorm":
        norm_eps = cfg.get("rms_norm_epsilon", 1e-5)
    elif norm_type == "scalenorm":
        norm_eps = cfg.get("scalenorm_epsilon", 1e-8)
    else:
        assert False
    return fwd.ModelSpec(
        name=checkpoint_path.parent.name,
        checkpoint_path=str(checkpoint_path),
        vocab_size=cfg["make_vocab_size_divisible_by"],
        num_layers=cfg["num_layers"],
        num_heads=cfg["num_attention_heads"],
        emb_dim=cfg["hidden_size"],
        head_dim=cfg["hidden_size"] // cfg["num_attention_heads"],
        rotary_pct=cfg.get("rotary_pct", 0.0),
        max_position_embeddings=cfg.get("max_position_embeddings", 8192),
        unscaled_max_position_embeddings=cfg.get("max_position_embeddings", 8192),
        norm_eps=norm_eps,
    )


class PaddedStepFunction:
    """Wraps around `step` to ensure it is always called with one of `round_sizes`.

    - Does not handle arbitrarily long sequences.
    - Only compatible with `BasicAttention`.
    """

    def __init__(self, step_fn: fwd.ForwardStepFn, round_sizes: Sequence[int]):
        self.step_fn = step_fn
        self.round_sizes = sorted(round_sizes)

    def __call__(
        self, tokens: Sequence[int], attn: cached_attention.Attention
    ) -> fwd.ModelOutput:
        if not isinstance(attn, cached_attention.BasicAttention):
            raise ValueError(f"Only supports BasicAttention. Got {type(attn)} instead.")
        batch_size = batching.choose_round_size(self.round_sizes, len(tokens))
        padding = batch_size - len(tokens)
        padded_tokens = list(tokens)
        padded_tokens.extend([0] * padding)
        logits = self.step_fn(padded_tokens, attn).checked_cast(torch.Tensor)
        logits = logits[: len(tokens)]
        attn.reset(by_positions=padding)
        return fwd_torch.TorchLogits2D(logits)


def pad_and_step(
    step: fwd.ForwardStepFn,
    round_sizes: Sequence[int],
) -> fwd.ForwardStepFn:
    """Wraps around `step` to ensure it is always called with one of `round_sizes`."""
    round_sizes = sorted(round_sizes)
    max_round_size = max(round_sizes)

    padded_step = PaddedStepFunction(step, round_sizes)

    def step_fn(
        tokens: Sequence[int], attn: cached_attention.Attention
    ) -> fwd.ModelOutput:
        start = 0
        num_tokens = len(tokens)
        combined_logits = []
        while start < num_tokens:
            remaining_length = num_tokens - start
            round_size = min(max_round_size, remaining_length)
            logits = padded_step(tokens[start : start + round_size], attn).checked_cast(
                torch.Tensor
            )
            assert logits.shape[0] == round_size
            combined_logits.append(logits)
            start += round_size
        assert start == num_tokens
        return fwd_torch.TorchLogits2D(torch.cat(combined_logits, dim=0))

    return step_fn


def generate(
    step: fwd.ForwardStepFn,
    attn: cached_attention.BasicAttention,
    prefix: list[int],
    max_tokens: int,
) -> tuple[list[int], float]:
    attn.reset()
    score, output = 0.0, []
    for _ in range(max_tokens):
        logits = step(prefix, attn).checked_cast(torch.Tensor)
        log_probs = logits[-1].log_softmax(dim=-1)
        next_token = int(log_probs.argmax().item())
        output.append(int(next_token))
        score += log_probs[next_token].item()
        prefix = [next_token]
    return output, score


def log_perplexity(
    step: fwd.ForwardStepFn, attn: cached_attention.BasicAttention, input_: list[int]
) -> float:
    """Compute the log perplexity of the input."""
    attn.reset()
    logits = step(input_[:-1], attn).checked_cast(torch.Tensor)
    # Logits represent next-token probabilities, so skip we have to first token.
    input_as_tensor = torch.tensor(
        input_, dtype=torch.long, device="cuda"
    )  # dtype long is needed for cross_entropy for some reason
    return F.cross_entropy(logits, input_as_tensor[1:]).item()


def log_perplexity_continuation(
    step: fwd.ForwardStepFn,
    attn: cached_attention.BasicAttention,
    input_: list[int],
    target: list[int],
) -> float:
    """Compute the log perplexity of the target conditioned in the input."""
    attn.reset()
    logits = step(input_ + target, attn).checked_cast(torch.Tensor)
    target_logits = logits[len(input_) - 1 :]
    target_tensor = torch.tensor(
        target, dtype=torch.long, device="cuda"
    )  # dtype long is needed for cross_entropy for some reason
    return F.cross_entropy(target_logits, target_tensor).item()


def get_embeddings(
    step: fwd.ForwardStepFn,
    attn: cached_attention.BasicAttention,
    input_: list[int],
    embedding_token_ids: list[int],
):
    """Get an embedding corresponding to a given embedding token id."""
    attn.reset()
    logits = step(input_, attn).checked_cast(torch.Tensor)

    for embedding_token_id in embedding_token_ids:
        if embedding_token_id in input_:
            return logits[input_.index(embedding_token_id)]

    raise ValueError("Couldn't find any embedding tokens in the input.")


def get_checkpoint_sha(checkpoint_path: Path) -> str:
    """Obtain the SHA256 of a checkpoint."""
    ckp = CkptManifest.load_from_path(checkpoint_path)
    if ckp is None:
        raise ValueError(f"No checkpoint found at {checkpoint_path}")
    sha = ckp.to_dataclass().manifestSha256
    if sha is None:
        raise ValueError(f"No SHA256 found for checkpoint at {checkpoint_path}")
    return sha
