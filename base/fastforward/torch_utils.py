"""Miscellaneous utilities used across fastforward for torch."""

import time
from typing import Any, Callable, NamedTuple, Type, TypeVar

import torch

ModuleT = TypeVar("ModuleT", bound=torch.nn.Module)


def cuda_timeit(
    op: Callable[[], Any],
    repeats: int = 16,
    warmup_steps: int = 1,
    inner_steps: int = 1,
) -> tuple[float, list[float]]:
    """Profile a CUDA operation.

    This utility function takes care of warmup and synchronoizing cuda before and after.

    Args:
        op: An operation to profile.
        repeats: How many times to repeat `op` for measurements.
        warmup_steps: How many times to run `op` to warmup.
        inner_steps: How many times to run `op` in a single cuda synchronize to
          amortize kerneling scheduling time.

    Returns:
        The median latency in ms, a list of measured latency times.
    """
    latency_ms = []

    # warmup
    for _ in range(warmup_steps):
        op()

    for _ in range(repeats):
        torch.cuda.synchronize()
        start_time = time.time()
        for _ in range(inner_steps):
            op()
        torch.cuda.synchronize()
        step_time_ms = (time.time() - start_time) * 1e3 / inner_steps
        latency_ms.append(step_time_ms)

    latency_ms.sort()
    median_latency_ms = latency_ms[len(latency_ms) // 2]
    return median_latency_ms, latency_ms


def _load_state_dict(
    module: torch.nn.Module,
    state_dict: dict[str, torch.Tensor],
):
    """Version of torch's `load_state_dict` that assigns weights instead of copying.

    TODO(arun): this function should be replaced with `torch.nn.Module.load_state_dict`
    when we upgrade to torch 2.1.

    NOTE(arun): Because this function is effectively deprecated as it is being written,
        we are not implementing all the load_state_dict features including:
            - load_state_dict_pre_hooks.
            - enforcing that persistent buffers are set.
    """

    class _IncompatibleKeys(NamedTuple):
        missing_keys: set[str]
        unexpected_keys: set[str]

    incompatible_keys = _IncompatibleKeys(set(), set(state_dict.keys()))

    def _load_state_dict_rec(module: torch.nn.Module, prefix: str = ""):
        local_keys = {
            key[len(prefix) :] for key in state_dict.keys() if key.startswith(prefix)
        }
        local_incompatible_keys = _IncompatibleKeys(set(), local_keys)

        for key, value in module.named_buffers(recurse=False):
            if (key_ := prefix + key) not in state_dict:
                # NOTE(arun): some buffers can be non-persistent, so we don't enforce
                # their presence here.
                continue
            local_incompatible_keys.unexpected_keys.remove(key)
            incompatible_keys.unexpected_keys.remove(key_)
            value.data = state_dict[key_]
        for key, value in module.named_parameters(recurse=False):
            if (key_ := prefix + key) not in state_dict:
                incompatible_keys.missing_keys.add(key_)
                local_incompatible_keys.missing_keys.add(key)
                continue
            local_incompatible_keys.unexpected_keys.remove(key)
            incompatible_keys.unexpected_keys.remove(key_)
            value.data = state_dict[key_]

        for name, child in module.named_children():
            _load_state_dict_rec(child, f"{prefix}{name}.")

        # pylint:disable-next=protected-access
        for hook in module._load_state_dict_post_hooks.values():
            hook(module, local_incompatible_keys)

    _load_state_dict_rec(module)
    if incompatible_keys.unexpected_keys or incompatible_keys.missing_keys:
        raise ValueError(f"Inconsistent loading of state dict: {incompatible_keys}")


def init_with_weights(
    module_cls: Type[ModuleT], state_dict: dict[str, torch.Tensor], *args, **kwargs
) -> ModuleT:
    """Initialize a model with its weights.

    This utility wraps around the `torch.nn.utils.init.skip_init` + `load_state_dict`
    torch pattern to avoid unnecessary memory allocation or initialization.
    `skip_init` should never be used outside of this function: it invokes spooky PyTorch
    magic that our codebase is better off without.
    """
    module: ModuleT = torch.nn.utils.init.skip_init(module_cls, *args, **kwargs)  # type: ignore
    # TODO(arun): The below works in PyTorch 2.1.
    # module.load_state_dict(state_dict, assign=True)
    _load_state_dict(module, state_dict)
    return module


def shard(
    tensor: torch.Tensor, axis: int, process_idx: int, num_processes: int
) -> torch.Tensor:
    """Shard a tensor along an axis.

    Args:
        tensor: The tensor to shard.
        axis: The axis to shard along.
        process_idx: The index of the process.
        num_processes: The number of processes.

    Returns:
        The sharded tensor, with dimensions identical to `tensor` but with the
        dimension at `axis` reduced to `tensor.shape[axis] // `num_processes`.
    """
    assert num_processes > 0
    s = tensor.shape
    if num_processes == 1:
        return tensor
    shard_size, remainder = divmod(s[axis], num_processes)
    assert remainder == 0
    # Exploits that torch.split creates views of the original tensor.
    return torch.split(tensor, split_size_or_sections=shard_size, dim=axis)[process_idx]


def index_in_second_dim(x: torch.Tensor, indices: torch.Tensor):
    assert x.ndim == 2, f"Unexpected tensor shape: {x.shape}"
    assert x.is_contiguous()
    assert (
        indices.ndim == 1
    ), f"Unexpected indices shape: {indices.shape} (x.shape={x.shape})"
    assert indices.dtype in (torch.int32, torch.int64)
    stride = x.stride(0)
    flat_indices = (
        torch.arange(indices.shape[0], device=indices.device) * stride + indices
    )
    flat_x = x.view(-1)
    return flat_x[flat_indices]


class ProfilingContext:
    """A context manager to include informations for profiling.

    This class is useful to annotate blocks of code with information for profiling.
    Currently, we only include torch.cuda.nvtx information, but more can be added.

    Usage:
        with ProfilingContext(name="llama_model"):
            run_llama()
    """

    def __init__(self, name: str):
        self._name = name

    def __enter__(self):
        torch.cuda.nvtx.range_push(self._name)

    def __exit__(self, exc_type, exc_val, exc_tb):
        torch.cuda.nvtx.range_pop()
