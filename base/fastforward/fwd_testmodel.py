"""A dummy language model designed for testing.

This model includes consistency checks on its state.
"""

import hashlib
import logging
from typing import Sequence

import torch

from base.fastforward import all_reduce, cached_attention, fwd, fwd_torch
from base.fastforward.parallel import ParallelContext

Tensor = torch.Tensor


# TODO(hieu): change all the `is_cpu` block in this file into `assert`
# after fixing the default device issues in continuous_batching_inference_runner.py.
#
# Currently, some tensors in runner.infer() are defaulted to `device="cuda"`. They
# need to be on the default device, or `testmodel` is not doing its correct job.


def get_dummy_modelspec() -> fwd.ModelSpec:
    return fwd.ModelSpec(
        name="dummy",
        checkpoint_path="dummy",
        num_layers=1,
        vocab_size=1000,
        emb_dim=1,
        num_heads=1,
        head_dim=1,
        rotary_pct=0,
    )


class TestAttentionState(cached_attention.MultiCacheAttention):
    """Records previous calls and checks for consistency.

    Consistency here means that the history (starting from a specified point)
    needs to be exactly what the model would have produced.

    This check is fast in the test model, but not in the real model. So we
    can do it here in every generation step.

    We chose a vocab size of 1000 to keep the output readable while making it
    unlikely that we see collisions (a token might be generated right
    accidentally, and we want to know early if something failed).
    """

    def __init__(
        self, max_len: int, vocab_size: int, ignore_first_n: int, num_caches: int = 1
    ):
        self.vocab_size = vocab_size
        self.max_len = max_len
        self.ignore_first_n = ignore_first_n
        self._num_caches = num_caches

        self._history: list[str] = []
        self._tokens: list[list[int]] = [[] for _ in range(num_caches + 1)]

        assert self.ignore_first_n > 0

    @property
    def num_caches(self) -> int:
        return self._num_caches

    @property
    def padding_cache_idx(self) -> int:
        return self._num_caches

    @property
    def padding_cache_pos(self) -> int:
        return 0

    def get_device_idx_from_process_idx(self, process_idx: int | None) -> int:
        assert not process_idx, process_idx
        return 0

    def get_device_from_process_idx(self, process_idx: int | None) -> torch.device:
        assert not process_idx, process_idx
        return torch.device("cpu")

    def get_devices(self) -> Sequence[torch.device]:
        return (torch.device("cpu"),)

    @property
    def history(self) -> list[str]:
        return list(self._history)

    def __call__(
        self,
        qkv: Tensor,
        cache_idxs: Tensor,
        cache_pos: Tensor,
        layer_idx: int,
        cumulative_round_pos: torch.Tensor | None = None,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: Tensor | None = None,
    ) -> Tensor:
        """We assume qkv to be a tensor of size seq_len, which is the token."""
        assert parallel_ctx.process_idx == 0, parallel_ctx.process_idx
        assert parallel_ctx.cfg.num_processes == 1, parallel_ctx.cfg.num_processes

        if not qkv.is_cpu:
            print(f"WARNING: {qkv.device=}, but expecting CPUs.", flush=True)
            qkv = qkv.cpu()

        assert qkv.dtype == torch.int32, qkv.dtype
        assert qkv.ndim == 2
        assert qkv.numel() == cache_idxs.numel()

        qkv_list = qkv.squeeze(1).numpy().tolist()
        cache_idxs_list: list[int] = cache_idxs.numpy().tolist()
        self._history.append(
            f"__call__({qkv=}, {cache_idxs=}, {cache_pos=}, {layer_idx=})"
        )

        num_tokens = len(qkv)
        next_tokens = []
        for i in range(num_tokens):
            if cache_idxs_list[i] == self.padding_cache_idx:
                next_tokens.append(0)
                continue
            self._tokens[cache_idxs_list[i]].append(qkv_list[i])
            next_tokens.append(
                qkv_list[i + 1]
                if i + 1 < num_tokens and cache_idxs_list[i + 1] == cache_idxs_list[i]
                else self.next_token(self._tokens[cache_idxs_list[i]])
            )

        if max([len(t) for t in self._tokens]) > self.max_len:
            logging.error(
                "Maxlen exceeded: %s > %s",
                max([len(t) for t in self._tokens]),
                self.max_len,
            )
            raise ValueError("Maxlen exceeded.")

        self.validate()  # validates the entire sequence; O(n^2) runtime
        next_tokens = torch.tensor(next_tokens, dtype=torch.int32).unsqueeze(1)
        return next_tokens

    def register_tokens_get_positions(
        self, tokens: Tensor, cache_idxs: Tensor, process_idx: int
    ) -> Tensor:
        if not tokens.is_cpu:
            print(f"WARNING: {tokens.device=}, but expecting CPUs.", flush=True)
            tokens = tokens.cpu()

        if not cache_idxs.is_cpu:
            print(f"WARNING: {cache_idxs.device=}, but expecting CPUs.", flush=True)
            cache_idxs = cache_idxs.cpu()

        cache_idxs = cache_idxs.numpy().tolist()
        tokens = tokens.numpy().tolist()
        assert len(cache_idxs) == len(tokens), f"{len(cache_idxs)=} != {len(tokens)=}"
        self._history.append(
            f"register_tokens_get_positions({tokens=}, {cache_idxs=}, {process_idx=})"
        )

        positions = []
        idx_cnts = [len(t) for t in self._tokens]
        for cache_idx in cache_idxs:
            if cache_idx == self.padding_cache_idx:
                positions.append(self.padding_cache_pos)
            else:
                positions.append(idx_cnts[cache_idx])
                idx_cnts[cache_idx] += 1
        return torch.tensor(positions, dtype=torch.int32)

    def get_fill_length(self, cache_idx: int, process_idx: int) -> Tensor:
        del process_idx
        return torch.tensor([len(self._tokens[cache_idx])], dtype=torch.int32)

    def reset(
        self,
        *,
        cache_idx: int,
        to_position: int | None = None,
        by_positions: int | None = None,
    ):
        self._history.append(f"reset({to_position=}, {by_positions=}, {cache_idx=})")
        if to_position is None and by_positions is None:
            to_position = 0
        if to_position is not None and by_positions is not None:
            raise ValueError(
                f"Only one of to_position and by_positions can be specified. "
                f"Got to_position={to_position} and by_positions={by_positions}."
            )
        if by_positions is not None:
            if by_positions < 0:
                raise ValueError(f"{by_positions=} < 0")
            current_position = int(self.get_fill_length(cache_idx=0, process_idx=0))
            to_position = current_position - by_positions
        assert to_position is not None
        assert to_position <= len(
            self._tokens[cache_idx]
        ), f"{to_position=} but {len(self._tokens[cache_idx])=}"
        self._tokens[cache_idx] = self._tokens[cache_idx][:to_position]

    def get_tokens(self, cache_idx: int = 0) -> Tensor:
        tokens = torch.tensor(self._tokens[cache_idx], dtype=torch.int32)
        return tokens

    def next_token(self, x: list[int]) -> int:
        h = hashlib.sha256(str(x).encode())
        return (
            int.from_bytes(h.digest(), byteorder="big", signed=False) % self.vocab_size
        )

    def validate(self):
        for cache_idx in range(self._num_caches):
            self._validate_for_cache(cache_idx)

    def _validate_for_cache(self, cache_idx: int):
        """Checks whether the sequence is correct."""
        tokens = self._tokens[cache_idx]

        if len(tokens) <= self.ignore_first_n:
            return  # nothing to check

        for idx in range(self.ignore_first_n, len(tokens)):
            tokens_so_far = tokens[:idx]
            next_token = self.next_token(tokens_so_far)
            if next_token != tokens[idx]:
                raise ValueError(
                    f"Validation for {cache_idx=} failed at {idx=}:\n"
                    f"  {next_token=} != {tokens[idx]=}.\n"
                    f"  {self=}"
                )

    def __repr__(self) -> str:
        attrs = ", ".join(
            [
                f"{self.vocab_size=}",
                f"{self.max_len=}",
                f"{self.ignore_first_n=}",
                f"{self._tokens=}",
            ]
        )
        history = "\n".join(self._history)
        return f"TestAttentionState({attrs})\nHistory:\n{history}"

    def get_tensors_for_cuda_graph(
        self, process_idx: int | None = None
    ) -> dict[str, Tensor]:
        raise RuntimeError(
            "This method should only be used during CUDA-graph capturing, but this "
            "class should never be captured in a CUDA-graph. Reaching this line is a bug."
        )

    def get_id(self) -> str:
        # TestAttentionState is not supported with parallel execution.
        raise NotImplementedError

    @property
    def captured_attn_maxes(self) -> dict[int, Tensor]:
        raise NotImplementedError

    def get_max_requests_in_round(self) -> int | None:
        return None

    def get_small_request_max_seqlen(self) -> int | None:
        return None


class TestModelAttentionFactory(fwd.AttentionFactory):
    """Creates TestAttentionState objects."""

    def __init__(
        self,
        model_spec: fwd.ModelSpec,
        num_processes: int = 1,
        ignore_first_n: int = 1,
    ):
        del num_processes
        self.model_spec = model_spec
        self.ignore_first_n = ignore_first_n

    def create_cache_pool(
        self, max_length: int, num_attention_caches: int
    ) -> cached_attention.MultiCacheAttention:
        return TestAttentionState(
            max_len=max_length,
            vocab_size=self.model_spec.vocab_size,
            ignore_first_n=self.ignore_first_n,
            num_caches=num_attention_caches,
        )


def get_attention_factory(
    model_spec: fwd.ModelSpec, num_processes: int = 1, ignore_first_n: int = 1
) -> fwd.AttentionFactory:
    return TestModelAttentionFactory(
        model_spec=model_spec,
        num_processes=num_processes,
        ignore_first_n=ignore_first_n,
    )


class TestStepFn:
    """Implements a `fwd.ForwardStepFn`."""

    def __init__(self, vocab_size: int):
        self.vocab_size = vocab_size

    def __call__(
        self,
        tokens: Sequence[int],
        attn: cached_attention.Attention,
    ) -> fwd.ModelOutput:
        """Returning the logits which would produce `[tokens[1:], next_token]`.

        Args:
            tokens: an int32 torch Tensor.
            attn: a dummy attention module responsible for generating `next_token`.
        """

        tokens_tensor = torch.tensor(tokens, dtype=torch.int32, device="cpu")

        if not tokens_tensor.is_cpu:
            print(f"WARNING: {tokens_tensor.device=}, but expecting CPUs.", flush=True)
            tokens_tensor = tokens_tensor.cpu()

        num_tokens = tokens_tensor.numel()

        attn.register_tokens_get_positions(tokens_tensor, process_idx=0)

        next_tokens = attn(tokens_tensor[:, None], layer_idx=0).reshape(-1, 1)
        assert (
            next_tokens.numel() == num_tokens
        ), f"{next_tokens=} so {next_tokens.numel()=} but {num_tokens=}."

        logits = (
            (torch.arange(self.vocab_size)[None, :] == next_tokens)
            .to(torch.float16)
            .mul_(100.0)
        )

        return fwd_torch.TorchLogits2D(logits)


def generate_step_fn(
    model_spec: fwd.ModelSpec, num_processes: int = 1, auto_capture_graphs: bool = True
) -> fwd.ForwardStepFn:
    del auto_capture_graphs
    assert num_processes > 0
    step_fn: fwd.ForwardStepFn = TestStepFn(model_spec.vocab_size)
    return step_fn
