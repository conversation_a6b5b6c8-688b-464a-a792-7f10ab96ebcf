#include <torch/extension.h>

namespace compiled_rotary_qkv {

// Forward declaration.
torch::Tensor rotary_embed_qkv_cuda(torch::Tensor& q, torch::Tensor& q_pos_idxs, torch::Tensor& k,
                                    torch::Tensor& v, torch::Tensor& freqs_cos,
                                    torch::Tensor& freqs_sin, torch::Tensor& kv_pos_idxs,
                                    torch::Tensor& k_dest, torch::Tensor& v_dest,
                                    torch::Tensor& kv_cache_idxs, torch::Tensor& qkv_scales);

// Performs RoPE with position indices on `q` and `k` given pre-computed cos and sin
// tables. Also copies `k` and `v` into their destination tensors. Note that `v` is never
// rotated.
// Arguments:
//   q: query tensor of shape (ntokens_q, kv_heads, query_heads_per_kv, headdim) or (ntokens_q,
//      kv_heads, headdim).
//   q_pos_idxs: Sequence indices of shape (ntokens_q,) for each element of `q`. Each
//               index must lie in the range [0, max_seqlen).
//   k: key tensor of shape (ntokens_kv, kv_heads, headdim).
//   v: value tensor of shape (ntokens_kv, kv_heads, headdim).
//   freqs_{cos, sin}: tables of shape (max_seqlen, ropedim //
//                     2).
//      NOTE: in the common case, headdim == ropedim. But if you want to
//      pass-through some of the activations, set ropedim < headdim.
//   kv_pos_idxs: Sequence indices of shape (ntokens_kv,) for each element of `k` and `v`. Each
//                index must lie in the range [0, max_seqlen).
//   k_dest: key cache tensor of shape (ncaches, cache_size, kv_heads, headdim).
//   v_dest: value cache tensor of shape (ncaches, cache_size, kv_heads, headdim).
//   kv_cache_idxs: Indices of shape (ntokens_kv,) in the range [0, ncaches). Used to index into
//                 `k_dest` and `v_dest`.
//   qkv_scales: 3-element tensor with fp8 scales for Q/K/V, respectively. *Must* be a tensor of
//               all-ones if you don't want/need fp8 scaling.
//
// Returns:
//   Result tensor of shape (ntokens_q, kv_heads, [query_heads_per_kv,] headdim) where
//   query_heads_per_kv is missing if `q` has one head dimension.
//   The results for the key and value are written into their destination tensors.
// Constraints:
//  - All inputs must live on a CUDA device.
//  - All inputs must be contiguous except for `q`, `k`, and `v`.
//  - `q`, `k`, and `v` are any floating point type.
//  - `freqs_cos` and `freqs_sin` are both float32.
//  - `kv_pos_idxs` and `kv_cache_idxs` are int32.
torch::Tensor rotary_embed_qkv(torch::Tensor& q, torch::Tensor& q_pos_idxs, torch::Tensor& k,
                               torch::Tensor& v, torch::Tensor& freqs_cos, torch::Tensor& freqs_sin,
                               torch::Tensor& kv_pos_idxs, torch::Tensor& k_dest,
                               torch::Tensor& v_dest, torch::Tensor& kv_cache_idxs,
                               torch::Tensor& qkv_scales) {
    // check shapes
    TORCH_CHECK(q.dim() == 3 || q.dim() == 4, "q must have one or two head dimensions.");
    TORCH_CHECK(k.dim() == 3, "k must have one head dimension.");
    TORCH_CHECK(v.dim() == 3, "v must have one head dimension.");
    TORCH_CHECK(k_dest.dim() == 4, "k_dest must have two head dimensions.");
    TORCH_CHECK(v_dest.dim() == 4, "v_dest must have two head dimensions.");
    TORCH_CHECK(k.size(0) == v.size(0), "k and v must have the same number of tokens.");
    TORCH_CHECK(q.size(1) == k.size(1), "q and k must have the same number of kv heads.");
    TORCH_CHECK(q.size(1) == v.size(1), "q and v must have the same number of kv heads.");
    TORCH_CHECK(q.size(3) == k.size(2), "q and k must have the same head dimension.");
    TORCH_CHECK(q.size(3) == v.size(2), "q and v must have the same head dimension.");
    TORCH_CHECK(kv_cache_idxs.dim() == 1, "kv_cache_idxs must be a 1-dimensional tensor.");
    TORCH_CHECK(kv_pos_idxs.dim() == 1, "kv_pos_idxs must be a 1-dimensional tensor.");
    TORCH_CHECK(kv_cache_idxs.size(0) == kv_pos_idxs.size(0),
                "kv_cache_idxs and kv_pos_idxs must have the same number of positions.");
    TORCH_CHECK(k.size(0) == kv_cache_idxs.size(0),
                "k and kv_cache_idxs must have the same number of positions.");
    TORCH_CHECK(q_pos_idxs.dim() == 1, "q_pos_idxs must be a 1-dimensional tensor.");
    TORCH_CHECK(q_pos_idxs.size(0) == q.size(0),
                "q_pos_idxs and q must have the same number of positions.");
    TORCH_CHECK(qkv_scales.numel() == 3, "qkv_scales must have 3 elements.");

    // All inputs must live on a CUDA device.
    TORCH_CHECK(q.device().is_cuda(), "q must live on a CUDA device.");
    TORCH_CHECK(k.device().is_cuda(), "k must live on a CUDA device.");
    TORCH_CHECK(v.device().is_cuda(), "v must live on a CUDA device.");
    TORCH_CHECK(freqs_cos.device().is_cuda(), "freqs_cos must live on a CUDA device.");
    TORCH_CHECK(freqs_sin.device().is_cuda(), "freqs_sin must live on a CUDA device.");
    TORCH_CHECK(kv_pos_idxs.device().is_cuda(), "kv_pos_idxs must live on a CUDA device.");
    TORCH_CHECK(q_pos_idxs.device().is_cuda(), "q_pos_idxs must live on a CUDA device.");
    TORCH_CHECK(k_dest.device().is_cuda(), "k_dest must live on a CUDA device.");
    TORCH_CHECK(v_dest.device().is_cuda(), "v_dest must live on a CUDA device.");
    TORCH_CHECK(kv_cache_idxs.device().is_cuda(), "kv_cache_idxs must live on a CUDA device.");
    TORCH_CHECK(qkv_scales.device().is_cuda(), "qkv_scales must live on a CUDA device.");

    // check dtypes
    // `q`, `k`, and `v` are any floating point type.
    TORCH_CHECK(q.dtype() == k.dtype() && q.dtype() == v.dtype(),
                "q, k, and v must have the same dtype.");
    TORCH_CHECK(freqs_cos.dtype() == torch::kFloat32, "freqs_cos must be float32.");
    TORCH_CHECK(freqs_sin.dtype() == torch::kFloat32, "freqs_sin must be float32.");
    TORCH_CHECK(kv_pos_idxs.dtype() == torch::kInt32, "kv_pos_idxs must be int32.");
    TORCH_CHECK(q_pos_idxs.dtype() == torch::kInt32, "q_pos_idxs must be int32.");
    TORCH_CHECK(kv_cache_idxs.dtype() == torch::kInt32, "kv_cache_idxs must be int32.");
    if (k_dest.dtype() == torch::kFloat8_e4m3fn) {
        // fp8 case
        TORCH_CHECK(v_dest.dtype() == torch::kFloat8_e4m3fn, "v_dest must be float8_e4m3fn.");
        TORCH_CHECK(k.dtype() == v.dtype(), "k and v must have the same dtype.");
    } else {
        TORCH_CHECK(k_dest.dtype() == k.dtype(), "k_dest must have the same dtype as k.");
        TORCH_CHECK(v_dest.dtype() == v.dtype(), "v_dest must have the same dtype as v.");
    }
    TORCH_CHECK(qkv_scales.dtype() == torch::kFloat32, "qkv_scales must be float32.");

    // check contiguity
    TORCH_CHECK(freqs_cos.is_contiguous(), "freqs_cos must be contiguous.");
    TORCH_CHECK(freqs_sin.is_contiguous(), "freqs_sin must be contiguous.");
    TORCH_CHECK(kv_pos_idxs.is_contiguous(), "kv_pos_idxs must be contiguous.");
    TORCH_CHECK(q_pos_idxs.is_contiguous(), "q_pos_idxs must be contiguous.");
    TORCH_CHECK(k_dest.is_contiguous(), "k_dest must be contiguous.");
    TORCH_CHECK(v_dest.is_contiguous(), "v_dest must be contiguous.");
    TORCH_CHECK(kv_cache_idxs.is_contiguous(), "kv_cache_idxs must be contiguous.");
    TORCH_CHECK(qkv_scales.is_contiguous(), "qkv_scales must be contiguous.");
    // q, k, and v don't have to be contiguous

    return rotary_embed_qkv_cuda(q, q_pos_idxs, k, v, freqs_cos, freqs_sin, kv_pos_idxs, k_dest,
                                 v_dest, kv_cache_idxs, qkv_scales);
}

}  // namespace compiled_rotary_qkv

PYBIND11_MODULE(compiled_rotary_qkv, m) {
    m.def("rotary_embed_qkv", &compiled_rotary_qkv::rotary_embed_qkv, "Rotary embed.");
}
