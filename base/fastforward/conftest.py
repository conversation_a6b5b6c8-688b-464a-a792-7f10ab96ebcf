"""conftest.py is a special file that pytest will automatically load."""

from typing import Sequence

import pytest

from base.fastforward import fwd, fwd_utils, fwd_testmodel
from base.fastforward.cached_attention import AttentionImpl
from base.fastforward.starcoder import fwd_starcoder


def pytest_addoption(parser):
    parser.addoption(
        "--enable-h100-kernels",
        action="store_true",
        help="use H100-specific kernels (if relevant)",
        default=False,
    )


@pytest.fixture(scope="session")
def enable_h100_kernels(request) -> bool:
    return request.config.getoption("--enable-h100-kernels")


@pytest.fixture
def testmodel_fixture():
    ms = fwd_testmodel.get_dummy_modelspec()
    step_fn = fwd_testmodel.generate_step_fn(ms)
    max_len = 128
    attn_factory = fwd_testmodel.get_attention_factory(ms, ignore_first_n=max_len)
    return step_fn, attn_factory, max_len


def load_starcoder_1b_fp16(
    use_parallel: bool = False,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    attention_impl: AttentionImpl = AttentionImpl.BATCHED_FLASH,
    use_fused_kernels: bool = False,
) -> tuple[fwd.ForwardStepFn, fwd.AttentionFactory]:
    """Creates the forward and attention factory for starcoderbase 1B."""
    model_spec = fwd_utils.get_model_spec_from_neox_checkpoint(
        "/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint_v2"
    )
    model_spec.checkpoint_sha256 = (
        "8a26c397f2c233ec1c0cac92e66b85def17f8322bc0f53f84c6233fc57c6a220"
    )

    num_processes = 2 if use_parallel else 1
    return (
        fwd_starcoder.generate_step_fn(
            model_spec,
            num_processes=num_processes,
            auto_capture_graphs=auto_capture_graphs,
            batch_sizes=batch_sizes,
        ),
        fwd_starcoder.StarcoderAttentionFactory(
            model_spec,
            num_processes=num_processes,
            attention_impl=attention_impl,
            pre_attention_kernel_fusion=use_fused_kernels,
            use_register_tokens_kernel=use_fused_kernels,
        ),
    )


@pytest.fixture(
    scope="module",
    params=[
        (AttentionImpl.BATCHED_FLASH, False),
        (AttentionImpl.MULTI_REQUEST_FLASH, True),
    ],
)
def starcoder_1b_fp16_fixture(request):
    """Creates the forward and attention factory for starcoderbase 1B."""
    yield load_starcoder_1b_fp16(
        auto_capture_graphs=False,
        attention_impl=request.param[0],
        use_fused_kernels=request.param[1],
    )


@pytest.fixture(
    scope="module",
    params=[
        (AttentionImpl.BATCHED_FLASH, False),
        (AttentionImpl.MULTI_REQUEST_FLASH, True),
    ],
)
def starcoder_1b_fp16_graphed_fixture(request):
    """Creates the forward and attention factory for starcoderbase 1B."""
    batch_sizes = [8, 16, 32]
    step_fn, attn_factory = load_starcoder_1b_fp16(
        auto_capture_graphs=True,
        batch_sizes=batch_sizes,
        attention_impl=request.param[0],
        use_fused_kernels=request.param[1],
    )
    yield step_fn, attn_factory, batch_sizes
