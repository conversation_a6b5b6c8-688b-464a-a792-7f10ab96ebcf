#include <c10/cuda/CUDAStream.h>
#include <cuda.h>
#include <torch/extension.h>

namespace compiled_attention_utils {

#define HEAD_GROUP_SIZE 8

/*
 * This kernel copies queries from `q_src` to `qbuf_dst` and vice versa.
 *
 * Arguments:
 * - q_src: (round_size, nheads, headdim)
 * - qbuf_dst: (nreqs, buf_seqlen, nheads, headdim)
 * - cumulative_seqlens: (nreqs+1, )
 * - round_size: The number of tokens in the round.
 * - nreqs: The number of requests in the round.
 * - buf_seqlen: The seqlen of the buffer.
 * - nheads: The number of query heads.
 * - headdim: The head dimension.
 * - is_copy_out_to_result: If true, copy from qbuf_dst to q_src. Otherwise,
 *      copy from q_src to qbuf_dst.
 * - write_trailing_zeros: If true, write trailing zeros to the end of q_src.
 *      Only works if nreqs == 1 and buf_seqlen == round_size and
 *      is_copy_out_to_result == true.
 *
 */
template <typename T>
__global__ void multirequest_flash_copy_queries_kernel(
    torch::PackedTensorAccessor32<T, 3, torch::RestrictPtrTraits> q_src,
    torch::PackedTensorAccessor32<T, 4, torch::RestrictPtrTraits> qbuf_dst,
    const int* cumulative_seqlens, int round_size, int nreqs, int buf_seqlen, int nheads,
    int headdim, bool is_copy_out_to_result, bool write_trailing_zeros) {
    int my_req = blockIdx.x;
    int start = cumulative_seqlens[my_req];    // index of the first token in the request
    int end = cumulative_seqlens[my_req + 1];  // index of the last token in the request + 1
    int request_length = end - start;
    int my_request_token_position = blockIdx.y;  // position within the request
    bool is_past_request_end = my_request_token_position >= request_length;
    // marks the thread blocks that may write the trailing zeros
    bool is_trailing_zeros_block =
        ((write_trailing_zeros
          // for now we assume that there is only one request in case of write_trailing_zeros.
          // This avoids overwriting the results between different thread blocks. When we
          // want to support multiple requests, we need to add a check for the last request
          // and that the position is past the end of the last request.
          //  && (my_req == nreqs - 1)
          && is_past_request_end && (start + my_request_token_position < round_size)));
    if (is_past_request_end && !is_trailing_zeros_block) {
        return;
    }

    // We need to read from columns [start:end]
    // So we offset by (start + my_request_token_position)
    int src_token_offset = start + my_request_token_position;
    // We are writing to the _tail_ of qbuf_dst
    // So first we offset fully by which request we are
    // And then we offset by:
    //  - buf_seqlen - request_length: starting token position
    //  - _plus_ my_request_token_position: which token I am in the request
    int dst_token_offset = buf_seqlen - request_length + my_request_token_position;

    int head_offset = blockIdx.z * HEAD_GROUP_SIZE;
    int max_local_head = min(head_offset + HEAD_GROUP_SIZE, nheads);

    if (is_trailing_zeros_block) {
        // ensure that the result is zeroed out
        for (int head_idx = head_offset; head_idx < max_local_head; ++head_idx) {
            q_src[src_token_offset][head_idx][threadIdx.x] = 0;
        }
        return;
    }

    for (int head_idx = head_offset; head_idx < max_local_head; ++head_idx) {
        T* src = &q_src[src_token_offset][head_idx][threadIdx.x];
        T* dst = &qbuf_dst[my_req][dst_token_offset][head_idx][threadIdx.x];
        if (is_copy_out_to_result) {
            // In the result case, the meaning of src and dst are swapped
            *src = *dst;
        } else {
            *dst = *src;
        }
    }
}

void multirequest_flash_copy_queries_cuda(
    torch::Tensor& q_src,               // (round_size, nheads, headdim)
    torch::Tensor& qbuf_dst,            // (nreqs, buf_seqlen, nheads, headdim)
    torch::Tensor& cumulative_seqlens,  // (nreqs+1, )
    bool is_copy_out_to_result, bool write_trailing_zeros) {
    int nreqs = qbuf_dst.size(0);
    int buf_seqlen = qbuf_dst.size(1);
    int nheads = qbuf_dst.size(2);
    int headdim = qbuf_dst.size(3);
    int round_size = q_src.size(0);

    // We split the nheads dimension into 8-head groups.
    int num_head_groups = (nheads + HEAD_GROUP_SIZE - 1) / HEAD_GROUP_SIZE;  // round up
    dim3 grid_dims(nreqs, buf_seqlen, num_head_groups);

    int block_size = headdim;

    AT_DISPATCH_FLOATING_TYPES_AND3(
        at::ScalarType::Half, at::ScalarType::BFloat16, at::ScalarType::Float8_e4m3fn,
        qbuf_dst.scalar_type(), "multirequest_flash_copy_queries_kernel", ([&] {
            multirequest_flash_copy_queries_kernel<scalar_t>
                <<<grid_dims, block_size, 0, at::cuda::getCurrentCUDAStream().stream()>>>(
                    q_src.packed_accessor32<scalar_t, 3, torch::RestrictPtrTraits>(),
                    qbuf_dst.packed_accessor32<scalar_t, 4, torch::RestrictPtrTraits>(),
                    cumulative_seqlens.data_ptr<int>(), round_size, nreqs, buf_seqlen, nheads,
                    headdim, is_copy_out_to_result, write_trailing_zeros);
        }));
}

__global__ void multirequest_flash_copy_cache_metadata_kernel(const int* cumulative_seqlens,
                                                              const int* cache_idxs,
                                                              const int* cache_locs,
                                                              int* cache_batch_idxs_dst,
                                                              int* cache_seqlens_dst) {
    int my_req = blockIdx.x;
    int start = cumulative_seqlens[my_req];
    int end = cumulative_seqlens[my_req + 1];
    if (end > start) {
        cache_batch_idxs_dst[my_req] = cache_idxs[end - 1];
        cache_seqlens_dst[my_req] = cache_locs[end - 1];
    } else {
        cache_batch_idxs_dst[my_req] = 0;
        cache_seqlens_dst[my_req] = 0;
    }
}

void multirequest_flash_copy_cache_metadata_cuda(torch::Tensor& cumulative_seqlens,  // (nreqs+1, )
                                                 torch::Tensor& cache_idxs,  // (round_size, )
                                                 torch::Tensor& cache_locs,  // (round_size, )
                                                 torch::Tensor& cache_batch_idxs_dst,  // (nreqs, )
                                                 torch::Tensor& cache_seqlens_dst      // (nreqs, )
) {
    int nreqs = cumulative_seqlens.size(0) - 1;
    multirequest_flash_copy_cache_metadata_kernel<<<nreqs, 1, 0,
                                                    at::cuda::getCurrentCUDAStream().stream()>>>(
        cumulative_seqlens.data_ptr<int>(), cache_idxs.data_ptr<int>(), cache_locs.data_ptr<int>(),
        cache_batch_idxs_dst.data_ptr<int>(), cache_seqlens_dst.data_ptr<int>());
}

/*
 * Splits the cumulative position tensor for sequence parallelism.
 */
__global__ void split_cumulative_pos_for_sequence_parallel_kernel(const int* cumulative_pos,
                                                                  int num_tokens_per_device,
                                                                  int process_idx, int num_requests,
                                                                  int* split) {
    int start_pos_this_device = process_idx * num_tokens_per_device;
    int end_pos_this_device = (process_idx + 1) * num_tokens_per_device;
    int num_requests_this_device = 0;
    split[0] = 0;  // ensure that the first position is always zero
    for (int request_idx = 0; request_idx < num_requests; request_idx++) {
        int pos = cumulative_pos[request_idx + 1];
        int end_position_this_request_on_this_device = std::min(pos, end_pos_this_device);
        int pos_relative_to_this_devices_start =
            end_position_this_request_on_this_device - start_pos_this_device;
        if (pos_relative_to_this_devices_start <= 0) {
            // skip requests that happen before the first token on this device
            continue;
        }
        split[num_requests_this_device + 1] = pos_relative_to_this_devices_start;
        num_requests_this_device += 1;
    }
    // fill remaining positions by repeating the last value
    for (int i = num_requests_this_device; i < num_requests; i++) {
        split[i + 1] = split[num_requests_this_device];
    }
}

void split_cumulative_pos_for_sequence_parallel_cuda(torch::Tensor& cumulative_pos,  // (nreqs+1, )
                                                     int num_tokens_per_device, int process_idx,
                                                     torch::Tensor& split) {  // (nreqs+1, )
    int num_requests = cumulative_pos.size(0) - 1;
    split_cumulative_pos_for_sequence_parallel_kernel<<<
        1, 1, 0, at::cuda::getCurrentCUDAStream().stream()>>>(cumulative_pos.data_ptr<int>(),
                                                              num_tokens_per_device, process_idx,
                                                              num_requests, split.data_ptr<int>());
}

// NOTE(register-tokens-rounds): the register tokens kernel is basically serial, so it runs in a
// single CTA, with the core loop on thread0. To speed it up, we load all the tokens and cache_idxs
// into shared memory. This breaks when the token count gets too high, since there isn't enough
// shared memory for a single CTA. The fix is to load the tokens / cache_idxs to shmem in rounds
// of this many tokens.
static const int kRegisterTokensBlockSize = 4096;

__global__ void register_tokens_kernel(
    const torch::PackedTensorAccessor32<int, 1, torch::RestrictPtrTraits> tokens,
    const torch::PackedTensorAccessor32<int, 1, torch::RestrictPtrTraits> cache_idxs,
    torch::PackedTensorAccessor32<int, 1, torch::RestrictPtrTraits> positions,
    torch::PackedTensorAccessor32<int, 2, torch::RestrictPtrTraits> tokens_cache,
    int padding_cache_idx,
    torch::PackedTensorAccessor32<int, 1, torch::RestrictPtrTraits> new_pos) {
    extern __shared__ int32_t shared_mem[];
    int32_t* shared_mem_positions = shared_mem;
    int32_t* shared_mem_cache_idxs = shared_mem + positions.size(0);
    int32_t* shared_mem_tokens = shared_mem + positions.size(0) + kRegisterTokensBlockSize;

#pragma unroll
    for (int i = threadIdx.x; i < positions.size(0); i += blockDim.x) {
        shared_mem_positions[i] = positions[i];
    }

    // NOTE: we are guaranteed that both `tokens` and `cache_idxs` have size [num_tokens].
    int num_tokens = tokens.size(0);
    int num_rounds = (num_tokens + kRegisterTokensBlockSize - 1) / kRegisterTokensBlockSize;
    for (int round_idx = 0; round_idx < num_rounds; round_idx++) {
        int start_token = round_idx * kRegisterTokensBlockSize;
        int end_token = min(start_token + kRegisterTokensBlockSize, num_tokens);

#pragma unroll
        for (int i = start_token + threadIdx.x; i < end_token; i += blockDim.x) {
            shared_mem_cache_idxs[i - start_token] = cache_idxs[i];
        }
#pragma unroll
        for (int i = start_token + threadIdx.x; i < end_token; i += blockDim.x) {
            shared_mem_tokens[i - start_token] = tokens[i];
        }
        __syncthreads();

        if (threadIdx.x == 0) {
#pragma unroll 32
            for (int index_in_round = start_token; index_in_round < end_token; index_in_round++) {
                int cache_idx = shared_mem_cache_idxs[index_in_round - start_token];
                int position = shared_mem_positions[cache_idx];
                int token = shared_mem_tokens[index_in_round - start_token];
                if (cache_idx == padding_cache_idx) {
                    new_pos[index_in_round] = 0;
                } else {
                    new_pos[index_in_round] = position;
                    shared_mem_positions[cache_idx] = position + 1;
                    if (tokens_cache.size(0) > 1) {  // avoids writing to the dummy tensor
                        tokens_cache[cache_idx][position] = token;
                    }
                }
            }
        }
        __syncthreads();
    }
#pragma unroll
    for (int i = threadIdx.x; i < positions.size(0); i += blockDim.x) {
        int position = shared_mem_positions[i];
        positions[i] = position;
    }
}

void register_tokens_cuda(torch::Tensor& tokens, torch::Tensor& cache_idxs,
                          torch::Tensor& positions, std::optional<torch::Tensor> tokens_cache,
                          int padding_cache_idx, torch::Tensor& new_pos) {
    // See NOTE(register-tokens-rounds). We assert that positions is smaller than the round_size
    // to avoid accidentally blowing up shmem allocation. Hard to imagine this happening, since
    // it would mean more than 4096 kv cache lines.
    TORCH_CHECK(positions.size(0) <= kRegisterTokensBlockSize);
    int shmem_size = (positions.size(0) + (2 * kRegisterTokensBlockSize)) * sizeof(int32_t);

    torch::Tensor dummy_tensor;
    if (!tokens_cache.has_value()) {
        dummy_tensor = torch::empty({1, 1}, tokens.options());
    }
    auto tokens_cache_accessor =
        tokens_cache.has_value()
            ? tokens_cache.value().packed_accessor32<int, 2, torch::RestrictPtrTraits>()
            : dummy_tensor.packed_accessor32<int, 2, torch::RestrictPtrTraits>();

    register_tokens_kernel<<<1, 32, shmem_size, at::cuda::getCurrentCUDAStream().stream()>>>(
        tokens.packed_accessor32<int, 1, torch::RestrictPtrTraits>(),
        cache_idxs.packed_accessor32<int, 1, torch::RestrictPtrTraits>(),
        positions.packed_accessor32<int, 1, torch::RestrictPtrTraits>(), tokens_cache_accessor,
        padding_cache_idx, new_pos.packed_accessor32<int, 1, torch::RestrictPtrTraits>());
}

template <typename scalar_t>
__global__ void fused_q_contiguous_kv_copy_kernel(
    const torch::PackedTensorAccessor32<scalar_t, 4, torch::RestrictPtrTraits> q,
    const torch::PackedTensorAccessor32<scalar_t, 3, torch::RestrictPtrTraits> k,
    const torch::PackedTensorAccessor32<scalar_t, 3, torch::RestrictPtrTraits> v,
    const torch::PackedTensorAccessor32<int, 1, torch::RestrictPtrTraits> kv_pos_idxs,
    const torch::PackedTensorAccessor32<int, 1, torch::RestrictPtrTraits> kv_cache_idxs,
    torch::PackedTensorAccessor32<scalar_t, 4, torch::RestrictPtrTraits> q_dest,
    torch::PackedTensorAccessor32<scalar_t, 4, torch::RestrictPtrTraits> k_dest,
    torch::PackedTensorAccessor32<scalar_t, 4, torch::RestrictPtrTraits> v_dest) {
    int token_idx = blockIdx.x;
    int kv_head_idx = blockIdx.y;
    int my_kv_pos = kv_pos_idxs[token_idx];
    int my_cache_idx = kv_cache_idxs[token_idx];
#pragma unroll 16
    for (int q_head_idx = 0; q_head_idx < q.size(2); ++q_head_idx) {
        q_dest[token_idx][kv_head_idx][q_head_idx][threadIdx.x] =
            q[token_idx][kv_head_idx][q_head_idx][threadIdx.x];
    }
    k_dest[my_cache_idx][my_kv_pos][kv_head_idx][threadIdx.x] =
        k[token_idx][kv_head_idx][threadIdx.x];
    v_dest[my_cache_idx][my_kv_pos][kv_head_idx][threadIdx.x] =
        v[token_idx][kv_head_idx][threadIdx.x];
}

torch::Tensor fused_q_contiguous_kv_copy_cuda(
    torch::Tensor& q,             // (ntokens, nheads_kv, q_per_kv, headdim)
    torch::Tensor& k,             // (ntokens, nheads_kv, headdim)
    torch::Tensor& v,             // (ntokens, nheads_kv, headdim)
    torch::Tensor& kv_pos_idxs,   // (ntokens, )
    torch::Tensor& k_dest,        // (ncaches, seqlen, nheads_kv, headdim)
    torch::Tensor& v_dest,        // (ncaches, seqlen, nheads_kv, headdim)
    torch::Tensor& kv_cache_idxs  // (ntokens, )
) {
    // Note: no fp8 support.
    TORCH_CHECK(k_dest.dtype() == v_dest.dtype());
    TORCH_CHECK(k_dest.dtype() != torch::kFloat8_e4m3fn);
    torch::Tensor q_dest = torch::empty_like(q);
    // Re-do dimension checks
    TORCH_CHECK(q.dim() == 4);
    TORCH_CHECK(k.dim() == 3);
    TORCH_CHECK(v.dim() == 3);
    TORCH_CHECK(kv_pos_idxs.dim() == 1);
    TORCH_CHECK(kv_cache_idxs.dim() == 1);
    TORCH_CHECK(k_dest.dim() == 4);
    TORCH_CHECK(v_dest.dim() == 4);

    // One block per token per kv head.
    dim3 num_blocks(k.size(0), q.size(1), 1);
    // One thread per headdim element.
    int num_threads = q.size(-1);
    AT_DISPATCH_FLOATING_TYPES_AND2(
        at::ScalarType::Half, at::ScalarType::BFloat16, q.scalar_type(),
        "fused_q_contiguous_kv_copy_kernel", ([&] {
            fused_q_contiguous_kv_copy_kernel<scalar_t>
                <<<num_blocks, num_threads, 0, at::cuda::getCurrentCUDAStream().stream()>>>(
                    q.packed_accessor32<scalar_t, 4, torch::RestrictPtrTraits>(),
                    k.packed_accessor32<scalar_t, 3, torch::RestrictPtrTraits>(),
                    v.packed_accessor32<scalar_t, 3, torch::RestrictPtrTraits>(),
                    kv_pos_idxs.packed_accessor32<int, 1, torch::RestrictPtrTraits>(),
                    kv_cache_idxs.packed_accessor32<int, 1, torch::RestrictPtrTraits>(),
                    q_dest.packed_accessor32<scalar_t, 4, torch::RestrictPtrTraits>(),
                    k_dest.packed_accessor32<scalar_t, 4, torch::RestrictPtrTraits>(),
                    v_dest.packed_accessor32<scalar_t, 4, torch::RestrictPtrTraits>());
        }));
    return q_dest;
}

}  // namespace compiled_attention_utils
