"""Tests for base.fastforward.torch_utils."""

import pytest
import torch
from torch import nn

from base.fastforward import torch_utils


def test_init_with_weights():
    state_dict = {
        "weight": torch.zeros(10, 20, device="cuda"),
        "bias": torch.zeros(10, device="cuda"),
    }
    linear = torch_utils.init_with_weights(
        torch.nn.Linear, state_dict, in_features=20, out_features=10, bias=True
    )
    assert linear.weight.is_cuda
    assert linear.bias.is_cuda
    assert linear.weight.data_ptr() == state_dict["weight"].data_ptr()
    assert linear.bias.data_ptr() == state_dict["bias"].data_ptr()


def test_init_with_weights_hook():
    class Foo(nn.Module):
        def __init__(self, device="cpu"):
            super().__init__()
            self.param = nn.Parameter(torch.tensor(1.0, device=device))
            self.register_buffer("buffer", torch.tensor(1.0, device=device))
            self._hook_ran = False
            self.register_load_state_dict_post_hook(self.load_state_dict_post_hook)

        def load_state_dict_post_hook(self, _, __):
            self._hook_ran = True

    class Bar(nn.Module):
        def __init__(self, device="cpu"):
            super().__init__()
            self.param = nn.Parameter(torch.tensor(1.0, device=device))
            self.register_buffer("buffer", torch.tensor(1.0, device=device))
            self.foo = Foo()
            self._hook_ran = False
            self.register_load_state_dict_post_hook(self.load_state_dict_post_hook)

        def load_state_dict_post_hook(self, _, __):
            self._hook_ran = True

    state_dict = {
        "param": torch.tensor(1.0),
        "buffer": torch.tensor(2.0),
        "foo.param": torch.tensor(3.0),
        "foo.buffer": torch.tensor(4.0),
    }
    bar = torch_utils.init_with_weights(Bar, state_dict)
    assert bar._hook_ran
    assert bar.foo._hook_ran


def test_cuda_timeit():
    # Run the operation with a medium computation and make sure the times make sense.
    x = torch.randn(2**10, 2**10, device="cuda")
    median_latency_ms, _ = torch_utils.cuda_timeit(lambda: x @ x)
    assert median_latency_ms > 0


def test_shard_identity():
    # Identity if num_processes == 1.
    tensor = torch.arange(100, device="cuda")
    torch.testing.assert_close(torch_utils.shard(tensor, 0, 0, 1), tensor)


def test_shard_split():
    # Split tensor into two equal parts.
    tensor = torch.arange(100, device="cuda")
    torch.testing.assert_close(torch_utils.shard(tensor, 0, 0, 2), tensor[:50])
    torch.testing.assert_close(torch_utils.shard(tensor, 0, 1, 2), tensor[50:])


def test_shard_split_uneven():
    # Split tensor into three unequal parts.
    tensor = torch.arange(100, device="cuda")
    with pytest.raises(AssertionError):
        torch.testing.assert_close(torch_utils.shard(tensor, 0, 0, 3), tensor[:34])


def test_shard_inner_axis():
    # Split tensor along inner axis.
    tensor = torch.arange(100, device="cuda").reshape(10, 10)
    torch.testing.assert_close(torch_utils.shard(tensor, 0, 0, 2), tensor[:5, :])
    torch.testing.assert_close(torch_utils.shard(tensor, 0, 1, 2), tensor[5:, :])
    torch.testing.assert_close(torch_utils.shard(tensor, 1, 0, 2), tensor[:, :5])
    torch.testing.assert_close(torch_utils.shard(tensor, 1, 1, 2), tensor[:, 5:])


def test_index_in_second_dim():
    x = torch.tensor([[0, 1, 2, 3], [4, 5, 6, 7], [8, 9, 10, 11]])
    indices = torch.tensor([2, 1, 0], dtype=torch.int32)
    expected = torch.tensor([2, 5, 8])
    torch.testing.assert_close(torch_utils.index_in_second_dim(x, indices), expected)
