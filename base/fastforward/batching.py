"""Batching utilities.

This module contains utilities to batch requests for the inference server
and the embedder service.
"""

import uuid
from dataclasses import dataclass
from typing import Any, Sequence, Tuple

import numpy as np
import torch

from base.fastforward import all_reduce, cached_attention
from base.fastforward.parallel import ParallelContext

Tokens = Sequence[int]


@dataclass(frozen=True)
class RequestInRound:
    """A short sequence of tokens that are part of an inference round.

    In case of model parallelism, this class is pickled and sent to the other
    processes, so we avoid referencing unnecessary objects in here.
    """

    num_tokens: int
    cache_idx: int
    round_start_idx: int  # index of the first token of this element in the round
    metadata: Any = None


# NOTES ON ROUNDS, PADDING, AND SORTING
# - At a baseline, a round has a set of requests, each containing some number of tokens.
# - There are two ways that a round can be "padded":
#   (1) Token padding: add extra tokens s.t. the round size (total token count) is some value.
#   (2) Request padding: add extra requests (of zero tokens) s.t. the number of requests is some value.
# - These kinds of padding serve different goals:
#   - Token padding makes the round an even size for graph execution, matmul sizing, etc.
#   - Request padding makes attention happy by having statically-sized metadata buffers.
# - Most notably, these things are handled _independently_. Padding tokens don't add requests, and
#   padding requests don't add tokens.
# - NOTE(padding-tokens-no-request): this point about padding tokens not being represented by a
#   request is super important. If you loop over requests, you may "miss" the padding tokens. See,
#   eg, the note in `load_requests` below.
# - Furthermore, a round can be "multirequest sorted". This means sorting the requests s.t. there
#   is a single "big" request at the front followed by a set of "small" requests. This satisfies
#   the requirements of multi-request flash attention.
#
# The constraints are slightly weaker than this, but the correct order for updating a round is:
#   1. Sort
#   2. Pad requests
#   3. Pad tokens
# We combine (1+2) with `sort_and_extend_requests_for_multirequest_attention`.
@dataclass(frozen=True)
class Round:
    """A round of tokens and their corresponding request info."""

    tokens: Tokens
    requests_in_round: Sequence[RequestInRound]
    has_token_padding: bool = False
    has_request_padding: bool = False
    is_multirequest_sorted: bool = False
    _num_padding_tokens: int = 0

    def __post_init__(self):
        num_tokens_sum = sum(
            request_info.num_tokens for request_info in self.requests_in_round
        )
        if self.has_token_padding:
            # As discussed in NOTE(padding-tokens-no-request), the padding tokens don't have a
            # request, so we manually add them in.
            num_tokens_sum += self._num_padding_tokens
        if len(self.tokens) != num_tokens_sum:
            raise ValueError(
                f"tokens and requests_in_round do not match: {len(self.tokens)} != {num_tokens_sum}"
            )

    def pad_to_num_requests(
        self,
        num_requests: int,
        padding_cache_idx: int = 0,
    ) -> "Round":
        """Pad the round to have a certain number of requests."""
        if self.has_request_padding:
            raise ValueError("Round already has request padding.")
        if self.has_token_padding:
            raise ValueError(
                "Tried to pad requests after padding tokens. This may be OK but is almost certainly an error."
            )
        if len(self.requests_in_round) > num_requests:
            raise ValueError(
                f"Too many requests in round: {len(self.requests_in_round)} > {num_requests=}"
            )
        new_requests = list(self.requests_in_round)
        num_padding_requests = num_requests - len(self.requests_in_round)
        assert num_padding_requests >= 0
        for _ in range(num_padding_requests):
            new_requests.append(
                RequestInRound(
                    num_tokens=0,
                    cache_idx=padding_cache_idx,
                    round_start_idx=len(self.tokens),
                )
            )
        return Round(
            tokens=list(self.tokens),
            requests_in_round=new_requests,
            has_token_padding=self.has_token_padding,
            has_request_padding=True,
            is_multirequest_sorted=self.is_multirequest_sorted,
            _num_padding_tokens=self._num_padding_tokens,
        )

    def _sort_requests_for_multirequest_attention(
        self,
        max_small_request_size: int | None,
    ) -> tuple["Round", Sequence[int]]:
        if self.has_token_padding or self.has_request_padding:
            raise ValueError("Cannot sort a round after it has been padded.")

        # We need the largest request at the front, so just sort in decreasing length
        sorted_requests_with_orig_idxs = sorted(
            enumerate(self.requests_in_round),
            key=lambda x: x[1].num_tokens,
            reverse=True,
        )
        # Note: don't use zip(*) here to unpack in case it's an empty list
        new_to_orig_idxs = [x[0] for x in sorted_requests_with_orig_idxs]
        requests_in_decreasing_length = [x[1] for x in sorted_requests_with_orig_idxs]
        orig_to_new_idxs = np.argsort(np.array(new_to_orig_idxs)).tolist()

        # Check that every request after the first satisfies the max_small_request_size constraint
        if max_small_request_size is not None:
            for req in requests_in_decreasing_length[1:]:
                if req.num_tokens > max_small_request_size:
                    raise ValueError(
                        f"Request size {req.num_tokens} is larger than {max_small_request_size=}"
                    )

        # Construct new RequestInRound objects for a permuted round in decreasing length
        new_tokens = [0 for _ in range(len(self.tokens))]
        new_requests = []
        cumulative_tokens_seen = 0
        for req in requests_in_decreasing_length:
            old_start = req.round_start_idx
            old_end = old_start + req.num_tokens
            new_start = cumulative_tokens_seen
            new_end = new_start + req.num_tokens
            new_tokens[new_start:new_end] = self.tokens[old_start:old_end]
            new_requests.append(
                RequestInRound(
                    num_tokens=req.num_tokens,
                    cache_idx=req.cache_idx,
                    round_start_idx=new_start,
                    metadata=req.metadata,
                )
            )
            cumulative_tokens_seen += req.num_tokens
        # Sanity checks on what we've processed so far
        assert cumulative_tokens_seen == len(self.tokens)
        return (
            Round(
                tokens=new_tokens,
                requests_in_round=new_requests,
                is_multirequest_sorted=True,
            ),
            orig_to_new_idxs,
        )

    def sort_and_extend_requests_for_multirequest_attention(
        self,
        max_requests_in_round: int | None,
        max_small_request_size: int | None,
        dummy_cache_idx: int = 0,
    ) -> tuple["Round", Sequence[int]]:
        sorted_round, orig_to_new_idxs = self._sort_requests_for_multirequest_attention(
            max_small_request_size=max_small_request_size,
        )
        if max_requests_in_round is not None:
            sorted_round = sorted_round.pad_to_num_requests(
                num_requests=max_requests_in_round,
                padding_cache_idx=dummy_cache_idx,
            )
        return sorted_round, orig_to_new_idxs

    def pad_to_next_round_size(
        self,
        round_sizes: Sequence[int],
        padding_token: int = 0,
    ) -> "Round":
        """Pad tokens to the next round size."""
        if self.has_token_padding:
            raise ValueError("Round already has token padding.")
        round_size = choose_round_size(round_sizes, len(self.tokens))
        assert len(self.tokens) <= round_size
        if len(self.tokens) == round_size:
            return self

        num_padding = round_size - len(self.tokens)
        assert num_padding > 0
        tokens = list(self.tokens)
        tokens += [padding_token] * num_padding
        requests = list(self.requests_in_round)
        return Round(
            tokens=tokens,
            requests_in_round=requests,
            has_token_padding=True,
            has_request_padding=self.has_request_padding,
            is_multirequest_sorted=self.is_multirequest_sorted,
            _num_padding_tokens=num_padding,
        )


class RoundAttention(cached_attention.Attention):
    """Wraps an attention module to split batches based on round composition.

    This class implements a batch attention mechanism that can be used to
    selectively attend to a subset of the tokens in a batch. It has some
    similarities with selective batching, described in "ORCA: A Distributed
    Serving System for Transformer-Based Generative Models"

    https://www.usenix.org/system/files/osdi22-yu.pdf

    In case of model parallelism, this class is pickled and sent to the other
    processes.
    """

    def __init__(
        self,
        max_round_size: int,
        mc_attn: cached_attention.MultiCacheAttention,
    ):
        self._max_round_size = max_round_size
        self._mc_attn = mc_attn

        devices = mc_attn.get_devices()
        # for each token in the round, which cache does it belong to?
        self._cache_idxs = {
            device: torch.zeros(
                max_round_size,
                dtype=torch.int32,
                device=device,
            )
            for device in devices
        }
        # for each token in the round, how many tokens have been processed before it?
        self._cache_pos = {
            device: torch.zeros(
                max_round_size,
                dtype=torch.int32,
                device=device,
            )
            for device in devices
        }
        max_requests_in_round = mc_attn.get_max_requests_in_round()
        if max_requests_in_round is None:
            max_requests_in_round = self._max_round_size
        self._cumulative_round_pos = {
            device: torch.zeros(
                max_requests_in_round + 1,  # +1 for the leading 0
                dtype=torch.int32,
                device=device,
            )
            for device in devices
        }

        # The stable ID is needed to make the RoundAttention usable in a multi-gpu
        # setting, where the attention object is sent via pickle to the other processes.
        # To the other processes, the attention object appears to be new every time, so
        # it would create cache misses (in the cuda graph cache). Basing the hashes
        # and equality on a stable ID ensures that we reuse cuda graphs.
        self._stable_id = str(uuid.uuid4())
        print(f"Created RoundAttention with stable_id {self._stable_id}.", flush=True)

    def __hash__(self) -> int:
        return hash(self._stable_id)

    def __eq__(self, other: object) -> bool:
        return type(self) == type(other) and self._stable_id == other._stable_id  # type: ignore

    @property
    def max_round_size(self) -> int:
        return self._max_round_size

    def load_requests(self, round_: Round):
        """Write request_infos into self._cache_idxs."""
        num_caches = self._mc_attn.num_caches + (1 if round_.has_token_padding else 0)
        for device in self._mc_attn.get_devices():
            # See NOTE(padding-tokens-no-request). This is an instance where we cannot depend on
            # the loop over `requests_in_round` below, since it misses the padding tokens. So we
            # need to explicitly `fill_` all of the cache_idxs beforehand with the padding idx.
            self._cache_idxs[device].fill_(self.get_padding_cache_idx())
            cumulative_token_count = 0
            for request_idx, request_info in enumerate(round_.requests_in_round):
                if request_info.cache_idx >= num_caches:
                    raise ValueError(
                        f"{request_info.cache_idx} >= {num_caches}, {round_.has_token_padding}"
                    )
                start_idx = request_info.round_start_idx
                final_idx = start_idx + request_info.num_tokens
                self._cache_idxs[device][start_idx:final_idx].fill_(
                    request_info.cache_idx
                )
                self._cumulative_round_pos[device][request_idx] = cumulative_token_count
                cumulative_token_count += request_info.num_tokens
            # Fill out the remainder with the total token count (ie, pad with empty requests)
            num_requests_in_round = len(round_.requests_in_round)
            self._cumulative_round_pos[device][num_requests_in_round:].fill_(
                cumulative_token_count
            )

    def __call__(
        self,
        qkv: torch.Tensor,
        layer_idx: int,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: torch.Tensor | None = None,
    ):
        device = self._mc_attn.get_device_from_process_idx(
            process_idx=parallel_ctx.process_idx
        )
        cache_idxs = self._cache_idxs[device]
        cache_pos = self._cache_pos[device]
        round_seq_size = qkv.size(0)
        # For sequence parallel processing we process only a section of the round,
        # but we still pass in the cache_idxs and cache_pos for the entire round.
        # The batch dimension of the qkv tensor is reduced - it only contains the tokens
        # that live on this device.
        round_seq_size *= parallel_ctx.sp_size
        return self._mc_attn(
            qkv=qkv,
            cache_idxs=cache_idxs[:round_seq_size],
            cache_pos=cache_pos[:round_seq_size],
            layer_idx=layer_idx,
            cumulative_round_pos=self._cumulative_round_pos[device],
            parallel_ctx=parallel_ctx,
            attn_all_gather_kit=attn_all_gather_kit,
            attn_qkv_scales=attn_qkv_scales,
        )

    def register_tokens_get_positions(
        self, tokens: torch.Tensor, process_idx: int
    ) -> torch.Tensor:
        device = self._mc_attn.get_device_from_process_idx(process_idx=process_idx)
        cache_idxs = self._cache_idxs[device]
        cache_pos = self._cache_pos[device]
        assert tokens.ndim == 1
        num_tokens = tokens.numel()
        assert num_tokens <= self._max_round_size, (
            f"{num_tokens=} > {self._max_round_size=} ... "
            "if this fails you may have tried to run sequence parallelism with too "
            "few tokens in the round."
        )
        pos = self._mc_attn.register_tokens_get_positions(
            tokens=tokens, cache_idxs=cache_idxs[:num_tokens], process_idx=process_idx
        )
        cache_pos[:num_tokens].copy_(pos)
        return pos

    def reset(self, to_position: int | None = None, by_positions: int | None = None):
        for cache_idx in range(self._mc_attn.num_caches):
            self._mc_attn.reset(
                cache_idx=cache_idx, to_position=to_position, by_positions=by_positions
            )

    def get_tensors_for_cuda_graph(
        self, process_idx: int | None = None
    ) -> dict[str, torch.Tensor]:
        """Returns a dict of all cache tensors. Used when capture CUDA graphs."""
        tensors = self._mc_attn.get_tensors_for_cuda_graph(process_idx=process_idx)
        device = self._mc_attn.get_device_from_process_idx(process_idx=process_idx)
        tensors["cache_idxs"] = self._cache_idxs[device]
        tensors["cache_pos"] = self._cache_pos[device]
        tensors["cumulative_round_pos"] = self._cumulative_round_pos[device]
        return tensors

    def get_padding_cache_idx(self) -> int:
        return self._mc_attn.padding_cache_idx

    def get_id(self) -> str:
        return self._stable_id

    def get_max_requests_in_round(self) -> int | None:
        return self._mc_attn.get_max_requests_in_round()

    def get_small_request_max_seqlen(self) -> int | None:
        return self._mc_attn.get_small_request_max_seqlen()


def process_round_sizes(round_sizes: Sequence[int]) -> Tuple[int, Sequence[int]]:
    """Sort round_sizes list and make sure it is montonically increasing.

    Args:
        round_sizes: the list of round_sizes

    Returns:
        The largest round size and a sorted list
    """
    if len(round_sizes) < 1:
        raise ValueError("round_sizes is empty")
    if not all(x < y for x, y in zip(round_sizes, round_sizes[1:])):
        raise ValueError("round_sizes is not monotonically increasing")
    return (round_sizes[-1], round_sizes)


def choose_round_size(
    round_sizes: Sequence[int], original_size: int, allow_overflow: bool = False
) -> int:
    """Choose the lowest round size equal to or higher than original_size.

    Args:
        round_sizes: a *sorted* sequence of valid round sizes.
        original_size: the original size we want to map into one of `round_sizes`.
        allow_overflow: if True, allow original_size to be larger than the largest
            round size. In this case, we return the largest round size.

    Returns:
        The smallest round size larger than original_size if original_size is smaller
        than the largest round size, and the largest round size otherwise.
    """
    if len(round_sizes) < 1:
        raise ValueError("round_sizes is empty")
    if original_size < 0:
        raise ValueError(f"{original_size=} < 0. Requires a non-negative number.")
    if not all(x < y for x, y in zip(round_sizes, round_sizes[1:])):
        raise ValueError("round_sizes is not monotonically increasing")
    try:
        return next(
            round_size for round_size in round_sizes if round_size >= original_size
        )
    except StopIteration:
        if not allow_overflow:
            raise ValueError(f"{original_size=} > {round_sizes[-1]=}")
        return round_sizes[-1]
