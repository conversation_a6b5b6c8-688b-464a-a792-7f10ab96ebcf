"""Test for various cross-GPU communication (allreduce) implementations."""

from typing import Callable

import functools
import pytest

import torch
import torch.distributed as dist

from base.fastforward import all_reduce, fp8, parallel
from base.static_analysis.common import check_not_none


def _init_nccl_allreduce(
    process_idx: int = 0, num_processes: int = 1
) -> Callable[[torch.Tensor], torch.Tensor]:
    def fn(values: torch.Tensor) -> torch.Tensor:
        values = values.clone().to(f"cuda:{process_idx}")
        dist.all_reduce(values, op=dist.ReduceOp.SUM)
        return values

    return fn


def test_nccl_allreduce():
    """Ensures the default allreduce implementation works as expected."""
    dim = 128
    values = torch.ones(dim, dtype=torch.float16, device="cuda")
    expected = values * 2

    with parallel.ParallelRunner(num_processes=2) as runner:
        mp_fn = runner.initialize(_init_nccl_allreduce)

        mp_result = mp_fn(values)
        assert mp_result.dtype == torch.float16

        torch.testing.assert_close(expected, mp_result, rtol=2e-2, atol=1e-2)


def _simulated_computation(
    values: torch.Tensor,
    all_reduce_kit: all_reduce.AllReduceKit,
    one_shot: bool,
    use_fp8: bool,
    residual_add: bool,
) -> torch.Tensor:
    """Test all_reduce when mixed in with torch operations."""

    # Values is a 1-D tensor but matmul likes 2-D tensors. unsqueeze to make it 2-D
    values = values.unsqueeze(0)
    dim = values.shape[-1]
    matrix = torch.eye(dim, dtype=values.dtype, device="cuda")
    all_reduced_values = torch.zeros_like(values, device="cuda")

    if use_fp8:
        fp8_scale = torch.ones(1, device="cuda", dtype=torch.float32)
        values_fp8 = fp8.to_fp8(values, 1.0, preserve_input=True)
        all_reduce_kit.get_input_tensor(shape=values.shape, dtype=torch.uint8).copy_(
            values_fp8
        )
    else:
        torch.matmul(
            values,
            matrix,
            out=all_reduce_kit.get_input_tensor(shape=values.shape, dtype=values.dtype),
        )
        fp8_scale = None

    all_reduce_kit.all_reduce(
        out=all_reduced_values,
        one_shot=one_shot,
        fp8_scale=fp8_scale,
        residual_buffer=values if residual_add else None,
    )

    all_reduce_kit.barrier()

    if use_fp8:
        torch.add(
            fp8.to_fp8(all_reduced_values, 1.0, preserve_input=True),
            torch.zeros_like(values, dtype=torch.uint8),
            out=all_reduce_kit.get_input_tensor(shape=values.shape, dtype=torch.uint8),
        )
    else:
        torch.add(
            all_reduced_values,
            torch.zeros_like(values, device="cuda"),
            out=all_reduce_kit.get_input_tensor(shape=values.shape, dtype=values.dtype),
        )

    all_reduce_kit.all_reduce(
        out=all_reduced_values,
        one_shot=one_shot,
        fp8_scale=fp8_scale,
        residual_buffer=values if residual_add else None,
    )

    # Unit test expected a 1-D tensor so squeeze it back
    return torch.add(
        all_reduced_values, torch.zeros_like(values, device="cuda")
    ).squeeze(0)


def _init_fastforward_allreduce(
    one_shot: bool,
    use_fp8: bool,
    residual_add: bool,
    process_idx: int = 0,
    num_processes: int = 1,
) -> Callable[[torch.Tensor], torch.Tensor]:
    assert num_processes == dist.get_world_size()
    assert process_idx == dist.get_rank()

    def fn(values: torch.Tensor) -> torch.Tensor:
        all_reduce_kit = all_reduce.AllReduceKit(
            values.numel(),
            values.dtype,
            input_is_fp8=use_fp8,
            process_group=check_not_none(dist.group.WORLD),
        )

        return _simulated_computation(
            values.clone().to(f"cuda:{process_idx}"),
            all_reduce_kit,
            one_shot,
            use_fp8,
            residual_add,
        )

    return fn


def _init_fastforward_allreduce_with_graph(
    one_shot: bool,
    use_fp8: bool,
    residual_add: bool,
    process_idx: int = 0,
    num_processes: int = 1,
) -> Callable[[torch.Tensor], torch.Tensor]:
    assert num_processes == dist.get_world_size()
    assert process_idx == dist.get_rank()

    def fn(values: torch.Tensor) -> torch.Tensor:
        # The * 5 means it's unlikely what's calculated during graph
        # capture will satisfy the unit test.
        static_values = torch.ones_like(values, device="cuda") * 5
        static_ret = torch.zeros_like(values, device="cuda")

        all_reduce_kit = all_reduce.AllReduceKit(
            static_values.numel(),
            static_values.dtype,
            input_is_fp8=use_fp8,
            process_group=check_not_none(dist.group.WORLD),
        )

        torch.cuda.synchronize()

        s = torch.cuda.Stream()
        s.wait_stream(torch.cuda.current_stream())
        with torch.cuda.stream(s):
            for _ in range(3):
                _simulated_computation(
                    static_values,
                    all_reduce_kit,
                    one_shot,
                    use_fp8,
                    residual_add=residual_add,
                )
                torch.cuda.synchronize()

        torch.cuda.current_stream().wait_stream(s)

        g = torch.cuda.CUDAGraph()

        with torch.cuda.graph(g):
            static_ret = _simulated_computation(
                static_values,
                all_reduce_kit,
                one_shot,
                use_fp8,
                residual_add=residual_add,
            )

        g.replay()
        torch.cuda.synchronize()

        # Show we can replay more than once.
        static_values.copy_(values)
        g.replay()
        torch.cuda.synchronize()

        return static_ret

    return fn


@pytest.mark.parametrize(
    "init_fn",
    [
        pytest.param(_init_fastforward_allreduce, id="no_graph"),
        pytest.param(_init_fastforward_allreduce_with_graph, id="graph"),
    ],
)
@pytest.mark.parametrize(
    "use_fp8", [pytest.param(False, id="fp16"), pytest.param(True, id="fp8")]
)
@pytest.mark.parametrize(
    "residual_add",
    [pytest.param(False, id="no_residual"), pytest.param(True, id="residual")],
)
def test_fastforward_allreduce_one_shot(init_fn, use_fp8, residual_add):
    """Sanity test of the FastForward AllReduce Implementation."""
    dim = 128
    torch.manual_seed(31415)

    with parallel.ParallelRunner(num_processes=2) as runner:
        mp_fn = runner.initialize(
            functools.partial(
                init_fn, one_shot=True, use_fp8=use_fp8, residual_add=residual_add
            )
        )

        # NOTE: it would make sense to parametrize over dtype, but the ParallelRunner setup is
        # _expensive_, so we move it inside the test function.
        for dtype in [torch.bfloat16, torch.float16]:
            values = torch.ones(dim, dtype=dtype, device="cuda")
            if not use_fp8:
                # FP8 allreduce has some issues with its scaling factors that causes the assert_close
                # to fail if we use random values with FP8.
                # TODO: fix the FP8 scaling factor handling, then remove the if-condition
                values.uniform_(0.5, 1)
            # We do two allreduces of the identity transformation on two nodes so expect a vector of 4s: (1 * 2) * 2.
            # Residual add adds another copy of the original values at the end of each allreduce: (1 * 2 + 1) * 2 + 1.
            expected = values * 7 if residual_add else values * 4
            mp_result = mp_fn(values)
            assert mp_result.dtype == values.dtype

            torch.testing.assert_close(expected, mp_result, rtol=2e-2, atol=1e-2)


# fp8 is not supported for two-shot allreduce.
@pytest.mark.parametrize(
    "init_fn",
    [
        pytest.param(_init_fastforward_allreduce, id="no_graph"),
        pytest.param(_init_fastforward_allreduce_with_graph, id="graph"),
    ],
)
@pytest.mark.parametrize(
    "residual_add",
    [pytest.param(False, id="no_residual"), pytest.param(True, id="residual")],
)
def test_fastforward_allreduce_two_shot(init_fn, residual_add):
    """Sanity test of the FastForward AllReduce Implementation."""
    dim = 128
    torch.manual_seed(31415)

    with parallel.ParallelRunner(num_processes=2) as runner:
        mp_fn = runner.initialize(
            functools.partial(
                init_fn, one_shot=False, use_fp8=False, residual_add=residual_add
            )
        )

        for dtype in [torch.bfloat16, torch.float16]:
            values = torch.ones(dim, dtype=dtype, device="cuda")
            values.uniform_(0.5, 1)
            expected = values * 7 if residual_add else values * 4

            mp_result = mp_fn(values)
            assert mp_result.dtype == values.dtype

            torch.testing.assert_close(expected, mp_result, rtol=2e-2, atol=1e-2)


def _init_all_gather(
    max_elements: int, dtype: torch.dtype, process_idx: int, num_processes: int
):
    assert num_processes == dist.get_world_size()
    assert process_idx == dist.get_rank()
    ar_kit = all_reduce.AllReduceKit(
        max_elements,
        dtype,
        process_group=check_not_none(dist.group.WORLD),
    )
    torch.manual_seed(1234 + process_idx)

    def fn(shape):
        if dtype.is_floating_point:
            x = torch.rand(shape, dtype=dtype, device="cuda")
        else:
            x = torch.randint(0, 256, shape, dtype=dtype, device="cuda")
        ar_kit.get_input_tensor(x.shape, x.dtype).copy_(x)
        ffwd_result = ar_kit.all_gather()
        nccl_result = torch.zeros(
            num_processes * x.shape[0],
            *x.shape[1:],
            dtype=dtype,
            device="cuda",
        )
        dist.all_gather_into_tensor(nccl_result, x)
        torch.testing.assert_close(ffwd_result, nccl_result)

    return fn


def _init_all_gather_inplace(
    max_input_elements: int,
    dtype: torch.dtype,
    process_idx: int,
    num_processes: int,
):
    assert num_processes == dist.get_world_size()
    assert process_idx == dist.get_rank()
    ag_kit = all_reduce.AllGatherKit(
        max_input_elements,
        dtype,
        process_group=check_not_none(dist.group.WORLD),
    )
    torch.manual_seed(1234 + process_idx)

    def fn(input_shape: torch.Size):
        assert input_shape.numel() <= max_input_elements
        if dtype.is_floating_point:
            x = torch.rand(input_shape, dtype=dtype, device="cuda")
        else:
            x = torch.randint(0, 256, input_shape, dtype=dtype, device="cuda")
        ag_kit.get_input_tensor(input_shape).copy_(x)
        ffwd_result = ag_kit.all_gather(input_shape)
        nccl_result = torch.zeros(
            num_processes * input_shape[0],
            *input_shape[1:],
            dtype=dtype,
            device="cuda",
        )
        dist.all_gather_into_tensor(nccl_result, x)
        torch.testing.assert_close(ffwd_result, nccl_result)

    return fn


@pytest.mark.parametrize("dtype", [torch.uint8, torch.bfloat16, torch.float32])
def test_all_gather(
    dtype: torch.dtype,
):
    shapes = [(1024,), (32, 32), (16, 16, 16), (8, 8, 8, 8)]
    max_elements = max(torch.prod(torch.tensor(shape)).item() for shape in shapes)
    with parallel.ParallelRunner(num_processes=2) as runner:
        mp_fn = runner.initialize(
            _init_all_gather, max_elements=max_elements, dtype=dtype
        )
        for shape in shapes:
            mp_fn(shape)

    with parallel.ParallelRunner(num_processes=2) as runner:
        mp_fn = runner.initialize(
            _init_all_gather_inplace, max_input_elements=max_elements, dtype=dtype
        )
        for shape in shapes:
            mp_fn(torch.Size(shape))
