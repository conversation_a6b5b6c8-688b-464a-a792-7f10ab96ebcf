#include <stddef.h>
#include <stdint.h>
#include <torch/extension.h>

static constexpr size_t MAX_RANKS_PER_NODE = 8;

struct AllReduceParams {
    bool one_shot = true;
    size_t elts_total;
    size_t elts_per_rank;
    size_t elts_per_block;
    size_t local_rank;
    size_t world_size;
    size_t blocks_per_grid;
    size_t threads_per_block;

    uint32_t* gpu_barrier_ptrs[MAX_RANKS_PER_NODE];
    uint32_t* block_barrier_ptrs[MAX_RANKS_PER_NODE];

    void* peer_comm_buffer_ptrs[MAX_RANKS_PER_NODE];
    void* local_output_buffer_ptr;
    void* local_residual_buffer_ptr;

    bool use_fp8 = false;
    float* fp8_scale = nullptr;
};

int do_all_reduce(AllReduceParams params, torch::Tensor& output);
void do_all_gather(AllReduceParams params, torch::Tensor& output);
void do_all_gather_inplace(AllReduceParams params, torch::Tensor& output);

struct BarrierParams {
    size_t local_rank;
    size_t world_size;
    uint32_t* gpu_barrier_ptrs[MAX_RANKS_PER_NODE];
};

void do_barrier(BarrierParams params);
