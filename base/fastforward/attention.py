"""Grouped query attention (or multiple queries per head) as in https://arxiv.org/abs/2305.13245."""

from typing import Optional

import torch
import torch.nn.functional as F

# Constant to determine the chunk size for the queries in the sequence length.
# Currently, the implementation guarantees that the memory requirement is
# linear in the sequence length and the batch size and the number of heads.
# In the future we could improve the memory requirement to constant in the
# sequence length (via flash attention), and constant in the batch and head
# dimension (via chunking over those dimensions).
#
# Tuned for training with queries_per_head=8; long sequence lengths; likely
# a good baseline for other sizes.
Q_CHUNK_LEN = 512

# TODO: memory-efficient version
# TODO: support common prefix for key and value via memory-efficient attention trick.
# TODO: use sub-batches to limit memory usage further


def causal_mask(q_len: int, kv_len: int, device: str = "cuda"):
    q_idxs = torch.arange(q_len, device=device).view(1, 1, q_len, 1)
    kv_idxs = torch.arange(kv_len, device=device).view(1, 1, 1, kv_len)
    return q_idxs >= kv_idxs


def window_len_mask(q_len: int, kv_len: int, window_len: int, device: str = "cuda"):
    q_idxs = torch.arange(q_len, device=device).view(1, 1, q_len, 1)
    kv_idxs = torch.arange(kv_len, device=device).view(1, 1, 1, kv_len)
    return q_idxs - window_len < kv_idxs


def gqa_attention(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    mask: Optional[torch.Tensor] = None,
    is_causal: bool = False,
    window_len: Optional[int] = None,
) -> torch.Tensor:
    """Grouped query attention (or multiple queries per head).

    Similar to https://arxiv.org/abs/2305.13245.

    Additional features:
    - Saves work when causal attention masks are given (if is_causal is set).
    - Saves work for local attention; see window_len.
    - Supports float16, float32, probably bfloat16.

    Args:
        query: Tensor of shape (batch, heads, queries_per_head, q_len, qk_dim)
        key: Tensor of shape (batch, heads, kv_len, qk_dim)
        value: Tensor of shape (batch, heads, kv_len, value_dim)
        mask: Tensor of shape (batch, heads, q_len, kv_len) of type bool.
            `True` indicates that the entry is kept (same semantics as in
            F.scaled_dot_product_attention and Google implementations).
        is_causal: flag indicating that the given mask is causal.
        window_len: an int indicating window length encoded in the given mask.

    Returns:
        Tensor of shape (batch, heads, queries_per_head, q_len, value_dim)
    """
    device: str = query.device  # type: ignore
    dtype = query.dtype
    batch, heads, queries_per_head, q_len, qk_dim = query.size()
    kv_len, value_dim = value.size()[-2:]
    if is_causal and q_len > kv_len:
        raise ValueError(
            "Unclear how to align sequence lengths for causal mask."
            "q_len: %s, kv_len: %s" % (q_len, kv_len)
        )
    # If there are more kv than queries, we align the queries on the right side
    # with the kv tensor for causal masking and windowed attention.
    q_to_kv_offset = kv_len - q_len
    assert key.size()[:-1] == value.size()[:-1]
    assert key.size()[-1] == qk_dim
    assert window_len is None or window_len > 0

    scaled_query = query * (qk_dim**-0.5)
    del query  # protect against accidental use
    # flatten batch and head dim to enable easier splitting
    scaled_query = scaled_query.view(batch * heads, queries_per_head, q_len, qk_dim)
    key = key.view(batch * heads, kv_len, qk_dim)
    value = value.view(batch * heads, kv_len, value_dim)
    if mask is not None:
        mask = mask.view(
            -1, q_len, kv_len
        )  # -1 because mask may not be broadcasted yet

    efficient_causal_mask = is_causal and mask is None
    assert (
        not efficient_causal_mask or window_len is None
    ), "Efficient causal mask handling does not work yet with window_len."
    q_chunk_len = min(Q_CHUNK_LEN, q_len)

    attn_result = torch.empty(
        size=(batch * heads, queries_per_head, q_len, value_dim),
        dtype=dtype,
        device=device,
    )

    # tensor.split shares the underlying memory, so it is a no-op for `not is_causal`.
    for query_chunk_idx, query_chunk in enumerate(
        scaled_query.split(q_chunk_len, dim=-2)
    ):
        this_q_chunk_len = min(q_chunk_len, query_chunk.size()[-2])
        assert query_chunk.size()[-2] == this_q_chunk_len, (
            query_chunk.size(),
            this_q_chunk_len,
        )
        q_start_idx = query_chunk_idx * q_chunk_len
        q_end_idx = query_chunk_idx * q_chunk_len + this_q_chunk_len
        kv_start_idx = 0
        kv_end_idx = kv_len
        chunk_mask = mask
        if chunk_mask is not None:
            chunk_mask = chunk_mask[:, q_start_idx:q_end_idx, :]
        if is_causal:
            kv_end_idx = q_end_idx + q_to_kv_offset
        if window_len is not None:
            assert mask is not None
            # window len includes current position, so +1
            kv_start_idx = max(0, q_start_idx + q_to_kv_offset - window_len + 1)

        kv_chunk_len = kv_end_idx - kv_start_idx
        assert kv_chunk_len >= 0

        key_chunk = key[:, kv_start_idx:kv_end_idx, :]
        value_chunk = value[:, kv_start_idx:kv_end_idx, :]
        if chunk_mask is not None:
            chunk_mask = chunk_mask[:, :, kv_start_idx:kv_end_idx]

        # TODO: use sub-batches to limit memory usage
        # sub_batch_size = q_len * kv_len * qk_dim // 10**10
        # sub_batch_size = max(sub_batch_size, 1)
        # sub_batch_size = min(sub_batch_size, batch)
        # print(f"{sub_batch_size=}")

        # n is queries_per_head
        # q is q_seq_len
        # k is seq_len
        # f is head_dim
        # In performance tests einsum turned out to be faster than matmul.
        scores = torch.einsum("...nqf,...kf->...nqk", query_chunk, key_chunk)
        if dtype == torch.bfloat16:
            # do masking and softmax in float32
            scores = scores.to(torch.float32)
        scores_size = tuple(scores.size())
        assert scores_size == (
            batch * heads,
            queries_per_head,
            this_q_chunk_len,
            kv_chunk_len,
        ), (
            scores_size,
            (batch * heads, queries_per_head, this_q_chunk_len, kv_chunk_len),
        )
        if chunk_mask is not None:
            chunk_mask = chunk_mask.unsqueeze(1)  # add queries_per_head dim

            # Ablated the performance of where against other ways to apply mask.
            # The only other competitive method is `scores.masked_fill_(...)`,
            # but this needs flipped (megatron) mask semantics.
            neg_inf = torch.tensor([float("-inf")], device=device).to(scores.dtype)
            torch.where(chunk_mask, scores, neg_inf, out=scores)
        elif efficient_causal_mask:
            assert chunk_mask is None
            chunk_mask = causal_mask(this_q_chunk_len, this_q_chunk_len, device=device)
            neg_inf = torch.tensor([float("-inf")], device=device).to(scores.dtype)
            scores_to_mask = scores[:, :, :, -this_q_chunk_len:]
            torch.where(chunk_mask, scores_to_mask, neg_inf, out=scores_to_mask)
        # For some reason, inplace softmax costs us performance...
        probs = torch.softmax(scores, dim=-1)
        if dtype == torch.bfloat16:
            probs = probs.to(torch.bfloat16)
        assert probs.size() == (
            batch * heads,
            queries_per_head,
            this_q_chunk_len,
            kv_chunk_len,
        )
        assert value_chunk.size() == (batch * heads, kv_chunk_len, value_dim)
        # Ablated einsum here against inplace matmul and it is faster,
        # unexpectedly.
        attn_result[:, :, q_start_idx:q_end_idx, :] = torch.einsum(
            "...nqk,...kf->...nqf", probs, value_chunk
        )
    assert attn_result.dtype == dtype
    return attn_result.view(batch, heads, queries_per_head, q_len, value_dim)


def gqa_flash(query, key, value, is_causal: bool = False):
    """Simulate GQA via flash attention until we have a proper kernel for it.

    This function works only if:
    - head_dim <= 128
    - we have no attention mask (besides causal attention)

    The function is only faster in for q_len >> 1, so don't use it for
    inference.

    Args:
    query: Tensor of shape (batch, heads, queries_per_head, q_len, qk_dim)
    key: Tensor of shape (batch, heads, kv_len, qk_dim)
    value: Tensor of shape (batch, heads, kv_len, value_dim)
    is_causal: flag indicating that the given mask is causal.

    """
    batch, heads, queries_per_head, q_len, qk_dim = query.size()
    query = query.view(batch, heads * queries_per_head, q_len, qk_dim)
    kv_len = key.size()[2]
    assert key.size() == (batch, heads, kv_len, qk_dim), key.size()
    key = torch.repeat_interleave(key, repeats=queries_per_head, dim=1)
    value = torch.repeat_interleave(value, repeats=queries_per_head, dim=1)
    assert key.size() == (batch, heads * queries_per_head, kv_len, qk_dim), key.size()
    assert key.size() == value.size()
    if is_causal and q_len != kv_len:
        raise NotImplementedError("Need to implement an offset for causal mask.")

    with torch.backends.cuda.sdp_kernel(  # type: ignore
        enable_flash=True, enable_math=False, enable_mem_efficient=False
    ):
        result = F.scaled_dot_product_attention(query, key, value, is_causal=is_causal)
        return result.view(batch, heads, queries_per_head, q_len, qk_dim)
