"""Multi-request flash attention module."""

import logging

import flash_attn
import torch

MAX_REQUESTS_IN_ROUND_DEFAULT = 8
SMALL_REQUEST_MAX_SEQLEN_DEFAULT = 16


class MultiRequestFlashAttention:
    """Wrapper class to support flash attention for multi-request batches."""

    def __init__(
        self,
        nheads_q: int,
        headdim: int,
        max_round_size: int,
        max_requests_in_round: int | None = None,
        max_large_requests_in_round: int = 1,
        small_request_max_seqlen: int | None = None,
        dtype=torch.bfloat16,
        device: str | torch.device = "cuda",
        use_flash_attn_v3: bool = False,
        use_fp8: bool = False,
        # Special case parameter: add an adjustment to produce a larger q_buf than the given
        # nheads_q would imply when we are running a Q_PER_HEADS model under sequence parallel.
        # In Q_PER_HEADS mode, the weights and attention state can be shared between tensor and
        # sequence parallel models but all head splitting is currently computed as if for tensor
        # parallel only. When a running sequence parallel step function we need to invert the
        # head-splitting computation (i.e. "unsplit" the heads) to account for this.
        sequence_parallel_q_scale_factor: int = 1,
    ):
        if max_requests_in_round is None:
            max_requests_in_round = MAX_REQUESTS_IN_ROUND_DEFAULT
        if small_request_max_seqlen is None:
            small_request_max_seqlen = SMALL_REQUEST_MAX_SEQLEN_DEFAULT
        if (
            max_large_requests_in_round > max_requests_in_round
            or max_large_requests_in_round < 1
        ):
            raise ValueError(
                f"max_large_requests_in_round ({max_large_requests_in_round}) must be "
                f"<= max_requests_in_round ({max_requests_in_round}) and >= 1."
            )
        logging.info(
            "Initializing MultiRequestFlashAttention with max_round_size=%d, max_requests_in_round=%d, max_large_requests_in_round=%d, small_request_max_seqlen=%d, use_flash_attn_v3=%s, use_fp8=%s.",
            max_round_size,
            max_requests_in_round,
            max_large_requests_in_round,
            small_request_max_seqlen,
            use_flash_attn_v3,
            use_fp8,
        )
        self.nheads_q = nheads_q
        self.headdim = headdim
        self.num_requests = max_requests_in_round
        self.num_large_requests = max_large_requests_in_round
        self.num_small_requests = max_requests_in_round - max_large_requests_in_round
        self.small_request_max_seqlen = small_request_max_seqlen
        self.max_round_size = max_round_size
        self.dtype = dtype
        self.use_fp8 = use_fp8
        self.sequence_parallel_q_scale_factor = sequence_parallel_q_scale_factor
        if use_fp8:
            q_dtype = torch.float8_e4m3fn
        else:
            q_dtype = dtype
        # See NOTE(flat-q-buf) below.
        self.flat_q_buf_large = torch.zeros(
            self.num_large_requests
            * max_round_size
            * nheads_q
            * sequence_parallel_q_scale_factor
            * headdim,
            dtype=q_dtype,
            device=device,
        )
        self.q_buf_small_unsplit = (
            None
            if sequence_parallel_q_scale_factor == 1
            else torch.zeros(
                (
                    self.num_small_requests,
                    small_request_max_seqlen,
                    nheads_q * sequence_parallel_q_scale_factor,
                    headdim,
                ),
                dtype=q_dtype,
                device=device,
            )
        )
        self.q_buf_small = torch.zeros(
            (self.num_small_requests, small_request_max_seqlen, nheads_q, headdim),
            dtype=q_dtype,
            device=device,
        )
        self.cache_seqlens_buf = torch.zeros(
            (self.num_requests,), dtype=torch.int32, device=device
        )
        self.cache_batch_idxs_buf = torch.zeros(
            (self.num_requests,), dtype=torch.int32, device=device
        )
        # Note that we need to allocate on `__call__`, since we can't send a Stream object over a
        # multiprocessing queue.
        self.small_request_stream = None
        # TODO(carl): have caller produce the inverse scales
        self.one = torch.ones(1, dtype=torch.float32, device=device)

        # Allow use of FA3 only when requested _and_ we are compute capability 9.0 (H100) or later
        if use_flash_attn_v3:
            cuda_major, _ = torch.cuda.get_device_capability()
            if cuda_major < 9:
                raise RuntimeError(
                    "flash-attn v3 requires compute capability 9.0 (H100) or later."
                )
            if headdim not in (64, 128):
                raise RuntimeError(
                    f"flash-attn v3 is tested for only headdim=64 or 128, but got headdim={headdim}."
                )
            # Ugh: import away from top-level because the current FA3 entrypoing loads cuda code.
            # As a result, the top-level import will crash CPU clients.
            # TODO(carl): get the FA3 entrypoint updated to avoid this (as FA2 does).
            import flash_attn_interface as flash_attn3  # pylint: disable=import-outside-toplevel

            self.flash_attn_fn = flash_attn3.flash_attn_with_kvcache
        else:
            self.flash_attn_fn = flash_attn.flash_attn_with_kvcache

    def __call__(
        self,
        q: torch.Tensor,
        k_cache: torch.Tensor,
        v_cache: torch.Tensor,
        cumulative_request_seqlens: torch.Tensor,
        cache_idxs: torch.Tensor,
        cache_seqlens: torch.Tensor,
        sequence_parallel_rank: int | None = None,
        use_sequence_parallel: bool = False,
        attn_qkv_scales: torch.Tensor | None = None,
    ) -> torch.Tensor:
        """Entrypoint for attention on a round / batch.
        Args:
            q: (round_size, nheads, headdim)
            k_cache: (num_caches, cache_seqlen, nheads_kv, headdim)
            v_cache: (num_caches, cache_seqlen, nheads_kv, headdim)
            cumulative_request_seqlens: (requests_in_round + 1,)
            cache_idxs: (round_size,)
            cache_seqlens: (round_size,)
            sequence_parallel_rank: int, the rank of this process in its sequence parallel group.
                This is needed with `use_sequence_parallel=True` to split the
                `cumulative_request_seqlens` tensor.
            use_sequence_parallel: whether to use sequence parallelism.
            attn_qkv_scales: (3,) # scales for Q/K/V, respectively
        Returns:
            (round_size, nheads, headdim)

        For any req, its indices in `q` (and the output) are:
        - start=cumulative_request_seqlens[req]
        - end=cumulative_request_seqlens[req+1]

        SEE NOTE(round-structure) below for a description of the assumed round structure.
        """
        if len(cumulative_request_seqlens) != self.num_requests + 1:
            raise ValueError(
                f"cumulative_request_seqlens must have {self.num_requests + 1} elements, "
                f"but has {len(cumulative_request_seqlens)}."
            )
        round_size = q.size(0)
        if round_size > self.max_round_size:
            raise ValueError(f"{round_size=} > {self.max_round_size=}.")
        if self.use_fp8 != (attn_qkv_scales is not None):
            raise ValueError("Pass attn_qkv_scales iff use_fp8=True.")
        assert len(cache_idxs) == round_size
        assert len(cache_seqlens) == round_size
        result = torch.empty_like(
            q, dtype=self.dtype
        )  # write_trailing_zeros=True ensures this is zeroed

        # Split cumulative_request_seqlens for sequence parallelism
        if use_sequence_parallel:
            assert sequence_parallel_rank is not None
            # pylint: disable=import-outside-toplevel,no-name-in-module
            from base.fastforward.compiled_attention_utils import (
                split_cumulative_pos_for_sequence_parallel,
            )
            # pylint: enable=import-outside-toplevel,no-name-in-module

            cumulative_request_seqlens = split_cumulative_pos_for_sequence_parallel(
                cumulative_request_seqlens,
                round_size,
                sequence_parallel_rank,
            )

        if self.use_fp8:
            inverse_qkv_scales = self.one / attn_qkv_scales
            assert q.dtype == torch.float8_e4m3fn
            assert k_cache.dtype == torch.float8_e4m3fn
            assert v_cache.dtype == torch.float8_e4m3fn
        else:
            inverse_qkv_scales = None

        # Allocate side-stream on the first call
        if self.small_request_stream is None:
            self.small_request_stream = torch.cuda.Stream()

        # NOTE(flat-q-buf)
        # Two things going on here:
        # 1. This is a dynamic slice (bad for cuda graphs), but the only variable is `round_size`,
        #    and there is a graph per-round-size.
        # 2. Since `round_size` isn't the leading dimension, we cannot just slice on its dim and
        #    preserve contiguity. Instead we take a contiguous prefix of the flat buffer of the
        #    correct size and then view it as needed.
        num_needed_elems = (
            self.num_large_requests
            * round_size
            * self.nheads_q
            * self.sequence_parallel_q_scale_factor
            * self.headdim
        )
        q_buf_large_slice = self.flat_q_buf_large[:num_needed_elems].view(
            self.num_large_requests,
            round_size,
            self.nheads_q * self.sequence_parallel_q_scale_factor,
            self.headdim,
        )
        assert q_buf_large_slice.is_contiguous()

        q_buf_small = (
            self.q_buf_small_unsplit
            if use_sequence_parallel and self.q_buf_small_unsplit is not None
            else self.q_buf_small
        )

        # Ensure that the small request stream waits for all work up to this point to be done
        main_stream = torch.cuda.current_stream()
        self.small_request_stream.wait_stream(main_stream)  # type: ignore
        attn_results = []
        # NOTE(round-structure)
        # This loop depends on the structure of the round. We are assuming, in order:
        # - `num_large_requests` large requests (up to full-round size)
        # - `num_small_requests` small requests (up to small_request_max_seqlen)
        #
        # The `requests_slice` at each iteration tells you what to slice from
        # `cumulative_request_seqlens` for that iteration. They are, in order:
        # - [:num_large_requests + 1] -- the leading large requests
        # - [num_large_requests:] -- the trailing small requests
        # Note that in both cases you have one more element than the number of requests, since
        # just as in `cumulative_request_seqlens`.
        #
        # `cache_slice` tells you how much of cache_{seqlens,batch_idxs}_buf you need:
        # - The `(None, num_large_requests)` slice for the large requests
        # - The `(num_large_requests, None)` slice for the small requests
        # fmt: off
        for this_qbuf, requests_slice, cache_slice, stream in [
            (q_buf_large_slice, slice(None, self.num_large_requests + 1), slice(None, self.num_large_requests), main_stream),
            (q_buf_small, slice(self.num_large_requests, None), slice(self.num_large_requests, None), self.small_request_stream),
        ]:
            # It's possible to have _no_ small requests -- skip calling kernels in this case.
            # You can't have no large requests since the constructor asserts there is at least one.
            # (In general you should expect at least one, as otherwise it's kind of weird to have
            # a full round_size worth of tokens.)
            if this_qbuf.numel() == 0:
                assert self.num_small_requests == 0
                continue
            with torch.cuda.stream(stream):
                attn_results.append(
                    self._attn(
                        q,
                        this_qbuf,
                        k_cache,
                        v_cache,
                        cumulative_request_seqlens[requests_slice],
                        cache_idxs,
                        cache_seqlens,
                        self.cache_seqlens_buf[cache_slice],
                        self.cache_batch_idxs_buf[cache_slice],
                        inverse_qkv_scales,
                    )
                )

        # pylint: disable=import-outside-toplevel,no-name-in-module
        from base.fastforward.compiled_attention_utils import (
            multirequest_flash_copy_out_results,
        )
        # pylint: enable=import-outside-toplevel,no-name-in-module
        # fmt: on

        # NOTE(multistream-copy-out): the following code is a little subtle. At this point, we have
        # scheduled work like this (s0=main_stream, s1=small_request_stream)
        #   [s0] * -> copy_in+copy_metadata(big_req) ----> attn(big_req)
        #         \
        #   [s1]   -> copy_in+copy_metadata(small_reqs) -> attn(small_reqs)
        #
        # The remaining work is to copy out the results for both the big req and the small reqs.
        # Because of the behavior of `write_trailing_zeros`, we need to do things in the following
        # order:
        #   1) Write out the big_reqs result with write_trailing_zeros=True. This fills the result
        #      with the true result for the big request and puts zeros everywhere else.
        #   2) Write out the small_reqs with write_trailing_zeros=False. This fills the remaining
        #      needed values in the result tensor and leaves zeros as appropriate.
        # (1) can run as soon as attn(big_req) is done, so we place it immediately in the main
        # stream. (2) must run after (1). We do so by having the main stream wait on the side
        # stream and then running (2) in the main stream itself. The picture now looks like:
        #   [s0] ...attn(big_req) -> copy_out(big_req, zeros=True) -> * -> copy_out(small_reqs, zeros=False)
        #                                                            /
        #   [s1] ...attn(small_reqs) --------------------------------

        # Another note: the `write_trailing_zeros` argument (that lets us use uninitialized `result`)
        # works only for single-request inputs. (See `multirequest_flash_copy_queries_kernel` in
        # `compiled_attention_utils.cu` for details.) So we manually zero-out here for the multiple
        # large request case.
        # TODO(carl): extend the kernel to support multiple large requests.
        if self.num_large_requests > 1:
            result.zero_()
        multirequest_flash_copy_out_results(
            attn_results[0],
            result,
            cumulative_request_seqlens[: self.num_large_requests + 1],
            self.num_large_requests == 1,  # `write_trailing_zeros`
        )
        main_stream.wait_stream(self.small_request_stream)
        if len(attn_results) > 1:
            multirequest_flash_copy_out_results(
                attn_results[1],
                result,
                cumulative_request_seqlens[self.num_large_requests :],
                False,
            )
        else:
            assert self.num_small_requests == 0
        return result

    def _attn(
        self,
        q: torch.Tensor,
        q_buf: torch.Tensor,
        k_cache: torch.Tensor,
        v_cache: torch.Tensor,
        cumulative_request_seqlens_slice: torch.Tensor,
        cache_idxs: torch.Tensor,
        cache_seqlens: torch.Tensor,
        cache_seqlens_buf_slice: torch.Tensor,
        cache_batch_idxs_buf_slice: torch.Tensor,
        inverse_qkv_scales: torch.Tensor | None,
    ):
        """Performs the attention operation itself."""
        # We need to put these imports at the call site s.t. CPU-only users can (transitively)
        # import this module w/o getting a "can't find libcuda" error.
        # pylint: disable=import-outside-toplevel,no-name-in-module
        from base.fastforward.compiled_attention_utils import (
            multirequest_flash_copy_cache_metadata,
            multirequest_flash_copy_in_queries,
        )

        # pylint: enable=import-outside-toplevel,no-name-in-module

        assert q.is_contiguous()
        assert q_buf.is_contiguous()

        # 1) Copy a batch of queries _right-aligned_ into the static input buffer:
        # - `cumulative_request_seqlens_slice` tells you (start, end) for each request
        # - For each request, copies q[start:end, ...] to q_buf[i, buf_seqlen - this_seqlen, ...]
        # We need right-alignment because flash attention right-aligns queries with the KV cache
        # when applying the causal attention mask.
        multirequest_flash_copy_in_queries(
            q,
            q_buf,
            cumulative_request_seqlens_slice,
        )
        # 2) Copy `cache_idxs` and `cache_seqlens` (each ntokens-sized) to the static input
        #    buffers for flash attention (each nrequests-sized).
        multirequest_flash_copy_cache_metadata(
            cumulative_request_seqlens_slice,
            cache_idxs,
            cache_seqlens,
            cache_batch_idxs_buf_slice,
            cache_seqlens_buf_slice,
        )
        if self.use_fp8:
            assert inverse_qkv_scales is not None
            dequant_args = {
                "descale_q": inverse_qkv_scales[0].unsqueeze(0),
                "descale_k": inverse_qkv_scales[1].unsqueeze(0),
                "descale_v": inverse_qkv_scales[2].unsqueeze(0),
            }
        else:
            dequant_args = {}
        # 3) Attention
        ret = self.flash_attn_fn(
            q=q_buf,
            k_cache=k_cache,
            v_cache=v_cache,
            cache_seqlens=cache_seqlens_buf_slice,
            cache_batch_idx=cache_batch_idxs_buf_slice,
            causal=True,
            **dequant_args,  # type: ignore
        )
        if ret.dtype != self.dtype:  # type: ignore
            # FA3 fp8 always returns bfloat16. The caller might have asked for fp16, so we cast it
            # here. This will go away one fp8 output is supported.
            ret = ret.to(self.dtype)  # type: ignore
        return ret
