"""Tests for base.fastforward.compiled_attention_utils."""

import pytest
import torch

from base.fastforward.compiled_attention_utils import (
    split_cumulative_pos_for_sequence_parallel,
)


def _split_cumulative_pos_for_sequence_parallel_for_tests(
    cumulative_pos: torch.Tensor,
    num_tokens_per_device: int,
    process_idx: int,
    use_kernel: bool = False,
):
    """Python implementation of the split_cumulative_pos_for_sequence_parallel kernel."""
    if use_kernel:
        return split_cumulative_pos_for_sequence_parallel(
            cumulative_pos, num_tokens_per_device, process_idx
        )
    split = torch.zeros_like(cumulative_pos)
    start_pos_this_device = process_idx * num_tokens_per_device
    end_pos_this_device = (process_idx + 1) * num_tokens_per_device
    num_requests_this_device = 0
    for pos in cumulative_pos[1:]:
        end_position_this_request_on_this_device = min(int(pos), end_pos_this_device)
        if end_position_this_request_on_this_device <= start_pos_this_device:
            continue
        split[num_requests_this_device + 1] = (
            end_position_this_request_on_this_device - start_pos_this_device
        )
        num_requests_this_device += 1
    # fill remaining positions by repeating the last value
    split[num_requests_this_device:].fill_(split[num_requests_this_device])
    return split


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_cumulative_pos_for_sequence_parallel(use_kernel: bool):
    cumulative_pos = torch.tensor([0, 10, 20, 30, 30], dtype=torch.int32, device="cuda")
    num_tokens_per_device = 16
    process_idx = 0
    expected = torch.tensor([0, 10, 16, 16, 16], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_cumulative_pos_for_sequence_parallel_2(use_kernel: bool):
    cumulative_pos = torch.tensor([0, 10, 20, 30, 30], dtype=torch.int32, device="cuda")
    num_tokens_per_device = 16
    process_idx = 1
    expected = torch.tensor([0, 4, 14, 14, 14], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_cumulative_pos_for_sequence_parallel_single_request(use_kernel: bool):
    cumulative_pos = torch.tensor(
        [0, 512, 512, 512, 512], dtype=torch.int32, device="cuda"
    )
    num_tokens_per_device = 256
    process_idx = 0
    expected = torch.tensor([0, 256, 256, 256, 256], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)
    process_idx = 1
    expected = torch.tensor([0, 256, 256, 256, 256], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_cumulative_pos_for_sequence_parallel_empty(use_kernel: bool):
    cumulative_pos = torch.tensor([0, 0, 0, 0, 0], dtype=torch.int32, device="cuda")
    num_tokens_per_device = 256
    process_idx = 0
    expected = torch.tensor([0, 0, 0, 0, 0], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)

    process_idx = 1
    expected = torch.tensor([0, 0, 0, 0, 0], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_cumulative_pos_for_sequence_parallel_first_req_split(use_kernel: bool):
    cumulative_pos = torch.tensor(
        [0, 300, 316, 332, 332], dtype=torch.int32, device="cuda"
    )
    num_tokens_per_device = 256
    process_idx = 0
    expected = torch.tensor([0, 256, 256, 256, 256], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)
    process_idx = 1
    expected = torch.tensor([0, 44, 60, 76, 76], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_cumulative_pos_for_sequence_parallel_all_first(use_kernel: bool):
    cumulative_pos = torch.tensor(
        [0, 100, 200, 250, 250], dtype=torch.int32, device="cuda"
    )
    num_tokens_per_device = 256
    process_idx = 0
    expected = torch.tensor([0, 100, 200, 250, 250], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)
    process_idx = 1
    expected = torch.tensor([0, 0, 0, 0, 0], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_cumulative_four_devices(use_kernel: bool):
    cumulative_pos = torch.tensor(
        [0, 100, 200, 300, 400], dtype=torch.int32, device="cuda"
    )
    num_tokens_per_device = 128
    process_idx = 0
    expected = torch.tensor([0, 100, 128, 128, 128], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)
    process_idx = 1
    expected = torch.tensor([0, 72, 128, 128, 128], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)
    process_idx = 2
    expected = torch.tensor([0, 44, 128, 128, 128], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)
    process_idx = 3
    expected = torch.tensor([0, 16, 16, 16, 16], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


@pytest.mark.parametrize("use_kernel", [True, False])
def test_split_exactly_at_256(use_kernel: bool):
    cumulative_pos = torch.tensor([0, 256, 300, 400], dtype=torch.int32, device="cuda")
    num_tokens_per_device = 256
    process_idx = 0
    expected = torch.tensor([0, 256, 256, 256], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)
    process_idx = 1
    expected = torch.tensor([0, 44, 144, 144], dtype=torch.int32, device="cuda")
    actual = _split_cumulative_pos_for_sequence_parallel_for_tests(
        cumulative_pos, num_tokens_per_device, process_idx, use_kernel=use_kernel
    )
    assert torch.equal(actual, expected)


def test_split_is_graphable():
    """Test that the compiled kernel is graphable."""

    cumulative_pos = torch.tensor(
        [0, 100, 200, 300, 400], dtype=torch.int32, device="cuda"
    )
    num_tokens_per_device = 128
    process_idx = 0

    # Create a CUDA graph
    s = torch.cuda.Stream()
    g = torch.cuda.CUDAGraph()

    with torch.cuda.stream(s):
        # Warmup
        _ = _split_cumulative_pos_for_sequence_parallel_for_tests(
            cumulative_pos, num_tokens_per_device, process_idx, use_kernel=True
        )

    # Capture the graph
    with torch.cuda.graph(g):
        result = _split_cumulative_pos_for_sequence_parallel_for_tests(
            cumulative_pos, num_tokens_per_device, process_idx, use_kernel=True
        )

    # Run the graph
    g.replay()

    # Verify the result
    expected = torch.tensor([0, 100, 128, 128, 128], dtype=torch.int32, device="cuda")
    assert torch.equal(result, expected)

    # Test with different input
    cumulative_pos.copy_(
        torch.tensor([0, 50, 150, 250, 350], dtype=torch.int32, device="cuda")
    )
    g.replay()
    expected = torch.tensor([0, 50, 128, 128, 128], dtype=torch.int32, device="cuda")
    assert torch.equal(result, expected)

    # Clean up
    del g
    torch.cuda.empty_cache()
