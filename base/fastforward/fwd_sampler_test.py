"""Tests for fwd_model_output_test."""

import pytest
import torch

from base.fastforward import fwd_sampler, fwd_torch

_SIMPLE_TEST_CASE = [
    list(range(9)),
    list(reversed(range(9))),
    [1] * 4 + [2] + [1] * 4,
    [1] * 3 + [2] * 3 + [3.1] + [3] * 2,
]
_SIMPLE_TEST_CASE_ARGMAX = [8, 0, 4, 6]


@pytest.mark.parametrize("top_k", [1])
@pytest.mark.parametrize("temperature", [None, 1.0, 0.1, 0.5])
@pytest.mark.parametrize("device", [torch.device("cpu"), torch.device("cuda")])
@pytest.mark.parametrize("dtype", [torch.float32, torch.float16, torch.bfloat16])
def test_torch_sample_same_as_argmax(
    top_k: int | None,
    temperature: float | None,
    device: torch.device,
    dtype: torch.dtype,
):
    """Tests whether TorchLogits2D works as expected."""
    torch_tensor = torch.tensor(_SIMPLE_TEST_CASE, dtype=dtype, device=device)
    logits = fwd_torch.TorchLogits2D(torch_tensor)
    assert id(logits.checked_cast(torch.Tensor)) == id(torch_tensor)
    sampler = fwd_sampler.Sampler(rng_seed=1)
    # Test the sample_with_log_prob function
    sampled_tokens, _, _ = sampler.sample_with_log_prob(
        logits,
        fwd_sampler.SamplingConfig(top_k=top_k, temperature=temperature),
    )
    assert sampled_tokens == _SIMPLE_TEST_CASE_ARGMAX


@pytest.mark.parametrize("shape", [(64, 16)])
@pytest.mark.parametrize("temperature", [0.2, 1.0, 20.0])
@pytest.mark.parametrize("device", [torch.device("cpu"), torch.device("cuda")])
@pytest.mark.parametrize("dtype", [torch.float32, torch.float16, torch.bfloat16])
def test_torch_logits_2d_dist_match(
    shape: tuple[int, ...],
    temperature: float,
    device: torch.device,
    dtype: torch.dtype,
):
    """Check the sampling distribution vs. the expected distribution."""
    torch_tensor = torch.randn(
        *shape,
        generator=torch.Generator(device=device).manual_seed(9),
        device=device,
        dtype=dtype,
    )
    logits = fwd_torch.TorchLogits2D(torch_tensor)
    sampler = fwd_sampler.Sampler(rng_seed=9)
    counts = torch.zeros(shape[-1])
    for _ in range(1000):
        tokens, _, _ = sampler.sample_with_log_prob(
            logits,
            fwd_sampler.SamplingConfig(
                top_k=shape[-1],
                temperature=temperature,
            ),
        )
        for x in tokens:
            counts[x] += 1
    sample_dist = counts / sum(counts)
    expected_dist = torch.mean(
        torch.softmax(logits.checked_cast(torch.Tensor).float() / temperature, dim=-1),
        dim=0,
    ).cpu()
    torch.testing.assert_close(
        sample_dist,
        expected_dist,
        rtol=1e-2,
        atol=1e-2,
    )


@pytest.mark.parametrize(
    "shape,rng_seed,device",
    [
        pytest.param((16, 9), 0, torch.device("cpu"), id="shape_128_9_r0"),
        pytest.param((16, 16), 1, torch.device("cpu"), id="shape_128_16_r1"),
        pytest.param((16, 32), 888, torch.device("cuda"), id="shape_128_32_r888"),
    ],
)
def test_torch_logits_2d_reproducible(
    shape: tuple[int, ...], rng_seed: int, device: torch.device
):
    """Check whether the sampling is reproducible with the same rng_seed."""
    tensor = torch.randn(*shape, device=device)
    logits = fwd_torch.TorchLogits2D(tensor)
    sampler_ref = fwd_sampler.Sampler(rng_seed=rng_seed)
    sampler_repeat = fwd_sampler.Sampler(rng_seed=rng_seed)

    result_ref, _, _ = sampler_ref.sample_with_log_prob(
        logits, fwd_sampler.SamplingConfig(top_k=shape[-1] // 2, temperature=1.0)
    )
    result_repeat, _, _ = sampler_repeat.sample_with_log_prob(
        logits, fwd_sampler.SamplingConfig(top_k=shape[-1] // 2, temperature=1.0)
    )
    assert result_ref == result_repeat

    result_ref_v2, _, _ = sampler_ref.sample_with_log_prob(
        logits, fwd_sampler.SamplingConfig(top_k=shape[-1] // 2, temperature=1.0)
    )
    assert result_ref != result_ref_v2


@pytest.mark.parametrize("device", [torch.device("cpu"), torch.device("cuda")])
@pytest.mark.parametrize("dtype", [torch.float32, torch.float16, torch.bfloat16])
def test_sample_log_prob(device: torch.device, dtype: torch.dtype):
    """Tests the sample_with_log_prob function."""
    token_tensor = torch.tensor(
        [[1.0, 1.0, 1.0, 1.01], [1.0, 2.0, 3.0, 4.0]], dtype=dtype, device=device
    )
    assert list(token_tensor.shape) == [2, 4]
    logits = fwd_torch.TorchLogits2D(token_tensor)
    expected_tokens = [3, 3]
    expected_log_probs = [
        torch.log(torch.tensor([1.01 / (4.01)])).item(),
        torch.log_softmax(torch.tensor([1.0, 2.0, 3.0, 4.0]), dim=-1)[-1].item(),
    ]
    sampler = fwd_sampler.Sampler(rng_seed=0)
    tokens, log_probs, _ = sampler.sample_with_log_prob(
        logits, fwd_sampler.SamplingConfig()
    )
    assert tokens == expected_tokens
    torch.testing.assert_close(
        torch.tensor(log_probs),
        torch.tensor(expected_log_probs),
        rtol=5e-3,
        atol=5e-3,
    )


@pytest.mark.parametrize("device", [torch.device("cpu"), torch.device("cuda")])
@pytest.mark.parametrize("dtype", [torch.float32, torch.float16, torch.bfloat16])
@pytest.mark.parametrize(
    "target_tokens,expected_correct_predictions",
    [
        ([0], [True]),
        ([3, 3], [False, True]),
        ([0, 3], [True, True]),
        ([0, 3, 0], [True, True]),
    ],
)
def test_sample_log_prob_with_target_tokens(
    device: torch.device,
    dtype: torch.dtype,
    target_tokens: list[int],
    expected_correct_predictions: list[bool],
):
    """Tests the sample_with_log_prob function."""
    token_tensor = torch.tensor(
        [[2.0, 1.0, 1.0, 1.0], [1.0, 2.0, 3.0, 4.0]], dtype=dtype, device=device
    )
    assert list(token_tensor.shape) == [2, 4]
    logits = fwd_torch.TorchLogits2D(token_tensor)

    scores = torch.log_softmax(token_tensor.to(dtype=torch.float32), dim=-1)
    expected_log_probs = [
        float(scores[i, t]) for i, t in enumerate(target_tokens) if i < len(scores)
    ]

    sampler = fwd_sampler.Sampler(rng_seed=0)
    tokens, log_probs, correct_predictions = sampler.sample_with_log_prob(
        logits, fwd_sampler.SamplingConfig(), target_tokens=target_tokens
    )
    assert tokens[: len(target_tokens)] == target_tokens[: len(tokens)]
    assert correct_predictions == expected_correct_predictions
    torch.testing.assert_close(
        torch.tensor(log_probs[: len(expected_log_probs)]),
        torch.tensor(expected_log_probs[: len(log_probs)]),
        rtol=5e-3,
        atol=5e-3,
    )
