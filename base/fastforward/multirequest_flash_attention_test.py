"""Tests of MultirequestFlashAttention."""

from typing import Sequence

import flash_attn
import pytest
import torch

from base.fastforward import cuda_graphs
from base.fastforward.compiled_attention_utils_test import (
    random_round_cumulative_seqlens,
)
from base.fastforward.multirequest_flash_attention import (
    MAX_REQUESTS_IN_ROUND_DEFAULT,
    SMALL_REQUEST_MAX_SEQLEN_DEFAULT,
    MultiRequestFlashAttention,
)


# Reference for the fully-batched way of calling flash-attn.
def _reference_batch_flash_attn(
    q: torch.Tensor,
    k_cache: torch.Tensor,
    v_cache: torch.Tensor,
    cache_seqlens: torch.Tensor,
    cache_idxs: torch.Tensor,
    attn_qkv_scales: torch.Tensor | None,
    attn_dtype: torch.dtype,
) -> torch.Tensor:
    # These are no-ops in the non-fp8 case.
    q = q.to(attn_dtype)
    k_cache = k_cache.to(attn_dtype)
    v_cache = v_cache.to(attn_dtype)
    if attn_qkv_scales is not None:
        q = q / attn_qkv_scales[0]
        k_cache = k_cache / attn_qkv_scales[1]
        v_cache = v_cache / attn_qkv_scales[2]

    ntokens, nheads, headdim = q.size()
    return flash_attn.flash_attn_with_kvcache(
        q=q.reshape(ntokens, 1, nheads, headdim),
        k_cache=k_cache,
        v_cache=v_cache,
        cache_seqlens=cache_seqlens,
        cache_batch_idx=cache_idxs,
        causal=True,
    ).reshape(ntokens, nheads, headdim)


# This ensures that the cumulative_seqlens satisfy the small request size constraints. It does so
# by adjusting all requests after the first _down_ in size and moving those tokens to the first
# request.
def _adjust_cumulative_seqlens_for_small_requests(
    cumulative_seqlens: torch.Tensor,
    num_large_requests_in_round: int = 1,
) -> torch.Tensor:
    seqlens = (cumulative_seqlens[1:] - cumulative_seqlens[:-1]).tolist()
    tokens_to_add = 0
    for i in range(num_large_requests_in_round, len(seqlens)):
        this_seqlen = seqlens[i]
        extra_tokens = this_seqlen - SMALL_REQUEST_MAX_SEQLEN_DEFAULT
        if extra_tokens > 0:
            seqlens[i] -= extra_tokens
            tokens_to_add += extra_tokens
    seqlens[0] += tokens_to_add
    return torch.cumsum(
        torch.tensor([0] + seqlens, device="cuda"),
        dim=0,
        dtype=torch.int32,
    )


# Generates random cache_idxs for the given round.
def random_cache_idxs(
    num_caches: int,
    num_tokens: int,
    cumulative_seqlens: torch.Tensor,
    seed: int = 1234,
) -> torch.Tensor:
    torch.manual_seed(seed)
    all_shuffled_idxs = torch.randperm(num_caches, device="cuda")
    result = torch.zeros(num_tokens, dtype=torch.int32, device="cuda")
    num_requests = cumulative_seqlens.size(0) - 1
    for req in range(num_requests):
        start = cumulative_seqlens[req].item()
        end = cumulative_seqlens[req + 1].item()
        result[start:end].fill_(all_shuffled_idxs[req])
    return result


# Generates random cache_seqlens for the given round.
def random_cache_seqlens(
    num_tokens: int,
    max_cache_seqlen: int,
    cumulative_seqlens: torch.Tensor,
) -> torch.Tensor:
    result = torch.zeros(num_tokens, dtype=torch.int32, device="cuda")
    num_requests = cumulative_seqlens.size(0) - 1
    for req in range(num_requests):
        start = cumulative_seqlens[req].item()
        end = cumulative_seqlens[req + 1].item()
        this_seqlen = end - start
        cache_start = torch.randint(
            low=0, high=max_cache_seqlen - this_seqlen, size=(1,)
        ).item()
        result[start:end].copy_(
            torch.arange(cache_start + 1, cache_start + this_seqlen + 1)
        )
    return result


def _check_equal(
    is_graphed,
    mrfa,
    q,
    k_cache,
    v_cache,
    cache_seqlens,
    cache_idxs,
    round_size,
    cumulative_seqlens,
    attn_qkv_scales: torch.Tensor | None,
    tolerance: float = 1e-3,
):
    mrfa_fn = mrfa
    if is_graphed:
        mrfa_fn = cuda_graphs.GraphedFunction(
            mrfa,
            batch_sizes=[round_size],
            argument_specs=[
                cuda_graphs.ArgumentSpec(batch_dim=0),
                cuda_graphs.ArgumentSpec(batch_dim=None),
                cuda_graphs.ArgumentSpec(batch_dim=None),
                cuda_graphs.ArgumentSpec(batch_dim=None),
                cuda_graphs.ArgumentSpec(batch_dim=0),
                cuda_graphs.ArgumentSpec(batch_dim=0),
            ],
            kw_argument_specs={
                "attn_qkv_scales": cuda_graphs.ArgumentSpec(batch_dim=None)
            }
            if attn_qkv_scales is not None
            else None,
        )
    # Ugh: our cuda graph capture library doesn't (really) support optional arguments. So we branch
    # here based on whether we need to pass qkv_scales or not.
    if attn_qkv_scales is not None:
        result = mrfa_fn(
            q,
            k_cache,
            v_cache,
            cumulative_seqlens,
            cache_idxs,
            cache_seqlens,
            attn_qkv_scales=attn_qkv_scales,
        )
    else:
        result = mrfa_fn(
            q, k_cache, v_cache, cumulative_seqlens, cache_idxs, cache_seqlens
        )
    ref = _reference_batch_flash_attn(
        q, k_cache, v_cache, cache_seqlens, cache_idxs, attn_qkv_scales, mrfa.dtype
    )

    torch.testing.assert_close(result, ref, atol=tolerance, rtol=tolerance)


@pytest.mark.parametrize(
    "round_size, num_empty_requests, num_padding_tokens, num_large_requests_in_round",
    [
        (32, 0, 0, 1),
        (32, 1, 0, 1),
        (32, 0, 17, 1),
        (32, 2, 7, 1),
        (512, 0, 0, 1),
        (512, 3, 0, 1),
        (512, 0, 108, 1),
        (512, 3, 303, 1),
        (32, 0, 0, MAX_REQUESTS_IN_ROUND_DEFAULT),
        (32, 1, 0, MAX_REQUESTS_IN_ROUND_DEFAULT - 3),
        (512, 3, 108, MAX_REQUESTS_IN_ROUND_DEFAULT),
        (512, 3, 117, MAX_REQUESTS_IN_ROUND_DEFAULT - 6),
    ],
)
@pytest.mark.parametrize("nheads_q", [16, 56])
@pytest.mark.parametrize("nheads_kv", [1, 8])
@pytest.mark.parametrize("headdim", [64, 128])
@pytest.mark.parametrize("dtype", [torch.bfloat16, torch.float16])
@pytest.mark.parametrize("is_graphed", [False, True])
@pytest.mark.parametrize(
    "use_fp8,attn_qkv_scales",
    [
        (False, None),
        (True, [1.0, 1.0, 1.0]),
        (True, [2.0, 0.5, 4.0]),
    ],
)
def test_attention_correctness(
    enable_h100_kernels: bool,
    round_size: int,
    num_empty_requests: int,
    num_padding_tokens: int,
    num_large_requests_in_round: int,
    nheads_q: int,
    nheads_kv: int,
    headdim: int,
    dtype: torch.dtype,
    is_graphed: bool,
    use_fp8: bool,
    attn_qkv_scales: list[float] | None,
):
    if use_fp8 and not enable_h100_kernels:
        pytest.skip("Skipping fp8 tests since they require H100 kernels.")
    assert (
        num_empty_requests < MAX_REQUESTS_IN_ROUND_DEFAULT
    ), f"Cannot have only empty requests. ({num_empty_requests=} {MAX_REQUESTS_IN_ROUND_DEFAULT=})"
    max_round_size = round_size * 2
    mrfa = MultiRequestFlashAttention(
        nheads_q,
        headdim,
        max_round_size=max_round_size,
        dtype=dtype,
        use_flash_attn_v3=enable_h100_kernels,
        use_fp8=use_fp8,
        max_requests_in_round=MAX_REQUESTS_IN_ROUND_DEFAULT,
        max_large_requests_in_round=num_large_requests_in_round,
        small_request_max_seqlen=SMALL_REQUEST_MAX_SEQLEN_DEFAULT,
    )
    cumulative_seqlens = random_round_cumulative_seqlens(
        round_size,
        MAX_REQUESTS_IN_ROUND_DEFAULT,
        num_empty_requests,
        num_padding_tokens,
    )
    cumulative_seqlens = _adjust_cumulative_seqlens_for_small_requests(
        cumulative_seqlens,
        num_large_requests_in_round=num_large_requests_in_round,
    )
    torch.manual_seed(2345)

    num_caches = 32
    cache_seqlen = 6000

    k_cache = torch.randn(
        (num_caches, cache_seqlen, nheads_kv, headdim), dtype=dtype, device="cuda"
    )
    v_cache = torch.randn(
        (num_caches, cache_seqlen, nheads_kv, headdim), dtype=dtype, device="cuda"
    )
    q = torch.randn((round_size, nheads_q, headdim), dtype=dtype, device="cuda")
    if use_fp8:
        assert attn_qkv_scales is not None
        k_cache = (k_cache * attn_qkv_scales[1]).to(torch.float8_e4m3fn)
        v_cache = (v_cache * attn_qkv_scales[2]).to(torch.float8_e4m3fn)
        q = (q * attn_qkv_scales[0]).to(torch.float8_e4m3fn)
    cache_idxs = random_cache_idxs(num_caches, round_size, cumulative_seqlens)
    cache_seqlens = random_cache_seqlens(round_size, cache_seqlen, cumulative_seqlens)

    if use_fp8:
        tolerance = 2e-2
    elif enable_h100_kernels and dtype == torch.bfloat16:
        # FA3 in bfloat has worse tolerance than FA2 (or fp16).
        tolerance = 3e-3
    else:
        tolerance = 1e-3
    _check_equal(
        is_graphed,
        mrfa,
        q,
        k_cache,
        v_cache,
        cache_seqlens,
        cache_idxs,
        round_size,
        cumulative_seqlens,
        torch.tensor(attn_qkv_scales, device="cuda")
        if attn_qkv_scales is not None
        else None,
        tolerance=tolerance,
    )


@pytest.mark.parametrize(
    "request_sizes,cache_seqlens",
    [
        ([1], [1024]),
        ([2], [1024]),
        ([32], [1024]),
        ([31, 1], [1024, 1024]),
        ([16, 16], [1024, 1024]),
        ([16, 15, 1], [1024, 1024, 1024]),
        ([16], [16]),
        ([16, 1], [16, 1]),
        ([16, 1, 1], [16, 1, 1]),
        ([1] * MAX_REQUESTS_IN_ROUND_DEFAULT, [1024] * MAX_REQUESTS_IN_ROUND_DEFAULT),
        ([1] * MAX_REQUESTS_IN_ROUND_DEFAULT, [1] * MAX_REQUESTS_IN_ROUND_DEFAULT),
        ([], []),  # All padding
    ],
)
def test_attention_correctness_boundary_cases(
    enable_h100_kernels: bool,
    request_sizes: Sequence[int],
    cache_seqlens: Sequence[int],
    round_size: int = 32,
    is_graphed: bool = False,
    head_dim: int = 128,
    nheads_q: int = 1,
    nheads_kv: int = 1,
    max_requests_in_round: int = MAX_REQUESTS_IN_ROUND_DEFAULT,
    small_request_max_seqlen: int = SMALL_REQUEST_MAX_SEQLEN_DEFAULT,
):
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)
    # Validity checks on test conditions
    assert (
        sum(request_sizes) <= round_size
    ), f"Too many tokens in round: {request_sizes} > {round_size}"
    assert (
        len(request_sizes) == len(cache_seqlens)
    ), f"Mismatch in request_sizes and cache_seqlens: {request_sizes} != {cache_seqlens}"
    assert (
        len(request_sizes) <= max_requests_in_round
    ), f"Too many requests: {len(request_sizes)} > {max_requests_in_round}"
    request_sizes = sorted(request_sizes, reverse=True)
    cumulative_request_sizes = [0] + [
        sum(request_sizes[:i]) for i in range(1, len(request_sizes) + 1)
    ]
    # fill to max_requests_in_round
    num_padding_requests = max_requests_in_round - len(request_sizes)
    cumulative_round_sizes = (
        cumulative_request_sizes + [cumulative_request_sizes[-1]] * num_padding_requests
    )
    cumulative_round_sizes = torch.tensor(
        cumulative_round_sizes, dtype=torch.int32, device="cuda"
    )
    mrfa = MultiRequestFlashAttention(
        nheads_q,
        head_dim,
        max_round_size=round_size,
        dtype=torch.float16,
        use_flash_attn_v3=enable_h100_kernels,
        max_requests_in_round=max_requests_in_round,
        small_request_max_seqlen=small_request_max_seqlen,
    )
    torch.manual_seed(2345)
    q = torch.randn(
        (round_size, nheads_q, head_dim), dtype=torch.float16, device="cuda"
    )
    if len(cache_seqlens) == 0:
        # If there is only padding, we need to pick _some_ value, doesn't matter
        n_caches = 1
        cache_full_seqlen = 1024
    else:
        n_caches = len(request_sizes)
        cache_full_seqlen = max(cache_seqlens)
    k_cache = torch.randn(
        (n_caches, cache_full_seqlen, nheads_kv, head_dim),
        dtype=torch.float16,
        device="cuda",
    )
    v_cache = torch.randn(
        (n_caches, cache_full_seqlen, nheads_kv, head_dim),
        dtype=torch.float16,
        device="cuda",
    )
    cache_idxs = torch.zeros(round_size, dtype=torch.int32, device="cuda")
    for i, _ in enumerate(request_sizes):
        cache_idxs[cumulative_round_sizes[i] : cumulative_round_sizes[i + 1]] = i

    cache_seqlens_tensor = torch.zeros(round_size, dtype=torch.int32, device="cuda")
    for i, seqlen in enumerate(cache_seqlens):
        start = cumulative_round_sizes[i].item()
        end = cumulative_round_sizes[i + 1].item()
        this_seqlen = end - start
        cache_seqlens_tensor[start:end].copy_(
            torch.arange(seqlen - this_seqlen + 1, seqlen + 1)
        )

    _check_equal(
        is_graphed,
        mrfa,
        q,
        k_cache,
        v_cache,
        cache_seqlens_tensor,
        cache_idxs,
        round_size,
        cumulative_round_sizes,
        None,
    )


# Test that the constructor raises if you ask for FA3 on non-H100 or an invalid headdim.
def test_flash_attn_v3_device_and_headdim_check():
    cuda_major, _ = torch.cuda.get_device_capability()
    is_h100 = cuda_major >= 9
    for headdim, is_allowed in [(64, True), (128, True), (256, False)]:
        if is_h100 and is_allowed:
            MultiRequestFlashAttention(
                16,
                headdim,
                1024,
                use_flash_attn_v3=True,
                max_requests_in_round=MAX_REQUESTS_IN_ROUND_DEFAULT,
                small_request_max_seqlen=SMALL_REQUEST_MAX_SEQLEN_DEFAULT,
            )
        else:
            with pytest.raises(RuntimeError):
                MultiRequestFlashAttention(
                    16,
                    headdim,
                    1024,
                    use_flash_attn_v3=True,
                    max_requests_in_round=MAX_REQUESTS_IN_ROUND_DEFAULT,
                    small_request_max_seqlen=SMALL_REQUEST_MAX_SEQLEN_DEFAULT,
                )


def test_request_sizing_checks():
    """Test that the constructor validates request sizing parameters correctly."""
    valid_cases = [
        (8, 8),
        (8, 1),
        (8, 4),
        (1, 1),
    ]

    for max_requests, num_large in valid_cases:
        MultiRequestFlashAttention(
            16,
            128,
            1024,
            max_requests_in_round=max_requests,
            max_large_requests_in_round=num_large,
        )

    # Invalid cases - should raise ValueError
    invalid_cases = [
        # (max_requests_in_round, num_large_requests_in_round, expected_error_pattern)
        (4, 5, "max_large_requests_in_round .* must be <= max_requests_in_round"),
        (4, 0, "max_large_requests_in_round .* must be .* >= 1"),
        (4, -2, "max_large_requests_in_round .* must be .* >= 1"),
    ]

    for max_requests, num_large, error_pattern in invalid_cases:
        with pytest.raises(ValueError, match=error_pattern):
            MultiRequestFlashAttention(
                16,
                128,
                1024,
                max_requests_in_round=max_requests,
                max_large_requests_in_round=num_large,
            )
