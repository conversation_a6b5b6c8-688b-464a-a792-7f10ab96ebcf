#include "all_reduce_kernel.h"

#include <cuda.h>
#include <pybind11/functional.h>

namespace all_reduce_kernel {

torch::Tensor cuda_ptr_to_tensor(std::vector<int64_t> shape, py::object dtype_arg,
                                 uint64_t data_ptr, uint64_t size_in_bytes) {
    TORCH_CHECK(data_ptr != 0, "data_ptr must be non-zero");

    auto dtype = torch::python::detail::py_object_to_dtype(dtype_arg);
    auto ret = torch::from_blob(
        reinterpret_cast<void*>(data_ptr), shape,
        torch::TensorOptions().dtype(dtype).layout(torch::kStrided).device(torch::kCUDA));

    TORCH_CHECK(ret.element_size() * ret.numel() <= size_in_bytes,
                "data_ptr region too small to store max_elements");

    return ret;
}

void all_reduce(int process_idx, int num_processes, torch::Tensor& output,
                std::vector<uint64_t> all_input_ptrs, std::vector<uint64_t> all_barrier_ptrs,
                std::vector<uint64_t> all_block_barrier_ptrs, size_t tensor_size_in_elements,
                size_t blocks_per_grid, size_t threads_per_block, bool use_one_shot,
                size_t elements_per_block, size_t elements_per_rank, bool use_fp8,
                std::optional<torch::Tensor> fp8_scale,
                std::optional<torch::Tensor> residual_buffer) {
    TORCH_CHECK(process_idx >= 0 && process_idx < num_processes, "process_idx out of range");
    TORCH_CHECK(num_processes >= 2 && num_processes <= MAX_RANKS_PER_NODE,
                "num_processes out of range");
    TORCH_CHECK(all_input_ptrs.size() == num_processes, "all_input_ptrs.size() != num_processes");
    TORCH_CHECK(all_barrier_ptrs.size() == num_processes,
                "all_barrier_ptrs.size() != num_processes");
    TORCH_CHECK(all_block_barrier_ptrs.size() == num_processes,
                "all_block_barrier_ptrs.size() != num_processes");

    AllReduceParams params;
    TORCH_CHECK((use_one_shot || !use_fp8), "fp8 is not supported for two-shot allreduce");

    params.local_rank = process_idx;
    params.world_size = num_processes;
    params.elts_total = tensor_size_in_elements;
    params.elts_per_rank = elements_per_rank;
    params.elts_per_block = elements_per_block;
    params.one_shot = use_one_shot;
    params.blocks_per_grid = blocks_per_grid;
    params.threads_per_block = threads_per_block;
    params.use_fp8 = use_fp8;
    if (use_fp8) {
        TORCH_CHECK(fp8_scale.has_value() && fp8_scale.value().numel() == 1,
                    "fp8_scale must be a single element tensor");
        params.fp8_scale = fp8_scale.value().data_ptr<float>();
    } else {
        params.fp8_scale = nullptr;
    }

    if (residual_buffer.has_value()) {
        params.local_residual_buffer_ptr = residual_buffer.value().data_ptr();
    } else {
        params.local_residual_buffer_ptr = nullptr;
    }

    memset(params.peer_comm_buffer_ptrs, 0, sizeof(params.peer_comm_buffer_ptrs));
    int i = 0;
    for (auto& ptr : all_input_ptrs) {
        params.peer_comm_buffer_ptrs[i++] = reinterpret_cast<uint32_t*>(ptr);
    }
    memset(params.gpu_barrier_ptrs, 0, sizeof(params.gpu_barrier_ptrs));
    i = 0;
    for (auto& ptr : all_barrier_ptrs) {
        params.gpu_barrier_ptrs[i++] = reinterpret_cast<uint32_t*>(ptr);
    }
    memset(params.block_barrier_ptrs, 0, sizeof(params.block_barrier_ptrs));
    i = 0;
    for (auto& ptr : all_block_barrier_ptrs) {
        params.block_barrier_ptrs[i++] = reinterpret_cast<uint32_t*>(ptr);
    }

    do_all_reduce(params, output);

    return;
}

void all_gather(int process_idx, int num_processes, torch::Tensor& output,
                std::vector<uint64_t> all_input_ptrs, std::vector<uint64_t> all_barrier_ptrs,
                size_t output_size_in_elements, size_t blocks_per_grid, size_t threads_per_block,
                size_t elements_per_block, size_t elements_per_rank) {
    TORCH_CHECK(process_idx >= 0 && process_idx < num_processes, "process_idx out of range");
    TORCH_CHECK(num_processes >= 2 && num_processes <= MAX_RANKS_PER_NODE,
                "num_processes out of range");
    TORCH_CHECK(all_input_ptrs.size() == num_processes, "all_input_ptrs.size() != num_processes");
    TORCH_CHECK(all_barrier_ptrs.size() == num_processes,
                "all_barrier_ptrs.size() != num_processes");

    AllReduceParams params;
    params.local_rank = process_idx;
    params.world_size = num_processes;
    params.elts_total = output_size_in_elements;
    params.elts_per_rank = elements_per_rank;
    params.elts_per_block = elements_per_block;
    params.one_shot = false;  // Ignored
    params.blocks_per_grid = blocks_per_grid;
    params.threads_per_block = threads_per_block;

    for (size_t i = 0; i < num_processes; ++i) {
        params.peer_comm_buffer_ptrs[i] = reinterpret_cast<uint32_t*>(all_input_ptrs[i]);
        params.gpu_barrier_ptrs[i] = reinterpret_cast<uint32_t*>(all_barrier_ptrs[i]);
        params.block_barrier_ptrs[i] = nullptr;  // unused
    }
    do_all_gather(params, output);
}

void all_gather_inplace(int process_idx, int num_processes, std::vector<uint64_t> all_buffer_ptrs,
                        std::vector<uint64_t> all_barrier_ptrs, torch::Tensor& output,
                        size_t blocks_per_grid, size_t threads_per_block, size_t elements_per_block,
                        size_t elements_per_rank) {
    TORCH_CHECK(process_idx >= 0 && process_idx < num_processes, "process_idx out of range");
    TORCH_CHECK(num_processes >= 2 && num_processes <= MAX_RANKS_PER_NODE,
                "num_processes out of range");
    TORCH_CHECK(all_buffer_ptrs.size() == num_processes, "all_buffer_ptrs.size() != num_processes");
    TORCH_CHECK(all_barrier_ptrs.size() == num_processes,
                "all_barrier_ptrs.size() != num_processes");

    AllReduceParams params;
    params.local_rank = process_idx;
    params.world_size = num_processes;
    params.elts_total = output.numel();
    params.elts_per_rank = elements_per_rank;
    params.elts_per_block = elements_per_block;
    params.one_shot = false;  // Ignored
    params.blocks_per_grid = blocks_per_grid;
    params.threads_per_block = threads_per_block;

    size_t elems_per_process = output.numel() / num_processes;
    for (size_t i = 0; i < num_processes; ++i) {
        // NOTE: each of `all_buffer_ptrs` is a pointer to the _base_ of the buffer. The data is
        // offset based on the process index (for all_gather). So we need to populate
        // `peer_comm_buffer_ptrs` with the offset pointer.
        char* peer_ptr = reinterpret_cast<char*>(all_buffer_ptrs[i]);
        size_t offset = i * elems_per_process * output.element_size();
        params.peer_comm_buffer_ptrs[i] = reinterpret_cast<uint32_t*>(peer_ptr + offset);
        params.gpu_barrier_ptrs[i] = reinterpret_cast<uint32_t*>(all_barrier_ptrs[i]);
        params.block_barrier_ptrs[i] = nullptr;  // unused
    }
    do_all_gather_inplace(params, output);
}

void barrier(int process_idx, int num_processes, std::vector<uint64_t> all_barrier_ptrs) {
    TORCH_CHECK(process_idx >= 0 && process_idx < num_processes, "process_idx out of range");
    TORCH_CHECK(num_processes >= 2 && num_processes <= MAX_RANKS_PER_NODE,
                "num_processes out of range");
    TORCH_CHECK(all_barrier_ptrs.size() == num_processes,
                "all_barrier_ptrs.size() != num_processes");

    BarrierParams params;

    params.local_rank = process_idx;
    params.world_size = num_processes;

    memset(params.gpu_barrier_ptrs, 0, sizeof(params.gpu_barrier_ptrs));
    int i = 0;
    for (auto& ptr : all_barrier_ptrs) {
        params.gpu_barrier_ptrs[i++] = reinterpret_cast<uint32_t*>(ptr);
    }

    do_barrier(params);
}

}  // namespace all_reduce_kernel

PYBIND11_MODULE(all_reduce_kernel, m) {
    m.doc() = "Custom all-reduce kernel.";

    m.def("cuda_ptr_to_tensor", &all_reduce_kernel::cuda_ptr_to_tensor,
          "Returns a CUDA tensor from a like tensor and data pointer.");
    m.def("all_reduce", &all_reduce_kernel::all_reduce, "All reduce.");
    m.def("all_gather", &all_reduce_kernel::all_gather, "All gather.");
    m.def("all_gather_inplace", &all_reduce_kernel::all_gather_inplace, "All gather inplace.");
    m.def("barrier", &all_reduce_kernel::barrier, "Multi GPU barrier.");
}
