#include <torch/extension.h>

namespace compiled_rotary {

// Forward declaration.
torch::Tensor rotary_embed_cuda(torch::Tensor& x, torch::Tensor& freqs_cos,
                                torch::Tensor& freqs_sin, torch::Tensor& idxs);

// Performs RoPE with position indices on `x` given pre-computed cos and sin
// tables. Arguments:
//   x: Input activations of shape (ntokens, *, headdim) where * is one or two
//   head dimensions.
//   freqs_{cos, sin}: tables of shape (max_seqlen, ropedim // 2).
//     NOTE: in the common case, headdim == ropedim. But if you want to
//     pass-through some of the activations, set ropedim < headdim.
//   idxs: Sequence indices of shape (ntokens,) for each element of `x`. Each
//   index must lie in the range [0, max_seqlen).
// Returns:
//   Result tensor of shape (ntokens, *, headdim).
// Constraints:
//  - All inputs must live on a CUDA device.
//  - All inputs must be contiguous _except_ for `x`.
//  - `x` is any floating point type.
//  - `freqs_cos` and `freqs_sin` are both float32.
//  - `idxs` is int32.
torch::Tensor rotary_embed(torch::Tensor& x, torch::Tensor& freqs_cos, torch::Tensor& freqs_sin,
                           torch::Tensor& idxs) {
    TORCH_CHECK(x.dim() == 3 || x.dim() == 4, "x must have one or two head dimensions.");
    TORCH_CHECK(freqs_cos.is_contiguous(), "freqs_cos must be contiguous.");
    TORCH_CHECK(freqs_sin.is_contiguous(), "freqs_sin must be contiguous.");
    TORCH_CHECK(idxs.is_contiguous(), "idxs must be contiguous.");
    TORCH_CHECK(x.device().is_cuda(), "x must live on a CUDA device.");
    TORCH_CHECK(freqs_cos.device().is_cuda(), "freqs_cos must live on a CUDA device.");
    TORCH_CHECK(freqs_sin.device().is_cuda(), "freqs_sin must live on a CUDA device.");
    TORCH_CHECK(idxs.device().is_cuda(), "idxs must live on a CUDA device.");
    TORCH_CHECK(freqs_cos.dtype() == torch::kFloat32, "freqs_cos must be float32.");
    TORCH_CHECK(freqs_sin.dtype() == torch::kFloat32, "freqs_sin must be float32.");
    TORCH_CHECK(idxs.dtype() == torch::kInt32, "idxs must be int32.");

    return rotary_embed_cuda(x, freqs_cos, freqs_sin, idxs);
}

}  // namespace compiled_rotary

PYBIND11_MODULE(compiled_rotary, m) {
    m.def("rotary_embed", &compiled_rotary::rotary_embed, "Rotary embed.");
}
