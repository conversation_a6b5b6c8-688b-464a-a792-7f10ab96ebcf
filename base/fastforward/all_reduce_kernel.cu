#include <c10/cuda/CUDAStream.h>
#include <cuda.h>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <cuda_fp8.h>

#include <cassert>
#include <iostream>

#include "all_reduce_kernel.h"

#define CHECK_CUDA(val) check((val), #val, __FILE__, __LINE__)
void check(cudaError_t err, const char* const func, const char* const file, const int line) {
    if (err != cudaSuccess) {
        std::cerr << "CUDA Runtime Error at: " << file << ":" << line << std::endl;
        std::cerr << cudaGetErrorString(err) << " " << func << std::endl;
        std::exit(EXIT_FAILURE);
    }
}

namespace ffwd::allreduce {

#define MAX_THREADS_PER_BLOCK 1024

// flag functions
static inline __device__ void st_flag_release(uint32_t const& flag, uint32_t* flag_addr) {
    asm volatile("st.global.release.sys.b32 [%1], %0;" ::"r"(flag), "l"(flag_addr));
}

static inline __device__ uint32_t ld_flag_acquire(uint32_t* flag_addr) {
    uint32_t flag;
    asm volatile("ld.global.acquire.sys.b32 %0, [%1];" : "=r"(flag) : "l"(flag_addr));
    return flag;
}

// multi_gpu_barrier won't return until all peer GPUs have called multi_gpu_barrier.
//
// multi_gpu_barrier requires each GPU to allocate a buffer of world_size + 1 elements
// and share it will all other GPUs. The buffer should be set to 0 before the
// first use of multi_gpu_barrier. Subsequent uses of multi_gpu_barrier do not require
// a memset.
//
// multi_gpu_barrier can be reset after use. This is done by scheduling a
// reset_multi_gpu_barrier kernel.
//
// flags_buf includes the GPU buffers for all GPUs, including the local one.
// flags_bufs[my_rank] should be the buffer of the local GPU.
//
// world_size is the number of GPUs.
// bidx and tidx together identify which thread calling multi_gpU_barrier
//
// Implementation notes:
//    Each entry in a GPU's buffer is a flag for a peer GPU to indicate that it has
// reached multi_gpu_barrier.
//
//    A given GPU will toggle a value in each of its peer GPUs's buffers to indicate it has
// reached multi_gpu_barrier.
//
//    A given GPU will then poll its own buffer to check that other GPUs have
// reached multi_gpu_barrier. When the GPU's buffer indicates that all other GPUs
// have reached multi_gpu_barrier, multi_gpu_barrier returns.
__inline__ __device__ void multi_gpu_barrier(uint32_t** flag_bufs, size_t my_rank,
                                             size_t world_size, int tidx, int bidx) {
    // Thread i handles the flags for peer_rank == i. Other threads will wait at syncthreads.
    if (tidx < world_size) {
        uint32_t* my_flag_buf = flag_bufs[my_rank];
        uint32_t* peer_flag_buf = flag_bufs[tidx];

        uint32_t expected_flag_val = my_flag_buf[world_size] + 1;

        // Elect the first block to do the writing, but every block will do waiting
        if (bidx == 0) {
            st_flag_release(expected_flag_val, peer_flag_buf + my_rank);
        }
        uint32_t* my_flag_addr = my_flag_buf + tidx;
        while (ld_flag_acquire(my_flag_addr) != expected_flag_val) {
            /* spin */
        }
    }
    __syncthreads();
}

static __global__ void reset_multi_gpu_barrier(uint32_t* my_flag_bufs, size_t world_size) {
    my_flag_bufs[world_size] += 1;
}

static __global__ void barrier(BarrierParams params) {
    assert(blockIdx.x == 0);

    int const tidx = threadIdx.x;

    multi_gpu_barrier(params.gpu_barrier_ptrs, params.local_rank, params.world_size, tidx, 0);
    if (tidx == 0) {
        params.gpu_barrier_ptrs[params.local_rank][params.world_size] += 1;
    }
}

// block_barrier will not return until, for a given block index bidx, all peer GPUs
// have called block_barrier with the same bidx.
__inline__ __device__ void block_barrier(uint32_t** flag_bufs, uint32_t expected_flag_val,
                                         size_t my_rank, size_t world_size, int tidx, int bidx,
                                         size_t blocks_per_grid) {
    // Thread i handles the flags for peer_rank == i. Other threads wait at syncthreads.
    if (tidx < world_size) {
        uint32_t* my_flag_buf = flag_bufs[my_rank];
        uint32_t* peer_flag_buf = flag_bufs[tidx];

        // Each block writes flag to bufs[peer_rank][my_block][my_rank]
        uint32_t block_offset = bidx * world_size;
        st_flag_release(expected_flag_val, peer_flag_buf + block_offset + my_rank);

        // Then wait on bufs[my_rank][my_block][peer_rank]
        uint32_t* my_flag_addr = my_flag_buf + block_offset + tidx;
        while (ld_flag_acquire(my_flag_addr) != expected_flag_val) {
            /* spin */
        }
    }
    __syncthreads();
}

using PackedHalf = union {
    int4 packed;
    half2 unpacked[4];
};

using PackedBFloat16 = union {
    int4 packed;
    __nv_bfloat162 unpacked[4];
};

template <typename T>
struct PackedOn16Bytes {};

template <>
struct PackedOn16Bytes<at::Half> {
    using Type = PackedHalf;
};

template <>
struct PackedOn16Bytes<at::BFloat16> {
    using Type = PackedBFloat16;
};

template <typename T>
inline __device__ int4 add128b(T& a, T& b) {
    T c;
    c.unpacked[0] = a.unpacked[0] + b.unpacked[0];
    c.unpacked[1] = a.unpacked[1] + b.unpacked[1];
    c.unpacked[2] = a.unpacked[2] + b.unpacked[2];
    c.unpacked[3] = a.unpacked[3] + b.unpacked[3];
    return c.packed;
}

// NOTE(FP8): the core weirdness of fp8 all_reduce is that the output type (high precision) is
// different than the input type. As a result, we have a size mismatch between inputs and outputs.
// The following structs exist to handle the fact that any 16 byte _read_ of 16x fp8 values
// produces 32 bytes of data to write (16x fp16 or bf16).
using PackedFP8 = union {
    int4 packed;
    __nv_fp8x4_e4m3 unpacked[4];
};

struct int8 {
    int4 x;
    int4 y;
};

using Packed16xHalf = union {
    int8 packed;
    half2 unpacked[8];
};

using Packed16xBFloat16 = union {
    int8 packed;
    __nv_bfloat162 unpacked[8];
};

template <typename T>
struct PackedOn32Bytes {};

template <>
struct PackedOn32Bytes<at::Half> {
    using Type = Packed16xHalf;
};

template <>
struct PackedOn32Bytes<at::BFloat16> {
    using Type = Packed16xBFloat16;
};

template <typename T>
inline __device__ int8 add16xfp8(T& a, PackedFP8& b, float inverse_scale);

template <>
inline __device__ int8 add16xfp8(Packed16xHalf& a, PackedFP8& b, float inverse_scale) {
    Packed16xHalf c;
#pragma unroll
    for (int i = 0; i < 4; ++i) {
        float4 f4 = static_cast<float4>(b.unpacked[i]);
        __half2 part1 = __floats2half2_rn(f4.x * inverse_scale, f4.y * inverse_scale);
        __half2 part2 = __floats2half2_rn(f4.z * inverse_scale, f4.w * inverse_scale);
        c.unpacked[2 * i] = __hadd2(a.unpacked[2 * i], part1);
        c.unpacked[2 * i + 1] = __hadd2(a.unpacked[2 * i + 1], part2);
    }
    return c.packed;
}

template <>
inline __device__ int8 add16xfp8(Packed16xBFloat16& a, PackedFP8& b, float inverse_scale) {
    Packed16xBFloat16 c;
#pragma unroll
    for (int i = 0; i < 4; ++i) {
        float4 f4 = static_cast<float4>(b.unpacked[i]);
        __nv_bfloat162 part1 = __floats2bfloat162_rn(f4.x * inverse_scale, f4.y * inverse_scale);
        __nv_bfloat162 part2 = __floats2bfloat162_rn(f4.z * inverse_scale, f4.w * inverse_scale);
        c.unpacked[2 * i] = __hadd2(a.unpacked[2 * i], part1);
        c.unpacked[2 * i + 1] = __hadd2(a.unpacked[2 * i + 1], part2);
    }
    return c.packed;
}

template <typename T, int RANKS_PER_NODE, bool RESIDUAL>
static __global__ __launch_bounds__(MAX_THREADS_PER_BLOCK) void oneShotAllReduceKernel(
    AllReduceParams params) {
    int const bidx = blockIdx.x;
    int const tidx = threadIdx.x;

    static constexpr int PACKED_ELTS = 16 / sizeof(T);
    using PackedStruct = typename PackedOn16Bytes<T>::Type;

    T* local_output_buffer = reinterpret_cast<T*>(params.local_output_buffer_ptr);

    // Start and end offsets of the thread
    size_t const chunk_start = bidx * params.elts_per_block + tidx * PACKED_ELTS;
    size_t const chunk_end = min((bidx + 1) * params.elts_per_block, params.elts_total);

    T* buffers[RANKS_PER_NODE];
#pragma unroll
    for (int ii = 0; ii < RANKS_PER_NODE; ++ii) {
        int rank = (params.local_rank + ii) % RANKS_PER_NODE;
        buffers[ii] = reinterpret_cast<T*>(params.peer_comm_buffer_ptrs[rank]);
    }
    multi_gpu_barrier(params.gpu_barrier_ptrs, params.local_rank, RANKS_PER_NODE, tidx, bidx);
    // All GPUs have now reached this point. That means that any kernels related to filling
    // the input buffers have completed on those GPUs.
    //
    // Note that absent additional synchronization, some GPUs may be here and some GPUs may be
    // well past this point, even having completed oneShotAllReduceKernel and executing the next
    // kernels.

    for (size_t iter_offset = chunk_start; iter_offset < chunk_end;
         iter_offset += blockDim.x * PACKED_ELTS) {
        PackedStruct vals[RANKS_PER_NODE];
        PackedStruct sums;
#pragma unroll
        for (int ii = 0; ii < RANKS_PER_NODE; ++ii) {
            vals[ii].packed = *reinterpret_cast<int4 const*>(&buffers[ii][iter_offset]);
        }

        if constexpr (RESIDUAL) {
            sums.packed = *reinterpret_cast<int4 const*>(
                &reinterpret_cast<T*>(params.local_residual_buffer_ptr)[iter_offset]);
        } else {
            sums.packed = {0, 0, 0, 0};
        }
#pragma unroll
        for (int rank = 0; rank < RANKS_PER_NODE; ++rank) {
            int ii = (rank + RANKS_PER_NODE - params.local_rank) % RANKS_PER_NODE;
            sums.packed = add128b(sums, vals[ii]);
        }

        *reinterpret_cast<int4*>(&local_output_buffer[iter_offset]) = sums.packed;
    }
}

template <typename OutT, int RANKS_PER_NODE, bool RESIDUAL>
static __global__ __launch_bounds__(MAX_THREADS_PER_BLOCK) void oneShotAllReduceKernelFP8(
    AllReduceParams params) {
    int const bidx = blockIdx.x;
    int const tidx = threadIdx.x;
    float fp8_inverse_scale = 1.0f / *params.fp8_scale;

    using FP8 = __nv_fp8_e4m3;
    static constexpr int PACKED_ELTS = 16 / sizeof(FP8);

    OutT* local_output_buffer = reinterpret_cast<OutT*>(params.local_output_buffer_ptr);
    using PackedOutType = typename PackedOn32Bytes<OutT>::Type;

    // Start and end offsets of the thread
    size_t const chunk_start = bidx * params.elts_per_block + tidx * PACKED_ELTS;
    size_t const chunk_end = min((bidx + 1) * params.elts_per_block, params.elts_total);

    FP8 const* buffers[RANKS_PER_NODE];
#pragma unroll
    for (int ii = 0; ii < RANKS_PER_NODE; ++ii) {
        int rank = (params.local_rank + ii) % RANKS_PER_NODE;
        buffers[ii] = reinterpret_cast<FP8 const*>(params.peer_comm_buffer_ptrs[rank]);
    }
    multi_gpu_barrier(params.gpu_barrier_ptrs, params.local_rank, RANKS_PER_NODE, tidx, bidx);

    for (size_t iter_offset = chunk_start; iter_offset < chunk_end;
         iter_offset += blockDim.x * PACKED_ELTS) {
        PackedFP8 vals[RANKS_PER_NODE];
        PackedOutType sums;
#pragma unroll
        for (int ii = 0; ii < RANKS_PER_NODE; ++ii) {
            vals[ii].packed = *reinterpret_cast<int4 const*>(&buffers[ii][iter_offset]);
        }

        if constexpr (RESIDUAL) {
            sums.packed = *reinterpret_cast<int8 const*>(
                &reinterpret_cast<OutT*>(params.local_residual_buffer_ptr)[iter_offset]);
        } else {
            sums.packed = {0, 0, 0, 0, 0, 0, 0, 0};
        }
#pragma unroll
        for (int rank = 0; rank < RANKS_PER_NODE; ++rank) {
            int ii = (rank + RANKS_PER_NODE - params.local_rank) % RANKS_PER_NODE;
            sums.packed = add16xfp8(sums, vals[ii], fp8_inverse_scale);
        }

        *reinterpret_cast<int8*>(&local_output_buffer[iter_offset]) = sums.packed;
    }
}

template <typename T, int RANKS_PER_NODE, bool RESIDUAL>
static __global__ __launch_bounds__(MAX_THREADS_PER_BLOCK) void twoShotAllReduceKernel(
    AllReduceParams params) {
    int bidx = blockIdx.x;
    int tidx = threadIdx.x;
    int grid_size = gridDim.x;

    static constexpr int PACKED_ELTS = 16 / sizeof(T);
    // PackedType.packed is an int4 (128 bytes)
    // PackedType.unpacked is a 4-element array of {half2, bfloat2}
    using PackedType = typename PackedOn16Bytes<T>::Type;

    T* local_shared_buffer = reinterpret_cast<T*>(params.peer_comm_buffer_ptrs[params.local_rank]);
    T* local_output_buffer = reinterpret_cast<T*>(params.local_output_buffer_ptr);

    size_t chunk_start = bidx * params.elts_per_block + tidx * PACKED_ELTS;
    size_t chunk_end = min(chunk_start + params.elts_per_block, params.elts_per_rank);

    T* buffers[RANKS_PER_NODE];
    int ranks[RANKS_PER_NODE];
#pragma unroll
    for (int i = 0; i < RANKS_PER_NODE; ++i) {
        // Remap the ranks so a loop over ranks is shifted for each GPU
        int rank = (params.local_rank + i) % RANKS_PER_NODE;
        ranks[i] = rank;
        buffers[i] = reinterpret_cast<T*>(params.peer_comm_buffer_ptrs[rank]);
    }

    multi_gpu_barrier(params.gpu_barrier_ptrs, params.local_rank, RANKS_PER_NODE, tidx, bidx);

    int rank_offset = params.local_rank * params.elts_per_rank;
    // Data is ready to read -- time to reduce_scatter.
    // Outer loop is over offset into a chunk
    for (size_t local_offset = chunk_start; local_offset < chunk_end;
         local_offset += blockDim.x * PACKED_ELTS) {
        // How far to offset based on which rank has this data
        size_t responsible_block_offset = local_offset + rank_offset;

        // Inner loop: Read a 128 byte slice (per thread) from every rank and update sum
        PackedType vals[RANKS_PER_NODE];
#pragma unroll
        for (int i = 0; i < RANKS_PER_NODE; ++i) {
            vals[i].packed = *reinterpret_cast<int4 const*>(&buffers[i][responsible_block_offset]);
        }

        // Reduce those 128 bytes of values (either as 4x floats or 8x halfs)
        PackedType sums;
        if constexpr (RESIDUAL) {
            sums.packed = *reinterpret_cast<int4 const*>(
                &reinterpret_cast<T*>(params.local_residual_buffer_ptr)[responsible_block_offset]);
        } else {
            sums.packed = {0, 0, 0, 0};
        }
#pragma unroll
        for (int rank = 0; rank < RANKS_PER_NODE; ++rank) {
            // Always start from real rank 0 for stable order.
            int i = (rank + RANKS_PER_NODE - params.local_rank) % RANKS_PER_NODE;
            sums.packed = add128b(sums, vals[i]);
        }

        // Save the reduce value into the local shared buffer
        *reinterpret_cast<int4*>(&local_shared_buffer[responsible_block_offset]) = sums.packed;
    }

    block_barrier(params.block_barrier_ptrs,
                  params.gpu_barrier_ptrs[params.local_rank][params.world_size] + 1,
                  params.local_rank, RANKS_PER_NODE, tidx, bidx, grid_size);

    // Allgather the ready data for this block
    // Outer loop is the same: over offset into the chunk this block is responsible for
    for (size_t local_offset = chunk_start; local_offset < chunk_end;
         local_offset += blockDim.x * PACKED_ELTS) {
        // Inner loop: read the per-block slice from every GPU
#pragma unroll
        for (int i = 0; i < RANKS_PER_NODE; ++i) {
            // Use the shifted `ranks` to find starting place
            size_t offset_rank = ranks[i] * params.elts_per_rank + local_offset;
            if (offset_rank >= params.elts_total) {
                continue;
            }
            *reinterpret_cast<int4*>(&local_output_buffer[offset_rank]) =
                *reinterpret_cast<int4*>(&buffers[i][offset_rank]);
        }
    }
}

template <typename T, int RANKS_PER_NODE, bool RESIDUAL>
void dispatch_all_reduce_layer_3(AllReduceParams params) {
    auto stream = at::cuda::getCurrentCUDAStream().stream();
    if (params.use_fp8) {
        TORCH_CHECK(params.one_shot, "fp8 all_reduce supports only one_shot=True");
        oneShotAllReduceKernelFP8<T, RANKS_PER_NODE, RESIDUAL>
            <<<params.blocks_per_grid, params.threads_per_block, 0, stream>>>(params);
    } else if (params.one_shot) {
        oneShotAllReduceKernel<T, RANKS_PER_NODE, RESIDUAL>
            <<<params.blocks_per_grid, params.threads_per_block, 0, stream>>>(params);
    } else {
        twoShotAllReduceKernel<T, RANKS_PER_NODE, RESIDUAL>
            <<<params.blocks_per_grid, params.threads_per_block, 0, stream>>>(params);
    }
}

template <typename T, int RANKS_PER_NODE>
void dispatch_all_reduce_layer_2(AllReduceParams params) {
    if (params.local_residual_buffer_ptr == nullptr) {
        dispatch_all_reduce_layer_3<T, RANKS_PER_NODE, false>(params);
    } else {
        dispatch_all_reduce_layer_3<T, RANKS_PER_NODE, true>(params);
    }
}

template <typename T>
void dispatch_all_reduce_layer_1(AllReduceParams params) {
    switch (params.world_size) {
        case 2:
            dispatch_all_reduce_layer_2<T, 2>(params);
            break;
        case 4:
            dispatch_all_reduce_layer_2<T, 4>(params);
            break;
        case 8:
            dispatch_all_reduce_layer_2<T, 8>(params);
            break;
        default:
            TORCH_CHECK(false, "world_size must be 2, 4, or 8");
    }
}

template <int ELEM_SIZE, int RANKS_PER_NODE, bool INPLACE>
static __global__ __launch_bounds__(MAX_THREADS_PER_BLOCK) void allGatherKernel(
    AllReduceParams params) {
    int bidx = blockIdx.x;
    int tidx = threadIdx.x;

    static constexpr int ELTS_PER_LD = 16 / ELEM_SIZE;
    size_t int4_elts_per_rank = params.elts_per_rank / ELTS_PER_LD;
    size_t int4_elts_per_block = params.elts_per_block / ELTS_PER_LD;
    size_t int4_elts_total = params.elts_total / ELTS_PER_LD;

    int4* local_output_buffer = reinterpret_cast<int4*>(params.local_output_buffer_ptr);
    size_t chunk_start = bidx * int4_elts_per_block + tidx;
    size_t chunk_end = min(chunk_start + int4_elts_per_block, int4_elts_per_rank);

    int4* buffers[RANKS_PER_NODE];
    int ranks[RANKS_PER_NODE];
#pragma unroll
    for (int i = 0; i < RANKS_PER_NODE; ++i) {
        int rank = (params.local_rank + i) % RANKS_PER_NODE;
        buffers[i] = reinterpret_cast<int4*>(params.peer_comm_buffer_ptrs[rank]);
        ranks[i] = rank;
    }

    multi_gpu_barrier(params.gpu_barrier_ptrs, params.local_rank, RANKS_PER_NODE, tidx, bidx);

    for (size_t local_offset = chunk_start; local_offset < chunk_end; local_offset += blockDim.x) {
#pragma unroll
        for (int i = 0; i < RANKS_PER_NODE; ++i) {
            int rank = ranks[i];
            if constexpr (INPLACE) {
                if (rank == params.local_rank) {
                    continue;
                }
            }

            size_t offset_rank = rank * int4_elts_per_rank + local_offset;
            if (offset_rank >= int4_elts_total) {
                continue;
            }
            local_output_buffer[offset_rank] = buffers[i][local_offset];
        }
    }
}

template <int ELEM_SIZE, bool INPLACE>
void dispatch_all_gather_world_size(AllReduceParams params) {
    auto stream = at::cuda::getCurrentCUDAStream().stream();
    switch (params.world_size) {
        case 2:
            allGatherKernel<ELEM_SIZE, 2, INPLACE>
                <<<params.blocks_per_grid, params.threads_per_block, 0, stream>>>(params);
            break;
        case 4:
            allGatherKernel<ELEM_SIZE, 4, INPLACE>
                <<<params.blocks_per_grid, params.threads_per_block, 0, stream>>>(params);
            break;
        case 8:
            allGatherKernel<ELEM_SIZE, 8, INPLACE>
                <<<params.blocks_per_grid, params.threads_per_block, 0, stream>>>(params);
            break;
        default:
            TORCH_CHECK(false, "world_size must be 2, 4, or 8");
    }
}

template <bool INPLACE>
void dispatch_all_gather_elem_size(AllReduceParams params, int elem_size) {
    switch (elem_size) {
        case 1:
            ffwd::allreduce::dispatch_all_gather_world_size<1, INPLACE>(params);
            break;
        case 2:
            ffwd::allreduce::dispatch_all_gather_world_size<2, INPLACE>(params);
            break;
        case 4:
            ffwd::allreduce::dispatch_all_gather_world_size<4, INPLACE>(params);
            break;
        default:
            TORCH_CHECK(false, "Unsupported element size");
    }
}

};  // namespace ffwd::allreduce

int do_all_reduce(AllReduceParams params, torch::Tensor& output) {
    TORCH_CHECK(params.threads_per_block <= MAX_THREADS_PER_BLOCK,
                "threads_per_block must be <= MAX_THREADS_PER_BLOCK");
    params.local_output_buffer_ptr = reinterpret_cast<void*>(output.data_ptr());
    AT_DISPATCH_REDUCED_FLOATING_TYPES(output.scalar_type(), "all_reduce", [&] {
        ffwd::allreduce::dispatch_all_reduce_layer_1<scalar_t>(params);
    });

    // Reset barriers for next call to all_reduce.
    //
    // All blocks participate in the barrier, so we can't reset the barrier until all blocks have
    // completed. An easy way to do this is to launch a kernel with a single thread.
    auto stream = at::cuda::getCurrentCUDAStream().stream();
    ffwd::allreduce::reset_multi_gpu_barrier<<<1, 1, 0, stream>>>(
        params.gpu_barrier_ptrs[params.local_rank], params.world_size);

    return 0;
}

void do_all_gather(AllReduceParams params, torch::Tensor& output) {
    TORCH_CHECK(params.threads_per_block <= MAX_THREADS_PER_BLOCK,
                "threads_per_block must be <= MAX_THREADS_PER_BLOCK");
    params.local_output_buffer_ptr = reinterpret_cast<void*>(output.data_ptr());
    ffwd::allreduce::dispatch_all_gather_elem_size<false>(params, output.element_size());
    auto stream = at::cuda::getCurrentCUDAStream().stream();
    ffwd::allreduce::reset_multi_gpu_barrier<<<1, 1, 0, stream>>>(
        params.gpu_barrier_ptrs[params.local_rank], params.world_size);
}

void do_all_gather_inplace(AllReduceParams params, torch::Tensor& output) {
    TORCH_CHECK(params.threads_per_block <= MAX_THREADS_PER_BLOCK,
                "threads_per_block must be <= MAX_THREADS_PER_BLOCK");
    params.local_output_buffer_ptr = reinterpret_cast<void*>(output.data_ptr());
    ffwd::allreduce::dispatch_all_gather_elem_size<true>(params, output.element_size());
    auto stream = at::cuda::getCurrentCUDAStream().stream();
    ffwd::allreduce::reset_multi_gpu_barrier<<<1, 1, 0, stream>>>(
        params.gpu_barrier_ptrs[params.local_rank], params.world_size);
}

void do_barrier(BarrierParams params) {
    auto stream = at::cuda::getCurrentCUDAStream().stream();
    ffwd::allreduce::barrier<<<1, params.world_size, 0, stream>>>(params);
}
