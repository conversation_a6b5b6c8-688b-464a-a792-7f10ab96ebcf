"""An allreduce implementation, hopefully faster in many cases than Torch+NCCL."""

from enum import Enum
from typing import Any, Callable, Sequence, TypeAlias

import torch
import torch.distributed as dist

from cuda import cuda, cudart  # type: ignore
from torch.distributed.distributed_c10d import ProcessGroup


CudaPtr: TypeAlias = int
CudaIpcMemHandle: TypeAlias = cudart.cudaIpcMemHandle_t


class AllReduceImpl(Enum):
    NCCL = 0
    FASTFORWARD = 1
    FASTFORWARD_FP8 = 2

    def is_fastforward(self):
        return self in (AllReduceImpl.FASTFORWARD, AllReduceImpl.FASTFORWARD_FP8)


ALL_ALL_REDUCE_IMPLS = [e for e in AllReduceImpl]


def cuda_assert(err: Any):
    """Throw an exception if `err` is not `cudaSuccess`."""
    if isinstance(err, cuda.CUresult):
        if err != cuda.CUresult.CUDA_SUCCESS:
            raise RuntimeError(f"Cuda Error: {err}")
    elif isinstance(err, cudart.cudaError_t):
        if err != cudart.cudaError_t.cudaSuccess:
            raise RuntimeError(f"Cuda Error: {err}")
    else:
        raise RuntimeError(f"Unknown error type: {type(err)}")


def cuda_check(ret):
    err, *rest = ret
    cuda_assert(err)
    if len(rest) == 0:
        return None
    elif len(rest) == 1:
        return rest[0]
    else:
        return tuple(rest)


def _allgather_ipc_handles(
    my_handle: CudaIpcMemHandle, process_group: ProcessGroup
) -> list[CudaIpcMemHandle]:
    """Share my_handle with peers and gather IPC handles from peers.

    Input:
        - my_handle: The handle to my memory.
        - world_size: The number of processes.

    Returns:
        - A list of handles organized by process_idx. List includes my_handle.
    """
    world_size = dist.get_world_size(group=process_group)
    my_handle_tensor = torch.frombuffer(
        bytearray(my_handle.reserved), dtype=torch.uint8
    ).cuda()
    gather_results = [torch.empty_like(my_handle_tensor) for _ in range(world_size)]
    dist.all_gather(gather_results, my_handle_tensor, group=process_group)
    all_handles: list[CudaIpcMemHandle] = []
    for handle_tensor in gather_results:
        handle = cudart.cudaIpcMemHandle_t()
        handle.reserved = bytes(handle_tensor.tolist())
        all_handles.append(handle)
    return all_handles


def _open_ipc_handles(
    all_handles: Sequence[CudaIpcMemHandle], my_rank: int, my_ptr: int
) -> list[CudaPtr]:
    """Open all cuda IPC handles in the Sequence except for the one at `my_rank`.

    Asserts on error.
    """
    all_ptrs = []
    for i, handle in enumerate(all_handles):
        if i == my_rank:
            all_ptrs.append(my_ptr)
        else:
            err, peer_ptr = cudart.cudaIpcOpenMemHandle(
                handle, cudart.cudaIpcMemLazyEnablePeerAccess
            )
            cuda_assert(err)
            all_ptrs.append(peer_ptr)
    return all_ptrs


def _close_ipc_handles(all_handles: Sequence[CudaIpcMemHandle], process_idx: int):
    """Close all cuda IPC handles in the Sequence except for the one at `process_idx`.

    Asserts on error.
    """
    assert process_idx < len(all_handles)

    for i, handle in enumerate(all_handles):
        if i != process_idx:
            (err,) = cudart.cudaIpcCloseMemHandle(handle)
            cuda_assert(err)


def _cuda_malloc(nbytes: int) -> CudaPtr:
    """Wrapper around cudaMalloc that checks for errors."""
    err, ptr = cudart.cudaMalloc(nbytes)
    cuda_assert(err)
    return ptr


def div_up(x: int, divisor: int) -> int:
    """Round up `x // divisor`"""
    return (x + divisor - 1) // divisor


def round_up(x: int, alignment: int) -> int:
    """Round `x` up to make it divisible by `alignment`."""
    return div_up(x, alignment) * alignment


def _calculate_kernel_config(
    num_processes: int, numel: int, dtype: torch.dtype, one_shot: bool
) -> tuple[int, int, int, int]:
    tensor_element_size = dtype.itemsize
    elts_per_thread = 16 // tensor_element_size
    tensor_size_in_elements = numel

    if one_shot:
        assert (
            tensor_size_in_elements % elts_per_thread == 0
        ), f"tensor_size_in_elements={tensor_size_in_elements} elts_per_thread={elts_per_thread}"
        total_threads = round_up(tensor_size_in_elements // elts_per_thread, 32)
        threads_per_block = min(1024, total_threads)
        blocks_per_grid = min(MAX_BLOCKS, div_up(total_threads, threads_per_block))
        elements_per_rank = tensor_size_in_elements
        elements_per_block = round_up(
            div_up(tensor_size_in_elements, blocks_per_grid), elts_per_thread
        )
    else:
        assert (
            tensor_size_in_elements % (elts_per_thread * num_processes) == 0
        ), f"tensor_size_in_elements={tensor_size_in_elements} elts_per_thread={elts_per_thread} num_processes={num_processes}"
        total_threads = round_up(
            tensor_size_in_elements // (elts_per_thread * num_processes), 32
        )
        blocks_per_grid = 1
        while (
            total_threads % blocks_per_grid != 0
            or total_threads // blocks_per_grid > 1024
        ):
            blocks_per_grid += 1
        threads_per_block = total_threads // blocks_per_grid

        if blocks_per_grid > MAX_BLOCKS:
            iter_factor = 1
            while (
                blocks_per_grid // iter_factor > MAX_BLOCKS
                or blocks_per_grid % iter_factor != 0
            ):
                iter_factor += 1
            blocks_per_grid //= iter_factor

        elements_per_rank = tensor_size_in_elements // num_processes
        elements_per_block = round_up(
            div_up(elements_per_rank, blocks_per_grid), elts_per_thread
        )

    return (
        blocks_per_grid,
        threads_per_block,
        elements_per_rank,
        elements_per_block,
    )


MAX_BLOCKS = 1024


class AllReduceKit:
    """Encapsulates steps of a repeatable collective communication operation.

    There are three major steps:
        - setup - caller tells AllReduceKit the shape of the max size tensor.
          AllReduceKit will allocate a buffer and share it to the peers.
          Setup current happens as part of __init__.

        - filling input tensor - caller uses get_input_tensor to get a tensor
          to fill with data.

        - all_reduce or all_gather - perform the communication.

    Once these steps are completed, the AllReduceKit can be reused. On subsequent uses,
    the setup step is not needed. The caller fills the input tensor and then calls
    all_reduce.

    AllReduceKit works with CUDA graphs.

    AllReduceKit requires a configured torch.distributed process group and for the
    current CUDA device to be correctly set.
    """

    def __init__(
        self,
        max_elements: int,
        dtype: torch.dtype,
        process_group: ProcessGroup,
        input_is_fp8: bool = False,
    ):
        """Setup the AllReduceKit.

        Args:
            max_elements: The maximum number of elements in the _input_ tensor. For all_reduce,
                this is the same as the output tensor. For all_gather, the output tensor will
                have num_processes times as many elements.
            dtype: The data type of the _output_ tensor. For all_gather, this is the same as the
                input tensor. For all_reduce, this is the same as the input tensor when
                input_is_fp8=False. In the fp8 case, the input is uint8 and the output is this
                provided dtype.
            process_group: The NCCL process group to match.
            input_is_fp8: If True, input must be uint8 and a scale factor must be provided to
                all_reduce. This argument is IGNORED when calling all_gather (since all_gather
                performs no math, so it doesn't care about the dtype).
        """

        assert dist.is_initialized()
        self.my_rank = dist.get_rank(group=process_group)
        self.world_size = dist.get_world_size(group=process_group)
        assert self.world_size > 1, "Must have more than one process."
        self.output_dtype = dtype
        self.input_is_fp8 = input_is_fp8

        tensor_size_in_elements = max_elements
        assert (
            tensor_size_in_elements % self.world_size == 0
        ), f"tensor_size_in_elements={tensor_size_in_elements} num_processes={self.world_size}"
        tensor_element_size = 1 if input_is_fp8 else dtype.itemsize

        tensor_size_in_bytes = tensor_size_in_elements * tensor_element_size
        self.input_buffer_size_bytes = tensor_size_in_bytes

        assert (
            tensor_size_in_bytes % 128 == 0
        ), f"tensor_size_in_bytes={tensor_size_in_bytes}"

        self.input_ptr = _cuda_malloc(tensor_size_in_bytes)
        (err,) = cudart.cudaMemset(self.input_ptr, 0, tensor_size_in_bytes)
        cuda_assert(err)

        barrier_size_bytes = (self.world_size + 1) * 4
        barrier_ptr = _cuda_malloc(barrier_size_bytes)
        (err,) = cudart.cudaMemset(barrier_ptr, 0, barrier_size_bytes)
        cuda_assert(err)
        err, input_handle = cudart.cudaIpcGetMemHandle(self.input_ptr)
        cuda_assert(err)
        err, barrier_handle = cudart.cudaIpcGetMemHandle(barrier_ptr)
        cuda_assert(err)

        block_barrier_size = self.world_size * MAX_BLOCKS * 4
        block_barrier_ptr = _cuda_malloc(block_barrier_size)
        (err,) = cudart.cudaMemset(block_barrier_ptr, 0, block_barrier_size)
        cuda_assert(err)
        err, block_barrier_handle = cudart.cudaIpcGetMemHandle(block_barrier_ptr)
        cuda_assert(err)

        self.all_input_handles = _allgather_ipc_handles(input_handle, process_group)
        self.all_barrier_handles = _allgather_ipc_handles(barrier_handle, process_group)
        self.all_block_barrier_handles = _allgather_ipc_handles(
            block_barrier_handle, process_group
        )

        self.all_input_ptrs = _open_ipc_handles(
            self.all_input_handles, self.my_rank, self.input_ptr
        )
        self.all_barrier_ptrs = _open_ipc_handles(
            self.all_barrier_handles, self.my_rank, barrier_ptr
        )
        self.all_block_barrier_ptrs = _open_ipc_handles(
            self.all_block_barrier_handles, self.my_rank, block_barrier_ptr
        )
        self.input_tensor: torch.Tensor | None = None

    def get_input_tensor(self, shape: torch.Size, dtype: torch.dtype) -> torch.Tensor:
        """Returns the input tensor associated with this AllReduceKit.

        The input tensor is not reallocated between calls to `all_reduce()`.

        Warnings:
            - The product of `shape` must be less than `max_elements` passed to the constructor.
            - the contents of the input tensor may be overwritten by the all_reduce.
        """
        from base.fastforward import all_reduce_kernel  # pylint: disable=import-outside-toplevel

        assert (
            shape.numel() * dtype.itemsize <= self.input_buffer_size_bytes
        ), f"{shape} {dtype} > {self.input_buffer_size_bytes}"
        if self.input_is_fp8:
            assert dtype == torch.uint8, f"FP8 input must be uint8, got {dtype}"

        input_tensor = all_reduce_kernel.cuda_ptr_to_tensor(
            shape,
            dtype,
            self.input_ptr,
            self.input_buffer_size_bytes,
        )
        assert input_tensor is not None

        # Don't garbage collect AllReduceKit before input_tensor
        input_tensor.augment_all_reduce_ref = self  # type: ignore

        self.input_tensor = input_tensor
        return input_tensor

    def _use_one_shot_heuristic(self, numel: int) -> bool:
        """Heuristic for whether to use one-shot or two-shot all_reduce."""
        # One-shot is strictly better for two processes, and it's the only supported impl for fp8.
        if self.world_size <= 2 or self.input_is_fp8:
            return True
        # NOTE(Carl): the general tradeoff here is:
        # - As you add more GPUs, two-shot is better in general (less excess traffic).
        # - One-shot is better for small messages (latency-dominated).
        # My H100 measurements (Jan 2025) are:
        # - 4-GPU cutoff is ~1MB
        # - 8-GPU cutoff is ~500KB
        buf_num_bytes = numel * self.output_dtype.itemsize
        if self.world_size <= 4:
            return buf_num_bytes < 1024 * 1024
        else:
            return buf_num_bytes < 512 * 1024

    def all_reduce(
        self,
        out: torch.Tensor | None = None,
        one_shot: bool | None = None,
        fp8_scale: torch.Tensor | None = None,
        residual_buffer: torch.Tensor | None = None,
    ) -> torch.Tensor:
        """Perform the all_reduce on the contents of the input tensor.

        The input tensor may be overwritten by the all_reduce, so the result is
        returned in a new tensor. Note that this is different from pytorch's
        built-in all_reduce (which operates in-place).

        The tensor passed in the `out` argument receives the result of the
        all_reduce operation. It must have the same shape, dtype, and device as the
        input tensor. If no output is provided, an empty output is allocated and used.

        You can specify whether to use one-shot or two-shot all_reduce by setting one_shot to
        True or False, respectively. If one_shot is None (default), a heuristic is used to decide.

        If a residual_buffer is provided, it's added to the all_reduce result.

        Warning:
            - The input tensor may be overwritten by the all_reduce.
            - `all_reduce` on one GPU may complete while `all_reduce` is still running
               on other GPUs. Those GPUs may be reading the input buffer.

               In other words, all_reduce does not perform the necessary
               synchronization for the caller to know that all GPUs have finished
               using the input buffer.
        """
        from base.fastforward import all_reduce_kernel  # pylint: disable=import-outside-toplevel

        assert self.input_tensor is not None
        if one_shot is None:
            one_shot = self._use_one_shot_heuristic(self.input_tensor.numel())
        if self.input_is_fp8:
            assert fp8_scale is not None, "Must provide fp8_scale if input is fp8."
            assert one_shot, "FP8 all_reduce supports only one_shot."
            assert (
                fp8_scale.numel() == 1
            ), f"Expected a single scale, got {fp8_scale.numel()}"
        else:
            assert fp8_scale is None, "fp8_scale must be None if input is not fp8."
        if out is None:
            out = torch.empty_like(self.input_tensor, dtype=self.output_dtype)
        assert (
            out.shape == self.input_tensor.shape
        ), f"{out.shape} != {self.input_tensor.shape}"
        if self.input_is_fp8:
            assert self.input_tensor.dtype == torch.uint8, f"{self.input_tensor.dtype}"
            assert out.dtype == self.output_dtype, f"{out.dtype} != {self.output_dtype}"
        else:
            assert (
                out.dtype == self.input_tensor.dtype
            ), f"{out.dtype} != {self.input_tensor.dtype}"
        assert out.is_cuda
        assert out.is_contiguous()
        assert (
            self.input_tensor.stride() == out.stride()
        ), f"{self.input_tensor.stride()} != {out.stride()}"
        if residual_buffer is not None:
            assert (
                residual_buffer.shape == out.shape
            ), f"{residual_buffer.shape} != {out.shape}"
            assert (
                residual_buffer.dtype == self.output_dtype
            ), f"{residual_buffer.dtype} != {self.output_dtype}"
            assert residual_buffer.is_cuda, f"{residual_buffer.is_cuda}"
            assert residual_buffer.is_contiguous()
        assert (
            out.device == self.input_tensor.device
        ), f"{out.device} != {self.input_tensor.device}"
        (blocks_per_grid, threads_per_block, elements_per_rank, elements_per_block) = (
            _calculate_kernel_config(
                self.world_size,
                self.input_tensor.numel(),
                self.input_tensor.dtype,
                one_shot,
            )
        )

        all_reduce_kernel.all_reduce(
            self.my_rank,
            self.world_size,
            out,
            self.all_input_ptrs,
            self.all_barrier_ptrs,
            self.all_block_barrier_ptrs,
            self.input_tensor.numel(),
            blocks_per_grid,
            threads_per_block,
            one_shot,
            elements_per_block,
            elements_per_rank,
            self.input_is_fp8,
            fp8_scale,
            residual_buffer,
        )
        return out

    def all_gather(
        self,
        out: torch.Tensor | None = None,
    ):
        """Perform an all_gather on the contents of the input tensor.

        The all_gather is on the _leading_ dimension of the input. So if the input has shape
        (dim1, *), the output will have shape (num_processes * dim1, *). If `out` is None, then
        an empty output is allocated. Both input and output must be contiguous.
        """
        assert self.input_tensor is not None
        leading_dim, rest_dims = self.input_tensor.shape[0], self.input_tensor.shape[1:]
        if out is None:
            out = torch.empty(
                self.world_size * leading_dim,
                *rest_dims,
                dtype=self.input_tensor.dtype,
                device=self.input_tensor.device,
            )
        assert (
            out.shape[0] == self.world_size * leading_dim
        ), f"{out.shape[0]} != {self.world_size * leading_dim}"
        assert out.shape[1:] == rest_dims, f"{out.shape[1:]} != {rest_dims}"
        assert (
            out.dtype == self.input_tensor.dtype
        ), f"{out.dtype} != {self.input_tensor.dtype}"
        assert (
            out.device == self.input_tensor.device
        ), f"{out.device} != {self.input_tensor.device}"
        assert self.input_tensor.is_contiguous()
        assert out.is_contiguous()
        (blocks_per_grid, threads_per_block, elements_per_rank, elements_per_block) = (
            _calculate_kernel_config(
                self.world_size, out.numel(), out.dtype, one_shot=False
            )
        )

        from base.fastforward import all_reduce_kernel  # pylint: disable=import-outside-toplevel

        all_reduce_kernel.all_gather(
            self.my_rank,
            self.world_size,
            out,
            self.all_input_ptrs,
            self.all_barrier_ptrs,
            out.numel(),
            blocks_per_grid,
            threads_per_block,
            elements_per_block,
            elements_per_rank,
        )
        return out

    def barrier(self):
        """Wait for all GPUs to reach this point."""
        from base.fastforward import all_reduce_kernel  # pylint: disable=import-outside-toplevel

        all_reduce_kernel.barrier(self.my_rank, self.world_size, self.all_barrier_ptrs)

    def fits(self, shape: torch.Size, dtype: torch.dtype) -> bool:
        """Returns true if the shape fits in the input buffer."""
        return self.input_buffer_size_bytes >= shape.numel() * dtype.itemsize

    def __del__(self):
        if hasattr(self, "all_input_ptrs"):
            _close_ipc_handles(self.all_input_ptrs, self.my_rank)
        if hasattr(self, "all_barrier_ptrs"):
            _close_ipc_handles(self.all_barrier_ptrs, self.my_rank)

        if hasattr(self, "input_ptr"):
            (err,) = cudart.cudaFree(self.input_ptr)
            cuda_assert(err)


class AllGatherKit:
    """This class is a specialization of AllReduceKit for exactly the case of all_gather on the
    leading dimension.
    """

    def __init__(
        self,
        input_max_numel: int,
        dtype: torch.dtype,
        process_group: ProcessGroup,
    ):
        """Initialize the AllGatherKit.

        Args:
            input_max_numel: The maximum number of elements in the input tensor. The result will
                have num_processes times as many elements.
            dtype: The data type of the input and output tensors.
            process_group: The NCCL process group to match.
        """
        self.input_max_numel = input_max_numel
        self.dtype = dtype
        self.my_rank = dist.get_rank(group=process_group)
        self.world_size = dist.get_world_size(group=process_group)

        buffer_size_bytes = input_max_numel * dtype.itemsize * self.world_size
        self.buffer_ptr = _cuda_malloc(buffer_size_bytes)
        cuda_check(cudart.cudaMemset(self.buffer_ptr, 0, buffer_size_bytes))

        barrier_size_bytes = (self.world_size + 1) * 4
        self.barrier_ptr = _cuda_malloc(barrier_size_bytes)
        cuda_check(cudart.cudaMemset(self.barrier_ptr, 0, barrier_size_bytes))

        buffer_handle = cuda_check(cudart.cudaIpcGetMemHandle(self.buffer_ptr))
        barrier_handle = cuda_check(cudart.cudaIpcGetMemHandle(self.barrier_ptr))

        self.all_buffer_handles = _allgather_ipc_handles(buffer_handle, process_group)
        self.all_barrier_handles = _allgather_ipc_handles(barrier_handle, process_group)

        self.all_buffer_ptrs = _open_ipc_handles(
            self.all_buffer_handles, self.my_rank, self.buffer_ptr
        )
        self.all_barrier_ptrs = _open_ipc_handles(
            self.all_barrier_handles, self.my_rank, self.barrier_ptr
        )

    def _get_result_tensor_for_shape(self, input_shape: torch.Size) -> torch.Tensor:
        from base.fastforward import all_reduce_kernel  # pylint: disable=import-outside-toplevel

        assert (
            input_shape.numel() <= self.input_max_numel
        ), f"{input_shape} > {self.input_max_numel}"
        dim0, *rest_dims = input_shape
        result_shape = torch.Size([dim0 * self.world_size, *rest_dims])
        result_tensor = all_reduce_kernel.cuda_ptr_to_tensor(
            result_shape,
            self.dtype,
            self.buffer_ptr,
            result_shape.numel() * self.dtype.itemsize,
        )
        return result_tensor

    def get_input_tensor(self, input_shape: torch.Size) -> torch.Tensor:
        """Access the input tensor in order to copy your process' data into it.

        `input_shape` must be the same across all participating processes.

        NOTE(all-gather-synchronization): the _start_ of each all_gather call serves as a global
        barrier among all participating GPUs. This ensures that the input data is ready everywhere.
        However, there is no barrier at the _end_: each GPU can proceed as soon as it has gathered
        all the data. As a result, if you plan to modify the input buffer later, you must provide
        additional inter-GPU synchronization so that no GPU can race ahead and modify its input
        buffer while other GPUs are still reading it from a previous all_gather call.

        The most common way to do this is to use _two_ AllGatherKit's in a ping-pong fashion so
        that each all_gather call serves as a barrier for the other one. If you absolutely must
        have a plain barrier, you can use the barrier() method.
        """
        result_tensor = self._get_result_tensor_for_shape(input_shape)
        my_start = input_shape[0] * self.my_rank
        my_end = my_start + input_shape[0]
        input_tensor = result_tensor[my_start:my_end, ...]
        assert input_tensor.is_contiguous()
        return input_tensor

    def all_gather(self, input_shape: torch.Size) -> torch.Tensor:
        """Perform all_gather on the contents of the input tensor and return the result.

        See NOTE(all-gather-synchronization) above for information on race safety.
        """

        from base.fastforward import all_reduce_kernel  # pylint: disable=import-outside-toplevel

        result_tensor = self._get_result_tensor_for_shape(input_shape)
        (blocks_per_grid, threads_per_block, elements_per_rank, elements_per_block) = (
            _calculate_kernel_config(
                self.world_size,
                result_tensor.numel(),
                self.dtype,
                one_shot=False,
            )
        )

        all_reduce_kernel.all_gather_inplace(
            self.my_rank,
            self.world_size,
            self.all_buffer_ptrs,
            self.all_barrier_ptrs,
            result_tensor,
            blocks_per_grid,
            threads_per_block,
            elements_per_block,
            elements_per_rank,
        )
        return result_tensor

    def barrier(self):
        """Wait for all GPUs to reach this point."""
        from base.fastforward import all_reduce_kernel  # pylint: disable=import-outside-toplevel

        all_reduce_kernel.barrier(self.my_rank, self.world_size, self.all_barrier_ptrs)

    def __del__(self):
        if hasattr(self, "all_buffer_ptrs"):
            _close_ipc_handles(self.all_buffer_ptrs, self.my_rank)
        if hasattr(self, "all_barrier_ptrs"):
            _close_ipc_handles(self.all_barrier_ptrs, self.my_rank)
        if hasattr(self, "buffer_ptr"):
            cuda_check(cudart.cudaFree(self.buffer_ptr))
        if hasattr(self, "barrier_ptr"):
            cuda_check(cudart.cudaFree(self.barrier_ptr))


class DoubleBufferedCommunicator:
    """A wrapper class for holding two All{Reduce,Gather}Kit objects and switching between them.

    A pattern common to our custom communicators is to use two instances and switch back-and-forth
    to provide mutual synchronization. See NOTE(all-gather-synchronization) above for more
    discussion. This class wraps this pattern while also checking that you use them in the right
    order. See `fwd_llama_fp8.py` for an example usage.
    """

    def __init__(
        self,
        num_calls: int,
        constructor,
        *args,
        **kwargs,
    ):
        self._num_calls = num_calls
        self._cur_calls = 0
        self._comm0 = constructor(*args, **kwargs)
        self._comm1 = constructor(*args, **kwargs)

    def get(self, idx: int):
        if idx != self._cur_calls:
            raise RuntimeError("Out-of-order use of DoubleBufferedCommunicator")
        self._cur_calls += 1
        return self._comm0 if idx % 2 == 0 else self._comm1

    def verify_num_calls(self):
        if self._cur_calls != self._num_calls:
            raise RuntimeError(
                "DoubleBufferedCommunicator not used the right number of times"
            )
