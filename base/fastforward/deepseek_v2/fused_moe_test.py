"""Tests for the fused MoE implementation."""

import pytest
import torch
import torch.nn.functional as F

from base.fastforward.deepseek_v2.fused_moe import FusedMoEBlock, fused_topk
from base.fastforward.deepseek_v2.fwd_dsv2 import MLP, ReferenceMoEBlock


@pytest.mark.parametrize("ntokens", [1, 17, 429])
@pytest.mark.parametrize(
    "hidden_size, intermediate_size, num_experts, topk_experts",
    [
        (256, 128, 4, 2),
        (2048, 1408, 64, 6),
    ],
)
@pytest.mark.parametrize("dtype", [torch.float16, torch.bfloat16])
def test_fused_moe_block(
    ntokens: int,
    hidden_size: int,
    intermediate_size: int,
    num_experts: int,
    topk_experts: int,
    dtype: torch.dtype,
    seed: int = 1234,
):
    torch.cuda.manual_seed(seed)
    fused_moe = FusedMoEBlock(
        hidden_size=hidden_size,
        intermediate_size=intermediate_size,
        num_experts=num_experts,
        dtype=dtype,
    )
    torch.nn.init.normal_(fused_moe.experts_gate_up_proj, mean=0.0, std=0.02)
    torch.nn.init.normal_(fused_moe.experts_down_proj, mean=0.0, std=0.02)
    reference_moe = ReferenceMoEBlock(
        hidden_size=hidden_size,
        intermediate_size=intermediate_size,
        num_experts=num_experts,
        dtype=dtype,
    )
    # Copy weights from the fused MoE block to the reference MoE block
    for i in range(num_experts):
        expert: MLP = reference_moe.experts[i]
        expert.gate_proj.weight.data.copy_(
            fused_moe.experts_gate_up_proj[i, :intermediate_size]
        )
        expert.up_proj.weight.data.copy_(
            fused_moe.experts_gate_up_proj[i, intermediate_size:]
        )
        expert.down_proj.weight.data.copy_(fused_moe.experts_down_proj[i])

    # Allocate random router weights and compute topk fused and unfused
    inputs = torch.empty(ntokens, hidden_size, dtype=dtype, device="cuda")
    torch.nn.init.normal_(inputs, mean=0.0, std=0.02)
    router_weights = torch.randn(num_experts, hidden_size, dtype=dtype, device="cuda")
    logits = F.linear(inputs, router_weights).to(torch.float32)
    scores = torch.softmax(logits, dim=-1)

    fused_topk_weights, fused_topk_idxs = fused_topk(inputs, logits, topk_experts)
    ref_topk_weights, ref_topk_idxs = torch.topk(
        scores, k=topk_experts, dim=-1, sorted=False
    )
    # Check that we get the same topk after sorting (since they might have different orderings)
    torch.testing.assert_close(
        fused_topk_weights.sort(dim=-1).values, ref_topk_weights.sort(dim=-1).values
    )
    torch.testing.assert_close(
        fused_topk_idxs.sort(dim=-1).values,
        ref_topk_idxs.to(torch.int32).sort(dim=-1).values,
    )

    fused_out = fused_moe(inputs, fused_topk_idxs, fused_topk_weights)
    ref_out = reference_moe(inputs, ref_topk_idxs, ref_topk_weights)

    torch.testing.assert_close(fused_out, ref_out, atol=1e-2, rtol=1e-2)
