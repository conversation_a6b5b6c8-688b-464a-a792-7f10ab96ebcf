r"""ModelSpecs for the DeepSeek-V2 models."""

import dataclasses
import pathlib

from base.fastforward import positional_embeddings


@dataclasses.dataclass
class MLPSpec:
    """ModelSpec for the DeepSeek-V2 MLP layer."""

    hidden_dim: int = 2048
    """The hidden dimension of the MLP layer."""

    intermediate_size: int = 4096
    """The intermediate size of the MLP layer."""


@dataclasses.dataclass
class MoESpec:
    """ModelSpec for the DeepSeek-V2 MoE layer."""

    hidden_dim: int = 2048
    """The feature dimension of the input tensor."""

    n_group: int = 1
    """The number of expert groups in the MoE layer."""

    n_routed_experts: int = 16
    """The number of experts in the MoE layer."""

    routed_scaling_factor: float = 1.0
    """The scaling factor for the routed experts."""

    num_experts_per_token: int = 6
    """The number of experts per token."""

    intermediate_size: int = 1408
    """The intermediate size of each routed expert."""

    n_shared_experts: int = 2
    """The number of shared experts."""

    topk_group: int = 1
    """The number of expert groups to include in grouped top-k"""

    topk_method: str = "greedy"
    """The topk method of the MoE layer. One of:
    greedy
    group_limited_greedy
    """

    implementation: str = "reference"
    """Which implementation to use (one of "fused" or "reference")."""


@dataclasses.dataclass
class DeepSeekV2MLASpec:
    """The DeepSeek-V2 MLA Spec"""

    hidden_dim: int = 2048
    """The hidden dimension of the MLP layer."""

    num_heads: int = 1
    """The number of attention key/value heads."""

    v_head_dim: int = 128
    """The hidden dimension of each attention head."""

    q_lora_rank: int | None = None

    kv_lora_rank: int = 512

    qk_rope_head_dim: int = 64

    qk_nope_head_dim: int = 128

    bias: bool = False

    use_mqa_for_mla: bool = False
    """Whether to use the MQA trick."""


@dataclasses.dataclass
class DeepSeekV2ModelSpec:
    """ModelSpec for DeepSeek-V2 models."""

    name: str
    """The name of the model."""

    checkpoint_path: str
    """The path to the checkpoint."""

    checkpoint_sha256: str | None
    """The SHA256 hash of the checkpoint."""

    num_layers: int
    """The number of layers in the model."""

    vocab_size: int
    """The size of the vocabulary."""

    emb_dim: int
    """The hidden dimension of the model as well as the embedding dimension."""

    norm_eps: float
    """The epsilon value for the RMS norm."""

    attention: DeepSeekV2MLASpec

    mlp: MLPSpec

    moe: MoESpec

    rope: positional_embeddings.RotaryConfig


def get_model_spec(
    model_name: str,
    checkpoint_path: pathlib.Path | str = "",
    checkpoint_sha256: str | None = None,
) -> DeepSeekV2ModelSpec:
    """Returns a DeepSeek-Coder-V2 model spec."""
    if isinstance(checkpoint_path, pathlib.Path):
        checkpoint_path = str(checkpoint_path.absolute())
    if model_name == "deepseek-coder-v2":
        ms = DeepSeekV2ModelSpec(
            name="deepseek-coder-v2",
            checkpoint_path=checkpoint_path,
            checkpoint_sha256=checkpoint_sha256,
            num_layers=60,
            vocab_size=102400,
            emb_dim=5120,
            norm_eps=1e-06,
            attention=DeepSeekV2MLASpec(
                hidden_dim=5120,
                num_heads=128,
                v_head_dim=128,
                q_lora_rank=1536,
                kv_lora_rank=512,
                qk_rope_head_dim=64,
                qk_nope_head_dim=128,
                bias=False,
            ),
            mlp=MLPSpec(hidden_dim=5120, intermediate_size=12288),
            moe=MoESpec(
                hidden_dim=5120,
                n_routed_experts=160,
                n_group=8,
                routed_scaling_factor=16.0,
                num_experts_per_token=6,
                intermediate_size=1536,
                n_shared_experts=2,
                topk_group=3,
                topk_method="group_limited_greedy",
            ),
            rope=positional_embeddings.RotaryConfig(
                rotary_ratio=1.0 / 3 + 1e-7,
                rotary_theta=10000.0,
                max_position_embeddings=163840,
                ext_config=positional_embeddings.YaRNExtensionConfig(
                    rotary_scaling_factor=40.0,
                    unscaled_max_position_embeddings=4096,
                    beta_fast=32,
                    beta_slow=1,
                    mscale=1,
                ),
                rotary_interleave=True,
            ),
        )
    elif model_name == "deepseek-coder-v2-nano":
        # This is created by reducing deepseek-coder-v2-lite into 3 layers.
        ms = DeepSeekV2ModelSpec(
            name="deepseek-coder-v2-nano",
            checkpoint_path=checkpoint_path,
            checkpoint_sha256=checkpoint_sha256,
            num_layers=3,
            vocab_size=102400,
            emb_dim=2048,
            norm_eps=1e-06,
            attention=DeepSeekV2MLASpec(
                hidden_dim=2048,
                num_heads=16,
                v_head_dim=128,
                q_lora_rank=None,
                kv_lora_rank=512,
                qk_rope_head_dim=64,
                qk_nope_head_dim=128,
                bias=False,
            ),
            mlp=MLPSpec(hidden_dim=2048, intermediate_size=10944),
            moe=MoESpec(
                hidden_dim=2048,
                n_routed_experts=64,
                routed_scaling_factor=1.0,
                num_experts_per_token=6,
                intermediate_size=1408,
                n_shared_experts=2,
                topk_method="greedy",
            ),
            rope=positional_embeddings.RotaryConfig(
                rotary_ratio=1.0 / 3 + 1e-7,
                rotary_theta=10000.0,
                max_position_embeddings=163840,
                ext_config=positional_embeddings.YaRNExtensionConfig(
                    rotary_scaling_factor=40.0,
                    unscaled_max_position_embeddings=4096,
                    beta_fast=32,
                    beta_slow=1,
                    mscale=0.707,
                ),
                rotary_interleave=True,
            ),
        )
    elif model_name == "deepseek-coder-v2-nano-mqa":
        # This is created by reducing deepseek-coder-v2-lite into 3 layers.
        ms = DeepSeekV2ModelSpec(
            name="deepseek-coder-v2-nano-mqa",
            checkpoint_path=checkpoint_path,
            checkpoint_sha256=checkpoint_sha256,
            num_layers=3,
            vocab_size=102400,
            emb_dim=2048,
            norm_eps=1e-06,
            attention=DeepSeekV2MLASpec(
                hidden_dim=2048,
                num_heads=16,
                v_head_dim=128,
                q_lora_rank=None,
                kv_lora_rank=512,
                qk_rope_head_dim=64,
                qk_nope_head_dim=128,
                bias=False,
                use_mqa_for_mla=True,
            ),
            mlp=MLPSpec(hidden_dim=2048, intermediate_size=10944),
            moe=MoESpec(
                hidden_dim=2048,
                n_routed_experts=64,
                routed_scaling_factor=1.0,
                num_experts_per_token=6,
                intermediate_size=1408,
                n_shared_experts=2,
                topk_method="greedy",
            ),
            rope=positional_embeddings.RotaryConfig(
                rotary_ratio=1.0 / 9 + 1e-7,
                rotary_theta=10000.0,
                max_position_embeddings=163840,
                ext_config=positional_embeddings.YaRNExtensionConfig(
                    rotary_scaling_factor=40.0,
                    unscaled_max_position_embeddings=4096,
                    beta_fast=32,
                    beta_slow=1,
                    mscale=0.707,
                ),
                rotary_interleave=True,
            ),
        )
    elif model_name == "deepseek-coder-v2-lite":
        ms = DeepSeekV2ModelSpec(
            name="deepseek-coder-v2-lite",
            checkpoint_path=checkpoint_path,
            checkpoint_sha256=checkpoint_sha256,
            num_layers=27,
            vocab_size=102400,
            emb_dim=2048,
            norm_eps=1e-06,
            attention=DeepSeekV2MLASpec(
                hidden_dim=2048,
                num_heads=16,
                v_head_dim=128,
                q_lora_rank=None,
                kv_lora_rank=512,
                qk_rope_head_dim=64,
                qk_nope_head_dim=128,
                bias=False,
            ),
            mlp=MLPSpec(hidden_dim=2048, intermediate_size=10944),
            moe=MoESpec(
                hidden_dim=2048,
                n_routed_experts=64,
                routed_scaling_factor=1.0,
                num_experts_per_token=6,
                intermediate_size=1408,
                n_shared_experts=2,
                topk_method="greedy",
            ),
            rope=positional_embeddings.RotaryConfig(
                rotary_ratio=1.0 / 3 + 1e-7,
                rotary_theta=10000.0,
                max_position_embeddings=163840,
                ext_config=positional_embeddings.YaRNExtensionConfig(
                    rotary_scaling_factor=40.0,
                    unscaled_max_position_embeddings=4096,
                    beta_fast=32,
                    beta_slow=1,
                    mscale=0.707,
                ),
                rotary_interleave=True,
            ),
        )
    else:
        raise ValueError(f"Unknown model name: {model_name}")
    return ms
