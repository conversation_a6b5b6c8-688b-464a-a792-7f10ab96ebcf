import torch


def topk_softmax(topk_weights, topk_ids, token_expert_indices, gating_output):
    from base.fastforward.deepseek_v2 import vllm_compiled_ops

    vllm_compiled_ops.topk_softmax(
        topk_weights, topk_ids, token_expert_indices, gating_output
    )


def moe_align_block_size(
    topk_ids: torch.Tensor,
    num_experts: int,
    block_size: int,
    sorted_token_ids: torch.Tensor,
    experts_ids: torch.Tensor,
    num_tokens_post_pad: torch.Tensor,
) -> None:
    from base.fastforward.deepseek_v2 import vllm_compiled_ops

    vllm_compiled_ops.moe_align_block_size(
        topk_ids,
        num_experts,
        block_size,
        sorted_token_ids,
        experts_ids,
        num_tokens_post_pad,
    )
