"""Fastforward implementation for the DeepSeek-V2 architectures.

This file is currently just a structure for the DeepSeek-V2 model family.

TODO:
- support tensor parallelism for Attention
- replace the vanilla implementation with efficient one.
- optimize the checkpoint loading.
"""

import logging
import re
from typing import Sequence

import torch
import torch.distributed as dist
import torch.nn as nn
import torch.nn.functional as F

from base.fastforward import cuda_graphs_attention, fwd, fwd_torch, layers, torch_utils
from base.fastforward.cached_attention import (
    Attention,
    MultiCacheAttention,
    MultiCacheAttentionImplementation,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.checkpoints.impl import sharding
from base.fastforward.deepseek_v2 import model_specs
from base.fastforward.deepseek_v2.fused_moe import FusedMoEBlock, fused_topk
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.parallel_fwd import ParallelForwardRunner

Tensor = torch.Tensor
Device = layers.Device
WordEmbeddings = layers.WordEmbeddings


class MLP(nn.Module):
    """The MLP layer used in the DeepSeek-V2 model."""

    def __init__(
        self,
        hidden_size: int,
        intermediate_size: int,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
    ):
        super().__init__()
        self.hidden_size = hidden_size
        if intermediate_size % num_processes:
            raise ValueError(
                f"{intermediate_size=} is not divisible by {num_processes=}. "
                "Cannot run with this tensor parallelism."
            )
        self.intermediate_size = intermediate_size // num_processes
        self._process_idx = process_idx
        self._num_processes = num_processes
        self.gate_proj = nn.Linear(
            self.hidden_size,
            self.intermediate_size,
            device=device,
            dtype=dtype,
            bias=False,
        )
        self.up_proj = nn.Linear(
            self.hidden_size,
            self.intermediate_size,
            device=device,
            dtype=dtype,
            bias=False,
        )
        self.down_proj = nn.Linear(
            self.intermediate_size,
            self.hidden_size,
            device=device,
            dtype=dtype,
            bias=False,
        )
        self.act_fn = nn.SiLU()

    @torch.no_grad()
    def forward(self, x):
        down_proj = self.down_proj(self.act_fn(self.gate_proj(x)) * self.up_proj(x))
        return down_proj


class ReferenceMoEBlock(nn.Module):
    """Python for-loop based implementation of a single MoE block."""

    def __init__(
        self,
        hidden_size: int,
        intermediate_size: int,
        num_experts: int,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
    ):
        super().__init__()
        self.experts = nn.ModuleList(
            [
                MLP(
                    hidden_size=hidden_size,
                    intermediate_size=intermediate_size,
                    device=device,
                    dtype=dtype,
                    process_idx=process_idx,
                    num_processes=num_processes,
                )
                for i in range(num_experts)
            ]
        )

    def forward(
        self, inputs: torch.Tensor, topk_idxs: torch.Tensor, topk_weights: torch.Tensor
    ) -> torch.Tensor:
        cnts = topk_idxs.new_zeros((topk_idxs.shape[0], len(self.experts)))
        cnts.scatter_(1, topk_idxs, 1)
        tokens_per_expert = cnts.sum(dim=0)
        idxs = topk_idxs.view(-1).argsort()
        sorted_tokens = inputs[idxs // topk_idxs.shape[1]]

        tokens_per_expert = tokens_per_expert.cpu().numpy()
        outputs, start_idx = [], 0
        for i, num_tokens in enumerate(tokens_per_expert):
            if num_tokens == 0:
                continue
            end_idx = start_idx + num_tokens
            expert = self.experts[i]
            tokens_for_this_expert = sorted_tokens[start_idx:end_idx]
            expert_out = expert(tokens_for_this_expert)
            outputs.append(expert_out)
            start_idx = end_idx
        if len(outputs) == 0:
            raise ValueError("No experts are selected.")
        outs = torch.cat(outputs, dim=0)
        new_x = torch.empty_like(outs)
        new_x[idxs] = outs
        result = (
            new_x.view(*topk_idxs.shape, -1)
            .type(topk_weights.dtype)
            .mul_(topk_weights.unsqueeze(dim=-1))
            .sum(dim=1)
            .type(new_x.dtype)
        )
        return result


class DeepSeekV2MoE(nn.Module):
    """The DeepSeek-V2-MoE layer, a mix of shared experts + routed experts."""

    def __init__(
        self,
        config: model_specs.MoESpec,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
    ):
        super().__init__()
        self._device = device
        self._process_idx = process_idx
        self._num_processes = num_processes
        self._config = config
        assert config.n_shared_experts >= 1
        if config.implementation == "reference":
            MoeCls = ReferenceMoEBlock
        elif config.implementation == "fused":
            MoeCls = FusedMoEBlock
        else:
            raise ValueError(f"Unknown MoE implementation: {config.implementation}")
        self.routed_experts = MoeCls(
            hidden_size=config.hidden_dim,
            intermediate_size=config.intermediate_size,
            num_experts=config.n_routed_experts,
            dtype=dtype,
            device=device,
            process_idx=process_idx,
            num_processes=num_processes,
        )
        self.use_fused_topk = config.implementation == "fused"
        # No tensor parallelism for gate_weight
        self.gate_weight = nn.Parameter(
            torch.empty(
                (config.n_routed_experts, config.hidden_dim), device=device, dtype=dtype
            )
        )
        self.shared_experts = MLP(
            hidden_size=config.hidden_dim,
            intermediate_size=config.intermediate_size * config.n_shared_experts,
            device=device,
            dtype=dtype,
            process_idx=process_idx,
            num_processes=num_processes,
        )

    @staticmethod
    def group_limited_greedy(
        scores: torch.Tensor, n_group: int, topk_group: int, topk: int
    ) -> torch.return_types.topk:
        """Perform torch topk with additional stipulation that selected indexes
        will be taken from at most topk_group groups.

        A group is defined as the set of scores for which (score idx / n_group) is the same.

        Pre: all scores are non-negative

        Note: the scores tensor will be mutated by this function to mask out scores
        for experts not in the topk_group groups. Copy it before calling this function
        if you need to preserve the original scores.
        """
        # 1. First select top groups for each token
        # scores shape [n_tokens, n_scores]
        # view as [n_tokens, n_group, n_scores_per_group]
        grouped = scores.view(scores.shape[0], n_group, -1)
        # [n_tokens, n_group]
        group_scores = grouped.max(dim=-1).values
        _, topk_idx = torch.topk(group_scores, k=topk_group, dim=-1, sorted=False)

        # 2. Mask out scores for experts not in those groups
        # [n_tokens, n_group]
        mask = group_scores.new_zeros(group_scores.shape)
        mask.scatter_(1, topk_idx, 1)
        # [n_tokens, n_group, n_scores_per_group]
        mask = mask.view(mask.shape[0], mask.shape[1], 1).expand_as(grouped)
        # [n_tokens, n_scores]
        # Forced to reshape rather than view I believe due to expand()
        mask = mask.reshape(scores.shape)
        scores.masked_fill_(~mask.bool(), 0.0)

        # 3. Now select top experts based on remaining scores
        return torch.topk(scores, k=topk, dim=-1, sorted=False)

    def compute_gate(self, inputs: torch.Tensor):
        logits = F.linear(
            inputs.type(torch.float32), self.gate_weight.type(torch.float32), None
        )
        if self._config.topk_method == "greedy":
            if self.use_fused_topk:
                topk_weight, topk_idx = fused_topk(
                    inputs, logits, self._config.num_experts_per_token
                )
            else:
                scores = logits.softmax(dim=-1, dtype=torch.float32)
                topk_weight, topk_idx = torch.topk(
                    scores, k=self._config.num_experts_per_token, dim=-1, sorted=False
                )
        elif self._config.topk_method == "group_limited_greedy":
            scores = logits.softmax(dim=-1, dtype=torch.float32)
            topk_weight, topk_idx = self.group_limited_greedy(
                scores,
                self._config.n_group,
                self._config.topk_group,
                self._config.num_experts_per_token,
            )
        else:
            raise ValueError(f"Unknown topk method: {self._config.topk_method}")
        # DeepSeek-V2 doesn't normalize the topk prob.
        topk_weight = topk_weight * self._config.routed_scaling_factor
        return topk_idx, topk_weight

    @torch.no_grad()
    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        topk_idx, topk_weight = self.compute_gate(inputs)
        outs_of_routed_experts = self.routed_experts(inputs, topk_idx, topk_weight)
        outs_of_shared_experts = self.shared_experts(inputs)
        return outs_of_routed_experts + outs_of_shared_experts


class DeepSeekV2AttentionImpl1(nn.Module):
    """Projection into heads -> QKV -> projection back -> residual."""

    def __init__(
        self,
        config: model_specs.DeepSeekV2MLASpec,
        norm_eps: float = 1e-5,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
    ):
        super().__init__()
        self._parallel_ctx = parallel_ctx
        self._device = device
        self._config = config
        self.q_head_dim = config.qk_rope_head_dim + config.qk_nope_head_dim
        self.v_head_dim = config.v_head_dim

        if self._config.q_lora_rank is None:
            self.q_proj = nn.Linear(
                self._config.hidden_dim,
                self._config.num_heads * self.q_head_dim,
                bias=False,
                device=device,
                dtype=dtype,
            )
        else:
            self.q_a_proj = nn.Linear(
                self._config.hidden_dim,
                self._config.q_lora_rank,
                bias=self._config.bias,
                device=device,
                dtype=dtype,
            )
            self.q_a_layernorm = layers.RmsNorm(
                self._config.q_lora_rank, eps=norm_eps, cast_to_fp32=True, device=device
            )
            self.q_b_proj = nn.Linear(
                self._config.q_lora_rank,
                self._config.num_heads * self.q_head_dim,
                bias=False,
                device=device,
                dtype=dtype,
            )

        self.kv_a_proj_with_mqa = nn.Linear(
            self._config.hidden_dim,
            self._config.kv_lora_rank + self._config.qk_rope_head_dim,
            bias=self._config.bias,
            device=device,
            dtype=dtype,
        )
        self.kv_a_layernorm = layers.RmsNorm(
            self._config.kv_lora_rank, eps=norm_eps, cast_to_fp32=True, device=device
        )
        self.kv_b_proj = nn.Linear(
            self._config.kv_lora_rank,
            self._config.num_heads
            * (self.q_head_dim - self._config.qk_rope_head_dim + self.v_head_dim),
            bias=False,
            device=device,
            dtype=dtype,
        )
        self.o_proj = nn.Linear(
            self._config.num_heads * self.v_head_dim,
            self._config.hidden_dim,
            bias=self._config.bias,
            device=device,
            dtype=dtype,
        )

    @torch.inference_mode()
    def forward(
        self, inputs: torch.Tensor, attn: Attention, layer_idx: int
    ) -> torch.Tensor:
        seq, _ = inputs.shape

        if self._config.q_lora_rank is None:
            q_proj = self.q_proj(inputs)
        else:
            q_proj = self.q_b_proj(self.q_a_layernorm(self.q_a_proj(inputs)))
        # [seq_len, num_heads, q_head_dim]
        q_proj = q_proj.view(seq, self._config.num_heads, self.q_head_dim)

        q_nope, q_pe = torch.split(
            q_proj,
            [self._config.qk_nope_head_dim, self._config.qk_rope_head_dim],
            dim=-1,
        )
        # Process KV
        compressed_kv = self.kv_a_proj_with_mqa(inputs)
        compressed_kv, k_pe = torch.split(
            compressed_kv,
            [self._config.kv_lora_rank, self._config.qk_rope_head_dim],
            dim=-1,
        )
        # The shared key for all heads to carry RoPE
        k_pe = k_pe.view(-1, 1, self._config.qk_rope_head_dim)
        kv = self.kv_b_proj(self.kv_a_layernorm(compressed_kv)).view(
            seq,
            self._config.num_heads,
            self._config.qk_nope_head_dim + self.v_head_dim,
        )
        k_nope, v_states = torch.split(
            kv, [self._config.qk_nope_head_dim, self.v_head_dim], dim=-1
        )

        q_states = torch.cat([q_pe, q_nope], dim=-1)
        k_states = k_pe.new_empty(seq, self._config.num_heads, self.q_head_dim)
        # For the lite model, there are 192 dimension and the first 64 dimension will apply the RoPE
        k_states[:, :, : self._config.qk_rope_head_dim] = k_pe
        k_states[:, :, self._config.qk_rope_head_dim :] = k_nope

        v_pad_states = v_states.new_zeros(seq, self._config.num_heads, self.q_head_dim)
        v_pad_states[:, :, : self.v_head_dim] = v_states
        # The current q, k, and v are in the shape of [num_heads, seq_len, ...]
        qkv = torch.cat(
            [
                q_states.reshape(seq, self._config.num_heads, 1, self.q_head_dim),
                k_states.reshape(seq, self._config.num_heads, 1, self.q_head_dim),
                v_pad_states.reshape(seq, self._config.num_heads, 1, self.q_head_dim),
            ],
            dim=2,
        ).view(seq, self._config.num_heads * 3 * self.q_head_dim)
        attn_out = attn(qkv=qkv, layer_idx=layer_idx, parallel_ctx=self._parallel_ctx)
        attn_out = attn_out.view(seq, self._config.num_heads, self.q_head_dim)
        attn_out = attn_out[:, :, : self.v_head_dim].reshape(seq, -1)
        attn_out = self.o_proj(attn_out)
        return attn_out


class DeepSeekV2AttentionImpl2(nn.Module):
    """Projection into heads -> QKV -> projection back."""

    def __init__(
        self,
        config: model_specs.DeepSeekV2MLASpec,
        norm_eps: float = 1e-5,
        dtype: torch.dtype = torch.bfloat16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
    ):
        super().__init__()
        self._device = device
        self._config = config
        self._parallel_ctx = parallel_ctx
        self.q_head_dim = config.qk_rope_head_dim + config.kv_lora_rank
        self.v_head_dim = config.qk_rope_head_dim + config.v_head_dim

        if config.q_lora_rank is None:
            self.q_proj = nn.Linear(
                config.hidden_dim,
                config.num_heads * self.q_head_dim,
                bias=False,
                device=device,
                dtype=dtype,
            )
        else:
            self.q_a_proj = nn.Linear(
                config.hidden_dim,
                config.q_lora_rank,
                bias=config.bias,
                device=device,
                dtype=dtype,
            )
            self.q_a_layernorm = layers.RmsNorm(
                config.q_lora_rank, eps=norm_eps, cast_to_fp32=True, device=device
            )
            self.q_b_proj = nn.Linear(
                config.q_lora_rank,
                config.num_heads * self.q_head_dim,
                bias=False,
                device=device,
                dtype=dtype,
            )

        self.kv_a_proj_with_mqa = nn.Linear(
            config.hidden_dim,
            config.qk_rope_head_dim + config.kv_lora_rank,
            bias=config.bias,
            device=device,
            dtype=dtype,
        )
        self.kv_a_layernorm = layers.RmsNorm(
            config.kv_lora_rank, eps=norm_eps, cast_to_fp32=True
        )
        self.v_proj = nn.Parameter(
            torch.zeros(
                config.num_heads,
                config.kv_lora_rank,
                config.v_head_dim,
                dtype=dtype,
                device=device,
            ),
            requires_grad=False,
        )
        self.o_proj = nn.Linear(
            config.num_heads * config.v_head_dim,
            config.hidden_dim,
            bias=config.bias,
            device=device,
            dtype=dtype,
        )

    def forward(
        self, inputs: torch.Tensor, attn: Attention, layer_idx: int
    ) -> torch.Tensor:
        config = self._config
        seq_len = inputs.size(0)

        if config.q_lora_rank is None:
            q = self.q_proj(inputs)
        else:
            q = self.q_b_proj(self.q_a_layernorm(self.q_a_proj(inputs)))

        # [seq_len, 1, num_heads, q_head_dim = qk_rope_head_dim + kv_lora_rank]
        q = q.view(seq_len, 1, config.num_heads, self.q_head_dim)

        # [seq_len, 1, 1, qk_rope_head_dim + kv_lora_rank]
        compressed_kv = self.kv_a_proj_with_mqa(inputs)
        compressed_kv[:, config.qk_rope_head_dim :] = self.kv_a_layernorm(
            compressed_kv[:, config.qk_rope_head_dim :]
        )
        compressed_kv = compressed_kv.view(
            seq_len, 1, 1, config.qk_rope_head_dim + config.kv_lora_rank
        )

        # TODO(markus): We are duplicating compressed_kv here. This will lead to
        # larger KV caches and some additional memory bandwidth usage. FLOPs are
        # not affected. We could avoid this potentially by sharing some of the
        # underlying memory in the KV cache of MultiCacheAttentionImplementation.
        # TODO(markus): Call MultiCacheAttentionImplementation.attend directly to
        # avoid copying these tensors for the concatenation, which is undone in
        # MultiCacheAttentionImplementation.__call__.
        qkv = torch.cat([q, compressed_kv, compressed_kv], dim=2).view(
            seq_len,
            (config.num_heads + 2) * (config.qk_rope_head_dim + config.kv_lora_rank),
        )

        out = attn(qkv, layer_idx=layer_idx, parallel_ctx=self._parallel_ctx)
        out = out.view(
            seq_len, config.num_heads, config.qk_rope_head_dim + config.kv_lora_rank
        )
        out = out[:, :, config.qk_rope_head_dim :]
        # TODO(markus): This einsum might be very inefficient, as the memory accesses
        # are not dense in the last dimension. We could to transpose the weights
        # of self.v_proj.
        out = torch.einsum(
            "sqd,qdh->sqh",
            out,  # [seq_len, num_heads,  kv_lora_rank]
            self.v_proj,  # [num_heads, kv_lora_rank, v_head_dim]
        )
        assert out.shape == (
            seq_len,
            config.num_heads,
            config.v_head_dim,
        )
        out = out.reshape(
            seq_len, config.num_heads * config.v_head_dim
        )  # TODO(markus): figure out why a view doesn't work here.
        out = self.o_proj(out)
        return out


class DeepSeekV2TransformerBlock(nn.Module):
    """Computes: input -> norm -> attn -> residual -> norm -> swiglu -> residual."""

    def __init__(
        self,
        attn_config: model_specs.DeepSeekV2MLASpec,
        mlp_or_moe_config: model_specs.MLPSpec | model_specs.MoESpec,
        norm_eps: float = 1e-5,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
    ):
        super().__init__()
        self._device = device
        self._num_processes = num_processes
        self._parallel_ctx = parallel_ctx
        assert parallel_ctx.cfg.num_processes == num_processes
        assert parallel_ctx.process_idx == process_idx
        assert parallel_ctx.sp_size == 1
        assert attn_config.hidden_dim == mlp_or_moe_config.hidden_dim
        if attn_config.use_mqa_for_mla:
            self.attn = DeepSeekV2AttentionImpl2(
                attn_config,
                norm_eps=norm_eps,
                dtype=dtype,
                device=device,
                parallel_ctx=parallel_ctx,
            )
        else:
            self.attn = DeepSeekV2AttentionImpl1(
                attn_config,
                norm_eps=norm_eps,
                dtype=dtype,
                device=device,
                parallel_ctx=parallel_ctx,
            )

        if isinstance(mlp_or_moe_config, model_specs.MLPSpec):
            self.ffn = MLP(
                hidden_size=mlp_or_moe_config.hidden_dim,
                intermediate_size=mlp_or_moe_config.intermediate_size,
                dtype=dtype,
                device=device,
                process_idx=process_idx,
                num_processes=num_processes,
            )
        else:
            self.ffn = DeepSeekV2MoE(
                config=mlp_or_moe_config,
                dtype=dtype,
                device=device,
                process_idx=process_idx,
                num_processes=num_processes,
            )

        self.attn_norm = layers.RmsNorm(
            inp_dim=attn_config.hidden_dim,
            eps=norm_eps,
            cast_to_fp32=True,
            dtype=dtype,
            device=device,
        )

        self.ffn_norm = layers.RmsNorm(
            inp_dim=attn_config.hidden_dim,
            eps=norm_eps,
            cast_to_fp32=True,
            dtype=dtype,
            device=device,
        )

    @torch.inference_mode()
    def forward(
        self,
        inputs: torch.Tensor,
        attn: Attention,
        layer_idx: int,
    ) -> torch.Tensor:
        with torch_utils.ProfilingContext(f"layer_{layer_idx}"):
            x = inputs
            with torch_utils.ProfilingContext(f"attention_{layer_idx}"):
                residual = x
                x = self.attn_norm(x)
                x = self.attn(x, attn=attn, layer_idx=layer_idx)
                x.add_(residual)

            with torch_utils.ProfilingContext(f"ffn_{layer_idx}"):
                residual = x
                x = self.ffn_norm(x)
                # Both MLP and MoE support tensor parallelism, so the result should
                # always be all-reduced.
                x = self.ffn(x)
                if self._num_processes > 1:
                    dist.all_reduce(x, op=dist.ReduceOp.SUM)
                x.add_(residual)
        return x


class DeepSeekV2(nn.Module):
    """The DeepSeek-V2 model: https://github.com/deepseek-ai/DeepSeek-Coder-V2."""

    def __init__(
        self,
        ms: model_specs.DeepSeekV2ModelSpec,
        dtype: torch.dtype = torch.bfloat16,
        device: Device = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
        auto_capture_graphs: bool = False,
        batch_sizes: Sequence[int] | None = None,
    ):
        super().__init__()
        self._dtype = dtype
        self._device = device
        self._process_idx = process_idx
        self._num_processes = num_processes
        self._parallel_ctx = ParallelContext(
            ParallelConfig.from_legacy_config(num_processes, False), process_idx
        )

        self.embs = WordEmbeddings(
            vocab_size=ms.vocab_size,
            emb_dim=ms.emb_dim,
            dtype=dtype,
            device=device,
        )

        if auto_capture_graphs:
            if batch_sizes is None:
                raise ValueError(
                    "batch_sizes must be specified if auto_capture_graphs is True."
                )
            self._apply_transformer_layers = (
                cuda_graphs_attention.GraphedEmbForwardPass(
                    step_fn=self._emb_step,
                    process_idx=process_idx,
                    num_processes=num_processes,
                    batch_sizes=batch_sizes,
                )
            )
            assert False, "CUDA capture is not working yet."
        else:
            self._apply_transformer_layers = self._emb_step

        self.layers = nn.ModuleList()
        for ilayer in range(ms.num_layers):
            self.layers.append(
                DeepSeekV2TransformerBlock(
                    attn_config=ms.attention,
                    mlp_or_moe_config=ms.mlp if ilayer == 0 else ms.moe,
                    norm_eps=ms.norm_eps,
                    dtype=dtype,
                    device=device,
                    process_idx=process_idx,
                    num_processes=num_processes,
                    parallel_ctx=self._parallel_ctx,
                )
            )

        self.final_layer_norm = nn.LayerNorm(
            normalized_shape=ms.emb_dim,
            eps=ms.norm_eps,
            bias=False,
            dtype=dtype,
            device=device,
        )

        self.score = nn.Linear(
            in_features=ms.emb_dim,
            out_features=ms.vocab_size,
            bias=False,
            device=device,
            dtype=dtype,
        )

    @property
    def dtype(self) -> torch.dtype:
        return self._dtype

    def _emb_step(self, emb: torch.Tensor, attn: Attention) -> torch.Tensor:
        for layer_idx, layer in enumerate(self.layers):
            emb = layer(emb, attn=attn, layer_idx=layer_idx)
        return emb

    @torch.no_grad()
    def forward(self, tokens: Sequence[int], attn: Attention) -> fwd.ModelOutput:
        tokens_on_device = torch.tensor(tokens, dtype=torch.int32, device=self._device)

        attn.register_tokens_get_positions(
            tokens_on_device, process_idx=self._process_idx
        )
        x = self.embs(tokens_on_device)
        x = self._apply_transformer_layers(x, attn)
        x = self.final_layer_norm(x)
        x = self.score(x)
        return fwd_torch.TorchLogits2D(x)


class DeepSeekCoderV2AttentionFactory(fwd.AttentionFactory):
    """Creates attention caches for the DeepSeekCoder-V2 models."""

    def __init__(
        self,
        ms: model_specs.DeepSeekV2ModelSpec,
        num_processes: int = 1,
        dtype: torch.dtype = torch.bfloat16,
        max_requests_in_round: int | None = None,
        small_request_max_seqlen: int | None = None,
    ):
        """The AttentionFactory for DeepSeekCoder-V2 models.

        Args:
            ms: the specific model_specs.DeepSeekCoderV2ModelSpec or similar model spec.
            num_processes: for tensor parallelism.
            dtype: the dtype of the `qkv` tensor that will be passed to Attention
                objects created by this factory. we also use this `dtype` to construct
                this object's KV-caches.
        """
        self._ms = ms
        self._parallel_config = ParallelConfig.from_legacy_config(num_processes, False)
        self._dtype = dtype
        self._max_requests_in_round = max_requests_in_round
        self._small_request_max_seqlen = small_request_max_seqlen

    def create_cache_pool(
        self,
        max_length: int,
        num_attention_caches: int,
    ) -> MultiCacheAttention:
        rotary_config = self._ms.rope

        if self._ms.attention.use_mqa_for_mla:
            head_dim = (
                self._ms.attention.kv_lora_rank + self._ms.attention.qk_rope_head_dim
            )
            mc_attn = MultiCacheAttentionImplementation(
                num_caches=num_attention_caches,
                num_layers=self._ms.num_layers,
                num_heads=1,
                queries_per_head=self._ms.attention.num_heads,
                head_dim=head_dim,
                max_len=max_length,
                use_mqa_for_mla=True,
                pre_attention_kernel_fusion=False,
                rotary_config=rotary_config,
                parallel_config=self._parallel_config,
                softmax_scale=(
                    self._ms.attention.qk_rope_head_dim
                    + self._ms.attention.qk_nope_head_dim
                )
                ** -0.5,
                dtype=self._dtype,
            )
        else:
            head_dim = (
                self._ms.attention.qk_rope_head_dim
                + self._ms.attention.qk_nope_head_dim
            )
            mc_attn = MultiCacheAttentionImplementation(
                num_caches=num_attention_caches,
                num_layers=self._ms.num_layers,
                num_heads=self._ms.attention.num_heads,
                head_dim=head_dim,
                max_len=max_length,
                use_mqa_for_mla=False,
                rotary_config=rotary_config,
                parallel_config=self._parallel_config,
                dtype=self._dtype,
                max_requests_in_round=self._max_requests_in_round,
                small_request_max_seqlen=self._small_request_max_seqlen,
            )
        return mc_attn


def shard_args_by_name(
    process_idx: int,
    num_processes: int,
) -> sharding.ShardLoadArgsMap:
    # Shard all MLP (gate|up|down) projection weights
    # This includes layer(s) implemented as single dense ffn, and the
    # routed/shared expert ffns from MoE layers.
    # In other words, we want to match all of:
    # layers.0.ffn.down_proj.weight
    # layers.1.ffn.shared_experts.down_proj.weight
    # layers.1.ffn.experts.0.down_proj.weight
    ffn_match = (
        r"layers\.[0-9]+\.ffn(\.shared_experts|\.routed_experts\.experts\.[0-9]+)?"
    )
    return {
        re.compile(rf"^{ffn_match}\.(gate|up)_proj.weight$"): sharding.ShardLoadArgs(
            shard_idxs=(process_idx,), shard_count=num_processes, split_dim=0
        ),
        re.compile(rf"^{ffn_match}\.down_proj.weight$"): sharding.ShardLoadArgs(
            shard_idxs=(process_idx,), shard_count=num_processes, split_dim=1
        ),
    }


def _generate_step_fn(
    ms: model_specs.DeepSeekV2ModelSpec,
    dtype: torch.dtype = torch.bfloat16,
    batch_sizes: Sequence[int] | None = None,
    process_idx: int = 0,
    num_processes: int = 1,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
) -> fwd.ForwardStepFn:
    logging.info(
        "Loading a DeepSeek-V2 model %s onto %d processes.", ms.name, num_processes
    )
    logging.debug(
        "ms: %s, max_batch_size: %s, process_idx: %s, num_processes: %s",
        ms,
        batch_sizes,
        process_idx,
        num_processes,
    )

    if torch.cuda.is_available():
        device = f"cuda:{process_idx}"
    else:
        device = "cpu"
        logging.warning("CUDA is not available, using CPU instead.")
        assert num_processes == 1 and process_idx == 0
        assert not auto_capture_graphs, "CPU does not support auto_capture_graphs."

    model = DeepSeekV2(
        ms=ms,
        dtype=dtype,
        device=device,
        process_idx=process_idx,
        num_processes=num_processes,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
    )
    if load_checkpoint_weights:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        shard_args = shard_args_by_name(
            process_idx=process_idx, num_processes=num_processes
        )

        # For now, load_weights loads all weights to CPU memory, then moves to GPU memory,
        # so run in batches to limit peak CPU memory usage
        # It would be nice to push incremental loading and verification of model state dict
        # down into save_load, as each model ends up re-implementing this pattern.
        num_layers_per_load = 15
        layer_ranges = [None] + [
            range(base, min(base + num_layers_per_load, ms.num_layers))
            for base in range(0, ms.num_layers, num_layers_per_load)
        ]

        loaded_keys = set()
        for layer_range in layer_ranges:
            # for batch_idx, batch in enumerate(batches):
            if layer_range is None:
                # Special handling for layers outside of any layer
                batch_regexes = (r"^(?!layers\.)",)
            else:
                batch_regexes = tuple(rf"^layers\.{i}\." for i in layer_range)
            weights = save_load.load_weights(
                ms.checkpoint_path,
                target_sha256=ms.checkpoint_sha256,
                require_patterns=batch_regexes,
                shard_load_args=shard_args,
                device=device,
            )
            # If we're using fused MoE, we need to re-map the weight names to the stacked ones
            if layer_range is not None and ms.moe.implementation == "fused":
                _update_moe_weights_for_fused_layer(
                    weights, list(layer_range), ms.moe.n_routed_experts
                )

            if layer_range is not None and ms.attention.use_mqa_for_mla:
                update_weights_for_mqa_for_mla(weights, list(layer_range), ms.attention)
            loaded_keys.update(weights.keys())
            model.load_state_dict(weights, strict=False)
            del weights
            torch.cuda.empty_cache()
        # Because we loaded state with strict=False, check it now
        model_keys = set(model.state_dict().keys())
        if loaded_keys != model_keys:
            raise RuntimeError(
                f"Unexpected keys: {loaded_keys - model_keys}. "
                f"Missing keys: {model_keys - loaded_keys}."
            )
    return model


def _update_moe_weights_for_fused_layer(
    weights: dict[str, Tensor], layer_idxs: Sequence[int], num_experts: int
) -> None:
    """Update the state_dict to stack together all experts as well as cat gate and up proj."""
    for ilayer in layer_idxs:
        # NOTE: layer0 is _not_ an MoE layer. It is easier to handle this here than to filter out
        # exactly layer 0 at the call site.
        if ilayer == 0:
            continue
        all_gate_proj = [
            weights.pop(
                f"layers.{ilayer}.ffn.routed_experts.experts.{iexpert}.gate_proj.weight"
            )
            for iexpert in range(num_experts)
        ]
        all_up_proj = [
            weights.pop(
                f"layers.{ilayer}.ffn.routed_experts.experts.{iexpert}.up_proj.weight"
            )
            for iexpert in range(num_experts)
        ]
        all_down_proj = [
            weights.pop(
                f"layers.{ilayer}.ffn.routed_experts.experts.{iexpert}.down_proj.weight"
            )
            for iexpert in range(num_experts)
        ]
        all_fused_gate_up = [
            torch.cat((gate, up), dim=0) for gate, up in zip(all_gate_proj, all_up_proj)
        ]
        weights[f"layers.{ilayer}.ffn.routed_experts.experts_gate_up_proj"] = (
            torch.stack(all_fused_gate_up, dim=0)
        )
        weights[f"layers.{ilayer}.ffn.routed_experts.experts_down_proj"] = torch.stack(
            all_down_proj, dim=0
        )


def update_weights_for_mqa_for_mla(
    weights: dict[str, Tensor],
    layer_idxs: Sequence[int],
    attention: model_specs.DeepSeekV2MLASpec,
) -> None:
    """Update the state_dict to combine the RoPE, query projection, and KV decompression steps."""
    for ilayer in layer_idxs:
        raw_q_proj = weights.pop(f"layers.{ilayer}.attn.q_proj.weight")
        raw_kv_b_proj = weights.pop(f"layers.{ilayer}.attn.kv_b_proj.weight")

        # RoPE portion is unchanged, just grab the RoPE portion of the query projection
        q_rope_weights = raw_q_proj.view(
            attention.num_heads,
            attention.qk_nope_head_dim + attention.qk_rope_head_dim,
            attention.hidden_dim,
        )[:, attention.qk_nope_head_dim :, :]
        assert q_rope_weights.shape == (
            attention.num_heads,
            attention.qk_rope_head_dim,
            attention.hidden_dim,
        )

        # Non-RoPE portion has to be KV-decompressed to generate weights that can be naively concatenated with RoPE within FlashAttention
        q_nope_weights = torch.bmm(
            raw_kv_b_proj.view(
                attention.num_heads,
                attention.qk_nope_head_dim + attention.v_head_dim,
                attention.kv_lora_rank,
            )[:, : attention.qk_nope_head_dim].transpose(1, 2),
            raw_q_proj.view(
                attention.num_heads,
                attention.qk_nope_head_dim + attention.qk_rope_head_dim,
                attention.hidden_dim,
            )[:, : attention.qk_nope_head_dim, :],
        )
        assert q_nope_weights.shape == (
            attention.num_heads,
            attention.kv_lora_rank,
            attention.hidden_dim,
        )

        weights[f"layers.{ilayer}.attn.q_proj.weight"] = torch.reshape(
            torch.cat([q_rope_weights, q_nope_weights], dim=1),
            (
                attention.num_heads
                * (attention.qk_rope_head_dim + attention.kv_lora_rank),
                attention.hidden_dim,
            ),
        )

        # V projection is just the KV-decompression step now
        weights[f"layers.{ilayer}.attn.v_proj"] = (
            raw_kv_b_proj.view(
                attention.num_heads,
                attention.qk_nope_head_dim + attention.v_head_dim,
                attention.kv_lora_rank,
            )[:, attention.qk_nope_head_dim :, :]
            .transpose(1, 2)
            .contiguous()
        )

        # # Reverse RoPE and non-RoPE in kv_a_proj
        raw_kv_a_proj_with_mqa = weights.pop(
            f"layers.{ilayer}.attn.kv_a_proj_with_mqa.weight"
        )

        kv_a_nope, kv_a_rope = torch.split(
            raw_kv_a_proj_with_mqa,
            [attention.kv_lora_rank, attention.qk_rope_head_dim],
            dim=0,
        )
        weights[f"layers.{ilayer}.attn.kv_a_proj_with_mqa.weight"] = torch.cat(
            [kv_a_rope, kv_a_nope], dim=0
        ).contiguous()


def generate_step_fn(
    ms: model_specs.DeepSeekV2ModelSpec,
    dtype: torch.dtype = torch.bfloat16,
    num_processes: int = 1,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        ms: Model spec.
        dtype: the dtype for both computations and weights of the created step_fn.
            note that it is the user's responsibility to ensure this matches the dtype
            of their attention cache. Otherwise, the resulting step_fn fails.
        num_processes: Number of GPUs to use.
        load_checkpoint_weights: load the weights into the step_fn. should be `True`
            by default. only set to `False` for testing, and if so, make sure you
            know what you are doing.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
    """
    if num_processes <= 0:
        raise ValueError(f"{num_processes=} is invalid.")

    if num_processes == 1:
        return _generate_step_fn(
            ms=ms,
            dtype=dtype,
            batch_sizes=batch_sizes,
            load_checkpoint_weights=load_checkpoint_weights,
            auto_capture_graphs=auto_capture_graphs,
        )
    else:
        mp = ParallelForwardRunner(num_processes=num_processes)
        return mp.initialize(
            [_generate_step_fn],
            [
                {
                    "ms": ms,
                    "dtype": dtype,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                }
            ],
        )
