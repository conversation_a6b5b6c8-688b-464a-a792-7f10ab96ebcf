#!/bin/sh -e

if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY not set (run with bazel)" >&2
	exit 1
fi

OUTDIR="$BUILD_WORKSPACE_DIRECTORY/base/fastforward/deepseek_v2"

# There is some trickiness here:
# - First, try to import torch -- if we don't have it, bail out. (Eg, pre-commit)
# - The compiled shared object file has paths to torch libraries _inside_ the bazel sandbox.
# - We use the `patchelf` utility to fix this to point to the outer environment's path.
# - The `.so` file isn't writeable, so we fix that temporarily.
if ! python3 -c "import torch"; then
	echo "No torch available, skipping fastforward install."
	exit 0
fi
PYTORCH_LIB_DIR=$(python3 -c "import os, torch; print(os.path.dirname(torch.__file__))")/lib
for MODULE in vllm_compiled_ops.so; do
	LIBFILE=base/fastforward/deepseek_v2/$MODULE
	chmod +w $LIBFILE
	patchelf --set-rpath $PYTORCH_LIB_DIR $LIBFILE
	chmod -w $LIBFILE
	cp --remove-destination $LIBFILE "$OUTDIR/$MODULE"
done
