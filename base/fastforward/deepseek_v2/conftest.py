"""conftest.py is a special file that pytest will automatically load."""

import pytest
from torch._subclasses.fake_tensor import FakeTensorMode

from base.fastforward import fwd
from base.fastforward.deepseek_v2 import fwd_dsv2, model_specs


@pytest.fixture(scope="module")
def deepseek_coder_v2_lite_bf16_fake_fixture():
    """Load a fake deepseek model to use for basic tests."""
    ms = model_specs.get_model_spec(
        model_name="deepseek-coder-v2-lite",
        checkpoint_path="",
        checkpoint_sha256=None,
    )
    fake_mode = FakeTensorMode()
    with fake_mode:
        return fwd_dsv2.DeepSeekV2(ms=ms)


def _deepseek_coder_v2_nano_model_spec():
    """Model spec for a model that is just the first 3 layers of the Lite model."""
    return model_specs.get_model_spec(
        model_name="deepseek-coder-v2-nano",
        checkpoint_path="/mnt/efs/augment/checkpoints/deepseek-v2/DeepSeek-Coder-V2-Nano-Instruct-ffw-20240812",
        checkpoint_sha256="9ea7568c2098c518cda526ee916fc45063e3f587a6f157c0f76d9f69ffb2d994",
    )


@pytest.fixture(scope="module", params=["reference", "fused"])
def deepseek_coder_v2_nano_bf16_fixture(
    request,
) -> tuple[model_specs.DeepSeekV2ModelSpec, fwd.ForwardStepFn, fwd.AttentionFactory]:
    """Load the weights of a DeepSeek-V2-Nano model to use for all tests."""
    ms = _deepseek_coder_v2_nano_model_spec()
    # Update the MoE implementation based on the fixture param
    ms.moe.implementation = request.param
    step_fn = fwd_dsv2.generate_step_fn(
        ms, load_checkpoint_weights=True, auto_capture_graphs=False, batch_sizes=None
    )
    attn_factory = fwd_dsv2.DeepSeekCoderV2AttentionFactory(ms=ms)
    return ms, step_fn, attn_factory


@pytest.fixture(scope="module")
def deepseek_coder_v2_mqa_for_mla_nano_bf16_fixture() -> (
    tuple[model_specs.DeepSeekV2ModelSpec, fwd.ForwardStepFn, fwd.AttentionFactory]
):
    """Load the weights of a DeepSeek-V2-Nano model to use for all tests."""
    ms = _deepseek_coder_v2_nano_model_spec()
    ms.attention.use_mqa_for_mla = True
    step_fn = fwd_dsv2.generate_step_fn(
        ms, load_checkpoint_weights=True, auto_capture_graphs=False, batch_sizes=None
    )
    attn_factory = fwd_dsv2.DeepSeekCoderV2AttentionFactory(ms=ms)
    return ms, step_fn, attn_factory


@pytest.fixture(scope="module")
def deepseek_coder_v2_nano_bf16_multigpu_fixture() -> (
    tuple[model_specs.DeepSeekV2ModelSpec, fwd.ForwardStepFn, fwd.AttentionFactory]
):
    """Load the weights of a dbrx-nano model to use for all tests."""
    ms = _deepseek_coder_v2_nano_model_spec()
    step_fn = fwd_dsv2.generate_step_fn(
        ms,
        load_checkpoint_weights=True,
        auto_capture_graphs=False,
        batch_sizes=None,
        num_processes=2,
    )
    attn_factory = fwd_dsv2.DeepSeekCoderV2AttentionFactory(ms=ms, num_processes=2)
    return ms, step_fn, attn_factory
