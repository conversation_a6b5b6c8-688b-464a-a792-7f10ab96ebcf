#include <torch/extension.h>

namespace vllm_compiled_ops {

void topk_softmax_cuda(torch::Tensor& topk_weights, torch::Tensor& topk_indices,
                       torch::Tensor& token_expert_indices, torch::Tensor& gating_output);

void topk_softmax(torch::Tensor& topk_weights, torch::Tensor& topk_indices,
                  torch::Tensor& token_expert_indices, torch::Tensor& gating_output) {
    topk_softmax_cuda(topk_weights, topk_indices, token_expert_indices, gating_output);
}

void moe_align_block_size_cuda(torch::Tensor topk_ids, int64_t num_experts, int64_t block_size,
                               torch::Tensor sorted_token_ids, torch::Tensor experts_ids,
                               torch::Tensor num_tokens_post_pad);

void moe_align_block_size(torch::Tensor topk_ids, int64_t num_experts, int64_t block_size,
                          torch::Tensor sorted_token_ids, torch::Tensor experts_ids,
                          torch::Tensor num_tokens_post_pad) {
    moe_align_block_size_cuda(topk_ids, num_experts, block_size, sorted_token_ids, experts_ids,
                              num_tokens_post_pad);
}

}  // namespace vllm_compiled_ops

PYBIND11_MODULE(vllm_compiled_ops, m) {
    m.def("topk_softmax", &vllm_compiled_ops::topk_softmax, "Topk softmax.");
    m.def("moe_align_block_size", &vllm_compiled_ops::moe_align_block_size,
          "Moe align block size.");
}
