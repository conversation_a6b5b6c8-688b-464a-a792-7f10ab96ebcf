"""Unit tests for ffw_dsv2 on multiple GPUs"""

from base.fastforward import fwd_model_test_utils


def test_compare_single_to_multi_gpu(
    deepseek_coder_v2_nano_bf16_fixture, deepseek_coder_v2_nano_bf16_multigpu_fixture
):
    """Ensure that the model generates approximately the same logits running single vs multi-gpu"""
    ms1, step_fn1, attn_factory1 = deepseek_coder_v2_nano_bf16_fixture
    ms2, step_fn2, attn_factory2 = deepseek_coder_v2_nano_bf16_multigpu_fixture

    # These tokens represent:
    # <｜begin▁of▁sentence｜>User: write a quick sort algorithm in python.
    #
    # Assistant:
    # fmt: off
    inp = [100000, 5726, 25, 3708, 245, 3399, 3734, 6712, 279, 9934, 13, 185, 185, 77398, 25]
    # fmt: on

    fwd_model_test_utils.check_logits_are_close(
        step_fn1,
        step_fn2,
        attn_factory1,
        attn_factory2,
        inp,
        kv_len=128,
    )
