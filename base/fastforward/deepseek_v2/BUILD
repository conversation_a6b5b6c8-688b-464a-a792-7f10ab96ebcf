load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:pytorch.bzl", "pytorch_cpp_extension")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    deps = [
        ":fwd_dsv2",
        ":model_specs",
        "//base/fastforward:fwd",
        requirement("pytest"),
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_dsv2",
    srcs = ["fwd_dsv2.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":fused_moe",
        ":model_specs",
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers",
        "//base/fastforward:parallel_fwd",
        "//base/fastforward/checkpoints:save_load",
        requirement("torch"),
        requirement("numpy"),
    ],
)

py_library(
    name = "model_specs",
    srcs = ["model_specs.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        requirement("torch"),
        requirement("numpy"),
    ],
)

py_library(
    name = "fused_moe",
    srcs = ["fused_moe.py"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        ":vllm_ops",
        "//base/fastforward:fp8",
        "//base/fastforward:layers",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fused_moe_test",
    srcs = [
        "fused_moe_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":fused_moe",
        ":fwd_dsv2",
        requirement("torch"),
    ],
)

py_library(
    name = "vllm_ops",
    srcs = ["vllm_ops.py"],
    data = [":vllm_compiled_ops.so"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        requirement("torch"),
    ],
)

pytorch_cpp_extension(
    name = "vllm_compiled_ops",
    srcs = [
        "vllm_compiled_ops.cpp",
    ],
    cuda_srcs = [
        "moe_kernels.cu",
        "topk_softmax_kernels.cu",
    ],
)

pytest_test(
    name = "fwd_dsv2_test",
    size = "medium",
    srcs = [
        "fwd_dsv2_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        "//base/fastforward/deepseek_v2:conftest",
        requirement("flash-attn"),
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_dsv2_multigpu_test",
    size = "medium",
    srcs = [
        "fwd_dsv2_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward/deepseek_v2:conftest",
        requirement("torch"),
    ],
)

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    data = [
        ":vllm_compiled_ops.so",
    ],
    visibility = ["//base:__subpackages__"],
)
