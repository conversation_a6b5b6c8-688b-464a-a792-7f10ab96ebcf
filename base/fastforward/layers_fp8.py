"""Implementation of key Transformer layers with FP8 matmuls."""

from typing import Callable, <PERSON>ple

import torch
import torch.distributed as dist
import torch.nn as nn
import torch.nn.functional as F

from base.fastforward import all_reduce, cached_attention
from base.fastforward.fp8 import (
    FP8LayerNorm,
    FP8Linear,
    FP8RmsNorm,
    FP8Types,
    tex,
    texcpp,
)
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.torch_utils import ProfilingContext

Device = torch.device | str
SplitHeadModes = cached_attention.SplitHeadModes


class QKV(nn.Module):
    """Implementation of the QKV pre-attention calculation."""

    def __init__(
        self,
        num_heads: int,
        head_dim: int,
        emb_dim: int,
        layernorm_eps: float,
        device: torch.device | str = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
    ):
        super().__init__()
        self.num_heads_q = num_heads
        self.head_dim = head_dim
        self.emb_dim = emb_dim

        self.input_layernorm = FP8LayerNorm(
            emb_dim,
            eps=layernorm_eps,
            dtype=torch.float16,
            device=device,
        )
        self._use_tensor_parallelism = num_processes > 1
        if self._use_tensor_parallelism:
            self.num_heads_q, _num_heads_kv = cached_attention.split_heads(
                num_heads_q=num_heads,
                num_heads_kv=1,
                parallel_config=ParallelConfig.from_legacy_config(num_processes, False),
                split_head_mode=cached_attention.SplitHeadModes.Q_PER_HEADS,
            )
            assert (
                _num_heads_kv == 1
            ), f"MultiQueryAttention expected, got KV {_num_heads_kv=}"
        self.linear_qkv = FP8Linear(
            in_features=emb_dim,
            # With multi-query attention, we use `num_heads` query heads and 1 head each
            # for key and value.
            out_features=((self.num_heads_q + 2) * head_dim),
            bias=True,
            device=device,
        )

    def forward(self, emb: torch.Tensor) -> torch.Tensor:
        # NOTE(arun): We output in FP8 to save a cast in the qkv Linear layer.
        with ProfilingContext("qkv"):
            emb_ln1 = self.input_layernorm.forward(
                emb, output_fp8_meta=self.linear_qkv.input_fp8_meta
            )
            out = self.linear_qkv(emb_ln1)
        assert len(out.shape) == 2
        return out


class AttnOutMLP(nn.Module):
    """Attention output projection, attention residual, layer norm, and MLP."""

    def __init__(
        self,
        emb_dim: int,
        layernorm_eps: float,
        device: torch.device | str = "cuda",
        activation_fn: Callable[[torch.Tensor], torch.Tensor] = F.gelu,
        process_idx: int = 0,
        num_processes: int = 1,
    ):
        assert activation_fn == F.gelu, "FP8 only supports gelu."
        assert num_processes == 1, "FP8 only supports single-device."

        super().__init__()
        self.emb_dim = emb_dim

        self.linear_hto4h = FP8Linear(emb_dim, 4 * emb_dim, device=device)
        self.linear_4htoh = FP8Linear(4 * emb_dim, emb_dim, device=device)
        self.linear_attention_dense = FP8Linear(emb_dim, emb_dim, device=device)
        self.post_attention_layernorm = FP8LayerNorm(
            emb_dim,
            eps=layernorm_eps,
            device=device,
            dtype=torch.float16,
        )

    def forward(self, emb: torch.Tensor, attn: torch.Tensor) -> torch.Tensor:
        with ProfilingContext("mlp"):
            emb = self.linear_attention_dense.forward(attn, out=emb, accumulate=True)
            emb_ln2 = self.post_attention_layernorm(
                emb, output_fp8_meta=self.linear_hto4h.input_fp8_meta
            )
            intermediate = self.linear_hto4h.forward(
                emb_ln2,
                with_gelu=True,
                output_fp8_meta=self.linear_4htoh.input_fp8_meta,
            )
            emb = self.linear_4htoh(intermediate, out=emb, accumulate=True)
        return emb


class CrossLayer(nn.Module):
    """Everything between two attention calls; spans two layers."""

    def __init__(
        self,
        num_heads: int,
        head_dim: int,
        emb_dim: int,
        layernorm_eps: float,
        device: torch.device | str = "cuda",
        process_idx: int = 0,
        num_processes: int = 1,
    ):
        """Constructs a new MLP object.

        Args:
            num_heads: The number of heads in the attention layer.
            head_dim: The dimension of each head.
            emb_dim: The dimension of the input and output embeddings.
            device: The device to use.
            process_idx: The process index.
            num_processes: The number of processes.
        """
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = head_dim
        self.emb_dim = emb_dim
        self.process_idx = process_idx
        self.num_processes = num_processes

        self.mlp = AttnOutMLP(
            emb_dim,
            layernorm_eps=layernorm_eps,
            device=device,
            process_idx=process_idx,
            num_processes=num_processes,
        )
        self.qkv = QKV(
            num_heads,
            head_dim,
            emb_dim,
            layernorm_eps=layernorm_eps,
            device=device,
            process_idx=process_idx,
            num_processes=num_processes,
        )

    def forward(
        self, emb: torch.Tensor, attn: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        with ProfilingContext("cross"):
            emb = self.mlp(emb, attn)
            qkv_out = self.qkv(emb)
        return emb, qkv_out


# This is a helper function to combine an fp8 matmul followed by an all_reduce.
# Logically, it performs the following:
#     x = fp8_linear(x)
#     if needs_tp_all_reduce:
#         x = all_reduce(x)
#     if residual is not None:
#         x = x + residual
#     return x
#
# This is factored into a helper function because of the trickiness of the interaction between
# tensor parallelism and the residual add. There are three separate cases to consider here:
# (1) No tensor parallelism
# (2a) Tensor parallel with NCCL
# (2b) Tensor parallel with fastforward custom comms
# In case (1), we always fuse the residual add into the matmul. In both (2a) and (2b), we
# delay the residual add until _after_ the all_reduce to avoid double-adding. In case (2a),
# we let the matmul allocate its out output tensor. In case (2b), we use the pre-allocated
# tensor from the all_reduce_kit as the output.
# NOTE also: we need to pass in `needs_tp_all_reduce` distinct from the `parallel_context`, since
# whether to perform an all_reduce depends on more than just tp_size > 1. In particular, for the
# post-attention matmul, we _don't_ perform an all_reduce if split_heads_mode is NO_SPLIT.
def _fp8_matmul_with_all_reduce(
    fp8_linear: FP8Linear,
    x: torch.Tensor,
    residual: torch.Tensor | None,
    needs_tp_all_reduce: bool,
    parallel_context: ParallelContext,
    all_reduce_kit: all_reduce.AllReduceKit | None,
    output_shape: torch.Size,
):
    # Accumulate onto the residual directly only when:
    # (a) it is present
    # (b) we don't need to delay the residual add until post-all_reduce
    accumulate = not needs_tp_all_reduce and residual is not None
    if not needs_tp_all_reduce:  # Case (1)
        # Note: we don't need any kind of `residual = None` setting here specifically because we
        # are guaranteed to be running w/o tensor parallelism, so every GPU is tp_rank 0.
        matmul_out = residual
        output_fp8_meta = None
    elif all_reduce_kit is not None:  # Case (2b)
        if all_reduce_kit.input_is_fp8:
            matmul_out = all_reduce_kit.get_input_tensor(output_shape, torch.uint8)
            # TODO: fix this to use the `output_scale` once we have it
            output_fp8_meta = fp8_linear.input_fp8_meta
        else:
            matmul_out = all_reduce_kit.get_input_tensor(output_shape, fp8_linear.dtype)
            output_fp8_meta = None
    else:  # Case (2a)
        matmul_out = None
        output_fp8_meta = None

    x = fp8_linear(
        x,
        out=matmul_out,
        output_fp8_meta=output_fp8_meta,
        accumulate=accumulate,
    )

    if needs_tp_all_reduce:
        if all_reduce_kit is not None:
            if all_reduce_kit.input_is_fp8:
                # TODO: fix this to use the `output_scale` once we have it
                fp8_scale = fp8_linear.input_scale
            else:
                fp8_scale = None
            # Note that the all_reduce input is already filled by `fp8_linear` above.
            x = all_reduce_kit.all_reduce(fp8_scale=fp8_scale, residual_buffer=residual)
        else:
            dist.all_reduce(x, op=dist.ReduceOp.SUM, group=parallel_context.tp_group)

            # Add the residual after the all_reduce if we're not using the all_reduce_kit (which
            # does the residual add as part of the kernel)
            if residual is not None:
                x.add_(residual)

    return x


class LlamaAttention(nn.Module):
    """RMSNorm -> Projection into heads -> QKV -> projection back -> residual.

    Please see layers.py::LlamaAttention for explanations on SplitHeadModes.
    """

    def __init__(
        self,
        emb_dim: int,
        num_heads_q: int,
        num_heads_kv: int,
        head_dim: int,
        use_bias: bool = False,
        qkv_only_bias: bool = False,
        split_head_mode: cached_attention.SplitHeadModes = cached_attention.SplitHeadModes.NO_SPLIT,
        dtype: torch.dtype = torch.bfloat16,
        device: torch.device | str = "cuda",
        parallel_context: ParallelContext = ParallelContext.single_process(),
        all_reduce_kit: all_reduce.AllReduceKit | None = None,
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
    ):
        super().__init__()
        if use_bias and qkv_only_bias:
            raise ValueError("Cannot use both use_bias and qkv_only_bias.")

        self._device = device
        self._parallel_context = parallel_context
        self._all_reduce_kit = all_reduce_kit
        self._use_tensor_parallelism = (
            split_head_mode != cached_attention.SplitHeadModes.NO_SPLIT
            and parallel_context.tp_size > 1
        )
        self._split_head_mode = split_head_mode
        self._attn_all_gather_kit = attn_all_gather_kit

        if num_heads_q % num_heads_kv:
            raise ValueError(f"{num_heads_q=} is not divisible by {num_heads_kv=}.")

        if self._use_tensor_parallelism:
            num_heads_q, num_heads_kv = cached_attention.split_heads(
                num_heads_q, num_heads_kv, parallel_context.cfg, split_head_mode
            )

        self.qkv = FP8Linear(
            in_features=emb_dim,
            out_features=(num_heads_q + 2 * num_heads_kv) * head_dim,
            dtype=dtype,
            bias=use_bias or qkv_only_bias,
            device=device,
        )

        # See NOTE(add-output-bias) in layers.py for why we do this.
        my_rank_should_add_output_bias = (
            not self._use_tensor_parallelism or parallel_context.tp_rank == 0
        )
        self.out = FP8Linear(
            in_features=num_heads_q * head_dim,
            out_features=emb_dim,
            dtype=dtype,
            bias=use_bias and my_rank_should_add_output_bias,
            device=device,
        )

        self.attn_qkv_scales = nn.Parameter(
            torch.ones(3, dtype=torch.float32, device=device),
            requires_grad=False,
        )

    @property
    def input_fp8_meta(self) -> tex.FP8FwdTensors:
        return self.qkv.input_fp8_meta

    def forward(
        self,
        inputs: torch.Tensor,
        attn: cached_attention.Attention,
        layer_idx: int,
        residual: torch.Tensor | None = None,
        output_fp8_meta: tex.FP8TensorMeta | None = None,
    ) -> torch.Tensor:
        if output_fp8_meta is not None:
            raise ValueError("FP8 output unsupported.")
        x = inputs
        x = self.qkv(x)
        x = attn(
            qkv=x,
            layer_idx=layer_idx,
            parallel_ctx=self._parallel_context,
            attn_all_gather_kit=self._attn_all_gather_kit,
            attn_qkv_scales=self.attn_qkv_scales,
        )

        # Call the combined matmul+all_reduce on `self.out`.
        x = _fp8_matmul_with_all_reduce(
            fp8_linear=self.out,
            x=x,
            residual=residual,
            needs_tp_all_reduce=self._use_tensor_parallelism,
            parallel_context=self._parallel_context,
            all_reduce_kit=self._all_reduce_kit,
            output_shape=inputs.shape,
        )
        return x


class LlamaSwiGLU(nn.Module):
    """RMSNorm -> silu(F_1(x)) * F_3(x) -> projection back -> residual."""

    def __init__(
        self,
        emb_dim: int,
        mlp_dim: int,
        use_bias: bool = False,
        dtype: torch.dtype = torch.bfloat16,
        device: torch.device | str = "cuda",
        parallel_context: ParallelContext = ParallelContext.single_process(),
        all_reduce_kit: all_reduce.AllReduceKit | None = None,
    ):
        super().__init__()

        self._device = device
        self._parallel_context = parallel_context
        self._all_reduce_kit = all_reduce_kit

        if mlp_dim % parallel_context.tp_size:
            raise ValueError(
                f"{mlp_dim=} is not divisible by {parallel_context.tp_size=}."
            )

        mlp_dim_per_device = mlp_dim // parallel_context.tp_size

        self.expand = FP8Linear(
            in_features=emb_dim,
            out_features=mlp_dim_per_device * 2,
            bias=use_bias,
            device=device,
            dtype=dtype,
        )

        self.shrink = FP8Linear(
            in_features=mlp_dim_per_device,
            out_features=emb_dim,
            bias=use_bias and parallel_context.tp_rank == 0,
            device=device,
            dtype=dtype,
        )

    @property
    def input_fp8_meta(self) -> tex.FP8FwdTensors:
        return self.expand.input_fp8_meta

    def forward(
        self,
        inputs: torch.Tensor,
        residual: torch.Tensor | None = None,
        output_fp8_meta: tex.FP8TensorMeta | None = None,
    ) -> torch.Tensor:
        if output_fp8_meta is not None:
            raise ValueError("FP8 output unsupported.")
        x = inputs
        x = self.expand(x)
        x = texcpp.swiglu(
            inp=x,
            fp8_meta_tensor=self.shrink.input_fp8_meta,
            fp8_tensor=0,
            otype=FP8Types["e4m3"],
        )

        # Call the combined matmul+all_reduce on `self.shrink`.
        x = _fp8_matmul_with_all_reduce(
            fp8_linear=self.shrink,
            x=x,
            residual=residual,
            needs_tp_all_reduce=self._parallel_context.tp_size > 1,
            parallel_context=self._parallel_context,
            all_reduce_kit=self._all_reduce_kit,
            output_shape=inputs.shape,
        )
        return x


class LlamaTransformerBlock(nn.Module):
    """Input -> norm -> attn -> residual -> norm -> swiglu -> residual."""

    def __init__(
        self,
        emb_dim: int,
        num_heads_q: int,
        num_heads_kv: int,
        head_dim: int,
        mlp_dim: int,
        norm_eps: float = 1e-5,
        split_head_mode: cached_attention.SplitHeadModes = cached_attention.SplitHeadModes.NO_SPLIT,
        use_bias: bool = False,
        qkv_only_bias: bool = False,
        dtype: torch.dtype = torch.bfloat16,
        device: torch.device | str = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
        ffn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
    ):
        assert (ffn_all_reduce_kit is None) == (attn_all_reduce_kit is None), (
            "ffn_all_reduce_kit and attn_all_reduce_kit must be both None or both "
            "not None. See PAIRED BARRIERS in layers.py."
        )
        assert (ffn_all_reduce_kit is None) or (
            attn_all_reduce_kit != ffn_all_reduce_kit
        ), "Need different kits for attn and ffn. See PAIRED BARRIERS in layers.py."
        if use_bias and qkv_only_bias:
            raise ValueError("Cannot use both use_bias and qkv_only_bias.")

        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx

        self.attn_norm = FP8RmsNorm(
            inp_dim=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            device=device,
        )
        self.attn = LlamaAttention(
            emb_dim=emb_dim,
            num_heads_q=num_heads_q,
            num_heads_kv=num_heads_kv,
            head_dim=head_dim,
            use_bias=use_bias,
            qkv_only_bias=qkv_only_bias,
            split_head_mode=split_head_mode,
            dtype=dtype,
            device=device,
            parallel_context=parallel_ctx,
            all_reduce_kit=attn_all_reduce_kit,
            attn_all_gather_kit=attn_all_gather_kit,
        )

        self.ffn_norm = FP8RmsNorm(
            inp_dim=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            device=device,
        )
        self.ffn = LlamaSwiGLU(
            emb_dim=emb_dim,
            mlp_dim=mlp_dim,
            use_bias=use_bias and self._parallel_ctx.tp_rank == 0,
            dtype=dtype,
            device=device,
            parallel_context=parallel_ctx,
            all_reduce_kit=ffn_all_reduce_kit,
        )

    def forward(
        self, inputs: torch.Tensor, attn: cached_attention.Attention, layer_idx: int
    ) -> torch.Tensor:
        with ProfilingContext(f"layer_{layer_idx}"):
            x = inputs

            with ProfilingContext(f"attention_{layer_idx}"):
                x_norm = self.attn_norm(x, output_fp8_meta=self.attn.input_fp8_meta)
                x = self.attn(inputs=x_norm, attn=attn, layer_idx=layer_idx, residual=x)

            with ProfilingContext(f"ffn_{layer_idx}"):
                x_norm = self.ffn_norm(
                    x, output_fp8_meta=self.ffn.expand.input_fp8_meta
                )
                x = self.ffn(inputs=x_norm, residual=x)

        return x


class StarCoder2FFN(nn.Module):
    """Expand -> gelu(F_1(x)) -> projection back."""

    def __init__(
        self,
        emb_dim: int,
        mlp_dim: int,
        use_bias: bool = True,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_context: ParallelContext = ParallelContext.single_process(),
        all_reduce_kit: all_reduce.AllReduceKit | None = None,
    ):
        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_context
        self._is_tp_rank_0 = parallel_context.tp_rank == 0
        self._all_reduce_kit = all_reduce_kit

        if mlp_dim % parallel_context.tp_size:
            raise ValueError(
                f"{mlp_dim=} is not divisible by {parallel_context.tp_size=}."
            )

        mlp_dim_per_device = mlp_dim // parallel_context.tp_size

        self.expand = FP8Linear(
            in_features=emb_dim,
            out_features=mlp_dim_per_device,
            bias=use_bias,
            device=device,
            dtype=dtype,
        )

        self.shrink = FP8Linear(
            in_features=mlp_dim_per_device,
            out_features=emb_dim,
            bias=use_bias and self._is_tp_rank_0,
            device=device,
            dtype=dtype,
        )

    def forward(
        self,
        inputs: torch.Tensor,
        residual: torch.Tensor | None = None,
        output_fp8_meta: tex.FP8TensorMeta | None = None,
    ) -> torch.Tensor:
        """Forward pass.

        Args:
            inputs: `num_tokens, hidden_size`.
            residual: if not `None`, must be with the same shape as `inputs`, the
                result is added to this tensor.
            output_fp8_meta: if not `None`, the metadata for FP8 conversions.

        Returns:
            A tensor with the same shape as `inputs`.
        """
        if output_fp8_meta is not None:
            raise ValueError("FP8 output unsupported.")
        x = self.expand(inputs)
        x = texcpp.gelu(
            inp=x,
            fp8_meta_tensor=self.shrink.input_fp8_meta,
            fp8_tensor=0,
            otype=FP8Types["e4m3"],
        )

        # Call the combined matmul+all_reduce on `self.shrink`.
        x = _fp8_matmul_with_all_reduce(
            fp8_linear=self.shrink,
            x=x,
            residual=residual,
            needs_tp_all_reduce=self._parallel_ctx.tp_size > 1,
            parallel_context=self._parallel_ctx,
            all_reduce_kit=self._all_reduce_kit,
            output_shape=inputs.shape,
        )
        return x


class StarCoder2TransformerBlock(nn.Module):
    """Computes: input -> norm -> attn -> residual -> norm -> swiglu -> residual."""

    def __init__(
        self,
        emb_dim: int,
        num_heads_q: int,
        num_heads_kv: int,
        head_dim: int,
        mlp_dim: int,
        norm_eps: float = 1e-5,
        split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT,
        use_bias: bool = False,
        dtype: torch.dtype = torch.float16,
        device: Device = "cuda",
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        ffn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
        attn_all_reduce_kit: all_reduce.AllReduceKit | None = None,
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
    ):
        assert (ffn_all_reduce_kit is None) == (attn_all_reduce_kit is None), (
            "ffn_all_reduce_kit and attn_all_reduce_kit must be both None or both "
            "not None. See PAIRED BARRIERS in layers.py."
        )
        assert (ffn_all_reduce_kit is None) or (
            attn_all_reduce_kit != ffn_all_reduce_kit
        ), "Need different kits for attn and ffn. See PAIRED BARRIERS in layers.py."

        super().__init__()
        self._device = device
        self._parallel_ctx = parallel_ctx

        self.attn_norm = FP8LayerNorm(
            normalized_shape=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            device=device,
        )
        self.attn = LlamaAttention(
            emb_dim=emb_dim,
            num_heads_q=num_heads_q,
            num_heads_kv=num_heads_kv,
            head_dim=head_dim,
            use_bias=use_bias,
            split_head_mode=split_head_mode,
            dtype=dtype,
            device=device,
            parallel_context=parallel_ctx,
            all_reduce_kit=attn_all_reduce_kit,
            attn_all_gather_kit=attn_all_gather_kit,
        )

        self.ffn_norm = FP8LayerNorm(
            normalized_shape=emb_dim,
            eps=norm_eps,
            dtype=dtype,
            device=device,
        )
        self.ffn = StarCoder2FFN(
            emb_dim=emb_dim,
            mlp_dim=mlp_dim,
            use_bias=use_bias,
            dtype=dtype,
            device=device,
            parallel_context=parallel_ctx,
            all_reduce_kit=ffn_all_reduce_kit,
        )

    def forward(
        self, inputs: torch.Tensor, attn: cached_attention.Attention, layer_idx: int
    ) -> torch.Tensor:
        """One attention, followed by one FFN, both with residuals.

        Args:
            inputs: `num_tokens, hidden_size`.
            attn: the attention object to handle caches.
            layer_idx: the current layer in the model, used by `attn` to navigate
                the KV caches.

        Returns:
            A tensor with the same shape as `inputs`.
        """
        with ProfilingContext(f"layer_{layer_idx}"):
            x = inputs

            with ProfilingContext(f"attention_{layer_idx}"):
                x_norm = self.attn_norm(x, output_fp8_meta=self.attn.input_fp8_meta)
                x = self.attn(inputs=x_norm, attn=attn, layer_idx=layer_idx, residual=x)

            with ProfilingContext(f"ffn_{layer_idx}"):
                x_norm = self.ffn_norm(
                    x, output_fp8_meta=self.ffn.expand.input_fp8_meta
                )
                x = self.ffn(inputs=x_norm, residual=x)

        return x
