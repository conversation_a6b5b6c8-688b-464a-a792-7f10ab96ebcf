"""Test for the cuda_graphs module."""

import pytest
import torch
from immutabledict import immutabledict

from base.fastforward.cuda_graphs import (
    ArgumentSpec,
    GraphedFunction,
    GraphedFunctionRecapturable,
    OnDataPtrChange,
    OutputSpec,
    _RecaptureKey,
    make_graphed_callable,
    tensor_slice,
)


def test_tensor_slice():
    x = torch.tensor([[0, 1, 2], [3, 4, 5], [6, 7, 8]])
    torch.testing.assert_close(tensor_slice(x, 0, slice(1)), torch.tensor([[0, 1, 2]]))
    torch.testing.assert_close(
        tensor_slice(x, 1, slice(1)), torch.tensor([[0], [3], [6]])
    )
    torch.testing.assert_close(
        tensor_slice(x, 1, slice(2)), torch.tensor([[0, 1], [3, 4], [6, 7]])
    )


def test_tensor_slice_aliasing():
    x = torch.tensor([[0, 1, 2], [3, 4, 5], [6, 7, 8]])
    y = tensor_slice(x, 0, slice(1))
    torch.testing.assert_close(y, torch.tensor([[0, 1, 2]]))
    assert y is not x
    # z = x[:1]
    assert y.data_ptr() == x.data_ptr()


def add(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
    return x + y


def iadd(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
    x += y
    return x


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_simple_call(capture_fn):
    graphed_add = capture_fn(add, max_batch_size=8)
    x = torch.tensor([[0, 1]], dtype=torch.float16, device="cuda")
    y = torch.tensor([[3, 0]], dtype=torch.float16, device="cuda")
    torch.testing.assert_close(graphed_add(x, y), add(x, y))


@pytest.mark.parametrize(
    "capture_fn", [make_graphed_callable, GraphedFunction, GraphedFunctionRecapturable]
)
@pytest.mark.parametrize("fn", [add])
def test_make_graphed_callable(capture_fn, fn):
    seq_len = 8
    graphed_fn = capture_fn(fn, max_batch_size=8)

    for batch_size in [1, 2, 4, 8]:
        x = torch.arange(seq_len * batch_size, dtype=torch.float16, device="cuda").view(
            batch_size, seq_len
        )
        y = torch.arange(seq_len * batch_size, dtype=torch.float16, device="cuda").view(
            batch_size, seq_len
        )

        for _ in range(2):
            actual = graphed_fn(x, y)
            expected = fn(x, y)
            torch.testing.assert_close(actual, expected)


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_make_graphed_callable_aliasing(capture_fn):
    graphed_iadd = capture_fn(iadd, max_batch_size=2)
    x = torch.tensor([[0, 1]], dtype=torch.float16, device="cuda")
    y = torch.tensor([[3, 0]], dtype=torch.float16, device="cuda")
    x1 = x.clone()
    y1 = y.clone()
    x2 = x.clone()
    y2 = y.clone()
    expected = iadd(x, y)
    assert expected is x
    assert graphed_iadd(x1, y1) is not x1
    torch.testing.assert_close(graphed_iadd(x2, y2), expected)


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_reuse_memory(capture_fn):
    graphed_iadd = capture_fn(iadd, max_batch_size=2)
    x = torch.tensor([[0, 1]], dtype=torch.float16, device="cuda")
    y = torch.tensor([[3, 0]], dtype=torch.float16, device="cuda")
    x1 = x.clone()
    y1 = y.clone()
    x2 = x.clone()
    y2 = y.clone()

    actual = graphed_iadd(graphed_iadd(x1, y1), y1)
    # Graphed operations do *not* overwrite their initial arguments.
    torch.testing.assert_close(x, x1)
    expected = iadd(iadd(x2, y2), y2)
    # While the default ones do.
    assert not torch.allclose(x, x2)
    torch.testing.assert_close(actual, expected, msg=f"{actual=}, {expected=}")


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_provide_static(capture_fn):
    static_args = [
        torch.zeros((8, 2), dtype=torch.float16, device="cuda"),  # x
        torch.zeros((8, 2), dtype=torch.float16, device="cuda"),  # y
    ]
    graphed_iadd = capture_fn(
        iadd,
        argument_specs=[
            ArgumentSpec(pre_alloc_tensor=static_args[0], batch_dim=0),
            ArgumentSpec(pre_alloc_tensor=static_args[1], batch_dim=0),
        ],
        max_batch_size=8,  # max_batch_size must be the same as the static args.
    )
    x = torch.tensor([[0, 1]], dtype=torch.float16, device="cuda")
    y = torch.tensor([[3, 0]], dtype=torch.float16, device="cuda")
    x1 = x.clone()
    x1_unused = x.clone()
    y1 = y.clone()

    tmp_expected = iadd(x, y)
    tmp_expected_copy = tmp_expected.clone()  # because iadd overwrites its input.
    expected = iadd(tmp_expected, y)

    tmp = graphed_iadd(x1, y1)
    assert tmp.shape == tmp_expected_copy.shape
    torch.testing.assert_close(tmp, tmp_expected_copy)
    actual = graphed_iadd(tmp, y1)
    torch.testing.assert_close(x1_unused, x1)
    assert actual.shape == expected.shape
    torch.testing.assert_close(actual, expected)


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
@pytest.mark.parametrize("max_batch_size", [1, 4, 8, 16])
def test_unbatched_static_args(capture_fn, max_batch_size: int):
    x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    expected = add(x, y)

    captured = capture_fn(
        add,
        argument_specs=[
            ArgumentSpec(pre_alloc_tensor=x, batch_dim=None),
            ArgumentSpec(batch_dim=None),
        ],
        output_spec=[
            OutputSpec(batch_dim=None),
        ],
        max_batch_size=max_batch_size,
    )
    result = captured(x, y)
    torch.testing.assert_close(result, expected)

    x1 = x.clone()
    y1 = y.clone()
    result1 = captured(x1, y1)
    torch.testing.assert_close(result1, expected)


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_raises_on_batchsize_mismatch(capture_fn):
    x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    with pytest.raises(ValueError):
        captured = capture_fn(
            iadd,
            argument_specs=[
                ArgumentSpec(pre_alloc_tensor=x, batch_dim=0),
                ArgumentSpec(pre_alloc_tensor=y, batch_dim=0),
            ],
            max_batch_size=2,  # max_batch_size must be the same as the static args.
        )
        captured(x, y)


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_reuses_static_args(capture_fn):
    static_x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    static_y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")

    def add_with_checks(x, y):
        assert x.data_ptr() == static_x.data_ptr()
        assert y.data_ptr() == static_y.data_ptr()
        return x + y

    graphed_iadd = capture_fn(
        add_with_checks,
        argument_specs=[
            ArgumentSpec(pre_alloc_tensor=static_x, batch_dim=0),
            ArgumentSpec(pre_alloc_tensor=static_y, batch_dim=0),
        ],
        max_batch_size=8,  # max_batch_size must be the same as the static args.
    )
    x = torch.tensor([[0, 1]], dtype=torch.float16, device="cuda")
    y = torch.tensor([[3, 0]], dtype=torch.float16, device="cuda")

    static_x[:1].copy_(x)
    static_y[:1].copy_(y)
    # Mostly this is a smoke test whether the input tensor is indeed reused.
    result1 = graphed_iadd(x, y)
    expected = x + y
    torch.testing.assert_close(result1, expected)
    result2 = graphed_iadd(static_x[:1], static_y[:1])
    torch.testing.assert_close(result1, expected)
    torch.testing.assert_close(result2, expected)


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_check_data_ptr_with_static_args(capture_fn):
    static_x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    static_y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")

    graphed_add = capture_fn(
        add,
        argument_specs=[
            ArgumentSpec(
                pre_alloc_tensor=static_x,
                batch_dim=0,
                on_data_ptr_change=OnDataPtrChange.FAIL,
            ),
            ArgumentSpec(pre_alloc_tensor=static_y, batch_dim=0),
        ],
        max_batch_size=8,  # max_batch_size must be the same as the static args.
    )

    # does not raise:
    _ = graphed_add(static_x[:2], static_y[:2])

    # raises, because the underlying tensor has changed while the argument has
    # been marked as static:
    x_alt = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    with pytest.raises(ValueError):
        _ = graphed_add(x_alt[:2], static_y[:2])


def test_check_data_ptr_batch_dim_raises():
    graphed_add = GraphedFunction(
        add,
        argument_specs=[
            ArgumentSpec(
                pre_alloc_tensor=None,
                batch_dim=0,
                on_data_ptr_change=OnDataPtrChange.FAIL,
            ),
        ],
        max_batch_size=8,  # max_batch_size must be the same as the static args.
    )
    x = torch.tensor([[0, 1]], dtype=torch.float16, device="cuda")
    y = torch.tensor([[3, 0]], dtype=torch.float16, device="cuda")

    # raises, because the underlying tensor is specified
    # to have a batch dimension, so a tensor would have
    # to be pre-allocated, but then the data pointer cannot be equal
    # to the given argument x.
    with pytest.raises(ValueError):
        _ = graphed_add(x, y)


@pytest.mark.parametrize("capture_fn", [GraphedFunction, GraphedFunctionRecapturable])
def test_check_data_ptr_no_pre_alloc_raises(capture_fn):
    graphed_add = capture_fn(
        add,
        argument_specs=[
            ArgumentSpec(
                pre_alloc_tensor=None,
                batch_dim=None,
                on_data_ptr_change=OnDataPtrChange.FAIL,
            ),
            ArgumentSpec(
                batch_dim=None,
            ),
        ],
        max_batch_size=8,  # max_batch_size must be the same as the static args.
    )
    x = torch.tensor([[0, 1]], dtype=torch.float16, device="cuda")
    y = torch.tensor([[3, 0]], dtype=torch.float16, device="cuda")

    # raises, because we are copying the inputs into a fresh tensor that is
    # different from x. This could be fixed, but it's not a priority.
    with pytest.raises(ValueError):
        _ = graphed_add(x, y)


def test_recapture_on_data_ptr_change():
    def add_but_use_static_x(x, y):
        batch_size = y.shape[0]
        return x[:batch_size] + y

    graphed_fn = GraphedFunctionRecapturable(
        add_but_use_static_x,
        argument_specs=[
            ArgumentSpec(
                # pre_alloc_tensor=x,  # TODO(markus): maybe we want to enable that again
                batch_dim=None,
                on_data_ptr_change=OnDataPtrChange.RECAPTURE,
            ),
            ArgumentSpec(batch_dim=0),
        ],
        max_batch_size=8,
    )

    x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    used_batch_size = 4
    y = torch.zeros((used_batch_size, 2), dtype=torch.float16, device="cuda")

    _ = graphed_fn(x, y)
    assert len(graphed_fn._captured_graphs) == 1

    _ = graphed_fn(x, y[:2])
    assert len(graphed_fn._captured_graphs) == 1

    x1 = x.clone()
    _ = graphed_fn(x1, y)
    assert len(graphed_fn._captured_graphs) == 2


def test_data_ptr_equality():
    x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    assert x.data_ptr() == x.data_ptr()
    assert x.data_ptr() != y.data_ptr()
    assert x.data_ptr() is not y.data_ptr()
    assert x.data_ptr() == x[:2].data_ptr()
    assert x.data_ptr() is not x[:2].data_ptr()
    assert x.data_ptr() == x[0].data_ptr()
    assert x.data_ptr() is not x[0].data_ptr()
    assert x.data_ptr() == x[0, 0].data_ptr()
    assert x.data_ptr() is not x[0, 0].data_ptr()
    assert x.data_ptr() != x.clone().data_ptr()
    assert x.data_ptr() is not x.clone().data_ptr()
    assert x.clone().data_ptr() != x.clone().data_ptr()

    x_copy = x.clone()
    # behavior in hash maps
    d = {
        x.data_ptr(): 1,
        x_copy.data_ptr(): 2,
        x.data_ptr(): 3,
    }
    assert len(d) == 2
    assert d[x.data_ptr()] == 3
    with pytest.raises(KeyError):
        # note that this KeyError depends on the previous
        # copy of x to not have been garbage collected.
        x_copy_2 = x.clone()
        _ = d[x_copy_2.data_ptr()]

    assert d[x[0].data_ptr()] == 3


def test_set_capture_allowed():
    x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    captured = GraphedFunction(
        add,
        argument_specs=[
            ArgumentSpec(pre_alloc_tensor=x, batch_dim=0),
            ArgumentSpec(pre_alloc_tensor=y, batch_dim=0),
        ],
        max_batch_size=8,
    )
    captured.set_capture_allowed(False)
    with pytest.raises(RuntimeError):
        _ = captured(x, y)


def test_batch_sizes_pre_graph():
    captured = GraphedFunction(
        add,
        batch_sizes=[2, 4, 8],
    )

    def create_inputs_with_batch_size(batch_size):
        return (
            torch.zeros((batch_size, 2), dtype=torch.float16, device="cuda"),
            torch.zeros((batch_size, 2), dtype=torch.float16, device="cuda"),
        )

    x, y = create_inputs_with_batch_size(4)

    captured(x, y)
    assert len(captured._graph_cache) == 3

    captured(x, y)
    assert len(captured._graph_cache) == 3

    x, y = create_inputs_with_batch_size(8)

    captured(x, y)
    assert len(captured._graph_cache) == 3

    x, y = create_inputs_with_batch_size(2)
    captured(x, y)
    assert len(captured._graph_cache) == 3

    x, y = create_inputs_with_batch_size(1)

    with pytest.raises(ValueError):
        captured(x, y)

    assert len(captured._graph_cache) == 3


def test_batch_sizes_with_static_args():
    x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")

    captured = GraphedFunction(
        add,
        argument_specs=[
            ArgumentSpec(pre_alloc_tensor=x, batch_dim=0),
            ArgumentSpec(pre_alloc_tensor=y, batch_dim=0),
        ],
        batch_sizes=[2, 4, 8],
    )

    captured(x, y)
    assert len(captured._graph_cache) == 3


def test_batch_sizes_inconsistent_raises():
    with pytest.raises(ValueError):
        GraphedFunction(
            add,
            max_batch_size=16,
            batch_sizes=[2, 4, 8],
        )


def test_recapture_key():
    x = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    y = torch.zeros((8, 2), dtype=torch.float16, device="cuda")
    # _RecaptureKey
    assert _RecaptureKey(data_ptrs=immutabledict({"x": x.data_ptr()})) == _RecaptureKey(
        data_ptrs=immutabledict({"x": x.data_ptr()})
    )
    assert _RecaptureKey(data_ptrs=immutabledict({"x": x.data_ptr()})) != _RecaptureKey(
        data_ptrs=immutabledict({"y": x.data_ptr()})
    )
    assert _RecaptureKey(data_ptrs=immutabledict({"x": x.data_ptr()})) != _RecaptureKey(
        data_ptrs=immutabledict({"x": x.data_ptr(), "y": y.data_ptr()})
    )

    d = {}
    d[_RecaptureKey(data_ptrs=immutabledict({"x": x.data_ptr()}))] = 1
    d[_RecaptureKey(data_ptrs=immutabledict({"x": x.data_ptr()}))] = 2
    assert len(d) == 1
    assert d[_RecaptureKey(data_ptrs=immutabledict({"x": x.data_ptr()}))] == 2

    x_copy = x.clone()
    d[_RecaptureKey(data_ptrs=immutabledict({"x": x_copy.data_ptr()}))] = 3
    assert len(d) == 2

    x_alias = x[:3]
    assert x_alias.data_ptr() == x.data_ptr()
    assert x_alias.data_ptr() is not x.data_ptr()
    assert d[_RecaptureKey(data_ptrs=immutabledict({"x": x_alias.data_ptr()}))] == 2
