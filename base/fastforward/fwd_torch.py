"""The torch implementation for fastforward's output."""

import torch
import typing
from base.fastforward import fwd


class TorchLogits2D(fwd.Logits2D):
    """A tensor of logits."""

    def __init__(self, tensor: torch.Tensor):
        assert tensor.ndim == 2
        self.tensor = tensor

    def __getitem__(self, arg_slice: slice | None) -> "TorchLogits2D":
        if arg_slice is None:
            return self
        return TorchLogits2D(self.tensor[arg_slice])

    def __len__(self) -> int:
        return len(self.tensor)

    @property
    def ndim(self) -> int:
        """The number of dimensions of the tensor."""
        return self.tensor.ndim

    def checked_cast(self, type: typing.Type[torch.Tensor]) -> torch.Tensor:
        if not isinstance(self.tensor, type):
            raise TypeError(f"Expected {type} but get {type(self.tensor)}.")
        return self.tensor


class TorchEmbedding(fwd.Embedding):
    """A tensor of embeddings."""

    def __init__(self, tensor: torch.Tensor):
        self.tensor = tensor

    def __getitem__(self, arg_slice: slice | None) -> "TorchEmbedding":
        if arg_slice is None:
            return self
        return TorchEmbedding(self.tensor[arg_slice])

    def __len__(self) -> int:
        return len(self.tensor)

    @property
    def ndim(self) -> int:
        """The number of dimensions of the tensor."""
        return self.tensor.ndim

    def checked_cast(self, type: typing.Type[torch.Tensor]) -> torch.Tensor:
        if not isinstance(self.tensor, type):
            raise TypeError(f"Expected {type} but get {type(self.tensor)}.")
        return self.tensor
