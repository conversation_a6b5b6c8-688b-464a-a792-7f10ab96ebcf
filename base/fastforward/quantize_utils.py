r"""Utils for quantization.

TODO(hieu):
  - Auto check memory usage and offload intermediate values to GPUs.

"""

import abc
import functools
import gc
import logging
import sys
from typing import Any, Callable, Optional, Sequence

import torch
import torch.nn as nn

from base.fastforward import cached_attention, fp8, fwd_torch
from base.fastforward.fp8 import FP8Linear


class SmoothQuantFn(metaclass=abc.ABCMeta):
    """A function that can be called on a model to smooth-quant it."""

    @abc.abstractmethod
    def __call__(self, model: nn.Module, input_amaxes: dict[str, torch.Tensor]):
        pass


def curr_mem(device: torch.device | str = "cuda") -> float:
    return float(
        torch.cuda.memory_stats_as_nested_dict(device)["active_bytes"]["all"]["current"]
        / 1024**3
    )


def compute_input_prescale(
    weights: torch.Tensor, input_amaxes: torch.Tensor, eps: float = 1e-5
) -> torch.Tensor:
    """Used for SmoothQuant."""
    # TODO(hieu): Comment to explain what is going on

    wmax = weights.abs().amax(dim=0)

    # NOTE(arun): SmoothQuant theoretically works with different `alphas`, but
    # using a sqrt (alpha = 0.5) is much more numerically stable than pow.
    return (
        torch.sqrt(input_amaxes.float() / wmax.float().clamp(min=eps))
        .clamp(min=eps)
        .to(dtype=weights.dtype)
    )


def calibrate_input_scale_factors(
    layer_name: str,
    layer: nn.Module,
    calibration_data: Sequence[dict[str, Any]],  # type: ignore
    return_outputs: bool = False,
    preserve_input: bool = False,
    reset_fn: Optional[Callable[[], None]] = None,
    register_tokens_get_positions: bool = False,
    *,
    excludes: Sequence[str] = (),
    **layer_kwargs,
) -> tuple[dict[str, torch.Tensor], dict[str, torch.Tensor], list[torch.Tensor]]:
    """Calibrate input scale factors for all Linear sub-layers in the layer.

    Args:
        layer_name: a string used for logging. does not need to be the same as the
            layer's name in its model.
        layer: the `nn.Module` object that represents the layer.
        calibration_data: data to compute amaxes for quantization.
        return_outputs: if True, returns the layer's outputs on its calibration_data.
            when calibrating a large model that does not fit in a GPU's memory, we
            can quantize one layer at a time, and then use this flag to return the
            calibration data which we can use quantize the next layer.
        preserve_input: if True, will clone input tensors to preserve their
            original values, just in case they are altered, e.g., for residual ops.
        reset_fn: if given, this function is run between calibration steps. this is
            useful when quantizing stateful models, such as those with stateful
            attention caches, for which the caches need to be reset.
        register_tokens_get_positions: if True, will register tokens and get their
            position indices. this is useful for attention layers.
        excludes: a list of layer names to exclude from calibration.

    Returns:
        a tuple of:
            a mapping from linear layer names to their per-channel input scale factors.
            a mapping from linear layer names to their per-channel output scale factors.
            a list of the layer's outputs on its calibration_data.
    """

    # 1. Setup hooks to keep track of input amaxes on all Linear layers.
    input_amaxes = {}
    output_amaxes = {}

    def _dimwise_amax(x: torch.Tensor):
        if x.ndim > 1:
            return x.abs().amax(dim=0)
        return x.abs().amax()

    @torch.no_grad()
    def update_amaxes(module: nn.Module, args, kwargs, output, name: str):
        del module
        if len(args) > 0:
            input_ = args[0]
        elif "input" in kwargs:
            input_ = kwargs["input"]
        else:
            raise ValueError("No input found")
        input_amax = _dimwise_amax(input_)
        output_amax = _dimwise_amax(output)
        torch.max(input_amaxes[name], input_amax, out=input_amaxes[name])
        torch.max(output_amaxes[name], output_amax, out=output_amaxes[name])

    for name, module in layer.named_modules():
        if isinstance(module, nn.Linear) and name not in excludes:
            input_amaxes[name] = torch.zeros(
                module.in_features, device=module.weight.device
            )
            output_amaxes[name] = torch.zeros(
                module.out_features, device=module.weight.device
            )
            module.register_forward_hook(
                functools.partial(update_amaxes, name=name), with_kwargs=True
            )

    # 2. Run the layer on some calibration data to get input amaxes.
    calibration_data_out = []
    num_data = len(calibration_data)
    for i, inp in enumerate(calibration_data):
        if reset_fn is not None:
            reset_fn()

        torch.cuda.empty_cache()
        torch.cuda.synchronize()

        inp = dict(**inp)
        inp.update(**layer_kwargs)
        if preserve_input:
            inp = {
                k: v.clone() if isinstance(v, torch.Tensor) else v
                for k, v in inp.items()
            }
        cuda_inp = {
            k: v.detach().cuda() if isinstance(v, torch.Tensor) else v
            for k, v in inp.items()
        }

        if register_tokens_get_positions:
            # TODO(yury): refactor this.
            # When we quantize a model layer by layer we need to make sure
            # that the tokens are registered before the Transformer block is
            # called. In order to do that, we assume input contains `tokens`,
            # which we pass to the `register_tokens_get_positions` method.
            # We also remove `tokens` from layer input as layer don't need it.
            tokens = cuda_inp.pop("tokens")
            layer_kwargs["attn"].register_tokens_get_positions(tokens, process_idx=0)

        # Note: we should not assume that the output is just a tensor.
        out = layer(**cuda_inp)
        if isinstance(out, fwd_torch.TorchLogits2D):
            out = out.checked_cast(torch.Tensor)
        if isinstance(out, fwd_torch.TorchEmbedding):
            out = out.tensor
        out = out.detach().cpu()
        del cuda_inp
        for k in list(inp.keys()):
            if k != "residual":
                del inp[k]

        if return_outputs:
            calibration_data_out.append(out)
        else:
            calibration_data_out = [out]

        sys.stdout.write(
            f"\r{layer_name} mem(gb)={curr_mem():<7.2f} progress={i+1}/{num_data}"
        )
        sys.stdout.flush()

    print(
        f"\r{layer_name} mem(gb)={curr_mem():<7.2f} progress={num_data}/{num_data}",
        flush=True,
    )
    gc.collect()

    return input_amaxes, output_amaxes, calibration_data_out


def quantize_layer(
    layer_name: str,
    layer_inp: nn.Module,
    calibration_data: Sequence[dict[str, Any]],  # type: ignore
    prefix: str = "",
    smooth_quant_fn: Optional[SmoothQuantFn] = None,
    return_outputs: bool = False,
    reset_fn: Optional[Callable[[], None]] = None,
    register_tokens_get_positions: bool = False,
    *,
    layer_out: Optional[nn.Module] = None,
    **layer_kwargs,
) -> tuple[dict[str, torch.Tensor], list[torch.Tensor]]:
    """Quantize a layer.

    Args:
        layer_name: a string used for logging. does not need to be the same as the
            layer's name in its model.
        layer_inp: the `nn.Module` object that represents the layer.
        calibration_data: data to compute amaxes for quantization.
        prefix: the prefix to the layer in its model. this is used to inferring the
            quantized weights' names.
        smooth_quant_fn: if given, the function will be run for smooth quant. Note that
            smooth quant is depends on the model's internal structure, so it is the
            user's responsibility to implement their model's smooth_quant_fn.
        return_outputs: if True, returns the layer's outputs on its calibration_data.
            when calibrating a large model that does not fit in a GPU's memory, we
            can quantize one layer at a time, and then use this flag to return the
            calibration data which we can use quantize the next layer.
        reset_fn: if given, this function is run between calibration steps. this is
            useful when quantizing stateful models, such as those with stateful
            attention caches, for which the caches need to be reset.
        register_tokens_get_positions: if True, will register tokens and get their
            position indices. this is useful for attention layers.
        layer_out: the `nn.Module` object that represents the layer in FP8. it is not
            needed for quantization per se. but if given, we will use it to compute
            the difference between the outputs of `layer_inp` and `layer_out`, to see
            how much error is introduced by quantization. useful for debugging and
            experimenting with quantization techniques.
        layer_kwargs: additional arguments to be passed into `layer_inp` and
            `layer_out`. for instance, the `attn` object of certain models.

    Returns:
        The layer's weights in FP8, and the layer's outputs on its calibration_data
            if return_outputs is set.
    """
    input_amaxes, output_amaxes, calibration_data_out = calibrate_input_scale_factors(
        layer_name=layer_name,
        layer=layer_inp,
        calibration_data=calibration_data,
        preserve_input=smooth_quant_fn is not None,
        return_outputs=return_outputs,
        reset_fn=reset_fn,
        register_tokens_get_positions=register_tokens_get_positions,
        **layer_kwargs,
    )

    if smooth_quant_fn is not None:
        smooth_quant_fn(layer_inp, input_amaxes)

        input_amaxes, output_amaxes, calibration_data_out = (
            calibrate_input_scale_factors(
                layer_name=f"{layer_name:<20} [SMOOTH CALIBRATION]",
                layer=layer_inp,
                calibration_data=calibration_data,
                return_outputs=return_outputs,
                reset_fn=reset_fn,
                register_tokens_get_positions=register_tokens_get_positions,
                **layer_kwargs,
            )
        )

    with torch.no_grad():
        layer_inp = layer_inp.cpu()  # offload to RAM since we no longer need the model
        torch.cuda.empty_cache()

        weights_e4m3 = {k: v.cpu() for k, v in layer_inp.named_parameters()}
        assert input_amaxes.keys() == output_amaxes.keys()
        for name, input_amax in input_amaxes.items():
            output_amax = output_amaxes[name]
            weight_name_prefix = f"{prefix}{name}"
            if len(weight_name_prefix) > 0:
                weight_name_prefix += "."
            torch.cuda.empty_cache()
            fp8.convert_linear_state_dict_to_fp8(
                state_dict=weights_e4m3,
                input_amaxes=input_amax,
                output_amaxes=output_amax,
                prefix=weight_name_prefix,
                preserve_input=False,
            )

    if layer_out is not None:
        # NOTE: the attention scales (`attn.attn_qkv_scales`) are added to the fp8 state_dict _after_
        # quantize_layer runs here. As a result, this load can fail as a strict load, since
        # layer_out expects all fp8 keys, but the attn_qkv_scales aren't there yet.
        load_result = layer_out.load_state_dict(weights_e4m3, strict=False)
        assert len(load_result.unexpected_keys) == 0, load_result.unexpected_keys
        if len(load_result.missing_keys) > 0:
            assert (
                len(load_result.missing_keys) == 1
                and load_result.missing_keys[0] == "attn.attn_qkv_scales"
            )
        out_orig = calibration_data_out[-1]

        out_kwargs = dict(**layer_kwargs)
        out_kwargs.update(calibration_data[-1])
        out_kwargs = {
            k: v.clone().cuda() if isinstance(v, torch.Tensor) else v
            for k, v in out_kwargs.items()
        }

        if register_tokens_get_positions:
            # TODO(yury): refactor this.
            # When we quantize a model layer by layer we need to make sure
            # that the tokens are registered before the Transformer block is
            # called. In order to do that, we assume input contains `tokens`,
            # which we pass to the `register_tokens_get_positions` method.
            # We also remove `tokens` from layer input as layer don't need it.
            tokens = out_kwargs.pop("tokens")
            layer_kwargs["attn"].register_tokens_get_positions(tokens, process_idx=0)

        if isinstance(layer_out, FP8Linear):
            # NOTE: Linear and FP8Linear have different input names,
            # so we need to adjust input names to match FP8Linear's input names.
            out_kwargs = {"x": out_kwargs["input"]}

        # NOTE(carl): when we run an fp8 layer that uses attention, we need to disable capturing
        # attention maxes. (This is disallowed since the scaling factors get backed into the QKV
        # values before they'd be recorded.) We do so _super_ manually here, since it's kind of
        # an insane situation with a single attention object shared between the 16-bit and fp8
        # models.
        if "attn" in layer_kwargs:
            assert isinstance(layer_kwargs["attn"], cached_attention.BasicAttention)
            mc_attn = layer_kwargs["attn"]._mc_attn
            assert isinstance(
                mc_attn, cached_attention.MultiCacheAttentionImplementation
            )
            prev_should_capture = mc_attn._should_capture_attn_maxes
            mc_attn._should_capture_attn_maxes = False
        out_e4m3 = layer_out(**out_kwargs).to(out_orig.device)
        if "attn" in layer_kwargs:
            mc_attn._should_capture_attn_maxes = prev_should_capture  # type: ignore

        if out_orig.dtype.is_floating_point:
            abs_diff = (out_orig - out_e4m3).abs()
            max_diff = abs_diff.amax().item()
            avg_diff = abs_diff.mean().item()
            logging.info(
                "%-15s mem(gb)=%-6.2f max_diff=%-7.3f avg_diff=%-7.3f",
                layer_name,
                curr_mem(),
                max_diff,
                avg_diff,
            )
        else:
            diff = (out_orig != out_e4m3).to(torch.float32)
            num_diff = diff.sum().to(torch.int32).item()
            avg_diff = diff.mean().item()
            logging.info(
                "%-15s mem(gb)=%-6.2f num_diff=%-8d percent_diff=%-7.3f",
                layer_name,
                curr_mem(),
                num_diff,
                avg_diff,
            )

    return weights_e4m3, calibration_data_out


def update_weights_with_attn_scales(
    weights_e4m3: dict[str, torch.Tensor],
    attn_maxes: dict[int, torch.Tensor],
    num_layers: int,
):
    for layer_idx in range(num_layers):
        # if this fails with a KeyError, it means that means you are probably trying to
        # quantize the model without having set the flag for capturing attention maxes
        # in the attention factory.
        layer_maxes = attn_maxes[layer_idx]
        attn_qkv_scales = fp8.optimal_scaling_factor_for_amax(layer_maxes)
        weights_e4m3[f"layers.{layer_idx}.attn.attn_qkv_scales"] = attn_qkv_scales
