"""Test for the layers.py that require multiple GPUs."""

from typing import Callable, <PERSON><PERSON>

import torch

from base.fastforward import layers, parallel


def _random_weights(shape: Tuple[int, ...]) -> torch.Tensor:
    return torch.normal(0, 1, size=shape, dtype=torch.float16, device="cuda")


# This function needs to exist as multiprocessing can only call named functions in
# the source code in other processes.
def _init_attnoutmlp(
    emb_dim: int, process_idx: int = 0, num_processes: int = 1
) -> Callable[[torch.Tensor, torch.Tensor], torch.Tensor]:
    # create weights for AttnOutMLP
    torch.manual_seed(31415)
    weight_dense_h_to_4h = _random_weights((4 * emb_dim, emb_dim))
    weight_dense_4h_to_h = _random_weights((emb_dim, 4 * emb_dim))
    weight_attention_dense = _random_weights((emb_dim, emb_dim))
    weight_post_attention_layernorm = _random_weights((emb_dim,))
    bias_dense_h_to_4h = _random_weights((4 * emb_dim,))
    bias_dense_4h_to_h = _random_weights((emb_dim,))
    bias_attention_dense = _random_weights((emb_dim,))
    bias_post_attention_layernorm = _random_weights((emb_dim,))

    module = layers.AttnOutMLP(
        weight_dense_h_to_4h=weight_dense_h_to_4h,
        weight_dense_4h_to_h=weight_dense_4h_to_h,
        weight_attention_dense=weight_attention_dense,
        weight_post_attention_layernorm=weight_post_attention_layernorm,
        bias_dense_h_to_4h=bias_dense_h_to_4h,
        bias_dense_4h_to_h=bias_dense_4h_to_h,
        bias_attention_dense=bias_attention_dense,
        bias_post_attention_layernorm=bias_post_attention_layernorm,
        activation_fn=torch.nn.functional.gelu,
        layernorm_eps=1e-5,
        process_idx=process_idx,
        num_processes=num_processes,
    )

    # move inputs to device
    def fn(emb: torch.Tensor, attn: torch.Tensor) -> torch.Tensor:
        emb = emb.clone().to(f"cuda:{process_idx}")
        attn = attn.clone().to(f"cuda:{process_idx}")
        result = module(emb, attn)
        # The result tensor is the same as the emb tensor, which we
        # have received from the main process. We need to clone it to
        # avoid the error:
        # "RuntimeError: Attempted to send CUDA tensor received from another process."
        result = result.clone()
        return result

    return fn


def test_attnoutmlp():
    """Ensures single-process and multi-process AttnOutMLP produce the same result."""
    emb_dim = 128
    single_process_fn = _init_attnoutmlp(emb_dim)

    torch.manual_seed(1337)
    emb_input = _random_weights((1, emb_dim))
    attn_input = _random_weights((1, emb_dim))
    print(f"before {emb_input.requires_grad=}, {attn_input.requires_grad=}", flush=True)
    single_process_result = single_process_fn(emb_input, attn_input)

    assert single_process_result.shape == (1, emb_dim)
    assert single_process_result.dtype == torch.float16

    with parallel.ParallelRunner(num_processes=2) as runner:
        mp_fn = runner.initialize(_init_attnoutmlp, emb_dim=emb_dim)
        # is required_grad=False for the multiprocess version to work.
        print(
            f"after {emb_input.requires_grad=}, {attn_input.requires_grad=}", flush=True
        )
        mp_result = mp_fn(emb_input, attn_input)
        assert mp_result.shape == (1, emb_dim)
        assert mp_result.dtype == torch.float16

        torch.testing.assert_close(
            single_process_result, mp_result, rtol=2e-2, atol=1e-2
        )
