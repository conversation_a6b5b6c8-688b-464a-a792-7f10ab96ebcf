"""Cached attention module.

This attention module aims to model any autoregressive state, which means a
mutable state where layer i at position x can only be computed after layer i-1
is computed for all positions up to x.
"""

import abc
import uuid
from enum import Enum
from typing import Optional, Sequence

import flash_attn
import torch
import torch.nn.functional as F

from base.fastforward import (
    all_reduce,
    parallel,
    positional_embeddings,
    register_tokens,
)
from base.fastforward.multirequest_flash_attention import MultiRequestFlashAttention
from base.fastforward.parallel import ParallelConfig, ParallelContext

Tensor = torch.Tensor
Device = torch.device | str


class AttentionImpl(Enum):
    """Enum specifying which attention implementation to use."""

    BATCHED_FLASH = 0
    MULTI_REQUEST_FLASH = 1
    MULTI_REQUEST_FLASH_V3 = 2
    MULTI_REQUEST_FLASH_V3_FP8 = 3

    def is_multirequest_flash(self) -> bool:
        return self in (
            AttentionImpl.MULTI_REQUEST_FLASH,
            AttentionImpl.MULTI_REQUEST_FLASH_V3,
            AttentionImpl.MULTI_REQUEST_FLASH_V3_FP8,
        )

    def is_flash_v3(self) -> bool:
        return self in (
            AttentionImpl.MULTI_REQUEST_FLASH_V3,
            AttentionImpl.MULTI_REQUEST_FLASH_V3_FP8,
        )


ALL_ATTN_IMPLS_NO_FA3 = [e for e in AttentionImpl if not e.is_flash_v3()]
ALL_ATTN_IMPLS_NO_FP8 = [
    e for e in AttentionImpl if e != AttentionImpl.MULTI_REQUEST_FLASH_V3_FP8
]
ALL_ATTN_IMPLS_WITH_FP8 = [e for e in AttentionImpl]


_UNFILLED_TOKEN_IDX = -1
# All cached_attention modules maintain the tokens corresponding to what their
# caches hold. These tokens are stored in pre-allocated tensors, where the
# unfilled slots have the value _UNFILLED_TOKEN_IDX.


def causal_mask(length: int, device: Optional[Device] = None) -> Tensor:
    """Creates a square causal mask."""
    d = device or torch.cuda.current_device()
    idxs1 = torch.arange(length, device=d).view(length, 1)
    idxs2 = torch.arange(length, device=d).view(1, length)
    return idxs1 >= idxs2


# We create some static tensor that can be reusd by all the layers.
# This is a bit of a hack, but it's the easiest way to avoid creating
# tensors for each layer and batch element. The tensors are created
# once per device and only on the first use.

# Max size 1024 is chosen well above typical round size, and small enough, so
# we don't care about the 2MB wasted GPU memory.
MAX_Q_LEN = 1024
CAUSAL_MASK_BY_DEVICE: dict[str, Tensor] = {}


def get_static_causal_mask(device: Device) -> Tensor:
    d_str = repr(device)
    if d_str not in CAUSAL_MASK_BY_DEVICE:
        mask = causal_mask(MAX_Q_LEN, device)
        CAUSAL_MASK_BY_DEVICE[d_str] = mask
    return CAUSAL_MASK_BY_DEVICE[d_str]


# creating neg_inf outside of mask function to satisfy cuda graphs
NEG_INF_BY_DEVICE = {}


def get_static_neg_inf(device: Device):
    if device not in NEG_INF_BY_DEVICE:
        NEG_INF_BY_DEVICE[device] = torch.tensor(
            float("-inf"), device=device, dtype=torch.float16
        )
    return NEG_INF_BY_DEVICE[device]


def apply_causal_mask(scores: Tensor, device: Device) -> Tensor:
    q_len, kv_len, num_heads, queries_per_head = scores.shape
    assert (
        q_len <= MAX_Q_LEN
    ), q_len  # for large q_len we can iterate over multiple applications
    assert q_len <= kv_len
    if q_len > 1:
        neg_inf = get_static_neg_inf(device)
        assert neg_inf.device == scores.device
        assert neg_inf.dtype == scores.dtype
        scores_to_mask = scores[:, -q_len:]
        assert scores_to_mask.size() == (q_len, q_len, num_heads, queries_per_head)
        mask = get_static_causal_mask(device)[:q_len, :q_len].view(q_len, q_len, 1, 1)
        torch.where(mask, scores_to_mask, neg_inf, out=scores_to_mask)
    return scores


def causal_attention(q: Tensor, k: Tensor, v: Tensor, is_causal: bool = True) -> Tensor:
    """Attention operation with megatron axis order."""
    q_len, num_heads, queries_per_head, head_dim = q.shape
    kv_len, num_heads, head_dim = k.shape
    q = q.view(1, q_len, num_heads * queries_per_head, head_dim)
    k = k.view(1, kv_len, num_heads, head_dim)
    v = v.view(1, kv_len, num_heads, head_dim)
    res = flash_attn.flash_attn_func(  # type:ignore
        q,
        k,
        v,
        dropout_p=0.0,  # dropout
        softmax_scale=head_dim**-0.5,  # softmax scale
        causal=is_causal,
        return_attn_probs=False,  # return softmax
    )
    return res.view(q_len, num_heads, queries_per_head, head_dim)  # type: ignore


def _causal_attention_no_flash(
    q: Tensor,
    k: Tensor,
    v: Tensor,
    cache_idxs: Tensor,
    cache_pos: Tensor,
    softmax_scale: float | None = None,
) -> Tensor:
    """Slow implementation for verification purposes.

    Args:
        q: (seq_len, num_heads_kv, q_per_kv, head_dim)
        k: (num_caches, cache_size, num_heads_kv, head_dim)
        v: (num_caches, cache_size, num_heads_kv, head_dim)
        cache_idxs: (seq_len)
        cache_pos: (seq_len)
        softmax_scale: float or None; if None, use head_dim**-0.5

    Returns:
        out: (seq_len, q_per_kv, num_heads_kv, head_dim), same as q.
    """
    seq_len, num_heads_kv, q_per_kv, head_dim = q.size()
    assert k.size(2) == num_heads_kv, f"{q.size()=} {k.size()=} but {num_heads_kv=}."
    num_caches, cache_size, _, _ = k.size()
    k = k.view(-1, num_heads_kv, head_dim)
    v = v.view(-1, num_heads_kv, head_dim)

    # seq_len (s)
    # num_heads_kv (n)
    # num_heads_q (q)
    # num_caches * cache_size (c)
    # head_dim (h)
    s = torch.einsum("snqh,cnh->snqc", q, k).to(torch.float32)
    if softmax_scale is None:
        softmax_scale = head_dim**-0.5
    s *= softmax_scale

    mask_by_cache_idxs = (  # seq_len, num_caches
        cache_idxs[:, None]
        == torch.arange(num_caches, device=cache_idxs.device)[None, :]
    )

    mask_by_cache_pos = (  # seq_len, cache_size
        cache_pos[:, None] >= torch.arange(cache_size, device=cache_pos.device)[None, :]
    )

    # true means to keep, false means to replace with -INF
    mask_to_keep = torch.logical_and(
        mask_by_cache_idxs.view(seq_len, num_caches, 1),
        mask_by_cache_pos.view(seq_len, 1, cache_size),
    ).view(seq_len, 1, 1, num_caches * cache_size)

    neg_inf = torch.tensor(-float("inf"), device=mask_to_keep.device)

    torch.where(condition=mask_to_keep, input=s, other=neg_inf, out=s)

    p = F.softmax(s, dim=-1).to(v.dtype)

    o = torch.einsum("snqv,vnh->snqh", p, v)
    return o


class Attention(metaclass=abc.ABCMeta):
    """Minimal interface for stateful attention modules as used by the models."""

    @abc.abstractmethod
    def __call__(
        self,
        qkv: Tensor,
        layer_idx: int,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: Tensor | None = None,
    ) -> Tensor:
        pass

    @abc.abstractmethod
    def register_tokens_get_positions(self, tokens: Tensor, process_idx: int) -> Tensor:
        """Register tokens and get their position indices.

        This method is called before the attention is called for the first time
        in the current round.

        Args:
            tokens: (seq_len)
            process_idx: the index of the process that called this method. This
                is used to determine the device to use.

        Returns:
            Tensor of shape (seq_len). The returned tensor indicates the absolute
            position of the tokens in their cache. This can be used for absolute
            position embeddings.
        """
        pass

    @abc.abstractmethod
    def reset(self, to_position: int | None = None, by_positions: int | None = None):
        """Resets the attention cache to the specified position."""
        pass

    @abc.abstractmethod
    def get_tensors_for_cuda_graph(
        self, process_idx: int | None = None
    ) -> dict[str, Tensor]:
        """Returns a dict of all cache tensors. Used when capture CUDA graphs."""
        pass

    @abc.abstractmethod
    def get_id(self) -> str:
        """Returns an ID, for the purposes of determining equality."""
        pass

    @abc.abstractmethod
    def get_max_requests_in_round(self) -> int | None:
        pass

    @abc.abstractmethod
    def get_small_request_max_seqlen(self) -> int | None:
        pass


class MultiCacheAttention(metaclass=abc.ABCMeta):
    """Attention with utility methods needed for reusing caches.

    Split out from Attention to avoid having to implement these methods
    for higher abstractions of the Attention interface that are not reused,
    like RoundAttention.
    """

    @property
    @abc.abstractmethod
    def num_caches(self) -> int:
        pass

    @property
    @abc.abstractmethod
    def padding_cache_idx(self) -> int:
        pass

    # TODO(markus): this should neither be in the interface nor be a property.
    # Make this a field in the implementation.
    @property
    @abc.abstractmethod
    def padding_cache_pos(self) -> int:
        pass

    @abc.abstractmethod
    def get_devices(self) -> Sequence[Device]:
        pass

    @abc.abstractmethod
    def get_device_idx_from_process_idx(self, process_idx: int | None) -> int:
        pass

    @abc.abstractmethod
    def get_device_from_process_idx(self, process_idx: int | None) -> Device:
        pass

    @abc.abstractmethod
    def get_max_requests_in_round(self) -> int | None:
        pass

    @abc.abstractmethod
    def get_small_request_max_seqlen(self) -> int | None:
        pass

    @abc.abstractmethod
    def reset(
        self,
        *,
        cache_idx: int,
        to_position: int | None = None,
        by_positions: int | None = None,
    ):
        pass

    @abc.abstractmethod
    def get_fill_length(self, cache_idx: int, process_idx: int) -> Tensor:
        """Get the position up to which the current attention cache is filled.

        Args:
            cache_idx: int
            process_idx: int, the index of the process that called this method.
                This is used to determine the device to use. We need to get the
                fill length from the right device, so that this method can be
                used in cuda graphs.

        Returns:
            Tensor of shape (1)
        """
        pass

    @abc.abstractmethod
    def get_tokens(self, cache_idx: int) -> Tensor:
        pass

    @abc.abstractmethod
    def register_tokens_get_positions(
        self, tokens: Tensor, cache_idxs: Tensor, process_idx: int
    ) -> Tensor:
        """Register tokens and get their position indices.

        This method is called before the attention is called for the first time
        in the current round.
        """
        pass

    @abc.abstractmethod
    def get_id(self) -> str:
        """Returns an ID, for the purposes of determining equality."""
        pass

    @abc.abstractmethod
    def __call__(
        self,
        qkv: Tensor,
        cache_idxs: Tensor,
        cache_pos: Tensor,
        layer_idx: int,
        cumulative_round_pos: torch.Tensor | None = None,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: Tensor | None = None,
    ) -> Tensor:
        """Applies attention to the given query-key-value tensor.

        Args:
            qkv: (seq_len, (num_heads_q + 2*num_heads_kv) * head_dim)
            cache_idxs: (seq_len), for each token in the round, what is the index
                of the cache line that it will be stored in.
                When sequence parallelism is used, this contains the cache indexes of
                the entire round, because we need to store the keys and values for
                the entire round.
            cache_pos: (seq_len), for each token in the round, what is the position
                that it will occupy in the KV cache (i.e. in the sequence of the request).
                When sequence parallelism is used, this contains the cache positions of
                the entire round, because we need to store the keys and values for
                the entire round.
            layer_idx: single integer 0 <= layer_idx < num_layers
            cumulative_round_pos: A tensor with a single dimension of size at least
                `num_requests_in_round + 1`. Indicates the start and end position of
                each request in the current round. End position is exclusive. Starts
                with a leading 0.
            process_idx: int, the index of the process that called this method.
                This is used to determine the device to use.
            use_sequence_parallel: whether to use sequence parallelism.
            attn_all_gather_kit: if not None, must be an AllGatherKit to use for gathering
                the keys and values. If None, will use the NCCL-based all_gather provided
                through parallel.py.
            attn_qkv_scales: 3-element tensor with fp8 scales for Q/K/V, respectively.

        Returns:
            Tensor of shape (seq_len, num_heads_q * head_dim)
        """
        pass

    @abc.abstractmethod
    def get_tensors_for_cuda_graph(
        self, process_idx: int | None = None
    ) -> dict[str, Tensor]:
        """Returns a dict of all cache tensors. Used when capture CUDA graphs."""
        pass

    @property
    @abc.abstractmethod
    def captured_attn_maxes(self) -> dict[int, Tensor]:
        """Returns a dict of captured attention maxes (per-layer) for quantization."""
        pass


class SplitHeadModes(Enum):
    """Modes to split attention heads when running on multiple GPUs.

    See `Readme-qkv-heads.md` for an extended discussion of head splitting and how it interacts
    with other aspects of model execution.
    """

    NO_SPLIT = 0
    """Not splitting attention heads."""

    KV_HEADS = 1
    """ (often used in MHA and GQA where there are multiple KV-heads).

        Each device will hold `num_kv_heads // num_processes`. In the case of GQA
        and MHA, every Q-head will be on the same device where its KV-head resides.

        In this mode, we will assert that `num_heads_kv % num_processes == 0`.
    """

    Q_PER_HEADS = 2
    """ (often used in MQA or GQA where there are few KV-heads).

        Each KV-head will be replicated onto all devices, while the Q-heads will
            be split. To understand this mode, let:
            ```
                queries_per_head = num_heads_q // num_heads_kv
            ```
            Then for each KV-head, each device will hold a block of
            `queries_per_head` Q-head corresponding to the KV-head.

        NOTE: In this mode, we will assert two things:
                `num_heads_q % num_heads_kv == 0`
                `queries_per_head % num_processes == 0`
                `(num_heads_q // num_heads_kv) % num_processes == 0`
    """


def split_heads(
    num_heads_q: int,
    num_heads_kv: int,
    parallel_config: ParallelConfig,
    split_head_mode: SplitHeadModes,
) -> tuple[int, int]:
    """Splits the number of attention heads across devices.

    Args:
        num_heads_q: number of Q-heads of the model.
        num_heads_kv: number of KV-heads of the model.
        num_processes: number of processes / GPUs.
        split_head_mode: the mode to split the attention heads.

    Returns:
        num_heads_q, num_heads_kv per device.
    """
    num_processes = parallel_config.tp_size
    if split_head_mode == SplitHeadModes.KV_HEADS:
        if num_heads_kv % num_processes:
            raise ValueError(f"{num_heads_kv=} is not divisible by {num_processes=}.")
        if num_heads_q % num_processes:
            # technically not possible, because num_heads_q % num_heads_kv == 0
            raise ValueError(f"{num_heads_q=} is not divisible by {num_processes=}.")

        num_heads_q //= num_processes
        num_heads_kv //= num_processes
    elif split_head_mode == SplitHeadModes.Q_PER_HEADS:
        num_queries_per_head = num_heads_q // num_heads_kv
        if num_queries_per_head % num_processes:
            raise ValueError(
                f"{num_queries_per_head=} ({num_heads_q=}, {num_heads_kv=}) "
                f"is not divisible by {num_processes=}."
            )
        num_heads_q //= num_processes
    elif split_head_mode != SplitHeadModes.NO_SPLIT:
        raise ValueError(f"Unknown value for {split_head_mode=}.")
    return num_heads_q, num_heads_kv


class MultiCacheAttentionImplementation(MultiCacheAttention):
    """Handles attention calls against a cache."""

    def __init__(
        self,
        num_layers: int,
        num_caches: int,
        num_heads: int,
        max_len: int,
        head_dim: int,
        max_requests_in_round: int | None = None,
        max_large_requests_in_round: int = 1,
        small_request_max_seqlen: int | None = None,
        queries_per_head: int = 1,
        dtype=torch.float16,
        parallel_config: ParallelConfig = ParallelConfig.single_process(),
        split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT,
        attention_impl: AttentionImpl = AttentionImpl.BATCHED_FLASH,
        pre_attention_kernel_fusion: bool = False,
        rotary_config: positional_embeddings.RotaryConfig | None = None,
        use_mqa_for_mla: bool = False,
        softmax_scale: float | None = None,
        capture_attn_maxes_for_quantization: bool = False,
        use_register_tokens_kernel: bool = False,
    ):
        """Creates an attention object.

        Conceptual shapes:
            q: batch_size, q_len, num_heads_q, head_dim
            kv: batch_size, kv_len, num_heads_kv, head_dim
            kv_cache: batch_size, kv_cache_len, num_heads_kv, head_dim

        Expected shapes in `__call__`:
            qkv: seq_len, (num_heads_q + 2*num_heads_kv) * head_dim
            kv_cache: num_caches, kv_cache_len, num_heads_kv, head_dim

        The `qkv` tensor will be split and reshaped into:
            q: batch_size=1, seq_len, num_heads_q, head_dim
            kv: batch_size=1, seq_len, num_heads_kv, head_dim

        Args:
            num_layers: how many layers in the network that will use this module.
            num_caches: the number of caches that this module holds.
            num_heads: the number of heads in `k` and `v`.
            max_len: maximum cache length.
            head_dim: dimension of each head.
            queries_per_head: how many `q` heads look at each `kv` head. for MQA / GQA.
            dtype: torch type of `qkv`.
            num_processes: for model parallelism.
            split_head_mode: if given, will the split the attention heads according
                to the description in the class `SplitHeadModes` above.
            attention_impl: the implementation of attention to use.
            pre_attention_kernel_fusion: if True, will fuse the rotary embedding
                with copying the keys and values into the cache.
            rotary_config: if given, will use rotary embeddings.
            use_mqa_for_mla: if `True`, this attention implements the Multi-Head
                Latent Attention algorithm (MLA). It has a different ways to handle
                the KV caches and the QKV tensors. For details about MLA, please see
                the note:
                https://www.notion.so/Multi-head-Latent-Attention-2fd7bc95e789408aa57855e1d04b5a2c
            softmax_scale: if not None, will multiply this with the logits `q @ k.T`.
                if None, will use the default value of `1. / sqrt(head_dim)`.
            max_requests_in_round: the maximum number of requests in a round.
            small_request_max_seqlen: the maximum sequence length for a small request.
            max_large_requests_in_round: the maximum number of large requests in a round.
        """
        self._num_layers = num_layers
        self._num_caches = num_caches
        self._max_len = max_len
        self._head_dim = head_dim
        self._dtype = dtype
        # When used with the parallel.py module, the attention caches are sent
        # from the main process to the other processes via pickle. So, the attention
        # onlyknow the number of processes, and the individual process index needs
        # to be passed in the method calls.
        self._parallel_config = parallel_config
        self._split_head_mode = split_head_mode
        self._attention_impl = attention_impl
        self._use_fp8_attn = attention_impl == AttentionImpl.MULTI_REQUEST_FLASH_V3_FP8
        self._pre_attention_kernel_fusion = pre_attention_kernel_fusion
        self._use_mqa_for_mla = use_mqa_for_mla
        self._softmax_scale = softmax_scale
        self._should_capture_attn_maxes = capture_attn_maxes_for_quantization
        self._running_attn_maxes: dict[int, Tensor] = {}
        self._stable_id = str(uuid.uuid4())
        self._use_register_tokens_kernel = use_register_tokens_kernel

        self._num_runs = 0

        # We store the tokens in the attention object to allow us to check
        # for overlaps in the cache pool. Positions in the token array that are
        # are not used are set to -1.
        #
        # They are stored only on device 0 as we don't need the dupicates.
        self._tokens = (
            torch.zeros(num_caches + 1, max_len, dtype=torch.int32, device="cuda") - 1
        )

        # head(-ache) parameters
        # Per existing conventions:
        #
        #    - num_heads == num_heads_kv
        #       (and hence self._num_heads, or heads_per_device are all about KV-heads)
        #
        #    - num_heads * queries_per_head == num_heads_q

        self._num_heads_q, self._num_heads_kv = split_heads(
            num_heads * queries_per_head,
            num_heads,
            parallel_config,
            split_head_mode,
        )

        self._queries_per_kv_head = self._num_heads_q // self._num_heads_kv

        self._rotary_by_device: list[positional_embeddings.FusedRotaryEmbedding] = []

        kv_shape = (
            num_layers,
            num_caches + 1,
            self._max_len,
            self._num_heads_kv,
            head_dim,
        )
        # For each device we have one position tensor for each cache.
        self._positions: list[Tensor] = []
        self._multiflash_by_device: list[MultiRequestFlashAttention] = []

        # Create the KV caches. Separately handles MLA and non-MLA
        self._kv_by_device: list[tuple[Tensor, ...]] = []
        if self._use_fp8_attn:
            kv_dtype = torch.float8_e4m3fn
        else:
            kv_dtype = dtype
        if use_mqa_for_mla:
            kv_shape = (num_layers, num_caches + 1, max_len, head_dim)
            for device in self.get_devices():
                self._kv_by_device.append(
                    (torch.zeros(size=kv_shape, dtype=kv_dtype, device=device),)
                )
        else:
            kv_shape = (
                num_layers,
                num_caches + 1,
                max_len,
                self._num_heads_kv,
                head_dim,
            )
            for device in self.get_devices():
                self._kv_by_device.append(
                    (
                        torch.zeros(size=kv_shape, dtype=kv_dtype, device=device),
                        torch.zeros(size=kv_shape, dtype=kv_dtype, device=device),
                    )
                )

        for device in self.get_devices():
            self._positions.append(
                torch.zeros(num_caches + 1, dtype=torch.int32, device=device)
            )
            # Save some memory by creating the MRFA object only when it's needed.
            if self._attention_impl.is_multirequest_flash():
                self._multiflash_by_device.append(
                    MultiRequestFlashAttention(
                        nheads_q=self._num_heads_q,
                        headdim=head_dim,
                        max_round_size=self._max_len,
                        max_requests_in_round=max_requests_in_round,
                        max_large_requests_in_round=max_large_requests_in_round,
                        small_request_max_seqlen=small_request_max_seqlen,
                        dtype=dtype,
                        device=device,
                        use_flash_attn_v3=self._attention_impl.is_flash_v3(),
                        use_fp8=self._use_fp8_attn,
                        # TODO(carl): update this once we re-support dynamic parallelism with Q-split.
                        sequence_parallel_q_scale_factor=1,
                    )
                )

        if rotary_config is not None:
            self._create_rotary(rotary_config)

    @property
    def num_caches(self) -> int:
        return self._num_caches

    @property
    def padding_cache_idx(self) -> int:
        return self._num_caches

    @property
    def padding_cache_pos(self) -> int:
        return 0

    def get_max_requests_in_round(self) -> int | None:
        """Return the maximum number of requests in a round."""
        if not self._multiflash_by_device:
            return None
        return self._multiflash_by_device[0].num_requests

    def get_small_request_max_seqlen(self) -> int | None:
        """Return the maximum sequence length for a small request."""
        if not self._multiflash_by_device:
            return None
        return self._multiflash_by_device[0].small_request_max_seqlen

    def reset(
        self,
        *,
        cache_idx: int,
        to_position: int | None = None,
        by_positions: int | None = None,
    ):
        """Reset the attention cache to a given position.

        Not cuda graph safe.
        """
        if torch.cuda.current_device() != 0:
            raise ValueError(
                f"This method should only be called from the main process. "
                f"Called from {torch.cuda.current_device()}."
            )
        if to_position is None and by_positions is None:
            to_position = 0
        if to_position is not None and by_positions is not None:
            raise ValueError(
                f"Only one of to_position and by_positions can be specified. "
                f"Got to_position={to_position} and by_positions={by_positions}."
            )
        if by_positions is not None:
            if by_positions < 0:
                raise ValueError(f"{by_positions=} < 0")
            # passing in process_idx=0 is fine as we assume this method is only called
            # from the main process.
            current_position = int(
                self.get_fill_length(cache_idx=cache_idx, process_idx=0)
            )
            to_position = current_position - by_positions
        assert to_position is not None
        if to_position > self._max_len:
            raise ValueError(f"{to_position=} but {self._max_len=}")
        if to_position < 0:
            raise ValueError(f"{to_position=} < 0")
        curr_idx = self._positions[0][cache_idx].item()

        if to_position == curr_idx:
            # Do nothing if the cache is already at the desired position.
            return

        if to_position > curr_idx:
            raise ValueError(
                f"Cannot reset cache {cache_idx} to position {to_position} "
                f"because it is at position {curr_idx}."
            )

        self._tokens[cache_idx, to_position:curr_idx].fill_(_UNFILLED_TOKEN_IDX)

        for i in range(self._parallel_config.num_processes):
            self._positions[i][cache_idx] = to_position

        self._stable_id = str(uuid.uuid4())

    def get_fill_length(self, cache_idx: int, process_idx: int) -> Tensor:
        """Get the position up to which the current attention cache is filled."""
        device_idx = self.get_device_idx_from_process_idx(process_idx)
        return self._positions[device_idx][cache_idx]

    def register_tokens_get_positions(
        self, tokens: Tensor, cache_idxs: Tensor, process_idx: int
    ) -> Tensor:
        """Add tokens to the attention cache and return their position indices.

        Note(markus): This function is more complex than strictly needed as it
        is written in a cuda-graphable way. Please keep it as such for now.

        # TODO(hieu): instead of using -1, use an explicit value

        If  self._positions = [0 2 0 1 0]
        and      cache_idxs = [3 3 3 | 1 1 1 1 | 2 2 | -1]
        then        new_pos = [1 2 3 | 2 3 4 5 | 0 1 | 0]
        """
        device_idx = self.get_device_idx_from_process_idx(process_idx)
        device = self.get_device_from_process_idx(process_idx)
        assert tokens.device == device, f"{tokens.device=} != {device=}"
        assert cache_idxs.device == device, f"{cache_idxs.device=} != {device=}"

        new_pos = register_tokens.register_tokens_core(
            tokens=tokens,
            cache_idxs=cache_idxs,
            positions=self._positions[device_idx],
            tokens_cache=self._tokens,
            num_caches=self._num_caches,
            padding_cache_idx=self.padding_cache_idx,
            padding_cache_pos=self.padding_cache_pos,
            device=device,
            device_idx=device_idx,
            use_kernel=self._use_register_tokens_kernel,
        )

        return new_pos

    def get_tokens(self, cache_idx: int) -> Tensor:
        if torch.cuda.current_device() != 0:
            raise ValueError(
                "If not called from device 0, this is likely a bug. get_tokens "
                "should only be called from outside of the step function."
            )
        return self._tokens[cache_idx, : self._positions[0][cache_idx]]

    def get_id(self) -> str:
        return self._stable_id

    def __call__(
        self,
        qkv: Tensor,
        cache_idxs: Tensor,
        cache_pos: Tensor,
        layer_idx: int,
        cumulative_round_pos: torch.Tensor | None = None,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: Tensor | None = None,
    ) -> Tensor:
        """Splits the qkv tensor into query, key, and value tensors and calls the attention function.

        This is the legacy interface to be deprecated when all models produce separate
        query, key, and value tensors.
        """
        # NOTE: this check is related to support dynamic parallelism (ie, differently-configured)
        # models sharing an attention object. There are two cases:
        # 1. If there is no head-splitting, then we can support attention cache sharing between
        #    models so long as they have the same number of processes. (`else` branch)
        # 2. If there is head-splitting, then we currently disable dynamic parallelism. With
        #    further work, we can enable dynamic parallelism for Q-splitting.
        if self._split_head_mode != SplitHeadModes.NO_SPLIT:
            assert (
                parallel_ctx.cfg == self._parallel_config
            ), f"{parallel_ctx.cfg=} but {self._parallel_config=}"
        else:
            assert parallel_ctx.num_processes == self._parallel_config.num_processes, (
                f"{parallel_ctx.num_processes=} but "
                f"{self._parallel_config.num_processes=}"
            )

        head_dim = self._head_dim
        num_heads_q = self._num_heads_q
        num_heads_kv = self._num_heads_kv
        num_queries_per_head = self._queries_per_kv_head
        # TODO(carl): this is the (old) code to support dynamic parallelism where the attention
        # object supports multiple parallel configs.
        # if self._split_head_mode == SplitHeadModes.Q_PER_HEADS:
        #     num_heads_q *= parallel_ctx.sp_size
        #     num_queries_per_head *= parallel_ctx.sp_size
        num_heads_total = num_heads_q + 2 * num_heads_kv
        assert num_queries_per_head * num_heads_kv == num_heads_q

        assert cache_idxs.numel() == cache_pos.numel()
        if parallel_ctx.sp_size > 1:
            assert (
                self._split_head_mode != SplitHeadModes.KV_HEADS
            ), "Sequence parallelism is incompatible with KV head splitting."
        # num_tokens in qkv should be 1/sp_size of the total number of tokens in the batch.
        assert qkv.size(0) == cache_idxs.numel() // parallel_ctx.sp_size, (
            f"{qkv.size(0)=} but {cache_idxs.numel() // parallel_ctx.sp_size=}. "
            f"{parallel_ctx.sp_size=}"
        )

        assert qkv.size(1) == num_heads_total * head_dim, (
            f"{qkv.size(1)=} but {num_heads_total * head_dim=}. "
            f"{num_heads_kv=} {num_heads_q=} {head_dim=}"
        )
        if self._use_fp8_attn and attn_qkv_scales is None:
            raise ValueError("attn_qkv_scales must be provided for fp8 attention.")

        curr_len = qkv.size(0)
        qkv = qkv.view(curr_len, num_heads_kv, num_queries_per_head + 2, head_dim)
        # TODO(markus): The following `split` call will return non-contiguous tensors.
        # We should refactor models to generate separate query, key, and value tensors
        # that are contiguous.
        query, k_new, v_new = qkv.split([num_queries_per_head, 1, 1], dim=2)
        k_new = k_new.view(curr_len, num_heads_kv, head_dim)
        v_new = v_new.view(curr_len, num_heads_kv, head_dim)
        q_new = query.view(curr_len, num_heads_kv, num_queries_per_head, head_dim)

        if self._use_mqa_for_mla:
            assert parallel_ctx.sp_size == 1, "MLA is only supported in SP1 mode."
            # TODO(markus): this is a temporary hack that we should remove urgently, as
            # soon as flash attention supports head dim 576.
            return self.attend_mqa_for_mla(
                query=q_new,
                key=k_new,
                value=v_new,
                cache_idxs=cache_idxs,
                cache_pos=cache_pos,
                layer_idx=layer_idx,
                cumulative_round_pos=cumulative_round_pos,
                # Since we have sp_size=1, we are guaranteed the process_idx is the same globally and in the tp group.
                process_idx=parallel_ctx.process_idx,
            )
        return self.attend(
            q_new,
            k_new,
            v_new,
            cache_idxs,
            cache_pos,
            layer_idx,
            cumulative_round_pos,
            parallel_ctx,
            attn_all_gather_kit,
            attn_qkv_scales,
        )

    def attend_mqa_for_mla(
        self,
        query: Tensor,
        key: Tensor,
        value: Tensor,
        cache_idxs: Tensor,
        cache_pos: Tensor,
        layer_idx: int,
        cumulative_round_pos: torch.Tensor | None = None,
        process_idx: int | None = None,
    ) -> Tensor:
        if not self._use_mqa_for_mla:
            raise RuntimeError(
                "atten_mqa_for_mla() is only supported in mqa_for_mla mode."
            )

        if self._num_heads_kv != 1:
            raise RuntimeError(
                "atten_mqa_for_mla() is only supported for num_heads_kv == 1."
            )

        head_dim = self._head_dim
        num_heads_q = self._num_heads_q
        num_heads_kv = self._num_heads_kv
        q_per_kv = num_heads_q // num_heads_kv
        curr_len = query.size(0)

        query = query.view(curr_len, num_heads_q, head_dim)
        key = key.view(curr_len, 1, head_dim)
        value = value.view(curr_len, head_dim)

        device_idx = self.get_device_idx_from_process_idx(process_idx)
        kv_cache = self._kv_by_device[device_idx][0][layer_idx]
        num_caches = self._num_caches
        max_len = kv_cache.size(1)

        rotary_module = (
            self._rotary_by_device[device_idx] if self._rotary_by_device else None
        )
        if self._pre_attention_kernel_fusion and rotary_module:
            raise NotImplementedError(
                "Pre-attention kernel fusion is not supported the in mqa_for_mla mode."
            )
        else:
            if rotary_module:
                query = rotary_module(query, pos=cache_pos)
                key = rotary_module(key, pos=cache_pos)
            key = key.squeeze(1)
            kv_cache[cache_idxs, cache_pos] = key

        torch.cuda.nvtx.range_push("attention")

        # kv_shape = (num_caches + 1, max_len, head_dim)
        out = _causal_attention_no_flash(
            q=query.view(curr_len, num_heads_kv, q_per_kv, head_dim),
            k=kv_cache.view(num_caches + 1, max_len, num_heads_kv, head_dim),
            v=kv_cache.view(num_caches + 1, max_len, num_heads_kv, head_dim),
            cache_idxs=cache_idxs,
            cache_pos=cache_pos,
            softmax_scale=self._softmax_scale,
        )
        out = out.view(curr_len, -1)

        torch.cuda.nvtx.range_pop()
        return out

    def attend(
        self,
        query: Tensor,
        key: Tensor,
        value: Tensor,
        cache_idxs: Tensor,
        cache_pos: Tensor,
        layer_idx: int,
        cumulative_round_pos: torch.Tensor | None = None,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: Tensor | None = None,
    ) -> Tensor:
        """Attention interface with separate query, key, and value tensors."""
        if self._use_fp8_attn:
            assert attn_qkv_scales is not None, "Must pass attn_qkv_scales for fp8."
        head_dim = self._head_dim
        num_queries_per_head = self._queries_per_kv_head
        num_heads_q = self._num_heads_q
        # TODO(carl): this is the (old) code to support dynamic parallelism where the attention
        # object supports multiple parallel configs.
        # if self._split_head_mode == SplitHeadModes.Q_PER_HEADS:
        #     num_heads_q *= parallel_ctx.sp_size
        #     num_queries_per_head *= parallel_ctx.sp_size
        num_heads_kv = self._num_heads_kv
        assert num_queries_per_head * num_heads_kv == num_heads_q

        curr_len = query.size(0)
        assert (
            query.size() == (curr_len, num_heads_kv, num_queries_per_head, head_dim)
        ), f"{query.size()=} but {curr_len=} {num_heads_kv=} {num_queries_per_head=} {head_dim=}"
        assert key.size() == (
            curr_len,
            num_heads_kv,
            head_dim,
        ), f"{key.size()=} but {curr_len=} {num_heads_kv=} {head_dim=}"
        assert value.size() == (
            curr_len,
            num_heads_kv,
            head_dim,
        ), f"{value.size()=} but {curr_len=} {num_heads_kv=} {head_dim=}"

        # TODO(carl): remove the redundant device_idx <-> process_idx mapping
        device_idx = self.get_device_idx_from_process_idx(parallel_ctx.process_idx)
        k_cache, v_cache = self._kv_by_device[device_idx]
        k_cache: Tensor = k_cache[layer_idx]
        v_cache: Tensor = v_cache[layer_idx]

        # For sequence parallelism, at this point in the code:
        # - query / key / value are sequence_split
        # - But cache_pos and cache_idxs are not sequence split
        # - So we need to make the following changes:
        #     - All-gather keys/values so we have the full ones to populate the kv-cache
        #     - Use the full cache_pos/cache_idx tensors where we populate the kv-cache
        #     - But we need split pos/idxs for the purposes of attention itself, as there the positions refer to queries.
        cache_pos_q = cache_pos
        cache_idxs_q = cache_idxs
        # Notes: curr_len needs to be split. We have query len, and KV len, and an offset, because we may not attend to the end of the KV cache.
        if parallel_ctx.sp_size > 1:
            if attn_all_gather_kit is not None:
                # Key and value both have shape (curr_len, num_heads_kv, head_dim).
                merged_shape = torch.Size((curr_len, num_heads_kv, 2 * head_dim))
                input_tensor = attn_all_gather_kit.get_input_tensor(merged_shape)
                input_tensor[:, :, :head_dim].copy_(key)
                input_tensor[:, :, head_dim:].copy_(value)
                merged_result = attn_all_gather_kit.all_gather(merged_shape)
                key = merged_result[:, :, :head_dim]
                value = merged_result[:, :, head_dim:]
            else:
                ### Copy keys and values into joint tensor and then do one all-gather
                kv = torch.cat([key, value], dim=2)
                kv = parallel.gather_sequence_parallel(
                    tensor=kv,
                    parallel_ctx=parallel_ctx,
                )
                key = kv[:, :, :head_dim]
                value = kv[:, :, head_dim:]
            # The query remains the same, as we want to only evaluate the query
            # associated with the tokens that live on this device.
            cache_pos_q = parallel.split_tensor_for_sequence_parallelism(
                tensor=cache_pos,
                parallel_ctx=parallel_ctx,
            )
            cache_idxs_q = parallel.split_tensor_for_sequence_parallelism(
                tensor=cache_idxs,
                parallel_ctx=parallel_ctx,
            )
            assert (
                cache_pos_q.size(0) == curr_len
            ), f"{cache_pos_q.size(0)=} != {curr_len=}"
            assert (
                cache_idxs_q.size(0) == curr_len
            ), f"{cache_idxs_q.size(0)=} != {curr_len=}"

        rotary_module = (
            self._rotary_by_device[device_idx] if self._rotary_by_device else None
        )
        if self._pre_attention_kernel_fusion:
            if rotary_module:
                query = rotary_module.rotary_qkv(
                    q=query,
                    pos_q=cache_pos_q,
                    k=key,
                    pos_kv=cache_pos,
                    k_dest=k_cache,
                    v=value,
                    v_dest=v_cache,
                    kv_cache_idxs=cache_idxs,
                    qkv_scales=attn_qkv_scales if self._use_fp8_attn else None,
                )
            else:
                # NOTE(carl): out-of-top-level import to avoid import failures in research when
                # the compiled kernels haven't been built.
                from base.fastforward.compiled_attention_utils import (
                    fused_q_contiguous_kv_copy,
                )

                assert not self._use_fp8_attn
                query = fused_q_contiguous_kv_copy(
                    query,
                    key,
                    value,
                    cache_pos,
                    k_cache,
                    v_cache,
                    cache_idxs,
                )
        else:
            if rotary_module:
                query = rotary_module(query, pos=cache_pos_q)
                key = rotary_module(key, pos=cache_pos)
            # If fp8, here is where we cast Q/K/V to fp8.
            if self._use_fp8_attn:
                assert attn_qkv_scales is not None  # for pyright
                # NOTE(fp8-uint8): unfortunately, pytorch doesn't supported indexed-copy on its fp8
                # dtype. But FA3 expects the fp8 dtype as input for the kv-caches. The solution is:
                # - The kv-cache tensors are allocated with dtype=float8_e4m3fn (ie, real fp8).
                # - The Q/K/V tensors here are constructed / cast with the real fp8 type.
                # - We use PyTorch's `view`-based type punning to view K/V as dtype=uint8 and do
                #   the same to kv_cache s.t. the indexed-copy works right.
                query = (query * attn_qkv_scales[0]).to(torch.float8_e4m3fn)
                key = (
                    (key * attn_qkv_scales[1]).to(torch.float8_e4m3fn).view(torch.uint8)
                )
                value = (
                    (value * attn_qkv_scales[2])
                    .to(torch.float8_e4m3fn)
                    .view(torch.uint8)
                )
            # To handle the fp8 case, we need the extra `.view` on the kv-caches below. These
            # asserts are extra checks that this is a no-op _except_ in the fp8 case.
            assert key.dtype == value.dtype
            if key.dtype != torch.uint8:
                assert k_cache.dtype == key.dtype
                assert v_cache.dtype == value.dtype
            k_cache.view(key.dtype)[cache_idxs, cache_pos] = key
            v_cache.view(value.dtype)[cache_idxs, cache_pos] = value

        if self._should_capture_attn_maxes:
            assert (
                attn_qkv_scales is None
            ), "Cannot pass attn_qkv_scales when capturing maxes for quanzation."
            assert (
                not self._pre_attention_kernel_fusion
            ), "Capture maxes not supported with pre-attention kernel fusion."
            self._update_attn_maxes(layer_idx, query, key, value)

        torch.cuda.nvtx.range_push("attention")
        if self._attention_impl.is_multirequest_flash():
            if cumulative_round_pos is None:
                raise ValueError(
                    "cumulative_round_pos must be provided for multi-request attention"
                )
            mrfa = self._multiflash_by_device[device_idx]
            query = query.view(curr_len, num_heads_q, head_dim).contiguous()
            out = mrfa(
                q=query,
                k_cache=k_cache,
                v_cache=v_cache,
                cumulative_request_seqlens=cumulative_round_pos,
                cache_idxs=cache_idxs_q,
                cache_seqlens=cache_pos_q + 1,
                sequence_parallel_rank=parallel_ctx.sp_rank,
                use_sequence_parallel=parallel_ctx.sp_size > 1,
                attn_qkv_scales=attn_qkv_scales if self._use_fp8_attn else None,
            )
        else:
            # The following call requires a reshape instead of a view, because we
            # call `split` on the qkv tensor.
            query = query.reshape(curr_len, 1, num_heads_q, head_dim)
            out: torch.Tensor = flash_attn.flash_attn_with_kvcache(
                q=query,
                k_cache=k_cache,
                v_cache=v_cache,
                k=None,
                v=None,
                cache_seqlens=cache_pos_q + 1,
                cache_batch_idx=cache_idxs_q,
                softmax_scale=None,
                causal=True,
            )

        # TODO(markus): figure out why flash attention sometimes returns a non-contiguous tensor
        out = out.reshape(curr_len, -1)

        torch.cuda.nvtx.range_pop()
        return out

    def _create_rotary(self, rotary_config: positional_embeddings.RotaryConfig) -> None:
        """Create RoPE object."""
        assert not self._rotary_by_device

        if rotary_config.rotary_ratio == 0.0:
            return

        self._rotary_by_device = []
        for device in self.get_devices():
            self._rotary_by_device.append(
                positional_embeddings.FusedRotaryEmbedding(
                    head_dim=self._head_dim,
                    max_seq_len=self._max_len,
                    config=rotary_config,
                    device=device,
                )
            )

    def get_tensors_for_cuda_graph(
        self, process_idx: int | None = None
    ) -> dict[str, Tensor]:
        device_idx = self.get_device_idx_from_process_idx(process_idx)
        data = {
            "k_cache": self._kv_by_device[device_idx][0],
            "v_cache": self._kv_by_device[device_idx][1],
            "positions": self._positions[device_idx],
        }
        if device_idx == 0:
            data["tokens_history"] = self._tokens
        return data

    def get_device_idx_from_process_idx(self, process_idx: int | None) -> int:
        if self._parallel_config.num_processes > 1 and process_idx is None:
            raise ValueError(
                f"In a multi-process setting, process_idx must be specified. "
                f"Got {process_idx=}."
            )
        if process_idx is None:
            # TODO(markus): remove this fallback to the current device.
            # This requires all models to specify process_idx in
            # the forward step function.
            process_idx = torch.cuda.current_device()
        return process_idx

    def get_device_from_process_idx(self, process_idx: int | None) -> Device:
        return torch.device("cuda", self.get_device_idx_from_process_idx(process_idx))

    def get_devices(self) -> Sequence[Device]:
        return [
            self.get_device_from_process_idx(i)
            for i in range(self._parallel_config.num_processes)
        ]

    def _update_attn_maxes(
        self, layer_idx: int, query: Tensor, key: Tensor, value: Tensor
    ):
        if layer_idx not in self._running_attn_maxes:
            self._running_attn_maxes[layer_idx] = torch.zeros(
                3, dtype=torch.float32, device=query.device
            )
        max_tensor = self._running_attn_maxes[layer_idx]
        this_max = torch.zeros_like(max_tensor)
        this_max[0].copy_(query.abs().amax())
        this_max[1].copy_(key.abs().amax())
        this_max[2].copy_(value.abs().amax())
        torch.maximum(max_tensor, this_max, out=max_tensor)

    @property
    def captured_attn_maxes(self) -> dict[int, Tensor]:
        return self._running_attn_maxes


class BasicAttention(Attention):
    """A wrapper for MultiCacheAttention with the cache size of one."""

    def __init__(
        self, mc_attn: MultiCacheAttention, cache_idx: int = 0, num_processes: int = 1
    ):
        assert mc_attn.num_caches > cache_idx
        self._mc_attn = mc_attn
        self._cache_idx = cache_idx
        self._num_processes = num_processes

        # The stable ID is needed to make the BasicAttention usable in a multi-gpu
        # setting, where the attention object is sent via pickle to the other processes.
        # To the other processes, the attention object appears to be new every time, so
        # it would create cache misses (in the cuda graph cache). Basing the hashes
        # and equality on a stable ID ensures that we reuse cuda graphs.
        self._stable_id = str(uuid.uuid4())
        print(f"Created BasicAttention with stable_id {self._stable_id}.", flush=True)

    def __hash__(self) -> int:
        return hash(self._stable_id)

    def __eq__(self, other: object) -> bool:
        return type(self) == type(other) and self._stable_id == other._stable_id  # type: ignore

    def _default_cache_idxs(self, n: int, process_idx: int) -> Tensor:
        device = self._mc_attn.get_device_from_process_idx(process_idx)
        return torch.full(
            size=(n,), fill_value=self._cache_idx, dtype=torch.int32, device=device
        )

    def _default_cache_pos(self, num_tokens_in_round: int, process_idx: int) -> Tensor:
        start_pos = self._mc_attn.get_fill_length(
            cache_idx=self._cache_idx, process_idx=process_idx
        )
        device = self._mc_attn.get_device_from_process_idx(process_idx)
        pos = (
            torch.arange(-num_tokens_in_round, 0, dtype=torch.int32, device=device)
            + start_pos
        )
        pos = torch.max(pos, torch.zeros_like(pos))  # negative positions are not good.
        return pos

    def _default_cumulative_round_pos(
        self,
        num_tokens_in_round: int,
        process_idx: int | None,
    ) -> Tensor | None:
        # For a single request, the cumulative posistion is just [0] + [ntokens] * num_requests
        device = self._mc_attn.get_device_from_process_idx(process_idx)
        max_requests_in_round = self._mc_attn.get_max_requests_in_round()
        if max_requests_in_round is None:
            return None
        return torch.cat(
            (
                torch.zeros(1, dtype=torch.int32, device=device),
                torch.ones(
                    max_requests_in_round,
                    dtype=torch.int32,
                    device=device,
                )
                * num_tokens_in_round,
            ),
            dim=0,
        )

    def __call__(
        self,
        qkv: Tensor,
        layer_idx: int,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
        attn_all_gather_kit: all_reduce.AllGatherKit | None = None,
        attn_qkv_scales: Tensor | None = None,
    ) -> Tensor:
        ntokens = qkv.size(0)
        # For sequence parallel processing we process only a section of the round,
        # so the qkv tensor has a reduced batch dimension. We scale up the
        # number of tokens here, relying on the fact that we evenly split
        # the tokens across the processes.
        ntokens *= parallel_ctx.sp_size
        cache_idxs = self._default_cache_idxs(
            n=ntokens, process_idx=parallel_ctx.process_idx
        )
        cache_pos = self._default_cache_pos(
            num_tokens_in_round=ntokens,
            process_idx=parallel_ctx.process_idx,
        )
        assert cache_idxs.device == cache_pos.device
        assert (
            cache_idxs.device == qkv.device
        ), f"{cache_idxs.device=} != {qkv.device=}, {parallel_ctx.process_idx=}"
        cumulative_round_pos = self._default_cumulative_round_pos(
            num_tokens_in_round=ntokens,
            process_idx=parallel_ctx.process_idx,
        )
        return self._mc_attn(
            qkv=qkv,
            cache_idxs=cache_idxs,
            cache_pos=cache_pos,
            layer_idx=layer_idx,
            cumulative_round_pos=cumulative_round_pos,
            parallel_ctx=parallel_ctx,
            attn_all_gather_kit=attn_all_gather_kit,
            attn_qkv_scales=attn_qkv_scales,
        )

    def register_tokens_get_positions(self, tokens: Tensor, process_idx: int) -> Tensor:
        cache_idxs = self._default_cache_idxs(n=tokens.numel(), process_idx=process_idx)
        return self._mc_attn.register_tokens_get_positions(
            tokens=tokens, cache_idxs=cache_idxs, process_idx=process_idx
        )

    def reset(self, to_position: int | None = None, by_positions: int | None = None):
        self._mc_attn.reset(
            cache_idx=0, to_position=to_position, by_positions=by_positions
        )

    def get_id(self) -> str:
        return self._mc_attn.get_id()

    def get_tensors_for_cuda_graph(
        self, process_idx: int | None = None
    ) -> dict[str, Tensor]:
        return self._mc_attn.get_tensors_for_cuda_graph(process_idx=process_idx)

    @property
    def captured_attn_maxes(self) -> dict[int, Tensor]:
        return self._mc_attn.captured_attn_maxes

    def get_max_requests_in_round(self) -> int | None:
        return self._mc_attn.get_max_requests_in_round()

    def get_small_request_max_seqlen(self) -> int | None:
        return self._mc_attn.get_small_request_max_seqlen()
