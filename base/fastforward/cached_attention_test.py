"""Tests for the attention cache module."""

import pickle

import pytest
import torch
import torch.nn.functional as F

from base.fastforward import attention, cached_attention, positional_embeddings

DEVICE = "cuda"
PRNG = torch.Generator(device="cpu")
PRNG.manual_seed(1337)
DTYPE = torch.float16


def fresh_qkv(seq_len: int, num_heads: int = 1, qkv_dim: int = 4):
    qkv = torch.randn(generator=PRNG, dtype=DTYPE, size=(seq_len, num_heads, qkv_dim))
    return qkv.to(DEVICE)


def check_nan(t):
    assert not torch.isnan(t).any(), torch.isnan(t)


def test_causal_mask():
    m = cached_attention.causal_mask(3)
    assert torch.equal(
        m,
        torch.tensor(
            [[True, False, False], [True, True, False], [True, True, True]],
            device="cuda",
        ),
    )


def test_apply_causal_mask():
    scores = torch.randn(generator=PRNG, dtype=DTYPE, size=(2, 3, 1, 1)).to(DEVICE)
    masked = cached_attention.apply_causal_mask(scores, device=DEVICE)
    ninf = torch.tensor(float("-inf"), device=DEVICE, dtype=DTYPE)
    assert masked[0, 2] == ninf
    masked[0, 2] = 0
    assert not torch.isinf(masked).any()


def test_causal_attention_nan():
    q_len = 2
    q = fresh_qkv(q_len).reshape(q_len, 1, 1, 4)
    k = fresh_qkv(3)
    v = fresh_qkv(3)
    result = cached_attention.causal_attention(q, k, v)
    check_nan(result)


@pytest.mark.parametrize("use_rotary", (True, False))
def test_can_pickle(use_rotary: bool):
    seq_len = 15
    num_layers = 2
    rotary_config = positional_embeddings.RotaryConfig(
        rotary_ratio=0.5,
        rotary_theta=1e4,
        max_position_embeddings=128,
        rotary_interleave=True,
    )
    mc_attn = cached_attention.MultiCacheAttentionImplementation(
        num_layers=num_layers,
        num_heads=2,
        max_len=64,
        head_dim=8,
        num_caches=1,
        rotary_config=rotary_config if use_rotary else None,
    )
    attn = cached_attention.BasicAttention(mc_attn)

    def check(attn):
        attn_deserialized = pickle.loads(pickle.dumps(attn))
        assert hasattr(attn, "_mc_attn")
        assert hasattr(attn_deserialized, "_mc_attn")
        assert attn._mc_attn._head_dim == attn_deserialized._mc_attn._head_dim
        assert torch.cuda.current_device() == 0
        key, _ = attn._mc_attn._kv_by_device[0]
        key_deserialized, _ = attn_deserialized._mc_attn._kv_by_device[0]
        torch.testing.assert_close(key, key_deserialized)

    check(attn)

    for _ in range(3):
        tokens = torch.arange(seq_len, dtype=torch.int32, device="cuda")
        attn.register_tokens_get_positions(tokens, process_idx=0)
        for layer in range(num_layers):
            qkv = fresh_qkv(seq_len=seq_len, num_heads=2, qkv_dim=3 * 8).reshape(
                seq_len, -1
            )
            attn(qkv, layer_idx=layer)
        check(attn)
        attn.reset(to_position=11)
        check(attn)


@pytest.mark.parametrize(
    "is_causal, qph, q_len, kv_len",
    [
        (True, 1, 5, 5),
        (True, 2, 5, 5),
        (True, 1, 3, 15),
        (True, 4, 8, 15),
        (False, 1, 5, 5),
        (False, 2, 5, 5),
        (False, 2, 2, 8),
    ],
)
def test_compare_attention(is_causal: bool, qph: int, q_len: int, kv_len: int):
    head_dim = 4
    q = fresh_qkv(q_len, num_heads=qph).reshape(q_len, 1, qph, head_dim)
    k = fresh_qkv(kv_len)
    v = fresh_qkv(kv_len)

    this_result = cached_attention.causal_attention(
        q.clone(), k.clone(), v.clone(), is_causal=is_causal
    )

    # seq len axis needs to be after heads
    q = q.moveaxis(0, 2)
    k = k.moveaxis(0, 1)
    v = v.moveaxis(0, 1)
    # add batch
    q = q.reshape(1, 1, qph, q_len, head_dim)
    k = k.reshape(1, 1, kv_len, head_dim)
    v = v.reshape(1, 1, kv_len, head_dim)
    other_result = attention.gqa_attention(q, k, v, is_causal=is_causal)
    assert other_result.shape == (1, 1, qph, q_len, head_dim)
    other_result = other_result.moveaxis(3, 0).reshape(q_len, 1, qph, head_dim)

    print(f"{this_result=}")
    print(f"{other_result=}")
    torch.testing.assert_close(this_result, other_result, atol=1e-3, rtol=0.0)


def test_store_tokens():
    mc_attn = cached_attention.MultiCacheAttentionImplementation(
        num_layers=2, num_heads=2, max_len=32, head_dim=5, num_caches=1
    )
    attn = cached_attention.BasicAttention(mc_attn)
    tokens = torch.tensor([1, 2, 3, 4, 5], dtype=torch.int32, device="cuda")
    attn.register_tokens_get_positions(tokens, process_idx=0)
    assert attn._mc_attn._tokens[0].shape == (32,)  # max_len
    assert attn._mc_attn._positions[0][:-1].cpu().numpy().tolist() == [5]


def test_reset_tokens():
    mc_attn = cached_attention.MultiCacheAttentionImplementation(
        num_layers=2, num_heads=2, max_len=32, head_dim=5, num_caches=1
    )
    attn = cached_attention.BasicAttention(mc_attn)
    tokens = torch.tensor([1, 2, 3, 4, 5], dtype=torch.int32, device="cuda")
    attn.register_tokens_get_positions(tokens, process_idx=0)
    assert attn._mc_attn._positions[0][:-1].cpu().numpy().tolist() == [5]

    attn.reset(to_position=3)
    assert attn._mc_attn._tokens[0].shape == (32,)  # max_len
    assert attn._mc_attn._positions[0][:-1].cpu().numpy().tolist() == [3]
    assert torch.all(attn._mc_attn._tokens[0, 3:] == -1)


def _random_cache_idxs(num_tokens: int, num_caches: int) -> torch.Tensor:
    if num_caches == 1:
        return torch.zeros(num_tokens, dtype=torch.int32, device="cuda")

    d = torch.randint(
        low=1,
        high=min(num_tokens, num_caches),
        size=(),
        dtype=torch.int32,
        device="cuda",
    )

    seq_lens = torch.randperm(num_tokens - 1, dtype=torch.int32, device="cuda")
    seq_lens = torch.sort(seq_lens[0 : d - 1]).values
    seq_lens = torch.tensor(
        [0] + seq_lens.cpu().numpy().tolist() + [num_tokens],
        dtype=torch.int32,
        device="cuda",
    )
    seq_lens = (seq_lens[1:] - seq_lens[:-1]).cpu().numpy().tolist()

    idxs = torch.randperm(d, dtype=torch.int32, device="cuda").cpu().numpy().tolist()
    cache_idxs = torch.concat(
        [
            torch.full(size=(s,), fill_value=i, device="cuda")
            for i, s in zip(idxs, seq_lens)
        ]
    )
    return cache_idxs


@pytest.mark.parametrize(
    ",".join(
        [
            "num_layers",
            "num_queries_per_head",
            "num_heads",
            "head_dim",
            "max_len",
            "num_caches",
            "num_tokens",
        ]
    ),
    [
        pytest.param(2, 2, 1, 6, 32, 3, 10, id="dummy"),
        pytest.param(2, 2, 1, 6, 64, 2, 10, id="dev"),
        pytest.param(5, 2, 3, 16, 32, 8, 21, id="med"),
    ],
)
def test_multi_cache_attention_cache_correctness(
    num_layers: int,
    num_queries_per_head: int,
    num_heads: int,
    head_dim: int,
    max_len: int,
    num_caches: int,
    num_tokens: int,
):
    torch.manual_seed(31415)

    attn = cached_attention.MultiCacheAttentionImplementation(
        num_layers=num_layers,
        num_caches=num_caches,
        num_heads=num_heads,
        max_len=max_len,
        head_dim=head_dim,
        queries_per_head=num_queries_per_head,
    )

    def _register_tokens_get_positions(tokens, cache_idxs):
        """Reference implementation."""
        cur_pos = attn._positions[0].cpu().numpy().tolist()
        new_pos = []
        for token, cache_idx in zip(tokens, cache_idxs):
            p_i = cur_pos[cache_idx]
            attn._tokens[cache_idx, p_i] = token
            new_pos.append(p_i)
            cur_pos[cache_idx] += 1
        attn._positions[0] = torch.tensor(cur_pos, dtype=torch.int32, device="cuda")
        return new_pos

    tokens = torch.randint(
        low=0, high=100, size=(num_tokens,), dtype=torch.int32, device="cuda"
    )
    cache_idxs = _random_cache_idxs(num_tokens=num_tokens, num_caches=num_caches)

    out_cache_positions = (
        attn.register_tokens_get_positions(tokens, cache_idxs, process_idx=0)
        .cpu()
        .numpy()
        .tolist()
    )
    out_cache_tokens = attn._tokens.cpu().numpy().tolist()

    # reset
    for cache_idx in cache_idxs:
        attn.reset(cache_idx=int(cache_idx.item()), to_position=0)

    ref_cache_positions = _register_tokens_get_positions(tokens, cache_idxs)
    ref_cache_tokens = attn._tokens.cpu().numpy().tolist()

    assert ref_cache_positions == out_cache_positions
    assert ref_cache_tokens == out_cache_tokens


@pytest.mark.parametrize(
    "num_tokens, num_heads, head_dim, dtype",
    [
        pytest.param(10, 2, 8, torch.float16, id="dummy-fp16"),
        pytest.param(21, 3, 16, torch.float16, id="med-fp16"),
        pytest.param(7, 11, 8, torch.float16, id="odd-fp16"),
        pytest.param(11, 2, 8, torch.bfloat16, id="dummy-bf16"),
        pytest.param(22, 3, 16, torch.bfloat16, id="med-bf16"),
        pytest.param(9, 11, 8, torch.bfloat16, id="odd-bf16"),
    ],
)
@pytest.mark.parametrize("attn_impl", cached_attention.ALL_ATTN_IMPLS_NO_FA3)
def test_cache_attention_correctness(
    num_heads: int,
    head_dim: int,
    num_tokens: int,
    dtype: torch.dtype,
    attn_impl: cached_attention.AttentionImpl,
):
    torch.manual_seed(31415)
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)

    q = torch.empty(num_tokens, num_heads, head_dim, dtype=dtype, device="cuda")
    k = torch.empty(num_tokens, num_heads, head_dim, dtype=dtype, device="cuda")
    v = torch.empty(num_tokens, num_heads, head_dim, dtype=dtype, device="cuda")

    q.uniform_(-0.1, 0.1)
    k.uniform_(-0.1, 0.1)
    v.uniform_(-0.1, 0.1)

    ref = (
        F.scaled_dot_product_attention(
            query=q.transpose(0, 1).contiguous(),
            key=k.transpose(0, 1).contiguous(),
            value=v.transpose(0, 1).contiguous(),
            is_causal=True,
        )
        .transpose(0, 1)
        .reshape(num_tokens, -1)
        .contiguous()
    )

    mc_attn = cached_attention.MultiCacheAttentionImplementation(
        num_layers=1,
        num_heads=num_heads,
        max_len=num_tokens + 1,
        head_dim=head_dim,
        num_caches=1,
        dtype=dtype,
        attention_impl=attn_impl,
    )
    attn = cached_attention.BasicAttention(mc_attn)
    attn.register_tokens_get_positions(
        torch.arange(num_tokens, dtype=torch.int32, device="cuda"), process_idx=0
    )

    qkv = torch.concat([q, k, v], dim=-1).reshape(num_tokens, -1).contiguous()
    out = attn(qkv=qkv, layer_idx=0)

    torch.testing.assert_close(out, ref, atol=5e-4, rtol=1e-5)


def _register_tokens_get_positions(
    attn: cached_attention.MultiCacheAttention,
    tokens: torch.Tensor,
    cache_idxs: torch.Tensor,
) -> torch.Tensor:
    """Reference implementation."""
    cur_pos = attn._positions[0].cpu().numpy().tolist()
    new_pos = []
    for token, cache_idx in zip(tokens, cache_idxs):
        if cache_idx == attn.padding_cache_idx:
            new_pos.append(attn.padding_cache_pos)
        else:
            p_i = cur_pos[cache_idx]
            attn._tokens[cache_idx, p_i] = token
            new_pos.append(p_i)
            cur_pos[cache_idx] += 1
    attn._positions[0] = torch.tensor(cur_pos, dtype=torch.int32, device="cuda")
    return new_pos


@pytest.mark.parametrize(
    "num_caches,tokens,cache_idxs",
    [
        pytest.param(1, [120], [0], id="dummy"),
        pytest.param(2, [11, 81, 8, 41, 13], [1, 1, 0, 0, 2], id="dev"),
        pytest.param(8, [11, 81, 8, 41, 13, 18, 19], [1, 1, 2, 0, 0, 8, 8], id="xl"),
    ],
)
def test_register_tokens_get_positions_padding(
    num_caches: int,
    tokens: list[int],
    cache_idxs: list[int],
):
    tokens_tensor = torch.tensor(tokens, dtype=torch.int32, device="cuda")
    cache_idxs_tensor = torch.tensor(cache_idxs, dtype=torch.int32, device="cuda")

    attn_out = cached_attention.MultiCacheAttentionImplementation(
        num_layers=1,
        num_caches=num_caches,
        num_heads=1,
        max_len=128,
        head_dim=16,
        queries_per_head=1,
    )
    out_cache_positions = (
        attn_out.register_tokens_get_positions(
            tokens_tensor, cache_idxs_tensor, process_idx=0
        )
        .cpu()
        .numpy()
        .tolist()
    )
    out_cache_tokens = attn_out._tokens.cpu().numpy().tolist()

    attn_ref = cached_attention.MultiCacheAttentionImplementation(
        num_layers=1,
        num_caches=num_caches,
        num_heads=1,
        max_len=128,
        head_dim=16,
        queries_per_head=1,
    )
    ref_cache_positions = _register_tokens_get_positions(
        attn_ref, tokens_tensor, cache_idxs_tensor
    )
    ref_cache_tokens = attn_ref._tokens.cpu().numpy().tolist()

    assert ref_cache_positions == out_cache_positions
    assert ref_cache_tokens == out_cache_tokens


@pytest.mark.parametrize(
    ",".join(
        [
            "num_queries_per_head",
            "num_heads",
            "head_dim",
            "max_len",
            "num_caches",
            "num_tokens",
            "num_paddings",
            "num_repeats",
        ]
    ),
    [
        pytest.param(2, 1, 6, 32, 1, 10, 3, 1, id="single"),
        pytest.param(2, 1, 6, 32, 3, 10, 3, 1, id="dummy"),
        pytest.param(2, 1, 6, 64, 2, 10, 2, 1, id="dev"),
        pytest.param(2, 3, 16, 32, 8, 21, 17, 1, id="med"),
        pytest.param(1, 3, 16, 32, 8, 5, 4, 3, id="all_but_one"),
        pytest.param(1, 3, 16, 50, 8, 21, 20, 2, id="twice"),
    ],
)
def test_multi_cache_attention_cache_correctness_with_paddings(
    num_queries_per_head: int,
    num_heads: int,
    head_dim: int,
    max_len: int,
    num_caches: int,
    num_tokens: int,
    num_paddings: int,
    num_repeats: int,
):
    torch.manual_seed(31415)
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)

    print()

    attn_1 = cached_attention.MultiCacheAttentionImplementation(
        num_layers=1,
        num_caches=num_caches,
        num_heads=num_heads,
        max_len=max_len,
        head_dim=head_dim,
        queries_per_head=num_queries_per_head,
    )

    attn_2 = cached_attention.MultiCacheAttentionImplementation(
        num_layers=1,
        num_caches=num_caches,
        num_heads=num_heads,
        max_len=max_len,
        head_dim=head_dim,
        queries_per_head=num_queries_per_head,
    )

    tokens = torch.randint(
        low=0, high=100, size=(num_tokens,), dtype=torch.int32, device="cuda"
    )
    cache_idxs = _random_cache_idxs(num_tokens=num_tokens, num_caches=num_caches).to(
        torch.int32
    )
    cache_idxs[-num_paddings:].fill_(attn_1.padding_cache_idx)

    qkv = torch.randn(
        num_repeats,
        num_tokens,
        (num_queries_per_head + 2) * num_heads * head_dim,
        dtype=torch.float16,
        device="cuda",
    )

    # register first, ignoring all paddings
    out_2 = []
    for i in range(num_repeats):
        cache_positions_2 = attn_2.register_tokens_get_positions(
            tokens[:-num_paddings], cache_idxs[:-num_paddings], process_idx=0
        )
        tokens_2 = attn_2._tokens.cpu().numpy().tolist()
        positions_2 = attn_2._positions[0][:-1][0].cpu().numpy().tolist()
        out_2.append(
            attn_2(
                qkv=qkv[i][:-num_paddings],
                cache_idxs=cache_idxs[:-num_paddings],
                cache_pos=cache_positions_2,
                layer_idx=0,
            )
        )

    print("=" * 160)

    # register again, now with paddings
    out_1 = []
    for i in range(num_repeats):
        cache_positions_1 = attn_1.register_tokens_get_positions(
            tokens, cache_idxs, process_idx=0
        )
        tokens_1 = attn_1._tokens.cpu().numpy().tolist()
        positions_1 = attn_1._positions[0][0].cpu().numpy().tolist()
        out_1.append(
            attn_1(
                qkv=qkv[i],
                cache_idxs=cache_idxs,
                cache_pos=cache_positions_1,
                layer_idx=0,
            )[:-num_paddings]
        )

    for o_1, o_2 in zip(out_1, out_2):
        torch.testing.assert_close(o_1, o_2)

    assert torch.all(
        cache_positions_1.masked_select(cache_idxs == attn_1.padding_cache_idx)
        == attn_1.padding_cache_pos
    )
    assert (
        cache_positions_1.cpu().numpy().tolist()[:-num_paddings]
        == cache_positions_2.cpu().numpy().tolist()
    )
    assert positions_1 == positions_2

    assert tokens_1 == tokens_2


@pytest.mark.parametrize(
    "num_tokens, num_heads, head_dim, start_pos",
    [
        pytest.param(10, 2, 8, 0, id="dummy"),
        pytest.param(21, 3, 16, 0, id="med"),
        pytest.param(7, 11, 8, 0, id="odd"),
        pytest.param(10, 2, 8, 1, id="start_pos"),
        pytest.param(10, 2, 8, 3, id="longer_start_pos"),
        pytest.param(31, 3, 8, 17, id="large"),
    ],
)
def test_match_flash_attention_with_paddings(
    num_heads: int,
    head_dim: int,
    num_tokens: int,
    start_pos: int,
):
    import flash_attn

    print()
    torch.manual_seed(31415)
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)

    dtype = torch.float16

    cache_idxs = torch.zeros(num_tokens, dtype=torch.int32, device="cuda")
    cache_pos = torch.arange(num_tokens, dtype=torch.int32, device="cuda") + start_pos

    q = torch.randn(num_tokens, 1, num_heads, head_dim, dtype=dtype, device="cuda")
    k_cache = torch.randn(1, 128, num_heads, head_dim, dtype=dtype, device="cuda")
    v_cache = torch.randn(1, 128, num_heads, head_dim, dtype=dtype, device="cuda")

    out = flash_attn.flash_attn_with_kvcache(
        q=q,
        k_cache=k_cache,
        v_cache=v_cache,
        k=None,
        v=None,
        cache_seqlens=cache_pos + 1,
        cache_batch_idx=cache_idxs,
        softmax_scale=None,
        causal=True,
    )

    dummy = torch.zeros(start_pos, 1, num_heads, head_dim, dtype=dtype, device="cuda")
    q_ = torch.concat([dummy, q], dim=0).contiguous() if start_pos else q.clone()
    q_ = q_.squeeze_(1).transpose(0, 1).contiguous()
    k_ = k_cache[0, : start_pos + num_tokens, :, :].transpose(0, 1).contiguous()
    v_ = v_cache[0, : start_pos + num_tokens, :, :].transpose(0, 1).contiguous()
    ref = (
        F.scaled_dot_product_attention(query=q_, key=k_, value=v_, is_causal=True)
        .transpose(0, 1)
        .unsqueeze_(1)
        .contiguous()
    )[start_pos:]

    torch.testing.assert_close(out, ref, atol=1e-3, rtol=1e-3)
