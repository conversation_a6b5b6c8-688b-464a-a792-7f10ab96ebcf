load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "convert_to_v2",
    srcs = ["convert_to_v2.py"],
    deps = [
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/checkpoints:save_load",
        "//base/fastforward/starcoder:fwd_starcoder",
        requirement("torch"),
    ],
)

py_binary(
    name = "shards",
    srcs = ["shards.py"],
    deps = [
        "//base/fastforward/checkpoints:save_load",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
    ],
)
