r"""Convert checkpoints to v2.

Can read checkpoints in v1 format and in DeepSpeed format and in pytorch format. This script reads the weights
as a dictionary mapping strings to torch.Tensors.

Example usage:

bazel run //base/fastforward/checkpoints/migrations:convert_to_v2 -- \
    -i /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/ \
    -o /mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000_v2 -v

"""

import argparse
import logging
from pathlib import Path

import torch

from base.fastforward import fwd, fwd_utils
from base.fastforward.checkpoints import save_load
from base.fastforward.starcoder import fwd_starcoder


def load_deepspeed_weights(path: str) -> dict[str, torch.Tensor]:
    # Check to see if path passed in is a filename
    if Path(path).exists() and Path(path).is_file():
        if path.endswith(".pth"):
            logging.info(
                f"Trying to load checkpoint as monolithic starcoder FP8 checkpoint from {path}."
            )
            return torch.load(path, map_location="cpu")

    logging.info(f"Trying to load pipeline format of Starcoder checkpoint from {path}.")
    model_spec = fwd_utils.get_model_spec_from_neox_checkpoint(path)
    success_at_load = True
    sd = {}
    try:
        sd = fwd_starcoder.get_state_dict_from_pipeline_files(
            load_dir=Path(path),
            num_transformer_layers=model_spec.num_layers,
            output_type=fwd.OutputTensorType.VOCAB_LOGITS,
            target_device="cpu",
        )
    except ValueError as e:
        logging.error(
            f"Failed to load checkpoint: {e}. Trying to load as embedding model instead."
        )
        success_at_load = False
    if not success_at_load:
        sd = fwd_starcoder.get_state_dict_from_pipeline_files(
            load_dir=Path(path),
            num_transformer_layers=model_spec.num_layers,
            output_type=fwd.OutputTensorType.EMBEDDING,
            target_device="cpu",
        )

    return sd


def load_weights(path: str) -> dict[str, torch.Tensor]:
    ckpt_version = save_load.try_get_version(path)
    if ckpt_version == "2":
        raise ValueError(f"Checkpoint at {path} is already v2.")
    elif ckpt_version == "1":
        return save_load.load_weights(path=path)
    elif ckpt_version is None:
        weights = load_deepspeed_weights(path)
    else:
        raise ValueError(f"Unknown checkpoint version: {ckpt_version}")
    return weights


def migrate_weights(old_path: str, new_path: str, batch_size: int = 128) -> str:
    logging.info(f">>> Migrating {old_path} to {new_path}")
    weights = load_weights(path=old_path)

    # Batch names into groups of `batch_size`
    batched_names = [
        names
        for i in range(0, len(weights), batch_size)
        for names in [list(weights.keys())[i : i + batch_size]]
    ]

    count = 0
    manifest = None
    for names in batched_names:
        manifest = save_load.save_weights(
            path=new_path,
            weights={name: weights[name] for name in names},
            incremental=True,
        )
        # Triggers garbage collection
        for name in names:
            del weights[name]

        count += len(names)
        logging.info(f"Saved {count} weights. {len(weights)} remaining.")
    assert manifest is not None
    logging.info(
        f">>> Finished migrating {old_path} to {new_path}; manifest sha256: {manifest.manifest_sha256.hex()}"
    )
    return manifest.manifest_sha256.hex()


def validate_new(new_path: str, checksum: str):
    logging.info(f">>> Validating {new_path}")
    weights = save_load.load_weights(path=new_path, target_sha256=checksum)
    logging.info(f">>> Finished validating {new_path}")
    for name in list(weights.keys()):
        del weights[name]


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        "-i",
        type=str,
        help="The old checkpoint path. Should be a directory.",
    )
    parser.add_argument(
        "--output",
        "-o",
        type=str,
        help="The new checkpoint path. . Should be a directory.",
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="If set, will print intermediate outputs.",
        default=False,
    )

    args = parser.parse_args()

    # Set the logging levels from parser
    level = logging.DEBUG if args.verbose else logging.WARNING
    logging.getLogger().setLevel(level)

    checksum = migrate_weights(args.input, args.output)
    validate_new(args.output, checksum)
