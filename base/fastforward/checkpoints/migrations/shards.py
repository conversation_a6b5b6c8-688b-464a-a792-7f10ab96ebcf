import argparse
import functools
import logging
import shutil
import sys
from itertools import islice
from pathlib import Path
from typing import Callable, Iterable, <PERSON>ple

import torch

from base.fastforward.checkpoints import save_load
from base.fastforward.checkpoints.impl import sharding, save_load_v2, manifest

from base.fastforward.llama import model_specs as llama_model_specs, fwd_llama
from base.fastforward.parallel import ParallelConfig


def prepare_empty_dir(path: Path):
    if not path.exists():
        path.mkdir(parents=True)

    if path.is_dir():
        if len(list(islice(path.iterdir(), 1))) == 0:
            return
        elif (
            input(
                f'{path} is a non-empty directory. Enter "delete" to permamently delete its contents: '
            )
            == "delete"
        ):
            shutil.rmtree(path)
            path.mkdir()
            return
    elif (
        input(
            f'{path} already exists is not a directory. Enter "delete" to permanently delete: '
        )
        == "delete"
    ):
        path.unlink()
        path.mkdir()
        return
    logging.info("Operation cancelled")
    sys.exit(1)


def shortest_unique_suffixes(a: Path, b: Path) -> Tuple[str, str]:
    """Attempt to shorten two lengthy paths to the suffixes necessary for differentiation,
    for purposes of pretty-printing.

    e.g.
    shortest_unique_suffixes(Path("/path/to/checkpoints/modelA"), Path("/path/to/checkpoints/modelA-4shard"))
    returns ("modelA", "modelA-4shard")
    """
    n = -1
    try:
        while a.parts[n] == b.parts[n]:
            n -= 1
    except IndexError:
        return str(a), str(b)
    return str(Path(*a.parts[n:])), str(Path(*b.parts[n:]))


def batched(iterable: Iterable[str], count: int) -> Iterable[tuple[str, ...]]:
    it = iter(iterable)
    while batch := tuple(islice(it, count)):
        yield batch


def convert(
    src: Path, dst: Path, num_layers: int, shard_args: sharding.ShardSaveArgsMap
):
    prepare_empty_dir(dst)

    pretty_paths = " to ".join(map(str, shortest_unique_suffixes(src, dst)))

    src_manifest = manifest.CkptManifest.load_from_path(src)
    weight_keys = [
        k
        for (k, v) in src_manifest.children.items()
        if v.type == manifest.ManifestType.DIRECTORY
    ]

    logging.info(f"Migrating {pretty_paths}")
    dst_manifest = None
    for batch in batched(weight_keys, 100):
        weights = save_load.load_weights(
            path=src, require_patterns=batch, target_sha256="", override_sha_check=True
        )
        dst_manifest = save_load_v2.save_weights(
            path=dst, weights=weights, incremental=True, name_to_shard_args=shard_args
        )
        logging.info(f"Finished batch of {len(weights)} weights")
        del weights
    assert dst_manifest is not None
    logging.info(f"Finished migrating {pretty_paths}")
    logging.info(f"Manifest sha256 {dst_manifest.manifest_sha256.hex()}")


def validate(
    src: Path,
    dst: Path,
    num_layers: int,
    shard_args_gen: Callable[[int, ParallelConfig], sharding.ShardLoadArgsMap],
    procs: list[int],
):
    pretty_paths = " and ".join(map(str, shortest_unique_suffixes(src, dst)))

    sm = manifest.CkptManifest.load_from_path(src)
    dm = manifest.CkptManifest.load_from_path(dst)
    diff = set(sm.children.keys()).symmetric_difference(dm.children.keys())
    if diff:
        logging.error(f"weight keys differ between {pretty_paths}")
        logging.error(f"{diff}")
        sys.exit(1)
    logging.info(f"{pretty_paths} have same set of weight names")

    weight_keys = [
        k for (k, v) in sm.children.items() if v.type == manifest.ManifestType.DIRECTORY
    ]
    batches = list(batched(weight_keys, 100))

    for proc_cnt in procs:
        logging.info(
            f"Validating equivalence of {pretty_paths} when loaded for {proc_cnt=}"
        )
        parallel_config = ParallelConfig.from_legacy_config(proc_cnt, False)
        for proc_idx in range(proc_cnt):
            shard_args = (
                shard_args_gen(proc_idx, parallel_config) if proc_cnt > 1 else {}
            )
            for batch in batches:
                w1 = save_load.load_weights(
                    src,
                    require_patterns=batch,
                    shard_load_args=shard_args,
                    target_sha256=sm.manifest_sha256.hex(),
                )
                w2 = save_load.load_weights(
                    dst,
                    require_patterns=batch,
                    shard_load_args=shard_args,
                    target_sha256=dm.manifest_sha256.hex(),
                )
                if set(w1.keys()) != set(w2.keys()):
                    logging.error(f"weight keys differ between {pretty_paths}")
                    logging.error(
                        f"{set(w1.keys()).symmetric_difference(set(w2.keys()))}"
                    )
                    sys.exit(1)
                for k, v in w1.items():
                    if not torch.equal(v, w2[k]):
                        logging.error(
                            f"weight {k} ({proc_idx=}, {proc_cnt=}) differs between {pretty_paths}"
                        )
                        sys.exit(1)
                logging.info(f"Batch of {len(w1)} weights match")
                del w1
                del w2
        logging.info(f"Finished validating {pretty_paths} when loaded for {proc_cnt=}")
        logging.info(
            f"Validated with sha256 src={sm.manifest_sha256.hex()}, dst={dm.manifest_sha256.hex()}"
        )


def llama_model(
    name: str,
) -> Tuple[
    llama_model_specs.LlamaModelSpec,
    Callable[[int, ParallelConfig], sharding.ShardLoadArgsMap],
]:
    ms = llama_model_specs.get_llama_model_spec(name)
    return ms, functools.partial(fwd_llama.shard_args_by_name, ms)


model_opts = {
    "llama3-70b": llama_model("llama3-70b"),
}


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--verbose", "-v", action="store_true")
    subparsers = parser.add_subparsers(dest="subparser_name")

    convert_ = subparsers.add_parser("convert")
    validate_ = subparsers.add_parser("validate")
    for sp in [convert_, validate_]:
        sp.add_argument(
            "--src", type=Path, required=True, help="Path to old checkpoint"
        )
        sp.add_argument(
            "--dst",
            type=Path,
            required=True,
            help="Path to new checkpoint to be created or validated",
        )
        sp.add_argument(
            "--model",
            choices=model_opts.keys(),
            required=True,
            help="Model name to determine sharding requirements",
        )
    convert_.add_argument(
        "--max-processes",
        type=int,
        default=8,
        help="How many processes the weights should be sharded to support",
    )
    convert_.add_argument(
        "--validate-processes",
        type=int,
        nargs="+",
        default=[1, 2, 4, 8],
        help="List of process counts to validate against",
    )
    validate_.add_argument(
        "--processes",
        type=int,
        nargs="+",
        default=[1, 2, 4, 8],
        help="List of process counts to validate against",
    )

    args = parser.parse_args()
    logging.basicConfig(level=logging.DEBUG if args.verbose else logging.INFO)

    ms, shard_args_gen = model_opts[args.model]
    if args.subparser_name == "convert":
        save_args = {
            k: sharding.ShardSaveArgs.from_load_args(v)
            for (k, v) in shard_args_gen(0, args.max_processes).items()
        }
        convert(args.src, args.dst, ms.num_layers, save_args)
        validate(
            args.src,
            args.dst,
            ms.num_layers,
            shard_args_gen,
            procs=args.validate_processes,
        )
    elif args.subparser_name == "validate":
        validate(
            args.src, args.dst, ms.num_layers, shard_args_gen, procs=args.processes
        )
