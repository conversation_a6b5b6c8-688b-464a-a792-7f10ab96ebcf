"""Tests for saving and loading model weights."""

import hashlib

import pytest
import safetensors.torch
import torch

from base.fastforward.checkpoints.impl import save_load_v2, sharding

TEST_DATATYPES = [torch.uint8, torch.float16, torch.float32, torch.bfloat16]


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_validate_weight_manifest(dtype: torch.dtype):
    """Generate a manifest, then validate it to make sure it is intact."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu")

    w1_manifest = save_load_v2._generate_weight_manifest("layer.0.a", w1)
    w1_manifest.update_manifest_sha()
    assert w1_manifest.validate()  # type: ignore

    w1_manifest.get_sub_manifest_at("data.bin").content = b""
    assert not w1_manifest.validate()  # type: ignore


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_validate_manifest_sha(tmp_path, dtype: torch.dtype):
    """Generate a manifest, then validate it to make sure it is intact."""
    torch.manual_seed(31415)

    w1 = {
        "layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu"),
        "layer.0.hello": torch.randint(0, 16, (81, 3), dtype=dtype, device="cpu"),
        "layer.0.world": torch.randint(0, 16, (6, 8), dtype=dtype, device="cpu"),
        "layer.0.foo": torch.randint(0, 16, (7, 1), dtype=dtype, device="cpu"),
        "layer.0.bar": torch.randint(0, 16, (11,), dtype=dtype, device="cpu"),
        "layer.1.a": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.1.def": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.2.b": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
    }
    ckpt_info = save_load_v2.save_weights(tmp_path / "ckpt", w1)
    target_sha = ckpt_info.manifest_sha256.hex()
    save_load_v2.load_weights(tmp_path / "ckpt", dtype=dtype, target_sha256=target_sha)

    invalid_sha = hashlib.sha256(b"this is an invalid hash").hexdigest()
    with pytest.raises(RuntimeError):
        save_load_v2.load_weights(
            tmp_path / "ckpt", dtype=dtype, target_sha256=invalid_sha
        )


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_validate_modified_weight_manifest_fails(dtype: torch.dtype):
    """Generate a manifest, and make sure modifications makes validation fail."""
    torch.manual_seed(31415)

    w1 = torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu")
    w2 = torch.randint(0, 16, (15, 3, 10), dtype=dtype, device="cpu")

    # Modify name
    w1_manifest = save_load_v2._generate_weight_manifest("layer.0.a", w1)
    w1_manifest.update_manifest_sha()
    assert w1_manifest.validate()  # type: ignore

    w1_manifest.name = "AN INVALID NAME"
    assert not w1_manifest.validate()  # type: ignore

    # Modify hash
    w1_manifest = save_load_v2._generate_weight_manifest("layer.0.a", w1)
    w1_manifest.update_manifest_sha()

    assert w1_manifest.validate()  # type: ignore
    w1_manifest.manifest_sha256 = hashlib.sha256(b"this is an invalid hash").digest()
    assert not w1_manifest.validate()  # type: ignore

    # Modify contents
    w1_manifest = save_load_v2._generate_weight_manifest("layer.0.a", w1)
    w1_manifest.update_manifest_sha()

    assert w1_manifest.validate()  # type: ignore
    # Hydrate the manifest incorrectly
    w1_manifest.get_sub_manifest_at("data.bin").content = safetensors.torch.save(
        {"tensor": w2}
    )
    assert not w1_manifest.validate()  # type: ignore


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights. Asserts that after loading, they are the same.

    Primarily tests hydration + hashing logic
    """
    torch.manual_seed(31415)

    w1 = {
        "layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu"),
        "layer.0.b": torch.randint(0, 16, (81, 3), dtype=dtype, device="cpu"),
        "layer.1.b": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
    }
    w1_serialized = safetensors.torch.save(w1)

    save_load_v2.save_weights(tmp_path / "ckpt", w1)
    w2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        skip_checking_checksum=True,
    )
    w2_serialized = safetensors.torch.save(w2)

    assert w1_serialized == w2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_partial(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights. Asserts that after loading, they are the same."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu"),
        "layer.0.hello": torch.randint(0, 16, (81, 3), dtype=dtype, device="cpu"),
        "layer.0.world": torch.randint(0, 16, (6, 8), dtype=dtype, device="cpu"),
        "layer.0.foo": torch.randint(0, 16, (7, 1), dtype=dtype, device="cpu"),
        "layer.0.bar": torch.randint(0, 16, (11,), dtype=dtype, device="cpu"),
        "layer.1.a": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.1.def": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.2.b": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
    }

    save_load_v2.save_weights(tmp_path / "ckpt", weights_1)

    weights_1_serialized = safetensors.torch.save(
        {k: v for k, v in sorted(weights_1.items()) if k.startswith("layer.0")}
    )

    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_filters=(r"^layer\.0",),
        skip_checking_checksum=True,
    )
    weights_2 = {k: v for k, v in sorted(weights_2.items())}
    weights_2_serialized = safetensors.torch.save(weights_2)

    assert weights_1_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_parallel(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights. Asserts that after loading, they are the same."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu"),
        "layer.0.hello": torch.randint(0, 16, (81, 3), dtype=dtype, device="cpu"),
        "layer.0.world": torch.randint(0, 16, (6, 8), dtype=dtype, device="cpu"),
        "layer.0.foo": torch.randint(0, 16, (7, 1), dtype=dtype, device="cpu"),
        "layer.0.bar": torch.randint(0, 16, (11,), dtype=dtype, device="cpu"),
        "layer.1.a": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.1.def": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.2.b": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
    }

    save_load_v2.save_weights(tmp_path / "ckpt", weights_1, max_workers=4)
    weights_1_serialized = safetensors.torch.save(
        {k: v for k, v in sorted(weights_1.items())}
    )

    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        max_workers=4,
        skip_checking_checksum=True,
    )
    weights_2 = {k: v for k, v in sorted(weights_2.items())}
    weights_2_serialized = safetensors.torch.save(weights_2)

    assert weights_1_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_parallel_partial(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights. Asserts that after loading, they are the same."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu"),
        "layer.0.hello": torch.randint(0, 16, (81, 3), dtype=dtype, device="cpu"),
        "layer.0.world": torch.randint(0, 16, (6, 8), dtype=dtype, device="cpu"),
        "layer.0.foo": torch.randint(0, 16, (7, 1), dtype=dtype, device="cpu"),
        "layer.0.bar": torch.randint(0, 16, (11,), dtype=dtype, device="cpu"),
        "layer.1.a": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.1.def": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
        "layer.2.b": torch.randint(0, 16, (12, 4, 5), dtype=dtype, device="cpu"),
    }

    save_load_v2.save_weights(tmp_path / "ckpt", weights_1, max_workers=4)
    weights_1_serialized = safetensors.torch.save(
        {k: v for k, v in sorted(weights_1.items()) if k.startswith("layer.0")}
    )

    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_filters=(r"^layer\.0",),
        max_workers=4,
        skip_checking_checksum=True,
    )
    weights_2 = {k: v for k, v in sorted(weights_2.items())}
    weights_2_serialized = safetensors.torch.save(weights_2)

    assert weights_1_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_one_shard(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights sharded. Asserts that after loading, it is the same as the original."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (64, 64, 64), dtype=dtype, device="cpu"),
    }
    save_load_v2.save_weights(
        tmp_path / "ckpt",
        weights_1,
        name_to_shard_args={"layer.0.a": sharding.ShardSaveArgs(shard_count=8)},
    )

    shard_0 = weights_1["layer.0.a"].chunk(8, dim=0)[0]
    shard_0_serialized = safetensors.torch.save({"layer.0.a": shard_0})

    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_to_shard_args={
            "layer.0.a": sharding.ShardLoadArgs(shard_idxs=(0,), shard_count=8)
        },
        skip_checking_checksum=True,
    )
    weights_2_serialized = safetensors.torch.save(weights_2)
    assert shard_0_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_half_shards(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights sharded. Asserts that after loading, it is the same as the original."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (64, 64, 64), dtype=dtype, device="cpu"),
    }
    save_load_v2.save_weights(
        tmp_path / "ckpt",
        weights_1,
        name_to_shard_args={"layer.0.a": sharding.ShardSaveArgs(shard_count=8)},
    )

    shard_half = weights_1["layer.0.a"].chunk(2, dim=0)[0]
    shard_half_serialized = safetensors.torch.save({"layer.0.a": shard_half})
    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_to_shard_args={
            "layer.0.a": sharding.ShardLoadArgs(shard_idxs=(0, 1, 2, 3), shard_count=8)
        },
        skip_checking_checksum=True,
    )
    weights_2_serialized = safetensors.torch.save(weights_2)
    assert shard_half_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_shards_from_unsharded(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights unsharded. Assert that load can still request sub-shards
    of the weights. This allows model code to push its sharding logic into save_load without worrying
    about the timing of actually publishing pre-sharded weights to checkpoint storage.
    """
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (64, 64, 64), dtype=dtype, device="cpu"),
    }
    save_load_v2.save_weights(
        tmp_path / "ckpt",
        weights_1,
        name_to_shard_args={},  # No sharding at save time
    )

    shard_half = weights_1["layer.0.a"].chunk(2, dim=0)[0]
    shard_half_serialized = safetensors.torch.save({"layer.0.a": shard_half})
    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_to_shard_args={
            "layer.0.a": sharding.ShardLoadArgs(shard_idxs=(0, 1, 2, 3), shard_count=8)
        },
        skip_checking_checksum=True,
    )
    weights_2_serialized = safetensors.torch.save(weights_2)
    assert shard_half_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_all_shards(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights sharded. Asserts that after loading, it is the same as the original."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (64, 64, 64), dtype=dtype, device="cpu"),
    }
    weights_1_serialized = safetensors.torch.save(weights_1)
    save_load_v2.save_weights(
        tmp_path / "ckpt",
        weights_1,
        name_to_shard_args={"layer.0.a": sharding.ShardSaveArgs(shard_count=8)},
    )
    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_to_shard_args={
            "layer.0.a": sharding.ShardLoadArgs(
                shard_idxs=(0, 1, 2, 3, 4, 5, 6, 7), shard_count=8
            )
        },
        skip_checking_checksum=True,
    )
    weights_2_serialized = safetensors.torch.save(weights_2)
    assert weights_1_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_no_specified_shards(tmp_path, dtype: torch.dtype):
    """Save a dict of random weights sharded. Asserts that after loading, it is the same as the original.
    Test sharding along multiple dimensions, as the dimension in which the weights are sharded must be
    serialized in the checkpoint manifest.
    """
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (64, 64, 64), dtype=dtype, device="cpu"),
    }
    weights_1_serialized = safetensors.torch.save(weights_1)
    for dim in [0, 1, 2]:
        save_load_v2.save_weights(
            tmp_path / "ckpt",
            weights_1,
            name_to_shard_args={
                "layer.0.a": sharding.ShardSaveArgs(shard_count=8, split_dim=dim)
            },
        )
        weights_2 = save_load_v2.load_weights(
            tmp_path / "ckpt",
            target_sha256="not checked",
            dtype=dtype,
            skip_checking_checksum=True,
        )
        weights_2_serialized = safetensors.torch.save(weights_2)
        assert weights_1_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_save_load_weights_scaled_shards(tmp_path, dtype: torch.dtype):
    """Save a dict of randintom weights. Asserts that after loading, they are the same."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.randint(0, 16, (64, 64, 64), dtype=dtype, device="cpu"),
    }
    save_load_v2.save_weights(
        tmp_path / "ckpt",
        weights_1,
        name_to_shard_args={"layer.0.a": sharding.ShardSaveArgs(shard_count=8)},
    )

    shard_half = weights_1["layer.0.a"].chunk(2, dim=0)[0]
    shard_half_serialized = safetensors.torch.save({"layer.0.a": shard_half})
    weights_2 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_to_shard_args={
            "layer.0.a": sharding.ShardLoadArgs(shard_idxs=(0, 1), shard_count=4)
        },
        skip_checking_checksum=True,
    )
    weights_2_serialized = safetensors.torch.save(weights_2)
    assert shard_half_serialized == weights_2_serialized

    weights_3 = save_load_v2.load_weights(
        tmp_path / "ckpt",
        target_sha256="not checked",
        dtype=dtype,
        name_to_shard_args={
            "layer.0.a": sharding.ShardLoadArgs(shard_idxs=(0,), shard_count=2)
        },
        skip_checking_checksum=True,
    )
    weights_3_serialized = safetensors.torch.save(weights_3)
    assert shard_half_serialized == weights_3_serialized
