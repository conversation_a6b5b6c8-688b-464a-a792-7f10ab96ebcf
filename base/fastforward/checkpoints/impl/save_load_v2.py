"""Utils to save and load checkpoints."""

import json
import logging
import re
from pathlib import Path
from typing import Callable, Optional, Sequence, Tuple, Union

import safetensors.torch
import torch

from base.fastforward.checkpoints.impl.manifest import (
    CkptManifest,
    ManifestData,
    ManifestType,
)
from base.fastforward.checkpoints.impl.sharding import (
    ShardedTensor,
    ShardLoadArgsMap,
    ShardSaveArgsMap,
    compile_shard_args_selector,
    shard_weights,
    should_exclude_shard,
    unshard_weights,
)

MANIFEST_VERSION = "2"
DEFAULT_WEIGHT_DTYPE = torch.float16
Tensor = torch.Tensor
Device = Union[str, torch.device]

MANIFEST_FILE_NAME = "info.json"
WEIGHT_FILE_NAME = "data.bin"
SHARD_FILE_NAME = "shard_{}_of_{}.bin"

SHARD_FILE_MATCH = "shard_(?P<idx>[0-9]+)_of_(?P<count>[0-9]+).bin"


def _tensor_to_bytes(w: Tensor) -> bytes:
    """Convert a tensor to bytes."""
    return safetensors.torch.save(tensors={"tensor": w.contiguous()})


def _leaf_to_tensor(
    leaf: CkptManifest | None,
    dtype: Optional[torch.dtype] = None,
    device: Optional[Device] = None,
) -> Tensor | None:
    """Convert a leaf manifest to a tensor."""
    if not leaf:
        return None
    w_as_bytes = leaf.content
    # Once the content is loaded, we don't need it anymore
    # This will allow for garbage collection to be performed on the raw bytes.
    leaf.content = None
    if not w_as_bytes:
        return None
    return safetensors.torch.load(w_as_bytes)["tensor"].to(dtype=dtype, device=device)


def _generate_weight_manifest(
    name: str,
    weight: Tensor,
) -> CkptManifest:
    """Generate a weight manifest for an unsharded weight tensor

    weight_name: {
      children: {
        data.bin: { ... }
      }
    }
    """
    return CkptManifest(
        ManifestData(
            manifestVersion=str(MANIFEST_VERSION),
            name=name,
            type=ManifestType.DIRECTORY,
            children={
                WEIGHT_FILE_NAME: ManifestData(
                    manifestVersion=str(MANIFEST_VERSION),
                    name=WEIGHT_FILE_NAME,
                    type=ManifestType.FILE,
                    content=_tensor_to_bytes(weight),
                )
            },
        ),
    )


def _generate_sharded_weight_manifest(
    name: str,
    weight: ShardedTensor,
) -> CkptManifest:
    """Generate a weight manifest for a sharded weight tensor

    weight_name: {
      children: {
        info.json: { How the weight was sharded }
        shard_0_of_<N>.bin: { ... }
        shard_1_of_<N>.bin: { ... }
        ...
      }
    }

    The count will be redundantly stored in the shard file names and the json,
    as this makes it easier to load the needed shards in parallel with the json
    in a single hydration pass.
    """
    count = len(weight.shards)
    children = [
        (SHARD_FILE_NAME.format(i, count), _tensor_to_bytes(weight.shards[i]))  # type: ignore
        for i in range(count)
    ]
    meta = {"split_dim": weight.split_dim, "shard_count": count}
    children.append((MANIFEST_FILE_NAME, json.dumps(meta).encode("utf-8")))

    return CkptManifest(
        ManifestData(
            manifestVersion=str(MANIFEST_VERSION),
            name=name,
            type=ManifestType.DIRECTORY,
            children={
                c[0]: ManifestData(
                    manifestVersion=str(MANIFEST_VERSION),
                    name=c[0],
                    type=ManifestType.FILE,
                    content=c[1],
                )
                for c in children
            },
        ),
    )


def _deserialize_weight_manifest(
    wm: CkptManifest,
    dtype: Optional[torch.dtype] = None,
    device: Optional[Device] = None,
) -> Tensor | ShardedTensor | None:
    """Deserialize a weight manifest into either a full tensor or ShardedTensor"""
    if WEIGHT_FILE_NAME in wm.children:
        full = wm.get_sub_manifest_at(WEIGHT_FILE_NAME)
        return _leaf_to_tensor(full, dtype, device)

    detail = wm.get_sub_manifest_at(MANIFEST_FILE_NAME)
    assert detail is not None
    if detail.content is None:
        # This weight was skipped by name
        return None
    info = json.loads(detail.content)
    shards = [
        _leaf_to_tensor(
            wm.get_sub_manifest_at(SHARD_FILE_NAME.format(i, info["shard_count"])),
            dtype,
            device,
        )
        for i in range(info["shard_count"])
    ]
    return ShardedTensor(split_dim=info["split_dim"], shards=shards)


def save_weights(
    path: Union[str, Path],
    weights: dict[str, Tensor],
    max_workers: int = 16,
    detach_grad: bool = True,
    incremental: bool = False,
    name_to_shard_args: ShardSaveArgsMap = {},
) -> CkptManifest:
    """Save a dictionary of weight tensors to `path`."""
    logging.debug("Saving weights to '%s'...", path)

    if detach_grad:
        weights = {k: v.detach() for k, v in weights.items()}

    ckpt_path = path if isinstance(path, Path) else Path(path)
    ckpt_info_file = ckpt_path / MANIFEST_FILE_NAME
    sharded = shard_weights(weights, name_to_shard_args)

    if incremental and ckpt_info_file.exists():
        ckpt_info = CkptManifest.load_from_path(ckpt_path)
    else:
        ckpt_info = CkptManifest(
            ManifestData(
                manifestVersion=str(MANIFEST_VERSION),
                name=str(ckpt_path),
                type=ManifestType.DIRECTORY,
            ),
            ckpt_path,
        )

    for name, w in sharded.items():
        if isinstance(w, ShardedTensor):
            manifest = _generate_sharded_weight_manifest(name, w)
        else:
            manifest = _generate_weight_manifest(name, w)
        ckpt_info.add_child(manifest)

    ckpt_info.finalize_dehydrate_and_save(
        ckpt_path, worker_count=max_workers, incremental=incremental
    )
    logging.info(
        "Checkpoint saved to '%s', with checkpoint sha256 %s.",
        ckpt_path,
        ckpt_info.manifest_sha256.hex(),
    )
    return ckpt_info


def _get_name_matcher(
    name_filters: Sequence[str] = (),
) -> Callable[[CkptManifest], bool]:
    """Create a matcher to ignore weights that do not match name patterns."""

    def _matcher(m: CkptManifest) -> bool:
        if len(name_filters) == 0:
            return False
        no_matches = all(re.search(p, str(m.relpath)) is None for p in name_filters)
        return no_matches  # Skip hydration if no filters match

    return _matcher


def _get_shard_matcher(
    name_to_shard_args: ShardLoadArgsMap = {},
) -> Callable[[CkptManifest], bool]:
    """Create a matcher to ignore shards that do not match the given requested idxs."""

    selector = compile_shard_args_selector(name_to_shard_args)

    def _matcher(m: CkptManifest) -> bool:
        if m.parent is None:
            return False
        args = selector(m.parent.name)
        if args is None:
            return False
        match = re.search(SHARD_FILE_MATCH, m.name)
        if match is None:
            return False
        return should_exclude_shard(
            int(match.group("idx")), int(match.group("count")), args
        )

    return _matcher


def compose_matchers(
    *args: Callable[[CkptManifest], bool],
) -> Callable[[CkptManifest], bool]:
    """Compose multiple matchers into a single matcher."""

    return lambda m: any(f(m) for f in args)


def load_weights(
    path: Union[str, Path],
    target_sha256: str,
    dtype: Optional[torch.dtype] = None,
    device: Device = "cpu",
    max_workers: int = 16,
    name_filters: Sequence[str] = (),
    name_to_shard_args: ShardLoadArgsMap = {},
    skip_checking_checksum: bool = False,
) -> dict[str, Tensor]:
    """Load a dictionary of weight tensors from `path`. Verify the loaded SHA-256.

    Args:
        path: where to save the weights.
        target_sha256: the SHA-256 of the checkpoint matches the given value. Must match
            the SHA-256 of the checkpoint unless skip_checking_checksum is True.
        dtype: the target dtype for the weights. if `None, the weights' dtypes are
            kept at whatever the checkpoint stores.
        device: the target device for the weights.
        max_workers: maximum degree of parallelism.
        name_filters: if given, will only load the weights whose names match
            at least one of the patterns in `name_filters`. matching is done via
            `re.match(pattern, name_filters) is not None`.
        name_to_shard_args: if given, will shard the weights according to the
            given shard args.
        skip_checking_checksum: if True, will skip the checksum verification.
    """
    logging.debug("Loading weights from '%s' to device '%s'...", path, device)

    # Create a matcher to skip hydration
    skip_hydration = compose_matchers(
        _get_name_matcher(name_filters),
        _get_shard_matcher(name_to_shard_args),
    )

    # Validate the manifest + weights on disk
    ckpt_info = CkptManifest.load_from_path(path)
    if target_sha256 != ckpt_info.manifest_sha256.hex():
        if skip_checking_checksum:
            logging.warning(
                "Skipping checkpoint SHA-256 verification. This is strongly discouraged "
                "for production use cases. Target SHA is %s, actual SHA is %s.",
                target_sha256,
                ckpt_info.manifest_sha256.hex(),
            )
        else:
            raise RuntimeError(
                f"SHA-256 of the checkpoint does not match the expected one: "
                f"expected {target_sha256}, got {ckpt_info.manifest_sha256.hex()}"
            )
    if not ckpt_info.hydrate_and_validate(
        path, worker_count=max_workers, skip_hydration=skip_hydration
    ):
        raise RuntimeError("ckpt_hash after loading does not match the saved one.")

    logging.info("Checkpoint at '%s' has expected hash.", path)
    weights = {}
    for c in ckpt_info.children.values():
        weight = _deserialize_weight_manifest(c, dtype, device)
        if weight is not None:
            weights[c.name] = weight
    return unshard_weights(weights, name_to_shard_args, discard_input_tensors=True)
