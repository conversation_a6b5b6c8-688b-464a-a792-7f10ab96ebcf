import logging
import re
import torch

from typing import Callable, NamedTuple, Optional, Sequence, Tuple, TypeVar

DEFAULT_SPLIT_DIM = 0


class ShardSaveArgs(NamedTuple):
    """Arguments for saving sharded weights."""

    shard_count: int
    split_dim: int = DEFAULT_SPLIT_DIM  # The dimension to split the shard over

    @staticmethod
    def from_load_args(load_args: "ShardLoadArgs") -> "ShardSaveArgs":
        return ShardSaveArgs(
            shard_count=load_args.shard_count,
            split_dim=load_args.split_dim,
        )


class ShardLoadArgs(NamedTuple):
    """Arguments for loading sharded weights."""

    shard_idxs: Sequence[int]
    shard_count: int  # Total number of shards
    split_dim: int = DEFAULT_SPLIT_DIM  # The dimension the shard is split over


class ShardedTensor(NamedTuple):
    """A set of shards for a given weight."""

    split_dim: int
    shards: Sequence[torch.Tensor | None]

    def __getitem__(self, key):
        return self.shards[key]


T = TypeVar("T")
"""
A Shard(Save|Load)ArgsMap is how a model author specifies their desired sharding
behavior across the various weights within the model. Keys in the map can be either
str or a compiled regular expression pattern, with behavior as follows:

str : applies to a weight if the weight name matches exactly
re : applies to a weight if the pattern matches via re.search

String key matches take precedence over regular expression matches, and a weight
matching two regular expressions will return the first match. Up to clients to
write unambiguous patterns.
"""
ShardArgsMap = dict[(str | re.Pattern), T]
ShardSaveArgsMap = ShardArgsMap[ShardSaveArgs]
ShardLoadArgsMap = ShardArgsMap[ShardLoadArgs]


def compile_shard_args_selector(map: ShardArgsMap[T]) -> Callable[[str], Optional[T]]:
    """Return a callable that implements the key -> value mapping described above
    for ShardArgsMap.

    Specifically:
    Passing key K to the callable will return
    - map[K] if the key is in the map, or
    - map[P] for some P in the map where P is an re.Pattern and P.search(K) is not None, or
    - None, if neither of the above are satisfied
    """
    if not map:
        return lambda name: None

    patterns = [(k, v) for k, v in map.items() if isinstance(k, re.Pattern)]
    if not patterns:
        return map.get

    def _impl(name: str):
        if name in map:
            return map[name]
        for pattern, args in patterns:
            if pattern.search(name):
                return args
        return None

    return _impl


def _is_increasing(shard_idxs: Sequence[int]) -> bool:
    """Return whether sequence of shard indices is strictly increasing"""
    return all(a < b for (a, b) in zip(shard_idxs, shard_idxs[1:]))


def verify_is_increasing(load_args: ShardLoadArgs):
    """Raise error if sequence is not strictly increasing"""
    if not _is_increasing(load_args.shard_idxs):
        raise ValueError(
            "Requested shard indices must be increasing, but received {}".format(
                load_args.shard_idxs
            )
        )


def should_exclude_shard(
    shard_idx: int, shard_count: int, shard_args: ShardLoadArgs
) -> bool:
    """Check if a shard should be excluded. The ShardLoadArgs may indicate a different shard count
    for the weight than how it was actually sharded, and this function handles that scaling.
    """
    verify_is_increasing(shard_args)

    # TODO?: Slightly over-restrictive: implies that if a model wants to load to N processes,
    # we must either persist some multiple of N shards, or not shard at all.
    # We could still benefit from sharding to N/2 shards, performing the final shard in-memory,
    # rather than loading the entire weight tensor.
    if shard_count % shard_args.shard_count != 0:
        raise ValueError(
            "Requested load shard from a total of {} shards but disk found {}".format(
                shard_args.shard_count,
                shard_count,
            )
        )
    shard_scale_factor = shard_count // shard_args.shard_count
    return (shard_idx // shard_scale_factor) not in shard_args.shard_idxs


def scale_required_shards(
    requested_count: int, requested_idxs: Sequence[int], actual_count: int
) -> Sequence[int]:
    if actual_count == requested_count:
        return requested_idxs
    if actual_count % requested_count != 0:
        raise ValueError(
            f"Requested load shard from a total of {requested_count} shards but disk found {actual_count}"
        )
    shard_scale_factor = actual_count // requested_count
    actual_idxs = []
    for idx in requested_idxs:
        actual_idxs.extend(
            [idx * shard_scale_factor + i for i in range(shard_scale_factor)]
        )
    return actual_idxs


def shard_single_weight(
    weight: torch.Tensor,
    args: ShardSaveArgs,
) -> ShardedTensor:
    """Shard a single weight, normalizing the dimension to non-negative index into weight.shape"""
    shard_count, split_dim = args
    if weight.shape[split_dim] % shard_count != 0:
        raise ValueError(
            "Cannot shard a tensor of shape {} along dimension {} into {} shards".format(
                weight.shape, split_dim, shard_count
            )
        )
    normalized_split_dim = split_dim % len(weight.shape)
    return ShardedTensor(
        split_dim=normalized_split_dim, shards=weight.chunk(shard_count, dim=split_dim)
    )


def shard_weights(
    weights: dict[str, torch.Tensor],
    name_to_shard_args: ShardSaveArgsMap,
) -> dict[str, torch.Tensor | ShardedTensor]:
    """Shard the weights according to the given shard args.

    Args:
        weights: a dictionary of weights.
        name_to_shard_args: a dictionary of weight name => shard args.

    Returns:
        A dictionary of weights, with requested weights sharded.
    """
    new_weights = {}

    selector = compile_shard_args_selector(name_to_shard_args)
    for name, w in weights.items():
        args = selector(name)
        if args is None:
            new_weights[name] = w
            continue
        new_weights[name] = shard_single_weight(w, args)
    return new_weights


def shard_weights_using_load_args(
    weights: dict[str, torch.Tensor],
    name_to_shard_args: ShardLoadArgsMap,
) -> dict[str, torch.Tensor | ShardedTensor]:
    conv = {k: ShardSaveArgs.from_load_args(v) for (k, v) in name_to_shard_args.items()}
    return shard_weights(weights, conv)


def unshard_weights(
    weights: dict[str, torch.Tensor | ShardedTensor],
    name_to_shard_args: ShardLoadArgsMap,
    discard_input_tensors=False,
) -> dict[str, torch.Tensor]:
    """Unshard the weights according to the given shard args.

    If discard_input_tensors=True, then this method will leave the input dictionary
    empty, allowing any unreferenced tensors in that dictionary to be garbage collected.
    """
    if not discard_input_tensors:
        weights = dict(weights)

    for args in name_to_shard_args.values():
        verify_is_increasing(args)
    selector = compile_shard_args_selector(name_to_shard_args)

    expected_keys = set(weights.keys())
    new_weights = {}

    do_warn = True
    while weights:
        w_name, w_repr = weights.popitem()
        shard_args = selector(w_name)

        if isinstance(w_repr, torch.Tensor):
            if shard_args is None:
                new_weights[w_name] = w_repr
                continue
            if do_warn:
                logging.warning(
                    f"Requested to unshard weight {w_name} but it is not sharded. "
                    f"Pre-shard the weight to {shard_args.shard_count} to avoid unnecessary "
                    "loading from checkpoint storage"
                )
                do_warn = False
            w_repr = shard_single_weight(
                w_repr, ShardSaveArgs.from_load_args(shard_args)
            )

        assert isinstance(w_repr, ShardedTensor)
        if shard_args is None:
            # Caller wants the entire weight tensor; concat all shards
            required_shards = tuple(range(len(w_repr.shards)))
        else:
            ndims = set(shard.ndim for shard in w_repr.shards if shard is not None)
            assert len(ndims) == 1, f"Shards of {w_name} have different ndim: {ndims}"
            ndim = ndims.pop()

            # Caller wants a subset of shards; handle any scaling required
            if (shard_args.split_dim - w_repr.split_dim) % ndim != 0:
                raise ValueError(
                    f"Requested to unshard weight {w_name} with split_dim {shard_args.split_dim} "
                    f"but it is sharded with split_dim {w_repr.split_dim} {ndim=}"
                )
            required_shards = scale_required_shards(
                shard_args.shard_count, shard_args.shard_idxs, len(w_repr.shards)
            )

        shards = []
        for i in required_shards:
            if w_repr[i] is None:
                raise ValueError(
                    f"Requested shard {w_name} {i}/{len(w_repr.shards)} but it is not present"
                )
            shards.append(w_repr[i])

        new_weights[w_name] = torch.cat(shards, dim=w_repr.split_dim)
        # Discard the old representation to give up the memory ASAP
        del shards
        del w_repr
        torch.cuda.empty_cache()
    assert set(new_weights.keys()) == expected_keys
    return new_weights
