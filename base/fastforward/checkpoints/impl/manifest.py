"""This file describes a format for saving and loading weights."""

import hashlib
import json
import os
from concurrent import futures
from dataclasses import dataclass, field
from enum import IntEnum
from google.cloud import storage
from pathlib import Path, PurePath
from typing import Any, Callable, Iterable, Mapping, Optional, Sequence, Union

from dataclasses_json import dataclass_json

MANIFEST_FILE_NAME = "info.json"
MANIFEST_VERSION = "1.0.0"


def sha_256_items(*items: bytes) -> bytes:
    """Hash a list of items into a single SHA256 digest."""
    hash_obj = hashlib.sha256()
    for item in items:
        hash_obj.update(hashlib.sha256(item).digest())
    return hash_obj.digest()


class ManifestType(IntEnum):
    """The type of a manifest."""

    DIRECTORY = 1
    FILE = 2


@dataclass
class GCSPath:
    bucket: storage.Bucket
    path: PurePath

    def __truediv__(self, other: PurePath) -> "GCSPath":
        return GCSPath(self.bucket, self.path / other)

    def read_bytes(self) -> bytes:
        return self.bucket.blob(str(self.path)).download_as_bytes(checksum=None)  # pyright: ignore[reportGeneralTypeIssues]

    def read_text(self) -> str:
        return self.bucket.blob(str(self.path)).download_as_text()


RootPath = Union[Path, GCSPath]
RootPathArg = Union[str, Path, GCSPath]


def convert_to_rootpath(path: RootPathArg) -> RootPath:
    if isinstance(path, (Path, GCSPath)):
        return path
    elif path.startswith("gs://"):
        bucket_name, prefix_path = path[5:].split("/", 1)
        bucket = storage.Client().bucket(bucket_name)
        if bucket is None:
            raise ValueError(f"Bucket {bucket_name} does not exist.")
        return GCSPath(bucket, PurePath(prefix_path))
    return Path(path)


@dataclass_json
@dataclass
class ManifestData:
    """A manifest for a weight tree.

    Each manifest mirrors the weight structure, which is a tree, to reflect the
    structure on disk. Each node in the tree represents one of the types listed
    in ManifestType.

    An example of a directory structure for weights is below:
    ckpt_root/
        info.json
        layer.0/
            data.bin
            info.json
            ...
        layer.1/
            data.bin
            info.json
            ...
        ...

    The corresponding manifest will look like the below:
    {
        "name": "ckpt_root", # located at ckpt_root
        "type": directory,
        "children": {
            "layer.0": {
                "name": "layer.0", # located at ckpt_root/layer.0/
                "type": "directory",
                "children": {
                    "data.bin": {
                        "name": "data.bin", # located at ckpt_root/layer.0/data.bin
                        "type": "file",
                        "content": data.bin.read_bytes(),
                        "content_sha256": SHA256(data.bin.read_bytes()),
                        "manifest_sha256": SHA256(SHA256("data.bin") + this.content_sha256),
                    }
                },
                "manifest_sha256": SHA256(SHA256("layer.0") + this.content_sha256 + \
                    [SHA256(child.name) + child.manifest_sha256 for child in children]))),
            },
            "layer.1": { ... }
        },
        "manifest_sha256": SHA256("layer.0") + this.content_sha256 + \
            [SHA256(child.name) + child.manifest_sha256 for child in children]))),
    }
    """

    manifestVersion: str

    name: str
    type: ManifestType
    # Children is immutable so any changes will mark the manifest as dirty
    children: dict[str, "ManifestData"] = field(default_factory=dict)

    content: Optional[bytes] = None
    contentSha256: Optional[str] = None
    manifestSha256: Optional[str] = None


class CkptManifest:
    r"""A manifest class for a weight tree.

    This class is an extension of the ManifestData .
    This allows us to perform manipulations on the manifests in a cleaner way

    There are two key concepts in this class:
    - Hydration/dehydration
    - Validity/dirtiness

    ====== Hydration/Dehydration ======
    Hydration is the process of loading the binary contents of the manifest into memory.
    Dehydration is the process of removing the binary contents of the manifest and
        optionally saving it to persistent memory. A dehydrated manifest can still be
        used, but operations that rely on the content will not be considered valid
        unless `allow_dehydrated` is set to True
    Hydration needs to be done before any operations that rely on the content.
    This concept in general allows us to keep track of whether certain operations
        are valid or not, and enables us to support partial hydration of the manifest
        when only a subset of files need to be loaded.

    Below is a visual representation of the hydration process. Dehydration works
    the same way, but in reverse order. To parallelize, you can hydrate all of the
    leaves first, then hydrate the root with reload=False. Dehydration parallelization
    works the same way.

    Hydrated manifest: (NAME)
    Fully hydrated manifest: *

    A.hydrate()
    1)       (A)        2)    (A)        3)    (A)          4)    (A)*
            /   \\           /   \\           /   \\             /   \\
           B     (C)        B     (C)        B     (C)*         (B)*  (C)*
                /   \\           /   \\           /   \\             /   \\
               D      E        (D)*    E        (D)*   (E)*        (D)*   (E)*


    ====== Validity/Dirtiness ======
    Validity is the process of checking that the manifest has correct SHA hashes throughout
        the entire manifest tree. If a hydrated node is validated from scratch, it can
        be marked as valid, *as long as no changes are made to it*. This is why we need
        to keep track of the dirty flag -- whenever we mutate any variable (__setattr__),
        we want to force a validity re-check for the manifest and all ancestors.
    Dirtiness is whether a particular manifest has been modified since it was last
        validated. If a manifest is changed, we mark the manifest as dirty and set
        validity to False to force a validity re-check on the next call to validate.
        Note that because all ancestor manifests will have propagating SHA256 changes,
        we need to recursively mark all ancestors as dirty.

    Below is a visual representation of the validity marking process. Note that on
    initialization, all manifest nodes are marked as dirty. The only way to mark a node as
    valid is to call validate().

    Hydrated manifest: (NAME)
    Dirty manifest: ^
    Valid manifest: *

    C.validate() validates everything below C and then C, marking them as valid and not dirty
    1)       (A)^        2)     (A)^         3)     (A)^         4)     (A)^
            /   \\             /   \\              /   \\              /   \\
          (B)^   (C)^        (B)^   (C)^         (B)^   (C)^         (B)^   (C)*
                /   \\             /   \\              /   \\              /   \\
              (D)^   (E)^        (D)*   (E)^         (D)*   (E)*         (D)*   (E)*

    D.dehydrate() modifies content, so it traverses up the tree and marks nodes as dirty
    1)     (A)^          2)     (A)^
          /   \\               /   \\
        (B)^   (C)*          (B)^   (C)^
              /   \\               /   \\
             D^    (E)*           D^    (E)*

    C.validate() now fails, because C is dirty, D is dehydrated, and allow_dehydrated is False
    C.validate(True) can succeed, however:
    1)     (A)^          2)     (A)^
          /   \\               /   \\
        (B)^   (C)^          (B)^   (C)*
              /   \\               /   \\
             D*    (E)*           D*    (E)*
    """

    manifest_version: str  # Semver of the manifest manifest_version

    name: str  # the name of the manifest
    type: ManifestType  # the type of the manifest given by ManifestType
    relpath: PurePath  # the relative path of the manifest

    content: Optional[bytes]  # the raw binary contents of the file
    parent: Optional["CkptManifest"]  # Parent config of this manifest
    children: Mapping[str, "CkptManifest"]  # Immutable -- only copyable
    content_sha256: bytes  # SHA256 of the content
    manifest_sha256: bytes  # the SHA256(SHA256(version) | SHA256(name) | SHA256(type) | SHA256(content) |
    # [SHA(child.name) | child.manifest_sha256 for child in children])

    dirty: bool = True  # Flag if the manifest is dirty
    is_valid: bool = False  # Flag if the manifest is valid

    def __init__(
        self,
        manifest: ManifestData,
        relpath: Union[str, PurePath] = PurePath(""),
        parent: Optional["CkptManifest"] = None,
    ):
        self.manifest_version = manifest.manifestVersion

        self.name = manifest.name
        self.type = manifest.type

        self.relpath = PurePath(relpath)

        self.content = manifest.content
        self.parent = parent
        children = manifest.children or {}
        self.children = {
            k: CkptManifest(v, self.relpath / k, self) for k, v in children.items()
        }
        self.content_sha256 = bytes.fromhex(manifest.contentSha256 or "")
        self.manifest_sha256 = bytes.fromhex(manifest.manifestSha256 or "")

        self._mark_dirty()

    def __setattr__(self, name: str, value: Any) -> None:
        if name == "dirty" or name == "is_valid":
            super().__setattr__(name, value)
        else:
            self._mark_dirty()
            super().__setattr__(name, value)

    def _mark_dirty(self) -> None:
        """Marks this config and all that depend on it as dirty.

        This introduces an invariant that all ancestors of a dirty node are also dirty.
        This also means that when we encounter a dirty node, we can stop propagating up.
        """
        seen = set()
        curr = self
        while curr is not None:
            # If we encounter a dirty node, all parents are already marked as dirty
            if curr.dirty:
                return
            if curr in seen:
                raise ValueError("Parent cycle detected")
            seen.add(curr)
            curr.dirty = True
            curr.is_valid = False
            curr = curr.parent

    def _is_curr_node_hydrated(self) -> bool:
        """Check if the manifest and all children are hydrated.

        If this manifest cannot be hydrated, then it is considered hydrated.
        """
        return self.content is not None

    def _content_sha(
        self, allow_dehydrated: bool = False, update_inplace: bool = False
    ) -> bytes:
        """Compute the SHA256 of the content."""
        use_cached = (
            self.type == ManifestType.FILE
            and allow_dehydrated
            and not self._is_curr_node_hydrated()
        )
        if use_cached and self.content_sha256 == b"":
            raise ValueError(
                "Cannot return content_sha256 because sha has not been computed or calculated"
            )

        content_sha = (
            self.content_sha256 if use_cached else sha_256_items(self.content or b"")
        )
        if update_inplace:
            self.content_sha256 = content_sha
        return content_sha

    def _manifest_sha(
        self,
        allow_dehydrated: bool = False,
        shallow: bool = False,
        update_inplace: bool = False,
    ) -> bytes:
        """Compute the SHA256 of the manifest.

        Args:
            allow_dehydrated: if allow_dehydrated, will use pre-existing
                content_sha256 when a dehydrated node is encountered
            shallow: if true, will only compute the manifest hash of the current node,
                not the hashes of its children
            update_inplace: if true, will update the manifest inplace with the computed SHAs

        Returns:
            the manifest SHA256
        """
        # SHA the name as well
        hash_items = [
            self.manifest_version.encode(),
            self.name.encode(),
            bytes(self.type),
            self._content_sha(allow_dehydrated, update_inplace=update_inplace),
        ]

        for _, child in sorted(self.children.items(), key=lambda x: x[0]):
            hash_items.append(child.name.encode())
            if shallow:
                hash_items.append(child.manifest_sha256)
            else:
                hash_items.append(
                    child._manifest_sha(  # pylint: disable=protected-access
                        allow_dehydrated, update_inplace=update_inplace
                    )
                )

        manifest_sha = sha_256_items(*hash_items)
        if update_inplace:
            self.manifest_sha256 = manifest_sha

        return manifest_sha

    def _leaves(self) -> Iterable["CkptManifest"]:
        """Get all leaves of the manifest tree.

        Returns:
            a list of leaves
        """
        if len(self.children) == 0 and self.type == ManifestType.FILE:
            yield self
        for child in self.children.values():
            yield from child._leaves()  # pylint: disable=protected-access

    def change_path(self, new_relpath: Union[str, PurePath]) -> None:
        """Change the relative path of the manifest.

        Args:
            new_relpath: the new relative path
        """
        self.relpath = Path(new_relpath)

        for k, v in self.children.items():
            v.change_path(self.relpath / k)

    def add_child(self, child: "CkptManifest") -> None:
        """Add a child manifest to this manifest.

        Args:
            child: the child manifest to add
        """
        self.children = {**self.children, child.name: child}
        child.parent = self
        child.change_path(self.relpath / child.name)

    @staticmethod
    def from_json(manifest: str, *args, **kwargs) -> "CkptManifest":
        return CkptManifest(json.loads(manifest), *args, **kwargs)

    @staticmethod
    def load_from_path(path: RootPathArg) -> "CkptManifest":
        content = (convert_to_rootpath(path) / PurePath(MANIFEST_FILE_NAME)).read_text()
        return CkptManifest(
            ManifestData.from_json(content),  # type: ignore
        )

    def to_dataclass(self, dehydrate: bool = True) -> ManifestData:
        """Convert the manifest to a dehydrated dictionary."""
        return ManifestData(
            manifestVersion=self.manifest_version,
            name=self.name,
            type=self.type,
            content=None if dehydrate else self.content,
            children={k: v.to_dataclass(dehydrate) for k, v in self.children.items()},
            contentSha256=self.content_sha256.hex(),
            manifestSha256=self.manifest_sha256.hex(),
        )

    def to_json(self) -> str:
        return self.to_dataclass().to_json()  # type: ignore

    def save_to_path(self, path: Union[str, Path]) -> None:
        path = Path(path) if isinstance(path, str) else path
        path.mkdir(parents=True, exist_ok=True)
        (path / MANIFEST_FILE_NAME).write_text(self.to_json())

    def get_sub_manifest_at(
        self, relpath: Union[str, Path, Sequence[str]]
    ) -> "CkptManifest":
        """Get a sub-manifest by relative path.

        Args:
            relpath: the relative path of the sub-manifest
        Returns:
            the sub-manifest
        """
        if isinstance(relpath, str):
            parts = Path(relpath).parts
        elif isinstance(relpath, Path):
            parts = relpath.parts
        else:
            parts = relpath

        if len(parts) == 0:
            return self

        return self.children[parts[0]].get_sub_manifest_at(parts[1:])

    def hydrate(
        self,
        rootpath: RootPathArg,
        shallow: bool = False,
        reload: bool = False,
        skip_hydration: Optional[Callable[["CkptManifest"], bool]] = None,
    ) -> None:
        """Hydrate the manifest by populating the content.

        Below is a tree structure showing the hydration process.
            Hydrated node: *
            Deeply hydrated node: ^

        Args:
            shallow: if true, only hydrate the manifest itself, not its children
            reload: if true, reload the manifest from disk even if contents are populated
            skip_hydration: if given, will skip hydration if the function returns true

        This will hydrate the manifest without hydrating the children.
        """
        root = convert_to_rootpath(rootpath)

        if skip_hydration is not None and skip_hydration(self):
            return

        if self.type == ManifestType.FILE and (
            not self._is_curr_node_hydrated() or reload
        ):
            self.content = (root / self.relpath).read_bytes()

        if not shallow:
            for child in self.children.values():
                child.hydrate(root, shallow=False, reload=reload)

    @staticmethod
    def validate_dehydrate_path(path: Union[str, os.PathLike]) -> Path:
        path = Path(path)
        if path == Path(""):
            raise ValueError("Cannot dehydrate at empty path.")
        elif path.exists() and not path.is_dir():
            raise ValueError(
                f"Cannot dehydrate at {path} because it is not a directory."
            )
        return path

    def dehydrate(self, rootpath: Union[str, Path], create_dirs: bool = True) -> None:
        """Dehydrate the manifest by saving the content to disk.

        Args:
            rootpath: the rootpath to dehydrate the manifest to
            create_dirs: if true, will create the directories if they do not exist
                if false, will raise an error if the directories do not exist

        """
        rootpath = self.validate_dehydrate_path(rootpath)
        if not rootpath.exists() and not create_dirs:
            raise ValueError(
                f"Cannot dehydrate at {rootpath} because it does not exist."
            )

        if self.type == ManifestType.FILE and self._is_curr_node_hydrated():
            filepath = rootpath / self.relpath
            filepath.parent.mkdir(parents=True, exist_ok=True)
            filepath.write_bytes(self.content)  # type: ignore

        self.content = None  # Clears the manifest to dehydrate
        for child in self.children.values():
            child.dehydrate(rootpath=rootpath)

    def finalize_dehydrate_and_save(
        self,
        rootpath: Union[str, Path],
        create_dirs: bool = True,
        worker_count: int = 1,
        incremental: bool = False,
    ) -> None:
        """Finalize the SHAs, dehydrate all contents, and save the manifest.

        This is a convenience function that calls update_manifest_sha, dehydrate, and save_to_path.

        Args:
            rootpath: the rootpath to dehydrate the manifest to
            create_dirs: if true, will create the directories if they do not exist
                if false, will raise an error if the directories do not exist
            worker_count: the number of workers to use for dehydration
            incremental: if true, will update the manifest tree provided, even if some
                nodes are dehydrated. This enables incremental saving, as incremental dehydration
                is turned on.
        """
        rootpath = self.validate_dehydrate_path(rootpath)

        if worker_count > 1:
            # Go through all the leaves and finalize + dehydrate them in parallel
            with futures.ThreadPoolExecutor(max_workers=worker_count) as executor:

                def _dehydrate_and_save_leaf(leaf: CkptManifest) -> None:
                    leaf.update_manifest_sha(allow_dehydrated=incremental)
                    leaf.dehydrate(rootpath=rootpath, create_dirs=create_dirs)
                    leaf.save_to_path(rootpath / leaf.relpath)

                executor.map(_dehydrate_and_save_leaf, self._leaves())

            # Go through the non-leaf nodes and finalize + dehydrate them serially
            self.update_manifest_sha(allow_dehydrated=True)
            self.dehydrate(rootpath=rootpath, create_dirs=create_dirs)
        else:
            self.update_manifest_sha(allow_dehydrated=incremental)
            self.dehydrate(rootpath=rootpath, create_dirs=create_dirs)
        self.save_to_path(rootpath)

    def hydrate_and_validate(
        self,
        rootpath: RootPathArg,
        worker_count: int = 1,
        skip_hydration: Optional[Callable[["CkptManifest"], bool]] = None,
    ) -> bool:
        """Hydrate the manifest and validate checksums

        Args:
            rootpath: Filesystem path or Google Cloud Storage bucket and path to hydrate from.
            worker_count: the number of workers to use for hydration
            skip_hydration: if given, will skip hydration of a node if the function returns true

        Returns:
            true if the hydrated contents are checksum valid
        """
        allow_dehydrated = skip_hydration is not None
        root = convert_to_rootpath(rootpath)

        if worker_count > 1:
            # Go through all the leaves and hydrate and validate them in parallel
            with futures.ThreadPoolExecutor(max_workers=worker_count) as executor:

                def _hydrate_and_validate_leaf(leaf: CkptManifest) -> bool:
                    leaf.hydrate(root, shallow=True, skip_hydration=skip_hydration)
                    return leaf.validate(allow_dehydrated=allow_dehydrated)

                executor.map(_hydrate_and_validate_leaf, self._leaves())

            # Go through the non-leaf nodes and hydrate and validate them serially
            return self.validate(allow_dehydrated=allow_dehydrated, skip_clean=True)
        else:
            self.hydrate(root, skip_hydration=skip_hydration)
            return self.validate(allow_dehydrated=allow_dehydrated, skip_clean=True)

    def update_manifest_sha(self, allow_dehydrated: bool = False) -> bytes:
        """Recompute the SHA256 of the manifest.

        Args:
            allow_dehydrated: if allow_dehydrated, will use pre-existing
                content_sha256 when a dehydrated node is encountered

        Returns:
            the new manifest SHA256
        """
        return self._manifest_sha(allow_dehydrated, update_inplace=True)

    def validate(
        self, allow_dehydrated: bool = False, skip_clean: bool = False
    ) -> bool:
        """Validate the manifest.

        Args:
            allow_dehydrated: if allow_dehydrated, will use pre-existing
                content_sha256 when a dehydrated node is encountered
            skip_clean: if true, will skip validation of current node if the manifest is clean

        Returns:
            true if the manifest is valid, false otherwise
        """
        if skip_clean and not self.dirty:
            shallow_check = self.is_valid
        else:
            shallow_check = (
                self._manifest_sha(allow_dehydrated, shallow=True)
                == self.manifest_sha256
            )

        deep_check = shallow_check and all(
            child.validate(allow_dehydrated, skip_clean)
            for child in self.children.values()
        )

        self.dirty = not shallow_check or not deep_check
        self.is_valid = shallow_check and deep_check
        return self.is_valid
