"""Tests for saving and loading model weights."""

from typing import Optional, Union

import pytest
import safetensors.torch
import torch

from base.fastforward.checkpoints.impl import save_load_v1


@pytest.mark.parametrize("dtype", [torch.float16, torch.float32, torch.bfloat16])
def test_save_load_weights(tmp_path, dtype: torch.dtype):
    """Save a dict of random weights. Asserts that after loading, they are the same."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.empty(5, 10, dtype=dtype, device="cpu"),
        "layer.0.b": torch.empty(81, 3, dtype=dtype, device="cpu"),
        "layer.1.b": torch.empty(12, 4, 5, dtype=dtype, device="cpu"),
    }
    for w in weights_1.values():
        w.uniform_(-0.1, 0.1)
    weights_1_serialized = safetensors.torch.save(weights_1)

    save_load_v1.save_weights(tmp_path / "ckpt", weights_1, override_deprecation=True)
    weights_2 = save_load_v1.load_weights(tmp_path / "ckpt", dtype=dtype)
    weights_2_serialized = safetensors.torch.save(weights_2)

    assert weights_1_serialized == weights_2_serialized


@pytest.mark.parametrize("dtype", [torch.float16, torch.float32, torch.bfloat16])
def test_save_load_weights_with_fn(tmp_path, dtype: torch.dtype):
    """Save a dict of random weights. Asserts that after loading, they are the same."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.empty(5, 10, dtype=dtype, device="cpu"),
        "layer.0.b": torch.empty(81, 3, dtype=dtype, device="cpu"),
        "layer.1.b": torch.empty(12, 4, 5, dtype=dtype, device="cpu"),
    }
    for w in weights_1.values():
        w.uniform_(-0.1, 0.1)
    weights_1_serialized = safetensors.torch.save(
        {
            "layer.0.a": weights_1["layer.0.a"],
            "layer.0.b": weights_1["layer.0.b"],
        }
    )

    def _zero_layer_1_b_fn(
        name: str,
        w_as_bytes: bytes,
        device: Union[torch.device, str] = "cpu",
        dtype: Optional[torch.dtype] = None,
    ) -> torch.Tensor:
        """Turns all weights in layer.1.b to zero."""
        w = (
            safetensors.torch.load(w_as_bytes)["tensor"]
            .detach()
            .clone()
            .to(dtype=dtype, device=device)
        )
        w = torch.zeros_like(w) if name == "layer.1.b" else w
        return w

    save_load_v1.save_weights(tmp_path / "ckpt", weights_1, override_deprecation=True)
    weights_2 = save_load_v1.load_weights(
        tmp_path / "ckpt", dtype=dtype, device="cpu", weight_fn=_zero_layer_1_b_fn
    )
    weights_2_serialized = safetensors.torch.save(
        {
            "layer.0.a": weights_2["layer.0.a"],
            "layer.0.b": weights_2["layer.0.b"],
        }
    )

    assert weights_1_serialized == weights_2_serialized
    torch.testing.assert_close(
        weights_2["layer.1.b"], torch.zeros(12, 4, 5, dtype=dtype, device="cpu")
    )


@pytest.mark.parametrize("dtype", [torch.float16, torch.float32, torch.bfloat16])
def test_save_load_weights_partial(tmp_path, dtype: torch.dtype):
    """Save a dict of random weights. Asserts that after loading, they are the same."""
    torch.manual_seed(31415)

    weights_1 = {
        "layer.0.a": torch.empty(5, 10, dtype=dtype, device="cpu"),
        "layer.0.hello": torch.empty(81, 3, dtype=dtype, device="cpu"),
        "layer.0.world": torch.empty(6, 8, dtype=dtype, device="cpu"),
        "layer.0.foo": torch.empty(7, 1, dtype=dtype, device="cpu"),
        "layer.0.bar": torch.empty(11, dtype=dtype, device="cpu"),
        "layer.1.a": torch.empty(12, 4, 5, dtype=dtype, device="cpu"),
        "layer.1.def": torch.empty(12, 4, 5, dtype=dtype, device="cpu"),
        "layer.2.b": torch.empty(12, 4, 5, dtype=dtype, device="cpu"),
    }
    for w in weights_1.values():
        w.uniform_(-0.1, 0.1)
    save_load_v1.save_weights(tmp_path / "ckpt", weights_1, override_deprecation=True)

    weights_1_serialized = safetensors.torch.save(
        {k: v for k, v in sorted(weights_1.items()) if k.startswith("layer.0")}
    )

    weights_2 = save_load_v1.load_weights(
        tmp_path / "ckpt", dtype=dtype, require_patterns=(r"^layer\.0",)
    )
    weights_2 = {k: v for k, v in sorted(weights_2.items())}

    weights_2_serialized = safetensors.torch.save(weights_2)

    assert weights_1_serialized == weights_2_serialized
