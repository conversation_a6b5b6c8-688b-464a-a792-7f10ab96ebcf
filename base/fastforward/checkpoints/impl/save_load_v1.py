"""Utils to save and load checkpoints."""

import hashlib
import json
import logging
import re
from concurrent import futures
from pathlib import Path
from typing import Callable, Optional, Sequence, Union

import safetensors.torch
import torch
from typing_extensions import deprecated

DEFAULT_WEIGHT_DTYPE = torch.float16
Tensor = torch.Tensor
Device = Union[str, torch.device]


_SHA_256_SEP = "|"


def _sha256(obj: bytes) -> str:
    """Hash any object into a SHA256 string."""
    hash_obj = hashlib.sha256()
    hash_obj.update(obj)
    return hash_obj.hexdigest()


@deprecated("Use v2 instead. This function will be removed in the future.")
def save_weights(
    path: Union[str, Path],
    weights: dict[str, torch.Tensor],
    max_workers: int = 16,
    detach_grad: bool = True,
    incremental: bool = False,
    override_deprecation: bool = False,
):
    """Save a dictionary of weight tensors to `path`.

    For each key `weight_name` in `weights`, this method stores
    `weights[weight_name]` at `path/weight_name`, in two files:

        path/{weight_name}/data.bin  | store the values in the weight tensor
        path/{weight_name}/info.json | store the metadata of the weights.

    We also store one extra file `path/info.json` which stores all the
    `weight_name`s and the hash of the all the weight hashes (sorted
    lexicographically). During loading, we use this hash to verify the
    checkpoint is loaded correctly.

    NOTE(hieu): currently, path/{weight_name}/info.json only stores the
        weight's hash We might want to store other values.

    Args:
        path: where to save the weights.
        weights: a dictionary mapping weight names (as str) to the weight torch.Tensors.
        max_workers: maximum degree of parallelism.
        detach_grad: we store the weights as serialized torch.Tensor objects.
            when this flag is set to True, we call weights.detach() before
            serializing. this prevents the awkward situations when we load weights
            from a saved checkpoint and still have a `weight.requires_grad` set to
            True, causing inefficient memory usage while loading.
        incremental: if True, we assume that the checkpoint exists. we will first
            check the file `{ckpt-path}/info.json`, exclude the weight_name that
            are already there, and save the rest in `weights` to their new path.
            we will also update `info.json` accordingly.
        override_deprecation: By default the call will raise an exception, because it
            is deprecated. This flag allows the call to proceed.
    """

    logging.info(
        "Saving weights using DEPRECATED fastforward checkpoints v1 to '%s'...", path
    )
    if not override_deprecation:
        raise NotImplementedError("This function is deprecated. Please use v2 instead.")

    ckpt_path = path if isinstance(path, Path) else Path(path)
    ckpt_path.mkdir(parents=True, exist_ok=True)
    ckpt_info_file = ckpt_path / "info.json"

    def _save_weight(args: tuple[str, torch.Tensor]) -> tuple[str, str, list[int]]:
        # TODO(hieu): may need to ensure `name` is a valid path name.
        name, w = args
        w_path = ckpt_path / name
        w_path.mkdir(parents=True, exist_ok=True)
        w_byte: bytes = safetensors.torch.save(
            tensors={"tensor": w.detach() if detach_grad else w}
        )
        w_hash = _sha256(w_byte)
        (w_path / "data.bin").write_bytes(w_byte)
        (w_path / "info.json").write_text(json.dumps({"sha256": w_hash}))

        return name, w_hash, list(w.size())

    data = [(k, v) for k, v in weights.items()]
    all_names = []
    if incremental:
        if ckpt_info_file.exists():
            ckpt_info = json.loads(ckpt_info_file.read_text())
            all_names = ckpt_info["weight_names"]
            data = [(k, v) for k, v in data if k not in all_names]

    with futures.ThreadPoolExecutor(max_workers=max_workers) as pool:
        jobs = pool.map(_save_weight, data)
        for w_name, _, w_size in jobs:
            logging.debug("Saved tensor '%s' with shape %s.", w_name, str(w_size))
            all_names.append(w_name)

    def _read_hash(p: Path) -> tuple[str, str]:
        return str(p.name), json.loads((p / "info.json").read_text())["sha256"]

    all_hashes = []
    with futures.ThreadPoolExecutor(max_workers=max_workers) as pool:
        jobs = pool.map(_read_hash, [ckpt_path / n for n in all_names])
        for w_name, w_hash in jobs:
            if not incremental:  # `incremental` triggers too many logs
                logging.debug("Hash: %-40s %s.", w_name, w_hash)
            all_hashes.append(w_hash)

    logging.debug("Compute ckpt_hash...")
    hash_obj = hashlib.sha256()
    hash_obj.update(_SHA_256_SEP.join(sorted(all_hashes)).encode())
    ckpt_hash = hash_obj.hexdigest()
    ckpt_info_file.write_text(
        json.dumps(
            {"sha256": ckpt_hash, "weight_names": all_names}, indent=2, sort_keys=True
        )
    )

    logging.debug("Done.")


WeightFn = Callable[[str, bytes, Union[torch.device, str]], torch.Tensor]
"""
We call this function on each weight's name after the weight is loaded from its
path into `bytes` in memory.

When called, this function will process the `bytes`, turn it into a weight
tensor, perform necessary transformations such as sharding, and finally send the
weight tensor to the device it is expected.

If a weight tensor needs to be sharded, e.g., for tensor parallelism, the sharding
logic will go into this function.
"""


def default_weight_fn(
    name: str,
    w_as_bytes: bytes,
    device: Union[torch.device, str] = "cpu",
    dtype: Optional[torch.dtype] = None,
) -> torch.Tensor:
    """Simply load the weight and send it to the correct device."""
    # NOTE(hieu): by default, we do not need `name`. we use it to convey the
    # logic that we need to do certain things with certain weights, such as
    # sharding.
    del name
    w = safetensors.torch.load(w_as_bytes)["tensor"]
    if dtype is not None:
        w = w.detach().to(dtype=dtype)
    return w.detach().to(device=device)


def load_weights(
    path: Union[str, Path],
    dtype: Optional[torch.dtype] = None,
    device: Device = "cpu",
    weight_fn: WeightFn = default_weight_fn,
    max_workers: int = 16,
    require_patterns: Sequence[str] = (),
) -> dict[str, torch.Tensor]:
    """Load a dictionary of weight tensors from `path`. Verify the loaded SHA-256.

    Args:
        path: where to save the weights.
        dtype: the target dtype for the weights. if `None, the weights' dtypes are
            kept at whatever the checkpoint stores.
        device: the target device for the weights.
        weight_fn: a function called to load each individual weight.
        max_workers: maximum degree of parallelism.
        require_patterns: if given, will only load the weights whose names match
            at least one of the patterns in `require_patterns`. matching is done via
            `re.match(pattern, weight_name) is not None`.
    """
    logging.debug("Loading weights from '%s' to device '%s'...", path, device)

    ckpt_path = path if isinstance(path, Path) else Path(path)
    assert ckpt_path.exists() and ckpt_path.is_dir()

    ckpt_info = json.loads((ckpt_path / "info.json").read_text())
    w_paths = list(ckpt_info["weight_names"])

    if require_patterns:
        w_paths = [
            (w, all(re.search(p, w) is None for p in require_patterns)) for w in w_paths
        ]
    else:
        w_paths = [(w, False) for w in w_paths]

    w_paths = [(ckpt_path / w_name, x) for w_name, x in w_paths]

    def _load_weight(
        args: tuple[Path, bool],
    ) -> tuple[str, str, Optional[torch.Tensor]]:
        p, hash_only = args
        name = p.name

        assert hash_only is not None, "hash_only should be set to bool. This is a bug."

        if hash_only:
            w_hash = json.loads((p / "info.json").read_text())["sha256"]
            w = None
        else:
            w_byte: bytes = (p / "data.bin").read_bytes()
            w = weight_fn(name, w_byte, device, dtype)  # type: ignore
            w_hash = _sha256(w_byte)

        return name, w_hash, w

    weights = {}
    all_hashes = []
    with futures.ThreadPoolExecutor(max_workers=max_workers) as pool:
        jobs = pool.map(_load_weight, w_paths)
        for name, w_hash, w in jobs:
            all_hashes.append(w_hash)
            if w is not None:
                logging.debug(
                    "Loaded tensor '%s' with shape %s.", name, str(list(w.size()))
                )
                weights[name] = w

    # this check is only valid if the entire checkpoint is loaded
    logging.info("Compute and check ckpt_hash...")
    for w_hash in all_hashes:
        if _SHA_256_SEP in w_hash:
            raise RuntimeError(
                f"Hash {w_hash} contains '{_SHA_256_SEP}' which is "
                "non-standard in SHA-256. This is a probably a security "
                "breach."
            )

    ckpt_hash_now = _sha256(_SHA_256_SEP.join(sorted(all_hashes)).encode())
    ckpt_hash_ref = ckpt_info["sha256"]
    if ckpt_hash_now != ckpt_hash_ref:
        raise RuntimeError("ckpt_hash after loading does not match the saved one.")

    logging.debug("Done.")
    return weights
