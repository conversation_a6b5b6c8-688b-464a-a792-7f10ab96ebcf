"""Tests for saving and loading model weights."""

from pathlib import Path

import pytest

from base.fastforward.checkpoints.impl import manifest

VERSION = "1.2.3"
SHA_EMPTY = manifest.sha_256_items(b"")
SHA_FILE_TYPE = manifest.sha_256_items(bytes(manifest.ManifestType.FILE))
SHA_DIR_TYPE = manifest.sha_256_items(bytes(manifest.ManifestType.DIRECTORY))

SHA_A_LAYER0_DATA_CONTENT = manifest.sha_256_items(b"1234567890")
SHA_A_LAYER0_DATA_MANIFEST = manifest.sha_256_items(
    VERSION.encode(),
    "data.bin".encode(),
    bytes(manifest.ManifestType.FILE),
    SHA_A_LAYER0_DATA_CONTENT,
)
SHA_A_LAYER0_MANIFEST = manifest.sha_256_items(
    VERSION.encode(),
    "layer.0".encode(),
    bytes(manifest.ManifestType.DIRECTORY),
    SHA_EMPTY,
    "data.bin".encode(),
    SHA_A_LAYER0_DATA_MANIFEST,
)
SHA_A_LAYER1_DATA_CONTENT = manifest.sha_256_items(b"0987654321")
SHA_A_LAYER1_DATA_MANIFEST = manifest.sha_256_items(
    VERSION.encode(),
    "data.bin".encode(),
    bytes(manifest.ManifestType.FILE),
    SHA_A_LAYER1_DATA_CONTENT,
)
SHA_A_LAYER1_MANIFEST = manifest.sha_256_items(
    VERSION.encode(),
    "layer.1".encode(),
    bytes(manifest.ManifestType.DIRECTORY),
    SHA_EMPTY,
    "data.bin".encode(),
    SHA_A_LAYER1_DATA_MANIFEST,
)
SHA_A_MANIFEST = manifest.sha_256_items(
    VERSION.encode(),
    "ckpt_root".encode(),
    bytes(manifest.ManifestType.DIRECTORY),
    SHA_EMPTY,
    "layer.0".encode(),
    SHA_A_LAYER0_MANIFEST,
)

TEST_A_LAYER0_DATA: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="data.bin",  # located at ckpt_root/layer.0/data.bin
    type=manifest.ManifestType.FILE,
    content=b"1234567890",
    contentSha256=SHA_A_LAYER0_DATA_CONTENT.hex(),
    manifestSha256=SHA_A_LAYER0_DATA_MANIFEST.hex(),
    children={},
)

TEST_A_LAYER0: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="layer.0",  # located at ckpt_root/layer.0/
    type=manifest.ManifestType.DIRECTORY,
    children={"data.bin": TEST_A_LAYER0_DATA},
    content=None,
    contentSha256=SHA_EMPTY.hex(),
    manifestSha256=SHA_A_LAYER0_MANIFEST.hex(),
)

TEST_A_LAYER1_DATA: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="data.bin",  # located at ckpt_root/layer.1/data.bin
    type=manifest.ManifestType.FILE,
    content=b"0987654321",
    contentSha256=SHA_A_LAYER1_DATA_CONTENT.hex(),
    manifestSha256=SHA_A_LAYER1_DATA_MANIFEST.hex(),
    children={},
)

TEST_A_LAYER1: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="layer.1",  # located at ckpt_root/layer.1/
    type=manifest.ManifestType.DIRECTORY,
    children={"data.bin": TEST_A_LAYER1_DATA},
    content=None,
    contentSha256=SHA_EMPTY.hex(),
    manifestSha256=SHA_A_LAYER1_MANIFEST.hex(),
)

TEST_MANIFEST_A: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="ckpt_root",  # located at ckpt_root
    type=manifest.ManifestType.DIRECTORY,
    children={"layer.0": TEST_A_LAYER0},
    content=None,
    contentSha256=SHA_EMPTY.hex(),
    manifestSha256=SHA_A_MANIFEST.hex(),
)

TEST_A_LAYER0_DATA_DEHYDRATED: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="data.bin",  # located at ckpt_root/layer.0/data.bin
    type=manifest.ManifestType.FILE,
    content=None,
    contentSha256=SHA_EMPTY.hex(),
    manifestSha256=SHA_EMPTY.hex(),
    children={},
)

TEST_A_LAYER0_DEHYDRATED: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="layer.0",  # located at ckpt_root/layer.0/
    type=manifest.ManifestType.DIRECTORY,
    children={"data.bin": TEST_A_LAYER0_DATA_DEHYDRATED},
    content=None,
    contentSha256=SHA_EMPTY.hex(),
    manifestSha256=SHA_EMPTY.hex(),
)

TEST_MANIFEST_A_DEHYDRATED: manifest.ManifestData = manifest.ManifestData(
    manifestVersion=VERSION,
    name="ckpt_root",  # located at ckpt_root
    type=manifest.ManifestType.DIRECTORY,
    children={"layer.0": TEST_A_LAYER0_DEHYDRATED},
    content=None,
    contentSha256=SHA_EMPTY.hex(),
    manifestSha256=SHA_EMPTY.hex(),
)


def assert_valid_structure(ckpt: manifest.CkptManifest) -> None:
    """Assert that the manifest is valid.

    This should generally be run any time there is a mutation to the manifest tree.
    """
    if ckpt.type == manifest.ManifestType.DIRECTORY:
        assert ckpt.content is None or len(ckpt.content) == 0
        assert ckpt.children is not None
        assert isinstance(ckpt.children, dict)
        for k, v in ckpt.children.items():
            assert isinstance(k, str)
            assert isinstance(v, manifest.CkptManifest)
            assert_valid_structure(v)
    elif ckpt.type == manifest.ManifestType.FILE:
        assert ckpt.children is None or len(ckpt.children) == 0


def test__assert_valid_structure():
    """Test that the manifest structure is valid."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    assert_valid_structure(databin)

    layer0 = manifest.CkptManifest(TEST_A_LAYER0)
    assert_valid_structure(layer0)

    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    assert_valid_structure(ckpt)


def test__assert_valid_structure_invalid_manifest():
    """Test that the manifest structure is invalid throws an error."""
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    ckpt.children["layer.0"].content = b"123456789"
    with pytest.raises(AssertionError):
        assert_valid_structure(ckpt)


def test_get_sub_manifest():
    """Test that getting a sub-manifest is correct."""
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    assert ckpt.get_sub_manifest_at("./layer.0").name == "layer.0"
    assert ckpt.get_sub_manifest_at("./layer.0/data.bin").name == "data.bin"


def test_to_dataclass():
    """Test that the manifest structure is the same after serializing/deserializing to dict."""
    databin = manifest.CkptManifest(
        TEST_A_LAYER0_DATA,
    )
    databin_reloaded = manifest.CkptManifest(databin.to_dataclass(False))

    assert databin.content_sha256 == databin_reloaded.content_sha256
    assert databin.manifest_sha256 == databin_reloaded.manifest_sha256
    assert databin.content == databin_reloaded.content
    assert databin.type == databin_reloaded.type
    assert databin.name == databin_reloaded.name

    layer0 = manifest.CkptManifest(TEST_A_LAYER0)
    layer0_reloaded = manifest.CkptManifest(layer0.to_dataclass(False))
    assert layer0.content_sha256 == layer0_reloaded.content_sha256
    assert layer0.manifest_sha256 == layer0_reloaded.manifest_sha256
    assert layer0.content == layer0_reloaded.content
    assert layer0.type == layer0_reloaded.type
    assert layer0.name == layer0_reloaded.name

    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    ckpt_reloaded = manifest.CkptManifest(ckpt.to_dataclass(False))
    assert ckpt.content_sha256 == ckpt_reloaded.content_sha256
    assert ckpt.manifest_sha256 == ckpt_reloaded.manifest_sha256
    assert ckpt.content == ckpt_reloaded.content
    assert ckpt.type == ckpt_reloaded.type
    assert ckpt.name == ckpt_reloaded.name


def test_add_child():
    """Test that adding a manifest works and updates paths correctly."""
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    ckpt.add_child(manifest.CkptManifest(TEST_A_LAYER1))

    layer1 = ckpt.get_sub_manifest_at("./layer.1")
    layer1_databin = ckpt.get_sub_manifest_at("./layer.1/data.bin")
    assert layer1.name == "layer.1"
    assert layer1_databin.type == manifest.ManifestType.FILE
    assert layer1_databin.manifest_version == VERSION
    assert layer1_databin.name == "data.bin"
    assert layer1_databin.content == b"0987654321"
    assert layer1_databin.content_sha256 == SHA_A_LAYER1_DATA_CONTENT
    assert layer1_databin.manifest_sha256 == SHA_A_LAYER1_DATA_MANIFEST
    assert layer1_databin.type == manifest.ManifestType.FILE

    assert layer1.parent == ckpt
    assert layer1.type == manifest.ManifestType.DIRECTORY
    assert layer1.manifest_version == VERSION
    assert ckpt.children["layer.1"] == layer1
    assert layer1.relpath == Path("./layer.1")
    assert layer1_databin.relpath == Path("./layer.1/data.bin")


def test_hydrated_bool_computation():
    """Test that hydration boolean computations work."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    assert databin._is_curr_node_hydrated()

    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    assert not ckpt._is_curr_node_hydrated()


def test_dehydrated_bool_computation():
    """Test that dehydration boolean computations work."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA_DEHYDRATED)
    assert not databin._is_curr_node_hydrated()

    ckpt = manifest.CkptManifest(TEST_MANIFEST_A_DEHYDRATED)
    assert not ckpt._is_curr_node_hydrated()


def test_tree_modification_marks_dirty_valid_flags():
    """Test that dirty boolean computations work."""
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    layer0 = ckpt.get_sub_manifest_at("./layer.0")
    databin = layer0.get_sub_manifest_at("./data.bin")
    assert ckpt.dirty and not ckpt.is_valid
    assert layer0.dirty and not layer0.is_valid
    assert databin.dirty and not databin.is_valid

    # Validating databin should mark it as valid, but none of its parents
    assert databin.validate()
    assert not databin.dirty and databin.is_valid
    assert layer0.dirty and not layer0.is_valid
    assert ckpt.dirty and not ckpt.is_valid

    # Validating layer0 should mark it as valid, but none of its parents
    # databin should stay valid and clean
    assert layer0.validate()
    assert not databin.dirty and databin.is_valid
    assert not layer0.dirty and layer0.is_valid
    assert ckpt.dirty and not ckpt.is_valid

    # Validating ckpt should mark it as valid, all descendants are now clean
    assert ckpt.validate()
    assert not databin.dirty and databin.is_valid
    assert not layer0.dirty and layer0.is_valid
    assert not ckpt.dirty and ckpt.is_valid

    # Mutating ckpt should mark it as dirty, all descendants should still be clean
    ckpt.name = "foobar"
    assert not databin.dirty and databin.is_valid
    assert not layer0.dirty and layer0.is_valid
    assert ckpt.dirty and not ckpt.is_valid

    # Mutating databin should mark it as dirty, all ancestors are now dirty
    databin.name = "databin_mutated"
    assert databin.dirty and not databin.is_valid
    assert layer0.dirty and not layer0.is_valid
    assert ckpt.dirty and not ckpt.is_valid


def test_content_sha():
    """Test that the content sha computation works."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    assert databin._content_sha() == SHA_A_LAYER0_DATA_CONTENT

    # Modifying hash should not affect the computation of the content hash
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    databin.content_sha256 = b"this_is_not_a_hash"
    assert databin._content_sha() == SHA_A_LAYER0_DATA_CONTENT


def test_compute_modified_content_sha_fails():
    """Test that the content sha computation fails on modification."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    databin.content = b"123456789"
    assert databin._content_sha() != SHA_A_LAYER0_DATA_CONTENT

    # Modifying non-content fields should not change the content hash
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    databin.name = "foobar"
    assert databin._content_sha() == SHA_A_LAYER0_DATA_CONTENT


def test_manifest_sha_file():
    """Test that the manifest sha computation works."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    assert databin._manifest_sha() == SHA_A_LAYER0_DATA_MANIFEST

    # Modifying hash should not affect the computation of the manifest hash
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    databin.manifest_sha256 = b"this_is_also_not_a_hash"
    assert databin._manifest_sha() == SHA_A_LAYER0_DATA_MANIFEST


def test_compute_modified_manifest_sha_file_fails():
    """Test that the manifest sha computation fails on modification."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)
    databin.name = "foobar"
    assert databin._manifest_sha() != SHA_A_LAYER0_DATA_MANIFEST


def test_update_content_sha():
    """Test that the content sha is updated correctly."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)

    databin.content_sha256 = b"this_is_not_a_hash"
    databin._content_sha(allow_dehydrated=True, update_inplace=True)
    assert databin.content_sha256 == SHA_A_LAYER0_DATA_CONTENT

    databin.content_sha256 = b"this_is_not_a_hash"
    databin._content_sha(allow_dehydrated=False, update_inplace=True)
    assert databin.content_sha256 == SHA_A_LAYER0_DATA_CONTENT

    databin.content_sha256 = b"this_is_not_a_hash"
    databin._content_sha(allow_dehydrated=True, update_inplace=False)
    assert databin.content_sha256 == b"this_is_not_a_hash"

    databin.content_sha256 = b"this_is_not_a_hash"
    databin._content_sha(allow_dehydrated=False, update_inplace=False)
    assert databin.content_sha256 == b"this_is_not_a_hash"

    # Make sure that content SHA updates on non-leaves are valid with
    # content sha flags
    layer0 = manifest.CkptManifest(TEST_A_LAYER0)
    layer0.content_sha256 = b"this_is_also_not_a_hash"
    layer0._content_sha(allow_dehydrated=True, update_inplace=True)
    assert layer0.content_sha256 == SHA_EMPTY

    layer0.content_sha256 = b"this_is_also_not_a_hash"
    layer0._content_sha(allow_dehydrated=False, update_inplace=True)
    assert layer0.content_sha256 == SHA_EMPTY

    layer0.content_sha256 = b"this_is_also_not_a_hash"
    layer0._content_sha(allow_dehydrated=True, update_inplace=False)
    assert layer0.content_sha256 == b"this_is_also_not_a_hash"

    layer0.content_sha256 = b"this_is_also_not_a_hash"
    layer0._content_sha(allow_dehydrated=False, update_inplace=False)
    assert layer0.content_sha256 == b"this_is_also_not_a_hash"


def test_dehydrated_content_sha_update():
    """Test that the content sha errors correctly on dehydrated files."""
    databin = manifest.CkptManifest(TEST_A_LAYER0_DATA)

    # Accessing content sha with no content should fail
    with pytest.raises(ValueError):
        databin.content_sha256 = b""
        databin.content = None
        databin._content_sha(allow_dehydrated=True, update_inplace=True)


def test_update_manifest_sha():
    """Test that the manifest sha is updated correctly."""
    layer0 = manifest.CkptManifest(TEST_A_LAYER0)

    layer0.manifest_sha256 = b"this_is_also_not_a_hash"
    assert layer0.manifest_sha256 != SHA_A_LAYER0_MANIFEST
    layer0.update_manifest_sha()
    assert layer0.manifest_sha256 == SHA_A_LAYER0_MANIFEST

    layer0.content_sha256 = b"wrong_content_hash"
    layer0.manifest_sha256 = b"this_is_also_not_a_hash"
    assert layer0.content_sha256 != SHA_EMPTY
    assert layer0.manifest_sha256 != SHA_A_LAYER0_MANIFEST
    layer0.update_manifest_sha(allow_dehydrated=True)
    assert layer0.content_sha256 == SHA_EMPTY
    assert layer0.manifest_sha256 == SHA_A_LAYER0_MANIFEST


def test_update_manifest_sha_after_dehydration():
    """Test that the manifest sha is updated correctly after dehydration."""
    layer0 = manifest.CkptManifest(TEST_A_LAYER0)
    layer0_reloaded = manifest.CkptManifest(layer0.to_dataclass(True))
    assert layer0_reloaded.manifest_sha256 == SHA_A_LAYER0_MANIFEST

    layer0_reloaded.manifest_sha256 = b"this_is_not_a_hash"
    assert layer0_reloaded.manifest_sha256 != SHA_A_LAYER0_MANIFEST
    layer0_reloaded.update_manifest_sha(True)
    assert layer0_reloaded.manifest_sha256 == SHA_A_LAYER0_MANIFEST

    # If you do not explicitly make sure that dehydration is allowed,
    # the recomputed manifest sha will be incorrect
    layer0_reloaded.manifest_sha256 = b"this_is_also_not_a_hash"
    assert layer0_reloaded.manifest_sha256 != SHA_A_LAYER0_MANIFEST
    layer0_reloaded.update_manifest_sha()
    assert layer0_reloaded.manifest_sha256 != SHA_A_LAYER0_MANIFEST


def test_validate_manifest():
    """Test that the manifest is validated correctly."""
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    assert ckpt.validate()

    # On any modification, the manifest should not be valid
    databin = ckpt.get_sub_manifest_at("layer.0/data.bin")
    databin.content = b"3456789"
    assert not ckpt.validate()
    assert not ckpt.validate(True)  # When dehydrated allowed, should still fail

    # Test with dehydration instead of modification
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    databin = ckpt.get_sub_manifest_at("layer.0/data.bin")
    databin.content = None
    assert not ckpt.validate()
    assert ckpt.validate(True)  # When dehydrated allowed, should succeed


def test_subtree_manifest_validation():
    """Test that the manifest is validated correctly."""
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    databin = ckpt.get_sub_manifest_at("layer.0/data.bin")
    assert databin.validate()
    assert ckpt.validate()

    # On any modification, the manifest should not be valid
    databin.content = b"3456789"
    assert not databin.validate()
    assert not ckpt.validate()
    assert not ckpt.validate(True)  # When dehydrated allowed, should still fail

    # Test with dehydration instead of modification
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    databin = ckpt.get_sub_manifest_at("layer.0/data.bin")
    databin.content = None
    assert not databin.validate()
    assert not ckpt.validate()
    assert databin.validate(True)
    assert ckpt.validate(True)  # When dehydrated allowed, should succeed


def test_save_load_manifest(tmp_path):
    """Test that the manifest is saved and loaded correctly."""
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    ckpt.save_to_path(tmp_path / "ckpt")
    ckpt_reloaded = manifest.CkptManifest.load_from_path(tmp_path / "ckpt")

    assert ckpt.content_sha256 == ckpt_reloaded.content_sha256
    assert ckpt.manifest_sha256 == ckpt_reloaded.manifest_sha256
    assert ckpt.type == ckpt_reloaded.type
    assert ckpt.name == ckpt_reloaded.name


def test_save_load_dehydrate_rehydrate(tmp_path):
    """Test that the manifest is saved and loaded correctly."""
    CKPT_PATH = tmp_path / "ckpt"
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    ckpt.finalize_dehydrate_and_save(rootpath=CKPT_PATH)
    ckpt_reloaded = manifest.CkptManifest.load_from_path(CKPT_PATH)
    ckpt_reloaded.hydrate(CKPT_PATH)

    assert not ckpt.validate()
    assert ckpt.validate(allow_dehydrated=True)
    assert ckpt_reloaded.validate()

    assert ckpt.content_sha256 == ckpt_reloaded.content_sha256
    assert ckpt.manifest_sha256 == ckpt_reloaded.manifest_sha256
    assert ckpt.content == ckpt_reloaded.content
    assert ckpt.type == ckpt_reloaded.type
    assert ckpt.name == ckpt_reloaded.name

    # Use default computed root path here
    CKPT_PATH = tmp_path / "ckpt2"
    NEW_CKPT_PATH = tmp_path / "ckpt3" / "ckpt"
    ckpt = manifest.CkptManifest(TEST_MANIFEST_A)
    ckpt.finalize_dehydrate_and_save(CKPT_PATH)

    # Move the checkpoint to a new path and make sure loading still works
    NEW_CKPT_PATH.mkdir(parents=True, exist_ok=True)
    CKPT_PATH.replace(NEW_CKPT_PATH)
    ckpt_reloaded = manifest.CkptManifest.load_from_path(NEW_CKPT_PATH)
    ckpt_reloaded.hydrate(NEW_CKPT_PATH)

    assert not ckpt.validate()
    assert ckpt.validate(allow_dehydrated=True)
    assert ckpt_reloaded.validate()

    # Assert that the relative paths are the same
    assert ckpt.relpath == ckpt_reloaded.relpath
    assert (
        ckpt.get_sub_manifest_at("layer.0/data.bin").relpath
        == ckpt_reloaded.get_sub_manifest_at("layer.0/data.bin").relpath
    )

    assert ckpt.content_sha256 == ckpt_reloaded.content_sha256
    assert ckpt.manifest_sha256 == ckpt_reloaded.manifest_sha256
    assert ckpt.content == ckpt_reloaded.content
    assert ckpt.type == ckpt_reloaded.type
    assert ckpt.name == ckpt_reloaded.name
