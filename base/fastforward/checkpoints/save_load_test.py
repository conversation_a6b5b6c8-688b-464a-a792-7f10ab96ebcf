"""Tests for saving and loading model weights."""

from unittest.mock import patch

import pytest
import torch

from base.fastforward.checkpoints import save_load
from base.fastforward.checkpoints.impl import save_load_v1

TEST_DATATYPES = [torch.uint8, torch.float16, torch.float32, torch.bfloat16]


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_correct_routing_save_weights_default_v2(tmp_path, dtype: torch.dtype):
    """Test that saving weights is correctly routed to v2 when no version is specified."""
    with patch(
        "base.fastforward.checkpoints.impl.save_load_v1.save_weights"
    ) as mock_v1:
        with patch(
            "base.fastforward.checkpoints.impl.save_load_v2.save_weights"
        ) as mock_v2:
            torch.manual_seed(31415)

            w1 = {"layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu")}
            save_load.save_weights(tmp_path / "ckpt", w1)
            mock_v2.assert_called_once()
            mock_v1.assert_not_called()


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_correct_routing_load_weights_v1(tmp_path, dtype: torch.dtype):
    """Test that loading weights is correctly routed to v1 when no version is specified."""
    with patch(
        "base.fastforward.checkpoints.impl.save_load_v1.load_weights"
    ) as mock_v1:
        with patch(
            "base.fastforward.checkpoints.impl.save_load_v2.load_weights"
        ) as mock_v2:
            torch.manual_seed(31415)

            w1 = {"layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu")}
            save_load_v1.save_weights(tmp_path / "ckpt", w1, override_deprecation=True)
            with pytest.raises(ValueError):
                save_load.load_weights(tmp_path / "ckpt")
            mock_v2.assert_not_called()
            mock_v1.assert_not_called()


@pytest.mark.parametrize("dtype", TEST_DATATYPES)
def test_correct_routing_load_weights_v2(tmp_path, dtype: torch.dtype):
    """Test that loading weights is correctly routed to v2 when no version is specified and the manifest version is v2."""
    with patch(
        "base.fastforward.checkpoints.impl.save_load_v1.load_weights"
    ) as mock_v1:
        with patch(
            "base.fastforward.checkpoints.impl.save_load_v2.load_weights"
        ) as mock_v2:
            torch.manual_seed(31415)

            w1 = {"layer.0.a": torch.randint(0, 16, (5, 10), dtype=dtype, device="cpu")}
            manifest = save_load.save_weights(tmp_path / "ckpt", w1)
            save_load.load_weights(
                tmp_path / "ckpt", target_sha256=manifest.manifest_sha256.hex()
            )
            mock_v1.assert_not_called()
            mock_v2.assert_called_once()
