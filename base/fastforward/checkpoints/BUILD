load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "save_load",
    srcs = ["save_load.py"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        "//base/fastforward/checkpoints/impl:save_load_v1",
        "//base/fastforward/checkpoints/impl:save_load_v2",
    ],
)

pytest_test(
    name = "save_load_test",
    srcs = ["save_load_test.py"],
    deps = [
        ":save_load",
        "//base/fastforward/checkpoints/impl:save_load_v1",
    ],
)
