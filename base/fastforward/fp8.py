"""Core FP8 routines.

Note: TransformerEngine is a delicate library that has several preconditions on its use.
The intention of this module is to provide everything you might need from TE in a safe
and performant way -- you should never need to import TransformerEngine yourself.

To test if you have FP8 capability, try / except importing this module.

```
try:
    from base.fastforward import fp8
    supports_fp8 = True
except:
    supports_fp8 = False
```
"""

# TransformerEngine needs some magic to make it work in bazel.
import os

import torch
import torch.nn as nn

# deactivate flake8 warning E402 - module level import not at top of file
# flake8: noqa
if "BAZEL_TARGET_NAME" in os.environ:
    # Hacky way to identify that we're running in bazel.
    # Need to set Transformer Engine path differently.
    if "NVTE_INSTALL_PATH" not in os.environ:
        os.environ["NVTE_INSTALL_PATH"] = (
            "../rules_python~~pip~python_pip_311_transformer_engine/site-packages"
        )
import transformer_engine.pytorch.cpp_extensions as texcpp
import transformer_engine_extensions as tex
from transformer_engine.pytorch.constants import TE_DType as TE_DType

from base.fastforward import torch_utils

# cuBLAS uses a fixed segment of memory as its workspace for computation.
_cublas_workspace = None


def _get_cublas_workspace_size_bytes() -> int:
    """Return 32 MiB if using hopper, 4 MiB for all other architectures."""
    MiB = 2**20
    if torch.cuda.get_device_properties(torch.cuda.current_device()).major >= 9:
        return 32 * MiB
    return 4 * MiB


def _get_cublas_workspace() -> torch.Tensor:
    """Returns workspace for cublas."""
    global _cublas_workspace  # pylint: disable=global-statement
    if _cublas_workspace is None:
        _cublas_workspace = torch.empty(
            _get_cublas_workspace_size_bytes(), dtype=torch.uint8, device="cuda"
        )
    return _cublas_workspace


# TODO(arun): enumify.
FP8Types = dict[str, tex.DType](
    e4m3=tex.DType.kFloat8E4M3,
    e5m2=tex.DType.kFloat8E5M2,
)


_FP8Max = dict(
    e4m3=448.0,
    e5m2=57344.0,
)


def _is_fp8_format(fp8_format: str | tex.DType):
    return fp8_format in ["e4m3", "e5m2", tex.DType.kFloat8E4M3, tex.DType.kFloat8E5M2]


def _make_fp8_meta(
    scale: float, device: torch.device | str = "cuda"
) -> tex.FP8TensorMeta:
    meta = tex.FP8TensorMeta()
    # NOTE(arun): Scales *must* be in FP32, or the CuBLAS APIs will silently compute
    #   the wrong thing.
    meta.scale = torch.ones(1, device=device, dtype=torch.float32) * scale
    meta.scale_inv = torch.ones(1, device=device, dtype=torch.float32) / scale
    meta.amax_history = torch.zeros(1, 1, dtype=torch.float32, device=device)
    return meta


def to_fp8(
    x: torch.Tensor,
    scale: float | tex.FP8TensorMeta,
    fp8_format: str | tex.DType = "e4m3",
    preserve_input: bool = False,
) -> torch.Tensor:
    """Convert a tensor into an FP8 tensor.

    Args:
        x: The input tensor.
        scale: The scale factor to use.
        fp8_format: The format to use.
        preserve_input: If true, the input tensor is not modified.
            If false, `x` may be corrupted by the conversion.

    Returns:
        The FP8 tensor.
    """
    assert x.is_cuda
    assert _is_fp8_format(fp8_format)
    if preserve_input:
        x = x.clone()
    if isinstance(fp8_format, str):
        fp8_format = FP8Types[fp8_format]
    if isinstance(scale, (float, int)):
        scale = _make_fp8_meta(scale, x.device)
    ret = texcpp.cast_to_fp8(x, scale, 0, fp8_format)
    assert ret is not None
    return ret


def from_fp8(
    x: torch.Tensor,
    scale: float | tex.FP8TensorMeta,
    result_type: torch.dtype,
    fp8_format="e4m3",
) -> torch.Tensor:
    """Convert a tensor from an FP8 tensor.

    Args:
        x: The input tensor.
        scale: The scale factor to use.
        fp8_format: The format x is in.
        result_dtype: The target datatype.

    Returns:
        A tensor of type `result_type`.
    """
    assert x.is_cuda and x.dtype == torch.uint8
    assert _is_fp8_format(fp8_format)
    if isinstance(scale, (float, int)):
        scale = _make_fp8_meta(scale, x.device)
    return texcpp.cast_from_fp8(
        x, scale, 0, FP8Types[fp8_format], TE_DType[result_type]
    )


def optimal_scaling_factor_for_amax(
    amax: torch.Tensor, fp8_format: str = "e4m3"
) -> torch.Tensor:
    """Computes the optimal scaling factor for a given fp8 format."""
    assert _is_fp8_format(fp8_format)
    amax = amax.float()
    exponent = torch.floor(torch.log2(_FP8Max[fp8_format] / amax))
    default_scale = torch.ones_like(amax)

    # Guard against all-zeros, nonfinite scales, etc.
    # This code is roughly pytorch/fp8.py:_default_sf_compute from TransformerEngine.
    scale = torch.round(torch.pow(2, torch.abs(exponent)))
    scale = torch.where(amax > 0.0, scale, default_scale)
    scale = torch.where(torch.isfinite(amax), scale, default_scale)
    return torch.where(exponent < 0, 1.0 / scale, scale)


def convert_linear_state_dict_to_fp8(
    state_dict: dict[str, torch.Tensor],
    input_amaxes: torch.Tensor,
    output_amaxes: torch.Tensor,
    prefix: str = "",
    device: torch.device | None = None,
    preserve_input: bool = False,
):
    # NOTE: To save memory, the weights may have been offloaded to CPUs. Here,
    # we explicitly move them back to GPU if that's the case. If the weights
    # are already on the GPUs, .cuda() does nothing.
    weights = state_dict[prefix + "weight"].cuda()
    weight_scale = optimal_scaling_factor_for_amax(weights.abs().amax()).item()
    input_scale = optimal_scaling_factor_for_amax(input_amaxes.abs().amax()).item()
    output_scale = optimal_scaling_factor_for_amax(output_amaxes.abs().amax()).item()
    weights_fp8 = to_fp8(weights, weight_scale, preserve_input=preserve_input)
    state_dict.update(
        {
            prefix + "weight": weights_fp8,
            prefix + "weight_scale": torch.tensor(
                [weight_scale], dtype=torch.float32, device=device
            ),
            prefix + "input_scale": torch.tensor(
                [input_scale], dtype=torch.float32, device=device
            ),
            prefix + "output_scale": torch.tensor(
                [output_scale], dtype=torch.float32, device=device
            ),
        }
    )
    return state_dict


class FP8Linear(nn.Module):
    """An FP8 version of nn.Linear."""

    def __init__(
        self,
        in_features: int,
        out_features: int,
        bias: bool = True,
        device: torch.device | str = "cuda",
        dtype: torch.dtype = torch.float16,
        qdtype: tex.DType = tex.DType.kFloat8E4M3,
    ):
        super().__init__()
        assert (
            in_features % 16 == 0
        ), "FP8 multiplication requires in features be a multiple of 16."
        assert (
            out_features % 8 == 0
        ), "FP8 multiplication requires out features be a multiple of 8."
        self.in_features = in_features
        self.out_features = out_features
        self.dtype = dtype
        self.qdtype = qdtype

        self.weight = nn.Parameter(
            torch.zeros(out_features, in_features, dtype=torch.uint8, device=device),
            requires_grad=False,
        )
        self.bias = (
            nn.Parameter(
                torch.zeros(out_features, dtype=dtype, device=device),
                requires_grad=False,
            )
            if bias
            else None
        )
        self.weight_scale = nn.Parameter(
            torch.ones(1, device=device), requires_grad=False
        )
        self.input_scale = nn.Parameter(
            torch.ones(1, device=device), requires_grad=False
        )
        self.output_scale = nn.Parameter(
            torch.ones(1, device=device), requires_grad=False
        )
        self.weight_fp8_meta = _make_fp8_meta(1, device=device)
        self.input_fp8_meta = _make_fp8_meta(1, device=device)
        self.output_fp8_meta = _make_fp8_meta(1, device=device)

        self.register_load_state_dict_post_hook(self._load_state_dict_post_hook)

    def extra_repr(self) -> str:
        return "in_features={}, out_features={}, bias={}, qdtype={}".format(
            self.in_features,
            self.out_features,
            self.bias is not None,
            self.qdtype,
        )

    def _load_state_dict_post_hook(self, *args, **kwargs):
        del args, kwargs
        self.weight = self.weight.contiguous()  # pylint: disable=W0201
        assert self.weight_scale.dtype == torch.float, "Scales *must* be float32."
        self.weight_fp8_meta = _make_fp8_meta(
            self.weight_scale.item(), device=self.weight.device
        )
        self.input_fp8_meta = _make_fp8_meta(
            self.input_scale.item(), device=self.weight.device
        )
        self.output_fp8_meta = _make_fp8_meta(
            self.output_scale.item(), device=self.weight.device
        )

    def forward(
        self,
        x: torch.Tensor,
        with_gelu: bool = False,
        output_fp8_meta: tex.FP8TensorMeta | None = None,
        out: torch.Tensor | None = None,
        accumulate: bool = False,
    ):
        assert isinstance(
            accumulate, bool
        ), "The `accumulate` arg must be a bool (not a Tensor)."
        if accumulate:
            assert output_fp8_meta is None, "Cannot accumulate onto fp8 output."
            assert out is not None, "Must provide output tensor to accumulate into."
        assert (
            x.shape[-1] == self.weight.shape[-1]
        ), f"Input shape {x.shape} doesn't match weight shape {self.weight.shape}"
        # Need to view x as 2D [*, hidden_in]
        orig_x_shape = x.shape
        x = x.view((-1, x.shape[-1]))
        assert (
            x.shape[0] % 8 == 0
        ), f"FP8GEMM requires batch dimension to be 0 mod 8 ({x.shape})."

        output_in_fp8 = output_fp8_meta is not None
        if output_in_fp8:
            output_torch_dtype = torch.uint8
            output_tex_dtype = FP8Types["e4m3"]
        else:
            output_torch_dtype = self.dtype
            output_tex_dtype = TE_DType[output_torch_dtype]

        # If not fp8 input, cast to fp8
        if x.dtype != torch.uint8:
            x = to_fp8(x, self.input_fp8_meta, self.qdtype)

        if out is None:
            assert not accumulate, "Cannot accumulate into out=None."
            out = torch.empty(
                x.shape[0],
                self.weight.shape[0],
                dtype=output_torch_dtype,
                device=x.device,
            )
        else:
            assert out.shape[:-1] == orig_x_shape[:-1], "Output shape must match input."
            assert (
                out.shape[-1] == self.weight.shape[0]
            ), "Output features must match weight."
            assert out.dtype == output_torch_dtype, "Output dtype must match."
            assert out.device == x.device, "Output device must match input."
            assert out.is_contiguous(), "Output must be contiguous."
            # The `fp8_gemm` API expects 2D inputs and outputs. So we view output as 2D here just
            # as we view the input as 2D above. The final result is returned as the original shape.
            out = out.view((-1, out.shape[-1]))
            assert out.is_contiguous(), "Output must be contiguous."  # Extra paranoia

        texcpp.fp8_gemm(
            A=self.weight,
            A_scale_inv=self.weight_fp8_meta.scale_inv,
            A_fp8_tensor=0,
            A_dtype=tex.DType.kFloat8E4M3,
            B=x,
            B_scale_inv=self.input_fp8_meta.scale_inv,
            B_fp8_tensor=0,
            B_dtype=tex.DType.kFloat8E4M3,
            out_dtype=output_torch_dtype,
            workspace=_get_cublas_workspace(),
            gelu=with_gelu,
            accumulate=accumulate,
            out=out,
            out_index=None if output_fp8_meta is None else 0,
            fp8_meta_tensor=output_fp8_meta,
            bias=self.bias,
            use_bias=self.bias is not None,
            D_dtype=output_tex_dtype,
        )
        return out.view(*orig_x_shape[:-1], out.shape[-1])


class FP8LayerNorm(nn.LayerNorm):
    """An FP8 version of nn.LayerNorm."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def forward(
        self,
        input: torch.Tensor,  # pylint: disable=redefined-builtin
        *,
        output_fp8_meta: tex.FP8TensorMeta | None = None,
    ):
        # Fuses conversion of output in FP8 if `output_fp8_meta``.
        return texcpp.layernorm_fwd_fp8_inf(
            inp=input,
            weight=self.weight,
            bias=self.bias,
            eps=self.eps,
            fp8_meta_tensor=output_fp8_meta,
            fp8_tensor=0,
            otype=FP8Types["e4m3"],
            zero_centered_gamma=False,
        )


class FP8RmsNorm(nn.Module):
    """An FP8 version of RmsNorm."""

    def __init__(
        self,
        inp_dim: int,
        eps: float = 1e-6,
        in_place: bool = False,
        dtype: torch.dtype = torch.float16,
        device: torch.device | str = "cuda",
    ):
        super().__init__()
        self._device = device
        self._dtype = dtype

        self._inp_dim = inp_dim
        self._in_place = in_place
        self._eps = eps

        weight = torch.zeros(inp_dim, dtype=dtype, device=device)
        self.weight = nn.Parameter(weight, requires_grad=False)

    def forward(
        self,
        inp: torch.Tensor,  # pylint: disable=redefined-builtin
        *,
        output_fp8_meta: tex.FP8TensorMeta | None = None,
    ):
        # Fuses conversion of output in FP8 if `output_fp8_meta`.
        return texcpp.rmsnorm_fwd_fp8_inf(
            inp=inp,
            weight=self.weight,
            eps=self._eps,
            fp8_meta_tensor=output_fp8_meta,
            fp8_tensor=0,
            otype=FP8Types["e4m3"] if output_fp8_meta is not None else self._dtype,
            zero_centered_gamma=False,
        )


def convert_linear_to_fp8(
    linear: nn.Linear,
    input_amaxes: torch.Tensor,
    output_amaxes: torch.Tensor,
    qdtype: tex.DType = tex.DType.kFloat8E4M3,
    device: torch.device | None = None,
    preserve_input: bool = False,
) -> FP8Linear:
    """Convert a linear layer to an FP8 version.

    Args:
        linear: The linear layer to convert.
        input_amaxes: The maximum per-channel values of the input.
        output_amaxes: The maximum per-channel values of the output.
        qdtype: The quantization dtype to use.
        device: The device to use.
        preserve_input: If true, the input tensor is not modified.
            If false, `x` may be corrupted by the conversion.
        smooth_input: If true, the input and weights are smoothed together.

    Returns:
        The FP8 version of the linear layer.
    """
    state_dict = convert_linear_state_dict_to_fp8(
        linear.state_dict(),
        input_amaxes=input_amaxes,
        output_amaxes=output_amaxes,
        device=device,
        preserve_input=preserve_input,
    )

    return torch_utils.init_with_weights(
        FP8Linear,
        state_dict,
        in_features=linear.in_features,
        out_features=linear.out_features,
        bias=linear.bias is not None,
        dtype=linear.weight.dtype,
        qdtype=qdtype,
        device=device or linear.weight.device,
    )


def convert_linear_from_fp8(
    linear: FP8Linear,
    dtype: torch.dtype = torch.float16,
    device: torch.device | None = None,
) -> nn.Linear:
    state_dict = {
        "weight": from_fp8(
            linear.weight.data,
            linear.weight_scale.item(),
            dtype,
        ),
        # NOTE(arun): The bias is computed without quantization.
        "bias": linear.bias.data if linear.bias is not None else None,
    }

    return torch_utils.init_with_weights(
        nn.Linear,
        state_dict,
        in_features=linear.in_features,
        out_features=linear.out_features,
        bias=linear.bias is not None,
        device=device or linear.weight.device,
        dtype=dtype,
    )
