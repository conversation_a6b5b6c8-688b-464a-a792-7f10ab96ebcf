"""Smoke test some layer implementations and ensure they match expected outputs."""

import numpy as np
import pytest
import torch
import torch.nn as nn

from base.fastforward import layers
from base.fastforward.parallel import ParallelContext


def _init_module(mdl: nn.Module, gaussian_init: bool = False):
    with torch.inference_mode():
        for p in mdl.parameters():
            std = 1.0 / np.sqrt(p.shape[0])
            p.normal_(std=std) if gaussian_init else p.uniform_(-0.1, 0.1)


@pytest.mark.parametrize(
    "inp_dim, expected_out",
    [
        pytest.param(10, 0.03050602413713932, id="0"),
        pytest.param(11, 0.02675773948431015, id="1"),
        pytest.param(17, 0.03066488914191723, id="2"),
    ],
)
def test_rms_norm(inp_dim: int, expected_out: float):
    torch.random.manual_seed(31415)

    inp = torch.empty(inp_dim, dtype=torch.float32, device="cuda").uniform_(-0.1, 0.1)

    layer = layers.RmsNorm(inp_dim=inp_dim, dtype=torch.float32)
    _init_module(layer)

    out = layer(inp).float().mean().item()
    torch.testing.assert_close(out, expected_out)


@pytest.mark.parametrize(
    "num_tokens, vocab_size, emb_dim, expected_out",
    [pytest.param(3, 512, 32, 0.0010970182484015822, id="0")],
)
def test_llama_word_embeddings(
    num_tokens: int, vocab_size: int, emb_dim: int, expected_out: float
):
    torch.random.manual_seed(31415)
    inp = torch.randint(
        low=0, high=vocab_size, size=(num_tokens,), dtype=torch.int32, device="cuda"
    )

    layer = layers.WordEmbeddings(
        vocab_size=vocab_size,
        emb_dim=emb_dim,
        dtype=torch.float32,
    )
    _init_module(layer)
    out = layer(inp)
    torch.testing.assert_close(out.float().mean().item(), expected_out)


@pytest.mark.parametrize(
    "num_tokens, emb_dim, mlp_dim, expected_out",
    [
        pytest.param(10, 16, 32, 2.30897803703559e-05, id="0"),
        pytest.param(11, 12, 18, 4.87981342303101e-05, id="1"),
        pytest.param(17, 11, 32, -0.00031015370041132, id="2"),
    ],
)
def test_llama_swiglu(num_tokens: int, emb_dim: int, mlp_dim: int, expected_out: float):
    torch.random.manual_seed(31415)

    inp = torch.empty(num_tokens, emb_dim, dtype=torch.float32, device="cuda")
    inp.uniform_(-1.0, 1.0)

    layer = layers.LlamaSwiGLU(emb_dim=emb_dim, mlp_dim=mlp_dim, dtype=torch.float32)
    _init_module(layer)

    out = layer(inp).float().mean().item()
    torch.testing.assert_close(out, expected_out)


@pytest.mark.parametrize(
    "emb_dim, mlp_dim, moe_num_experts, moe_top_k",
    [
        pytest.param(32, 64, 4, 2, id="0"),
        pytest.param(32, 64, 16, 4, id="1"),
    ],
)
@torch.inference_mode()
def test_dbrx_ffn_layer(
    emb_dim: int, mlp_dim: int, moe_num_experts: int, moe_top_k: int
):
    """Test the result of DBRX FFN layer vs. a np-based dense style implementation."""
    torch.random.manual_seed(31415)
    seq_len = 16
    inp = torch.empty(seq_len, emb_dim, dtype=torch.float32, device="cuda")
    inp.normal_()

    layer = layers.DbrxFFN(
        emb_dim=emb_dim,
        mlp_dim=mlp_dim,
        moe_num_experts=moe_num_experts,
        moe_top_k=moe_top_k,
        moe_normalize_expert_weights=1.0,
        dtype=torch.float32,
        device="cuda",
    )
    _init_module(layer, gaussian_init=True)
    out = layer(inp)
    output_np = out.cpu().numpy()
    assert output_np.shape == (seq_len, emb_dim)

    # Manually compute without tensor parallel and sparsity.
    inputs_np = inp.cpu().numpy()
    w1_np = layer.w1.detach().cpu().numpy()
    w2_np = layer.w2.detach().cpu().numpy()
    v1_np = layer.v1.detach().cpu().numpy()
    router_np = layer.router.weight.detach().cpu().numpy()
    assert router_np.shape == (moe_num_experts, emb_dim)
    router_logits = inputs_np @ router_np.T
    router_logits = router_logits - np.max(router_logits, axis=-1, keepdims=True)
    router_probs = np.exp(router_logits) / np.sum(
        np.exp(router_logits), axis=-1, keepdims=True
    )
    top_experts_indices = np.argsort(-router_probs, axis=-1)[:, :moe_top_k]
    top_experts_mask = np.zeros_like(router_probs, dtype=bool)
    np.put_along_axis(top_experts_mask, top_experts_indices, True, axis=-1)
    router_probs[~top_experts_mask] = 0
    router_probs = router_probs / np.sum(router_probs, axis=-1, keepdims=True)
    assert w1_np.shape == (moe_num_experts, emb_dim, mlp_dim)
    gate_proj = (inputs_np[:, None, None, :] @ w1_np[None, ...]).reshape(
        seq_len, moe_num_experts, mlp_dim
    )
    gate_proj = gate_proj / (1.0 + np.exp(-gate_proj))
    up_proj = (inputs_np[:, None, None, :] @ v1_np[None, ...]).reshape(
        seq_len, moe_num_experts, mlp_dim
    )
    gated_proj = gate_proj * up_proj
    down_proj = (gated_proj[..., None, :] @ w2_np[None, ...]).reshape(
        seq_len, moe_num_experts, emb_dim
    )
    manual_output_np = (down_proj * router_probs[..., None]).sum(axis=1)
    assert np.allclose(output_np, manual_output_np, atol=1e-4)


@pytest.mark.parametrize(
    "num_heads_q,num_heads_kv,head_dim,seq_len",
    [
        pytest.param(32, 8, 128, 64, id="gqa_standard"),
        pytest.param(32, 32, 128, 64, id="mha_standard"),
        pytest.param(32, 1, 128, 64, id="mqa_standard"),
        pytest.param(8, 8, 64, 128, id="small_model"),
        pytest.param(32, 8, 128, 0, id="empty_sequence"),
    ],
)
def test_llama_attention_qk_norm(
    num_heads_q: int, num_heads_kv: int, head_dim: int, seq_len: int
):
    """Test QK normalization in LlamaAttention."""
    torch.random.manual_seed(42)

    # Create single-process parallel context
    parallel_ctx = ParallelContext.single_process()

    # Create attention layer with QK norm
    emb_dim = num_heads_q * head_dim  # Standard hidden dimension
    attn = layers.LlamaAttention(
        emb_dim=emb_dim,
        num_heads_q=num_heads_q,
        num_heads_kv=num_heads_kv,
        head_dim=head_dim,
        parallel_ctx=parallel_ctx,
        use_qk_norm=True,
        qk_norm_eps=1e-6,  # Standard RMSNorm epsilon
    )

    # Initialize weights
    _init_module(attn)

    # Verify q_norm and k_norm exist
    assert hasattr(attn, "q_norm")
    assert hasattr(attn, "k_norm")
    assert isinstance(attn.q_norm, layers.RmsNorm)
    assert isinstance(attn.k_norm, layers.RmsNorm)

    # Test empty sequence case
    if seq_len == 0:
        qkv_dim = (num_heads_q + 2 * num_heads_kv) * head_dim
        qkv = torch.zeros(0, qkv_dim, device="cuda", dtype=torch.float32)
        result = attn._apply_qk_norm(qkv)
        assert result.shape == (0, qkv_dim)
        assert result is qkv  # Should return same tensor for in-place operation
        return

    # Create QKV tensor
    qkv_dim = (num_heads_q + 2 * num_heads_kv) * head_dim
    qkv = torch.randn(seq_len, qkv_dim, device="cuda", dtype=torch.float32)
    qkv_original = qkv.clone()

    # Apply QK normalization
    result = attn._apply_qk_norm(qkv)

    # Verify in-place modification
    assert result is qkv

    # Verify shape preserved
    assert result.shape == qkv_original.shape

    # Verify Q and K were normalized but V unchanged
    queries_per_kv = num_heads_q // num_heads_kv
    qkv_grouped = result.view(seq_len, num_heads_kv, queries_per_kv + 2, head_dim)
    qkv_original_grouped = qkv_original.view(
        seq_len, num_heads_kv, queries_per_kv + 2, head_dim
    )

    # Extract Q, K, V
    q_normalized = qkv_grouped[:, :, :queries_per_kv, :]
    k_normalized = qkv_grouped[:, :, queries_per_kv, :]
    v_after = qkv_grouped[:, :, queries_per_kv + 1, :]

    q_original = qkv_original_grouped[:, :, :queries_per_kv, :]
    k_original = qkv_original_grouped[:, :, queries_per_kv, :]
    v_original = qkv_original_grouped[:, :, queries_per_kv + 1, :]

    # V should be unchanged
    torch.testing.assert_close(v_after, v_original)

    # Q and K should be normalized (different from original)
    assert not torch.allclose(q_normalized, q_original, rtol=1e-3)
    assert not torch.allclose(k_normalized, k_original, rtol=1e-3)

    # Verify normalization was applied correctly
    # Check that each head is normalized (has unit variance in expectation)
    q_flat = q_normalized.reshape(-1, head_dim)
    k_flat = k_normalized.reshape(-1, head_dim)

    # RMSNorm should make the RMS close to 1 (modulo the learnable weight)
    # Since we initialized weights uniformly, we just check they're different from original
    assert torch.isfinite(q_flat).all()
    assert torch.isfinite(k_flat).all()


@pytest.mark.parametrize(
    "dtype",
    [
        pytest.param(torch.float32, id="fp32"),
        pytest.param(torch.float16, id="fp16"),
        pytest.param(torch.bfloat16, id="bf16"),
    ],
)
def test_llama_attention_qk_norm_dtypes(dtype: torch.dtype):
    """Test QK normalization with different dtypes."""
    if dtype == torch.float16 and not torch.cuda.is_available():
        pytest.skip("FP16 requires CUDA")

    torch.random.manual_seed(42)

    # Create single-process parallel context
    parallel_ctx = ParallelContext.single_process()

    # Test parameters
    num_heads_q = 8
    num_heads_kv = 4
    head_dim = 64
    seq_len = 32

    # Create attention layer
    emb_dim = num_heads_q * head_dim
    attn = layers.LlamaAttention(
        emb_dim=emb_dim,
        num_heads_q=num_heads_q,
        num_heads_kv=num_heads_kv,
        head_dim=head_dim,
        parallel_ctx=parallel_ctx,
        use_qk_norm=True,
        qk_norm_eps=1e-6,
        dtype=dtype,
    )

    # Create input
    qkv_dim = (num_heads_q + 2 * num_heads_kv) * head_dim
    qkv = torch.randn(seq_len, qkv_dim, device="cuda", dtype=dtype)

    # Apply normalization
    result = attn._apply_qk_norm(qkv)

    # Basic checks
    assert result.dtype == dtype
    assert torch.isfinite(result).all()
    assert result is qkv  # In-place operation
