"""Utility to capture CUDA-graphs with attention objects."""

from typing import Callable, Sequence

import torch

from base.fastforward import cached_attention, cuda_graphs


class GraphedEmbForwardPass:
    """A wrapper for a step_fn that graph-captures the first call.

    This is needed so the attention cache object can be bound at first call
    rather than at construction of the `step_fn`.
    """

    def __init__(
        self,
        step_fn: Callable[[torch.Tensor, cached_attention.Attention], torch.Tensor],
        process_idx: int = 0,
        num_processes: int = 1,
        batch_sizes: Sequence[int] | None = None,
        pool=None,
    ):
        self._step_fn = step_fn
        self._batch_sizes = batch_sizes
        self._process_idx = process_idx
        self._num_processes = num_processes

        # It is very unsatisfactory that we need separate CUDA graphs for each
        # attention object. This is because attention objects do not only encapsulate
        # state, but can carry different logic.
        self._graphed_step_fn_by_attn: dict[
            cached_attention.Attention, cuda_graphs.GraphedFunctionRecapturable
        ] = {}
        self._shared_graph_pool = pool or torch.cuda.graph_pool_handle()

    def __call__(
        self, emb: torch.Tensor, attn: cached_attention.Attention
    ) -> torch.Tensor:
        attn_tensors = attn.get_tensors_for_cuda_graph(process_idx=self._process_idx)

        for name, tensor in attn_tensors.items():
            assert str(tensor.device) == f"cuda:{self._process_idx}", (
                f"The {name} tensor in the attention object is not on the "
                f"correct device. Expected cuda:{self._process_idx}, got "
                f"{tensor.device}."
            )

        if attn not in self._graphed_step_fn_by_attn:
            print(f"{self._process_idx=}: cuda graph attn cache miss")
            kwarg_specs = {}
            # TODO(markus): find a way to not depend on string identifiers defined
            # in another file. Maybe let attention return this dict right away?
            for name, tensor in attn_tensors.items():
                if name in ["k_cache", "v_cache"]:
                    kwarg_specs[name] = cuda_graphs.ArgumentSpec(
                        batch_dim=None,
                        pre_alloc_tensor=None,
                        on_data_ptr_change=cuda_graphs.OnDataPtrChange.RECAPTURE,
                        reset_behavior=cuda_graphs.ResetBehavior.IGNORE,
                    )
                elif name in [
                    "positions",
                    "tokens_history",
                    "cache_idxs",
                    "cache_pos",
                    "cumulative_round_pos",
                ]:
                    kwarg_specs[name] = cuda_graphs.ArgumentSpec(
                        batch_dim=None,
                        pre_alloc_tensor=None,
                        on_data_ptr_change=cuda_graphs.OnDataPtrChange.COPY,
                        reset_behavior=cuda_graphs.ResetBehavior.RESET_ON_CALL,  # default
                    )
                else:
                    raise NotImplementedError(
                        f"The {name} tensor in the attention object is unknown. Likely "
                        "you want to add it to the kwarg_specs with OnDataPtrChange.COPY."
                    )

            def synch_fn():
                if self._num_processes > 1:
                    torch.distributed.barrier()  # type: ignore
                torch.cuda.synchronize()

            # Here we assume that the behavior of the attention object is
            # deterministic in the tensors it returns in the get_tensors_for_cuda_graph
            # call. By passing these arguments explicitly in this call, we can ensure
            # that the cuda graph library can handle the input tensors correctly. That
            # might include copying some of the input tensors that have changed their
            # data pointers or recapturing the graph, depending on the argument specs.
            #
            # It is intentional that the kwargs are unused.
            def graphable_step_fn(
                tokens: torch.Tensor,
                **kwargs: torch.Tensor,  # pylint: disable=unused-argument
            ) -> torch.Tensor:
                # We bind the attention object here.
                return self._step_fn(tokens, attn)

            self._graphed_step_fn_by_attn[attn] = (
                cuda_graphs.GraphedFunctionRecapturable(
                    fn=graphable_step_fn,
                    kw_argument_specs=kwarg_specs,
                    batch_sizes=self._batch_sizes,
                    pool=self._shared_graph_pool,
                    synch_fn=synch_fn,
                    process_idx=self._process_idx,
                    num_processes=self._num_processes,
                )
            )
        out = self._graphed_step_fn_by_attn[attn](emb, **attn_tensors)
        return out
