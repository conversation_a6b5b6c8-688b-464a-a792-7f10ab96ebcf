"""Shared definitions between different forwards."""

import abc
import enum
import typing
from dataclasses import dataclass
from typing import Callable, Literal, Protocol, Sequence, Type, TypeVar

from base.fastforward import cached_attention

# TODO(markus): make "no_extension" the default; update deepseek v1 model configs
RotaryExtensionMethod = Literal["no_extension", "deepseek_v1", "yarn", "llama3_1"]


# TODO(arun): specialize this dataclass per model type (starcoder, codegen, etc.)
@dataclass
class ModelSpec:
    """Details needed to load a model from a checkpoint."""

    name: str
    """The name of the model."""

    checkpoint_path: str
    """The path to the checkpoint."""

    num_layers: int
    """The number of layers in the model."""

    vocab_size: int
    """The size of the vocabulary."""

    emb_dim: int
    """The hidden dimension of the model as well as the embedding dimension."""

    num_heads: int
    """The number of attention key/value heads."""

    head_dim: int
    """The hidden dimension of each attention head."""

    output_projection_dim: int | None = None
    """If not None, the output projection will be a linear layer with this dimension.

    This should be not None iff the model is an embedding model, as it is used in lieu of
    the vocab_size. StarCoder1 models derive this from the checkpoint weights, but other
    models should specify the output projection dimension in the model spec itself.
    """

    rotary_pct: float = 0.0
    """Rotary percentage used with ROPE embeddings."""

    rotary_theta: float = 1e4
    """Base power for rope."""

    rotary_scaling_factor: float = 1.0
    """Scaling factor for RoPE extension."""

    rotary_extension_method: RotaryExtensionMethod = "deepseek_v1"
    """Method for RoPE extension.

    One of:
    - "no_extension": Will fail if `rotary_scaling_factor` is not 1.0
    - "deepseek_v1": Position interpolation.
    - "yarn": YaRN.
    - "llama3_1": NTK-by-part.
    """

    beta_fast: int = 32
    """Rotation count of the higher-frequency end of the YaRN ramp."""

    beta_slow: int = 1
    """Rotation count of the lower-frequency end of the YaRN ramp."""

    rotary_interleave: bool = True
    """Whether the q/k tensors are organized in an interleaved or sequential way to be rotated."""

    max_position_embeddings: int = 8192
    """Max position size used with absolute position embeddings."""

    unscaled_max_position_embeddings: int = 0
    """Original max position size during training.

    This parameter is necessary to compute the ramp limits of YaRN. If 0, it will be
    inferred from `max_position_embeddings` and `rotary_scaling_factor`.
    """

    norm_eps: float = 1e-5
    """The epsilon value for normalization layers."""

    # The SHA-256 hash of the checkpoint stored at `checkpoint_path`
    checkpoint_sha256: str | None = None

    def __post_init__(self):
        assert self.emb_dim % self.num_heads == 0
        assert (self.emb_dim // self.num_heads) % self.head_dim == 0
        assert self.rotary_extension_method in typing.get_args(RotaryExtensionMethod)
        if self.rotary_extension_method == "no_extension":
            assert self.rotary_scaling_factor == 1.0


T = TypeVar("T")


class GenericTensor(Protocol):
    """A generic tensor protocal that defines the interface.

    This is a generic tensor protocal will be used as an interface for the interaction between FWD and other production libraries, and thus helps:
    - decoupling the underling deep learning framework (such as PyTorch, JAX, etc.) from the production system.

    The checked_cast method is used to cast the tensor to a specific type, which helps the type safety.
    """

    def __getitem__(self, arg_slice: slice | None) -> "GenericTensor":
        raise NotImplementedError()

    def __len__(self) -> int:
        raise NotImplementedError()

    @property
    def ndim(self) -> int:
        """The number of dimensions of the tensor."""
        raise NotImplementedError()

    def checked_cast(self, type: Type[T]) -> T:
        """Check if this tensor is of type `type` and return its raw tensor."""
        raise NotImplementedError()


class Logits2D(GenericTensor):
    """A protocal for a 2D tensor of logits."""

    def __getitem__(self, arg_slice: slice | None) -> "Logits2D":
        raise NotImplementedError()


class Embedding(GenericTensor):
    """A protocal for the embeddings tensor."""

    def __getitem__(self, arg_slice: slice | None) -> "Embedding":
        raise NotImplementedError()


ModelOutput = Logits2D | Embedding
ForwardStepFn = Callable[[Sequence[int], cached_attention.Attention], ModelOutput]


# TODO(Xuanyi): we should be able to remove this later.
class OutputTensorType(enum.Enum):
    """Specifies which tensor to return from the model."""

    VOCAB_LOGITS = 1  # used for generation
    EMBEDDING = 2  # used for retrieval


class AttentionFactory(metaclass=abc.ABCMeta):
    """Factory for creating Attention objects."""

    def __call__(self, max_length: int) -> cached_attention.BasicAttention:
        """Legacy interface, as this class used to be a simple function."""
        mc_attn = self.create_cache_pool(max_length=max_length, num_attention_caches=1)
        return cached_attention.BasicAttention(mc_attn)

    @abc.abstractmethod
    def create_cache_pool(
        self,
        max_length: int,
        num_attention_caches: int,
    ) -> cached_attention.MultiCacheAttention:
        pass
