#include <c10/cuda/CUDAStream.h>
#include <cuda.h>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <cuda_fp8.h>
#include <torch/extension.h>

namespace compiled_rotary_qkv {

// PairT is a helper struct to load a pair of values in a single instruction.
// TODO: this could plausibly be pulled out into a helper library if we need it more.
template <typename T>
struct PairT {};

template <>
struct PairT<at::Half> {
    using type = __half2;

    static inline __device__ __half2 make_pair(float x, float y) {
        return __floats2half2_rn(x, y);
    }
};

template <>
struct PairT<at::BFloat16> {
    using type = __nv_bfloat162;

    static inline __device__ __nv_bfloat162 make_pair(float x, float y) {
        return __floats2bfloat162_rn(x, y);
    }
};

template <>
struct PairT<float> {
    using type = float2;

    static inline __device__ float2 make_pair(float x, float y) {
        return make_float2(x, y);
    }
};

template <>
struct PairT<double> {
    using type = double2;

    static inline __device__ double2 make_pair(float x, float y) {
        return make_double2(x, y);
    }
};

template <>
struct PairT<__nv_fp8_e4m3> {
    using type = __nv_fp8x2_e4m3;

    static inline __device__ __nv_fp8x2_e4m3 make_pair(float x, float y) {
        return __nv_fp8x2_e4m3(make_float2(x, y));
    }
};


template <typename SrcT, typename DstT>
__device__ void apply_rotary(
    const SrcT* source,
    const int source_head_start,
    DstT* destination,
    const int dest_head_start,
    const int head_offset,
    const float* freqs_cos,
    const float* freqs_sin,
    const int my_pos,
    bool rotate,
    float cos,
    float sin,
    float scale_factor
) {
    /*
    This function applies the rotary transform to a pair of values.
    */
    float x_real, x_imag;
    constexpr int src_load_ratio = sizeof(typename PairT<SrcT>::type) / sizeof(SrcT);
    constexpr int dst_store_ratio = sizeof(typename PairT<DstT>::type) / sizeof(DstT);
    // Unfortuntely, kv-caching can give weird alignments, so we need to handle the case where a
    // coalesced pairwise load is invalid.
    if (reinterpret_cast<std::uintptr_t>(source) % sizeof(typename PairT<SrcT>::type) != 0) {
        x_real = static_cast<float>(source[source_head_start + head_offset]);
        x_imag = static_cast<float>(source[source_head_start + head_offset + 1]);
    } else {
        // Load a single PairT, then pull out the two T's and cast them to float.
        typename PairT<SrcT>::type x_pair = reinterpret_cast<const typename PairT<SrcT>::type*>(
            source)[(source_head_start + head_offset) / src_load_ratio];
        x_real = static_cast<float>(x_pair.x);
        x_imag = static_cast<float>(x_pair.y);
    }

    float real_result = x_real;
    float imag_result = x_imag;
    if (rotate) {
        float cos_real = cos * x_real;
        float sin_imag = sin * x_imag;
        float cos_imag = cos * x_imag;
        float sin_real = sin * x_real;
        real_result = cos_real - sin_imag;
        imag_result = cos_imag + sin_real;
    }

    // Reverse of loading: fill a PairT, then store the two T's.
    typename PairT<DstT>::type result_pair = PairT<DstT>::make_pair(real_result * scale_factor, imag_result * scale_factor);
    reinterpret_cast<typename PairT<DstT>::type*>(destination)[(dest_head_start + head_offset) / dst_store_ratio] = result_pair;
};

enum class QKV {
    Query,
    Key,
    Value
};

template <typename SrcT, typename DstT>
__device__ void rotary_embed_single_head(
    const SrcT* source,
    const float* freqs_cos,
    const float* freqs_sin,
    const int* pos,
    const int* kv_cache_idxs,
    DstT* destination,
    dim3 source_strides,
    dim3 dest_strides,
    int q_head_idx,
    int head_offset,
    int my_pos,
    float cos,
    float sin,
    bool dim_is_rope,
    QKV data_type,
    float scale_factor
) {
    /*
    This function determines the read and write locations and calls into the `apply_rotary` function.
    */
    int head_start;
    if (data_type == QKV::Query) {
        // query (seq_len, kv_heads, queries_per_head, head_dim)
        head_start = blockIdx.x * source_strides.x + blockIdx.y * source_strides.y + q_head_idx * source_strides.z;
    } else {  // assert is_key || is_value;
        // both key and value have their separate tensors and only one head per KV head, so they don't have a second head dim.
        head_start = blockIdx.x * source_strides.x + blockIdx.y * source_strides.y;
    }

    int output_start;
    if (data_type == QKV::Query) {
        output_start = blockIdx.x * dest_strides.x + blockIdx.y * dest_strides.y +
                        q_head_idx * dest_strides.z;
    } else {  // assert is_key || is_value;
        // both key and value have dim (cache_size, cache_seq_len, kv_heads, head_dim)
        int my_cache_idx = kv_cache_idxs[blockIdx.x];
        output_start = (
            my_cache_idx * dest_strides.x +  // cache index
            my_pos * dest_strides.y +  // x is the cache seq len
            blockIdx.y * dest_strides.z  // y is the kv head
        );
    }

    apply_rotary<SrcT, DstT>(
        source,
        head_start,
        destination,
        output_start,
        head_offset,
        freqs_cos,
        freqs_sin,
        my_pos,
        dim_is_rope,
        cos,
        sin,
        scale_factor
    );
}


// See `rotary_embed_qkv` in `base/fastforward/compiled_rotary_qkv.cpp`.
template <typename SrcT, typename DstT>
__global__ void rotary_embed_qkv_kernel(
    const SrcT* query,
    const int* q_pos_idxs,
    const int q_size,
    const SrcT* key,
    const SrcT* value,
    const float* freqs_cos,
    const float* freqs_sin,
    const int* kv_pos_idxs,
    const int* kv_cache_idxs,
    DstT* q_out,
    DstT* k_out,
    DstT* v_out,
    dim3 query_strides,
    dim3 key_strides,
    dim3 value_strides,
    dim3 q_out_strides,
    dim3 k_out_strides,
    dim3 v_out_strides,
    int ropedim,
    int queries_per_kv_head,
    const float* qkv_scales
) {
    /*
    This function pre-calculates some values that are needed across all query heads. In
    particular this avoids reloading cos and sin values over and over again. Then it calls
    `rotary_embed_single_head` for each query head and also for the key and value.
    */
    int head_offset = 2 * threadIdx.x;  // Per-thread offset into this head.
    int freqs_stride = ropedim / 2;
    bool dim_is_rope = (head_offset + 1 < ropedim);
    float q_scale = qkv_scales[0];
    float k_scale = qkv_scales[1];
    float v_scale = qkv_scales[2];

    // Query
    if (blockIdx.x < q_size) {  // Only process queries for the first q_size blocks.
        int my_pos_q = q_pos_idxs[blockIdx.x];       // Actual position index.

        float cos = freqs_cos[my_pos_q * freqs_stride + threadIdx.x];
        float sin = freqs_sin[my_pos_q * freqs_stride + threadIdx.x];

        #pragma unroll 16
        for (int q_head_idx = 0; q_head_idx < queries_per_kv_head; q_head_idx++) {
            rotary_embed_single_head(
                query,  // source
                freqs_cos,
                freqs_sin,
                q_pos_idxs,
                kv_cache_idxs,
                q_out,  // destination
                query_strides,
                q_out_strides,
                q_head_idx,
                head_offset,
                my_pos_q,
                cos,
                sin,
                dim_is_rope,
                QKV::Query,
                q_scale
            );
        }
    }

    // Key and Value
    // Call function for keys and values separately so that the compiler can specialize.
    int my_pos_kv = kv_pos_idxs[blockIdx.x];       // Actual position index.
    float cos = freqs_cos[my_pos_kv * freqs_stride + threadIdx.x];
    float sin = freqs_sin[my_pos_kv * freqs_stride + threadIdx.x];

    // Key
    rotary_embed_single_head(
        key,  // source
        freqs_cos,
        freqs_sin,
        kv_pos_idxs,
        kv_cache_idxs,
        k_out,  // destination
        key_strides,
        k_out_strides,
        -1,  // q_head_idx not needed for keys
        head_offset,
        my_pos_kv,
        cos,
        sin,
        dim_is_rope,
        QKV::Key,
        k_scale
    );
    // Value
    rotary_embed_single_head(
        value,  // source
        freqs_cos,
        freqs_sin,
        kv_pos_idxs,
        kv_cache_idxs,
        v_out,  // destination
        value_strides,
        v_out_strides,
        -1,  // q_head_idx not needed for values
        head_offset,
        my_pos_kv,
        cos,
        sin,
        false,  // don't apply rotatary to value
        QKV::Value,
        v_scale
    );
}

// Helper function to call into the rope kernel. The parallelism approach is:
//   - One thread block per token per KV head.
//   - Each thread iterates through the query heads, then processes the key and value for this KV head.
//   - Each thread reads two activations per head (real and imaginary part).
torch::Tensor rotary_embed_qkv_cuda(
    torch::Tensor& q,
    torch::Tensor& q_pos_idxs,
    torch::Tensor& k,
    torch::Tensor& v,
    torch::Tensor& freqs_cos,
    torch::Tensor& freqs_sin,
    torch::Tensor& kv_pos_idxs,
    torch::Tensor& k_dest,
    torch::Tensor& v_dest,
    torch::Tensor& kv_cache_idxs,
    torch::Tensor& qkv_scales
) {
    TORCH_CHECK(k_dest.dtype() == v_dest.dtype());
    bool result_is_fp8 = k_dest.dtype() == torch::kFloat8_e4m3fn;
    torch::Tensor q_dest;
    // If output is fp8, then q_dest has a different dtype than input q.
    if (result_is_fp8) {
        q_dest = torch::empty_like(q, torch::TensorOptions().dtype(torch::kFloat8_e4m3fn));
    } else {
        q_dest = torch::empty_like(q);
    }
    // query
    TORCH_CHECK(q.dim() == 3 || q.dim() == 4);
    auto query_dest_view = q_dest;
    // If q has one head dimension, add a dummy dimension to make indexing easier.
    if (q.dim() == 3) {
        q = q.unsqueeze(2);  // Add the (optional) second head dimension.
        query_dest_view = q_dest.view_as(q);
    }
    TORCH_CHECK(q.dim() == 4);
    TORCH_CHECK(query_dest_view.dim() == 4);
    dim3 q_strides(q.stride(0), q.stride(1), q.stride(2));
    dim3 query_dest_strides(query_dest_view.stride(0), query_dest_view.stride(1), query_dest_view.stride(2));
    TORCH_CHECK(q.stride(3) == 1);
    TORCH_CHECK(query_dest_view.stride(3) == 1);

    // key - apply rotary like for query
    TORCH_CHECK(k.dim() == 3);
    dim3 k_strides(k.stride(0), k.stride(1), k.stride(2));
    TORCH_CHECK(k.stride(2) == 1);
    TORCH_CHECK(k_dest.dim() == 4);
    dim3 k_dest_strides(k_dest.stride(0), k_dest.stride(1), k_dest.stride(2));
    TORCH_CHECK(k_dest.stride(3) == 1);

    // value - no rotary, just copy to destination
    TORCH_CHECK(v.dim() == 3);
    dim3 v_strides(v.stride(0), v.stride(1), v.stride(2));
    TORCH_CHECK(v.stride(2) == 1);
    TORCH_CHECK(v_dest.dim() == 4);
    dim3 v_dest_strides(v_dest.stride(0), v_dest.stride(1), v_dest.stride(2));
    TORCH_CHECK(v_dest.stride(3) == 1);

    int headdim = q.size(-1);
    TORCH_CHECK(headdim % 2 == 0, "headdim must be even.");
    int ropedim = freqs_cos.size(1) * 2;

    int queries_per_kv_head = q.size(2);

    TORCH_CHECK(q.size(0) <= k.size(0));
    int num_tokens_in_round = k.size(0);

    dim3 num_blocks(
        num_tokens_in_round,  // x: tokens in round
        q.size(1),  // y: kv heads
        1);  // we iterate over qkv_blocks
    int num_threads = headdim / 2;
    // Instantiates kernel for fp64, fp32, fp16, and bf16.
    AT_DISPATCH_FLOATING_TYPES_AND2(
        at::ScalarType::Half, at::ScalarType::BFloat16, q.scalar_type(), "rotary_embed_qkv_kernel",
        ([&] {
            if (result_is_fp8) {
                rotary_embed_qkv_kernel<scalar_t, __nv_fp8_e4m3>
                    <<<num_blocks, num_threads, 0, at::cuda::getCurrentCUDAStream().stream()>>>(
                        q.data_ptr<scalar_t>(),
                        q_pos_idxs.data_ptr<int>(),
                        q.size(0),
                        k.data_ptr<scalar_t>(),
                        v.data_ptr<scalar_t>(),
                        freqs_cos.data_ptr<float>(),
                        freqs_sin.data_ptr<float>(),
                        kv_pos_idxs.data_ptr<int>(),
                        kv_cache_idxs.data_ptr<int>(),
                        reinterpret_cast<__nv_fp8_e4m3*>(q_dest.data_ptr()),
                        reinterpret_cast<__nv_fp8_e4m3*>(k_dest.data_ptr()),
                        reinterpret_cast<__nv_fp8_e4m3*>(v_dest.data_ptr()),
                        q_strides,
                        k_strides,
                        v_strides,
                        query_dest_strides,
                        k_dest_strides,
                        v_dest_strides,
                        ropedim,
                        queries_per_kv_head,
                        qkv_scales.data_ptr<float>());
            } else {
                rotary_embed_qkv_kernel<scalar_t, scalar_t>
                    <<<num_blocks, num_threads, 0, at::cuda::getCurrentCUDAStream().stream()>>>(
                        q.data_ptr<scalar_t>(),
                        q_pos_idxs.data_ptr<int>(),
                        q.size(0),
                        k.data_ptr<scalar_t>(),
                        v.data_ptr<scalar_t>(),
                        freqs_cos.data_ptr<float>(),
                        freqs_sin.data_ptr<float>(),
                        kv_pos_idxs.data_ptr<int>(),
                        kv_cache_idxs.data_ptr<int>(),
                        q_dest.data_ptr<scalar_t>(),
                        k_dest.data_ptr<scalar_t>(),
                        v_dest.data_ptr<scalar_t>(),
                        q_strides,
                        k_strides,
                        v_strides,
                        query_dest_strides,
                        k_dest_strides,
                        v_dest_strides,
                        ropedim,
                        queries_per_kv_head,
                        qkv_scales.data_ptr<float>());
            }
        }));
    return q_dest;
}

}  // namespace compiled_rotary_qkv
