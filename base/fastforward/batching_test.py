"""Tests for the batching module."""

import pickle

import pytest
import torch

from base.fastforward import (
    batching,
    cached_attention,
    fwd,
)


@pytest.mark.parametrize("original_size, expected", ((1, 1), (2, 2), (3, 4), (4, 4)))
def test_choose_round_size(original_size: int, expected: int):
    round_sizes = [1, 2, 4]
    assert batching.choose_round_size(round_sizes, original_size) == expected


def test_choose_round_size_zero():
    """Test that chose round size returns the first round size when original_size is zero."""
    assert batching.choose_round_size([1, 2], 0) == 1


def test_choose_round_size_empty():
    """Test that choose round size raises ValueError when list of round sizes is empty."""
    with pytest.raises(ValueError):
        _ = batching.choose_round_size([], 1)


def test_choose_round_size_overflow():
    """Test that choose round size raises ValueError when original_size too large."""
    with pytest.raises(ValueError):
        _ = batching.choose_round_size([1], 2)


def test_choose_round_size_overflow_allowed():
    """Test that choose round size raises ValueError when original_size is too large."""
    chosen_size = batching.choose_round_size([1, 2], 3, allow_overflow=True)
    assert chosen_size == 2


def test_round_attention_single_request(testmodel_fixture):
    """Test that a round with a single request is not padded."""
    step_fn, attention_factory, max_len = testmodel_fixture
    attn = attention_factory.create_cache_pool(
        max_length=max_len, num_attention_caches=1
    )
    basic_attn = attention_factory(max_length=max_len)

    round_tokens = [1, 2, 3]
    basic_result = step_fn(round_tokens, basic_attn).checked_cast(
        torch.Tensor
    )  # we expect the round wrapper to keep the result unchanged

    round_requests = [
        batching.RequestInRound(
            num_tokens=3,
            cache_idx=0,
            round_start_idx=0,
        )
    ]

    round_attn = batching.RoundAttention(
        max_round_size=3,
        mc_attn=attn,
    )
    round_ = batching.Round(tokens=round_tokens, requests_in_round=round_requests)
    round_attn.load_requests(round_)
    round_result = step_fn(round_tokens, round_attn).checked_cast(torch.Tensor)
    torch.testing.assert_close(round_result, basic_result)


def test_round_attention_multiple_requests(testmodel_fixture):
    """Test that a round with two requests returns the same result as each request individually."""
    step_fn, attention_factory, max_len = testmodel_fixture
    basic_attn = attention_factory(max_length=max_len)

    tokens1 = [1, 2, 3]
    basic_result1 = step_fn(tokens1, basic_attn).checked_cast(torch.Tensor)
    basic_attn.reset()
    tokens2 = [4, 5, 6]
    basic_result2 = step_fn(tokens2, basic_attn).checked_cast(torch.Tensor)

    # reproduce the same results with RoundAttention
    mc_attn = attention_factory.create_cache_pool(
        max_length=max_len, num_attention_caches=2
    )
    round_attn = batching.RoundAttention(
        max_round_size=max_len,
        mc_attn=mc_attn,
    )
    round_requests = [
        batching.RequestInRound(
            num_tokens=3,
            cache_idx=0,
            round_start_idx=0,
        ),
        batching.RequestInRound(
            num_tokens=3,
            cache_idx=1,
            round_start_idx=3,
        ),
    ]

    round_ = batching.Round(tokens=tokens1 + tokens2, requests_in_round=round_requests)
    round_attn.load_requests(round_)
    round_result = step_fn(round_.tokens, round_attn).checked_cast(torch.Tensor)
    torch.testing.assert_close(round_result[:3], basic_result1)
    torch.testing.assert_close(round_result[3:], basic_result2)


def test_round_attention_padding(testmodel_fixture):
    """Test that a padded round with a single request computes the same result as the unpadded one."""
    step_fn, attention_factory, max_len = testmodel_fixture
    attn = attention_factory.create_cache_pool(
        max_length=max_len, num_attention_caches=1
    )
    basic_attn = attention_factory(max_length=max_len)

    tokens = [1, 2, 3]
    # we expect that batching with RoundAttention will keep the result unchanged
    basic_result = step_fn(tokens, basic_attn).checked_cast(torch.Tensor)

    round_requests = [
        batching.RequestInRound(
            num_tokens=3,
            cache_idx=0,
            round_start_idx=0,
        ),
    ]
    round_ = batching.Round(
        tokens=tokens,
        requests_in_round=round_requests,
    )
    padded_round = round_.pad_to_next_round_size(
        round_sizes=[2, 8, 128],
    )
    assert padded_round.has_token_padding
    padded_token_length = len(padded_round.tokens)
    assert padded_token_length == 8

    round_attn = batching.RoundAttention(
        max_round_size=max_len,
        mc_attn=attn,
    )
    round_attn.load_requests(padded_round)
    round_result = step_fn(tokens, round_attn).checked_cast(torch.Tensor)
    torch.testing.assert_close(round_result[:3], basic_result)


def test_pad_empty_round():
    """Test that we can pad an empty round."""
    round_ = batching.Round(tokens=[], requests_in_round=[])
    padded_round = round_.pad_to_next_round_size(
        round_sizes=[2, 8, 128],
    )
    assert padded_round.has_token_padding
    assert len(padded_round.tokens) == 2

    sorted_round, old_to_new_idxs = (
        round_.sort_and_extend_requests_for_multirequest_attention(
            max_requests_in_round=4,
            max_small_request_size=2,
        )
    )
    assert not sorted_round.has_token_padding
    assert sorted_round.has_request_padding
    assert sorted_round.is_multirequest_sorted
    assert len(sorted_round.tokens) == 0
    assert len(sorted_round.requests_in_round) == 4
    assert len(old_to_new_idxs) == 0

    sorted_padded_round = sorted_round.pad_to_next_round_size(
        round_sizes=[2, 8, 128],
    )
    assert sorted_padded_round.has_token_padding
    assert sorted_padded_round.has_request_padding
    assert sorted_padded_round.is_multirequest_sorted
    assert len(sorted_padded_round.tokens) == 2
    assert len(sorted_padded_round.requests_in_round) == 4


def test_simple_sort_and_pad():
    """Manually check the result of sorting and padding a round."""
    round_ = batching.Round(
        tokens=[5, 6, 1, 2, 3, 4],
        requests_in_round=[
            batching.RequestInRound(
                num_tokens=2,
                cache_idx=1,
                round_start_idx=0,
            ),
            batching.RequestInRound(
                num_tokens=4,
                cache_idx=2,
                round_start_idx=2,
            ),
        ],
    )
    sorted_round, old_to_new_idxs = (
        round_.sort_and_extend_requests_for_multirequest_attention(
            max_requests_in_round=4,
            max_small_request_size=2,
        )
    )
    assert sorted_round.is_multirequest_sorted
    assert sorted_round.has_request_padding
    assert not sorted_round.has_token_padding
    assert len(sorted_round.tokens) == 6
    assert sorted_round.tokens == [1, 2, 3, 4, 5, 6]
    assert len(sorted_round.requests_in_round) == 4
    # Manually checking:
    # - Longer request moved to the front, followed by shorter request
    # - Followed by two dummy requests
    reqs = sorted_round.requests_in_round
    assert reqs[0].num_tokens == 4
    assert reqs[0].cache_idx == 2
    assert reqs[0].round_start_idx == 0
    assert reqs[1].num_tokens == 2
    assert reqs[1].cache_idx == 1
    assert reqs[1].round_start_idx == 4
    for i in [2, 3]:
        assert reqs[i].num_tokens == 0
        assert reqs[i].cache_idx == 0
        assert reqs[i].round_start_idx == 6
    assert old_to_new_idxs == [1, 0]

    sorted_padded_round = sorted_round.pad_to_next_round_size(
        round_sizes=[2, 10],
    )
    assert sorted_padded_round.is_multirequest_sorted
    assert sorted_padded_round.has_request_padding
    assert sorted_padded_round.has_token_padding
    assert len(sorted_padded_round.tokens) == 10
    assert sorted_padded_round.tokens == [1, 2, 3, 4, 5, 6, 0, 0, 0, 0]
    assert len(sorted_padded_round.requests_in_round) == 4
    for r1, r2 in zip(
        sorted_round.requests_in_round, sorted_padded_round.requests_in_round
    ):
        assert r1.num_tokens == r2.num_tokens
        assert r1.cache_idx == r2.cache_idx
        assert r1.round_start_idx == r2.round_start_idx


def test_invalid_rounds_fail_sort_and_pad():
    """Tests the boundary conditions of invalid sorted rounds."""
    num_requests = 4
    all_requests = []
    tokens_so_far = 0
    for i in range(num_requests):
        this_request_tokens = 2 * (i + 1)
        all_requests.append(
            batching.RequestInRound(
                num_tokens=this_request_tokens,
                cache_idx=i + 1,
                round_start_idx=tokens_so_far,
            )
        )
        tokens_so_far += this_request_tokens

    # This round has 4 requests of sizes [2, 4, 6, 8]
    round_ = batching.Round(
        tokens=list(range(tokens_so_far)),
        requests_in_round=all_requests,
    )
    request_sizes = [request.num_tokens for request in all_requests]
    largest_request = max(request_sizes)
    # Max requests 4 should work
    sorted_round, _ = round_.sort_and_extend_requests_for_multirequest_attention(
        max_requests_in_round=4,
        max_small_request_size=largest_request,
    )
    assert sorted_round.is_multirequest_sorted
    # But 3 requests should fail
    with pytest.raises(ValueError):
        round_.sort_and_extend_requests_for_multirequest_attention(
            max_requests_in_round=3,
            max_small_request_size=largest_request,
        )

    # Small request cutoff at second-largest is fine
    sorted_round, _ = round_.sort_and_extend_requests_for_multirequest_attention(
        max_requests_in_round=4,
        max_small_request_size=request_sizes[-2],
    )
    # But small request cutoff at third-largest should fail
    with pytest.raises(ValueError):
        round_.sort_and_extend_requests_for_multirequest_attention(
            max_requests_in_round=4,
            max_small_request_size=request_sizes[-3],
        )

    # Padding a sorted round should succeed
    sorted_and_padded_round = sorted_round.pad_to_next_round_size(
        round_sizes=[32],
    )
    assert sorted_and_padded_round.is_multirequest_sorted
    assert sorted_and_padded_round.has_token_padding
    assert sorted_and_padded_round.has_request_padding
    repadded_round = sorted_round.pad_to_next_round_size(
        round_sizes=[32],
    )
    assert repadded_round.tokens == sorted_and_padded_round.tokens
    assert len(repadded_round.requests_in_round) == len(
        sorted_and_padded_round.requests_in_round
    )


def test_sorted_padding_matches_no_padding_and_unsorted_padding(testmodel_fixture):
    """Test that the logits match for sorted and unsorted batches."""
    step_fn, attention_factory, max_len = testmodel_fixture
    basic_attn = attention_factory(max_length=max_len)
    tokens1 = [1, 2]
    basic_result1 = step_fn(tokens1, basic_attn).checked_cast(torch.Tensor)
    basic_attn.reset()
    tokens2 = [3, 4, 5, 6]
    basic_result2 = step_fn(tokens2, basic_attn).checked_cast(torch.Tensor)

    mc_attn = attention_factory.create_cache_pool(
        max_length=max_len, num_attention_caches=2
    )
    round_attn = batching.RoundAttention(
        max_round_size=max_len,
        mc_attn=mc_attn,
    )
    round_requests = [
        batching.RequestInRound(
            num_tokens=2,
            cache_idx=0,
            round_start_idx=0,
        ),
        batching.RequestInRound(
            num_tokens=4,
            cache_idx=1,
            round_start_idx=2,
        ),
    ]
    # Compute result with a sorted+padded batch
    round_ = batching.Round(tokens=tokens1 + tokens2, requests_in_round=round_requests)
    sorted_round, old_to_new_idxs = (
        round_.sort_and_extend_requests_for_multirequest_attention(
            max_requests_in_round=4,
            max_small_request_size=2,
        )
    )
    sorted_round = sorted_round.pad_to_next_round_size(
        round_sizes=[2, 8, 128],
    )
    round_attn.load_requests(sorted_round)
    sorted_result = step_fn(sorted_round.tokens, round_attn).checked_cast(torch.Tensor)
    # Manually check the results match the unpadded results
    torch.testing.assert_close(sorted_result[4:6], basic_result1)
    torch.testing.assert_close(sorted_result[:4], basic_result2)

    # Then compute the result with a padded (but unsorted) batch
    round_attn.reset(to_position=0)
    padded_round = round_.pad_to_next_round_size(
        round_sizes=[2, 8, 128],
    )
    round_attn.load_requests(padded_round)
    unsorted_result = step_fn(padded_round.tokens, round_attn).checked_cast(
        torch.Tensor
    )
    # Check that we can use the permutation idxs to compare the results
    for old_idx, new_idx in enumerate(old_to_new_idxs):
        padded_request = padded_round.requests_in_round[old_idx]
        sorted_request = sorted_round.requests_in_round[new_idx]
        assert padded_request.num_tokens == sorted_request.num_tokens
        assert padded_request.cache_idx == sorted_request.cache_idx
        torch.testing.assert_close(
            unsorted_result[
                padded_request.round_start_idx : padded_request.round_start_idx
                + padded_request.num_tokens
            ],
            sorted_result[
                sorted_request.round_start_idx : sorted_request.round_start_idx
                + sorted_request.num_tokens
            ],
        )


# NOTE: because both fixtures are parametrized over attention implementation, this test runs the
# full cross product of attention implementations graphed and ungraphed.
def test_round_attention_cuda_graph(
    starcoder_1b_fp16_fixture, starcoder_1b_fp16_graphed_fixture
):
    """Test that loading a model with and without cuda graphs produces the same result."""
    max_len = 128
    step_fn, attention_factory = starcoder_1b_fp16_fixture
    graphed_step_fn, graphed_attention_factory, graphed_batch_sizes = (
        starcoder_1b_fp16_graphed_fixture
    )
    prompt = [1, 2, 3]
    ntokens = len(prompt)

    def get_result(
        step_fn: fwd.ForwardStepFn, attention_factory: fwd.AttentionFactory
    ) -> torch.Tensor:
        attn = attention_factory.create_cache_pool(
            max_length=max_len, num_attention_caches=4
        )
        round_ = batching.Round(
            tokens=prompt,
            requests_in_round=[
                batching.RequestInRound(
                    num_tokens=ntokens,
                    cache_idx=0,
                    round_start_idx=0,
                )
            ],
        )
        target_size_after_padding = 8
        assert target_size_after_padding in graphed_batch_sizes
        is_multirequest_flash = (
            attention_factory.attention_impl
            == cached_attention.AttentionImpl.MULTI_REQUEST_FLASH
        )
        # If we are using multirequest flash attn, we need to extend the round's requests to match
        # the expected number of requests.
        if is_multirequest_flash:
            round_, _ = round_.sort_and_extend_requests_for_multirequest_attention(
                max_requests_in_round=attn.get_max_requests_in_round(),
                max_small_request_size=attn.get_small_request_max_seqlen(),
            )
        padded_round = round_.pad_to_next_round_size(
            round_sizes=[target_size_after_padding],
        )
        assert padded_round.has_token_padding
        assert len(padded_round.tokens) == target_size_after_padding
        # For multirequest flash, need to specify the number of requests in the round.
        round_attention = batching.RoundAttention(
            max_round_size=max_len,
            mc_attn=attn,
        )
        round_attention.load_requests(padded_round)

        tokens = padded_round.tokens
        return step_fn(tokens, round_attention).checked_cast(torch.Tensor)

    result_without_graph = get_result(step_fn, attention_factory)
    result_with_graph = get_result(graphed_step_fn, graphed_attention_factory)
    # Note that different attention implementations handle the padding tokens differently. So:
    # - If the attention implementation is the same, we can compare the full result
    # - Otherwise, we compare only the valid tokens
    if attention_factory.attention_impl == graphed_attention_factory.attention_impl:
        token_slice = slice(None)
    else:
        token_slice = slice(ntokens)
    torch.testing.assert_close(
        result_with_graph[token_slice, :],
        result_without_graph[token_slice, :],
        rtol=1e-3,
        atol=1e-2,
    )


def test_round_attention_is_pickleable():
    """Test that a round attention object is pickleable."""
    mc_attn = cached_attention.MultiCacheAttentionImplementation(
        num_layers=2, num_heads=2, max_len=32, head_dim=5, num_caches=1
    )
    round_attn = batching.RoundAttention(
        max_round_size=128,
        mc_attn=mc_attn,
    )
    rir = batching.RequestInRound(
        num_tokens=3,
        cache_idx=0,
        round_start_idx=0,
    )
    round_attn.load_requests(batching.Round(tokens=[1, 2, 3], requests_in_round=[rir]))
    before_hash = hash(round_attn)
    round_attn_pickled = pickle.loads(pickle.dumps(round_attn))
    assert round_attn_pickled.max_round_size == 128
    assert hash(round_attn_pickled) == before_hash
