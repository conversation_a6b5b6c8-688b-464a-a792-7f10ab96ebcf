"""Simple script to profile FFWD models.

Usage:

python profile_models.py --model_type llama --model_size llama-350m --q_len 128,256,512 --kv_len 1024,2048,4096 --fp8 --cuda_graphs --output_csv results.csv

python profile_models.py --model_type starcoder2 --model_size starcoder2-15b --q_len 128,256,512 --kv_len 1024,2048,4096 --fp8 --cuda_graphs --output_csv results.csv

To obtain NSYS profile:

nsys profile --output="profile_results" --capture-range=cudaProfilerApi --force-overwrite=true --cuda-graph-trace=node  \
python profile_models.py --model_type llama --model_size llama-350m --q_len 128,256,512 --kv_len 1024,2048,4096 --fp8 --cuda_graphs --use_cuda_profiler_api --output_csv results.csv

To install nsys:

sudo dpkg -i /mnt/efs/augment/user/markus/nsightsystems-linux-cli-public-2024.2.1.106-3403790.deb

To run this script, you may need to add the git root to PYTHONPATH:

export PYTHONPATH="/home/<USER>/augment:$PYTHONPATH"

The script now accepts comma-separated lists for q_len and kv_len, and will profile all combinations.
Results are written to a CSV file specified by the --output_csv argument, with kv lengths as columns and q lengths as rows.
"""

import argparse
import csv
import functools
import itertools
import sys
import time
from collections import defaultdict
from typing import List, Sequence

import torch

from base.fastforward import (
    batching,
    cached_attention,
    fwd,
    fwd_utils,
    multirequest_flash_attention,
    positional_embeddings,
)
from base.fastforward.all_reduce import AllReduceImpl
from base.fastforward.cached_attention import AttentionImpl
from base.fastforward.deepseek_v2 import fwd_dsv2
from base.fastforward.deepseek_v2 import model_specs as deepseek_v2_model_specs
from base.fastforward.llama import fwd_llama, fwd_llama_fp8
from base.fastforward.llama import model_specs as llama_model_specs
from base.fastforward.parallel import ParallelConfig
from base.fastforward.starcoder import (
    fwd_starcoder,
    fwd_starcoder2,
    fwd_starcoder2_fp8,
    fwd_starcoder_fp8,
)
from base.fastforward.starcoder import model_specs as starcoder_model_specs
from base.fastforward.torch_utils import cuda_timeit


def parse_int_list(s: str) -> List[int]:
    return [int(x.strip()) for x in s.split(",")]


def load_deepseek_v2_model(
    ms: deepseek_v2_model_specs.DeepSeekV2ModelSpec,
    fp8: bool,
    gpus: int,
    cuda_graphs: bool,
    q_lengths: Sequence[int],
    max_length: int,
    attention_impl: AttentionImpl,
    all_reduce_impl: AllReduceImpl,
    sequence_parallel: bool,
    small_request_max_seqlen: int,
    max_requests_in_round: int,
):
    assert all_reduce_impl == AllReduceImpl.NCCL
    assert not sequence_parallel

    assert not fp8
    del attention_impl  # TODO: wire this through when available
    if max_length > ms.rope.max_position_embeddings:
        ms.rope = positional_embeddings.RotaryConfig(
            rotary_ratio=ms.rope.rotary_ratio,
            rotary_theta=ms.rope.rotary_theta,
            max_position_embeddings=max_length,
            ext_config=ms.rope.ext_config,
            rotary_interleave=ms.rope.rotary_interleave,
        )
    step_fn = fwd_dsv2.generate_step_fn(
        ms,
        dtype=torch.bfloat16,
        num_processes=gpus,
        load_checkpoint_weights=False,
        auto_capture_graphs=cuda_graphs,
        batch_sizes=q_lengths,
    )
    attention_factory = fwd_dsv2.DeepSeekCoderV2AttentionFactory(
        ms=ms,
        num_processes=gpus,
        dtype=torch.bfloat16,
        max_requests_in_round=max_requests_in_round,
        small_request_max_seqlen=small_request_max_seqlen,
    )

    return step_fn, attention_factory


def load_llama_model(
    ms: llama_model_specs.LlamaModelSpec,
    fp8: bool,
    gpus: int,
    cuda_graphs: bool,
    q_lengths: Sequence[int],
    max_length: int,
    attention_impl: AttentionImpl,
    all_reduce_impl: AllReduceImpl,
    sequence_parallel: bool,
    small_request_max_seqlen: int,
    max_requests_in_round: int,
):
    if max_length > ms.max_position_embeddings:
        ms.max_position_embeddings = max_length
        ms.unscaled_max_position_embeddings = int(max_length / ms.rotary_scaling_factor)
    parallel_config = ParallelConfig.from_legacy_config(gpus, sequence_parallel)
    if fp8:
        step_fn = fwd_llama_fp8.generate_step_fn(
            ms,
            parallel_config=parallel_config,
            # TODO(markus): use real weights instead to avoid performance impact.
            # https://www.thonking.ai/p/strangely-matrix-multiplications%20
            load_checkpoint_weights=False,
            auto_capture_graphs=cuda_graphs,
            batch_sizes=q_lengths,
            all_reduce_impl=all_reduce_impl,
        )
    else:
        assert not sequence_parallel
        step_fn = fwd_llama.generate_step_fn(
            ms,
            # TODO(markus): use real weights instead to avoid performance impact.
            # https://www.thonking.ai/p/strangely-matrix-multiplications%20
            load_checkpoint_weights=False,
            auto_capture_graphs=cuda_graphs,
            batch_sizes=q_lengths,
            num_processes=gpus,
            all_reduce_impl=all_reduce_impl,
        )

    attention_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=attention_impl,
        pre_attention_kernel_fusion=True,
        use_register_tokens_kernel=True,
        small_request_max_seqlen=small_request_max_seqlen,
        max_requests_in_round=max_requests_in_round,
    )
    return step_fn, attention_factory


def load_starcoder_model(
    ms: fwd.ModelSpec,
    fp8: bool,
    gpus: int,
    cuda_graphs: bool,
    q_lengths: Sequence[int],
    max_length: int,
    attention_impl: AttentionImpl,
    all_reduce_impl: AllReduceImpl,
    sequence_parallel: bool,
    small_request_max_seqlen: int,
    max_requests_in_round: int,
):
    assert all_reduce_impl == AllReduceImpl.NCCL
    assert not sequence_parallel

    ms.max_position_embeddings = max(ms.max_position_embeddings, max_length)
    if fp8:
        step_fn = fwd_starcoder_fp8.generate_step_fn(
            ms,
            auto_capture_graphs=cuda_graphs,
            batch_sizes=q_lengths,
            num_processes=gpus,
        )
    else:
        step_fn = fwd_starcoder.generate_step_fn(
            ms,
            auto_capture_graphs=cuda_graphs,
            batch_sizes=q_lengths,
            num_processes=gpus,
        )

    attention_factory = fwd_starcoder.StarcoderAttentionFactory(
        model_spec=ms,
        num_processes=gpus,
        attention_impl=attention_impl,
        # pre_attention_kernel_fusion=True,  # not supported because it's not using rope!
        small_request_max_seqlen=small_request_max_seqlen,
        max_requests_in_round=max_requests_in_round,
    )
    return step_fn, attention_factory


def load_starcoder2_model(
    ms: starcoder_model_specs.StarCoder2ModelSpec,
    fp8: bool,
    gpus: int,
    cuda_graphs: bool,
    q_lengths: Sequence[int],
    max_length: int,
    attention_impl: AttentionImpl,
    all_reduce_impl: AllReduceImpl,
    sequence_parallel: bool,
    small_request_max_seqlen: int,
    max_requests_in_round: int,
):
    ms.max_position_embeddings = max(ms.max_position_embeddings, max_length)
    parallel_config = ParallelConfig.from_legacy_config(gpus, sequence_parallel)
    if fp8:
        step_fn = fwd_starcoder2_fp8.generate_step_fn(
            ms,
            load_checkpoint_weights=False,
            auto_capture_graphs=cuda_graphs,
            batch_sizes=q_lengths,
            parallel_config=parallel_config,
            all_reduce_impl=all_reduce_impl,
            small_round_parallel_config=ParallelConfig.from_legacy_config(gpus, False)
            if sequence_parallel
            else None,
            small_round_token_cutoff=128,
        )
    else:
        assert not sequence_parallel
        step_fn = fwd_starcoder2.generate_step_fn(
            ms,
            load_checkpoint_weights=False,
            auto_capture_graphs=cuda_graphs,
            batch_sizes=q_lengths,
            num_processes=gpus,
            all_reduce_impl=all_reduce_impl,
        )

    attention_factory = fwd_starcoder2.StarCoder2AttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=attention_impl,
        pre_attention_kernel_fusion=True,
        use_register_tokens_kernel=True,
        small_request_max_seqlen=small_request_max_seqlen,
        max_requests_in_round=max_requests_in_round,
    )
    return step_fn, attention_factory


def load_model(
    model_type: str,
    model_size: str,
    checkpoint_path: str,
    fp8: bool,
    gpus: int,
    cuda_graphs: bool,
    q_lengths: Sequence[int],
    max_length: int,
    attention_impl: cached_attention.AttentionImpl,
    all_reduce_impl: AllReduceImpl,
    sequence_parallel: bool,
    small_request_max_seqlen: int,
    max_requests_in_round: int,
):
    ms = None
    load_fn = None
    if model_type == "deepseek_v2":
        ms = deepseek_v2_model_specs.get_model_spec(
            model_name=model_size, checkpoint_path=checkpoint_path
        )
        load_fn = load_deepseek_v2_model
    elif model_type == "llama":
        ms = llama_model_specs.get_llama_model_spec(
            model_name=model_size,
            checkpoint_path=checkpoint_path,
        )
        load_fn = load_llama_model
    elif model_type == "starcoder":
        if model_size == "1b" and not fp8:
            ms = fwd_utils.get_model_spec_from_neox_checkpoint(
                "/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint"
            )
        elif model_size == "1b" and fp8:
            ms = fwd_utils.get_model_spec_from_neox_checkpoint(
                "/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint_fp8.pth"
            )
        elif model_size == "16b" and not fp8:
            ms = fwd_utils.get_model_spec_from_neox_checkpoint(
                "/mnt/efs/augment/checkpoints/starcoderbase-16b_neox/checkpoint"
            )
        elif model_size == "16b" and fp8:
            ms = fwd_utils.get_model_spec_from_neox_checkpoint(
                "/mnt/efs/augment/checkpoints/starcoderbase-16b_neox/checkpoint_fp8.pth"
            )
        else:
            raise NotImplementedError(f"model_size not supported; got {model_size}")
        load_fn = load_starcoder_model
    elif model_type == "starcoder2":
        ms = starcoder_model_specs.get_starcoder2_model_spec(
            model_name=model_size, checkpoint_path=checkpoint_path
        )
        load_fn = load_starcoder2_model
    else:
        raise NotImplementedError(f"model_type not supported; got {model_type}")

    return load_fn(
        ms=ms,  # type: ignore
        fp8=fp8,
        gpus=gpus,
        cuda_graphs=cuda_graphs,
        q_lengths=q_lengths,
        max_length=max_length,
        attention_impl=attention_impl,
        all_reduce_impl=all_reduce_impl,
        sequence_parallel=sequence_parallel,
        small_request_max_seqlen=small_request_max_seqlen,
        max_requests_in_round=max_requests_in_round,
    )


def _create_round_to_measure(
    q_len: int,
    num_requests_in_round: int,
    attn: cached_attention.Attention,
) -> batching.Round:
    """Create a round with possibly multiple requests.

    For n = num_requests_in_round, we create:
    - n-1 requests of size min(max_small_request, q_len/n), and
    - one request to fill out the rest of q_len (usually a "big" request for large round sizes).
    """
    assert num_requests_in_round > 0
    max_tokens_small_requests = attn.get_small_request_max_seqlen() or q_len
    assert num_requests_in_round <= max_tokens_small_requests
    requests_in_round = []
    num_tokens_in_round = 0

    for idx in range(num_requests_in_round):
        num_tokens = min(max_tokens_small_requests, q_len // num_requests_in_round)
        if idx == num_requests_in_round - 1:
            num_tokens = q_len - idx * max_tokens_small_requests
        rir = batching.RequestInRound(
            num_tokens=num_tokens,
            cache_idx=idx,
            round_start_idx=num_tokens_in_round,
        )
        requests_in_round.append(rir)
        num_tokens_in_round += num_tokens
    assert num_tokens_in_round == q_len, f"{num_tokens_in_round} != {q_len}"
    measure_round = batching.Round(
        tokens=[0] * q_len,
        requests_in_round=requests_in_round,
    )
    measure_round, _ = (
        measure_round.sort_and_extend_requests_for_multirequest_attention(
            max_requests_in_round=num_requests_in_round,
            max_small_request_size=attn.get_small_request_max_seqlen(),
        )
    )
    return measure_round


def _create_prefill_round(
    num_requests_in_round: int,
    minimum_round_size: int,
    max_tokens_small_requests: int,
) -> batching.Round:
    """Create a round with requests of equal length.

    This is helpful to equally fill the cache for multiple requests.
    """

    requests_in_round = []
    num_tokens_in_round = 0

    if num_requests_in_round == 1:
        num_tokens_per_request = minimum_round_size
    else:
        num_tokens_per_request = max_tokens_small_requests

    for idx in range(num_requests_in_round):
        rir = batching.RequestInRound(
            num_tokens=num_tokens_per_request,
            cache_idx=idx,
            round_start_idx=num_tokens_in_round,
        )
        requests_in_round.append(rir)
        num_tokens_in_round += num_tokens_per_request
    assert num_tokens_in_round == num_requests_in_round * num_tokens_per_request
    prefill_round = batching.Round(
        tokens=[0] * num_tokens_in_round,
        requests_in_round=requests_in_round,
    )
    return prefill_round


def run_profile_round_attention(
    step_fn: fwd.ForwardStepFn,
    attn: batching.RoundAttention,
    fp8: bool,
    q_len: int,
    kv_len: int,
    num_requests_in_round: int,
    minimum_round_size: int,
    do_warmup: bool = True,
):
    attn.reset()

    # We need a different kind of round to prefill the cache so that
    # all requests have a cache of equal length.
    prefill_round = _create_prefill_round(
        num_requests_in_round=num_requests_in_round,
        minimum_round_size=minimum_round_size,
        max_tokens_small_requests=attn.get_small_request_max_seqlen()
        or max(minimum_round_size, 16),
    )
    prefill_round_length = len(prefill_round.tokens)
    if prefill_round_length < minimum_round_size:
        print(
            f"Prefill round length {prefill_round_length} is smaller than minimum round size {minimum_round_size}"
        )
        (
            prefill_round,
            orig_to_sorted_request_idxs,
        ) = prefill_round.sort_and_extend_requests_for_multirequest_attention(
            max_requests_in_round=num_requests_in_round,
            max_small_request_size=attn.get_small_request_max_seqlen(),
        )
        prefill_round = prefill_round.pad_to_next_round_size(
            round_sizes=[minimum_round_size],
        )
    attn.load_requests(prefill_round)

    if do_warmup:
        tokens = prefill_round.tokens
        torch.cuda.nvtx.range_push("profile_warmup")
        step_fn(tokens, attn)
        torch.cuda.nvtx.range_pop()
        attn.reset()
        torch.cuda.synchronize()
        torch.cuda.empty_cache()

    # prefill cache
    prefilled = 0
    prefill_start_time = time.time()
    while prefilled < kv_len:
        # TODO(markus): use real tokens instead to avoid performance impact.
        # https://www.thonking.ai/p/strangely-matrix-multiplications%20
        tokens = prefill_round.tokens
        torch.cuda.nvtx.range_push("profile_prefill")
        step_fn(tokens, attn)
        torch.cuda.nvtx.range_pop()
        prefilled += prefill_round_length // num_requests_in_round

    torch.cuda.synchronize()
    prefill_end_time = time.time()
    prefill_duration = prefill_end_time - prefill_start_time + 1e-6

    print(f"Prefilled {prefilled} tokens in {prefill_duration:.4f} seconds")
    print(f"Prefill rate: {prefilled / prefill_duration:.2f} tokens/second")

    print(f"prefilled {prefilled} tokens")

    # phase 2: ready to measure
    measure_round = _create_round_to_measure(
        q_len=q_len,
        num_requests_in_round=num_requests_in_round,
        attn=attn,
    )

    torch.cuda.nvtx.range_push("profile_load_requests")
    attn.load_requests(measure_round)
    torch.cuda.nvtx.range_pop()

    tokens = measure_round.tokens

    def do_step():
        attn.reset(to_position=prefilled)  # noqa
        torch.cuda.nvtx.range_push("profile_step")
        _ = step_fn(tokens, attn)  # noqa
        torch.cuda.nvtx.range_pop()

    median_latency_ms, _ = cuda_timeit(do_step, warmup_steps=5)

    print(
        f'dtype={"fp8" if fp8 else "fp16":<8}'
        f"q={q_len:<7d}"
        f"kv={kv_len:<7d}"
        f"median_latency_ms={median_latency_ms:0.5f}",
        flush=True,
    )

    torch.cuda.synchronize()
    del attn
    torch.cuda.empty_cache()
    return median_latency_ms


def run_profile_basic_attention(
    step_fn: fwd.ForwardStepFn,
    attn: cached_attention.BasicAttention,
    fp8: bool,
    q_len: int,
    kv_len: int,
    do_warmup: bool = True,
):
    if do_warmup:
        tokens = [0] * q_len
        torch.cuda.nvtx.range_push("profile_warmup")
        step_fn(tokens, attn)
        torch.cuda.nvtx.range_pop()
        attn.reset()
        torch.cuda.synchronize()
        torch.cuda.empty_cache()

    # prefill cache
    remaining_prefill = kv_len
    prefilled = 0
    while remaining_prefill > q_len:
        # TODO(markus): use real tokens instead to avoid performance impact.
        # https://www.thonking.ai/p/strangely-matrix-multiplications%20
        tokens = [0] * q_len
        torch.cuda.nvtx.range_push("profile_prefill")
        step_fn(tokens, attn)
        torch.cuda.nvtx.range_pop()
        remaining_prefill -= q_len
        prefilled += q_len

    tokens = [0] * q_len

    def do_step():
        attn.reset(to_position=prefilled)  # noqa
        torch.cuda.nvtx.range_push("profile_step")
        _ = step_fn(tokens, attn)  # noqa
        torch.cuda.nvtx.range_pop()

    median_latency_ms, _ = cuda_timeit(do_step, warmup_steps=5)

    print(
        f'dtype={"fp8" if fp8 else "fp16":<8}'
        f"q={q_len:<7d}"
        f"kv={kv_len:<7d}"
        f"median_latency_ms={median_latency_ms:0.5f}",
        flush=True,
    )

    torch.cuda.synchronize()
    del attn
    torch.cuda.empty_cache()
    return median_latency_ms


def main():
    parser = argparse.ArgumentParser(
        description="Simple script to profile FFWD models."
    )
    parser.add_argument("--checkpoint_path", type=str, default="")
    parser.add_argument("--model_type", type=str, required=True)
    parser.add_argument("--model_size", type=str, required=True)
    parser.add_argument("--fp8", dest="fp8", default=False, action="store_true")
    parser.add_argument(
        "--cuda_graphs", dest="cuda_graphs", default=False, action="store_true"
    )
    parser.add_argument("--verbose", dest="verbose", default=False, action="store_true")
    parser.add_argument(
        "--q_len", type=str, required=True, help="Comma-separated list of query lengths"
    )
    parser.add_argument(
        "--kv_len",
        type=str,
        required=True,
        help="Comma-separated list of key/value lengths",
    )
    parser.add_argument("--requests_in_round", type=int, default=1)
    parser.add_argument(  # Use BasicAttention instead of RoundAttention
        "--basic_attention",
        dest="basic_attention",
        default=False,
        action="store_true",
    )
    parser.add_argument("--flash_attn_v3", default=False, action="store_true")
    parser.add_argument("--fp8_attn", default=False, action="store_true")
    parser.add_argument("--gpus", type=int, default=1)
    # To be used with nsys flag "--capture-range=cudaProfilerApi"
    parser.add_argument(
        "--use_cuda_profiler_api",
        dest="use_cuda_profiler_api",
        default=False,
        action="store_true",
    )

    parser.add_argument(
        "--output_csv",
        type=argparse.FileType("w"),
        default=sys.stdout,
        help="Path to output csv.",
    )

    parser.add_argument(
        "--all_reduce_impl",
        default="NCCL",
        choices=[e.name for e in AllReduceImpl],
        help="Choice of all reduce implementation.",
    )
    parser.add_argument(
        "--sequence_parallel",
        dest="sequence_parallel",
        default=False,
        action="store_true",
        help="Use sequence parallelism instead of tensor parallelism.",
    )
    parser.add_argument(
        "--small_request_max_seqlen",
        type=int,
        default=multirequest_flash_attention.SMALL_REQUEST_MAX_SEQLEN_DEFAULT,
        help="Maximum sequence length for small requests.",
    )
    parser.add_argument(
        "--max_requests_in_round",
        type=int,
        default=multirequest_flash_attention.MAX_REQUESTS_IN_ROUND_DEFAULT,
        help="Minimum round size for multirequest attention.",
    )

    args = parser.parse_args()

    q_lens = parse_int_list(args.q_len)
    kv_lens = parse_int_list(args.kv_len)

    if args.fp8_attn and not (args.fp8 and args.flash_attn_v3):
        raise ValueError("fp8_attn requires fp8 and flash_attn_v3 to be set. ")

    print("=" * 80, flush=True)

    results = defaultdict(dict)

    num_gpus = torch.cuda.device_count()

    round_sizes = q_lens
    minimum_round_size = min(round_sizes)
    if args.fp8_attn:
        attention_impl = AttentionImpl.MULTI_REQUEST_FLASH_V3_FP8
    elif args.flash_attn_v3:
        attention_impl = AttentionImpl.MULTI_REQUEST_FLASH_V3
    else:
        attention_impl = AttentionImpl.MULTI_REQUEST_FLASH

    max_length = max(kv_lens) + max(q_lens)

    if args.use_cuda_profiler_api:
        for i in range(num_gpus):
            with torch.cuda.device(f"cuda:{i}"):
                torch.cuda.profiler.cudart().cudaProfilerStart()  # type: ignore

    step_fn, attention_factory = load_model(
        model_type=args.model_type,
        model_size=args.model_size,
        checkpoint_path=args.checkpoint_path,
        fp8=args.fp8,
        gpus=args.gpus,
        cuda_graphs=args.cuda_graphs,
        q_lengths=round_sizes,
        max_length=max_length,
        attention_impl=attention_impl,
        all_reduce_impl={e.name: e for e in AllReduceImpl}[args.all_reduce_impl],
        sequence_parallel=args.sequence_parallel,
        small_request_max_seqlen=args.small_request_max_seqlen,
        max_requests_in_round=args.max_requests_in_round,
    )
    max_length = 2 * max(round_sizes) + max_length
    if args.basic_attention:
        attn = attention_factory(max_length=max_length)
        profiling_fn = functools.partial(
            run_profile_basic_attention,
            step_fn=step_fn,
            attn=attn,
            fp8=args.fp8,
        )
    else:
        mc_attn = attention_factory.create_cache_pool(
            max_length=max_length, num_attention_caches=args.requests_in_round
        )
        for i in range(mc_attn.num_caches):
            mc_attn.reset(cache_idx=i)
        attn = batching.RoundAttention(
            max_round_size=max(round_sizes),
            mc_attn=mc_attn,
        )
        profiling_fn = functools.partial(
            run_profile_round_attention,
            step_fn=step_fn,
            attn=attn,
            fp8=args.fp8,
            num_requests_in_round=args.requests_in_round,
            minimum_round_size=minimum_round_size,
        )

    for q_len, kv_len in itertools.product(q_lens, kv_lens):
        median_latency_ms = profiling_fn(q_len=q_len, kv_len=kv_len)
        results[q_len][kv_len] = f"{median_latency_ms:.2f}"

    if args.use_cuda_profiler_api:
        torch.cuda.synchronize()
        for i in range(num_gpus):
            with torch.cuda.device(f"cuda:{i}"):
                torch.cuda.profiler.cudart().cudaProfilerStop()  # type: ignore

    # Write results to CSV
    with args.output_csv as csvfile:
        writer = csv.writer(csvfile)

        # Write header row
        header = ["q_len / kv_len"] + kv_lens
        writer.writerow(header)

        # Write data rows
        for q_len in q_lens:
            row = [q_len] + [results[q_len].get(kv_len, "") for kv_len in kv_lens]
            writer.writerow(row)

    print(f"Results written to {args.output_csv.name}")


if __name__ == "__main__":
    main()
