"""Simple script to profile matrix multiplies in FP8 vs FP16.

Caveats:
- This profiling script is profiling a very small operation (a matrix multiply); this
  operation may not actually saturate a GPU's compute and the speed ups measured here
  may look quite different in a more practical scenario.
"""

import csv
import logging
import sys
from typing import cast

import torch
import torch.nn as nn
import tqdm

from base.fastforward import cuda_graphs, fp8
from base.fastforward.torch_utils import cuda_timeit


def profile(linear: nn.Linear | fp8.FP8Linear, inputs, output_fp8_meta=None):
    # NOTE(arun,markus): We may want to cuda graph many inner loops to get a more
    # accurate measurement.
    if output_fp8_meta:
        op = lambda: linear.forward(  # noqa: E731
            inputs,
            output_fp8_meta=output_fp8_meta,  # type: ignore
        )
    else:
        op = lambda: linear.forward(inputs)  # noqa

    return cuda_timeit(op)


def main():
    logging.basicConfig(level=logging.INFO)
    import argparse

    parser = argparse.ArgumentParser(description="Simple script to profile Starcoder.")
    parser.add_argument("--cg", action="store_true")
    parser.add_argument(
        "--output-csv",
        "-o",
        type=argparse.FileType("w"),
        default=sys.stdout,
        help="Path to output csv",
    )
    args = parser.parse_args()

    writer = csv.writer(args.output_csv)
    writer.writerow(
        [
            "hidden-dim",
            "batch-dim",
            "fp16-linear",
            "fp8-linear(fp16 -> fp16)",
            "fp8-linear(fp8 -> fp16)",
            "fp8-linear(fp8 -> fp8)",
        ]
    )

    max_batch_dim = 8192

    # NOTE(arun): sizes correspond to 1b, 3b, 7b and 16b model hidden dims.
    for hidden_dim in tqdm.tqdm([2048, 2816, 4096, 6144], desc="hidden dim"):
        linear = torch.nn.Linear(
            hidden_dim, hidden_dim, bias=True, dtype=torch.float16, device="cuda"
        )
        inputs = torch.randn(
            max_batch_dim, hidden_dim, dtype=torch.float16, device="cuda"
        )
        fp8_linear = fp8.convert_linear_to_fp8(
            linear,
            inputs.abs().amax(),
            inputs.abs().amax(),  # Don't care about output scale, so re-use the input
            preserve_input=True,
        )
        fp8_inputs = fp8.to_fp8(inputs, fp8_linear.input_fp8_meta, preserve_input=True)
        output_fp8_meta = fp8_linear.input_fp8_meta

        if args.cg:
            linear = cast(
                nn.Linear,
                cuda_graphs.make_graphed_callable(linear, max_batch_size=max_batch_dim),
            )
            fp8_linear = cast(
                fp8.FP8Linear,
                cuda_graphs.make_graphed_callable(
                    fp8_linear, max_batch_size=max_batch_dim
                ),
            )

        for batch_dim in tqdm.tqdm(
            [8, 32, 128, 256, 512, 1024, 2048, 4096, 8192], desc="batch dim"
        ):
            writer.writerow(
                [
                    hidden_dim,
                    batch_dim,
                    profile(linear, inputs[:batch_dim]),
                    profile(fp8_linear, inputs[:batch_dim]),
                    profile(fp8_linear, fp8_inputs[:batch_dim]),
                    profile(
                        fp8_linear,
                        fp8_inputs[:batch_dim],
                        output_fp8_meta=output_fp8_meta,
                    ),
                ]
            )


if __name__ == "__main__":
    main()
