"""Simple script to profile all_reduce (torch distributed and fastforward implementations).

Does not verify the output of all_reduce.

Does not use CUDA graphs. The kernels being timed are launched by one C++ call.

Does not include the time to copy data into the input tensor of fastforward all_reduce.

Recommend running using:
  % bazel run -c opt //base/fastforward/profiling:profile_all_reduce -- \
    -o $HOME/dont-forget-to-name-your-output.csv
"""

import argparse
import csv
import functools
import logging
import sys
from typing import Callable

import torch
import torch.distributed as dist

from base.fastforward import all_reduce, parallel
from base.fastforward.torch_utils import cuda_timeit
from base.static_analysis.common import check_not_none


def torch_all_reduce_factory(
    dim: int, process_idx: int, num_processes: int
) -> Callable[[], float]:
    tensor = torch.ones(dim, dtype=torch.bfloat16, device="cuda")

    def fn() -> float:
        dist.barrier()
        return cuda_timeit(lambda: dist.all_reduce(tensor))[0]

    return fn


def fastforward_all_reduce_factory(
    dim: int, use_one_shot: bool, process_idx: int, num_processes: int
) -> Callable[[], float]:
    assert num_processes == dist.get_world_size()
    assert process_idx == dist.get_rank()
    tensor = torch.ones(dim, dtype=torch.bfloat16, device="cuda")
    all_reduce_kit = all_reduce.AllReduceKit(
        tensor.numel(),
        tensor.dtype,
        process_group=check_not_none(dist.group.WORLD),
    )

    def fn() -> float:
        in_tensor = all_reduce_kit.get_input_tensor(tensor.shape, tensor.dtype)
        in_tensor.copy_(tensor)
        # Make sure all GPUs have set up their input tensor
        dist.barrier()
        return cuda_timeit(
            lambda: all_reduce_kit.all_reduce(out=tensor, one_shot=use_one_shot),
            # inner_steps spreads the cost of the cuda synchronize
            inner_steps=10,
        )[0]

    return fn


def run_parallel(
    fixture: Callable[[int, int], Callable[[], float]], num_processes: int
) -> float:
    with parallel.ParallelRunner(num_processes=num_processes) as runner:
        return runner.initialize(fixture)()


def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser(description="Simple script to profile all_reduce.")
    parser.add_argument(
        "--output-csv",
        "-o",
        type=argparse.FileType("w"),
        default=sys.stdout,
        help="Path to output csv",
    )
    args = parser.parse_args()

    writer = csv.writer(args.output_csv)
    writer.writerow(
        [
            "hidden-dim",
            "q",
            "num_processes",
            "torch-all-reduce",
            "fastforward-all-reduce-one-shot",
            "fastforward-all-reduce-two-shot",
        ]
    )

    device_count = torch.cuda.device_count()

    for hidden_dim, q, num_processes in [
        # Deepseek-33B
        (7168, 32, 2),
        (7168, 512, 2),
        # Starcoder2-15B
        (6144, 32, 2),
        (6144, 1536, 2),
        # Llama-70B
        (8192, 32, 4),
        (8192, 32, 8),
        (8192, 1024, 4),
        (8192, 1024, 8),
        (8192, 2048, 4),
        (8192, 2048, 8),
    ]:
        case = f"{hidden_dim=} {q=} {num_processes=}"
        dim = hidden_dim * q

        if num_processes > device_count:
            print(f"Skipping {case} because num_processes > {device_count=}")
            continue

        print(f"Profiling all_reduce {case}")
        all_reduce_time = run_parallel(
            functools.partial(torch_all_reduce_factory, dim),
            num_processes=num_processes,
        )

        print(f"Profiling fastforward all_reduce one shot {case}")
        fastforward_one_shot_time = run_parallel(
            functools.partial(fastforward_all_reduce_factory, dim, use_one_shot=True),
            num_processes=num_processes,
        )

        print(f"Profiling fastforward all_reduce two shot {case}")
        fastforward_two_shot_time = run_parallel(
            functools.partial(fastforward_all_reduce_factory, dim, use_one_shot=False),
            num_processes=num_processes,
        )
        print(f"Profiling done {case}")

        writer.writerow(
            [
                hidden_dim,
                q,
                num_processes,
                all_reduce_time,
                fastforward_one_shot_time,
                fastforward_two_shot_time,
            ]
        )


if __name__ == "__main__":
    main()
