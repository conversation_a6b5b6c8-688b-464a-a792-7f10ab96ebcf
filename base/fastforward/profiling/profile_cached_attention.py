"""Simple script to profile attention performance."""

import logging
from typing import cast

import torch

import flash_attn

from base.fastforward.torch_utils import cuda_timeit
from base.fastforward import cached_attention


def main():
    logging.basicConfig(level=logging.INFO)
    import argparse

    parser = argparse.ArgumentParser(description="Simple script to profile attention.")
    parser.add_argument("--num_tokens", type=int, required=True)
    parser.add_argument("--num_heads", type=int, required=True)
    parser.add_argument("--head_dim", type=int, required=True)
    parser.add_argument("--queries_per_head", type=int, default=1)
    args = parser.parse_args()

    num_tokens = args.num_tokens
    num_heads = args.num_heads
    head_dim = args.head_dim
    dtype = torch.float16

    torch.manual_seed(31415)
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)

    q = torch.empty(num_tokens, num_heads, head_dim, dtype=dtype, device="cuda")
    k = torch.empty(num_tokens, num_heads, head_dim, dtype=dtype, device="cuda")
    v = torch.empty(num_tokens, num_heads, head_dim, dtype=dtype, device="cuda")

    q.uniform_(-0.1, 0.1)
    k.uniform_(-0.1, 0.1)
    v.uniform_(-0.1, 0.1)

    mc_attn = cached_attention.MultiCacheAttentionImplementation(
        num_layers=1,
        num_heads=num_heads,
        max_len=num_tokens + 1,
        head_dim=head_dim,
        num_caches=1,
        dtype=dtype,
        queries_per_head=args.queries_per_head,
        attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,
    )
    attn = cached_attention.BasicAttention(mc_attn)
    attn.register_tokens_get_positions(
        torch.arange(num_tokens, dtype=torch.int32, device="cuda"), process_idx=0
    )

    qkv = torch.concat([q, k, v], dim=-1).reshape(num_tokens, -1).contiguous()
    median_latency_ms, _ = cuda_timeit(lambda: attn(qkv=qkv, layer_idx=0), repeats=8)
    print(median_latency_ms)


if __name__ == "__main__":
    print(flash_attn.__version__)
    main()
