"""Simple script to profile the MLP layers in FP8 vs FP16."""

import csv
import logging
import sys
from typing import cast

import torch
import torch.nn as nn
import tqdm

from base.fastforward import cuda_graphs, layers, layers_fp8
from base.fastforward.torch_utils import cuda_timeit


def main():
    logging.basicConfig(level=logging.INFO)
    import argparse

    parser = argparse.ArgumentParser(description="Simple script to profile Starcoder.")
    parser.add_argument("--cg", action="store_true")
    parser.add_argument(
        "--output-csv",
        "-o",
        type=argparse.FileType("w"),
        default=sys.stdout,
        help="Path to output csv",
    )
    args = parser.parse_args()

    writer = csv.writer(args.output_csv)
    writer.writerow(
        [
            "model-size",
            "batch-dim",
            "fp16",
            "fp8",
        ]
    )

    max_batch_dim = 8192
    model_sizes = {
        "1b": (16, 128, 16 * 128),
        "3b": (22, 128, 22 * 128),
        "7b": (32, 128, 32 * 128),
        "16b": (48, 128, 48 * 128),
    }

    # NOTE(arun): sizes correspond to 1b, 3b, 7b and 16b model hidden dims.
    for model_name, (num_heads, head_dim, emb_dim) in model_sizes.items():
        h, hd = emb_dim, head_dim
        cross_layer = layers.CrossLayer(
            weight_dense_h_to_4h=torch.randn(
                (h, 4 * h), dtype=torch.float16, device="cuda"
            ),
            weight_dense_4h_to_h=torch.randn(
                (4 * h, h), dtype=torch.float16, device="cuda"
            ),
            bias_dense_h_to_4h=torch.randn(
                (4 * h,), dtype=torch.float16, device="cuda"
            ),
            bias_dense_4h_to_h=torch.randn((h,), dtype=torch.float16, device="cuda"),
            weight_attention_dense=torch.randn(
                (h, h), dtype=torch.float16, device="cuda"
            ),
            bias_attention_dense=torch.randn((h,), dtype=torch.float16, device="cuda"),
            weight_post_attention_layernorm=torch.randn(
                (h,), dtype=torch.float16, device="cuda"
            ),
            bias_post_attention_layernorm=torch.randn(
                (h,), dtype=torch.float16, device="cuda"
            ),
            activation_fn=nn.functional.gelu,
            #
            weight_input_layernorm=torch.randn(
                (h,), dtype=torch.float16, device="cuda"
            ),
            bias_input_layernorm=torch.randn((h,), dtype=torch.float16, device="cuda"),
            weight_attention_query=torch.randn(
                (h, h), dtype=torch.float16, device="cuda"
            ),
            weight_attention_key_value=torch.randn(
                (h, 2 * hd), dtype=torch.float16, device="cuda"
            ),
            bias_attention_query=torch.randn((h,), dtype=torch.float16, device="cuda"),
            bias_attention_key_value=torch.randn(
                (2 * hd,), dtype=torch.float16, device="cuda"
            ),
            #
            layernorm_eps=1e-5,
        )
        fp8_cross_layer = layers_fp8.CrossLayer(
            num_heads,
            head_dim,
            emb_dim,
            layernorm_eps=1e-5,
            device="cuda",
        )
        inputs = torch.randn(
            (max_batch_dim, emb_dim), dtype=torch.float16, device="cuda"
        )
        attn = torch.randn((max_batch_dim, emb_dim), dtype=torch.float16, device="cuda")

        if args.cg:
            cross_layer = cast(
                layers.CrossLayer,
                cuda_graphs.make_graphed_callable(
                    cross_layer, max_batch_size=max_batch_dim
                ),
            )
            fp8_cross_layer = cast(
                layers.CrossLayer,
                cuda_graphs.make_graphed_callable(
                    fp8_cross_layer, max_batch_size=max_batch_dim
                ),
            )

        for batch_dim in tqdm.tqdm(
            [8, 16, 32, 64, 128, 256, 512, 1024, 2048], desc="batch dim"
        ):
            writer.writerow(
                [
                    model_name,
                    batch_dim,
                    cuda_timeit(
                        lambda x=inputs[:batch_dim], y=attn[:batch_dim]: cross_layer(  # noqa
                            x, y
                        ),
                        inner_steps=10,
                    )[0],
                    cuda_timeit(
                        lambda x=inputs[:batch_dim],
                        y=attn[:batch_dim]: fp8_cross_layer(  # noqa
                            x, y
                        ),
                        inner_steps=10,
                    )[0],
                ]
            )
        del cross_layer
        del fp8_cross_layer
        torch.cuda.empty_cache()


if __name__ == "__main__":
    main()
