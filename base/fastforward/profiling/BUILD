load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "profile_models",
    srcs = [
        "profile_models.py",
    ],
    main = "profile_models.py",
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:batching",
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_utils",
        "//base/fastforward:multirequest_flash_attention",
        "//base/fastforward:torch_utils",
        "//base/fastforward/deepseek_v2:fwd_dsv2",
        "//base/fastforward/deepseek_v2:model_specs",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:fwd_llama_fp8",
        "//base/fastforward/llama:model_specs",
        "//base/fastforward/starcoder:fwd_starcoder",
        "//base/fastforward/starcoder:fwd_starcoder2",
        "//base/fastforward/starcoder:fwd_starcoder2_fp8",
        "//base/fastforward/starcoder:fwd_starcoder_fp8",
        "//base/fastforward/starcoder:model_specs",
        requirement("torch"),
    ],
)

py_binary(
    name = "profile_gemm",
    srcs = [
        "profile_gemm.py",
    ],
    main = "profile_gemm.py",
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cuda_graphs",
        "//base/fastforward:fp8",
        "//base/fastforward:torch_utils",
        requirement("torch"),
    ],
)

py_binary(
    name = "profile_mlp",
    srcs = [
        "profile_mlp.py",
    ],
    main = "profile_mlp.py",
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cuda_graphs",
        "//base/fastforward:layers",
        "//base/fastforward:layers_fp8",
        "//base/fastforward:torch_utils",
        requirement("torch"),
        requirement("tqdm"),
    ],
)

py_binary(
    name = "profile_cached_attention",
    srcs = [
        "profile_cached_attention.py",
    ],
    main = "profile_cached_attention.py",
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:multirequest_flash_attention",
        "//base/fastforward:torch_utils",
        requirement("torch"),
    ],
)

py_binary(
    name = "profile_all_reduce",
    srcs = [
        "profile_all_reduce.py",
    ],
    main = "profile_all_reduce.py",
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:all_reduce",
        "//base/fastforward:parallel",
        "//base/fastforward:torch_utils",
        "//base/static_analysis:common",
        requirement("torch"),
    ],
)
