#!/bin/bash
# This script runs the profile_models.py script for various combinations of parameters.

# To run the script in the background, use:
# nohup ./run_profile_models.sh > output.log &

# Define the possible values for various parameters
q_lens="32,64,128,256,512,1024"
kv_lens="0,4096,8192,16384,32768"
gpu_counts=(2 4)
model_types=("llama")
requests_in_rounds=(1)

# Define the command-line arguments
extra_args="--fp8 --cuda_graphs --use_cuda_profiler_api --all_reduce_impl FASTFORWARD --flash_attn_v3"

# Iterate over model types, GPU counts, and requests in round
for model_type in "${model_types[@]}"; do
	if [ "$model_type" == "llama" ]; then
		model_size="llama3_1-8b"
	elif [ "$model_type" == "starcoder2" ]; then
		model_size="starcoder2-15b"
	fi

	for gpus in "${gpu_counts[@]}"; do
		for requests_in_round in "${requests_in_rounds[@]}"; do
			echo "========================================"
			echo "Running tests for:"
			echo "Model: $model_type-$model_size"
			echo "GPUs: $gpus"
			echo "Requests in round: $requests_in_round"
			echo "========================================"

			output_file="results_${model_type}_${model_size}_${gpus}gpus_${requests_in_round}req.csv"

			cmd="python profile_models.py --model_type $model_type --model_size $model_size --q_len $q_lens --kv_len $kv_lens --gpus $gpus --requests_in_round $requests_in_round $extra_args --output_csv $output_file"

			echo "Running command: $cmd"
			$cmd

			echo "Results written to $output_file"
			echo "----------------------------------------"
		done
	done
done
