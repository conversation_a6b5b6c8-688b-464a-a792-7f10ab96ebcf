"""Tests for cuda_graphs.py on multiple GPUs."""

import enum
from typing import Callable

import pytest
import torch
import torch.distributed as dist

from base.fastforward import cuda_graphs, parallel


class Mode(enum.Enum):
    """Enum for test modes."""

    NO_GRAPH = 0
    GRAPH = 1
    RECAPTURE = 2


def compute_fn(z) -> torch.Tensor:
    z = 2 * z
    dist.reduce(z, dst=0, op=dist.ReduceOp.SUM)
    return z


# This function is being called on each device.
# It must be top-level function, or else it cannot be referenced by multiprocessing
def compute_sum_factory(
    graph_mode: Mode, process_idx: int, num_processes: int
) -> Callable[[torch.Tensor, torch.Tensor], torch.Tensor]:
    graph_fn = compute_fn

    if graph_mode == Mode.GRAPH:
        graph_fn = cuda_graphs.GraphedFunction(graph_fn, batch_sizes=[2, 4, 8])
    elif graph_mode == Mode.RECAPTURE:
        # This is the heart of the test. We want to check whether the recapture on data pointer change works
        # in the context of multiple GPUs. The problem here is that the tensor has been moved to another device
        # and the data pointer comparisons needed to store the graphs under a _RecaptureKey in a dictionary
        # may fail.
        kwarg_specs = {
            "z": cuda_graphs.ArgumentSpec(
                batch_dim=None,
                on_data_ptr_change=cuda_graphs.OnDataPtrChange.RECAPTURE,
            ),
        }
        graph_fn = cuda_graphs.GraphedFunctionRecapturable(
            graph_fn,
            batch_sizes=[2, 4, 8],
            kw_argument_specs=kwarg_specs,
            output_spec=[cuda_graphs.OutputSpec(batch_dim=None)],
            process_idx=process_idx,
            num_processes=num_processes,
            wait_to_capture_nccl_graph=False,
        )
    else:
        assert graph_mode == Mode.NO_GRAPH

    def wrapped_fn(x, y):
        if process_idx == 0:
            z = x
        else:
            z = y
        print(f"{process_idx=}: {z.data_ptr()=}", flush=True)
        out = graph_fn(z=z)
        if graph_mode == Mode.RECAPTURE:
            # This check is the heart of this test.
            # Only one graph should be captured - even if this function is called multiple times.
            num_graphs = len(graph_fn._captured_graphs)  # type: ignore
            assert num_graphs == 1, f"{num_graphs=}"
        return out

    return wrapped_fn


@pytest.mark.parametrize("graph_mode", [Mode.GRAPH, Mode.RECAPTURE, Mode.NO_GRAPH])
def test_multigpu_cuda_graph(graph_mode: Mode):
    """Test that a cuda graph can be run on multiple GPUs."""
    fn = parallel.ParallelRunner(num_processes=2).initialize(
        compute_sum_factory, graph_mode=graph_mode
    )
    print("test_multigpu_cuda_graph has initialized parallel runner", flush=True)
    x = torch.tensor([2, 2, 2, 2], dtype=torch.int32, device="cuda:0")
    y = torch.tensor([3, 3, 3, 3], dtype=torch.int32, device="cuda:1")
    expected = torch.tensor([10, 10, 10, 10], dtype=torch.int32, device="cuda:0")

    try:
        result = fn(x, y)
        torch.testing.assert_close(result, expected)
        # calling the function a second time to check whether the cuda graph
        # is reused or capturing is triggered again.
        _ = fn(x, y)
    finally:
        del fn
