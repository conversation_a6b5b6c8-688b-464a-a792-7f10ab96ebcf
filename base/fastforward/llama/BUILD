load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        "//base/fastforward:conftest",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:fwd_llama_fp8",
        "//base/fastforward/llama:model_specs",
        requirement("pytest"),
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_llama",
    srcs = ["fwd_llama.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:all_reduce",
        "//base/fastforward:cached_attention",
        "//base/fastforward:cuda_graphs_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers",
        "//base/fastforward:parallel_fwd",
        "//base/fastforward/checkpoints:save_load",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
        requirement("numpy"),
    ],
)

py_library(
    name = "model_specs",
    srcs = ["model_specs.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
    ],
)

pytest_test(
    name = "fwd_llama_test",
    size = "medium",
    srcs = [
        "fwd_llama_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/checkpoints:save_load",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
        requirement("numpy"),
        requirement("safetensors"),
    ],
)

pytest_test(
    name = "fwd_llama_multigpu_test",
    size = "medium",
    srcs = [
        "fwd_llama_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward/checkpoints:save_load",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_llama_multigpu_cuda_graph_test",
    srcs = [
        "fwd_llama_multigpu_cuda_graph_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_llama_fp8",
    srcs = ["fwd_llama_fp8.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:layers",
        "//base/fastforward:layers_fp8",
        "//base/fastforward:torch_utils",
        "//base/fastforward/checkpoints:save_load",
        "//base/fastforward/llama:fwd_llama",
        requirement("numpy"),
        requirement("torch"),
        requirement("nvtx"),
    ],
)

pytest_test(
    name = "fwd_llama_fp8_test",
    srcs = [
        "fwd_llama_fp8_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        "//base/fastforward:cuda_graphs",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:fwd_llama_fp8",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_llama_fp8_test_h100_kernels",
    srcs = [
        "fwd_llama_fp8_test.py",
    ],
    args = ["--enable-h100-kernels"],
    tags = [
        "exclusive",
        "gpu",
        "large-gpu",
    ],
    deps = [
        "//base/fastforward:cuda_graphs",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:fwd_llama_fp8",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_llama_fp8_multigpu_test",
    size = "medium",
    srcs = [
        "fwd_llama_fp8_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama_fp8",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_llama_fp8_multigpu_cuda_graph_test",
    srcs = [
        "fwd_llama_fp8_multigpu_cuda_graph_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_model_test_utils",
        "//base/fastforward:fwd_utils",
        "//base/fastforward:parallel_fwd",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:fwd_llama_fp8",
        "//base/fastforward/llama:model_specs",
        requirement("torch"),
    ],
)
