"""Tests for ffw_llama."""

from typing import Sequence

import pytest
import torch

from base.fastforward import cached_attention, fwd_model_test_utils
from base.fastforward.llama import fwd_llama, model_specs

_LLAMA_350M_PROMPT_TOKS = fwd_llama.LLAMA_350M_PROMPT_TOKS
_LLAMA_350M_OUTPUT_TOKS = fwd_llama.LLAMA_350M_OUTPUT_TOKS


def test_generate_default(llama_350m_fp16_fixture):
    """Ensure that the model generates the correct outputs."""
    _, step_fn, attn_factory = llama_350m_fp16_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        list(_LLAMA_350M_PROMPT_TOKS),
        list(_LLAMA_350M_OUTPUT_TOKS),
    )


def test_generate_cuda_graph(llama_350m_fp16_graphed_fixture):
    """Ensure that the model generates the correct outputs."""
    _, step_fn, attn_factory = llama_350m_fp16_graphed_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        list(_LLAMA_350M_PROMPT_TOKS),
        list(_LLAMA_350M_OUTPUT_TOKS),
        extra_kv_len=128,
    )


@pytest.mark.parametrize("attention_impl", cached_attention.ALL_ATTN_IMPLS_NO_FA3)
def test_deepseek_33b_smoke(attention_impl: cached_attention.AttentionImpl):
    """Ensures that the DeepSeek-33B model can run."""
    torch.manual_seed(31415)

    ms = model_specs.get_llama_model_spec(
        "deepseek-coder-33b",
        checkpoint_sha256="cb1fd229691e0c5ddc64f59524d37d57f8fdc6311bdd26685bb7a5b62363547e",
    )
    ms.num_layers = 1
    model = fwd_llama.Llama(ms=ms)
    attn = fwd_llama.LlamaAttentionFactory(
        ms=ms, attention_impl=attention_impl, pre_attention_kernel_fusion=True
    )(64)
    with torch.no_grad():
        for p in model.parameters():
            p.uniform_(-0.01, 0.01)

    tokens = torch.randint(
        low=0, high=ms.vocab_size, size=(10,), dtype=torch.int32, device="cuda"
    )
    attn.reset()

    logits = model.forward(tokens.tolist(), attn=attn).checked_cast(torch.Tensor)
    assert list(logits.size()) == [tokens.size(0), ms.vocab_size]
    assert list(logits.size()) == [tokens.size(0), ms.vocab_size]


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="single_token"),
        pytest.param([21, 12000], id="dummy"),
        pytest.param(_LLAMA_350M_PROMPT_TOKS, id="known_sequence"),
    ],
)
def test_batched_equals_sequential(prompt: Sequence[int], llama_350m_fp16_fixture):
    """Ensure that the batched and sequential models produce the same outputs."""
    _, step_fn, attn_factory = llama_350m_fp16_fixture
    fwd_model_test_utils.check_batched_equals_sequential(step_fn, attn_factory, prompt)


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="single_token"),
        pytest.param([1, 2, 3], id="dummy"),
        pytest.param(_LLAMA_350M_PROMPT_TOKS, id="known_sequence"),
    ],
)
def test_batched_equals_sequential_cuda_graph(
    prompt: Sequence[int], llama_350m_fp16_graphed_fixture
):
    """Ensure that the batched and sequential models produce the same outputs."""
    _, step_fn, attn_factory = llama_350m_fp16_graphed_fixture
    fwd_model_test_utils.check_batched_equals_sequential(
        step_fn, attn_factory, prompt, kv_len=128
    )
