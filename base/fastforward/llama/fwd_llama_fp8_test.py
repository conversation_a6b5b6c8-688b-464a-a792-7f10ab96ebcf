"""Smoke test LLAMA models in e4m3."""

from typing import Sequence

import pytest

from base.fastforward import fwd_model_test_utils, fwd_utils
from base.fastforward.llama import fwd_llama

_LLAMA_350M_PROMPT_TOKS = fwd_llama.LLAMA_350M_PROMPT_TOKS
_LLAMA_350M_OUTPUT_TOKS = fwd_llama.LLAMA_350M_OUTPUT_TOKS


def test_generate(llama_350m_fp8_fixture):
    """Ensure that the model generates the correct outputs."""
    step_fn, attn_factory = llama_350m_fp8_fixture
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=[8, 16])
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        _LLAMA_350M_PROMPT_TOKS,
        _LLAMA_350M_OUTPUT_TOKS,
        allowed_mismatches=1,
        extra_kv_len=16,
    )


def test_generate_cuda_graph(llama_350m_fp8_graphed_fixture):
    """Ensure that the model generates the correct outputs."""
    step_fn, attn_factory = llama_350m_fp8_graphed_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        _LLAMA_350M_PROMPT_TOKS,
        _LLAMA_350M_OUTPUT_TOKS,
        allowed_mismatches=1,
        extra_kv_len=128,  # to account for graph capturing
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="single_token"),
        pytest.param([1, 2, 3], id="dummy"),
        pytest.param(_LLAMA_350M_PROMPT_TOKS, id="known_sequence"),
    ],
)
def test_batched_equals_sequential(llama_350m_fp8_fixture, prompt: Sequence[int]):
    """Ensure that the batched and sequential models produce the same outputs."""
    step_fn, attn_factory = llama_350m_fp8_fixture
    step_fn = fwd_utils.pad_and_step(step_fn, [8])
    fwd_model_test_utils.check_batched_equals_sequential(
        step_fn, attn_factory, prompt, extra_kv_len=8
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="single_token"),
        pytest.param([1, 2, 3], id="dummy"),
        pytest.param(_LLAMA_350M_PROMPT_TOKS, id="known_sequence"),
    ],
)
def test_batched_equals_sequential_cuda_graph(llama_350m_fp8_graphed_fixture, prompt):
    """Ensure that the batched and sequential models produce the same outputs."""
    step_fn, attn_factory = llama_350m_fp8_graphed_fixture
    fwd_model_test_utils.check_batched_equals_sequential(
        step_fn,
        attn_factory,
        prompt,
        extra_kv_len=128,  # to account for graph capturing
    )


@pytest.mark.parametrize(
    "prompt",
    [
        # pytest.param([21], id="single_token"),
        pytest.param([21, 12000], id="dummy"),
        pytest.param(_LLAMA_350M_PROMPT_TOKS, id="known_sequence"),
    ],
)
def test_fp8_matches_fp16(llama_350m_fp8_fixture, llama_350m_fp16_fixture, prompt):
    """Ensure that the fp8 and fp16 models produce the same outputs."""
    step_fn_fp8, attn_factory_fp8 = llama_350m_fp8_fixture
    _, step_fn_fp16, attn_factory_fp16 = llama_350m_fp16_fixture
    step_fn_fp8 = fwd_utils.pad_and_step(step_fn_fp8, round_sizes=[8])
    fwd_model_test_utils.check_logits_are_close(
        step_fn_fp8,
        step_fn_fp16,
        attn_factory_fp8,
        attn_factory_fp16,
        prompt,
        # Note(markus): The first token showed some unusual deviation in fp16,
        # when run with batch size 1 vs batch size 2. As far as we can tell,
        # this is benign.
        ignore_positions=[0],
        extra_kv_len=8,
    )
