"""conftest.py is a special file that pytest will automatically load."""

import dataclasses
import pytest
import torch

from base.fastforward import fwd_utils
from base.fastforward.all_reduce import ALL_ALL_REDUCE_IMPLS, AllReduceImpl
from base.fastforward.cached_attention import (
    ALL_ATTN_IMPLS_NO_FA3,
    ALL_ATTN_IMPLS_NO_FP8,
    ALL_ATTN_IMPLS_WITH_FP8,
    AttentionImpl,
)
from base.fastforward.llama import fwd_llama, fwd_llama_fp8, model_specs
from base.fastforward.parallel import ParallelConfig


@pytest.fixture(scope="module")
def llama_350m_gqa_model_spec_fixture():
    # This is a modified version of the llama-350m checkpoint below that simply strips out
    # KV-heads 4-15 s.t. we can run tests that use GQA.
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-gqa4-fp8",
        checkpoint_sha256="7e66fd17bac40e4a938f38be1a893abf0ad17eb0480bbce8db2b3948c499f098",
    )
    # Patch up the model spec to reflect the fact that we have 4 kv-heads.
    ms = dataclasses.replace(ms, num_heads=4, num_queries_per_head=4)
    return ms


def create_llama_350m(
    auto_capture_graphs: bool = False,
    batch_sizes: list[int] | None = None,
    attention_impl: AttentionImpl = AttentionImpl.BATCHED_FLASH,
):
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m",
        checkpoint_sha256="e22e039ec50c175821833e5b7110e6ffb2ee881a310bccc13527f85861593b5e",
    )

    step_fn = fwd_llama.generate_step_fn(
        ms=ms, auto_capture_graphs=auto_capture_graphs, batch_sizes=batch_sizes
    )
    attn_factory = fwd_llama.LlamaAttentionFactory(ms=ms, attention_impl=attention_impl)
    return ms, step_fn, attn_factory


@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_NO_FP8)
def llama_350m_fp16_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    if request.param.is_flash_v3() and not request.config.getoption(
        "--enable-h100-kernels"
    ):
        pytest.skip("Skipping FA3 since --enable-h100-kernels was not passed.")
    yield create_llama_350m(attention_impl=request.param)


@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_NO_FP8)
def llama_350m_fp16_graphed_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    if request.param.is_flash_v3() and not request.config.getoption(
        "--enable-h100-kernels"
    ):
        pytest.skip("Skipping FA3 since --enable-h100-kernels was not passed.")

    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m",
        checkpoint_sha256="e22e039ec50c175821833e5b7110e6ffb2ee881a310bccc13527f85861593b5e",
    )
    batch_sizes = [1, 8, 16, 32, 64, 128]
    step_fn = fwd_llama.generate_step_fn(
        ms=ms, auto_capture_graphs=True, batch_sizes=batch_sizes
    )
    attn_factory = fwd_llama.LlamaAttentionFactory(ms=ms, attention_impl=request.param)
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=batch_sizes)
    return ms, step_fn, attn_factory


# Include FA3 for llama in fp8
@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_WITH_FP8)
def llama_350m_fp8_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    if request.param.is_flash_v3() and not request.config.getoption(
        "--enable-h100-kernels"
    ):
        pytest.skip("Skipping FA3 since --enable-h100-kernels was not passed.")
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-fp8",
        checkpoint_sha256="3668939a42f0016ee16971b8cb17c4b5bbeb9e971ae66bb6507c5a6b990abc65",
    )
    step_fn = fwd_llama_fp8.generate_step_fn(ms)
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms, attention_impl=request.param, pre_attention_kernel_fusion=True
    )
    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_WITH_FP8)
def llama_350m_fp8_graphed_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    if request.param.is_flash_v3() and not request.config.getoption(
        "--enable-h100-kernels"
    ):
        pytest.skip("Skipping FA3 since --enable-h100-kernels was not passed.")
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-fp8",
        checkpoint_sha256="3668939a42f0016ee16971b8cb17c4b5bbeb9e971ae66bb6507c5a6b990abc65",
    )
    batch_sizes = [8, 16, 32, 64, 128]
    step_fn = fwd_llama_fp8.generate_step_fn(
        ms, auto_capture_graphs=True, batch_sizes=batch_sizes
    )
    attn_factory = fwd_llama.LlamaAttentionFactory(ms=ms, attention_impl=request.param)
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=batch_sizes)
    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(scope="module", params=ALL_ATTN_IMPLS_NO_FA3)
def llama_350m_cuda_graph_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    _, step_fn, attn_factory = create_llama_350m(attention_impl=request.param)
    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(
    scope="module",
    params=[
        (attn_impl, all_reduce)
        for attn_impl in ALL_ATTN_IMPLS_NO_FA3
        for all_reduce in ALL_ALL_REDUCE_IMPLS
    ],
)
def llama_350m_fp16_multigpu_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    if request.param[1] == AllReduceImpl.FASTFORWARD_FP8:
        pytest.skip(
            "Skipping FP8 all_reduce since it is not supported for fp16 models."
        )
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m",
        checkpoint_sha256="e22e039ec50c175821833e5b7110e6ffb2ee881a310bccc13527f85861593b5e",
    )
    print(f"{request.param=}")
    step_fn = fwd_llama.generate_step_fn(
        ms=ms,
        num_processes=2,
        auto_capture_graphs=False,
        all_reduce_impl=request.param[1],
    )
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=ParallelConfig.from_legacy_config(2, False),
        attention_impl=request.param[0],
        pre_attention_kernel_fusion=True,
    )
    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(
    scope="function",
    params=[
        (attn_impl, all_reduce)
        # TODO(carl): enable FA3 for multi-gpu tests once there is a multi-H100 test story
        for attn_impl in ALL_ATTN_IMPLS_NO_FA3
        for all_reduce in ALL_ALL_REDUCE_IMPLS
    ],
)
def llama_350m_fp8_multigpu_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-fp8",
        checkpoint_sha256="3668939a42f0016ee16971b8cb17c4b5bbeb9e971ae66bb6507c5a6b990abc65",
    )
    print(f"{request.param=}")
    parallel_config = ParallelConfig(num_processes=2, tp_size=2, sp_size=1)
    step_fn = fwd_llama_fp8.generate_step_fn(
        ms,
        parallel_config=parallel_config,
        auto_capture_graphs=False,
        all_reduce_impl=request.param[1],
    )
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=request.param[0],
        pre_attention_kernel_fusion=True,
    )
    yield step_fn, attn_factory
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors


@pytest.fixture(
    scope="function",
    params=[
        (attn_impl, all_reduce)
        for attn_impl in ALL_ATTN_IMPLS_NO_FA3
        for all_reduce in ALL_ALL_REDUCE_IMPLS
    ],
)
def llama_350m_fp8_multigpu_sequence_parallel_fixture(request):
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-fp8",
        checkpoint_sha256="3668939a42f0016ee16971b8cb17c4b5bbeb9e971ae66bb6507c5a6b990abc65",
    )
    attn_impl = request.param[0]
    all_reduce_impl = request.param[1]
    parallel_config = ParallelConfig(num_processes=2, tp_size=1, sp_size=2)
    step_fn = fwd_llama_fp8.generate_step_fn(
        ms,
        parallel_config=parallel_config,
        auto_capture_graphs=False,
        all_reduce_impl=all_reduce_impl,
    )
    round_sizes = [16, 512]
    padded_step_fn = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=attn_impl,
        pre_attention_kernel_fusion=False,
    )
    try:
        yield padded_step_fn, attn_factory, max(round_sizes)
    finally:
        del padded_step_fn
        del step_fn
        del attn_factory
    torch.cuda.synchronize()


@pytest.fixture(
    scope="function",
    params=[
        (attn_impl, all_reduce)
        for attn_impl in ALL_ATTN_IMPLS_NO_FA3
        for all_reduce in ALL_ALL_REDUCE_IMPLS
    ],
)
def llama_350m_fp8_multigpu_two_way_parallel_fixture(request):
    # TODO(carl): enable this once we have a 4GPU option in CI.
    pytest.skip("Skipping two way parallel test since it requires 4 GPUs.")
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-fp8",
        checkpoint_sha256="3668939a42f0016ee16971b8cb17c4b5bbeb9e971ae66bb6507c5a6b990abc65",
    )
    attn_impl = request.param[0]
    all_reduce_impl = request.param[1]
    parallel_config = ParallelConfig(num_processes=4, tp_size=2, sp_size=2)
    step_fn = fwd_llama_fp8.generate_step_fn(
        ms,
        parallel_config=parallel_config,
        auto_capture_graphs=False,
        all_reduce_impl=all_reduce_impl,
    )
    round_sizes = [16, 512]
    padded_step_fn = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=attn_impl,
        pre_attention_kernel_fusion=False,
    )
    try:
        yield padded_step_fn, attn_factory, max(round_sizes)
    finally:
        del padded_step_fn
        del step_fn
        del attn_factory
    torch.cuda.synchronize()


@pytest.fixture(
    scope="module",
    params=[
        (attn_impl, all_reduce)
        for attn_impl in ALL_ATTN_IMPLS_NO_FA3
        for all_reduce in ALL_ALL_REDUCE_IMPLS
    ],
)
def llama_350m_fp8_multigpu_cuda_graph_fixture(request):
    """Load the weights of a llama-350m model to use for all tests."""
    ms = model_specs.get_llama_model_spec(
        model_name="llama-350m",
        checkpoint_path="/mnt/efs/augment/checkpoints/v2/llama/fastforward/llama-350m-fp8",
        checkpoint_sha256="3668939a42f0016ee16971b8cb17c4b5bbeb9e971ae66bb6507c5a6b990abc65",
    )
    batch_sizes = [8, 16, 32, 64, 128]
    parallel_config = ParallelConfig(num_processes=2, tp_size=2, sp_size=1)
    step_fn = fwd_llama_fp8.generate_step_fn(
        ms,
        parallel_config=parallel_config,
        auto_capture_graphs=True,
        batch_sizes=batch_sizes,
        all_reduce_impl=request.param[1],
    )
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=request.param[0],
        pre_attention_kernel_fusion=True,
    )
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=batch_sizes)
    yield step_fn, attn_factory, batch_sizes
    del step_fn
    del attn_factory
    torch.cuda.synchronize()  # executed at tear down to help tracking down cuda errors
