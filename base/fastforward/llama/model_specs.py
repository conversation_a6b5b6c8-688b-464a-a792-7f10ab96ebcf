"""ModelSpecs for different models, such as LLaMA and DeepSeek."""

import dataclasses
import pathlib

from base.fastforward import fwd
from base.fastforward.cached_attention import SplitHeadModes


@dataclasses.dataclass
class LlamaModelSpec(fwd.ModelSpec):
    """ModelSpec for LLAMA models. Needs to specify num_queries_per_had for GQA."""

    num_queries_per_head: int = 1
    """Used for GQA / MQA models."""

    mlp_dim_divisible_by: int = 256
    """MLP's hidden_dim must be divisible by this number."""

    ffn_dim_multiplier: float = 1.0
    """MLP's hidden_dim are expanded this number."""

    attn_split_head_mode: SplitHeadModes = SplitHeadModes.NO_SPLIT
    """Tensor parallelism in attention layers. None means to not use."""

    qkv_only_bias: bool = False
    """Whether to use bias only for the QKV projection. (Needed for Qwen.)"""

    use_qk_norm: bool = False
    """Whether to use QK normalization after QKV projection. (Needed for Qwen3.)"""

    def __post_init__(self):
        super().__post_init__()
        assert (
            self.emb_dim == self.num_heads * self.head_dim * self.num_queries_per_head
        )
        self.num_heads_kv = self.num_heads
        self.num_heads_q = self.num_heads_kv * self.num_queries_per_head

        hidden_dim = 4 * self.emb_dim
        hidden_dim = int(2 * hidden_dim / 3)
        hidden_dim = int(hidden_dim * self.ffn_dim_multiplier)

        def _next_multiple(a: int, m: int) -> int:
            return (a + m - 1) // m * m

        self.mlp_hidden_dim = _next_multiple(hidden_dim, self.mlp_dim_divisible_by)


_MODEL_SPECS: dict[str, LlamaModelSpec] = {
    "llama-350m": LlamaModelSpec(
        name="llama-350m",
        checkpoint_path="",
        emb_dim=1024,
        num_layers=20,
        num_heads=16,
        head_dim=64,
        num_queries_per_head=1,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=51200,
        mlp_dim_divisible_by=128,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-05,
        max_position_embeddings=4096,
        unscaled_max_position_embeddings=4096,
    ),
    "llama-350m-sd": LlamaModelSpec(
        name="llama-350m",
        checkpoint_path="",
        emb_dim=2048,
        num_layers=6,
        num_heads=2,
        head_dim=128,
        num_queries_per_head=8,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=51200,
        mlp_dim_divisible_by=128,
        ffn_dim_multiplier=1.5,
        norm_eps=1e-05,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=16384,
    ),
    "deepseek-coder-5.7b": LlamaModelSpec(
        name="deepseek-coder-5.7b",
        checkpoint_path="",
        emb_dim=4096,
        num_layers=32,
        num_heads=1,
        num_queries_per_head=32,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=100000.0,
        rotary_scaling_factor=4.0,
        rotary_pct=1.0,
        vocab_size=32256,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-06,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=4096,
    ),
    "deepseek-coder-1.3b": LlamaModelSpec(
        name="deepseek-coder-1.3b",
        checkpoint_path="",
        emb_dim=2048,
        num_layers=24,
        num_heads=16,
        num_queries_per_head=1,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=100000.0,
        rotary_scaling_factor=4.0,
        rotary_pct=1.0,
        vocab_size=32256,
        mlp_dim_divisible_by=128,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-05,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=4096,
    ),
    "deepseek-coder-33b": LlamaModelSpec(
        name="deepseek-coder-33b",
        checkpoint_path="",
        emb_dim=7168,
        num_layers=62,
        num_heads=8,
        num_queries_per_head=7,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=100000.0,
        rotary_scaling_factor=4.0,
        rotary_pct=1.0,
        vocab_size=32256,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-05,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=4096,
    ),
    "llama3-70b": LlamaModelSpec(
        name="llama3-70b",
        checkpoint_path="",
        emb_dim=8192,
        num_layers=80,
        num_heads=8,
        num_queries_per_head=8,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=500000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=128256,
        mlp_dim_divisible_by=4096,
        ffn_dim_multiplier=1.3,
        norm_eps=1e-05,
        max_position_embeddings=8192,
        unscaled_max_position_embeddings=8192,
    ),
    "llama3-70b-16k": LlamaModelSpec(
        name="llama3-70b-16k",
        checkpoint_path="",
        emb_dim=8192,
        num_layers=80,
        num_heads=8,
        num_queries_per_head=8,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=500000.0,
        rotary_scaling_factor=4.0,
        rotary_extension_method="yarn",
        beta_fast=32,
        beta_slow=1,
        rotary_pct=1.0,
        vocab_size=128256,
        mlp_dim_divisible_by=4096,
        ffn_dim_multiplier=1.3,
        norm_eps=1e-05,
        max_position_embeddings=16384,
        unscaled_max_position_embeddings=4096,
    ),
    "llama3_1-8b": LlamaModelSpec(
        name="llama3_1-8b",
        checkpoint_path="",
        emb_dim=4096,
        num_layers=32,
        num_heads=8,
        num_queries_per_head=4,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=500_000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        rotary_extension_method="llama3_1",
        vocab_size=128256,
        mlp_dim_divisible_by=1024,
        ffn_dim_multiplier=1.3,
        norm_eps=1e-05,
        max_position_embeddings=8192,
        unscaled_max_position_embeddings=8192,
    ),
    "qwen2_5-coder-1.5b-retriever": LlamaModelSpec(
        name="qwen2_5-coder-1.5b",
        checkpoint_path="",
        emb_dim=1536,
        num_layers=28,
        num_heads=2,
        num_queries_per_head=6,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.KV_HEADS,
        rotary_theta=1000000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=152064,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=2.15,
        norm_eps=1e-06,
        max_position_embeddings=32768,
        unscaled_max_position_embeddings=32768,
        qkv_only_bias=True,
        output_projection_dim=512,
    ),
    "qwen2_5-7b": LlamaModelSpec(
        name="qwen2_5-7b",
        checkpoint_path="",
        emb_dim=3584,
        num_layers=28,
        num_heads=4,
        num_queries_per_head=7,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=1000000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=152064,
        mlp_dim_divisible_by=512,
        ffn_dim_multiplier=1.95,
        norm_eps=1e-06,
        max_position_embeddings=32768,
        unscaled_max_position_embeddings=32768,
        qkv_only_bias=True,
    ),
    "qwen2_5-14b": LlamaModelSpec(
        name="qwen2_5-14b",
        checkpoint_path="",
        emb_dim=5120,
        num_layers=48,
        num_heads=8,
        num_queries_per_head=5,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=1000000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=152064,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=1.0,
        norm_eps=1e-06,
        max_position_embeddings=32768,
        unscaled_max_position_embeddings=32768,
        qkv_only_bias=True,
    ),
    "qwen2_5-32b": LlamaModelSpec(
        name="qwen2_5-32b",
        checkpoint_path="",
        emb_dim=5120,
        num_layers=64,
        num_heads=8,
        num_queries_per_head=5,
        head_dim=128,
        attn_split_head_mode=SplitHeadModes.NO_SPLIT,
        rotary_theta=1000000.0,
        rotary_scaling_factor=1.0,
        rotary_pct=1.0,
        vocab_size=152064,
        mlp_dim_divisible_by=256,
        ffn_dim_multiplier=2.02,
        norm_eps=1e-06,
        max_position_embeddings=32768,
        unscaled_max_position_embeddings=32768,
        qkv_only_bias=True,
    ),
}


def get_llama_model_spec(
    model_name: str,
    checkpoint_path: pathlib.Path | str | None = None,
    checkpoint_sha256: str | None = None,
) -> LlamaModelSpec:
    ms = _MODEL_SPECS[model_name]
    ms = dataclasses.replace(ms)
    if checkpoint_path is not None:
        if not isinstance(checkpoint_path, str):
            checkpoint_path = str(checkpoint_path.absolute())
        ms.checkpoint_path = checkpoint_path
    if checkpoint_sha256 is not None:
        ms.checkpoint_sha256 = checkpoint_sha256
    return ms
