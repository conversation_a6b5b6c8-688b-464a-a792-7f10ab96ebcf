"""LLAMA architectures in FP8."""

import logging
from typing import Sequence, Union

import torch
import torch.nn as nn

from base.fastforward import all_reduce, cuda_graphs_attention, fwd, fwd_torch, parallel
from base.fastforward.cached_attention import Attention
from base.fastforward.checkpoints import save_load
from base.fastforward.fp8 import FP8Linear, FP8RmsNorm
from base.fastforward.layers import RmsNorm, WordEmbeddings
from base.fastforward.layers_fp8 import LlamaTransformerBlock
from base.fastforward.llama.fwd_llama import process_loaded_weights, shard_args_by_name
from base.fastforward.llama.model_specs import LlamaModelSpec
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.parallel_fwd import ParallelForwardRunner

Device = Union[torch.device, str]


class Llama(nn.Module):
    """Entire Llama architecture in FP8."""

    def __init__(
        self,
        ms: LlamaModelSpec,
        dtype: torch.dtype = torch.bfloat16,
        device: Device = "cuda",
        process_idx: int = 0,
        parallel_config: ParallelConfig = ParallelConfig.single_process(),
        auto_capture_graphs: bool = False,
        batch_sizes: Sequence[int] | None = None,
        all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
        output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
    ):
        super().__init__()
        self._parallel_ctx = ParallelContext(parallel_config, process_idx)
        self._device = device
        self._dtype = dtype
        self._auto_capture_graphs = auto_capture_graphs
        self._output_type = output_type

        # Default values for custom communicators are to return `None`.
        # - There are 2*num_layers all_reduce calls (one for attn, one for mlp).
        # - There are num_layers all_gather calls (one per attn).
        all_reduce_kits = all_reduce.DoubleBufferedCommunicator(
            ms.num_layers * 2,
            lambda: None,
        )
        all_gather_kits = all_reduce.DoubleBufferedCommunicator(
            ms.num_layers,
            lambda: None,
        )

        if all_reduce_impl.is_fastforward():
            # max tokens per round
            max_tokens = max(batch_sizes) if batch_sizes is not None else 4096

            if self._parallel_ctx.tp_size > 1:
                all_reduce_kits = all_reduce.DoubleBufferedCommunicator(
                    ms.num_layers * 2,
                    all_reduce.AllReduceKit,
                    ms.emb_dim * max_tokens,
                    dtype,
                    process_group=self._parallel_ctx.tp_group,
                    input_is_fp8=all_reduce_impl
                    == all_reduce.AllReduceImpl.FASTFORWARD_FP8,
                )
            if self._parallel_ctx.sp_size > 1:
                all_gather_kits = all_reduce.DoubleBufferedCommunicator(
                    ms.num_layers,
                    all_reduce.AllGatherKit,
                    input_max_numel=max_tokens * ms.num_heads_kv * ms.head_dim,
                    dtype=dtype,
                    process_group=self._parallel_ctx.sp_group,
                )

        self.embs = WordEmbeddings(
            vocab_size=ms.vocab_size,
            emb_dim=ms.emb_dim,
            dtype=dtype,
            device=device,
        )
        logging.info("create emb.")

        self.layers = nn.ModuleList()
        for layer_idx in range(ms.num_layers):
            self.layers.append(
                LlamaTransformerBlock(
                    emb_dim=ms.emb_dim,
                    num_heads_q=ms.num_heads_q,
                    num_heads_kv=ms.num_heads_kv,
                    head_dim=ms.head_dim,
                    split_head_mode=ms.attn_split_head_mode,
                    mlp_dim=ms.mlp_hidden_dim,
                    norm_eps=ms.norm_eps,
                    use_bias=False,
                    qkv_only_bias=ms.qkv_only_bias,
                    dtype=dtype,
                    device=device,
                    parallel_ctx=self._parallel_ctx,
                    attn_all_reduce_kit=all_reduce_kits.get(2 * layer_idx),
                    ffn_all_reduce_kit=all_reduce_kits.get(2 * layer_idx + 1),
                    attn_all_gather_kit=all_gather_kits.get(layer_idx),
                )
            )
        all_reduce_kits.verify_num_calls()
        all_gather_kits.verify_num_calls()

        logging.info("create final_rms_norm.")

        if output_type == fwd.OutputTensorType.EMBEDDING:
            assert ms.output_projection_dim is not None
            # NOTE that the embedding model runs its final layer in 16bits.
            # TODO(carl): consider an option (here and for SC1) to support pure fp8 in case of
            # quantized embedding storage.
            self.final_rms_norm = RmsNorm(
                inp_dim=ms.emb_dim,
                eps=ms.norm_eps,
                dtype=dtype,
                cast_to_fp32=True,
                device=device,
            )
            self.output_projection = nn.Linear(
                in_features=ms.emb_dim,
                out_features=ms.output_projection_dim,
                bias=True,  # llama-based embedders have a bias
                device=device,
                dtype=dtype,
            )
        else:
            assert output_type == fwd.OutputTensorType.VOCAB_LOGITS
            logging.info("create score_proj.")
            self.final_rms_norm = FP8RmsNorm(
                inp_dim=ms.emb_dim,
                eps=ms.norm_eps,
                dtype=dtype,
                device=device,
            )
            self.score = FP8Linear(
                in_features=ms.emb_dim,
                out_features=ms.vocab_size,
                bias=False,
                device=device,
                dtype=dtype,
            )
        if self._auto_capture_graphs:
            if batch_sizes is None:
                raise ValueError(
                    "batch_sizes must be specified if auto_capture_graphs is True."
                )
            self._apply_transformer_layers = (
                cuda_graphs_attention.GraphedEmbForwardPass(
                    step_fn=self._graphable_forward,
                    process_idx=self._parallel_ctx.process_idx,
                    num_processes=self._parallel_ctx.num_processes,
                    batch_sizes=batch_sizes,
                )
            )
        else:
            self._apply_transformer_layers = self._graphable_forward

    def _graphable_forward(
        self, tokens_on_device: torch.Tensor, attn: Attention
    ) -> torch.Tensor:
        assert tokens_on_device.dtype == torch.int32
        assert tokens_on_device.device.type == "cuda", tokens_on_device.device
        x = self.embs(tokens_on_device).to(self._dtype)
        if self._parallel_ctx.sp_size > 1:
            x = parallel.split_tensor_for_sequence_parallelism(
                x,
                parallel_ctx=self._parallel_ctx,
            )
        for layer_idx, layer in enumerate(self.layers):
            x = layer(x, attn=attn, layer_idx=layer_idx)
        if self._parallel_ctx.sp_size > 1:
            x = parallel.gather_sequence_parallel(x, self._parallel_ctx)
        return x

    @torch.inference_mode()
    def forward(self, tokens: Sequence[int], attn: Attention) -> fwd.ModelOutput:
        tokens_on_device = torch.tensor(tokens, dtype=torch.int32, device=self._device)

        torch.cuda.nvtx.range_push("register_tokens_get_positions")
        attn.register_tokens_get_positions(
            tokens_on_device, process_idx=self._parallel_ctx.process_idx
        )
        torch.cuda.nvtx.range_pop()
        x = self._apply_transformer_layers(tokens_on_device, attn)
        if self._output_type == fwd.OutputTensorType.EMBEDDING:
            x = self.final_rms_norm(x)
            x = self.output_projection(x)
            return fwd_torch.TorchEmbedding(x)
        else:
            x = self.final_rms_norm(x, output_fp8_meta=self.score.input_fp8_meta)
            x = self.score(x)
            return fwd_torch.TorchLogits2D(x)


def _loaded_weights_are_valid(
    loaded_weights: set[str],
    model_weights: set[str],
) -> bool:
    # Exact match is always valid
    if loaded_weights == model_weights:
        return True
    # Unassigned keys are never valid (this should be caught beforehand)
    if len(loaded_weights - model_weights) > 0:
        return False
    # Compute the missing keys and check if they are a valid case
    missing_keys = model_weights - loaded_weights
    attn_scale_keys = set(k for k in model_weights if k.endswith(".attn_qkv_scales"))
    output_scale_keys = set(k for k in model_weights if k.endswith(".output_scale"))
    # Valid cases are either or both of attn_ and output_scale_keys missing:
    if (
        missing_keys == attn_scale_keys | output_scale_keys
        or missing_keys == attn_scale_keys
        or missing_keys == output_scale_keys
    ):
        return True
    return False


def _generate_step_fn(
    ms: LlamaModelSpec,
    parallel_config: ParallelConfig,
    dtype: torch.dtype = torch.bfloat16,
    batch_sizes: Sequence[int] | None = None,
    process_idx: int = 0,
    num_processes: int = 1,
    num_layers_per_load: int = 10,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    logging.info(
        "Loading a LLAMA model %s with parallel_config=%s.", ms.name, parallel_config
    )
    logging.debug("ms: %s, parallel_config: %s", ms, parallel_config)
    if parallel_config.num_processes != num_processes:
        raise ValueError(
            f"parallel_config.num_processes={parallel_config.num_processes} does not match num_processes={num_processes}"
        )

    # TODO: load only the weights that we need for this process.
    device = f"cuda:{process_idx}"
    model = Llama(
        ms=ms,
        dtype=dtype,
        device=device,
        process_idx=process_idx,
        parallel_config=parallel_config,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
        all_reduce_impl=all_reduce_impl,
        output_type=output_type,
    )

    if load_checkpoint_weights:
        shards_by_name = shard_args_by_name(
            ms=ms,
            process_idx=process_idx,
            parallel_config=parallel_config,
        )

        loaded_weights = set()
        torch.cuda.empty_cache()
        # First, load only the weights that aren't on a layer block
        weights = process_loaded_weights(
            save_load.load_weights(
                ms.checkpoint_path,
                require_patterns=(
                    r"^embs\.",
                    r"^final_rms_norm\.",
                    r"^score\.",
                    r"^output_projection\.",
                ),
                shard_load_args=shards_by_name,
                target_sha256=ms.checkpoint_sha256,
                device=device,
            ),
            ms=ms,
            device=device,
            process_idx=process_idx,
            parallel_config=parallel_config,
        )
        model.load_state_dict(weights, strict=False)
        loaded_weights.update(weights.keys())
        del weights

        # Second, load the layer weights in batches
        for block_idx in range(
            (ms.num_layers + num_layers_per_load - 1) // num_layers_per_load
        ):
            torch.cuda.empty_cache()
            layer_idxs = [
                rf"^layers\.{num_layers_per_load * block_idx + i}\."
                for i in range(num_layers_per_load)
                if num_layers_per_load * block_idx + i < ms.num_layers
            ]
            weights = process_loaded_weights(
                save_load.load_weights(
                    ms.checkpoint_path,
                    require_patterns=tuple(layer_idxs),
                    shard_load_args=shards_by_name,
                    target_sha256=ms.checkpoint_sha256,
                    device=device,
                ),
                ms=ms,
                device=device,
                process_idx=process_idx,
                parallel_config=parallel_config,
            )
            model.load_state_dict(weights, strict=False)
            loaded_weights.update(weights.keys())
            del weights
        # We loaded state with strict=False; perform that check ourselves now
        model_weights = set(model.state_dict().keys())
        if not _loaded_weights_are_valid(loaded_weights, model_weights):
            raise RuntimeError(
                f"Invalid set of loaded weights.\n{loaded_weights=}.\n{model_weights=}"
            )
    logging.info("Done loading LLAMA model.")

    return model


def generate_step_fn(
    ms: LlamaModelSpec,
    parallel_config: ParallelConfig = ParallelConfig.single_process(),
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
    small_round_parallel_config: ParallelConfig | None = None,
    small_round_token_cutoff: int = 0,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        ms: Model spec.
        parallel_config: configuration of model parallelism.
        load_checkpoint_weights: load the weights into the step_fn. should be `True`
            by default. only set to `False` for testing, and if so, make sure you
            know what you are doing.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
        all_reduce_impl: The implementation of all reduce to use.
        small_round_parallel_config: The parallel config to use for small rounds.
        small_round_token_cutoff: The token cutoff to use for small rounds.
    """
    logging.info("Using base.fastforward")

    if parallel_config.num_processes == 1:
        return _generate_step_fn(
            ms=ms,
            parallel_config=parallel_config,
            batch_sizes=batch_sizes,
            load_checkpoint_weights=load_checkpoint_weights,
            auto_capture_graphs=auto_capture_graphs,
            all_reduce_impl=all_reduce_impl,
            output_type=output_type,
        )
    elif small_round_parallel_config is not None and small_round_token_cutoff > 0:
        assert (
            small_round_parallel_config.num_processes == parallel_config.num_processes
        )
        mp = ParallelForwardRunner(
            num_processes=parallel_config.num_processes,
        )
        return mp.initialize(
            [_generate_step_fn, _generate_step_fn],
            [
                {
                    "ms": ms,
                    "parallel_config": small_round_parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                    "output_type": output_type,
                },
                {
                    "ms": ms,
                    "parallel_config": parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                    "output_type": output_type,
                },
            ],
            lambda tokens, attn: 0 if len(tokens) <= small_round_token_cutoff else 1,
        )
    else:
        mp = ParallelForwardRunner(
            num_processes=parallel_config.num_processes,
        )
        return mp.initialize(
            [_generate_step_fn],
            [
                {
                    "ms": ms,
                    "parallel_config": parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                    "output_type": output_type,
                }
            ],
        )
