"""Tests for ffw_llama on multiple GPUs."""

import torch

from base.fastforward import fwd_model_test_utils
from base.fastforward.llama import fwd_llama

_LLAMA_350M_PROMPT_TOKS = fwd_llama.LLAMA_350M_PROMPT_TOKS
_LLAMA_350M_OUTPUT_TOKS = fwd_llama.LLAMA_350M_OUTPUT_TOKS


def test_generate(llama_350m_fp8_multigpu_cuda_graph_fixture):
    """Ensure that on generating on multipe GPUs matches that on a single GPU."""
    assert (
        torch.cuda.device_count() > 1
    ), "This test should not run on environments with < 2 GPUs."

    step_fn, attn_factory, batch_sizes = llama_350m_fp8_multigpu_cuda_graph_fixture

    try:
        fwd_model_test_utils.check_if_model_generates_target_sequence(
            step_fn,
            attn_factory,
            _LLAMA_350M_PROMPT_TOKS,
            _LLAMA_350M_OUTPUT_TOKS,
            allowed_mismatches=2,
            extra_kv_len=max(batch_sizes),  # to account for graph capturing
        )
    finally:
        del step_fn
        del attn_factory
