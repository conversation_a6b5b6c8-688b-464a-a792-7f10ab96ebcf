"""Tests for codegen model."""

import copy
import time

import pytest
import torch

from base.fastforward import cached_attention, fwd
from base.fastforward.codegen import fwd_codegen
from base.fastforward.parallel import ParallelContext

CG_KNOWN_SEQUENCE = fwd_codegen.CG_KNOWN_SEQUENCE


def get_350M_model_spec():
    model_spec = fwd.ModelSpec(
        name="codegen-350M-multi",
        checkpoint_path="/mnt/efs/augment/checkpoints/codegen-350M-multi/checkpoint/",
        vocab_size=51200,  # repo_config.model_arch.vocab_size,
        num_layers=20,  # repo_config.model_arch.num_layers,
        num_heads=16,  # repo_config.model_arch.num_heads,
        emb_dim=1024,  # repo_config.model_arch.emb_dim,
        head_dim=64,  # repo_config.model_arch.head_dim,
        rotary_pct=0.5,  # repo_config.model_arch.rotary_pct,
        norm_eps=1e-5,  # repo_config.model_arch.norm_eps,
    )

    return model_spec


def test_smoke():
    model_spec = get_350M_model_spec()

    step_fn = fwd_codegen.generate_step_fn(model_spec)
    attn_factory = fwd_codegen.get_attention_factory(model_spec)
    attn = attn_factory(128)

    def run_step(prompt: list[int]):
        start_time = time.time()
        # prompt = torch.tensor(prompt, dtype=torch.int32, device="cuda")
        result = step_fn(prompt, attn).checked_cast(torch.Tensor)
        torch.cuda.synchronize()
        step_time = time.time() - start_time
        print(f"step executed in {step_time} sec.")
        return result

    run_step([1])


def assert_basic_attn_close(
    attn1: cached_attention.BasicAttention, attn2: cached_attention.BasicAttention
):
    torch.testing.assert_close(attn1.position, attn2.position, check_device=False)
    key1, _ = attn1.kv_by_device[0]
    key2, _ = attn2.kv_by_device[0]
    torch.testing.assert_close(key1, key2, rtol=1e-3, atol=1e-2, check_device=False)


@pytest.fixture(scope="module")  # stateless; load only once
def model() -> tuple[fwd.ForwardStepFn, fwd.AttentionFactory]:
    model_spec = get_350M_model_spec()
    step_fn = fwd_codegen.generate_step_fn(model_spec)
    attn_factory = fwd_codegen.get_attention_factory(model_spec)
    return step_fn, attn_factory


@pytest.fixture(scope="module")  # stateless; load only once
def model_cuda_graph() -> tuple[fwd.ForwardStepFn, fwd.AttentionFactory]:
    model_spec = get_350M_model_spec()
    step_fn = fwd_codegen.generate_step_fn(model_spec, auto_capture_graphs=True)
    attn_factory = fwd_codegen.get_attention_factory(model_spec)
    return step_fn, attn_factory


@pytest.mark.parametrize("prompt_len", [3, 7, 11])
def test_produces_correct_continuation(
    model: tuple[fwd.ForwardStepFn, fwd.AttentionFactory],
    prompt_len: int,
):
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)
    print()

    step_fn, attn_factory = model
    attn = attn_factory(128)

    prompt = CG_KNOWN_SEQUENCE[:prompt_len]
    expected_token = CG_KNOWN_SEQUENCE[prompt_len]
    logits = step_fn(prompt, attn).checked_cast(torch.Tensor)
    next_token = int(torch.argmax(logits[-1]))
    assert next_token == expected_token


@pytest.mark.parametrize("prompt_len", [3, 7, 11])
def test_produces_correct_continuation_cuda_graph(
    model_cuda_graph: tuple[fwd.ForwardStepFn, fwd.AttentionFactory],
    prompt_len: int,
):
    torch.set_printoptions(precision=3, threshold=10000, sci_mode=False, linewidth=200)
    print()

    step_fn, attn_factory = model_cuda_graph
    attn = attn_factory(128)

    prompt = CG_KNOWN_SEQUENCE[:prompt_len]
    expected_token = CG_KNOWN_SEQUENCE[prompt_len]
    logits = step_fn(prompt, attn).checked_cast(torch.Tensor)
    next_token = int(torch.argmax(logits[-1]))
    assert next_token == expected_token


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1, 2, 3], id="dummy"),
        pytest.param(CG_KNOWN_SEQUENCE, id="known_seq"),
    ],
)
def test_batched_equals_sequential(model, prompt: list[int]):
    step_fn, attn_factory = model

    attn_bat = attn_factory(128)
    attn_seq = attn_factory(128)

    logits_batched = step_fn(
        torch.tensor(prompt, dtype=torch.int32, device="cuda"), attn_bat
    ).checked_cast(torch.Tensor)

    logits_sequential = []
    for t in prompt:
        logits_sequential.append(
            step_fn(
                torch.tensor([t], dtype=torch.int32, device="cuda"), attn_seq
            ).checked_cast(torch.Tensor)
        )
    logits_sequential = torch.cat(logits_sequential, dim=0)

    torch.testing.assert_close(
        logits_batched,
        logits_sequential,
        rtol=1e-2,
        atol=1e-1,
        check_device=False,
    )

    torch.testing.assert_close(
        attn_bat._mc_attn._positions[0][:-1],
        attn_seq._mc_attn._positions[0][:-1],
        check_device=False,
    )

    torch.testing.assert_close(
        attn_bat._mc_attn._kv_by_device[0][0][:, :-1, :, :, :],
        attn_seq._mc_attn._kv_by_device[0][0][:, :-1, :, :, :],
        rtol=5e-3,
        atol=1e-2,
        check_device=False,
    )

    torch.testing.assert_close(
        attn_bat._mc_attn._kv_by_device[0][1][:, :-1, :, :, :],
        attn_seq._mc_attn._kv_by_device[0][1][:, :-1, :, :, :],
        rtol=5e-3,
        atol=1e-2,
        check_device=False,
    )


# TODO(markus): Replace this custom padding implementation with the one in
# batching.py and fwd_utils.py. But probably we'll delete the codegen
# model first.
class _AttentionWithPaddings(cached_attention.BasicAttention):
    """Attention where the last `n` tokens are paddings."""

    def __init__(
        self, mc_attn: cached_attention.MultiCacheAttention, num_paddings: int
    ):
        super().__init__(mc_attn=mc_attn)
        self._num_paddings = num_paddings
        self._cache_pos = None

    def _cache_idxs(
        self, n: int, device: torch.device = torch.device("cuda")
    ) -> torch.Tensor:
        idxs = torch.zeros(n, dtype=torch.int32, device=device)
        idxs[-self._num_paddings :].fill_(self._mc_attn.padding_cache_idx)
        return idxs

    def __call__(
        self,
        qkv: torch.Tensor,
        layer_idx: int,
        parallel_ctx: ParallelContext = ParallelContext.single_process(),
    ) -> torch.Tensor:
        assert self._cache_pos is not None
        assert parallel_ctx.process_idx == 0, parallel_ctx.process_idx
        assert parallel_ctx.cfg.num_processes == 1, parallel_ctx.cfg.num_processes
        cache_idxs = self._cache_idxs(n=qkv.size(0), device=qkv.device)
        return self._mc_attn(
            qkv=qkv,
            cache_idxs=cache_idxs,
            cache_pos=self._cache_pos,
            layer_idx=layer_idx,
            parallel_ctx=parallel_ctx,
        )

    def register_tokens_get_positions(
        self, tokens: torch.Tensor, process_idx: int
    ) -> torch.Tensor:
        cache_idxs = self._cache_idxs(n=tokens.numel(), device=tokens.device)
        self._cache_pos = self._mc_attn.register_tokens_get_positions(
            tokens=tokens, cache_idxs=cache_idxs, process_idx=process_idx
        )
        return self._cache_pos


class _AttentionFactoryWithPaddings(fwd_codegen.CodeGenAttentionFactory):
    """AttentionFactory that builds AttentionWithPaddings instances."""

    def __init__(
        self, num_paddings: int, model_spec: fwd.ModelSpec, num_processes: int = 1
    ):
        super().__init__(model_spec=model_spec, num_processes=num_processes)
        self._num_paddings = num_paddings

    def __call__(self, max_length: int) -> cached_attention.BasicAttention:
        """Legacy interface, as this used to be a simple function."""
        mc_attn = self.create_cache_pool(max_length=max_length, num_attention_caches=1)
        return _AttentionWithPaddings(mc_attn=mc_attn, num_paddings=self._num_paddings)


@pytest.mark.parametrize(
    "prompt,num_paddings",
    [
        pytest.param([1, 2, 3], 1, id="dummy"),
        pytest.param([7, 72, 182, 18, 48, 12, 48], 5, id="dev"),
        # TODO(hieu): continue here. pass this test
        pytest.param(copy.copy(CG_KNOWN_SEQUENCE), 2, id="known_seq"),
    ],
)
def test_paddings_correctness(model, prompt: list[int], num_paddings: int):
    step_fn, _ = model

    # with paddings
    model_spec = get_350M_model_spec()
    attn_factory_with_paddings = _AttentionFactoryWithPaddings(
        model_spec=model_spec, num_paddings=num_paddings
    )
    attn_with_paddings = attn_factory_with_paddings(128)
    logits_with_paddings = step_fn(
        tokens=torch.tensor(prompt, dtype=torch.int32, device="cuda"),
        attn=attn_with_paddings,
    ).checked_cast(torch.Tensor)

    # without paddings
    attn_factory_no_paddings = fwd_codegen.CodeGenAttentionFactory(model_spec)
    attn_no_paddings = attn_factory_no_paddings(128)
    logits_no_paddings = step_fn(
        tokens=torch.tensor(prompt[:-num_paddings], dtype=torch.int32, device="cuda"),
        attn=attn_no_paddings,
    ).checked_cast(torch.Tensor)

    rtol = 1e-2
    atol = 8e-2

    torch.testing.assert_close(
        logits_with_paddings[:-num_paddings],
        logits_no_paddings,
        rtol=rtol,
        atol=atol,
    )

    torch.testing.assert_close(
        torch.argmax(logits_with_paddings[:-num_paddings], dim=-1),
        torch.argmax(logits_no_paddings, dim=-1),
    )

    torch.testing.assert_close(
        attn_with_paddings._mc_attn._positions[0][:-1],
        attn_no_paddings._mc_attn._positions[0][:-1],
        rtol=rtol,
        atol=atol,
    )

    torch.testing.assert_close(
        attn_with_paddings._mc_attn._tokens,
        attn_no_paddings._mc_attn._tokens,
        rtol=rtol,
        atol=atol,
    )

    torch.testing.assert_close(
        attn_with_paddings._mc_attn._kv_by_device[0][0][:, :-1, :, :, :],
        attn_no_paddings._mc_attn._kv_by_device[0][0][:, :-1, :, :, :],
        rtol=rtol,
        atol=atol,
    )

    torch.testing.assert_close(
        attn_with_paddings._mc_attn._kv_by_device[0][1][:, :-1, :, :, :],
        attn_no_paddings._mc_attn._kv_by_device[0][1][:, :-1, :, :, :],
        rtol=rtol,
        atol=atol,
    )
