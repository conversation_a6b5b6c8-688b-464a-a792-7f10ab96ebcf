"""An implementation of the CodeGen forward pass without torch.nn."""

import functools
import logging
from concurrent import futures
from pathlib import Path
from typing import Callable, Dict, List, Sequence

import torch
import torch.nn.functional as F
from torch import distributed as dist

from base.fastforward import (
    cached_attention,
    fwd,
    fwd_torch,
    parallel,
    positional_embeddings,
)
from base.fastforward.parallel import ParallelConfig, ParallelContext

WEIGHT_DTYPE = torch.float16


# This token sequence corresponds to the string:
# "<|python|># -*- coding: utf-8 -*-\n#\n# Copyright (C"
# This sequence is produced by the codegen 350M model if just the token 50256
# is given as the first token. We use this sequence to check if the model
# produces the correct continuations.
CG_KNOWN_SEQUENCE = [
    50256,
    27,
    91,
    29412,
    91,
    29,
    2,
    532,
    9,
    12,
    19617,
    25,
    3384,
    69,
    12,
    23,
    532,
    9,
    12,
    198,
    2,
    198,
    2,
    15069,
    357,
    34,
]


def shard(
    tensor: torch.Tensor, axis: int, process_idx: int, num_processes: int
) -> torch.Tensor:
    if num_processes == 1:
        return tensor
    s = tensor.shape
    shard_size, remainder = divmod(s[axis], num_processes)
    assert remainder == 0
    # Exploits that torch.split creates views of the original tensor.
    return torch.split(tensor, split_size_or_sections=shard_size, dim=axis)[process_idx]


def _get_state_dict_from_pipeline_files(
    load_dir: Path,
    num_transformer_layers: int,
    output_type: fwd.OutputTensorType,
    target_device="cuda",
):
    """Load the pipeline model states from a deepspeed checkpoint and put them in a simple state_dict."""
    sd = {}

    layer = torch.load(
        load_dir / "layer_00-model_00-model_states.pt", map_location=target_device
    )
    sd["embed.dummy"] = layer["dummy"]
    sd["embed.word_embeddings.weight"] = layer["word_embeddings.weight"]

    def load_layer(i):
        result_dict = {}

        layer = torch.load(
            load_dir / f"layer_{i+2:02d}-model_00-model_states.pt",
            map_location=target_device,
        )
        for key in [
            "input_layernorm.weight",
            "input_layernorm.bias",
            "attention.query_key_value.weight",
            "attention.query_key_value.bias",
            # "attention.rotary_emb.inv_freq",  # usually not part of the ckpt; will be recomputed
            "attention.dense.weight",
            "attention.dense.bias",
            "mlp.dense_h_to_4h.weight",
            "mlp.dense_h_to_4h.bias",
            "mlp.dense_4h_to_h.weight",
            "mlp.dense_4h_to_h.bias",
        ]:
            assert key in layer, (key, layer.keys())
            result_dict[f"transformer_layers.{i}.{key}"] = layer[key]
        logging.info("Loaded CodeGen layer %s", i)
        return result_dict

    with futures.ThreadPoolExecutor(max_workers=16) as pool:
        jobs = pool.map(load_layer, range(num_transformer_layers))
        for result in jobs:
            sd.update(result)

    layer = torch.load(
        load_dir / f"layer_{num_transformer_layers + 3:02d}-model_00-model_states.pt",
        map_location=target_device,
    )
    sd["final_norm.weight"] = layer["norm.weight"]
    sd["final_norm.bias"] = layer["norm.bias"]
    layer = torch.load(
        load_dir / f"layer_{num_transformer_layers + 4:02d}-model_00-model_states.pt",
        map_location=target_device,
    )
    if output_type == fwd.OutputTensorType.VOCAB_LOGITS:
        sd["final_projection.final_linear.weight"] = layer["final_linear.weight"]
        if "final_linear.bias" in layer:
            sd["final_projection.final_linear.bias"] = layer["final_linear.bias"]

    logging.info("Done loading model.")
    for k, v in sd.items():
        sd[k] = v.to(WEIGHT_DTYPE)
    logging.info("Weight count %s", number_of_weights(sd))
    return sd


def norm(t: torch.Tensor, w, b, eps):
    return F.layer_norm(t, t.size()[-1:], w, b, eps=eps)


class QKV:
    """Computes the layer norm and QKV projections."""

    def __init__(
        self,
        qkv_weights: torch.Tensor,
        qkv_bias: torch.Tensor | None,
        attn_proj_weights: torch.Tensor,
        attn_proj_bias: torch.Tensor | None,
        num_heads: int,
        head_dim: int,
        layer_idx: int,
        process_idx: int,
        num_processes: int,
        parallel_context: ParallelContext,
        reduce: bool = True,
    ):
        self.layer_idx = layer_idx
        self.process_idx = process_idx
        self.parallel_context = parallel_context
        assert parallel_context.num_processes == num_processes
        assert parallel_context.tp_rank == process_idx
        self.num_processes = num_processes
        self.reduce = reduce

        self.qkv_weights = qkv_weights
        if num_processes > 1:
            emb_dim = qkv_weights.shape[0]
            qkv_weights = qkv_weights.view(emb_dim, num_heads, head_dim * 3)
            assert num_heads % num_processes == 0
            num_heads_per_device = num_heads // num_processes
            qkv_weights = qkv_weights[
                :,
                process_idx * num_heads_per_device : (process_idx + 1)
                * num_heads_per_device,
            ]
            self.qkv_weights = qkv_weights.view(emb_dim, -1).clone()
        self.qkv_bias = qkv_bias
        if qkv_bias is not None and torch.all(qkv_bias == 0):
            self.qkv_bias = None
        self.attn_proj_weights = shard(
            attn_proj_weights,
            axis=0,
            process_idx=process_idx,
            num_processes=num_processes,
        )
        self.attn_proj_bias = attn_proj_bias
        if attn_proj_bias is not None and torch.all(attn_proj_bias == 0):
            self.attn_proj_bias = None

    def __call__(
        self, emb_ln: torch.Tensor, attn: cached_attention.Attention
    ) -> Callable[[], torch.Tensor]:
        qkv = torch.matmul(emb_ln, self.qkv_weights)
        attn_result = attn(qkv, self.layer_idx, parallel_ctx=self.parallel_context)
        attn_out_proj = torch.matmul(attn_result, self.attn_proj_weights)

        handle = None
        if self.num_processes > 1 and self.reduce:
            handle = dist.all_reduce(attn_out_proj, op=dist.ReduceOp.SUM, async_op=True)

        def future(handle):
            if handle is not None:
                handle.wait()
            return attn_out_proj

        return functools.partial(future, handle)


class MLP:
    """A multi-layer perceptron with model parallelism."""

    def __init__(
        self,
        w1: torch.Tensor,
        w2: torch.Tensor,
        activation_fn: Callable[[torch.Tensor], torch.Tensor],
        b1: torch.Tensor | None = None,
        b2: torch.Tensor | None = None,
        process_idx: int = 0,
        num_processes: int = 1,
        reduce: bool = True,
        activation_dtype=torch.float16,
        max_batch_size: int = 1024,  # only needed if auto_capture_graphs is True
        auto_capture_graphs: bool = False,
    ):
        this_shard = functools.partial(
            shard, process_idx=process_idx, num_processes=num_processes
        )
        self.w1 = this_shard(w1, axis=1)
        self.w2 = this_shard(w2, axis=0)
        self.activation_fn = activation_fn
        if b1 is not None and torch.allclose(b1, torch.zeros_like(b1)):
            b1 = None
        if b2 is not None and torch.allclose(b2, torch.zeros_like(b2)):
            b2 = None
        self.b1 = b1
        if b1 is not None:
            self.b1 = this_shard(b1, axis=0)
        self.b2 = b2  # b2 not sharded
        self.process_idx = process_idx
        self.num_processes = num_processes
        self.reduce = reduce
        self.auto_capture_graphs = auto_capture_graphs

        self.graphs: Dict[int, torch.cuda.CUDAGraph] = {}
        self.max_batch_size = max_batch_size
        self.static_input: torch.Tensor = torch.zeros(
            size=(self.max_batch_size, self.w1.shape[0]),
            dtype=activation_dtype,
            device=self.w1.device,
        )
        self.static_output: torch.Tensor = torch.zeros_like(self.static_input)

    def forward(self, emb: torch.Tensor) -> Callable[[], torch.Tensor]:
        intermediate = torch.matmul(emb, self.w1)
        if self.b1 is not None:
            intermediate += self.b1
        intermediate = self.activation_fn(intermediate)

        # pos_intermediate = F.relu(intermediate)
        # pos_intermediate = torch.sum(pos_intermediate, axis=0)
        # # print(f"{pos_intermediate.shape=}")
        # idxs = torch.nonzero(pos_intermediate)
        # # print(f"{idxs.shape=}")
        # idxs = idxs.reshape(-1)
        # sub_intermediate = intermediate[:, idxs]
        # sub_w2 = self.w2[idxs]
        # # print(f"{idxs=} {sub_w2=} {sub_intermediate=}")
        # out = torch.matmul(sub_intermediate, sub_w2)
        out = torch.matmul(intermediate, self.w2)

        if self.b2 is not None and self.process_idx == 0:
            out += self.b2

        handle = None
        if self.num_processes > 1 and self.reduce:
            handle = dist.all_reduce(out, op=dist.ReduceOp.SUM, async_op=True)

        def future():
            if handle is not None:
                handle.wait()
            return out

        return future

    def _get_graph(self, batch: int) -> torch.cuda.CUDAGraph:
        if batch not in self.graphs:
            static_input = self.static_input[:batch]
            g = torch.cuda.CUDAGraph()
            self.graphs[batch] = g
            # warmup in side stream
            warmup_stream = torch.cuda.Stream(device=self.process_idx)
            warmup_stream.wait_stream(torch.cuda.current_stream())  # type: ignore
            with torch.cuda.stream(warmup_stream):  # type: ignore
                for _ in range(3):
                    self.static_output[:batch] = self.forward(static_input)()

            capture_stream = torch.cuda.Stream(device=self.process_idx)
            capture_stream.wait_stream(warmup_stream)  # type: ignore
            # capture
            with torch.cuda.graph(g, stream=capture_stream):
                self.static_output[:batch] = self.forward(static_input)()
            torch.cuda.current_stream().wait_stream(capture_stream)

        return self.graphs[batch]

    def __call__(self, emb: torch.Tensor) -> Callable[[], torch.Tensor]:
        if self.auto_capture_graphs:
            batch = emb.shape[0]
            assert batch <= self.max_batch_size
            g = self._get_graph(batch)
            self.static_input[:batch] = emb
            g.replay()
            return lambda: self.static_output[:batch]
        else:
            return self.forward(emb)


def compute_layer(
    w: dict,
    emb: torch.Tensor,
    mlp: MLP,
    qkv: QKV,
    attn: cached_attention.Attention,
    layer_idx: int,
    process_idx: int,
    num_processes: int,
    layernorm_eps: float,
):
    del layer_idx
    del process_idx
    # dict_keys([
    #   'input_layernorm.weight',
    #   'input_layernorm.bias',
    #   'attention.query_key_value.weight',
    #   'attention.query_key_value.bias',
    #   'attention.dense.weight',
    #   'attention.dense.bias',
    #   'mlp.dense_h_to_4h.weight',
    #   'mlp.dense_h_to_4h.bias',
    #   'mlp.dense_4h_to_h.weight',
    #   'mlp.dense_4h_to_h.bias'])
    # batch_size, emb_dim = emb.size()
    emb_ln1 = norm(
        emb, w["input_layernorm.weight"], w["input_layernorm.bias"], eps=layernorm_eps
    )
    attn_result = qkv(emb_ln1, attn)
    mlp_res = mlp(emb_ln1)
    attn_result = attn_result()
    mlp_res = mlp_res()
    step_result = attn_result + mlp_res
    if num_processes > 1:
        dist.all_reduce(step_result, op=dist.ReduceOp.SUM)
    emb += step_result
    return emb


def _layer_weights(weights: dict, layer_idx: int) -> dict:
    d = {}
    for k in weights.keys():
        layer_prefix = f"transformer_layers.{layer_idx}."
        if k.startswith(layer_prefix):
            d[k[len(layer_prefix) :]] = weights[k]
    return d


def step(
    tokens: Sequence[int],
    attn: cached_attention.Attention,
    weights: Dict[str, torch.Tensor],
    layer_weights: List[Dict[str, torch.Tensor]],
    mlps: List[MLP],
    qkvs: List[QKV],
    num_layers: int,
    process_idx: int,
    num_processes: int,
    output_type: fwd.OutputTensorType,
    layernorm_eps: float,
) -> fwd.ModelOutput:
    assert len(layer_weights) == num_layers == len(mlps)
    device_str = f"cuda:{process_idx}"
    tokens_tensor = torch.tensor(tokens, dtype=torch.int32, device=device_str)
    attn.register_tokens_get_positions(tokens_tensor, process_idx=process_idx)

    emb = weights["embed.word_embeddings.weight"][tokens_tensor]
    for layer_idx, w, mlp, qkv in zip(range(num_layers), layer_weights, mlps, qkvs):
        emb = compute_layer(
            w=w,
            emb=emb,
            mlp=mlp,
            qkv=qkv,
            attn=attn,
            layer_idx=layer_idx,
            process_idx=process_idx,
            num_processes=num_processes,
            layernorm_eps=layernorm_eps,
        )
    # if output_type == fwd.OutputTensorType.NONE:
    #     return torch.zeros(size=(1,))
    emb = norm(
        emb,
        w=weights["final_norm.weight"],
        b=weights["final_norm.bias"],
        eps=layernorm_eps,
    )
    if output_type == fwd.OutputTensorType.EMBEDDING:
        return fwd_torch.TorchEmbedding(emb)
    elif output_type == fwd.OutputTensorType.VOCAB_LOGITS:
        scores = torch.matmul(emb, weights["final_projection.final_linear.weight"].T)
        scores += weights["final_projection.final_linear.bias"]
        return fwd_torch.TorchLogits2D(scores)
    else:
        raise ValueError(f"Unknown output type {output_type}")


def number_of_weights(weights: Dict[str, torch.Tensor]) -> str:
    num_weights = 0
    for w in weights.values():
        num_weights += w.numel()
    return f"{num_weights / 1_000_000_000:.2f}b weights"


class CodeGenAttentionFactory(fwd.AttentionFactory):
    """Creates attention caches for codegen models."""

    def __init__(
        self,
        model_spec: fwd.ModelSpec,
        num_processes: int = 1,
        attention_impl: cached_attention.AttentionImpl = cached_attention.AttentionImpl.BATCHED_FLASH,
    ):
        self.model_spec = model_spec
        self.parallel_config = ParallelConfig.from_legacy_config(
            num_processes, use_sequence_parallel=False
        )
        self.attention_impl = attention_impl

    def create_cache_pool(
        self, max_length: int, num_attention_caches: int
    ) -> cached_attention.MultiCacheAttention:
        assert self.model_spec.rotary_scaling_factor == 1.0
        rotary_config = positional_embeddings.RotaryConfig(
            rotary_ratio=self.model_spec.rotary_pct,
            rotary_theta=self.model_spec.rotary_theta,
            max_position_embeddings=4096,  # hardcoded; unknown whether this can be changed
            rotary_interleave=self.model_spec.rotary_interleave,
        )
        attn = cached_attention.MultiCacheAttentionImplementation(
            num_caches=num_attention_caches,
            num_layers=self.model_spec.num_layers,
            num_heads=self.model_spec.num_heads,
            queries_per_head=1,
            max_len=max_length,
            head_dim=self.model_spec.head_dim,
            parallel_config=self.parallel_config,
            attention_impl=self.attention_impl,
            rotary_config=rotary_config,
        )
        return attn


# Deprecated: call CodeGenAttentionFactory directly
def get_attention_factory(
    model_spec: fwd.ModelSpec, num_processes: int = 1
) -> fwd.AttentionFactory:
    return CodeGenAttentionFactory(model_spec=model_spec, num_processes=num_processes)


def _generate_step_fn(
    model_spec: fwd.ModelSpec,
    process_idx: int = 0,
    num_processes: int = 1,
    auto_capture_graphs: bool = False,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    logging.info(
        "Process %s: Loading model with CodeGen architecture: %s",
        process_idx,
        model_spec,
    )
    parallel_ctx = ParallelContext(
        ParallelConfig.from_legacy_config(num_processes, False), process_idx
    )
    # TODO: load only the weights that we need for this process.
    sd = _get_state_dict_from_pipeline_files(
        Path(model_spec.checkpoint_path),
        model_spec.num_layers,
        output_type=output_type,
        target_device=f"cuda:{process_idx}",
    )

    mlps = []
    qkvs = []
    ws = []
    for layer_idx in range(model_spec.num_layers):
        w = _layer_weights(sd, layer_idx)
        ws.append(w)
        mlp = MLP(
            w1=w["mlp.dense_h_to_4h.weight"].T,
            w2=w["mlp.dense_4h_to_h.weight"].T,
            activation_fn=F.gelu,
            b1=w["mlp.dense_h_to_4h.bias"],
            b2=w["mlp.dense_4h_to_h.bias"],
            process_idx=process_idx,
            num_processes=num_processes,
            auto_capture_graphs=auto_capture_graphs,
            reduce=False,
        )
        mlps.append(mlp)
        qkv = QKV(
            qkv_weights=w["attention.query_key_value.weight"].T,
            qkv_bias=w["attention.query_key_value.bias"],
            attn_proj_weights=w["attention.dense.weight"].T,
            attn_proj_bias=w["attention.dense.bias"],
            num_heads=model_spec.num_heads,
            head_dim=model_spec.head_dim,
            layer_idx=layer_idx,
            process_idx=process_idx,
            num_processes=num_processes,
            parallel_context=parallel_ctx,
            reduce=False,
        )
        qkvs.append(qkv)

    return functools.partial(
        step,
        weights=sd,
        layer_weights=ws,
        mlps=mlps,
        qkvs=qkvs,
        num_layers=model_spec.num_layers,
        process_idx=process_idx,
        num_processes=num_processes,
        output_type=output_type,
        layernorm_eps=model_spec.norm_eps,
    )


def generate_step_fn(
    model_spec: fwd.ModelSpec,
    num_processes: int = 1,
    auto_capture_graphs: bool = False,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    assert num_processes > 0
    if num_processes == 1:
        return _generate_step_fn(
            model_spec, auto_capture_graphs=auto_capture_graphs, output_type=output_type
        )
    else:
        mp = parallel.ParallelRunner(num_processes=num_processes)
        return mp.initialize(
            _generate_step_fn,
            model_spec=model_spec,
            auto_capture_graphs=auto_capture_graphs,
            embedding_type=output_type,
        )
