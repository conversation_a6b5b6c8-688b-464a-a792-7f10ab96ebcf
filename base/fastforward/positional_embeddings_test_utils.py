"""Utilities for positional embedding tests.

Current content:
1. A legacy implementation of `FusedRotaryEmbeddings` before the introduction of YaRN.
2. Official reference implementation of YaRN from https://github.com/jquesnelle/yarn.
   Specifically, this file contains code in
   `scaled_rope/LlamaDynamicYaRNScaledRotaryEmbedding.py`.
3. LLaMa 3.1 official implementation of YaRN from
   https://github.com/meta-llama/llama-models. Specifically, this file contains code in
   `models/llama3_1/api/model.py`.
"""

import math
from dataclasses import dataclass

import torch

try:
    from base.fastforward import compiled_rotary

    _compiled_rotary_exception = None
    _compiled_rotary_exception_msg = None
except ImportError as ex:
    msg = """There was an issue importing the `compiled_rotary` package. If you
             are outside a bazel-managed environment, you can build the package with:
             > bazel run //base/fastforward:install
          """
    compiled_rotary = None
    _compiled_rotary_exception = ex
    _compiled_rotary_exception_msg = msg


try:
    from base.fastforward import compiled_rotary_qkv

    _compiled_rotary_qkv_exception = None
    _compiled_rotary_qkv_exception_msg = None
except ImportError as ex:
    msg = """There was an issue importing the `compiled_rotary_qkv` package. If you
             are outside a bazel-managed environment, you can build the package with:
             > bazel run //base/fastforward:install
          """
    compiled_rotary_qkv = None
    _compiled_rotary_qkv_exception = ex
    _compiled_rotary_qkv_exception_msg = msg

# ==================== Legacy FusedRotaryEmbedding ====================

Device = torch.device | str


@dataclass(frozen=True)
class LegacyRotaryConfig:
    """Configuration for the rotary embeddings."""

    rotary_ratio: float
    """Ratio of head dimension to which we apply rotary embeddings.

    Also called `rotary_pct` in other locations, but it is always a value in [0, 1]
    """

    rotary_theta: float
    """Base power for rope."""

    max_position_embeddings: int
    """Max position size used with absolute position embeddings."""

    rotary_scaling_factor: float = 1.0
    """Scaling factor applied to the sequence length when computing the rotation."""

    rotary_interleave: bool = True
    """Whether to interleave the rotary embeddings."""

    def __post_init__(self):
        assert 0.0 <= self.rotary_ratio <= 1.0
        assert self.rotary_theta > 0.0
        assert self.max_position_embeddings > 0


class LegacyFusedRotaryEmbedding(torch.nn.Module):
    """Legacy implementation of the fused rotary embeddings for regression testing."""

    def __init__(
        self,
        head_dim: int,
        config: LegacyRotaryConfig,
        device: Device = "cuda",
    ):
        super().__init__()
        if config.rotary_ratio is None:
            rope_dim = head_dim
        else:
            rope_dim = int(config.rotary_ratio * head_dim)
        assert head_dim >= rope_dim
        assert rope_dim % 2 == 0
        self.rope_dim = rope_dim

        self.head_dim = head_dim
        self.max_seq_len = config.max_position_embeddings
        self.rotary_interleave = config.rotary_interleave
        self.ones = torch.ones(3, dtype=torch.float32, device=device)

        # pre-compute the cos and sin tensors
        dim_range = torch.arange(0, rope_dim, 2, dtype=torch.float32, device=device)
        freqs_powers = dim_range[: (rope_dim // 2)] / rope_dim

        freqs = 1.0 / (config.rotary_theta**freqs_powers)
        t = (
            torch.arange(
                config.max_position_embeddings, dtype=torch.float32, device=device
            )
            / config.rotary_scaling_factor
        )
        freqs = torch.outer(t, freqs)
        self.freqs_cos = freqs.cos()
        self.freqs_sin = freqs.sin()
        assert self.freqs_cos.shape == (config.max_position_embeddings, rope_dim // 2)
        assert self.freqs_sin.shape == (config.max_position_embeddings, rope_dim // 2)

    def forward(self, x: torch.Tensor, pos: torch.Tensor):
        assert x.size(0) <= self.max_seq_len, f"{x.size(0)=} > {self.max_seq_len=}"
        assert x.size(-1) == self.head_dim, f"Expect {self.head_dim=}, got {x.size()=}."
        assert pos.ndim == 1
        assert pos.size(0) == x.size(0)

        freqs_cos, freqs_sin = self.freqs_cos, self.freqs_sin

        # NOTE: we enforce that the compiled kernels are available. I have left the other branch
        # below for debugging or to move over to `torch.compile` once we can use it in production.
        if compiled_rotary is None:
            raise RuntimeError(
                _compiled_rotary_exception_msg
            ) from _compiled_rotary_exception
        assert compiled_rotary is not None

        # TODO(Xuanyi or Carl): support the rotary_interleave FLAG in the compiled kernel.
        if self.rotary_interleave:
            return compiled_rotary.rotary_embed(x, freqs_cos, freqs_sin, pos)
        else:
            x = _not_to_interleave(x)
            x = compiled_rotary.rotary_embed(x, freqs_cos, freqs_sin, pos)
            return _interleave_to_not(x)

    def rotary_qkv(
        self,
        q: torch.Tensor,
        pos: torch.Tensor,
        k: torch.Tensor,
        k_dest: torch.Tensor,
        v: torch.Tensor,
        v_dest: torch.Tensor,
        kv_cache_idxs: torch.Tensor,
    ):
        """Integrated kernel for rotary embeddings for query and key; also copying keys and values into target tensors.

        Args:
            q: Activations of shape (ntokens, q_heads, headdim). This argument can be either
                query or key, as we have to apply the same rotary embeddings to both. For simplicity,
                we call it `q`, as this argument is intended to be used for the query if a key is
                also passed in as `k`.
            pos: Sequence indices of shape (ntokens,) for each element of `q`. Each index must lie
                in the range [0, max_seqlen).
            k: Activations of shape (ntokens, kv_heads, headdim). We apply rotary embeddings to the
                keys and copy them into the destination tensor.
            k_dest: Destination activations of shape (ncaches, ntokens, kv_heads, headdim).
            v: Activations of shape (ntokens, kv_heads, headdim). We never apply rotary embeddings to
                the values. We only copy them into the destination tensor.
            v_dest: Destination activations of shape (ncaches, ntokens, kv_heads, headdim).
            kv_cache_idxs: Indices of shape (ntokens,) in the range [0, ncaches). Used to index into
                `k_dest` and `v_dest`.
        """
        assert q.size(0) <= self.max_seq_len
        assert q.size(-1) == self.head_dim, f"Expect {self.head_dim=}, got {q.size()=}."
        assert pos.ndim == 1
        assert pos.size(0) == q.size(0)

        freqs_cos, freqs_sin = self._get_freqs(q)

        assert k.shape == v.shape
        assert k.shape[1:] == k_dest.shape[2:]
        assert v.shape[1:] == v_dest.shape[2:]
        assert (
            pos.shape == kv_cache_idxs.shape
        ), f"{pos.shape=} != {kv_cache_idxs.shape=}"

        assert (
            self.rotary_interleave
        ), "rotary_qkv is only supported for interleaved rotary embeddings."

        if compiled_rotary_qkv is None:
            raise RuntimeError(
                _compiled_rotary_qkv_exception_msg
            ) from _compiled_rotary_qkv_exception
        assert compiled_rotary_qkv is not None
        return compiled_rotary_qkv.rotary_embed_qkv(
            q,
            pos,  # using the same pos for both q and k
            k,
            v,
            freqs_cos,
            freqs_sin,
            pos,
            k_dest,
            v_dest,
            kv_cache_idxs,
            self.ones,
        )


# ==================== Official reference YaRN ====================


def _interleave_to_not(x: torch.Tensor):
    """Permute the tensor from [x_1, y_1, ..., x_n, y_n] to [x_1, ..., x_n, y_1, ..., y_n]."""
    x_aux = x.reshape(*x.shape[:-1], -1, 2).transpose(-2, -1)
    return x_aux.reshape(*x.shape)


def _not_to_interleave(x: torch.Tensor):
    """Permute the tensor from [x_1, ..., x_n, y_1, ..., y_n] to [x_1, y_1, ..., x_n, y_n]."""
    x_aux = x.reshape(*x.shape[:-1], 2, -1).transpose(-2, -1)
    return x_aux.reshape(*x.shape)


# Inverse dim formula to find dim based on number of rotations
def find_correction_dim(num_rotations, dim, base=10000, max_position_embeddings=2048):
    return (dim * math.log(max_position_embeddings / (num_rotations * 2 * math.pi))) / (
        2 * math.log(base)
    )


# Find dim range bounds based on rotations
def find_correction_range(
    low_rot, high_rot, dim, base=10000, max_position_embeddings=2048
):
    low = math.floor(find_correction_dim(low_rot, dim, base, max_position_embeddings))
    high = math.ceil(find_correction_dim(high_rot, dim, base, max_position_embeddings))
    return max(low, 0), min(high, dim - 1)  # Clamp values just in case


def linear_ramp_mask(min, max, dim):
    if min == max:
        max += 0.001  # Prevent singularity

    linear_func = (torch.arange(dim, dtype=torch.float32) - min) / (max - min)
    ramp_func = torch.clamp(linear_func, 0, 1)
    return ramp_func


def get_mscale(scale=1):
    if scale <= 1:
        return 1.0
    return 0.1 * math.log(scale) + 1.0


class LlamaDynamicYaRNScaledRotaryEmbedding(torch.nn.Module):
    def __init__(
        self,
        dim,
        max_position_embeddings=2048,
        base=10000,
        original_max_position_embeddings=2048,
        extrapolation_factor=1,
        attn_factor=1,
        beta_fast=32,
        beta_slow=1,
        finetuned=False,
        device=None,
    ):
        super().__init__()

        self.dim = dim
        self.max_position_embeddings = max_position_embeddings
        self.base = base
        self.original_max_position_embeddings = original_max_position_embeddings
        self.extrapolation_factor = extrapolation_factor
        self.attn_factor = attn_factor
        self.beta_fast = beta_fast
        self.beta_slow = beta_slow

        if finetuned:
            self.yarn(
                self.max_position_embeddings / self.original_max_position_embeddings,
                device,
            )
        else:
            inv_freq = 1.0 / (
                base ** (torch.arange(0, dim, 2).float().to(device) / dim)
            )
            self.register_buffer("inv_freq", inv_freq)
            self.mscale = 1

        # Build here to make `torch.jit.trace` work.
        self.max_seq_len_cached = max_position_embeddings
        t = torch.arange(
            self.max_seq_len_cached,
            device=self.inv_freq.device,
            dtype=self.inv_freq.dtype,
        )
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        # Different from paper, but it uses a different permutation in order to obtain the same calculation
        emb = torch.cat((freqs, freqs), dim=-1)
        self.emb = emb
        dtype = torch.get_default_dtype()

        self.register_buffer(
            "cos_cached",
            (emb.cos() * self.mscale)[None, None, :, :].to(dtype),
            persistent=False,
        )
        self.register_buffer(
            "sin_cached",
            (emb.sin() * self.mscale)[None, None, :, :].to(dtype),
            persistent=False,
        )

    def forward(self, x, seq_len=None):
        # x: [bs, num_attention_heads, seq_len, head_size]
        # This `if` block is unlikely to be run after we build sin/cos in `__init__`. Keep the logic here just in case.
        assert seq_len is not None
        if seq_len > self.max_seq_len_cached:
            self.max_seq_len_cached = seq_len

            self.yarn(seq_len / self.original_max_position_embeddings, x.device)

            t = torch.arange(
                self.max_seq_len_cached, device=x.device, dtype=self.inv_freq.dtype
            )
            freqs = torch.einsum("i,j->ij", t, self.inv_freq)
            # Different from paper, but it uses a different permutation in order to obtain the same calculation
            emb = torch.cat((freqs, freqs), dim=-1).to(x.device)

            self.register_buffer(
                "cos_cached",
                (emb.cos() * self.mscale)[None, None, :, :].to(x.dtype),
                persistent=False,
            )
            self.register_buffer(
                "sin_cached",
                (emb.sin() * self.mscale)[None, None, :, :].to(x.dtype),
                persistent=False,
            )
        return (
            self.cos_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
            self.sin_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
        )

    def yarn(self, scale, device):
        pos_freqs = self.base ** (
            torch.arange(0, self.dim, 2).float().to(device) / self.dim
        )
        inv_freq_extrapolation = 1.0 / pos_freqs
        inv_freq_interpolation = 1.0 / (scale * pos_freqs)

        low, high = find_correction_range(
            self.beta_fast,
            self.beta_slow,
            self.dim,
            self.base,
            self.original_max_position_embeddings,
        )
        inv_freq_mask = (
            (1 - linear_ramp_mask(low, high, self.dim // 2).float().to(device))
            * self.extrapolation_factor
        )  # Get n-d rotational scaling corrected for extrapolation
        inv_freq = (
            inv_freq_interpolation * (1 - inv_freq_mask)
            + inv_freq_extrapolation * inv_freq_mask
        )

        self.register_buffer("inv_freq", inv_freq)
        self.mscale = float(
            get_mscale(scale) * self.attn_factor
        )  # Get n-d magnitude scaling corrected for interpolation


# ==================== LLaMa 3.1 NTK-by-part ====================


def ref_llama3_1_apply_scaling(freqs: torch.Tensor):
    """LLaMa 3.1 official freq scaling.

    Copied from `apply_scaling()` in
    https://github.com/meta-llama/llama-models/blob/f45cdfd624b98b6655540f7101d8d9cb432e631c/models/llama3_1/reference_impl/model.py#L45-L67.
    """
    # Values obtained from grid search
    scale_factor = 8
    low_freq_factor = 1
    high_freq_factor = 4
    old_context_len = 8192  # original llama3 length

    low_freq_wavelen = old_context_len / low_freq_factor
    high_freq_wavelen = old_context_len / high_freq_factor
    new_freqs = []
    for freq in freqs:
        wavelen = 2 * math.pi / freq
        if wavelen < high_freq_wavelen:
            new_freqs.append(freq)
        elif wavelen > low_freq_wavelen:
            new_freqs.append(freq / scale_factor)
        else:
            assert low_freq_wavelen != high_freq_wavelen
            smooth = (old_context_len / wavelen - low_freq_factor) / (
                high_freq_factor - low_freq_factor
            )
            new_freqs.append((1 - smooth) * freq / scale_factor + smooth * freq)
    return torch.tensor(new_freqs, dtype=freqs.dtype, device=freqs.device)


def ref_llama3_1_precompute_freqs_cis(
    dim: int, end: int, theta: float = 10000.0, use_scaled: bool = False
):
    """LLaMa 3.1 official RoPE table precomputation.

    Copied from `precompute_freqs_cis()` in
    https://github.com/meta-llama/llama-models/blob/f45cdfd624b98b6655540f7101d8d9cb432e631c/models/llama3_1/reference_impl/model.py#L70-L79.
    """
    freqs = 1.0 / (theta ** (torch.arange(0, dim, 2)[: (dim // 2)].float() / dim))
    t = torch.arange(end, device=freqs.device, dtype=torch.float32)
    if use_scaled:
        freqs = ref_llama3_1_apply_scaling(freqs)
    freqs = torch.outer(t, freqs)
    freqs_cis = torch.polar(torch.ones_like(freqs), freqs)  # complex64
    return freqs_cis
