"""Helper functions for testing ffwd models."""

from typing import Sequence

import torch
from base.fastforward import fwd


def check_model_logits_on_target_sequence(
    step_fn: fwd.ForwardStepFn,
    attn_factory: fwd.AttentionFactory,
    tokens: list[int],
    start_compare_index: int,
):
    """Check that the model produces the correct tokens on the target sequence."""
    attn = attn_factory(max_length=128)
    logits = step_fn(list(tokens), attn).checked_cast(torch.Tensor)
    maxes = torch.argmax(logits, dim=-1).to(dtype=torch.int32)
    tokens_to_match = tokens[start_compare_index:]
    assert tokens_to_match == maxes[start_compare_index - 1 : -1].tolist()


def check_logits_are_close(
    step_fn_1: fwd.ForwardStepFn,
    step_fn_2: fwd.ForwardStepFn,
    attn_factory_1: fwd.AttentionFactory,
    attn_factory_2: fwd.AttentionFactory,
    test_tokens: list[int],
    kv_len=None,
    ignore_positions: Sequence[int] = (),
    extra_kv_len: int = 0,
    allowed_mismatches: int = 0,
):
    """Compare the logits of the top tokens generated by two models."""
    if kv_len is None:
        kv_len = len(test_tokens) + extra_kv_len
    assert len(test_tokens) <= kv_len, f"{len(test_tokens)=} > {kv_len=}"

    attn_1 = attn_factory_1(kv_len)
    attn_2 = attn_factory_2(kv_len)

    # run the models
    logits_1 = step_fn_1(list(test_tokens), attn_1).checked_cast(torch.Tensor)
    logits_2 = step_fn_2(list(test_tokens), attn_2).checked_cast(torch.Tensor)

    # ideally, we want to have the following guarantee:
    # torch.testing.assert_close(logits_1, logits_2)
    # but in practice, logits are not very close, so here we just check that
    # the logits of the greedy tokens are the same.

    greedy_tokens = torch.argmax(logits_2, dim=-1)
    greedy_token_scores_1 = torch.tensor(
        [logits_1[i, t] for i, t in enumerate(greedy_tokens)]
    )
    greedy_token_scores_2 = torch.tensor(
        [logits_2[i, t] for i, t in enumerate(greedy_tokens)]
    )
    for i in ignore_positions:
        greedy_token_scores_1[i] = 0
        greedy_token_scores_2[i] = 0
    matches = torch.isclose(
        greedy_token_scores_1, greedy_token_scores_2, rtol=1e-1, atol=1e-1
    )
    if matches.sum().item() + allowed_mismatches < len(test_tokens):
        print(f"Too many mismatches: {len(test_tokens) - matches.sum().item()}")
        # Re-call into `assert_close` for a good error message
        torch.testing.assert_close(
            greedy_token_scores_1, greedy_token_scores_2, rtol=1e-1, atol=1e-1
        )


def check_if_model_generates_target_sequence(
    step_fn: fwd.ForwardStepFn,
    attn_factory: fwd.AttentionFactory,
    given_prefix: Sequence[int],
    target_sequence: Sequence[int],
    allowed_mismatches: int = 0,
    extra_kv_len: int = 0,
):
    max_seq_length = len(given_prefix) + len(target_sequence) + extra_kv_len
    attn = attn_factory(
        max_length=max_seq_length,
    )

    print(f"{given_prefix=}", flush=True)

    generated_tokens = []
    model_inputs = list(given_prefix)
    for target_token in target_sequence:
        logits = step_fn(model_inputs, attn).checked_cast(torch.Tensor)
        generated_token_tensor = torch.argmax(logits, dim=-1)
        generated_token = int(generated_token_tensor[-1])
        generated_tokens.append(generated_token)
        model_inputs = [target_token]
    print(f"{generated_tokens=}", flush=True)

    num_mismatches = sum(t1 != t2 for t1, t2 in zip(generated_tokens, target_sequence))
    if num_mismatches > allowed_mismatches:
        print(f"Too many mismatches: {num_mismatches}")
        print(f"{generated_tokens=}")
        print(f"{target_sequence=}")
    assert (
        num_mismatches <= allowed_mismatches
    ), f"{generated_tokens=}, {target_sequence=}, {num_mismatches=}, {allowed_mismatches=}"


def check_batched_equals_sequential(
    step_fn: fwd.ForwardStepFn,
    attn_factory: fwd.AttentionFactory,
    prompt: Sequence[int],
    kv_len=None,
    extra_kv_len: int = 0,
):
    if kv_len is None:
        kv_len = len(prompt) + extra_kv_len
    attn_bat = attn_factory(kv_len)

    logits_batched = step_fn(prompt, attn_bat).checked_cast(torch.Tensor)

    attn_seq = attn_factory(kv_len)
    logits_sequential: list[torch.Tensor] = []

    print(f"{prompt=}")
    for i, t in enumerate(prompt):
        print(f"{i=}")
        logits = step_fn([t], attn_seq).checked_cast(torch.Tensor)
        logits_sequential.append(logits)

    logits_sequential_bycat = torch.cat(logits_sequential, dim=0)

    assert logits_batched.shape == logits_sequential_bycat.shape

    # Ideally we want to test this:
    # torch.testing.assert_close(logits_batched, logits_sequential, rtol=1e-2, atol=0.3)
    # But logits are not very close, so we only compare on
    # the top 3 tokens.
    torch.set_printoptions(precision=2, edgeitems=10, sci_mode=False)
    top_batched = torch.topk(logits_batched, 3)
    top_sequential = torch.topk(logits_sequential_bycat, 3)
    print(f"{top_batched.indices=}")
    print(f"{top_sequential.indices=}")
    print(f"{top_batched.values=}")
    print(f"{top_sequential.values=}")
    # Note that this check often breaks on H100s. The tests are meant
    # to be run on L4 GPUs.
    torch.testing.assert_close(
        top_batched.values, top_sequential.values, rtol=1e-2, atol=0.3
    )

    torch.testing.assert_close(
        attn_bat._mc_attn._positions[0][:-1],  # type: ignore
        attn_seq._mc_attn._positions[0][:-1],  # type: ignore
    )

    n = len(prompt)
    # check that keys are equal
    kv_dtype = attn_bat._mc_attn._kv_by_device[0][0].dtype  # type: ignore
    if kv_dtype == torch.float8_e4m3fn:
        kv_dtype = (
            torch.float16
        )  # In the fp8 case, up-convert so that `assert_close` works.
    torch.testing.assert_close(
        attn_bat._mc_attn._kv_by_device[0][0][:, :-1, :n, :, :].to(dtype=kv_dtype),  # type: ignore
        attn_seq._mc_attn._kv_by_device[0][0][:, :-1, :n, :, :].to(dtype=kv_dtype),  # type: ignore
        rtol=1e-2,
        atol=2e-1,
    )

    # check that values are equal
    torch.testing.assert_close(
        attn_bat._mc_attn._kv_by_device[0][1][:, :-1, :n, :, :].to(dtype=kv_dtype),  # type: ignore
        attn_seq._mc_attn._kv_by_device[0][1][:, :-1, :n, :, :].to(dtype=kv_dtype),  # type: ignore
        rtol=1e-2,
        atol=2e-1,
    )
