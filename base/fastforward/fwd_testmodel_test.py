"""Tests the test model."""

import pytest
import torch
from base.fastforward import fwd_testmodel
from base.fastforward import fwd


def run_model(
    step_fn: fwd.ForwardStepFn,
    attn,
    num_tokens: int = 10,
    tokens: list[int] | None = None,
) -> list[int]:
    if tokens is None:
        tokens = [13]
    produced_tokens = []
    for _ in range(num_tokens):
        logits = step_fn(tokens, attn).checked_cast(torch.Tensor)
        assert logits.ndim == 2
        tokens = torch.argmax(logits, dim=-1).cpu().numpy().tolist()
        assert tokens is not None
        tokens = [int(tokens[-1])]  # the last token is the new one
        produced_tokens.extend(tokens)
    return produced_tokens


def test_smoke():
    """Smoke test for a single step of the test model."""
    ms = fwd_testmodel.get_dummy_modelspec()
    step_fn = fwd_testmodel.generate_step_fn(ms)
    attn_factory = fwd_testmodel.get_attention_factory(ms, ignore_first_n=1)
    attn = attn_factory(128)
    run_model(step_fn, attn)


@pytest.mark.parametrize("ignore_first_n", [1, 2, 3, 4, 5, 6])
def test_fail_validate(ignore_first_n: int):
    """Check that ignore_first_n is larger than the list of tokens given as input."""
    ms = fwd_testmodel.get_dummy_modelspec()
    step_fn = fwd_testmodel.generate_step_fn(ms)
    attn_factory = fwd_testmodel.get_attention_factory(
        ms, ignore_first_n=ignore_first_n
    )
    attn = attn_factory(128)
    # The validation should fail; we only ignore the first
    tokens = [13, 23, 1, 2]
    if ignore_first_n < len(tokens):
        with pytest.raises(ValueError):
            step_fn(tokens, attn)
    else:
        step_fn(tokens, attn)  # should not raise


@pytest.mark.parametrize("reset_to", [0, 1, 2, 4, 5, 6])
def test_reset(reset_to: int):
    """Run model, reset to different positions, and check that rerun agrees."""
    ms = fwd_testmodel.get_dummy_modelspec()
    step_fn = fwd_testmodel.generate_step_fn(ms)
    attn_factory = fwd_testmodel.get_attention_factory(ms, ignore_first_n=2)
    attn = attn_factory(128)
    if reset_to == 0:
        initial_tokens = [5]
    else:
        initial_tokens = [5, 13]
    num_tokens = 5
    produced_tokens1 = run_model(
        step_fn, attn, num_tokens=num_tokens, tokens=initial_tokens
    )
    assert len(produced_tokens1) == num_tokens
    all_tokens1 = initial_tokens + produced_tokens1

    attn.reset(to_position=reset_to)
    next_token = all_tokens1[reset_to]
    produced_tokens2 = run_model(
        step_fn, attn, num_tokens=num_tokens, tokens=[next_token]
    )
    assert len(produced_tokens2) == num_tokens

    all_tokens2 = all_tokens1[: reset_to + 1] + produced_tokens2
    assert all(t1 == t2 for t1, t2 in zip(all_tokens1, all_tokens2))


def test_different_outputs():
    """Run model, reset to different positions, and check that rerun agrees."""
    ms = fwd_testmodel.get_dummy_modelspec()
    step_fn = fwd_testmodel.generate_step_fn(ms)
    attn_factory = fwd_testmodel.get_attention_factory(ms, ignore_first_n=2)
    attn = attn_factory(128)

    initial_tokens1 = [51, 13]
    initial_tokens2 = [32, 13]
    produced_tokens1 = run_model(step_fn, attn, tokens=initial_tokens1)
    attn.reset(0)
    produced_tokens2 = run_model(step_fn, attn, tokens=initial_tokens2)
    assert any(t1 != t2 for t1, t2 in zip(produced_tokens1, produced_tokens2))
