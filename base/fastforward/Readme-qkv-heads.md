# Multi-Head Attention in Fastforward
This document is an overview of how we handle various tricky bits with multi-head attention and its variants in fastforward. Topics include:
- QKV weight layout
- The different head splitting modes for tensor parallel execution
- How head splitting interacts with layouts and communication patterns

## Preliminaries
### Terminology
Our code is inconsistent in its terminology and variable-naming w.r.t. attention. This document tries to stick to the following:
- Head counts are expressed as:
  - `nheads_q`: number of q-heads
  - `nheads_kv`: number of kv-heads
- `headdim` is the per-head dimension
- `hidden` is the model's hidden dimension (i.e. the embedding dimension)
- MHA ("multi-head attention") can refer either to attention generically or specifically to the case where `nheads_q == nheads_kv`. "Vanilla attention" of "vanilla MHA" is another term to emphasize that we are talking about `nheads_q == nheads_kv`.
- GQA ("grouped-query attention") is attention with `nheads_kv < nheads_q` (strict inequality).
  - A "query group" is the set of queries that all attend to a single (shared) kv-head.
  - The variable `q_per_kv` is used for the size of a query group (`nheads_q // nheads_kv`).
- MQA ("multi-query attention") is a special case of GQA with `nheads_kv == 1`.

### Matrix layouts
Everything here is PyTorch-default. That means:
- Matrices are always row-major.
- Activations are sized as `(ntokens, hidden)`. (Where `ntokens` is the number of tokens in the current round.)
- Weights are stored as `(out_dim, in_dim)`.
  - Equivalently, the "channels" or "features" of a weight matrix are the _rows_.
  - The matmul for a linear operation is `x @ w.T` for inputs `x` and weights `w`.

## QKV weight layout
In vanilla PyTorch when the QKV weights are stored separately, life is easy:
```
# w_q has shape (nheads_q * headdim, hidden)
# w_{k, v} have shape (nheads_kv * headdim, hidden)
q = x @ w_q.T
k = x @ w_k.T
v = x @ w_v.T
```
In fastforward, we fuse the three weights into a single `w_qkv` for speed reasons. But, the fused weight is _not_ a simple concatenation of the three weights. Instead, the weights are _interleaved_ on a per-head basis. In the vanilla MHA case, this looks like:
```
   <hidden>
+------------+
|      |     |
|     q_0    |
|      |     |
+------------+
|      |     |
|     k_0    |
|      |     |
+------------+
|      |     |
|     v_0    |
|      |     |
+------------+
|      |     |
|     q_1    |
|      |     |
+------------+
|      |     |
|     k_1    |
|      |     |
...
```
Each block has `headdim` rows (the weights for a single attention head), so the full matrix has `3 * nheads * headdim` rows. Prefetching a little, this layout is good for tensor-parallel splitting, since simple chunking along the rows leaves each GPU with a continuous range of QKV heads.

In the GQA case, we generalize this by putting each set of q-heads before the corresponding kv-heads that they share. Eg, if `q_per_kv == 2`, then the layout looks like:
```
   <hidden>
+------------+
|      |     |
|     q_0    |
|      |     |
+------------+
|      |     |
|     q_1    |
|      |     |
+------------+
|      |     |
|     k_0    |
|      |     |
+------------+
|      |     |
|     v_0    |
|      |     |
+------------+
|      |     |
|     q_2    |
|      |     |
+------------+
...
```
Now the full matrix has `(nheads_q + 2 * nheads_kv) * headdim` rows. Given this layout, the following code (adapted from `cached_attention.py`) should make sense for multiplying by the QKV weight and then splitting out the q, k, and v results:
```
qkv = x @ w_qkv.T
# At this point qkv has shape (ntokens, (nheads_q + 2 * nheads_kv) * headdim),
# so we materialize the kv-groups by splitting up dim=1 into two dimensions.
qkv = qkv.view(-1, nheads_kv, (q_per_kv + 2), headdim)
q, k, v = qkv.split([q_per_kv, 1, 1], dim=2)
# q has shape (ntokens, nheads_kv, q_per_kv, headdim) and
#   can be flattened to (ntokens, nheads_q, headdim).
# k/v have shape (ntokens, nheads_kv, 1, headdim) and you can squeeze out dim=2.
```

## Understanding head-splitting modes
Setting aside attention heads for a minute, the QKV matmul is the first of two matmuls in the attention block. Using "standard" tensor parallelism, we should split the weight matrix on the _output_ dimension: the `3 * nheads * headdim` dimension. The post-attention projection is then split on the input dimension and followed with an all_reduce of the activations. If all we care about is vanilla MHA plus vanilla tensor parallelism, then we can do exactly that. Given the interleaved layout of the QKV weight matrix, if you chunk it along the rows (output dimension), then each GPU gets a `1/num_gpus` slice of the attention heads and otherwise operates entirely the same as the single-GPU case.

There are two extensions we have to make to this simple case:
1. Support for GQA and MQA. In particular, we might have too few KV-heads to simply chunk the QKV weight.
2. Mixed parallelism. Eg, if a tensor- and sequence-parallel model share an attention cache, then the tensor-parallel model needs to accomdate the needs of the sequence-parallel model in the stored KV cache.

To support these extensions, we use "head-splitting modes". (Aside: this name is obviously hilarious and self-referential.) There are three modes:
- `KV_HEADS`: Split the KV-heads across GPUs along with the corresponding Q-heads. This corresponds to the simple case described above.
- `Q_PER_HEADS`: _Replicate_ the KV-heads across GPUs, but split the Q-heads. Eg, if there are 2 KV-heads, 8 Q-heads, and 2 GPUs, then each GPU gets the 2 KV-heads and 4 Q-heads. A clearer name for this mode is "Q_HEADS_ONLY".
- `NO_SPLIT`: Don't split the heads at all. The QKV weight matrix is simply replicated to every GPU and each will compute the full KV-cache state and every query.

### Head-splitting and tensor-parallel communication
There are two cases for communcation:
1. `KV_HEADS` and `Q_PER_HEADS`: in either case, the resulting activations are sharded across the tensor parallel group. Ie, each GPU has `1/num_gpus` of the "query" activations. Consequently, the post-attention projection is split on the input dimension and followed with an all_reduce of the activations (like normal). Note that since the post-attention projetcion is split on the _input_, the bias (if present) is _not_ split. As a result, we add the bias to the linear projection on only the 0th rank. The other ranks pick up the bias as part of the all_reduce.
2. `NO_SPLIT`: since the activations are not sharded, the post-attention projection is also not split. Each GPU redundantly does the full projection and there is no all_reduce. This means that _every_ rank needs to add the bias to the projection. This is easy to get wrong: search for `NOTE(add-output-bias)` in the code for the places we handle this case.

### Head-splitting weight layouts
Coming back to where we started (weight layouts), the `NO_SPLIT` and `KV_HEADS` cases are simple: chunk the QKV weight matrix (and bias, if present) by the tensor parallel group size for `KV_HEADS`, no chunking for `NO_SPLIT`.

`Q_PER_HEADS` is worth more discussion. We need to split the Q-heads across the GPUs of the tensor parallel group. Consider the case of `nheads_q=4`, `nheads_kv=2`, and 2 GPUs. The naive split assigns `q_{0,1}` to GPU0 and `q_{2,3}` to GPU1. But this splitting leaves self-attention in a weird state:
- GPU0 has `q_{0,1}`, both of which attend to `kv_0`.
- GPU1 has `q_{2,3}`, both of which attend to `kv_1`.
Since the KV-heads are replicated, each GPU _has_ both of `kv_{0,1}`, but they each _use_ a different one for self-attention. You can make this work, but it means that the attention call needs special offset handling for tensor parallelism.

What we do instead is split the Q-heads in an interleaved fashion:
- GPU0 has `q_{0,2}`, attending to `kv_{0,1}`, respectively.
- GPU1 has `q_{1,3}`, attending to `kv_{0,1}`, respectively.
This ensures that self-attention is the same across GPUs, just with fewer Q-heads than the single-GPU case. Which is what you expect for "split Q-heads only."

But! The result of this interleaving is that the Q-heads are out-of-order for the post-attention projection. Simple chunking of the post-attention projection assumes that GPU0 produces `q_{0,1}` and GPU1 produces `q_{2,3}` (as would be the case for `KV_HEADS` splitting). Instead of simple chunking, we need to split the post-attention projection in the _same_ interleaved fashion as the Q-heads.

You can see where we do this in the code in two places:
- `fwd_llama.py:_shard_attn_out_weight`
- `fwd_starcoder2.py:_shard_attn_out_weight`

In both places, there is code that looks roughly like this:
```
# `w` has (hidden, nheads_q * headdim)
# First, split the nheads_q into their corresponding kv-groups.
w_kv_groups = w.chunk(nheads_kv, dim=1)
# Next, take my slice out of each group based on tp_rank
my_slices = [group.chunk(tp_size, dim=1)[tp_rank] for group in w_kv_groups]
# Finally, concatenate the slices back together.
my_w = torch.cat(my_slices, dim=1)
```
Taken together, the full story for `Q_PER_HEADS` is:
- When splitting the QKV weight matrix, the Q-heads are split in an interleaved fashion, taking a slice from each kv-group. The KV-heads are replicated.
- When splitting the post-attention projection, the input dimension is split in the same interleaved fashion.
- As a result, the final output of the attention block matches the single-GPU case (post-all_reduce).
