"""Tests for the diesel prompt formatter."""

import textwrap
from base import tokenizers
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.diesel_embedding_prompt_formatter import (
    <PERSON>1<PERSON><PERSON>ument<PERSON><PERSON>atter,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ormatter,
    _split_budget,
)
from base.prompt_format_retrieve.prompt_formatter import (
    DocumentRetrieverPromptInput,
    InstructRetrieverPromptInput,
)


def test_split_budget():
    """Test that the split budget function works as expected."""
    test_cases = [
        {
            "name": "common case",
            "token_budget": 1000,
            "prefix_fraction": 0.35,
            "instruction_fraction": 0.1,
            "selected_code_fraction": 0.2,
            "prefix_len": 2000,
            "suffix_len": 2000,
            "instruction_len": 100,
            "selected_code_len": 500,
            "expected_prefix_budget": 350,
            "expected_suffix_budget": 350,
            "expected_instruction_budget": 100,
            "expected_selected_code_budget": 200,
        },
        {
            "name": "short prefix",
            "token_budget": 1000,
            "prefix_fraction": 0.35,
            "instruction_fraction": 0.1,
            "selected_code_fraction": 0.2,
            "prefix_len": 50,
            "suffix_len": 2000,
            "instruction_len": 100,
            "selected_code_len": 500,
            "expected_prefix_budget": 50,
            "expected_suffix_budget": 650,
            "expected_instruction_budget": 100,
            "expected_selected_code_budget": 200,
        },
        {
            "name": "short suffix",
            "token_budget": 1000,
            "prefix_fraction": 0.35,
            "instruction_fraction": 0.1,
            "selected_code_fraction": 0.2,
            "prefix_len": 2000,
            "suffix_len": 50,
            "instruction_len": 100,
            "selected_code_len": 500,
            "expected_prefix_budget": 650,
            "expected_suffix_budget": 50,
            "expected_instruction_budget": 100,
            "expected_selected_code_budget": 200,
        },
        {
            "name": "short prefix and suffix, long selected code",
            "token_budget": 1000,
            "prefix_fraction": 0.35,
            "instruction_fraction": 0.1,
            "selected_code_fraction": 0.2,
            "prefix_len": 50,
            "suffix_len": 50,
            "instruction_len": 100,
            "selected_code_len": 2000,
            "expected_prefix_budget": 50,
            "expected_suffix_budget": 50,
            "expected_instruction_budget": 100,
            "expected_selected_code_budget": 800,
        },
    ]

    for test_case in test_cases:
        actual = _split_budget(
            test_case["token_budget"],
            test_case["prefix_fraction"],
            test_case["instruction_fraction"],
            test_case["selected_code_fraction"],
            test_case["prefix_len"],
            test_case["suffix_len"],
            test_case["instruction_len"],
            test_case["selected_code_len"],
        )
        expected = (
            test_case["expected_prefix_budget"],
            test_case["expected_suffix_budget"],
            test_case["expected_instruction_budget"],
            test_case["expected_selected_code_budget"],
        )
        assert actual == expected, test_case["name"]


def test_diesel_query_prompt_formatter(
    large_example_code_edit_input: InstructRetrieverPromptInput,
):
    """Test that the Diesel query prompt formatter works as expected.

    Tests include clipping behavior, as well as the ability to add a path and suffix.
    """
    test_cases = [
        {
            "max_content_len": 2048,
            "add_suffix": False,
            "expected_prompt": (
                """src/example.py<|fim-prefix|>
def dot_product(a,b):
  # This is a function that applies to two vectors.
<|instruction|>Implement dot product of two vectors.<|selected-code|>
  aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y
<|ret-endofquery|>"""
            ),
        },
        {
            "max_content_len": 2048,
            "add_suffix": True,
            "expected_prompt": (
                """src/example.py<|fim-prefix|>
def dot_product(a,b):
  # This is a function that applies to two vectors.
<|instruction|>Implement dot product of two vectors.<|selected-code|>
  aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y
<|fim-suffix|>
  return aggregated_output
<|ret-endofquery|>"""
            ),
        },
        {
            "max_content_len": 5,
            "add_suffix": False,
            "expected_prompt": "src/example.<|ret-endofquery|>",
        },
        {
            "max_content_len": 5,
            "add_suffix": True,
            "expected_prompt": "src/example.<|ret-endofquery|>",
        },
    ]

    tokenizer = tokenizers.create_tokenizer_by_name("fim")

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        )

        prompter = Diesel1QueryFormatter(
            config,
            tokenizer,
            add_suffix=test_case["add_suffix"],
        )

        prompt_tokens = prompter.format_prompt(large_example_code_edit_input).tokens()
        assert (
            prompter.tokenizer.detokenize(prompt_tokens) == test_case["expected_prompt"]
        ), (
            f"====== EXPECTED\n|{test_case['expected_prompt']}|\n------ GOT\n"
            f"|{prompter.tokenizer.detokenize(prompt_tokens)}|\n========\n"
        )


def test_diesel_document_prompt_formatter():
    """Test that the Diesel query prompt formatter works as expected.

    Tests include clipping behavior, as well as the ability to add a path and suffix.
    """
    document_input = DocumentRetrieverPromptInput(
        path="src/example.py",
        text=textwrap.dedent("""
            def dot_product(a,b):
              # This is a function that applies to two vectors.
            """),
    )

    test_cases = [
        {
            "max_content_len": 2048,
            "expected_prompt": (
                """<|startofsequence|>src/example.py<|fim-mid|>
def dot_product(a,b):
  # This is a function that applies to two vectors.
<|ret-endofkey|>"""
            ),
        },
        {
            "max_content_len": 5,
            "expected_prompt": "<|startofsequence|>src/example<|ret-endofkey|>",
        },
    ]

    tokenizer = tokenizers.create_tokenizer_by_name("fim")

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        )

        prompter = Diesel1DocumentFormatter(config, tokenizer)

        prompt_tokens = prompter.format_prompt(document_input).tokens()
        assert (
            prompter.tokenizer.detokenize(prompt_tokens) == test_case["expected_prompt"]
        ), (
            f"====== EXPECTED\n|{test_case['expected_prompt']}|\n------ GOT\n"
            f"|{prompter.tokenizer.detokenize(prompt_tokens)}|\n========\n"
        )
