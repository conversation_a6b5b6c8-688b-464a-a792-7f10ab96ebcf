"""Prompt formatter to format prompts for Diesel embedding keys and queries."""

import math
import typing
from typing import Op<PERSON>, <PERSON><PERSON>
from typing_extensions import override

from base.prompt_format.util import head_n, trailing_n
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (
    Ethanol6DocumentFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import (
    InstructRetrieverPromptInput,
    RetrieverPromptFormatter,
    PromptFormatterOutput,
)
from base.tokenizers import CodeGenSpecialTokens, Tokenizer


def _split_budget(
    token_budget: int,
    prefix_fraction: float,
    instruction_fraction: float,
    selected_code_fraction: float,
    prefix_len: int,
    suffix_len: int,
    instruction_len: int,
    selected_code_len: int,
) -> Tuple[int, int, int, int]:
    """Split the budget between the prefix, suffix, instruction, and selected code."""
    # Split the budget.
    assert prefix_fraction + instruction_fraction + selected_code_fraction <= 1
    budget_dict = {
        "prefix": math.floor(token_budget * prefix_fraction),
        "instruction": math.floor(token_budget * instruction_fraction),
        "selected_code": math.floor(token_budget * selected_code_fraction),
    }
    budget_dict["suffix"] = token_budget - sum(budget_dict.values())

    initial_budget_dict = {**budget_dict}

    # Validate the budgets.
    assert budget_dict["prefix"] >= 0
    assert budget_dict["suffix"] >= 0
    assert budget_dict["instruction"] >= 0
    assert budget_dict["selected_code"] >= 0
    assert sum(budget_dict.values()) <= token_budget

    # Step 1: Sum up surplus budget amongst parts of prompt
    possible_surpluses = {
        "prefix": budget_dict["prefix"] - prefix_len,
        "suffix": budget_dict["suffix"] - suffix_len,
        "instruction": budget_dict["instruction"] - instruction_len,
        "selected_code": budget_dict["selected_code"] - selected_code_len,
    }
    surpluses = {k: v for k, v in possible_surpluses.items() if v > 0}
    deficits = {k: v for k, v in possible_surpluses.items() if v < 0}
    # Step 2: Distribute surplus budget to all parts that don't have surplus budget
    total_surplus_left = sum(surpluses.values())
    for k, v in deficits.items():
        if total_surplus_left <= 0:
            break
        assert v < 0
        budget_to_add = min(total_surplus_left, -v)
        budget_dict[k] += budget_to_add
        total_surplus_left -= budget_to_add
    for k, v in surpluses.items():
        assert v > 0
        budget_dict[k] -= v

    # Validate the adjusted budgets.
    assert budget_dict["prefix"] >= 0
    assert budget_dict["suffix"] >= 0
    assert budget_dict["instruction"] >= 0
    assert budget_dict["selected_code"] >= 0
    assert sum(budget_dict.values()) <= token_budget, (
        initial_budget_dict,
        possible_surpluses,
        surpluses,
        deficits,
        budget_dict,
        token_budget,
    )

    return (
        budget_dict["prefix"],
        budget_dict["suffix"],
        budget_dict["instruction"],
        budget_dict["selected_code"],
    )


class Diesel1QueryFormatter(RetrieverPromptFormatter[InstructRetrieverPromptInput]):
    """The query formatter for Diesel1 embedding models."""

    input_type = InstructRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        add_suffix: bool,
        prefix_budget_fraction: float = 0.375,
        instruction_budget_fraction: float = 0.05,
        selected_code_budget_fraction: float = 0.2,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.add_suffix = add_suffix
        self.special_tokens = typing.cast(
            CodeGenSpecialTokens, tokenizer.special_tokens
        )
        self.prefix_budget_fraction = prefix_budget_fraction
        self.instruction_budget_fraction = instruction_budget_fraction
        self.selected_code_budget_fraction = selected_code_budget_fraction

    @override
    def format_prompt(
        self,
        prompt_input: InstructRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Diesel6 model."""
        if not self.apportionment_config:
            raise ValueError(
                "Apportionment configuration is required for Diesel6 query formatter."
            )
        max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_query
        )
        if max_tokens < 0:
            raise ValueError(
                "Inconsistent prompt configuration:"
                f"max_content_len={self.apportionment_config.max_content_len}"
            )

        # Header tokens
        header_tokens = []

        # add path
        if prompt_input.path:
            header_tokens += self.tokenizer.tokenize_safe(prompt_input.path)
            header_tokens.append(self.special_tokens.fim_prefix)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(prompt_input.prefix)

        # Instruction tokens
        assert prompt_input.instruction is not None
        instruction_tokens = [
            self.special_tokens.instruction
        ] + self.tokenizer.tokenize_safe(prompt_input.instruction)

        # Selected code tokens
        assert prompt_input.selected_code is not None
        selected_code_tokens = [
            self.special_tokens.selected_code
        ] + self.tokenizer.tokenize_safe(prompt_input.selected_code)

        # Optionally add suffix
        if self.add_suffix:
            suffix_tokens = [
                self.special_tokens.fim_suffix
            ] + self.tokenizer.tokenize_safe(prompt_input.suffix)
        else:
            suffix_tokens = []

        # Trim the prompt to fit into the max_tokens constraint.

        if max_tokens >= len(header_tokens) + len(prefix_tokens) + len(
            instruction_tokens
        ) + len(selected_code_tokens) + len(suffix_tokens):
            # No trimming is needed.
            prompt = (
                header_tokens
                + prefix_tokens
                + instruction_tokens
                + selected_code_tokens
                + suffix_tokens
            )

        elif max_tokens > len(header_tokens):
            # The header fits, but the prefix+suffix do not. Trim prefix/suffix.
            (
                prefix_budget,
                suffix_budget,
                instruction_budget,
                selected_code_budget,
            ) = _split_budget(
                token_budget=max_tokens - len(header_tokens),
                prefix_fraction=self.prefix_budget_fraction,
                instruction_fraction=self.instruction_budget_fraction,
                selected_code_fraction=self.selected_code_budget_fraction,
                prefix_len=len(prefix_tokens),
                suffix_len=len(suffix_tokens),
                instruction_len=len(instruction_tokens),
                selected_code_len=len(selected_code_tokens),
            )

            prompt = (
                header_tokens
                + trailing_n(prefix_tokens, prefix_budget)
                + head_n(instruction_tokens, instruction_budget)
                + head_n(selected_code_tokens, selected_code_budget)
                + head_n(suffix_tokens, suffix_budget)
            )

        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = head_n(header_tokens, max_tokens)

        prompt += [self.special_tokens.end_of_query]
        return PromptFormatterOutput([prompt])


class Diesel1DocumentFormatter(Ethanol6DocumentFormatter):
    """The document formatter for Diesel1 embedding models."""

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
    ):
        super().__init__(
            apportionment_config=apportionment_config,
            tokenizer=tokenizer,
            add_path=True,
        )
