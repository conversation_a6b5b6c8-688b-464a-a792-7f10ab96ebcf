load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

# This target exposes the prompt formatter interface and is marked public so that
# it can be used by other modules.
py_library(
    name = "prompt_formatter",
    srcs = [
        "prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//base/prompt_format:common",
        "//base/tokenizers",
    ],
)

py_library(
    name = "conftest",
    srcs = [
        "conftest.py",
    ],
    deps = [
        ":prompt_format_retrieve",
        "//base/prompt_format:common",
        "//base/prompt_format_completion",
        "//base/tokenizers",
        requirement("pytest"),
    ],
)

py_library(
    name = "ethanol_embedding_prompt_formatter",
    srcs = [
        "ethanol_embedding_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/languages:language_guesser",
        "//base/prompt_format:util",
        "//base/prompt_format_completion:token_apportionment",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "ethanol_embedding_prompt_formatter_test",
    srcs = ["ethanol_embedding_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":ethanol_embedding_prompt_formatter",
    ],
)

py_library(
    name = "chatanol_prompt_formatter",
    srcs = [
        "chatanol_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/prompt_format:common",
        "//base/prompt_format:util",
        "//base/prompt_format_completion:token_apportionment",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "chatanol_prompt_formatter_test",
    srcs = ["chatanol_prompt_formatter_test.py"],
    deps = [
        ":chatanol_prompt_formatter",
        ":conftest",
    ],
)

py_library(
    name = "diesel_embedding_prompt_formatter",
    srcs = [
        "diesel_embedding_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/languages:language_guesser",
        "//base/prompt_format:common",
        "//base/prompt_format:util",
        "//base/prompt_format_completion:token_apportionment",
        "//base/prompt_format_retrieve:ethanol_embedding_prompt_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "diesel_embedding_prompt_formatter_test",
    srcs = ["diesel_embedding_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":diesel_embedding_prompt_formatter",
    ],
)

py_library(
    name = "passthrough_prompt_formatter",
    srcs = [
        "passthrough_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/languages:language_guesser",
        "//base/prompt_format:util",
        "//base/prompt_format_completion:token_apportionment",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "passthrough_prompt_formatter_test",
    srcs = ["passthrough_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":passthrough_prompt_formatter",
    ],
)

py_library(
    name = "prompt_format_retrieve",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":chatanol_prompt_formatter",
        ":diesel_embedding_prompt_formatter",
        ":ethanol_embedding_prompt_formatter",
        ":passthrough_prompt_formatter",
        "//base/prompt_format_next_edit:location_prompt_formatter",
        "//base/prompt_format_next_edit:retrieval_prompt_formatter",
    ],
)
