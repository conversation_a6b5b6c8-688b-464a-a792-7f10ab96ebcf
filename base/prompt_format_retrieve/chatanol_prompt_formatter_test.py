"""Tests for the ethanol prompt formatter."""

import dataclasses
import textwrap
import pytest
from base import tokenizers
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    DocumentationMetadata,
    Exchange,
)

from base.prompt_format_retrieve.chatanol_prompt_formatter import (
    Cha<PERSON>l6DocumentFormatter,
    Chatanol6QueryFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    DocumentRetrieverPromptInput,
)


@pytest.mark.parametrize("tokenizer_name", ["fim", "starcoder"])
def test_chatanol_query_prompt_formatter(
    large_example_chat_input: ChatRetrieverPromptInput, tokenizer_name: str
):
    """Test the chatanol query prompt formatter."""

    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    test_cases = [
        {
            "max_content_len": 2048,
            "with_selected_code": True,
            "with_history": False,
            "include_answers": False,
            "expected_prompt": tokenizer.tokenize_safe(
                """### Instruction:\nImplement dot product of two vectors."""
            )
            + [tokenizer.special_tokens.end_of_query],
        },
        {
            "max_content_len": 2048,
            "with_selected_code": False,
            "with_history": True,
            "expected_prompt": tokenizer.tokenize_safe(
                "### Instruction:\nWhat is the main idea of attention?\n"
                "### Response:\n"
                "Attention is a mechanism that allows a model to focus on specific parts of the input data.\n"
                "### Instruction:\nHow do we implement attention?\n"
                "### Response:\n"
                "We can implement attention by using the dot product.\n"
                "### Instruction:\nImplement dot product of two vectors."
            )
            + [tokenizer.special_tokens.end_of_query],
        },
        {
            "max_content_len": 64,
            "with_selected_code": False,
            "with_history": True,
            "expected_prompt": tokenizer.tokenize_safe(
                "### Instruction:\nHow do we implement attention?\n"
                "### Response:\n"
                "We can implement attention by using the dot product.\n"
                "### Instruction:\nImplement dot product of two vectors."
            )
            + [tokenizer.special_tokens.end_of_query],
        },
        {
            "max_content_len": 32,
            "with_selected_code": False,
            "with_history": True,
            "expected_prompt": tokenizer.tokenize_safe(
                "### Instruction:\nImplement dot product of two vectors."
            )
            + [tokenizer.special_tokens.end_of_query],
        },
    ]

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        )

        prompter = Chatanol6QueryFormatter(
            config,
            tokenizer,
        )
        input = large_example_chat_input
        if not test_case["with_selected_code"]:
            input = dataclasses.replace(input, selected_code="")
        if not test_case["with_history"]:
            input = dataclasses.replace(input, chat_history=[])

        prompt_tokens = prompter.format_prompt(input).tokens()
        expected_tokens = test_case["expected_prompt"]
        prompt_text = prompter.tokenizer.detokenize(prompt_tokens)
        expected_text = prompter.tokenizer.detokenize(expected_tokens)

        assert prompt_tokens == expected_tokens, (
            f"====== EXPECTED\n|{expected_text}|\n------ GOT\n"
            f"|{prompt_text}|\n========\n"
        )


def test_chatanol_document_prompt_formatter():
    """Test that the Chatanol document prompt formatter works as expected."""
    document_input = DocumentRetrieverPromptInput(
        path="src/example.py",
        text=textwrap.dedent("""Welcome to Graphite!
    ## Step 1: Install the Graphite CLI"""),
        metadata=DocumentationMetadata(
            source_id="docset://graphite.dev",
            name="graphite.dev",
            page_id="1",
            headers=["#Header1", "##Header2"],
        ),
    )

    tokenizer = tokenizers.create_tokenizer_by_name("fim")
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    test_cases = [
        {
            "max_content_len": 2048,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("graphite.dev"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe(
                    """#Header1
##Header2
Welcome to Graphite!
    ## Step 1: Install the Graphite CLI"""
                ),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 6,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("graphite.dev"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 1,
            "expected_prompt": [
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 2,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.end_of_key,
            ],
        },
    ]

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=128,
        )

        prompter = Chatanol6DocumentFormatter(config, tokenizer, add_path=False)

        prompt_tokens = prompter.format_prompt(document_input).tokens()
        expected_tokens = test_case["expected_prompt"]

        expected_text = prompter.tokenizer.detokenize(expected_tokens)
        prompt_text = prompter.tokenizer.detokenize(prompt_tokens)
        assert prompt_tokens == expected_tokens, (
            f"====== EXPECTED\n|{expected_text}|\n------ GOT\n"
            f"|{prompt_text}|\n========\n"
        )


@pytest.mark.parametrize("tokenizer_name", ["fim", "starcoder"])
def test_chatanol_document_prompt_formatter_with_path(
    example_document_input: DocumentRetrieverPromptInput, tokenizer_name: str
):
    """Test that the Chatanol document prompt formatter includes path.

    Tests include clipping behavior and adding a path, prefix and suffix.
    """
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    test_cases = [
        {
            "max_content_len": 2048,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 2048,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 1,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 2,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 3,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.fim_middle,
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 4,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 3,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 7,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 8,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 9,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 15,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 16,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
    ]

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=128,
        )

        prompter = Chatanol6DocumentFormatter(
            config,
            tokenizer,
            add_path=test_case["add_path"],
        )
        prompt_tokens = prompter.format_prompt(example_document_input).tokens()
        expected_tokens = test_case["expected_prompt"]

        expected_text = prompter.tokenizer.detokenize(expected_tokens)
        prompt_text = prompter.tokenizer.detokenize(prompt_tokens)
        assert prompt_tokens == expected_tokens, (
            f"====== EXPECTED\n|{expected_text}|\n------ GOT\n"
            f"|{prompt_text}|\n========\n"
        )


@pytest.mark.parametrize("tokenizer_name", ["fim"])
def test_chatanol_query_prompt_formatter_tool_use(tokenizer_name: str):
    input = ChatRetrieverPromptInput(
        path="src/example.py",
        prefix="""
def dot_product(a,b):
  # This is a function that applies to two vectors.
""",
        suffix="""
  return aggregated_output
""",
        selected_code="""
  aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y
""",
        message=[
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="toolu_123",
                    content="""total 4
-rw-rw-r--  1 <USER> <GROUP>    2634 Dec 29 06:24 BUILD
-rw-rw-r--  1 <USER> <GROUP>    1087 Dec 29 06:24 CODEOWNERS
-rw-rw-r--  1 <USER> <GROUP>  164928 Jan 10 05:51 Cargo.lock
-rw-rw-r--  1 <USER> <GROUP>    4773 Jan 10 05:51 Cargo.toml
""",
                    is_error=False,
                    request_id="tool_result_request_id",
                ),
            )
        ],
        chat_history=[
            Exchange(
                "List the current directory",
                [
                    ChatResultNode(
                        id=0, type=ChatResultNodeType.RAW_RESPONSE, content="Sure!"
                    ),
                    ChatResultNode(
                        id=0,
                        type=ChatResultNodeType.TOOL_USE,
                        content="I can do that.",
                        tool_use=ChatResultToolUse(
                            name="shell",
                            input={
                                "cmd": "ls",
                                "args": ["-l"],
                            },
                            tool_use_id="toolu_123",
                        ),
                    ),
                ],
            ),
        ],
    )

    config = TokenApportionmentConfig(
        max_content_len=2048,
        input_fraction=0,
        prefix_fraction=0,
        max_path_tokens=0,
    )
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    prompter = Chatanol6QueryFormatter(
        config,
        tokenizer,
    )

    prompt_tokens = prompter.format_prompt(input).tokens()
    prompt_text = prompter.tokenizer.detokenize(prompt_tokens)

    # Note: request_id of tool result not desired in prompt
    expected_tokens = prompter.tokenizer.tokenize_safe(
        """### Instruction:
List the current directory
### Response:
Sure!
I can do that.
### Instruction:
total 4
-rw-rw-r--  1 <USER> <GROUP>    2634 Dec 29 06:24 BUILD
-rw-rw-r--  1 <USER> <GROUP>    1087 Dec 29 06:24 CODEOWNERS
-rw-rw-r--  1 <USER> <GROUP>  164928 Jan 10 05:51 Cargo.lock
-rw-rw-r--  1 <USER> <GROUP>    4773 Jan 10 05:51 Cargo.toml
"""
    ) + [tokenizer.special_tokens.end_of_query]
    expected_text = prompter.tokenizer.detokenize(expected_tokens)

    assert prompt_tokens == expected_tokens, (
        f"====== EXPECTED\n|{expected_text}|\n------ GOT\n"
        f"|{prompt_text}|\n========\n"
    )
