"""Tests for the ethanol prompt formatter."""

import pytest

from base import tokenizers
from base.prompt_format_retrieve.passthrough_prompt_formatter import (
    PassthroughPromptFormatter,
    PassthroughDocumentPromptFormatter,
    PassthroughRetrieverInput,
)
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig


@pytest.mark.parametrize("tokenizer_name", ["fim", "starcoder"])
@pytest.mark.parametrize(
    "input_text",
    [
        "hello, this is a test.",
        "def foo(): ...",
    ],
)
def test_passthrough_query_prompt_formatter(tokenizer_name: str, input_text: str):
    """Test that the `PassthroughPromptFormatter` works as expected.

    Tests include clipping behavior, as well as the ability to add a path and suffix.
    """
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    prompt_formatter = PassthroughPromptFormatter(tokenizer)

    actual_tokens = prompt_formatter.format_prompt(
        PassthroughRetrieverInput(text=input_text)
    ).tokens()
    expected_tokens = tokenizer.tokenize_unsafe(input_text) + [
        tokenizer.special_tokens.end_of_query
    ]

    assert actual_tokens == expected_tokens, (
        f"====== EXPECTED\n|{tokenizer.detokenize(expected_tokens)}|\n------ GOT\n"
        f"|{tokenizer.detokenize(actual_tokens)}|\n========\n"
    )


@pytest.mark.parametrize(
    "input_text, max_content_len, expected_tokens",
    [
        ("hello, this is a test.", 1000, [7656, 30, 458, 438, 312, 894, 32, 49167]),
        ("A very loooooooooooong text.", 3, [51, 5029, 49167]),
    ],
)
def test_passthrough_document_prompt_formatter(
    input_text: str, max_content_len: int, expected_tokens: list[int]
):
    """Test that the `PassthroughDocumentPromptFormatter` works as expected."""
    tokenizer = tokenizers.create_tokenizer_by_name("rogue")
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    prompt_formatter = PassthroughDocumentPromptFormatter(
        TokenApportionmentConfig(
            max_content_len=max_content_len,
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        ),
        tokenizer,
    )
    actual_tokens = prompt_formatter.format_prompt(
        PassthroughRetrieverInput(
            text=input_text,
        )
    ).tokens()

    assert actual_tokens == expected_tokens
    assert actual_tokens[-1] == tokenizer.special_tokens.end_of_key
