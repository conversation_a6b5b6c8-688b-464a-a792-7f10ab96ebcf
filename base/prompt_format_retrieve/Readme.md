# Prompt Format

This module contains the prompt formatting logic for the retrieval models.

Each LLM model is trained with a certain format how the various information (e.g. prefix, suffix, instructions) are formatted into the prompt.
During inference, we need to mirror the formatting rules.

Each model is configured with a prompt formatter name (e.g. in //services/deploy/configs).
The implementation of these formatters can be found in this directory.
Please see `__init__.py` for the list of supported formatters.

Users of the module should use the `//base/prompt_format` library and only create
instances via the get_retrieval_prompt_formatter_by_name factory function.

## Prompt Formatter and Tokenizer Limitations

See overview in [base/prompt_format_completion/Readme.md](base/prompt_format_completion/Readme.md).
