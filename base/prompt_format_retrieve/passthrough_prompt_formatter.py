"""Prompt formatter to format prompts for Ethanol embedding keys and queries."""

from dataclasses import dataclass
from typing import Op<PERSON>
from typing_extensions import override

from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.prompt_formatter import (
    PromptFormatterOutput,
    RetrieverPromptFormatter,
)
from base.tokenizers import RetrievalSpecialTokens, Tokenizer


@dataclass
class PassthroughRetrieverInput:
    text: str
    """The exact text to use as the prompt."""


class PassthroughPromptFormatter(RetrieverPromptFormatter[PassthroughRetrieverInput]):
    """A prompt formatter that passes through the prefix as tokens to the prompt.

    This can be useful if you want to e.g. send the tokens from a training or validation
    dataset to a model.

    ```python
    for tokens in validation_dataset:
        my_model(RetrieverPromptInput(prefix=tokenizer.detokenize(tokens)))
    ```

    Note that tokenizers aren't guaranteed to be reversible, so there may be slight
    variations introduced by detokenizing the input tokens.
    """

    input_type = PassthroughRetrieverInput

    def __init__(
        self,
        tokenizer: Tokenizer,
    ):
        self.tokenizer = tokenizer
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens

    @override
    def format_prompt(
        self, prompt_input: PassthroughRetrieverInput
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Ethanol6 model."""
        prompt = self.tokenizer.tokenize_unsafe(prompt_input.text)
        prompt += [self.special_tokens.end_of_query]

        return PromptFormatterOutput([prompt])


class PassthroughDocumentPromptFormatter(
    RetrieverPromptFormatter[PassthroughRetrieverInput]
):
    """A document prompt formatter that passes through the prefix as tokens to the prompt."""

    input_type = PassthroughRetrieverInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1000,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens

    @override
    def format_prompt(
        self, prompt_input: PassthroughRetrieverInput
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Ethanol6 model."""
        max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_query
        )

        prompt = self.tokenizer.tokenize_unsafe(prompt_input.text)[:max_tokens] + [
            self.special_tokens.end_of_key
        ]

        return PromptFormatterOutput([prompt])
