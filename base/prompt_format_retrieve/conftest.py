"""Contains shared fixtures for prompt formatting unit tests."""

from typing import Iterable

import pytest

from base.prompt_format.common import Exchange
from base.prompt_format_completion import PromptChunk
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    CompletionRetrieverPromptInput,
    DocumentRetrieverPromptInput,
    InstructRetrieverPromptInput,
)


@pytest.fixture()
def example_input():
    """Returns an example prompt input."""
    return CompletionRetrieverPromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
    )


@pytest.fixture()
def example_document_input():
    """Returns an example prompt input."""
    return DocumentRetrieverPromptInput(
        path="src/example.py",
        text="def aggregate(a,b):\n",
    )


@pytest.fixture()
def example_code_edit_input():
    """Returns an example prompt input."""
    return InstructRetrieverPromptInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        selected_code="# You can aggregate with a pooling function.",
        instruction="Implement a function that aggregates two numbers.",
    )


@pytest.fixture()
def large_example_chat_input():
    """Returns an example prompt input."""
    return ChatRetrieverPromptInput(
        path="src/example.py",
        prefix="""
def dot_product(a,b):
  # This is a function that applies to two vectors.
""",
        suffix="""
  return aggregated_output
""",
        selected_code="""
  aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y
""",
        message="Implement dot product of two vectors.",
        chat_history=[
            Exchange(
                "What is the main idea of attention?",
                "Attention is a mechanism that allows a model to focus on specific parts of the input data.",
            ),
            Exchange(
                "How do we implement attention?",
                "We can implement attention by using the dot product.",
            ),
        ],
    )


@pytest.fixture()
def large_example_code_edit_input():
    """Returns an example prompt input."""
    return InstructRetrieverPromptInput(
        path="src/example.py",
        prefix="""
def dot_product(a,b):
  # This is a function that applies to two vectors.
""",
        suffix="""
  return aggregated_output
""",
        selected_code="""
  aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y
""",
        instruction="Implement dot product of two vectors.",
    )


@pytest.fixture()
def example_chunks() -> Iterable[PromptChunk]:
    """Returns examples.

    The chunks are assumed to be ordered in high to low scoring.
    """
    return (  # tuple to make it harder to modify accidentally
        PromptChunk(
            text="# You can aggregate\n# with a maxing\n# function.\n",
            path="src/bar.py",
        ),
        PromptChunk(
            text="# You can aggregate\n# with a pooling function.\n",
            path="src/foo.py",
        ),
    )


@pytest.fixture()
def example_chunks_no_newline() -> Iterable[PromptChunk]:
    """Returns example chunk where the text doesn't end on a newline.

    That can e.g. happen at the end of a file.

    The chunks are assumed to be ordered in high to low scoring.
    """
    return [PromptChunk(text="def aggregate(a,b):\n    while", path="src/foo.py")]
