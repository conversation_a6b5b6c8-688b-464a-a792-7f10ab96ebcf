"""Classes that are used to build the prompts for the retriever model."""

from dataclasses import dataclass, field
from typing import Protocol, Sequence, TypeVar

from base.tokenizers import Tokenizer
from base.prompt_format.common import (
    DocumentationMetadata,
    Exchange,
    PromptFormatterOutput,
    RequestMessage,
)

InputT = TypeVar("InputT")


class RetrieverPromptFormatter(Protocol[InputT]):
    """The Retrieval Prompt Formatter protocol.

    Because retrievers are used horizontally across task models, e.g. completion, chat,
    etc., they have a generic input type.
    """

    input_type: type[InputT]
    """The type of the input. Used for dynamic type checking."""

    tokenizer: Tokenizer
    """The tokenizer used to tokenize the prompt."""

    def format_prompt(
        self,
        prompt_input: InputT,
    ) -> PromptFormatterOutput:
        """Build tokenized prompt.

        Args:
            prompt_input: RetrieverPromptInput object describing input

        Returns:
            prompt as list of tokens
        """
        raise NotImplementedError()


@dataclass
class CompletionRetrieverPromptInput:
    """The set of inputs used for constructing the retrieval model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.
    """

    prefix: str
    """
    For queries, the prefix is the content before the (cursor|selected_code).
    For embeddings, the prefix is the complete document.
    """

    suffix: str
    """The content after the (cursor|selected_code)."""

    path: str
    """The file path."""


@dataclass
class InstructRetrieverPromptInput:
    """The set of inputs used for constructing the retrieval model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.
    """

    prefix: str
    """
    For queries, the prefix is the content before the (cursor|selected_code).
    For embeddings, the prefix is the complete document.
    """

    suffix: str
    """The content after the (cursor|selected_code)."""

    path: str
    """The file path."""

    instruction: str
    """The optional instruction for the prompt."""

    selected_code: str
    """The optional selected code for the prompt."""

    chat_history: Sequence[Exchange] = field(default_factory=list)
    """The conversation history as a list of request_message/response_text pairs."""


@dataclass
class ChatRetrieverPromptInput:
    """The set of inputs used for constructing the retrieval model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.
    """

    prefix: str
    """
    For queries, the prefix is the content before the (cursor|selected_code).
    For embeddings, the prefix is the complete document.
    """

    suffix: str
    """The content after the (cursor|selected_code)."""

    path: str
    """The file path."""

    message: RequestMessage
    """The message for the prompt."""

    selected_code: str
    """The optional selected code for the prompt."""

    chat_history: Sequence[Exchange] = field(default_factory=list)
    """The conversation history as a list of request_message/response_text pairs."""

    prefix_begin: int = 0
    """The offset in UTF-8 characters where prefix begins, relative to the beginning of file."""

    suffix_end: int = 0
    """The offset in UTF-8 characters where suffix ends, relative to the beginning of file."""

    blob_name: str = ""
    """The blob name of the file containing this chunk."""


@dataclass
class DocumentRetrieverPromptInput:
    """The set of inputs used for constructing the retrieval model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.
    """

    text: str
    """The text of the document or chunk."""

    path: str
    """The file path."""

    metadata: DocumentationMetadata | None = None
    """The metadata of the document."""
