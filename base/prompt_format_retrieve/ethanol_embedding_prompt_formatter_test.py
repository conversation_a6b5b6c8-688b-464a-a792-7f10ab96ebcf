"""Tests for the ethanol prompt formatter."""

import dataclasses
import pytest
from base import tokenizers
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (
    Ethanol6D<PERSON><PERSON>nt<PERSON><PERSON>atter,
    <PERSON>ol<PERSON><PERSON>uery<PERSON>ormatter,
    Ethanol6QuerySimpleChatFormatter,
    Ethanol6QuerySimpleInstructFormatter,
    add_selected_code_and_instructions_to_prefix_and_suffix,
)
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    CompletionRetrieverPromptInput,
    DocumentRetrieverPromptInput,
    InstructRetrieverPromptInput,
)


@pytest.mark.parametrize("tokenizer_name", ["fim", "starcoder"])
def test_ethanol_query_prompt_formatter(
    example_input: CompletionRetrieverPromptInput, tokenizer_name: str
):
    """Test that the Ethanol query prompt formatter works as expected.

    Tests include clipping behavior, as well as the ability to add a path and suffix.
    """
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    test_cases = [
        {
            "max_content_len": 2048,
            "add_path": False,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": True,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.fim_suffix,
                *tokenizer.tokenize_safe("\nreturn aggregated_output\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 1,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 2,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 3,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 6,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 7,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 8,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 9,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("):\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 14,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe(" aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 15,
            "add_path": True,
            "add_suffix": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 7,
            "add_path": True,
            "add_suffix": True,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 8,
            "add_path": True,
            "add_suffix": True,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 9,
            "add_path": True,
            "add_suffix": True,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("\n"),
                tokenizer.special_tokens.fim_suffix,
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 10,
            "add_path": True,
            "add_suffix": True,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("):\n"),
                tokenizer.special_tokens.fim_suffix,
                tokenizer.special_tokens.end_of_query,
            ],
        },
        {
            "max_content_len": 23,
            "add_path": True,
            "add_suffix": True,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.fim_suffix,
                *tokenizer.tokenize_safe("\nreturn aggregated_output\n"),
                tokenizer.special_tokens.end_of_query,
            ],
        },
    ]

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        )

        prompter = Ethanol6QueryFormatter(
            config,
            tokenizer,
            add_path=test_case["add_path"],
            add_suffix=test_case["add_suffix"],
        )

        prompt_tokens = prompter.format_prompt(example_input).tokens()
        expected_tokens = test_case["expected_prompt"]
        expected_text = prompter.tokenizer.detokenize(expected_tokens)
        prompt_text = prompter.tokenizer.detokenize(prompt_tokens)
        assert prompt_tokens == expected_tokens, (
            f"====== EXPECTED\n|{expected_text}|\n------ GOT\n"
            f"|{prompt_text}|\n========\n"
        )


def test_add_selected_code_and_instructions(
    example_code_edit_input: InstructRetrieverPromptInput,
):
    """Test that the add_selected_code_and_instructions option on Ethanol query prompt formatter works as expected.

    Test that we can correctly add instructions and selected code to the prompt when
    the add_selected_code_and_instructions option is set to True. Check for edge cases
    where the selected code or instruction is an empty string.
    """

    def _make_empty_selected_code(
        mi: InstructRetrieverPromptInput,
    ) -> InstructRetrieverPromptInput:
        return dataclasses.replace(mi, selected_code="")

    def _make_empty_instruction(
        mi: InstructRetrieverPromptInput,
    ) -> InstructRetrieverPromptInput:
        return dataclasses.replace(mi, instruction="")

    def _make_empty_prefix_suffix(
        mi: InstructRetrieverPromptInput,
    ) -> InstructRetrieverPromptInput:
        return dataclasses.replace(mi, prefix="", suffix="")

    instruction = f"# {example_code_edit_input.instruction}\n"
    empty_instruction = "# \n"
    selected_code = f"{example_code_edit_input.selected_code}"

    test_cases = [
        # Confirm expected behavior when non-empty selected code and instruction.
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": True,
            "add_selected_code_and_instructions": True,
            "prepropcessing": lambda mi: mi,  # no-op
            "expected_prompt": (
                "src/example.py<|fim-prefix|>def aggregate(a,b):\n\n"
                f"{instruction}"
                f"<|fim-suffix|>{selected_code}"
                "\nreturn aggregated_output\n<|ret-endofquery|>"
            ),
        },
        # Confirm expected behavior when empty selected code and non-empty instruction.
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": True,
            "add_selected_code_and_instructions": True,
            "prepropcessing": _make_empty_selected_code,
            "expected_prompt": (
                "src/example.py<|fim-prefix|>def aggregate(a,b):\n\n"
                f"{instruction}"
                "<|fim-suffix|>\nreturn aggregated_output\n<|ret-endofquery|>"
            ),
        },
        # Confirm expected behavior when non-empty selected code and empty instruction.
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": True,
            "add_selected_code_and_instructions": True,
            "prepropcessing": _make_empty_instruction,
            "expected_prompt": (
                "src/example.py<|fim-prefix|>def aggregate(a,b):\n\n"
                f"{empty_instruction}"
                f"<|fim-suffix|>{selected_code}"
                "\nreturn aggregated_output\n<|ret-endofquery|>"
            ),
        },
        # Confirm expected behavior when empty selected code and empty instruction.
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": True,
            "add_selected_code_and_instructions": True,
            "prepropcessing": lambda x: _make_empty_instruction(
                _make_empty_selected_code(x)
            ),
            "expected_prompt": (
                "src/example.py<|fim-prefix|>def aggregate(a,b):\n\n"
                f"{empty_instruction}"
                "<|fim-suffix|>\nreturn aggregated_output\n<|ret-endofquery|>"
            ),
        },
        # Confirm expected behavior when empty selected code and empty instruction and prefix/suffix
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": True,
            "add_selected_code_and_instructions": True,
            "prepropcessing": lambda x: _make_empty_instruction(
                _make_empty_selected_code(_make_empty_prefix_suffix(x))
            ),
            "expected_prompt": (
                "src/example.py<|fim-prefix|>\n"
                f"{empty_instruction}"
                "<|fim-suffix|><|ret-endofquery|>"
            ),
        },
        # Confirm expected behavior when empty prefix/suffix
        {
            "max_content_len": 2048,
            "add_path": True,
            "add_suffix": True,
            "add_selected_code_and_instructions": True,
            "prepropcessing": lambda x: _make_empty_prefix_suffix(x),
            "expected_prompt": (
                "src/example.py<|fim-prefix|>\n"
                f"{instruction}"
                f"<|fim-suffix|>{selected_code}<|ret-endofquery|>"
            ),
        },
    ]

    tokenizer = tokenizers.create_tokenizer_by_name("fim")

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        )

        prompter = Ethanol6QuerySimpleInstructFormatter(
            config,
            tokenizer,
            add_path=test_case["add_path"],
            add_suffix=test_case["add_suffix"],
        )

        processed_input = test_case["prepropcessing"](example_code_edit_input)

        prompt_tokens = prompter.format_prompt(processed_input).tokens()
        assert (
            prompter.tokenizer.detokenize(prompt_tokens) == test_case["expected_prompt"]
        ), (
            f"====== EXPECTED\n|{test_case['expected_prompt']}|\n------ GOT\n"
            f"|{prompter.tokenizer.detokenize(prompt_tokens)}|\n========\n"
        )


def test_add_selected_code_and_instructions_to_prefix_and_suffix_function(
    large_example_code_edit_input: InstructRetrieverPromptInput,
):
    """Test that the add_selected_code_and_instructions utility function works as expected."""

    new_input = add_selected_code_and_instructions_to_prefix_and_suffix(
        large_example_code_edit_input
    )
    expected_output = CompletionRetrieverPromptInput(
        path="src/example.py",
        prefix="""
def dot_product(a,b):
  # This is a function that applies to two vectors.

  # Implement dot product of two vectors.

  aggregated_output = 0
""",
        suffix="""  for x,y in zip(a,b):
    aggregated_output += x*y

  return aggregated_output
""",
    )

    assert new_input == expected_output

    # Try adding leading new line to selected code:
    large_example_code_edit_input_edgecase1 = dataclasses.replace(
        large_example_code_edit_input,
        selected_code=f"\n{large_example_code_edit_input.selected_code}",
    )
    new_input_edgecase1 = add_selected_code_and_instructions_to_prefix_and_suffix(
        large_example_code_edit_input_edgecase1
    )
    expected_output_edgecase1 = CompletionRetrieverPromptInput(
        path="src/example.py",
        prefix="""
def dot_product(a,b):
  # This is a function that applies to two vectors.

  # Implement dot product of two vectors.


""",
        suffix="""  aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y

  return aggregated_output
""",
    )

    assert new_input_edgecase1 == expected_output_edgecase1

    # Try empty selected code.
    large_example_code_edit_input_edgecase2 = dataclasses.replace(
        large_example_code_edit_input, selected_code=""
    )
    new_input_edgecase2 = add_selected_code_and_instructions_to_prefix_and_suffix(
        large_example_code_edit_input_edgecase2
    )
    expected_output_edgecase2 = CompletionRetrieverPromptInput(
        path="src/example.py",
        prefix="""
def dot_product(a,b):
  # This is a function that applies to two vectors.

  # Implement dot product of two vectors.
""",
        suffix="\n  return aggregated_output\n",
    )

    assert new_input_edgecase2 == expected_output_edgecase2


@pytest.mark.parametrize("tokenizer_name", ["fim", "starcoder"])
def test_ethanol_document_prompt_formatter(
    example_document_input: DocumentRetrieverPromptInput, tokenizer_name: str
):
    """Test that the Ethanol document prompt formatter works as expected.

    Tests include clipping behavior and adding a path, prefix and suffix.
    """
    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    test_cases = [
        {
            "max_content_len": 2048,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 2048,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 1,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 2,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 3,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.fim_middle,
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 4,
            "add_path": False,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 3,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 7,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 8,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 9,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 15,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
        {
            "max_content_len": 16,
            "add_path": True,
            "expected_prompt": [
                tokenizer.special_tokens.start_of_key,
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_middle,
                *tokenizer.tokenize_safe("def aggregate(a,b):\n"),
                tokenizer.special_tokens.end_of_key,
            ],
        },
    ]

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        )

        prompter = Ethanol6DocumentFormatter(
            config,
            tokenizer,
            add_path=test_case["add_path"],
        )
        prompt_tokens = prompter.format_prompt(example_document_input).tokens()
        expected_tokens = test_case["expected_prompt"]

        expected_text = prompter.tokenizer.detokenize(expected_tokens)
        prompt_text = prompter.tokenizer.detokenize(prompt_tokens)
        assert prompt_tokens == expected_tokens, (
            f"====== EXPECTED\n|{expected_text}|\n------ GOT\n"
            f"|{prompt_text}|\n========\n"
        )


@pytest.mark.parametrize("tokenizer_name", ["fim", "starcoder"])
def test_simple_chat_function(
    large_example_chat_input: ChatRetrieverPromptInput, tokenizer_name: str
):
    """Test the simple chat variant of the query prompt formatter."""

    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)
    assert isinstance(tokenizer.special_tokens, tokenizers.RetrievalSpecialTokens)

    test_cases = [
        {
            "max_content_len": 2048,
            "add_path": False,
            "with_selected_code": False,
            "add_selected_code": True,
            "expected_prompt": tokenizer.tokenize_safe(
                "Implement dot product of two vectors."
            ),
        },
        {
            "max_content_len": 2048,
            "add_path": True,
            "with_selected_code": False,
            "add_selected_code": True,
            "expected_prompt": tokenizer.tokenize_safe(
                "Implement dot product of two vectors."
            ),
        },
        {
            "max_content_len": 2048,
            "add_path": False,
            "with_selected_code": True,
            "add_selected_code": True,
            "expected_prompt": tokenizer.tokenize_safe("""# Implement dot product of two vectors.
aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y"""),
        },
        {
            "max_content_len": 2048,
            "add_path": True,
            "with_selected_code": True,
            "add_selected_code": True,
            "expected_prompt": [
                *tokenizer.tokenize_safe("src/example.py"),
                tokenizer.special_tokens.fim_prefix,
                *tokenizer.tokenize_safe("""# Implement dot product of two vectors.
aggregated_output = 0
  for x,y in zip(a,b):
    aggregated_output += x*y"""),
            ],
        },
        {
            "max_content_len": 2048,
            "add_path": False,
            "with_selected_code": True,
            "add_selected_code": False,
            "expected_prompt": tokenizer.tokenize_safe(
                """Implement dot product of two vectors."""
            ),
        },
        {
            "max_content_len": 2048,
            "add_path": True,
            "with_selected_code": True,
            "add_selected_code": False,
            "expected_prompt": [
                *tokenizer.tokenize_safe("""Implement dot product of two vectors."""),
            ],
        },
    ]

    for test_case in test_cases:
        config = TokenApportionmentConfig(
            max_content_len=test_case["max_content_len"],
            input_fraction=0,
            prefix_fraction=0,
            max_path_tokens=0,
        )

        prompter = Ethanol6QuerySimpleChatFormatter(
            config,
            tokenizer,
            add_selected_code=test_case["add_selected_code"],
            add_path=test_case["add_path"],
        )
        input = large_example_chat_input
        if not test_case["with_selected_code"]:
            input = dataclasses.replace(input, selected_code="")

        prompt_tokens = prompter.format_prompt(input).tokens()
        expected_tokens = test_case["expected_prompt"]
        prompt_text = prompter.tokenizer.detokenize(prompt_tokens)
        expected_text = prompter.tokenizer.detokenize(expected_tokens)

        assert prompt_tokens == expected_tokens, (
            f"====== EXPECTED\n|{expected_text}|\n------ GOT\n"
            f"|{prompt_text}|\n========\n"
        )
