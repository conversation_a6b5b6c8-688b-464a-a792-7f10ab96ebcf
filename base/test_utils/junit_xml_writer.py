"""
A module to write junit xml files

Example Usage:
helper = JUnitXMLWriter()
helper.add_testcase(name="test1", classname="ClassA", time=1.23, status="passed")
helper.add_testcase(name="test2", classname="ClassA", time=0.56, status="failed", message="Asser<PERSON> failed")
helper.add_testcase(name="test3", classname="ClassB", time=0.78, status="skipped", message="Test skipped")
helper.add_testcase(name="test4", classname="ClassC", time=0.45, status="error", message="Exception occurred")

xml_output = helper.to_string()
print(xml_output)

# Save to file
helper.to_file("test_results.xml")
"""

import xml.etree.ElementTree as ET  # nosec We do not parse xml
import pathlib


class JUnitXMLWriter:
    def __init__(self) -> None:
        """
        Initializes the JUnitXMLHelper instance.
        Sets up the root testsuites element and a single testsuite child element.
        Initializes counters for tests, failures, errors, and skipped tests.
        """
        self.testsuites = ET.Element("testsuites")
        self.testsuite = ET.SubElement(
            self.testsuites,
            "testsuite",
            attrib={
                "name": "SampleTestSuite",
                "tests": "0",
                "failures": "0",
                "errors": "0",
                "skipped": "0",
            },
        )
        self.test_count = 0
        self.failure_count = 0
        self.error_count = 0
        self.skipped_count = 0

    def add_testcase(
        self,
        name: str,
        classname: str,
        time: float,
        status: str = "passed",
        message: str | None = None,
        stdout: str | None = None,
        stderr: str | None = None,
    ) -> None:
        """
        Adds a test case to the testsuite.

        :param name: Name of the test case.
        :param classname: Class name of the test case.
        :param time: Time taken for the test case.
        :param status: Status of the test case (passed, failed, error, skipped). Default is "passed".
        :param message: Optional message for the status (used for failures, errors, skipped).
        """
        testcase = ET.SubElement(
            self.testsuite,
            "testcase",
            attrib={"name": name, "classname": classname, "time": str(time)},
        )
        self.test_count += 1

        if status == "failed":
            ET.SubElement(
                testcase,
                "failure",
                attrib={"message": message if message else "Test failed"},
            )
            self.failure_count += 1
        elif status == "error":
            ET.SubElement(
                testcase,
                "error",
                attrib={"message": message if message else "Test error"},
            )
            self.error_count += 1
        elif status == "skipped":
            ET.SubElement(
                testcase,
                "skipped",
                attrib={"message": message if message else "Test skipped"},
            )
            self.skipped_count += 1
        elif status == "passed":
            pass
        else:
            raise ValueError(f"Invalid status: {status}")

        if stdout:
            ET.SubElement(testcase, "system-out").text = stdout
        if stderr:
            ET.SubElement(testcase, "system-err").text = stderr

    def update_testsuite_counts(self) -> None:
        """
        Updates the testsuite element with the current counts of tests, failures, errors, and skipped tests.
        """
        self.testsuite.set("tests", str(self.test_count))
        self.testsuite.set("failures", str(self.failure_count))
        self.testsuite.set("errors", str(self.error_count))
        self.testsuite.set("skipped", str(self.skipped_count))

    def to_string(self) -> str:
        """
        Converts the testsuites element to a string.

        :return: String representation of the XML.
        """
        self.update_testsuite_counts()
        return ET.tostring(self.testsuites, encoding="unicode")

    def to_file(self, file_path: pathlib.Path) -> None:
        """
        Writes the testsuites element to a file.

        :param file_path: Path to the file where the XML should be written.
        """
        self.update_testsuite_counts()
        tree = ET.ElementTree(self.testsuites)
        tree.write(str(file_path), encoding="unicode", xml_declaration=True)
