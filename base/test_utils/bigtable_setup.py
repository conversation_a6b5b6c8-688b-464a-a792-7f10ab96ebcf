"""Bigtable setup for tests."""

import dataclasses
from typing import Optional

from google.cloud import bigtable  # type: ignore


@dataclasses.dataclass
class BigtableTable:  # TODO: rename to BigtableDetails and add "column family here"
    """Bigtable table."""

    table_name: str
    project: Optional[str] = (
        "google-cloud-bigtable-emulator"  # using default value from bigtable library because auth-query does not support an empty value
    )
    instance: str = "test-instance"


def create_table(table_id: BigtableTable, family_columns: list[str]):
    client = bigtable.Client(project=table_id.project, admin=True)
    instance = client.instance(table_id.instance)
    table = instance.table(table_id.table_name)
    table.create()
    try:
        for column in family_columns:
            table.column_family(column).create()

        yield table_id
    finally:
        table.delete()


# This should only be called if the emulator itself will be shut down after the test is run
def create_table_no_del(table_id: BigtableTable, family_columns: list[str]):
    client = bigtable.Client(project=table_id.project, admin=True)
    instance = client.instance(table_id.instance)
    table = instance.table(table_id.table_name)
    table.create()
    for column in family_columns:
        table.column_family(column).create()
    yield table_id
