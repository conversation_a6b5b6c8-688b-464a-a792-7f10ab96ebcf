package test_utils

import (
	"context"
	"testing"

	"google.golang.org/protobuf/proto"
)

type RequestResponse struct {
	Request  proto.Message
	Response proto.Message
}

// Make a series of requests that should be successful and check that their responses match what we
// expect.
func CheckResponses[ReqT proto.Message, RespT proto.Message](
	t *testing.T,
	ctx context.Context,
	requestResponses []RequestResponse,
	requestFunc func(context.Context, ReqT) (RespT, error),
) {
	for _, reqResp := range requestResponses {
		request := reqResp.Request.(ReqT)
		expectedResponse := reqResp.Response

		response, err := requestFunc(ctx, request)
		if err != nil {
			t.<PERSON>rf("Unexpected error for request %v: %v", request, err)
		}
		if !proto.Equal(response, expectedResponse) {
			t.<PERSON>("Unexpected response proto. Expected %v, got %v", expectedResponse, response)
		}
	}
}
