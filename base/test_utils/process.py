"""Utilities to run processes."""

import logging
import os
import re
import signal
import subprocess
import sys
import threading
import time
from typing import IO, Match, Optional


def echo(infile: IO[str], outfile: IO[str]):
    """Echo from infile to outfile."""
    while True:
        buf = infile.readline()
        if not buf:
            break
        outfile.write(buf)
        outfile.flush()


def wait_for_line(
    ifile: IO[str],
    regexp: str,
    ofile: Optional[IO[str]] = None,
    timeout_secs: float = 3600.0,
) -> Match[str]:
    """Wait for a line to appear in a file.

    Throws an exception if the line does not appear.
    Activity reading from ifile and writing to ofile
    may continue even after an exception is thrown.
    """

    if ofile is None:
        ofile = sys.stdout

    end = time.monotonic() + timeout_secs

    # The thread will place its return value in ret
    ret: list[Match[str]] = []

    # The thread function
    def wait_for_line_inner():
        while time.monotonic() < end:
            line = ifile.readline()
            if not line:
                break
            ofile.write(line)
            # Some downstream reader may be waiting for this
            ofile.flush()

            m = re.search(regexp, line)
            if m:
                ret.append(m)
                return

    t = threading.Thread(target=wait_for_line_inner)
    t.daemon = True
    t.start()
    t.join(timeout=timeout_secs)
    if t.is_alive():
        raise TimeoutError(f"Timeout waiting for {regexp}")
    if not ret:
        raise RuntimeError(f"No match for {regexp}")
    return ret[0]


class ServerManager:
    """Start and stop a server and echo its stdout.

    Warning: will hang on exit if the server or a child process of the
    server does not exit on SIGTERM.
    """

    def __init__(self, args: list[str], redirect_stderr: bool = False, env=None):
        self.popen = subprocess.Popen(
            args,
            stdin=subprocess.DEVNULL,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT if redirect_stderr else None,
            encoding="utf-8",
            text=True,
            close_fds=True,
            env=env,
        )
        self.thread = None
        if self.popen.stdout is None:
            self.popen.kill()
            raise RuntimeError(f"Failed to start {args}")
        self.stdout = self.popen.stdout
        logging.info("Started %s got pid %s", args, self.popen.pid)

    def __enter__(self):
        return self

    def detach_stdout(self, stdout=None):
        assert self.thread is None

        if stdout is None:
            stdout = sys.stdout

        self.thread = threading.Thread(target=echo, args=(self.stdout, stdout))
        self.thread.daemon = True
        self.thread.start()

    def __exit__(self, exception_type, exception_value, traceback):
        if not self.popen.poll():
            logging.info("Sending SIGTERM to pid %s", self.popen.pid)
            try:
                os.kill(self.popen.pid, signal.SIGTERM)
            except ProcessLookupError:
                logging.info("Process %s already dead", self.popen.pid)
            self.popen.wait()
            logging.info("Finished waiting for pid %s to exit", self.popen.pid)
        if self.thread is not None:
            self.thread.join()
        self.stdout.close()
        logging.info("Finished ServerManager cleanup for pid %s", self.popen.pid)
