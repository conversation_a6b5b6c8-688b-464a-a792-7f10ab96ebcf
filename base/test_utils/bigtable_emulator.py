"""A helper for creating fixtures for the BigTable emulator."""

from typing import Generator

from python.runfiles import Runfiles

import base.test_utils.process as process


def start_emulator(runfiles: Runfiles | None = None) -> Generator[str, None, None]:
    """Helper for implementing pytest fixture to start Bigtable emulator.

    Suggested usage:
        @pytest.fixture(scope="module")
        def bigtable_emulator():
            yield from start_emulator()
    """
    if runfiles is None:
        runfiles = Runfiles.Create()

    assert runfiles is not None
    emulator_path = runfiles.Rlocation("bigtable_emulator_amd64/emulator")
    if emulator_path is None:
        raise ValueError("Could not locate bigtable emulator in runfiles")

    with process.ServerManager(
        [emulator_path, "-port=0"],
    ) as p:
        m = process.wait_for_line(
            p.stdout,
            r"Cloud Bigtable emulator running on 127.0.0.1:(\d+)",
            timeout_secs=30,
        )
        p.detach_stdout()
        port = int(m.group(1))
        yield "127.0.0.1:%d" % port
