package bqemulator

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand/v2"
	"os"
	"os/exec"
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/option"

	"github.com/bazelbuild/rules_go/go/runfiles"
)

// This struct can be used to start and manage a BigQuery emulator for testing. For simplicity, an
// emulator is associated with a single dataset (this could be easily updated in the future if
// we have a use-case for testing multiple datasets at once).
type BigQueryEmulator struct {
	// BigQuery client that callers should use for database operations.
	Client *bigquery.Client

	// Dataset configured for this emulator.
	Dataset *bigquery.Dataset

	// Command running the emulator in the background.
	cmd *exec.Cmd
}

// Table struct, for deserializing a JSON schema. This should be kept in sync with
// services/request_insight/bigquery_exporter/schema.jsonnet.
type table struct {
	Name        string `json:"name"`
	Description string `json:"description"`

	// Only one of Schema or ViewQuery will be present. When Schema is present this is a table.
	Schema []row `json:"schema"`

	// Only one of Schema or ViewQuery will be present. When ViewQuery is non-empty this is a view.
	// This query CANNOT end in a semicolon, or the emulator will hang. GH issue:
	// https://github.com/goccy/bigquery-emulator/issues/300
	ViewQuery string `json:"viewQuery"`
}

// Row struct, for deserializing a JSON schema. This should be kept in sync with
// services/request_insight/bigquery_exporter/schema.jsonnet.
type row struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Mode        string `json:"mode"`
	Description string `json:"description"`
}

// Create a new BigQuery emulator. If no error is returned, the returned BigQueryEmulator.Client is
// ready to use.
func New(
	ctx context.Context, projectId string, datasetName string,
) (*BigQueryEmulator, error) {
	// In an ideal world, we would start the emulator with port=0 and read the port we got assigned
	// from stdout, but this doesn't work because the emulator has a bug  will just print
	// "listening at 0.0.0.0:0" in that case. Until that's fixed, just try random ports until one
	// works or we time out. Github issue: https://github.com/goccy/bigquery-emulator/issues/333
	var bqCommand *exec.Cmd
	var port int
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		// Pick a random port to run the emulator on. 49152-65535 are ephemeral ports.
		port = rand.IntN(65535-49152) + 49152

		realPath, err := runfiles.Rlocation("bigquery_emulator_amd64/emulator")
		if err != nil {
			return nil, err
		}

		bqCommand = exec.Command(
			realPath,
			"--project", projectId,
			"--dataset", datasetName,
			"--port", strconv.Itoa(port),
			// We need a unique gRPC port for the server to start sucessfully but don't need to know
			// what that port is.
			"--grpc-port=0",
		)

		// Start the emulator in the background.
		err = bqCommand.Start()
		if err != nil {
			// This is unexpected.
			log.Warn().Msgf("Unexpected error starting BigQuery emulator: %v", err)
			return nil, err
		}

		// Start a goroutine that waits for the emulator to exit and reports errors on a channel.
		// This goroutine will keep running until the emulator exits, but note that it should be
		// impossible to end up with more than one waiting thread because we'll only create a new
		// goroutine if this one fails.
		errChan := make(chan error)
		go func() {
			err := bqCommand.Wait()
			errChan <- err
		}()

		// Make sure the emulator is still running after 2s, and consider it to have started
		// successfully if it is. This is a hack, but the emulator doesn't print anything after it
		// starts successfully so there's nothing to listen for.
		select {
		case err := <-errChan:
			if err != nil {
				log.Warn().Msgf("BigQuery emulator failed to start: %v", err)
				continue
			}
		case <-time.After(2 * time.Second):
		}

		log.Info().Msgf("BigQuery emulator started on port %d", port)
		break
	}

	// Create a BigQuery client that connects to the emulator.
	bqClient, err := bigquery.NewClient(
		ctx,
		"test-project",
		option.WithEndpoint(fmt.Sprintf("http://127.0.0.1:%d", port)),
		option.WithoutAuthentication(),
	)
	if err != nil {
		return nil, err
	}

	// Create a dummy table to ensure the emulator is ready.
	dataset := bqClient.Dataset(datasetName)
	setupTable := dataset.Table("connection_test")
	err = setupTable.Create(
		ctx,
		&bigquery.TableMetadata{Schema: bigquery.Schema{
			{Name: "test_col", Type: bigquery.StringFieldType},
		}},
	)
	if err != nil {
		return nil, err
	}
	// Delete the dummy table.
	err = setupTable.Delete(ctx)
	if err != nil {
		return nil, err
	}

	// The emulator started successfully.
	return &BigQueryEmulator{
		Client:  bqClient,
		Dataset: dataset,
		cmd:     bqCommand,
	}, nil
}

// Load a JSON-formatted list of tables from a file into the emulator. See `LoadJsonSchemaString`.
func (b *BigQueryEmulator) LoadJsonSchemaFile(ctx context.Context, jsonSchemaPath string) error {
	jsonSchema, err := os.ReadFile(jsonSchemaPath)
	if err != nil {
		return err
	}
	return b.LoadJsonSchemaString(ctx, string(jsonSchema))
}

// Load a JSON-formatted list of tables into the emulator. It is valid to load multiple schemas in
// the same emulator, though not recommended since it could lead to conflicts. If this method
// returns an error the dataset may be in an inconsistent state with a subset of tables created.
func (b *BigQueryEmulator) LoadJsonSchemaString(ctx context.Context, jsonSchema string) error {
	var tables []table
	err := json.Unmarshal([]byte(jsonSchema), &tables)
	if err != nil {
		return err
	}

	for _, table := range tables {
		err := b.Dataset.Table(table.Name).Create(ctx, &bigquery.TableMetadata{
			Description: table.Description,
			Schema:      table.toBigquerySchema(),
			ViewQuery:   table.ViewQuery,
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// Convert a table's JSON schema into a bigquery.Schema. Returns nil for views.
func (t *table) toBigquerySchema() bigquery.Schema {
	if t.isView() {
		return nil
	}

	var schema bigquery.Schema
	for _, row := range t.Schema {
		schema = append(schema, &bigquery.FieldSchema{
			Name:        row.Name,
			Type:        bigquery.FieldType(row.Type),
			Required:    row.Mode == "REQUIRED",
			Repeated:    row.Mode == "REPEATED",
			Description: row.Description,
		})
	}
	return schema
}

func (t *table) isView() bool {
	return t.ViewQuery != ""
}

// Clean up an emulator's resources.
func (b *BigQueryEmulator) Close() {
	b.Client.Close()
	b.cmd.Process.Kill()
}
