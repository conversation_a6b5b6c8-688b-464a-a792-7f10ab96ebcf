load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "emulator_go",
    testonly = True,
    srcs = ["emulator.go"],
    data = ["@bigquery_emulator_amd64//:all"],
    importpath = "github.com/augmentcode/augment/base/test_utils/bigquery/emulator",
    visibility = BASE_VISIBILITY,
    deps = [
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@io_bazel_rules_go//go/runfiles",
        "@org_golang_google_api//option",
    ],
)

go_test(
    name = "emulator_go_test",
    size = "medium",  # Because it starts other processes.
    srcs = ["emulator_test.go"],
    data = [
        "test_data/test_schema.json",
    ],
    embed = [":emulator_go"],
    deps = [
        "@org_golang_google_api//iterator",
    ],
)
