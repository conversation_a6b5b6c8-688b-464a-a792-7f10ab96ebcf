package bqemulator

import (
	"context"
	"testing"

	"google.golang.org/api/iterator"
)

type testTable struct {
	StringColumn string `bigquery:"string_column"`
	IntColumn    int    `bigquery:"int_column"`
}

// Test the basic, happy path usage of the emulator.
func TestBasic(t *testing.T) {
	// Start the emulator.
	emulator, err := New(context.Background(), "test-project", "test_dataset")
	if err != nil {
		t.Fatalf("Error starting BigQuery emulator: %v", err)
	}
	defer emulator.Close()

	// Load the schema.
	err = emulator.LoadJsonSchemaFile(context.Background(), "test_data/test_schema.json")
	if err != nil {
		t.Fatalf("Error loading schema: %v", err)
	}

	inserter := emulator.Dataset.Table("test_table").Inserter()
	err = inserter.Put(context.Background(), []testTable{
		{StringColumn: "foo", IntColumn: 100},
	})
	if err != nil {
		t.Fatalf("Error writing row: %v", err)
	}

	// Read the row back.
	query := emulator.Client.Query(`
		SELECT * FROM test_dataset.test_table
	`)
	it, err := query.Read(context.Background())
	if err != nil {
		t.Fatalf("Error reading row: %v", err)
	}
	var row testTable
	err = it.Next(&row)
	if err != nil {
		t.Fatalf("Error reading row: %v", err)
	}
	if row.StringColumn != "foo" {
		t.Fatalf("Expected string column to be 'foo', got '%s'", row.StringColumn)
	}
	if row.IntColumn != 100 {
		t.Fatalf("Expected int column to be 100, got %d", row.IntColumn)
	}
	if it.Next(&row) != iterator.Done {
		t.Fatalf("Expected only one row, but there are more")
	}

	// Read from a view.
	query = emulator.Client.Query(`
		SELECT * FROM test_dataset.test_view
	`)
	it, err = query.Read(context.Background())
	if err != nil {
		t.Fatalf("Error reading row: %v", err)
	}
	err = it.Next(&row)
	if err != nil {
		t.Fatalf("Error reading row: %v", err)
	}
	if row.StringColumn != "foo" {
		t.Fatalf("Expected string column to be 'foo', got '%s'", row.StringColumn)
	}
	if row.IntColumn != 100 {
		t.Fatalf("Expected int column to be 100, got %d", row.IntColumn)
	}
	if it.Next(&row) != iterator.Done {
		t.Fatalf("Expected only one row, but there are more")
	}
}

// Test that multiple emulators can run at the same time..
func MultipleEmulators(t *testing.T) {
	// Start an emulator.
	emulator1, err := New(context.Background(), "test-project", "test_dataset")
	if err != nil {
		t.Fatalf("Error starting BigQuery emulator: %v", err)
	}
	defer emulator1.Close()

	// Start another emulator on a different port
	emulator2, err := New(context.Background(), "test-project", "test_dataset")
	if err != nil {
		t.Fatalf("Error starting BigQuery emulator: %v", err)
	}
	defer emulator2.Close()
}
