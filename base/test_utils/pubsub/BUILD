load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "pubsub_emulator",
    testonly = True,
    srcs = ["pubsub_emulator.py"],
    data = [
        "@bazel_tools//tools/jdk:current_java_runtime",
        "@cloud_pubsub_emulator_jar//:all",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/test_utils:process",
    ],
)

pytest_test(
    name = "pubsub_emulator_test",
    srcs = ["pubsub_emulator_test.py"],
    deps = [
        requirement("google-cloud-pubsub"),
        ":pubsub_emulator",
    ],
)

go_library(
    name = "pubsub_emulator_go",
    testonly = True,
    srcs = ["pubsub_emulator.go"],
    data = [
        "@bazel_tools//tools/jdk:current_java_runtime",
        "@cloud_pubsub_emulator_jar//:all",
    ],
    importpath = "github.com/augmentcode/augment/base/test_utils/pubsub",
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/test_utils/process:process_go",
        "@com_github_pkg_errors//:errors",
    ],
)

go_test(
    name = "pubsub_emulator_go_test",
    srcs = ["pubsub_emulator_test.go"],
    embed = [":pubsub_emulator_go"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_google_cloud_go_pubsub//:pubsub",
    ],
)
