// Package pubsub provides utilities for working with the PubSub emulator in tests.
package pubsub

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"regexp"
	"time"

	"github.com/pkg/errors"

	"github.com/augmentcode/augment/base/test_utils/process"
)

// StartEmulator starts the PubSub emulator on a random port or the specified port.
// It returns the emulator endpoint (host:port) and a cleanup function.
// The cleanup function should be called to stop the emulator when it's no longer needed.
func StartEmulator(port int) (string, func(), error) {
	log.Println("Starting pubsub emulator")

	// Find java executable in runfiles
	runfilesDir := os.Getenv("RUNFILES_DIR")
	if runfilesDir == "" {
		runfilesDir = "."
	}

	// Find Java executable
	javaBinPattern := filepath.Join(runfilesDir, "**", "bin", "java")
	javaExecutables, err := filepath.Glob(javaBinPattern)
	if err != nil {
		return "", nil, errors.Wrap(err, "failed to glob for Java executable")
	}
	if len(javaExecutables) == 0 {
		return "", nil, errors.New("could not find Java executable in runfiles")
	}
	javaExecutable := javaExecutables[0]
	log.Printf("Using Java executable: %s", javaExecutable)

	// Find emulator jar
	emulatorJarPattern := filepath.Join(runfilesDir, "cloud_pubsub_emulator_jar", "cloud-pubsub-emulator-*.jar")
	emulatorJars, err := filepath.Glob(emulatorJarPattern)
	if err != nil {
		return "", nil, errors.Wrap(err, "failed to glob for emulator jar")
	}
	if len(emulatorJars) == 0 {
		return "", nil, errors.New("could not find pubsub emulator jar in runfiles")
	}
	emulatorJar := emulatorJars[0]
	log.Printf("Using emulator jar: %s", emulatorJar)

	getRandomPort := func() int {
		return rand.Intn(65535-49152) + 49152
	}

	portToTry := port
	if portToTry == 0 {
		portToTry = getRandomPort()
	}

	for i := 0; i < 10; i++ {
		args := []string{
			javaExecutable,
			"-jar",
			emulatorJar,
			fmt.Sprintf("--port=%d", portToTry),
			"--host=127.0.0.1",
		}

		ctx, cancel := context.WithCancel(context.Background())
		serverManager, err := process.NewServerManager(ctx, args, true, nil)
		if err != nil {
			cancel()
			return "", nil, errors.Wrap(err, "failed to start server manager")
		}

		listeningPattern := regexp.MustCompile(fmt.Sprintf("listening on %d|BindException", portToTry))
		match, err := serverManager.WaitForLine(listeningPattern, os.Stdout, 30*time.Second)
		if err != nil {
			serverManager.Stop()
			cancel()
			return "", nil, errors.Wrap(err, "failed to wait for emulator to start")
		}

		if match[0] == "BindException" {
			log.Printf("Port %d is already in use", portToTry)
			serverManager.Stop()
			cancel()
			if port != 0 {
				return "", nil, fmt.Errorf("port %d is already in use", portToTry)
			}
			portToTry = getRandomPort()
			continue
		}

		serverManager.DetachStdout(os.Stdout)
		log.Printf("Pubsub emulator started on port %d", portToTry)

		endpoint := fmt.Sprintf("127.0.0.1:%d", portToTry)
		cleanup := func() {
			serverManager.Stop()
			cancel()
		}

		return endpoint, cleanup, nil
	}

	return "", nil, errors.New("failed to start pubsub emulator after 10 retries")
}
