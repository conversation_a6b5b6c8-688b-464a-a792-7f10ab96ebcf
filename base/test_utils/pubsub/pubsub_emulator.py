"""A helper for creating fixtures for the BigTable emulator."""

from typing import Generator
import glob
import logging
import os
import random

import base.test_utils.process as process


def start_emulator(port: int = 0) -> Generator[str, None, None]:
    """Helper for implementing pytest fixture to start pub-sub emulator.

    Suggested usage:
        @pytest.fixture(scope="module")
        def pubsub_emulator():
            yield from start_emulator()
    """
    logging.info("Starting pubsub emulator")

    # Find java executable in runfiles
    runfiles_dir = os.environ.get("RUNFILES_DIR", ".")
    java_bin_patterns = [
        # Pattern for Linux
        os.path.join(runfiles_dir, "**", "bin", "java"),
    ]
    java_executables = glob.glob(java_bin_patterns[0], recursive=True)
    if not java_executables:
        raise RuntimeError("Could not find Java executable in runfiles")
    java_executable = java_executables[0]
    logging.info("Using Java executable: %s", java_executable)

    emulator_jars = glob.glob(
        os.path.join(
            runfiles_dir, "cloud_pubsub_emulator_jar", "cloud-pubsub-emulator-*.jar"
        ),
    )
    if not emulator_jars:
        raise RuntimeError("Could not find pubsub emulator jar in runfiles")
    emulator_jar = emulator_jars[0]
    logging.info("Using emulator jar: %s", emulator_jar)

    def get_random_port():
        return random.randint(49152, 65535)

    port_to_try = port if port != 0 else get_random_port()

    for _ in range(10):
        with process.ServerManager(
            [
                java_executable,
                "-jar",
                emulator_jar,
                "--port=%d" % port_to_try,
                "--host=127.0.0.1",
            ],
            redirect_stderr=True,
        ) as p:
            m = process.wait_for_line(
                p.stdout,
                r"listening on %s|BindException" % port_to_try,
                timeout_secs=30,
            )
            if m.group(0) == "BindException":
                logging.info("Port %d is already in use", port_to_try)
                if port != 0:
                    raise RuntimeError("Port %d is already in use" % port_to_try)
                port_to_try = get_random_port()
                continue
            p.detach_stdout()
            logging.info("Pubsub emulator started on port %d", port_to_try)
            yield "127.0.0.1:%d" % port_to_try
            return

    raise RuntimeError("Failed to start pubsub emulator after 10 retries")
