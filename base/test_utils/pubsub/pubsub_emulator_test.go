package pubsub

import (
	"context"
	"log"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEmulator(t *testing.T) {
	// Start the emulator
	endpoint, cleanup, err := StartEmulator(0)
	require.NoError(t, err, "Failed to start PubSub emulator")
	defer cleanup()

	// Set the environment variable for the PubSub client
	os.Setenv("PUBSUB_EMULATOR_HOST", endpoint)

	// Create a PubSub client
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	client, err := pubsub.NewClient(ctx, "test-project")
	require.NoError(t, err, "Failed to create PubSub client")
	defer client.Close()

	// Create a topic
	topicID := "test-topic"
	topic, err := client.CreateTopic(ctx, topicID)
	require.NoError(t, err, "Failed to create topic")
	log.Printf("Created topic: %s", topic.ID())

	// Verify the topic exists
	exists, err := topic.Exists(ctx)
	assert.NoError(t, err, "Failed to check if topic exists")
	assert.True(t, exists, "Topic should exist")

	// Create a subscription
	subID := "test-subscription"
	sub, err := client.CreateSubscription(ctx, subID, pubsub.SubscriptionConfig{
		Topic: topic,
	})
	require.NoError(t, err, "Failed to create subscription")
	log.Printf("Created subscription: %s", sub.ID())

	// Publish a message
	result := topic.Publish(ctx, &pubsub.Message{
		Data: []byte("Hello, world!"),
		Attributes: map[string]string{
			"key": "value",
		},
	})

	// Get the server-generated message ID
	msgID, err := result.Get(ctx)
	require.NoError(t, err, "Failed to publish message")
	log.Printf("Published message with ID: %s", msgID)

	// Receive the message
	received := make(chan *pubsub.Message, 1)
	ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		received <- msg
		msg.Ack()
		close(received)
		cancel()
	})

	select {
	case msg := <-received:
		assert.Equal(t, "Hello, world!", string(msg.Data), "Message data should match")
		assert.Equal(t, "value", msg.Attributes["key"], "Message attribute should match")
	default:
		t.Fatal("Failed to receive message")
	}
}
