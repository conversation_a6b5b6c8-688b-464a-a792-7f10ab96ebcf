from base.test_utils.pubsub import pubsub_emulator
import logging
import os
import pytest

from google.cloud import pubsub_v1


@pytest.fixture(scope="module")
def pubsub_endpoint():
    yield from pubsub_emulator.start_emulator()


def test_emulator(pubsub_endpoint):
    os.environ["PUBSUB_EMULATOR_HOST"] = pubsub_endpoint

    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path("test-project", "test-topic")
    publisher.create_topic(request={"name": topic_path})
    logging.info("Created topic %s", topic_path)
