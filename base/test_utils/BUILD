load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

LIBRARY_VISIBILITY = BASE_VISIBILITY + ["//base/test_utils:__subpackages__"]

py_library(
    name = "process",
    testonly = True,
    srcs = ["process.py"],
    visibility = LIBRARY_VISIBILITY,
)

py_library(
    name = "bigtable_setup",
    testonly = True,
    srcs = ["bigtable_setup.py"],
    pyright_extra_args = {
        "reportMissingParameterType": True,
    },
    visibility = BASE_VISIBILITY,
    deps = [
        requirement("google-cloud-bigtable"),
    ],
)

py_library(
    name = "bigtable_emulator",
    testonly = True,
    srcs = ["bigtable_emulator.py"],
    data = [
        "@bigtable_emulator_amd64//:all",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        ":process",
        "@rules_python//python/runfiles",
    ],
)

pytest_test(
    name = "process_test",
    srcs = ["process_test.py"],
    deps = [":process"],
)

py_library(
    name = "synchronous_executor",
    testonly = True,
    srcs = ["synchronous_executor.py"],
    visibility = LIBRARY_VISIBILITY,
)

py_library(
    name = "junit_xml_writer",
    testonly = True,
    srcs = ["junit_xml_writer.py"],
    visibility = BASE_VISIBILITY,
)

py_library(
    name = "testing_utils",
    testonly = True,
    srcs = ["testing_utils.py"],
    visibility = ["//visibility:public"],
)

go_library(
    name = "testing_utils_go",
    testonly = True,
    srcs = ["testing_utils.go"],
    importpath = "github.com/augmentcode/augment/base/test_utils",
    visibility = BASE_VISIBILITY,
    deps = [
        "@org_golang_google_protobuf//proto",
    ],
)
