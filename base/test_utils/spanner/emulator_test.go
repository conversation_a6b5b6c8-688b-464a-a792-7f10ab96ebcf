package spanner_emulator

import (
	"context"
	"errors"
	"testing"

	"cloud.google.com/go/spanner"
	"github.com/stretchr/testify/require"
)

// Test the basic, happy path usage of the emulator.
func TestBasic(t *testing.T) {
	// Start the emulator.
	emulator, err := New(context.Background())
	require.NoError(t, err)
	defer emulator.Close()

	// Create a database.
	client, cleanup, err := emulator.NewDatabaseFromJson(context.Background(), "test_data/test_ddl.json")
	require.NoError(t, err)
	defer cleanup()

	// Write a row.
	_, err = client.Apply(context.Background(), []*spanner.Mutation{
		spanner.Insert("TestTable", []string{"ID", "Value"}, []interface{}{1, "test"}),
	})
	require.NoError(t, err)

	// Read the row back.
	row, err := client.Single().ReadRow(
		context.Background(),
		"TestTable", spanner.Key{1}, []string{"Value"},
	)
	require.NoError(t, err)

	var value string
	err = row.Column(0, &value)
	require.NoError(t, err)
	require.Equal(t, "test", value)
}

// Test that databases in the same emulator are isolated from each other.
func TestIsolatedDatabases(t *testing.T) {
	// Start the emulator.
	emulator, err := New(context.Background())
	require.NoError(t, err)
	defer emulator.Close()

	// Create two databases.
	client1, cleanup1, err := emulator.NewDatabaseFromJson(context.Background(), "test_data/test_ddl.json")
	require.NoError(t, err)
	defer cleanup1()

	client2, cleanup2, err := emulator.NewDatabaseFromJson(context.Background(), "test_data/test_ddl.json")
	require.NoError(t, err)
	defer cleanup2()

	// Write a row to one database.
	_, err = client1.Apply(context.Background(), []*spanner.Mutation{
		spanner.Insert("TestTable", []string{"ID", "Value"}, []interface{}{1, "test"}),
	})
	require.NoError(t, err)

	// Make sure the row is missing from the other database.
	_, err = client2.Single().ReadRow(
		context.Background(),
		"TestTable", spanner.Key{1}, []string{"Value"},
	)
	if !errors.Is(err, spanner.ErrRowNotFound) {
		t.Fatalf("Expected ErrRowNotFound, got %v", err)
	}
}

// Test that multiple emulators can run at the same time.
func TestMultipleEmulators(t *testing.T) {
	// Start an emulator.
	emulator1, err := New(context.Background())
	require.NoError(t, err)
	defer emulator1.Close()

	// Start another emulator on a different port
	emulator2, err := New(context.Background())
	require.NoError(t, err)
	defer emulator2.Close()
}
