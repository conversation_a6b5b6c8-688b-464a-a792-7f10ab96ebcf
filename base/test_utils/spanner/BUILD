load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "emulator_go",
    testonly = True,
    srcs = ["emulator.go"],
    data = ["@spanner_emulator_amd64//:all"],
    importpath = "github.com/augmentcode/augment/base/test_utils/spanner/emulator",
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/test_utils/process:process_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_spanner//:spanner",
        "@com_google_cloud_go_spanner//admin/database/apiv1",
        "@com_google_cloud_go_spanner//admin/database/apiv1/databasepb",
        "@com_google_cloud_go_spanner//admin/instance/apiv1",
        "@com_google_cloud_go_spanner//admin/instance/apiv1/instancepb",
        "@io_bazel_rules_go//go/runfiles",
        "@org_golang_google_api//option",
    ],
)

go_test(
    name = "emulator_go_test",
    size = "medium",  # Because it starts other processes.
    srcs = ["emulator_test.go"],
    data = [
        "test_data/test_ddl.json",
    ],
    embed = [":emulator_go"],
    deps = [
        "@com_github_stretchr_testify//require",
    ],
)
