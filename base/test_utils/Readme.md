## Augment Test Utilities

Set of tools for testing.

* Bigtable emulator
* Spinning up processes with wait for line
* Synchronous executors


# Bigtable Emulator Usage Example

Add the following dependency to your test
```bazel
"//base/test_utils:bigtable_emulator",
```

Add the following to your pytest

```python
import base.test_utils.bigtable_emulator

@pytest.fixture(scope="module")
def bigtable_emulator(): # spin up the emulator
    """Fixture to start a Bigtable emulator."""
    yield from base.test_utils.bigtable_emulator.start_emulator()


def bigtable_table(bigtable_emulator): # connect to the emulator
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator

    bigtable_connector.setup_table(instance_id=INSTANCE_ID, table_name=TABLE_NAME)
    table = bigtable_connector.connect(instance_id=INSTANCE_ID, table_name=TABLE_NAME)
    yield table
```
