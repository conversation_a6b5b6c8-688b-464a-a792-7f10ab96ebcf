// Package process provides utilities for running processes.
package process

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"regexp"
	"sync"
	"syscall"
	"time"

	"github.com/pkg/errors"
)

// ServerManager manages a server process.
type ServerManager struct {
	Cmd       *exec.Cmd
	reader    *bufio.Reader
	waitGroup *sync.WaitGroup
}

// NewServerManager creates a new ServerManager.
func NewServerManager(ctx context.Context, args []string, redirectStderr bool, env []string) (*ServerManager, error) {
	cmd := exec.CommandContext(ctx, args[0], args[1:]...)

	// Set up environment
	if env != nil {
		cmd.Env = env
	} else {
		cmd.Env = os.Environ()
	}

	// Set up stdin/stdout/stderr
	cmd.Stdin = nil

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return nil, errors.Wrap(err, "failed to create stdout pipe")
	}

	if redirectStderr {
		cmd.Stderr = cmd.Stdout
	}

	// Start the process
	if err := cmd.Start(); err != nil {
		return nil, errors.Wrap(err, "failed to start process")
	}

	log.Printf("Started %v with pid %d", args, cmd.Process.Pid)

	return &ServerManager{
		Cmd:       cmd,
		reader:    bufio.NewReader(stdout),
		waitGroup: &sync.WaitGroup{},
	}, nil
}

// WaitForLine waits for a line matching the given regexp to appear in the input.
// It returns the matched string and an error if the timeout is reached.
func (s *ServerManager) WaitForLine(pattern *regexp.Regexp, output io.Writer, timeout time.Duration) ([]string, error) {
	// Channel to receive the match
	matchCh := make(chan []string, 1)

	f, _ := output.(interface{ Flush() error })

	// Start a goroutine to scan for the pattern
	go func() {
		for {
			line, isPrefix, err := s.reader.ReadLine()
			if isPrefix {
				output.Write(line)
				if f != nil {
					f.Flush()
				}
				log.Printf("Line too long for buffer, skipping")
				continue
			}
			if line != nil && output != nil {
				output.Write(line)
				output.Write([]byte{'\n'})
				if f != nil {
					f.Flush()
				}
			}
			if err != nil {
				if err != io.EOF {
					log.Printf("Error reading from process: %v", err)
				}
				break
			}
			if match := pattern.FindStringSubmatch(string(line)); match != nil {
				matchCh <- match
				return
			}
		}
		close(matchCh)
	}()

	// Wait for a match or timeout
	select {
	case match, ok := <-matchCh:
		if !ok {
			return nil, fmt.Errorf("no match for pattern: %s", pattern.String())
		}
		return match, nil
	case <-time.After(timeout):
		return nil, fmt.Errorf("timeout waiting for pattern: %s", pattern.String())
	}
}

// DetachStdout starts a goroutine to copy stdout to the given writer.
func (s *ServerManager) DetachStdout(stdout io.Writer) {
	s.waitGroup.Add(1)
	f, _ := stdout.(interface{ Flush() error })

	go func() {
		defer s.waitGroup.Done()
		buffer := make([]byte, 1024)
		for {
			n, err := s.reader.Read(buffer)
			if n > 0 {
				stdout.Write(buffer[:n])
				if f != nil {
					f.Flush()
				}
			}
			if err != nil {
				if err != io.EOF {
					log.Printf("Error reading from process: %v", err)
				}
				break
			}
		}
	}()
}

func (s *ServerManager) Wait() error {
	return s.Cmd.Wait()
}

// Stop stops the server.
func (s *ServerManager) Stop() {
	if s.Cmd.Process != nil {
		log.Printf("Sending SIGTERM to pid %d", s.Cmd.Process.Pid)
		if err := s.Cmd.Process.Signal(syscall.SIGTERM); err != nil {
			log.Printf("Failed to send SIGTERM to pid %d: %v", s.Cmd.Process.Pid, err)
			s.Cmd.Process.Kill()
		}

		// Wait for the process to exit
		err := s.Cmd.Wait()
		if err != nil {
			log.Printf("Process %d exited with error: %v", s.Cmd.Process.Pid, err)
		} else {
			log.Printf("Process %d exited successfully", s.Cmd.Process.Pid)
		}
	}

	// Wait for the stdout goroutine to finish
	s.waitGroup.Wait()
	log.Printf("Finished ServerManager cleanup for pid %d", s.Cmd.Process.Pid)
}
