package process

import (
	"bytes"
	"context"
	"os"
	"regexp"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestServerManager(t *testing.T) {
	// Skip on Windows as the command is Unix-specific
	if os.Getenv("GOOS") == "windows" {
		t.Skip("Skipping on Windows")
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Use "echo" command for testing
	manager, err := NewServerManager(ctx, []string{"echo", "Hello, World!"}, true, nil)
	require.NoError(t, err, "NewServerManager should not return an error")

	match, err := manager.WaitForLine(regexp.MustCompile("Hello, World!"), os.Stdout, 10*time.Second)
	require.NoError(t, err, "WaitForLine should not return an error")
	assert.Equal(t, "Hello, World!", match[0], "WaitForLine should return the matched line")

	manager.DetachStdout(os.Stdout)
	manager.Wait()
	manager.Stop()
}

func TestServerManagerTimeout(t *testing.T) {
	// Skip on Windows as the command is Unix-specific
	if os.Getenv("GOOS") == "windows" {
		t.Skip("Skipping on Windows")
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Use "echo" command for testing
	manager, err := NewServerManager(ctx, []string{"sleep", "10"}, true, nil)
	require.NoError(t, err, "NewServerManager should not return an error")
	match, err := manager.WaitForLine(regexp.MustCompile("Hello, World!"), os.Stdout, 500*time.Millisecond)
	require.Error(t, err, "WaitForLine should return an error")
	assert.Empty(t, match, "WaitForLine should not return a match")

	manager.Stop()
}

func TestServerManagerDetachStdout(t *testing.T) {
	// Skip on Windows as the command is Unix-specific
	if os.Getenv("GOOS") == "windows" {
		t.Skip("Skipping on Windows")
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Use a command that produces multiple lines of output
	manager, err := NewServerManager(ctx, []string{"sh", "-c", "echo line1; echo line2; echo line3"}, true, nil)
	require.NoError(t, err, "NewServerManager should not return an error")

	match, err := manager.WaitForLine(regexp.MustCompile("line1"), os.Stdout, 10*time.Second)
	require.NoError(t, err, "WaitForLine should not return an error")
	assert.Equal(t, "line1", match[0], "WaitForLine should return the matched line")

	var output bytes.Buffer
	manager.DetachStdout(&output)

	manager.Wait()

	// Stop the server
	manager.Stop()

	// Check the output
	assert.Contains(t, output.String(), "line2", "Output should contain line2")
	assert.Contains(t, output.String(), "line3", "Output should contain line3")
}
