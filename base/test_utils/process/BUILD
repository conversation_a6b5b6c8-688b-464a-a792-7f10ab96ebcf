load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")

LIBRARY_VISIBILITY = BASE_VISIBILITY + ["//base/test_utils:__subpackages__"]

go_library(
    name = "process_go",
    srcs = ["process.go"],
    importpath = "github.com/augmentcode/augment/base/test_utils/process",
    visibility = LIBRARY_VISIBILITY,
    deps = [
        "@com_github_pkg_errors//:errors",
    ],
)

go_test(
    name = "process_go_test",
    srcs = ["process_test.go"],
    embed = [":process_go"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
