load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("//base:base.bzl", "BASE_VISIBILITY")

go_library(
    name = "bigtable_utils_go",
    srcs = [
        "bigtable_emulator.go",
        "bigtable_setup.go",
    ],
    data = [
        "@bigtable_emulator_amd64//:all",
    ],
    importpath = "github.com/augmentcode/augment/base/test_utils/bigtable_utils",
    visibility = BASE_VISIBILITY,
    deps = [
        "//base/test_utils/process:process_go",
        "@com_github_pkg_errors//:errors",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@io_bazel_rules_go//go/runfiles:go_default_library",
        "@org_golang_google_api//option",
    ],
)
