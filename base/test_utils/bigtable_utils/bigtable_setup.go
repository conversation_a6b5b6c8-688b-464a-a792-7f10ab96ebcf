package bigtable_utils

import (
	"context"
	"fmt"
	"os"

	"cloud.google.com/go/bigtable"
	"google.golang.org/api/option"
)

// TableDetails contains the configuration for a Bigtable table
type TableDetails struct {
	TableName string
	Project   string
	Instance  string
}

// NewTableDetails creates a new TableDetails with default test values
func NewTableDetails(tableName string) *TableDetails {
	return &TableDetails{
		TableName: tableName,
		// Using default value from bigtable library because auth-query does not support an empty value
		Project:  "google-cloud-bigtable-emulator",
		Instance: "test-instance",
	}
}

// CreateTable creates a new Bigtable table with the specified column families
// Returns a cleanup function to delete the table
func CreateTable(ctx context.Context, table *TableDetails, familyColumns []string) (func(), error) {
	adminClient, err := bigtable.NewAdminClient(ctx,
		table.Project,
		table.Instance,
		option.WithEndpoint(getEmulatorHost()),
		option.WithoutAuthentication(),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create admin client: %v", err)
	}

	// Create table
	if err := adminClient.CreateTable(ctx, table.TableName); err != nil {
		adminClient.Close()
		return nil, fmt.Errorf("failed to create table: %v", err)
	}

	// Create column families
	for _, family := range familyColumns {
		if err := adminClient.CreateColumnFamily(ctx, table.TableName, family); err != nil {
			adminClient.Close()
			return nil, fmt.Errorf("failed to create column family %s: %v", family, err)
		}
	}

	cleanup := func() {
		if err := adminClient.DeleteTable(ctx, table.TableName); err != nil {
			fmt.Printf("failed to delete table: %v\n", err)
		}
		adminClient.Close()
	}

	return cleanup, nil
}

// CreateTableNoDel creates a table without returning a cleanup function
// This should only be used when the emulator itself will be shut down after the test
func CreateTableNoDel(ctx context.Context, table *TableDetails, familyColumns []string) error {
	adminClient, err := bigtable.NewAdminClient(ctx,
		table.Project,
		table.Instance,
		option.WithEndpoint(getEmulatorHost()),
		option.WithoutAuthentication(),
	)
	if err != nil {
		return fmt.Errorf("failed to create admin client: %v", err)
	}
	defer adminClient.Close()

	if err := adminClient.CreateTable(ctx, table.TableName); err != nil {
		return fmt.Errorf("failed to create table: %v", err)
	}

	for _, family := range familyColumns {
		if err := adminClient.CreateColumnFamily(ctx, table.TableName, family); err != nil {
			return fmt.Errorf("failed to create column family %s: %v", family, err)
		}
	}

	return nil
}

func getEmulatorHost() string {
	if host := os.Getenv("BIGTABLE_EMULATOR_HOST"); host != "" {
		return host
	}
	return "localhost:8086" // fallback default
}
