package bigtable_utils

import (
	"context"
	"fmt"
	"os"
	"regexp"
	"time"

	"github.com/augmentcode/augment/base/test_utils/process"
	"github.com/bazelbuild/rules_go/go/runfiles"
	"github.com/pkg/errors"
)

// StartEmulator starts the Bigtable emulator and returns the host:port string
// The caller must call the returned cleanup function when done
func StartEmulator() (hostPort string, cleanup func(), err error) {
	emulatorPath, err := runfiles.Rlocation("bigtable_emulator_amd64/emulator")
	if err != nil {
		return "", nil, fmt.Errorf("failed to find emulator: %v", err)
	}

	serverManager, err := process.NewServerManager(context.Background(), []string{emulatorPath, "-port=0"}, false, nil)
	if err != nil {
		return "", nil, errors.Wrap(err, "failed to start server manager")
	}

	match, err := serverManager.WaitForLine(regexp.MustCompile(`Cloud Bigtable emulator running on 127.0.0.1:(\d+)`), os.Stdo<PERSON>, 30*time.Second)
	if err != nil {
		serverManager.Stop()
		return "", nil, errors.Wrap(err, "failed to wait for emulator to start")
	}

	hostPort = fmt.Sprintf("127.0.0.1:%s", match[1])

	serverManager.DetachStdout(os.Stdout)

	cleanup = func() {
		serverManager.Stop()
	}

	return hostPort, cleanup, nil
}

// SetupEmulatorEnv sets up the environment variables for the Bigtable emulator
func SetupEmulatorEnv(hostPort string) {
	os.Setenv("BIGTABLE_EMULATOR_HOST", hostPort)
}

// CleanupEmulatorEnv cleans up the environment variables for the Bigtable emulator
func CleanupEmulatorEnv() {
	os.Unsetenv("BIGTABLE_EMULATOR_HOST")
}
