"""Utilities that are used for unit testing."""

# ------------------- property test utils ----------------------

import difflib
import sys
import types
from contextlib import contextmanager
from random import Random
from typing import Callable, TypeVar

AT = TypeVar("AT")
BT = TypeVar("BT")
_ET = TypeVar("_ET", bound=Exception)


def random_char(rng: Random) -> str:
    """Return a random character for testing."""
    if rng.random() < 0.3:
        return rng.choice([" ", "\n", "\t"])
    # return a random visible character
    return rng.choice(["a", "b", "c", "♺", "😃", ":", "?"])


def random_str(rng: Random, max_len: int = 16) -> str:
    """Return a random string for testing."""
    if rng.random() < 0.2:
        # return one of the special strings
        return rng.choice(
            [
                "",
                " ",
                "a",
                "\n",
                "\n\na",
                "\r\n",
            ]
        )
    n = rng.randint(0, max_len)
    return "".join(random_char(rng) for _ in range(n))


def run_property_test(
    f: Callable[[Random], None], trials: int = 1000, seed: int = 1
) -> None:
    """Run a randomized property test for `trials` times."""
    run_property_test_sized(lambda rng, _: f(rng), trials=trials, seed=seed)


def run_property_test_sized(
    f: Callable[[Random, float], None], trials: int = 1000, seed: int = 1
) -> None:
    """Run a randomized property test for `trials` times.

    This variant calls `f` with a `size` parameter that increases from 0 to 1 over
    the course of the trials.
    """
    for i in range(trials):
        size = i / trials
        rng = Random(seed + i)
        for _ in range(5):
            # warm up the rng to reduce the correlations from using adjacent seeds
            rng.random()
        with error_context(f"trial {i}"):
            f(rng, size)


@contextmanager
def error_context(msg: str):
    """Print out additional information when an exception is raised in a 'with' block.

    ## Example:
    ```python
    x = 2
    with error_context(f"x = {x}"):
        assert x == 3
    ```
    """
    try:
        yield
    except Exception:
        print(f"Error Context: {msg}", file=sys.stderr)
        raise


def show_whitespaces(text: str) -> str:
    """Explicitly show special whitespace characters using unicode symbols."""
    return (
        text.replace(" ", "·")
        .replace("\t", "   →")
        .replace("\n", "↩\n")
        .replace("\r", "\\r")
    )


def _drop_stack_frame(e: _ET) -> _ET:
    """Drop the top stack frame from the traceback of `e`."""
    tb = sys.exc_info()[2]
    assert tb is not None
    back_frame = tb.tb_frame.f_back
    assert back_frame is not None
    back_tb = types.TracebackType(
        tb_next=None,
        tb_frame=back_frame,
        tb_lasti=back_frame.f_lasti,
        tb_lineno=back_frame.f_lineno,
    )
    return e.with_traceback(back_tb)


def _empty_message():
    return ""


def _show_str_diff(old: str, new: str) -> str:
    """Show the diff between two strings."""

    def show_line_ending(line: str) -> str:
        if line.endswith("\n"):
            return line
        else:
            return line + "\\ No newline\n"

    before_lines = [show_line_ending(line) for line in old.splitlines(keepends=True)]
    #
    new_lines = [show_line_ending(line) for line in new.splitlines(keepends=True)]
    difflines = difflib.unified_diff(before_lines, new_lines)
    return "".join(difflines)


def assert_eq(
    actual: AT, expect: AT, message: Callable[[], str] = _empty_message
) -> None:
    """Assert that two values x and y are equal.

    If not equal, will raise an `AssertionError` containing the values and types of
    the two inputs.
    """
    if actual != expect:
        try:
            raise AssertionError(
                f"{actual} (of type {type(actual).__name__}) "
                f"!= {expect} (of type {type(expect).__name__}).\n" + message()
            )
        except AssertionError as e:
            # drop `assert_eq` from the stack trace to improve readability
            raise _drop_stack_frame(e)


def assert_str_eq(
    actual: str, expect: str, message: Callable[[], str] = _empty_message
):
    """Assert that two strings x and y are equal.

    If not equal, will raise an `AssertionError` containing a diff of the two strings.
    """
    if actual != expect:
        actual = show_whitespaces(actual)
        expect = show_whitespaces(expect)
        diff = _show_str_diff(old=expect, new=actual)
        try:
            raise AssertionError(
                f"Strings are not equal: {message()}\n"
                f"[[Expected]]\n{expect}\n"
                f"[[Actual]]\n{actual}\n"
                f"[[Diff against expected]]\n{diff}\n"
            )
        except AssertionError as e:
            # drop `assert_str_eq` from the stack trace to improve readability
            raise _drop_stack_frame(e)
    return True
