use std::io;

use actix_web::{dev::Server, web, App, HttpResponse, HttpServer};

use tracing_actix_web::DefaultRootSpanBuilder;

/// HTTP handler for reporting metrics to prometheus.
async fn get_metrics() -> HttpResponse {
    use prometheus::Encoder;
    let encoder = prometheus::TextEncoder::new();

    let mut buffer = Vec::new();
    if let Err(e) = encoder.encode(&prometheus::gather(), &mut buffer) {
        eprintln!("could not encode prometheus metrics: {e}");
    };
    let prometheus_metrics = match String::from_utf8(buffer) {
        Ok(v) => v,
        Err(e) => {
            eprintln!("prometheus metrics could not be from_utf8'd: {e}");
            String::default()
        }
    };
    HttpResponse::Ok().body(prometheus_metrics)
}

/// setup internal routes
pub fn setup_metrics_http_server(bind_address: &str, port: u16) -> io::Result<Server> {
    let s = HttpServer::new(move || {
        App::new()
            .wrap(tracing_actix_web::TracingLogger::<DefaultRootSpanBuilder>::new())
            .service(web::resource("/metrics").route(web::get().to(get_metrics)))
    })
    .bind((bind_address, port))?;

    s.addrs().iter().for_each(|addr| {
        tracing::info!("Metrics server listening on {}", addr);
    });

    Ok(s.run())
}

pub fn setup_default_metrics() {
    // register global task collector
    let task_collector = tokio_metrics_collector::default_task_collector();
    prometheus::default_registry()
        .register(Box::new(task_collector))
        .unwrap();
}
