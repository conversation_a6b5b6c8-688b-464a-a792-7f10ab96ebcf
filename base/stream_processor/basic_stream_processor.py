import enum
from dataclasses import dataclass
from typing import Iterable, Protocol

import structlog

from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    PromptCacheUsage,
    ReplaceTextResponse,
    ThirdPartyModelResponse,
    ToolUseResponse,
    ToolUseStart,
)

logger = structlog.get_logger("BasicStreamProcessor")


def _last_delim_index(s: str):
    for i in range(len(s) - 1, -1, -1):
        if s[i].isspace() or s[i] == ":" or s[i] == ".":
            return i
    return -1


class StreamProcessorOutputType(enum.Enum):
    ANSWER = 1
    CITATION = 2
    TOOL = 3
    SUGGESTED_QUESTIONS = 4
    RELEVANT_SOURCES = 5
    TOOL_START = 6
    END_OF_STREAM = 7
    THINKING = 8


@dataclass(frozen=True)
class StreamProcessorOutput:
    """The output of the stream processor."""

    text: str
    """The text to send to the client."""

    type: StreamProcessorOutputType
    """The type of the output."""

    replace_text_response: ReplaceTextResponse | None = None

    tool_use: ToolUseResponse | None = None
    tool_use_start: ToolUseStart | None = None

    end_of_stream: EndOfStream | None = None


class StreamProcessor(Protocol):
    """Processes response streams from chat models."""

    def process_stream(
        self, ls_substr: Iterable[ThirdPartyModelResponse]
    ) -> Iterable[StreamProcessorOutput]:
        """Processes a substring of the response stream.

        Args:
            substr: The substring to process.

        Returns:
            The output of the stream processor.
        """
        raise NotImplementedError()

    def get_stream_history(self) -> str:
        """Returns the history of the stream."""
        raise NotImplementedError()


class BasicStreamProcessor(StreamProcessor):
    def __init__(self):
        self.history = ""

    def process_stream(
        self, ls_substr: Iterable[ThirdPartyModelResponse]
    ) -> Iterable[StreamProcessorOutput]:
        text_buffer = ""
        end_of_stream: EndOfStream | None = None
        post_eos_error_logged = False

        for substr in ls_substr:
            self.history += substr.text

            if end_of_stream is not None and not post_eos_error_logged:
                post_eos_error_logged = True
                logger.error("ThirdPartyModelResponse content returned after EOS")

            # replace_text_response has unique behavior of not flushing the text buffer,
            # and passing through its own text, bypassing the stream processing...
            # this is covered in a test so I'm matching existing behavior
            if substr.replace_text_response:
                yield StreamProcessorOutput(
                    substr.text,
                    StreamProcessorOutputType.TOOL,
                    replace_text_response=substr.replace_text_response,
                )
                continue

            if substr.reasoning_content is not None:
                # Flush any buffered text before yielding thinking content
                if text_buffer:
                    yield StreamProcessorOutput(
                        text_buffer, StreamProcessorOutputType.ANSWER
                    )
                    text_buffer = ""
                yield StreamProcessorOutput(
                    substr.reasoning_content,
                    StreamProcessorOutputType.THINKING,
                )
                continue

            text_buffer += substr.text
            cut_index = _last_delim_index(text_buffer)
            if cut_index >= 0:
                text_to_send = text_buffer[: cut_index + 1]
                text_buffer = text_buffer[cut_index + 1 :]
                yield StreamProcessorOutput(
                    text_to_send, StreamProcessorOutputType.ANSWER
                )

            if text_buffer and (
                substr.tool_use_start is not None or substr.tool_use is not None
            ):
                yield StreamProcessorOutput(
                    text_buffer, StreamProcessorOutputType.ANSWER
                )
                text_buffer = ""

            if substr.end_of_stream is not None:
                end_of_stream = substr.end_of_stream
                # This is the awkward part of having EOS be an element of the
                # stream. If the input generator has any cleanup to perform
                # after reporting EOS, we should continue iterating?
            elif substr.tool_use_start is not None:
                yield StreamProcessorOutput(
                    "",
                    StreamProcessorOutputType.TOOL_START,
                    tool_use_start=substr.tool_use_start,
                )
            elif substr.tool_use is not None:
                yield StreamProcessorOutput(
                    "",
                    StreamProcessorOutputType.TOOL,
                    tool_use=substr.tool_use,
                )

        if len(text_buffer):
            yield StreamProcessorOutput(text_buffer, StreamProcessorOutputType.ANSWER)

        if end_of_stream is not None:
            yield StreamProcessorOutput(
                "",
                StreamProcessorOutputType.END_OF_STREAM,
                end_of_stream=end_of_stream,
            )

    def get_stream_history(self) -> str:
        return self.history
