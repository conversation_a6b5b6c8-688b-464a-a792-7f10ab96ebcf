"""Unit tests for the codeblocks XML stream processor."""

import pytest
import re
import numpy as np
from textwrap import dedent

from base.stream_processor.codeblocks_xml_stream_processor import process_codeblocks_xml


def _last_whitespace_index(s: str):
    for i in range(len(s) - 1, -1, -1):
        if s[i].isspace():
            return i
    return -1


# This is a naive implementation of the basic stream processor's split logic.
def basic_stream_processor_split(text: list[str]):
    processed_text = []

    text_buffer = ""
    for substr in text:
        text_buffer += substr
        cut_index = _last_whitespace_index(text_buffer)
        if cut_index >= 0:
            text_to_send = text_buffer[: cut_index + 1]
            text_buffer = text_buffer[cut_index + 1 :]
            processed_text.append(text_to_send)

    if len(text_buffer):
        processed_text.append(text_buffer)

    return processed_text


def random_split(text, n_chunks):
    if not text:
        return []
    np.random.seed(hash(text) % 2**32)
    split_points = sorted(np.random.choice(len(text), n_chunks - 1, replace=False))
    chunks = []
    start = 0
    for point in split_points:
        chunks.append(text[start:point])
        start = point
    chunks.append(text[start:])
    return [chunk for chunk in chunks if chunk]


def random_split5(text):
    n_chunks = max(1, int(len(text) / 5))
    return random_split(text, n_chunks)


def random_split10(text):
    n_chunks = max(1, int(len(text) / 10))
    return random_split(text, n_chunks)


def random_split5_with_basic_processor(text):
    n_chunks = max(1, int(len(text) / 5))
    return basic_stream_processor_split(random_split(text, n_chunks))


def random_split10_with_basic_processor(text):
    n_chunks = max(1, int(len(text) / 10))
    return basic_stream_processor_split(random_split(text, n_chunks))


def split_merge_whitespace(text):
    # Split the text, keeping whitespace as separate groups
    parts = re.split(r"(\s+)", text)
    # Filter out empty strings and merge non-whitespace with following whitespace
    return ["".join(parts[i : i + 2]) for i in range(0, len(parts), 2) if parts[i]]


def split_by_every_char(text):
    return [ch for ch in text]


def no_split(text):
    return [text]


def run_processor(
    text,
    n_extra_backticks: int = 0,
    split_fn=split_merge_whitespace,
    process_fn=process_codeblocks_xml,
):
    return "".join(
        list(process_fn(split_fn(text), n_extra_backticks=n_extra_backticks))
    )


SPLIT_FUNCTIONS = [
    random_split5,
    random_split10,
    random_split5_with_basic_processor,
    random_split10_with_basic_processor,
    split_merge_whitespace,
    split_by_every_char,
    no_split,
]
BACKTICKS = ["```", "````", "`````"]


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_basic_1(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        <augment_code_snippet path="foo/bar.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>

        Let me know if you have any other questions!
        """)
    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python path=foo/bar.py mode=EXCERPT
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_extra_backticks]}

        Let me know if you have any other questions!
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_basic_2(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet path="config/app_config.yaml" mode="EDIT">
        {BACKTICKS[n_input_backticks]}yaml
        app:
        name: MyWebApp
        version: 1.3.0

        database:
        host: new-db.example.com
        port: 5432
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}yaml path=config/app_config.yaml mode=EDIT
        app:
        name: MyWebApp
        version: 1.3.0

        database:
        host: new-db.example.com
        port: 5432
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_basic_3(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet path="hello/world.rb" mode="EDIT">
        {BACKTICKS[n_input_backticks]}ruby
        def main
        puts "Hello, world!"
        end
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}ruby path=hello/world.rb mode=EDIT
        def main
        puts "Hello, world!"
        end
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_basic_4(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        3. In the `experimental/yury/inference/benchmark_inference_server.py` file:

        <augment_code_snippet path="experimental/yury/inference/benchmark_inference_server.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
        non_neural_speculation_factory=longest_overlap_lm.LongestOverlapLM,
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>

        In these cases, LongestOverlapLM is being used as a draft model or speculation model in various neural language model setups and benchmarking scenarios.
        """)
    expected_output = dedent(f"""\
        3. In the `experimental/yury/inference/benchmark_inference_server.py` file:

        {BACKTICKS[n_extra_backticks]}python path=experimental/yury/inference/benchmark_inference_server.py mode=EXCERPT
        non_neural_speculation_factory=longest_overlap_lm.LongestOverlapLM,
        {BACKTICKS[n_extra_backticks]}

        In these cases, LongestOverlapLM is being used as a draft model or speculation model in various neural language model setups and benchmarking scenarios.
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_empty_codeblock(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet path="empty.py" mode="EDIT">
        {BACKTICKS[n_input_backticks]}python
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python path=empty.py mode=EDIT
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_nested_codeblocks_2(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet path="outer.py" mode="EDIT">
        {BACKTICKS[n_input_backticks]}python
        def outer_function():
            # Inner codeblock
            ```python
            def inner_function():
                pass
            ```

            # This metadata should be treated as regular text
            # Since we are still in the codeblock.
            <augment_code_snippet path="outer.py" mode="EDIT">

            test test test
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python path=outer.py mode=EDIT
        def outer_function():
            # Inner codeblock
            ```python
            def inner_function():
                pass
            ```

            # This metadata should be treated as regular text
            # Since we are still in the codeblock.
            <augment_code_snippet path="outer.py" mode="EDIT">

            test test test
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_indented_codeblock(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet path="indented.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
            def indented_function():
                if True:
                    print("This function has indentation")
                    for i in range(3):
                        print(f"Indented line i")
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python path=indented.py mode=EXCERPT
            def indented_function():
                if True:
                    print("This function has indentation")
                    for i in range(3):
                        print(f"Indented line i")
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_no_language_hint(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet path="no_lang.txt" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}
        This is a codeblock without a language hint.
        It should still be processed correctly.
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]} path=no_lang.txt mode=EXCERPT
        This is a codeblock without a language hint.
        It should still be processed correctly.
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_codeblock_without_xml_wrapper(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is a codeblock without XML wrapper:
        {BACKTICKS[n_input_backticks]}python
        def unwrapped_function():
            return "I'm not wrapped in XML tags"
        {BACKTICKS[n_input_backticks]}
        """)
    expected_output = input_text  # Should remain unchanged
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_no_path_mode_attributes(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet>
        {BACKTICKS[n_input_backticks]}python
        def no_attributes():
            return "This codeblock has no path or mode attributes"
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python
        def no_attributes():
            return "This codeblock has no path or mode attributes"
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0])
@pytest.mark.parametrize("n_extra_backticks", [0])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_no_closing_tag(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <augment_code_snippet path="unclosed.py" mode="EDIT">
        {BACKTICKS[n_input_backticks]}python
        def unclosed_function():
            return "This codeblock has no closing XML tag"
        {BACKTICKS[n_input_backticks]}
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python path=unclosed.py mode=EDIT
        def unclosed_function():
            return "This codeblock has no closing XML tag"
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_two_codeblocks(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        <augment_code_snippet path="foo/bar1.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
            print("Hello, world!")
           ...
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>

        And here is another one

        <augment_code_snippet path="foo/bar2.py" mode="EDIT">
        {BACKTICKS[n_input_backticks]}python
            print("Hello, world!")
           ...
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>

        This is it.
        """)
    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python path=foo/bar1.py mode=EXCERPT
            print("Hello, world!")
           ...
        {BACKTICKS[n_extra_backticks]}

        And here is another one

        {BACKTICKS[n_extra_backticks]}python path=foo/bar2.py mode=EDIT
            print("Hello, world!")
           ...
        {BACKTICKS[n_extra_backticks]}

        This is it.
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_closing_tag_with_leading_extra_whitespaces(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        <augment_code_snippet path="foo/bar1.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
            print("Hello, world!")
           ...
        {BACKTICKS[n_input_backticks]}
             </augment_code_snippet>
        test
        """)
    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python path=foo/bar1.py mode=EXCERPT
            print("Hello, world!")
           ...
        {BACKTICKS[n_extra_backticks]}
        test
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_no_double_quotes_around_attributes(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        <augment_code_snippet path=foo/bar.py mode=EXCERPT>
        {BACKTICKS[n_input_backticks]}python
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>

        Let me know if you have any other questions!
        """)
    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python path=foo/bar.py mode=EXCERPT
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_extra_backticks]}

        Let me know if you have any other questions!
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0])
@pytest.mark.parametrize("n_extra_backticks", [0])
@pytest.mark.parametrize("split_fn", [no_split])
def test_xml_tags_in_the_middle_of_codeblock(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_input_backticks]}python
            print("Hello, world!")
        <augment_code_snippet path="foo/bar1.py" mode="EXCERPT">

           ...
            </augment_code_snippet>
        xxx
        {BACKTICKS[n_input_backticks]}
        test
        """)
    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python
            print("Hello, world!")
        <augment_code_snippet path="foo/bar1.py" mode="EXCERPT">

           ...
            </augment_code_snippet>
        xxx
        {BACKTICKS[n_extra_backticks]}
        test
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_tag_inside_codeblock(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_input_backticks]}python
        <augment_code_snippet path="foo/bar1.py" mode="EXCERPT">
            print("Hello, world!")
           ...
        </augment_code_snippet>
        {BACKTICKS[n_input_backticks]}
        test
        """)
    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python path=foo/bar1.py mode=EXCERPT
            print("Hello, world!")
           ...
        {BACKTICKS[n_extra_backticks]}
        test
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
def test_opening_tag_with_leading_extra_whitespaces(
    n_input_backticks, n_extra_backticks, process_fn=process_codeblocks_xml
):
    input_parts = [
        '     <augment_code_snippet path="foo/bar1.py" mode="EXCERPT">\n',
        f"{BACKTICKS[n_input_backticks]}python\n",
        '    print("Hello, world!")\n',
        "    ...\n",
        f"{BACKTICKS[n_input_backticks]}\n",
        "     </augment_code_snippet>\n",
        "test\n",
    ]
    actual_output = "".join(list(process_fn(input_parts, n_extra_backticks)))
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python path=foo/bar1.py mode=EXCERPT
            print("Hello, world!")
            ...
        {BACKTICKS[n_extra_backticks]}
        test
        """)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
def test_opening_tag_split_into_chunks(
    n_input_backticks, n_extra_backticks, process_fn=process_codeblocks_xml
):
    input_parts = [
        "     <augment_code",
        '_snippet path="foo/bar1.py" mode="EXCERPT">\n',
        f"{BACKTICKS[n_input_backticks]}python\n",
        '    print("Hello, world!")\n',
        "    ...\n",
        f"{BACKTICKS[n_input_backticks]}\n",
        "     </augment_code_snippet>\n",
        "test\n",
    ]
    actual_output = "".join(list(process_fn(input_parts, n_extra_backticks)))
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python path=foo/bar1.py mode=EXCERPT
            print("Hello, world!")
            ...
        {BACKTICKS[n_extra_backticks]}
        test
        """)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
def test_long_answer(
    n_input_backticks, n_extra_backticks, process_fn=process_codeblocks_xml
):
    input_parts = [
        "Based on the ",
        "provided code ",
        "excerpts, ",
        "the ",
        "LongestOverlapLM class ",
        "is ",
        "instantiated in ",
        "a few ",
        "different places:\n\n",
        "1. In the ",
        "`experimental/yury/parallel_sd_staged_v0.ipynb`, ",
        "`experimental/yury/parallel_sd_staged_v1.ipynb`, and ",
        "`experimental/yury/sd_staged.ipynb` ",
        "files:\n\n",
        "<augment_code_snippet ",
        'path="experimental/yury/parallel_sd_staged_v0.ipynb" ',
        'mode="EXCERPT">\n',
        f"{BACKTICKS[n_input_backticks]}python\n",
        "lm = ",
        "NeuralLM(tokens_budget=16, ",
        "draft_model=LongestOverlapLM(12, ",
        "1, ",
        "True), ",
        "draft_tokens_budget=36, ",
        "n_rounds=1)\n",
        f"{BACKTICKS[n_input_backticks]}\n",
        "</augment_code_snippet>\n\n",
        "2. In the ",
        "`experimental/yury/inference/benchmark_inference_server_old.py` ",
        "file:\n\n",
        "<augment_code_snippet ",
        'path="experimental/yury/inference/benchmark_inference_server_old.py" ',
        'mode="EXCERPT">\n',
        f"{BACKTICKS[n_input_backticks]}python\n",
        f"speculation_factory=longest_overlap_lm.LongestOverlapLM,\n{BACKTICKS[n_input_backticks]}\n",
        "</augment_code_snippet>\n\n3. In ",
        "the ",
        "`experimental/yury/inference/benchmark_inference_server.py` ",
        "file:\n\n",
        "<augment_code_snippet ",
        'path="experimental/yury/inference/benchmark_inference_server.py" ',
        'mode="EXCERPT">\n',
        f"{BACKTICKS[n_input_backticks]}python\n",
        "non_neural_speculation_factory=longest_overlap_lm.LongestOverlapLM,\n",
        f"{BACKTICKS[n_input_backticks]}\n",
        "</augment_code_snippet>\n\n",
        "In ",
        "these cases, ",
        "LongestOverlapLM is ",
        "being ",
        "used as ",
        "a ",
        "draft model ",
        "or ",
        "speculation ",
        "model ",
        "in ",
        "various ",
        "neural language ",
        "model ",
        "setups ",
        "and ",
        "benchmarking ",
        "scenarios.\n",
    ]
    actual_output = "".join(list(process_fn(input_parts, n_extra_backticks)))

    expected_output = dedent(f"""\
        Based on the provided code excerpts, the LongestOverlapLM class is instantiated in a few different places:

        1. In the `experimental/yury/parallel_sd_staged_v0.ipynb`, `experimental/yury/parallel_sd_staged_v1.ipynb`, and `experimental/yury/sd_staged.ipynb` files:

        {BACKTICKS[n_extra_backticks]}python path=experimental/yury/parallel_sd_staged_v0.ipynb mode=EXCERPT
        lm = NeuralLM(tokens_budget=16, draft_model=LongestOverlapLM(12, 1, True), draft_tokens_budget=36, n_rounds=1)
        {BACKTICKS[n_extra_backticks]}

        2. In the `experimental/yury/inference/benchmark_inference_server_old.py` file:

        {BACKTICKS[n_extra_backticks]}python path=experimental/yury/inference/benchmark_inference_server_old.py mode=EXCERPT
        speculation_factory=longest_overlap_lm.LongestOverlapLM,
        {BACKTICKS[n_extra_backticks]}

        3. In the `experimental/yury/inference/benchmark_inference_server.py` file:

        {BACKTICKS[n_extra_backticks]}python path=experimental/yury/inference/benchmark_inference_server.py mode=EXCERPT
        non_neural_speculation_factory=longest_overlap_lm.LongestOverlapLM,
        {BACKTICKS[n_extra_backticks]}

        In these cases, LongestOverlapLM is being used as a draft model or speculation model in various neural language model setups and benchmarking scenarios.
        """)
    assert actual_output == expected_output


@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_mermaid_example_1(split_fn, process_fn=process_codeblocks_xml):
    # Bug report https://augment-wic8570.slack.com/archives/C07C9BULS90/p1730243866895919
    # https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/5a6b5ffc-c9ea-4c48-be13-8d3b944ca891
    input_text = dedent("""\
        Certainly! Here's a Mermaid diagram:

        <augment_code_snippet path="mermaid_diagram.md" mode="EDIT">
        ```mermaid
        graph TD
            A[Start] --> B{Is it raining?}
            B -->|Yes| C[Take an umbrella]
            B -->|No| D[Enjoy the weather]
            C --> E[Go outside]
            D --> E
            E --> F[End]
        ```
        </augment_code_snippet>
        """)
    expected_output = dedent("""\
        Certainly! Here's a Mermaid diagram:

        ````mermaid path=mermaid_diagram.md mode=EDIT
        graph TD
            A[Start] --> B{Is it raining?}
            B -->|Yes| C[Take an umbrella]
            B -->|No| D[Enjoy the weather]
            C --> E[Go outside]
            D --> E
            E --> F[End]
        ````
        """)
    actual_output = run_processor(input_text, 1, split_fn, process_fn)
    assert actual_output == expected_output


# TODO: re-enable this unit test when we can get a parsing function to handle it.
# @pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
# def test_mermaid_example_2(split_fn):
#     # Bug report https://augment-wic8570.slack.com/archives/C07C9BULS90/p1730243866895919
#     # https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/dbdfd4d2-4688-4a75-88b9-70505a49a75e
#     input_text = dedent("""\
#         Certainly! Here's a simple Mermaid script:

#         ````mermaid path=simple_flowchart.md mode=EDIT
#         graph TD
#             A[Start] --> B[Input name]
#             B --> C[Generate greeting]
#             C --> D[Display greeting]
#             D --> E[End]
#         ```
#         </augment_code_snippet>

#         This Mermaid script creates a simple flowchart that represents the flow of a greeting program. It starts with inputting a name, then generates a greeting, displays it, and ends. This corresponds to the basic structure of the Python script from the previous example.
#         """)
#     expected_output = dedent("""\
#         Certainly! Here's a simple Mermaid script:

#         ````mermaid path=simple_flowchart.md mode=EDIT
#         graph TD
#             A[Start] --> B[Input name]
#             B --> C[Generate greeting]
#             C --> D[Display greeting]
#             D --> E[End]
#         ````

#         This Mermaid script creates a simple flowchart that represents the flow of a greeting program. It starts with inputting a name, then generates a greeting, displays it, and ends. This corresponds to the basic structure of the Python script from the previous example.
#         """)
#     actual_output = run_processor(input_text, 1, split_fn)
#     assert actual_output == expected_output


def test_dogfood_example(process_fn=process_codeblocks_xml):
    input_parts = [
        "I",
        "'ll",
        " help",
        " you reverse the order",
        " of the imports.",
        " Here's the modifie",
        "d version",
        ":\n\n",
        "<aug",
        "ment",
        "_code_",
        "snippet",
        " path",
        '="research',
        "/core",
        "/str",
        "_diff.py",
        '"',
        " mode",
        '="',
        "EDIT",
        '">\n```',
        "python\nfrom base",
        ".diff_utils",
        ".str_diff",
        " import (\n    ",
        "precise",
        "_line",
        "_diff,",
        "\n    precise_char",
        "_diff,",
        "\n    line_diff",
        ",\n    cleanup",
        "_diff_spans",
        ",\n    ",
        "StrDiff,",
        "\n    Noop",
        "Span,",
        "\n    ModSpan",
        ",\n    D",
        "iffSpan,",
        "\n    Delete",
        "dSpan,",
        "\n    Adde",
        "dSpan,",
        "\n)\n```",
        "\n</augment_",
        "code_snippet>",
        "\n",
    ]
    actual_output = "".join(list(process_fn(input_parts, 1)))

    expected_output = dedent("""\
        I'll help you reverse the order of the imports. Here's the modified version:

        ````python path=research/core/str_diff.py mode=EDIT
        from base.diff_utils.str_diff import (
            precise_line_diff,
            precise_char_diff,
            line_diff,
            cleanup_diff_spans,
            StrDiff,
            NoopSpan,
            ModSpan,
            DiffSpan,
            DeletedSpan,
            AddedSpan,
        )
        ````
        """)

    assert actual_output == expected_output


def test_dogfood_example_2(process_fn=process_codeblocks_xml):
    input_parts = [
        "I'll ",
        "help you reverse the ",
        "order of the ",
        "imports. Here's the ",
        "modified ",
        "version:\n\n",
        "<augment_code_snippet ",
        'path="research/core/str_diff.py" ',
        'mode="EDIT">\n',
        "```python\nfrom ",
        "base.diff_utils.str_diff import (\n    ",
        "precise_line_diff,\n    ",
        "precise_char_diff,\n    ",
        "line_diff,\n    ",
        "cleanup_diff_spans,\n    ",
        "StrDiff,\n    ",
        "NoopSpan,\n    ",
        "ModSpan,\n    ",
        "DiffSpan,\n    ",
        "DeletedSpan,\n    ",
        "AddedSpan,\n)\n",
        "```\n",
        "</augment_code_snippet>\n",
    ]
    actual_output = "".join(list(process_fn(input_parts, 1)))

    expected_output = dedent("""\
        I'll help you reverse the order of the imports. Here's the modified version:

        ````python path=research/core/str_diff.py mode=EDIT
        from base.diff_utils.str_diff import (
            precise_line_diff,
            precise_char_diff,
            line_diff,
            cleanup_diff_spans,
            StrDiff,
            NoopSpan,
            ModSpan,
            DiffSpan,
            DeletedSpan,
            AddedSpan,
        )
        ````
        """)

    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_dogfood_example_3(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        First, install numpy:

        ```
        pip install numpy
        ```

        <augment_code_snippet path="numpy.py" mode="EDIT">
        {BACKTICKS[n_input_backticks]}python
        import numpy
        print(np.random.random())
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        """)

    expected_output = dedent(f"""\
        First, install numpy:

        ```
        pip install numpy
        ```

        {BACKTICKS[n_extra_backticks]}python path=numpy.py mode=EDIT
        import numpy
        print(np.random.random())
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


# Tests for <code_snippet> tag support


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_code_snippet_basic_1(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        <code_snippet path="foo/bar.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_input_backticks]}
        </code_snippet>

        Let me know if you have any other questions!
        """)
    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python path=foo/bar.py mode=EXCERPT
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_extra_backticks]}

        Let me know if you have any other questions!
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_code_snippet_basic_2(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <code_snippet path="config/app_config.yaml" mode="EDIT">
        {BACKTICKS[n_input_backticks]}yaml
        app:
          name: MyApp
          version: 1.0.0
        {BACKTICKS[n_input_backticks]}
        </code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}yaml path=config/app_config.yaml mode=EDIT
        app:
          name: MyApp
          version: 1.0.0
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_code_snippet_without_attributes(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    input_text = dedent(f"""\
        <code_snippet>
        {BACKTICKS[n_input_backticks]}python
        def hello():
            print("Hello, World!")
        {BACKTICKS[n_input_backticks]}
        </code_snippet>
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python
        def hello():
            print("Hello, World!")
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output


@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_mixed_tags(
    n_input_backticks, n_extra_backticks, split_fn, process_fn=process_codeblocks_xml
):
    """Test that both <augment_code_snippet> and <code_snippet> tags work in the same text."""
    input_text = dedent(f"""\
        First, here's an augment code snippet:

        <augment_code_snippet path="old/file.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
        def old_function():
            pass
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>

        And here's a regular code snippet:

        <code_snippet path="new/file.py" mode="EDIT">
        {BACKTICKS[n_input_backticks]}python
        def new_function():
            pass
        {BACKTICKS[n_input_backticks]}
        </code_snippet>
        """)
    expected_output = dedent(f"""\
        First, here's an augment code snippet:

        {BACKTICKS[n_extra_backticks]}python path=old/file.py mode=EXCERPT
        def old_function():
            pass
        {BACKTICKS[n_extra_backticks]}

        And here's a regular code snippet:

        {BACKTICKS[n_extra_backticks]}python path=new/file.py mode=EDIT
        def new_function():
            pass
        {BACKTICKS[n_extra_backticks]}
        """)
    actual_output = run_processor(input_text, n_extra_backticks, split_fn, process_fn)
    assert actual_output == expected_output
