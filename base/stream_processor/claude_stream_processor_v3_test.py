from textwrap import dedent
from typing import Iterable

import pytest
from base.prompt_format.common import StopReason
from base.stream_processor.basic_stream_processor import (
    StreamProcessorOutput,
    StreamProcessorOutputType,
)
from base.stream_processor.claude_stream_processor_v3 import ClaudeStreamProcessorV3
from base.stream_processor.codeblocks_xml_stream_processor_test import (
    BACKTICKS,
    SPLIT_FUNCTIONS,
    test_basic_1,
    test_basic_2,
    test_basic_3,
    test_basic_4,
    test_empty_codeblock,
    test_nested_codeblocks_2,
    test_indented_codeblock,
    test_no_language_hint,
    test_codeblock_without_xml_wrapper,
    test_no_path_mode_attributes,
    test_no_closing_tag,
    test_two_codeblocks,
    test_closing_tag_with_leading_extra_whitespaces,
    test_no_double_quotes_around_attributes,
    test_xml_tags_in_the_middle_of_codeblock,
    test_tag_inside_codeblock,
    test_opening_tag_with_leading_extra_whitespaces,
    test_opening_tag_split_into_chunks,
    test_long_answer,
    test_mermaid_example_1,
    test_dogfood_example,
    test_dogfood_example_2,
    test_dogfood_example_3,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ThirdPartyModelResponse,
    ToolUseResponse,
    ToolUseStart,
    PromptCacheUsage,
)

test_functions = [
    test_basic_1,
    test_basic_2,
    test_basic_3,
    test_basic_4,
    test_empty_codeblock,
    test_nested_codeblocks_2,
    test_indented_codeblock,
    test_no_language_hint,
    test_codeblock_without_xml_wrapper,
    test_no_path_mode_attributes,
    test_no_closing_tag,
    test_two_codeblocks,
    test_closing_tag_with_leading_extra_whitespaces,
    test_no_double_quotes_around_attributes,
    test_xml_tags_in_the_middle_of_codeblock,
    test_tag_inside_codeblock,
    test_opening_tag_with_leading_extra_whitespaces,
    test_opening_tag_split_into_chunks,
    test_long_answer,
    test_mermaid_example_1,
    test_dogfood_example,
    test_dogfood_example_2,
    test_dogfood_example_3,
]


def process_v3(lb_substr: Iterable[str], n_extra_backticks: int = 0) -> Iterable[str]:
    """A wrapper function for testing purposes."""
    lb_substr_wrapped = [ThirdPartyModelResponse(text) for text in lb_substr]
    processor = ClaudeStreamProcessorV3(n_extra_backticks=n_extra_backticks)
    for response in processor.process_stream(lb_substr_wrapped):
        yield response.text


def process_v3_wrapped(
    lb_substr: Iterable[str], n_extra_backticks: int = 0
) -> tuple[str, list[StreamProcessorOutput]]:
    """A wrapper function for testing purposes."""
    lb_substr_wrapped = [ThirdPartyModelResponse(text) for text in lb_substr]
    processor = ClaudeStreamProcessorV3(n_extra_backticks=n_extra_backticks)

    text_part = ""
    other_result = []
    for response in processor.process_stream(lb_substr_wrapped):
        if response.type == StreamProcessorOutputType.ANSWER:
            text_part += response.text
        else:
            other_result.append(response)
    return text_part, other_result


for test_function in test_functions:
    if test_function in [
        test_opening_tag_with_leading_extra_whitespaces,
        test_opening_tag_split_into_chunks,
        test_long_answer,
    ]:

        def custom_test(n_input_backticks, n_extra_backticks, test_fn=test_function):  # type: ignore
            test_fn(n_input_backticks, n_extra_backticks, process_fn=process_v3)
    elif test_function in [test_mermaid_example_1]:

        def custom_test(split_fn, test_fn=test_function):  # type: ignore
            test_fn(split_fn, process_fn=process_v3)
    elif test_function in [test_dogfood_example, test_dogfood_example_2]:

        def custom_test(test_fn=test_function):  # type: ignore
            test_fn(process_fn=process_v3)
    else:

        def custom_test(
            n_input_backticks, n_extra_backticks, split_fn, test_fn=test_function
        ):
            test_fn(
                n_input_backticks, n_extra_backticks, split_fn, process_fn=process_v3
            )

    custom_test.__name__ = f"{test_function.__name__}_custom"
    globals()[custom_test.__name__] = custom_test
    if hasattr(test_function, "pytestmark"):
        globals()[custom_test.__name__].pytestmark = test_function.pytestmark


@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
@pytest.mark.parametrize("trailing_text", ["", "\nsome_text_after\n"])
def test_suggested_question_basic(split_fn, trailing_text):
    input_text = (
        dedent("""\
        Some text...
        line1
        <guess_of_next_user_question>
            <next_user_question>QUESTION 1</next_user_question>
            <next_user_question>QUESTION 2
            WITH MULTIPLE LINES</next_user_question>
        </guess_of_next_user_question>""")
        + trailing_text
    )

    if trailing_text.startswith("\n"):
        # This \n is consumed by "</guess_of_next_user_question>\n" regexp
        trailing_text = trailing_text[1:]

    expected_output = (
        dedent("""\
        Some text...
        line1
        """)
        + trailing_text
    )
    text_output, other_output = process_v3_wrapped(split_fn(input_text), 0)
    assert text_output == expected_output
    assert len(other_output) == 1
    assert other_output[0] == StreamProcessorOutput(
        "QUESTION 1\nQUESTION 2     WITH MULTIPLE LINES",
        StreamProcessorOutputType.SUGGESTED_QUESTIONS,
    )


@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
def test_model_messed_up_in_suggested_questions(split_fn):
    """Test that we don't show garbage to user if model messed up."""
    input_text = dedent("""\
        Some text...
        line1
        <guess_of_next_user_question>
            some
            nonsencical
                text
        </guess_of_next_user_question>""")

    expected_output = dedent("""\
        Some text...
        line1
        """)
    text_output, other_output = process_v3_wrapped(split_fn(input_text), 0)
    assert text_output == expected_output
    assert len(other_output) == 0


@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
@pytest.mark.parametrize("ends_with_newline", [True, False])
@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
def test_codeblock_at_the_end(
    split_fn, ends_with_newline, n_input_backticks, n_extra_backticks
):
    input_text = dedent(f"""\
        This is the relevant excerpt from your code:

        <augment_code_snippet path="foo/bar.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>""")
    if ends_with_newline:
        input_text += "\n"

    expected_output = dedent(f"""\
        This is the relevant excerpt from your code:

        {BACKTICKS[n_extra_backticks]}python path=foo/bar.py mode=EXCERPT
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_extra_backticks]}
        """)

    actual_output = "".join(list(process_v3(split_fn(input_text), n_extra_backticks)))
    assert actual_output == expected_output


@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
def test_codeblock_at_the_beginning(split_fn, n_input_backticks, n_extra_backticks):
    input_text = dedent(f"""\
        <augment_code_snippet path="foo/bar.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        ...
        """)
    expected_output = dedent(f"""\
        {BACKTICKS[n_extra_backticks]}python path=foo/bar.py mode=EXCERPT
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_extra_backticks]}
        ...
        """)
    actual_output = "".join(list(process_v3(split_fn(input_text), n_extra_backticks)))
    assert actual_output == expected_output


@pytest.mark.parametrize("split_fn", SPLIT_FUNCTIONS)
@pytest.mark.parametrize("n_input_backticks", [0, 1])
@pytest.mark.parametrize("n_extra_backticks", [0, 1, 2])
def test_augment_tag_injection(split_fn, n_input_backticks, n_extra_backticks):
    input_text = dedent(f"""\
        Ok! I'll wrap my response in `<augment_code_snippet>` tag!
        I'll provide arguments like this: <augment_code_snippet path=foo/bar.py mode=EXCERPT>

        <augment_code_snippet path="foo/bar.py" mode="EXCERPT">
        {BACKTICKS[n_input_backticks]}python
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_input_backticks]}
        </augment_code_snippet>
        ...
        """)
    expected_output = dedent(f"""\
        Ok! I'll wrap my response in `<augment_code_snippet>` tag!
        I'll provide arguments like this: <augment_code_snippet path=foo/bar.py mode=EXCERPT>

        {BACKTICKS[n_extra_backticks]}python path=foo/bar.py mode=EXCERPT
        class AbstractTokenizer():
            def __init__(self, name):
                self.name = name

            ...
        {BACKTICKS[n_extra_backticks]}
        ...
        """)

    actual_output = "".join(list(process_v3(split_fn(input_text), n_extra_backticks)))
    assert actual_output == expected_output


@pytest.mark.parametrize("n_extra_backticks", [2])
def test_tools(n_extra_backticks):
    stream = [
        ThirdPartyModelResponse(text="Hi!"),
        ThirdPartyModelResponse(text="Bye!"),
        ThirdPartyModelResponse(
            text="This will be a tool use.",
        ),
        ThirdPartyModelResponse(
            text="",
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_name="my_tool_name",
                input={"tool_param": "tool_value999"},
                tool_use_id="tool_use_id-111",
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(
                stop_reason=StopReason.TOOL_USE_REQUESTED,
                output_tokens=100,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=200,
                    cache_read_input_tokens=50,
                    cache_creation_input_tokens=150,
                ),
            ),
        ),
    ]

    """A wrapper function for testing purposes."""
    processor = ClaudeStreamProcessorV3(n_extra_backticks=n_extra_backticks)

    text_output = ""
    other_result = []
    for response in processor.process_stream(stream):
        if response.type == StreamProcessorOutputType.ANSWER:
            text_output += response.text
        else:
            other_result.append(response)

    assert text_output == "Hi!Bye!This will be a tool use.\n"
    assert other_result == [
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL_START,
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        ),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL,
            tool_use=ToolUseResponse(
                "my_tool_name",
                {"tool_param": "tool_value999"},
                "tool_use_id-111",
            ),
        ),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.END_OF_STREAM,
            end_of_stream=EndOfStream(
                stop_reason=StopReason.TOOL_USE_REQUESTED,
                output_tokens=100,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=200,
                    cache_read_input_tokens=50,
                    cache_creation_input_tokens=150,
                ),
            ),
        ),
    ]


@pytest.mark.parametrize("include_tool_start", [False, True])
def test_single_tool_use_only(include_tool_start):
    # no text generated, only a single tool use
    # (this is a regression test for a bug we had)
    stream = [
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_name="my_tool_name",
                input={"tool_param": "tool_value999"},
                tool_use_id="tool_use_id-111",
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(
                stop_reason=StopReason.TOOL_USE_REQUESTED,
                output_tokens=1,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=2,
                    cache_read_input_tokens=5,
                    cache_creation_input_tokens=15,
                ),
            ),
        ),
    ]
    if include_tool_start:
        stream.insert(
            0,
            ThirdPartyModelResponse(
                text="",
                tool_use_start=ToolUseStart(
                    tool_name="my_tool_name",
                    tool_use_id="tool_use_id-111",
                ),
            ),
        )

    """A wrapper function for testing purposes."""
    processor = ClaudeStreamProcessorV3(n_extra_backticks=2)

    text_output = ""
    other_result = []
    for response in processor.process_stream(stream):
        if response.type == StreamProcessorOutputType.ANSWER:
            text_output += response.text
        else:
            other_result.append(response)

    assert text_output == ""
    assert len(other_result) == 2 + include_tool_start
    if include_tool_start:
        assert other_result.pop(0) == StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL_START,
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        )
    assert other_result == [
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL,
            tool_use=ToolUseResponse(
                "my_tool_name",
                {"tool_param": "tool_value999"},
                "tool_use_id-111",
            ),
        ),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.END_OF_STREAM,
            end_of_stream=EndOfStream(
                stop_reason=StopReason.TOOL_USE_REQUESTED,
                output_tokens=1,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=2,
                    cache_read_input_tokens=5,
                    cache_creation_input_tokens=15,
                ),
            ),
        ),
    ]


def test_only_end_of_stream():
    # No text, no tool, no problem
    stream = [
        ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(
                stop_reason=StopReason.REASON_UNSPECIFIED,
                output_tokens=3,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=200,
                    cache_read_input_tokens=50,
                    cache_creation_input_tokens=150,
                ),
            ),
        ),
    ]

    processor = ClaudeStreamProcessorV3(n_extra_backticks=2)
    out_stream = processor.process_stream(stream)
    assert list(out_stream) == [
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.END_OF_STREAM,
            end_of_stream=EndOfStream(
                stop_reason=StopReason.REASON_UNSPECIFIED,
                output_tokens=3,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=200,
                    cache_read_input_tokens=50,
                    cache_creation_input_tokens=150,
                ),
            ),
        ),
    ]
