from base.stream_processor.claude_with_citations_stream_processor import (
    ClaudeStreamProcessor,
    StreamProcessorOutput,
    StreamProcessorOutputType,
)
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse,
    PromptCacheUsage,
)


def test_basic():
    # Test basic input as sanity check.
    processor = ClaudeStreamProcessor()
    assert processor.get_stream_history() == ""
    stream = [
        ThirdPartyModelResponse("""
<augment-answer>Hi! My name is <PERSON></augment-answer>
<augment-citation>/path/to/fileA.py</augment-citation>
<augment-citation>/path/to/fileB.py</augment-citation>
""")
    ]
    assert list(processor.process_stream(stream)) == [
        StreamProcessorOutput("Hi! My name is <PERSON>", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("/path/to/fileA.py", StreamProcessorOutputType.CITATION),
        StreamProcessorOutput("/path/to/fileB.py", StreamProcessorOutputType.CITATION),
    ]

    # Random text between sections shouldn't affect output.
    processor = ClaudeStreamProcessor()
    assert processor.get_stream_history() == ""
    stream = [
        ThirdPartyModelResponse("""
blah blah
<augment-answer>Hi! My name is Colin</augment-answer>
blah blah
<augment-citation>/path/to/fileA.py</augment-citation>
blah blah
<augment-citation>/path/to/fileB.py</augment-citation>
blah blah
""")
    ]
    assert list(processor.process_stream(stream)) == [
        StreamProcessorOutput("Hi! My name is Colin", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("/path/to/fileA.py", StreamProcessorOutputType.CITATION),
        StreamProcessorOutput("/path/to/fileB.py", StreamProcessorOutputType.CITATION),
    ]

    # Neatly broke-up chunks works (with some random code in between for extra robustness check).
    processor = ClaudeStreamProcessor()
    assert processor.get_stream_history() == ""
    stream = [
        ThirdPartyModelResponse(
            "<augment-answer>Hi! My name is Colin</augment-answer>blahblah"
        ),
        ThirdPartyModelResponse(
            "<augment-citation>/path/to/fileA.py</augment-citation>blahblahblah"
        ),
        ThirdPartyModelResponse(
            "<augment-citation>/path/to/fileB.py</augment-citation>blahblahblahblah"
        ),
    ]
    assert list(processor.process_stream(stream)) == [
        StreamProcessorOutput("Hi! My name is ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("Colin", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("/path/to/fileA.py", StreamProcessorOutputType.CITATION),
        StreamProcessorOutput("/path/to/fileB.py", StreamProcessorOutputType.CITATION),
    ]


def test_breaking_up_chunks():
    # Put breaks inbetween in answers and citations
    processor = ClaudeStreamProcessor()
    assert processor.get_stream_history() == ""
    stream = [
        ThirdPartyModelResponse("<augment-answer>Hi! My name"),
        ThirdPartyModelResponse(" is Colin"),
        ThirdPartyModelResponse("</augment-answer>blahblah"),
        ThirdPartyModelResponse("<augment-citation>/path/"),
        ThirdPartyModelResponse("to/"),
        ThirdPartyModelResponse("fileA.py</augment-citation>blahblahblah"),
        ThirdPartyModelResponse(
            "<augment-citation>/path/to/fileB.py</augment-citation>blahblahblahblah"
        ),
    ]
    assert list(processor.process_stream(stream)) == [
        StreamProcessorOutput("Hi! My ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("name is ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("Colin", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("/path/to/fileA.py", StreamProcessorOutputType.CITATION),
        StreamProcessorOutput("/path/to/fileB.py", StreamProcessorOutputType.CITATION),
    ]

    # Put breaks in the middle of XML tags
    processor = ClaudeStreamProcessor()
    assert processor.get_stream_history() == ""
    stream = [
        ThirdPartyModelResponse("<augment-a"),
        ThirdPartyModelResponse("nswer>Hi! My name"),
        ThirdPartyModelResponse(" is Colin"),
        ThirdPartyModelResponse("</augmen"),
        ThirdPartyModelResponse("t-answer>blahblah"),
        ThirdPartyModelResponse("<"),
        ThirdPartyModelResponse("augment-citation>/path/"),
        ThirdPartyModelResponse("to/"),
        ThirdPartyModelResponse("fileA.py</augment-citation"),
        ThirdPartyModelResponse(">"),
        ThirdPartyModelResponse(
            "blahblahblah<augment-citation>/path/to/fileB.py</augment-citation>blahblahblahblah"
        ),
    ]
    assert list(processor.process_stream(stream)) == [
        StreamProcessorOutput("Hi! My ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("name is ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("Colin", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("/path/to/fileA.py", StreamProcessorOutputType.CITATION),
        StreamProcessorOutput("/path/to/fileB.py", StreamProcessorOutputType.CITATION),
    ]
