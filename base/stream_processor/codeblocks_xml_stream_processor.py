"""A stream processing for codeblocks metadata in XML format.

Converts

<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
```python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
```
</augment_code_snippet>

or

<code_snippet path="foo/bar.py" mode="EXCERPT">
```python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
```
</code_snippet>

into

```python path=foo/bar.py mode=EXCERPT
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
```
"""

from enum import Enum

# Relevant documentation is here https://pypi.org/project/regex/ (really hard to Google!)
import regex as re
from typing import Generator, Iterable


OPEN_CODEBLOCK_TAG_PREFIX = "<augment_code_snippet"
CLOSE_CODEBLOCK = "```\n"
CLOSE_CODEBLOCK_TAG = "</augment_code_snippet>\n"

# Patterns for both <augment_code_snippet> and <code_snippet> tags
OPEN_TAG_THEN_BACKTICKS_PATTERN = re.compile(
    r'[^\S\n]*<(?:augment_code_snippet|code_snippet)(?:\s+path=(?:"?)(.*?)(?:"?))?(?:\s+mode=(?:"?)(.*?)(?:"?))?>\n'
    r"([^\S\n]*)`{3,}([^\n]*)\n"
)
BACKTICKS_THEN_OPEN_TAG_PATTERN = re.compile(
    r"([^\S\n]*)`{3,}([^\n]*)\n"
    r'[^\S\n]*<(?:augment_code_snippet|code_snippet)(?:\s+path=(?:"?)(.*?)(?:"?))?(?:\s+mode=(?:"?)(.*?)(?:"?))?>[^\S\n]*\n'
)
BACKTICKS_THEN_CLOSE_TAG_PATTERN = re.compile(
    r"([^\S\n]*)`{3,}\n" r"[^\S\n]*</(?:augment_code_snippet|code_snippet)>\n"
)
CLOSE_TAG_THEN_BACKTICKSPATTERN = re.compile(
    r"[^\S\n]*</(?:augment_code_snippet|code_snippet)>\n" r"([^\S\n]*)`{3,}\n"
)


class ProcessingState(Enum):
    """The state of the streaming processor."""

    NONE = 0
    CODEBLOCK_BODY = 1


def adjust_open_codeblock(
    leading_whitespaces: str | None,
    path: str | None,
    mode: str | None,
    language: str | None,
    n_extra_backticks: int = 0,
):
    """Adjusts the opening codeblock."""
    adjusted_text = []
    if leading_whitespaces is not None:
        adjusted_text.append(leading_whitespaces)
    adjusted_text.append("```" + "`" * n_extra_backticks)
    if language is not None:
        adjusted_text.append(language)
    if path is not None:
        adjusted_text.append(f" path={path}")
    if mode is not None:
        adjusted_text.append(f" mode={mode}")
    adjusted_text.append("\n")
    return "".join(adjusted_text)


def adjust_close_codeblock(leading_whitespaces: str | None, n_extra_backticks: int = 0):
    """Adjusts the closing codeblock."""
    adjusted_text = []
    if leading_whitespaces is not None:
        adjusted_text.append(leading_whitespaces)
    adjusted_text.append("```" + "`" * n_extra_backticks + "\n")
    return "".join(adjusted_text)


def step(
    buffer: str, state: ProcessingState, n_extra_backticks: int = 0
) -> tuple[str, ProcessingState, str]:
    """Processes a substring of the stream."""
    empty_buffer, empty_text = "", ""
    if state == ProcessingState.NONE:
        # We are not in any state, so we expect the opening XML tag,
        # one a separate line, potentially with leading whitespaces.
        match1 = re.fullmatch(OPEN_TAG_THEN_BACKTICKS_PATTERN, buffer, partial=True)
        no_match1 = match1 is None or match1.end() == match1.start()
        match1_partial = match1 is not None and match1.partial and not no_match1
        match2 = re.fullmatch(BACKTICKS_THEN_OPEN_TAG_PATTERN, buffer, partial=True)
        no_match2 = match2 is None or match2.end() == match2.start()
        match2_partial = match2 is not None and match2.partial and not no_match2

        if no_match1 and no_match2:
            return empty_buffer, ProcessingState.NONE, buffer
        elif match1_partial or match2_partial:
            # We are still in the process of generating the open tag.
            return (
                buffer,
                ProcessingState.NONE,
                empty_text,
            )
        elif match1 is not None and not match1.partial:
            adjusted_text = adjust_open_codeblock(
                leading_whitespaces=match1.group(3),
                path=match1.group(1),
                mode=match1.group(2),
                language=match1.group(4),
                n_extra_backticks=n_extra_backticks,
            )
            return (
                buffer[match1.end() :],
                ProcessingState.CODEBLOCK_BODY,
                buffer[: match1.start()] + adjusted_text,
            )
        elif match2 is not None and not match2.partial:
            adjusted_text = adjust_open_codeblock(
                leading_whitespaces=match2.group(1),
                path=match2.group(3),
                mode=match2.group(4),
                language=match2.group(2),
                n_extra_backticks=n_extra_backticks,
            )
            return (
                buffer[match2.end() :],
                ProcessingState.CODEBLOCK_BODY,
                buffer[: match2.start()] + adjusted_text,
            )
        else:
            raise ValueError(f"Invalid state for buffer: {buffer}")

    elif state == ProcessingState.CODEBLOCK_BODY:
        match1 = re.fullmatch(BACKTICKS_THEN_CLOSE_TAG_PATTERN, buffer, partial=True)
        no_match1 = match1 is None or match1.end() == match1.start()
        match2 = re.fullmatch(CLOSE_TAG_THEN_BACKTICKSPATTERN, buffer, partial=True)
        no_match2 = match2 is None or match2.end() == match2.start()

        if no_match1 and no_match2:
            return empty_buffer, ProcessingState.CODEBLOCK_BODY, buffer
        elif (match1 is None or match1.partial) and (match2 is None or match2.partial):
            return (
                buffer,
                ProcessingState.CODEBLOCK_BODY,
                empty_text,
            )
        else:
            if match1 is not None and not match1.partial:
                match = match1
            elif match2 is not None and not match2.partial:
                match = match2
            else:
                raise ValueError(f"Invalid state for buffer: {buffer}")

            adjusted_text = adjust_close_codeblock(
                leading_whitespaces=match.group(1),
                n_extra_backticks=n_extra_backticks,
            )
            return (
                buffer[match.end() :],
                ProcessingState.NONE,
                buffer[: match.start()] + adjusted_text,
            )
    else:
        raise ValueError(f"Invalid state: {state}")


def co_process_codeblocks_xml(
    n_extra_backticks: int = 0,
) -> Generator[str, str | None, None]:
    """A couroutine that processes the codeblocks in XML format."""
    state = ProcessingState.NONE
    buffer = ""
    output = ""
    while True:
        text = yield output
        if text is None:
            break
        output = []
        for adjust_text in text.splitlines(keepends=True):
            buffer += adjust_text
            buffer, state, current_output = step(buffer, state, n_extra_backticks)
            output.append(current_output)
        output = "".join(output)

    # The buffer should not be empty unless something is broken.
    # But it's best to recover gracefully, so we return the buffer back.
    if len(buffer) > 0:
        yield buffer


def process_codeblocks_xml(
    lb_substr: Iterable[str], n_extra_backticks: int = 0
) -> Iterable[str]:
    """A wrapper function for testing purposes."""
    processor = co_process_codeblocks_xml(n_extra_backticks=n_extra_backticks)
    next(processor)  # Prime the coroutine
    yield from (processor.send(text) for text in lb_substr)
    try:
        output = processor.send(None)  # Signal the end of input
        if output is not None:
            yield output
    except StopIteration:
        pass
