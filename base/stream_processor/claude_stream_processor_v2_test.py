from typing import List
from base.stream_processor.basic_stream_processor import (
    StreamProcessorOutput,
    StreamProcessorOutputType,
)
from base.stream_processor.claude_stream_processor_v2 import ClaudeStreamProcessorV2
from base.third_party_clients.third_party_model_client import (
    ReplaceTextResponse,
    ThirdPartyModelResponse,
    ToolUseResponse,
)


def _create_responses(texts: List[str]) -> List[ThirdPartyModelResponse]:
    """
    Creates a list of ThirdPartyModelResponse objects from a given list of texts.
    Each text string becomes a separate ThirdPartyModelResponse.
    """
    return [ThirdPartyModelResponse(text) for text in texts]


def _create_answer_outputs(texts: List[str]) -> List[StreamProcessorOutput]:
    """
    Creates a list of StreamProcessorOutput objects from a given list of texts.
    Each text string becomes a separate StreamProcessorOutput with StreamProcessorOutputType.ANSWER.
    """
    return [
        StreamProcessorOutput(text, StreamProcessorOutputType.ANSWER) for text in texts
    ]


def _get_stream_processor() -> ClaudeStreamProcessorV2:
    """
    Returns a new instance of ClaudeStreamProcessorV2.
    """
    return ClaudeStreamProcessorV2()


def test_basic():
    """Tests that the stream processor returns None when there is no result."""

    # Test basic functionality
    stream_processor = _get_stream_processor()
    stream = _create_responses(["Hi!", "You seem cool and"])
    assert list(stream_processor.process_stream(stream)) == _create_answer_outputs(
        [
            "Hi!You seem cool ",
            "and",
        ]
    )
    assert stream_processor.get_stream_history() == "Hi!You seem cool and"

    # Test no spaces edge case
    stream_processor = _get_stream_processor()
    assert list(
        stream_processor.process_stream(_create_responses(["Hi! "]))
    ) == _create_answer_outputs(["Hi! "])
    assert stream_processor.get_stream_history() == "Hi! "
    stream_processor = _get_stream_processor()
    assert list(
        stream_processor.process_stream(_create_responses(["Hi!"]))
    ) == _create_answer_outputs(["Hi!"])
    assert stream_processor.get_stream_history() == "Hi!"

    # Test empty input
    stream_processor = _get_stream_processor()
    assert list(stream_processor.process_stream(_create_responses([]))) == []
    assert stream_processor.get_stream_history() == ""
    stream_processor = _get_stream_processor()
    assert list(stream_processor.process_stream(_create_responses([""]))) == []
    assert stream_processor.get_stream_history() == ""
    stream_processor = _get_stream_processor()
    assert list(
        stream_processor.process_stream(_create_responses([" "]))
    ) == _create_answer_outputs(["", " "])
    assert stream_processor.get_stream_history() == " "

    # Test tools
    stream_processor = _get_stream_processor()
    example_tool_resp = ReplaceTextResponse(
        replacement_text="I'm a tool repl",
        start_line_number=1,
        end_line_number=3,
        old_text=None,
        sequence_id=0,
    )
    stream = [
        ThirdPartyModelResponse(text="Hi!"),
        ThirdPartyModelResponse(
            text="I'm a tool", replace_text_response=example_tool_resp
        ),
        ThirdPartyModelResponse(text="You seem cool and"),
    ]
    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput(
            "I'm a tool", StreamProcessorOutputType.TOOL, example_tool_resp
        ),
        StreamProcessorOutput("Hi!You seem cool ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("and", StreamProcessorOutputType.ANSWER),
    ]
    assert stream_processor.get_stream_history() == "Hi!I'm a toolYou seem cool and"

    # Test general tool use
    stream_processor = _get_stream_processor()
    stream = [
        ThirdPartyModelResponse(text="Hi!"),
        ThirdPartyModelResponse(text="Bye!"),
        ThirdPartyModelResponse(
            text="This will be a tool use.Hello",
        ),
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_name="my_tool_name",
                input={"tool_param": "tool_value999"},
                tool_use_id="tool_use_id-111",
            ),
        ),
    ]

    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput(
            "Hi!Bye!This will be a tool use.", StreamProcessorOutputType.ANSWER
        ),
        StreamProcessorOutput("Hello", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL,
            tool_use=ToolUseResponse(
                "my_tool_name",
                {"tool_param": "tool_value999"},
                "tool_use_id-111",
            ),
        ),
    ]
    assert (
        stream_processor.get_stream_history() == "Hi!Bye!This will be a tool use.Hello"
    )

    stream = [
        ThirdPartyModelResponse(text="Case with trailing space "),
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_name="my_tool_name2",
                input={"tool_param": "tool_value888"},
                tool_use_id="tool_use_id-222",
            ),
        ),
    ]
    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput(
            "Case with trailing space ", StreamProcessorOutputType.ANSWER
        ),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL,
            tool_use=ToolUseResponse(
                "my_tool_name2",
                {"tool_param": "tool_value888"},
                "tool_use_id-222",
            ),
        ),
    ]
    assert (
        stream_processor.get_stream_history()
        == "Hi!Bye!This will be a tool use.HelloCase with trailing space "
    )
