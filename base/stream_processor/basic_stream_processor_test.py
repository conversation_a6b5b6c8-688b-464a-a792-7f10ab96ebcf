import pytest

from base.stream_processor.basic_stream_processor import (
    BasicStreamProcessor,
    StreamProcessor,
    StreamProcessorOutput,
    StreamProcessorOutputType,
)
from base.third_party_clients.third_party_model_client import (
    ReplaceTextResponse,
    ThirdPartyModelResponse,
    ToolUseResponse,
    PromptCacheUsage,
    ToolUseStart,
    EndOfStream,
)
from base.prompt_format.common import StopReason


def _get_stream_processor() -> StreamProcessor:
    return BasicStreamProcessor()


def test_basic():
    """Tests that the stream processor returns None when there is no result."""

    # Test basic functionality
    stream_processor = _get_stream_processor()
    stream = [
        ThirdPartyModelResponse(text="Hi!"),
        ThirdPartyModelResponse(text="You seem cool and"),
    ]
    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput("Hi!You seem cool ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("and", StreamProcessorOutputType.ANSWER),
    ]
    assert stream_processor.get_stream_history() == "Hi!You seem cool and"


def test_no_spaces():
    # Test no spaces edge case
    stream_processor = _get_stream_processor()
    assert list(
        stream_processor.process_stream([ThirdPartyModelResponse(text="Hi! ")])
    ) == [StreamProcessorOutput("Hi! ", StreamProcessorOutputType.ANSWER)]
    assert stream_processor.get_stream_history() == "Hi! "
    stream_processor = _get_stream_processor()
    assert list(
        stream_processor.process_stream([ThirdPartyModelResponse(text="Hi!")])
    ) == [StreamProcessorOutput("Hi!", StreamProcessorOutputType.ANSWER)]
    assert stream_processor.get_stream_history() == "Hi!"


def test_empty_input():
    # Test empty input
    stream_processor = _get_stream_processor()
    assert list(stream_processor.process_stream([])) == []
    assert stream_processor.get_stream_history() == ""
    stream_processor = _get_stream_processor()
    assert (
        list(stream_processor.process_stream([ThirdPartyModelResponse(text="")])) == []
    )
    assert stream_processor.get_stream_history() == ""
    stream_processor = _get_stream_processor()
    assert list(
        stream_processor.process_stream([ThirdPartyModelResponse(text=" ")])
    ) == [StreamProcessorOutput(" ", StreamProcessorOutputType.ANSWER)]
    assert stream_processor.get_stream_history() == " "


def test_tools():
    # Test tools
    stream_processor = _get_stream_processor()
    example_tool_resp = ReplaceTextResponse(
        replacement_text="I'm a tool repl",
        start_line_number=1,
        end_line_number=3,
        old_text=None,
        sequence_id=0,
    )
    stream = [
        ThirdPartyModelResponse(text="Hi!"),
        ThirdPartyModelResponse(
            text="I'm a tool", replace_text_response=example_tool_resp
        ),
        ThirdPartyModelResponse(text="You seem cool and"),
    ]
    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput(
            "I'm a tool", StreamProcessorOutputType.TOOL, example_tool_resp
        ),
        StreamProcessorOutput("Hi!You seem cool ", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput("and", StreamProcessorOutputType.ANSWER),
    ]
    assert stream_processor.get_stream_history() == "Hi!I'm a toolYou seem cool and"


def test_tool_use_streaming():
    # Test general tool use
    stream_processor = _get_stream_processor()
    stream = [
        ThirdPartyModelResponse(text="Hi!"),
        ThirdPartyModelResponse(text="Bye!"),
        ThirdPartyModelResponse(
            text="This will be a tool use.",
        ),
        ThirdPartyModelResponse(
            text="Hello",
        ),
        ThirdPartyModelResponse(
            text="",
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_name="my_tool_name",
                input={"tool_param": "tool_value999"},
                tool_use_id="tool_use_id-111",
            ),
        ),
    ]

    assert [
        StreamProcessorOutput(
            "Hi!Bye!This will be a tool use.", StreamProcessorOutputType.ANSWER
        ),
        StreamProcessorOutput("Hello", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL_START,
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        ),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL,
            tool_use=ToolUseResponse(
                "my_tool_name",
                {"tool_param": "tool_value999"},
                "tool_use_id-111",
            ),
        ),
    ] == list(stream_processor.process_stream(stream))
    assert (
        stream_processor.get_stream_history() == "Hi!Bye!This will be a tool use.Hello"
    )

    stream = [
        ThirdPartyModelResponse(text="Case with trailing space "),
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_name="my_tool_name2",
                input={"tool_param": "tool_value888"},
                tool_use_id="tool_use_id-222",
            ),
        ),
    ]
    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput(
            "Case with trailing space ", StreamProcessorOutputType.ANSWER
        ),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL,
            tool_use=ToolUseResponse(
                "my_tool_name2",
                {"tool_param": "tool_value888"},
                "tool_use_id-222",
            ),
        ),
    ]
    assert (
        stream_processor.get_stream_history()
        == "Hi!Bye!This will be a tool use.HelloCase with trailing space "
    )

    # With TOOL_START
    stream.insert(
        1,
        ThirdPartyModelResponse(
            text="",
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        ),
    )
    assert list(stream_processor.process_stream(stream))[0] == StreamProcessorOutput(
        "Case with trailing space ", StreamProcessorOutputType.ANSWER
    )


def test_delim():
    stream = [
        ThirdPartyModelResponse(text="Let's run this:"),
        ThirdPartyModelResponse(
            text="",
            tool_use=ToolUseResponse(
                tool_name="my_tool_name2",
                input={"tool_param": "tool_value888"},
                tool_use_id="tool_use_id-222",
            ),
        ),
    ]
    stream_processor = _get_stream_processor()
    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput("Let's run this:", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL,
            tool_use=ToolUseResponse(
                "my_tool_name2",
                {"tool_param": "tool_value888"},
                "tool_use_id-222",
            ),
        ),
    ]
    assert stream_processor.get_stream_history() == "Let's run this:"


@pytest.mark.parametrize(
    "stop_reason",
    [
        StopReason.END_TURN,
        StopReason.MAX_TOKENS,
        StopReason.TOOL_USE_REQUESTED,
        StopReason.REASON_UNSPECIFIED,
    ],
)
def test_end_of_stream(stop_reason):
    stream = [
        ThirdPartyModelResponse(text="Let's run this:"),
        ThirdPartyModelResponse(
            text="",
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(
                stop_reason=stop_reason,
                output_tokens=100,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=200,
                    cache_read_input_tokens=50,
                    cache_creation_input_tokens=150,
                ),
            ),
        ),
    ]

    stream_processor = _get_stream_processor()
    assert list(stream_processor.process_stream(stream)) == [
        StreamProcessorOutput("Let's run this:", StreamProcessorOutputType.ANSWER),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.TOOL_START,
            tool_use_start=ToolUseStart(
                tool_name="my_tool_name",
                tool_use_id="tool_use_id-111",
            ),
        ),
        StreamProcessorOutput(
            "",
            StreamProcessorOutputType.END_OF_STREAM,
            end_of_stream=EndOfStream(
                stop_reason=stop_reason,
                output_tokens=100,
                prompt_cache_usage=PromptCacheUsage(
                    input_tokens=200,
                    cache_read_input_tokens=50,
                    cache_creation_input_tokens=150,
                ),
            ),
        ),
    ]
    assert stream_processor.get_stream_history() == "Let's run this:"
