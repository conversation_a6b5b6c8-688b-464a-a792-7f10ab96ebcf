load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "basic_stream_processor",
    srcs = [
        "basic_stream_processor.py",
    ],
    visibility = ["//services/chat_host/server:__subpackages__"],
    deps = [
        "//base/third_party_clients:clients",
    ],
)

pytest_test(
    name = "basic_stream_processor_test",
    srcs = ["basic_stream_processor_test.py"],
    deps = [
        ":basic_stream_processor",
    ],
)

py_library(
    name = "claude_with_citations_stream_processor",
    srcs = [
        "claude_with_citations_stream_processor.py",
    ],
    visibility = ["//services/chat_host/server:__subpackages__"],
    deps = [
        ":basic_stream_processor",
        "//base/third_party_clients:clients",
    ],
)

pytest_test(
    name = "claude_with_citations_stream_processor_test",
    srcs = ["claude_with_citations_stream_processor_test.py"],
    deps = [
        ":claude_with_citations_stream_processor",
    ],
)

py_library(
    name = "claude_stream_processor_v2",
    srcs = [
        "claude_stream_processor_v2.py",
    ],
    visibility = ["//services/chat_host/server:__subpackages__"],
    deps = [
        ":basic_stream_processor",
        ":codeblocks_xml_stream_processor",
    ],
)

pytest_test(
    name = "claude_stream_processor_v2_test",
    srcs = ["claude_stream_processor_v2_test.py"],
    deps = [
        ":claude_stream_processor_v2",
    ],
)

py_library(
    name = "codeblocks_xml_stream_processor",
    srcs = [
        "codeblocks_xml_stream_processor.py",
    ],
    visibility = ["//services/chat_host/server:__subpackages__"],
    deps = [
        requirement("regex"),
    ],
)

pytest_test(
    name = "codeblocks_xml_stream_processor_test",
    srcs = ["codeblocks_xml_stream_processor_test.py"],
    deps = [
        ":codeblocks_xml_stream_processor",
        requirement("numpy"),
    ],
)

py_library(
    name = "claude_stream_processor_v3",
    srcs = [
        "claude_stream_processor_v3.py",
    ],
    visibility = ["//services/chat_host/server:__subpackages__"],
    deps = [
        ":basic_stream_processor",
        ":codeblocks_xml_stream_processor",
    ],
)

pytest_test(
    name = "claude_stream_processor_v3_test",
    srcs = ["claude_stream_processor_v3_test.py"],
    deps = [
        ":claude_stream_processor_v3",
        # Introduced to re-use tests
        ":codeblocks_xml_stream_processor_test",
    ],
)
