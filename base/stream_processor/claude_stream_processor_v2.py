"""A stream process for <PERSON>lau<PERSON>.

Handles tool use and, optionally, metadata for codeblocks in XML format.
"""

from typing import Iterable
from base.stream_processor.basic_stream_processor import (
    BasicStreamProcessor,
    StreamProcessor,
    StreamProcessorOutput,
    StreamProcessorOutputType,
)
from base.stream_processor.codeblocks_xml_stream_processor import (
    co_process_codeblocks_xml,
)
from base.third_party_clients.third_party_model_client import ThirdPartyModelResponse


class ClaudeStreamProcessorV2(StreamProcessor):
    def __init__(self, n_extra_backticks: int = 0):
        super().__init__()
        self.basic_stream_processor = BasicStreamProcessor()
        self.n_extra_backticks = n_extra_backticks

    def get_stream_history(self) -> str:
        return self.basic_stream_processor.get_stream_history()

    # Not handling tool_start or end_of_stream
    def process_stream(
        self, ls_substr: Iterable[ThirdPartyModelResponse]
    ) -> Iterable[StreamProcessorOutput]:
        # break on spaces
        whitespace_delimited_stream = self.basic_stream_processor.process_stream(
            ls_substr
        )

        processor = co_process_codeblocks_xml(n_extra_backticks=self.n_extra_backticks)
        next(processor)  # Prime the coroutine

        for stream_output in whitespace_delimited_stream:
            # Early exit if we're in a tool
            if (
                stream_output.replace_text_response
                or stream_output.type == StreamProcessorOutputType.TOOL
            ):
                yield stream_output
            else:
                output = processor.send(stream_output.text)
                yield StreamProcessorOutput(
                    output,
                    StreamProcessorOutputType.ANSWER,
                )

        # Flush the processor for any remaining buffer.
        try:
            output = processor.send(None)  # Signal the end of input
            if output is not None:
                yield StreamProcessorOutput(
                    output,
                    StreamProcessorOutputType.ANSWER,
                )
        except StopIteration:
            pass
