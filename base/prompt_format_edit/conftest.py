"""Contains shared fixtures for the code edit prompt formatting unit tests."""

import pytest

from base.prompt_format_edit.prompt_formatter import EditPromptInput, PromptChunk


@pytest.fixture()
def example_basic_input():
    """Returns an example prompt input."""
    return EditPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code=r"""file = pathlib.Path("foo")
for x in file.open():
""",
        suffix="    print(x)\n",
        instruction="fix bugs",
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(),
    )


@pytest.fixture()
def example_retrieval_input():
    """Returns an example prompt input with retrieved chunks."""
    return EditPromptInput(
        path="src/example.py",
        prefix="import pathlib\n",
        selected_code=r"""file = pathlib.Path("foo")
for x in file.open():
""",
        suffix="    print(x)\n",
        instruction="fix bugs",
        prefix_begin=0,
        suffix_end=76,
        retrieved_chunks=(
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
            ),
        ),
    )
