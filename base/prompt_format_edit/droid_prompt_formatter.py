"""The Droid prompt formatter for the code edit."""

from typing import Optional

from base.prompt_format.util import concatenate_retrieved_chunks, head_n, trailing_n
from base.prompt_format_edit.prompt_formatter import (
    EditPromptFormatter,
    EditPromptInput,
    EditPromptOutput,
    EditTokenApportionment,
    Exceed<PERSON>ontext<PERSON>ength,
    PromptChunk,
    TokenList,
    filter_overlapping_retrieved_chunks,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer

_GENERATION_BUDGET_SLACK = 512  # ~50 lines
_MINIMUM_GENERATION_BUDGET = 2048  # ~200 lines


class DroidEditPromptFormatter(EditPromptFormatter):
    """The code edit prompt formatter for the Droid code edit model."""

    def __init__(
        self,
        tokenizer: DeepSeekCoderInstructTokenizer,
        token_apportionment: Optional[EditTokenApportionment] = None,
    ):
        self.tokenizer = tokenizer
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = EditTokenApportionment(
                prefix_len=1536,
                suffix_len=1024,
                max_context_len=16384,
                # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
                dynamic_resizing=False,
                path_len=256,
                instruction_len=512,
                selected_code_len=4096,
                max_prompt_len=16384 - 4096,  # 4096 represents the max output tokens
            )
        self.token_apportionment = token_apportionment

    def _get_chunk_tokens(self, chunk: PromptChunk) -> TokenList:
        return (
            self.tokenizer.tokenize_safe(f"See ({chunk.path}):\n")
            + self.tokenizer.tokenize_safe("```\n")
            + self.tokenizer.tokenize_safe(chunk.text)
            + self.tokenizer.tokenize_safe("\n```\n")
        )

    def format_prompt(self, prompt_input: EditPromptInput) -> EditPromptOutput:
        """Format prompt for Droid code edit model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length at most self.seq_length - max_output_token_count, in tokens.
        """

        # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
        if self.token_apportionment.dynamic_resizing is False:
            return self._format_prompt_LEGACY_TO_BE_DEPRECATED(prompt_input)

        token_counts, tokenizer = self.token_apportionment, self.tokenizer

        header_tokens: TokenList = []
        retrieval_tokens: TokenList = []
        context_tokens: TokenList = []

        header_tokens: TokenList = list(self.tokenizer.special_tokens.begin_sequence)
        path_section = (
            tokenizer.tokenize_safe("Path: ")
            + tokenizer.tokenize_safe(prompt_input.path)
            + tokenizer.tokenize_safe("\n")
        )
        header_tokens.extend(path_section)
        instruction_section = (
            tokenizer.tokenize_safe("Instruction: ")
            + tokenizer.tokenize_safe(prompt_input.instruction)
            + tokenizer.tokenize_safe("\n")
        )
        header_tokens.extend(instruction_section)

        selected_code_tokens = tokenizer.tokenize_safe(prompt_input.selected_code)
        selected_code_section = (
            tokenizer.tokenize_safe("Selected Code:\n```\n")
            + selected_code_tokens
            + tokenizer.tokenize_safe("\n```\n\n")
            + tokenizer.tokenize_safe("Updated Code:\n```\n")
        )
        context_tokens: TokenList = selected_code_section

        reasonable_generation_budget = max(
            _MINIMUM_GENERATION_BUDGET,
            len(selected_code_tokens) + _GENERATION_BUDGET_SLACK,
        )

        available_budget = token_counts.max_context_len - (
            len(header_tokens)
            + len(selected_code_tokens)
            + reasonable_generation_budget
        )

        prefix_open_sep = tokenizer.tokenize_safe("Prefix:\n```\n")
        prefix_close_sep = tokenizer.tokenize_safe("\n```\n\n")
        available_budget -= len(prefix_open_sep) + len(prefix_close_sep)
        suffix_open_sep = tokenizer.tokenize_safe("Suffix:\n```\n")
        suffix_close_sep = tokenizer.tokenize_safe("\n```\n\n")
        available_budget -= len(suffix_open_sep) + len(suffix_close_sep)

        if available_budget < 0:
            raise ExceedContextLength(
                f"available_budget={available_budget} for context length = {token_counts.max_context_len}"
            )

        # Ae have now reached a bare minimum prompt. Next we should try to add
        # prefix, suffix, and retrieval chunks.

        # we may need these:
        empty_prefix = prefix_open_sep + prefix_close_sep
        empty_suffix = suffix_open_sep + suffix_close_sep

        # The prefix can use up to half the available budget.
        clipped_prefix_tokens = trailing_n(
            tokenizer.tokenize_safe(prompt_input.prefix),
            min(token_counts.prefix_len, available_budget // 2),
        )
        available_budget -= len(clipped_prefix_tokens)
        if available_budget < 0:
            return EditPromptOutput(
                header_tokens + empty_prefix + empty_suffix + context_tokens, []
            )

        # The suffix can use up to half the available budget.
        clipped_suffix_tokens = head_n(
            tokenizer.tokenize_safe(prompt_input.suffix),
            min(token_counts.suffix_len, available_budget // 2),
        )
        available_budget -= len(clipped_suffix_tokens)
        if available_budget < 0:
            return EditPromptOutput(
                header_tokens
                + prefix_open_sep
                + clipped_prefix_tokens
                + prefix_close_sep
                + empty_suffix
                + context_tokens,
                [],
            )
        context_tokens = (
            prefix_open_sep
            + clipped_prefix_tokens
            + prefix_close_sep
            + suffix_open_sep
            + clipped_suffix_tokens
            + suffix_close_sep
            + context_tokens
        )

        # Build the retrieved chunk tokens.
        retrieval_len = available_budget
        filtered_retrieved_chunks = filter_overlapping_retrieved_chunks(
            prompt_input,
            tokenizer,
            prefix_tokens=clipped_prefix_tokens,
            suffix_tokens=clipped_suffix_tokens,
            retrieved_chunks=prompt_input.retrieved_chunks,
        )
        tokens_per_retrieved_chunk = []
        retrieved_chunks_in_prompt = []
        for chunk in filtered_retrieved_chunks:
            tokens_per_retrieved_chunk.append(self._get_chunk_tokens(chunk))
            retrieved_chunks_in_prompt.append(chunk)
            if sum(map(len, tokens_per_retrieved_chunk)) >= retrieval_len:
                break
        if tokens_per_retrieved_chunk and retrieval_len >= 0:
            retrieval_tokens = concatenate_retrieved_chunks(
                retrieved_chunks=tokens_per_retrieved_chunk,
                separator_tokens=[],
                max_total_tokens=retrieval_len,
            )
        else:
            retrieval_tokens = []
        final_tokens = header_tokens + retrieval_tokens + context_tokens
        return EditPromptOutput(final_tokens, retrieved_chunks_in_prompt)

    def _format_prompt_LEGACY_TO_BE_DEPRECATED(
        self, prompt_input: EditPromptInput
    ) -> EditPromptOutput:
        """TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05.

        Format prompt for Droid code edit model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length at most self.seq_length - max_output_token_count, in tokens.
        """
        token_counts, tokenizer = self.token_apportionment, self.tokenizer

        clipped_path_tokens = trailing_n(
            tokenizer.tokenize_safe(prompt_input.path), token_counts.path_len
        )
        instruct_tokens = tokenizer.tokenize_safe(prompt_input.instruction)
        if len(instruct_tokens) > token_counts.instruction_len:
            raise ExceedContextLength(
                f"Instruction token length exceeds {token_counts.instruction_len}"
            )

        clipped_prefix_tokens = trailing_n(
            tokenizer.tokenize_safe(prompt_input.prefix), token_counts.prefix_len
        )
        clipped_suffix_tokens = head_n(
            tokenizer.tokenize_safe(prompt_input.suffix), token_counts.suffix_len
        )
        selected_code_tokens = tokenizer.tokenize_safe(prompt_input.selected_code)
        if len(selected_code_tokens) > token_counts.selected_code_len:
            raise ExceedContextLength(
                f"Selected code token length exceeds {token_counts.selected_code_len}"
            )

        # Build the header tokens.
        header_tokens: TokenList = list(self.tokenizer.special_tokens.begin_sequence)
        header_tokens.extend(tokenizer.tokenize_safe("Path: "))
        header_tokens.extend(clipped_path_tokens)
        header_tokens.extend(tokenizer.tokenize_safe("\n"))
        header_tokens.extend(tokenizer.tokenize_safe("Instruction: "))
        header_tokens.extend(instruct_tokens)
        header_tokens.extend(tokenizer.tokenize_safe("\n"))

        context_tokens: TokenList = []
        context_tokens.extend(
            tokenizer.tokenize_safe("Prefix:\n```\n")
            + clipped_prefix_tokens
            + tokenizer.tokenize_safe("\n```\n\n")
        )
        context_tokens.extend(
            tokenizer.tokenize_safe("Suffix:\n```\n")
            + clipped_suffix_tokens
            + tokenizer.tokenize_safe("\n```\n\n")
        )
        context_tokens.extend(
            tokenizer.tokenize_safe("Selected Code:\n```\n")
            + selected_code_tokens
            + tokenizer.tokenize_safe("\n```\n\n")
            + tokenizer.tokenize_safe("Updated Code:\n```\n")
        )
        # Build the retrieved chunk tokens.
        retrieval_len = (
            token_counts.max_prompt_len - len(header_tokens) - len(context_tokens)
        )
        filtered_retrieved_chunks = filter_overlapping_retrieved_chunks(
            prompt_input,
            tokenizer,
            prefix_tokens=clipped_prefix_tokens,
            suffix_tokens=clipped_suffix_tokens,
            retrieved_chunks=prompt_input.retrieved_chunks,
        )
        tokens_per_retrieved_chunk = []
        retrieved_chunks_in_prompt = []
        for chunk in filtered_retrieved_chunks:
            tokens_per_retrieved_chunk.append(self._get_chunk_tokens(chunk))
            retrieved_chunks_in_prompt.append(chunk)
            if sum(map(len, tokens_per_retrieved_chunk)) >= retrieval_len:
                break
        if tokens_per_retrieved_chunk and retrieval_len >= 0:
            retrieval_tokens = concatenate_retrieved_chunks(
                retrieved_chunks=tokens_per_retrieved_chunk,
                separator_tokens=[],
                max_total_tokens=retrieval_len,
            )
        else:
            retrieval_tokens = []
        final_tokens = header_tokens + retrieval_tokens + context_tokens
        return EditPromptOutput(final_tokens, retrieved_chunks_in_prompt)
