"""Tests for the Droid prompt formatter.

pytest base/prompt_format_edit/droid_prompt_formatter_test.py
"""

import dataclasses

import pytest

from base import tokenizers
from base.prompt_format_edit.droid_prompt_formatter import DroidEditPromptFormatter
from base.prompt_format_edit.prompt_formatter import (
    EditPromptInput,
    EditTokenApportionment,
    ExceedContextLength,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer


def test_droid_basic(example_basic_input: EditPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Droid's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("deepseek_coder_instruct")
    if not isinstance(tokenizer, DeepSeekCoderInstructTokenizer):
        raise ValueError("Tokenizer must be a DeepSeekCoderInstructTokenizer.")
    prompter = DroidEditPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_basic_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<｜begin▁of▁sentence｜>Path: src/example.py
Instruction: fix bugs
Prefix:
```
import pathlib

```

Suffix:
```
    print(x)

```

Selected Code:
```
file = pathlib.Path("foo")
for x in file.open():

```

Updated Code:
```
"""
    assert prompt == expected_prompt


def test_droid_w_retrieval(example_retrieval_input: EditPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Droid's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("deepseek_coder_instruct")
    if not isinstance(tokenizer, DeepSeekCoderInstructTokenizer):
        raise ValueError("Tokenizer must be a DeepSeekCoderInstructTokenizer.")
    prompter = DroidEditPromptFormatter(tokenizer, None)

    prompt_output = prompter.format_prompt(example_retrieval_input)
    prompt = tokenizer.detokenize(prompt_output.tokens)

    expected_prompt = """<｜begin▁of▁sentence｜>Path: src/example.py
Instruction: fix bugs
See (src/foo.py):
```
# You can aggregate
# with a pooling function.

```
See (src/bar.py):
```
# You can aggregate
# with a maxing
# function.

```
Prefix:
```
import pathlib

```

Suffix:
```
    print(x)

```

Selected Code:
```
file = pathlib.Path("foo")
for x in file.open():

```

Updated Code:
```
"""
    assert prompt == expected_prompt
    assert len(list(prompt_output.retrieved_chunks_in_prompt)) == 2


@pytest.mark.parametrize(
    "token_counts, str_to_inject_in_selected_code, expected_output_str_or_err",
    [
        # Common example, no resizing needed.
        (
            EditTokenApportionment(
                prefix_len=2000,
                suffix_len=2000,
                max_context_len=4096,
                # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
                dynamic_resizing=True,
                path_len=1,
                instruction_len=1,
                selected_code_len=1,
                max_prompt_len=1,
            ),
            "",
            (
                """<｜begin▁of▁sentence｜>Path: src/example.py
Instruction: fix bugs
See (src/foo.py):
```
# You can aggregate
# with a pooling function.

```
See (src/bar.py):
```
# You can aggregate
# with a maxing
# function.

```
Prefix:
```
import pathlib

```

Suffix:
```
    print(x)

```

Selected Code:
```
file = pathlib.Path("foo")
for x in file.open():

```

Updated Code:
```
"""
            ),
        ),
        # Large selected_code, partial resizing of retrieval budget needed.
        (
            EditTokenApportionment(
                prefix_len=2000,
                suffix_len=2000,
                max_context_len=4096,
                # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
                dynamic_resizing=True,
                path_len=1,
                instruction_len=1,
                selected_code_len=1,
                max_prompt_len=1,
            ),
            "c" * 6_000,
            (
                f"""<｜begin▁of▁sentence｜>Path: src/example.py
Instruction: fix bugs
See (src/foo.py):
```
# You can aggregate
# with a pooling function.

```
See (src/bar.py):
```
# You can aggregate
# with a maxing
# function.

```
Prefix:
```
import pathlib

```

Suffix:
```
    print(x)

```

Selected Code:
```
file = pathlib.Path("foo")
for x in file.open():
{"c" * 6_000}
```

Updated Code:
```
"""
            ),
        ),
        # Too large selected_code, exception thrown
        (
            EditTokenApportionment(
                prefix_len=2000,
                suffix_len=2000,
                max_context_len=4096,
                # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
                dynamic_resizing=True,
                path_len=1,
                instruction_len=1,
                selected_code_len=1,
                max_prompt_len=1,
            ),
            "c" * 7_000,
            (
                f"""<｜begin▁of▁sentence｜>Path: src/example.py
Instruction: fix bugs
Prefix:
```
import pathlib

```

Suffix:
```
    print(
```

Selected Code:
```
file = pathlib.Path("foo")
for x in file.open():
{"c" * 7_000}
```

Updated Code:
```
"""
            ),
        ),
        # Large selected_code, all of retrieval budget and some of prefix/suffix needed.
        (
            EditTokenApportionment(
                prefix_len=2000,
                suffix_len=2000,
                max_context_len=4096,
                # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
                dynamic_resizing=True,
                path_len=1,
                instruction_len=1,
                selected_code_len=1,
                max_prompt_len=1,
            ),
            "c" * 8_000,
            ExceedContextLength("available_budget=-489 for context length = 4096"),
        ),
    ],
)
def test_dynamic_resizing(
    token_counts: EditTokenApportionment,
    str_to_inject_in_selected_code: str,
    expected_output_str_or_err: str,
    example_retrieval_input: EditPromptInput,
):
    tokenizer = tokenizers.create_tokenizer_by_name("deepseek_coder_instruct")
    if not isinstance(tokenizer, DeepSeekCoderInstructTokenizer):
        raise ValueError("Tokenizer must be a DeepSeekCoderInstructTokenizer.")
    prompter = DroidEditPromptFormatter(tokenizer, token_counts)

    example_retrieval_input = dataclasses.replace(
        example_retrieval_input,
        selected_code=example_retrieval_input.selected_code
        + str_to_inject_in_selected_code,
    )

    if isinstance(expected_output_str_or_err, str):
        prompt_output = prompter.format_prompt(example_retrieval_input)
        output_str = tokenizer.detokenize(prompt_output.tokens)
        assert output_str == expected_output_str_or_err
    else:
        with pytest.raises(
            ExceedContextLength, match=expected_output_str_or_err.message
        ):
            prompt_output = prompter.format_prompt(example_retrieval_input)
