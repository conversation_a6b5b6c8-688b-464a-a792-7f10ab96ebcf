"""Classes that are used to build the prompts for the code edit model."""

import typing
from dataclasses import dataclass
from typing import Iterable

from dataclasses_json import dataclass_json

from base.prompt_format.common import PromptChunk, TokenList
from base.tokenizers.tokenizer import Tokenizer


class ExceedContextLength(Exception):
    """Raised when some fields exceed the maximum allowed length."""

    def __init__(self, message: str = "Exceed the context length"):
        self.message = message
        super().__init__(self.message)


@dataclass_json
@dataclass(frozen=True)
class EditTokenApportionment:
    """Stores the apportionment of the tokens for the code edit model."""

    prefix_len: int
    """The number of tokens of the prefix to include."""

    suffix_len: int
    """The number of tokens of the suffix to include."""

    max_context_len: int
    """The maximum number of tokens that can be used in the context."""

    def __post_init__(self):
        assert self.prefix_len >= 0
        assert self.suffix_len >= 0
        assert self.max_context_len >= 0

    # -----------------------------------------------
    # DEPRECATION PLAN: deprecate below fields
    # once dynamic_resizing is turned on everywhere.
    # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
    # -----------------------------------------------

    dynamic_resizing: bool
    """Whether to use dynamic resizing. If true, the below fields are ignored."""

    path_len: int
    """The number of tokens of the path to include."""

    instruction_len: int
    """The number of tokens of the instruction to include."""

    selected_code_len: int
    """The number of tokens of the selected_code to include."""

    max_prompt_len: int
    """The number of tokens of the total prompt length, which will be used to compute the budget for retrieved chunks."""


@dataclass(frozen=True)
class EditPromptInput:
    """The set of inputs used for constructing the code edit model prompts.

    The goal is to maintain everything about the raw inputs in a single centralized place. If
    we have a new kind of input, just add a new field here and set its default value.

    `prefix_begin`, `suffix_end` and `file_path` are required to filter overlapping retrieval chunks.
    """

    path: str
    """The file path."""

    prefix: str
    """The content before the selection."""

    selected_code: str
    """The content that the user selected."""

    suffix: str
    """The content after the selection, where its end location is at the cursor."""

    instruction: str
    """The user's instruction about how to edit the selected code based on context."""

    prefix_begin: int
    """The offset in UTF-8 characters where prefix begins, relative to the beginning of file."""

    suffix_end: int
    """The offset in UTF-8 characters where suffix ends, relative to the beginning of file."""

    retrieved_chunks: typing.Iterable[PromptChunk]
    """The retrieved chunks, sorted from high to low relevancy."""


@dataclass(frozen=True)
class EditPromptOutput:
    """The set of outputs used for constructing the code edit model prompts."""

    tokens: TokenList
    """The tokenized prompt."""

    # TODO(markus): We should avoid having fields like this one. The purpose of this output is
    # to log something to request insight. Instead we should pass a callback into the prompt formatter
    # in its constructor, and call it with the retrieved chunks.
    retrieved_chunks_in_prompt: typing.Iterable[PromptChunk]
    """The retrieved chunks that are finally used in the prompt (e.g., after the potential filtering logic)."""


class EditPromptFormatter(typing.Protocol):
    """The EditPromptFormatter protocol for the code edit model."""

    token_apportionment: EditTokenApportionment

    def format_prompt(self, prompt_input: EditPromptInput) -> EditPromptOutput:
        """Build tokenized prompt.

        Args:
            prompt_input: EditPromptInput object describing input (prefix, selected_code, suffix, instruction, path)
            retrieved_chunks: The retrieved chunks, sorted from high to low relevancy.
            max_output_token_count: The maximal number of output tokens that should be generated.

        Returns:
            prompt as list of tokens
        """
        raise NotImplementedError()


def filter_overlapping_retrieved_chunks(
    prompt_input: EditPromptInput,
    tokenizer: Tokenizer,
    prefix_tokens: TokenList,
    suffix_tokens: TokenList,
    retrieved_chunks: Iterable[PromptChunk],
) -> Iterable[PromptChunk]:
    """Takes an array of PromptChunk and returns only ones that do not overlap with text used in prompt formatting.

    This method is supposed to be used in `format_prompt` method, right after you computed `prefix_tokens` and
    `suffix_tokens` that will be used in prompt.

    Args:
        prompt_input: Preferably the one that `format_prompt` received
            (i.e. without modifications to `.prefix`, `.suffix`, `prefix_begin` and `suffix_end`).
        tokenizer: The one used in `format_prompt`.
        prefix_tokens: Full list of prefix tokens that directly go to prompt.
        suffix_tokens: Same as prefix_tokens, but for suffix.
        retrieved_chunks: Array of PromptChunk to be filtered.
            PromptChunk.char_start and PromptChunk.char_end have to be correctly set.

    Returns:
        Iterable of filtered PromptChunk.
    """

    unused_prefix_len = len(prompt_input.prefix) - len(
        tokenizer.detokenize(prefix_tokens)
    )
    unused_suffix_len = len(prompt_input.suffix) - len(
        tokenizer.detokenize(suffix_tokens)
    )

    effective_prefix_begin = prompt_input.prefix_begin + unused_prefix_len
    effective_suffix_end = prompt_input.suffix_end - unused_suffix_len

    for chunk in retrieved_chunks:
        is_different_file = chunk.path != prompt_input.path
        is_ranges_non_intersect = min(chunk.char_end, effective_suffix_end) <= max(
            chunk.char_start, effective_prefix_begin
        )
        if is_different_file or is_ranges_non_intersect:
            yield chunk
