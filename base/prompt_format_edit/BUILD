load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "conftest",
    srcs = [
        "conftest.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/tokenizers",
        requirement("pytest"),
    ],
)

py_library(
    name = "prompt_formatter",
    srcs = [
        "prompt_formatter.py",
    ],
    deps = [
        "//base/prompt_format:common",
        "//base/tokenizers",
        requirement("dataclasses_json"),
    ],
)

pytest_test(
    name = "prompt_formatter_test",
    srcs = ["prompt_formatter_test.py"],
    deps = [
        ":prompt_formatter",
    ],
)

py_library(
    name = "droid_prompt_formatter",
    srcs = [
        "droid_prompt_formatter.py",
    ],
    deps = [
        ":prompt_formatter",
        "//base/prompt_format:util",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "droid_prompt_formatter_test",
    srcs = ["droid_prompt_formatter_test.py"],
    deps = [
        ":conftest",
        ":droid_prompt_formatter",
        ":prompt_formatter",
    ],
)

py_library(
    name = "prompt_format_edit",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":droid_prompt_formatter",
        ":prompt_formatter",
    ],
)
