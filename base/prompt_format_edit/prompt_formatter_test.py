"""Tests for EditPromptFormatter."""

from base.prompt_format.common import PromptChunk
from base.prompt_format_edit.prompt_formatter import (
    EditPromptInput,
    filter_overlapping_retrieved_chunks,
)
from base.tokenizers import create_tokenizer_by_name


def get_chunk(char_start, char_end, path):
    return PromptChunk(
        text="a" * (char_end - char_start),
        path=path,
        char_start=char_start,
        char_end=char_end,
    )


def test_filter_overlapping_retrieved_chunks():
    prompt_input = EditPromptInput(
        path="/p1",
        prefix="(unused prefix) def some_function(a,b):\n",
        suffix="return a+b\n (unused suffix)",
        prefix_begin=10,
        suffix_end=101,
        selected_code="call_some_function(a,b)\n",
        instruction="Change something",
        retrieved_chunks=(),
    )
    tokenizer = create_tokenizer_by_name("deepseek_coder_instruct")

    # effective indices: prefix_begin=25, suffix_end=85
    effective_prefix = " def some_function(a,b):\n"
    effective_suffix = "return a+b\n"

    prefix_tokens = tokenizer.tokenize_safe(effective_prefix)
    suffix_tokens = tokenizer.tokenize_safe(effective_suffix)

    chunks = [
        get_chunk(0, 10, "/p1"),
        get_chunk(10, 25, "/p1"),
        get_chunk(10, 40, "/p1"),
        get_chunk(30, 40, "/p1"),
        get_chunk(50, 85, "/p1"),
        get_chunk(70, 100, "/p1"),
        get_chunk(85, 100, "/p1"),
        get_chunk(90, 100, "/p1"),
        get_chunk(10, 40, "/p2"),
        get_chunk(30, 40, "/p2"),
        get_chunk(50, 85, "/p2"),
    ]

    filtered_chunks = filter_overlapping_retrieved_chunks(
        prompt_input, tokenizer, prefix_tokens, suffix_tokens, chunks
    )

    assert set(filtered_chunks) == {chunks[i] for i in [0, 1, 6, 7, 8, 9, 10]}
