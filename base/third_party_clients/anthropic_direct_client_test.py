from contextlib import contextmanager
from typing import Any, Generator, List
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch
from datetime import datetime, timezone, timedelta

import pytest
from anthropic import BadRequestError, ContentBlockStopEvent, InputJsonEvent, TextEvent
from anthropic.types import (
    InputJSONDelta,
    Message,
    RawContentBlockDeltaEvent,
    RawContentBlockStartEvent,
    RawContentBlockStopEvent,
    RawMessageDeltaEvent,
    RawMessageStartEvent,
    RawMessageStopEvent,
    TextBlock,
    TextDelta,
    ToolUseBlock,
    Usage,
)
import httpx

import base.feature_flags
from base.prompt_format.common import (
    ChatRequestImage,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    ImageFormatType,
    StopReason,
)
from base.third_party_clients import anthropic_direct_client
from base.third_party_clients.anthropic_direct_client import (
    _IGNORED_EVENT_TYPES,
    EVENT_TYPE_CONTENT_BLOCK_DELTA,
    EVENT_TYPE_CONTENT_BLOCK_START,
    EVENT_TYPE_CONTENT_BLOCK_STOP,
    EVENT_TYPE_INPUT_JSON,
    EVENT_TYPE_MESSAGE_DELTA,
    EVENT_TYPE_MESSAGE_START,
    EVENT_TYPE_MESSAGE_STOP,
    EVENT_TYPE_TEXT,
    should_return_partial_tool_use_block,
    _SAVE_FILE_PARTIAL_TOOL_USE,
    format_response_message_content,
    bad_request_reason_from_exception,
    create_error_details_from_bad_request,
)
from base.third_party_clients.third_party_model_client import (
    PromptCacheUsage,
    ThirdPartyModelResponse,
    ToolDefinition,
    ToolUseResponse,
)
from base.error_details.error_details_pb2 import ErrorCode

anthropic_client = anthropic_direct_client.AnthropicDirectClient(
    api_key="",
    model_name="claude-2.1",
    temperature=0.2,
    max_output_tokens=10,
)
format_request_message_content = anthropic_client.format_request_message_content


@pytest.fixture
def feature_flags():
    yield from base.feature_flags.feature_flag_fixture()


def test_format_request_message():
    # Strings are pass-through
    assert (
        format_request_message_content("Summarize this library")
        == "Summarize this library"
    )
    assert format_request_message_content("") == ""
    assert format_request_message_content("\n") == "\n"

    # Structured nodes
    input_empty_text_node = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content=""),
        tool_result_node=None,
    )
    output_empty_text_node = {
        "type": "text",
        "text": "",
    }
    # Requests with only text nodes are collapsed to a string
    assert format_request_message_content([input_empty_text_node]) == ""

    input_whitespace_only_text_node = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="\n"),
        tool_result_node=None,
    )
    output_whitespace_only_text_node = {
        "type": "text",
        "text": "\n",
    }
    # Requests with only text nodes are collapsed to a string
    assert format_request_message_content([input_whitespace_only_text_node]) == "\n"

    input_non_empty_text_node = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello world!"),
        tool_result_node=None,
    )
    output_non_empty_text_node = {
        "type": "text",
        "text": "Hello world!",
    }
    # Requests with only text nodes are collapsed to a string
    assert format_request_message_content([input_non_empty_text_node]) == "Hello world!"
    # And concatenated
    assert (
        format_request_message_content(
            [input_non_empty_text_node, input_whitespace_only_text_node]
        )
        == "Hello world!\n"
    )

    input_tool_result = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id="toolu_123",
            content="42",
            is_error=False,
        ),
    )
    output_tool_result = {
        "type": "tool_result",
        "tool_use_id": "toolu_123",
        "content": "42",
    }
    assert format_request_message_content([input_tool_result]) == [output_tool_result]

    input_tool_result_error = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id="toolu_124",
            content="Could not read file at the specified path.",
            is_error=True,
        ),
    )
    output_tool_result_error = {
        "type": "tool_result",
        "tool_use_id": "toolu_124",
        "content": "Could not read file at the specified path.",
        "is_error": True,
    }
    assert format_request_message_content([input_tool_result_error]) == [
        output_tool_result_error
    ]

    input_png = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.IMAGE,
        text_node=None,
        tool_result_node=None,
        image_node=ChatRequestImage(
            image_data="base64_encoded_image_data",
            format=ImageFormatType.PNG,
        ),
    )
    output_png = {
        "type": "image",
        "source": {
            "type": "base64",
            "media_type": "image/png",
            "data": "base64_encoded_image_data",
        },
    }
    assert format_request_message_content([input_png]) == [output_png]

    input_jpeg = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.IMAGE,
        text_node=None,
        tool_result_node=None,
        image_node=ChatRequestImage(
            image_data="base64_encoded_image_data",
            format=ImageFormatType.JPEG,
        ),
    )
    output_jpeg = {
        "type": "image",
        "source": {
            "type": "base64",
            "media_type": "image/jpeg",
            "data": "base64_encoded_image_data",
        },
    }
    assert format_request_message_content([input_jpeg]) == [output_jpeg]

    input_unknown_image_type = ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.IMAGE,
        text_node=None,
        tool_result_node=None,
        image_node=ChatRequestImage(
            image_data="base64_encoded_image_data",
            format=ImageFormatType.IMAGE_FORMAT_UNSPECIFIED,
        ),
    )
    with pytest.raises(ValueError):
        format_request_message_content([input_unknown_image_type])

    assert format_request_message_content(
        [
            input_non_empty_text_node,
            input_tool_result_error,
            input_png,
            input_whitespace_only_text_node,
            input_tool_result,
            input_empty_text_node,
            input_jpeg,
        ]
    ) == [
        output_non_empty_text_node,
        output_tool_result_error,
        output_png,
        output_whitespace_only_text_node,
        output_tool_result,
        output_empty_text_node,
        output_jpeg,
    ]


def test_format_response_message():
    # Strings are pass-through
    assert format_response_message_content("Hello world!") == "Hello world!"
    assert format_response_message_content("") == ""
    assert format_response_message_content("\n") == "\n"

    # Empty text nodes are dropped; this differs from format_request_message_content primarily
    # because we know in practice that anthropic models will occasionally produce a tool use with
    # no text content, or simply no output at all, and that we store this in our client as a
    # structured text node with empty content. Calling claude with such a node will result in a
    input_empty_text_node = ChatResultNode(
        id=0,
        type=ChatResultNodeType.RAW_RESPONSE,
        content="",
        tool_use=None,
    )
    assert format_response_message_content([input_empty_text_node]) == []

    input_whitespace_only_text_node = ChatResultNode(
        id=0,
        type=ChatResultNodeType.RAW_RESPONSE,
        content="\n",
        tool_use=None,
    )
    assert format_response_message_content([input_whitespace_only_text_node]) == []

    input_non_empty_text_node = ChatResultNode(
        id=0,
        type=ChatResultNodeType.RAW_RESPONSE,
        content="I will look that up for you",
        tool_use=None,
    )
    output_non_empty_text_node = {
        "type": "text",
        "text": "I will look that up for you",
    }
    assert format_response_message_content([input_non_empty_text_node]) == [
        output_non_empty_text_node
    ]

    input_tool_use_node = ChatResultNode(
        id=1,
        type=ChatResultNodeType.TOOL_USE,
        content="",
        tool_use=ChatResultToolUse(
            tool_use_id="toolu_123",
            name="tool_name",
            input={"tool_param": "tool_value"},
        ),
    )
    output_tool_use_node = {
        "type": "tool_use",
        "id": "toolu_123",
        "name": "tool_name",
        "input": {"tool_param": "tool_value"},
    }
    assert format_response_message_content([input_tool_use_node]) == [
        output_tool_use_node
    ]

    # Text nodes are always before tool uses
    assert format_response_message_content(
        [
            input_tool_use_node,
            input_non_empty_text_node,
            input_empty_text_node,
            input_tool_use_node,
            input_whitespace_only_text_node,
            input_non_empty_text_node,
        ]
    ) == [
        output_non_empty_text_node,
        output_non_empty_text_node,
        output_tool_use_node,
        output_tool_use_node,
    ]


def test_event_handling(caplog):
    """Test that the client correctly processes events from the Anthropic API."""
    caplog.set_level("DEBUG")

    mock_logger = MagicMock()

    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="fake-api-key",  # pragma: allowlist secret
        model_name="claude-3-7-sonnet-20250219",
        temperature=0.2,
        max_output_tokens=10,
    )

    # Replace the instance logger with our mock
    client.logger = mock_logger

    mock_messages = MagicMock()
    mock_stream = MagicMock()

    mock_stream.return_value.__enter__.return_value = [
        RawMessageStartEvent(
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason=None,
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
            type="message_start",
        ),
        RawContentBlockStartEvent(
            content_block=TextBlock(text="", type="text"),
            index=0,
            type="content_block_start",
        ),
        RawContentBlockDeltaEvent(
            delta=TextDelta(text="I'll write a program", type="text_delta"),
            index=0,
            type="content_block_delta",
        ),
        TextEvent(
            type="text", text="I'll write a program", snapshot="I'll write a program"
        ),
        RawContentBlockDeltaEvent(
            delta=TextDelta(
                text=" that gets the current date using the available tool, calculates the",
                type="text_delta",
            ),
            index=0,
            type="content_block_delta",
        ),
        TextEvent(
            type="text",
            text=" that gets the current date using the available tool, calculates the",
            snapshot="I'll write a program that gets the current date using the available tool, calculates the",
        ),
        RawContentBlockDeltaEvent(
            delta=TextDelta(
                text=" factorial of 5, and prints both results.\n\nFirst",
                type="text_delta",
            ),
            index=0,
            type="content_block_delta",
        ),
        TextEvent(
            type="text",
            text=" factorial of 5, and prints both results.\n\nFirst",
            snapshot="I'll write a program that gets the current date using the available tool, calculates the factorial of 5, and prints both results.\n\nFirst",
        ),
        RawContentBlockDeltaEvent(
            delta=TextDelta(
                text=", let me get the current date using the get_date tool:",
                type="text_delta",
            ),
            index=0,
            type="content_block_delta",
        ),
        TextEvent(
            type="text",
            text=", let me get the current date using the get_date tool:",
            snapshot="I'll write a program that gets the current date using the available tool, calculates the factorial of 5, and prints both results.\n\nFirst, let me get the current date using the get_date tool:",
        ),
        ContentBlockStopEvent(
            index=0,
            type="content_block_stop",
            content_block=TextBlock(
                text="I'll write a program that gets the current date using the available tool, calculates the factorial of 5, and prints both results.\n\nFirst, let me get the current date using the get_date tool:",
                type="text",
            ),
        ),
        RawContentBlockStartEvent(
            content_block=ToolUseBlock(
                id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
                input={},
                name="get_date",
                type="tool_use",
            ),
            index=1,
            type="content_block_start",
        ),
        RawContentBlockDeltaEvent(
            delta=InputJSONDelta(partial_json="", type="input_json_delta"),
            index=1,
            type="content_block_delta",
        ),
        InputJsonEvent(type="input_json", partial_json="", snapshot={}),
        ContentBlockStopEvent(
            index=1,
            type="content_block_stop",
            content_block=ToolUseBlock(
                id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
                input={},
                name="get_date",
                type="tool_use",
            ),
        ),
        RawMessageStopEvent(
            type="message_stop",
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason="end_turn",
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
        ),
    ]

    mock_messages.stream = mock_stream

    # Replace the client's messages attribute with our mock
    original_client = client.client
    client.client = MagicMock()
    client.client.messages = mock_messages

    try:
        responses = list(
            client.generate_response_stream(
                cur_message="Tell me the current date", model_caller="test"
            )
        )

        assert len(responses) == 7

        # Check text events
        assert responses[0].text == "I'll write a program"
        assert responses[0].tool_use is None

        assert (
            responses[1].text
            == " that gets the current date using the available tool, calculates the"
        )
        assert responses[1].tool_use is None

        assert responses[2].text == " factorial of 5, and prints both results.\n\nFirst"
        assert responses[2].tool_use is None

        assert (
            responses[3].text
            == ", let me get the current date using the get_date tool:"
        )
        assert responses[3].tool_use is None

        # Check tool use event(s)
        assert responses[4].text == ""
        assert responses[4].tool_use_start is not None
        assert responses[4].tool_use_start.tool_name == "get_date"
        assert (
            responses[4].tool_use_start.tool_use_id
            == "toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx"
        )

        assert responses[5].text == ""
        assert responses[5].tool_use is not None
        assert responses[5].tool_use.tool_name == "get_date"
        assert responses[5].tool_use.input == {}
        assert not responses[5].tool_use.is_partial
        assert (
            responses[5].tool_use.tool_use_id == "toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx"
        )

        assert responses[6].text == ""
        assert responses[6].end_of_stream is not None
        assert responses[6].end_of_stream.stop_reason == StopReason.END_TURN
        assert responses[6].end_of_stream.output_tokens == 5
        assert responses[6].end_of_stream.prompt_cache_usage is not None
        assert responses[6].end_of_stream.prompt_cache_usage.input_tokens == 444
        assert (
            responses[6].end_of_stream.prompt_cache_usage.cache_creation_input_tokens
            == 0
        )
        assert (
            responses[6].end_of_stream.prompt_cache_usage.cache_read_input_tokens == 0
        )

        mock_logger.info.assert_any_call("generating response for %s messages", 0)
        mock_logger.info.assert_any_call(
            "tool_choice_param: %s", anthropic_direct_client.Anthropic_NOT_GIVEN
        )

        logged_event_types = set()
        if hasattr(mock_logger, "debug") and mock_logger.debug.call_count > 0:
            for call in mock_logger.debug.call_args_list:
                args = call[0]
                if (
                    len(args) == 1
                    and args[0] in anthropic_direct_client._IGNORED_EVENT_TYPES
                ):
                    logged_event_types.add(args[0])

            assert (
                len(logged_event_types) > 0
            ), "No event types were logged in debug logs"

        info_log_count = mock_logger.info.call_count
        error_log_count = mock_logger.error.call_count
        warning_log_count = (
            mock_logger.warning.call_count if hasattr(mock_logger, "warning") else 0
        )
        debug_log_count = (
            mock_logger.debug.call_count if hasattr(mock_logger, "debug") else 0
        )

        # Based on the actual log counts from our test run
        assert (
            3 <= info_log_count <= 6
        ), f"Expected 3-6 info logs, found {info_log_count}"
        assert error_log_count == 0, f"Expected 0 error logs, found {error_log_count}"
        assert (
            warning_log_count == 0
        ), f"Expected 0 warning logs, found {warning_log_count}"

        # We expect debug logs for the ignored event types
        # The client logs these events at debug level, not info level
        assert (
            6 <= debug_log_count <= 10
        ), f"Expected 6-10 debug logs, found {debug_log_count}"
    finally:
        client.client = original_client


@pytest.mark.parametrize(
    "stop_message, expected_stop_reason",
    [
        ("end_turn", StopReason.END_TURN),
        ("stop_sequence", StopReason.END_TURN),
        ("tool_use", StopReason.TOOL_USE_REQUESTED),
        ("max_tokens", StopReason.MAX_TOKENS),
    ],
)
def test_each_stop_reason(stop_message, expected_stop_reason):
    """Test that the client correctly processes events from the Anthropic API."""
    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="fake-api-key",  # pragma: allowlist secret
        model_name="claude-3-7-sonnet-20250219",
        temperature=0.2,
        max_output_tokens=10,
    )

    mock_messages = MagicMock()
    mock_stream = MagicMock()

    mock_stream.return_value.__enter__.return_value = [
        RawMessageStartEvent(
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason=None,
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
            type="message_start",
        ),
        RawMessageStopEvent(
            type="message_stop",
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason=stop_message,
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
        ),
    ]

    mock_messages.stream = mock_stream

    # Replace the client's messages attribute with our mock
    original_client = client.client
    client.client = MagicMock()
    client.client.messages = mock_messages

    try:
        responses = list(
            client.generate_response_stream(
                cur_message="Tell me the current date", model_caller="test"
            )
        )

        assert len(responses) == 1

        # Check end of stream event
        assert responses[0].text == ""
        assert responses[0].end_of_stream is not None
        assert responses[0].end_of_stream.stop_reason == expected_stop_reason
        assert responses[0].end_of_stream.output_tokens == 5
        assert responses[0].end_of_stream.prompt_cache_usage is not None
        assert responses[0].end_of_stream.prompt_cache_usage.input_tokens == 444
        assert (
            responses[0].end_of_stream.prompt_cache_usage.cache_creation_input_tokens
            == 0
        )
        assert (
            responses[0].end_of_stream.prompt_cache_usage.cache_read_input_tokens == 0
        )
    finally:
        client.client = original_client


@pytest.mark.parametrize(
    "tool_use_block, expected",
    [
        (
            ToolUseBlock(
                id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                name="str-replace-editor",
                type="tool_use",
                input={
                    "command": "str_replace",
                    "path": "example/file.py",
                    "old_str_1": "def old_function():",
                    "old_str_start_line_1": 10,
                    "old_str_end_line_1": 10,
                    "new_str_1": "def new_function():",
                },
            ),
            True,
        ),
        (
            ToolUseBlock(
                id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                name="str-replace-editor",
                type="tool_use",
                input={
                    "command": "view",
                    "path": "example/file.py",
                },
            ),
            False,
        ),
        (
            ToolUseBlock(
                id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                name="save-file",
                type="tool_use",
                input={
                    "path": "example/file.py",
                    "file_content_1": "def new_function():",
                },
            ),
            True,
        ),
        (
            ToolUseBlock(
                id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                name="save-file",
                type="tool_use",
                input={
                    "path": "example/file.py",
                },
            ),
            True,
        ),
        (
            ToolUseBlock(
                id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                name="save-file",
                type="tool_use",
                input={
                    "file_content": "def new_function():",
                },
            ),
            False,
        ),
        (
            ToolUseBlock(
                id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                name="save-file",
                type="tool_use",
                input='{ "path": "example/file.py", "file_content_1": "def new_function():" }',
            ),
            False,
        ),
    ],
)
def test_should_return_partial_tool_use_block(
    feature_flags, tool_use_block: ToolUseBlock, expected: bool
):
    """Test that the client correctly determines whether to return a partial tool use block."""
    feature_flags.set_flag(_SAVE_FILE_PARTIAL_TOOL_USE, True)
    assert should_return_partial_tool_use_block(tool_use_block) == expected


def test_max_tokens_with_partial_tool_use():
    """Test that the client correctly returns a partial tool use when max_tokens is reached."""
    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="fake-api-key",  # pragma: allowlist secret
        model_name="claude-3-7-sonnet-20250219",
        temperature=0.2,
        max_output_tokens=10,
    )

    mock_messages = MagicMock()
    mock_stream = MagicMock()

    # Create a partial str-replace-editor tool use that should be returned
    # even when max_tokens is reached
    partial_tool_use = ToolUseBlock(
        id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
        name="str-replace-editor",
        type="tool_use",
        input={
            "command": "str_replace",
            "path": "example/file.py",
            "old_str_1": "def old_function():",
            "old_str_start_line_1": 10,
            "old_str_end_line_1": 10,
            "new_str_1": "def new_function():",
        },
    )

    mock_stream.return_value.__enter__.return_value = [
        RawMessageStartEvent(
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason=None,
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
            type="message_start",
        ),
        RawContentBlockStartEvent(
            content_block=TextBlock(text="", type="text"),
            index=0,
            type="content_block_start",
        ),
        TextEvent(
            type="text",
            text="I'll help you modify that file",
            snapshot="I'll help you modify that file",
        ),
        ContentBlockStopEvent(
            index=0,
            type="content_block_stop",
            content_block=TextBlock(
                text="I'll help you modify that file",
                type="text",
            ),
        ),
        RawContentBlockStartEvent(
            content_block=partial_tool_use,
            index=1,
            type="content_block_start",
        ),
        RawMessageStopEvent(
            type="message_stop",
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[
                    TextBlock(text="I'll help you modify that file", type="text"),
                    partial_tool_use,
                ],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason="max_tokens",
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
        ),
    ]

    mock_messages.stream = mock_stream

    # Replace the client's messages attribute with our mock
    original_client = client.client
    client.client = MagicMock()
    client.client.messages = mock_messages

    try:
        responses = list(
            client.generate_response_stream(
                cur_message="Update the function name in example/file.py",
                model_caller="test",
            )
        )

        # We should have 4 responses: text, tool_use_start, tool_use, and end_of_stream
        assert len(responses) == 4

        # Check text event
        assert responses[0].text == "I'll help you modify that file"
        assert responses[0].tool_use is None
        assert responses[0].tool_use_start is None

        # Check tool_use_start event
        assert responses[1].text == ""
        assert responses[1].tool_use is None
        assert responses[1].tool_use_start is not None
        assert responses[1].tool_use_start.tool_name == "str-replace-editor"
        assert (
            responses[1].tool_use_start.tool_use_id
            == "toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H"
        )

        # Check tool use event - this is the partial tool use that should be returned
        # even though max_tokens was reached
        assert responses[2].text == ""
        assert responses[2].tool_use is not None
        assert responses[2].tool_use.tool_name == "str-replace-editor"
        assert (
            responses[2].tool_use.tool_use_id == "toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H"
        )
        assert responses[2].tool_use.input == {
            "command": "str_replace",
            "path": "example/file.py",
            "old_str_1": "def old_function():",
            "old_str_start_line_1": 10,
            "old_str_end_line_1": 10,
            "new_str_1": "def new_function():",
        }
        assert responses[2].tool_use.is_partial

        # Check end of stream event
        assert responses[3].text == ""
        assert responses[3].end_of_stream is not None
        assert responses[3].end_of_stream.stop_reason == StopReason.MAX_TOKENS
        assert responses[3].end_of_stream.output_tokens == 5
        assert responses[3].end_of_stream.prompt_cache_usage is not None
        assert responses[3].end_of_stream.prompt_cache_usage.input_tokens == 444
    finally:
        client.client = original_client


def test_max_tokens_with_non_returned_partial_tool_use():
    """Test that the client does not return a partial tool use for non-str-replace-editor tools when max_tokens is reached."""
    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="fake-api-key",  # pragma: allowlist secret
        model_name="claude-3-7-sonnet-20250219",
        temperature=0.2,
        max_output_tokens=10,
    )

    mock_messages = MagicMock()
    mock_stream = MagicMock()

    # Create a partial web-search tool use that should NOT be returned
    # when max_tokens is reached (only str-replace-editor with specific schema should be returned)
    partial_tool_use = ToolUseBlock(
        id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
        name="web-search",
        type="tool_use",
        input={
            "query": "How to implement a binary search tree",
            "num_results": 5,
        },
    )

    mock_stream.return_value.__enter__.return_value = [
        RawMessageStartEvent(
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason=None,
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
            type="message_start",
        ),
        RawContentBlockStartEvent(
            content_block=TextBlock(text="", type="text"),
            index=0,
            type="content_block_start",
        ),
        RawContentBlockDeltaEvent(
            delta=TextDelta(
                text="I'll search for information about binary search trees",
                type="text_delta",
            ),
            index=0,
            type="content_block_delta",
        ),
        TextEvent(
            type="text",
            text="I'll search for information about binary search trees",
            snapshot="I'll search for information about binary search trees",
        ),
        ContentBlockStopEvent(
            index=0,
            type="content_block_stop",
            content_block=TextBlock(
                text="I'll search for information about binary search trees",
                type="text",
            ),
        ),
        RawContentBlockStartEvent(
            content_block=partial_tool_use,
            index=1,
            type="content_block_start",
        ),
        RawMessageStopEvent(
            type="message_stop",
            message=Message(
                id="msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                content=[
                    TextBlock(
                        text="I'll search for information about binary search trees",
                        type="text",
                    ),
                    partial_tool_use,
                ],
                model="claude-3-7-sonnet-20250219",
                role="assistant",
                stop_reason="max_tokens",
                stop_sequence=None,
                type="message",
                usage=Usage(
                    input_tokens=444,
                    output_tokens=5,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                ),
            ),
        ),
    ]

    mock_messages.stream = mock_stream

    # Replace the client's messages attribute with our mock
    original_client = client.client
    client.client = MagicMock()
    client.client.messages = mock_messages

    try:
        responses = list(
            client.generate_response_stream(
                cur_message="Tell me about binary search trees",
                model_caller="test",
            )
        )

        # We should have 3 responses: text, tool_use_start, and end_of_stream
        # (but NOT the partial tool use since it's not str-replace-editor)
        assert len(responses) == 3

        # Check text event
        assert (
            responses[0].text == "I'll search for information about binary search trees"
        )
        assert responses[0].tool_use is None
        assert responses[0].tool_use_start is None

        # Check tool_use_start event
        assert responses[1].text == ""
        assert responses[1].tool_use is None
        assert responses[1].tool_use_start is not None
        assert responses[1].tool_use_start.tool_name == "web-search"
        assert (
            responses[1].tool_use_start.tool_use_id
            == "toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H"
        )

        # Check end of stream event - should come immediately after tool_use_start
        # without returning the partial tool use
        assert responses[2].text == ""
        assert responses[2].end_of_stream is not None
        assert responses[2].end_of_stream.stop_reason == StopReason.MAX_TOKENS
        assert responses[2].end_of_stream.output_tokens == 5
        assert responses[2].end_of_stream.prompt_cache_usage is not None
        assert responses[2].end_of_stream.prompt_cache_usage.input_tokens == 444
    finally:
        client.client = original_client


def test_dummy_tool_injection():
    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="",
        model_name="claude-2.1",
        temperature=0.2,
        max_output_tokens=10,
    )
    client.client = MagicMock()
    client.client.messages = MagicMock()
    client.client.messages.stream.side_effect = Exception("fail_for_testing")

    # No tools in history, no tools provided
    cur_message = "Hello world!"
    chat_history = [
        Exchange(
            request_message=[
                ChatRequestNode(
                    id=0,
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(content="What's your purpose?"),
                    tool_result_node=None,
                ),
            ],
            response_text=[
                ChatResultNode(
                    id=0,
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content="I'm here to help!",
                ),
            ],
        ),
    ]
    with pytest.raises(Exception, match="fail_for_testing"):
        stream = client.generate_response_stream(
            cur_message=cur_message,
            chat_history=chat_history,
            model_caller="test",
        )
        next(stream)
    kwargs = client.client.messages.stream.call_args[1]
    # Expect that we add no tools
    assert kwargs["tools"] == []
    client.client.messages.stream.reset_mock()

    # Tools in history, tools provided
    cur_message = [
        ChatRequestNode(
            id=0,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
                content="2025-03-19",
                is_error=False,
            ),
        ),
    ]
    chat_history[0].response_text.append(  # type: ignore
        ChatResultNode(
            id=1,
            type=ChatResultNodeType.TOOL_USE,
            content="",
            tool_use=ChatResultToolUse(
                name="get_date",
                input={},
                tool_use_id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
            ),
        ),
    )
    with pytest.raises(Exception, match="fail_for_testing"):
        stream = client.generate_response_stream(
            cur_message=cur_message,
            chat_history=chat_history,
            model_caller="test",
            tool_definitions=[
                ToolDefinition(
                    name="get_date",
                    description="Get the current date in YYYY-MM-DD format",
                    input_schema_json="{}",
                )
            ],
        )
        next(stream)
    kwargs = client.client.messages.stream.call_args[1]
    # Should not inject any extra tools; not needed
    assert kwargs["tools"] == [
        {
            "name": "get_date",
            "description": "Get the current date in YYYY-MM-DD format",
            "input_schema": {},
        }
    ]

    # Tools in history, no tools provided
    with pytest.raises(Exception, match="fail_for_testing"):
        stream = client.generate_response_stream(
            cur_message=cur_message,
            chat_history=chat_history,
            model_caller="test",
        )
        next(stream)
    kwargs = client.client.messages.stream.call_args[1]
    # Should inject a dummy tool
    assert kwargs["tools"] == [
        {
            "name": "dummy_tool",
            "description": "A placeholder tool. DO NOT CALL IT.",
            "input_schema": {
                "type": "object",
                "properties": {
                    "input": {
                        "type": "string",
                        "description": "The input to the dummy tool.",
                    },
                },
                "required": ["input"],
            },
        }
    ]


def test_bad_request_reasons():
    cases = [
        (
            "messages: text content blocks must contain non-whitespace text",
            "empty_content",
        ),
        (
            "tools.15.custom.input_schema: JSON schema is invalid. It must match JSON Schema draft 2020-12 "
            + "(https://json-schema.org/draft/2020-12). Learn more about tool use at https://docs.anthropic.com/en/docs/tool-use.",
            "invalid_tool_schema",
        ),
        (
            "tools.0.custom.name: String should match pattern '^[a-zA-Z0-9_-]{1,64}$'",
            "invalid_tool_name",
        ),
        (
            "Could not process image",
            "could_not_process_image",
        ),
        (
            "tools: Tool names must be unique.",
            "tool_names_not_unique",
        ),
        (
            "prompt is too long: 202612 tokens > 200000 maximum",
            "prompt_too_long",
        ),
        (
            "input length and `max_tokens` exceed context limit: 196880 + 8192 > 200000, decrease input length or `max_tokens` and try again",
            "prompt_too_long",
        ),
        (
            "messages.11.content.3: `tool_use` ids must be unique",
            "tool_use_ids_not_unique",
        ),
        (
            "messages.2.content.0: unexpected `tool_use_id` found in `tool_result` blocks: toolu_vrtx_01XUymT8443YU7DMaDycjs47. "
            "Each `tool_result` block must have a corresponding `tool_use` block in the previous message.",
            "tool_result_without_use",
        ),
        (
            "messages.308: `tool_use` ids were found without `tool_result` blocks immediately after: toolu_vrtx_01MnsLxb1CdSigP3NGcagDcR."
            "Each `tool_use` block must have a corresponding `tool_result` block in the next message.",
            "tool_use_without_result",
        ),
        (
            "Unknown error message",
            "unknown",
        ),
    ]
    response = httpx.Response(400, json={}, request=httpx.Request("GET", "http://test"))
    for message, expected_reason in cases:
        # This is essentially how the error is constructed in the SDK
        body = {
            "type": "error",
            "error": {"type": "invalid_request_error", "message": message},
        }
        err_msg = f"Error code: 400 - {body}"
        e = BadRequestError(err_msg, body=body, response=response)
        assert bad_request_reason_from_exception(e) == expected_reason
    # It's possible that 'body' is None (if there was no response) or a string (if couldn't decode json)
    # https://github.com/anthropics/anthropic-sdk-python/blob/8b244157a7d03766bec645b0e1dc213c6d462165/src/anthropic/_base_client.py#L398
    e = BadRequestError("raw response", body="raw response", response=response)
    assert bad_request_reason_from_exception(e) == "unknown"
    e = BadRequestError("no response", body=None, response=response)
    assert bad_request_reason_from_exception(e) == "unknown"
    # Test an unexpected json body
    e = BadRequestError("message", body={"foo": "bar"}, response=response)
    assert bad_request_reason_from_exception(e) == "unknown"


@pytest.mark.parametrize(
    "header_value, mock_now_offset_seconds, expected_seconds",
    [
        # Valid integer string
        ("60", None, 60),
        ("0", None, 0),
        # Valid HTTP-date string in the future
        (
            "Wed, 21 Oct 2025 07:28:00 GMT",
            (
                datetime(2025, 10, 21, 7, 27, 0, tzinfo=timezone.utc)
                - datetime(1970, 1, 1, tzinfo=timezone.utc)
            ).total_seconds(),  # Mock now to be 1 min before header
            60,
        ),
        (  # Test with a different future date and time
            "Fri, 10 Nov 2023 10:00:00 GMT",
            (
                datetime(2023, 11, 10, 9, 55, 0, tzinfo=timezone.utc)
                - datetime(1970, 1, 1, tzinfo=timezone.utc)
            ).total_seconds(),  # Mock now to be 5 mins before header
            300,  # 5 minutes
        ),
        # Valid HTTP-date string in the past
        (
            "Wed, 21 Oct 2015 07:28:00 GMT",
            (
                datetime(2025, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
                - datetime(1970, 1, 1, tzinfo=timezone.utc)
            ).total_seconds(),  # Mock now to be far in the future
            0,  # Expect 0 as date is in the past
        ),
        # Malformed/unparseable date string
        ("not-a-date", None, None),
        # Header value is None
        (None, None, None),
        # Header value is already an int
        (120, None, 120),
        (0, None, 0),
        # Header value is an empty string
        ("", None, None),
        # Header value is a non-digit, non-date-like string (but parseable by parsedate_to_datetime as invalid)
        ("invalid string but somewhat date-like", None, None),
        # String that is not a number and not a valid date format for parsedate_to_datetime
        ("gibberish", None, None),
        # String with digits but not purely a number (e.g., "120s") - should be treated as unparseable date
        ("120s", None, None),
    ],
)
def test_parse_retry_after_header(
    header_value,
    mock_now_offset_seconds,
    expected_seconds,
    caplog,  # caplog is still a fixture, but we won't use it directly for assertions
):
    """Tests the _parse_retry_after_header method with various inputs."""
    # Create a fresh client instance for this test
    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="test-key",  # pragma: allowlist secret
        model_name="test-model",
        temperature=0,  # Irrelevant for this test but required
        max_output_tokens=1,  # Irrelevant for this test but required
    )
    # Configure caplog to capture DEBUG level logs and above for this test,
    # even if we don't assert on them, they might be useful for debugging.
    caplog.set_level("DEBUG")

    actual_seconds = None
    if mock_now_offset_seconds is not None:
        # mock_now_datetime is seconds since epoch, convert to datetime
        mock_now_dt = datetime(1970, 1, 1, tzinfo=timezone.utc) + timedelta(
            seconds=mock_now_offset_seconds
        )
        with patch(
            "base.third_party_clients.anthropic_direct_client.datetime", autospec=True
        ) as mock_datetime_module:
            mock_datetime_module.now.return_value = mock_now_dt

            def datetime_side_effect(*args, **kwargs):
                if len(args) > 1 or kwargs:
                    return datetime(*args, **kwargs)
                return mock_datetime_module

            mock_datetime_module.side_effect = datetime_side_effect

            actual_seconds = client._parse_retry_after_header(header_value)
    else:
        actual_seconds = client._parse_retry_after_header(header_value)

    assert actual_seconds == expected_seconds

    """Test that the client correctly processes events from the Anthropic API."""
    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="fake-api-key",  # pragma: allowlist secret
        model_name="claude-3-7-sonnet-20250219",
        temperature=0.2,
        max_output_tokens=10,
    )

    mock_messages = MagicMock()
    mock_stream = MagicMock()
    mock_stream.return_value.__enter__.side_effect = Exception("fail_for_testing")
    mock_messages.stream = mock_stream

    # Replace the client's messages attribute with our mock
    client.client = MagicMock()
    client.client.messages = mock_messages

    cur_message = [
        ChatRequestNode(
            id=0,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
                content="2025-03-19",
                is_error=False,
            ),
        ),
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content="Hello world!"),
            tool_result_node=None,
        ),
    ]
    single_exchange = Exchange(
        request_message=[
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
                    content="2025-03-19",
                    is_error=False,
                ),
            ),
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(content="Steering..."),
                tool_result_node=None,
            ),
        ],
        response_text=[
            ChatResultNode(
                id=0,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="I'm here to help!",
            ),
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.TOOL_USE,
                content="",
                tool_use=ChatResultToolUse(
                    name="get_date",
                    input={},
                    tool_use_id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
                ),
            ),
        ],
    )

    def run_test_case(chat_history):
        with pytest.raises(Exception, match="fail_for_testing"):
            next(
                client.generate_response_stream(
                    model_caller="test_caller",
                    cur_message=cur_message,
                    chat_history=chat_history,
                    use_caching=True,
                )
            )
        kwargs = client.client.messages.stream.call_args[1]
        assert kwargs["extra_headers"] == {
            "anthropic-beta": "prompt-caching-2024-07-31"
        }
        messages = kwargs["messages"]
        breakpoint_positions = []
        for message_idx, message in enumerate(messages):
            for content_idx, content in enumerate(message["content"]):
                if "cache_control" in content:
                    breakpoint_positions.append((message_idx, content_idx))
        return breakpoint_positions

    # Cache tool result block in user message
    assert run_test_case([]) == [(0, 0)]
    # Cache tool result block, entire history, and more positions as the conversation grows
    assert run_test_case([single_exchange]) == [(1, 1), (2, 0)]
    assert run_test_case([single_exchange] * 2) == [(3, 1), (4, 0)]
    assert run_test_case([single_exchange] * 3) == [(4, 1), (5, 1), (6, 0)]
    # Cache tool result block, full history, 90%
    assert run_test_case([single_exchange] * 50) == [
        (90, 1),
        (99, 1),
        (100, 0),
    ]


def test_tool_result_empty_error():
    client = anthropic_direct_client.AnthropicDirectClient(
        api_key="fake-api-key",  # pragma: allowlist secret
        model_name="claude-3-7-sonnet-20250219",
        temperature=0.2,
        max_output_tokens=10,
    )
    client.client = MagicMock()
    client.client.messages = MagicMock()
    client.client.messages.stream.side_effect = Exception("fail_for_testing")

    cur_message = [
        ChatRequestNode(
            id=0,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
                content="",
                is_error=True,
            ),
        )
    ]
    with pytest.raises(Exception, match="fail_for_testing"):
        next(
            client.generate_response_stream(
                model_caller="test_caller",
                cur_message=cur_message,
                use_caching=False,
            )
        )
    assert client.client.messages.stream.call_args[1]["messages"][-1]["content"][
        -1
    ] == {
        "type": "tool_result",
        "tool_use_id": "toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx",
        "is_error": True,
        "content": "Unknown error occurred",
    }


def test_create_error_details_from_bad_request_tool_use_without_result():
    """Test creating error details from BadRequestError for tool use without result."""
    # Create a mock BadRequestError
    mock_error = Mock(spec=BadRequestError)
    mock_error.message = (
        "`tool_use` ids were found without `tool_result` blocks immediately after"
    )
    mock_error.body = {
        "error": {
            "message": "`tool_use` ids were found without `tool_result` blocks immediately after"
        }
    }

    details = create_error_details_from_bad_request(mock_error)

    assert details.code == ErrorCode.INVALID_TOOL_USE_HISTORY
    assert details.message == "Invalid tool use history"
    assert (
        details.detail == "Tool use block found without corresponding tool result block"
    )


def test_create_error_details_from_bad_request_duplicate_tool_names():
    """Test creating error details from BadRequestError for duplicate tool names."""
    mock_error = Mock(spec=BadRequestError)
    mock_error.message = "Tool names must be unique"
    mock_error.body = {"error": {"message": "Tool names must be unique"}}

    details = create_error_details_from_bad_request(mock_error)

    assert details.code == ErrorCode.DUPLICATE_TOOL_NAMES
    assert details.message == "Duplicate tool names"
    assert details.detail == "Tool names must be unique within a request"


def test_create_error_details_from_bad_request_prompt_too_long():
    """Test creating error details from BadRequestError for prompt too long."""
    mock_error = Mock(spec=BadRequestError)
    mock_error.message = "input length and `max_tokens` exceed context limit"
    mock_error.body = {
        "error": {"message": "input length and `max_tokens` exceed context limit"}
    }

    details = create_error_details_from_bad_request(mock_error)

    assert details.code == ErrorCode.PROMPT_LENGTH_EXCEEDED
    assert details.message == "Prompt length exceeded"
    assert (
        details.detail
        == "The input and max output tokens exceed the model's context limit"
    )


def test_create_error_details_from_bad_request_invalid_tool_name():
    """Test creating error details from BadRequestError for invalid tool name."""
    mock_error = Mock(spec=BadRequestError)
    mock_error.message = "tools.0.custom.name: String should match pattern"
    mock_error.body = {
        "error": {"message": "tools.0.custom.name: String should match pattern"}
    }

    details = create_error_details_from_bad_request(mock_error)

    assert details.code == ErrorCode.INVALID_TOOL_DEFINITION
    assert details.message == "Invalid tool definition"
    assert (
        details.detail
        == "Tool name does not match the required pattern ^[a-zA-Z0-9_-]{1,64}$"
    )


def test_create_error_details_from_bad_request_unknown_error():
    """Test creating error details from BadRequestError for unknown error."""
    mock_error = Mock(spec=BadRequestError)
    mock_error.message = "Some unknown error message"
    mock_error.body = {"error": {"message": "Some unknown error message"}}

    details = create_error_details_from_bad_request(mock_error)

    # Unknown errors return None
    assert details is None


def test_create_error_details_from_bad_request_no_body():
    """Test creating error details from BadRequestError with no body."""
    mock_error = Mock(spec=BadRequestError)
    mock_error.message = "Error message"
    mock_error.body = None

    details = create_error_details_from_bad_request(mock_error)

    # Errors with no body return None
    assert details is None
