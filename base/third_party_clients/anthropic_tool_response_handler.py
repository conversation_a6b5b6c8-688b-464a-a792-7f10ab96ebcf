"""Handling streaming of tool responses from Anthropic."""

import re
from json import loads
from typing import Iterator, Optional, cast

import prometheus_client
from anthropic import MessageStream, TextEvent

from base.third_party_clients.third_party_model_client import (
    ReplaceTextResponse,
    ThirdPartyModelResponse,
)

_instructions_event_type_counter = prometheus_client.Counter(
    "au_anthropic_tool_use_handler_instructions_event_type",
    "Number of events from Anthropic client for instructions",
    ["event_type"],
)


def get_prefix_set(s):
    prefix_set = set()
    for i in range(1, len(s)):
        prefix_set.add(s[:i])
    return prefix_set


def get_num_trailing_chars(s: str, c: str) -> int:
    total = 0
    i = len(s) - 1
    while i >= 0:
        if s[i] != c:
            return total
        total += 1
        i -= 1
    return total


class CodeInstructionsHandlerV2:
    def __init__(self):
        self.buffer = ""
        self.finished = False

    def do_yield(self, replacement_text: Optional[str] = None):
        if not replacement_text:
            return
        values = {
            "text": "",
            "old_text": None,
            "replacement_text": replacement_text,
            "start_line_number": None,
            "end_line_number": None,
            "sequence_id": 0,
        }
        yield ThirdPartyModelResponse(
            text=values.pop("text"), replace_text_response=ReplaceTextResponse(**values)
        )

    def parse(self, partial_text) -> Iterator[ThirdPartyModelResponse]:
        self.buffer += partial_text

        if self.finished:
            return

        # First, we look for the closing pattern
        closing_pattern = r">>>>>>> *updated\n?"
        closing_pattern_match = re.search(closing_pattern, self.buffer)
        if closing_pattern_match:
            # If we find closing pattern, we yield everything before it and finish
            yield from self.do_yield(
                replacement_text=self.buffer[: closing_pattern_match.start()]
            )
            self.buffer = ""
            self.finished = True
            return

        # If we didn't find closing pattern, we look for ">>>>>>>" at the beginning of some line
        possible_start_of_closing_match = re.search(
            r"^>>>>>>>", self.buffer, re.MULTILINE
        )
        if possible_start_of_closing_match:
            # If there is such line, then the generation of closing pattern might've already started
            # So we're safe to yield only values prior to ">>>>>>>"
            start_of_closing_pos = possible_start_of_closing_match.start()
            yield from self.do_yield(
                replacement_text=self.buffer[:start_of_closing_pos]
            )
            self.buffer = self.buffer[start_of_closing_pos:]
        else:
            # If there is no such line, then either:
            # 1. We only generated a part of ">>>>>>>"
            # 2. We didn't start generating ">>>>>>>" yet, at all
            num_trailing_signs = get_num_trailing_chars(self.buffer, ">")
            if num_trailing_signs > 0:
                yield from self.do_yield(
                    replacement_text=self.buffer[:-num_trailing_signs]
                )
                self.buffer = self.buffer[-num_trailing_signs:]
            else:
                yield from self.do_yield(replacement_text=self.buffer)
                self.buffer = ""


def stream_code_instructions_v2_on_third_party_model_response(
    third_party_model_responses: Iterator[ThirdPartyModelResponse],
):
    handler = CodeInstructionsHandlerV2()
    is_very_first_chunk = True
    for response in third_party_model_responses:
        if response.text:
            text = response.text
            # Claude API doesn't allow to put trailing \n in prefilled response.
            # So the model response will start with \n which we need to ignore.
            if is_very_first_chunk and text.startswith("\n"):
                text = text[1:]
                is_very_first_chunk = False
            yield from handler.parse(text)
        yield response
