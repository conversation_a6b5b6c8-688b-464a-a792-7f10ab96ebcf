import functools
import json
from dataclasses import asdict, dataclass
from enum import Enum
from typing import (
    Any,
    Callable,
    Generator,
    Optional,
    Protocol,
    runtime_checkable,
)

import dataclasses_json

from base.prompt_format.common import (
    Exchange,
    RequestMessage,
    StopReason,
    ToolDefinition,
)
from base.third_party_clients.token_counter.token_counter import Token<PERSON>ounter


@dataclass
class ReplaceTextResponse:
    """
    A class to represent a replace text tool use response from a third-party model.
    """

    old_text: str | None
    """The original text."""

    replacement_text: str | None
    """The replacement text."""

    start_line_number: int | None
    """The start line number."""

    end_line_number: int | None
    """The end line number."""

    sequence_id: int
    """The sequence id of the replace text block currently streaming."""


@dataclass
class ToolUseResponse:
    """
    A class to represent a tool use response from a third-party model.
    """

    tool_name: str
    """The name of the tool that the model wants to use."""

    input: dict[str, Any]
    """The input to pass to the tool."""

    tool_use_id: str
    """The ID for the tool request."""

    is_partial: bool = False
    """Whether this is a partial tool use response."""

    def to_json(self) -> str:
        return json.dumps(asdict(self))


@dataclass
class ToolUseStart:
    tool_name: str
    """The name of the tool that the model wants to use."""

    tool_use_id: str
    """The ID for the tool request."""


@dataclass
class PromptCacheUsage:
    """
    A class to represent prompt cache usage for a third-party model.
    """

    input_tokens: int
    """The number of (uncached) input tokens."""

    cache_creation_input_tokens: int
    """The number of input tokens used to create the cache."""

    cache_read_input_tokens: int
    """The number of input tokens used to read from the cache."""

    text_input_tokens: int = 0
    """The number of input tokens in the current message (excluding tool result)."""

    tool_input_tokens: int = 0
    """The number of input tokens in the tool result that is sent as part of the current message."""

    text_output_tokens: int = 0
    """The number of output tokens in the current response from the model (excluding tool use inputs)"""

    tool_output_tokens: int = 0
    """The number of output tokens in the tool input generated by the model"""

    model_caller: str | None = None
    """The model caller."""

    @property
    def total_input_tokens(self) -> int:
        return (
            self.input_tokens
            + self.cache_read_input_tokens
            + self.cache_creation_input_tokens
        )

    @property
    def cache_ratio(self) -> float:
        return self.cache_read_input_tokens / (self.total_input_tokens + 1e-6)


@dataclass
class EndOfStream:
    stop_reason: StopReason
    """The reason for the stop."""

    output_tokens: int | None = None
    """The number of output tokens generated."""

    prompt_cache_usage: PromptCacheUsage | None = None
    """The prompt cache usage information."""


@dataclass
class ThirdPartyModelResponse:
    """
    A class to represent a request to a third-party model.
    """

    text: str
    """The generated text from the third-party model."""

    replace_text_response: ReplaceTextResponse | None = None

    tool_use: ToolUseResponse | None = None
    tool_use_start: ToolUseStart | None = None

    prompt_cache_usage: PromptCacheUsage | None = None

    final_parameters: dict[str, Any] | None = None

    end_of_stream: EndOfStream | None = None

    reasoning_content: str | None = None
    """Reasoning/thinking content from models that support it (e.g., OpenAI o1)."""

    def __str__(self):
        return self.text

    def is_model_output(self):
        # final_parameters -- model input
        # prompt_cache_usage / end_of_stream -- metadata
        # end_of_stream(MAX_TOKENS) -- indicates model was definitely generating; I'm calling this output
        return (
            self.text
            or self.replace_text_response is not None
            or self.tool_use is not None
            or self.tool_use_start is not None
            or (
                self.end_of_stream is not None
                and self.end_of_stream.stop_reason == StopReason.MAX_TOKENS
            )
        )


class ToolChoiceType(Enum):
    # The model can decide whether to use a tool or not.
    AUTO = 1
    # The model must use some tool.
    ANY = 2
    # The model must use the tool with the given name.
    TOOL = 3


@dataclass
class ToolChoice(dataclasses_json.DataClassJsonMixin):
    type: ToolChoiceType = ToolChoiceType.AUTO
    name: str | None = None
    disable_parallel_tool_use: bool = False


@dataclass
class ModelAPICall:
    """
    Information about an API call to a third-party model.
    """

    model_name: str
    """The name of the model that was called."""

    provider: str
    """The provider of the model (e.g., 'anthropic', 'openai')."""

    region: str | None = None
    """The region where the model is hosted, if applicable."""

    request_params: dict[str, Any] | None = None
    """The parameters sent in the request."""

    response_metadata: dict[str, Any] | None = None
    """Metadata about the response."""


@runtime_checkable
class ThirdPartyModelClient(Protocol):
    """
    A class to interact with a third-party model for generating responses.
    """

    def __init__(self):
        """
        Initialize the ThirdPartyModel.
        """
        raise NotImplementedError()

    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """
        Generate a response based on the given message.

        Args:
            messages: list[tuple[str, str]]: The messages to generate a response for in the format (user_m, assistant_m)
                where the last tuple only has the user_message. Deprecated in favor of `chat_history`.
            system_prompt (str): The system prompt to use for the model.
            cur_message (RequestMessage): The final User message to generate a response for.
            chat_history [list[Exchange] | None]: A structured format of `messages`.
            prefill (str | None): If provided, the model will start generating from this string.
            use_caching (bool): If True, we try using vendor-specific caching.
            tools (list[str]): List of tool names to use for the model.
                Tool usage will be returned as a ToolUseResponse in the sequence of responses. Due to issues
                parsing tool use responses in streaming mode for some implementations, use of tools may disable streaming.
                The exception is "replace_text" tool, which will produce ReplaceTextResponse results. See below.
            tool_choice: Control how the model uses tools
            retrieval_exchange (Exchange): Optional exchange containing retrieval results.
            model_caller (str): A field passed in by the caller (could be chat, agent, etc.) to keep track of the caller, for metrics purposes.
            request_context (Any): Optional request context containing information about the request, including session ID.
            yield_final_parameters (bool): If True, yield a ThirdPartyModelResponse with final_parameters set to the parameters used for the request.
            model_api_call_callback (Callable[[ModelAPICall], None]): Optional callback to record model API calls for request insight.

        All of these should be handleable by just returning text stream from the model, and parsing
        the result in the caller.

        Returns:
            Generator[ThirdPartyModelResponse]: A sequence of responses from the model.
        """
        raise NotImplementedError()

    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: The number of tokens in the given message.
        """
        raise NotImplementedError()

    def token_counter(self) -> TokenCounter:
        """
        Return a token counter appropriate for the model behind the client
        """

        class ClientTokenCounter(TokenCounter):
            def __init__(self, client):
                self.client = client

            @functools.lru_cache(maxsize=128)
            def count_tokens(self, prompt_chars: str) -> int:
                return self.client.count_tokens(prompt_chars)

        return ClientTokenCounter(self)
