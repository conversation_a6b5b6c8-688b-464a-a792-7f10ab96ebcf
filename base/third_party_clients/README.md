# Third Party Clients

This directory contains integration code for third-party language models and AI services that are used in our production chat systems.

## Overview

The code in this directory implements client interfaces and adapters for various third-party AI models that we leverage in our production environment. While these models are not trained or maintained by us, they are essential components of our service infrastructure.

## Purpose

- Provides standardized interfaces to interact with third-party AI models
- Handles authentication, rate limiting, and error handling for external API calls
- Implements necessary adapters to normalize responses across different providers
- Manages token counting and usage metrics for third-party services
- [soon]: connect to our Claude model proxy to enable better load balancing and robust error handling

## Usage

The clients in this directory should be used when integrating with external AI services. They provide consistent error handling and metrics collection across all third-party integrations.

## Best Practices

- Always use the provided client interfaces rather than calling external APIs directly
- Error handling: ensure that all errors are properly caught and logged
- Monitor token usage and API costs through the integrated metrics

## Note

When adding new third-party integrations, please ensure proper documentation and testing are included.
