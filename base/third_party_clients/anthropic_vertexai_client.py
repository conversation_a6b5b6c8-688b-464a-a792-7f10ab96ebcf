"""A client for Anthropic models via Vertex AI."""

import anthropic
import httpx
import structlog
from typing_extensions import override

from base.third_party_clients import anthropic_direct_client
from base.third_party_clients.prompt_usage_metrics import PromptUsageMetrics

MAX_RETRIES = 2  # Default is 2
# Default is httpx.Timeout(timeout=600.0, connect=5.0)
# We add a read timeout, but it's not very strict as it covers context processing
TIMEOUT = httpx.Timeout(timeout=280.0, connect=3.0, read=75.0)


class AnthropicVertexAiClient(anthropic_direct_client.AnthropicBaseClient):
    """
    A class to interact with Anthropic via VertexAi.
    """

    client_type = "anthropic_vertexai"

    def __init__(
        self,
        project_id,
        region,
        model_name,
        temperature,
        max_output_tokens,
    ):
        """
        Initialize the Anthropic VertexAi API with project details and model parameters.

        Args:
            project_id (str): The ID of the Google Cloud project.
            region (str): The region of the Google Cloud project.
            model_name (str): The name of the model to use for generating responses.
            temperature (float): The temperature parameter for controlling the randomness of the responses.
            max_output_tokens (int): The maximum number of tokens to generate in the response.
        """
        super().__init__(
            model_name=model_name,
            client=anthropic.AnthropicVertex(
                region=region,
                project_id=project_id,
                max_retries=MAX_RETRIES,
                timeout=TIMEOUT,
            ),
            temperature=temperature,
            max_output_tokens=max_output_tokens,
            provider_region=region,
        )
