"""
Shared library for prompt usage metrics tracking across third-party clients.

This module provides a unified way to track prompt-related metrics including
token usage, cache performance, and other prompt-specific measurements.
"""

import prometheus_client

from base.third_party_clients.third_party_model_client import PromptCacheUsage

# Global registry to store metrics instances to avoid duplicate registration
_METRICS_REGISTRY = {}


class PromptUsageMetrics:
    """
    A class to track prompt usage metrics for third-party model clients.

    This class encapsulates all prompt-related metrics including token counts,
    cache usage, and hit ratios. It provides a consistent interface for tracking
    these metrics across different third-party clients.
    """

    def __init__(self, model_name: str):
        """
        Initialize prompt usage metrics for a specific model.

        Args:
            model_name: The name of the model (used as a prefix for metric names)
        """
        self.model_name = model_name

        # Define common token buckets used across all metrics
        self._token_buckets = [
            0,
            100,
            200,
            500,
            1000,
            2000,
            5000,
            10000,
            20000,
            50000,
            60000,
            70000,
            80000,
            90000,
            100000,
            200000,
            500000,
            float("inf"),
        ]

        # Define cache hit ratio buckets (0% to 100% in 5% increments)
        self._cache_ratio_buckets = [x * 0.05 for x in range(21)]

        # Common label names for all metrics
        self._label_names = ["model_caller", "model", "client_type", "provider_region"]

        # Initialize all metrics
        self._init_metrics()

    def _init_metrics(self):
        """Initialize all Prometheus metrics."""
        metric_prefix = f"au_{self.model_name.lower()}_prompt"

        # Check if metrics are already registered for this model
        if metric_prefix in _METRICS_REGISTRY:
            # Reuse existing metrics
            metrics = _METRICS_REGISTRY[metric_prefix]
            self.total_input_tokens = metrics["total_input_tokens"]
            self.input_tokens = metrics["input_tokens"]
            self.cache_read = metrics["cache_read"]
            self.cache_write = metrics["cache_write"]
            self.cache_hit_ratio = metrics["cache_hit_ratio"]
            return

        self.total_input_tokens = prometheus_client.Histogram(
            f"{metric_prefix}_total_input_tokens",
            labelnames=self._label_names,
            documentation="Total tokens in the prompt input for a given request, including cache reads and writes",
            buckets=self._token_buckets,
        )

        self.input_tokens = prometheus_client.Histogram(
            f"{metric_prefix}_input_tokens",
            labelnames=self._label_names,
            documentation="Tokens in the prompt input for a given request, not including cache reads and writes",
            buckets=self._token_buckets,
        )

        self.cache_read = prometheus_client.Histogram(
            f"{metric_prefix}_cache_read",
            labelnames=self._label_names,
            documentation="Cache read input tokens for a given request",
            buckets=self._token_buckets,
        )

        self.cache_write = prometheus_client.Histogram(
            f"{metric_prefix}_cache_write",
            labelnames=self._label_names,
            documentation="Cache write input tokens for a given request",
            buckets=self._token_buckets,
        )

        self.cache_hit_ratio = prometheus_client.Histogram(
            f"{metric_prefix}_cache_hit_ratio",
            labelnames=self._label_names,
            documentation="Percentage of input tokens found in cache",
            buckets=self._cache_ratio_buckets,
        )

        # Store metrics in global registry
        _METRICS_REGISTRY[metric_prefix] = {
            "total_input_tokens": self.total_input_tokens,
            "input_tokens": self.input_tokens,
            "cache_read": self.cache_read,
            "cache_write": self.cache_write,
            "cache_hit_ratio": self.cache_hit_ratio,
        }

    def observe(
        self,
        model_caller: str,
        model: str,
        client_type: str,
        provider_region: str,
        prompt_cache_usage: PromptCacheUsage,
    ):
        """
        Record observations for all prompt usage metrics.

        Args:
            model_caller: The caller of the model (e.g., "chat", "agent")
            model: The specific model name
            client_type: The type of client (e.g., "anthropic_direct", "google_genai")
            provider_region: The provider region
            total_input_tokens: Total input tokens including cache reads and writes
            input_tokens: Input tokens not including cache reads and writes
            cache_read_tokens: Number of tokens read from cache
            cache_write_tokens: Number of tokens written to cache
        """
        labels = {
            "model_caller": model_caller,
            "model": model,
            "client_type": client_type,
            "provider_region": provider_region,
        }

        self.total_input_tokens.labels(**labels).observe(
            prompt_cache_usage.total_input_tokens
        )
        self.input_tokens.labels(**labels).observe(prompt_cache_usage.input_tokens)
        self.cache_read.labels(**labels).observe(
            prompt_cache_usage.cache_read_input_tokens
        )
        self.cache_write.labels(**labels).observe(
            prompt_cache_usage.cache_creation_input_tokens
        )
        self.cache_hit_ratio.labels(**labels).observe(prompt_cache_usage.cache_ratio)
