import random
import string

import pytest

from base import tokenizers
from base.prompt_format.common import (
    ToolResultContentNode,
    ToolResultContentNodeType,
    ChatRequestImage,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    ImageFormatType,
)
from base.third_party_clients.token_counter import token_counter
from base.third_party_clients.token_counter.token_counter import RoughTokenCounter
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer


def generate_random_case():
    random.seed(42)
    text_length = int(10 ** random.uniform(2, 6.30103))  # log10(2,000,000) ≈ 6.30103
    text = "".join(random.choices(string.printable, k=text_length))
    budget = random.randint(8000, 32000)
    indicator = "".join(random.choices(string.ascii_letters, k=random.randint(1, 5)))
    reveresed = random.choice([True, False])
    return (text, budget, indicator, reveresed)


class TestTokenCounter:
    @pytest.mark.parametrize(
        "text,budget,indicator,reveresed", [generate_random_case() for _ in range(1000)]
    )
    def test_truncate_to_budget_random(self, text, budget, indicator, reveresed):
        token_counter = RoughTokenCounter()
        result = token_counter.truncate_to_budget(text, budget, indicator, reveresed)
        total_token_count = token_counter.count_tokens(text)
        token_count = token_counter.count_tokens(result)

        assert token_count <= budget, "Budget exceeded"
        assert (
            token_count >= budget - 3 or total_token_count < budget
        ), "Too far from budget"
        if reveresed:
            assert result.startswith(indicator) or result.startswith(
                text
            ), f"{result[:len(indicator)]} != {indicator}"
        else:
            assert result.endswith(indicator) or result.endswith(
                text
            ), f"{result[-len(indicator):]} != {indicator}"

    def test_tool_use(self):
        token_counter = RoughTokenCounter()
        input_text = "A" * 117
        token_count = token_counter.count_tokens(input_text)
        tool_use_token_count = token_counter.count_tokens_in_response(
            [
                ChatResultNode(
                    id=1,
                    content="",
                    type=ChatResultNodeType.TOOL_USE,
                    tool_use=ChatResultToolUse(
                        name="tool_name",
                        input={"tool_param": input_text},
                        tool_use_id="tool_use_id",
                    ),
                )
            ]
        )

        # Validate that tool use uses up at least as many tokens as just the input
        # itself.
        assert tool_use_token_count >= token_count

        tool_use_token_count2 = token_counter.count_tokens_in_response(
            [
                ChatResultNode(
                    id=1,
                    content=input_text,
                    type=ChatResultNodeType.RAW_RESPONSE,
                ),
                ChatResultNode(
                    id=1,
                    content="",
                    type=ChatResultNodeType.TOOL_USE,
                    tool_use=ChatResultToolUse(
                        name="tool_name",
                        input={"tool_param": input_text},
                        tool_use_id="tool_use_id",
                    ),
                ),
            ]
        )

        # Validate that tool use uses up at least as many tokens as just the input
        # itself.
        assert tool_use_token_count2 >= 2 * token_count

        # This is an alternate encoding of the same tool use
        tool_use_token_count3 = token_counter.count_tokens_in_response(
            [
                ChatResultNode(
                    id=1,
                    content=input_text,
                    type=ChatResultNodeType.TOOL_USE,
                    tool_use=ChatResultToolUse(
                        name="tool_name",
                        input={"tool_param": input_text},
                        tool_use_id="tool_use_id",
                    ),
                )
            ]
        )
        assert tool_use_token_count3 >= 2 * token_count

    def test_tool_result(self):
        token_counter = RoughTokenCounter()
        text = "A" * 117
        token_count = token_counter.count_tokens(text)
        tool_result_token_count = token_counter.count_tokens_in_request(
            [
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool_use_id",
                        content=text,
                        is_error=False,
                        request_id="tool_result_request_id",
                    ),
                    text_node=None,
                    image_node=None,
                )
            ]
        )

        # Validate that tool result uses up at least as many tokens as just the output
        # itself.
        assert tool_result_token_count >= token_count

    def test_image_result(self):
        token_counter = RoughTokenCounter()
        # Test with a sample base64 encoded PNG image data, this is a 100x100 pixel pure green image
        base64_image = "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAA5klEQVR4nO3QQQkAIADAQDW50a3gXiLcJRibYw8urdcBPzErMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCs4MosBSL/dChIAAAAASUVORK5CYII="
        image_token_count = token_counter.count_tokens_in_request(
            [
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.IMAGE,
                    tool_result_node=None,
                    text_node=None,
                    image_node=ChatRequestImage(
                        image_data=base64_image, format=ImageFormatType.PNG
                    ),
                )
            ]
        )

        # Validate that image nodes use a non-zero number of tokens
        assert image_token_count > 0, "Image node should consume tokens"

        # Test multiple image nodes
        multiple_image_token_count = token_counter.count_tokens_in_request(
            [
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.IMAGE,
                    tool_result_node=None,
                    text_node=None,
                    image_node=ChatRequestImage(
                        image_data=base64_image, format=ImageFormatType.PNG
                    ),
                ),
                ChatRequestNode(
                    id=2,
                    type=ChatRequestNodeType.IMAGE,
                    tool_result_node=None,
                    text_node=None,
                    image_node=ChatRequestImage(
                        image_data=base64_image, format=ImageFormatType.PNG
                    ),
                ),
            ]
        )

        # Validate that multiple image nodes sum their token counts
        assert multiple_image_token_count > image_token_count

    def test_tool_result_with_images(self):
        token_counter = RoughTokenCounter()
        # Test with a sample base64 encoded PNG image data, this is a 100x100 pixel pure green image
        base64_image = "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAA5klEQVR4nO3QQQkAIADAQDW50a3gXiLcJRibYw8urdcBPzErMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCswKzArMCs4MosBSL/dChIAAAAASUVORK5CYII="

        # Test tool result with text content only
        text_content = "This is a tool result with text content"
        text_tool_result_token_count = token_counter.count_tokens_in_request(
            [
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool_use_id",
                        content=text_content,
                        is_error=False,
                        request_id="tool_result_request_id",
                        content_nodes=None,
                    ),
                    text_node=None,
                    image_node=None,
                )
            ]
        )

        # Test tool result with content_nodes containing text and image
        content_nodes_tool_result_token_count = token_counter.count_tokens_in_request(
            [
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool_use_id",
                        content="",  # Empty content since we're using content_nodes
                        is_error=False,
                        request_id="tool_result_request_id",
                        content_nodes=[
                            ToolResultContentNode(
                                type=ToolResultContentNodeType.CONTENT_TEXT,
                                text_content=text_content,
                                image_content=None,
                            ),
                            ToolResultContentNode(
                                type=ToolResultContentNodeType.CONTENT_IMAGE,
                                text_content=None,
                                image_content=ChatRequestImage(
                                    image_data=base64_image, format=ImageFormatType.PNG
                                ),
                            ),
                        ],
                    ),
                    text_node=None,
                    image_node=None,
                )
            ]
        )

        # Validate that tool result with content_nodes containing an image uses more tokens
        # than just the text content alone
        assert content_nodes_tool_result_token_count > text_tool_result_token_count

        # Test tool result with multiple content_nodes containing images
        multiple_images_tool_result_token_count = token_counter.count_tokens_in_request(
            [
                ChatRequestNode(
                    id=1,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="tool_use_id",
                        content="",  # Empty content since we're using content_nodes
                        is_error=False,
                        request_id="tool_result_request_id",
                        content_nodes=[
                            ToolResultContentNode(
                                type=ToolResultContentNodeType.CONTENT_TEXT,
                                text_content=text_content,
                                image_content=None,
                            ),
                            ToolResultContentNode(
                                type=ToolResultContentNodeType.CONTENT_IMAGE,
                                text_content=None,
                                image_content=ChatRequestImage(
                                    image_data=base64_image, format=ImageFormatType.PNG
                                ),
                            ),
                            ToolResultContentNode(
                                type=ToolResultContentNodeType.CONTENT_IMAGE,
                                text_content=None,
                                image_content=ChatRequestImage(
                                    image_data=base64_image, format=ImageFormatType.PNG
                                ),
                            ),
                        ],
                    ),
                    text_node=None,
                    image_node=None,
                )
            ]
        )

        # Validate that tool result with multiple images uses more tokens
        assert (
            multiple_images_tool_result_token_count
            > content_nodes_tool_result_token_count
        )


def test_token_based_token_counter():
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    counter = token_counter.TokenizerBasedTokenCounter(tokenizer)
    assert counter.count_tokens("Hello world!") == 3
