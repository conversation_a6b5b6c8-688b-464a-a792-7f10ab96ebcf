import functools
from base.third_party_clients.token_counter.token_counter import Token<PERSON>ounter
from vertexai.preview.tokenization import get_tokenizer_for_model


class VertexTokenCounter(TokenCounter):
    def __init__(self, model_name: str):
        self.tokenizer = get_tokenizer_for_model(model_name)

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        count_token_result = self.tokenizer.count_tokens(prompt_chars)
        return count_token_result.total_tokens
