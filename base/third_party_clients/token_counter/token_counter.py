"""Module containing token counter classes."""

import base64
import bisect
import functools
import io
import typing

from PIL import Image

from base.prompt_format.common import (
    ToolResultContentNodeType,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    Exchange,
    RequestMessage,
    ResponseMessage,
)
from base.tokenizers.tokenizer import Tokenizer


class TokenCounter(typing.Protocol):
    """A protocol for counting tokens."""

    def count_tokens(self, prompt_chars: str) -> int:
        """Count the number of tokens in the prompt."""
        raise NotImplementedError()

    def _calculate_image_tokens(self, image_data: str) -> int:
        """Calculate the number of tokens for an image using <PERSON><PERSON><PERSON>'s formula

        The formula is: tokens = (width * height) / 750

        Args:
            image_data: Raw image data in bytes

        Returns:
            int: Number of tokens required for the image

        Note:
            If image dimensions cannot be read, falls back to a conservative estimate
            based on image data size assuming RGB format (3 bytes per pixel).
        """
        try:
            # Load image from base64 str and get dimensions
            img_io = io.BytesIO(base64.b64decode(image_data))
            img = Image.open(img_io)
            width, height = img.size

            # Calculate tokens using Anthropic's official formula
            # Reference: https://docs.anthropic.com/en/docs/build-with-claude/vision#calculate-image-costs
            return int((width * height) / 750)
        except Exception:
            # Fallback calculation if we can't process the image
            # Assume roughly 3 bytes per pixel (RGB) and apply the same formula
            estimated_pixels = len(image_data) / 3
            return int(estimated_pixels / 750)

    def count_tokens_in_request(
        self,
        request_message: RequestMessage,
        node_type: ChatRequestNodeType | None = None,
    ) -> int:
        """Count the number of tokens in the request."""
        if isinstance(request_message, str):
            if node_type is None or node_type == ChatRequestNodeType.TEXT:
                return self.count_tokens(request_message)
            return 0
        return sum(
            self._count_tokens_in_request_node(node)
            for node in request_message
            if node_type is None or node.type == node_type
        )

    def count_tokens_in_response(self, response_message: ResponseMessage) -> int:
        """Count the number of tokens in the response."""
        if isinstance(response_message, str):
            return self.count_tokens(response_message)
        return sum(
            self._count_tokens_in_response_node(node) for node in response_message
        )

    def count_tokens_in_exchange(self, exchange: Exchange) -> int:
        """Count the number of tokens in the exchange."""
        return self.count_tokens_in_request(
            exchange.request_message
        ) + self.count_tokens_in_response(exchange.response_text)

    def count_tokens_in_chat_history(self, chat_history: list[Exchange]) -> int:
        """Count the number of tokens in the chat history."""
        return sum(self.count_tokens_in_exchange(exchange) for exchange in chat_history)

    def _count_tokens_in_request_node(self, node: ChatRequestNode) -> int:
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            return self.count_tokens(node.text_node.content)
        if node.type == ChatRequestNodeType.TOOL_RESULT:
            assert node.tool_result_node is not None
            # Check if the tool result has content_nodes with images
            if node.tool_result_node.content_nodes:
                total_tokens = 0
                for content_node in node.tool_result_node.content_nodes:
                    if content_node.type == ToolResultContentNodeType.CONTENT_TEXT:
                        assert content_node.text_content is not None
                        total_tokens += self.count_tokens(content_node.text_content)
                    elif content_node.type == ToolResultContentNodeType.CONTENT_IMAGE:
                        assert content_node.image_content is not None
                        total_tokens += self._calculate_image_tokens(
                            content_node.image_content.image_data
                        )
                return total_tokens
            # Fall back to the content field if no content_nodes
            return self.count_tokens(node.tool_result_node.content)
        if node.type == ChatRequestNodeType.IMAGE:
            assert node.image_node is not None
            return self._calculate_image_tokens(node.image_node.image_data)
        if node.type == ChatRequestNodeType.IDE_STATE:
            assert node.ide_state_node is not None
            # NOTE(arun): we actually add these counts outside this function because
            # we format it as a text node and that depends on previous nodes and the
            # prompt template.
            return 0
        if node.type == ChatRequestNodeType.EDIT_EVENTS:
            assert node.edit_events_node is not None
            # TODO(arun): Actually estimate costs based on the size of the diff.
            return 0
        raise ValueError(f"Unsupported node type: {node.type}")

    def _count_tokens_in_response_node(self, node: ChatResultNode) -> int:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            return self.count_tokens(node.content)
        if node.type == ChatResultNodeType.TOOL_USE:
            assert node.tool_use is not None
            return self.count_tokens(
                f"{node.content} {node.tool_use.name} {node.tool_use.input}"
            )
        raise ValueError(f"Unsupported node type: {node.type}")

    def truncate_to_budget(
        self,
        text: str,
        token_budget: int,
        truncation_indicator: str = "",
        reversed: bool = False,
    ) -> str:
        """Truncate the text to fit within the given token budget."""
        text_count = self.count_tokens(text)
        if text_count <= token_budget:
            return text
        indicator_count = self.count_tokens(truncation_indicator)
        if indicator_count > token_budget:
            return ""
        token_budget -= indicator_count

        def key_func(i):
            if not reversed:
                return self.count_tokens(text[:i])
            else:
                return self.count_tokens(text[-i:]) if i > 0 else 0

        cut_point = bisect.bisect_left(
            range(len(text)),
            token_budget,
            key=key_func,
        )
        if not reversed:
            return text[:cut_point] + truncation_indicator
        if cut_point == 0:
            return truncation_indicator
        return truncation_indicator + text[-cut_point:]


class TokenizerBasedTokenCounter(TokenCounter):
    """A token counter that uses a tokenizer."""

    def __init__(self, tokenizer: Tokenizer):
        self.tokenizer = tokenizer

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        return len(self.tokenizer.tokenize_safe(prompt_chars))


class RoughTokenCounter(TokenCounter):
    """A token counter that uses a rough character-based approximation."""

    def count_tokens(self, prompt_chars: str) -> int:
        return int(len(prompt_chars) / 3)
