import functools
import threading

from base.third_party_clients.token_counter.token_counter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    TokenizerBasedTokenCounter,
)

from base.tokenizers import Llama3InstructTokenizer

"""
The best local approximation we currently have for <PERSON> token count is 1.3 * Llama3 instruct token count

To protect against undercounting due to variance, we take an extra 10% buffer

See # See https://www.notion.so/Claude-token-count-statistics-161bba10175a80c99ff3c6a0d57f73a3
"""

LLAMA3_CLAUDE_MEAN_RATIO = 1.3
SAFETY_MARGIN = 1.1


class ClaudeTokenCounter(TokenCounter):
    # Static tokenizer on class to be reused by all instances
    _token_counter = None
    _instance_lock = threading.Lock()

    @classmethod
    def get_token_counter(cls):
        if cls._token_counter is None:
            with cls._instance_lock:
                if cls._token_counter is None:
                    cls._token_counter = TokenizerBasedTokenCounter(
                        Llama3InstructTokenizer()
                    ).count_tokens
        return cls._token_counter

    def __init__(self):
        self.count_tokens_internal = self.get_token_counter()

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        return round(
            self.count_tokens_internal(prompt_chars)
            * LLAMA3_CLAUDE_MEAN_RATIO
            * SAFETY_MARGIN
        )
