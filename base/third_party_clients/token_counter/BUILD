load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

py_library(
    name = "token_counter",
    srcs = [
        "token_counter.py",
    ],
    visibility = [
        "//base:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        "//base/prompt_format:common",
        "//base/tokenizers",
        requirement("Pillow"),  # Added to calculate image tokens
    ],
)

py_library(
    name = "token_counter_claude",
    srcs = [
        "token_counter_claude.py",
    ],
    visibility = [
        "//base:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":token_counter",
        "//base/tokenizers",
        requirement("structlog"),
    ],
)

py_library(
    name = "token_counter_vertex",
    srcs = [
        "token_counter_vertex.py",
    ],
    visibility = [
        "//base:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":token_counter",
        "//base/tokenizers",
        requirement("google-cloud-aiplatform"),
        requirement("sentencepiece"),  # Implicit dependency from `google-cloud-aiplatform[tokenization]`
        requirement("anthropic"),
        requirement("google-auth"),  # Implicit dependency from `anthropic[vertex]`
        requirement("structlog"),
    ],
)

pytest_test(
    name = "token_counter_test",
    srcs = ["token_counter_test.py"],
    deps = [
        ":token_counter",
        "//base/prompt_format:common",
    ],
)
