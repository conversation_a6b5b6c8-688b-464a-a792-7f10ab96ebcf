"""An OpenAI Responses API client for o1-pro model."""

import json
from typing import Any, Callable, Generator, Literal, Optional

import httpx
import openai
import prometheus_client
import structlog

# Removed unused import
from openai import (
    APIConnectionError,
    APIStatusError,
    BadRequestError,
    InternalServerError,
    RateLimitError,
)
from typing_extensions import override

from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNodeType,
    Exchange,
    ImageFormatType,
    RequestMessage,
    ResponseMessage,
    StopReason,
    try_get_request_message_as_text,
)
from base.prompt_format_chat import get_token_counter_by_prompt_formatter_name
from base.third_party_clients.common import (
    InvalidArgumentRpcError,
    UnavailableRpcError,
)
from base.third_party_clients.prompt_usage_metrics import PromptUsageMetrics
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ModelAPICall,
    PromptCacheUsage,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
)
from base.third_party_clients.token_counter.token_counter import TokenCounter

# Feature flag to enable resource exhausted error handling
OPENAI_RATE_LIMIT_IS_RESOURCE_EXHAUSTED = False

# Note that this metric can be inaccurate, because it depends on the client
# closing the generator stream as soon as it is done consuming results.
OPENAI_RESPONSE_LATENCY = prometheus_client.Histogram(
    "au_openai_response_client_latency",
    "Latency of OpenAI Response API responses in seconds",
    ["model"],
    buckets=[
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
        20.0,
        50.0,
        100.0,
        200.0,
        float("inf"),
    ],
)


MAX_RETRIES = 2  # Default is 2
# Default is httpx.Timeout(timeout=600.0, connect=5.0)
# We add a read timeout, but it's not very strict as it covers context processing and
# periods of time when model is generating tool input where nothing is streamed.
TIMEOUT = httpx.Timeout(timeout=280.0, connect=3.0, read=75.0)

_token_count_calls = prometheus_client.Counter(
    "au_openai_response_token_count_calls",
    "Number of token counting calls made to OpenAI Response client",
)

_output_text_chars_counter = prometheus_client.Counter(
    "au_openai_response_output_text_size",
    "Size of output text from OpenAI Response client",
    ["model"],
)

_output_tokens_histogram = prometheus_client.Histogram(
    "au_openai_response_output_tokens",
    "Number of output tokens from OpenAI Response client",
    ["model_caller"],
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        100000,
        float("inf"),
    ],
)

_error_counter = prometheus_client.Counter(
    "au_openai_response_error_count",
    "Number of errors from OpenAI Response client",
    ["model", "error_type"],
)

_event_type_counter = prometheus_client.Counter(
    "au_openai_response_event_type",
    "Number of events from OpenAI Response client",
    ["model", "event_type"],
)

OPENAI_RESPONSES_MODEL_ATTRIBUTES = {
    "o1-pro-2025-03-19": {
        "system_prompt": True,
        "streaming": False,
        "temperature": False,
        "function_calling": False,
        "uses_max_tokens": True,
        "uses_max_completion_tokens": True,
    },
}


def to_stop_reason(finish_reason: str) -> StopReason:
    match finish_reason:
        case "stop":
            return StopReason.END_TURN
        case "length":
            return StopReason.MAX_TOKENS
        case "tool_calls":
            return StopReason.TOOL_USE_REQUESTED
        case _:
            return StopReason.REASON_UNSPECIFIED


class OpenAIResponseClient(ThirdPartyModelClient):
    """
    A class to interact with OpenAI Responses API for o1-pro model.
    """

    client_type = "openai_response"

    def __init__(
        self,
        api_key: str,
        model_name: str,
        temperature: float,
        max_output_tokens: int,
        prompt_cache_usage: str | None = None,
    ):
        """
        Initialize the OpenAI Responses API with project details and model parameters.

        Args:
            api_key: The API key for the API.
            model_name: The name of the model to use for generating responses.
            temperature: The temperature parameter for controlling the randomness of the
                responses.
            max_output_tokens: The maximum number of tokens to generate in the response.
        """
        self.model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.supports_system_prompt = False
        self.supports_streaming = False
        self.supports_function_calling = False
        self.supports_temperature = False
        self.api_type = "responses"

        if model_name not in OPENAI_RESPONSES_MODEL_ATTRIBUTES:
            raise ValueError(f"Model {model_name} is not supported by Responses API.")

        model_attributes = OPENAI_RESPONSES_MODEL_ATTRIBUTES[model_name]

        self._token_counter = get_token_counter_by_prompt_formatter_name("binks-openai")
        self.supports_system_prompt = model_attributes["system_prompt"]
        self.supports_streaming = model_attributes["streaming"]
        self.supports_function_calling = model_attributes["function_calling"]
        self.supports_temperature = model_attributes["temperature"]
        self.max_token_key: Literal["max_tokens", "max_completion_tokens"]
        if model_attributes["uses_max_tokens"]:
            self.max_token_key = "max_tokens"  # nosec B105
        elif model_attributes["uses_max_completion_tokens"]:
            self.max_token_key = "max_completion_tokens"  # nosec B105
        else:
            raise ValueError(f"Model {model_name} does not support max tokens.")

        if not self.supports_temperature:
            assert (
                self.temperature == 1
            ), f"{self.model_name} only supports temperature of 1."

        self.client = openai.OpenAI(
            api_key=api_key,
            max_retries=MAX_RETRIES,
            timeout=TIMEOUT,
        )

        self.logger = structlog.get_logger().bind(
            client_type=self.client_type, model_name=self.model_name
        )
        self.logger.info("OpenAIResponseClient initialized")

        self.prompt_cache_usage = prompt_cache_usage
        self.prompt_caching_headers = {}
        self.prompt_usage_metrics = PromptUsageMetrics("openai_response")

        if prompt_cache_usage:
            self.logger.warning(
                "OpenAIResponseClient does not support prompt caching now.",
                prompt_cache_usage=prompt_cache_usage,
            )

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Any = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """
        Generate a response based on the given message using the Responses API.

        Args:
            messages: List of (user message, assistant message) tuples. Deprecated in favor of `chat_history`.
            system_prompt: System prompt to guide model behavior.
            cur_message: The current user message to generate a response for.
            chat_history: Structured conversation history as a list of Exchange objects.
            tools: List of tool names available for the model to use.
            tool_definitions: List of tool definitions specifying tool interfaces.
            tool_choice: Specification for which tool the model should use.
            max_output_tokens: Maximum number of tokens in the generated response.
            temperature: Sampling temperature for response generation (0.0 to 1.0).
            prefill: Pre-filled text to start the response with.
            use_caching: Whether to cache and reuse responses.
            request_context: Optional request context containing information about the request, including session ID.
            yield_final_parameters: Whether to yield a response with the final parameters used for the request.

        Returns:
            Generator[ThirdPartyModelResponse]: Stream of response chunks.

        Raises:
            ResponseValidationError: If the response fails validation.
            Exception: If response generation fails.
        """
        self.logger.info(
            "OpenAIResponseClient generating response", message_count=len(messages)
        )

        del request_context  # Not used

        if cur_message:
            try:
                input_tokens = self.count_tokens(
                    try_get_request_message_as_text(cur_message) or ""
                )
                # Create a PromptCacheUsage object for metrics tracking
                # Since OpenAI Responses API doesn't support caching yet, cache values are 0
                prompt_cache_usage = PromptCacheUsage(
                    input_tokens=input_tokens,
                    cache_creation_input_tokens=0,
                    cache_read_input_tokens=0,
                    text_input_tokens=input_tokens,  # All input is text for now
                    tool_input_tokens=0,
                    text_output_tokens=0,  # Will be updated when we get the response
                    tool_output_tokens=0,
                    model_caller=model_caller,
                )
                self.prompt_usage_metrics.observe(
                    model_caller=model_caller,
                    model=self.model_name,
                    client_type=self.client_type,
                    provider_region="",  # OpenAI doesn't expose regions
                    prompt_cache_usage=prompt_cache_usage,
                )
            except Exception as e:
                self.logger.warning("Failed to count input tokens", error=str(e))

        if use_caching and self.prompt_cache_usage:
            self.logger.info(
                "Using prompt caching",
                prompt_cache_usage=self.prompt_cache_usage,
            )
            # In the future, if OpenAI supports prompt caching, we would use it here

        if tool_choice:
            self.logger.error(
                "OpenAI Responses API doesn't support tool_choice yet, ignoring"
            )

        if not self.supports_function_calling and (
            len(tools) > 0 or len(tool_definitions) > 0
        ):
            tool_names = tools + [tool_def.name for tool_def in tool_definitions]
            self.logger.warning(
                f"This model does not currently support tools, but got {tool_names}."
            )
            tools = []
            tool_definitions = []

        temperature = self.temperature if temperature is None else temperature
        if not self.supports_temperature:
            assert temperature == 1
        max_output_tokens = max_output_tokens or self.max_output_tokens

        self.logger.info(
            "OpenAIResponseClient generating response", message_count=len(messages)
        )

        formatted_messages = []
        if system_prompt is not None:
            # For OpenAI Responses API, use "developer" role for system prompts
            # Reference: https://cdn.openai.com/spec/model-spec-2024-05-08.html
            # "developer": from the application developer (possibly OpenAI), formerly "system"
            formatted_messages.append(
                {
                    "role": "developer" if self.supports_system_prompt else "user",
                    "content": system_prompt,
                }
            )

        if chat_history is None:
            chat_history = [
                Exchange(request_message=message[0], response_text=message[1])
                for message in messages
            ]

        for message in chat_history:
            formatted_messages += format_request_message_as_nodes(
                message.request_message
            )
            formatted_messages.append(
                format_response_message_as_node(message.response_text)
            )

        formatted_messages += format_request_message_as_nodes(cur_message)

        if prefill:
            # Note: OpenAI Responses API doesn't officially support prefill
            # This is a workaround that adds an assistant message, but behavior may be unpredictable
            self.logger.warning(
                "OpenAI Responses API doesn't officially support prefill. "
                "Adding assistant message as workaround, but behavior may vary."
            )
            formatted_messages.append(
                {
                    "role": "assistant",
                    "content": prefill,
                }
            )

        with OPENAI_RESPONSE_LATENCY.labels(
            model=self.model_name,
        ).time():
            # o1-pro uses the Responses API which doesn't support streaming
            try:
                if yield_final_parameters:
                    yield ThirdPartyModelResponse(
                        text="",
                        final_parameters={
                            "model": self.model_name,
                            "messages": formatted_messages,
                            self.max_token_key: max_output_tokens,
                        },
                    )

                # Use the responses API endpoint
                # For o1-pro, we need to use the responses API, not chat completions
                # Make a direct HTTP request to the responses endpoint
                # Note: The OpenAI client base URL already includes /v1
                # We need to manually add the Authorization header when using the internal client
                # The Responses API uses 'input' instead of 'messages'
                # The Responses API doesn't accept max_tokens parameter
                request_data = {
                    "model": self.model_name,
                    "input": formatted_messages,
                }

                # Log the request for debugging
                self.logger.info(f"Request data: {json.dumps(request_data, indent=2)}")

                http_response = self.client._client.post(
                    "/responses",
                    json=request_data,
                    headers={
                        "Authorization": f"Bearer {self.client.api_key}",
                    },
                )

                # Log the response for debugging
                self.logger.info(f"Response status: {http_response.status_code}")
                self.logger.info(f"Response text: {http_response.text}")

                # Check if the response was successful
                if http_response.status_code != 200:
                    error_data = http_response.json() if http_response.text else {}
                    error_message = error_data.get("error", {}).get(
                        "message", http_response.text
                    )
                    self.logger.error(f"API error: {error_message}")
                    raise Exception(f"OpenAI API error: {error_message}")

                # Parse the response
                response_data = http_response.json()

                # Extract the text content from the OpenAI Responses API format
                # The response has output[1].content[0].text structure
                text_content = ""
                if "output" in response_data and len(response_data["output"]) > 1:
                    message_output = response_data["output"][1]
                    if (
                        message_output.get("type") == "message"
                        and "content" in message_output
                        and len(message_output["content"]) > 0
                    ):
                        text_content = message_output["content"][0].get("text", "")

                # Create a response object that matches the expected structure
                from types import SimpleNamespace

                response = SimpleNamespace(
                    choices=[
                        SimpleNamespace(
                            message=SimpleNamespace(
                                content=text_content,
                                role="assistant",
                            ),
                            finish_reason="stop",
                        )
                    ],
                    usage=SimpleNamespace(
                        prompt_tokens=response_data.get("usage", {}).get(
                            "input_tokens", 0
                        ),
                        completion_tokens=response_data.get("usage", {}).get(
                            "output_tokens", 0
                        ),
                        total_tokens=response_data.get("usage", {}).get(
                            "total_tokens", 0
                        ),
                    ),
                )

                text = response.choices[0].message.content or ""

                # Track event type for text
                _event_type_counter.labels(
                    model=self.model_name,
                    event_type="text",
                ).inc()

                # Track output text size
                _output_text_chars_counter.labels(
                    model=self.model_name,
                ).inc(len(text))

                yield ThirdPartyModelResponse(text)

                # Track output tokens if available
                output_tokens = None
                if (
                    hasattr(response, "usage")
                    and response.usage
                    and hasattr(response.usage, "completion_tokens")
                ):
                    output_tokens = response.usage.completion_tokens
                    if output_tokens:
                        _output_tokens_histogram.labels(
                            model_caller=model_caller,
                        ).observe(output_tokens)

                _event_type_counter.labels(
                    model=self.model_name,
                    event_type="end_of_stream",
                ).inc()

                yield ThirdPartyModelResponse(
                    text="",
                    end_of_stream=EndOfStream(
                        stop_reason=to_stop_reason(response.choices[0].finish_reason),
                        output_tokens=output_tokens,
                    ),
                )
            except BadRequestError as e:
                self.logger.error("OpenAI BadRequestError: %s", e)
                _error_counter.labels(
                    model=self.model_name,
                    error_type="bad_request",
                ).inc()
                if hasattr(e, "code") and e.code == "invalid_prompt":
                    yield ThirdPartyModelResponse(e.message)
                else:
                    raise InvalidArgumentRpcError(f"OpenAI bad request: {e}")
            except (
                RateLimitError,
                InternalServerError,
                APIConnectionError,
                APIStatusError,
                Exception,
            ) as e:
                self._handle_openai_error(e, is_streaming=False)

    @override
    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: An estimate of the number of tokens in the given message.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        _token_count_calls.inc()
        # Track token count in metrics
        token_count = self._token_counter.count_tokens(message)
        # Add a 10% buffer due to potential tokenizer inaccuracy, similar to Anthropic client
        return round(token_count * 1.1)

    @override
    def token_counter(self) -> TokenCounter:
        return self._token_counter

    def _handle_openai_error(self, error, is_streaming=False):
        """Centralized error handling for OpenAI API errors.

        Args:
            error: The exception raised by the OpenAI API
            is_streaming: Whether the error occurred during streaming

        Raises:
            UnavailableRpcError: For rate limits, server errors, connection issues, or API status errors
            InvalidArgumentRpcError: For bad requests
        """
        context = "stream" if is_streaming else "processing"

        if isinstance(error, RateLimitError):
            self.logger.warning(f"OpenAI RateLimitError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="rate_limit",
            ).inc()
            raise UnavailableRpcError(f"Rate limit exceeded: {error}")

        elif isinstance(error, InternalServerError):
            self.logger.warning(f"OpenAI InternalServerError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="internal_server",
            ).inc()
            raise UnavailableRpcError(f"OpenAI internal server error: {error}")

        elif isinstance(error, APIConnectionError):
            self.logger.warning(f"OpenAI APIConnectionError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="connection",
            ).inc()
            raise UnavailableRpcError(f"OpenAI connection error: {error}")

        elif isinstance(error, APIStatusError):
            self.logger.warning(
                f"OpenAI APIStatusError: {error.status_code} {error.message}"
            )
            _error_counter.labels(
                model=self.model_name,
                error_type="api_status",
            ).inc()
            raise UnavailableRpcError(
                f"OpenAI API status error {error.status_code}: {error.message}"
            )

        elif isinstance(error, BadRequestError):
            self.logger.error(f"OpenAI BadRequestError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="bad_request",
            ).inc()
            raise InvalidArgumentRpcError(f"OpenAI bad request: {error}")

        else:  # Catch any other unexpected errors
            self.logger.exception(f"Unexpected error during OpenAI {context}")
            _error_counter.labels(
                model=self.model_name,
                error_type="unexpected",
            ).inc()
            raise UnavailableRpcError(f"Unexpected error: {error}")


def format_request_message_as_nodes(request_message: RequestMessage):
    message_text = try_get_request_message_as_text(request_message)
    if message_text is not None:
        return [
            {
                "role": "user",
                "content": message_text,
            }
        ]
    # This check might be redundant if RequestMessage is always list-like
    # but keeping it for safety, mirroring original function.
    if isinstance(request_message, str):
        raise ValueError("request_message should not be a plain string here.")

    # Assert that request_message is iterable
    # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
    assert iter(request_message) is not None

    formatted_messages = []
    for node in request_message:
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            assert node.tool_result_node is None
            formatted_messages.append(
                {
                    "role": "user",
                    "content": node.text_node.content,
                }
            )
        elif node.type == ChatRequestNodeType.TOOL_RESULT:
            assert node.tool_result_node is not None
            assert node.text_node is None
            formatted_messages.append(
                {
                    "role": "tool",
                    "tool_call_id": node.tool_result_node.tool_use_id,
                    "content": node.tool_result_node.content,
                }
            )
        elif node.type == ChatRequestNodeType.IMAGE:
            assert node.image_node is not None
            assert node.text_node is None
            assert node.tool_result_node is None
            # Map image format to media type
            media_type = "jpeg"  # default
            if node.image_node.format == ImageFormatType.PNG:
                media_type = "png"
            elif node.image_node.format == ImageFormatType.JPEG:
                media_type = "jpeg"
            formatted_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/{media_type};base64,{node.image_node.image_data}"
                            },
                        }
                    ],
                }
            )
        else:
            raise ValueError(f"Unsupported node type: {node.type}")
    return formatted_messages


def format_response_message_as_node(response_message: ResponseMessage):
    if isinstance(response_message, str):
        return {
            "role": "assistant",
            "content": response_message,
        }
    # Assert that response_message is iterable
    # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
    assert iter(response_message) is not None

    content_parts = []
    tool_calls = []
    for node in response_message:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            # For RAW_RESPONSE nodes, use the content field directly
            content_parts.append(node.content)
        elif node.type == ChatResultNodeType.TOOL_USE:
            assert node.tool_use is not None
            tool_calls.append(
                {
                    "id": node.tool_use.tool_use_id,
                    "type": "function",
                    "function": {
                        "name": node.tool_use.name,
                        "arguments": json.dumps(node.tool_use.input),
                    },
                }
            )
        else:
            # Skip other node types that aren't relevant for OpenAI format
            pass

    message: dict[str, Any] = {"role": "assistant"}
    if content_parts:
        message["content"] = "".join(content_parts)
    if tool_calls:
        message["tool_calls"] = tool_calls
    return message
