import json

from base.third_party_clients.third_party_model_client import Too<PERSON><PERSON><PERSON><PERSON>, ToolChoiceType


class TestToolChoice:
    def test_tool_choice_serialization(self):
        """Test that ToolChoice can be serialized to JSON."""
        # Test with default values
        tool_choice = ToolChoice()
        json_str = tool_choice.to_json()
        json_obj = json.loads(json_str)

        assert json_obj["type"] == ToolChoiceType.AUTO.value
        assert json_obj["name"] is None

        # Test with custom values
        tool_choice = ToolChoice(type=ToolChoiceType.TOOL, name="test_tool")
        json_str = tool_choice.to_json()
        json_obj = json.loads(json_str)

        assert json_obj["type"] == ToolChoiceType.TOOL.value
        assert json_obj["name"] == "test_tool"

    def test_tool_choice_deserialization(self):
        """Test that ToolChoice can be deserialized from JSON."""
        # Test with default AUTO type
        json_str = '{"type": 1, "name": null}'
        tool_choice = ToolChoice.from_json(json_str)

        assert tool_choice.type == ToolChoiceType.AUTO
        assert tool_choice.name is None

        # Test with ANY type
        json_str = '{"type": 2, "name": null}'
        tool_choice = ToolChoice.from_json(json_str)

        assert tool_choice.type == ToolChoiceType.ANY
        assert tool_choice.name is None

        # Test with TOOL type and name
        json_str = '{"type": 3, "name": "test_tool"}'
        tool_choice = ToolChoice.from_json(json_str)

        assert tool_choice.type == ToolChoiceType.TOOL
        assert tool_choice.name == "test_tool"

    def test_tool_choice_round_trip(self):
        """Test that ToolChoice can be serialized and then deserialized."""
        # Create original object
        original = ToolChoice(type=ToolChoiceType.TOOL, name="test_tool")

        # Serialize to JSON
        json_str = original.to_json()

        # Deserialize back to object
        deserialized = ToolChoice.from_json(json_str)

        # Verify fields match
        assert deserialized.type == original.type
        assert deserialized.name == original.name
