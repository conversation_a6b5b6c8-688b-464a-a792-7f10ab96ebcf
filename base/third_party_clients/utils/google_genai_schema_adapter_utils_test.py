"""Tests for google_genai_schema_adapter_utils."""

import copy
import json
import unittest
from typing import Literal

import pytest
from google.genai.types import J<PERSON><PERSON>chema, JSONSchemaType, Schema, Type
from pydantic import BaseModel, Field

from base.third_party_clients.utils.google_genai_schema_adapter_utils import (
    JSONSchemaWithCamelCase,
    handle_const,
    json_schema_str_to_genai_schema,
    map_oneof_and_allof_to_anyof,
    prep_nullable_fields,
)


class TestJSONSchemaWithCamelCase(unittest.TestCase):
    # We already have massively verbose test output, so include full diffs
    # when assertions fail.
    maxDiff = None

    def test_any_of(self) -> None:
        """Validate that the `anyOf` field is properly populated from
        `any_of` in the JSONSchemaWithCamelCase model.
        """
        input_schema = {
            "anyOf": [
                {
                    "description": "test",
                },
            ]
        }
        json_schema = JSONSchemaWithCamelCase.model_validate(input_schema)

        expected_json_schema = JSONSchema(
            any_of=[
                JSONSchema(description="test"),
            ]
        )

        self.assertEqual(
            json_schema.model_dump(exclude_unset=True),
            expected_json_schema.model_dump(exclude_unset=True),
        )

    def test_json_schema_with_camel_case_in_list(self) -> None:
        """Test that JSON Schema is correctly extracted when nested
        in a list.
        """
        input_schema = {
            "anyOf": [
                {
                    "minItems": 1,
                }
            ]
        }
        json_schema = JSONSchemaWithCamelCase.model_validate(input_schema)

        expected_json_schema = JSONSchema(
            any_of=[
                JSONSchema(min_items=1),
            ]
        )

        self.assertEqual(
            json_schema.model_dump(exclude_unset=True),
            expected_json_schema.model_dump(exclude_unset=True),
        )

    def test_json_schema_with_camel_case_in_properties(self) -> None:
        """Validate that our JSONSchemaWithCamelCase class properly
        produces a Pydantic model where the fields are populated correctly
        from camelCase input.
        """
        input_schema = {
            "type": "object",
            "properties": {
                "name": {
                    "anyOf": [
                        {
                            "type": "string",
                        },
                        {
                            "maxLength": 10,
                        },
                    ]
                },
            },
        }
        json_schema = JSONSchemaWithCamelCase.model_validate(input_schema)

        # It should match a JSONSchema produced from snake cased fields.
        # Using the JSONSchema constructor with dict expansion rather than
        # JSONShema.model_validate is important so that we are confident
        # all provided fields exist in the JSONSchema class.
        expected = JSONSchema(
            type=JSONSchemaType.OBJECT,
            properties={
                "name": JSONSchema(
                    any_of=[
                        JSONSchema(type=JSONSchemaType.STRING),
                        JSONSchema(max_length=10),
                    ]
                )
            },
        )
        self.assertEqual(
            expected.model_dump(exclude_unset=True),
            json_schema.model_dump(exclude_unset=True),
        )


class TestPrepNullableFields(unittest.TestCase):
    maxDiff = None

    def test_prep_nullable_fields(self) -> None:
        input_schema = {
            "type": "object",
            "properties": {
                "name": {
                    "anyOf": [
                        {
                            "type": "string",
                        },
                        {
                            "type": "int",
                        },
                        {
                            "type": "null",
                        },
                    ]
                },
            },
        }
        expected_schema = {
            "type": "object",
            "properties": {
                "name": {
                    "anyOf": [{"type": ["string", "null"]}, {"type": ["int", "null"]}],
                },
            },
        }

        prep_nullable_fields(input_schema)
        self.assertEqual(input_schema, expected_schema)


class TestHandleConst:
    max_diff = None

    @pytest.mark.parametrize(
        "const_value,expected_type,field_name",
        [
            ("active", "string", "status"),
            (42, "integer", "version"),
            (True, "boolean", "enabled"),
            (3.14, "number", "pi"),
            (None, "null", "empty"),
        ],
    )
    def test_handle_const_basic_types(self, const_value, expected_type, field_name):
        """Test that const fields with basic types are converted to enum + type."""
        input_schema = {
            "type": "object",
            "properties": {
                field_name: {
                    "const": const_value,
                },
            },
        }
        expected_schema = {
            "type": "object",
            "properties": {
                field_name: {
                    "type": expected_type,
                    "enum": [const_value],
                },
            },
        }

        handle_const(input_schema)
        assert input_schema == expected_schema

    def test_handle_const_nested(self):
        """Test that const fields in nested structures are converted."""
        input_schema = {
            "type": "object",
            "properties": {
                "config": {
                    "type": "object",
                    "properties": {
                        "mode": {
                            "const": "production",
                        },
                    },
                },
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {
                                "const": "item",
                            },
                        },
                    },
                },
            },
        }
        expected_schema = {
            "type": "object",
            "properties": {
                "config": {
                    "type": "object",
                    "properties": {
                        "mode": {
                            "type": "string",
                            "enum": ["production"],
                        },
                    },
                },
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {
                                "type": "string",
                                "enum": ["item"],
                            },
                        },
                    },
                },
            },
        }

        handle_const(input_schema)
        assert input_schema == expected_schema

    def test_handle_const_anyof(self):
        """Test that const fields in anyOf structures are converted."""
        input_schema = {
            "anyOf": [
                {
                    "type": "object",
                    "properties": {
                        "type": {
                            "const": "dog",
                        },
                    },
                },
                {
                    "type": "object",
                    "properties": {
                        "type": {
                            "const": "cat",
                        },
                    },
                },
            ],
        }
        expected_schema = {
            "anyOf": [
                {
                    "type": "object",
                    "properties": {
                        "type": {
                            "type": "string",
                            "enum": ["dog"],
                        },
                    },
                },
                {
                    "type": "object",
                    "properties": {
                        "type": {
                            "type": "string",
                            "enum": ["cat"],
                        },
                    },
                },
            ],
        }

        handle_const(input_schema)
        assert input_schema == expected_schema

    def test_handle_const_no_const_fields(self):
        """Test that schemas without const fields are unchanged."""
        input_schema = {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                },
                "age": {
                    "type": "integer",
                },
            },
        }
        expected_schema = copy.deepcopy(input_schema)

        handle_const(input_schema)
        assert input_schema == expected_schema


class TestGoogleGenAISchemaAdapterUtils(unittest.TestCase):
    maxDiff = None

    def test_simple_non_pydantic_schema(self) -> None:
        """Sanity check that non-Pydantic JSON Schema validates fine.

        This was written to prevent regression of a bug where we would get
        and error on `del schema["$defs"]`, since not all schemas have that.
        """
        input_schema = {
            "type": "object",
            "properties": {
                "name": {
                    "anyOf": [
                        {
                            "type": "string",
                        },
                    ]
                },
            },
        }
        expected_schema = Schema(
            type=Type.OBJECT,
            properties={
                "name": Schema(
                    any_of=[
                        Schema(type=Type.STRING),
                    ]
                )
            },
        )
        genai_schema = json_schema_str_to_genai_schema(json.dumps(input_schema))
        self.assertEqual(
            genai_schema,
            expected_schema,
        )

    def test_github_api_schema(self) -> None:
        """
        Explicitly test that a Pydantic schema vaguely modeled off of the
        Github-API tool does what we expect, as we've had repeated issues
        with this input. Check defaulted nullable fields, as well as const fields.
        """

        class GitHubAPIInput(BaseModel):
            """Class description"""

            summary: str | None = Field(
                default=None,
                description="summary description",
            )
            const_field: Literal["const-val"]

        # Generate the schema string
        schema_string = json.dumps(GitHubAPIInput.model_json_schema(), indent=2)

        # Parse to schema using our utility
        genai_schema = json_schema_str_to_genai_schema(schema_string)

        # Check that it matches expected. In particular `summary` should not be defined as
        # an anyOf of string or null, instead it should be one field marked as nullable.
        expected = Schema(
            type=Type.OBJECT,
            properties={
                "summary": Schema(
                    any_of=[Schema(type=Type.STRING, nullable=True)],
                    title="Summary",
                    description="summary description",
                ),
                "const_field": Schema(
                    type=Type.STRING,
                    enum=["const-val"],
                    title="Const Field",
                ),
            },
            description="Class description",
            title="GitHubAPIInput",
            required=["const_field"],
        )
        # Model dump is preferred over directly comparing Schema as Pytest renders
        # the diff better.
        self.assertEqual(
            genai_schema.model_dump(exclude_unset=True),
            expected.model_dump(exclude_unset=True),
        )

    def test_map_oneof_and_allof_to_anyof(self) -> None:
        input_schema = {
            "properties": {
                "name": {"description": "The name of the owner.", "type": "string"},
                "pet": {
                    "oneOf": [
                        {
                            "type": "object",
                            "properties": {"catName": {"type": "string"}},
                        },
                        {
                            "allOf": [
                                {
                                    "type": "object",
                                    "properties": {"dogName": {"type": "string"}},
                                },
                                {
                                    "type": "object",
                                    "properties": {"dogBreed": {"type": "string"}},
                                },
                            ]
                        },
                    ],
                    "description": "The owner's pet.",
                },
            },
        }
        expected_schema = {
            "properties": {
                "name": {"description": "The name of the owner.", "type": "string"},
                "pet": {
                    "anyOf": [
                        {
                            "type": "object",
                            "properties": {"catName": {"type": "string"}},
                        },
                        {
                            "anyOf": [
                                {
                                    "type": "object",
                                    "properties": {"dogName": {"type": "string"}},
                                },
                                {
                                    "type": "object",
                                    "properties": {"dogBreed": {"type": "string"}},
                                },
                            ]
                        },
                    ],
                    "description": "The owner's pet.",
                },
            },
        }
        # Use deepcopy as the function modifies the dict in-place.
        schema_to_modify = copy.deepcopy(input_schema)
        map_oneof_and_allof_to_anyof(schema_to_modify)
        self.assertEqual(schema_to_modify, expected_schema)

    def test_json_schema_str_to_genai_schema_simple(self) -> None:
        """
        Test that shows the expected contract for Pydantic model to Google Schema.

        Used as a simple sanity check that we're not dropping large chunks of field
        descriptions by the time the schema reaches Gemini. The actual
        expected output itself is admittedly strict (and brittle). If this test
        breaks due to an update in Google's SDK, it's likely fine to update
        the expected output, as long as it still looks reasonable (i.e.: it
        still has all of the original fields + descriptions).
        """

        class InnerModel(BaseModel):
            # Test with both snake_case and camelCase field names.
            # Casing should be preserved.
            inner_field: str = Field(description="An inner field.")
            anotherInnerField: int = Field(description="Another inner field.")

        class OuterModel(BaseModel):
            outer_field: str = Field(description="An outer field.")
            nested_model: InnerModel = Field(description="A nested model.")
            optional_field: str | None = Field(
                default=None, description="An optional field."
            )

        schema_dict = OuterModel.model_json_schema(by_alias=False)
        schema_str_dumped = json.dumps(schema_dict)

        # This will raise a warning as we'
        genai_schema = json_schema_str_to_genai_schema(schema_str_dumped)

        # Expected structure after transformations. Taken from a copy of
        # running the code, because Google's Schema converter does some
        # extra stuff, it's easiest to just sanity check the output
        # looks reasonable, then use that as the "correct" output.
        expected_dict = {
            "properties": {
                "outer_field": {
                    "description": "An outer field.",
                    "title": "Outer Field",
                    "type": "STRING",
                },
                "nested_model": {
                    # In Pydantic this is originally an `allOf`.
                    "any_of": [
                        {
                            "properties": {
                                "inner_field": {
                                    "description": "An inner field.",
                                    "title": "Inner Field",
                                    "type": "STRING",
                                },
                                "anotherInnerField": {
                                    "description": "Another inner field.",
                                    "title": "Anotherinnerfield",
                                    "type": "INTEGER",
                                },
                            },
                            "required": ["inner_field", "anotherInnerField"],
                            "title": "InnerModel",
                            "type": "OBJECT",
                        }
                    ],
                    "description": "A nested model.",
                },
                "optional_field": {
                    "any_of": [{"type": "STRING", "nullable": True}],
                    "description": "An optional field.",
                    "title": "Optional Field",
                },
            },
            "required": ["outer_field", "nested_model"],
            "title": "OuterModel",
            "type": "OBJECT",
        }

        # Using exclude_unset=True to only keep values that were actually set
        # (easier to read typically).
        actual_dump = genai_schema.model_dump(exclude_unset=True)

        # We do a serialization/deserialization round trip to map all custom
        # enums Google has to their string form for easier comparison against
        # expected dict.
        self.assertEqual(json.loads(json.dumps(actual_dump)), expected_dict)

    def test_json_schema_str_with_discriminator(self) -> None:
        """
        Test that shows the expected contract for Pydantic models with discriminated
        unions.

        Similar deal to `test_json_schema_str_to_genai_schema_simple`, where
        the expected output is brittle, and can be updated as long as the new
        output isn't losing large bits of field information.
        """

        class Dog(BaseModel):
            name: str = Field(description="The name of the dog.")
            weight: int = Field(description="The weight of the dog.")
            type: Literal["dog"] = "dog"

        class Cat(BaseModel):
            name: str = Field(description="The name of the cat.")
            weight: int = Field(description="The weight of the cat.")
            type: Literal["cat"] = "cat"

        class PetWithDiscriminator(BaseModel):
            pet: Dog | Cat = Field(discriminator="type", description="The pet.")

        schema_dict = PetWithDiscriminator.model_json_schema(by_alias=False)
        # Pydantic v2 model_json_schema() returns a dict, not a string.
        # We need to dump it to a string first.
        schema_str_dumped = json.dumps(schema_dict, indent=2)
        genai_schema = json_schema_str_to_genai_schema(schema_str_dumped)

        # Expected structure after transformations. Taken from a copy of
        # running the code, because Google's Schema converter does some
        # extra stuff, it's easiest to just sanity check the output
        # looks reasonable, then use that as the "correct" output.
        expected_dict = {
            "properties": {
                "pet": {
                    "any_of": [
                        {
                            "properties": {
                                "name": {
                                    "description": "The name of the dog.",
                                    "title": "Name",
                                    "type": "STRING",
                                },
                                "weight": {
                                    "description": "The weight of the dog.",
                                    "title": "Weight",
                                    "type": "INTEGER",
                                },
                                "type": {
                                    # "const" is in source schema, but not supported by
                                    # Google. Our handle_const function converts it to enum + type.
                                    # "const": "dog",
                                    "default": "dog",
                                    "enum": ["dog"],
                                    "title": "Type",
                                    "type": "STRING",
                                },
                            },
                            "required": ["name", "weight"],
                            "title": "Dog",
                            "type": "OBJECT",
                        },
                        {
                            "properties": {
                                "name": {
                                    "description": "The name of the cat.",
                                    "title": "Name",
                                    "type": "STRING",
                                },
                                "weight": {
                                    "description": "The weight of the cat.",
                                    "title": "Weight",
                                    "type": "INTEGER",
                                },
                                "type": {
                                    # "const": "cat",
                                    "default": "cat",
                                    "enum": ["cat"],
                                    "title": "Type",
                                    "type": "STRING",
                                },
                            },
                            "required": ["name", "weight"],
                            "title": "Cat",
                            "type": "OBJECT",
                        },
                    ],
                    "description": "The pet.",
                    "title": "Pet",
                },
            },
            "required": [
                "pet",
            ],
            "title": "PetWithDiscriminator",
            "type": "OBJECT",
        }

        # Using exclude_unset=True to only keep values that were actually set
        # (easier to read typically).
        actual_dump = genai_schema.model_dump(exclude_unset=True)

        # We do a serialization/deserialization round trip to map all custom
        # enums Google has to their string form for easier comparison against
        # expected dict.
        self.assertEqual(json.loads(json.dumps(actual_dump)), expected_dict)


if __name__ == "__main__":
    unittest.main()
