"""
Adapter utils for massaging JSON Schema strings into valid Google GenAI Schema objects.

The Google GenAI SDK only accepts a subset of valid JSON Schema objects, and unsupported
parts are unfortunately just silently dropped. The utilities in this file aim to massage
most JSON Schema strings into valid Google GenAI Schema objects.
"""

import copy
import json
from typing import Any, Optional, Union

# jsonref can be used to resolve and inline $ref fields in the schema
# output by Pydantic: https://github.com/pydantic/pydantic/issues/889#issuecomment-1064688675
import jsonref
import structlog
from google.genai.types import JSONSchema, JSONSchemaType, Schema
from pydantic import ConfigDict, Field, ValidationError

log = structlog.getLogger(__name__)


class JSONSchemaWithCamelCase(JSONSchema):
    """A subclass of `google.genai.types.JSONSchema` that adds
    camelCase alias support during validation to cure Google's random
    broken choice to only allow snake_case for field names (which is NOT
    valid JSON Schema!)
    """

    # Allow populating fields by the snake case name, as well as aliases.
    # Properly formed JSON Schema should only be camel case, but let's just
    # try and be more accepting to match Google's JSON Schema madness.
    model_config = ConfigDict(populate_by_name=True)

    type: Optional[Union[JSONSchemaType, list[JSONSchemaType]]] = Field(
        default=None,
        description="""Validation succeeds if the type of the instance matches the type represented by the given type, or matches at least one of the given types.""",
    )
    format: Optional[str] = Field(
        default=None,
        description="Define semantic information about a string instance.",
    )
    title: Optional[str] = Field(
        default=None,
        description=(
            "A preferably short description about the purpose of the instance"
            " described by the schema."
        ),
    )
    description: Optional[str] = Field(
        default=None,
        description=(
            "An explanation about the purpose of the instance described by the"
            " schema."
        ),
    )
    default: Optional[Any] = Field(
        default=None,
        description=(
            "This keyword can be used to supply a default JSON value associated"
            " with a particular schema."
        ),
    )
    # The 'type: ignore' is necessary to silence Pyright since technically we
    # are overriding the parent type with an incompatible type, but
    # JSONSchemaWithCamelCase is fully substitutable for JSONSchema from
    # a dict POV, so we just silence this warning. Same for all other
    # types below where we replace JSONSchema with JSONSchemaWithCamelCase
    # so that recursive camel case fields are handled.
    items: Optional["JSONSchemaWithCamelCase"] = Field(  # type: ignore
        default=None,
        description=(
            "Validation succeeds if each element of the instance not covered by"
            " prefixItems validates against this schema."
        ),
    )
    min_items: Optional[int] = Field(
        default=None,
        description=(
            "An array instance is valid if its size is greater than, or equal to,"
            " the value of this keyword."
        ),
        alias="minItems",
    )
    max_items: Optional[int] = Field(
        default=None,
        description=(
            "An array instance is valid if its size is less than, or equal to,"
            " the value of this keyword."
        ),
        alias="maxItems",
    )
    enum: Optional[list[Any]] = Field(
        default=None,
        description=(
            "Validation succeeds if the instance is equal to one of the elements"
            " in this keyword’s array value."
        ),
    )
    properties: Optional[dict[str, "JSONSchemaWithCamelCase"]] = Field(  # type: ignore
        default=None,
        description=(
            "Validation succeeds if, for each name that appears in both the"
            " instance and as a name within this keyword’s value, the child"
            " instance for that name successfully validates against the"
            " corresponding schema."
        ),
    )
    required: Optional[list[str]] = Field(
        default=None,
        description=(
            "An object instance is valid against this keyword if every item in"
            " the array is the name of a property in the instance."
        ),
    )
    min_properties: Optional[int] = Field(
        default=None,
        description=(
            "An object instance is valid if its number of properties is greater"
            " than, or equal to, the value of this keyword."
        ),
        alias="minProperties",
    )
    max_properties: Optional[int] = Field(
        default=None,
        description=(
            "An object instance is valid if its number of properties is less"
            " than, or equal to, the value of this keyword."
        ),
        alias="maxProperties",
    )
    minimum: Optional[float] = Field(
        default=None,
        description=(
            "Validation succeeds if the numeric instance is greater than or equal"
            " to the given number."
        ),
    )
    maximum: Optional[float] = Field(
        default=None,
        description=(
            "Validation succeeds if the numeric instance is less than or equal to"
            " the given number."
        ),
    )
    min_length: Optional[int] = Field(
        default=None,
        description=(
            "A string instance is valid against this keyword if its length is"
            " greater than, or equal to, the value of this keyword."
        ),
        alias="minLength",
    )
    max_length: Optional[int] = Field(
        default=None,
        description=(
            "A string instance is valid against this keyword if its length is"
            " less than, or equal to, the value of this keyword."
        ),
        alias="maxLength",
    )
    pattern: Optional[str] = Field(
        default=None,
        description=(
            "A string instance is considered valid if the regular expression"
            " matches the instance successfully."
        ),
    )
    any_of: Optional[list["JSONSchemaWithCamelCase"]] = Field(  # type: ignore
        default=None,
        description=(
            "An instance validates successfully against this keyword if it"
            " validates successfully against at least one schema defined by this"
            " keywords value."
        ),
        alias="anyOf",
    )


# To detect when Google would be dropping fields we subclass their JSONSchema
# object and configure it to forbid extra fields.
class JSONSchemaWithCamelCaseStrict(JSONSchemaWithCamelCase):
    model_config = ConfigDict(extra="forbid")


def map_oneof_and_allof_to_anyof(schema: dict) -> None:
    """Recursively map all appearances of the keys `oneOf` and `allOf` to `anyOf`
    in the provided dict, in-place."""
    # Recursively map any `oneOf` or `allOf` keys to `any_of``
    for key in ["oneOf", "allOf"]:
        if key in schema:
            schema["anyOf"] = schema[key]
            del schema[key]

    # Recursively process nested dictionaries and lists
    for v in schema.values():
        if isinstance(v, dict):
            map_oneof_and_allof_to_anyof(v)
        elif isinstance(v, list):
            for item in v:
                if isinstance(item, dict):
                    map_oneof_and_allof_to_anyof(item)


def prep_nullable_fields(schema: dict, nullable: bool = False) -> None:
    """Convert Pydantic's JSON Schema for nullable fields to an alternative representation
    that Google's GenAI SDK accepts in-place.

    Context: Pydantic represents nullable fields as a `anyOf` between the field's
    type and `null`. e.g.:

    ```
    "data": {
        "anyOf": [{ "type": "string", }, { "type": "int", }, { "type": "null", },],
    }
    ```

    Unfortunately Google's Schema.from_json_schema() call
    doesn't properly parse this representation, and when sent to Google's API you
    get back a 400. However, Google's Schema.from_json_schema() supports
    the following JSONSchema and interprets it as a nullable string:
    ```
    "data": {
        "anyOf": [{ "type": ["string", "null"], { "type": ["int", "null"], }, }],
    }
    ```

    So this function mutates the provided schema in-place to convert
    such fields to the representation that Google's SDK accepts.
    """

    def _is_null_type(item: Any) -> bool:
        """Check if an anyOf item represents a null type."""
        return isinstance(item, dict) and item.get("type") == "null"

    def _make_nullable(item: Any) -> Any:
        """Convert a schema item to include null in its type array."""
        if isinstance(item, dict) and isinstance(item.get("type"), str):
            return {**item, "type": [item["type"], "null"]}
        return item

    def _process_recursively(obj: Any, nullable: bool) -> None:
        """Recursively apply prep_nullable_fields to nested structures."""
        if isinstance(obj, dict):
            prep_nullable_fields(obj, nullable)
        elif isinstance(obj, list):
            for item in obj:
                _process_recursively(item, nullable)

    # Transform anyOf with null type to Google's format
    if "anyOf" in schema:
        any_of_list = schema["anyOf"]

        # Filter out null types and check if any existed
        non_null_items = [item for item in any_of_list if not _is_null_type(item)]
        has_null_type = len(non_null_items) < len(any_of_list)

        # Convert to Google's format: merge null into each type array
        if has_null_type and non_null_items:
            schema["anyOf"] = [_make_nullable(item) for item in non_null_items]

    # Recursively process nested structures
    for value in schema.values():
        _process_recursively(value, nullable)


def handle_const(schema: dict) -> None:
    """Convert Pydantic's JSON Schema for const fields to an alternative representation
    that Google's GenAI SDK accepts in-place.

    Context: Pydantic represents Literal type fields as a `const` key in the schema.
    e.g.:
    ```
    "data": { "const": "value", }
    ```

    Google's GenAI SDK doesn't support the `const` key, so we convert it
    to an `enum` with a single value + the type. e.g.:
    ```
    "data": { "type": "string", "enum": ["value"], }
    ```
    """
    # Handle const fields at the current level
    if "const" in schema:
        const_value = schema.pop("const")

        # Determine the type based on the const value
        if isinstance(const_value, str):
            schema["type"] = "string"
        elif isinstance(const_value, bool):
            schema["type"] = "boolean"
        elif isinstance(const_value, int):
            schema["type"] = "integer"
        elif isinstance(const_value, float):
            schema["type"] = "number"
        elif const_value is None:
            schema["type"] = "null"
        else:
            # For other types, default to string representation
            schema["type"] = "string"

        # Add the enum with the single value
        schema["enum"] = [const_value]

    # Recursively process nested dictionaries and lists
    for value in schema.values():
        if isinstance(value, dict):
            handle_const(value)
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, dict):
                    handle_const(item)


def json_schema_str_to_genai_schema(json_schema_str: str) -> Schema:
    """Convert the provided JSON Schema string to a Google GenAI Schema.

    Use this instead of `google.genai.types.JSONSchema.model_validate` as we
    do some extra massaging of the input to get more information to pass through
    into GenAI's scuffed Schema implementation.

    Google GenAI's current schema representation is problematic because
    it doesn't support large portions of JSON Schema/OpenAPI Schema objects.
    This method does it's best to transpile regular schema objects into
    GenAI's format (although the semantics may be broken in some cases).
    https://github.com/googleapis/python-genai/issues/460
    """
    # This "inlines" any JSON References within the schema ('$ref' values). The
    # schema will still have '$defs' as a top-level property if it had
    # references, but it doesn't matter since Google just ignores it.
    schema = jsonref.replace_refs(json.loads(json_schema_str))

    # The json schema string should have loaded into a dict.
    if not isinstance(schema, dict):
        raise ValueError("Failed to load JSON Schema string into a dict.")

    # Do a deepcopy to make it so that we can delete "$defs" from the
    # object while not breaking references. Not sure why this fixes
    # it, but it does and helps us only raise warnings on actually
    # unhandled JSONSchema fields.
    schema = copy.deepcopy(schema)

    # Remove the "$defs" key to prevent any spurious warnings from
    # JSONSchemaWithCamelCaseStrict.model_validate
    schema.pop("$defs", None)

    prep_nullable_fields(schema)

    # Convert const fields to enum + type format that Google's GenAI SDK accepts
    handle_const(schema)

    # Google's Schema type currently only supports anyOf. While not quite
    # semantically correct, it's better for us to map all `oneOf` and `allOf`
    # fields to `anyOf`, so that Gemini can at least see the fields +
    # descriptions.
    map_oneof_and_allof_to_anyof(schema)

    try:
        # Attempt strict conversion to Google JSON schema with no allowed
        # extra fields.
        google_json_schema = JSONSchemaWithCamelCaseStrict.model_validate(schema)
        return Schema.from_json_schema(
            json_schema=google_json_schema, raise_error_on_unsupported_field=True
        )
    except (ValidationError, ValueError) as e:
        log.warning(f"Failed to convert JSON Schema to Google JSON Schema: {e}")

    # Otherwise fallback to non-strict conversion.
    google_json_schema = JSONSchemaWithCamelCase.model_validate(schema)
    return Schema.from_json_schema(json_schema=google_json_schema)
