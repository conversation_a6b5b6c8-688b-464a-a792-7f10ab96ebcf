load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

pytest_test(
    name = "google_genai_schema_adapter_utils_test",
    srcs = ["google_genai_schema_adapter_utils_test.py"],
    deps = [
        ":google_genai_schema_adapter_utils",
    ],
)

py_library(
    name = "google_genai_schema_adapter_utils",
    srcs = ["google_genai_schema_adapter_utils.py"],
    visibility = ["//base/third_party_clients:__subpackages__"],
    deps = [
        requirement("jsonref"),
        requirement("structlog"),
        requirement("google-genai"),
        requirement("pydantic"),
    ],
)
