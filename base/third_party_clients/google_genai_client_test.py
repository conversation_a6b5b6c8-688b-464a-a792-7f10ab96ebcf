import json
from unittest.mock import MagicMock, patch

import pytest
from google.genai.types import (
    Content as GenaiContent,
)
from google.genai.types import (
    FunctionCall as GenaiFunctionCall,
)
from google.genai.types import (
    FunctionResponse as GenaiFunctionResponse,
)
from google.genai.types import (
    GenerateContentResponseUsageMetadata,
    Schema,
    Type,
)
from google.genai.types import (
    Part as GenaiPart,
)

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
)
from base.third_party_clients.common import InvalidArgumentRpcError
from base.third_party_clients.google_genai_client import (
    GoogleGenaiClient,
    convert_to_schema,
    format_request_message_content,
    format_response_message_content,
)


# This is way under-tested, but just validate our version of the SDK supports
# this functionality at a basic level
def test_convert_to_schema_sanity():
    json_schema = json.dumps(
        {
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "The path of the file to save.",
                },
                "file_content": {
                    "type": "string",
                    "description": "The content of the file.",
                },
                "add_last_line_newline": {
                    "type": "boolean",
                    "description": "Whether to add a newline at the end of the file (default: true).",
                },
            },
            "required": ["file_path", "file_content"],
        }
    )
    assert convert_to_schema(json_schema, "test") == Schema(
        type=Type.OBJECT,
        properties={
            "file_path": Schema(
                type=Type.STRING, description="The path of the file to save."
            ),
            "file_content": Schema(
                type=Type.STRING, description="The content of the file."
            ),
            "add_last_line_newline": Schema(
                type=Type.BOOLEAN,
                description="Whether to add a newline at the end of the file (default: true).",
            ),
        },
        required=["file_path", "file_content"],
    )


def test_convert_to_schema_error():
    json_schema = json.dumps(
        {
            "type": "object",
            "properties": {
                "value": {"type": "int", "description": "The value to record."}
            },
            "required": ["value"],
        }
    )
    with pytest.raises(
        InvalidArgumentRpcError,
        match="Tool test failed input schema validation \(jsonschema 2020-12\)",
    ):
        convert_to_schema(json_schema, "test")


# --- Tests for Tool Name hydration based on Tool Use IDs ---


def test_format_response_message_content_populates_tool_map():
    tool_use_id_to_name = {}
    response_msg = [
        ChatResultNode(
            id=1,
            type=ChatResultNodeType.TOOL_USE,
            tool_use=ChatResultToolUse(
                tool_use_id="model_tool_id_123",
                name="test_tool_from_model",
                input={"arg1": "value1"},
            ),
            content="",
        )
    ]

    expected_content = GenaiContent(
        role="model",
        parts=[
            GenaiPart(
                function_call=GenaiFunctionCall(
                    name="test_tool_from_model",
                    args={"arg1": "value1"},
                    id="model_tool_id_123",
                )
            )
        ],
    )

    result_content = format_response_message_content(response_msg, tool_use_id_to_name)

    assert tool_use_id_to_name == {"model_tool_id_123": "test_tool_from_model"}
    assert result_content == expected_content


def test_format_response_message_content_raises_error_for_duplicate_tool_use_id():
    # Pre-populate the map with a tool use ID
    tool_use_id_to_name = {"duplicate_tool_id": "existing_tool_name"}

    # Create a response message with the same tool use ID
    response_msg = [
        ChatResultNode(
            id=1,
            type=ChatResultNodeType.TOOL_USE,
            tool_use=ChatResultToolUse(
                tool_use_id="duplicate_tool_id",  # Same ID as in the map
                name="another_tool_name",
                input={"param": "value"},
            ),
            content="",
        )
    ]

    # Test that the function raises an error for duplicate tool use ID
    with pytest.raises(InvalidArgumentRpcError) as excinfo:
        format_response_message_content(response_msg, tool_use_id_to_name)

    # Verify the error message
    assert "Multiple tool_use with same tool_use_id" in str(excinfo.value)


def test_format_request_message_content_uses_tool_map():
    tool_use_id_to_name = {"user_tool_id_456": "actual_tool_name_in_map"}
    request_msg = [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TOOL_RESULT,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="user_tool_id_456",
                content="tool result content",
                is_error=False,
            ),
            text_node=None,
        )
    ]
    expected_content = GenaiContent(
        role="user",
        parts=[
            GenaiPart(
                function_response=GenaiFunctionResponse(
                    name="actual_tool_name_in_map",
                    id="user_tool_id_456",
                    response={"output": "tool result content"},
                )
            )
        ],
    )

    result_content = format_request_message_content(request_msg, tool_use_id_to_name)
    assert result_content == expected_content


def test_format_request_message_content_id_not_in_map():
    tool_use_id_to_name = {}  # ID not in map
    request_msg = [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TOOL_RESULT,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="unknown_tool_id_789",
                content="another result",
                is_error=False,
            ),
            text_node=None,
        )
    ]
    format_request_message_content(request_msg, tool_use_id_to_name)


def test_tool_name_mapping_across_exchange_simulation():
    tool_use_id_to_name = {}  # Shared map

    # 1. Model responds with a tool use
    model_response_msg = [
        ChatResultNode(
            id=1,
            type=ChatResultNodeType.TOOL_USE,
            tool_use=ChatResultToolUse(
                tool_use_id="sim_tool_id_001",
                name="sim_tool_name",
                input={"param": "test"},
            ),
            content="",
        )
    ]
    format_response_message_content(model_response_msg, tool_use_id_to_name)
    assert tool_use_id_to_name == {"sim_tool_id_001": "sim_tool_name"}

    # 2. User provides result for that tool use
    user_request_msg = [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TOOL_RESULT,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="sim_tool_id_001",
                content="sim tool output",
                is_error=False,
            ),
            text_node=None,
        )
    ]
    expected_user_content = GenaiContent(
        role="user",
        parts=[
            GenaiPart(
                function_response=GenaiFunctionResponse(
                    name="sim_tool_name",  # Should be retrieved from map
                    id="sim_tool_id_001",
                    response={"output": "sim tool output"},
                )
            )
        ],
    )
    user_formatted_content = format_request_message_content(
        user_request_msg, tool_use_id_to_name
    )
    assert user_formatted_content == expected_user_content


# --- Tests for Client Stream Processing populating Tool Use IDs in responses ---

# Constants for GoogleGenaiClient instantiation
_TEST_PROJECT_ID = "test-project"
_TEST_REGION = "test-region"
_TEST_MODEL_NAME = "gemini-1.5-pro-001"  # A model that supports tools
_TEST_TEMPERATURE = 0.5
_TEST_MAX_OUTPUT_TOKENS = 100


@patch("base.third_party_clients.google_genai_client.genai_Client")
def test_generate_response_stream_generates_tool_id_if_missing(mock_genai_constructor):
    mock_sdk_client = MagicMock()
    mock_genai_constructor.return_value = mock_sdk_client

    client = GoogleGenaiClient(
        project_id=_TEST_PROJECT_ID,
        region=_TEST_REGION,
        model_name=_TEST_MODEL_NAME,
        temperature=_TEST_TEMPERATURE,
        max_output_tokens=_TEST_MAX_OUTPUT_TOKENS,
    )

    # Mock the generate_tool_use_id method
    client.generate_tool_use_id = MagicMock(return_value="generated_test_id_007")

    # Simulate SDK stream response with a FunctionCall MISSING an ID
    mock_actual_part = MagicMock()
    mock_actual_part.text = None
    mock_actual_part.function_call = GenaiFunctionCall(
        name="another_sdk_tool",
        args={"param": "arg"},
        id=None,  # ID is None
    )
    mock_content = MagicMock()
    mock_content.parts = [mock_actual_part]
    mock_candidate = MagicMock()
    mock_candidate.content = mock_content
    mock_chunk = MagicMock()
    mock_chunk.candidates = [mock_candidate]
    mock_chunk.usage_metadata = GenerateContentResponseUsageMetadata(
        prompt_token_count=10,
        candidates_token_count=0,
        cached_content_token_count=5,
        total_token_count=0,
    )
    mock_sdk_client.models.generate_content_stream.return_value = iter([mock_chunk])

    responses = list(
        client.generate_response_stream(cur_message="Test message", model_caller="test")
    )

    client.generate_tool_use_id.assert_called_once()

    assert len(responses) == 2
    assert responses[0].tool_use is not None
    assert responses[0].tool_use.tool_name == "another_sdk_tool"
    assert responses[0].tool_use.input == {"param": "arg"}
    assert responses[0].tool_use.tool_use_id == "generated_test_id_007"
    assert responses[1].end_of_stream is not None


@patch("base.third_party_clients.google_genai_client.genai_Client")
def test_generate_response_stream_generates_tool_id_if_empty_string(
    mock_genai_constructor,
):
    mock_sdk_client = MagicMock()
    mock_genai_constructor.return_value = mock_sdk_client

    client = GoogleGenaiClient(
        project_id=_TEST_PROJECT_ID,
        region=_TEST_REGION,
        model_name=_TEST_MODEL_NAME,
        temperature=_TEST_TEMPERATURE,
        max_output_tokens=_TEST_MAX_OUTPUT_TOKENS,
    )

    # Mock the generate_tool_use_id method
    client.generate_tool_use_id = MagicMock(return_value="generated_test_id_008")
    mock_actual_part = MagicMock()
    mock_actual_part.text = None
    mock_actual_part.function_call = GenaiFunctionCall(
        name="empty_id_tool",
        args={},
        id="",  # ID is empty string
    )
    mock_content = MagicMock()
    mock_content.parts = [mock_actual_part]
    mock_candidate = MagicMock()
    mock_candidate.content = mock_content
    mock_chunk = MagicMock()
    mock_chunk.candidates = [mock_candidate]
    mock_chunk.usage_metadata = GenerateContentResponseUsageMetadata(
        prompt_token_count=10,
        candidates_token_count=0,
        cached_content_token_count=5,
        total_token_count=0,
    )
    mock_sdk_client.models.generate_content_stream.return_value = iter([mock_chunk])

    responses = list(
        client.generate_response_stream(cur_message="Another test", model_caller="test")
    )

    client.generate_tool_use_id.assert_called_once()
    assert len(responses) == 2
    assert responses[0].tool_use is not None
    assert responses[0].tool_use.tool_use_id == "generated_test_id_008"
    assert responses[1].end_of_stream is not None


@patch("base.third_party_clients.google_genai_client._logger")
@patch("base.third_party_clients.google_genai_client.genai_Client")
def test_generate_response_stream_processes_only_first_tool_call_and_warns(
    mock_genai_constructor,
    mock_logger,
):
    mock_sdk_client = MagicMock()
    mock_genai_constructor.return_value = mock_sdk_client

    client = GoogleGenaiClient(
        project_id=_TEST_PROJECT_ID,
        region=_TEST_REGION,
        model_name=_TEST_MODEL_NAME,
        temperature=_TEST_TEMPERATURE,
        max_output_tokens=_TEST_MAX_OUTPUT_TOKENS,
    )
    # Mock generate_tool_use_id to ensure it's not called when IDs are provided
    client.generate_tool_use_id = MagicMock()

    # Simulate SDK stream response with a single chunk containing multiple function calls
    fc1 = GenaiFunctionCall(name="tool1", args={"p1": "v1"}, id="fcid1")
    part1 = GenaiPart(function_call=fc1)

    fc2 = GenaiFunctionCall(name="tool2", args={"p2": "v2"}, id="fcid2")
    part2 = GenaiPart(function_call=fc2)

    mock_content = MagicMock()
    mock_content.parts = [
        part1,
        part2,
    ]  # Multiple tool calls in the same content parts list
    mock_candidate = MagicMock()
    mock_candidate.content = mock_content
    mock_chunk = MagicMock()
    mock_chunk.candidates = [mock_candidate]
    mock_chunk.usage_metadata = GenerateContentResponseUsageMetadata(
        prompt_token_count=10,
        candidates_token_count=0,
        cached_content_token_count=5,
        total_token_count=0,
    )
    mock_sdk_client.models.generate_content_stream.return_value = iter([mock_chunk])

    responses = list(
        client.generate_response_stream(
            cur_message="Test with multiple tool calls", model_caller="test"
        )
    )

    # Assertions
    assert len(responses) == 2, "Should yield one tool use and one end_of_stream"

    # Check the first response (tool use)
    assert responses[0].text == ""
    assert responses[0].tool_use is not None, "First response should be a tool use"
    assert responses[0].tool_use.tool_name == "tool1"
    assert responses[0].tool_use.input == {"p1": "v1"}
    assert responses[0].tool_use.tool_use_id == "fcid1"
    assert responses[0].end_of_stream is None

    # Check the second response (end of stream)
    assert responses[1].text == ""
    assert responses[1].tool_use is None
    assert responses[1].end_of_stream is not None

    # Assert that the warning for the dropped tool call was logged
    mock_logger.warning.assert_called_once_with("Dropping a parallel tool call")
