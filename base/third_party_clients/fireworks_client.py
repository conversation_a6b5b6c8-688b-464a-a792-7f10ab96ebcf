"""Fireworks AI client."""

import json
from typing import Any, Call<PERSON>, Generator, Iterator, Optional

import prometheus_client
import structlog
from fireworks.client import Fireworks
from typing_extensions import override

from base.prompt_format.common import (
    Exchange,
    RequestMessage,
    StopReason,
)
from base.prompt_format_chat import get_token_counter_by_prompt_formatter_name
from base.third_party_clients.openai_client import (
    format_request_message_as_nodes,
    format_response_message_as_node,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ModelAPICall,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolChoiceType,
    ToolDefinition,
    ToolUseResponse,
    ToolUseStart,
)
from base.third_party_clients.token_counter.token_counter import TokenCounter

logger = structlog.get_logger("FireworksClient")

# Prometheus metrics for monitoring
_output_tool_use_blocks = prometheus_client.Counter(
    "au_fireworks_output_tool_use_blocks",
    "Number of tool use blocks from Fireworks client",
    ["model"],
)

_event_type_counter = prometheus_client.Counter(
    "au_fireworks_event_type",
    "Number of events from Fireworks client",
    ["model", "event_type"],
)


class FireworksClient(ThirdPartyModelClient):
    """
    A class to interact with OpenAI for generating responses.
    """

    @staticmethod
    def get_tool_definition(tool_name: str) -> dict[str, Any] | None:
        """Get tool definition in Fireworks format (OpenAI-compatible)."""
        fireworks_tool_definitions: dict[str, dict[str, Any]] = {
            "replace_text": {
                "type": "function",
                "function": {
                    "name": "replace_text",
                    "description": "Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "old_text": {
                                "type": "string",
                                "description": "The original text.",
                            },
                            "start_line_number": {
                                "type": "integer",
                                "description": "The line number where the original text starts, inclusive.",
                            },
                            "end_line_number": {
                                "type": "integer",
                                "description": "The line number where the original text ends, inclusive.",
                            },
                            "replacement_text": {
                                "type": "string",
                                "description": "The new text.",
                            },
                        },
                        "required": [
                            "old_text",
                            "start_line_number",
                            "end_line_number",
                            "replacement_text",
                        ],
                    },
                },
            },
            "command_output_contain_errors": {
                "type": "function",
                "function": {
                    "name": "command_output_contain_errors",
                    "description": "Report if the given command output contains errors.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "result": {
                                "type": "boolean",
                                "description": "Boolean indicating whether the command output contains errors.",
                            },
                            "desc": {
                                "type": "string",
                                "description": "Short and concise one line explanation.",
                            },
                        },
                        "required": ["result", "desc"],
                    },
                },
            },
        }

        return fireworks_tool_definitions.get(tool_name)

    @staticmethod
    def to_stop_reason(finish_reason: str) -> StopReason:
        """Convert Fireworks finish reason to StopReason."""
        match finish_reason:
            case "stop":
                return StopReason.END_TURN
            case "length":
                return StopReason.MAX_TOKENS
            case "tool_calls":
                return StopReason.TOOL_USE_REQUESTED
            case _:
                return StopReason.REASON_UNSPECIFIED

    def __init__(
        self,
        api_key: str,
        model_name: str,
        temperature: float,
        max_output_tokens: int,
        *,
        top_p: float | None = None,
        top_k: int | None = None,
        repetition_penalty: float | None = None,
    ):
        """Initialize the Fireworks AI client.

        Args:
            api_key: The API key for accessing Fireworks AI services.
            model_name: The name of the model to use for generating responses.
            temperature: Controls randomness in the model's output (0.0 to 1.0).
            max_output_tokens: Maximum number of tokens to generate in the response.
            top_p: Optional nucleus sampling parameter.
            top_k: Optional top-k sampling parameter.
            repetition_penalty: Optional repetition penalty parameter.
        """
        self.api_key = api_key
        self.model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.top_p = top_p
        self.top_k = top_k
        self.repetition_penalty = repetition_penalty
        self.client = Fireworks(api_key=self.api_key)
        self._token_counter = get_token_counter_by_prompt_formatter_name("binks-openai")

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """Generate a streaming response from the Fireworks AI model.

        Args:
            model_caller: A field passed in by the caller (could be chat, agent, etc.) to keep track of the caller, for metrics purposes.
            messages: Deprecated. List of (user, assistant) message tuples. Use chat_history instead.
            system_prompt: Optional system prompt to guide model behavior.
            cur_message: Current user message to generate a response for.
            chat_history: List of previous Exchange objects containing request/response pairs.
            tools: List of tool names available for the model to use.
            tool_definitions: List of tool definitions specifying tool interfaces.
            tool_choice: Specification for which tool the model should use.
            temperature: Sampling temperature for response generation (0.0 to 1.0).
            max_output_tokens: Maximum number of tokens in the generated response.
            prefill: Optional text to prefill response (not supported by Fireworks).
            use_caching: Whether to use response caching (not supported by Fireworks).
            request_context: Optional request context containing information about the request, including session ID.
            yield_final_parameters: Whether to yield a response with the final parameters used for the request.
            model_api_call_callback: Optional callback for recording API call information.

        Returns:
            Generator of ThirdPartyModelResponse objects containing response text chunks.

        Raises:
            ValueError: If unsupported parameters are provided.
        """
        if prefill is not None:
            logger.warning(
                "FireworksClient does not currently support prefill. Ignoring prefill parameter."
            )

        if use_caching:
            logger.warning(
                "FireworksClient does not currently support caching. Ignoring use_caching=True parameter."
            )

        logger.info(
            "FireworksClient generating response for %s messages", len(messages)
        )

        # Handle tool choice conversion
        tool_choice_param = None
        if tool_choice is not None:
            if tool_choice.type == ToolChoiceType.AUTO:
                tool_choice_param = "auto"
            elif tool_choice.type == ToolChoiceType.ANY:
                tool_choice_param = {"type": "function"}
            elif tool_choice.type == ToolChoiceType.TOOL:
                if tool_choice.name is None:
                    raise ValueError("ToolChoice of type TOOL requires tool name")
                tool_choice_param = {
                    "type": "function",
                    "function": {"name": tool_choice.name},
                }
            else:
                raise ValueError(f"Unknown tool_choice value: {tool_choice}")

        # Use provided temperature and max_output_tokens or fall back to instance defaults
        temp_param = temperature if temperature is not None else self.temperature
        tokens_param = (
            max_output_tokens
            if max_output_tokens is not None
            else self.max_output_tokens
        )

        formatted_messages = []
        if system_prompt is not None:
            formatted_messages.append(
                {
                    "role": "system",
                    "content": system_prompt,
                }
            )
        if chat_history is None:
            chat_history = [
                Exchange(request_message=message[0], response_text=message[1])
                for message in messages
            ]

        for message in chat_history:
            formatted_messages += format_request_message_as_nodes(
                message.request_message
            )
            formatted_messages.append(
                format_response_message_as_node(message.response_text)
            )

        # Add cur message
        formatted_messages += format_request_message_as_nodes(cur_message)

        # Get all tool definitions in Fireworks format (OpenAI-compatible)
        all_tool_definitions = [
            self.get_tool_definition(tool_name)
            for tool_name in tools
            if self.get_tool_definition(tool_name) is not None
        ] + [
            {
                "type": "function",
                "function": {
                    "name": tool_def.name,
                    "description": tool_def.description,
                    "parameters": json.loads(tool_def.input_schema_json),
                },
            }
            for tool_def in tool_definitions
        ]

        if yield_final_parameters:
            final_params: dict[str, Any] = {
                "model": self.model_name,
                "messages": formatted_messages,
                "temperature": temp_param,
                "max_completion_tokens": tokens_param,
                "tools": all_tool_definitions if all_tool_definitions else None,
                "tool_choice": tool_choice_param,
                "stream": True,
            }
            if self.top_p is not None:
                final_params["top_p"] = self.top_p
            if self.top_k is not None:
                final_params["top_k"] = self.top_k
            if self.repetition_penalty is not None:
                final_params["repetition_penalty"] = self.repetition_penalty
            yield ThirdPartyModelResponse(
                text="",
                final_parameters=final_params,
            )

        # Prepare API call parameters
        api_params: dict[str, Any] = {
            "model": self.model_name,
            "messages": formatted_messages,
            "temperature": temp_param,
            "max_completion_tokens": tokens_param,
            "stream": True,
        }
        # Optional sampling params supported by Fireworks
        if self.top_p is not None:
            api_params["top_p"] = self.top_p
        if self.top_k is not None:
            api_params["top_k"] = self.top_k
        if self.repetition_penalty is not None:
            api_params["repetition_penalty"] = self.repetition_penalty

        # Add tools if provided
        if all_tool_definitions:
            api_params["tools"] = all_tool_definitions
            if tool_choice_param is not None:
                api_params["tool_choice"] = tool_choice_param

        response_iterator = self.client.chat.completions.create(**api_params)  # type: ignore
        assert isinstance(response_iterator, Iterator)

        # Track tool calls during streaming
        tool_calls = {}
        stop_reason = StopReason.REASON_UNSPECIFIED

        for chunk in response_iterator:
            if not chunk.choices:
                continue

            choice = chunk.choices[0]

            # Handle text content
            if choice.delta.content:
                yield ThirdPartyModelResponse(text=choice.delta.content)

            # Handle tool calls
            if choice.delta.tool_calls:
                for delta_tool_call in choice.delta.tool_calls:
                    if delta_tool_call.index not in tool_calls:
                        tool_calls[delta_tool_call.index] = {
                            "id": None,
                            "name": None,
                            "argument_string": "",
                            "start_sent": False,
                        }
                    tool_call = tool_calls[delta_tool_call.index]

                    if id_ := delta_tool_call.id:
                        tool_call["id"] = id_
                    if function := delta_tool_call.function:
                        if name := function.name:
                            tool_call["name"] = name
                        if arguments := function.arguments:
                            tool_call["argument_string"] += arguments

                    # Send tool use start event
                    if (
                        not tool_call["start_sent"]
                        and tool_call["name"]
                        and tool_call["id"]
                    ):
                        tool_call["start_sent"] = True
                        _output_tool_use_blocks.labels(model=self.model_name).inc()
                        _event_type_counter.labels(
                            model=self.model_name, event_type="tool_use_start"
                        ).inc()

                        yield ThirdPartyModelResponse(
                            text="",
                            tool_use_start=ToolUseStart(
                                tool_name=tool_call["name"],
                                tool_use_id=tool_call["id"],
                            ),
                        )

            # Handle finish reason
            if choice.finish_reason:
                stop_reason = self.to_stop_reason(choice.finish_reason)

        # Process completed tool calls after stream ends
        for tool_call_data in tool_calls.values():
            if tool_call_data.get("id") and tool_call_data.get("name"):
                try:
                    argument_string = tool_call_data.get("argument_string", "")
                    if not argument_string:
                        parsed_args = {}
                    else:
                        try:
                            parsed_args = json.loads(argument_string)
                        except json.JSONDecodeError as e:
                            # Instead of raising, log the error and return a placeholder response,
                            # assuming the agent could recover from the error.
                            logger.error(
                                "Failed to parse tool arguments JSON",
                                tool_name=tool_call_data["name"],
                                tool_use_id=tool_call_data["id"],
                                arguments=tool_call_data.get("argument_string", ""),
                                error=str(e),
                            )
                            parsed_args = {
                                "error": str(e),
                                "argument_string": argument_string,
                            }

                    _output_tool_use_blocks.labels(model=self.model_name).inc()
                    _event_type_counter.labels(
                        model=self.model_name, event_type="tool_use"
                    ).inc()

                    tool_use_response = ToolUseResponse(
                        tool_name=tool_call_data["name"],
                        input=parsed_args,
                        tool_use_id=tool_call_data["id"],
                    )

                    yield ThirdPartyModelResponse(text="", tool_use=tool_use_response)
                except Exception as e:
                    logger.error(
                        "Error processing tool call",
                        tool_name=tool_call_data.get("name"),
                        tool_use_id=tool_call_data.get("id"),
                        error=str(e),
                    )

        yield ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(stop_reason=stop_reason),
        )

    @override
    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: An estimate of the number of tokens in the given message.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        return self._token_counter.count_tokens(message)

    @override
    def token_counter(self) -> TokenCounter:
        return self._token_counter
