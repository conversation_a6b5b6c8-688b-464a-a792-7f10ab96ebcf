"""Exceptions thrown by the component registry."""


class MissingComponentNameField(ValueError):
    """A reference to a component that is not registered."""

    def __init__(self, component_name: str):
        super().__init__(
            f"Component {component_name} missing required field `$component_name`."
        )
        self.component_name = component_name


class MissingComponentDefinition(ValueError):
    """A reference to a component that is not registered."""

    def __init__(self, component_name: str):
        super().__init__(f"Component {component_name} not registered.")
        self.component_name = component_name


class MissingComponentReference(ValueError):
    """A reference to a component that is not registered."""

    def __init__(self, component_name: str, reference_name: str):
        super().__init__(
            f"Component {component_name} references {reference_name}, which is missing."
        )
        self.component_name = component_name
        self.reference_name = reference_name


class ComponentTypeError(TypeError):
    """A reference to a component that is not of the expected type."""

    def __init__(
        self,
        component_name: str,
        reference_name: str,
        expected_type: type,
        actual_type: type,
    ):
        super().__init__(
            f"Component {component_name} references {reference_name}, which is not of type {expected_type}. "
            f"Instead, it is of type {actual_type}."
        )
        self.component_name = component_name
        self.reference_name = reference_name
        self.expected_type = expected_type
        self.actual_type = actual_type


class ComponentValidationError(ValueError):
    """A reference to a component that is not of the expected type."""

    def __init__(
        self,
        *errors: Exception,
    ):
        super().__init__(*errors)
        self.errors = errors
