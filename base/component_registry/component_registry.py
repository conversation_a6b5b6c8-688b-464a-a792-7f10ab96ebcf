"""A lightweight registry for components."""

from __future__ import annotations

import dataclasses
import functools
import inspect
import logging
import threading
import types
import typing
from collections.abc import Collection, MutableMapping
from typing import (
    Any,
    Callable,
    Generic,
    Mapping,
    Optional,
    ParamSpec,
    Sequence,
    Type,
    TypeVar,
    Union,
    cast,
)

import dataclasses_json
import graphlib
import marshmallow
from dataclasses_json import DataClassJsonMixin
from immutabledict import immutabledict
from marshmallow import fields as mm_fields

from base.collections.typesafe_dict import TypesafeDict
from base.component_registry.exceptions import (
    ComponentTypeError,
    ComponentValidationError,
    MissingComponentDefinition,
    MissingComponentNameField,
    MissingComponentReference,
)

logger = logging.getLogger(__name__)


METADATA_KEY = "component_registry"
"""Key used to store reference type information in the dataclass field metadata."""

COMPONENT_NAME_KEY = "component_name"
"""Key used to store component name in the dataclass field metadata."""

REFERENCE_PREFIX = "$"
"""Prefix used for the component name key and for component references.

Note that for backward-compatibility, the $ is optional, but it is recommended to
highlight features specific to the component registry framework.
"""

COMPONENT_DELIMITER = "/"
"""Delimiter used to separate names in nested components when serializing configs.

NOTE(arun): We use '/' as the delimiter between the names in nested components to avoid
conflicting with '.' which is used as a delimiter between nested dictionary values.
"""


@dataclasses.dataclass
class ComponentConfig(DataClassJsonMixin):
    """Base class for all component configs.

    This base class: (1) ensures the dataclass_json is used in a way that plays well
    with static type checking, and (2) adds the required `component_name` field.
    """

    component_name: str = dataclasses.field(
        metadata=dataclasses_json.config(mm_field=mm_fields.String())
    )
    """The kind of component this config is for.

    This field is required to dynamically dispatch the config to the correct component
    builder. It should be set to the simple name of the component class.
    """


# Common type aliases
ComponentMap = TypesafeDict
"""A type-safe dictionary mapping names to components.

This is used to return created components or pass in existing ones.
"""

ConfigT = TypeVar("ConfigT", bound=ComponentConfig)
"""Type alias for the config type."""
ComponentT = TypeVar("ComponentT")
"""Type alias for component class types."""
P = ParamSpec("P")
"""Type alias for component object types."""
ComponentBuilder = Callable[[ConfigT, ComponentMap], ComponentT]
"""Type alias for component object types."""


@dataclasses.dataclass(frozen=True)
class RefType:
    """A list of typed name references to components."""

    typ: type
    container_type: Union[None, type[list], type[dict]]
    allow_unset: bool = False


@dataclasses.dataclass
class _ComponentInfo(Generic[ConfigT, ComponentT]):
    """Keeps track of a component's type, configuration type and builder."""

    config_cls: type[ConfigT]
    component_cls: type[ComponentT]
    builder: ComponentBuilder[ConfigT, ComponentT]


class ComponentRegistry:
    """Allows registered components to be constructed from configuration dicts.

    Typically one `ComponentRegistry` is created in each package. The names of the
    components are the simple names of the component classes and must be unique within
    the registry.
    """

    def __init__(self, strict: bool = False):
        """Create a new component registry.

        Args:
            strict: If True, raise exceptions when component with the same name as an
                existing component is registered. Otherwise, log a warning and override
                the registration. Setting strict=False is needed to use the registry
                when reloading modules, e.g. in a notebook.
        """
        self._lock = threading.Lock()
        self._registry: dict[str, _ComponentInfo] = {}
        self.strict = strict

    def register_component(
        self, config_cls: type[ConfigT], component_cls: type[ComponentT]
    ) -> Callable[
        [ComponentBuilder[ConfigT, ComponentT]], ComponentBuilder[ConfigT, ComponentT]
    ]:
        """A decorator to register a component builder.

        Usage:
        >>> @ExampleRegistry.register_component(SimpleConfig, SimpleWidget)
        ... def create_widget(cfg: SimpleConfig, _) -> SimpleWidget:
        ...     return SimpleWidget(cfg.param)
        """

        def decorator(builder: ComponentBuilder[ConfigT, ComponentT]):
            """Returns the decorated component builder."""
            self._register_info(
                _get_full_name_for_type(component_cls),
                _ComponentInfo(config_cls, component_cls, builder),
            )
            return builder

        return decorator

    @typing.overload
    def autoregister(self, cls_or_func: type[ComponentT]) -> type[ComponentT]: ...

    @typing.overload
    def autoregister(
        self, cls_or_func: Callable[P, ComponentT]
    ) -> Callable[P, ComponentT]: ...

    def autoregister(self, cls_or_func: type[ComponentT] | Callable[P, ComponentT]):
        """Attempt to automatically register a component using type hints.

        This function can be used either as a decorator or as a function, and creates a
        config dataclass and a component builder from the type hints. The dataclass is
        used to validate a configuration when loading it from the registry.
        ```python

        # Usage as a decorator.
        @ExampleRegistry.autoregister
        class SimpleWidget:
            def __init__(self, param: str):
                self.param = param

        # Used as a function.
        MyOtherRegistry.autoregister(SimpleWidget)

        # Usage to create components -- the configuration dict is validated before
        # any components are created!
        ExampleRegistry.create_components({"widget1": {
            "component_name": "SimpleWidget",
            "param": "foo",
        }})
        ```

        Autoregister supports any class with a fully-typed constructor, or any function
        with fully-typed arguments. Simple JSON-able parameters (i.e. strings, bools,
        numbers, dataclass-jsons, and lists/dicts of the same) are represented as is
        in the config, while other object types appear in the config as _references_.

        In the configuration dictionary, every component is named, and references are
        simply the name of another component in the dictionary. Existing Python objects
        can also be given names and passed into `create_components` through the
        `existing_components` argument. See the example below:

        ```python
        # ...
        class MultiWidget:
            def __init__(self, widgets: list[SimpleWidget], default_index: int):
                assert default_index < len(widgets), "Default index out of bounds."
                self.widgets = widgets
                self.default_index = default_index

        ExampleRegistry.autoregister(MultiWidget)

        # Create the widgets.
        ExampleRegistry.create_components({
            "multi": {
                "component_name": "MultiWidget",
                # Here we are referencing widget1 and widget2.
                # For the purposes of this example, we'll specify widget1 in the
                # configuration, and pass in widget2 via `existing_components` -- it's
                # more common you'll do one or the other though.
                "widgets": ["$widget1", "$widget2"],
                "default_index": 0,
            },
            # This is an example of creating the `widget1` referenced above in the
            # component configuration.
            "widget1": {
                "component_name": "SimpleWidget",
                "param": "foo",
            },
        },
        # This is an example of passing in `widget2` as an existing component.
        existing_components={"widget2": SimpleWidget("bar")})
        ```

        Finally, autoregister can also be used with functions with a return type:
        ```python
        def pick_widget(widgets: list[SimpleWidget], index: int) -> SimpleWidget:
            assert index < len(widgets), "Default index out of bounds."
            return widgets[index]

        ExampleRegistry.autoregister(pick_widget)

        # Create the widgets.
        ExampleRegistry.create_components({
            "my_widget": {
                "$component_name": "pick_widget",
                # Here we are referencing widget1 and widget2.
                "widgets": ["$widget1", "$widget2"],
                "index": 1,
            },
            "widget1": {
                "$component_name": "SimpleWidget",
                "param": "foo",
            },
        }, existing_components={"widget2": SimpleWidget("bar")})
        ```
        """
        # 1. Create a config dataclass representing the arguments of the callable.
        signature = inspect.signature(cls_or_func)
        empty = inspect.Parameter.empty
        sig_str = f"{_get_full_name_for_type(cls_or_func)}{signature}"

        if COMPONENT_NAME_KEY in signature.parameters:
            raise ValueError(
                f"`{COMPONENT_NAME_KEY}` is a reserved field in {sig_str}."
            )
        if any(param.annotation is empty for param in signature.parameters.values()):
            raise ValueError(
                "Cannot autoregister a component with missing parameter type "
                f"annotations: {sig_str}."
            )
        if not isinstance(cls_or_func, type) and signature.return_annotation is empty:
            raise ValueError(
                "Cannot autoregister a function with missing return type annotations: "
                f"{sig_str}."
            )
        if any(
            isinstance(param.annotation, str) for param in signature.parameters.values()
        ):
            logger.warning(
                f"Found string type annotations for {sig_str}. This is likely to fail "
                "for anything but basic types. "
                "Avoid `from __future__ import annotations` if possible."
            )

        fields = []

        # Add the component kind field.
        return_typ = (
            signature.return_annotation
            if signature.return_annotation is not empty
            and signature.return_annotation is not None
            else cls_or_func
        )
        fields.append((COMPONENT_NAME_KEY, str, dataclasses.field()))

        # Add all the other fields
        for param_name, param in signature.parameters.items():
            type_annotation = param.annotation

            if _is_json_serializable(type_annotation):
                # If the type annotation suggests that the parameter is a basic type
                # or a dataclass, we'll put it into the config.
                field_value = (
                    dataclasses.field(default=param.default)
                    if param.default is not empty
                    else dataclasses.field()
                )
            # Otherwise, we'll assume the parameter will be constructed elsewhere
            # and passed in as a reference.
            elif (typ_ := _unpack_optional(type_annotation)) is not None:
                _log_reference_type(typ_)
                has_default = param.default is not empty
                field_value = ref_field(
                    typ_,
                    allow_unset=has_default,
                    default=None if has_default else cast(None, dataclasses.MISSING),
                )
                type_annotation = Optional[str]
            else:
                _log_reference_type(type_annotation)
                field_value = ref_field(type_annotation)
                if typing.get_origin(type_annotation) is list:
                    type_annotation = list[str]
                elif typing.get_origin(type_annotation) is dict:
                    type_annotation = dict[str, str]
                else:
                    type_annotation = str

            fields.append((param_name, type_annotation, field_value))

        config_cls: type[ComponentConfig] = dataclasses_json.dataclass_json(
            dataclasses.make_dataclass(cls_or_func.__name__ + "Config", fields=fields)
        )

        # 2. Create a builder from the config class.
        def builder_fn(
            cfg: dict | DataClassJsonMixin, components: ComponentMap
        ) -> ComponentT:
            if isinstance(cfg, dict):
                cfg = config_cls.schema().load(cfg)

            # Prepare the arguments for the function call.
            kwargs = {}
            for name, _, dataclass_field in fields:
                if name == COMPONENT_NAME_KEY:
                    # Ignore the component class field.
                    continue
                if (ref_type := dataclass_field.metadata.get(METADATA_KEY)) is not None:
                    ref_type: RefType
                    if ref_type.container_type is None:
                        # This is a single reference field.
                        ref_name = getattr(cfg, name)
                        kwargs[name] = components.get(ref_name)
                    elif ref_type.container_type is list:
                        # This is a container field.
                        kwargs[name] = [
                            components.get(ref_name) for ref_name in getattr(cfg, name)
                        ]
                    elif ref_type.container_type is dict:
                        # This is a container field.
                        kwargs[name] = {
                            key: components.get(ref_name)
                            for key, ref_name in getattr(cfg, name).items()
                        }
                    else:
                        raise ValueError(
                            f"Unexpected container type: {ref_type.container_type}"
                        )
                else:
                    kwargs[name] = getattr(cfg, name)

            # NOTE(arun): type safety: We create kwargs using the signature of the
            # function, so this will be safe.
            return cls_or_func(**kwargs)  # type: ignore

        self._register_info(
            _get_full_name_for_type(cls_or_func),
            _ComponentInfo(config_cls, return_typ, builder_fn),
        )

        return cls_or_func

    def create_components(
        self,
        config_dcts: Mapping[str, dict],
        existing_components: ComponentMap | dict | None = None,
        validate_only: bool = False,
    ) -> ComponentMap:
        """Create components from the given configurations. See `create_components`."""
        return create_components(
            config_dcts, [self], existing_components, validate_only
        )

    def _register_info(self, component_name: str, info: _ComponentInfo) -> None:
        """Register a ComponentInfo."""
        with self._lock:
            if component_name in self._registry:
                if self.strict:
                    raise ValueError(
                        f"Trying to register another component with {component_name=}. "
                        f"Original component: {self._registry['component_name']}, "
                        f"new component: {info}. If you want to replace the existing "
                        "component, set strict=False."
                    )
                else:
                    logger.warning(
                        "Replacing existing component with name %s. "
                        "Original component: %s, new component: %s.",
                        component_name,
                        self._registry[component_name],
                        info,
                    )
            self._registry[component_name] = info

    def _get_info(self, component_name: str) -> _ComponentInfo:
        """Fetch ComponentInfo by component name."""
        with self._lock:
            return self._registry[component_name]

    def __contains__(self, component_name_or_typ: str | type[ComponentT]) -> bool:
        """Check if a component kind is registered."""
        if isinstance(component_name_or_typ, (type, Callable)):
            component_name_or_typ = _get_full_name_for_type(component_name_or_typ)

        with self._lock:
            # Early exit if we have an exact name match.
            return component_name_or_typ in self._registry or any(
                cls_name.endswith(component_name_or_typ)
                for cls_name, info in self._registry.items()
            )


def create_components(
    config_dcts: Mapping[str, dict],
    registries: Sequence[ComponentRegistry],
    existing_components: ComponentMap | dict | None = None,
    validate_only: bool = False,
) -> ComponentMap:
    """Create components from the given configurations.

    This function validates configurations by checking:
    1. All component kinds are registered.
    2. All configs are valid.
    3. All refs exist without cycles.
    4. All required components are present.

    Args:
        config_dcts: A dictionary mapping component names to their configurations.
        registries: The component registries to use. We will resolve the component kinds
            from these registries, with the first registry that contains the component
            kind taking precedence.
        existing_components: The existing components to use.
        validate_only: If true, we will not create any components, only validate
            the configurations.

    Returns:
        A map from component names to their components.
    """
    components = (
        ComponentMap(existing_components)
        if existing_components is not None
        else ComponentMap()
    )

    errors = []
    # 1a. Normalize the components names.
    configs = _normalize_component_names(config_dcts, errors)
    if any(errors):
        raise ComponentValidationError(*errors)

    # 1b. Resolve the component kinds.
    infos = _resolve_component_infos(registries, configs, errors)
    if any(errors):
        raise ComponentValidationError(*errors)

    # 2a. Normalize the reference names.
    configs = _normalize_reference_names(configs, infos, errors)
    if any(errors):
        raise ComponentValidationError(*errors)

    # 2b. Parse the configs.
    configs = _parse_configs(configs, infos, errors)
    if any(errors):
        raise ComponentValidationError(*errors)

    # 3. Validate that all refs exist without cycles.
    component_order = _sort_component_infos(
        configs,
        infos,
        components,
        errors,
    )
    if any(errors):
        raise ComponentValidationError(*errors)

    if validate_only:
        return ComponentMap()

    for name in component_order:
        components[name] = infos[name].builder(configs[name], components)
    return components


def create_component(
    self,
    config: dict,
    component_cls: type[ComponentT],
    existing_components: Optional[ComponentMap] = None,
) -> ComponentT:
    """Create a single component.

    This is a utility version of `create_components` that only creates a single
    component. It is useful for testing and for creating components from a single
    config.
    """
    if (
        f"{REFERENCE_PREFIX}{COMPONENT_NAME_KEY}" not in config
        and COMPONENT_NAME_KEY not in config
    ):
        config = config.copy()
        config[COMPONENT_NAME_KEY] = _get_full_name_for_type(component_cls)

    sentinel_key = "__main__"
    assert (
        not existing_components or sentinel_key not in existing_components
    ), f"Did not expect to find {sentinel_key=} in existing components."
    components = create_components({sentinel_key: config}, [self], existing_components)
    return components.get_with_type(sentinel_key, component_cls)


@typing.overload
def ref_field(
    typ: type[list],
    *,
    default: Sequence[str] = cast(Sequence[str], dataclasses.MISSING),
) -> list[str]: ...


@typing.overload
def ref_field(
    typ: type[dict],
    *,
    default: Mapping[str, str] = cast(Mapping[str, str], dataclasses.MISSING),
) -> dict[str, str]: ...


@typing.overload
def ref_field(
    typ: type,
    *,
    default: str = cast(str, dataclasses.MISSING),
) -> str: ...


@typing.overload
def ref_field(
    typ: type,
    *,
    allow_unset: bool,
    default: Optional[str] = cast(None, dataclasses.MISSING),
) -> Optional[str]: ...


def ref_field(typ, *, default=dataclasses.MISSING, allow_unset=False):
    """Create a dataclass field for a reference to a component.

    The returned field includes metadata to encode and decode the reference as JSON,
    and to keep around the required type information to validate the reference.

    The keyword arguments have the same semantics as those of dataclass.field.

    Args:
        typ: The type of the reference.
        default: The default value of the reference.

    Returns:
        A dataclass field for the reference.
    """
    if typing.get_origin(typ) is list:
        if len(typing.get_args(typ)) != 1:
            raise ValueError(
                f"`ref_field` with list type must be fully typed, got {typ}"
            )
        (typ_,) = typing.get_args(typ)

        if default is not dataclasses.MISSING:
            # Make the default immutable.
            default = tuple(default)

        metadata = {
            METADATA_KEY: RefType(typ_, list),
            **dataclasses_json.config(mm_field=mm_fields.List(mm_fields.String())),
        }
    elif typing.get_origin(typ) is dict:
        if len(typing.get_args(typ)) != 2:
            raise ValueError(
                f"`ref_field` with dict type must be fully typed, got {typ}"
            )
        elif typing.get_args(typ)[0] != str:
            raise ValueError(
                f"`ref_field` with dict type must keyed by strings, got {typ}"
            )
        _, typ_ = typing.get_args(typ)

        if default is not dataclasses.MISSING:
            # Make the default immutable.
            default = immutabledict(default)
        metadata = {
            METADATA_KEY: RefType(typ_, dict),
            **dataclasses_json.config(
                mm_field=mm_fields.Dict(mm_fields.String(), mm_fields.String())
            ),
        }
    else:
        if allow_unset and default is dataclasses.MISSING:
            default = None

        metadata = {
            METADATA_KEY: RefType(typ, None, allow_unset=allow_unset),
            **dataclasses_json.config(
                mm_field=mm_fields.String(allow_none=allow_unset)
            ),
        }

    return dataclasses.field(default=default, metadata=metadata)


# Implementation details.
def _normalize_component_names(
    config_dcts: Mapping[str, dict],
    errors: list[Exception],
) -> dict[str, dict]:
    """Normalize component names that use the `$` syntax.

    Component names start with a optional `$` character to indicate that they are
    references to existing components. Using the `$` is recommended but not required
    for backward compatibility. This function normalizes the component names to remove
    the `$` character.

    Args:
        config_dcts: The component configurations.
        errors: A list of errors that may be updated by this function.

    Returns:
        The normalized component configurations.
    """
    ret = {}
    for name, config_dct in config_dcts.items():
        """Drop the leading $ if it exists."""
        if COMPONENT_NAME_KEY in config_dct:
            ret[name] = config_dct
        elif REFERENCE_PREFIX + COMPONENT_NAME_KEY in config_dct:
            config_dct = dict(config_dct)
            config_dct[COMPONENT_NAME_KEY] = config_dct.pop(
                REFERENCE_PREFIX + COMPONENT_NAME_KEY
            )
            ret[name] = config_dct
        else:
            errors.append(
                MissingComponentNameField(config_dct.get(COMPONENT_NAME_KEY, name))
            )

    return ret


def _resolve_component_infos(
    registries: Sequence[ComponentRegistry],
    configs: Mapping[str, dict],
    errors: list[Exception],
) -> dict[str, _ComponentInfo]:
    """Resolve the component kinds from the given registries."""
    infos = dict[str, _ComponentInfo]()

    # 1. Resolve the component kinds.
    for name, config_dct in configs.items():
        # This is checked in `_normalize_component_names`.
        assert (
            COMPONENT_NAME_KEY in config_dct
        ), f"Component {name} missing required field `{COMPONENT_NAME_KEY}`."

        component_name = config_dct[COMPONENT_NAME_KEY]
        # Try to resolve this component class from the registries.
        candidates = [
            info
            for registry in registries
            # pylint: disable-next=protected-access
            for cls_name, info in registry._registry.items()
            if cls_name.endswith(component_name)
        ]

        if len(candidates) == 1:
            (info,) = candidates
            infos[name] = info
        else:
            error = MissingComponentDefinition(component_name)
            if errors:
                errors.append(error)
            else:
                raise error

    return infos


def _normalize_reference_names(
    configs: Mapping[str, dict],
    infos: dict[str, _ComponentInfo],
    errors: list[Exception],
) -> Mapping[str, dict]:
    """Normalize reference names that use the `$` syntax.

    Reference values start with a optional `$` character to indicate that they are
    references to existing components. Using the `$` is recommended but not required
    for backward compatibility. This function normalizes the reference names to remove
    the `$` character.

    Args:
        configs: The component configurations.
        infos: The component infos.
        errors: A list of errors that may be updated by this function.

    Returns:
        The normalized component configurations.
    """
    # We don't currently flag any errors in the implementation below, but keeping it
    # around for consistency.
    del errors

    def _normalize_reference_name(ref_name: str) -> str:
        """Drop the leading $ if it exists."""
        return ref_name[1:] if ref_name.startswith(REFERENCE_PREFIX) else ref_name

    for name, info in infos.items():
        for field in dataclasses.fields(info.config_cls):
            # Skip fields that are not references.
            if (ref_type := field.metadata.get(METADATA_KEY)) is None:
                continue
            assert isinstance(ref_type, RefType)

            # Skip fields that are not set.
            if (field_value := configs[name].get(field.name)) is None:
                continue

            if ref_type.container_type is None:
                configs[name][field.name] = _normalize_reference_name(field_value)
            elif ref_type.container_type is list:
                configs[name][field.name] = [
                    _normalize_reference_name(ref_name) for ref_name in field_value
                ]
            elif ref_type.container_type is dict:
                configs[name][field.name] = {
                    key: _normalize_reference_name(ref_name)
                    for key, ref_name in field_value.items()
                }
            else:
                assert False, f"Unexpected container type: {ref_type.container_type}"
    return configs


def _parse_configs(
    configs: Mapping[str, dict],
    infos: dict[str, _ComponentInfo],
    errors: list[Exception],
) -> dict[str, ComponentConfig]:
    """Parse the config dicts into their corresponding dataclasses."""
    configs_dict = dict[str, ComponentConfig]()
    for name, info in infos.items():
        try:
            configs_dict[name] = info.config_cls.schema().load(configs[name])
        except marshmallow.ValidationError as e:
            errors.append(e)
    return configs_dict


def _check_references(
    name: str,
    config: ComponentConfig,
    infos: dict[str, _ComponentInfo],
    existing_components: ComponentMap,
    errors: list[Exception],
) -> Collection[str]:
    """Get the references in the config, check they exist and are of the right type."""
    assert dataclasses.is_dataclass(config)
    ref_names = set[str]()

    for field in dataclasses.fields(config):
        # Skip fields that are not references.
        if (ref_type := field.metadata.get(METADATA_KEY)) is None:
            continue
        assert isinstance(ref_type, RefType)

        ref_names_ = set[str]()
        field_value = getattr(config, field.name)
        if ref_type.container_type is None and not ref_type.allow_unset:
            assert isinstance(field_value, str)
            ref_names_.add(field_value)
        elif ref_type.container_type is None:
            if field_value is not None:
                ref_names_.add(field_value)
        elif ref_type.container_type is list:
            ref_names_.update(field_value)
        elif ref_type.container_type is dict:
            ref_names_.update(field_value.values())
        else:
            assert False, f"Unexpected container type: {ref_type.container_type}"

        # Type check the references.
        expected_type = ref_type.typ
        for ref_name in ref_names_:
            if ref_name in existing_components:
                if isinstance(existing_components[ref_name], expected_type):
                    continue

                actual_type = type(existing_components[ref_name])
                errors.append(
                    ComponentTypeError(name, ref_name, expected_type, actual_type)
                )
            elif ref_name in infos:
                actual_type = infos[ref_name].component_cls
                if issubclass(actual_type, expected_type):
                    ref_names.add(ref_name)
                    continue

                errors.append(
                    ComponentTypeError(name, ref_name, expected_type, actual_type)
                )
            else:
                errors.append(MissingComponentReference(name, ref_name))

    return ref_names


def _sort_component_infos(
    configs: dict[str, ComponentConfig],
    infos: dict[str, _ComponentInfo],
    existing_components: ComponentMap,
    errors: list[Exception],
) -> Sequence[str]:
    """Sort the components in topological order (checking for cylic dependencies)."""
    dependency_graph = graphlib.TopologicalSorter()
    for name, config in configs.items():
        ref_names = _check_references(name, config, infos, existing_components, errors)
        dependency_graph.add(name, *ref_names)
    try:
        return list(dependency_graph.static_order())
    except graphlib.CycleError as e:
        errors.append(e)
        return []


def _is_json_serializable(typ: type | str) -> bool:
    """Check if a type is a simple type that can be represented in a dataclass json."""
    if typ in (
        # Basic Python types.
        str,
        int,
        float,
        bool,
        type(None),
        # Include string versions of the basic types. These can show up if the user
        # has run `from __future__ import annotations`.
        str.__name__,
        int.__name__,
        float.__name__,
        bool.__name__,
        "None",
    ):
        return True
    # NOTE(arun): "int | str" is types.UnionType not typing.Union. Don't ask why.
    elif typing.get_origin(typ) in (
        tuple,
        list,
        dict,
        set,
        typing.Union,
        types.UnionType,
    ):
        # Container types that are fully JSON serializable.
        return all(_is_json_serializable(arg) for arg in typing.get_args(typ))
    elif isinstance(typ, type) and typ in dataclasses_json.global_config.decoders:
        # Type has been registered to be JSON serializable.
        return True
    elif isinstance(typ, type) and issubclass(typ, DataClassJsonMixin):
        # Type is a dataclass JSON so can be serialized as well.
        return True
    else:
        return False


def _unpack_optional(typ: type) -> Optional[type]:
    """Unpack an optional type."""
    if typing.get_origin(typ) is typing.Union:
        args = typing.get_args(typ)
        if len(args) == 2 and type(None) in args:
            (typ_,) = [arg for arg in args if arg is not type(None)]
            return typ_
    return None


def _get_full_name_for_type(cls_or_func: Callable | type) -> str:
    """Get the full name of a type."""
    return f"{cls_or_func.__module__}.{cls_or_func.__qualname__}"


# Cache calls to this function to not spam the user with log messages.
@functools.lru_cache(maxsize=1024)
def _log_reference_type(typ: type):
    logger.info(
        "Auto-registering a function or class with an argument of type %s which isn't "
        "JSON-serializable. This field will be configured via reference.",
        typ,
    )


@typing.runtime_checkable
class Serializable(typing.Protocol):
    def create_config(self) -> Mapping[str, typing.Any]:
        """Create a configuration dictionary for this component.

        Returns:
            A dictionary of configuration values sufficient to recreate this component.
            The dictionary may contain other Serializable, in which case
            `serialize_component_configs` will call `create_config` recursively.
        """
        raise NotImplementedError()


def serialize_component_configs(
    components: Mapping[str, Serializable],
) -> Mapping[str, dict]:
    """Serializes a mapping of name to components into name to configuration dictionary.

    This function tries to convert a mapping from names to components into a corresponding
    mapping of names to configuration dictionaries. It does so by calling
    `create_config` on each value, which in turn returns a configuration dictionary of
    arguments to its constructor.

    If any of these arguments are themselves `Serializable`, this function recursively
    calls `create_config` on them. However, when we do so, we add these sub-component's
    configurations to the top-level dictionary with a unique name and reference this
    name in the original configuration.

    Example (also in README.md):
    ```python
    # A simple component with a single string parameter.
    @dataclass
    class Simple:
        a_str: str

        def create_config(self):
            return {
                "a_str": self.a_str,
            }

    # A more complex component that contains a sub-component and a config dict.
    @dataclass
    class Complex:
        sub_component: Simple
        other_config: dict

        def create_config(self):
            return {
                "sub_component": self.simple,
                "other_config": self.config,
            }

    # Let's look athe the serialized configuration we create
    assert serialize_component_configs({
        "complex": Complex(Simple("bar"), {"foo": "bar"}),
    }) == {
        "complex": {
            # A fully-qualified component class name is saved by the function.
            "$component_name": "my_module.Complex",
            # As noted above, the sub-component's configuration is added to the
            # top-level dictionary below with a unique name, and is replaced with a
            # reference here.
            "sub_component": "$complex/simple",
            "other_config": {
                "foo": "bar",
            },
        },
        "complex/simple": {
            "$component_name": "Simple",
            "a_str": "bar",
        },
    }
    ```

    Args:
        components: A mapping from names to components.

    Returns:
        A mapping from names to configuration dictionaries. This mapping includes
        configurations corresponding to the components in `components`, as well as any
        sub-components that appear in the components' configurations.
    """
    for name, component in components.items():
        if not isinstance(component, Serializable):
            raise ValueError(
                f"Component {name}={component} missing required method `create_config()`."
            )

    refs = dict[int, str]()
    config = dict[str, dict]()

    while components:
        new_components = dict[str, Serializable]()

        for name, component in components.items():
            # This should never happen because we namespace names.
            assert name not in config, f"Duplicate component name: {name}"
            # This should never happen because we only ever add collectables to
            # components.
            assert isinstance(
                component, Serializable
            ), f"{name}={component} missing required method `create_config()`."

            # Register the component if it hasn't been seen before.
            if id(component) not in refs:
                refs[id(component)] = name

            # Before replacing any serializables, first validate that the config can be
            # used to call the constructor of the component correctly.
            unserialized_config = component.create_config()
            _validate_config_for_component(unserialized_config, type(component))

            config[name] = _replace_serializables(
                unserialized_config, refs, new_components, name
            )
            # Add a fully-qualified name of the component type.
            config[name][REFERENCE_PREFIX + COMPONENT_NAME_KEY] = (
                _get_full_name_for_type(type(component))
            )

        components = new_components
    return config


@typing.overload
def _replace_serializables(
    obj: Mapping[str, Any],
    refs: dict[int, str],
    new_components: dict[str, Serializable],
    name: str,
) -> dict: ...


@typing.overload
def _replace_serializables(
    obj: Sequence[Any],
    refs: dict[int, str],
    new_components: dict[str, Serializable],
    name: str,
) -> list: ...


@typing.overload
def _replace_serializables(
    obj: Serializable,
    refs: dict[int, str],
    new_components: dict[str, Serializable],
    name: str,
) -> str: ...


def _replace_serializables(
    obj,
    refs: dict[int, str],
    new_components: dict[str, Serializable],
    name: str,
):
    if isinstance(obj, Serializable):
        if id(obj) not in refs:
            refs[id(obj)] = name
            new_components[name] = obj
        return REFERENCE_PREFIX + refs[id(obj)]
    elif isinstance(obj, DataClassJsonMixin):
        # Special casing for dataclasses_json because it's central for this module.
        return obj.schema().dump(obj)
    elif _is_json_serializable(type(obj)):
        return obj
    elif isinstance(obj, Mapping):
        return {
            key: _replace_serializables(
                value, refs, new_components, f"{name}{COMPONENT_DELIMITER}{key}"
            )
            for key, value in obj.items()
        }
    elif isinstance(obj, Sequence):
        return [
            _replace_serializables(
                value, refs, new_components, f"{name}{COMPONENT_DELIMITER}{key}"
            )
            for key, value in enumerate(obj)
        ]
    else:
        raise ValueError(
            f"Component {name}={obj} is not JSON-serializable. "
            "Please make sure all components are JSON-serializable."
        )


def _validate_config_for_component(
    config: Mapping[str, Any], component_cls: type
) -> None:
    """Validate that the config can be used to construct the component.

    Args:
        config: The serialized configuration dictionary.
        component_cls: The component class.
    """
    signature = inspect.signature(component_cls)
    empty = inspect.Parameter.empty
    sig_str = f"{_get_full_name_for_type(component_cls)}{signature}"

    if COMPONENT_NAME_KEY in signature.parameters:
        raise ValueError(
            f"`{COMPONENT_NAME_KEY}` is a reserved field and can't be used with {sig_str}."
        )
    if any(param.annotation is empty for param in signature.parameters.values()):
        raise ValueError(
            "Cannot safely serialize a component with missing parameter type "
            f"annotations: {sig_str}."
        )
    if any(
        isinstance(param.annotation, str) for param in signature.parameters.values()
    ):
        logger.warning(
            f"Found string type annotations for {sig_str}. This is likely to fail "
            "for anything but basic types. "
            "Avoid `from __future__ import annotations` if possible."
        )

    # Add all the other fields
    errors = []
    for param_name, param in signature.parameters.items():
        type_annotation = param.annotation

        if param_name not in config and param.default is not empty:
            continue
        elif param_name not in config:
            errors.append(f"Missing required parameter {param_name} in {sig_str}.")
        elif not _dynamic_type_check(config[param_name], type_annotation):
            errors.append(
                f"Parameter {param_name} in {sig_str} has type {type(config[param_name])}, "
                f"but expected {type_annotation}."
            )
    if errors:
        raise ValueError(
            f"The config returned by `{sig_str}.create_config()` had the following errors:\n"
            + "\n".join(errors)
        )


def _dynamic_type_check(obj: Any, typ: type) -> bool:
    """A simple dynamic type check that handles union and container types.

    Args:
        obj: The object to check.
        typ: The type to check against.

    Returns:
        True if `obj` is of type `typ`, False otherwise.
    """
    base_typ = typing.get_origin(typ)
    if base_typ is Union:
        return any(
            _dynamic_type_check(obj, sub_typ) for sub_typ in typing.get_args(typ)
        )
    # Handle basic container types.
    elif base_typ in [
        list,
        set,
        frozenset,
        Sequence,
        Mapping,
        MutableMapping,
        Collection,
    ]:
        assert base_typ is not None
        sub_typ = typing.get_args(typ)[0]
        an_obj = next(iter(obj), None)
        return isinstance(obj, base_typ) and (
            not an_obj or _dynamic_type_check(an_obj, sub_typ)
        )
    elif base_typ in [dict, Mapping, MutableMapping]:
        assert base_typ is not None
        key_typ, val_typ = typing.get_args(typ)
        a_key, a_value = next(iter(obj.items()), (None, None))
        return isinstance(obj, base_typ) and (
            not obj
            or _dynamic_type_check(next(iter(obj.keys())), key_typ)
            and _dynamic_type_check(next(iter(obj.values())), val_typ)
        )
    elif base_typ and typing.get_args(typ):
        logging.warning(f"Unable to fully check generic type {typ}.")
        return isinstance(obj, base_typ)
    else:
        return isinstance(obj, typ)
