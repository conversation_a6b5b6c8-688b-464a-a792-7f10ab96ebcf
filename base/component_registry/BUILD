load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "component_registry",
    srcs = [
        "__init__.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":component_registry_lib",
        ":exceptions",
    ],
)

py_library(
    name = "component_registry_lib",
    srcs = [
        "component_registry.py",
    ],
    deps = [
        ":exceptions",
        "//base/collections:typesafe_dict",
        requirement("dataclasses_json"),
        requirement("immutabledict"),
        requirement("marshmallow"),
    ],
)

pytest_test(
    name = "component_registry_test",
    srcs = ["component_registry_test.py"],
    deps = [":component_registry"],
)

py_library(
    name = "exceptions",
    srcs = [
        "exceptions.py",
    ],
)
