# component_registry: A lightweight framework to configure and build components.

A common problem in writing software is that a general entry point (e.g. a server main,
a command line tool, or an evaluation script) needs to construct a myriad of components
from a configuration provided by the user. Often, these components have dependencies
between each other (e.g., they share a tokenizer or a handle to a remote service).
An anti-pattern is to have constructor functions in the entry-point that tightly couple
implementation details, enforce an interface on component constructors and make it
onerous to add components that rely on new configuration options or other components.

This package addresses this problem with a lightweight framework that:
1. Provides a mechanism describe component dependencies (`ref_field`) in configuration
   schema. The configuration schema is built on top of `dataclasses_json` which provides
   a mechanism to encode and decode configuration dictionaries from JSON.
2. Provides a mechanism to register component builders with a configuration schema
   (`ComponentRegistry.register_component`). This allows the entry point to build
   components from configuration without needing to know how they are implemented.
3. Provides a utility to validate configurations against the provided schema
   (`ComponentRegistry.check_configurations`), and to create components in the right
   dependency order (`ComponentRegistry.create_components`).
4. The recommended way of using this package is via `ComponentRegistry.autoregister`
   that automatically registers components based on type hints (see Usage below).
5. The package also provides a `serialize_component_configs` utility that can be used
   to serialize component configurations into a dictionary. This can be viewed as the
   "inverse" of the `ComponentRegistry.create_components` function above that constructs
   components from a configuration dictionary (see example below).

# Usage (autoregister)

The following example sketches out how this framework can be used, starting with
what the entry point looks like and then how the components are defined. See the
tests for more complete examples.

```python
# my_service/main.py - The entry point

# We rely on dynamic dispatch, so we need to import the components we want to use.
#   Import order doesn't matter, but we will exception if a component is duplicated.
from my_service.registry import ServiceRegistry
from my_service import simple_widget, complex_widget

# The framework relies on a dictionary mapping component names to their configurations
# represnted as dicts. This dictionary could be loaded from a JSON file, for example.
def main(configs: dict[str, dict]):
    # `create_components` creates the components in the right order.
    # Raises:
    #   ComponentValidationError if any of the below checks fail.
    #   - a configuration is missing a required field.
    #   - a referenced component is missing.
    #   - a referenced component has the wrong type.
    #   - a cyclic dependency is found.
    #  It may raise exceptions from a component builder, e.g., trying to connect to a
    #  service that doesn't exist.
    # create_components takes a list of registries to use -- this allows e.g. importing
    # a registry for a specific package (e.g. prompt formatters).
    components = create_components(configs, [ServiceRegistry])
    # Finally, components can be resolved in a type-safe way by name.
    widget = components.get_with_type("main_widget", complex_widget.ComplexWidget)

    # That's it, you can now use the widget.
    widget.spin()

# A minimal example of how a configuration might look.
main(configs={
    # Configurations have a reserved field `$component_name` that specifies the type of
    # component to build. This name is always the simple name of the component class.
    "simple1": {"$component_name": "SimpleWidget", "param": "foo"},
    # There can be multiple components of the same type, as long as they have different
    # names.
    "simple2": {"$component_name": "SimpleWidget", "param": "bar"},
    "main_widget": {
        "$component_name": "ComplexWidget",
        # Configurations can reference other components by name.
        "a_widget": "$simple1",
        # The framework supports lists and dicts of references too.
        "widgets": ["$simple1", "$simple2"],
        "widget_map": {"foo": "$simple1", "bar": "$simple2"},
    },
})

# my_service/registry.py - Where a registry is defined.
from base.component_registry import ComponentRegistry
ServiceRegistry = ComponentRegistry()

# simple_widget.py - A simple widget that doesn't have any dependencies, etc.
from dataclasses import dataclass
from base.component_registry import ComponentConfig
from my_service.registry import ServiceRegistry

# Most of the time, `autoregister` is all you need to register a simple component.
# `ComponentRegistry` will create a dataclass schema based on the argument names and
# types and use it to validate configurations.
@ServiceRegistry.autoregister
class Widget:
    def __init__(self, param: str):
        ...

# complex_widget.py - A more complex component that depends on other ones.
from base.component_registry import ComponentMap, ComponentConfig, ref_field
from my_service.registry import ServiceRegistry
from my_service.simple_widget import Widget

# Auto-registering also works for components that reference other components. In these
# situations, the configuration expects a reference to a component of the specified type.
@ServiceRegistry.autoregister
class ComplexWidget:
    def __init__(self,
        a_widget: Widget,
        maybe_widget: Optional[Widget],
        widgets: list[Widget],
        widget_map: dict[str, Widget]
    ): ...
```

That's it!

# Usage in a notebook

```python
from base.component_registry import create_component
from my_service.registry import ServiceRegistry
from my_service import simple_widget, complex_widget

widget = create_component(Widget,
    {
        "$component_name": "ComplexWidget",
        "a_widget_ref": "$simple1",
        "widget_refs": ["$simple1", "$simple2"],
        "widget_map_refs": {"foo": "$simple1", "bar": "$simple2"},
    },
    [ServiceRegistry],
    existing_components={
        "simple1": simple_widget.SimpleWidget("foo"),
        "simple2": simple_widget.SimpleWidget("bar"),
    }
)
```

# Relation to `base.registry`

*  `base.registry` is a simpler general-purpose registry that allows you to fetch an
  object by name. Unfortunately, `base.registry` has led to an anti-pattern where it
  is used to retrieve types/classes by name and to create objects using a single
  "factory" function. This approach enforces an interface on how components are created,
  and doesn't allow you to handle dependencies between components.
* In contrast, `component_registry` extends `base.registry` to provide an opiniated way
  of creating components from their configurations. It allows you to register many
  different component types in a single registry and doesn't rely on a type hierarchy.

# Important caveats and limitations

* This framework, like `dataclasses_json` that it uses, relies on _dynamic_ type
  checking. Using `from __future__ import annotations` converts any type annotations
  in that file into strings, which makes it hard/impossible to dynamically check types.
  Unfortnately, this is a well-known problem in Python typing; the safest solution is to
  avoid `from __future__ import annotations` when defining a `ComponentConfig`.


# Serializing component configurations

The framework also supports serializing component configurations into a dictionary so
that they can be created again using `create_components`.
To use this feature each class is expected to have a `create_config` method that returns
dictionary of argument values for its constructor. The framework takes care of
serializing sub-components recursively and de-duplicating object references.

Here's an example:
```python
# An simple component with simple arguments.
class Simple:
    def __init__(self, a_str: str):
        self.a_str = a_str

    def create_config(self):
        return {
            "a_str": self.a_str,
        }

# Here's a more complex component that includes a sub-component and some other
# configuration parameters.
class Complex:
    def __init__(self, sub_component: Simple, other_config: dict):
        self.sub_component = simple
        self.config = other_config

    def create_config(self, collector, name):
        return {
            "sub_component": self.simple,
            "other_config": self.config,
        }

# Here's what the serialized configuration dictionary looks like:
assert serialize_component_configs({
    "complex": Complex(Simple("bar"), {"foo": "bar"}),
}) == {
    "complex": {
        # `serialize_component_configs` saves the component name with a fully-specified
        # name, which can be used to re-create the component in the future.
        "$component_name": "my_module.Complex",
        # When `Simple("bar")`'s configuration is serialized, its configuration is saved
        # at the top-level, and a reference to it is saved here. If the same component
        # appears multiple times in the configuration, they will share a name.
        "sub_component": "$complex/sub_component",
        # Other component configurations are saved as is.
        "other_config": {
            "foo": "bar",
        },
    },
    # Sub-components are serialized recursively and added to the top-level component
    # configuration dictionary. The name of a sub-component comes from the hierarchical
    # path (here 'complex' -> 'sub_component') where it was first encountered.
    "complex/sub_component": {
        "$component_name": "Simple",
        "a_str": "bar",
    },
}
```

# Advanced: Manually registering components.

For more complex use-cases, you may want to register components manually. This is useful
if you want to do some non-trivial logic when creating components or if the
configuration doesn't align with the component's constructor arguments. Here's what
that looks like for the above examples:

```python
# simple_widget.py - A simple widget that doesn't have any dependencies, etc.

# No `@ServiceRegistry.autoregister` this time.
class Widget:

    # All configs need to be dataclasses that inherit from ComponentConfig.
    # They don't need to be nested in a class like here, though that's often convenient.
    @dataclass
    class Config(ComponentConfig):
        param: str

    # For this example, we'll assume that we also want to construct some bytes from
    # the param.
    def __init__(self, param: str, param_bytes: bytes):
        ...

# Components are registered with their configuration schema and the type of the object
# they build.
# Raises `ValueError` if a component with the same simple class name was previously
# registered.
@ServiceRegistry.register_component(Widget.Config, Widget)
def create_widget(cfg: Widget.Config, _) -> Widget:
    param_bytes = cfg.param.encode()
    return Widget(cfg.param, param_bytes)

# complex_widget.py - A more complex component that depends on other ones.
class ComplexWidget:

    @dataclass
    class Config(ComponentConfig):
        # Ref fields store metadata about the type of the reference expected.
        # The framework uses this information to resolve the dependency order.
        # SimpleWidgets are allowed here because they subclass Widget.
        a_widget: str = ref_field(Widget)
        # The framework also supports optionals, lists, dicts of references.
        # Optional references have a None value by default.
        maybe_widget: Optional[str] = ref_field(Widget, allow_unset=False)
        widget_refs: list[str] = ref_field(list[Widget])
        widget_map_refs: dict[str, str] = ref_field(dict[str, Widget])

    def __init__(self,
        a_widget: Widget,
        maybe_widget: Optional[Widget],
        widgets: list[Widget],
        widget_map: dict[str, Widget]
    ): ...

@ServiceRegistry.register_component(ComplexWidget.Config, ComplexWidget)
def create_complex_widget(cfg: ComplexWidget.Config, components: ComponentMap) -> ComplexWidget:
    # Resolver helps us resolve the Ref-typed fields in a type-safe way.
    return ComplexWidget(
        a_widget=components.get_with_type(cfg.a_widget_ref, Widget),
        widgets=[components.get_with_type(ref, Widget) for ref in cfg.widget_refs],
        widget_map={
            name: components.get_with_type(ref, Widget)
            for name, ref in cfg.widget_map_refs.items()
        },
    )
```
