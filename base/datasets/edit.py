"""Dataclasses for exported Request Insight code-instructions."""

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from dataclasses_json import dataclass_json

logger = logging.getLogger(__name__)


@dataclass_json
@dataclass(frozen=True)
class EditPosition:
    """Represents the position of a completion request relative to a cached blob."""

    blob_name: str
    """The blob name of the cached copy of the file."""

    prefix_begin: int
    """The offset in Unicode characters where the prefix region begins in `blob_name`."""

    suffix_end: int
    """The offset in Unicode characters where the suffix region ends in `blob_name`."""

    original_prefix_length: int
    """The length of the prefix in the original request (in Unicode characters)."""

    original_suffix_length: int
    """The length of the suffix in the original request (in Unicode characters)."""


@dataclass_json
@dataclass(frozen=True)
class EditRequest:
    """Represents a single edit request."""

    prefix: str
    """The prefix in the edit request.

    To make it easier to use, the prefix is expanded to begin at the start of the file.
    """

    suffix: str
    """The suffix in the edit request.

    To make it easier to use, the suffix is expanded to the end of the file.
    """

    selected_text: str
    """The selected text in the edit request."""

    instruction: str
    """The instruction in the edit request."""

    path: str
    """The path of the current file being completed."""

    # NOTE(arun): the type of this field needs to be a list because dataclasses-json
    # doesn't understand Sequence.
    blob_names: list[str]
    """The list of blob names (i.e. files) in the request.

    This list does not contain the blob name for "current file" (i.e. the file being
    completed); instead the current file contents is `prefix + suffix`.
    """

    timestamp: datetime
    """The timestamp of the edit request."""

    lang: str
    """Programming language of the current file."""

    position: Optional[EditPosition] = None
    """The position of the edit request relative to the cached blob.

    This information is useful when replaying requests against a production model
    client.
    """

    @property
    def original_prefix(self) -> str:
        """The original prefix in the edit request."""
        return (
            self.prefix[-self.position.original_prefix_length :]
            if self.position
            else self.prefix
        )

    @property
    def original_suffix(self) -> str:
        """The original suffix in the edit request."""
        return (
            self.suffix[: self.position.original_suffix_length]
            if self.position
            else self.suffix
        )


@dataclass_json
@dataclass
class EditResponse:
    """Represents a single completion response."""

    timestamp: datetime
    """The timestamp of the completion response."""

    text: str
    """The completion text generated by the system."""

    model_name: str
    """The model used to generate the edit."""

    unknown_blob_names: list[str]
    """The list of blob names that were not found at the time of the request."""

    checkpoint_not_found: bool
    """True if the checkpoint for the completion response was not found."""


@dataclass_json
@dataclass
class EditResolution:
    """Represents an edit resolution."""

    is_accepted: bool
    annotated_text: Optional[str] = None
    annotated_instruction: Optional[str] = None
    timestamp: Optional[datetime] = None


@dataclass_json
@dataclass
class EditDatum:
    """Represents a full edit event with its request, response."""

    request_id: str
    """The request ID of the completion event."""

    user_id: str
    """The user ID of the user who made the edit."""

    user_agent: str
    """The user agent of the user who made the edit."""

    request: EditRequest
    """The edit request."""

    response: EditResponse
    """The edit response."""

    resolution: Optional[EditResolution]
    """The edit resolution."""

    @property
    def status(self) -> str:
        """The resolution status of the edit: accepted, rejected, or unknown."""
        if self.resolution is None:
            return "unknown"
        if self.resolution.is_accepted:
            return "accepted"
        return "rejected"

    @property
    def annotated_text(self) -> Optional[str]:
        """The annotated text of the edit, or None if not available."""
        if self.resolution is not None:
            return self.resolution.annotated_text
        return None

    @property
    def annotated_instruction(self) -> Optional[str]:
        """The annotated instruction of the edit, or None if not available."""
        if self.resolution is not None:
            return self.resolution.annotated_instruction
        return None

    @property
    def is_annotated(self) -> bool:
        """True if the edit was annotated."""
        return self.annotated_text is not None and len(self.annotated_text) > 0
