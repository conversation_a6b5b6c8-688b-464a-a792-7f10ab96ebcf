"""Code for downloading and processing the raw Hindsight data into the intermediate data type format.

Adapted from hindsight_next_edit_dataset.py.
"""

import dataclasses
from bisect import bisect_left, bisect_right
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from random import Random
from typing import (
    Callable,
    Iterable,
    Iterator,
    Optional,
    Sequence,
)

import dataclasses_json
import google.auth.credentials
import pytz
import structlog
from google.cloud import storage
from tqdm import tqdm

from base.blob_names.python.blob_names import BlobName, FilePath
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache, GCSPathCache
from base.datasets.gcs_client import GCSRequestInsightFetcher
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.datasets.itertools import groupby
from base.datasets.next_edit import NextEditDatum
from base.datasets.next_edit_dataset_gcs import Fields<PERSON>ilter, NextEditDataFromGCS
from base.datasets.tenants import DatasetTenant
from base.datasets.user_event import (
    NextEditRequestIdIssuedEvent,
    TextEditEvent,
    UserEvent,
)
from base.datasets.user_event_lib import UserEventFilters, UserEventStream
from base.diff_utils.apply_replacements_to_files import (
    FileReplacementError,
    apply_replacements_to_files,
)
from base.diff_utils.diff_utils import File
from base.logging.secret_logging import IgnoreAllLogger
from base.ranges.range_types import CharRange, LineRange

logger = structlog.getLogger()

SessionId = str
RequestId = str

GB = 2**30


@dataclass
class GCSCaches:
    """Holds the GCloud storage caches needed to download the intermediate data."""

    tenant: DatasetTenant
    blob_cache: GCSBlobCache
    path_cache: GCSPathCache
    checkpoint_cache: GCSCheckpointCache
    gcp_creds: google.auth.credentials.Credentials

    @staticmethod
    def create(tenant: DatasetTenant) -> "GCSCaches":
        gcp_creds, _ = get_gcp_creds()
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        blob_cache = GCSBlobCache(
            blob_bucket, tenant.blob_bucket_prefix, max_size_bytes=5 * GB
        )
        blob_cache.logging_fn = lambda _: None
        path_cache = GCSPathCache(
            blob_bucket, tenant.blob_bucket_prefix, max_size_bytes=1 * GB
        )
        path_cache.logging_fn = lambda _: None
        checkpoint_cache = GCSCheckpointCache(
            storage_client.bucket(tenant.checkpoint_bucket_name),
            tenant.checkpoint_bucket_prefix,
            max_size_bytes=1 * GB,
        )
        checkpoint_cache.logging_fn = lambda _: None

        return GCSCaches(
            tenant=tenant,
            gcp_creds=gcp_creds,
            blob_cache=blob_cache,
            path_cache=path_cache,
            checkpoint_cache=checkpoint_cache,
        )


def default_text_event_filter(event: TextEditEvent) -> bool:
    # we don't need edit events for .git and .ipynb files
    return Path(event.file_path).suffix not in (".git", ".ipynb")


@dataclass(frozen=True)
class NextEditRawDataQueryArgs(dataclasses_json.DataClassJsonMixin):
    """Args for querying the hindsight next edit dataset."""

    user_event_limit: Optional[int] = None
    """If set, the maximum number of events (next edit, text edit, etc...) to return."""

    timezone: pytz.BaseTzInfo = pytz.timezone("America/Los_Angeles")
    """The timezone to which to convert the timestamps."""

    text_event_filter: Callable[[TextEditEvent], bool] = default_text_event_filter
    """Decides which text events to keep."""

    max_blobs_per_request: int = 30_000
    """The maximum number of blobs in each request. Request with more than this number
    of blobs will be dropped."""

    min_session_requests: int = 1
    """The minimum number of requests per session id.

    If a session id has fewer than this number of requests, it will be dropped for
    efficiency.
    """


@dataclass
class HindsightNextEditRawData:
    """Raw hindsight data for a single session."""

    session_id: SessionId
    text_edit_events: Sequence[TextEditEvent]
    next_edit_data: Sequence[NextEditDatum]

    def __post_init__(self):
        if not self.text_edit_events:
            raise ValueError("No text edit events found.")
        self.user_id = self.text_edit_events[0].user_id
        self.tenant = self.text_edit_events[0].tenant


def download_user_events(
    timestamp_begin: datetime,
    total_duration: timedelta,
    future_events_duration: timedelta,
    query_args: NextEditRawDataQueryArgs,
    tenant: DatasetTenant,
) -> list[UserEvent]:
    """Download the raw user events within the time window and group them by session_id."""

    def loc_time(dt: datetime) -> datetime:
        return dt.astimezone(query_args.timezone)

    def localize_event(dt: UserEvent) -> UserEvent:
        return dataclasses.replace(dt, time=loc_time(dt.time))

    # Query user events
    latest_request_time = loc_time(timestamp_begin + total_duration)
    latest_edit_time = loc_time(latest_request_time + future_events_duration)
    filters = UserEventFilters(
        timestamp_begin=timestamp_begin,
        timestamp_end=latest_edit_time,
        event_types=[
            "next_edit_request_id_issued",
            "text_edit",
        ],
    )
    stream = UserEventStream.from_query(tenant, filters, query_args.user_event_limit)
    events = [localize_event(e) for e in stream.events]
    return events


def filter_user_events(
    events: list[UserEvent],
    query_args: NextEditRawDataQueryArgs,
) -> tuple[list[TextEditEvent], list[NextEditRequestIdIssuedEvent]]:
    """Filter the user events and split them into text edit and next edit request events."""
    text_edit_events = [
        event
        for event in events
        if isinstance(event, TextEditEvent) and query_args.text_event_filter(event)
    ]
    request_id_issued_events = [
        event for event in events if isinstance(event, NextEditRequestIdIssuedEvent)
    ]
    logger.info(f"{len(text_edit_events)=}, {len(request_id_issued_events)=}")

    request_id_issued_events = filter_by_min_session_requests(
        request_id_issued_events, min_session_requests=query_args.min_session_requests
    )
    logger.info(
        f"{len(request_id_issued_events)=} after filtering by min session requests."
    )
    request_id_issued_events = filter_inactive_requests(
        request_id_issued_events,
        text_edit_events,
    )
    logger.info(f"{len(request_id_issued_events)=} after filtering inactive requests.")
    request_id_issued_events = list(pick_end_of_streak_events(request_id_issued_events))
    logger.info(f"{len(request_id_issued_events)=} after picking end of streak.")

    return text_edit_events, request_id_issued_events


def user_events_to_next_edit_data(
    text_edit_events: list[TextEditEvent],
    request_id_issued_events: list[NextEditRequestIdIssuedEvent],
    query_args: NextEditRawDataQueryArgs,
    request_fetcher: GCSRequestInsightFetcher,
    checkpoint_cache: GCSCheckpointCache,
) -> list[NextEditDatum]:
    """Convert the user events to next edit datum objects."""
    next_edit_data = NextEditDataFromGCS(
        request_fetcher=request_fetcher,
        checkpoint_cache=checkpoint_cache,
        request_ids=[x.request_id for x in request_id_issued_events],
        fields_filter=FieldsFilter(response=False),
    )

    # TODO(jeff): next_edit_data may return a subset of request_ids, and we don't know
    # the exact number beforehand, so tqdm will underreport slightly until finished.
    next_edit_data = list(
        tqdm(
            next_edit_data,
            total=len(request_id_issued_events),
            smoothing=0,
            desc="next_edit_dataset.get_next_edits()",
        )
    )
    success_rate = len(next_edit_data) / len(request_id_issued_events)
    logger.info(
        f"Found {len(next_edit_data)} next edit data (success rate={success_rate:.1%})."
    )
    for datum in next_edit_data:
        datum.request.timestamp = datum.request.timestamp.astimezone(
            query_args.timezone
        )
    next_edit_data = [
        datum
        for datum in next_edit_data
        if len(datum.request.blob_names) <= query_args.max_blobs_per_request
    ]
    logger.info(f"{len(next_edit_data)=} after filtering by request blob count.")
    next_edit_data = filter_overridden_requests(next_edit_data, text_edit_events)
    logger.info(f"{len(next_edit_data)=} after filtering overridden requests.")
    next_edit_data = filter_newly_active_requests(next_edit_data, text_edit_events)
    logger.info(f"{len(next_edit_data)=} after filtering newly active requests.")

    # sort the data by timestamp
    session_data = sorted(next_edit_data, key=lambda x: x.request.timestamp)
    return session_data


def download_raw_data(
    gcp_caches: GCSCaches,
    timestamp_begin: datetime,
    total_duration: timedelta,
    future_events_duration: timedelta,
    query_args: NextEditRawDataQueryArgs,
) -> dict[SessionId, HindsightNextEditRawData]:
    # Query next edits using GCS dataset
    user_events = download_user_events(
        timestamp_begin,
        total_duration,
        future_events_duration,
        query_args,
        gcp_caches.tenant,
    )
    # first sort by user_id, then group by session_id to help improve data adjacency
    user_events.sort(key=lambda x: x.user_id)
    user_events_by_session_id = groupby(user_events, key=lambda x: x.session_id)
    logger.info(f"Found {len(user_events_by_session_id)} different sessions ids.")

    request_fetcher = GCSRequestInsightFetcher.from_tenant(gcp_caches.tenant)
    result = dict[SessionId, HindsightNextEditRawData]()

    for session_id, user_events in user_events_by_session_id.items():
        text_edit_events, request_id_issued_events = filter_user_events(
            user_events,
            query_args,
        )
        if not text_edit_events or not request_id_issued_events:
            # We cannot perform any analysis on this session
            continue

        next_edit_data = user_events_to_next_edit_data(
            text_edit_events,
            request_id_issued_events,
            query_args,
            request_fetcher,
            gcp_caches.checkpoint_cache,
        )
        result[session_id] = HindsightNextEditRawData(
            session_id=session_id,
            text_edit_events=text_edit_events,
            next_edit_data=next_edit_data,
        )

    logger.info(f"Found {len(result)} different sessions ids.")

    return result


def filter_by_min_session_requests(
    requests: Iterable[NextEditRequestIdIssuedEvent],
    min_session_requests: int,
):
    session_to_requests = groupby(requests, key=lambda x: x.session_id)
    kept: list[NextEditRequestIdIssuedEvent] = []
    for _, session_requests in session_to_requests.items():
        if len(session_requests) >= min_session_requests:
            kept.extend(session_requests)
    return kept


def filter_inactive_requests(
    requests: Iterable[NextEditRequestIdIssuedEvent],
    user_edits: Iterable[TextEditEvent],
    inactive_gap_limit: timedelta = timedelta(minutes=6),
    time_error_tolerance: timedelta = timedelta(seconds=0.5),
) -> list[NextEditRequestIdIssuedEvent]:
    """Filter out requests that do not have any follow-up user edits in the next
    `inactive_time_limit` period.
    """
    requests = sorted(requests, key=lambda x: x.time)
    user_edits = sorted(user_edits, key=lambda x: x.time)
    session_to_user_edits = groupby(user_edits, key=lambda x: x.session_id)
    session_to_requests = groupby(requests, key=lambda x: x.session_id)
    kept_requests: list[NextEditRequestIdIssuedEvent] = []

    for session_id, session_requests in session_to_requests.items():
        session_user_edits = session_to_user_edits.get(session_id, [])
        for request in session_requests:
            inactive_check_range = (
                request.time - time_error_tolerance,
                request.time + inactive_gap_limit + time_error_tolerance,
            )
            left_bound = bisect_left(
                session_user_edits, inactive_check_range[0], key=lambda x: x.time
            )
            right_bound = bisect_right(
                session_user_edits, inactive_check_range[1], key=lambda x: x.time
            )
            # only add if there is any user edit within the time range
            if right_bound > left_bound:
                kept_requests.append(request)

    kept_requests.sort(key=lambda x: x.time)
    return kept_requests


def filter_newly_active_requests(
    requests: Iterable[NextEditDatum],
    user_edits: Iterable[TextEditEvent],
    inactive_gap_limit: timedelta = timedelta(minutes=6),
    time_error_tolerance: timedelta = timedelta(seconds=0.1),
) -> list[NextEditDatum]:
    """Filter out requests that occur after a long period of inactivity (more than
    `inactive_gap_limit` since the previous request).

    Args:
        requests: The requests to filter.
        inactive_gap_limit: The maximum allowed time gap between consecutive requests.
        time_error_tolerance: Small time window to account for timing inconsistencies.
    """
    requests = sorted(requests, key=lambda x: x.request.timestamp)
    user_edits = sorted(user_edits, key=lambda x: x.time)
    user_to_edits = groupby(user_edits, key=lambda x: x.user_id)
    user_to_requests = groupby(requests, key=lambda x: x.user_id)
    kept_requests: list[NextEditDatum] = []

    for user_id, user_requests in user_to_requests.items():
        if not user_requests:
            continue

        user_edits = user_to_edits.get(user_id, [])
        for request in user_requests:
            # check if there is any user edit within the past inactive_gap_limit time
            inactive_check_range = (
                request.request.timestamp - inactive_gap_limit - time_error_tolerance,
                request.request.timestamp + time_error_tolerance,
            )
            left_bound = bisect_left(
                user_edits, inactive_check_range[0], key=lambda x: x.time
            )
            right_bound = bisect_right(
                user_edits, inactive_check_range[1], key=lambda x: x.time
            )
            if right_bound > left_bound:
                # only add if there is any user edit within the time range
                kept_requests.append(request)

    kept_requests.sort(key=lambda x: x.request.timestamp)
    return kept_requests


def filter_overridden_requests(
    requests: Iterable[NextEditDatum],
    user_edits: Iterable[TextEditEvent],
    too_close_time_limit: timedelta = timedelta(seconds=1),
    time_error_tolerance: timedelta = timedelta(seconds=0.1),
) -> list[NextEditDatum]:
    """Filter out requests that have any follow-up user edit that is too
    close to the request time (`too_close_time_limit`).
    """
    requests = sorted(requests, key=lambda x: x.request.timestamp)
    user_edits = sorted(user_edits, key=lambda x: x.time)
    user_to_edits = groupby(user_edits, key=lambda x: x.user_id)
    user_to_requests = groupby(requests, key=lambda x: x.user_id)
    kept_requests: list[NextEditDatum] = []

    for user_id, session_requests in user_to_requests.items():
        user_edits = user_to_edits.get(user_id, [])
        for request in session_requests:
            too_close_time_range = (
                request.request.timestamp - time_error_tolerance,
                request.request.timestamp + too_close_time_limit + time_error_tolerance,
            )
            left_bound = bisect_left(
                user_edits, too_close_time_range[0], key=lambda x: x.time
            )
            right_bound = bisect_right(
                user_edits, too_close_time_range[1], key=lambda x: x.time
            )
            if right_bound == left_bound:
                # only add if there is no user edit within the time range
                kept_requests.append(request)

    kept_requests.sort(key=lambda x: x.request.timestamp)
    return kept_requests


def pick_end_of_streak_events(
    events: Iterable[NextEditRequestIdIssuedEvent],
    time_interval: timedelta = timedelta(seconds=3),
) -> Iterable[NextEditRequestIdIssuedEvent]:
    """For each picked event, there cannot be a later event within time_interval.

    Sometimes the front end sends multiple requests of different scopes in a quick
    succession. This function picks the last request in each such streak.
    """
    events = sorted(events, key=lambda x: x.time)
    session_to_last_event: dict[SessionId, NextEditRequestIdIssuedEvent] = {}
    for event in events:
        last_event = session_to_last_event.get(event.session_id)
        session_to_last_event[event.session_id] = event
        if last_event is None:
            continue
        if event.time - last_event.time >= time_interval:
            # it has been sufficiently long, the last event is end of streak
            yield last_event


def downsample_events_by_time_uniform(
    events: Iterable[NextEditRequestIdIssuedEvent],
    time_interval: timedelta,
) -> Iterable[NextEditRequestIdIssuedEvent]:
    """For each session_id, keep at most one event per time interval."""
    events = sorted(events, key=lambda x: x.time)
    session_to_last_time: dict[SessionId, datetime] = {}
    for event in events:
        last_time = session_to_last_time.get(event.session_id)
        if last_time is None or event.time - last_time >= time_interval:
            # it has been sufficiently long
            yield event
            session_to_last_time[event.session_id] = event.time


@dataclass
class NextEditIntermediateErrorDetails:
    """Detailed errors for debugging NextEditIntermediateDataLoader."""

    processed_requests_count: int = 0
    """The number of requests that have been processed so far."""

    no_events_found: list[RequestId] = field(default_factory=list)
    """Requests with no events found. Contains request IDs with this error."""

    some_edited_files_not_found: dict[RequestId, list[FilePath]] = field(
        default_factory=dict
    )
    """Requests with some edited files not found. Contains a dictionary of request IDs to the files that were not found."""

    unable_to_reconstruct_files: dict[RequestId, list[FileReplacementError]] = field(
        default_factory=dict
    )
    """Requests with some files unable to be reconstructed. Contains a dictionary of request IDs to errors."""

    duplicated_data: list[RequestId] = field(default_factory=list)
    """Requests with duplicated data. Contains request IDs with this error."""

    def summary(self) -> str:
        """Returns a summary of the error details."""
        return (
            f"processed_requests_count: {self.processed_requests_count}\n"
            f"no_events_found: {len(self.no_events_found)}\n"
            f"some_edited_files_not_found: {len(self.some_edited_files_not_found)}\n"
            f"unable_to_reconstruct_files: {len(self.unable_to_reconstruct_files)}\n"
            f"duplicated_data: {len(self.duplicated_data)}\n"
        )


@dataclass
class NextEditIntermediateDataLoader:
    """A simplified method of accessing next edit examples.
    Provides next edit requests with file contents and subsequent changes.
    """

    tenant: DatasetTenant
    """The tenant that the query belongs to."""

    raw_data: HindsightNextEditRawData
    """The raw query results from `_query`."""

    blob_cache: GCSBlobCache
    """The blob cache."""

    path_cache: GCSPathCache
    """The path cache."""

    def __post_init__(self):
        self._sorted_text_events = sorted(
            self.raw_data.text_edit_events, key=lambda x: x.time
        )

        if not self._sorted_text_events:
            raise ValueError("No text events found in the query results.")

        self._timestamp_end = self._sorted_text_events[-1].time

        self._sorted_next_edit_data = sorted(
            self.raw_data.next_edit_data, key=lambda x: x.request.timestamp
        )  # Next edits sorted by time

    def iterator(
        self,
        future_events_duration: timedelta,
        error_collector: NextEditIntermediateErrorDetails,
    ) -> Iterator[NextEditIntermediateType]:
        """Iterates through all next edit requests in `self.query` produces a stream of
        NextEditIntermediateType data.
        """
        disabled_logger = IgnoreAllLogger()
        session_id = self.raw_data.session_id

        for suggestion in self._sorted_next_edit_data:
            request = suggestion.request
            request_id = request.request_id
            error_collector.processed_requests_count += 1

            # Get all events within the timespan
            relevant_events = self._get_relevant_events(
                future_events_duration, suggestion
            )

            # Skip request if no relevant events are found
            if not relevant_events:
                error_collector.no_events_found.append(request_id)
                continue

            # Get the path of all blobs' files
            path_mapping = self._get_paths(request.blob_names)

            # Get all paths and blobs modified by the user or by recent changes
            future_edit_paths = {x.file_path for x in relevant_events}
            pending_replacements = [
                x for x in request.recent_changes if not x.present_in_blob
            ]
            replacements_paths = {x.path for x in pending_replacements}
            accessed_paths = future_edit_paths | replacements_paths
            accessed_files = self._get_available_files(
                [path_mapping[x] for x in accessed_paths if x in path_mapping]
            )
            missing_files = accessed_paths.difference({x.path for x in accessed_files})
            # we will reconstruct the current file anyway
            # TODO: remove this after Moogi's new mechanism is in place
            missing_files -= {request.path}

            if missing_files:
                # This is normal. Changes to git files, node_modules, etc. are not indexed.
                error_collector.some_edited_files_not_found[request_id] = list(
                    missing_files
                )

            future_files: dict[FilePath, File] = {x.path: x for x in accessed_files}

            # Apply recent changes to files
            file_updates = (
                apply_replacements_to_files(
                    pending_replacements,
                    accessed_files,
                    safe_logger=disabled_logger,
                    skip_error_checks=True,
                )
                # apply_replacements_to_files requires at least one replacement
                if pending_replacements
                else []
            )

            replacement_errors = list[FileReplacementError]()
            for update in file_updates:
                if isinstance(update, File):
                    future_files[update.path] = update
                # we will reconstruct the current file anyway
                elif update.file_path != request.path:
                    replacement_errors.append(update)

            if {e.file_path for e in replacement_errors} & future_edit_paths:
                # some future files were not reconstructed correctly
                error_collector.unable_to_reconstruct_files[request_id] = (
                    replacement_errors
                )
                continue

            # Always use active file to update future files
            active_file = File(
                path=request.path,
                contents=request.prefix + request.selected_text + request.suffix,
            )
            future_files[active_file.path] = active_file

            yield NextEditIntermediateType(
                request=request,
                future_events=relevant_events,
                files_for_events=future_files,
                session_id=session_id,
            )

    def _get_available_files(self, blob_names: Sequence[BlobName]) -> list[File]:
        """Returns a list of files that are available in the blob cache."""
        blob_cache_results = self.blob_cache.get(blob_names)
        return [
            File(path=str(content.path), contents=content.content)
            for content in blob_cache_results
            if content is not None
        ]

    def _get_relevant_events(
        self,
        timespan: timedelta,
        suggestion: NextEditDatum,
    ) -> list[TextEditEvent]:
        """Gets all of the current session's events within `timespan` time after the
        request was issued.

        `timespan`: the timespan after the request is issued to accept requests.
        `suggestion`: the next edit suggestion to get events for.
        """

        left_bound = bisect_left(
            self._sorted_text_events,
            suggestion.request.timestamp,
            key=lambda x: x.time,
        )

        right_bound = bisect_right(
            self._sorted_text_events,
            suggestion.request.timestamp + timespan,
            key=lambda x: x.time,
        )

        return self._sorted_text_events[left_bound:right_bound]

    def _get_paths(self, blob_names: Sequence[BlobName]) -> dict[FilePath, BlobName]:
        """Returns a dictionary mapping paths to blob names."""
        path_cache_results = self.path_cache.get(blob_names)
        return {
            str(path): blob_name
            for path, blob_name in zip(path_cache_results, blob_names)
            if path is not None
        }


@dataclass
class GroundTruthEdit:
    """A ground truth edit."""

    path: FilePath
    before_crange: CharRange
    after_crange: CharRange
    before_lrange: LineRange
    after_lrange: LineRange
    before_text: str
    after_text: str


@dataclass
class DatasetWithGroundTruths:
    """The dataset for a tenant."""

    tenant_name: str
    data: list[tuple[NextEditIntermediateType, GroundTruthEdit]]
    errors: list = field(default_factory=list)
