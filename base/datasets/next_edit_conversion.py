"""Contains conversion functions from proto to datasets classes for next edits."""

from datetime import datetime

import services.next_edit_host.next_edit_pb2 as next_edit_pb2
import services.request_insight.request_insight_pb2 as request_insight_pb2
from base.datasets.next_edit import (
    Diagnostic,
    DiffSpan,
    FileLocation,
    FileRegion,
    GranularEditEvent,
    NextEditFeedback,
    NextEditGeneration,
    NextEditRequest,
    NextEditResponse,
    NextEditResult,
    NextEditSuggestion,
    ReplacementText,
    RetrievalChunk,
    ScoredFileHunk,
    Tokenization,
    VCSChange,
    WorkingDirectoryChange,
)
from base.diff_utils.proto_wrapper import from_proto
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from base.static_analysis.proto_convertor import (
    DataClassJsonConvertorSpec,
    ProtoConvertorGroup,
    RecursiveConvertorSpec,
)

proto_convertor_group = ProtoConvertorGroup(request_insight_pb2)
from_ri_proto = proto_convertor_group.from_proto
to_ri_proto = proto_convertor_group.to_proto


proto_convertor_group.add_default_convertor(<PERSON><PERSON><PERSON><PERSON><PERSON>, next_edit_pb2.CharRange)
proto_convertor_group.add_convertor_by_spec(DataClassJsonConvertorSpec(RetrievalChunk))

proto_convertor_group.add_convertor_by_spec(
    DataClassJsonConvertorSpec(
        WorkingDirectoryChange, next_edit_pb2.WorkingDirectoryChange
    )
)

proto_convertor_group.add_convertor_by_spec(
    DataClassJsonConvertorSpec(VCSChange, next_edit_pb2.VCSChange)
)


proto_convertor_group.add_convertor_by_spec(DataClassJsonConvertorSpec(Tokenization))


# NOTE: proto stores enums as int, so we need to manually convert them
proto_convertor_group.add_convertor_by_spec(
    RecursiveConvertorSpec(
        NextEditGeneration,
        proto_extra_fields={"post_process_result"},
        get_proto_extra_fields=lambda obj: {
            "post_process_result": obj.post_process_result.value
        },
        py_dropped_fields={"dataclass_json_config", "post_process_result"},
        get_python_dropped_fields=lambda d: {
            "post_process_result": NextEditGeneration.PostProcessResult(
                d["post_process_result"]
            )
        },
        proto_class=request_insight_pb2.RINextEditGeneration,
    )
)

proto_convertor_group.add_convertor_by_spec(
    DataClassJsonConvertorSpec(DiffSpan, next_edit_pb2.DiffSpan)
)


proto_convertor_group.add_convertor_by_spec(
    DataClassJsonConvertorSpec(ScoredFileHunk, next_edit_pb2.ScoredFileHunk)
)


proto_convertor_group.add_convertor_by_spec(
    DataClassJsonConvertorSpec(NextEditResult, next_edit_pb2.NextEditResponse)
)


proto_convertor_group.add_convertor_by_spec(
    DataClassJsonConvertorSpec(
        NextEditSuggestion, request_insight_pb2.RINextEditSuggestion
    )
)


def from_next_edit_request_proto(
    proto: request_insight_pb2.RINextEditRequest,
    backend_time: datetime,
    request_id: str,
) -> NextEditRequest:
    recent_changes = [
        ReplacementText(
            blob_name=change.blob_name,
            path=change.path,
            crange=CharRange(
                change.char_start,
                change.char_end,
            ),
            replacement_text=change.replacement_text,
            present_in_blob=change.present_in_blob,
        )
        for change in proto.request.recent_changes
    ]
    diagnostics = [
        Diagnostic(
            location=FileLocation(
                path=diagnostic.location.path,
                line_start=diagnostic.location.line_start,
                line_end=diagnostic.location.line_end,
            ),
            message=diagnostic.message,
            severity=Diagnostic.DiagnosticSeverity(diagnostic.severity),
        )
        for diagnostic in proto.request.diagnostics
    ]
    vcs_change = from_ri_proto(proto.request.vcs_change, VCSChange)
    edit_events = [
        from_proto(event, GranularEditEvent) for event in proto.request.edit_events
    ]
    if proto.request.HasField("client_created_at"):
        timestamp = proto.request.client_created_at.ToDatetime()
    else:
        # fall back to use backend time as a proxy
        timestamp = backend_time

    return NextEditRequest(
        model_name=proto.request.model_name,
        sequence_id=proto.request.sequence_id,
        lang=proto.request.lang,
        instruction=proto.request.instruction,
        recent_changes=recent_changes,
        vcs_change=vcs_change,
        path=proto.request.path,
        blob_name=proto.request.blob_name,
        selection_begin_char=proto.request.selection_begin_char,
        selection_end_char=proto.request.selection_end_char,
        prefix=proto.request.prefix,
        selected_text=proto.request.selected_text,
        suffix=proto.request.suffix,
        diagnostics=diagnostics,
        mode=NextEditRequest.NextEditMode(proto.request.mode),
        scope=NextEditRequest.NextEditScope(proto.request.scope),
        change_probability_override=proto.request.change_probability_override,
        edit_events=edit_events,
        blocked_locations=[
            FileRegion(
                path=changed.path,
                char_start=changed.char_start,
                char_end=changed.char_end,
            )
            for changed in proto.request.blocked_locations
        ],
        blob_names=[],
        restrict_to_file=(proto.request.scope == NextEditRequest.NextEditScope.FILE),
        timestamp=timestamp,
        request_id=request_id,
        api_version=proto.request.api_version,
    )


def from_next_edit_response_protos(
    response_protos: list[request_insight_pb2.RINextEditResponse],
    timestamps: list[datetime],
) -> NextEditResponse:
    response_message = request_insight_pb2.RINextEditResponse()
    for response_proto in response_protos:
        response_message.MergeFrom(response_proto)

    return NextEditResponse(
        retrieved_locations=from_ri_proto(response_message.retrieved_locations),
        generations=from_ri_proto(response_message.generation),
        suggestions=from_ri_proto(response_message.suggestions),
        timestamp=max(timestamps),
    )


def from_next_edit_feedback_proto(
    feedback_proto: request_insight_pb2.NextEditFeedback,
    timestamp: datetime,
) -> NextEditFeedback:
    return NextEditFeedback(
        rating=NextEditFeedback.Rating(feedback_proto.rating),
        note=feedback_proto.note,
        timestamp=timestamp,
    )
