"""Utilities for sharding datasets."""

import uuid
from collections.abc import Iterator
from typing import ClassVar

from dataclasses import dataclass

StrRange = tuple[str, str]


def ceildiv(a: int, b: int) -> int:
    """Returns the ceiling of a / b."""
    return (a + b - 1) // b


@dataclass
class UuidSharder:
    """A sharder that shards by UUID.

    The shards are contiguous and evenly distributed.
    """

    num_shards: int

    NUM_UUIDS: ClassVar[int] = 2**128

    def shard_range(self, shard_idx: int) -> StrRange:
        """Returns the range of UUIDs for the given shard.

        The range is inclusive on the start and exclusive on the end.
        Raises if the shard index is invalid.
        """
        if shard_idx < 0 or shard_idx >= self.num_shards:
            raise ValueError(f"Invalid shard index: {shard_idx}")

        # If S = num shards, and N = num uuids, then for a given shard s,
        # a hash x is in that shard if s == (x * S) // N.
        # This happens iff s * (N / S) <= x < (s + 1) * (N / S).
        # So each left boundary is at ceil(s * (N / S))
        start = ceildiv(shard_idx * self.NUM_UUIDS, self.num_shards)
        end = ceildiv((shard_idx + 1) * self.NUM_UUIDS, self.num_shards)

        start_str = str(uuid.UUID(int=start))
        if end == self.NUM_UUIDS:
            # Special case for the last shard, because there is no uuid at NUM_UUIDS.
            # Longer strings always compare lexicographically later.
            end_str = str(uuid.UUID(int=end - 1)) + "0"
        else:
            end_str = str(uuid.UUID(int=end))
        return (start_str, end_str)

    def shard_ranges(self) -> Iterator[StrRange]:
        """Returns the ranges of UUIDs for all shards, in order."""
        for i in range(self.num_shards):
            yield self.shard_range(i)

    def shard(self, uuid_str: str) -> int:
        """Returns the shard for the given UUID.

        Raises if the UUID does not parse correctly.

        May not roundtrip with shard_range if the uuid_str is not the same
        format as would be returned by str(uuid.UUID(uuid_str)), i.e.
        00000000-0000-0000-0000-000000000000.
        """
        uuid_int = uuid.UUID(uuid_str).int
        return (uuid_int * self.num_shards) // self.NUM_UUIDS

    @classmethod
    def total_range(cls) -> StrRange:
        """Returns the total range of UUIDs."""
        return UuidSharder(1).shard_range(0)
