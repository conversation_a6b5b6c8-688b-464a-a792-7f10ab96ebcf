"""Process data in a multi-threaded pipeline.

Each stage of the pipeline runs in a separate thread, and passes its results to the
next stage in the pipeline. This abstraction is useful for hiding latency between stages
of the pipeline, especially if they are network-bound.
"""

from __future__ import annotations

from concurrent.futures import ThreadPoolExecutor
import logging
import queue
from collections.abc import Iterable, Sequence
from threading import Event
from typing import TypeVar, Callable, Generic

logger = logging.getLogger(__name__)

# Type variables representing the types of the input and output of each stage.
U, V = TypeVar("U"), TypeVar("V")


class Pipeline(Generic[U]):
    """Process data in a multi-threaded pipeline."""

    def __init__(
        self,
        source: Iterable,
        source_name: str = "source",
        stages: Sequence[Callable] = (),
        stage_names: Sequence[str] = (),
    ):
        """Create a new pipeline that generates an iterable of `U`s.

        Prefer using `Pipeline.from_source` and `Pipeline.and_then` to create your
        pipeline in a type-safe way.
        """
        self._source = source
        self._source_name = source_name
        self._stages = stages
        self._stage_names = stage_names

    @classmethod
    def from_source(cls, source: Iterable[U], name: str = "source") -> "Pipeline[U]":
        """Create a new pipeline that generates an iterable of `U`s.

        Args:
            source: The source of data.
            name: An optional tag used to identify the source in logs.

        Returns:
            A new pipeline that generates an iterable of `U`s.
        """
        return Pipeline[U](source, name)

    def and_then(self, stage: Callable[[U], V], name: str = "stage") -> "Pipeline[V]":
        """Add a stage to the pipeline.

        Args:
            stage: The stage to add.
            name: An optional tag used to identify the stage in logs.

        Returns:
            A new pipeline that includes the new stage.
        """
        return Pipeline[V](
            self._source,
            self._source_name,
            (*self._stages, stage),
            (*self._stage_names, name),
        )

    def run(self, max_queue_size: int = 128) -> Iterable[U]:
        """Run the pipeline.

        Args:
            max_queue_size: The maximum size of each queue between stages.

        Returns:
            An iterable of the output of the pipeline.
        """
        if not self._stages:
            # Simple edge-case when no stages have been added.
            yield from self._source
            return

        # The source and each stage should run on a separate threads.
        with ThreadPoolExecutor(max_workers=len(self._stages) + 1) as executor:
            queues = [queue.Queue(max_queue_size) for _ in range(len(self._stages) + 1)]
            stop_events = [Event() for _ in range(len(self._stages) + 1)]
            futures = [
                executor.submit(
                    _wrap_source,
                    self._source,
                    queues[0],
                    stop_events[0],
                    name=self._source_name,
                ),
            ] + [
                executor.submit(
                    _wrap_stage,
                    stage,
                    queues[i],
                    queues[i + 1],
                    stop_events[i],
                    stop_events[i + 1],
                    name=self._stage_names[i],
                )
                for i, stage in enumerate(self._stages)
            ]

            while (datum := queues[-1].get()) is not None:
                yield datum

            for future in futures:
                future.result()


def _wrap_source(
    source: Iterable[U],
    out_queue: queue.Queue[U | None],
    stop_event: Event,
    name: str = "source",
):
    """Wrap the source in a function that can be run in a thread.

    Args:
        source: The source of data.
        out_queue: The queue to put the data in. A None will be inserted in the queue
            when this producer is done, either because we drained `source`, or because
            we hit an exception.
        stop_event: An event that is set when the consumer is done for any reason. Once
            set, we will stop early.
        name: Tag used to identify the source in logs.
    """
    try:
        for i, batch in enumerate(source):
            # If the consumer is done, we should stop processing.
            if stop_event.is_set():
                break
            logger.debug("%s: Enqueuing %d", name, i)
            out_queue.put(batch)
    except Exception:
        logger.exception("Exception in %s", name)
        raise
    finally:
        out_queue.put(None)


def _wrap_stage(
    stage: Callable[[U], V],
    in_queue: queue.Queue[U | None],
    out_queue: queue.Queue[V | None],
    stop_in: Event,
    stop_out: Event,
    name: str = "stage",
):
    """Wrap the stage in a function that can be run in a thread.

    Args:
        stage: The stage to wrap.
        in_queue: The queue to get the data from. A None will be inserted in the queue
            when the upstream producer is done.
        out_queue: The queue to put the data in. A None will be inserted in the queue
            when this producer is done.
        stop_in: An event that should be set to let the upstream producer know we are
            done.
        stop_out: An event that is set when a downstream consumer is done for any
            reason.
        name: Tag used to identify the stage in logs.
    """
    try:
        i = 0
        while (u := in_queue.get()) is not None:
            if stop_out.is_set():
                stop_in.set()
                logger.info(
                    "Downstream from %s stopped processing. Draining upstream producer.",
                    name,
                )
                while in_queue.get() is not None:
                    continue
                break

            i += 1
            logger.debug("%s: Processing %d", name, i)
            out_queue.put(stage(u))
    except Exception:
        # The consumer is done, so notify the producer and drain the queue.
        logger.exception("Exception in %s. Draining upstream producer.", name)
        stop_in.set()
        while in_queue.get() is not None:
            continue
        raise
    finally:
        out_queue.put(None)
