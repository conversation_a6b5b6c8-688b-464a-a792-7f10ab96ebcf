"""Library to load cached blobs from GCS."""

from __future__ import annotations

import json
from concurrent import futures
from pathlib import Path
from typing import Callable, Generic, Iterable, NamedTuple, Sequence, TypeVar
from dataclasses import dataclass

import structlog
from google.cloud import storage  # type: ignore
from requests.adapters import HTTPAdapter

from base.blob_names import blob_names_pb2
from base.caching.cache import Cache
from base.caching.lru_cache import LRUCache, measure_bytes

log = structlog.get_logger()


class PathAndContent(NamedTuple):
    """A named tuple for a blob's path and contents."""

    path: Path
    content: str


BlobCache = Cache[str, PathAndContent, ...]
PathCache = Cache[str, Path, ...]


@dataclass
class CheckpointContent:
    """A named tuple for a checkpoint's contents."""

    blob_names: list[str]


CheckpointCache = Cache[str, CheckpointContent, ...]


def _extract_only_path(key: str, blob: storage.Blob | None) -> Path | None:
    """Extract the path from a blob."""
    if blob is None:
        return None
    elif blob.metadata is None:
        # The blob's metadata could be missing if the blob was incorrectly uploaded.
        log.warning("Found blob %s in GCS but it is missing metadata.", key)
        return None
    return Path(blob.metadata.get("path"))


def _extract_path_and_content(
    key: str, blob: storage.Blob | None
) -> PathAndContent | None:
    """Extract the path and content from a blob."""
    if blob is None:
        return None
    elif blob.metadata is None:
        # The blob's metadata could be missing if the blob was incorrectly uploaded.
        log.warning("Found blob %s in GCS but it is missing metadata.", key)
        return None
    return PathAndContent(
        path=Path(blob.metadata.get("path")), content=blob.download_as_text()
    )


def _extract_checkpoint_content(
    key: str, blob: storage.Blob | None
) -> CheckpointContent | None:
    """Extract the checkpoint content from a blob."""
    if blob is None:
        return None
    blob_text = blob.download_as_text()
    # blob_text will be a json list of hex strings.
    # See CheckpointExporter in blob_exporter.py
    content = json.loads(blob_text)
    return CheckpointContent(content)


T = TypeVar("T")


class GCSFetcher(Generic[T]):
    """A class to fetch storage.Blob objects from GCS."""

    def __init__(
        self,
        bucket: storage.Bucket,
        bucket_prefix: str,
        transform_fn: Callable[[str, storage.Blob | None], T | None],
        num_threads: int = 10,
    ):
        self._bucket = bucket
        self._bucket_prefix = bucket_prefix
        self._transform_fn = transform_fn
        self._executor = futures.ThreadPoolExecutor(num_threads)

        # NOTE(arun): By default, the GCS bucket client uses a connection pool of 10.
        # This snippet of code increases the pool size to the number of threads we want
        # to use.
        adapter = HTTPAdapter(pool_connections=num_threads, pool_maxsize=num_threads)
        self._bucket.client._http.mount("https://", adapter)
        self._bucket.client._http._auth_request.session.mount("https://", adapter)

    def _get(self, key: str) -> T | None:
        blob = self._bucket.get_blob(f"{self._bucket_prefix}/{key}")
        return self._transform_fn(key, blob)

    def get_missing(self, keys: Iterable[str]) -> Iterable[T | None]:
        return self._executor.map(self._get, keys)


class GCSBlobCache(LRUCache[str, PathAndContent, ...]):
    """Cache for file blobs in GCS."""

    def __init__(
        self,
        bucket: storage.Bucket,
        bucket_prefix: str,
        max_size_bytes: int,
        max_elem_size_bytes: int | None = None,
        num_threads: int = 10,
        cache_missing_keys: bool = True,
    ):
        """Create a new GCS cache for file blobs.

        Args:
            bucket: the GCS bucket to use.
            bucket_prefix: a path prefix for blobs in the bucket.
            max_size_bytes: the maximum size of the cache in bytes. this is approximate.
            max_elem_size_bytes: the maximum size of an element we'll store in the
                cache. If an element is larger than this, we won't store it because
                it would evict too many things from the cache.
                If None, use `max_size_bytes` as a default.
            num_threads: the number of threads to use to fetch blobs from GCS.
            cache_missing_keys: if true, we will not reattempt fetching blobs missing
                from GCS until the key is evicted from the cache.
        """
        self._fetcher = GCSFetcher(
            bucket,
            bucket_prefix,
            _extract_path_and_content,
            num_threads=num_threads,
        )
        super().__init__(
            self._fetcher.get_missing,
            max_size_bytes,
            max_elem_size_bytes,
            cache_missing_keys=cache_missing_keys,
            size_fn=measure_bytes,
        )


class GCSCheckpointCache(LRUCache[str, CheckpointContent, ...]):
    """Cache for checkpoint blobs in GCS."""

    def __init__(
        self,
        bucket: storage.Bucket,
        bucket_prefix: str,
        max_size_bytes: int,
        max_elem_size_bytes: int | None = None,
        num_threads: int = 10,
        cache_missing_keys: bool = True,
    ):
        """Create a new GCS cache for checkpoint blobs

        Args:
            bucket: the GCS bucket to use.
            bucket_prefix: a path prefix for blobs in the bucket.
            max_size_bytes: the maximum size of the cache in bytes. this is approximate.
            max_elem_size_bytes: the maximum size of an element we'll store in the
                cache. If an element is larger than this, we won't store it because
                it would evict too many things from the cache.
                If None, use `max_size_bytes` as a default.
            num_threads: the number of threads to use to fetch blobs from GCS.
            cache_missing_keys: if true, we will not reattempt fetching blobs missing
                from GCS until the key is evicted from the cache.
        """
        self._fetcher = GCSFetcher(
            bucket,
            bucket_prefix,
            _extract_checkpoint_content,
            num_threads=num_threads,
        )
        super().__init__(
            self._fetcher.get_missing,
            max_size_bytes,
            max_elem_size_bytes,
            cache_missing_keys=cache_missing_keys,
            size_fn=measure_bytes,
        )


def resolve_blob_names(
    checkpoint: CheckpointContent | None,
    added: Iterable[bytes],
    deleted: Iterable[bytes],
):
    """Resolve the blobs delta into a list of blob names."""
    added_blob_names = set(name.hex() for name in added)
    deleted_blob_names = set(name.hex() for name in deleted)
    checkpoint_blob_names = set(checkpoint.blob_names if checkpoint is not None else [])
    blob_names = sorted(
        list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
    )
    return blob_names


def resolve_checkpoints(
    checkpoint_cache: CheckpointCache,
    blobs_list: Sequence[blob_names_pb2.Blobs],
) -> list[list[str]]:
    """Resolve the checkpoints into blob names for each request.

    This method is batched to be more efficient with getting blobs from GCS.
    """
    checkpoints = checkpoint_cache.get(
        [blobs.baseline_checkpoint_id for blobs in blobs_list]
    )

    return [
        resolve_blob_names(checkpoint, blobs.added, blobs.deleted)
        for checkpoint, blobs in zip(checkpoints, blobs_list)
    ]


class GCSPathCache(LRUCache[str, Path, ...]):
    """Cache for the path of file blobs in GCS, to enable lazily loading content."""

    def __init__(
        self,
        bucket: storage.Bucket,
        bucket_prefix: str,
        max_size_bytes: int,
        max_elem_size_bytes: int | None = None,
        num_threads: int = 10,
        cache_missing_keys: bool = True,
    ):
        """Create a new GCS cache to load only the path of file blobs.

        Args:
            bucket: the GCS bucket to use.
            bucket_prefix: a path prefix for blobs in the bucket.
            max_size_bytes: the maximum size of the cache in bytes. this is approximate.
            max_elem_size_bytes: the maximum size of an element we'll store in the
                cache. If an element is larger than this, we won't store it because
                it would evict too many things from the cache.
                If None, use `max_size_bytes` as a default.
            num_threads: the number of threads to use to fetch blobs from GCS.
            cache_missing_keys: if true, we will not reattempt fetching blobs missing
                from GCS until the key is evicted from the cache.
        """
        self._fetcher = GCSFetcher(
            bucket,
            bucket_prefix,
            _extract_only_path,
            num_threads=num_threads,
        )
        super().__init__(
            self._fetcher.get_missing,
            max_size_bytes,
            max_elem_size_bytes,
            cache_missing_keys=cache_missing_keys,
            size_fn=measure_bytes,
        )
