"""
Hindsight Next Edit dataset. This is currently a placeholder; once a heuristic is ready and tested, it will be inserted.
"""

import dataclasses
import itertools
import logging

from bisect import bisect_left, bisect_right
from collections import Counter
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import (
    Any,
    Iterable,
    Iterator,
    Mapping,
    Optional,
    Sequence,
    TextIO,
)
from tqdm import tqdm

import dataclasses_json
import marshmallow
from google.cloud import bigquery, storage  # type: ignore

from base.blob_names.python.blob_names import BlobName
from base.datasets.next_edit_dataset_gcs import NextEditDataset
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import (
    BlobCache,
    PathCache,
    GCSBlobCache,
    PathAndContent,
)
from base.datasets.hindsight_next_edit import (
    HindsightNextEditDatum,
    NextEditIntermediateType,
)
from base.datasets.next_edit import (
    DiffSpan,
    NextEditGroundTruth,
    NextEditDatum,
    get_current_version_of_files,
)

from base.datasets.tenants import DatasetTenant
from base.datasets.user_event import (
    NextEditRequestIdIssuedEvent,
    TextEditEvent,
)
from base.datasets.user_event_lib import (
    UserEventFilters,
    UserEventStream,
    UserEventUnion,
)

from base.diff_utils.apply_replacements_to_files import FileReplacementError
from base.diff_utils.diff_utils import File

from base.ranges.range_types import CharRange

logger = logging.getLogger(__name__)


GB = 2**30


class NoNextEditEventError(Exception):
    """None of the next edit event was not found."""


@dataclass(frozen=True)
class HindsightNextEditQueryArgs(dataclasses_json.DataClassJsonMixin):
    """Args for querying the hindsight next edit dataset."""

    user_event_limit: Optional[int] = None
    """If set, the maximum number of events (next edit, text edit, etc...) to return."""

    sample_limit: Optional[int] = None
    """If set, the maximum number of next edit requests (consisting of one next edit event and its associated text edit events) to sample."""


@dataclass(frozen=True)
class HindsightNextEditProcessArgs(dataclasses_json.DataClassJsonMixin):
    """Args for processing the hindsight next edit dataset."""

    # NOTE: may not have round-trip serialize/deserialize for very large timedeltas
    # (>270 years), but this is fine since we are just dumping this for logging.
    time_limit: timedelta = dataclasses.field(
        default=timedelta(hours=1),
        metadata=dataclasses_json.config(
            encoder=lambda td: td.total_seconds(),
            decoder=lambda sec: timedelta(seconds=sec),
            mm_field=marshmallow.fields.TimeDelta(serialization_type=float),
        ),
    )
    """The amount of time we watch for text edits to a range."""

    max_interruption_chars: int = 0
    """The max total number of characters across all interruptions to text edits to a
    range. Results in an "error" if exceeded. These samples will be filtered out."""

    allow_overlaps: bool = False
    """If False, text edits that touch a range, but are not fully contained by it,
    will result in an "error". These samples will be filtered out."""

    allow_empty_ground_truth: bool = False
    """If False, treat empty ground truth as an "error".
    These samples will be filtered out."""

    min_blobs: int = 0
    """The minimum number of blobs required for a next edit. If a next edit has fewer
    blobs, it will be filtered out."""

    context_size: Optional[int] = None
    """The number of characters to include in the context range on either side of the
    next edit. For example, if the cursor is at (2, 2) and context_size = 2, then we
    will also track the range (0, 4) to use for change trimming. If None, no context
    is included *and* change trimming is turned off."""


@dataclass
class HindsightNextEditDataset:
    """Dataset for next edits logged via RequestInsight with hindsight ground truths.
    Intended for training and evaluation."""

    data: Sequence[HindsightNextEditDatum]
    """The data."""

    blobs: BlobCache
    """The blob cache."""

    def __iter__(
        self,
    ) -> Iterator[tuple[HindsightNextEditDatum, Mapping[BlobName, PathAndContent]]]:
        """Iterate over the data and blobs."""
        for datum in self.data:
            blob_names = datum.next_edit_datum.request.blob_names
            blobs = self.blobs.get(blob_names)
            blob_map = {
                blob_name: blob
                for blob_name, blob in zip(blob_names, blobs)
                if blob is not None
            }
            yield datum, blob_map

    def dump_data(self, f: TextIO):
        """Dump data to a file."""
        for datum in self.data:
            f.write(HindsightNextEditDatum.schema().dumps(datum))
            f.write("\n")

    @staticmethod
    def load_data(
        f: TextIO, limit: Optional[int] = None
    ) -> list[HindsightNextEditDatum]:
        """Load data from a file."""
        f_limit = itertools.islice(f, limit)
        data: list[HindsightNextEditDatum] = []
        for line in f_limit:
            cur_d = HindsightNextEditDatum.schema().loads(line, many=False)
            assert isinstance(cur_d, HindsightNextEditDatum)
            data.append(cur_d)
        return data

    @staticmethod
    def create_data_from_bigquery(
        tenant: DatasetTenant,
        timestamp_begin: datetime,
        timestamp_end: datetime,
        query_args: HindsightNextEditQueryArgs = HindsightNextEditQueryArgs(),
        process_args: HindsightNextEditProcessArgs = HindsightNextEditProcessArgs(),
    ) -> list[HindsightNextEditDatum]:
        """Create data from BigQuery. See query_and_process."""
        _, results = query_and_process(
            tenant,
            timestamp_begin,
            timestamp_end,
            query_args,
            process_args,
        )
        return results.data

    @staticmethod
    def create_blobs_from_gcs(
        tenant: DatasetTenant,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        service_account_file: Optional[Path] = None,
    ) -> BlobCache:
        """Create a cache for the blobs via google cloud storage."""
        gcp_creds, _ = get_gcp_creds(service_account_file)
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        blob_cache = GCSBlobCache(
            blob_bucket,
            tenant.blob_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        return blob_cache


@dataclass
class HindsightNextEditQueryResults:
    """Raw data for hindsight next edit dataset."""

    events: list[UserEventUnion]
    """All user events returned by the user_events query."""
    next_edit_events: list[NextEditRequestIdIssuedEvent]
    """A random sample of next edit events from `events`. Ordered by request_id."""
    next_edits: list[NextEditDatum]
    """Next edit datums for the sampled `next_edit_events` that were able to be
    returned by the request_events query."""


@dataclass(frozen=True)
class HindsightNextEditError:
    """An error when processing the hindsight next edit dataset."""

    event: NextEditRequestIdIssuedEvent
    """The next edit event."""
    datum: Optional[NextEditDatum]
    """The next edit datum, if it exists."""
    error_type: str
    """The error type."""
    error_detail: str
    """The error detail."""


@dataclass
class HindsightNextEditProcessResults:
    """Processed data for hindsight next edit dataset."""

    data: list[HindsightNextEditDatum]
    """Sampled next edit events that we were able to determine ground truths for."""
    errors: list[HindsightNextEditError]
    """Errors for sampled next edit events that were not successfully processed."""


def query_and_process(
    tenant: DatasetTenant,
    timestamp_begin: datetime,
    timestamp_end: datetime,
    query_args: HindsightNextEditQueryArgs = HindsightNextEditQueryArgs(),
    process_args: HindsightNextEditProcessArgs = HindsightNextEditProcessArgs(),
) -> tuple[HindsightNextEditQueryResults, HindsightNextEditProcessResults]:
    """Query bigquery and process the data to determine ground truth.

    We first query user_events for next edit or text_edit events in the time range,
    and then sample next edits with the smallest request_ids. We then query
    NextEditDataset for the full next edit data for those request_ids.

    We then process the data to determine ground truth using a heuristic.
    Any request that was sampled, but we could not determine ground truth for, is returned
    as an error with a categorical type and detail.
    """
    query_results = _query(
        tenant,
        timestamp_begin,
        timestamp_end,
        query_args,
    )
    results = _process(
        query_results,
        timestamp_end,
        process_args,
    )
    return query_results, results


def _dedupe_next_edits(events: list[UserEventUnion]) -> list[UserEventUnion]:
    """Deduplicates next edit events by request ids, keeping only the first event.

    There are two cases of duplicates:
    1. The duplicate events are exactly equal in all other fields.
    2. The duplicate events have the same request id, but different data.
    Both of these indicate something is weird or wrong with data collection.
    """
    deduped_events = []
    next_edit_map: dict[str, NextEditRequestIdIssuedEvent] = {}
    request_counter = Counter()

    for event in events:
        if not isinstance(event, NextEditRequestIdIssuedEvent):
            deduped_events.append(event)
            continue

        request_counter[event.request_id] += 1
        if event.request_id in next_edit_map:
            next_edit_event = next_edit_map[event.request_id]
            if event != next_edit_event:
                logger.warning(
                    "Duplicate next edit event has different data: %s != %s",
                    event,
                    next_edit_event,
                )
            continue

        next_edit_map[event.request_id] = event
        deduped_events.append(event)

    # Print a summary
    duplicate_counter = Counter({k: v for k, v in request_counter.items() if v > 1})
    if len(duplicate_counter) > 0:
        logger.warning(
            "Found %s duplicate next edits. Total count: %s. "
            "Top 10 duplicate request_ids: %s",
            len(duplicate_counter),
            duplicate_counter.total(),
            duplicate_counter.most_common(10),
        )

    return deduped_events


def _sample(
    events: list[UserEventUnion],
    sample_limit: Optional[int],
) -> list[NextEditRequestIdIssuedEvent]:
    """Sample the next edit events from all user events by smallest request_id."""
    next_edit_events = [
        event for event in events if isinstance(event, NextEditRequestIdIssuedEvent)
    ]
    logger.info("Found %s next edit events.", len(next_edit_events))

    # Sort and sample the smallest request ids.
    next_edit_events = sorted(next_edit_events, key=lambda x: x.request_id)
    if sample_limit is not None:
        next_edit_events = next_edit_events[:sample_limit]
    return next_edit_events


def _query(
    tenant: DatasetTenant,
    timestamp_begin: datetime,
    timestamp_end: datetime,
    query_args: HindsightNextEditQueryArgs,
) -> HindsightNextEditQueryResults:
    """Query for the raw user event and next edit data."""

    # Query user events
    filters = UserEventFilters(
        timestamp_begin=timestamp_begin,
        timestamp_end=timestamp_end,
        event_types=[
            "next_edit_request_id_issued",
            "text_edit",
        ],
    )
    stream = UserEventStream.from_query(tenant, filters, query_args.user_event_limit)
    events = list(stream.events)
    logger.info("Found %s user events in the time range.", len(events))

    events = _dedupe_next_edits(events)
    logger.info("%s user events after deduping.", len(events))

    # Sample next edits by smallest request id
    next_edit_events = _sample(events, query_args.sample_limit)
    request_ids = [next_edit.request_id for next_edit in next_edit_events]
    if not request_ids:
        raise NoNextEditEventError(
            "No next edit events found. Fix your query, or we are missing data."
        )

    # Query next edits using GCS version
    logger.info("Querying for %s next edit events.", len(request_ids))
    next_edit_data = NextEditDataset.create_data_from_gcs(
        tenant=tenant, request_ids=request_ids, logging_fn=lambda _: None
    )

    logger.info("Dataset loaded from GCS.")

    # The progress bar only updates when a batch is completed, since rows are
    # processed in batches.
    # TODO(jeff): next_edit_data may return a subset of request_ids, and we don't know
    # the exact number beforehand, so tqdm will underreport slightly until finished.
    next_edits = sorted(
        tqdm(next_edit_data, total=len(request_ids)),
        key=lambda x: x.request_id,
    )
    logger.info("Found %s next edits.", len(next_edits))

    return HindsightNextEditQueryResults(events, next_edit_events, next_edits)


def _validate_next_edit(
    next_edit: NextEditDatum,
    next_edit_user_event: NextEditRequestIdIssuedEvent,
) -> Optional[HindsightNextEditError]:
    """Validates that the next edit datum from request_events matches the next edit
    event from user_events. Returns None if they match.
    """
    if next_edit.session_id != next_edit_user_event.session_id:
        return HindsightNextEditError(
            next_edit_user_event,
            next_edit,
            "session_id_mismatch",
            f"{next_edit.session_id} != {next_edit_user_event.session_id}",
        )

    if next_edit_user_event.file_path == "":
        return HindsightNextEditError(
            next_edit_user_event,
            next_edit,
            "empty_file_path",
            f"{next_edit_user_event.file_path}",
        )

    if len(next_edit.request.recent_changes) == 0:
        return HindsightNextEditError(
            next_edit_user_event,
            next_edit,
            "no_recent_changes",
            f"len(next_edit.request.recent_changes) = {len(next_edit.request.recent_changes)}",
        )

    if next_edit.request.recent_changes[-1].path != next_edit_user_event.file_path:
        return HindsightNextEditError(
            next_edit_user_event,
            next_edit,
            "file_path_mismatch",
            f"{next_edit.request.recent_changes[-1].path} != {next_edit_user_event.file_path}",
        )

    # More checks may be added in the future based on testing.

    return None


def _filter_next_edit(
    next_edit: NextEditDatum,
    next_edit_user_event: NextEditRequestIdIssuedEvent,
    process_args: HindsightNextEditProcessArgs,
) -> Optional[HindsightNextEditError]:
    """Filter out next edits that we do not want to process."""
    if len(next_edit.request.blob_names) < process_args.min_blobs:
        return HindsightNextEditError(
            next_edit_user_event,
            next_edit,
            "too_few_blobs",
            f"{len(next_edit.request.blob_names)}",
        )

    return None


def _process(
    data: HindsightNextEditQueryResults,
    timestamp_end: datetime,
    process_args: HindsightNextEditProcessArgs,
) -> HindsightNextEditProcessResults:
    """Process the raw data to determine ground truth.

    data: The raw data returned by the SQL queries.
    timestamp_end: The end time for the data, should be the same as for query.
    process_args: Args for processing the data, see HindsightNextEditProcessArgs.
    """
    # errors will have one entry per next edit event, and as we add to errors,
    # we will remove the corresponding entry from next_edits_map.
    errors: list[HindsightNextEditError] = []
    next_edits_map = {next_edit.request_id: next_edit for next_edit in data.next_edits}

    # Warn about duplicate next edit datums. This is not fatal, but could indicate
    # an issue in the BQ query.
    duplicates = Counter(next_edit.request_id for next_edit in data.next_edits)
    duplicates = Counter({k: v for k, v in duplicates.items() if v > 1})
    if len(duplicates) > 0:
        logger.warning(
            "Found %s duplicate next edits. Total count: %s. "
            "Top 10 duplicate request_ids: %s",
            len(duplicates),
            duplicates.total(),
            duplicates.most_common(10),
        )

    # Check that the events are in chronological order.
    for i in range(1, len(data.events)):
        if data.events[i - 1].time > data.events[i].time:
            raise ValueError(
                f"Events are not in chronological order: "
                f"{data.events[i - 1].time} > {data.events[i].time}"
            )

    # Check the returned request ids are what we requested.
    # This shouldn't fail, but doesn't hurt to check.
    next_edit_events_map = {event.request_id: event for event in data.next_edit_events}
    if extra_ids := (next_edits_map.keys() - next_edit_events_map.keys()):
        raise ValueError(f"Got extra request ids: {extra_ids}")

    # Validate and filter individual next edits
    for event in data.next_edit_events:
        if event.request_id not in next_edits_map:
            errors.append(HindsightNextEditError(event, None, "missing_next_edits", ""))
            continue
        next_edit = next_edits_map[event.request_id]
        error = _validate_next_edit(next_edit, event)
        error = error or _filter_next_edit(next_edit, event, process_args)
        if error is not None:
            errors.append(error)
            next_edits_map.pop(event.request_id)

    # Dummy heuristic: return the next edit result as ground truth.

    # Collect ground truths and errors
    hindsight_next_edits: list[HindsightNextEditDatum] = []
    for event in data.next_edit_events:
        # May have been removed due to validation
        if event.request_id not in next_edits_map:
            continue

        next_edit = next_edits_map[event.request_id]

        # Result will be used in later code
        ground_truths: list[NextEditGroundTruth] = []
        if next_edit.response:
            ground_truths = [
                NextEditGroundTruth(
                    path=retrieved_location.path,
                    diff_span=DiffSpan(
                        original=CharRange(
                            suggestion.result.suggested_edit.char_start,
                            suggestion.result.suggested_edit.char_end,
                        ),
                        updated=CharRange(
                            suggestion.result.suggested_edit.char_start,
                            suggestion.result.suggested_edit.char_start
                            + len(suggestion.result.suggested_edit.suggested_code),
                        ),
                    ),
                    crange=CharRange(
                        suggestion.result.suggested_edit.char_start,
                        suggestion.result.suggested_edit.char_end,
                    ),
                    old_text=suggestion.result.suggested_edit.existing_code,
                    new_text=suggestion.result.suggested_edit.suggested_code,
                )
                for retrieved_location, suggestion in zip(
                    next_edit.response.retrieved_locations,
                    next_edit.response.suggestions,
                )
            ]

        hindsight_next_edits.append(
            HindsightNextEditDatum(
                next_edit_datum=next_edit, ground_truths=ground_truths
            )
        )
    return HindsightNextEditProcessResults(data=hindsight_next_edits, errors=errors)


@dataclass
class NextEditIntermediateErrorDetails:
    """Detailed errors for debugging NextEditIntermediateDataLoader."""

    no_events_found: list[str] = field(default_factory=list)
    """Requests with no events found. Contains request IDs with this error."""

    some_edited_files_not_found: dict[str, list[str]] = field(default_factory=dict)
    """Requests with some edited files not found. Contains a dictionary of request IDs to the files that were not found."""

    unable_to_reconstruct_files: dict[str, list[tuple[str, FileReplacementError]]] = (
        field(default_factory=dict)
    )
    """Requests with some files unable to be reconstructed. Contains a dictionary of request IDs to the `(blob_name, error)` tuples."""

    no_edited_files_found: list[str] = field(default_factory=list)
    """Requests with no edited files found. Contains request IDs with this error."""


@dataclass
class NextEditIntermediateDataLoader:
    """A simplified method of accessing next edit examples.
    Provides next edit requests with file contents and subsequent changes.
    """

    tenant: DatasetTenant
    """The tenant that the query belongs to."""

    query: HindsightNextEditQueryResults
    """The raw query results from `_query`."""

    blob_cache: BlobCache
    """The blob cache."""

    path_cache: PathCache
    """The path cache."""

    timestamp_end: Optional[datetime] = None
    """The end time to obtain data from. When running the iterator, only requests up to `self.end_time - timespan` will be processed.
    If no end time is set, the end time will be the time of the most recent text event."""

    @property
    def num_processed_requests(self) -> int:
        """The number of requests that have been processed in the last run of the iterator."""
        return self._processed_requests_count

    @property
    def error_details(self) -> NextEditIntermediateErrorDetails:
        """The error details from the last run of the iterator."""
        return self._error_details

    def __post_init__(self):
        text_events = [x for x in self.query.events if isinstance(x, TextEditEvent)]
        self._sorted_text_events = sorted(text_events, key=lambda x: x.time)

        if len(self._sorted_text_events) == 0:
            raise ValueError("No text events found in the query results.")

        # Get end time of the query if does not exist
        if not self.timestamp_end:
            self.timestamp_end = self._sorted_text_events[-1].time
        self.timestamp_end = self.timestamp_end.astimezone()

        # Match next edits with session IDs
        next_edit_events = [
            x for x in self.query.events if isinstance(x, NextEditRequestIdIssuedEvent)
        ]
        self._session_id_map = {
            x.request_id: x.session_id for x in next_edit_events
        }  # A mapping of request IDs to session IDs.

        self._processed_requests_count = 0  # The number of requests that have been processed in the last run of the iterator.

        self._error_details = NextEditIntermediateErrorDetails()

        self._next_edits = sorted(
            self.query.next_edits, key=lambda x: x.request.timestamp
        )  # Next edits sorted by time

    def _get_relevant_events(
        self,
        timespan: timedelta,
        suggestion: NextEditDatum,
    ) -> list[TextEditEvent]:
        """Gets all of the current session's events within `timespan` time after the
        request was issued.

        `timespan`: the timespan after the request is issued to accept requests.
        `suggestion`: the next edit suggestion to get events for.
        """

        left_bound = bisect_left(
            self._sorted_text_events,
            suggestion.request.timestamp.astimezone(),
            key=lambda x: x.time.astimezone(),
        )

        right_bound = bisect_right(
            self._sorted_text_events,
            suggestion.request.timestamp.astimezone() + timespan,
            key=lambda x: x.time.astimezone(),
        )

        relevant_events = [
            x
            for x in self._sorted_text_events[left_bound:right_bound]
            if x.tenant == self.tenant.name
            and x.session_id == self._session_id_map[suggestion.request_id]
            and x.user_id == suggestion.user_id
        ]

        return relevant_events

    def _get_paths(self, blob_names: Sequence[BlobName]) -> dict[str, BlobName]:
        """Returns a dictionary mapping paths to blob names.

        `blob_names`: the blob names to get paths for.
        """
        path_cache_results = self.path_cache.get(blob_names)
        return {
            str(path): blob_name
            for path, blob_name in zip(path_cache_results, blob_names)
            if path is not None
        }

    def iterator(
        self,
        timespan: timedelta,
        user_ids: Optional[list[str]] = None,
        session_ids: Optional[list[str]] = None,
    ) -> Iterator[NextEditIntermediateType]:
        """Iterates through all next edit requests and returns next edit problems.

        `timespan`: the timespan after the request is issued to accept requests.
        `user_ids`: the users to include. Do not filter if None.
        `session_ids`: the sessions to include. Do not filter if None.
        """
        self._processed_requests_count = 0
        self._error_details = NextEditIntermediateErrorDetails()

        for suggestion in self._next_edits:
            request = suggestion.request
            request_time = request.timestamp.astimezone()

            # Skip requests from users/sessions not in the filter, if the filter exists
            if user_ids is not None and suggestion.user_id not in user_ids:
                continue
            if (
                session_ids is not None
                and self._session_id_map[request.request_id] not in session_ids
            ):
                continue

            # Skip requests if the query data ends before `timespan` time elapses from the request time
            # This check ensures that requests where later events are missing from the query are not included
            assert self.timestamp_end is not None

            if self.timestamp_end - request_time < timespan:
                logger.info(
                    "Skipping request %s, less than %s time between request and end time",
                    request.request_id,
                    timespan,
                )
                continue

            # Log the status of requests
            self._processed_requests_count += 1

            # Get all events within the timespan
            relevant_events = self._get_relevant_events(timespan, suggestion)

            # Skip request if no relevant events are found
            if len(relevant_events) == 0:
                logger.info(
                    "Skipping request %s, no events found for request",
                    request.request_id,
                )
                self._error_details.no_events_found.append(request.request_id)
                continue

            # Get the path of all blobs' files
            path_mapping = self._get_paths(request.blob_names)

            # Get all paths and blobs modified by the user
            accessed_paths = set([x.file_path for x in relevant_events])
            accessed_blobs = [
                path_mapping[x]
                for x in accessed_paths
                if x in path_mapping
                and x
                != request.path  # The active file should be reconstructed from the request
            ]

            # Map the cache results into a format suitable for reconstruction
            accessed_blob_cache: Iterable[PathAndContent | None] = self.blob_cache.get(
                accessed_blobs
            )
            accessed_blob_cache_mapping = {
                blob_name: data
                for blob_name, data in zip(accessed_blobs, accessed_blob_cache)
                if data is not None
            }

            # Map paths to data
            paths_to_data: dict[str, tuple[BlobName, File | FileReplacementError]] = {
                str(data.path): (
                    blob_name,
                    File(path=str(data.path), contents=data.content),
                )
                for blob_name, data in accessed_blob_cache_mapping.items()
            }

            # Apply recent changes to files
            updated_files = get_current_version_of_files(
                [
                    x
                    for x in request.recent_changes
                    if x.blob_name in accessed_blob_cache_mapping
                ],
                accessed_blob_cache_mapping,
                skip_validation=True,
            )

            for blob_name, file in updated_files.items():
                if isinstance(file, File):
                    paths_to_data[file.path] = (blob_name, file)
                elif isinstance(file, FileReplacementError):
                    paths_to_data[str(file.file_path)] = (blob_name, file)

            # Add current file if edited in future events
            if request.path in accessed_paths:
                paths_to_data[request.path] = (
                    request.blob_name,
                    File(
                        path=request.path,
                        contents=request.prefix
                        + request.selected_text
                        + request.suffix,
                    ),
                )

            # Log request if some edited files are missing from the cache
            if len(paths_to_data) < len(accessed_paths):
                # This is normal. Changes to git files, node_modules, etc. are not indexed.
                reconstruction_attempt_paths: list[str] = []
                for blob_name, file in paths_to_data.values():
                    if isinstance(file, File):
                        reconstruction_attempt_paths.append(file.path)
                    elif isinstance(file, FileReplacementError):
                        reconstruction_attempt_paths.append(str(file.file_path))

                missing_files = list(
                    accessed_paths.difference(reconstruction_attempt_paths)
                )
                logger.info(
                    "Could not reconstruct all edited files for request %s. Missing files: %s",
                    request.request_id,
                    str(missing_files),
                )
                self._error_details.some_edited_files_not_found[request.request_id] = (
                    missing_files
                )

            # The final product, to be returned to the user
            files_for_events = {
                blob_name: file
                for blob_name, file in paths_to_data.values()
                if isinstance(file, File)
            }

            # Log missing files
            if len(files_for_events) < len(paths_to_data):
                # This should never happen, but this is being handled in case the reconstruction algorithm changes.
                logger.info(
                    "Could not reconstruct all edited files for request %s. Failed to reconstruct files: %s",
                    request.request_id,
                    str(
                        [
                            blob_name
                            for blob_name, error in paths_to_data.values()
                            if isinstance(error, FileReplacementError)
                        ]
                    ),
                )
                self._error_details.unable_to_reconstruct_files[request.request_id] = [
                    (blob_name, error)
                    for blob_name, error in paths_to_data.values()
                    if isinstance(error, FileReplacementError)
                ]

            # Skip request if no edited files are found
            if len(files_for_events) == 0:
                logger.info(
                    "Skipping request %s, no edited files found for request",
                    request.request_id,
                )
                self._error_details.no_edited_files_found.append(request.request_id)
                continue

            yield NextEditIntermediateType(
                request=request,
                future_events=relevant_events,
                files_for_events=files_for_events,
                session_id=self._session_id_map[request.request_id],
            )
