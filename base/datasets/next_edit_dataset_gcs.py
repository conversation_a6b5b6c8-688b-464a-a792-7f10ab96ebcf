"""Dataset for next edits logged via RequestInsight."""

# TODO(jeff): This is next_edits_dataset.py, but it uses GCS instead of BigQuery.
# This will eventually replace next_edits_dataset.py.
# Note that they have different interfaces.

import itertools
import logging
from collections.abc import Callable, Iterable, Mapping
from dataclasses import dataclass, replace
from datetime import timezone
from pathlib import Path
from typing import Iterator, TextIO

from dataclasses_json import dataclass_json
from google.cloud import storage

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import (
    BlobCache,
    CheckpointCache,
    GCSBlobCache,
    GCSCheckpointCache,
    PathAndContent,
    resolve_checkpoints,
)
from base.datasets.gcs_client import (
    GCSRequestInsightFetcher,
    Request,
    group_by_event_name,
    max_by_time,
)
from base.datasets.itertools import batched
from base.datasets.next_edit import NextEditDatum
from base.datasets.next_edit_conversion import (
    from_next_edit_request_proto,
    from_next_edit_response_protos,
)
from base.datasets.pipeline import Pipeline
from base.datasets.tenants import DatasetTenant
from services.request_insight import request_insight_pb2

logger = logging.getLogger(__name__)


@dataclass_json
@dataclass
class FieldsFilter:
    """Filters for the fields in NextEditDatum to return.

    By default, all fields are returned if present.
    This is useful for saving memory and time when only some fields are needed.
    Note that request and response are currently always required.
    """

    response: bool = True
    """If set, return the datum.response field."""
    request_blob_names: bool = True
    """If set, return the datum.request.blob_names field.

    Will also save a checkpoint cache call if False.
    """


@dataclass(frozen=True)
class NextEditDataset:
    """Dataset for next_edits logged via RequestInsight."""

    data: Iterable[NextEditDatum]
    """The data."""

    blobs: BlobCache
    """The blob cache."""

    def __iter__(
        self,
    ) -> Iterator[tuple[NextEditDatum, Mapping[str, PathAndContent]]]:
        """Iterate over the data and blobs."""
        for datum in self.data:
            blob_names = datum.request.blob_names
            blobs = self.blobs.get(blob_names)
            blob_map = {
                blob_name: blob
                for blob_name, blob in zip(blob_names, blobs)
                if blob is not None
            }
            yield datum, blob_map

    def dump_data(self, f: TextIO):
        """Dump data to a file."""
        for datum in self.data:
            f.write(NextEditDatum.schema().dumps(datum))
            f.write("\n")

    @staticmethod
    def load_data(f: TextIO, limit: int | None = None) -> list[NextEditDatum]:
        """Load data from a file."""
        f_limit = itertools.islice(f, limit)
        data: list[NextEditDatum] = []
        for line in f_limit:
            cur_d = NextEditDatum.schema().loads(line, many=False)
            assert isinstance(cur_d, NextEditDatum)
            data.append(cur_d)
        return data

    @staticmethod
    def create_data_from_gcs(
        tenant: DatasetTenant,
        request_ids: list[str],
        fields_filter: FieldsFilter | None = None,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        fetcher_max_pool_connections: int = 100,
        fetcher_batch_size: int | None = 64,
        process_batch_size: int = 128,
        queue_size: int = 10,
        service_account_file: Path | None = None,
        logging_fn: Callable | None = None,
    ) -> "NextEditDataFromGCS":
        """Create a dataset of next edits from GCS."""
        gcp_creds, _ = get_gcp_creds(service_account_file)
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        request_fetcher = GCSRequestInsightFetcher.from_tenant(
            tenant,
            max_pool_connections=fetcher_max_pool_connections,
        )
        checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
        checkpoint_cache = GCSCheckpointCache(
            checkpoint_bucket,
            tenant.checkpoint_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        fields_filter = fields_filter or FieldsFilter()
        if logging_fn is not None:
            checkpoint_cache.logging_fn = logging_fn
        return NextEditDataFromGCS(
            request_fetcher,
            checkpoint_cache,
            request_ids,
            fields_filter,
            fetcher_batch_size=fetcher_batch_size,
            process_batch_size=process_batch_size,
            queue_size=queue_size,
        )

    @staticmethod
    def create_blobs_from_gcs(
        tenant: DatasetTenant,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        service_account_file: Path | None = None,
    ) -> BlobCache:
        """Create a cache for the blobs via google cloud storage."""
        gcp_creds, _ = get_gcp_creds(service_account_file)
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        blob_cache = GCSBlobCache(
            blob_bucket,
            tenant.blob_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        return blob_cache


@dataclass
class _Row:
    request_id: str
    request_pb: request_insight_pb2.RequestEvent | None
    response_pbs: list[request_insight_pb2.RequestEvent] | None
    request_metadata_pb: request_insight_pb2.RequestEvent | None

    @staticmethod
    def from_request(request: Request) -> "_Row":
        """Create a row from a request."""
        grouped = group_by_event_name(request.events)
        return _Row(
            request_id=request.request_id,
            request_pb=max_by_time(grouped.get("next_edit_host_request")),
            response_pbs=grouped.get("next_edit_host_response"),
            request_metadata_pb=max_by_time(grouped.get("request_metadata")),
        )


# TODO(jeff): currently this doesn't do anything.
NON_HUMAN_PREFIXES = [
    "Augment-EvalHarness",
    "AugmentHealthCheck",
    "api_proxy_client",
    "augment.info-chat",
]


@dataclass(frozen=True)
class NextEditDataFromGCS(Iterable[NextEditDatum]):
    """Completion data from GCS. Implements Iterable[NextEditDatum]."""

    request_fetcher: GCSRequestInsightFetcher
    """The request fetcher."""

    checkpoint_cache: CheckpointCache
    """The checkpoint cache."""

    request_ids: list[str]
    """The request IDs to fetch."""

    fields_filter: FieldsFilter
    """Filters for the fields in NextEditDatum to return."""

    fetcher_batch_size: int | None = 64
    """Limits the number of concurrent requests to GCS when fetching requests.

    Note that None defaults to min(32, (os.cpu_count() or 1) + 4), but we default
    to 64 here since we should be IO-bound.
    """

    process_batch_size: int = 128
    """Processes requests in batches. In particular, this batches requests to the checkpoint cache,
    which is an LRU cache which does not have a fine-grained lock."""

    queue_size: int = 10
    """The size of the queue to use when fetching blobs. We use this
    queue to pipeline bigquery and GCS requests, and set a max size to
    avoid memory issues if users use this dataset in a notebook and don't
    iterate through the whole dataset."""

    def __iter__(self) -> Iterator[NextEditDatum]:
        # NOTE(arun): To hide the (often considerable) network latency in getting data
        # from bigquery, and blobs from GCS, we process the data in pipelined batches.
        # Getting data in batches speeds up the script by ~5-10x, and pipelining the
        # script speeds it up by another factor of 2-3x.
        request_event_names = self.get_request_event_names(self.fields_filter)
        requests = self.request_fetcher.get_requests(
            self.request_ids,
            request_event_names=request_event_names,
            batch_size=self.fetcher_batch_size,
        )
        pipeline = (
            Pipeline.from_source(batched(requests, self.process_batch_size))
            .and_then(self._process_batch)
            .run(max_queue_size=self.queue_size)
        )
        for batch in pipeline:
            yield from batch

    def _process_batch(
        self, batch: Iterable[Request | Exception]
    ) -> Iterable[NextEditDatum]:
        """Process a batch of requests."""
        rows = [
            _Row.from_request(request)
            for request in batch
            if isinstance(request, Request)
        ]
        rows = [row for row in rows if row.request_pb and row.request_metadata_pb]

        if self.fields_filter.request_blob_names:
            # A tragedy of type checking that we need to include the if still.
            blobs_list = [
                row.request_pb.next_edit_host_request.request.blobs
                for row in rows
                if row.request_pb
            ]
            assert len(rows) == len(blobs_list)
            blob_names_list = resolve_checkpoints(self.checkpoint_cache, blobs_list)
            # This will replace the blob_names field if not already set.
            return [
                self._make_datum(row, blob_names)
                for row, blob_names in zip(rows, blob_names_list)
            ]
        else:
            # This will clear the blob_names field.
            return [self._make_datum(row, None) for row in rows]

    def _make_datum(self, row: _Row, blob_names: list[str] | None) -> NextEditDatum:
        """Make a NextEditDatum from a row.

        If blob_names is present, we replace the blob_names field if not already set,
        so requests not in checkpoint format will remain with blob_names unchanged.
        If blob_names is None, we clear the blob_names field.
        """
        assert row.request_pb
        assert row.request_metadata_pb

        request = from_next_edit_request_proto(
            row.request_pb.next_edit_host_request,
            row.request_pb.time.ToDatetime(tzinfo=timezone.utc),
            row.request_id,
        )
        if blob_names is None:
            request = replace(request, blob_names=[])
        elif not request.blob_names:
            request = replace(request, blob_names=blob_names)

        response = None
        if row.response_pbs:
            response = from_next_edit_response_protos(
                [
                    response_pb.next_edit_host_response
                    for response_pb in row.response_pbs
                ],
                [
                    response_pb.time.ToDatetime(tzinfo=timezone.utc)
                    for response_pb in row.response_pbs
                ],
            )

        return NextEditDatum(
            request_id=row.request_id,
            user_id=row.request_metadata_pb.request_metadata.user_id,
            session_id=row.request_metadata_pb.request_metadata.session_id,
            request=request,
            response=response,
            user_agent=row.request_metadata_pb.request_metadata.user_agent,
        )

    @staticmethod
    def get_request_event_names(fields_filter: FieldsFilter) -> frozenset[str]:
        """Returns the request event names to fetch from GCS."""
        request_event_names = [
            "next_edit_host_request",
            "request_metadata",
        ]
        if fields_filter.response:
            request_event_names.append("next_edit_host_response")
        return frozenset(request_event_names)
