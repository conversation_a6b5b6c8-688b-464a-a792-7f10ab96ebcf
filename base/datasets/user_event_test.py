import dataclasses
from datetime import datetime
import hashlib
import math
from random import Random
from typing import Sequence
from base.datasets.user_event import (
    ContentChange,
    TextEditEvent,
    TextValidationError,
    merge_adjacent_edit_stream,
    merge_adjacent,
)
from base.ranges.range_types import Char<PERSON>ange
from base.test_utils.testing_utils import (
    error_context,
    random_str,
    run_property_test_sized,
)


def make_edit_event(
    content_changes: Sequence[ContentChange],
    file_path: str = "test.py",
    session_id: str = "",
    user_id: str = "",
    tenant: str = "",
    time: datetime = datetime(2024, 1, 1),
    after_changes_hash: str | None = None,
    hash_char_ranges: Sequence[CharRange] = (),
    after_doc_length: int | None = None,
) -> TextEditEvent:
    """Helper function to create a TextEditEvent for testing."""
    return TextEditEvent(
        session_id=session_id,
        user_id=user_id,
        tenant=tenant,
        time=time,
        file_path=file_path,
        content_changes=list(content_changes),
        after_changes_hash=after_changes_hash,
        hash_char_ranges=list(hash_char_ranges),
        after_doc_length=after_doc_length,
    )


def test_apply_to_text_basic():
    """Test basic text replacement functionality."""
    event = make_edit_event(
        content_changes=[
            ContentChange(
                text="world",
                crange=CharRange(0, 5),
            )
        ],
    )

    text = "hello there"
    result = event.apply_to_text(text)

    assert result == "world there", result


def test_apply_to_text_multiple_changes():
    """Test applying multiple content changes in one event."""
    event = make_edit_event(
        content_changes=[
            ContentChange(text="1", crange=CharRange(0, 1)),
            ContentChange(text="2", crange=CharRange(2, 3)),
        ],
    )

    text = "abcde"
    result = event.apply_to_text(text)

    assert result == "1b2de", result


def test_apply_to_text_with_hash_check():
    """Test that hash checking works correctly."""
    text = "some old text"
    new_text = "some modified text"
    checked_range = CharRange(2, len(new_text))
    correct_hash = hashlib.sha256(
        new_text[checked_range.to_slice()].encode("utf-8")
    ).hexdigest()
    incorrect_hash = "incorrect_hash"

    good_event = make_edit_event(
        content_changes=[ContentChange(text="modified", crange=CharRange(5, 8))],
        after_changes_hash=correct_hash,
        hash_char_ranges=[checked_range],
    )

    result = good_event.apply_to_text(text)

    assert result == new_text

    bad_event = dataclasses.replace(good_event, after_changes_hash=incorrect_hash)
    result = bad_event.apply_to_text(text)

    assert isinstance(result, TextValidationError)


def test_apply_to_text_with_doc_length():
    """Test that the doc length is used correctly."""
    text = "some old text"
    new_text = "some modified text"
    checked_range = CharRange(2, len(new_text))
    correct_hash = hashlib.sha256(
        new_text[checked_range.to_slice()].encode("utf-8")
    ).hexdigest()
    correct_length = len(new_text)
    incorrect_length = len(new_text) + 10

    good_event = make_edit_event(
        content_changes=[ContentChange(text="modified", crange=CharRange(5, 8))],
        after_changes_hash=correct_hash,
        hash_char_ranges=[checked_range],
        after_doc_length=correct_length,
    )

    result = good_event.apply_to_text(text)

    assert result == new_text

    bad_event = dataclasses.replace(good_event, after_doc_length=incorrect_length)
    result = bad_event.apply_to_text(text)

    assert isinstance(result, TextValidationError)


def test_try_merge():
    """Test TextEditEvent.try_merge method."""
    # Test case 1: Adjacent changes should merge
    event1 = make_edit_event([ContentChange(text="hello", crange=CharRange(0, 0))])
    event2 = make_edit_event([ContentChange(text=" world", crange=CharRange(5, 5))])
    assert merge_adjacent(event1, event2) == make_edit_event(
        [ContentChange(text="hello world", crange=CharRange(0, 0))]
    )

    # Test case 2: Non-adjacent changes should not merge
    event3 = make_edit_event([ContentChange(text="hello", crange=CharRange(0, 0))])
    event4 = make_edit_event([ContentChange(text="world", crange=CharRange(10, 10))])
    assert merge_adjacent(event3, event4) is None

    # Test case 3: Different file paths should not merge
    event5 = make_edit_event(
        [ContentChange(text="hello", crange=CharRange(0, 0))],
        file_path="file1.py",
    )
    event6 = make_edit_event(
        [ContentChange(text="world", crange=CharRange(5, 5))],
        file_path="file2.py",
    )
    assert merge_adjacent(event5, event6) is None

    # Test case 4: Consecutive deletions should merge
    # delete one char at position 3
    delete1 = make_edit_event([ContentChange(text="", crange=CharRange(3, 4))])
    # delete next char at position 3
    delete2 = make_edit_event([ContentChange(text="", crange=CharRange(3, 4))])
    # merged deletion of two chars
    assert merge_adjacent(delete1, delete2) == make_edit_event(
        [ContentChange(text="", crange=CharRange(3, 5))]
    )

    # Test case 5: Replace the same range
    replace1 = make_edit_event([ContentChange(text="old", crange=CharRange(2, 3))])
    # after replacement, the change range is 2:5
    replace2 = make_edit_event([ContentChange(text="new text", crange=CharRange(2, 5))])
    assert merge_adjacent(replace1, replace2) == make_edit_event(
        [ContentChange(text="new text", crange=CharRange(2, 3))]
    )

    # Test case 6: Left adjacent
    edit1 = make_edit_event([ContentChange(text="\n\na", crange=CharRange(3, 3))])
    edit2 = make_edit_event([ContentChange(text="b:?😃:a", crange=CharRange(2, 3))])
    assert merge_adjacent(edit1, edit2) == make_edit_event(
        [ContentChange(text="b:?😃:a\n\na", crange=CharRange(2, 3))]
    )


def test_merge_adjacent_edits_randomized():
    # Test that applying the merged events results in the same text as applying
    # the original events.
    def property(rng: Random, size: float):
        init_text = random_str(rng, max_len=math.ceil(20 * size))
        num_edits = 2 + rng.randint(0, math.ceil(10 * size))
        edits = list[TextEditEvent]()
        text = init_text
        for _ in range(num_edits):
            start = rng.randint(0, len(text))
            end = rng.randint(start, len(text))
            replacement = random_str(rng, max_len=8)
            edit = make_edit_event(
                content_changes=[
                    ContentChange(text=replacement, crange=CharRange(start, end))
                ],
                hash_char_ranges=[CharRange(start, start + len(replacement))],
                after_changes_hash=hashlib.sha256(
                    replacement.encode("utf-8")
                ).hexdigest(),
            )
            text = edit.apply_to_text(text)
            assert isinstance(text, str)
            edits.append(edit)
        final_text = text

        merged_edits = list(merge_adjacent_edit_stream(edits))
        assert len(merged_edits) <= num_edits
        merged_text = init_text
        for edit in merged_edits:
            merged_text = edit.apply_to_text(merged_text)
            assert isinstance(merged_text, str)

        edits_str = "\n".join(str(edit.content_changes) for edit in edits)
        merged_edits_str = "\n".join(str(edit.content_changes) for edit in merged_edits)
        with error_context(
            f"\n{init_text=},\n{final_text=},\n"
            f"Edits:\n{edits_str},\n"
            f"Merged Edits:\n{merged_edits_str}\n"
        ):
            assert merged_text == final_text

    run_property_test_sized(property, seed=100)
