"""Tests for base.datasets.pipeline."""

import sys
from functools import partial
import time
from collections.abc import Iterable

import pytest

from base.datasets.pipeline import Pipeline


def test_source_pipeline():
    pipeline = Pipeline.from_source(range(10))
    result = pipeline.run()
    assert list(result) == list(range(10))


def test_simple_pipeline():
    # Example of a pipeline that changes types at each stage.
    # The pipeline just adds up the digits of a number.
    result = (
        Pipeline.from_source([10, 15, 20])
        .and_then(lambda x: str(x), "str")
        .and_then(lambda x: [int(i) for i in x], "to_digits")
        .and_then(lambda xs: sum(xs), "sum")
        .run()
    )
    assert list(result) == [1, 6, 2]


def _increment_by_one(x: int, faulty_int: int = -1) -> int:
    """Test stage function that fails at a specified given index."""
    if x == faulty_int:
        raise ValueError("Simulated failure")
    return x + 1


def test_pipeline_without_failures():
    # Making the source large enough to trigger queue back-ups and force thread
    # switching.
    result = (
        Pipeline.from_source(range(100))
        .and_then(_increment_by_one, "stage1")
        .and_then(_increment_by_one, "stage2")
        .and_then(_increment_by_one, "stage3")
        .run()
    )
    assert list(result) == list(range(3, 103))


def test_pipeline_with_failure_start():
    # Making the source large enough to trigger queue back-ups and force thread
    # switching.
    result = (
        Pipeline.from_source(range(100))
        .and_then(partial(_increment_by_one, faulty_int=50), "stage1")
        .and_then(_increment_by_one, "stage2")
        .and_then(_increment_by_one, "stage3")
        .run(max_queue_size=10)
    )
    with pytest.raises(ValueError):
        list(result)


def test_pipeline_with_failure_middle():
    # Making the source large enough to trigger queue back-ups and force thread
    # switching.
    result = (
        Pipeline.from_source(range(100))
        .and_then(_increment_by_one, "stage1")
        .and_then(partial(_increment_by_one, faulty_int=50), "stage2")
        .and_then(_increment_by_one, "stage3")
        .run(max_queue_size=10)
    )
    with pytest.raises(ValueError):
        list(result)


def test_pipeline_with_failure_end():
    # Making the source large enough to trigger queue back-ups and force thread
    # switching.
    result = (
        Pipeline.from_source(range(100))
        .and_then(_increment_by_one, "stage1")
        .and_then(_increment_by_one, "stage2")
        .and_then(partial(_increment_by_one, faulty_int=50), "stage3")
        .run(max_queue_size=10)
    )
    with pytest.raises(ValueError):
        list(result)
