"""Dataclasses for hindsight completions."""

from dataclasses import dataclass

import dataclasses_json

from base.datasets.completion import CompletionDatum


@dataclass
class HindsightCompletionDatum(dataclasses_json.DataClassJsonMixin):
    """Represents a full completion event, and a heuristically derived ground_truth."""

    completion: CompletionDatum
    """The completion event."""

    ground_truth: str
    """The heuristic ground truth text."""
