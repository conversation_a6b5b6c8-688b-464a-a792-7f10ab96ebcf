from __future__ import annotations

import logging
import re
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Iterable, cast, Optional, Sequence, Tuple, TypedDict, Union

from google.cloud import bigquery  # type: ignore
from google.protobuf.json_format import ParseDict

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_client import GCSRequestInsightFetcher, SessionEventKey
import base.datasets.gcs_client as gcs_client
from base.datasets.tenants import DatasetTenant
from base.datasets.user_event import (
    CompletionRequestIdIssuedEvent,
    EditRequestIdIssuedEvent,
    NextEditRequestIdIssuedEvent,
    TextEditEvent,
    ContentChange,
)
from base.ranges import CharRange
from services.request_insight import request_insight_pb2

logger = logging.getLogger(__name__)

UserEventUnion = Union[
    CompletionRequestIdIssuedEvent,
    EditRequestIdIssuedEvent,
    NextEditRequestIdIssuedEvent,
    TextEditEvent,
]


@dataclass
class UserEventFilters:
    """Filters for the dataset."""

    timestamp_begin: datetime
    """If set, only return events with a timestamp >= than this."""
    timestamp_end: datetime
    """If set, only return events with a timestamp < than this."""
    event_types: list[str] | None = None
    """If set, only return events with an event type in this list."""
    session_id_begin: Optional[str] = None
    """If set, only return events with session IDs >= than this."""
    session_id_end: Optional[str] = None
    """If set, only return events with session IDs < than this."""


@dataclass
class UserEventStream:
    """A stream of user events."""

    events: Iterable[UserEventUnion]
    """The events in the stream."""
    num_events: Optional[int]
    """The number of events in the stream."""

    @classmethod
    def from_query(
        cls,
        tenant: DatasetTenant,
        filters: UserEventFilters,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
        fetcher_max_pool_connections: int = 100,
        fetcher_batch_size: int | None = 64,
        service_account_file: Optional[Path] = None,
    ) -> UserEventStream:
        """Create a user event stream from a BigQuery query.

        Args:
            tenant: The tenant to use.
            filters: Filters to apply to the dataset.
            limit: If set, the maximum number of rows to return.
            page_size: The page size to use when querying BigQuery.
                Defaults to a sensible value set by the API
            service_account_file: The path to a service account file to use to get
                credentials.
        """
        query, job_config = _build_query(
            tenant.search_dataset_name, tenant.tenant_id, filters, limit
        )
        gcp_creds, _ = get_gcp_creds(service_account_file)
        bigquery_client = bigquery.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        request_fetcher = GCSRequestInsightFetcher.from_tenant(
            tenant, max_pool_connections=fetcher_max_pool_connections
        )

        logger.info("Querying BigQuery: %s", query)
        logger.info("Job config: %s", job_config)
        row_iterator = bigquery_client.query_and_wait(
            query, job_config=job_config, page_size=page_size
        )
        logger.info("Found %d rows", row_iterator.total_rows or -1)
        rows = list(row_iterator)
        session_events = request_fetcher.get_session_events(
            [
                SessionEventKey(
                    session_id=row["session_id"],
                    event_name=row["event_type"],
                    event_id=row["event_id"],
                )
                for row in rows
            ],
            batch_size=fetcher_batch_size,
        )

        events = (
            event
            for row, session_event in zip(rows, session_events)
            if isinstance(session_event, gcs_client.SessionEvent)
            if (event := from_row_and_gcs_session_event(row, session_event)) is not None
        )
        return UserEventStream(
            events=events, num_events=cast(Optional[int], row_iterator.total_rows)
        )


class _Row(TypedDict):
    """A typed dict representing a row in the user_event BigQuery table."""

    session_id: str
    tenant_id: str
    tenant: str
    time: datetime
    event_type: str
    event_id: str


def _convert_content_changes(
    content_changes: Sequence[request_insight_pb2.ContentChange],
) -> list[ContentChange]:
    """Convert a sequence of ContentChange protos to a list of ContentChange objects."""
    return [
        ContentChange(
            text=content_change.text,
            crange=CharRange(
                start=content_change.range.start,
                stop=content_change.range.end,
            ),
        )
        for content_change in content_changes
    ]


def from_row_and_gcs_session_event(
    row: _Row, session_event: gcs_client.SessionEvent
) -> UserEventUnion | None:
    """Convert a row in BigQuery and a corresponding SessionEvent to a user event.

    Note: SKIPS missing or invalid data rather than throwing an exception.
    """
    assert row["event_id"] == session_event.key.event_id
    assert row["session_id"] == session_event.key.session_id
    assert row["event_type"] == session_event.key.event_name
    event_proto = session_event.event
    if event_proto is None:
        logger.warning("Event %s not found in GCS", row["event_id"])
        return None

    time = row["time"]
    if time != event_proto.time.ToDatetime(time.tzinfo):
        logger.warning(
            "Row time and proto time do not match for event %s: %s != %s",
            row["event_id"],
            row["time"],
            event_proto.time.ToDatetime(time.tzinfo),
        )

    # The row's time and the proto's time should be the same, but we use
    # the row's time, since this is what we've sorted by.
    event_type = event_proto.WhichOneof("event")
    if event_type == "completion_request_id_issued":
        proto = event_proto.completion_request_id_issued
        return CompletionRequestIdIssuedEvent(
            session_id=row["session_id"],
            user_id="",
            tenant=row["tenant"],
            time=row["time"],
            file_path=proto.file_path,
            request_id=proto.request_id,
        )
    elif event_type == "edit_request_id_issued":
        proto = event_proto.edit_request_id_issued
        return EditRequestIdIssuedEvent(
            session_id=row["session_id"],
            user_id="",
            tenant=row["tenant"],
            time=row["time"],
            file_path=proto.file_path,
            request_id=proto.request_id,
        )
    elif event_type == "next_edit_request_id_issued":
        proto = event_proto.next_edit_request_id_issued
        return NextEditRequestIdIssuedEvent(
            session_id=row["session_id"],
            user_id="",
            tenant=row["tenant"],
            time=row["time"],
            file_path=proto.file_path,
            request_id=proto.request_id,
        )
    elif event_type == "text_edit":
        proto = event_proto.text_edit
        return TextEditEvent(
            session_id=row["session_id"],
            user_id="",
            tenant=row["tenant"],
            time=row["time"],
            file_path=proto.file_path,
            content_changes=_convert_content_changes(proto.content_changes),
            after_changes_hash=proto.after_changes_hash
            if proto.HasField("after_changes_hash")
            else None,
            hash_char_ranges=[
                CharRange(start=crange.start, stop=crange.end)
                for crange in proto.hash_char_ranges
            ],
        )
    else:
        logger.warning(
            "Unknown event type, event_id: %s, event_type (BQ): %s, event_type (proto): %s",
            row["event_id"],
            row["event_type"],
            event_type,
        )
        return None


def _build_query(
    dataset_name: str,
    tenant_id: str,
    filters: UserEventFilters,
    limit: Optional[int] = None,
) -> Tuple[str, bigquery.QueryJobConfig]:
    """Build a BigQuery query."""

    # Some very light weight input validation to prevent SQL injections.
    if re.match(r"^[a-zA-Z0-9_\-]+$", dataset_name) is None:
        raise ValueError(f"Invalid dataset name: {dataset_name}")
    if re.match(r"^[a-zA-Z0-9_\-]+$", tenant_id) is None:
        raise ValueError(f"Invalid tenant id: {tenant_id}")
    query_parameters = []
    filter_exprs = []

    query_parameters.append(
        bigquery.ScalarQueryParameter("tenant_id", "STRING", tenant_id)
    )
    filter_exprs.append("tenant_id = @tenant_id")

    query_parameters.append(
        bigquery.ScalarQueryParameter(
            "timestamp_begin", "TIMESTAMP", filters.timestamp_begin
        )
    )
    filter_exprs.append("time >= @timestamp_begin")

    query_parameters.append(
        bigquery.ScalarQueryParameter(
            "timestamp_end", "TIMESTAMP", filters.timestamp_end
        )
    )
    filter_exprs.append("time < @timestamp_end")

    event_types = [
        "completion_request_id_issued",
        "edit_request_id_issued",
        "next_edit_request_id_issued",
        "text_edit",
    ]
    event_types = [
        event_type
        for event_type in event_types
        if filters.event_types is None or event_type in filters.event_types
    ]
    query_parameters.append(
        bigquery.ArrayQueryParameter("event_types", "STRING", event_types)
    )
    filter_exprs.append("event_type IN UNNEST(@event_types)")

    if filters.session_id_begin:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "session_id_begin", "STRING", filters.session_id_begin
            )
        )
        filter_exprs.append("session_id >= @session_id_begin")

    if filters.session_id_end:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "session_id_end", "STRING", filters.session_id_end
            )
        )
        filter_exprs.append("session_id < @session_id_end")

    if limit:
        query_parameters.append(bigquery.ScalarQueryParameter("limit", "INT64", limit))
        limit_expr = "LIMIT @limit"
    else:
        limit_expr = ""

    order_by_expr = "ORDER BY time"
    filter_expr = "\n".join(f"AND {expr}" for expr in filter_exprs)
    query = f"""
        SELECT
            session_id,
            tenant_id,
            tenant,
            time,
            event_type,
            event_id,
        FROM `{dataset_name}.session_event`
        WHERE TRUE
        {filter_expr}
        {order_by_expr}
        {limit_expr}
    """  # nosec
    return query, bigquery.QueryJobConfig(query_parameters=query_parameters)
