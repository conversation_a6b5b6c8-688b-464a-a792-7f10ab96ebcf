"""Tests for base.datasets.edit."""

from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock

from google.protobuf.json_format import MessageToDict

from base.blob_names import blob_names_pb2
from base.caching.cache import Cache
from base.datasets.edit import (
    EditDatum,
    EditPosition,
    EditRequest,
    EditResolution,
    EditResponse,
)
from base.datasets.edit_dataset import EditDataset
from base.datasets.gcs_blob_cache import CheckpointContent, PathAndContent
from services.edit_host import edit_pb2
from services.request_insight import request_insight_pb2


@dataclass
class SimulatedFile:
    """Simulates a "cached" file with a stale content manager view and a user view."""

    user_contents: str
    """The contents of the full file as seen by the user."""
    prefix_begin: int
    """Where the prefix region sent in requests begins."""
    cursor_position: int
    """Where the cursor position in the user's view is."""
    suffix_end: int
    """Where the suffix region sent in requests ends."""

    @property
    def cached_contents(self) -> str:
        """Simulates what the content manager view could be."""
        return (
            self.user_contents[: self.prefix_begin]
            # This is just a placeholder character.
            + "x" * (self.suffix_end - self.prefix_begin)
            + self.user_contents[self.suffix_end :]
        )

    @property
    def request_prefix(self) -> str:
        """The prefix that would be sent in a completion request."""
        return self.user_contents[self.prefix_begin : self.cursor_position]

    @property
    def request_suffix(self) -> str:
        """The suffix that would be sent in a completion request."""
        return self.user_contents[self.cursor_position : self.suffix_end]

    @property
    def full_prefix(self) -> str:
        """The full prefix of the user's view of the file."""
        return self.user_contents[: self.cursor_position]

    @property
    def full_suffix(self) -> str:
        """The full suffix of the user's view of the file."""
        return self.user_contents[self.cursor_position :]


def test_get_edits():
    # We set up a file with a simulated content manager and user view. This is probably
    # the most complicated logic in the completion dataset.
    simulated_file = SimulatedFile(
        user_contents="def foo():\n    pass\n\ndef bar():\n    pass\n",
        prefix_begin=10,
        cursor_position=15,
        suffix_end=20,
    )
    cached_blobs = {
        "my-blob-name": PathAndContent(
            Path("foo.py"),
            simulated_file.cached_contents,
        ),
    }
    cache = MagicMock(Cache)
    cache.get.side_effect = lambda keys: [cached_blobs.get(key) for key in keys]

    # In new format, the names are stored as raw bytes and not hex.
    # NOTE: two hex chars per byte so hex must be even.
    checkpoint_blobs = {
        "checkpoint-id": CheckpointContent(["my-blob-name", "de1e7edb10b0"])
    }
    checkpoint_cache = MagicMock(Cache)
    checkpoint_cache.get.side_effect = lambda keys: [
        checkpoint_blobs.get(key) for key in keys
    ]
    # The new state is checkpoint + added - deleted = ["my-blob-name", "addedb10b0"]
    added_blob_names = [bytes.fromhex("addedb10b0")]
    deleted_blob_names = [bytes.fromhex("de1e7edb10b0")]

    expected = EditDatum(
        request_id="test-request",
        user_id="test-user",
        user_agent="test-agent",
        request=EditRequest(
            prefix=simulated_file.full_prefix,
            suffix=simulated_file.full_suffix,
            selected_text="test-selected-text",
            instruction="test-instruction",
            path="test-path",
            lang="test-lang",
            blob_names=[a.hex() for a in added_blob_names],
            timestamp=datetime(2024, 1, 1, 0, 0, 0),
            position=EditPosition(
                prefix_begin=simulated_file.prefix_begin,
                suffix_end=simulated_file.suffix_end,
                blob_name="my-blob-name",
                original_prefix_length=(
                    simulated_file.cursor_position - simulated_file.prefix_begin
                ),
                original_suffix_length=(
                    simulated_file.suffix_end - simulated_file.cursor_position
                ),
            ),
        ),
        response=EditResponse(
            text="test-generated-text",
            model_name="test-model",
            timestamp=datetime(2024, 1, 1, 0, 0, 1),
            unknown_blob_names=["unknown-blob-name"],
            checkpoint_not_found=False,
        ),
        resolution=EditResolution(
            is_accepted=True,
            annotated_text="annotated text",
            annotated_instruction="annotated instruction",
            timestamp=datetime(2024, 1, 1, 0, 0, 2),
        ),
    )

    request = request_insight_pb2.RIEditRequest(
        request=edit_pb2.EditRequest(
            prefix=simulated_file.request_prefix,
            suffix=simulated_file.request_suffix,
            selected_text=expected.request.selected_text,
            instruction=expected.request.instruction,
            path=expected.request.path,
            lang=expected.request.lang,
            position=edit_pb2.EditPosition(
                prefix_begin=simulated_file.prefix_begin,
                suffix_end=simulated_file.suffix_end,
                blob_name="my-blob-name",
            ),
            model_name="test-model",
            blobs=blob_names_pb2.Blobs(
                baseline_checkpoint_id="checkpoint-id",
                added=added_blob_names,
                deleted=deleted_blob_names,
            ),
        )
    )
    response = request_insight_pb2.RIEditResponse(
        response=edit_pb2.EditResponse(
            text=expected.response.text,
            unknown_blob_names=expected.response.unknown_blob_names,
            checkpoint_not_found=expected.response.checkpoint_not_found,
        )
    )
    assert expected.resolution
    resolution = request_insight_pb2.EditResolution(
        is_accepted=expected.resolution.is_accepted,
        annotated_text=expected.resolution.annotated_text,
        annotated_instruction=expected.resolution.annotated_instruction,
    )

    dataset = EditDataset(
        [
            {
                "request_id": expected.request_id,
                "user_id": "test-user",
                "user_agent": "test-agent",
                "request_timestamp": expected.request.timestamp,
                "request_json": MessageToDict(request),
                "response_timestamp": expected.response.timestamp,
                "response_json": MessageToDict(response),
                "resolution_json": MessageToDict(resolution) if resolution else None,
                "resolution_timestamp": expected.resolution.timestamp,
            },
        ],
        1,
        cache,
        checkpoint_cache,
        reconstruct_request_files=True,
    )

    (actual,) = list(dataset.get_entries())
    assert actual == expected
