"""Dataset for completions logged via RequestInsight."""

import logging
import re
from collections.abc import Iterable, Sequence
from dataclasses import dataclass, field, replace
from datetime import datetime
from pathlib import Path
from typing import Literal, Optional, TypedDict, cast

from dataclasses_json import config as json_config
from dataclasses_json import dataclass_json
from google.cloud import bigquery, storage  # type: ignore
from google.protobuf.json_format import ParseDict
from marshmallow import fields
from typing_extensions import assert_never

from base.datasets.completion import (
    CompletionDatum,
    CompletionRequest,
    CompletionResolution,
)
from base.datasets.completion_conversion import (
    from_completion_feedback_proto,
    from_completion_request_proto,
    from_completion_response_proto,
    from_inference_response_proto,
)
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import (
    BlobCache,
    CheckpointCache,
    CheckpointContent,
    GCSBlobCache,
    GCSCheckpointCache,
    PathAndContent,
)
from base.datasets.itertools import batched
from base.datasets.pipeline import Pipeline
from base.datasets.tenants import DatasetTenant
from services.request_insight import request_insight_pb2

logger = logging.getLogger(__name__)


class _Row(TypedDict):
    """A typed dict representing a row in the BigQuery table."""

    request_id: str
    user_id: str
    user_agent: str
    request_json: dict
    request_timestamp: datetime
    response_json: dict
    response_timestamp: datetime
    retrieval_jsons: Optional[list[dict]]
    accepted: Optional[bool]
    resolution_timestamp: Optional[datetime]
    feedback_json: Optional[dict]
    feedback_timestamp: Optional[datetime]
    inference_response_json: Optional[dict]
    inference_response_timestamp: Optional[datetime]


class CompletionDataset:
    """Dataset for completions logged via RequestInsight."""

    OrderBy = Literal["request_id", "request_timestamp", "-request_timestamp", None]
    """Options to order this dataset by."""

    @dataclass_json
    @dataclass
    class Filters:
        """Filters for the dataset."""

        timestamp_begin: Optional[datetime] = field(
            default=None,
            metadata=json_config(
                encoder=lambda dt: dt.strftime("%Y-%m-%d"),
                mm_field=fields.DateTime(format="%Y-%m-%d"),
            ),
        )
        """If set, only return events with a timestamp >= than this."""
        timestamp_end: Optional[datetime] = field(
            default=None,
            metadata=json_config(
                encoder=lambda dt: dt.strftime("%Y-%m-%d"),
                mm_field=fields.DateTime(format="%Y-%m-%d"),
            ),
        )
        """If set, only return events with a timestamp < than this."""
        accepted_completion: Optional[bool] = None
        """If set, only return events which were accepted (True) or rejected (False)."""
        model_names: Optional[list[str]] = None
        """If set, only return events that used by any of these models."""
        min_completion_length: Optional[int] = 1
        """If set, only return events with a completion length greater than this."""
        max_resolution_time_ms: Optional[int] = None
        """If set, only return events with a resolution time less than this."""
        min_reject_resolution_time_ms: Optional[int] = None
        """If set, only return events with a resolution time greater than this."""
        request_ids: Optional[list[str]] = None
        """If set, only return events with a request ID in this list."""
        # TODO(arun): Support filtering by types of feedback or keywords once these
        # are exposed as columns in bigquery.
        with_feedback: bool = False
        """If set, only return events with feedback."""

    def __init__(
        self,
        rows: Iterable[_Row],
        num_rows: Optional[int],
        blob_cache: BlobCache,
        checkpoint_cache: CheckpointCache,
        with_reconstructed_files: bool = True,
        page_size: int = 128,
        queue_size: int = 10,
    ):
        """Create a dataset of completions.

        Args:
            rows: The rows of the dataset. This is typically created by calling
                `.create()`, but is exposed for testing.
            num_rows: The number of rows in the dataset.
            blob_cache: The blob cache to use.
            with_reconstructed_files: If set, we will reconstruct the whole prefix and
                suffix in a request.
            page_size: The page size to use when querying BigQuery. The default here
                is a reasonable baseline, and shouldn't need to be fiddled with much.
            queue_size: The size of the queue to use when fetching blobs. We use this
                queue to pipeline bigquery and GCS requests, and set a max size to
                avoid memory issues if users use this dataset in a notebook and don't
                iterate through the whole dataset.
        """
        self._rows = rows
        self._num_rows = num_rows
        self._blob_cache = blob_cache
        self._checkpoint_cache = checkpoint_cache
        self._with_reconstructed_files = with_reconstructed_files
        self._page_size = page_size
        self._queue_size = queue_size

    @classmethod
    def create(
        cls,
        tenant: DatasetTenant,
        filters: Filters = Filters(),
        limit: Optional[int] = None,
        order_by: OrderBy = None,
        with_reconstructed_files: bool = True,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        page_size: int = 128,
        queue_size: int = 10,
        service_account_file: Optional[Path] = None,
    ):
        """Create a dataset of completions.

        Args:
            tenant: The tenant to use.
            filters: Filters to apply to the dataset.
            limit: If set, the maximum number of rows to return.
            order_by: If set, the order to return the rows in.
            with_reconstructed_files: If set, we will reconstruct the whole prefix and
               suffix in a request.
            blob_cache_size_bytes: The size of the blob cache in bytes.
            blob_cache_num_threads: The number of threads to use for the blob cache.
            page_size: The page size to use when querying BigQuery. The default here
                is a reasonable baseline, and shouldn't need to be fiddled with much.
            queue_size: The size of the queue to use when fetching blobs. We use this
                queue to pipeline bigquery and GCS requests, and set a max size to
                avoid memory issues if users use this dataset in a notebook and don't
                iterate through the whole dataset.
            service_account_file: The path to a service account file to use to get
                resources from BigQuery and GCS.
                If not set, the default credentials will be used.
        """
        query, job_config = _build_query(
            tenant.dataset_name, tenant.name, filters, order_by, limit
        )
        return _create_from_query(
            query,
            job_config,
            tenant,
            with_reconstructed_files,
            blob_cache_size_bytes,
            blob_cache_num_threads,
            page_size,
            queue_size,
            service_account_file,
        )

    @property
    def num_rows(self) -> Optional[int]:
        """Try to get the number of rows."""
        return self._num_rows

    def get_blobs(self, keys: Iterable[str]) -> Iterable[Optional[PathAndContent]]:
        return self._blob_cache.get(keys)

    def get_completions(self) -> Iterable[CompletionDatum]:
        # NOTE(arun): To hide the (often considerable) network latency in getting data
        # from bigquery, and blobs from GCS, we process the data in pipelined batches.
        # Getting data in batches speeds up the script by ~5-10x, and pipelining the
        # script speeds it up by another factor of 2-3x.
        pipeline = (
            Pipeline.from_source(batched(self._rows, self._page_size))
            .and_then(self._process_batch)
            .run(max_queue_size=self._queue_size)
        )
        for batch in pipeline:
            yield from batch

    def _process_batch(self, batch: Iterable[_Row]) -> Iterable[CompletionDatum]:
        request_pbs = [
            ParseDict(
                row["request_json"],
                request_insight_pb2.CompletionHostRequest(),
                ignore_unknown_fields=True,
            )
            for row in batch
        ]

        requests = [
            from_completion_request_proto(
                request_pb, row["request_timestamp"], row["request_id"]
            )
            for row, request_pb in zip(batch, request_pbs)
        ]
        # Reconstruct after resolving, since we will remove the current
        # blob_name from blob_names.
        requests = self._resolve_checkpoints(requests, request_pbs)
        if self._with_reconstructed_files:
            requests = self._reconstruct_files(requests)

        responses = [
            from_completion_response_proto(
                ParseDict(
                    row["response_json"],
                    request_insight_pb2.CompletionHostResponse(),
                    ignore_unknown_fields=True,
                ),
                [
                    ParseDict(
                        retrieval_json,
                        request_insight_pb2.RetrievalResponse(),
                        ignore_unknown_fields=True,
                    )
                    for retrieval_json in (row["retrieval_jsons"] or [])
                ],
                request_pb,
                row["response_timestamp"],
            )
            for row, request_pb in zip(batch, request_pbs)
        ]

        resolutions = [
            (
                CompletionResolution(
                    accepted=row["accepted"],
                    timestamp=row["resolution_timestamp"],
                )
                if "accepted" in row.keys()
                and row["accepted"] is not None
                and "resolution_timestamp" in row.keys()
                and row["resolution_timestamp"] is not None
                else None
            )
            for row in batch
        ]

        feedback = [
            (
                from_completion_feedback_proto(
                    ParseDict(
                        row["feedback_json"],
                        request_insight_pb2.CompletionFeedback(),
                        ignore_unknown_fields=True,
                    ),
                    row["feedback_timestamp"],
                )
                if "feedback_json" in row.keys()
                and row["feedback_json"] is not None
                and "feedback_timestamp" in row.keys()
                and row["feedback_timestamp"] is not None
                else None
            )
            for row in batch
        ]

        inference_responses = [
            (
                from_inference_response_proto(
                    ParseDict(
                        row["inference_response_json"],
                        request_insight_pb2.InferenceHostResponse(),
                        ignore_unknown_fields=True,
                    ),
                    row["inference_response_timestamp"],
                )
                if "inference_response_json" in row.keys()
                and row["inference_response_json"] is not None
                and "inference_response_timestamp" in row.keys()
                and row["inference_response_timestamp"] is not None
                else None
            )
            for row in batch
        ]

        return [
            CompletionDatum(
                request_id=row["request_id"],
                user_id=row["user_id"],
                request=request,
                response=response,
                resolution=resolution,
                feedback=feedback_,
                inference_response=inference_response,
                user_agent=row["user_agent"],
            )
            for row, request, response, resolution, feedback_, inference_response in zip(
                batch,
                requests,
                responses,
                resolutions,
                feedback,
                inference_responses,
            )
            if request and response
        ]

    def _reconstruct_files(
        self, requests: Sequence[CompletionRequest]
    ) -> Sequence[CompletionRequest]:
        """Reconstruct the complete prefix and suffix given position information.

        This method is batched to be more efficient with getting blobs from GCS.
        """
        cached_files = self._blob_cache.get(
            [
                request.position.blob_name
                if request.position is not None
                and request.position.blob_name is not None
                else ""
                for request in requests
            ]
        )

        def _update_request(
            request: CompletionRequest, cached_file: Optional[PathAndContent]
        ) -> CompletionRequest:
            if request.position is None or cached_file is None:
                return request

            # Remove current file's blob name and update the prefix/suffix.
            blob_names = list(request.blob_names)
            if request.position.blob_name in blob_names:
                blob_names.remove(request.position.blob_name)
            prefix = (
                cached_file.content[: request.position.prefix_begin] + request.prefix
            )
            suffix = request.suffix + cached_file.content[request.position.suffix_end :]
            return replace(request, prefix=prefix, suffix=suffix, blob_names=blob_names)

        return [
            _update_request(request, cached_file)
            for request, cached_file in zip(requests, cached_files)
        ]

    def _resolve_checkpoints(
        self,
        requests: Sequence[CompletionRequest],
        request_pbs: Sequence[request_insight_pb2.CompletionHostRequest],
    ) -> Sequence[CompletionRequest]:
        """Resolve the checkpoints into blob names for each request.

        Requests not in checkpoint format will remain with blob_names unchanged.
        This method is batched to be more efficient with getting blobs from GCS.
        """
        checkpoints = self._checkpoint_cache.get(
            [
                request_pb.blobs.baseline_checkpoint_id
                if request_pb.blobs.HasField("baseline_checkpoint_id")
                else ""
                for request_pb in request_pbs
            ]
        )

        def _update_request(
            request: CompletionRequest,
            request_pb: request_insight_pb2.CompletionHostRequest,
            checkpoint: Optional[CheckpointContent],
        ) -> CompletionRequest:
            if request.blob_names:
                return request

            # New checkpoint format uses bytes for blob names, but the actual keys
            # in BigTable are hex encoded.
            added_blob_names = set(name.hex() for name in request_pb.blobs.added)
            deleted_blob_names = set(name.hex() for name in request_pb.blobs.deleted)
            checkpoint_blob_names = set(
                checkpoint.blob_names if checkpoint is not None else []
            )
            blob_names = sorted(
                list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
            )

            return replace(request, blob_names=blob_names)

        return [
            _update_request(request, request_pb, checkpoint)
            for request, request_pb, checkpoint in zip(
                requests, request_pbs, checkpoints
            )
        ]


def _create_from_query(
    query: str,
    job_config: bigquery.QueryJobConfig,
    tenant: DatasetTenant,
    with_reconstructed_files: bool = True,
    blob_cache_size_bytes: int = 2**30,
    blob_cache_num_threads: int = 32,
    page_size: int = 128,
    queue_size: int = 10,
    service_account_file: Optional[Path] = None,
):
    """Create a dataset of completions from a SQL query."""
    gcp_creds, _ = get_gcp_creds(service_account_file)
    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
    blob_cache = GCSBlobCache(
        blob_bucket,
        tenant.blob_bucket_prefix,
        blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
    checkpoint_cache = GCSCheckpointCache(
        checkpoint_bucket,
        tenant.checkpoint_bucket_prefix,
        blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    logger.info("Querying BigQuery: %s", query)
    rows = bigquery_client.query_and_wait(
        query, job_config=job_config, page_size=page_size
    )
    logger.info("Found %d rows", rows.total_rows or -1)

    return CompletionDataset(
        rows,
        cast(Optional[int], rows.total_rows),
        blob_cache=blob_cache,
        checkpoint_cache=checkpoint_cache,
        with_reconstructed_files=with_reconstructed_files,
        page_size=page_size,
        queue_size=queue_size,
    )


def _build_query(
    dataset_name: str,
    tenant_name: str,
    filters: CompletionDataset.Filters,
    order_by: CompletionDataset.OrderBy = None,
    limit: Optional[int] = None,
) -> tuple[str, bigquery.QueryJobConfig]:
    # Some very light weight input validation to prevent SQL injections.
    if re.match(r"^[a-zA-Z0-9_\-]+$", dataset_name) is None:
        raise ValueError(f"Invalid dataset name: {dataset_name}")
    if re.match(r"^[a-zA-Z0-9_\-]+$", tenant_name) is None:
        raise ValueError(f"Invalid tenant name: {tenant_name}")
    if filters.request_ids:
        for request_id in filters.request_ids:
            if re.match(r"^[a-zA-Z0-9_\-]+$", request_id) is None:
                raise ValueError(f"Invalid request ID: {request_id}")

    ensure_tables = set()
    query_parameters = []
    filter_exprs = {
        "request": [],
        "response": [],
        "retrieval": [],
        "resolution": [],
        "feedback": [],
        "inference_response": [],
        "metadata": [],
        "all": [],
    }
    query_parameters.append(
        bigquery.ScalarQueryParameter("tenant", "STRING", tenant_name)
    )
    if filters.timestamp_begin:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "timestamp_begin", "TIMESTAMP", filters.timestamp_begin
            )
        )
    if filters.timestamp_end:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "timestamp_end", "TIMESTAMP", filters.timestamp_end
            )
        )
    if filters.request_ids:
        query_parameters.append(
            bigquery.ArrayQueryParameter("request_ids", "STRING", filters.request_ids)
        )
    for table in filter_exprs:
        if table == "all":
            continue

        filter_exprs[table].append("tenant = @tenant")
        if filters.timestamp_begin:
            filter_exprs[table].append("time >= @timestamp_begin")
        if filters.timestamp_end:
            filter_exprs[table].append("time < @timestamp_end")
        if filters.request_ids:
            filter_exprs[table].append("request_id IN UNNEST(@request_ids)")

    if filters.model_names:
        query_parameters.append(
            bigquery.ArrayQueryParameter("model_names", "STRING", filters.model_names)
        )
        filter_exprs["request"].append(
            "JSON_EXTRACT_SCALAR(raw_json, '$.model') IN UNNEST(@model_names)"
        )
        ensure_tables.add("request")

    # NOTE(jeff): In data older than 2024-03-09 or so, the response is split across
    # multiple rows, one of which has the retrieval response, and the other has the
    # completion response. This filters out the retrieval response rows.
    # We need to look up retrieved_chunks and embeddings_prompt because null text is not
    # a distinguishing feature since the response can be empty.
    filter_exprs["response"].append(
        "JSON_EXTRACT(raw_json, '$.embeddings_prompt') is NULL"
    )
    filter_exprs["response"].append(
        "JSON_EXTRACT(raw_json, '$.retrieved_chunks') is NULL"
    )
    if filters.min_completion_length:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "min_completion_length", "INT64", filters.min_completion_length
            )
        )
        filter_exprs["response"].append(
            "CHAR_LENGTH(JSON_VALUE(raw_json, '$.text')) >= @min_completion_length"
        )

    resolution_time_ms_expr = (
        "EXTRACT(second FROM resolution.time - response.time) * 1000 "
        "+ EXTRACT(millisecond FROM resolution.time - response.time)"
    )

    # Check for None as both True and False are set valid values.
    if filters.accepted_completion is not None:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "accepted_completion", "BOOL", filters.accepted_completion
            )
        )
        filter_exprs["all"].append("resolution.accepted = @accepted_completion")
        ensure_tables.add("resolution")
    if filters.max_resolution_time_ms:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "max_resolution_time_ms", "INT64", filters.max_resolution_time_ms
            )
        )
        filter_exprs["all"].append(
            f"{resolution_time_ms_expr} <= @max_resolution_time_ms"
        )
        ensure_tables.add("resolution")
    if filters.min_reject_resolution_time_ms:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "min_reject_resolution_time_ms",
                "INT64",
                filters.min_reject_resolution_time_ms,
            )
        )
        filter_exprs["all"].append(
            f"(resolution.accepted OR {resolution_time_ms_expr} "
            ">= @min_reject_resolution_time_ms)"
        )
        ensure_tables.add("resolution")

    if filters.with_feedback:
        ensure_tables.add("feedback")

    # Add all the tables that have to be present for the filters to work.
    filter_exprs["all"].extend(
        f"{table}.raw_json IS NOT NULL" for table in ensure_tables
    )

    filter_expr = {
        table: "\n".join(f"AND {expr}" for expr in filter_exprs[table])
        for table in filter_exprs
    }

    if order_by is None:
        order_by_expr = ""
    elif order_by == "request_id":
        order_by_expr = "ORDER BY request.request_id"
    elif order_by == "request_timestamp":
        order_by_expr = "ORDER BY request.time"
    elif order_by == "-request_timestamp":
        order_by_expr = "ORDER BY request.time DESC"
    else:
        assert_never(order_by)

    if limit:
        query_parameters.append(bigquery.ScalarQueryParameter("limit", "INT64", limit))
        limit_expr = "LIMIT @limit"
    else:
        limit_expr = ""

    # NOTE(arun): We have sanitized all the input going into this expression.
    # Furthermore, we run this query with read-only permissions, so injections shouldn't
    # even be possible.
    # NOTE(jeff): Queries before 2024-02-22 won't work because request_metadata
    # is newer and was never backfilled.
    # NOTE(jeff): accepted_idx is its own column in resolution because of proto
    # shenanigans. It would also be equivalent to
    # COALESCE(PARSE_NUMERIC(JSON_VALUE(raw_json, "$.accepted_idx")), 0)
    query = f"""
        WITH
            request AS (
                SELECT
                    request_id,
                    raw_json,
                    time,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'completion_host_request'
                {filter_expr['request']}
            ),
            response AS (
                SELECT
                    request_id,
                    MAX_BY(raw_json, time) as raw_json,
                    MAX(time) as time,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'completion_host_response'
                {filter_expr['response']}
                GROUP BY request_id
            ),
            retrieval AS (
                SELECT
                    request_id,
                    ARRAY_AGG(raw_json) as raw_jsons,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'retrieval_response'
                {filter_expr['retrieval']}
                GROUP BY request_id
            ),
            resolution AS (
                SELECT
                    request_id,
                    MAX_BY(raw_json, time) AS raw_json,
                    MAX(time) as time,
                    MAX_BY(accepted_idx >= 0, time) AS accepted,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'completion_resolution'
                {filter_expr['resolution']}
                GROUP BY request_id
            ),
            inference_response AS (
                SELECT
                    request_id,
                    MAX_BY(raw_json, time) AS raw_json,
                    MAX(time) as time,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'inference_host_response'
                {filter_expr['inference_response']}
                GROUP BY request_id
            ),
            feedback AS (
                SELECT
                    request_id,
                    MAX_BY(raw_json, time) AS raw_json,
                    MAX(time) as time,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'completion_feedback'
                {filter_expr['feedback']}
                GROUP BY request_id
            ),
            metadata AS (
                SELECT
                    request_id,
                    MAX(JSON_VALUE(raw_json, "$.user_id")) AS user_id,
                    MAX(JSON_VALUE(raw_json, "$.user_agent")) AS user_agent,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'request_metadata'
                AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'AugmentHealthCheck')
                AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'augment_review_bot')
                AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'Augment-EvalHarness')
                AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'augment.info-chat')
                {filter_expr['metadata']}
                GROUP BY request_id
            )
        SELECT
            request.request_id,
            metadata.user_id,
            metadata.user_agent,
            request.raw_json as request_json,
            request.time as request_timestamp,
            response.raw_json as response_json,
            response.time as response_timestamp,
            retrieval.raw_jsons as retrieval_jsons,
            resolution.accepted,
            resolution.time as resolution_timestamp,
            feedback.raw_json as feedback_json,
            feedback.time as feedback_timestamp,
            inference_response.raw_json as inference_response_json,
            inference_response.time as inference_response_timestamp
        FROM request
        JOIN response USING (request_id)
        JOIN metadata USING (request_id)
        LEFT JOIN retrieval USING (request_id)
        LEFT JOIN resolution USING (request_id)
        LEFT JOIN feedback USING (request_id)
        LEFT JOIN inference_response USING (request_id)
        WHERE TRUE
        {filter_expr["all"]}
        {order_by_expr}
        {limit_expr}
        """  # nosec
    return query, bigquery.QueryJobConfig(query_parameters=query_parameters)
