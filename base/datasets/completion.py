"""Dataset for completions logged via RequestInsight."""

import enum
from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional

import dataclasses_json
from base.datasets.recency_info import RecencyInfo
from base.ranges import CharRange


@dataclass
class RetrievedChunk(dataclasses_json.DataClassJsonMixin):
    """Represents a single retrieved chunk."""

    text: str
    """The text of the chunk."""

    origin: str
    """The name of the retriever that originated this chunk."""

    blob_name: str
    """Where the prefix region of the request starts in blob_name."""

    path: str
    """The path of the file the chunk comes from."""

    crange: CharRange
    """Offset in UTF-8 characters where the region represented by this chunk starts."""


@dataclass(frozen=True)
class CompletionPosition(dataclasses_json.DataClassJsonMixin):
    """Represents the position of a completion request relative to a cached blob.

    The real position data sent to the backend, depending on client version, may
    be missing various fields (which then get converted in RI to empty or 0s).

    blob_name should always be added as None if it was the empty string.
    For the other fields, we offer a validate method that tries to detect when all the
    fields are legitimately 0 vs missing.
    """

    blob_name: str | None
    """The blob name of the cached copy of the file.

    We try to use None instead of empty string when a blob_name is missing to force
    callers to think about the missing case via type-checking.
    """

    prefix_begin: int
    """The offset in Unicode characters where the prefix region begins. At different
    times, this may have been relative to the `blob_name` or to the *current file*."""

    cursor_position: int
    """The offset in Unicode characters of the cursor position is in the *current file*."""

    suffix_end: int
    """The offset in Unicode characters where the suffix region ends. At different
    times, this may have been relative to the `blob_name` or to the *current file*."""

    original_prefix_length: int
    """The length of the prefix in the original request (in Unicode characters)."""

    original_suffix_length: int
    """The length of the suffix in the original request (in Unicode characters)."""

    def guess_validity(self) -> bool:
        """Returns true if we think that the fields are most likely valid, e.g.
        a 0 value actually indicates 0 and not None.

        Notes:
        1. Before https://github.com/augmentcode/augment/pull/8211, it is possible for
        cursor position to be missing if blob_name is missing (e.g. untracked file).
        Afterwards, all the fields should be present.
        2. After ~07/04 (possibly, https://github.com/augmentcode/augment/pull/8572),
        it is possible for blob_name only to be missing, despite having cursor_position.
        3. At some point in time, prefix_begin/suffix_end switched from being `blob_name`
        relative to *current file* relative.
        4. At some point in time, it is possible for the characters before prefix_begin
        to not be equal between `blob_name` and the current file (e.g. there are recent
        changes).
        """
        # If this fails, either the cursor is missing, or we did the notebook
        # translation incorrectly. Either way, not valid.
        if self.cursor_position < self.original_prefix_length:
            return False

        # These are set together, so if any one is present, they're all valid.
        if self.cursor_position != 0 or self.prefix_begin != 0 or self.suffix_end != 0:
            return True

        # If blob_name is set, then they're all valid (even if 0).
        if self.blob_name is not None:
            return True

        # By here, everything is empty or 0 except suffix. This is the guessing part:
        # If suffix is also empty, then likely this is just an empty file.
        if self.original_suffix_length == 0:
            return True

        # Otherwise, it is technically possible for suffix to be nonempty, but
        # more likely, cursor_position, etc. are missing.
        return False


@dataclass
class SingleEdit(dataclasses_json.DataClassJsonMixin):
    """Represents a single edit."""

    before_start: int
    """The start of the edit in the before file."""

    after_start: int
    """The start of the edit in the after file."""

    before_text: str
    """The text before the edit."""

    after_text: str
    """The text after the edit."""


@dataclass
class GranularEditEvent(dataclasses_json.DataClassJsonMixin):
    """Represents a single granular edit event."""

    path: str
    """The path of the file being edited."""

    before_blob_name: str
    """The blob name of the file before the edit."""

    after_blob_name: str
    """The blob name of the file after the edit."""

    edits: list[SingleEdit]
    """The edits in this event, assumed to happen in parallel."""


@dataclass(frozen=True)
class CompletionRequest(dataclasses_json.DataClassJsonMixin):
    """Represents a single completion request."""

    prefix: str
    """The prefix in the completion request.

    To make it easier to use the completion request, the prefix is expanded to begin at
    the start of the file.
    """

    suffix: str
    """The suffix in the completion request.

    To make it easier to use the completion request, the suffix is expanded to the end
    of the file.
    """

    path: str
    """The path of the current file being completed."""

    # NOTE(arun): the type of this field needs to be a list because dataclasses-json
    # doesn't understand Sequence.
    blob_names: list[str]
    """The list of blob names (i.e. files) in the completion request.

    This list does not contain the blob name for "current file" (i.e. the file being
    completed); instead the current file contents is `prefix + suffix`.
    """

    output_len: int
    """The maximum number of tokens to generate overall."""

    timestamp: datetime
    """The timestamp of the completion request."""

    position: Optional[CompletionPosition] = None
    """The position of the completion request. May be None if missing/invalid.

    This information is useful when replaying requests against a production model
    client.
    """

    recency_info: Optional[RecencyInfo] = None
    """Recent client events."""

    edit_events: list[GranularEditEvent] | None = None
    """More granular events representing the most recent edits."""

    @property
    def original_prefix(self) -> str:
        """The original prefix in the completion request."""
        if not self.position:
            return self.prefix
        assert 0 <= self.position.original_prefix_length <= len(self.prefix)
        # Handles the case where self.position.original_prefix_length == 0
        return self.prefix[len(self.prefix) - self.position.original_prefix_length :]

    @property
    def original_suffix(self) -> str:
        """The original suffix in the completion request."""
        if not self.position:
            return self.suffix
        assert 0 <= self.position.original_suffix_length <= len(self.suffix)
        return self.suffix[: self.position.original_suffix_length]


@dataclass
class CompletionResponse(dataclasses_json.DataClassJsonMixin):
    """Represents a single completion response."""

    text: str
    """The completion text generated by the system."""

    model: str
    """The model used to generate the completion."""

    skipped_suffix: str
    """If non-empty, the suffix that is "skipped over" in the completion request."""

    suffix_replacement_text: str
    """If non-empty, the suffix that replaces the "skipped over" suffix."""

    unknown_blob_names: list[str]
    """The list of files (blobs) missing in the completion request (and response).

    Used to track or exactly reproduce the behavior of the system.
    """

    retrieved_chunks: list[RetrievedChunk]
    """The list of chunks that were retrieved for this query."""

    timestamp: datetime
    """The timestamp of the completion response."""

    tokens: list[str]
    """The list of tokens generated by the system."""

    token_log_probs: list[float]
    """Log probability of `tokens`."""

    prompt_tokens: list[str]
    """The list of tokens sent to the system."""

    prompt_token_ids: list[int] | None = None
    """The list of token IDs sent to the system."""

    token_ids: list[int] = field(default_factory=list)
    """The list of token IDs generated by the system."""

    is_empty_response: bool | None = None

    contains_skip_token: bool | None = None

    @property
    def prompt(self) -> str:
        """The prompt sent to the system."""
        return "".join(self.prompt_tokens)


@dataclass
class CompletionResolution(dataclasses_json.DataClassJsonMixin):
    """Represents the resolution of a completion request."""

    accepted: bool
    """Whether the completion was accepted."""

    timestamp: datetime
    """The timestamp of the resolution event."""


@dataclass
class CompletionFeedback(dataclasses_json.DataClassJsonMixin):
    """Represents feedback received for a completion."""

    class Rating(enum.Enum):
        """The rating of the completion."""

        UNSET = 0
        """The completion is neutral or unset."""

        POSITIVE = 1
        """The completion is positive."""

        NEGATIVE = 2
        """The completion is negative."""

    rating: Rating
    """The rating provided."""

    note: str
    """Feedback note."""

    timestamp: datetime
    """The timestamp of the feedback event."""


@dataclass
class InferenceResponse(dataclasses_json.DataClassJsonMixin):
    """Represents a single inference response."""

    timestamp: datetime
    """The timestamp of the inference response."""

    tokens: list[str]
    """The list of tokens generated by the system."""

    token_log_probs: list[float]
    """Log probability of `tokens`."""

    token_ids: list[int]
    """The list of token IDs generated by the system."""


@dataclass
class CompletionDatum(dataclasses_json.DataClassJsonMixin):
    """Represents a full completion event with its request, response and resolution."""

    request_id: str
    """The request ID of the completion event."""

    user_id: str
    """The user ID of the user who requested the completion."""

    request: CompletionRequest
    """The request that was completed."""

    response: CompletionResponse
    """The completion response."""

    resolution: Optional[CompletionResolution] = None
    """How the completion was resolved."""

    feedback: Optional[CompletionFeedback] = None
    """Any feedback provided for the completion."""

    inference_response: Optional[InferenceResponse] = None
    """The inference response."""

    user_agent: str | None = None
    """The user agent of the user who requested the completion."""

    session_id: str | None = None
    """The session ID of the user who requested the completion."""
