"""Tests for base.datasets.next_edit."""

from datetime import datetime
import json
from pathlib import Path

from google.protobuf.json_format import ParseDict

from base.datasets.gcs_blob_cache import PathAndContent
from base.datasets.next_edit import (
    get_current_version_of_files,
    DiffSpan,
    NextEditGeneration,
    NextEditResponse,
    NextEditResult,
    NextEditSuggestion,
    ReplacementText,
    RetrievalChunk,
    ScoredFileHunk,
    Tokenization,
)
from base.datasets.next_edit_conversion import from_next_edit_response_protos
from base.diff_utils.apply_replacements_to_files import (
    FileReplacementError,
    FileReplacementErrorType,
)
from base.diff_utils.diff_utils import File
from base.ranges.range_types import CharRange
from services.request_insight import request_insight_pb2


def test_get_current_version_of_files():
    original_file1 = File(path="path1", contents="old text 1")
    final_file1 = File(path="path1", contents="old new text text 1")

    original_file2 = File(path="path2", contents="old text 2")
    final_file2 = File(path="path2", contents="old new text text 2")

    original_file3 = File(path="path3", contents="old text 3")

    replacements = [
        ReplacementText(
            blob_name=original_file1.blob_name,
            expected_blob_name=final_file1.blob_name,
            path="path1",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=False,
        ),
        ReplacementText(
            blob_name=original_file2.blob_name,
            expected_blob_name=final_file2.blob_name,
            path="path2",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=True,
        ),
    ]

    blob_name_to_path_and_content = {
        original_file1.blob_name: PathAndContent(
            Path(original_file1.path), original_file1.contents
        ),
        original_file2.blob_name: PathAndContent(
            Path(original_file2.path), original_file2.contents
        ),
        original_file3.blob_name: PathAndContent(
            Path(original_file3.path), original_file3.contents
        ),
    }

    result = get_current_version_of_files(replacements, blob_name_to_path_and_content)

    assert result == {final_file1.blob_name: final_file1}


def test_build_next_edit_response():
    """The fields in this test are the ones stored in RI.
    The field values have been simplified for test purposes.
    """

    retrieved_location = """\
{"retrieved_locations":[{\
    "blob_name":"location_blob_name",\
    "char_end":40,\
    "origin":"location_retriever",\
    "path":"test.py",\
    "text":"def"\
}]}"""

    generation = """\
{"generation":[{\
        "generation_id":"gen_id",\
        "generation_output":{\
            "log_probs":[0,0],\
            "offsets":[0,13],\
            "text":"<|no_change|><|endoftext|>",\
            "token_ids":[49175,0]\
        },\
        "generation_prompt":{\
            "offsets":[0],\
            "text":"<pr_diff>",\
            "token_ids":[24]\
        },\
        "location_chunk":{\
            "blob_name":"location_blob_name",\
            "char_end":40,\
            "origin":"location_retriever",\
            "path":"test.py",\
            "text":"def"\
        }\
    }\
]}\
"""

    suggestion = """\
{"suggestions":[\
    {\
        "generation_id":"gen_id",\
        "result":{\
            "suggested_edit":{\
                "blob_name":"location_blob_name",\
                "char_end":40,\
                "editing_score":1,\
                "existing_code":"def",\
                "path":"test.py",\
                "suggested_code":"def",\
                "suggestion_id":"sug_id",\
                "diff_spans":[\
                    {\
                        "original":{\
                            "start":0,\
                            "stop":3\
                        },\
                        "updated":{\
                            "start":0,\
                            "stop":3\
                        }\
                    }\
                ]\
            }\
        }\
    }\
]}"""
    other_suggestion = """\
{"suggestions":[\
    {\
        "generation_id":"other_gen_id",\
        "result":{\
            "suggested_edit":{\
                "blob_name":"other_location_blob_name",\
                "char_end":42,\
                "editing_score":1,\
                "existing_code":"other_def",\
                "path":"other_test.py",\
                "suggested_code":"other_def",\
                "suggestion_id":"other_sug_id",\
                "diff_spans":[\
                    {\
                        "original":{\
                            "start":0,\
                            "stop":9\
                        },\
                        "updated":{\
                            "start":0,\
                            "stop":9\
                        }\
                    }\
                ]\
            }\
        }\
    }\
]}"""

    response_jsons: list[dict] = [
        json.loads(x.replace(" ", ""))
        for x in [suggestion, retrieved_location, generation, other_suggestion]
    ]
    timestamps: list[datetime] = [
        datetime(2024, 1, 1, 0, 0, 1),
        datetime(2024, 1, 1, 0, 0, 2),
        datetime(2024, 1, 1, 0, 0, 3),
    ]
    response_protos = [
        ParseDict(response_json, request_insight_pb2.RINextEditResponse())
        for response_json in response_jsons
    ]
    response = from_next_edit_response_protos(response_protos, timestamps)

    expected_response = NextEditResponse(
        retrieved_locations=[
            RetrievalChunk(
                blob_name="location_blob_name",
                char_end=40,
                origin="location_retriever",
                path="test.py",
                text="def",
                chunk_index=0,
                score=0.0,
                char_offset=0,
            )
        ],
        generations=[
            NextEditGeneration(
                generation_id="gen_id",
                retrieved_chunks=[],
                generation_prompt=Tokenization(
                    token_ids=[24],
                    offsets=[0],
                    text="<pr_diff>",
                    log_probs=[],
                ),
                generation_output=Tokenization(
                    token_ids=[49175, 0],
                    offsets=[0, 13],
                    text="<|no_change|><|endoftext|>",
                    log_probs=[0.0, 0.0],
                ),
                location_chunk=RetrievalChunk(
                    blob_name="location_blob_name",
                    char_end=40,
                    origin="location_retriever",
                    path="test.py",
                    chunk_index=0,
                    score=0.0,
                    char_offset=0,
                    text="def",
                ),
                post_process_result=NextEditGeneration.PostProcessResult.NOOP,
                editing_score=0.0,
            )
        ],
        suggestions=[
            NextEditSuggestion(
                generation_id="gen_id",
                description_prompt=Tokenization(
                    token_ids=[],
                    offsets=[],
                    text="",
                    log_probs=[],
                ),
                description_output=Tokenization(
                    token_ids=[],
                    offsets=[],
                    text="",
                    log_probs=[],
                ),
                result=NextEditResult(
                    suggested_edit=ScoredFileHunk(
                        path="test.py",
                        blob_name="location_blob_name",
                        char_start=0,
                        char_end=40,
                        existing_code="def",
                        suggested_code="def",
                        truncation_char=0,
                        change_description="",
                        diff_spans=[DiffSpan(CharRange(0, 3), CharRange(0, 3))],
                        localization_score=0.0,
                        editing_score=1.0,
                        editing_score_threshold=0.0,
                        suggestion_id="sug_id",
                    ),
                    unknown_blob_names=[],
                    checkpoint_not_found=False,
                ),
                suggestion_order=0,
            ),
            NextEditSuggestion(
                generation_id="other_gen_id",
                description_prompt=Tokenization(
                    token_ids=[],
                    offsets=[],
                    text="",
                    log_probs=[],
                ),
                description_output=Tokenization(
                    token_ids=[],
                    offsets=[],
                    text="",
                    log_probs=[],
                ),
                result=NextEditResult(
                    suggested_edit=ScoredFileHunk(
                        path="other_test.py",
                        blob_name="other_location_blob_name",
                        char_start=0,
                        char_end=42,
                        existing_code="other_def",
                        suggested_code="other_def",
                        truncation_char=0,
                        change_description="",
                        diff_spans=[DiffSpan(CharRange(0, 9), CharRange(0, 9))],
                        localization_score=0.0,
                        editing_score=1.0,
                        editing_score_threshold=0.0,
                        suggestion_id="other_sug_id",
                    ),
                    unknown_blob_names=[],
                    checkpoint_not_found=False,
                ),
                suggestion_order=0,
            ),
        ],
        timestamp=datetime(2024, 1, 1, 0, 0, 3),
    )

    assert response == expected_response


def test_skip_validation_true():
    original_file1 = File(path="path1", contents="old text 1")
    final_file1 = File(path="path1", contents="old new text text 1")

    original_file2 = File(path="path2", contents="old text 2")

    original_file3 = File(path="path3", contents="old text 3")

    replacements = [
        ReplacementText(
            blob_name=original_file1.blob_name,
            expected_blob_name="",
            path="path1",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=False,
        ),
        ReplacementText(
            blob_name=original_file2.blob_name,
            expected_blob_name="",
            path="path2",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=True,
        ),
    ]

    blob_name_to_path_and_content = {
        original_file1.blob_name: PathAndContent(
            Path(original_file1.path), original_file1.contents
        ),
        original_file2.blob_name: PathAndContent(
            Path(original_file2.path), original_file2.contents
        ),
        original_file3.blob_name: PathAndContent(
            Path(original_file3.path), original_file3.contents
        ),
    }

    result = get_current_version_of_files(
        replacements, blob_name_to_path_and_content, skip_validation=True
    )

    assert result == {final_file1.blob_name: final_file1}


def test_skip_validation_false():
    original_file1 = File(path="path1", contents="old text 1")

    original_file2 = File(path="path2", contents="old text 2")

    original_file3 = File(path="path3", contents="old text 3")
    final_file3 = FileReplacementError(
        file_blob_name=original_file3.blob_name,
        file_path=original_file3.path,
        error_type=FileReplacementErrorType.MISSING_EXPECTED_BLOB_NAME,
    )

    original_file4 = File(path="path4", contents="old text 4")
    final_file4 = FileReplacementError(
        file_blob_name=original_file4.blob_name,
        file_path=original_file4.path,
        error_type=FileReplacementErrorType.PATH_MISMATCH,
    )

    original_file5 = File(path="path5", contents="old text 5")
    final_file5 = FileReplacementError(
        file_blob_name=original_file5.blob_name,
        file_path=original_file5.path,
        error_type=FileReplacementErrorType.INCONSISTENT_BLOB_NAME,
    )

    original_file6 = File(path="path6", contents="old text 6")
    final_file6 = FileReplacementError(
        file_blob_name=original_file6.blob_name,
        file_path=original_file6.path,
        error_type=FileReplacementErrorType.RECONSTRUCTION_MISMATCH,
    )

    replacements = [
        ReplacementText(
            blob_name=original_file1.blob_name,
            expected_blob_name="",
            path="path1",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=True,
        ),
        ReplacementText(
            blob_name=original_file3.blob_name,
            expected_blob_name="",
            path="path3",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=False,
        ),
        ReplacementText(
            blob_name=original_file4.blob_name,
            expected_blob_name="wrong",
            path="path-other",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=False,
        ),
        ReplacementText(
            blob_name=original_file5.blob_name,
            expected_blob_name="foo",
            path="path5",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=False,
        ),
        ReplacementText(
            blob_name=original_file5.blob_name,
            expected_blob_name="bar",
            path="path5",
            crange=CharRange(7, 8),
            replacement_text=" new text ",
            present_in_blob=False,
        ),
        ReplacementText(
            blob_name=original_file6.blob_name,
            expected_blob_name="wrong",
            path="path6",
            crange=CharRange(3, 4),
            replacement_text=" new text ",
            present_in_blob=False,
        ),
    ]

    blob_name_to_path_and_content = {
        original_file1.blob_name: PathAndContent(
            Path(original_file1.path), original_file1.contents
        ),
        original_file2.blob_name: PathAndContent(
            Path(original_file2.path), original_file2.contents
        ),
        original_file3.blob_name: PathAndContent(
            Path(original_file3.path), original_file3.contents
        ),
        original_file4.blob_name: PathAndContent(
            Path(original_file4.path), original_file4.contents
        ),
        original_file5.blob_name: PathAndContent(
            Path(original_file5.path), original_file5.contents
        ),
        original_file6.blob_name: PathAndContent(
            Path(original_file6.path), original_file6.contents
        ),
    }

    result = get_current_version_of_files(
        replacements, blob_name_to_path_and_content, skip_validation=False
    )

    assert result == {
        original_file3.blob_name: final_file3,
        original_file4.blob_name: final_file4,
        original_file5.blob_name: final_file5,
        original_file6.blob_name: final_file6,
    }
