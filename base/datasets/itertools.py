import itertools
from collections.abc import Iterable
from typing import TypeVar, Callable

T = TypeVar("T")


# NOTE(jeff): itertools.batched is only available in Python 3.12+, so we need to
# implement our own.
def batched(iterable: Iterable[T], n: int) -> Iterable[tuple[T, ...]]:
    """Batch data into tuples of length n. The last batch may be shorter."""
    # batched('ABCDEFG', 3) --> ABC DEF G
    if n < 1:
        raise ValueError("n must be at least one")
    it = iter(iterable)
    while batch := tuple(itertools.islice(it, n)):
        yield batch


AT = TypeVar("AT")
BT = TypeVar("BT")


# TODO(jeff): currently stolen from base.static_analysis.common, we should deduplicate
# Also, renamed keyfunc -> key to be more consistent with itertools.groupby
def groupby(iterable: Iterable[AT], key: Callable[[AT], BT]) -> dict[BT, list[AT]]:
    """Group a sequence of items by a key function into a dict.

    NOTE: This has different behavior from itertools.groupby. Namely, itertools only
    groups consecutive items with the same key. This function groups all items with the
    same key (which means we have to return a dict and not an iterable).

    Example:
    >>> groupby([1, 2, 3, 4, 5], lambda x: x % 2)
    {1: [1, 3, 5], 0: [2, 4]}
    """
    groups = dict[BT, list[AT]]()
    for item in iterable:
        k = key(item)
        groups.setdefault(k, []).append(item)
    return groups
