#!/bin/bash
# Installs the required depenencies for base.datasets.
# This is currently just responsible for copying the service protos to the right
# location. This script is mainly meant to be run when using this library in the
# research codebase.
#
# Usage: `bazel run //base/datasets:install`.
#

if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY not set (run with bazel)" >&2
	exit 1
fi

OUTDIR="$BUILD_WORKSPACE_DIRECTORY"

echo "Copying generated proto files."
mkdir -p $OUTDIR
for fname in $(find . -iname '*_pb2.py' -o -iname '*_pb2.pyi'); do
	# ignore everything not in services or base, e.g. third_party/proto/google
	# as these should already be pip installed via requirements.txt
	if [[ $fname != "./services"* ]] && [[ $fname != "./base"* ]]; then
		continue
	fi
	dstdir="$OUTDIR/$(dirname $fname)"
	mkdir -p "$dstdir"
	echo "Copying $fname..."
	cp --remove-destination "$fname" "$dstdir"
done
echo "Finish install for base/datasets."
