from hashlib import sha256
from base.datasets.user_event import TextEditEvent
from base.static_analysis.common import replace_str

DocPath = str
RequestId = str


def validate_text_edit_event(
    event: TextEditEvent,
    content: str,
    default: bool = True,
) -> bool:
    """Validates that the text edit event is valid for the given content.

    `event`: The text edit event to validate.
    `content`: The content of the file at the time of the event.
    `default`: What to return if the event does not have validation information.
    By default, events without validation information are deemed valid to allow
    the usage of data generated before the validation information was added.

    Returns True if the event is valid, False otherwise.
    """

    # Do not process data without validation information
    if event.after_changes_hash is None or len(event.hash_char_ranges) == 0:
        return default

    # Reconstruct file after event
    content = replace_str(content, [(x.crange, x.text) for x in event.content_changes])

    # Get changed content and hash it
    hashed_content = "".join([content[x.to_slice()] for x in event.hash_char_ranges])
    change_hash = sha256(hashed_content.encode()).hexdigest()

    return change_hash == event.after_changes_hash
