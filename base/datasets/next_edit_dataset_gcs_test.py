"""Tests for base.datasets.next_edit_dataset_gcs."""

import dataclasses
import io
from datetime import datetime
from unittest.mock import Magic<PERSON>ock

import pytest
from google.protobuf import timestamp_pb2

from base.datasets.gcs_blob_cache import BlobCache, CheckpointCache
from base.datasets.gcs_client import Request
from base.datasets.next_edit_dataset_gcs import (
    Fields<PERSON>ilter,
    NextEditDataFromGCS,
    NextEditDataset,
    NextEditDatum,
    _Row,
)
from services.request_insight import request_insight_pb2


def pb2_from_datetime(timestamp: datetime):
    """Converts a datetime to a protobuf timestamp."""
    pb2 = timestamp_pb2.Timestamp()
    pb2.FromDatetime(timestamp)
    return pb2


@pytest.fixture
def next_edit_dataset_gcs_row(
    next_edit_datum,
    next_edit_request_proto,
    next_edit_response_proto,
    next_edit_request_metadata_proto,
):
    """A NextEditDatasetGCS row."""
    return _Row(
        request_id=next_edit_datum.request_id,
        request_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(next_edit_datum.request.timestamp),
            next_edit_host_request=next_edit_request_proto,
        ),
        response_pbs=[
            request_insight_pb2.RequestEvent(
                time=pb2_from_datetime(next_edit_datum.response.timestamp),
                next_edit_host_response=next_edit_response_proto,
            )
        ],
        request_metadata_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(next_edit_datum.request.timestamp),
            request_metadata=next_edit_request_metadata_proto,
        ),
    )


@pytest.fixture
def gcs_client_request(next_edit_dataset_gcs_row):
    """A GCSClient request."""
    return Request(
        request_id=next_edit_dataset_gcs_row.request_id,
        events=[
            next_edit_dataset_gcs_row.request_pb,
            *next_edit_dataset_gcs_row.response_pbs,
            next_edit_dataset_gcs_row.request_metadata_pb,
        ],
    )


def test_row_from_request(next_edit_dataset_gcs_row: _Row, gcs_client_request: Request):
    """Test that we can create a row from a request."""
    row = _Row.from_request(gcs_client_request)
    assert row == next_edit_dataset_gcs_row


def test_row_from_request_aggregated(
    next_edit_dataset_gcs_row: _Row, gcs_client_request: Request
):
    """Test that we can create a row from a request with multiple events of the same type."""
    dupe_requests: list[request_insight_pb2.RequestEvent] = []
    for event in gcs_client_request.events:
        new_event = request_insight_pb2.RequestEvent()
        new_event.CopyFrom(event)
        new_event.time.seconds -= 1
        dupe_requests.append(new_event)
    request = dataclasses.replace(
        gcs_client_request, events=gcs_client_request.events + dupe_requests
    )

    dupe_response_pbs: list[request_insight_pb2.RequestEvent] = []
    assert next_edit_dataset_gcs_row.response_pbs is not None
    for event in next_edit_dataset_gcs_row.response_pbs:
        new_event = request_insight_pb2.RequestEvent()
        new_event.CopyFrom(event)
        new_event.time.seconds -= 1
        dupe_response_pbs.append(new_event)
    expected_row = dataclasses.replace(
        next_edit_dataset_gcs_row,
        response_pbs=next_edit_dataset_gcs_row.response_pbs + dupe_response_pbs,
    )

    row = _Row.from_request(request)
    assert row == expected_row


def test_next_edit_data_from_gcs(
    gcs_client_request: Request,
    next_edit_datum: NextEditDatum,
    checkpoint_cache: CheckpointCache,
):
    """Test that we can create a NextEditDataFromGCS."""
    request_fetcher = MagicMock()
    request_fetcher.get_requests.return_value = [gcs_client_request]
    dataset = NextEditDataFromGCS(
        request_fetcher=request_fetcher,
        checkpoint_cache=checkpoint_cache,
        request_ids=[gcs_client_request.request_id],
        fields_filter=FieldsFilter(),
        fetcher_batch_size=64,
    )

    assert request_fetcher.get_requests.called_once_with(
        [gcs_client_request.request_id],
        request_event_names=frozenset(
            [
                "next_edit_host_request",
                "next_edit_host_response",
                "request_metadata",
            ]
        ),
        batch_size=64,
    )
    assert list(dataset) == [next_edit_datum]


def test_dump_load_data(
    next_edit_datum: NextEditDatum, checkpoint_cache: CheckpointCache
):
    """Test that we can dump and load data."""
    next_edit_datum2 = dataclasses.replace(
        next_edit_datum,
        request=dataclasses.replace(
            next_edit_datum.request,
            prefix="test-prefix-2",
            suffix="test-suffix-2",
        ),
    )
    data = [next_edit_datum, next_edit_datum2]
    dataset = NextEditDataset(data=data, blobs=checkpoint_cache)

    buffer = io.StringIO()
    dataset.dump_data(buffer)

    buffer.seek(0)
    data2 = NextEditDataset.load_data(buffer)

    assert data2 == data


def test_iter_data(next_edit_datum: NextEditDatum, blob_cache: BlobCache):
    """Test that we can iterate over data."""
    next_edit_datum2 = dataclasses.replace(
        next_edit_datum,
        request=dataclasses.replace(
            next_edit_datum.request,
            prefix="test-prefix-2",
            suffix="test-suffix-2",
        ),
    )
    data = [next_edit_datum, next_edit_datum2]
    dataset = NextEditDataset(data=data, blobs=blob_cache)

    blob_names = next_edit_datum.request.blob_names
    expected = [(d, dict(zip(blob_names, blob_cache.get(blob_names)))) for d in data]
    assert list(dataset) == expected
