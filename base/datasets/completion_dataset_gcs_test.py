"""Tests for base.datasets.completion_dataset_gcs."""

import dataclasses
import io
from datetime import datetime, timezone
from unittest.mock import Magic<PERSON>ock

import pytest
from google.protobuf import timestamp_pb2

from base.datasets.completion_dataset_gcs import (
    CompletionDataFromGCS,
    CompletionDataset,
    Fields<PERSON>ilter,
    Filters,
    _<PERSON>,
)
from base.datasets.gcs_client import Request
from services.request_insight import request_insight_pb2


def pb2_from_datetime(timestamp):
    pb2 = timestamp_pb2.Timestamp()
    pb2.FromDatetime(timestamp)
    return pb2


@pytest.fixture
def completion_dataset_gcs_row(
    completion_datum,
    completion_request_proto,
    completion_response_proto,
    completion_retrieval_response_protos,
    completion_feedback_proto,
    inference_response_proto,
    completion_request_metadata_proto,
):
    assert completion_datum.inference_response is not None
    assert completion_datum.resolution is not None
    assert completion_datum.feedback is not None
    return _Row(
        request_id=completion_datum.request_id,
        request_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(completion_datum.request.timestamp),
            completion_host_request=completion_request_proto,
        ),
        response_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(completion_datum.response.timestamp),
            completion_host_response=completion_response_proto,
        ),
        retrieval_pbs=[
            request_insight_pb2.RequestEvent(
                time=pb2_from_datetime(completion_datum.response.timestamp),
                retrieval_response=retrieval_response,
            )
            for retrieval_response in completion_retrieval_response_protos
        ],
        resolution_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(completion_datum.resolution.timestamp),
            completion_resolution=request_insight_pb2.CompletionResolution(
                accepted_idx=0 if completion_datum.resolution.accepted else -1,
            ),
        ),
        feedback_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(completion_datum.feedback.timestamp),
            completion_feedback=completion_feedback_proto,
        ),
        inference_response_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(completion_datum.inference_response.timestamp),
            inference_host_response=inference_response_proto,
        ),
        request_metadata_pb=request_insight_pb2.RequestEvent(
            time=pb2_from_datetime(completion_datum.request.timestamp),
            request_metadata=completion_request_metadata_proto,
        ),
    )


@pytest.fixture
def gcs_client_request(completion_dataset_gcs_row):
    return Request(
        request_id=completion_dataset_gcs_row.request_id,
        events=[
            completion_dataset_gcs_row.request_pb,
            completion_dataset_gcs_row.response_pb,
            *completion_dataset_gcs_row.retrieval_pbs,
            completion_dataset_gcs_row.resolution_pb,
            completion_dataset_gcs_row.feedback_pb,
            completion_dataset_gcs_row.inference_response_pb,
            completion_dataset_gcs_row.request_metadata_pb,
        ],
    )


def test_row_from_request(completion_dataset_gcs_row, gcs_client_request):
    row = _Row.from_request(gcs_client_request)
    assert row == completion_dataset_gcs_row


def test_row_from_request_aggregated(completion_dataset_gcs_row, gcs_client_request):
    dupe_requests: list[request_insight_pb2.RequestEvent] = []
    for event in gcs_client_request.events:
        new_event = request_insight_pb2.RequestEvent()
        new_event.CopyFrom(event)
        new_event.time.seconds -= 1
        dupe_requests.append(new_event)
    request = dataclasses.replace(
        gcs_client_request, events=gcs_client_request.events + dupe_requests
    )

    dupe_retrieval_pbs: list[request_insight_pb2.RequestEvent] = []
    for event in completion_dataset_gcs_row.retrieval_pbs:
        new_event = request_insight_pb2.RequestEvent()
        new_event.CopyFrom(event)
        new_event.time.seconds -= 1
        dupe_retrieval_pbs.append(new_event)
    expected_row = dataclasses.replace(
        completion_dataset_gcs_row,
        retrieval_pbs=completion_dataset_gcs_row.retrieval_pbs + dupe_retrieval_pbs,
    )

    row = _Row.from_request(request)
    assert row == expected_row


@pytest.mark.parametrize(
    "filters, expected",
    [
        (Filters([]), True),
        (Filters([], timestamp_begin=datetime(2024, 1, 1, tzinfo=timezone.utc)), True),
        (Filters([], timestamp_begin=datetime(2024, 1, 2, tzinfo=timezone.utc)), False),
        (Filters([], timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc)), True),
        (Filters([], timestamp_end=datetime(2024, 1, 1, tzinfo=timezone.utc)), False),
        (Filters([], accepted_completion=True), True),
        (Filters([], accepted_completion=False), False),
        (Filters([], model_names=["test-model"]), True),
        (Filters([], model_names=["other-model"]), False),
        (Filters([], min_completion_length=1), True),
        (Filters([], min_completion_length=100), False),
        (Filters([], with_feedback=True), True),
        (Filters([], human=True), True),
    ],
)
def test_passes_filter(filters, expected, completion_dataset_gcs_row):
    assert (
        CompletionDataFromGCS.passes_filter(filters, completion_dataset_gcs_row)
        == expected
    )


@pytest.mark.parametrize(
    "filters, expected",
    [
        (Filters([], timestamp_begin=datetime(2024, 1, 1, tzinfo=timezone.utc)), False),
        (Filters([], timestamp_begin=datetime(2024, 1, 2, tzinfo=timezone.utc)), False),
        (Filters([], timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc)), False),
        (Filters([], timestamp_end=datetime(2024, 1, 1, tzinfo=timezone.utc)), False),
        (Filters([], accepted_completion=True), False),
        (Filters([], accepted_completion=False), False),
        (Filters([], model_names=["test-model"]), False),
        (Filters([], model_names=["other-model"]), False),
        (Filters([], min_completion_length=1), False),
        (Filters([], min_completion_length=100), False),
        (Filters([], with_feedback=True), False),
        (Filters([], human=True), False),
    ],
)
def test_passes_filter_none(filters, expected, completion_dataset_gcs_row):
    row = dataclasses.replace(
        completion_dataset_gcs_row,
        request_pb=None,
        response_pb=None,
        retrieval_pbs=None,
        resolution_pb=None,
        inference_response_pb=None,
        feedback_pb=None,
        request_metadata_pb=None,
    )
    assert CompletionDataFromGCS.passes_filter(filters, row) == expected


@pytest.mark.parametrize(
    "user_agent, expected",
    [
        ("Augment-EvalHarness/0 (Regression Testing)", False),
        ("AugmentHealthCheck/0", False),
        ("api_proxy_client/0 (Python)", False),
        ("augment.info-chat/0", False),
        ("Augment.vscode-augment/0.230.0", True),
        ("augment.intellij/0.0.1", True),
    ],
)
def test_passes_filter_human(user_agent, expected, completion_dataset_gcs_row):
    row = dataclasses.replace(
        completion_dataset_gcs_row,
        request_metadata_pb=request_insight_pb2.RequestEvent(
            time=completion_dataset_gcs_row.request_metadata_pb.time,
            request_metadata=request_insight_pb2.RequestMetadata(
                user_agent=user_agent,
            ),
        ),
    )
    assert CompletionDataFromGCS.passes_filter(Filters([], human=True), row) == expected


def test_completion_data_from_gcs(
    gcs_client_request, completion_datum, simulated_file, checkpoint_cache
):
    request_fetcher = MagicMock()
    request_fetcher.get_requests.return_value = [gcs_client_request]
    dataset = CompletionDataFromGCS(
        request_fetcher=request_fetcher,
        checkpoint_cache=checkpoint_cache,
        filters=Filters([gcs_client_request.request_id]),
        fields_filter=FieldsFilter(),
        fetcher_batch_size=64,
    )

    assert request_fetcher.get_requests.called_once_with(
        [gcs_client_request.request_id],
        request_event_names=frozenset(
            [
                "completion_host_request",
                "completion_host_response",
                "request_metadata",
                "retrieval_response",
                "completion_resolution",
                "completion_feedback",
                "inference_host_response",
            ]
        ),
        batch_size=64,
    )

    # We don't reconstruct files, so use the original request prefix/suffix.
    assert completion_datum.request.position is not None
    completion_datum = dataclasses.replace(
        completion_datum,
        request=dataclasses.replace(
            completion_datum.request,
            blob_names=completion_datum.request.blob_names
            + [completion_datum.request.position.blob_name],
            prefix=simulated_file.request_prefix,
            suffix=simulated_file.request_suffix,
        ),
    )
    assert list(dataset) == [completion_datum]


def test_dump_load_data(completion_datum, checkpoint_cache):
    completion_datum2 = dataclasses.replace(
        completion_datum,
        request=dataclasses.replace(
            completion_datum.request,
            prefix="test-prefix-2",
            suffix="test-suffix-2",
        ),
    )
    data = [completion_datum, completion_datum2]
    dataset = CompletionDataset(data=data, blobs=checkpoint_cache)

    buffer = io.StringIO()
    dataset.dump_data(buffer)

    buffer.seek(0)
    data2 = CompletionDataset.load_data(buffer)

    assert data2 == data


def test_iter_data(completion_datum, blob_cache, simulated_file):
    completion_datum = dataclasses.replace(
        completion_datum,
        request=dataclasses.replace(
            completion_datum.request,
            blob_names=completion_datum.request.blob_names
            + [completion_datum.request.position.blob_name],
            prefix=simulated_file.request_prefix,
            suffix=simulated_file.request_suffix,
        ),
    )
    completion_datum2 = dataclasses.replace(
        completion_datum,
        request=dataclasses.replace(
            completion_datum.request,
            prefix="test-prefix-2",
            suffix="test-suffix-2",
        ),
    )
    data = [completion_datum, completion_datum2]
    dataset = CompletionDataset(data=data, blobs=blob_cache)

    blob_names = completion_datum.request.blob_names
    expected = [(d, dict(zip(blob_names, blob_cache.get(blob_names)))) for d in data]
    assert list(dataset) == expected
