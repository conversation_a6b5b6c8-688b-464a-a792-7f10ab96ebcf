"""Dataclasses for hindsight next edits."""

from dataclasses import dataclass

import dataclasses_json

from base.datasets.next_edit import (
    NextEditDatum,
    NextEditGroundTruth,
    NextEditRequest,
)
from base.diff_utils.diff_utils import File
from base.blob_names.python.blob_names import FilePath
from base.datasets.user_event import TextEditEvent


@dataclass
class HindsightNextEditDatum(dataclasses_json.DataClassJsonMixin):
    """Contains a next edit datum and a list of heuristically derived ground truths."""

    next_edit_datum: NextEditDatum
    """The next edit datum."""

    ground_truths: list[NextEditGroundTruth]
    """The heuristic ground truth text."""


@dataclass
class NextEditIntermediateType:
    request: NextEditRequest
    """The original next edit request."""

    future_events: list[TextEditEvent]
    """Future edits that happened after the request."""

    files_for_events: dict[FilePath, File]
    """Up-to-date files that are touched by future edit events or by replacement texts.

    This includes the currently active file, which is reconstructed from `request`.
    """

    session_id: str
    """The session ID of the next edit request. Convenient for grouping the data."""
