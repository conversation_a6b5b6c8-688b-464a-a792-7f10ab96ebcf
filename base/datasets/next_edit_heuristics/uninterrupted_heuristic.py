"""
This heuristic uses StrDiff to generate diffs and tracks the validity of changes using
interruptions.
"""

import dataclasses
import enum
import logging
import uuid

from collections import Counter
from dataclasses import dataclass
from itertools import chain
from typing import Iterable, Optional

from base.diff_utils.str_diff import line_diff, precise_char_diff
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.datasets.hindsight_next_edit_lib import validate_text_edit_event
from base.datasets.hindsight_lib import (
    changes_is_nondisjoint,
    changes_has_duplicate_points,
)
from base.datasets.next_edit import DiffSpan, NextEditGroundTruth
from base.datasets.user_event import ContentChange
from base.ranges import CharRange
from base.static_analysis.common import replace_str


DocPath = str
RequestId = str

logger = logging.getLogger(__name__)


@dataclass
class RangeState:
    crange: CharRange
    valid: bool
    last_updated_chars: int = 0


@dataclass
class LineLevelChange:
    old_range: CharRange
    offset: int
    num_added_chars: int
    range_id: uuid.UUID


class RangeTracker:
    """A data structure for tracking the text in character ranges within a document.

    The range tracker tracks ranges within a single document. Ranges are automatically
    added and merged as needed using `update_ranges`.
    """

    def __init__(self, max_interruption: int = 0):
        """
        Initializes the range tracker.

        `max_interruption`: the maximum number of characters that can be added to other
        ranges before a current range is considered interrupted.
        """
        # Ranges are exclusive (i.e. no ranges will touch)
        self.ranges: dict[uuid.UUID, RangeState] = dict[uuid.UUID, RangeState]()

        self.max_interruption: int = max_interruption

        self.num_chars = 0  # Tracking the number of characters added to the document

        # Tracking the range ID of the merged range for all component ranges
        self.range_maps: dict[uuid.UUID, uuid.UUID] = {}

    def track_range(
        self,
        range_id: uuid.UUID,
        crange: CharRange,
        num_added_chars: int,
        valid: bool,
    ):
        """Track the given text range.

        `range_id`: the unique identifier for the range.
        `crange`: the character range to track.
        `num_added_chars`: the number of characters changed by the change to the range.
        `valid`: whether the range is valid.
        """

        if range_id in self.ranges:
            raise ValueError(f"range_id {range_id} is already tracked")

        assert num_added_chars > 0
        self.num_chars += num_added_chars

        self.ranges[range_id] = RangeState(crange, valid, self.num_chars)

    def untrack_range(self, range_id: uuid.UUID) -> RangeState:
        """Untrack the given text range, as given by `range_id`."""
        if range_id not in self.ranges:
            raise ValueError(f"range_id {range_id} does not exist")

        range_state = self.ranges.pop(range_id)
        return range_state

    def __contains__(self, range_id: uuid.UUID) -> bool:
        """Check if the given `range_id` is tracked."""
        return range_id in self.ranges

    def get_range(self, range_id: uuid.UUID) -> RangeState:
        """Get the tracked text range, as given by `range_id`."""
        if range_id not in self.ranges:
            raise ValueError(f"range_id {range_id} does not exist")

        range_state = self.ranges[range_id]
        return range_state

    def find_nearby_ranges(
        self, crange: CharRange, max_distance: int
    ) -> dict[uuid.UUID, RangeState]:
        """
        Finds the text ranges near the given range.

        `crange`: the character range to find nearby ranges for.
        `max_distance`: the maximum character distance between ranges to consider.
        """
        return {
            range_id: range_state
            for range_id, range_state in self.ranges.items()
            if range_state.crange.distance(crange) <= max_distance
        }

    def find_touching_ranges(self, crange: CharRange) -> dict[uuid.UUID, RangeState]:
        """Finds the text ranges touching the given range (`crange`)."""
        return {
            range_id: range_state
            for range_id, range_state in self.ranges.items()
            if range_state.crange.touches(crange)
        }

    def update_ranges(
        self,
        agg_changes: list[LineLevelChange],
        max_distance: int = 0,
    ) -> Optional[Iterable[uuid.UUID]]:
        """
        Update tracked ranges in the given document given a list of content changes.

        The changes are treated as simultaneous. If the changes are ambiguous,
        e.g. not disjoint or same point range, an error is raised.

        If the changes cause an interruption, return None.

        `agg_changes`: a list of line-level changes.
        `max_distance`: the maximum character distance between ranges to merge.
        """
        # Backup changes
        ranges = self.ranges.copy()
        max_interruption = self.max_interruption
        num_chars = self.num_chars
        range_maps = self.range_maps.copy()

        # Applying the changes bottom up is equivalent to applying the changes simultaneously
        sorted_agg_changes = sorted(
            agg_changes, key=lambda x: x.old_range, reverse=True
        )

        new_ranges = []

        for llc in sorted_agg_changes:
            range_result = self._update_ranges(
                llc,
                max_distance,
            )
            # If the change causes an interruption, restore the original state and return None
            if range_result is None:
                self.ranges = ranges
                self.max_interruption = max_interruption
                self.num_chars = num_chars
                self.range_maps = range_maps
                return None
            new_ranges.append(range_result)

        return new_ranges

    def _update_ranges(
        self,
        llc: LineLevelChange,
        max_distance: int,
    ) -> Optional[uuid.UUID]:
        """
        Update tracked ranges in the given document given a content change.
        If the change causes an interruption, return None.

        `llc`: a line-level change.
        `max_distance`: the maximum character distance between ranges to merge.
        """
        old_range = llc.old_range
        offset = llc.offset
        num_added_chars = llc.num_added_chars
        range_id = llc.range_id

        nearby_ranges = self.find_nearby_ranges(old_range, max_distance)
        num_added_chars = (old_range.stop - old_range.start) + offset

        if len(nearby_ranges) == 0:
            # If there are no nearby ranges, we can create a new range.
            min_total = None
            max_total = None

            self.track_range(
                range_id,
                CharRange(start=old_range.start, stop=old_range.stop + offset),
                num_added_chars,
                True,
            )
        else:
            # Otherwise, we merge the ranges.
            min_total = nearby_ranges[
                min(nearby_ranges, key=lambda x: nearby_ranges[x].crange.start)
            ].crange.start
            min_total = min(min_total, old_range.start)

            max_total = nearby_ranges[
                max(nearby_ranges, key=lambda x: nearby_ranges[x].crange.stop)
            ].crange.stop
            max_total = max(max_total, old_range.stop)

            merged_range = CharRange(start=min_total, stop=max_total + offset)
            merged_valid = all(
                x.valid
                and x.last_updated_chars + self.max_interruption >= self.num_chars
                for x in nearby_ranges.values()
            )

            # If the new range wouldn't be valid, do not merge.
            if not merged_valid:
                return None

            # Remove the old ranges
            for old_range_id in nearby_ranges:
                self.range_maps[old_range_id] = range_id
                self.untrack_range(old_range_id)

            self.track_range(range_id, merged_range, num_added_chars, merged_valid)

        # Shift all subsequent ranges
        for _, range_state in self.ranges.items():
            crange = range_state.crange
            if old_range.stop <= crange.start:
                range_state.crange = range_state.crange.shifted(offset)

        return range_id


@dataclass
class NextEditStrDiffHeuristicResult:
    """A StrDiff-based heuristic result for next edits."""

    class ResultType(enum.Enum):
        """The result's status."""

        SUCCESS = 0
        """The result was successful."""
        VALIDATION = 1
        """Only some of the changes could be processed; a change could not be validated."""
        AMBIGUOUS_CHANGE = 2
        """Only some of the changes could be processed; a change was ambiguous."""
        INVALID_RANGE = 3
        """Only some of the changes could be processed; a change's ranges are out of bounds."""
        INTERRUPTION = 4
        """Some changes could not be processed; a change caused an interruption."""
        RANGE_TRACKING_ERROR = 5
        """The range tracker failed to properly map ranges to diff hunk mappings."""
        UNSORTABLE = 6
        """Could not sort diff hunks into a chronological order by change."""

    ground_truth: list[NextEditGroundTruth] = dataclasses.field(default_factory=list)
    """The ground truth."""
    result_type: ResultType = ResultType.SUCCESS
    """The result type."""
    result_detail: str = ""
    """The result details."""


DiffHunk = tuple[CharRange, CharRange]


@dataclass
class StrDiffHeuristic:
    """A ground truth heuristic that uses StrDiff to generate diffs and TextEditEvents to
    sequence the diffs."""

    max_interruption: int = 0
    """The maximum number of characters that can be added to a range before it is considered interrupted."""

    max_distance: int = 0
    """The maximum distance between ranges to merge."""

    require_chronological_order: bool = False
    """
    If true, if there is no way to order hunks in strict chronological edit order, the heuristic will return an error.
    Otherwise, the heuristic will return a best-fit.
    """

    error_on_missing_hunks: bool = True
    """
    If true, the heuristic will return an error if some hunks cannot be mapped to a range. Otherwise, the heuristic
    will return mappable hunks, even though some hunks may be skipped.
    """

    def __post_init__(self):
        self._starting_contents = {}
        self._file_contents = {}
        self._tracker: dict[str, RangeTracker] = {}

    def update_range_ids(
        self, ranges: list[tuple[str, list[uuid.UUID]]]
    ) -> list[tuple[str, list[uuid.UUID]]]:
        """
        Get the most up-to-date ranges being tracked.

        `ranges`: the original source of ranges by file and range id.
        """
        new_ranges = []
        for path, range_ids in ranges:
            new_range_ids = []
            for range_id in range_ids:
                curr_range_id = range_id
                while curr_range_id in self._tracker[path].range_maps:
                    curr_range_id = self._tracker[path].range_maps[curr_range_id]
                new_range_ids.append(curr_range_id)
            new_ranges.append((path, new_range_ids))
        return new_ranges

    def remove_unreliable_ranges(
        self,
        ranges: list[tuple[str, list[uuid.UUID]]],
        broken_file: Optional[str],
        broken_ranges: set[uuid.UUID],
    ) -> list[tuple[str, list[uuid.UUID]]]:
        """
        Remove unreliable ranges from the list. No ranges after the first unreliable one are included in
        the final output.

        `ranges`: the original source of ranges by file and range id.
        `broken_file`: the file with the broken ranges.
        `broken_ranges`: the ranges that were broken.
        """
        if broken_file is None:
            return ranges
        for idx, (file, range_ids) in enumerate(ranges):
            for range_id in range_ids:
                if file == broken_file and range_id in broken_ranges:
                    return ranges[:idx]
        return ranges

    def expand_range(self, doc_path: str, crange: CharRange) -> Optional[CharRange]:
        """
        Expand the range to from the beginning of the first line to the end of the last line
        that the char range is located in.

        `doc_path`: the path of the document.
        `crange`: the range to expand.
        """
        start = crange.start
        stop = crange.stop

        # Check for invalid ranges
        if start > len(self._file_contents[doc_path]) or stop > len(
            self._file_contents[doc_path]
        ):
            return None
        if stop < start:
            return None
        if start < 0 or stop < 0:
            return None

        # Find beginning of first line and end of last line.
        while start > 0 and self._file_contents[doc_path][start - 1] != "\n":
            start -= 1

        while (
            stop < len(self._file_contents[doc_path])
            and self._file_contents[doc_path][stop] != "\n"
        ):
            stop += 1

        return CharRange(start, stop + 1)

    def chunk_content_changes(
        self, old_text: str, changes: list[ContentChange]
    ) -> list[ContentChange]:
        """
        Chunk the content changes into smaller changes.

        `old_text`: the old text.
        `changes`: the changes to chunk.
        """
        new_changes = []
        for change in changes:
            old = old_text[change.crange.to_slice()]
            new = change.text
            for start, end in precise_char_diff(old, new).span_ranges():
                if old[start.to_slice()] == new[end.to_slice()]:
                    continue
                new_range = CharRange(
                    change.crange.start + start.start, change.crange.start + start.stop
                )
                new_changes.append(ContentChange(new[end.to_slice()], new_range))
        return new_changes

    def verify_lexicographic_order(
        self,
        diff_hunks_by_range: list[tuple[tuple[str, DiffHunk], tuple[int, int]]],
    ):
        """
        Verify that the diff hunks are in lexicographic order.

        `diff_hunks_by_range`: the diff hunks by range.
        """
        for idx, (_, (start, _)) in enumerate(diff_hunks_by_range):
            if idx > 0:
                _, prev_end = diff_hunks_by_range[idx - 1][1]
                if prev_end > start:
                    return False
        return True

    def generate(
        self,
        data: NextEditIntermediateType,
    ) -> NextEditStrDiffHeuristicResult:
        """
        Generate the ground truth for the given data.

        `data`: the data to generate the ground truth for.
        """
        # Initialization
        self._starting_contents = {
            x.path: x.contents for x in data.files_for_events.values()
        }
        self._file_contents = self._starting_contents.copy()
        self._tracker = {
            x.path: RangeTracker(self.max_interruption)
            for x in data.files_for_events.values()
        }

        future_events = sorted(data.future_events, key=lambda x: x.time)
        ranges: list[
            tuple[str, list[uuid.UUID]]
        ] = []  # Contains the generated ranges for each event
        result_type = NextEditStrDiffHeuristicResult.ResultType.SUCCESS
        result_detail = ""

        for idx, event in enumerate(future_events):
            if event.file_path not in self._file_contents:
                logger.debug(
                    "Skipping, no contents for file %s requested by event idx %s",
                    event.file_path,
                    idx,
                )
                continue

            if not validate_text_edit_event(
                event, self._file_contents[event.file_path]
            ):
                logger.info("Returning early, validation error at event idx %s", idx)
                result_type = NextEditStrDiffHeuristicResult.ResultType.VALIDATION
                result_detail = f"Validation error at event index {idx}"
                break

            content_changes = self.chunk_content_changes(
                self._file_contents[event.file_path], event.content_changes
            )

            if len(content_changes) == 0:
                logger.debug(
                    "Skipping, no content changes to process at event idx %s", idx
                )
                continue

            if changes_is_nondisjoint(content_changes) or changes_has_duplicate_points(
                content_changes
            ):
                logger.info("Returning early, ambiguous changes at event idx %s", idx)
                result_type = NextEditStrDiffHeuristicResult.ResultType.AMBIGUOUS_CHANGE
                result_detail = f"Ambiguous changes at event index {idx}"
                break

            line_level_offsets: Counter[CharRange] = Counter()
            line_level_added_chars: Counter[CharRange] = Counter()
            for content_change in content_changes:
                expanded = self.expand_range(event.file_path, content_change.crange)
                if expanded is None:
                    logger.info(
                        "Returning early, invalid range for change in event idx %s", idx
                    )
                    result_type = (
                        NextEditStrDiffHeuristicResult.ResultType.INVALID_RANGE
                    )
                    result_detail = f"Invalid range at event index {idx}"
                    break
                line_level_offsets[expanded] += len(content_change.text) - len(
                    content_change.crange
                )
                # We conservatively assume that the changed text was deleted and replaced in full
                line_level_added_chars[expanded] += len(content_change.text) + len(
                    content_change.crange
                )

            agg_changes = [
                LineLevelChange(
                    range_,
                    line_level_offsets[range_],
                    line_level_added_chars[range_],
                    uuid.uuid4(),
                )
                for range_ in line_level_offsets.keys()
            ]

            # Break if one of the ranges couldn't be expanded.
            if result_type != NextEditStrDiffHeuristicResult.ResultType.SUCCESS:
                break

            new_ranges = self._tracker[event.file_path].update_ranges(
                agg_changes,
                max_distance=self.max_distance,
            )

            # If one of the changes causes a range interruption, stop processing events
            if new_ranges is None:
                logger.info("Returning early, range interruption at event idx %s", idx)
                result_type = NextEditStrDiffHeuristicResult.ResultType.INTERRUPTION
                result_detail = f"Interrupted at event index {idx}"
                broken_ranges = set(
                    chain.from_iterable(
                        [
                            self._tracker[event.file_path]
                            .find_touching_ranges(llc.old_range)
                            .keys()
                            for llc in agg_changes
                        ]
                    )
                )
                # Some ranges may have merged into others; update ranges
                ranges = self.update_range_ids(ranges)
                # If we cannot trust the correctness of certain ranges, remove them and all future
                # ranges from the results
                ranges = self.remove_unreliable_ranges(
                    ranges, event.file_path, broken_ranges
                )
                break

            ranges.append((event.file_path, list(new_ranges)))

            self._file_contents[event.file_path] = replace_str(
                self._file_contents[event.file_path],
                [(x.crange, x.text) for x in content_changes],
            )

        # Some ranges may have merged into others; update ranges
        ranges = self.update_range_ids(ranges)

        # Get the change indices that each range is associated with.
        ranges_to_order: dict[tuple[str, uuid.UUID], list[int]] = {}
        for idx, (path, range_ids) in enumerate(ranges):
            for range_id in range_ids:
                if (path, range_id) not in ranges_to_order:
                    ranges_to_order[(path, range_id)] = []
                ranges_to_order[(path, range_id)].append(idx)

        diff_hunks: dict[tuple[str, DiffHunk], list[int]] = {}

        # Get diff hunks
        for doc_path in self._starting_contents.keys():
            diff = line_diff(
                self._starting_contents[doc_path], self._file_contents[doc_path]
            )
            diff_spans = diff.span_ranges()

            for start, end in diff_spans:
                # If the hunk mapping is the same from start to end, we can ignore it.
                if (
                    self._starting_contents[doc_path][start.to_slice()]
                    == self._file_contents[doc_path][end.to_slice()]
                ):
                    continue
                nearby_ranges = self._tracker[doc_path].find_touching_ranges(end)
                if len(nearby_ranges) == 0:
                    # This indicates that the range tracking algorithm and string diff algorithm do not converge
                    # on the same hunks.
                    logger.info("Range tracking error: missing hunks")
                    if self.error_on_missing_hunks:
                        return NextEditStrDiffHeuristicResult(
                            result_type=NextEditStrDiffHeuristicResult.ResultType.RANGE_TRACKING_ERROR,
                            result_detail="The range tracking algorithm could not map all diff hunks to ranges.",
                        )
                is_valid = all([x.valid for x in nearby_ranges.values()])
                if is_valid:  # If all of the ranges nearby are valid, add the hunks
                    for range_id in nearby_ranges:
                        if (doc_path, range_id) not in ranges_to_order:
                            continue
                        if (doc_path, (start, end)) not in diff_hunks:
                            diff_hunks[(doc_path, (start, end))] = []
                        diff_hunks[(doc_path, (start, end))].extend(
                            ranges_to_order[(doc_path, range_id)]
                        )

        # Sort diff hunks by the order that the edit events change them
        diff_hunks_by_range = {k: (min(v), max(v)) for k, v in diff_hunks.items()}
        diff_hunks_by_range = sorted(diff_hunks_by_range.items(), key=lambda x: x[1])

        # Assemble results
        results: list[NextEditGroundTruth] = []
        for (path, (start, end)), _ in diff_hunks_by_range:
            results.append(
                NextEditGroundTruth(
                    path=path,
                    diff_span=DiffSpan(
                        original=CharRange(start.start, start.stop),
                        updated=CharRange(end.start, end.stop),
                    ),
                    crange=end,
                    old_text=self._starting_contents[path][start.to_slice()],
                    new_text=self._file_contents[path][end.to_slice()],
                )
            )

        if not self.verify_lexicographic_order(diff_hunks_by_range):
            logger.info(
                "Returning early, could not sort diff hunks in chronological order"
            )
            result_detail = "Diffs cannot be sorted in chronological order"
            ground_truth = []
            if self.require_chronological_order:
                ground_truth = results
            return NextEditStrDiffHeuristicResult(
                ground_truth=ground_truth,
                result_type=NextEditStrDiffHeuristicResult.ResultType.UNSORTABLE,
                result_detail=result_detail,
            )

        return NextEditStrDiffHeuristicResult(
            ground_truth=results,
            result_type=result_type,
            result_detail=result_detail,
        )
