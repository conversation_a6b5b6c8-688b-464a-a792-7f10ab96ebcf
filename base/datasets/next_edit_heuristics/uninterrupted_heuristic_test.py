"""Tests for base.datasets.next_edit_heuristics.uninterrupted_heuristic"""

from datetime import datetime, timezone

import pytest

from base.diff_utils.diff_utils import File
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.datasets.next_edit import (
    DiffSpan,
    NextEditGroundTruth,
    NextEditRequest,
    VCSChange,
)
from base.datasets.next_edit_heuristics.uninterrupted_heuristic import (
    NextEditStrDiffHeuristicResult,
    StrDiffHeuristic,
)
from base.datasets.user_event import ContentChange, TextEditEvent
from base.ranges import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_uninterrupted_heuristic_basic():
    """Uninterrupted heuristic smoke test."""
    session_id = "session-id"
    user_id = "user-id"
    tenant = "tenant"

    foo_file = File("foo.py", "a\nbc")
    bar_file = File("bar.py", "x\nyz")

    data = NextEditIntermediateType(
        files_for_events={
            foo_file.blob_name: foo_file,
            bar_file.blob_name: bar_file,
        },
        future_events=[
            TextEditEvent(
                session_id=session_id,
                user_id=user_id,
                tenant=tenant,
                time=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
                file_path=foo_file.path,
                content_changes=[
                    ContentChange(
                        text="a",
                        crange=CharRange(0, 0),
                    )
                ],
            ),
            TextEditEvent(
                session_id=session_id,
                user_id=user_id,
                tenant=tenant,
                time=datetime(2024, 1, 1, 0, 0, 1, tzinfo=timezone.utc),
                file_path=foo_file.path,
                content_changes=[
                    ContentChange(
                        text="bb",
                        crange=CharRange(0, 2),
                    )
                ],
            ),
        ],
        request=NextEditRequest(
            model_name="",
            sequence_id=0,
            lang="",
            instruction="",
            recent_changes=[],
            vcs_change=VCSChange(working_directory_changes=[]),
            path=bar_file.path,
            blob_name=bar_file.blob_name,
            selection_begin_char=0,
            selection_end_char=0,
            prefix="",
            selected_text="",
            suffix=bar_file.contents,
            diagnostics=[],
            mode=NextEditRequest.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,
            scope=NextEditRequest.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,
            change_probability_override=0.0,
            edit_events=[],
            blob_names=[foo_file.blob_name, bar_file.blob_name],
            blocked_locations=[],
            restrict_to_file=False,
            timestamp=datetime(2024, 1, 1, tzinfo=timezone.utc),
            request_id="",
        ),
        session_id=session_id,
    )

    result = StrDiffHeuristic().generate(data)

    assert len(result.ground_truth) == 1

    assert result.ground_truth[0] == NextEditGroundTruth(
        path=foo_file.path,
        diff_span=DiffSpan(original=CharRange(0, 2), updated=CharRange(0, 3)),
        crange=CharRange(0, 3),
        old_text="a\n",
        new_text="bb\n",
    )


def test_uninterrupted_heuristic_advanced():
    """Uninterrupted heuristic test of interruptions and finer hunk behavior."""
    session_id = "session-id"
    user_id = "user-id"
    tenant = "tenant"

    foo_file = File("foo.py", "a\n\nbc")
    bar_file = File("bar.py", "x\nyz")

    data = NextEditIntermediateType(
        files_for_events={
            foo_file.blob_name: foo_file,
            bar_file.blob_name: bar_file,
        },
        future_events=[
            TextEditEvent(
                session_id=session_id,
                user_id=user_id,
                tenant=tenant,
                time=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
                file_path=foo_file.path,
                content_changes=[
                    ContentChange(
                        text="a",
                        crange=CharRange(0, 0),
                    )
                ],
            ),
            TextEditEvent(
                session_id=session_id,
                user_id=user_id,
                tenant=tenant,
                time=datetime(2024, 1, 1, 0, 0, 1, tzinfo=timezone.utc),
                file_path=foo_file.path,
                content_changes=[
                    ContentChange(
                        text="bb",
                        crange=CharRange(0, 2),
                    )
                ],
            ),
            TextEditEvent(
                session_id=session_id,
                user_id=user_id,
                tenant=tenant,
                time=datetime(2024, 1, 1, 0, 0, 2, tzinfo=timezone.utc),
                file_path=foo_file.path,
                content_changes=[
                    ContentChange(
                        text="c",
                        crange=CharRange(5, 5),
                    )
                ],
            ),
            TextEditEvent(
                session_id=session_id,
                user_id=user_id,
                tenant=tenant,
                time=datetime(2024, 1, 1, 0, 0, 3, tzinfo=timezone.utc),
                file_path=foo_file.path,
                content_changes=[
                    ContentChange(
                        text="",
                        crange=CharRange(5, 6),
                    )
                ],
            ),
            TextEditEvent(
                session_id=session_id,
                user_id=user_id,
                tenant=tenant,
                time=datetime(2024, 1, 1, 0, 0, 4, tzinfo=timezone.utc),
                file_path=foo_file.path,
                content_changes=[
                    ContentChange(
                        text="a",
                        crange=CharRange(0, 0),
                    )
                ],
            ),
        ],
        request=NextEditRequest(
            model_name="",
            sequence_id=0,
            lang="",
            instruction="",
            recent_changes=[],
            vcs_change=VCSChange(working_directory_changes=[]),
            path=bar_file.path,
            blob_name=bar_file.blob_name,
            selection_begin_char=0,
            selection_end_char=0,
            prefix="",
            selected_text="",
            suffix=bar_file.contents,
            diagnostics=[],
            mode=NextEditRequest.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,
            scope=NextEditRequest.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,
            change_probability_override=0.0,
            edit_events=[],
            blob_names=[foo_file.blob_name, bar_file.blob_name],
            blocked_locations=[],
            restrict_to_file=False,
            timestamp=datetime(2024, 1, 1, tzinfo=timezone.utc),
            request_id="",
        ),
        session_id=session_id,
    )

    result = StrDiffHeuristic(max_interruption=0).generate(data)

    assert len(result.ground_truth) == 0

    assert result.result_type == NextEditStrDiffHeuristicResult.ResultType.INTERRUPTION
