load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

py_library(
    name = "uninterrupted_heuristic",
    srcs = [
        "uninterrupted_heuristic.py",
    ],
    deps = [
        "//base/datasets:hindsight_lib",
        "//base/datasets:hindsight_next_edit",
        "//base/datasets:hindsight_next_edit_lib",
        "//base/datasets:next_edit",
        "//base/datasets:user_event",
        "//base/diff_utils",
        "//base/ranges",
        "//base/static_analysis:common",
    ],
)

pytest_test(
    name = "uninterrupted_heuristic_test",
    srcs = [
        "uninterrupted_heuristic_test.py",
    ],
    deps = [
        ":uninterrupted_heuristic",
        "//base/diff_utils",
        "//base/ranges",
    ],
)
