{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.tenants import DOGFOOD\n", "from google.cloud import bigquery\n", "\n", "tenant = DOGFOOD\n", "bigquery_client = bigquery.Client(project=tenant.project_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pathlib\n", "\n", "results = bigquery_client.query_and_wait(\"\"\"\n", "SELECT request_id, \n", "request.raw_json AS request_json,\n", "suggestion AS suggestion_json\n", "FROM `staging_request_insight_full_export_dataset.next_edit_host_response` response\n", "JOIN `staging_request_insight_full_export_dataset.next_edit_host_request` request USING (request_id)\n", "JOIN UNNEST(JSON_EXTRACT_ARRAY(response.raw_json, \"$.suggestions\")) AS suggestion\n", "WHERE JSON_EXTRACT(suggestion, \"$.description_prompt\") IS NOT NULL                               \n", "                               \"\"\")\n", "\n", "\n", "num_duplicates = 0\n", "description_prompt_token_ids: set[tuple[int]] = set()\n", "\n", "\n", "# we need to de-duplicate based on the prompt input\n", "def has_duplicate(row):\n", "    # reverse it because prompt changes towards the end\n", "    description_prompt_token_id = tuple(\n", "        row[\"suggestion_json\"][\"description_prompt\"][\"token_ids\"][::-1]\n", "    )\n", "    if description_prompt_token_id in description_prompt_token_ids:\n", "        return True\n", "    else:\n", "        description_prompt_token_ids.add(description_prompt_token_id)\n", "        return False\n", "\n", "\n", "with (pathlib.Path.home() / \"suggestions_deduplicated.jsonl\").open(\"w\") as f:\n", "    for row in results:\n", "        if has_duplicate(row):\n", "            num_duplicates += 1\n", "            print(f\"{num_duplicates=}\")\n", "            continue\n", "        f.write(\n", "            json.dumps(\n", "                {\n", "                    \"request_id\": row[\"request_id\"],\n", "                    \"request_json\": row[\"request_json\"],\n", "                    \"suggestion_json\": row[\"suggestion_json\"],\n", "                }\n", "            )\n", "            + \"\\n\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl\n", "\n", "file_path = pathlib.Path.home() / \"suggestions.jsonl\"\n", "\n", "# Read the first 10 rows\n", "for i, row in enumerate(read_jsonl(file_path)):\n", "    if i >= 10:\n", "        break\n", "    # or process the row as needed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "from typing import Any, Dict\n", "\n", "\n", "def pretty_json(obj):\n", "    return json.dumps(obj, indent=2, sort_keys=True)\n", "\n", "\n", "file_path = Path.home() / \"suggestions.jsonl\"\n", "\n", "# Read the first row\n", "with file_path.open(\"r\") as f:\n", "    first_row = json.loads(f.readline())\n", "\n", "\n", "def remove_fields(data: Dict[str, Any], fields_to_remove: list[str]) -> Dict[str, Any]:\n", "    \"\"\"Recursively remove specified fields from a nested dictionary.\"\"\"\n", "    if isinstance(data, dict):\n", "        return {\n", "            k: remove_fields(v, fields_to_remove)\n", "            for k, v in data.items()\n", "            if k not in fields_to_remove\n", "        }\n", "    elif isinstance(data, list):\n", "        return [remove_fields(item, fields_to_remove) for item in data]\n", "    else:\n", "        return data\n", "\n", "\n", "new_dict = remove_fields(\n", "    first_row[\"request_json\"],\n", "    [\"blobs\", \"diagnostics\", \"edit_events\", \"recent_changes\", \"vcs_change\"],\n", ")[\"request\"]\n", "\n", "# from the request we can get the before file using a combination of prefix, selected_code, and suffix\n", "# print(pretty_json(new_dict))\n", "\n", "selected_code = new_dict.get(\"selected_code\") or \"\"\n", "before_file = new_dict[\"prefix\"] + selected_code + new_dict[\"suffix\"]\n", "# print(before_file)\n", "\n", "# from the suggestion we need the change and the range to replace in\n", "clean_suggestion_dict = remove_fields(\n", "    first_row[\"suggestion_json\"], [\"description_output\", \"result\"]\n", ")\n", "print(pretty_json(clean_suggestion_dict[\"description_prompt\"][\"token_ids\"]))\n", "\n", "\n", "def parse_jsonl_row_to_files(row):\n", "    try:\n", "        request = row[\"request_json\"][\"request\"]\n", "        selected_code = request.get(\"selected_code\") or \"\"\n", "        before_file = request[\"prefix\"] + selected_code + request[\"suffix\"]\n", "    except KeyError as e:\n", "        error_message = f\"Key {e} not found in {row}\"\n", "        print(error_message)  # Print the error message\n", "        raise  # Re-raise the exception to stop execution\n", "\n", "    try:\n", "        suggested_edit = row[\"suggestion_json\"][\"result\"][\"suggested_edit\"]\n", "        char_start = suggested_edit[\"char_start\"]\n", "        char_end = suggested_edit[\"char_end\"]\n", "        suggested_code = suggested_edit[\"suggested_code\"]\n", "    except KeyError as e:\n", "        error_message = f\"Key {e} not found in {row}\"\n", "        print(error_message)  # Print the error message\n", "        raise  # Re-raise the exception to stop execution\n", "\n", "    after_file = before_file[:char_start] + suggested_code + before_file[char_end:]\n", "\n", "    return before_file, after_file"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}