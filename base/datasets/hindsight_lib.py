"""Utilities for computing ground truths from user events (e.g. text edits).

This module contains the actual algorithms for processing a series of user events
(e.g. completion requests and text edits) that are collected from contractor/dogfood
users and using heuristics to determine what the response should have been based on
what the user actually typed. For example, see UninterruptedHeuristic.
"""

import dataclasses
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Generic, Optional, TypeVar

from base.ranges import CharRange
from base.datasets.user_event import ContentChange

DocPath = TypeVar("DocPath")
RangeId = TypeVar("RangeId")


@dataclass
class TextRange:
    """A character range and the text inside."""

    crange: CharRange
    text: str

    @staticmethod
    def from_text(start: int, text: str) -> "TextRange":
        """Constructs a TextRange from a start and text."""
        end = start + len(text)
        return TextRange(CharRange(start, end), text)

    def __getitem__(self, key: slice) -> "TextRange":
        """Slices the text range.

        Note that the indices are absolute, not relative to the text indices.
        The slice is also clamped to the bounds of the range.
        For example, ((2, 5), "abc")[1:3] -> ((2, 3), "a").
        """
        assert key.step is None

        # Default start, stop and then clamp.
        start = key.start if key.start is not None else self.crange.start
        stop = key.stop if key.stop is not None else self.crange.stop
        start = min(max(start, self.crange.start), self.crange.stop)
        stop = min(max(stop, self.crange.start), self.crange.stop)

        text_start = start - self.crange.start
        text_stop = stop - self.crange.start
        assert 0 <= text_start <= text_stop <= len(self.text)
        return TextRange(
            CharRange(start, stop),
            self.text[text_start:text_stop],
        )


# TODO: Separate common functions from completion heuristic (matts)
def changes_is_nondisjoint(changes: list[ContentChange]) -> bool:
    """Check if changes is nondisjoint, i.e. has overlapping ranges.

    Precondition: changes are sorted by CharRange, ascending or descending.
    """
    for i in range(len(changes) - 1):
        if changes[i].crange.overlaps(changes[i + 1].crange):
            return True
    return False


def changes_has_duplicate_points(changes: list[ContentChange]) -> bool:
    """Check if changes has duplicate point ranges.

    Precondition: changes are sorted by CharRange, ascending or descending.
    """
    for i in range(len(changes) - 1):
        if changes[i].crange.is_point() and changes[i].crange == changes[i + 1].crange:
            return True
    return False


class RangeTracker(Generic[RangeId]):
    """A data structure for tracking the text in character ranges within a document.

    The range tracker tracks ranges within a single document. A range can be tracked
    by specifying a range and the text inside, and assigning it a unique RangeId.
    The RangeId can be any unique identifier, but is usually tied to a request_id.
    A change to a document occurs when a specified range is replaced with new text.

    When a change occurs, the tracked ranges are updated.
    1. If the change does not touch the range, we simply shift the range.
    2. If the change is contained by the range, we replace the text and calculate the
        new end of the range.
    3. If the change touches the range and text outside the range, it is ambiguous what
        part of the change belongs to the range. To be safe, we expand the range to the
        union of the two, before performing the text replacement and calculating the
        new endpoints. For example ((2, 5), "abc"), where we replace (1, 3) -> "def"
        becomes ((1, 6), "defbc"). This handles all inputs without having to throw an
        error, and defers the decision to the caller.
    """

    def __init__(self):
        """Initializes the range tracker."""
        self.ranges = dict[RangeId, TextRange]()

    def track_range(self, range_id: RangeId, text_range: TextRange):
        """Track the given text range."""
        crange = text_range.crange
        text = text_range.text
        if len(crange) != len(text):
            raise ValueError(
                f"range and text must be the same length: {crange}, {text}"
            )

        if range_id in self.ranges:
            raise ValueError(f"range_id {range_id} is already tracked")

        range_copy = dataclasses.replace(text_range)
        self.ranges[range_id] = range_copy

    def untrack_range(self, range_id: RangeId) -> TextRange:
        """Untrack the given text range."""
        if range_id not in self.ranges:
            raise ValueError(f"range_id {range_id} does not exist")

        text_range = self.ranges.pop(range_id)
        return text_range

    def clear_tracked_ranges(self):
        """Clear all tracked ranges."""
        self.ranges.clear()

    def __contains__(self, range_id: RangeId) -> bool:
        """Check if the given range_id is tracked."""
        return range_id in self.ranges

    def get_range(self, range_id: RangeId) -> TextRange:
        """Get the tracked text range."""
        if range_id not in self.ranges:
            raise ValueError(f"range_id {range_id} does not exist")

        text_range = self.ranges[range_id]
        return text_range

    def find_containing_ranges(self, crange: CharRange) -> list[TextRange]:
        """Finds the text ranges containing the given range."""
        return [
            text_range
            for _, text_range in self.ranges.items()
            if text_range.crange.contains(crange)
        ]

    def update_ranges(self, changes: list[ContentChange]):
        """Update tracked ranges in the given document given a list of content changes.

        The changes are treated as simultaneous. If the changes are ambiguous,
        e.g. not disjoint or same point range, an error is raised.
        """
        changes = sorted(changes, key=lambda x: x.crange, reverse=True)
        if changes_is_nondisjoint(changes) or changes_has_duplicate_points(changes):
            raise ValueError(f"changes are ambiguous: {changes}")

        for change in changes:
            self._update_ranges(change.crange, change.text)

    def _update_ranges(self, old_range: CharRange, new_text: str):
        """Update tracked ranges in the given document given a content change."""
        for _, text_range in self.ranges.items():
            crange = text_range.crange
            if crange.touches(old_range):
                # We handle both cases 2 and 3 above here, since 2 is actually
                # a special case of 3.
                #
                # For the new range, we first expand to the merged range via
                # crange.merge(old_range), before calculating the new endpoints.
                # For the new text, we first calculate the absolute position to replace
                # via crange.intersect(old_range), then turning it into a relative
                # index into text.
                #
                # Example 1 (simple containment):
                # crange=2:5, text="abc", old_range=3:4, new_text="def"
                # merged_range=1:5 => new_range=1:7
                # replace_range=3:4 => index=1:2 => new_text="adefc"
                #
                # Example 2 (partial overlap):
                # crange=2:5, text="abc", old_range=1:3, new_text="def"
                # merged_range=1:5 => new_range=1:6
                # replace_range=2:3 => index=0:1 => new_text="defbc"

                # Expand crange, then account for the text replacement.
                merged_range = crange.merge(old_range)
                new_range = CharRange(
                    merged_range.start,
                    merged_range.stop + len(new_text) - len(old_range),
                )

                # Replace the text in old_range with new_text using document indexing.
                updated_text = (
                    text_range[: old_range.start].text
                    + new_text
                    + text_range[old_range.stop :].text
                )
                text_range.crange = new_range
                text_range.text = updated_text

                assert len(text_range.crange) == len(text_range.text)

            elif old_range < crange:
                # This handles case 1 where the ranges don't touch.
                # If old_range occurs later, we don't need to shift.
                # If old_range occurs earlier, we shift by the change in length.
                shift = len(new_text) - len(old_range)
                text_range.crange = text_range.crange.shifted(shift)


RequestId = TypeVar("RequestId")


def common_prefix_suffix(a: str, b: str) -> tuple[int, int]:
    """Returns the number of common prefix and suffix chars between the two strings.

    If the strings are equal, prefix == len(a) == len(b) and suffix == 0, so that no
    characters are ever double counted.
    """
    p = 0
    while p < len(a) and p < len(b) and a[p] == b[p]:
        p += 1
    a = a[p:]
    b = b[p:]
    s = -1
    while s >= -len(a) and s >= -len(b) and a[s] == b[s]:
        s -= 1
    return p, -s - 1


@dataclass
class _UninterruptedHeuristicState(Generic[DocPath]):
    """The state for a single request."""

    start_time: datetime
    doc_path: DocPath
    num_interruption_chars: int = 0


@dataclass(frozen=True)
class _UninterruptedHeuristicRangeId(Generic[RequestId]):
    """The range id. We track a text_range and context_range for each request."""

    request_id: RequestId
    "The request id."

    is_context: bool = False
    "If this is a context range."


@dataclass
class _UninterruptedHeuristicDocStates(Generic[DocPath, RequestId]):
    """The states for requests in a single document."""

    states: dict[RequestId, _UninterruptedHeuristicState[DocPath]] = dataclasses.field(
        default_factory=dict
    )
    range_tracker: RangeTracker[_UninterruptedHeuristicRangeId[RequestId]] = (
        dataclasses.field(
            default_factory=RangeTracker[_UninterruptedHeuristicRangeId[RequestId]]
        )
    )


@dataclass
class UninterruptedHeuristic(Generic[DocPath, RequestId]):
    """
    A simple heuristic that creates a ground truth from text edits to a range,
    so long as all text edits to that range within a given time interval are
    uninterrupted.

    This class assumes all calls are made with increasing timestamps. An interruption
    is defined as any text edit occuring *outside* a text range occurring time-wise
    between the first and last text edit *inside* the range.
    """

    @dataclass(frozen=True)
    class Result:
        ground_truth: Optional[str] = None
        """The ground truth text. None if error."""
        error_type: str = ""
        """The error type. Empty if success."""
        error_detail: str = ""
        """The error details. Empty if success."""

    time_limit: timedelta
    """The amount of time we watch for text edits to a range."""
    max_interruption_chars: int = 0
    """The max total number of characters across all interruptions to text edits to a
    range. Results in an error if exceeded."""
    allow_overlaps: bool = False
    """If False, text edits that touch a range, but are not fully contained by it,
    will result in an error."""
    trim_changes: bool = False
    """If True, trim the common prefix and suffix of replacement changes. For example,
    "ab" -> "abcd" becomes "" -> "cd", with the cursor moved accordingly."""

    def __post_init__(self):
        self.doc_states = defaultdict[
            DocPath, _UninterruptedHeuristicDocStates[DocPath, RequestId]
        ](_UninterruptedHeuristicDocStates[DocPath, RequestId])
        self.results: dict[RequestId, UninterruptedHeuristic.Result] = {}
        self.seen_requests = set[RequestId]()

    def add_request(
        self,
        time: datetime,
        request_id: RequestId,
        doc_path: DocPath,
        text_range: TextRange,
        context_range: Optional[TextRange] = None,
    ):
        """Add a request to calculate the heuristic for.

        Args:
            time: The time the request was made.
            request_id: The request id. Must be unique.
            doc_path: The document path.
            text_range: The text range in the document to track.
            context_range: Any additional context. The heuristic does not otherwise
                know about characters outside the text_range. This additional context
                can be used for change trimming, which requires knowing what characters
                were replaced.
        """
        if request_id in self.seen_requests:
            raise ValueError(f"request_id {request_id} is already added")
        self.seen_requests.add(request_id)
        doc_state = self.doc_states[doc_path]
        doc_state.states[request_id] = _UninterruptedHeuristicState(time, doc_path)
        doc_state.range_tracker.track_range(
            _UninterruptedHeuristicRangeId(request_id), text_range
        )
        if context_range is not None and self.trim_changes:
            # Track the context range if it is not contained by any other range.
            if not doc_state.range_tracker.find_containing_ranges(context_range.crange):
                doc_state.range_tracker.track_range(
                    _UninterruptedHeuristicRangeId(request_id, is_context=True),
                    context_range,
                )

    def _remove_request(
        self,
        doc_state: _UninterruptedHeuristicDocStates[DocPath, RequestId],
        request_id: RequestId,
    ) -> TextRange:
        """Helper method to remove a request and return the resulting text range."""
        doc_state.states.pop(request_id)
        text_range = doc_state.range_tracker.untrack_range(
            _UninterruptedHeuristicRangeId(request_id)
        )
        context_id = _UninterruptedHeuristicRangeId(request_id, is_context=True)
        if context_id in doc_state.range_tracker:
            doc_state.range_tracker.untrack_range(context_id)
        return text_range

    def _update_state(
        self,
        request_id: RequestId,
        request_doc: DocPath,
        state: _UninterruptedHeuristicState[DocPath],
        change_doc: DocPath,
        changes: list[ContentChange],
    ):
        """Updates a request's state in response to the given content changes.

        This method updates the number of interruptions, and performs checks
        if the range is modified.

        Since the changes are treated as simultaneous, the updates are accumulated
        and applied all at the end. Precedence is given to overlap_disallowed over
        interruption_chars_exceeded.
        """
        num_interruption_chars = 0
        error = None
        error_priority = (0, 0, 0)
        for change in changes:
            if request_doc != change_doc:
                # The change was elsewhere, just increment interruptions
                num_interruption_chars += len(change.crange) + len(change.text)
                continue

            cur = self.doc_states[request_doc].range_tracker.get_range(
                _UninterruptedHeuristicRangeId(request_id)
            )
            if not cur.crange.touches(change.crange):
                # The change was elsewhere, just increment interruptions
                num_interruption_chars += len(change.crange) + len(change.text)

            elif not cur.crange.contains(change.crange) and not self.allow_overlaps:
                # The change was an overlap and is disallowed
                priority = (2, change.crange.start, change.crange.stop)
                if priority < error_priority:
                    continue
                error_priority = priority
                error = UninterruptedHeuristic.Result(
                    error_type="overlap_disallowed",
                    error_detail=f"cur_range={cur.crange}, changed_range={change.crange}",
                )

            elif state.num_interruption_chars > self.max_interruption_chars:
                # The request range was modified, but there were too many interruptions
                priority = (1, 0, 0)
                if priority < error_priority:
                    continue
                error_priority = priority
                error = UninterruptedHeuristic.Result(
                    error_type="interruption_chars_exceeded",
                    error_detail=f"num_interruption_chars={state.num_interruption_chars}",
                )

        if error is not None:
            self._remove_request(self.doc_states[request_doc], request_id)
            self.results[request_id] = error
        else:
            state.num_interruption_chars += num_interruption_chars

    def _trim_content_change(
        self,
        doc_state: _UninterruptedHeuristicDocStates[DocPath, RequestId],
        change: ContentChange,
    ) -> ContentChange:
        """Trims content changes that contain redundant changes.

        For example, if we replace the range (0, 4) = "abef" with "abcdef", we can
        reduce this to an insertion at (2, 2) = "" -> "cd". Note that redundancies
        (if present) always happen at the prefix and suffix.

        Raises ValueError if the lookup of the text being replaced returns inconsistent
        results.
        """
        if change.crange.is_point():
            return change

        # NOTE: RangeTracker does not verify that all text in ranges are consistent.
        # It's possible for the calculated text to differ from the actual document
        # (e.g. UTF-16 surrogate pairs, git pull).
        # So we get all the containing ranges and raise if they differ.
        text_ranges = doc_state.range_tracker.find_containing_ranges(change.crange)
        if len(text_ranges) == 0:
            return change

        old_ranges = [
            text_range[change.crange.to_slice()] for text_range in text_ranges
        ]
        for old_range in old_ranges:
            if old_range != old_ranges[0]:
                raise ValueError(f"ranges did not match: {old_range}, {old_ranges[0]}")

        assert old_ranges[0].crange == change.crange

        # Now that we know the old text, we trim any unchanged prefix or suffix,
        # i.e. prefix or suffix that is common to both the old and new text.
        p, s = common_prefix_suffix(old_ranges[0].text, change.text)
        return ContentChange(
            text=change.text[p : len(change.text) - s],
            crange=CharRange(change.crange.start + p, change.crange.stop - s),
        )

    def add_change(self, time: datetime, doc_path: DocPath, change: ContentChange):
        """Add a change and update all the heuristics."""
        self.add_changes(time, doc_path, [change])

    def add_changes(
        self, time: datetime, doc_path: DocPath, changes: list[ContentChange]
    ):
        """Add multiple changes and update all the heuristics."""
        self._tick(time, doc_path)

        changes_error = None
        # Check if changes are ambiguous.
        changes = sorted(changes, key=lambda x: x.crange, reverse=True)
        if changes_is_nondisjoint(changes):
            changes_error = UninterruptedHeuristic.Result(
                error_type="changes_nondisjoint",
                error_detail=f"changes={changes}",
            )
        elif changes_has_duplicate_points(changes):
            changes_error = UninterruptedHeuristic.Result(
                error_type="changes_duplicate_points",
                error_detail=f"changes={changes}",
            )

        if self.trim_changes and not changes_error:
            # NOTE: we don't need to check changes again after trimming for ambiguity
            # because trimming can only reduce ranges, so non-ambiguity is preserved.
            try:
                changes = [
                    self._trim_content_change(self.doc_states[doc_path], change)
                    for change in changes
                ]
            except ValueError as e:
                changes_error = UninterruptedHeuristic.Result(
                    error_type="changes_inconsistent_text",
                    error_detail=str(e),
                )

        # If there is some issue with changes, we invalidate the entire document.
        if changes_error:
            doc_state = self.doc_states[doc_path]
            for request_id, state in list(doc_state.states.items()):
                self._remove_request(doc_state, request_id)
                self.results[request_id] = changes_error

        # Update the states for all requests. This doesn't require valid changes.
        for request_doc, doc_state in self.doc_states.items():
            for request_id, state in list(doc_state.states.items()):
                self._update_state(request_id, request_doc, state, doc_path, changes)

        # update_ranges can throw if changes are ambiguous, so only update if no error.
        if not changes_error:
            self.doc_states[doc_path].range_tracker.update_ranges(changes)

    def _tick_all(self, time: datetime):
        """Move the time forward for all tracked requests."""
        for doc_path in self.doc_states:
            self._tick(time, doc_path)

    def _tick(self, time: datetime, doc_path: DocPath):
        """Move the time forward for the requests in the given document."""
        doc_state = self.doc_states[doc_path]
        for request_id, state in list(doc_state.states.items()):
            if time - state.start_time >= self.time_limit:
                text_range = self._remove_request(doc_state, request_id)
                self.results[request_id] = UninterruptedHeuristic.Result(
                    ground_truth=text_range.text
                )

    def finish(
        self, time: datetime
    ) -> dict[RequestId, "UninterruptedHeuristic.Result"]:
        """Finishes all the tracked requests and returns the results."""
        self._tick_all(time)

        # All the other requests didn't meet the minimum time requirement.
        for _, doc_state in self.doc_states.items():
            for request_id, state in list(doc_state.states.items()):
                self._remove_request(doc_state, request_id)
                self.results[request_id] = UninterruptedHeuristic.Result(
                    error_type="unfinished_request",
                    error_detail=", ".join(
                        [
                            f"start_time={state.start_time}",
                            f"cur_time={time}",
                        ]
                    ),
                )
        return self.results
