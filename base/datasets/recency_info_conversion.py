"""Contains conversion functions from proto to datasets classes for recency info."""

import logging
from datetime import timezone

from base.datasets.recency_info import (
    CharRang<PERSON>,
    GitDiffFileInfo,
    RecencyInfo,
    ReplacementText,
    TabSwitchEvent,
    ViewedContentEvent,
)
from services.completion_host import completion_pb2
from services.request_insight import request_insight_pb2

logger = logging.getLogger(__name__)


def from_replacement_text_proto(
    change: completion_pb2.ReplacementText,
) -> ReplacementText:
    """Converts a proto to a ReplacementText.

    Raises ValueError if char_start > char_end.
    """
    return ReplacementText(
        blob_name=change.blob_name,
        path=change.path,
        crange=CharRange(
            change.char_start,
            change.char_end,
        ),
        replacement_text=change.replacement_text,
        present_in_blob=change.present_in_blob,
        expected_blob_name=change.expected_blob_name,
        timestamp=change.timestamp.ToDatetime(tzinfo=timezone.utc)
        if change.HasField("timestamp")
        else None,
    )


def from_tab_switch_event_proto(
    event: completion_pb2.TabSwitchEvent,
) -> TabSwitchEvent:
    """Converts a proto to a TabSwitchEvent."""
    return TabSwitchEvent(
        path=event.path,
        file_blob_name=event.file_blob_name,
    )


def from_git_diff_file_info_proto(
    info: completion_pb2.GitDiffFileInfo,
) -> GitDiffFileInfo:
    """Converts a proto to a GitDiffFileInfo."""
    return GitDiffFileInfo(
        content_blob_name=info.content_blob_name,
        file_blob_name=info.file_blob_name,
    )


def from_viewed_content_event_proto(
    event: completion_pb2.ViewedContentEvent,
) -> ViewedContentEvent:
    """Converts a proto to a ViewedContentEvent."""
    return ViewedContentEvent(
        path=event.path,
        file_blob_name=event.file_blob_name,
        visible_content=event.visible_content,
        line_start=event.line_start,
        line_end=event.line_end,
        char_start=event.char_start,
        char_end=event.char_end,
        timestamp=event.timestamp.ToDatetime(tzinfo=timezone.utc),
    )


def from_recency_info_proto(
    proto: request_insight_pb2.CompletionHostRequest,
    request_id: str,
) -> RecencyInfo:
    """Converts a recency info proto to a RecencyInfo."""
    try:
        recent_changes = [
            from_replacement_text_proto(change)
            for change in proto.recency_info.recent_changes
        ]
    except ValueError as e:
        # This can happen if we have a bug in the recent changes tracking violating
        # char_start < char_end. It shouldn't happen often, but if it does, let's just
        # drop the recent changes and log to console.
        logger.warning("Error in recent changes for request %s: %s", request_id, e)
        recent_changes = []

    tab_switch_events = [
        from_tab_switch_event_proto(event)
        for event in proto.recency_info.tab_switch_events
    ]
    git_diff_info = [
        from_git_diff_file_info_proto(info)
        for info in proto.recency_info.git_diff_file_info
    ]
    viewed_contents = [
        from_viewed_content_event_proto(event)
        for event in proto.recency_info.viewed_contents
    ]
    return RecencyInfo(
        tab_switch_events=tab_switch_events,
        git_diff_info=git_diff_info,
        recent_changes=recent_changes,
        viewed_contents=viewed_contents,
    )
