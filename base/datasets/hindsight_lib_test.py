from datetime import datetime, timedelta, timezone

from base.datasets.hindsight_lib import Range<PERSON>racker, TextRange, UninterruptedHeuristic
from base.datasets.user_event import ContentChange
from base.ranges import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_range_tracker_basic():
    """Tests the range tracker."""
    tracker = RangeTracker[int]()
    tracker.track_range(0, TextRange.from_text(5, "hello"))

    # insert
    tracker.update_ranges([ContentChange(" ", <PERSON><PERSON><PERSON><PERSON><PERSON>(10, 10))])
    assert tracker.get_range(0) == TextRange.from_text(5, "hello ")

    # adjoining range after
    tracker.update_ranges([ContentChange("world", <PERSON><PERSON><PERSON><PERSON><PERSON>(11, 12))])
    assert tracker.get_range(0) == TextRange.from_text(5, "hello world")


def test_range_tracker_multiple():
    tracker = RangeTracker[int]()
    tracker.track_range(1, TextRange.from_text(5, "foobar"))
    tracker.track_range(2, TextRange.from_text(8, "barbaz"))

    # overlapping range
    tracker.update_ranges([ContentChange("_abcd_", <PERSON><PERSON><PERSON><PERSON><PERSON>(7, 10))])
    assert tracker.get_range(1) == TextRange.from_text(5, "fo_abcd_r")
    assert tracker.get_range(2) == TextRange.from_text(7, "_abcd_rbaz")

    # adjoining range before and non-overlapping range
    tracker.update_ranges([ContentChange("xyz", CharRange(1, 5))])
    assert tracker.get_range(1) == TextRange.from_text(1, "xyzfo_abcd_r")
    assert tracker.get_range(2) == TextRange.from_text(6, "_abcd_rbaz")

    # multiple changes
    tracker.update_ranges(
        [
            ContentChange("!", CharRange(4, 13)),
            ContentChange("?", CharRange(13, 13)),
            ContentChange(".", CharRange(13, 14)),
        ],
    )
    assert tracker.get_range(1) == TextRange.from_text(1, "xyz!?.")
    assert tracker.get_range(2) == TextRange.from_text(4, "!?.az")


def add_typing_changes(
    heuristic: UninterruptedHeuristic[str, str],
    t: datetime,
    doc_path: str,
    strs: list[str],
    i: int,
):
    """Types strings one at a time, with empty char meaning delete."""
    for s in strs:
        if s == "":
            heuristic.add_change(t, doc_path, ContentChange(s, CharRange(i - 1, i)))
            i -= 1
        else:
            heuristic.add_change(t, doc_path, ContentChange(s, CharRange(i, i)))
            i += len(s)


def add_change(
    heuristic: UninterruptedHeuristic[str, str],
    t: datetime,
    doc_path: str,
    text: str,
    start: int,
    stop: int,
):
    """Adds a change to the heuristic."""
    heuristic.add_change(t, doc_path, ContentChange(text, CharRange(start, stop)))


def add_changes(
    heuristic: UninterruptedHeuristic[str, str],
    t: datetime,
    doc_path: str,
    changes: list[tuple[str, int, int]],
):
    """Adds multiple changes to the heuristic."""
    content_changes = [
        ContentChange(text, CharRange(start, stop)) for text, start, stop in changes
    ]
    heuristic.add_changes(t, doc_path, content_changes)


def test_uninterrupted_heuristic_basic():
    """Tests uninterrupted heuristic."""
    heuristic = UninterruptedHeuristic[str, str](timedelta(hours=1))
    t = datetime(2024, 4, 10, 21, 9, 36, tzinfo=timezone.utc)
    s = timedelta(seconds=1)
    expected = {}

    # insert single letters
    expected["r0"] = UninterruptedHeuristic.Result(ground_truth="abcd")
    heuristic.add_request(t, "r0", "a.py", TextRange.from_text(100, ""))
    add_typing_changes(heuristic, t + s, "a.py", ["a", "b", "c", "d"], 100)

    # replace + insert + delete
    expected["r1"] = UninterruptedHeuristic.Result(ground_truth="hello_world")
    heuristic.add_request(t + 10 * s, "r1", "a.py", TextRange.from_text(200, "hlleo"))
    add_change(heuristic, t + 11 * s, "a.py", "ell", 201, 204)
    add_typing_changes(heuristic, t + 12 * s, "a.py", ["_wordl", "", "", "ld"], 205)

    # separate document, edit a huge range, test we don't mess up r0 or r1.
    expected["r2"] = UninterruptedHeuristic.Result(ground_truth="r2d2")
    heuristic.add_request(t + 20 * s, "r2", "b.py", TextRange.from_text(100, ""))
    add_typing_changes(heuristic, t + 21 * s, "b.py", ["c"] * 300, 100)
    add_change(heuristic, t + 22 * s, "b.py", "r2d2", 100, 400)

    # shifting, test that we don't mess up r0.
    expected["r3"] = UninterruptedHeuristic.Result(ground_truth="xyzabc")
    heuristic.add_request(t + 30 * s, "r3", "a.py", TextRange.from_text(99, ""))
    add_typing_changes(heuristic, t + 31 * s, "a.py", ["xyz", "abc"], 99)

    results = heuristic.finish(t + 3660 * s)
    assert results == expected


def test_uninterrupted_heuristic_errors():
    """Tests uninterrupted heuristic errors."""
    heuristic = UninterruptedHeuristic[str, str](timedelta(hours=1))
    t = datetime(2024, 4, 10, 21, 9, 36, tzinfo=timezone.utc)
    s = timedelta(seconds=1)
    expected = {}

    # overlap disallowed
    expected["r0"] = UninterruptedHeuristic.Result(
        error_type="overlap_disallowed",
        error_detail="cur_range=100:106, changed_range=99:101",
    )
    heuristic.add_request(t, "r0", "a.py", TextRange.from_text(100, "abcdef"))
    add_change(heuristic, t + s, "a.py", "a", 99, 101)

    # interruption, same document
    expected["r1"] = UninterruptedHeuristic.Result(
        error_type="interruption_chars_exceeded",
        error_detail="num_interruption_chars=6",
    )
    heuristic.add_request(t + 10 * s, "r1", "a.py", TextRange.from_text(200, "hlleo"))
    add_change(heuristic, t + 11 * s, "a.py", "ell", 201, 204)
    add_change(heuristic, t + 12 * s, "a.py", "", 100, 106)
    add_typing_changes(heuristic, t + 13 * s, "a.py", ["_wordl", "", "", "ld"], 199)

    # interruption, separate document
    expected["r2"] = UninterruptedHeuristic.Result(
        error_type="interruption_chars_exceeded",
        error_detail="num_interruption_chars=300",
    )
    heuristic.add_request(t + 20 * s, "r2", "b.py", TextRange.from_text(100, ""))
    add_typing_changes(heuristic, t + 21 * s, "a.py", ["c"] * 300, 100)
    add_change(heuristic, t + 22 * s, "b.py", "r2d2", 100, 100)

    # timelimit exceeded
    expected["r3"] = UninterruptedHeuristic.Result(
        error_type="unfinished_request",
        error_detail="start_time=2024-04-10 21:11:36+00:00, "
        "cur_time=2024-04-10 22:10:36+00:00",
    )
    heuristic.add_request(t + 120 * s, "r3", "a.py", TextRange.from_text(200, ""))
    results = heuristic.finish(t + 3660 * s)
    assert results == expected


def test_uninterrupted_heuristic_simultaneous():
    """Tests uninterrupted heuristic with simultaneous changes."""
    heuristic = UninterruptedHeuristic[str, str](timedelta(hours=1))
    t = datetime(2024, 4, 10, 21, 9, 36, tzinfo=timezone.utc)
    s = timedelta(seconds=1)
    expected = {}

    # test range tracking + state update (no errors)
    expected["r0"] = UninterruptedHeuristic.Result(ground_truth="abcd")
    expected["r1"] = UninterruptedHeuristic.Result(ground_truth="hello_world")
    heuristic.add_request(t, "r0", "a.py", TextRange.from_text(100, "bc"))
    heuristic.add_request(t, "r1", "a.py", TextRange.from_text(200, "hello_bye"))
    add_changes(
        heuristic,
        t + s,
        "a.py",
        [("a", 100, 100), ("d", 102, 102), ("world", 206, 209)],
    )

    # test error priority, overlap > interruption
    expected["r2"] = UninterruptedHeuristic.Result(
        error_type="overlap_disallowed",
        error_detail="cur_range=100:104, changed_range=90:101",
    )
    heuristic.add_request(t + 10 * s, "r2", "b.py", TextRange.from_text(100, "r2d2"))
    add_change(heuristic, t + 11 * s, "b.py", "c3po", 200, 200)
    add_changes(heuristic, t + 12 * s, "b.py", [("a3b3c", 90, 101), ("3", 103, 104)])

    # test error priority, later overlap > earlier
    expected["r3"] = UninterruptedHeuristic.Result(
        error_type="overlap_disallowed",
        error_detail="cur_range=300:303, changed_range=302:304",
    )
    heuristic.add_request(t + 20 * s, "r3", "b.py", TextRange.from_text(300, "rrr"))
    add_changes(heuristic, t + 21 * s, "b.py", [("x", 299, 301), ("y", 302, 304)])

    # test error priority, interruption > no error
    expected["r4"] = UninterruptedHeuristic.Result(
        error_type="interruption_chars_exceeded",
        error_detail="num_interruption_chars=3",
    )
    heuristic.add_request(t + 30 * s, "r4", "c.py", TextRange.from_text(100, "abcd"))
    add_change(heuristic, t + 31 * s, "c.py", "xyz", 200, 200)
    add_changes(heuristic, t + 32 * s, "c.py", [("w", 200, 200), ("efg", 104, 104)])

    results = heuristic.finish(t + 3660 * s)
    assert results == expected


def test_uninterrupted_heuristic_trim_changes():
    """Tests uninterrupted heuristic with trim changes."""
    heuristic = UninterruptedHeuristic[str, str](timedelta(hours=1), trim_changes=True)
    t = datetime(2024, 4, 10, 21, 9, 36, tzinfo=timezone.utc)
    s = timedelta(seconds=1)

    expected = {}

    expected["r0"] = UninterruptedHeuristic.Result(ground_truth="abcdef")
    heuristic.add_request(t, "r0", "a.py", TextRange.from_text(100, ""))
    add_typing_changes(heuristic, t + s, "a.py", ["a", "b"], 100)

    # Without explicit context, should be able to use other ranges.
    expected["r1"] = UninterruptedHeuristic.Result(ground_truth="cdef")
    heuristic.add_request(t + 2 * s, "r1", "a.py", TextRange.from_text(102, ""))

    # With explicit context, is able to use the context.
    expected["r2"] = UninterruptedHeuristic.Result(ground_truth="_world")
    heuristic.add_request(
        t + 3 * s,
        "r2",
        "a.py",
        TextRange.from_text(200, ""),
        TextRange.from_text(188, "print('hello')"),
    )

    # Simultaneous changes.
    add_changes(
        heuristic,
        t + 10 * s,
        "a.py",
        [("abcdef", 100, 102), ("'hello_world'", 194, 201)],
    )

    # Context should have aged out.
    expected["r3"] = UninterruptedHeuristic.Result(
        error_type="overlap_disallowed",
        error_detail="cur_range=200:200, changed_range=195:206",
    )
    heuristic.add_request(t + 3660 * s, "r3", "a.py", TextRange.from_text(200, ""))
    add_change(heuristic, t + 3661 * s, "a.py", "hello world!", 195, 206)

    results = heuristic.finish(t + 7320 * s)
    assert results == expected


def test_uninterrupted_heuristic_dogfood():
    """An anonymized example from dogfood."""
    heuristic = UninterruptedHeuristic[str, str](timedelta(hours=1))
    t = datetime(2024, 4, 10, 21, 9, 36, tzinfo=timezone.utc)
    s = timedelta(seconds=1)

    expected = {}

    expected["x"] = UninterruptedHeuristic.Result(
        ground_truth="foobarbaz_registry import FooBarBazRegistry, abc_field, FooBarBazConfig"
    )
    heuristic.add_request(t, "x", "a.py", TextRange.from_text(243, ""))
    add_typing_changes(heuristic, t, "a.py", ["ab", "", "", "foo", "b", ""], 243)

    expected["y"] = UninterruptedHeuristic.Result(
        error_type="overlap_disallowed",
        error_detail="cur_range=246:249, changed_range=243:249",
    )
    heuristic.add_request(t, "y", "a.py", TextRange.from_text(246, ""))
    add_typing_changes(heuristic, t + s, "a.py", ["b", "a", "r"], 246)
    add_change(heuristic, t + 2 * s, "a.py", "foobarbaz_registry", 243, 249)
    add_typing_changes(heuristic, t + 3 * s, "a.py", [".", "", " import", " C"], 261)
    add_change(heuristic, t + 4 * s, "a.py", "FooBarBazRegistry", 269, 270)
    add_typing_changes(heuristic, t + 6 * s, "a.py", [", ", "abc_field", ", "], 286)
    add_typing_changes(
        heuristic, t + 7 * s, "a.py", ["F", "o", "o", "BarBazConfig"], 299
    )

    add_change(heuristic, t + 10 * s, "a.py", "", 2883, 2899)

    expected["z"] = UninterruptedHeuristic.Result(
        ground_truth="(AbcdeXxxyyyzzzConfig, AbcdeXxxyyyzzz)"
    )
    heuristic.add_request(t + 20 * s, "z", "a.py", TextRange.from_text(4642, ""))
    add_change(heuristic, t + 20 * s, "a.py", "()", 4642, 4642)
    next_text = "AbcdeXxxyyyzzzConfig, AbcdeXxxyyyzzz"
    add_change(heuristic, t + 21 * s, "a.py", next_text, 4643, 4643)
    add_change(heuristic, t + 22 * s, "a.py", "    ", 4756, 4913)
    add_typing_changes(heuristic, t + 23 * s, "a.py", ["r", "e", "f"], 4760)
    results = heuristic.finish(t + 3660 * s)

    assert results == expected
