"""Dataclasses for exported Request Insight user_events."""

from dataclasses import dataclass, field
from datetime import datetime
import hashlib
from typing import Iterable

import dataclasses_json

from base.ranges import CharRange, replace_str


@dataclass(frozen=True)
class UserEvent(dataclasses_json.DataClassJsonMixin):
    """Base class for a user event, which contains all the shared metadata fields."""

    session_id: str
    """The session ID of the event."""
    user_id: str
    """The ID of the user who performed the event. DEPRECATED: Will now always be empty string."""
    tenant: str
    """The tenant of the request"""
    time: datetime
    """The time of the event"""
    file_path: str
    """The path of the file the event applies to. Empty string for events that do not apply to a file."""


@dataclass(frozen=True)
class ContentChange(dataclasses_json.DataClassJsonMixin):
    """A single content change to a file."""

    text: str
    """The new text in the specified range."""
    crange: CharRange
    """The range of the text that was replaced."""

    @property
    def after_crange(self) -> Char<PERSON>ange:
        """The range of the text after the replacement."""
        return CharRange(self.crange.start, self.crange.start + len(self.text))


@dataclass
class TextValidationError(ValueError):
    """Error raised when applying a text edit event fails due to validation errors."""

    new_text: str
    """The text that was produced after applying the event."""

    reason: str
    """The reason for the failure."""

    def __repr__(self):
        return f"TextValidationError(reason={self.reason})"


@dataclass(frozen=True)
class TextEditEvent(UserEvent):
    """A single VSCode text edit event, e.g. keystroke, copy paste."""

    content_changes: list[ContentChange]
    """Each event can consist of one or more content changes."""

    after_changes_hash: str | None = None
    """Hash of the characters in and around the changes after applying them
    Used to detect external file changes (e.g. branch switches) between text edit events
    Calculated as the hash of a simple concat of the text defined by hash char ranges."""

    hash_char_ranges: list[CharRange] = field(default_factory=list)
    """The char ranges used to compute the hashes
    we avoid hashing entire files because it is expensive for large files
    client is free to pick any set of char ranges"""

    after_doc_length: int | None = None
    """The character length of the document after the changes are applied.
    This is used to detect discrepancies between the frontend and backend."""

    def apply_to_text(self, text: str) -> str | ValueError | TextValidationError:
        """Apply the event to the given text.

        Returns new_text or an error if the event fails to apply.

        Hash check is only performed if `after_changes_hash` is not None.
        """

        try:
            new_text = replace_str(
                text,
                [(change.crange, change.text) for change in self.content_changes],
            )
        except ValueError as e:
            return e

        if self.after_changes_hash:
            if (
                self.after_doc_length is not None
                and len(new_text) != self.after_doc_length
            ):
                return TextValidationError(
                    new_text,
                    reason=f"Length check failed: {len(text)=}, {self.after_doc_length=}",
                )
            str_to_check = "".join(
                new_text[r.to_slice()] for r in self.hash_char_ranges
            )
            hash_ = hashlib.sha256(str_to_check.encode("utf-8")).hexdigest()
            if hash_ != self.after_changes_hash:
                return TextValidationError(
                    new_text,
                    reason=f"Hash check failed: new_hash={hash_}, expected={self.after_changes_hash}",
                )

        return new_text


def merge_adjacent_edit_stream(
    events: Iterable[TextEditEvent],
) -> Iterable[TextEditEvent]:
    """Merge consecutive edits if they are adjacent in location and don't partially
    overlap."""
    last_event: TextEditEvent | None = None
    for event in events:
        if last_event is None:
            last_event = event
            continue

        merged = merge_adjacent(last_event, event)
        if merged is not None:
            last_event = merged
        else:
            yield last_event
            last_event = event

    if last_event is not None:
        yield last_event


def merge_adjacent(
    event1: TextEditEvent, event2: TextEditEvent
) -> TextEditEvent | None:
    """Try to merge two edit events if they are adjacent in both time and space and
    don't partially overlap."""
    # for now, only try to merge single-edit events
    if len(event1.content_changes) != 1 or len(event2.content_changes) != 1:
        return None
    if event1.file_path != event2.file_path or event1.session_id != event2.session_id:
        return None
    change1 = event1.content_changes[0]
    change2 = event2.content_changes[0]
    if change1.after_crange.stop == change2.crange.start:
        # the next event starts right after this one.
        new_change = ContentChange(
            text=change1.text + change2.text,
            crange=CharRange(
                change1.crange.start, change1.crange.stop + len(change2.crange)
            ),
        )
    elif change2.crange.stop == change1.after_crange.start:
        # the next event ends right before this one.
        new_change = ContentChange(
            text=change2.text + change1.text,
            crange=CharRange(
                change1.crange.start - len(change2.crange), change1.crange.stop
            ),
        )
    elif change1.after_crange == change2.crange:
        # the next event exactly overrides the current event
        new_change = ContentChange(
            text=change2.text,
            crange=change1.crange,
        )
    else:
        return None
    return TextEditEvent(
        session_id=event1.session_id,
        user_id=event1.user_id,
        tenant=event1.tenant,
        time=event1.time,
        file_path=event1.file_path,
        content_changes=[new_change],
        # Since the hash checks for after state, we use the info from the later event.
        after_changes_hash=event2.after_changes_hash,
        hash_char_ranges=event2.hash_char_ranges,
    )


@dataclass(frozen=True)
class CompletionRequestIdIssuedEvent(UserEvent):
    """Request id issued by the extension for a completion request."""

    request_id: str
    """The request id issued by the extension."""


@dataclass(frozen=True)
class EditRequestIdIssuedEvent(UserEvent):
    """Request id issued by the extension for an instruction request."""

    request_id: str
    """The request id issued by the extension."""


@dataclass(frozen=True)
class NextEditRequestIdIssuedEvent(UserEvent):
    """Request id issued by the extension for a next edit request."""

    request_id: str
    """The request id issued by the extension."""
