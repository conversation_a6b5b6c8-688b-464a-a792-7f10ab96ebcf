"""Common datatypes across tasks."""

from dataclasses import dataclass, field
from datetime import datetime
import dataclasses_json

from base.ranges.range_types import CharRange


@dataclass
class TabSwitchEvent(dataclasses_json.DataClassJsonMixin):
    """Represents a tab-switch event on the client."""

    path: str
    """The path that was switched to."""

    file_blob_name: str
    """The blob name of the file that was switched to."""


@dataclass
class GitDiffFileInfo(dataclasses_json.DataClassJsonMixin):
    """Represents git-diff output for one file on the client."""

    content_blob_name: str
    """The blob name that contains the contents of the diff."""

    file_blob_name: str
    """The blob name of the file that the diff affects."""


@dataclass
class ReplacementText(dataclasses_json.DataClassJsonMixin):
    """Represents recent content written by the client."""

    blob_name: str
    """The blob name that this change applies to."""

    path: str
    """The path that this change applies to."""

    crange: CharRange
    """The start and end of the modified region of the blob."""

    replacement_text: str
    """The new content of the modified region."""

    present_in_blob: bool
    """Indicates whether the blob already contains this change."""

    expected_blob_name: str = ""
    """The blob name of the file after applying all replacements.

    This is defaulted to the empty string for backwards compatibility.
    """

    timestamp: datetime | None = None
    """The timestamp of the change if available."""


@dataclass
class ViewedContentEvent(dataclasses_json.DataClassJsonMixin):
    """Represents content viewed by the user on the client."""

    path: str
    """The path of the file that was viewed."""

    file_blob_name: str
    """The blob name of the file that was viewed."""

    visible_content: str
    """The visible content of the file."""

    line_start: int
    """The start line number of the visible range."""

    line_end: int
    """The end line number of the visible range."""

    char_start: int
    """The start character offset of the visible range."""

    char_end: int
    """The end character offset of the visible range."""

    timestamp: datetime
    """The timestamp of when the content was viewed if available."""


@dataclass
class RecencyInfo(dataclasses_json.DataClassJsonMixin):
    """Represents recent client events."""

    tab_switch_events: list[TabSwitchEvent]
    git_diff_info: list[GitDiffFileInfo]
    recent_changes: list[ReplacementText]
    viewed_contents: list[ViewedContentEvent] = field(default_factory=list)
