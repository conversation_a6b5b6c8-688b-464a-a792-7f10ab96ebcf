"""Tests for base.datasets.gcs_blob_cache."""

from __future__ import annotations

import json
from pathlib import Path
from unittest.mock import MagicMock

import pytest
from google.cloud import storage

from base.blob_names import blob_names_pb2
from base.datasets.gcs_blob_cache import (
    GCSCheckpointCache,
    GCSBlobCache,
    PathAndContent,
    resolve_checkpoints,
)


def _make_blob(path: str, content: str):
    return MagicMock(
        storage.Blob, download_as_text=lambda: content, metadata={"path": path}
    )


def _make_checkpoint(blob_names: list[str]):
    return MagicMock(
        storage.Blob,
        download_as_text=lambda: json.dumps(blob_names),
    )


CONTENTS = {
    "prefix/bbb1": _make_blob("path1", "content1"),
    "prefix/bbb2": _make_blob("path2", "content2"),
    "prefix/bbb3": _make_blob("path3", "content3"),
    "prefix/ccc1": _make_checkpoint(["bbb1", "bbb2"]),
}


@pytest.fixture
def bucket() -> storage.Bucket:
    return MagicMock(storage.Bucket, get_blob=lambda key: CONTENTS.get(key))


def test_get(bucket: storage.Bucket):
    """Test that we can get blobs from GCS."""
    cache = GCSBlobCache(bucket, "prefix", 1000)
    assert cache.get(["bbb1", "bbb2", "bbb3", "missing"]) == [
        PathAndContent(Path("path1"), "content1"),
        PathAndContent(Path("path2"), "content2"),
        PathAndContent(Path("path3"), "content3"),
        None,
    ]


@pytest.mark.parametrize(
    "blobs, expected",
    [
        (
            [
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="ccc1",
                    added=[bytes.fromhex("bbb3")],
                    deleted=[bytes.fromhex("bbb1")],
                ),
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="ccc1",
                    added=[bytes.fromhex("bbb4")],
                    deleted=[bytes.fromhex("bbb2")],
                ),
            ],
            [["bbb2", "bbb3"], ["bbb1", "bbb4"]],
        ),
        (
            [blob_names_pb2.Blobs(baseline_checkpoint_id="ccc1", added=[], deleted=[])],
            [["bbb1", "bbb2"]],
        ),
        (
            [
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="", added=[bytes.fromhex("bbb1")], deleted=[]
                )
            ],
            [["bbb1"]],
        ),
        (
            [
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="ccc1",
                    added=[],
                    deleted=[bytes.fromhex("bbb1"), bytes.fromhex("bbb2")],
                )
            ],
            [[]],
        ),
        (
            [
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="missing",
                    added=[bytes.fromhex("bbb1")],
                    deleted=[],
                )
            ],
            [["bbb1"]],
        ),
    ],
)
def test_resolve_checkpoints(
    bucket: storage.Bucket, blobs: list[blob_names_pb2.Blobs], expected: list[list[str]]
):
    """Test that we can resolve checkpoints from GCS."""
    cache = GCSCheckpointCache(bucket, "prefix", 1000)
    assert resolve_checkpoints(cache, blobs) == expected
