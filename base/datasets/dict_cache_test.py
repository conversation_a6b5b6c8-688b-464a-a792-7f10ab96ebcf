"""Tests for base.datasets.dict_cache."""

from __future__ import annotations

from typing import Mapping

import pytest

from base.datasets.dict_cache import DictCache


@pytest.fixture
def mapping() -> dict[str, str]:
    return {
        "key1": "value1",
        "key2": "value2",
        "key3": "value3",
    }


def test_get(mapping: Mapping[str, str]):
    cache = DictCache(mapping=mapping)
    expected = ["value1", "value2", "value3", None]
    assert cache.get(["key1", "key2", "key3", "missing"]) == expected
