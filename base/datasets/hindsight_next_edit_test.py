"""Tests for base.datasets.hindsight_next_edit_dataset"""

import dataclasses
import hashlib
import io
from datetime import datetime, timedelta, timezone
from pathlib import Path
from random import Random

import pytest

from base.datasets.dict_cache import DictCache
from base.datasets.gcs_blob_cache import PathAndContent
from base.datasets.hindsight_next_edit import HindsightNextEditDatum
from base.datasets.hindsight_next_edit_dataset import (
    HindsightNextEditDataset,
    HindsightNextEditProcessArgs,
    HindsightNextEditQueryResults,
    NextEditIntermediateDataLoader,
    _process,
    _sample,
)
from base.datasets.hindsight_next_edit_lib import validate_text_edit_event
from base.datasets.next_edit import (
    DiffSpan,
    NextEditDatum,
    NextEditGroundTruth,
    NextEditRequest,
    NextEditResponse,
    NextEditResult,
    NextEditSuggestion,
    ReplacementText,
    RetrievalChunk,
    ScoredFileHunk,
    Tokenization,
    VCSChange,
)
from base.datasets.tenants import DatasetTenant
from base.datasets.user_event import (
    ContentChange,
    NextEditRequestIdIssuedEvent,
    TextEditEvent,
)
from base.datasets.user_event_lib import UserEventUnion
from base.diff_utils.diff_utils import File
from base.ranges.range_types import CharRange


@pytest.fixture
def example_datum() -> HindsightNextEditDatum:
    return HindsightNextEditDatum(
        next_edit_datum=NextEditDatum(
            request_id="test-request-id",
            user_id="test-user-id",
            user_agent="test-user-agent",
            session_id="test-session-id",
            request=NextEditRequest(
                model_name="test-model",
                sequence_id=0,
                lang="",
                instruction="test-instruction",
                recent_changes=[
                    ReplacementText(
                        path="foo.py",
                        blob_name="test-blob-name",
                        present_in_blob=True,
                        replacement_text="test-replacement-text",
                        crange=CharRange(30, 40),
                    )
                ],
                vcs_change=VCSChange(working_directory_changes=[]),
                path="foo.py",
                blob_name="test-blob-name",
                selection_begin_char=10,
                selection_end_char=20,
                prefix="test-prefix",
                selected_text="test-existing-code",
                suffix="test-suffix",
                diagnostics=[],
                mode=NextEditRequest.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,
                scope=NextEditRequest.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,
                change_probability_override=0.0,
                edit_events=[],
                blob_names=["test-blob-name"],
                blocked_locations=[],
                restrict_to_file=False,
                timestamp=datetime(2024, 1, 1, tzinfo=timezone(timedelta(0), "UTC")),
                request_id="test-request-id",
            ),
            response=NextEditResponse(
                retrieved_locations=[
                    RetrievalChunk(
                        text="test-retrieval-chunk-text",
                        origin="test-retrieval-chunk-origin",
                        blob_name="test-retrieval-chunk-blob-name",
                        path="foo.py",
                        chunk_index=1,
                        score=0.5,
                        char_offset=10,
                        char_end=20,
                    )
                ],
                suggestions=[
                    NextEditSuggestion(
                        generation_id="test-suggestion-id-1",
                        suggestion_order=0,
                        description_prompt=Tokenization(
                            token_ids=[1, 2],
                            offsets=[1, 2],
                            text="test-existing-code",
                            log_probs=[1.0, 2.0],
                        ),
                        description_output=Tokenization(
                            token_ids=[1, 2],
                            offsets=[1, 2],
                            text="test-suggested-code",
                            log_probs=[1.0, 2.0],
                        ),
                        result=NextEditResult(
                            suggested_edit=ScoredFileHunk(
                                path="foo.py",
                                blob_name="test-blob-name",
                                char_start=10,
                                char_end=20,
                                existing_code="test-existing-code",
                                suggested_code="test-suggested-code",
                                truncation_char=0,
                                change_description="test-change-description",
                                diff_spans=[],
                                localization_score=0.5,
                                editing_score=0.5,
                                editing_score_threshold=1.0,
                            ),
                            unknown_blob_names=["unknown-blob-name"],
                            checkpoint_not_found=False,
                        ),
                    )
                ],
                generations=[],
                timestamp=datetime(2024, 1, 1, tzinfo=timezone(timedelta(0), "UTC")),
            ),
            resolution=None,
            feedback=None,
        ),
        ground_truths=[
            NextEditGroundTruth(
                path="foo.py",
                diff_span=DiffSpan(
                    original=CharRange(10, 20),
                    updated=CharRange(10, 29),
                ),
                crange=CharRange(10, 20),
                old_text="test-existing-code",
                new_text="test-suggested-code",
            )
        ],
    )


@pytest.fixture
def example_datum2() -> HindsightNextEditDatum:
    return HindsightNextEditDatum(
        next_edit_datum=NextEditDatum(
            request_id="test-request-id-2",
            user_id="test-user-id-2",
            user_agent="test-user-agent",
            session_id="test-session-id-2",
            request=NextEditRequest(
                model_name="test-model",
                sequence_id=0,
                lang="",
                instruction="test-instruction",
                recent_changes=[
                    ReplacementText(
                        path="foo.py",
                        blob_name="test-blob-name",
                        present_in_blob=True,
                        replacement_text="test-replacement-text",
                        crange=CharRange(10, 20),
                    )
                ],
                vcs_change=VCSChange(working_directory_changes=[]),
                path="foo.py",
                blob_name="test-blob-name",
                selection_begin_char=10,
                selection_end_char=20,
                prefix="test-prefix",
                selected_text="test-selected-text",
                suffix="test-suffix",
                diagnostics=[],
                mode=NextEditRequest.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,
                scope=NextEditRequest.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,
                change_probability_override=0.0,
                edit_events=[],
                blob_names=["test-blob-name"],
                blocked_locations=[],
                restrict_to_file=False,
                timestamp=datetime(2024, 1, 1, tzinfo=timezone.utc),
                request_id="test-request-id-2",
            ),
            response=NextEditResponse(
                retrieved_locations=[
                    RetrievalChunk(
                        text="test-retrieval-chunk-text",
                        origin="test-retrieval-chunk-origin",
                        blob_name="test-retrieval-chunk-blob-name",
                        path="foo.py",
                        chunk_index=1,
                        score=0.5,
                        char_offset=10,
                        char_end=20,
                    )
                ],
                suggestions=[
                    NextEditSuggestion(
                        generation_id="test-suggestion-id-2",
                        suggestion_order=0,
                        description_prompt=Tokenization(
                            token_ids=[1, 2],
                            offsets=[1, 2],
                            text="test-suggested-code-2",
                            log_probs=[1.0, 2.0],
                        ),
                        description_output=Tokenization(
                            token_ids=[1, 2],
                            offsets=[1, 2],
                            text="test-suggested-code-2",
                            log_probs=[1.0, 2.0],
                        ),
                        result=NextEditResult(
                            suggested_edit=ScoredFileHunk(
                                path="foo.py",
                                blob_name="test-blob-name",
                                char_start=10,
                                char_end=20,
                                existing_code="test-existing-code-2",
                                suggested_code="test-suggested-code-2",
                                truncation_char=0,
                                change_description="test-change-description",
                                diff_spans=[],
                                localization_score=0.5,
                                editing_score=0.5,
                                editing_score_threshold=1.0,
                                suggestion_id="test-suggestion-id-2",
                            ),
                            unknown_blob_names=[],
                            checkpoint_not_found=False,
                        ),
                    )
                ],
                generations=[],
                timestamp=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ),
            resolution=None,
            feedback=None,
        ),
        ground_truths=[
            NextEditGroundTruth(
                path="foo.py",
                diff_span=DiffSpan(
                    original=CharRange(10, 20),
                    updated=CharRange(10, 31),
                ),
                crange=CharRange(10, 20),
                old_text="test-existing-code-2",
                new_text="test-suggested-code-2",
            )
        ],
    )


@pytest.fixture
def example_next_edit_event() -> NextEditRequestIdIssuedEvent:
    return NextEditRequestIdIssuedEvent(
        session_id="test-session-id",
        user_id="test-user-id",
        tenant="test-tenant",
        time=datetime(2024, 1, 1, tzinfo=timezone(timedelta(0), "UTC")),
        file_path="foo.py",
        request_id="test-request-id",
    )


@pytest.fixture
def example_text_edit_event(example_datum) -> TextEditEvent:
    text_edit = TextEditEvent(
        session_id="test-session-id",
        user_id="test-user-id",
        tenant="test-tenant",
        time=datetime(2024, 1, 1, tzinfo=timezone(timedelta(0), "UTC")),
        file_path="foo.py",
        content_changes=[
            ContentChange(
                text=example_datum.ground_truths[0].new_text,
                crange=example_datum.ground_truths[0].crange,
            )
        ],
    )
    return text_edit


@pytest.fixture
def example_blob_cache() -> DictCache[str, PathAndContent]:
    return DictCache(
        mapping={
            "test-blob-name": PathAndContent(
                Path("foo.py"), "test-retrieval-chunk-text"
            ),
            "test-blob-name-2": PathAndContent(
                Path("foo2.py"), "test-retrieval-chunk-text-2"
            ),
        }
    )


def test_dump_load_data(example_datum, example_blob_cache):
    """Test dumping and loading data."""

    example_datum2 = dataclasses.replace(
        example_datum,
        ground_truths=[
            NextEditGroundTruth(
                path="foo.py",
                diff_span=DiffSpan(
                    original=CharRange(10, 20),
                    updated=CharRange(10, 30),
                ),
                crange=CharRange(10, 20),
                old_text="test-old-text-2",
                new_text="test-new-text-2",
            )
        ],
    )
    data = [example_datum, example_datum2]
    dataset = HindsightNextEditDataset(data=data, blobs=example_blob_cache)

    buffer = io.StringIO()
    dataset.dump_data(buffer)

    buffer.seek(0)
    data2 = HindsightNextEditDataset.load_data(buffer)

    assert data2 == dataset.data


def test_iter_data(example_datum, example_blob_cache):
    """Test iterating over the data."""
    example_datum2 = dataclasses.replace(
        example_datum,
        next_edit_datum=dataclasses.replace(
            example_datum.next_edit_datum,
            request=dataclasses.replace(
                example_datum.next_edit_datum.request,
                blob_names=["test-blob-name-2"],
            ),
        ),
        ground_truths=[
            NextEditGroundTruth(
                path="foo2.py",
                diff_span=DiffSpan(
                    original=CharRange(10, 20),
                    updated=CharRange(10, 30),
                ),
                crange=CharRange(10, 20),
                old_text="test-old-text-2",
                new_text="test-new-text-2",
            )
        ],
    )
    data = [example_datum, example_datum2]
    dataset = HindsightNextEditDataset(data=data, blobs=example_blob_cache)

    expected = [
        (
            d,
            dict(
                zip(
                    d.next_edit_datum.request.blob_names,
                    example_blob_cache.get(d.next_edit_datum.request.blob_names),
                )
            ),
        )
        for d in data
    ]
    assert list(dataset) == expected


def test_process_simple_multi_user(
    example_datum, example_datum2, example_next_edit_event, example_text_edit_event
):
    """Test that we don't mix up different users."""
    example_next_edit_event_2 = dataclasses.replace(
        example_next_edit_event,
        user_id="test-user-id-2",
        session_id="test-session-id-2",
        request_id="test-request-id-2",
    )
    new_crange = CharRange(1, 1)
    example_text_edit_event_2 = dataclasses.replace(
        example_text_edit_event,
        user_id="test-user-id-2",
        session_id="test-session-id-2",
        content_changes=[
            ContentChange(
                text=example_datum2.ground_truths[0].new_text,
                crange=new_crange,
            )
        ],
    )

    results = _process(
        data=HindsightNextEditQueryResults(
            events=[
                example_next_edit_event,
                example_next_edit_event_2,
                example_text_edit_event,
                example_text_edit_event_2,
            ],
            next_edit_events=[example_next_edit_event, example_next_edit_event_2],
            next_edits=[
                example_datum.next_edit_datum,
                example_datum2.next_edit_datum,
            ],
        ),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
        process_args=HindsightNextEditProcessArgs(
            time_limit=timedelta(hours=1),
        ),
    )

    assert results.errors == []
    assert results.data == [example_datum, example_datum2]


def test_duplicate_next_edits(
    example_datum, example_next_edit_event, example_text_edit_event
):
    """Test that we only produce one next edit per example_next_edit_event."""
    results = _process(
        data=HindsightNextEditQueryResults(
            events=[
                example_next_edit_event,
                example_next_edit_event,
                example_text_edit_event,
            ],
            next_edit_events=[example_next_edit_event],
            next_edits=[
                example_datum.next_edit_datum,
                example_datum.next_edit_datum,
            ],
        ),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
        process_args=HindsightNextEditProcessArgs(
            time_limit=timedelta(hours=1),
        ),
    )
    assert results.errors == []
    assert results.data == [example_datum]


def test_sample(example_next_edit_event, example_text_edit_event):
    """Test that we sample the correct next edits."""
    events = [
        dataclasses.replace(
            example_next_edit_event, request_id=f"test-request-id-{i:02d}"
        )
        for i in range(100)
    ]
    expected = events[:10]
    events += [example_text_edit_event] * 100
    Random(42).shuffle(events)
    results = _sample(events=events, sample_limit=10)
    assert results == expected


def generate_tenant(name: str) -> DatasetTenant:
    return DatasetTenant(
        name=name,
        project_id="test-project",
        tenant_id="test-tenant-id",
        events_bucket_name="test-events-bucket",
        search_dataset_name="test-search-dataset",
        analytics_dataset_name="test-analytics-dataset",
        dataset_name="test-dataset",
        blob_bucket_name="test-blob-bucket",
        blob_bucket_prefix="test-blob-prefix",
        checkpoint_bucket_name="test-checkpoint-bucket",
        checkpoint_bucket_prefix="test-checkpoint-prefix",
        namespace="test-namespace",
        internal_domain_suffix="test-domain-suffix",
    )


def generate_cache(
    files: list[File],
) -> tuple[DictCache[str, PathAndContent], DictCache[str, Path]]:
    blob_cache = DictCache(
        mapping={
            file.blob_name: PathAndContent(Path(file.path), file.contents)
            for file in files
        }
    )
    path_cache = DictCache(mapping={file.blob_name: Path(file.path) for file in files})
    return blob_cache, path_cache


def test_dataloader_pass_fail(example_datum):
    """Test that the dataloader's pass and fail conditions occur."""
    foo_file_v1 = File("foo.py", "")
    foo_file_v2 = File("foo.py", "fib")
    bar_file_v1 = File("bar.py", "baz")
    arc_file_v1 = File("arc.py", "")
    tenant_name = "tenant1"

    edit_events: list[NextEditRequestIdIssuedEvent] = [
        NextEditRequestIdIssuedEvent(  # This is a normal request. Testing next edit ordering.
            session_id="f",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            request_id="f11",
        ),
        NextEditRequestIdIssuedEvent(  # This is a normal request. Testing correctness.
            session_id="a",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 0, 1, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            request_id="a11",
        ),
        NextEditRequestIdIssuedEvent(  # This request has no corresponding events
            session_id="b",
            user_id="2",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 0, 30, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            request_id="b12",
        ),
        NextEditRequestIdIssuedEvent(  # This request's edited files are not blobbed, so no edited files are found
            session_id="c",
            user_id="3",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 4, 0, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            request_id="c13",
        ),
        NextEditRequestIdIssuedEvent(  # This request is not included in the user or session ID list
            session_id="d",
            user_id="4",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 4, 0, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            request_id="d14",
        ),
        NextEditRequestIdIssuedEvent(  # This request happened too late to be included
            session_id="e",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 9, 0, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            request_id="e15",
        ),
    ]

    text_events: list[TextEditEvent] = [
        TextEditEvent(  # This is for a normal event
            session_id="a",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 1, 0, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            content_changes=[
                ContentChange(
                    text="fib ",
                    crange=CharRange(0, 0),
                )
            ],
        ),
        TextEditEvent(  # This is for a normal event
            session_id="a",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 2, 0, tzinfo=timezone.utc),
            file_path=bar_file_v1.path,
            content_changes=[
                ContentChange(
                    text="fob ",
                    crange=CharRange(0, 0),
                )
            ],
        ),
        TextEditEvent(  # This is for a normal event
            session_id="f",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 2, 1, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            content_changes=[
                ContentChange(
                    text="fibfob ",
                    crange=CharRange(0, 0),
                )
            ],
        ),
        TextEditEvent(  # This event doesn't edit any saved files
            session_id="c",
            user_id="3",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 6, 0, tzinfo=timezone.utc),
            file_path=arc_file_v1.path,
            content_changes=[
                ContentChange(
                    text="arc ",
                    crange=CharRange(0, 0),
                )
            ],
        ),
    ]

    query = HindsightNextEditQueryResults(
        events=list(edit_events + text_events),
        next_edit_events=edit_events,
        next_edits=[
            dataclasses.replace(
                example_datum.next_edit_datum,
                request_id="f11",
                user_id="1",
                request=dataclasses.replace(
                    example_datum.next_edit_datum.request,
                    timestamp=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
                    request_id="f11",
                    path=foo_file_v2.path,
                    prefix="",
                    selected_text="",
                    suffix="fib",
                    blob_name=foo_file_v2.blob_name,
                    blob_names=[foo_file_v1.blob_name, bar_file_v1.blob_name],
                    recent_changes=[
                        ReplacementText(
                            path=foo_file_v1.path,
                            blob_name=foo_file_v1.blob_name,
                            present_in_blob=False,
                            replacement_text="fob ",
                            crange=CharRange(0, 0),
                        ),
                        ReplacementText(
                            path=bar_file_v1.path,
                            blob_name=bar_file_v1.blob_name,
                            present_in_blob=True,
                            replacement_text="pretext ",
                            crange=CharRange(0, 0),
                        ),
                    ],
                ),
                response=example_datum.next_edit_datum.response,
            ),
            dataclasses.replace(
                example_datum.next_edit_datum,
                request_id="a11",
                user_id="1",
                request=dataclasses.replace(
                    example_datum.next_edit_datum.request,
                    timestamp=datetime(2024, 1, 1, 0, 0, 1, tzinfo=timezone.utc),
                    request_id="a11",
                    path=foo_file_v2.path,
                    prefix="",
                    selected_text="",
                    suffix="fib",
                    blob_name=foo_file_v2.blob_name,
                    blob_names=[foo_file_v1.blob_name, bar_file_v1.blob_name],
                    recent_changes=[
                        ReplacementText(
                            path=foo_file_v1.path,
                            blob_name=foo_file_v1.blob_name,
                            present_in_blob=False,
                            replacement_text="fob ",
                            crange=CharRange(0, 0),
                        ),
                        ReplacementText(
                            path=bar_file_v1.path,
                            blob_name=bar_file_v1.blob_name,
                            present_in_blob=True,
                            replacement_text="pretext ",
                            crange=CharRange(0, 0),
                        ),
                    ],
                ),
                response=example_datum.next_edit_datum.response,
            ),
            dataclasses.replace(
                example_datum.next_edit_datum,
                request_id="b12",
                user_id="2",
                request=dataclasses.replace(
                    example_datum.next_edit_datum.request,
                    timestamp=datetime(2024, 1, 1, 0, 0, 30, tzinfo=timezone.utc),
                    request_id="b12",
                    path=foo_file_v1.path,
                    prefix="",
                    selected_text="",
                    suffix="",
                    blob_name=foo_file_v1.blob_name,
                    blob_names=[foo_file_v1.blob_name, bar_file_v1.blob_name],
                    recent_changes=[],
                ),
                response=example_datum.next_edit_datum.response,
            ),
            dataclasses.replace(
                example_datum.next_edit_datum,
                request_id="c13",
                user_id="3",
                request=dataclasses.replace(
                    example_datum.next_edit_datum.request,
                    timestamp=datetime(2024, 1, 1, 0, 4, 0, tzinfo=timezone.utc),
                    request_id="c13",
                    path=foo_file_v1.path,
                    prefix="",
                    selected_text="",
                    suffix="",
                    blob_name=foo_file_v1.blob_name,
                    blob_names=[foo_file_v1.blob_name],
                    recent_changes=[],
                ),
                response=example_datum.next_edit_datum.response,
            ),
            dataclasses.replace(
                example_datum.next_edit_datum,
                request_id="d14",
                user_id="4",
                request=dataclasses.replace(
                    example_datum.next_edit_datum.request,
                    timestamp=datetime(2024, 1, 1, 0, 4, 0, tzinfo=timezone.utc),
                    request_id="d14",
                    path=foo_file_v1.path,
                    prefix="",
                    selected_text="",
                    suffix="",
                    blob_name=foo_file_v1.blob_name,
                    blob_names=[foo_file_v1.blob_name],
                    recent_changes=[],
                ),
                response=example_datum.next_edit_datum.response,
            ),
            dataclasses.replace(
                example_datum.next_edit_datum,
                request_id="e15",
                user_id="5",
                request=dataclasses.replace(
                    example_datum.next_edit_datum.request,
                    timestamp=datetime(2024, 1, 1, 0, 9, 0, tzinfo=timezone.utc),
                    request_id="e15",
                    path=foo_file_v1.path,
                    prefix="",
                    selected_text="",
                    suffix="",
                    blob_name=foo_file_v1.blob_name,
                    blob_names=[foo_file_v1.blob_name],
                    recent_changes=[],
                ),
                response=example_datum.next_edit_datum.response,
            ),
        ],
    )

    blob_cache, path_cache = generate_cache([foo_file_v1, bar_file_v1, arc_file_v1])

    dataloader = NextEditIntermediateDataLoader(
        tenant=generate_tenant(tenant_name),
        query=query,
        blob_cache=blob_cache,
        path_cache=path_cache,
        timestamp_end=datetime(2024, 1, 1, 0, 10, 0, tzinfo=timezone.utc),
    )

    result = dataloader.iterator(
        timedelta(minutes=5),
        user_ids=["1", "2", "3"],
        session_ids=["a", "b", "c", "e", "f"],
    )

    result_list = list(result)

    assert len(result_list) == 2

    # Testing that the request ordering is correct
    assert result_list[0].request == query.next_edits[0].request

    assert result_list[1].request == query.next_edits[1].request

    assert result_list[1].files_for_events[foo_file_v2.blob_name] == foo_file_v2
    assert result_list[1].files_for_events[bar_file_v1.blob_name] == bar_file_v1

    assert result_list[1].future_events == text_events[:2]

    assert result_list[1].request.request_id == "a11"

    assert dataloader.num_processed_requests == 4

    assert dataloader.error_details.no_events_found == ["b12"]
    assert dataloader.error_details.some_edited_files_not_found == {
        "c13": [arc_file_v1.path]
    }
    assert dataloader.error_details.no_edited_files_found == ["c13"]


def test_dataloader_get_correct_files(example_datum):
    """Test that the dataloader reconstructs files correctly."""
    foo_file_v1 = File("foo.py", "")
    foo_file_v2 = File("foo.py", "fib")
    bar_file_v1 = File("bar.py", "baz")
    tenant_name = "tenant1"

    edit_event = NextEditRequestIdIssuedEvent(
        session_id="a",
        user_id="1",
        tenant=tenant_name,
        time=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
        file_path=foo_file_v1.path,
        request_id="a11",
    )

    text_events = [
        TextEditEvent(
            session_id="a",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 1, 0, tzinfo=timezone.utc),
            file_path=foo_file_v1.path,
            content_changes=[
                ContentChange(
                    text="fib ",
                    crange=CharRange(0, 0),
                )
            ],
        ),
        TextEditEvent(
            session_id="a",
            user_id="1",
            tenant=tenant_name,
            time=datetime(2024, 1, 1, 0, 2, 0, tzinfo=timezone.utc),
            file_path=bar_file_v1.path,
            content_changes=[
                ContentChange(
                    text="fob ",
                    crange=CharRange(0, 0),
                )
            ],
        ),
    ]

    recent_changes = [
        ReplacementText(
            path=foo_file_v1.path,  # This edit would be included, but it is overriden by foo_file_v2
            blob_name=foo_file_v1.blob_name,
            present_in_blob=False,
            replacement_text="fob ",
            crange=CharRange(0, 0),
        ),
        ReplacementText(
            path=bar_file_v1.path,  # This edit should not be included
            blob_name=bar_file_v1.blob_name,
            present_in_blob=True,
            replacement_text="pretext ",
            crange=CharRange(0, 0),
        ),
    ]

    query = HindsightNextEditQueryResults(
        events=[edit_event, text_events[0], text_events[1]],
        next_edit_events=[edit_event],
        next_edits=[
            dataclasses.replace(
                example_datum.next_edit_datum,
                request_id="a11",
                user_id="1",
                request=dataclasses.replace(
                    example_datum.next_edit_datum.request,
                    timestamp=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
                    request_id="a11",
                    path=foo_file_v2.path,
                    prefix="",
                    selected_text="",
                    suffix="fib",
                    blob_name=foo_file_v2.blob_name,  # foo_file_v2 overrides foo_file_v1
                    blob_names=[foo_file_v1.blob_name, bar_file_v1.blob_name],
                    recent_changes=recent_changes,
                ),
                response=example_datum.next_edit_datum.response,
            ),
        ],
    )

    blob_cache, path_cache = generate_cache([foo_file_v1, bar_file_v1])

    dataloader = NextEditIntermediateDataLoader(
        tenant=generate_tenant(tenant_name),
        query=query,
        blob_cache=blob_cache,
        path_cache=path_cache,
        timestamp_end=datetime(2024, 1, 1, 0, 10, 0, tzinfo=timezone.utc),
    )

    result = dataloader.iterator(
        timedelta(minutes=5), user_ids=["1"], session_ids=["a"]
    )

    result_list = list(result)

    assert len(result_list) == 1
    assert result_list[0].request == query.next_edits[0].request

    assert result_list[0].files_for_events[foo_file_v2.blob_name] == foo_file_v2
    assert result_list[0].files_for_events[bar_file_v1.blob_name] == bar_file_v1

    assert result_list[0].future_events == query.events[1:3]


def test_validate_text_edit_event():
    """Test that the text edit event validation works."""

    old_content = (
        "the fox"  # After the text edit event, the file will contain "the quick fox"
    )
    new_content = "the quick fox"

    hash_char_ranges = [CharRange(4, 5), CharRange(6, 7)]
    changed_content = "".join(
        [new_content[x.to_slice()] for x in hash_char_ranges]
    )  # This should be "qi"

    change_hash = hashlib.sha256(changed_content.encode()).hexdigest()

    text_edit_event = TextEditEvent(
        session_id="test-session-id",
        user_id="test-user-id",
        tenant="test-tenant",
        time=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone(timedelta(0), "UTC")),
        file_path="foo.py",
        content_changes=[
            ContentChange(
                text="quick ",
                crange=CharRange(4, 4),
            ),
        ],
        after_changes_hash=change_hash,
        hash_char_ranges=hash_char_ranges,
    )

    assert validate_text_edit_event(text_edit_event, old_content)


def test_validate_without_validation_information():
    """Test that text edit events revert to default behavior without validation information."""

    text_edit_event = TextEditEvent(
        session_id="test-session-id",
        user_id="test-user-id",
        tenant="test-tenant",
        time=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone(timedelta(0), "UTC")),
        file_path="foo.py",
        content_changes=[
            ContentChange(
                text="quick ",
                crange=CharRange(4, 4),
            ),
        ],
    )

    assert validate_text_edit_event(text_edit_event, "", default=True)

    assert not validate_text_edit_event(text_edit_event, "", default=False)
