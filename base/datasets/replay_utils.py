"""Utilities for replaying requests."""

import logging
import os
from collections.abc import Iterable, Sequence
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Optional

from dataclasses_json import dataclass_json
from google.cloud import storage

from base.augment_client.client import AugmentClient, UploadContent
from base.blob_names import blob_names_pb2
from base.datasets.completion import CompletionDatum, CompletionResponse
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import (
    BlobCache,
    CheckpointCache,
    GCSBlobCache,
    GCSCheckpointCache,
    resolve_checkpoints,
)
from base.datasets.itertools import batched
from base.datasets.tenants import DatasetTenant


@dataclass_json
@dataclass
class ReplayDatum(CompletionDatum):
    """The replayed data includes a new request id and response."""

    replayed_request_id: Optional[str] = None
    replayed_response: Optional[CompletionResponse] = None


def get_augment_client(
    api_token_env_var: str = "AUGMENT_TOKEN",  # nosec
    api_token_path: str = "~/.config/augment/api_token",  # nosec
    **kwargs,
) -> AugmentClient:
    """Returns an augment client for the given url and token."""
    if "token" in kwargs:
        raise ValueError("API token should come from a file!")

    api_token = None
    if not api_token:
        api_token = os.getenv(api_token_env_var)
    if not api_token:
        path = Path(api_token_path)
        path = path.expanduser()
        if path.exists():
            api_token = path.read_text(encoding="utf-8")
    if not api_token:
        raise ValueError("API Token not found: ${api_token_env_var}, {api_token_path}")

    return AugmentClient(
        token=api_token,
        **kwargs,
    )


def replay(
    client: AugmentClient,
    completion_host_name: str,
    completions: Sequence[CompletionDatum],
) -> list[ReplayDatum]:
    """Replays the given request insight data against the given model client.

    Args:
        client: The client to use for replaying the data.
        completion_host_name: The name of the completion host to use for replaying the data.
        completions: The request insight data to replay.

    Returns:
        The replayed data.
    """
    model_client = client.client_for_model(completion_host_name)
    replayed_responses = []
    total = len(completions)
    for idx, datum in enumerate(completions):
        print(f"({idx} of {total}) Replaying request {datum.request_id}")

        request = datum.request
        response = model_client.complete(
            prompt=request.original_prefix,
            suffix=request.original_suffix,
            path=request.path,
            max_tokens=request.output_len,
            memories=request.blob_names,
            blob_name=request.position and request.position.blob_name,
            prefix_begin=request.position and request.position.prefix_begin,
            cursor_position=request.position and request.position.cursor_position,
            suffix_end=request.position and request.position.suffix_end,
        )
        replayed_responses.append(
            ReplayDatum(
                request_id=datum.request_id,
                user_id=datum.user_id,
                user_agent=datum.user_agent,
                request=datum.request,
                response=datum.response,
                resolution=datum.resolution,
                replayed_request_id=str(response.request_id),
                replayed_response=CompletionResponse(
                    text=response.text,
                    model=completion_host_name,
                    # TODO(arun): We don't track skipped fields in the client dataclasses.
                    skipped_suffix="",
                    suffix_replacement_text="",
                    unknown_blob_names=response.unknown_memory_names,
                    retrieved_chunks=[],
                    timestamp=datetime.now(),
                    tokens=[],
                    # TODO(arun): We don't track log probs in the client dataclasses.
                    token_log_probs=[],
                    prompt_tokens=[],
                ),
            )
        )

        print(f"--> Replayed as {response.request_id}")
    return replayed_responses


def ensure_blobs_exist(
    client: AugmentClient,
    blob_cache: BlobCache,
    completions: Iterable[CompletionDatum],
    model_name: str,
    upload_batch_size: int = 100,
    find_missing_batch_size: int = 2000,
) -> None:
    """Ensure the blobs for a given set of completions exist on the target endpoint.

    Args:
        client: The client to use for the target endpoint.
        blob_cache: The dataset to use for uploading the data.
        completions: The completions to upload.
        model_name: Model name to use for finding missing blobs.
        upload_batch_size: The number of blobs to upload in a single batch.
        find_missing_batch_size: The number of blobs to find missing in a single batch.
    """
    upload_and_ensure_blobs_exist(
        client,
        blob_cache,
        {blob_name for datum in completions for blob_name in datum.request.blob_names},
        model_name,
        upload_batch_size,
        find_missing_batch_size,
    )


def upload_and_ensure_blobs_exist(
    client: AugmentClient,
    blob_cache: BlobCache,
    blob_names: set[str],
    model_name: str,
    upload_batch_size: int = 100,
    find_missing_batch_size: int = 2000,
) -> None:
    """Uploads and ensure `blob_names` exist on the `client` endpoint."""
    to_upload = []
    for batch in batched(list(blob_names), find_missing_batch_size):
        # TODO(vzhao): Consider move the batching process logic to the client.
        to_upload.extend(
            client.find_missing(model_name, list(batch)).unknown_memory_names
        )

    logging.info("Uploading %d missing blobs.", len(to_upload))

    content_to_upload = [
        UploadContent(path_content.content, str(path_content.path))
        if path_content is not None
        else None
        for path_content in blob_cache.get(to_upload)
    ]

    missing_blob_names = [
        blob_name
        for blob_name, maybe_content in zip(to_upload, content_to_upload)
        if maybe_content is None
    ]
    if missing_blob_names:
        logging.warning("Couldn't retrieve blobs: %s", missing_blob_names)

    found_blob_names = [
        blob_name
        for blob_name, maybe_content in zip(to_upload, content_to_upload)
        if maybe_content is not None
    ]

    uploaded_blob_names = []
    for batch in batched(
        [
            maybe_content
            for maybe_content in content_to_upload
            if maybe_content is not None
        ],
        upload_batch_size,
    ):
        uploaded_blob_names = client.batch_upload(batch)

    mismatched_blob_names = [
        (blob_name, uploaded_blob_name)
        for blob_name, uploaded_blob_name in zip(found_blob_names, uploaded_blob_names)
        if blob_name != uploaded_blob_name
    ]
    if mismatched_blob_names:
        logging.error(
            "Mismatch between expected blob names and what the server returned: %s",
            mismatched_blob_names,
        )

    if uploaded_blob_names:
        # TODO(arun): We should wait for the uploaded files to index, but until that
        # change makes it in, we'll just "sleep" for a few seconds.
        logging.info(
            "Be aware that it takes time for the newly uploaded blobs to be indexed..."
        )


def index_status(
    client: AugmentClient,
    blob_names: set[str],
    model_name: str,
    find_missing_batch_size: int = 2000,
) -> None:
    """Check the index status of the blobs for a given set of completions."""
    unknown_memory_names, nonindexed_blob_names = [], []
    for batch in batched(list(blob_names), find_missing_batch_size):
        rst = client.find_missing(model_name, list(batch))
        unknown_memory_names.extend(rst.unknown_memory_names)
        nonindexed_blob_names.extend(rst.nonindexed_blob_names)
    print(f"Unknown blob names: {len(unknown_memory_names)}")
    print(f"Nonindexed blob names: {len(nonindexed_blob_names)}")


def get_blob_cache(
    tenant: DatasetTenant,
    service_account_file: Optional[Path] = None,
    blob_cache_size_bytes: int = 2**30,
    blob_cache_num_threads: int = 32,
) -> GCSBlobCache:
    """Returns `GCSBlobCache` for the given tenant."""
    gcp_creds, _ = get_gcp_creds(service_account_file)
    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
    blob_cache = GCSBlobCache(
        blob_bucket,
        tenant.blob_bucket_prefix,
        blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    return blob_cache


def get_checkpoint_cache(
    tenant: DatasetTenant,
    service_account_file: Optional[Path] = None,
    blob_cache_size_bytes: int = 2**30,
    blob_cache_num_threads: int = 32,
) -> CheckpointCache:
    """Returns `GCSCheckpointCache` for the given tenant."""
    gcp_creds, _ = get_gcp_creds(service_account_file)
    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
    checkpoint_cache = GCSCheckpointCache(
        checkpoint_bucket,
        tenant.checkpoint_bucket_prefix,
        blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    return checkpoint_cache


def resolve_checkpoint(
    blobs: blob_names_pb2.Blobs, checkpoint_cache: CheckpointCache
) -> list[str]:
    """Resolve the checkpoint into blob names."""
    return resolve_checkpoints(checkpoint_cache, [blobs])[0]
