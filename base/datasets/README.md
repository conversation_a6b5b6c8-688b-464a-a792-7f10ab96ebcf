# Datasets derived from user interactions.

This directory includes code to create datasets from user interactions (
e.g., completions). It is intended to be used in research for first-party tenants (i.e.
dogfood and contractors) and in services as a diagnostic metric.

**IMPORTANT:** This dataset can include our source code and represents valuable IP.
Treat it with great care: don't upload it to third-party services (e.g., Weights and
Biases). With great power comes great responsibility.

# Usage

Here is a minimal example of iterating through the dataset:

```python
from datetime import datetime
from google.cloud import bigquery, storage

from base.datasets.gcs_blob_cache import GCSBlobCache
from base.datasets.completion_dataset import CompletionDataset
from base.datasets import tenants

dataset = CompletionDataset.create(
    tenant=tenants.DOGFOOD,
    filters=CompletionDataset.Filters(
        timestamp_begin=datetime(2024,1,24),
        timestamp_end=datetime(2024,1,25),
        min_reject_resolution_time_ms=350,
    ),
)
for request_id, request, response, resolution in dataset.get_completions():
    # This is how you can get the actual blobs for a request.
    blobs = list(blob_cache.get(request.blob_names))
    print(request)
    print(len(blobs))
    print(response)
    print(resolution)
```

# Dataclasses

All research users should only use the dataclasses exported in the corresponding files
(e.g. `completion.py`, `edit.py`) and *not* use the request insight protos. The protos
are liable to change and bigquery will store data from multiple evolutions of these
protos. The dataclasses here are responsible for unifying these different versions.

Many of the dataclasses below are near-duplicates of similar dataclasses in services.
We are currently treating these two as separate copies to avoid adding dependencies
from base into services.
