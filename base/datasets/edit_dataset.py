"""Dataset for edits logged via RequestInsight."""

import logging
import re
from collections.abc import Iterable, Sequence
from dataclasses import dataclass, field, replace
from datetime import datetime
from pathlib import Path
from typing import Literal, Optional, TypedDict, cast

from dataclasses_json import config as json_config
from dataclasses_json import dataclass_json
from google.cloud import bigquery, storage  # type: ignore
from google.protobuf.json_format import ParseDict
from marshmallow import fields
from typing_extensions import assert_never

from base.datasets.edit import (
    EditDatum,
    EditPosition,
    EditRequest,
    EditResolution,
    EditResponse,
)
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import (
    BlobCache,
    CheckpointCache,
    CheckpointContent,
    GCSBlobCache,
    GCSCheckpointCache,
    PathAndContent,
)
from base.datasets.itertools import batched
from base.datasets.pipeline import Pipeline
from base.datasets.tenants import DatasetTenant
from services.request_insight import request_insight_pb2

logger = logging.getLogger(__name__)


class _Row(TypedDict):
    """A typed dict representing a row in the BigQuery table."""

    request_id: str
    user_id: str
    user_agent: str
    request_json: dict
    request_timestamp: datetime
    response_json: dict
    response_timestamp: datetime
    resolution_json: Optional[dict]
    resolution_timestamp: Optional[datetime]


class BlobStore:
    """Public interface to retrieve blob content from GCS.

    External clients, like eval, may use this to retrieve blob content from GCS
    from a previously exported dataset.
    """

    def __init__(
        self, tenant: DatasetTenant, service_account_file: Optional[Path] = None
    ):
        gcp_creds, _ = get_gcp_creds(service_account_file)

        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)

        # We can expose the cache configuration if a client absolutely needs to worry about it.
        blob_cache_size_bytes: int = 2**30
        blob_cache_num_threads: int = 32
        self._blob_cache = GCSBlobCache(
            blob_bucket,
            tenant.blob_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )

    def get_blobs(self, keys: Iterable[str]) -> Iterable[Optional[PathAndContent]]:
        return self._blob_cache.get(keys)


class EditDataset:
    """Dataset for completions logged via RequestInsight."""

    OrderBy = Literal["request_id", "request_timestamp", None]
    """Options to order this dataset by."""

    @dataclass_json
    @dataclass
    class Filters:
        """Filters for the dataset."""

        timestamp_begin: Optional[datetime] = field(
            default=None,
            metadata=json_config(
                encoder=lambda dt: dt.strftime("%Y-%m-%d"),
                mm_field=fields.DateTime(format="%Y-%m-%d"),
            ),
        )
        """If set, only return events with a timestamp >= than this."""
        timestamp_end: Optional[datetime] = field(
            default=None,
            metadata=json_config(
                encoder=lambda dt: dt.strftime("%Y-%m-%d"),
                mm_field=fields.DateTime(format="%Y-%m-%d"),
            ),
        )
        request_ids: Optional[list[str]] = None
        """If set, only return events with a request ID in this list."""
        denied_request_ids: Optional[list[str]] = None
        """If set, only return events with a request ID that are not in this list."""
        with_resolution: bool = False
        """If set, only return events with feedback."""

    def __init__(
        self,
        rows: Iterable[_Row],
        num_rows: Optional[int],
        blob_cache: BlobCache,
        checkpoint_cache: CheckpointCache,
        page_size: int = 128,
        queue_size: int = 10,
        reconstruct_request_files: bool = False,
    ):
        """Create a dataset of completions.

        Args:
            rows: The rows of the dataset. This is typically created by calling
                `.create()`, but is exposed for testing.
            num_rows: The number of rows in the dataset.
            blob_cache: The blob cache to use.
            page_size: The page size to use when querying BigQuery. The default here
                is a reasonable baseline, and shouldn't need to be fiddled with much.
            queue_size: The size of the queue to use when fetching blobs. We use this
                queue to pipeline bigquery and GCS requests, and set a max size to
                avoid memory issues if users use this dataset in a notebook and don't
                iterate through the whole dataset.
            reconstruct_request_files: If set, reconstruct the entire request file using
                the prefix and suffix offsets.
        """
        self._rows = rows
        self._num_rows = num_rows
        self._blob_cache = blob_cache
        self._checkpoint_cache = checkpoint_cache
        self._page_size = page_size
        self._queue_size = queue_size
        self._should_reconstruct_files = reconstruct_request_files

    @classmethod
    def create(
        cls,
        tenant: DatasetTenant,
        filters: Filters = Filters(),
        limit: Optional[int] = None,
        order_by: OrderBy = None,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        page_size: int = 128,
        queue_size: int = 10,
        service_account_file: Optional[Path] = None,
        reconstruct_request_files: bool = False,
    ):
        """Create a dataset of edits.

        Args:
            tenant: The tenant to use.
            filters: Filters to apply to the dataset.
            limit: If set, the maximum number of rows to return.
            order_by: If set, the order to return the rows in.
            blob_cache_size_bytes: The size of the blob cache in bytes.
            blob_cache_num_threads: The number of threads to use for the blob cache.
            page_size: The page size to use when querying BigQuery. The default here
                is a reasonable baseline, and shouldn't need to be fiddled with much.
            queue_size: The size of the queue to use when fetching blobs. We use this
                queue to pipeline bigquery and GCS requests, and set a max size to
                avoid memory issues if users use this dataset in a notebook and don't
                iterate through the whole dataset.
            service_account_file: The path to a service account file to use to get
                resources from BigQuery and GCS.
                If not set, the default credentials will be used.
            reconstruct_request_files: If set, reconstruct the entire request file using
                the prefix and suffix offsets.
        """
        gcp_creds, _ = get_gcp_creds(service_account_file)

        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        blob_cache = GCSBlobCache(
            blob_bucket,
            tenant.blob_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
        checkpoint_cache = GCSCheckpointCache(
            checkpoint_bucket,
            tenant.checkpoint_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        bigquery_client = bigquery.Client(
            project=tenant.project_id, credentials=gcp_creds
        )

        query = _build_query(tenant.dataset_name, tenant.name, filters, order_by, limit)
        logger.info("Querying BigQuery: %s", query)
        rows = bigquery_client.query_and_wait(query, page_size=page_size)

        return cls(
            rows,
            cast(Optional[int], rows.total_rows),
            blob_cache=blob_cache,
            checkpoint_cache=checkpoint_cache,
            page_size=page_size,
            queue_size=queue_size,
            reconstruct_request_files=reconstruct_request_files,
        )

    @property
    def num_rows(self) -> Optional[int]:
        """Try to get the number of rows."""
        return self._num_rows

    def get_blobs(self, keys: Iterable[str]) -> Iterable[Optional[PathAndContent]]:
        return self._blob_cache.get(keys)

    def get_entries(self) -> Iterable[EditDatum]:
        # NOTE(arun): To hide the (often considerable) network latency in getting data
        # from bigquery, and blobs from GCS, we process the data in pipelined batches.
        # Getting data in batches speeds up the script by ~5-10x, and pipelining the
        # script speeds it up by another factor of 2-3x.
        pipeline = (
            Pipeline.from_source(batched(self._rows, self._page_size))
            .and_then(self._process_batch)
            .run(max_queue_size=self._queue_size)
        )
        for batch in pipeline:
            yield from batch

    def _process_batch(self, batch: Iterable[_Row]) -> Iterable[EditDatum]:
        request_pbs = [
            ParseDict(
                row["request_json"],
                request_insight_pb2.RIEditRequest(),
                ignore_unknown_fields=True,
            )
            for row in batch
        ]

        requests = [
            self._build_request(request_pb, row["request_timestamp"])
            for row, request_pb in zip(batch, request_pbs)
        ]
        # Reconstruct after resolving, since we will remove the current
        # blob_name from blob_names.
        requests = self._resolve_checkpoints(requests, request_pbs)
        if self._should_reconstruct_files:
            requests = self._reconstruct_files(requests)

        responses = [
            self._build_response(
                ParseDict(
                    row["response_json"],
                    request_insight_pb2.RIEditResponse(),
                    ignore_unknown_fields=True,
                ),
                request_pb,
                row["response_timestamp"],
            )
            for row, request_pb in zip(batch, request_pbs)
        ]

        resolutions = [
            (
                self._build_resolution(
                    ParseDict(
                        row["resolution_json"],
                        request_insight_pb2.EditResolution(),
                        ignore_unknown_fields=True,
                    ),
                    row["resolution_timestamp"],
                )
                if (
                    row["resolution_json"] is not None
                    and row["resolution_timestamp"] is not None
                )
                else None
            )
            for row in batch
        ]

        return [
            EditDatum(
                request_id=row["request_id"],
                user_id=row["user_id"],
                user_agent=row["user_agent"],
                request=request,
                response=response,
                resolution=resolution,
            )
            for row, request, response, resolution in zip(
                batch, requests, responses, resolutions
            )
        ]

    def _build_request(
        self, proto: request_insight_pb2.RIEditRequest, timestamp: datetime
    ) -> EditRequest:
        prefix = proto.request.prefix
        suffix = proto.request.suffix
        if proto.request.position.blob_name:
            position = EditPosition(
                blob_name=proto.request.position.blob_name,
                prefix_begin=proto.request.position.prefix_begin,
                suffix_end=proto.request.position.suffix_end,
                original_prefix_length=len(proto.request.prefix),
                original_suffix_length=len(proto.request.suffix),
            )
        else:
            position = None

        return EditRequest(
            prefix=prefix,
            selected_text=proto.request.selected_text,
            instruction=proto.request.instruction,
            suffix=suffix,
            path=proto.request.path,
            lang=proto.request.lang,
            blob_names=[],
            timestamp=timestamp,
            position=position,
        )

    def _build_response(
        self,
        response_proto: request_insight_pb2.RIEditResponse,
        request_proto: request_insight_pb2.RIEditRequest,
        timestamp: datetime,
    ) -> EditResponse:
        return EditResponse(
            timestamp=timestamp,
            text=response_proto.response.text,
            model_name=request_proto.request.model_name,
            unknown_blob_names=list(response_proto.response.unknown_blob_names),
            checkpoint_not_found=response_proto.response.checkpoint_not_found,
        )

    def _build_resolution(
        self,
        resolution_proto: request_insight_pb2.EditResolution,
        timestamp: datetime,
    ) -> EditResolution:
        return EditResolution(
            is_accepted=resolution_proto.is_accepted,
            annotated_text=resolution_proto.annotated_text,
            annotated_instruction=resolution_proto.annotated_instruction,
            timestamp=timestamp,
        )

    def _reconstruct_files(
        self, requests: Sequence[EditRequest]
    ) -> Sequence[EditRequest]:
        """Reconstruct the complete prefix and suffix given position information.

        This method is batched to be more efficient with getting blobs from GCS.
        """
        # Remove current file's blob name and update the prefix/suffix.
        cached_files = self._blob_cache.get(
            [
                request.position.blob_name if request.position is not None else ""
                for request in requests
            ]
        )

        def _update_request(
            request: EditRequest, cached_file: Optional[PathAndContent]
        ) -> EditRequest:
            if request.position is None or cached_file is None:
                return request
            blob_names = list(request.blob_names)
            if request.position.blob_name in blob_names:
                blob_names.remove(request.position.blob_name)
            prefix = (
                cached_file.content[: request.position.prefix_begin] + request.prefix
            )
            suffix = request.suffix + cached_file.content[request.position.suffix_end :]
            return replace(request, prefix=prefix, suffix=suffix, blob_names=blob_names)

        return [
            _update_request(request, cached_file)
            for request, cached_file in zip(requests, cached_files)
        ]

    def _resolve_checkpoints(
        self,
        requests: Sequence[EditRequest],
        request_pbs: Sequence[request_insight_pb2.RIEditRequest],
    ) -> Sequence[EditRequest]:
        """Resolve the checkpoints into blob names for each request.

        Requests not in checkpoint format will remain with blob_names unchanged.
        This method is batched to be more efficient with getting blobs from GCS.
        """
        checkpoints = self._checkpoint_cache.get(
            [
                request_pb.request.blobs.baseline_checkpoint_id
                if request_pb.request.blobs.HasField("baseline_checkpoint_id")
                else ""
                for request_pb in request_pbs
            ]
        )

        def _update_request(
            request: EditRequest,
            request_pb: request_insight_pb2.RIEditRequest,
            checkpoint: Optional[CheckpointContent],
        ) -> EditRequest:
            if request.blob_names:
                return request

            # New checkpoint format uses bytes for blob names, but the actual keys
            # in BigTable are hex encoded.
            added_blob_names = set(
                name.hex() for name in request_pb.request.blobs.added
            )
            deleted_blob_names = set(
                name.hex() for name in request_pb.request.blobs.deleted
            )
            checkpoint_blob_names = set(
                checkpoint.blob_names if checkpoint is not None else []
            )
            blob_names = sorted(
                list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
            )

            return replace(request, blob_names=blob_names)

        return [
            _update_request(request, request_pb, checkpoint)
            for request, request_pb, checkpoint in zip(
                requests, request_pbs, checkpoints
            )
        ]


def _build_query(
    dataset_name: str,
    tenant_name: str,
    filters: EditDataset.Filters,
    order_by: EditDataset.OrderBy = None,
    limit: Optional[int] = None,
) -> str:
    matcher = re.compile(r"^[a-zA-Z0-9_\-]+$")

    # Some very light weight input validation to prevent SQL injections.
    if matcher.match(dataset_name) is None:
        raise ValueError(f"Invalid dataset name: {dataset_name}")
    if matcher.match(tenant_name) is None:
        raise ValueError(f"Invalid tenant name: {tenant_name}")
    if filters.request_ids:
        for request_id in filters.request_ids:
            if matcher.match(request_id) is None:
                raise ValueError(f"Invalid request ID in request_ids: {request_id}")
    if filters.denied_request_ids:
        for request_id in filters.denied_request_ids:
            if matcher.match(request_id) is None:
                raise ValueError(
                    f"Invalid request ID in denied_request_ids: {request_id}"
                )

    filter_exprs = {
        "request": [],
        "response": [],
        "resolution": [],
        "metadata": [],
        "all": [],
    }
    for table in filter_exprs:
        if table == "all":
            continue
        filter_exprs[table].append(f"tenant = '{tenant_name}'")
        if filters.timestamp_begin:
            filter_exprs[table].append(
                f"time >= '{filters.timestamp_begin.isoformat()}'"
            )
        if filters.timestamp_end:
            filter_exprs[table].append(f"time < '{filters.timestamp_end.isoformat()}'")
        if filters.request_ids:
            escaped_request_ids = [f'"{r}"' for r in filters.request_ids]
            filter_exprs[table].append(
                f"request_id IN ({','.join(escaped_request_ids)})"
            )
        if filters.denied_request_ids:
            escaped_denied_request_ids = [f'"{r}"' for r in filters.denied_request_ids]
            filter_exprs[table].append(
                f"request_id NOT IN ({','.join(escaped_denied_request_ids)})"
            )

    ensure_tables = set()
    if filters.with_resolution:
        ensure_tables.add("resolution")

    # Add all the tables that have to be present for the filters to work.
    filter_exprs["all"].extend(f"{table}_json IS NOT NULL" for table in ensure_tables)

    filter_expr = {
        table: "\n".join(f"AND {expr}" for expr in filter_exprs[table])
        for table in filter_exprs
    }

    if order_by is None:
        order_by_expr = ""
    elif order_by == "request_id":
        order_by_expr = "ORDER BY request.request_id"
    elif order_by == "request_timestamp":
        order_by_expr = "ORDER BY request.time"
    elif order_by == "-request_timestamp":
        order_by_expr = "ORDER BY request_timestamp DESC"
    else:
        assert_never(order_by)

    limit_expr = f"LIMIT {limit}" if limit else ""

    # NOTE(arun): We have sanitized all the input going into this expression.
    # Furthermore, we run this query with read-only permissions, so injections shouldn't
    # even be possible.
    return f"""
        WITH
            request AS (
                SELECT
                    request_id,
                    raw_json AS request_json,
                    time AS request_timestamp
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'edit_host_request'
                {filter_expr['request']}
            ),
            response AS (
                SELECT
                    request_id,
                    raw_json AS response_json,
                    time AS response_timestamp
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'edit_host_response'
                {filter_expr['response']}
            ),
            resolution AS (
                SELECT
                    request_id,
                    MAX_BY(raw_json, time) AS resolution_json,
                    MAX(time) AS resolution_timestamp
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'edit_resolution'
                {filter_expr['resolution']}
                GROUP BY request_id
            ),
            metadata AS (
                SELECT
                    request_id,
                    JSON_VALUE(raw_json, "$.user_id") AS user_id,
                    JSON_VALUE(raw_json, "$.user_agent") AS user_agent,
                FROM `{dataset_name}.request_event`
                WHERE event_type = 'request_metadata'
                AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'AugmentHealthCheck')
                AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'augment_review_bot')
                AND NOT STARTS_WITH(JSON_VALUE(raw_json, "$.user_agent"), 'Augment-EvalHarness')
                {filter_expr['metadata']}
            )
        SELECT
            request.request_id,
            metadata.user_id,
            metadata.user_agent,
            request.request_json,
            request.request_timestamp,
            response.response_json,
            response.response_timestamp,
            resolution.resolution_json,
            resolution.resolution_timestamp
        FROM request
        JOIN response USING (request_id)
        JOIN metadata USING (request_id)
        LEFT JOIN resolution USING (request_id)
        WHERE TRUE
        {filter_expr["all"]}
        {order_by_expr}
        {limit_expr}
        """  # nosec
