"""Tests for sharding."""

import pytest

import uuid

from base.datasets.sharding import UuidSharder


@pytest.mark.parametrize(
    "num_shards, uuid_str, shard_idx",
    [
        (2, "00000000-0000-0000-0000-000000000000", 0),
        (2, "7fffffff-ffff-ffff-ffff-ffffffffffff", 0),
        (2, "*************-0000-0000-000000000000", 1),
        (2, "ffffffff-ffff-ffff-ffff-ffffffffffff", 1),
        (256, "00000000-0000-0000-0000-000000000000", 0),
        (256, "7fffffff-ffff-ffff-ffff-ffffffffffff", 127),
        (17, "00000000-0000-0000-0000-000000000000", 0),
        (17, "0f0f0f0f-0f0f-0f0f-0f0f-0f0f0f0f0f0f", 0),
        (17, "0f0f0f0f-0f0f-0f0f-0f0f-0f0f0f0f0f10", 1),
    ],
)
def test_uuid_sharder_shard(num_shards: int, uuid_str: str, shard_idx: int):
    """Test that the sharder shards UUIDs correctly."""
    sharder = UuidSharder(num_shards=num_shards)
    assert sharder.shard(uuid_str) == shard_idx


def test_uuid_sharder_total_range():
    """Test the total range."""
    assert UuidSharder.total_range() == (
        "00000000-0000-0000-0000-000000000000",
        "ffffffff-ffff-ffff-ffff-ffffffffffff0",
    )


@pytest.mark.parametrize(
    "num_shards, shards",
    [
        (
            1,
            [
                "00000000-0000-0000-0000-000000000000",
                "ffffffff-ffff-ffff-ffff-ffffffffffff0",
            ],
        ),
        (
            2,
            [
                "00000000-0000-0000-0000-000000000000",
                "*************-0000-0000-000000000000",
                "ffffffff-ffff-ffff-ffff-ffffffffffff0",
            ],
        ),
        (
            17,
            [
                "00000000-0000-0000-0000-000000000000",
                "0f0f0f0f-0f0f-0f0f-0f0f-0f0f0f0f0f10",
                "1e1e1e1e-1e1e-1e1e-1e1e-1e1e1e1e1e1f",
                "2d2d2d2d-2d2d-2d2d-2d2d-2d2d2d2d2d2e",
                "3c3c3c3c-3c3c-3c3c-3c3c-3c3c3c3c3c3d",
                "4b4b4b4b-4b4b-4b4b-4b4b-4b4b4b4b4b4c",
                "5a5a5a5a-5a5a-5a5a-5a5a-5a5a5a5a5a5b",
                "69696969-6969-6969-6969-69696969696a",
                "78787878-7878-7878-7878-787878787879",
                "87878787-8787-8787-8787-************",
                "*************-9696-9696-************",
                "a5a5a5a5-a5a5-a5a5-a5a5-a5a5a5a5a5a6",
                "b4b4b4b4-b4b4-b4b4-b4b4-b4b4b4b4b4b5",
                "c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c4",
                "d2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d3",
                "e1e1e1e1-e1e1-e1e1-e1e1-e1e1e1e1e1e2",
                "f0f0f0f0-f0f0-f0f0-f0f0-f0f0f0f0f0f1",
                "ffffffff-ffff-ffff-ffff-ffffffffffff0",
            ],
        ),
    ],
)
def test_uuid_sharder_shard_ranges(num_shards, shards):
    """Test shard_ranges and shard_range."""
    sharder = UuidSharder(num_shards=num_shards)
    expected_ranges = list(zip(shards[:-1], shards[1:]))
    ranges = list(sharder.shard_ranges())
    assert ranges == expected_ranges
    assert [sharder.shard_range(i) for i in range(num_shards)] == expected_ranges


@pytest.mark.parametrize("num_shards", [1, 2, 17, 256, 1234])
def test_uuid_sharder_auto(num_shards):
    """Test shard_ranges, shard_range, and shard match."""
    sharder = UuidSharder(num_shards=num_shards)
    ranges = list(sharder.shard_ranges())

    # shard_range(i) should always match the ith shard_ranges
    assert [sharder.shard_range(i) for i in range(num_shards)] == ranges

    # The front and end of each shard_range should be in that shard.
    max_uuid = uuid.UUID(int=2**128 - 1)
    for i, (start, end) in enumerate(ranges):
        assert sharder.shard(start) == i

        if end > str(max_uuid):
            last = str(max_uuid)
        else:
            last = str(uuid.UUID(int=uuid.UUID(end).int - 1))
        assert sharder.shard(last) == i
