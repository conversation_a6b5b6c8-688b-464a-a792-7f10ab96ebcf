"""Tests that we can import the request_insight_pb2 proto."""

from services.request_insight import request_insight_pb2
from services.api_proxy import public_api_pb2


def test_import():
    """Test that we can import protos."""
    a = request_insight_pb2.RequestEvent(
        completion_host_request=request_insight_pb2.CompletionHostRequest(
            prefix="test-prefix",
        ),
    )
    assert a.completion_host_request.prefix == "test-prefix"
    b = public_api_pb2.Model(name="model")
    assert b.name == "model"
