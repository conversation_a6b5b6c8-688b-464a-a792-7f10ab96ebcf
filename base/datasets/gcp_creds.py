import logging
from pathlib import Path
from typing import Optional

import google.auth
import google.auth.credentials
import google.oauth2.service_account

logger = logging.getLogger(__name__)


def get_gcp_creds(
    service_account_path: Optional[Path] = None,
    service_account_info: Optional[dict[str, str]] = None,
    project_id: Optional[str] = None,
) -> tuple[google.auth.credentials.Credentials, Optional[str]]:
    """Get GCP credentials using one of the supported methods.

    Args:
        service_account_path: The path to a service account file to use to get
            credentials.
        project_id: The project ID to use.
        service_account_info: The service account info to use, in the form of key value
            pairs; this is the content of the service account JSON file.

    Note: if both service_account_path and service_account_info are provided,
        service_account_path is used.   Service account info is for situations
        where saving the service account file to disk is unsafe.

    Returns:
        A tuple of the credentials and the project ID.
    """
    if service_account_path:
        logger.info("Loading service credentials from %s.", service_account_path)
        creds = google.oauth2.service_account.Credentials.from_service_account_file(
            service_account_path,
        )
        return creds, project_id or creds.project_id
    elif service_account_info:
        logger.info("Loading service credentials from provided info.")
        creds = google.oauth2.service_account.Credentials.from_service_account_info(
            service_account_info,
            scopes=["https://www.googleapis.com/auth/cloud-platform"],
        )
        return creds, project_id or creds.project_id
    else:
        creds, def_project_id = google.auth.default()
        return creds, project_id or def_project_id  # type: ignore
