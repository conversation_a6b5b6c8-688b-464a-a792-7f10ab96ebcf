load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "pytest_test")

# This should list all the proto deps needed by research.
# Note that this might include protos that are already included by
# other installs, like base.blob_names.
py_library(
    name = "protos_py",
    srcs = [],
    deps = [
        # TODO(jeff): public_api.proto is needed by augment_client, but not datasets.
        "//services/api_proxy:public_api_py_proto",
        "//services/request_insight:request_insight_py_proto",
    ],
)

pytest_test(
    name = "protos_test",
    size = "small",
    srcs = ["protos_test.py"],
    deps = [
        ":protos_py",
    ],
)

# NOTE(jeff): This is an incantation that collects all the files in protos_py
# or its dependencies. Somehow bazel accepts a filegroup in deps of a
# sh_binary, but not a py_library.
filegroup(
    name = "protos_sh",
    srcs = [
        ":protos_py",
    ],
)

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    visibility = ["//base:__subpackages__"],
    deps = [
        ":protos_sh",
    ],
)

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    deps = [
        ":completion",
        ":gcs_blob_cache",
        ":next_edit",
        ":recency_info",
        "//base/blob_names/python:blob_names",
        "//base/caching:cache",
        "//base/diff_utils:edit_events_py_proto",
        "//base/ranges",
        "//services/request_insight:request_insight_py_proto",
        requirement("pytest"),
        requirement("google-cloud-bigquery"),
    ],
)

py_library(
    name = "itertools",
    srcs = ["itertools.py"],
    visibility = ["//services:__subpackages__"],
    deps = [],
)

py_library(
    name = "gcp_creds",
    srcs = ["gcp_creds.py"],
    visibility = ["//experimental:__subpackages__"],
    deps = [
        requirement("google-auth"),
    ],
)

pytest_test(
    name = "gcp_creds_test",
    srcs = ["gcp_creds_test.py"],
    deps = [
        ":gcp_creds",
        # TODO(jeff): One of google-cloud-bigquery or google-cloud-storage is a hidden
        # dependency required for google.auth.default() to run without throwing
        # DefaultCredentialsError.
        requirement("google-cloud-bigquery"),
        requirement("google-cloud-storage"),
    ],
)

py_library(
    name = "gcs_client_lib",
    srcs = ["gcs_client.py"],
    deps = [
        ":gcp_creds",
        ":itertools",
        ":tenants",
        "//services/request_insight:request_insight_py_proto",
        requirement("google-cloud-storage"),
        requirement("tenacity"),
    ],
)

py_binary(
    name = "gcs_client",
    srcs = ["gcs_client.py"],
    main = "gcs_client.py",
    deps = [":gcs_client_lib"],
)

pytest_test(
    name = "gcs_client_test",
    size = "small",
    srcs = ["gcs_client_test.py"],
    deps = [
        ":gcs_client_lib",
    ],
)

py_library(
    name = "recency_info",
    srcs = [
        "recency_info.py",
    ],
    visibility = ["//base:__subpackages__"],
    deps = [
        "//base/ranges",
        requirement("dataclasses-json"),
    ],
)

py_library(
    name = "recency_info_conversion",
    srcs = [
        "recency_info_conversion.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":recency_info",
        "//services/request_insight:request_insight_py_proto",
        requirement("dataclasses-json"),
    ],
)

py_library(
    name = "completion",
    srcs = [
        "completion.py",
    ],
    deps = [
        ":recency_info",
        "//base/ranges",
        requirement("dataclasses-json"),
    ],
)

py_library(
    name = "completion_conversion",
    srcs = [
        "completion_conversion.py",
    ],
    deps = [
        ":completion",
        ":recency_info_conversion",
        "//base/diff_utils:edit_events_py_proto",
        "//base/ranges",
        "//services/request_insight:request_insight_py_proto",
    ],
)

py_library(
    name = "completion_dataset",
    srcs = [
        "completion_dataset.py",
    ],
    deps = [
        ":completion",
        ":completion_conversion",
        ":gcp_creds",
        ":gcs_blob_cache",
        ":itertools",
        ":pipeline",
        ":tenants",
        "//services/request_insight:request_insight_py_proto",
        requirement("dataclasses-json"),
        requirement("google-cloud-bigquery"),
    ],
)

pytest_test(
    name = "completion_dataset_test",
    size = "small",
    srcs = ["completion_dataset_test.py"],
    deps = [
        ":completion_dataset",
        ":conftest",
    ],
)

py_library(
    name = "completion_dataset_gcs",
    srcs = [
        "completion_dataset_gcs.py",
    ],
    deps = [
        ":completion",
        ":completion_conversion",
        ":gcp_creds",
        ":gcs_blob_cache",
        ":gcs_client_lib",
        ":itertools",
        ":pipeline",
        ":tenants",
        "//services/request_insight:request_insight_py_proto",
        requirement("dataclasses-json"),
        requirement("google-cloud-bigquery"),
    ],
)

pytest_test(
    name = "completion_dataset_gcs_test",
    size = "small",
    srcs = ["completion_dataset_gcs_test.py"],
    deps = [
        ":completion_dataset_gcs",
        ":conftest",
    ],
)

py_library(
    name = "next_edit",
    srcs = ["next_edit.py"],
    visibility = ["//base/datasets:__subpackages__"],
    deps = [
        ":gcs_blob_cache",
        ":recency_info",
        "//base/blob_names/python:blob_names",
        "//base/diff_utils",
        "//base/diff_utils:proto_wrapper",
        "//base/logging:secret_logging",
        "//base/ranges",
        requirement("dataclasses-json"),
    ],
)

py_library(
    name = "next_edit_conversion",
    srcs = ["next_edit_conversion.py"],
    deps = [
        ":next_edit",
        "//base/diff_utils:proto_wrapper",
        "//base/ranges",
        "//base/static_analysis:proto_convertor",
        "//services/request_insight:request_insight_py_proto",
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "next_edit_test",
    size = "small",
    srcs = ["next_edit_test.py"],
    deps = [
        ":conftest",
        ":next_edit",
        ":next_edit_conversion",
        "//base/diff_utils",
        "//base/ranges",
        "//services/request_insight:request_insight_py_proto",
    ],
)

py_library(
    name = "next_edit_dataset_gcs",
    srcs = [
        "next_edit_dataset_gcs.py",
    ],
    deps = [
        ":gcp_creds",
        ":gcs_blob_cache",
        ":gcs_client_lib",
        ":itertools",
        ":next_edit",
        ":next_edit_conversion",
        ":pipeline",
        ":tenants",
        "//services/request_insight:request_insight_py_proto",
        requirement("dataclasses-json"),
        requirement("google-cloud-bigquery"),
    ],
)

pytest_test(
    name = "next_edit_dataset_gcs_test",
    size = "small",
    srcs = ["next_edit_dataset_gcs_test.py"],
    deps = [
        ":conftest",
        ":next_edit_dataset_gcs",
        "//base/diff_utils",
        "//base/ranges",
        "//services/request_insight:request_insight_py_proto",
    ],
)

py_library(
    name = "edit",
    srcs = [
        "edit.py",
        "edit_dataset.py",
    ],
    deps = [
        ":gcp_creds",
        ":gcs_blob_cache",
        ":itertools",
        ":pipeline",
        ":tenants",
        "//base/caching:cache",
        "//services/request_insight:request_insight_py_proto",
        requirement("dataclasses-json"),
        requirement("google-cloud-bigquery"),
    ],
)

pytest_test(
    name = "edit_test",
    size = "small",
    srcs = ["edit_test.py"],
    deps = [
        ":edit",
    ],
)

py_library(
    name = "dict_cache",
    srcs = ["dict_cache.py"],
    deps = [
        "//base/caching:cache",
    ],
)

pytest_test(
    name = "dict_cache_test",
    size = "small",
    srcs = ["dict_cache_test.py"],
    deps = [
        ":dict_cache",
    ],
)

py_library(
    name = "gcs_blob_cache",
    srcs = ["gcs_blob_cache.py"],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/caching:cache",
        "//base/caching:lru_cache",
        requirement("google-cloud-storage"),
    ],
)

pytest_test(
    name = "gcs_blob_cache_test",
    srcs = ["gcs_blob_cache_test.py"],
    deps = [
        ":gcs_blob_cache",
    ],
)

py_library(
    name = "tenants",
    srcs = ["tenants.py"],
    visibility = ["//experimental:__subpackages__"],
    deps = [],
)

py_library(
    name = "replay_utils",
    srcs = ["replay_utils.py"],
    visibility = ["//experimental:__subpackages__"],
    deps = [
        requirement("dataclasses-json"),
        ":completion_dataset",
        "//base/augment_client:client",
    ],
)

py_library(
    name = "pipeline",
    srcs = ["pipeline.py"],
    deps = [],
)

pytest_test(
    name = "pipeline_test",
    srcs = ["pipeline_test.py"],
    deps = [
        ":pipeline",
    ],
)

py_library(
    name = "sharding",
    srcs = ["sharding.py"],
    deps = [],
)

pytest_test(
    name = "sharding_test",
    size = "small",
    srcs = ["sharding_test.py"],
    deps = [
        ":sharding",
    ],
)

py_library(
    name = "user_event",
    srcs = [
        "user_event.py",
        "user_event_lib.py",
    ],
    visibility = ["//base/datasets:__subpackages__"],
    deps = [
        ":gcp_creds",
        ":gcs_client_lib",
        ":tenants",
        "//base/ranges",
        "//services/request_insight:request_insight_py_proto",
        requirement("google-cloud-bigquery"),
    ],
)

pytest_test(
    name = "user_event_test",
    size = "small",
    srcs = ["user_event_test.py"],
    deps = [
        ":user_event",
        "//base/ranges",
        "//base/test_utils:testing_utils",
    ],
)

py_library(
    name = "hindsight_lib",
    srcs = [
        "hindsight_lib.py",
    ],
    visibility = ["//base/datasets:__subpackages__"],
    deps = [
        ":user_event",
        "//base/caching:cache",
        "//base/ranges",
    ],
)

pytest_test(
    name = "hindsight_lib_test",
    srcs = ["hindsight_lib_test.py"],
    deps = [
        ":hindsight_lib",
        ":user_event",
        "//base/ranges",
    ],
)

py_library(
    name = "hindsight_completion",
    srcs = [
        "hindsight_completion.py",
        "hindsight_completion_dataset.py",
    ],
    deps = [
        ":completion",
        ":completion_dataset_gcs",
        ":gcp_creds",
        ":gcs_blob_cache",
        ":hindsight_lib",
        ":tenants",
        ":user_event",
        "//base/caching:cache",
        requirement("dataclasses-json"),
        requirement("google-cloud-storage"),
    ],
)

pytest_test(
    name = "hindsight_completion_test",
    srcs = ["hindsight_completion_test.py"],
    deps = [
        ":completion_dataset",
        ":dict_cache",
        ":gcs_blob_cache",
        ":hindsight_completion",
        ":user_event",
        "//base/ranges",
    ],
)

py_library(
    name = "hindsight_next_edit_lib",
    srcs = [
        "hindsight_next_edit_lib.py",
    ],
    visibility = ["//base/datasets:__subpackages__"],
    deps = [
        ":user_event",
        "//base/static_analysis:common",
    ],
)

py_library(
    name = "hindsight_next_edit",
    srcs = [
        "hindsight_next_edit.py",
        "hindsight_next_edit_dataset.py",
    ],
    visibility = ["//base/datasets:__subpackages__"],
    deps = [
        ":gcp_creds",
        ":gcs_blob_cache",
        ":next_edit_dataset_gcs",
        ":tenants",
        ":user_event",
        "//base/blob_names/python:blob_names",
        "//base/caching:cache",
        "//base/diff_utils",
        "//base/diff_utils:proto_wrapper",
        requirement("dataclasses-json"),
        requirement("google-cloud-storage"),
        requirement("tqdm"),
    ],
)

pytest_test(
    name = "hindsight_next_edit_test",
    srcs = ["hindsight_next_edit_test.py"],
    deps = [
        ":dict_cache",
        ":gcs_blob_cache",
        ":hindsight_next_edit",
        ":hindsight_next_edit_lib",
        ":user_event",
        "//base/ranges",
    ],
)

py_library(
    name = "hindsight_next_edit_intermediate_dataset",
    srcs = ["hindsight_next_edit_intermediate_dataset.py"],
    deps = [
        ":gcp_creds",
        ":gcs_blob_cache",
        ":gcs_client",
        ":hindsight_next_edit",
        ":itertools",
        ":next_edit",
        ":next_edit_dataset_gcs",
        ":tenants",
        ":user_event",
        "//base/blob_names/python:blob_names",
        "//base/diff_utils",
        "//base/logging:secret_logging",
        "//base/ranges",
        requirement("dataclasses-json"),
        requirement("google-cloud-storage"),
        requirement("tqdm"),
        requirement("pytz"),
    ],
)
