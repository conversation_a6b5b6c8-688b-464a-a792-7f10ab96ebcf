load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "range_types",
    srcs = ["range_types.py"],
    visibility = ["//visibility:public"],
    deps = [
        requirement("dataclasses_json"),
        requirement("typing_extensions"),
    ],
)

pytest_test(
    name = "range_types_test",
    size = "small",
    srcs = ["range_types_test.py"],
    deps = [":range_types"],
)

py_library(
    name = "byte_map",
    srcs = ["byte_map.py"],
    deps = [
        ":range_types",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "byte_map_test",
    size = "small",
    srcs = ["byte_map_test.py"],
    data = [
        ":testdata/unicode_file.txt",
    ],
    deps = [":byte_map"],
)

py_library(
    name = "line_map",
    srcs = ["line_map.py"],
    deps = [
        ":range_types",
    ],
)

pytest_test(
    name = "line_map_test",
    size = "small",
    srcs = ["line_map_test.py"],
    data = [
        ":testdata/unicode_file.txt",
    ],
    deps = [":line_map"],
)

py_library(
    name = "string_utils",
    srcs = ["string_utils.py"],
    visibility = [
        "//models:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":range_types",
    ],
)

pytest_test(
    name = "string_utils_test",
    size = "small",
    srcs = ["string_utils_test.py"],
    deps = [":string_utils"],
)

py_library(
    name = "ranges",
    srcs = [
        "__init__.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":byte_map",
        ":line_map",
        ":range_types",
        ":string_utils",
    ],
)
