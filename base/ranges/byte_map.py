"""Maps between different range-based data structures."""

import bisect
import sys

import numpy as np

from base.ranges.range_types import ByteRang<PERSON>, CharRange

_CODEC = "utf-32-le" if sys.byteorder == "little" else "utf-32-be"


def count_utf8_bytes(text: str) -> np.ndarray:
    """Count the number of bytes in each character of the UTF-8 encoded text.

    See https://en.wikipedia.org/wiki/UTF-8#Encoding for the cutoffs below.
    """
    # Arun's bytearray trick below avoids constructing an expensive python list.
    char_ords = np.frombuffer(bytearray(text, _CODEC), dtype=np.uint32)
    byte_sizes = 1 + (char_ords >= 0x80) + (char_ords >= 0x800) + (char_ords >= 0xFFFF)
    return byte_sizes


class ByteMap:
    """Map between byte-based and unicode-based cursor locations."""

    def __init__(self, text: str):
        # Count the number of bytes in each character.
        byte_sizes = count_utf8_bytes(text)
        # Get byte positions by summing up the byte sizes.
        byte_idxs = np.cumsum(byte_sizes)

        self._char_to_byte = byte_idxs
        self._total_chars = len(text)
        self._total_bytes = byte_idxs[-1] if text else 0

    def __repr__(self):
        return f"ByteMap(char_to_byte={self._char_to_byte})"

    def size_chars(self) -> int:
        """The total number of characters in the file."""
        return self._total_chars

    def size_bytes(self) -> int:
        """The total number of bytes in the file."""
        return self._total_bytes

    def byte_to_char(self, byte_idx: int) -> int:
        """Find the character index corresponding to a given byte position.

        Any invalid byte index is mapped to the immediately preceding character.
        """
        return bisect.bisect_right(self._char_to_byte, byte_idx)

    def byte_to_char_checked(self, byte_idx: int) -> int:
        """Find the character index corresponding to a given byte position.

        Any invalid byte index will trigger an IndexError.
        """
        char_idx = self.byte_to_char(byte_idx)
        byte_idx_mapped = self.char_to_byte(char_idx)
        if byte_idx_mapped != byte_idx:
            if not (0 <= byte_idx <= self._total_bytes):
                raise IndexError(
                    f"Byte index out of range: {byte_idx=}, {self._total_bytes=}"
                )
            raise IndexError(f"Invalid byte index: {byte_idx=}, {byte_idx_mapped=}.")
        return char_idx

    def char_to_byte(self, char_idx: int) -> int:
        """Find the byte index corresponding to a given character position.

        For multi-byte characters, the character maps to its first byte.
        When indexing the last character position, we return the last byte position.
        """
        if char_idx >= self._total_chars:
            return self._total_bytes
        if 0 < char_idx:
            return self._char_to_byte[char_idx - 1]
        if char_idx == 0:
            return 0
        raise IndexError(f"{char_idx=} out of range {self._total_chars=}.")

    def brange_to_crange(self, brange: ByteRange) -> CharRange:
        """Find the char range corresponding to a given byte range.

        Any invalid byte index is mapped to the immediately preceding character.
        """
        start = self.byte_to_char(brange.start)
        stop = self.byte_to_char(brange.stop)
        return CharRange(start, stop)

    def brange_to_crange_checked(self, brange: ByteRange) -> CharRange:
        """Find the char range corresponding to a given byte range.

        Any invalid byte index will trigger an IndexError.
        """
        start = self.byte_to_char_checked(brange.start)
        stop = self.byte_to_char_checked(brange.stop)
        return CharRange(start, stop)

    def crange_to_brange(self, crange: CharRange) -> ByteRange:
        """Find the byte range corresponding to a given character range."""
        start = self.char_to_byte(crange.start)
        stop = self.char_to_byte(crange.stop)
        return ByteRange(start, stop)
