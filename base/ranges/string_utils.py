from typing import Literal, TypeVar, Sequence, cast

from typing_extensions import assert_never

from base.ranges.range_types import ByteRange, IntRange

StrOrBytes = TypeVar("StrOrBytes", str, bytes)


def replace_str(
    byte_seq: StrOrBytes,
    replacements: Sequence[tuple[IntRange, StrOrBytes]],
    allow_overlap: bool = False,
) -> StrOrBytes:
    """Replace parts of a given str or bytes.

    Args:
        byte_seq: the input string or bytes to be modified.
        replacements: a list of replacements, each a tuple of (range, replacement).
            where the range is the original range in the input.
        allow_overlap: if True, allow replacements to overlap. If False, raise
            ValueError if replacements overlap.
    """
    replacements = list(sorted(replacements, key=lambda x: x[0].start))
    if not allow_overlap and ByteRange.any_overlaps(r for r, _ in replacements):
        raise ValueError(
            f"replacements have overlapping ranges: {[r for r, _ in replacements]}"
        )

    outputs = list[StrOrBytes]()
    i = 0
    for r, replacement in replacements:
        outputs.append(byte_seq[i : r.start])
        outputs.append(replacement)
        i = r.stop
    outputs.append(byte_seq[i:])
    if isinstance(byte_seq, bytes):
        return b"".join(cast(Sequence[bytes], outputs))
    else:
        return "".join(cast(Sequence[str], outputs))


def shorten_str(
    string: str,
    max_len: int = 60,
    omit_mode: Literal["middle", "right", "left"] = "middle",
) -> str:
    """Shorten a string to some maximum length. Omit some parts if too long.

    Args:
        string: the input string.
        max_len: the number of characters above which the string will get shortened.
        omit_mode: Which part of the string to omit when too long.
    """
    return shorten_str_detailed(string, max_len, omit_mode)[0]


def shorten_str_detailed(
    string: str,
    max_len: int = 60,
    omit_mode: Literal["middle", "right", "left"] = "middle",
) -> tuple[str, bool]:
    """Shorten a string to some maximum length. Omit some parts if too long.

    Args:
        string: the input string.
        max_len: the number of characters above which the string will get shortened.
        omit_mode: Which part of the string to omit when too long.

    Returns:
        A tuple containing the string (shortened if necessary) and a boolean indicating
        whether the string was shortened.
    """
    assert max_len >= 0
    if len(string) <= max_len:
        return string, False
    if omit_mode == "middle":
        return string[: max_len // 2] + "[...]" + string[-max_len // 2 :], True
    elif omit_mode == "right":
        return string[:max_len] + "[...]", True
    elif omit_mode == "left":
        return "[...]" + string[-max_len:], True
    else:
        assert_never(omit_mode)
