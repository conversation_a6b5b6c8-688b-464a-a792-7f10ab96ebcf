# ranges

Implements datatypes that represent ranges and a mapping between range
types. This method also exposes a few specific instantiations for bytes, characters and
lines that are especially pertinent to our codebase.

A range is represents the contiguous sequence `[start, stop)` and admits common
operations like ordering, overlaps and intersection.

A range map simply maps ranges from one type (e.g. characters) to another (e.g. lines).

## Example usage

```
from base.ranges import ByteRange, CharRange, LineRange, LineMap, ByteMap

@dataclass
class Chunk:
    """Represents a chunk of text from a file."""
    text: str
    char_start: int
    char_end: int
    byte_start: int
    byte_end: int


def sample_line_based_chunks(rng, file_text: str, n_chunks: int = 10) -> list[Chunk]:
    """Samples chunks from a file that are aligned with line boundaries."""
    lmap = LineMap(file_text)
    bmap = ByteMap(file_text)

    chunks = []
    for _ in range(n_chunks):
        start = rng.randint(0, lmap.size_lines() - 1)
        end = rng.randint(start + 1, lmap.size_lines())
        lrange = LineRange(start, end)
        crange = lmap.lrange2crange(lrange)
        brange = bmap.crange2brange(crange)
        chunks.append(
            Chunk(
                file_text[crange.start:crange.end],
                crange.start,
                crange.end,
                brange.start,
                brange.end,
            )
        )

    return chunks
```
