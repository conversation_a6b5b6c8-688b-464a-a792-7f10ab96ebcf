"""Maps between different range-based data structures."""

import bisect
from typing import <PERSON><PERSON><PERSON><PERSON>, Sequence

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange


class LnCol(NamedTuple):
    """A line number and column number, zero-indexed."""

    ln: int
    col: int

    def __repr__(self):
        return f"LnCol({self.ln}, {self.col})"


# The list of characters that str.splitlines() will split on.
SINGLE_CHAR_LINE_BREAKS = frozenset(
    {
        "\n",
        "\r",
        "\x0b",
        "\x0c",
        "\x1c",
        "\x1d",
        "\x1e",
        "\x85",
        "\u2028",
        "\u2029",
    }
)


def get_line_break(line: str) -> str:
    """Get the line break character for a given line.

    Return an empty string if the line does not end with a line break.
    """
    if line[-2:] == "\r\n":
        return "\r\n"
    elif line and (last_char := line[-1]) in SINGLE_CHAR_LINE_BREAKS:
        return last_char
    else:
        return ""


class LineMap:
    """Maps between character positions and (line, column) numbers.

    If the input is given as a sequence of lines it is expected to match
    `splitlines(keepends=True)` output, i.e. line break character(s) included at the end.
    """

    _line_stops: Sequence[int]
    """stores the char positions after each line break."""
    _line_break_n_chars: Sequence[int]
    """stores the size of each line break. e.g., `\n` is 1, `\r\n` is 2."""

    def __init__(self, text: str | Sequence[str]):
        last_line_stop = 0
        line_stops = list[int]()
        line_break_n_chars = list[int]()
        if isinstance(text, str):
            lines = text.splitlines(keepends=True)
        else:
            lines = text
            if len(lines) >= 2 and get_line_break(lines[0]) == "":
                raise ValueError(f"Unexpected line ending: {lines[0]=}, {lines[1]=}")

        for line in lines:
            last_line_stop += len(line)
            line_stops.append(last_line_stop)
            line_break = get_line_break(line)
            line_break_n_chars.append(len(line_break))
        assert last_line_stop == (
            len(text) if isinstance(text, str) else sum(len(t) for t in text)
        )
        self._line_stops = line_stops
        self._line_break_n_chars = line_break_n_chars

    def size_lines(self) -> int:
        """Get number of lines covered by this map."""
        return len(self._line_stops)

    def size_chars(self) -> int:
        """Get number of characters covered by this map."""
        return self._line_stops[-1] if self._line_stops else 0

    def get_line_number(self, char_pos: int, *, align_left: bool = False) -> int:
        r"""Map a char position into the corresponding line (starting from 0).

        When `char_pos` is at a line boundary and `align_left` is True, the
        previous line is returned, otherwise the next line is returned.
        In other words,
          given the text:         a b \n c d
          and indices:            0 1  2 3 4
          line no. (align right): 0 0  0 1 1
          line no. (align left):  0 0  0 0 1
        This behavior is mostly only necessary when computing the exclusive boundary
        of a range.
        """
        if char_pos < 0 or char_pos > self.size_chars():
            raise IndexError(f"{char_pos=} beyond range of file {self.size_chars()=}.")
        elif char_pos == self.size_chars():
            return self.size_lines() - 1 if align_left else self.size_lines()

        if align_left:
            return bisect.bisect_left(self._line_stops, char_pos)
        else:
            return bisect.bisect_right(self._line_stops, char_pos)

    def get_line_column(self, char_pos: int) -> LnCol:
        """Map a char position into (line_number, column_number) (0-indexed)."""
        line = self.get_line_number(char_pos)
        # Special case -- for the character at the boundary of the file, return the
        # line at the boundary of the file.
        if line == self.size_lines():
            return LnCol(line, 0)

        line_start = 0 if line == 0 else self._line_stops[line - 1]
        column = char_pos - line_start
        return LnCol(line, column)

    def get_char_index(self, line: int, column: int = 0) -> int:
        """Map (line, column) to char position (assuming zero-based indexing)."""
        if line < 0 or line > self.size_lines():
            raise IndexError(f"{line=} beyond range of file.")
        elif line == self.size_lines() and column > 0:
            raise IndexError(f"{column=} beyond range of file.")
        elif line == self.size_lines():
            return self.size_chars()

        line_start = 0 if line == 0 else self._line_stops[line - 1]
        line_end = self._line_stops[line]
        if column < 0 or column > (line_end - line_start):
            raise IndexError(f"{column=} beyond range of line.")
        return line_start + column

    def char_from_ln_col(self, ln_col: LnCol) -> int:
        """Map (line, column) to char position (assuming zero-based indexing)."""
        ln, col = ln_col
        return self.get_char_index(ln, col)

    def char_before_line_break(self, line: int) -> int:
        """The location after the last character of a line (before the line break)."""
        return (
            self._line_stops[line] - self._line_break_n_chars[line]
            if line < len(self._line_stops)
            else self.size_chars()
        )

    def crange_to_lrange(self, crange: CharRange) -> LineRange:
        """Get the minimal range of lines that contains the character range.

        NOTE: A point character range results in a point line ranges for that point.
        """
        line_start = self.get_line_number(crange.start)
        if crange.is_point():
            return LineRange.point(line_start)
        line_end = self.get_line_number(crange.stop, align_left=True) + 1
        return LineRange(line_start, line_end)

    def lrange_to_crange(self, lrange: LineRange) -> CharRange:
        """Convert a line range into corresponding character range.

        NOTE: A point line range results in a point character range for the first
        character in the line.
        """
        char_start = self.get_char_index(lrange.start, 0)
        char_end = self.get_char_index(lrange.stop, 0)
        return CharRange(char_start, char_end)
