"""Unit tests for core types."""

import pathlib
from typing import Iterable

import pytest

from base.ranges.line_map import LineMap, get_line_break
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange


def all_ranges(max_stop: int) -> Iterable[tuple[int, int]]:
    """Returns all the valid ranges up to max_stop."""
    for start in range(max_stop):
        for stop in range(start, max_stop):
            yield start, stop


@pytest.fixture
def unicode_text() -> str:
    my_path = pathlib.Path(__file__).parent
    return (my_path / "testdata/unicode_file.txt").read_text()


@pytest.fixture
def unicode_bytes(unicode_text: str) -> bytes:
    my_path = pathlib.Path(__file__).parent
    return (my_path / "testdata/unicode_file.txt").read_bytes()


@pytest.mark.parametrize(
    "text",
    ["", "a", "a\nb", "a\nb\n", "a\nb\nc", "a\n\n", "a\r\nb", "a\x85"],
)
def test_size_chars(text: str):
    lmap = LineMap(text)
    assert lmap.size_chars() == len(text)


def test_size_chars_file(unicode_text: str):
    lmap = LineMap(unicode_text)
    assert lmap.size_chars() == len(unicode_text)


@pytest.mark.parametrize(
    "text",
    [
        "",
        "a",
        "a\nb",
        "a\nb\n",
        "a\nb\nc",
        "a\n\n",
        "a\r\nb",
        "a\x85",
    ],
)
def test_size_lines(text: str):
    lmap = LineMap(text)
    assert lmap.size_lines() == len(text.splitlines(keepends=True))


def test_size_lines_file(unicode_text: str):
    lmap = LineMap(unicode_text)
    assert lmap.size_lines() == len(unicode_text.splitlines(keepends=True))


@pytest.mark.parametrize(
    "text, expected",
    [
        ("a", [0]),
        ("a\nb", [0, 0, 1]),
        ("a\nb\n", [0, 0, 1, 1]),
        ("a\nb\nc", [0, 0, 1, 1, 2]),
        ("ab\ncd", [0, 0, 0, 1, 1]),
        ("a\n\n", [0, 0, 1]),
        ("a\r\n", [0, 0, 0]),  # IBM line endings.
        ("a\r\nb", [0, 0, 0, 1]),  # IBM line endings.
        ("a\x85b", [0, 0, 1]),  # NEL
    ],
)
def test_get_line_number(text: str, expected: list[int]):
    assert len(text) == len(expected)

    lmap = LineMap(text)
    for char_loc, expected_line in enumerate(expected):
        assert lmap.get_line_number(char_loc) == expected_line


@pytest.mark.parametrize(
    "text, expected",
    [
        ("a", [0]),
        ("ab\ncd", [0, 0, 0, 0, 1]),
        ("a\nb\nc", [0, 0, 0, 1, 1]),
        ("a\n\n", [0, 0, 0]),
        ("a\r\n", [0, 0, 0]),  # IBM line endings.
        ("a\r\nb", [0, 0, 0, 0]),  # IBM line endings.
        ("ab\r\ncd", [0, 0, 0, 0, 0, 1]),  # IBM line endings.
    ],
)
def test_get_line_number_align_left(text: str, expected: list[int]):
    assert len(text) == len(expected)

    lmap = LineMap(text)
    for char_loc, expected_line in enumerate(expected):
        assert lmap.get_line_number(char_loc, align_left=True) == expected_line


def test_get_line_number_file(unicode_text: str):
    lmap = LineMap(unicode_text)
    for char_loc in range(len(unicode_text)):
        expected = unicode_text.count("\n", 0, char_loc)
        assert lmap.get_line_number(char_loc) == expected


@pytest.mark.parametrize(
    "text, expected",
    [
        ("a", [(0, 0)]),
        ("a\nb", [(0, 0), (0, 1), (1, 0)]),
        ("a\nb\n", [(0, 0), (0, 1), (1, 0), (1, 1)]),
        ("a\nb\nc", [(0, 0), (0, 1), (1, 0), (1, 1), (2, 0)]),
        ("a\n\n", [(0, 0), (0, 1), (1, 0)]),
        ("a\r\n", [(0, 0), (0, 1), (0, 2)]),  # IBM line endings.
        ("a\r\nb", [(0, 0), (0, 1), (0, 2), (1, 0)]),  # IBM line endings.
    ],
)
def test_get_line_column(text: str, expected: list[tuple[int, int]]):
    assert len(text) == len(expected)
    lmap = LineMap(text)
    for char_loc, (expected_line, expected_col) in enumerate(expected):
        assert lmap.get_line_column(char_loc) == (expected_line, expected_col)


@pytest.mark.parametrize("splitlines", [True, False])
def test_get_line_column_file(unicode_text: str, splitlines: bool):
    input = unicode_text.splitlines(keepends=True) if splitlines else unicode_text
    lmap = LineMap(input)
    for char_loc in range(len(unicode_text)):
        expected_line = lmap.get_line_number(char_loc)
        last_newline = unicode_text.rfind("\n", 0, char_loc) + 1
        expected_col = char_loc - last_newline
        assert lmap.get_line_column(char_loc) == (expected_line, expected_col)


@pytest.mark.parametrize(
    "text, expected",
    [
        ("a", [(0, 0)]),
        ("a\nb", [(0, 0), (0, 1), (1, 0)]),
        ("a\nb\n", [(0, 0), (0, 1), (1, 0), (1, 1)]),
        ("a\nb\nc", [(0, 0), (0, 1), (1, 0), (1, 1), (2, 0)]),
        ("a\n\n", [(0, 0), (0, 1), (1, 0)]),
        ("a\r\n", [(0, 0), (0, 1), (0, 2)]),  # IBM line endings.
        ("a\r\nb", [(0, 0), (0, 1), (0, 2), (1, 0)]),  # IBM line endings.
    ],
)
def test_get_char_idx(text: str, expected: list[tuple[int, int]]):
    assert len(text) == len(expected)

    lmap = LineMap(text)
    for expected_char_loc, (line, line_col) in enumerate(expected):
        assert lmap.get_char_index(line, line_col) == expected_char_loc


@pytest.mark.parametrize("splitlines", [True, False])
def test_get_char_idx_file(unicode_text: str, splitlines: bool):
    input = unicode_text.splitlines(keepends=True) if splitlines else unicode_text
    lmap = LineMap(input)
    for char_loc in range(len(unicode_text)):
        line = lmap.get_line_number(char_loc)
        line_start = unicode_text.rfind("\n", 0, char_loc) + 1
        line_col = char_loc - line_start
        assert lmap.get_char_index(line, line_col) == char_loc


@pytest.mark.parametrize(
    "text, crange, expected",
    [
        # Boundaries with a single character.
        ("a", (0, 0), (0, 0)),
        ("a", (0, 1), (0, 1)),
        ("a", (1, 1), (1, 1)),
        # Boundaries with two lines.
        ("a\nbc", (0, 1), (0, 1)),
        ("a\nbc", (0, 2), (0, 1)),
        ("a\nbc", (0, 3), (0, 2)),
        ("a\nbc", (2, 2), (1, 1)),
        ("a\nbc", (3, 3), (1, 1)),
        ("a\nb", (3, 3), (2, 2)),
        ("a\r\nb\r\n", (0, 3), (0, 1)),  # IBM line endings.
        # A range starting at the middle of the new line to the middle of the next.
        ("a\r\nb\r\n", (1, 4), (0, 2)),  # IBM line endings.
    ],
)
def test_crange_to_lrange(
    text: str, crange: tuple[int, int], expected: tuple[int, int]
):
    lmap = LineMap(text)
    assert lmap.crange_to_lrange(CharRange(*crange)) == LineRange(*expected)


@pytest.mark.parametrize("splitlines", [True, False])
def test_crange_to_lrange_file(unicode_text: str, splitlines: bool):
    input = unicode_text.splitlines(keepends=True) if splitlines else unicode_text
    lmap = LineMap(input)
    lines = unicode_text.splitlines(keepends=True)
    for char_start, char_end in all_ranges(len(unicode_text)):
        lrange = lmap.crange_to_lrange(CharRange(char_start, char_end))
        # We are testing the property that lrange is the minimal range of lines that
        # contains the given characters.
        assert unicode_text[char_start:char_end] in "".join(
            lines[lrange.start : lrange.stop]
        )
        if lrange.stop > lrange.start:
            assert unicode_text[char_start:char_end] not in "".join(
                lines[lrange.start : lrange.stop - 1]
            )


@pytest.mark.parametrize(
    "text, lrange, expected",
    [
        # Boundaries with a single character.
        ("a", (0, 0), (0, 0)),
        ("a", (0, 1), (0, 1)),
        ("a", (1, 1), (1, 1)),
        # Boundaries with two lines.
        ("a\nbc", (0, 1), (0, 2)),
        ("a\nbc", (0, 1), (0, 2)),
        ("a\nbc", (0, 2), (0, 4)),
        ("a\nbc", (1, 1), (2, 2)),
        ("a\nbc", (2, 2), (4, 4)),
        ("a\r\nb\r\n", (0, 1), (0, 3)),  # IBM line endings.
        # A range starting at the middle of the new line to the middle of the next.
        ("a\r\nb\r\n", (0, 2), (0, 6)),  # IBM line endings.
    ],
)
def test_lrange_to_crange(
    text: str, lrange: tuple[int, int], expected: tuple[int, int]
):
    lmap = LineMap(text)
    assert lmap.lrange_to_crange(LineRange(*lrange)) == CharRange(*expected)


@pytest.mark.parametrize(
    "text, line, expected",
    [
        # Boundaries with a single character.
        ("abc", 0, 3),
        ("abc\nde", 0, 3),
        ("abc\rde", 0, 3),
        ("abc\r\nde", 0, 3),
        ("abc\r\ndef\ngh", 1, 8),
    ],
)
def test_char_before_line_break(text: str, line: int, expected: int):
    lmap = LineMap(text)
    actual = lmap.char_before_line_break(line)
    assert actual == expected


@pytest.mark.parametrize("splitlines", [True, False])
def test_lrange_to_crange_file(unicode_text: str, splitlines: bool):
    input = unicode_text.splitlines(keepends=True) if splitlines else unicode_text
    lmap = LineMap(input)
    lines = unicode_text.splitlines(keepends=True)
    for line_start, line_end in all_ranges(len(lines)):
        char_start = sum(len(line) for line in lines[:line_start])
        char_end = sum(len(line) for line in lines[:line_end])
        assert lmap.lrange_to_crange(LineRange(line_start, line_end)) == (
            CharRange(char_start, char_end)
        )
        assert unicode_text[char_start:char_end] == "".join(lines[line_start:line_end])


def test_line_map_raises_on_invalid_line_index(unicode_text: str):
    lmap = LineMap(unicode_text)
    lines = unicode_text.splitlines(keepends=True)

    for line_idx, line in enumerate(lines):
        lmap.get_char_index(line_idx, 0)
        lmap.get_char_index(line_idx, len(line))
        with pytest.raises(IndexError):
            lmap.get_char_index(line_idx, -1)
        with pytest.raises(IndexError):
            lmap.get_char_index(line_idx, len(line) + 1)
    lmap.get_char_index(len(lines), 0)
    with pytest.raises(IndexError):
        lmap.get_char_index(len(lines), 1)
    with pytest.raises(IndexError):
        lmap.get_char_index(len(lines) + 1, 0)

    lmap.lrange_to_crange(LineRange(0, len(lines)))
    with pytest.raises(IndexError):
        lmap.lrange_to_crange(LineRange(-1, len(lines)))
    with pytest.raises(IndexError):
        lmap.lrange_to_crange(LineRange(0, len(lines) + 1))


def test_line_map_raises_on_invalid_char_index(unicode_text: str):
    lmap = LineMap(unicode_text)
    lmap.get_line_column(0)
    lmap.get_line_column(len(unicode_text))
    with pytest.raises(IndexError):
        lmap.get_line_column(-1)
    with pytest.raises(IndexError):
        lmap.get_line_column(len(unicode_text) + 1)

    lmap.crange_to_lrange(CharRange(0, len(unicode_text)))
    with pytest.raises(IndexError):
        lmap.crange_to_lrange(CharRange(-1, len(unicode_text)))
    with pytest.raises(IndexError):
        lmap.crange_to_lrange(CharRange(0, len(unicode_text) + 1))


def test_get_line_break():
    assert get_line_break("hello\r\n") == "\r\n"
    assert get_line_break("hello\n") == "\n"
    assert get_line_break("hello\r") == "\r"
    assert get_line_break("hello\x85") == "\x85"
    assert get_line_break("hello") == ""
    assert get_line_break("") == ""
