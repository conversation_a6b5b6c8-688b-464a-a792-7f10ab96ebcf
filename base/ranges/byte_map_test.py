"""Unit tests for ByteMap."""

import pathlib
from typing import Iterable

import pytest

from base.ranges.byte_map import ByteMap
from base.ranges.range_types import ByteRange, <PERSON>r<PERSON><PERSON><PERSON>


def all_ranges(max_stop: int) -> Iterable[tuple[int, int]]:
    """Returns all the valid ranges up to max_stop."""
    for start in range(max_stop):
        for stop in range(start, max_stop):
            yield start, stop


@pytest.fixture
def unicode_text() -> str:
    """Load some unicode text."""
    my_path = pathlib.Path(__file__).parent
    return (my_path / "testdata/unicode_file.txt").read_text()


@pytest.fixture
def unicode_bytes() -> bytes:
    """Load some unicode bytes."""
    my_path = pathlib.Path(__file__).parent
    return (my_path / "testdata/unicode_file.txt").read_bytes()


# Tests for ByteMap
@pytest.mark.parametrize(
    "text, char_idxs",
    [
        # The smiley face is 4 bytes long.
        ("a😃b", [0, 1, 1, 1, 1, 2]),
        ("", []),
        ("a", [0]),
        ("😃", [0, 0, 0, 0]),
    ],
)
def test_byte_to_char(text: str, char_idxs: list[int]):
    assert len(char_idxs) == len(text.encode())
    bmap = ByteMap(text)
    for i, char_idx in enumerate(char_idxs):
        assert bmap.byte_to_char(i) == char_idx, f"{i=}, {char_idx=}"


def test_byte_map_size_bytes(unicode_text: str, unicode_bytes: bytes):
    bmap = ByteMap(unicode_text)
    assert bmap.size_bytes() == len(unicode_bytes)


def test_byte_map_size_chars(unicode_text: str):
    bmap = ByteMap(unicode_text)
    assert bmap.size_chars() == len(unicode_text)


@pytest.mark.parametrize(
    "text, byte_idxs",
    [
        # The smiley face is 4 bytes long.
        ("a😃b", [0, 1, 5]),
        ("😃", [0]),
        ("a😃b", [0, 1, 5]),
    ],
)
def test_char_to_byte(text: str, byte_idxs: list[int]):
    assert len(byte_idxs) == len(text)
    bmap = ByteMap(text)
    for i, byte_idx in enumerate(byte_idxs):
        assert bmap.char_to_byte(i) == byte_idx


def test_byte_to_char_longtext(unicode_text: str, unicode_bytes: bytes):
    bmap = ByteMap(unicode_text)
    for byte_loc in range(len(unicode_bytes)):
        # All bytes in the same multi-byte character should map to the same
        # character, so we ignore any decoding errors when computing char positions.
        char_loc = bmap.byte_to_char(byte_loc)
        assert char_loc == len(unicode_bytes[:byte_loc].decode(errors="ignore"))
        assert unicode_bytes[byte_loc] in unicode_text[char_loc].encode()


def test_char_to_byte_longtext(unicode_text: str, unicode_bytes: bytes):
    bmap = ByteMap(unicode_text)
    for char_loc in range(len(unicode_text)):
        # To measure the byte position, we measure the length of the text right
        # before the current character.
        byte_loc = bmap.char_to_byte(char_loc)
        assert byte_loc == len(unicode_text[:char_loc].encode())
        assert unicode_bytes[byte_loc] in unicode_text[char_loc].encode()


def test_brange_to_crange(unicode_text: str, unicode_bytes: bytes):
    bmap = ByteMap(unicode_text)
    for c_start, c_stop in all_ranges(len(unicode_text)):
        b_start = len(unicode_text[:c_start].encode())
        b_stop = len(unicode_text[:c_stop].encode())
        brange = ByteRange(b_start, b_stop)
        crange_ = bmap.brange_to_crange(brange)
        # We ignore any decoding errors when testing for this match.
        assert crange_ == CharRange(c_start, c_stop)


def test_crange_to_brange(unicode_text: str, unicode_bytes: bytes):
    bmap = ByteMap(unicode_text)
    for char_start, char_stop in all_ranges(len(unicode_text)):
        crange = CharRange(char_start, char_stop)
        brange = bmap.crange_to_brange(crange)
        assert (
            unicode_bytes[brange.start : brange.stop].decode(errors="strict")
            == unicode_text[crange.start : crange.stop]
        )


def test_crange_to_brange_inverts(unicode_text: str, unicode_bytes: bytes):
    bmap = ByteMap(unicode_text)
    for char_start, char_stop in all_ranges(len(unicode_text)):
        crange = CharRange(char_start, char_stop)
        brange = bmap.crange_to_brange(crange)
        crange_ = bmap.brange_to_crange(brange)
        assert crange_ == crange, f"{char_start=}, {char_stop=}"


def test_raises_on_invalid_byte_location(unicode_text: str, unicode_bytes: bytes):
    bmap = ByteMap(unicode_text)

    bmap.byte_to_char(0)
    bmap.byte_to_char(len(unicode_bytes))
    with pytest.raises(IndexError):
        bmap.byte_to_char_checked(-1)
    with pytest.raises(IndexError):
        bmap.byte_to_char_checked(len(unicode_bytes) + 1)

    bmap.brange_to_crange(ByteRange(0, len(unicode_bytes)))
    bmap.brange_to_crange(ByteRange(len(unicode_bytes), len(unicode_bytes)))
    with pytest.raises(IndexError):
        bmap.brange_to_crange_checked(ByteRange(-1, len(unicode_bytes)))
    with pytest.raises(IndexError):
        bmap.brange_to_crange_checked(ByteRange(0, len(unicode_bytes) + 1))


def test_raises_on_invalid_char_location(unicode_text: str):
    bmap = ByteMap(unicode_text)

    bmap.char_to_byte(0)
    bmap.char_to_byte(len(unicode_text))
    with pytest.raises(IndexError):
        bmap.char_to_byte(-1)
    assert bmap.char_to_byte(len(unicode_text) + 10) == len(unicode_text.encode())

    bmap.crange_to_brange(CharRange(0, len(unicode_text)))
    bmap.crange_to_brange(CharRange(len(unicode_text), len(unicode_text)))
    with pytest.raises(IndexError):
        bmap.crange_to_brange(CharRange(-1, len(unicode_text)))
