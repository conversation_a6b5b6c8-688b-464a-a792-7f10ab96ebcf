"""Unit tests for core types."""

from __future__ import annotations

import itertools
from random import Random

import pytest

from base.ranges.range_types import IntRange


@pytest.fixture
def rng() -> Random:
    return Random(42)


def rand_range(
    rng: Random, min_start: int = 0, max_stop: int = 20, min_length: int = 0
):
    """Get a range [min_start, max_stop] at least min_length long (exclusive)."""
    assert min_start < max_stop
    a = rng.randint(min_start, max_stop - min_length)
    b = rng.randint(a + min_length, max_stop)
    return IntRange(a, b)


def test_equals():
    r1 = IntRange(0, 10)
    r2 = IntRange(0, 10)
    r3 = IntRange(2, 10)
    assert r1 == r2
    assert r1 != r3


@pytest.mark.parametrize(
    "smaller, larger",
    [
        # Non-intersecting
        (IntRange(0, 2), IntRange(5, 10)),
        # Adjacent
        (IntRange(0, 5), IntRang<PERSON>(5, 10)),
        # Overlapping
        (IntRange(0, 7), IntRange(5, 10)),
        # Co-starting
        (IntRange(0, 4), IntRange(0, 5)),
        # Co-ending
        (IntRange(0, 5), IntRange(2, 5)),
        # Contained
        (IntRange(0, 5), IntRange(2, 4)),
    ],
)
def test_ordering(smaller: IntRange, larger: IntRange):
    assert smaller < larger
    assert larger > smaller


def test_point():
    r = IntRange.point(9)
    assert r.start == 9
    assert r.stop == 9


def test_is_point():
    assert IntRange(0, 0).is_point()
    assert not IntRange(0, 5).is_point()
    assert IntRange(5, 5).is_point()


def test_invalid_range_raises_exception():
    with pytest.raises(ValueError):
        IntRange(10, 7)


@pytest.mark.parametrize(
    "splitter, expected_left, expected_right",
    [
        # Contained point
        (IntRange(5, 5), IntRange(1, 5), IntRange(5, 9)),
        # Contained range
        (IntRange(4, 6), IntRange(1, 4), IntRange(6, 9)),
        # Co-starting
        (IntRange(1, 5), IntRange(1, 1), IntRange(5, 9)),
        # Co-ending
        (IntRange(5, 9), IntRange(1, 5), IntRange(9, 9)),
        # Overlapping start
        (IntRange(0, 5), None, IntRange(5, 9)),
        # Overlapping end
        (IntRange(5, 10), IntRange(1, 5), None),
        # Superset
        (IntRange(0, 10), None, None),
        # Left adjoining
        (IntRange(0, 1), None, IntRange(1, 9)),
        # Right adjoining
        (IntRange(9, 10), IntRange(1, 9), None),
    ],
)
def test_split_by_range(
    splitter: IntRange, expected_left: IntRange | None, expected_right: IntRange | None
):
    base = IntRange(1, 9)
    left, right = base.split_by_range(splitter)
    assert left == expected_left
    assert right == expected_right


def test_split_by_range_randomized(rng: Random):
    for _ in range(500):
        r = rand_range(rng, min_length=2)
        splitter = rand_range(rng)
        left, right = r.split_by_range(splitter)

        assert (splitter.start <= r.start) != left
        if left is not None:
            assert splitter.start >= r.start
            assert all(i < splitter.start for i in left)
        else:
            assert splitter.start < r.start

        if right is not None:
            assert splitter.stop <= r.stop
            assert all(i >= splitter.stop for i in right)
        else:
            assert splitter.stop > r.stop


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # point ranges are still unioned into the full set.
        (IntRange(0, 0), IntRange(8, 10), IntRange(0, 10)),
        # Disjoint ranges
        (IntRange(0, 2), IntRange(8, 10), IntRange(0, 10)),
        # Adjacent ranges
        (IntRange(0, 5), IntRange(5, 10), IntRange(0, 10)),
        # Overlapping ranges
        (IntRange(0, 6), IntRange(6, 10), IntRange(0, 10)),
        # Contained ranges
        (IntRange(0, 10), IntRange(2, 8), IntRange(0, 10)),
    ],
)
def test_merge(r1, r2, expected):
    assert r1.merge(r2) == expected
    assert r2.merge(r1) == expected


def test_merge_randomized(rng: Random):
    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)
        merged = r1.merge(r2)
        assert min(itertools.chain(r1.points(), r2.points())) == min(merged.points())
        assert max(itertools.chain(r1.points(), r2.points())) == max(merged.points())


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # Disjoint ranges intersect to empty set.
        (IntRange(0, 0), IntRange(8, 10), None),
        # Adjacent ranges intersect to a point.
        (IntRange(0, 5), IntRange(5, 10), IntRange(5, 5)),
        # Overlapping ranges intersect to their region of overlap.
        (IntRange(0, 7), IntRange(5, 10), IntRange(5, 7)),
        # Contained ranges intersect to themselves.
        (IntRange(0, 10), IntRange(5, 7), IntRange(5, 7)),
    ],
)
def test_intersect(r1: IntRange, r2: IntRange, expected: IntRange | None):
    assert r1.intersect(r2) == expected
    assert r2.intersect(r1) == expected


def test_intersect_randomized(rng: Random):
    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)
        intersect = r1.intersect(r2)
        if intersect is None:
            assert set(r1.points()).isdisjoint(r2.points())
        else:
            assert set(r1.points()).intersection(r2.points()) == set(intersect.points())


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # Non-adjacent.
        (IntRange(0, 4), IntRange(5, 10), False),
        # Adjacent.
        (IntRange(0, 5), IntRange(5, 10), False),
        # Overlapping.
        (IntRange(0, 7), IntRange(5, 10), True),
        # Co-starting.
        (IntRange(5, 7), IntRange(5, 10), True),
        # Co-ending.
        (IntRange(7, 10), IntRange(5, 10), True),
        # Contained.
        (IntRange(7, 9), IntRange(5, 10), True),
        # Adjacent point.
        (IntRange(0, 5), IntRange(5, 5), False),
        # Co-starting point.
        (IntRange(5, 5), IntRange(5, 10), False),
        # Co-ending point.
        (IntRange(10, 10), IntRange(5, 10), False),
        # Contained point.
        (IntRange(7, 7), IntRange(5, 10), False),
    ],
)
def test_overlaps(r1: IntRange, r2: IntRange, expected: bool):
    assert r1.overlaps(r2) is expected
    assert r2.overlaps(r1) is expected


def test_overlaps_randomized(rng: Random):
    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)
        assert r1.overlaps(r2) is not set(r1).isdisjoint(set(r2))


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # Non-adjacent.
        (IntRange(0, 4), IntRange(5, 10), False),
        # Adjacent.
        (IntRange(0, 5), IntRange(5, 10), True),
        # Overlapping.
        (IntRange(0, 7), IntRange(5, 10), True),
        # Co-starting.
        (IntRange(5, 7), IntRange(5, 10), True),
        # Co-ending.
        (IntRange(7, 10), IntRange(5, 10), True),
        # Contained.
        (IntRange(7, 9), IntRange(5, 10), True),
        # Adjacent point.
        (IntRange(0, 5), IntRange(5, 5), True),
        # Co-starting point.
        (IntRange(5, 5), IntRange(5, 10), True),
        # Co-ending point.
        (IntRange(10, 10), IntRange(5, 10), True),
        # Contained point.
        (IntRange(7, 7), IntRange(5, 10), True),
        # Non-adjacent point.
        (IntRange(4, 4), IntRange(5, 10), False),
    ],
)
def test_touches(r1: IntRange, r2: IntRange, expected: bool):
    assert r1.touches(r2) is expected
    assert r2.touches(r1) is expected


def test_touches_randomized(rng: Random):
    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)
        assert r1.touches(r2) == bool(set(r1.points()) & set(r2.points()))


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # Non-adjacent.
        (IntRange(0, 4), IntRange(5, 10), False),
        # Adjacent.
        (IntRange(0, 5), IntRange(5, 10), True),
        # Adjacent point.
        (IntRange(0, 5), IntRange(5, 5), True),
        # Overlapping.
        (IntRange(0, 7), IntRange(5, 10), False),
        # Co-starting.
        (IntRange(5, 7), IntRange(5, 10), False),
        # Co-ending.
        (IntRange(7, 10), IntRange(5, 10), False),
        # Contained.
        (IntRange(7, 9), IntRange(5, 10), False),
        # Contained point.
        (IntRange(7, 7), IntRange(5, 10), False),
    ],
)
def test_adjoins(r1: IntRange, r2: IntRange, expected: bool):
    assert r1.adjoins(r2) is expected
    assert r2.adjoins(r1) is expected


@pytest.mark.parametrize(
    "r1, r2, expected12, expected21",
    [
        # Non-adjacent.
        (IntRange(0, 4), IntRange(5, 10), [IntRange(0, 4)], [IntRange(5, 10)]),
        # Adjacent.
        (IntRange(0, 5), IntRange(5, 10), [IntRange(0, 5)], [IntRange(5, 10)]),
        # Overlapping.
        (IntRange(0, 7), IntRange(5, 10), [IntRange(0, 5)], [IntRange(7, 10)]),
        # Co-starting.
        (IntRange(5, 7), IntRange(5, 10), [], [IntRange(7, 10)]),
        # Co-ending.
        (IntRange(7, 10), IntRange(5, 10), [], [IntRange(5, 7)]),
        # Contained.
        (IntRange(7, 9), IntRange(5, 10), [], [IntRange(5, 7), IntRange(9, 10)]),
    ],
)
def test_difference(
    r1: IntRange, r2: IntRange, expected12: IntRange, expected21: IntRange
):
    assert list(r1.difference(r2)) == expected12
    assert list(r2.difference(r1)) == expected21


def test_difference_randomized(rng: Random):
    # We need to remove the stop point from the set of points.
    def points(r: IntRange):
        return set(r.points()) - {r.stop}

    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)
        difference_points = {x for r in r1.difference(r2) for x in points(r)}
        assert difference_points == set(points(r1)) - set(points(r2))


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # Non-adjacent.
        (IntRange(5, 10), IntRange(0, 4), False),
        # Adjacent.
        (IntRange(5, 10), IntRange(0, 5), False),
        # Overlapping start.
        (IntRange(5, 10), IntRange(0, 7), False),
        # Overlapping end.
        (IntRange(5, 10), IntRange(7, 12), False),
        # Co-starting
        (IntRange(5, 10), IntRange(5, 7), True),
        # Co-ending
        (IntRange(5, 10), IntRange(7, 10), True),
        # Contained
        (IntRange(5, 10), IntRange(7, 9), True),
        # Adjacent point.
        (IntRange(5, 10), IntRange(5, 5), True),
        # Adjacent point.
        (IntRange(5, 10), IntRange(10, 10), True),
        # Contained point.
        (IntRange(5, 10), IntRange(7, 7), True),
        # Equal points.
        (IntRange(5, 5), IntRange(5, 5), True),
        # Non-equal points.
        (IntRange(5, 5), IntRange(7, 7), False),
    ],
)
def test_contains(r1: IntRange, r2: IntRange, expected: bool):
    assert r1.contains(r2) is expected


def test_contains_randomized(rng: Random):
    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)

        assert r1.contains(r2) is set(r2.points()).issubset(r1.points())


def test_to_slice():
    base = IntRange(5, 10)
    assert slice(5, 10) == base.to_slice()


def test_to_tuple():
    base = IntRange(5, 10)
    assert (5, 10) == base.to_tuple()


@pytest.mark.parametrize(
    "base, offset, expected",
    [(IntRange(5, 10), 1, IntRange(6, 11)), (IntRange(5, 10), -1, IntRange(4, 9))],
)
def test_shifted(base, offset, expected):
    assert base.shifted(offset) == expected


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # Overlapping.
        (IntRange(0, 5), IntRange(4, 10), 0),
        # Adjacent.
        (IntRange(0, 5), IntRange(5, 10), 0),
        # Non-adjacent.
        (IntRange(0, 5), IntRange(6, 10), 1),
        # Contained point.
        (IntRange(0, 5), IntRange(4, 4), 0),
        # Co-starting point.
        (IntRange(0, 5), IntRange(0, 0), 0),
        # Co-ending point.
        (IntRange(0, 5), IntRange(5, 5), 0),
        # Non-adjacent points.
        (IntRange(5, 5), IntRange(6, 6), 1),
    ],
)
def test_distance(r1: IntRange, r2: IntRange, expected: int):
    assert r1.distance(r2) == expected
    assert r2.distance(r1) == expected


def test_distance_randomized(rng: Random):
    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)

        distance = r1.distance(r2)
        r1set = set(range(r1.start, r1.stop + 1))
        r2set = set(range(r2.start, r2.stop + 1))
        set_distance = min(abs(x - y) for x in r1set for y in r2set)
        assert distance == set_distance, f"{r1=}, {r2=}, {distance=}, {set_distance=}"


def test_len():
    assert len(IntRange(5, 5)) == 0
    assert len(IntRange(5, 10)) == 5


def test_iter():
    assert list(iter(IntRange(5, 10))) == list(range(5, 10))


def test_in():
    left, right = 5, 10
    base = IntRange(left, right)
    assert left - 1 not in base
    for i in range(left, right):
        assert i in base
    assert right not in base


def test_any_overlaps():
    assert not IntRange.any_overlaps(
        [IntRange(0, 5), IntRange(5, 6), IntRange(6, 8), IntRange(8, 10)]
    )
    assert IntRange.any_overlaps(
        [
            IntRange(0, 5),
            IntRange(6, 8),
            IntRange(8, 10),
            IntRange(8, 11),
            IntRange(4, 6),  # the one overlapping sequence
        ]
    )


def test_json_serialization():
    r1 = IntRange(0, 1000)
    s = r1.schema().dumps(r1)  # type: ignore
    r2 = IntRange.schema().loads(s)  # type: ignore
    assert len(s) < 100
    assert r2 == r1


@pytest.mark.parametrize(
    "r1, r2, expected",
    [
        # point ranges are still unioned into the full set.
        (IntRange(0, 0), IntRange(8, 10), [IntRange(0, 0), IntRange(8, 10)]),
        # Disjoint ranges
        (IntRange(0, 2), IntRange(8, 10), [IntRange(0, 2), IntRange(8, 10)]),
        # Adjacent ranges
        (IntRange(0, 5), IntRange(5, 10), [IntRange(0, 10)]),
        # Overlapping ranges
        (IntRange(0, 6), IntRange(4, 10), [IntRange(0, 10)]),
        # Contained ranges
        (IntRange(0, 10), IntRange(2, 8), [IntRange(0, 10)]),
    ],
)
def test_merge_touching(r1, r2, expected):
    assert IntRange.merge_touching([r1, r2]) == expected


def test_merge_touching_randomized(rng: Random):
    for _ in range(500):
        r1 = rand_range(rng)
        r2 = rand_range(rng)
        r3 = rand_range(rng)
        r4 = rand_range(rng)
        ranges_to_merge = [r1, r2, r3, r4]
        merged = IntRange.merge_touching(ranges_to_merge)

        # number of ranges can only decrease
        assert len(merged) <= len(ranges_to_merge)

        # no merged ranges should touch
        for merge1, merge2 in itertools.combinations(merged, 2):
            if merge1 == merge2:
                # ranges will touch themselves, so we skip comparing in this case
                continue
            assert not merge1.touches(merge2)

        # every range_to_merge should be contained in one of the merged ranges
        for r in ranges_to_merge:
            assert any(merge.contains(r) for merge in merged)

        # every merged range should contain at least one of the ranges_to_merge
        for merge in merged:
            assert any(merge.contains(r) for r in ranges_to_merge)
