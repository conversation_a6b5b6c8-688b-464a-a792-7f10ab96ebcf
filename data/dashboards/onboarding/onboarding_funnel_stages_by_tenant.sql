WITH events AS (
    SELECT
        session_id,
        MAX(CASE WHEN (STRING(JSON_QUERY(sanitized_json, '$.event_name')) = "signed-in") THEN 1 ELSE 0 END) AS signed_in,
        MAX(CASE WHEN (STRING(JSON_QUERY(sanitized_json, '$.event_name')) = "first-finished-syncing") THEN 1 ELSE 0 END) AS finished_syncing,
        MAX(CASE WHEN (STRING(JSON_QUERY(sanitized_json, '$.event_name')) = "first-started-syncing") THEN 1 ELSE 0 END) AS started_syncing,
  		MAX(CASE WHEN (STRING(JSON_QUERY(sanitized_json, '$.event_name')) = "first-used-chat") THEN 1 ELSE 0 END) AS used_chat,
  		MAX(CASE WHEN (STRING(JSON_QUERY(sanitized_json, '$.event_name')) = "first-accepted-completion") THEN 1 ELSE 0 END) AS accepted_completion,
  		MAX(CASE WHEN (STRING(JSON_QUERY(sanitized_json, '$.event_name')) = "first-used-slash-action") THEN 1 ELSE 0 END) AS used_slash_action,
  		MAX(CASE WHEN (STRING(JSON_QUERY(sanitized_json, '$.event_name')) = "saw-summary") THEN 1 ELSE 0 END) AS saw_summary,
    FROM
        `system-services-prod.us_prod_request_insight_analytics_dataset.onboarding_session_event`
  	WHERE
  		SUBSTR(
          JSON_VALUE(sanitized_json, '$.user_agent'),
          STRPOS(JSON_VALUE(sanitized_json, '$.user_agent'), 'Augment.vscode-augment/') + 23,
          7
        ) >= '0.284.0'
  		AND
  		tenant=@tenant
  		AND
        DATE(time, @TIMEZONE) BETWEEN
  			PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
    GROUP BY
        session_id
)

SELECT
    'Signed In' AS funnel_stage,
    COUNT(session_id) AS user_count
FROM
    events
WHERE
    signed_in = 1

UNION ALL

SELECT
    'Saw Summary' AS funnel_stage,
    COUNT(session_id) AS saw_summary
FROM
    events
WHERE
	saw_summary = 1
AND
	signed_in = 1

UNION ALL

SELECT
    'Finished Syncing' AS funnel_stage,
    COUNT(session_id) AS user_count
FROM
    events
WHERE
    finished_syncing = 1
AND
	signed_in = 1

UNION ALL

SELECT
    'Used Chat' AS funnel_stage,
    COUNT(session_id) AS user_count
FROM
    events
WHERE
	used_chat = 1
AND
	signed_in = 1
AND
	saw_summary = 1

UNION ALL

SELECT
    'Accepted Completion' AS funnel_stage,
    COUNT(session_id) AS user_count
FROM
    events
WHERE
	accepted_completion = 1
AND
	signed_in = 1
AND
	saw_summary = 1

UNION ALL

SELECT
    'Used Slash Action' AS funnel_stage,
    COUNT(session_id) AS user_count
FROM
    events
WHERE
	used_slash_action = 1
AND
	signed_in = 1
AND
	saw_summary = 1
