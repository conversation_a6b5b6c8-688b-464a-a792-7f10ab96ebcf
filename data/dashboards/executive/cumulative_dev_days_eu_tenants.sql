-- Cumulative Dev Days for EU Tenants (Last 90 Days)
-- Automatically pulls last 90 days of data and normalizes time to US/Pacific

WITH UnifiedResolutions AS (
    SELECT
        resolution.time,
        request.user_id,
        'completion' AS resolution_type
    FROM
        eu_prod_request_insight_analytics_dataset.completion_resolution AS resolution
    JOIN
        eu_prod_request_insight_analytics_dataset.customers_request_metadata AS request
        ON resolution.request_id = request.request_id
    WHERE
        resolution.accepted
        AND DATE(resolution.time, 'America/Los_Angeles') >= DATE_SUB(CURRENT_DATE('America/Los_Angeles'), INTERVAL 90 DAY)
        AND DATE(resolution.time, 'America/Los_Angeles') < CURRENT_DATE('America/Los_Angeles')

    UNION ALL

    SELECT
        resolution.time,
        request.user_id,
        'instruction' AS resolution_type
    FROM
        eu_prod_request_insight_analytics_dataset.edit_resolution AS resolution
    JOIN
        eu_prod_request_insight_analytics_dataset.customers_request_metadata AS request
        ON resolution.request_id = request.request_id
    WHERE
        resolution.accepted
        AND DATE(resolution.time, 'America/Los_Angeles') >= DATE_SUB(CURRENT_DATE('America/Los_Angeles'), INTERVAL 90 DAY)
        AND DATE(resolution.time, 'America/Los_Angeles') < CURRENT_DATE('America/Los_Angeles')

    UNION ALL

    SELECT
        response.time,
        request.user_id,
        'next_edit' AS resolution_type
    FROM
        eu_prod_request_insight_analytics_dataset.next_edit_host_response AS response
    JOIN
        eu_prod_request_insight_analytics_dataset.customers_request_metadata AS request
        ON response.request_id = request.request_id
    WHERE
        response.character_counts IS NOT NULL
        AND DATE(response.time, 'America/Los_Angeles') >= DATE_SUB(CURRENT_DATE('America/Los_Angeles'), INTERVAL 90 DAY)
        AND DATE(response.time, 'America/Los_Angeles') < CURRENT_DATE('America/Los_Angeles')

    UNION ALL

    SELECT
        time,
        user_id,
        'chat' AS resolution_type
    FROM
         eu_prod_request_insight_analytics_dataset.customers_request_metadata
    WHERE
        request_type = 'CHAT'
        AND DATE(time, 'America/Los_Angeles') >= DATE_SUB(CURRENT_DATE('America/Los_Angeles'), INTERVAL 90 DAY)
        AND DATE(time, 'America/Los_Angeles') < CURRENT_DATE('America/Los_Angeles')
),
DeveloperDays AS (
    SELECT
        DATE(resolution.time, 'America/Los_Angeles') AS event_date_eu,
        COUNT(DISTINCT user_id) AS developer_days
    FROM
        UnifiedResolutions AS resolution
    GROUP BY
        event_date_eu
)

SELECT
    event_date_eu,
    developer_days,
    SUM(developer_days) OVER (ORDER BY event_date_eu) AS cumulative_developer_days
FROM
    DeveloperDays
ORDER BY
    event_date_eu;
