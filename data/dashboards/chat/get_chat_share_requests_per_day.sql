-- Get the number of get chat share requests per day, in staging.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @ENV: List of environments to query [PROD, STAGING].

SELECT
  DATE(share_request.time, @TIMEZONE) AS day,
  COUNT(DISTINCT share_request.request_id) AS get_shared_chat_requests
FROM us_staging_request_insight_analytics_dataset.share_get_chat_request AS share_request
WHERE
	share_request.env IN UNNEST(@ENV)
  AND DATE(share_request.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY day
;

