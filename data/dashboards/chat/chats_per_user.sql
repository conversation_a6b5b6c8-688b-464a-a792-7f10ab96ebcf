-- Count chat requests per user.
-- Number of chat requests, per message category, in prod.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @REQUEST_TYPES: List of request types to include towards the chat count.

SELECT
  metadata.user_id AS user_id,
  MAX(metadata.tenant) AS tenant,
  COUNT(DISTINCT request_id) AS request_count
FROM  us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata
WHERE
  metadata.request_type IN UNNEST(@REQUEST_TYPES)
  AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY
  metadata.tenant_id, metadata.user_id
;
