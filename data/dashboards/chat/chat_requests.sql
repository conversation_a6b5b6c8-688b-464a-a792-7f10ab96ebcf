-- Number of chat requests, by date.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @REQUEST_TYPES: List of request types to include.

SELECT
	DATE(metadata.time, @TIMEZONE) AS request_date,
	COUNT(DISTINCT metadata.request_id) AS request_count
FROM us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata
WHERE
  metadata.request_type IN UNNEST(@REQUEST_TYPES)
	AND DATE(metadata.time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
	AND DATE(metadata.time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY request_date
;
