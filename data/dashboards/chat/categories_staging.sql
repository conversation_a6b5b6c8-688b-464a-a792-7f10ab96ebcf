-- Number of chat requests, per message category, in staging.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.

SELECT
  category,
  COUNT(DISTINCT request_id) AS category_count
FROM
  us_staging_request_insight_analytics_dataset.chat_host_request req,
  UNNEST(message_categories) AS category
WHERE
  JSON_VALUE(req.sanitized_json, '$.request_source') IN UNNEST(@REQUEST_SOURCES)
  AND DATE(req.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY
  category
;
