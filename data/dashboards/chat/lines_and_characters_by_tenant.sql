-- Number of lines and characters in chat responses, by tenant.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @REQUEST_TYPES: List of request types to include.

SELECT
  response.tenant,
  SUM(response.line_count) AS total_line_count,
  SUM(response.character_count) AS total_character_count
FROM
  us_prod_request_insight_analytics_dataset.chat_host_response AS response
-- Filter out non-customer tenants and non-human requests.
JOIN us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata USING(request_id)
WHERE
  metadata.request_type IN UNNEST(@REQUEST_TYPES)
  AND DATE(response.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY tenant
;
