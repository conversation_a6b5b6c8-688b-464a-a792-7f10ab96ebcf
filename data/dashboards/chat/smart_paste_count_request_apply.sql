WITH instruction_request AS (
  SELECT
    request_id,
    env,
    tenant,
    time,
  FROM `us_cross_env_request_insight_analytics_dataset.instruction_host_request`
  WHERE code_block_character_count > 0
  AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
  AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
),
create_clicks AS (
  SELECT
    tenant,
    env,
    time,
    CAST(JSON_VALUE(sanitized_json, '$.value') AS INT64) as count_create
  FROM `us_cross_env_request_insight_analytics_dataset.client_metric`
  WHERE JSON_EXTRACT_SCALAR(sanitized_json, '$.client_metric') = 'webview__chat__chat-codeblock-create'
  AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
  AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
),
smart_paste_ranked_rows AS (
  SELECT
    request_id,
    env,
    tenant,
    time,
    sanitized_json,
    ROW_NUMBER() OVER (PARTITION BY request_id ORDER BY time DESC) AS row_num
  FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.smart_paste_client_timeline`
  WHERE DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
  AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
),
smart_paste_timelines AS (
  SELECT * EXCEPT(row_num),
  FROM smart_paste_ranked_rows
  WHERE row_num = 1
),
metadata AS (
  SELECT
    request_id,
    CASE
      WHEN CONTAINS_SUBSTR(user_agent, 'cursor') THEN 'Cursor'
      WHEN CONTAINS_SUBSTR(user_agent, 'vscode') THEN 'VSCode'
      WHEN CONTAINS_SUBSTR(user_agent, 'intellij') THEN 'IntelliJ'
      ELSE 'Other'
    END AS editor
  FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.request_metadata`
  WHERE DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
  AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
)
SELECT
  COALESCE(instruction_request.env, smart_paste_timelines.env) AS env,
  COALESCE(instruction_request.tenant, smart_paste_timelines.tenant) AS tenant,
  COALESCE(instruction_request.time, smart_paste_timelines.time) AS time,
  COALESCE(metadata.editor, "Other") as editor,
  CASE WHEN instruction_request.request_id IS NOT NULL THEN 1 END AS count_smart_paste,
  CASE WHEN JSON_VALUE(smart_paste_timelines.sanitized_json, '$.apply_time') IS NOT NULL THEN 1 END AS count_apply,
  0 AS count_create,
FROM instruction_request
FULL OUTER JOIN smart_paste_timelines USING(request_id)
FULL OUTER JOIN metadata USING(request_id)

UNION ALL

SELECT
  env,
  tenant,
  time,
  NULL as editor,
  0 AS count_smart_paste,
  0 AS count_apply,
  count_create,
FROM create_clicks
