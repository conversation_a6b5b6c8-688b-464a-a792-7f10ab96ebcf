WITH date_range AS (
  SELECT date
  FROM UNNEST(GENERATE_DATE_ARRAY(
    PARSE_DATE('%Y%m%d', @DS_START_DATE),
    PARSE_DATE('%Y%m%d', @DS_END_DATE)
  )) AS date
),
requests AS (
  SELECT DISTINCT
    resolution.request_id,
    resolution.tenant_id,
    metadata.user_id,
    resolution.env,
    resolution.tenant,
    CAST(resolution.time AS DATE) as date,
    CASE
      WHEN CONTAINS_SUBSTR(metadata.user_agent, 'cursor') THEN 'Cursor'
      WHEN STARTS_WITH(metadata.user_agent, "Augment.vscode-augment") THEN 'VSCode'
      WHEN STARTS_WITH(metadata.user_agent, "augment.intellij") THEN 'IntelliJ'
      ELSE 'Other'
    END AS editor
  FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.smart_paste_resolution` AS resolution
  LEFT JOIN `system-services-prod.us_cross_env_request_insight_analytics_dataset.customers_request_metadata` AS metadata USING(request_id)
  WHERE DATE(resolution.time, @TIMEZONE) >= DATE_SUB(PARSE_DATE('%Y%m%d', @DS_START_DATE), INTERVAL 6 DAY)
  AND DATE(resolution.time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
  AND DATE(metadata.time, @TIMEZONE) >= DATE_SUB(PARSE_DATE('%Y%m%d', @DS_START_DATE), INTERVAL 6 DAY)
  AND DATE(metadata.time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
),
count_by_date AS (
  SELECT
    date,
    env,
    tenant,
    editor,
    COUNT(DISTINCT user_id) AS count_users,
    COUNT(DISTINCT tenant_id) AS count_tenants
  FROM requests
  GROUP BY date, env, tenant, editor
)
SELECT
  dr.date,
  env,
  tenant,
  editor,
  COUNT(DISTINCT CASE WHEN r.date BETWEEN DATE_SUB(dr.date, INTERVAL 6 DAY) AND dr.date THEN user_id END) AS rolling_users_7d,
  COUNT(DISTINCT CASE WHEN r.date BETWEEN DATE_SUB(dr.date, INTERVAL 6 DAY) AND dr.date THEN tenant_id END) AS rolling_tenants_7d,
FROM date_range dr
CROSS JOIN requests r
GROUP BY date, env, tenant, editor
ORDER BY tenant, date, editor
