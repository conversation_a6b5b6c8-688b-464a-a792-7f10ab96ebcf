-- Days between first completion and first chat request.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.

WITH first_completion AS (
  -- Count the first completion as the first accepted resolution.
  SELECT
    metadata.user_id,
    metadata.tenant_id,
    MIN(metadata.tenant) AS tenant,
    MIN(completion_resolution.time) AS time
  FROM us_prod_request_insight_analytics_dataset.completion_resolution
  JOIN us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata USING (request_id)
  WHERE
    completion_resolution.accepted
    AND DATE(completion_resolution.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
    AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY metadata.tenant_id, metadata.user_id
),
first_chat_request AS (
  -- Just use metadata to count the first chat request. Ignoring Slackbot for this one.
  SELECT
    metadata.user_id,
    metadata.tenant_id,
    MIN(metadata.tenant) AS tenant,
    MIN(metadata.time) AS time
  from us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata
  WHERE
    metadata.request_type = 'CHAT'
    AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY metadata.tenant_id, metadata.user_id
)
SELECT
  first_chat_request.user_id AS user,
  first_chat_request.tenant AS tenant,
  TIMESTAMP_DIFF(first_chat_request.time, first_completion.time, DAY) AS completion_chat_difference_days,
FROM first_chat_request
JOIN first_completion USING (tenant_id, user_id)
;
