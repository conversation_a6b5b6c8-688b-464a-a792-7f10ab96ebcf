-- Number of days between onboarding and first chat request.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.

WITH first_event AS (
  -- First event of any type is approximately when the user onboarded.
  SELECT
    metadata.user_id,
    metadata.tenant_id,
    MIN(metadata.tenant) AS tenant,
    MIN(metadata.time) AS time
  from us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata
  WHERE DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY metadata.user_id, metadata.tenant_id
),
first_chat_request AS (
  -- Just use metadata to count the first chat request. Ignoring Slackbot for this one.
  SELECT
    metadata.user_id,
    metadata.tenant_id,
    <PERSON><PERSON>(metadata.tenant) AS tenant,
    MIN(metadata.time) AS time
  from us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata
  WHERE
    metadata.request_type = 'CHAT'
    AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY metadata.tenant_id, metadata.user_id
)

SELECT
  DATE(first_event.time, @TIMEZONE) AS onboard_date,
  AVG(TIMESTAMP_DIFF(first_chat_request.time, first_event.time, DAY)) AS days_to_first_chat
FROM first_chat_request
JOIN first_event USING (tenant_id, user_id)
GROUP BY onboard_date
;
