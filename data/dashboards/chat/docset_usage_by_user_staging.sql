-- Get the number of docset requests per user, in staging.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.

SELECT
  metadata.user_id,
  MAX(metadata.tenant) AS tenant,
  COUNT(DISTINCT metadata.request_id) AS request_count
FROM us_staging_request_insight_analytics_dataset.chat_host_request AS chat_request
JOIN us_staging_request_insight_analytics_dataset.customers_request_metadata AS metadata USING (request_id)
WHERE masked_external_source_ids IS NOT NULL
  AND ARRAY_LENGTH(masked_external_source_ids) > 0
  AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  AND DATE(chat_request.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY metadata.tenant_id, metadata.user_id
;
