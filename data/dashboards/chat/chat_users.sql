-- Count chat vs non-chat users.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @REQUEST_TYPES: List of request types to include as chat.

WITH chat_users AS (
  SELECT
    DISTINCT user_id
  FROM us_prod_request_insight_analytics_dataset.customers_request_metadata
  WHERE
    request_type IN UNNEST(@REQUEST_TYPES)
    AND DATE(time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
),

all_users AS (
  SELECT
    DISTINCT user_id
  FROM us_prod_request_insight_analytics_dataset.customers_request_metadata
  WHERE
    DATE(time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
)

SELECT
  'Chat Users' AS category,
  COUNT(DISTINCT user_id) AS count
FROM
  chat_users

UNION ALL

SELECT
  'Non-Chat Users' AS category,
  COUNT(DISTINCT user_id) AS count
FROM
  all_users
WHERE
  user_id NOT IN (SELECT user_id FROM chat_users);
