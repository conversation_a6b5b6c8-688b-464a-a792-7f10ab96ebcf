WITH ranked_rows AS (
  SELECT
    request_id,
    env,
    tenant,
    time,
    sanitized_json,
    ROW_NUMBER() OVER (PARTITION BY request_id ORDER BY time DESC) AS row_num
  FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.smart_paste_resolution`
  WHERE DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
  AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
),
smart_paste_resolutions AS (
  SELECT * EXCEPT(row_num),
  FROM ranked_rows
  WHERE row_num = 1
),
metadata AS (
  SELECT
    request_id,
    CASE
      WHEN CONTAINS_SUBSTR(user_agent, 'cursor') THEN 'Cursor'
      WHEN STARTS_WITH(user_agent, "Augment.vscode-augment") THEN 'VSCode'
      WHEN STARTS_WITH(user_agent, "augment.intellij") THEN 'IntelliJ'
      ELSE 'Other'
    END AS editor
  FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.request_metadata`
  WHERE DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
  AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
),
flattened_data AS (
  SELECT
    request_id,
    JSON_EXTRACT_ARRAY(sanitized_json, '$.is_accepted_chunks') AS chunks,
  FROM smart_paste_resolutions
),
unnested_data AS (
  SELECT
    request_id,
    JSON_VALUE(chunk) = 'true' AS is_accepted
  FROM
    flattened_data,
    UNNEST(chunks) AS chunk
),
counts AS (
  SELECT
    request_id,
    COUNTIF(is_accepted = TRUE) AS accept_count,
    COUNTIF(is_accepted = FALSE) AS reject_count
  FROM unnested_data
  GROUP BY request_id
),
result AS (
  SELECT
    smart_paste_resolutions.*,
    counts.accept_count,
    counts.reject_count,
    JSON_VALUE(sanitized_json, '$.is_accept_all') AS is_accept_all,
    JSON_VALUE(sanitized_json, '$.is_reject_all') AS is_reject_all,
    metadata.editor
  FROM smart_paste_resolutions
  FULL OUTER JOIN counts USING(request_id)
  LEFT JOIN metadata USING(request_id)
)
SELECT
  env,
  tenant,
  time,
  editor,
  accept_count,
  reject_count,
  is_accept_all,
  CASE
    WHEN editor = 'IntelliJ' THEN
      CASE WHEN is_reject_all = 'true' THEN TRUE END -- reject all in intellij reverts all suggestions
    ELSE
	    CASE WHEN is_reject_all = 'true' AND reject_count != 0 THEN TRUE END
  END AS is_reject_all,
  CASE
    WHEN editor = 'IntelliJ' THEN
      CASE WHEN is_reject_all IS NULL AND is_accept_all IS NULL THEN TRUE END -- is_closed_tab in intellij just means the user closed the tab
    ELSE
      CASE WHEN is_reject_all = 'true' AND reject_count IS NULL THEN TRUE END -- is_closed_tab in vscode means we lose reject count and accept count
  END AS is_closed_tab,
FROM result
