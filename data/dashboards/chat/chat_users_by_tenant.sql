-- Count chat vs non-chat users.
-- Number of chat requests, per message category, in prod.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @REQUEST_TYPES: List of request types to include towards the chat count.

WITH chat_users AS (
  SELECT
    tenant_id,
    MIN(tenant) AS tenant,
    COUNT(DISTINCT user_id) AS user_count
  FROM us_prod_request_insight_analytics_dataset.customers_request_metadata
  WHERE
    request_type in UNNEST(@REQUEST_TYPES)
    AND DATE(time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY tenant_id
),

active_users AS (
  SELECT
    tenant_id,
    MIN(tenant) AS tenant,
    COUNT(DISTINCT req_meta.user_id) AS user_count
  FROM
    us_prod_request_insight_analytics_dataset.customers_request_metadata AS req_meta
  WHERE
    DATE(time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY
    tenant_id
)

SELECT
  active_users.tenant,
  COALESCE(chat_users.user_count, 0) AS num_chat_users,
  COALESCE(active_users.user_count, 0) AS num_active_users,
  ROUND(COALESCE(chat_users.user_count, 0) / NULLIF(COALESCE(active_users.user_count, 0), 0) * 100, 2) AS chat_user_percentage
FROM active_users
LEFT JOIN chat_users USING (tenant_id)
;
