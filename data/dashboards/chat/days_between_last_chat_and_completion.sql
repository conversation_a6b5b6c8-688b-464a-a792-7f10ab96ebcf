-- Number of days between the last completion request and the first chat request. For users where
-- this difference is high, this indicates that somebody uses chat but not completions.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.

WITH
  -- Get the last completion request date for each user
  last_completion AS (
    SELECT
      user_id,
      tenant_id,
      MAX(tenant),
      MAX(time) AS time
    FROM us_prod_request_insight_analytics_dataset.customers_request_metadata
    WHERE
      request_type = 'COMPLETION'
      AND DATE(time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
    GROUP BY tenant_id, user_id
  ),

  -- Get the chat requests that are more than 3 days after the last completion request
  late_chat_request AS (
    SELECT
      metadata.user_id,
      metadata.tenant_id,
      MAX(metadata.tenant) AS tenant,
      MIN(metadata.time) AS chat_request_time,
      MAX(last_completion.time) AS last_completion_time,
    FROM us_prod_request_insight_analytics_dataset.customers_request_metadata metadata
    JOIN last_completion USING (tenant_id, user_id)
    WHERE
      metadata.request_type IN UNNEST(@REQUEST_TYPES)
      AND metadata.time > TIMESTAMP_ADD(last_completion.time, INTERVAL 3 DAY)
      AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
    GROUP BY metadata.tenant_id, metadata.user_id
  )

SELECT
  user_id,
  tenant,
  DATE(chat_request_time, @TIMEZONE) AS chat_request_date,
  DATE(last_completion_time, @TIMEZONE) AS completion_request_date,
  DATE_DIFF(DATE(chat_request_time, @TIMEZONE), DATE(last_completion_time, @TIMEZONE), DAY) AS days_between_chat_and_completion
FROM late_chat_request
;
