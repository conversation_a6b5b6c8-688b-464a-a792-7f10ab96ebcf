-- Get the number of save chat share requests per user.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @ENV: List of environments to query [PROD, STAGING].

SELECT
  metadata.user_id,
  MAX(metadata.tenant) AS tenant,
  COUNT(DISTINCT metadata.request_id) AS request_count
FROM us_cross_env_request_insight_analytics_dataset.share_save_chat_request AS share_request
JOIN us_cross_env_request_insight_analytics_dataset.customers_request_metadata AS metadata USING (request_id)
WHERE 
  share_request.env IN UNNEST(@ENV)
  AND DATE(metadata.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  AND DATE(share_request.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY metadata.tenant_id, metadata.user_id
;
