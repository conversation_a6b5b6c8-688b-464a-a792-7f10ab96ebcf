-- Get the number of save chat share requests per day.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @ENV: List of environments to query [PROD, STAGING].

SELECT
  DATE(share_request.time, @TIMEZONE) AS day,
  COUNT(DISTINCT share_request.request_id) AS save_shared_chat_requests
FROM us_cross_env_request_insight_analytics_dataset.share_save_chat_request AS share_request
WHERE
  share_request.env IN UNNEST(@ENV)
  AND DATE(share_request.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY day
;
