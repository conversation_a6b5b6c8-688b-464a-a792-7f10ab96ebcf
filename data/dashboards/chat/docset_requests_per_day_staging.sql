-- Get the number of docset requests per day, in staging.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.

SELECT
  DATE(chat_request.time, @TIMEZONE) AS day,
  COUNT(DISTINCT chat_request.request_id) AS requests_with_docsets
FROM us_staging_request_insight_analytics_dataset.chat_host_request AS chat_request
WHERE
  chat_request.masked_external_source_ids IS NOT NULL
  AND ARRAY_LENGTH(chat_request.masked_external_source_ids) > 0
  AND DATE(chat_request.time, @TIMEZONE) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY day
;
