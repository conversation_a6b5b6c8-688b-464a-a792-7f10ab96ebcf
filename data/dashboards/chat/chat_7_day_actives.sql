-- Number of distinct chat users over a 7 day period.
-- Parameters:
--   @DS_START_DATE: Start date, in YYYYMMDD format.
--   @DS_END_DATE: End date, in YYYYMMDD format.
--   @TIMEZONE: Timezone to use for the date.
--   @REQUEST_TYPES: List of request types to include.

SELECT
  date,
  COUNT(DISTINCT metadata.user_id) AS unique_active_users
FROM UNNEST(GENERATE_DATE_ARRAY(
  PARSE_DATE('%Y%m%d', @DS_START_DATE),
  PARSE_DATE('%Y%m%d', @DS_END_DATE))
) AS date
LEFT JOIN us_prod_request_insight_analytics_dataset.customers_request_metadata AS metadata
  ON DATE(metadata.time, @TIMEZONE) BETWEEN DATE_SUB(date, INTERVAL 6 DAY) AND date
    AND metadata.request_type IN UNNEST(@REQUEST_TYPES)
GROUP BY
  date
;
