WITH app_mention AS (
  SELECT
    DATE(time, @TIMEZONE) AS request_date,
    JSON_VALUE(sanitized_json.channel_type) AS channel_type,
    COUNT(DISTINCT request_id) AS channel_type_count
  FROM us_cross_env_request_insight_analytics_dataset.slackbot_app_mention
  WHERE
    env IN UNNEST(@ENV)
    AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
    AND JSON_VALUE(sanitized_json.channel_type) IS NOT NULL
  GROUP BY request_date, JSON_VALUE(sanitized_json.channel_type)
),

message AS (
  SELECT
    DATE(time, @TIMEZONE) AS request_date,
    JSON_VALUE(sanitized_json.channel_type) AS channel_type,
    COUNT(DISTINCT request_id) AS channel_type_count
  FROM us_cross_env_request_insight_analytics_dataset.slackbot_message
  WHERE
    env IN UNNEST(@ENV)
    AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
    AND JSON_VALUE(sanitized_json.channel_type) IS NOT NULL
  GROUP BY request_date, JSON_VALUE(sanitized_json.channel_type)
)

-- Note that message events always have channel_type 'IM' and app_mention events never have
-- channel_type 'IM', so it's safe to do a union here. If that assumption ever changes we'll need to
-- update the query.

SELECT
  app_mention.request_date,
  app_mention.channel_type,
  app_mention.channel_type_count
FROM app_mention

UNION ALL

SELECT
  message.request_date,
  message.channel_type,
  message.channel_type_count
FROM message
;
