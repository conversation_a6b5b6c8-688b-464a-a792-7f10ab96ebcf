WITH app_mention AS (
  SELECT
    DATE(time, @TIMEZONE) AS request_date,
    tenant,
    JSON_EXTRACT_SCALAR(sanitized_json, '$.slack_event.app_mention.user') as user_id,
    COUNT(DISTINCT request_id) as request_count,
  FROM us_cross_env_request_insight_analytics_dataset.slackbot_app_mention
  WHERE
    env IN UNNEST(@ENV)
    AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY request_date, tenant, user_id
),

message AS (
  SELECT
    DATE(time, @TIMEZONE) AS request_date,
    tenant,
    JSON_EXTRACT_SCALAR(sanitized_json, '$.slack_event.message.user') as user_id,
    COUNT(DISTINCT request_id) as request_count,
  FROM us_cross_env_request_insight_analytics_dataset.slackbot_message
  WHERE
    env IN UNNEST(@ENV)
    AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY request_date, tenant, user_id
)

SELECT
  COALESCE(app_mention.request_date, message.request_date) AS request_date,
  COALESCE(app_mention.tenant, message.tenant) AS tenant,
  COALESCE(app_mention.user_id, message.user_id) AS user_id,
  COALESCE(app_mention.request_count, 0) AS mentions,
  COALESCE(message.request_count, 0) AS messages
FROM app_mention
FULL OUTER JOIN message
  ON app_mention.request_date = message.request_date
  AND app_mention.tenant = message.tenant
  AND app_mention.user_id = message.user_id
WHERE
  COALESCE(app_mention.request_date, message.request_date) IS NOT NULL
;
