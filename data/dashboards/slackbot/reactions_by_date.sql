WITH all_reactions AS (
SELECT
  ROW_NUMBER() OVER (PARTITION BY request_id ORDER BY time DESC) AS row_num,
  DATE(time, @TIMEZONE) AS request_date,
  JSON_VALUE(reaction_added.reaction) AS reaction,
FROM us_cross_env_request_insight_analytics_dataset.slackbot_reaction_added
WHERE
  env IN UNNEST(@ENV)
  AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
  AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
)
-- Some request_ids are repeated, possible due to retries.  Remove those
SELECT
  request_date,
  ARRAY_AGG(reaction) AS reactions
FROM all_reactions
WHERE row_num = 1
GROUP BY request_date
