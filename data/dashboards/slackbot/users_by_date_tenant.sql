WITH app_mention AS (
  SELECT
    DATE(time, @TIMEZONE) AS request_date,
    ARRAY_AGG(JSON_VALUE(app_mention.user)) AS users,
    tenant,
  FROM us_cross_env_request_insight_analytics_dataset.slackbot_app_mention
  WHERE
    env IN UNNEST(@ENV)
    AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY request_date, tenant
),

message AS (
  SELECT
    DATE(time, @TIMEZONE) AS request_date,
    ARRAY_AGG(JSON_VALUE(message.user)) AS users,
    tenant,
  FROM us_cross_env_request_insight_analytics_dataset.slackbot_message
  WHERE
    env IN UNNEST(@ENV)
    AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY request_date, tenant
)

SELECT
  COALESCE(app_mention.request_date, message.request_date) AS request_date,
  COALESCE(app_mention.tenant, message.tenant) AS tenant,
  ARRAY_CONCAT(IFNULL(app_mention.users, ARRAY<STRING>[]), IFNULL(message.users, ARRAY<STRING>[])) AS users,
FROM app_mention
FULL OUTER JOIN message
  ON app_mention.request_date = message.request_date
  AND app_mention.tenant = message.tenant
WHERE
  COALESCE(app_mention.request_date, message.request_date) IS NOT NULL
;
