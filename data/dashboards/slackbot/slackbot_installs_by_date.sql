
WITH installation_events AS (
  SELECT
    DATETIME(time, @TIMEZONE) AS request_datetime,
    DATE(DATETIME(time, @TIMEZONE)) AS request_date,
    JSON_VALUE(sanitized_json, '$.event_type') AS event_type,
    tenant,
    COUNT(DISTINCT tenant) AS event_count
  FROM us_prod_request_insight_analytics_dataset.slackbot_installation_event
  WHERE
    DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
  	AND JSON_VALUE(sanitized_json, '$.event_type') IN ('INSTALL', 'UNINSTALL')
  GROUP BY request_date, event_type, tenant, request_datetime
)

SELECT
  request_date,
  request_datetime,
  IFNULL(SUM(CASE WHEN event_type = 'INSTALL' THEN event_count ELSE 0 END), 0) AS install_count,
  IFNULL(SUM(CASE WHEN event_type = 'UNINSTALL' THEN event_count ELSE 0 END), 0) AS uninstall_count,
  tenant,
  event_type
FROM installation_events
GROUP BY request_date, tenant, event_type, request_datetime
ORDER BY request_date DESC
;
