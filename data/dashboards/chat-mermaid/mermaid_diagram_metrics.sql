WITH metrics AS (
  SELECT
    DATE(time, @TIMEZONE) AS request_date,
    JSON_VALUE(sanitized_json, '$.client_metric') AS metric_name,
    COUNT(sanitized_json) AS count
  FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.client_metric`
  WHERE JSON_VALUE(sanitized_json, '$.client_metric') IN (
    'webview__chat__chat-mermaidblock-initialize',
    'webview__chat__chat-mermaidblock-toggle',
    'webview__chat__chat-mermaidblock-interact',
    'webview__chat__chat-mermaidblock-error'
  )
    AND DATE(time, @TIMEZONE) >= PARSE_DATE('%Y%m%d', @DS_START_DATE)
    AND DATE(time, @TIMEZONE) <= PARSE_DATE('%Y%m%d', @DS_END_DATE)
  GROUP BY request_date, metric_name
)
SELECT
  request_date,
  IFNULL(MAX(CASE WHEN metric_name = 'webview__chat__chat-mermaidblock-initialize' THEN count END), 0) AS initialize_count,
  IFNULL(MAX(CASE WHEN metric_name = 'webview__chat__chat-mermaidblock-toggle' THEN count END), 0) AS toggle_count,
  IFNULL(MAX(CASE WHEN metric_name = 'webview__chat__chat-mermaidblock-interact' THEN count END), 0) AS interact_count,
  IFNULL(MAX(CASE WHEN metric_name = 'webview__chat__chat-mermaidblock-error' THEN count END), 0) AS error_count
FROM metrics
GROUP BY request_date
ORDER BY request_date
