/*
 * Definition of TENANT specific flags and default values.
 *
 * Changing the default value of a flag should only be done
 * when the new value is ready to be used everywhere. Once
 * confidence is gained by running everywhere with the new
 * value, the flag should be removed.
 */
local flags_lib = import 'deploy/tenants/namespace_configs/flags-lib.jsonnet';

local flags = {
  override:: function(field, val) flags_lib.override(self, field, val),

  supportAccessControl: true,
  supportAccessControl_: {
    owner: 'dirk',
    date: '2024/01/04',
    description: |||
      Enable support access control. Access control is disabled by default in dev environments
      and enabled by default in staging/prod environments.

      DO NOT DO THIS WITHOUT EXPLICIT CONSENT FROM THE CUSTOMER
    |||,
    name: 'support_access_control',
  },

  multiTenantAllowed: false,
  multiTenantAllowed_: {
    owner: 'dirk',
    date: '2024/05/28',
    description: |||
      Enables multi-tenancy for a tenant.

      If set to false, a namespace will by created for the tenant.
      Do not change this flag for ANY existing tenant.
    |||,
    name: 'multi_tenant_allowed',
  },

  supportTenant: false,
  supportTenant_: {
    owner: 'dirk',
    date: '2024/06/09',
    description: |||
      A tenant in a shard namespace that to be used for support.

      Each shard namespace needs a support tenant with the same
      name as the shard namespace. The health check will operate
      in the support tenant of the shard namespace.
    |||,
    name: 'support_tenant',
  },

  defaultSupportTenant: false,
  defaultSupportTenant_: {
    owner: 'aswin',
    date: '2024/09/17',
    description: |||
      Use this tenant as the default tenant in the support UI.

      Mainly intended for use in dev deploys.
    |||,
    name: 'default_support_tenant',
  },

  slackbotAllowlistChannels: [],
  slackbotAllowlistChannels_: {
    owner: 'aswin',
    date: '2024/11/22',
    description: |||
      Restricts Slackbot responses to specified channels and direct messages when set to a non-empty list.
      When configured, the bot will only respond in:
      - Listed channels
      - Direct messages with listed users

      If enough customers end up using this, we should move this to be a real
      setting in the Admin UI instead.
    |||,
    name: 'slackbot_allowlist_channels',
  },

  blockGenieRequestAccess: false,
  blockGenieRequestAccess_: {
    owner: 'cam',
    date: '2025/02/05',
    description: |||
      If true, blocks Genie access requests for this tenant.

      This means we can't provide support without a code change.

      DO NOT DISABLE THIS WITHOUT EXPLICIT CONSENT FROM THE CUSTOMER
    |||,
    name: 'block_genie_request_access',
  },

  ipAllowlist: '',
  ipAllowlist_: {
    owner: 'mattm',
    date: '2025/03/14',
    description: |||
      Additional IP-based authorization checks restricting tenant users' IP
      address.  A CSV list of IP (v4 and/or v6) CIDR ranges. By default, no IP
      authorization checks are performed.

      Examples:
        - '' - The empty/default value, no IP-based authorization performed.
        - '************/24,10.0.0.0/8,fd00::/8 - A public IPv4 range, private IPv4 range, and private IPv6 range.
        - '0.0.0.0/0,::/0' - All IPv4 and IPv6 addresses.
    |||,
    name: 'ip_allowlist',
  },

  isSelfServeTeam: false,
  isSelfServeTeam_: {
    owner: 'zhewei',
    date: '2025/04/02',
    description: |||
      If true, the tenant is a self-serve team. This flag is expected to be a temporary solution
      to distinguish self-serve teams from vanguard / discovery tenants.
    |||,
    name: 'is_self_serve_team',
  },


  overrideDataExportTier: '',
  overrideDataExportTier_: {
    owner: 'jacqueline',
    date: '2025/05/27',
    description: |||
      If set, overrides the tenant's tier when deciding what dataset to export their data to.
      For example, if set to COMMUNITY for an ENTERPRISE tenant, the tenant's data will go to the
      nonenterprise support database.
      As of the time of writing, this flag should only be used for dogfood.
    |||,
    name: 'override_data_export_tier',
  },
};

local errors = flags_lib.check_flags(flags);

assert std.length(errors) == 0 : 'Errors: ' + std.toString(errors);

flags
