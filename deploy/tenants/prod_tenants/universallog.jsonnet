{
  name: 'universallog',
  namespace: 'e9',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  domain: 'universallogistics.com',
  tier: 'ENTERPRISE',
  tenantId: 'b126c8284cbf5b2f26405edb3cc71aca',
  allowed_identity_providers: ['waad|universallog'] + standardIdentityProviders,
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true),
}
