{
  name: 'maxar-cmk',
  namespace: 'e5',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  domain: 'maxar.com',
  email_address_domains: ['maxar.com', 'digitalglobe.com', 'mdaus.corp', 'world.ssd.loral.com'],
  encryptionKeyName: 'projects/augment-code/locations/global/keyRings/augmentcode/cryptoKeys/augment-code-key/cryptoKeyVersions/1',
  encryptionKeyTTL: '90m',
  tier: 'ENTERPRISE',
  allowed_identity_providers: ['okta|maxar'],
  tenantId: 'dae9ac3f3609802052ea19961e89628e',
  tenantFlags: tenantFlags
               .override('supportTenant', false),
}
