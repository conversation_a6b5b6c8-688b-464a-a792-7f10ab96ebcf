local devDefaults = import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet';
local tenantsLib = import 'deploy/tenants/tenants_lib.jsonnet';

local tenant = tenantsLib.getTenant('DEV', 'test', 'GCP_US_CENTRAL1_DEV', devDefaults, 'augment');
local flags = tenantsLib.getTenantFlags(tenant, devDefaults);

// These default flags should always be set this way for the "augment" tenant in
// dev deployments
assert flags.supportAccessControl == false;

{}
