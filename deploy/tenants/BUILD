load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
    "jsonnet_to_json",
    "jsonnet_to_json_test",
)
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_binary")

sh_binary(
    name = "generate_prod_tenants",
    srcs = ["generate_prod_tenants.sh"],
    visibility = ["//visibility:private"],
)

genrule(
    name = "more_prod_tenants",
    srcs = glob(["prod_tenants/*.jsonnet"]),
    outs = ["more_prod_tenants.jsonnet"],
    cmd = "$(location :generate_prod_tenants) $(SRCS) > $@",
    tools = [":generate_prod_tenants"],
    visibility = ["//visibility:private"],
)

jsonnet_library(
    name = "more-prod-tenants-lib",
    srcs = [":more_prod_tenants"],
    # Don't rely on getting a complete list of tenants
    # at build time. Since we create tenants from the repository
    # using a target in services/deploy, share it there.
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":standard_identity_providers",
    ],
)

py_binary(
    name = "tenants_util",
    srcs = ["tenants_util.py"],
    data = [
        ":namespaces_json",
        "//deploy/common:cloud_info_json",
    ],
    visibility = [
        "//tools/tenant_manager/processor:__subpackages__",
    ],
    deps = [
        "//base/logging:console_logging",
    ],
)

kubecfg_library(
    name = "tenants-lib",
    srcs = [
        "slack_configs.jsonnet",
        "tenant_flags.jsonnet",
        "tenants.jsonnet",
        "tenants_lib.jsonnet",
    ],
    # One should not rely on getting a complete list of tenants
    # at build time so think carefully before increasing visibility here
    #
    # On this list are services we haven't cleaned up yet.
    visibility = [
        "//deploy:__subpackages__",
        "//services/auth/query/server:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/request_insight/feedback_slackbot:__subpackages__",
    ],
    deps = [
        ":standard_identity_providers",
        "//deploy/tenants/namespace_configs:flags-lib",
    ],
)

kubecfg_library(
    name = "tenants",
    srcs = [
        "tenants.jsonnet",
    ],
    # One should not rely on getting a complete list of tenants
    # at build time so think carefully before increasing visibility here
    visibility = [
        "//visibility:private",
    ],
    deps = [
        ":standard_identity_providers",
        ":tenants-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/tenants/namespace_configs:flags-lib",
    ],
)

kubecfg_library(
    name = "tokens",
    srcs = glob(["tokens/*.jsonnet"]),
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

kubecfg_library(
    name = "tenant_flags",
    srcs = ["tenant_flags.jsonnet"],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_library(
    name = "health-check-token",
    srcs = ["tokens/health-check-allow.jsonnet"],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_library(
    name = "shard_namespaces",
    srcs = [
        "shard_namespaces.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_to_json(
    name = "tenants_json",
    src = "tenants_lib.jsonnet",
    outs = ["tenants.json"],
    visibility = [
        "//tools/notion_tenant_sync:__subpackages__",
    ],
    deps = [
        ":tenants",
    ],
)

jsonnet_to_json_test(
    name = "tenants-test",
    src = "tenants_test.jsonnet",
    deps = [
        ":more-prod-tenants-lib",
        ":namespaces",
        ":shard_namespaces",
        ":tenants",
    ],
)

jsonnet_library(
    name = "namespaces",
    srcs = [
        "namespaces.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":shard_namespaces",
        ":tenants",
    ],
)

jsonnet_to_json(
    name = "namespaces_json",
    src = "namespaces.jsonnet",
    outs = ["namespaces.json"],
    visibility = [
        "//tools/feature_flags:__subpackages__",
    ],
    deps = [
        ":namespaces",
    ],
)

jsonnet_to_json_test(
    name = "test-dev-flags",
    src = "test-dev-flags.jsonnet",
    deps = [
        ":tenants-lib",
        "//deploy/tenants/namespace_configs:namespace-configs",
    ],
)

jsonnet_library(
    name = "standard_identity_providers",
    srcs = ["standard_identity_providers.jsonnet"],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)
