import argparse
import json
import logging
import os
import pathlib
import secrets
import time

from base.logging.console_logging import setup_console_logging

DEFAULT_NAMESPACES = {
    "GCP_US_CENTRAL1_PROD": "e3",
    "GCP_EU_WEST4_PROD": "e0-eu",
}

TENANT_TYPE_TO_TIER_MAPPING = {
    "SUPPORT": "ENTERPRISE",
    "COMMUNITY": "COMMUNITY",
    "PROFESSIONAL": "PROFESSIONAL",
    "PROFESSIONAL-TEAM": "PROFESSIONAL",
    "ENTERPRISE": "ENTERPRISE",
}


def read_namespaces():
    """Read the namespaces.json file and return the contents as a dict."""
    with pathlib.Path("deploy/tenants/namespaces.json").open(encoding="utf-8") as f:
        return json.load(f)


def get_tenant_string(args: argparse.Namespace):
    """Returns a string to specify a new tenant."""
    namespaces = read_namespaces()
    n = [n for n in namespaces["prodNamespaces"] if n["namespace"] == args.namespace]
    if not n:
        raise ValueError(f"Namespace {args.namespace} not found")

    if args.namespace not in DEFAULT_NAMESPACES.values():
        logging.warning("Adding a tenant to a non-default namespace is not recommended")

    tenant_id = hex(secrets.randbits(128))[2:]

    namespace = args.namespace
    cloud = n[0]["cloud"]
    name = args.name
    domain = "null"
    support_tenant = "false"

    if args.tenant_type == "SUPPORT":
        support_tenant = "true"

    tier = TENANT_TYPE_TO_TIER_MAPPING.get(args.tenant_type, args.tenant_type)
    tier_str = f"    tier: '{tier}',\n"

    if args.domain:
        domain = f"'{args.domain}'"

    # Build the tenant flags override
    tenant_flags_override = f"""tenantFlags
                 .override('supportTenant', {support_tenant})"""

    # Add isSelfServeTeam override if needed (only for professional-team)
    if args.tenant_type == "PROFESSIONAL-TEAM":
        tenant_flags_override += "\n                 .override('isSelfServeTeam', true)"

    extra_data = f"""{{
    name: '{name}',
    namespace: '{namespace}',
    env: 'PROD',
    cloud: '{cloud}',
    domain: {domain},
{tier_str}    tenantId: '{tenant_id}',
    tenantFlags: {tenant_flags_override},
}}"""
    return extra_data


def add_tenant(args: argparse.Namespace):
    """Add a new tenant to the tenants.jsonnet file."""
    tenants_jsonnet_data = pathlib.Path(
        os.environ["BUILD_WORKSPACE_DIRECTORY"], "deploy/tenants/tenants.jsonnet"
    ).read_text(encoding="utf-8")

    extra_data = get_tenant_string(args) + ",\n" + "  // ADD PROD TENANT HERE\n"
    tenants_jsonnet_data = tenants_jsonnet_data.replace(
        "// ADD PROD TENANT HERE\n", extra_data
    )

    logging.info(
        "Writing tenants.jsonnet with tenant %s (%s)", args.name, args.namespace
    )
    pathlib.Path(
        os.environ["BUILD_WORKSPACE_DIRECTORY"], "deploy/tenants/tenants.jsonnet"
    ).write_text(tenants_jsonnet_data, encoding="utf-8")


def delete_tenant(args: argparse.Namespace):
    """Delete a tenant frrom the tenants.jsonnet file."""

    tenants_jsonnet_data = pathlib.Path(
        os.environ["BUILD_WORKSPACE_DIRECTORY"], "deploy/tenants/tenants.jsonnet"
    ).read_text(encoding="utf-8")
    now = time.strftime("%Y-%m-%dT%H:%M:%SZ")
    current = f"name: '{args.name}',"
    assert current in tenants_jsonnet_data, f"Tenant {args.name} not found"
    extra_data = f"""name: '{args.name}',
    deleted_at: '{now}',"""
    tenants_jsonnet_data = tenants_jsonnet_data.replace(current, extra_data)

    pathlib.Path(
        os.environ["BUILD_WORKSPACE_DIRECTORY"], "deploy/tenants/tenants.jsonnet"
    ).write_text(tenants_jsonnet_data, encoding="utf-8")


def main():
    """Main entry function when used as a binary."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.set_defaults(action=None)

    subparsers = parser.add_subparsers(dest="action")

    add_tenant_parser = subparsers.add_parser("add", help="Add a new tenant")
    add_tenant_parser.set_defaults(action=add_tenant)
    add_tenant_parser.add_argument("--name", help="Tenant name", required=True)
    add_tenant_parser.add_argument("--domain", help="Tenant domain")
    add_tenant_parser.add_argument(
        "--namespace", help="Tenant namespace", required=True
    )
    add_tenant_parser.add_argument(
        "--tenant-type",
        help="Tenant type: { SUPPORT, ENTERPRISE, PROFESSIONAL, PROFESSIONAL-TEAM, COMMUNITY }",
        choices=[
            "SUPPORT",
            "ENTERPRISE",
            "PROFESSIONAL",
            "PROFESSIONAL-TEAM",
            "COMMUNITY",
        ],
        required=True,
    )

    delete_tenant_parser = subparsers.add_parser("delete", help="Delete a tenant")
    delete_tenant_parser.set_defaults(action=delete_tenant)
    delete_tenant_parser.add_argument("--name", help="Tenant name", required=True)

    args = parser.parse_args()

    if args.action:
        args.action(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
