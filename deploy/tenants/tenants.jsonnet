// List of our tenants. All of our real customers go in `prodTenants`. Having a list of staging
// tenants just makes our metadata files cleaner.
// To add a new tenant you must also add their namespace configuration (in the namespace_configs
// directory).
// See https://www.notion.so/Runbook-How-to-configure-a-new-tenant-da6a5866570341caa8b1d154877ef9d1
local slackConfigs = import 'deploy/tenants/slack_configs.jsonnet';
local tenantFlags = import 'deploy/tenants/tenant_flags.jsonnet';
local standardIdentityProviders = (import 'deploy/tenants/standard_identity_providers.jsonnet').list;

local stagingTenants = [
  // tenant in staging that is configured as closely as possible to the production tenant.
  {
    namespace: 'pre-prod',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'pre-prod.augmentcode.com',
    name: 'pre-prod',
    tier: 'ENTERPRISE',
    tenantId: 'e5f000abef68aca453a6c8cdcfc18f11',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  // support tenant for staging-shard-0
  {
    name: 'staging-shard-0',
    tier: 'ENTERPRISE',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'staging-shard-0',
    tenantId: '07e5a0d66265b8624c14b819fb3598c0',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'dogfood-shard',
    tier: 'ENTERPRISE',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'augmentcode.com',
    namespace: 'staging-shard-0',
    tenantId: '352a91ac7d4283558ccfbc094a527746',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false)
                 .override('overrideDataExportTier', 'COMMUNITY'),  // We don't want access control on dogfood data
  },
  {
    name: 'staging-discovery0',
    tier: 'PROFESSIONAL',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'staging-shard-0',
    tenantId: '7c287a5142174fed85794fb2fe2b44fc',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'staging-vanguard0',
    tier: 'COMMUNITY',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'staging-shard-0',
    tenantId: '7f16c44dcaf340c9b67d08e9a2f00a97',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },

  {
    name: 'pre-prod-shard',
    tier: 'ENTERPRISE',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '62a2af31a19ae67e4db9ef6e9b31a0a7',
    namespace: 'staging-shard-0',
    tenantFlags: tenantFlags,
  },
  {
    name: 'pre-prod-eu-w4',
    tier: 'ENTERPRISE',
    env: 'STAGING',
    cloud: 'GCP_EU_WEST4_PROD',
    tenantId: '7083136f50124304ee0f3b8e56e50b12',
    namespace: 'eu-staging-0',
    tenantFlags: tenantFlags,
  },
  // support tenant for eu-staging-0
  {
    name: 'eu-staging-0',
    tier: 'ENTERPRISE',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '0149365baeba274d667da50735b76eb8',
    namespace: 'eu-staging-0',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // ADD STAGING TENANT HERE
];
// Please keep tenant names sorted alphabetically.
// Tenant names should be sort and the 10 char prefix needs to be unique
local prodTenants = [
  {
    // customer 2024-09-26
    namespace: 'advisor360',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'advisor360',
    tier: 'ENTERPRISE',
    domain: 'advisor360.com',
    tenantId: '55d5fe7f2732dffe89de6bcd41473143',
  },
  {
    namespace: 'aitutor-mercor',
    env: 'PROD',
    name: 'aitutor-mercor',
    tier: 'ENTERPRISE',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
    tenantId: 'd33d1ede5c91a1bc179f480caffb1470',
  },
  {
    namespace: 'aitutor-turing',
    env: 'PROD',
    name: 'aitutor-turing',
    tier: 'ENTERPRISE',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '82951ccf8d0daaae79bda4489968b8fa',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    // customer 2024-09-06
    namespace: 'ampsortation',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'ampsortation',
    tier: 'ENTERPRISE',
    domain: 'amprobotics.com',
    tenantId: 'fb53074bd39099de99ab6c39288491e9',
  },
  {
    namespace: 'augmentdemo',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'augmentdemo',
    tier: 'ENTERPRISE',
    tenantId: '13bb4248bc34d6aeecd3108941b5bfe2',
  },
  {
    name: 'bighatbio',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:01:48Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'bighatbio.com',
    tenantId: '7b90af30926b1fdc18a6fe188abd15b4',
    tenantFlags: tenantFlags,
  },
  {
    name: 'bitwarden',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:02:52Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'bitwarden.com',
    tenantId: '5fc0e55bcc3052265c13dfb144e30f98',
    tenantFlags: tenantFlags,
  },
  {
    name: 'chaidiscovery',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'chaidiscovery.com',
    tenantId: '4c977b75797cf88f5e7f13ec0e40a0ef',
    tenantFlags: tenantFlags,
  },
  {
    name: 'chainalysis',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    deleted_at: '2025-01-24T17:10:35Z',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'chainalysis.com',
    tenantId: 'b249e12009f0a7fb729a636804b142c8',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-09-24
    name: 'coalitioninc',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'coalitioninc.com',
    allowed_identity_providers: ['okta|coalitioninc'],
    tenantId: '6f0c0fa92f84d48a71673bc5489afe5e',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-08-02
    namespace: 'collective',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'collective',
    tier: 'ENTERPRISE',
    domain: 'collective.com',
    tenantId: '2007884d9c561a34d2f150806c3cb3d8',
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'collectors',
    tier: 'ENTERPRISE',
    domain: 'collectors.com',
    allowed_identity_providers: standardIdentityProviders + ['okta|collectors'],
    tenantId: '97f6df14d49ce1a09a78d3fca6a4dbed',
    tenantFlags: tenantFlags
                 .override('slackbotAllowlistChannels', slackConfigs.collectors.allowlistChannels),
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'docana',
    tier: 'ENTERPRISE',
    domain: 'docana.com',
    tenantId: '4a8d6cf8a83d13fb9da13a471d6524d9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'dropthought',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:04:41Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'dropthought.com',
    tenantId: '76e8965e33967c3f882b81d8d152ccd2',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'eikon',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'eikon',
    tier: 'ENTERPRISE',
    domain: 'eikontx.com',
    tenantId: 'c88b306aa60a44a902cd3b8513878f0c',
  },
  {
    name: 'eliolabs',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:05:22Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'eliolabs.com',
    tenantId: '5b018b73e483891f7fe510cc3a350f8c',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'enfabrica',
    tier: 'ENTERPRISE',
    domain: 'enfabrica.net',
    tenantId: 'fc05abbd51156ca07640ec1c652cbf66',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'flume',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'flume',
    tier: 'ENTERPRISE',
    domain: 'tryflume.ai',
    tenantId: '0c5efb3d564558d8e7564b1266e14327',
  },
  {
    namespace: 'e1',
    deleted_at: '2025-01-23T21:52:30Z',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'gitkraken',
    tier: 'ENTERPRISE',
    domain: 'gitkraken.com',
    tenantId: '32996e4a081aaf222fae13b874567ca6',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'gladly',
    tier: 'ENTERPRISE',
    domain: 'gladly.com',
    tenantId: '5a37717009d42efe56de66f14ce0a2b5',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'gofundme',
    tier: 'ENTERPRISE',
    domain: 'gofundme.com',
    allowed_identity_providers: ['okta|gofundme'],
    email_address_domains: ['gofundme.com', 'classy.org'],
    tenantId: '42a206619e5438aa9da464a95a218f54',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-09-16
    namespace: 'gopigment',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    name: 'gopigment',
    tier: 'ENTERPRISE',
    domain: 'pigment.com',
    username_domains: ['pigment.com'],
    email_address_domains: ['pigment.com'],
    tenantId: 'f68ef98cfb52f24d6b7fc8f47d0882a6',
  },
  {
    name: 'goplayfully',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:06:20Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'goplayfully.com',
    tenantId: '0dc61fe81afec0af09ca0b35fa4d55b1',
    tenantFlags: tenantFlags,
  },
  {
    name: 'hextech-us',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:06:55Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'hex.tech',
    tenantId: '0624a964d27933fb81e87dcee8850ec6',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'humaninterest',
    tier: 'ENTERPRISE',
    domain: 'humaninterest.com',
    email_address_domains: ['humaninterest.com', 'humaninterestadvisors.com'],
    allowed_identity_providers: standardIdentityProviders + ['okta|humaninterest'],
    tenantId: '24919d1b367b80b93cca0734418377c5',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'lemonade',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'lemonade',
    tier: 'ENTERPRISE',
    domain: 'lemonade.com',
    tenantId: '529bbad85c0d4938d4bb6608a06fce8c',
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'lettuce',
    tier: 'ENTERPRISE',
    domain: 'lettuce.co',
    tenantId: 'b27057913b49531e6a5f4ed60b28bb0d',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-09-05
    namespace: 'lmnt',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'lmnt',
    tier: 'ENTERPRISE',
    domain: 'lmnt.com',
    tenantId: 'fe18dc22881279f77f706116e56eb094',
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'lyrahealth',
    tier: 'ENTERPRISE',
    domain: 'lyrahealth.com',
    tenantId: '5b10553482a0ab3dd653f0cf9ed8101e',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-09-10
    namespace: 'meinauto',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    name: 'meinauto',
    tier: 'ENTERPRISE',
    domain: 'meinauto.de',
    tenantId: 'b6a171f3f3cb78d2d3545e10e667e4bb',
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'meltwater',
    tier: 'ENTERPRISE',
    deleted_at: '2024-12-11T16:43:09Z',
    domain: 'meltwater.com',
    tenantId: '1132fc0adaea2fb6dc8a6bafe34f8920',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'mirdin',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-13T19:23:06Z',
    domain: 'mirdin.com',
    tenantId: 'd24c5f1959a656ed3070d0d60fc8b91e',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-09-25
    namespace: 'montecarlodata',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'montecarlodata',
    tier: 'ENTERPRISE',
    domain: 'montecarlodata.com',
    allowed_identity_providers: ['oidc|montecarlodata', 'okta|montecarlodata-okta'],
    tenantId: '29cccd6b4d30c49112e3d24e7dfbc73f',
  },
  {
    // customer 2024-08-29
    namespace: 'newfront',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'newfront',
    tier: 'ENTERPRISE',
    domain: 'newfront.com',
    tenantId: '8a087138ec094ec3bfeaa23e5b99a012',
  },
  {
    namespace: 'nucleus',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'nucleus',
    tier: 'ENTERPRISE',
    domain: 'nucleussec.com',
    tenantId: '9bdd02c63a337517087cacdeaf1ee2df',
  },
  {
    namespace: 'observeinc',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'observeinc.com',
    name: 'observeinc',
    tier: 'ENTERPRISE',
    tenantId: '5ff8908862cb4d48ebf574698c04d3c2',
  },
  {
    // customer 2024-08-30
    namespace: 'onebuild',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'onebuild',
    tier: 'ENTERPRISE',
    domain: '1build.com',
    email_address_domains: ['1build.com', 'handoff.ai'],
    tenantId: '47d6c742b2c84d81fbb70b0abb710c0e',
  },
  {
    // customer 2024-08-16
    namespace: 'onelineage',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'onelineage',
    tier: 'ENTERPRISE',
    domain: 'lineagelogistics.com',
    username_domains: ['onelineage.com'],
    email_address_domains: ['onelineage.com'],
    allowed_identity_providers: standardIdentityProviders + ['okta|lineage-logistics'],
    tenantId: '8759160c8dc633250ec7f6dd6ba1bfb4',
  },
  {
    // customer 2024-09-20
    namespace: 'paystone',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'paystone',
    tier: 'ENTERPRISE',
    domain: 'paystone.com',
    tenantId: 'a5cb481743d2564b53f9bd67462b3205',
  },
  {
    // customer 2024-07-03
    namespace: 'pocketlaw',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'pocketlaw',
    tier: 'ENTERPRISE',
    domain: 'pocketlaw.com',
    tenantId: '7f92d7a33b8647f9d7e06502b1f3da7c',
  },
  {
    // customer 2024-08-20
    namespace: 'pollyex',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'pollyex',
    tier: 'ENTERPRISE',
    domain: 'pollyex.com',
    tenantId: '092b94fa94826114faba401cc52b8a9e',
  },
  {
    // customer 2024-09-30
    namespace: 'profiq',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    name: 'profiq',
    tier: 'ENTERPRISE',
    domain: 'profiq.com',
    tenantId: 'ee2e5dc3a2c5be800e27c848420c5ea5',
  },
  {
    // customer 2024-09-12
    namespace: 'purestorage',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'purestorage.com',
    name: 'purestorage',
    allowed_identity_providers: standardIdentityProviders + ['okta|purestorage'],
    tier: 'ENTERPRISE',
    tenantId: '755fc754e141a27626402278d204c37d',
  },
  {
    namespace: 'realtor',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'realtor',
    tier: 'ENTERPRISE',
    username_domains: ['corp.homestore.net'],
    email_address_domains: ['realtor.com', 'move.com'],
    allowed_identity_providers: ['okta|newscorp'],
    domain: 'realtor.com',
    tenantId: 'f5d06e4139c1efa6f6eaea37429e2035',
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'replicahq',
    tier: 'ENTERPRISE',
    domain: 'replicahq.com',
    tenantId: 'dcac8dc8b224b64fa859db01d0767042',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-09-09
    namespace: 'reveart',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'reveart',
    tier: 'ENTERPRISE',
    domain: 'reve.art',
    tenantId: '95bb2384ac77fbb4d9b805478576cb49',
  },
  {
    // customer 2024-08-09
    namespace: 'roboto',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'roboto',
    tier: 'ENTERPRISE',
    domain: 'roboto.ai',
    tenantId: 'a8020ed715e8150041b82f61f056c2f5',
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'runway',
    tier: 'ENTERPRISE',
    deleted_at: '2024-12-17T00:06:14Z',
    domain: 'runway.com',
    tenantId: 'e73fd756deb03f77f9603de1d70a5ce7',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'samaya',
    tier: 'ENTERPRISE',
    domain: 'samaya.ai',
    tenantId: '08592857a4d42ed5fa670a6daf323c4f',
  },
  {
    name: 'sanity',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:07:47Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'sanity.io',
    tenantId: '87c04520680106ece8e9a8db7f593550',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'schoolstatus',
    tier: 'ENTERPRISE',
    domain: 'schoolstatus.com',
    tenantId: '59f971d54bb09511a97a5b3154cc0e96',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'sifive',
    tier: 'ENTERPRISE',
    domain: 'sifive.com',
    allowed_identity_providers: standardIdentityProviders + ['okta|sifive'],
    tenantId: 'b64d2734d74bffad8b9f36e155d0029f',
    tenantFlags: tenantFlags,
  },
  {
    name: 'sigma',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'sigmacomputing.com',
    tenantId: '5add39e379a24c054ccd3bb88cc46e27',
    tenantFlags: tenantFlags,
  },
  {
    name: 'skylo',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    deleted_at: '2025-01-24T17:09:58Z',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'skylo.tech',
    tenantId: 'f871bcd33faef46a659ff0a602d1d30e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'squidcloud',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:09:28Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'squid.cloud',
    tenantId: 'b7316b06540d5631b848db803d2e7682',
    tenantFlags: tenantFlags,
  },
  {
    name: 'tangiblemat',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:10:14Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'tangiblematerials.com',
    tenantId: 'e9bc2e1df483410ebbb17a9bdfc83868',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'tangotango',
    tier: 'ENTERPRISE',
    domain: 'tangoptt.com',
    tenantId: '9f95430affbe07a8a6e9c6a71f28a1a2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'taro',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:10:45Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'jointaro.com',
    tenantId: '418314e2feef6da94302f6ea6b0a74fb',
    tenantFlags: tenantFlags,
  },
  {
    name: 'thirdwave',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:11:23Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'thirdwave.ai',
    tenantId: '5fbf919a1d8bc4de47fd56d424c86a62',
    tenantFlags: tenantFlags,
  },
  {
    name: 'tifo',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:11:54Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'jointifo.com',
    tenantId: '8cb00837c0fc848a14fb691b0e26de5a',
    tenantFlags: tenantFlags,
  },
  {
    namespace: 'viaduct-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    name: 'viaduct-eu',
    tier: 'ENTERPRISE',
    domain: 'viaduct.ai',
    tenantId: 'b1b9e71298c4638db8e917bc7c25e818',
  },
  {
    // customer 2024-09-26
    namespace: 'webflow',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    name: 'webflow',
    tier: 'ENTERPRISE',
    allowed_identity_providers: standardIdentityProviders + ['okta|webflow'],
    domain: 'webflow.com',
    tenantId: '18516b80eea3ea48d43ee8b7995b3b95',
  },
  // support tenant for e0
  {
    name: 'e0',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e0',
    tenantId: '8b8f7dd4cdfed5be753be431cd028c62',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e1
  {
    name: 'e1',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e1',
    tenantId: 'bb1e393d0a053ff45b22e01569ee11a1',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e2
  {
    name: 'e2',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e2',
    tenantId: '53387f53c352784476998a71ad07bfc7',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e3
  {
    name: 'e3',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e3',
    tenantId: 'f59f8d15d5cf0d28409f79d2e5ff71aa',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e4
  {
    name: 'e4',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e4',
    tenantId: '43f1956a21307a3cf4551a13713315d9',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e5
  {
    name: 'e5',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e5',
    tenantId: '418b3798f0aab6ead304aa5993c45337',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e6
  {
    name: 'e6',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e6',
    tenantId: '6ff69fb4bf435cae93450a88bd5147af',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e7
  {
    name: 'e7',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e7',
    tenantId: 'af01c1de1b0ca627c8a3342bcc5d707f',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e8
  {
    name: 'e8',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e8',
    tenantId: 'c984e10abc099fe7bb8a1555c27d899a',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e9
  {
    name: 'e9',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e9',
    tenantId: 'd8cafb46b8654fbed37f9706835c2051',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e10
  {
    name: 'e10',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e10',
    tenantId: 'e92f9a181bac980e9ba8204dfc72a170',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e11
  {
    name: 'e11',
    tier: 'ENTERPRISE',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'e11',
    tenantId: 'd1a70e2224606e6fe798584d36e009cc',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e0-eu
  {
    name: 'e0-eu',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: null,
    tenantId: '9ae1e477d7ffeabcb982ecb8bb3c0e3a',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e1-eu
  {
    name: 'e1-eu',
    tier: 'ENTERPRISE',
    namespace: 'e1-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: null,
    tenantId: '16ea7c7690159af0a7d26cd001e2f289',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for e2-eu
  {
    name: 'e2-eu',
    tier: 'ENTERPRISE',
    namespace: 'e2-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: null,
    tenantId: '1930d5430c08c2bba45ad3448849ff13',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // support tenant for i0
  {
    name: 'i0',
    tier: 'COMMUNITY',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'i0',
    tenantId: '5bab833f44667018de0df9fab783ebfd',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-0',
    tier: 'COMMUNITY',
    deleted_at: '2024-12-03T19:30:45Z',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '51b96ec9cd1b02a7c48e30f8503092be',
    tenantFlags: tenantFlags,
  },
  // put vanguard (individual users) into this tenant
  {
    name: 'i0-vanguard0',
    tier: 'COMMUNITY',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '789b1a18a6970fc4de4cf3aa89a35827',
    tenantFlags: tenantFlags
                 // at this point, we are guaranteed that all users opt-in
                 // into data sharing and we can disable support access control
                 // for content and requests
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-vanguard1',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tier: 'COMMUNITY',
    tenantId: 'ce26583a2f0a0d8130e698bb1f651f27',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-vanguard2',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tier: 'COMMUNITY',
    tenantId: '74fee6f8b118d38476247c3206dc9c31',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-vanguard3',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tier: 'COMMUNITY',
    tenantId: '51fefac672e825104ea261d63418d070',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-vanguard4',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tier: 'COMMUNITY',
    tenantId: 'e2dc5f8565810210d35d64826ba22120',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-vanguard5',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tier: 'COMMUNITY',
    tenantId: 'a2a834210447eb987d1eff9b11384ed',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-vanguard6',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tier: 'COMMUNITY',
    tenantId: 'b2e745e73d8d01ccda38498d1a4de5e7',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i0-vanguard7',
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tier: 'COMMUNITY',
    tenantId: 'a94be078d83a082a8b1b30ecc12dc173',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  // support tenant for i1 (second vanguard shard)
  {
    name: 'i1',
    tier: 'COMMUNITY',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'b8b03504ec057103c40c07fd2cc4b918',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard0',
    tier: 'COMMUNITY',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'eac8863de00e749b73eae23300f69e92',
    tenantFlags: tenantFlags
                 // at this point, we are guaranteed that all users opt-in
                 // into data sharing and we can disable support access control
                 // for content and requests
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard1',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'COMMUNITY',
    tenantId: '7521771141a76e2a9e686a3d89390fb5',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard2',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'COMMUNITY',
    tenantId: 'ba2cda8f56ce724d5b2f32a1641471e1',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard3',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'COMMUNITY',
    tenantId: '49f0bdc475cce55ac6e802ad69ed19a5',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard4',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'COMMUNITY',
    tenantId: 'a71d9d7889482b73100a45c8ce3953c6',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard5',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'COMMUNITY',
    tenantId: '2cf7cd507edd34c1c05a954544474084',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard6',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'COMMUNITY',
    tenantId: '4900799c06e1299aed1e2d0707417f15',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'i1-vanguard7',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'COMMUNITY',
    tenantId: 'c0041ab726a7096a56a21d7009419600',
    tenantFlags: tenantFlags
                 .override('supportAccessControl', false),
  },
  {
    name: 'jotai',
    tier: 'ENTERPRISE',
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'f71d27a356d306effe40005d7450d968',
    tenantFlags: tenantFlags
                 // at this point, we are guaranteed that all users opt-in
                 // into data sharing and we can disable support access control
                 // for content and requests
                 .override('supportAccessControl', false),
  },
  {
    name: 'able',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:12:34Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'able.co',
    tenantId: '1318825a197a3d3b43394d81d0f12aaf',
    tenantFlags: tenantFlags,
  },
  {
    name: 'accenture',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'accenture.com',
    tenantId: 'e74471c81eae1140b1c7a03ed35cf75a',
    tenantFlags: tenantFlags,
  },
  {
    // customer 2024-09-17
    name: 'accountants',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'accountantsacademy.be',
    tenantId: 'b54526b0e462f1fe746e3fdbbcd21b84',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ackee',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:13:04Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'ackee.cz',
    tenantId: '0d1394140c819da8735dda714c75f4a9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'adastra',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:08:03Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'adastracorp.com',
    tenantId: '7f9a0b86e7c0308c3b2d0497b591ad0d',
    tenantFlags: tenantFlags,
    auth_central_ignore: true,
  },
  {
    name: 'adastra-eu',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:13:46Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'adastracorp.com',
    tenantId: '3e5caa7d56a5154c6b43079d96c25331',
    tenantFlags: tenantFlags,
  },
  {
    name: 'aeroflowinc',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'aeroflowinc.com',
    tenantId: 'e88bf5c6775c4de8ab36bda92f65dd01',
    tenantFlags: tenantFlags,
  },
  {
    name: 'afresh',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'afreshtechnologies.com',
    tenantId: '9f7ab0d2b2be2a6127bd6239660feb47',
    tenantFlags: tenantFlags,
  },
  {
    name: 'aircon',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:08:38Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'airconai.com',
    tenantId: 'e68d7177cd1a8f3ba0e4c3b2c5f21dc2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'alida',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'alida.com',
    tenantId: '13f6fcc0fc394f84b2237af2122192e2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'amazon',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'amazon.com',
    tenantId: '8a14c277c68220d2c7775a3ac04d5d61',
    tenantFlags: tenantFlags,
  },
  {
    name: 'amplitude',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'amplitude.com',
    deleted_at: '2025-06-13T03:17:58Z',
    tenantId: '3f7f5d70d8d44a89a2a9f0e94a6f23b1',
    allowed_identity_providers: ['okta|amplitude'],
    tenantFlags: tenantFlags,
  },
  {
    name: 'antler',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'antler.co',
    tenantId: '392172296613ab587bad84e213ab54eb',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ateam',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:22:56Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'a.team',
    tenantId: '3cf2895651f00f8ff04b20565c3cb245',
    tenantFlags: tenantFlags,
  },
  {
    name: 'atomikos',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'atomikos.com',
    tenantId: '33a3d09fc10faa899fe634d2e8394741',
    tenantFlags: tenantFlags,
  },
  {
    name: 'atommap',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'atommap.com',
    tenantId: 'bdec2db5d50f3e0a7b7c1389dd4f2e13',
    tenantFlags: tenantFlags,
  },
  {
    name: 'atmosphere',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'atmosphere.tv',
    tenantId: 'bf1640fbc19791a0622c2bdde54dbf0b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'athena',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'athenaintelligence.ai',
    tenantId: '2310fd58ebaa7e21c6d60a33c1a9559d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'atomcomputing',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:14:29Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'atom-computing.com',
    tenantId: 'c8cc0027f9b2a6f162920228467ec4ea',
    tenantFlags: tenantFlags,
  },
  {
    name: 'augmentmarket',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'augment.market',
    tenantId: 'ea8f0441227cd09c6ea1af58e5bf5ab3',
    tenantFlags: tenantFlags,
  },
  {
    name: 'avalara',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'avalara.com',
    tenantId: '6480b922c77c48f0b9e0f739a40d52b6',
    tenantFlags: tenantFlags,
  },
  {
    name: 'banyaninfra',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'banyaninfrastructure.com',
    tenantId: 'b8cb2efabd5df4fd4251e949b4b9c02d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'barrage',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'barrage.net',
    tenantId: 'c25a5f7240168f9095c0f79e2964be91',
    tenantFlags: tenantFlags,
  },
  {
    name: 'baseten',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-24T17:11:27Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'baseten.co',
    tenantId: '9af686a68a0bb2c5640427eec788646',
    tenantFlags: tenantFlags,
  },
  {
    name: 'bcny',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:08:40Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'thebrowser.company',
    tenantId: '9a351ed261f4b1d92ca94dc9edcf082',
    tenantFlags: tenantFlags,
  },
  {
    name: 'betterup',
    tier: 'ENTERPRISE',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'betterup.com',
    tenantId: 'd1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6',
    tenantFlags: tenantFlags,
  },
  {
    name: 'beyondidentity',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:11Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'beyondidentity.com',
    tenantId: '1a5770926bc2223e4929e0f37ac3a405',
    tenantFlags: tenantFlags,
  },
  {
    name: 'bigcartel',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'bigcartel.com',
    tenantId: 'c4b7d9f80e1a4f6b9e2c3a5d8f7e6c1a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'bigcommerce',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'bigcommerce.com',
    tenantId: 'c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'bigpanda',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:17Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'bigpanda.io',
    tenantId: 'c8fcbdf57b2afd269d0aa025a52d0ff8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'blend360',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'blend360.com',
    tenantId: '00111344a3735b526d455e07f7ff9a8c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'boeing',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'boeing.com',
    tenantId: '6837caccf2e06ed46e2109df89fd1a7c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'bonfy',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'bonfy.ai',
    tenantId: 'a66876ea05c096092764d82c15ff48ee',
    tenantFlags: tenantFlags,
  },
  {
    name: 'brainvoy',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'brainvoy.com',
    email_address_domains: ['brainvoy.com'],
    tenantId: 'cf5eb3d4dcc0e646ce6cf941410aa678',
    tenantFlags: tenantFlags,
  },
  {
    name: 'brex',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'brex.com',
    tenantId: 'f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'cache',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:16:00Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'usecache.com',
    tenantId: 'c48da487270f6269a326506a9c52b41c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'campus',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'campus.edu',
    tenantId: '96734c1da1ece01b94e1e104d9a4c5b5',
    tenantFlags: tenantFlags,
  },
  {
    name: 'capsule',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'capsule.video',
    tenantId: 'd7299aedc580fd1f57f9424a5680667f',
    tenantFlags: tenantFlags,
  },
  {
    name: 'carta',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'carta.com',
    tenantId: '5fa86d75c9abb09867a6030437b5e95e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'cevalogistics',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:16:57Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'cevalogistics.com',
    tenantId: '492a6fdb4de0b0d6abe371863ce7e515',
    tenantFlags: tenantFlags,
  },
  {
    name: 'chegg',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'chegg.com',
    tenantId: 'f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0',
    tenantFlags: tenantFlags,
  },
  {
    name: 'cisco',
    tier: 'ENTERPRISE',
    namespace: 'xld',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'cisco.com',
    allowed_identity_providers: standardIdentityProviders + ['oidc|cisco'],
    tenantId: 'b14a02ec6d2f2e8229f85595adecbc25',
    tenantFlags: tenantFlags,
  },
  {
    name: 'cityplumbing',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:17:16Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'cityplumbing.co.uk',
    tenantId: '********************************',
    tenantFlags: tenantFlags,
  },
  {
    name: 'clearme',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'clearme.com',
    allowed_identity_providers: ['okta|clearme'],
    tenantId: '92a5ebdb1f037cc5d9cb84ec8771e00a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'cloudchef',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-24T17:12:10Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'cloudchef.co',
    tenantId: 'eba210187bcb0fc9251f42c66736a825',
    tenantFlags: tenantFlags,
  },
  {
    name: 'clutch',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'clutch.co',
    tenantId: 'd37062546c9e18c9254620e3b1d6f662',
    tenantFlags: tenantFlags,
  },
  {
    name: 'codem',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'codem.com',
    tenantId: '828c18b6dbb2075a380bb94c50fbedc1',
    tenantFlags: tenantFlags,
  },
  {
    name: 'cognism',
    tier: 'ENTERPRISE',
    deleted_at: '2025-03-18T09:18:46Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'cognism.com',
    tenantId: '111e270b1be47c178a24783de2366a7a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'comarch',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'comarch.com',
    tenantId: 'ccb031306413c3ba4738e66421e35866',
    tenantFlags: tenantFlags,
  },
  {
    name: 'commit',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:18:12Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'comm-it.com',
    tenantId: '3b0badd771de072200b0847447e459bf',
    tenantFlags: tenantFlags,
  },
  {
    name: 'compliance',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:18:46Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'compliancesolutions.com',
    tenantId: 'a9c0085b830cd56e9fce6a122ac7e905',
    tenantFlags: tenantFlags,
  },
  {
    name: 'coolplanet',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'coolplanet.io',
    tenantId: '2743b88a8b244741ba05e531e4a64f38',
    tenantFlags: tenantFlags,
  },
  {
    name: 'creativedock',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:19:15Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'creativedock.com',
    tenantId: '99befa501d848d32f4f3d8fe06db133c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'cribl',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'cribl.io',
    tenantId: 'a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6',
    tenantFlags: tenantFlags,
  },
  {
    name: 'crimzo',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:19:46Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'crimzo.com',
    tenantId: '2e8bda01e4f426c32c0009a832f54eff',
    tenantFlags: tenantFlags,
  },
  {
    name: 'curietech',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:20:25Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'curietech.net',
    tenantId: '8e0274f80fe7f349dadb056d69d31e2c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'datastax',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'datastax.com',
    tenantId: '8fffd258ea8660d6a62586d294ce4143',
    tenantFlags: tenantFlags,
  },
  {
    name: 'daytona',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:23:49Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'daytona.io',
    tenantId: 'e565225b1c12c5d6b0d2ff5cac3e6a19',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ddn',
    tier: 'ENTERPRISE',
    namespace: 'e2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'ddn.com',
    username_domains: [],
    email_address_domains: ['ddn.com', 'tintri.com', 'whamcloud.com'],
    tenantId: 'b484699b72ee1b1a5171c26784ece82a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'detroitsoftware',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'detroitsoftware.com',
    tenantId: '0b26dbedcbcf6e42fd9e64e7d04c9438',
    tenantFlags: tenantFlags,
  },
  {
    name: 'dfjs',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'dfjs.co.uk',
    tenantId: 'ad2649c6ba5b44f9ab26bb4f7792f252',
    tenantFlags: tenantFlags,
  },
  {
    name: 'discord',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'discord.com',
    tenantId: '78c3d60818e23a1054a8316c6564aeda',
    tenantFlags: tenantFlags,
  },
  {
    name: 'divelement',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'divelement.io',
    tenantId: '6c2eedb3219d9b9c5444eb5ddecc2ccc',
    tenantFlags: tenantFlags,
  },
  {
    name: 'docker',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'docker.com',
    tenantId: 'e7d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'doctoranywhere',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'doctoranywhere.com',
    tenantId: 'ed63eb20872b57064e851d24a358b866',
    tenantFlags: tenantFlags,
  },
  {
    name: 'dotdashmdp',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'dotdashmdp.com',
    tenantId: '08fc016b3a0a7dd3932cdf22d8766661',
    tenantFlags: tenantFlags,
  },
  {
    name: 'drata',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'drata.com',
    allowed_identity_providers: ['okta|drata', 'okta|drata-safebase'],
    email_address_domains: ['drata.com', 'safebase.io'],
    tenantId: '36087c1c95002896453ec18fb3b94b25',
    tenantFlags: tenantFlags,
  },
  {
    name: 'dreamgames',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'dreamgames.com',
    tenantId: 'fa3263d25d433b4cf96ced8ae6a5031c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'dropbox',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'dropbox.com',
    tenantId: '********************************',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ebrd',
    tier: 'ENTERPRISE',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'ebrd.com',
    tenantId: '2ff7fb71e067461abc806227ba980d00',
    tenantFlags: tenantFlags,
  },
  {
    name: 'eightfold',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'eightfold.ai',
    tenantId: 'a2c6972289033c774b67c3c5529eff52',
    tenantFlags: tenantFlags,
  },
  {
    name: 'epam',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'epam.com',
    tenantId: 'a0b5e1fd3c9b4740865683b0540608ef',
    tenantFlags: tenantFlags,
  },
  {
    name: 'etsy',
    tier: 'ENTERPRISE',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'etsy.com',
    tenantId: 'a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5',
    tenantFlags: tenantFlags,
  },
  {
    name: 'faire',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'faire.com',
    tenantId: 'b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0',
    tenantFlags: tenantFlags,
  },
  {
    name: 'farmart',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'farmart.co',
    tenantId: '0f5f94d4d4776b39a6decf3c8943e69b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ferryhealth',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'ferry.health',
    tenantId: 'afea0068af708828689976b544903e9e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'filevine',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'filevine.com',
    tenantId: '62c7a2f38c2d2ace0b4b68af29ba4440',
    tenantFlags: tenantFlags,
  },
  {
    name: 'flyr',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'flyr.com',
    tenantId: 'b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'fmad',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'fmad.io',
    tenantId: '4926318ea69444428daee6d70f316974',
    tenantFlags: tenantFlags,
  },
  {
    name: 'fmglobal',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'fmglobal.com',
    tenantId: 'b35ece8d63c8d9c817018c770a901594',
    tenantFlags: tenantFlags,
  },
  {
    name: 'fnal',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'fnal.gov',
    tenantId: '7f0c5afd425470793a6749fb753cbf5e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'forgeglobal',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:08:46Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'forgeglobal.com',
    tenantId: 'f6d45cbba473ab6d7a1caa6f4396eb9e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'foundry',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:23Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'mlfoundry.com',
    tenantId: '82976673e88293e144296f4cc850540',
    tenantFlags: tenantFlags,
  },
  {
    name: 'fourkites',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'fourkites.com',
    tenantId: 'f50ad9e5ed12aa92ec2286d9f77c6e1c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'gatik',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'gatik.ai',
    tenantId: '1e47e4637abfc28907eeaa57e870a13a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'gilead',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'gilead.com',
    tenantId: '963db9ff8fcee8d0f249267b74866e56',
    tenantFlags: tenantFlags,
  },
  {
    name: 'glassnode',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'glassnode.com',
    tenantId: 'ff731517362542be83510dcaa366c886',
    tenantFlags: tenantFlags,
  },
  {
    name: 'globality',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'globality.com',
    tenantId: 'f042ff66a3961a9c0b4151690f94468a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'goinvo',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:36Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'goinvo.com',
    tenantId: 'c779019fcd8b3c14b7675cd8c2774644',
    tenantFlags: tenantFlags,
  },
  {
    name: 'google',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'google.com',
    tenantId: 'a57431c114dd682e3466664d140a3d93',
    tenantFlags: tenantFlags,
  },
  {
    name: 'gotinder',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:41Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'gotinder.com',
    tenantId: 'c9f7c12116a4e98dd35e717cc8a7bc83',
    tenantFlags: tenantFlags,
  },
  {
    name: 'grafana',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'grafana.com',
    tenantId: 'db6d668b852bceeac348f12a15ff5aae',
    tenantFlags: tenantFlags,
  },
  {
    name: 'grammarly',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'grammarly.com',
    tenantId: 'e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'graph8',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:28:01Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'graph8.com',
    tenantId: 'd402cd8547f19d32f8663154cdcd5d2e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'grasshopper',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:47Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'grasshopper.bank',
    tenantId: 'dac61860c58300f4dc0454cff5fcf8f1',
    tenantFlags: tenantFlags,
  },
  {
    name: 'greenberry',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'greenberry.com',
    tenantId: '5be01a9bcc9cc4e035c2a7532c3def2a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'groq',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'groq.com',
    tenantId: 'ce75720e192b0d81d60d43fb5d5c809d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'gusto',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'gusto.com',
    tenantId: 'c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1',
    tenantFlags: tenantFlags,
  },
  {
    name: 'hawaiianair',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'hawaiianair.com',
    tenantId: '6edd3f09de1260b828a1db6cd8376437',
    tenantFlags: tenantFlags,
  },
  {
    name: 'hey',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '5d8277982258465991bb9e912c1b8584',
    tenantFlags: tenantFlags,
  },
  {
    name: 'holidayextras',
    tier: 'ENTERPRISE',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'holidayextras.com',
    tenantId: '36f50e0939290665125c8396d44ee857',
    tenantFlags: tenantFlags,
  },
  {
    name: 'indra-eu',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-23T23:08:06Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'indra.es',
    tenantId: 'edd1e3ca5788bb8a3bb0de4623cdecd2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'infogain',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'infogain.com',
    tenantId: 'b1b5f99c9a064c371c1d0426c6456fdd',
    tenantFlags: tenantFlags,
  },
  {
    name: 'integratedbiosciences',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:37:30Z',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'integratedbiosciences.com',
    tenantId: '22408a24efc849069698b0ffee543892',
    tenantFlags: tenantFlags,
  },
  {
    name: 'intertec',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'intertec.io',
    tenantId: '614b8b60db04c8e8b96f86745b15178f',
    tenantFlags: tenantFlags,
  },
  {
    name: 'itradenetwork',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'itradenetwork.com',
    tenantId: '52fa5088ffb68f7ce4121928b1034774',
    tenantFlags: tenantFlags,
  },
  {
    name: 'itsmorse',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'itsmorse.com',
    tenantId: '3b36efaf16840d5b066fdcae4ac6b8ab',
    tenantFlags: tenantFlags,
  },
  {
    name: 'jackson',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'jackson.com',
    tenantId: '58e1283f7aba4493045591976bf7cd95',
    tenantFlags: tenantFlags,
  },
  {
    name: 'jaggaer',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'jaggaer.com',
    tenantId: '349e81c4e25fbdbe5ead6c0a4d5ca0a5',
    tenantFlags: tenantFlags,
  },
  {
    name: 'jpmchase',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'jpmchase.com',
    tenantId: 'ac64bb27c39ec7e79dc9a6004627192a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'juniper',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'juniper.net',
    tenantId: '26e36a182c85550f4d91731b8417634f',
    tenantFlags: tenantFlags,
  },
  {
    name: 'kindermorgan',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'kindermorgan.com',
    tenantId: '8814a60d9107487c9e73a154e71a4dc0',
    tenantFlags: tenantFlags,
  },
  {
    name: 'king',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'king.com',
    tenantId: '60a1c11b68ca06c9e21a064f6c663811',
    tenantFlags: tenantFlags,
  },
  {
    name: 'kla-tencor',
    tier: 'ENTERPRISE',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'kla-tencor.com',
    tenantId: '01306e1a19ce44f4a301b31e9df6112e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'knak',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'knak.com',
    allowed_identity_providers: ['okta|knak'],
    tenantId: 'a30fce64a13f1e24918231ee51099067',
    tenantFlags: tenantFlags,
  },
  {
    name: 'knowbe4',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'knowbe4.com',
    tenantId: '3b0bea5c74c245229b4b142c39909ff8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'kyro',
    tier: 'ENTERPRISE',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'kyro.ai',
    tenantId: '125a9faa67d247c9b832110fb54c2cc8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'lattice',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'lattice.com',
    tenantId: 'a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'leadtech',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'leadtech.com',
    tenantId: '5b8ba5871dfc05e85c052e7918741e31',
    tenantFlags: tenantFlags,
  },
  {
    name: 'leandata',
    deleted_at: '2025-05-15T18:36:11Z',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'leandata.com',
    tenantId: '2a9d5b3018190533862b1fab25148e2a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'lentra',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'lentra.ai',
    tenantId: '86b2bf003d889221f5684a400bba7c25',
    tenantFlags: tenantFlags,
  },
  {
    name: 'linktree',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:29:28Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'linktr.ee',
    tenantId: 'bada096485ab3763517a7e072e3f2fd0',
    tenantFlags: tenantFlags,
  },
  {
    name: 'lepton',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'lepton.ai',
    tenantId: '333f8fef5f1e82145ceee8a442aaa58f',
    tenantFlags: tenantFlags,
  },
  {
    name: 'lodgistics',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'lodgistics.com',
    tenantId: '409272489be04c588ffd19bf5e3e69e1',
    tenantFlags: tenantFlags,
  },
  {
    name: 'logos',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'logos.com',
    tenantId: 'fa296549ff9524d8812541fb6e7dbecf',
    tenantFlags: tenantFlags,
  },
  {
    name: 'makenotion',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'makenotion.com',
    email_address_domains: ['makenotion.com', 'notion.so'],
    tenantId: 'febef4bf59b2467f958e79f7e4f1fcc1',
    tenantFlags: tenantFlags,
  },
  {
    name: 'makimo',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:08:56Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'makimo.pl',
    tenantId: '4c9f2312976ab9200c9060106a0484cc',
    tenantFlags: tenantFlags,
  },
  {
    name: 'marky',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:53Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'mymarky.ai',
    tenantId: '61587b3cb6b461949e4590da993d6f6e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'medallia',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'medallia.com',
    tenantId: '45a57b338aa143221180b9050d90efc2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'medevio',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'medevio.cz',
    tenantId: 'a791e11bc055d05ecdd50d2052439767',
    tenantFlags: tenantFlags,
  },
  {
    name: 'metacoregames',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'metacoregames.com',
    tenantId: '7309567330a341999c5d1d3ceb33ba2e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'momentummedia',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'momentummedia.com.au',
    tenantId: '3c96cf159df91bea5a446f121411d33c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'monzo',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'monzo.com',
    tenantId: 'e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'motorola',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:08:59Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'motorolasolutions.com',
    tenantId: '750379b5926e9f728aa6c253d37e3792',
    tenantFlags: tenantFlags,
  },
  {
    name: 'noblq',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:09:02Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'noblq.com',
    tenantId: 'e79aca8b8e00f2050230549c876741f3',
    tenantFlags: tenantFlags,
  },
  {
    name: 'netdocuments',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'netdocuments.com',
    tenantId: '680dd56e0839920c3433b2c454d0c331',
    tenantFlags: tenantFlags,
  },
  {
    name: 'nextgeneration',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'nextgeneration.com',
    tenantId: '809b0f7cffe54b2eb7f605a28ef6cc49',
    tenantFlags: tenantFlags,
  },
  {
    name: 'nextracker',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'nextracker.com',
    tenantId: 'a106935a6fc7efaab1208e8706b64aa6',
    tenantFlags: tenantFlags,
  },
  {
    name: 'niceforyou',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'niceforyou.com',
    tenantId: '7147f82d14914c1db0c17e5d1d15ee8a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'novozymes',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'novozymes.com',
    tenantId: '2454dc873cf34e5bef390d2b5d4008ec',
    tenantFlags: tenantFlags,
  },
  {
    name: 'omni',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:09:06Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'omni.co',
    tenantId: '6258e306f9da1a70f160abe1a1e8150c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'onwish',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'onwish.ai',
    tenantId: 'e630537a166d4fd1a1898c6d41df7e63',
    tenantFlags: tenantFlags,
  },
  {
    name: 'osf',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'osf.digital',
    tenantId: 'bbff5878c62a4c4dac6efcb95a6b9046',
    tenantFlags: tenantFlags,
  },
  {
    name: 'outreach',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'outreach.io',
    tenantId: 'd3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'oxide',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'oxide.computer',
    tenantId: '38e44e1463ef49418ece8de48ae1b64b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'paloaltonetworks',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'paloaltonetworks.com',
    tenantId: '61d4dd297f749e3291ed8ae744da57de',
    tenantFlags: tenantFlags,
  },
  {
    name: 'peek',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'peek.com',
    tenantId: '458cb0e9bb9d7667ec2794303ed2e3ec',
    tenantFlags: tenantFlags,
  },
  {
    name: 'perfios',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'perfios.com',
    tenantId: 'f7d23768b8f23c20241288a404615d4a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'plaid',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'plaid.com',
    tenantId: 'f1e2d3c4b5a6978685746362514a3b2c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'plejd',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'plejd.com',
    tenantId: '90e4c2375dfc3b24a4cb2aa1fa52dec2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'plutis',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'plutis.io',
    tenantId: 'de54cadc5a92c2f9fe507fc005269a3d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'processor',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'processor.com.br',
    email_address_domains: ['processor.com.br', 'gotobiz.com.br'],
    tenantId: 'ad299fae00559be1d33ae29d801331e3',
    tenantFlags: tenantFlags,
  },
  {
    name: 'procraft',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:09:09Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'procraft.ai',
    tenantId: '31ebaf491689ff5504953a375281b0af',
    tenantFlags: tenantFlags,
  },
  {
    name: 'proximity',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'proximity.tech',
    tenantId: 'e793d2b3554346afa34f18ca00f26611',
    tenantFlags: tenantFlags,
  },
  {
    name: 'qualified',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:25:52Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'qualified.com',
    tenantId: '442f661e578bd6a37550f8eb54fbdf5c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'questlabs',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'questlabs.biz',
    tenantId: '03d7219fa60e87d768f464ec85ef2bb9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'quorum',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'quorum.us',
    tenantId: '64d30e3018512becda9afca7ae9f00f2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ramp',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'ramp.com',
    tenantId: '315b2ff1a75f4954b794dcb876879d41',
    tenantFlags: tenantFlags,
  },
  {
    name: 'redis',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'redis.com',
    allowed_identity_providers: ['okta|redis'],
    tenantId: '7bc50114b3f1df48d3b372821459728a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'respell',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:09:12Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'respell.ai',
    tenantId: '9cc588011a013e3615db2b6a5fbff6f',
    tenantFlags: tenantFlags,
  },
  {
    name: 'rewst',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-24T17:11:53Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'rewst.io',
    tenantId: '4f115d5e0480579fa7cee4bd773020c8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ritchiebros',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'ritchiebros.com',
    tenantId: 'f860f620487b6dba2fbbcd390727811b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'river',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:23:59Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'river.com',
    tenantId: 'f2b842dcf8079a0ea85c2b9b555ba52e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ro',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'ro.co',
    tenantId: '865cdc8c1cc0d253acc3df7f2d7eb854',
    tenantFlags: tenantFlags,
  },
  {
    name: 'roomsync',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'roomsync.com',
    tenantId: 'e01a05cdaead3c52717d951c4b9726c0',
    tenantFlags: tenantFlags,
  },
  {
    name: 'rubrik',
    tier: 'ENTERPRISE',
    deleted_at: '2025-05-29T03:00:48Z',
    namespace: 'xlb',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'aace89b17f627cb1a896d192fe2b65d2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'rubrik-cmk',
    tier: 'ENTERPRISE',
    namespace: 'xle',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'rubrik.com',
    tenantId: '40b2c033a7ecfb2a65c9d4f9fb9f6e18',
    allowed_identity_providers: ['okta|rubrik-cmk'],
    encryptionKeyName: 'projects/gcp-rubrikcom-infosec-cmk-prod/locations/global/keyRings/AugmentComputing/cryptoKeys/AugmentCMEK-HMAC-01/cryptoKeyVersions/1',
    encryptionKeyTTL: '1h',
    tenantFlags: tenantFlags,
  },
  {
    name: 'salesfolks',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:24:04Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'salesfolks.com',
    tenantId: 'fa91c560d015447ca4f9d3c48ff96df4',
    tenantFlags: tenantFlags,
  },
  {
    name: 'salesforce',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:38:08Z',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'salesforce.com',
    tenantId: 'a55ce61d7a294428a42ee63924f096dd',
    tenantFlags: tenantFlags,
  },
  {
    name: 'samsung',
    tier: 'ENTERPRISE',
    deleted_at: '2025-04-18T19:08:14Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'samsung.com',
    tenantId: '8fdaf234c4c987b71e357f101b618670',
    tenantFlags: tenantFlags,
  },
  {
    name: 'sequencefilm',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'sequence.film',
    tenantId: 'a134f086f486ff87e03c7e8c1def1421',
    tenantFlags: tenantFlags,
  },
  {
    name: 'shipwire',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'shipwire.com',
    tenantId: '3b8dd4f58bf745a6a4602e8c2e6980f3',
    tenantFlags: tenantFlags,
  },
  {
    name: 'shv',
    tier: 'ENTERPRISE',
    namespace: 'e2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'shv.com',
    tenantId: '7378e8e3ddf9f0c69209709f6490e062',
    tenantFlags: tenantFlags,
    auth_central_ignore: true,
  },
  {
    name: 'sigen',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:24:11Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'sigen.ai',
    tenantId: '42f20285b77fe13769825727e3e86799',
    tenantFlags: tenantFlags,
  },
  {
    name: 'silanano',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'silanano.com',
    allowed_identity_providers: ['okta|silanano'],
    tenantId: 'e41dff8ef751d1967390ec44033b1e89',
    tenantFlags: tenantFlags,
  },
  {
    name: 'slalom',
    tier: 'ENTERPRISE',
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'slalom.com',
    tenantId: 'cce0f128f230f8dc1a33c10efa5b65a7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'smartfren',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'smartfren.com',
    tenantId: '2226384662ef6fe402f98ecac3d99930',
    tenantFlags: tenantFlags,
  },
  {
    name: 'snowflake',
    tier: 'ENTERPRISE',
    namespace: 'xlc',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'snowflake.com',
    tenantId: 'c0f301d90e87fbd218c79830104c31b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'sparelabs',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'sparelabs.com',
    tenantId: '348ac97b8c165b0099f3a20ee3742777',
    tenantFlags: tenantFlags,
  },
  {
    name: 'specstory',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'specstory.com',
    tenantId: 'e764519ae45c3ebc9000db2dcc97e627',
    tenantFlags: tenantFlags,
  },
  {
    name: 'spoton',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'spoton.com',
    tenantId: 'c0777a137637495289a97dacb10d8367',
    tenantFlags: tenantFlags,
  },
  {
    name: 'stratadecision',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'stratadecision.com',
    tenantId: '3907eb09d07241faaa4a14b73443e3de',
    tenantFlags: tenantFlags,
  },
  {
    name: 'sysco',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'sysco.com',
    tenantId: '25bb5c6576fd4650992a8384b59de9a8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'taskrabbit',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'taskrabbit.com',
    tenantId: '1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'teamwork',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'teamwork.com',
    tenantId: '23e2f0d211894d9693262d65046f7c6c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'techmahindra',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    deleted_at: '2025-06-24T16:24:20Z',
    domain: 'techmahindra.com',
    tenantId: 'eb33f44e1dfbc41c27e8aa403e5fa778',
    tenantFlags: tenantFlags,
  },
  {
    name: 'tecton',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'tecton.ai',
    tenantId: '95611addc202476b0a4ac2f26fc94bb5',
    tenantFlags: tenantFlags,
  },
  {
    name: 'tekion',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    deleted_at: '2025-05-07T16:24:20Z',
    tenantId: '5790aed34c6fc38ab93115c9218f162b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'tencent',
    tier: 'ENTERPRISE',
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'tencent.com',
    tenantId: '0fc8b7e9a372a6fa446d8b6d235c5de8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'thebrowser',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'thebrowser.company',
    tenantId: '16d8fd872730446195224be903a25d6a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'three-form',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:24:20Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: '3-form.com',
    tenantId: '9d4b7149f6d839fa7dd17778d8ae4e6',
    tenantFlags: tenantFlags,
  },
  {
    name: 'timescale',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'timescale.com',
    tenantId: '411384ef9b50cef1a4aa53a8e238b9de',
    tenantFlags: tenantFlags,
  },
  {
    name: 'toasttab',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'toasttab.com',
    tenantId: 'e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3',
    tenantFlags: tenantFlags,
  },
  {
    name: 'trilogyeu',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:09:19Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'trilogy.com',
    tenantId: 'dbad5dbea0c2a7a9d575c6beeb520bc7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'tromzo',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:26:47Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'tromzo.com',
    tenantId: '69267046fcaedea0c9118e4a0e2cfc74',
    tenantFlags: tenantFlags,
  },
  {
    name: 'trustpilot',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'trustpilot.com',
    tenantId: '203e15bbef69f619e9ed3566a7225e9d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'tutorintel',
    tier: 'ENTERPRISE',
    deleted_at: '2025-02-13T05:05:41Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'tutorintelligence.com',
    tenantId: '37cb7e78621f038548ad7083fba573b9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'twelve',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:24:27Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'twelve.co',
    tenantId: '7dca4d25a2d64f4adfb63b66da9968db',
    tenantFlags: tenantFlags,
  },
  {
    name: 'twilio',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'twilio.com',
    tenantId: 'f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4',
    tenantFlags: tenantFlags,
  },
  {
    name: 'ubenai',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:24:33Z',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'ubenai.com',
    tenantId: '6cc517486bbf036b71d83e5f9f4488a8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'uncountable',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'uncountable.com',
    tenantId: 'b82ef9143f59007064f3c89f497f338f',
    tenantFlags: tenantFlags,
  },
  {
    name: 'upwork',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'upwork.com',
    email_address_domains: ['upwork.com', 'cloud.upwork.com'],
    tenantId: 'dfc3c2d62f19954a17b05133ff2f0dcf',
    tenantFlags: tenantFlags,
  },
  {
    name: 'usestyle',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'usestyle.ai',
    tenantId: '70f6f7e930024ddba8b670a1c2f2ffd7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'vercel',
    deleted_at: '2025-07-29T00:28:08Z',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'vercel.com',
    tenantId: 'd7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'veryfi',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'veryfi.com',
    tenantId: 'a3aec01c7019b1ca4e17ab2c0ce82b62',
    tenantFlags: tenantFlags,
    auth_central_ignore: true,
    // deleted_at: '2024-08-29T23:45:00Z',
    // The tenant-gc job's TTL is 30 days, so artifically set this back a month
    // to make tenant GC run on this tenant, since the customer explicitly asked
    // us to delete this data.
    deleted_at: '2024-07-29T23:45:00Z',
  },
  {
    name: 'veryfi-2',
    tier: 'ENTERPRISE',
    deleted_at: '2024-10-01T21:09:19Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'veryfi.com',
    tenantId: 'dd8582279728b797aeb6b725c1f1182c',
    tenantFlags: tenantFlags,
  },
  {
    name: 'veson',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'veson.com',
    allowed_identity_providers: ['waad|veson'] + standardIdentityProviders,
    tenantId: '32c91572b1771c52aa6a5e9514793b4e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'viasat',
    tier: 'ENTERPRISE',
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'viasat.com',
    tenantId: 'e2f28e4881dd49b4b0c85686464696ba',
    tenantFlags: tenantFlags,
  },
  {
    name: 'vidio',
    tier: 'ENTERPRISE',
    deleted_at: '2024-11-07T16:24:38Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'vid.io',
    tenantId: 'd6dae50cfca4816c6c08c794aad1e069',
    tenantFlags: tenantFlags,
  },
  {
    name: 'vista',
    tier: 'ENTERPRISE',
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'vista.com',
    tenantId: '81ba83d2da0ec0294386937e0f70a6a3',
    tenantFlags: tenantFlags,
  },
  {
    name: 'voicemod',
    tier: 'ENTERPRISE',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'voicemod.net',
    tenantId: '169ec5ddcf715085e45052216a1a8616',
    tenantFlags: tenantFlags,
  },
  {
    name: 'walleyecapital',
    tier: 'ENTERPRISE',
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'walleyecapital.com',
    tenantId: '832d8f0b43a6d73acc4c95ef2318554a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'wearenotch',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'wearenotch.com',
    tenantId: '40381cac52f6e5ac2d2edeb6b129d44a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'wehaa',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'wehaa.com',
    tenantId: '9347bceaddc66574f897858168a914ec',
    tenantFlags: tenantFlags,
  },
  {
    name: 'wiz',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'wiz.io',
    tenantId: '7a55b817f1a6c9346de0d837410371d8',
    tenantFlags: tenantFlags,
  },
  {
    name: 'woflow',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:29:01Z',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'woflow.com',
    tenantId: 'b9dedcd143cd79de7f17dcbc8b450cdb',
    tenantFlags: tenantFlags,
  },
  {
    name: 'workato',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'workato.com',
    tenantId: 'da2bacea7b4cec0bb370311640ea801a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'wwt',
    tier: 'ENTERPRISE',
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'wwt.com',
    tenantId: '83ed2a9a58c54dcaad436c828ab61d89',
    tenantFlags: tenantFlags,
  },
  {
    name: 'x',
    tier: 'ENTERPRISE',
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'x.com',
    tenantId: '9a4323a3426983ab24744f3fdbd15200',
    tenantFlags: tenantFlags,
  },
  {
    name: 'yologram',
    tier: 'ENTERPRISE',
    deleted_at: '2025-01-29T09:38:50Z',
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
    domain: 'yologram.com',
    tenantId: '6a22c2cf5788fdfa7b5b71b50a84277b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'zapier',
    tier: 'ENTERPRISE',
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'zapier.com',
    tenantId: 'a54902989a804ae1b1d1b76bb29434dd',
    tenantFlags: tenantFlags,
  },
  {
    name: 'zenbusiness',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'zenbusiness.com',
    tenantId: 'aed8c0a0e51641ffb2caf88010f7f485',
    tenantFlags: tenantFlags,
  },
  {
    name: 'zoyya',
    tier: 'ENTERPRISE',
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'zoyya.com',
    tenantId: '9f870f4b2a2d0565c4d622e4173019d7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'zup',
    tier: 'ENTERPRISE',
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'zup.com.br',
    tenantId: '1fd51849ad9f4c4d9379ccf247ba9f4e',
    tenantFlags: tenantFlags,
  },
  // Launch waitlist tenant - for approved users with non-corp emails
  {
    name: 'waitlist',
    deleted_at: '2025-05-13T20:19:26Z',
    tier: 'ENTERPRISE',
    namespace: 'e2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '82b7609c95b3a24106e0963c5721ece2',
    tenantFlags: tenantFlags,
  },
  // Launch dry-run test tenant
  {
    name: 'augment-test-1',
    tier: 'ENTERPRISE',
    namespace: 'e2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'augmentcode.com',
    tenantId: '7300c0d0ba9422b1cb40919bcefb66e2',
    tenantFlags: tenantFlags,
  },
  // support tenant for d0
  {
    name: 'd0',
    tier: 'PROFESSIONAL',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    namespace: 'd0',
    tenantId: 'ebf72a67e49ea773792586e20cc803ee',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // Put discovery users (individuals/enterprise-tier) into this tenant
  {
    name: 'discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '547bc5d2ea39a8a3605ae752ab3437f7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'regrello1',
    tier: 'ENTERPRISE',
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'regrello.com',
    tenantId: 'e1d53ada859da4b4c65fa24a7aca7576',
    tenantFlags: tenantFlags,
  },
  {
    name: 'xlb',
    tier: 'ENTERPRISE',
    namespace: 'xlb',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '8b7f8a01dc22f864c89ce3f742650792',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'xlc',
    tier: 'ENTERPRISE',
    namespace: 'xlc',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'cb830c62d8c5afa02afc9a00d51528b3',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'xld',
    tier: 'ENTERPRISE',
    namespace: 'xld',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '4002c7d55a1774b0deaf1fd62bb2cde4',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd1',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '3efe184c133cb61aa7c931999c2f2f0',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '76e460e27650804458fc961ac17b3911',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd1-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'ab62238e51e3ef692c1af332aa99e670',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd1-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '91626fee8217760fcb317a4085b2b2ee',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd1-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'ac9c225a1c6e3344105123f91fe3eba9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd1-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '2f805ce0248adc2e9c1f585d07256adb',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd1-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'd00283fae8546923f0ca5bb067c1ee40',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd1-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'a2d2143cfbcf28dc56a22daf31b49c73',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd1-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '27513201ce5b434fe59137c398568331',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '676b9e1c8cc2163fd4287aa5ba81df8e',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'afbe86b2910e960fe1bd8d1ded71d082',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'e6a0a587a6c69d919c77d047cb305325',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '4c9b427d3168aa47e0190cf9501dd694',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '985b1aae539f600271cece26b0448730',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '780566960bbe71d616cd5f1c77ca8393',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'bd373c75f7ed32e5360e001aaad27af7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'e3a9ca2730b12e37830922613b24fbaf',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd2-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '8968422dda27a720e1cb83611a65872b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '79d401ac0142d6b34405827fe1a71105',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '6c9a69fc1a2b00b34174afaecb8f75a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'b2f6eeb5198ac4bb4dd34ad2c3083b9b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '18ffc9249e17b1c7d168d701d57d9cab',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'bb9aa25de1b936bf5cf71b48b2b9eb36',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '29934a9b480add17f7d1d19ad4b7c4b2',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '4dd3ce3abda96106a245ea7aa143b1ec',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'fd7eb3ad888364f7b879253b146ce0d7',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd3-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'eb2676081c6625781db5467e7fccdb6b',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '1a8a320c089884a500be3a6ad93b2a01',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd4-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '417db354746f70d1407baea02578fd64',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'e3532bb98a36e819c0447538abe42bac',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'ca90236228dc66c052541299af54ab3d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'c2f9052cc0a992ec535960ee5c28c40a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'cda70efb707d7011f62580f9618e1453',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '50658b7d53cbcd65c7584e6d52943485',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '39c3079ed33ca4eda6393e680739d46e',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd4-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'b8ced283bb8fc9632a373c3dd173ae32',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '7de4130e8fdb47c2e18dcfc10805de3d',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd5-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '52efd623495cf7efa31a869cd6dc6aef',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'dd27ddf6a5de61a63c8cedcee147e332',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'ff5a8532292b5386153495be82e09b2a',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '5250ae0952f5716a5e64ce9437766570',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: '995f2dd0f6e772f074a5810670bdc89d',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'b2c6a27eb57ebd4e76ff69b2a4db0e09',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'b296b29c44215a849822f9b0b94fd6d6',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd5-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tenantId: 'c6a2ef3fb09064f7d3e76f8647e20aa9',
    tenantFlags: tenantFlags,
  },
  {
    name: 'd6',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b1eb6e321630be05e5ac33b8e3b6dbc8',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd6-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'd0157682e21b0ebc25e0b146bf308eb6',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd6-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b4ca6ed101dd5fb93c833dfa3405b02',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd6-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '350ff36a75331ba6e3d23cb8566a8abf',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd6-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '84a85e0c220f2418fa606b30ed004f91',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd6-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'd7fc0432d6e5110237f165e822a8e1ad',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd6-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '8946712c794f8e8ca8322869951f1757',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd6-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6ccc35baf1da258395605f414779a7db',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd6-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'cd08176b03bc9f1fe46494179b87aa15',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f0780f99079e77e918c5324ca08aa1ef',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd7-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '3583bf96f87217bada9dd7c9f64b10f6',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '3f49b34d45a44fef4e4273b357eb7275',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '3cd796fa32f649f08755fe82c37b77b3',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '468dea679f6c06d7e8f549c8034cf72',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'e8cdc667c76ab43964f956fd8a4b4b60',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '4a5dcebe5c84adaef10e1bd3432247a5',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '2f77b766244166800e228d070ff13533',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd7-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '1011eba1f6144a2d3a68596598798243',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9e87ebf15777c6762fddf5bb5b27ce86',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd8-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9bc5a09f54de20515a85a6cf5fd11696',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '2d4595590620ee051b2c566980f2832c',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f69c613efd4dc870d1d7767e66d40400',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'db3b7087810f2e4741425eaadd9685f',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '67ecbb58efa2a1b4933f195c033b7932',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '3749ddebbc3b78a41ba283697e1fd323',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '40e40eda377c603b274e254bf9f8d75c',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd8-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '1265583313f0e35d540a6b98d54d35c6',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '5c36eeed40f0dd9f95134dbec1b45572',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd9-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '2fb4df6d11df165e36bed77f7e8e9014',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f77ebe77f675dedf69e7e081025331aa',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7605cb0c27dfa7d075a9c3d2b9af3f18',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '87eb43720d35fb42c4990e06ddbc3d12',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '835ea79e9756030087ab645c4a7c9557',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9d03186e92f5deb56b83d4abb6e9bf3a',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'cd9856fd3096fd6be6fe6395722cb7c0',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd9-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '688949a0c728565cc43bba5589923ce1',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b75e3d716fad80619d3027f360b65c9f',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd10-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '2e9069391bc43c6b94327cd1ca62ddf8',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'a5a4b66ea0c1624c83f479f1c5d68aca',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '314cbf5e8429e0390d68273d9aa70abe',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b143ad3ea8e6d54648a8b99da2e40600',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7a2f4d8149cac95b625d94e0f524799c',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '5dfff19b00bdfbee0919d715e14d9819',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '54bbd21750b835a9bc74369186865d5f',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd10-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f8ae7fe23ffeffcb6748865a724f730d',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'cc5c5dadbaffa18a6938553e8f7f1150',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd11-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'cd63a4e00557bcbaa4896f29791c2225',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '479360d0b0d1ed9fd49c1d766c25c5d1',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'bb88838227da51f7be92b16532926cc2',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '1c09f8a33a4f0d62e9559cf31d0c8ed5',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f3344322f306653fb21af0e5f3851e44',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'e6beaebe2a6ab30621df558a9b8edca7',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '4ddd83aee87b76e4a73bf83eed730f2f',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd11-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '30610e7e7cdc6b785a286bdd471e529f',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'ed822194564282831654fbdfccedd842',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd12-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b2edb83ae688d452bd270933148cd0ec',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9f34960401b131a3172b6901216f6697',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '2a18615f266ccb8123fc550c9111e51f',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '4c8d3b6a0923f2d51c9077ed64d5d781',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'a5be695db6c75355493daef4eecb1664',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '99d3a834e3e03c4efad74f3013cbfa51',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '2db90e6b3c61f4cbcc74dc13615dbae6',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd12-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd12',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6c51f8ab2de12ee9a432699427e445f9',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'aa2825b1d8e4c2a426565fe3848c340c',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd13-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b47da9eca75c943707fafab0467bb4e1',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'a25414d8c245f80a80edbab98cb3c6ab',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '873494d75ae70a147ad27c1cc2cafb79',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '39221a2afa2b92b8eeb65fd5cc5b58d9',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'eae5a00dd4098392bd824ef9458e2966',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '609f8f4b27126e5faa61fa1e5b02112',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '3a3434c90c4245e422892859bc115c53',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd13-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd13',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '132884d24839eb84a2f0d6aaa17bec6f',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'bf30d009df85584d68f2ee98c6116e01',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd14-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f09134cc11808c97c8044b4e4299a8a3',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '96994dbaca9c6849bdbe576cf9d893a9',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '4acf726cbb4460b3a99984bee061cb5d',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '852a91f908ee228bc9bf40a850942ba8',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9e2a61d4d79d355278bd27404ef4d248',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'd530c306c6c6404885777b03144e3075',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '547a0da2879d5a78814b70ff8ecfb7c5',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd14-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd14',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '575d1cb05dff92292ad89c235f9d4351',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '45a4062bf7cc97f517439307e4f65619',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd15-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '426b70e26b4f619caf605e00f873ac48',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '92ba60d1965a2da646f080431e186a05',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'a92efcb45a277fdbeb96701c8e248894',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6e2ec278df9fd59b7fbf690ebcdc4056',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'c116dc4871070450e04a2a5e08a00eab',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '614cd95044a438e40574848537968a4c',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'dfbaa453a53bb349bedcb2a5c4d1b27e',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd15-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd15',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'eba08a712602529e1b360dacae4fd64b',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'a546182c977380bdf2ba92497a6fc06b',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd16-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '2fb3d631db41fbad597c4a70783a772d',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '75af7955dcd4d462fa3e913de5bf426d',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'd6a79859b6e9ff8a73aaf0cc78bb7857',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '598aeb2a934c17ae61dd148111170c43',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '95998647aafe245daec78ac0e0ec5a42',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b0b4936b6c5544961879a95423afb521',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '32d5cd7d66abbd7f87fe48cf2135962c',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd16-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd16',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f99f2d8ba5d0e641f11a5e7bf5a12059',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '106406e46b7de7cfdec2cdbbd000fdde',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd17-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b6e639917f64d9dad5aadca15c9d3415',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'e4d5d2cf688f488309bb540c6d00a4d2',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6a3bb60d34a1a365ac77a738c64980f5',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '686643f3fc948d7594827de8ec9d5e4b',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '886b9df533daac7b63a9e847246db9cf',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '262ae97cb529b4461f58e4925ecb3e65',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9f959b41bd2ec345c0672fc18bcdb0e8',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd17-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd17',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '58bdb9da920fcfc2f259a534e8dfba35',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '5a8541778bba92ceed967197a20146c7',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd18-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9461caf202d41ccc99d9ac6269948d1b',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7da2ebd7a428f03c6e1f89813d07e16',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'c82769dfdd1e64ac50b5382547f8a15a',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7f87aebce07f08574faab51d58c12be4',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7094d106da04aa12c5a02561b095eed9',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6433767814b426998428b3b1fbaf4258',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6ee5d47e4e63fbf67abd1885c0ec80d0',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd18-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd18',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '16bb5186264bc1e8e1e02a482bef9170',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6f16bae9a23184c981eeee0ea26d5ad3',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd19-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '42eaf976341c9208c82f6ed736dfa416',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '87b588447c3c735e8e4eabda31cb0529',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '53123cd4dd649764edb5598b3148bd8c',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '8bb4f76898dbd91aedad36a88b77f6fa',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '3d24dff3691c3200c8fb23d754a61872',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f782f91b492bb323c23775042f37e9e3',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'e9c9772e215712c78def94620c0480cb',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd19-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd19',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7ff9691bcb820711f314e64062de0598',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'f9c5316378fec247562b97d24597084f',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'd20-discovery0',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '9d4c2bb8989904a33a369bf293c14a0b',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20-discovery1',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '25b6ad8d93f971ba1cb50c14bf2fcaa3',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20-discovery2',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'acc046afe68bade75d784c04ce2839fd',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20-discovery3',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7bafb5cd3c3ecc92bfc420c3a737db94',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20-discovery4',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: 'b0a03079f76ee67f91125ae796ab54d4',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20-discovery5',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '6662c6edf697548d893d85ebf2c839b4',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20-discovery6',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '7c5ef5167e93196d40b3eadac6e6dff2',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'd20-discovery7',
    tier: 'PROFESSIONAL',
    namespace: 'd20',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: null,
    tenantId: '3ecc3e9489d4896f13e2b910e5f83c52',
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'discovery-blocked',
    namespace: 'd1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'linshiyou.com',
    tier: 'PROFESSIONAL',
    allowed_identity_providers: ['nope'],
    tenantId: '45ed9d9a5d15ab0b524fd233e043940',
    email_address_domains: [
      'linshiyou.com',
      'merepost.com',
      'ncps.cc',
      'youxiang.dev',
      'bichengo.com',
      'fadacaia.com',
      'htn.lianzhanao.com',
      'hotanmi.com',
      'mailto.plus',
      'fanno.fun',
      'fage.asia',
      'feiniao.site',
      'figuree.online',
      'temp.now',
      'futuree.fun',
      'ision.us',
      'ptct.net',
      'gggapi.ggff.net',
      'redaaa.me',
      'yuanyoupush.com',
      'loveu.kg',
      'ggapi.dpdns.org',
    ],
    tenantFlags: tenantFlags
                 .override('supportTenant', false),
  },
  {
    name: 'xle',
    namespace: 'xle',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'ENTERPRISE',
    tenantId: '5120bbd60da3c379d0fb9b09e328ea62',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  {
    name: 'xlf',
    namespace: 'xlf',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
    tier: 'ENTERPRISE',
    tenantId: 'e3bdbde105037d3dbc2e89c6b2019380',
    tenantFlags: tenantFlags
                 .override('supportTenant', true)
                 .override('supportAccessControl', false),
  },
  // ADD PROD TENANT HERE
];
{
  stagingTenants: stagingTenants,
  prodTenants: prodTenants,
}
