local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         .override('exportFullData', true)
         .override('exportAnnotations', {
    enabled: true,
    webhook_url: '',
  })
         .override(
    'extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
      memories_params: '{\n        "injection_prompt": "\\nHere are the memories already saved:\\n```\\n{currentMemories}\\n```\\n\\nHere is the new memory to remember:\\n```\\n- {newMemory}\\n```\\n\\nIncorporate the new memory into the current memories, by adding/removing/modifying memories as needed.\\nIf new memory is already present in current memories, just return current memories as is.\\n\\nReturn ONLY full updated memories and nothing else.\\n",\n        "compression_prompt": "\\nHere are the full memories assembled through all interactions of user with coding agent:\\n```\\n{memories}\\n```\\n\\nYou task is to summarize and merge related or redundant memories to retain only up {compressionTarget} most informative and distinct ones.\\nPrioritize preserving information that:\\n- Relevant to codebase, policies, preferred technologies or patterns\\n- Will be useful in long-term\\n- Describes user preferences or long-term information about user (like name/email)\\n\\nReturn ONLY full updated memories and nothing else.\\n",\n        "classify_and_distill_prompt": "\\nHere is the last message from the user:\\n```\\n{message}\\n```\\n\\nYour task is to detect if the last message contains some information worth remembering in long-term.\\nInformation is worth remembering if in the last message user gives some new information, asks to do something differently or describes user preferences.\\nKnowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!\\n\\nExceptions (do not remember such information):\\n- If user asks not to use some existing tools\\n\\nReturn JSON with three keys (in provided order): \\"explanation\\" (str), \\"worthRemembering\\" (bool) and \\"content\\" (str).\\n\\"explanation\\" should be short (1 sentence) text that describes why the information is worth remembering or not.\\n\\"content\\" should be short (1 sentence) text that describes the information worth remembering.\\nIf \\"worthRemembering\\" is false, then \\"content\\" should be empty.\\nReturn only JSON and nothing else.\\nExample: {\\"explanation\\": \\"some explanation\\", \\"worthRemembering\\": true or false, \\"content\\": \\"memory content\\"}\\n"\n      }',
    }
  ),
}
