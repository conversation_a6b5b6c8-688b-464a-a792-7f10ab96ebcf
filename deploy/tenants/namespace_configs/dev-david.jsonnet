local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config
.withDeployFlags({
  orbWebhookSecretOverride: {
    name: 'dev-billing-webhook-orb-signing-secret-dev-david',
    version: 'latest',
  },
  // Enable full data export for debugging
  exportFullData: true,
  // Use dedicated BigQuery instance instead of shared dev
  useSharedDevRequestInsightBigquery: false,
})
.withFakeFeatureFlags({
  auth_central_login_invitations_enabled: true,
  // Billing and subscription related flags
  auth_central_enable_stripe_event_processor: false,
  auth_central_create_setup_intent_on_customer_created: true,
  auth_central_enable_billing_event_processor: true,
  // Enable team management features
  team_management: true,
  // Enable bearer token authentication for e2e tests
  auth_central_bearer_token_auth_enabled: true,

  team_management_dry_run_checkout_flows_enabled: true,

  // Enable e2e mode in customer UI
  customer_ui_e2e_mode_enabled: true,
  // Disable react signup
  auth_central_react_frontend: false,
})
