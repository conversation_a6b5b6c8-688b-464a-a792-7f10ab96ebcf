{
  override:: function(obj, field, val, allow_new_field=false)
    if std.objectHas(obj, field) then
      if std.type(obj[field]) == std.type(val) then
        obj + { [field]: val }
      else
        error 'Type mismatch for field ' + field + ' expected:' + std.type(obj[field]) + ' got:' + std.type(val)
    else if allow_new_field then
      obj + { [field]: val }
    else
      error 'Unknown field ' + field,

  check_flags:: function(obj) std.flatMap(function(field)
    if std.endsWith(field, '_') then
      local flag = std.substr(field, 0, std.length(field) - 1);
      if !std.objectHas(obj, flag) then
        [flag + ' is missing']
      else if std.type(obj[field]) != 'object' then
        [field + ' is not an object']
      else
        std.flatMap(function(f) if !std.objectHas(obj[field], f) then
          [field + '.' + f + ' is missing']
        else
          [], ['date', 'description', 'owner'])
    else
      if !std.objectHas(obj, field + '_') then
        [field + '_ is missing']
      else
        [], std.objectFields(obj)),
}
