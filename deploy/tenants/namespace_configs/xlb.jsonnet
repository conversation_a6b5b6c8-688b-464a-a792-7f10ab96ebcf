local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

config + {
  api_tokens: [],
  flags: config.flags
         .override('passthroughBigtableProxy', false)
         // experimental RAM overrides for Adobe Acrobat PoC
         .override('embeddingsSearchRamGbOverride', 128)
         .override('checkpointIndexerRamGbOverride', 128)
         .override('usePremiumCpuHighmem', true)
         .override('useBigEmbeddingsSearch', true),  // treat this like pstg size
}
