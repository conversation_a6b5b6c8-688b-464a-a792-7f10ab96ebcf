local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         .override('useBigEmbeddingsSearch', true)
         .override('passthroughBigtableProxy', false)
         //  .override('embeddingsSearchPartitions', 2)
         //  .override('enablePartitionedEmbeddingsSearch', true)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
  }),
}
