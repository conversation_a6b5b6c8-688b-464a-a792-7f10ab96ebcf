local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

local orbWebhookSecretOverride = {
  name: 'dev-billing-webhook-orb-signing-secret-dev-neil',
  version: 'latest',
};

config + {
  flags: config.flags
         .override('orbWebhookSecretOverride', orbWebhookSecretOverride)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    auth_central_enable_stripe_event_processor: true,
    auth_central_enable_billing_event_processor: true,
  }),
}
