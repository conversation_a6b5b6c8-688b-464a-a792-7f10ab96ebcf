local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

config + {
  api_tokens: [],
  flags: config.flags
         .override('passthroughBigtableProxy', false)
         // treat this like rubrik size
         .override('usePremiumCpuHighmem', true)
         .override('useBigEmbeddingsSearch', true)
         .override('chatHostReplicaScale', 2.0)
         .override('completionHostReplicaScale', 3.0)
         .override('deployGlean', true),
}
