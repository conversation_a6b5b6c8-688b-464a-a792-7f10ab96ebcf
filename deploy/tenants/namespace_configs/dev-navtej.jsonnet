local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

local orbWebhookSecretOverride = {
  name: 'dev-billing-webhook-orb-signing-secret-dev-navtej',
  version: 'latest',
};

// Then make sure it's included in the .override() chain

config + {
  flags: config.flags
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    auth_central_enable_stripe_event_processor: true,
    auth_central_create_setup_intent_on_customer_created: true,
    auth_central_enable_billing_event_processor: true,
    auth_central_invite_users_to_tenant_plus_allowed: true,
    team_management: true,
    auth_central_bearer_token_auth_enabled: true,
    customer_ui_e2e_mode_enabled: true,
  }).override('orbWebhookSecretOverride', orbWebhookSecretOverride),
}
