local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         .override('exportFullData', true)
         // TODO(arun): Temporarily adding this flag to test the shared vendor bucket
         // feature.
         .override('sharedVendorBucket', {
    enabled: true,
    adminEmails: [],
    accessEmails: [],
  })
         .override('deployCommitRetrieval', true),
}
