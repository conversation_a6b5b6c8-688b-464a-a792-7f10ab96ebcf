// AI-tutors-turing is a project for contractors from the Turing vendor to generate
// data for AI training.
local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

local sharedVendorBucket = {
  enabled: true,
  adminEmails: [
    // NOTE(arun, 01/16/2024): Added per email thread with Satayu.
    '<EMAIL>',
  ],
  accessEmails: [
    // NOTE(arun, 01/16/2024): Added per email thread with Satayu.
    '<EMAIL>',
    // NOTE(arun, 01/16/2024): Added per email thread with Satayu.
    '<EMAIL>',
  ],
};
local exportAnnotations = {
  enabled: true,
  // NOTE(aswin, 03/05/2024): Add per email thread with Satayu
  webhook_url: 'https://us-central1-turing-gpt.cloudfunctions.net/augmentcode',
};

config + {
  flags: config.flags
         .override('exportFullData', true)
         .override('request_insight_event_retention_days', 365)
         .override('sharedVendorBucket', sharedVendorBucket)
         .override('exportAnnotations', exportAnnotations)
         // slackbot is deployed here for aitutors to try out slackbot. They
         // work on open source repos, so no data leakage concerns.
         .override('deployGlean', false),  // glean is still not deployed since AI tutors do not have a Glean instance
}
