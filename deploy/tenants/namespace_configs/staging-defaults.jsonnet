// Configuration for all staging namespaces that do not have their own file.
// Staging namespaces that have their own file should import this file.
local prodConfig = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

prodConfig + {
  api_tokens: [],
  // okay to enable as we do not care about audit logging in staging
  flags: prodConfig.flags
         .override('enableApiProxyGrpcDebugEndpoint', true)
         .override('exposeInternalModelNames', true)
         .override('remoteAgentWorkspacePoolGroups', ['staging'])
         .override('tokenExchangeAclCheck', false)
         .override('authQueryTokenCacheTtlSecs', 60)  // testing out a shorter cache ttl for auth tokens - want to use this for self-serve namespaces eventually
         .override('deployCommitRetrieval', true)
         .override('enableChatStreamPayloadTimeout', true)
         .override('enableBillingPlanValidationAndCreation', true),
}
