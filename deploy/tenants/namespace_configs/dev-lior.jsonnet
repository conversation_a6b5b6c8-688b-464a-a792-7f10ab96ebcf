local config = import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet';

// This is configured for https://github.com/organizations/augmentcode/settings/apps/augment-app-dev
local githubSealedSecrets = {
  app_id: 'AgCWhNa8x1Uer2vzFLqVxiRR+29KIVOnpuFfST4ItLtbJvWnrMNumNtCCn5jC1rPFSJCUQEUFPRg6aGg2k7bQ/LdkGytElhzDSLXWHqgb1u8uS/MleTqDZxvhc8B+WQrk/JyNiEhDRzsY2DcNKb2Y0/hElYLHUWK23ii4KNFucT9Rz0rfzvo2Aoz9aWMUcXqejtwAv2fMh2jLoagVVVUoewWnsZknoK9YmJm+E1jVKxF9ENHzynwZaB6+kPnGkeVbc2AsP7fhhQ5VvYXbqtdA2FIxSCpOua9pNImgDiBAEMFL22Y52gTP5T4zSmz3WeCmnvHyDot3t9EsCfVqCzvDFUW/kjyff68hewDdKIxVdyPclw+nOok6fGQUQNtcbTCRlS6zpv6qUkwOGdRCLT8GbDrxX7Y9Kl3MtAjIN4S0nqB0+qrj72B0QJvcpZuTR4jn8zgV11Eqqj3tWDDgEzg5fa1uZuN/PO8fIr9ojWDUd3BCClW8jLMqb3cWddN2j+/Go50eEFeEzIyYQMygwtWehF6WFGMrvTpysqiBK04Xqw+yHQhfTlh0y/NVl5LqIHdNsgtzlmucneqb8QPt/OglEspEIHMhzwkB9uUf0dkmrPTGXvlO4caB1L8qwzJx94c/mDcTrDAgiTdCMPxbOtOsl1J5qDnDTNGnExBI8UVeiVlsGC7ZQBwuhG7Z+nArPMAuFXVBIm1HuFa',  // pragma: allowlist secret
  client_id: 'AgBoga5NqkKM0FTqmYZKJCnPDr4HS+BmA0GK/ZCFmQpQqiu3ioX37XhWddBBxHwXGRoQBOQFnL1cus7yT1hTMrKnqiBO+BQRG0GC/ny4MIgCkysvirpje0y9ELKkbgvulopXkV1vLog3ImDRk3I98VnTzby3aIaWlAAmy1x5F7Mi7jSXdjJDSZkXDWm7cvuJdHfQ+PP2zwuBWayeSEr3UPcL+cFQHYHQQ9LaLAAEkg6BnTB1sr9KsXCepxe06YfQWCRE57hoA+aaUvxTRAI3058wTTsgWUvy4JU5maZPhmi5OORFG1vrzMh408rSr9WcFp1Vl0G77TLzSwo1FnWcrByqf9Q5aXAfNYBxMoqmC+M332reDArC9U6IU360iRXq2EPB5qRZGDnNZ4zg9fi9I9HHWZyzQ9Ng2etqMSzWfoCKbs2w9fJ6avwFxAcmdy5MDkP4uz1Jp4uhXTjfZCppcuehElg5aYfWAG6hF0Mc4cU6UEkxbLr6wtPvDwdJDuvH41w3hlDFOfQ3Yz6idI9AqZ//boMTaYhvUw5Q0SNGkABeRXDvP0MFYJ8xcYWKiw7jc1ks70EvMfyeersMcMSIEk4lUWCrJvsgIqzGNu4m9t/Z547KkBLZ56CrqBO83d1AOYKEA8b/b+WRDjCNIvJrnS2AOcuI3e/r9i44BfNo6Sxo9CLwT+Z2L9D0bJ1eYuhFYqgQTcVk4+28MxI88BdDGWGy7PCsag==',  // pragma: allowlist secret
  client_secret: 'AgCJly9MZzClTU/9LKFNvDhU/0cVHSyEjllx41oVhtF6E3xIqMtkoLFUMQmH/9luIAZiqvuxE8e9l1VVyvviWzKqMjIZSHf5yJP7pi4q/K0j95srytnZOh4hwX3hhv2BC6WnvvNP4T/4s1DbjSOjSQ8FdH+t2FJxJT/lx6PO8QgWg2OvftIxCnm38xZU9zkB1MnjJxGo9Z5iU+FeEC5EicxOitbAwYCXL+lxGtvZ9lT2bi6Pn4Z1kWxVEQSmn9du0m1duursDg6fJHkR475ICo4/3FDPNWzzRsCI8i2FcYzKGoxmXuxsVazC07g+5D0veFNZSW14nbO8sekgJ3VwMDyuA5uYeNcY9A9KfwnjKnwwHIrH33lccObuBWwc2XdqxEIQEirHUOq2iipa1R+k9cAf5eCfb6AI6pNQe8SCSySP1Ybp+4NDBF7JK79xQzNr5MeZPITiIIXGKavYounji3vrPpY+n+HO4zRtSsH+OMo7us9cMhGSsZ+2GGYVnPBeFQCoGMNqSSChS2Zxl6cp+4nPI7IblGAmSJbiaIxE2TWP1pw8bjbf9PkB0jArpcl82fPZdrjB0zCK2Qdw+VmCBdbFbjZnQvlvZawfR0fllk1Hu1ZThqUY8s5r7ryuf4kCjoO6BarE4Z4bunrDpD1zaOr+1s0AkADxFgaaH7UDnT42bOz58vrAndWLmI9Tgy+AW60dtp8n0ghtpVUIIuF1h9ZbCOu4hwaBR2q9MYdwKBAqmBP4zaFdeYH6',  // pragma: allowlist secret
};
local githubPrivateKeyInfo = {
  name: 'dev-igor-github-processor-private-key',
  version: 1,
};

local linearOauthClientIdOverride = {
  name: 'dev-igor-linear-client-id',
  version: 'latest',
};
local linearOauthClientSecretOverride = {
  name: 'dev-igor-linear-client-secret',
  version: 'latest',
};

config
.withDeployFlags({
  remoteAgentImageTag: '{namespace}',
  githubSealedSecrets: githubSealedSecrets,
  githubPrivateKeyInfo: githubPrivateKeyInfo,
  linearOauthClientIdOverride: linearOauthClientIdOverride,
  linearOauthClientSecretOverride: linearOauthClientSecretOverride,
})
.withFakeFeatureFlags({
  remote_agent_actions_enable_endpoints: true,
  remote_agent_actions_enable_webhook_trigger: true,
  beachhead_enable_sub_agent_tool: true,
})
