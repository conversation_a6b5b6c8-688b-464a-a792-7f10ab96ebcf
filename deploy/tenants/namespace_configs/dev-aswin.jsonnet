local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

// Setup instructions: https://www.notion.so/Setting-up-a-dev-Github-app-11cbba10175a8030af17de8f140724eb
local githubSealedSecrets = {
  app_id: 'AgBoOJ1PoemvO8tISyUuNvfT8H7kF2ik+OM4z0ITyPsjGOQF7sNPRGih4ZR8id6VlAqJyK+vbgVYPPdJ3T+DAC38v/vc2j6mQ120TuFcYGH3iJ/Y3zq5eBg4XnmoF5PARf3acPamngyCp3I07NfYDFKSIaKym7PGU4D/oz7jr+0lIGicnBCHIMTdNh+75Z8HAvwvYJ9nLyf+PMLSkrG9JMkuOMof921qsMzvcNxTNRKfnwctZpuE8vTP1J5mBsqKG9gDEt1034IlPZSZ9B9IbPqjpzt4O9tY14HCA+gQXTUmoJxZUwFPlxKM9gj7ICNJTjn4vCBqimBpUc36mSDFxU6MrgscmDUypjCDwHK6F77997ZiazG0xyGd2xkM9EoCaYPbArJ48xKiDZqfBlbtEQBfOQQmcf/BStn0qluO53fBeOab1XbDSSVraHQUlvOV+sWrU/wjwt7rmh4NWaimGaQtzdPXJsQWP4n/f14/pPOaie/1UhLn5jWFcFdhnkGH/LM3Xc9pntbzOk2A4tW7ucZq8ErtYaFSQzj5LpRMa2bdLBof2rE+Fdvg8QX6eJp9sxFt7Tr1CndxvdK9zRmd/qeRw/CE4T84qXmfEOlFrGt5hQWtz8JaI6+fEQV1Sy9znsqX6s7rb/gQwwXwR5o/Zf3iUJyn3RfNo4k9bV8vO//tkPW320tBjyum2k3MLmK2bWCA+zd00z8r',  // pragma: allowlist secret
  client_id: 'AgBMDBK0qRy0q1AnI6MBDLxiuSi2w+UYKQXSQ4EiEO562pHpJB+7oRNhyDWHFjgAEeuAXD3YQYMxUVCn0cMkyeY4JImkjHvRBTWP+lyU7bt83TpACFQQy7KyQ+Yc7qp2oGZwRNmODroW1vIyMPTCOhhQsdlwfmgrYxqygAGxkO9omFZjoM70Hn/7hT7XaoS/hCRQ3L9Q2Zcw30QYmvFBqzbqP84uCLMkFMc3Uvjf7xljGNAILeL60JD2csGm9Kz79QYqqPgZBPli39xmmzYbvOz/w8zuRFdmwE7YqANQvBBef0R1gih3ATpNkkhThz6fVaHlnPAfnzrX+8tpKoANOqSM8LSeyfFi/7Q7/vyCOigvgIislahS7b8R9Epcvv+VcW+MU1lEuSlYLURx0X92rXX62p6KMsfvrILrVrDERFbfo52fCYIkHrbZCrpZy+HVkgsyG9rb5220dNIY7K4xJliC0yWQ29VlNk/UDZDArsEGNWwKqg6IT35JSrzaFXvpmUA9XnpWuLbI+TgsIPIT8S/wriO0evBTbMfIcEPvEJKAbOgdJDVAmdASVK9TAjJRBbNELyGLYQfgmjNZXXh4gXBX9zDKXe9RKNxCwZXZgLC4gzcZkxgnc6iKGwHFPsuQpqYTnQNknOtOuoynVOtQRtn/8nz9GZSVNhG2J3uiqItUGxSmrlBc8KJhJhNZOg+1gt+E+1pgTc7PuAtJaREufudCk0j3hw==',  // pragma: allowlist secret
  client_secret: 'AgA3oUU8B3WB8zMg+Nusy6MDs9aw6Mk2V+QkGJb7CebgnIwrCI9fr84VM6O7dQIiisr5RwZ/FqCCIGTWPPOkOqfasO81lVWElqqUEMVclfnPLf4Idcud+1nWHMCax+pZQ58vA94ovGrwQcQfB7BXT1EVLxusdubTM2InGFZvXXwHyeYwcT2EfWp+mGS9TLekC9uiidKsvJt7w01n2Bn09tdwpRK3mO19tsyT1Zi/qtKkzfe6PtIVoSdu+Qyph82PiWFdAnFAcRiuT705SY9aaUxlSgvneuQfDH238cUflWDkSX89k99XNbGTLCVMcQvcHPQnt2p5l9LVY4DxF0DKwJ+7irRlbHLkjH/LNcCSyfrwHF4mCKIQ/O4jEGQ/KbMzAHjBxC2QT5CoF18t835DXVQE/FKZfXiBZr9WfCdQyfNV3bm9AUqluaJElxnDANSDp/IjDfSnKGJNep+PuNeLXhuTrsVnYNI/VqRq6Iz0+ZAdbitOOhjktamiTwzM7+l2wR5bJUxqhs2Pkv7W0WDEFnrv5QTvCp1J7/WABbtLoEpbidEbKBAEhEc4xW/HNMgSxZivvvvTUJ+in9LrT1Sbjx9iL04FraiFlQW16KbW0NlhMDKxJ9XsxnShZDhODS0FdMjwwvPDo/ITRHfC32vPVilGgNhWcdsQBKc0bTlQ78kHtyv1wPmAsk0DYSNAY9r5+w4HmvgHi9TazGucq62idjDwy8ZOjhGvnemRu3qeR6gJPqqElq4CDodb',  // pragma: allowlist secret
};

local githubPrivateKeyInfo = {
  name: 'dev-aswin-processor-private-key',
  version: 1,
};

local githubOauthSecretOverride = {
  name: 'dev-aswin-github-processor-oauth-app-secret',
  version: 'latest',
};

// Slack secrets for Aswin-Augment-Dev.
// Setup instructions: https://www.notion.so/Setting-up-a-dev-Slack-app-111bba10175a804b81afcab1cc4e4a62
local slackSealedSecrets = {
  basic_creds: 'AgAjqCIfflknUkaX0tDiD156+TM++3uU8QXP5AXH5T14H1f6P+h+MDysDQN3OYxjQrszysxCysWTQpXfToje4tqWSoFQPbd+BJ57Vbf1VZTSJOidu1wye6OhECgvJjb8Kh+hLGYCuhqM9Tixt4SZ4v3LQ2DPvISCwOr5z5U4PTFtD4TIhTEmjhAW+DxXVyqO7H9znyF/bXFfG8jRSkl/K9YWSngKuQgN9XTgSv0+XqGZGELZvkin6IcV8Iee3K/O9OwXBADiU+YDA/oirmjko7UbEujPBp3dQWURzF4p3gdNxhXKqw00OshGqAECK/+8Nwxllhe7uDQjE2BX6dOanxPrT9k/T0ZRGJseACz/tvAW/j4joWUQszgh53VcmAGPcaqiW+n2iw/ozi0GN44FjME1xEZO2r7jcK7naVJtWMNEpcwiUDiMTZ+9kXxrU7DrgiykNtoywXFVshzBEk69CEUeTag24fIBICcSB74d0fK1nbiyr7ALlusjsQWqgs9+kvm5Jj0e1BUSmfWd9rZ4fSk+3WhKmAcaU8WRCurWziMbo4Dgnnb5UbWcPlI4i4znT1l1d0s6Q2bV+EjXKwFDz/T6U77sxC+wV5vzEeqFRbfUW3AFzfjd2kTFsMv6aRBQeYea9rwhz4vmFaZnMd5emkRHBZpeSrVGnfJ0gU0T/+yvPN2aDXDtg2YJGYMcQHY/0qpJIjiq9oSEFusD0xX9PZAAURcCkzkFzbTgwf6N1dD7dLskq7+sMucMRROdke3aeQzgX92GMNTINUEuhTB5x7fjhqo6rp4fB8KL9vTvafjenQ==',  // pragma: allowlist secret
  signing_secret: 'AgCiDqck6MDK5Kah0Puv4mG4ABJyXi0GoAL0UxQHL2XHjlIDC/xsFx0anub5VkCJZ0lrj6iuLblFAchC7Pa4Jq/8tqPwV0657zpcB3itzEZdGYr4oeMb0BXH93aVCw9V8maYZolXLtKsZGMx/ELBPwaGuotaVmf7R3TeBBIXKOKRhx6vEz6fSWKLAzgnTdwbPWiRCuoM6WvS3MYGD6Rjg4w21Dh0iH9he5ZW8Ly5TQo0MuiMyhn/7rvjm4GeRKoJDDN9Muc4gH0yrQG/7vwRg0ZstSP7ekejb54FRSfsIffPxoir5+uKSsWPvccmbKyYqPqmoHRfl6W5K0nw0P5QoW1DXoTQFHESUBKf839oMFo2rv6kWu0cf3C1h3qqsgFZPrvkYSr4ZVV0r70Y8Xko1rdwFBJxoi79x39kPr1jNjQ6RHNAGiseuPy5aTKsrzuOLsZDvs8x8rVi5E4ItfNPiJpCuAdTSxaha/4iYKM/s7SI8HFfb9qXGQ1c9SJjzxeoh5cHsuG6LQld+1a+cNc5LjQ/zCNp55e+sx0uZhF6S+b9cNGpl+6ztskzXTk/VBxFq68QiFtfQbaDNzoX2/fRBJgx110uGLyeSPyeKFqwPuIx37f8ymBnGYqGx7/avyvF+paavT6e9XItO8iuvtM+jiewpJ3Te0yFtdAU3deFFvx1y26o+DMpjZatX02iyt1p85FW1+wDWU9QY4cdqfHgEm8YVT/9wSPJ50o/DhrXId/GMw==',  // pragma: allowlist secret
  token: 'AgCLLYMhtl6YA4Z+24zQ3AlQ43YXSm02DdwewGKX9PFp0qQ2XJA4spdRcdfFHqNN2clMx+iUYLUWj1czs5CpR41r5J/AE9M/5oQsUVX3XTkiplULu2JpCYrNKIkTvbPAXj1NIZpBq+HQEOuFr4SPX3QeUNQz+RhT0kIVIslwEQjVa736u41/m8NTJkH1g1BS0B3625COdtVKhyOF4l51Jgsk2bNjcJQaTAjyBNmhPY9HcItAQl5ZDFvIi2iaxEG467Xde/6mPTFUkDInkOvzex8S0W/2s58EpWUxqY7ZXw8gl7jpHq8hX4Iq5KOwwQm1Cr48PWGbZ8p8TRHxFjDLmwcDDNb9oms/6BwFTLCvOXnbnesjWuSA0ZItD4rZiej4lzpUDvlUkkaY9aGTnvV9tLqt3N6Ju5LJoMSKbd2PAfQIGGWNm8o9zdA1BtOJkSvk/jtScFHjQXTiiPQXwDIpUM7c4oYCj+0VvgCfoan7xs5sKYyBFnf0Lu7ipQdEHLhcNSnFiBVUlYSwgw2a06BWBkVY+/do4TbTylETHPy8uha5hBGzGZJ/Or0un//GWEf5ViblDd+VJuYxYlxZykuGAr5xDsY9HRzlQmhMwvzM2u+3nnQAyPErmO4Cpzeiu03S2zNyfMPcg5SpJueYgAhhkShui/NrEV/XDxwNfINzlhSxWUF8hHVxhU43phPek/G8uzlytK5NGC+hO/Droqiqm5qa5Vxjpk/4mh51Z5v8853EvWdvCxu62btkqWQNe46UB//POOuEU6WmSio=',  // pragma: allowlist secret
};

config + {
  flags: config.flags
         .override('useSharedDevRequestInsightBigquery', false)
         .override('exportFullData', true)
         //  .override('githubSealedSecrets', githubSealedSecrets)
         //  .override('githubPrivateKeyInfo', githubPrivateKeyInfo)
         //  .override('githubOauthSecretOverride', githubOauthSecretOverride)
         //  .override('slackSealedSecrets', slackSealedSecrets)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    // For slackbot
    // chat_raw_output_model: 'claude-sonnet-3-5-16k-v4-chat',
    slackbot_select_repo_context: true,
    enable_glean: true,
  })
         .override('exportAnnotations', {
    enabled: true,
    webhook_url: '',
  }),
}
