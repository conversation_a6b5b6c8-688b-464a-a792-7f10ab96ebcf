local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

// Slack secrets for Sophie-Augment-Dev
local slackSealedSecrets = {
  basic_creds: 'AgCG6D/zrQjIXNetITmqyn698t8cid7tNBmbKhznqAcjV585fsudi2vaw0RChD9LvaUE/JKgqwLNfMEUUo1YvEBTsIsBDiIMY+UiXg5EH/xEx+Hi2QzgtFTuwu2Dh72BupX1PKZaywnSoOxUdeOeDYUgzlmvrOZC++ndGUQH2I1dVOtYGpp1Ad86nTwrLnS7H90p5445N+CMXCVRRe4lwovRlzO4JNsxZB3/b8rVc6pwNiwrU5VF2rbxQ9AnSDSmcb3VIFnEk/+eSsP5r4ikE+Qw10cuYMPiC+T9B/vWBTkPSLCwlJ4vyaW+k4P1FbvujjrvVS7fcojh6QVg8m6oLDl5srrtytZ3XXqiPBwXj+PaFkwoJ2jc2xJpasYUMqBlN0Jdo01LvVWiDK06K+buC6jp2H2bl3Xw75SdayP66djJp525EzGuPZxJX2un4pkBRT7LpAruUh6v8OX+KBit4RJTzKkIJDbTTlDC7FTy5uW6RKKFmP8MiR8gKQZ1gEv7Uw7bsnP9vXn8vP/A1uJa+5TcExf7KB3jzCHYUJ05+srwkl51PURECsE6oW6gah62wvBoVN15TMD+DRMq0xxXPVbTihF1eUP3y8kJMMO3zp+dbM69CmOJ8GWH3TF+bEhvfR9RDPRTSCIg6ysWz6iabDRJnW9kD2STwQEeD/Au7GOEaNhxtY8tCCDGLX05ZjNNvPIBiifceLdkbUZI1/8l0PzGLICKcilpV+rHsxDhhROf8q/x3yC62DvHa2PNBaYGKkMGsGk445AalZPGxkVZ8xukr5NvVHwfEtFe+SnNh1UImw==',  // pragma: allowlist secret
  signing_secret: 'AgACh41tNHw41OulDuWTF1BriMe0SfG1U6z7MtQ4vsAnH9tLgMooNGZA9xM+0Rq31z0ZeC/eaN5gQJS7lcC4b5E1QEWkHof84TlmGlesFXW9kSmjK+W5gA/kzmFZXDnxQ1rZRv/qDwZcL4vHp7uIDcKChYm7N1wMEA8XismrdySBxWPPloPikTSjU4uosjpaSwf9J/wWg+S1aFSDh51UW24V5NK8+PFmWMwrnb4+nKRbrav2Qs7ZYrHzgcy86Yezu9jESyDh8DhiOu1J3b0K4JfuwybcgTGGhitIKXuA1Jzb5Uqz0sVXYL61EjGRDWMo6WIUtGDPmN7wcs/IlyrSoWN5ToRlz8B9wtTvTD9eDMMr2HKnxuiR4qgXva8aWrh0nB4TSoW2LO8RT5Q3o2jfyzUM9QrQXaga50Gkii3hlqDSJZev7nV8EzEgEzVgfK7uuZtL3ypl7YEt+BDDGwFPAp6Yj3qMfV1cJT4afr+r8I+vXJ+KmyGJcqqo3IWhRT4UNHupPt3CbaVwqnOWCUBn8qf/CsGBLlno1G/m1334fquB0zCPAXDrX+EilHeOBVJ1jPmAEhXwxZBCSNoLkhPRvQJtXlyfx/WGn3FNuZPlCgApufXC/W5x78iyFzXOuL1ahKfU2HGA8ndZFcHrX5kw1gkcMNR/ko0veh/rbR9o86yhHEGhcevmUawR7YFbjbdaW+Zd/Xu0VzRb3z1gXMVQK+lcvEBP9ZC6sPZrenM5Wgk3gw==',  // pragma: allowlist secret
  token: 'AgBWg8gYjUCbySpRJXQgfm0/l8gMGpJY8oJp6wjCZubzCCxCDTtcbkfWqTDQn8IoqDPlYzKXUEvrVrcNpOInI1hjzZbSnZk5gj8krdts2fJddbKYqxAjo2aE52AYE5EyNE3SI2qq9NNurDFsOcu57NEqOlXbxyJTbtp3cMHNZRrLp7OWO9mTCRxi3guWzdUl8L+hDRPtTbkVIFsH9lPPYCgbMgtYnTmrjdaKqyqSj/cDw78JPmsfVN7M2oHUQxbarTHPaS1u7GA11FC/oyBB0+/w4USUPFeBXT43w9BsYq94FvBkd1Hji4mVWN32iqq8j358IwA2lxphv2NspfbPUTQ12/ZHEmfkH+NVX06pEXZlJaz44EJIyX44TForL45IvQFBWmMRLw2/uuXweUXT0FHHWeprRhjZkHSSMh9NGTUl+0Nq5nMlAIyzE4Q0t3izuVW6g1PH78rHFANxNolaO0t5U2IsNBvyjZ+AqsaTPAKBYsKFzUlPPclCfHInR+ONDLBhLMxh3fajek0ytIWuLSVJQRgawu4dpDM/+ga8ITOXNTDuBjGELALCRbxOhTSzMK05vL1KIrvrN6eCpX/xt+iuCeRASkp519tE1m3GsGD9/wKy3IalqYczDf9ihe8YuizPPbFprQo7ylhsv+2OwPO/2RbgGJByfeI7t6Wndzr1THOiuIrbRJAjA4h/doqMEY68xRmf+m1iYSJTZAey1JmApfo0aXiSugRbPe4+bJfnGCjr5Z/2HJIfAlXlc3j0H2hw8hT0ms44JoA=',  // pragma: allowlist secret
};

// Github secrets
local githubSealedSecrets = {
  app_id: 'AgCvw224Rr2NALGyFmGN9AAhsYsF8n69fzzMG7sAwWsUTh749IjhBhAN3cZBDwb/wOeCoynqrUF0dkHFdKcyTFnuWtbzY8RFnl95TVXj4KiYcXaoMlqTx0Ba4nFXkwZGE9yooxOdKkmiR0kCFHY54PKbJysvdGUHNLpOmpemrmkLFe/SyI41zNF/JEGAAzswCCwyp9El9pawIYeDFcm9/u/gfPtmu+6w3ynTlM3yRXxcq5usIUxuDFG6ZDu7O/PZitOLN5WKwMgkvR8QUNlvybuSLb92jgSp3ielDDbJ4tNmMyqsSi7RfT2HJ01eDb4Nt0DnYXDl25hQMHiXKEW8S4qcNBAK8l99ij+XPvWB6cBaBFv/zFcM6/52nJmIko+JJ/DvjsC0Y1G9mkOgvquI2AtSBNiJQM0+1wd/ms46Pfj8q5zYlKL2t8cVg1Wxtfj316Qh0BZdwhOVRbQnygkabBSrzm0yfuFMUI8AkT0/6Zlt5EGl/dXp6GcmVDCFARIWNOP3/Vu/Y76GtEFxdm9U1sAAnL+L0gLlyJlNoZlDYWU1WmF8Y2KD+xsIYn1orLjvbtbSK0ZNJGfC4vBiv56ii4MtL5KeU3zwFV0G8yO833EM2dbWYfjP7+12Xre+SVHmKZLeOCf7zEvUzyC0DPH/wNGgERY+7Kh/532rwjWSo8CntbcJJNsuGfLNS+51JEMqzY1rpt6U9/yQ',  // pragma: allowlist secret
  client_id: 'AgCPfwrqpKrYrUrxjV25ytz8MbqRPE5daNlhlWLykXImNQR62d7KMWpQApBAW4khYutLdTydfkq5xRnNPp5JDXCPw0oTC2C3uu1xi2dzjACf1N/hV2BdTSP7B5yfl/CYcpS9lCtKdnZGzB/wlZkrRVnMtcFAWmqwW1VLwxFAiRRLWnEKe/NEj3OF9luhX/xAGN6HKdEkCGNB0jJxKgJTBn3+TTGAE98uhmqnTn4LXEqqFSX3x7pGEQylNalSdpHBvFEBQPfDM4kXThVP+TgRaXoCxkGNOqNCsfk4Xewyhf3EV/5eiD+rLJlGzy/u6XivUXFe5rMjhaT6odsb1vvPVAdEf/VGynsIrtSdjTAP1pV19jqT1lG/LBkFN/W0jzGFn4jf5zS5WXTXFXiGVx67WB7M5NPez09as28OhGZewKIz0lZCiGYBzJofV3+2SLBmm5Z9JTsF4HSNrLGotDwcWqKSWmFajN3dVQhT6bSOFRY+/8Qz9fMxfi0eeJl3b/k821YnsZ8/5K/5yKqNDIoZBwgGNSGqdTClNHNk8dfPjID2rE9lM6MFMoXsvgP1MRFaIuyXIks43VRerciutWzy7S6vD0VB6nhIbQmsAMGkhKclBEGWti3x1zIXGEzG+22YE/ci4361eprdtVzgcZNYrgVVMOlMr2pr5XLtq+eKHNkHyIjPDbIVkk0Voy59fa5xxCAPhkK7o62yLu/SSrG5nhOhtVsEGw==',  // pragma: allowlist secret
  client_secret: 'AgBDIAqKPCphaPCG+LcokKBXXcg+BbmewlKlkL8JxkuH/JMq+llws0gqeMPz6l06D7ZY1P990g/pR9WFRUAeVNd6wS469bYqC47LxDJsBqewi9BX1Hymb+zOTdqInLOav2+6sk5GMS2thKzGfO+Crb5aBXg8Z85I7oMpzlX/XKdSfp+wasHF20DMy486DWzBjaW3BRA/fqYwfWWDuIYW0a5r2ap11RSEF3Lq109vMSmOWHCh3nvZIBFCu1NMfNHH9P+uHb7/7s61noRGk2UnEO/e2vM/jSYdwGDL690XWtAnpNBf8T1dvwp+Gb+rrwu6+jMCFwM7jZzj8zdwBrVTIt1krPchKYbrlyerlZ9xk13yoC51KHX2I64gi5ZpyZChQYcMBSFVrjJ+bBpn+2BGFAU2TuDvzziosXUMHGMAz0yS6/KGIyj4F1H9HGEhYWNyuY7V5ywMqL4X8fjlE9QVvF+JP90Cx0tkE5mSefHGFpAcTKB8lEaX2ZxYdAh6BfG8LL/tjA6vHvfecVDuPKpE3Q/T/1FLubI3XfWoifxh0pGdUXiLtUmol+/UOeyRh04COKUGM8cl3VY6R2DVd5wYQgTztXQciwaFkM+pWa+/nBpXn60rL0KlyKWS1azMpdO6wnvtTcd/bGpZOk05n/Yv/gQx2eTFqmM6XuvRUEU5VKi3bGQpMjUReMCyy/NJzNuKMBzEXWgv4eARGf5zqxrKAdpCITzTE+Nz6qa8uXfnDi43R9nzPyxGVC6l',  // pragma: allowlist secret
};

local githubPrivateKeyInfo = {
  name: 'dev-github-processor-private-key',
  version: 7,
};

local linearOauthClientIdOverride = {
  name: 'dev-sophie-linear-client-id',
  version: 'latest',
};

local linearOauthClientSecretOverride = {
  name: 'dev-sophie-linear-client-secret',
  version: 'latest',
};

local orbWebhookSecretOverride = {
  name: 'dev-billing-webhook-signing-secret-dev-sophie',
  version: 'latest',
};

config + {
  flags: config.flags
         .override('slackSealedSecrets', slackSealedSecrets)
         .override('githubSealedSecrets', githubSealedSecrets)
         .override('githubPrivateKeyInfo', githubPrivateKeyInfo)
         .override('linearOauthClientIdOverride', linearOauthClientIdOverride)
         .override('linearOauthClientSecretOverride', linearOauthClientSecretOverride)
         .override('orbWebhookSecretOverride', orbWebhookSecretOverride)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    slackbot_select_repo_context: true,
    auth_central_enable_stripe_event_processor: true,
    auth_central_enable_billing_event_processor: true,
  }),
}
