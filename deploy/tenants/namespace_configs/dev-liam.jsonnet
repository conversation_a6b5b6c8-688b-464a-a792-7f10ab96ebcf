local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

local orbWebhookSecretOverride = {
  name: 'dev-billing-webhook-orb-signing-secret-dev-liam',
  version: 'latest',
};

config + {
  flags: config.flags
         .override('forceMtls', false)
         .override('useSharedDevRequestInsightBigquery', false)
         .override('orbWebhookSecretOverride', orbWebhookSecretOverride)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    auth_enable_team_invitation_email: true,
    auth_central_login_invitations_enabled: true,
    auth_central_enable_stripe_event_processor: true,
    auth_central_enable_billing_event_processor: true,
    customer_ui_content_deletion_enabled: true,
    auth_central_react_frontend: false,
    auth_central_implicit_tos_acceptance: true,
  }),
}
