local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags.override('useFakeSlack', true)
         .override('testNamespace', 'test-dirk')
         .override('workingSetEndpoint', 'working-set-svc:50051')
         .override('embeddingsSearchEndpoint', 'embeddings-search-shadow-svc:50051')
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
  }),
  defaultTenantFlags: config.defaultTenantFlags.override('supportAccessControl', false),
}
