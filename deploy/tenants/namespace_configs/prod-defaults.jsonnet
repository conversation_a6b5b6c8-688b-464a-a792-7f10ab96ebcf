// Configuration for all prod namespaces that do not have their own file.
// Prod namespaces that have their own file should import this file.
local flags = import 'deploy/tenants/namespace_configs/flags.jsonnet';

{
  api_tokens: [],
  flags: flags.override('userTier', 'ENTERPRISE_TIER')
         .override('bigtableProxyMinReplicas', 2)
         .override('bigtableProxyMaxReplicas', 16)
         .override('workingSetEndpoint', 'working-set-svc:50051'),
}
