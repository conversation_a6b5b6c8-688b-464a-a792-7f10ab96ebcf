local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         // creating and deleting ingresses takes a long time, so we disable them
         .override('loadBalancerType', '')
         .override('useFakeSlack', true)
         .override('passthroughBigtableProxy', false)
         .override('remoteAgentImageTag', '{namespace}')
         // Delete remote agents quickly to free up resources
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    remote_agents_enable_auto_delete: true,
    remote_agents_deletion_pending_period_hours: 0,
    remote_agents_scan_interval_seconds: 10,
  })
         // Use a fixed secret name for the Stripe webhook test
         .override('stripeWebhookSecretOverride', {
    name: 'dev-stripe-webhook-signing-secret-test',
    version: 'latest',
  }),
}
