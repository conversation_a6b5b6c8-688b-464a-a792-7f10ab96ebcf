load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_to_json_test",
)
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")
load("//tools/kubecfg:validate_namespace_config.bzl", "namespace_config_test")

kubecfg_library(
    name = "namespace-configs",
    srcs = glob(
        ["*.jsonnet"],
        exclude = ["test-*.jsonnet"],
    ) + ["test-defaults.jsonnet"],
    visibility = [
        "//base/python/k8s_test_helper:__subpackages__",
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":flags-lib",
        "//deploy/tenants:tenant_flags",
        "//deploy/tenants:tokens",
    ],
)

kubecfg_library(
    name = "flags-lib",
    srcs = ["flags-lib.jsonnet"],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_to_json_test(
    name = "test-flags-lib",
    src = "test-flags-lib.jsonnet",
    deps = [":flags-lib"],
)

jsonnet_to_json_test(
    name = "test-flags-lib-override-not-exist",
    src = "test-flags-lib-override-not-exist.jsonnet",
    error = 1,
    deps = [":flags-lib"],
)

jsonnet_to_json_test(
    name = "test-flags-lib-override-type-failure",
    src = "test-flags-lib-override-type-failure.jsonnet",
    error = 1,
    deps = [":flags-lib"],
)

namespace_config_test(
    name = "namespace-configs_test",
    srcs = [":namespace-configs"],
)
