local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

// Slack secrets for Jacqueline-Augment-Dev.
local slackSealedSecrets = {
  basic_creds: 'AgAlFM5iwrYF6LR+mL9+2vxd6OYzndl+9U34cpyomBXhMoPblUXVORZvnwqSRq232jUDYkLVkVE3FC1WKv1UijzQ6WUZrGfgiLOBe+i68EqLGgUTZ4f3QZvULRc52MUQeBAHP2Fw9HG+TpIoxpVSS+/x72yofM9bgFxecaeRMHozjZChe+W+X1CTOjF+epSWM9iEJyKoYCb83yiqh5gQQpdV2w+nm3kg18zLPwRj8Z5GyfslxeZFeQne14GGSXD7Z6PPJtUnXSLjzKG0xR1M22TAcsAY3FyIU++qBH5odPyhXnOe5gOENUe0IVFFKM9QS6JxeoTpg/zt3VLF6D2e0pF/29SA5yRfw3r6lg1GsE3vCp11egAmaIsvYIyjoul9TbMV9CUswjHkVIzB/3IFEia+nqsf384JooJhzf7KB7qvrqzqUE3UjFBcjEE9JsWDReJlE9byGEGkE8QTEi/hlcnFQCbwb0Fl8x2+qV+FLQ3L4VX35JeVq1Py7Vos5wRXxmKFBKBAkwasx7Uw6sDKcz/My3bN3RAg1PO1/LcY9b0s0QGyZjhQ0RJocNPfPclVCdwpmqde3KE/rpYo0BYBKpsHhWUoHO1g9Y4rcvh3yDR/Eu8vJOerhYZqSoMovB1hdCLGCEdLvd1i/ZITWsOeFy9TBQffRpQhltMaXJFHLk5N5EsZK2ISjxaF/OkP9aYWA6+ajvHsSsGG2cEbMy3HsTZFQFJOim/XO4CxPN8vo1aINn67yhIbAAX6gqU6WgB9aqU61aVkAtGTw9GZNB6FMNAhOoeXpD0YOBXdTT8tBgttiw==',  // pragma: allowlist secret
  signing_secret: 'AgAm71bbIaUWb8X0+3V6h3VDeYmJ9nGGc/6FvpGUVRqClsct6bkMLRPsmWsLnJN4m0di+jSjrNj2YdvEPIOFrv2E31J0BJWUa0mV5X0W1XGFmY2NNWPzIF68yUjFPxbYWXbJyk8fnZ3H/XAWSsDmkwta+773cYVEgz83YE4BOBZENDGLimiiqS0W4kh1HWpNGL/o7R5XEg1/fCqxMSuBKhAxtDtndXUUBLJGm11G+C852Z9+QM55HBLPN+upm8HnSd7rJQ7wWF9nRCe0nXZyFTmq7Bxv/DUFoEFq66oJ95jDKavTBChHZUn2MeODwwv4eQc+Wy8dLqJ6isjR4UEmhWYdK1NcRS9kj6n9ZCCbWo6o22nm8AStP3JX0g1fjK6Rmc6nOSh0GIHtsZNJRashmKHe4p4OotVkpJInU8dLghJLE3l3ZdoROnMIzTkRVSVBi6jGQK6j0sLBlSnVRtn82e47uOFhePaKlOxf9CWQ9f95aSTB1ouyZaKfgIL1SaNVXt+DfofS/h9L5JHnuIolZbhMdwdZiN/g2uqkYxNe8iz+yE/etVHEwcLYUMAQZBIz7uzoX8+B6TRC1q8s898/DiRY/zLQDNB9VfIOyg204WE7pQhMVgYADNQn8x9KQYD9I4PhELapInZcJwcJ+R9Kgs9a61PqyFAcaUkYmBozxLhGC+H/Uv0DdibWvgSL+LakbsXuYD1HhXUTp+lrFJ5O6zWbRBSSn1Gt1vafLMPpGcNe5w==',  // pragma: allowlist secret
  token: 'AgAMdBSrpXsCHQ4bckpVmBt5Fb9byhooQh2JqGDzZQnsIRkq4k8GoFmx3LYVAK20Qkh8w06ntJILx31PEl8Pw/IHB90yR1c45ogCtAWB5Im/X8SBoRKoQinZNwZ0q7XqKVRlB4l8Ut77lQxIuVYC6s8QY+fnpZVYlvNZas9Y79y94adg9itJQRi2am6o75iFzYlo0kryV1Y4S/3jTWZeIQ7vqmWaddSxI2LS5EjUzWzcJou+LUFk/YSUnASfkyg6qf7ablHUnItCYxioRi1S81voa25LaeUeEcedG+dhvM532a6YZdwtCAAnEt+Cmao9dDQ6ocYlsPKN02YMAvoIRAEaozYDq0XD3xdiMPswpgUBdw1RaBmGiLbePHTpcSt8OMRcFuka33ZOLVAHXPcQzk6mGUpT6UyI4k05gKZSzYYJSe/0cmBXcloAH6+3daHN10eon9zub50SqVRQAFSXP/MnxYAU00UbJWOyz1gwESYYgzUKREktIE0wOHn2nZyUOGgiT1SQ2sfiVoeDFhJtK5Ttwmktbh+lJ1d1dexiZohNTY8GEFoqqHWnfkpKjwtvBmRZiVEPz1A/2zx/IBRhWGg++m/SkLUTRwso2WNEpLzYB6XvMMvufE4C2lurhbuE7YFaahY9aPZoi0PWaNx/cwqbAqnbWlJuagolF0coXfg3/NC2DjInm9hWbClgVYWY3SG2u9mH3ZYwpkWLpmE2O7xIgigyEvJZsJ2XHNXIz4K1LvR0Kfwu69fX8V1sYZqaSPHmUYKFa8H/5vM=',  // pragma: allowlist secret
};

config + {
  flags: config.flags
         .override('slackSealedSecrets', slackSealedSecrets)
         .override('enforcePIIPolicyTags', true),
}
