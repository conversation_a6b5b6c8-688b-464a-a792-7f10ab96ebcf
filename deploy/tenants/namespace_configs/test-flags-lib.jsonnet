local flags_lib = import 'deploy/tenants/namespace_configs/flags-lib.jsonnet';

assert flags_lib.check_flags({}) == [];
assert flags_lib.check_flags({ a: 1 }) == ['a_ is missing'];
assert flags_lib.check_flags({ a_: 1 }) == ['a is missing'] : flags_lib.check_flags({ a_: 1 });
assert flags_lib.check_flags({ a: 1, a_: 2 }) == ['a_ is not an object'] : flags_lib.check_flags({ a: 1, a_: 2 });
assert flags_lib.check_flags({ a: 1, a_: {} }) == ['a_.date is missing', 'a_.description is missing', 'a_.owner is missing'] : flags_lib.check_flags({ a: 1, a_: {} });

assert flags_lib.override({ a: 1 }, 'a', 2) == { a: 2 };

{
}
