local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         .override(
    'extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
      chat_return_raw_response: true,
      vscode_chat_with_tools_min_version: '0.0.0',
      vscode_agent_mode_min_version: '0.0.0',
      vscode_agent_edit_tool: 'str_replace_editor_tool',
    }
  ),
}
