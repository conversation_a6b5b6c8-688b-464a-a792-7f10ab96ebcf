local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         .override('exportFullData', true)
         .override('useSharedDevRequestInsightBigquery', false)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    auth_central_enable_stripe_event_processor: true,
    auth_central_enable_billing_event_processor: true,
  }),
}
