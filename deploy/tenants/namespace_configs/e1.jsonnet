local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

config + {
  api_tokens: [],
  flags: config.flags
         .override('migratedTenantIds', ['b484699b72ee1b1a5171c26784ece82a', 'aace89b17f627cb1a896d192fe2b65d2'])
         .override('passthroughBigtableProxy', false)
         .override('bigtableProxyMinReplicas', 4)
         .override('usePremiumCpuHighmem', true)  // Give embeddings search extra memory
         .override('deployGlean', true),  // deploying glean to e1 so we can have Collectors try it out
}
