local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config
.withDeployFlags({
  useSharedDevRequestInsightBigquery: false,
  exportFullData: true,
})
.withFakeFeatureFlags({
  remote_agents_scan_interval_seconds: 60,  // Run the cron job more often than the default (5 minutes)
  remote_agents_auto_pause_soft_ttl_minutes: 3,  // Auto-pause earlier than the default (15 minutes)
})
