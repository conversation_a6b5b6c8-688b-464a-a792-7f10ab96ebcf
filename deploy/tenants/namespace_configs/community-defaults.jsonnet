local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

config + {
  api_tokens: [],
  flags: config.flags
         .override('enableClientDataCollection', true)  // upload client events for all tenants
         .override('exportFullData', true)  // the data of all tenants in the namespace is exported
         .override('passthroughBigtableProxy', false)
         .override('useBigEmbeddingsSearch', true)  // A medium sized repo is 20k files. 200 users -> pstg size.
         .override('enablePartitionedEmbeddingsSearch', true)
         .override('usePremiumCpuHighmem', true)  // Give embeddings search extra memory
         .override('deployGlean', false)  // Glean is currently only intended for enterprise tenants.
         .override('embeddingsSearchPartitions', 4)  // Provide extra instances of embeddings search
         .override('bigtableProxyMinReplicas', 4)
         .override('userTier', 'COMMUNITY_TIER')
         .override('authQueryTokenCacheTtlSecs', 300)  // reducing auth query cache ttl to 5 minutes so we fetch user's subscription status more frequently
         .override('enableChatStreamPayloadTimeout', true)
         .override('deployCommitRetrieval', true)
         .override('chatHostReplicaScale', 2.0),
}
