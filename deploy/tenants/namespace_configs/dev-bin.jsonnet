local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  api_tokens: config.api_tokens + [
    {
      user_id: 'bin-vanguard',
      token_sha256: 'f31ce5b931fd8a7e57896f068ff59c5d1963cf52533f11bf27f8fe2e36cddce6',  // pragma: allowlist secret
      tenant_name: 'vanguard0',
    },
  ],
  flags: config.flags.override(
    'extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
      auth_central_enable_billing_event_processor: true,
      auth_enable_team_invitation_email: true,
    }
  ).override('exportFullData', true)
         .override('useSharedDevRequestInsightBigquery', false)
         .override('enableBillingPlanValidationAndCreation', true)
         .override('exportAnnotations', {
    enabled: true,
    webhook_url: '',
  }),
}
