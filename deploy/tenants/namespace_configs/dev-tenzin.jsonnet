local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

// Setup instructions: https://www.notion.so/Setting-up-a-dev-Github-app-11cbba10175a8030af17de8f140724eb
local githubSealedSecrets = {
  app_id: 'AgALhMNoF8Uh8nN9FP0zTYpai32Kx+xu1ycBPoCD12oVUkW4Cn0eROCHC8vUqHyRl8x6RPRHbNhnQpBhQc622YgvrJN1VMTKDxnYcdw/IFc7BlMH6LgRrdNPWbGsDSok5gE2mTPqi2CTT5IKxm3XqmglUroARDoFvku3Ize4QjXzS2yCTw739d35iRe1LQ6IK6mYQ85xhCvi8ZPV7mBsuigFwpfh/At5S6MKJSic0vryF3iufeC0ViV0L2XM/IGPgIK77/XJIV0ABL2uJVsA1fMRCVCbX8g0M6iznnk/Mkv38/DcbCDVpkWupbCXhQzRiNFJb3zGJCpSpPyQkVF97ZVUzO+euU5nzClXJsO2LARVUN6/IaP+kFFJB09aYo2houeHAc+ojV4hdjAfS7aYkXazgSbPXCNdTB+GstdeWK+fBCtk1RKLEp2kzjZOI/IGjYlAGpHEINn7SPTK7/QIlkfGsuOK3XBmKu+ir3XAjq9XSMeLXUgRpe2r+1LtCwopYWkQqffeVe7xCCwmaLjo+oMXYA0x5MdI/k8YxaHcTKGMeXZOMlrEN9Q1TDBzC8luiORw4FCkET+4q6OpxlCzxShla2xb+I+S7V2LJOgfdF8+lhtWdeJ3CKNRUGhiVOt36rgZrO+Yf05pyOB3GEj30bBdh/GVlRRkEAxoYeVgLssl0Su8ziMHcHtwDhFKR75fr2p4Gke6mWV4',  // pragma: allowlist secret
  client_id: 'AgC/Fwp6RWx5HmfmhlZQxa0HOG5sRd/SrVOPSZAZ3Hu5748U/WUy8H/YimLLmYJAXyZoY55SSg+lY1Nswlg11Wri7Mitas0dLJcu10j6r8DhqFspr1ODaMDE/8XFZmNZiIh0eCJruMBr9tDV13cmqS+6rXtxluohBsHsHsSpEBNaoJYbUy99H73H7VqDsko60BoMWBDRl5U9XQJfommebCuPk7uPxT9qwHf3Xx5cqpL6nTimylgSiidtUUADKLbahf99uKwhF6P1iQ9LPr/+9w3P7SW7UHgO/yNrlqcTy+px1iVElr4scljatqroW/2TLMjDVVBfyFKUtiZLa1BaQwtdQ0fxaobT6MFflD0tA90QDnPrGFc8OkTJkFodWw7Fgvd/LtlYeNIrN6/8XFjxzIZJLoTCFkmC2RLFPr8SMMKOQF+OIO4DVOH2T/+Sjt8EY6whz0Jgs+E6m46QdQ2OWkjcFW/SUGBOqnFwZkQeWOaBSDu+ZhWMbrAoMsVqPa0r0OiccQaRp1TYo0NEIRgNSxVSOpptcj9CJ8Cfujx2d0UHpR/Tkb2Zy7bR+/0oyMpekUW4prr1x3UazXHV2OyibcCgMto+ZiiCQAyDqRrmt776X6sFEvrhYn4v+90P+Eagfu7kYlOpCQVd6nWSb5u1GkHJRJEljIjekbBMWQ5+/zuvBUc7pQKFJml5FEJHXC+UX6uOseQaG9IzkrvIErZ77AMdy6y/2A==',  // pragma: allowlist secret
  client_secret: 'AgCCws2kaSg+VjvVoHLsLQUBMzDCqfpu2AopNoeGDlIgIW9osO2HEaYb5HmDvZRVExe/JkTkn5H6/kP160HZwlCIqgHb+j4cM9Qm2jS9yVCt/mN5whRt+7q4a0ikNUzjX8VVn16QOl5RZGxV9t725lj9UksAu2cIDUjq2xr3a6xKcBO1gydP6Dq96eJ1/FdrU7O+HblnxZ9fcbIHtHs7HGtxUXGxsrg866Sr9NsO8Q3zfxQBfuka3KHdkLYOPxmfO8u0CPjMwev4FYreFuPmcQN+IBZdruBsWHUzxnDY+4DhA4gRdq+o1bW9tmh1mNDhlug4Ehkd1nTXdOa+zji85iRDzIlH8/LmFo4a9KvzX6u7ZaGM+JxGOPCXO5iThdzjgSAemexGtjoHVyWqPcSfs1I35215vzOMK9YefxJhiNKf5YxvhbEcmBRoC1iKwKGpm+M1g9ODiSc0+oO2Z0wuWPEbMlx7JmZnB1wiAlox80tKzeyrl7qXGtoeOY8iisld/55/YDvqXCBaEnOaFOM26vdcbLmfNnapbtbl22NqyMHZbwdaTupsAY/vS15G0P3/KWkD1z5jjTPsiRpJDTE+pAbl5giRQ4jZLO9/M7bhD1urY8DcTauWwaYOm1Z5g7ZlMuc70n48CEctjAxtNKfWFVS47MvyUO+uGIZk0eXsE9rXNHoRH1HEWNVMaQLSYJIo/rOQBIFfmLw75MogobZDsWuagDYnjEpsTMOGoWYjYdjrfFo383ndaJXP',  // pragma: allowlist secret
};

local githubPrivateKeyInfo = {
  name: 'dev-tenzin-github-processor-private-key',
  version: 1,
};

// Setup insructions: https://www.notion.so/Setting-up-a-dev-Slack-app-111bba10175a804b81afcab1cc4e4a62
local slackSealedSecrets = {
  basic_creds: 'AgBSFo1GYYDJ3jf+Eo17TBuHG6qx2kdmpAJ0NF7YIaI3WcUihztW1ijUjNW2OJM2kjaKVxzHF14qlu3PV5rO60uq4fe67sI9C1I01VhY/Aov2wVqXfQN0FW9WFZ4hDHfBuznBsMryLn3aZ0DqDjWkQZ/DG02Nixo14OB4XMsKs99VBduid15CvM7aPPPbBO+xOGLeSvYJnqrBY9UsoztAYdh6KHrjQD0ST7PtjEol8qqI/qNJB9XHOUZil2LgOoutr/v9vGKML69dB/mdRo9xDlTJELMswLdAtih0HaBGbzAP1XNrKUcU4NasAI8Q3RiYUyE0+e/rx8xsjlbzpt0a//EUjS+1olLcEIlUqq+GpDmyH/ZSEEcHVcRJPPhtuiwpJirghlgYiftnWgJ/uzan9t16tdw09qlGxTynjVU21gM9bkZaXfEH0g5H5/iA2VaHlLnsMNzzy82Crm+npLMldnARGmb55PgHXjmc/ABc5pEDbdOv/9qsN3ZvvGTZe9RnKTs2Ahp8V2/W7OXl4Y3WTPaJUy6iSQ71URzYpX1Ui8jkCyVUilHKYUrJrB766pQKJsTj++BPnlCMwZS3KUjI32IUANO3wfzMS3jU7VUMmakTFe64DrCuxs1FMLvna04uiEEL73sPAUmb45WFK6t/VUTguJ3paAqJiCVWiO3QzuE9L2k/ZOx9BbSZF1dS6GykG/DtUtWSt3GK/h4nTP/bMf4IAfno5e1HCVnOsumZdqA6Dmt4kqK8mocf8nju9wZigSRcpa+hydbNJmfK+QJpaoFCOwNH5Zb3k8Ns0vtopUaIg==',  // pragma: allowlist secret
  signing_secret: 'AgBhBjAINRL6AQum0f9w8OGkC1Bp75uI/JxmW25LdPq83jkr+7Bp5AvL/1GSD48EHiFCA2swEVfCwAhjvdbC+xvYxAJikLVpibtqPX9XqA/imH3ZF7GfZC18V7IYht42O0o/x95y0gyknVLoRE1ijUN1pCYgaljg4Gz6VHYYU2OVqg1+gzxvFsrDntCI/roiH0LdJAFScoUoMhU6elGq6NuJPyCED03pehNZZ6t26JQ+a42arsa7ln32E0ChjN9c2Haer9WZOvzxIeXNpvw1W1pVXNntBxe+4jmj336BZonr+mHtBNp2PfDx/0i+GoDFSaEZ9QI1DeSSvG8R368kdW3ORcbYG+9R8E1WA7FQmLuv6IutMSs7GHVccCZ7VeGqytVREAVa0uHvfHjgsmSUh29wWv7Y5q4ZJ7w3F69x7qvUipr/OAOmIbYQu9bDSsKQ7y7Bl8RAjtydYQmn/UbB61dOi4htVfynM/Z+2OeW4UGDN5gaZxjOmpop3F2GbscHMcirlSbuVMlpVaF9FYmlVmw8oZBpXUdTw1RBoqHsdF5PZX0jtP6opuxAzAiW6dLF6+Mw98H+sLJW6CJtldWqpKx0qv7reQ7vj+pgJsmWxtmHyFxMkKfh8LkgPm2NvHV8IPXvn+6dl82YlAx0SGWAzU9abRx6b4NE5HKZQx5LOjbBZ/fon4hmbVOC/V1NXTpmiZdFMUBeO4qW/8jQjiLS0Lfqx2An2u6V73+uvayZrtthBA==',  // pragma: allowlist secret
  token: 'AgBX7eZSU3SxR5m/rXtJttICGlT/hpcoli9WNODwFemIFkXqFbvyOgh/bucU0ALnppIRWYFAUYpJjNj45o84yktzFiuhQX+qJ/rCUToFpvNZliizEaYu0tQUmtSpEu77feGEgRjx1ea9M0wJcrVdku1xsvjhD+DVMT9t1cQFSlSdEMK0hSjh7F2B1N0+/fSvsTHZSmZLKDycZH74H2zsN591hwjsC6JvWUjEE+Vu2cjHLunV+9aN0jcvozDPtshCJ8E8aXVkBhAeBBV4gEi9Ad2XynCx4s9rbzRYtsu8vQ71E4oEuzaw4NmsRNhKtf/gJE+QJXsJ1o9at5SESXAE/rwG2FSanFFc5H+eWsaeZrCaR6S90OxMQ3kfZRwux9HlFgEmDSOcFgV0TjiubH3BY4leO3SOd/yXwKYHWCJfW0k5qapsaJPtAmDa9rz7vy7hkIAbiSLyansbakAEeUFvKLYJtYlSTc1YcUbag1jBsyx2RD8jFdxsTavYbrIF0Yeij1+NSnBThEi7X0rxoVbnsTAas8YeMHGQj0r2nE395YANZwaArjrHOdIzbM29nPn1xy9ylmg1MkqteTWAKV1iRjyCINyzX0NaC1CepP3nBkCeE9j/PSXxC527eEFf/q65pNYL0xBSu6+IDWNfhyqkCtP2TTE4mtVTdgg7ZXVyyCToseOFREyV17Acc62e7RXQ2HvgciCba5s4oBJO+Z5/mDS8kt+tRlJpvRSt51C/uY9JfBkz48sTUANJMT/Bv7KHzjfXuAtntfuqsBc=',  // pragma: allowlist secret
};

local linearOauthClientIdOverride = {
  name: 'dev-tenzin-linear-client-id',
  version: 'latest',
};

local linearOauthClientSecretOverride = {
  name: 'dev-tenzin-linear-client-secret',
  version: 'latest',
};

local githubOauthSecretOverride = {
  name: 'dev-tenzin-github-processor-oauth-app-secret',
  version: 'latest',
};

config + {
  flags: config.flags
         .override('githubSealedSecrets', githubSealedSecrets)
         .override('githubPrivateKeyInfo', githubPrivateKeyInfo)
         .override('slackSealedSecrets', slackSealedSecrets)
         .override('linearOauthClientIdOverride', linearOauthClientIdOverride)
         .override('linearOauthClientSecretOverride', linearOauthClientSecretOverride)
         .override('githubOauthSecretOverride', githubOauthSecretOverride)
         .override('extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
    vscode_agent_mode_min_version: '0.0.0',
    vscode_design_system_rich_text_editor_min_version: '0.0.0',
    enable_glean: true,
    websearch_tool_safety: false,
    enable_model_registry: true,
    // Match default agent in staging as of 2025-05-14
    agent_chat_model: 'gemini2-5-pro-200k-v3-2-c4-p2-agent',
    model_registry: std.manifestJson({
      'Claude 3.7 Sonnet': 'claude-sonnet-3-7-200k-v3-c4-p2-agent',
      'Gemini 2.5 (v3-1) (03-25-2025)': 'gemini2-5-pro-200k-v3-1-c4-p2-agent',
      'Gemini 2.5 (v3-2) (DEFAULT) (05-06-2025)': 'gemini2-5-pro-200k-v3-2-c4-p2-agent',
    }),
  }),
}
