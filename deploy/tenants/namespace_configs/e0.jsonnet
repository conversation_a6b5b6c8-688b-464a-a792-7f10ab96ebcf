local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

config + {
  api_tokens: [],
  flags: config.flags
         .override('migratedTenantIds', ['b14a02ec6d2f2e8229f85595adecbc25'])
         .override('bigtableProxyMinReplicas', 4)
         .override('usePremiumCpuHighmem', true)  // Give embeddings search extra memory
         .override('passthroughBigtableProxy', false),
}
