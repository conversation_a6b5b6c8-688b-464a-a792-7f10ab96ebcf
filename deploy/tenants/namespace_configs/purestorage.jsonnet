local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

config + {
  flags: config.flags
         // 4x normal sizing and 2x normal replica count, to compensate for big repo (500k chunks) and handle usage spikes respectively.
         // Temporary configuration only; longer-term we will move to autoscaling and multi-tenancy.
         .override('useBigEmbeddingsSearch', true)
         .override('usePremiumCpuHighmem', true)  // Give embeddings search extra memory
         .override('embeddingsSearchReplicasOverride', 4),
}
