local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');


local githubSealedSecrets = {
  app_id: 'AgBehW0GupiiWx+u20HeQ8ILLpllELXZyN0Y8s2tUl7SbzOpCWyKaHence8/rIPrHEWUWB8chd8MRWA6ZiyF/1lOHtL/hr7ReihZAqv834eHkOtNc8mkx3AwusAfZCYEvOSpoMWxxPZyEuZ5MMJz/nYzMOVECwY823YuNOZxv4zK52XeTK/gXifW/NB6B4RIoGY8PvZNAFHY8YaRz35IXwsYXVOP4IbrKMX9G2Ozd77s7RAAHey/LBo8MDPq7MprNoYy4rZNKFc1FPBa+7SpTc6/GDYm864+ZD0+HMtXezi+wdqhPzEzZbRnRjFYax41QSYqoljDQ1hlFHIEppZler2NLELWiYHQiKAWdKSei65qWZwZBnml+cGVhsJmRwNHhtG96TsQgS2g8Cc6lW/sD0Vyi95qYSd/in/vPzVyvnO7ProlXtCMpRqUMMhY6v/vLDn9p2qZfrxVpGVLjE63tICN2asPBaaXkLBP6vp0OYC48mY4h+GKmQXFQWGHZf6S6kkpfGfCeJDuhJO2D2mHltZ/HdZvrth0NpfzydbQ0iibEEkPW4DxjENJlnxHiuBmkJ+0PIsL7QDgnD1/xpfQCVS7ESQYUBJSad0/vHDoPP021RoGKIwS8k3MFd0SqhxJ2awM0Oir1uC0I1Zfn6rTfOw30tRTjYoiSnN64OaUaaBxAeH35hHPgORRZ5yq97hTYV3EzwE1A0rs',  // pragma: allowlist secret
  client_id: 'AgBng6DMYHwz5LCMiSYmSvQBmN+HZoWlRJuARU+HDgTro2lEDguSjaHqOdmSnY+uNCVySVljCP8a+OsziSWFmkEeyAjoQBJLLbtaYApozdun+NIkZFHJ4Tx/EAaLKpPivIdrnSqZhLw1OekjQiA7ffmpGWSiWdJ7Tlix4jc9eMIjIFYxyCbTiFHaCir4diuTRozE5K5LBhXbg6y+QyIVqIezIcOgp8JKMN1MEraoxLhAWiRyxxkEY98zT1jCwJVCtTBGHpe8Tgbh//hPQMmQDW7m7mqy7pOTQlWEVsfk1y7sSJGXpC0wv8bact1mSzYc3iMAoDELKTbgDHuKZ2aWpbGChNlC5d6lvxoIU8dV/VBHNrcacnsCYzlzYVIH/qjuZSWslBeFU/j6clHiA4RFuwn3jdmgWDS5yDILQzqTxjz/VmZKyaq/1ZbBUIdl2WeI84CcTGQfGJ/teb7e2jJLsuvfX5RJAOssEvsXDkobOv27klwlNQC1B0S+6GCK34rvcjfkUz8kbgB/MxXItiIUHQc9AZyY9Ls9Glckd61JR+TYT0fmXgLtrt5wggS3Z7fDS2unSksfczlKNgWRJ6Ve7k8k/3fp1SZsV90fuhGPR3CHUXqUEmQJ9nsmilRc5Na35gfP+fATzak6Keq1+4Vx//NA0bS2Si4om66YBBQbYHtS6ay034DMGxNNVcu+dkNyDXFJ+N618CKfmIEhE86/VUvOnPICbg==',  // pragma: allowlist secret
  client_secret: 'AgABgwzN0ypCdrQ6s28mThIKwMgHwazf/r3+Jyid0bpztIcm0TL8yhNziOb7AHXpaAs98r7B/3AkpyNXVEn60V16TnDchaxMyOLpxHCa9OzOkzMuvZ0e8PC3nP0WVGJf+X+zLbteim+J57miGZQeRG9I6/TtL2OhIAZEJ5BGjJuYrWtuq0Cu7l+xhgSu/9FtJuit8h7LE4yCUInwjqZaqOYM+haxd6lcOkmpL0ybQFBq5BmMlbVM0aOdA9WqHp7T0JHChLzdfkY7DSIwNVX3mTTRelhvHo/4THws96+HpQafjCX9Knvc9K9dRaGjhgg9PpSZpNKNOMVzH1c4/sxAWjrGMSmeJLozIOqjNmelFhXB5UBhWwVRJCPOTvwJtr+qj0ObhkeiyIBZafv3a4vhLx+DaIXKUTdwlmt35SEiTLpLz0RpqJFX1XpH2Zhd2F4eybCA7QuHZqSKxs8VbBXHoZcFhu7nUs4jpr7lPBZ/g0036BYZsqBJz3FHsK+8kzx/HQllqAdy26jg7eoEfniukVaiyTCu2fiLHvgna0/h22T4ppKiBzrEmQye+LVlVk4LQyM8WsSoFggAaL8QXlCtfkZeFRE9yLgKVbJB0tBapQx1kxWt24fK0+m/8ETJp+1sLl9nCzxkzkq1/2CLg3w7fFuOjcRPWpP0kD+1P4CDwc3SqAX+V0bOWFFezitRiH245WxaLRuVIzDlvukEpOHAmCB6q/2QZlwc9KEjb61CVEjWEqonPBdTNakS',  // pragma: allowlist secret
};

local githubPrivateKeyInfo = {
  name: 'dev-surbhi-github-processor-private-key',
  version: 1,
};

local githubOauthSecretOverride = {
  name: 'dev-surbhi-github-processor-oauth-app-secret',
  version: 'latest',
};

local orbWebhookSecretOverride = {
  name: 'dev-billing-webhook-orb-signing-secret-dev-surbhi',
  version: 'latest',
};

config + {
  flags: config.flags
         .override('githubSealedSecrets', githubSealedSecrets)
         .override('githubPrivateKeyInfo', githubPrivateKeyInfo)
         .override('githubOauthSecretOverride', githubOauthSecretOverride)
         .override('authQueryTokenCacheTtlSecs', 5)
         .override('orbWebhookSecretOverride', orbWebhookSecretOverride)
         .override('useSharedDevRequestInsightBigquery', false)
         .override(
    'extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
      enable_glean: true,
      vscode_chat_with_tools_min_version: '0.0.0',
      vscode_agent_mode_min_version: '0.0.0',
      vscode_design_system_rich_text_editor_min_version: '0.0.0',
      auth_central_enable_stripe_event_processor: true,
      api_proxy_trial_expiration_days_threshold: 14,
      auth_central_enable_billing_event_processor: true,
      auth_central_promotion_recaptcha_threshold: 0.5,
      customer_ui_cursor_promotion_enabled: true,
      auth_central_cursor_promotion_enabled: true,
    }
  ),
  defaultTenantFlags: config.defaultTenantFlags.override('supportAccessControl', false),
}
