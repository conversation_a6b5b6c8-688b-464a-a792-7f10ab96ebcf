[Unit]
Description=Augment Training Job Runner
After=network.target
StartLimitIntervalSec=3600
StartLimitBurst=3

[Service]
Type=simple
Restart=always
RestartSec=1
User=ubuntu
ExecStart=/bin/bash -c "source /home/<USER>/anaconda3/condabin/activate pytorch_latest_p37; ~/augment/research/gpt-neox/jobs/poll.sh"
WorkingDirectory=/home/<USER>/augment/research/gpt-neox

[Install]
WantedBy=multi-user.target
