// creates the certificate infrastructure for service-to-service
// MTLS encryption
local certLib = import 'cert-lib.jsonnet';
local cloudInfo = import 'cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local namespaces = import 'eng-namespaces.jsonnet';
function(cloud)
  local createCentralNamespaceCerts = function(centralNamespace)
    assert centralNamespace.cloud == cloud;
    local name = if centralNamespace.env == 'PROD' then 'inference-root' else 'inference-%s-root' % std.asciiLower(centralNamespace.env);
    local leadClusterCertificate = if cloudInfo.isLeadCluster(cloud) then {
      apiVersion: 'cert-manager.io/v1',
      kind: 'Certificate',
      metadata: {
        name: '%s-certificate' % name,
        namespace: 'cert-manager',
      },
      spec: {
        isCA: true,
        secretName: '%s-certificate' % name,
        commonName: 'Root Certificate',
        // See AU-624 for why we need to renew more often than client
        // and server certificates.
        // Important to keep numbers in this file and cert-lib.jsonnet
        // consistent so that root certificates don't expire before
        // the client and service certificate kubernetes objects are updated.
        duration: '2160h',  // 90 days
        renewBefore: '1440h',  // renew 60 days before expiration
        privateKey: {
          algorithm: 'RSA',
          size: 4096,
          encoding: 'PKCS8',
        },
        issuerRef: {
          name: 'bootstrap-issuer',
          kind: 'ClusterIssuer',
        },
        secretTemplate: {
          labels: {
            'eng.augmentcode.com/cloud-sync': 'true',
          },
        },
      },
    }
    else
      // non-lead clusters will receive the root secret via cloud-sync
      null;


    local issuer = {
      apiVersion: 'cert-manager.io/v1',
      kind: 'ClusterIssuer',
      metadata: {
        name: certLib.getInferenceIssuer(centralNamespace.env),
      },
      spec: {
        ca: {
          secretName: '%s-certificate' % name,  // pragma: allowlist secret
        },
      },
    };

    lib.flatten([issuer, leadClusterCertificate]);
  local centralNamespaceRootCertificates = std.filterMap(
    function(centralNamespace) centralNamespace.cloud == cloud,
    createCentralNamespaceCerts,
    cloudInfo.centralNamespaces
  );
  local n = namespaces(cloud);
  lib.flatten(
    [
      std.map(certLib.createNamespaceIssuer, ['devtools', 'monitoring'] +
                                             std.map(function(ns) ns.name, n.eng + n.deployment)),
      centralNamespaceRootCertificates,
    ]
  )
