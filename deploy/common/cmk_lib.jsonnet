local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

{
  // Returns the service account name for Customer Managed Keys (CMK) based on environment
  getCmkServiceAccountName:: function(env)
    {
      PROD: 'cmk-service-account',
      STAGING: 'staging-cmk-service-account',
      DEV: 'dev-cmk-service-account',
    }[env],

  // Returns the service account email address for Customer Managed Keys (CMK) based on environment and cloud
  getCmkServiceAccountEmail:: function(env, cloud)
    '%s@%s.iam.gserviceaccount.com' % [self.getCmkServiceAccountName(env), cloudInfo[cloud].projectId],
}
