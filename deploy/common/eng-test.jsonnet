local eng = import 'deploy/common/eng.jsonnet';
local isSorted() =
  // keeps the list sorted alphabetically
  local sortedEngs = std.sort(eng, keyF=function(i) i.username);
  assert eng == sortedEngs : 'Not sorted alphabetically';
  true;
local isValidDnsComponent(component) =
  // Check the length of the component
  std.length(component) >= 1 &&
  std.length(component) <= 63 &&
  // Ensure it does not start or end with a hyphen
  std.substr(component, 0, 1) != '-' &&
  std.substr(component, std.length(component) - 1, 1) != '-' &&
  // Ensure it contains only alphanumeric characters or hyphens
  std.all(std.map(
    function(c)
      std.member('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-', c),
    std.stringChars(component)
  ));
local validate(u) =
  assert isValidDnsComponent(u.username) || u.gcp_access == null : 'Invalid user name: %s' % u.username;
  assert std.length(u.username) < 16 || u.gcp_access == null : 'User name too long: %s' % u.username;
  assert u.gcp_access == null || u.gcp_access == 'full' || u.gcp_access == 'external' : 'Invalid gcp_access: %s' % std.toString(u);
  assert u.piiAccess == 'general' || u.piiAccess == 'fraud' || u.piiAccess == 'masked' : 'Invalid piiAccess: %s' % std.toString(u);
  assert u.fullname != null : 'Missing fullname: %s' % std.toString(u);
  true;
[
  std.all(std.map(function(u) validate(u), eng)),
  isSorted(),
]
