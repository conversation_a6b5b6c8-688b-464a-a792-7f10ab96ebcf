local cloudInfoLib = import 'deploy/common/cloud_info.jsonnet';
// jsonnet library to grpc
local grpcDefaultPort = 50051;
// returns the global DNS name for the GRPC service
//
// Args
// - cloud: the cloud the service is deployed to
// - serviceName: the name of the service
// - namespace: the namespace the service is deployed to
// - headless: if headless mode (for client side load balancing) should be used for a non-global serivce or not
local globalGrpcServiceHostname(cloud, serviceName, namespace, headless=false) =
  if headless then
    '%s-gheadless-svc.%s.t.%s' % [serviceName, namespace, cloudInfoLib[cloud].internalDomainSuffix]
  else
    '%s.%s.t.%s' % [serviceName, namespace, cloudInfoLib[cloud].internalDomainSuffix];
local globalGrpcServiceHostnames(cloud, serviceName, namespace) =
  [
    globalGrpcServiceHostname(cloud, serviceName, namespace, headless=false),
    globalGrpcServiceHostname(cloud, serviceName, namespace, headless=true),
  ];
{
  // default port for grpc services
  grpcDefaultPort: grpcDefaultPort,

  // defines a liveness, readiness check or startup check configuration using grpc health check
  //
  // Args:
  // - serviceName: The name of the service that is exposed by the service. Can be null if mtls is false
  // - tls: if the service requires TLS
  // - serverCerts: The volume mount in the container where the find TLS server certificates (mandatory if tls is true)
  // - clientCerts: The volume mount in the container where the find TLS client certificates (use null to disable client cert usage)
  //
  // This assumes a container image which has the //tools/docker:grpc_health_probe_tar layer
  grpcHealthCheck: function(serviceName, tls, serverCerts=null, port=grpcDefaultPort, clientCerts='/client-certs')
    local clientCertOpts = if clientCerts != null then [
      '-tls-client-cert=%s/tls.crt' % clientCerts,
      '-tls-client-key=%s/tls.key' % clientCerts,
    ] else [];

    local tlsOpts = if tls then [
      assert serverCerts != null : 'serverCerts must be set if tls is true';

      '-tls',
      '-tls-ca-cert=%s/ca.crt' % serverCerts,
      '-tls-server-name=%s' % serviceName,
    ] + clientCertOpts else [];

    {
      exec: {
        command: [
          'grpc-health-probe',
          '--addr=localhost:%s' % port,
          '-connect-timeout=10s',
          '-rpc-timeout=10s',
        ] + tlsOpts,
      },
      // The default timeoutSeconds for a probe in k8s is 1 second.
      // For whatever reason, we use a 10 second timeout with grpc-health-probe.
      // It doesn't make sense for timeoutSeconds to be less than the
      // timeout we pass to grpc-health-probe. Also, we tend to use a period
      // of 30 seconds for our probes - it simplifies thinking to have timeoutSeconds
      // less than the period.
      //
      // Kubernetes may ignore this setting for exec probes if the ExecProbeTimeout
      // feature gate on kubelet is set to False. It is set to true on all of our
      // clusters as of the writing of this comment and since the feature gate
      // was introduced in 1.20 and any new clusters will be 1.30+, I imagine new
      // ones will also have it set to True.
      //
      // To check:
      // kubectl get --raw /metrics | grep "kubernetes_feature_enabled.*ExecProbeTimeout"
      // 1 means True, 0 means False
      //
      // https://github.com/kubernetes/enhancements/tree/master/keps/sig-node/1972-kubelet-exec-probe-timeouts
      timeoutSeconds: 21,
    },
  //
  // returns the DNS names for the GRPC services
  // if namespace is set, returns namespace-qualified dns names (svc.namespace) as well
  grpcServiceNames: function(serviceBaseName, namespace=null) [
                                                                '%s-svc' % serviceBaseName,
                                                                '%s-headless-svc' % serviceBaseName,
                                                              ]
                                                              + if namespace != null then [
                                                                '%s-svc.%s' % [serviceBaseName, namespace],
                                                                '%s-headless-svc.%s' % [serviceBaseName, namespace],
                                                              ] else [],
  // returns the namespace-qualifiedDNS names for the GRPC services (svc.namespace)
  //
  // this is for cluster local communication
  grpcServiceNamespaceNames: function(serviceBaseName, namespace) [
    '%s-svc.%s' % [serviceBaseName, namespace],
    '%s-headless-svc.%s' % [serviceBaseName, namespace],
  ],
  // defines services for GRPC
  //
  // This creates a clusterIp service under
  // Args:
  // - appName: Name of the app, this is usually is the selector of the service on the deployment for the 'app.kubernetes.io/name' label.
  // - serviceBaseName: The base name of the service. If not set, the appName is used.
  // - ports: The ports to expose. Defaults to [grpcDefaultPort]
  // - exposeToGrpcDebug: Whether to expose the service to the grpc debug service. Defaults to true.
  // Returns:
  // JSON document with
  // - objects: list of objects to create GRPC service
  // - portDetails: a map of ports to service port details
  createGrpcService: function(appName, namespace, serviceBaseName=null, ports=[grpcDefaultPort], exposeToGrpcDebug=true)
    local serviceName = if serviceBaseName == null then appName else serviceBaseName;
    local portDetails = {
      [std.toString(port)]: {
        name: if port == grpcDefaultPort then 'grpc-svc' else 'grpc-svc-%s' % port,
        protocol: 'TCP',
        port: port,
        targetPort: if port == grpcDefaultPort then 'grpc-svc' else 'grpc-svc-%s' % port,
      }
      for port in ports
    };

    local objects = [
      {
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          name: '%s-svc' % serviceName,
          namespace: namespace,
          labels: {
            app: appName,

          } + (if exposeToGrpcDebug then {
                 'eng.augmentcode.com/service-type': 'grpc',
               } else {}),
        },
        spec: {
          selector: {
            app: appName,
          },
          ports: std.objectValues(portDetails),
        },
      },
      {
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          name: '%s-headless-svc' % serviceName,
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          selector: {
            app: appName,
          },
          clusterIP: 'None',
          ports: std.objectValues(portDetails),
        },
      },
    ];

    {
      objects: objects,
      portDetails: portDetails,
    },
  // defines services for GRPC
  //
  // This creates a clusterIp service under
  // Args:
  // - appName: Name of the app, this is usually is the selector of the service on the deployment for the 'app.kubernetes.io/name' label.
  // - serviceBaseName: The base name of the service. If not set, the appName is used.
  // - ports: The ports to expose. Defaults to [grpcDefaultPort]
  // - exposeToGrpcDebug: Whether to expose the service to the grpc debug service. Defaults to true.
  // Returns:
  // list of objects to create GRPC service
  grpcService: function(appName, namespace, serviceBaseName=null, ports=[grpcDefaultPort], exposeToGrpcDebug=true)
    local grpcService = self.createGrpcService(appName, namespace, serviceBaseName, ports, exposeToGrpcDebug);
    grpcService.objects,
  globalGrpcServiceHostname: globalGrpcServiceHostname,
  globalGrpcServiceHostnames: globalGrpcServiceHostnames,
  globalGrpcService: function(cloud, appName, namespace, serviceBaseName=null, ports=[grpcDefaultPort])
    local serviceName = if serviceBaseName == null then appName else serviceBaseName;
    local cloudInfo = cloudInfoLib[cloud];
    local hostname = globalGrpcServiceHostname(cloud, serviceName, namespace);
    local headlessHostname = globalGrpcServiceHostname(cloud, serviceName, namespace, headless=true);
    [
      {
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          name: '%s-global-svc' % serviceName,
          namespace: namespace,
          labels: {
            app: appName,
          },
          // see https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb
          annotations: {
            'networking.gke.io/load-balancer-type': 'Internal',
            'networking.gke.io/internal-load-balancer-allow-global-access': 'true',
            'external-dns.alpha.kubernetes.io/hostname': hostname,
          },
        },
        spec: {
          selector: {
            app: appName,
          },
          type: 'LoadBalancer',
          ports: [
            {
              name: 'grpc-global-svc-port-%s' % port,
              protocol: 'TCP',
              port: port,
              targetPort: if port == grpcDefaultPort then 'grpc-svc' else 'grpc-svc-%s' % port,
            }
            for port in ports
          ],
        },
      },
      {
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          // gh = global headless
          name: '%s-gheadless-svc' % serviceName,
          namespace: namespace,
          labels: {
            app: appName,
          },
          annotations: {
            'external-dns.alpha.kubernetes.io/hostname': headlessHostname,
            'external-dns.alpha.kubernetes.io/ttl': '30',
          },
        },
        spec: {
          selector: {
            app: appName,
          },
          clusterIP: 'None',
          ports: [
            {
              name: 'grpc-global-svc-port-%s' % port,
              protocol: 'TCP',
              port: port,
              targetPort: if port == grpcDefaultPort then 'grpc-svc' else 'grpc-svc-%s' % port,
            }
            for port in ports
          ],
        },
      },
    ],
  isMtls: function(env, namespace, namespace_config)
    env != 'DEV' || (namespace_config != null && namespace_config.flags.forceMtls),
}
