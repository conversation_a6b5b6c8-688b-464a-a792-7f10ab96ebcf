local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
// function to create a namespace with certain required
// objects, e.g. for config connector
local createNamespace = function(cloud, namespace, appName=null)
  local projectId = cloudInfo[cloud].projectId;
  local iamServiceAccountName = 'configconnector-sa@%s.iam.gserviceaccount.com' % projectId;
  [
    // the namespace itself
    {
      apiVersion: 'v1',
      kind: 'Namespace',
      metadata: {
        name: namespace.name,
        annotations: {
          'cnrm.cloud.google.com/project-id': projectId,
        } + if std.objectHas(namespace, 'annotations') then namespace.annotations else {},
        labels: if appName != null then {
          app: appName,
        } else {},
      },
    },
    // iam workload identity binding for the namespaced config connector to use the config connector sa
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'cc-workload-identity-%s' % namespace.name,
        namespace: 'configconnector-operator-system',
        labels: if appName != null then {
          app: appName,
        } else {},
      },
      spec: {
        resourceRef: {
          kind: 'IAMServiceAccount',
          external: 'projects/%s/serviceAccounts/%s' % [projectId, iamServiceAccountName],
        },
        bindings: [
          {
            role: 'roles/iam.workloadIdentityUser',
            members: [
              {
                member: 'serviceAccount:%s.svc.id.goog[cnrm-system/cnrm-controller-manager-%s]' % [projectId, namespace.name],
              },
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'cnrm-rolebinding-%s' % namespace.name,
        labels: if appName != null then {
          app: appName,
        } else {},
      },
      roleRef: {
        apiGroup: 'rbac.authorization.k8s.io',
        kind: 'ClusterRole',
        name: 'cnrm-role',
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: 'cnrm-controller-manager-%s' % namespace.name,
          namespace: 'cnrm-system',
        },
      ],
    },
    // setup the namespace config connector
    {
      apiVersion: 'core.cnrm.cloud.google.com/v1beta1',
      kind: 'ConfigConnectorContext',
      metadata: {
        name: 'configconnectorcontext.core.cnrm.cloud.google.com',
        namespace: namespace.name,
        labels: if appName != null then {
          app: appName,
        } else {},
      },
      spec: {
        googleServiceAccount: iamServiceAccountName,
        stateIntoSpec: 'Absent',
      },
    },
  ];
{
  createNamespace:: createNamespace,
}
