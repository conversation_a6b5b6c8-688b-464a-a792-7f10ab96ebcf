{
  createConfigMap: function(appName, namespace, config, extraConfigs={}, suffix='config')
    local data = {
      'config.json': std.manifestJson(config),
    } + extraConfigs;
    // jsonnet doesn't have sha256, so we use md5. We do not care about
    // generated collisions and we only use the first 6 characters. The risk
    // of accidental collision is low.
    local hash = std.substr(std.md5(std.manifestJson(data)), 0, 6);
    local object = {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: '%s-%s-%s' % [appName, hash, suffix],
        namespace: namespace,
        labels: {
          app: appName,
          'to-gc': 'true',
        },
      },
      data: data,
    };
    {
      filename: '/%s/config.json' % suffix,
      objects: [object],
      name: object.metadata.name,
      volumeMountDef: {
        name: object.metadata.name,
        mountPath: '/%s' % suffix,
        readOnly: true,
      },
      podVolumeDef: {
        name: object.metadata.name,
        configMap: {
          name: object.metadata.name,
        },
      },
    },
}
