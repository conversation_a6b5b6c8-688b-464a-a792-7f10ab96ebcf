local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

function(cloud)
  local projectId = cloudInfo[cloud].projectId;
  // usually only in project lead clusters.
  if cloudInfo.isProjectLeadCluster(cloud) then
    [
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMServiceAccount',
        metadata: {
          name: 'dns01-solver',
          namespace: 'cert-manager',
        },
        spec: {
          displayName: 'dns01-solver',
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPolicyMember',
        metadata: {
          name: 'iampolicymember-dns01-solver-dns-admin',
          namespace: 'cert-manager',
          labels: {
            app: 'cert-manager',
          },
        },
        spec: {
          member: 'serviceAccount:dns01-solver@%s.iam.gserviceaccount.com' % projectId,
          role: 'roles/dns.admin',
          resourceRef: {
            kind: 'Project',
            external: 'projects/%s' % projectId,
          },
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPolicy',
        metadata: {
          name: 'dns01-solver-workload-identity',
          namespace: 'cert-manager',
        },
        spec: {
          resourceRef: {
            apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
            kind: 'IAMServiceAccount',
            name: 'dns01-solver',
          },
          bindings: [
            {
              role: 'roles/iam.workloadIdentityUser',
              members: [
                'serviceAccount:%s.svc.id.goog[cert-manager/cert-manager]' % projectId,
              ],
            },
          ],
        },
      },
    ] else []
