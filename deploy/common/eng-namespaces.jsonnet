local cloudInfo = import 'cloud_info.jsonnet';
local tenantNamespaceLib = import 'deploy/tenants/namespaces.jsonnet';
local eng = import 'eng.jsonnet';

function(cloud)
  // The test and test shard namespace creation have been moved to
  // the bazel_runner/server/deploy.jsonnet.
  local hasAccess = function(u) (u.gcp_access != null);
  local individualNamespaces = if cloudInfo.isDevCluster(cloud) then [{
    name: 'dev-%s' % e.username,
    access_type: 'dev',
    annotations: {
      'eng.augmentcode.com/owner': e.username,
    },
  } for e in std.filter(hasAccess, eng)] else [];
  local teamNamespaces = if cloudInfo.isDevCluster(cloud) then [{
    name: 'dev-team-ra',
    access_type: 'dev',
  }] else [];
  local engNamespaces = individualNamespaces + teamNamespaces;
  local sharedNamespaces = [
    {
      name: 'devtools',
      access_type: 'prod',
    },
    {
      name: 'monitoring',
      access_type: 'prod',
    },
    {
      name: 'cert-manager',
      access_type: 'prod',
    },
    {
      name: 'gmp-public',
      access_type: 'prod',
    },
    {
      name: 'keda',
      access_type: 'prod',
    },
    {
      name: 'external-dns',
      access_type: 'prod',
    },
    {
      name: 'configconnector-operator-system',
      access_type: 'prod',
    },
    {
      name: 'opentelemetry',
      access_type: 'prod',
    },
    {
      name: 'security',
      access_type: 'prod',
    },
  ];
  local tenantNamespaces = [{
    name: ns.namespace,
    access_type: std.asciiLower(ns.env),
  } for ns in tenantNamespaceLib.namespaces if ns.cloud == cloud];
  local centralNamespaces = if cloudInfo.isProdCluster(cloud) then [
    // namespace for central services (auth, MT inference)
    {
      name: 'central',
      access_type: 'prod',
    },
    {
      name: 'central-staging',
      access_type: 'staging',
    },
  ] else [
    {
      name: 'central-dev',
      access_type: 'dev',
    },
  ];
  local deploymentNamespaces = centralNamespaces + tenantNamespaces;
  local allNamespaces = sharedNamespaces + engNamespaces + deploymentNamespaces;

  {
    // engineer development namespaces
    eng: engNamespaces,
    deployment: deploymentNamespaces,
    shared: sharedNamespaces,
    all: allNamespaces,
    central: centralNamespaces,

    // function that returns the namespace infos by namespace name
    info: function(name) std.filter(function(n) n.name == name, allNamespaces)[0],
  }
