local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

function(cloud)
  local projectId = cloudInfo[cloud].projectId;
  local domainFilters = cloudInfo[cloud].domainFilters;

  local dns01 = {
    cloudDNS: {
      project: projectId,
    },
  };
  local solvers = [
    {
      selector: {
        dnsZones: [
          domain,
        ],
      },
      dns01: dns01,
    }
    for domain in domainFilters
  ];
  [
    {
      apiVersion: 'cert-manager.io/v1',
      kind: 'ClusterIssuer',
      metadata: {
        name: 'letsencrypt-staging',
      },
      spec: {
        acme: {
          server: 'https://acme-staging-v02.api.letsencrypt.org/directory',
          email: '<EMAIL>',
          privateKeySecretRef: {
            name: 'letsencrypt-staging',
          },
          solvers: solvers,
        },
      },
    },
    {
      apiVersion: 'cert-manager.io/v1',
      kind: 'ClusterIssuer',
      metadata: {
        name: 'letsencrypt-prod',
      },
      spec: {
        acme: {
          server: 'https://acme-v02.api.letsencrypt.org/directory',
          email: '<EMAIL>',
          privateKeySecretRef: {
            name: 'letsencrypt-prod',
          },
          solvers: solvers,
        },
      },
    },
    {
      apiVersion: 'cert-manager.io/v1',
      kind: 'ClusterIssuer',
      metadata: {
        name: 'google-pki-prod',
      },
      spec: {
        acme: {
          // If you ever need to generate and register a new private key
          // with Google Trust Services, somnething like this might work:
          //    - create a new cluster issuer and rename all references to google-pki-prod to something else (e.g. google-pki-prod-v2)
          //    - set disableAccountKeyGeneration to false.
          //    - Get an EAB key ID and secret from Google Trust Services
          //    - kubectl -n cert-manager create secret generic google-pki-prod-eab-secret --from-literal secret=<secret from google>
          //    - Add the following YAML:
          //        externalAccountBinding:
          //          keyID: <account ID from google>
          //          keySecretRef:
          //            name: google-pki-prod-eab-secret
          //            key: secret
          //    - Wait for the google-pki-prod-v2 secret to get created in the cert-manager namespace.
          //    - overwrite google-pki-prod secret with google-pki-prod-v2 secret
          //    - restart cert-manager for good measure
          //    - delete the google-pki-prod-v2 cluster issuer
          //    - generate sealed secrets
          disableAccountKeyGeneration: true,
          server: 'https://dv.acme-v02.api.pki.goog/directory',
          email: '<EMAIL>',
          privateKeySecretRef: {
            name: 'google-pki-prod',
          },
          solvers: solvers,
        },
      },
    },
    // Generated the sealed secrets by doing:
    //   kubectl -n cert-manager get secret google-pki-prod -o yaml | bazel run //tools:kubeseal -- --context <context> -n cert-manager
    {
      kind: 'SealedSecret',
      apiVersion: 'bitnami.com/v1alpha1',
      metadata: {
        name: 'google-pki-prod',
        namespace: 'cert-manager',
      },
      spec: {
        encryptedData: {
          GCP_US_CENTRAL1_DEV: {
            'tls.key': '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',  // pragma: allowlist secret
          },
          GCP_US_CENTRAL1_PROD: {
            'tls.key': '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',  // pragma: allowlist secret
          },
          GCP_EU_WEST4_PROD: {
            'tls.key': '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',  // pragma: allowlist secret
          },
          GCP_US_CENTRAL1_GSC_PROD: {
            'tls.key': '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',  // pragma: allowlist secret
          },
        }[cloud],
      },
    },
    {
      apiVersion: 'cert-manager.io/v1',
      kind: 'ClusterIssuer',
      metadata: {
        name: 'bootstrap-issuer',
      },
      spec: {
        selfSigned: {},
      },
    },
  ]
