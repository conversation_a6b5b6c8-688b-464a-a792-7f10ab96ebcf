local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
function(cloud)
  local projectId = cloudInfo[cloud].projectId;
  local domainFilters = cloudInfo[cloud].domainFilters;

  // Dev DNS zones aren't managed by ConfigConnector currently.
  local dnsZones = if cloud == 'GCP_US_CENTRAL1_PROD' then [
    {
      apiVersion: 'dns.cnrm.cloud.google.com/v1beta1',
      kind: 'DNSManagedZone',
      metadata: {
        name: 'prod-zone',
        namespace: 'external-dns',
        annotations: {
          'cnrm.cloud.google.com/project-id': projectId,
        },
      },
      spec: {
        description: 'DNS zone for production services',
        dnsName: 'prod.augmentcode.com.',
        visibility: 'public',
        dnssecConfig: {
          state: 'off',
        },
        cloudLoggingConfig: {
          enableLogging: true,
        },
      },
    },
  ] else if cloud == 'GCP_US_CENTRAL1_GSC_PROD' then [
    {
      apiVersion: 'dns.cnrm.cloud.google.com/v1beta1',
      kind: 'DNSManagedZone',
      metadata: {
        name: 'prod-gsc-zone',
        namespace: 'external-dns',
        annotations: {
          'cnrm.cloud.google.com/project-id': projectId,
        },
      },
      spec: {
        description: 'DNS zone for production services',
        dnsName: cloudInfo.GCP_US_CENTRAL1_GSC_PROD.internalDomainSuffix + '.',
        visibility: 'public',
        dnssecConfig: {
          state: 'off',
        },
        cloudLoggingConfig: {
          enableLogging: true,
        },
      },
    },
  ] else if cloudInfo.isMainDevCluster(cloud) then [
    {
      apiVersion: 'dns.cnrm.cloud.google.com/v1beta1',
      kind: 'DNSManagedZone',
      metadata: {
        name: 'dev-zone',
        namespace: 'external-dns',
        annotations: {
          'cnrm.cloud.google.com/project-id': projectId,
        },
      },
      spec: {
        description: 'DNS zone for production services',
        dnsName: 'dev.augmentcode.com.',
        visibility: 'public',
        dnssecConfig: {
          state: 'off',
        },
        cloudLoggingConfig: {
          enableLogging: true,
        },
      },
    },
  ] else [];
  local iamMembers = if cloudInfo.isProjectLeadProdCluster(cloud) then [
    // only in GCP MAIN PROD cluster. The GCP objects are shared between production clusters.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicyMember',
      metadata: {
        name: 'external-dns-dns-dns-admin-member',
        namespace: 'external-dns',
      },
      spec: {
        member: 'serviceAccount:external-dns-iam-sa@%s.iam.gserviceaccount.com' % projectId,
        role: 'roles/dns.admin',
        resourceRef: {
          kind: 'Project',
          external: 'projects/%s' % projectId,
        },
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMServiceAccount',
      metadata: {
        name: 'external-dns-iam-sa',
        namespace: 'external-dns',
        labels: {
          'app.kubernetes.io/name': 'external-dns',
        },
      },
      spec: {
        displayName: 'external-dns-iam-sa',
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'exteral-dns-workload-identity',
        namespace: 'external-dns',
        labels: {
          'app.kubernetes.io/name': 'external-dns',
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMServiceAccount',
          name: 'external-dns-iam-sa',
        },
        bindings: [
          {
            role: 'roles/iam.workloadIdentityUser',
            members: [
              'serviceAccount:%s.svc.id.goog[external-dns/external-dns-sa]' % projectId,
            ],
          },
        ],
      },
    },
  ] else if cloud == 'GCP_US_CENTRAL1_GSC_PROD' then [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMServiceAccount',
      metadata: {
        name: 'external-dns-iam-sa',
        namespace: 'external-dns',
        labels: {
          'app.kubernetes.io/name': 'external-dns',
        },
      },
      spec: {
        displayName: 'external-dns-iam-sa',
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'exteral-dns-workload-identity',
        namespace: 'external-dns',
        labels: {
          'app.kubernetes.io/name': 'external-dns',
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMServiceAccount',
          name: 'external-dns-iam-sa',
        },
        bindings: [
          {
            role: 'roles/iam.workloadIdentityUser',
            members: [
              'serviceAccount:%s.svc.id.goog[external-dns/external-dns-sa]' % projectId,
            ],
          },
        ],
      },
    },
  ] else [];
  local txtOwnerId = {
    GCP_US_CENTRAL1_PROD: 'external-dns',
    GCP_US_CENTRAL1_DEV: 'external-dns',
    GCP_EU_WEST4_PROD: 'external-dns-eu-west4',
    GCP_US_CENTRAL1_GSC_PROD: 'external-dns-gsc',
  }[cloud];
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'external-dns',
      namespace: 'external-dns',
    },
    spec: {
      strategy: {
        type: 'Recreate',
      },
      selector: {
        matchLabels: {
          app: 'external-dns',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'external-dns',
          },
        },
        spec: {
          priorityClassName: cloudInfo.envToPriorityClass('PROD'),
          serviceAccountName: 'external-dns-sa',
          containers: [
            {
              name: 'external-dns',
              image: 'k8s.gcr.io/external-dns/external-dns:v0.10.2',
              args: [
                '--source=service',
                '--source=ingress',
                '--provider=google',
                '--policy=sync',
                '--log-format=json',  // google cloud logs parses severity of the "text" log format incorrectly
                '--google-zone-visibility=public',  // Use this to filter to only zones with this visibility. Set to either 'public' or 'private'. Omitting will match public and private zones
                '--registry=txt',
                '--txt-owner-id=%s' % txtOwnerId,
              ] + [
                '--domain-filter=%s' % domain
                for domain in domainFilters
              ],
            },
          ],
          securityContext: {
            fsGroup: 65534,
          },
        },
      },
    },
  };

  local serviceAccount = {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
    metadata: {
      name: 'external-dns-sa',
      namespace: 'external-dns',
      labels: {
        'app.kubernetes.io/name': 'external-dns',
      },
      annotations: {
        'iam.gke.io/gcp-service-account': 'external-dns-iam-sa@%s.iam.gserviceaccount.com' % projectId,
      },
    },
  };
  local roles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'external-dns',
        labels: {
          'app.kubernetes.io/name': 'external-dns',
        },
      },
      rules: [
        {
          apiGroups: [
            '',
          ],
          resources: [
            'services',
            'endpoints',
            'pods',
          ],
          verbs: [
            'get',
            'watch',
            'list',
          ],
        },
        {
          apiGroups: [
            'extensions',
            'networking.k8s.io',
          ],
          resources: [
            'ingresses',
          ],
          verbs: [
            'get',
            'watch',
            'list',
          ],
        },
        {
          apiGroups: [
            '',
          ],
          resources: [
            'nodes',
          ],
          verbs: [
            'list',
            'watch',
          ],
        },
      ],
    },
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'external-dns-viewer',
        labels: {
          'app.kubernetes.io/name': 'external-dns',
        },
      },
      roleRef: {
        apiGroup: 'rbac.authorization.k8s.io',
        kind: 'ClusterRole',
        name: 'external-dns',
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: 'external-dns-sa',
          namespace: 'external-dns',
        },
      ],
    },
  ];
  lib.flatten(
    [
      serviceAccount,
      roles,
      deployment,
      dnsZones,
      iamMembers,
    ]
  )
