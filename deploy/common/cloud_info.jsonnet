// basic information about the cloud environments supported
// the file should be materializable as json, so all functions should be marked as hidden for
// materialization (see https://jsonnet.org/ref/language.html)
{
  envToPriorityClass(env, resource=null)::
    if env == 'DEV' then null
    else if env == 'STAGING' then
      if resource == 'large' then 'staging-gpu'
      else if resource == 'premiumCpu' || resource == 'premiumCpuHighmem' then 'staging-premium-cpu'
      else 'staging'
    else
      if resource == 'large' then 'prod-gpu'
      else if resource == 'premiumCpu' || resource == 'premiumCpuHighmem' then 'prod-premium-cpu'
      else 'prod',
  centralNamespaces:: [
    {
      cloud: 'GCP_US_CENTRAL1_PROD',
      env: 'STAGING',
      namespace: 'central-staging',
    },
    {
      cloud: 'GCP_US_CENTRAL1_PROD',
      env: 'PROD',
      namespace: 'central',
    },
    {
      cloud: 'GCP_EU_WEST4_PROD',
      env: 'STAGING',
      namespace: 'central-staging',
    },
    {
      cloud: 'GCP_EU_WEST4_PROD',
      env: 'PROD',
      namespace: 'central',
    },
    {
      cloud: 'GCP_US_CENTRAL1_GSC_PROD',
      env: 'STAGING',
      namespace: 'central-staging',
    },
    {
      cloud: 'GCP_US_CENTRAL1_GSC_PROD',
      env: 'PROD',
      namespace: 'central',
    },
  ],
  // returns the central namespace for a given namespace
  getCentralNamespaceForNamespace:: function(env, namespace, cloud)
    if env == 'DEV' then namespace else std.filter(function(cn) cn.env == env && cn.cloud == cloud, self.centralNamespaces)[0].namespace,
  isKubecfgTestNamespace:: function(namespace) namespace == 'dev-ef0123456789',
  isCentralNamespace:: function(env, namespace, cloud) namespace == self.getCentralNamespaceForNamespace(env, namespace, cloud) || self.isKubecfgTestNamespace(namespace),
  // returns true if the cluster can host non-devtools production workloads
  getLeadClusterForCluster:: function(cloud)
    if cloud == 'GCP_EU_WEST4_PROD' then 'GCP_US_CENTRAL1_PROD' else cloud,
  isProdCluster:: function(cloud) cloud == 'GCP_US_CENTRAL1_PROD' || cloud == 'GCP_EU_WEST4_PROD' || cloud == 'GCP_US_CENTRAL1_GSC_PROD' || cloud == 'GCP_AGENT_US_CENTRAL1_PROD' || cloud == 'GCP_AGENT_EU_WEST4_PROD',
  // returns true if the cluster hosts dev workloads. Inverse of isProdCluster
  isDevCluster:: function(cloud) !self.isProdCluster(cloud),
  // returns true if the cluster is the lead production cloud.
  // some GCP objects are only created in the lead cloud and shared across all prod clusters
  isLeadProdCluster:: function(cloud) cloud == 'GCP_US_CENTRAL1_PROD',
  // returns true if the cluster is the cloud cluster in a project.
  isProjectLeadProdCluster:: function(cloud) cloud == 'GCP_US_CENTRAL1_PROD' || cloud == 'GCP_US_CENTRAL1_GSC_PROD',
  isProjectLeadCluster:: function(cloud) self.isProjectLeadProdCluster(cloud) || self.isLeadDevCluster(cloud),
  // Returns true if the cluster is a lead cloud for DEV purpuses.
  isLeadDevCluster:: function(cloud) cloud == 'GCP_US_CENTRAL1_DEV',

  // returns true if the cluster is the main dev cluster.
  isMainDevCluster:: function(cloud) cloud == 'GCP_US_CENTRAL1_DEV',

  // returns true if the cluster is the lead cluster in a project.
  //
  // each GCP project needs one and only one lead cluster.
  // some GCP objects are only created in the lead cloud and shared across all clusters in the same project
  isLeadCluster:: function(cloud) self.isLeadProdCluster(cloud) || self.isLeadDevCluster(cloud),

  // returns true if the namespace is unique inside a project
  isUniqueNamespace:: function(cloud, env, namespace)
    if self.isDevCluster(cloud) then
      // We only have one dev cluster in the dev project
      true
    else
      if self.isCentralNamespace(env, namespace, cloud) then
        // We have multiple clusters in the prod project
        // Clusters share names for central namespaces, e.g. central, central-staging
        false
      else
        // For non-central prod namespaces, they should be unique across clusters
        true,

  // returns true if the cloud is located in the EU
  isEuCluster:: function(cloud) std.startsWith(self[cloud].region, 'europe'),

  GCP_US_CENTRAL1_DEV: {
    projectId: 'system-services-dev',
    projectNumber: '1035750215372',
    region: 'us-central1',
    mainZone: 'us-central1-a',
    domainFilters: [
      'us-central.api.augmentcode.com',
      'dev.augmentcode.com',
    ],
    context: 'gke_system-services-dev_us-central1_us-central1-dev',
    clusterName: 'us-central1-dev',
    internalDomainSuffix: 'us-central1.dev.augmentcode.com',
    rootDNSZone: 'dev-zone',
    // TODO change to dev.augmentcode.com once staging and tenzero moved
    apiDomain: 'us-central.api.augmentcode.com',
    shortName: 'us-c1-dev',
    privateServiceConnectDomain: 'psc.dev.augmentcode.com',  // Creates Certs and DNS records for this domain.
    privateServiceConnectDNSLead: true,  // Hosts the DNSManagedZone for privateServiceConnectDomain.
    privateServiceConnectSubnet: '**********/20',  // system-services-dev **********/16#0
  },
  GCP_US_CENTRAL1_PROD: {
    projectId: 'system-services-prod',
    projectNumber: '835723878709',
    region: 'us-central1',
    mainZone: 'us-central1-a',
    domainFilters: [
      'augmentcode.com',
    ],
    context: 'gke_system-services-prod_us-central1_us-central1-prod',
    clusterName: 'us-central1-prod',
    internalDomainSuffix: 'us-central1.prod.augmentcode.com',
    rootDNSZone: 'augmentcode-com',
    apiDomain: 'api.augmentcode.com',
    location: 'us-central1',
    shortName: 'us-c1',
    privateServiceConnectDomain: 'psc.augmentcode.com',  // Creates Certs and DNS records for this domain.
    privateServiceConnectDNSLead: true,  // Hosts the DNSManagedZone for privateServiceConnectDomain.
    privateServiceConnectSubnet: '**********/20',  // system-services-prod **********/16#0
  },
  GCP_US_CENTRAL1_GSC_PROD: {
    projectId: 'system-services-prod-gsc',
    projectNumber: '610909115406',
    region: 'us-central1',
    mainZone: 'us-central1-a',
    domainFilters: [
      'augmentcode.com',
    ],
    context: 'gke_system-services-prod-gsc_us-central1_prod-gsc',
    clusterName: 'prod-gsc',
    internalDomainSuffix: 'us-central1-gsc.prod.augmentcode.com',
    apiDomain: null,  // does not export api proxies
    location: 'us-central1',
    shortName: 'us-c1-gsc',
  },
  GCP_EU_WEST4_PROD: {
    projectId: 'system-services-prod',
    region: 'europe-west4',
    mainZone: 'europe-west4-a',
    projectNumber: '835723878709',
    domainFilters: [
      'augmentcode.com',
    ],
    context: 'gke_system-services-prod_europe-west4_eu-west4-prod',
    clusterName: 'eu-west4-prod',
    internalDomainSuffix: 'eu-west4.prod.augmentcode.com',
    apiDomain: 'api.augmentcode.com',
    shortName: 'eu-w4',
    privateServiceConnectDomain: 'psc.augmentcode.com',  // Creates Certs and DNS records for this domain.
    privateServiceConnectDNSLead: false,  // Does NOT host DNSManagedZone.
    privateServiceConnectSubnet: '***********/20',  // system-services-prod **********/16#1
  },
  GCP_AGENT_US_CENTRAL1_PROD: {
    projectId: 'agent-sandbox-prod',
    projectNumber: '882427529463',
    region: 'us-central1',
    mainZone: 'us-central1-a',
    domainFilters: [
      'augmentcode.com',
    ],
    context: 'gke_agent-sandbox-prod_us-central1_gcp-prod-agent0',
    clusterName: 'gcp-prod-agent0',
    internalDomainSuffix: 'us-central1.agent.augmentcode.com',
    apiDomain: null,  // agent clusters don't export api proxies
    shortName: 'agent-us-c1',
  },
  GCP_AGENT_EU_WEST4_PROD: {
    projectId: 'agent-sandbox-prod',
    projectNumber: '882427529463',
    region: 'europe-west4',
    mainZone: 'europe-west4-a',
    domainFilters: [
      'augmentcode.com',
    ],
    context: 'gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0',
    clusterName: 'gcp-eu-w4-prod-agent0',
    internalDomainSuffix: 'eu-west4.agent.augmentcode.com',
    apiDomain: null,  // agent clusters don't export api proxies
    shortName: 'agent-eu-w4',
  },
}
