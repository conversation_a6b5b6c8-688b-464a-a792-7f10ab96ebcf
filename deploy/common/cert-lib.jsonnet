local checkDnsNames(dnsNames) =
  // all labels of DNS names should be <= 63
  // see https://devblogs.microsoft.com/oldnewthing/20120412-00/?p=7873
  std.filter(function(n) std.length(n) > 63, std.flatMap(function(n) std.split(n, '.'), dnsNames)) == [];

local getIngressIssuer(env) = 'google-pki-prod';

// We should limit how many CertificateRequest objects are kept around,
// otherwise, especially for certificates that rotate very often (e.g. 1h), we
// can end up with a lot of stranded CertificateRequest objects. With enough
// extra objects, we have seen this cause failures in cert-manager, because it's
// request to get a list of CertificateRequests times out.
local defaultRevisionHistoryLimit = 10;

local createCertCommon(
  name,
  namespace,
  appName,
  volumeName,
  spec,
      ) =
  local objects = {
    apiVersion: 'cert-manager.io/v1',
    kind: 'Certificate',
    metadata: {
      name: name,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: spec + {
      revisionHistoryLimit: defaultRevisionHistoryLimit,
    },
  };
  local podVolumeDef = {
    name: volumeName,
    secret: {
      secretName: name,
    },
  };
  local volumeMountDef = {
    name: volumeName,
    mountPath: '/%s' % volumeName,
    readOnly: true,
  };
  local config = {
    ca_path: '/%s/ca.crt' % volumeName,
    cert_path: '/%s/tls.crt' % volumeName,
    key_path: '/%s/tls.key' % volumeName,
  };
  {
    objects: objects,
    podVolumeDef: podVolumeDef,
    volumeMountDef: volumeMountDef,
    config: config,
  };

// jsonnet library to create certificates
local createClientCert(
  name,
  namespace,
  appName,
  volumeName='client-certs',
  dnsNames=null,
  duration='1440h',  // 60 days
  renewBefore='720h',  // 30 days before
      ) =
  assert checkDnsNames([name]) : name;
  createCertCommon(
    name=name,
    namespace=namespace,
    appName=appName,
    volumeName=volumeName,
    spec={
      // If you change this duration, consider interactions
      // with root certificate renewal period (AU-624)
      duration: duration,
      renewBefore: renewBefore,
      revisionHistoryLimit: defaultRevisionHistoryLimit,
      secretName: name,
      privateKey: {
        algorithm: 'RSA',
        encoding: 'PKCS8',
        size: 4096,
        rotationPolicy: 'Always',
      },
      commonName: if dnsNames != null then dnsNames[0] else name,
      dnsNames: dnsNames,
      usages: [
        'client auth',
        'key encipherment',
        'digital signature',
      ],
      issuerRef: {
        name: 'root-issuer',
        kind: 'Issuer',
      },
    },
  );

local createPublicServerCert(
  name,
  dnsNames,
  namespace,
  appName,
  env,
  volumeName='public-server-certs',
      ) =
  assert checkDnsNames(dnsNames);
  createCertCommon(
    name=name,
    namespace=namespace,
    appName=appName,
    volumeName=volumeName,
    spec={
      // If you change this duration, consider interactions
      // with root certificate renewal period (AU-624)
      duration: '1440h',  // 60 days
      renewBefore: '720h',  // 30 days before
      revisionHistoryLimit: defaultRevisionHistoryLimit,
      secretName: name,
      privateKey: {
        algorithm: 'RSA',
        encoding: 'PKCS8',
        size: 4096,
        rotationPolicy: 'Always',
      },
      commonName: dnsNames[0],
      dnsNames: dnsNames,
      usages: [
        'server auth',
        'key encipherment',
        'digital signature',
      ],
      issuerRef: {
        name: getIngressIssuer(env),
        kind: 'ClusterIssuer',
      },
    },
  );

local createServerCert(
  name,
  dnsNames,
  namespace,
  appName,
  volumeName='server-certs',
      ) =
  assert checkDnsNames(dnsNames);
  createCertCommon(
    name=name,
    namespace=namespace,
    appName=appName,
    volumeName=volumeName,
    spec={
      // If you change this duration, consider interactions
      // with root certificate renewal period (AU-624)
      duration: '1440h',  // 60 days
      renewBefore: '720h',  // 30 days before
      revisionHistoryLimit: defaultRevisionHistoryLimit,
      secretName: name,
      privateKey: {
        algorithm: 'RSA',
        encoding: 'PKCS8',
        size: 4096,
        rotationPolicy: 'Always',
      },
      commonName: dnsNames[0],
      dnsNames: dnsNames,
      usages: [
        'server auth',
        'key encipherment',
        'digital signature',
      ],
      issuerRef: {
        name: 'root-issuer',
        kind: 'Issuer',
      },
    },
  );

local getInferenceIssuer(env) =
  assert env == 'STAGING' || env == 'PROD';
  if env == 'PROD' then 'inference-root-issuer' else 'inference-%s-root-issuer' % std.asciiLower(env);
/*
 * Create a certificate that allows a client to access a service that's hosted in central (PROD) or central-staging (STAGING).
 * For DEV deployment, we don't use central namespaces right now, so fall back to createClientCert. In the future, DEV deployments may want
 * to use a different issuer to better simulate the real system.
 *
 *   name - k8s name of the certificate object
 *   env - what kind of environment is being targetted
 *   namespace - namespace to create the client cert object (usually a tenant, not central/central-staging)
 *   appName - app with which to associate k8s objects returned
 *   dnsNames - DNS names to include in the certificate. These DNS names are not checked as part of TLS connection setup, but
 *              certain services may use them to distinguish between clients and assign them different permissions.
 *
 * Returns: a k8s certificate object
 */
local createCentralClientCert(
  name,
  env,
  namespace,
  appName,
  volumeName='central-client-certs',
  dnsNames=null,
  duration='1440h',  // 60 days
  renewBefore='720h',  // 30 days before
      ) =
  if env == 'DEV' then createClientCert(
    name=name,
    namespace=namespace,
    dnsNames=dnsNames,
    appName=appName,
    volumeName=volumeName,
    duration=duration,
    renewBefore=renewBefore
  ) else
    assert checkDnsNames(dnsNames);
    local issuer = getInferenceIssuer(env);
    createCertCommon(
      name=name,
      namespace=namespace,
      appName=appName,
      volumeName=volumeName,
      spec={
        // If you change this duration, consider interactions
        // with root certificate renewal period (AU-624)
        duration: duration,
        renewBefore: renewBefore,
        revisionHistoryLimit: defaultRevisionHistoryLimit,
        secretName: name,
        privateKey: {
          algorithm: 'RSA',
          encoding: 'PKCS8',
          size: 4096,
          rotationPolicy: 'Always',
        },
        commonName: if dnsNames != null then dnsNames[0] else name,
        dnsNames: dnsNames,
        usages: [
          'client auth',
          'key encipherment',
          'digital signature',
        ],
        issuerRef: {
          name: issuer,
          kind: 'ClusterIssuer',
        },
      },
    );

/*
 * Create a certificate for a service that's hosted in central (PROD) or central-staging (STAGING).
 * For DEV deployment, we don't use central namespaces right now, so fall back to createServerCert. In the future,
 * DEV deployments may want to use a different issuer to better simulate the real system.
 *
 *   name - k8s name of the certificate object
 *   env - what kind of environment is being targetted
 *   namespace - namespace to create the server cert object (usually a central namespace, except for DEV)
 *   appName - app with which to associate k8s objects returned
 *   dnsNames - DNS names to include in the certificate. Important that they match the DNS names used to connect to the service
 *              otherwise connections will fail with certificate verification errors.
 *
 * Returns: a k8s certificate object
 */

local createCentralServerCert(
  name,
  dnsNames,
  namespace,
  env,
  appName,
  volumeName='central-server-certs',
      ) =
  if env == 'DEV' then createServerCert(
    name=name,
    namespace=namespace,
    dnsNames=dnsNames,
    appName=appName,
    volumeName=volumeName,
  ) else
    assert checkDnsNames(dnsNames);
    createCertCommon(
      name=name,
      namespace=namespace,
      appName=appName,
      volumeName=volumeName,
      spec={
        // If you change this duration, consider interactions
        // with root certificate renewal period (AU-624)
        duration: '1440h',  // 60 days
        renewBefore: '720h',  // 30 days before
        revisionHistoryLimit: defaultRevisionHistoryLimit,
        secretName: name,
        privateKey: {
          algorithm: 'RSA',
          encoding: 'PKCS8',
          size: 4096,
          rotationPolicy: 'Always',
        },
        commonName: dnsNames[0],
        dnsNames: dnsNames,
        usages: [
          'server auth',
          'key encipherment',
          'digital signature',
        ],
        issuerRef: {
          name: getInferenceIssuer(env),
          kind: 'ClusterIssuer',
        },
      },
    );

// Create a private/public key pair, using a cert-manager certificate.
local createPrivateKeyCert(name, namespace, appName, algorithm, size, volumeName='jwt-certs', cloudSync=false) =
  // Assertions are based on https://cert-manager.io/docs/reference/api-docs/#cert-manager.io/v1.CertificatePrivateKey
  assert std.member(['RSA', 'ECDSA', 'Ed25519'], algorithm);
  assert if algorithm == 'RSA' then std.member([2048, 4096, 8192], size) else if algorithm == 'ECDSA' then std.member([256, 384, 521], size) else true;
  {
    objects: {
      apiVersion: 'cert-manager.io/v1',
      kind: 'Certificate',
      metadata: {
        name: name,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        // If you change this duration, consider interactions
        // with root certificate renewal period (AU-624)
        duration: '1440h',  // 60 days
        renewBefore: '720h',  // 30 days before
        revisionHistoryLimit: defaultRevisionHistoryLimit,
        secretName: name,
        privateKey: {
          algorithm: algorithm,
          encoding: 'PKCS8',
          size: size,
          rotationPolicy: 'Always',
        },
        commonName: name,
        usages: [
          'signing',
          'digital signature',
        ],
        issuerRef: {
          name: 'root-issuer',
          kind: 'Issuer',
        },
      } + if cloudSync then {
        secretTemplate: {
          labels: {
            'eng.augmentcode.com/cloud-sync': 'true',
          },
        },
      } else {},
    },
    podVolumeDef: {
      name: volumeName,
      secret: {
        secretName: name,
      },
    },
    volumeMountDef: {
      name: volumeName,
      mountPath: '/%s' % volumeName,
      readOnly: true,
    },
    config: {
      cert_path: '/%s/tls.crt' % volumeName,
      key_path: '/%s/tls.key' % volumeName,
    },
  };

// Creates a certificate authority for a namespace. Using a different CA
// per namespace makes it less likely that a pod in one namespace can
// successfully connect to a pod in another namespace, providing some
// level of network isolation. This property is becoming less important
// as we move to authenticating all requests with tokens rather than
// certificates.
local createNamespaceIssuer(namespace, appName='namespace-issuer') = [
  {
    apiVersion: 'cert-manager.io/v1',
    kind: 'Certificate',
    metadata: {
      labels: {
        app: appName,
      },
      name: 'root-certificate',
      namespace: namespace,
    },
    spec: {
      isCA: true,
      secretName: 'root-certificate',  // pragma: allowlist secret
      commonName: 'Root Certificate',
      // See AU-624 for why we need to renew more often than client
      // and server certificates.
      // Important to keep numbers in this file and cert-lib.jsonnet
      // consistent so that root certificates don't expire before
      // the client and service certificate kubernetes objects are updated.
      duration: '2160h',  // 90 days
      renewBefore: '1440h',  // renew 60 days before expiration
      privateKey: {
        algorithm: 'RSA',
        size: 4096,
        encoding: 'PKCS8',
      },
      issuerRef: {
        name: 'bootstrap-issuer',
        kind: 'ClusterIssuer',
      },
    },
  },
  {
    apiVersion: 'cert-manager.io/v1',
    kind: 'Issuer',
    metadata: {
      labels: {
        app: appName,
      },
      name: 'root-issuer',
      namespace: namespace,
    },
    spec: {
      ca: {
        secretName: 'root-certificate',  // pragma: allowlist secret
      },
    },
  },

];

{
  createClientCert: createClientCert,
  createServerCert: createServerCert,
  createCentralClientCert: createCentralClientCert,
  createCentralServerCert: createCentralServerCert,
  getInferenceIssuer: getInferenceIssuer,
  getIngressIssuer: getIngressIssuer,
  createPublicServerCert: createPublicServerCert,
  createPrivateKeyCert: createPrivateKeyCert,
  createNamespaceIssuer: createNamespaceIssuer,
}
