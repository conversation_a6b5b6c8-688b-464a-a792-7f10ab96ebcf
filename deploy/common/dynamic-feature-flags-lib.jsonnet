local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

local createLaunchDarklySecret = function(cloud, env, namespace, appName)
  // Map from cloud to env to secret.
  local sealed_sdk_keys = {
    GCP_US_CENTRAL1_PROD: {
      // Staging and prod are the same.
      STAGING: 'AgA/Bu7N+rmWZaUKaJNAXmIO3XdO7UqWZ4P00zZe3GOw2eXM3SqtI/ZHJAoeNlalGX3F5exJo4WDwjDzXojV6Adsf7M58pehLSgV5clPglX8URTIHm4gF7mbuCnuA54oDFGn4dGNjIyrpBYNoyTxK18gtjFOh4Bz0Ca4xD3hAYB6RAndPXtJrugmzwumOjwLt4mIwchqeGzgd0Wriktd1OBN7Oi6MQgrXjuzRpqqp0cWBEL/AM7QIdXQhqdryiGAV/miwhBkmhAxHgDymKucqicf64TOdIQCmr4ECIz+ry7HXhEa4l1/xXfWllwNVGauDKDd+wMOPlyWNkmM6Y8UoszumsMmlvr03hB6zphvRnewl4ac1P9oMOUbN1LM588Cm80VzFhOddeKqWYbVc4zcTAn8NibxjY9sDoj4X9tsRtMC/1kD6Be6S+Fk5eTpM1nTHhf+0LHW/pbh8XWhII9ZO5EKp+hVj83zqFfmzZlIkuSV9CqmfLq93xQ7eAGCTZF0/Jn/tsYWTsH0WHhmdcIYsTAEJvldnzWt37EJ+zYcmyXsm3FqTjLgPO6QBOEtbepLEwhRXuWPtpX/U2cOlIjX2pQiGRuE4WsYQfYkkgDb5tK5Ccu26/w6QvMmkn9lV4MQ0AOTACMOuWRL7yTTG90AICylqolDqXDn6h1Kg9mfTh1chUmjlYa/5zBtbw3vJPOJCV+Fvp/WI9VXYB/SW/Pd/DNTV99EpTe0VV8aNfabnvWbouFgKxYRkzf',  // pragma: allowlist secret
      PROD: 'AgA/Bu7N+rmWZaUKaJNAXmIO3XdO7UqWZ4P00zZe3GOw2eXM3SqtI/ZHJAoeNlalGX3F5exJo4WDwjDzXojV6Adsf7M58pehLSgV5clPglX8URTIHm4gF7mbuCnuA54oDFGn4dGNjIyrpBYNoyTxK18gtjFOh4Bz0Ca4xD3hAYB6RAndPXtJrugmzwumOjwLt4mIwchqeGzgd0Wriktd1OBN7Oi6MQgrXjuzRpqqp0cWBEL/AM7QIdXQhqdryiGAV/miwhBkmhAxHgDymKucqicf64TOdIQCmr4ECIz+ry7HXhEa4l1/xXfWllwNVGauDKDd+wMOPlyWNkmM6Y8UoszumsMmlvr03hB6zphvRnewl4ac1P9oMOUbN1LM588Cm80VzFhOddeKqWYbVc4zcTAn8NibxjY9sDoj4X9tsRtMC/1kD6Be6S+Fk5eTpM1nTHhf+0LHW/pbh8XWhII9ZO5EKp+hVj83zqFfmzZlIkuSV9CqmfLq93xQ7eAGCTZF0/Jn/tsYWTsH0WHhmdcIYsTAEJvldnzWt37EJ+zYcmyXsm3FqTjLgPO6QBOEtbepLEwhRXuWPtpX/U2cOlIjX2pQiGRuE4WsYQfYkkgDb5tK5Ccu26/w6QvMmkn9lV4MQ0AOTACMOuWRL7yTTG90AICylqolDqXDn6h1Kg9mfTh1chUmjlYa/5zBtbw3vJPOJCV+Fvp/WI9VXYB/SW/Pd/DNTV99EpTe0VV8aNfabnvWbouFgKxYRkzf',  // pragma: allowlist secret
      DEV: '',
    },
    GCP_US_CENTRAL1_DEV: {
      PROD: 'AgCl5CL5h9u9EtlWtv5fSrKnWB4vgyP0Oxe4MJ+O74BytUBHqdEV2W2Z5mOgHuxCAkZD4bP6GKiPVQ8HrtJaybDBLZO3t6QjSs3PY/2hRGKFNSB+FwQpnxPDlg/Q9awv/A6Ai4q1Pg5T83A9qjHqsXOGwBYQKyEZv1kqMu3CjrRZE9+By1lNyOkBV5kxQmzs5jgkKTwxK6DUeMOtq3IscXmPIpvP9jh25D3/TRqZENRdbS6oPjCnK2l596Fo+QpjdZqLkRQWuMALHOXIQemfF1kGiLEnEsolwwv49QPiQg1wJpKFr1iFjz5tZhO239oqWWqRy/ncgTXTCDni2Kh0769/pOZ6MLrTpR1dJPDyKjjezJAvMwULpqpm+FjabbR8/oUDXf+Y0Fyk/bB/5nZzoA9NNQgtMCT+VBvH1ayS9c9kuNWjYFcPAHFMlNghgSmXAb+fkA4NiHv/VZlpNyGVTu2RmB0wHh2WJTuo0B/wIiwgZntljrhmuj5x7v2+ulZgG20Q9UmhAukr1xw7HsP1zr/b/ii/H7UcckNSrJRp4kq1LDXkoPinHwXXM/WVDithGtPQsqFVNjFmMkf4Bi/OlPOImNYMzVnMyXttC/ceQoIfOz7ia1zaVxvdHBqJqFV4C3HUb7TOnSANGr12A7ZD2sBcm8/fQSPuHewLsxXTf+L1O1r6Y3CVPVItcB7j8Aq8T4LPw+8dk+sXZX0y2+EWfj5iNOCf4FEIwA+D+AVcQLwYppt5r9H2FI1g',  // pragma: allowlist secret
      // Staging and dev are the same.
      STAGING: 'AgBiDjo2WMRHPYXogLNfdYB2zRxi9CxZQEj1gclK/Gtjc1D6farIJtU9Dn4avAdtMb0dnPOfkFgf3DSH+ayq5+YEnMbONDNp8x6Qq7yKAn4ydFvLge4ubsmar0I49YQA9k6bPqKhqELzEKARWEs/bVFoHoC2jx1jVA/no9pJmIlDkodQbMRMFvXz0l+ClFGUyLhE/3d2ES/FkWgBP0fzQso0AgryNaYiXlC+ejfDSeMDAYQ7Mlg46ODHyGi7suhzU3p9xxzR6iy/IXq/Z3nvzABKlCIm6tsXRVC9sUlCCr2vjMk+3328920wbk+Fbzpl27bd7Mjpz5RKELGoyej/2XBqZW6hUGL6NbGm2rHOv4y+HahKVmZCClq6x8PRkfWdCTmM0FQgu35ugaeCRyKIYMQxh/SI655p9LOzr58mwXyQZituOO4yfe8U47h7lolRK4E7kJOiFBQRzT+bsYlvmEEP5FDw/9fojKLFsGMqzvQfpuQg2LEJ1jHzGLIeWsP4e23y8Xhj83j3GzOmZOYKiaxZrZay85K1I/fLWp/Vll1NjvaFGmVNprxKgGXdrkqq96B87Dr3+JGQdDzFYhmG37+Pyq3jSXiZLkTHEnswTGnprUnnszbVclkO77FqvFMdjDW2YOfS0eR3Y5/oST3OlNAvY4NAt9b3yALeD14OaotbQaxu8QCkSydkVRb05ihhli4zBOpZCmopNt7dvT+iU7NIYHOdPQaMPRkIzAetDLy2AXQPRq2/zA+u',  // pragma: allowlist secret
      DEV: 'AgBiDjo2WMRHPYXogLNfdYB2zRxi9CxZQEj1gclK/Gtjc1D6farIJtU9Dn4avAdtMb0dnPOfkFgf3DSH+ayq5+YEnMbONDNp8x6Qq7yKAn4ydFvLge4ubsmar0I49YQA9k6bPqKhqELzEKARWEs/bVFoHoC2jx1jVA/no9pJmIlDkodQbMRMFvXz0l+ClFGUyLhE/3d2ES/FkWgBP0fzQso0AgryNaYiXlC+ejfDSeMDAYQ7Mlg46ODHyGi7suhzU3p9xxzR6iy/IXq/Z3nvzABKlCIm6tsXRVC9sUlCCr2vjMk+3328920wbk+Fbzpl27bd7Mjpz5RKELGoyej/2XBqZW6hUGL6NbGm2rHOv4y+HahKVmZCClq6x8PRkfWdCTmM0FQgu35ugaeCRyKIYMQxh/SI655p9LOzr58mwXyQZituOO4yfe8U47h7lolRK4E7kJOiFBQRzT+bsYlvmEEP5FDw/9fojKLFsGMqzvQfpuQg2LEJ1jHzGLIeWsP4e23y8Xhj83j3GzOmZOYKiaxZrZay85K1I/fLWp/Vll1NjvaFGmVNprxKgGXdrkqq96B87Dr3+JGQdDzFYhmG37+Pyq3jSXiZLkTHEnswTGnprUnnszbVclkO77FqvFMdjDW2YOfS0eR3Y5/oST3OlNAvY4NAt9b3yALeD14OaotbQaxu8QCkSydkVRb05ihhli4zBOpZCmopNt7dvT+iU7NIYHOdPQaMPRkIzAetDLy2AXQPRq2/zA+u',  // pragma: allowlist secret
    },
    GCP_EU_WEST4_PROD: {
      PROD: 'AgCh9lD7wOF6EaHSF6aKCfQqcIcwVZtHCGIbxCHS6AiwsBUVTqkkY5O/7zz5kfb7qxAYC4Sp+XfixW7H6QWBC18oEStDNzvEEEmrAwC7xD6VauXnbNSm5nszDcU3FBHCRq4WUNfBMeU9sCdtBmvwF3OUzLD9V9MyL8dUoqFq/lTOHvlA/eKZ/+HhoTsU2aLuKaZh9YE05gD4UF3j1lFue0sYypaqKQqQgSqZn7i/AenTJnH2+jy9o36dOQW/XRnOOBESG/6Di7JYaBstfjzgLrFIdUjaMIWD/GvH3gD6O6xEQ2V9qiPiMdCon7gdz4wnxM74N8tbHmlvRYbi/rcCVTjHKoTByoi1CUVpRlDRnAWou6yZnfmBZ+6cYgI750XmHWtdI4bvg7ut272NRzuhUOWJZVuUhkfHx9U3VktTCVuprOPc7c6jd/G74P88dsJ26CzBeGZmJ01mmA7/73UVayws9hG1i4jkL0Q4Nf/V8ty9jEJW27t2lorx+Mj0rEMyI8iilyeN6+//FlX9xh3/J0APTYL0Ro/qNz6ikOySaiMvNiIl/RDh9jhWdBQIfMILieRSWoKov0Io0GGEA2P1nGOuBz8orai44mM14VsR5nGQvbEWuY1PESVsjufRyUiqI4+nXqxYMgy9bKtTtntRBCAC6m7p9x8xTiFSBUcwOqMi4cU+28enElMT34CC2nqdFq4QcytFZ2U8DxYBYcJ9Z98FmJoT8+9oC+vPgYAT4ogUCkPqQXJgtq7C',  // pragma: allowlist secret
      STAGING: 'AgCh9lD7wOF6EaHSF6aKCfQqcIcwVZtHCGIbxCHS6AiwsBUVTqkkY5O/7zz5kfb7qxAYC4Sp+XfixW7H6QWBC18oEStDNzvEEEmrAwC7xD6VauXnbNSm5nszDcU3FBHCRq4WUNfBMeU9sCdtBmvwF3OUzLD9V9MyL8dUoqFq/lTOHvlA/eKZ/+HhoTsU2aLuKaZh9YE05gD4UF3j1lFue0sYypaqKQqQgSqZn7i/AenTJnH2+jy9o36dOQW/XRnOOBESG/6Di7JYaBstfjzgLrFIdUjaMIWD/GvH3gD6O6xEQ2V9qiPiMdCon7gdz4wnxM74N8tbHmlvRYbi/rcCVTjHKoTByoi1CUVpRlDRnAWou6yZnfmBZ+6cYgI750XmHWtdI4bvg7ut272NRzuhUOWJZVuUhkfHx9U3VktTCVuprOPc7c6jd/G74P88dsJ26CzBeGZmJ01mmA7/73UVayws9hG1i4jkL0Q4Nf/V8ty9jEJW27t2lorx+Mj0rEMyI8iilyeN6+//FlX9xh3/J0APTYL0Ro/qNz6ikOySaiMvNiIl/RDh9jhWdBQIfMILieRSWoKov0Io0GGEA2P1nGOuBz8orai44mM14VsR5nGQvbEWuY1PESVsjufRyUiqI4+nXqxYMgy9bKtTtntRBCAC6m7p9x8xTiFSBUcwOqMi4cU+28enElMT34CC2nqdFq4QcytFZ2U8DxYBYcJ9Z98FmJoT8+9oC+vPgYAT4ogUCkPqQXJgtq7C',  // pragma: allowlist secret
    },
    GCP_US_CENTRAL1_GSC_PROD: {
      // Staging and prod are the same.
      PROD: 'AgAtcsHp64qUlQenoSEOhsk/tRnQDIZ8ru3xbuqNqApDPM4LPmC5nptdgb5D7//K8qGp55eHhtbfSNtF8inMhUWcCBIU8qVDkerxSFX+Q+L2NQMtF21maPriZazep9ds8rXnnWPDyNqEGA6PbDAgaXeTcK9fNk9Zpbv7ZAKddG8FxlErAh6py1hzqpYCjzphoTEj6igMVtCY8rrd/X49u9STLG0BBFj0NIAa53EnguX0NbidUrmFIrp9KFNcV65S+zW3JQJsBTejVZPw8Rp2nZpA0hbRXuQR92nNXBnVP/384r6xbXfZ6PUBDsY8GgYkUdcW8RMDjmwxG6HdgSePfpZc0aDfWwKVNXv2rQysvqaAr4sC7b64S24LQCWV9p3aifWf6g0wGiJp8vQDYTbFGleEozKld55t+2uoIlHvPUAjE1cT57lodULmcS9SlW65NCiLgnQh6tl9824RTIvp+GPGF/W/Ouspm+vOTXQYiEXsRya/lAdS4ECV24J/QcnN4QQ1/NokfUTIbR9xTaWUHErS8HyGevvy0rBTl7uqIVAxWmxno7zWhjuoGX0eh+XEENxZVumaCDU5Eu80IxsNe90i15mhAIuKD867VPBgsBYQORxsa9RiJmjK07uZ8/PMQMqHsz2Ll5etQ3vN1Jj8pXsINNsBwV9vN1rbIOJB0pnYcKyZ+yUnIzIeV1v/tyML7b6XAJw5sI0MxDkbsJ1FAhnUNzgh1ojAB8nEV/rVxdn4MuFN/QLF3cZI',  // pragma: allowlist secret
      STAGING: 'AgAtcsHp64qUlQenoSEOhsk/tRnQDIZ8ru3xbuqNqApDPM4LPmC5nptdgb5D7//K8qGp55eHhtbfSNtF8inMhUWcCBIU8qVDkerxSFX+Q+L2NQMtF21maPriZazep9ds8rXnnWPDyNqEGA6PbDAgaXeTcK9fNk9Zpbv7ZAKddG8FxlErAh6py1hzqpYCjzphoTEj6igMVtCY8rrd/X49u9STLG0BBFj0NIAa53EnguX0NbidUrmFIrp9KFNcV65S+zW3JQJsBTejVZPw8Rp2nZpA0hbRXuQR92nNXBnVP/384r6xbXfZ6PUBDsY8GgYkUdcW8RMDjmwxG6HdgSePfpZc0aDfWwKVNXv2rQysvqaAr4sC7b64S24LQCWV9p3aifWf6g0wGiJp8vQDYTbFGleEozKld55t+2uoIlHvPUAjE1cT57lodULmcS9SlW65NCiLgnQh6tl9824RTIvp+GPGF/W/Ouspm+vOTXQYiEXsRya/lAdS4ECV24J/QcnN4QQ1/NokfUTIbR9xTaWUHErS8HyGevvy0rBTl7uqIVAxWmxno7zWhjuoGX0eh+XEENxZVumaCDU5Eu80IxsNe90i15mhAIuKD867VPBgsBYQORxsa9RiJmjK07uZ8/PMQMqHsz2Ll5etQ3vN1Jj8pXsINNsBwV9vN1rbIOJB0pnYcKyZ+yUnIzIeV1v/tyML7b6XAJw5sI0MxDkbsJ1FAhnUNzgh1ojAB8nEV/rVxdn4MuFN/QLF3cZI',  // pragma: allowlist secret
    },
  };
  local secretName = appName + '-launchdarkly-sdk-key';
  {
    volumeMountDef: {
      name: 'launchdarkly-sdk-key',
      mountPath: '/launchdarkly-sdk-key',
      readOnly: true,
    },
    podVolumeDef: {
      name: 'launchdarkly-sdk-key',
      secret: {
        secretName: secretName,  // pragma: allowlist secret
      },
    },
    env:: [
      {
        name: 'POD_CLOUD',
        value: cloud,
      },
      {
        name: 'POD_ENV',
        value: env,
      },
      {
        // With this flag set to true, we require that feature flags are initialized
        // before any feature flags are used. This allows the research code base and
        // tests to use the defaults and to fail fast in production services.
        name: 'DYNAMIC_FEATURE_FLAGS_REQUIRE_INITIALIZATION',
        value: std.toString(true),
      },
    ],
    secretsFilePath: '/launchdarkly-sdk-key/sdk_key',
    k8s_objects:: [{
      kind: 'SealedSecret',
      apiVersion: 'bitnami.com/v1alpha1',
      metadata: {
        name: secretName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'sealedsecrets.bitnami.com/cluster-wide': 'true',
        },
      },
      spec: {
        template: {
          metadata: {
            name: secretName,
            annotations: {
              'sealedsecrets.bitnami.com/cluster-wide': 'true',
            },
          },
          type: 'Opaque',
        },
        encryptedData: {
          sdk_key: sealed_sdk_keys[cloud][env],
        },
      },
    }],
  };

// Mount the LaunchDarkly secret from GCP secret manager. We're migrating to use this instead of
// relying on sealed secrets.
local mountLaunchDarklySecret = function(cloud, env, namespace, appName, serviceAccount)
  local gcpSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='launchdarkly-sdk-key',
    overrideSecretName='%s-launchdarkly-sdk-key' % std.asciiLower(env),
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  gcpSecret + {
    env:: [
      {
        name: 'POD_CLOUD',
        value: cloud,
      },
      {
        name: 'POD_ENV',
        value: env,
      },
      {
        // With this flag set to true, we require that feature flags are initialized
        // before any feature flags are used. This allows the research code base and
        // tests to use the defaults and to fail fast in production services.
        name: 'DYNAMIC_FEATURE_FLAGS_REQUIRE_INITIALIZATION',
        value: std.toString(true),
      },
    ],
  }
;

{
  createLaunchDarklySecret:: createLaunchDarklySecret,
  mountLaunchDarklySecret:: mountLaunchDarklySecret,
}
