local cloudInfo = import 'cloud_info.jsonnet';
local namespaces = import 'eng-namespaces.jsonnet';
local eng = import 'eng.jsonnet';
local lib = import 'lib.jsonnet';
function(cloud)
  local coreResources =
    [
      'componentstatuses',
      'endpoints',
      'limitranges',
      'namespaces',
      'persistentvolumeclaims',
      'persistentvolumes',
      'pods',
      'pods/log',
      'pods/exec',
      'pods/portforward',
      'podtemplates',
      'replicationcontrollers',
      'resourcequotas',
      'secrets',
      'services',
      'events',
    ];
  local notPermissionOrSecretApiGroups = [
    'acme.cert-manager.io',
    'admissionregistration.k8s.io',
    'apiextensions.k8s.io',
    'apps',
    'artifactregistry.cnrm.cloud.google.com',
    'autoscaling',
    'bigquery.cnrm.cloud.google.com',
    'bigtable.cnrm.cloud.google.com',
    'batch',
    'bitnami.com',
    'cert-manager.io',
    'cloud.google.com',
    'container.cnrm.cloud.google.com',
    'core.cnrm.cloud.google.com',
    'customize.core.cnrm.cloud.google.com',
    'dns.cnrm.cloud.google.com',
    'eng.augmentcode.com',
    'filestore.cnrm.cloud.google.com',
    'jaegertracing.io',
    'keda.k8s.io',
    'keda.sh',
    'monitoring.googleapis.com',
    'monitoring.cnrm.cloud.google.com',
    'networking.gke.io',
    'networking.k8s.io',
    'policy',  // PodDisruptionBudget
    'priorityclasses.scheduling.k8s.io',
    'pubsub.cnrm.cloud.google.com',
    'rbac.authorization.k8s.io',
    'scheduling.k8s.io',
    'spanner.cnrm.cloud.google.com',
    'storage.cnrm.cloud.google.com',
    'storage.k8s.io',
  ];
  local apiGroups = [
    '',  // "" indicates the core API group
    'acme.cert-manager.io',
    'admissionregistration.k8s.io',
    'apiextensions.k8s.io',
    'apiregistration.k8s.io',
    'apps',
    'artifactregistry.cnrm.cloud.google.com',
    'autoscaling',
    'bigquery.cnrm.cloud.google.com',
    'bigtable.cnrm.cloud.google.com',
    'batch',
    'bitnami.com',
    'cert-manager.io',
    'cloud.google.com',
    'cloudidentity.cnrm.cloud.google.com',
    'compute.cnrm.cloud.google.com',
    'container.cnrm.cloud.google.com',
    'core.cnrm.cloud.google.com',
    'customize.core.cnrm.cloud.google.com',
    'dns.cnrm.cloud.google.com',
    'eng.augmentcode.com',
    'filestore.cnrm.cloud.google.com',
    'iam.cnrm.cloud.google.com',
    'jaegertracing.io',
    'keda.k8s.io',
    'keda.sh',
    'kms.cnrm.cloud.google.com',
    'logging.cnrm.cloud.google.com',  // logging is not per-se a permission or secret, but audit relevant
    'monitoring.googleapis.com',
    'monitoring.cnrm.cloud.google.com',
    'networking.gke.io',
    'networking.k8s.io',
    'pubsub.cnrm.cloud.google.com',
    'policy',  // PodDisruptionBudget
    'priorityclasses.scheduling.k8s.io',
    'rbac.authorization.k8s.io',
    'scheduling.k8s.io',
    'secretmanager.cnrm.cloud.google.com',
    'secrets-store.csi.x-k8s.io',
    'spanner.cnrm.cloud.google.com',
    'storage.cnrm.cloud.google.com',
    'storage.k8s.io',
  ];
  local deployRules = [
    // all rights in a single namespace
    {
      apiGroups: apiGroups,
      resources: ['*'],
      verbs: ['*'],
    },
  ];
  local deployRole = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRole',
    metadata: {
      name: 'deploy-role',
    },
    rules: deployRules,
  };

  local stagingRules = [
    {
      apiGroups: [''],
      // not 'secrets
      resources: [
        'pods',
        'services',
        'configmaps',
        'persistentvolumeclaims',
        'persistentvolumes',
        'nodes',
        'serviceaccounts',
        'pods/log',
        'pods/portforward',
        'events',
        'pods/exec',
      ],
      verbs: ['*'],
    },
    {
      apiGroups: ['iam.cnrm.cloud.google.com', 'rbac.authorization.k8s.io'],
      resources: ['*'],
      // not mutating verbs
      verbs: ['get', 'list', 'watch'],
    },
    {
      apiGroups: notPermissionOrSecretApiGroups,
      resources: ['*'],
      verbs: ['*'],
    },
  ];
  local createStagingRoles(namespace) = [{
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'Role',
    metadata: {
      namespace: namespace,
      name: 'staging-role',
    },
    rules: stagingRules,
  }, {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: 'staging-role-binding',
      namespace: namespace,
    },
    subjects: [
      {
        kind: 'User',
        name: '%<EMAIL>' % u.username,
      }
      for u in eng
    ],
    roleRef: {
      kind: 'Role',
      name: 'staging-role',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  }];
  local prodRules = [
    {
      apiGroups: [''],
      // Not 'secrets, pods/exec, or pods/portforward. See #inc_exec_in_prod; for some reason
      // get/list/watch on pods/exec and pods/portforward is enough to allow exec/forwarding, even
      // though those should require create.
      resources: [r for r in coreResources if (r != 'secrets' && r != 'pods/exec' && r != 'pods/portforward')],
      verbs: ['get', 'list', 'watch'],
    },
    {
      apiGroups: ['iam.cnrm.cloud.google.com', 'rbac.authorization.k8s.io'],
      resources: ['*'],
      // not mutating verbs
      verbs: ['get', 'list', 'watch'],
    },
    {
      apiGroups: notPermissionOrSecretApiGroups,
      resources: ['*'],
      verbs: ['get', 'list', 'watch'],
    },
  ];
  local createProdRoles(namespace) = [{
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'Role',
    metadata: {
      namespace: namespace,
      name: 'prod-role',
    },
    rules: prodRules,
  }, {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: 'prod-role-binding',
      namespace: namespace,
    },
    subjects: [
      {
        kind: 'User',
        name: '%<EMAIL>' % u.username,
      }
      for u in eng
    ],
    roleRef: {
      kind: 'Role',
      name: 'prod-role',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  }];
  local createDevRoles() =
    // creates rules that are very permissive expect deleting namespaces
    local devRules = [
      // allow all verbs in core resources except namespaces
      {
        apiGroups: [''],
        resources: [r for r in coreResources if r != 'namespaces'],
        verbs: ['*'],
      },
      // do not allow to delete namespaces
      // Note: GCP Editor will still grant that permission at this point
      {
        apiGroups: [''],
        resources: ['namespaces'],
        verbs: ['get', 'list', 'watch'],
      },
      // give all other permissions
      {
        apiGroups: [g for g in apiGroups if g != ''],
        resources: ['*'],
        verbs: ['*'],
      },
    ];
    [{
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'dev-role',
      },
      rules: devRules,
    }, {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'dev-role-binding',
      },
      subjects: [
        {
          kind: 'User',
          name: '%<EMAIL>' % u.username,
        }
        for u in eng
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'dev-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    }];
  local genieRules = [
    {
      apiGroups: [''],
      // including secrets
      resources: ['*'],
      verbs: ['*'],
    },
    {
      apiGroups: notPermissionOrSecretApiGroups,
      resources: ['*'],
      verbs: ['*'],
    },
    {
      apiGroups: [g for g in apiGroups if !std.member(notPermissionOrSecretApiGroups, g)],
      resources: ['*'],
      verbs: ['delete'],
    },
  ];
  // create a cluster-role for genie to use
  // the clusterrole will not be used with clusterolebindings, but attached to a single namespace
  // see https://kubernetes.io/docs/reference/access-authn-authz/rbac/#rolebinding-example
  local genieRole = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRole',
    metadata: {
      name: 'genie-role',
    },
    rules: genieRules,
  };
  local genieLimitedRules = [
    {
      apiGroups: [g for g in apiGroups if !std.member(notPermissionOrSecretApiGroups, g)],
      resources: ['*'],
      verbs: ['delete'],
    },
    {
      apiGroups: ['apps'],
      resources: ['deployments/scale'],
      verbs: ['patch', 'update'],
    },
    {
      apiGroups: [''],
      resources: ['pods/portforward'],
      verbs: ['create', 'get'],
    },
    {
      apiGroups: ['autoscaling'],
      resources: ['horizontalpodautoscalers'],
      verbs: ['get', 'list', 'watch', 'patch'],
    },
    {
      apiGroups: ['keda.sh'],
      resources: ['scaledobjects'],
      verbs: ['patch', 'update'],
    },
  ];
  local genieLimitedRole = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRole',
    metadata: {
      name: 'genie-limited-role',
    },
    rules: genieLimitedRules,
  };
  local createNamespaceRoles = function(namespace)
    if namespace.access_type == 'staging' then createStagingRoles(namespace.name)
    else if namespace.access_type == 'prod' then createProdRoles(namespace.name)
    else
      assert false : 'Invalid namespace access type: ' + std.toString(namespace);
      [];

  local standardRoles = [
    deployRole,
  ];
  if !cloudInfo.isProdCluster(cloud) then
    local glassbreakerUsers = std.filterMap(
      function(u) std.get(u, 'glassbreakerDev', false),
      function(u) u.username,
      eng
    );
    local glassBreakerRoleBinding =
      {
        apiVersion: 'rbac.authorization.k8s.io/v1',
        kind: 'ClusterRoleBinding',
        metadata: {
          name: 'dev-glassbreaker-role-binding',
        },
        subjects: lib.flatten([
          [
            {
              kind: 'User',
              name: '%<EMAIL>' % u,
            },
            if u != 'dirk' then {
              kind: 'User',
              name: '%<EMAIL>' % u,
            } else null,
          ]
          for u in glassbreakerUsers
        ]),
        roleRef: {
          kind: 'ClusterRole',
          name: 'deploy-role',
          apiGroup: 'rbac.authorization.k8s.io',
        },
      };
    local devRoles = createDevRoles();
    lib.flatten([
      standardRoles,
      devRoles,
      glassBreakerRoleBinding,
      genieRole,
      genieLimitedRole,
    ])
  else
    local glassbreakerUsers = std.filterMap(
      function(u) std.get(u, 'glassbreakerProd', false),
      function(u) u.username,
      eng
    );
    local createUserRoleBinding = function(users)
      {
        apiVersion: 'rbac.authorization.k8s.io/v1',
        kind: 'ClusterRoleBinding',
        metadata: {
          name: 'glassbreaker-role-binding',
        },
        subjects: lib.flatten([
          [
            {
              kind: 'User',
              name: '%<EMAIL>' % u,
            },
            if u != 'dirk' then {
              kind: 'User',
              name: '%<EMAIL>' % u,
            } else null,
          ]
          for u in users
        ]),
        roleRef: {
          kind: 'ClusterRole',
          name: 'deploy-role',
          apiGroup: 'rbac.authorization.k8s.io',
        },
      };
    local glassbreakerRoles = createUserRoleBinding(glassbreakerUsers);
    local namespaceRoles = std.flatMap(createNamespaceRoles, namespaces(cloud).all);
    lib.flatten([glassbreakerRoles, standardRoles, namespaceRoles, genieRole, genieLimitedRole])
