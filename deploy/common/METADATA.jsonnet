// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'namespaces',
      priority: 1000,  // this creates the namespaces, should it has to run first
      kubecfg: {
        target: '//deploy/common:kubecfg_namespace',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'mounts',
      kubecfg: {
        target: '//deploy/common:kubecfg_mounts',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'roles',
      kubecfg: {
        target: '//deploy/common:kubecfg_roles',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'eng-auth',
      kubecfg: {
        target: '//deploy/common:kubecfg_eng_auth',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      // Deploy eng GCP permission changes right after merging
      deployment_schedule_name: 'QUICK_DEPLOY',
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'security-audit',
      kubecfg: {
        target: '//deploy/common:kubecfg_security_audit',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#security',
        },
      },
    },
    {
      name: 'certs',
      kubecfg: {
        target: '//deploy/common:kubecfg_certs',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'external-dns',
      kubecfg: {
        target: '//deploy/common:kubecfg_external_dns',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'cert-manager-dev',
      kubecfg: {
        target: '//deploy/common:kubecfg_cert_manager_dev',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'cert-manager-prod',
      kubecfg: {
        target: '//deploy/common:kubecfg_cert_manager_prod',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'cert-manager-issuer',
      kubecfg: {
        target: '//deploy/common:kubecfg_cert_manager_issuer',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'cert-manager-crds',
      kubecfg: {
        target: '//deploy/common:kubecfg_cert_manager_crds',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'reloader',
      kubecfg: {
        target: '//deploy/common:kubecfg_reloader',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'keda',
      kubecfg: {
        target: '//deploy/common:kubecfg_keda',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}
