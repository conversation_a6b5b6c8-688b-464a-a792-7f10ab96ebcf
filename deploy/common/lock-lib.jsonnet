local createLock(appName, namespace, serviceAccountName) =
  local lock = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: '%s-lock' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    data: {
    },
  };
  local role = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'Role',
    metadata: {
      name: '%s-lock-role' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    rules: [
      {
        apiGroups: [
          'coordination.k8s.io',
        ],
        resources: [
          'leases',
        ],
        verbs: [
          'get',
          'watch',
          'list',
          'create',
          'update',
          'patch',
        ],
      },
    ],
  };
  local roleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-lock-binding' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccountName,
        namespace: namespace,
      },
    ],
    roleRef: {
      kind: 'Role',
      name: role.metadata.name,
      apiGroup: 'rbac.authorization.k8s.io',
    },
  };
  {
    objects: [lock, role, roleBinding],
    name: lock.metadata.name,
  };
{
  createLock:: createLock,
}
