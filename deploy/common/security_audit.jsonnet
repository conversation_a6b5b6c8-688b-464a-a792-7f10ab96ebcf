local cloudInfo = import 'cloud_info.jsonnet';
local securityUsers = [
  'sa<PERSON><PERSON><PERSON><PERSON>@augmentcode.com',
  'jonm<PERSON><PERSON><PERSON><PERSON>@augmentcode.com',
];
function(cloud)
  if !cloudInfo.isProjectLeadCluster(cloud) then [] else
    local projectId = cloudInfo[cloud].projectId;
    local roles = [
      'roles/viewer',
      'roles/iam.securityReviewer',
      'roles/bigquery.resourceViewer',
      'roles/bigquery.jobUser',
      'roles/bigquery.dataViewer',
      'roles/logging.viewer',
      'roles/compute.networkViewer',
      'roles/securitycenter.admin',
      'roles/securitycenter.settingsAdmin',
      'roles/iam.securityAdmin',
      'roles/resourcemanager.projectIamAdmin',
    ];
    local securityAuditPolicy = {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'iampolicy-soc-viewer',
        namespace: 'devtools',
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: 'project/%s' % projectId,
        },
        bindings: [
          {
            role: role,
            members: std.map(function(u)
              {
                member: 'user:%s' % u,
              }, securityUsers),
          }
          for role in roles
        ],
      },
    };
    local pentestRoles = [
      'roles/iap.httpsResourceAccessor',
      'roles/container.clusterViewer',
      'roles/container.viewer',
      'roles/errorreporting.viewer',
      'roles/logging.viewer',
      'roles/monitoring.viewer',
      // gives permissions ot see topics and subscription as well as health metrics
      // it doesn't give access to the data
      // see https://cloud.google.com/pubsub/docs/access-control
      'roles/pubsub.viewer',

      // allows engineers to see tables and the status information (load, data size)
      // doesn't give access to the data
      // see https://cloud.google.com/bigtable/docs/access-control#bigtable.viewer

      'roles/bigtable.viewer',
      'roles/compute.viewer',
      'roles/file.viewer',
    ];
    local pentestUsers = [];
    local pentestPolicy = {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'pentest-roles',
        namespace: 'devtools',
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: 'project/%s' % projectId,
        },
        bindings: [
          {
            role: role,
            members: std.map(function(u)
              {
                member: 'user:%s' % u,
              }, pentestUsers),
          }
          for role in pentestRoles
        ],
      },
    };
    local notPermissionOrSecretApiGroups = [
      'acme.cert-manager.io',
      'admissionregistration.k8s.io',
      'apiextensions.k8s.io',
      'apps',
      'artifactregistry.cnrm.cloud.google.com',
      'autoscaling',
      'bigquery.cnrm.cloud.google.com',
      'bigtable.cnrm.cloud.google.com',
      'batch',
      'bitnami.com',
      'cert-manager.io',
      'cloud.google.com',
      'container.cnrm.cloud.google.com',
      'core.cnrm.cloud.google.com',
      'dns.cnrm.cloud.google.com',
      'eng.augmentcode.com',
      'filestore.cnrm.cloud.google.com',
      'iam.cnrm.cloud.google.com',
      'jaegertracing.io',
      'keda.k8s.io',
      'keda.sh',
      'monitoring.googleapis.com',
      'monitoring.cnrm.cloud.google.com',
      'networking.gke.io',
      'networking.k8s.io',
      'policy',  // PodDisruptionBudget
      'priorityclasses.scheduling.k8s.io',
      'pubsub.cnrm.cloud.google.com',
      'rbac.authorization.k8s.io',
      'scheduling.k8s.io',
      'storage.cnrm.cloud.google.com',
      'storage.k8s.io',
    ];
    local coreResources =
      [
        'componentstatuses',
        'endpoints',
        'limitranges',
        'namespaces',
        'persistentvolumeclaims',
        'persistentvolumes',
        'pods',
        'pods/log',
        'pods/exec',
        'pods/portforward',
        'podtemplates',
        'replicationcontrollers',
        'resourcequotas',
        'secrets',
        'services',
        'events',
      ];
    local pentestRules = [
      {
        apiGroups: [''],
        // not 'secrets
        resources: [r for r in coreResources if r != 'secrets'],
        verbs: ['get', 'list', 'watch'],
      },
      {
        apiGroups: ['iam.cnrm.cloud.google.com', 'rbac.authorization.k8s.io'],
        resources: ['*'],
        // not mutating verbs
        verbs: ['get', 'list', 'watch'],
      },
      {
        apiGroups: notPermissionOrSecretApiGroups,
        resources: ['*'],
        verbs: ['get', 'list', 'watch'],
      },
      {
        apiGroups: [''],
        resources: ['pods/exec'],
        verbs: ['create'],
      },
    ];
    local pentestRole = {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        namespace: 'pentest',
        name: 'pentest-role',
      },
      rules: pentestRules,
    };
    local pentestRoleBinding = {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: 'epntest-role-binding',
        namespace: 'pentest',
      },
      subjects: [
        {
          kind: 'User',
          name: '%s@' % u,
        }
        for u in pentestUsers
      ],
      roleRef: {
        kind: 'Role',
        name: 'pentest-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    };
    [
      securityAuditPolicy,
      pentestPolicy,
    ] + if false then [
      pentestRole,
      pentestRoleBinding,
    ] else []
