local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

local createNamespaceMounts = function(cloud, namespace, appName='mounts') [
  // Return kubernetes objects to help pods that want to mount network file systems,
  // in this case, our network file system containing checkpoints, a.k.a network
  // file store.
  //
  // The pods will have to reference the PersistentVolumeClaim filestore-checkpoint-claim
  // to mount the checkpoints file system.

  local ip = std.get({
    GCP_US_CENTRAL1_DEV: '*************',
    // see https://console.cloud.google.com/filestore/instances/locations/us-central1-a/id/checkpoint-fs;tab=overview?project=system-services-prod
    GCP_US_CENTRAL1_PROD: '**********',
    // see https://console.cloud.google.com/filestore/instances/locations/europe-west4-a/id/checkpoint-fs-europe-west4;tab=overview?project=system-services-prod
    GCP_EU_WEST4_PROD: '*************',
    // see https://console.cloud.google.com/filestore/instances/locations/us-central1-a/id/checkpoint-fs;tab=overview?project=system-services-prod-gsc
    GCP_US_CENTRAL1_GSC_PROD: '**************',
  }, cloud, null);
  if ip != null then [
    {
      apiVersion: 'v1',
      kind: 'PersistentVolume',
      metadata: {
        labels: {
          app: appName,
        },
        name: 'filestore-checkpoint-pv-' + namespace,
      },
      spec: {
        capacity: {
          // FileStore CSI does not enforce capacity limits, but a value needs to be set here
          storage: '500Gi',
        },
        mountOptions: ['lookupcache=pos'],
        volumeMode: 'Filesystem',
        accessModes: ['ReadWriteMany'],
        persistentVolumeReclaimPolicy: 'Retain',
        storageClassName: '',
        csi: {
          driver: 'filestore.csi.storage.gke.io',
          volumeHandle: 'modeInstance/%s/checkpoint-fs/checkpoint_share' % cloudInfo[cloud].mainZone,
          volumeAttributes: {
            ip: ip,
            volume: 'checkpoint_share',
          },
        },
      },
    },
    {
      apiVersion: 'v1',
      kind: 'PersistentVolumeClaim',
      metadata: {
        labels: {
          app: appName,
        },
        name: 'filestore-checkpoint-claim',
        namespace: namespace,
      },
      spec: {
        accessModes: ['ReadWriteMany'],
        volumeMode: 'Filesystem',
        volumeName: 'filestore-checkpoint-pv-%s' % namespace,
        storageClassName: '',
        resources: {
          requests: {
            // FileStore CSI does not enforce capacity limits, but a value needs to be set here
            storage: '500Gi',
          },
        },
      },
    },
  ],
];

{
  createNamespaceMounts:: createNamespaceMounts,
}
