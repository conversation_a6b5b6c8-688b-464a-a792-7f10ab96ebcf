// jsonnet library to help scheduling a pod on a node with certain GPUs or other resources

// null value side is intentional-- this is only used as a set
local availableNodeTypes = {
  GCP_US_CENTRAL1_DEV: {
    prohibitGpu: null,
    premiumCpuHighmem: null,
    small: null,
    large: null,
  },
  GCP_US_CENTRAL1_PROD: {
    prohibitGpu: null,
    premiumCpuHighmem: null,
    large: null,
  },
  GCP_EU_WEST4_PROD: {
    prohibitGpu: null,
    premiumCpuHighmem: null,
    large: null,
  },
  GCP_US_CENTRAL1_GSC_PROD: {
    prohibitGpu: null,
    large: null,
  },
};
local getNodeType = function(cloud, resource)
  assert resource == null || std.objectHas(availableNodeTypes[cloud], resource) : "Invalid node type '%s' in cloud %s" % [resource, cloud];
  resource;

// function to return the right tolerations array given the node request.
//
// Tolerations are metadata that allow a pod to be scheduled and certain nodes that
// a pod would otherwise not (taints). This is not in itself a requirement to schedule a pod
// on such a node. See https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/
//
// Args:
// env: dev | staging | prod
// nodeType: null | premiumCpuHighmem | small | large
// count: number of requested CPUs / GPUs.
//     Maximal number depends on nodeType (large: 8, small: 1 or 4, premiumCpuHighmem: unused)
local gcpTolerations = function(env, nodeType, count=1)
  if nodeType == null then if env != 'DEV' then
    // H100 allowed if no gpu is needed and the environment is not dev
    [
      {
        key: 'nvidia.com/gpu',
        operator: 'Equal',
        value: 'present',
        effect: 'NoSchedule',
      },
      {
        key: 'gpu',
        operator: 'Equal',
        value: '8h100',
        effect: 'NoSchedule',
      },
    ] else []
  else if nodeType == 'prohibitGpu' then
    // explicitly schedule this on normal non-H100 nodes only (good for singleton pods that suffer from being preempted)
    []
  else if nodeType == 'premiumCpuHighmem' then
    assert count == 1;  // not a GPU allocation
    [
      {
        key: 'cpu-avx512fp16-highmem',
        operator: 'Equal',
        value: 'present',
        effect: 'NoSchedule',
      },
      {
        key: 'nvidia.com/gpu',
        operator: 'Equal',
        value: 'present',
        effect: 'NoSchedule',
      },
      {
        key: 'gpu',
        operator: 'Equal',
        value: '8h100',
        effect: 'NoSchedule',
      },
    ]
  else if nodeType == 'small' then
    assert count <= 4;
    local l4Tolerations = if count == 1 then
      [
        {
          key: 'nvidia.com/gpu',
          operator: 'Equal',
          value: 'present',
          effect: 'NoSchedule',
        },
        {
          key: 'gpu',
          operator: 'Equal',
          value: '1l4',
          effect: 'NoSchedule',
        },
      ]
    else
      [
        {
          key: 'nvidia.com/gpu',
          operator: 'Equal',
          value: 'present',
          effect: 'NoSchedule',
        },
        {
          key: 'gpu',
          operator: 'Equal',
          value: '4l4',
          effect: 'NoSchedule',
        },
      ];
    // currently we allow H100 in dev for small workloads as we
    // have more available H100 capacity than L4 capacity
    local h100Tolerations = if env == 'DEV' then [
      {
        key: 'gpu',
        operator: 'Equal',
        value: '8h100',
        effect: 'NoSchedule',
      },
    ] else [];
    l4Tolerations + h100Tolerations
  else
    // H100 allowed if explicitly requested
    assert nodeType == 'large';
    assert count <= 8;
    [
      {
        key: 'nvidia.com/gpu',
        operator: 'Equal',
        value: 'present',
        effect: 'NoSchedule',
      },
      {
        key: 'gpu',
        operator: 'Equal',
        value: '8h100',
        effect: 'NoSchedule',
      },
    ];
// function to return a node affinity for the given pod spec.
// See https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#affinity-and-anti-affinity
//
// An affinity is a scheduling requirement for a pod.
// Note that in some situations additional tolerations need to be set too.
local affinity = function(requiredMatchExpressions, preferredMatchExpressions, env, appName, cloud, antiAffinity=null) {
  nodeAffinity: {
    requiredDuringSchedulingIgnoredDuringExecution: if std.length(requiredMatchExpressions) > 0 then
      {
        nodeSelectorTerms: [{
          matchExpressions: requiredMatchExpressions,
        }],
      } else null,
    preferredDuringSchedulingIgnoredDuringExecution: if std.length(preferredMatchExpressions) > 0 then std.map(function(x)
      // prefer 2xlarge instead of 12xlarge if possible
      {
        weight: 1,
        preference: {
          matchExpressions: [x],
        },
      }, preferredMatchExpressions) else null,
  },
  // podAntiAffinity is used to prevent multiple pods from being scheduled on the same node.
  //
  // see https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  // see https://kubernetes.io/docs/tutorials/stateful-application/zookeeper/#tolerating-node-failure
  podAntiAffinity: if appName != null && env == 'PROD' then
    if antiAffinity == null && (cloud == 'GCP_US_CENTRAL1_GSC_PROD' || cloud == 'GCP_EU_WEST4_PROD') then {
      // For GSC, use preferred anti-affinity
      preferredDuringSchedulingIgnoredDuringExecution: [
        {
          weight: 100,
          podAffinityTerm: {
            labelSelector: {
              matchExpressions: [
                {
                  key: 'app',
                  operator: 'In',
                  values: [appName],
                },
              ],
            },
            topologyKey: 'kubernetes.io/hostname',
          },
        },
      ],
    } else if antiAffinity == false then {
      requiredDuringSchedulingIgnoredDuringExecution: [],
    } else {
      // For other clouds, keep required anti-affinity
      requiredDuringSchedulingIgnoredDuringExecution: [
        {
          labelSelector: {
            matchExpressions: [
              {
                key: 'app',
                operator: 'In',
                values: [appName],
              },
            ],
          },
          topologyKey: 'kubernetes.io/hostname',
        },
      ],
    } else null,
};
local gcpAffinity = function(env,
                             nodeType,
                             count=1,
                             extraRequiredMatchExpressions,
                             extraPreferredMatchExpressions,
                             appName,
                             cloud,
                             antiAffinity=null)
  local gpuMatchExpressions = if nodeType == 'large' then [{
    key: 'cloud.google.com/gke-accelerator',
    operator: 'In',
    values:
      [
        'nvidia-h100-80gb',
        'nvidia-h100-mega-80gb',
      ],
  }] else if nodeType == 'small' then [{
    key: 'cloud.google.com/gke-accelerator',
    operator: 'In',
    values:
      [
        'nvidia-l4',
        // Allow H100 in dev for small workloads as we
        // have more available H100 capacity than L4 capacity
      ] + if env == 'DEV' then ['nvidia-h100-80gb'] else [],
  }] else if nodeType == 'premiumCpuHighmem' then [{
    // https://cloud.google.com/kubernetes-engine/docs/how-to/node-auto-provisioning#custom_machine_family
    key: 'cloud.google.com/machine-family',
    operator: 'In',
    values:
      [
        'c3',
        'a3',
      ],
  }] else [];
  local spotMatchExpressions = [{
    key: 'cloud.google.com/gke-provisioning',
    operator: 'NotIn',
    values:
      [
        'spot',
      ],
  }];
  local requiredMatchExpressions = spotMatchExpressions + gpuMatchExpressions + extraRequiredMatchExpressions;
  local preferredMatchExpressions = if env == 'DEV' && nodeType == 'small' then [
    {
      key: 'cloud.google.com/gke-provisioning',
      operator: 'In',
      values:
        [
          'spot',
        ],
    },
    // prefer L4 over H100 even if H100 is allowed
    {
      key: 'cloud.google.com/gke-accelerator',
      operator: 'In',
      values:
        [
          'nvidia-l4',
        ],
    },
  ] else if env == 'DEV' && nodeType == null then [{
    key: 'cloud.google.com/gke-provisioning',
    operator: 'In',
    values:
      [
        'spot',
      ],
  }] + extraPreferredMatchExpressions else extraPreferredMatchExpressions;
  affinity(requiredMatchExpressions,
           preferredMatchExpressions,
           env=env,
           appName=appName,
           cloud=cloud,
           antiAffinity=antiAffinity);

local tolerations = function(env, resource, cloud, count=1)
  local nodeType = getNodeType(cloud, resource);
  gcpTolerations(env=env, nodeType=nodeType, count=count);

local affinity = function(env,
                          resource,
                          cloud,
                          count=1,
                          extraRequiredMatchExpressions=[],
                          extraPreferredMatchExpressions=[],
                          appName,
                          antiAffinity=null)
  local nodeType = getNodeType(cloud, resource);
  gcpAffinity(env=env,
              nodeType=nodeType,
              count=count,
              extraRequiredMatchExpressions=extraRequiredMatchExpressions,
              extraPreferredMatchExpressions=extraPreferredMatchExpressions,
              appName=appName,
              cloud=cloud,
              antiAffinity=antiAffinity);

local podDisruption = function(namespace, appName, env=env, minAvailable=null)
  {
    local min = if minAvailable == null then
      if env == 'DEV' then 0 else 1 else minAvailable,
    apiVersion: 'policy/v1',
    kind: 'PodDisruptionBudget',
    metadata: {
      name: '%s-pdb' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      minAvailable: min,
      selector: {
        matchLabels: {
          app: appName,
        },
      },
    },
  };
{
  tolerations: tolerations,
  // affinity: calculates the affinity for the given pod spec.
  // See https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#affinity-and-anti-affinity
  //
  // Args:
  //   env: the environment (DEV, STAGING, PROD)
  //   resource: the resource (gpu)
  //   cloud: the cloud (e.g. GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_STAGING, GCP_US_CENTRAL1_PROD)
  //   count: the number of resources e.g. gpu to request
  //   extraRequiredMatchExpressions: extra match expressions to add to the required match expressions
  //   appName: the name of the app (if the appName is set the pod anti-affinity is set)
  affinity: affinity,
  podDisruption: podDisruption,
}
