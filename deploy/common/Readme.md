# Common deployment

This directory contains Kubernetes deployment settings that are not related to as single application or namespace.

## Ingress

An [Ingress Controller](https://kubernetes.io/docs/concepts/services-networking/ingress/) is an object that manages
access to services in a cluster via HTTP/HTTPS. It is a more dynamic version of the build-in load balancer service class.

It allows (in contrast to an LB service) to direct different endpoints to different services.
This is used to implement OAuth authentication.

The ingress controller configured in `ingress.yaml` serves the eng.augmentcode.com domains and is able
to provide TLS for the connection (HTTPS).

To add a new service in the eng.augmentcode.com domain do:

- Add the Service with ClusterIP node type
- Add a new Ingress object for the service as listed below
- Add a CNAME route to the AWS Route53 configuration at [https://us-east-1.console.aws.amazon.com/route53/v2/hostedzones?region=us-east-1#ListRecordSets/Z09872551UKYWO5PYK0LD]. The CNAME needs to be the AWS load balancer external name, i.e. `ab20ec8d35b034d428044e8a407b2fac-59907d01eba1d334.elb.us-west-2.amazonaws.com
`

A template for a valid ingress configuration is:
```
{
  "apiVersion": "networking.k8s.io/v1",
  "kind": "Ingress",
  "metadata": {
    "annotations": {
      "nginx.ingress.kubernetes.io/auth-url": "https://oauth2.eng.augmentcode.com/oauth2/auth",
      "nginx.ingress.kubernetes.io/auth-signin": "https://oauth2.eng.augmentcode.com/oauth2/start?rd=/redirect/$http_host$escaped_request_uri"
    },
    "name": "<SERVICENAME>-ingress",
    "namespace": "monitoring"
  },
  "spec": {
    "ingressClassName": "nginx",
    "rules": [
      {
        "host": "<HOSTNAME>",
        "http": {
          "paths": [
            {
              "path": "/",
              "pathType": "Prefix",
              "backend": {
                "service": {
                  "name": "<SERVICENAME>",
                  "port": {
                    "number": <PORT>
                  }
                }
              }
            }
          ]
        }
      }
    ]
  }
};
```

The ingress controller serves public traffic.
Always ensure that the traffic is HTTPS encrypted (ensured by the ingress controller) and protected by the oauth proxy (see the template above).

## GCP
