local dffLib = import 'dynamic-feature-flags-lib.jsonnet';
local telemetryEnv = function(service_name, otlp_endpoint, attributes='') [
  {
    name: 'POD_NAME',
    valueFrom: {
      fieldRef: {
        fieldPath: 'metadata.name',
      },
    },
  },
  {
    name: 'POD_NAMESPACE',
    valueFrom: {
      fieldRef: {
        fieldPath: 'metadata.namespace',
      },
    },
  },
  // OTEL_ is short for OpenTelemetry
  {
    name: 'OTEL_EXPORTER_OTLP_ENDPOINT',
    value: otlp_endpoint,
  },
  {
    name: 'OTEL_SERVICE_NAME',
    value: service_name,
  },
  {
    name: 'OTEL_RESOURCE_ATTRIBUTES',
    value: 'service.namespace=$(POD_NAMESPACE),service.instance.id=$(POD_NAME)' + attributes,
  },
];
{
  telemetryEnv: telemetryEnv,
  collector: 'http://opentelemetry-collector.opentelemetry:4317/',
  collectorUri: function(env, namespace, cloud)
    'http://opentelemetry-collector.opentelemetry:4317/',
}
