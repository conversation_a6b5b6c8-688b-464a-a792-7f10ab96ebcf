// Create the Spanner instance(s) for a cluster.
function(cloud)
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];
  local appName = 'spanner-instance';

  local createInstance = function(env, namespace)
    local instanceName = 'spanner-%s-%s' % [cloudInfo.shortName, namespace];
    local numNodes = {
      DEV: 1,
      STAGING: 1,
      PROD: 1,
    }[env];

    {
      apiVersion: 'spanner.cnrm.cloud.google.com/v1beta1',
      kind: 'SpannerInstance',
      metadata: {
        name: instanceName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        config: 'regional-%s' % cloudInfo.region,
        displayName: instanceName,
        resourceID: instanceName,
        numNodes: numNodes,
        // This is intended to help get the ConfigConnector connection back in a good state after
        // an issue with setting both numNodes and processingUnits. Can be removed once the instance
        // is in a good state.
        processingUnits: null,
        // Disable backups in dev, which can block deletions. AUTOMATIC uses the default backup
        // schedule, which is a daily full backup.
        defaultBackupScheduleType: if env == 'DEV' then 'NONE' else 'AUTOMATIC',
        // We can upgrade later, so starting with standard edition for now.
        // https://cloud.google.com/spanner/docs/create-manage-instances#upgrade-edition
        // edition: 'STANDARD',
      },
    }
  ;

  local instances =
    if cloud == 'GCP_US_CENTRAL1_DEV' then
      [
        createInstance('DEV', 'central-dev'),
      ]
    else if cloud == 'GCP_US_CENTRAL1_PROD' then
      [
        createInstance('STAGING', 'central-staging'),
        createInstance('PROD', 'central'),
      ]
    else [];

  instances
