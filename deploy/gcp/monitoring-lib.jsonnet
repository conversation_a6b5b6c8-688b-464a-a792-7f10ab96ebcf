local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';

local pagerDutyNotificationByTeam = {
  default: {
    // This is the OG services notification channel
    external: 'projects/system-services-prod/notificationChannels/3794038724698564570',
  },
  'extended-context': {
    external: 'projects/system-services-prod/notificationChannels/3817996888476254611',
  },
  'next-edit': self.default,
  chat: {
    external: 'projects/system-services-prod/notificationChannels/6841939512463879854',
  },
  // Merge completion into services. The old pager duty channel is here:
  // { external: 'projects/system-services-prod/notificationChannels/5392150897461209130' }
  completion: self.default,
  insights: {
    external: 'projects/system-services-prod/notificationChannels/9426656783431764726',
  },
  'self-serve': {
    external: 'projects/system-services-prod/notificationChannels/907533864220882407',
  },
  'remote-agents': {
    external: 'projects/system-services-prod/notificationChannels/18128327917217573219',
  },
  // This channel is currently only for monitoring additional VSCode forks that users
  // try to use and only goes to Zhuoran. It is separate from self-serve to avoid
  // bothering other folks.
  'growth-auxiliary': {
    external: 'projects/system-services-prod/notificationChannels/8699282594404366534',
  },
};

// Helper function: metric label template for documentation string
local label(key) =
  '`${metric_or_resource.label.%s}`' % key;

/*
Sample condition:
{
  displayName: 'API Proxy Health',
  conditionPrometheusQueryLanguage: {
    duration: '0s',
    evaluationInterval: '60s',
    labels: { severity: 'warning' },
    query: 'sum by (namespace) (increase(api_proxy_health_check_status_total{status="success"}[%sm])) / sum by (namespace)(increase(api_proxy_health_check_status_total{}[%sm])) < 0.9' % [minutes, minutes],
  },
}
*/
local alertPolicy(cloud, condition, name, description, team='default', enableInDev=false) =
  // Alert policies are per-project not per-cluster so we only set them on the lead cluster
  assert cloudInfo.isLeadCluster(cloud);
  // Supported severities so far are _info_, _warning_, and _error_.
  // _info_ will not ping, good choice for experimental or low-signal alerts
  // _warning_ will create a pagerduty incident
  // _error_ will also create a pagerduty incident which will also create a slack channel
  assert std.objectHas(condition.conditionPrometheusQueryLanguage.labels, 'severity');
  local pdNotification = pagerDutyNotificationByTeam[team];
  local notificationChannels = if cloudInfo.isDevCluster(cloud) then []
  else if condition.conditionPrometheusQueryLanguage.labels.severity == 'error' || condition.conditionPrometheusQueryLanguage.labels.severity == 'warning' then [pdNotification]
  else if condition.conditionPrometheusQueryLanguage.labels.severity == 'info' then []
  else
    assert false : 'Unsupported severity %s' % condition.conditionPrometheusQueryLanguage.labels.severity;
    [];

  local spec = {
    alertStrategy: {
      autoClose: '604800s',  // 1 week
    },
    // Treat this as a no-op unary OR since promQL lets you compose conditions arbitrarily
    combiner: 'OR',
    conditions: [
      condition,
    ],
    displayName: condition.displayName,
    enabled: enableInDev || cloudInfo.isProdCluster(cloud),
    notificationChannels: notificationChannels,
    documentation: {
      content: description,
      mimeType: 'text/markdown',
    },
    severity: {
      'error': 'ERROR',
      warning: 'WARNING',
      info: 'WARNING',
    }[condition.conditionPrometheusQueryLanguage.labels.severity],
  };
  {
    apiVersion: 'monitoring.cnrm.cloud.google.com/v1beta1',
    kind: 'MonitoringAlertPolicy',
    metadata: {
      name: name,
      // TODO: understand where these vs the condition labels show up in GCP
      labels: condition.conditionPrometheusQueryLanguage.labels,
    },
    spec: spec,
  };

local alertTombstone(names, priority) =
  {
    name: 'monitoring-alert-policy-tombstones',
    priority: priority,
    kubecfg_tombstone: {
      object: [
        {
          apiVersion: 'monitoring.cnrm.cloud.google.com/v1beta1',
          kind: 'MonitoringAlertPolicy',
          name: name,
        }
        for name in names
      ],
      task: [
        {
          cloud: 'GCP_US_CENTRAL1_PROD',
          env: 'PROD',
          namespace: 'devtools',
        },
      ],
    },
  };

{
  alertPolicy: alertPolicy,
  alertTombstone: alertTombstone,
  label: label,
}
