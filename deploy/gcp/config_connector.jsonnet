local gcpInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
function(cloud)
  local cloudInfo = gcpInfo[cloud];
  local iamServiceAccountName = 'configconnector-sa@%s.iam.gserviceaccount.com' % cloudInfo.projectId;
  local apiGroups = [
    'artifactregistry.cnrm.cloud.google.com',
    'bigquery.cnrm.cloud.google.com',
    'bigtable.cnrm.cloud.google.com',
    'cloudidentity.cnrm.cloud.google.com',
    'compute.cnrm.cloud.google.com',
    'container.cnrm.cloud.google.com',
    'core.cnrm.cloud.google.com',
    'customize.core.cnrm.cloud.google.com',
    'dns.cnrm.cloud.google.com',
    'filestore.cnrm.cloud.google.com',
    'iam.cnrm.cloud.google.com',
    'kms.cnrm.cloud.google.com',
    'logging.cnrm.cloud.google.com',  // logging is not per-se a permission or secret, but audit relevant
    'monitoring.cnrm.cloud.google.com',
    'pubsub.cnrm.cloud.google.com',
    'secretmanager.cnrm.cloud.google.com',
    'spanner.cnrm.cloud.google.com',
    'storage.cnrm.cloud.google.com',
  ];
  local role = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRole',
    metadata: {
      name: 'cnrm-role',
    },
    rules: [{
      apiGroups: apiGroups,
      resources: ['*'],
      verbs: ['*'],
    }],
  };

  lib.flatten([
    role,
    {
      apiVersion: 'core.cnrm.cloud.google.com/v1beta1',
      kind: 'ConfigConnector',
      metadata: {
        name: 'configconnector.core.cnrm.cloud.google.com',
      },
      spec: {
        mode: 'namespaced',
        stateIntoSpec: 'Absent',
      },
    },
    if gcpInfo.isProjectLeadCluster(cloud) then {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'config-connector-roles-admin',
        namespace: 'devtools',
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: 'projects/%s' % cloudInfo.projectId,
        },
        bindings: [
          {
            role: 'roles/iam.roleAdmin',
            members: [
              { member: 'serviceAccount:%s' % iamServiceAccountName },
            ],
          },
          {
            role: 'roles/logging.admin',
            members: [
              { member: 'serviceAccount:%s' % iamServiceAccountName },
            ],
          },
        ],
      },
    } else null,
  ])
