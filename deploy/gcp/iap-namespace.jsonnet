// This file creates IAP-related objects in each namespace.
local namespaces = import 'deploy/common/eng-namespaces.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(cloud)
  local n = namespaces(cloud);
  lib.flatten([
    std.map(function(ns) gcpLib.createIapSecret(cloud, ns), ['devtools', 'monitoring'] +
                                                            std.map(function(ns) ns.name, n.eng + n.deployment)),
  ])
