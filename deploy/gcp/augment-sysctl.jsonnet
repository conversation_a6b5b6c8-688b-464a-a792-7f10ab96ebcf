// A DaemonSet to set host parameters
//
// tcp_retries2: default was 15 which was 924 seconds. That's great
// for SSH, but not so good for a modern web service.
function(cloud)
  [{
    apiVersion: 'apps/v1',
    kind: 'DaemonSet',
    metadata: {
      name: 'augment-sysctl',
      namespace: 'kube-system',
    },
    spec: {
      selector: {
        matchLabels: {
          app: 'augment-sysctl',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'augment-sysctl',
          },
        },
        spec: {
          priorityClassName: 'prod',
          hostPID: true,
          hostNetwork: true,
          initContainers: [
            {
              name: 'sysctl-modifier',
              image: 'alpine@sha256:beefdbd8a1da6d2915566fde36db9db0b524eb737fc57cd1367effd16dc0d06d',
              command: [
                '/bin/sh',
                '-c',
                'echo 7 > /proc/sys/net/ipv4/tcp_retries2',
              ],
              securityContext: {
                privileged: true,
              },
              volumeMounts: [
                {
                  name: 'host-proc',
                  mountPath: '/proc',
                  readOnly: false,
                },
              ],
              resources: {
                limits: {
                  cpu: '10m',
                  memory: '8Mi',
                },
                requests: {
                  cpu: '10m',
                  memory: '8Mi',
                },
              },
            },
          ],
          containers: [
            {
              name: 'pause',
              image: 'registry.k8s.io/pause@sha256:7031c1b283388d2c2e09b57badb803c05ebed362dc88d84b480cc47f72a21097',
              resources: {
                limits: {
                  cpu: '1m',
                  memory: '8Mi',
                },
                requests: {
                  cpu: '1m',
                  memory: '8Mi',
                },
              },
            },
          ],
          nodeSelector: {
            'kubernetes.io/os': 'linux',
          },
          tolerations: [
            {
              effect: 'NoSchedule',
              operator: 'Exists',
            },
            {
              effect: 'NoExecute',
              operator: 'Exists',
            },
          ],
          volumes: [
            {
              name: 'host-proc',
              hostPath: {
                path: '/proc',
                type: 'Directory',
              },
            },
          ],
        },
      },
    },
  }]
