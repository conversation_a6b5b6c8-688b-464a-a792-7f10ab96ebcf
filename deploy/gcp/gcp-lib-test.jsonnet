local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local bigtableTest() =
  local actual = gcpLib.createBigtableTable(
    cloud='GCP_US_CENTRAL1_DEV',
    env='PROD',
    app='api-proxy',
    namespace='default',
    tableName='test-table',
    columnFamily=['family1'],
    iamServiceAccountName='service-account-name',
  );
  local expected = {
    instanceName: 'bigtable-central-dev',
    instanceNamespace: 'central-dev',
    objects: [
      {
        apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
        kind: 'BigtableTable',
        metadata: {
          annotations:
            { 'cnrm.cloud.google.com/deletion-policy': 'abandon' },
          labels:
            { app: 'api-proxy' },
          name: 'default-test-table',
          namespace: 'default',
        },
        spec: {
          columnFamily: ['family1'],
          instanceRef: {
            name: 'bigtable-central-dev',
            namespace: 'central-dev',
          },
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          labels: { app: 'api-proxy' },
          name: 'bigtable-central-default-test-table-user',
          namespace: 'default',
        },
        spec: {
          bindings: [
            { members: [
              { memberFrom: { serviceAccountRef: { name: 'service-account-name' } } },
            ], role: 'roles/bigtable.user' },
          ],
          resourceRef: { kind: 'BigtableTable', name: 'default-test-table' },
        },
      },
    ],
    projectId: 'system-services-dev',
    tableName: 'default-test-table',
    tableNamespace: 'default',
  };
  std.assertEqual(actual, expected);
local bigtableinProdLegacyNamespaceTest() =
  local actual = gcpLib.createBigtableTable(
    cloud='GCP_US_CENTRAL1_PROD',
    env='PROD',
    app='api-proxy',
    namespace='observeinc',
    tableName='test-table',
    columnFamily=['family1'],
    iamServiceAccountName='service-account-name',
  );
  local expected = {
    instanceName: 'bigtable-observeinc',
    instanceNamespace: null,
    objects: [
      {
        apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
        kind: 'BigtableTable',
        metadata: {
          annotations:
            { 'cnrm.cloud.google.com/deletion-policy': 'abandon' },
          labels:
            { app: 'api-proxy' },
          name: 'test-table',
          namespace: 'observeinc',
        },
        spec: {
          columnFamily: ['family1'],
          instanceRef: {
            name: 'bigtable-observeinc',
            namespace: null,
          },
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          labels: { app: 'api-proxy' },
          name: 'bigtable-central-test-table-user',
          namespace: 'observeinc',
        },
        spec: {
          bindings: [
            { members: [
              { memberFrom: { serviceAccountRef: { name: 'service-account-name' } } },
            ], role: 'roles/bigtable.user' },
          ],
          resourceRef: { kind: 'BigtableTable', name: 'test-table' },
        },
      },
    ],
    projectId: 'system-services-prod',
    tableName: 'test-table',
    tableNamespace: 'observeinc',
  };
  std.assertEqual(actual, expected);
local bigtableinProdTest() =
  local actual = gcpLib.createBigtableTable(
    cloud='GCP_US_CENTRAL1_PROD',
    env='PROD',
    app='api-proxy',
    namespace='default',
    tableName='test-table',
    columnFamily=['family1'],
    iamServiceAccountName='service-account-name',
  );
  local expected = {
    instanceName: 'bigtable-central',
    instanceNamespace: 'central',
    objects: [
      {
        apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
        kind: 'BigtableTable',
        metadata: {
          annotations:
            { 'cnrm.cloud.google.com/deletion-policy': 'abandon' },
          labels:
            { app: 'api-proxy' },
          name: 'default-test-table',
          namespace: 'default',
        },
        spec: {
          columnFamily: ['family1'],
          instanceRef: {
            name: 'bigtable-central',
            namespace: 'central',
          },
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          labels: { app: 'api-proxy' },
          name: 'bigtable-central-default-test-table-user',
          namespace: 'default',
        },
        spec: {
          bindings: [
            { members: [
              { memberFrom: { serviceAccountRef: { name: 'service-account-name' } } },
            ], role: 'roles/bigtable.user' },
          ],
          resourceRef: { kind: 'BigtableTable', name: 'default-test-table' },
        },
      },
    ],
    projectId: 'system-services-prod',
    tableName: 'default-test-table',
    tableNamespace: 'default',
  };

  std.assertEqual(actual, expected);

[bigtableTest(), bigtableinProdTest(), bigtableinProdLegacyNamespaceTest()]
