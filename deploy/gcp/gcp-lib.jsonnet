// helper functions to create GCP objects
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

// creates a frontend config name
//
// Any frontend config created by createFrontendConfig should have this name.
local frontendConfigName(app) =
  '%s-frontend-config' % app;
// crates a frontend config with the default name
//
// it sets the correct ssl policy
local createFrontendConfig(app, cloud, namespace) =
  {
    apiVersion: 'networking.gke.io/v1beta1',
    kind: 'FrontendConfig',
    metadata: {
      name: frontendConfigName(app),
      namespace: namespace,
      labels: {
        app: app,
      },
    },
    spec: {
      // assumes the ssl policy is already created by node_groups.jsonnet
      sslPolicy: 'restricted-ssl-policy',
    },
  };
// see https://cloud.google.com/kubernetes-engine/docs/how-to/ingress-configuration#cloud_cdn for details about the cdn
local createBackendConfig(app, cloud, namespace, healthCheck=null, sessionAffinity=false, iap=false, cdn={ enabled: false }, timeoutSec=30, securityPolicy='ingress-ip-throttle') =
  {
    apiVersion: 'cloud.google.com/v1',
    kind: 'BackendConfig',
    metadata: {
      name: '%s-backend-config' % app,
      namespace: namespace,
      labels: {
        app: app,
      },
    },
    spec: {
      timeoutSec: timeoutSec,
      healthCheck: healthCheck,
      cdn: cdn,
      sessionAffinity: {
        // One of NONE, CLIENT_IP, or GENERATED_COOKIE
        affinityType: if sessionAffinity then 'CLIENT_IP' else 'NONE',
      },
      securityPolicy: {
        // This needs to match the GCE name. This is not
        // a reference to a Kubernetes object.
        name: securityPolicy,
      },
      iap: if iap then
        {
          enabled: true,
          oauthclientCredentials: {
            secretName: 'iap-oauth-secret',  // pragma: allowlist secret
          },
        }
      else {
        enabled: false,
      },
    },
  };
// Creates just an IAM service account without Kubernetes service account
// Args:
// - app: the name of the app
// - env: the environment (DEV, STAGING, PROD)
// - cloud: the cloud to deploy to
// - namespace: the namespace to deploy to
// - iamServiceAccountName: the name of the IAM service account
// - overridePrefix: if not null, use this prefix instead of the app name
// - dropClusterName: whether to always drop the cluster name in the IAM service
//   account name. This is a workaround to fix over 100 kubecfg test failures after
//   prepending cluster name to service account names of central namespaces. We need
//   to fix all tests before removing this workaround.
local createIAMServiceAccountOnly(
  app,
  env,
  cloud,
  namespace,
  iamServiceAccountName=null,
  overridePrefix=null,
  dropClusterName=true,
      ) =
  local iamServiceAccountNameArgument = iamServiceAccountName;
  // For Reasons, IAM service account names need to be <= 30 characters.
  local shortNamespace = std.substr(namespace, 0, 10);
  local prefix = if overridePrefix == null then app else overridePrefix;
  local iamServiceAccountName = if iamServiceAccountNameArgument == null then
    if dropClusterName || cloudInfo.isUniqueNamespace(cloud, env, namespace) then
      '%s-%s-iam' % [shortNamespace, prefix]
    else
      '%s-%s-%s-iam' % [cloudInfo[cloud].shortName, shortNamespace, prefix]
  else iamServiceAccountNameArgument;
  assert std.length(iamServiceAccountName) <= 30 : 'IAM service account names need to be <= 30 characters. [%s]' % iamServiceAccountName;
  local projectId = cloudInfo[cloud].projectId;
  local serviceAccountGcpEmailAddress = '%s@%s.iam.gserviceaccount.com' % [iamServiceAccountName, projectId];

  local iamObject = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMServiceAccount',
    metadata: {
      name: iamServiceAccountName,
      namespace: namespace,
      labels: {
        app: app,
      },
      // don't delete the underlying IAM service account as removing
      // them leaves a "deleted:" stump entry behind and there is a 1500
      // limit on the number of entries.
      annotations: if env == 'DEV' then {
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      } else {
      },
    },
    spec: {
      displayName: iamServiceAccountName,
    },
  };

  {
    // objects that the caller should return
    objects: [iamObject],
    // the email of the iam service account
    serviceAccountGcpEmailAddress: serviceAccountGcpEmailAddress,
    // the name of the iam service account
    iamServiceAccountName: iamServiceAccountName,
  };  // Creates a service account with possible GCP IAM integration
// Args:
// - app: the name of the app
// - cloud: the cloud to deploy to
// - namespace: the namespace to deploy to
// - iam: whether to enable IAM integration
// - iap: whether to allow the service account to use Identity Aware Proxy
// - overridePrefix: if not null, use this prefix instead of the app name
// - serviceAccountName: if not null, use this name instead of the default
// - iamServiceAccountName: if not null, use this name instead of the default
// - dropClusterName: whether to always drop the cluster name in the IAM service
// - abandon: iam accounts always abandoned, other services need to explicitly specify this.
//   account name. This is a workaround to fix over 100 kubecfg test failures after
//   prepending cluster name to service account names of central namespaces. We need
//   to fix all tests before removing this workaround.
local createServiceAccount(
  app,
  env,
  cloud,
  namespace,
  iam=false,
  iap=false,
  overridePrefix=null,
  serviceAccountName=null,
  iamServiceAccountName=null,
  dropClusterName=true,
  abandon=false,
      ) =
  local projectId = cloudInfo[cloud].projectId;
  local prefix = if overridePrefix == null then app else overridePrefix;
  local serviceAccountNameArgument = serviceAccountName;
  local serviceAccountName = if serviceAccountNameArgument == null then '%s-sa' % prefix else serviceAccountNameArgument;
  // the iam service account
  local iamServiceAccount = if iam then createIAMServiceAccountOnly(
    app=app,
    env=env,
    cloud=cloud,
    namespace=namespace,
    iamServiceAccountName=iamServiceAccountName,
    overridePrefix=overridePrefix,
    dropClusterName=dropClusterName,
  );
  local serviceAccountGcpEmailAddress = if iam then iamServiceAccount.serviceAccountGcpEmailAddress;
  local iamServiceAccountName = if iam then iamServiceAccount.iamServiceAccountName;

  local iamAnnotations = if iam then {
    'iam.gke.io/gcp-service-account': serviceAccountGcpEmailAddress,
    'eng.augmentcode.com/deletion-policy': 'abandon',  // always abandon iam accounts
  } else {};

  local abandonAnnotations = if abandon then {
    'eng.augmentcode.com/deletion-policy': 'abandon',
  } else {};

  local objects = [
    {
      apiVersion: 'v1',
      kind: 'ServiceAccount',
      metadata: {
        name: serviceAccountName,
        namespace: namespace,
        labels: {
          app: app,
        },
        annotations: iamAnnotations + abandonAnnotations,
      },
    },
  ];

  local iamObjects =
    if iam then
      iamServiceAccount.objects + [
        // workload identity binding kubernetes SA and IAM service account together
        {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMPolicy',
          metadata: {
            name: '%s-workload-identity' % prefix,
            namespace: namespace,
            labels: {
              app: app,
            },
            annotations: if env == 'DEV' then {
              'cnrm.cloud.google.com/deletion-policy': 'abandon',
            } else {
            },
          },
          spec: {
            resourceRef: {
              apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
              kind: 'IAMServiceAccount',
              name: iamServiceAccountName,
            },
            bindings: [
              {
                role: 'roles/iam.workloadIdentityUser',
                members: [
                  'serviceAccount:%(projectId)s.svc.id.goog[%(namespace)s/%(serviceAccountName)s]' % {
                    namespace: namespace,
                    projectId: projectId,
                    serviceAccountName: serviceAccountName,
                  },
                ],
              },
            ] + if iap then [
              {
                role: 'roles/iam.serviceAccountTokenCreator',
                members: [
                  'serviceAccount:%(projectId)s.svc.id.goog[%(namespace)s/%(serviceAccountName)s]' % {
                    namespace: namespace,
                    projectId: projectId,
                    serviceAccountName: serviceAccountName,
                  },
                ],
              },
            ] else [],
          },
        },
      ]
    else [];
  {
    // objects that the caller should return
    objects: objects + iamObjects,
    // the name of the kuberentes service account
    name: serviceAccountName,
    // the name of the iam service account or null if IAM integration wasn't requested
    iamServiceAccountName: iamServiceAccountName,
    // the email of the iam service account of null if IAM intergration wasn't requested
    serviceAccountGcpEmailAddress: serviceAccountGcpEmailAddress,
    // kubernetes service account objects
    serviceAccountObjects: objects,
    // IAM objects
    workloadIdentityObjects: iamObjects,
  };

// Grants a service account permission to impersonate another service account
// Args:
// - app: the name of the app
// - env: the environment (DEV, STAGING, PROD)
// - cloud: the cloud to deploy to
// - namespace: the namespace where the impersonating service account is located
// - impersonatorServiceAccountName: the name of the service account that needs to impersonate
// - impersonatorServiceAccountEmail: the email of the service account that needs to impersonate
// - targetServiceAccountName: the name of the service account to be impersonated
// - targetNamespace: the namespace where the target service account is located
local grantServiceAccountImpersonation(
  app,
  env,
  cloud,
  namespace,
  impersonatorServiceAccountName,
  impersonatorServiceAccountEmail,
  targetServiceAccountName,
  targetNamespace
      ) =
  local projectId = cloudInfo[cloud].projectId;

  local impersonationName = '%s-imp-%s-%s' % [
    std.substr(impersonatorServiceAccountName, 0, 10),
    std.substr(targetServiceAccountName, 0, 10),
    std.substr(std.md5('%s-%s-%s' % [impersonatorServiceAccountName, targetServiceAccountName, cloud]), 0, 5),
  ];

  {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPolicyMember',
    metadata: {
      name: impersonationName,
      namespace: namespace,
      labels: {
        app: app,
      },
    },
    spec: {
      member: 'serviceAccount:%s' % impersonatorServiceAccountEmail,
      role: 'roles/iam.serviceAccountTokenCreator',
      resourceRef: {
        kind: 'IAMServiceAccount',
        external: 'projects/%s/serviceAccounts/%s@%s.iam.gserviceaccount.com' % [
          projectId,
          targetServiceAccountName,
          projectId,
        ],
      },
    },
  };

local BIGTABLE_LEGACY_NAMESPACES = [
  'aitutor-turing',
  'observeinc',
];

local getBigtableTable(cloud, env, namespace, tableName) =
  local projectId = cloudInfo[cloud].projectId;
  if cloud == 'GCP_US_CENTRAL1_DEV' || env == 'DEV' then
    local instanceName = if std.startsWith(namespace, 'test-') then 'bigtable-central-test' else 'bigtable-central-dev';
    {
      // the table name to use
      tableName: '%s-%s%s' % [namespace, tableName, if std.startsWith(namespace, 'test-') then '-test' else ''],
      // the project id
      projectId: projectId,
      // the name of the bigtable instance
      instanceName: instanceName,
      instanceNamespace: 'central-dev',
      // namespace of the object returned as a convenience for grantBigtableAccess
      tableNamespace: namespace,
    }
  else
    if std.count(BIGTABLE_LEGACY_NAMESPACES, namespace) > 0
       && cloud == 'GCP_US_CENTRAL1_PROD'  // legacy namespaces only exist in GCP_US_CENTRAL1_PROD
    then
      local instanceName = 'bigtable-%s' % namespace;
      {
        // the table name to use
        tableName: tableName,
        // the project id
        projectId: projectId,
        // the name of the bigtable instance
        instanceName: instanceName,
        instanceNamespace: null,
        // namespace of the object returned as a convenience for grantBigtableAccess
        tableNamespace: namespace,
      }
    else if env == 'STAGING' then
      assert namespace != 'central';
      local instanceName = if cloudInfo.isLeadCluster(cloud) then 'bigtable-central-staging' else 'bigtable-%s-central-staging' % [cloudInfo[cloud].shortName];
      {
        // the table name to use
        tableName: '%s-%s' % [namespace, tableName],
        // the project id
        projectId: projectId,
        // the name of the bigtable instance
        instanceName: instanceName,
        instanceNamespace: 'central-staging',
        // namespace of the object returned as a convenience for grantBigtableAccess
        tableNamespace: namespace,
      }
    else
      assert env == 'PROD';
      assert namespace != 'central-staging';
      local instanceName = if cloudInfo.isLeadCluster(cloud) then 'bigtable-central' else 'bigtable-%s-central' % [cloudInfo[cloud].shortName];

      {
        // the table name to use
        tableName: '%s-%s' % [namespace, tableName],
        // the project id
        projectId: projectId,
        // the name of the bigtable instance
        instanceName: instanceName,
        instanceNamespace: 'central',
        // namespace of the object returned as a convenience for grantBigtableAccess
        tableNamespace: namespace,
      };
// Creates a Bigtable table and a matching IAM policy for the service account given.
//
// see https://cloud.google.com/config-connector/docs/reference/resource-docs/bigtable/bigtabletable
// for details.
//
// Args:
// - cloud: the cloud to deploy to
// - env: the environment to deploy to
// - namespace: the namespace to deploy to
// - app: the name of the app. The app name is added to the labels of all created objects.
// - tableName: the name of the table
// - columnFamily: a list of column families. This is a group of columns within a table which share a common configuration.
// - iamServiceAccountName: the name of the iam service account
local createBigtableTable(cloud, env, namespace, app, tableName, columnFamily, iamServiceAccountName) =
  local tableInfo = getBigtableTable(cloud, env, namespace, tableName);
  local fullTableName = 'projects/%(projectId)s/instances/%(instanceName)s/tables/%(tableName)s' % tableInfo;
  local objects = [
    {
      apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
      kind: 'BigtableTable',
      metadata: {
        name: tableInfo.tableName,
        namespace: namespace,
        annotations: if env != 'DEV' then {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        } else {
        },
        labels: {
          app: app,
        },
      },
      spec: {
        columnFamily: columnFamily,
        instanceRef: {
          name: tableInfo.instanceName,
          namespace: tableInfo.instanceNamespace,
        },
      },
    },
    if iamServiceAccountName == null then null else
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'bigtable-central-%s-user' % tableInfo.tableName,
          namespace: namespace,
          labels: {
            app: app,
          },
        },
        spec: {
          resourceRef: {
            kind: 'BigtableTable',
            name: tableInfo.tableName,
          },
          bindings: [
            {
              role: 'roles/bigtable.user',
              members: [
                {
                  memberFrom: {
                    serviceAccountRef: {
                      name: iamServiceAccountName,
                    },
                  },
                },
              ],
            },
          ],
        },
      },
  ];
  tableInfo + {
    objects: objects,
  };
// grants the given service account access to the given table
//
// Args:
// - cloud: the cloud to deploy to
// - env: the environment to deploy to
// - namespace: the namespace to deploy to
// - app: the name of the app. The app name is added to the labels of all created objects.
// - table: the output of getBigtableTable
// - iamServiceAccountNames: the names of the service accounts to grant access to the table
// - nameSuffix: a suffix to add to the name of the policy. The name suffix is important to ensure that the policy is unique. No two policies can have the same name.
//    So nameSuffix should be unique per table.
// - role: the role to grant the service accounts. Defaults to 'roles/bigtable.user'
local grantBigtableAccess(cloud, env, namespace, app, table, iamServiceAccountNames, nameSuffix, role='roles/bigtable.user') =
  assert nameSuffix != 'user' : "nameSuffix should not be 'user' as this is the default role";
  [{
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: 'bigtable-%s-%s' % [table.tableName, nameSuffix],
      namespace: namespace,
      labels: {
        app: app,
      },
    },
    spec: {
      resourceRef: {
        kind: 'BigtableTable',
        name: table.tableName,
        namespace: table.tableNamespace,
      },
      bindings: [
        {
          role: role,
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: iamServiceAccountName,
                },
              },
            }
            for iamServiceAccountName in iamServiceAccountNames
          ],
        },
      ],
    },
  }];
local createBigtableGc(cloud, env, namespace, appName, bigtable, columnFamilies, gcRules) = {
  objects:
    [{
      apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
      kind: 'BigtableGCPolicy',
      metadata: {
        name: '%s-%s-gc' % [bigtable.tableName, std.asciiLower(columnFamily)],
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        columnFamily: columnFamily,
        instanceRef: {
          name: bigtable.instanceName,
          namespace: bigtable.instanceNamespace,
        },
        tableRef: {
          name: bigtable.tableName,
          namespace: bigtable.tableNamespace,
        },
        gcRules: std.manifestJson(gcRules),
      },
    } for columnFamily in columnFamilies],
};
local grantAccess(name, appName, env, namespace, resourceRef, bindings, abandon=false) =
  {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: name,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: if abandon then {
        'eng.augmentcode.com/deletion-policy': 'abandon',
      } else {},
    },
    spec: {
      resourceRef: resourceRef,
      bindings: bindings,
    },
  };

local cloudIdentityGroup(namespace, appName, groupName, groupDescription) = {
  groupEmail: '%<EMAIL>' % groupName,
  objects: [
    {
      apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
      kind: 'CloudIdentityGroup',
      metadata: {
        name: groupName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        description: groupDescription,
        parent: 'customers/C02kn0kha',
        groupKey: {
          id: '%<EMAIL>' % groupName,
        },
        // WITH_INITIAL_OWNER means that whoever creates the group (i.e., configconnector) becomes the
        // owner of the group.
        initialGroupConfig: 'WITH_INITIAL_OWNER',
        labels: {
          'cloudidentity.googleapis.com/groups.discussion_forum': '',
        },
      },
    },
  ],
};
local cloudIdentityGroupMembership(namespace, appName, groupName, memberUsername) = {
  apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
  kind: 'CloudIdentityMembership',
  metadata: {
    name: '%s-%s-membership' % [groupName, memberUsername],
    namespace: namespace,
    labels: {
      app: appName,
    },
  },
  spec: {
    groupRef: {
      name: groupName,
      namespace: namespace,
    },
    preferredMemberKey: {
      id: '%<EMAIL>' % memberUsername,
    },
    roles: [
      {
        name: 'MEMBER',
      },
    ],
  },
};


local createIapSecret(cloud, namespace, appName='iap-secret') = [
  // secret for the IAP in GCP clusters
  // this sealed secret is not the secret data itself, but an encryped version that can only
  // be used in that GKE cluster.
  // the secret is created as cluster-wise. It can be used in different namespaces
  {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      labels: {
        app: appName,
      },
      namespace: namespace,
      name: 'iap-oauth-secret',
      creationTimestamp: null,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      template: {
        metadata: {
          name: 'iap-oauth-secret',
          creationTimestamp: null,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
      },
      // the client_id is not per se secret, but needs to be in the same secret object
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          client_id: 'AgBIYYOIyKNu4WcBpIkgsDFIVl2SBWWoFQ+Aav5R4NjWDhjlpKDJgsCX91TSQCe+OlptPejmctksOlRaOrtC34Xb0NUB2qgjZOXup9oRR4MLGJDEaS7+pyS1dMK4Rx2WS+73VRdIfHEeLWT9syCwU3GFavl8vjnETAZ9WtlOCGABv/uW01jt6ceXxuZiCZ67w4ntZ4d8tX0599FmmR7q4QpgqUvmZEJAs0vrokX1Lt306TEUTuyJknWbbQwz7HekrGWgMGHwSBg1jw515dgOTJCz8wmW4UwOXyFS6eal+R0Dbvfb4pfBUsr8J3NmP+xdcaBZlfgJOwhhYA3t2ijch4ZjhgyHXFA8nuYeihJ6fQ6P78VOtLU3w1vSKsJ0vVKsXkgWHeVPkWQN/yPhHY5glmJZEXEWHKFGUvgEuzmuu28GgXwMffQfgTYBSZ5efSvuIow3eiohl4iN8mmpIgkXnXdvAqWrP/eyAz8xCBAtfn5Xm+VKKEhdobptg2iFm/5M4cTi3CLPPKxmMCp1D2Sp2eFQ8turbf1yVSwgw+nmuNPZtkWKb5kftI7q3/KbYwFngtemYd0v0bI+ysImLK+4nkJLYY0f1bDd/v4zK7mDSgVPw1S+pq+gnaWurWkKOe5kdukiVB8xFtpywfHMZI8fHYExBrdC9JIWMz6dIK1VYdO7b9I1MYBPVOp0R1xeJLjfgvq6eB8UvGC6M7x2H+gX5/bQEGrx6ENSa3qZ2oUGq3omWBnnvbaSPdErly3IqWTKTwZrt23IFS74Jfs+OABfY1rTmB6nptZ5jFw=',  // pragma: allowlist secret
          client_secret: 'AgA1uCwjlJ8dWjWD4/CDW/dsBbAyXO4CDNCoJLrMaaP+dKUlCfNoK2MheHzVKnpawsYKhVBbFH7bc4dZzpfw+ULEQ7BDFxFI0K4PJX1j+deioGR9VU666rnEDsG6p2DlWSjO1MwqMbZDHAM0CiHR1LVRTS9piKzC6UbQizF1jJ7n4lRarG4TbFffo5MpUyajeYW4kJ5XugbAaIJTk3LHaNXsaMzQb/CbqAF+j7+BACYKd2lr29CyPESCDPGTYBP/VmkHhBY1iD5dlAO1qLEwJQvH5z3kyoKBB1SLoTvJgQD0upESJi7L4fxjI10CvCjgxJzisCzT8XcteoYZGMrOnRv7siU92j9WsW4xKtm00VaJ9abYg8a51KnrkR5tN3Mbe/MX36GvltY7fxkLU28TwUqMEZaD4U8F9WuZTjoM91H3TDadLZitFrWq+8TGr4JYYDlz2WCIUB1jf/FP4BAr8cNEAf/awIpCocPawXE0Rzish/+aT1dOdANqlrLRN/x+tJw8JAlNnmGlxjwr9858zcZdlLRPawyiZPzc6cmTkerXVAEKeLVIPA1f/ZJ97OQ31+ZSFP2K2f5HWysKJLswvXzVxKvT8uQFLw3Mmlr/x9HekNhATVAbpQBQC5L9LNirTOSIf6+sogHc2kIi05Mf1othDd0Uesx9150deTfACsTEw7gp8AcmqeYwXvZ9i03sDjwff7JWT2J9T9jbQO0GAq7IkY5e2adjUPx+DrVaDmTYDxXmXA==',  // pragma: allowlist secret
        },
        GCP_US_CENTRAL1_PROD: {
          client_id: 'AgA3NIYrLbz4y+55VSBO23iJ6VvbKrdZbuk2zm0aOprTEfJzR4D1KDBGtlHGzWzy9lolyVKf3W/9TFslqeiToxFLn2mK/BgRue3sUwjMVlwkljUbYAfXCcSdHCuju56wb1/QQflyIHE9aCnW2f2uqmUq8xwuLYpMKJ42invugLKFJxXKh8ZDk37ee+IBZ5gl5U+2OvByj0e9tcniJo7R4rl8l66nZKBZOVU9aHsNKXwhGtVkc0EgfNJ0mR++GZqdIoRduK0qL9jKyPkvDjv/kO6LY2RG2ZqgYkiRsIEDdVVMAbWy0DM+4GYRR4QgdAFLDNbkIRXsHkSr53iiPJLdE7rnDvYFbxcCWySvHpbKyAO1e7l+M9FgThu7C+boGJRulN8rG6XGbJqXurx6k0GRCOd+ij34F4ixlodyMl06zXsW0icG4mRBjZ4Od/f7OktenYGO+JlMKdvUSIZA2QuIDnUezSXeBjX8cqAWYBuSEYgvK9zd7QdFTYu5gWyiYb/ReTwOspsgIxM0Z6EHKMQR9g/dRnEu4PXlCQnN2A49ckLlNTnz5hQ6Bv+NpP/TekQfN11302E67+DAforwMyex6xFzkuXo4skS5vvKgCFA69gt85cAB/086cJ4sxG2R/6ci/zM2DgtLB8Sr3NU1J8KAddg7HHfQGQ8uC4y0C1Wgx57+xGD2XPkTSsoyFhs+8Z43Qzp6LYJ3+bzDmWrXfWlx23JYWHiawTM0EQE7d1WnYMdmEviXBx39FtzXuXqLy/XMcNakbKg0UDBZ0jctoMCGG00KqWnhmIM6o4=',  // pragma: allowlist secret
          client_secret: 'AgCxuKoISA4kcH9WLSM3oHs4fttv1w0VcP53+DAZkyFL4Wk6F0GayWAUQTdh1hS3AUFA15ZfYn6kc29wFeWwZ2SgjVCk7WWc/UQy22CoP7eXB822oDU8d9MQ/x1anx73LeplU5zrkmfrgmP3Oof6WEwvV2vCuqT/gn0Hz9bxv3zxF4AKRDqqRY/B3QB0UQD3XI7XwMOUE7JfrPPxiRYh8i5kLvUN51lwBBP1+Q9ZeIPJZXyw8vQWktCF214tdEKlPdNjcSK+dviKc+L1ztGU3blchWvrulw1+WKqFMsJZpz/2lBunuEN2B49WEG5EiCy4ch25fAGG+3Exqup65bHphpRuDt7Yq5BOKjNZWHAf5Z7o87sw0OR7vhQpAegtQeT/BsR8xLfVjalebHv9stZXso5XM+ddRQxJ689iYUdRS1BF7n8If7vxoRGABqSCUJMPRFIgTr0w0tGGmQU3RMBH8Y4anBvv+hYuBEhq26vs4ZwkgH5CffNxgLdHORVjS9KIUvmwYMBMrPS9mPX5cLRdQ0nmEJMFF6eCqVGsIxM02sLgLLtAqW4IPXNAeMiLlwgOmYW2uqUWcICtH3qjzmerqG3SuiIY/blRSaXS8TILjLfPEp0iGLeAxHNqDKvK9KwW9Nc7zYJd6G3a+PPQXdX58Mrx9TMvEa3XvvzphbhDtyPy0LcdRbZUkQnQqViGUiuuzbmeIX2PhEGft9Jtkf/aNPV/ZTDwKN10RKpruH14wn4G8dtcg==',  // pragma: allowlist secret
        },
        GCP_EU_WEST4_PROD: {
          client_id: 'AgCugBqEPviCWcdjKJyoCxXkzGPfiNtrq2TdxiP2KRSL6hVI19OrZy4QCHXvMLCqNc/dCnEWLyTUCpNLpxYve4fKFxP62gxG3flzIgQhcgsryfwvI/cmqDMqPaJiTbWnthvAvBV69Y30+U9k6jxW1SyvB6+MU8uUbuPpFO6iS8kSVVWIp86u+Vbpx2L2gjPS/KHw10MbfZNfPu6p0LUzM5PbkijCZnUNm0UjUA14m8uQBAdavB6ZlECEnvTuUMt9ZC8wh0dZl3wLvkrg7DAFQI5vKkXj4RZoM62Qi3Ixq/DLdXiKHRDRmpdG2PPAeZf41jAavftWO3OGxDJOlI/+0ZabapEMEC3ImOxO5W0xs/PRSHr2JrivuweHvKs4URiAevYMzlm7ybyjBTYzk8McCiAywi6AndICe+sr7gMOaIm1K44S0XVhrR5vBflfhTsWXKZYrOoOvIAR163JcHCB51XbIXtIYuOaGaPvicSppAh/eN5hHiQAiP1D3tiUiaBl3W8ozxA9Z3KQCQt7RXRgyuRmixTQDEo7UDyVIKz6sNsjoxREBJDF1+w8krdkHQf0MV/INybr93oD9R7jWa+x9JfSY70QChVwWg5kpisSDgVLf7Oznhh4hnsO68cDWzFHEnSPjvzYCSZk180nhiYgWebMlz9jNCSfxMwC+SfGNkOghhUFUak2oxyim/1LksKxuSucwibleuAz/06IBkRqTRztt8PXKAzmEC6j1+nzR8mrUbs6FF8lQr3QnH+56GY1y/S8A2rDMrtF7tXdNkc7cqxy7Ouyew1XjLs=',  // pragma: allowlist secret
          client_secret: 'AgA8IYNlIerbvj/rM39H0Uj9+fJPsdmkDDC7G2+Ssq5panrioUWgdeMsgqWD5rWAEahL27QuRwu1Ft2HEu585IgBiay4mjTRo2nLhYqy2XBLljTVpGFlOVGeG8ItFarB+2gmOlg5zltBZLHSdK32Y0ptv0YWKhr01HtyjX6FJw3HWCz7RhE+d97MyfnhmbrKak9hPpCsvnBVgtDgEgNhfujK88ZuOUognE5DD0e2c8XSTBqtpFru7ryb1vAMgT7PYVFN4qkk6mUfPgduh7RfBGWMAeQNGQhJFo/zqzuDiWKx5KU1C2UbIwdpLbZF8W2aswbU9D2GAg7SLZfMqa4/GKM/nX1IamiUGgh2WtJZHmNTvAQYYxmDnENrUEdDoipYF8syraZSTjDm/hw/7YK3/S/S8azevla2G5vPoovrwqPw7LF7MTpdjIBJDEf8gZ8JZPgVAn57Z+C6kDyqFXUHHkI0zJ3yhcpTFAt/Ty0oXSOVcMQGfOv7v/sRGjyTRWSOus+SoUcIXYhHFGh33dn14Vyg4WX5qgZnqkKXukK1M6YrHCIDHgWtAYazTkQukj1fjNVn/aCiBHDBFjLszHauv77X8lGAv3gRFyXcBqTPnBbuS4Zcd6DUJs1O/6p1yM6c52nQ5S+EPaJ4UD4XWDkXMKWQt+7kfmTJ7Evp9ulibiK+yRmlWiGuB0kjlpxzIAs71+DKfiCo7lW0I9qwmNx/SrxT7hDH8A3/3OuHpX6yYsfnKrqfYw==',  // pragma: allowlist secret
        },
        GCP_US_CENTRAL1_GSC_PROD: {
          client_id: 'AgAOg7Fjlwlp/ta79von+QTNtK+5TIIWLQMtTTTLs1yvWp1cyhRKHeKd0CEI6z10OEEaPnBdL8f5FgBWIno7TuAVAj1ENBS98X/1BgqCyWz39H2nGFnNVEemRlOjoDK1PjZXU6NzGGuq/eC7tfaLK8lLfDNuu4dswSkIUMNXY9MdhrnmXVJuh0gPkr0oqDf3c4jKbX4W8VjDmkuYLEX1loomOovPBrCvWI2upuTiL20gml4O879AnOP6LDrKd6ysLVfqFMSe7j86gGYPgZGE3b1NctyI9lf/UBZZmAtHiq32rIS+hu1l1fOpA8tZnJq7pxq4MMTOAem5RTGlLw3tcpZ1f4S1z4oOs19Esq/lDRMRqTE4x9WkL4qo6FvvyS9FyEbGP8IKvg4UjGjyy/V7bfnKbekEKN1oMtduirdkLWe0h9R88d9WAyCPOJUT7w2UrktrxOkWeaFlz0tJto5Y2vfyhSVqw+Up2xeajL1vCeRRPBtH+pcwSPflOSSf4Jg2F6fCCjBW6El4UMabYtF9q9/GJlPIuvvlhWIdLYFnM0iwnRjzIC3VEl6AP5fXjphuT9OW7F5/PfbEqHmYV0ADFNitOvoBm9oF6aKGK4g/yn9643hLpIwuo6h7Q6yvtTuMtzGg6PvX7jomBNUvvH/0tAOdJHfzv1Py0d25smegnxiojHE64con2AzlLGHCcjIklrW/x8vPfQeVC0NDLzeRTJWLGPAbTHIFdYIY2PT/U/ltl3J3oa10Jw49sJeV3aSAPcmk2deVtb2Vd6+xrUOSjdsVV0qXPMmcYY0=',  // pragma: allowlist secret
          client_secret: 'AgAQQ5Al4FK85wluuWAUodsOpHYUUZ4WS9U7pnPo0NeVGFpXg3+c6cf9tQlsNsxOonRs7nic6IIMWdTZ9CIvW/QWriiPfhFoCVJ6bo+9ZgB/RjhzehnWuPJkEJ0MHyfaaR2QrYp0Ays36YmsKe7Va5mHGAHXu4bjLOnpSC5nAiIq+thzMwoXF8xLlOfrTDtzI9ubPjM2FJZUVYHzsxoo1Exi4r0jzsFKryFLOOLSw3dYE4eui2F+2WyFmjOXpUYEH65DR8WM6foWLeDBFyvYhnxsmvKxfmY1Bi2xQWcesm0RwBWeoCju8+3iunzIa2o5CiyEnLe2mHu8r+NQeDrAGpLSfMSu0nI0u95q4t+xBdzy02J4heg2nspB4Ji5bMzlOZ7xxW4TDbew3qXKS318wo1QMGEvXsfDvxw6PkGEzCYfB6SAdLiX2guHjjyS0wYoV301/fqOU+WMNCCbkTT7r25+9SPuZHcdR64wULoUoalTlHmBu9qgI2ZRK3wWGlcSqZZ5uQ5xRyOmrcB3qQZb3e9/ExTtnJywgf1PtOS34akWXGd7DWOmaloLqMPSc+CurcEJT2HTRNwvhq3cYjwj3rW46DZBslYzcDifhoIl8XhFd0X/ENO+ui8s/62bGjxMZW/3fubJRzvU4YIMClR3q7LiGo5fZs+UMnzeHVjE7Gm0I1d+vIfVbbavYjuXBMf96MSdlgfJZKRo51zYDcl5Cp02oaH4hjvOxygohZ5jFyq2vy/gOw==',  // pragma: allowlist secret
        },
      }[cloud],
    },
  },
];

local getSpannerDatabaseInfo(cloud, env, namespace, database) =
  local databaseName = '%s-%s' % [namespace, database];
  local instanceNamespace = {
    DEV: 'central-dev',
    STAGING: 'central-staging',
    PROD: 'central',
  }[env];
  local instanceName = 'spanner-%s-%s' % [cloudInfo[cloud].shortName, instanceNamespace];
  {
    databaseName: databaseName,
    databaseFullName: 'projects/%s/instances/%s/databases/%s' % [cloudInfo[cloud].projectId, instanceName, databaseName],
    instanceName: instanceName,
    instanceNamespace: instanceNamespace,
  };

// Get the definition for a Spanner database.
// Args:
// - cloud: The cloud to deploy to
// - env: The environment to deploy to
// - namespace: The namespace to deploy to
// - appName: The name of the app. The app name is added to the labels of all created objects.
// - databaseName: The name of the database. This should be a short name, like "auth". This function
//     will handle adding the namespace to resource names as appropriate. Callers should use the
//     returned database name instead of the value passed here for referencing the database
//     elsewhere.
// - ddl: The DDL statements to apply to the database. This is an immutable list and ConfigConnector
//     will apply just the changes.
// - externalInstanceRef: Only exposed for backwards compatibility for older databases. New databases
//    should keep the default.
local createSpannerDatabase(cloud, env, namespace, appName, databaseName, ddl, externalInstanceRef=true) =
  local databaseInfo = getSpannerDatabaseInfo(cloud, env, namespace, databaseName);
  local objects = [
    {
      apiVersion: 'spanner.cnrm.cloud.google.com/v1beta1',
      kind: 'SpannerDatabase',
      metadata: {
        name: databaseInfo.databaseName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        instanceRef: if externalInstanceRef then {
          external: databaseInfo.instanceName,
        } else {
          name: databaseInfo.instanceName,
          namespace: databaseInfo.instanceNamespace,
        },
        databaseDialect: 'GOOGLE_STANDARD_SQL',
        ddl: ddl,
      },
    },
  ];
  databaseInfo + {
    objects: objects,
  };

// Grant the given service account read/write access to the given Spanner database. This also gives
// monitoring.metricWriter at the project level, which is needed for the spanner client to record
// metrics. Without it the client continuously logs errors.
local grantSpannerAccess(cloud, env, namespace, appName, databaseFullName, serviceAccountName) =
  [
    grantAccess(
      name='%s-spanner-database-grant' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'SpannerDatabase',
        external: databaseFullName,
      },
      bindings=[
        {
          role: 'roles/spanner.databaseUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccountName,
                },
              },
            },
          ],
        },
      ],
    ),
    // Give monitoring.metricWriter so that the spanner client can record metrics. You're supposed to
    // get this from spanner.databaseUser but it seems like you need this at a project level rather
    // than a database level.
    grantAccess(
      name='%s-monitoring-grant' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'Project',
        external: cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/monitoring.metricWriter',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccountName,
                },
              },
            },
          ],
        },
      ],
    ),
  ];
{
  createFrontendConfig:: createFrontendConfig,
  frontendConfigName:: frontendConfigName,
  createBackendConfig:: createBackendConfig,
  createServiceAccount:: createServiceAccount,
  createIAMServiceAccountOnly:: createIAMServiceAccountOnly,
  grantServiceAccountImpersonation:: grantServiceAccountImpersonation,
  createBigtableTable:: createBigtableTable,
  getBigtableTable:: getBigtableTable,
  grantBigtableAccess:: grantBigtableAccess,
  createBigtableGc:: createBigtableGc,
  grantAccess:: grantAccess,
  cloudIdentityGroup:: cloudIdentityGroup,
  cloudIdentityGroupMembership:: cloudIdentityGroupMembership,
  createIapSecret:: createIapSecret,
  getSpannerDatabaseInfo:: getSpannerDatabaseInfo,
  createSpannerDatabase:: createSpannerDatabase,
  grantSpannerAccess:: grantSpannerAccess,

  mountSecretManagerSecret:: function(env, cloud, namespace, appName, purpose, version, serviceAccount, fileName='secret', overrideSecretName=null)
    // Returns configuration needed to access a Google Secret Manager secret from a container
    //
    // The secret must already exist in Google Secret Manager for the project associated with `cloud`. The function assumes that the secret is
    // named <env>-<appName>-<purpose> unless `overrideSecretName` is set.
    // The sharing of secrets between different applications requires 'overrideSecretName' to be set.
    //
    // By default, the secret appears in the container at /<purpose>/secret.
    //
    // Args:
    // - env: the environment (DEV, PROD, ...)
    // - cloud: the cloud to deploy to (e.g. GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
    // - namespace: the namespace to deploy any Kubernetes objects needed to access the secret
    // - appName: the name of the app. The app name is added to the labels of all created objects. It is also used to name the secret.
    // - purpose: a kebab-case string describing the purpose of the secret. The purpose is used to name the secret.
    // - version: the version of the secret to use. "latest" is not allowed in staging/production.
    // - serviceAccount: the service account to grant access to the secret
    // - fileName: the name of the secret file in the container. Defaults to `secret`. Directory is fixed to `/<purpose>`.
    // - overrideSecretName: if not null, use this name instead of the default scheme
    //
    // Returns:
    // - filePath: the path to the secret in the container
    // - podVolumeDef: the volume definition to add to the pod spec
    // - volumeMountDef: the volume mount definition to add to the container spec
    // - objects: the Kubernetes objects needed to access the secret
    {
      assert std.findSubstr(' ', purpose) == [] : 'purpose must not contain spaces',
      assert std.findSubstr('_', purpose) == [] : 'purpose must not contain underscores',
      assert version != 'latest' || env == 'DEV' : 'latest is not allowed in staging/production',

      local secretName = if overrideSecretName == null then std.asciiLower(env) + '-' + appName + '-' + purpose else overrideSecretName,
      local volumeName = '%s-volume' % secretName,

      // Multiple apps accessing the same secret need distinctly named k8s object. The default secret
      // naming scheme has appName in the name. However, a shared secret won't have the appName in
      // the secret name, so make sure it appears in the k8s object names.
      local objectName = if std.length(std.findSubstr(appName + '-', secretName)) == 0 then appName + '-' + secretName else secretName,

      filePath: '/%s/%s' % [purpose, fileName],
      podVolumeDef: {
        name: volumeName,
        csi: {
          driver: 'secrets-store-gke.csi.k8s.io',
          readOnly: true,
          volumeAttributes: {
            secretProviderClass: objectName,
          },
        },
      },
      volumeMountDef: {
        name: volumeName,
        mountPath: '/' + purpose,
        readOnly: true,
      },
      objects: [
        {
          apiVersion: 'secrets-store.csi.x-k8s.io/v1',
          kind: 'SecretProviderClass',
          metadata: {
            name: objectName,
            namespace: namespace,
            labels: {
              app: appName,
            },
          },
          spec: {
            provider: 'gke',
            parameters: {
              secrets: '[{"resourceName":"projects/%s/secrets/%s/versions/%s","path":"%s"}]' %
                       [cloudInfo[cloud].projectId, secretName, version, fileName],
            },
          },
        },
        {
          kind: 'IAMPartialPolicy',
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          metadata: {
            name: objectName,
            namespace: namespace,
            labels: {
              app: appName,
            },
            annotations: if env == 'DEV' then {
              'eng.augmentcode.com/deletion-policy': 'abandon',
            } else {
            },
          },
          spec: {
            resourceRef: {
              kind: 'SecretManagerSecret',
              external: 'projects/%s/secrets/%s' % [cloudInfo[cloud].projectId, secretName],
            },
            bindings: [
              {
                role: 'roles/secretmanager.secretAccessor',
                members: [
                  { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
                ],
              },
            ],
          },
        },
      ],
    },
}
