local cloud_info = import 'deploy/common/cloud_info.jsonnet';

function(cloud) {
  _ret:: gsc_peer + psc_ns + psc_dns + psc_subnet,

  local C = cloud_info[cloud],

  local psc_domain = std.get(C, 'privateServiceConnectDomain', null),
  local psc_dns_lead = std.get(C, 'privateServiceConnectDNSLead', false),  // Controls which cluster hosts the DNSManagedZone resource.
  local psc_subnet_range = std.get(C, 'privateServiceConnectSubnet', null),  // Controls which cluster hosts the ComputeSubnet resources (need only 1 per project per region).
  local psc_namespace = 'private-service-connect',

  local ns_shard = {
    GCP_US_CENTRAL1_DEV: 'b',
    GCP_US_CENTRAL1_PROD: 'c',
  }[cloud],

  // A small namespace to hold the DNS and/or Subnet resources.
  local psc_ns = if !(psc_dns_lead || psc_subnet == null) then [] else [{
    apiVersion: 'v1',
    kind: 'Namespace',
    metadata: {
      name: psc_namespace,
      annotations+: {
        'cnrm.cloud.google.com/project-id': C.projectId,
      },
    },
  }],

  local psc_dns = if !psc_dns_lead then [] else {
    _ret:: [self.dns_zone, self.dns_ns_record],
    local dns = self,

    dns_zone:: {
      apiVersion: 'dns.cnrm.cloud.google.com/v1beta1',
      kind: 'DNSManagedZone',
      local o = self,
      metadata: {
        name: std.strReplace(std.stripChars(o.spec.dnsName, '.'), '.', '-'),
        namespace: psc_namespace,
      },
      spec: {
        dnsName: psc_domain + '.',
        description: 'DNS Zone for Private Service Connect.',
        visibility: 'public',
      },
    },

    dns_ns_record:: {
      apiVersion: 'dns.cnrm.cloud.google.com/v1beta1',
      kind: 'DNSRecordSet',
      local o = self,
      metadata: {
        name: std.strReplace(std.stripChars(o.spec.name, '.'), '.', '-') + '-' + std.asciiLower(o.spec.type),
        namespace: psc_namespace,
      },
      spec: {
        managedZoneRef: {
          external: C.rootDNSZone,
        },
        name: dns.dns_zone.spec.dnsName,
        type: 'NS',
        rrdatas: [
          'ns-cloud-%(shard)s%(num)d.googledomains.com.' % {
            shard: ns_shard,
            num: num,
          }
          for num in std.range(1, 4)
        ],
      },
    },
  }._ret,

  local psc_subnet = if psc_subnet_range == null then [] else [{
    apiVersion: 'compute.cnrm.cloud.google.com/v1beta1',
    kind: 'ComputeSubnetwork',
    metadata: {
      name: 'psc0',
      namespace: psc_namespace,
    },
    spec: {
      ipCidrRange: psc_subnet_range,
      networkRef: {
        external: 'projects/' + C.projectId + '/global/networks/default',
      },
      region: C.region,
      purpose: 'PRIVATE_SERVICE_CONNECT',
    },
  }],

  local gsc_peer = if cloud != 'GCP_US_CENTRAL1_PROD' then [] else [
    // the peer in GSC_PROD to PROD has been setup by Matt Monaco
    {
      apiVersion: 'compute.cnrm.cloud.google.com/v1beta1',
      kind: 'ComputeNetworkPeering',
      metadata: {
        name: 'prod-gsc-vpc0-system-services-prod-default',
        namespace: 'devtools',
      },
      spec: {
        exportCustomRoutes: false,
        exportSubnetRoutesWithPublicIp: true,
        importCustomRoutes: false,
        importSubnetRoutesWithPublicIp: false,
        networkRef: {
          external: 'projects/system-services-prod/global/networks/default',
        },
        peerNetworkRef: {
          external: 'projects/system-services-prod-gsc/global/networks/prod-gsc-vpc0',
        },
        resourceID: 'prod-gsc-vpc0-system-services-prod-default',
      },
    },
  ],

}._ret
