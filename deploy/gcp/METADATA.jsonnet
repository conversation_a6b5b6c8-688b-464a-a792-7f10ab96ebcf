// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'augment-sysctl',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_augment_sysctl',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#services',
        },
      },
    },
    {
      name: 'bigtable',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_bigtable',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'luke', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'spanner-instance',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_spanner_instance',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'peering',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_peering',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'config-connector',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_config_connector',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'node-groups',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_node_groups',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'nvidia-image-keeper',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_nvidia_image_keeper',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'cloud-armor-policies',
      priority: 500,
      kubecfg: {
        target: '//deploy/gcp:kubecfg_cloud_armor_policies',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#security',
        },
      },
    },
    {
      name: 'config-connector-operator-1-133-0',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_config_connector_operator-1-133-0',
        task: [
          {
            cloud: 'ALL_GCP',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
          },
          {
            cloud: 'GCP_US_CENTRAL1_GSC_PROD',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'iap-namespace',
      kubecfg: {
        target: '//deploy/gcp:kubecfg_iap_namespace',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}
