# GCP

## Setup

```bash
PROJECT=system-services-prod-gsc
gcloud iam service-accounts create configconnector-sa --project=$PROJECT
gcloud projects add-iam-policy-binding $PROJECT \
    --member="serviceAccount:configconnector-sa@$PROJECT-gsc.iam.gserviceaccount.com" \
    --role="roles/editor"
gcloud projects add-iam-policy-binding "$PROJECT" \
    --member="serviceAccount:configconnector-sa@$PROJECT.iam.gserviceaccount.com" \
    --role="roles/iam.roleAdmin" \
    --project=$PROJECT
gcloud projects add-iam-policy-binding "$PROJECT" \
    --member="serviceAccount:configconnector-sa@$PROJECT.iam.gserviceaccount.com" \
    --role="roles/logging.admin" \
    --project=$PROJECT
gcloud projects add-iam-policy-binding "$PROJECT" \
    --member="serviceAccount:configconnector-sa@$PROJECT.iam.gserviceaccount.com" \
    --role="roles/iam.securityAdmin" \
    --project=$PROJECT
gcloud iam service-accounts add-iam-policy-binding \
    configconnector-sa@$PROJECT.iam.gserviceaccount.com \
    --member="serviceAccount:$PROJECT.svc.id.goog[cnrm-system/cnrm-controller-manager]" \
    --role="roles/iam.workloadIdentityUser" \
    --project=$PROJECT
```

## APIS to enable

- "Cloud FileStore"
- "Cloud Resource Manager API"
