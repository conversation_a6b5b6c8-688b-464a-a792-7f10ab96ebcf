#!/bin/bash -x

# Graphite is the (stacked) PR and review tool commonly
# used at Augment
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

# Install brew
curl -o brew.sh -fsSL https://raw.githubusercontent.com/Homebrew/install/e65f88c8b7a49ce741a7092ab22774d2fdd798c0/install.sh
sha256sum -c <<SUM
    67b0989bd0a404cdd32c1df20e3fb724b7c278c83a068fd5a16dac6f8d317a79  brew.sh
SUM
chmod +x brew.sh
yes | ./brew.sh || true
rm brew.sh

# Install graphite
if ! grep -q "linuxbrew" ~/.bashrc; then
	(
		echo
		echo 'eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"'
	) >>~/.bashrc
	eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
fi
/home/<USER>/.linuxbrew/bin/brew install withgraphite/tap/graphite || true
/home/<USER>/.linuxbrew/bin/brew upgrade withgraphite/tap/graphite || true
