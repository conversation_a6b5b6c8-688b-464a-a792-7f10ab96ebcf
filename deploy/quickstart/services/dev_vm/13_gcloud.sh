#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

PATH=~/.local/bin:$PATH
export PATH

# not the version of gcloud that we want
sudo snap remove google-cloud-cli

# Install gcloud
GCLOUD_CLI_TAR=google-cloud-cli-504.0.1-linux-x86_64.tar.gz
curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/$GCLOUD_CLI_TAR
sha256sum -c <<SUM
a01ff5312980a18b073c9d2cd6f287ff7d2684f33bd4c927aec20d1d17344874  $GCLOUD_CLI_TAR
SUM
tar -xzf $GCLOUD_CLI_TAR
rm $GCLOUD_CLI_TAR
rm -rf ~/.local/google-cloud-sdk
mv google-cloud-sdk ~/.local
pushd ~/.local/google-cloud-sdk
yes | ./install.sh || true
popd

if ! grep -q "google-cloud-sdk/completion.bash.inc" ~/.bashrc; then
	echo "source ~/.local/google-cloud-sdk/completion.bash.inc" >>~/.bashrc
	echo "source ~/.local/google-cloud-sdk/path.bash.inc" >>~/.bashrc
fi

# Finishing up
GCLOUD_CMD=$HOME/.local/google-cloud-sdk/bin/gcloud
yes | $GCLOUD_CMD components install gke-gcloud-auth-plugin || true
if ! $GCLOUD_CMD auth list | grep -q "@augmentcode.com"; then
	$GCLOUD_CMD auth login --update-adc
fi
$GCLOUD_CMD config set project system-services-dev
yes | $GCLOUD_CMD auth configure-docker us-central1-docker.pkg.dev || true
yes | $GCLOUD_CMD auth configure-docker europe-west4-docker.pkg.dev || true
yes | $GCLOUD_CMD auth configure-docker asia-southeast1-docker.pkg.dev || true
$GCLOUD_CMD container clusters get-credentials us-central1-prod --region us-central1 --project system-services-prod || echo "unable to get prod credentials, skipping..."
$GCLOUD_CMD container clusters get-credentials eu-west4-prod --region europe-west4 --project system-services-prod || echo "unable to get prod credentials, skipping..."
$GCLOUD_CMD container clusters get-credentials us-central1-dev --region us-central1 --project system-services-dev
$GCLOUD_CMD container clusters get-credentials prod-gsc --region us-central1 --project system-services-prod-gsc
kubectl config use-context gke_system-services-dev_us-central1_us-central1-dev
$GCLOUD_CMD artifacts print-settings python --project=system-services-dev --repository pypi-public --location us-central1 >~/.pypirc

# install gcr docker credential helper
VERSION=2.1.22
OS=linux   # or "darwin" for OSX, "windows" for Windows.
ARCH=amd64 # or "386" for 32-bit OSs

curl -fsSL "https://github.com/GoogleCloudPlatform/docker-credential-gcr/releases/download/v${VERSION}/docker-credential-gcr_${OS}_${ARCH}-${VERSION}.tar.gz" |
	tar xz docker-credential-gcr
sha256sum -c <<SUM
21bc92e50a6e800ea60779b8213feea51de1e4e9912931a0ed9aba77138306b5  docker-credential-gcr
SUM
chmod +x docker-credential-gcr
sudo mv docker-credential-gcr ~/.local/bin
