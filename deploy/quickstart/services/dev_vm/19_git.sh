#!/bin/bash -x

# Installs a newer version of git as the base OS provides.
# In particilar, graphite relies on a decent git version.
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

sudo -i bash <<'EOF'
    set -euxo pipefail

    # add for current git
    add-apt-repository ppa:git-core/ppa

    apt-get update
    DEBIAN_FRONTEND=noninteractive apt-get -y install git
EOF
