#!/bin/bash -x

declare -r VIM_VERSION='9.1.1199'
declare -r VIM_SHA256='fc71b4cd30e55cd02c3f4147ea9c678e53fefc3f016eab368881bada72d18d4b'

declare -r VIM_URL="https://github.com/vim/vim/archive/refs/tags/v${VIM_VERSION}.tar.gz"
declare -r VIM_TGZ="vim-v${VIM_VERSION}.tar.gz"
declare -r VIM_SRCDIR="vim-${VIM_VERSION}"

set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

### Assure build deps needed to be equivalent with Ubuntu stock vim features.

DEBIAN_FRONTEND=noninteractive sudo -E apt-get install -y libpython3-dev libgpm-dev libsodium-dev libncurses-dev

### Download and verify source.

mkdir -p "$HOME/.cache"
cd "$HOME/.cache"
[[ -e "$VIM_TGZ" ]] || curl -fsSL "$VIM_URL" -o "$VIM_TGZ"
printf "%s %s\n" "$VIM_SHA256" "$VIM_TGZ" | sha256sum -c

rm -fr "$VIM_SRCDIR"
tar -xz --no-same-owner -f "$VIM_TGZ"
cd "$VIM_SRCDIR/src"

### Build (enable python3 like the distro vim package)

make \
	CONF_OPT_PYTHON3='--enable-python3interp --with-python3-command=/usr/bin/python3'

### Install (to /usr/local)

sudo make install
