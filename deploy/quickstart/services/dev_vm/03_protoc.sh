#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

# Install protoc (into /usr/local)
PROTOC_VERSION=25.0-rc-2
PROTOC_ZIP=protoc-$PROTOC_VERSION-linux-x86_64.zip
wget http://github.com/protocolbuffers/protobuf/releases/download/v25.0-rc2/$PROTOC_ZIP
sha256sum -c <<SUM
d64fc42971396bcd8b68bc6b3272bdf2b62c085906d950404ed05f49b9e6ae93  $PROTOC_ZIP
SUM
sudo unzip -o ./$PROTOC_ZIP -d /usr/local
rm $PROTOC_ZIP
