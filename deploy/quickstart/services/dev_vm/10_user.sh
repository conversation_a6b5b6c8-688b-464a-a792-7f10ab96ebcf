#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

sudo usermod -a -G docker $USER

#
# Run these as the ubuntu user
#
mkdir -p ~/.augment
if [[ ! -f ~/.augment/user.json ]]; then
	echo "{\"name\": \"$(whoami)\"}" >~/.augment/user.json
fi

# Update the running shell
PATH=~/.local/bin:$PATH
export PATH
echo PATH: $PATH
