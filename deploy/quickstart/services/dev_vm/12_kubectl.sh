#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

# Install kubectl
curl -LO https://dl.k8s.io/release/v1.32.3/bin/linux/amd64/kubectl
sha256sum -c <<SUM
    ab209d0c5134b61486a0486585604a616a5bb2fc07df46d304b3c95817b2d79f  kubectl
SUM
chmod +x kubectl
mv ./kubectl ~/.local/bin/kubectl
