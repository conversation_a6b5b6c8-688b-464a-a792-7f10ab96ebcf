#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

sudo -i bash <<'EOF'
    set -euxo pipefail
    export DEBIAN_FRONTEND=noninteractive

    apt-get update
    apt-get -y install \
        python3.11 \
        python3.11-dev
EOF

curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python3.11 get-pip.py --user
rm get-pip.py

python3.11 -m pip install \
	black \
	ruff==0.6.1 \
	torch==2.4.0+cu124 \
	pyright==1.1.353 \
	pybind11 \
	pre-commit \
	keyrings.google-artifactregistry-auth keyring \
	pylint==2.17.0 \
	--extra-index-url https://download.pytorch.org/whl/cu124

# Also install `uv`
install_uv() {
	declare -r UV_VERSION=0.8.0
	declare -r UV_SHA256SUM=a7d74ee5c5ff3069b9d88236a05f293cc4e2809bad872f3a88a384489ba3675e
	declare -r UV_URL="https://github.com/astral-sh/uv/releases/download/$UV_VERSION/uv-x86_64-unknown-linux-gnu.tar.gz"
	declare -r UV_TGZ="uv-x86_64-unknown-linux-gnu-$UV_VERSION.tar.gz"
	declare -r UV_DL="$HOME/.cache/$UV_TGZ"

	[[ -e "$UV_DL" ]] || curl -fsSL "$UV_URL" -o "$UV_DL"
	printf "%s %s\n" "$UV_SHA256SUM" "$UV_DL" | sha256sum -c
	tar -xz --no-same-owner -C "$HOME/.local/bin" --strip-components=1 -f "$UV_DL"
}
install_uv
