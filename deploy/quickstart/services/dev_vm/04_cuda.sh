#!/bin/bash -x

set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

sudo -i bash <<'EOF'
    set -euxo pipefail

    wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-ubuntu2204.pin
    sha256sum -c <<<'dd00df91301f85f920a43641113793b3e8d6006e058e36fc69f44eadaebf648a  cuda-ubuntu2204.pin'
    mv cuda-ubuntu2204.pin /etc/apt/preferences.d/cuda-repository-pin-600
    apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/3bf863cc.pub
    add-apt-repository "deb https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/ /"

    apt-get update
    DEBIAN_FRONTEND=noninteractive apt-get -y install cuda-12-4
EOF
