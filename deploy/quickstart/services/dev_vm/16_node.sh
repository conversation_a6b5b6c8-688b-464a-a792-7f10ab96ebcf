#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

# Install node
curl -o nvm.sh https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.0/install.sh
sha256sum -c <<SUM
    bdea8c52186c4dd12657e77e7515509cda5bf9fa5a2f0046bce749e62645076d  nvm.sh
SUM
chmod +x nvm.sh
./nvm.sh
rm nvm.sh

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" # This loads nvm
nvm install --lts

npm install -g pnpm@latest-9
