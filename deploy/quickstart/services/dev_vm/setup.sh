#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

echo "Setting up services dev VM..."
echo ""
echo "If you are setting up a research-focused VM, please visit https://notion.so/Augment-Research-Infra-Quick-Start-f43f2c6062d84eb1a5e746c072a7b2f8 instead."

# Set the trap to catch exit codes
trap 'exit_hook' ERR

# Exit hook function
exit_hook() {
	local exit_code=$?
	if [ $exit_code -ne 0 ]; then
		echo "Error: One of the subcommands failed. The installation is NOT complete."
	fi
}

THIS_DIR=$(dirname "$0")

# OS level setup
$THIS_DIR/01_apt.sh
$THIS_DIR/02_nfs.sh
$THIS_DIR/03_protoc.sh
$THIS_DIR/04_cuda.sh

# Tooling setup
$THIS_DIR/10_user.sh
$THIS_DIR/11_python.sh
$THIS_DIR/12_kubectl.sh
$THIS_DIR/13_gcloud.sh
$THIS_DIR/14_bazel.sh
$THIS_DIR/15_rust.sh
$THIS_DIR/16_node.sh
$THIS_DIR/17_go.sh
$THIS_DIR/18_graphite.sh
$THIS_DIR/19_git.sh

$THIS_DIR/20_click.sh
$THIS_DIR/21_vim.sh

echo "Setup complete!"
echo "Please logout of the dev VM and login again to complete the setup."
