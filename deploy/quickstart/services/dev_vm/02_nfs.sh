#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

sudo -i bash <<'EOF'
    set -euxo pipefail

    mkdir -p /mnt/efs/augment
    if grep -q "checkpoint_share" /etc/fstab; then
        sed -i -e "s|^.*/checkpoint_share.*|*************:/checkpoint_share /mnt/efs/augment nfs defaults 0 0|" /etc/fstab
    else
        echo "*************:/checkpoint_share /mnt/efs/augment nfs defaults 0 0" >> /etc/fstab
    fi
    if mountpoint -q /mnt/efs/augment; then
      echo "already mounted /mnt/efs/augment"
    else
      mount /mnt/efs/augment
    fi

EOF
