# Augment developer quickstart

## Launching a new host in the cloud for development work.

### TL;DR
- complete your first-day items
  - AWS login, including creation of an access key and secret
  - Coreweave login, including installing the .kubeconfig
  - GitHub account setup
  - Augment email setup
- clone the code to your laptop
  - [Fork the augment repository](https://github.com/augmentcode/augment/fork) into your github account
- Satisfy the local configuration requirements (qs_setup.sh)
- launch a cloud host in AWS or Coreweave for your development work
- success!

## Local configuration requirements before you start

All of these items can be configured by running the qs_setup.sh script.
*You will be prompted for your AWS credentials, and possibly your local password for sudo.*

    EMAIL_NAME=<username> ./qs_setup.sh

- `~/.augment/user.json`

    used to tell the system who you are; this is generally the "username" portion of your @augmentcode.com email address.

- AWS CLI

    https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html

- AWS Credentials

    It's assumed that a default region is set in ~/.aws/config and access key and secret are in ~/.aws/credentials.  Once you get the credentials as part of your onboarding, you can configure your local environment with the `aws configure` command.

## AWS or Coreweave?

TL;DR Ask your mentor

This probably depends on if you are working primarily on systems (AWS) or training (CoreWeave)

## AWS

See [aws/README.md](aws/README.md)

## CoreWeave

See [../coreweave/README.md](../coreweave/README.md)

## Other quickstart setup tools

### AWS
- TBD

### CoreWeave
- [Launch a dev container on a CoreWeave host](../coreweave/dev_container/README.md)
- [Launch a training pod in coreweave](../coreweave/cw_pod/README.md)
