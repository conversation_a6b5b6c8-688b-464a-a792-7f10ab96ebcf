load("//tools/bzl:kubecfg.bzl", "kubecfg")

kubecfg(
    name = "kubecfg_create_dev_namespace",
    src = "create-dev-namespace.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    cluster_wide = True,
    # The jsonnet expects extra top-level args. To allow linting
    # we need to pass these.
    kubecfg_test_args = [
        "--tla-str",
        "namespaceName=dev-test",
        "--tla-str",
        "namespaceOwner=test",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:namespaces-lib",
    ],
)

kubecfg(
    name = "kubecfg_setup_dev_namespace",
    src = "setup-dev-namespace.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    cluster_wide = True,
    # The jsonnet expects extra top-level args. To allow linting
    # we need to pass these.
    kubecfg_test_args = [
        "--tla-str",
        "namespaceName=dev-test",
        "--tla-str",
        "namespaceOwner=test",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:mounts-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

sh_binary(
    name = "create_and_setup_dev_namespace",
    srcs = ["create_and_setup_dev_namespace.sh"],
    data = [
        ":kubecfg_create_dev_namespace",
        ":kubecfg_setup_dev_namespace",
    ],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "delete_dev_namespace",
    srcs = ["delete_dev_namespace.sh"],
    data = [
        ":kubecfg_create_dev_namespace",
        ":kubecfg_setup_dev_namespace",
    ],
    visibility = ["//visibility:public"],
)
