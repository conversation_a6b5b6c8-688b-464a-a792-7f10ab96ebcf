#!/bin/bash
set -eu
# --- begin runfiles.bash initialization v3 ---
# Copy-pasted from the Bazel Bash runfiles library v3.
set -uo pipefail
set +e
f=bazel_tools/tools/bash/runfiles/runfiles.bash
source "${RUNFILES_DIR:-/dev/null}/$f" 2>/dev/null ||
	source "$(grep -sm1 "^$f " "${RUNFILES_MANIFEST_FILE:-/dev/null}" | cut -f2- -d' ')" 2>/dev/null ||
	source "$0.runfiles/$f" 2>/dev/null ||
	source "$(grep -sm1 "^$f " "$0.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null ||
	source "$(grep -sm1 "^$f " "$0.exe.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null ||
	{
		echo >&2 "ERROR: cannot find $f"
		exit 1
	}
f=
set -e
# --- end runfiles.bash initialization v3 ---

if [ "$#" -lt 2 ]; then
	echo "Usage: $0 <namespace-name> <namespace-owner>"
	exit 1
fi

NAMESPACE_NAME=$1
NAMESPACE_OWNER=$2

# Find the paths to the kubecfg scripts
CREATE_SCRIPT="$(rlocation _main/deploy/quickstart/namespace/kubecfg_create_dev_namespace.sh)"
SETUP_SCRIPT="$(rlocation _main/deploy/quickstart/namespace/kubecfg_setup_dev_namespace.sh)"

echo
echo "CREATE_SCRIPT: $CREATE_SCRIPT"
echo "SETUP_SCRIPT: $SETUP_SCRIPT"
echo

echo "Creating namespace: $NAMESPACE_NAME for owner '$NAMESPACE_OWNER'"
echo

echo "Step 1: Creating the namespace..."
# We pass --batch to skip the confirmation prompt.
"$CREATE_SCRIPT" \
	apply --batch \
	--extra-config-args "namespaceName=$NAMESPACE_NAME" \
	--extra-config-args "namespaceOwner=$NAMESPACE_OWNER"

echo "Step 2: Setting up the namespace..."
"$SETUP_SCRIPT" \
	apply --batch \
	--extra-config-args "namespaceName=$NAMESPACE_NAME" \
	--extra-config-args "namespaceOwner=$NAMESPACE_OWNER"

echo "Namespace $NAMESPACE_NAME has been created and set up successfully!"
