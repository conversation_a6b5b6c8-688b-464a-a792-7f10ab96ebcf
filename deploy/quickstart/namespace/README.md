# Namespace Quickstarters

We use kubernetes for deploying and testing many of our services. Every developer should have at
least one dev namespace to be able to test their code within.

The standard and more well-maintained way to create dev namespaces is by adding an entry for yourself
in `eng.jsonnet`. However this system relies on waiting until the next standard deployment to create
the objects. Moreover, sometimes it's helpful to have a second namespace for development within which
you're okay with more frequently deleting all objects.

Unfortunately creating a new dev namespace isn't as simple as `kubectl create namespace` and
running `bazel run //services/deploy:dev_deploy -- --operation Apply --services default`.
This is because there are other static k8s objects that need to be allocated per namespace
that aren't deployed by the `dev_deploy` script.

The files in this directory are meant to be helpers to help quickly deploy a dev namespace.
To be clear: devs should still be registering themselves in `eng.jsonnet` and going through
a standard deployment as that will configure additional access (like kubernetes access).
The main purpose of these scripts is to help create additional dev namespaces
more quickly.

## How To Setup a New Dev Namespace?

```
bazel run //deploy/quickstart/namespace:create_and_setup_dev_namespace -- <namespace-name> <namespace-owner>
```

Args:
- `namespace-name`: The name of the namespace to create.
- `namespace-owner`: The owner of the namespace (used for a kubernetes annotation, not too important).

## How Do I Teardown a Dev Namespace Created With These Scripts?

```
bazel run //deploy/quickstart/namespace:delete_dev_namespace -- <namespace-name> <namespace-owner>
```

Deletes all objects created by the above scripts (assuming the jsonnets haven't changed since the namespace was created).

**Why can't I just `kubectl delete namespace`?**

**A:** Unfortunately not all of the resources created to setup a dev namespace are namespace-scoped, some are
cluster-scoped. Most notably are the persistent volumes used to mount model checkpoints. If you do not tear these
down, then if you later re-create a dev namespace with the same name using these scripts, the old persistent volume
will reject any new persistent volume claims until its spec is either manually updated (or the persistent volume is
recreated). Of course this isn't too bad, but it does require some level of manual intervention that's unclear
for new devs.
