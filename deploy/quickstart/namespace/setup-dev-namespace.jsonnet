// Setup fundamental dev namespace objects that aren't configured by
// dev_deploy.py.
//
// The dev namespace should first be created with `create-dev-namespace.jsonnet`.


local certLib = import 'deploy/common/cert-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local mountLib = import 'deploy/common/mounts-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

// Creates the certificate infrastructure for service-to-service
// MTLS encryption. Matches what `certs.jsonnet` sets up for
// dev namespaces.
local createNamespaceIssuer(namespaceName) =
  assert std.isString(namespaceName);
  lib.flatten(certLib.createNamespaceIssuer(namespaceName));

// Setup k8s objects that allow pods to mount certain special network file systems from Google Cloud.
// Primary use case is for model checkpoint mounting.
local createNamespaceMounts(cloud, namespaceName) =
  assert std.isString(namespaceName);
  lib.flatten(mountLib.createNamespaceMounts(cloud, namespaceName));

// Secret for accessing Google's Identity-Aware-Proxy (IAP) in GCP clusters.
// Services that have BackendConfigs will reference this secret.
local createIapSecret(cloud, namespaceName) =
  assert std.isString(namespaceName);
  lib.flatten(gcpLib.createIapSecret(cloud, namespaceName));

// See REAMDE.md for how to pass these top-level args when invoking through `kubecfg` bazel target.
//
// Args:
//  namespaceName: The name of the namespace to create.
//  namespaceOwner: The owner of the namespace (used for a kubernetes annotation, not too important).
//  cloud: The cloud to deploy to. Should always be 'GCP_US_CENTRAL1_DEV'. We're forced to take this
//        as an argument because `kubecfg_util.py` always passes that argument and jsonnet errors out
//        if we don't accept it.
function(namespaceName, namespaceOwner, cloud)
  // This jsonnet file should only be used for setting up dev namespaces.
  assert cloud == 'GCP_US_CENTRAL1_DEV';
  lib.flatten([
    createNamespaceIssuer(namespaceName),
    createNamespaceMounts(cloud, namespaceName),
    createIapSecret(cloud, namespaceName),
  ])
