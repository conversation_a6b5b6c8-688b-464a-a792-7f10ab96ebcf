/**
 * Helper file for deploying a new dev namespace quickly for testing.
 *
 * This file does not enforce it, but new namespaces should generally be prefixed with `dev-`
 * so that <PERSON>'s GPU cleanup script will garbage collect unused GPU resources.
 */

local lib = import 'deploy/common/lib.jsonnet';
local namespaceLib = import 'deploy/common/namespaces-lib.jsonnet';

// Namespace should be provided as `--tla-code namespace='my-namespace'`
// See README.md for how to pass top-level args through our custom kubecfg script.
//
// NOTE: Please prefix your namespace with `dev-` so that it is garbage collected by <PERSON>'s script.
//
// Args:
//  namespaceName: The name of the namespace to create.
//  namespaceOwner: The owner of the namespace (used for a kubernetes annotation, not too important).
function(namespaceName, namespaceOwner, cloud)
  // We're forced to take cloud as an argument because `kubecfg_util.py` always passes that argument
  // and jsonnet errors out if we don't accept it. We'll just assert that the cloud is
  // dev.
  assert cloud == 'GCP_US_CENTRAL1_DEV';

  local namespace = {
    name: namespaceName,
    access_type: 'dev',
    annotations: {
      'eng.augmentcode.com/owner': namespaceOwner,
    },
  };
  lib.flatten(namespaceLib.createNamespace('GCP_US_CENTRAL1_DEV', namespace))
