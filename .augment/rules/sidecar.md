---
type: "agent_requested"
description: "Use the sidecar to manage state"
---

- Prefer client-agnostic solutions for syncing state between different webviews rather than using VSCode-specific state management.
- Implement cross-webview state synchronization using sidecar rather than VSCode-specific solutions to maintain client-agnostic architecture.
- Use the async await this.\_asyncMsgSender.sendToSidecar method to implement sidecar methods called from the webview
