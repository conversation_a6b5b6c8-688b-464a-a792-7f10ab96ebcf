---
alwaysApply: true
---

## C/C++

- **Bazel**:
  - Build: `bazel build //path/to/target`
  - Test: `bazel test //path/to/test [--test_output=all]`
- **PyTorch extensions**: Built with `pytorch_cpp_extension` rule
- **Tests**: Defined with `cc_test` or `cc_gpu_test` rules
- **Tags**: `gpu`, `multi_gpu`, `exclusive`

## Rust

- **Cargo**:
  - Build: `cargo build -p <package_name>`
  - Test: `cargo test -p <package_name> [test_name]`
- **Bazel**:
  - Build: `bazel build //services/path/to:target`
  - Test: `bazel test //services/path/to:test [--test_filter=test_name]`
- **Tests**: Inline with `#[cfg(test)]`, separate files, or in `tests/` directory
