---
type: "always_apply"
---

## JavaScript/TypeScript

- **npm/pnpm**:
  - Build: `pnpm run build`, `pnpm run vscode:build-dev`
  - Test: `pnpm run test`, `pnpm run vscode:test`
- **Bazel**:
  - Build: `bazel build //clients/vscode:build_dev_to_workspace`
  - Test: `bazel test //clients/vscode:test`
- **Tests**: Located in `__tests__` directories with `.test.ts` suffix
- **Frameworks**: Jest (VSCode), Vitest (webviews)
- **Style**: Avoid using any for types and prefer unknown

## CSS

- Avoid using !important to overwrite styles

## Frontend (HTML)

- **Svelte** (clients/common/webviews):
  - Build: `pnpm build:vscode`
  - Test: `pnpm vitest`
- **React** (services/\*/frontend):
  - Build: `pnpm build`
  - Test: `jest`
- **Tests**: Located alongside components with `.test.ts` or `.test.tsx` suffix

## Icons

- Prefer importing icons with the following syntax $common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-left.svg?component";

## Testing

- Use `cd clients/vscode && pnpm vscode:jest' to run tests for the VSCode extension.
- Use `cd clients/common/webviews && pnpm run vitest` (for webviews)
- Use `cd clients/sidecar/libs && pnpm run jest` (for sidecar)

### Svelte
- Don't use dispatch in Svelte, it is deprecated.
