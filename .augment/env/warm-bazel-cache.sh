#!/bin/bash

set -euo pipefail

main() {
	install-gcloud
	docker-setup
	bazel-warm
}

bazel-warm() {
	declare -ra targets=(
		# Last check ~/.cache/bazel was ~38G (with //base/... ~65G).
		# These targets include some artificates that require GCP auth (docker and/or gcs).
		"//tools/bzl/..."
		"//deploy/..."
		"//infra/..."
		"//services/api_proxy/..."
		"//services/remote_agents/..."
		"//clients/beachhead:all",
	)

	echo "Warming bazel cache (first pass with --keep_going)..."
	bazel build --keep_going "${targets[@]}"

	echo "Warming bazel cache (second pass fail on error, requires gcloud auth)..."
	bazel build "${targets[@]}"

	echo "Warming bazel cache complete. Disk usage:"
	du -hs ~/.cache/bazel
}

install-gcloud() {
	if command -v gcloud >/dev/null 2>&1; then
		return
	fi

	echo "Installing gcloud..."
	if [[ ! -e /usr/share/keyrings/cloud.google.gpg ]]; then
		sudo install -oroot -groot -m0644 /dev/stdin /usr/share/keyrings/cloud.google.gpg <<<"$(
			curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor
		)"
	fi
	if [[ ! -e /etc/apt/sources.list.d/google-cloud-sdk.list ]]; then
		sudo install -oroot -groot -m0644 /dev/stdin /etc/apt/sources.list.d/google-cloud-sdk.list <<<"$(
			printf "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main\n"
		)"
	fi
	sudo apt-get update -y
	sudo apt-get install -y google-cloud-cli google-cloud-cli-gke-gcloud-auth-plugin
}

# borrowed from gcloud-init
docker-setup() {
	echo "Configuring Docker Cred Helpers for GCR..."
	declare -ra registries=(
		'us-central1-docker.pkg.dev'
		'us-east4-docker.pkg.dev'
		'europe-west4-docker.pkg.dev'
		'asia-southeast1-docker.pkg.dev'
	)
	declare reg

	for reg in "${registries[@]}"; do
		docker_reg "$reg"
	done
}

docker_reg() {
	declare -r reg="$1"
	declare -r cur_config="$(
		[[ -e ~/.docker/config.json ]] && cat ~/.docker/config.json || echo '{}'
	)"
	declare -r cur_value="$(echo "$cur_config" | jq --arg reg "$reg" '.credHelpers[$reg] // ""' -r)"
	declare -r want_value="gcloud"

	if [[ "$cur_value" == "$want_value" ]]; then
		return
	elif [[ ! "$cur_value" ]]; then
		info "Adding %s to ~/.docker/config.json." "$reg"
	else
		info "Updating %s in ~/.docker/config.json: %s -> %s." "$reg" "$cur_value" "$want_value"
	fi

	<<<"$cur_config" jq --arg reg "$reg" --arg val "$want_value" '.credHelpers[$reg] = $val' >~/.docker/config.json
}

main "$@"
