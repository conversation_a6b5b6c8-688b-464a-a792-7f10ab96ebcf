#!/bin/bash
set -e

echo "Setting up development environment for Augment codebase..."

# Update package lists
echo "Updating package lists..."
sudo apt-get update

# Install basic dependencies
echo "Installing basic dependencies..."
sudo apt-get install -y \
	build-essential \
	curl \
	wget \
	git \
	unzip \
	zip \
	pkg-config \
	software-properties-common \
	apt-transport-https \
	ca-certificates \
	gnupg \
	lsb-release \
	libssl-dev \
	zlib1g-dev \
	python3-pip \
	python3-dev \
	libtinfo5 \
	libncurses5

# Install Python packages
echo "Installing Python packages..."
pip3 install pytest pytest-forked pytest-timeout pre-commit
# Add ~/.local/bin to PATH for pytest
echo 'export PATH="$HOME/.local/bin:$PATH"' >>~/.bashrc
export PATH="$HOME/.local/bin:$PATH"

# Install Node.js v20 (required for pnpm 9)
echo "Installing Node.js v20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm
echo "Installing pnpm..."
sudo npm install -g pnpm@9

# Install Bazel
echo "Installing Bazelisk..."
wget -q https://github.com/bazelbuild/bazelisk/releases/download/v1.25.0/bazelisk-linux-amd64
chmod +x bazelisk-linux-amd64
sudo mv bazelisk-linux-amd64 /usr/local/bin/bazel

# Install Buildifier
echo "Installing Buildifier..."
wget -q https://github.com/bazelbuild/buildtools/releases/download/v7.1.1/buildifier-linux-amd64
chmod +x buildifier-linux-amd64
sudo mv buildifier-linux-amd64 /usr/local/bin/buildifier

# Install protoc
echo "Installing protoc..."
PROTOC_VERSION="25.0"
PROTOC_ZIP="protoc-${PROTOC_VERSION}-linux-x86_64.zip"
wget -q "https://github.com/protocolbuffers/protobuf/releases/download/v${PROTOC_VERSION}/${PROTOC_ZIP}"
sudo unzip -o $PROTOC_ZIP -d /usr/local bin/protoc
sudo unzip -o $PROTOC_ZIP -d /usr/local 'include/*'
rm -f $PROTOC_ZIP

# Add environment variables to .bashrc
echo 'export PNPM_HOME="$HOME/.local/share/pnpm"' >>~/.bashrc
echo 'export PATH="$PNPM_HOME:$PATH"' >>~/.bashrc

# Create a .bazelrc.user file to override remote cache settings
cat >~/.bazelrc.user <<EOF
# Disable remote cache for local development
build --remote_cache=
EOF

pnpm install -C clients/
pnpm install -C clients/vscode
pre-commit install --hook-type pre-push
