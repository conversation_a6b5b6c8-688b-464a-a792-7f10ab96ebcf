# Agents App

#### An web app for managing Augment Agents.

📖 See the [SvelteKit docs](https://kit.svelte.dev/docs/introduction) for details on supported features.

This app is built with SvelteKit and Vite. We use SvelteKit in [SPA mode](https://svelte.dev/docs/kit/single-page-apps) and serve the app from the customer UI server (`../server.ts`)

## Development

Set up your dev deployment:

```bash
$ bazel run //services/deploy:dev_deploy -- --services default customer_ui --operation Apply
```

Run the app in dev mode:

```bash
$ npx ibazel run //services/customer/frontend:dev
```

## HMR (Hot Module Reloading)

Running the app in dev mode above will run the customer UI server for the Remix customer app and the agents app. However, HMR will not work correctly for the agents app (it will take up to a minute to rebuild, then you will need to refresh the page). To get HMR working for the agents app (much faster and no page refresh), you can run the following command from the `webapp` directory:

```bash
$ pnpm dev:e2e
```

This will run the customer UI backend and the agents app vite server concurrently. The agents app will be available at `http://localhost:5300` and will
proxy API requests to the customer UI server running on `http://localhost:5200`.
If running in a devpod, you will need to port forward 5300 to your local machine to access the agents app.
