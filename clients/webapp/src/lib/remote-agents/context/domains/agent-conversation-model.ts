/**
 * Remote agent conversation model.
 */

import type { RemoteAgentExchange } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import type { Readable } from "svelte/store";

export interface IAgentConversationModel {
  /** Map from agent ID to conversation */
  readonly conversations: Readable<Map<string, RemoteAgentExchange[]>>;
  /** The currently active remote agent conversation */
  readonly currentConversationID: Readable<string | undefined>;
}

export function createAgentConversationModel(
  conversations: Readable<Map<string, RemoteAgentExchange[]>>,
  currentConversationID: Readable<string | undefined>,
): IAgentConversationModel {
  return {
    conversations,
    currentConversationID,
  };
}
