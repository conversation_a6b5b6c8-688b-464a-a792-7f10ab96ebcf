/**
 * Model for managing the list of agents and their statuses.
 */

import type { RemoteAgent } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import type { Readable } from "svelte/store";

export interface IAgentListModel {
  readonly agents: Readable<RemoteAgent[]>;
}

export function createAgentListModel(
  agents: Readable<RemoteAgent[]>,
): IAgentListModel {
  return {
    agents,
  };
}
