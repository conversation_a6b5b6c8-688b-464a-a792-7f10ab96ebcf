/**
 * Agent Stream Model.
 * Tracks the state of all active streams.
 */

import type { Readable } from "svelte/store";

export type StreamStatus = "connecting" | "active" | "error";
export type StreamType = "agent-list" | "agent-conversation";

export interface StreamState {
  id: string;
  type: StreamType;
  status: StreamStatus;
  errorMsg?: string;
}

export interface IAgentStreamModel {
  /** Map from Agent ID (or "agent-list") to stream state */
  readonly streamStates: Readable<Map<string, StreamState>>;
}

export function createAgentStreamModel(
  streamStates: Readable<Map<string, StreamState>>,
): IAgentStreamModel {
  return {
    streamStates,
  };
}
