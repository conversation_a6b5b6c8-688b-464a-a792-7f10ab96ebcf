/**
 * Remote Agent Model
 *
 * READONLY reactive state aggregation for remote agents in the Agents Web App.
 * Provides domain-specific stores and Svelte context management.
 *
 * Architecture: (see also: git-agent-edit/context/model.ts)
 *  - Model: Pure reactive read surface (no side-effects)
 *  - Controller: Handles mutations and RPC calls
 *  - Domain models: Focused leaf-store groupings
 *
 *
 * Core responsibilities:
 *  - Owns writable sources (_agents, _agentConversations, _streamStates)
 *  - Exposes readonly wrappers for components
 *  - Composes domain model factories
 *
 *
 * Extension guidance:
 *  - Add new concerns via domain model factories, not class expansion
 *  - Keep constructor minimal
 *  - Extend _getWritableStores() for new controller write access
 */

import type {
  RemoteAgent,
  RemoteAgentExchange,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { writable, type Writable } from "svelte/store";
import {
  createAgentConversationModel,
  type IAgentConversationModel,
} from "./domains/agent-conversation-model";
import {
  createAgentListModel,
  type IAgentListModel,
} from "./domains/agent-list-model";
import {
  createAgentStreamModel,
  type IAgentStreamModel,
  type StreamState,
} from "./domains/agent-stream-model";

export interface IRemoteAgentModel {
  /** List of all agents */
  readonly agentsListModel: IAgentListModel;
  /** Map from agent ID to conversation model */
  readonly agentConversationsModels: IAgentConversationModel;
  readonly agentStreamModel: IAgentStreamModel;
}

export class RemoteAgentModel implements IRemoteAgentModel {
  private _agents: Writable<RemoteAgent[]> = writable([]);
  private _agentConversations: Writable<Map<string, RemoteAgentExchange[]>> =
    writable(new Map());
  private _currentConversationID: Writable<string | undefined> =
    writable(undefined);
  private _streamStates: Writable<Map<string, StreamState>> = writable(
    new Map(),
  );

  private _agentsListModel: IAgentListModel = createAgentListModel(
    this._agents,
  );
  private _agentConversationsModels: IAgentConversationModel =
    createAgentConversationModel(
      this._agentConversations,
      this._currentConversationID,
    );
  private _agentStreamModel: IAgentStreamModel = createAgentStreamModel(
    this._streamStates,
  );

  readonly agentsListModel: IAgentListModel = this._agentsListModel;
  readonly agentConversationsModels: IAgentConversationModel =
    this._agentConversationsModels;
  readonly agentStreamModel: IAgentStreamModel = this._agentStreamModel;

  /**
   * Access writable stores underlying the models. Should only be used by controllers.
   */
  _getWritableStores() {
    return {
      agents: this._agents,
      agentConversations: this._agentConversations,
      currentConversationID: this._currentConversationID,
      streamStates: this._streamStates,
    };
  }
}
