import type { APIServer } from "../../api";
import type { IRemoteAgentController } from "./controller";
import type { IRemoteAgentModel } from "./model";
import { RemoteAgentController } from "./controller";
import { RemoteAgentModel } from "./model";
import { getContext, setContext } from "svelte";

export interface IRemoteAgentContext {
  remoteAgentModel: IRemoteAgentModel;
  remoteAgentController: IRemoteAgentController;
}

export const REMOTE_AGENT_CONTEXT_KEY = "remote-agent-context";

export const createRemoteAgentContext = (
  apiServer: APIServer,
): IRemoteAgentContext => {
  const remoteAgentModel = new RemoteAgentModel();
  const remoteAgentController = new RemoteAgentController(
    remoteAgentModel,
    apiServer,
  );

  setContext(REMOTE_AGENT_CONTEXT_KEY, {
    remoteAgentModel,
    remoteAgentController,
  });

  return { remoteAgentModel, remoteAgentController };
};

export const getRemoteAgentContext = (): IRemoteAgentContext => {
  const context = getContext<IRemoteAgentContext>(REMOTE_AGENT_CONTEXT_KEY);
  if (!context) {
    throw new Error("RemoteAgentContext not found");
  }
  return context;
};
