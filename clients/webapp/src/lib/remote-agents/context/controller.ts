/**
 * Remote Agent Controller
 *
 * Handles all mutations and API calls for the remote agent context.
 * Exclusively mutates RemoteAgentModel's writable stores.
 * Handles all API calls and data transformation.
 */

import {
  AgentHistoryUpdateType,
  AgentListUpdateType,
  type AgentHistoryUpdate,
  type Agent<PERSON>istUpdate,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import type { APIServer } from "../../api";
import type { StreamType } from "./domains/agent-stream-model";
import type { RemoteAgentModel } from "./model";
import { get } from "svelte/store";

const STREAM_CANCELLED = "STREAM_CANCELLED";

export interface IRemoteAgentController {
  setActiveRemoteAgent: (agentId: string | undefined) => void;

  startAgentListStream: () => void;
  startAgentConversationStream: (agentId: string) => void;
  cancelAgentListStream: () => void;
  cancelAgentConversationStream: (agentId: string) => void;
}

export class RemoteAgentController implements IRemoteAgentController {
  private _stores: ReturnType<RemoteAgentModel["_getWritableStores"]>;
  private _abortControllers: Map<string, AbortController> = new Map();

  constructor(
    private readonly _model: RemoteAgentModel,
    public readonly api: APIServer,
  ) {
    this._stores = this._model._getWritableStores();
    void this.initialize();
  }

  async initialize(): Promise<void> {
    this.startAgentListStream();
  }

  setActiveRemoteAgent(agentId: string | undefined): void {
    console.log("setting active agent", agentId);
    this._stores.currentConversationID.set(agentId);
  }

  private _getAbortSignal(streamId: string) {
    const controller =
      this._abortControllers.get(streamId) || new AbortController();
    this._abortControllers.set(streamId, controller);
    return controller.signal;
  }

  startAgentListStream(): void {
    this.startStream(
      "agent-list",
      "agent-list",
      () =>
        this.api.getRemoteAgentOverviewsStream(
          undefined,
          this._getAbortSignal("agent-list"),
        ),
      () => this.cancelStream("agent-list"),
    );
  }

  startAgentConversationStream(agentId: string): void {
    this.startStream(
      agentId,
      "agent-conversation",
      () =>
        this.api.getRemoteAgentChatHistoryStream(
          agentId,
          0,
          this._getAbortSignal(agentId),
        ),
      () => this.cancelStream(agentId),
    );
  }

  cancelAgentListStream(): void {
    this.cancelStream("agent-list");
  }

  cancelAgentConversationStream(agentId: string): void {
    this.cancelStream(agentId);
  }

  private async startStream(
    streamId: string,
    streamType: StreamType,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    startFn: () => AsyncGenerator<any>,
    cancelFn: () => void,
  ) {
    // If we're already streaming or connecting, do nothing
    const currState = get(this._stores.streamStates).get(streamId);
    if (currState && ["active", "connecting"].includes(currState.status)) {
      return;
    }
    // Mark as connecting
    this._stores.streamStates.update((streamStates) => {
      streamStates.set(streamId, {
        id: streamId,
        type: streamType,
        status: "connecting",
      });
      return streamStates;
    });
    try {
      const stream = startFn();
      for await (const updateChunk of stream) {
        this._stores.streamStates.update((streamStates) => {
          streamStates.set(streamId, {
            id: streamId,
            type: streamType,
            status: "active",
          });
          return streamStates;
        });
        switch (streamType) {
          case "agent-list":
            for (const update of updateChunk.updates) {
              this.processAgentListUpdate(update);
            }
            break;
          case "agent-conversation":
            for (const update of updateChunk.updates) {
              this.processAgentConversationUpdate(update);
            }
            break;
        }
      }
    } catch (error) {
      console.error(`Error in stream ${streamId}:`, error);
      if (error instanceof DOMException && error.name === STREAM_CANCELLED) {
        return;
      }
      this._stores.streamStates.update((streamStates) => {
        streamStates.set(streamId, {
          id: streamId,
          type: streamType,
          status: "error",
          errorMsg: error instanceof Error ? error.message : String(error),
        });
        return streamStates;
      });
    } finally {
      cancelFn();
    }
  }

  private processAgentListUpdate(update: AgentListUpdate) {
    switch (update.type) {
      case AgentListUpdateType.AGENT_LIST_AGENT_ADDED:
        this._stores.agents.update((agents) => {
          agents.push(update.agent!);
          return agents;
        });
        break;
      case AgentListUpdateType.AGENT_LIST_AGENT_UPDATED:
        this._stores.agents.update((agents) => {
          const index = agents.findIndex(
            (agent) => agent.remote_agent_id === update.agent!.remote_agent_id,
          );
          if (index >= 0) {
            agents[index] = update.agent!;
          }
          return agents;
        });
        break;
      case AgentListUpdateType.AGENT_LIST_AGENT_DELETED:
        this._stores.agents.update((agents) => {
          return agents.filter(
            (agent) => agent.remote_agent_id !== update.deleted_agent_id,
          );
        });
        break;
      case AgentListUpdateType.AGENT_LIST_ALL_AGENTS:
        this._stores.agents.set(update.all_agents!);
        break;
    }
  }

  private processAgentConversationUpdate(update: AgentHistoryUpdate) {
    const currentAgent = get(this._stores.currentConversationID);
    if (!currentAgent) {
      // If we're not currently on a remote agent conversation, then
      // we don't know who these updates belong to
      return;
    }
    switch (update.type) {
      case AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE:
        this._stores.agentConversations.update((agentConversations) => {
          const newAgentConversations = new Map(agentConversations);
          const currentConversation =
            newAgentConversations.get(currentAgent ?? "") || [];
          // TODO: Merge the new exchange into the current conversation like we
          // do in the vscode HybridStateModel
          const updatedConversation = [
            ...currentConversation,
            update.exchange!,
          ];
          newAgentConversations.set(currentAgent, updatedConversation);
          return newAgentConversations;
        });
        break;
      case AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE_UPDATE:
        this._stores.agentConversations.update((agentConversations) => {
          const currentConversation =
            agentConversations.get(update.agent?.remote_agent_id ?? "") || [];
          if (update.exchange_update) {
            // Find the exchange with the matching sequence ID
            const sequenceId = update.exchange_update.sequence_id;
            const exchangeIndex = currentConversation.findIndex(
              (exchange) => exchange.sequence_id === sequenceId,
            );

            if (exchangeIndex >= 0) {
              const exchange = currentConversation[exchangeIndex];
              const currentText = exchange.exchange?.response_text || "";
              exchange.exchange.response_text =
                currentText + update.exchange_update.appended_text;

              const appendedNodes = update.exchange_update.appended_nodes;
              if (appendedNodes && appendedNodes.length > 0) {
                const responseNodes = exchange.exchange.response_nodes ?? [];
                exchange.exchange.response_nodes = [
                  ...responseNodes,
                  ...appendedNodes,
                ];
              }

              const appendedChangedFiles =
                update.exchange_update.appended_changed_files;
              if (appendedChangedFiles && appendedChangedFiles.length > 0) {
                const currentChangedFiles = exchange.changed_files ?? [];
                exchange.changed_files = [
                  ...currentChangedFiles,
                  ...appendedChangedFiles,
                ];
              }
            }
            agentConversations.set(
              update.agent!.remote_agent_id,
              currentConversation,
            );
          }
          return agentConversations;
        });
        break;
      case AgentHistoryUpdateType.AGENT_HISTORY_AGENT_STATUS:
        this._stores.agents.update((agents) => {
          const index = agents.findIndex(
            (agent) => agent.remote_agent_id === update.agent!.remote_agent_id,
          );
          if (index >= 0) {
            agents[index] = update.agent!;
          }
          return agents;
        });
        break;
    }
  }

  private cancelStream(streamId: string) {
    this._abortControllers.get(streamId)?.abort(STREAM_CANCELLED);
    this._stores.streamStates.update((streamStates) => {
      streamStates.delete(streamId);
      return streamStates;
    });
    this._abortControllers.delete(streamId);
  }
}
