/**
 * Feature flags state management for the agents app
 */
import type { FeatureFlags } from "../types";
import { LoadingState } from "./loading-state";

/**
 * Feature flags manager with dependency injection and reactive state
 */
export class FeatureFlagsManager {
  private featureFlagsState = $state<{
    flags: FeatureFlags;
    loadingState: LoadingState;
    error: string | null;
  }>({
    flags: {},
    loadingState: LoadingState.NOT_LOADED,
    error: null,
  });

  constructor(private _getFeatureFlags: () => Promise<FeatureFlags>) {}

  get flags() {
    return this.featureFlagsState.flags;
  }

  get isLoaded() {
    return this.featureFlagsState.loadingState === LoadingState.LOADED;
  }

  get isLoading() {
    return this.featureFlagsState.loadingState === LoadingState.LOADING;
  }

  get error() {
    return this.featureFlagsState.error;
  }
  /**
   * Load feature flags from the API
   */
  async load(): Promise<void> {
    if (this.featureFlagsState.loadingState === LoadingState.LOADING) {
      return;
    }

    this.featureFlagsState.loadingState = LoadingState.LOADING;
    this.featureFlagsState.error = null;

    try {
      const flags = await this._getFeatureFlags();
      this.featureFlagsState.flags = flags;
      this.featureFlagsState.loadingState = LoadingState.LOADED;
    } catch (error) {
      console.error("Failed to load feature flags:", error);
      this.featureFlagsState.error =
        error instanceof Error ? error.message : "Unknown error";
      this.featureFlagsState.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * Reset feature flags state
   */
  reset(): void {
    this.featureFlagsState.flags = {};
    this.featureFlagsState.loadingState = LoadingState.NOT_LOADED;
    this.featureFlagsState.error = null;
  }
}
