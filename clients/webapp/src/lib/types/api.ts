/**
 * API Response Types
 */

export type FeatureFlags = {
  [key: string]: boolean | string | number | undefined;
};

export interface ModelInfo {
  name: string;
  display_name: string;
  provider: string;
  context_length?: number;
}

export interface GetModelsResponse {
  default_model: string;
  models: ModelInfo[];
  feature_flags?: FeatureFlags;
  user_tier?:
    | "UNKNOWN"
    | "COMMUNITY_TIER"
    | "PROFESSIONAL_TIER"
    | "ENTERPRISE_TIER";
  user?: {
    id: string;
    email?: string;
  };
}
