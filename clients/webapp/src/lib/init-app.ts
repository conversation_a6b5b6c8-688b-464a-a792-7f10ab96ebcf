/**
 * File for initializing app dependencies.
 * Should be imported in +layout.ts.
 */

// Ideally we would use the inline-monaco-bootstrap plugin to inline the script into
// our app.html file, but vite plugins with SvelteKit don't call transformIndexHtml.
// So we have to instead import the underlying script here.
// See https://github.com/sveltejs/kit/discussions/8269
import "$common-webviews/scripts/vite-plugins/monaco-bootstrap.js";

// It would also be nice to have this script included in the app.html file, but
// while vite would resolve the import from $common-webviews, SvelteKit's static adapter does not.
import "$common-webviews/src/design-system/_libs/design-system-init";

// Init chat deps
import "./web-host";

// Init sidecar-libs api deps
import { setLibraryClientAuth } from "@augment-internal/sidecar-libs/src/client-interfaces/client-auth";
import { setLibraryClientConfig } from "@augment-internal/sidecar-libs/src/client-interfaces/client-config";
import { WebAuth } from "./api/auth-config";
import { WebClientConfig } from "./api/client-config";
import {
  setLibraryLogger,
  type AugmentLogger,
} from "@augment-internal/sidecar-libs/src/logging";

setLibraryClientAuth(new WebAuth());
setLibraryClientConfig(new WebClientConfig());
const logger: AugmentLogger = { ...console, verbose: console.debug };
// Set the logger in the sidecar-libs
setLibraryLogger(logger);
