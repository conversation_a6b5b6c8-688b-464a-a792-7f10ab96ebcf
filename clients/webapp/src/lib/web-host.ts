import {
  HostClientType,
  type HostInterface,
} from "$common-webviews/src/common/hosts/host-types";
import { handleUnimplementedWebviewMessages } from "./webview-messages";

const webHost: HostInterface = {
  clientType: HostClientType.web,
  postMessage: (msg: any) => {
    console.log("WebHost postMessage:", msg);
    // You can extend this to handle specific message types
    switch (msg.type) {
      default:
        // If we don't have a specific handler, handle the message generically
        handleUnimplementedWebviewMessages(msg);
        break;
    }
  },
  getState: () => {
    try {
      const stored = localStorage.getItem("augment-webhost-state");
      return stored ? JSON.parse(stored) : undefined;
    } catch (error) {
      console.warn("Failed to get state from localStorage:", error);
      return undefined;
    }
  },
  setState: (state: any) => {
    try {
      localStorage.setItem("augment-webhost-state", JSON.stringify(state));
    } catch (error) {
      console.warn("Failed to save state to localStorage:", error);
    }
  },
};

export function initializeWebHost() {
  if (typeof window !== "undefined") {
    window.augment = window.augment || {};
    window.augment.host = webHost;

    // Set up other global objects that webview components might expect
    window.augmentDeps = window.augmentDeps || {};
    window.augmentFlags = window.augmentFlags || {};

    console.log("WebHost initialized and set on window.augment.host");
  }
}

initializeWebHost();
