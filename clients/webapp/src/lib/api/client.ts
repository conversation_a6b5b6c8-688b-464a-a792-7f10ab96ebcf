/**
 * HTTP API Client
 */

import type { FetchFunction } from "@augment-internal/sidecar-libs/src/api/types";

export class ApiError extends Error {
  status?: number;
  code?: string;

  constructor(message: string) {
    super(message);
    this.name = "ApiError";
  }
}

export interface ApiRequestOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  timeout?: number;
}

/**
 * Make an authenticated API call to the Augment backend via proxy
 *
 * This routes all requests through the Express proxy. The proxy handles authentication
 * via httpOnly cookies.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function callAPI<T = any>(
  endpoint: string,
  data = {},
  options: ApiRequestOptions = {},
): Promise<T> {
  const { method = "POST", headers = {}, timeout = 30000 } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const proxyUrl = `/agents/api/${endpoint}`;

    const response = await fetch(proxyUrl, {
      method,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      body: method === "GET" ? undefined : JSON.stringify(data),
      signal: controller.signal,
      credentials: "include", // Important: include cookies in request
    });

    clearTimeout(timeoutId);

    if (
      response.status === 401 ||
      response.status === 403 ||
      response.status === 502
    ) {
      // we get 502s in dev when making api calls from localhost to a dev deploy
      // Redirect to login if auth fails
      window.location.href = "/auth/login";
      const error = new ApiError("Authentication failed - please log in again");
      error.status = response.status;
      throw error;
    }

    if (!response.ok) {
      const errorText = await response.text();
      const error = new ApiError(`API request failed: ${errorText}`);
      error.status = response.status;
      throw error;
    }

    const responseData = await response.json();
    return responseData as T;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof DOMException && error.name === "AbortError") {
      throw new ApiError("Request timeout");
    }

    // Re-throw API errors as-is
    if (error instanceof ApiError) {
      throw error;
    }

    // Wrap other errors
    console.error("API request failed:", error);
    throw new ApiError(
      `Network error: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

/**
 * Make a fetch function for the sidecar api
 */
export const makeWebFetch = (): FetchFunction => {
  return fetch;
};
