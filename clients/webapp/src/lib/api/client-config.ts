import type {
  IClientConfig,
  SidecarAugmentConfig,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-config";

export class WebClientConfig implements IClientConfig {
  public getConfig(): Promise<Readonly<SidecarAugmentConfig>> {
    return Promise.resolve({
      apiToken: "",
      completionURL: `${window.location.origin}/agents/api`,
      chat: {
        url: "",
        model: "",
      },
      agent: {
        model: "",
      },
      enableDebugFeatures: false,
    });
  }
}
