import type { IClientAuth } from "@augment-internal/sidecar-libs/src/client-interfaces/client-auth";

export class WebAuth implements IClientAuth {
  async getAPIToken(): Promise<string> {
    // The web app uses cookie-based auth, so we don't need to provide an API token from the browser
    return Promise.resolve("");
  }

  async getCompletionURL(): Promise<string> {
    return Promise.resolve(`${window.location.origin}/agents/api/`);
  }

  removeAuthSession(): Promise<void> {
    throw new Error("Not implemented");
  }
}
