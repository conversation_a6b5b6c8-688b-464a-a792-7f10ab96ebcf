import { callAP<PERSON>, makeWebFetch } from "./client";
import type { FeatureFlags, GetModelsResponse } from "../types";
import {
  SidecarAPIServerImpl,
  type SidecarAPIServer,
} from "@augment-internal/sidecar-libs/src/api/augment-api";

export interface APIServer extends SidecarAPIServer {
  getModels(): Promise<GetModelsResponse>;
  getFeatureFlags(): Promise<FeatureFlags>;
}

export class APIServerImpl extends SidecarAPIServerImpl implements APIServer {
  constructor() {
    // TODO (JW): Get a UUID
    const sessionId = "web-app";
    const userAgent = "augment-web";
    super(sessionId, userAgent, makeWebFetch());
  }

  async getModels(): Promise<GetModelsResponse> {
    return await callAPI("get-models");
  }
  async getFeatureFlags(): Promise<FeatureFlags> {
    const response = await this.getModels();
    return response.feature_flags || {};
  }
}
