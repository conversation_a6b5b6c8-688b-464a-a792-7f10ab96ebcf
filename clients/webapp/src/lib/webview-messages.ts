/**
 * TODO: Correctly respond to all incoming webview messages
 *
 * This is a stub to temporarily (7/31/2025) respond to all incoming webview messages
 * from the webviews in order to not have to wait 30+ seconds for the messages
 * to automatically timeout.
 *
 * All of these messages should be properly implemented and then the stub here
 * can be removed.
 */

export function handleUnimplementedWebviewMessages(msg: any) {
  switch (msg.type) {
    case "async-wrapper": {
      handleUnimplementedAsyncMessage(msg);
      break;
    }
    default: {
      console.warn("[webapp] Unhandled webview message:", msg);
      break;
    }
  }
}

function handleUnimplementedAsyncMessage(msg: any) {
  switch (msg.baseMsg.type) {
    case "chat-loaded": {
      console.warn('[WebHost] received "chat-loaded" message, TODO');
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "chat-initialize",
          data: {
            enableAgentMode: true,
          },
        }),
      );
      break;
    }
    case "agent-set-current-conversation": {
      console.warn(
        '[WebHost] received "agent-set-current-conversation" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "empty",
        }),
      );
      break;
    }
    case "get-remote-agent-pinned-status-request": {
      console.warn(
        '[WebHost] received "get-remote-agent-pinned-status-request" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "get-remote-agent-pinned-status-response",
          data: {},
        }),
      );
      break;
    }
    case "agent-get-edit-list-request": {
      console.warn(
        '[WebHost] received "agent-get-edit-list-request" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "agent-get-edit-list-response",
          data: {
            edits: [],
            totalCheckpointCount: 0,
            latestTimestamp: Date.now(),
          },
        }),
      );
      break;
    }
    case "get-remote-agent-notification-enabled-request": {
      console.warn(
        '[WebHost] received "get-remote-agent-notification-enabled-request" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "get-remote-agent-notification-enabled-response",
          data: {},
        }),
      );
      break;
    }
    case "get-remote-agent-status": {
      console.warn(
        '[WebHost] received "get-remote-agent-status" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "remote-agent-status-response",
          data: {
            isRemoteAgentSshWindow: false,
            remoteAgentId: undefined,
          },
        }),
      );
      break;
    }
    case "chat-mode-changed": {
      console.warn('[WebHost] received "chat-mode-changed" message, TODO');
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "empty",
          data: {},
        }),
      );
      break;
    }
    case "report-remote-agent-event": {
      console.warn(
        '[WebHost] received "report-remote-agent-event" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "empty",
          data: {},
        }),
      );
      break;
    }
    case "get-rules-list-request": {
      console.warn('[WebHost] received "get-rules-list-request" message, TODO');
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "get-rules-list-response",
          data: {
            rules: [],
          },
        }),
      );
      break;
    }
    case "check-has-ever-used-remote-agent": {
      console.warn(
        '[WebHost] received "check-has-ever-used-remote-agent" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "check-has-ever-used-remote-agent-response",
          data: false,
        }),
      );
      break;
    }
    case "set-has-ever-used-agent": {
      console.warn(
        '[WebHost] received "set-has-ever-used-agent" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "empty",
          data: {},
        }),
      );
      break;
    }
    case "get-subscription-info": {
      console.warn('[WebHost] received "get-subscription-info" message, TODO');
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "get-subscription-info-response",
          data: {
            data: {
              activeSubscription: {
                usageBalanceDepleted: false,
                endDate: new Date().toISOString(),
              },
              enterprise: false,
              inactiveSubscription: false,
            },
          },
        }),
      );
      break;
    }
    case "get-workspace-info-request": {
      console.warn(
        '[WebHost] received "get-workspace-info-request" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "get-workspace-info-response",
          data: {
            trackedFileCount: [1000, 500], // Mock tracked file counts
          },
        }),
      );
      break;
    }
    case "create-task-request": {
      console.warn('[WebHost] received "create-task-request" message, TODO');
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "create-task-response",
          data: {
            uuid: "new-task-uuid",
          },
        }),
      );
      break;
    }
    case "set-current-root-task-uuid": {
      console.warn(
        '[WebHost] received "set-current-root-task-uuid" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "empty",
          data: {},
        }),
      );
      break;
    }
    case "get-hydrated-task-request": {
      console.warn(
        '[WebHost] received "get-hydrated-task-request" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "get-hydrated-task-response",
          data: {
            task: undefined,
          },
        }),
      );
      break;
    }
    case "load-conversation-tooluse-states-request": {
      console.warn(
        '[WebHost] received "load-conversation-tooluse-states-request" message, TODO',
      );
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "load-conversation-tooluse-states-response",
          data: {
            toolUseStates: {},
          },
        }),
      );
      break;
    }
    case "resolve-file-request": {
      console.warn('[WebHost] received "resolve-file-request" message, TODO');
      window.dispatchEvent(
        createAsyncWrapperMessageEvent(msg.requestId, {
          type: "resolve-file-response",
          data: undefined,
        }),
      );
      break;
    }
    default: {
      console.warn("[webapp] Unhandled async webview message:", msg);
      break;
    }
  }
}

function createAsyncWrapperMessageEvent(
  requestId: string,
  baseMsg: any,
): MessageEvent {
  return new MessageEvent("message", {
    data: {
      type: "async-wrapper",
      requestId: requestId,
      error: null,
      baseMsg: baseMsg,
      streamCtx: undefined,
    },
  });
}
