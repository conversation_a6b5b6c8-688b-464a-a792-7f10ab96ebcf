<script lang="ts">
  import { goto } from "$app/navigation";
  import AgentCardBase from "$common-webviews/src/apps/remote-agent-manager/components/AgentList/AgentCardBase.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import AugmentLogo from "$common-webviews/src/design-system/icons/augment/augment-logo.svelte";
  import type { RemoteAgent } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import { getContext, onDestroy, onMount } from "svelte";
  import { getRemoteAgentContext } from "../lib/remote-agents/context/context";
  import { FeatureFlagsManager } from "../lib/shared/feature-flags.svelte";

  const featureFlags = getContext<FeatureFlagsManager>("featureFlags");
  const { remoteAgentModel, remoteAgentController } = getRemoteAgentContext();

  remoteAgentController.startAgentListStream();

  // Convert Svelte 4 readable store to Svelte 5 store
  let agentList = $state([] as RemoteAgent[]);
  const unsubscribe = remoteAgentModel.agentsListModel.agents.subscribe(
    (val) => {
      agentList = val;
    }
  );
  onDestroy(unsubscribe);

  onMount(() => {
    featureFlags.load()
  });

</script>

<svelte:head>
  <title>Agents - Augment 2</title>
</svelte:head>

<div class="l-agents-app-layout">
  <div class="l-agents-app-layout__header">
    <div class="l-agents-app-layout__header-left">
      <AugmentLogo />
      <TextAugment size={8}>Augment Code</TextAugment>
    </div>
  </div>
  <div class="l-agents-app-layout__section">
    <div class="l-agents-app-layout__section-header">
      <TextAugment size={6}>Agents</TextAugment>
    </div>
    <div class="l-agents-app-layout__section-content">
      <div class="l-agents-app-layout__agents-grid">
        {#if agentList}
          {#each agentList as agent (agent.remote_agent_id)}
            <AgentCardBase
              {agent}
              selected={false}
              onSelect={() => {
                goto(`/agents/chat/${agent.remote_agent_id}`);
              }}
              onDelete={async () => {}}
            />
          {/each}
        {/if}
      </div>
    </div>
  </div>
  <div class="l-agents-app-layout__section">
    <div class="l-agents-app-layout__section-header">
      <TextAugment size={6}>Debug</TextAugment>
    </div>
    <div class="l-agents-app-layout__section-content">
      <div class="space-y-6">
        {#if featureFlags.isLoaded}
          <div class="bg-gray-100 p-4 rounded text-sm">
            <div class="font-mono space-y-1">
              {#each Object.entries(featureFlags.flags).slice(-10) as [key, value] (key)}
                <div>{key}: {JSON.stringify(value)}</div>
              {/each}
              {#if Object.keys(featureFlags.flags).length === 0}
                <div class="text-gray-500">No feature flags available</div>
              {/if}
            </div>
          </div>
        {/if}

        <p class="text-gray-600">
          Common components: <a
            href="/agents/shared"
            class="text-blue-600 hover:text-blue-800 underline"
            >/agents/shared</a
          >
        </p>
        <p class="text-gray-600">
          Chat: <a
            href="/agents/chat/1"
            class="text-blue-600 hover:text-blue-800 underline"
            >/agents/chat/1</a
          >
        </p>
      </div>
    </div>
  </div>
</div>

<style>
  .l-agents-app-layout {
    padding: 0 var(--ds-spacing-9);
  }

  .l-agents-app-layout__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-4) 0;
  }

  .l-agents-app-layout__header-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-3);

    & :global(svg) {
      width: 28px;
      height: 28px;
    }
  }

  .l-agents-app-layout__section {
    margin-bottom: var(--ds-spacing-8);
  }

  .l-agents-app-layout__section-header {
    margin-bottom: var(--ds-spacing-4);
  }

  .l-agents-app-layout__agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--ds-spacing-3);
  }
</style>
