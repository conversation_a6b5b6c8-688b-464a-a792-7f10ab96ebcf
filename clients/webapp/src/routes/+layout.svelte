<script lang="ts">
    import { setContext } from 'svelte';
    import '../app.css';
    import { APIServerImpl } from '../lib/api';
    import { createRemoteAgentContext } from '../lib/remote-agents/context/context';
    import { FeatureFlagsManager } from '../lib/shared/feature-flags.svelte';
    import MonacoProvider from '$common-webviews/src/design-system/components/MonacoProvider';

	let { children } = $props();

	const apiServer = new APIServerImpl();
	const featureFlags = new FeatureFlagsManager(apiServer.getFeatureFlags.bind(apiServer));

	setContext('apiServer', apiServer);
	setContext('featureFlags', featureFlags);
	createRemoteAgentContext(apiServer);
</script>

<MonacoProvider.Root>
    {@render children?.()}
</MonacoProvider.Root>
