<script lang="ts">
    import CardAugment from '$common-webviews/src/design-system/components/CardAugment.svelte';
    import ButtonAugment from '$common-webviews/src/design-system/components/ButtonAugment.svelte';

    import CheckIcon from '$common-webviews/src/design-system/icons/check.svelte';
    import DownloadIcon from '$common-webviews/src/design-system/icons/download.svelte';
    import GearIcon from '$common-webviews/src/design-system/icons/gear.svelte';
    import ChevronLeftIcon from '$common-webviews/src/design-system/icons/chevron-left.svelte';
</script>

<svelte:head>
    <title>Components - Agents - Augment</title>
</svelte:head>

<div class="p-8">
    <h1 class="text-4xl font-bold text-blue-600">Components</h1>
    <p class="mt-4 text-gray-600">These are some components imported from the webviews design system.</p>

    <div class="mt-8">
        <h2 class="text-2xl font-semibold mb-4">Shared Components from Webviews</h2>

        <div class="space-y-4">
            <CardAugment size={2} variant="surface" class="p-4">
                <h3 class="text-lg font-medium">Surface Card</h3>
                <p class="text-gray-600">This is a CardAugment component imported from the webviews design system.</p>
            </CardAugment>

            <CardAugment size={1} variant="ghost" interactive class="p-4" on:click={() => console.log('clicked')}>
                <h3 class="text-lg font-medium">Interactive Ghost Card</h3>
                <p class="text-gray-600">Hover me</p>
            </CardAugment>
        </div>

        <div class="mt-8">
            <h3 class="text-xl font-semibold mb-4">ButtonAugment Examples</h3>
            <div class="space-y-4">
                <div class="flex gap-4 items-center">
                    <ButtonAugment variant="solid" color="accent">Solid Accent</ButtonAugment>
                    <ButtonAugment variant="soft" color="neutral">Soft Neutral</ButtonAugment>
                    <ButtonAugment variant="outline" color="success">Outline Success</ButtonAugment>
                    <ButtonAugment variant="ghost" color="error">Ghost Error</ButtonAugment>
                </div>

                <div class="flex gap-4 items-center">
                    <ButtonAugment size={1} variant="solid">Size 1</ButtonAugment>
                    <ButtonAugment size={2} variant="solid">Size 2</ButtonAugment>
                    <ButtonAugment size={3} variant="solid">Size 3</ButtonAugment>
                    <ButtonAugment size={4} variant="solid">Size 4</ButtonAugment>
                </div>

                <div class="flex gap-4 items-center">
                    <ButtonAugment variant="solid" disabled>Disabled</ButtonAugment>
                    <ButtonAugment variant="solid" loading>Loading</ButtonAugment>
                    <ButtonAugment variant="solid" radius="full">Full Radius</ButtonAugment>
                </div>

                <div class="flex gap-4 items-center">
                    <ButtonAugment variant="solid" color="success">
                        <CheckIcon slot="iconLeft" />
                        Success
                    </ButtonAugment>
                    <ButtonAugment variant="outline" color="neutral">
                        Download
                        <DownloadIcon slot="iconRight" />
                    </ButtonAugment>
                    <ButtonAugment variant="soft" color="accent">
                        <GearIcon slot="iconLeft" />
                        Settings
                        <ChevronLeftIcon slot="iconRight" />
                    </ButtonAugment>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8 space-y-4">
        <div class="p-4 bg-gray-50 rounded-lg">
            <h2 class="text-xl font-semibold text-gray-800">Navigation</h2>
            <a href="/agents" class="text-blue-600 hover:text-blue-800 underline">← Back to Home</a>
        </div>
    </div>
</div>
