<script lang="ts">
  import { page } from "$app/state";
  import type { IConversation } from "$common-webviews/src/apps/chat/models/types";
  import { PersonaType } from "@augment-internal/sidecar-libs/src/chat";
  import { getRemoteAgentContext } from "../../../lib/remote-agents/context/context";
  import {
    ExchangeStatus,
    SeenState,
    type ChatItem,
  } from "$common-webviews/src/apps/chat/types/chat-message";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { onDestroy } from "svelte";
  import type { RemoteAgent } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
  import RegularChevronLeftIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-left.svg?component";

  const { remoteAgentModel, remoteAgentController } = getRemoteAgentContext();

  const allConversations =
    remoteAgentModel.agentConversationsModels.conversations;
  const allAgents = remoteAgentModel.agentsListModel.agents;

  let agentID = $derived(page.params.agent);
  let currentAgent = $derived.by(() => {
    return $allAgents.find(
      (agent: RemoteAgent) => agent.remote_agent_id === agentID
    );
  });

  let chatHistory = $derived.by(() => {
      const conversation = $allConversations.get(agentID);
      return conversation ? conversation.map(({ exchange }, index) => {
          // Check if this is a pending message (optimistically added)
          const isPending = exchange.request_id.startsWith("pending-");

          return ({
            /* eslint-disable @typescript-eslint/naming-convention */
            seen_state: SeenState.seen,
            structured_request_nodes: exchange.request_nodes ?? [],
            status: isPending ? ExchangeStatus.sent : ExchangeStatus.success,
            request_message: exchange.request_message,
            response_text: isPending ? "" : exchange.response_text,
            structured_output_nodes: exchange.response_nodes ?? [],
            request_id: exchange.request_id ?? `remote-agent-${index}`,
            /* eslint-enable @typescript-eslint/naming-convention */
          } as ChatItem);
        }) : [];
  });

  let initialConversation = $derived({
    id: agentID,
    createdAtIso: new Date().toISOString(),
    lastInteractedAtIso: new Date().toISOString(),
    chatHistory,
    feedbackStates: {},
    requestIds: [],
    isShareable: false,
    extraData: {
      isAgentConversation: true,
    },
    personaType: PersonaType.DEFAULT,
  } as IConversation);

  let currentConversation = $derived($allConversations.get(agentID) ?? []);

  $effect(() => {
    if (agentID) {
      remoteAgentController.startAgentConversationStream(agentID);
      remoteAgentController.setActiveRemoteAgent(agentID);
    }
    return () => {
      if (agentID) {
        remoteAgentController.cancelAgentConversationStream(agentID);
      }
      remoteAgentController.setActiveRemoteAgent(undefined);
    };
  });

  onDestroy(() => {
    if (agentID) {
      remoteAgentController.cancelAgentConversationStream(agentID);
    }
    remoteAgentController.setActiveRemoteAgent(undefined);
  });

  const initialFlags = {
    enableAgentMode: false,
    enableDebugFeatures: true,
    enableExternalSources: false,
    enableWorkspaceFileChunks: true,
  };
  const initialSubscriptionInfo = undefined;
  const initialSubscriptionDismissed = false;
</script>

<div class="l-chat-page__header">
  <ButtonAugment on:click={() => history.back()} color="accent">
    <RegularChevronLeftIcon slot="iconLeft" />
    Back
  </ButtonAugment>
</div>
{#await import("$common-webviews/src/apps/chat/Chat.svelte")}
  <div class="loading">Loading...</div>
{:then { default: Chat }}
  <!-- We need a key to force the Chat component to re-render when the agent ID changes
   and when there is new content in the exchange -->
  {#key `${agentID}-${currentAgent?.updated_at}-${currentConversation?.length}`}
    <div class="test-chat-page">
      <div class="chat-container">
        <Chat
          {initialConversation}
          {initialFlags}
          {initialSubscriptionInfo}
          {initialSubscriptionDismissed}
        />
      </div>
    </div>
  {/key}
{:catch err}
  <div class="error">
    <h2>Error loading Chat component</h2>
    <p>{err.message}</p>
  </div>
{/await}

<style>
  .l-chat-page__header {
    padding: var(--ds-spacing-4);
  }

  .test-chat-page {
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .error {
    background: #fee;
    border: 1px solid #fcc;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
  }

  .error h2 {
    margin-top: 0;
    color: #c33;
  }

  .chat-container {
    border: 1px solid #ddd;
    border-radius: 8px;

    & :global(.chat) {
      height: calc(100dvh - 8rem);
    }

    --augment-chat-max-width: 100%; /* The Chat internally uses 100vw which will cause overflow */
  }
</style>
