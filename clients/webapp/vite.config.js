/* eslint-env node */
import { sveltekit } from "@sveltejs/kit/vite";
import { defineConfig } from "vite";
import tailwindcss from "@tailwindcss/vite";
import svg from "@poppanator/sveltekit-svg";
import { resolve, join } from "path";

const isDev =
  process.argv.includes("--watch") ||
  process.env.AUGMENT_JS_ENV === "development";

/**
 * Detects if running in a Bazel environment using environment variables.
 */
function isInBazel() {
  // We check for a few different environment variables set by <PERSON><PERSON> to detect
  // if we're running in a Bazel environment.

  // Based on logging actual Bazel executions, these environment variables are available:
  // - BAZEL_TARGET: The full Bazel target being executed (e.g., "//clients/common/webviews:webview-apps")
  // - BAZEL_WORKSPACE: The workspace name (e.g., "_main")
  // - BAZEL_PACKAGE: The package path (e.g., "clients/common/webviews")
  // - RUNFILES: Points to the runfiles directory (rules_js sets this instead of RUNFILES_DIR)
  // - JS_BINARY__RUNFILES: Alternative runfiles path set by js_binary rules
  // - BAZEL_TARGET_CPU: Target CPU architecture (e.g., "darwin_arm64")
  // - BAZEL_COMPILATION_MODE: Compilation mode (e.g., "fastbuild")
  // - BAZEL_BINDIR: Binary output directory
  return (
    !!(
      process.env.BAZEL_TARGET ||
      process.env.BAZEL_WORKSPACE ||
      process.env.RUNFILES ||
      process.env.JS_BINARY__RUNFILES
    ) && !isDev
  );
}

/**
 * Returns the path to the workspace directory ("this" directory),
 * handling both Bazel and development environments.
 *
 * When run through pnpm, uses relative path resolution from __dirname.
 *
 * In Bazel environments, uses process.cwd() which correctly resolves to the sandbox execroot.
 * (We can't use __dirname in this case as it escapes the sandbox and can cause confusing
 * non-hermetic behavior).
 *
 * @see https://github.com/augmentcode/augment/pull/30078
 */
function workspacePath(...pathSegments) {
  if (isInBazel()) {
    // In Bazel, use cwd which points to the execroot containing the workspace structure
    return join(process.cwd(), ...pathSegments);
  } else {
    // In development, resolve relative to this file's directory
    return resolve(__dirname, ...pathSegments);
  }
}

const DEV_PORT = 5300;

export default defineConfig({
  plugins: [
    sveltekit(),
    tailwindcss(),
    // Copied from clients/common/webviews/vite.config.ts
    svg({
      // bazel requires absolute paths for this to work
      includePaths: [
        workspacePath(
          "..",
          "common",
          "webviews",
          "src",
          "design-system",
          "icons",
          "fontawesome",
          "svgs",
        ),
      ],
      svgoOptions: {
        multipass: true,
        plugins: [
          {
            name: "preset-default",
            // by default svgo removes the viewBox which prevents svg icons from scaling
            // not a good idea! https://github.com/svg/svgo/pull/1461
            params: { overrides: { removeViewBox: false } },
          },
          { name: "removeAttrs", params: { attrs: "(fill|stroke)" } },
          {
            /**
             * This adds a attribute which we select to automatically style icons
             */
            name: "addAttributesToSVGElement",
            params: {
              attributes: [{ "data-ds-icon": "fa" }],
            },
          },
        ],
      },
    }),
  ],

  resolve: {
    alias: {
      "$common-webviews": workspacePath("..", "common", "webviews"),
      $vscode: workspacePath("..", "vscode"),
      "@augment-internal/sidecar-libs": workspacePath("..", "sidecar", "libs"),
      $clients: workspacePath(".."),
    },
  },

  server: {
    port: DEV_PORT,
    host: true,
    proxy:
      isDev && process.env.PROXY_URL
        ? {
            "/agents/api": {
              target: process.env.PROXY_URL,
              changeOrigin: true,
            },
            "/auth": {
              target: process.env.PROXY_URL,
              changeOrigin: true,
              secure: false,
              headers: {
                "X-Forwarded-Protocol": "http",
                "X-Forwarded-Hostname": `localhost:${DEV_PORT}`,
              },
            },
          }
        : undefined,
    fs: isDev
      ? {
          allow: ["..", "/home/<USER>/.cache/bazel", "../../.."],
        }
      : undefined,
  },

  build: {
    outDir: "dist",
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["svelte"],
        },
      },
    },
  },

  define: {
    "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV),
  },

  base: "/agents",
});
