import path from "path";
import fs from "fs";
import { spawn } from "child_process";

// Read username from ~/.augment/user.json
function getUsernameFromConfig() {
  const userConfigPath = path.join(
    process.env.HOME || process.env.USERPROFILE,
    ".augment",
    "user.json",
  );

  try {
    if (fs.existsSync(userConfigPath)) {
      const userConfig = JSON.parse(fs.readFileSync(userConfigPath, "utf8"));
      if (userConfig.name) {
        return userConfig.name;
      }
    }
    throw new Error("Username not found in config file");
  } catch (error) {
    console.error(`Error reading username from config: ${error.message}`);
    throw error;
  }
}

const PROXY_URL = `https://rpc-proxy.dev-${getUsernameFromConfig()}.us-central1.dev.augmentcode.com/`;

const child = spawn("pnpm", ["dev"], {
  stdio: "inherit",
  env: { ...process.env, PROXY_URL, AUGMENT_JS_ENV: "development" },
});

child.on("close", (code) => {
  process.exit(code ?? 0);
});

process.on("SIGINT", () => {
  child.kill("SIGINT");
});

process.on("exit", () => {
  child.kill();
});
