load("@aspect_rules_js//js:defs.bzl", "js_library", "js_run_binary")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//clients/webapp:vite/package_json.bzl", vite_bin = "bin")

npm_link_all_packages()

AGENTS_APP_DEPS = [
    ":node_modules/@sveltejs/adapter-static",
    ":node_modules/@sveltejs/kit",
    ":node_modules/@sveltejs/vite-plugin-svelte",
    ":node_modules/@tailwindcss/vite",
    ":node_modules/@tanstack/svelte-query",
    ":node_modules/@types/node",
    ":node_modules/svelte",
    ":node_modules/svelte-check",
    ":node_modules/tailwindcss",
    ":node_modules/typescript",
    ":node_modules/vite",
    ":node_modules/@radix-ui/colors",
    ":node_modules/@poppanator/sveltekit-svg",
    "//clients/common/webviews:webview-apps-full-src",
]

vite_bin.vite_binary(
    name = "vite",
    chdir = package_name(),
    data = AGENTS_APP_DEPS,
    env = {"NODE_OPTIONS": "--max-old-space-size=8192"},
)

js_library(
    name = "agents_app_src",
    srcs = glob([
        "src/**/*.ts",
        "src/**/*.svelte",
        "src/**/*.css",
        "src/**/*.html",
        "static/**/*",
        "*.js",
        "*.json",
        "*.ts",
    ]),
    deps = AGENTS_APP_DEPS,
)

js_run_binary(
    name = "build",
    srcs = [":agents_app_src"],
    args = ["build"],
    out_dirs = ["dist"],
    tool = ":vite",
    visibility = [
        "//services/customer/frontend:__pkg__",
        "//services/web_rpc_proxy/backend:__pkg__",
    ],
)
