/**
 * ESLint config for the agents webapp.
 *
 * TODO: If we want to support more svelte 5 features like @render, we will need
 * to update eslint-plugin-svelte to the latest. But eslint-plugin-svelte 3+ doesn't support
 * the hierarchical eslintrc files, only the flag eslint.config.js format. So we will need
 * to migrate to that format before we can upgrade.
 * That will require removing all our .eslintrc.cjs files and moving to  a single eslint.config.js.
 * https://sveltejs.github.io/eslint-plugin-svelte/migration/#2-es-lint-flat-config-only
 */

module.exports = {
  root: true,

  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
  },
  env: {
    browser: true,
  },

  // Base config
  extends: ["eslint:recommended"],

  overrides: [
    // Typescript
    {
      files: ["**/*.{ts,tsx}", "./**/*.svelte.ts"],
      plugins: ["@typescript-eslint", "import"],
      parser: "@typescript-eslint/parser",
      parserOptions: {
        sourceType: "module",
        project: "./tsconfig.json",
        tsconfigRootDir: __dirname,
      },
      settings: {
        "import/internal-regex": "^~/",
        "import/resolver": {
          node: {
            extensions: [".ts", ".tsx"],
          },
          typescript: {
            alwaysTryTypes: true,
          },
        },
      },
      extends: [
        "plugin:@typescript-eslint/recommended",
        "plugin:import/recommended",
        "plugin:import/typescript",
      ],
      rules: {
        "@typescript-eslint/consistent-type-imports": [
          "error",
          {
            prefer: "type-imports",
            fixStyle: "inline-type-imports",
          },
        ],
        "@typescript-eslint/no-import-type-side-effects": "error",
        "@typescript-eslint/no-unused-vars": [
          "error",
          { argsIgnorePattern: "^_", varsIgnorePattern: "^_" },
        ],
        "@typescript-eslint/no-explicit-any": "off",
        "import/default": "off",
        "import/named": "error",
        "import/no-duplicates": "error",
        "import/no-unresolved": "off",
        "import/namespace": "off", // Rule does not support const type parameters
        "no-constant-condition": "off",
        "no-empty-pattern": "off",
        "@typescript-eslint/no-misused-promises": [
          "error",
          {
            checksVoidReturn: false,
          },
        ],
      },
    },

    {
      files: ["./**/*.svelte"],
      extends: ["plugin:svelte/recommended"],
      parser: "svelte-eslint-parser",
      parserOptions: {
        parser: "@typescript-eslint/parser",
        extraFileExtensions: [".svelte"],
        ecmaVersion: "latest",
        sourceType: "module",
      },
      rules: {
        // These errors seem to occur due to type misunderstandings between
        // svelte and typescript.
        "@typescript-eslint/no-unsafe-member-access": "off",
        "@typescript-eslint/no-unsafe-argument": "off",
        "@typescript-eslint/no-unsafe-assignment": "off",
        "@typescript-eslint/no-unsafe-call": "off",
        "@typescript-eslint/no-unsafe-return": "off",
        "@typescript-eslint/no-redundant-type-constituents": "off",
      },
      env: {
        browser: true,
      },
      globals: {
        // Svelte 5 runes
        $state: "readonly",
        $derived: "readonly",
        $effect: "readonly",
        $props: "readonly",
        $bindable: "readonly",
        $inspect: "readonly",
      },
    },

    // Node
    {
      files: [".eslintrc.cjs", "scripts/*.js"],
      env: {
        node: true,
      },
    },
  ],
};
