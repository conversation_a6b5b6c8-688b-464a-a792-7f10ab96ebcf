{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"allowJs": true, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowSyntheticDefaultImports": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "types": ["vite/client", "svelte"], "noEmit": true, "isolatedModules": true, "paths": {"$common-webviews/*": ["../common/webviews/*"], "@augment-internal/sidecar-libs/*": ["../sidecar/libs/*", "../sidecar/libs/out/*"]}}, "include": ["src/**/*.d.ts", "src/**/*.ts", "src/**/*.js", "src/**/*.svelte"]}