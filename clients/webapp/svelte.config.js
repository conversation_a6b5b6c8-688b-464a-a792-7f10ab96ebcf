import adapter from "@sveltejs/adapter-static";
import { vitePreprocess } from "@sveltejs/vite-plugin-svelte";

/** @type {import('@sveltejs/kit').Config} */
const config = {
  // https://kit.svelte.dev/docs/integrations#preprocessors
  // Enable script preprocessing for TypeScript features in webview components
  preprocess: vitePreprocess({ script: true }),

  kit: {
    // Static adapter for SPA mode
    // https://svelte.dev/docs/kit/adapter-static
    adapter: adapter({
      pages: "dist",
      assets: "dist",
      fallback: "index.html",
      precompress: false,
      strict: true,
    }),

    paths: {
      base: "/agents",
    },
  },
};

export default config;
