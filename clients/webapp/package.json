{"name": "agents-web-app", "version": "0.0.1", "private": true, "description": "Augment webapp for managing agents.", "type": "module", "scripts": {"dev": "AUGMENT_JS_ENV=development vite dev --port 5300", "dev:e2e": "concurrently \"bazel run //services/customer/frontend:dev\" \"pnpm dev\"", "dev:proxy": "node scripts/dev.js", "build": "NODE_OPTIONS=--max-old-space-size=8192 vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/kit": "^2.22.2", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@tailwindcss/vite": "^4.0.16", "@types/node": "^20.14.11", "concurrently": "^8.2.2", "globals": "^16.3.0", "svelte": "^5.2.9", "svelte-check": "^4.0.8", "tailwindcss": "^4.0.16", "typescript": "^5.5.3", "vite": "^5.4.19"}, "dependencies": {"@poppanator/sveltekit-svg": "^5.0.1", "@radix-ui/colors": "^3.0.0", "@sveltejs/adapter-static": "^3.0.8", "@tanstack/svelte-query": "^5.81.2"}, "engines": {"node": ">=20.0.0", "pnpm": "9"}}