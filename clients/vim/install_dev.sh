#!/usr/bin/env bash

set -e

which node >/dev/null || (echo "Error: node not found" && exit 1)

# Build the sidecar
echo "Building sidecar..."
pushd . >/dev/null
cd sidecar
npm install
echo
npm run build
echo
popd >/dev/null

# Install the plugin
PLUGIN_SOURCE=$(pwd)/plugin
PLUGIN_TARGET=$HOME/.vim/pack/augment/start/augment-vim-dev
mkdir -p $(dirname $PLUGIN_TARGET)
rm -f $PLUGIN_TARGET
ln -s $PLUGIN_SOURCE $PLUGIN_TARGET
echo "Symlinked plugin from $PLUGIN_SOURCE to $PLUGIN_TARGET"

# Install the sidecar
SIDECAR_SOURCE=$(pwd)/sidecar/dist/server.js
SIDECAR_TARGET=$(pwd)/plugin/dist/server.js
mkdir -p $(dirname $SIDECAR_TARGET)
rm -f $SIDECAR_TARGET
ln -s $SIDECAR_SOURCE $SIDECAR_TARGET
echo "Symlinked sidecar from $SIDECAR_SOURCE to $SIDECAR_TARGET"
