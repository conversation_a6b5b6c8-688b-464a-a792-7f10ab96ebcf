# Augment Vim Plugin

Source code and associated tooling for the Augment Vim plugin.

## Directory Structure

- `plugin/`: Core plugin implementation. During the release process in
  `.github/workflows/vim-release.yml`, this directory is copied 1:1 into the
  `augment.vim` repository and a new commit is created, so only changes that are
  specific to the release version of the plugin should be made here.
- `sidecar/`: LSP server implementation.
- `test/`: Test framework for the plugin.
- `version/`: Version management scripts.

## Development

To install the development version of the plugin:

```bash
./install_dev.sh
```

This script will:
1. Build the LSP server from the `sidecar/` directory
2. Create a symlink to the plugin at `~/.vim/pack/augment/start/augment-vim-dev`
3. Link the built LSP server into the plugin directory
