load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")
load("@aspect_rules_js//js:defs.bzl", "js_binary", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

SRC_FILES = glob(
    ["src/**/*.ts"],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
)

TEST_FILES = glob([
    "src/**/__tests__/**/*.ts",
    "src/**/__tests__/__fixtures__/*.json",
    "src/**/__tests__/**/__snapshots__/*.snap",
    "src/**/__tests__/**/*.html",
    "src/**/__mocks__/**/*.ts",
])

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
)

# Add a package target if needed
filegroup(
    name = "package",
    srcs = ["package.json"],
)

jest_test(
    name = "test",
    timeout = "moderate",
    config = "jest.config.js",
    data = SRC_FILES + TEST_FILES + [
        ":node_modules",
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
        "//third_party/node-ignore",
    ],
    node_modules = "//clients/vim/sidecar:node_modules",
)

esbuild(
    name = "server_bundle",
    srcs = SRC_FILES + [
        "license_banner.txt",
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
    ],
    entry_point = "src/server.ts",
    format = "cjs",
    minify = True,
    output = "dist/server.bundle.js",
    platform = "node",
    target = "ES2022",
    deps = [
        ":node_modules",
        "//third_party/node-ignore",
    ],
)

js_binary(
    name = "sidecar",
    data = [
        ":server_bundle",
    ],
    entry_point = "dist/server.bundle.js",
    visibility = ["//clients/vim:__subpackages__"],
)
