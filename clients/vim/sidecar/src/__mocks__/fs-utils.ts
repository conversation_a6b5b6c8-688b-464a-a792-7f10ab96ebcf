import { URI as Uri } from "vscode-uri";

import { FileType, StatInfo } from "../utils/types";
import { BlobNameCalculator } from "../workspace/blob-name-calculator";
import { MockFileSystem } from "./mock-filesystem";

export interface FSSubscriber extends MockFileSystem.FSSubscriber {}
export interface FSWatcher extends MockFileSystem.FSWatcher {}
export interface FSIterator extends MockFileSystem.Iterator {}

/**
 * MockFSUtils is a class that is used to implement a mock version of the "fs-utils"
 * module. The actual functions of the mock module are below this class.
 */
export class MockFSUtils {
    private _fs!: MockFileSystem.FS;
    private _blobNameCalculator = new BlobNameCalculator(1000);
    public readonly textEncoder = new TextEncoder(); // uses utf-8
    public readonly textDecoder = new TextDecoder(); // uses utf-8

    constructor() {
        this._newFS();
    }

    get fs(): MockFileSystem.FS {
        return this._fs;
    }

    reset() {
        this._fs.stopWatchers();
        this._newFS();
    }

    private _newFS() {
        this._fs = new MockFileSystem.FS();
    }

    readFileRaw(pathName: string): Uint8Array {
        return this._fs.readFile(pathName);
    }

    readFileUtf8(pathName: string): string {
        const content = this._fs.readFile(pathName);
        return this.textDecoder.decode(content);
    }

    statFile(pathName: string): StatInfo {
        return this._fs.stat(
            pathName,
            (_: string, type: FileType, size: number | undefined, mtime: number): StatInfo => {
                return { size: size ?? 0, type, mtime };
            }
        );
    }

    readDirectory(pathName: string): [string, FileType][] {
        return this._fs.readDir(
            pathName,
            (name: string, type: FileType, _: number | undefined): [string, FileType] => {
                return [name, type];
            }
        );
    }

    makeDirs(pathName: string): void {
        return this._fs.makeDirs(pathName);
    }

    rename(oldPathName: string, newPathName: string): void {
        return this._fs.rename(oldPathName, newPathName);
    }

    writeFileUtf8(pathName: string, text: string, makeDirs = false, mtime?: number): string {
        return this.writeFileRaw(pathName, this.textEncoder.encode(text), makeDirs, mtime);
    }

    writeFileUtf8Sync(pathName: string, text: string, makeDirs = false, mtime?: number): string {
        return this.writeFileRaw(pathName, this.textEncoder.encode(text), makeDirs, mtime);
    }

    writeFileRaw(pathName: string, contents: Uint8Array, makeDirs = false, mtime?: number): string {
        this._fs.writeFile(pathName, contents, makeDirs, mtime);
        return this._blobNameCalculator.calculate(pathName, contents)!;
    }

    verifyFileUtf8(pathName: string, expectedText: string): void {
        this.verifyFileRaw(pathName, this.textEncoder.encode(expectedText));
    }

    verifyFileRaw(pathName: string, expectedContents: Uint8Array): void {
        const contents = this.readFileRaw(pathName);
        expect(contents).toStrictEqual(expectedContents);
    }

    deleteFile(pathName: string) {
        this._fs.deleteFile(pathName);
    }

    createDirectories(pathName: string) {
        this._fs.makeDirs(pathName);
    }

    createIterator(startPathName = "/"): FSIterator {
        return this._fs.makeIterator(startPathName);
    }

    createWatcher(subscriber: FSSubscriber): FSWatcher {
        return this._fs.createWatcher(subscriber);
    }

    publishFSEvent(type: MockFileSystem.FSEvent, pathOrUri: string | Uri) {
        this._fs.publish(type, pathOrUri);
    }
}

export var mockFSUtils = new MockFSUtils();

export async function statFile(pathName: string): Promise<StatInfo> {
    return mockFSUtils.statFile(pathName);
}

export function statFileSync(pathName: string): StatInfo {
    return mockFSUtils.statFile(pathName);
}

export async function readDirectory(pathName: string): Promise<[string, FileType][]> {
    return mockFSUtils.readDirectory(pathName);
}

export function readDirectorySync(pathName: string): [string, FileType][] {
    return mockFSUtils.readDirectory(pathName);
}

export async function makeDirs(pathName: string): Promise<void> {
    mockFSUtils.makeDirs(pathName);
}

export async function rename(oldPath: string, newPath: string): Promise<void> {
    mockFSUtils.rename(oldPath, newPath);
}

export async function readFileRaw(pathName: string): Promise<Uint8Array> {
    return mockFSUtils.readFileRaw(pathName);
}

export async function readFileUtf8(pathName: string): Promise<string> {
    return mockFSUtils.readFileUtf8(pathName);
}

export async function writeFileUtf8(pathName: string, text: string): Promise<void> {
    mockFSUtils.writeFileUtf8(pathName, text);
}

export async function writeFileUtf8Sync(pathName: string, text: string): Promise<void> {
    mockFSUtils.writeFileUtf8Sync(pathName, text);
}

export async function fileIsReadable(_pathName: string): Promise<boolean> {
    return true;
}

export function fileExists(pathName: string): boolean {
    try {
        const st = mockFSUtils.statFile(pathName);
        return st.type === FileType.file;
    } catch {
        return false;
    }
}

export async function directoryExistsAsync(pathName: string): Promise<boolean> {
    try {
        const st = mockFSUtils.statFile(pathName);
        return st.type === FileType.directory;
    } catch {
        return false;
    }
}

export function publishFSEvent(type: MockFileSystem.FSEvent, pathOrUri: string | Uri) {
    mockFSUtils.publishFSEvent(type, pathOrUri);
}
