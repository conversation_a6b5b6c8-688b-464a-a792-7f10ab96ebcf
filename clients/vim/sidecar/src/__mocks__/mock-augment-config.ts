import merge from "lodash/merge";

import { AugmentConfig, AugmentConfigListener, UserConfig } from "../augment-config-listener";
import type { WorkspaceConfiguration } from "../vscode";
import { mockDefaultModelName } from "./mock-modelinfo";

export function getExampleUserConfig(overrides?: DeepPartial<UserConfig>): UserConfig {
    return merge(
        {
            disableFocusOnAugmentPanel: true,
            enableShortcutsAboveSelectedText: true,
            shortcutsDisplayDelayMS: 2000,
            enableEmptyFileHint: true,
            enableBackgroundSuggestions: true,
            enableGlobalBackgroundSuggestions: false,
            showAllBackgroundSuggestionLineHighlights: true,
            completions: {
                enableAutomaticCompletions: true,
                disableCompletionsByLanguage: [],
                enableQuickSuggestions: true,
            },
            chat: {
                userGuidelines: "",
            },
            agent: {
                model: mockDefaultModelName,
            },
            conflictingCodingAssistantCheck: true,
            advanced: {
                apiToken: "example-api-token-1234abcd",
                completionURL: "http://api.augmentcode.com",
                model: mockDefaultModelName,
                codeInstruction: {
                    model: mockDefaultModelName,
                },
                chat: {
                    model: mockDefaultModelName,
                },
                agent: {
                    model: mockDefaultModelName,
                },

                oauth: {
                    clientID: "abcd1234",
                    url: "http://auth_api.augmecode.com",
                },
                enableWorkspaceUpload: true,
                enableDebugFeatures: true,
                enableReviewerWorkflows: true,
                completions: {
                    timeoutMs: 800,
                    maxWaitMs: 1600,
                    addIntelliSenseSuggestions: true,
                    filterThreshold: undefined,
                },
                openFileManagerV2: {
                    enabled: false,
                },
                enableDataCollection: false,
                nextEdit: {
                    url: undefined,
                    enabled: undefined,
                    backgroundEnabled: false,
                    showInstructionTextbox: false,
                },
                recencySignalManager: {
                    collectTabSwitchEvents: false,
                },
                preferenceCollection: {
                    enable: false,
                    enableRetrievalDataCollection: false,
                    enableRandomizedMode: false,
                },
                vcs: {
                    watcherEnabled: false,
                },
                conflictingCodingAssistantCheck: true,
                smartPaste: {
                    url: undefined,
                    model: undefined,
                },
                instructions: {
                    model: undefined,
                },
            },
        },
        overrides
    );
}

export function getExampleAugmenConfig(overrides?: DeepPartial<AugmentConfig>): AugmentConfig {
    const augmentConfig = AugmentConfigListener.normalizeConfig(
        AugmentConfigListener.normalizeUserConfig(generateMockWorkspaceConfig())
    );
    return merge(augmentConfig, overrides);
}

// Generate a mock workspace config for Augment's config
export function generateMockWorkspaceConfig(
    config: DeepPartial<UserConfig> = {}
): WorkspaceConfiguration {
    return {
        ...config,
    } as WorkspaceConfiguration;
}

// This function is useful if you want a workspace configuration for something other than Augments config.
// If you want Augment config - use generateMockWorkspaceConfig.
export function generateUncheckedMockWorkspaceConfig(config: Object = {}): WorkspaceConfiguration {
    return {
        ...config,
    } as WorkspaceConfiguration;
}

type DeepPartial<T> = {
    [P in keyof T]?: DeepPartial<T[P]>;
};
