/**
 * vscode-types.ts contains a mock implementation of a subset of the vscode
 * interface for use in unit tests.
 */

/* eslint-disable @typescript-eslint/naming-convention */
// We pull the mock like this instead of through the `vscodeMock` object because
// we want access to the helper methods it provides (e.g. `isUri`), but the
// vscodeMock object gets its type definitions from the real vscode interaface,
// not from the mock.
import * as path from "path";

import { uriToAbsPath } from "../utils/uri";
import {
    CancellationTokenSource,
    ConfigurationChangeEvent,
    Disposable,
    EndOfLine,
    Event,
    Position,
    Range,
    SecretStorageChangeEvent,
    Uri,
    Utils,
    WorkspaceConfiguration,
    WorkspaceFolder,
    WorkspaceFoldersChangeEvent,
} from "../vscode-impl";
import { BlobNameCalculator } from "../workspace/blob-name-calculator";
import { FSSubscriber, FSWatcher, mockFSUtils } from "./fs-utils";
import { generateMockWorkspaceConfig, getExampleUserConfig } from "./mock-augment-config";

export { CancellationTokenSource, Disposable };
export { Position, Range, Uri, Utils };
export class TextLine {
    constructor(
        public lineNumber: number,
        public text: string,
        public range: Range,
        public rangeIncludingLineBreak: Range,
        public firstNonWhitespaceCharacterIndex: number,
        public isEmptyOrWhitespace: boolean
    ) {}
}

export class TextDocument {
    protected _version = 0;
    protected _savedVersion = 0;

    constructor(
        public readonly uri: Uri,
        protected _text: string
    ) {}

    // Map of file extensions to vscode language ids
    private static extToLangMap = new Map<string, string>([
        [".js", "javascript"],
        [".ts", "typescript"],
        [".json", "json"],
        [".java", "java"],
        [".c", "c"],
        [".cpp", "cpp"],
        [".h", "c"],
        [".hpp", "cpp"],
        [".py", "python"],
        [".go", "go"],
        [".rs", "rust"],
        [".md", "markdown"],
    ]);

    public get fileName(): string {
        return this.uri.fsPath;
    }

    public get isUntitled(): boolean {
        return false;
    }

    public get languageId(): string {
        const ext = path.extname(this.fileName);
        return ext ? TextDocument.extToLangMap.get(ext) || ext : "plaintext";
    }

    public get version(): number {
        return this._version;
    }

    public get isDirty(): boolean {
        return this._version !== this._savedVersion;
    }

    public get isClosed(): boolean {
        return false;
    }

    public save(): Thenable<boolean> {
        this._savedVersion = this._version;
        return Promise.resolve(true);
    }

    public get eol(): EndOfLine {
        return EndOfLine.LF;
    }

    public get lineCount(): number {
        let line = 0;
        for (const ch of this._text) {
            if (ch === "\n") {
                line++;
            }
        }
        if (this._text.length > 0) {
            line++;
        }
        return line;
    }

    public lineAt(location: number | Position): TextLine {
        let line: number;
        if (location instanceof Position) {
            line = location.line;
        } else if (typeof location === "number") {
            line = location;
        } else {
            throw new Error("Invalid argument");
        }

        const lines = this._text.split("\n");
        const text = lines[line];
        const endsInNewline = line < lines.length - 1 || this.getText().endsWith("\n");
        const range = new Range(line, 0, line, text.length);
        const rangeIncludingLineBreak = new Range(
            line,
            0,
            line,
            text.length + (endsInNewline && text.length > 0 ? 1 : 0)
        );
        let firstNonWhitespaceCharacterIndex = text.search(/\S/);
        const isEmptyOrWhitespace = text.trim() === "";

        return new TextLine(
            line,
            text,
            range,
            rangeIncludingLineBreak,
            firstNonWhitespaceCharacterIndex,
            isEmptyOrWhitespace
        );
    }

    public offsetAt(position: Position): number {
        let line = 0;
        let character = 0;
        let offset = 0;
        for (; offset < this._text.length; offset++) {
            if (line === position.line && character === position.character) {
                return offset;
            }
            if (this._text[offset] === "\n") {
                if (line >= position.line) {
                    throw new Error(
                        `TextDocument line ${position.line} doesn't have` +
                            ` ${position.character} characters`
                    );
                }
                line++;
                character = 0;
            } else {
                character++;
            }
        }
        if (line === position.line && character === position.character) {
            return offset;
        }
        if (line === position.line) {
            throw new Error(
                `TextDocument line ${position.line} doesn't have` +
                    ` ${position.character} characters`
            );
        }
        // NOTE(arun): vscode doesn't throw an error if the line is out of range, but
        // rather returns the very last character of the document.
        return this._text.length;
    }

    public positionAt(offset: number): Position {
        offset = Math.min(offset, this._text.length);
        let line = 0;
        let character = 0;
        for (let curr = 0; curr < offset; curr++) {
            if (this._text[curr] === "\n") {
                line++;
                character = 0;
            } else {
                character++;
            }
        }
        return new Position(line, character);
    }

    public getText(range?: Range): string {
        const start = range === undefined ? 0 : this.offsetAt(range.start);
        const end = range === undefined ? this._text.length : this.offsetAt(range.end);
        return this._text.slice(start, end);
    }

    getWordRangeAtPosition(_position: Position, _regex?: RegExp): Range | undefined {
        throw new Error("Not implemented");
    }

    validateRange(range: Range): Range {
        const start = this.validatePosition(range.start);
        const end = this.validatePosition(range.end);
        return new Range(start, end);
    }

    validatePosition(position: Position): Position {
        try {
            this.offsetAt(position);
            return position;
        } catch {
            // If the character count is out of range, return end of that line
            // OR return the end of the document
            if (position.line <= this.lineCount - 1) {
                return new Position(position.line, this.lineAt(position.line).text.length);
            }
            return this.positionAt(this._text.length);
        }
    }
}

export type MutableTextDocumentInsert = {
    offset: number;
    text: string;
};
export type MutableTextDocumentDelete = {
    offset: number;
    count: number;
};

export type MutableTextDocumentUpdate = {
    offset: number;
    count: number;
    text: string;
};

export type MutableTextDocumentReplace = {
    offset: number;
    count: number;
    text: string;
};

export type MutableTextDocumentChange = {
    type: "insert" | "delete" | "update" | "replace";
    change:
        | MutableTextDocumentInsert
        | MutableTextDocumentDelete
        | MutableTextDocumentUpdate
        | MutableTextDocumentReplace;
};

export class MutableTextDocument extends TextDocument {
    static readonly textEncoder = new TextEncoder();
    private _blobNameCalculator = new BlobNameCalculator(1000);

    public markDirty(): void {
        this._version++;
    }

    public insert(offset: number, text: string): TextDocumentChangeEvent {
        const change = this._insert(offset, text);
        this.markDirty();
        return new TextDocumentChangeEvent(this, [change], undefined);
    }

    public multiInsert(changes: [number, string][]): TextDocumentChangeEvent {
        const events = changes.map(([offset, text]) => this._insert(offset, text));
        this.markDirty();
        return new TextDocumentChangeEvent(this, events, undefined);
    }

    private _insert(offset: number, text: string): TextDocumentContentChangeEvent {
        const position = this.positionAt(offset);
        this._text = this._text.slice(0, offset) + text + this._text.slice(offset);
        return TextDocumentContentChangeEvent.forInsert(this, position, offset, text);
    }

    public update(offset: number, count: number, text: string): TextDocumentChangeEvent {
        const change = this._update(offset, count, text);
        this.markDirty();
        return new TextDocumentChangeEvent(this, [change], undefined);
    }

    private _update(offset: number, count: number, text: string): TextDocumentContentChangeEvent {
        const position = this.positionAt(offset);
        this._text = this._text.slice(0, offset) + text + this._text.slice(offset + count);
        return TextDocumentContentChangeEvent.forUpdate(this, position, offset, count, text);
    }

    public delete(offset: number, count: number): TextDocumentChangeEvent {
        const change = this._delete(offset, count);
        this.markDirty();
        return new TextDocumentChangeEvent(this, [change], undefined);
    }

    private _delete(offset: number, count: number): TextDocumentContentChangeEvent {
        const position = this.positionAt(offset);
        this._text = this._text.slice(0, offset) + this._text.slice(offset + count);
        return TextDocumentContentChangeEvent.forDelete(this, position, offset, count);
    }

    private _replace(
        offset: number,
        deletedChars: number,
        insertedText: string
    ): TextDocumentContentChangeEvent[] {
        if (deletedChars === 0) {
            return [this._insert(offset, insertedText)];
        } else if (insertedText.length === 0) {
            return [this._delete(offset, deletedChars)];
        }
        const change1 = this._delete(offset, deletedChars);
        const change2 = this._insert(offset, insertedText);
        return [change1, change2];
    }

    public replace(
        offset: number,
        deletedChars: number,
        insertedText: string
    ): TextDocumentChangeEvent {
        const changes = this._replace(offset, deletedChars, insertedText);
        this.markDirty();
        return new TextDocumentChangeEvent(this, changes, undefined);
    }

    public applyChanges(changes: readonly MutableTextDocumentChange[]): TextDocumentChangeEvent {
        const contentChanges: TextDocumentContentChangeEvent[] = [];
        for (const { type, change } of changes) {
            switch (type) {
                case "insert": {
                    const insertChange = change as MutableTextDocumentInsert;
                    contentChanges.push(this._insert(insertChange.offset, insertChange.text));
                    break;
                }
                case "update": {
                    const updateChange = change as MutableTextDocumentUpdate;
                    contentChanges.push(
                        this._update(updateChange.offset, updateChange.count, updateChange.text)
                    );
                    break;
                }
                case "delete": {
                    const deleteChange = change as MutableTextDocumentDelete;
                    contentChanges.push(this._delete(deleteChange.offset, deleteChange.count));
                    break;
                }
                case "replace": {
                    const replaceChange = change as MutableTextDocumentReplace;
                    contentChanges.push(
                        ...this._replace(
                            replaceChange.offset,
                            replaceChange.count,
                            replaceChange.text
                        )
                    );
                    break;
                }
            }
        }
        this.markDirty();
        return new TextDocumentChangeEvent(this, contentChanges, undefined);
    }

    public applyEdit(edit: TextEdit): TextDocumentChangeEvent {
        const startOffset = this.offsetAt(edit.range.start);
        const endOffset = this.offsetAt(edit.range.end);
        this._text = this._text.slice(0, startOffset) + edit.newText + this._text.slice(endOffset);
        return new TextDocumentChangeEvent(
            this,
            [
                new TextDocumentContentChangeEvent(
                    edit.range,
                    startOffset,
                    endOffset - startOffset,
                    edit.newText
                ),
            ],
            undefined
        );
    }

    public getBlobName(pathName: string): string {
        const bytes = MutableTextDocument.textEncoder.encode(this._text);
        return this._blobNameCalculator.calculate(pathName, bytes)!;
    }
}

export class TextDocumentContentChangeEvent {
    constructor(
        public readonly range: Range,
        public readonly rangeOffset: number,
        public readonly rangeLength: number,
        public readonly text: string
    ) {}

    /**
     * The methods below aren't part of the real vscode.TextDocumentContentChangeEvent
     */

    static forInsert(
        document: TextDocument,
        position: Position,
        offset: number,
        text: string
    ): TextDocumentContentChangeEvent {
        const range = new Range(position, position);
        return new TextDocumentContentChangeEvent(range, offset, 0, text);
    }

    static forUpdate(
        document: TextDocument,
        position: Position,
        offset: number,
        count: number,
        text: string
    ): TextDocumentContentChangeEvent {
        const range = new Range(position, position.translate(0, count));
        return new TextDocumentContentChangeEvent(range, offset, count, text);
    }

    static forDelete(
        document: TextDocument,
        position: Position,
        offset: number,
        count: number
    ): TextDocumentContentChangeEvent {
        // We have observed that VSCode's deletion ranges are the length of
        // the deletion.
        const range = new Range(position, position.translate(0, count));
        return new TextDocumentContentChangeEvent(range, offset, count, "");
    }
}

export enum TextDocumentChangeReason {
    /** The text change is caused by an undo operation. */
    Undo = 1,

    /** The text change is caused by an redo operation. */
    Redo = 2,
}

export class TextDocumentChangeEvent {
    constructor(
        public readonly document: TextDocument,
        public readonly contentChanges: readonly TextDocumentContentChangeEvent[],
        public readonly reason: TextDocumentChangeReason | undefined = undefined
    ) {}
}

export function emptyTextDocumentChangeEvent(
    document: TextDocument,
    reason?: TextDocumentChangeReason
) {
    return new TextDocumentChangeEvent(document, [], reason);
}

export class TrackedDisposable implements Disposable {
    private static instanceCount = 0;
    private static disposedCount = 0;
    private static items = new Set<TrackedDisposable>();
    private disposed = false;
    private trace = new Error().stack;

    constructor(private readonly onDispose: () => void) {
        TrackedDisposable.instanceCount++;
        TrackedDisposable.items.add(this);
    }

    dispose(): void {
        if (!this.disposed) {
            this.onDispose();
            this.disposed = true;
            TrackedDisposable.disposedCount++;
            TrackedDisposable.items.delete(this);
        }
    }

    static from(...disposables: Disposable[]): Disposable {
        return new Disposable(() => {
            for (const disposable of disposables) {
                disposable.dispose();
            }
        });
    }

    /* These methods are used for testing only. */

    static getStats(): { constructed: number; disposed: number } {
        return {
            constructed: this.instanceCount,
            disposed: this.disposedCount,
        };
    }

    static printTraces(): void {
        const linesToExclude = ["vscode-mocks.ts", ".pnpm"];
        for (const item of this.items) {
            // eslint-disable-next-line no-console
            console.log(
                item.trace
                    ?.split("\n")
                    .filter((line) => linesToExclude.every((l) => !line.includes(l)))
                    .join("\n")
            );
            // eslint-disable-next-line no-console
            console.log("------------------------------");
        }
    }

    static resetStats(): void {
        this.instanceCount = 0;
        this.disposedCount = 0;
    }

    static assertDisposed(): void {
        if (this.instanceCount !== this.disposedCount) {
            throw new Error(
                `Disposable leak detected: ${this.instanceCount - this.disposedCount} undisposed (constructed=${this.instanceCount}, disposed=${this.disposedCount})`
            );
        }
    }
}

function emptyDisposable(): Disposable {
    return { dispose: () => {} };
}

type EventListener<T> = (e: T) => any;

export class EventEmitter<T> {
    static eventType = "EventEmitter";
    private nextSubscriberId = 123;
    private subscribers!: Map<number, EventListener<T>>;

    constructor() {
        this._reset();
    }

    private subscribe(
        listener: EventListener<T>,
        _thisArgs?: any,
        _disposables?: Disposable[]
    ): Disposable {
        const subscriberId = this.nextSubscriberId;
        this.nextSubscriberId += 1;

        this.subscribers.set(subscriberId, listener);
        return new TrackedDisposable(() => {
            this.unsubscribe(subscriberId);
        });
    }

    private unsubscribe(subscriberId: number) {
        this.subscribers.delete(subscriberId);
    }

    public event = this.subscribe.bind(this);

    public fire(data: T) {
        for (const subscriber of this.subscribers.values()) {
            subscriber(data);
        }
    }

    public dispose() {
        this._reset();
    }

    private _reset() {
        this.subscribers = new Map<number, EventListener<T>>();
    }
}

export class MockWorkspaceFolder implements WorkspaceFolder {
    public readonly index = 0;
    public readonly uri: Uri;

    constructor(public readonly name: string) {
        this.uri = Uri.file(name);
    }
}

export class RelativePattern {
    public workspaceFolder: WorkspaceFolder;

    constructor(
        base: string | Uri | WorkspaceFolder,
        public pattern: string
    ) {
        if (Uri.isUri(base)) {
            this.workspaceFolder = new MockWorkspaceFolder(base.path);
        } else if (typeof base === "string") {
            this.workspaceFolder = new MockWorkspaceFolder(base);
        } else {
            this.workspaceFolder = base;
        }
    }

    public get baseUri(): Uri {
        return this.workspaceFolder.uri;
    }

    public get path(): string {
        return this.baseUri.path;
    }
}

export type GlobPattern = string | RelativePattern;

class GlobPatternMatcher {
    private base: string;
    private pattern: string;
    private globPattern: boolean;

    constructor(globPattern: string | RelativePattern) {
        if (globPattern instanceof RelativePattern) {
            this.globPattern = true;
            this.base = globPattern.path;
            this.pattern = globPattern.pattern;
        } else if (typeof globPattern === "string") {
            this.globPattern = false;
            this.base = "";
            this.pattern = globPattern;
        } else {
            throw new Error("globPattern is neither string nor RelativePattern");
        }

        if (this.pattern.includes("*") && this.pattern !== "**/*") {
            throw new Error(
                "Sorry, GlobPatternMatcher only accepts **/* or specific explicit paths"
            );
        }
    }

    public accepts(path: string): boolean {
        if (this.base + "/" + this.pattern === path) {
            return true;
        }
        return path.startsWith(this.base) && this.globPattern;
    }
}

export class FileSystemWatcher implements Disposable, FSSubscriber {
    private matcher: GlobPatternMatcher;

    private notifyOnCreate: boolean;
    private notifyOnChange: boolean;
    private notifyOnDelete: boolean;

    private onDidCreateEmitter = new EventEmitter<Uri>();
    private onDidChangeEmitter = new EventEmitter<Uri>();
    private onDidDeleteEmitter = new EventEmitter<Uri>();

    public onDidCreate = this.onDidCreateEmitter.event;
    public onDidChange = this.onDidChangeEmitter.event;
    public onDidDelete = this.onDidDeleteEmitter.event;

    private _fsWatcher: FSWatcher;

    constructor(
        globPattern: GlobPattern,
        ignoreCreateEvents: boolean = false,
        ignoreChangeEvents: boolean = false,
        ignoreDeleteEvents: boolean = false,
        subscribe: (subscriber: FSSubscriber) => FSWatcher
    ) {
        this.matcher = new GlobPatternMatcher(globPattern);
        this.notifyOnCreate = !ignoreCreateEvents;
        this.notifyOnChange = !ignoreChangeEvents;
        this.notifyOnDelete = !ignoreDeleteEvents;

        this._fsWatcher = subscribe(this);
    }

    public dispose() {
        this._fsWatcher.cancel();
    }

    // FSSubscriber
    public onCreate(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        if (this.notifyOnCreate && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
            this.onDidCreateEmitter.fire(pathOrUri);
        }
    }

    // FSSubscriber
    public onChange(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        if (this.notifyOnChange && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
            this.onDidChangeEmitter.fire(pathOrUri);
        }
    }

    // FSSubscriber
    public onDelete(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        if (this.notifyOnDelete && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
            this.onDidDeleteEmitter.fire(pathOrUri);
        }
    }
}

export class VSCodeFS {
    createFileSystemWatcher(
        globPattern: GlobPattern,
        ignoreCreateEvents?: boolean,
        ignoreChangeEvents?: boolean,
        ignoreDeleteEvents?: boolean
    ): FileSystemWatcher {
        const subscribe = (subscriber: FSSubscriber): FSWatcher => {
            return mockFSUtils.createWatcher(subscriber);
        };
        return new FileSystemWatcher(
            globPattern,
            ignoreCreateEvents,
            ignoreChangeEvents,
            ignoreDeleteEvents,
            subscribe
        );
    }
}

export namespace workspace {
    let fs = new VSCodeFS();
    let configurationChanged = new EventEmitter<ConfigurationChangeEvent>();
    export const textDocumentSaved = new EventEmitter<TextDocument>();
    export const textDocumentChanged = new EventEmitter<TextDocumentChangeEvent>();
    // export const notebookDocumentChanged = new EventEmitter<NotebookDocumentChangeEvent>();
    export const workspaceFoldersChanged = new EventEmitter<WorkspaceFoldersChangeEvent>();
    export const openTextDocument1 = new EventEmitter<TextDocument>();
    export const closeTextDocument = new EventEmitter<TextDocument>();
    export let workspaceFolders: readonly WorkspaceFolder[] = [];
    export let textDocuments: TextDocument[] = [];
    // export let notebookDocuments: readonly NotebookDocument[] = [];
    export let textDocumentLoads = new Map<string, Promise<TextDocument>>();
    export let handleMissingTextDocument: ((uri: Uri) => Promise<TextDocument>) | undefined =
        undefined;
    export let onDidChangeConfiguration = configurationChanged.event;
    export const getConfiguration = jest.fn((key) => {
        if (key === "augment") {
            return generateMockWorkspaceConfig(getExampleUserConfig());
        }
        return generateMockWorkspaceConfig();
    });

    export const onWillSaveTextDocument = jest.fn().mockReturnValue(emptyDisposable());
    export const onWillRenameFiles = jest.fn().mockReturnValue(emptyDisposable());
    export const onDidSaveTextDocument = textDocumentSaved.event;
    export const onDidChangeTextDocument = textDocumentChanged.event;
    // export const onDidChangeNotebookDocument = notebookDocumentChanged.event;
    export const onDidChangeTextEditorSelection = jest.fn().mockReturnValue(emptyDisposable());
    export const onDidChangeWorkspaceFolders = workspaceFoldersChanged.event;
    export const onDidCloseNotebookDocument = jest.fn().mockReturnValue(emptyDisposable());
    export const registerTextDocumentContentProvider = jest.fn().mockReturnValue(emptyDisposable());

    export const onDidOpenTextDocument = openTextDocument1.event;
    export const onDidCloseTextDocument = closeTextDocument.event;

    let workspaceIndex = 0;
    export function getWorkspaceFolder(uri: Uri): WorkspaceFolder {
        return {
            uri: uri,
            name: uri.path,
            index: workspaceIndex++,
        };
    }

    export function openTextDocument(uri: Uri | string): Thenable<TextDocument> {
        if (typeof uri === "string") {
            uri = Uri.file(uri);
        }
        // Document already opened
        for (const document of textDocuments) {
            if (document.uri.toString() === uri.toString()) {
                return Promise.resolve(document);
            }
        }

        // Test-injected async document opens
        let future = textDocumentLoads.get(uri.toString());
        if (future !== undefined) {
            return future;
        }
        if (handleMissingTextDocument !== undefined) {
            let uriKey = uri.toString();
            let future = handleMissingTextDocument(uri).then(
                (document) => {
                    textDocumentLoads.delete(uriKey);
                    addTextDocument(document);
                    publishOpenTextDocument(document);
                    return document;
                },
                (err) => {
                    textDocumentLoads.delete(uriKey);
                    throw err;
                }
            );
            textDocumentLoads.set(uriKey, future);
            return future;
        }

        // Unknown resource
        throw new Error(`No document found for ${uri.toString()}`);
    }

    export function createFileSystemWatcher(
        globPattern: GlobPattern,
        ignoreCreateEvents?: boolean,
        ignoreChangeEvents?: boolean,
        ignoreDeleteEvents?: boolean
    ): FileSystemWatcher {
        return fs.createFileSystemWatcher(
            globPattern,
            ignoreCreateEvents,
            ignoreChangeEvents,
            ignoreDeleteEvents
        );
    }

    /**
     * Not part of vscode API
     */

    export function reset() {
        configurationChanged = new EventEmitter<ConfigurationChangeEvent>();
        onDidChangeConfiguration = configurationChanged.event;
        fs = new VSCodeFS();
        workspaceFolders = [];
        textDocuments = [];
        textDocumentLoads.clear();
        // notebookDocuments = [];
    }

    export function mockConfigurationChange(newConfig?: WorkspaceConfiguration) {
        if (newConfig) {
            workspace.getConfiguration.mockReturnValueOnce(newConfig);
        }
        configurationChanged.fire({ affectsConfiguration: jest.fn() });
    }

    export function setWorkspaceFolders(newWorkspaceFolders: Array<WorkspaceFolder>) {
        const added = new Array<WorkspaceFolder>();
        const removed = new Array<WorkspaceFolder>();
        for (const newFolder of newWorkspaceFolders) {
            let idx: number;
            for (idx = 0; idx < workspace.workspaceFolders.length; idx++) {
                if (newFolder.uri.path === workspace.workspaceFolders[idx].uri.path) {
                    break;
                }
            }
            if (idx === workspace.workspaceFolders.length) {
                added.push(newFolder);
            }
        }
        for (const folder of workspace.workspaceFolders) {
            let idx: number;
            for (idx = 0; idx < newWorkspaceFolders.length; idx++) {
                if (folder.uri.path === newWorkspaceFolders[idx].uri.path) {
                    break;
                }
            }
            if (idx === newWorkspaceFolders.length) {
                removed.push(folder);
            }
        }

        workspace.workspaceFolders = newWorkspaceFolders;
        workspaceFoldersChanged.fire({ added, removed });
    }
}

export function resetMockWorkspace() {
    workspace.reset();
}

export function getWorkspaceFolder(uri: Uri): WorkspaceFolder {
    return workspace.getWorkspaceFolder(uri);
}

export function setWorkspaceFolders(folderRoots: readonly string[]) {
    const newWorkspaceFolders = folderRoots.map((folderRoot) =>
        getWorkspaceFolder(Uri.file(folderRoot))
    );
    workspace.setWorkspaceFolders(newWorkspaceFolders);
}

export function publishWorkspaceFoldersChange(newWorkspaceFolders: WorkspaceFolder[]) {
    workspace.setWorkspaceFolders(newWorkspaceFolders);
}

export function mockWorkspaceConfigChange(newConfig: WorkspaceConfiguration) {
    workspace.mockConfigurationChange(newConfig);
}

export function publishTextDocumentSaved(document: TextDocument) {
    workspace.textDocumentSaved.fire(document);
}

export function publishTextDocumentChange(event: TextDocumentChangeEvent) {
    workspace.textDocumentChanged.fire(event);
}

export function publishOpenTextDocument(document: TextDocument) {
    workspace.openTextDocument1.fire(document);
}

export function publishCloseTextDocument(document: TextDocument) {
    workspace.closeTextDocument.fire(document);
}

export function addTextDocument(document: TextDocument) {
    workspace.textDocuments.push(document);
}

export function setOpenTextDocuments(documents: TextDocument[]) {
    workspace.textDocuments = documents;
}

export function setHandleMissingTextDocument(handler: (uri: Uri) => Promise<TextDocument>) {
    workspace.handleMissingTextDocument = handler;
}

// This class has the same interfaces as vscode.ExtensionContext.globalState
// and is used to mock the global state storage.
// Uses singleton storage to reproduce the storage behavior.
export class Memento {
    // Pretend we have a Memento storage that really isn't persistent
    private static _values: Map<string, any> = new Map();
    public keys(): string[] {
        return Array.from(Memento._values.keys());
    }

    public setKeysForSync(_keys: readonly string[]): void {
        // Do nothing
    }

    public get<T>(key: string): T | undefined {
        return Memento._values.get(key);
    }

    public update(key: string, value: any): Thenable<void> {
        return new Promise<void>((resolve, reject) => {
            try {
                Memento._values.set(key, value);
                resolve();
            } catch (e) {
                reject(e);
            }
        });
    }

    public static resetValues(): void {
        Memento._values.clear();
    }
}

// This class has the same interfaces as vscode.ExtensionContext.SecretStorage
// and is used to mock the global secret storage.
// Uses singleton storage to reproduce the storage behavior.
export class SecretStorage {
    private static _values: Map<string, string> = new Map();
    private _onDidChange = new EventEmitter<SecretStorageChangeEvent>();

    get(key: string): Thenable<string | undefined> {
        return Promise.resolve(SecretStorage._values.get(key));
    }

    store(key: string, value: string): Thenable<void> {
        SecretStorage._values.set(key, value);
        this._onDidChange.fire({ key });
        return Promise.resolve();
    }

    delete(key: string): Thenable<void> {
        SecretStorage._values.delete(key);
        this._onDidChange.fire({ key });
        return Promise.resolve();
    }

    get onDidChange(): Event<SecretStorageChangeEvent> {
        return this._onDidChange.event;
    }

    public static resetValues(): void {
        SecretStorage._values.clear();
    }
}

export class ExtensionContext {
    readonly subscriptions: { dispose(): any }[] = [];
    readonly extensionUri: Uri = Uri.file("/no/extension/uri");

    constructor(
        readonly workspaceState: Memento = new Memento(),
        readonly globalState: Memento = new Memento(),
        readonly secrets: SecretStorage = new SecretStorage()
    ) {}

    get extensionPath() {
        return this.extensionUri.fsPath;
    }

    get environmentVariableCollection(): any {
        throw new Error("not implemented");
    }

    asAbsolutePath(_relPath: string): string {
        throw new Error("not implemented");
    }

    readonly storageUri: Uri = Uri.file("/no/storage/uri");

    get storagePath(): string {
        return this.storageUri.fsPath;
    }

    readonly globalStorageUri: Uri = Uri.file("/no/global/storage/uri");

    get globalStoragePath(): string {
        return this.globalStorageUri.fsPath;
    }

    readonly logUri: Uri = Uri.file("/no/log/uri");

    get logPath(): string {
        return this.logUri.fsPath;
    }
}

export class TextEdit {
    newEol?: EndOfLine;

    constructor(
        public range: Range,
        public newText: string
    ) {}
}

export function openTextDocument(uri: Uri): MutableTextDocument {
    const content = mockFSUtils.readFileUtf8(uri.fsPath);
    return new MutableTextDocument(uri, content);
}
