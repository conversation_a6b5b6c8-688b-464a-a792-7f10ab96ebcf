import { Disposable, EventEmitter, Uri } from "../vscode";
import { mockFSUtils } from "./fs-utils";
import { MockFileSystem } from "./mock-filesystem";

interface FSSubscriber extends MockFileSystem.FSSubscriber {}
interface FSWatcher extends MockFileSystem.FSWatcher {}

// File system watcher for the mock filesystem

// NOTE(mpauly): Since our version of the filewatcher doesn't accept a glob
// pattern (this functionality is also not used in vscode), comment out
// related code.
class FileSystemWatcher implements Disposable, FSSubscriber {
    // private matcher: GlobPatternMatcher;

    private notifyOnCreate: boolean;
    private notifyOnChange: boolean;
    private notifyOnDelete: boolean;

    private onDidCreateEmitter = new EventEmitter<Uri>();
    private onDidChangeEmitter = new EventEmitter<Uri>();
    private onDidDeleteEmitter = new EventEmitter<Uri>();

    public onDidCreate = this.onDidCreateEmitter.event;
    public onDidChange = this.onDidChangeEmitter.event;
    public onDidDelete = this.onDidDeleteEmitter.event;

    private _fsWatcher: FSWatcher;

    constructor(
        // globPattern: GlobPattern,
        ignoreCreateEvents: boolean = false,
        ignoreChangeEvents: boolean = false,
        ignoreDeleteEvents: boolean = false,
        subscribe: (subscriber: FSSubscriber) => FSWatcher
    ) {
        // this.matcher = new GlobPatternMatcher(globPattern);
        this.notifyOnCreate = !ignoreCreateEvents;
        this.notifyOnChange = !ignoreChangeEvents;
        this.notifyOnDelete = !ignoreDeleteEvents;

        this._fsWatcher = subscribe(this);
    }

    public dispose() {
        this._fsWatcher.cancel();
    }

    // FSSubscriber
    public onCreate(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        // if (this.notifyOnCreate && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
        if (this.notifyOnCreate) {
            this.onDidCreateEmitter.fire(pathOrUri);
        }
    }

    // FSSubscriber
    public onChange(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        // if (this.notifyOnChange && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
        if (this.notifyOnChange) {
            this.onDidChangeEmitter.fire(pathOrUri);
        }
    }

    // FSSubscriber
    public onDelete(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        // if (this.notifyOnDelete && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
        if (this.notifyOnDelete) {
            this.onDidDeleteEmitter.fire(pathOrUri);
        }
    }
}

// Mock the factory function in fs-watcher.ts
// Since we are mocking the filesystem, we can ignore the passed workspaceFolder
export function createFileSystemWatcher(_: string): FileSystemWatcher {
    const subscribe = (subscriber: FSSubscriber): FSWatcher => {
        return mockFSUtils.createWatcher(subscriber);
    };
    return new FileSystemWatcher(false, false, false, subscribe);
}
