// TOD<PERSON> pulled from another file
// import { APIStatus } from "./utils/types";
// These values are based on the gRPC statuscodes
// https://grpc.github.io/grpc/core/md_doc_statuscodes.html
export enum APIStatus {
    ok,
    cancelled,
    unknown,
    unavailable,
    unimplemented,
    invalidArgument,
    resourceExhausted,
    unauthenticated,
    permissionDenied,
    deadlineExceeded,
    // Non-gRPC status codes are prefixed with augment
    augmentTooLarge,
}

/**
 * ExtensionDisabled in an Error that reports the disabling of the Augment extension.
 */
export class ExtensionDisabled extends Error {
    constructor() {
        super("Augment extension has been disabled");
    }
}

/**
 * ShutdownError is an Error that reports that an operation was interrupted because
 * a component is shutting down.
 */
export class ShutdownError extends Error {
    constructor(component: string) {
        super(`${component} is shutting down`);
    }
}

/**
 * UnknownModelError in an Error that reports the configured model is not available
 */
export class UnknownModelError extends Error {
    constructor(public modelName: string) {
        super(`Configured model "${modelName}" is not available`);
    }
}

/**
 * UnknownModelError in an Error that reports the configured model is not available
 */
export class NoDefaultModelError extends UnknownModelError {
    constructor() {
        super("<default>");
    }
}

/**
 * NoModelsError in an Error that reports the configured model is not available
 */
export class NoModelsError extends Error {
    constructor() {
        super("No models available");
    }
}

/**
 * SkipCompletion is thrown when we intend to not provide a completion.
 */
export class SkipCompletion extends Error {
    constructor(message: string = "Skipping inline completion.") {
        super(message);
    }
}

export interface ErrorDetails {
    code: number;
    message: string;
    detail: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    help_uri?: string;
}

/**
 * APIError is an Error that results from a server error.
 */
export class APIError extends Error {
    constructor(
        public status: APIStatus,
        msg: string,
        public errorDetails?: ErrorDetails,
        cause?: unknown
    ) {
        super(msg, { cause });
    }

    static transientIssue(err: Error | string): APIError {
        if (err instanceof Error) {
            return new APIError(APIStatus.unavailable, err.message, undefined, err);
        }
        return new APIError(APIStatus.unavailable, err);
    }

    static async fromResponse(resp: Response): Promise<APIError> {
        const status = statusFromHTTPCode(resp.status);
        const baseMsg = `HTTP error: ${resp.status} ${resp.statusText}`;

        // Try to extract error details from the response body
        try {
            const contentType = resp.headers.get("content-type");
            if (contentType && contentType.includes("application/json")) {
                const clonedResp = resp.clone();
                // eslint-disable-next-line @typescript-eslint/naming-convention
                const body = (await clonedResp.json()) as { error_details?: ErrorDetails };

                if (body && body.error_details) {
                    return new APIError(status, baseMsg, body.error_details);
                }
            }
        } catch (e) {
            // If we can't parse the response body, just return the basic error
            // Using a more TypeScript-friendly logging approach
            if (e instanceof Error) {
                // eslint-disable-next-line no-console
                console.warn(`Failed to parse error details from response: ${e.message}`);
            }
        }

        return new APIError(status, baseMsg);
    }

    static isAPIErrorWithStatus(e: unknown, s: APIStatus): boolean {
        if (!(e instanceof APIError)) {
            return false;
        }
        return e.status === s;
    }

    static isRetriableAPIError(e: unknown): boolean {
        if (!(e instanceof APIError)) {
            return false;
        }
        return retriableStatuses.has(e.status);
    }
}

// The following code is based on gRPC mappings of HTTP codes to gRPC statuses.
// https://github.com/grpc/grpc/blob/master/doc/http-grpc-status-mapping.md
function statusFromHTTPCode(code: number): APIStatus {
    switch (code) {
        case 200:
            return APIStatus.ok;
        case 400:
            return APIStatus.invalidArgument;
        case 401:
            return APIStatus.unauthenticated;
        case 403:
            return APIStatus.permissionDenied;
        case 404:
            return APIStatus.unimplemented;
        case 413:
            return APIStatus.augmentTooLarge;
        case 429:
            return APIStatus.resourceExhausted;
        case 499:
            return APIStatus.cancelled;
        case 504:
            return APIStatus.deadlineExceeded;
    }
    if (code >= 500 && code < 600) {
        return APIStatus.unavailable;
    }
    return APIStatus.unknown;
}

const retriableStatuses = new Set<APIStatus>([APIStatus.unavailable, APIStatus.cancelled]);

function getCause(e: Error): string {
    if (e.cause instanceof String) {
        return String(e.cause);
    }
    if (e.cause instanceof Object) {
        return JSON.stringify(e.cause);
    }
    return "";
}

export function getErrmsg(e: unknown, includeCause: boolean = false): string {
    if (e instanceof Error) {
        if (includeCause) {
            const cause = getCause(e);
            if (cause !== "") {
                return `${e.message} (due to ${cause})`;
            }
        }
        return e.message;
    }
    return String(e);
}
