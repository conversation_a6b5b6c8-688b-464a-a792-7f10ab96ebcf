/**
 * Open Document cache
 *
 * We maintain a simple open document cache to track the state of documents
 * that are open in the editor. This is used to provide completions and chat
 * context. There is some overlap with the OpenFileManager, which tracks open
 * documents for the purpose of uploading them to the server. In contrast,
 * the cache is only for providing completion and chat context without concern
 * for uploading. In the future, it may be possible to merge the two.
 */
import { DidChangeTextDocumentParams } from "vscode-languageserver-protocol";
import { TextDocument } from "vscode-languageserver-textdocument";

export class LspDocumentTracker {
    private documents: Map<string, TextDocument> = new Map();

    public add(document: TextDocument): void {
        this.documents.set(document.uri.toString(), document);
    }

    public remove(uri: string): void {
        this.documents.delete(uri);
    }

    public get(uri: string): TextDocument | undefined {
        return this.documents.get(uri);
    }

    public has(uri: string): boolean {
        return this.documents.has(uri);
    }

    public clear(): void {
        this.documents.clear();
    }

    public getAllDocuments(): TextDocument[] {
        return Array.from(this.documents.values());
    }

    public getDocumentVersion(uri: string): number | undefined {
        return this.documents.get(uri)?.version;
    }

    public updateDocument(uri: string, changes: DidChangeTextDocumentParams): void {
        const document = this.documents.get(uri);
        if (!document) {
            return;
        }
        const doc = TextDocument.update(
            document,
            changes.contentChanges,
            changes.textDocument.version
        );
        this.documents.set(uri, doc);
    }
}
