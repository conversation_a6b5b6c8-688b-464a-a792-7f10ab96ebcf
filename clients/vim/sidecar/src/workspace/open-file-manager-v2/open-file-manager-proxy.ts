// using a proxy to replace versions to new implementation internally
import { difference } from "lodash";

import { AugmentConfigListener } from "../../augment-config-listener";
import { getLogger } from "../../logging";
import * as vscode from "../../vscode";
import { DocumentType } from "../document-type";
import { OpenFileManager, RecencySummary } from "../open-file-manager";
import * as workspaceEvents from "../workspace-events";
import { IOpenFileManagerV2 } from "./open-file-manager-v2-types";

/**
 * Temporary wrapper for migration to new open file manager
 */
export class OpenFileManagerProxy {
    private readonly _logger = getLogger("OpenFileManagerProxy");
    constructor(
        private readonly _configListener: AugmentConfigListener,

        private readonly _openFileManagerV1: OpenFileManager,
        private readonly _openFileManagerV2?: IOpenFileManagerV2 | undefined
    ) {}

    get isV2Enabled(): boolean {
        return false;
        // return this._configListener.config.openFileManager.v2Enabled;
    }

    startTrackingFolder(workspaceName: string, folderId: number): vscode.Disposable[] {
        if (this.isV2Enabled) {
            return [
                this._openFileManagerV2!.startTrackingFolder(workspaceName, folderId),
                this._openFileManagerV1.openSourceFolder(folderId),
            ];
        } else {
            return [this._openFileManagerV1.openSourceFolder(folderId)];
        }
    }

    addOpenedDocument(event: workspaceEvents.FolderTextDocumentOpenedEvent): void {
        this._openFileManagerV1.startTracking(event.folderId, event.relPath, event.document);
        if (this.isV2Enabled) {
            this._openFileManagerV2!.addOpenedDocument(event);
        }
    }

    getBlobName(folderId: number, pathName: string): string | undefined {
        if (this.isV2Enabled) {
            const newResult = this._openFileManagerV2!.getBlobName(folderId, pathName);
            const oldResult = this._openFileManagerV1.getBlobName(folderId, pathName);

            // we expect the implementations to return different values
            // but they should both return either a value or undefined
            if (
                (newResult === undefined && oldResult !== undefined) ||
                (newResult !== undefined && oldResult === undefined)
            ) {
                this._logger.debug(
                    `[WARN] getBlobName returned different results between v1 and v2 [${folderId}:${pathName}]\n[${JSON.stringify(newResult)}]\n[${JSON.stringify(oldResult)}]`
                );
            }
            return oldResult; // old implementation is source of truth for now.
        } else {
            return this._openFileManagerV1.getBlobName(folderId, pathName);
        }
    }

    handleMissingBlob(folderId: number, pathName: string, blobName: string): boolean {
        if (this.isV2Enabled) {
            const newResult = this._openFileManagerV2!.handleMissingBlob(
                folderId,
                pathName,
                blobName
            );
            const oldResult = this._openFileManagerV1.notifyMissingBlob(
                folderId,
                pathName,
                blobName
            );
            return oldResult || newResult; // we expect the different implementation to produce different blobs and values.
        } else {
            return this._openFileManagerV1.notifyMissingBlob(folderId, pathName, blobName);
        }
    }

    loseFocus(): void {
        this._openFileManagerV1.loseFocus();
    }

    stopTracking(folderId: number, pathName: string): void {
        this._openFileManagerV1.stopTracking(folderId, pathName);
        if (this.isV2Enabled) {
            this._openFileManagerV2!.stopTracking(folderId, pathName);
        }
    }

    handleClosedDocument(event: workspaceEvents.FolderTextDocumentClosedEvent): void {
        const isNotebook = false;
        this._openFileManagerV1.stopTracking(
            event.folderId,
            event.relPath,
            isNotebook ? DocumentType.notebook : DocumentType.text
        );
        if (this.isV2Enabled) {
            this._openFileManagerV2!.handleClosedDocument(event);
        }
    }

    handleChangedDocument(event: workspaceEvents.FolderTextDocumentChangedEvent): void {
        this._openFileManagerV1.applyTextDocumentChange(event.folderId, event.relPath, event.event);
        if (this.isV2Enabled) {
            this._openFileManagerV2!.handleChangedDocument(event);
        }
    }

    isTracked(folderId: number, pathName: string): boolean {
        if (this.isV2Enabled) {
            const newResult = this._openFileManagerV2!.isTracked(folderId, pathName);
            const oldResult = this._openFileManagerV1.isTracked(folderId, pathName);
            if (newResult !== oldResult) {
                this._logger.debug(
                    `[WARN] isTracked returned different results between v1 and v2 [${folderId}:${pathName}]\n[${JSON.stringify(newResult)}]\n[${JSON.stringify(oldResult)}]`
                );
            }
            return oldResult; // old implementation is source of truth for now.
        } else {
            return this._openFileManagerV1.isTracked(folderId, pathName);
        }
    }

    getTrackedPaths(folderId: number): Array<string> {
        if (this.isV2Enabled) {
            const newResult = this._openFileManagerV2!.getTrackedPaths(folderId);
            const oldResult = this._openFileManagerV1.getTrackedPaths(folderId);

            // in new but not in old
            const diff = difference(newResult, oldResult);
            if (diff.length > 0) {
                this._logger.debug(
                    `[WARN] getTrackedPaths in new but not in old [${folderId}]\n[${JSON.stringify(diff)}]`
                );
            }

            // in old but not in new
            const diff2 = difference(oldResult, newResult);
            if (diff2.length > 0) {
                this._logger.debug(
                    `[WARN] getTrackedPaths in old but not in new [${folderId}]\n[${JSON.stringify(diff2)}]`
                );
            }
            return oldResult; // old implementation is source of truth for now.
        } else {
            return this._openFileManagerV1.getTrackedPaths(folderId);
        }
    }

    dispose(): void {
        this._openFileManagerV2?.dispose();
        this._openFileManagerV1.dispose();
    }

    getRecencySummary(chunkSize: number): RecencySummary {
        return this._openFileManagerV1.getRecencySummary(chunkSize);
    }

    translateRange(
        folderId: number,
        relPath: string,
        beginOffset: number,
        endOffset: number
    ): { blobName: string; beginOffset: number; endOffset: number } | undefined {
        if (this.isV2Enabled) {
            const newResult = this._openFileManagerV2!.translateRange(
                folderId,
                relPath,
                beginOffset,
                endOffset
            );
            const oldResult = this._openFileManagerV1.translateRange({
                folderId,
                relPath: relPath,
                beginOffset,
                endOffset,
            });

            if (
                newResult?.blobName !== oldResult?.blobName ||
                newResult?.beginOffset !== oldResult?.beginOffset ||
                newResult?.endOffset !== oldResult?.endOffset
            ) {
                this._logger.debug(
                    `[WARN] translateRange returned different results between v1 and v2 [${folderId}:${relPath}]\n[${JSON.stringify(newResult)}]\n[${JSON.stringify(oldResult)}]`
                );
            }
            return oldResult; // old implementation is source of truth for now.
        } else {
            return this._openFileManagerV1.translateRange({
                folderId,
                relPath: relPath,
                beginOffset,
                endOffset,
            });
        }
    }
}
