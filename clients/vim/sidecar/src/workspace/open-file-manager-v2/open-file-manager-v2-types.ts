import * as vscode from "../../vscode";
import * as workspaceEvents from "../workspace-events";

export interface IOpenFileManagerV2 extends vscode.Disposable {
    /**
     *
     * @description Called when Augment starts to track a new folder.
     * Allows to create any resources that are needed for the folder.
     *
     * @returns Disposable that will be called when the resources should be disposed.
     *
     */
    startTrackingFolder(workspaceName: string, folderId: number): vscode.Disposable;

    /**
     *

     */
    addOpenedDocument(event: workspaceEvents.FolderTextDocumentOpenedEvent): void;

    /**
     * @description Open File Manager keeps the latest known blob name for a path while it is open.
     * If document is not open, it will return undefined.
     * In this case the DiskFileManager should be used.
     *
     */
    getBlobName(folderId: number, pathName: string): string | undefined;

    isTracked(folderId: number, pathName: string): boolean;

    getTrackedPaths(folderId: number): Array<string>;

    /**
     *
     * @description Called when the server reports that a blob is missing.
     * If the blob is tracked by the OpenFileManager, it will upload the blob again.
     */
    handleMissingBlob(folderId: number, pathName: string, blobName: string): boolean;

    handleChangedDocument(event: workspaceEvents.FolderTextDocumentChangedEvent): void;

    handleClosedDocument(event: workspaceEvents.FolderTextDocumentClosedEvent): void;

    /**
     *
     * @description Used when Augment stops tracking a file without the document being closed.
     * For example, the file was added to .gitignore and the user refreshed the source folder.
     */
    stopTracking(folderId: number, pathName: string): void;

    /**
     * @description Translates a range in the document to a range in the indexed blob.
     * Effectively changes the range as if file edits after indexing never occurred.
     * If the document is not tracked, it will return the same ranges.
     *
     * NOTE (01/09/2025): I (moogi) think we no longer need this function, but keeping for backward compatibility.
     * We should revisit this at some point.
     *
     *
     */
    translateRange(
        folderId: number,
        pathName: string,
        beginOffset: number,
        endOffset: number
    ): { blobName: string; beginOffset: number; endOffset: number } | undefined;
}
