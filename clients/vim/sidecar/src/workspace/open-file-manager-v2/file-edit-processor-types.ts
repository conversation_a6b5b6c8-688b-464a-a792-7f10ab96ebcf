import * as workspaceEvents from "../../workspace/workspace-events";

// TODO: pull FileEditWatcher out of "next-edit" and merge the two if possible.
// TODO: support Notebook events.

export interface IFileEditProcessor {
    handleDocumentOpened(event: workspaceEvents.FolderTextDocumentOpenedEvent): void;
    handleDocumentClosed(event: workspaceEvents.FolderTextDocumentClosedEvent): void;
    handleFileWillRename(event: workspaceEvents.FolderFileWillRenameEvent): void;
    handleFileDeletion(event: workspaceEvents.FolderFileDeletedEvent): void;

    /**
     *
     * @param event
     * @param protectedBlobName - blob name we should not merge with.
     * @returns the change of size made to the history of events.
     * If we processed an event that added 10 character, this will return 10.
     * If we processed an event that removed 10 characters, this will return 10.
     * Removed character are also counted.
     *
     */
    handleDocumentChange(
        event: workspaceEvents.FolderTextDocumentChangedEvent,
        protectedBlobName?: string
    ): number;

    // getEvents(): FileEditEvent[];
}
