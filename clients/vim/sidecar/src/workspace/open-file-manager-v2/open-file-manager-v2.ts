import { getLogger } from "../../logging";
import * as vscode from "../../vscode";
import * as workspaceEvents from "../workspace-events";
import { BlobStatusStore } from "./blob-status-store";
import { IOpenFileManagerV2 } from "./open-file-manager-v2-types";

class FolderResources implements vscode.Disposable {
    constructor(
        public blobStatusStore: BlobStatusStore,
        public workspaceName: string,
        public folderId: number
    ) {}

    dispose(): void {
        this.blobStatusStore.clear();
    }
}

enum UploadReason {
    newFile = "new file",
}

export class OpenFileManagerV2 implements IOpenFileManagerV2 {
    private _folderResources = new Map<number, FolderResources>(); // TODO: use in memory store
    private _logger = getLogger("OpenFileManagerV2");

    startTrackingFolder(workspaceName: string, folderId: number): vscode.Disposable {
        const resources = new FolderResources(new BlobStatusStore(), workspaceName, folderId);
        this._folderResources.set(folderId, resources);
        return resources;
    }

    // TODO: Complete
    _upload(
        folderId: number,
        document: vscode.TextDocument,
        relPath: string,
        reason: UploadReason
    ): string | undefined {
        const workspaceName = this._folderResources.get(folderId)?.workspaceName;
        this._logger.info(`[${workspaceName}] Uploading [${relPath}] because [${reason}]`);
        // TODO
        return undefined;
    }

    stopTracking(folderId: number, pathName: string): void {
        this._logger.info(
            `[${this._folderResources.get(folderId)?.workspaceName}] Stopping tracking [${pathName}]`
        );
        this._folderResources.get(folderId)?.blobStatusStore.removePath(pathName);
    }

    // TODO: Complete
    addOpenedDocument(
        event: workspaceEvents.FolderTextDocumentOpenedEvent,
        blobName?: string
    ): void {
        const _workspaceName = this._folderResources.get(event.folderId)?.workspaceName;
        const folder = this._folderResources.get(event.folderId);
        if (folder === undefined) {
            throw new Error(`Source folder [${event.folderId}] is not open`);
        }

        if (!blobName) {
            const uploadedBlobName = this._upload(
                event.folderId,
                event.document,
                event.relPath,
                UploadReason.newFile
            );
            if (!uploadedBlobName) {
                folder.blobStatusStore.embargoPath(event.relPath);
                return;
            }
            folder.blobStatusStore.addUploadedBlob(uploadedBlobName, event.relPath);
            return;
        }

        folder.blobStatusStore.addIndexedBlob(blobName, event.relPath);
    }

    getBlobName(folderId: number, pathName: string): string | undefined {
        return this._folderResources.get(folderId)?.blobStatusStore?.getIndexedBlobName(pathName);
    }

    // TODO: Complete
    handleMissingBlob(folderId: number, pathName: string, blobName: string): boolean {
        const isTrackingBlob =
            this._folderResources.get(folderId)?.blobStatusStore.isTrackingBlob(blobName) !==
            undefined;
        const workspaceName = this._folderResources.get(folderId)?.workspaceName;
        if (!isTrackingBlob) {
            return false;
        }
        this._logger.info(
            `TODO [${workspaceName}] Re-uploading ${blobName} for ${pathName} in ${folderId}`
        );
        return true;
    }

    handleClosedDocument(event: workspaceEvents.FolderTextDocumentClosedEvent): void {
        const workspaceName = this._folderResources.get(event.folderId)?.workspaceName;
        this._logger.info(`[${workspaceName}] Handling closed document ${event.relPath}`);
        this.stopTracking(event.folderId, event.relPath);
    }

    // TODO: Complete
    handleChangedDocument(event: workspaceEvents.FolderTextDocumentChangedEvent): void {
        const workspaceName = this._folderResources.get(event.folderId)?.workspaceName;
        this._logger.info(`[${workspaceName}] Handling changed document`);
    }

    isTracked(folderId: number, pathName: string): boolean {
        return (
            this._folderResources.get(folderId)?.blobStatusStore.isTrackingPath(pathName) ?? false
        );
    }

    getTrackedPaths(folderId: number): Array<string> {
        return this._folderResources.get(folderId)?.blobStatusStore.getTrackedPaths() ?? [];
    }

    dispose(): void {
        this._folderResources.forEach((resources) => resources.dispose());
    }

    // TODO: Complete
    translateRange(
        _folderId: number,
        _pathName: string,
        _beginOffset: number,
        _endOffset: number
    ): { blobName: string; beginOffset: number; endOffset: number } | undefined {
        return undefined;
    }
}
