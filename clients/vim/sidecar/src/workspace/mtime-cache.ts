/* eslint-disable @typescript-eslint/no-unsafe-argument */

/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { getErrmsg } from "../exceptions";
import { type AugmentLogger, getLogger } from "../logging";
import { fileExists, makeDirs, readFileUtf8, rename, writeFileUtf8 } from "../utils/fs-utils";
import { joinPath } from "../utils/path-utils";
import { blobNamingVersion } from "./blob-name-calculator";

export type MtimeCacheEntry = {
    mtime: number;
    name: string;
};

class MtimeCacheBase {
    static readonly cacheFileName = "mtime-cache.json";
    static readonly tmpFileName = "mtime-cache.json.tmp";
}

export interface MtimeCacheWriter {
    // `write` persists the items returned by the given iterator to this._cacheFileUri. The
    // iterator should return results of the form [pathName, mtime, blobName].
    write(iter: Iterable<[string, number, string]>): Promise<void>;
}

class PersistentMtimeCache {
    public readonly entries = new Array<[string, MtimeCacheEntry]>();
    constructor(public readonly namingVersion = blobNamingVersion) {}
}

// toMtimeCacheEntry converts the given object to a MtimeCacheEntry if possible,
// returning undefined otherwise.
function toMtimeCacheEntry(obj: any): MtimeCacheEntry | undefined {
    if (obj.mtime === undefined || typeof obj.mtime !== "number" || !obj.mtime) {
        return undefined;
    }
    if (obj.name === undefined || typeof obj.name !== "string" || !obj.name) {
        return undefined;
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    return { mtime: obj.mtime, name: obj.name };
}

export function makeMtimeCacheFileName(cacheDirName: string): string {
    return joinPath(cacheDirName, MtimeCacheBase.cacheFileName);
}

export function mtimeCacheExists(cacheDirName: string): boolean {
    const cacheFileName = makeMtimeCacheFileName(cacheDirName);
    return fileExists(cacheFileName);
}

export async function migrateMtimeCache(origCacheDir: string, newCacheDir: string): Promise<void> {
    const origCacheFileName = joinPath(origCacheDir, MtimeCacheBase.cacheFileName);
    const newCacheFileName = joinPath(newCacheDir, MtimeCacheBase.cacheFileName);
    await makeDirs(newCacheDir);
    await rename(origCacheFileName, newCacheFileName);
}

export async function readMtimeCache(
    name: string,
    cacheDirName: string
): Promise<Map<string, MtimeCacheEntry>> {
    const entries: Map<string, MtimeCacheEntry> = new Map();
    const logger = getLogger(`MtimeCache[${name}]`);
    const cacheFileName = joinPath(cacheDirName, MtimeCacheBase.cacheFileName);

    logger.info(`reading blob name cache from ${cacheFileName}`);
    try {
        let count = 0;
        const cacheAsString = await readFileUtf8(cacheFileName);
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const jsonObj = JSON.parse(cacheAsString);
        if (jsonObj.namingVersion === undefined || jsonObj.namingVersion !== blobNamingVersion) {
            logger.info(`blob naming version ${jsonObj.namingVersion} !== ${blobNamingVersion}`);
        } else if (Array.isArray(jsonObj.entries)) {
            for (const [pathName, obj] of jsonObj.entries) {
                const entry = toMtimeCacheEntry(obj);
                if (entry !== undefined) {
                    entries.set(pathName, { mtime: entry.mtime, name: entry.name });
                    count++;
                }
            }
        }
        logger.info(`read ${count} entries from ${cacheFileName}`);
    } catch (e: any) {
        const msg = getErrmsg(e);
        if (e instanceof Error && "code" in e && e.code === "ENOENT") {
            // A missing mtime cache probably just means this is a new source folder. It is not
            // an error.
            logger.info(
                `no blob name cache found at ${cacheFileName} (probably new source folder); ` +
                    `error = ${msg}`
            );
        } else {
            logger.error(`failed to read blob name cache ${cacheFileName}: ${msg}`);
        }
        // Fall through to return whatever we have so far.
    }

    return entries;
}

export class MtimeCacheWriterImpl extends MtimeCacheBase implements MtimeCacheWriter {
    protected readonly _cacheFileName: string;
    protected readonly _tmpFileName: string;
    private readonly _logger: AugmentLogger = getLogger(`MTimeCacheWriter`);

    constructor(
        private readonly _name: string,
        private readonly _cacheDirName: string
    ) {
        super();
        this._cacheFileName = joinPath(this._cacheDirName, MtimeCacheBase.cacheFileName);
        this._tmpFileName = joinPath(this._cacheDirName, MtimeCacheBase.tmpFileName);
    }

    public get cacheFileName(): string {
        return this._cacheFileName;
    }

    // `write` persists the items returned by the given iterator to this._cacheFileUri. The
    // iterator should return results of the form [pathName, mtime, blobName].
    public async write(iter: Generator<[string, number, string]>): Promise<void> {
        this._logger.debug(`persisting to ${this._cacheFileName}`);

        const toPersist = new PersistentMtimeCache();
        for (const [pathName, mtime, blobName] of iter) {
            toPersist.entries.push([pathName, { mtime: mtime, name: blobName }]);
        }

        await makeDirs(this._cacheDirName);
        await writeFileUtf8(this._tmpFileName, JSON.stringify(toPersist, undefined, 4));
        await rename(this._tmpFileName, this._cacheFileName);

        this._logger.debug(
            `persisted ${toPersist.entries.length} entries ` +
                `at naming version ${blobNamingVersion} ` +
                `to ${this._cacheFileName}`
        );
    }
}
