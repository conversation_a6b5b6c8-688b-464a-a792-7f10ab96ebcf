import { APIServer } from "../augment-api";
import { IgnoreSource, IgnoreStackBuilder } from "../utils/ignore-file";
import { listTrackedFiles } from "../utils/list-tracked-files";
import { makePathFilter } from "../utils/path-iterator";
import { joinPath } from "../utils/path-utils";
import * as vscode from "../vscode";
import { ContentType, PathHandler } from "./types";

export type SourceFolderDescription =
    | {
          trackable: true;
          trackableFiles: number;
          uploadedFraction: number;
      }
    | {
          trackable: false;
      };

// SourceFolderDescriber is a class that gathers summary information about the contents of a
// source folder.
export class SourceFolderDescriber {
    static readonly verifyBatchSize = 1000;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _pathHandler: PathHandler,
        private readonly _fileExtensions: Set<string> | undefined,
        private readonly _maxTrackedFiles: number
    ) {}

    // `describe` returns summary information about the contents of the given source folder,
    // including the number of trackable files and the estimated percentage of those files that
    // have already been uploaded. The estimate is computed by choosing a random sample of
    // `verifyBatchSize` files and calling find-missing on them to see what fraction are reported
    // as missing. If the folder has more than `maxTrackedFiles` files, it will be considered
    // untrackable and no estimate will be computed.
    public async describe(
        folderRoot: string,
        repoRoot: string,
        ignoreSources: Array<IgnoreSource>
    ): Promise<SourceFolderDescription> {
        const pathNames = await this._getAllPathNames(folderRoot, repoRoot, ignoreSources);
        if (pathNames.length > this._maxTrackedFiles) {
            return { trackable: false };
        }

        const trackableFiles = pathNames.length;
        const blobsNamesToVerify = await this._chooseBlobNameSample(repoRoot, pathNames);
        if (blobsNamesToVerify.length === 0) {
            return { trackable: true, trackableFiles: 0, uploadedFraction: 1.0 };
        }

        const result = await this._apiServer.findMissing(blobsNamesToVerify);
        const totalBlobNames = blobsNamesToVerify.length;
        const unknownBlobNames = Math.min(result.unknownBlobNames.length, totalBlobNames);
        return {
            trackable: true,
            trackableFiles,
            uploadedFraction: (totalBlobNames - unknownBlobNames) / totalBlobNames,
        };
    }

    // _getAllPathNames returns an array of all path names in the given source folder. If the
    // folder has more than `maxTrackedFiles` files, the returned array will be truncated to
    // a size greater than `maxTrackedFiles`.
    private async _getAllPathNames(
        folderRoot: string,
        repoRoot: string,
        ignoreSources: Array<IgnoreSource>
    ): Promise<Array<string>> {
        const pathFilter = await makePathFilter(
            vscode.Uri.file(folderRoot),
            vscode.Uri.file(repoRoot),
            new IgnoreStackBuilder(ignoreSources),
            this._fileExtensions
        );
        return await listTrackedFiles(
            vscode.Uri.file(folderRoot),
            vscode.Uri.file(repoRoot),
            pathFilter,
            this._maxTrackedFiles + 1
        );
    }

    // _chooseBlobNameSample returns a random sample of verifyBatchSize files from the given
    // source folder. This method is destructive to the pathNames array.
    private async _chooseBlobNameSample(
        repoRoot: string,
        pathNames: Array<string>
    ): Promise<Array<string>> {
        const blobNamesToVerify = new Array<string>();
        while (
            blobNamesToVerify.length < SourceFolderDescriber.verifyBatchSize &&
            pathNames.length > 0
        ) {
            const pathIdx = Math.floor(Math.random() * pathNames.length);
            const relPath = pathNames[pathIdx];
            pathNames[pathIdx] = pathNames[pathNames.length - 1];
            pathNames.pop();

            const absPath = joinPath(repoRoot, relPath);
            const fileContents = await this._pathHandler.readText(absPath);
            if (fileContents.type !== ContentType.text) {
                continue;
            }

            const blobName = this._pathHandler.calculateBlobName(relPath, fileContents.contents);
            blobNamesToVerify.push(blobName!);
        }

        return blobNamesToVerify;
    }
}
