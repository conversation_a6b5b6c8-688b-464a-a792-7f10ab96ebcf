import { FileType } from "../utils/types";

export class InvalidSourceFolderUriError extends E<PERSON>r {
    constructor() {
        super("Invalid source folder uri");
    }
}

export class SourceFolderNotAccessibleError extends Error {
    constructor(reason: string) {
        super(`cannot access source folder: ${reason}`);
    }
}

export class FolderRootNotADirectoryError extends Error {
    constructor() {
        super(`source folder is not a directory`);
    }
}

export class DuplicateSourceFolderError extends Error {
    constructor() {
        super("source folder already exists");
    }
}

export class HomeDirectoryError extends Error {
    constructor() {
        super("source folder is home directory");
    }
}

export class NotAnExternalSourceFolderError extends Error {
    constructor() {
        super("source folder is not an external source folder");
    }
}

export class UnknownSourceFolderError extends Error {
    constructor() {
        super(`source folder does not exist in workspace`);
    }
}

export class UntrackedFolderError extends <PERSON>rror {
    constructor() {
        super(`source folder is not being synced`);
    }
}

export class SourceFolderNotReadyError extends <PERSON><PERSON>r {
    constructor() {
        super(`source folder enumeration is not complete`);
    }
}

// SourceFolderType is the type of a source folder:
// * vscodeWorkspaceFolder: A vscode workspace folder.
// * externalFolder: A folder that is not a vscode workspace folder but is tracked as a source
//   folder.
// * nestedWorkspaceFolder: A folder that is contained within a vscode workspace folder.
// * nestedExternalFolder: A folder that is contained within an external source folder.
export enum SourceFolderType {
    vscodeWorkspaceFolder,
    externalFolder,
    nestedWorkspaceFolder,
    nestedExternalFolder,
    untrackedFolder,
}

// SourceFolderInfoBase contains fields that are common to all source folder types.
// * name: A name of the source folder. For vscode workspace folders, this is the same as
//   workspaceFolder.name. For external folders, it is a name of our own choosing. Note that
//   it is not a path name; it's just a name.
// * folderRoot: The absolute path of the root of the source folder. This may or may not be the
//   same as its repo root.
type SourceFolderInfoBase = {
    name: string;
    folderRoot: string;
    syncingEnabled: boolean;
};

export enum SourceFolderEnumerationState {
    inProgress,
    complete,
}

// TrackedSourceFolderInfo extends SourceFolderInfoBase with fields that are common to all
// tracked source folders (vscode workspace folders and external folders, but not nested folders).
// * type: The type of the source folder.
// * syncingEnabled: True if syncing is enabled for this source folder.
// * trackedFileCount: The number of tracked files in the source folder. Only files are counted, not
//   directories or other items.
// * containsExcludedItems: True if the source folder contains items that are excluded by Augment
//   for any reason.
// * enumerationState: The state of the source folder's enumeration.
export type TrackedSourceFolderInfo = SourceFolderInfoBase & {
    type: SourceFolderType.vscodeWorkspaceFolder | SourceFolderType.externalFolder;
    trackedFileCount: number;
    containsExcludedItems: boolean;
    containsUnindexedItems: boolean;
    enumerationState: SourceFolderEnumerationState;
};

// NestedSourceFolderInfo extends SourceFolderInfoBase with fields that are unique to nested
// folders.
// * containingFolderRoot: The folderRoot of the folder that contains this folder.
export type NestedSourceFolderInfo = SourceFolderInfoBase & {
    type: SourceFolderType.nestedWorkspaceFolder | SourceFolderType.nestedExternalFolder;
    containingFolderRoot: string;
};

export enum UntrackedFolderReason {
    homeDir = "home directory",
    tooLarge = "too large",
    permissionNotGranted = "permission not granted",
}

export type UntrackedFolderInfo = SourceFolderInfoBase & {
    type: SourceFolderType.untrackedFolder;
    reason: UntrackedFolderReason;
};

export type SourceFolderInfo =
    | TrackedSourceFolderInfo
    | NestedSourceFolderInfo
    | UntrackedFolderInfo;

// SourceFolderItem is a type that describes the contents of a directory of a source folder.
// * name: The "simple" name of the item, not its full path name.
// * folderRoot: The folderRoot of the item's source folder.
// * relPath: The relative path of the item from the folderRoot of the item's source folder.
// * type: The type of the item (file, directory, or other).
// * included: True if the item is included by Augment.
// * trackedFileCount: For directory items, the number of tracked files in the directory and its
//   descendants. For non-directory items, this field will be undefined or omitted. Only files are
//   counted, not directories or other items.
// * containsExcludedItems: For directory items, this field contains true if the directory contains
//   excluded items. For non-directory items, this field should be ignored.
// * reason: A string describing why the item is included or excluded.
export type SourceFolderItemBase = {
    name: string;
    folderRoot: string;
    relPath: string;
    type: FileType;
    included: boolean;
    reason: string;
};

export type SourceFolderItemDirectory = SourceFolderItemBase & {
    type: FileType.directory;
    trackedFileCount: number;
    containsExcludedItems: boolean;
    containsUnindexedItems: boolean;
};

export type SourceFolderItemFile = SourceFolderItemBase & {
    type: FileType.file;
    indexed: boolean;
};

export type SourceFolderItemOther = SourceFolderItemBase & {
    type: FileType.other;
};

export type SourceFolderItem =
    | SourceFolderItemFile
    | SourceFolderItemDirectory
    | SourceFolderItemOther;

// FolderSyncingPermission is an enum of the possible states of syncing permission for a given
// source folder. "granted" and "denied" are self-explanatory. "unknown" means that we have no
// information one way or the other about syncing permission for the folder.
export enum FolderSyncingPermission {
    granted = "granted",
    denied = "denied",
    unknown = "unknown",
}
