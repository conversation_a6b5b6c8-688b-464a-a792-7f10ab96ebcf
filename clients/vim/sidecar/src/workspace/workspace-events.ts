import { FileType } from "../utils/types";
import * as vscode from "../vscode";

type FolderTextDocumentChangedEvent = {
    folderId: number;
    relPath: string;
    event: vscode.TextDocumentChangeEvent;
};

type FolderTextDocumentOpenedEvent = {
    folderId: number;
    relPath: string;
    document: vscode.TextDocument;
};

type FolderTextDocumentClosedEvent = {
    folderId: number;
    relPath: string;
    document: vscode.TextDocument;
};

type FolderFileDeletedEvent = {
    folderId: number;
    relPath: string;
};

type FolderFileWillRenameEvent = {
    folderId: number;
    oldRelPath: string;
    newRelPath: string;
    type: FileType;
};

export {
    FolderTextDocumentChangedEvent,
    FolderTextDocumentOpenedEvent,
    FolderTextDocumentClosedEvent,
    FolderFileDeletedEvent,
    FolderFileWillRenameEvent,
};
