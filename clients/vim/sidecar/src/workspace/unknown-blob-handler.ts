import { APIServer, FindMissingResult } from "../augment-api";
import { type AugmentLogger, getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { logArray } from "../utils/logging";
import { retryWithBackoff } from "../utils/promise-utils";
import { KVWorkQueue, PeriodicKicker } from "../utils/work-queue";
import { QualifiedPathName } from "./qualified-path-name";
import { WorkspaceManager } from "./workspace-manager";

type ProbeItem = {
    qualifiedPath: QualifiedPathName;
    startTime: number;
};

export class UnknownBlobHandler extends DisposableService {
    static readonly probeBatchSize = 1000;
    static readonly probeRetryWaitMs = 5 * 1000;
    static readonly probePatienceMs = 2 * 60 * 1000;
    static readonly longRetryWaitMs = 60 * 1000;

    // _toProbe is a workqueue of blobName -> ProbeItem to be probed
    // --> _toProbe: KVWorkQueue<blobName, ProbeItem>
    private readonly _toProbe: KVWorkQueue<string, ProbeItem>;

    // _currentBatch is the batch of blobs to be probed that we are currently building.
    private _currentBatch = new Map<string, ProbeItem>();

    // _retryWaiters and _retryBackoffWaiters are work queues of blobName -> ProbeItem that are
    // waiting to be re-probed. The former is for recently enqueued blobs, while the latter is for
    // blobs that have been reported as non-indexed for a long time (more than probePatienceMs).
    private _probeWaiters: KVWorkQueue<string, ProbeItem>;
    private _probeWaitersKicker: PeriodicKicker;

    private _longWaiters: KVWorkQueue<string, ProbeItem>;
    private _longWaitersKicker: PeriodicKicker;

    private readonly _logger: AugmentLogger;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _workspaceManager: WorkspaceManager
    ) {
        super();

        this._logger = getLogger("UnknownBlobHandler");

        this._toProbe = new KVWorkQueue<string, ProbeItem>(this._probe.bind(this));
        this.addDisposable(this._toProbe);

        this._probeWaiters = new KVWorkQueue<string, ProbeItem>(this._enqueueForProbe.bind(this));
        this.addDisposable(this._probeWaiters);

        this._probeWaitersKicker = new PeriodicKicker(
            this._probeWaiters,
            UnknownBlobHandler.probeRetryWaitMs
        );
        this.addDisposable(this._probeWaitersKicker);

        this._longWaiters = new KVWorkQueue<string, ProbeItem>(this._enqueueForProbe.bind(this));
        this.addDisposable(this._longWaiters);

        this._longWaitersKicker = new PeriodicKicker(
            this._longWaiters,
            UnknownBlobHandler.longRetryWaitMs
        );
        this.addDisposable(this._longWaitersKicker);
    }

    // Enqueue a list of unknown blobs. blobMap is a map from blob name to path name for blobs
    // that came from the OpenFileManager. Blobs that came from the PathMap will not be present
    // in the blobMap and must be looked up in the PathMap.
    public enqueue(unknownBlobs: Array<[string, QualifiedPathName]>): void {
        for (const [blobName, qualifiedPath] of unknownBlobs) {
            this._logger.verbose(`enqueue: ${qualifiedPath.rootPath}:${qualifiedPath.relPath}`);
            this._toProbe.insert(blobName, { qualifiedPath: qualifiedPath, startTime: Date.now() });
        }
        void this._toProbe.kick();
    }

    private _grabCurrentBatch(): Map<string, ProbeItem> | undefined {
        if (this._currentBatch.size === 0) {
            return undefined;
        }
        const batch = this._currentBatch;
        this._currentBatch = new Map<string, ProbeItem>();
        return batch;
    }

    private async _probe(item: [string, ProbeItem] | undefined): Promise<void> {
        if (item !== undefined) {
            const [blobName, probeItem] = item;

            const verifyBlobName = this._workspaceManager.getBlobName(probeItem.qualifiedPath);
            if (verifyBlobName !== blobName) {
                // This blob is no longer in use.
                return;
            }
            this._currentBatch.set(blobName, probeItem);

            if (this._currentBatch.size < UnknownBlobHandler.probeBatchSize) {
                // We don't have a full batch yet. Wait until we do.
                return;
            }
        }

        const batch = this._grabCurrentBatch();
        if (batch === undefined) {
            return;
        }

        const blobNames = [...batch.keys()];

        let result: FindMissingResult | undefined = undefined;
        try {
            result = await retryWithBackoff(
                async () => this._apiServer.findMissing(blobNames),
                this._logger
            );
        } catch {}

        if (result === undefined) {
            for (const [blobName, probeItem] of batch) {
                this._addRetryWaiter(blobName, probeItem);
            }
        } else {
            this._logger.verbose(
                `find-missing reported ${result.nonindexedBlobNames.length} ` +
                    "nonindexed blob names"
            );
            if (result.nonindexedBlobNames.length > 0) {
                logArray(this._logger, "verbose", result.nonindexedBlobNames, 5);
            }

            // We only retry blobs that are reported as non-indexed. If a blob starts being
            // reported as unknown, it will be handled the next time a completion or edit
            // request uses it.
            const unknownBlobNames = new Set(result.unknownBlobNames);
            const nonindexedBlobNames = new Set(result.nonindexedBlobNames);
            for (const [blobName, probeItem] of batch) {
                if (unknownBlobNames.has(blobName)) {
                    this._workspaceManager.notifyBlobMissing(probeItem.qualifiedPath, blobName);
                } else if (nonindexedBlobNames.has(blobName)) {
                    this._addRetryWaiter(blobName, probeItem);
                }
                // else: the blob is now indexed. We can drop it.
            }
        }
    }

    private _enqueueForProbe(item: [string, ProbeItem] | undefined): Promise<void> {
        if (item === undefined) {
            void this._toProbe.kick();
        } else {
            const [blobName, probeItem] = item;
            this._logger.verbose(
                "probe enqueue: " +
                    `${probeItem.qualifiedPath.rootPath}:${probeItem.qualifiedPath.relPath}: ` +
                    `${blobName}`
            );
            this._toProbe.insert(blobName, probeItem);
        }
        return Promise.resolve();
    }

    private _addRetryWaiter(blobName: string, probeItem: ProbeItem): void {
        if (Date.now() - probeItem.startTime < UnknownBlobHandler.probePatienceMs) {
            this._logger.verbose(
                "retry enqueue: " +
                    `${probeItem.qualifiedPath.rootPath}:${probeItem.qualifiedPath.relPath}: ` +
                    `${blobName}`
            );
            this._probeWaiters.insert(blobName, probeItem);
        } else {
            this._logger.verbose(
                "long retry enqueue: " +
                    `${probeItem.qualifiedPath.rootPath}:${probeItem.qualifiedPath.relPath}: ` +
                    `${blobName}`
            );
            this._longWaiters.insert(blobName, probeItem);
        }
    }
}
