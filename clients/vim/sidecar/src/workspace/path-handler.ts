import { isUtf8 } from "node:buffer";

import { FileReader } from "../file-reader";
import { FileType } from "../utils/types";
import { BlobNameCalculator } from "./blob-name-calculator";
import { ContentType, FileContents, PathHandler, PathInfo, PathType } from "./types";

export class PathHandlerImpl implements PathHandler {
    private readonly _blobNameCalculator: BlobNameCalculator;

    constructor(
        maxBlobSize: number,
        private readonly _fileReader: FileReader
    ) {
        this._blobNameCalculator = new BlobNameCalculator(maxBlobSize);
    }

    public get maxBlobSize(): number {
        return this._blobNameCalculator.maxBlobSize;
    }

    public classifyPath(absPath: string): PathInfo {
        const statInfo = this._fileReader.stat(absPath);
        if (!statInfo) {
            return { type: PathType.inaccessible };
        }
        if (statInfo.type !== FileType.file) {
            return { type: PathType.notAFile, mtime: statInfo.mtime };
        }
        if (statInfo.size > this._blobNameCalculator.maxBlobSize) {
            return { type: PathType.largeFile, mtime: statInfo.mtime, size: statInfo.size };
        }
        return { type: PathType.accepted, size: statInfo.size, mtime: statInfo.mtime };
    }

    public async readText(absPath: string): Promise<FileContents> {
        let contents: Uint8Array | undefined;
        try {
            contents = await this._fileReader.read(absPath);
            if (contents === undefined) {
                return { type: ContentType.inaccessible };
            }
        } catch {
            return { type: ContentType.inaccessible };
        }
        if (!isUtf8(contents)) {
            return { type: ContentType.binary };
        }
        if (contents.length > this._blobNameCalculator.maxBlobSize) {
            return { type: ContentType.largeFile, size: contents.length };
        }
        return { type: ContentType.text, contents };
    }

    public calculateBlobName(relPath: string, contents: Uint8Array): string | undefined {
        return this._blobNameCalculator.calculateOrThrow(relPath, contents);
    }
}
