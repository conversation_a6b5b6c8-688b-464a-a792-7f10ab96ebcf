export interface WorkspaceFileChunk {
    charStart: number;
    charEnd: number;
    blobName: string;
    file?: FileDetails;
}

export enum ChatResultNodeType {
    /* eslint-disable @typescript-eslint/naming-convention */
    RAW_RESPONSE = 0,
    SUGGESTED_QUESTIONS = 1,
    MAIN_TEXT_FINISHED = 2,
    /* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatResultNode {
    id: number;
    type: ChatResultNodeType;
    content: string;
}

export interface FileDetails {
    repoRoot: string;
    pathName: string;
    uriScheme?: string;
    // The display range of the file. Expressed with 1-indexing for historical reasons.
    // Only contains the start and end lines, 1-indexed.
    range?: {
        start: number;
        stop: number;
    };
    // These are the full range of the file, expressed with 0-indexing.
    // The full range includes the start and end lines, as well as the
    // start and end character columns.
    fullRange?: {
        startLineNumber: number;
        startColumn: number;
        endLineNumber: number;
        endColumn: number;
    };
    originalCode?: string;
    modifiedCode?: string;
    lineChanges?: LineChanges;
    // Whether actions on this file should be triggered from a different tab.
    differentTab?: boolean;
    requestId?: string;
    suggestionId?: string;
    // The snippet to highlight in the file.
    snippet?: string;
}

export interface LineChanges {
    lineChanges: LineChange[];
    lineOffset: number;
}

export interface LineChange {
    originalStart: number;
    originalEnd: number;
    modifiedStart: number;
    modifiedEnd: number;
}
