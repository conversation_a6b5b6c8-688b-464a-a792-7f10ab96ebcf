import { ChangeType } from "../enums";

type VCSChange = {
    commits: Commit[];
    workingDirectory: WorkingDirectoryChange[];
};

type Commit = {
    hash: string;
    files: CommitChange[];
};
type CommitChange = {
    afterPath?: string;
    beforePath?: string;
    changeType: ChangeType;
    beforeBlobName?: string; // empty if file deleted
    afterBlobName?: string; // empty if file deleted
};

type WorkingDirectoryChange = {
    afterPath?: string;
    beforePath?: string;
    changeType: ChangeType;
    headBlobName?: string; // empty if file added
    indexedBlobName?: string; // empty if file deleted or not indexed yet.
    currentBlobName?: string; // empty if file deleted
};

export {
    type ChangeType,
    type VCSChange,
    type Commit,
    type CommitChange,
    type WorkingDirectoryChange,
};
