export class AugmentCompletion {
    constructor(
        readonly completionText: string,
        readonly suffixReplacementText: string,
        readonly skippedSuffix: string,
        readonly range: { startOffset: number; endOffset: number }
    ) {}

    toString(): string {
        return `text: ${this.completionText}
    suffixReplacementText: ${this.suffixReplacementText}
    skippedSuffix: ${this.skippedSuffix}
    start: ${this.range.startOffset}
    end: ${this.range.endOffset}`;
    }
}
