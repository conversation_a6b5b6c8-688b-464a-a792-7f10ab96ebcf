import * as pathLib from "node:path";

import * as vscode from "../vscode";
import { statFile } from "./fs-utils";
import { FileType } from "./types";

const posixSeparator = pathLib.posix.sep;
const windowsSeparatorRegex = new RegExp("/\\/g");

/**
 * Throughout this extension there are two kinds of file names: Uri's (type
 * `vscode.Uri`), and paths expressed as type `string`.
 *
 * Uris contain absolute pathnames. If a name is to be used to open or otherwise
 * operate on a file, a Uri is used.
 *
 * String path names are always relative, and they are relative to some Uri in
 * the code, for example the Augment root. String paths are never used to open
 * a file, since they are relative and the extension has no concept of a current
 * directory. Instead, they are used as unique names for files. For example, memory
 * object names are based on path names that are relative to the Augment root.
 *
 * Because string path names are relative, they never begin with a separator
 * character. We adopt the convention that string directory path names always
 * end with a separator, for two reasons. First it makes it simple to form the
 * path name of a child of the directory: simply append the rest of the name --
 * no additional separator needed. Second, the gitignore-parser package, with
 * which this module is designed to work, requires that directory names end with
 * a separator, as some gitignore rules are specific to directories.
 *
 * The above rules do lead to one oddity: the canonical form of a string directory
 * name consisting of a single dot or a single separator is the empty string.
 */

/**
 * isAbsolutePathName returns true if the given path is absolute.
 */
export function isAbsolutePathName(pathName: string): boolean {
    return pathLib.isAbsolute(pathName);
}

/**
 * toPosixPath: on windows filesystems returns `pathName` with backslashes converted
 * to forward slashes. On posix filesystems, returns `pathName` unmodified.
 */
function toPosixPath(pathName: string): string {
    if (pathLib.sep === "\\") {
        return pathName.replace(windowsSeparatorRegex, posixSeparator);
    }
    return pathName;
}

/**
 * normalizePathName returns the normalized form of `pathName`. Currently, all it
 * does is convert backslashes to slashes on windows filesystems.
 */
export function normalizePathName(pathName: string): string {
    return toPosixPath(pathName);
}

/**
 * dirNameUri returns the directory name portion of the given Uri as a new Uri.
 * Example:
 *   dirNameUri('file:///walrus/giraffe/spider.py') --> 'file:///walrus/giraffe'
 */
export function dirNameUri(uri: vscode.Uri): vscode.Uri {
    return uri.with({ path: pathLib.dirname(uri.fsPath) });
}

/**
 * baseName returns the base name of the given path.
 */
export function baseName(path: string): string {
    return pathLib.basename(path);
}

/**
 * dirName returns the directory name portion of the given path. If the directory is '.', the
 * empty string is returned. Otherwise, the returned name will always end in a slash.
 *
 * Example:
 *   dirName('walrus/giraffe/spider.py') --> 'walrus/giraffe/'
 *   dirName('spider') --> ''
 *   dirName(./spider' --> ''
 */
export function dirName(path: string): string {
    const dirName = pathLib.dirname(path);
    if (dirName === ".") {
        return "";
    }
    return asDirName(dirName);
}

/**
 * asDirName returns the given path with a trailing slash if it doesn't already have one.
 * Example:
 *   asDirName('walrus/giraffe') --> 'walrus/giraffe/'
 *   asDirName('walrus/giraffe/') --> 'walrus/giraffe/'
 *   asDirName('') --> '/'
 *   asDirName('.') --> './'
 *   asDirName('/') --> '/'
 */
export function asDirName(path: string): string {
    if (!path.endsWith(posixSeparator)) {
        return path + posixSeparator;
    }
    return path;
}

/**
 * pathNameSansSep with any trailing slash(es) (or backslash(es) on windows) removed. The sole
 * exception is "/" (or "\" on windows), which is returned unchanged.
 * Example:
 *   pathNameSansSep('walrus/giraffe/') --> 'walrus/giraffe'
 *   pathNameSansSep('walrus/giraffe//') --> 'walrus/giraffe'
 *   pathNameSansSep('walrus/giraffe') --> 'walrus/giraffe'
 *   pathNameSansSep('') --> ''
 *   pathNameSansSep('/') --> '/'
 *   pathNameSansSep('//') --> '/'
 */
export function pathNameSansSep(path: string): string {
    while (true) {
        if (path === pathLib.sep || path === posixSeparator) {
            return path;
        }
        // Note that we will return here if path is the empty string, as the empty string does
        // not end with a separator.
        if (!path.endsWith(posixSeparator) && !path.endsWith(pathLib.sep)) {
            return path;
        }
        path = path.slice(0, -1);
    }
}

/**
 * relativePathName returns the relative path name from `from` to `to` as
 * an ordinary (non-directory) file name (no separator is appended).
 */
export function relativePathName(from: string | vscode.Uri, to: string | vscode.Uri): string {
    const fromPath = typeof from === "string" ? from : from.fsPath;
    const toPath = typeof to === "string" ? to : to.fsPath;
    return pathLib.relative(fromPath, toPath);
}

/**
 * relativeDirname returns the relative path name from `from` to `to` as
 * a directory name. The resulting name will end with a separator except when
 * `to` and `from` reference the same directory, in which case the empty string
 * is returned.
 */
export function relativeDirName(from: string | vscode.Uri, to: string | vscode.Uri): string {
    let relativePath = relativePathName(from, to);
    if (relativePath === ".") {
        return "";
    }
    if (relativePath.length > 0 && !relativePath.endsWith(pathLib.sep)) {
        return relativePath + pathLib.sep;
    }
    return relativePath;
}

/**
 * joinPath returns the path formed by joining the given directory and file names,
 * formatted according to the given fileType (a trailing separator is added to
 * directory names). If `asDirName` is true then the resulting path is formatted as
 * a directory name (with a trailing '/').
 */
export function joinPath(dirname: string, filename: string, asDirName = false): string {
    let joined = pathLib.join(dirname, filename);
    if (asDirName && !joined.endsWith(pathLib.sep)) {
        joined += pathLib.sep;
    }
    return joined;
}

/**
 * descendentPath returns the path of `pathName` relative to `dirName` if `pathName` is a
 * descendent of `dirName`. Otherwise, it returns undefined.
 *
 * This function only uses string manipulations to determine whether `pathName` is a descendent of
 * `dirName`. It does not access the filesystem. Hence it can be fooled by things like sylinks. It
 * also does not verify that either path name actually exists.
 */
export function descendentPath(dirName: string, pathName: string): string | undefined {
    const relPath = relativePathName(dirName, pathName);
    if (
        relPath === ".." ||
        relPath.startsWith(".." + pathLib.sep) ||
        relPath.startsWith(".." + pathLib.posix.sep)
    ) {
        return undefined;
    }
    return relPath;
}

/**
 * directoryContainsPath indicates whether `pathName` is contained within the directory given by
 * `dirName`. Both `dirName` and `pathName` should either be absolute paths or relative paths that
 * are relative to the same directory.
 *
 * This function only uses string manipulations to determine whether `pathName` is a descendent of
 * `dirName`. It does not access the filesystem. Hence it can be fooled by things like sylinks. It
 * also does not verify that either path name actually exists.
 */
export function directoryContainsPath(dirName: string, pathName: string): boolean {
    return descendentPath(dirName, pathName) !== undefined;
}

/**
 * sameDirectory indicates whether `dirName1` and `dirName2` reference the same directory.
 * Both `dirName1` and `dirName2` should either be absolute paths or relative paths that
 * are relative to the same directory.
 *
 * This function only uses string manipulations to make its determination. It does not access
 * the filesystem. Hence it can be fooled by things like sylinks. It also does not verify that
 * either path name actually exists.
 */
export function sameDirectory(dirName1: string, dirName2: string): boolean {
    const relPath = relativePathName(dirName1, dirName2);
    return (
        relPath === "" ||
        relPath === "." ||
        relPath === "." + pathLib.sep ||
        relPath === "." + pathLib.posix.sep
    );
}

/**
 * firstPathComponent returns the given path name up to the first path separator. If the path has
 * no separators, it is returned unchanged.
 *
 * Example:
 *   firstPathComponent('walrus/giraffe/spider.py') --> 'walrus'
 *   firstPathComponent('walrus') --> 'walrus'
 *   firstPathComponent('walrus/') --> 'walrus'
 *   firstPathComponent('./walrus') --> '.'
 *   firstPathComponent('.') --> '.'
 *   firstPathComponent('/') --> ''
 *   firstPathComponent('') --> ''
 */
export function firstPathComponent(pathName: string, sep = pathLib.sep): string {
    let idx = pathName.indexOf(sep);
    if (sep !== pathLib.posix.sep) {
        const idx2 = pathName.indexOf(pathLib.posix.sep);
        if (idx === -1) {
            idx = idx2;
        } else if (idx2 !== -1) {
            idx = Math.min(idx, idx2);
        }
    }
    if (idx < 0) {
        return pathName;
    }
    return pathName.slice(0, idx);
}

// const pathSepCharClass = pathLib.sep === pathLib.posix.sep ? "/" : "\\/";
// const pathSepRegex = new RegExp(`[${pathSepCharClass}]+`, "g");

/**
 * splitRelPath splits the given relative path into a list of its components. It will throw if
 * passed an absolute path name. Any instances of "./" and any trailing path separators in the path
 * name are discarded.
 *
 * Example:
 *   splitRelPath('walrus/giraffe/spider.py') --> ['walrus', 'giraffe', 'spider.py']
 *   splitRelPath('walrus') --> ['walrus']
 *   splitRelPath('walrus/') --> ['walrus']
 *   splitRelPath('./walrus') --> ['walrus']
 *   splitRelPath('./walrus/') --> ['walrus']
 *   splitRelPath('walrus/giraffe') --> ['walrus', 'giraffe]
 *   splitRelPath('walrus/././giraffe') --> ['walrus', 'giraffe]
 *   splitRelPath('././walrus./././giraffe/./.') --> ['walrus', 'giraffe]
 *   splitRelPath('././walrus./././giraffe/././') --> ['walrus', 'giraffe]
 *   splitRelPath('.') --> ['']
 *   splitRelPath('./') --> ['']
 *   splitRelPath('./.') --> ['']
 *   splitRelPath('./././') --> ['']
 *   splitRelPath('./././.') --> ['']
 *   splitRelPath('') --> []
 */
export function splitRelPath(pathName: string): string[] {
    if (pathLib.isAbsolute(pathName)) {
        throw new Error(`splitPath: ${pathName} must be a relative path`);
    }

    let currPath = pathName;
    const components = new Array<string>();
    for (let count = 0; currPath.length > 0; count++) {
        if (count > 10000) {
            throw new Error(`Too-deep or malformed path name ${pathName}`);
        }
        const basename = pathLib.basename(currPath);
        if (basename !== ".") {
            // Discard "." and "./"
            components.push(basename);
        }
        const dirname = dirName(currPath);
        if (dirname === currPath || dirname === ".") {
            break;
        }
        currPath = dirname;
    }
    return components.reverse();
}

/**
 * findFileName searches for a given file, starting in the given startDir and walking
 * up the directory tree. It returns the Uri of the directory containing the file if
 * it is found, otherwise undefined.
 *
 * If the optional fileType is provided then this function will match only on a
 * directory entry of the specified type. If it finds an entry of a different type,
 * it will continue searching up the tree.
 */
export async function findFileName(
    startDir: string | vscode.Uri,
    fileName: string,
    fileType?: FileType
): Promise<vscode.Uri | undefined> {
    let dir = typeof startDir === "string" ? vscode.Uri.file(startDir) : startDir;
    while (true) {
        try {
            const fileUri = vscode.Utils.joinPath(dir, fileName);
            const stat = await statFile(fileUri.fsPath);
            if (fileType === undefined || stat.type === fileType) {
                return dir;
            }
        } catch {
            // `dir`/`fileName` doesn't exist. Keep looking up the tree.
        }

        // Traverse to `dir`s parent. If it is its own parent then it must be the root
        // of the filesystem, in which case the requested file does not exist.
        const parentPath = pathLib.dirname(dir.fsPath);
        if (pathLib.relative(parentPath, dir.fsPath).length === 0) {
            return undefined;
        }
        dir = dir.with({ path: parentPath });
    }
}
