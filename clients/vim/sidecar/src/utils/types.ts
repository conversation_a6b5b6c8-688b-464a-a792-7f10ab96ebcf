export enum FileType {
    directory = "Directory",
    file = "File",
    other = "Other",
}

export type StatInfo = {
    size: number;
    type: FileType;
    mtime: number;
};

export enum DiagnosticSeverity {
    /* eslint-disable @typescript-eslint/naming-convention */
    /* These names must match the backend ProtoBuf */
    Error = "ERROR",
    Warning = "WARNING",
    Information = "INFORMATION",
    Hint = "HINT",
    /* eslint-enable @typescript-eslint/naming-convention */
}

/**
 * Information about a diagnostic provided by VSCode.
 */
export type Diagnostic = {
    location: DiagnosticFileLocation;
    message: string;
    severity: DiagnosticSeverity;
    /* eslint-disable @typescript-eslint/naming-convention */
    current_blob_name?: string;
    /* eslint-enable @typescript-eslint/naming-convention */
};

export type DiagnosticFileLocation = {
    path: string;
    /* eslint-disable @typescript-eslint/naming-convention */
    /* These names must match the backend ProtoBuf */
    line_start: number;
    line_end: number;
    /* eslint-enable @typescript-eslint/naming-convention */
};

export interface SelectedCodeDetails {
    selectedCode: string;
    prefix: string;
    suffix: string;
    path: string;
    language: string;
    // Character index of first character in prefix relative to document
    // See public_api.proto for more details.
    prefixBegin?: number;
    // Character index of last character in suffix relative to document
    suffixEnd?: number;
}

export enum SystemStatus {
    initializing = "initializing",
    complete = "complete",
    incomplete = "incomplete",
    error = "error",
}

export enum SystemStateName {
    authenticated = "authenticated",
    disabledGithubCopilot = "disabledGithubCopilot",
    hasMovedExtensionAside = "hasMovedExtensionAside",
    workspacePopulated = "workspacePopulated",
    syncingPermitted = "syncingPermitted",
    disabledCodeium = "disabledCodeium",
    showSummary = "showSummary",
    workspaceTooLarge = "workspaceTooLarge",
    uploadingHomeDir = "uploadingHomeDir",
}

/* eslint-disable @typescript-eslint/naming-convention */
export interface PerFileChangeStats {
    file_path: string;
    insertion_count: number;
    deletion_count: number;
    old_file_path: string;
}

export interface PerTypeChangedFileStats {
    changed_file_count: number;
    per_file_change_stats_head: PerFileChangeStats[];
    per_file_change_stats_tail: PerFileChangeStats[];
}

export interface ChangedFileStats {
    added_file_stats: PerTypeChangedFileStats;
    broken_file_stats: PerTypeChangedFileStats;
    copied_file_stats: PerTypeChangedFileStats;
    deleted_file_stats: PerTypeChangedFileStats;
    modified_file_stats: PerTypeChangedFileStats;
    renamed_file_stats: PerTypeChangedFileStats;
    unmerged_file_stats: PerTypeChangedFileStats;
    unknown_file_stats: PerTypeChangedFileStats;
}

export interface GenerateCommitMessageSubrequest {
    relevant_commit_messages: string[];
    example_commit_messages: string[];
}

export interface CommitMessagePromptData {
    changedFileStats: ChangedFileStats;
    diff: string;
    generatedCommitMessageSubrequest: GenerateCommitMessageSubrequest;
}

export interface PromptDataOptions {
    diffBudget: number;
    messageBudget: number;
    relevantMessageSubbudget: number;
    diffNoopLineLimit: number;
    onlyUseStagedChanges: boolean;
    maxExampleCommitMessages: number;
}

/* eslint-enable @typescript-eslint/naming-convention */

// These values are based on the gRPC statuscodes
// https://grpc.github.io/grpc/core/md_doc_statuscodes.html
export enum APIStatus {
    ok,
    cancelled,
    unknown,
    unavailable,
    unimplemented,
    invalidArgument,
    resourceExhausted,
    unauthenticated,
    permissionDenied,
    deadlineExceeded,
    // Non-gRPC status codes are prefixed with augment
    augmentTooLarge,
}
