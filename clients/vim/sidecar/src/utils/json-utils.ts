/* eslint-disable @typescript-eslint/no-explicit-any */
export function verifyArray(className: string, attributeName: string, obj: any) {
    if (!Array.isArray(obj)) {
        throw new Error(`Value of ${className}.${attributeName} is not Array`);
    }
}

export function toNumber(className: string, attributeName: string, value: any): number {
    const typeName = typeof value;
    if (typeName !== "number") {
        throw new Error(
            `Value of ${className}.${attributeName} has unexpected type. ` +
                `Expected number, received ${typeName}`
        );
    }
    return value as number;
}

export function toString(className: string, attributeName: string, value: any): string {
    const typeName = typeof value;
    if (typeName !== "string") {
        throw new Error(
            `Value of ${className}.${attributeName} has unexpected type. ` +
                `Expected string, received ${typeName}`
        );
    }
    return value as string;
}

export function toStringArray(className: string, attributeName: string, obj: any): string[] {
    verifyArray(className, attributeName, obj);
    const resultArray: string[] = [];
    for (const s of obj) {
        resultArray.push(toString(className, attributeName, s));
    }
    return resultArray;
}

export function toStringOrNull(
    className: string,
    attributeName: string,
    value: any
): string | null {
    const typeName = typeof value;
    if (typeName !== "string" && value !== null) {
        throw new Error(
            `Value of ${className}.${attributeName} has unexpected type. ` +
                `Expected string, received ${typeName}`
        );
    }
    return value as string | null;
}

export function toBoolean(
    className: string,
    attributeName: string,
    value: any,
    fallback: boolean = false
): boolean {
    if (value === null) {
        return fallback;
    }
    const typeName = typeof value;
    if (typeName !== "boolean") {
        throw new Error(
            `Value of ${className}.${attributeName} has unexpected type. ` +
                `Expected boolean, received ${typeName}`
        );
    }
    return value as boolean;
}
