import * as vscode from "../vscode";
import { directoryExistsAsync, makeDirs, readFileUtf8, writeFileUtf8 } from "./fs-utils";

export interface IAugmentGlobalState {
    // In-memory storage. Use for smaller data.
    update<T>(key: string, value: T): Thenable<void>;
    get<T>(key: string): T | undefined;

    // Persistent storage. Use for larger data.
    save<T>(key: string, value: T, opts?: IGlobalContextSaveLoadOpts): Thenable<void>;
    load<T>(key: string, opts?: IGlobalContextSaveLoadOpts): Thenable<T | undefined>;
}

// Keys for the extensionContext.globalState
export enum GlobalContextKeys {
    lastEnabledExtensionVersion = "lastEnabledExtensionVersion",
    chatPreviouslyOpened = "chatPreviouslyOpened",
    userKeybindingInfo = "userKeybindingInfo",
    onboarding = "onboarding",
    sessionId = "sessionId",
    actionSystemStates = "actionSystemStates",
    requestIdSelectionMetadata = "requestIdSelectionMetadata",
    recentlyOpenedFiles = "recentlyOpenedFiles",
    fuzzyFsFoldersIndex = "fuzzyFsFoldersIndex",
    fuzzyFsFilesIndex = "fuzzyFsFilesIndex",
    fuzzyBlobNamesToSymbols = "fuzzyBlobNamesToSymbols",
    workspaceMessageStates = "workspaceMessageStates",
}

export interface IGlobalContextSaveLoadOpts {
    uniquePerWorkspace?: boolean;
}

export class AugmentGlobalState implements IAugmentGlobalState {
    private static storageSubDir: string = "augment-global-state";

    constructor(private _extensionContext: vscode.ExtensionContext) {}

    update<T>(key: GlobalContextKeys, value: T): Thenable<void> {
        return this._extensionContext.globalState.update(key, value);
    }

    get<T>(key: GlobalContextKeys): T | undefined {
        return this._extensionContext.globalState.get(key);
    }

    async save<T>(
        key: GlobalContextKeys,
        value: T,
        opts?: IGlobalContextSaveLoadOpts
    ): Promise<void> {
        await this._ensureStorageUriExists(opts);
        const uri = this._getFileUri(key, opts);
        return await writeFileUtf8(uri.fsPath, JSON.stringify(value));
    }

    async load<T>(
        key: GlobalContextKeys,
        opts?: IGlobalContextSaveLoadOpts
    ): Promise<T | undefined> {
        await this._ensureStorageUriExists(opts);
        const uri = this._getFileUri(key, opts);
        try {
            const data = await readFileUtf8(uri.fsPath);
            return JSON.parse(data) as T;
        } catch {
            return undefined;
        }
    }

    private async _ensureStorageUriExists(opts?: IGlobalContextSaveLoadOpts): Promise<void> {
        const uri = this._getStorageUri(opts);
        if (!(await directoryExistsAsync(uri.fsPath))) {
            await makeDirs(uri.fsPath);
        }
    }

    private _getStorageUri(opts?: IGlobalContextSaveLoadOpts): vscode.Uri {
        // If we are in a workspace, and we request unique per workspace,
        // use the workspace's storageUri.
        if (opts?.uniquePerWorkspace && this._extensionContext.storageUri) {
            return vscode.Utils.joinPath(
                this._extensionContext.storageUri,
                AugmentGlobalState.storageSubDir
            );
        }
        return vscode.Utils.joinPath(
            this._extensionContext.globalStorageUri,
            AugmentGlobalState.storageSubDir
        );
    }

    private _getFileUri(key: GlobalContextKeys, opts?: IGlobalContextSaveLoadOpts): vscode.Uri {
        return vscode.Utils.joinPath(this._getStorageUri(opts), `${key}.json`);
    }
}
