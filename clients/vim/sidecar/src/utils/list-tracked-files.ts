import * as vscode from "../vscode";
import { readDirectorySync } from "./fs-utils";
import { FullPathFilter } from "./path-iterator";
import { joinPath, relativeDirName } from "./path-utils";
import { FileType } from "./types";
import { uriToAbsPath } from "./uri";

// listTrackedFiles returns a list of files under startUri that are accepted by the given
// pathFilter. The returned path names are relative to rootUri. The optional maxFiles parameter
// caps the number of files returned: if maxFiles is set, the returned list will be at most
// maxFiles long. If maxFiles is not set, the returned list will contain all accepted files.
export async function listTrackedFiles(
    startUri: vscode.Uri,
    rootUri: vscode.Uri,
    pathFilter: FullPathFilter,
    maxFiles?: number
): Promise<Array<string>> {
    const startPath = uriToAbsPath(startUri);
    const rootPath = uriToAbsPath(rootUri);
    const directoryStack = new Array<string>();
    directoryStack.push(startPath);
    const acceptedFilePaths = new Array<string>();

    // This function is performance sensitive, so it is important that it go async as little as
    // possible. The variables below control a hand-coded periodic yielder that causes it to yield
    // every `yieldMs` milliseconds to avoid blocking the event loop while minimizing awaits. This
    // yielding method is faster, if uglier, than the periodicYielder() function used elsewhere,
    // mainly because it only goes async when needed.
    // We yield at the top of both of the loops below, as either one can theoretically become a
    // tight loop independently of the other.
    const yieldMs = 200;
    let lastYield = Date.now();

    let currDirAbs;
    while (
        (currDirAbs = directoryStack.pop()) !== undefined &&
        (maxFiles === undefined || acceptedFilePaths.length < maxFiles)
    ) {
        // See comments above regarding yielding.
        const now = Date.now();
        if (now - lastYield >= yieldMs) {
            await new Promise((resolve) => setTimeout(resolve, 0));
            lastYield = Date.now();
        }

        const currDirRel = relativeDirName(rootPath, currDirAbs);
        const localPathFilter = pathFilter.makeLocalPathFilter(currDirRel);
        const dirEntries = readDirectorySync(currDirAbs);
        for (const [fileName, fileType] of dirEntries) {
            // See comments above regarding yielding.
            const now = Date.now();
            if (now - lastYield >= yieldMs) {
                await new Promise((resolve) => setTimeout(resolve, 0));
                lastYield = Date.now();
            }

            if (fileName === "." || fileName === "..") {
                continue;
            }

            const relativePath = joinPath(currDirRel, fileName, fileType === FileType.directory);
            if (!localPathFilter.acceptsPath(relativePath, fileType)) {
                continue;
            }

            if (fileType === FileType.file) {
                acceptedFilePaths.push(relativePath);
            } else if (fileType === FileType.directory) {
                directoryStack.push(joinPath(currDirAbs, fileName));
            }
        }
    }

    return Promise.resolve(acceptedFilePaths);
}
