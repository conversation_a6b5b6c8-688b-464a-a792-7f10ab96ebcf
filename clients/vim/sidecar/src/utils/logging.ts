import { type AugmentLogger } from "../logging";

export function logArray<T extends string>(
    logger: AugmentLogger,
    logLevel: string,
    array: Array<T>,
    maxItems?: number
): void {
    let count = 0;
    for (const item of array) {
        logger.log(logLevel, `  ${item}`);
        count++;
        if (maxItems !== undefined && count >= maxItems) {
            logger.log(logLevel, `  ...`);
            break;
        }
    }
}
