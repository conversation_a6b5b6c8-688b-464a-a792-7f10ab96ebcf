// msecToTimestamp converts a number of milliseconds to an array
// of seconds and nanoseconds.
export function msecToTimestamp(msec: number): [number, number] {
    // Round down to the nearest second, remainder will be returned
    // as nanoseconds
    const sec = Math.floor(msec / 1000);
    const nsec = (msec % 1000) * 1000000;
    return [sec, nsec];
}

// msToReadableString converts a number of milliseconds to a user readable string.
export function msecToReadableString(ms: number): string {
    const minutes = Math.floor(ms / (1000 * 60));

    if (minutes > 0) {
        return `${minutes} minute${minutes !== 1 ? "s" : ""}`;
    } else {
        const seconds = Math.floor((ms % (1000 * 60)) / 1000);
        return `${seconds} second${seconds !== 1 ? "s" : ""}`;
    }
}
