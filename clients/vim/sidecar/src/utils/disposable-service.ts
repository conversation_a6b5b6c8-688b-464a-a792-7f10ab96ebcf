import * as vscode from "../vscode";

/**
 * DisposableCollection is a class for managing a collection of disposables.
 */
export class DisposableCollection implements vscode.Disposable {
    private _disposables: Array<vscode.Disposable> = [];

    // Arrange for the given disposable to be disposed when this object is disposed.
    public add<T extends vscode.Disposable>(item: T): T {
        if (item === undefined) {
            throw new Error("Attempt to add undefined disposable to DisposableCollection");
        }
        this._disposables.push(item);
        return item;
    }

    // Arrange for the given array of disposable to be disposed when this object is disposed.
    public addAll(...items: vscode.Disposable[]) {
        items.forEach((item) => this.add(item));
    }

    // Transfer the disposables from the given collection to this one.
    public adopt(disposables: DisposableCollection) {
        this._disposables.push(...disposables._disposables);
        disposables._disposables.length = 0;
    }

    public dispose() {
        for (const disposable of this._disposables) {
            disposable.dispose();
        }
        this._disposables.length = 0;
    }
}

/**
 * DisposableService is a base class for components that are disposable and want
 * to dispose some of their components when they are disposed.
 */
export class DisposableService implements vscode.Disposable {
    private _disposables = new DisposableCollection();
    private _priorityDisposables = new DisposableCollection();

    constructor(
        disposables: DisposableCollection = new DisposableCollection(),
        priorityDisposables: DisposableCollection = new DisposableCollection()
    ) {
        this._disposables.adopt(disposables);
        this._priorityDisposables.adopt(priorityDisposables);
    }

    // Arrange for the given disposable to be disposed when this object is disposed. Items added
    // with the priority flag will be disposed before items added without it.
    public addDisposable<T extends vscode.Disposable>(item: T, priority = false): T {
        if (priority) {
            return this._priorityDisposables.add(item);
        } else {
            return this._disposables.add(item);
        }
    }

    // Arrange for the given array of disposable to be disposed when this object is disposed.
    public addDisposables(...items: vscode.Disposable[]) {
        this._disposables.addAll(...items);
    }

    dispose() {
        this._priorityDisposables.dispose();
        this._disposables.dispose();
    }
}
