import { existsSync, FSWatcher, watch, WatchEventType } from "fs";

import { getErrmsg } from "./exceptions";
import { getLogger } from "./logging";
import { LogLevel, NotificationManager } from "./notifications";
import { joinPath } from "./utils/path-utils";
import { EventEmitter, FileSystemWatcher, Uri } from "./vscode";

type FSEventType = "create" | "change" | "delete";

/**
 * Creates a FileSystemWatcher for the Vim environment.
 *
 * While VSCode provides its own FileSystemWatcher implementation, we need a custom version
 * for the Vim integration because:
 * 1. We're running in a separate Node.js process (sidecar) outside of VSCode
 * 2. We need to monitor filesystem changes to keep the Vim extension in sync with workspace changes
 *
 * This implementation uses Node's built-in `fs.watch` to monitor filesystem events and
 * transforms them into VSCode-compatible events (onDidCreate, onDidChange, onDidDelete)
 * that match VSCode's FileSystemWatcher interface. This allows the rest of the codebase
 * to work with filesystem events in the same way regardless of the editor environment.
 */
class NodeFileSystemWatcher implements FileSystemWatcher {
    private _onDidCreate = new EventEmitter<Uri>();
    private _onDidChange = new EventEmitter<Uri>();
    private _onDidDelete = new EventEmitter<Uri>();

    private logger = getLogger("FileSystemWatcher");
    private workspaceFolder: string;
    private watcher: FSWatcher;

    public readonly onDidCreate = this._onDidCreate.event;
    public readonly onDidChange = this._onDidChange.event;
    public readonly onDidDelete = this._onDidDelete.event;

    constructor(workspaceFolder: string) {
        this.workspaceFolder = workspaceFolder;
        const options = {
            recursive: true,
            persistent: true,
        };

        this.watcher = watch(workspaceFolder, options, (eventType, filename) =>
            this.listener(eventType, filename)
        );
        this.watcher.on("error", (error) => {
            this.logger.error(getErrmsg(error));
            NotificationManager.getInstance().sendLogMessage(
                `Filesystem watcher error (workspace ${workspaceFolder}): ${getErrmsg(error)}`,
                LogLevel.error
            );
        });

        this.logger.info(`Filesystem watcher initialized with directory ${workspaceFolder}`);
    }

    private listener(eventType: WatchEventType, filename: string | null): void {
        // NOTE(mpauly): I don't know why the filename would be null, but the types say it's possible
        if (filename === null) {
            return;
        }

        // TODO(mpauly): Once we're confident that this is working correctly, remote this log line
        this.logger.debug(`Received raw filesystem event: ${eventType} ${filename}`);

        // TODO(mpauly): add cache

        const path = joinPath(this.workspaceFolder, filename);
        if (eventType === "rename") {
            this.statThenFire(path);
        } else if (eventType === "change") {
            this.fireEvent(path, "change");
        }
    }

    // Check if a file exists and then fire the appropriate event. This is necessary because node's
    // fs.watch sends the same event (rename) for file creation and deletion
    private statThenFire(path: string) {
        if (existsSync(path)) {
            this.fireEvent(path, "create");
        } else {
            this.fireEvent(path, "delete");
        }
    }

    private fireEvent(path: string, eventType: FSEventType) {
        this.logger.debug(`Firing filesystem event: ${eventType} ${path}`);

        if (eventType === "create") {
            this._onDidCreate.fire(Uri.file(path));
        } else if (eventType === "change") {
            this._onDidChange.fire(Uri.file(path));
        } else if (eventType === "delete") {
            this._onDidDelete.fire(Uri.file(path));
        }
    }

    public dispose(): void {
        this.watcher.close();
    }
}

class DummyFileSystemWatcher implements FileSystemWatcher {
    public readonly onDidCreate = new EventEmitter<Uri>().event;
    public readonly onDidChange = new EventEmitter<Uri>().event;
    public readonly onDidDelete = new EventEmitter<Uri>().event;
    public dispose(): void {}
}

export function createFileSystemWatcher(workspaceFolder: string): FileSystemWatcher {
    try {
        const watcher = new NodeFileSystemWatcher(workspaceFolder);
        return watcher;
    } catch (error: unknown) {
        const logger = getLogger("FileSystemWatcher");
        if (error instanceof Error) {
            logger.error(`Failed to create filesystem watcher: ${getErrmsg(error)}`);
            logger.error(`Stack trace: ${error.stack}`);
        } else {
            logger.error(`Failed to create filesystem watcher: ${getErrmsg(error)}`);
        }
        logger.error("Falling back to dummy filesystem watcher");

        // Notify the user
        NotificationManager.getInstance().sendLogMessage(
            `Failed to create filesystem watcher (workspace ${workspaceFolder}): ${getErrmsg(error)}`,
            LogLevel.error
        );

        return new DummyFileSystemWatcher();
    }
}
