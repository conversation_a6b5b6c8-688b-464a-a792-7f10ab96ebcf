import fs from "fs";
import log, { Logger, LogLevelNames } from "loglevel";
import os from "os";
import path from "path";

// If running in the test environment, use a temporary directory for storage
const XDG_STATE_HOME = process.env.TEST_TMPDIR
    ? path.join(process.env.TEST_TMPDIR, "state")
    : process.env.XDG_STATE_HOME || path.join(os.homedir(), ".local", "state");
const LOG_FILE = path.join(XDG_STATE_HOME, "augment", "augment-server.log");

export function initializeLogger(): Logger {
    // Ensure the directory for the log file exists
    const logDir = path.dirname(LOG_FILE);
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }

    // Set the log level, defaulting to "warn"
    const logLevel = (process.env.AUGMENT_LOG_LEVEL || "warn").toLowerCase();
    let logLevelWarning: string | undefined = undefined;
    if (["trace", "debug", "info", "warn", "error"].includes(logLevel)) {
        log.setLevel(logLevel as LogLevelNames);
    } else {
        logLevelWarning = `Environment variable AUGMENT_LOG_LEVEL set to invalid log level "${logLevel}". Defaulting to "warn"`;
        log.setLevel("warn");
    }

    // Write to file
    log.methodFactory = function (methodName, _logLevel, loggerName) {
        return function (message) {
            const timestamp = new Date().toISOString();
            const name = typeof loggerName === "string" ? ` '${loggerName}'` : "";
            fs.appendFileSync(
                LOG_FILE,
                `${timestamp} [${methodName.toUpperCase()}]${name} ${message}\n`
            );
        };
    };

    const logger = log.getLogger("server");

    // Log the warning if there was one. We do this here because the log level must be set before
    // the logger is initialized and the logger must be initialized before we can log anything.
    if (logLevelWarning) {
        logger.warn(logLevelWarning);
    }

    return logger;
}

// TODO(rich): move to Winston?
export function getLogger(name: string): AugmentLogger {
    const logger = log.getLogger(name);
    return {
        ...logger,
        verbose: (message: string, ...args: unknown[]) => logger.debug(message, ...args),
    };
}

// We restrict the logger API to the subset that we use.
// This avoids a footgun: https://github.com/winstonjs/winston/issues/1711
export interface AugmentLogger {
    log: (level: string, message: string, ...args: any[]) => void;
    info: (message: string, ...args: any[]) => void;
    debug: (message: string, ...args: any[]) => void;
    warn: (message: string, ...args: any[]) => void;
    error: (message: string, ...args: any[]) => void;
    verbose: (message: string, ...args: any[]) => void;
}
