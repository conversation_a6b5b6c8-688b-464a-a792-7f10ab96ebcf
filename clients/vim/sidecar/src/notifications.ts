import { Connection, LogMessageParams, MessageType } from "vscode-languageserver/node";

import { type AugmentLogger, getLogger } from "./logging";

export enum LogLevel {
    error = 1,
    warning = 2,
    info = 3,
    debug = 4,
}

export class NotificationManager {
    private static instance: NotificationManager;
    private connection: Connection | null = null;
    private logger: AugmentLogger;

    private constructor() {
        this.logger = getLogger("NotificationManager");
    }

    public static getInstance(): NotificationManager {
        if (!NotificationManager.instance) {
            NotificationManager.instance = new NotificationManager();
        }
        return NotificationManager.instance;
    }

    public setConnection(connection: Connection): void {
        this.connection = connection;
    }

    public sendLogMessage(message: string, level: LogLevel): void {
        if (!this.connection) {
            this.logger.error(`Failed to send log message to client: No connection available`);
            return;
        }

        this.logger.debug(`Sending log message to client: ${message}`);

        const params: LogMessageParams = {
            type: this._logLevelToMessageType(level),
            message: message,
        };
        void this.connection.sendNotification("window/logMessage", params);
    }

    private _logLevelToMessageType(level: LogLevel): MessageType {
        switch (level) {
            case LogLevel.error:
                return MessageType.Error;
            case LogLevel.warning:
                return MessageType.Warning;
            case LogLevel.info:
                return MessageType.Info;
            case LogLevel.debug:
                return MessageType.Debug;
        }
    }
}
