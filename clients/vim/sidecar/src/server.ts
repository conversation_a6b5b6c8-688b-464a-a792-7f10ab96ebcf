import os from "os";
import * as path from "path";
import { TextDocument } from "vscode-languageserver-textdocument";
import {
    CompletionItem,
    createConnection,
    DidChangeTextDocumentParams,
    DidCloseTextDocumentParams,
    DidOpenTextDocumentParams,
    ErrorCodes,
    InitializeParams,
    InitializeResult,
    ResponseError,
    TextDocumentPositionParams,
    TextDocumentSyncKind,
    WorkspaceFolder,
} from "vscode-languageserver/node";

import { APIServer, APIServerImpl, ChatResult } from "./augment-api";
import { AugmentConfigListener } from "./augment-config-listener";
import { AuthSessionStore } from "./auth/auth-session-store";
import { OAuthFlow } from "./auth/oauth-flow";
import { Exchange } from "./chat/chat-types";
import { getErrmsg, SkipCompletion } from "./exceptions";
import { AugmentExtension, getSessionId } from "./extension";
import { initializeLogger } from "./logging";
import { NotificationManager } from "./notifications";
import { AugmentGlobalState } from "./utils/context";
import * as vscode from "./vscode";

enum AugmentErrorCode {
    unauthorized = 401,
}

const logger = initializeLogger();

const connection = createConnection();
const notificationManager = NotificationManager.getInstance();
notificationManager.setConnection(connection);

// If running in the test environment, use a temporary directory for storage
const XDG_DATA_HOME = process.env.TEST_TMPDIR
    ? path.join(process.env.TEST_TMPDIR, "data")
    : process.env.XDG_DATA_HOME || path.join(os.homedir(), ".local", "share");
const STORAGE_PATH = path.join(XDG_DATA_HOME, "vim-augment");

let augmentAPI: APIServer;
let oauthFlow: OAuthFlow;
let session: AuthSessionStore;
let extension: AugmentExtension | undefined = undefined;
const configListener = new AugmentConfigListener();

/**
 * Initialize the extension
 * The code is roughly the same as the activate() function in vscode's
 * extension.ts file.
 */
async function initExtension(context: vscode.ExtensionContext, userAgent: string) {
    session = new AuthSessionStore(context, configListener);

    const globalState = new AugmentGlobalState(context);
    const sessionId = getSessionId(globalState);
    logger.info(`Session ID: ${sessionId}`);

    augmentAPI = new APIServerImpl(configListener, session, sessionId, userAgent, global.fetch);
    oauthFlow = new OAuthFlow(context, configListener, augmentAPI, session);

    extension = new AugmentExtension(context, configListener, augmentAPI, session);
    await extension.enable();
}

connection.onInitialize(async (params: InitializeParams) => {
    logger.info("Initializing Language Server");

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
    const editor = params.initializationOptions?.editor ?? "unknown";
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
    const pluginVersion = params.initializationOptions?.pluginVersion ?? "unknown";
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
    const vimVersion = params.initializationOptions?.vimVersion ?? "unknown";
    const userAgent = `Augment.vim/${pluginVersion} ${editor}/${vimVersion}`;
    logger.info(`User agent: ${userAgent}`);

    let workspaceFolders = params.workspaceFolders ?? ([] as WorkspaceFolder[]);
    logger.info(`Roots: ${JSON.stringify(workspaceFolders)}`);
    for (const folder of workspaceFolders) {
        logger.info(`Root name, URI: ${folder.name}, ${folder.uri}`);
    }
    vscode.setWorkspaceFolders(workspaceFolders);

    let context: vscode.ExtensionContext;
    try {
        context = new vscode.ExtensionContext(vscode.Uri.file(STORAGE_PATH));
    } catch (error) {
        logger.error(`Error initializing server context storage: ${getErrmsg(error)}`);
        throw error;
    }

    // Call the extension entry point
    await initExtension(context, userAgent);

    const result: InitializeResult = {
        capabilities: {
            completionProvider: {
                resolveProvider: false,
            },
            textDocumentSync: TextDocumentSyncKind.Incremental,
        },
    };
    return result;
});

connection.onInitialized(() => {
    logger.info("Language server initialized");
});

connection.onCompletion(
    async (
        _textDocumentPosition: TextDocumentPositionParams
    ): Promise<CompletionItem[] | ResponseError<void>> => {
        if (!extension?.ready) {
            logger.debug("Not logged in. Please login first.");
            return new ResponseError(
                // NOTE(mpauly): Use a custom error code so that the client can handle the not
                // logged in case without needing to parse the error message.
                AugmentErrorCode.unauthorized,
                "Not logged in. Please login first."
            );
        }
        const document = vscode.workspace.documents.get(_textDocumentPosition.textDocument.uri);
        if (!document) {
            logger.warn(`Not tracking the document ${_textDocumentPosition.textDocument.uri}`);
            return new ResponseError(
                ErrorCodes.InternalError,
                `Not tracking the document ${_textDocumentPosition.textDocument.uri}`
            );
        }
        if (extension?.completionServer === undefined) {
            logger.debug("Completion server is not initialized.");
            return new ResponseError(
                ErrorCodes.InternalError,
                "Completion server is not initialized"
            );
        }

        try {
            // convert lsp text document to vscode text document, which is used internally
            const vscodeDoc = new vscode.LspTextDocumentWrapper(document);
            const pos = new vscode.Position(
                _textDocumentPosition.position.line,
                _textDocumentPosition.position.character
            );
            const result = await extension.completionsModel.generateCompletion(vscodeDoc, pos);
            logger.info(`Completion: ${result?.requestId}`);
            logger.debug(`Completion result: ${JSON.stringify(result)}`);
            return [
                {
                    label: result?.requestId ?? "",
                    insertText: result?.completions[0]?.completionText ?? "",
                },
            ];
        } catch (error) {
            // Don't return an error if the completion was cancelled or skipped
            if (error instanceof SkipCompletion) {
                logger.debug(`Completion was cancelled or skipped: ${getErrmsg(error)}`);
                return [];
            }
            logger.error(`Error getting completion: ${getErrmsg(error)}`);
            return new ResponseError(
                ErrorCodes.InternalError,
                `Error getting completion: ${getErrmsg(error)}`
            );
        }
    }
);

// Initiate the OAuth flow.
interface LoginResponse {
    loggedIn: boolean;
    url: string;
}

connection.onRequest("augment/login", async (): Promise<LoginResponse | ResponseError<string>> => {
    try {
        if (session.isLoggedIn) {
            logger.info("Already logged in");
            return { loggedIn: true, url: "" };
        } else {
            logger.info("Logging in...");
            const url = await oauthFlow.startFlow();
            return { loggedIn: false, url: url };
        }
    } catch (error) {
        const stack = error instanceof Error ? error.stack : "No stack trace available";
        const errmsg = getErrmsg(error);
        logger.error(`Error handling augment/login: ${errmsg}`);
        logger.error(`Stack trace: ${stack}`);
        return new ResponseError(
            ErrorCodes.InternalError,
            "Failed to process request. See server log for details."
        );
    }
});

interface LogoutResponse {
    success: boolean;
}

connection.onRequest(
    "augment/logout",
    async (): Promise<LogoutResponse | ResponseError<string>> => {
        try {
            await session.removeSession();
            extension?.disable();
            logger.info("Logged out.");
            return { success: true };
        } catch (error) {
            const stack = error instanceof Error ? error.stack : "No stack trace available";
            const errmsg = getErrmsg(error);
            logger.error(`Error handling augment/logout: ${errmsg}`);
            logger.error(`Stack trace: ${stack}`);
            return new ResponseError(
                ErrorCodes.InternalError,
                "Failed to process request. See server log for details."
            );
        }
    }
);

// Get an authentication token
interface TokenMessageParams {
    code: string;
}

interface TokenResponse {}

connection.onRequest(
    "augment/token",
    async (params: TokenMessageParams): Promise<TokenResponse | ResponseError<string>> => {
        // Handle provided code
        try {
            await oauthFlow.handleAuthJson(params.code);
        } catch (error) {
            const stack = error instanceof Error ? error.stack : "No stack trace available";
            const errmsg = getErrmsg(error);
            logger.error(`Error handling user code: ${errmsg}`);
            logger.error(`Stack trace: ${stack}`);

            if (error instanceof SyntaxError) {
                return new ResponseError(
                    ErrorCodes.InternalError,
                    "Failed to parse user code. Did you paste the code provided by the sign in page?"
                );
            } else if (errmsg === "Unknown state") {
                return new ResponseError(
                    ErrorCodes.InternalError,
                    "Could not find sign in state. Did you paste the code into the same prompt where you copied the link and did you navigate to the full sign in URL (it may wrap onto multiple lines)?"
                );
            } else {
                return new ResponseError(
                    ErrorCodes.InternalError,
                    "Failed to process user code. Please try again."
                );
            }
        }

        // Enable the extension
        try {
            await extension?.enable();
            logger.info("Logged in.");
            return {};
        } catch (error) {
            const stack = error instanceof Error ? error.stack : "No stack trace available";
            const errmsg = getErrmsg(error);
            logger.error(`Error enabling extension: ${errmsg}`);
            logger.error(`Stack trace: ${stack}`);
            return new ResponseError(
                ErrorCodes.InternalError,
                "Failed to process request. See server log for details."
            );
        }
    }
);

// Get the current login status
interface StatusResponse {
    loggedIn: boolean;
    syncPercentage?: number;
}

connection.onRequest("augment/status", (): StatusResponse => {
    if (!extension?.ready) {
        return { loggedIn: false };
    }

    const syncProgress = extension?.workspaceManager?.getSyncingProgress();
    if (!syncProgress) {
        return { loggedIn: true };
    }

    let totalFiles = 0;
    let syncedFiles = 0;
    for (const p of syncProgress) {
        if (!p.progress) {
            // If we don't have progress yet, we can't calculate the sync percentage
            return { loggedIn: true };
        }

        // We use only newly tracked workspace folders for the sync percentage
        if (p.progress.newlyTracked) {
            logger.info(
                `New folder ${p.folderRoot} sync progress: tracked=${p.progress.trackedFiles}, backlog=${p.progress.backlogSize}`
            );
            totalFiles += p.progress.trackedFiles;
            syncedFiles += p.progress.trackedFiles - p.progress.backlogSize;
        }
    }
    const syncPercentage = totalFiles > 0 ? Math.floor((syncedFiles / totalFiles) * 100) : 100;

    return {
        loggedIn: true,
        syncPercentage,
    };
});

// Make a chat request
interface ChatParams {
    textDocumentPosition: TextDocumentPositionParams;
    message: string;
    selectedText?: string;
    history?: Exchange[];
}

export interface ChatResponse {
    requestId: string;
    text: string;
}

export interface ChatChunkParams {
    requestId: string;
    text: string;
}

connection.onRequest(
    "augment/chat",
    async (params: ChatParams): Promise<ChatResponse | ResponseError<string>> => {
        if (!extension?.ready) {
            logger.debug("Not logged in. Please login first.");
            return new ResponseError(
                // NOTE(mpauly): Use a custom error code so that the client can handle the not
                // logged in case without needing to parse the error message.
                AugmentErrorCode.unauthorized,
                "Not logged in. Please login first."
            );
        }
        const uri = params.textDocumentPosition.textDocument.uri;
        const document = vscode.workspace.documents.get(uri);
        if (!document) {
            logger.warn(`Not tracking the document ${uri}`);
            //     return new ResponseError(
            //         ErrorCodes.InternalError,
            //         `Not tracking the document ${uri}`
            //     );
        }
        if (extension?.completionServer === undefined) {
            logger.debug("Completion server is not initialized.");
            return new ResponseError(
                ErrorCodes.InternalError,
                "Completion server is not initialized"
            );
        }

        // // Must truncate the prefix and suffix to the appropriate length, otherwise
        // // the completion server will throw an error.
        const position = params.textDocumentPosition.position;
        const offset = document?.offsetAt(position) || 0;
        const text = document?.getText() || "";

        // For now, consider the entire document as the prefix and suffix.
        const prefixLength = offset;
        const suffixLength = text.length - offset;

        const prefix = text.slice(Math.max(0, offset - prefixLength), offset);
        const suffix = text.slice(offset, offset + suffixLength);

        const requestId = extension.completionServer.createRequestId();
        logger.info(`Chat (${requestId}) requested`);
        if (document) {
            logger.debug(`Document URI: ${document.uri}`);
            logger.debug(`Language ID: ${document.languageId}`);
        }
        if (params.selectedText !== undefined) {
            logger.debug(`Selected text: ${params.selectedText}`);
        }

        const context = extension?.workspaceManager?.getContext();
        const blobs = context?.blobs ?? {
            checkpointId: undefined,
            addedBlobs: [],
            deletedBlobs: [],
        };

        // Stream the chat response back to the client
        let fullResponse = "";
        try {
            const stream: AsyncIterable<ChatResult> = await augmentAPI.chatStream(
                requestId,
                params.message,
                params.history ?? [],
                blobs,
                [],
                [],
                undefined, // model
                // vcsChange,
                [],
                undefined,
                params.selectedText,
                prefix,
                suffix,
                document?.uri,
                document?.languageId,
                undefined,
                false,
                undefined,
                undefined
            );

            for await (const chunk of stream) {
                logger.debug(`Chat chunk for ${requestId}: ${chunk.text}`);
                const chatChunk: ChatChunkParams = {
                    requestId: requestId,
                    text: chunk.text,
                };
                await connection.sendNotification("augment/chatChunk", chatChunk);
                fullResponse += chunk.text;
            }
        } catch (error) {
            logger.error(`Error streaming chat: ${getErrmsg(error)}`);
            return new ResponseError(
                ErrorCodes.InternalError,
                `Error streaming chat: ${getErrmsg(error)}`
            );
        }

        extension.workspaceManager?.recordChatReponse(fullResponse);

        // Send the full response back to the client after streaming is complete. This is used in
        // the client to maintain the chat history
        return {
            requestId: requestId,
            text: fullResponse,
        };
    }
);

// Get the latest vim plugin version
interface PluginVersionParams {
    version: string;
}

interface PluginVersionResponse {
    version: string;
    isPrerelease: boolean;
}

connection.onRequest(
    "augment/pluginVersion",
    async (params: PluginVersionParams): Promise<PluginVersionResponse | ResponseError<string>> => {
        // Check if using a dev version
        if (params.version === "0.0.0") {
            return {
                version: "0.0.0",
                isPrerelease: false,
            };
        }

        // Extract the major/minor/patch version
        const pluginVersion = params.version.match(/^(\d+)\.(\d+)\.(\d+)$/);
        if (!pluginVersion) {
            return new ResponseError(
                ErrorCodes.InternalError,
                `Invalid version format: ${params.version}`
            );
        }
        const pluginMajor = parseInt(pluginVersion[1]);
        const pluginMinor = parseInt(pluginVersion[2]);
        const pluginPatch = parseInt(pluginVersion[3]);
        // Prerelease versions have a patch version of 0
        const isPrerelease = pluginPatch === 0;
        const branch = isPrerelease ? "prerelease" : "main";

        try {
            const url = `https://api.github.com/repos/augmentcode/augment.vim/commits/${branch}`;
            const response = await fetch(url);
            if (!response.ok) {
                logger.error(`Failed to fetch version: ${response.statusText}`);
                return new ResponseError(
                    ErrorCodes.InternalError,
                    `Failed to fetch plugin version: ${response.statusText}`
                );
            }

            const json = (await response.json()) as { commit?: { message?: unknown } };
            if (json?.commit?.message && typeof json.commit.message === "string") {
                const upstreamVersion = json.commit.message.match(
                    /^Augment Vim v(\d+)\.(\d+)\.(\d+)$/
                );
                if (!upstreamVersion) {
                    return new ResponseError(
                        ErrorCodes.InternalError,
                        "Unable to parse version from commit message"
                    );
                }

                // Check if the plugin version is ahead of the upstream version
                const upstreamMajor = parseInt(upstreamVersion[1]);
                const upstreamMinor = parseInt(upstreamVersion[2]);
                const upstreamPatch = parseInt(upstreamVersion[3]);
                if (
                    pluginMajor > upstreamMajor ||
                    (pluginMajor === upstreamMajor && pluginMinor > upstreamMinor) ||
                    (pluginMajor === upstreamMajor &&
                        pluginMinor === upstreamMinor &&
                        pluginPatch > upstreamPatch)
                ) {
                    return new ResponseError(
                        ErrorCodes.InternalError,
                        `Plugin version ${params.version} is ahead of upstream version ${upstreamMajor}.${upstreamMinor}.${upstreamPatch}`
                    );
                }

                return {
                    version: `${upstreamMajor}.${upstreamMinor}.${upstreamPatch}`,
                    isPrerelease,
                };
            }
            return new ResponseError(
                ErrorCodes.InternalError,
                "Unable to parse message from commit reponse"
            );
        } catch (error) {
            logger.error(`Error fetching version: ${getErrmsg(error)}`);
            return new ResponseError(
                ErrorCodes.InternalError,
                `Failed to fetch plugin version: ${getErrmsg(error)}`
            );
        }
    }
);

// Report accept/reject resolution for a completion
interface ResolveCompletionParams {
    requestId: string;
    accept: boolean;
}

connection.onNotification("augment/resolveCompletion", (params: ResolveCompletionParams) => {
    logger.info(`Completion (${params.requestId}) resolved with accept=${params.accept}`);

    if (extension?.completionReporter === undefined) {
        logger.warn("Completion reporter is not yet initialized.");
        return;
    }
    extension.completionReporter.reportResolution(
        params.requestId,
        Date.now(),
        Date.now(),
        params.accept ? 0 : undefined
    );
});

/**
 * onDidOpenTextDocument, onDidCloseTextDocument, and onDidChangeTextDocument are critical events
 * for tracking document content, which is needed for completions and chat context. We maintain
 * document state in a custom cache rather than the TextDocuments cache that comes with
 * vscode-languageserver-node, because we need access to the recent changes to the document.
 */
connection.onDidOpenTextDocument((params: DidOpenTextDocumentParams) => {
    logger.debug(`Document opened: ${params.textDocument.uri} ${params.textDocument.version}`);
    const docItem = params.textDocument;
    const doc = TextDocument.create(docItem.uri, docItem.languageId, docItem.version, docItem.text);
    vscode.workspace.documents.add(doc);
    vscode.onDidOpenTextDocumentEmitter.fire(new vscode.LspTextDocumentWrapper(doc));
});

connection.onDidCloseTextDocument((params: DidCloseTextDocumentParams) => {
    logger.debug(`Document closed: ${params.textDocument.uri}`);
    const doc = vscode.workspace.documents.get(params.textDocument.uri);
    if (!doc) {
        return;
    }
    vscode.workspace.documents.remove(params.textDocument.uri);
    vscode.onDidCloseTextDocumentEmitter.fire(new vscode.LspTextDocumentWrapper(doc));
});

connection.onDidChangeTextDocument((params: DidChangeTextDocumentParams) => {
    logger.debug(`Document changed: ${params.textDocument.uri}`);
    logger.debug(`Document changed params: ${JSON.stringify(params)}`);
    let doc = vscode.workspace.documents.get(params.textDocument.uri);
    if (!doc) {
        return;
    }
    // Note(rich): I'm iterating through each change rather than pass the set directly to update because
    // we'll need to translate the changes into the vscode format in an upcoming PR.
    const documentChanges = [];
    for (const change of params.contentChanges) {
        logger.debug(`Content change: ${JSON.stringify(change)}`);

        let innerRange: vscode.Range;
        if ("range" in change) {
            innerRange = new vscode.Range(
                change.range.start.line,
                change.range.start.character,
                change.range.end.line,
                change.range.end.character
            );
        } else {
            const lastPos = doc.positionAt(doc.getText().length);
            innerRange = new vscode.Range(0, 0, lastPos.line, lastPos.character);
        }
        let contentChanges = {
            range: innerRange,
            rangeOffset: doc.offsetAt(innerRange.start),
            rangeLength: doc.offsetAt(innerRange.end) - doc.offsetAt(innerRange.start),
            text: change.text,
        };
        documentChanges.push(contentChanges);

        doc = TextDocument.update(doc, [change], params.textDocument.version);
        logger.debug(`Updated document (${doc.version}):\n${doc.getText()}`);
    }
    vscode.workspace.documents.add(doc);
    const docChange: vscode.TextDocumentChangeEvent = {
        document: new vscode.LspTextDocumentWrapper(doc),
        contentChanges: documentChanges,
    };
    vscode.onDidChangeTextDocumentEmitter.fire(docChange);
});

connection.listen();
