/**
 * RingBuffer is a buffer that stores up to a maximum number of items, with newer
 * items replacing older items if the maxium size is exceeded, hence the buffer always
 * contains the last (up to) _maxItems items.
 */
export class RingBuffer<T> {
    // _insertCount is the number of items that have ever been added to the buffer minus
    // the number of items dropped from the end of the buffer through shiftRight, even
    // though it will never have more than _maxItems of them. It is used to determine
    // whether the buffer is full and where to place new items.
    private _insertCount = 0;

    // _emptySlots is the number of empty slots caused by items being dropped. This value
    // will never exceed _maxItems.
    private _emptySlots: number;

    // _items holds the contents of the buffer. Items are stored in a circular fashion,
    // with each newly inserted item overwriting the oldest one once the buffer reaches
    // _maxItems items.
    private _items: Array<T>;

    constructor(private readonly _maxItems: number) {
        this._items = new Array<T>(_maxItems);
        this._emptySlots = _maxItems;
    }

    // `empty` returns true if the buffer is empty
    public get empty() {
        return this.length === 0;
    }

    // `length` returns the number of items in the buffer
    public get length() {
        return this._maxItems - this._emptySlots;
    }

    // addItem adds an item to the buffer, increasing its `length` by one, up to _maxItems.
    public addItem(item: T) {
        this._items[this._insertCount % this._maxItems] = item;
        this._emptySlots = Math.max(this._emptySlots - 1, 0);
        this._insertCount++;
    }

    // shiftLeft removes up to the given number of items from the left of the buffer (i.e., the
    // oldest items), shifting any surviving items to the left and decreasing the `length` of the
    // buffer by the number of items dropped.
    public shiftLeft(count: number) {
        this._emptySlots = Math.min(this._emptySlots + count, this._maxItems);
    }

    // shiftRight removes up to the given number of items from the right of the buffer (i.e., the
    // newest items), shifting any surviving items to the right and decreasing the `length` of the
    // buffer by the number of items dropped.
    public shiftRight(count: number) {
        this._insertCount -= Math.min(count, this.length);
        this._emptySlots = Math.min(this._emptySlots + count, this._maxItems);
    }

    // `at` returns the item at the given index, where at(0) is the oldest item and
    // at(length - 1) is the newest item. As with Array.prototype.at, a negative index counts
    // backwards from the end of the buffer. `at` returns undefined if the index is out
    // of range.
    public at(index: number): T | undefined {
        if (index < 0) {
            if (index < -this.length) {
                return undefined;
            }
            return this._items[(this._insertCount + index) % this._maxItems];
        }
        if (index >= this.length) {
            return undefined;
        }
        return this._items[(this._insertCount + this._emptySlots + index) % this._maxItems];
    }

    // `_normalizeSliceIdx` converts a possibly negative index into a nonnegative index in
    // the range [0, length].
    private _normalizeSliceIdx(idx: number): number {
        if (idx >= 0) {
            return Math.min(idx, this.length);
        }
        return Math.max(this.length + idx, 0);
    }

    // `_translateIdx` converts a logical index into a physical index into the _items array.
    private _translateIdx(index: number): number {
        return (this._insertCount + this._emptySlots + index) % this._maxItems;
    }

    // `slice` returns a portion of the contents of the buffer. As with Array.prototype.slice,
    // a negative `start` or `end` counts backwards from the end of the buffer.
    public slice(start?: number, end?: number): T[] {
        const startIdx = this._normalizeSliceIdx(start ?? 0);
        const endIdx = this._normalizeSliceIdx(end ?? this.length);
        if (startIdx > endIdx) {
            return [];
        }
        const count = endIdx - startIdx;
        const sliceStart = this._translateIdx(startIdx);
        if (sliceStart + count <= this._maxItems) {
            return this._items.slice(sliceStart, sliceStart + count);
        }
        return this._items
            .slice(sliceStart)
            .concat(this._items.slice(0, (sliceStart + count) % this._maxItems));
    }

    // `Symbol.iterator` returns an iterator over the items in the buffer
    public *[Symbol.iterator](): Generator<T> {
        for (let idx = 0; idx < this.length; idx++) {
            yield this.at(idx)!;
        }
    }

    // Delete all items in the buffer
    public clear() {
        this._emptySlots = this._maxItems;
    }
}
