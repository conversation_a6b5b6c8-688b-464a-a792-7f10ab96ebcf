import { assert } from "console";
import { createHash } from "crypto";
import { v4 as uuidv4, validate as uuidValidate } from "uuid";

import {
    APIServer,
    CompletionResult,
    InvalidCompletionURLError,
    Language,
    Model,
    ModelConfig,
} from "./augment-api";
import { AugmentConfigListener } from "./augment-config-listener";
import { AuthSessionStore } from "./auth/auth-session-store";
import { CompletionServer } from "./completion-server";
import { CompletionsModel } from "./completions/completions-model";
import { APIError, getErrmsg, NoModelsError, UnknownModelError } from "./exceptions";
import { FeatureFlagManager, type FeatureFlags } from "./feature-flags";
import { type AugmentLogger, getLogger } from "./logging";
import { ClientMetricsReporter } from "./metrics/client-metrics-reporter";
import { CompletionAcceptanceReporter } from "./metrics/completion-acceptance-reporter";
import { AugmentGlobalState, GlobalContextKeys } from "./utils/context";
import { DisposableService } from "./utils/disposable-service";
import { delayMs } from "./utils/promise-utils";
import { APIStatus } from "./utils/types";
import * as vscode from "./vscode";
import { BlobNameCalculator } from "./workspace/blob-name-calculator";
import { WorkspaceManager } from "./workspace/workspace-manager";

export class AugmentExtension extends DisposableService {
    static readonly modelConfigBackoffMsecMax = 30000; // 30 seconds
    private _completionServer: CompletionServer | undefined = undefined;
    public workspaceManager: WorkspaceManager | undefined = undefined;
    private _enableCancel: vscode.CancellationTokenSource | undefined;

    private _defaultModel: string | undefined;
    private _modelInfo: Model | undefined;
    private _blobNameCalculator?: BlobNameCalculator;
    public get modelInfo(): Readonly<Model> | undefined {
        return this._modelInfo;
    }

    private _availableModels: string[] = [];

    private _languages: Language[] = [];
    // Test interface for external validation of languages
    public get languages(): ReadonlyArray<Language> {
        return this._languages;
    }
    // current feature flags
    readonly featureFlagManager = new FeatureFlagManager({
        fetcher: this._fetchFeatureFlags.bind(this),
        refreshIntervalMSec: 30 * 60 * 1000, // 30 minutes
    });

    private _completionAcceptanceReporter: CompletionAcceptanceReporter;
    private _clientMetricsReporter: ClientMetricsReporter;
    // For startup and shutdown.
    private enabled = false;
    private disposeOnDisable: vscode.Disposable[] = [];

    private _completionsModel: CompletionsModel;

    private readonly _logger: AugmentLogger = getLogger("AugmentExtension");
    constructor(
        private _extensionContext: vscode.ExtensionContext,
        private _augmentConfigListener: AugmentConfigListener,
        private _apiServer: APIServer,
        private _auth: AuthSessionStore
    ) {
        super();

        this._completionAcceptanceReporter = new CompletionAcceptanceReporter(_apiServer);
        this._clientMetricsReporter = new ClientMetricsReporter(_apiServer);
        this._completionsModel = new CompletionsModel(
            this,
            this._augmentConfigListener,
            this._clientMetricsReporter
        );
    }

    public get completionServer(): CompletionServer | undefined {
        return this._completionServer;
    }

    public get completionsModel(): CompletionsModel {
        return this._completionsModel;
    }

    // Note(rich): Direct access to the completion reporter means we do not have to
    // port some additional types.
    public get completionReporter(): CompletionAcceptanceReporter | undefined {
        return this._completionAcceptanceReporter;
    }

    // enableInProgress indicates whether there is an in-progress enable task.
    public get enableInProgress(): boolean {
        return this._enableCancel !== undefined;
    }

    // ready() indicates whether the extension has finished starting up.
    public get ready(): boolean {
        return this.enabled && !this.enableInProgress;
    }

    // enable() starts a background task to enable the extension. It does not wait
    // for the task to finish.
    public async enable(): Promise<void> {
        if (this.enabled || this.enableInProgress) {
            return;
        }

        const enableCancel = new vscode.CancellationTokenSource();
        this._enableCancel = enableCancel;

        try {
            await this._enable(enableCancel.token);
        } catch (e: any) {
            this._logger.info(`Unable to enable extension: ${getErrmsg(e)}`);
            if (process.env.JEST_WORKER_ID) {
                throw e;
            }
        } finally {
            // Uninstall this._enableCancel to indicate that there is no
            // "enable" in progress.
            enableCancel.dispose();
            this._enableCancel = undefined;
        }
    }

    // _enable() enables the extension by querying the model configuration from the
    // back end, opening the current set of workspace folders, and installing the
    // listeners needed to maintain its state.
    private async _enable(cancelToken: vscode.CancellationToken): Promise<void> {
        assert(!this.enabled);

        if (this._auth.useOAuth) {
            // Use OAuth flow
            const session = await this._auth.getSession();
            if (!session) {
                this._logger.info("Auth session not found. Please log in.");
                return;
            }
        } else {
            // Use API token
            this._logger.info("Using API token");
            if (!this._augmentConfigListener.config.apiToken) {
                this._logger.warn("No API token is configured");
                return;
            }

            if (!this._augmentConfigListener.config.completionURL) {
                this._logger.warn("No completion URL is configured");
                return;
            }
        }

        let modelConfig: ModelConfig;
        try {
            modelConfig = await this._getModelConfig(cancelToken);
            if (modelConfig.models.length === 0) {
                throw new NoModelsError();
            }
            this._defaultModel = modelConfig.defaultModel;
            this._languages = modelConfig.languages;
            this._availableModels = modelConfig.models.map((m) => `${m.name} - ${m.internalName}`);
            // Extract the current model info from the config
            const config = this._augmentConfigListener.config;
            const modelName = config.modelName || modelConfig.defaultModel;
            this._modelInfo = modelConfig.models.find(
                (model) =>
                    [model.name, model.internalName].includes(modelName) ||
                    model.name === createHash("sha256").update(modelName).digest("hex")
            );
            if (this._modelInfo === undefined) {
                throw new UnknownModelError(modelName);
            }
            // Extract feature flags from the config
            this.featureFlagManager.update(modelConfig.featureFlags);
        } catch (e: any) {
            if (APIError.isAPIErrorWithStatus(e, APIStatus.unauthenticated)) {
                return;
            } else if (e instanceof InvalidCompletionURLError) {
                return;
            } else if (e instanceof vscode.CancellationError) {
                // If we cancelled the enablement of the extension, do nothing.
                return;
            }

            const msg = getErrmsg(e);
            this._logger.error(`Failed to get model config: ${msg}`);
            throw e;
        }

        this._completionServer = new CompletionServer(
            this._apiServer,
            this._modelInfo.completionTimeoutMs,
            this._modelInfo.suggestedPrefixCharCount,
            this._modelInfo.suggestedSuffixCharCount
        );

        const maxUploadSizeBytes = this.featureFlagManager.currentFlags.maxUploadSizeBytes;
        this._blobNameCalculator = new BlobNameCalculator(maxUploadSizeBytes);

        this.workspaceManager = new WorkspaceManager(
            this._extensionContext,
            this._apiServer,
            this._augmentConfigListener,
            this.featureFlagManager,
            this._clientMetricsReporter,
            this._completionServer,
            this._blobNameCalculator,
            maxUploadSizeBytes,
            modelConfig.languages
        );
        this.disposeOnDisable.push(this.workspaceManager);

        // Start background tasks.
        {
            // Start background metrics uploading and arrange to stop it if we get disposed.
            const reporters = [this._completionAcceptanceReporter];
            for (const r of reporters) {
                r.enableUpload();
                this.disposeOnDisable.push(r);
            }
        }

        this.enabled = true;
    }

    private async _fetchFeatureFlags(
        cancelToken: vscode.CancellationToken
    ): Promise<FeatureFlags | undefined> {
        try {
            const modelConfig = await this._getModelConfig(cancelToken);
            return modelConfig.featureFlags;
        } catch (e) {
            this._logger.error("Failed to fetch feature flags: ", e);
            return undefined;
        }
    }

    updateModelInfo(result: CompletionResult): void {
        if (!this._modelInfo) {
            throw new Error("Model info not set");
        }
        if (result.suggestedPrefixCharCount !== undefined) {
            this._modelInfo.suggestedPrefixCharCount = result.suggestedPrefixCharCount;
        }
        if (result.suggestedSuffixCharCount !== undefined) {
            this._modelInfo.suggestedSuffixCharCount = result.suggestedSuffixCharCount;
        }
        this._modelInfo.completionTimeoutMs = result.completionTimeoutMs;
    }

    // _getModelConfig returns the model configuration retrieved from the back end.
    // On error it retries, with backoff, until it succeeds or is cancelled by the
    // given cancel token. In the latter case, it throws vscode.CancellationError.
    private async _getModelConfig(cancelToken: vscode.CancellationToken): Promise<ModelConfig> {
        let backoffMsec = 1000;
        let modelConfig: ModelConfig | undefined;
        let failureCount = 0;
        const consecutiveFailureLimit = 6;
        // const stateController = new StateController(this._statusBar);
        try {
            while (true) {
                if (cancelToken.isCancellationRequested) {
                    throw new vscode.CancellationError();
                }

                try {
                    this._logger.info("Retrieving model config");
                    modelConfig = await this._apiServer.getModelConfig();
                    this._logger.info("Retrieved model config");
                } catch (err) {
                    // Retry any error, after back-off, until we succeed or get cancelled.
                    this._logger.error("Failed to retrieve model config: ", err);
                    if (APIError.isAPIErrorWithStatus(err, APIStatus.unauthenticated)) {
                        // If the user is unauthenticated, we shouldn't retry.
                        throw err;
                    } else if (err instanceof InvalidCompletionURLError) {
                        // If we know the config is invalid, there is no point in
                        // retrying.
                        throw err;
                    }

                    failureCount++;
                }

                if (cancelToken.isCancellationRequested) {
                    this._logger.info("Model config retrieval cancelled");
                    throw new vscode.CancellationError();
                }
                if (modelConfig !== undefined) {
                    this._logger.info(`Returning model config`);
                    return modelConfig;
                }

                if (failureCount >= consecutiveFailureLimit) {
                    this._logger.warn("Model config retrieval failed");
                    // stateController.setState(statusbarStates.getModelConfigFailed);
                }

                this._logger.info(`Retrying model config retrieval in ${backoffMsec} msec`);
                await delayMs(backoffMsec);
                backoffMsec = Math.min(backoffMsec * 2, AugmentExtension.modelConfigBackoffMsecMax);
            }
        } finally {
            // stateController.dispose();
        }
    }

    // disable() closes all existing roots and cancels all pending roots. New roots
    // cannot be added until the extension has been reenabled.
    public disable(): void {
        this.enabled = false;

        while (this.disposeOnDisable.length) {
            const disp = this.disposeOnDisable.pop();
            disp!.dispose();
        }
        this.reset();
    }

    // reset() closes all context roots and cancels any in-progress completion. It
    // does not disable the extension.
    public reset(): void {
        this._enableCancel?.cancel();
        this._enableCancel?.dispose();
        this._enableCancel = undefined;

        this.workspaceManager?.dispose();
        this.workspaceManager = undefined;
    }
}

/**
 * Get the session ID associated with a particular VSCode extension instance
 * @param context VSCode extension context
 * @returns Session ID associated with the vscode instance
 */
export function getSessionId(globalState: AugmentGlobalState): string {
    let sessionId = globalState.get<string>(GlobalContextKeys.sessionId);
    if (sessionId === undefined || !uuidValidate(sessionId)) {
        sessionId = uuidv4();
        void globalState.update(GlobalContextKeys.sessionId, sessionId);
    }
    return sessionId;
}
