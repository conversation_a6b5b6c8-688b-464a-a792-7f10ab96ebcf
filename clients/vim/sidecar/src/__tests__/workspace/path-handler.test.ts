import { FileReader } from "../../file-reader";
import { FileType } from "../../utils/types";
import { StatInfo } from "../../utils/types";
import { PathHandlerImpl } from "../../workspace/path-handler";
import { ContentType, PathHandler, PathType } from "../../workspace/types";
import { verifyDefined } from "../__utils__/test-utils";

type DirEntry =
    | {
          mtime: number;
          type: FileType.directory;
      }
    | {
          mtime: number;
          type: FileType.file;
          contents: Uint8Array;
      };

class PathHandlerTestKit implements FileReader {
    public static readonly maxBlobSize = 20;
    public fileSystem = new Map<string, DirEntry>();
    public textDecoder = new TextDecoder();

    constructor(files: Map<string, DirEntry> = new Map()) {
        this.fileSystem = new Map(files);
    }

    public makePathHandler(): PathHandler {
        return new PathHandlerImpl(PathHandlerTestKit.maxBlobSize, this);
    }

    public async read(absPath: string): Promise<Uint8Array | undefined> {
        const entry = this.fileSystem.get(absPath);
        if (entry === undefined) {
            return undefined;
        }
        if (entry.type === FileType.directory) {
            throw new Error("Not a file");
        }
        return Buffer.from(entry.contents);
    }

    public stat(absPath: string): StatInfo | undefined {
        const entry = this.fileSystem.get(absPath);
        if (entry === undefined) {
            return undefined;
        }
        return {
            size: entry.type === FileType.file ? entry.contents.length : 0,
            type: entry.type,
            mtime: entry.mtime,
        };
    }
}

const ostrichContents = Buffer.from("ostrich");
const partridgeContents = Buffer.from("partridge");
const grizzlyContents = Buffer.from("grizzly");
const bigfileContents = Buffer.from("012345678901234567890123");
const binaryContents = Buffer.from([0xde, 0xad, 0xbe, 0xef]);

const sampleFiles = new Map<string, DirEntry>([
    ["/giraffe/dog", { mtime: 101, type: FileType.directory }],
    ["/giraffe/dog/ostrich.py", { mtime: 102, type: FileType.file, contents: ostrichContents }],
    ["/giraffe/dog/partridge.py", { mtime: 103, type: FileType.file, contents: partridgeContents }],
    ["/whale/grizzly.cpp", { mtime: 104, type: FileType.file, contents: grizzlyContents }],
    ["/bigfile.txt", { mtime: 105, type: FileType.file, contents: bigfileContents }],
    ["/binaryfile.bin", { mtime: 106, type: FileType.file, contents: binaryContents }],
]);

describe("PathHandler", () => {
    let kit: PathHandlerTestKit;
    let pathHandler: PathHandler;

    beforeEach(() => {
        kit = new PathHandlerTestKit(sampleFiles);
        pathHandler = kit.makePathHandler();
    });

    test.each([
        ["/no/such/file", PathType.inaccessible],
        ["/giraffe/dog", PathType.notAFile],
        ["/giraffe/dog/ostrich.py", PathType.accepted],
        ["/giraffe/dog/partridge.py", PathType.accepted],
        ["/whale/grizzly.cpp", PathType.accepted],
        ["/bigfile.txt", PathType.largeFile],
        ["/binaryfile.bin", PathType.accepted],
    ])("classifyPath(%s, %s)", (pathName: string, pathType: PathType) => {
        const pathInfo = pathHandler.classifyPath(pathName);
        expect(pathInfo.type).toBe(pathType);
        if (pathInfo.type === PathType.inaccessible) {
            return;
        }
        const dirEntry = sampleFiles.get(pathName);
        verifyDefined(dirEntry);
        if (pathInfo.type === PathType.accepted || pathInfo.type === PathType.largeFile) {
            expect(dirEntry.type).toBe(FileType.file);
            if (dirEntry.type === FileType.file) {
                expect(pathInfo.size).toBe(dirEntry.contents.length);
            }
        }
        expect(pathInfo.mtime).toBe(dirEntry.mtime);
    });

    test.each([
        ["/no/such/file", ContentType.inaccessible],
        ["/giraffe/dog", ContentType.inaccessible],
        ["/giraffe/dog/ostrich.py", ContentType.text],
        ["/giraffe/dog/partridge.py", ContentType.text],
        ["/whale/grizzly.cpp", ContentType.text],
        ["/bigfile.txt", ContentType.largeFile],
        ["/binaryfile.bin", ContentType.binary],
    ])("readText(%s, %s)", async (pathName: string, contentType: ContentType) => {
        const fileContents = await pathHandler.readText(pathName);
        expect(fileContents.type).toBe(contentType);
        if (fileContents.type === ContentType.inaccessible) {
            return;
        }
        const dirEntry = sampleFiles.get(pathName);
        verifyDefined(dirEntry);
        if (dirEntry.type !== FileType.file) {
            throw new Error("Unexpected dir entry type");
        }
        if (fileContents.type === ContentType.binary) {
            return;
        }
        if (fileContents.type === ContentType.largeFile) {
            expect(fileContents.size).toBe(dirEntry.contents.length);
            return;
        }
        if (fileContents.type === ContentType.text) {
            expect(fileContents.contents).toStrictEqual(dirEntry.contents);
        }
    });
});
