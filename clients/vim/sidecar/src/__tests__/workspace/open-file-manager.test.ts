import { <PERSON>ckAcceptPath } from "../../__mocks__/mock-accept-path";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { MutableTextDocument } from "../../__mocks__/vscode-mocks";
import { BlobMetadata, FindMissingResult } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { CompletionServer } from "../../completion-server";
import { APIError } from "../../exceptions";
import { getLogger } from "../../logging";
import { joinPath } from "../../utils/path-utils";
import { disposableDelayer } from "../../utils/promise-utils";
import { SequenceGenerator } from "../../utils/sequence-generator";
import { FileType } from "../../utils/types";
import { APIStatus } from "../../utils/types";
import * as vscode from "../../vscode";
import { BlobNameCalculator } from "../../workspace/blob-name-calculator";
import { DocumentType } from "../../workspace/document-type";
import {
    ChunkInfo,
    maxTotalChunks,
    OpenFileManager,
    RawChunk,
    RecencySummary,
} from "../../workspace/open-file-manager";
import { PathMap } from "../../workspace/path-map";
import { verifyDefined } from "../__utils__/test-utils";
import { advanceTimeUntilTrue } from "../__utils__/time";

const defaultFiles = new Map<string, string>([
    ["foo.py", "this is foo.py"],
    ["bar.py", "this is bar.py"],
    ["foo.ipynb", "this is foo.ipynb"],
]);

class OpenFileManagerTestKit implements vscode.Disposable {
    static readonly maxBlobSize = 10000;
    static readonly completionTimeoutMs = 1000;
    static readonly prefixSize = 100;
    static readonly suffixSize = 100;
    static readonly chunkSize = 20;
    static readonly acceptPath = new MockAcceptPath();

    public readonly fs = new Map<string, string>();
    public readonly workspaceName = "test workspace";
    public readonly apiServer = new MockAPIServer(OpenFileManagerTestKit.maxBlobSize);
    public readonly completionServer = new CompletionServer(
        this.apiServer,
        OpenFileManagerTestKit.completionTimeoutMs,
        OpenFileManagerTestKit.prefixSize,
        OpenFileManagerTestKit.suffixSize,
        OpenFileManagerTestKit.chunkSize
    );
    public readonly config = new AugmentConfigListener();
    public readonly blobNameCalculator = new BlobNameCalculator(OpenFileManagerTestKit.maxBlobSize);
    public readonly pathMap = new PathMap();
    public readonly openFileManagers = new Array<OpenFileManager>();

    public nextContentSeq = 2000;
    public nextMtime = 12345;

    public readonly logger = getLogger("OpenFileManagerTestKit");

    public dispose() {
        for (const openFileManager of this.openFileManagers.values()) {
            openFileManager.dispose();
        }
        this.openFileManagers.length = 0;
    }

    public get prefixSize() {
        return this.completionServer.completionParams.prefixSize;
    }

    public get chunkSize() {
        return this.completionServer.completionParams.chunkSize;
    }

    public async runTimers() {
        await jest.runOnlyPendingTimersAsync();
    }

    public toAbsPath(folderId: number, relPath: string): string {
        const repoRoot = this.pathMap.getRepoRoot(folderId);
        verifyDefined(repoRoot);
        return joinPath(repoRoot, relPath);
    }

    public createFile(folderId: number, relPath: string, contents: string) {
        const absPath = this.toAbsPath(folderId, relPath);
        this.fs.set(absPath, contents);
        this.pathMap.insert(folderId, relPath, FileType.file, OpenFileManagerTestKit.acceptPath);
        this.pathMap.update(
            folderId,
            relPath,
            this.nextContentSeq++,
            this.blobNameCalculator.calculate(relPath, contents)!,
            this.nextMtime++
        );
    }

    public calculateBlobName(
        folderId: number,
        relPath: string,
        contents?: string
    ): string | undefined {
        const absPath = this.toAbsPath(folderId, relPath);
        const contentsToUse = contents ?? this.fs.get(absPath);
        verifyDefined(contentsToUse);
        return this.blobNameCalculator.calculate(relPath, contentsToUse);
    }

    public makeOpenFileManager(): OpenFileManager {
        const openFileManager = new OpenFileManager(
            this.apiServer,
            this.completionServer,
            this.config,
            this.blobNameCalculator,
            this.pathMap,
            new SequenceGenerator()
        );
        this.openFileManagers.push(openFileManager);
        return openFileManager;
    }

    public verifyEmpty(openFileManager: OpenFileManager) {
        const summary = openFileManager.getRecencySummary(this.chunkSize);
        const totalBlobs = Array.from(summary.folderMap).reduce(
            (total, [_folderId, folder]) => total + folder.size,
            0
        );
        expect(totalBlobs).toBe(0);
    }

    public verifyTracked(
        openFileManager: OpenFileManager,
        folderId: number,
        relPath: string,
        blobName: string
    ) {
        expect(openFileManager.getBlobName(folderId, relPath)).toBe(blobName);
    }

    public verifyNotTracked(openFileManager: OpenFileManager, folderId: number, relPath: string) {
        expect(openFileManager.getBlobName(folderId, relPath)).toBeUndefined();
    }

    // newDocument creates a new MutableTextDocument with the given contents but no underlying file.
    public newDocument(folderId: number, relPath: string, contents: string): MutableTextDocument {
        const absPath = this.toAbsPath(folderId, relPath);
        return new MutableTextDocument(vscode.Uri.file(absPath), contents);
    }

    // openDocument creates a new MutableTextDocument with the contents of the given file.
    public openDocument(folderId: number, relPath: string): MutableTextDocument {
        const absPath = this.toAbsPath(folderId, relPath);
        const contents = this.fs.get(absPath);
        verifyDefined(contents);
        return new MutableTextDocument(vscode.Uri.file(absPath), contents);
    }

    public applyTextDocumentUpdate(
        openFileManager: OpenFileManager,
        folderId: number,
        relPath: string,
        document: MutableTextDocument,
        offset: number,
        text: string,
        charsToDelete = 0
    ): string {
        const editEvent = document.replace(offset, charsToDelete, text);
        openFileManager.applyTextDocumentChange(folderId, relPath, editEvent);
        return this.calculateBlobName(folderId, relPath, document.getText())!;
    }

    public verifyNoUpload() {
        expect(this.apiServer.memorize).not.toHaveBeenCalled();
    }

    public verifyUpload(folderId: number, relPath: string, document: vscode.TextDocument) {
        const blobName = this.calculateBlobName(folderId, relPath, document.getText())!;
        expect(this.apiServer.getBlobName(relPath)).toBe(blobName);
    }

    public async awaitIndexedBlobName(
        openFileManager: OpenFileManager,
        folderId: number,
        relPath: string,
        blobName: string
    ) {
        while (openFileManager.getBlobName(folderId, relPath) !== blobName) {
            await this.runTimers();
        }
    }

    public async forceUploadAndIndex(
        openFileManager: OpenFileManager,
        folderId: number,
        relPath: string,
        blobName: string
    ) {
        openFileManager.loseFocus();
        await this.awaitIndexedBlobName(openFileManager, folderId, relPath, blobName);
    }

    public getRecencySummary(openFileManager: OpenFileManager): RecencySummary {
        return openFileManager.getRecencySummary(this.chunkSize);
    }

    public getRecentChunkInfo(
        openFileManager: OpenFileManager,
        includeStale = false
    ): Array<ChunkInfo> {
        return openFileManager.getRecentChunkInfo(this.chunkSize, includeStale);
    }

    public verifyChunkOrdering(chunks: RawChunk[]) {
        let lastSeq: number | undefined = undefined;
        let uploaded = false;
        for (const chunk of chunks) {
            if (uploaded) {
                expect(chunk.uploaded).toBe(true);
            } else if (chunk.uploaded) {
                // Reset lastSeq when we see the first uploaded chunk, as the sedquence order
                // restarts at that point.
                lastSeq = undefined;
            }
            uploaded = chunk.uploaded;
            if (lastSeq === undefined) {
                lastSeq = chunk.seq;
            } else {
                expect(chunk.seq).toBeLessThan(lastSeq);
            }
            lastSeq = chunk.seq;
        }
    }

    public verifyReconstruct(
        uploadedBlobName: string,
        uploadedText: string,
        chunks: RawChunk[],
        currentText: string
    ) {
        // Verify that we can construct the final text by applying the non-uploaded chunks to
        // the starting text.
        const constructedText = this.constructDocument(uploadedBlobName, uploadedText, chunks);
        expect(constructedText).toBe(currentText);

        // Verify that every non-uploaded chunk actually contains changes that are not present
        // in the starting text by omitting each one in turn and verifying that the resulting
        // constructed text does not match the final text.
        if (currentText !== uploadedText) {
            for (let idx = 0; idx < chunks.length; idx++) {
                if (chunks[idx].blobName !== uploadedBlobName || chunks[idx].uploaded) {
                    continue;
                }
                const constructedText = this.constructDocument(
                    uploadedBlobName,
                    uploadedText,
                    chunks,
                    idx
                );
                expect(constructedText).not.toBe(currentText);
            }
        }
    }

    public constructDocument(
        blobName: string,
        initialText: string,
        chunks: RawChunk[],
        skipIdx?: number
    ): string {
        let sortedChunks: RawChunk[];
        if (skipIdx === undefined) {
            sortedChunks = chunks.slice();
        } else {
            sortedChunks = chunks.slice(0, skipIdx).concat(chunks.slice(skipIdx + 1));
        }
        sortedChunks.sort((a, b) => a.origStart - b.origStart);
        let text = "";
        let offset = 0;
        for (let idx = 0; idx < sortedChunks.length; idx++) {
            if (skipIdx === idx) {
                continue;
            }
            const chunk = sortedChunks[idx];
            if (chunk.blobName !== blobName) {
                continue;
            }
            if (chunk.uploaded) {
                continue;
            }
            text += initialText.slice(offset, chunk.origStart) + chunk.text;
            offset = chunk.origStart + chunk.origLength;
        }
        text += initialText.slice(offset);
        return text;
    }
}

function createRepoFiles(
    kit: OpenFileManagerTestKit,
    folderId: number,
    files: Map<string, string> = defaultFiles
): void {
    for (const [pathName, contents] of files) {
        kit.createFile(folderId, pathName, contents);
    }
}

describe("OpenFileManager tracking and upload", () => {
    const repoRoot = "/src/repoRoot";

    let kit: OpenFileManagerTestKit;
    let openFileManager: OpenFileManager;
    let folderId: number;

    beforeEach(() => {
        jest.useFakeTimers();
        kit = new OpenFileManagerTestKit();
        folderId = kit.pathMap.openSourceFolder(repoRoot, repoRoot);
        createRepoFiles(kit, folderId);
        openFileManager = kit.makeOpenFileManager();
        openFileManager.openSourceFolder(folderId);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("start and stop tracking", () => {
        const relPath = "foo.py";

        kit.verifyEmpty(openFileManager);
        kit.verifyNotTracked(openFileManager, folderId, relPath);

        const document = kit.openDocument(folderId, relPath);
        const blobName = kit.calculateBlobName(folderId, relPath)!;

        openFileManager.startTracking(folderId, relPath, document);
        kit.verifyTracked(openFileManager, folderId, relPath, blobName);

        openFileManager.stopTracking(folderId, relPath, DocumentType.text);
        kit.verifyNotTracked(openFileManager, folderId, relPath);
    });

    test("get tracked paths", () => {
        const relPath1 = "foo.py";
        const relPath2 = "bar.py";

        const document1 = kit.openDocument(folderId, relPath1);
        const document2 = kit.openDocument(folderId, relPath2);
        let trackedPaths = openFileManager.getTrackedPaths(folderId);
        expect(trackedPaths.length).toBe(0);

        openFileManager.startTracking(folderId, relPath1, document1);
        trackedPaths = openFileManager.getTrackedPaths(folderId);
        expect(trackedPaths.length).toBe(1);
        expect(trackedPaths).toContain(relPath1);

        openFileManager.startTracking(folderId, relPath2, document2);
        trackedPaths = openFileManager.getTrackedPaths(folderId);
        expect(trackedPaths.length).toBe(2);
        expect(trackedPaths).toContain(relPath1);
        expect(trackedPaths).toContain(relPath2);

        openFileManager.stopTracking(folderId, relPath1, DocumentType.text);
        trackedPaths = openFileManager.getTrackedPaths(folderId);
        expect(trackedPaths.length).toBe(1);
        expect(trackedPaths).toContain(relPath2);

        openFileManager.stopTracking(folderId, relPath2, DocumentType.text);
        trackedPaths = openFileManager.getTrackedPaths(folderId);
        expect(trackedPaths.length).toBe(0);
    });

    test("upload when change focus", () => {
        const relPath1 = "foo.py";
        const relPath2 = "bar.py";

        const document1 = kit.openDocument(folderId, relPath1);
        openFileManager.startTracking(folderId, relPath1, document1);

        kit.applyTextDocumentUpdate(openFileManager, folderId, relPath1, document1, 0, "hello");
        kit.verifyNoUpload();

        // Switch to a different document. Should trigger upload of the first one.
        const document2 = kit.openDocument(folderId, relPath2);
        openFileManager.startTracking(folderId, relPath2, document2);
        kit.verifyUpload(folderId, relPath1, document1);
    });

    test("upload when edit far away", () => {
        const relPath = "long.py";
        kit.createFile(folderId, relPath, "a".repeat(kit.prefixSize * 2));

        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);

        const insertOffset1 = 0;
        const insertText1 = "xyz";
        kit.applyTextDocumentUpdate(
            openFileManager,
            folderId,
            relPath,
            document,
            insertOffset1,
            insertText1
        );
        kit.verifyNoUpload();

        // Do a second insert more than prefixSize characters away from the first insert. Should
        // trigger upload.
        const insertOffset2 = insertOffset1 + kit.prefixSize + insertText1.length + 1;
        const insertText2 = "pdq";
        kit.applyTextDocumentUpdate(
            openFileManager,
            folderId,
            relPath,
            document,
            insertOffset2,
            insertText2
        );
        kit.verifyUpload(folderId, relPath, document);
    });

    test("upload when focus lost", () => {
        const relPath = "foo.py";

        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);

        kit.applyTextDocumentUpdate(openFileManager, folderId, relPath, document, 0, "hello");
        kit.verifyNoUpload();

        // Tell the blob manager that the document no longer has focus. Should trigger upload.
        openFileManager.loseFocus();
        kit.verifyUpload(folderId, relPath, document);
    });

    test("upload after large change", () => {
        const relPath = "foo.py";

        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);
        kit.verifyNoUpload();

        // Insert text that is larger than the prefix size. Should trigger upload.
        const insertText = "X".repeat(kit.prefixSize + 1);
        kit.applyTextDocumentUpdate(openFileManager, folderId, relPath, document, 0, insertText);
        kit.verifyUpload(folderId, relPath, document);
    });

    test("upload when blob name not already known", () => {
        const relPath = "nonexistent.py";

        const document = kit.newDocument(folderId, relPath, "hello");
        openFileManager.startTracking(folderId, relPath, document);
        kit.verifyUpload(folderId, relPath, document);
    });

    test("report only indexed blob names", async () => {
        const relPath = "foo.py";
        const origBlobName = kit.calculateBlobName(folderId, relPath)!;

        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);
        expect(openFileManager.getBlobName(folderId, relPath)).toBe(origBlobName);

        const editEvent = document.insert(0, "hello");
        const newBlobName = kit.calculateBlobName(folderId, relPath, document.getText())!;

        // Make find-missing report the new blob name as nonindexed.
        let findMissingCount = 0;
        kit.apiServer.findMissing.mockImplementation(
            async (blobNames: string[]): Promise<FindMissingResult> => {
                expect(blobNames).toEqual([newBlobName]);
                findMissingCount++;
                return {
                    unknownBlobNames: [],
                    nonindexedBlobNames: [newBlobName],
                };
            }
        );

        // Apply the edit and trigger upload.
        openFileManager.applyTextDocumentChange(folderId, relPath, editEvent);
        openFileManager.loseFocus();

        // Wait for upload and find-missing. Even though the upload has completed, the blob manager
        // should continue to return the old blob name as long as find-missing reports the blob
        // name as nonindexed.
        while (findMissingCount === 0) {
            await kit.runTimers();
        }
        expect(kit.apiServer.getBlobName(relPath)).toBe(newBlobName);
        expect(openFileManager.getBlobName(folderId, relPath)).toBe(origBlobName);

        // Make find-missing report the new blob name as indexed.
        kit.apiServer.findMissing.mockImplementation(
            async (blobNames: string[]): Promise<FindMissingResult> => {
                expect(blobNames).toEqual([newBlobName]);
                return {
                    unknownBlobNames: [],
                    nonindexedBlobNames: [],
                };
            }
        );

        // The blob manager should eventually return the new blob name.
        await kit.awaitIndexedBlobName(openFileManager, folderId, relPath, newBlobName);
    });

    test("embargo large files", async () => {
        const relPath = "file.py";
        kit.createFile(folderId, relPath, "x".repeat(OpenFileManagerTestKit.maxBlobSize + 10));
        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);

        kit.verifyNotTracked(openFileManager, folderId, relPath);

        // Editing the file should not change the embargoed state.
        kit.applyTextDocumentUpdate(openFileManager, folderId, relPath, document, 0, "x");
        kit.verifyNotTracked(openFileManager, folderId, relPath);
    });

    test("embargo files that grow large", async () => {
        const relPath = "file.py";
        kit.createFile(folderId, relPath, "x".repeat(OpenFileManagerTestKit.maxBlobSize - 10));
        const document = kit.openDocument(folderId, relPath);
        const origBlobName = kit.calculateBlobName(folderId, relPath)!;
        openFileManager.startTracking(folderId, relPath, document);
        await kit.forceUploadAndIndex(openFileManager, folderId, relPath, origBlobName);
        expect(openFileManager.getBlobName(folderId, relPath)).toBe(origBlobName);

        const newBlobName = kit.applyTextDocumentUpdate(
            openFileManager,
            folderId,
            relPath,
            document,
            document.getText().length,
            "x".repeat(20)
        );

        expect(newBlobName).toBeUndefined();
        openFileManager.loseFocus();
        while (openFileManager.getBlobName(folderId, relPath) === origBlobName) {
            await kit.runTimers();
        }

        kit.verifyNotTracked(openFileManager, folderId, relPath);

        // "startTracking" and editing should not change the embargoed state.
        openFileManager.startTracking(folderId, relPath, document);
        kit.verifyNotTracked(openFileManager, folderId, relPath);

        kit.applyTextDocumentUpdate(openFileManager, folderId, relPath, document, 0, "x");
        kit.verifyNotTracked(openFileManager, folderId, relPath);
    });

    // Verify that OpenFileManager retries uploads that fail with a cancel error.
    test("retry upload on cancel", async () => {
        const relPath = "file.py";
        const maxRetries = 4;

        // Make the first `maxRetries` upload attempts fail with a cancel error.
        let retryCount = 0;
        let uploadSucceeded = false;
        kit.apiServer.memorize = jest.fn(
            async (
                uploadedPathName: string,
                _text: string,
                blobName: string,
                _metadata: BlobMetadata,
                _timeoutMs?: number
            ) => {
                if (uploadedPathName === relPath) {
                    if (retryCount < maxRetries) {
                        retryCount++;
                        throw new APIError(APIStatus.cancelled, "mock timeout");
                    }
                }
                uploadSucceeded = true;
                return { blobName };
            }
        );

        // Create a file and start tracking it.
        kit.createFile(folderId, relPath, "x".repeat(100));
        const document = kit.openDocument(folderId, relPath);
        expect(retryCount).toBe(0);
        openFileManager.startTracking(folderId, relPath, document);

        // Trigger an upload.
        kit.applyTextDocumentUpdate(openFileManager, folderId, relPath, document, 0, "insertText");
        openFileManager.loseFocus();

        // The upload should succeed after `maxRetries` retries.
        await advanceTimeUntilTrue(() => uploadSucceeded);
        expect(retryCount).toBe(maxRetries);
    });
});

describe("OpenFileManager translate range", () => {
    const repoRoot = "/src/repoRoot";

    let kit: OpenFileManagerTestKit;
    let openFileManager: OpenFileManager;
    let folderId: number;

    beforeEach(() => {
        kit = new OpenFileManagerTestKit();
        folderId = kit.pathMap.openSourceFolder(repoRoot, repoRoot);
        createRepoFiles(kit, folderId);
        openFileManager = kit.makeOpenFileManager();
        openFileManager.openSourceFolder(folderId);
    });

    afterEach(() => {
        kit.dispose();
    });

    test.each([
        [10, 30, 10, 30],
        [10, 50, 10, 50],
        [10, 52, 10, 50],
        [10, 60, 10, 55],
        [50, 60, 50, 55],
        [52, 60, 50, 55],
        [60, 70, 55, 65],
    ])(
        "translate range with insert(%i, %i)",
        (beginOffset: number, endOffset: number, expectedBegin: number, expectedEnd: number) => {
            const relPath = "file.py";
            kit.createFile(folderId, relPath, "x".repeat(100));
            const document = kit.openDocument(folderId, relPath);
            openFileManager.startTracking(folderId, relPath, document);
            const blobName = openFileManager.getBlobName(folderId, relPath);
            kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                50,
                "abcde",
                0
            );

            let documentRange = { folderId, relPath, beginOffset, endOffset };
            const blobRange = openFileManager.translateRange(documentRange);
            expect(blobRange).toEqual({
                blobName,
                beginOffset: expectedBegin,
                endOffset: expectedEnd,
            });
        }
    );

    test.each([
        [10, 30, 10, 30],
        [10, 50, 10, 55],
        [10, 60, 10, 65],
        [50, 60, 50, 65],
        [60, 70, 65, 75],
    ])(
        "translate range with delete(%i, %i)",
        (beginOffset: number, endOffset: number, expectedBegin: number, expectedEnd: number) => {
            const relPath = "file.py";
            kit.createFile(folderId, relPath, "x".repeat(100));
            const document = kit.openDocument(folderId, relPath);
            openFileManager.startTracking(folderId, relPath, document);
            const blobName = openFileManager.getBlobName(folderId, relPath);
            kit.applyTextDocumentUpdate(openFileManager, folderId, relPath, document, 50, "", 5);

            let documentRange = { folderId, relPath, beginOffset, endOffset };
            const blobRange = openFileManager.translateRange(documentRange);
            expect(blobRange).toEqual({
                blobName,
                beginOffset: expectedBegin,
                endOffset: expectedEnd,
            });
        }
    );
});

describe("OpenFileManager recency summary", () => {
    const repoRoot = "/src/repoRoot";

    let kit: OpenFileManagerTestKit;
    let openFileManager: OpenFileManager;
    let folderId: number;

    beforeEach(() => {
        jest.useFakeTimers();
        kit = new OpenFileManagerTestKit();
        folderId = kit.pathMap.openSourceFolder(repoRoot, repoRoot);
        createRepoFiles(kit, folderId);
        openFileManager = kit.makeOpenFileManager();
        openFileManager.openSourceFolder(folderId);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("no recent changes", () => {
        const relPath = "foo.py";

        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);

        const summary = kit.getRecencySummary(openFileManager);
        expect(summary.recentChunks.length).toBe(0);
        expect(summary.folderMap.get(folderId)!.keys()).toContain(relPath);
    });

    test.each([
        [[10], 1],
        [[10, 15], 1],
        [[10, 40], 2],
        [[10, 15, 40, 45], 2],
    ])("recent chunks[%#]", (locations: number[], expectedChunkCount: number) => {
        const relPath = "file0.py";
        const documentSize = 200;

        const contents = "x".repeat(documentSize);
        kit.createFile(folderId, relPath, contents);
        const document = kit.openDocument(folderId, relPath);
        const origText = document.getText();
        const origBlobName = kit.calculateBlobName(folderId, relPath, origText)!;
        openFileManager.startTracking(folderId, relPath, document);

        for (let idx = 0; idx < locations.length; idx++) {
            const location = locations[idx];
            kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                location,
                `${idx}`
            );
        }
        const newText = document.getText();

        // Verify that the recency summary contains the expected number of chunks.
        const summary = kit.getRecencySummary(openFileManager);
        expect(summary.recentChunks.length).toBe(expectedChunkCount);

        // Verify chunk ordering and metadata.
        kit.verifyChunkOrdering(summary.recentChunks);
        for (const chunk of summary.recentChunks) {
            expect(chunk.pathName).toBe(relPath);
            expect(chunk.blobName).toBe(origBlobName);
        }
        kit.verifyReconstruct(origBlobName, origText, summary.recentChunks, newText);
    });

    test.each([undefined, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11])(
        "ordered chunks(%i)",
        async (uploadIdx: number | undefined) => {
            const documentSize = 100;
            const files = [
                ["file0.py", "x"],
                ["file1.py", "y"],
                ["file2.py", "z"],
            ];

            // relPath, contents, blobName, document
            const fileInfo = new Array<[string, string, string, MutableTextDocument]>();
            for (const [relPath, contentChar] of files) {
                const text = contentChar.repeat(documentSize);
                kit.createFile(folderId, relPath, text);
                const document = kit.openDocument(folderId, relPath);
                openFileManager.startTracking(folderId, relPath, document);
                const origBlobName = kit.calculateBlobName(folderId, relPath, text)!;
                fileInfo.push([relPath, text, origBlobName, document]);
            }

            const updates = [
                [2, 40],
                [1, 15],
                [0, 10],
                [1, 40],
                [0, 45],
                [1, 10],
                [2, 45],
                [1, 45],
                [0, 15],
                [2, 10],
                [2, 15],
                [0, 40],
            ];

            for (let idx = 0; idx < updates.length; idx++) {
                const [fileIdx, offset] = updates[idx];
                const relPath = fileInfo[fileIdx][0];
                const document = fileInfo[fileIdx][3];
                const toInsert = `${idx}`;
                kit.applyTextDocumentUpdate(
                    openFileManager,
                    folderId,
                    relPath,
                    document,
                    offset,
                    toInsert,
                    toInsert.length
                );

                if (uploadIdx === idx) {
                    for (const file of fileInfo) {
                        const [relPath, _, _origBlobName, document] = file;
                        const newBlobName = kit.calculateBlobName(
                            folderId,
                            relPath,
                            document.getText()
                        )!;
                        openFileManager.loseFocus();
                        await kit.awaitIndexedBlobName(
                            openFileManager,
                            folderId,
                            relPath,
                            newBlobName
                        );
                        file[1] = document.getText();
                        file[2] = newBlobName;
                    }
                }
            }
            for (const file of fileInfo) {
                const [relPath, _0, blobName, _1] = file;
                expect(openFileManager.getBlobName(folderId, relPath)).toBe(blobName);
            }

            const summary = kit.getRecencySummary(openFileManager);
            expect(summary.recentChunks.length).toBe(6);
            kit.verifyChunkOrdering(summary.recentChunks);

            for (const [_relPath, uploadedContents, uploadedBlobName, document] of fileInfo) {
                kit.verifyReconstruct(
                    uploadedBlobName,
                    uploadedContents,
                    summary.recentChunks,
                    document.getText()
                );
            }
        }
    );

    // Verify that updates and competions that happen while an upload is in progress behave
    // correctly.
    test("updates and completions during upload", async () => {
        const rangeSize = kit.chunkSize + 10;
        const rangeCount = 10;
        const documentSize = rangeSize * rangeCount;

        const relPath = "file0.py";
        kit.createFile(folderId, relPath, "x".repeat(documentSize));
        const document = kit.openDocument(folderId, relPath);
        const origText = document.getText();
        const origBlobName = kit.calculateBlobName(folderId, relPath, origText)!;
        openFileManager.startTracking(folderId, relPath, document);

        // Delay the upload of a `delayBlobName`
        let delayBlobName: string | undefined = undefined;
        let [promise, delayDispose] = disposableDelayer();
        let delayBlobWaiting = false;
        kit.apiServer.memorize.mockImplementation(
            async (
                _relPath: string,
                _text: string,
                blobName: string,
                _metadata: BlobMetadata,
                _timeoutMs?: number
            ) => {
                if (blobName === delayBlobName) {
                    delayBlobWaiting = true;
                    await promise;
                }
                return { blobName };
            }
        );
        kit.apiServer.findMissing.mockImplementation(
            async (_: string[]): Promise<FindMissingResult> => {
                return {
                    unknownBlobNames: [],
                    nonindexedBlobNames: [],
                };
            }
        );

        // Make an update in the middle of the file. Choose an odd-numbered range, because we
        // will be updating the even ranges below. Set `delayBlobName` to force a delay in the
        // upload of this blob.
        const initialRange = 5;
        const initialOffset = initialRange * rangeSize;
        const toReplace = "AAA";
        const editEvent = document.replace(initialOffset, 0, toReplace);
        const uploadedText1 = document.getText();
        const uploadedBlobName1 = kit.calculateBlobName(folderId, relPath, uploadedText1)!;
        delayBlobName = uploadedBlobName1;
        openFileManager.applyTextDocumentChange(folderId, relPath, editEvent);

        // Force an upload of the blob and wait for the upload request to reach the api server.
        openFileManager.loseFocus();
        while (!delayBlobWaiting) {
            await kit.runTimers();
        }

        // With the upload in progress, update the even-numbered ranges.
        for (let idx = 0; idx < rangeCount; idx += 2) {
            const toReplace = `${idx}`;
            kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                idx * rangeSize,
                toReplace,
                0
            );
        }

        // With the upload in progress, verify the recenct chunks.
        const recencySummaryDuringUpload = kit.getRecencySummary(openFileManager);
        expect(recencySummaryDuringUpload.recentChunks.length).toBe(rangeCount / 2 + 1);
        kit.verifyReconstruct(
            origBlobName,
            origText,
            recencySummaryDuringUpload.recentChunks,
            document.getText()
        );

        // Allow the upload to complete and wait for the blob name to be reported by the
        // path map.
        const uploadedText2 = document.getText();
        const uploadedBlobName2 = kit.calculateBlobName(folderId, relPath, uploadedText2)!;
        openFileManager.loseFocus();
        delayDispose.dispose();
        while (openFileManager.getBlobName(folderId, relPath) !== uploadedBlobName2) {
            await kit.runTimers();
        }

        // The post-upload recency summary should be the same as the during-upload summary, but
        // with the chunks reported as uploaded and with the updated blob name.
        const recencySummaryPostUpload = kit.getRecencySummary(openFileManager);
        expect(recencySummaryPostUpload.recentChunks.length).toBe(
            recencySummaryDuringUpload.recentChunks.length
        );
        let duringUploadChunks = recencySummaryDuringUpload.recentChunks.slice();
        let postUploadChunks = recencySummaryPostUpload.recentChunks.slice();
        duringUploadChunks.sort((a, b) => a.origStart - b.origStart);
        postUploadChunks.sort((a, b) => a.origStart - b.origStart);
        let offset = 0;
        for (let idx = 0; idx < duringUploadChunks.length; idx++) {
            const duringUploadChunk = duringUploadChunks[idx];
            const postUploadChunk = postUploadChunks[idx];

            expect(postUploadChunk.uploaded).toBe(true);
            expect(postUploadChunk.blobName).toBe(uploadedBlobName2);
            expect(postUploadChunk.origStart).toBe(duringUploadChunk.origStart + offset);
            offset += postUploadChunk.origLength - duringUploadChunk.origLength;
        }
    });

    // Verify that the blob manager doesn't accumulate an unbounded number of "recent" chunks from
    // a single file.
    test("gc chunks, single file", async () => {
        const rangeSize = kit.chunkSize + 10;
        const rangeCount = maxTotalChunks * 4;
        const documentSize = rangeSize * rangeCount;

        const relPath = "file0.py";
        kit.createFile(folderId, relPath, "x".repeat(documentSize));
        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);

        let chunkCountHWM = 0;
        for (let idx = 0; idx < rangeCount; idx++) {
            const toReplace = `${idx}`;
            const newBlobName = kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                idx * rangeSize,
                toReplace,
                toReplace.length
            );
            await kit.forceUploadAndIndex(openFileManager, folderId, relPath, newBlobName);

            const recencySummary = kit.getRecencySummary(openFileManager);
            expect(recencySummary.recentChunks.length).toBeGreaterThan(0);
            expect(recencySummary.recentChunks.length).toBeLessThanOrEqual(maxTotalChunks);
            chunkCountHWM = Math.max(chunkCountHWM, recencySummary.recentChunks.length);
        }
        expect(chunkCountHWM).toBeGreaterThanOrEqual(maxTotalChunks - 1);
        expect(chunkCountHWM).toBeLessThanOrEqual(maxTotalChunks);
    });

    // Verify that the blob manager doesn't accumulate an unbounded number of "recent" chunks
    // from a single file, even if it is not able to complete a successful upload of the file.
    test("uploads stuck, single file", async () => {
        const rangeSize = kit.chunkSize + 10;
        const rangeCount = maxTotalChunks * 4;
        const documentSize = rangeSize * rangeCount;

        kit.apiServer.delayMemorize();

        const relPath = "file0.py";
        kit.createFile(folderId, relPath, "x".repeat(documentSize));
        const document = kit.openDocument(folderId, relPath);
        openFileManager.startTracking(folderId, relPath, document);

        let chunkCountHWM = 0;
        for (let idx = 0; idx < rangeCount; idx++) {
            const toReplace = `${idx}`;
            kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                idx * rangeSize,
                toReplace,
                toReplace.length
            );

            const chunkInfo = kit.getRecentChunkInfo(openFileManager, true);
            expect(chunkInfo.length).toBeGreaterThan(0);
            expect(chunkInfo.length).toBeLessThanOrEqual(maxTotalChunks * 2);
            chunkCountHWM = Math.max(chunkCountHWM, chunkInfo.length);
        }
        expect(chunkCountHWM).toBeLessThanOrEqual(maxTotalChunks * 2);
    });

    // Verifies that the blob manager doesn't accumulate an unbounded number of "recent" chunks
    // from multiple files.
    test("gc chunks, multi file", async () => {
        const documentSize = 10;

        let chunkCountHWM = 0;
        for (let idx = 0; idx < 400; idx++) {
            const relPath = `file${idx}.py`;
            kit.createFile(folderId, relPath, "x".repeat(documentSize));
            const document = kit.openDocument(folderId, relPath);
            openFileManager.startTracking(folderId, relPath, document);

            const newBlobName = kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                documentSize / 2,
                "a",
                1
            );
            await kit.forceUploadAndIndex(openFileManager, folderId, relPath, newBlobName);

            const recencySummary = kit.getRecencySummary(openFileManager);
            expect(recencySummary.recentChunks.length).toBeGreaterThan(0);
            chunkCountHWM = Math.max(chunkCountHWM, recencySummary.recentChunks.length);
        }
        expect(chunkCountHWM).toBe(maxTotalChunks);
    });

    // Verify that the blob manager doesn't accumulate an unbounded number of "recent" chunks
    // across multiple files, even if it is not able to complete any successful uploads.
    test("uploads stuck, multi file", async () => {
        const documentSize = 10;

        kit.apiServer.delayMemorize();

        let chunkCountHWM = 0;
        for (let idx = 0; idx < 400; idx++) {
            const relPath = `file${idx}.py`;
            kit.createFile(folderId, relPath, "x".repeat(documentSize));
            const document = kit.openDocument(folderId, relPath);
            openFileManager.startTracking(folderId, relPath, document);

            kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                documentSize / 2,
                "a",
                1
            );

            const recencySummary = kit.getRecencySummary(openFileManager);
            expect(recencySummary.recentChunks.length).toBeGreaterThan(0);
            chunkCountHWM = Math.max(chunkCountHWM, recencySummary.recentChunks.length);
        }
        expect(chunkCountHWM).toBeLessThanOrEqual(maxTotalChunks * 2);
    });

    // Verifiy that the blob manager will eventually stop tracking updates to a file if it is
    // unable to complete an upload, and that it will resume once uploads are successful.
    test("recover from connectivity loss", async () => {
        const rangeSize = kit.chunkSize + 10;
        const rangeCount = maxTotalChunks * 4;
        const documentSize = rangeSize * rangeCount;

        const relPath = "file0.py";
        kit.createFile(folderId, relPath, "x".repeat(documentSize));
        const document = kit.openDocument(folderId, relPath);
        const origText = document.getText();
        const origBlobName = kit.calculateBlobName(folderId, relPath, origText)!;
        openFileManager.startTracking(folderId, relPath, document);

        let idx = 0;
        let chunkCountHWM = 0;

        // Prevent uploads from completing.
        const uploadDelay = kit.apiServer.delayMemorize();
        let uploadsBlocked = true;

        const updateDocument = (): boolean => {
            const toReplace = `${idx}`;
            kit.applyTextDocumentUpdate(
                openFileManager,
                folderId,
                relPath,
                document,
                idx * rangeSize,
                toReplace,
                toReplace.length
            );

            const recencySummary = kit.getRecencySummary(openFileManager);
            expect(recencySummary.recentChunks.length).toBeLessThanOrEqual(maxTotalChunks * 2);
            chunkCountHWM = Math.max(chunkCountHWM, recencySummary.recentChunks.length);

            const blobName = recencySummary.folderMap.get(folderId)!.get(relPath);
            if (uploadsBlocked && blobName !== undefined) {
                expect(blobName).toBe(origBlobName);
                kit.verifyReconstruct(
                    blobName,
                    origText,
                    recencySummary.recentChunks,
                    document.getText()
                );
            }

            return blobName !== undefined;
        };

        // We should be able to update the document for a while with it still being included
        // in the recency summary. But eventually it should stop reporting it, as it needs
        // at least an occasional successful completion.
        while (updateDocument()) {
            idx++;
        }
        // 10 is arbitrary, and in practice it should be much more than this. We just don't have
        // a specific number to test.
        expect(idx).toBeGreaterThan(10);
        expect(idx).toBeLessThan(rangeCount);

        // Further updates to the document should not cause it to start getting reported again.
        let targetIdx = idx + 10;
        while (idx < targetIdx) {
            expect(updateDocument()).toBe(false);
            idx++;
        }

        // Allow uploads to complete and wait for the current state to be uploaded.
        uploadDelay.dispose();
        uploadsBlocked = false;
        const blobName = kit.calculateBlobName(folderId, relPath, document.getText())!;
        await kit.forceUploadAndIndex(openFileManager, folderId, relPath, blobName);

        // The document should again be included in recency summaries, and OpenFileManager should
        // have maintained a bounded amount of history the entire time.
        expect(updateDocument()).toBe(true);
        expect(chunkCountHWM).toBeLessThanOrEqual(maxTotalChunks * 2);
    });
});

describe("OpenFileManager multi-folder", () => {
    const folderRoot1 = "/src/folderRoot1";
    const folderRoot2 = "/src/folderRoot2";

    const folder1Files = defaultFiles;
    const folder2Files = new Map<string, string>([
        ["foo.py", "this is foo.py in folder2"],
        ["bar.py", "this is bar.py"],
        ["baz.py", "this is baz.py"],
    ]);

    let kit: OpenFileManagerTestKit;
    let openFileManager: OpenFileManager;
    let folderId1: number;
    let folderId2: number;

    beforeEach(() => {
        jest.useFakeTimers();
        kit = new OpenFileManagerTestKit();
        folderId1 = kit.pathMap.openSourceFolder(folderRoot1, folderRoot1);
        createRepoFiles(kit, folderId1, folder1Files);
        folderId2 = kit.pathMap.openSourceFolder(folderRoot2, folderRoot2);
        createRepoFiles(kit, folderId2, folder2Files);

        openFileManager = kit.makeOpenFileManager();
        openFileManager.openSourceFolder(folderId1);
        openFileManager.openSourceFolder(folderId2);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("start and stop tracking", () => {
        const relPath = "foo.py";

        const document1 = kit.openDocument(folderId1, relPath);
        const document2 = kit.openDocument(folderId2, relPath);

        openFileManager.startTracking(folderId1, relPath, document1);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(true);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(false);

        openFileManager.startTracking(folderId2, relPath, document2);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(true);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(true);

        let blobName1 = openFileManager.getBlobName(folderId1, relPath);
        verifyDefined(blobName1);
        kit.verifyTracked(openFileManager, folderId1, relPath, blobName1);

        let blobName2 = openFileManager.getBlobName(folderId2, relPath);
        verifyDefined(blobName2);
        kit.verifyTracked(openFileManager, folderId2, relPath, blobName2);

        openFileManager.stopTracking(folderId1, relPath, DocumentType.text);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(false);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(true);

        openFileManager.stopTracking(folderId2, relPath, DocumentType.text);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(false);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(false);
    });

    test("recency summary", () => {
        const relPath = "foo.py";

        const document1 = kit.openDocument(folderId1, relPath);
        const document2 = kit.openDocument(folderId2, relPath);

        openFileManager.startTracking(folderId1, relPath, document1);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(true);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(false);

        openFileManager.startTracking(folderId2, relPath, document2);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(true);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(true);

        let blobName1 = openFileManager.getBlobName(folderId1, relPath);
        verifyDefined(blobName1);
        kit.verifyTracked(openFileManager, folderId1, relPath, blobName1);

        let blobName2 = openFileManager.getBlobName(folderId2, relPath);
        verifyDefined(blobName2);
        kit.verifyTracked(openFileManager, folderId2, relPath, blobName2);

        const recencySummary = kit.getRecencySummary(openFileManager);
        expect(recencySummary.recentChunks.length).toBe(0);
        expect(recencySummary.folderMap.get(folderId1)!.keys()).toContain(relPath);
        expect(recencySummary.folderMap.get(folderId2)!.keys()).toContain(relPath);
    });

    test("updates", async () => {
        const relPath = "foo.py";

        const document1 = kit.openDocument(folderId1, relPath);
        const document2 = kit.openDocument(folderId2, relPath);

        openFileManager.startTracking(folderId1, relPath, document1);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(true);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(false);

        openFileManager.startTracking(folderId2, relPath, document2);
        expect(openFileManager.isTracked(folderId1, relPath)).toBe(true);
        expect(openFileManager.isTracked(folderId2, relPath)).toBe(true);

        let blobName1 = openFileManager.getBlobName(folderId1, relPath);
        verifyDefined(blobName1);
        kit.verifyTracked(openFileManager, folderId1, relPath, blobName1);

        let blobName2 = openFileManager.getBlobName(folderId2, relPath);
        verifyDefined(blobName2);
        kit.verifyTracked(openFileManager, folderId2, relPath, blobName2);

        blobName1 = kit.applyTextDocumentUpdate(
            openFileManager,
            folderId1,
            relPath,
            document1,
            5,
            "hello"
        );

        blobName2 = kit.applyTextDocumentUpdate(
            openFileManager,
            folderId2,
            relPath,
            document2,
            3,
            "goodbye"
        );

        openFileManager.loseFocus();
        await kit.awaitIndexedBlobName(openFileManager, folderId1, relPath, blobName1);
        await kit.awaitIndexedBlobName(openFileManager, folderId2, relPath, blobName2);
    });
});
