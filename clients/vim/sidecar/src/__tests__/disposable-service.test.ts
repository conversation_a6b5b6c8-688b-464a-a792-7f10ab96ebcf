import { DisposableCollection, DisposableService } from "../utils/disposable-service";
import * as vscode from "../vscode";

describe("disposable collection", () => {
    test("addDisposable", () => {
        // Create a set containing the numbers [0, itemCount). Create two DisposableCollections,
        // one that removes the even numbers from the set and one that removes the odd numbers.
        // Verify that disposing the collections removes the correct items from the set.
        const itemCount = 20;
        const items = new Set<number>();
        const evenDisps = new DisposableCollection();
        const oddDisps = new DisposableCollection();

        for (let i = 0; i < itemCount; i++) {
            items.add(i);
            const disp = new vscode.Disposable(() => {
                expect(items).toContain(i);
                items.delete(i);
            });
            if (i % 2 === 0) {
                evenDisps.add(disp);
            } else {
                oddDisps.add(disp);
            }
        }

        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(true);
        }

        oddDisps.dispose();
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(i % 2 === 0);
        }

        evenDisps.dispose();
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(false);
        }
    });

    test("addDisposables", () => {
        // Similar to the addDisposable test, but adds the disposables explicitly using the
        // addDisposables method.
        const items = new Set<number>();

        items.add(0);
        const disp0 = new vscode.Disposable(() => {
            expect(items).toContain(0);
            items.delete(0);
        });

        items.add(1);
        const disp1 = new vscode.Disposable(() => {
            expect(items).toContain(1);
            items.delete(1);
        });

        items.add(2);
        const disp2 = new vscode.Disposable(() => {
            expect(items).toContain(2);
            items.delete(2);
        });

        items.add(3);
        const disp3 = new vscode.Disposable(() => {
            expect(items).toContain(3);
            items.delete(3);
        });

        items.add(4);
        const disp4 = new vscode.Disposable(() => {
            expect(items).toContain(4);
            items.delete(4);
        });

        const evenDisps = new DisposableCollection();
        const oddDisps = new DisposableCollection();
        evenDisps.addAll(disp0, disp2, disp4);
        oddDisps.addAll(disp1, disp3);

        const itemCount = items.size;
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(true);
        }

        oddDisps.dispose();
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(i % 2 === 0);
        }

        evenDisps.dispose();
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(false);
        }
    });

    test("adopt", () => {
        const itemCount = 20;
        const items = new Set<number>();
        const disposables = new DisposableCollection();

        for (let i = 0; i < itemCount; i++) {
            items.add(i);
            disposables.add(
                new vscode.Disposable(() => {
                    expect(items).toContain(i);
                    items.delete(i);
                })
            );
        }

        const collection = new DisposableCollection();
        collection.adopt(disposables);
        collection.dispose();
        expect(items.size).toBe(0);
    });
});

class DisposableServiceTestKit extends DisposableService {
    constructor(
        disposables: DisposableCollection = new DisposableCollection(),
        priorityDisposables: DisposableCollection = new DisposableCollection()
    ) {
        super(disposables, priorityDisposables);
    }
}

describe("disposable service", () => {
    test("addDisposable", () => {
        // Create a set containing the numbers [0, itemCount). Create a DisposableService and
        // add disposables to delete just the odd numbers from the set. Verify that the correct
        // items are removed from the set when the service is disposed.
        const kit = new DisposableServiceTestKit();
        const itemCount = 20;
        const items = new Set<number>();

        for (let i = 0; i < itemCount; i++) {
            items.add(i);
            if (i % 2 === 1) {
                kit.addDisposable(
                    new vscode.Disposable(() => {
                        expect(items).toContain(i);
                        items.delete(i);
                    })
                );
            }
        }

        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(true);
        }

        kit.dispose();
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(i % 2 === 0);
        }
    });

    test("addDisposables", () => {
        // Similar to the addDisposable test, but adds the disposables explicitly using the
        // addDisposables method.
        const kit = new DisposableServiceTestKit();
        const items = new Set<number>();

        items.add(0);
        const disp0 = new vscode.Disposable(() => {
            expect(items).toContain(0);
            items.delete(0);
        });

        items.add(1);

        items.add(2);
        const disp2 = new vscode.Disposable(() => {
            expect(items).toContain(2);
            items.delete(2);
        });

        items.add(3);

        items.add(4);
        const disp4 = new vscode.Disposable(() => {
            expect(items).toContain(4);
            items.delete(4);
        });

        kit.addDisposables(disp0, disp2, disp4);

        const itemCount = items.size;
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(true);
        }

        kit.dispose();
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(i % 2 === 1);
        }
    });

    test("construct with disposables", () => {
        // Similar to the addDisposable test, but constructs the service from a
        // DisposableCollection.
        const itemCount = 20;
        const items = new Set<number>();

        const collection = new DisposableCollection();
        for (let i = 0; i < itemCount; i++) {
            items.add(i);
            if (i % 2 === 1) {
                collection.add(
                    new vscode.Disposable(() => {
                        expect(items).toContain(i);
                        items.delete(i);
                    })
                );
            }
        }

        const kit = new DisposableServiceTestKit(collection);

        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(true);
        }

        kit.dispose();
        for (let i = 0; i < itemCount; i++) {
            expect(items.has(i)).toBe(i % 2 === 0);
        }
    });

    test("priority disposables", () => {
        // Create a set of even numbers and a set of odd numbers. Add disposables to remove
        // all of the numbers from the sets, but give the odd disposables priority. Verify that
        // the all of the odd numbers are removed before any of the even numbers.
        const itemCount = 20;
        const evenItems = new Set<number>();
        const oddItems = new Set<number>();
        const kit = new DisposableServiceTestKit();

        for (let i = 0; i < itemCount; i++) {
            if (i % 2 === 0) {
                evenItems.add(i);
                kit.addDisposable(
                    new vscode.Disposable(() => {
                        expect(oddItems.size).toBe(0);
                        expect(evenItems).toContain(i);
                        evenItems.delete(i);
                    })
                );
            } else {
                oddItems.add(i);
                kit.addDisposable(
                    new vscode.Disposable(() => {
                        expect(oddItems).toContain(i);
                        oddItems.delete(i);
                    }),
                    true
                );
            }
        }

        kit.dispose();
        expect(evenItems.size).toBe(0);
        expect(oddItems.size).toBe(0);
    });

    test("construct with priority disposables", () => {
        // Similar to the priority disposables test, but constructs the service from two
        // DisposableCollections.
        const itemCount = 20;
        const evenItems = new Set<number>();
        const oddItems = new Set<number>();

        const evenCollection = new DisposableCollection();
        const oddCollection = new DisposableCollection();

        for (let i = 0; i < itemCount; i++) {
            if (i % 2 === 0) {
                evenItems.add(i);
                evenCollection.add(
                    new vscode.Disposable(() => {
                        expect(oddItems.size).toBe(0);
                        expect(evenItems).toContain(i);
                        evenItems.delete(i);
                    })
                );
            } else {
                oddItems.add(i);
                oddCollection.add(
                    new vscode.Disposable(() => {
                        expect(oddItems).toContain(i);
                        oddItems.delete(i);
                    })
                );
            }
        }

        const kit = new DisposableServiceTestKit(evenCollection, oddCollection);
        kit.dispose();
        expect(evenItems.size).toBe(0);
        expect(oddItems.size).toBe(0);
    });
});
