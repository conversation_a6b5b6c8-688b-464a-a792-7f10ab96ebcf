import { getLogger } from "../logging";
import { MonitoredParameter } from "../monitored-parameter";

describe("MonitoredParameter", () => {
    it("string updates", () => {
        const p = new MonitoredParameter<string>("example", getLogger("test"));
        expect(p.update("hello")).toEqual(true);
        expect(p.update("hello")).toEqual(false);
        expect(p.update("goodbye")).toEqual(true);
    });

    it("number updates", () => {
        const p = new MonitoredParameter<Number>("example", getLogger("test"));
        expect(p.update(123)).toEqual(true);
        expect(p.update(123)).toEqual(false);
        expect(p.update(456)).toEqual(true);
    });

    it("object updates", () => {
        const p = new MonitoredParameter<Object>("example", getLogger("test"));
        // Check empty object
        expect(p.update({})).toEqual(true);
        expect(p.update({})).toEqual(false);

        // Check for key/values
        expect(p.update({ example: "test" })).toEqual(true);
        expect(p.update({ example: "test" })).toEqual(false);
        expect(p.update({ example: "other" })).toEqual(true);

        // Check for nested objects
        expect(p.update({ example: "other", nested: { example: 123 } })).toEqual(true);
        expect(p.update({ example: "other", nested: { example: 123 } })).toEqual(false);
        expect(p.update({ example: "other", nested: { example: 456 } })).toEqual(true);
    });
});

describe("MonitoredParameter.diff", () => {
    it("undefined to values", () => {
        const p = new MonitoredParameter<string>("example", getLogger("test"));
        expect(p.diff(undefined, undefined)).toEqual([]);
        expect(p.diff(undefined, "hello")).toEqual(['undefined to "hello"']);
        expect(p.diff(undefined, 123)).toEqual(["undefined to 123"]);

        const exampleObject = {
            hello: "world",
            nested: {
                value: 123,
            },
        };
        expect(p.diff(undefined, exampleObject)).toEqual([
            `undefined to ${JSON.stringify(exampleObject)}`,
        ]);
    });

    it("null to values", () => {
        const p = new MonitoredParameter<string>("example", getLogger("test"));
        expect(p.diff(null, null)).toEqual([]);
        expect(p.diff(null, "hello")).toEqual(['null to "hello"']);
        expect(p.diff(null, 123)).toEqual(["null to 123"]);

        const exampleObject = {
            hello: "world",
            nested: {
                value: 123,
            },
        };
        expect(p.diff(null, exampleObject)).toEqual([`null to ${JSON.stringify(exampleObject)}`]);
    });

    it("object to values", () => {
        const exampleObject = {
            hello: "world",
            nested: {
                value: 123,
            },
        };

        const p = new MonitoredParameter<string>("example", getLogger("test"));
        expect(p.diff(exampleObject, exampleObject)).toEqual([]);
        expect(p.diff(exampleObject, "hello")).toEqual([
            `${JSON.stringify(exampleObject)} to "hello"`,
        ]);
        expect(p.diff(exampleObject, 123)).toEqual([`${JSON.stringify(exampleObject)} to 123`]);

        expect(
            p.diff(exampleObject, Object.assign({}, exampleObject, { hello: "goodbye" }))
        ).toEqual(['hello: "world" to "goodbye"']);
        expect(
            p.diff(exampleObject, Object.assign({}, exampleObject, { nested: { value: 456 } }))
        ).toEqual(["nested > value: 123 to 456"]);

        expect(
            p.diff(
                exampleObject,
                Object.assign({}, exampleObject, { somethingNew: { value: 456 } })
            )
        ).toEqual([`somethingNew: undefined to ${JSON.stringify({ value: 456 })}`]);
        expect(p.diff(exampleObject, { hello: exampleObject.hello })).toEqual([
            `nested: ${JSON.stringify({ value: 123 })} to undefined`,
        ]);
    });
});
