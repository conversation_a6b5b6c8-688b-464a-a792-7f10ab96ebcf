import { AugmentCompletion } from "../../completions/augment-completion";

describe("AugmentCompletion", () => {
    test("toString", () => {
        expect(
            new AugmentCompletion("test", "replacement", "skipped", {
                startOffset: 1,
                endOffset: 2,
            }).toString()
        ).toBe(
            "text: test\n    suffixReplacementText: replacement\n    skippedSuffix: skipped\n    start: 1\n    end: 2"
        );
    });
});
