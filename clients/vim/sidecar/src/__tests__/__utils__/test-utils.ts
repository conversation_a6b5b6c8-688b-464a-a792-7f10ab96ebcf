// verifyDefined verifies that the given value is defined and not null. Because it is a type
// assertion, it has the benefit of narrowing the type of its argument in the calling function.
// For example:
//     const str = returnAPossiblyNullorUndefinedString(); // str is `string | undefined | null'
//     verifyDefined(str); // If str survives this call, it will have been narrowed to just `string`
//     const len = str.length; // No need to use `str!`.
export function verifyDefined<T>(value: T | undefined | null): asserts value is T {
    expect(value).toBeDefined();
    expect(value).not.toBeNull();
}
