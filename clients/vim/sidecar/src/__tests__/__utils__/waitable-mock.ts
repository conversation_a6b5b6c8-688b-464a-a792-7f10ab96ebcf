// This is from https://github.com/jestjs/jest/issues/7432
export const waitableJestFn = (times: number): WaitableMock => {
    let _resolve: Function;
    const promise = new Promise<void>((resolve) => (_resolve = resolve));

    let i: number = 0;
    const mock = jest.fn(() => {
        i++;
        if (i >= times) {
            _resolve();
        }
    }) as WaitableMock; // force casting

    mock.waitUntilComplete = () => promise;

    return mock;
};

type WaitableMock = jest.Mock & {
    waitUntilComplete(): Promise<void>;
};
