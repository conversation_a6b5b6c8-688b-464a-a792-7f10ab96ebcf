// advanceTimeUntilTrue repeatedly advances time by the given number of milliseconds until the
// given test function returns true.
export async function advanceTimeUntilTrue(test: () => boolean, incrementMs = 200): Promise<void> {
    while (!test()) {
        await jest.advanceTimersByTimeAsync(incrementMs);
    }
}

// waitMs repeatedly advances time by the given increment until the given number of milliseconds
// has passed.
export async function waitMs(ms: number, incrementMs = 200): Promise<void> {
    let totalWait = 0;
    await advanceTimeUntilTrue(() => {
        totalWait += incrementMs;
        return totalWait >= ms;
    }, ms);
}

// advanceTimeUntilResolve repeatedly advances time by the given number of milliseconds until the
// given promise resolves.
export async function advanceTimeUntilResolve<T = void>(
    promise: Promise<T>,
    incrementMs = 200
): Promise<void> {
    let done = false;
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    const advanceTimePromise = new Promise<void>(async () => {
        while (!done) {
            await jest.advanceTimersByTimeAsync(incrementMs);
        }
    });

    // "Race" the given promise with a promise that repeatedly advances time. It's not actually a
    // race, because the time-advancing promise doesn't resolve until after the given promise wins
    // the race and the `done` flag is set to true, but presenting the two promises to Promise.race
    // allows us to wait while they are both running.
    await Promise.race([promise, advanceTimePromise]);
    done = true;
}
