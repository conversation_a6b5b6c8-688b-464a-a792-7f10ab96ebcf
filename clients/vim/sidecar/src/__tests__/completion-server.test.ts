import { MockAPIServer } from "../__mocks__/mock-api-server";
import { CompletionServer } from "../completion-server";
import { CompletionTimeline } from "../completions/completion-timeline";

class CompletionServerTestKit {
    constructor(public readonly apiServer: MockAPIServer) {}

    public createCompletionServer() {
        return new CompletionServer(this.apiServer, undefined, 1000, 1000);
    }

    static create(): CompletionServerTestKit {
        const apiServer = new MockAPIServer();
        return new CompletionServerTestKit(apiServer);
    }
}

describe("completion-server", () => {
    let kit: CompletionServerTestKit;

    beforeEach(() => {
        kit = CompletionServerTestKit.create();
    });

    test("complete", async () => {
        const completionServer = kit.createCompletionServer();
        const completionTimeline = new CompletionTimeline();
        await completionServer.complete(
            "example-request-id-123",
            "def quicksort(",
            "",
            "quicksort.py",
            "quickSortBlobName",
            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
            "python",
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
            [],
            undefined,
            undefined,
            undefined,
            completionTimeline
        );

        expect(completionTimeline.isComplete()).toBe(false);
        expect(completionTimeline.emitTime).toBeUndefined();
        expect(completionTimeline.rpcStart).toBeDefined();
        expect(completionTimeline.rpcEnd).toBeDefined();
        expect(completionTimeline.requestStart).toBeDefined();

        const times = [
            completionTimeline.requestStart,
            completionTimeline.rpcStart,
            completionTimeline.rpcEnd,
        ];
        const sortedTimes = times.sort();
        expect(times).toEqual(sortedTimes);
    });
});
