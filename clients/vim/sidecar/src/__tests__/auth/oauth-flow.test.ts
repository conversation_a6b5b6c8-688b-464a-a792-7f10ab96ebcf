import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { ExtensionContext } from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AuthSessionStore } from "../../auth/auth-session-store";
import { base64URLEncode, OAuthFlow } from "../../auth/oauth-flow";
import * as vscode from "../../vscode";

jest.mock("crypto", () => {
    return {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        __esModule: true,
        ...jest.requireActual("crypto"),
        randomBytes: () => Buffer.from("random-bytes-1234"),
    };
});

describe("vim-oauth-flow", () => {
    test("save session in same instance", async () => {
        const code = "example-code-1234";
        const tenantURL = "http://augment-tenant-example.augmentcode.com";

        const apiServer = new MockAPIServer();

        const context = new ExtensionContext();
        const config = new AugmentConfigListener();
        const authSession = new TestAuthSessionStore(context as any, config);
        await authSession.ready;

        const auth = new OAuthFlow(context, config, apiServer, authSession);

        const url = await auth.startFlow();
        const expectedUrl =
            "https://auth.augmentcode.com/authorize?response_type=code&code_challenge=ptlKUgDZqrZIFPKu3rtPtVDTd_EYnMzlt99-__DGsYc&client_id=v&state=cmFuZG9tLWJ5dGVzLTEyMzQ&prompt=login";
        expect(url).toEqual(expectedUrl);

        const result = await auth.handleAuthJson(
            JSON.stringify({
                /* eslint-disable @typescript-eslint/naming-convention */
                state: base64URLEncode(Buffer.from("random-bytes-1234")),
                code: code,
                tenant_url: tenantURL,
                /* eslint-enable @typescript-eslint/naming-convention */
            })
        );
        expect(result).toEqual("success");

        const session = await authSession.getSession();
        expect(session).toEqual({
            accessToken: "fake-access-token",
            scopes: ["email"],
            tenantURL,
        });
    });
});

class TestAuthSessionStore extends AuthSessionStore {
    public readonly ready: Promise<void>;

    constructor(context: vscode.ExtensionContext, config: AugmentConfigListener) {
        super(context, config);

        this.ready = this._ready;
    }
}
