import { <PERSON><PERSON><PERSON><PERSON> } from "../ring-buffer";

/**
 * verifyContent verifies that the buffer contains the expected items
 * @param buffer the buffer to verify
 * @param expectedOldestItem expected value of the oldest item in the buffer (i.e., buffer.at(0))
 * @param expectedCount expected number of items in the buffer
 */
function verifyContent(
    buffer: RingBuffer<number>,
    expectedOldestItem: number,
    expectedCount: number
): void {
    expect(buffer.length).toBe(expectedCount);

    // test at() with nonnegative index
    for (let idx = 0; idx < expectedCount; idx++) {
        expect(buffer.at(idx)).toBe(expectedOldestItem + idx);
    }
    expect(buffer.at(buffer.length)).not.toBeDefined();

    // test at() with negative index
    for (let idx = 0; idx < expectedCount; idx++) {
        expect(buffer.at(-1 - idx)).toBe(expectedOldestItem + expectedCount - idx - 1);
    }
    if (buffer.length === 0) {
        expect(buffer.at(-1)).not.toBeDefined();
    }

    // test iterator
    let idx = 0;
    for (let item of buffer) {
        expect(item).toBe(expectedOldestItem + idx);
        idx++;
    }

    // test slice()
    const items = buffer.slice();
    expect(items.length).toBe(expectedCount);
    for (const [index, item] of items.entries()) {
        expect(item).toBe(expectedOldestItem + index);
        expect(item).toBe(buffer.at(index));
    }
}

/**
 * testRingBuffer tests basic functionality of the RingBuffer class
 * @param maxItems size of the RingBuffer
 * @param toAdd number of items in the buffer
 * @param expectedOldestItem the oldest item in the buffer
 * @param expectedCount expected number of items in the buffer
 */
function testRingBuffer(
    maxItems: number,
    toAdd: number,
    expectedOldestItem: number,
    expectedCount: number
): void {
    const base = 100;

    const buffer = new RingBuffer<number>(maxItems);
    for (let i = 0; i < toAdd; i++) {
        buffer.addItem(base + i);
    }
    verifyContent(buffer, base + expectedOldestItem, expectedCount);
    buffer.clear();
    verifyContent(buffer, 0, 0);
}

/**
 * testSlice tests the slice() method of the RingBuffer class
 * @param maxItems size of the RingBuffer
 * @param toAdd number of items in the buffer
 * @param start start index of the slice() method
 * @param end end index of the slice() method
 * @param expectedOldestIdx index of the oldest item in the buffer
 * @param expectedCount expected number of items in the buffer
 */
function testSlice(
    maxItems: number,
    toAdd: number,
    start: number | undefined,
    end: number | undefined,
    expectedOldestIdx: number | undefined,
    expectedCount: number
): void {
    const base = 100;

    const buffer = new RingBuffer<number>(maxItems);
    for (let i = 0; i < toAdd; i++) {
        buffer.addItem(base + i);
    }

    const slice = buffer.slice(start, end);
    expect(slice.length).toBe(expectedCount);
    for (const [index, item] of slice.entries()) {
        expect(item).toBe(base + expectedOldestIdx! + index);
    }
}

describe("RingBuffer", () => {
    // Test basic RingBuffer functionality
    test.each([
        [10, 0, 0, 0],
        [10, 1, 0, 1],
        [10, 5, 0, 5],
        [10, 10, 0, 10],
        [10, 15, 5, 10],
        [10, 30, 20, 10],
    ])("testRingBuffer(maxItems=%d, toAdd=%d)", testRingBuffer);

    // Test slice method with empty buffer
    test.each([
        [10, 0, 0, 0, undefined, 0],
        [10, 0, 1, 0, undefined, 0],
        [10, 0, 0, -1, undefined, 0],
        [10, 0, 1, -1, undefined, 0],
        [10, 0, undefined, undefined, undefined, 0],
    ])("testSlice(maxItems=%d, toAdd=%d, start=%d, end=%d)", testSlice);

    // Test slice method with small buffer (fewer than maxItems items)
    test.each([
        // all of these are equivalent to [1, 3)
        [10, 5, 1, 3, 1, 2],
        [10, 5, 1, -2, 1, 2],
        [10, 5, -4, 3, 1, 2],
        [10, 5, -4, -2, 1, 2],

        // all of these are equivalent to [0, 3)
        [10, 5, 0, 3, 0, 3],
        [10, 5, 0, -2, 0, 3],
        [10, 5, -5, 3, 0, 3],
        [10, 5, -5, -2, 0, 3],
        [10, 5, -20, 3, 0, 3],

        // all of these are equivalent to [2, 5)
        [10, 5, 2, 5, 2, 3],
        [10, 5, -3, 5, 2, 3],
        [10, 5, -3, 20, 2, 3],
    ])("testSlice(maxItems=%d, toAdd=%d, start=%d, end=%d)", testSlice);

    // Test slice method with large buffer (more than maxItems items), no wraparound
    test.each([
        // all of these are equivalent to [6, 6)
        [10, 25, 6, 6, undefined, 0],
        [10, 25, 6, -4, undefined, 0],
        [10, 25, -4, 6, undefined, 0],
        [10, 25, -4, -4, undefined, 0],

        // all of these are equivalent to [6, 8)
        [10, 25, 6, 8, 21, 2],
        [10, 25, 6, -2, 21, 2],
        [10, 25, -4, 8, 21, 2],
        [10, 25, -4, -2, 21, 2],

        // all of these are equivalent to [6, 10)
        [10, 25, 6, 10, 21, 4],
        [10, 25, 6, 20, 21, 4],
        [10, 25, 6, undefined, 21, 4],

        // all of these are equivalent to [8, 6)
        [10, 25, 8, 6, undefined, 0],
        [10, 25, 8, -4, undefined, 0],
        [10, 25, -2, 6, undefined, 0],
        [10, 25, -2, -4, undefined, 0],
    ])("testSlice(maxItems=%d, toAdd=%d, start=%d, end=%d)", testSlice);

    // Test slice method with large buffer (more than maxItems items), with wraparound
    test.each([
        // all of these are equivalent to [0, 7)
        [10, 25, 0, 7, 15, 7],
        [10, 25, 0, -3, 15, 7],
        [10, 25, undefined, 7, 15, 7],
        [10, 25, undefined, -3, 15, 7],
        [10, 25, -10, 7, 15, 7],
        [10, 25, -10, -3, 15, 7],
        [10, 25, -20, 7, 15, 7],

        // all of these are equivalent to [2, 7)
        [10, 25, 2, 7, 17, 5],
        [10, 25, 2, -3, 17, 5],
        [10, 25, -8, 7, 17, 5],
        [10, 25, -8, -3, 17, 5],

        // all of these are equivalent to [2, 10)
        [10, 25, 2, 10, 17, 8],
        [10, 25, -8, 10, 17, 8],
        [10, 25, 2, 20, 17, 8],
        [10, 25, -8, 20, 17, 8],
        [10, 25, 2, undefined, 17, 8],
        [10, 25, -8, undefined, 17, 8],

        // all of these are equivalent to [7, 2)
        [10, 25, 7, 2, undefined, 0],
        [10, 25, 7, -8, undefined, 0],
        [10, 25, -3, 2, undefined, 0],
        [10, 25, -3, -8, undefined, 0],
    ])("testSlice(maxItems=%d, toAdd=%d, start=%d, end=%d)", testSlice);

    // Test the dropItemsLeft method
    test.each([
        [8, 2, 2, 6], // drop fewer than added, no wraparound
        [8, 20, 0, 0], // drop more than added, no wraparound
        [18, 2, 10, 8], // drop fewer than added, with wraparound
        [18, 20, 10, 0], // drop more than added, with wraparound
    ])(
        "dropItemsLeft(toAdd=%d, toDrop=%d)",
        (toAdd: number, toDrop: number, expectedOldestItem: number, expectedCount: number) => {
            const base = 100;
            const maxItems = 10;
            const buffer = new RingBuffer<number>(maxItems);
            for (let idx = 0; idx < toAdd; idx++) {
                buffer.addItem(base + idx);
            }
            buffer.shiftLeft(toDrop);
            verifyContent(buffer, base + expectedOldestItem, expectedCount);
        }
    );

    // Test the shiftRight method
    test.each([
        [8, 2, 0, 5, 6], // drop fewer than added, no wraparound
        [8, 20, 0, undefined, 0], // drop more than added, no wraparound
        [18, 2, 8, 15, 8], // drop fewer than added, with wraparound
        [18, 20, 10, undefined, 0], // drop more than added, with wraparound
    ])(
        "shiftRight(toAdd=%d, toDrop=%d)",
        (
            toAdd: number,
            toDrop: number,
            expectedOldestItem: number,
            expectedNewestItem: number | undefined,
            expectedCount: number
        ) => {
            const base = 100;
            const maxItems = 10;
            const buffer = new RingBuffer<number>(maxItems);
            for (let idx = 0; idx < toAdd; idx++) {
                buffer.addItem(base + idx);
            }
            buffer.shiftRight(toDrop);
            verifyContent(buffer, base + expectedOldestItem, expectedCount);
            expect(buffer.at(-1)).toBe(expectedNewestItem ? base + expectedNewestItem : undefined);
        }
    );

    // Test that the shiftRight method with a large parameter won't corrupt the buffer.
    it("shiftRight too far", async () => {
        const buffer = new RingBuffer<number>(7);
        for (let idx = 0; idx < 7; idx++) {
            buffer.addItem(idx);
        }
        buffer.shiftRight(11);
        expect(buffer.length).toBe(0);
        expect(buffer.at(0)).not.toBeDefined();
        buffer.addItem(42);
        expect(buffer.length).toBe(1);
        expect(buffer.at(0)).toBe(42);
        expect(buffer.at(-1)).toBe(42);
    });
});
