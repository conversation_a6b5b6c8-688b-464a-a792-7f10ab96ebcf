import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";

describe("ClientMetricsReporter", () => {
    let apiServer: MockAPIServer;

    beforeEach(() => {
        apiServer = new MockAPIServer();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
        jest.resetAllMocks();
    });

    test("report metric", async () => {
        jest.spyOn(apiServer, "clientMetrics");

        const reporter = new ClientMetricsReporter(apiServer);
        reporter.report({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: "my-metric",
            value: 123,
        });
        reporter.report({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: "my-other-metric",
            value: 456,
        });

        reporter.enableUpload();
        jest.advanceTimersByTime(ClientMetricsReporter.defaultUploadMsec);

        expect(apiServer.clientMetrics).toHaveBeenCalledTimes(1);
        expect(apiServer.clientMetrics).toHaveBeenCalledWith([
            {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                client_metric: "my-metric",
                value: 123,
            },
            {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                client_metric: "my-other-metric",
                value: 456,
            },
        ]);
    });
});
