import * as objectUtils from "../../utils/object-utils";

describe("object-utils", () => {
    describe("getPropertiesSize", () => {
        it("should return the size of the object properties", () => {
            const exampleObject = {
                name: "<PERSON>",
                age: 30,
                isActive: true,
                hobbies: ["reading", "hiking", "coding"],
                address: {
                    street: "123 Main St",
                    city: "Wonderland",
                    postalCode: "12345",
                },
                metadata: {
                    createdAt: "2024-01-01T00:00:00Z",
                    tags: ["example", "typescript"],
                    hasVisited: null,
                },
                nestedArrays: [
                    {
                        children: [
                            {
                                grandchildren: [
                                    {
                                        greatgrandchildren: "hello",
                                    },
                                ],
                            },
                        ],
                    },
                ],
                emptyArray: [],
                arrayWithNils: [null, undefined],
                emptyObject: {},
                objectWithNils: {
                    a1: null,
                    a2: undefined,
                },
                longArrays: [
                    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
                    23, 24, 25, 26, 27, 28, 29,
                ],
            };

            const result = objectUtils.getPropertySizes(exampleObject);
            expect(result).toMatchSnapshot();
        });
    });
});
