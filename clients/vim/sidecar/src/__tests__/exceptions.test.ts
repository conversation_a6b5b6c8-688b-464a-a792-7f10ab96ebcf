import { APIError } from "../exceptions";
import { APIStatus } from "../utils/types";

describe("exceptions-apierror", () => {
    test("transientIssue", () => {
        const msg = "Example message";
        const e = APIError.transientIssue(msg);
        expect(e.message).toEqual(msg);
        expect(e.status).toEqual(APIStatus.unavailable);
        expect(APIError.isAPIErrorWithStatus(e, APIStatus.unavailable)).toEqual(true);
    });

    const testCases: [number, APIStatus][] = [
        [200, APIStatus.ok],
        [400, APIStatus.invalidArgument],
        [401, APIStatus.unauthenticated],
        [403, APIStatus.permissionDenied],
        [404, APIStatus.unimplemented],
        [413, APIStatus.augmentTooLarge],
        [429, APIStatus.resourceExhausted],
        [500, APIStatus.unavailable],
        [550, APIStatus.unavailable],
        [599, APIStatus.unavailable],

        // Unknown status code
        [418, APIStatus.unknown],
    ];
    for (const [code, status] of testCases) {
        test(`fromResponse ${code}`, async () => {
            const resp = new Response(undefined, { status: code, statusText: "Example Text" });
            const e = await APIError.fromResponse(resp);
            expect(e.message).toEqual(`HTTP error: ${code} Example Text`);
            expect(e.status).toEqual(status);
            expect(APIError.isAPIErrorWithStatus(e, status)).toEqual(true);
        });
    }

    test("fromResponse 500", async () => {
        const resp = new Response(undefined, { status: 500, statusText: "Internal Server Error" });
        const e = await APIError.fromResponse(resp);
        expect(e.message).toEqual("HTTP error: 500 Internal Server Error");
        expect(e.status).toEqual(APIStatus.unavailable);
        expect(APIError.isAPIErrorWithStatus(e, APIStatus.unavailable)).toEqual(true);
        expect(APIError.isRetriableAPIError(e)).toEqual(true);
    });

    for (const status of [400, 413, 500, 429]) {
        test(`fromResponse with error details (${status})}`, async () => {
            const errorDetails = {
                code: 1, // INVALID_TOOL_DEFINITION
                message: "Invalid tool definition",
                detail: "Tool name contains invalid characters",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                help_uri: "https://docs.augmentcode.com/errors/invalid-tool-definition",
            };
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const responseBody = JSON.stringify({ error_details: errorDetails });
            const resp = new Response(responseBody, {
                status: status,
                statusText: "Bad Request",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                headers: { "content-type": "application/json" },
            });

            const e = await APIError.fromResponse(resp);
            expect(e.message).toEqual(`HTTP error: ${status} Bad Request`);
            expect(e.errorDetails).toBeDefined();
            expect(e.errorDetails?.code).toEqual(1);
            expect(e.errorDetails?.message).toEqual("Invalid tool definition");
            expect(e.errorDetails?.detail).toEqual("Tool name contains invalid characters");
            expect(e.errorDetails?.help_uri).toEqual(
                "https://docs.augmentcode.com/errors/invalid-tool-definition"
            );
        });
    }

    const respCases: [string, string][] = [
        ["application/json", "{}"],
        ["application/json", '{"error": "Internal Server Error"}'],
        ["application/json", '"just a string"'],
        ["application/json", "[]"],
        ["application/json", "null"],
        ["application/json", "true"],
        ["application/json", "1"],
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ["text/html", JSON.stringify({ error_details: { code: 0, message: "", detail: "" } })],
    ];
    for (const [i, [contentType, body]] of respCases.entries()) {
        test(`fromResponse with no error details ${i}`, async () => {
            const resp = new Response(body, {
                status: 400,
                statusText: "Bad Request",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                headers: { "content-type": contentType },
            });
            const e = await APIError.fromResponse(resp);
            expect(e.errorDetails).toBeUndefined();
            expect(e.status).toEqual(APIStatus.invalidArgument);
            expect(e.message).toEqual("HTTP error: 400 Bad Request");
        });
    }

    test("preserveCause", () => {
        const origin = new Error("Origin error");
        const wrapped = new Error("Wrapped error", { cause: origin });
        const e = APIError.transientIssue(wrapped);
        expect(e.status).toEqual(APIStatus.unavailable);
        expect(e.message).toEqual("Wrapped error");
        expect(e.cause instanceof Error).toEqual(true);
        expect((e.cause as Error).message).toEqual("Wrapped error");
        expect(((e.cause as Error).cause as Error).message).toEqual("Origin error");
        expect(((e.cause as Error).cause as Error).cause).toEqual(undefined);
    });
});
