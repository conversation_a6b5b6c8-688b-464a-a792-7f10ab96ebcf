import {
    generateMockWorkspaceConfig,
    getExampleAugmenConfig,
    getExampleUserConfig,
} from "../__mocks__/mock-augment-config";
import { mockWorkspaceConfigChange, resetMockWorkspace } from "../__mocks__/vscode-mocks";
import { AugmentConfig, AugmentConfigListener, UserConfig } from "../augment-config-listener";

jest.mock("../vscode");

/**
 * compareConfigs verifies that the given config matches the expected config.
 */
function compareConfigs(config: AugmentConfig, expected: UserConfig) {
    expect(config.completionURL).toBe(expected.advanced.completionURL);
    expect(config.modelName).toBe(expected.advanced.model);
    expect(config.enableUpload).toBe(expected.advanced.enableWorkspaceUpload);
}

class ConfigListenerTestKit {
    public configListener = new AugmentConfigListener();
}

describe("augment-config-listener", () => {
    beforeEach(() => {
        // Clean out the mock vscode workspace before each test
        resetMockWorkspace();
    });

    afterEach(() => {
        resetMockWorkspace();
    });

    // Verify that AugmentConfigListener.config produces the current configuration.
    test("get-config", () => {
        const kit = new ConfigListenerTestKit();
        const configListener = kit.configListener;
        const config = configListener.config;
        compareConfigs(config, getExampleUserConfig());
    });

    // Verify that AugmentConfigListener.onDidChange() will notify subscribers
    // of changes to the configured model.
    test("listen-model-change", () => {
        const kit = new ConfigListenerTestKit();
        const configListener = kit.configListener;

        // Listen for configuration changes.
        const notify = jest.fn(() => {});
        configListener.onDidChange(() => notify());
        expect(notify).not.toHaveBeenCalled();

        // Verify original config
        const config = configListener.config;
        compareConfigs(config, getExampleUserConfig());

        // Change only the model name
        const newAugmentConfig = generateMockWorkspaceConfig({
            advanced: {
                model: getExampleUserConfig().advanced.model + "xyz",
            },
        });

        mockWorkspaceConfigChange(newAugmentConfig);
        expect(notify).toHaveBeenCalled();
    });

    // Verify that AugmentConfigListener.onDidChange() will notify subscribers
    // of changes to memorization.
    test("listen-model-memorization", () => {
        const kit = new ConfigListenerTestKit();
        const configListener = kit.configListener;

        // Listen for configuration changes.
        const notify = jest.fn(() => {});
        configListener.onDidChange(() => notify());
        expect(notify).not.toHaveBeenCalled();

        // Verify original config
        const config = configListener.config;
        compareConfigs(config, getExampleUserConfig());

        // Change only the memory enablement
        const newAugmentConfig = generateMockWorkspaceConfig({
            advanced: {
                enableWorkspaceUpload: !getExampleUserConfig().advanced.enableWorkspaceUpload,
            },
        });

        mockWorkspaceConfigChange(newAugmentConfig);
        expect(notify).toHaveBeenCalled();
    });

    const normalizationTests: GetConfigTestCase = {
        noconfig: {
            userConfig: {
                // Scenario where no settings are defined by VSCode.
            },
            wantConfig: getExampleAugmenConfig(),
        },
        minimal: {
            userConfig: {
                advanced: {
                    apiToken: "example-token-abcd1234",
                    completionURL: "https://api.augmentcode.com",
                },
            },
            wantConfig: getExampleAugmenConfig({
                apiToken: "EXAMPLE-TOKEN-ABCD1234",
                completionURL: "https://api.augmentcode.com",
            }),
        },
        chatConfig: {
            userConfig: {
                advanced: {
                    chat: {
                        stream: false,
                    },
                },
            },
            wantConfig: getExampleAugmenConfig({
                chat: {
                    stream: false,
                },
            }),
        },
        legacyAPITokenAndCompletionURL: {
            userConfig: {
                apiToken: "example-token-abcd1234",
                completionURL: "https://api.augmentcode.com",
            },
            wantConfig: getExampleAugmenConfig({
                apiToken: "EXAMPLE-TOKEN-ABCD1234",
                completionURL: "https://api.augmentcode.com",
            }),
        },
        internal: {
            userConfig: {
                advanced: {
                    model: "example-model",
                    codeInstruction: {
                        model: "edit-model",
                    },
                    enableDebugFeatures: true,
                    enableReviewerWorkflows: true,
                },
            },
            wantConfig: getExampleAugmenConfig({
                modelName: "example-model",
                codeInstruction: {
                    model: "edit-model",
                },
                enableDebugFeatures: true,
                enableReviewerWorkflows: true,
                nextEdit: {
                    useCursorDecorations: false,
                    useSmallHover: true,
                },
            }),
        },
        booleanstring: {
            userConfig: {
                advanced: {
                    enableWorkspaceUpload: "true",
                },
            },
            wantConfig: getExampleAugmenConfig({
                enableUpload: true,
            }),
        },
    };
    for (const [k, v] of Object.entries(normalizationTests)) {
        test(`get-config - ${k}`, () => {
            const kit = new ConfigListenerTestKit();
            const configListener = kit.configListener;
            mockWorkspaceConfigChange(generateMockWorkspaceConfig(v.userConfig));
            expect(configListener.config).toEqual(v.wantConfig);
        });
    }
});

interface GetConfigTestCase {
    [key: string]: {
        userConfig: { [key: string]: any };
        wantConfig: AugmentConfig;
    };
}
