import {
    asDirName,
    descendentPath,
    directoryContainsPath,
    dirName,
    firstPathComponent,
    isAbsolutePathName,
    pathNameSansSep,
    sameDirectory,
    splitRelPath,
} from "../utils/path-utils";
import { uriToAbsPath } from "../utils/uri";
import * as vscode from "../vscode";

describe("path-utils", () => {
    test.each([
        ["/", "/", true],
        ["/abc", "/abc", true],
        ["/abc", "/abc/def", true],
        ["/abc", "/abc/def/ghi", true],
        ["/abc", "/abcdef", false],
        ["/abc", "/abcdef/ghi", false],
        ["/abc/def", "/abc", false],
    ])("uriContainsUri(%s, %s)", (parentPath: string, childPath: string, contains: boolean) => {
        const parentUri = vscode.Uri.file(parentPath);
        const childUri = vscode.Uri.file(childPath);
        expect(directoryContainsPath(uriToAbsPath(parentUri), uriToAbsPath(childUri))).toBe(
            contains
        );
    });

    test.each([
        ["walrus/giraffe/spider.py", "walrus/giraffe/"],
        ["spider", ""],
        ["./spider", ""],
        ["/spider", "/"],
    ])("dirName(%s)", (path: string, expected: string) => {
        expect(dirName(path)).toBe(expected);
    });

    test.each([
        ["walrus/giraffe", "walrus/giraffe/"],
        ["walrus/giraffe/", "walrus/giraffe/"],
        ["", "/"],
        [".", "./"],
        ["/", "/"],
    ])("asDirName(%s)", (path: string, expected: string) => {
        expect(asDirName(path)).toBe(expected);
    });

    test.each([
        ["walrus/giraffe", "walrus/giraffe"],
        ["walrus/giraffe/", "walrus/giraffe"],
        ["walrus/giraffe//", "walrus/giraffe"],
        ["goldfish", "goldfish"],
        ["", ""],
        [".", "."],
        ["/", "/"],
        ["//", "/"],
    ])("fileNameSansSep(%s)", (path: string, expected: string) => {
        expect(pathNameSansSep(path)).toBe(expected);
    });

    test.each([
        /*
         * Absolute path names.
         */
        // A directory contains itself.
        ["/walrus/giraffe", "/walrus/giraffe", ""],
        ["/walrus/giraffe", "/walrus/giraffe/", ""],
        ["/walrus/giraffe", "/walrus/giraffe//", ""],
        ["/walrus/giraffe/", "/walrus/giraffe", ""],
        ["/walrus/giraffe/", "/walrus/giraffe/", ""],
        ["/walrus/giraffe/", "/walrus/giraffe//", ""],
        ["/walrus/giraffe//", "/walrus/giraffe", ""],
        ["/walrus/giraffe//", "/walrus/giraffe/", ""],
        ["/walrus/giraffe//", "/walrus/giraffe//", ""],
        ["/walrus/giraffe", "/walrus/giraffe/./.", ""],
        ["/walrus/giraffe", "/walrus/giraffe/../giraffe", ""],

        // // Pathname within directory.
        ["/walrus/giraffe", "/walrus/giraffe/bug", "bug"],
        ["/walrus/giraffe", "/walrus/giraffe/bug/", "bug"],
        ["/walrus/giraffe", "/walrus/giraffe/bug//", "bug"],
        ["/walrus/giraffe/", "/walrus/giraffe/bug", "bug"],
        ["/walrus/giraffe/", "/walrus/giraffe/bug/", "bug"],
        ["/walrus/giraffe/", "/walrus/giraffe/bug//", "bug"],
        ["/walrus/giraffe//", "/walrus/giraffe/bug", "bug"],
        ["/walrus/giraffe//", "/walrus/giraffe/bug/", "bug"],
        ["/walrus/giraffe//", "/walrus/giraffe/bug//", "bug"],
        ["/walrus/giraffe", "/walrus/giraffe/././bug", "bug"],
        ["/walrus/giraffe", "/walrus/giraffe/../giraffe/bug", "bug"],

        // // Pathname not within directory.
        ["/walrus/giraffe", "/dog/cat/pigeon", undefined],
        ["/walrus/giraffe", "/walrus/bug", undefined],
        ["/walrus/giraffe", "/walrus/bug/", undefined],
        ["/walrus/giraffe", "/walrus/giraffeXYZ", undefined],
        ["/walrus/giraffe", "/walrus/gir", undefined],
        ["/walrus/giraffe", "/walrus/giraffe/..", undefined],

        /*
         * Relative path names.
         */
        // A directory contains itself.
        ["walrus/giraffe", "walrus/giraffe", ""],
        ["walrus/giraffe", "walrus/giraffe/", ""],
        ["walrus/giraffe", "walrus/giraffe//", ""],
        ["walrus/giraffe/", "walrus/giraffe", ""],
        ["walrus/giraffe/", "walrus/giraffe/", ""],
        ["walrus/giraffe/", "walrus/giraffe//", ""],
        ["walrus/giraffe//", "walrus/giraffe", ""],
        ["walrus/giraffe//", "walrus/giraffe/", ""],
        ["walrus/giraffe//", "walrus/giraffe//", ""],
        ["walrus/giraffe", "walrus/giraffe/./.", ""],
        ["walrus/giraffe", "walrus/giraffe/../giraffe", ""],

        // // Pathname within directory.
        ["walrus/giraffe", "walrus/giraffe/bug", "bug"],
        ["walrus/giraffe", "walrus/giraffe/bug/", "bug"],
        ["walrus/giraffe", "walrus/giraffe/bug//", "bug"],
        ["walrus/giraffe/", "walrus/giraffe/bug", "bug"],
        ["walrus/giraffe/", "walrus/giraffe/bug/", "bug"],
        ["walrus/giraffe/", "walrus/giraffe/bug//", "bug"],
        ["walrus/giraffe//", "walrus/giraffe/bug", "bug"],
        ["walrus/giraffe//", "walrus/giraffe/bug/", "bug"],
        ["walrus/giraffe//", "walrus/giraffe/bug//", "bug"],
        ["walrus/giraffe", "walrus/giraffe/././bug", "bug"],
        ["walrus/giraffe", "walrus/giraffe/../giraffe/bug", "bug"],

        // Pathname not within directory.
        ["walrus/giraffe", "dog/cat/pigeon", undefined],
        ["walrus/giraffe", "walrus/bug", undefined],
        ["walrus/giraffe", "walrus/bug/", undefined],
        ["walrus/giraffe", "walrus/giraffeXYZ", undefined],
        ["walrus/giraffe", "walrus/gir", undefined],
        ["walrus/giraffe", "walrus/giraffe/..", undefined],

        // Funny cases
        ["/walrus/giraffe", "/walrus/giraffe/..a", "..a"],
        ["", "abc", "abc"],
        ["", "../abc", undefined],
    ])(
        'descendentPath and directoryContainsPath("%s", "%s")',
        (dirName: string, pathName: string, expectedRelPath: string | undefined) => {
            const relPath = descendentPath(dirName, pathName);
            expect(relPath).toBe(expectedRelPath);
            expect(directoryContainsPath(dirName, pathName)).toBe(expectedRelPath !== undefined);
        }
    );

    test.each([
        /*
         * Absolute path names.
         */
        ["/", "/", true],
        ["/walrus/giraffe", "/walrus/giraffe", true],
        ["/walrus/giraffe", "/walrus/giraffe/", true],
        ["/walrus/giraffe", "/walrus/giraffe//", true],
        ["/walrus/giraffe/", "/walrus/giraffe", true],
        ["/walrus/giraffe/", "/walrus/giraffe/", true],
        ["/walrus/giraffe/", "/walrus/giraffe//", true],
        ["/walrus/giraffe//", "/walrus/giraffe", true],
        ["/walrus/giraffe//", "/walrus/giraffe/", true],
        ["/walrus/giraffe//", "/walrus/giraffe//", true],
        ["/walrus/giraffe", "/walrus/giraffe/./.", true],
        ["/walrus/giraffe", "/walrus/giraffe/../giraffe", true],

        ["/walrus/giraffe", "/walrus/giraffe/bug", false],
        ["/walrus/giraffe", "/walrus/giraffe/bug/", false],
        ["/walrus/giraffe", "/walrus/giraffe/bug//", false],

        ["/walrus/giraffe", "/dog/cat/pigeon", false],
        ["/walrus/giraffe", "/walrus/bug", false],
        ["/walrus/giraffe", "/walrus/bug/", false],
        ["/walrus/giraffe", "/walrus/giraffeXYZ", false],
        ["/walrus/giraffe", "/walrus/gir", false],
        ["/walrus/giraffe", "/walrus/giraffe/..", false],

        /*
         * Relative path names.
         */
        [".", ".", true],
        ["..", "..", true],
        ["walrus/giraffe", "walrus/giraffe", true],
        ["walrus/giraffe", "walrus/giraffe/", true],
        ["walrus/giraffe", "walrus/giraffe//", true],
        ["walrus/giraffe/", "walrus/giraffe", true],
        ["walrus/giraffe/", "walrus/giraffe/", true],
        ["walrus/giraffe/", "walrus/giraffe//", true],
        ["walrus/giraffe//", "walrus/giraffe", true],
        ["walrus/giraffe//", "walrus/giraffe/", true],
        ["walrus/giraffe//", "walrus/giraffe//", true],
        ["walrus/giraffe", "walrus/giraffe/./.", true],
        ["walrus/giraffe", "walrus/giraffe/../giraffe", true],

        // Pathname within directory.
        ["walrus/giraffe", "walrus/giraffe/bug", false],
        ["walrus/giraffe", "walrus/giraffe/bug/", false],
        ["walrus/giraffe", "walrus/giraffe/bug//", false],

        // Pathname not within directory.
        ["walrus/giraffe", "dog/cat/pigeon", false],
        ["walrus/giraffe", "walrus/bug", false],
        ["walrus/giraffe", "walrus/bug/", false],
        ["walrus/giraffe", "walrus/giraffeXYZ", false],
        ["walrus/giraffe", "walrus/gir", false],
        ["walrus/giraffe", "walrus/giraffe/..", false],
    ])("sameDirectory(%s, %s)", (dirName: string, pathName: string, contains: boolean) => {
        expect(sameDirectory(dirName, pathName)).toBe(contains);
    });

    test.each([
        ["walrus/giraffe/spider.py", "walrus"],
        ["walrus", "walrus"],
        ["walrus/", "walrus"],
        ["./walrus", "."],
        [".", "."],
        ["/", ""],
        ["", ""],
    ])("firstPathComponent(%s)", (path: string, expected: string) => {
        expect(firstPathComponent(path)).toBe(expected);
    });

    test.each([
        ["walrus/giraffe/spider.py", "walrus"],
        ["walrus\\giraffe/spider.py", "walrus"],
        ["walrus/giraffe\\spider.py", "walrus"],
        ["walrus", "walrus"],
        ["walrus/", "walrus"],
        ["walrus\\", "walrus"],
        ["./walrus", "."],
        [".\\walrus", "."],
        [".", "."],
        ["/", ""],
        ["\\", ""],
        ["", ""],
    ])("firstPathComponent with windows path separator(%s)", (path: string, expected: string) => {
        expect(firstPathComponent(path, "\\")).toBe(expected);
    });

    test.each([
        ["/walrus/giraffe/spider.py", true],
        ["walrus/giraffe/spider.py", false],
        ["/", true],
        [".", false],
        ["", false],
        ["/walrus", true],
        ["walrus", false],
    ])("isAbsolutePathName(%s)", (path: string, expected: boolean) => {
        expect(isAbsolutePathName(path)).toBe(expected);
    });

    test.each([
        ["walrus/giraffe/spider.py", ["walrus", "giraffe", "spider.py"]],
        ["walrus", ["walrus"]],
        ["walrus/", ["walrus"]],
        ["./walrus", ["walrus"]],
        ["././walrus", ["walrus"]],
        ["./././walrus", ["walrus"]],
        [".", []],
        ["./", []],
        ["./.", []],
        ["././", []],
        ["././.", []],
        ["./././", []],
        ["./././.", []],
        ["", []],
        ["walrus////giraffe////spider.py", ["walrus", "giraffe", "spider.py"]],
        ["walrus///", ["walrus"]],
        ["walrus/./giraffe", ["walrus", "giraffe"]],
        ["walrus/././giraffe", ["walrus", "giraffe"]],
        ["walrus/./././giraffe", ["walrus", "giraffe"]],
        ["walrus/giraffe/.", ["walrus", "giraffe"]],
        ["walrus/giraffe/./", ["walrus", "giraffe"]],
        ["walrus/giraffe/./.", ["walrus", "giraffe"]],
        [".//.///././//walrus/.//.///./giraffe/.//.", ["walrus", "giraffe"]],
        [".//.///././//walrus/.//.///./giraffe/.//.////", ["walrus", "giraffe"]],
    ])('splitRelPath("%s")', (path: string, expected: Array<string>) => {
        const components = splitRelPath(path);
        expect(components.length).toBe(expected.length);
        for (let i = 0; i < components.length; i++) {
            expect(components[i]).toBe(expected[i]);
        }
    });

    test("splitRelPath throws on absolute path", () => {
        expect(() => splitRelPath("/walrus/giraffe")).toThrow();
    });
});
