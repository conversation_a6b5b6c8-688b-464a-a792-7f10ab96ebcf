import { APIServer, CompletionResolution } from "../augment-api";
import { msecToTimestamp } from "../utils/time";
import { MetricsReporter } from "./metrics-reporter";

export class CompletionAcceptanceReporter extends MetricsReporter<CompletionResolution> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1000;
    public static defaultUploadMsec = 10000;

    constructor(
        private _apiServer: APIServer,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "CompletionAcceptanceReporter",
            maxRecords ?? CompletionAcceptanceReporter.defaultMaxRecords,
            uploadMs ?? CompletionAcceptanceReporter.defaultUploadMsec,
            batchSize ?? CompletionAcceptanceReporter.defaultBatchSize
        );
    }

    // reportResolution reports on the resolution of a completion request.
    public reportResolution(
        requestId: string,
        emitTime: number,
        resolveTime: number,
        acceptedIndex: number | undefined
    ): void {
        const [emitTimeSec, emitTimeNsec] = msecToTimestamp(emitTime);
        const [resolveTimeSec, resolveTimeNsec] = msecToTimestamp(resolveTime);
        /* eslint-disable @typescript-eslint/naming-convention */
        this.report({
            request_id: requestId,
            emit_time_sec: emitTimeSec,
            emit_time_nsec: emitTimeNsec,
            resolve_time_sec: resolveTimeSec,
            resolve_time_nsec: resolveTimeNsec,
            accepted_idx: acceptedIndex ?? -1,
        });
    }

    protected performUpload(batch: CompletionResolution[]): Promise<void> {
        return this._apiServer.resolveCompletions(batch);
    }
}
