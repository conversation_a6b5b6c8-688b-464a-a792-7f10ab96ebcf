import { APIServer, ClientMetric } from "../augment-api";
import { MetricsReporter } from "./metrics-reporter";

export class ClientMetricsReporter extends MetricsReporter<ClientMetric> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 500;
    public static defaultUploadMsec = 10000;

    constructor(private _apiServer: APIServer) {
        super(
            "ClientMetricsReporter",
            ClientMetricsReporter.defaultMaxRecords,
            ClientMetricsReporter.defaultUploadMsec,
            ClientMetricsReporter.defaultBatchSize
        );
    }

    protected performUpload(batch: ClientMetric[]): Promise<void> {
        return this._apiServer.clientMetrics(batch);
    }
}
