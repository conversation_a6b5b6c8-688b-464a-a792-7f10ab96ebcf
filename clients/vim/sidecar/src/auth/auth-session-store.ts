import { isEqual } from "lodash";

import { AugmentConfigListener } from "../augment-config-listener";
import { DisposableService } from "../utils/disposable-service";
import * as vscode from "../vscode";

const SESSION_KEY = "augment.sessions";
export const SESSION_SCOPES = ["email"];

// The AuthSessionStore is responsible for storing and retrieving the users
// OAuth session.
// The `onDidChangeSession` event can be used to listen for changes.
export class AuthSessionStore extends DisposableService {
    // Event emitter for when sign in / out events occur
    private _sessionChangeEmitter = new vscode.EventEmitter<AugmentSession | null>();
    // Event emitter for when the AuthSessionStore is ready
    private _readyEmitter = new vscode.EventEmitter<void>();
    // Track the current logged in state
    private _isLoggedIn: boolean | undefined;

    // Ready allows tests to wait for this class to set up it's initial state
    // that requires async calls.
    protected readonly _ready: Promise<void>;

    constructor(
        private _context: vscode.ExtensionContext,
        private _config: AugmentConfigListener
    ) {
        super();

        this.addDisposables(
            // Set context when the user is logged in or not.
            this.onDidChangeSession((session) => {
                this._isLoggedIn = !!session;
            }),

            // Trigger the session change when the extensions session state changes
            this._context.secrets.onDidChange(async (e) => {
                if (e.key !== SESSION_KEY) {
                    return;
                }
                this._sessionChangeEmitter.fire(await this.getSession());
            })

            // Set context when the config changes and impacts the use of OAuth
        );

        this._ready = this.initState();
    }

    get onDidChangeSession() {
        return this._sessionChangeEmitter.event;
    }

    get onReady() {
        return this._readyEmitter.event;
    }

    get useOAuth(): boolean {
        const config = this._config.config;
        return (
            !!config.oauth &&
            !!config.oauth.url &&
            !!config.oauth.clientID &&
            !config.apiToken &&
            !config.completionURL
        );
    }

    async initState() {
        this._isLoggedIn = !!(await this.getSession());
        this._readyEmitter.fire();
    }

    // isLoggedIn has an async initialization, so it has three possible
    // states, initially it's undefined, then it's true or false.
    get isLoggedIn(): boolean | undefined {
        return this._isLoggedIn;
    }

    // Save the session to the secret store
    public async saveSession(accessToken: string, tenantURL: string): Promise<void> {
        await this._context.secrets.store(
            SESSION_KEY,
            JSON.stringify({
                accessToken,
                tenantURL,
                scopes: SESSION_SCOPES,
            })
        );
    }

    // Get the current session if one is available
    public async getSession(): Promise<AugmentSession | null> {
        const sessionData = await this._context.secrets.get(SESSION_KEY);
        if (sessionData) {
            const session = JSON.parse(sessionData) as AugmentSession;
            // Check the scopes to ensure they haven't changed meaning we need to
            // re-authenticate.
            if (isEqual(session.scopes, SESSION_SCOPES)) {
                return session;
            }
        }
        return null;
    }

    // Remove the session from the secret store (Essentially sign out)
    public async removeSession(): Promise<void> {
        const session = await this._context.secrets.get(SESSION_KEY);
        if (!session) {
            return;
        }
        await this._context.secrets.delete(SESSION_KEY);
    }
}

export interface AugmentSession {
    accessToken: string;
    scopes: string[];
    tenantURL: string;
}
