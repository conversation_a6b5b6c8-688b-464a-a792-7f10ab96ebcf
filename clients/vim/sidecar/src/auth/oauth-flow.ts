import { createHash, randomBytes } from "crypto";
import { URL } from "url";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { getErrmsg } from "../exceptions";
import { getLogger } from "../logging";
// Used for secure storage
import * as vscode from "../vscode";
import { AuthSessionStore } from "./auth-session-store";

const OAUTH_KEY = "augment.oauth-state"; // pragma: allowlist secret
const AUGMENT_HOSTNAME = process.env.TEST_HOSTNAME ?? ".augmentcode.com";
const SIGN_IN_TIMEOUT_MINS = 10;

// This class manages the OAuth flow.
// Multiple sign in attempts are handled by this class.
// If the user attempts to sign-in multiple times in the same window, previous
// attempts are cancelled before starting a new flow.
// If the user has multiple windows opened, the OAuth state is stores in
// VSCode secrets, so any window can handle the browser -> VSCode Uri.
// The resulting session is stored in the AuthSessionStore, which should be
// used to get the session and listen for changes.
export class OAuthFlow {
    private _logger = getLogger("OAuthFlow");

    // Programmatically cancel any pending OAuth flow
    // private _programmaticCancellation = new vscode.EventEmitter<string>();

    // Any previous login promise
    // private _previousLogin: Promise<AugmentSession> | undefined;

    // The extension can only have one Uri handler, so we expose the auth URI
    // so that the extension can compare the path and parse the URI to the auth
    // instance.
    public readonly authRedirectURI: vscode.Uri;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private _config: AugmentConfigListener,
        private _apiServer: APIServer,
        private _authSession: AuthSessionStore
        // private _onboardingSessionEventReporter: OnboardingSessionEventReporter
    ) {
        this.authRedirectURI = vscode.Uri.from({
            scheme: "vscode",
            authority: "augment.vscode-augment",
            path: "/auth/result",
        });
    }

    // public doProgrammaticCancellation() {
    //     this._programmaticCancellation.fire("Cancelled by user");
    // }

    public async startFlow(/* withProgress: boolean = true */): Promise<string> {
        // try {
        // Cancel any pending OAuth flows.
        // this._programmaticCancellation.fire("Cancelled due to new sign in");

        // Wait for any pending logins to stop
        // await Promise.allSettled([this._previousLogin]);

        // Start the new login
        this._logger.info(`Creating new session...`);
        // let session: AugmentSession;
        // if (withProgress) {
        //     session = await this.loginWithProgress();
        // } else {
        const authUri = await this.login();

        // _Do_ skip encoding the string, since we're displaying it.
        const urlString = authUri.toString(true);
        return urlString;
        // }
        // this._logger.info(`Created session ${session.tenantURL}`);
        // this._onboardingSessionEventReporter.reportEvent(OnboardingSessionEventName.SignedIn);
        // } catch (e) {
        // void vscode.window.showErrorMessage(`Sign in failed. ${getErrmsg(e)}`);
        //   throw e;
        // }
        // TODO: In the original code, we wait for an event and then clean up the state
    }

    private async createOAuthState(): Promise<OAuthState> {
        this._logger.info("Creating OAuth state");
        const verifier = base64URLEncode(randomBytes(32));
        const challenge = base64URLEncode(sha256(Buffer.from(verifier)));
        // NOTE(mpauly): 8 bytes of randomness (vs 16 in a uuid) should be fine for the state and lets us shorten the url
        const state = base64URLEncode(randomBytes(8));
        const oauthState: OAuthState = {
            codeVerifier: verifier,
            codeChallenge: challenge,
            state,
            creationTime: new Date().getTime(),
        };
        await this._context.secrets.store(OAUTH_KEY, JSON.stringify(oauthState));
        this._logger.info("Created OAuth state");
        return oauthState;
    }

    private async getOAuthState(): Promise<OAuthState | null> {
        this._logger.info("Getting OAuth state");
        const stateData = await this._context.secrets.get(OAUTH_KEY);
        if (stateData) {
            const state = JSON.parse(stateData) as OAuthState;
            // Ensure the state is not too old
            if (new Date().getTime() - state.creationTime < SIGN_IN_TIMEOUT_MINS * 60 * 1000) {
                return state;
            }
        }
        return null;
    }

    private async removeOAuthState(): Promise<void> {
        this._logger.info("Removing OAuth state");
        const state = await this._context.secrets.get(OAUTH_KEY);
        if (!state) {
            return;
        }
        await this._context.secrets.delete(OAUTH_KEY);
    }

    // Login the user via OAuth flow
    private async login(): Promise<vscode.Uri> {
        // Produce the authorization url. An external action will open the link.
        try {
            // Create the state for the OAuth flow
            const oauthState = await this.createOAuthState();
            return this.generateAuthorizeURL(oauthState);
        } catch (e) {
            await this.removeOAuthState();
            throw e;
        }
        // finally {
        //     // Once one of the events above has triggered we no longer need the OAuth state
        //     await this.removeOAuthState();
        // }
    }

    // Open the browser for the user to sign in
    private generateAuthorizeURL(oauthState: OAuthState): vscode.Uri {
        // NOTE(mpauly): For vim, we drop the scope, redirect_uri, and code_challenge_method as
        // they aren't strictly necessary (defaults will be used automatically). This allows the
        // auth url to be shorter which prevents truncation.
        const searchParams = new URLSearchParams({
            /* eslint-disable @typescript-eslint/naming-convention */
            response_type: "code",
            code_challenge: oauthState.codeChallenge,
            client_id: this._config.config.oauth.clientID || "",
            /* eslint-enable @typescript-eslint/naming-convention */
            state: oauthState.state,
            prompt: "login",
        });

        const authorizeURL = new URL(
            `/authorize?${searchParams.toString()}`,
            this._config.config.oauth.url
        );
        const uri = vscode.Uri.parse(authorizeURL.toString());

        // VSCode allows the user to copy the URL, and openExternal will return
        // false when this occurs, so no need to check the return value.
        // await vscode.env.openExternal(uri);
        return uri;
    }

    private async processAuthRedirect(uri: vscode.Uri) {
        const query = new URLSearchParams(uri.query);
        const state = query.get("state");
        if (!state) {
            throw new Error("No state");
        }
        const oauthState = await this.getOAuthState();
        if (!oauthState) {
            throw new Error("No OAuth state found");
        }

        // Since we have a URI response, we won't need this state again.
        await this.removeOAuthState();

        if (oauthState.state !== state) {
            throw new Error("Unknown state");
        }

        const error = query.get("error");
        if (error) {
            const parts: string[] = [`(${error})`];
            const errorDescrip = query.get("error_description");
            if (errorDescrip) {
                parts.push(errorDescrip);
            }
            throw new Error(`OAuth request failed: ${parts.join(" ")}`);
        }

        const code = query.get("code");
        if (!code) {
            throw new Error("No code");
        }

        const tenantURL = query.get("tenant_url");

        if (!tenantURL) {
            throw new Error("No tenant URL");
        } else if (!new URL(tenantURL).hostname.endsWith(AUGMENT_HOSTNAME)) {
            throw new Error("OAuth request failed: invalid OAuth tenant URL");
        }

        // Swap the code for an access token
        try {
            this._logger.info("Calling get access token to retrieved access token");
            const accessToken = await this._apiServer.getAccessToken(
                "", // This is the redirect URI, which should be an empty string for vim
                tenantURL,
                oauthState.codeVerifier,
                code
            );
            await this._authSession.saveSession(accessToken, tenantURL);
            this._logger.info("Successfully retrieved access token");
        } catch (err) {
            this._logger.error(`Failed to get and save access token: ${getErrmsg(err)}`);
            throw new Error(`If you have a firewall, please add "${tenantURL}" to your allowlist.`);
        }
    }

    // When a URI is received by VSCode, our URI handler will call this method
    public async handleAuthURI(uri: vscode.Uri) {
        try {
            await this.processAuthRedirect(uri);
        } catch (err) {
            this._logger.warn("Failed to process auth request:", err);
            // If we run into errors, cancel any in-progress OAuth UI
            // this._programmaticCancellation.fire(getErrmsg(err));
        }
    }

    public async handleAuthJson(jsonData: string): Promise<string> {
        // TODO: Form a url string just to reuse the processing code. This
        // should be cleaned up.
        const args = JSON.parse(jsonData) as AuthArgs;
        const oauthState = await this.getOAuthState();
        if (!oauthState) {
            throw new Error("No OAuth state found");
        }
        const uri = this.authRedirectURI.with({
            query: `code=${args.code}&state=${args.state}&tenant_url=${args.tenant_url}`,
        });
        await this.processAuthRedirect(uri);
        return "success";
    }
}

export function base64URLEncode(data: Buffer): string {
    return data.toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}

function sha256(buffer: Buffer): Buffer {
    return createHash("sha256").update(buffer).digest();
}

interface OAuthState {
    codeVerifier: string;
    codeChallenge: string;
    state: string;
    creationTime: number;
}

// Represents the decoded json data and uses the original key names
interface AuthArgs {
    code: string;
    state: string;
    /* eslint-disable @typescript-eslint/naming-convention */
    tenant_url: string;
    /* eslint-enable @typescript-eslint/naming-convention */
}
