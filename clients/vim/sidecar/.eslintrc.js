const path = require("path");

module.exports = {
    root: true,
    extends: ["eslint:recommended", "plugin:@typescript-eslint/recommended-type-checked"],
    env: {
        node: true,
    },
    globals: {
        // These were added to make linting pass when adding eslint:recommended.
        // We may be able to remove them by improving our typing or updating
        // eslint version and/or config.
        NodeJS: true,
        Thenable: true,
        AsyncGenerator: true,
        RequestInfo: true,
        RequestInit: true,
    },
    parser: "@typescript-eslint/parser",
    parserOptions: {
        ecmaVersion: 6,
        sourceType: "module",
        project: true,
    },
    plugins: ["@typescript-eslint", "unused-imports"],
    rules: {
        "@typescript-eslint/naming-convention": "error",
        "@typescript-eslint/semi": "warn",
        curly: "warn",
        eqeqeq: ["error", "smart"],
        "no-throw-literal": "warn",
        "no-console": "error",
        semi: "off",
        "no-empty": ["error", { allowEmptyCatch: true }],
        "prefer-arrow-callback": "error",

        // https://www.npmjs.com/package/eslint-plugin-unused-imports
        "no-unused-vars": "off", // or "@typescript-eslint/no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
            "error",
            {
                vars: "all",
                varsIgnorePattern: "^_",
                args: "after-used",
                argsIgnorePattern: "^_",
            },
        ],

        // The following rules were disabled due to errors when adding
        // eslint:recommended.
        // Please feel free to enable the rule if you want to.
        "no-var": "off",
        "no-constant-condition": "off",
        "no-async-promise-executor": "off",
        "prefer-const": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-extra-semi": "off",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/ban-types": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-inferrable-types": "off",
        "@typescript-eslint/no-unnecessary-type-constraint": "off",
    },
    ignorePatterns: [
        "out",
        "dist",
        "**/*.d.ts",
        "jest.config.js",
        "src/third-party",
        ".eslintrc.js",
    ],
    overrides: [
        {
            extends: ["plugin:jest/recommended"],
            files: ["**/__tests__/**/*.test.ts", "**/__mocks__/**/*"],
            env: {
                jest: true,
            },
            rules: {
                // The following rules were disabled due to errors when adding
                // plugin:jest/recommended.
                // Please feel free to enable the rule if you want to.
                "jest/expect-expect": "off",
                "jest/no-standalone-expect": "off",
                "jest/no-mocks-import": "off",
                "jest/no-commented-out-tests": "off",
                "jest/valid-expect": "off",
                "jest/no-conditional-expect": "off",
                "jest/no-alias-methods": "off",
                "@typescript-eslint/no-namespace": "off",
                "@typescript-eslint/no-empty-interface": "off",
                "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
                "@typescript-eslint/require-await": "off",
                "@typescript-eslint/unbound-method": "off",
                "@typescript-eslint/no-floating-promises": "off",
                "@typescript-eslint/no-unsafe-argument": "off",
                "@typescript-eslint/no-unsafe-call": "off",
                "@typescript-eslint/no-misused-promises": "off",
                "@typescript-eslint/no-unsafe-assignment": "off",
                "@typescript-eslint/no-unsafe-member-access": "off",
                "@typescript-eslint/no-unsafe-return": "off",
            },
        },
        {
            extends: ["plugin:mocha/recommended"],
            parserOptions: {
                // eslint doesn't treat "./" as the root of the directory
                // .eslint.json is in, it's treated as augment/ by pre-commit.
                project: path.join(__dirname, "tsconfig.e2e.json"),
            },
            files: ["**/__tests__/**/*.e2e.ts", "wdio.conf.ts"],
            env: {
                mocha: true,
            },
            rules: {
                "no-console": "off",
                "mocha/no-mocha-arrows": "off",
            },
        },
    ],
};
