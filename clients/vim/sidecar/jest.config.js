module.exports = {
    roots: ["<rootDir>/src"],
    transform: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "^.+\\.ts?$": "ts-jest",
    },
    testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.test.ts?$",
    moduleFileExtensions: ["ts", "js", "json", "node"],
    collectCoverage: true,
    clearMocks: true,
    resetMocks: false,
    coverageDirectory: "coverage",
    testEnvironment: "node",
    setupFilesAfterEnv: ["<rootDir>/src/__mocks__/setup-jest.ts"],
    // Any local modules that we wish to mock need to be added to this list.
    moduleNameMapper: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "^.*/os$": "<rootDir>/src/__mocks__/os.ts",
        "^.*/fs-utils$": "<rootDir>/src/__mocks__/fs-utils.ts",
        "^.*/fs-watcher$": "<rootDir>/src/__mocks__/fs-watcher.ts",
    },
};
