# Vim Plugin Sidecar

## Overview

This directory contains the code for the LSP server (or "sidecar") used by the
Vim plugin. The server communicates over standard in and out using the Language
Server Protocal (LSP) which is a set of messages defined over JSON-RPC.

When the Vim plugin (located in `clients/vim/plugin`) is loaded, it launches an
instance of the sidecar and begins sending document updates and completion
requests. The sidecar handles all interaction with Augment API (auth +
completions). We currently do not perform any workspace management, but once we
add that it that functionality will live in the sidecar as well.

The sidecar uses the `vscode-languageserver-node` npm package which provides
handlers and types for LSP messages.


## Development

- `npm install` to get node dependencies
- `npm run build` to build to javascript files in `dist/` with entrypoint
  `server.js`
- `npm run watch` to run the `build` target whenever the source files are
  modified (useful for development)
- `npm run build:bundle` to build single minified file `dist/server.bundle.js`

## Notes on the Implementation

This lsp server implementation is mostly a port of the VSCode extension. We try
to keep the directory structure, file names, and file content consistent with
the VSCode implementation. In other words, we treat the VSCode directory as an
upstream dependency. This simplifies taking file differences to track changes
in the upstream extension, since there is still ongoing work there.

One difference worth noting: The VSCode extension depends on some vscode
specific types. The sidecar re-implements these types to work in the node
environment. Those changes always go into `src/vscode.ts`. When porting files
over, we typically have to change the path of the vscode import, like:
`import * as vscode from "vscode"` then becomes `import * as vscode from "./vscode"`

* We do not support Notebooks

## References

- [LSP Specification](https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/)
- [vscode-languageserver-node npm package](https://github.com/microsoft/vscode-languageserver-node?tab=readme-ov-file)
- [VSCode Language Server Extension Guide](https://code.visualstudio.com/api/language-extensions/language-server-extension-guide)
