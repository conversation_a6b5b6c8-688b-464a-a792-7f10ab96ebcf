" Test the status command and g:augment_disable_completions variable

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function BeforeTest()
    " Since the tests can be ran in any order, ensure we're always starting enabled
    let g:augment_disable_completions = v:false

    edit test.py
    messages clear
endfunction

function Test_status()
    Augment status

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Signed in\.', messages)
endfunction

function Test_status_disable()
    let g:augment_disable_completions = v:true
    Augment status

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Signed in, completions disabled\.', messages)

    messages clear

    let g:augment_disable_completions = v:false
    Augment status

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Signed in\.', messages)
endfunction
