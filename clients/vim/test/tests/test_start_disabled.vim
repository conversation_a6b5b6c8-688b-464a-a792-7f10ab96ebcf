" Test the g:augment_disable_completions option

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'

    " Set the g:augment_disable_completions option to true to start disabled
    let g:augment_disable_completions = v:true

    call SetupPlugin('mock_server')
endfunction

function Test_start_disabled()
    edit test.py

    messages clear
    Augment status

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Signed in, completions disabled', messages)
endfunction
