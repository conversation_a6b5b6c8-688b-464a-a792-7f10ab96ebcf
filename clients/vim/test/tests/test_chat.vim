" Basic tests for chat functionality

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function Test_simple()
    edit test.py

    " Setup a variable to track the chat response
    let g:_chat_response = v:false
    augroup augment_test
        autocmd!
        autocmd User ChatResponse let g:_chat_response = v:true
    augroup END

    " We can add keys to the input buffer that will be executed when input()
    " is called in the chat command
    call feedkeys("test message\<cr>", 'nt')
    Augment chat

    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'File:',
                \ 'test.py',
                \ '',
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction
