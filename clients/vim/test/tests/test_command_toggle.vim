" Test the `:Augment chat-toggle` command

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function BeforeTest()
    edit test.py

    " If the panel is open, close it
    let chat_id = bufwinid('AugmentChatHistory')
    if chat_id != -1
        call win_execute(chat_id, 'close')
    endif
endfunction

function Test_toggle()
    " Initially, chat panel should not exist
    let chat_id = bufwinid('AugmentChatHistory')
    call assert_equal(-1, chat_id)

    Augment chat-toggle
    let chat_id = bufwinid('AugmentChatHistory')
    call assert_notequal(-1, chat_id)

    Augment chat-toggle
    let chat_id = bufwinid('AugmentChatHistory')
    call assert_equal(-1, chat_id)
endfunction

function Test_toggle_after_chat()
    " Initially, chat panel should not exist
    let chat_id = bufwinid('AugmentChatHistory')
    call assert_equal(-1, chat_id)

    " Request a chat and wait for the response to be received
    let g:_chat_response = v:false
    augroup augment_test
        autocmd!
        autocmd User ChatResponse let g:_chat_response = v:true
    augroup END

    call feedkeys("test message\<cr>", 'nt')
    Augment chat
    call WaitFor({-> g:_chat_response})

    " Chat panel should now exist
    let chat_id = bufwinid('AugmentChatHistory')
    call assert_notequal(-1, chat_id)

    " Toggling should close the panel
    Augment chat-toggle
    let chat_id = bufwinid('AugmentChatHistory')
    call assert_equal(-1, chat_id)
endfunction
