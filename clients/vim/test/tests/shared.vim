" Shared utils for the vim client tests

function SetupPlugin(command) abort
    " If the provided command is a string, treat it as a server name. If it's
    " a list, treat it as a custom command.
    if type(a:command) == v:t_string
        let client_root = expand('<script>:p:h:h:h')
        let runtime = client_root . '/test/server/server_wrapper.sh'
        let server_file = client_root . '/test/server/' . a:command . '.py'
        let g:augment_job_command = [runtime, server_file]

    elseif type(a:command) == v:t_list
        let g:augment_job_command = a:command
    else
        throw 'Invalid startup command: ' . string(a:command)
    endif

    " Set the log file
    let g:augment_log_file = expand('<script>:p:h:h') . '/logs/plugin.log'

    " Source the plugin
    let plugin_root = expand('<script>:p:h:h:h') . '/plugin'
    let &runtimepath .= ',' . plugin_root
    execute 'source ' . plugin_root . '/plugin/augment.vim'
endfunction

" Check the current state of the buffer and if it's as expected accept the completion
function Accept(buf_expected) abort
    if type(a:buf_expected) == type([])
        let expected_list = a:buf_expected
    else
        let expected_list = [a:buf_expected]
    endif

    if expected_list ==# getline(1, '$')
        " Remove the autocommand
        augroup augment_test
            autocmd!
        augroup END

        call feedkeys("\<tab>")

        " Feedkeys is weird.. need to wait until next pass of event loop to send escape
        call timer_start(0, {_ -> feedkeys("\<esc>")})
    endif
endfunction

" Setup an autocomand to accept the completion when the buffer is as expected.
" We need to check the buffer state so that we don't accept the completion too
" early.
function AcceptOn(buf_expected) abort
    " Need to create a copy in script-local scope for when the autocommand fires
    let s:buf_expected = a:buf_expected

    augroup augment_test
        autocmd!
        autocmd User CompletionUpdated call Accept(s:buf_expected)
    augroup END
endfunction

" Wait for a condition to be true. Argument `Condition` is a function that
" returns a boolean and optional `sleep_ms` is the number of milliseconds to
" sleep between checks.
function WaitFor(Condition, ...) abort
    let sleep_ms = get(a:, 1, 50)

    while !a:Condition()
        execute 'sleep ' . sleep_ms . 'm'
    endwhile
endfunction

function LogContains(message)
    Augment log
    let log = getline(1, '$')
    quit

    let match = match(log, a:message)
    return match != -1
endfunction

function CheckChatReponse(expected)
    let buf = bufnr('AugmentChatHistory')

    " Do a little arithmatic to account for the extra newlines added by the chat command
    let len = line('$', bufwinid(buf)) - 3
    let lines = getbufline(buf, len - len(a:expected) + 1, len)

    call assert_equal(a:expected, lines)
endfunction
