" Test the runtime check when a runtime is not found

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin(['not-a-runtime'])
endfunction

function Test_runtime_not_found()
    edit test.py

    " NOTE(mpauly): It's difficult to capture the message we echo, so look for
    " the log entry.

    " On success this will return and on failure the test will time out.
    call WaitFor({-> LogContains('The Augment runtime (not-a-runtime) was not found')})
endfunction

function Test_command_restriction()
    edit test.py
    messages clear

    " Chat should not work
    Augment chat
    call assert_equal(bufnr('AugmentChatHistory'), -1)
    call WaitFor({-> LogContains('The Augment plugin failed to initialize')})
endfunction
