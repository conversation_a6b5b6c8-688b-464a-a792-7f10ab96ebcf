" Basic tests for completions functionality

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server_empty')
endfunction

function Test_empty()
    edit test.py

    call AcceptOn('')
    call feedkeys('i', 'xt!')
    call assert_equal("\t", getline(1))
endfunction

function Test_empty_inline()
    edit test.py

    call AcceptOn('abc123')
    call feedkeys("iabc123\<esc>hh", 'xt')
    call feedkeys('i', 'xt!')
    call assert_equal("abc\t123", getline(1))
endfunction
