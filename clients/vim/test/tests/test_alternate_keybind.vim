" Test setting an alternate accept keybind

function Setup()
    " Disable the default tab mapping for Test_disable_tab. Note that this
    " must be set before the plugin is loaded.
    let g:augment_disable_tab_mapping = v:true

    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function Test_alternate_keybind()
    inoremap <c-y> <cmd>call augment#Accept()<cr>

    edit test.py

    " Use Ctrl-Y to accept the completion when it's ready
    function FeedCtrlY()
        call feedkeys("\<c-y>")
        " Feedkeys is weird.. need to wait until next pass of event loop to send escape
        call timer_start(0, {_ -> feedkeys("\<esc>")})
    endfunction

    augroup augment_test
        autocmd User CompletionUpdated call FeedCtrlY()
    augroup END

    call feedkeys('i', 'xt!')

    call assert_equal('test completion', getline(1))
    call assert_equal(col('.'), len('test completion'))
endfunction

function Test_disable_tab()
    edit test.py

    call AcceptOn('')
    call feedkeys('i', 'xt!')
    call assert_equal("\t", getline(1))
endfunction
