" Basic tests for completions functionality

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server_indentation')
endfunction

function Test_indentation()
    edit test.py

    call AcceptOn('')
    call feedkeys('i', 'xt!')
    let expected = [
                \ 'line one',
                \ '    4 spaces',
                \ '        8 spaces',
                \ "\t1 tab",
                \ "\t\t2 tabs",
                \ ]
    call assert_equal(expected, getline(1, '$'))
endfunction
