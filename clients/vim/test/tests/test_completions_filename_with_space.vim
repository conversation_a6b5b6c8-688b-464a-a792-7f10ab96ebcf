" Ensure completions work with a file that contains a space

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function Test_simple()
    edit test\ space.py

    call AcceptOn('')
    call feedkeys('i', 'xt!')
    call assert_equal('test completion', getline(1))
    call assert_equal(col('.'), len('test completion'))
endfunction
