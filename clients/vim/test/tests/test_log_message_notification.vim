" Test the window/logMessage notification

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server_notify')
endfunction

function Test_window_log_message()
    edit test.py

    call WaitFor({-> LogContains('\[ERROR\] Error message')})
    call WaitFor({-> LogContains('\[WARN\] Warning message')})
    call WaitFor({-> LogContains('\[INFO\] Info message')})
endfunction
