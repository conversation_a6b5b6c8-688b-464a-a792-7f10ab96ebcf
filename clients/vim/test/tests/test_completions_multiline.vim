" Basic tests for completions functionality

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server_multiline')
endfunction

function Test_multiline()
    edit test.py

    call AcceptOn('')
    call feedkeys('i', 'xt!')
    let expected = ['line one', 'line two', '', 'line three']
    call assert_equal(expected, getline(1, '$'))
    call assert_equal(line('.'), len(expected))
    call assert_equal(col('.'), len(expected[-1]))
endfunction

function Test_multiline_between()
    edit test.py

    call AcceptOn(['abc', 'prefix ', '123'])
    call feedkeys("iabc\<cr>prefix \<cr>123\<esc>k", 'xt')
    call feedkeys('A', 'xt!')
    let expected = ['abc', 'prefix line one', 'line two', '', 'line three', '123']
    call assert_equal(expected, getline(1, '$'))
    call assert_equal(line('.'), 5)
    call assert_equal(col('.'), len('line three'))
endfunction
