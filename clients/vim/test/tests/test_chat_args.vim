" Test providing the chat message in the command arguments

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function BeforeTest()
    " Setup a variable and autocommand to track the chat response
    let g:_chat_response = v:false
    augroup augment_test
        autocmd!
        autocmd User ChatResponse let g:_chat_response = v:true
    augroup END

    " Set buffer contents
    edit test.py
endfunction

function Test_args()
    Augment chat message in the args!
    call WaitFor({-> g:_chat_response})

    let reponse_expected = [
                \ 'Message:',
                \ 'message in the args!',
                \ '',
                \ 'Selected text:',
                \ '',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

function Test_args_many_spaces()
    Augment     chat     message in the args with many spaces!
    call WaitFor({-> g:_chat_response})

    let reponse_expected = [
                \ 'Message:',
                \ 'message in the args with many spaces!',
                \ '',
                \ 'Selected text:',
                \ '',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

function Test_args_with_tabs()
    execute "Augment\tchat\tmessage with tabs!"
    call WaitFor({-> g:_chat_response})

    let reponse_expected = [
                \ 'Message:',
                \ 'message with tabs!',
                \ '',
                \ 'Selected text:',
                \ '',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction
