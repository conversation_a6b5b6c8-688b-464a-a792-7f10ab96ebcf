" Test the check for an out-of-date runtime

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'

    " Set the mock runtime to one the will report a version of 18.0.0
    let client_root = expand('<script>:p:h:h:h')
    let runtime = client_root . '/test/server/server_wrapper_out_of_date.sh'
    let server_file = client_root . '/test/server/mock_server.py'
    let g:augment_job_command = [runtime, server_file]
    call SetupPlugin([runtime, server_file])
endfunction

function Test_runtime_out_of_date()
    edit test.py

    call WaitFor({-> LogContains('Unsupported runtime version: v18.0.0')})
endfunction
