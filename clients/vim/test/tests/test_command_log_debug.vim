" Test the `:Augment log` command with debug logging enabled

function Setup()
    " Enable debug logging
    let g:augment_debug = v:true

    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function Test_log_debug()
    edit test.py

    call augment#log#Debug('Test debug message')

    Augment log
    let log = getline(1, '$')

    " Check that the log contains debug messages
    let debug_match = match(log, '\[DEBUG\]')
    call assert_notequal(-1, debug_match, 'Expected to find [DEBUG] messages in log')

    " Check that debug was enabled in server via environment variable
    let log_level_env = getenv('AUGMENT_LOG_LEVEL')
    call assert_match('debug', log_level_env, 'Expected AUGMENT_LOG_LEVEL to be set to debug')
endfunction
