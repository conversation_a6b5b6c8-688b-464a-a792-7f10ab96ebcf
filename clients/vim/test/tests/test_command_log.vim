" Test the `:Augment log` command

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function Test_log()
    edit test.py

    call AcceptOn('')
    call feedkeys('i', 'xt!')
    call assert_equal('test completion', getline(1))

    Augment log
    let log = getline(1, '$')

    " Check that the log contains the startup message
    let match = match(log, '\[INFO\] Starting Augment Server v0\.0\.0')
    call assert_notequal(-1, match)
endfunction

function Test_log_levels()
    edit test.py

    call augment#log#Info('Test info message')
    call augment#log#Warn('Test warn message')
    call augment#log#Error('Test error message')
    call augment#log#Debug('Test debug message')

    Augment log
    let log = getline(1, '$')

    " Check that the log contains the expected messages
    let info_match = match(log, '\[INFO\] Test info message')
    call assert_notequal(-1, info_match, 'Expected to find INFO message in log')
    let warn_match = match(log, '\[WARN\] Test warn message')
    call assert_notequal(-1, warn_match, 'Expected to find WARN message in log')
    let error_match = match(log, '\[ERROR\] Test error message')
    call assert_notequal(-1, error_match, 'Expected to find ERROR message in log')

    " Since the default log level is info, the debug message should not be present
    let debug_match = match(log, '\[DEBUG\] Test debug message')
    call assert_equal(-1, debug_match, 'Expected to NOT find DEBUG message in log')
endfunction
