" Test the signin and signout commands

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function BeforeTest()
    edit test.py
    messages clear
endfunction

function Test_signin()
    " We can add keys to the input buffer that will be executed when
    " inputsecret() is called in the signin command
    call feedkeys("{\"state\": \"fake-state\"}\<cr>", 'nt')
    Augment signin

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Sign in successful', messages)
endfunction

function Test_signin_bad_code()
    " We can add keys to the input buffer that will be executed when
    " inputsecret() is called in the signin command
    call feedkeys("bad-code\<cr>", 'nt')
    Augment signin

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Error signing in: Invalid code', messages)
endfunction

function Test_signout()
    Augment signout

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Sign out successful', messages)
endfunction
