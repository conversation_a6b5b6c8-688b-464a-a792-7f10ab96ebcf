" Test that completions are not issued in nofile buffers

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server_no_completion')
endfunction

function Test_nofile()
    edit test.py

    " Set the buffer type to nofile, which should prevent completions
    set buftype=nofile

    " Attempt to trigger a completion
    call feedkeys('iabc', 'xt')

    " Check that the server is still running. With mock_server_no_completion, the
    " server will crash if it receives a completion request.
    messages clear
    Augment status
    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('Signed in\.', messages)
endfunction
