" Basic tests for completions functionality

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function Test_simple()
    edit test.py

    call AcceptOn('')
    call feedkeys('i', 'xt!')
    call assert_equal('test completion', getline(1))
    call assert_equal(col('.'), len('test completion'))
endfunction

function Test_inline()
    edit test.py

    call AcceptOn('abc123')
    call feedkeys("iabc123\<esc>hh", 'xt')
    call feedkeys('i', 'xt!')
    call assert_equal('abctest completion123', getline(1))
    call assert_equal(col('.'), len('test completion') + 3)
endfunction
