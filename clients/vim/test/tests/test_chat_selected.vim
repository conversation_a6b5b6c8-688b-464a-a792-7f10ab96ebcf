" Test chat with selected test

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server')
endfunction

function BeforeTest()
    " Setup a variable and autocommand to track the chat response
    let g:_chat_response = v:false
    augroup augment_test
        autocmd!
        autocmd User ChatResponse let g:_chat_response = v:true
    augroup END

    " Set buffer contents
    edit test.py
    call feedkeys("i123\<cr>456\<cr>789", 'xt')
endfunction

" Test visual line mode (<cmd>Augment chat)
function Test_chat_cmd_binding_visual_line()
    call feedkeys("gg0Vj", 'xt')
    call feedkeys("test message\<cr>", 'nt')
    Augment chat
    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '123',
                \ '456',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

" Test visual line mode where the selection starts at the bottom (<cmd>Augment
" chat). We need to do extra checks for the `<cmd>` case because the . and v
" marks don't work quite the same as '< and '>
function Test_chat_cmd_binding_visual_line_reverse()
    call feedkeys("gg0jVk", 'xt')
    call feedkeys("test message\<cr>", 'nt')
    Augment chat
    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '123',
                \ '456',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

" TODO(AU-6118): Selected text in visual mode is broken when calling the
" command with <cmd>

" " Test visual mode (<cmd>Augment chat)
" function Test_chat_cmd_binding_visual()
"     call feedkeys("gg0vll", 'xt')
"     call feedkeys("test message\<cr>", 'nt')
"     Augment chat
"     call WaitFor({-> g:_chat_response})
"
"     " Check response buffer
"     call assert_match('AugmentChat-', bufname('%'))
"     let reponse_expected = [
"                 \ 'Message:',
"                 \ 'test message',
"                 \ '',
"                 \ 'Selected text:',
"                 \ '23',
"                 \ ]
"     call assert_equal(reponse_expected, getline(1, '$'))
" endfunction
"
" " Test visual mode revered (<cmd>Augment chat)
" function Test_chat_cmd_binding_visual_reverse()
"     call feedkeys("gg0$vhh", 'xt')
"     call feedkeys("test message\<cr>", 'nt')
"     Augment chat
"     call WaitFor({-> g:_chat_response})
"
"     " Check response buffer
"     call assert_match('AugmentChat-', bufname('%'))
"     let reponse_expected = [
"                 \ 'Message:',
"                 \ 'test message',
"                 \ '',
"                 \ 'Selected text:',
"                 \ '123',
"                 \ ]
"     call assert_equal(reponse_expected, getline(1, '$'))
" endfunction
"
" " Test visual mode multiline (<cmd>Augment chat)
" function Test_chat_cmd_binding_visual_multiline()
"     call feedkeys("gg0lvjl", 'xt')
"     call feedkeys("test message\<cr>", 'nt')
"     Augment chat
"     call WaitFor({-> g:_chat_response})
"
"     " Check response buffer
"     call assert_match('AugmentChat-', bufname('%'))
"     let reponse_expected = [
"                 \ 'Message:',
"                 \ 'test message',
"                 \ '',
"                 \ 'Selected text:',
"                 \ '6',
"                 \ '78',
"                 \ ]
"     call assert_equal(reponse_expected, getline(1, '$'))
" endfunction
"
" " Test visual mode multiline reversed (<cmd>Augment chat)
" function Test_chat_cmd_binding_visual_multiline_reverse()
"     call feedkeys("gg0jlvkl", 'xt')
"     call feedkeys("test message\<cr>", 'nt')
"     Augment chat
"     call WaitFor({-> g:_chat_response})
"
"     " Check response buffer
"     call assert_match('AugmentChat-', bufname('%'))
"     let reponse_expected = [
"                 \ 'Message:',
"                 \ 'test message',
"                 \ '',
"                 \ 'Selected text:',
"                 \ '3',
"                 \ '45',
"                 \ ]
"     call assert_equal(reponse_expected, getline(1, '$'))
" endfunction

" Test visual line mode (:Augment chat)
function Test_chat_visual_line()
    call feedkeys("gg0jVj\<esc>", 'xt')
    call feedkeys("test message\<cr>", 'nt')
    '<,'>Augment chat
    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '456',
                \ '789',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

" Test visual line mode reversed (:Augment chat). This should exercise the
" same code path as the test above, but included as a sanity check
function Test_chat_visual_line_reverse()
    call feedkeys("gg0jjVk\<esc>", 'xt')
    call feedkeys("test message\<cr>", 'nt')
    '<,'>Augment chat
    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '456',
                \ '789',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

" Test visual mode (:Augment chat)
function Test_chat_visual()
    call feedkeys("gg0vl\<esc>", 'xt')
    call feedkeys("test message\<cr>", 'nt')
    '<,'>Augment chat
    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '12',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

" Test visual mode multiline (:Augment chat)
function Test_chat_visual_multiline()
    call feedkeys("gg0lvjl\<esc>", 'xt')
    call feedkeys("test message\<cr>", 'nt')
    '<,'>Augment chat
    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '23',
                \ '456',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction

" Highlight some text then reenter normal mode. There should not be any
" selected text when the chat command is called.
function Test_no_selection()
    call feedkeys("gg0jVj\<esc>", 'xt')
    call feedkeys("test message\<cr>", 'nt')
    Augment chat
    call WaitFor({-> g:_chat_response})

    " Check response buffer
    let reponse_expected = [
                \ 'Message:',
                \ 'test message',
                \ '',
                \ 'Selected text:',
                \ '',
                \ ]
    call CheckChatReponse(reponse_expected)
endfunction
