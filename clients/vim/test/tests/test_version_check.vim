" Test the check for an out of date plugin version

function Setup()
    execute 'source ' . expand('<script>:p:h') . '/shared.vim'
    call SetupPlugin('mock_server_out_of_date')
endfunction

function Test_version_check()
    edit test.py
    messages clear

    call WaitFor({-> !empty(execute('messages'))})
    let messages = execute('messages')
    call assert_match('lower than the latest stable version v0.999999.1', messages)
endfunction
