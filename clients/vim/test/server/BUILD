load("//tools/bzl:python.bzl", "py_binary", "py_library")

py_library(
    name = "mock_server_lib",
    srcs = ["mock_server.py"],
)

[py_binary(
    name = name,
    srcs = [name + ".py"],
    deps = [":mock_server_lib"],
) for name in [
    "mock_server",
    "mock_server_notify",
    "mock_server_empty",
    "mock_server_multiline",
    "mock_server_indentation",
    "mock_server_out_of_date",
    "mock_server_no_completion",
]]

filegroup(
    name = "mock_servers",
    srcs = [
        "server_wrapper.sh",
        "server_wrapper_out_of_date.sh",
        ":mock_server",
        ":mock_server_empty",
        ":mock_server_indentation",
        ":mock_server_multiline",
        ":mock_server_no_completion",
        ":mock_server_notify",
        ":mock_server_out_of_date",
    ],
    visibility = ["//clients/vim/test:__pkg__"],
)
