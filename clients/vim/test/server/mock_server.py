"""Mock server for testing the vim client.

The default server will listen for LSP messages on stdin. It will respond to
the initialize request then respond to all completions requests with a single
fixed completion.
"""

import json
import sys
from typing import Callable, NoReturn
from pathlib import Path


def read_message() -> dict:
    """Read a JSON-RPC message from stdin.

    Reads the Content-Length header, a newline, then the content.
    """

    header = sys.stdin.buffer.readline().decode("ascii")
    if not header.startswith("Content-Length: "):
        raise ValueError(f"Invalid header: {header}")
    content_length = int(header.split(": ")[1])

    sys.stdin.buffer.readline()

    content = sys.stdin.buffer.read(content_length).decode("utf-8")
    return json.loads(content)


def send_message(message: dict) -> None:
    """Write a JSON-RPC message to stdout."""

    content = json.dumps(message)
    content_bytes = content.encode("utf-8")
    header = f"Content-Length: {len(content_bytes)}\r\n\r\n"

    sys.stdout.buffer.write(header.encode("ascii"))
    sys.stdout.buffer.write(content_bytes)
    sys.stdout.buffer.flush()


# initialize
def handle_initialize_default(request: dict) -> dict:
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {
            "capabilities": {},
        },
    }


# augment/status
def handle_status_default(request: dict) -> dict:
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {"loggedIn": True},
    }


# augment/chat
def handle_chat_default(request: dict) -> dict:
    path = request["params"]["textDocumentPosition"]["textDocument"]["uri"].replace(
        "file://", ""
    )
    file = Path(path).name
    message = request["params"]["message"]
    selected_text = request["params"].get("selectedText", "")
    text = f"""File:
{file}

Message:
{message}

Selected text:
{selected_text}
"""

    # Stream the response character by character
    for char in text:
        send_message(
            {
                "jsonrpc": "2.0",
                "method": "augment/chatChunk",
                "params": {"requestId": "abc123", "text": char},
            }
        )

    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {"requestId": "abc123", "text": text},
    }


# augment/pluginVersion
def handle_plugin_version_default(request: dict) -> dict:
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {"version": "0.0.0", "isPrerelease": False},
    }


# augment/login
def handle_login_default(request: dict) -> dict:
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {"loggedIn": False, "url": "auth.augmentcode.com/abc123"},
    }


# augment/token
def handle_token_default(request: dict) -> dict:
    code = request["params"]["code"]
    try:
        json.loads(code)
    except json.JSONDecodeError:
        return {
            "jsonrpc": "2.0",
            "id": request["id"],
            "error": {"message": "Invalid code"},
        }
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {"success": True},
    }


# augment/logout
def handle_logout_default(request: dict) -> dict:
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {"success": True},
    }


class MockServer:
    def __init__(
        self,
        handle_initialize: Callable[[dict], dict] | None = None,
        handle_did_open: Callable[[dict], dict] | None = None,
        handle_did_change: Callable[[dict], dict] | None = None,
        handle_completion: Callable[[dict], dict] | None = None,
        handle_status: Callable[[dict], dict] | None = None,
        handle_chat: Callable[[dict], dict] | None = None,
        handle_plugin_version: Callable[[dict], dict] | None = None,
        handle_login: Callable[[dict], dict] | None = None,
        handle_token: Callable[[dict], dict] | None = None,
        handle_logout: Callable[[dict], dict] | None = None,
    ):
        self.handlers = {
            "initialize": handle_initialize or handle_initialize_default,
            "textDocument/didOpen": handle_did_open or self.handle_did_open_default,
            "textDocument/didChange": handle_did_change
            or self.handle_did_change_default,
            "textDocument/completion": handle_completion
            or self.handle_completion_default,
            "augment/status": handle_status or handle_status_default,
            "augment/chat": handle_chat or handle_chat_default,
            "augment/pluginVersion": handle_plugin_version
            or handle_plugin_version_default,
            "augment/login": handle_login or handle_login_default,
            "augment/token": handle_token or handle_token_default,
            "augment/logout": handle_logout or handle_logout_default,
        }
        self.open_files = {}

    # textDocument/completion
    def handle_completion_default(self, request: dict) -> dict:
        uri = request["params"]["textDocument"]["uri"]
        if uri not in self.open_files:
            raise ValueError(f"File {uri} not open")
        return {
            "jsonrpc": "2.0",
            "id": request["id"],
            "result": [{"label": "test label", "insertText": "test completion"}],
        }

    # textDocuemnt/didOpen
    def handle_did_open_default(self, request: dict) -> None:
        uri = request["params"]["textDocument"]["uri"]
        self.open_files[uri] = request["params"]["textDocument"]["text"]

    # textDocument/didChange
    def handle_did_change_default(self, request: dict) -> None:
        uri = request["params"]["textDocument"]["uri"]
        if uri not in self.open_files:
            raise ValueError(f"File {uri} not open")
        self.open_files[uri] = request["params"]["contentChanges"][0]["text"]

    def start(self) -> NoReturn:
        while True:
            message = read_message()

            # Ignore messages that we don't have a handler for
            if message["method"] not in self.handlers:
                continue

            handler = self.handlers[message["method"]]
            response = handler(message)
            send_message(response)


if __name__ == "__main__":
    server = MockServer()
    server.start()
