"""Mock server that sends window/logMessage notifications on initialization."""

from mock_server import MockServer, send_message


def handle_initialize(request: dict) -> dict:
    response = {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": {
            "capabilities": {},
        },
    }
    for level, message in [
        (1, "Error message"),
        (2, "Warning message"),
        (3, "Info message"),
    ]:
        send_message(
            {
                "jsonrpc": "2.0",
                "method": "window/logMessage",
                "params": {"type": level, "message": message},
            }
        )
    return response


if __name__ == "__main__":
    server = MockServer(handle_initialize=handle_initialize)
    server.start()
