#!/usr/bin/env bash

# The server wrapper mocks the node runtime by returning a fixed version number
# for testing purposes. When the first argument is --version or -v, it prints
# the version and exits. Otherwise, it runs the server with the given
# arguments. The version number is fixed to v18.0.0, which we do not support.

if [ $# -eq 1 ] && { [ "$1" = "--version" ] || [ "$1" = "-v" ]; }; then
	echo "v18.0.0"
else
	python3 "$@"
fi
