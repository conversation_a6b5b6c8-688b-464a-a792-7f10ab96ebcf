"""Mock server that will crash if it receives a completion request.

This is useful for testing that completions are not requested in certain situations.
"""

from mock_server import MockServer


def completion_crash(request: dict) -> dict:
    raise Exception(f"Received unexpected completion request: {request}")


if __name__ == "__main__":
    server = MockServer(handle_completion=completion_crash)
    server.start()
