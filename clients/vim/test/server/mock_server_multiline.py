"""Mock server that returns multiline completions."""

from mock_server import <PERSON>ckServer


def completion_multiline(request: dict) -> dict:
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": [
            {"label": "test label", "insertText": "line one\nline two\n\nline three"}
        ],
    }


if __name__ == "__main__":
    server = MockServer(handle_completion=completion_multiline)
    server.start()
