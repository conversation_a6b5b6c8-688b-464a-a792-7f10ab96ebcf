"""Mock server that returns multiline completions."""

from mock_server import <PERSON>ckServer


def completion_indentation(request: dict) -> dict:
    return {
        "jsonrpc": "2.0",
        "id": request["id"],
        "result": [
            {
                "label": "test label",
                "insertText": "line one\n    4 spaces\n        8 spaces\n\t1 tab\n\t\t2 tabs",
            }
        ],
    }


if __name__ == "__main__":
    server = MockServer(handle_completion=completion_indentation)
    server.start()
