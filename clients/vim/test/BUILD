load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "wrap_tty",
    testonly = True,
    srcs = ["wrap_tty.py"],
)

sh_test(
    name = "vim_tests",
    srcs = ["run.sh"],
    data = [
        "run.vim",
        ":wrap_tty",
        "//clients/vim:plugin_files",
        "//clients/vim/test/server:mock_servers",
        "//clients/vim/test/tests:test_files",
        "@vim_9_1_891//:all",
    ],
    # disable sandbox for pseudoterminals
    # see https://github.com/bazelbuild/bazel/issues/5373
    tags = ["no-sandbox"],
)
