# Testing the Augment Vim Plugin

The `run.sh` script runs the tests, writes the results to standard output, and
exits with a non-zero exit code if any tests failed.

Tests are written as Vim script files in the `tests/` directory. Each test file:
- Runs in a clean Vim instance (no user config)
- Uses a mock server instead of the real Augment server
- Can simulate typing and check results
- Logs test output to `.log` files
