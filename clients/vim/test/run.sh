#!/usr/bin/env bash

usage() {
	echo "Usage: $(basename "$0") [test_file]"
	echo
	echo "Run Vim plugin tests."
	echo
	echo "Arguments:"
	echo "  test_file    Optional path to a specific test file to run"
	echo "               If not provided, all tests in tests/test_*.vim will be run"
	echo
	echo "Options:"
	echo "  -h, --help   Show this help message"
	exit 1
}

# Handle help flags
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
	usage
fi

# Check for too many arguments
if [[ $# -gt 1 ]]; then
	echo "Error: Too many arguments"
	usage
fi

# If we are running in bazel, use the vim executable from the test target
if [[ -n "$BAZEL_TEST" ]]; then
	VIM_EXECUTABLE=$TEST_SRCDIR/vim_9_1_891/vim_9_1_891/bin/vim
	export VIM=$TEST_SRCDIR/vim_9_1_891/vim_9_1_891/share/vim
	cd clients/vim/test
else
	VIM_EXECUTABLE=$(which vim)
fi

echo "Using VIM version: $($VIM_EXECUTABLE --version | head -n 2)"

# Create the logs directory
mkdir -p logs

VIM_ARGS=(
	-Nu NONE                                       # Run without configuration
	--not-a-term                                   # Suppress not running in terminal warning
	--cmd 'au SwapExists * let v:swapchoice = "e"' # Proceed even if there's a swap file
)

# Function to run a single test file
run_test() {
	local test_file=$1
	local test_vim_out=logs/$(basename $test_file).vimout.log

	if [[ -n "$BAZEL_TEST" ]]; then
		# Run vim in a tty
		$(rlocation _main/clients/vim/test/wrap_tty) $VIM_EXECUTABLE "${VIM_ARGS[@]}" -S run.vim $test_file >/dev/null
	else
		$VIM_EXECUTABLE "${VIM_ARGS[@]}" -S run.vim $test_file >/dev/null
	fi

	local result=$?
	echo
	cat "logs/$(basename $test_file).log"

	# If the test failed, output the plugin log
	if [[ $result -ne 0 ]]; then
		echo
		echo "Augment plugin log:"
		cat "logs/plugin.log"
	fi

	return $result
}

exit_code=0

if [[ $# -eq 1 ]]; then
	# Run single test
	test_file=$1
	if [[ ! -f $test_file ]]; then
		echo "Error: Test file '$test_file' not found"
		exit 1
	fi
	run_test $test_file || exit_code=1
else
	# Run all tests
	for test_file in tests/test_*.vim; do
		run_test $test_file || exit_code=1
	done
fi

exit $exit_code
