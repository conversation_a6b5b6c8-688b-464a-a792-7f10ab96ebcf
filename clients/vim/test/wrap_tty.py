"""A utility script that runs commands in a pseudo-terminal (PTY) environment.

This script provides functionality to execute commands in a PTY environment,
which simulates a real terminal. This is useful for running programs that
require a TTY for proper output formatting or interactive features. The script
can optionally capture and save the command's output to a file.
"""

import argparse
import os
import pty


def run_tty(command: list[str], output_file: str | None = None) -> int:
    pid, fd = pty.fork()

    if pid == 0:
        # In child process
        os.execv(command[0], command)  # nosec B606
    else:
        # In parent process
        command_output = b""
        try:
            while True:
                try:
                    data = os.read(fd, 1024)
                    if not data:
                        break
                    command_output += data
                except OSError:
                    break

            _, status = os.waitpid(pid, os.WNOHANG)
            os.close(fd)

            if output_file:
                with open(output_file, "w") as f:
                    f.write(command_output.decode())
        except KeyboardInterrupt as e:
            if output_file:
                with open(output_file, "w") as f:
                    f.write(command_output.decode())
            raise e

        return os.WEXITSTATUS(status)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--output_file",
        type=str,
        default=None,
        help="If provided, file to write command stdout to",
    )
    parser.add_argument(
        "command",
        nargs=argparse.REMAINDER,
        type=str,
        help="Command to run",
    )
    args = parser.parse_args()

    if not args.command:
        parser.error("At least one command argument is required")

    status = run_tty(args.command, args.output_file)
    exit(status)
