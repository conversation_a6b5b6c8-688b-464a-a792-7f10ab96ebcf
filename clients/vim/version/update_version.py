#!/usr/bin/env python3

"""Script for checking and incrementing the version of the Vim plugin.

The following commands are available:
1. check: Validates that the given version string is a valid semver and that
   the new version is greater than the previous.
2. increment_minor: Increments the minor version.
3. promote: Promotes a pre-release version to a stable release. This first
   checks the the provided version is a valid prerelease (patch of 0) then
   increments the patch to 1.
"""

import argparse
import semver


def main():
    parser = argparse.ArgumentParser(description="Check or increment semver versions.")
    subparsers = parser.add_subparsers(dest="command", required=True)

    # Check command
    check_parser = subparsers.add_parser(
        "check",
        help="Check that the version is valid semver and that the new version is greater than the current",
    )
    check_parser.add_argument("new", type=str, help="Version to check (e.g. 1.2.3)")
    check_parser.add_argument(
        "current", type=str, help="Current plugin version to check against"
    )

    # Increment command
    increment_parser = subparsers.add_parser(
        "increment_minor", help="Increment the minor version"
    )
    increment_parser.add_argument(
        "current", type=str, help="Current plugin version (e.g. 1.2.3)"
    )

    # Promote command
    promote_parser = subparsers.add_parser(
        "promote", help="Promote a pre-release to a stable release"
    )
    promote_parser.add_argument(
        "prerelease", type=str, help="Prerelease version to promote (e.g. 1.2.0)"
    )

    args = parser.parse_args()

    if args.command == "check":
        new = semver.Version.parse(args.new)
        current = semver.Version.parse(args.current)
        if new <= current:
            raise ValueError(
                f"New version {new} not greater than current version {current}"
            )
    elif args.command == "increment_minor":
        current = semver.Version.parse(args.current)
        print(current.bump_minor())
    elif args.command == "promote":
        prerelease = semver.Version.parse(args.prerelease)
        if prerelease.patch != 0:
            raise ValueError(
                f"Version {prerelease} is not a valid prerelease (patch != 0)"
            )
        print(prerelease.bump_patch())
    else:
        raise ValueError(f"Unknown command: {args.command}")


if __name__ == "__main__":
    main()
