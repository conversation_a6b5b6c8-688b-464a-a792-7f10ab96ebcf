#!/usr/bin/env bash

# This script generates a `version.vim` file that should be placed in the Vim
# plugin files at `autoload/augment/version.vim` and provides a function
# `augment#version#String()` which returns the version of the plugin. This
# script should be ran as a part of the release workflow and its generated file
# copied along with other plugin files.

set -euo pipefail

if [ $# -ne 1 ]; then
	echo "Usage: $0 <version>" >&2
	exit 1
fi

VERSION="$1"

# Validate semver format
if ! [[ $VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[0-9A-Za-z-]+)?(\+[0-9A-Za-z-]+)?$ ]]; then
	echo "Error: Version must be in semver format (e.g., 1.2.3)" >&2
	exit 1
fi

# Create the version.vim file
cat >version.vim <<EOF
" Copyright (c) 2025 Augment
" MIT License - See LICENSE.md for full terms

function! augment#version#Version() abort
    return '${VERSION}'
endfunction
EOF

echo "$(pwd)/version.vim"
