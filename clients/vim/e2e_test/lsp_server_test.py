"""End-to-end tests for the LSP server implementation."""

from urllib.parse import urlparse, parse_qs
import time
import os
import json
import subprocess
import threading
from pathlib import Path
from typing import Dict, List
import pytest
from http.server import HTTPServer, BaseHTTPRequestHandler
from pylspclient import LspClient, LspEndpoint, JsonRpcEndpoint
from pylspclient.lsp_pydantic_strcuts import (
    TextDocumentItem,
    TextDocumentIdentifier,
    TextDocumentPositionParams,
    Position,
    CompletionContext,
    CompletionTriggerKind,
)
from dataclasses import dataclass, asdict

# <PERSON><PERSON> produces the following script to run the sidecar application.
# The script launches node with server.bundle.js.
server_path = "clients/vim/sidecar/sidecar_/sidecar"


class MockAugmentHandler(BaseHTTPRequestHandler):
    """Mock HTTP handler for Augment API endpoints."""

    completions: List[Dict] = []  # Stores completion responses
    known_memories: set[str] = set()  # Stores known memory names
    last_path = None
    last_request = None

    def do_POST(self):
        content_length = int(self.headers["Content-Length"])
        body = self.rfile.read(content_length)
        request = json.loads(body)

        # Store the last request for validation
        MockAugmentHandler.last_path = self.path
        MockAugmentHandler.last_request = request
        MockAugmentHandler.last_headers = self.headers

        print("Received request:", self.path)

        status = 500
        if self.path == "/chat":
            response = {
                "text": "mock_chat",
                "unknown_memory_names": [],
                "checkpoint_not_found": False,
            }
            status = 200
        elif self.path == "/chat-stream":
            # For chat-stream we use chunked encoding
            self.send_response(200)
            self.send_header("Content-Type", "application/json")
            self.send_header("Transfer-Encoding", "chunked")
            self.end_headers()

            chunks = [
                {
                    "text": "mock",
                    "unknown_memory_names": [],
                    "checkpoint_not_found": False,
                    "workspace_file_chunks": [],
                    "incorporated_external_sources": [],
                    "nodes": [],
                },
                {
                    "text": "_chat",
                    "unknown_memory_names": [],
                    "checkpoint_not_found": False,
                    "workspace_file_chunks": [],
                    "incorporated_external_sources": [],
                    "nodes": [],
                },
            ]

            def write_chunk(data: bytes):
                # Write chunk size in hexadecimal
                self.wfile.write(f"{len(data):X}\r\n".encode("utf-8"))
                # Write chunk data
                self.wfile.write(data)
                self.wfile.write(b"\r\n")
                self.wfile.flush()

            for chunk in chunks:
                chunk_data = json.dumps(chunk).encode("utf-8") + b"\n"
                write_chunk(chunk_data)

            # Write empty chunk to signal the end of the response
            write_chunk(b"")

            # Return early because we've already sent the response
            return
        elif self.path == "/completion":
            response = {
                "text": "mock_completion",
                "unknown_memory_names": [],
                "suggested_prefix_char_count": 100,
                "suggested_suffix_char_count": 100,
                "completion_items": [
                    {
                        "text": "mock_completion",
                        "skipped_suffix": "",
                        "suffix_replacement_text": "",
                    }
                ],
                "checkpoint_not_found": False,
            }
            self.completions.append(request)
            status = 200
        elif self.path == "/get-models":
            response = {
                "default_model": "test-model",
                "models": [
                    {
                        "name": "test-model",
                        "suggested_prefix_char_count": 100,
                        "suggested_suffix_char_count": 100,
                    }
                ],
                "feature_flags": {
                    "bypass_language_filter": True,
                },
            }
            status = 200
        elif self.path == "/find-missing":
            print("find-missing request:", request)
            missing = list(
                set(request["mem_object_names"]) - MockAugmentHandler.known_memories
            )
            response = {
                "unknown_memory_names": missing,
                "nonindexed_blob_names": missing,
            }
            print("find-missing response:", response)
            status = 200
        elif self.path == "/batch-upload":
            print("batch-upload request:", request)
            MockAugmentHandler.known_memories.update(
                [blob["blob_name"] for blob in request["blobs"]]
            )
            response = {
                "blob_names": [blob["blob_name"] for blob in request["blobs"]],
            }
            status = 200
        elif self.path == "/token":
            response = {"accessToken": "11111111111111111111111111111111"}
            try:
                assert request["client_id"] == "v"
            except AssertionError as e:
                print("AssertionError:", e)
                status = 500
            else:
                status = 200
        else:
            response = {"error": "unknown endpoint"}
            status = 404

        self.send_response(status)
        self.send_header("Content-Type", "application/json")
        self.end_headers()
        self.wfile.write(json.dumps(response).encode("utf-8"))

    def handle(self):
        try:
            super().handle()
        except (ConnectionResetError, BrokenPipeError):
            # Ignore connection errors during shutdown
            pass


class LSPTestKit:
    def __init__(self, tmp_path):
        self.server_process = None
        self.http_server = None
        self.server_thread = None
        self._lsp_client = None
        self.tmp_path = tmp_path
        self.url = None

    def setup(self, api_token: str | None = None):
        # Start mock HTTP server
        print("test dir", self.tmp_path)
        self.http_server = HTTPServer(("localhost", 0), MockAugmentHandler)
        self.server_thread = threading.Thread(target=self.http_server.serve_forever)
        self.server_thread.daemon = True
        self.server_thread.start()

        # Start LSP server with environment pointing to our mock server

        url = f"http://localhost:{self.http_server.server_port}"
        self.url = url
        env = {
            "TEST_TMPDIR": self.tmp_path,
            "TEST_HOSTNAME": "localhost",
            "AUGMENT_LOG_LEVEL": "debug",
        }

        # Create a mock session file
        secrets = Path(self.tmp_path) / "data" / "vim-augment" / "secrets.json"
        secrets.parent.mkdir(parents=True, exist_ok=True)
        inner_record = {
            "accessToken": "00000000000000000000000000000000",
            "tenantURL": url,
            "scopes": ["email"],
        }
        session = {"augment.sessions": json.dumps(inner_record)}
        secrets.write_text(json.dumps(session))

        # If specified, create a user config file with the api token
        if api_token:
            user_config = {
                "apiToken": api_token,
                "completionURL": url,
            }
            print(f"creating user config: {user_config}")
            config_path = (
                Path(self.tmp_path) / "config" / "augment" / "user_config.json"
            )
            config_path.parent.mkdir(parents=True, exist_ok=True)
            config_path.write_text(json.dumps(user_config))

        self.server_process = subprocess.Popen(
            [server_path, "--stdio"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env={**os.environ, **env},
        )

        # Wait a moment for the server to start before checking for failure.
        time.sleep(2)
        if self.server_process.poll() is not None:
            assert self.server_process.stderr is not None
            err = self.server_process.stderr.read().decode()
            raise RuntimeError(f"Failed to start LSP server {err}")

        # Create LSP client
        json_rpc_endpoint = JsonRpcEndpoint(
            self.server_process.stdin, self.server_process.stdout
        )
        lsp_endpoint = LspEndpoint(json_rpc_endpoint)
        self._lsp_client = LspClient(lsp_endpoint)

    @property
    def lsp_client(self):
        assert self._lsp_client is not None
        return self._lsp_client

    def initialize_lsp(self, workspaceFolders: List[dict] | None = None):
        """Initialize the LSP connection."""
        assert self.lsp_client is not None
        workspaceFolders = [] if workspaceFolders is None else workspaceFolders
        self.lsp_client.initialize(
            processId=os.getpid(),
            rootPath=None,
            rootUri=None,
            initializationOptions={},
            capabilities={},  # capabilities,
            trace="off",
            workspaceFolders=workspaceFolders,
        )
        self.lsp_client.initialized()

    def teardown(self):
        if self.lsp_client:
            self.lsp_client.shutdown()
            self.lsp_client.exit()

        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()

        if self.http_server:
            self.http_server.shutdown()
            self.http_server.server_close()
            if self.server_thread:
                self.server_thread.join()

    def open_document(self, uri: str, text: str):
        """Notify LSP server about document open."""
        item = TextDocumentItem(uri=uri, languageId="python", version=1, text=text)
        self.lsp_client.didOpen(item)

    def wait_for_status(self, login_status: bool, sync_percentage: int | None = None):
        status_response = None
        for _ in range(10):
            status_response = self.lsp_client.lsp_endpoint.call_method("augment/status")
            # when a folder is added for the first time, the sync percentage may not be immediately present.
            if (
                sync_percentage is None
                or status_response.get("syncPercentage", None) == sync_percentage
            ) and status_response["loggedIn"] == login_status:
                break

            time.sleep(1)
        else:
            print("Last status response:", status_response)
            assert False, f"Failed to reach logged-in status {login_status}"


@pytest.fixture
def lsp_test_kit(tmp_path):
    kit = LSPTestKit(tmp_path)
    yield kit
    kit.teardown()


def test_completion(lsp_test_kit: LSPTestKit):
    """Test basic completion request/response."""
    kit = lsp_test_kit
    kit.setup()
    kit.initialize_lsp()
    # Clear previous completion requests
    MockAugmentHandler.completions.clear()

    # Open a document
    doc_uri = "file:///test.py"
    kit.open_document(doc_uri, "def test_func")

    # Request completion

    ctx = CompletionContext(
        triggerKind=CompletionTriggerKind.Invoked, triggerCharacter=None
    )
    completion_response = kit.lsp_client.completion(
        TextDocumentIdentifier(uri=doc_uri), Position(line=0, character=12), context=ctx
    )

    # Verify completion request was sent to mock server
    assert len(MockAugmentHandler.completions) == 1
    completion_request = MockAugmentHandler.completions[0]
    assert "prompt" in completion_request

    # Verify LSP response
    assert len(completion_response) == 1
    assert completion_response[0].insertText == "mock_completion"
    assert completion_response[0].label


def test_signin(lsp_test_kit: LSPTestKit):
    """Test basic oauth authentication.

    Signout and then signin again. Ensure that Status is updated correctly.
    """
    kit = lsp_test_kit
    kit.setup()
    kit.initialize_lsp()

    # Clear previous completion requests
    MockAugmentHandler.completions.clear()

    ## Signout: Status should be updated and completions should fail
    logout_response = kit.lsp_client.lsp_endpoint.call_method("augment/logout")
    print("Logout response:", logout_response)

    kit.wait_for_status(login_status=False)

    # Completions should fail now because we are not logged in.
    doc_uri = "file:///test.py"
    kit.open_document(doc_uri, "def test_func")

    ctx = CompletionContext(
        triggerKind=CompletionTriggerKind.Invoked, triggerCharacter=None
    )

    with pytest.raises(Exception) as exc_info:
        completion_response = kit.lsp_client.completion(
            TextDocumentIdentifier(uri=doc_uri),
            Position(line=0, character=12),
            context=ctx,
        )
        print("Completion response:", completion_response)
    assert "Not logged in. Please login first" in str(exc_info.value)
    assert exc_info.value.code == 401  # AugmentErrorCode.UNAUTHORIZED

    ## Signin: Status should be updated and completions should work
    login_response = kit.lsp_client.lsp_endpoint.call_method("augment/login")
    assert not login_response["loggedIn"]
    # The expected url should look like:
    # http://localhost:63543/authorize?response_type=code&code_challenge=JfD0MQakGnxjEgQMvMTCZ6ypd9rgLdntTG_vgbf7JNE&client_id=v&state=WkJ6lbY3yLQ&prompt=login
    parsed_url = urlparse(login_response["url"])
    query_params = parse_qs(parsed_url.query, keep_blank_values=True)
    assert query_params["response_type"] == ["code"]
    assert query_params["client_id"] == ["v"]
    assert query_params["prompt"] == ["login"]
    assert len(query_params["state"][0]) > 0  # State should be non-empty
    assert (
        len(query_params["code_challenge"][0]) == 43
    )  # PKCE code challenge is always 43 chars

    state = query_params["state"][0]
    auth_args = {
        "code": "123",
        "state": state,
        "tenant_url": kit.url,
    }
    print("auth args:", auth_args)
    # If there's an issue with the signin, the augment/token will return an error
    kit.lsp_client.lsp_endpoint.call_method("augment/token", code=json.dumps(auth_args))

    kit.wait_for_status(login_status=True)

    ## Signed in, completions should work
    completion_response = kit.lsp_client.completion(
        TextDocumentIdentifier(uri=doc_uri), Position(line=0, character=12), context=ctx
    )
    print("Completion response:", completion_response)


def test_signin_bad_code(lsp_test_kit: LSPTestKit):
    """Test that a bad code during the signin process is handled correctly."""
    kit = lsp_test_kit
    kit.setup()
    kit.initialize_lsp()

    # Start signed out
    kit.lsp_client.lsp_endpoint.call_method("augment/logout")
    kit.wait_for_status(login_status=False)

    # Attempt to sign in with a code that doesn't parse
    kit.lsp_client.lsp_endpoint.call_method("augment/login")
    with pytest.raises(Exception) as exc_info:
        kit.lsp_client.lsp_endpoint.call_method("augment/token", code="bad_code")
    assert "Failed to parse user code" in str(exc_info.value)

    # Attempt to sign in with an invalid state
    kit.lsp_client.lsp_endpoint.call_method("augment/login")
    auth_args = {
        "code": "bad_code",
        "state": "bad_state",
        "tenant_url": kit.url,
    }
    with pytest.raises(Exception) as exc_info:
        kit.lsp_client.lsp_endpoint.call_method(
            "augment/token", code=json.dumps(auth_args)
        )
    assert "Could not find sign in state" in str(exc_info.value)

    # Sign in normally
    login_response = kit.lsp_client.lsp_endpoint.call_method("augment/login")
    parsed_url = urlparse(login_response["url"])
    query_params = parse_qs(parsed_url.query, keep_blank_values=True)
    auth_args = {
        "code": "123",
        "state": query_params["state"][0],
        "tenant_url": kit.url,
    }
    kit.lsp_client.lsp_endpoint.call_method("augment/token", code=json.dumps(auth_args))


def test_api_token(lsp_test_kit):
    """Test that the API token is used when provided via the user config."""
    kit = lsp_test_kit
    kit.setup(api_token="TEST-TOKEN")
    kit.initialize_lsp()

    # Clear previous completion requests
    MockAugmentHandler.completions.clear()

    # Open a document and request a completion
    doc_uri = "file:///test.py"
    kit.open_document(doc_uri, "def test_func")
    ctx = CompletionContext(
        triggerKind=CompletionTriggerKind.Invoked, triggerCharacter=None
    )
    completion_response = kit.lsp_client.completion(
        TextDocumentIdentifier(uri=doc_uri), Position(line=0, character=12), context=ctx
    )

    # Verify completion request was sent to mock server
    assert len(MockAugmentHandler.completions) == 1
    completion_request = MockAugmentHandler.completions[0]
    assert "prompt" in completion_request

    # Verify that the API token was used
    assert MockAugmentHandler.last_headers["Authorization"] == "Bearer TEST-TOKEN"

    # Verify LSP response
    assert len(completion_response) == 1
    assert completion_response[0].insertText == "mock_completion"
    assert completion_response[0].label


@dataclass
class ChatParams:
    textDocumentPosition: TextDocumentPositionParams
    message: str
    selectedText: str | None = None


@dataclass
class ChatStreamParams(ChatParams):
    partialResultToken: str | None = None


@dataclass
class ChatResponse:
    label: str
    text: str


def test_chat(lsp_test_kit: LSPTestKit):
    """Test basic chat.

    Send a simple chat request and verify the response.
    """
    kit = lsp_test_kit
    kit.setup()
    kit.initialize_lsp()

    doc_pos = TextDocumentPositionParams(
        textDocument=TextDocumentIdentifier(uri="file:///test.py"),
        position=Position(line=0, character=0),
    )
    chat_params = ChatParams(
        textDocumentPosition=doc_pos,
        message="test message",
        selectedText="test selected text",
    )
    chat_response = kit.lsp_client.lsp_endpoint.call_method(
        "augment/chat", **asdict(chat_params)
    )

    # Verify LSP response
    assert chat_response["text"] == "mock_chat"


def test_completion_with_chat_context(lsp_test_kit: LSPTestKit):
    """Test that a completion request includes the context from chat.

    Completion requests should include the last chat response as context.
    """
    kit = lsp_test_kit
    kit.setup()
    kit.initialize_lsp()

    doc_pos = TextDocumentPositionParams(
        textDocument=TextDocumentIdentifier(uri="file:///test.py"),
        position=Position(line=0, character=0),
    )

    # Send a chat request
    chat_params = ChatParams(
        textDocumentPosition=doc_pos,
        message="test message",
        selectedText="test selected text",
    )
    chat_response = kit.lsp_client.lsp_endpoint.call_method(
        "augment/chat", **asdict(chat_params)
    )
    assert chat_response["text"] == "mock_chat"

    # Now, send a completion request
    ctx = CompletionContext(
        triggerKind=CompletionTriggerKind.Invoked, triggerCharacter=None
    )

    doc_uri = "file:///test.py"
    kit.open_document(doc_uri, "def test_func")
    completion_response = kit.lsp_client.completion(
        TextDocumentIdentifier(uri=doc_uri), Position(line=0, character=0), context=ctx
    )
    assert len(completion_response) == 1
    assert completion_response[0].insertText == "mock_completion"

    # Verify that the chat context was included in the completion request.
    # We know that the chat context comes through the recency_info structure,
    # so we can explicitly check for its presence.
    # Example:
    # 'recency_info': {'recent_changes': [{'blob_name': '', 'path': '', 'char_start': 0, 'char_end': 0, 'replacement_text': 'mock_chat', 'present_in_blob': False, 'expected_blob_name': ''}]}
    backend_request = MockAugmentHandler.last_request
    assert backend_request is not None
    assert chat_response["text"]
    assert (
        backend_request["recency_info"]["recent_changes"][0]["replacement_text"]
        == chat_response["text"]
    )


def test_workspace(lsp_test_kit: LSPTestKit, tmp_path: Path):
    """Test basic workspace creation

    A workspace folder with one file should produce a blobs context with one file.
    """
    kit = lsp_test_kit
    kit.setup()
    dir = tmp_path / "test-workspace"
    dir.mkdir()
    src_file = dir / "main.py"
    src_file.write_text("def test_func")

    kit.initialize_lsp(workspaceFolders=[{"uri": str(dir)}])
    # Clear previous completion requests
    MockAugmentHandler.completions.clear()

    kit.wait_for_status(login_status=True, sync_percentage=100)

    # Open a document
    prompt_doc = "/test.py"
    doc_uri = "file://" + prompt_doc
    kit.open_document(doc_uri, "def test_func")

    # Request completion

    ctx = CompletionContext(
        triggerKind=CompletionTriggerKind.Invoked, triggerCharacter=None
    )
    completion_response = kit.lsp_client.completion(
        TextDocumentIdentifier(uri=doc_uri), Position(line=0, character=12), context=ctx
    )
    assert len(completion_response) == 1
    assert completion_response[0].insertText == "mock_completion"

    # Verify completion request was sent with one file in the workspace
    assert MockAugmentHandler.last_request is not None
    assert MockAugmentHandler.last_request["path"] == prompt_doc
    assert len(MockAugmentHandler.last_request["blobs"]["added_blobs"]) == 1
    assert len(MockAugmentHandler.last_request["blobs"]["deleted_blobs"]) == 0
