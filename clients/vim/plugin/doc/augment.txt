*augment.txt* *augment* *Augment*

  Augment AI: Augment yourself with the best AI pair programmer
------------------------------------------------------------------------------
Table of Contents                                  *augment-table-of-contents*

1. Quick Start                                                 |augment-start|
2. Commands                                                 |augment-commands|
3. Options                                                   |augment-options|
4. Alternate Keybinds                             |augment-alternate-keybinds|
5. Highlighting                                         |augment-highlighting|

------------------------------------------------------------------------------
Quick Start                                                *augment-start*

Get started with Augment by signing in using the `:Augment signin` command.
Once signed in, suggestions will be available in all supported languages.
Open a file, start typing, and use tab to accept suggestions as they appear.

------------------------------------------------------------------------------
Commands                                                    *augment-commands*

The following commands are provided:

                                                            *:Augment_status*
`:Augment status`
    View the current status of the plugin.

                                                            *:Augment_signin*
`:Augment signin`
   Authenticate with the Augment service using OAuth. This is required before
   using the plugin for the first time.

                                                            *:Augment_signout*
`:Augment signout`
    Sign out of Augment.



                                                            *:Augment_log*
`:Augment log`
    View the plugin log. This is useful for debugging.

                                                            *:Augment_chat*
`:Augment chat [message]`
    Start a chat with Augment AI. In visual mode, the selected text will be
    included in the chat request.

                                                            *:Augment_chat_new*
`:Augment chat-new`
    Start a new chat conversation with Augment AI.

                                                            *:Augment_chat_toggle*
`:Augment chat-toggle`
    Open/close the chat conversation window.


------------------------------------------------------------------------------
Options                                                   *augment-options*

The following options are available:

                                               *g:augment_disable_tab_mapping*
g:augment_disable_tab_mapping
    The default tab mapping can be disabled by setting
    `g:augment_disable_tab_mapping = v:true` before the plugin is loaded.

                                               *g:augment_disable_completions*
g:augment_disable_completions
    Inline completions can be disabled by setting
    `g:augment_disable_completions = v:true` in your vimrc or at any time
    during editing.

                                               *g:augment_workspace_folders*
g:augment_workspace_folders
    Enhance completion quality by providing a list of workspace directories to
    Augment. These directories will be analyzed to provide additional context
    to the completion model, improving the accuracy and style of suggestions.
    For example, including your project's root directory helps Augment generate
    completions that match your codebase's patterns and conventions. After
    adding a workspace folder and restarting vim, the output of the `:Augment
    status` command will include the syncing progress for the added folder.

    Workspace folders can be specified using absolute paths or paths relative
    to your home directory (~).

    Example:
    ```vim
    let g:augment_workspace_folders = ['/path/to/project', '~/another-project']
    ```

    Note: This option must be set before the plugin is loaded.

                                               *g:augment_suppress_version_warning*
g:augment_suppress_version_warning
    By default, Augment will display a warning message if the plugin version
    is outdated. This can be disabled by setting
    `g:augment_suppress_version_warning = v:true` before the plugin is loaded.

                                               *g:augment_node_command*
g:augment_node_command
    Specify a custom Node.js executable to use when launching the Augment
    server. By default, Augment will use the 'node' command found in your
    PATH. The provided command can either be the full path to a Node.js binary
    or the name of a command in your PATH. Set this option before the plugin
    is loaded if you need to use a specific Node.js installation. The Node.js
    version is recommended to be 22.0.0 or higher.

    Note: If the specified Node.js executable is not found, an error message
    will be displayed in the plugin log.

    Example:
    ```vim
    let g:augment_node_command = '/usr/local/bin/node'
    " Or
    let g:augment_node_command = 'node-22'
    ```


------------------------------------------------------------------------------
Alternate Keybinds                             *augment-alternate-keybinds*

By default, tab is used to accept a suggestion. If you want to use a different
key, create a mapping that calls `augment#Accept()`. The function takes an
optional arugment used to specify the fallback text to insert if no suggestion
is available.

>vim
    " Use Ctrl-Y to accept a suggestion
    inoremap <c-y> <cmd>call augment#Accept()<cr>

    " Use enter to accept a suggestion, falling back to a newline if no suggestion
    " is available
    inoremap <cr> <cmd>call augment#Accept("\n")<cr>

Of, for neovim:

>lua
    -- Use Ctrl-Y to accept a suggestion
    vim.keymap.set('i', '<C-Y>', '<cmd>call augment#Accept()<CR>', { noremap = true })
    -- Use enter to accept a suggestion, falling back to a newline if no suggestion is available
    vim.keymap.set('i', '<cr>', '<cmd>call augment#Accept()<CR>', { noremap = true })
<

------------------------------------------------------------------------------
Highlighting                                         *augment-highlighting*

You can change the inline suggestion highlighting by using an autocmd to update the
`AugmentSuggestionHighlight` highlight group. For example, to change the highlighting
when using the `peachpuff` color scheme, use:

>vim
    autocmd ColorScheme peachpuff highlight AugmentSuggestionHighlight guifg=#888888 ctermbg=8
<

Or, for neovim:

>lua
    vim.api.nvim_create_autocmd('ColorScheme', {
      pattern = 'peachpuff',
      callback = function()
        vim.api.nvim_set_hl(0, 'AugmentSuggestionHighlight', {
          fg = '#888888',
          ctermfg = 8,
          force = true
        })
      end
    })
<


vim:tw=78:ts=8:noet:ft=help:norl:
