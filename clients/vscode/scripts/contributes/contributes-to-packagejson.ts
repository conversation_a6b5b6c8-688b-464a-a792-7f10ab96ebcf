import { writeFile } from "node:fs/promises";
import path from "node:path";

import packageJson from "../../package.json";
import { contributes } from "../../src/contributes/contributes-config";

export { packageJson };

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
packageJson.contributes = contributes as unknown as typeof packageJson.contributes;

export async function generatePackageJson() {
    // eslint-disable-next-line no-console
    console.info("Generating package.json with contributes");
    const packageJsonPath = path.resolve(__dirname, "../../package.json");
    await writeFile(packageJsonPath, `${JSON.stringify(packageJson, null, 4)}\n`);
}
