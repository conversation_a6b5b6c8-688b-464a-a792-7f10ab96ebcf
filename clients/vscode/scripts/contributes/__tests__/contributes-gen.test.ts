import packageJson from "../../../package.json";
import { contributes as generatedContributes } from "../../../src/contributes/contributes-config";

it("does not change the package.json", () => {
    // This test is here to ensure that the contributes-gen script does not
    // change the package.json file during the build process.
    expect(packageJson.contributes).toBeDefined();
    try {
        expect(generatedContributes).toEqual(packageJson.contributes);
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error(
            "Contents of `contributes-config.ts` does not match 'packageJson', try running `pnpm run contributes-gen` or fixing the `contributes-config.ts` file."
        );
        throw error; // Re-throw the original error to get Jest's diff output
    }
});
it("detects a a discrepancy between package.json and contributes-config", () => {
    expect(packageJson.contributes).toBeDefined();
    // Edit this in place so that the objects are not equal
    packageJson.contributes.customEditors = [];
    expect(generatedContributes).not.toEqual(packageJson.contributes);
});

it('does not have undefined values in "contributes"', () => {
    const packageJSON = JSON.stringify(
        generatedContributes,
        (_, v: unknown) => (v === undefined ? "undefined" : v),
        2
    );
    const processed = packageJSON.replace('"undefined"', "undefined").replaceAll('"', "'");
    expect(processed).not.toContain("undefined");
});
