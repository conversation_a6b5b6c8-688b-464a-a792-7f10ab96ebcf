#!/usr/bin/env node
//eslint-disable-next-line @typescript-eslint/no-var-requires
const net = require("net");
//eslint-disable-next-line @typescript-eslint/no-var-requires
const fs = require("fs");
/**
 *
 * @returns number - A open port number;
 */
async function resolvePort() {
    return new Promise((resolve) => {
        const server = net.createServer();
        server.listen(0, "127.0.0.1", () => {
            const addr = server.address();
            //eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const port = addr.port;
            server.close(() => {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                resolve(port);
            });
        });
    });
}

const DOT_HMR = "./.augment-hmr-env";

async function main(out = DOT_HMR) {
    if (/^-h|--help$/.test(out)) {
        throw new Error(`
Usage: generate-augment-hmr-env [out]
Generate a .augment-hmr-env file with a random port number.

Options:
  -h, --help  Show this help message
  out         Output file (default: .augment-hmr-env)
`);
    }
    try {
        const existing = fs
            .readFileSync(out, "utf8")
            .split("\n")
            .filter((line) => !/^\s*(#|$)/.test(line))
            .reduce((ret, line) => {
                const [key, value] = line.split("=");
                ret[key] = value;
                return ret;
            }, {});
        for (const key of ["AUGMENT_HMR_PORT", "AUGMENT_HMR"]) {
            if (!existing[key]) {
                throw new Error(`🔴 "${key}" not found in ${out}`);
            }
        }
        //eslint-disable-next-line no-console
        return `✅ Using exiting port: '${existing.AUGMENT_HMR_PORT}' read from '${out}'`;
    } catch (e) {
        //eslint-disable-next-line
        const port = await resolvePort();
        fs.writeFileSync(
            out,
            `AUGMENT_IS_HMR=true\nAUGMENT_HMR_PORT=${port}\nAUGMENT_HMR=http://localhost:${port}\nAUGMENT_JS_ENV=development\n`
        );
        return `✅ Found open port: '${port}' wrote to '${out}'`;
    }
}
//eslint-disable-next-line no-console
main(...process.argv.slice(2)).then(console.log, console.error);
