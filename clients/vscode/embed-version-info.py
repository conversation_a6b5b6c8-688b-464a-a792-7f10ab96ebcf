#!/usr/bin/env python3
"""Script for placing computed version number in package.json."""

import json
import os
import pathlib
import subprocess

with pathlib.Path("package.json").open(encoding="utf-8") as f:
    contents = f.read()

    with pathlib.Path("package.json.bak").open(mode="w", encoding="utf-8") as f_bak:
        f_bak.write(contents)
        f_bak.flush()

    data = json.loads(contents)
    if "EXTENSION_VERSION" in os.environ:
        data["version"] = os.environ["EXTENSION_VERSION"]
    else:
        version = subprocess.check_output(
            "echo $(head -n1 version)-$(git rev-parse --abbrev-ref HEAD).$(git log --format='%ct.%h' -n1)",
            shell=True,
            encoding="utf-8",
        )
        # for branch names with / and _
        version = version.replace("/", "-").replace("_", "-")

        data["version"] = version.strip()

with pathlib.Path("package.json.new").open(mode="w", encoding="utf-8") as f_new:
    f_new.write(json.dumps(data, indent=2))
    f_new.flush()

pathlib.Path("package.json.new").rename("package.json")
