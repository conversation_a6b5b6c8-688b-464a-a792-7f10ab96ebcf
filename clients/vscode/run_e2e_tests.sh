#!/bin/bash
# A test wrapper that starts a virtual framebuffer if necessary.
# this is useful to run the tests on a remote machine, e.g. test infrastructure
# this assumes that xvfb is installed on the machine, e.g. in the cbazel test container.

# --- begin runfiles.bash initialization v3 ---
# Copy-pasted from the Bazel Bash runfiles library v3.
set -uo pipefail
set +e
f=bazel_tools/tools/bash/runfiles/runfiles.bash
# shellcheck disable=SC1090
source "${RUNFILES_DIR:-/dev/null}/$f" 2>/dev/null ||
	source "$(grep -sm1 "^$f " "${RUNFILES_MANIFEST_FILE:-/dev/null}" | cut -f2- -d' ')" 2>/dev/null ||
	source "$0.runfiles/$f" 2>/dev/null ||
	source "$(grep -sm1 "^$f " "$0.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null ||
	source "$(grep -sm1 "^$f " "$0.exe.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null ||
	{
		echo >&2 "ERROR: cannot find $f"
		exit 1
	}
f=
set -e
# --- end runfiles.bash initialization v3 ---

# Check if DISPLAY is set; if not, start xvfb on display :99
if [ -z "${DISPLAY:-}" ]; then
	echo "DISPLAY is not set. Starting Xvfb on :99."
	Xvfb :99 -screen 0 1024x768x24 &
	export DISPLAY=:99
	trap "kill -9 %1" EXIT
else
	echo "DISPLAY is already set to $DISPLAY. Skipping Xvfb."
fi

# location of the test binary
ls -l "$(rlocation _main/clients/vscode/)"
TEST_BIN="$(rlocation _main/clients/vscode/wdio_binary_/wdio_binary)"
echo "Running: $TEST_BIN $@"
$TEST_BIN "$@"
