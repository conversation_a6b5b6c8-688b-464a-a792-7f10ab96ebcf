"""Unit tests for update_versions script used for vscode publishing."""

import json
import unittest

from update_versions import (
    ExtensionVers<PERSON>,
    Semver,
    get_new_version,
    get_sorted_versions,
    get_stable_release_ref,
    versions_from_vsce_data,
)


class TestSemver(unittest.TestCase):
    """Semver parsing."""

    def test_is_semver_string(self):
        self.assertEqual(Semver.is_semver_string("0.0.0"), True)
        self.assertEqual(Semver.is_semver_string("1.12.123"), True)
        self.assertEqual(Semver.is_semver_string("Nope"), False)
        self.assertEqual(Semver.is_semver_string(""), False)

    def test_from_string(self):
        self.assertEqual(Semver.from_string("0.0.0"), Semver(0, 0, 0))
        self.assertEqual(Semver.from_string("1.12.123"), Semver(1, 12, 123))

    def test_from_string_error(self):
        with self.assertRaises(SystemError) as context:
            Semver.from_string("nope")

        self.assertTrue("Failed to parse version" in str(context.exception))

    def test_lt(self):
        self.assertTrue(Semver(0, 0, 0) < Semver(0, 0, 1))
        self.assertTrue(Semver(0, 0, 0) < Semver(0, 1, 0))
        self.assertTrue(Semver(0, 0, 0) < Semver(1, 0, 0))
        self.assertTrue(Semver(0, 0, 1) < Semver(0, 0, 2))
        self.assertTrue(Semver(0, 1, 1) < Semver(0, 2, 2))
        self.assertTrue(Semver(1, 1, 1) < Semver(2, 2, 2))
        self.assertTrue(Semver(1, 1, 1) < Semver(2, 0, 0))

    def test_eq(self):
        self.assertTrue(Semver(0, 0, 0) == Semver(0, 0, 0))
        self.assertTrue(Semver(0, 0, 1) == Semver(0, 0, 1))
        self.assertTrue(Semver(0, 1, 0) == Semver(0, 1, 0))
        self.assertTrue(Semver(1, 0, 0) == Semver(1, 0, 0))
        self.assertTrue(Semver(1, 2, 3) == Semver(1, 2, 3))

    def test_increment(self):
        self.assertEqual(Semver(0, 0, 0).increment("patch"), Semver(0, 0, 1))
        self.assertEqual(Semver(0, 0, 0).increment("minor"), Semver(0, 1, 0))
        self.assertEqual(Semver(0, 0, 0).increment("major"), Semver(1, 0, 0))

    def test_increment_err(self):
        with self.assertRaises(SystemError) as context:
            Semver(0, 0, 0).increment("other")

        self.assertTrue("Unknown update type" in str(context.exception))

    def test_get_channel(self):
        self.assertEqual(Semver(0, 0, 0).get_channel(), "prerelease")
        self.assertEqual(Semver(0, 0, 1).get_channel(), "stable")
        self.assertEqual(Semver(0, 0, 2).get_channel(), "stable")


class TestExtensionVersions(unittest.TestCase):
    """Store and update the marketplace stable + prerelease versions."""

    def test_update(self):
        ev = ExtensionVersions(
            Semver(0, 0, 1),
            Semver(0, 1, 0),
        )
        ev.update("stable")
        self.assertEqual(ev.stable, Semver(0, 0, 2))
        self.assertEqual(ev.prerelease, Semver(0, 1, 0))

        ev.update("prerelease")
        self.assertEqual(ev.stable, Semver(0, 0, 2))
        self.assertEqual(ev.prerelease, Semver(0, 2, 0))

    def test_update_err(self):
        with self.assertRaises(SystemError) as context:
            ExtensionVersions(
                Semver(0, 0, 1),
                Semver(0, 1, 0),
            ).update("other")

        self.assertTrue("Unknown channel" in str(context.exception))

    def test_get_channel_version(self):
        ev = ExtensionVersions(
            Semver(0, 0, 1),
            Semver(0, 1, 0),
        )

        self.assertEqual(ev.get_channel_version("stable"), Semver(0, 0, 1))
        self.assertEqual(ev.get_channel_version("prerelease"), Semver(0, 1, 0))

    def test_get_channel_version_err(self):
        ev = ExtensionVersions(
            Semver(0, 0, 1),
            Semver(0, 1, 0),
        )

        with self.assertRaises(SystemError) as context:
            ev.get_channel_version("other")

        self.assertTrue("Unknown channel" in str(context.exception))

    def test_assert_version_order(self):
        ExtensionVersions(
            Semver(0, 0, 1),
            Semver(0, 1, 0),
        ).assert_version_order()

        ExtensionVersions(
            Semver(0, 0, 1),
            Semver(1, 0, 0),
        ).assert_version_order()

        ExtensionVersions(
            Semver(0, 10, 10),
            Semver(0, 11, 0),
        ).assert_version_order()

    def test_assert_version_order_err(self):
        with self.assertRaises(SystemError) as context:
            ExtensionVersions(
                Semver(0, 0, 0),
                Semver(0, 1, 0),
            ).assert_version_order()
        self.assertTrue("The stable version is invalid" in str(context.exception))

        with self.assertRaises(SystemError) as context:
            ExtensionVersions(
                Semver(0, 0, 1),
                Semver(0, 1, 1),
            ).assert_version_order()
        self.assertTrue("The prerelease version is invalid" in str(context.exception))

        with self.assertRaises(SystemError) as context:
            ExtensionVersions(
                Semver(0, 1, 1),
                Semver(0, 1, 0),
            ).assert_version_order()
        self.assertTrue(
            "The prerelease version should be > the stable version"
            in str(context.exception)
        )


class TestVSCEData(unittest.TestCase):
    """Parse vsce data and return stable/prerelease versions."""

    def test_versions_from_vsce_data(self):
        e = versions_from_vsce_data(
            get_sorted_versions(
                [
                    # Out of order pre-release
                    {"version": "0.1.0"},
                    {"version": "0.0.0"},
                    {"version": "0.10.0"},
                    {"version": "1.1.0"},
                    {"version": "0.2.0"},
                    # Out of order stable
                    {"version": "0.0.2"},
                    {"version": "0.0.1"},
                    {"version": "0.0.10"},
                    {"version": "0.0.3"},
                ]
            )
        )
        self.assertEqual(e.stable, Semver(0, 0, 10))
        self.assertEqual(e.prerelease, Semver(1, 1, 0))


class TestGetNewVersion(unittest.TestCase):
    """Core of the script that returns the updated version for a channel."""

    def test_no_release_channel(self):
        with self.assertRaises(SystemError) as context:
            get_new_version()
        self.assertTrue("RELEASE_CHANNEL is not defined" in str(context.exception))

    def test_bad_release_channel(self):
        with self.assertRaises(SystemError) as context:
            get_new_version(channel="other")
        self.assertTrue(
            "Release channel must be stable or prerelease" in str(context.exception)
        )

    def test_no_version_info(self):
        with self.assertRaises(SystemError) as context:
            get_new_version(channel="prerelease")
        self.assertTrue(
            "Either EXTENSION_DATA or VERSION_OVERRIDE is needed"
            in str(context.exception)
        )

    def test_invalid_version_override(self):
        with self.assertRaises(AssertionError) as context:
            get_new_version(
                version_override="0.0.1",
                channel="prerelease",
            )
        self.assertTrue(
            "The provided VERSION_OVERRIDE '0.0.1' does not meet the criteria for a 'prerelease'"
            in str(context.exception)
        )

        with self.assertRaises(AssertionError) as context:
            get_new_version(
                version_override="0.0.0",
                channel="stable",
            )
        self.assertTrue(
            "The provided VERSION_OVERRIDE '0.0.0' does not meet the criteria for a 'stable'"
            in str(context.exception)
        )

    def test_return_version_override(self):
        self.assertEqual(
            get_new_version(
                version_override="0.0.0",
                channel="prerelease",
            ),
            Semver(0, 0, 0),
        )

        self.assertEqual(
            get_new_version(
                version_override="0.0.1",
                channel="stable",
            ),
            Semver(0, 0, 1),
        )

    def test_no_versions(self):
        with self.assertRaises(SystemError) as context:
            get_new_version(
                channel="prerelease",
                raw_extension_data=json.dumps({}),
            )
        self.assertTrue(
            "No versions defined in the extension data" in str(context.exception)
        )

        with self.assertRaises(SystemError) as context:
            get_new_version(
                channel="prerelease",
                raw_extension_data=json.dumps([]),
            )
        self.assertTrue(
            "No versions defined in the extension data" in str(context.exception)
        )

    def test_missing_version(self):
        with self.assertRaises(SystemError) as context:
            get_new_version(
                channel="prerelease",
                raw_extension_data=json.dumps(
                    [
                        # prerelease only
                        {
                            "version": "0.0.0",
                        }
                    ],
                ),
            )
        self.assertTrue("Failed to find version for stable" in str(context.exception))

        with self.assertRaises(SystemError) as context:
            get_new_version(
                channel="prerelease",
                raw_extension_data=json.dumps(
                    [
                        # stable only
                        {
                            "version": "0.0.1",
                        }
                    ]
                ),
            )
        self.assertTrue(
            "Failed to find version for prerelease" in str(context.exception)
        )

    def test_new_versions_cause_bad_state(self):
        with self.assertRaises(SystemError) as context:
            get_new_version(
                channel="prerelease",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.0.0",
                        },
                        {
                            "version": "1.0.1",
                        },
                    ],
                ),
            )
        self.assertTrue(
            "The prerelease version should be > the stable version: stable (1.0.1), prerelease (0.1.0)"
            in str(context.exception)
        )

        with self.assertRaises(SystemError) as context:
            get_new_version(
                channel="stable",
                stable_release_ref="0.1.0",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.1.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ]
                ),
            )
        self.assertTrue(
            "The prerelease version should be > the stable version: stable (0.1.1), prerelease (0.1.0)"
            in str(context.exception)
        )

    def test_new_prerelease(self):
        self.assertEqual(
            get_new_version(
                channel="prerelease",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.1.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            ),
            Semver(0, 2, 0),
        )

    def test_new_prerelease_to_stable(self):
        self.assertEqual(
            get_new_version(
                channel="stable",
                stable_release_ref="0.1.0",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            ),
            Semver(0, 1, 1),
        )

    def test_new_prerelease_to_stable_with_optional_prefix(self):
        self.assertEqual(
            get_new_version(
                channel="stable",
                stable_release_ref="vscode@0.1.0",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            ),
            Semver(0, 1, 1),
        )

    def test_stable_patch_release(self):
        self.assertEqual(
            get_new_version(
                channel="stable",
                stable_release_ref="commitshaabcdef1234",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            ),
            Semver(0, 0, 2),
        )


class TestGetStableReleaseRef(unittest.TestCase):
    """Get the stable release ref."""

    def test_not_stable_release(self):
        with self.assertRaises(SystemError) as context:
            get_stable_release_ref(
                channel="preelease",
                stable_release_ref="commitshaabcdef1234",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            )
        self.assertTrue("release channel must be stable" in str(context.exception))

    def test_invalid_stable_release_ref(self):
        self.assertEqual(
            get_stable_release_ref(
                channel="stable",
                stable_release_ref="commitshaabcdef1234",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            ),
            "commitshaabcdef1234",
        )

    def test_return_stable_release_ref(self):
        self.assertEqual(
            get_stable_release_ref(
                channel="stable",
                stable_release_ref="0.1.1",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            ),
            "vscode@0.1.1",
        )

    def test_not_enough_prerelease_versions(self):
        with self.assertRaises(SystemError) as context:
            get_stable_release_ref(
                channel="stable",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            )
        self.assertTrue(
            "Failed to find a suitable pre-release to promote to stable"
            in str(context.exception)
        )

    def test_return_prerelease_versions(self):
        self.assertEqual(
            get_stable_release_ref(
                channel="stable",
                raw_extension_data=json.dumps(
                    [
                        {
                            "version": "0.3.0",
                        },
                        {
                            "version": "0.2.0",
                        },
                        {
                            "version": "0.0.1",
                        },
                    ],
                ),
            ),
            "vscode@0.2.0",
        )


if __name__ == "__main__":
    unittest.main()
