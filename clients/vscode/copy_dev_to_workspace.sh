#!/bin/bash -e
# This script copies the vscode out/ build to the workspace directory so
# VSCode can run the extension host with this build.
#
# Usage: `bazel run //clients/vscode:build_dev_to_workspace`.
#

if [ "$(uname)" == "Darwin" ]; then
	IS_MACOS=true
else
	IS_MACOS=false
fi

if [ "$1" == "--hmr" ]; then
	IS_HMR=true
fi

copy_files() {
	if [ $IS_MACOS == "true" ]; then
		mkdir -p "$2"
		chmod -R +w "$2"
		rm -rf "$2"
		mkdir -p "$2"
		cp -rf "$1/." "$2/"
	else
		mkdir -p "$2"
		cp --recursive --remove-destination --no-preserve=mode,ownership "$1/." "$2/"
	fi
}

if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY not set (run with bazel)" >&2
	exit 1
fi

OUTDIR="$BUILD_WORKSPACE_DIRECTORY"

echo "Starting to copy VSCode build..."
mkdir -p "$OUTDIR"

common_webview_build_dir="clients/vscode/common-webviews"
common_webview_workspace_dir="$OUTDIR/$common_webview_build_dir"
if [ "$IS_HMR" = "true" ]; then
	echo "    Skipping $common_webview_build_dir for HMR build"
else
	echo "    Copy $common_webview_build_dir/..."
	copy_files $common_webview_build_dir $common_webview_workspace_dir
fi
extension_out_build_dir="clients/vscode/out"
extension_out_workspace_dir="$OUTDIR/$extension_out_build_dir"
echo "    Copy $extension_out_build_dir/..."
copy_files $extension_out_build_dir $extension_out_workspace_dir

echo "Copying VSCode build done."
