#!/usr/bin/env python3
"""Script for incrementing the version of the vscode extension."""

from __future__ import annotations

import argparse
import json
import os
import re
import sys
from dataclasses import dataclass
from functools import total_ordering
from typing import List

STABLE_CHANNEL = "stable"
PRERELEASE_CHANNEL = "prerelease"


@dataclass
@total_ordering
class Semver:
    """Semver stores a vscode semver version."""

    major: int
    minor: int
    patch: int

    @classmethod
    def _match(cls, s: str) -> re.Match[str] | None:
        pattern = r"^(?:\w+@)?(\d+)\.(\d+)\.(\d+)(-.+)?$"
        return re.match(pattern, s)

    @classmethod
    def is_semver_string(cls, s: str) -> bool:
        return cls._match(s) is not None

    @classmethod
    def from_string(cls, semver_string: str) -> "Semver":
        match = cls._match(semver_string)
        if match:
            major = int(match.group(1))
            minor = int(match.group(2))
            patch = int(match.group(3))
            return Semver(major, minor, patch)
        else:
            raise SystemError(f"Failed to parse version from '{semver_string}'")

    def __str__(self):
        return f"{self.major}.{self.minor}.{self.patch}"

    def __lt__(self, other: "Semver") -> bool:
        if self.major < other.major:
            return True
        elif self.major == other.major:
            if self.minor < other.minor:
                return True
            elif self.minor == other.minor:
                return self.patch < other.patch
        return False

    def __eq__(self, other: "Semver") -> bool:
        return (
            self.major == other.major
            and self.minor == other.minor
            and self.patch == other.patch
        )

    def increment(self, update_type: str) -> "Semver":
        if update_type == "patch":
            return Semver(self.major, self.minor, self.patch + 1)
        elif update_type == "minor":
            return Semver(self.major, self.minor + 1, 0)
        elif update_type == "major":
            return Semver(self.major + 1, 0, 0)
        else:
            raise SystemError(f"Unknown update type: {update_type}")

    def get_channel(self):
        if self.patch == 0:
            return PRERELEASE_CHANNEL
        else:
            return STABLE_CHANNEL


@dataclass
class ExtensionVersions:
    """ExtensionVersions stores the stable and prerelease versions of an extension."""

    stable: Semver
    prerelease: Semver

    def update(self, channel: str) -> None:
        if channel == STABLE_CHANNEL:
            self.stable = self.stable.increment("patch")
        elif channel == PRERELEASE_CHANNEL:
            self.prerelease = self.prerelease.increment("minor")
        else:
            raise SystemError(f"Unknown channel: {channel}")

    def get_channel_version(self, channel: str) -> Semver:
        if channel == STABLE_CHANNEL:
            return self.stable
        elif channel == PRERELEASE_CHANNEL:
            return self.prerelease
        else:
            raise SystemError(f"Unknown channel: {channel}")

    # Ensure the channel version rules are mantained and that
    # stable is always behind pre-release
    def assert_version_order(self) -> None:
        if self.stable.get_channel() != STABLE_CHANNEL:
            raise SystemError(f"The stable version is invalid: {self.stable}")
        if self.prerelease.get_channel() != PRERELEASE_CHANNEL:
            raise SystemError(f"The prerelease version is invalid: {self.prerelease}")

        if self.prerelease <= self.stable:
            raise SystemError(
                f"The prerelease version should be > the stable version: stable ({self.stable}), prerelease ({self.prerelease})"
            )


# Helper method to print to stderr.
def info(message, *args, **kwargs) -> None:
    sys.stderr.write(message.format(*args, **kwargs) + "\n")


# Get the stable and prerelease versions from the array of extension versions
def versions_from_vsce_data(vsce_data: List[Semver]) -> "ExtensionVersions":
    e = ExtensionVersions(
        find_version_for_channel(vsce_data, STABLE_CHANNEL),
        find_version_for_channel(vsce_data, PRERELEASE_CHANNEL),
    )
    return e


# This iterates over the array of versions from `vsce show --json` and
# returns the version for a given channel. We use Augments versioning rules
# to identify a stable vs pre-release version.
def find_version_for_channel(version_data: List[Semver], channel: str) -> Semver:
    for v in version_data:
        if v.get_channel() == channel:
            return v
    raise SystemError(f"Failed to find version for {channel}")


def get_sorted_versions(versions: List[dict]) -> List[Semver]:
    all_versions: List[Semver] = []
    for data in versions:
        all_versions.append(Semver.from_string(data["version"]))

    return sorted(all_versions, reverse=True)


def get_env_var(key: str) -> str:
    return (os.environ.get(key) or "").strip()


def get_new_version(
    version_override: str | None = None,
    raw_extension_data: str | None = None,
    channel: str | None = None,
    stable_release_ref: str | None = None,
) -> Semver:
    # Ensure the environment is set up correctly
    if not channel:
        raise SystemError("RELEASE_CHANNEL is not defined in the current environment")
    if channel not in [STABLE_CHANNEL, PRERELEASE_CHANNEL]:
        raise SystemError(f"Release channel must be stable or prerelease: {channel}")

    if not version_override and not raw_extension_data:
        raise SystemError(
            "Either EXTENSION_DATA or VERSION_OVERRIDE is needed to update the version"
        )

    if version_override:
        # The version override is a kind of escape hatch should we need
        # to manually update the version.
        # The only thing we check is that stable/prerelease channel versioning
        # rules are adhered to.
        info(f"Using version override: {version_override}\n")
        override_semver = Semver.from_string(version_override)
        assert (
            override_semver.get_channel() == channel
        ), f"The provided VERSION_OVERRIDE '{override_semver}' does not meet the criteria for a '{channel}' version number"
        return override_semver
    else:
        extension_data = get_extension_data(raw_extension_data)
        sorted_versions = get_sorted_versions(extension_data)
        extension_versions = versions_from_vsce_data(sorted_versions)
        info("Current VSCode versions:")
        info(f"    Stable: {extension_versions.stable}")
        info(f"    Pre-Release: {extension_versions.prerelease}\n")

        if channel == STABLE_CHANNEL:
            # We don't care what the previous stable release was as we
            # are publishing a promoting a pre-release build to stable.
            if not stable_release_ref:
                raise SystemError(
                    "STABLE_RELEASE_REF is not defined in the current environment"
                )

            # The stable ref is either a commit SHA or tag.
            # The tag will be a semver string indicating the pre-release version
            # being publihed to stable.
            # A commit SHA is used when we want to publish an update to existing
            # stable release.
            # For the tag, we update stable to the tags semver and increment the
            # patch (0.4.0 -> 0.4.1). For the SHA, we can use the latest
            # stable release and increment the patch (0.4.1 -> 0.4.2)
            if Semver.is_semver_string(stable_release_ref):
                s = Semver.from_string(stable_release_ref)
                if s < extension_versions.stable:
                    raise SystemError(
                        f"The stable version should be > the previous stable version: previous stable ({extension_versions.stable}), new stable ({s})"
                    )
                extension_versions.stable = s

        # Get the channel version, increment the semver version, and set it back
        extension_versions.update(channel)

        info("New VSCode versions:")
        info(f"    Stable: {extension_versions.stable}")
        info(f"    Pre-Release: {extension_versions.prerelease}\n")

        # Confirm the new versions are valid
        extension_versions.assert_version_order()

        return extension_versions.get_channel_version(channel)


def get_extension_data(raw_extension_data: str | None) -> List[dict]:
    if not raw_extension_data:
        raise SystemError("EXTENSION_DATA is not defined in the current environment")
    info("Using extension data\n")
    extension_data = json.loads(raw_extension_data)
    if len(extension_data) == 0:
        raise SystemError("No versions defined in the extension data")
    return extension_data


def get_stable_release_ref(
    raw_extension_data: str | None = None,
    channel: str | None = None,
    stable_release_ref: str | None = None,
) -> str:
    if channel != STABLE_CHANNEL:
        raise SystemError("release channel must be stable")
    if stable_release_ref:
        if Semver.is_semver_string(stable_release_ref):
            parsed_ref = Semver.from_string(stable_release_ref)
            info(f"Using {parsed_ref} as the stable release ref\n")
            return f"vscode@{parsed_ref}"
        else:
            info(f"Assuming value is a commit SHA: {stable_release_ref}\n")
            return stable_release_ref

    extension_data = get_extension_data(raw_extension_data)
    sorted_versions = get_sorted_versions(extension_data)
    prerelease_count = 0
    for version in sorted_versions:
        if version.get_channel() == "prerelease":
            prerelease_count += 1
            if prerelease_count == 2:
                info(f"Using {version} as the stable release ref\n")
                return f"vscode@{version}"

    raise SystemError("Failed to find a suitable pre-release to promote to stable")


def main() -> int:
    parser = argparse.ArgumentParser(
        prog="ProgramName",
        description="What the program does",
        epilog="Text at the bottom of help",
    )

    # new_release will print the version for the next new release
    # stable_release_ref will print the version for the previous pre-release
    # version that should be promoted to stable
    parser.add_argument("command", choices=["new_release", "stable_release_ref"])

    args = parser.parse_args()

    if args.command == "new_release":
        version_override = get_env_var("VERSION_OVERRIDE")
        raw_extension_data = get_env_var("EXTENSION_DATA")
        channel = get_env_var("RELEASE_CHANNEL")
        stable_ref = get_env_var("STABLE_RELEASE_REF")
        print(
            get_new_version(
                version_override,
                raw_extension_data,
                channel,
                stable_ref,
            )
        )
        return 0
    elif args.command == "stable_release_ref":
        raw_extension_data = get_env_var("EXTENSION_DATA")
        channel = get_env_var("RELEASE_CHANNEL")
        stable_ref = get_env_var("STABLE_RELEASE_REF")
        print(
            get_stable_release_ref(
                raw_extension_data,
                channel,
                stable_ref,
            )
        )
        return 0
    return 0


if __name__ == "__main__":
    raise SystemExit(main())
