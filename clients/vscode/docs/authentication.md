# Authentication

The extension has the following behaviors for its OAuth flow:

1. User starts the sign in flow (via the signIn command)
1. The sign in command will:
    1. Setup state and code verification for the flow
    1. Open a browser to the Augment auth service
    1. Wait for one of the following to happen:
        1. The Augment extension to receive a URI (i.e. vscode://augment.vscode-augment/auth/result); OR
        1. The user to start a new sign in (this causes the current sign in attempt to be cancelled); OR
        1. The user to cancel the progress window for sign (causing the current sign in attempt to be cancelled); OR
        1. 10 mins before the extension cancels the sign in attempt
    1. If a URI is received from the previous step, the following happens:
        1. The code and tenant URL are checked
        1. The code is swapped for a token with the tenant URL
        1. The token is stored with vscode as a secret and is used for future
           API calls.

## Timeout

The main reason for a timeout being included in the possible ways for the
sign in flow to be cancelled is to reduce the risk of the following:

1. <PERSON> tries to sign in to Augment
1. <PERSON> (malicious) sees the URL for the auth request and gains access to the state and verifier
1. <PERSON> signs in with their own account using the state and code verifier from the previous step
1. <PERSON> replays the redirect URI from previous step on <PERSON>'s machine
1. <PERSON> is now signed in as <PERSON>

The timeout doesn't prevent this attack, but makes it harder for <PERSON>.
