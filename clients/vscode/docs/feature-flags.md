# Feature Flags
We use the same [**feature flag system**](https://github.com/augmentcode/augment/blob/main/tools/feature_flags/Readme.md) used by our backend to enable/disable features in the client extensions as well.

The flags are exposed in the get_models result (see `BackGetModelsResult`) and you can see how they are passed in `augment-api.ts`.

These feature flags are managed in the extension by the `FeatureFlagManager` class (`feature-flags.ts`), which is instantiated in `extension.ts`.

`FeatureFlagManager.currentFlags` should be considered the main way to access feature flags throughout the application.

## `minVersion` flags (deprecated)
**STOP** using `minVersion`/`min_version` flags. These were needed before we had the `min_client_version`, `max_client_version` and `client` fields in flags.jsonnet. Now that we have these you can send boolean flags directly to the client rather than SemVer strings for the flag values.

The purpose of this is to allow feature rollouts where the feature is not quite finished in version X but is ready for release in X+1 (however the feature flag was already being examined by the extension in version X). By setting a min version we ensure that customers only get the feature once they are on a version that is ready.

## `min_client_version` rules
You can use `min_client_version` rules in `flags.jsonnet` to control which clients are allowed to see a feature. This is useful for rolling out a feature to a subset of clients.

Here is an [example of using these rules](https://github.com/augmentcode/augment/commit/f4c21cf341959f1414885c0af7b30db8177d082a)

## Example PR adding feature flag
Here is an [example PR that added a feature flag](https://github.com/augmentcode/augment/pull/25583/files).

**NOTE** this PR also added a separate client-side flag in `augment-config-listener.ts`. This is not necessary and you may use Client Side Overrides instead.

## Client Side Overrides
When in the staging ("dogfood") environment, you are allowed to override flags through your settings.json by setting `"augment.advanced.featureFlagOverrides": { ... }`. This is not allowed if you are pointed at any other environment though. You can specify the flags by their camelCased names (the same as they would be in FeatureFlagManager.currentFlags).

Note that if you are using minVersion flags then the value you specify in the overrides will also be a version (identical to whatever would be returned from the backend).

Also note that you may need to change the version number in package.json when running the extension in a debugger because the default version "999.999.999" is generally allowed to defeat all "minVersion" checks.

_TODO(navtej): now that we have overrides maybe we don't need this special version anymore?_

## Expected feature development process

1. Create a `my_feature_enabled` feature flag in `flags.jsonnet`, `public-api.proto` and `handlers.rs` and merge it
2. Add the flag to the `FeatureFlags` interface in `feature-flags.ts`
3. Override the flag locally through settings.json while you do development
4. Test with the various values of the flag to make sure your feature turns off safely
5. Merge your feature to `main`
6. Edit `flags.jsonnet` to enable your feature in the staging/dogfood environment
7. Continue development and fixes on your feature
8. Once your feature is ready for customers you can do a gradual ramp by enabling various groups of customers or users at different times (by editing `flags.jsonnet`). You can set the `min_client_version` and `client` rules to restrict which clients are allowed to use the feature.
9. Once your feature is on for everyone you can remove the flag from the client side if you judge it is safe to do so. However your flag will remain forever in `public-api.proto` and `handlers.rs`. You will probably wish to keep the flag in `flags.jsonnet` so that older versions of the extension do not break.

**Note:** as an alternative, you may wish to make the client side flag first if you are uncertain about the future of your feature and want to share it a little internally before actually committing to a flag.

_TODO(navtej): The above note perhaps needs a little more fleshing out as we have not actually done this yet. Do we need to be careful about naming flags that have not been created in the backend yet?_

We have historically done things this way when we did not have feature flag overrides and just used settings.json as a catch-all for various client-side feature switches. The nuisance here was then going through and replacing those usages with the actual feature flag once it was created.
