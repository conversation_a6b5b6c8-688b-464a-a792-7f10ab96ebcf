load("@aspect_bazel_lib//lib:copy_directory.bzl", "copy_directory")
load("@aspect_bazel_lib//lib:copy_to_bin.bzl", "copy_to_bin")
load("@aspect_bazel_lib//lib:copy_to_directory.bzl", "copy_to_directory")
load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")
load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_js//npm:defs.bzl", "stamped_package_json")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//clients/vscode:@vscode/vsce/package_json.bzl", vsce_bin = "bin")
load("@npm//clients/vscode:@wdio/cli/package_json.bzl", wdio_bin = "bin")
load("@npm//clients/vscode:eslint/package_json.bzl", eslint_bin = "bin")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

npm_link_all_packages()

SRC_FILES = glob(
    ["src/**/*.ts"],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
) + [
    "//clients/sidecar/libs:src",
]

TEST_FILES = glob([
    "src/**/__tests__/**/*.ts",
    "src/**/__tests__/__fixtures__/*.json",
    "src/**/__tests__/**/__snapshots__/*.snap",
    "src/**/__tests__/**/*.html",
    "src/**/__mocks__/**/*.ts",
])

EXTENSION_DEPS = [
    "//clients/common/feature-vector-collector:src",
    "//third_party/node-ignore",
    "//third_party/vscode-git",
    ":node_modules/@vscode/vsce",
    ":node_modules/archiver",
    ":node_modules/denque",
    ":node_modules/semver",
    ":node_modules/fuse.js",
    ":node_modules/ignore",
    ":node_modules/isomorphic-fetch",
    ":node_modules/json5",
    ":node_modules/lodash",
    ":node_modules/mac-ca",
    ":node_modules/shlex",
    ":node_modules/uuid",
    ":node_modules/winston",
    ":node_modules/winston-transport",
    ":node_modules/@types/archiver",
    ":node_modules/@types/node",
    ":node_modules/@types/lodash",
    ":node_modules/@types/lodash.throttle",
    ":node_modules/@types/uuid",
    ":node_modules/@types/vscode",
    ":node_modules/@types/semver",
    ":node_modules/monaco-editor",
    ":node_modules/lru-cache",
    ":node_modules/lodash.throttle",
    ":node_modules/@anthropic-ai",
    ":node_modules/encoding",
    ":node_modules/simple-git",
    ":node_modules/zod",
    ":node_modules/p-limit",
    ":node_modules/@types/diff",
    ":node_modules/diff",
    ":node_modules/exponential-backoff",
    ":node_modules/node-diff3",
    ":node_modules/@bufbuild/protobuf",
    ":node_modules/@connectrpc/connect",
]

# TODO: Separate out the WebViews such that this target is no longer needed.
js_library(
    name = "shared_webview_files",
    srcs = [
        "src/chat/chat-types.ts",
        "src/chat/guidelines-types.ts",
        "src/code-edit-types.ts",
        "src/completions/completion-types.ts",
        "src/main-panel/action-cards/types.ts",
        "src/metrics/types.ts",
        "src/metrics/work-timer.ts",
        "src/next-edit/next-edit-types.ts",
        "src/remote-agent-manager/commit-ref-types.ts",
        "src/types/feedback-rating.ts",
        "src/utils/edits/diff-by-line.ts",
        "src/utils/get-diff-explanation.ts",
        "src/utils/ranges.ts",
        "src/utils/remote-agent-setup/types.ts",
        "src/utils/types.ts",
        "src/utils/webviews/messaging.ts",
        "src/utils/webviews/types.ts",
        "src/webview-panels/preference-panel-types.ts",
        "src/webview-panels/remote-agents/common-webview-store.ts",
        "src/webview-panels/remote-agents/types.ts",
        "src/webview-panels/settings-panel-types.ts",
        "src/webview-providers/tool-types.ts",
        "src/webview-providers/webview-messages.ts",
        "src/workspace/types.ts",
        ":node_modules/@types/diff",
        ":node_modules/diff",
        ":node_modules/monaco-editor",
        ":tsconfig",
        ":tsconfig-e2e",
        ":tsconfig-node",
    ],
    visibility = [
        "//clients:__subpackages__",
    ],
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//clients/vscode:__subpackages__"],
    deps = [
        ":tsconfig-e2e",
        ":tsconfig-node",
        "//clients:tsconfig",
    ],
)

ts_config(
    name = "tsconfig-e2e",
    src = "tsconfig.e2e.json",
    visibility = ["//clients/vscode:__subpackages__"],
)

ts_config(
    name = "tsconfig-node",
    src = "tsconfig.node.json",
    visibility = ["//clients/vscode:__subpackages__"],
)

## Files needed for HMR dev build
filegroup(
    name = "dev_hmr",
    srcs = [
        # Build of the extension source
        ":copy_selected_out",

        # Additional files needed for the extension
        ":package",
        ":extension_files",
        "//clients/data/file-ext:augment_supported_extensions",
        "//third_party/node-ignore",
        "//third_party/vscode-git",
    ],
    visibility = ["//clients/vscode:__subpackages__"],
)

## Files needed for non-HMR dev build
filegroup(
    name = "dev",
    srcs = [
        # Build of the extension source
        ":dev_hmr",
        # Webviews
        ":common_webviews",
    ],
    visibility = ["//clients/vscode:__subpackages__"],
)

sh_binary(
    name = "build_dev_to_workspace",
    srcs = ["copy_dev_to_workspace.sh"],
    data = [
        ":dev",
        # Include the :ts target here so we do type
        # checking for dev builds.
        ":ts",
    ],
    deps = [
        ":dev",
    ],
)

sh_binary(
    name = "build_dev_to_workspace_hmr",
    srcs = ["copy_dev_to_workspace.sh"],
    args = ["--hmr"],
    data = [
        ":dev_hmr",
        # Include the :ts target here so we do type
        # checking for dev builds.
        ":ts",
    ],
    deps = [
        ":dev_hmr",
    ],
)

copy_to_bin(
    name = "extension_files",
    srcs = [
        ".vscodeignore",
        "CHANGELOG.md",
        "README.md",
        "augment-icon-font.woff",
        "augment-kb-icon-font.woff",
        "icon.png",
    ] + glob([
        "media/**/*",
    ]),
)

# target to compile the extension
esbuild(
    name = "dev_build",
    # Esbuild can build with json files, whereas ts_project cannot.
    srcs = SRC_FILES + [
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ],
    config = {
        "loader": {
            ".node": "file",
        },
    },
    entry_point = "src/extension.ts",
    external = [
        "vscode",
        "osx-temperature-sensor",
    ],
    format = "cjs",
    minify = False,
    output = "out_dev/extension.js",
    platform = "node",
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = EXTENSION_DEPS,
)

# target to compile the extension
esbuild(
    name = "prod_build",
    # Esbuild can build with json files, whereas ts_project cannot.
    srcs = SRC_FILES + [
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ],
    config = {
        "loader": {
            ".node": "file",
        },
    },
    entry_point = "src/extension.ts",
    external = [
        "vscode",
        "osx-temperature-sensor",
    ],
    format = "cjs",
    minify = True,
    output = "out_prod/extension.js",
    platform = "node",
    sourcemap = False,
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = EXTENSION_DEPS,
)

PKG_EXTENSION_SRC = [
    # Build of extension src
    ":copy_selected_out",

    # Webviews
    ":common_webviews",

    # The stamped package.json
    ":package",

    # Files needed / bundled by vsce
    ":extension_files",
]

PKG_EXTENSION_ARGS = [
    "package",
    "--no-dependencies",
    "--allow-star-activation",
    "--skip-license",
]

vsce_bin.vsce(
    name = "package-extension-prerelease",
    srcs = PKG_EXTENSION_SRC,
    outs = ["vscode-augment-prerelease.vsix"],
    args = PKG_EXTENSION_ARGS + [
        "--pre-release",
        "--out",
        "vscode-augment-prerelease.vsix",
    ],
    chdir = package_name(),
)

vsce_bin.vsce(
    name = "package-extension-stable",
    srcs = PKG_EXTENSION_SRC,
    outs = ["vscode-augment-stable.vsix"],
    args = PKG_EXTENSION_ARGS + [
        "--out",
        "vscode-augment-stable.vsix",
    ],
    chdir = package_name(),
)

build_test(
    name = "package-extension-stable-test",
    targets = [":package-extension-stable"],
)

build_test(
    name = "package-extension-prerelease-test",
    targets = [":package-extension-prerelease"],
)

# This isn't used for build output, but is used for typechecking
# via :ts_typecheck_test
ts_project(
    name = "ts",
    srcs = SRC_FILES,
    no_emit = True,  # Use for typechecking only
    resolve_json_module = True,
    root_dir = "..",  # Needed since we import from sidecar libs
    tsconfig = ":tsconfig",
    deps = EXTENSION_DEPS,
)

# Ensure the types are correct in VSCode
build_test(
    name = "build-ts",
    targets = [":ts"],
)

copy_directory(
    name = "common_webviews",
    src = "//clients/common/webviews:webview-apps",
    out = "common-webviews",
)

jest_test(
    name = "test",
    timeout = "long",
    config = "jest.config.js",
    data = SRC_FILES + TEST_FILES + glob([
        "scripts/**/*.ts",
    ]) + EXTENSION_DEPS + [
        ":node_modules",
        ":package",
        ":tsconfig",
        "babel.config.js",
        "//base/blob_names/test_data:blob-name-test-data-js",
        "//clients/common/webviews:webview-apps",
        "//clients/data/file-ext:augment_supported_extensions",

        # Jest doesn't seem to use the .d.ts type definitions from the :ts
        # target, so it needs to use the source files directly.
        "//clients/sidecar/libs:mocks",
    ],
    include_transitive_types = True,  # Needed for type checking
    node_modules = "//clients/vscode:node_modules",
)

stamped_package_json(
    # name of the resulting `jq` target, must be "package"
    name = "package",
    # Variable set via tools/bzl/workspace-status
    stamp_var = "STABLE_VSCODE_RELEASE_VERSION",
)

filegroup(
    name = "eslint_config",
    srcs = [".eslintrc.js"],
    visibility = ["//clients/vscode:__subpackages__"],
)

eslint_bin.eslint_test(
    name = "eslint_test",
    args = ["./clients/vscode/src/**/*.ts"],
    data = [
        ":eslint_config",
        ":node_modules",
        ":package",
        ":tsconfig",
    ] + SRC_FILES + EXTENSION_DEPS,
)

py_library(
    name = "update_versions",
    srcs = [
        "update_versions.py",
    ],
)

pytest_test(
    name = "update_versions_test",
    srcs = ["update_versions_test.py"],
    deps = [
        ":update_versions",
    ],
)

config_setting(
    name = "debug_mode",
    values = {"compilation_mode": "dbg"},
)

config_setting(
    name = "release_mode",
    values = {"compilation_mode": "opt"},
)

# When build with `--compilation_mode=dbg`, use the :dev_build,
# otherwise use the prod_build.
copy_to_directory(
    name = "copy_selected_out",
    srcs = select({
        ":release_mode": [
            ":prod_build",
            "//clients/sidecar/libs:runtime_bundle",
        ],
        ":debug_mode": [
            ":dev_build",
            "//clients/sidecar/libs:runtime_bundle",
        ],
        "//conditions:default": [
            ":dev_build",
            "//clients/sidecar/libs:runtime_bundle",
        ],
    }),
    out = "out",
    replace_prefixes = {
        "out_dev": "",
        "out_prod": "",
        "clients/sidecar/libs/runtime_prebuilds": "",
    },
)

copy_to_bin(
    name = "e2e_files",
    srcs = [
        "wdio.conf.ts",
        ":package",
    ] + TEST_FILES,
)

wdio_bin.wdio_binary(
    name = "wdio_binary",
    args = ["./wdio.conf.ts"],
    chdir = package_name(),
    data = [
        ":dev",
        ":e2e_files",
        ":node_modules",
        ":tsconfig",
    ],
)

# These tests are not currently supported in our CI environment.
# TODO (mattgauntseo): Fix the CI image
sh_test(
    name = "e2e",
    srcs = ["run_e2e_tests.sh"],
    data = [
        ":wdio_binary",
    ],
    tags = [
        "exclusive",
    ],  # exclusive runs the test locally, not remotely, needed because the test requires a display
    deps = ["@bazel_tools//tools/bash/runfiles"],
)
