import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import * as vscode from "vscode";

import { getLogger } from "./logging";
import { readFileUtf8 } from "./utils/fs-utils";
import type { AsyncMsgHandler } from "./utils/webviews/messaging";
import {
    type ReadFileRequest,
    type ReadFileResponse,
    WebViewMessageType,
} from "./webview-providers/webview-messages";

export class ResolveFileService {
    private _logger = getLogger("ResolveFileService");

    constructor() {}

    private getOpenDocumentText(fsPath: string): string | null {
        for (const doc of vscode.workspace.textDocuments) {
            if (doc.uri.fsPath === fsPath && doc.uri.scheme === "file") {
                return doc.getText();
            }
        }
        return null;
    }

    /**
     * Get the content of a file, preferring the open document buffer if available.
     * @param pathName
     * @returns content of file or "" if not found
     */
    public async resolveFile(pathName: IQualifiedPathName) {
        const fsPath = QualifiedPathName.from(pathName).absPath;

        let content = this.getOpenDocumentText(fsPath);
        if (content == null) {
            try {
                content = await readFileUtf8(fsPath);
            } catch (e) {
                this._logger.warn(`Could not read file: ${fsPath}`, e);
                content = "";
            }
        }
        return content;
    }

    /**
     * Register a handler for "readFileRequest"
     */
    public register(handler: AsyncMsgHandler) {
        handler.registerHandler<ReadFileRequest, ReadFileResponse>(
            WebViewMessageType.readFileRequest,
            async (req: ReadFileRequest) => ({
                type: WebViewMessageType.readFileResponse,
                data: {
                    pathName: req.data.pathName,
                    content: await this.resolveFile(req.data.pathName),
                },
            })
        );
    }
}
