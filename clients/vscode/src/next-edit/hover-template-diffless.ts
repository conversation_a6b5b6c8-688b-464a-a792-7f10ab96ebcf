import * as vscode from "vscode";

export type LinkAndText = {
    href: string;
    text: string;
    tooltip?: string | null;
    keybindingIcons?: string | null;
    noLeftMargin?: boolean;
};

/**
 *
 * @param content the main text of the box in HTML and/or markdown. Expected to be safely escaped
 * @param headerButtons
 * @param footerContent the footer text in HTML and/or markdown. Expected to be safely escaped
 * @param footerButtons
 * @param contentWidthInFixedChars width of the "main" content of the hover measured in fixed-width characters
 * @returns
 */
export function hoverTemplate(
    content: string,
    headerButtons: Array<LinkAndText>,
    footerContent?: string,
    _footerButtons?: Array<LinkAndText>,
    _contentWidthInFixedChars?: number
): vscode.MarkdownString {
    const header = "$(augment-icon-simple)";
    let buttonsHtml = headerButtons.length > 0 ? _buttonHtml(headerButtons[0]) : "";
    for (let i = 1; i < headerButtons.length; i++) {
        let leftMargin = headerButtons[i].noLeftMargin
            ? "&nbsp;&nbsp;"
            : "&nbsp;&nbsp;&nbsp;&nbsp;";
        buttonsHtml += `${leftMargin}${_buttonHtml(headerButtons[i])}`;
    }
    const footer = footerContent ? `<br/>${footerContent}` : "";
    const str = new vscode.MarkdownString(
        `${header}&nbsp;&nbsp;&nbsp;&nbsp;${buttonsHtml}<br/>${content}${footer}`
    );
    str.isTrusted = true;
    str.supportHtml = true;
    str.supportThemeIcons = true;
    return str;
}

function _buttonHtml(button: LinkAndText) {
    return `<a href="${button.href}" title="${button.tooltip}">${button.text}${button.keybindingIcons ? `&nbsp;${button.keybindingIcons}` : ""}</a>`;
}
