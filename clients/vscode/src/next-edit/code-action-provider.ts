import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { NextEditBackgroundAcceptCodeActionCommand } from "../commands/next-edit";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { LineRange } from "../utils/ranges";
import {
    ChangeType,
    NextEditSessionEventName,
    NextEditSessionEventSource,
} from "./next-edit-types";
import { SuggestionManager } from "./suggestion-manager";

export class BackgroundNextEditsCodeActionProvider implements vscode.CodeActionProvider {
    constructor(
        private readonly _suggestionManager: SuggestionManager,
        private readonly _configListener: AugmentConfigListener,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter
    ) {}

    public provideCodeActions(
        document: vscode.TextDocument,
        range: vscode.Range | vscode.Selection,
        _context: vscode.CodeActionContext,
        _token: vscode.CancellationToken
    ): vscode.CodeAction[] | undefined {
        if (!this._configListener.config.enableDebugFeatures) {
            return;
        }

        const lineRange = new LineRange(range.start.line, range.end.line);
        const suggestion = this._suggestionManager
            .getActiveSuggestions()
            .find(
                (s) =>
                    s.changeType !== ChangeType.noop &&
                    s.previewCursorRange(document).intersects(lineRange)
            );
        if (!suggestion) {
            return;
        }
        const description = suggestion.result.changeDescription || "Augment: Accept suggestion";

        const action = new vscode.CodeAction(description, vscode.CodeActionKind.QuickFix);
        action.command = {
            command: NextEditBackgroundAcceptCodeActionCommand.commandID,
            title: description,
            arguments: [suggestion],
        };

        this._nextEditSessionEventReporter.reportEventFromSuggestion(
            suggestion,
            NextEditSessionEventName.CodeActionShown,
            NextEditSessionEventSource.CodeAction
        );

        return [action];
    }

    public resolveCodeAction(
        codeAction: vscode.CodeAction,
        _token: vscode.CancellationToken
    ): vscode.CodeAction {
        return codeAction;
    }
}
