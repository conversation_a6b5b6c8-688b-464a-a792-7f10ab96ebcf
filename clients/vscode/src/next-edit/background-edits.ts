import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { AugmentGlobalState } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { KeybindingWatcher } from "../utils/keybindings";
import { isNotebookDocument, isNotebookUri } from "../utils/notebook";
import { toLineRange } from "../utils/ranges-vscode";
import { isNextEditSupportedPath } from "../utils/uri";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { CompletionVisibilityWatcher } from "./completion-visibility-watcher";
import { NextEditConfigManager } from "./next-edit-config-manager";
import {
    ChangeType,
    NextEditMode,
    NextEditScope,
    NextEditSessionEventName,
    NextEditSessionEventSource,
    SuggestionState,
} from "./next-edit-types";
import { INextEditRequestManager } from "./request-manager";
import { SuggestionManager } from "./suggestion-manager";
import { NextEditTutorial } from "./tutorial";
import { currentlyShowingDiffOrMerge, isFreshChange, isFreshSuggestion } from "./utils";

export enum NextMode {
    next,
    closest,
}

export class BackgroundNextEdits extends DisposableService {
    private _logger = getLogger("BackgroundNextEdits");
    private _noopSources = new Set<NextEditSessionEventSource>(); // for analytics
    private _noopReportTimer: NodeJS.Timeout | null = null;
    private _noopReportDelayMs = 60000 * 5; // every 5 minutes
    private _debugRequestCaching = true; // Flag to control detailed request caching logs
    private _delayedFileRequestTimer: NodeJS.Timeout | null = null;
    private static readonly _fileRequestDelayMs = 4000; // 4 seconds delay

    /**
     * Log a verbose message only if debugRequestCaching is enabled
     */
    private _logRequestCaching(message: string | (() => string)): void {
        if (this._debugRequestCaching) {
            const finalMessage = typeof message === "string" ? message : message();
            this._logger.verbose(finalMessage);
        }
    }

    /**
     * Cancel any pending delayed file request
     */
    private _cancelDelayedFileRequest(): void {
        if (this._delayedFileRequestTimer) {
            this._logRequestCaching("Cancelling delayed FILE request");
            clearTimeout(this._delayedFileRequestTimer);
            this._delayedFileRequestTimer = null;
        }
    }

    constructor(
        private readonly workspaceManager: WorkspaceManager,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter,
        keybindingWatcher: KeybindingWatcher,
        private readonly _configListener: AugmentConfigListener,
        private readonly _suggestionManager: SuggestionManager,
        private readonly _requestManager: INextEditRequestManager,
        globalState: AugmentGlobalState,
        private readonly _nextEditConfigManager: NextEditConfigManager,
        private readonly _completionVisibilityWatcher: CompletionVisibilityWatcher
    ) {
        super();

        this.addDisposable(
            new vscode.Disposable(() => {
                // Clear recently-completed requests so that if we re-enable background mode we'll
                // make new requests.
                // Note that we clear all requests (not just background ones) since background ones
                // can overwrite earlier foreground requests, in which case we'd clear the
                // suggestion.  If we didn't clear the associated foreground request, we wouldn't
                // be able to make a new one.  Since this is only a minor optimization (for
                // foreground requests), it doesn't seem worth fixing.
                this._requestManager?.clearCompletedRequests();
                // Note that we only clear background suggestions.
                // TODO: We could potentially be smarter here and keep the suggestions
                // and just not show them until we re-enable, but that seems rather
                // low priority.
                this._suggestionManager?.clear(false, NextEditMode.Background);

                if (this._noopReportTimer) {
                    clearTimeout(this._noopReportTimer);
                    this._noopReportTimer = null;
                }

                // Clean up delayed file request timer
                this._cancelDelayedFileRequest();
            })
        );

        // Schedule the periodic noop report
        this._scheduleNoopReport();

        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument(this._handleTextDocumentChanged)
        );
        this.addDisposable(
            vscode.window.onDidChangeTextEditorSelection(this._handleTextEditorSelectionChanged)
        );
        this.addDisposable(
            new vscode.Disposable(
                // Handle cascading requests: CURSOR -> FILE -> WORKSPACE
                this._requestManager.lastFinishedRequest.listen((result) => {
                    if (
                        !result ||
                        result.apiResult !== APIStatus.ok ||
                        result.mode !== NextEditMode.Background
                    ) {
                        return;
                    }

                    const editor = vscode.window.activeTextEditor;
                    const qualifiedPath =
                        editor && this.workspaceManager.safeResolvePathName(editor.document.uri);

                    if (result.suggestions.length === 0) {
                        this._logRequestCaching(
                            `Request (scope=${result.scope}) returned no suggestions.`
                        );
                        return;
                    }

                    if (result.scope === NextEditScope.Cursor) {
                        const freshChanges = result.suggestions.filter(isFreshChange);
                        if (freshChanges.length === 0) {
                            // Immediate fallback to FILE if no suggestions
                            this._logRequestCaching(
                                "CURSOR request returned no results, falling back to FILE request immediately"
                            );
                            this._requestManager.enqueueRequest(
                                qualifiedPath,
                                NextEditMode.Background,
                                NextEditScope.File
                            );
                        } else {
                            // Schedule a delayed FILE request if we got suggestions
                            this._logRequestCaching(
                                `CURSOR request returned ${freshChanges.length} suggestions, scheduling delayed FILE request in ${BackgroundNextEdits._fileRequestDelayMs}ms`
                            );
                            this._delayedFileRequestTimer = setTimeout(() => {
                                this._logRequestCaching("Enqueuing delayed FILE request");
                                this._requestManager.enqueueRequest(
                                    qualifiedPath,
                                    NextEditMode.Background,
                                    NextEditScope.File
                                );
                                this._delayedFileRequestTimer = null;
                            }, BackgroundNextEdits._fileRequestDelayMs);
                        }
                    } else if (
                        result.scope === NextEditScope.File &&
                        result.suggestions.filter(isFreshChange).length === 0 &&
                        this._configListener.config.nextEdit.enableGlobalBackgroundSuggestions
                    ) {
                        // Enqueue a WORKSPACE request if the FILE request finished without any results
                        // and global background suggestions are enabled
                        this._logRequestCaching(
                            "FILE request returned no results, falling back to WORKSPACE request"
                        );
                        this._requestManager.enqueueRequest(
                            qualifiedPath,
                            NextEditMode.Background,
                            NextEditScope.Workspace
                        );
                    }
                })
            )
        );
        this.addDisposable(
            this._suggestionManager.onSuggestionsChanged((event) => {
                // If we just accepted a truncated suggestion,
                // make a new cursor request at its location.
                for (const suggestion of event.accepted.filter(
                    (s) => s.result.truncationChar !== undefined
                )) {
                    this._requestManager.enqueueRequest(
                        suggestion.qualifiedPathName,
                        NextEditMode.Background,
                        NextEditScope.Cursor,
                        suggestion.lineRange
                    );
                }
                // If we just accepted a suggestion and if the only fresh suggestions from that
                // request are truncated, make a new cursor request for those remaining
                // truncated suggestions.
                const acceptedRIs = event.accepted.map((s) => s.requestId);
                const suggestionsPerRI = acceptedRIs.map((r) =>
                    event.newSuggestions.filter((s) => isFreshChange(s) && s.requestId === r)
                );
                for (const suggestions of suggestionsPerRI) {
                    if (!suggestions.some((s) => s.result.truncationChar === undefined)) {
                        for (const suggestion of suggestions) {
                            this._requestManager.enqueueRequest(
                                suggestion.qualifiedPathName,
                                NextEditMode.Background,
                                NextEditScope.Cursor,
                                suggestion.lineRange
                            );
                        }
                    }
                }
            })
        );

        this.addDisposable(
            new NextEditTutorial(
                this._configListener,
                this._suggestionManager,
                keybindingWatcher,
                globalState,
                _nextEditSessionEventReporter,
                this._nextEditConfigManager,
                this._completionVisibilityWatcher
            )
        );

        // Send a request.
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        const qualifiedPath = this.workspaceManager.safeResolvePathName(editor.document.uri);
        if (!qualifiedPath) {
            return;
        }
        // Only send CURSOR request initially, it will in turn queue up FILE requests
        this._requestManager.enqueueRequest(
            qualifiedPath,
            NextEditMode.Background,
            NextEditScope.Cursor
        );
    }

    private get _isDebugging() {
        return (
            vscode.debug.activeDebugSession !== undefined &&
            !this._configListener.config.nextEdit.allowDuringDebugging
        );
    }

    private _scheduleNoopReport() {
        // Clear any existing timer
        if (this._noopReportTimer) {
            clearTimeout(this._noopReportTimer);
            this._noopReportTimer = null;
        }

        // Schedule the report after the delay
        this._noopReportTimer = setTimeout(() => {
            this._reportNoopSources();
            // Reschedule for the next interval
            this._scheduleNoopReport();
        }, this._noopReportDelayMs);
    }

    private _reportNoopSources() {
        if (this._noopSources.size === 0) {
            return;
        }
        for (const source of this._noopSources.values()) {
            this._nextEditSessionEventReporter.reportEventWithoutIds(
                NextEditSessionEventName.BackgroundNoop,
                source
            );
        }
        this._noopSources.clear();
    }

    /**
     * Handle changes to the text.
     *
     * All the logic of actually updating suggestions happens in `SuggestionManager`:
     * the manager figures out if the change event "accepted" any suggestions or not,
     * and updates the state of suggestions to be "stale" if so.
     *
     * The role of this function is to determine whether to make a new request or not,
     * and that logic simply boils down to:
     * - are there any fresh suggestions? If so, don't make requests.
     * - otherwise, make a new request.
     */
    private _handleTextDocumentChanged = (event: vscode.TextDocumentChangeEvent) => {
        // Ignore empty events.
        if (event.contentChanges.length === 0) {
            this._noopSources.add(NextEditSessionEventSource.NoContentChanges);
            return;
        }

        if (this._isDebugging) {
            this._noopSources.add(NextEditSessionEventSource.DebugSession);
            return;
        }

        if (isNotebookDocument(event.document)) {
            // notebook is interesting to us specifically as we plan to add support to it.
            // so lets track it specifically.
            this._noopSources.add(NextEditSessionEventSource.NotebookDocument);
            return;
        }

        // Ignore notebooks, since they're tricky (they're json on-disk and
        // VSCode events see cells individually).
        if (!isNextEditSupportedPath(event.document.uri)) {
            this._noopSources.add(NextEditSessionEventSource.UnsupportedUri);
            return;
        }

        const qualifiedPathName = this.workspaceManager.safeResolvePathName(event.document.uri);
        // We also ignore making new requests on documents outside of the workspace.
        if (!qualifiedPathName) {
            this._noopSources.add(NextEditSessionEventSource.MissingPathName);
            return;
        }

        // We won't try to make a new request on anything but the active editor.
        const editor = vscode.window.activeTextEditor;
        if (!editor || event.document !== editor.document) {
            this._noopSources.add(NextEditSessionEventSource.NotActiveEditor);
            return;
        }
        this._logger.verbose(`Received TextDocumentChangeEvent for ${qualifiedPathName.relPath}`);

        // Cancel any delayed file request before sending a new cursor request
        this._cancelDelayedFileRequest();
        this._logRequestCaching("Canceled delayed FILE request due to document change.");

        // NOTE(arun): We used to previously clear a cursor hint if the cursor moved.
        // We are not doing this anymore, as it's not clear that it's a good idea, and
        // it made this bit of code complicated.

        // NOTE(arun): We used to track "just triggered next" to avoid starting a new
        // query. However, if we're moving to a suggestion, we'll never make a new query
        // anyways, so this was unnecessary.

        // When the user just accepted a suggestion and we have more suggestions
        // available, we'll continue using them before making a new request.
        // Note that we do not consider truncated suggestions here, since we hope the model will
        // complete them.
        const freshSuggestions = this._suggestionManager
            .getActiveSuggestions()
            .filter(
                (s) =>
                    s.qualifiedPathName.equals(qualifiedPathName) &&
                    isFreshChange(s) &&
                    s.result.truncationChar === undefined
            );
        if (freshSuggestions.length > 0) {
            this._noopSources.add(NextEditSessionEventSource.FreshSuggestions);
            this._logRequestCaching("Continuing with remaining fresh suggestions.");
            return;
        }

        // There are no fresh suggestions, so we'll make a new request.
        // Note that the suggestion manager has already marked all suggestions as stale,
        // so we don't need to clear anything.

        // Only send CURSOR request initially, it will in turn queue up FILE requests
        this._requestManager.enqueueRequest(
            qualifiedPathName,
            NextEditMode.Background,
            NextEditScope.Cursor
        );
    };

    /**
     * Handle the cursor moving.
     *
     * When the cursor moves, the document hasn't changed, and most suggestions remain
     * fresh, but the "in focus" suggestion will change.
     *
     * We will make a new query if:
     * - the cursor is not in a fresh suggestion region.
     * - (TODO): there's not an inflight request within 10 lines of the cursor.
     */
    private _handleTextEditorSelectionChanged = (event: vscode.TextEditorSelectionChangeEvent) => {
        const editor = vscode.window.activeTextEditor;
        if (!editor || event.textEditor.document !== editor.document) {
            return;
        }

        if (currentlyShowingDiffOrMerge()) {
            return;
        }
        if (this._isDebugging) {
            return;
        }

        // Ignore notebooks, since they're tricky (they're json on-disk and
        // VSCode events see cells individually).
        if (isNotebookUri(editor.document.uri)) {
            return;
        }
        const qualifiedPath = this.workspaceManager.safeResolvePathName(
            event.textEditor.document.uri
        );
        if (!qualifiedPath) {
            return;
        }

        // Determine if we need to make a new request.

        // Get all active suggestions that are fresh (including no-ops) and are in the current file
        // We include no-ops for cursor movement detection to avoid triggering new requests
        // when the cursor moves to a line with a no-op suggestion
        const freshSuggestions = this._suggestionManager
            .getActiveSuggestions()
            .filter(
                (s) =>
                    s.qualifiedPathName.equals(qualifiedPath) &&
                    isFreshSuggestion(s) &&
                    s.result.truncationChar === undefined
            )
            // Sort suggestions by their line range start position
            .sort((a, b) => {
                const aRange =
                    a.state === SuggestionState.accepted
                        ? a.afterLineRange(editor.document)
                        : a.lineRange;
                const bRange =
                    b.state === SuggestionState.accepted
                        ? b.afterLineRange(editor.document)
                        : b.lineRange;
                return aRange.start - bRange.start;
            });

        this._logRequestCaching(
            () =>
                `Found ${freshSuggestions.length} fresh suggestions in current file: ${freshSuggestions
                    .map((s) => {
                        const range =
                            s.state === SuggestionState.accepted
                                ? s.afterLineRange(editor.document)
                                : s.lineRange;
                        const type = s.changeType === ChangeType.noop ? "no-op" : "change";
                        return `[${range.toString()}, ${type}]`;
                    })
                    .join(", ")}`
        );

        const cursorRange = toLineRange(editor.selection);
        this._logRequestCaching(`Cursor moved to line range: ${cursorRange.toString()}`);

        // Find which suggestion(s) the cursor is in, if any
        const overlappingSuggestions = freshSuggestions.filter((s) => {
            const sRange =
                s.state === SuggestionState.accepted
                    ? s.afterLineRange(editor.document)
                    : s.lineRange;
            return sRange.intersects(cursorRange) || sRange.touches(cursorRange);
        });

        const cursorInFreshSuggestion = overlappingSuggestions.length > 0;

        if (!cursorInFreshSuggestion) {
            this._logRequestCaching(
                "Cursor not in any fresh suggestion region, sending new CURSOR request"
            );
            // There is no fresh suggestion in the cursor's range, so let's query the
            // server.

            // Cancel any delayed file request before sending a new cursor request
            this._cancelDelayedFileRequest();
            this._logRequestCaching("Canceled delayed FILE request due to cursor movement.");

            // Only send CURSOR request initially, it will in turn queue up FILE requests
            this._requestManager.enqueueRequest(
                qualifiedPath,
                NextEditMode.Background,
                NextEditScope.Cursor
            );
        } else {
            // Log which suggestion(s) the cursor is in
            for (const suggestion of overlappingSuggestions) {
                const sRange =
                    suggestion.state === SuggestionState.accepted
                        ? suggestion.afterLineRange(editor.document)
                        : suggestion.lineRange;
                const type = suggestion.changeType === ChangeType.noop ? "no-op" : "change";
                this._logRequestCaching(
                    `Cursor is in fresh suggestion at lines ${sRange.toString()}, ` +
                        `suggestion ID: ${suggestion.requestId}, ` +
                        `state: ${SuggestionState[suggestion.state]}, ` +
                        `type: ${type}`
                );
            }
            this._logRequestCaching(
                "Not sending new request as cursor is in fresh suggestion regions"
            );
        }
    };
}
