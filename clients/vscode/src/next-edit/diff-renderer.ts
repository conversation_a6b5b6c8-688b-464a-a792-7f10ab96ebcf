import { truncate } from "@augment-internal/sidecar-libs/src/utils/strings";
import os from "os";

import { AugmentLogger } from "../logging";
import { isVsCodeVersionGte } from "../utils/environment";
import { borderRadiusAllowed } from "../utils/markdown-html-utils";
import { CharRange } from "../utils/ranges";
import { utf32Length } from "../utils/unicode";
import { escapeAndBreakMarkdownCodicons } from "./hover-utils";
import { AnnotatedOneLineDiffSpan, ChangeType } from "./next-edit-types";
import { EditSuggestion } from "./suggestion-manager";
import { WhitespaceInfo } from "./whitespace-info";

// if you want to use these types outside of this file then move it to next-edit-types.ts
enum RenderableLineType {
    original = "original",
    updated = "updated",
    noop = "noop",
}

type RenderableDiffLine = {
    type: RenderableLineType;
    spans: RenderableSpan[];
    originalLineNumber: number;
    updatedLineNumber: number;
};

type RenderableSpan = {
    type: ChangeType;
    text: string;
    charRange: CharRange;
};

/**
 * Colors specified as valid CSS, however we are limited to whatever is allowed here:
 * https://github.com/microsoft/vscode/blob/1.93.0/src/vs/base/browser/markdownRenderer.ts#L401
 * If you assigned a color and found it is not being applied, it's likely being rejected by vscode.
 * These colors will not be stacking, so inline colors need to apply their full color on their own.
 */
export type DiffColors = {
    originalLineColor: string;
    originalTextColor: string;
    updatedLineColor: string;
    updatedTextColor: string;
    lineNumberColor: string;
};

/**
 * Produce an inline diff in VSCode-markdown friendly HTML.
 * @param suggestion
 * @param beforeContextText A single line of text for the line before the changed code.
 * @param afterContextText A single line of text for the line after the changed code.
 * @param minWidthInFixedWidthChars The minimum width of the diff in fixed-width chars of the same font as the diff.
 * @param colors the colors to use for the diff.
 * @param trimLeadingWhitespace whether or not to trim leading whitespace
 * @param logger a logger for warnings and errors.
 * @returns complete HTML for the diff as a string.
 */
export function renderFullDiff(
    suggestion: EditSuggestion,
    beforeContextText: string | undefined,
    afterContextText: string | undefined,
    minWidthInFixedWidthChars: number,
    colors: DiffColors,
    trimLeadingWhitespace?: boolean,
    logger?: AugmentLogger
): { result: string; longestLineLength: number } {
    const linesToRender = spansToLines(suggestion.makeOneLineDiffSpans());
    // compute how many lines were added/removed so we can figure out the new ending line number
    const lineCountDelta =
        linesToRender.filter((line) => line.type === RenderableLineType.updated).length -
        linesToRender.filter((line) => line.type === RenderableLineType.original).length;
    // +1 to account for the 0-based line numbers, +1 to account for context line, +1 length for spacing
    const lineNumberLength = (suggestion.lineRange.stop + 2 + lineCountDelta).toString().length + 1;

    let whitespaceInfo: WhitespaceInfo;
    if (trimLeadingWhitespace) {
        const beforeContextLines = beforeContextText?.split("\n") ?? [];
        const afterContextLines = afterContextText?.split("\n") ?? [];
        const allLines = [
            ...beforeContextLines,
            ...linesToRender.map((line) => line.spans.map((s) => s.text).join("")),
            ...afterContextLines,
        ];
        whitespaceInfo = WhitespaceInfo.computeCommonLeadingWhitespace(allLines);
        beforeContextText = beforeContextLines
            .map((line) => whitespaceInfo.trimLeadingFull(line, logger))
            .join("\n");
        afterContextText = afterContextLines
            .map((line) => whitespaceInfo.trimLeadingFull(line, logger))
            .join("\n");
    } else {
        whitespaceInfo = WhitespaceInfo.computeCommonLeadingWhitespace([]);
    }

    // be careful here -- since this is markdown adding extra indentation can cause it to be treated like a code block.
    // Also, since this goes into a <pre> tag, newlines are meaningful so extra newlines in spans may be rendered as newlines.

    const minWidth = minWidthInFixedWidthChars;

    const { rendered: resultHtml, longestLineLength } = renderLines(
        linesToRender,
        minWidth,
        colors,
        suggestion.result.truncationChar,
        whitespaceInfo,
        logger
    );
    // truncate context texts to longestLineLength (which already accounts for minWidth)
    // basically we do not want the context lines to be the reason for the diff to be too wide.
    if (beforeContextText) {
        beforeContextText = truncate(beforeContextText, longestLineLength);
    }
    if (afterContextText) {
        afterContextText = truncate(afterContextText, longestLineLength);
    }

    const beforeLineNum = leftPad(suggestion.lineRange.start, lineNumberLength);
    const beforeHtml = !beforeContextText
        ? ""
        : `<span style="color:${colors.lineNumberColor};">${beforeLineNum}${beforeLineNum}<span class="codicon codicon-blank"></span> ${escapeAndBreakMarkdownCodicons(rightPad(trimTrailingNewline(beforeContextText), longestLineLength + 1))}</span>\n`;

    const afterLineNumOriginal = leftPad(suggestion.lineRange.stop + 1, lineNumberLength);

    const afterLineNumUpdated = leftPad(
        suggestion.lineRange.stop + 1 + lineCountDelta,
        lineNumberLength
    );
    // We make the font bold on windows because it is quite faint
    // otherwise (typewriter style font - Courier New maybe?)
    let strongStart = "",
        strongEnd = "";
    if (os.platform() === "win32") {
        strongStart = "<strong>";
        strongEnd = "</strong>";
    }
    const backgroundStyle =
        `background-color:var(--vscode-editor-background);` +
        (multilineSpanBorderRadiusAllowed() ? "border-radius:5px;" : "");
    const afterHtml = !afterContextText
        ? ""
        : `\n<span style="color:${colors.lineNumberColor};">${afterLineNumOriginal}${afterLineNumUpdated}<span class="codicon codicon-blank"></span> ${escapeAndBreakMarkdownCodicons(rightPad(trimTrailingNewline(afterContextText), longestLineLength + 1))}</span>`;
    return {
        result: `
${strongStart}<pre><span style="${backgroundStyle}">${beforeHtml}${resultHtml}${afterHtml}</span></pre>${strongEnd}
`,
        // TODO: I think this should be lineNumberLength * 2
        // but I don't want to mess up the existing width calculations
        longestLineLength: longestLineLength + lineNumberLength,
    };
}

function multilineSpanBorderRadiusAllowed(): boolean {
    // in 1.97 spans lost their inline-block styling so a multi-line span with border radius does not look
    // like a box but instead a series of lines where the first line has a radius and the last line has a radius
    // and the middle lines are straight.
    return borderRadiusAllowed() && !isVsCodeVersionGte("1.97.0");
}

/**
 * This is the hard part, we are grouping lines so that we show a reasonable inline diff.
 * The general idea is to output the lines in order, but show old code on top of new code
 * for lines that have changed. For lines that have not changed we only show them once.
 * We break groups of old/new pairs up when we encounter a no-op line
 * example:
 * old
 * old
 * new
 * new
 * no-op
 * new
 * no-op
 * old
 * old
 * new
 * new
 *
 * @param spans Diff spans that are guaranteed to be no longer than one line. They
 * may or may not end in a newline.
 * @returns lines in the order they should be rendered for an inline diff
 */
function spansToLines(spans: AnnotatedOneLineDiffSpan[]): RenderableDiffLine[] {
    const originalLines: RenderableDiffLine[] = [];
    const updatedLines: RenderableDiffLine[] = [];

    if (spans.length === 0) {
        return [];
    }
    let currentOriginalLine: RenderableDiffLine = {
        type: RenderableLineType.noop,
        spans: [],
        originalLineNumber: -1, // we don't know it yet.
        updatedLineNumber: -1, // we don't know it yet.
    };
    let currentUpdatedLine: RenderableDiffLine = {
        type: RenderableLineType.noop,
        spans: [],
        originalLineNumber: -1, // we don't know it yet.
        updatedLineNumber: -1, // we don't know it yet.
    };

    // actual blank lines should get counted here because they contain a newline char.
    // this should only strip out fully empty lines (phantom lines of a sort).
    const pushCurrentOriginalLine = () => {
        if (currentOriginalLine.spans.some((s) => s.text.length > 0)) {
            originalLines.push(currentOriginalLine);
        }
    };
    const pushCurrentUpdatedLine = () => {
        if (currentUpdatedLine.spans.some((s) => s.text.length > 0)) {
            updatedLines.push(currentUpdatedLine);
        }
    };

    for (const span of spans) {
        if (span.original.text !== "" || !span.updated.isWholeLine) {
            currentOriginalLine.spans.push({
                type: span.type,
                text: span.original.text,
                charRange: span.original.charRange,
            });
            currentOriginalLine.originalLineNumber = span.original.line;
            if (span.type !== ChangeType.noop) {
                currentOriginalLine.type = RenderableLineType.original;
            } else {
                // we track both line numbers for no-op lines
                currentOriginalLine.updatedLineNumber = span.updated.line;
            }
        }
        if (span.updated.text !== "" || !span.original.isWholeLine) {
            currentUpdatedLine.spans.push({
                type: span.type,
                text: span.updated.text,
                charRange: span.updated.charRange,
            });
            currentUpdatedLine.updatedLineNumber = span.updated.line;
            if (span.type !== ChangeType.noop) {
                currentUpdatedLine.type = RenderableLineType.updated;
            } else {
                // we track both line numbers for no-op lines
                currentUpdatedLine.originalLineNumber = span.original.line;

                if (currentUpdatedLine.type !== RenderableLineType.noop) {
                    // this is a no-op span in a non-no-op line, so the original
                    // line must not be a no-op.
                    currentOriginalLine.type = RenderableLineType.original;
                } else {
                    // this is a no-op span in a no-op line (so far), but we
                    // must check that the matching span's original line is also
                    // a no-op.
                    if (currentOriginalLine.type !== RenderableLineType.noop) {
                        currentUpdatedLine.type = RenderableLineType.updated;
                    }
                }
            }
        }
        if (span.original.text.endsWith("\n")) {
            pushCurrentOriginalLine();
            currentOriginalLine = {
                type: RenderableLineType.noop,
                spans: [],
                originalLineNumber: -1, // we don't know it yet.
                updatedLineNumber: -1, // we don't know it yet.
            };
        }
        if (span.updated.text.endsWith("\n")) {
            pushCurrentUpdatedLine();
            currentUpdatedLine = {
                type: RenderableLineType.noop,
                spans: [],
                originalLineNumber: -1, // we don't know it yet.
                updatedLineNumber: -1, // we don't know it yet.
            };
        }
    }
    pushCurrentOriginalLine();
    pushCurrentUpdatedLine();

    return groupLines(originalLines, updatedLines);
}

function groupLines(
    originalLines: RenderableDiffLine[],
    updatedLines: RenderableDiffLine[]
): RenderableDiffLine[] {
    const result: RenderableDiffLine[] = [];
    let infiniteLoopGuard = 0;
    let i = 0,
        j = 0;
    while (i < originalLines.length || j < updatedLines.length) {
        if (infiniteLoopGuard++ > 10000) {
            throw new Error("infinite loop in groupLines");
        }
        // first push the original lines until we encounter a no-op line
        while (i < originalLines.length && originalLines[i].type !== RenderableLineType.noop) {
            result.push(originalLines[i]);
            i++;
        }
        // then push updated lines until we encounter the same no-op line.
        while (j < updatedLines.length && updatedLines[j].type !== RenderableLineType.noop) {
            result.push(updatedLines[j]);
            j++;
        }

        // then push the no-op lines
        while (
            i < originalLines.length &&
            j < updatedLines.length &&
            originalLines[i].type === RenderableLineType.noop &&
            updatedLines[j].type === RenderableLineType.noop
        ) {
            result.push(originalLines[i]);
            i++;
            j++;
        }
    }
    return result;
}

/**
 * Produce HTML from an array of lines that are already ordered.
 * @param lines RenderableDiffLine[] already in the right order for an inline diff
 * @returns a single string of HTML suitable to be put inside of a <pre> tag
 */
function renderLines(
    lines: RenderableDiffLine[],
    minWidthInFixedWithChars: number,
    colors: DiffColors,
    truncationChar: number | undefined,
    whitespaceInfo: WhitespaceInfo,
    logger?: AugmentLogger
): { rendered: string; longestLineLength: number } {
    if (lines.length === 0) {
        return { rendered: "", longestLineLength: 0 };
    }

    const { linePadAmounts, longestLineLength } = computeRightPadding(
        lines,
        minWidthInFixedWithChars,
        whitespaceInfo
    );

    // +1 to account for the 0-based line numbers, +1 to account for context line, +1 length for spacing
    const largestLineNumber = lines
        .map((line) => line.updatedLineNumber)
        .concat(lines.map((line) => line.originalLineNumber))
        .reduce((a, b) => Math.max(a, b), 0);
    const lineNumberLength = (largestLineNumber + 2).toString().length + 1;

    let result = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        let str, backgroundColor;
        if (line.type === RenderableLineType.noop) {
            str = line.spans.map((s) => s.text).join("");
            str = whitespaceInfo.trimLeadingFull(str);
            backgroundColor = "#********"; // transparent
        } else if (line.type === RenderableLineType.original) {
            str = renderInlineHighlights(line.spans, true, colors, whitespaceInfo, logger);
            backgroundColor = colors.originalLineColor;
        } else {
            str = renderInlineHighlights(line.spans, false, colors, whitespaceInfo, logger);
            backgroundColor = colors.updatedLineColor;
        }
        // drop any newline chars at the end of the string, we want the newline
        // to be outside the outer span so we'll put it back when we join the lines.
        str = trimTrailingNewline(str);
        // no-op lines have both line numbers, but changed spans only have one.
        let displayableOriginalLineNumber = (
            line.type !== RenderableLineType.updated ? line.originalLineNumber + 1 : ""
        ).toString();
        let displayableUpdatedLineNumber = (
            line.type !== RenderableLineType.original ? line.updatedLineNumber + 1 : ""
        ).toString();
        const plusminus =
            line.type === RenderableLineType.noop
                ? `<span class="codicon codicon-blank"></span>`
                : line.type === RenderableLineType.original
                  ? `<span class="codicon codicon-diff-remove"></span>`
                  : `<span class="codicon codicon-diff-insert"></span>`;

        result.push(
            `<span style="color:${colors.lineNumberColor};background-color:${backgroundColor};">` +
                `${leftPad(displayableOriginalLineNumber, lineNumberLength)}${leftPad(displayableUpdatedLineNumber, lineNumberLength)}` +
                `</span>` +
                `<span style="background-color:${backgroundColor};">${plusminus} </span>` +
                `${str}<span style="background-color:${backgroundColor};">${" ".repeat(linePadAmounts[i])}` +
                `</span>`
        );
    }
    if (truncationChar !== undefined) {
        result.push(
            `<span style="color:${colors.lineNumberColor};">${leftPad(" ", 2 * lineNumberLength + 1)}...(MORE CHANGES)...</span>`
        );
    }
    return {
        rendered: result.join("\n"),
        longestLineLength: longestLineLength,
    };
}

/**
 * Compute how many spaces we need after each line to make our line-highlights look like
 * they are actually full-line highlights
 * @param lines
 * @returns
 */
function computeRightPadding(
    lines: RenderableDiffLine[],
    minWidthInFixedWithChars: number,
    whitespaceInfo: WhitespaceInfo
): { linePadAmounts: number[]; longestLineLength: number } {
    const lineTexts = lines.map((s) => trimTrailingNewline(s.spans.map((s) => s.text).join("")));
    // TODO: This does not handle tabs well.
    const longestLineLength = Math.max(
        minWidthInFixedWithChars,
        lineTexts.map((s) => s.length).reduce((a, b) => Math.max(a, b), 0) - whitespaceInfo.total()
    );
    // +1 to length to for extra padding on right
    return {
        linePadAmounts: lineTexts.map(
            (s) => longestLineLength - Math.max(0, s.length - whitespaceInfo.total()) + 1
        ),
        longestLineLength,
    };
}

/** Render inline highlights on just the new text or just the old text. */
function renderInlineHighlights(
    diffSpans: RenderableSpan[],
    isOldText: boolean,
    colors: DiffColors,
    whitespaceInfo: WhitespaceInfo,
    logger?: AugmentLogger
): string {
    const htmlSpans: string[] = [];
    if (diffSpans.length === 0 || diffSpans.every((s) => s.text === "")) {
        return "";
    }

    // these spans are each known to be one line or less. They do contain the newline
    // char if they are at the end of the line (or the whole line)
    for (const span of diffSpans) {
        let style: string;
        if (span.type === ChangeType.noop) {
            // it's a noop, so we use the line color instead of text color
            if (isOldText) {
                style = `background-color:${colors.originalLineColor};`;
            } else {
                style = `background-color:${colors.updatedLineColor};`;
            }
        } else {
            // it's an addition or deletion. (we don't note "modifications" here like we do in the editor itself)
            if (isOldText) {
                style = `background-color:${colors.originalTextColor};`;
            } else {
                style = `background-color:${colors.updatedTextColor};`;
            }
        }

        // Since diffSpans is a series of spans on a single line, we trim off
        // as much as we can from each span. It's possible that the leading
        // whitespace is in more than one span (if say, whitespace is being
        // changed by the suggestion). So we trim from each span until we've
        // eaten up all the whitespace. We will never trim non-leading
        // whitespace because our initial counts are pre-computed.
        let str = span.text;
        const trimmed = whitespaceInfo.trimLeadingIncremental(span.text);
        if (!trimmed) {
            logger?.debug(`No common leading whitespace for span: ${span.text}`);
        } else {
            str = trimmed.trimmed;
            whitespaceInfo = trimmed.remaining;
        }

        let escText = escapeAndBreakMarkdownCodicons(str);
        const endswithNewline = escText.endsWith("\n");
        escText = trimTrailingNewline(escText);

        htmlSpans.push(`<span style="${style}">${escText}</span>${endswithNewline ? "\n" : ""}`);
    }
    if (!whitespaceInfo.isEmpty()) {
        logger?.debug(`Untrimmed whitespace`);
    }
    return htmlSpans.join("");
}

interface ToStringable {
    toString(): string;
}

function leftPad(arg: ToStringable, n: number): string {
    const str = arg.toString();
    if (n - utf32Length(str) >= 0) {
        return " ".repeat(n - str.length) + str;
    } else {
        return str;
    }
}

function rightPad(arg: ToStringable, n: number): string {
    const str = arg.toString();
    if (n - utf32Length(str) >= 0) {
        return str + " ".repeat(n - str.length);
    } else {
        return str;
    }
}

function trimTrailingNewline(str: string): string {
    if (str.endsWith("\n")) {
        return str.slice(0, -1);
    }
    return str;
}
