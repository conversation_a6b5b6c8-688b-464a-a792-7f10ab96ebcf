import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import {
    NextEditBackgroundDismissCommand,
    NextEditBackgroundGotoNextSmartCommand,
    NextEditLearnMoreCommand,
    NextEditOpenPanelCommand,
    NextEditUndoAcceptSuggestionCommand,
} from "../commands/next-edit";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { KeybindingWatcher } from "../utils/keybindings";
import { CompletionVisibilityWatcher } from "./completion-visibility-watcher";
import { NextEditConfigManager } from "./next-edit-config-manager";
import { NextEditSessionEventName, NextEditSessionEventSource } from "./next-edit-types";
import { EditSuggestion, SuggestionChangedEvent, SuggestionManager } from "./suggestion-manager";
import { getKeybindingForCommand, isFreshChange } from "./utils";

/**
 * A message that we can display to the user with an associated action.
 */
class TutorialMessage implements vscode.MessageItem {
    constructor(
        readonly title: string,
        readonly action: () => void
    ) {
        this.title = title;
        this.action = action;
    }
}

/**
 * A class that manages the next edit tutorials.
 *
 * As an optimization, we only add the listeners if the user has not seen the tutorial.
 * We could remove them after the tutorial is seen, but we'll just wait for a restart.
 */
export class NextEditTutorial extends DisposableService {
    constructor(
        private readonly _configListener: AugmentConfigListener,
        private readonly _suggestionManager: SuggestionManager,
        private readonly _keybindingWatcher: KeybindingWatcher,
        private readonly _globalState: AugmentGlobalState,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter,
        private readonly _nextEditConfigManager: NextEditConfigManager,
        private readonly _completionVisibilityWatcher: CompletionVisibilityWatcher
    ) {
        super();

        this._addInitialTutorial();
        this._addSecondTutorial();
    }

    /**
     * Shows a tutorial message the first time a user sees a suggestion.
     */
    private _addInitialTutorial() {
        // Note that this will use the config values at startup, not when we show the tutorial.
        // But it seems unlikely that users will change the settings before using the feature.
        // TODO: We can fix this if we think we care.
        const animateMode = this._nextEditConfigManager.config.enableAutoApply;
        const nextKeybinding = getKeybindingForCommand(
            this._keybindingWatcher,
            NextEditBackgroundGotoNextSmartCommand.commandID,
            true
        );
        const dismissKeybinding = getKeybindingForCommand(
            this._keybindingWatcher,
            NextEditBackgroundDismissCommand.commandID,
            true
        );
        this._addTutorial(
            GlobalContextKey.nextEditSuggestionSeen,
            (event) => event.newSuggestions,
            isFreshChange,
            "You have a Next Edit suggestion available. " +
                "Next Edit helps you complete your train of thought by suggesting " +
                "changes that continue your recent work.",
            [
                new TutorialMessage(
                    animateMode
                        ? `Preview & Apply (${nextKeybinding})`
                        : `Preview (${nextKeybinding})`,
                    execCommand(NextEditBackgroundGotoNextSmartCommand.commandID)
                ),
                new TutorialMessage(
                    `Dismiss All (${dismissKeybinding})`,
                    execCommand(NextEditBackgroundDismissCommand.commandID)
                ),
            ],
            NextEditSessionEventName.TutorialInitialShown
        );
    }

    /**
     * Shows a tutorial message the first time a user accepts a suggestion.
     */
    private _addSecondTutorial() {
        const undoKeybinding = getKeybindingForCommand(
            this._keybindingWatcher,
            NextEditUndoAcceptSuggestionCommand.commandID,
            true
        );
        const redoKeybinding = getKeybindingForCommand(this._keybindingWatcher, "redo", true);
        const panelKeybinding = getKeybindingForCommand(
            this._keybindingWatcher,
            NextEditOpenPanelCommand.commandID,
            true
        );

        const tutorialMessages = [
            new TutorialMessage("Learn More", execCommand(NextEditLearnMoreCommand.commandID)),
        ].concat(
            this._nextEditConfigManager.config.enablePanel
                ? [
                      new TutorialMessage(
                          `View All in Panel (${panelKeybinding})`,
                          execCommand(NextEditOpenPanelCommand.commandID)
                      ),
                  ]
                : []
        );

        this._addTutorial(
            GlobalContextKey.nextEditSuggestionAccepted,
            (event) => event.accepted,
            undefined,
            "You just applied a Next Edit suggestion! " +
                `Use Undo (${undoKeybinding}) and Redo (${redoKeybinding}) to go back and ` +
                "forth between the original and suggested code.",
            tutorialMessages,
            NextEditSessionEventName.TutorialAfterAcceptShown
        );
    }

    /**
     * A helper function for adding a tutorial message.
     * @param key The key that controls whether the user has seen this tutorial.
     * @param getSuggestions A function that returns the suggestions to consider.
     * @param filter An optional filter function to apply to the suggestions.
     * @param message The message to display to the user.
     * @param actions The buttons that the user can click.
     * @param eventName The name of the event to report when the tutorial is shown.
     */
    private _addTutorial(
        key: GlobalContextKey,
        getSuggestions: (event: SuggestionChangedEvent) => EditSuggestion[],
        filter: ((s: EditSuggestion) => boolean) | undefined,
        message: string,
        actions: TutorialMessage[],
        eventName: NextEditSessionEventName
    ) {
        if (this._globalState.get(key) === true) {
            return;
        }
        this.addDisposable(
            this._suggestionManager.onSuggestionsChanged(async (event) => {
                if (!this._shouldShowTutorial(key, getSuggestions(event), filter)) {
                    return;
                }
                if (this._completionVisibilityWatcher.maybeInlineCompletionVisible) {
                    return;
                }
                void this._globalState.update(key, true);
                this._nextEditSessionEventReporter.reportEventWithoutIds(
                    eventName,
                    NextEditSessionEventSource.Unknown
                );
                const response = await vscode.window.showInformationMessage(message, ...actions);
                response?.action();
                this._nextEditSessionEventReporter.reportEventWithoutIds(
                    response
                        ? NextEditSessionEventName.TutorialNonemptyResponse
                        : NextEditSessionEventName.TutorialEmptyResponse,
                    NextEditSessionEventSource.Unknown
                );
            })
        );
    }

    /**
     * A helper function for determining whether we should show a tutorial.
     * @param key The key that controls whether the user has seen this tutorial.
     * @param suggestions The suggestions to consider.
     * @param filter An optional filter function to apply to the suggestions.
     * @returns whether we should show the tutorial.
     */
    private _shouldShowTutorial(
        key: GlobalContextKey,
        suggestions: EditSuggestion[],
        filter: ((s: EditSuggestion) => boolean) | undefined = undefined
    ): boolean {
        // Apply the filter if one is provided.
        if (filter) {
            suggestions = suggestions.filter(filter);
        }
        // Don't show the tutorial if there are no relevant suggestions.
        // TODO: Should we ignore suggestions in other files?
        // Maybe only if baby global is disabled?
        if (suggestions.length === 0) {
            return false;
        }
        // Don't show the tutorial if the user has already seen it.
        if (this._globalState.get(key) === true) {
            return false;
        }
        return true;
    }
}

/* Returns a function that executes the given command. */
function execCommand(command: string) {
    return () => void vscode.commands.executeCommand(command, NextEditSessionEventSource.Tutorial);
}
