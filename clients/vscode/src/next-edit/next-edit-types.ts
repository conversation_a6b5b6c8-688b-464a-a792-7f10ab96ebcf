/**
 * Common next edit types shared/used by webviews.
 *
 * Avoid importing `vscode` here as they are incompatible with webviews.
 */
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

import { FeedbackRating } from "../types/feedback-rating";
import type { CharRange, ICharRange, ILineRange } from "../utils/ranges";

/* eslint-disable @typescript-eslint/naming-convention */
/** Mode used when generating next edit. */
export enum NextEditMode {
    Background = "BACKGROUND",
    Foreground = "FOREGROUND",
    Forced = "FORCED",
}

/** Scope used when generating next edit. */
export enum NextEditScope {
    Cursor = "CURSOR",
    File = "FILE",
    Workspace = "WORKSPACE",
}

/** Canonical names of a next edit user events that can be logged. */
export enum NextEditSessionEventName {
    Accept = "accept",
    AcceptAllInFile = "accept-all-in-file",
    AcceptAll = "accept-all",
    Reject = "reject",
    RejectAllInFile = "reject-all-in-file",
    RejectAll = "reject-all",
    UndoAccept = "undo-accept",
    UndoAllInFile = "undo-all-in-file",
    DismissAll = "dismiss-all",
    HoverShown = "hover-shown",
    /** The user has moused over a "baby global suggestion" hint. */
    GlobalHoverShown = "global-hover-shown", // TODO - we removed the usage of this event but may want to re-introduce it
    HoverDismissed = "hover-dismissed", // TODO - we removed the usage of this event but may want to re-introduce it
    /** The user triggered the "goto hinting" command from this suggestion. */
    GotoNextSmartTriggeredFrom = "goto-hinting-triggered-from", // keeping the old name for stats continuity
    /** The user triggered the "goto hinting" command to this suggestion. */
    GotoNextSmartTriggeredTo = "goto-hinting-triggered-to", // keeping the old name for stats continuity
    /** The user triggered the "next" command from this suggestion. */
    NextTriggeredFrom = "next-triggered-from",
    /** The user triggered the "next" command to this suggestion. */
    NextTriggeredTo = "next-triggered-to",
    /** The user triggered the "previous" command from this suggestion. */
    PreviousTriggeredFrom = "previous-triggered-from",
    /** The user triggered the "previous" command to this suggestion. */
    PreviousTriggeredTo = "previous-triggered-to",
    DiffViewTriggered = "diff-view-triggered",
    GlobalModeTriggered = "global-mode-triggered",
    GlobalModeRefreshed = "global-mode-refreshed",
    GlobalModeCanceled = "global-mode-canceled",
    GlobalModeDiffWindowOpened = "global-mode-diff-window-opened",
    GlobalModeDiffUpdated = "global-mode-diff-updated",
    GlobalModeSingleFileToggled = "global-mode-single-file-toggled",
    GlobalModeSelectedFilesChanged = "global-mode-selected-files-changed",
    GlobalModeAccept = "global-mode-accept",
    GlobalModeReject = "global-mode-reject",
    NonemptySuggestionAdded = "nonempty-suggestion-added",
    SuggestionForced = "suggestion-forced",
    BackgroundSuggestionsEnabled = "background-suggestions-enabled",
    BackgroundSuggestionsDisabled = "background-suggestions-disabled",
    HighlightsEnabled = "highlights-enabled",
    HighlightsDisabled = "highlights-disabled",
    /** Event when we create the next edit panel for the first time. */
    PanelCreated = "panel-created",
    /** Event when we open (or reopen) the next edit panel. */
    PanelOpened = "panel-opened",
    /** Event when we execute the command to focus the next edit panel. */
    PanelFocusExecuted = "panel-focus-executed",
    PanelSuggestionClicked = "panel-suggestion-clicked",
    NonemptySuggestionBecomesStale = "nonempty-suggestion-becomes-stale",
    NonemptySuggestionDropped = "nonempty-suggestion-dropped",
    NonemptySuggestionAccepted = "nonempty-suggestion-accepted",
    NonemptySuggestionInvalidated = "nonempty-suggestion-invalidated",
    NonemptySuggestionCleared = "nonempty-suggestion-cleared",
    NonemptySuggestionUndone = "nonempty-suggestion-undone",
    NonemptySuggestionRedone = "nonempty-suggestion-redone",
    /** Event when we show a hint in the grey text on the right of a suggestion. */
    SuggestionHintShown = "suggestion-hint-shown",
    /** Event when we show a hint in the grey text at the cursor for a suggestion in the same file. */
    SuggestionOffsetTextShown = "suggestion-offset-text-shown",
    /** Event when we show a hint in the grey text for a suggestion in another file. */
    SuggestionGlobalOffsetTextShown = "suggestion-global-offset-text-shown",
    /** Event a user triggers "accept and next" after seeing a baby global hint. */
    SuggestionGlobalOffsetTextTriggered = "suggestion-global-offset-text-triggered",
    /** Event when we visibly show a suggestion on the screen. */
    SuggestionVisiblyShown = "suggestion-visibly-shown",
    /** Event when we show a hint at the bottom of the screen for a suggestion in the same file. */
    SuggestionBottomTextShown = "suggestion-bottom-text-shown",
    /** Event when we show a hint at the bottom of the screen for a suggestion in another file. */
    SuggestionGlobalBottomTextShown = "suggestion-global-bottom-text-shown",
    ErrorGlobalModeError = "error-global-mode-error",
    ErrorHoverError = "error-hover-error",
    ErrorAPIError = "error-api-error",
    ErrorIntersectingSuggestions = "error-intersecting-suggestions",
    ErrorMovingToLineThatDoesntExist = "error-moving-to-line-that-doesnt-exist",
    ErrorResponseNotLineAligned = "error-response-not-line-aligned",
    ErrorResponseNotLineAlignedForCurrentFile = "error-response-not-line-aligned-for-current-file",
    ErrorCodeInBufferDoesntMatchCodeInResponse = "error-code-in-buffer-doesnt-match-code-in-response",
    ErrorCodeInBufferDoesntMatchCodeInResponseForCurrentFile = "error-code-in-buffer-doesnt-match-code-in-response-for-current-file",
    ErrorNoDocumentForResponse = "error-no-document-for-response",
    ErrorResponseFileIsDeleted = "error-response-file-is-deleted",
    ErrorNoSuggestionToAccept = "error-no-suggestion-to-accept",
    ErrorAcceptSuggestionWrongDocument = "error-accept-suggestion-wrong-document",
    ErrorNoSuggestionFound = "error-no-suggestion-found",
    NoopClicked = "noop-clicked",
    SuggestionTruncatedInHover = "suggestion-truncated-in-hover",
    /** Event when the cursor is inside a suggestion. */
    CursorInsideSuggestion = "cursor-inside-suggestion",
    ShowDiffInHover = "show-diff-in-hover",
    HideDiffInHover = "hide-diff-in-hover",
    PreviewDecorationDismissed = "preview-decoration-dismissed",
    ReverseDecorationDismissed = "reverse-decoration-dismissed",
    UndidAcceptedSuggestion = "undid-accepted-suggestion",
    StateTransitionedToNoSuggestions = "state-transitioned-to-no-suggestions",
    StateTransitionedToHinting = "state-transitioned-to-hinting",
    StateTransitionedToBeforePreview = "state-transitioned-to-before-preview",
    StateTransitionedToAfterPreview = "state-transitioned-to-after-preview",
    StateTransitionedToAnimating = "state-transitioned-to-animating",
    TutorialInitialShown = "tutorial-initial-shown",
    TutorialAfterAcceptShown = "tutorial-after-accept-shown",
    TutorialEmptyResponse = "tutorial-empty-response",
    TutorialNonemptyResponse = "tutorial-nonempty-response",
    CodeActionShown = "code-action-shown",
    SuggestionOpened = "suggestion-opened",
    TogglePanelHorizontalSplit = "toggle-panel-horizontal-split",
    LearnMoreClicked = "learn-more-clicked",
    /** Event when next-edit initialization succeeds. */
    InitializationSuccess = "initialization-success",
    /** Event when next-edit initialization fails. */
    InitializationFailure = "initialization-failure",
    /** Event when next-edit initialization is skipped. */
    InitializationSkip = "initialization-skip",
    /** When we dispose Next-Edit */
    Disposed = "disposed",
    /** Background decided to do nothing for text change event */
    BackgroundNoop = "background-noop",
}

/** Sources of a next edit user events that can be logged. */
export enum NextEditSessionEventSource {
    Unknown = "unknown",
    Command = "command",
    Background = "background",
    Global = "global",
    Click = "click",
    RightClick = "right-click",
    /** The user clicked on a button in the hover. */
    HoverClick = "hover-click",
    /** The user clicked on an editor action. */
    EditorActionClick = "editor-action-click",
    /** The user triggered a keybinding. */
    Keybinding = "keybinding",
    /** The user used the keyboard. */
    Keyboard = "keyboard",
    /** The active editor changed. */
    ActiveEditorChanged = "active-editor-changed",
    /** The selection in the editor changed. */
    EditorSelectionChanged = "editor-selection-changed",
    /** The visible ranges in the editor changed. */
    EditorVisibleRangesChanged = "editor-visible-ranges-changed",
    /** The user hovered outside of a suggestion. */
    HoveredOutsideSuggestion = "hovered-outside-suggestion",
    /** The document changed. */
    DocumentChanged = "document-changed",
    /** The user clicked on an item in the panel to focus in the editor.*/
    NextEditPanelItemFocusClick = "next-edit-panel-item-focus-click",
    /** The user clicked on an item in the next edit panel, to accept or reject. */
    NextEditPanelItemClick = "next-edit-panel-item-click",
    /** The user triggered a code action. */
    CodeAction = "code-action",
    /** The user clicked on a gutter icon. */
    GutterClick = "gutter-click",
    /** The user clicked on a code lens. */
    CodeLens = "code-lens",
    /** The user clicked on a button in the tutorial. */
    Tutorial = "tutorial",
    /** An error prevented us from accomplishing a goal */
    Error = "error",
    /** We explicitly decided to do or not do something and we consider it a valid case */
    ValidationExpected = "validation-expected",
    /** We explicitly deciedd to do or not do something and we consider it an unexpected case. Should be followed with an error report providing more context. */
    ValidationUnexpected = "validation-unexpected",
    /** The user is in a debugging session. */
    DebugSession = "debug-session",
    /** The user is in a notebook document. */
    NotebookDocument = "notebook-document",
    /** The user is in an output document. */
    UnsupportedUri = "unsupported-uri",
    /** document.uri could not be resolved to a path name. */
    MissingPathName = "missing-path-name",
    /** The document is not the active editor. */
    NotActiveEditor = "not-active-editor",
    /** The document has no content changes. */
    NoContentChanges = "no-content-changes",
    /** There are fresh suggestions available. */
    FreshSuggestions = "fresh-suggestions",
}
/* eslint-enable @typescript-eslint/naming-convention */

export type DiffSpan = {
    original: ICharRange;
    updated: ICharRange;
};

export type AnnotatedOneLineDiffSpanComponent = {
    text: string;
    charRange: CharRange;
    line: number;
    isWholeLine: boolean;
};

/**
 * These spans are at most one line long. They may or may not end in a newline.
 */
export type AnnotatedOneLineDiffSpan = {
    type: ChangeType;
    original: AnnotatedOneLineDiffSpanComponent;
    updated: AnnotatedOneLineDiffSpanComponent;
};

export type UUID = string;

/** Result generated by next edit. */
export interface NextEditResult {
    suggestionId: UUID;
    path: string;
    blobName: string;
    charStart: number;
    charEnd: number;
    existingCode: string;
    suggestedCode: string;
    truncationChar?: number;
    changeDescription: string;
    diffSpans: DiffSpan[];
    editingScore: number;
    localizationScore: number;
    editingScoreThreshold: number;
}

export class PathAndRange {
    constructor(
        public path: string,
        public range: ILineRange,
        public charStart?: number,
        public charStop?: number,
        public header?: string
    ) {}
}

export class BlockedPathAndRange extends PathAndRange {
    constructor(
        path: string,
        range: ILineRange,
        charStart: number,
        charStop: number,
        header?: string
    ) {
        super(path, range, charStart, charStop, header);
    }
}

/** Enumerates the types of acceptable changes. */
export enum ChangeType {
    insertion = "insertion",
    deletion = "deletion",
    modification = "modification",
    noop = "noop",
}

/** The state of a suggestion. */
export enum SuggestionState {
    /**
     * This suggestion is still fresh; if we were to make the request again,
     * we would expect the same result.
     */
    fresh = "fresh",
    /**
     * There have been some changes to the user's workspace; if we were to make the
     * request again, we would expect different results.
     */
    stale = "stale",
    /**
     * The user rejected this suggestion.
     */
    rejected = "rejected",
    /**
     * The user accepted this suggestion.
     */
    accepted = "accepted",
    // In addition to the above, we also have an invalid state: the suggestion is
    // no longer valid and can't safely be applied to the document.
}

export interface IEditSuggestion {
    // This class extends `NextEditResult` by:
    // (1) making its range fields mutable.
    // (2) adding a few extra vscode specific fields:
    //    - `range`: the (line, char) range of the suggestion.
    //    - `qualifiedPathName`: the qualified path name for multi-root support.
    // (3) adding requestId and scope metadata.
    /** The ID of the request that generated the suggestion. */
    requestId: UUID;
    /** Mode of the request when this suggestion was generated. */
    mode: NextEditMode;
    /** Scope of the request when this suggestion was generated. */
    scope: NextEditScope;
    /** The core suggestion result. */
    result: NextEditResult;
    /** QualifiedPathName for multi-workspace support. */
    qualifiedPathName: IQualifiedPathName;
    /** (line, character) range for the suggestion; updated on text events. */
    lineRange: ILineRange;
    /** The URI scheme of the document. */
    uriScheme: string;
    /** When the suggestion was generated. */
    occurredAt: Date;
    /** The state of the suggestion. */
    state: SuggestionState;
    /** The type of change this suggestion represents. */
    changeType: ChangeType;
}

export type NextEditFeedback = {
    requestId: string;
    rating: FeedbackRating;
    note: string;
};

export class NextEditResultInfo {
    public readonly occurredAt: Date = new Date();
    constructor(
        public readonly requestId: string,
        public readonly mode: NextEditMode,
        public readonly scope: NextEditScope,
        public readonly qualifiedPathName: IQualifiedPathName | undefined,
        public readonly apiResult: APIStatus,
        public readonly suggestions: IEditSuggestion[],
        public readonly requestTime: Date
    ) {}
}
