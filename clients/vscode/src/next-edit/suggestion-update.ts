/** Describes how suggestions are updated based on text edit events. */
import * as vscode from "vscode";

import { LineRange } from "../utils/ranges";
import { SuggestionState } from "./next-edit-types";
import { EditSuggestion } from "./suggestion-manager";

/** An empty class to indicate that a suggestion was left unchanged. */
export class Unchanged {
    constructor(readonly suggestion: EditSuggestion) {}
}

/** An empty class to indicate that a suggestion was updated. */
export class Updated {
    constructor(readonly suggestion: EditSuggestion) {}
}

/** An empty class to indicate that a suggestion was accepted. */
export class Accepted {
    constructor(readonly suggestion: EditSuggestion) {}
}

/** An empty class to indicate that a suggestion was invalidated. */
export class Invalidated {
    constructor(readonly suggestion: EditSuggestion) {}
}

/** An empty class to indicate that a suggestion was undone. */
export class Undone {
    constructor(readonly suggestion: EditSuggestion) {}
}

/** Describes how a suggestion can be updated. */
export type SuggestionUpdate = Unchanged | Updated | Accepted | Invalidated | Undone;

/**
 * Update the given suggestion after a text change event.
 *
 * It is assumed that the edit event happens in the same document as the suggestion.
 *
 * @param suggestion The suggestion to update.
 * @param event The edit event.
 * @returns A SuggestionUpdate that summarizes the suggestion was updated.
 */
export function updateSuggestionAfterChangeEvent(
    suggestion: EditSuggestion,
    event: vscode.TextDocumentChangeEvent
): SuggestionUpdate {
    // Ignore empty changes and changes in other files.
    if (
        event.contentChanges.length === 0 ||
        event.document.uri.fsPath !== suggestion.qualifiedPathName.absPath
    ) {
        return new Unchanged(suggestion);
    }
    // If a suggestion was accepted, we need to check if the events correspond to an
    // undo instead of the accept.
    const isAccepted = suggestion.state === SuggestionState.accepted;
    const document = event.document;
    // If we're undoing a deletion, the range should be a point range, just like for real insertions.
    // In this case the document HAS been updated to the undone state, so it would be incorrect to pass
    // it in to afterLineRange (there is some separate tech debt to perhaps remove `document` from `afterLineRange`)
    const suggestionRange = isAccepted ? suggestion.afterLineRange() : suggestion.lineRange;
    // The events that occur before the suggestion.
    // An important case to consider are insertions: they have 0 width so they
    // appear to happen before the suggestion, but actually intersect. See the
    // test `accepts exact matching insert`.
    const beforeEvents = event.contentChanges.filter(
        (change) =>
            // We only count changes that happen before the suggestion and don't
            // intersect. A change that applies the suggestion will intersect.
            change.range.end.line <= suggestionRange.start &&
            !_changeIntersectsSuggestion(change, suggestionRange)
    );
    const offsetLines = beforeEvents
        .map((change) => {
            const newLines = change.text.match(/\n/g)?.length ?? 0;
            const oldLines = change.range.end.line - change.range.start.line;
            return newLines - oldLines;
        })
        .reduce((acc, cur) => acc + cur, 0);
    // When this function is called, changes have already been applied to the
    // document, and the line range in the suggestion could be outdated. We have to
    // update the suggestion range by the number of lines added or removed. Note that
    // this is sufficient for the intersection check because our edits are line-based.
    const updatedLineRange = new LineRange(
        suggestion.lineRange.start + offsetLines,
        suggestion.lineRange.stop + offsetLines
    );
    // To validate whether or not the change event matches the suggestion, we need to
    // translate the character offsets in the change event to be relative to the
    // start of the suggestion. We do this by (1) finding the character offset of the
    // start of the suggestion in the document now and (2) subtracting the number of
    // characters added/removed before the suggestion.
    const offsetChars = beforeEvents
        .map((change) => {
            return change.text.length - change.rangeLength;
        })
        .reduce((acc, cur) => acc + cur, 0);
    const originalOffset =
        document.offsetAt(new vscode.Position(updatedLineRange.start, 0)) - offsetChars;
    const updatedSuggestion = suggestion.with({
        result: {
            ...suggestion.result,
            charStart: suggestion.result.charStart + offsetChars,
            charEnd: suggestion.result.charEnd + offsetChars,
        },
        lineRange: updatedLineRange,
    });

    // Get the intersecting changes.
    const intersectingChanges = event.contentChanges
        .filter((change) => _changeIntersectsSuggestion(change, suggestionRange))
        .map((change) => {
            // Update the ranges of the changes to be relative to this suggestion.
            return {
                rangeOffset: change.rangeOffset - originalOffset,
                rangeLength: change.rangeLength,
                text: change.text,
            };
        })
        .sort((a, b) => a.rangeOffset - b.rangeOffset);

    if (intersectingChanges.length === 0) {
        return offsetChars === 0
            ? new Unchanged(updatedSuggestion)
            : new Updated(updatedSuggestion);
    }

    // We have intersecting changes. We need to check that the changes are exactly
    // equal to the suggestion.

    // Tracks of delta character offset between old and new texts.
    let offsetDelta = 0;
    for (const change of intersectingChanges) {
        // Get the offset of the change relative to the suggestion.
        const code = isAccepted ? suggestion.result.existingCode : suggestion.result.suggestedCode;
        if (
            code.slice(
                offsetDelta + change.rangeOffset,
                offsetDelta + change.rangeOffset + change.text.length
            ) !== change.text
        ) {
            // The change is within the suggestion, but not equal to the suggestion!
            // We are going to invalidate this hunk.
            return new Invalidated(suggestion);
        }
        offsetDelta += change.text.length - change.rangeLength;
    }
    if (isAccepted) {
        offsetDelta *= -1;
    }
    // Finally, make sure the change covers the entire suggestion (esp. for deletions).
    if (
        offsetDelta !==
        suggestion.result.suggestedCode.length - suggestion.result.existingCode.length
    ) {
        return new Invalidated(suggestion);
    }
    // The changes are exactly equal to the suggestion. We can safely ignore this
    // suggestion.
    return isAccepted
        ? new Undone(updatedSuggestion.with({ state: SuggestionState.fresh }))
        : new Accepted(updatedSuggestion.with({ state: SuggestionState.accepted }));
}

function _lineRange(range: vscode.Range): LineRange {
    return new LineRange(
        range.start.line,
        // If the (exclusive) range end includes any characters in the line, include the
        // whole line.
        range.end.line + (range.end.character > 0 ? 1 : 0)
    );
}

/** Tests whether a change intersects a suggestion. */
function _changeIntersectsSuggestion(
    change: vscode.TextDocumentContentChangeEvent,
    updatedLineRange: LineRange
) {
    const changeRange = _lineRange(change.range);
    // There are two types of changes that need to be considered:
    // 1. intersecting changes of any type
    if (
        changeRange.intersects(updatedLineRange) ||
        (updatedLineRange.length === 0 && changeRange.touches(updatedLineRange))
    ) {
        return true;
    }
    // 2. insertions that start at the same line as the suggestion.
    const isInsertion = changeRange.length === 0;
    if (isInsertion && changeRange.start === updatedLineRange.start) {
        // When there's a co-starting insertion, we have two cases:
        // 1. The change inserts some characters at the start of the line: we
        //    always consider this to be an intersecting change as it modifies
        //    the text of the suggestion.
        // 2. The change inserts some _lines_ at the start: we only want to
        //    consider this change as intersecting if the suggestion is also
        //    inserting a new line at this range. If the new lines are being
        //    added at the top of the suggestion, we can just move it down.
        const withinLineChange = !change.text.endsWith("\n");
        return withinLineChange || changeRange.equals(updatedLineRange);
    }
    // Everything else is non-intersecting.
    return false;
}
