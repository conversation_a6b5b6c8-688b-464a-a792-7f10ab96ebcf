import { Observable } from "@augment-internal/sidecar-libs/src/utils/observable";
import { truncate } from "@augment-internal/sidecar-libs/src/utils/strings";
import assert from "assert";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { NextEditBackgroundGotoNextSmartCommand } from "../commands/next-edit";
import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { buildGhostDecoration } from "../utils/decoration-types";
import { KeybindingWatcher } from "../utils/keybindings";
import { getKeybindingDecor } from "../utils/keyboard/keyboard-decorations";
import { endOfLineRange, startOfLineRange } from "../utils/line-utils";
import { relativePathName } from "../utils/path-utils";
import { LineRange } from "../utils/ranges";
import { getSortedVisibleRanges, toVSCodeRange } from "../utils/ranges-vscode";
import { utf32Length } from "../utils/unicode";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { HintedSuggestion } from "./background-state";
import { DecorationCollection, DecorationManager } from "./decoration-manager";
import {
    ChangeType,
    NextEditSessionEventName,
    NextEditSessionEventSource,
    SuggestionState,
} from "./next-edit-types";
import { EditSuggestion } from "./suggestion-manager";
import { ZeroHeightDiffLineBuilder } from "./zero-height-diff-line-builder";

export type BackgroundDecorationManagerOptions = {
    /**
     * Indicates which suggestion is hinted / in focus.
     */
    hintSuggestion?: HintedSuggestion | undefined;
    /**
     * Indicates which suggestion is active.
     *
     * You will accept this suggestion if you execute 'AcceptAndNext'.
     */
    activeSuggestion?: EditSuggestion | undefined;
    /**
     * Indicates whether we are currently animating a suggestion.
     */
    isAnimating?: boolean | undefined;
};

enum NoDiffPreviewMode {
    active,
    accepted,
}

export class BackgroundDecorationManager extends DecorationManager<
    EditSuggestion,
    BackgroundDecorationManagerOptions
> {
    // NOTE(arun): When adding to the decoration types here, please make sure to add
    // to `createEmptyDecorations` as well.
    private _gutterDecorations: {
        active: {
            insertion: vscode.TextEditorDecorationType;
            insertionInBetween: vscode.TextEditorDecorationType;
            deletion: vscode.TextEditorDecorationType;
            modification: vscode.TextEditorDecorationType;
        };
        inactive: {
            insertion: vscode.TextEditorDecorationType;
            insertionInBetween: vscode.TextEditorDecorationType;
            deletion: vscode.TextEditorDecorationType;
            modification: vscode.TextEditorDecorationType;
        };
        applied: vscode.TextEditorDecorationType;
        appliedInBetween: vscode.TextEditorDecorationType;
        grayline: vscode.TextEditorDecorationType;
        grayhook: vscode.TextEditorDecorationType;
    };
    private _lineDecorations: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _wholeLineDecorations: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _focusLineDecorations: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _focusWholeLineDecorations: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _inlineDecorations: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _rightDecorationType: vscode.TextEditorDecorationType;
    private _bottomBoxLogoDecorationType: vscode.TextEditorDecorationType;
    private _rightKeybindingDecorationTypes: Array<vscode.TextEditorDecorationType>;
    private _rightSpacerDecorationType: vscode.TextEditorDecorationType;
    // Bottom decorations
    /** Decoration that blanks out the bottom lines so we can render stuff there. */
    private _bottomLineDecorationType: vscode.TextEditorDecorationType;
    /** Decoration that contains the keybindings and text hint with rounded corners. */
    private _bottomBoxDecorationType: vscode.TextEditorDecorationType;
    /** Decoration for the text we want to render. */
    private _bottomTextDecorationType: vscode.TextEditorDecorationType;

    private _zeroWidthChangeDecorationsRight: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _zeroWidthChangeDecorationsLeft: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _zeroWidthChangeDecorationsStartLine: Map<ChangeType, vscode.TextEditorDecorationType>;
    private _zeroWidthChangeDecorationsEndLine: Map<ChangeType, vscode.TextEditorDecorationType>;

    // No-diff mode box decorations
    private _activeChangeDecoration1Line: Map<NoDiffPreviewMode, vscode.TextEditorDecorationType>;
    private _activeChangeDecorationTop: Map<NoDiffPreviewMode, vscode.TextEditorDecorationType>;
    private _activeChangeDecorationMiddle: Map<NoDiffPreviewMode, vscode.TextEditorDecorationType>;
    private _activeChangeDecorationBottom: Map<NoDiffPreviewMode, vscode.TextEditorDecorationType>;

    private _zeroHeightDiffLineBuilder: ZeroHeightDiffLineBuilder;

    public shouldDrawBottomDecorations: Observable<boolean> = new Observable<boolean>(true);

    // TODO: If we decide to support only cursor/bottom, revisit this.
    /** The range of the baby global hint, if it is visible. */
    public babyGlobalHintRange: vscode.Range | undefined;

    /** The range of the bottom box, if it is visible. */
    public bottomBoxRange: vscode.Range | undefined;

    /** The range of the cursor hint, if it is visible. */
    public cursorHintRange: vscode.Range | undefined;

    // We want to ensure that we only show our right-click option for lines with gutter icons.
    // However, VSCode only seems to allow us to check independent conditions such as "the given
    // file has gutter icons" and "the given line has gutter icons".  There does not seem to be a
    // way to combine the two and check that the given line in the given file has gutter icons.
    // So we check for these two conditions.  This means that we can show the menu option for lines
    // that have gutter icons in a different file, but this is likely rare, and we will fail the
    // command and show a user-visible warning in such cases.
    /** Absolute filenames that have gutter icons. */
    private _filesWithGutterIconActions = new Set<string>();
    /** 1-based lines that have gutter icons. */
    private _linesWithGutterIconActions: Array<number> = [];

    private _logger = getLogger("BackgroundDecorationManager");

    private static readonly suppressGhostTextLineThreshold = 3;

    /** Maximum number of characters to show in the gray text. */
    private static readonly _maxGrayTextChars = 40;
    /** Maximum number of characters to show in the path in the bottom text. */
    private static readonly _maxBottomTextPathChars = 40;
    /** Maximum number of characters to show in the bottom text. */
    private static readonly _maxBottomTextChars = 80;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _keybindingWatcher: KeybindingWatcher,
        private readonly _configListener: AugmentConfigListener,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter,
        private readonly _completionVisible: () => boolean,
        private readonly _showAllLineHighlights: () => boolean
    ) {
        super();

        const gutterIcons = {
            active: {
                insertion: {
                    light: "nextedit-addition-selected-light.svg",
                    dark: "nextedit-addition-selected-dark.svg",
                },
                insertionInBetween: {
                    light: "nextedit-addition-inbetween-selected-light.svg",
                    dark: "nextedit-addition-inbetween-selected-dark.svg",
                },
                deletion: {
                    light: "nextedit-deletion-selected-light.svg",
                    dark: "nextedit-deletion-selected-dark.svg",
                },
                modification: {
                    light: "nextedit-change-selected-light.svg",
                    dark: "nextedit-change-selected-dark.svg",
                },
            },
            inactive: {
                insertion: {
                    light: "nextedit-addition-light.svg",
                    dark: "nextedit-addition-dark.svg",
                },
                insertionInBetween: {
                    light: "nextedit-addition-inbetween-light.svg",
                    dark: "nextedit-addition-inbetween-dark.svg",
                },
                deletion: {
                    light: "nextedit-deletion-light.svg",
                    dark: "nextedit-deletion-dark.svg",
                },
                modification: {
                    light: "nextedit-change-light.svg",
                    dark: "nextedit-change-dark.svg",
                },
            },
            applied: {
                light: "nextedit-applied-light.svg",
                dark: "nextedit-applied-dark.svg",
            },
            appliedInBetween: {
                light: "nextedit-applied-inbetween-light.svg",
                dark: "nextedit-applied-inbetween-dark.svg",
            },
            grayline: {
                light: "bg-next-edit-gray-line.svg",
                dark: "bg-next-edit-gray-line.svg",
            },
            grayhook: {
                light: "bg-next-edit-gray-hook.svg",
                dark: "bg-next-edit-gray-hook.svg",
            },
        };

        type ChangeTypeWithInBetween =
            | "insertion"
            | "insertionInBetween"
            | "deletion"
            | "modification";

        // Color types:
        // - inline: These are the colors we use to show a _span_ has been
        //  inserted, deleted or modified. They tend to be the strongest colors.
        //  These colors MUST agree with those used in the hover popup.
        // - focusLine: These are the colors we use for the whole suggestion
        //  when it is in "focus". It should be bolder than lineBackground.
        // - lineBackground: These are the colors we use for the whole suggestion
        //  when it is not in "focus". It should be the faintest colors.
        //
        // General principles:
        // - Avoid layering two colors over each other. Dealing with their
        //   composition is possible (color-mix!), but is the way of madness.
        const diffColors = new Map([
            [
                ChangeType.insertion,
                {
                    // note the greens in various themes seemed to need a little more pop compared to red and blue
                    inline: "rgb(from var(--vscode-diffEditor-insertedTextBackground) r g b / calc(alpha * 1.8))",
                    focusLine:
                        "rgb(from var(--vscode-diffEditor-insertedTextBackground) r g b / calc(alpha * 1.3))",
                    lineBackground: new vscode.ThemeColor("diffEditor.insertedLineBackground"),
                    lineOverview: new vscode.ThemeColor("editorOverviewRuler.addedForeground"),
                },
            ],
            [
                ChangeType.deletion,
                {
                    // We'll tone down the deleted lines a bit to make them less
                    // in your face.
                    inline: "rgb(from var(--vscode-diffEditor-removedTextBackground) r g b / calc(alpha * 2))",
                    focusLine: "var(--vscode-diffEditor-removedLineBackground)",
                    lineBackground:
                        "rgb(from var(--vscode-diffEditor-removedLineBackground) r g b / calc(alpha * 0.5))",
                    lineOverview: new vscode.ThemeColor("editorOverviewRuler.deletedForeground"),
                },
            ],
            [
                ChangeType.modification,
                {
                    // In light themes, the unmodified colors can occlude the text
                    // underneath.
                    inline: "rgb(from var(--vscode-editorOverviewRuler-modifiedForeground) r g b / calc(alpha * 0.5))",
                    focusLine:
                        "rgb(from var(--vscode-editorOverviewRuler-modifiedForeground) r g b / calc(alpha * 0.35))",
                    lineBackground:
                        "rgb(from var(--vscode-editorOverviewRuler-modifiedForeground) r g b / calc(alpha * 0.2))",
                    lineOverview: new vscode.ThemeColor("editorOverviewRuler.modifiedForeground"),
                },
            ],
        ]);

        const noDiffModeInlineColors = new Map([
            [
                ChangeType.insertion,
                {
                    dark: {
                        backgroundColor: "#27E14966",
                        color: "#BCF0C9",
                    },
                    light: {
                        backgroundColor: "#82CC0266",
                        color: "#4B543E",
                    },
                },
            ],
            [
                ChangeType.deletion,
                {
                    dark: {
                        backgroundColor: "#FF272766",
                        color: "#F4A2A6",
                    },
                    light: {
                        backgroundColor: "#FF272766",
                        color: "#412C2D",
                    },
                },
            ],
            [
                ChangeType.modification,
                {
                    dark: {
                        backgroundColor: "#FFE30966",
                        color: "#F4EEB6",
                    },
                    light: {
                        backgroundColor: "#EDBD0099",
                        color: "#433C1E",
                    },
                },
            ],
        ]);

        const bottomBoxColors = {
            light: {
                after: {
                    color: "color-mix(in srgb, rgb(0, 0, 0) 50%, var(--vscode-editor-background))",
                    backgroundColor:
                        "color-mix(in srgb, rgb(0,0,0) 15%, var(--vscode-editor-background))",
                },
            },

            dark: {
                after: {
                    color: "color-mix(in srgb, rgb(255, 255, 255) 90%, var(--vscode-editor-background))",
                    backgroundColor:
                        "color-mix(in srgb, rgb(255, 255, 255) 25%, var(--vscode-editor-background))",
                },
            },
        };

        const _getGutterIconDecorForChangeType = (
            style: "active" | "inactive",
            changeType: ChangeTypeWithInBetween,
            theme: "light" | "dark",
            size: string = "75%"
        ) => {
            const iconPath = gutterIcons[style][changeType][theme];
            return {
                gutterIconPath: vscode.Uri.joinPath(
                    _context.extensionUri,
                    "media",
                    "next-edit",
                    iconPath
                ),
                gutterIconSize: size,
            };
        };

        const _getGutterIconDecor = (
            style: "applied" | "appliedInBetween" | "grayline" | "grayhook",
            theme: "light" | "dark",
            size: string = "75%"
        ) => {
            const iconPath = gutterIcons[style][theme];
            return {
                gutterIconPath: vscode.Uri.joinPath(
                    _context.extensionUri,
                    "media",
                    "next-edit",
                    iconPath
                ),
                gutterIconSize: size,
            };
        };

        this._gutterDecorations = {
            active: {
                insertion: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType("active", "insertion", "light"),
                    dark: _getGutterIconDecorForChangeType("active", "insertion", "dark"),
                    overviewRulerColor: diffColors.get(ChangeType.insertion)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
                insertionInBetween: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType(
                        "active",
                        "insertionInBetween",
                        "light"
                    ),
                    dark: _getGutterIconDecorForChangeType("active", "insertionInBetween", "dark"),
                    overviewRulerColor: diffColors.get(ChangeType.insertion)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
                deletion: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType("active", "deletion", "light"),
                    dark: _getGutterIconDecorForChangeType("active", "deletion", "dark"),
                    overviewRulerColor: diffColors.get(ChangeType.deletion)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
                modification: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType("active", "modification", "light"),
                    dark: _getGutterIconDecorForChangeType("active", "modification", "dark"),
                    overviewRulerColor: diffColors.get(ChangeType.modification)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
            },
            inactive: {
                insertion: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType("inactive", "insertion", "light"),
                    dark: _getGutterIconDecorForChangeType("inactive", "insertion", "dark"),
                    overviewRulerColor: diffColors.get(ChangeType.insertion)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
                insertionInBetween: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType(
                        "inactive",
                        "insertionInBetween",
                        "light"
                    ),
                    dark: _getGutterIconDecorForChangeType(
                        "inactive",
                        "insertionInBetween",
                        "dark"
                    ),
                    overviewRulerColor: diffColors.get(ChangeType.insertion)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
                deletion: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType("inactive", "deletion", "light"),
                    dark: _getGutterIconDecorForChangeType("inactive", "deletion", "dark"),
                    overviewRulerColor: diffColors.get(ChangeType.deletion)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
                modification: vscode.window.createTextEditorDecorationType({
                    light: _getGutterIconDecorForChangeType("inactive", "modification", "light"),
                    dark: _getGutterIconDecorForChangeType("inactive", "modification", "dark"),
                    overviewRulerColor: diffColors.get(ChangeType.modification)?.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                }),
            },
            applied: vscode.window.createTextEditorDecorationType({
                light: _getGutterIconDecor("applied", "light"),
                dark: _getGutterIconDecor("applied", "dark"),
            }),
            appliedInBetween: vscode.window.createTextEditorDecorationType({
                light: _getGutterIconDecor("appliedInBetween", "light"),
                dark: _getGutterIconDecor("appliedInBetween", "dark"),
            }),
            grayline: vscode.window.createTextEditorDecorationType({
                light: _getGutterIconDecor("grayline", "light", "cover"),
                dark: _getGutterIconDecor("grayline", "dark", "cover"),
            }),
            grayhook: vscode.window.createTextEditorDecorationType({
                light: _getGutterIconDecor("grayhook", "light", "cover"),
                dark: _getGutterIconDecor("grayhook", "dark", "cover"),
            }),
        };
        this.addDisposables(
            ...Object.values(this._gutterDecorations.active),
            ...Object.values(this._gutterDecorations.inactive),
            this._gutterDecorations.applied,
            this._gutterDecorations.appliedInBetween,
            this._gutterDecorations.grayline,
            this._gutterDecorations.grayhook
        );

        this._focusLineDecorations = new Map<ChangeType, vscode.TextEditorDecorationType>();
        this._focusWholeLineDecorations = new Map<ChangeType, vscode.TextEditorDecorationType>();
        this._wholeLineDecorations = new Map<ChangeType, vscode.TextEditorDecorationType>();
        this._lineDecorations = new Map<ChangeType, vscode.TextEditorDecorationType>();
        this._zeroWidthChangeDecorationsRight = new Map<
            ChangeType,
            vscode.TextEditorDecorationType
        >();
        this._zeroWidthChangeDecorationsLeft = new Map<
            ChangeType,
            vscode.TextEditorDecorationType
        >();
        this._zeroWidthChangeDecorationsStartLine = new Map<
            ChangeType,
            vscode.TextEditorDecorationType
        >();
        this._zeroWidthChangeDecorationsEndLine = new Map<
            ChangeType,
            vscode.TextEditorDecorationType
        >();
        for (const [type, colors] of diffColors) {
            this._focusLineDecorations.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    backgroundColor: colors.focusLine,
                    isWholeLine: false,
                    overviewRulerColor: colors.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                })
            );
            this._focusWholeLineDecorations.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    backgroundColor: colors.focusLine,
                    isWholeLine: true,
                    overviewRulerColor: colors.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                })
            );
            this._lineDecorations.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    backgroundColor: colors.lineBackground,
                    isWholeLine: false,
                    overviewRulerColor: colors.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                })
            );
            this._wholeLineDecorations.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    backgroundColor: colors.lineBackground,
                    isWholeLine: true,
                    overviewRulerColor: colors.lineOverview,
                    overviewRulerLane: vscode.OverviewRulerLane.Right,
                })
            );
            const baseZeroWidthStyle = { borderStyle: "solid", borderColor: colors.inline };
            this._zeroWidthChangeDecorationsRight.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    borderWidth: "0 0.2em 0 0",
                    ...baseZeroWidthStyle,
                })
            );
            this._zeroWidthChangeDecorationsLeft.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    borderWidth: "0 0 0 0.2em",
                    ...baseZeroWidthStyle,
                })
            );
            this._zeroWidthChangeDecorationsStartLine.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    borderWidth: "0 0 0 0.4em",
                    ...baseZeroWidthStyle,
                })
            );
            this._zeroWidthChangeDecorationsEndLine.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    borderWidth: "0 0.4em 0 0",
                    ...baseZeroWidthStyle,
                })
            );
        }
        this.addDisposables(
            ...this._focusLineDecorations.values(),
            ...this._focusWholeLineDecorations.values(),
            ...this._lineDecorations.values(),
            ...this._wholeLineDecorations.values(),
            ...this._zeroWidthChangeDecorationsRight.values(),
            ...this._zeroWidthChangeDecorationsLeft.values(),
            ...this._zeroWidthChangeDecorationsStartLine.values(),
            ...this._zeroWidthChangeDecorationsEndLine.values()
        );

        // note the order matters here, inline decorations are on top of line decorations.
        // otherwise the line decoration color will tint the inline decoration.
        // (this does not seem to make sense because the line decorations are not actually
        // put onto the lines here, they are just put into a map ????)
        this._inlineDecorations = new Map<ChangeType, vscode.TextEditorDecorationType>();
        for (const [type, colors] of noDiffModeInlineColors) {
            this._inlineDecorations.set(
                type,
                vscode.window.createTextEditorDecorationType({
                    ...colors,
                    borderRadius: "0.2em",
                })
            );
        }
        this.addDisposables(...this._inlineDecorations.values());

        // Order matters here! This bottom line decorations must be drawn before the box
        // and keybindings.
        this._bottomLineDecorationType = vscode.window.createTextEditorDecorationType({
            after: {
                backgroundColor: "var(--vscode-editor-background)",
            },
        });
        this.addDisposables(this._bottomLineDecorationType);

        // Order matters here! This bottom box decorations must be drawn before the
        // and keybindings / text.
        this._bottomBoxDecorationType = vscode.window.createTextEditorDecorationType({
            light: {
                after: {
                    backgroundColor: bottomBoxColors.light.after.backgroundColor,
                },
            },
            dark: {
                after: {
                    backgroundColor: bottomBoxColors.dark.after.backgroundColor,
                },
            },
        });
        this.addDisposables(this._bottomBoxDecorationType);

        this._bottomBoxLogoDecorationType = vscode.window.createTextEditorDecorationType({
            light: {
                after: {
                    color: bottomBoxColors.light.after.color,
                },
            },
            dark: {
                after: {
                    // we subdue the logo in dark mode since the text is quite bright. we make it closer to the keybindings.
                    color: `color-mix(in srgb, ${bottomBoxColors.dark.after.color} 50%, ${bottomBoxColors.dark.after.backgroundColor})`,
                },
            },
        });
        this.addDisposables(this._bottomBoxLogoDecorationType);

        // order matters here too! The spacer comes first
        // followed by the keybinding decorations
        // followed by the description text
        [
            this._rightSpacerDecorationType,
            this._rightKeybindingDecorationTypes,
            this._rightDecorationType,
        ] = buildGhostDecoration();
        this.addDisposables(
            this._rightSpacerDecorationType,
            ...this._rightKeybindingDecorationTypes,
            this._rightDecorationType
        );

        // The bottom text decoration needs to be drawn after the keybindings
        this._bottomTextDecorationType = vscode.window.createTextEditorDecorationType({
            dark: {
                after: {
                    color: bottomBoxColors.dark.after.color,
                    backgroundColor: bottomBoxColors.dark.after.backgroundColor,
                },
            },
            light: {
                after: {
                    color: bottomBoxColors.light.after.color,
                    backgroundColor: bottomBoxColors.light.after.backgroundColor,
                },
            },
        });
        this.addDisposables(this._bottomTextDecorationType);

        const baseNextSuggestionBorderDecor = {
            light: {
                borderColor: "#87BFFF80",
                backgroundColor: "#87BFFF33",
                overviewRulerColor: "#87BFFF",
            },
            dark: {
                borderColor: "#9ECBFF80",
                backgroundColor: "#9ECBFF1A",
                overviewRulerColor: "#9ECBFF",
            },
            borderStyle: "solid",
            isWholeLine: true,
            borderWidth: "0.1em",
            borderRadius: "0.4em",
            overviewRulerLane: vscode.OverviewRulerLane.Right,
        };
        const baseActiveSuggestionBorderDecor = {
            light: {
                borderColor: "#87BFFF",
                backgroundColor: "#87BFFF33",
                overviewRulerColor: "#87BFFF",
            },
            dark: {
                borderColor: "#9ECBFF",
                backgroundColor: "#9ECBFF1A",
                overviewRulerColor: "#9ECBFF",
            },
            borderStyle: "solid",
            isWholeLine: true,
            borderWidth: "0.2em",
            borderRadius: "0.4em",
            overviewRulerLane: vscode.OverviewRulerLane.Right,
        };
        this._zeroHeightDiffLineBuilder = new ZeroHeightDiffLineBuilder({
            insertion: {
                // we only have insertions like this when we are in the "before" mode
                lineColor: "rgb(from var(--vscode-diffEditor-insertedLineBackground) r g b / 1)",
                lightBorderColor: baseNextSuggestionBorderDecor.light.borderColor,
                darkBorderColor: baseNextSuggestionBorderDecor.dark.borderColor,
                lightBackgroundColor:
                    "color-mix(in srgb, #87BFFF 33%, var(--vscode-editor-background))",
                darkBackgroundColor:
                    "color-mix(in srgb, #9ECBFF 10%, var(--vscode-editor-background))",
                borderWidth: "0.3em",
                overviewRulerColor: diffColors.get(ChangeType.insertion)?.lineOverview,
            },
            deletion: {
                // we only have deletions like this when we are in the "after" mode
                lineColor:
                    "rgb(from var(--vscode-diffEditor-removedLineBackground) r g b / calc(alpha * 1.3))",
                lightBorderColor: baseActiveSuggestionBorderDecor.light.borderColor,
                darkBorderColor: baseActiveSuggestionBorderDecor.dark.borderColor,
                lightBackgroundColor:
                    "color-mix(in srgb, #87BFFF 33%, var(--vscode-editor-background))",
                darkBackgroundColor:
                    "color-mix(in srgb, #9ECBFF 10%, var(--vscode-editor-background))",
                borderWidth: "0.4em",
                overviewRulerColor: diffColors.get(ChangeType.deletion)?.lineOverview,
            },
        });
        this.addDisposables(this._zeroHeightDiffLineBuilder);

        this._activeChangeDecoration1Line = new Map<
            NoDiffPreviewMode,
            vscode.TextEditorDecorationType
        >();
        this._activeChangeDecorationTop = new Map<
            NoDiffPreviewMode,
            vscode.TextEditorDecorationType
        >();
        this._activeChangeDecorationMiddle = new Map<
            NoDiffPreviewMode,
            vscode.TextEditorDecorationType
        >();
        this._activeChangeDecorationBottom = new Map<
            NoDiffPreviewMode,
            vscode.TextEditorDecorationType
        >();
        for (const { baseType, mode } of [
            { baseType: baseNextSuggestionBorderDecor, mode: NoDiffPreviewMode.active },
            { baseType: baseActiveSuggestionBorderDecor, mode: NoDiffPreviewMode.accepted },
        ]) {
            this._activeChangeDecoration1Line.set(
                mode,
                vscode.window.createTextEditorDecorationType({
                    ...baseType,
                })
            );
            this._activeChangeDecorationTop.set(
                mode,
                vscode.window.createTextEditorDecorationType({
                    ...baseType,
                    borderWidth: [
                        baseType.borderWidth,
                        baseType.borderWidth,
                        "0",
                        baseType.borderWidth,
                    ].join(" "),
                    borderRadius: [baseType.borderRadius, baseType.borderRadius, "0", "0"].join(
                        " "
                    ),
                })
            );
            this._activeChangeDecorationMiddle.set(
                mode,
                vscode.window.createTextEditorDecorationType({
                    ...baseType,
                    borderRadius: undefined,
                    borderWidth: ["0", baseType.borderWidth, "0", baseType.borderWidth].join(" "),
                })
            );
            this._activeChangeDecorationBottom.set(
                mode,
                vscode.window.createTextEditorDecorationType({
                    ...baseType,
                    borderWidth: [
                        "0",
                        baseType.borderWidth,
                        baseType.borderWidth,
                        baseType.borderWidth,
                    ].join(" "),
                    borderRadius: ["0", "0", baseType.borderRadius, baseType.borderRadius].join(
                        " "
                    ),
                })
            );
        }
        this.addDisposables(
            ...this._activeChangeDecoration1Line.values(),
            ...this._activeChangeDecorationTop.values(),
            ...this._activeChangeDecorationMiddle.values(),
            ...this._activeChangeDecorationBottom.values()
        );
    }

    protected createEmptyDecorations(): DecorationCollection {
        const decorations: DecorationCollection = new Map();
        decorations.set(this._rightSpacerDecorationType, []);
        decorations.set(this._rightDecorationType, []);
        this._rightKeybindingDecorationTypes.forEach((decoration) => {
            decorations.set(decoration, []);
        });
        decorations.set(this._bottomLineDecorationType, []);
        decorations.set(this._bottomBoxDecorationType, []);
        decorations.set(this._bottomBoxLogoDecorationType, []);
        decorations.set(this._bottomTextDecorationType, []);
        this._zeroHeightDiffLineBuilder.addEmptyDecorations(decorations);
        this._activeChangeDecoration1Line.forEach((decoration) => {
            decorations.set(decoration, []);
        });
        this._activeChangeDecorationTop.forEach((decoration) => {
            decorations.set(decoration, []);
        });
        this._activeChangeDecorationMiddle.forEach((decoration) => {
            decorations.set(decoration, []);
        });
        this._activeChangeDecorationBottom.forEach((decoration) => {
            decorations.set(decoration, []);
        });
        Object.values(this._gutterDecorations.active).forEach((decoration) => {
            decorations.set(decoration, []);
        });
        Object.values(this._gutterDecorations.inactive).forEach((decoration) => {
            decorations.set(decoration, []);
        });
        decorations.set(this._gutterDecorations.applied, []);
        decorations.set(this._gutterDecorations.appliedInBetween, []);
        decorations.set(this._gutterDecorations.grayline, []);
        decorations.set(this._gutterDecorations.grayhook, []);

        const types = [ChangeType.insertion, ChangeType.deletion, ChangeType.modification];
        for (const suggestionType of types) {
            decorations.set(this._lineDecorations.get(suggestionType)!, []);
            decorations.set(this._focusLineDecorations.get(suggestionType)!, []);
            decorations.set(this._wholeLineDecorations.get(suggestionType)!, []);
            decorations.set(this._focusWholeLineDecorations.get(suggestionType)!, []);
            decorations.set(this._inlineDecorations.get(suggestionType)!, []);
            decorations.set(this._zeroWidthChangeDecorationsRight.get(suggestionType)!, []);
            decorations.set(this._zeroWidthChangeDecorationsLeft.get(suggestionType)!, []);
            decorations.set(this._zeroWidthChangeDecorationsStartLine.get(suggestionType)!, []);
            decorations.set(this._zeroWidthChangeDecorationsEndLine.get(suggestionType)!, []);
        }
        return decorations;
    }

    public decorate(items: EditSuggestion[], extraArgs: BackgroundDecorationManagerOptions) {
        this._filesWithGutterIconActions.clear();
        this._linesWithGutterIconActions = [];
        super.decorate(items, extraArgs);
        void vscode.commands.executeCommand(
            "setContext",
            "vscode-augment.nextEdit.linesWithGutterIconActions",
            this._linesWithGutterIconActions
        );
        void vscode.commands.executeCommand(
            "setContext",
            "vscode-augment.nextEdit.filesWithGutterIconActions",
            Array.from(this._filesWithGutterIconActions)
        );
    }

    protected addDecorationsForItem(
        decorations: DecorationCollection,
        editor: vscode.TextEditor,
        suggestion: EditSuggestion,
        options: BackgroundDecorationManagerOptions
    ): DecorationCollection {
        // Don't do anything for suggestions in other files.
        // We draw cursor-line decorations for these in
        // `addExtraDecorationsForActiveEditor`.
        if (
            !suggestion.qualifiedPathName.equals(editor.document.uri) ||
            suggestion.changeType === ChangeType.noop
        ) {
            return decorations;
        }

        const isActive = suggestion.equals(options.activeSuggestion);
        const isHint = isActive || suggestion.equals(options.hintSuggestion?.suggestion);
        const isAccepted = suggestion.state === SuggestionState.accepted;

        const annotatedSpans = suggestion.makeOneLineDiffSpans();

        const suggestionLineRangeNow = isAccepted
            ? suggestion.afterLineRange(editor.document)
            : suggestion.lineRange;
        // If this change is an insertion, or the very first span is whole line insertion,
        // then we back up one line
        let suggestionLineRange;
        if (
            suggestionLineRangeNow.start === 0 ||
            (suggestion.changeType !== ChangeType.insertion &&
                !(
                    annotatedSpans[0] &&
                    annotatedSpans[0].type === ChangeType.insertion &&
                    annotatedSpans[0].updated.isWholeLine
                ))
        ) {
            suggestionLineRange = suggestionLineRangeNow;
        } else if (editor.document.lineAt(suggestionLineRangeNow.start - 1).text.length > 0) {
            // Show the line highlight on the whole previous line for insertions.
            suggestionLineRange = new LineRange(
                suggestionLineRangeNow.start - 1,
                suggestionLineRangeNow.stop
            );
        } else {
            suggestionLineRange = new LineRange(
                suggestionLineRangeNow.start - 1,
                suggestionLineRangeNow.stop - 1
            );
        }

        // 1. Add gutter decorations for the first line of the suggestion range.
        // line parameter is 0-based
        const addLineWithGutterIcon = (line: number) => {
            if (!isAccepted) {
                this._filesWithGutterIconActions.add(suggestion.qualifiedPathName.absPath);
                // we add 1 because the usages of this data is 1-based, not 0-based
                this._linesWithGutterIconActions.push(line + 1);
            }
        };

        const changeType =
            suggestion.changeType === ChangeType.insertion && suggestion.lineRange.length === 0
                ? "insertionInBetween"
                : suggestion.changeType;

        // Are we marking some other suggestion as active?
        const existsActive =
            options.activeSuggestion !== undefined &&
            options.activeSuggestion.state !== SuggestionState.accepted;
        let iconDecoration;
        if (isAccepted && suggestionLineRangeNow.length === 0) {
            iconDecoration = this._gutterDecorations.appliedInBetween;
        } else if (isAccepted) {
            iconDecoration = this._gutterDecorations.applied;
            // Don't decorate the next hinted suggestion while we're animating.
        } else if (isActive || (!existsActive && isHint)) {
            iconDecoration = this._gutterDecorations.active[changeType];
        } else {
            iconDecoration = this._gutterDecorations.inactive[changeType];
        }

        const gutterIconRange = suggestion.previewBoxRange(editor.document);

        decorations.get(iconDecoration)?.push(startOfLineRange(gutterIconRange.start));
        addLineWithGutterIcon(gutterIconRange.start);

        // if there are more lines then add gray lines for the rest of the lines.
        for (let i = gutterIconRange.start + 1; i < gutterIconRange.stop - 1; i++) {
            decorations.get(this._gutterDecorations.grayline)?.push(startOfLineRange(i));
            addLineWithGutterIcon(i);
        }
        // and add a gray hook for the end.
        if (gutterIconRange.stop - gutterIconRange.start > 1) {
            decorations
                .get(this._gutterDecorations.grayhook)
                ?.push(startOfLineRange(gutterIconRange.stop - 1));
            addLineWithGutterIcon(gutterIconRange.stop - 1);
        }

        // If we're in the middle of an animation, only show decorations for the active suggestion.
        if (options.isAnimating && options.activeSuggestion && !isActive) {
            return decorations;
        }

        // If we are hint, but a completion is visible, then let the completion take
        // priority (but we still show the gutter icon)
        if (isHint && this._completionVisible()) {
            return decorations;
        }
        // this means "I want to highlight no-op spans and/or entire lines"
        const showNoDiffHighlights = isActive;
        const showFullLineHighlights =
            this._showAllLineHighlights() && !showNoDiffHighlights && !isAccepted;
        // 2. Add line decorations for the suggestion, in the current editor.
        const lineHighlightRange = toVSCodeRange(suggestionLineRange);
        if (showFullLineHighlights && !isActive) {
            // When the suggestion is hint but not active, only show line decorations.
            if (suggestion.changeType === ChangeType.insertion) {
                this._zeroHeightDiffLineBuilder.addSimpleInsertionDecoration(
                    lineHighlightRange.start.line + 1,
                    decorations
                );
            } else {
                // For insertions on empty lines, we have to use whole line decorations
                // for them to be visible.
                const isEmptyLine =
                    suggestionLineRange.length === 0 || suggestion.result.existingCode === "\n";
                const decoration = [
                    [this._lineDecorations, this._wholeLineDecorations], // other suggestions
                    [this._focusLineDecorations, this._focusWholeLineDecorations], // hint suggestion
                ][+isHint][+isEmptyLine];
                let lineRange = lineHighlightRange;
                if (isEmptyLine) {
                    lineRange = lineHighlightRange.with(
                        undefined,
                        lineHighlightRange.end.translate(-1, 0)
                    );
                }
                decorations.get(decoration.get(suggestion.changeType)!)?.push(lineRange);
            }
        } else if (showNoDiffHighlights) {
            // When the suggestion is active, show more detailed span decorations.
            if (
                (suggestion.changeType === ChangeType.insertion && !isAccepted) ||
                (suggestion.changeType === ChangeType.deletion && isAccepted)
            ) {
                this._zeroHeightDiffLineBuilder.addDecorations(
                    suggestion.changeType,
                    lineHighlightRange.start.line +
                        (suggestion.changeType === ChangeType.insertion ? 1 : 0),
                    decorations
                );
            } else if (
                suggestion.changeType === (isAccepted ? ChangeType.deletion : ChangeType.insertion)
            ) {
                // Only show the line highlight for insertions.
                const decoration =
                    suggestionLineRange.length === 0
                        ? this._focusWholeLineDecorations
                        : this._focusLineDecorations;
                decorations.get(decoration.get(suggestion.changeType)!)?.push(lineHighlightRange);
            }
            // 3. Add detailed inline decorations
            for (const [spanType, spanRange] of _getLabeledSpans(
                editor.document,
                suggestion,
                !isAccepted
            )) {
                if (spanType === ChangeType.noop) {
                    if (showFullLineHighlights) {
                        // Show the line decorations for no-op spans so that it "looks"
                        // like the background, but we don't need to layer colors.
                        decorations
                            .get(this._focusLineDecorations.get(suggestion.changeType)!)
                            ?.push(spanRange);
                    }
                } else if (
                    spanType === ChangeType.deletion &&
                    spanRange.start.character === 0 &&
                    spanRange.end.character === 0
                ) {
                    // For deleted lines show the line decorations instead; the
                    // span decorations are too bright.
                    decorations.get(this._focusLineDecorations.get(spanType)!)?.push(spanRange);
                    continue;
                } else {
                    if (spanRange.start.isEqual(spanRange.end)) {
                        this._addZeroWidthDecoration(
                            editor.document,
                            decorations,
                            spanRange,
                            spanType
                        );
                    } else {
                        decorations.get(this._inlineDecorations.get(spanType)!)!.push(spanRange);
                    }
                }
            }
            const mode = isAccepted ? NoDiffPreviewMode.accepted : NoDiffPreviewMode.active;
            this._addPreviewBox(decorations, editor, suggestion, mode);
        }
        // 4. Add right-side decorations only to the focus suggestion.
        //    ("cursor line" decorations are drawn in `addExtraDecorationsForActiveEditor`).
        const suggestionDescription = suggestion.result.changeDescription;
        const cursor = editor.selection;
        // There can be multiple visible ranges, e.g., if some code is folded in the editor.
        // The visible ranges are then the lines that are visible (e.g., aren't folded).
        const suggestionIsVisible = editor.visibleRanges.some((r) =>
            r.contains(toVSCodeRange(suggestionLineRange))
        );

        const useCursorDecorations = this._configListener.config.nextEdit.useCursorDecorations;
        const offsetLines = cursor.active.line - suggestionLineRange.start;
        // If we're showing bottom decorations, always show the keybindings if the
        // suggestion is on screen.
        // If we're not, don't show keybindings if we are showing the cursor ghost text.
        //   We show the cursor ghost text as long as we are not too close to the
        //   current suggestion or if the current suggestion is off screen.
        //   The visibleRanges functionality of vscode is a little inaccurate so this
        //   isn't perfect, but it's pretty good.
        const shouldShowKeybindings =
            !useCursorDecorations ||
            Math.abs(offsetLines) <= BackgroundDecorationManager.suppressGhostTextLineThreshold;
        // Only show grey text for the hint suggestion if it's visible but not active.
        if (isHint && suggestionIsVisible && !isAccepted && !isActive) {
            if (shouldShowKeybindings) {
                this.addKeybindingDecor(
                    suggestionLineRange.start,
                    decorations,
                    editor,
                    NextEditBackgroundGotoNextSmartCommand.commandID
                );
            }
            this.addGrayText(
                `${suggestionDescription}`,
                suggestionLineRange.start,
                decorations,
                editor
            );
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.SuggestionHintShown,
                NextEditSessionEventSource.Background
            );
        }
        if (suggestionIsVisible && !isAccepted) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.SuggestionVisiblyShown,
                NextEditSessionEventSource.Background
            );
        }

        return decorations;
    }
    /**
     * This is a bit weird - you would think you could just put a border on a zero-width
     * range and get equal borders on both sides but it doesn't seem to work that way.
     * Instead you seem to get a border on the right side only, regardless of whether you
     * specify it as left or right. Instead we use 2 ranges, one for each char on either side.
     * We then give the left one a right border and the right one a left border. We can't do
     * this at the line edges, so we just give thoe a double border.
     * @param document
     * @param decorations
     * @param spanRange assumed to have zero width - start and end are same.
     * @param spanType
     */
    private _addZeroWidthDecoration(
        document: vscode.TextDocument,
        decorations: DecorationCollection,
        spanRange: vscode.Range,
        spanType: ChangeType
    ) {
        if (spanRange.start.character === 0) {
            // for the start of the line, we only decorate the first character with a left border
            // that is double width
            decorations
                .get(this._zeroWidthChangeDecorationsStartLine.get(spanType)!)!
                .push(new vscode.Range(spanRange.start, spanRange.start.translate(0, 1)));
            return;
        } else if (
            spanRange.start.character === document.lineAt(spanRange.start.line).text.length
        ) {
            // for the end of the line, we only decorate the last character with a right border
            // that is double width
            decorations
                .get(this._zeroWidthChangeDecorationsEndLine.get(spanType)!)!
                .push(new vscode.Range(spanRange.start.translate(0, -1), spanRange.start));
            return;
        } else {
            decorations
                .get(this._zeroWidthChangeDecorationsLeft.get(spanType)!)!
                .push(new vscode.Range(spanRange.start, spanRange.start.translate(0, 1)));
            decorations
                .get(this._zeroWidthChangeDecorationsRight.get(spanType)!)!
                .push(new vscode.Range(spanRange.start.translate(0, -1), spanRange.start));
        }
    }

    private addKeybindingDecor(
        line: number,
        decorations: DecorationCollection,
        activeEditor: vscode.TextEditor,
        commandId: string = NextEditBackgroundGotoNextSmartCommand.commandID
    ) {
        getKeybindingDecor(commandId, this._keybindingWatcher, this._context).map(
            (decor, index) => {
                decorations.get(this._rightKeybindingDecorationTypes[index])?.push({
                    range: endOfLineRange(activeEditor, line),
                    renderOptions: decor,
                });
            }
        );
    }

    private addGrayText(
        text: string,
        line: number,
        decorations: DecorationCollection,
        activeEditor: vscode.TextEditor
    ) {
        decorations.get(this._rightSpacerDecorationType)!.push({
            range: endOfLineRange(activeEditor, line),
        });
        const truncatedText = truncate(text, BackgroundDecorationManager._maxGrayTextChars);
        decorations.get(this._rightDecorationType)!.push({
            range: endOfLineRange(activeEditor, line),
            renderOptions: {
                after: {
                    contentText: truncatedText,
                },
            },
        });
    }

    protected addExtraDecorationsForActiveEditor(
        decorations: DecorationCollection,
        activeEditor: vscode.TextEditor,
        _items: EditSuggestion[],
        options: BackgroundDecorationManagerOptions
    ) {
        assert(activeEditor === vscode.window.activeTextEditor);
        const currentPathName = this._workspaceManager.safeResolvePathName(
            activeEditor.document.uri
        );
        // Ignore drawing decorations when we can't resolve a path name for it.
        // This covers e.g. output terminals, etc.
        if (!currentPathName) {
            return decorations;
        }

        this.babyGlobalHintRange = undefined;
        this.bottomBoxRange = undefined;
        this.cursorHintRange = undefined;
        // Only show decorations for the hint suggestion, if one exists and there is no
        // completion visible.
        const hintSuggestion = options.hintSuggestion?.suggestion;
        if (
            this._completionVisible() ||
            !hintSuggestion ||
            activeEditor.visibleRanges.length === 0
        ) {
            return decorations;
        }
        const useCursorDecorations = this._configListener.config.nextEdit.useCursorDecorations;

        if (hintSuggestion.qualifiedPathName.equals(activeEditor.document.uri)) {
            // If the hint suggestion is in the current file, then show how far away it
            // is.
            const suggestionDescription = hintSuggestion.result.changeDescription;
            const cursor = activeEditor.selection;

            let suggestionLineRange;
            if (
                hintSuggestion.changeType !== ChangeType.insertion ||
                hintSuggestion.lineRange.start === 0
            ) {
                suggestionLineRange = hintSuggestion.lineRange;
            } else if (
                activeEditor.document.lineAt(hintSuggestion.lineRange.start - 1).text.length > 0
            ) {
                // Show the line highlight on the whole previous line for insertions.
                suggestionLineRange = new LineRange(
                    hintSuggestion.lineRange.start - 1,
                    hintSuggestion.lineRange.stop
                );
            } else {
                suggestionLineRange = new LineRange(
                    hintSuggestion.lineRange.start - 1,
                    hintSuggestion.lineRange.stop - 1
                );
            }

            if (useCursorDecorations) {
                const inSuggestion = cursor.intersection(toVSCodeRange(suggestionLineRange));
                const offsetLines = cursor.start.line - suggestionLineRange.start;

                // we show the cursor ghost text as long as we are not too close to the current suggestion
                // or if the current suggestion is off screen. The visibleRanges functionality of vscode
                // is a little inaccurate so this isn't perfect, but it's pretty good.
                const shouldShowCursorGhostText =
                    Math.abs(offsetLines) >
                        BackgroundDecorationManager.suppressGhostTextLineThreshold ||
                    !activeEditor.visibleRanges.some((r) =>
                        r.contains(toVSCodeRange(suggestionLineRange))
                    );
                let offsetText = "";
                if (!inSuggestion && shouldShowCursorGhostText) {
                    const offsetMarker = offsetLines > 0 ? "↑" : "↓";
                    offsetText = `${offsetMarker}${Math.abs(offsetLines)} lines: `;
                }
                if (offsetText || inSuggestion) {
                    this.addKeybindingDecor(
                        cursor.start.line,
                        decorations,
                        activeEditor,
                        NextEditBackgroundGotoNextSmartCommand.commandID
                    );
                    this.addGrayText(
                        `${offsetText}${suggestionDescription}`,
                        cursor.start.line,
                        decorations,
                        activeEditor
                    );
                    this.cursorHintRange = endOfLineRange(activeEditor, cursor.start.line);
                    this._nextEditSessionEventReporter.reportEventFromSuggestion(
                        hintSuggestion,
                        NextEditSessionEventName.SuggestionOffsetTextShown,
                        NextEditSessionEventSource.Background
                    );
                }
            } else {
                // Only draw bottom decorations if no suggestion is currently visible.
                if (
                    activeEditor.visibleRanges.some((r) =>
                        r.contains(toVSCodeRange(hintSuggestion.highlightRange))
                    )
                ) {
                    return decorations;
                }
                const offsetMarker =
                    suggestionLineRange.start < getSortedVisibleRanges(activeEditor)[0].start.line
                        ? "↑"
                        : "↓";
                this.addBottomDecoration(
                    decorations,
                    activeEditor,
                    `${offsetMarker} ${suggestionDescription}`
                );
                this._nextEditSessionEventReporter.reportEventFromSuggestion(
                    hintSuggestion,
                    NextEditSessionEventName.SuggestionBottomTextShown,
                    NextEditSessionEventSource.Background
                );
            }
        } else if (this._configListener.config.nextEdit.enableGlobalBackgroundSuggestions) {
            // Otherwise, just show the file name.
            if (useCursorDecorations) {
                this.addKeybindingDecor(
                    activeEditor.selection.active.line,
                    decorations,
                    activeEditor
                );
                const prefix = "in file: ";
                this.addGrayText(
                    `${prefix}${truncate(hintSuggestion.qualifiedPathName.relPath, BackgroundDecorationManager._maxGrayTextChars - utf32Length(prefix), true)}`,
                    activeEditor.selection.active.line,
                    decorations,
                    activeEditor
                );
                this.babyGlobalHintRange = endOfLineRange(
                    activeEditor,
                    activeEditor.selection.active.line
                );
            } else {
                const prefix = "↪ ";
                const relPath = relativePathName(
                    currentPathName.relPath,
                    hintSuggestion.qualifiedPathName.relPath
                );
                const shortPathName = truncate(
                    relPath.length < hintSuggestion.qualifiedPathName.relPath.length
                        ? relPath
                        : hintSuggestion.qualifiedPathName.relPath,
                    BackgroundDecorationManager._maxBottomTextPathChars,
                    true
                );
                const suggestionDescription = hintSuggestion.result.changeDescription;
                const text = truncate(
                    `${prefix}${shortPathName}: ${suggestionDescription}`,
                    BackgroundDecorationManager._maxBottomTextChars,
                    false
                );
                this.addBottomDecoration(decorations, activeEditor, text);
            }
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                hintSuggestion,
                NextEditSessionEventName.SuggestionGlobalBottomTextShown,
                NextEditSessionEventSource.Background
            );
        }

        return decorations;
    }

    /** Draw a badge on a particular line. */
    private addBottomDecoration(
        decorations: DecorationCollection,
        editor: vscode.TextEditor,
        contentText: string
    ) {
        if (editor.visibleRanges.length === 0 || !this.shouldDrawBottomDecorations.value) {
            return;
        }
        const sortedVisibleRanges = getSortedVisibleRanges(editor);
        const lastVisibleRange = sortedVisibleRanges[sortedVisibleRanges.length - 1];
        // Unless the last line is visible, we'll draw the suggestion on the 2nd to last
        // line because the last line can be occluded by the horizontal scrollbar.
        const drawOnLastLine = lastVisibleRange.end.line >= editor.document.lineCount - 1;
        const cursor = editor.selection;
        // We don't want to draw the decorations if the cursor is too close to the
        // bottom because the user might care about the lines occluded by the
        // decorations. "Too close" means the line above where we draw the decorations
        // or below.
        const cursorIsNearBottom = drawOnLastLine
            ? cursor.active.line > lastVisibleRange.end.line - 2
            : cursor.active.line > lastVisibleRange.end.line - 3;
        if (sortedVisibleRanges.some((r) => r.intersection(cursor)) && cursorIsNearBottom) {
            return;
        }
        const truncatedText = truncate(
            contentText,
            BackgroundDecorationManager._maxBottomTextChars
        );

        const addBlankLine = (line: number) => {
            if (line < 0 || line >= editor.document.lineCount) {
                return;
            }
            const lineWidth = editor.document.lineAt(line).text.length;
            decorations.get(this._bottomLineDecorationType)!.push({
                range: new vscode.Range(line, lineWidth, line, lineWidth),
                renderOptions: {
                    after: {
                        // Add negative margin to the left to cover the text on this
                        // line.
                        // Add negative margin to the right adjust for the whitespace we
                        // just added, so following decorations don't need to add
                        // negative margin themselves.
                        margin: `0 ${-lineWidth}ch 0 ${-lineWidth}ch`,
                        contentText: `${"\xa0".repeat(lineWidth)}`,
                        padding: "0 0 0.3lh 0",
                    } as vscode.DecorationRenderOptions,
                },
            });
        };

        // 1. Draw blank lines to hide the code underneath.
        if (drawOnLastLine) {
            addBlankLine(lastVisibleRange.end.line);
        } else {
            // We're drawing the decorations on the last but one-th line, so we'll blank
            // out everything underneath.
            addBlankLine(lastVisibleRange.end.line - 1);
            addBlankLine(lastVisibleRange.end.line);
            // (it's possible for the line + 1 and line + 2 to be partially visible).
            addBlankLine(lastVisibleRange.end.line + 1);
            addBlankLine(lastVisibleRange.end.line + 2);
        }

        const decorationLine = drawOnLastLine
            ? editor.document.lineCount - 1
            : lastVisibleRange.end.line - 1;
        const lineWidth = editor.document.lineAt(decorationLine).text.length;
        const decorationLineRange = new vscode.Range(
            decorationLine,
            lineWidth,
            decorationLine,
            lineWidth
        );
        this.babyGlobalHintRange = new vscode.Range(decorationLine, 0, decorationLine, lineWidth);
        this.bottomBoxRange = this.babyGlobalHintRange;
        // 2. Draw the bounding box with nice rounded corners.
        // This width is actually too short, but we give the the text itself a matching background too.
        // It really only needs to be long enough for the keybindings and logo. This way we let the
        // natural text width determine the exact right size of the box.
        const boxWidth = 20;

        decorations.get(this._bottomBoxDecorationType)!.push({
            range: decorationLineRange,
            renderOptions: {
                after: {
                    // Add negative margin to the right adjust for the whitespace we
                    // add, and add negative margin.
                    // +1 to account for the one character right padding.
                    margin: `0 -${boxWidth + 1}ch 0 0`,
                    // Fill the box with whitespace so we can draw things on top of it.
                    contentText: `${"\xa0".repeat(boxWidth)}`,
                    // Add a little more top padding because keybinding icons are tall.
                    padding: "0.2em 1ch 0.15em 1ch",
                    // NOTE(arun): borderRadius isn't an officially supported property,
                    // but it works if we force the type to DecorationRenderOptions.
                    borderRadius: "0.3em",
                } as vscode.DecorationRenderOptions,
            },
        });

        const bottomBoxLogoDecorBase = {
            // font can only be specified here, not at creation
            contentText: "\uE901",
            fontFamily: '"Augment.vscode-augment/augment-icon-font.woff"',
            verticalAlign: "top",
            margin: "0 1ch 0 0",
        };
        decorations.get(this._bottomBoxLogoDecorationType)!.push({
            // to set the font face we MUST do it this way,
            // it has to sneak in through the light/dark overrides,
            // and can only be set here, not at decoration creation time.
            // refer to https://github.com/microsoft/vscode/blob/release/1.94/src/vs/editor/browser/services/abstractCodeEditorService.ts#L782
            renderOptions: {
                light: {
                    after: bottomBoxLogoDecorBase as vscode.ThemableDecorationRenderOptions,
                },
                dark: {
                    after: bottomBoxLogoDecorBase as vscode.ThemableDecorationRenderOptions,
                },
            } as vscode.DecorationRenderOptions,
            range: decorationLineRange,
        });

        // 3. Draw the keybindings.
        this.addKeybindingDecor(decorationLine, decorations, editor);

        // 4. Draw the text hint.
        decorations.get(this._bottomTextDecorationType)!.push({
            range: decorationLineRange,
            renderOptions: {
                after: {
                    // 1ch left padding after the keybindings.
                    margin: `0 0 0 1ch`,
                    contentText: truncatedText,
                    // we match the top, bottom and right padding of the box.
                    padding: "0.2em 1ch 0.15em 0",
                    // NOTE: borderRadius isn't an officially supported property,
                    // but it works if we force the type to DecorationRenderOptions.
                    // We only put border radius on the right side to match the box.
                    borderRadius: "0 0.3em 0.3em 0",
                } as vscode.DecorationRenderOptions,
            },
        });
    }

    private _addPreviewBox(
        decorations: DecorationCollection,
        editor: vscode.TextEditor,
        suggestion: EditSuggestion,
        mode: NoDiffPreviewMode
    ) {
        const isAccepted = mode === NoDiffPreviewMode.accepted;
        if (
            (suggestion.changeType === ChangeType.insertion && !isAccepted) ||
            (suggestion.changeType === ChangeType.deletion && isAccepted)
        ) {
            // this decoration is handled by the zeroHeightDiffLineBuilder
            return;
        }

        let range = suggestion.previewBoxRange(editor.document);
        if (range.stop - range.start <= 1) {
            decorations
                .get(this._activeChangeDecoration1Line.get(mode)!)
                ?.push(startOfLineRange(range.start));
        } else {
            decorations
                .get(this._activeChangeDecorationTop.get(mode)!)
                ?.push(startOfLineRange(range.start));
        }
        for (let i = range.start + 1; i < range.stop - 1; i++) {
            decorations
                .get(this._activeChangeDecorationMiddle.get(mode)!)
                ?.push(startOfLineRange(i));
        }
        if (range.stop - range.start > 1) {
            decorations
                .get(this._activeChangeDecorationBottom.get(mode)!)
                ?.push(startOfLineRange(range.stop - 1));
        }
    }
}

/**
 * Gets (change type, range) tuples for every changed span in the suggestion.
 *
 * @param document The document to which the suggestion applies.
 * @param suggestion The suggestion.
 * @param isBefore indicates the current state of the document is before the suggestion
 * has been applied. If false, the current state is after the suggesetion has been applied.
 * @returns A list of (change type, range) tuples. The ranges are relative to the
 *  document.
 */
function _getLabeledSpans(
    document: vscode.TextDocument,
    suggestion: EditSuggestion,
    isBefore: boolean
): [ChangeType, vscode.Range][] {
    const oldText = suggestion.result.existingCode;
    const newText = suggestion.result.suggestedCode;

    const resultSpans: [ChangeType, vscode.Range][] = [];
    for (const span of suggestion.result.diffSpans) {
        const oldSpan = oldText.slice(span.original.start, span.original.stop);
        const newSpan = newText.slice(span.updated.start, span.updated.stop);
        const spanType = EditSuggestion.determineChangeType(oldSpan, newSpan);

        const targetSpan = isBefore ? span.original : span.updated;
        let startOffset = suggestion.result.charStart + targetSpan.start;
        let endOffset = suggestion.result.charStart + targetSpan.stop;

        if (spanType === (isBefore ? ChangeType.insertion : ChangeType.deletion)) {
            // just move the range to the end of the previous line
            if (document.positionAt(startOffset).character === 0 && newSpan.endsWith("\n")) {
                startOffset = suggestion.result.charStart + targetSpan.start - 1;
            }
        }

        resultSpans.push([
            spanType,
            new vscode.Range(document.positionAt(startOffset), document.positionAt(endOffset)),
        ]);
    }
    return resultSpans;
}
