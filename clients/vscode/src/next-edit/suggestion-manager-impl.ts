/**
 * `SuggestionManager` manages a collection of next edit suggestions to keep track
 * of acceptances and rejections, and to keep them consistent with text edit events.
 *
 * This manager is used by the different views (background, diff-view, global, etc.).
 */
import { partition } from "lodash";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { DisposableService } from "../utils/disposable-service";
import { EphemeralObservable } from "../utils/ephemeral-flag";
import { WorkspaceManager } from "../workspace/workspace-manager";
import {
    ChangeType,
    NextEditMode,
    NextEditSessionEventName,
    NextEditSessionEventSource,
    SuggestionState,
} from "./next-edit-types";
import { EditSuggestion, SuggestionChangedEvent, SuggestionManager } from "./suggestion-manager";
import {
    Accepted,
    Invalidated,
    SuggestionUpdate,
    Unchanged,
    Undone,
    Updated,
    updateSuggestionAfterChangeEvent,
} from "./suggestion-update";

type SuggestionWithTimeout = readonly [suggestion: EditSuggestion, timeout: number];

export class SuggestionManagerImpl extends DisposableService implements SuggestionManager {
    /** All currently valid suggestions in the workspace. */
    private _suggestions: EditSuggestion[] = [];
    /**
     * Suggestions that were just accepted.
     *
     * We keep these around to allow users to "undo" accepting them from the editor
     * or the panel.
     */
    private _justAcceptedSuggestions: EditSuggestion[] = [];
    /** Suggestions rejected by the user, the time the rejection was made. */
    private _rejectedSuggestions: SuggestionWithTimeout[] = [];

    /**
     * A list of listeners for suggestions changed events.
     *
     * NOTE(arun): We don't use vscode.EventEmitter here because we want to immediately
     * notify listeners of changes and not wait for the next event loop.
     */
    private _suggestionsChangedListeners: ((
        event: SuggestionChangedEvent
    ) => void | Promise<void>)[] = [];

    public readonly suggestionWasJustAccepted: EphemeralObservable<boolean>;
    public readonly suggestionWasJustUndone: EphemeralObservable<boolean>;

    private _logger = getLogger("SuggestionManagerImpl");

    constructor(
        /** The workspace manager; used to resolve URIs. */
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter,
        /** The lifetime of a rejection in milliseconds. */
        private readonly defaultRejectionDurationMs = 1000 * 60 * 10
    ) {
        super();
        this.suggestionWasJustAccepted = new EphemeralObservable();
        this.suggestionWasJustUndone = new EphemeralObservable();
        this.addDisposable(this.suggestionWasJustAccepted);
        this.addDisposable(this.suggestionWasJustUndone);
        this.addDisposable(new vscode.Disposable(() => this.clear(true)));
        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument((event) => {
                this.handleChangeEvent(event);
            })
        );
    }

    /** Register an event listener for suggestions changed event. */
    public onSuggestionsChanged(
        callback: (event: SuggestionChangedEvent) => void | Promise<void>
    ): vscode.Disposable {
        this._suggestionsChangedListeners.push(callback);
        return new vscode.Disposable(() => {
            this._suggestionsChangedListeners = this._suggestionsChangedListeners.filter(
                (c) => c !== callback
            );
        });
    }

    /** Actually dispatch a suggestions changed event. */
    private dispatchSuggestionsChangedEvent(event: SuggestionChangedEvent): void {
        const filteredEvent = {
            ...event,
            newSuggestions: event.newSuggestions,
            oldSuggestions: event.oldSuggestions,
        };
        this._suggestionsChangedListeners.forEach((c) => void c(filteredEvent));
    }

    /**
     * Add new suggestions to the manager.
     *
     * @param suggestions The suggestions to add.
     * @param overrideRejection Whether to override the rejection of the suggestions.
     *   If true, the suggestions will be added to the manager even if they have been
     *   rejected previously, and those rejections will be cleared from the manager.
     */
    public add(suggestions: EditSuggestion[], overrideRejection?: boolean): boolean {
        // Clear out old rejections.
        this._rejectedSuggestions = this._getCurrentRejectedSuggestions();
        if (overrideRejection) {
            // Clear any rejections that overlap with the suggestions.
            const rejectedSuggestionCount = this._rejectedSuggestions.length;
            this._rejectedSuggestions = this._rejectedSuggestions.filter(
                ([s, _]) => !suggestions.some((s2) => s.intersects(s2))
            );
            if (rejectedSuggestionCount !== this._rejectedSuggestions.length) {
                this._logger.debug(
                    `Clearing ${
                        rejectedSuggestionCount - this._rejectedSuggestions.length
                    } old rejections.`
                );
            }
        } else {
            const suggestionCount = suggestions.length;
            suggestions = suggestions.filter(
                (s) => !this._rejectedSuggestions.some(([s2, _]) => s2.intersects(s))
            );
            if (suggestionCount !== suggestions.length) {
                this._logger.debug(
                    `Dropping ${suggestionCount - suggestions.length} suggestions ` +
                        `that overlap with rejected suggestions.`
                );
            }
        }

        if (!suggestions.length) {
            return false;
        }

        const oldSuggestions = this._suggestions;
        // Remove any existing suggestions that intersect with the new suggestions.
        this._filterSuggestions((s) => !suggestions.some((s2) => s.intersects(s2)));
        // And now add the new suggestions in.
        for (const suggestion of suggestions) {
            this._reportNonemptyEvent(suggestion, NextEditSessionEventName.NonemptySuggestionAdded);
        }
        this._suggestions.push(...suggestions);
        this.checkValidity(this._suggestions);
        this.dispatchSuggestionsChangedEvent({
            oldSuggestions: oldSuggestions,
            newSuggestions: this._suggestions,
            accepted: this._justAcceptedSuggestions,
            rejected: [],
            undone: [],
        });
        return true;
    }

    /** Remove suggestions from the manager. */
    public remove(suggestions: EditSuggestion[]): boolean {
        if (!suggestions.length) {
            return false;
        }

        const oldSuggestions = this._suggestions;
        const oldRejectedSuggestions = this._rejectedSuggestions;
        this._filterSuggestions((s) => !suggestions.some((s2) => s2.equals(s)));
        this._rejectedSuggestions = this._getCurrentRejectedSuggestions().filter(
            ([s, _]) => !suggestions.some((s2) => s2.equals(s))
        );
        this._logger.debug(
            `Removing ${oldSuggestions.length - this._suggestions.length} suggestions and ${
                oldRejectedSuggestions.length - this._rejectedSuggestions.length
            } rejections from the manager.`
        );
        if (oldSuggestions.length !== this._suggestions.length) {
            // NOTE(arun): We don't currently notify when rejections have been
            // changed, but maybe we should?
            this.dispatchSuggestionsChangedEvent({
                oldSuggestions: oldSuggestions,
                newSuggestions: this._suggestions,
                accepted: this._justAcceptedSuggestions,
                rejected: [],
                undone: [],
            });
            return true;
        }
        return oldRejectedSuggestions.length !== this._rejectedSuggestions.length;
    }

    public clear(clearRejectedSuggestions: boolean, mode?: NextEditMode) {
        const oldSuggestions = this._suggestions;
        for (const suggestion of oldSuggestions) {
            this._reportNonemptyEvent(
                suggestion,
                NextEditSessionEventName.NonemptySuggestionCleared
            );
        }
        if (mode) {
            this._suggestions = this._suggestions.filter((s) => s.mode !== mode);
        } else {
            this._suggestions = [];
        }
        if (clearRejectedSuggestions) {
            this._rejectedSuggestions = [];
        }
        this._justAcceptedSuggestions = [];
        this.dispatchSuggestionsChangedEvent({
            oldSuggestions: oldSuggestions,
            newSuggestions: [],
            accepted: this._justAcceptedSuggestions,
            rejected: [],
            undone: [],
        });
    }

    /** Get the currently active suggestions. */
    public getActiveSuggestions(): EditSuggestion[] {
        return [...this._suggestions];
    }

    public getJustAcceptedSuggestions(): EditSuggestion[] {
        return [...this._justAcceptedSuggestions];
    }

    public clearJustAcceptedSuggestions() {
        if (this._justAcceptedSuggestions.length === 0) {
            return;
        }
        this._justAcceptedSuggestions = [];
        this.dispatchSuggestionsChangedEvent({
            oldSuggestions: this._suggestions,
            newSuggestions: this._suggestions,
            accepted: this._justAcceptedSuggestions,
            rejected: [],
            undone: [],
        });
    }

    public findSuggestionById(suggestionId: string): EditSuggestion | undefined {
        return this._suggestions.find((s) => s.result.suggestionId === suggestionId);
    }

    private _getCurrentRejectedSuggestions(): SuggestionWithTimeout[] {
        const now = Date.now();
        const currentRejectedSuggestions = this._rejectedSuggestions.filter(
            ([_, rejectedAt]) => now < rejectedAt
        );
        if (currentRejectedSuggestions.length !== this._rejectedSuggestions.length) {
            this._logger.debug(
                `Filtering out ${
                    this._rejectedSuggestions.length - currentRejectedSuggestions.length
                } expired rejections.`
            );
        }
        return currentRejectedSuggestions;
    }

    /**
     * Get the currently rejected suggestions.
     *
     * Note that this will also clear out old rejections.
     */
    public getRejectedSuggestions(): EditSuggestion[] {
        // Clear out old rejections.
        this._rejectedSuggestions = this._getCurrentRejectedSuggestions();
        return this._rejectedSuggestions.map(([s, _]) => s);
    }

    public getAllSuggestions(): EditSuggestion[] {
        return [...this.getActiveSuggestions(), ...this.getRejectedSuggestions()];
    }

    /**
     * Mark suggestions as accepted; update the remaining suggestions in the manager.
     *
     * This method will update the suggestions in the manager, and trigger the
     * `onSuggestionsChanged` event.
     */
    public accept(suggestions: EditSuggestion[]): void {
        if (suggestions.length === 0) {
            return;
        }

        // We'll just apply all the suggestions to the workspace; the suggestions will
        // be removed from the manager when updateAfterChangeEvent occurs.
        const workspaceEdit = new vscode.WorkspaceEdit();
        for (const suggestion of suggestions) {
            // TODO(arun): We should validate that suggestion's existing code matches
            // what's in the document, but it will make this function asynchronous as we
            // need to open all the documents first.
            workspaceEdit.replace(
                vscode.Uri.from({
                    scheme: suggestion.uriScheme,
                    path: suggestion.qualifiedPathName.absPath,
                }),
                new vscode.Range(
                    new vscode.Position(suggestion.lineRange.start, 0),
                    new vscode.Position(suggestion.lineRange.stop, 0)
                ),
                suggestion.result.suggestedCode
            );
        }
        void vscode.workspace.applyEdit(workspaceEdit);
    }

    /**
     * Mark suggestions as rejected; avoid showing/adding suggestion going forward.
     *
     * This method will update the suggestions in the manager, and trigger the
     * `onSuggestionsChanged` event.
     */
    public reject(
        rejectedSuggestions: EditSuggestion[],
        rejectionDurationMs: number = this.defaultRejectionDurationMs
    ): void {
        if (rejectedSuggestions.length === 0) {
            return;
        }
        this._logger.debug(`Rejecting ${rejectedSuggestions.length} suggestions.`);

        this._justAcceptedSuggestions = this._justAcceptedSuggestions.filter(
            (s) => !rejectedSuggestions.some((s2) => s2.equals(s))
        );

        const oldSuggestions = this._suggestions;
        // Filter out the rejected suggestions from the active list.
        this._filterSuggestions((s) => !rejectedSuggestions.some((s2) => s2.equals(s)));
        // And add them to the rejected suggestions list.
        const expiresAt = Date.now() + rejectionDurationMs;
        const rejectedSuggestionsAt = rejectedSuggestions.map((s) => [s, expiresAt] as const);
        this._rejectedSuggestions =
            this._getCurrentRejectedSuggestions().concat(rejectedSuggestionsAt);
        this.dispatchSuggestionsChangedEvent({
            oldSuggestions: oldSuggestions,
            newSuggestions: this._suggestions,
            accepted: this._justAcceptedSuggestions,
            rejected: rejectedSuggestions,
            undone: [],
        });
    }

    /**
     * Update the suggestions after a text change event.
     *
     * Callers will typically call this method after a text change event.
     *
     * This method will update the suggestions in the manager, and trigger the
     * `onSuggestionsChanged` event.
     */
    public handleChangeEvent(event: vscode.TextDocumentChangeEvent): boolean {
        if (event.contentChanges.length === 0) {
            return false;
        }
        // Ignore changes in files that outside our workspace.
        const qualifiedPathName = this._workspaceManager.safeResolvePathName(event.document.uri);
        if (qualifiedPathName === undefined) {
            return false;
        }
        const oldSuggestions = this._suggestions;

        // We'll get all the suggestions from the same file as the event, and
        // apply the change event to them. All the other suggestions will be marked
        // stale, but don't need their offsets adjusted.
        const [sameFileSuggestions, otherSuggestions] = partition(this._suggestions, (s) =>
            qualifiedPathName.equals(s.qualifiedPathName)
        );
        const suggestionUpdates: SuggestionUpdate[] = sameFileSuggestions.map((s) =>
            updateSuggestionAfterChangeEvent(s, event)
        );
        const justAcceptedSuggestionUpdates = this._justAcceptedSuggestions.map((s) =>
            updateSuggestionAfterChangeEvent(s, event)
        );
        const justRejectedSuggestionUpdates = this._getCurrentRejectedSuggestions().map(([s, _]) =>
            updateSuggestionAfterChangeEvent(s, event)
        );

        const acceptedSuggestions = filterSuggestionUpdates(suggestionUpdates, Accepted);
        const invalidatedSuggestions = filterSuggestionUpdates(suggestionUpdates, Invalidated);
        const undoneSuggestions = filterSuggestionUpdates(justAcceptedSuggestionUpdates, Undone);
        const rejectedUndoneSuggestions = filterSuggestionUpdates(
            justRejectedSuggestionUpdates,
            Undone
        );
        // Note how we concat the latest updates at the end here. That way the most recent
        // accepted suggestion is at the end of the array.
        this._justAcceptedSuggestions = filterSuggestionUpdates(
            justAcceptedSuggestionUpdates,
            Unchanged
        ).concat(filterSuggestionUpdates(justAcceptedSuggestionUpdates, Updated));

        // NOTE(arun): It is common for the backend to return a suggestion for the same
        // line range as the one we just accepted. Undoing the accepted suggestion
        // would naturally invalidate the new one. This is fine, but we don't want to
        // mark all the remaining suggestions as stale as that causes everything
        // to be cleared.
        const allUndoneSuggestions = undoneSuggestions.concat(rejectedUndoneSuggestions);
        const invalidatedDueToUndo = invalidatedSuggestions.filter((s) =>
            allUndoneSuggestions.some(
                (s2) =>
                    s2.lineRange.equals(s.afterLineRange(event.document)) ||
                    s2.lineRange.intersects(s.afterLineRange(event.document))
            )
        );
        if (
            acceptedSuggestions.length > 0 ||
            invalidatedSuggestions.length > 0 ||
            allUndoneSuggestions.length > 0
        ) {
            this._logger.debug(
                `Accepting ${acceptedSuggestions.length} suggestions ` +
                    `and invalidating ${invalidatedSuggestions.length} suggestions ` +
                    `(of which ${invalidatedDueToUndo.length} were invalidated due to undo) ` +
                    `and undoing ${allUndoneSuggestions.length} suggestions.`
            );
        }

        // Update the state of the suggestions that were accepted.
        const markStale =
            acceptedSuggestions.length === 0 &&
            allUndoneSuggestions.length === 0 &&
            invalidatedSuggestions.length > invalidatedDueToUndo.length;
        const updatedSuggestions = [
            ...filterSuggestionUpdates(suggestionUpdates, Unchanged),
            ...filterSuggestionUpdates(suggestionUpdates, Updated),
        ].map((s) =>
            this._markStaleIfNeeded(s, markStale, NextEditSessionEventSource.DocumentChanged)
        );
        const updatedOtherSuggestions = otherSuggestions.map((s) =>
            this._markStaleIfNeeded(s, markStale, NextEditSessionEventSource.DocumentChanged)
        );
        for (const s of acceptedSuggestions) {
            this._reportNonemptyEvent(
                s,
                event.reason === vscode.TextDocumentChangeReason.Redo
                    ? NextEditSessionEventName.NonemptySuggestionRedone
                    : NextEditSessionEventName.NonemptySuggestionAccepted,
                NextEditSessionEventSource.DocumentChanged
            );
        }
        for (const s of invalidatedSuggestions) {
            this._reportNonemptyEvent(
                s,
                NextEditSessionEventName.NonemptySuggestionInvalidated,
                NextEditSessionEventSource.DocumentChanged
            );
        }
        for (const s of allUndoneSuggestions) {
            this._reportNonemptyEvent(
                s,
                NextEditSessionEventName.NonemptySuggestionUndone,
                NextEditSessionEventSource.DocumentChanged
            );
        }

        // Finally combine the suggestions that were not updated with the ones that
        // were as well as the ones that were undone, and let listeners know.
        // Note this is undoneSuggestions and not allUndoneSuggestions (we don't want to bring
        // back rejected suggestions)
        this._suggestions = updatedOtherSuggestions.concat(updatedSuggestions, undoneSuggestions);
        this.checkValidity(this._suggestions);

        // Ignore previously just accepted suggestions if there was an unexpected edit.
        // Otherwise, we'll keep around the list of just accepted suggestions: this
        // allows the user to "undo" or "redo" accepting a suggestion from the
        // panel.
        this._justAcceptedSuggestions = (markStale ? [] : this._justAcceptedSuggestions).concat(
            acceptedSuggestions
        );

        if (acceptedSuggestions.length > 0) {
            this.suggestionWasJustAccepted.set(true);
        }
        // We don't do this for rejectedUndones because we aren't going to show the hover and such for them.
        if (undoneSuggestions.length > 0) {
            this.suggestionWasJustUndone.set(true);
        }
        this.dispatchSuggestionsChangedEvent({
            oldSuggestions: oldSuggestions,
            newSuggestions: this._suggestions,
            accepted: this._justAcceptedSuggestions,
            rejected: [],
            undone: undoneSuggestions,
        });
        return acceptedSuggestions.length > 0;
    }

    private _filterSuggestions(filterFn: (s: EditSuggestion) => boolean) {
        this._suggestions = this._suggestions.filter((s) => {
            const keep = filterFn(s);
            if (!keep) {
                this._reportNonemptyEvent(s, NextEditSessionEventName.NonemptySuggestionDropped);
            }
            return keep;
        });
        this.checkValidity(this._suggestions);
    }

    private _markStaleIfNeeded(
        s: EditSuggestion,
        markStale: boolean,
        source?: NextEditSessionEventSource
    ): EditSuggestion {
        if (markStale) {
            this._reportNonemptyEvent(
                s,
                NextEditSessionEventName.NonemptySuggestionBecomesStale,
                source
            );
            return s.with({ state: SuggestionState.stale });
        }
        return s;
    }

    /**
     * Helper function to check if a set of suggestions is valid.
     *
     * A set of suggestions is valid iff:
     * - All suggestions are non-intersecting.
     */
    private checkValidity(suggestions: EditSuggestion[]): boolean {
        if (suggestions.length < 2) {
            return true;
        }

        // TODO(arun): We should maintain suggestions in sorted order in the first place
        // but we'll sort a copy to make sure nothing changes.
        const sortedSuggestions = [...suggestions];
        sortedSuggestions.sort(
            (s, s2) =>
                s.qualifiedPathName.relPath.localeCompare(s2.qualifiedPathName.relPath) ||
                s.lineRange.compareTo(s2.lineRange)
        );

        const prevSuggestion = sortedSuggestions[0];
        for (let i = 1; i < sortedSuggestions.length; i++) {
            const currSuggestion = sortedSuggestions[i];
            if (prevSuggestion.intersects(currSuggestion)) {
                this._logger.error(
                    `Found intersecting suggestions, ${prevSuggestion.toString()} and ${currSuggestion.toString()}`
                );
                this._nextEditSessionEventReporter.reportEventFromSuggestion(
                    currSuggestion,
                    NextEditSessionEventName.ErrorIntersectingSuggestions,
                    NextEditSessionEventSource.Unknown
                );
                return false;
            }
        }
        return true;
    }

    private _reportNonemptyEvent(
        suggestion: EditSuggestion,
        eventName: NextEditSessionEventName,
        source?: NextEditSessionEventSource
    ) {
        if (suggestion.changeType !== ChangeType.noop) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                eventName,
                source ?? NextEditSessionEventSource.Unknown
            );
        }
    }
}

/** Helper function to get suggestions of a particular update type. */
function filterSuggestionUpdates<T extends SuggestionUpdate>(
    updates: SuggestionUpdate[],
    type: new (suggestion: EditSuggestion) => T
): EditSuggestion[] {
    return updates
        .filter((update): update is T => update instanceof type)
        .map((update) => update.suggestion);
}
