import { escapeHtml } from "../utils/escape-html";

/**
 *
 * @param input a string possibly containing codicons like $(gear) or $(file) which
 * vscode's Markdown system would render into icons, but we don't want them to
 * become icons because, for example, they are actually coming from source code.
 * @returns the string with the codicons broken up with an empty HTML tag so that they are not rendered as icons.
 */
export function breakMarkdownCodicons(input: string): string {
    return input.replace(/\$\(([\w-]+)\)/gi, "&dollar;($1)");
}

export function escapeAndBreakMarkdownCodicons(input: string): string {
    return breakMarkdownCodicons(escapeHtml(input));
}
