import * as vscode from "vscode";

import { DisposableService } from "../utils/disposable-service";
import { DecorationCollection } from "./decoration-manager";
import { ChangeType } from "./next-edit-types";

export type ZeroHeightDiffLineStyles = {
    lineColor: string | vscode.ThemeColor;
    lightBorderColor: string | vscode.ThemeColor;
    darkBorderColor: string | vscode.ThemeColor;
    lightBackgroundColor: string | vscode.ThemeColor;
    darkBackgroundColor: string | vscode.ThemeColor;
    borderWidth: string;
    overviewRulerColor: string | vscode.ThemeColor | undefined;
};

type AllowedChangeType = ChangeType.insertion | ChangeType.deletion;

const borderPartIndexes = {
    // The order of these is important for the way the decorations are applied.
    border: 0,
    background: 1,
    line: 2,
} as const;

/**
 * These decorations represent the situation where a line has been added or deleted and
 * we want to show the post-deletion or pre-insertion state. We just draw a thin line border
 * between the 2 surrounding lines. We use 3 levels of borders to create the effect of a box
 * like the "preview box" used by BackgroundDecorationManager.
 */
export class ZeroHeightDiffLineBuilder extends DisposableService {
    private _decorationTypes: Record<
        AllowedChangeType,
        { upper: vscode.TextEditorDecorationType[]; lower: vscode.TextEditorDecorationType[] }
    > = {
        [ChangeType.insertion]: { upper: [], lower: [] },
        [ChangeType.deletion]: { upper: [], lower: [] },
    };

    constructor(
        private _styles: {
            insertion: ZeroHeightDiffLineStyles;
            deletion: ZeroHeightDiffLineStyles;
        }
    ) {
        super();
        this._decorationTypes[ChangeType.insertion] = this._createDecorations(
            this._styles.insertion
        );
        this._decorationTypes[ChangeType.deletion] = this._createDecorations(this._styles.deletion);
        this.addDisposables(
            ...Object.values(this._decorationTypes[ChangeType.insertion]).flat(),
            ...Object.values(this._decorationTypes[ChangeType.deletion]).flat()
        );
    }

    private _createDecorations(styles: ZeroHeightDiffLineStyles): {
        upper: vscode.TextEditorDecorationType[];
        lower: vscode.TextEditorDecorationType[];
    } {
        return {
            upper: this._createHalfDecoration(styles, false),
            lower: this._createHalfDecoration(styles, true),
        };
    }

    private _createHalfDecoration(
        styles: ZeroHeightDiffLineStyles,
        isLower: boolean
    ): vscode.TextEditorDecorationType[] {
        const makeBorderWidth = (width: string) => {
            return isLower ? `${width} 0 0 0` : `0 0 ${width} 0`;
        };
        return [
            vscode.window.createTextEditorDecorationType({
                borderWidth: makeBorderWidth(styles.borderWidth),
                light: {
                    borderColor: styles.lightBorderColor,
                },
                dark: {
                    borderColor: styles.darkBorderColor,
                },
                borderStyle: "solid",
                isWholeLine: true,
            }),
            vscode.window.createTextEditorDecorationType({
                borderWidth: makeBorderWidth("0.2em"),
                light: {
                    borderColor: styles.lightBackgroundColor,
                },
                dark: {
                    borderColor: styles.darkBackgroundColor,
                },
                borderStyle: "solid",
                isWholeLine: true,
            }),
            vscode.window.createTextEditorDecorationType({
                borderWidth: makeBorderWidth("0.1em"),
                borderColor: styles.lineColor,
                borderStyle: "solid",
                isWholeLine: true,
                overviewRulerColor: isLower ? styles.overviewRulerColor : undefined,
                overviewRulerLane: isLower ? vscode.OverviewRulerLane.Right : undefined,
            }),
        ];
    }

    public addEmptyDecorations(decorations: DecorationCollection): void {
        for (const changeType of [
            ChangeType.insertion,
            ChangeType.deletion,
        ] as AllowedChangeType[]) {
            for (const half of ["upper", "lower"] as const) {
                for (const decorationType of this._decorationTypes[changeType][half]) {
                    decorations.set(decorationType, []);
                }
            }
        }
    }

    /**
     * Adds full-length border-line for the corresponding change type. The border goes above `line`.
     * This includes the blue preview box.
     * @param changeType
     * @param line
     * @param decorations the collection which we will add to
     */
    // TODO handle line number exceeding doc length? does it matter?
    public addDecorations(
        changeType: AllowedChangeType,
        line: number,
        decorations: DecorationCollection
    ): void {
        if (line > 0) {
            for (const decorationType of this._decorationTypes[changeType].upper) {
                decorations.get(decorationType)?.push(new vscode.Range(line - 1, 0, line - 1, 0));
            }
        }
        for (const decorationType of this._decorationTypes[changeType].lower) {
            decorations.get(decorationType)?.push(new vscode.Range(line, 0, line, 0));
        }
    }

    /**
     * This differs from `addDecorations` in that it only adds the thin line without the preview-box
     * This only matters for insertions because deletions only need the red line when they are in
     * the preview box.
     * @param line
     * @param decorations the collection which we will add to
     */
    // TODO handle line number exceeding doc length? does it matter?
    public addSimpleInsertionDecoration(line: number, decorations: DecorationCollection) {
        if (line > 0) {
            decorations
                .get(this._decorationTypes[ChangeType.insertion].upper[borderPartIndexes.line])
                ?.push(new vscode.Range(line - 1, 0, line - 1, 0));
        }

        decorations
            .get(this._decorationTypes[ChangeType.insertion].lower[borderPartIndexes.line])
            ?.push(new vscode.Range(line, 0, line, 0));
    }
}
