import { Augment<PERSON>ogger } from "../logging";

export class WhitespaceInfo {
    private readonly _spaces: number;
    private readonly _tabs: number;

    private constructor({ spaces, tabs }: { spaces: number; tabs: number }) {
        this._spaces = spaces;
        this._tabs = tabs;
    }

    /**
     * Compute the common leading whitespace in an array of lines.
     * We separately count spaces and tabs.
     * That is, if one line starts with four spaces and another with one tab,
     * the minimum spaces and spaces will both be 0.
     */
    public static computeCommonLeadingWhitespace(lines: string[]): WhitespaceInfo {
        if (lines.length === 0) {
            return new WhitespaceInfo({ spaces: 0, tabs: 0 });
        }
        const nonEmptyLines = lines.filter((line) => line.trim().length > 0);
        const leadingSpacesAndTabs = nonEmptyLines.map((line) => {
            const match = line.match(/^([ \t]*)/);
            return match ? match[1] : "";
        });
        const numSpaces = leadingSpacesAndTabs.map((s) => s.length - s.replace(/ /g, "").length);
        const numTabs = leadingSpacesAndTabs.map((s) => s.length - s.replace(/\t/g, "").length);
        return new WhitespaceInfo({
            spaces: numSpaces.reduce((a, b) => Math.min(a, b), numSpaces[0]),
            tabs: numTabs.reduce((a, b) => Math.min(a, b), numTabs[0]),
        });
    }

    public trimLeadingIncremental(
        text: string
    ): { trimmed: string; remaining: WhitespaceInfo } | undefined {
        let remainingSpaces = this._spaces;
        let remainingTabs = this._tabs;
        let index = 0;
        while (index < text.length && (remainingSpaces > 0 || remainingTabs > 0)) {
            const char = text[index++];
            if (char === " " && remainingSpaces > 0) {
                remainingSpaces--;
            } else if (char === "\t" && remainingTabs > 0) {
                remainingTabs--;
            } else {
                return undefined;
            }
        }
        return {
            trimmed: text.slice(index),
            remaining: new WhitespaceInfo({ spaces: remainingSpaces, tabs: remainingTabs }),
        };
    }

    public trimLeadingFull(text: string, logger?: AugmentLogger): string {
        const result = this.trimLeadingIncremental(text);
        if (!result) {
            logger?.debug(`No common leading whitespace for line: ${text}`);
            return text;
        }
        if (!result.remaining.isEmpty() && text.length > 0) {
            logger?.debug(`Untrimmed whitespace`);
            return text;
        }
        return result.trimmed;
    }

    public isEmpty(): boolean {
        return this._spaces === 0 && this._tabs === 0;
    }

    public total(): number {
        return this._spaces + this._tabs;
    }
}
