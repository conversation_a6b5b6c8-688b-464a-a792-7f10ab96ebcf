/**
 * Manages a collection of `vscode.TextEditorDecoration`s to show.
 */
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import assert from "assert";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";

export type DecorationCollection = Map<
    vscode.TextEditorDecorationType,
    (vscode.Range | vscode.DecorationOptions)[]
>;

interface HasPath {
    qualifiedPathName: QualifiedPathName;
}

const _logger = getLogger("DecorationManager");

/**
 * Manages which decorations are shown at the current cursor position.
 */
export abstract class DecorationManager<T extends HasPath, K> extends DisposableService {
    /** Previously shown decorations; used to clear out decorations. */
    private previousDecorationsPerEditor: Map<vscode.TextEditor, DecorationCollection> = new Map();

    /**
     * Add decorations for a single item.
     *
     * @param decorations A decoration collection to add to.
     * @param editor The editor this suggestion belongs to.
     * @param item Item to decorate.
     * @param extraArgs Additional arguments for rendering passed in via `decorate`.
     */
    protected abstract addDecorationsForItem(
        decorations: DecorationCollection,
        editor: vscode.TextEditor,
        item: T,
        extraArgs: K
    ): DecorationCollection;

    /**
     * Add extra decorations for the currently active editor. This runs once per
     * decorate step, after `addDecorationsForItem` has been called for all items in
     * the active editor.
     *
     * @param decorations A decoration collection to add to.
     * @param _activeEditor The currently active editor.
     * @param _items All items being decorated right now.
     * @param _extraArgs Additional arguments for rendering passed in via `decorate`.
     */
    protected addExtraDecorationsForActiveEditor(
        decorations: DecorationCollection,
        _activeEditor: vscode.TextEditor,
        _items: T[],
        _extraArgs: K
    ): DecorationCollection {
        return decorations;
    }

    /** Generate an empty collection where each decoration key has an empty array. */
    protected abstract createEmptyDecorations(): DecorationCollection;

    /**
     * Get the decoration for the last suggestion. Optional to implement.
     */
    protected getDecorationsForNextSuggestion:
        | undefined
        | ((this: this, editor: vscode.TextEditor, options: K) => DecorationCollection);

    /**
     * Apply decorations to the given editor.
     *
     * @param items The items to decorate.
     * @param args Additional arguments to pass to `getDecorations`.
     */
    public decorate(items: T[], extraArgs: K) {
        // Collect decorations from all suggestions.
        const decorationsPerEditor: Map<vscode.TextEditor, DecorationCollection> = new Map();

        for (const editor of vscode.window.visibleTextEditors) {
            // editor.viewColumn is undefined in diff editors.
            if (!editor.viewColumn) {
                continue;
            }
            const decorations =
                decorationsPerEditor.get(editor) ??
                decorationsPerEditor.set(editor, this.createEmptyDecorations()).get(editor)!;

            const itemsForEditor = items.filter((v) =>
                v.qualifiedPathName.equals(editor.document.uri)
            );
            for (const item of itemsForEditor) {
                this.addDecorationsForItem(decorations, editor, item, extraArgs);
            }
            if (editor === vscode.window.activeTextEditor) {
                this.addExtraDecorationsForActiveEditor(decorations, editor, items, extraArgs);
            }
        }

        // Apply decorations to the editor.
        for (const [editor, decorations] of decorationsPerEditor) {
            for (const [decorationType, ranges] of decorations) {
                if (decorationType === undefined) {
                    // This should never happen.
                    _logger.warn("Got an undefined decoration type.");
                    continue;
                }
                assert(ranges.every((range) => range instanceof ranges[0].constructor));
                editor.setDecorations(
                    decorationType,
                    ranges as readonly vscode.Range[] | readonly vscode.DecorationOptions[]
                );
            }
        }

        // Clear out decorations from editors that no longer have suggestions.
        for (const [editor, decorations] of this.previousDecorationsPerEditor) {
            // Get rid of closed editors.
            if (!vscode.window.visibleTextEditors.includes(editor)) {
                this.previousDecorationsPerEditor.delete(editor);
                continue;
            }
            if (decorationsPerEditor.has(editor)) {
                continue;
            }
            for (const [decorationType, _] of decorations) {
                if (decorationType === undefined) {
                    // This should never happen.
                    _logger.warn("Got an undefined decoration type.");
                    continue;
                }
                editor.setDecorations(decorationType, []);
            }
        }
        this.previousDecorationsPerEditor = decorationsPerEditor;
    }
}
