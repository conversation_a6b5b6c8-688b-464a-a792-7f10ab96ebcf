// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`file-edit-watcher calculates offsets correctly 1`] = `
[
  FileEditEvent {
    "afterBlobName": "6653c3cf73b0bde9c00a72c61716866a628d5089ba954932c036d52c8ba042e8",
    "beforeBlobName": "aa7fb71e6a31dd8d4690b2df8dff76a5902470462a6d8995bdfab7a360daf87b",
    "edits": [
      SingleEdit {
        "afterStart": 0,
        "afterText": "// ",
        "beforeStart": 0,
        "beforeText": "",
      },
      SingleEdit {
        "afterStart": 9,
        "afterText": "// ",
        "beforeStart": 6,
        "beforeText": "",
      },
      SingleEdit {
        "afterStart": 18,
        "afterText": "// ",
        "beforeStart": 12,
        "beforeText": "",
      },
    ],
    "path": "test.py",
  },
]
`;

exports[`file-edit-watcher coalescing canceling events 1`] = `
[
  FileEditEvent {
    "afterBlobName": "8050ec05e9f353967a2a213d42b9b668b4039f907ecb670bf7b2bf84d6776ba6",
    "beforeBlobName": "71e9990455c9e28488a28971b57a71a47ae2c9efb3b0c92122a07b602e7ad561",
    "edits": [
      SingleEdit {
        "afterStart": 5,
        "afterText": "!",
        "beforeStart": 5,
        "beforeText": "",
      },
    ],
    "path": "test.py",
  },
]
`;

exports[`file-edit-watcher coalescing deletion events 1`] = `
[
  FileEditEvent {
    "afterBlobName": "de93efe107c69ee9550e94e77ec43788b22b9064b2bca0d5d7a93709705a380e",
    "beforeBlobName": "71e9990455c9e28488a28971b57a71a47ae2c9efb3b0c92122a07b602e7ad561",
    "edits": [
      SingleEdit {
        "afterStart": 5,
        "afterText": "",
        "beforeStart": 5,
        "beforeText": " world",
      },
    ],
    "path": "test.py",
  },
]
`;

exports[`file-edit-watcher coalescing insertion events 1`] = `
[
  FileEditEvent {
    "afterBlobName": "71e9990455c9e28488a28971b57a71a47ae2c9efb3b0c92122a07b602e7ad561",
    "beforeBlobName": "de93efe107c69ee9550e94e77ec43788b22b9064b2bca0d5d7a93709705a380e",
    "edits": [
      SingleEdit {
        "afterStart": 6,
        "afterText": " world",
        "beforeStart": 6,
        "beforeText": "",
      },
    ],
    "path": "test.py",
  },
]
`;

exports[`file-edit-watcher coalescing multi-insertion events 1`] = `
[
  FileEditEvent {
    "afterBlobName": "4f35e5347e839f65a2776d5d8f4eb63aace92643a3743141b39e55462b2f4739",
    "beforeBlobName": "74e784ed5234a5ba30c2d5f75eacd4f72aafd7a49d97ea6ad790f8ff8f40dff9",
    "edits": [
      SingleEdit {
        "afterStart": 1,
        "afterText": "bc",
        "beforeStart": 1,
        "beforeText": "",
      },
      SingleEdit {
        "afterStart": 6,
        "afterText": "cb",
        "beforeStart": 4,
        "beforeText": "",
      },
    ],
    "path": "test.py",
  },
]
`;

exports[`file-edit-watcher handles previous text correctly based off document open events 1`] = `
[
  FileEditEvent {
    "afterBlobName": "f0ceba4e946515e9b63e791888cba731672f5a39ede9fd3b37bbb0ef4ab1264a",
    "beforeBlobName": "4f12527a02b3b5a02996627e4c393093835f66d09167c8c89d481e3f62d78d91",
    "edits": [
      SingleEdit {
        "afterStart": 0,
        "afterText": "",
        "beforeStart": 0,
        "beforeText": "test2 file",
      },
    ],
    "path": "test2.py",
  },
]
`;

exports[`file-edit-watcher sanity 1`] = `
[
  FileEditEvent {
    "afterBlobName": "71e9990455c9e28488a28971b57a71a47ae2c9efb3b0c92122a07b602e7ad561",
    "beforeBlobName": "de93efe107c69ee9550e94e77ec43788b22b9064b2bca0d5d7a93709705a380e",
    "edits": [
      SingleEdit {
        "afterStart": 6,
        "afterText": " world",
        "beforeStart": 6,
        "beforeText": "",
      },
    ],
    "path": "test.py",
  },
]
`;
