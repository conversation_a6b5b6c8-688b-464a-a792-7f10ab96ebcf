import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import path from "path";
import * as vscode from "vscode";

import * as mockFSUtils from "../../__mocks__/fs-utils";
import {
    emptyTextDocumentChangeEvent,
    MutableTextDocument,
    MutableTextDocumentChangeBuilder,
    publishOpenTextDocument,
    publishTextDocumentChange,
} from "../../__mocks__/vscode-mocks";
import { FileType } from "../../utils/types";
import { FileEditEventsWatcher } from "../file-edit-events";
import {
    FileEditEventsStoreImpl,
    FileEditEventsStoreNoop,
} from "../file-edit-events/file-edit-events-store";

function getMockWatcher(maxBlobSize: number = 1000): FileEditEventsWatcher {
    const watcher = new FileEditEventsWatcher(
        "test",
        new BlobNameCalculator(1000),
        maxBlobSize,
        new FileEditEventsStoreNoop()
    );
    listenOnEvents(watcher);
    return watcher;
}

function listenOnEvents(watcher: FileEditEventsWatcher) {
    vscode.workspace.onDidChangeTextDocument((event) => {
        watcher.handleChangedDocument({
            folderId: 0,
            relPath: path.relative(__dirname, event.document.uri.fsPath),
            event: event,
        });
    });

    vscode.workspace.onDidOpenTextDocument((document) => {
        watcher.handleOpenedDocument({
            folderId: 0,
            relPath: path.relative(__dirname, document.uri.fsPath),
            document: document,
        });
    });

    vscode.workspace.onDidCloseTextDocument((document) => {
        watcher.handleClosedDocument({
            folderId: 0,
            relPath: path.relative(__dirname, document.uri.fsPath),
            document: document,
        });
    });
}

describe("file-edit-watcher", () => {
    test("sanity", () => {
        const blobNameCalculator = new BlobNameCalculator(1000);
        const watcher = getMockWatcher();

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "hello"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () => document.insert(6, " world"),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }
        expect(watcher.getEvents()).toMatchSnapshot();

        // Check that the first blob name is recorded correctly.
        // On the first time we get an event on the file, we should have an empty event
        // but still record the correct text
        expect(watcher.getEvents()[0].beforeBlobName).toBe(
            blobNameCalculator.calculateNoThrow("test.py", "hello")
        );

        watcher.clear({ clearLastKnown: true });

        expect(watcher.getEvents()).toEqual([]);
    });

    test("clearing without last known text will pick up on the next change", () => {
        // const blobNameCalculator = new BlobNameCalculator(1000);
        const watcher = getMockWatcher();

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "hello"
        );

        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () => document.insert(6, " world"),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }

        watcher.clear({ clearLastKnown: false });
        expect(watcher.getEvents()).toEqual([]);

        const afterClearActions = [() => document.insert(12, "!")];
        for (const action of afterClearActions) {
            publishTextDocumentChange(action());
        }
        expect(watcher.getEvents()[0].edits[0].afterText).toEqual("!");
    });
    /**
     * The following tests shows that sequential events are coalesced into a single event.
     */
    test("coalescing insertion events", () => {
        const watcher = getMockWatcher();

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "hello"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () => document.insert(6, " "),
            () => document.insert(7, "w"),
            () => document.insert(8, "o"),
            () => document.insert(9, "rl"), // checks we support coalescing of multiple character edits.
            () => document.insert(11, "d"),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }
        expect(watcher.getEvents()).toMatchSnapshot();
    });

    /**
     * The following tests shows that sequential events are coalesced into a single event.
     */
    test("coalescing multi-insertion events", () => {
        const watcher = getMockWatcher();

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "aaaaaa"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () =>
                document.multiInsert([
                    [1, "b"],
                    [3, "b"],
                ]),
            () =>
                document.multiInsert([
                    [2, "c"],
                    [4, "c"],
                ]),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }

        expect(watcher.getEvents()).toMatchSnapshot();
    });

    test("coalescing deletion events", () => {
        const watcher = getMockWatcher();

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "hello world"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () => document.delete(10, 1),
            () => document.delete(8, 2),
            () => document.delete(7, 1),
            () => document.delete(6, 1), // checks we support coalescing of multiple character edits.
            () => document.delete(5, 1),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }
        expect(watcher.getEvents()).toMatchSnapshot();
    });

    test("coalescing canceling events", () => {
        const watcher = getMockWatcher();

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "hello world"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document), // 'hello world'
            () => document.delete(0, 5), // ' world'
            () => document.insert(0, "goodbye"), // 'goodbye world'
            () => document.delete(0, 6), // 'e world'
            () => document.insert(0, "h"), // 'he world'
            () => document.insert(2, "llo!"), // 'hello! world'
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }
        expect(watcher.getEvents()).toMatchSnapshot();
    });

    test("removes events if file was deleted", () => {
        const watcher = getMockWatcher();

        const test1File = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test1.py")),
            "hello world"
        );
        const test2File = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test2.py")),
            "hello world"
        );
        const test3File = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "tes3.py")),
            "hello world"
        );
        const actions = (document: MutableTextDocument) => {
            return [
                () => emptyTextDocumentChangeEvent(document), // 'hello world'
                () => document.delete(0, 5), // ' world'
                () => document.insert(0, "goodbye"), // 'goodbye world'
                () => document.delete(0, 6), // 'e world'
                () => document.insert(0, "h"), // 'he world'
                () => document.insert(2, "llo!"), // 'hello! world'
            ];
        };
        for (const document of [
            test1File,
            test2File,
            test3File,
            test1File,
            test2File,
            test3File,
            test1File,
            test2File,
            test3File,
        ]) {
            for (const action of actions(document)) {
                publishTextDocumentChange(action());
            }
        }
        expect(watcher.getEvents().map((e) => e.path)).toEqual([
            "test1.py",
            "test2.py",
            "tes3.py",
            "test1.py",
            "test2.py",
            "tes3.py",
            "test1.py",
            "test2.py",
            "tes3.py",
        ]);
        watcher.handleFileDeleted({
            folderId: 0,
            relPath: "test2.py",
            qualifiedPathName: {
                rootPath: "",
                relPath: "test2.py",
            },
        });
        expect(watcher.getEvents().map((e) => e.path)).toEqual([
            "test1.py",
            "tes3.py",
            "test1.py",
            "tes3.py",
            "test1.py",
            "tes3.py",
        ]);
        watcher.handleFileDeleted({
            folderId: 0,
            relPath: "test1.py",
            qualifiedPathName: {
                rootPath: "",
                relPath: "test1.py",
            },
        });
        expect(watcher.getEvents().map((e) => e.path)).toEqual(["tes3.py", "tes3.py", "tes3.py"]);
        watcher.handleFileDeleted({
            folderId: 0,
            relPath: "tes3.py",
            qualifiedPathName: {
                rootPath: "",
                relPath: "tes3.py",
            },
        });
        expect(watcher.getEvents()).toEqual([]);
    });

    test("updates data if file was renamed", () => {
        const watcher = getMockWatcher();
        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test1.py")),
            "hello world"
        );

        const actions = [
            () => emptyTextDocumentChangeEvent(document), // 'hello world'
            () => document.delete(0, 5), // ' world'
        ];

        for (const action of actions) {
            publishTextDocumentChange(action());
        }

        expect(watcher.getEvents().map((e) => e.path)).toEqual(["test1.py"]);

        watcher.handleFileWillRename({
            folderId: 0,
            oldRelPath: "test1.py",
            newRelPath: "test2.py",
            type: FileType.file,
        });
        expect(watcher.getEvents().map((e) => e.path)).toEqual(["test2.py"]);
    });

    test("updates data if folder was renamed", () => {
        const watcher = getMockWatcher();
        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "lib/test1.py")),
            "hello world"
        );

        const actions = [
            () => emptyTextDocumentChangeEvent(document), // 'hello world'
            () => document.delete(0, 5), // ' world'
        ];

        for (const action of actions) {
            publishTextDocumentChange(action());
        }

        expect(watcher.getEvents().map((e) => e.path)).toEqual(["lib/test1.py"]);

        watcher.handleFileWillRename({
            folderId: 0,
            oldRelPath: "lib",
            newRelPath: "src",
            type: FileType.directory,
        });
        expect(watcher.getEvents().map((e) => e.path)).toEqual(["src/test1.py"]);
    });

    /**
     * The following test shows that when multiple edits occur in the same file
     * The "after_start" is calculated correctly.
     *
     * As an example: Lets assume someone selected 3 lines and commented them out.
     */
    test("calculates offsets correctly", () => {
        const watcher = getMockWatcher();

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "line1\nline2\nline3"
        );
        const changesToApply = new MutableTextDocumentChangeBuilder()
            .insert(0, "// ")
            .insert(6, "// ")
            .insert(12, "// ")
            .build();

        publishTextDocumentChange(emptyTextDocumentChangeEvent(document));
        const event = document.applyChanges(changesToApply);
        publishTextDocumentChange(event);

        expect(watcher.getEvents()).toMatchSnapshot();
    });

    it("should ignore notebook events", () => {
        const watcher = getMockWatcher();

        // Note: We are using TextDocument because vscode fires 2 different types of events for notebooks.
        // Notebook events are something different - which we don't need for this test as we don't listen on those.
        // We only care about TextDocument events fired for notebook files.
        const document = new MutableTextDocument(
            vscode.Uri.from({ scheme: "vscode-notebook-cell", path: "test.py" }),
            "hello"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () => document.insert(6, " world"),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }

        expect(watcher.getEvents()).toEqual([]);
    });

    it("handles previous text correctly based off document open events", () => {
        const watcher = getMockWatcher();
        const doc1 = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test1.py")),
            "test1 file"
        );

        const doc2 = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test2.py")),
            "test2 file"
        );

        // we are interested to see "before text" is maintained, so lets delete content from the file.
        publishOpenTextDocument(doc1);

        publishOpenTextDocument(doc2);
        publishTextDocumentChange(doc2.delete(0, 10));

        expect(watcher.getEvents()).toMatchSnapshot();
    });

    it("respects max blob size", () => {
        const maxBlobSize = 8;
        const watcher = getMockWatcher(maxBlobSize);

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "snake"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () => document.insert(0, "_"),
            () => document.insert(2, "_"),
            () => document.insert(4, "_"), // checks we support coalescing of multiple character edits.
            () => document.insert(6, "_"),
            () => document.insert(8, "_"),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }
        expect(watcher.getEvents().length).toEqual(3); // Last 2 events should not be recorded
    });

    it("stores and loads events correctly", async () => {
        mockFSUtils.makeDirs("test-store");
        const watcher = new FileEditEventsWatcher(
            "test",
            new BlobNameCalculator(1000),
            1000,
            new FileEditEventsStoreImpl({ directory: "test-store" })
        );
        listenOnEvents(watcher);

        const document = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test.py")),
            "snake"
        );
        const actions = [
            () => emptyTextDocumentChangeEvent(document),
            () => document.insert(0, "_"),
            () => document.insert(2, "_"),
            () => document.insert(4, "_"), // checks we support coalescing of multiple character edits.
            () => document.insert(6, "_"),
            () => document.insert(8, "_"),
        ];
        for (const action of actions) {
            publishTextDocumentChange(action());
        }
        expect(watcher.getEvents().length).toEqual(5); // prerequirement check

        watcher.dispose();

        const watcher2 = new FileEditEventsWatcher(
            "test",
            new BlobNameCalculator(1000),
            1000,
            new FileEditEventsStoreImpl({ directory: "test-store" })
        );
        listenOnEvents(watcher2);

        expect(mockFSUtils.fileExists("test-store/file-edit-events.json")).toBe(true);

        await watcher2.loadEvents();

        expect(watcher2.getEvents().length).toEqual(5); // loaded events check
    });
});
