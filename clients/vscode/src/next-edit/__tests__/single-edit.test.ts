import { SingleEdit } from "../file-edit-events";

describe("SingleEdit.normalize()", () => {
    it("should leave already normalized events unchanged", () => {
        const event1 = new SingleEdit({
            beforeStart: 0,
            afterStart: 0,
            beforeText: "a",
            afterText: "b",
        });
        expect(event1.normalize()).toEqual(event1);

        const event2 = new SingleEdit({
            beforeStart: 5,
            afterStart: 7,
            beforeText: "a",
            afterText: "bcd",
        });
        expect(event2.normalize()).toEqual(event2);
    });

    it("should remove common prefix", () => {
        const event1 = new SingleEdit({
            beforeStart: 0,
            afterStart: 0,
            beforeText: "abc",
            afterText: "abxy",
        });
        expect(event1.normalize()).toEqual(
            new SingleEdit({
                beforeStart: 2,
                afterStart: 2,
                beforeText: "c",
                afterText: "xy",
            })
        );

        const bigEvent = new SingleEdit({
            beforeStart: 0,
            afterStart: 0,
            beforeText: "_".repeat(10000) + "abc",
            afterText: "_".repeat(10000) + "xyz",
        });
        expect(bigEvent.normalize()).toEqual(
            new SingleEdit({
                beforeStart: 10000,
                afterStart: 10000,
                beforeText: "abc",
                afterText: "xyz",
            })
        );
    });

    it("should remove common suffix", () => {
        const event1 = new SingleEdit({
            beforeStart: 1,
            afterStart: 2,
            beforeText: "hello abc",
            afterText: "bye abc",
        });
        expect(event1.normalize()).toEqual(
            new SingleEdit({
                beforeStart: 1,
                afterStart: 2,
                beforeText: "hello",
                afterText: "bye",
            })
        );

        const bigEvent = new SingleEdit({
            beforeStart: 0,
            afterStart: 0,
            beforeText: "abc" + "_".repeat(10000),
            afterText: "xyz" + "_".repeat(10000),
        });
        expect(bigEvent.normalize()).toEqual(
            new SingleEdit({
                beforeStart: 0,
                afterStart: 0,
                beforeText: "abc",
                afterText: "xyz",
            })
        );
    });

    it("should remove common prefix and suffix", () => {
        const event1 = new SingleEdit({
            beforeStart: 1,
            afterStart: 2,
            beforeText: "abc hello xyz",
            afterText: "abc world xyz",
        });
        expect(event1.normalize()).toEqual(
            new SingleEdit({
                beforeStart: 5,
                afterStart: 6,
                beforeText: "hello",
                afterText: "world",
            })
        );

        const bigEvent = new SingleEdit({
            beforeStart: 0,
            afterStart: 0,
            beforeText: "_".repeat(10000) + "abc" + "_".repeat(10000),
            afterText: "_".repeat(10000) + "xyz" + "_".repeat(10000),
        });
        expect(bigEvent.normalize()).toEqual(
            new SingleEdit({
                beforeStart: 10000,
                afterStart: 10000,
                beforeText: "abc",
                afterText: "xyz",
            })
        );
    });
});

describe("SingleEdit.mergeNext()", () => {
    it("should merge adjacent events", () => {
        const addEvent1 = new SingleEdit({
            beforeStart: 3,
            afterStart: 3,
            beforeText: "",
            afterText: "hello",
        });

        const addEvent2 = new SingleEdit({
            beforeStart: 8,
            afterStart: 8,
            beforeText: "",
            afterText: " world",
        });

        // addEvent2 is right-adjacent to addEvent1
        expect(addEvent1.mergeNext(addEvent2)).toEqual(
            new SingleEdit({
                beforeStart: 3,
                afterStart: 3,
                beforeText: "",
                afterText: "hello world",
            })
        );

        const replaceEvent = new SingleEdit({
            beforeStart: 0,
            afterStart: 0,
            beforeText: "is ",
            afterText: "I say: ",
        });
        // replaceEvent is left-adjacent to addEvent1
        expect(addEvent1.mergeNext(replaceEvent)).toEqual(
            new SingleEdit({
                beforeStart: 0,
                afterStart: 0,
                beforeText: "is ",
                afterText: "I say: hello",
            })
        );
    });

    it("should merge an in-range deletion is allowed as a special case", () => {
        const addEvent = new SingleEdit({
            beforeStart: 3,
            afterStart: 3,
            beforeText: "",
            afterText: "hallo",
        });
        const deleteEvent = new SingleEdit({
            beforeStart: 4,
            afterStart: 4,
            beforeText: "a",
            afterText: "",
        });
        // merge an in-range deletion is allowed as a special case
        expect(addEvent.mergeNext(deleteEvent)).toEqual(
            new SingleEdit({
                beforeStart: 3,
                afterStart: 3,
                beforeText: "",
                afterText: "hllo",
            })
        );
    });

    it("should not merge non-adjacent edits", () => {
        const addEvent = new SingleEdit({
            beforeStart: 3,
            afterStart: 3,
            beforeText: "",
            afterText: "hallo",
        });

        const replaceEvent = new SingleEdit({
            beforeStart: 4,
            afterStart: 4,
            beforeText: "a",
            afterText: "eee",
        });

        expect(addEvent.mergeNext(replaceEvent)).toBe(undefined);
    });
});
