import { FileEditEvent, SingleEdit } from "../file-edit-events";

describe("file-edit-events", () => {
    describe("#mergeNext()", () => {
        it("should merge multi-edit events if they are repeated changes", () => {
            const event1 = new FileEditEvent({
                path: "test.py",
                edits: [
                    new SingleEdit({
                        beforeStart: 5,
                        afterStart: 5,
                        beforeText: "",
                        afterText: "hello",
                    }),
                    new SingleEdit({
                        beforeStart: 10,
                        afterStart: 15, // 5 + len("hello")
                        beforeText: "",
                        afterText: "world",
                    }),
                ],
                beforeBlobName: "",
                afterBlobName: "",
            });
            const event2 = new FileEditEvent({
                path: "test.py",
                edits: [
                    new SingleEdit({
                        beforeStart: 10,
                        afterStart: 10, // 5 + len("hello")
                        beforeText: "",
                        afterText: "!!",
                    }),
                    new SingleEdit({
                        beforeStart: 20, // 15 + len("world")
                        afterStart: 22, // beforeStart + len("!!")
                        beforeText: "",
                        afterText: "!!",
                    }),
                ],
                beforeBlobName: "",
                afterBlobName: "",
            });
            let merged = event1.mergeNext(event2);
            expect(merged).toEqual(
                new FileEditEvent({
                    path: "test.py",
                    edits: [
                        new SingleEdit({
                            beforeStart: 5,
                            afterStart: 5,
                            beforeText: "",
                            afterText: "hello!!",
                        }),
                        new SingleEdit({
                            beforeStart: 10,
                            afterStart: 17, // beforeStart + len("hello!!")
                            beforeText: "",
                            afterText: "world!!",
                        }),
                    ],
                    beforeBlobName: "",
                    afterBlobName: "",
                })
            );

            // now delete the special characters
            const event3 = new FileEditEvent({
                path: "test.py",
                edits: [
                    new SingleEdit({
                        beforeStart: 10,
                        afterStart: 10,
                        beforeText: "!!",
                        afterText: "",
                    }),
                    new SingleEdit({
                        beforeStart: 22,
                        afterStart: 20, // beforeStart - len("!!")
                        beforeText: "!!",
                        afterText: "",
                    }),
                ],
                beforeBlobName: "",
                afterBlobName: "",
            });
            merged = merged!.mergeNext(event3);

            expect(merged).toEqual(
                new FileEditEvent({
                    path: "test.py",
                    edits: [
                        new SingleEdit({
                            beforeStart: 5,
                            afterStart: 5,
                            beforeText: "",
                            afterText: "hello",
                        }),
                        new SingleEdit({
                            beforeStart: 10,
                            afterStart: 15, // beforeStart + len("hello")
                            beforeText: "",
                            afterText: "world",
                        }),
                    ],
                    beforeBlobName: "",
                    afterBlobName: "",
                })
            );

            // if the multiple edits don't match, we shouldn't merge
            const event4 = new FileEditEvent({
                path: "test.py",
                edits: [
                    new SingleEdit({
                        beforeStart: 10,
                        afterStart: 10, // 5 + len("hello")
                        beforeText: "",
                        afterText: "abc",
                    }),
                    new SingleEdit({
                        beforeStart: 20, // 15 + len("world")
                        afterStart: 22, // beforeStart + len("!!")
                        beforeText: "",
                        afterText: "def",
                    }),
                ],
                beforeBlobName: "",
                afterBlobName: "",
            });

            expect(merged!.mergeNext(event4)).toEqual(undefined);
        });
    });
});
