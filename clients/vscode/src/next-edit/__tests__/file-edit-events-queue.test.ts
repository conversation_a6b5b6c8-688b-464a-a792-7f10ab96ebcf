import { FileEditEvent, FileEditEventsQueue, SingleEdit } from "../file-edit-events";

describe("file-edit-events-queue", () => {
    it("should keep track of the size", () => {
        const SIZE_LIMIT = 20;
        const queue = new FileEditEventsQueue(SIZE_LIMIT);

        const event = new FileEditEvent({
            path: "test.py",
            edits: [
                new SingleEdit({
                    beforeStart: 0,
                    afterStart: 2,
                    beforeText: "",
                    afterText: "helloworld",
                }),
            ],
            beforeBlobName: "before",
            afterBlobName: "after",
        });
        const sizeSum = (events: FileEditEvent[]) =>
            events.reduce((acc, cur) => acc + cur.changedChars(), 0);

        expect(sizeSum(queue.getEvents())).toBe(0);

        queue.addEvent(event);
        expect(sizeSum(queue.getEvents())).toBe(10);

        queue.addEvent(event);
        expect(sizeSum(queue.getEvents())).toBe(20);

        // now this new event cannot be merged with the last event since the resulting
        // event would be too large.
        queue.addEvent(event);
        expect(sizeSum(queue.getEvents())).toBe(20);
    });

    describe("#removeEventsPriorToBlob", () => {
        it("(au-8888) should remove events prior to specific blob", () => {
            /*
             * In this test file_reconstruction_test was fully indexed.
             * The last event's "afterBlob" should be the indexed version.
             * After removal we expect all events to be removed.
             */
            const events = [
                new FileEditEvent({
                    afterBlobName:
                        "6048976958e889a03e41e93c9659bd6cbb53876a0d0976b6fb199d46518f9875",
                    beforeBlobName:
                        "5b6661b0bfee829c2d39e8d839fc4ac2513f55100c9f99e109353ddcf66079b6",
                    edits: [
                        new SingleEdit({
                            afterStart: 1338,
                            afterText: "pytest",
                            beforeStart: 1338,
                            beforeText: "",
                        }),
                    ],
                    path: "services/next_edit_host/server/file_reconstruction_test.py",
                }),
                new FileEditEvent({
                    afterBlobName:
                        "071ad7b4e784270b3aa14c390093944d27af7950bdd58b74df522d8ef9eedfc3",
                    beforeBlobName:
                        "6048976958e889a03e41e93c9659bd6cbb53876a0d0976b6fb199d46518f9875",
                    edits: [
                        new SingleEdit({
                            afterStart: 1337,
                            afterText: "",
                            beforeStart: 1337,
                            beforeText: "@pytest",
                        }),
                    ],
                    path: "services/next_edit_host/server/file_reconstruction_test.py",
                }),
                new FileEditEvent({
                    afterBlobName:
                        "a47110cef7af8cd3a2f636c2911b99e2bf0b7ed2b83923871f372a7660b80d4b",
                    beforeBlobName:
                        "071ad7b4e784270b3aa14c390093944d27af7950bdd58b74df522d8ef9eedfc3",
                    edits: [
                        new SingleEdit({
                            afterStart: 3413,
                            afterText: "test_",
                            beforeStart: 3413,
                            beforeText: "",
                        }),
                    ],
                    path: "services/next_edit_host/server/file_reconstruction_test.py",
                }),
                new FileEditEvent({
                    afterBlobName:
                        "75fc0df6c0eb99cfcb2f0223d2f63347dbd3476f68cbbe9224e75929aa9e4dc2",
                    beforeBlobName:
                        "5429e072fd1fd1f56bedaf189357dd1891a5de7b850622a22ba407b011237fbb",
                    edits: [
                        new SingleEdit({
                            afterStart: 4028,
                            afterText: "",
                            beforeStart: 4028,
                            beforeText: "# ",
                        }),
                    ],
                    path: "services/next_edit_host/server/file_reconstruction.py",
                }),
            ];
            const queue = new FileEditEventsQueue(1000000);
            events.forEach((event) => queue.addEvent(event));
            queue.removeEventsPriorToBlob(
                "a47110cef7af8cd3a2f636c2911b99e2bf0b7ed2b83923871f372a7660b80d4b"
            );

            const allEvents = queue.getEvents();
            const fileReconstructionTestEvents = allEvents.filter(
                (event) =>
                    event.path === "services/next_edit_host/server/file_reconstruction_test.py"
            );
            expect(fileReconstructionTestEvents).toHaveLength(0);
        });
    });
});
