import { getLogger } from "../../logging";
import { FileEditEvent } from "./file-edit-event";
import { SizeTrackedQueue } from "./sized-queue";

/**
 * A queue of file edit events.
 * In favor of keeping the payload to the backend small
 * we limit the total size of characters collected by the queue.
 *
 * The queue will also merge events if they are one next to another.
 * This is limited to single character edits.
 */
export class FileEditEventsQueue {
    private readonly _logger = getLogger("FileEditEventsQueue");
    constructor(
        public readonly maxQueueSizeChars: number,
        private _queue: SizeTrackedQueue = new SizeTrackedQueue()
    ) {}

    /**
     * @returns the number of characters changed by the user in this event.
     * Negative if the user undid recent change.
     * Takes into account merges.
     * Ignores removal of old events.
     */
    addEvent(event: FileEditEvent, protectedBlobName?: string | undefined): number {
        const lastEvent = this._queue.newest();
        const lastEventProtected = lastEvent?.afterBlobName === protectedBlobName;
        let changeSize = 0;

        const mergedEvent = lastEventProtected ? undefined : lastEvent?.mergeNext(event);
        if (mergedEvent !== undefined) {
            // merge successful, so replace the last event with the merged one
            const removedEvent = this._queue.removeNew();
            changeSize -= removedEvent?.changedChars() ?? 0;
            // if the merged event is empty, just drop it.
            if (mergedEvent.hasChange()) {
                changeSize += this._queue.add(mergedEvent);
            }
        } else {
            // cannot merge, so just add the event to the queue.
            changeSize += this._queue.add(event);
        }

        // now remove older events until they fit under the limit.
        while (this._queue.sizeChars > this.maxQueueSizeChars) {
            this._queue.removeOld();
        }
        return changeSize;
    }

    removeEventsForFile(relPath: string) {
        // use reverse iteration to avoid messing up the index
        for (let i = this._queue.numEvents - 1; i >= 0; i--) {
            const event = this._queue.at(i);
            if (event !== undefined && event.path === relPath) {
                this._queue.removeAt(i);
            }
        }
    }

    removeEventsPriorToBlob(blobName: string) {
        let path = null;
        const lengthBefore = this._queue.numEvents;
        // Iterate backwards to avoid index shifting issues
        for (let i = this._queue.numEvents - 1; i >= 0; i--) {
            const event = this._queue.at(i);
            if (!path && event?.afterBlobName === blobName) {
                this._queue.removeAt(i);
                path = event.path;
                continue;
            }

            if (path && event?.path === path) {
                this._queue.removeAt(i);
            }
        }
        this._logger.debug(
            `Removed ${lengthBefore - this._queue.numEvents} events prior to ${blobName}`
        );
    }

    // TODO(au-5953): fix this.. currenlty path and blob names are out of sync.
    // We should
    // 1. Not update paths
    // 2. Add an event for the rename (before path) and with updated blob name (before & after)
    // 3. Add handling for this in the backend
    updatePath(oldPath: string, newPath: string) {
        for (let i = 0; i < this._queue.numEvents; i++) {
            const event = this._queue.at(i);
            if (event !== undefined && event.path === oldPath) {
                event.path = newPath;
            }
        }
    }

    getEvents() {
        return this._queue.asArray();
    }

    clear() {
        this._queue.clear();
    }
}
