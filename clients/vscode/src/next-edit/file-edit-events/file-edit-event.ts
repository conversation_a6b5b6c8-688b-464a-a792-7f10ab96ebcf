import { <PERSON>r<PERSON><PERSON><PERSON> } from "../../utils/ranges";
import { SingleEdit } from "./single-edit";

export class FileEditEvent {
    /** The path of the file that was edited. */
    public path: string;
    /** The blob name before the edits. */
    public beforeBlobName: string;
    /** The blob name after the edits. */
    public afterBlobName: string;
    /** The concurrent edits that were made to the file in this event. */
    public edits: SingleEdit[];

    constructor(args: {
        path: string;
        beforeBlobName: string;
        afterBlobName: string;
        edits: SingleEdit[];
    }) {
        this.path = args.path;
        this.beforeBlobName = args.beforeBlobName;
        this.afterBlobName = args.afterBlobName;
        this.edits = args.edits;
    }

    // this is a naive implementation of "class-transformer"
    static from(data: Partial<FileEditEvent>): FileEditEvent {
        const instance = new FileEditEvent({
            path: data.path ?? "",
            beforeBlobName: data.beforeBlobName ?? "",
            afterBlobName: data.afterBlobName ?? "",
            edits: data.edits?.map((edit) => SingleEdit.from(edit)) ?? [],
        });
        return instance;
    }

    /**
     * Returns the total number of characters changed by this event.
     */
    changedChars(): number {
        return this.edits.reduce((acc, cur) => {
            return acc + cur.beforeText.length + cur.afterText.length;
        }, 0);
    }

    /**
     * Returns true only if all edits contain the same change.
     */
    isRepeatedChange(): boolean {
        if (this.edits.length <= 1) {
            return true;
        }
        const firstEdit = this.edits[0];
        return this.edits.every(
            (edit) =>
                edit.beforeText === firstEdit.beforeText && edit.afterText === firstEdit.afterText
        );
    }

    /** Try to merge this singe edit event with the next event. Returns undefined if
     * they cannot be merged. */
    mergeNext(next: FileEditEvent): FileEditEvent | undefined {
        if (this.edits.length !== next.edits.length) {
            // only merge events with the same number of edits.
            return undefined;
        }
        if (this.path !== next.path) {
            // only merge events from the same file.
            return undefined;
        }
        if (!next.isRepeatedChange()) {
            // only merge if the next event is a repeated change.
            // otherwise, we may end up with overlaping ranges.
            return undefined;
        }
        const newEdits: SingleEdit[] = [];
        // if an earlier event's size has changed due to the merge, we need to update
        // the offsets of later events accordingly.
        let beforeOffset = 0;
        let afterOffset = 0;
        for (let i = 0; i < this.edits.length; i++) {
            const thisEdit = this.edits[i];
            const merged = thisEdit.mergeNext(next.edits[i]);
            if (merged === undefined) {
                return undefined;
            }
            newEdits.push(
                new SingleEdit({
                    beforeStart: merged.beforeStart + beforeOffset,
                    afterStart: merged.afterStart + afterOffset,
                    beforeText: merged.beforeText,
                    afterText: merged.afterText,
                })
            );
            beforeOffset += merged.beforeText.length - thisEdit.beforeText.length;
            afterOffset += merged.afterText.length - thisEdit.afterText.length;
        }
        // Check if the ranges are disjoint. If not, we cannot merge.
        if (newEdits.length >= 2) {
            const beforeRanges = newEdits.map((edit) => edit.beforeCRange);
            if (CharRange.anyOverlaps(beforeRanges)) {
                return undefined;
            }
            const afterRanges = newEdits.map((edit) => edit.afterCRange);
            if (CharRange.anyOverlaps(afterRanges)) {
                return undefined;
            }
        }
        return new FileEditEvent({
            path: this.path,
            beforeBlobName: this.beforeBlobName,
            afterBlobName: next.afterBlobName,
            edits: newEdits,
        }).normalize();
    }

    /** Try to shrink the size of this edit event by normalizing each single edit.
     * This should help in case where we get a gigantic VSCode editing event but the
     * actual changed part between beforeText and afterText is very small.
     */
    normalize(): FileEditEvent {
        const edits = this.edits
            .map((edit) => edit.normalize())
            .filter((edit) => edit.beforeText !== edit.afterText)
            .sort((a, b) => a.beforeStart - b.beforeStart);
        return new FileEditEvent({
            path: this.path,
            beforeBlobName: this.beforeBlobName,
            afterBlobName: this.afterBlobName,
            edits: edits,
        });
    }

    hasChange(): boolean {
        return this.edits.some((edit) => edit.beforeText !== edit.afterText);
    }
}
