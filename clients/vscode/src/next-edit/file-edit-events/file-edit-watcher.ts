import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import * as vscode from "vscode";

import { getLogger } from "../../logging";
import { DisposableService } from "../../utils/disposable-service";
import * as pathUtils from "../../utils/path-utils";
import { FileType } from "../../utils/types";
import * as workspaceEvents from "../../workspace/workspace-events";
import { FileEditEvent } from "./file-edit-event";
import { FileEditEventsQueue } from "./file-edit-events-queue";
import { IFileEditEventsStore } from "./file-edit-events-store";
import { SingleEdit } from "./single-edit";
import { ClearOptions, SUPPORTED_SCHEMES } from "./types";

/**
 * Collect more granular file edit events.
 * It assumes a single source folder.
 * It does not handle file renames nicely.
 */
export class FileEditEventsWatcher extends DisposableService {
    private _eventsQueue: FileEditEventsQueue;
    private _lastKnownText: Map<string, string> = new Map(); // relPath to text
    private _lastEventTimestamp: number = 0;
    private _logger;

    constructor(
        private _folderName: string,
        private _blobNameCalculator: BlobNameCalculator,
        private _maxBlobSizeBytes: number,
        private readonly _store: IFileEditEventsStore,
        public readonly maxEventCharsToReturn: number = 5000
    ) {
        super();
        this._logger = getLogger(`FileEditEventsWatcher[${this._folderName}]`);
        // We store twice as many events in the queue in case some events are deleted
        // later when merging with newer events.
        // This is motivated by the worst case where the user pasted in
        // maxEventCharsToReturn characters of changes and then immediately revert it.
        // We would then merge the two events together, which would be an empty event
        // which will get dropped. If we only store maxEventCharsToReturn chars of
        // changes in the queue, we will now have an empty list of events to send to
        // the front end. However, if we store 2*maxEventCharsToReturn events in the
        // queue, after the merging, we would still have maxEventCharsToReturn events.
        this._eventsQueue = new FileEditEventsQueue(this.maxEventCharsToReturn * 2);
    }

    private _swapLastKnownText(relPath: string, text: string): string {
        if (!this._lastKnownText.has(relPath)) {
            throw new Error(`no known text for [${relPath}]`);
        }

        const lastText = this._lastKnownText.get(relPath);
        this._lastKnownText.set(relPath, text);
        return lastText!;
    }

    private _vscodeEventToFileEditEvent(
        relPath: string,
        event: vscode.TextDocumentChangeEvent
    ): FileEditEvent {
        const text = event.document.getText();
        const previousText = this._swapLastKnownText(relPath, text);

        // first make edits from VSCode edit events
        let edits = event.contentChanges.map(
            (change: vscode.TextDocumentContentChangeEvent) =>
                new SingleEdit({
                    beforeStart: change.rangeOffset,
                    afterStart: change.rangeOffset, // to be updated later.
                    beforeText: previousText.substring(
                        change.rangeOffset,
                        change.rangeOffset + change.rangeLength
                    ),
                    afterText: change.text,
                })
        );

        // then fix the offsets for multi-edit events
        if (edits.length > 1) {
            edits.sort((a, b) => a.beforeStart - b.beforeStart);
            let offset: number = 0;
            edits = edits.map((edit) => {
                const newAfterStart = edit.afterStart + offset;
                offset += edit.afterText.length - edit.beforeText.length;
                return new SingleEdit({
                    beforeStart: edit.beforeStart,
                    afterStart: newAfterStart,
                    beforeText: edit.beforeText,
                    afterText: edit.afterText,
                });
            });
        }

        return new FileEditEvent({
            path: relPath,
            edits: edits,
            beforeBlobName: this._blobNameCalculator.calculateNoThrow(relPath, previousText),
            afterBlobName: this._blobNameCalculator.calculateNoThrow(relPath, text),
        }).normalize();
    }

    public handleChangedDocument(folderEvent: workspaceEvents.FolderTextDocumentChangedEvent) {
        const relPath = folderEvent.relPath;
        const event = folderEvent.event;

        if (!SUPPORTED_SCHEMES.includes(event.document.uri.scheme)) {
            // DO NOT PRINT HERE.. in VSCode the logs are also a file (with scheme = "output")
            // If you log here, it will cause an endless feedback when the logs panel is open.
            return;
        }
        // We assume file is utf8 since it is being edited.
        // This is a crude assumption and we might want to do something better here.
        if (event.document.getText().length > this._maxBlobSizeBytes) {
            this._logger.debug(`Ignoring event for ${relPath} because it is too large`);
            return;
        }
        if (!this._lastKnownText.has(relPath)) {
            this._logger.debug(
                event.contentChanges.length > 0
                    ? `Last known text is not for the same file. Missing last known text for [${relPath}].  This is ok if we have recently cleared.`
                    : `Updating last known text for ${relPath} - based on empty event`
            );
            this._lastKnownText.set(relPath, event.document.getText());
            return;
        }
        if (event.contentChanges.length === 0) {
            this._logger.verbose(`Ignoring event for ${relPath} - no content changes`);
            return;
        }
        const fileEvent = this._vscodeEventToFileEditEvent(relPath, event);
        if (fileEvent.hasChange()) {
            this._eventsQueue.addEvent(fileEvent);
            this._lastEventTimestamp = Date.now();
        }
    }

    get lastEventTimestamp(): number {
        return this._lastEventTimestamp;
    }

    public handleOpenedDocument(event: workspaceEvents.FolderTextDocumentOpenedEvent) {
        if (!SUPPORTED_SCHEMES.includes(event.document.uri.scheme)) {
            // DO NOT PRINT HERE.. in VSCode the logs are also a file (with scheme = "output")
            // If you log here, it will cause an endless feedback when the logs panel is open.
            return;
        }
        // We assume file is utf8 since it is being edited.
        // This is a crude assumption and we might want to do something better here.
        if (event.document.getText().length > this._maxBlobSizeBytes) {
            this._logger.debug(`Ignoring event for ${event.relPath} because it is too large`);
            return;
        }
        this._logger.verbose(
            `Adding last known text for ${event.relPath}. size before = ${this._lastKnownText.size}`
        );
        this._lastKnownText.set(event.relPath, event.document.getText());
    }

    public handleClosedDocument(event: workspaceEvents.FolderTextDocumentClosedEvent) {
        this._logger.verbose(
            `Removing last known text for ${event.relPath}. size before = ${this._lastKnownText.size}`
        );
        this._lastKnownText.delete(event.relPath);
    }

    getEvents(): FileEditEvent[] {
        // If the queue has more events than maxEventCharsToReturn, we want to return
        // the most recent events. So we reversely go through the events list below.
        const toReturn: FileEditEvent[] = [];
        let totalChars = 0;
        const allEvents = this._eventsQueue.getEvents();
        for (let i = allEvents.length - 1; i >= 0; i--) {
            const event = allEvents[i];
            if (totalChars + event.changedChars() > this.maxEventCharsToReturn) {
                break;
            }
            toReturn.push(event);
            totalChars += event.changedChars();
        }
        toReturn.reverse();
        return toReturn;
    }

    /**
     * @description: delete events for the given file.
     *
     * Currently only handles file deletions.
     * TODO: folder deletions.
     *
     * @param event - The deletion event
     **/
    handleFileDeleted(event: workspaceEvents.FolderFileDeletedEvent) {
        this._logger.debug(`Deleting events for ${event.relPath}`);
        this._lastKnownText.delete(event.relPath);
        this._eventsQueue.removeEventsForFile(event.relPath);
    }

    _handleFileWillRename(oldRelPath: string, newRelPath: string) {
        this._logger.debug(`Renaming events for file [${oldRelPath}] to [${newRelPath}]`);
        if (this._lastKnownText.has(oldRelPath)) {
            this._lastKnownText.set(newRelPath, this._lastKnownText.get(oldRelPath)!);
            this._lastKnownText.delete(oldRelPath);
        }
        this._eventsQueue.updatePath(oldRelPath, newRelPath);
    }

    /**
     * @description handle rename events.
     * Files - update the path of relevant state.
     * Folders - find relevant files and update their relevant state.
     */
    handleFileWillRename(event: workspaceEvents.FolderFileWillRenameEvent) {
        this._logger.debug(
            `Renaming events for file/folder [${event.oldRelPath}] to [${event.newRelPath}]`
        );
        if (event.type === FileType.file) {
            this._handleFileWillRename(event.oldRelPath, event.newRelPath);
        } else if (event.type === FileType.directory) {
            for (const oldRelPath of this._lastKnownText.keys()) {
                if (pathUtils.directoryContainsPath(event.oldRelPath, oldRelPath)) {
                    const newRelPath = event.newRelPath + oldRelPath.slice(event.oldRelPath.length);
                    this._handleFileWillRename(oldRelPath, newRelPath);
                }
            }
        }
    }

    clear(clearOptions: ClearOptions) {
        this._eventsQueue.clear();
        if (clearOptions.clearLastKnown) {
            this._lastKnownText.clear();
        }
        this._lastEventTimestamp = 0;
        // NOTE: we are relying on lastKnownText to get updated by change events.
        // While this might not be ideal, it allows for a simple implementation for now.
        // This however does not take care of documents that are open but will not change. (To be handled in a followup)
        this._store.clear();
    }

    async loadEvents(): Promise<void> {
        const loadedEvents = await this._store.load();
        loadedEvents.forEach((event) => this._eventsQueue.addEvent(FileEditEvent.from(event)));
    }

    dispose() {
        super.dispose();
        this._store.save(this._eventsQueue.getEvents());
        this._logger.debug("Disposing FileEditEventsWatcher");
    }
}
