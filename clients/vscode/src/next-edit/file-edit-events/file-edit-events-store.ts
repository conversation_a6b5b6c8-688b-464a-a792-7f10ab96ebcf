import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";

import { getLogger } from "../../logging";
import * as fsUtils from "../../utils/fs-utils";
import { FileEditEvent } from "./file-edit-event";

type FileEditEventsStoreConfig = {
    directory: string;
};

interface IFileEditEventsStore {
    /**
     * Save events to the store.
     * Overwrites the existing events.
     */
    save(events: FileEditEvent[]): void;
    /**
     * Load events from the store.
     * Returns an empty array if there are no events.
     */
    load(): Promise<FileEditEvent[]>;
    /**
     * Clear all events from the store.
     */
    clear(): void;
}

type FileEditEventsData = {
    version: string;
    events: FileEditEvent[];
};

const STORE_FILENAME = "file-edit-events.json";

class FileEditEventsStoreNoop implements IFileEditEventsStore {
    // used in rollout
    save(_events: FileEditEvent[]): Promise<void> {
        return Promise.resolve();
    }
    load(): Promise<FileEditEvent[]> {
        return Promise.resolve([]);
    }
    clear(): void {}
}

class FileEditEventsStoreImpl implements IFileEditEventsStore {
    private readonly _logger = getLogger("FileEditEventsStore");
    private readonly _version = "1"; // change this when we have a significant data change.
    private readonly _storeFile;

    constructor(_config: FileEditEventsStoreConfig) {
        this._logger.debug(`Using [${_config.directory}] to store events`);
        this._storeFile = joinPath(_config.directory, STORE_FILENAME);
    }

    save(events: FileEditEvent[]): void {
        this._logger.debug(`Saving ${events.length} events to ${this._storeFile}`);
        fsUtils.writeFileUtf8Sync(
            this._storeFile,
            JSON.stringify({ version: this._version, events })
        );
    }
    async load(): Promise<FileEditEvent[]> {
        this._logger.debug(`Loading events from ${this._storeFile}`);

        try {
            if (!fsUtils.fileExists(this._storeFile)) {
                this._logger.debug(`File ${this._storeFile} does not exist. Not loading events.`);
                return [];
            }
            const content = await fsUtils.readFileUtf8(this._storeFile);
            const json = JSON.parse(content) as FileEditEventsData;
            if (json.version !== this._version) {
                this._logger.debug(
                    `Version mismatch: ${json.version} !== ${this._version}. ` +
                        `Not loading events from ${this._storeFile}`
                );
                return [];
            }
            this._logger.debug(`Loaded ${json.events.length} events from ${this._storeFile}`);
            return json.events;
        } catch (e: any) {
            this._logger.debug(`Failed to load events from ${this._storeFile}`, e);
            return [];
        }
    }

    clear(): void {
        void this.save([]);
    }
}

export {
    FileEditEventsStoreConfig,
    IFileEditEventsStore,
    FileEditEventsStoreImpl,
    FileEditEventsStoreNoop,
};
