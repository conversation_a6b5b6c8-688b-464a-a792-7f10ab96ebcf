export enum UriScheme {
    file = "file",
    untitled = "untitled",
    // not listing the rest for now.
}

export type ClearOptions = {
    // If true, will clear the last known text for each open document. This is used to calculate the diff.
    // Set to TRUE in cases where the file is about to change - for example `git stash`.
    // Set to FALSE when file state is expected to remain the same. For example, when we clear from the command palette.
    clearLastKnown: boolean;
};

export const SUPPORTED_SCHEMES = [UriScheme.file.toString(), UriScheme.untitled.toString()];
