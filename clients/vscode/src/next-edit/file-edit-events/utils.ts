export function countCommonPrefixChars(s1: string, s2: string): number {
    const maxCommon = Math.min(s1.length, s2.length);
    const BLOCK = 1024;
    let i = 0;

    // first check one block at a time to make faster progress since
    // JS loops and string accesses are super slow.
    while (i < maxCommon) {
        const s1Block = s1.slice(i, i + BLOCK);
        const s2Block = s2.slice(i, i + BLOCK);
        if (s1Block !== s2Block) {
            break;
        }
        // block is identical, so we advance by the block's size.
        if (s1Block.length === 0) {
            // this guards against infinite loop.
            throw new Error(`unexpected empty block: s1=${s1}, s2=${s2}`);
        }
        i += s1Block.length;
    }
    // now it's safe to check one char at a time since there will be at most
    // one block left to compare
    while (i < maxCommon && s1[i] === s2[i]) {
        i++;
    }
    return i;
}

export function countCommonSuffixChars(s1: string, s2: string): number {
    const maxCommon = Math.min(s1.length, s2.length);
    const BLOCK = 1024;
    let i = 0;

    // first check one block at a time to make faster progress since
    // JS loops and string accesses are super slow.
    while (i < maxCommon) {
        const s1Block = s1.slice(Math.max(0, s1.length - i - BLOCK), s1.length - i);
        const s2Block = s2.slice(Math.max(0, s2.length - i - BLOCK), s2.length - i);
        if (s1Block !== s2Block) {
            break;
        }
        if (s1Block.length === 0) {
            // this guards against infinite loop.
            throw new Error(`unexpected empty block: s1=${s1}, s2=${s2}`);
        }
        // block is identical, so we advance by the block's size.
        i += s1Block.length;
    }
    // now it's safe to check one char at a time since there will be at most
    // one block left to compare
    const s1End = s1.length - 1;
    const s2End = s2.length - 1;
    while (i < maxCommon && s1[s1End - i] === s2[s2End - i]) {
        i++;
    }
    return i;
}
