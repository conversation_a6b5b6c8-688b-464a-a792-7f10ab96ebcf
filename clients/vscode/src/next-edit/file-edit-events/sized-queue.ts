import Denque from "denque";

import { FileEditEvent } from "./file-edit-event";

enum QueueChangeType {
    added = "ADDED",
    removed = "REMOVED",
}

export class SizeTrackedQueue {
    private _events: Denque<FileEditEvent> = new Denque<FileEditEvent>();
    private _queueSizeChars = 0;
    constructor() {}

    /** Add a new event to the queue. */
    add(event: FileEditEvent): number {
        this._events.push(event);
        return this._updateState(event, QueueChangeType.added);
    }

    // returns the number of characters added or removed from the queue.
    _updateState(event: FileEditEvent | undefined, changeType: QueueChangeType): number {
        if (event === undefined) {
            return 0;
        }
        if (changeType === QueueChangeType.added) {
            this._queueSizeChars += event.changedChars() ?? 0;
        } else {
            this._queueSizeChars -= event.changedChars() ?? 0;
        }
        return event.changedChars() ?? 0;
    }

    /** Remove and return the oldest event if queue not empty otherwise returns undefined. */
    removeOld(): FileEditEvent | undefined {
        const event = this._events.shift();
        this._updateState(event, QueueChangeType.removed);
        return event;
    }

    /** Remove and return the newest event if queue not empty otherwise returns undefined. */
    removeNew(): FileEditEvent | undefined {
        const event = this._events.pop();
        this._updateState(event, QueueChangeType.removed);
        return event;
    }

    /** Return the newest event in the queue if not empty otherwise returns undefined. */
    newest(): FileEditEvent | undefined {
        if (this._events.length === 0) {
            return undefined;
        }
        return this.at(this._events.length - 1);
    }

    /** Return all events in the queue as an array. */
    asArray(): FileEditEvent[] {
        return this._events.toArray();
    }

    /** Return the number of events in the queue. */
    get numEvents(): number {
        return this._events.length;
    }

    /** Return the total number of changed characters in the queue. */
    get sizeChars(): number {
        return this._queueSizeChars;
    }

    /** Return the event at the given index if it exists otherwise returns undefined. */
    at(index: number): FileEditEvent | undefined {
        return this._events.get(index);
    }

    removeAt(index: number) {
        const event = this._events.get(index);
        this._updateState(event, QueueChangeType.removed);
        this._events.removeOne(index);
    }

    clear() {
        this._events.clear();
        this._queueSizeChars = 0;
    }
}
