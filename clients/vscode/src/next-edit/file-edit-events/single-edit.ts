import { Char<PERSON><PERSON><PERSON> } from "../../utils/ranges";
import { countCommonPrefixChars, countCommonSuffixChars } from "./utils";

/** Represents an atomic text edit operation. */
export class SingleEdit {
    /**
     * The offset where the edit starts in the before text.
     */
    public readonly beforeStart: number;
    /**
     * The offset where the edit starts in the after text.
     */
    public readonly afterStart: number;
    /**
     * The text to be removed by this edit.
     */
    public readonly beforeText: string;
    /**
     * The text to be inserted by this edit.
     */
    public readonly afterText: string;

    constructor(args: {
        beforeStart: number;
        afterStart: number;
        beforeText: string;
        afterText: string;
    }) {
        this.beforeStart = args.beforeStart;
        this.afterStart = args.afterStart;
        this.beforeText = args.beforeText;
        this.afterText = args.afterText;
    }

    // this is a naive implementation of "class-transformer"
    static from(data: Partial<SingleEdit>): SingleEdit {
        const instance = new SingleEdit({
            beforeStart: data.beforeStart ?? 0,
            afterStart: data.afterStart ?? 0,
            beforeText: data.beforeText ?? "",
            afterText: data.afterText ?? "",
        });
        return instance;
    }

    get beforeEnd() {
        return this.beforeStart + this.beforeText.length;
    }

    get afterEnd() {
        return this.afterStart + this.afterText.length;
    }

    get beforeCRange() {
        return new CharRange(this.beforeStart, this.beforeEnd);
    }

    get afterCRange() {
        return new CharRange(this.afterStart, this.afterEnd);
    }

    toString(): string {
        return `SingleEdit{before=${this.beforeStart}:${this.beforeEnd}, after=${this.afterStart}:${this.afterEnd}, beforeText=${JSON.stringify(this.beforeText)}, afterText=${JSON.stringify(this.afterText)}}`;
    }

    /** Try to merge this singe edit event with the next event. Returns undefined if
     * they cannot be merged. */
    mergeNext(next: SingleEdit): SingleEdit | undefined {
        if (
            next.afterText === "" &&
            next.beforeStart >= this.afterStart &&
            next.beforeEnd <= this.afterEnd
        ) {
            // the next event is a deletion fully inside the first event's after range.
            const newAfterText =
                this.afterText.slice(0, next.beforeStart - this.afterStart) +
                this.afterText.slice(next.beforeEnd - this.afterStart);
            return new SingleEdit({
                beforeStart: this.beforeStart,
                afterStart: this.afterStart,
                beforeText: this.beforeText,
                afterText: newAfterText,
            });
        } else if (this.afterEnd === next.beforeStart) {
            // the next event starts right after this one.
            return new SingleEdit({
                beforeStart: this.beforeStart,
                afterStart: this.afterStart,
                beforeText: this.beforeText + next.beforeText,
                afterText: this.afterText + next.afterText,
            });
        } else if (next.beforeEnd === this.afterStart) {
            // the next event ends right before this one.
            return new SingleEdit({
                beforeStart: next.beforeStart,
                afterStart: next.afterStart,
                beforeText: next.beforeText + this.beforeText,
                afterText: next.afterText + this.afterText,
            });
        }
        return undefined;
    }

    /** Try to shrink the size of this edit by dropping prefixes and suffixes that
     * are common to both the before and after text.
     */
    normalize(): SingleEdit {
        let beforeStart = this.beforeStart;
        let afterStart = this.afterStart;
        let beforeText = this.beforeText;
        let afterText = this.afterText;

        const prefixCommon = countCommonPrefixChars(beforeText, afterText);
        if (prefixCommon > 0) {
            beforeStart += prefixCommon;
            afterStart += prefixCommon;
            beforeText = beforeText.slice(prefixCommon);
            afterText = afterText.slice(prefixCommon);
        }
        const suffixCommon = countCommonSuffixChars(beforeText, afterText);
        if (suffixCommon > 0) {
            beforeText = beforeText.slice(0, -suffixCommon);
            afterText = afterText.slice(0, -suffixCommon);
        }
        return new SingleEdit({
            beforeStart,
            afterStart,
            beforeText,
            afterText,
        });
    }
}
