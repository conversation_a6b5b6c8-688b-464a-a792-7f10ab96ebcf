import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import * as vscode from "vscode";

import { getLogger } from "../../logging";
import { DisposableService } from "../../utils/disposable-service";
import * as headChangeWatcher from "../../vcs/watcher/head-change-watcher";
import * as stashWatcher from "../../vcs/watcher/stash-watcher";
import * as workspaceEvents from "../../workspace/workspace-events";
import { FileEditEvent } from "./file-edit-event";
import {
    FileEditEventsStoreConfig,
    FileEditEventsStoreImpl,
    FileEditEventsStoreNoop,
} from "./file-edit-events-store";
import { FileEditEventsWatcher } from "./file-edit-watcher";
import { ClearOptions } from "./types";

/**
 * @description - This class maintains a map of file edit watcher per source folder.
 * It also exposes the official API available for consumers.
 */

export class FileEditManager extends DisposableService {
    private _watcher: Map<number, FileEditEventsWatcher> = new Map(); // folderId to watchers
    private _logger = getLogger("FileEditManager");
    constructor(
        private _blobNameCalculator: BlobNameCalculator,
        private _maxBlobSizeBytes: number,
        private _onFolderTextDocumentChanged: vscode.Event<workspaceEvents.FolderTextDocumentChangedEvent>,
        private _onFolderTextDocumentOpened: vscode.Event<workspaceEvents.FolderTextDocumentOpenedEvent>,
        private _onFolderTextDocumentClosed: vscode.Event<workspaceEvents.FolderTextDocumentClosedEvent>,
        private _onFolderFileDeleted: vscode.Event<workspaceEvents.FolderFileDeletedEvent>,
        private _onFolderFileWillRename: vscode.Event<workspaceEvents.FolderFileWillRenameEvent>,
        private _debugFeaturesEnabled: boolean,
        private _maxEventCharsToReturn: number = 5000
    ) {
        super();
    }

    public listenToEvents() {
        this._logger.debug("Listening to events");

        this.addDisposable(
            this._onFolderTextDocumentChanged((event) => this._handleTextDocumentChanged(event))
        );

        this.addDisposable(
            this._onFolderTextDocumentOpened((event) => this._handleTextDocumentOpened(event))
        );

        this.addDisposable(
            this._onFolderTextDocumentClosed((event) => this._handleTextDocumentClosed(event))
        );

        this.addDisposable(this._onFolderFileDeleted((event) => this._handleFileDeleted(event)));

        this.addDisposable(
            this._onFolderFileWillRename((event) => this._handleFileWillRename(event))
        );

        this.addDisposable(
            stashWatcher.onDidChange((event) => {
                this._logger.debug(`Stash changed for repo ${event.repoId}`);
                this._watcher.get(event.repoId)?.clear({ clearLastKnown: true });
            })
        );

        this.addDisposable(
            headChangeWatcher.onDidChange((event) => {
                this._logger.debug(`Head changed for repo ${event.repoId}`);
                this._watcher.get(event.repoId)?.clear({ clearLastKnown: true });
            })
        );
    }

    private _handleTextDocumentChanged(event: workspaceEvents.FolderTextDocumentChangedEvent) {
        const watcher = this._watcher.get(event.folderId);
        if (watcher) {
            watcher.handleChangedDocument(event);
        }
    }

    private _handleTextDocumentOpened(event: workspaceEvents.FolderTextDocumentOpenedEvent) {
        const watcher = this._watcher.get(event.folderId);
        if (watcher) {
            watcher.handleOpenedDocument(event);
        }
    }

    private _handleTextDocumentClosed(event: workspaceEvents.FolderTextDocumentClosedEvent) {
        const watcher = this._watcher.get(event.folderId);
        if (watcher) {
            watcher.handleClosedDocument(event);
        }
    }

    private _handleFileDeleted(event: workspaceEvents.FolderFileDeletedEvent) {
        const watcher = this._watcher.get(event.folderId);
        if (watcher) {
            watcher.handleFileDeleted(event);
        }
    }

    private _handleFileWillRename(event: workspaceEvents.FolderFileWillRenameEvent) {
        const watcher = this._watcher.get(event.folderId);
        if (watcher) {
            watcher.handleFileWillRename(event);
        }
    }

    public findFolderIdWithMostRecentChanges(): number {
        let watcher: FileEditEventsWatcher | undefined;
        let mostRecentTimestamp = -1;
        let mostRecentFolderId = -1;
        for (const [folderId, curWatcher] of this._watcher) {
            if (curWatcher.lastEventTimestamp > mostRecentTimestamp) {
                watcher = curWatcher;
                mostRecentTimestamp = curWatcher.lastEventTimestamp;
                mostRecentFolderId = folderId;
            }
        }
        if (watcher === undefined) {
            return -1;
        }
        return mostRecentFolderId;
    }

    public findEventsForFolder(folderId: number): FileEditEvent[] {
        const watcher = this._watcher.get(folderId);
        if (watcher === undefined) {
            return [];
        }
        return watcher.getEvents();
    }

    // TODO: consider using an actual event.
    public addInitialDocument(documentDetails: workspaceEvents.FolderTextDocumentOpenedEvent) {
        this._handleTextDocumentOpened(documentDetails);
    }

    public startTracking(
        folderId: number,
        folderName: string,
        storeConfig: FileEditEventsStoreConfig
    ): vscode.Disposable {
        this._logger.debug(`Tracking folder ${folderId} with store at ${storeConfig.directory}`);
        const watcher = new FileEditEventsWatcher(
            folderName,
            this._blobNameCalculator,
            this._maxBlobSizeBytes,
            this._debugFeaturesEnabled
                ? new FileEditEventsStoreImpl(storeConfig)
                : new FileEditEventsStoreNoop(),
            this._maxEventCharsToReturn
        );
        this._watcher.set(folderId, watcher);
        void watcher.loadEvents(); // allowing the async load to happen in the background.
        return watcher;
    }

    public clearAll(clearOptions: ClearOptions) {
        this._watcher.forEach((watcher) => watcher.clear(clearOptions));
    }

    public dispose() {
        super.dispose();
        this._logger.debug("Disposing FileEditManager");
        this._watcher.forEach((watcher) => watcher.dispose());
    }
}
