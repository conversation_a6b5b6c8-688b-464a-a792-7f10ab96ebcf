import * as vscode from "vscode";

import { AugmentConfig, AugmentConfigListener } from "../augment-config-listener";
import { FeatureFlagManager } from "../feature-flags";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import * as environment from "../utils/environment";

export enum NextEditWorkspaceConfigKey {
    showDiffInHover = "showDiffInHover",
    highlightSuggestionsInTheEditor = "highlightSuggestionsInTheEditor",
    enableAutoApply = "enableAutoApply",
}

/**
 * A convenient class to get next edit configs without having to pass around
 * configListener and featureFlagManager.
 */
export class NextEditConfigManager extends DisposableService {
    constructor(
        private readonly configListener: AugmentConfigListener,
        private readonly featureFlagManager: FeatureFlagManager,
        readonly augmentGlobalState: AugmentGlobalState
    ) {
        super();
        void augmentGlobalState.update(GlobalContextKey.nextEditUxMigrationStatus, undefined);
    }

    // TODO migrate other currently used configs into here.
    public get config() {
        return nextEditUxModeFlags(this.configListener.config, this.featureFlagManager);
    }

    public async toggleSetting(key: NextEditWorkspaceConfigKey) {
        const workspaceConfig = vscode.workspace.getConfiguration("augment");
        const configKey = "nextEdit." + NextEditWorkspaceConfigKey[key];
        const currentValue = workspaceConfig.inspect(configKey);
        let target = vscode.ConfigurationTarget.Global;
        if (currentValue?.workspaceValue !== undefined) {
            target = vscode.ConfigurationTarget.Workspace;
        }
        return workspaceConfig.update(configKey, !this.configListener.config.nextEdit[key], target);
    }

    public async setGlobalBooleanSetting(
        key: NextEditWorkspaceConfigKey,
        value: boolean | undefined
    ) {
        const workspaceConfig = vscode.workspace.getConfiguration("augment");
        const configKey = "nextEdit." + NextEditWorkspaceConfigKey[key];
        return workspaceConfig.update(configKey, value, vscode.ConfigurationTarget.Global);
    }
}

// exported for testing
export function nextEditUxModeFlags(
    config: AugmentConfig,
    featureFlagManager: FeatureFlagManager
): {
    enableAutoApply: boolean;
    showDiffInHover: boolean;
    enablePanel: boolean;
} {
    // NOTE(arun): We currently use enableDebugFeatures to determine if the panel is
    // enabled, but eventually we will migrate this to a feature flag.
    return {
        enableAutoApply: config.nextEdit.enableAutoApply,
        showDiffInHover: config.nextEdit.showDiffInHover,
        enablePanel:
            config.nextEdit.enableBottomPanel ??
            environment.isExtensionVersionGte(
                featureFlagManager.currentFlags.vscodeNextEditBottomPanelMinVersion
            ) ??
            false,
    };
}
