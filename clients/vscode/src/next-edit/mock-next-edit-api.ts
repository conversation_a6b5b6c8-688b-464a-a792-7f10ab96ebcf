import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import assert from "assert";
import JSON5 from "json5";
import * as vscode from "vscode";

import { NextEditGenerationResult, NextEditStreamRequest } from "../augment-api";
import { fileExists } from "../utils/fs-utils";
import { DiffSpan, NextEditResult } from "./next-edit-types";

/** Get the path to the mock result file. */
export function getMockResultPath(pathName: IQualifiedPathName): string {
    return joinPath(pathName.rootPath, pathName.relPath + ".next-edit-results.json5");
}

/** Extend NextEditResult to include fields useful for mocking. */
interface MockNextEditResult extends NextEditResult {
    // Specifying start/end characters is incredibly annoying and makes it really hard
    // to maintain the mock data.
    /** Line where the suggestion starts.  */
    lineStart: number;
    /** Line where the suggestion ends.  */
    lineEnd: number;
}

/**
 * A mocked version of apiServer.nextEditStream for testing.
 */
export async function* mockNextEditStream(
    request: NextEditStreamRequest
): AsyncGenerator<NextEditGenerationResult> {
    assert(request.pathName);
    const requestDoc = await vscode.workspace.openTextDocument(
        QualifiedPathName.from(request.pathName).absPath
    );

    const mockResultsPath = getMockResultPath(request.pathName);
    assert(fileExists(mockResultsPath));
    const mockResults: MockNextEditResult[] = JSON5.parse(
        (await vscode.workspace.openTextDocument(mockResultsPath)).getText()
    );
    // Get the full text of the document from the request.
    const requestText = requestDoc.getText();
    let idx = 0;
    for await (const result of mockResults) {
        // Make it easy to write mock data by inferring obvious fields like blob names,
        // paths, existingCode, etc. and using defaults for scores.
        // We also special case "noop" suggestions by inferring their suggestedCode and
        // diff spans.
        result.path = result.path ?? request.pathName?.relPath;
        result.blobName = request.blobName ?? "";
        // Add a unique blobname to the suggestions so they appear unique. This is
        // required because the panel tracks list position by suggestion id.
        result.suggestionId = result.suggestionId ?? `mock-suggestion-${result.blobName}-${idx++}`;

        // Convert lines to characters.
        result.charStart =
            result.charStart ?? requestDoc.offsetAt(new vscode.Position(result.lineStart, 0));
        result.charEnd =
            result.charEnd ?? requestDoc.offsetAt(new vscode.Position(result.lineEnd, 0));

        // To make it less cumbersome to describe diffSpans, we adopt a convention where
        // each span is delimited by `|` characters if existing or suggested code.
        result.diffSpans =
            result.diffSpans ??
            (result.existingCode != null && result.suggestedCode != null
                ? extractDiffSpans(result.existingCode, result.suggestedCode)
                : [
                      {
                          original: {
                              start: result.charStart,
                              stop: result.charEnd,
                          },
                          updated: {
                              start: result.charStart,
                              stop: result.charEnd,
                          },
                      },
                  ]);
        // Get rid of the `|` characters.
        result.existingCode =
            result.existingCode?.replaceAll("|", "") ??
            requestText.substring(result.charStart, result.charEnd);
        result.suggestedCode =
            result.suggestedCode?.replaceAll("|", "") ??
            requestText.substring(result.charStart, result.charEnd);
        result.changeDescription = result.changeDescription ?? "";
        result.editingScore = result.editingScore ?? 1;
        result.localizationScore = result.localizationScore ?? 1;
        result.editingScoreThreshold = result.editingScoreThreshold ?? 1;

        yield {
            result,
            unknownBlobNames: [],
            checkpointNotFound: false,
        };
    }
}

/**
 * Extract diff spans from text.
 *
 * Diff spans are encoded in the strings as the portions between `|` characters.
 *
 * For example: "This is a |foo|." and "This is a |bar|." would produce 3 diff spans
 * for "This is a " (shared), "foo" vs "bar" and "." (also shared).
 */
function extractDiffSpans(existingCode: string, suggestedCode: string): DiffSpan[] {
    const result: DiffSpan[] = [];

    // Get the split points between existingCode and suggestedCode.
    const existingSplits = existingCode.split("|");
    const suggestedSplits = suggestedCode.split("|");
    assert(existingSplits.length === suggestedSplits.length);
    let existingOffset = 0;
    let suggestedOffset = 0;
    for (let i = 0; i < existingSplits.length; i++) {
        result.push({
            original: {
                start: existingOffset,
                stop: existingOffset + existingSplits[i].length,
            },
            updated: {
                start: suggestedOffset,
                stop: suggestedOffset + suggestedSplits[i].length,
            },
        });
        existingOffset += existingSplits[i].length;
        suggestedOffset += suggestedSplits[i].length;
    }

    return result;
}
