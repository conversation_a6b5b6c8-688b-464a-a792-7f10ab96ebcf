import { Observable } from "@augment-internal/sidecar-libs/src/utils/observable";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import {
    NextEditBackgroundAcceptCommand,
    NextEditBackgroundRejectCommand,
    NextEditLearnMoreCommand,
    NextEditOpenPanelCommand,
    NextEditToggleHoverDiffCommand,
    NextEditUndoAcceptSuggestionCommand,
} from "../commands/next-edit";
import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { DisposableService } from "../utils/disposable-service";
import { isVsCodeVersionGte } from "../utils/environment";
import { KeybindingWatcher } from "../utils/keybindings";
import { getKeybindingMarkdownIcons } from "../utils/keyboard-icons";
import { toVSCodeRange } from "../utils/ranges-vscode";
import {
    AfterPreview,
    Animating,
    BackgroundState,
    BeforePreview,
    Hinting,
    NoSuggestions,
} from "./background-state";
import { DiffColors, renderFullDiff } from "./diff-renderer";
import { hoverTemplate as hoverTemplateDiffless, LinkAndText } from "./hover-template-diffless";
import { escapeAndBreakMarkdownCodicons } from "./hover-utils";
import { KeybindingStatus } from "./keybinding-state";
import { NextEditConfigManager } from "./next-edit-config-manager";
import {
    ChangeType,
    NextEditSessionEventName,
    NextEditSessionEventSource,
    SuggestionState,
} from "./next-edit-types";
import { EditSuggestion, SuggestionManager } from "./suggestion-manager";
import { getKeybindingForCommand } from "./utils";

const estimatedWidthOfDifflessHeaderInFixedWidthChars = 38;

type NextEditBackgroundCommandClass =
    | typeof NextEditBackgroundAcceptCommand
    | typeof NextEditBackgroundRejectCommand
    | typeof NextEditToggleHoverDiffCommand
    | typeof NextEditUndoAcceptSuggestionCommand
    | typeof NextEditOpenPanelCommand
    | typeof NextEditLearnMoreCommand;

export class BackgroundNextEditsHoverProvider
    extends DisposableService
    implements vscode.HoverProvider
{
    private _logger = getLogger("BackgroundNextEditsHoverProvider");

    // these colors are actually all the same or very similar in most of the themes,
    // both dark and light themes, so we can get away with hardcoding the text color ones.
    // The reason for this is to get a "double opacity" on the text colors because we will
    // not be stacking them in the diff the way they are normally stacked by vscode's own
    // diff (i.e. text span inside line span causing colors to be additive). The reason
    // for this is because every span in the hover gets an extra bottom margin and
    // these will accumulate, see https://github.com/microsoft/vscode/issues/232226
    static inlineDiffColors: DiffColors = {
        originalLineColor: "var(--vscode-diffEditor-removedLineBackground)",
        originalTextColor: "#FF000066",
        updatedLineColor: "var(--vscode-diffEditor-insertedLineBackground)",
        updatedTextColor: "#9CCC2C66",
        lineNumberColor: "var(--vscode-editorGhostText-foreground)",
    };

    // The actual VSCode hover provider.  We dynamically register and unregister it
    // on changes to try to show up higher in the hover.
    private _provider: vscode.Disposable | undefined;

    // This tracks whether we are waiting to show a hover so that we do not unregister
    // ourself at that time.
    private _waitingToShowHover: boolean = false;

    constructor(
        private _keybindingWatcher: KeybindingWatcher,
        private _nextEditSessionEventReporter: NextEditSessionEventReporter,
        private readonly _suggestionManager: SuggestionManager,
        private readonly _state: Observable<BackgroundState>,
        private readonly _configListener: AugmentConfigListener,
        private readonly _hasCompletionShowing: () => boolean,
        private readonly _showKeybindingsOnButtons: () => boolean,
        private readonly _keybindingStatus: KeybindingStatus,
        private readonly _nextEditConfigManager: NextEditConfigManager
    ) {
        super();

        this.addDisposable(
            vscode.window.onDidChangeTextEditorVisibleRanges((event) => {
                this._handleVisibleRangesChanged(event);
            })
        );

        // VSCode seems to prefer hover providers with more specific selectors
        // and those that are registered later.
        // We thus register a selector for the current file on every change,
        // which should help us get good scores on both counts.
        this._register(undefined);
        this.addDisposable(
            vscode.window.onDidChangeActiveTextEditor((editor) => this._register(editor))
        );
        this.addDisposable(
            this._suggestionManager.onSuggestionsChanged((_event) => {
                this._register(vscode.window.activeTextEditor);
            })
        );
        this.addDisposable(
            new vscode.Disposable(() => {
                this._provider?.dispose();
                this._provider = undefined;
            })
        );
    }

    private get _showDiffInDifflessHover() {
        return this._nextEditConfigManager.config.showDiffInHover;
    }

    /**
     *
     * Often small mouse movements trigger scrolls and changes to the visible ranges.
     * When we are in the preview state, we want to re-show the hover, but do so
     * with a small debounce to avoid transient events that especially occur during
     * the onboarding flow.
     *
     * @param event The visible ranges changed event.
     */
    private _handleVisibleRangesChanged(event: vscode.TextEditorVisibleRangesChangeEvent) {
        const eventDocument = event.textEditor.document;
        const suggestion = this._state.value.suggestion;
        if (
            suggestion &&
            (this._state.value instanceof BeforePreview ||
                this._state.value instanceof AfterPreview ||
                this._state.value instanceof Animating) &&
            suggestion.qualifiedPathName.equals(eventDocument.uri) &&
            event.visibleRanges.some((r) => r.contains(toVSCodeRange(suggestion.highlightRange)))
        ) {
            this.showHover();
        }
    }

    private _register(editor: vscode.TextEditor | undefined) {
        if (this._waitingToShowHover) {
            // We don't want to unregister ourselves and potentially not show the hover.
            return;
        }

        this._provider?.dispose();
        this._provider = undefined;

        if (!editor) {
            return;
        }
        this._provider = vscode.languages.registerHoverProvider(
            { pattern: editor.document.uri.fsPath },
            this
        );
    }

    public provideHover(
        document: vscode.TextDocument,
        position: vscode.Position,
        _token: vscode.CancellationToken
    ) {
        if (this._hoverDisabled()) {
            return undefined;
        }

        // for error logging purposes.
        let requestId: string | undefined = undefined;
        let suggestion: EditSuggestion | undefined = undefined;
        try {
            if (document.uri.scheme === "output" || this._hasCompletionShowing()) {
                return;
            }
            this._waitingToShowHover = false;
            const suggestionsInFile = this._suggestionManager
                .getActiveSuggestions()
                .filter(
                    (s) =>
                        s.state === SuggestionState.fresh &&
                        s.changeType !== ChangeType.noop &&
                        s.qualifiedPathName.equals(document.uri)
                );
            suggestion = suggestionsInFile.find((s) =>
                this.hoverContactCondition(s, position, document)
            );
            // In diffless mode we don't need to set the active suggestion
            // since the background manager does that.
            const str = this._provideDifflessHover(document, position, suggestion);
            if (str) {
                this._nextEditSessionEventReporter.reportEventFromSuggestion(
                    suggestion,
                    NextEditSessionEventName.HoverShown,
                    NextEditSessionEventSource.Unknown
                );
            }
            return str && new vscode.Hover(str);
        } catch (err) {
            if (err instanceof Error) {
                this._logger.error(
                    `Error in hover provider: ${err.message}, requestId: ${requestId}`
                );
                if (err.stack) {
                    this._logger.error(err.stack);
                }
                this._nextEditSessionEventReporter.reportEventFromSuggestion(
                    suggestion,
                    NextEditSessionEventName.ErrorHoverError,
                    NextEditSessionEventSource.Unknown
                );
            }
            return new vscode.Hover(`Error rendering hover. requestId: ${requestId}`);
        }
    }

    private _keybindingWillWork(
        command: NextEditBackgroundCommandClass,
        hoveredSuggestion: EditSuggestion | undefined
    ) {
        if (command === NextEditOpenPanelCommand) {
            return this._nextEditConfigManager.config.enablePanel;
        }
        if (hoveredSuggestion && !hoveredSuggestion.equals(this._state.value.suggestion)) {
            return false;
        }
        return this._keybindingStatus.get(command);
    }

    private _provideDifflessHover(
        document: vscode.TextDocument,
        position: vscode.Position,
        hoveredSuggestion: EditSuggestion | undefined
    ) {
        let suggestion: EditSuggestion | undefined = undefined;
        let buttons: Array<LinkAndText> = [];
        hoveredSuggestion =
            this._state.value instanceof NoSuggestions || this._state.value instanceof Hinting
                ? hoveredSuggestion
                : undefined;
        const buttonArgs: [NextEditSessionEventSource, string | undefined] = [
            NextEditSessionEventSource.HoverClick,
            hoveredSuggestion?.result.suggestionId ?? undefined,
        ];

        const getTooltip = (command: NextEditBackgroundCommandClass, text: string): string => {
            if (this._keybindingWillWork(command, hoveredSuggestion)) {
                let kb = this.getPrettyKeybinding(command.commandID);
                return `${text}${kb ? "&nbsp;" + kb : ""}`;
            }
            return text;
        };

        const buildCommandInfo = (
            commandClass: NextEditBackgroundCommandClass,
            shortText: string,
            longText: string
        ): LinkAndText => {
            const commandID = commandClass.commandID;
            return {
                href: commandClass.generateHref(...buttonArgs),
                text: shortText,
                tooltip: getTooltip(commandClass, longText),
                keybindingIcons:
                    this._showKeybindingsOnButtons() &&
                    this._keybindingWillWork(commandClass, hoveredSuggestion)
                        ? this.getPrettyKeybindingIcons(commandID)
                        : null,
            };
        };

        const rejectButton = buildCommandInfo(
            NextEditBackgroundRejectCommand,
            "Reject",
            "Reject Suggestion"
        );

        const panelButton = buildCommandInfo(
            NextEditOpenPanelCommand,
            "$(layout-panel)",
            "Open Suggestions Panel"
        );
        panelButton.keybindingIcons = null; // hide these because this is an icon only.
        const diffButton = buildCommandInfo(
            NextEditToggleHoverDiffCommand,
            "$(diff-single)",
            this._showDiffInDifflessHover ? "Hide Diff" : "Show Diff"
        );
        diffButton.noLeftMargin = true;
        const settingsButton = {
            href: `command:workbench.action.openSettings?${encodeURIComponent(JSON.stringify(["@ext:augment.vscode-augment augment.nextEdit"]))}`,
            text: "$(gear)",
            tooltip: "Open Settings",
            noLeftMargin: true,
        };

        const learnMoreButton = buildCommandInfo(
            NextEditLearnMoreCommand,
            "Learn More",
            "Learn More"
        );

        if (
            ((this._state.value instanceof BeforePreview ||
                this._state.value instanceof AfterPreview) &&
                this._state.value.suggestion
                    ?.previewCursorRange(document)
                    .contains(position.line)) ||
            hoveredSuggestion
        ) {
            suggestion = hoveredSuggestion || this._state.value.suggestion;
            buttons = [
                buildCommandInfo(NextEditBackgroundAcceptCommand, "Apply", "Apply Suggestion"),
                rejectButton,
                ...(this._showKeybindingsOnButtons() ? [learnMoreButton] : []),
                ...(this._nextEditConfigManager.config.enablePanel ? [panelButton] : []),
                diffButton,
                settingsButton,
            ];
        }
        if (this._state.value instanceof AfterPreview) {
            const reverseRange = this._state.value.suggestion.previewCursorRange(document);
            // Deletions have point after ranges, so we slightly expand their range.
            if (
                reverseRange.contains(position.line) ||
                (this._state.value.suggestion?.changeType === ChangeType.deletion &&
                    reverseRange?.touches(position.line))
            ) {
                suggestion = this._state.value.suggestion;
                buttons = [
                    buildCommandInfo(
                        // We need to use our own command to undo so that we can hide the hover.
                        NextEditUndoAcceptSuggestionCommand,
                        "Undo",
                        "Undo Suggestion"
                    ),
                    rejectButton,
                    ...(this._showKeybindingsOnButtons() ? [learnMoreButton] : []),
                    ...(this._nextEditConfigManager.config.enablePanel ? [panelButton] : []),
                    diffButton,
                    settingsButton,
                ];
            }
        }
        if (suggestion) {
            const escapedChangeDescription = escapeAndBreakMarkdownCodicons(
                suggestion.result.changeDescription
            );
            if (this._showDiffInDifflessHover) {
                const { result, longestLineLength } = this.getHoverDiff(
                    document,
                    suggestion,
                    estimatedWidthOfDifflessHeaderInFixedWidthChars
                );
                return hoverTemplateDiffless(
                    escapedChangeDescription,
                    buttons,
                    result,
                    [],
                    longestLineLength
                );
            } else {
                return hoverTemplateDiffless(escapedChangeDescription, buttons);
            }
        }
        return undefined;
    }

    /** Returns true if we should show a hover for the given suggestion at the given position. */
    public hoverContactCondition(
        suggestion: EditSuggestion,
        position: vscode.Position,
        document: vscode.TextDocument
    ) {
        return suggestion.previewCursorRange(document).contains(position.line);
    }

    protected getPrettyKeybinding(...commandID: string[]) {
        return this.getKeybindingsForCommands(commandID, true);
    }

    protected getPrettyKeybindingIcons(...commandID: string[]) {
        const keybinding = KeybindingWatcher.getStructuredKeybinding(
            this.getKeybindingsForCommands(commandID)
        );

        return getKeybindingMarkdownIcons(
            keybinding,
            this._keybindingWatcher.getSimplifiedPlatform()
        );
    }

    protected getKeybindingsForCommands(commandIDs: string[], prettyFormat: boolean = false) {
        const keybindings = commandIDs.map((commandID) =>
            getKeybindingForCommand(this._keybindingWatcher, commandID, prettyFormat)
        );
        if (keybindings.includes(null)) {
            return "";
        }
        return keybindings.join(" ");
    }

    protected getHoverDiff(
        document: vscode.TextDocument,
        suggestion: EditSuggestion,
        minWidthInFixedWidthChars: number
    ) {
        const range =
            suggestion.state === SuggestionState.accepted
                ? suggestion.afterLineRange(document)
                : suggestion.lineRange;
        const beforeContextRange =
            range.start > 0 ? new vscode.Range(range.start - 1, 0, range.start, 0) : undefined;

        const afterContextRange =
            range.stop < document.lineCount - 1
                ? new vscode.Range(range.stop, 0, range.stop + 1, 0)
                : undefined;

        const result = renderFullDiff(
            suggestion,
            beforeContextRange && document.getText(beforeContextRange),
            afterContextRange && document.getText(afterContextRange),
            minWidthInFixedWidthChars,
            BackgroundNextEditsHoverProvider.inlineDiffColors,
            true,
            this._logger
        );
        if (suggestion.result.truncationChar !== undefined) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.SuggestionTruncatedInHover,
                NextEditSessionEventSource.Unknown
            );
        }
        return result;
    }

    private _hoverDisabled() {
        return this._configListener.config.nextEdit.noDiffModeUseCodeLens;
    }

    public showHover() {
        if (this._hoverDisabled()) {
            return;
        }

        this._register(vscode.window.activeTextEditor);
        // Ensure that we don't unregister the hover before we show it.
        this._waitingToShowHover = true;

        // NOTE(arun): It is possible for us to call hideHover and showHover in
        // somewhat non-deterministic order: we aren't able to pin down exactly why
        // that is, but the consequence is that sometimes we call `showHover` when a
        // hover is showing, which causes the hover to be focussed, preventing new
        // hovers from appearing and replacing the old ones. This happens particularly
        // often when you click on a hover for certain machines.
        // The "noAutoFocus" argument below is an undocumented option that ensures that
        // this action never focuses a hover, preventing this issue.
        void vscode.commands.executeCommand("editor.action.showHover", { focus: "noAutoFocus" });
    }

    public hideHover(source: NextEditSessionEventSource = NextEditSessionEventSource.Unknown) {
        // For Reasons, doing `void hideHoverAsync` causes more flicker (flips table).
        // For Reasons, vscode automatically hides hovers when a keybinding is pressed.
        if (this._hoverDisabled() || source === NextEditSessionEventSource.Keybinding) {
            return;
        }
        if (isVsCodeVersionGte("1.97.0-insider") || isVsCodeVersionGte("1.97.0")) {
            // Starting 1.97.0, there is a command to actually hide the hover.
            void vscode.commands.executeCommand("editor.action.hideHover");
        } else {
            // For all previous versions, hide the hover by scrolling up and down by
            // zero characters twice.
            // This is a terrible hack but it seems to work.
            void vscode.commands.executeCommand("editorScroll", {
                to: "down",
                by: "line",
                value: 0,
            });
            void vscode.commands.executeCommand("editorScroll", {
                to: "up",
                by: "line",
                value: 0,
            });
        }
    }

    public async hideHoverAsync(
        source: NextEditSessionEventSource = NextEditSessionEventSource.Unknown
    ) {
        // For Reasons, vscode automatically hides hovers when a keybinding is pressed.
        if (this._hoverDisabled() || source === NextEditSessionEventSource.Keybinding) {
            return;
        }
        if (isVsCodeVersionGte("1.97.0-insider") || isVsCodeVersionGte("1.97.0")) {
            // Starting 1.97.0, there is a command to actually hide the hover.
            await vscode.commands.executeCommand("editor.action.hideHover");
        } else {
            // For all previous versions, hide the hover by scrolling up and down by
            // zero characters twice.
            // This is a terrible hack but it seems to work.
            await vscode.commands.executeCommand("editorScroll", {
                to: "down",
                by: "line",
                value: 0,
            });
            await vscode.commands.executeCommand("editorScroll", {
                to: "up",
                by: "line",
                value: 0,
            });
        }
    }
}
