import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { setContext } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { fileExists } from "../utils/fs-utils";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { shouldQueryNextEditStream } from "./next-edit-api";
import {
    NextEditMode,
    NextEditScope,
    NextEditSessionEventName,
    NextEditSessionEventSource,
} from "./next-edit-types";
import { NextEditRequestManager } from "./request-manager-impl";
import { SuggestionManager } from "./suggestion-manager";

export class GlobalNextEdits extends DisposableService {
    private readonly timeoutMs = 1000 * 30;

    private _logger = getLogger("GlobalNextEdits");

    constructor(
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _nextEditRequestManager: NextEditRequestManager,
        private readonly _suggestionManager: SuggestionManager,
        private readonly _configListener: AugmentConfigListener,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter
    ) {
        super();

        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument(this.handleWorkspaceEditsAvailable)
        );
        this.addDisposable(
            vscode.window.onDidChangeActiveTextEditor(this.handleWorkspaceEditsAvailable)
        );
        // listen for when a request gets cached
        this.addDisposable(
            new vscode.Disposable(
                this._nextEditRequestManager.lastFinishedRequest.listen(() => {
                    this._handleWorkspaceEditsCached();
                })
            )
        );
    }

    public async startGlobalQuery(eventSource?: NextEditSessionEventSource) {
        void vscode.commands.executeCommand(
            "setContext",
            "vscode-augment.nextEdit.global.updating",
            true
        );
        try {
            this._nextEditSessionEventReporter.reportEventWithoutIds(
                NextEditSessionEventName.GlobalModeRefreshed,
                eventSource ?? NextEditSessionEventSource.Command
            );

            const editor = vscode.window.activeTextEditor;
            const pathName = editor
                ? this._workspaceManager.safeResolvePathName(editor.document.uri)
                : undefined;

            const requestId = this._nextEditRequestManager.enqueueRequest(
                pathName,
                NextEditMode.Foreground,
                NextEditScope.Workspace
            );
            if (!requestId) {
                return;
            }

            await this._nextEditRequestManager.lastFinishedRequest
                .waitUntil(
                    (resultInfo) => resultInfo !== undefined && resultInfo.requestId === requestId,
                    this.timeoutMs
                )
                .catch((e) => {
                    this._logger.error(`Global next edit failed: ${e}`);
                    this._nextEditSessionEventReporter.reportEvent(
                        requestId,
                        undefined,
                        Date.now(),
                        NextEditSessionEventName.ErrorGlobalModeError,
                        NextEditSessionEventSource.Command
                    );
                });

            // Remove suggestions for deleted files, as we may have missed their deletion.
            const deletedFiles = this._suggestionManager.getActiveSuggestions().filter((s) => {
                return !fileExists(s.qualifiedPathName.absPath);
            });
            this._suggestionManager.remove(deletedFiles);
        } finally {
            void vscode.commands.executeCommand(
                "setContext",
                "vscode-augment.nextEdit.global.updating",
                false
            );
        }
    }

    public cancel() {
        // TODO: We could potentially cancel only our request.
        this._nextEditRequestManager.cancelAll();

        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.GlobalModeCanceled,
            NextEditSessionEventSource.Command
        );
    }

    public handleWorkspaceEditsAvailable = () => {
        const { activeTextEditor } = vscode.window;
        const qualifiedPathName = activeTextEditor
            ? this._workspaceManager.safeResolvePathName(activeTextEditor?.document.uri)
            : undefined;
        const hasEdits = shouldQueryNextEditStream(
            this._workspaceManager,
            this._configListener,
            qualifiedPathName
        );
        setContext("vscode-augment.nextEdit.global.canUpdate", hasEdits);
    };

    private _handleWorkspaceEditsCached() {
        const { activeTextEditor } = vscode.window;
        const qualifiedPathName = activeTextEditor
            ? this._workspaceManager.safeResolvePathName(activeTextEditor?.document.uri)
            : undefined;
        // is false if the request would not be made.
        // One example is if the request has already been made in the current state.
        const shouldNotMakeRequest = this._nextEditRequestManager.shouldNotEnqueueRequestReason(
            qualifiedPathName,
            NextEditMode.Foreground,
            NextEditScope.Workspace
        );
        setContext("vscode-augment.nextEdit.global.updateCached", shouldNotMakeRequest);
    }
}
