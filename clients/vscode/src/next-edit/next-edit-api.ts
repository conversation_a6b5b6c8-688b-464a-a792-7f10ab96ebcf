import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import * as vscode from "vscode";

import {
    APIServer,
    Diagnostic,
    DiagnosticSeverity,
    NextEditGenerationResult,
    NextEditStreamRequest,
} from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { DiagnosticsManager } from "../diagnostics";
import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { DisposableService } from "../utils/disposable-service";
import { fileExists } from "../utils/fs-utils";
import { CharRange } from "../utils/ranges";
import { toLineRange, vscodeRangeToString } from "../utils/ranges-vscode";
import { makeReplacementText } from "../utils/replacement-text";
import { utf32to16Offset } from "../utils/unicode";
import { viewTextDocument } from "../workspace/view-text-document";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { FileEditEvent } from "./file-edit-events";
import { getMockResultPath, mockNextEditStream } from "./mock-next-edit-api";
import {
    type IEditSuggestion,
    NextEditSessionEventName,
    NextEditSessionEventSource,
} from "./next-edit-types";
import { EditSuggestion } from "./suggestion-manager";

const NUM_DIAGNOSTICS_TO_SEND = 10;
const MAX_NUM_DIAGNOSTICS_PER_FILE = 3;
const MAX_PAYLOAD_SIZE = 5 * 1024 * 1024; // 5MB

/**
 * Get the most recent folder root from the workspace manager.
 */
function getMostRecentFolderRoot(
    workspaceManager: WorkspaceManager,
    qualifiedPathName: IQualifiedPathName | undefined
): string | undefined {
    return qualifiedPathName
        ? workspaceManager.getFolderRoot(QualifiedPathName.from(qualifiedPathName).absPath)
        : workspaceManager.getMostRecentlyChangedFolderRoot();
}

/**
 * Determine if we should query the next edit stream.
 * High level logic is as follows:
 * 1. If we have mock results, use them.
 * 2. If we have no edit events, skip.
 * 3. Otherwise, query the stream.
 * @param workspaceManager
 * @param configListener
 * @param qualifiedPathName
 * @param fileEditEvents
 * @returns
 */
export function shouldQueryNextEditStream(
    workspaceManager: WorkspaceManager,
    configListener: AugmentConfigListener,
    qualifiedPathName: IQualifiedPathName | undefined,
    fileEditEvents?: FileEditEvent[] | undefined
): boolean {
    const hasMock =
        !!configListener.config.nextEdit.useMockResults &&
        !!qualifiedPathName &&
        fileExists(getMockResultPath(qualifiedPathName));
    const editEvents =
        fileEditEvents ??
        workspaceManager.getFileEditEvents(
            getMostRecentFolderRoot(workspaceManager, qualifiedPathName)
        );
    return editEvents.length !== 0 || hasMock;
}

/**
 * Make the next edit streaming query.
 *
 * This function wraps around the underlying APIServer API call and does some necessary
 * state tracking to ensure the results are valid:
 * 1. Update line offsets based on changes in the editor between now and when the query
 *    was made.
 * 2. Validate that the response matches the text in the buffer and ultimately yielding
 *    an `EditSuggestion` object.
 *
 * @returns An async stream of request status and EditSuggestion objects.
 */
export async function* queryNextEditStream(
    request: NextEditStreamRequest,
    workspaceManager: WorkspaceManager,
    diagnosticsManager: DiagnosticsManager,
    apiServer: APIServer,
    blobNameCalculator: BlobNameCalculator,
    configListener: AugmentConfigListener,
    cancelToken: vscode.CancellationToken,
    nextEditSessionEventReporter: NextEditSessionEventReporter
): AsyncGenerator<{
    status: APIStatus;
    suggestion?: IEditSuggestion;
}> {
    const logger = getLogger("queryNextEditStream");
    // NOTE(arun): We check for cancellations after any API call.
    // Note that we should always yield statuses before returning.
    if (cancelToken.isCancellationRequested) {
        logger.debug("Skipping Next Edit with cancelled token.");
        yield { status: APIStatus.cancelled };
        return;
    }

    if (
        (request.prefix?.length ?? 0) +
            (request.selectedCode?.length ?? 0) +
            (request.suffix?.length ?? 0) >
        MAX_PAYLOAD_SIZE
    ) {
        logger.debug("Skipping Next Edit with too much context.");
        yield { status: APIStatus.ok };
        return;
    }

    // We always want to return results from the mock to make testing easier.
    const shouldProceedWithQuery = shouldQueryNextEditStream(
        workspaceManager,
        configListener,
        request.pathName,
        request.fileEditEvents
    );
    if (!shouldProceedWithQuery) {
        logger.verbose("Skipping Next Edit with no changes.");
        yield { status: APIStatus.ok };
        return;
    }

    const hasMock =
        configListener.config.nextEdit.useMockResults &&
        request.pathName &&
        fileExists(getMockResultPath(request.pathName));
    const mostRecentFolderRoot = getMostRecentFolderRoot(workspaceManager, request.pathName);
    const editEvents =
        request.fileEditEvents ?? workspaceManager.getFileEditEvents(mostRecentFolderRoot);

    const mostRecentRepoRoot = mostRecentFolderRoot
        ? workspaceManager.getRepoRootForFolderRoot(mostRecentFolderRoot)
        : undefined;
    const requestPath = request.pathName && QualifiedPathName.from(request.pathName);
    const workspaceRootPath = requestPath?.rootPath ?? mostRecentRepoRoot;

    const workspaceContext = workspaceManager.getContext();
    request = {
        ...request,
        blobName: request.blobName ?? (requestPath && workspaceManager.getBlobName(requestPath)),
        blobs: request.blobs ?? workspaceContext.blobs,
        recentChanges:
            request.recentChanges ??
            makeReplacementText(workspaceContext.recentChunks.filter((chunk) => !chunk.uploaded)),
        fileEditEvents: editEvents,
        unindexedEditEvents:
            request.unindexedEditEvents.length > 0
                ? request.unindexedEditEvents
                : workspaceContext.unindexedEditEvents,
        unindexedEditEventsBaseBlobNames:
            request.unindexedEditEventsBaseBlobNames.length > 0
                ? request.unindexedEditEventsBaseBlobNames
                : workspaceContext.unindexedEditEventsBaseBlobNames,
        diagnostics: await getDiagnostics(
            diagnosticsManager,
            workspaceRootPath,
            blobNameCalculator,
            workspaceManager
        ),
    };

    const offsetTracker = new PendingOffsetTracker(request.requestId, workspaceManager);
    logger.verbose(
        `[${request.requestId}] Starting request for ${requestPath?.relPath} (mode=${request.mode}, scope=${request.scope}).`
    );

    try {
        let stream: AsyncIterable<NextEditGenerationResult>;
        // Mock results for testing when using the debug extension.
        if (hasMock) {
            stream = mockNextEditStream(request);
        } else {
            stream = await apiServer.nextEditStream(request);
        }

        if (cancelToken.isCancellationRequested) {
            logger.debug(`[${request.requestId}] Skipping next edit with cancelled token.`);
            yield { status: APIStatus.cancelled };
            return;
        }

        for await (const result of stream) {
            const logPrefix = `[${request.requestId}/${result.result.suggestionId}]`;
            // Handle error info even if the request was cancelled.
            if (result.unknownBlobNames.length > 0) {
                workspaceManager.handleUnknownBlobs(workspaceContext, result.unknownBlobNames);
                // For debugging purposes, print out a message on missing blobs.
                logger.warn(`${logPrefix} Found ${result.unknownBlobNames.length} unknown blobs.`);
            }
            if (result.checkpointNotFound) {
                void workspaceManager.handleUnknownCheckpoint(
                    request.requestId,
                    request.blobs!.checkpointId!
                );
                logger.warn(`${logPrefix} Checkpoint was not found.`);
            }
            if (cancelToken.isCancellationRequested) {
                logger.debug(`${logPrefix} Cancelled by the client.`);
                yield { status: APIStatus.cancelled };
                return;
            }

            // Check that the response is valid.
            const resultDoc = await findDocument(
                result.result.path,
                workspaceRootPath,
                workspaceManager
            );
            const resultPath = resultDoc && workspaceManager.safeResolvePathName(resultDoc.uri);
            if (cancelToken.isCancellationRequested) {
                logger.debug(`${logPrefix} Cancelled by the client.`);
                yield { status: APIStatus.cancelled };
                return;
            }
            if (!resultPath) {
                logger.warn(`${logPrefix} Response path ${result.result.path} has no document.`);
                nextEditSessionEventReporter.reportEvent(
                    request.requestId,
                    result.result.suggestionId,
                    Date.now(),
                    NextEditSessionEventName.ErrorNoDocumentForResponse,
                    NextEditSessionEventSource.Unknown
                );
                continue;
            }
            if (resultDoc?.uri.scheme === "file" && !fileExists(resultPath.absPath)) {
                logger.warn(`${logPrefix} Response path ${resultPath.relPath} does not exist.`);
                nextEditSessionEventReporter.reportEvent(
                    request.requestId,
                    result.result.suggestionId,
                    Date.now(),
                    NextEditSessionEventName.ErrorResponseFileIsDeleted,
                    NextEditSessionEventSource.Unknown
                );
                continue;
            }

            // Convert character indices from UTF-32 (which the backend uses)
            // to UTF-16 (which VSCode and JS use).
            const fullText = resultDoc.getText();
            const charStart = utf32to16Offset(fullText, result.result.charStart);
            const charEnd = utf32to16Offset(fullText, result.result.charEnd);
            // Update the response based on how the buffer changed since the request.
            const charRange = offsetTracker.updateWithPendingEdits(
                resultPath,
                new CharRange(charStart, charEnd)
            );
            if (!charRange) {
                // This is totally normal when you're typing.
                logger.debug(`${logPrefix} Response was invalidated by pending edits.`);
                yield { status: APIStatus.invalidArgument };
                return;
            }
            let selectedRange = new vscode.Range(
                resultDoc.positionAt(charRange.start),
                resultDoc.positionAt(charRange.stop)
            );
            // If the document has no trailing newline and we're at the end, make
            // sure our end range starts at the beginning of the nonexistent next
            // line rather than ending at the end of the current line. Even though
            // this position doesn't actually exist the code works, while our code
            // doesn't handle the other case correctly.
            const endLine = resultDoc.lineAt(selectedRange.end.line);
            if (
                endLine.range.isEqual(endLine.rangeIncludingLineBreak) &&
                selectedRange.end.line === resultDoc.lineCount - 1 &&
                selectedRange.end.character === endLine.range.end.character &&
                !selectedRange.isEmpty
            ) {
                selectedRange = selectedRange.with({
                    end: new vscode.Position(selectedRange.end.line + 1, 0),
                });
            }

            // Check that the response is line-aligned.
            if (selectedRange.start.character !== 0 || selectedRange.end.character !== 0) {
                logger.warn(
                    `${logPrefix} Response was not line-aligned ${vscodeRangeToString(
                        selectedRange
                    )}.`
                );
                logger.verbose(
                    `${logPrefix} Converting char range ${result.result.charStart}-${result.result.charEnd} to ${charStart}-${charEnd}.`
                );
                logger.verbose(
                    `${logPrefix} Updated char range to ${charRange?.start}-${charRange?.stop}.`
                );
                logger.verbose(
                    `${logPrefix} The bad line is: "${
                        resultDoc.lineAt(
                            selectedRange.end.character !== 0
                                ? selectedRange.end.line
                                : selectedRange.start.line
                        ).text
                    }".`
                );
                nextEditSessionEventReporter.reportEvent(
                    request.requestId,
                    result.result.suggestionId,
                    Date.now(),
                    NextEditSessionEventName.ErrorResponseNotLineAligned,
                    NextEditSessionEventSource.Unknown
                );
                // Skip if not the current file
                if (resultPath.relPath !== request.pathName?.relPath) {
                    continue;
                }
                nextEditSessionEventReporter.reportEvent(
                    request.requestId,
                    result.result.suggestionId,
                    Date.now(),
                    NextEditSessionEventName.ErrorResponseNotLineAlignedForCurrentFile,
                    NextEditSessionEventSource.Unknown
                );
                yield { status: APIStatus.invalidArgument };
                return;
            }
            if (resultDoc.getText(selectedRange) !== result.result.existingCode) {
                logger.warn(`${logPrefix} Code in buffer doesn't match code in response.`);
                logger.verbose(
                    `${logPrefix} Converting char range ${result.result.charStart}-${result.result.charEnd} to ${charStart}-${charEnd}.`
                );
                logger.verbose(
                    `${logPrefix} Updated char range to ${charRange?.start}-${charRange?.stop}.`
                );
                logger.verbose(
                    `${logPrefix} Buffer code: "${resultDoc.getText(selectedRange)}", response code: "${result.result.existingCode}".`
                );
                nextEditSessionEventReporter.reportEvent(
                    request.requestId,
                    result.result.suggestionId,
                    Date.now(),
                    NextEditSessionEventName.ErrorCodeInBufferDoesntMatchCodeInResponse,
                    NextEditSessionEventSource.Unknown
                );
                // Skip if not the current file
                if (resultPath.relPath !== request.pathName?.relPath) {
                    continue;
                }
                nextEditSessionEventReporter.reportEvent(
                    request.requestId,
                    result.result.suggestionId,
                    Date.now(),
                    NextEditSessionEventName.ErrorCodeInBufferDoesntMatchCodeInResponseForCurrentFile,
                    NextEditSessionEventSource.Unknown
                );
                yield { status: APIStatus.invalidArgument };
                return;
            }

            // Construct edit suggestion.
            const suggestion = new EditSuggestion(
                request.requestId,
                request.mode,
                request.scope,
                {
                    ...result.result,
                    charStart: charRange.start,
                    charEnd: charRange.stop,
                },
                resultPath,
                toLineRange(selectedRange),
                resultDoc.uri.scheme
            );
            logger.verbose(
                `${logPrefix} Returning ${suggestion.changeType} suggestion for ${
                    resultPath.relPath
                }@${suggestion.lineRange.toString()}.`
            );
            yield {
                status: APIStatus.ok,
                suggestion,
            };
        }
        logger.verbose(`[${request.requestId}] Request completed.`);
    } catch (e: any) {
        if (APIError.isAPIErrorWithStatus(e, APIStatus.cancelled)) {
            logger.debug(`[${request.requestId}] Cancelled by the server.`);
            yield {
                status: APIStatus.cancelled,
            };
            return;
        }
        logger.warn(`[${request.requestId}] Next edit failed: ${e}.`);
        nextEditSessionEventReporter.reportEvent(
            request.requestId,
            undefined,
            Date.now(),
            NextEditSessionEventName.ErrorAPIError,
            NextEditSessionEventSource.Unknown
        );
        yield {
            status: APIStatus.unknown,
        };
        return;
    } finally {
        offsetTracker.dispose();
    }
}

/** Tracks how the workspace has changed since the request was started. */
class PendingOffsetTracker extends DisposableService {
    /** Tracks the character offset delta per document while a request is processing. */
    private readonly _pendingEdits: Map<string, vscode.TextDocumentContentChangeEvent[]> =
        new Map();

    constructor(
        /** The request id. */
        public readonly requestId: string,
        /** The workspace manager (used to resolve qualified path names). */
        private readonly workspaceManager: WorkspaceManager
    ) {
        super();
        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument((event) => {
                // We sometimes get empty events on file saves, etc. Ignore them.
                if (event.contentChanges.length === 0) {
                    return;
                }
                // We track changes in files that are part of the workspace or are
                // untitled.
                const qualifiedPathName = this.workspaceManager.safeResolvePathName(
                    event.document.uri
                );
                if (!qualifiedPathName) {
                    return;
                }

                const edits = this._pendingEdits.get(qualifiedPathName.absPath) ?? [];
                edits.push(...event.contentChanges);
                this._pendingEdits.set(qualifiedPathName.absPath, edits);
            })
        );
    }

    updateWithPendingEdits(
        qualifiedPathName: QualifiedPathName,
        charRange: CharRange
    ): CharRange | undefined {
        const changes = this._pendingEdits.get(qualifiedPathName.absPath) ?? [];
        // Replay the changes in order to update the range.
        for (const change of changes) {
            const changeDelta = change.text.length - change.rangeLength;
            if (change.rangeOffset + change.rangeLength < charRange.start) {
                charRange.start += changeDelta;
                charRange.stop += changeDelta;
            } else if (change.rangeOffset <= charRange.stop) {
                // The change is within the range, so there is no safe way to update
                // the range.
                // Insertions are zero-length, so two insertions at the same point
                // will have equality here.
                return undefined;
            }
        }
        return charRange;
    }
}

/**
 * Do our best to find a document for the path in the response.
 *
 * We make the following assumptions:
 * 1. Each request is tied to a single workspace root, and we should try to resolve
 *    paths relative to that root.
 * 2. The one exception are "untitled" documents that have never been saved and don't
 *    have a path per-se. For these, we send and receive a path like "untitled-1", and
 *    look at open editors to resolve the document.
 *
 * NOTE(arun): Until we try to guess the workspace root from the recently changed events
 * we try to see if the path uniquely matches one of the workspace roots.
 */
async function findDocument(
    responsePath: string,
    workspaceRootPath: string | undefined,
    workspaceManager: WorkspaceManager
): Promise<vscode.TextDocument | undefined> {
    if (!workspaceRootPath) {
        // When we don't have a workspace root, try to guess it if there's a unique
        // one that contains this path.
        const allCandidatePathnames = workspaceManager.getAllQualifiedPathNames(responsePath);
        if (allCandidatePathnames.length === 1) {
            workspaceRootPath = allCandidatePathnames[0].rootPath;
        }
        // There are no or multiple roots that contain this path and we don't know which
        // one to use. We'll see if we can resolve it from the open editors.
    }
    if (workspaceRootPath) {
        // When we know the workspace root, try to open the document relative to it.
        const uri = vscode.Uri.file(joinPath(workspaceRootPath, responsePath));
        const doc = await vscode.workspace.openTextDocument(uri);
        if (doc) {
            return doc;
        }
    }
    // Otherwise, try to see if the path matches any open documents, which
    // by definition must be open in an editor.
    return vscode.window.visibleTextEditors.find((editor) => {
        return editor.document.uri.fsPath === responsePath;
    })?.document;
}

async function getDiagnostics(
    diagnosticsManager: DiagnosticsManager,
    root: string | undefined,
    blobNameCalculator: BlobNameCalculator,
    workspaceManager: WorkspaceManager
): Promise<Diagnostic[]> {
    const diagnostics = await diagnosticsManager.getMostRecentDiagnostics(
        NUM_DIAGNOSTICS_TO_SEND,
        MAX_NUM_DIAGNOSTICS_PER_FILE,
        root
    );
    const cachedBlobNames = new Map<string, string>();
    const results: Array<Diagnostic | undefined> = await Promise.all(
        diagnostics.map(async (diag) => {
            let path = diag.uri.path;
            if (root && path.startsWith(root)) {
                path = path.substring(root.length);
            }
            let severityString;
            switch (diag.diagnostic.severity) {
                case vscode.DiagnosticSeverity.Error:
                    severityString = DiagnosticSeverity.Error;
                    break;
                case vscode.DiagnosticSeverity.Warning:
                    severityString = DiagnosticSeverity.Warning;
                    break;
                case vscode.DiagnosticSeverity.Information:
                    severityString = DiagnosticSeverity.Information;
                    break;
                case vscode.DiagnosticSeverity.Hint:
                    severityString = DiagnosticSeverity.Hint;
                    break;
            }
            if (!cachedBlobNames.has(path)) {
                cachedBlobNames.set(
                    path,
                    blobNameCalculator.calculateNoThrow(
                        path,
                        (await viewTextDocument(diag.uri.fsPath)).getText()
                    )
                );
            }

            const currentBlobName = cachedBlobNames.get(path);
            if (!currentBlobName) {
                return undefined;
            }

            const qpn = workspaceManager.safeResolvePathName(diag.uri);
            if (!qpn) {
                return undefined;
            }
            const indexedBlobName = workspaceManager.getBlobName(qpn);
            if (!indexedBlobName) {
                return undefined;
            }
            /* eslint-disable @typescript-eslint/naming-convention */
            const result: Diagnostic = {
                location: {
                    path: path,
                    line_start: diag.diagnostic.range.start.line,
                    line_end: diag.diagnostic.range.end.line,
                },
                message: diag.diagnostic.message,
                severity: severityString,
                current_blob_name: currentBlobName,
                blob_name: indexedBlobName,
                char_start: diag.charStart,
                char_end: diag.charEnd,
            };
            return result;
            /* eslint-enable @typescript-eslint/naming-convention */
        })
    );
    return results.filter((diag) => diag !== undefined);
}
