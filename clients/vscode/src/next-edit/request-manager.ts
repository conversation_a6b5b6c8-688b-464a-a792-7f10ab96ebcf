import { Observable } from "@augment-internal/sidecar-libs/src/utils/observable";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

import { LineRange } from "../utils/ranges";
import type {
    IEditSuggestion,
    NextEditMode,
    NextEditResultInfo,
    NextEditScope,
    UUID,
} from "./next-edit-types";

/**
 * Manages the lifecycle of next-edit requests.
 *
 * This class is intended to be a singleton shared by all features that make next-edit
 * requests; this is important because the backend expects exactly one active request
 * per session. Subsequent requests of higher priority (e.g. FOREGROUND > BACKGROUND)
 * will cancel prior ones.
 *
 * Multiple callers can enqueue requests in the manager; in the typical case, a single
 * listener (the `SuggestionManager`) consumes the responses from all callers.
 *
 * Next edit requests can be long-lived because they stream suggestions from multiple
 * locations. To ensure that different features are able to get their requests
 * completed, we maintain a queue, with the following policy:
 *
 * 1. "Foreground" mode requests (which are user-inititated) always supercede background
 *    requests, unless they would result in an equivalent request---e.g., a foreground
 *    "workspace" request should re-use results from a background "workspace" request.
 * 2. For requests in the same mode (foreground/background), requests with different
 *    scopes (file/cursor/workspace) queue up one after another (this is to provide
 *    some fairness for the different feature surfaces).
 * 3. For requests with the same mode and scope, newer requests supercede older ones,
 *    unless they would result in an equivalent request---e.g., a newer "workspace".
 *
 * When the workspace changes in a way that would invalidate the results of ongoing
 * requests (i.e. any form of text document change other than accepting a suggestion),
 * users are expected to call `cancelAll()`.
 */
export interface INextEditRequestManager {
    /**
     * Returns the reason why we should not enqueue a request for the given mode and scope.
     *
     * Otherwise, returns undefined.
     *
     * This is a helper method that can be used to avoid enqueueing requests that would
     * be immediately cancelled.
     */
    shouldNotEnqueueRequestReason(
        qualifiedPathName: IQualifiedPathName | undefined,
        mode: NextEditMode,
        scope: NextEditScope,
        selection?: LineRange
    ): string | undefined;

    /**
     * Enqueue a new request for a given mode and scope.
     *
     * Note that we use the state of the editor when it is processed, and not when it is
     * enqueued.
     *
     * @param qualifiedPathName The path of the file to request next edits for. Can be
     *    undefined for workspace requests.
     * @param mode The mode of the request.
     * @param scope The scope of the request.
     * @param selection The selection to use for the request if explicitly provided.
     * @returns The request ID, or undefined if the request was not enqueued.
     */
    enqueueRequest(
        qualifiedPathName: IQualifiedPathName | undefined,
        mode: NextEditMode,
        scope: NextEditScope,
        selection?: LineRange
    ): UUID | undefined;

    /** Cancel all inflight and pending requests. */
    cancelAll(): void;

    /**
     * Clear all completed requests.
     * @param mode If provided, only clear requests for the given mode.
     */
    clearCompletedRequests(mode?: NextEditMode): void;

    /** True if there is an inflight request. */
    get hasInflightRequest(): boolean;

    /** The number of pending requests. */
    get pendingQueueLength(): number;

    /** The last finished request (which may or may not have completed normally). */
    lastFinishedRequest: Observable<NextEditResultInfo | undefined>;

    /** The last response received. */
    lastResponse: Observable<IEditSuggestion | undefined>;

    /** The last state change event. */
    state: Observable<NextEditRequestManagerState>;
}

export enum NextEditRequestManagerState {
    /** We are waiting for a request to be enqueued. */
    ready = "ready",
    /** We are waiting for an enqueued request to be launched. */
    pending = "pending",
    /** We are waiting for a request to complete. */
    inflight = "inflight",
}
