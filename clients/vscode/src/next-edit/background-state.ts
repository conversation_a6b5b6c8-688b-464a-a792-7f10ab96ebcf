import * as vscode from "vscode";

import { EditSuggestion } from "./suggestion-manager";

/**
 * Represents a suggestion that is "hinted" (i.e. highlighted).
 * The isNext field indicates whether the suggestion is the next or previous one.
 * This is particularly useful when the next suggestion wraps around the document
 * and so appears before the cursor.
 */
export class HintedSuggestion {
    constructor(
        public readonly suggestion: EditSuggestion,
        public readonly isNext: boolean
    ) {}

    public toString() {
        return "hinted-suggestion";
    }
}

/**
 * Represents the normal state of the background suggestions.
 *
 * The suggestion is the "hinted" one, if any.
 */
export class NoSuggestions {
    constructor() {}

    // Let's provide a convenience getter so all states have the same interface.
    public get suggestion() {
        return undefined;
    }

    public toString() {
        return "no-suggestions";
    }
}

export class Hinting {
    constructor(
        public readonly suggestion: EditSuggestion,
        public readonly isNext: boolean
    ) {}

    public toString() {
        return "hinting";
    }

    public get hintedSuggestion() {
        return new HintedSuggestion(this.suggestion, this.isNext);
    }
}

/**
 * Represents a suggestion that is currently being previewed.
 */
export class BeforePreview {
    constructor(public readonly suggestion: EditSuggestion) {}
    public toString() {
        return "before-preview";
    }
}

/**
 * Represents a suggestion that was just accepted and that we are reverse-previewing.
 */
export class AfterPreview {
    constructor(public readonly suggestion: EditSuggestion) {}
    public toString() {
        return "after-preview";
    }
}

/**
 * Represents a suggestion whose acceptance we are currently "animating".
 *
 * In animation mode, when you go to the next suggestion, we immediately
 * enter preview mode and start a worker that will accept the suggestion
 * after a delay (which is canceled if the cursor or text change).
 */
export class Animating {
    constructor(
        public readonly suggestion: EditSuggestion,
        /**
         * The cursor when we started the animation.
         * We need this because we create the animation in open(), which is
         * almost immediately followed by the selection change event that would
         * clear the animation.  We thus store the cursor when we started the
         * animation and only clear the animation if the cursor is not the same.
         */
        public readonly selection: vscode.Selection,
        /** The timeout to clear the animation. */
        public readonly timeout: NodeJS.Timeout
    ) {}
    public toString() {
        return "animating";
    }
}

/**
 * The state of the background suggestions.
 */
export type BackgroundState = NoSuggestions | Hinting | BeforePreview | AfterPreview | Animating;

/**
 * Compares two states for equality.
 */
export function statesAreEqual(state1: BackgroundState, state2: BackgroundState): boolean {
    if (state1 instanceof NoSuggestions && state2 instanceof NoSuggestions) {
        return true;
    }
    if (state1 instanceof Hinting && state2 instanceof Hinting) {
        return state1.suggestion.equals(state2.suggestion) && state1.isNext === state2.isNext;
    }
    if (
        (state1 instanceof BeforePreview && state2 instanceof BeforePreview) ||
        (state1 instanceof AfterPreview && state2 instanceof AfterPreview)
    ) {
        return state1.suggestion.equals(state2.suggestion);
    }
    if (state1 instanceof Animating && state2 instanceof Animating) {
        return (
            state1.suggestion.equals(state2.suggestion) &&
            state1.selection.isEqual(state2.selection) &&
            state1.timeout === state2.timeout
        );
    }
    return false;
}
