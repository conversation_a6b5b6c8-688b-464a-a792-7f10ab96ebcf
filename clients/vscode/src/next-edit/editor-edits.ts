import { Observable } from "@augment-internal/sidecar-libs/src/utils/observable";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { debounce } from "lodash";
import * as vscode from "vscode";

import { AugmentConfigListener, ConfigChanges } from "../augment-config-listener";
import { NextEditBackgroundCodeLensProvider } from "../code-lens/next-edit-background";
import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { fileExists } from "../utils/fs-utils";
import { KeybindingWatcher } from "../utils/keybindings";
import { isNotebookUri } from "../utils/notebook";
import { LineRange } from "../utils/ranges";
import { toLineRange, toVSCodeRange } from "../utils/ranges-vscode";
import * as headWatcher from "../vcs/watcher/head-change-watcher";
import * as stashWatcher from "../vcs/watcher/stash-watcher";
import { pathNameToAbsPath } from "../workspace/types";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { BackgroundDecorationManager } from "./background-decoration-manager";
import {
    AfterPreview,
    Animating,
    BackgroundState,
    BeforePreview,
    Hinting,
    NoSuggestions,
    statesAreEqual,
} from "./background-state";
import { BackgroundNextEditsCodeActionProvider } from "./code-action-provider";
import { CompletionVisibilityWatcher } from "./completion-visibility-watcher";
import { BackgroundNextEditsHoverProvider } from "./hover-provider";
import { KeybindingStatus } from "./keybinding-state";
import { NextEditConfigManager, NextEditWorkspaceConfigKey } from "./next-edit-config-manager";
import {
    NextEditMode,
    NextEditScope,
    NextEditSessionEventName,
    NextEditSessionEventSource,
    SuggestionState,
} from "./next-edit-types";
import { INextEditRequestManager } from "./request-manager";
import { EditSuggestion, SuggestionChangedEvent, SuggestionManager } from "./suggestion-manager";
import { currentlyShowingDiffOrMerge, isAccepted, isFreshChange } from "./utils";

/** The last visible ranges we saw. */
type LastVisibleRanges = {
    uri: vscode.Uri;
    visibleRanges: readonly LineRange[];
};

/**
 * Actions that can be taken on the current editor.
 */
export interface IEditorEditActions {
    accept: (
        eventSource?: NextEditSessionEventSource,
        hideHover?: boolean,
        hoveredSuggestionId?: string,
        preserveFocus?: boolean
    ) => void;
    acceptSuggestion: (
        suggestion: EditSuggestion,
        eventSource?: NextEditSessionEventSource,
        hideHover?: boolean,
        preserveFocus?: boolean
    ) => void;
    acceptAllSuggestionsInFile: (
        qualifiedPathName: IQualifiedPathName,
        eventSource?: NextEditSessionEventSource
    ) => void;
    acceptAllSuggestions: (eventSource?: NextEditSessionEventSource) => void;
    reject: (eventSource?: NextEditSessionEventSource, hoveredSuggestionId?: string) => void;
    rejectAllSuggestionsInFile: (
        qualifiedPathName: IQualifiedPathName,
        eventSource?: NextEditSessionEventSource
    ) => void;
    undoAcceptSuggestion: (
        suggestion?: EditSuggestion,
        eventSource?: NextEditSessionEventSource
    ) => void;
    undoAllSuggestionsInFile: (
        qualifiedPathName: IQualifiedPathName,
        eventSource?: NextEditSessionEventSource
    ) => void;
    rejectAllSuggestions: (eventSource?: NextEditSessionEventSource) => void;
    /** Dismiss decorations, leaving the editor and suggestion state unchanged. */
    dismiss: (eventSource?: NextEditSessionEventSource) => void;
    /**
     * Contextually, dismiss decorations or reject suggestion based on state.
     * We use this e.g., to map keybindings.
     */
    dismissOrReject: (eventSource?: NextEditSessionEventSource) => void;
    gotoNextSmart: (eventSource?: NextEditSessionEventSource) => void;
    next: (eventSource?: NextEditSessionEventSource) => void;
    previous: (eventSource?: NextEditSessionEventSource) => void;
    toggleHoverDiff: (
        eventSource?: NextEditSessionEventSource,
        hoveredSuggestionId?: string
    ) => void;
    openSuggestionAt: (uri: vscode.Uri, line: number) => void;
}

/**
 * A class that manages next edit suggestions in an editor.
 * This includes managing decorations, hovers, accept/reject/next, etc.
 */
export class EditorNextEdits extends DisposableService implements IEditorEditActions {
    private static readonly _waitForAcceptTimeoutMs = 1000;
    private static readonly _applySuggestionDelayMs = {
        fromHover: 50,
        atCursor: 250,
        onScreen: 500,
        offScreen: 750,
        largeBonus: 250,
    };
    private static readonly _largeChangeLineCountThreshold = 3;

    /**
     * How long to wait before showing bottom decorations after a scroll event.
     *
     * A number shorter than 250ms can look janky when scrolling down. A number a lot
     * longer than 250ms takes too long to show the decorations.
     */
    private static readonly _postScrollRenderDelayMs = 250;

    /**
     * After this many usages of "accept" or "next" keybindings we stop showing
     * the keybinding hint in the hover. Thus we also stop counting once we hit this value.
     */
    public static readonly maxKeybindingUsageCount = 30;

    private _logger = getLogger("EditorNextEdits");

    /** Responsible for managing decorations in the current editor. */
    private readonly _decorationManager: BackgroundDecorationManager;

    private readonly _codeActionProvider: BackgroundNextEditsCodeActionProvider;

    /** Tracks the status of whether keybindings for specific commands are active. */
    private readonly _keybindingStatus: KeybindingStatus;

    /**
     * Responsible for managing hovers in the current editor.
     *
     * NOTE(arun): This really should be private, but is made public here so we can
     * access it in tests (which we need to do because of the lack of good mocks).
     */
    public readonly _hoverProvider: BackgroundNextEditsHoverProvider;

    private readonly _codeLensProvider: NextEditBackgroundCodeLensProvider;

    private readonly _state: Observable<BackgroundState>;

    /**
     * The last visible ranges we saw.
     */
    private _lastVisibleRanges: LastVisibleRanges | undefined;

    /**
     * Whether or not to ignore selection change events.
     *
     * We set this true when we change the selection ourselves so that we can ignore
     * the event. When this happens is actually pretty nuanced:
     * - when we open a suggestion and have to move the cursor.
     * - when we accept a suggestion and have to move the cursor (
     *   typically only insertions).
     * - when we _undo_ a suggestion and have to move the cursor (if the cursor isn't
     *   already in the suggestion region)
     */
    private _ignoreSelectionChangeEvents: boolean = false;

    // TODO(arun): We should probably move `drawDecorations` and all of this logic
    // into the decoration manager.
    private readonly _debouncedSetBottomDecorations = debounce(() => {
        this._decorationManager.shouldDrawBottomDecorations.value = true;
        this._drawDecorations();
    }, EditorNextEdits._postScrollRenderDelayMs);

    /**
     * Wait before showing a hover so it dosen't get dismissed by smooth scrolling.
     *
     * The smoothScroll duration appears to be 100-125ms, so we wait for 150ms.
     * https://github.com/search?q=repo%3Amicrosoft%2Fvscode%20smoothScrollDuration&type=code
     */
    private static get _smoothScrollDelayMs(): number {
        return (vscode.workspace.getConfiguration("editor").smoothScrolling ?? false) ? 150 : 0;
    }

    constructor(
        context: vscode.ExtensionContext,
        private readonly workspaceManager: WorkspaceManager,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter,
        keybindingWatcher: KeybindingWatcher,
        private readonly _configListener: AugmentConfigListener,
        private readonly _suggestionManager: SuggestionManager,
        private readonly _requestManager: INextEditRequestManager,
        private readonly _globalState: AugmentGlobalState,
        private readonly _nextEditConfigManager: NextEditConfigManager,
        private readonly _completionVisibilityWatcher: CompletionVisibilityWatcher,
        private readonly _onCursorWithinSuggestion = (_suggestion?: EditSuggestion) => {}
    ) {
        super();
        this._state = new Observable<BackgroundState>(new NoSuggestions(), statesAreEqual);
        this._decorationManager = new BackgroundDecorationManager(
            context,
            workspaceManager,
            keybindingWatcher,
            _configListener,
            _nextEditSessionEventReporter,
            this.isInlineCompletionVisible,
            this.isShowAllHighlightsEnabled
        );
        this.addDisposable(this._decorationManager);
        this._codeActionProvider = new BackgroundNextEditsCodeActionProvider(
            this._suggestionManager,
            this._configListener,
            _nextEditSessionEventReporter
        );

        this._keybindingStatus = this.addDisposable(new KeybindingStatus(this._state));

        this._hoverProvider = new BackgroundNextEditsHoverProvider(
            keybindingWatcher,
            _nextEditSessionEventReporter,
            this._suggestionManager,
            this._state,
            this._configListener,
            this.isInlineCompletionVisible,
            () =>
                (this._globalState.get<number>(GlobalContextKey.nextEditKeybindingUsageCount) ??
                    0) < EditorNextEdits.maxKeybindingUsageCount,
            this._keybindingStatus,
            this._nextEditConfigManager
        );
        this.addDisposable(this._hoverProvider);

        this._codeLensProvider = new NextEditBackgroundCodeLensProvider(
            this._state,
            keybindingWatcher
        );

        // Local disposables:
        this.addDisposable(
            new vscode.Disposable(() => {
                // The order of operations here is important... changing the observable
                // can trigger a decoration redraw, so we clear it first and let that happen. Afterwards
                // we can fully clear out decorations and hide the hover.
                this._state.value = new NoSuggestions();
                // Disposing the observable to clear all listeners.
                this._state.dispose();
                this._decorationManager.decorate([], {});
                this._hoverProvider.hideHover(NextEditSessionEventSource.Command);
            })
        );

        // Disposables for event listeners:
        this.addDisposable(
            vscode.languages.registerCodeActionsProvider("*", this._codeActionProvider)
        );

        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument(this._handleTextDocumentChanged)
        );
        this.addDisposable(vscode.window.onDidChangeActiveTextEditor(this._handleEditorChange));
        this.addDisposable(
            vscode.window.onDidChangeTextEditorSelection(this._handleTextEditorSelectionChanged)
        );
        // We need to listen to API responses and add them to the suggestion manager so that we can
        // support foreground requests.
        // TODO: We might want to move this logic into request manager or a new component.
        this.addDisposable(
            new vscode.Disposable(
                this._requestManager.lastResponse.listen((suggestion) => {
                    suggestion &&
                        this._suggestionManager.add(
                            [EditSuggestion.from(suggestion)],
                            suggestion.mode === NextEditMode.Forced
                        );
                })
            )
        );
        this.addDisposable(
            this._suggestionManager.onSuggestionsChanged((event) => {
                void this._handleSuggestionsChanged(event);
            })
        );

        this.addDisposable(
            vscode.window.onDidChangeTextEditorVisibleRanges((event) => {
                if (
                    this.workspaceManager.safeResolvePathName(event.textEditor.document.uri) &&
                    event.textEditor === vscode.window.activeTextEditor &&
                    !currentlyShowingDiffOrMerge() &&
                    // Don't hide the bottom decorations if the line range didn't change,
                    // e.g., if only the characters changed.
                    (!this._lastVisibleRanges ||
                        this._lastVisibleRanges.uri.fsPath !==
                            event.textEditor.document.uri.fsPath ||
                        this._lastVisibleRanges.visibleRanges.length !==
                            event.visibleRanges.length ||
                        this._lastVisibleRanges.visibleRanges.some(
                            (range, index) => !range.equals(toLineRange(event.visibleRanges[index]))
                        ))
                ) {
                    this._drawDecorations();
                    // Make sure we track bottom decoration state.
                    if (!this._configListener.config.nextEdit.useCursorDecorations) {
                        this._decorationManager.shouldDrawBottomDecorations.value = false;
                        this._debouncedSetBottomDecorations();
                    }
                    this._lastVisibleRanges = {
                        uri: event.textEditor.document.uri,
                        visibleRanges: event.visibleRanges.map((r) => toLineRange(r)),
                    };
                }
            })
        );

        this.addDisposable(
            new vscode.Disposable(
                this._suggestionManager.suggestionWasJustAccepted.listen((accepted) => {
                    if (accepted) {
                        this._debouncedSetBottomDecorations.flush();
                        this._drawDecorations();
                    }
                })
            )
        );

        this._configListener.addDisposable(
            this._configListener.onDidChange((change: ConfigChanges) => {
                // if our highlight preference changed, then redraw the decorations.
                if (
                    !!change.newConfig.nextEdit.highlightSuggestionsInTheEditor !==
                    !!change.previousConfig.nextEdit.highlightSuggestionsInTheEditor
                ) {
                    this._drawDecorations();
                }
            })
        );

        this.addDisposable(
            this.workspaceManager.onDidChangeSourceFolderContents((folderRoot: string) => {
                const fileExistsCache = new Map<string, boolean>();
                const deletedFiles = this._suggestionManager.getAllSuggestions().filter((s) => {
                    if (s.qualifiedPathName.rootPath !== folderRoot) {
                        return false;
                    }
                    const relPath = s.qualifiedPathName.relPath;
                    if (!fileExistsCache.get(relPath)) {
                        fileExistsCache.set(
                            relPath,
                            this.workspaceManager.hasFile(s.qualifiedPathName)
                        );
                    }
                    return !fileExistsCache.get(relPath);
                });
                this._suggestionManager.remove(deletedFiles);
            })
        );

        this.addDisposable(
            new vscode.Disposable(
                this._completionVisibilityWatcher.listen(() => {
                    this._drawDecorations();
                })
            )
        );

        if (this._configListener.config.nextEdit.noDiffModeUseCodeLens) {
            this.addDisposable(
                vscode.languages.registerCodeLensProvider("*", this._codeLensProvider)
            );
            this.addDisposable(
                new vscode.Disposable(
                    this._state.listen(() => {
                        this._codeLensProvider.refresh();
                    })
                )
            );
        }

        this.addDisposable(
            new vscode.Disposable(
                this._state.listen(() => {
                    this._drawDecorations();
                })
            )
        );

        // Send events for state transitions.
        this.addDisposable(
            new vscode.Disposable(
                this._state.listen((state) => {
                    let eventName: NextEditSessionEventName | undefined = undefined;
                    if (state instanceof NoSuggestions) {
                        eventName = NextEditSessionEventName.StateTransitionedToNoSuggestions;
                    } else if (state instanceof Hinting) {
                        eventName = NextEditSessionEventName.StateTransitionedToHinting;
                    } else if (state instanceof BeforePreview) {
                        eventName = NextEditSessionEventName.StateTransitionedToBeforePreview;
                    } else if (state instanceof AfterPreview) {
                        eventName = NextEditSessionEventName.StateTransitionedToAfterPreview;
                    } else if (state instanceof Animating) {
                        eventName = NextEditSessionEventName.StateTransitionedToAnimating;
                    }
                    if (eventName !== undefined) {
                        this._nextEditSessionEventReporter.reportEventFromSuggestion(
                            state.suggestion,
                            eventName,
                            NextEditSessionEventSource.Unknown
                        );
                    }
                })
            )
        );

        this.addDisposable(
            vscode.debug.onDidStartDebugSession(() => {
                this._suggestionManager.clear(false);
                this._decorationManager.decorate([], {});
            })
        );
        this.addDisposable(
            vscode.debug.onDidTerminateDebugSession(() => {
                this._handleEditorChange(vscode.window.activeTextEditor);
            })
        );

        this.addDisposable(
            headWatcher.onDidChange(() => {
                this._suggestionManager.clear(false);
                this._decorationManager.decorate([], {});
            })
        );
        this.addDisposable(
            stashWatcher.onDidChange(() => {
                this._suggestionManager.clear(false);
                this._decorationManager.decorate([], {});
            })
        );
    }

    public dismiss(
        eventSource?: NextEditSessionEventSource,
        hideHover: boolean = false,
        clearJustAccepted: boolean = true
    ) {
        if (this._state.value instanceof BeforePreview || this._state.value instanceof Animating) {
            // Dismiss decorations without rejecting the suggestion if user moved their
            // cursor in `BeforePreview`.
            this._state.value = new Hinting(this._state.value.suggestion, true);
            if (clearJustAccepted) {
                this._suggestionManager.clearJustAcceptedSuggestions();
            }
            this._nextEditSessionEventReporter.reportEventWithoutIds(
                NextEditSessionEventName.PreviewDecorationDismissed,
                eventSource ?? NextEditSessionEventSource.Command
            );
        } else if (this._state.value instanceof AfterPreview) {
            // Dismiss decorations without undoing + rejecting the suggestion if user
            // moved their cursor in `AfterPreview`.
            this._state.value = this._getHintedState();
            if (clearJustAccepted) {
                this._suggestionManager.clearJustAcceptedSuggestions();
            }
            this._nextEditSessionEventReporter.reportEventWithoutIds(
                NextEditSessionEventName.ReverseDecorationDismissed,
                eventSource ?? NextEditSessionEventSource.Command
            );
        }
        if (hideHover) {
            this._hoverProvider.hideHover();
        }
    }

    public async dismissOrReject(eventSource?: NextEditSessionEventSource) {
        const editor = vscode.window.activeTextEditor;
        if (this._state.value instanceof AfterPreview) {
            // If we're in reverse-preview mode, undo the suggestion before dismissing
            // highlights.
            await this._undoSuggestions(this._state.value.suggestion);
            this.dismiss(eventSource, true);
        } else if (
            this._state.value instanceof BeforePreview ||
            this._state.value instanceof Animating
        ) {
            this.dismiss(eventSource);
        } else if (
            editor &&
            this._state.value instanceof Hinting &&
            this._hoverProvider.hoverContactCondition(
                this._state.value.suggestion,
                editor.selection.active,
                editor.document
            )
        ) {
            // If you press `esc` while in the line range of a suggestion, only
            // reject that one suggestion.
            this.reject(eventSource, this._state.value.suggestion.result.suggestionId);
        } else {
            // In all other cases, reject all suggestions.
            // NOTE(arun): This is a pretty drastic action, and we want to change this
            // behavior. We're just not sure what to yet.
            this.reject(eventSource);
        }
    }

    private isInlineCompletionVisible = () => {
        return this._completionVisibilityWatcher.maybeInlineCompletionVisible;
    };
    private isShowAllHighlightsEnabled = () => {
        return this._configListener.config.nextEdit.highlightSuggestionsInTheEditor;
    };

    /**
     * Get the next available suggestion.
     *
     * Prefer suggestions in the currently active editor, followed by the best one in
     * other files.
     *
     * TODO: Maybe this should move into the suggestion manager, with cursor position
     * as an argument?
     */
    private nextAvailableSuggestion = (
        isNext: boolean = true,
        neverReturnActiveSuggestion: boolean = false
    ) => {
        return _findNextSuggestion(
            this._suggestionManager.getActiveSuggestions().filter(isFreshChange),
            this._state.value instanceof BeforePreview ||
                this._state.value instanceof AfterPreview ||
                this._state.value instanceof Animating
                ? this._state.value.suggestion
                : undefined,
            !isNext,
            neverReturnActiveSuggestion,
            this._configListener.config.nextEdit.enableGlobalBackgroundSuggestions
        );
    };

    /**
     * The set of active suggestions have changed.
     *
     * This can happen for many reasons:
     * - a text edit invalidated some suggestions.
     * - a suggestion was accepted or rejected.
     * - an inflight request completed and added additional suggestions to the manager.
     *
     * We'll just update the suggestions.
     */
    private _handleSuggestionsChanged = async (event: SuggestionChangedEvent) => {
        this._updateSuggestions(event.newSuggestions, true);

        // If the cursor is inside an undone suggestion, set it as active and show the hover.
        const editor = vscode.window.activeTextEditor;
        const undone =
            editor &&
            event.undone.find(
                (s) =>
                    s.lineRange.contains(editor.selection.active.line) ||
                    s.lineRange.touches(editor.selection.active.line)
            );
        if (undone && this._state.value instanceof AfterPreview) {
            // Asynchronously wait for the suggestion to be undone.
            // This ensures that we'll wait for the selection change events from
            // the undo are resolved before we show the hover and set the state.
            // We _believe_ this reduces the likelihood of the undo decorations suddenly
            // disappearing.
            await this._suggestionManager.suggestionWasJustUndone.waitUntil(
                (v) => v === true,
                EditorNextEdits._waitForAcceptTimeoutMs
            );
            const beforeSelection = this._getSuggestionSelection(undone);
            if (!beforeSelection.isEqual(editor.selection)) {
                // The highlighted range might change after selection
                // (e.g., for insertions), so always ensure the cursor is in a
                // consistent position: the top of the start of the suggestion.
                this._ignoreSelectionChangeEvents = true;
                editor.selection = beforeSelection;
            }
            this._state.value = new BeforePreview(undone);
            // Report that the suggestion was undone while we're in a preview state.
            // Because we don't capture the undo command, this is the most reliable way
            // for us to know that the user undid a suggestion they accepted through
            // Next Edit and not completions. If this is the last event for this
            // suggestion id, it effectively means the suggestion was rejected.
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                this._state.value.suggestion,
                NextEditSessionEventName.UndidAcceptedSuggestion,
                NextEditSessionEventSource.DocumentChanged
            );
            this._hoverProvider.showHover();
        }

        // If the user accepted (or re-did) a suggestion and we're in no-diff mode,
        // reverse-highlight it.
        if (
            editor &&
            event.accepted.length > 0 &&
            (this._state.value instanceof BeforePreview || this._state.value instanceof Animating)
        ) {
            // Asynchronously wait for the suggestion to be accepted.
            // If we don't wait for this, we immediately reverse-highlight this suggestion,
            // and then almost immediately get the document changed event in this file
            // and un-highlight it.  Waiting here lets us do those in the opposite order.
            await this._suggestionManager.suggestionWasJustAccepted.waitUntil(
                (v) => v === true,
                EditorNextEdits._waitForAcceptTimeoutMs
            );
            // We use the last item of the accepted array here because it should be the thing that was
            // most recently accepted. the event.accepted list puts older suggestions first. If you are
            // accepting one by one then the last item should be the one that was accepted most recently.
            // If you are accepting many at once (like accept all in file) then the last item should still
            // be the one of the most recently accepted ones.
            // TODO(navtej): this could be more explicit with an actual defined sequence of some sort.
            const accepted = event.accepted[event.accepted.length - 1];
            const afterSelection = this._getSuggestionSelection(accepted);
            if (!afterSelection.isEqual(editor.selection)) {
                // The highlighted range might change after selection
                // (e.g., for insertions), so always ensure the cursor is in a
                // consistent position: the top of the start of the suggestion.
                this._ignoreSelectionChangeEvents = true;
                editor.selection = afterSelection;
            }
            this._state.value = new AfterPreview(accepted);
            this._hoverProvider.showHover();
        } else if (event.accepted.length > 0) {
            // If we accepted multiple suggestions at once, we don't enter into a
            // after preview state but still want to ignore the next selection
            // so that we don't immediately dismiss the decorations.
            this._ignoreSelectionChangeEvents = true;
        }
    };

    /**
     * The set of active suggestions have changed.
     *
     * This can happen for many reasons:
     * - a text edit invalidated some suggestions.
     * - a suggestion was accepted or rejected.
     * - an inflight request completed and added additional suggestions to the manager.
     *
     * The role of this function is to do the following:
     * 1. Update the "in focus" suggestion (if updateNext).
     * 2. Update the decorations (if updateNext).
     *
     * NOTE(arun): It's unclear when we ever need to call `updateNext = false`. This
     * might be dead code.
     */
    private _updateSuggestions(activeSuggestions: readonly EditSuggestion[], updateNext: boolean) {
        if (currentlyShowingDiffOrMerge()) {
            return;
        }

        const freshEditSuggestions = activeSuggestions.filter(isFreshChange);
        // Get the fresh edit suggestions that a user could go to in the background
        // experience.
        const relevantFreshEditSuggestions = this._configListener.config.nextEdit
            .enableGlobalBackgroundSuggestions
            ? freshEditSuggestions
            : freshEditSuggestions.filter((s) =>
                  s.qualifiedPathName.equals(vscode.window.activeTextEditor?.document.uri)
              );

        if (relevantFreshEditSuggestions.length === 0 && this._state.value instanceof Hinting) {
            // If there are no fresh suggestions, clear the focus suggestion.
            this._state.value = new NoSuggestions();
            this._onCursorWithinSuggestion(undefined);
            return;
        }

        if (
            updateNext &&
            // Only update the state to Hinting if we aren't in a preview state.
            (this._state.value instanceof NoSuggestions || this._state.value instanceof Hinting)
        ) {
            this._state.value = this._getHintedState();
        }
        // We want to draw decorations since the set of suggestions has changed.
        this._drawDecorations();
    }

    private get _isDebugging() {
        return (
            vscode.debug.activeDebugSession !== undefined &&
            !this._configListener.config.nextEdit.allowDuringDebugging
        );
    }

    /** Draw decorations. */
    private _drawDecorations() {
        if (this._isDebugging) {
            // We don't want to show suggestions while debugging.
            this._decorationManager.decorate([], {});
            return;
        }
        // We'll draw a "fresh change" pencil for suggestions that are fresh.
        const freshChanges = this._suggestionManager.getActiveSuggestions().filter(isFreshChange);
        // We'll draw a "just accepted" pencil for suggestions that were just accepted.
        // This also handles "reverse preview" decorations.
        const justAcceptedSuggestions = this._suggestionManager.getJustAcceptedSuggestions();
        // TODO: Pass the state into the decoration manager.
        const hintSuggestion =
            this._state.value instanceof Hinting ? this._state.value : this._getHintedState();
        this._decorationManager.decorate(freshChanges.concat(justAcceptedSuggestions), {
            hintSuggestion:
                hintSuggestion instanceof Hinting ? hintSuggestion.hintedSuggestion : undefined,
            activeSuggestion:
                this._state.value instanceof BeforePreview ||
                this._state.value instanceof AfterPreview ||
                this._state.value instanceof Animating
                    ? this._state.value.suggestion
                    : undefined,
            isAnimating: this._state.value instanceof Animating,
        });
    }

    /**
     * Handle changes to the text.
     *
     * All the logic of actually updating suggestions happens in `SuggestionManager`:
     * the manager figures out if the change event "accepted" any suggestions or not,
     * and updates the state of suggestions to be "stale" if so.
     */
    private _handleTextDocumentChanged = (event: vscode.TextDocumentChangeEvent) => {
        // Ignore empty events.
        if (event.contentChanges.length === 0) {
            return;
        }

        const qualifiedPathName = this.workspaceManager.safeResolvePathName(event.document.uri);

        if (this._isDebugging) {
            return;
        }

        // Ignore notebooks, since they're tricky (they're json on-disk and
        // VSCode events see cells individually).
        if (isNotebookUri(event.document.uri)) {
            return;
        }
        // We don't care about changes to documents outside of the workspace.
        if (!qualifiedPathName) {
            return;
        }

        // We only care about changes in the active editor.
        const editor = vscode.window.activeTextEditor;
        if (!editor || event.document !== editor.document) {
            return;
        }

        this._clearAnimatedApply();

        if (this._state.value instanceof AfterPreview || this._state.value instanceof Animating) {
            this._state.value = new NoSuggestions();
            this._nextEditSessionEventReporter.reportEventWithoutIds(
                NextEditSessionEventName.ReverseDecorationDismissed,
                NextEditSessionEventSource.DocumentChanged
            );
        }
    };

    /**
     * Handle the cursor moving.
     *
     * When the cursor moves, the document hasn't changed, and most suggestions remain
     * fresh, but the "in focus" suggestion will change.
     *
     * We will set an "in focus" suggestion if:
     * - the user hasn't selected text.
     * - there exists a fresh suggestion.
     */
    private _handleTextEditorSelectionChanged = (event: vscode.TextEditorSelectionChangeEvent) => {
        const editor = vscode.window.activeTextEditor;
        if (!editor || event.textEditor.document !== editor.document) {
            return;
        }

        if (currentlyShowingDiffOrMerge()) {
            return;
        }
        if (this._isDebugging) {
            return;
        }

        // Ignore notebooks, since they're tricky (they're json on-disk and
        // VSCode events see cells individually).
        if (isNotebookUri(editor.document.uri)) {
            return;
        }
        const qualifiedPath = this.workspaceManager.safeResolvePathName(
            event.textEditor.document.uri
        );
        if (!qualifiedPath) {
            return;
        }

        this._clearAnimatedApply(editor.selection);

        // TODO(arun): We use the selection here to "ignore" selection changes caused
        // by us. We are going to explicitly track selection movements that should
        // let us get rid of this.

        // Ignore moves that come from commands or undefined, as those are ones we cause
        // ourselves when setting the cursor or from accept/undo.
        const ignoreSelectionChangeEvent =
            !(
                event.kind === vscode.TextEditorSelectionChangeKind.Keyboard ||
                event.kind === vscode.TextEditorSelectionChangeKind.Mouse
            ) && this._ignoreSelectionChangeEvents;
        this._ignoreSelectionChangeEvents = false;

        if (
            (this._state.value instanceof BeforePreview ||
                this._state.value instanceof Animating ||
                this._state.value instanceof AfterPreview) &&
            !ignoreSelectionChangeEvent
        ) {
            // Dismiss decorations when the selection legitimately changes.
            this.dismiss(NextEditSessionEventSource.EditorSelectionChanged);
        } else if (!ignoreSelectionChangeEvent) {
            // The `dismiss` call above will clear the just accepted suggestions, but
            // it's possible for no suggestion to be an an active preview state when
            // suggestions are accepted through the panel. Clear them here.
            this._suggestionManager.clearJustAcceptedSuggestions();
        }

        if (
            this._state.value instanceof NoSuggestions ||
            this._state.value instanceof Hinting ||
            // Moving the cursor doing an animation will cancel the animation.
            // (The cancel call is above this.)
            (this._state.value instanceof Animating &&
                !this._getSuggestionSelection(this._state.value.suggestion).isEqual(
                    editor.selection
                ))
        ) {
            // Mark the closest fresh suggestion as in focus.
            this._state.value = this._getHintedState();
        }
        // Always draw decorations; note that we might draw after nextSuggestion.value
        // changes, so we may be re-drawing.
        this._drawDecorations();
        const suggestion = this._state.value.suggestion;
        const isInsideSuggestion =
            suggestion &&
            editor.selection.isEmpty &&
            this._hoverProvider.hoverContactCondition(
                suggestion,
                editor.selection.active,
                editor.document
            );

        // Report an event when the cursor is inside a suggestion.
        if (isInsideSuggestion) {
            let source = NextEditSessionEventSource.Unknown;
            if (event.kind === vscode.TextEditorSelectionChangeKind.Keyboard) {
                source = NextEditSessionEventSource.Keyboard;
            } else if (event.kind === vscode.TextEditorSelectionChangeKind.Mouse) {
                source = NextEditSessionEventSource.Click;
            } else if (event.kind === vscode.TextEditorSelectionChangeKind.Command) {
                source = NextEditSessionEventSource.Command;
            }
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.CursorInsideSuggestion,
                source
            );
            this._onCursorWithinSuggestion(suggestion);
        } else {
            this._onCursorWithinSuggestion(undefined);
        }
    };

    /**
     * The current editor has changed.
     */
    private _handleEditorChange = (editor: vscode.TextEditor | undefined) => {
        // Dismiss decorations when the editor changes.
        // If we're in a new editor, we'll update state when handling a selection
        // changed event. But, we have to update state if we're in the terminal or
        // output panel.
        this.dismiss(NextEditSessionEventSource.ActiveEditorChanged);
        if (editor) {
            // NOTE(arun): It's unclear when we ever need to call `updateNext = false`.
            // This might be dead code.
            // We need to update suggestions when the editor changes to make sure
            // we treat the new editor as a new file.
            this._updateSuggestions(this._suggestionManager.getActiveSuggestions(), false);
            // We do not want to send a selection changed event, as we'll get one
            // soon but with the correct cursor location.
        }
    };

    /**
     *
     * @returns a promise that is true when a suggestion was accepted, false otherwise.
     */
    public async accept(
        eventSource?: NextEditSessionEventSource,
        hideHover: boolean = true,
        hoveredSuggestionId?: string,
        preserveFocus?: boolean
    ): Promise<boolean> {
        let current; // undefined is allowed - acceptSuggestion will show a message about it
        if (hoveredSuggestionId) {
            current = this._suggestionManager.findSuggestionById(hoveredSuggestionId);
            if (current) {
                this._hoverProvider.hideHover();
                // NOTE(arun): We enter this code path when users click "apply" in
                // a hover without being in a preview state. We want to basically
                // immediately apply the suggestion for them -- we'll do this via
                // animation.
                await this.open(current, {
                    shouldAutoApply: true,
                    animationDelayMs: EditorNextEdits._applySuggestionDelayMs.fromHover,
                    preserveFocus,
                    eventSource,
                });
                return true;
            }
        } else if (
            this._state.value instanceof BeforePreview ||
            this._state.value instanceof Animating ||
            this._state.value instanceof AfterPreview
        ) {
            current = this._state.value.suggestion;
        }

        return this.acceptSuggestion(current, eventSource, hideHover, preserveFocus);
    }

    /**
     *
     * @returns a promise that is true when a suggestion was accepted, false otherwise.
     */

    public async acceptSuggestion(
        current?: EditSuggestion,
        eventSource?: NextEditSessionEventSource,
        hideHover: boolean = true,
        preserveFocus: boolean = false
    ): Promise<boolean> {
        if (!current) {
            void vscode.window.showInformationMessage("No Next Edit to accept.");
            this._nextEditSessionEventReporter.reportEventWithoutIds(
                NextEditSessionEventName.ErrorNoSuggestionToAccept,
                eventSource ?? NextEditSessionEventSource.Command
            );
            return false;
        }
        if (
            this._state.value instanceof AfterPreview &&
            this._state.value.suggestion?.equals(current)
        ) {
            // The suggestion in AfterPreview is already applied, so just dismiss the
            // decorations.
            this.dismiss(eventSource);
            return false;
        }

        if (hideHover) {
            this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
        }
        await this.gotoSuggestion(current, undefined, preserveFocus);
        if (
            vscode.window.activeTextEditor &&
            !current.qualifiedPathName.equals(vscode.window.activeTextEditor.document.uri)
        ) {
            this._logger.debug(
                `Current suggestion ${current.qualifiedPathName.relPath} does not match active document ${vscode.window.activeTextEditor?.document.uri.toString()}`
            );
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                current,
                NextEditSessionEventName.ErrorAcceptSuggestionWrongDocument,
                eventSource ?? NextEditSessionEventSource.Command
            );
            return false;
        } else {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                current,
                NextEditSessionEventName.Accept,
                eventSource ?? NextEditSessionEventSource.Command
            );

            // We can call this function without being in before preview,
            // e.g., if we call it from the panel.
            // In these cases, let's enter before preview now so that
            // we can later detect that and enter after preview.
            if (!(this._state.value instanceof BeforePreview)) {
                this._state.value = new BeforePreview(current);
            }

            void createVimUndoPoint();
            void this._suggestionManager.accept([current]);

            try {
                await this._suggestionManager.suggestionWasJustAccepted.waitUntil(
                    (v) => v === true,
                    EditorNextEdits._waitForAcceptTimeoutMs
                );
            } catch (e) {
                this._logger.debug(`Error waiting for suggestion to be accepted.`);
                return false;
            }
            void this.incrementKeybindingUsageCount(eventSource);
            return true;
        }
    }

    private _acceptSuggestions(
        suggestions: EditSuggestion[],
        eventSource?: NextEditSessionEventSource
    ) {
        if (suggestions.length === 0) {
            void vscode.window.showInformationMessage("No Next Edits to accept.");
            return;
        }
        this._state.value = new NoSuggestions();
        void createVimUndoPoint();
        this._hoverProvider.hideHover(NextEditSessionEventSource.Command);
        for (const suggestion of suggestions) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.Accept,
                eventSource ?? NextEditSessionEventSource.Command
            );
        }
        this._suggestionManager.accept(suggestions);
    }

    /**
     * @param filePath use absolute path
     */
    public acceptAllSuggestionsInFile(
        qualifiedPathName: IQualifiedPathName,
        eventSource?: NextEditSessionEventSource
    ) {
        const filePath = pathNameToAbsPath(qualifiedPathName);
        const suggestions = this._suggestionManager
            .getActiveSuggestions()
            .filter(isFreshChange)
            .filter((s) => filePath === s.qualifiedPathName.absPath);
        this._acceptSuggestions(suggestions, eventSource);
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.AcceptAllInFile,
            eventSource ?? NextEditSessionEventSource.Command
        );
    }

    public acceptAllSuggestions(eventSource?: NextEditSessionEventSource) {
        const suggestions = this._suggestionManager.getActiveSuggestions().filter(isFreshChange);
        this._acceptSuggestions(suggestions, eventSource);
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.AcceptAll,
            eventSource ?? NextEditSessionEventSource.Command
        );
    }

    public reject(eventSource?: NextEditSessionEventSource, hoveredSuggestionId?: string) {
        // Try to get the current suggestion from the hover -- otherwise fall back to
        // the "current suggestion" in focus.
        let current; // undefined is allowed here.
        if (hoveredSuggestionId) {
            current = this._suggestionManager.findSuggestionById(hoveredSuggestionId);
        } else if (
            this._state.value instanceof BeforePreview ||
            this._state.value instanceof AfterPreview ||
            this._state.value instanceof Animating
        ) {
            current = this._state.value.suggestion;
        }
        return this.rejectSuggestion(current, eventSource);
    }

    /**
     *
     * Rejects the specified suggestion and updates the suggestion manager accordingly.
     * @param current The suggestion to reject, if any
     * @param eventSource The source that triggered this rejection (e.g., command, hover)
     */
    public rejectSuggestion(current?: EditSuggestion, eventSource?: NextEditSessionEventSource) {
        this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
        if (
            current &&
            vscode.window.activeTextEditor &&
            !current.qualifiedPathName.equals(vscode.window.activeTextEditor.document.uri)
        ) {
            this._logger.debug(
                `Current suggestion ${current.qualifiedPathName.relPath} does not match active document ${vscode.window.activeTextEditor?.document.uri.toString()}`
            );
        }

        // The reject call below will change the state, so we need to reset it first.
        const previousState = this._state.value;
        if (this._state.value instanceof Animating) {
            this._clearAnimatedApply();
            this._state.value = new NoSuggestions();
        } else {
            this._state.value = new NoSuggestions();
        }

        let suggestions: EditSuggestion[];
        let eventName: NextEditSessionEventName;
        let currentNeedsUndo = false;
        if (current) {
            // If there's a current suggestion, we'll only reject that.
            suggestions = [current];
            this._suggestionManager.reject(suggestions);
            eventName = NextEditSessionEventName.Reject;
            currentNeedsUndo = current.state === SuggestionState.accepted;
        } else {
            // Otherwise, reject everything to stop bothering the user.
            suggestions = this._suggestionManager.getActiveSuggestions().filter(isFreshChange);
            this._suggestionManager.reject(suggestions);
            eventName = NextEditSessionEventName.RejectAll;
        }
        // The state change above will trigger a redraw, but it's synchronous so it happens before
        // the reject.  So let's redraw again.
        this._drawDecorations();

        // Clear any completed forced requests since they show rejected suggestions.
        this._requestManager?.clearCompletedRequests(NextEditMode.Forced);

        for (const suggestion of suggestions) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                eventName,
                eventSource ?? NextEditSessionEventSource.Command
            );
        }

        // If we're in reverse-preview mode, undo the suggestion.
        if (previousState instanceof AfterPreview) {
            void this._undoSuggestions(previousState.suggestion);
        } else if (current && currentNeedsUndo) {
            void this._undoSuggestions(current);
        }
    }

    private _rejectSuggestions(
        suggestions: EditSuggestion[],
        eventSource?: NextEditSessionEventSource
    ) {
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.RejectAll,
            eventSource ?? NextEditSessionEventSource.Command
        );
        this._suggestionManager.reject(suggestions);
        if (suggestions.length === 0) {
            void vscode.window.showInformationMessage("No Next Edits to reject.");
            return;
        }
        this._state.value = new NoSuggestions();
        this._hoverProvider.hideHover(NextEditSessionEventSource.Command);
        this._suggestionManager.reject(suggestions);
        this._requestManager?.clearCompletedRequests(NextEditMode.Forced);
        for (const suggestion of suggestions) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.Reject,
                eventSource ?? NextEditSessionEventSource.Command
            );
        }
    }

    /**
     * @param filePath use absolute path
     */
    public rejectAllSuggestionsInFile(
        qualifiedPathName: IQualifiedPathName,
        eventSource?: NextEditSessionEventSource
    ) {
        const filePath = pathNameToAbsPath(qualifiedPathName);
        const justAccepted = this._suggestionManager
            .getJustAcceptedSuggestions()
            .filter((s) => filePath === s.qualifiedPathName.absPath);
        const suggestions = this._suggestionManager
            .getActiveSuggestions()
            .filter(isFreshChange)
            .filter((s) => filePath === s.qualifiedPathName.absPath)
            .concat(justAccepted);
        this._rejectSuggestions(suggestions, eventSource);
        void this._undoSuggestions(justAccepted);
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.RejectAllInFile,
            eventSource ?? NextEditSessionEventSource.Command
        );
    }

    public rejectAllSuggestions(eventSource?: NextEditSessionEventSource) {
        const justAccepted = this._suggestionManager.getJustAcceptedSuggestions();
        const suggestions = this._suggestionManager
            .getActiveSuggestions()
            .filter(isFreshChange)
            .concat(justAccepted);
        this._rejectSuggestions(suggestions, eventSource);
        void this._undoSuggestions(justAccepted);
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.RejectAll,
            eventSource ?? NextEditSessionEventSource.Command
        );
    }

    private _undoSuggestions(suggestions: EditSuggestion | EditSuggestion[]) {
        if (!Array.isArray(suggestions)) {
            suggestions = [suggestions];
        }
        const workspaceEdit = new vscode.WorkspaceEdit();
        for (const suggestion of suggestions) {
            workspaceEdit.replace(
                vscode.Uri.from({
                    scheme: suggestion.uriScheme,
                    path: suggestion.qualifiedPathName.absPath,
                }),
                new vscode.Range(
                    new vscode.Position(suggestion.afterLineRange().start, 0),
                    new vscode.Position(suggestion.afterLineRange().stop, 0)
                ),
                suggestion.result.existingCode
            );
        }
        return vscode.workspace.applyEdit(workspaceEdit);
    }

    public gotoNextSmart(eventSource?: NextEditSessionEventSource) {
        void this.incrementKeybindingUsageCount(eventSource);

        const hintedSuggestion = this._getHintedState().suggestion;
        if (this._state.value instanceof NoSuggestions) {
            void vscode.window.showInformationMessage("No more suggestions right now.");
            // We hide the hover if there is nothing next, otherwise moving to the next takes care of this.
            // We do it this way to avoid any jitter from the hide process (Which is a trick of moving up and down 1 line)
            this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
            return;
        } else if (this._state.value instanceof AfterPreview && !hintedSuggestion) {
            // This is the last cmd-; in a streak, so just exit the after preview.
            return this.dismiss(eventSource);
        } else if (this._state.value instanceof Animating) {
            // If the user triggers next while we're waiting to accept the suggestion
            // (animation mode), accept it immediately.
            this._clearAnimatedApply();
            return this.accept();
        } else if (hintedSuggestion) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                this._state.value instanceof BeforePreview ||
                    this._state.value instanceof AfterPreview
                    ? this._state.value.suggestion
                    : undefined,
                NextEditSessionEventName.GotoNextSmartTriggeredFrom,
                eventSource ?? NextEditSessionEventSource.Command
            );
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                hintedSuggestion,
                NextEditSessionEventName.GotoNextSmartTriggeredTo,
                eventSource ?? NextEditSessionEventSource.Command
            );
            return this.open(hintedSuggestion, { eventSource });
        } else {
            this._logger.debug("Could not goto hinting.");
        }
    }

    public next(eventSource?: NextEditSessionEventSource) {
        return this._nextOrPrevious(true, eventSource);
    }

    public previous(eventSource?: NextEditSessionEventSource) {
        return this._nextOrPrevious(false, eventSource);
    }

    private _nextOrPrevious(
        isNext: boolean,
        eventSource?: NextEditSessionEventSource,
        warnAboutNoSuggestions: boolean = true
    ) {
        const suggestions = this._suggestionManager.getActiveSuggestions().filter(isFreshChange);
        const nextSuggestion = _findNextSuggestion(
            suggestions,
            this._state.value instanceof BeforePreview ||
                this._state.value instanceof AfterPreview ||
                this._state.value instanceof Animating
                ? this._state.value.suggestion
                : undefined,
            !isNext,
            false, // neverReturnActiveSuggestion
            this._configListener.config.nextEdit.enableGlobalBackgroundSuggestions
        );
        this._nextEditSessionEventReporter.reportEventFromSuggestion(
            this._state.value instanceof BeforePreview || this._state.value instanceof AfterPreview
                ? this._state.value.suggestion
                : undefined,
            isNext
                ? NextEditSessionEventName.NextTriggeredFrom
                : NextEditSessionEventName.PreviousTriggeredFrom,
            eventSource ?? NextEditSessionEventSource.Command
        );
        this._nextEditSessionEventReporter.reportEventFromSuggestion(
            nextSuggestion,
            isNext
                ? NextEditSessionEventName.NextTriggeredTo
                : NextEditSessionEventName.PreviousTriggeredTo,
            eventSource ?? NextEditSessionEventSource.Command
        );
        if (!nextSuggestion) {
            if (warnAboutNoSuggestions) {
                void vscode.window.showInformationMessage("No more suggestions right now.");
            }
            // We hide the hover if there is nothing next, otherwise moving to the next takes care of this.
            // We do it this way to avoid any jitter from the hide process (Which is a trick of moving up and down 1 line)
            this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
            return;
        }

        // We don't want to draw the bottom decoration just to hide it immediately.
        this._decorationManager.shouldDrawBottomDecorations.value = false;
        this._debouncedSetBottomDecorations();

        if (this._state.value instanceof Animating) {
            // If the user triggers next (or previous) while we're waiting to
            // accept the suggestion (animation mode), accept it immediately.
            this._logger.debug("Finishing animation instead of opening suggestion.");
            this._clearAnimatedApply();
            return this.accept();
        }

        void this.incrementKeybindingUsageCount(eventSource);
        return this.open(nextSuggestion, { eventSource });
    }

    public undoAcceptSuggestion(
        suggestion?: EditSuggestion,
        eventSource?: NextEditSessionEventSource
    ) {
        // We provide the suggestion if you've clicked "undo" action in the panel.
        suggestion ??=
            this._state.value instanceof AfterPreview ? this._state.value.suggestion : undefined;
        if (suggestion && suggestion.state === SuggestionState.accepted) {
            void this._undoSuggestions(suggestion);
        } else {
            void vscode.commands.executeCommand("undo");
        }
        this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.UndoAccept,
            eventSource ?? NextEditSessionEventSource.Command
        );
    }

    public undoAllSuggestionsInFile(
        qualifiedPathName: IQualifiedPathName,
        eventSource?: NextEditSessionEventSource
    ) {
        const filePath = pathNameToAbsPath(qualifiedPathName);
        const suggestions = this._suggestionManager
            .getActiveSuggestions()
            .filter(isAccepted)
            .filter((s) => filePath === s.qualifiedPathName.absPath);
        if (suggestions.length === 0) {
            void vscode.window.showInformationMessage("No Next Edits to undo.");
            return;
        }
        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.UndoAllInFile,
            eventSource ?? NextEditSessionEventSource.Command
        );
        void this._undoSuggestions(suggestions);
        this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
    }

    public async toggleHoverDiff(
        eventSource?: NextEditSessionEventSource,
        hoveredSuggestionId?: string
    ) {
        // note we won't use eventSource here, instead the toggleHoverDiff code in hoverProvider has its own.
        await this._nextEditConfigManager.toggleSetting(NextEditWorkspaceConfigKey.showDiffInHover);
        if (hoveredSuggestionId) {
            // we get here if the hover is showing because of a true mouse-hover on a suggestion
            // (not a keyboard action).
            const suggestion = this._suggestionManager.findSuggestionById(hoveredSuggestionId);
            const editor = vscode.window.activeTextEditor;
            if (suggestion && editor) {
                this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
                const existingSelection = editor.selection;
                const suggestionSelection = this._getSuggestionSelection(suggestion);
                if (
                    !this._hoverProvider.hoverContactCondition(
                        suggestion,
                        editor.selection.active,
                        editor.document
                    ) ||
                    !editor.selection.isEqual(suggestionSelection)
                ) {
                    this._ignoreSelectionChangeEvents = true;
                    editor.selection = suggestionSelection;
                }
                this._hoverProvider.showHover();
                editor.selection = existingSelection;
            }
        } else {
            this._hoverProvider.hideHover(eventSource ?? NextEditSessionEventSource.Command);
            this._hoverProvider.showHover();
        }
    }

    // Clear the animation unless the given selection is equal to the cursor
    // when the animation started.
    private _clearAnimatedApply(selection?: vscode.Selection) {
        if (
            this._state.value instanceof Animating &&
            (!selection || !selection.isEqual(this._state.value.selection))
        ) {
            clearTimeout(this._state.value.timeout);
        }
    }

    /**
     * This attempts to go to a suggestion in the ide and also focus on the suggestion.
     * If the editor is not open, it will open it.
     * @returns the animation delay (since we need to compute it before we move the cursor).
     */
    private async gotoSuggestion(
        suggestion: EditSuggestion,
        animationDelayMs: number | undefined = undefined,
        preserveFocus: boolean = false
    ) {
        // 0. If there's an inline completion visible, dismiss it: we should only show
        // one of completions or next edit suggestions at a time. If the user has opened
        // a suggestion, it's because they explicitly asked for it (keybinding, gutter
        // icon, hover click, panel, etc.), which means it should take precedence.
        if (this._completionVisibilityWatcher.maybeInlineCompletionVisible) {
            this._logger.debug("Clearing inline completion before opening next edit suggestion.");
            await vscode.commands.executeCommand("editor.action.inlineSuggest.hide");
        }

        let didChangeFile = false;
        // 1. Make sure the document containing the suggestion is the active editor.
        if (!suggestion.qualifiedPathName.equals(vscode.window.activeTextEditor?.document.uri)) {
            didChangeFile = true;
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.SuggestionGlobalOffsetTextTriggered,
                NextEditSessionEventSource.Command
            );

            const editor = vscode.window.visibleTextEditors.find((editor) =>
                suggestion.qualifiedPathName.equals(editor.document.uri)
            );
            if (editor) {
                await vscode.window.showTextDocument(editor.document, {
                    selection: this._getSuggestionSelection(suggestion),
                    preserveFocus,
                });
            } else {
                // Ensure the file exists, since we may have missed its deletion.
                if (!fileExists(suggestion.qualifiedPathName.absPath)) {
                    void vscode.window.showInformationMessage(
                        `Suggestion for ${suggestion.qualifiedPathName.relPath} is no longer relevant.`
                    );
                    this._suggestionManager.remove(
                        this._suggestionManager
                            .getActiveSuggestions()
                            .filter((s) => s.qualifiedPathName.equals(suggestion.qualifiedPathName))
                    );
                    return undefined;
                }
                await vscode.window.showTextDocument(
                    vscode.Uri.file(suggestion.qualifiedPathName.absPath),
                    {
                        selection: this._getSuggestionSelection(suggestion),
                        preserveFocus,
                    }
                );
            }
        } else if (!preserveFocus) {
            // Make sure the editor has focus.
            await vscode.commands.executeCommand("workbench.action.focusActiveEditorGroup");
        }
        // should be focused now.
        const editor = vscode.window.activeTextEditor;
        if (!editor || !suggestion.qualifiedPathName.equals(editor.document.uri)) {
            this._logger.debug(
                `Unable to go to suggestion in ${suggestion.qualifiedPathName.absPath}.`
            );
            return undefined;
        }

        if (suggestion.lineRange.start >= editor.document.lineCount) {
            this._logger.warn("Trying to move to a line that doesn't exist.");
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.ErrorMovingToLineThatDoesntExist,
                NextEditSessionEventSource.Unknown
            );
        }

        // 2. Make sure the line is visible, but only scroll there if it isn't already visible.
        if (!editor.visibleRanges.some((r) => r.contains(toVSCodeRange(suggestion.lineRange)))) {
            editor.revealRange(
                new vscode.Range(suggestion.lineRange.start, 0, suggestion.lineRange.stop, 0),
                vscode.TextEditorRevealType.InCenterIfOutsideViewport
            );
            // Wait a little for the screen to scroll so that the hover doesn't get
            // dismissed by the scroll.
            await delayMs(EditorNextEdits._smoothScrollDelayMs);
        }

        // Compute the animation delay before we move the cursor.
        if (animationDelayMs === undefined) {
            const atCursor =
                !didChangeFile && suggestion.highlightRange.contains(editor.selection.active.line);
            const onScreen =
                !didChangeFile &&
                editor.visibleRanges.some((r) => r.contains(toVSCodeRange(suggestion.lineRange)));
            animationDelayMs = atCursor
                ? EditorNextEdits._applySuggestionDelayMs.atCursor
                : onScreen
                  ? EditorNextEdits._applySuggestionDelayMs.onScreen
                  : EditorNextEdits._applySuggestionDelayMs.offScreen;
            if (suggestion.lineRange.length > EditorNextEdits._largeChangeLineCountThreshold) {
                // NOTE(arun): We currently only use the "before" line range for animations
                // because that's the code that will be replaced. Using the "after" line
                // range is an unexplored alternative.
                animationDelayMs += EditorNextEdits._applySuggestionDelayMs.largeBonus;
            }
        }

        const suggestionSelection = this._getSuggestionSelection(suggestion);
        if (
            // 3. Make sure the cursor is in the suggestion.
            !this._hoverProvider.hoverContactCondition(
                suggestion,
                editor.selection.active,
                editor.document
            ) ||
            // In no-diff mode, we actually want the cursor to be at the start of
            // the suggestion.
            !editor.selection.isEqual(suggestionSelection)
        ) {
            this._ignoreSelectionChangeEvents = true;
            editor.selection = suggestionSelection;
        }

        return animationDelayMs;
    }

    /**
     * This attempts to open a suggestion in the ide and also focus on the suggestion.
     * If the editor is not open, it will open it.
     * @param suggestion
     * @param {Object} options - options to open with
     * @param {boolean} options.shouldAutoApply - Whether we should "animate" and auto-apply the suggestion.  Defaults to the user's current "animateNoDiffMode" setting.
     * @param {number} options.animationDelayMs - The animation delay to use, if we do not want to use the default.
     * @param {NextEditSessionEventSource} options.eventSource - The event source for the open.
     * @param {boolean} options.preserveFocus - Whether we should preserve focus when opening the suggestion.
     * @returns
     */
    public async open(
        suggestion: EditSuggestion,
        options: {
            shouldAutoApply?: boolean | undefined;
            animationDelayMs?: number | undefined;
            preserveFocus?: boolean | undefined;
            eventSource?: NextEditSessionEventSource;
        } = {}
    ) {
        if (suggestion.state === SuggestionState.stale) {
            this._logger.debug(`Tried to open stale suggestion. ${suggestion.result.suggestionId}`);
            return;
        }
        const animationDelayMs = await this.gotoSuggestion(
            suggestion,
            options.animationDelayMs,
            options.preserveFocus
        );
        if (animationDelayMs === undefined) {
            return;
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            this._logger.debug(
                `Unable to open suggestion in ${suggestion.qualifiedPathName.absPath}.`
            );
            return;
        }

        // We need to hide the hover before opening it, as otherwise it sometimes
        // doesn't close and we get stuck when going to the next / prev suggestions.
        // But, we only do this if it's already open for a different suggestion,
        // since otherwise we get some annoying scrolling from this hack.
        if (
            (this._state.value instanceof BeforePreview ||
                this._state.value instanceof AfterPreview) &&
            !suggestion.equals(this._state.value.suggestion)
        ) {
            await this._hoverProvider.hideHoverAsync(
                options.eventSource ?? NextEditSessionEventSource.Command
            );
        }
        if (suggestion.state === SuggestionState.accepted) {
            // It's possible to go to an accepted suggestion through the panel.
            this._state.value = new AfterPreview(suggestion);
        } else {
            this._state.value = new BeforePreview(suggestion);
        }
        if (
            this._state.value instanceof BeforePreview &&
            (options.shouldAutoApply ?? this.nextEditConfig.enableAutoApply)
        ) {
            // Automatically accept the suggestion after a delay.
            this._clearAnimatedApply();
            this._state.value = new Animating(
                suggestion,
                // TODO(arun): We use the selection here to "ignore" selection changes caused
                // by us. We are going to explicitly track selection movements that should
                // let us get rid of this.
                editor.selection,
                setTimeout(() => {
                    void this.accept(undefined, false, undefined, options.preserveFocus);
                }, animationDelayMs)
            );
        } else {
            // Open the hover.
            this._hoverProvider.showHover();
        }
    }

    public openSuggestionAt(uri: vscode.Uri, line: number) {
        const suggestion = this._suggestionManager.getActiveSuggestions().find((s) => {
            return (
                isFreshChange(s) &&
                s.qualifiedPathName.equals(uri) &&
                (s.lineRange.contains(line) || s.lineRange.touches(line))
            );
        });
        if (suggestion) {
            this._nextEditSessionEventReporter.reportEventFromSuggestion(
                suggestion,
                NextEditSessionEventName.SuggestionOpened,
                NextEditSessionEventSource.GutterClick
            );
            void this.open(suggestion, {
                animationDelayMs: EditorNextEdits._applySuggestionDelayMs.atCursor,
                eventSource: NextEditSessionEventSource.GutterClick,
            });
        } else {
            this._logger.error(`No suggestion found for ${uri.toString()} at line ${line}.`);
            void vscode.window.showInformationMessage(`No suggestion found.`);
            this._nextEditSessionEventReporter.reportEventWithoutIds(
                NextEditSessionEventName.ErrorNoSuggestionFound,
                NextEditSessionEventSource.Command
            );
        }
    }

    // Gets the suggestion for which we show a hint, if any.
    private _getHintedState(): NoSuggestions | Hinting {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return new NoSuggestions();
        }

        const next = this.nextAvailableSuggestion(true);
        if (!next) {
            return new NoSuggestions();
        }
        const prev = this.nextAvailableSuggestion(false);
        if (!prev) {
            return new Hinting(next, true);
        }

        if (
            editor.visibleRanges.some((r) => r.contains(toVSCodeRange(prev.highlightRange))) &&
            !editor.visibleRanges.some((r) => r.contains(toVSCodeRange(next.highlightRange)))
        ) {
            return new Hinting(prev, false);
        }
        return new Hinting(next, true);
    }

    public get state() {
        return this._state.value;
    }

    public addStateListener(listener: (state: BackgroundState, oldState: BackgroundState) => void) {
        return this._state.listen(listener);
    }

    private _getSuggestionSelection(suggestion: EditSuggestion) {
        const startLine = suggestion.previewTargetCursorLine;
        return new vscode.Selection(startLine, 0, startLine, 0);
    }

    private async incrementKeybindingUsageCount(
        eventSource: NextEditSessionEventSource | undefined
    ) {
        if (eventSource !== NextEditSessionEventSource.Keybinding) {
            return;
        }
        const currentValue: number =
            this._globalState.get(GlobalContextKey.nextEditKeybindingUsageCount) ?? 0;
        if (currentValue >= EditorNextEdits.maxKeybindingUsageCount) {
            return;
        }
        return this._globalState.update(
            GlobalContextKey.nextEditKeybindingUsageCount,
            currentValue + 1
        );
    }

    private get nextEditConfig() {
        return this._nextEditConfigManager.config;
    }
}

/** Attempt to create an undo point for Vim users. */
async function createVimUndoPoint() {
    // NOTE(arun): The VIM extension (`vscodevim.vim`) lives in the user's local machine,
    // so there isn't an easy way to check for it if we're on a remote machine. We
    // can't do this check because no errors are thrown if the command doesn't exist.

    // NOTE(arun): We are not currently able to explicitly create an undo point for vim,
    // but after digging into the vscode-vim codebase it appears that undo points are
    // created when there's a mode change. We don't know which mode they are in before
    // this command so we'll switch between normal and insert to force a change. It's
    // also reasonable to leave them in "Normal" mode afterwards.
    try {
        await vscode.commands.executeCommand("extension.vim_escape");
        await vscode.commands.executeCommand("extension.vim_insert");
        await vscode.commands.executeCommand("extension.vim_escape");
    } catch {
        // If the command doesn't exist, we'll just ignore it.
    }
}

function _findNextSuggestion(
    suggestions: EditSuggestion[],
    activeSuggestion: EditSuggestion | undefined,
    isReverse: boolean,
    neverReturnActiveSuggestion: boolean,
    enableGlobalBackgroundSuggestions: boolean
) {
    const reverser = isReverse ? -1 : 1;
    if (suggestions.length === 0) {
        return undefined;
    }
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        return undefined;
    }
    const cursorRange = editor.selection;
    if (!cursorRange) {
        return undefined;
    }
    const cursorLineRange = toLineRange(cursorRange);

    // Filter suggestions to only those in the current file,
    // and, if neverReturnActiveSuggestion is true, exclude the active suggestion
    let filteredSuggestions = suggestions.filter(
        (v) =>
            v.qualifiedPathName.equals(editor.document.uri) &&
            (!neverReturnActiveSuggestion || !v.equals(activeSuggestion))
    );

    // If there are no suggestions in the current file, use the best one in any file.
    if (filteredSuggestions.length === 0 && enableGlobalBackgroundSuggestions) {
        // We use the best suggestion here both to try to offer a good suggestion
        // and to keep the sort as stable as possible.
        return (
            suggestions
                // The active suggestion should never be in another file,
                // but filter it out just in case.
                .filter((s) => !neverReturnActiveSuggestion || !s.equals(activeSuggestion))
                // Only return WORKSPACE suggestions in other files. This prevents
                // suggestions FILE from a file following the user around as they go
                // to another file.
                .filter((s) => s.scope === NextEditScope.Workspace)
                .reduce(
                    (a: EditSuggestion | undefined, b: EditSuggestion) =>
                        a != null && a.result.localizationScore > b.result.localizationScore
                            ? a
                            : b,
                    undefined
                )
        );
    }

    // once again check for the active one and exclude it if needed
    if (neverReturnActiveSuggestion) {
        filteredSuggestions = filteredSuggestions.filter((s) => !s.equals(activeSuggestion));
    }

    const sorted = filteredSuggestions.sort(
        (a, b) => reverser * a.highlightRange.compareTo(b.highlightRange)
    );
    // If there's an active suggestion, do the comparison based on the active, otherwise
    //   do the comparison based on the cursor.
    // When comparing against the cursor, since there is no active,
    //   if the cursor is inside the suggestion, then it should be the next one.
    if (activeSuggestion) {
        return (
            sorted.find(
                (s) => reverser * s.highlightRange.compareTo(activeSuggestion.highlightRange) > 0
            ) ?? sorted[0]
        );
    }
    return (
        sorted.find(
            (s) =>
                reverser * s.highlightRange.compareTo(cursorLineRange) >= 0 ||
                s.previewBoxRange(editor.document).contains(cursorRange.active.line) ||
                s.previewBoxRange(editor.document).contains(cursorRange.anchor.line)
        ) ?? sorted[0]
    );
}
