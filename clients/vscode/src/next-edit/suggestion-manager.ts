/**
 * `SuggestionManager` manages a collection of next edit suggestions to keep track
 * of acceptances and rejections, and to keep them consistent with text edit events.
 *
 * This manager is used by the different views (background, diff-view, global, etc.).
 */
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { EphemeralObservable } from "../utils/ephemeral-flag";
import { CharRange, LineRange } from "../utils/ranges";
import { utf32Length } from "../utils/unicode";
import {
    type AnnotatedOneLineDiffSpan,
    ChangeType,
    type DiffSpan,
    type IEditSuggestion,
    type NextEditMode,
    type NextEditResult,
    type NextEditScope,
    SuggestionState,
    type UUID,
} from "./next-edit-types";

/** Describes a single suggestion.  */
export class EditSuggestion implements IEditSuggestion {
    /** The type of change this suggestion represents. */
    public readonly changeType: ChangeType;
    /** The range that will be highlighted. */
    // TODO: Use this for decoration.
    public readonly highlightRange: LineRange;

    constructor(
        /** The ID of the request that generated the suggestion. */
        public readonly requestId: UUID,
        /** Mode of the request when this suggestion was generated. */
        public readonly mode: NextEditMode,
        /** Scope of the request when this suggestion was generated. */
        public readonly scope: NextEditScope,
        /** The core suggestion result. */
        public readonly result: NextEditResult,
        /** QualifiedPathName for multi-workspace support. */
        public readonly qualifiedPathName: QualifiedPathName,
        /** line range for the suggestion; updated on text events. */
        public readonly lineRange: LineRange,
        /** The URI scheme of the document. */
        public readonly uriScheme: string = "file",
        /** When the suggestion was generated. */
        public readonly occurredAt: Date = new Date(),
        /** The state of the suggestion. */
        public readonly state: SuggestionState = SuggestionState.fresh
    ) {
        this.changeType = EditSuggestion.determineChangeType(
            this.result.existingCode,
            this.result.suggestedCode
        );
        if (this.changeType === ChangeType.modification) {
            // there may be whole lines inserted with unchanged lines in-between, in that case
            // we should treat it as an insertion rather than a modification. We don't do
            // for deletions because highlighting an existing section of code all red
            // makes it look like you are deleting the whole section of code.
            const annotatedChangeSpans = this.makeOneLineDiffSpans().filter(
                (span) => span.type !== ChangeType.noop
            );
            if (
                annotatedChangeSpans.every(
                    (span) => span.type === ChangeType.insertion && span.updated.isWholeLine
                )
            ) {
                this.changeType = ChangeType.insertion;
            }
        }
        if (this.changeType === ChangeType.insertion && this.lineRange.start > 0) {
            // We render highlights for insertions (that happen between lines
            // and have 0-length ranges) at the end of the previous line.
            // But multiline insertions should keep the longer end range.
            this.highlightRange = new LineRange(
                this.lineRange.start - 1,
                this.lineRange.stop -
                    (this.lineRange.stop > this.lineRange.start &&
                    this.result.existingCode.indexOf("\n") === -1
                        ? 1
                        : 0)
            );
        } else if (
            this.changeType === ChangeType.insertion &&
            this.lineRange.start === 0 &&
            this.lineRange.stop === 0
        ) {
            // There is one edge case: we have an insertion right at the beginning
            // of the file. In this case, we highlight the first line.
            this.highlightRange = new LineRange(0, 1);
        } else {
            this.highlightRange = this.lineRange;
        }
    }

    /**
     * This is the line range for the position the cursor is allowed to be in
     * and be considered "inside" the suggestion. Generally this is the highlightRange
     * of the suggestion plus the line above, unless the suggestion starts at
     * line 0, then it will just be the same as the highlightRange. This
     * function also accounts for whether we are in the "before" mode or "after"
     * mode.
     * Note that this is display logic and ideally would not be here but it is
     * used by several parts of the next edit system where we have the suggestion
     * object as the main source of data.
     * @param doc
     * @returns LineRange
     */
    public previewCursorRange(doc: vscode.TextDocument): LineRange {
        let range = this.previewBoxRange(doc);
        return new LineRange(Math.max(0, range.start - 1), range.stop);
    }

    /**
     * This is the line range for drawing the preview box.
     * This is display logic and ideally would not be here but it is used by
     * several parts of the next edit system where we have the suggestion
     * object as the main source of data.
     * @param doc
     * @returns LineRange
     */
    public previewBoxRange(doc: vscode.TextDocument): LineRange {
        const isAccepted = this.state === SuggestionState.accepted;

        let range = isAccepted ? this.afterLineRange(doc) : this.highlightRange;

        // This is a reversal of the logic used when computing the highlightRange for
        // insertions. But now we are doing the "After" range for deletions.
        if (isAccepted) {
            if (this.changeType === ChangeType.deletion && range.start > 0) {
                range = new LineRange(
                    range.start - 1,
                    range.stop -
                        (range.stop > range.start && this.result.suggestedCode.indexOf("\n") === -1
                            ? 1
                            : 0)
                );
            } else if (
                this.changeType === ChangeType.deletion &&
                range.start === 0 &&
                range.stop === 0
            ) {
                range = new LineRange(0, 1);
            }
        }
        return range;
    }

    /**
     * This is the line we want the cursor to be on when we are showing the preview box.
     * This is display logic, but we want to keep it next to previewCursorRange since they
     * are very related. Note that we want to be able to compute this without a handle
     * on the document.
     */
    public get previewTargetCursorLine(): number {
        return Math.max(0, this.lineRange.start - 1);
    }

    public toString(): string {
        return `EditSuggestion(${this.qualifiedPathName.relPath}:${this.lineRange.toString()})`;
    }

    /** Check if this suggestion is equal to another. */
    public equals(other: EditSuggestion | undefined) {
        return this.result.suggestionId === other?.result.suggestionId;
    }

    /** Order suggestions by path name, and then by line range. */
    public compareTo(other: EditSuggestion): number {
        if (this.qualifiedPathName.equals(other.qualifiedPathName)) {
            return this.lineRange.compareTo(other.lineRange);
        } else {
            return this.qualifiedPathName.relPath.localeCompare(other.qualifiedPathName.relPath);
        }
    }

    public makeOneLineDiffSpans(): AnnotatedOneLineDiffSpan[] {
        return EditSuggestion.makeOneLineDiffSpans(
            this.result.diffSpans,
            this.result.existingCode,
            this.result.suggestedCode,
            this.lineRange
        );
    }

    public static determineChangeType(oldText: string, newText: string): ChangeType {
        if (oldText === newText) {
            return ChangeType.noop;
        } else if (oldText === "") {
            return ChangeType.insertion;
        } else if (newText === "") {
            return ChangeType.deletion;
        } else {
            return ChangeType.modification;
        }
    }

    // this is static so that we can keep the input/output straight and know it does not
    // touch any of the instance state.
    private static makeOneLineDiffSpans(
        diffSpansInput: DiffSpan[],
        oldText: string,
        newText: string,
        lineRange: LineRange
    ): AnnotatedOneLineDiffSpan[] {
        if (!diffSpansInput) {
            return [];
        }
        const diffSpans = diffSpansInput.sort((a, b) => a.original.start - b.original.start);
        const result: AnnotatedOneLineDiffSpan[] = [];

        let currentOriginalLineNumber = lineRange.start;
        let currentUpdatedLineNumber = lineRange.start;
        let previousOriginalSpanHadNewline = true;
        let previousUpdatedSpanHadNewline = true;
        for (const span of diffSpans) {
            const oldSpanText = oldText.slice(span.original.start, span.original.stop);
            const newSpanText = newText.slice(span.updated.start, span.updated.stop);
            const changeType = EditSuggestion.determineChangeType(oldSpanText, newSpanText);
            const oldLines = oldSpanText.split("\n");
            const newLines = newSpanText.split("\n");
            const maxLines = Math.max(oldLines.length, newLines.length);

            let oldOffset = span.original.start;
            let newOffset = span.updated.start;

            for (let i = 0; i < maxLines; i++) {
                let oldLine = oldLines.length > i ? oldLines[i] : "";
                let newLine = newLines.length > i ? newLines[i] : "";
                if (i < oldLines.length - 1) {
                    oldLine += "\n";
                }
                if (i < newLines.length - 1) {
                    newLine += "\n";
                }

                if (oldLine === "" && newLine === "") {
                    continue;
                }

                const type = changeType;

                result.push({
                    original: {
                        text: oldLine,
                        charRange: new CharRange(oldOffset, oldOffset + oldLine.length),
                        line: currentOriginalLineNumber,
                        isWholeLine: previousOriginalSpanHadNewline && oldLine.endsWith("\n"),
                    },
                    updated: {
                        text: newLine,
                        charRange: new CharRange(newOffset, newOffset + newLine.length),
                        line: currentUpdatedLineNumber,
                        isWholeLine: previousUpdatedSpanHadNewline && newLine.endsWith("\n"),
                    },
                    type,
                });
                oldOffset += oldLine.length;
                newOffset += newLine.length;

                if (oldLine.endsWith("\n")) {
                    currentOriginalLineNumber++;
                    previousOriginalSpanHadNewline = true;
                } else {
                    previousOriginalSpanHadNewline = false;
                }
                if (newLine.endsWith("\n")) {
                    currentUpdatedLineNumber++;
                    previousUpdatedSpanHadNewline = true;
                } else {
                    previousUpdatedSpanHadNewline = false;
                }
            }
        }

        // we throw out any truly empty spans we may have injected.
        return result.filter((span) => {
            return span.original.text !== "" || span.updated.text !== "";
        });
    }

    /**
     * Get the line range after the suggestion is applied.
     * If the suggestion is not accepted, returns the current line range.
     * Note: there is some tech debt here -- we think it maybe should not take
     * a document as an argument because this depends on the change having
     * already been applied so that the document is in the expected state.
     */
    public afterLineRange(doc?: vscode.TextDocument): LineRange {
        if (this.state !== SuggestionState.accepted) {
            return this.lineRange;
        }
        if (!doc) {
            return new LineRange(
                this.lineRange.start,
                this.lineRange.stop +
                    this.result.suggestedCode.split("\n").length -
                    this.result.existingCode.split("\n").length
            );
        }
        if (
            this.result.charStart + utf32Length(this.result.suggestedCode) >
            utf32Length(doc.getText())
        ) {
            return this.lineRange;
        }
        return new LineRange(
            doc.positionAt(this.result.charStart).line,
            doc.positionAt(this.result.charStart + utf32Length(this.result.suggestedCode)).line
        );
    }

    /**
     * Check if this suggestion intersects another.
     * We consider two 0-length ranges to be intersecting if they are identical.
     */
    public intersects(other: EditSuggestion): boolean {
        return (
            this.qualifiedPathName.equals(other.qualifiedPathName) &&
            (this.lineRange.equals(other.lineRange) || this.lineRange.intersects(other.lineRange))
        );
    }

    /** Create a new suggestion by replacing some of its fields. */
    public with(updates: Partial<EditSuggestion>) {
        return new EditSuggestion(
            updates.requestId ?? this.requestId,
            updates.mode ?? this.mode,
            updates.scope ?? this.scope,
            updates.result ?? this.result,
            updates.qualifiedPathName ?? this.qualifiedPathName,
            updates.lineRange ?? this.lineRange,
            updates.uriScheme ?? this.uriScheme,
            updates.occurredAt ?? this.occurredAt,
            updates.state ?? this.state
        );
    }

    static from(suggestion: IEditSuggestion): EditSuggestion {
        return new EditSuggestion(
            suggestion.requestId,
            suggestion.mode,
            suggestion.scope,
            suggestion.result,
            QualifiedPathName.from(suggestion.qualifiedPathName),
            new LineRange(suggestion.lineRange.start, suggestion.lineRange.stop),
            suggestion.uriScheme,
            suggestion.occurredAt,
            suggestion.state
        );
    }
}

/** Event type for when the suggestions change. */
export interface SuggestionChangedEvent {
    /** The suggestions prior to this change event. */
    oldSuggestions: EditSuggestion[];
    /** The suggestions after this change event. */
    newSuggestions: EditSuggestion[];
    /** The suggestions that were accepted as part of this change event. Older suggestions first. The most recent should be at the end */
    accepted: EditSuggestion[];
    /** The suggestions that were rejected as part of this change event. */
    rejected: EditSuggestion[];
    /** The suggestions that were undone as part of this change event. */
    undone: EditSuggestion[];
}

/** The interface exposed by the SuggestionManager. */
export interface SuggestionManager {
    /**
     * Register an event listener for suggestions changed event.
     *
     * Returns a disposable that can be used to unregister the event listener.
     * Note that callbacks are called synchronously while processing events, so avoid
     * any long-running tasks here.
     */
    onSuggestionsChanged(
        callback: (event: SuggestionChangedEvent) => void | Promise<void>
    ): vscode.Disposable;

    /**
     * Add new suggestions to the manager.
     *
     * @param suggestions The suggestions to add.
     * @param overrideRejection Whether to override the rejection of the suggestions.
     *   If true, the suggestions will be added to the manager even if they have been
     *   rejected previously, and those rejections will be cleared from the manager.
     *
     * @returns true iff the suggestions were added to the manager.
     */
    add(suggestions: EditSuggestion[], overrideRejection?: boolean): boolean;
    /**
     * Remove suggestions from the manager.
     *
     * @param suggestions The suggestions to remove.
     *
     * @returns true iff the suggestions were removed from the manager.
     */
    remove(suggestions: EditSuggestion[]): boolean;

    /**
     * Clear all suggestions from the manager.
     *
     * @param clearRejectedSuggestions Whether to clear the rejected suggestions.
     * @param mode If provided, only clear suggestions for the given mode.
     */
    clear(clearRejectedSuggestions: boolean, mode?: NextEditMode): void;

    /** Get all suggestions currently being managed. */
    getAllSuggestions(): EditSuggestion[];
    /**
     * Get the currently active suggestions.
     *
     * @returns The currently active suggestions.
     */
    getActiveSuggestions(): EditSuggestion[];
    /**
     * Get the currently rejected suggestions.
     *
     * @returns The currently rejected suggestions.
     */
    getRejectedSuggestions(): EditSuggestion[];
    /**
     * Get the suggestions that were just accepted. With the most recent at the end.
     *
     * @returns The suggestions that were just accepted.
     */
    getJustAcceptedSuggestions(): EditSuggestion[];

    /**
     * Clear the just accepted suggestions.
     *
     * This is used by the editor to update the decorations.
     */
    clearJustAcceptedSuggestions(): void;

    /**
     * Find a suggestion by its ID.
     *
     * @param suggestionId The ID of the suggestion to find.
     * @returns The suggestion with the given ID, or undefined if not found.
     */
    findSuggestionById(suggestionId: string): EditSuggestion | undefined;

    /**
     * Mark suggestions as accepted; update the remaining suggestions in the manager.
     *
     * This method will update the suggestions in the manager, and trigger the
     * `onSuggestionsChanged` event.
     */
    accept(suggestions: EditSuggestion[]): void;
    /**
     * Mark suggestions as rejected; avoid showing/adding suggestion going forward.
     *
     * This method will update the suggestions in the manager, and trigger the
     * `onSuggestionsChanged` event.
     *
     * @param suggestions A list of suggestions to delete.
     * @param rejectionDurationMs If provided, how long this suggestions should stay
     *      rejected.
     */
    reject(suggestions: EditSuggestion[], rejectionDurationMs?: number): void;

    /**
     * Update the suggestions after a text change event.
     *
     * Callers will typically call this method after a text change event.
     *
     * This method will update the suggestions in the manager, and trigger the
     * `onSuggestionsChanged` event.
     *
     * Returns `true` iff this change event includes any accepted suggestions.
     */
    handleChangeEvent(event: vscode.TextDocumentChangeEvent): boolean;

    /**
     * Indicates if a suggestion was "just" accepted.
     *
     * Any subsequent editing or selection movement events will clear this state.
     */
    suggestionWasJustAccepted: EphemeralObservable<boolean>;

    /**
     * Indicates if a suggestion was "just" undone.
     *
     * Any subsequent editing or selection movement events will clear this state.
     */
    suggestionWasJustUndone: EphemeralObservable<boolean>;
}
