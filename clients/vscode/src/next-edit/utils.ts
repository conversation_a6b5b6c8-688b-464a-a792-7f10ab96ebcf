import * as vscode from "vscode";

import { AugmentConfig } from "../augment-config-listener";
import { NextEditUndoAcceptSuggestionCommand } from "../commands/next-edit";
import * as environment from "../utils/environment";
import { KeybindingWatcher } from "../utils/keybindings";
import { ChangeType, type IEditSuggestion, SuggestionState } from "./next-edit-types";
import { EditSuggestion } from "./suggestion-manager";

/**
 * Is NextEdit Available or Allowed for me to use. This does not check if the user
 * setting for next edit is turned on.
 * @param minVersion - from launch darkly feature flags. (vscode-next-edit-min-version)
 * @returns
 */
// TODO migrate this to a NextEditConfigManager
export function isNextEditEnabled(config: AugmentConfig, minVersion: string): boolean {
    return config.nextEdit.enabled ?? environment.isExtensionVersionGte(minVersion);
}

/**
 * Is background mode next edit enabled by feature flags AND by the user's settings
 * @param config
 * @param minVersion
 * @returns
 */
// TODO migrate this to a NextEditConfigManager
export function isNextEditBackgroundEnabled(config: AugmentConfig, minVersion: string): boolean {
    return (
        config.nextEdit.enableBackgroundSuggestions &&
        config.nextEdit.backgroundEnabled && // TODO this is deprecated
        isNextEditEnabled(config, minVersion)
    );
}

export function currentlyShowingDiffOrMerge(): boolean {
    const tabInput = vscode.window.tabGroups.activeTabGroup?.activeTab?.input;
    return (
        tabInput instanceof vscode.TabInputTextDiff ||
        // There doesn't seem to be an API to detect the conflict resolution view,
        // but looking at the debugger and the code this seems to work.
        (tabInput != null &&
            Object.hasOwn(tabInput, "base") &&
            Object.hasOwn(tabInput, "input1") &&
            Object.hasOwn(tabInput, "input2"))
    );
}

export const uniqueSuggestions = (suggestions: (IEditSuggestion | undefined)[]) => {
    const map = new Map<string, IEditSuggestion>();
    for (const suggestion of suggestions.filter(Boolean)) {
        map.set(suggestion!.result.suggestionId, suggestion!);
    }
    return Array.from(map.values());
};

export const or =
    <T>(...predicates: ((arg: T) => boolean)[]) =>
    (value: T) =>
        predicates.some((predicate) => predicate(value));

export function isAccepted(suggestion?: IEditSuggestion | undefined): boolean {
    return suggestion?.state === SuggestionState.accepted;
}

export function isRejected(suggestion?: IEditSuggestion | undefined): boolean {
    return suggestion?.state === SuggestionState.rejected;
}
export function isSettled(suggestion?: IEditSuggestion | undefined): boolean {
    return isAccepted(suggestion) || isRejected(suggestion);
}

export function isFreshChange(
    suggestion?: IEditSuggestion | undefined
): suggestion is EditSuggestion {
    return suggestion?.state === SuggestionState.fresh && suggestion.changeType !== ChangeType.noop;
}

/**
 * Similar to isFreshChange but also includes no-op suggestions.
 * This is useful for cursor movement detection to avoid triggering new requests
 * when the cursor moves to a line with a no-op suggestion.
 */
export function isFreshSuggestion(
    suggestion?: IEditSuggestion | undefined
): suggestion is EditSuggestion {
    return suggestion?.state === SuggestionState.fresh;
}

export function getKeybindingForCommand(
    keybindingWatcher: KeybindingWatcher,
    commandId: string,
    prettyFormat: boolean = false
) {
    let keybinding = keybindingWatcher.getKeybindingForCommand(commandId, prettyFormat);
    if (keybinding) {
        return keybinding;
    }
    // We need to special-case undo and redo, as our watcher can't easily get
    // default keybindings for non-Augment commands.
    if (commandId === NextEditUndoAcceptSuggestionCommand.commandID) {
        if (keybindingWatcher.getSimplifiedPlatform() === "darwin") {
            keybinding = "Cmd+Z";
        } else {
            keybinding = "Ctrl+Z";
        }
        return prettyFormat
            ? KeybindingWatcher.formatKeyboardShortcut(
                  keybinding,
                  keybindingWatcher.getSimplifiedPlatform()
              )
            : keybinding;
    }
    if (commandId === "redo") {
        if (keybindingWatcher.getSimplifiedPlatform() === "darwin") {
            keybinding = "Cmd+Shift+Z";
        } else {
            keybinding = "Ctrl+Y";
        }
        return prettyFormat
            ? KeybindingWatcher.formatKeyboardShortcut(
                  keybinding,
                  keybindingWatcher.getSimplifiedPlatform()
              )
            : keybinding;
    }
    return keybinding;
}
