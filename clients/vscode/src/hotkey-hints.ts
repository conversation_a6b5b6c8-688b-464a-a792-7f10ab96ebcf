import debounce from "lodash/debounce";
import * as vscode from "vscode";

import { FocusAugmentPanel } from "./commands/focus-augment-panel";
import { onCompletionRequest, onCompletionRequestCancelled } from "./completions/completion-events";
import { InlineCompletionProvider } from "./completions/inline-provider";
import { getLogger } from "./logging";
import { DisposableService } from "./utils/disposable-service";
import type { KeybindingWatcher } from "./utils/keybindings";

const COPILOT_HINT_OVERWRITE_DELAY_MS = 5;

export class HotKeyHints extends DisposableService {
    private _logger = getLogger("HotKeyHints");

    private activeCompletion = false;
    private emptyFileHotKeyHintDecorationType!: vscode.TextEditorDecorationType;

    private debouncedDecorations = debounce(
        (editor: vscode.TextEditor) => {
            // Only show hints for files to make sure they don't show up in places like the output panel or notebooks
            if (
                (editor.document.uri.scheme === "file" ||
                    editor.document.uri.scheme === "untitled") &&
                editor.document.getText() === "" &&
                !this.activeCompletion
            ) {
                editor.setDecorations(this.emptyFileHotKeyHintDecorationType, [editor.selection]);
            } else {
                this.hideHints(editor);
            }
        },
        COPILOT_HINT_OVERWRITE_DELAY_MS,
        {
            leading: false,
            trailing: true,
        }
    );

    constructor(
        private _keybindingWatcher: KeybindingWatcher,
        private _inlineCompletionProvider: InlineCompletionProvider | undefined
    ) {
        super();

        this.createDecorationTypes();

        this.addDisposables(...this.setupEmptyFileHint());

        // Handle showing the hint when vscode is opened with an empty file
        // and the Augment extension is still starting up
        const editor = vscode.window.activeTextEditor;
        if (editor && editor.document.getText() === "") {
            this.debouncedDecorations(editor);
        }

        this._logger.info("HotKeyHints initialized");
    }

    private createDecorationTypes() {
        const openChatKeybinding = this._keybindingWatcher.getKeybindingForCommand(
            FocusAugmentPanel.commandID
        );

        let hintText = "";
        if (openChatKeybinding) {
            hintText = `${openChatKeybinding} to open Augment.`;
        } else {
            hintText = "Click the robot icon in the side bar to open Augment.";
        }

        this.emptyFileHotKeyHintDecorationType = vscode.window.createTextEditorDecorationType({
            after: {
                contentText: hintText,
                color: "rgba(150, 150, 150, 0.9)",
                margin: "0 0 0 0.5rem",
            },
        });
    }

    private setupEmptyFileHint() {
        const disposables = [];

        disposables.push({
            dispose: () => {
                this.debouncedDecorations.cancel();
            },
        });

        if (this._inlineCompletionProvider) {
            disposables.push(
                onCompletionRequest((completionRequest) => {
                    const editor = vscode.window.activeTextEditor;
                    if (
                        !editor ||
                        !completionRequest ||
                        completionRequest.completions.length === 0
                    ) {
                        return;
                    }
                    // cancel any pending hint updates
                    this.debouncedDecorations.cancel();
                    // hide any visible hints
                    this.hideHints(editor);
                    // keep track of when a completion is present
                    this.activeCompletion = true;
                })
            );

            disposables.push(
                onCompletionRequestCancelled(() => {
                    this.activeCompletion = false;

                    const editor = vscode.window.activeTextEditor;
                    if (!editor || editor.document.getText() !== "") {
                        return;
                    }

                    this.debouncedDecorations(editor);
                })
            );
        }

        vscode.workspace.onDidCloseTextDocument(() => {
            this.activeCompletion = false;
        });

        disposables.push(
            vscode.window.onDidChangeActiveTextEditor((editor) => {
                if (!editor || editor.document.getText() !== "") {
                    return;
                }

                this.activeCompletion = false;

                this.debouncedDecorations(editor);
            })
        );

        disposables.push(
            vscode.workspace.onDidChangeTextDocument((event) => {
                // reset activeCompletion when a change like an accepted completion happens
                if (event.contentChanges.length > 0) {
                    this.activeCompletion = false;
                }

                // Only show hints for files to make sure they don't show up in places like the output panel or notebooks
                if (
                    event.document.uri.scheme !== "file" &&
                    event.document.uri.scheme !== "untitled"
                ) {
                    return;
                }

                const editor = vscode.window.activeTextEditor;
                // make sure the event is for the active editor
                if (!editor || editor.document.uri.toString() !== event.document.uri.toString()) {
                    return;
                }

                if (editor.document.getText() !== "") {
                    this.hideHints(editor);
                    return;
                }

                if (!this.activeCompletion) {
                    this.debouncedDecorations(editor);
                }
            })
        );
        return disposables;
    }

    private hideHints(editor: vscode.TextEditor) {
        editor.setDecorations(this.emptyFileHotKeyHintDecorationType, []);
    }
}

export class EmptyLineHints extends DisposableService {
    private _logger = getLogger("EmptyLineHints");
    private activeCompletion = false;

    private decorationType!: vscode.TextEditorDecorationType;

    private debouncedDecorations = debounce(
        (editor: vscode.TextEditor) => {
            const cursorPosition = editor.selection.active;
            const line = editor.document.lineAt(cursorPosition.line);
            const lineText = line.text;
            const lastNonWhitespaceIndex =
                lineText.lastIndexOf(" ") !== -1 ? lineText.lastIndexOf(" ") : lineText.length - 1;
            const range = new vscode.Range(
                cursorPosition.line,
                lastNonWhitespaceIndex + 1,
                cursorPosition.line,
                lastNonWhitespaceIndex + 1
            );
            if (
                /\S/.test(lineText) === false &&
                (lineText.trim() === "" || cursorPosition.character === lineText.length)
            ) {
                editor.setDecorations(this.decorationType, [range]);
            } else {
                this.hideHints(editor);
            }
        },
        16,
        {
            leading: false,
            trailing: true,
        }
    );

    constructor(
        private _keybindingWatcher: KeybindingWatcher,
        private _inlineCompletionProvider: InlineCompletionProvider | undefined
    ) {
        super();

        this.createDecorationTypes();

        this.addDisposables(...this.setupEmptyLineHint());
    }

    private createDecorationTypes() {
        let hintText = "";

        const openChatKeybinding = this._keybindingWatcher.getKeybindingForCommand(
            "augment-chat.focus",
            true
        );

        if (openChatKeybinding) {
            hintText = `${openChatKeybinding} to open Augment`;
        }

        this.decorationType = vscode.window.createTextEditorDecorationType({
            after: {
                contentText: hintText,
                color: "rgba(150, 150, 150, 0.5)",
                margin: "0 0 0 1.2rem",
            },
        });
    }

    private setupEmptyLineHint() {
        const disposables = [];

        disposables.push({
            dispose: () => {
                this.debouncedDecorations.cancel();
            },
        });

        if (this._inlineCompletionProvider) {
            disposables.push(
                onCompletionRequest((completionRequest) => {
                    const editor = vscode.window.activeTextEditor;
                    if (
                        !editor ||
                        !completionRequest ||
                        completionRequest.completions.length === 0
                    ) {
                        return;
                    }
                    // cancel any pending hint updates
                    this.debouncedDecorations.cancel();
                    // hide any visible hints
                    this.hideHints(editor);
                    // keep track of when a completion is present
                    this.activeCompletion = true;
                })
            );

            disposables.push(
                onCompletionRequestCancelled(() => {
                    this.activeCompletion = false;

                    const editor = vscode.window.activeTextEditor;
                    if (!editor) {
                        return;
                    }

                    this.debouncedDecorations(editor);
                })
            );
        }

        disposables.push(
            vscode.window.onDidChangeTextEditorSelection((event) => {
                this.debouncedDecorations(event.textEditor);
            })
        );

        disposables.push(
            vscode.workspace.onDidChangeTextDocument((event) => {
                const editor = vscode.window.activeTextEditor;
                if (!editor || editor.document.uri.toString() !== event.document.uri.toString()) {
                    return;
                }
                if (!this.activeCompletion) {
                    this.debouncedDecorations(editor);
                }
            })
        );

        return disposables;
    }

    private hideHints(editor: vscode.TextEditor) {
        editor.setDecorations(this.decorationType, []);
    }
}
