/**
 * CountReporter is an interface for reporting changes to a count of items, such
 * as the number of items in a queue. update() reports the current item count.
 * cancel() indicates that the counting is being terminated, for example because
 * the underlying object or service is being disposed.
 */
export interface CountReporter {
    update: (currentCount: number) => void;
    cancel: () => void;
}

/**
 * ProgressReporter is an interface for reporting progress toward a goal. update()
 * reports the number of items completed and the total number of items, both starting
 * from when the ProgressReporter was instantiated. cancel() indicates that progress
 * has been terminated, perhaps because the underlying object or service is being
 * disposed.
 */
export interface ProgressReporter {
    update: (completed: number, total: number) => void;
    cancel: () => void;
}

/**
 * StatusReporter is an interface for reporting a text message. update() reports the
 * new message.
 */
export interface StatusReporter {
    update: (message: string | undefined) => void;
}

/**
 * StatusPlumber is a StatusReporter that can optionally direct updates to another
 * StatusReporter (the "listener").
 */
export class StatusPlumber implements StatusReporter {
    private message: string | undefined;
    public listener: StatusReporter | undefined;

    public update(message: string | undefined): void {
        this.message = message;
        if (this.listener) {
            this.listener.update(this.get());
        }
    }

    public get(): string | undefined {
        return this.message;
    }
}
