import { APIServer } from "../augment-api";
import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { DiffViewResolveMessage, DiffViewResolveType } from "../webview-providers/webview-messages";

export enum SessionOrigin {
    /* eslint-disable @typescript-eslint/naming-convention */
    smartPaste = "smart-paste",
    instruction = "instruction",
    /* eslint-enable @typescript-eslint/naming-convention */
}

function msecToTimestamp(msec: number): [number, number] {
    const seconds = Math.floor(msec / 1000);
    const nanoseconds = (msec % 1000) * 1000000;
    return [seconds, nanoseconds];
}

export class DiffViewSessionReporter extends DisposableService {
    public chunkResolveByStartLine: Map<number, boolean> = new Map<number, boolean>();
    public isAcceptAll: boolean = false;
    public isRejectAll: boolean = false;

    public requestId?: string;
    public sessionOrigin?: SessionOrigin;
    public initialRequestTime?: number;
    public streamFinishTime?: number;
    public applyTime?: number;

    private _logger = getLogger("DiffViewSessionReporter");

    constructor(private _apiServer: APIServer) {
        super();
        this.addDisposable({ dispose: this._logDiffPanelResolution });
    }

    reset = () => {
        this.chunkResolveByStartLine.clear();
        this.isAcceptAll = false;
        this.isRejectAll = false;
        this.requestId = undefined;
        this.sessionOrigin = undefined;
        this.initialRequestTime = undefined;
        this.streamFinishTime = undefined;
        this.applyTime = undefined;
    };

    reportResolution = (msg: DiffViewResolveMessage) => {
        const { resolveType, changes, shouldApplyToAll } = msg.data;
        if (shouldApplyToAll && !this.chunkResolveByStartLine.size) {
            if (resolveType === DiffViewResolveType.accept) {
                this.isAcceptAll = true;
            } else {
                this.isRejectAll = true;
            }
        }

        for (const change of changes) {
            const lineChanges = change.lineChanges?.lineChanges || [];
            for (const lineChange of lineChanges) {
                const startLine = lineChange.originalStart;
                this.chunkResolveByStartLine.set(
                    startLine,
                    resolveType === DiffViewResolveType.accept
                );
            }
        }
    };

    private _logDiffPanelResolution = () => {
        this._logger.debug(`Logging diff panel resolution`);
        if (!this.requestId) {
            this.reset();
            // If this is an instruction/smart-paste, should have a request id
            this._logger.warn(`No request id found for diff panel resolution`);
            return;
        }

        const acceptedChunkKeys = Array.from(this.chunkResolveByStartLine.keys()).sort();
        const acceptedChunkValues: boolean[] = acceptedChunkKeys
            .map((key) => this.chunkResolveByStartLine.get(key))
            .filter((value): value is boolean => value !== undefined);

        const [initialRequestTimeS, initialRequestTimeNs] = this.initialRequestTime
            ? msecToTimestamp(this.initialRequestTime)
            : [0, 0];
        const now = Date.now();
        const [resolveTimeS, resolveTimeNs] = msecToTimestamp(now);
        // If stream didn't finish yet, use same timestamp as resolution for emit
        const [streamFinishTimeS, streamFinishTimeNs] = msecToTimestamp(
            this.streamFinishTime || now
        );

        if (this.sessionOrigin === SessionOrigin.instruction) {
            /* eslint-disable @typescript-eslint/naming-convention */
            void this._apiServer.logInstructionResolution({
                request_id: this.requestId,
                is_accepted_chunks: acceptedChunkValues,
                is_accept_all: this.isAcceptAll,
                is_reject_all: this.isRejectAll,
                emit_time_sec: streamFinishTimeS,
                emit_time_nsec: streamFinishTimeNs,
                resolve_time_sec: resolveTimeS,
                resolve_time_nsec: resolveTimeNs,
            });
            /* eslint-enable @typescript-eslint/naming-convention */
            this._logger.debug(`Logged instruction resolution`);
        } else if (this.sessionOrigin === SessionOrigin.smartPaste) {
            // Apply time only exists for smart paste context
            const [applyTimeS, applyTimeNs] = this.applyTime
                ? msecToTimestamp(this.applyTime)
                : [0, 0];
            /* eslint-disable @typescript-eslint/naming-convention */
            void this._apiServer.logSmartPasteResolution({
                request_id: this.requestId,
                is_accepted_chunks: acceptedChunkValues,
                is_accept_all: this.isAcceptAll,
                is_reject_all: this.isRejectAll,
                initial_request_time_sec: initialRequestTimeS,
                initial_request_time_nsec: initialRequestTimeNs,
                stream_finish_time_sec: streamFinishTimeS,
                stream_finish_time_nsec: streamFinishTimeNs,
                apply_time_sec: applyTimeS,
                apply_time_nsec: applyTimeNs,
                resolve_time_sec: resolveTimeS,
                resolve_time_nsec: resolveTimeNs,
            });
            /* eslint-enable @typescript-eslint/naming-convention */
            this._logger.debug(`Logged smart paste resolution`);
        } else {
            this._logger.warn(`Unknown session origin: ${this.sessionOrigin}`);
        }

        this.reset();
    };
}
