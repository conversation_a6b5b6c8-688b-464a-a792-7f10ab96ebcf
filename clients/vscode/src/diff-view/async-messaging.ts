/**
 * This file contains the DiffViewMessageHandler class, which is responsible for handling
 * messages between the webview and the extension in the context of a diff view.
 * It extends the DisposableService class to ensure proper resource management.
 */
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import * as vscode from "vscode";

import type { ChatInstructionStreamResult } from "../augment-api";
import { WorkTimer } from "../metrics/work-timer";
import { setContext } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { toEditStream } from "../utils/edits/diff-by-line";
import { AsyncMsgHandler } from "../utils/webviews/messaging";
import { createAsyncMsgHandlerFromWebview } from "../utils/webviews/messaging-helper";
import { DiffViewWebviewPanel } from "../webview-panels/diff-view-panel";
import {
    type ChatInstructionMessage,
    type DiffViewInitializeMessage,
    DiffViewResolveMessage,
    DiffViewWindowFocusChange,
    type EmptyMessage,
    WebViewMessage,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import type { WorkspaceManager } from "../workspace/workspace-manager";
import type { DiffViewControllerDeps, DiffViewControllerOptions } from "./diff-view-controller";
import { VSCodeDiffViewDocument } from "./document-vscode";
import { DiffViewSessionReporter, SessionOrigin } from "./session-reporter";

// Options for the DiffViewMessageHandler.
// Extends DiffViewControllerOptions with additional properties.
export type DiffViewMessageHandlerOptions = {
    // Function to get initial data for the diff view
    getInitData: () => Promise<DiffViewInitializeMessage>;
    // Function to get the current stream of messages
    getCurrentStream: () => AsyncGenerator<WebViewMessage> | undefined;
    // Function to resolve a diff
    resolveDiff: (msg: DiffViewResolveMessage) => Promise<EmptyMessage>;
} & DiffViewControllerOptions;

/**
 * Handles messages for the diff view, including user interactions and data streaming.
 * This is in charge of all communications with the webview, as well as the API server.
 */
export class DiffViewMessageHandler extends DisposableService {
    private readonly _asyncMsgHandler: AsyncMsgHandler;
    private readonly _workspaceManager: WorkspaceManager;

    /**
     * Constructs a new DiffViewMessageHandler.
     *
     * @param _webview - The webview to communicate with
     * @param _sessionReporter - Reporter for diff view sessions
     * @param _deps - Dependencies required for the diff view controller
     * @param _opts - Options for the diff view message handler
     */
    constructor(
        private readonly _webview: vscode.Webview,
        private readonly _sessionReporter: DiffViewSessionReporter,
        private readonly _deps: DiffViewControllerDeps,
        private readonly _opts: DiffViewMessageHandlerOptions,
        private readonly _workTimer: WorkTimer
    ) {
        super();
        this._workspaceManager = this._deps.workspaceManager;
        this._asyncMsgHandler = createAsyncMsgHandlerFromWebview(this._webview, this._workTimer);

        // Register handlers for various message types
        this._registerMessageHandlers();

        this.addDisposable(this._asyncMsgHandler);
    }

    /**
     * Registers all message handlers for the diff view.
     * This includes handlers for file and folder searches, diff view operations, and chat instructions.
     */
    private _registerMessageHandlers(): void {
        if (this._deps.fuzzyFsSearcher) {
            this._asyncMsgHandler.registerHandler(
                WebViewMessageType.findFileRequest,
                this._deps.fuzzyFsSearcher.findFiles
            );
            this._asyncMsgHandler.registerHandler(
                WebViewMessageType.findFolderRequest,
                this._deps.fuzzyFsSearcher.findFolders
            );
            this._asyncMsgHandler.registerHandler(
                WebViewMessageType.findRecentlyOpenedFilesRequest,
                this._deps.fuzzyFsSearcher.findRecentFiles
            );
        }

        this._asyncMsgHandler.registerStreamHandler(
            WebViewMessageType.diffViewFetchPendingStream,
            () => this._opts.getCurrentStream() ?? (async function* () {})()
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.diffViewResolveChunk,
            this._opts.resolveDiff
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.diffViewWindowFocusChange,
            this._onDiffViewFocusChange
        );

        this._asyncMsgHandler.registerHandler(WebViewMessageType.disposeDiffView, async () => {
            this._opts.onUserTriggeredClose?.();
            return Promise.resolve({} as EmptyMessage);
        });

        this._asyncMsgHandler.registerHandler(WebViewMessageType.diffViewLoaded, () => {
            return this._opts.getInitData();
        });

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.chatInstructionMessage,
            this._onUserSendInstruction.bind(this)
        );
    }

    // Notifies the webview to reinitialize.
    public notifyReinit(): void {
        void this._webview.postMessage({
            type: WebViewMessageType.diffViewNotifyReinit,
        });
    }

    // Accepts all chunks in the diff view.
    public acceptAllChunks(): void {
        if (!this._sessionReporter.chunkResolveByStartLine.size) {
            this._sessionReporter.isAcceptAll = true;
        }
        void this._webview.postMessage({
            type: WebViewMessageType.diffViewAcceptAllChunks,
        });
    }

    // Accepts the currently focused chunk in the diff view.
    public acceptFocusedChunk(): void {
        void this._webview.postMessage({
            type: WebViewMessageType.diffViewAcceptFocusedChunk,
        });
    }

    // Rejects the currently focused chunk in the diff view.
    public rejectFocusedChunk(): void {
        void this._webview.postMessage({
            type: WebViewMessageType.diffViewRejectFocusedChunk,
        });
    }

    // Focuses the previous chunk in the diff view.
    public focusPreviousChunk(): void {
        void this._webview.postMessage({
            type: WebViewMessageType.diffViewFocusPrevChunk,
        });
    }

    // Focuses the next chunk in the diff view.
    public focusNextChunk(): void {
        void this._webview.postMessage({
            type: WebViewMessageType.diffViewFocusNextChunk,
        });
    }

    /**
     * Handles focus change events for the diff view.
     *
     * @param msg - The focus change message
     * @returns An empty message
     */
    private _onDiffViewFocusChange = (msg: DiffViewWindowFocusChange): EmptyMessage => {
        const isFocused = msg.data;
        setContext("vscode-augment.internal-dv.panel-focused", isFocused);
        return { type: WebViewMessageType.empty };
    };

    /**
     * Handles user-sent instructions for the chat.
     *
     * @param message - The instruction message from the user
     * @returns An empty message
     * @throws Error if the message is invalid
     */
    private async _onUserSendInstruction(message: WebViewMessage): Promise<EmptyMessage> {
        this._deps.logger.info(`onUserSendInstruction: ${JSON.stringify(message)}`);
        if (
            message.type !== WebViewMessageType.chatInstructionMessage ||
            !("data" in message) ||
            typeof message.data !== "object" ||
            !("instruction" in message.data)
        ) {
            throw new Error("Invalid message type or data for instruction");
        }
        const requestId = this._deps.apiServer.createRequestId();

        try {
            const editInstructionStream = this._runChatInstructionStream(message, requestId);
            DiffViewWebviewPanel.createOrShow(this._deps, {
                ...this._opts,
                viewOptions: vscode.ViewColumn.Active,
                document: await VSCodeDiffViewDocument.fromPathName(this._opts.document.filePath),
            });
            DiffViewWebviewPanel.startStream(
                toEditStream(this._opts.document.originalCode ?? "", editInstructionStream),
                requestId,
                SessionOrigin.instruction
            );
        } catch (e) {
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            void vscode.window.showErrorMessage(`Failed to send instruction: ${e}`);
            throw e;
        }
        return { type: WebViewMessageType.empty };
    }

    /**
     * Processes a chat instruction and generates a stream of results.
     *
     * @param message - The chat instruction message containing the instruction and selected code details.
     * @param requestId - A unique identifier for the request.
     * @returns An AsyncGenerator that yields ChatInstructionStreamResult objects.
     *
     * This method performs the following steps:
     * 1. Extracts instruction and code details from the message.
     * 2. Logs the request.
     * 3. Determines the blob name based on the current document or context.
     * 4. Calls the API server to get a chat instruction stream.
     * 5. Yields the results from the stream.
     * 6. Handles and logs any errors that occur during the process.
     */
    private async *_runChatInstructionStream(
        message: ChatInstructionMessage,
        requestId: string
    ): AsyncGenerator<ChatInstructionStreamResult> {
        // Make sure that we've received the guidelines watcher
        if (!this._opts.guidelinesWatcher) {
            this._deps.logger.error("Guidelines watcher not initialized");
            return;
        }

        // Extract instruction and code details from the message
        const { instruction, selectedCodeDetails } = message.data;
        const { selectedCode, prefix, suffix, path, language, prefixBegin, suffixEnd } =
            selectedCodeDetails;

        // Log the request
        this._deps.logger.info(`Sending request to the instruction model: ${requestId}`);

        // Get the workspace context and determine the blob name
        const context = this._workspaceManager.getContext();
        let blobName: string | undefined;
        if (!this._opts.document.isEmptyDocument && !this._opts.document.isUntitled) {
            blobName = this._workspaceManager.getBlobName(this._opts.document.filePath);
        } else {
            blobName = context.blobs.checkpointId;
        }

        try {
            const userGuidelines = this._opts.guidelinesWatcher.getUserGuidelinesContent();
            const workspaceGuidelines =
                this._opts.guidelinesWatcher.getCurrentWorkspaceGuidelinesContent(
                    this._workspaceManager
                );
            // Call the API server to get the chat instruction stream
            const stream: AsyncIterable<ChatInstructionStreamResult> =
                await this._deps.apiServer.chatInstructionStream(
                    requestId,
                    instruction,
                    context.blobs,
                    [], // chatHistory - empty for instructions
                    selectedCode,
                    prefix,
                    suffix,
                    path,
                    blobName,
                    prefixBegin,
                    suffixEnd,
                    language,
                    userGuidelines,
                    workspaceGuidelines
                );
            // Yield all results from the stream
            for await (const result of stream) {
                yield result;
            }
        } catch (error) {
            // Log and rethrow any errors
            this._deps.logger.error(`Error in runChatInstructionStream: ${getErrmsg(error)}`);
            throw error;
        }
    }
}
