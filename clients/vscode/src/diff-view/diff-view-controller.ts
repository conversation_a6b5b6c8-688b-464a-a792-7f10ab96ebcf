import { DiffViewDocument } from "@augment-internal/sidecar-libs/src/diff-view/document";
import * as vscode from "vscode";

import type { APIServer } from "../augment-api";
import type { FuzzyFsSearcher } from "../chat/fuzzy-fs-searcher";
import type { FuzzySymbolSearcher } from "../chat/fuzzy-symbol-searcher";
import type { GuidelinesWatcher } from "../chat/guidelines-watcher";
import {
    AcceptAllChunksDiffViewCommand,
    AcceptFocusedChunkDiffViewCommand,
    CloseDiffViewCommand,
    FocusNextChunkDiffViewCommand,
    FocusPreviousChunkDiffViewCommand,
    RejectFocusedChunkDiffViewCommand,
} from "../commands/diff-view";
import { AugmentLogger } from "../logging";
import { WorkTimer } from "../metrics/work-timer";
import { DisposableService } from "../utils/disposable-service";
import type { KeybindingWatcher } from "../utils/keybindings";
import {
    DiffViewDiffStreamChunkData,
    type DiffViewInitializeMessage,
    DiffViewResolveMessage,
    DiffViewResolveType,
    EmptyMessage,
    FileDetails,
    IInstructionInitializeData,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import type { WorkspaceManager } from "../workspace/workspace-manager";
import { DiffViewMessageHandler } from "./async-messaging";
import { DiffViewSessionReporter, SessionOrigin } from "./session-reporter";
import { DiffStreamContext } from "./stream-context";

export type DiffViewControllerDeps = {
    logger: AugmentLogger;
    panel: vscode.WebviewPanel;
    extensionUri: vscode.Uri;
    workspaceManager: WorkspaceManager;
    apiServer: APIServer;
    keybindingWatcher: KeybindingWatcher;
    fuzzyFsSearcher: FuzzyFsSearcher | undefined;
    fuzzySymbolSearcher: FuzzySymbolSearcher | undefined;
    workTimer: WorkTimer;
};

// Options for configuring the DiffViewController
export type DiffViewControllerOptions = {
    document: DiffViewDocument;
    editable?: boolean;
    disableResolution?: boolean;
    disableApply?: boolean;
    onUserTriggeredClose: () => void;
    smartPasteContext?: { applyTime?: number };
    keybindings?: { [key: string]: string };
    instruction?: IInstructionInitializeData;
    guidelinesWatcher?: GuidelinesWatcher;
};

export class DiffViewController extends DisposableService {
    private _sessionReporter: DiffViewSessionReporter;
    private _diffViewMessageHandler: DiffViewMessageHandler;

    private _currentSelection: vscode.Selection | undefined;
    private _stream: DiffStreamContext | undefined;

    public constructor(
        private readonly _panel: vscode.WebviewPanel,
        private readonly _deps: DiffViewControllerDeps,
        private readonly _opts: DiffViewControllerOptions,
        private readonly _workTimer: WorkTimer,
        smartPasteContext?: { applyTime?: number }
    ) {
        super();

        // Initialize the reporter in charge of managing this diff view session
        this._sessionReporter = new DiffViewSessionReporter(this._deps.apiServer);
        if (smartPasteContext) {
            this._sessionReporter.applyTime = smartPasteContext.applyTime;
        }

        // Initialize the message handler in charge of handling messages from the webview
        this._diffViewMessageHandler = new DiffViewMessageHandler(
            this._panel.webview,
            this._sessionReporter,
            this._deps,
            {
                ...this._opts,
                getInitData: this.getInitData,
                resolveDiff: this.resolveDiff,
                getCurrentStream: () => this._stream?.stream,
            },
            this._workTimer
        );
        this.addDisposable(this._diffViewMessageHandler);
        this.addDisposable(this._sessionReporter);
        this.addDisposable(this._opts.document);

        // After initialization, notify the webview to reinitialize
        this._diffViewMessageHandler.notifyReinit();
    }

    public get diffViewMessageHandler(): DiffViewMessageHandler {
        return this._diffViewMessageHandler;
    }

    public get sessionReporter(): DiffViewSessionReporter {
        return this._sessionReporter;
    }

    public startStream(
        s: AsyncGenerator<DiffViewDiffStreamChunkData>,
        requestId: string,
        origin?: SessionOrigin
    ) {
        this._sessionReporter.requestId = requestId;
        this._sessionReporter.sessionOrigin = origin;
        this._sessionReporter.initialRequestTime = Date.now();
        this._cleanupStream();
        this._stream = new DiffStreamContext(
            this._deps.apiServer.createRequestId(),
            requestId,
            s,
            this._deps.logger
        );
    }

    private _cleanupStream = () => {
        this._stream?.cancel();
        this._stream = undefined;
    };

    private getInitData = (): Promise<DiffViewInitializeMessage> => {
        const selection = this._opts?.instruction?.selection;
        const response: DiffViewInitializeMessage = {
            type: WebViewMessageType.diffViewInitialize,
            data: {
                file: {
                    repoRoot: "",
                    pathName: "",
                    originalCode: "",
                    modifiedCode: "",
                },
                keybindings: this.keybindings,
                instruction: selection && {
                    selection: {
                        start: {
                            line: selection?.start.line,
                            character: selection?.start.character,
                        },
                        end: {
                            line: selection?.end.line,
                            character: selection?.end.character,
                        },
                    },
                },
                editable: this._opts.editable,
                disableResolution: this._opts.disableResolution,
                disableApply: this._opts.disableApply,
            },
        };
        if (this._opts?.instruction) {
            const { selection } = this._opts.instruction;
            const { start, end } = selection;
            this._currentSelection = new vscode.Selection(
                new vscode.Position(start.line, start.character),
                new vscode.Position(end.line, end.character)
            );
        }
        if (this._opts.document.isEmptyDocument) {
            return Promise.resolve(response);
        }

        response.data.file.pathName = this._opts.document.absPath;
        response.data.file.originalCode = this._opts.document.originalCode;
        response.data.file.modifiedCode = this._opts.document.modifiedCode;
        return Promise.resolve(response);
    };

    private resolveDiff = async (msg: DiffViewResolveMessage): Promise<EmptyMessage> => {
        const { file, resolveType, changes } = msg.data;
        this._sessionReporter.reportResolution(msg);

        // If the requested resolution has invalid parameters, ignore it.
        // Examples: no changes, requested path !== current path, no current path
        const currPath = vscode.Uri.file(this._opts.document?.absPath ?? "").fsPath;
        const resolvePath = vscode.Uri.file(file.pathName).fsPath;
        const hasNoChanges = file.modifiedCode === undefined;
        if (!this._opts.document || currPath !== resolvePath || hasNoChanges) {
            return { type: WebViewMessageType.empty };
        }

        if (
            resolveType === DiffViewResolveType.accept ||
            resolveType === DiffViewResolveType.reject
        ) {
            const hasChanged = this._opts.document.updateCodeVersions(
                file.originalCode,
                file.modifiedCode
            );
            if (hasChanged) {
                // If there is a document, update the current selection range based on changes
                const doc = await vscode.workspace.openTextDocument(this._opts.document.absPath);
                if (changes && changes.length > 0 && doc) {
                    this.updateChangeRange(doc, changes, resolveType);
                }
            }
        }

        return { type: WebViewMessageType.empty };
    };

    /**
     * Updates the current selection range to encompass code changes.
     *
     * When accepting changes, expands the selection to include all modified lines.
     * Ensures the selection stays within valid document boundaries while
     * maintaining context of the surrounding code.
     *
     * @param doc - The VS Code document being modified
     * @param changes - Array of file changes containing line modifications
     * @param resolveType - Determines how to adjust selection (e.g., when accepting changes)
     */
    private updateChangeRange = (
        doc: vscode.TextDocument,
        changes: FileDetails[],
        resolveType: DiffViewResolveType
    ): void => {
        // Exit if no selection exists
        if (!this._currentSelection) {
            return;
        }

        // Get current selection boundaries (0-based line numbers)
        let startLine = this._currentSelection.start.line;
        let endLine = this._currentSelection.end.line;

        // Track the outermost boundaries of all changes
        // MAX_SAFE_INTEGER ensures first change will always set minOriginalStart
        let minOriginalStart = Number.MAX_SAFE_INTEGER;
        let maxModifiedEnd = 0;

        // Process all changes to find the full extent of modifications
        for (const change of changes) {
            // Safely handle potentially undefined line changes
            if (change.lineChanges && change.lineChanges.lineChanges) {
                for (const lineChange of change.lineChanges.lineChanges) {
                    // Track earliest and latest affected lines
                    minOriginalStart = Math.min(minOriginalStart, lineChange.originalStart);
                    maxModifiedEnd = Math.max(maxModifiedEnd, lineChange.modifiedEnd);
                }
            }
        }

        // When accepting changes, expand selection to include context
        if (resolveType === DiffViewResolveType.accept) {
            // Adjust lines for 0-based indexing (-1)
            startLine = Math.min(startLine, minOriginalStart - 1);
            endLine = Math.max(endLine, maxModifiedEnd - 1);
        }

        // Ensure selection stays within document boundaries
        startLine = Math.max(0, startLine);
        endLine = Math.min(doc.lineCount - 1, endLine);

        // Create selection from start of first line to end of last line
        const endPosition = doc.lineAt(endLine).range.end;
        this._currentSelection = new vscode.Selection(
            new vscode.Position(startLine, 0),
            endPosition
        );
    };

    public get keybindings(): { [key: string]: string } {
        return {
            acceptAllChunks:
                this._deps.keybindingWatcher.getKeybindingForCommand(
                    AcceptAllChunksDiffViewCommand.commandID,
                    true
                ) ?? "",
            rejectAllChunks:
                this._deps.keybindingWatcher.getKeybindingForCommand(
                    CloseDiffViewCommand.commandID,
                    true
                ) ?? "",
            acceptFocusedChunk:
                this._deps.keybindingWatcher.getKeybindingForCommand(
                    AcceptFocusedChunkDiffViewCommand.commandID,
                    true
                ) ?? "",
            rejectFocusedChunk:
                this._deps.keybindingWatcher.getKeybindingForCommand(
                    RejectFocusedChunkDiffViewCommand.commandID,
                    true
                ) ?? "",
            focusPrevChunk:
                this._deps.keybindingWatcher.getKeybindingForCommand(
                    FocusPreviousChunkDiffViewCommand.commandID,
                    true
                ) ?? "",
            focusNextChunk:
                this._deps.keybindingWatcher.getKeybindingForCommand(
                    FocusNextChunkDiffViewCommand.commandID,
                    true
                ) ?? "",
        };
    }
}
