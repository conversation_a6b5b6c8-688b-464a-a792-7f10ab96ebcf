import * as vscode from "vscode";

import { AugmentLogger } from "../logging";
import CopyableGenerator from "../utils/copyable-generator";
import { DisposableService } from "../utils/disposable-service";
import type {
    DiffViewDiffStreamChunkData,
    DiffViewDiffStreamEnded,
    DiffViewDiffStreamStarted,
    DiffViewStreamMessage,
} from "../webview-providers/webview-messages";
import { WebViewMessageType } from "../webview-providers/webview-messages";
import { getNonce } from "../webview-providers/webview-utils";

/**
 * DiffStreamContext class for managing diff stream operations
 * Extends DisposableService for proper resource management
 */
export class DiffStreamContext extends DisposableService {
    // Cancellation token to cancel the stream when needed
    private _cancelToken = new vscode.CancellationTokenSource();

    // CopyableGenerator to allow multiple consumers of the stream
    private readonly _copyableGenerator: CopyableGenerator<DiffViewDiffStreamChunkData>;

    /**
     * Constructor for DiffStreamContext
     * @param streamId Unique identifier for the stream
     * @param requestId Identifier for the request
     * @param _stream AsyncGenerator for diff stream chunks
     * @param _logger Logger instance for error reporting
     */
    constructor(
        public readonly streamId: string,
        public readonly requestId: string,
        private readonly _stream: AsyncGenerator<DiffViewDiffStreamChunkData>,
        private readonly _logger: AugmentLogger
    ) {
        super();
        this._copyableGenerator = new CopyableGenerator(this._stream);
        this.addDisposable({ dispose: () => this._cancelToken?.dispose() });
    }

    /**
     * Getter for the stream. Cancels any existing streams.
     * @returns A copy of the CopyableGenerator for the stream
     */
    public get stream(): AsyncGenerator<DiffViewStreamMessage> {
        return this._formatStream(this._copyableGenerator.copy());
    }

    /**
     * Cancels the stream and disposes of resources
     */
    public cancel = () => {
        this._cancelToken?.cancel();
        this._copyableGenerator.cancel();
        this.dispose();
    };

    /**
     * Resets the cancellation token for the stream
     */
    private _resetStream = () => {
        this._cancelToken?.cancel();
        this._cancelToken?.dispose();
        this._cancelToken = new vscode.CancellationTokenSource();
    };

    /**
     * Formats the input stream into WebViewMessages
     * @param s Input AsyncGenerator of DiffViewDiffStreamChunkData
     * @param cancelToken CancellationToken to check for cancellation
     * @returns AsyncGenerator of WebViewMessage
     */
    private async *_formatStream(
        s: AsyncGenerator<DiffViewDiffStreamChunkData>
    ): AsyncGenerator<DiffViewStreamMessage> {
        this._resetStream();
        const currCancelToken = this._cancelToken.token;
        const streamId = `${this.streamId}-${getNonce()}`;
        try {
            yield this._streamStartedMsg(streamId);

            // Yield the rest of the stream, as long as cancellation is not requested
            for await (const msg of s) {
                if (currCancelToken.isCancellationRequested) {
                    break;
                }
                yield this._streamDataMsg(streamId, msg);
            }
        } catch (e) {
            this._notifyStreamError(e);
        } finally {
            yield this._streamEndedMsg(streamId);
        }
    }

    /**
     * Notifies the user of stream errors and logs the error
     * @param e The error object
     */
    private _notifyStreamError(e: any) {
        void vscode.window.showWarningMessage(
            "Error streaming in changes to Augment Diff. Please try again."
        );
        this._logger.error(`Error streaming in changes to Augment Diff: ${(e as Error).message}`);
    }

    /**
     * Data utilities to format different messages that need to be returned to the
     * webview. This allows the main control loop of stream data to be simplified.
     */

    private _streamStartedMsg = (streamId: string): DiffViewDiffStreamStarted => {
        return {
            data: { streamId, requestId: this.requestId },
            type: WebViewMessageType.diffViewDiffStreamStarted,
        };
    };

    private _streamDataMsg = (
        streamId: string,
        data: DiffViewDiffStreamChunkData
    ): DiffViewStreamMessage => {
        return {
            data: { ...data, streamId, requestId: this.requestId },
            type: WebViewMessageType.diffViewDiffStreamChunk,
        };
    };

    private _streamEndedMsg = (streamId: string): DiffViewDiffStreamEnded => {
        return {
            data: { streamId, requestId: this.requestId },
            type: WebViewMessageType.diffViewDiffStreamEnded,
        };
    };
}
