import * as vscode from "vscode";

/**
 * StatusTrace is a formatting facility for status tracing.
 */
export class StatusTrace implements vscode.Disposable {
    private static readonly _lineLen = 80;
    private static readonly _indent = "    ";
    private static readonly _subIndent = "  ";

    private _content = "";
    private _disposed = false;

    constructor(public publish: () => void) {}

    get content(): string {
        return this._content;
    }

    // Add a new top-level section to the status trace.
    public addSection(title: string) {
        const sep = (this._content.length === 0 ? "" : "\n") + "================\n";
        this._addLine(sep + title);
    }

    // Format the contents of the given object into the status trace.
    public addObject(object: Object | undefined) {
        if (object === undefined) {
            this.addLine(this.formatValue(object));
        } else {
            for (const key in object) {
                const value = object[key as keyof Object];
                if (typeof value !== "object") {
                    this.addValue(key, value);
                } else {
                    this.addValue(key, JSON.stringify(value));
                }
            }
        }
    }

    public formatValue(value: any): string {
        if (value === undefined) {
            return "<undefined>";
        }
        if (value === null) {
            return "<null>";
        }
        if (typeof value === "string") {
            return `"${value}"`;
        }
        return `${value}`;
    }

    // Add a single line to the status trace. A newline character will be added at
    // the end of the line.
    public addLine(line: string) {
        if (this._disposed) {
            return;
        }
        this._addLine(StatusTrace._indent + line);
    }

    // Add a block of text (potentially multiple lines) to the status trace.
    public addText(label: string, text: string) {
        if (this._disposed) {
            return;
        }
        const sep = (this._content.length === 0 ? "" : "\n") + ">>>>>>>>>>>>>>>> ";
        this._addLine(sep + label);
        this._addLine(text, "");
        if (text.length > 0 && text[text.length - 1] !== "\n") {
            this._addLine("|<---- (ends here)");
        }
        this._addLine("<<<<<<<<<<<<<<<< " + label);
    }

    // Add an error message to the status trace. If the message is too long, it will
    // be split into multiple lines at ": " boundaries.
    public addError(errStr: string) {
        if (StatusTrace._indent.length + errStr.length <= StatusTrace._lineLen) {
            this._addLine(StatusTrace._indent + errStr);
            return;
        }
        let startPos = 0;
        let indent = StatusTrace._indent;
        while (true) {
            const idx = errStr.indexOf(": ", startPos);
            if (idx === -1) {
                this._addLine(indent + errStr.substring(startPos));
                break;
            }
            this._addLine(indent + errStr.substring(startPos, idx + 1));
            startPos = idx + 2;
            indent += StatusTrace._subIndent;
        }
    }

    public addValue(label: string, value: any) {
        this._addLine(StatusTrace._indent + label + ": " + this.formatValue(value));
    }

    public addStringValue(label: string, value: string, quoted = true) {
        this._addLine(StatusTrace._indent + label + ": " + (quoted ? `"${value}"` : value));
    }

    // Internal method for adding a line to the trace.
    private _addLine(line: string, term = "\n") {
        if (this._disposed) {
            return;
        }
        this._content += line + term;
    }

    // Create a savepoint at the end of the current trace. You can roll back to this
    // point by passing the returned save point to rollback(). This facility allows the
    // caller to put provisional content into the trace (eg, "Attempting completion")
    // and remove it later after the attempted completion has been received.
    public savePoint(): number {
        return this._content.length;
    }

    // Remove all content after the given save point.
    public rollback(savePoint: number) {
        if (this._disposed) {
            return;
        }
        if (this._content.length > savePoint) {
            this._content = this._content.substring(0, savePoint);
        }
    }

    // Indicate that this status trace is no longer being displayed.
    public dispose() {
        this._disposed = true;
        this._content = "";
    }
}
