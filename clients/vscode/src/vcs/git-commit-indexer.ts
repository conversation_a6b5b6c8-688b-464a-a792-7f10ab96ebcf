import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { AugmentGlobalState, FileStorageKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { BlobUploader, BlobUploadRequest } from "../workspace/open-file-manager-v2/blob-uploader";
import {
    onBlobsIndexed,
    onBlobUploadFailed,
} from "../workspace/open-file-manager-v2/blob-uploader/blob-uploader-events";
import { GitAdapterImpl } from "./git-adapter";
import { findVCS } from "./vcs-finder";
import * as headChangeWatcher from "./watcher/head-change-watcher";
import { HeadChangeWatcher } from "./watcher/head-change-watcher";

const COMMIT_FETCH_BATCH_SIZE = 10;
const DEFAULT_POLLING_INTERVAL_MS = 60_000;

/**
 * Persistent state for the git commit indexer.
 * This is stored to disk to avoid re-indexing all commits on VSCode restart.
 */
interface GitCommitIndexerState {
    checkpointId?: string;
    folderKnownBlobNames: Record<string, string[]>; // Map<folderPath, blobNames[]>
    maxCommitsToIndex: number; // The maxCommitsToIndex value when this state was saved
    version: number; // For future schema migrations
}

/**
 * Information about a git commit.
 */
interface CommitInfo {
    hash: string;
    authorName: string;
    authorEmail: string;
    timestamp: number;
    subject: string;
    body: string;
    filesChanged: FileChange[];
}

/**
 * Information about a file change in a commit.
 */
interface FileChange {
    changeType: string;
    filePath: string;
    oldFilePath?: string;
    diff?: string;
}

/**
 * GitCommitIndexer is responsible for indexing git commits in the current workspace.
 * It runs at startup and uploads all git commits on the current branch (up to a configured limit)
 * if they are not yet uploaded.
 */
export class GitCommitIndexer extends DisposableService {
    private readonly _logger = getLogger("GitCommitIndexer");
    private _folderKnownBlobNames = new Map<string, Set<string>>();
    private _isIndexing = false;
    private _checkpointId: string | undefined;
    private readonly _blobUploader: BlobUploader;
    private _headChangeWatchers: HeadChangeWatcher[] = [];
    private _repoIdToFolder = new Map<number, vscode.WorkspaceFolder>();
    private _pollingTimer: NodeJS.Timeout | undefined;
    // Track pending uploads: blobName -> folderPath
    private _pendingUploads = new Map<string, string>();
    // Track if we need to create a checkpoint after uploads complete
    private _needsCheckpoint = false;
    // Track if we're currently processing batches to prevent premature checkpointing
    private _processingBatches = false;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _blobNameCalculator: BlobNameCalculator,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _globalState: AugmentGlobalState
    ) {
        super();
        this._blobUploader = new BlobUploader(this._blobNameCalculator, this._apiServer);

        // Register for feature flag changes and upload events
        this.addDisposables(
            this._featureFlagManager.subscribe(["enableCommitIndexing"], () => {
                if (this._featureFlagManager.currentFlags.enableCommitIndexing) {
                    void this.indexCommits();
                    this._startPolling();
                } else {
                    this._stopPolling();
                }
            }),
            this._blobUploader,
            // Subscribe to head change events
            headChangeWatcher.onDidChange(this._onHeadChange.bind(this)),
            // Subscribe to blob upload events
            onBlobsIndexed(this._onBlobsIndexed.bind(this)),
            onBlobUploadFailed(this._onBlobUploadFailed.bind(this))
        );

        // Start indexing if enabled
        if (this._featureFlagManager.currentFlags.enableCommitIndexing) {
            void this.indexCommits();
            this._startPolling();
        }
    }

    /**
     * Handler for HEAD change events.
     * Indexes only new commits since the last indexed commit for the specific repository.
     */
    private _onHeadChange(event: headChangeWatcher.HeadChangeEvent): void {
        if (!this._featureFlagManager.currentFlags.enableCommitIndexing) {
            return;
        }

        this._logger.debug(`Received HEAD change event for repo ${event.repoId}`);

        // Check if workspace folders exist (general safeguard)
        if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
            return;
        }

        // Schedule the indexing of new commits for this specific folder
        void this._indexNewCommitsForRepo(event.repoId);
    }

    /**
     * Handle successful blob indexing - add to known blob names and save state.
     */
    private _onBlobsIndexed(event: { blobNames: string[] }): void {
        if (!this._featureFlagManager.currentFlags.enableCommitIndexing) {
            return;
        }

        let hasChanges = false;

        for (const blobName of event.blobNames) {
            const folderPath = this._pendingUploads.get(blobName);
            if (folderPath) {
                // Add to known blob names for this folder
                let folderBlobNames = this._folderKnownBlobNames.get(folderPath);
                if (!folderBlobNames) {
                    folderBlobNames = new Set();
                    this._folderKnownBlobNames.set(folderPath, folderBlobNames);
                }
                folderBlobNames.add(blobName);
                this._pendingUploads.delete(blobName);
                hasChanges = true;
            }
        }

        // Save state if we had any changes
        if (hasChanges) {
            void this._savePersistedState();
        }

        // Check if all uploads are complete and create checkpoint if needed
        if (this._needsCheckpoint && this._pendingUploads.size === 0 && !this._processingBatches) {
            this._needsCheckpoint = false;
            this._logger.debug("All uploads complete, creating checkpoint");
            void this._createCheckpoint();
        }
    }

    /**
     * Handle failed blob uploads - remove from pending uploads.
     */
    private _onBlobUploadFailed(event: { blobName: string }): void {
        if (!this._featureFlagManager.currentFlags.enableCommitIndexing) {
            return;
        }

        const folderPath = this._pendingUploads.get(event.blobName);
        if (folderPath) {
            this._pendingUploads.delete(event.blobName);
            this._logger.debug(
                `Blob upload failed, removed from pending: ${event.blobName} for folder ${folderPath}`
            );
        }

        // Check if all uploads are complete and create checkpoint if needed
        if (this._needsCheckpoint && this._pendingUploads.size === 0 && !this._processingBatches) {
            this._needsCheckpoint = false;
            this._logger.debug("All uploads complete (some failed), creating checkpoint");
            void this._createCheckpoint();
        }
    }

    /**
     * Poll for new commits across all repositories.
     * This simply triggers the same indexing logic that HEAD change events use.
     */
    private _pollForNewCommits() {
        if (!this._featureFlagManager.currentFlags.enableCommitIndexing) {
            return;
        }
        this._logger.verbose("Polling for new commits");

        for (const repoId of this._repoIdToFolder.keys()) {
            void this._indexNewCommitsForRepo(repoId);
        }
    }

    /**
     * Index only new commits for a specific repository.
     */
    private async _indexNewCommitsForRepo(repoId: number): Promise<void> {
        if (this._isIndexing) {
            this._logger.debug(
                `Already indexing commits, skipping new commit indexing for repo ${repoId}`
            );
            return;
        }

        this._isIndexing = true;
        try {
            // Look up the folder from our map
            const matchingFolder = this._repoIdToFolder.get(repoId);
            if (!matchingFolder) {
                this._logger.debug(`No matching folder found for repo ID ${repoId}`);
                return;
            }

            this._logger.debug(
                `Indexing new commits for repo ${repoId} in folder ${matchingFolder.name}`
            );

            // Index only the matching folder
            const hasNewCommits = await this._indexCommitsInFolder(matchingFolder, true);

            // Only create a checkpoint if we actually found new commits
            if (hasNewCommits) {
                await this._createCheckpoint();
            } else {
                this._logger.debug(
                    `No new commits found for repo ${repoId}, skipping checkpoint creation`
                );
            }
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error indexing new commits for repo ${repoId}: ${error.message}`);
        } finally {
            this._isIndexing = false;
        }
    }

    /**
     * Load persistent state from disk to avoid re-indexing commits on restart.
     */
    private async _loadPersistedState(): Promise<void> {
        try {
            const state = await this._globalState.load<GitCommitIndexerState>(
                FileStorageKey.gitCommitIndexerState,
                { uniquePerWorkspace: true }
            );

            if (state && state.version === 1) {
                const currentMaxCommits = this._featureFlagManager.currentFlags.maxCommitsToIndex;

                // Check if maxCommitsToIndex has changed
                if (state.maxCommitsToIndex !== currentMaxCommits) {
                    this._logger.debug(
                        `maxCommitsToIndex changed from ${state.maxCommitsToIndex} to ${currentMaxCommits}, ` +
                            `invalidating persisted state`
                    );
                    // Clear state and start fresh with new limit
                    this._folderKnownBlobNames.clear();
                    this._checkpointId = undefined;
                } else {
                    this._checkpointId = state.checkpointId;

                    // Convert the persisted Record back to Map<string, Set<string>>
                    this._folderKnownBlobNames.clear();
                    let totalBlobs = 0;

                    for (const [folderPath, blobNames] of Object.entries(
                        state.folderKnownBlobNames
                    )) {
                        this._folderKnownBlobNames.set(folderPath, new Set(blobNames));
                        totalBlobs += blobNames.length;
                    }

                    this._logger.debug(
                        `Loaded persisted state: checkpointId=${this._checkpointId}, ` +
                            `folders=${Object.keys(state.folderKnownBlobNames).length}, ` +
                            `totalBlobs=${totalBlobs}, maxCommitsToIndex=${state.maxCommitsToIndex}`
                    );
                }
            } else {
                this._logger.debug("No valid persisted state found, starting fresh");
            }
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Failed to load persisted state: ${error.message}`);
        }
    }

    /**
     * Save current state to disk for persistence across VSCode restarts.
     */
    private async _savePersistedState(): Promise<void> {
        try {
            // Convert Map<string, Set<string>> to serializable Record<string, string[]>
            const folderKnownBlobNames: Record<string, string[]> = {};
            let totalBlobs = 0;
            for (const [folderPath, blobNameSet] of this._folderKnownBlobNames.entries()) {
                folderKnownBlobNames[folderPath] = Array.from(blobNameSet);
                totalBlobs += blobNameSet.size;
            }

            const state: GitCommitIndexerState = {
                checkpointId: this._checkpointId,
                folderKnownBlobNames,
                maxCommitsToIndex: this._featureFlagManager.currentFlags.maxCommitsToIndex,
                version: 1,
            };

            await this._globalState.save(FileStorageKey.gitCommitIndexerState, state, {
                uniquePerWorkspace: true,
            });

            this._logger.verbose(
                `Saved persisted state: checkpointId=${this._checkpointId}, ` +
                    `folders=${Object.keys(folderKnownBlobNames).length}, ` +
                    `totalBlobs=${totalBlobs}`
            );
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Failed to save persisted state: ${error.message}`);
        }
    }

    /**
     * Index git commits in the current workspace.
     * Creates a checkpoint after indexing all commits to avoid re-indexing the same commits in the future.
     * Also sets up HEAD change watchers for each repository.
     */
    public async indexCommits(): Promise<void> {
        if (this._isIndexing) {
            this._logger.debug("Already indexing commits, skipping");
            return;
        }

        this._isIndexing = true;
        try {
            // Load persisted state first
            await this._loadPersistedState();

            // Clear repo mapping (but keep blob names from persisted state)
            this._repoIdToFolder.clear();

            // Dispose any existing watchers
            for (const watcher of this._headChangeWatchers) {
                watcher.dispose();
            }
            this._headChangeWatchers = [];

            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                this._logger.debug("No workspace folders found, skipping commit indexing");
                return;
            }

            let repoId = 0;
            for (const folder of workspaceFolders) {
                // Set up HEAD change watcher for this folder
                const vcsDetails = await findVCS(folder.uri.fsPath);
                if (vcsDetails) {
                    const watcher = new HeadChangeWatcher(folder.name, repoId, vcsDetails);
                    watcher.listenForChanges();
                    this._headChangeWatchers.push(watcher);
                    // Store the mapping between repo ID and workspace folder
                    this._repoIdToFolder.set(repoId, folder);
                    this.addDisposables(watcher);
                    repoId++;
                }

                // Index commits in the folder
                // If we have persistent state (checkpoint ID), only look for new commits
                // Otherwise, do a full index
                const onlyNewCommits = this._checkpointId !== undefined;
                await this._indexCommitsInFolder(folder, onlyNewCommits);
            }
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error indexing commits: ${error.message}`);
        } finally {
            this._isIndexing = false;
        }
    }

    /**
     * Creates a checkpoint with all the currently indexed commit blob names.
     * This allows us to efficiently track which commits have been indexed.
     */
    private async _createCheckpoint(): Promise<void> {
        // Collect all blob names from all folders
        const allBlobNames: string[] = [];
        for (const [_, blobNameSet] of this._folderKnownBlobNames.entries()) {
            allBlobNames.push(...blobNameSet);
        }

        if (allBlobNames.length === 0) {
            this._logger.debug("No commits to checkpoint");
            return;
        }

        try {
            // Create a checkpoint using the existing checkpoint mechanism
            const result = await this._apiServer.checkpointBlobs({
                checkpointId: undefined,
                addedBlobs: allBlobNames,
                deletedBlobs: [],
            });

            // Store the new checkpoint ID for future use
            this._checkpointId = result.newCheckpointId;
            this._logger.debug(`Created commit checkpoint with ID: ${this._checkpointId}`);

            // Save the state with the new checkpoint ID
            await this._savePersistedState();
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error creating commit checkpoint: ${error.message}`);
        }
    }

    /**
     * Index git commits in a specific workspace folder.
     * Can index all commits or only new commits since the last indexing.
     * @returns true if new commits were processed, false otherwise
     */
    private async _indexCommitsInFolder(
        folder: vscode.WorkspaceFolder,
        onlyNewCommits: boolean
    ): Promise<boolean> {
        try {
            const vcsDetails = await findVCS(folder.uri.fsPath);
            if (!vcsDetails) {
                this._logger.debug(
                    `No VCS found in folder ${folder.name}, skipping commit indexing`
                );
                return false;
            }

            this._logger.debug(`Indexing commits in folder ${folder.name}`);

            // For full indexing, only clear existing known blob names if we don't have persistent state
            if (!onlyNewCommits && !this._checkpointId) {
                const folderBlobNames = this._folderKnownBlobNames.get(folder.uri.fsPath);
                if (folderBlobNames) {
                    folderBlobNames.clear();
                    this._logger.debug(
                        `Cleared existing blob names for fresh full index of ${folder.name}`
                    );
                }
            }

            // Create a git adapter for the folder
            const gitAdapter = new GitAdapterImpl(vcsDetails.root);

            // Get the branch to use for indexing
            const branchToUse = await this._getCurrentBranch(gitAdapter);
            if (!branchToUse) {
                this._logger.debug(
                    `Could not determine any branch in ${folder.name}, skipping commit indexing`
                );
                return false;
            }

            this._logger.debug(`Using branch '${branchToUse}' for indexing in ${folder.name}`);

            // Process commits
            const maxCommits = this._featureFlagManager.currentFlags.maxCommitsToIndex;
            const hasNewCommits = await this._processCommitsInBatches(
                gitAdapter,
                branchToUse,
                maxCommits,
                onlyNewCommits,
                folder
            );

            if (!hasNewCommits) {
                this._logger.debug(`No commits found on branch ${branchToUse} in ${folder.name}`);
                return false;
            }

            this._logger.debug(`Finished indexing commits in ${folder.name}`);
            return true;
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error indexing commits in folder ${folder.name}: ${error.message}`);
            return false; // Error occurred, no commits processed
        }
    }

    /**
     * Get the current branch for the repository.
     */
    private async _getCurrentBranch(gitAdapter: GitAdapterImpl): Promise<string | undefined> {
        try {
            const currentBranchResult = await gitAdapter.symbolicRef({ name: "HEAD" });
            if (currentBranchResult) {
                return currentBranchResult.trim().replace(/^refs\/heads\//, "");
            }
        } catch {
            this._logger.error("Failed to get current branch");
        }

        return undefined;
    }

    /**
     * Determine if we're currently on the main branch.
     * Uses the same logic as the old _getDefaultBranch method to detect the main branch.
     */
    private async _isOnMainBranch(gitAdapter: GitAdapterImpl): Promise<boolean> {
        try {
            const currentBranch = await this._getCurrentBranch(gitAdapter);
            if (!currentBranch) {
                return false;
            }

            const mainBranch = await this._getMainBranch(gitAdapter);
            return currentBranch === mainBranch;
        } catch {
            this._logger.debug("Error determining if on main branch");
            return false;
        }
    }

    /**
     * Get the main branch for the repository.
     * First tries to get it from remote (origin/HEAD), then tries common names locally,
     * and finally falls back to the current branch if needed.
     */
    private async _getMainBranch(gitAdapter: GitAdapterImpl): Promise<string | undefined> {
        const DEFAULT_BRANCH_NAMES = ["main", "master"];

        // First try to get the default branch from remote (origin/HEAD)
        try {
            const remoteHeadResult = await gitAdapter.symbolicRef({
                name: "refs/remotes/origin/HEAD",
            });
            if (remoteHeadResult) {
                // Format is typically "refs/remotes/origin/main"
                return remoteHeadResult.trim().replace("refs/remotes/origin/", "");
            }
        } catch {
            // Remote HEAD reference doesn't exist, continue to fallback
        }

        // Try common default branch names locally
        for (const branch of DEFAULT_BRANCH_NAMES) {
            try {
                const result = await gitAdapter.symbolicRef({ name: `refs/heads/${branch}` });
                if (result) {
                    return branch;
                }
            } catch {
                // Branch doesn't exist
            }
        }

        return undefined;
    }

    /**
     * Process commits in batches.
     * Uploads each batch immediately after processing.
     * @returns true if any commits were processed, false otherwise
     */
    private async _processCommitsInBatches(
        gitAdapter: GitAdapterImpl,
        branch: string | undefined,
        maxCommits: number,
        stopOnKnownCommit: boolean,
        folder: vscode.WorkspaceFolder
    ): Promise<boolean> {
        const FORMAT = "%H%n%an%n%ae%n%at%n%s%n%b";
        const SEPARATOR = "---COMMIT---";
        let skipCount = 0;
        let hasMoreCommits = true;
        let foundKnownCommit = false;
        let totalProcessedCommits = 0;

        this._processingBatches = true;

        try {
            // Check if we're on the main branch to determine if we should include HEAD commit
            const isOnMainBranch = await this._isOnMainBranch(gitAdapter);
            // If not on main branch, skip the HEAD commit
            if (!isOnMainBranch) {
                skipCount = 1;
                this._logger.debug("Not on main branch, skipping HEAD commit");
            }

            // Process commits in batches until we reach the maximum, find a known commit, or run out of commits
            while (hasMoreCommits && totalProcessedCommits < maxCommits && !foundKnownCommit) {
                // Calculate how many commits to fetch in this batch.
                // We need to batch because if the output is too long child_process throws an error.
                const batchSize = Math.min(
                    COMMIT_FETCH_BATCH_SIZE,
                    maxCommits - totalProcessedCommits
                );

                this._logger.verbose(`Fetching batch of ${batchSize} commits (skip=${skipCount})`);

                // Use skip parameter to paginate through commits
                // Include --name-status to get file changes in the same call
                const logResult = await gitAdapter.log({
                    format: `${SEPARATOR}%n${FORMAT}`,
                    commit1: `-n${batchSize} --skip=${skipCount}`,
                    commit2: `refs/heads/${branch}`,
                    noMerges: true,
                    nameStatus: true,
                });

                if (!logResult || logResult.trim() === "") {
                    hasMoreCommits = false;
                    break;
                }

                // Split by commit separator and remove the first empty element
                const commitBlocks = logResult.split(`${SEPARATOR}\n`).slice(1);
                const batchCommits: CommitInfo[] = [];

                for (const commitBlock of commitBlocks) {
                    if (!commitBlock.trim()) {
                        continue;
                    }

                    // Parse the commit block which contains commit info followed by name-status lines
                    const parsedCommit = this._parseCommitBlock(commitBlock);
                    if (!parsedCommit) {
                        continue;
                    }

                    const {
                        hash,
                        authorName,
                        authorEmail,
                        timestamp,
                        subject,
                        body,
                        filesChanged,
                    } = parsedCommit;

                    // Add diff information to file changes
                    await this._addDiffsToFileChanges(gitAdapter, hash, filesChanged);

                    // Create the commit info object
                    const commitInfo: CommitInfo = {
                        hash,
                        authorName,
                        authorEmail,
                        timestamp,
                        subject,
                        body,
                        filesChanged,
                    };

                    // If we're looking for new commits, check if we've already seen this one
                    if (stopOnKnownCommit && folder) {
                        const blobName = this._getBlobName(commitInfo);
                        const folderBlobNames = this._folderKnownBlobNames.get(folder.uri.fsPath);
                        if (folderBlobNames && folderBlobNames.has(blobName)) {
                            this._logger.debug(
                                `Found known commit ${hash} with blob name ${blobName}, stopping scan`
                            );
                            foundKnownCommit = true;
                            break;
                        }
                    }

                    batchCommits.push(commitInfo);

                    if (totalProcessedCommits + batchCommits.length >= maxCommits) {
                        break;
                    }
                }

                // Upload this batch immediately if we have commits
                if (batchCommits.length > 0) {
                    this._uploadCommitBatch(batchCommits, folder);
                    totalProcessedCommits += batchCommits.length;

                    this._logger.verbose(
                        `Processed batch of ${batchCommits.length} commits, total: ${totalProcessedCommits}`
                    );
                }

                // If we found a known commit, stop processing
                if (foundKnownCommit) {
                    break;
                }

                // If we got fewer commits than requested, there are no more commits
                if (batchCommits.length < batchSize) {
                    hasMoreCommits = false;
                }

                // Update skip count for the next batch
                skipCount += batchCommits.length;
            }

            // Mark that we need to create a checkpoint after all uploads complete
            if (totalProcessedCommits > 0) {
                this._needsCheckpoint = true;
            }

            return totalProcessedCommits > 0;
        } finally {
            // Clear the processing flag so checkpointing can proceed
            this._processingBatches = false;
        }
    }

    /**
     * Upload a batch of commits immediately to avoid memory accumulation.
     */
    private _uploadCommitBatch(commits: CommitInfo[], folder: vscode.WorkspaceFolder): void {
        if (commits.length === 0) {
            return;
        }

        this._logger.verbose(`Uploading batch of ${commits.length} commits in ${folder.name}`);

        for (const commit of commits) {
            const uploadRequest: BlobUploadRequest = {
                path: `git://commit/${commit.hash}`,
                readContent: async () => Promise.resolve(this._formatCommitAsString(commit)),
            };
            const blobName = this._blobUploader.enqueueUpload(
                uploadRequest,
                this._formatCommitAsString(commit)
            );

            // Track this upload as pending - will be added to known blobs when indexed
            if (blobName) {
                this._pendingUploads.set(blobName, folder.uri.fsPath);
            }
        }

        // Start the upload process for this batch
        this._blobUploader.startUpload();
    }

    /**
     * Parse a commit block that contains commit info followed by name-status lines.
     */
    private _parseCommitBlock(commitBlock: string): {
        hash: string;
        authorName: string;
        authorEmail: string;
        timestamp: number;
        subject: string;
        body: string;
        filesChanged: FileChange[];
    } | null {
        const lines = commitBlock.split("\n");

        // Need at least 6 lines for basic commit info
        if (lines.length < 6) {
            return null;
        }

        const hash = lines[0];
        const authorName = lines[1];
        const authorEmail = lines[2];
        const timestamp = parseInt(lines[3], 10);
        const subject = lines[4];

        // Name-status lines are always at the end of the commit block
        // Work backwards from the end to find where they start
        // Skip empty lines at the end
        let lastNonEmptyIndex = lines.length - 1;
        while (lastNonEmptyIndex >= 5 && !lines[lastNonEmptyIndex].trim()) {
            lastNonEmptyIndex--;
        }

        // Work backwards to find the first name-status line
        // Name-status lines contain tabs, commit message lines typically don't
        let nameStatusStartIndex = lastNonEmptyIndex + 1;
        for (let i = lastNonEmptyIndex; i >= 5; i--) {
            const line = lines[i];
            if (line.includes("\t")) {
                nameStatusStartIndex = i;
            } else {
                break; // Found a line without tab, so we've reached the commit message
            }
        }

        const body = lines.slice(5, nameStatusStartIndex).join("\n").trim();
        const nameStatusLines = lines.slice(nameStatusStartIndex, lastNonEmptyIndex + 1);

        // Parse file changes from name-status lines
        const filesChanged = this._parseFileChanges(nameStatusLines);

        return {
            hash,
            authorName,
            authorEmail,
            timestamp,
            subject,
            body,
            filesChanged,
        };
    }

    /**
     * Parse file changes from name-status lines.
     */
    private _parseFileChanges(nameStatusLines: string[]): FileChange[] {
        const filesChanged: FileChange[] = [];

        for (const line of nameStatusLines) {
            if (!line.trim()) {
                continue;
            }

            const parts = line.split("\t");
            if (parts.length < 2) {
                continue;
            }

            // Keep the full change type including similarity scores (e.g., "R100", "C050")
            const changeType = parts[0];

            // parts[1] is always the file path, parts[2] is the new path for renames/copies
            const filePath = parts.length > 2 ? parts[2] : parts[1];
            const oldFilePath = parts.length > 2 ? parts[1] : undefined;

            const fileChange: FileChange = {
                changeType,
                filePath,
                oldFilePath,
            };

            filesChanged.push(fileChange);
        }

        return filesChanged;
    }

    /**
     * Add diff information to file changes by fetching the full diff for the commit.
     */
    private async _addDiffsToFileChanges(
        gitAdapter: GitAdapterImpl,
        commitHash: string,
        filesChanged: FileChange[]
    ): Promise<void> {
        // Create a map for quick lookup of file changes
        const fileChangeMap = new Map<string, FileChange>();
        for (const fileChange of filesChanged) {
            fileChangeMap.set(fileChange.filePath, fileChange);
            if (fileChange.oldFilePath) {
                fileChangeMap.set(fileChange.oldFilePath, fileChange);
            }
        }

        // Now, get the full diff for the commit
        const fullDiff = await gitAdapter.show({
            object: commitHash,
            patch: true,
        });
        if (fullDiff) {
            // Parse the full diff to extract per-file diffs
            this._extractFileDiffs(fullDiff, fileChangeMap);
        } else {
            this._logger.debug(`Failed to get full diff for commit ${commitHash}`);
        }
    }

    /**
     * Extract individual file diffs from a full commit diff.
     */
    private _extractFileDiffs(fullDiff: string, fileChangeMap: Map<string, FileChange>): void {
        // Split the diff by file
        const diffLines = fullDiff.split("\n");
        let currentFile: string | null = null;
        let currentDiff: string[] = [];
        let inHeader = false;
        let headerEndIndex = -1;

        for (let i = 0; i < diffLines.length; i++) {
            const line = diffLines[i];

            // Check if this line starts a new file diff
            if (line.startsWith("diff --git ")) {
                // If we were processing a file, save its diff
                if (currentFile !== null && headerEndIndex !== -1) {
                    // Only include lines after the header
                    const cleanDiff = currentDiff.slice(headerEndIndex + 1).join("\n");
                    this._saveFileDiff(currentFile, cleanDiff, fileChangeMap);
                }

                // Reset for the new file
                currentDiff = [];
                headerEndIndex = -1;
                inHeader = true;

                // Extract the file path from the diff header
                // Format: "diff --git a/path/to/file b/path/to/file"
                const match = line.match(/diff --git a\/(.+) b\/.+/);
                if (match && match[1]) {
                    currentFile = match[1];
                } else {
                    currentFile = null;
                }
            }

            // Check if we're still in the header section
            if (inHeader) {
                // Headers typically end when we see the first line starting with "@@"
                if (line.startsWith("@@")) {
                    inHeader = false;
                    headerEndIndex = currentDiff.length;
                }
            }

            // Add the line to the current diff
            if (currentFile !== null) {
                currentDiff.push(line);
            }
        }

        // Save the last file's diff
        if (currentFile !== null && headerEndIndex !== -1) {
            // Only include lines after the header
            const cleanDiff = currentDiff.slice(headerEndIndex + 1).join("\n");
            this._saveFileDiff(currentFile, cleanDiff, fileChangeMap);
        }
    }

    /**
     * Save a file diff to the corresponding FileChange object.
     */
    private _saveFileDiff(
        filePath: string,
        diff: string,
        fileChangeMap: Map<string, FileChange>
    ): void {
        if (diff.trim() === "") {
            return;
        }
        const fileChange = fileChangeMap.get(filePath);
        if (fileChange) {
            fileChange.diff = diff;
        }
    }

    private _formatCommitAsString(commit: CommitInfo): string {
        return JSON.stringify(commit);
    }

    /**
     * Get the blob name for a commit.
     */
    private _getBlobName(commit: CommitInfo): string {
        return this._blobNameCalculator.calculateNoThrow(
            `git://commit/${commit.hash}`,
            this._formatCommitAsString(commit)
        );
    }

    /**
     * Get the current checkpoint ID for git commits.
     * This can be used to efficiently retrieve all indexed commits.
     * @returns The checkpoint ID, or undefined if no checkpoint has been created
     */
    public getCheckpointId(): string | undefined {
        return this._checkpointId;
    }

    /**
     * Start polling for new commits as a backup mechanism.
     * This provides redundancy in case HEAD change events are missed.
     */
    private _startPolling(): void {
        if (this._pollingTimer) {
            return;
        }

        this._pollingTimer = setInterval(() => {
            void this._pollForNewCommits();
        }, DEFAULT_POLLING_INTERVAL_MS);
        this.addDisposable(new vscode.Disposable(() => this._stopPolling()));

        this._logger.debug(`Started commit polling with interval ${DEFAULT_POLLING_INTERVAL_MS}ms`);
    }

    /**
     * Stop the polling timer.
     */
    private _stopPolling(): void {
        if (this._pollingTimer) {
            clearInterval(this._pollingTimer);
            this._pollingTimer = undefined;
            this._logger.debug("Stopped commit polling");
        }
    }
}
