import { EventEmitter, RelativePattern, workspace } from "vscode";

import { getLogger } from "../../logging";
import { DisposableService } from "../../utils/disposable-service";
import * as enums from "../enums";
import { VCSDetails } from "../vcs-finder";

export type HeadChangeEvent = {
    repoId: number;
};

const emitter = new EventEmitter<HeadChangeEvent>();

export const onDidChange = emitter.event;

/**
 * @description - listens to HEAD changes on VCS (Currently specific for GIT) and notifies.
 * @event onDidChange - when HEAD changes
 *
 * <code>
 * const watcher = new HeadChangeWatcherImpl();
 * watcher.listenForChanges(vcsDetails);
 * ...
 * watcher.dispose();
 * </code>
 *
 * Consuming events example:
 * <code>
 * const headChangeWatcher = require('./head-change-watcher');
 * headChangeWatcher.onDidChange(() => { ... })
 * </code>
 */

export class HeadChangeWatcher extends DisposableService {
    private _logger;
    private listening = false;

    constructor(
        folderName: string,
        private _reportId: number,
        private _vcsDetails: VCSDetails
    ) {
        super();
        this._logger = getLogger(`HeadChangeWatcher[${folderName}]`);
    }

    handleChange = () => {
        this._logger.debug("handling HEAD change");
        emitter.fire({ repoId: this._reportId });
    };

    listenForChanges(): void {
        if (this.listening) {
            return;
        }
        if (this._vcsDetails.toolName !== enums.VCSToolName.git) {
            throw new Error("only git is supported for now");
        }

        const fileSystemWatcher = workspace.createFileSystemWatcher(
            new RelativePattern(this._vcsDetails.root, ".git/logs/HEAD")
        );
        this.addDisposables(
            fileSystemWatcher,
            fileSystemWatcher.onDidCreate(this.handleChange),
            fileSystemWatcher.onDidChange(this.handleChange),
            fileSystemWatcher.onDidDelete(this.handleChange)
        );
        this.listening = true;
        this._logger.debug("Listening for HEAD changes.");
    }
}
