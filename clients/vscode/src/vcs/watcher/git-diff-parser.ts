import { ChangeType, FileChange } from "./types";

enum GitDiffChangeType {
    added = "A",
    deleted = "D",
    modified = "M",
    renamed = "R",
}

const TYPE_MAP: Record<GitDiffChangeType, ChangeType> = Object.freeze({
    [GitDiffChangeType.added]: ChangeType.added,
    [GitDiffChangeType.deleted]: ChangeType.deleted,
    [GitDiffChangeType.modified]: ChangeType.modified,
    [GitDiffChangeType.renamed]: ChangeType.renamed,
});

const RENAME_PATTERN = new RegExp(`^${GitDiffChangeType.renamed}\\d{3}$`);
const NEWLINE_SEPARATOR = "\n"; // TODO: use os.EOL
const COLUMN_SEPARATOR = "\t";

function stringToEnumValue<T>(enumType: T, value: string): T[keyof T] | undefined {
    for (const key in enumType) {
        if (enumType[key] === value) {
            return enumType[key];
        }
    }
}

function getGitChangeType(type: string): GitDiffChangeType {
    const isRename = RENAME_PATTERN.test(type);
    const gitDiffChangeType = isRename
        ? GitDiffChangeType.renamed
        : stringToEnumValue(GitDiffChangeType, type);

    if (!gitDiffChangeType) {
        throw new Error(`Unknown diff change type [${type}]`);
    }
    return gitDiffChangeType;
}

function parseType(type: string): ChangeType {
    return TYPE_MAP[getGitChangeType(type)];
}

function getBeforePath(
    pathA: string,
    _pathB: string | undefined,
    changeType: ChangeType
): string | undefined {
    const changePathMap: Record<ChangeType, string | undefined> = {
        [ChangeType.added]: undefined,
        [ChangeType.deleted]: pathA,
        [ChangeType.modified]: pathA,
        [ChangeType.renamed]: pathA,
    };

    return changePathMap[changeType];
}

function getAfterPath(
    pathA: string,
    pathB: string | undefined,
    changeType: ChangeType
): string | undefined {
    const changePathMap: Record<ChangeType, string | undefined> = {
        [ChangeType.added]: pathA,
        [ChangeType.deleted]: undefined,
        [ChangeType.modified]: pathA,
        [ChangeType.renamed]: pathB!,
    };

    return changePathMap[changeType];
}

/**
 * @example
 *
 *  M       foo.js
 *  A       foo bar.txt
 *  D       src/old.js
 *  R100    src/main.js   src/index.js
 * @param text
 */
function parse(text: string): FileChange[] {
    const result = [];
    const lines = text.split(NEWLINE_SEPARATOR);

    for (const line of lines) {
        if (!line || line.trim().length === 0) {
            // empty commits or line ending at end of output can reach here.
            continue;
        }
        const [rawType, pathA, pathB] = line.split(COLUMN_SEPARATOR);

        const changeType = parseType(rawType);

        const item = {
            beforePath: getBeforePath(pathA, pathB, changeType),
            afterPath: getAfterPath(pathA, pathB, changeType),
            changeType,
        };

        result.push(item);
    }

    return result;
}

export { parse };
