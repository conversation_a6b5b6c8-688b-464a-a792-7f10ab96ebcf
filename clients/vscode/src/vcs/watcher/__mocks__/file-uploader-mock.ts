import { Event, EventEmitter } from "vscode";

import { FileUploader } from "../placeholders/interfaces";
import { UploadedEventData } from "../placeholders/interfaces";

export class FileUploaderMock implements FileUploader {
    private _emitter = new EventEmitter<UploadedEventData>();
    upload = jest.fn().mockImplementation((_path: string, _content: string) => {
        return "blobName";
    });

    get onDidChange(): Event<UploadedEventData> {
        return this._emitter.event;
    }
}
