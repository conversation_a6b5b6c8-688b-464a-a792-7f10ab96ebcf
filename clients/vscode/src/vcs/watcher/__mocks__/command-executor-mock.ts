import { getLogger } from "../../../__mocks__/logging";
import { CommandExecutor } from "../../git-adapter";

const logger = getLogger("CommandExecutorMock");

type CommandItemMock = {
    command: string;
    stdout: string;
};
export class CommandExecutorMock {
    private _commandOutput: CommandItemMock[] = []; // commands to mock (fifo)

    withCommandStdout(command: string, stdout: string) {
        this._commandOutput.push({ command, stdout });
        return this;
    }

    reset() {
        this._commandOutput = [];
    }

    spy: CommandExecutor & jest.Mock<any, any> = jest
        .fn()
        .mockImplementation(async (command: string, _options?: any) => {
            logger.info(`🤖 moogi Executing command: [${command}]`);
            if (this._commandOutput[0].command === command) {
                return this._commandOutput.shift()!.stdout;
            }
        });
}
