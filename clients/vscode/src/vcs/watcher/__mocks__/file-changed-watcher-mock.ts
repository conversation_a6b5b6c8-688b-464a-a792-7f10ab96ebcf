import { Event, EventEmitter } from "vscode";

import { FileChangeDetails, FileChangeWatcher } from "../placeholders/interfaces";

export class FileChangeWatcherMock implements FileChangeWatcher {
    private _emitter = new EventEmitter<FileChangeDetails>();
    private _pathsWithBufferChanges: string[] = [];

    withPathsWithBufferChanges(_paths: string[]): FileChangeWatcherMock {
        this._pathsWithBufferChanges = _paths;
        return this;
    }

    getPathsWithBufferChanges = jest.fn().mockImplementation(() => {
        return this._pathsWithBufferChanges;
    });

    get onDidChangeFile(): Event<FileChangeDetails> {
        return this._emitter.event;
    }
}
