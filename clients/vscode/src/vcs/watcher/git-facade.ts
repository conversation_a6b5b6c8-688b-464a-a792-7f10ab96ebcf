import { ChangeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";

import { GitAdapter } from "../git-adapter";
import * as gitDiffParser from "./git-diff-parser";
import * as types from "./types";

/**
 * @description - a wrapper for the git adapter that provides convenience methods for getting changes
 */
class GitFacade implements types.VCSFacade {
    constructor(private _git: GitAdapter) {}

    /**
     *
     * @returns list of changes in working directory (file path, change type)
     */
    async getWorkingDirectoryChanges(): Promise<types.FileChange[]> {
        const NEWLINE_SEPARATOR = "\n";
        let result: types.FileChange[] = [];
        let foundFiles: Set<string> = new Set();

        const diffResult = await this._git.diff({ hash1: "HEAD", nameStatus: true });
        if (diffResult) {
            result = gitDiffParser.parse(diffResult);
            for (const change of result.slice(0, 50)) {
                // limit to 50 to avoid big workspace changes causing load on vscode.
                if (change.afterPath !== undefined) {
                    foundFiles.add(change.afterPath);
                }
            }
        }

        const lsFilesResult = await this._git.lsFiles({ others: true, excludeStandard: true });
        if (lsFilesResult) {
            const changes: types.FileChange[] = lsFilesResult
                .split(NEWLINE_SEPARATOR)
                .filter((line) => {
                    return line.trim().length > 0 && !foundFiles.has(line);
                })
                .map((path) => ({
                    afterPath: path,
                    beforePath: undefined,
                    changeType: ChangeType.added,
                }));
            result = result.concat(changes);
        }
        return result;
    }

    /**
     * @description current implementation assumes remote exists and "origin" is the default remote.
     * @returns default branch name. undefined if no default branch.
     */
    private async _getDefaultBranch(): Promise<string | undefined> {
        const PREFIX = "refs/remotes/origin";
        const result = await this._git.symbolicRef({ name: `${PREFIX}/HEAD` });
        if (result) {
            // trim will remove the newline
            return result.trim().replace(PREFIX + "/", "");
        }
    }

    /**
     *
     * @returns list of commit hashes on current branch only.
     */
    private async _getCommitHashes(): Promise<string[]> {
        const HASH_ONLY_FORMAT = "%H";
        const NEWLINE_SEPARATOR = "\n";

        const defaultBranch = await this._getDefaultBranch();
        const commitHashes = await this._git.log({
            commit1: defaultBranch,
            commit2: "HEAD",
            noMerges: true,
            format: HASH_ONLY_FORMAT,
            not: defaultBranch,
        });
        return commitHashes?.split(NEWLINE_SEPARATOR) ?? []; // TODO: use os.EOL
    }

    /**
     *
     * @returns map of commit hash to list of changes (file path, change type)
     * @throws if failed to collect commit changes. (e.g. command returned undefined unexpectedly)
     */
    async getCommitChanges(): Promise<Record<string, types.FileChange[]>> {
        const NEWLINE_SEPARATOR = "\n"; // use os.EOL
        const result: Record<string, types.FileChange[]> = {};
        const commitHashes = await this._getCommitHashes();

        for (const commitHash of commitHashes) {
            const commitResult = await this._git.show({
                object: commitHash,
                nameStatus: true,
                oneLine: true,
            });
            // remove the first line of the result - first line is the commit hash
            // remove it here to keep the parser simple and shared between show and diff.
            const commitResultBody = commitResult?.slice(
                commitResult.indexOf(NEWLINE_SEPARATOR) + 1
            );
            if (commitResultBody === undefined || commitResultBody === null) {
                // only handle unexpected behavior. empty commits are fine and handled by parser.
                throw new Error(`Could not get commit changes for commit ${commitHash}`);
            }
            const changes = gitDiffParser.parse(commitResultBody); // once first line is removed, output is similar to diff.
            result[commitHash] = changes;
        }
        return result;
    }

    async getFileWorkingDirectoryChange(relPath: string): Promise<types.FileChange | undefined> {
        const diffResult = await this._git.diff({
            hash1: "HEAD",
            nameStatus: true,
            relPath: relPath,
        });
        if (diffResult) {
            const result = gitDiffParser.parse(diffResult);
            if (result.length > 0) {
                return result[0];
            }
        }

        const lsFilesResult = await this._git.lsFiles({
            others: true,
            excludeStandard: true,
            relPath: relPath,
        });
        if (lsFilesResult?.trim() === relPath) {
            return {
                afterPath: relPath,
                beforePath: undefined,
                changeType: ChangeType.added,
            };
        }
    }

    /**
     *
     * @param relPath path of file relative to git repo root
     * @param commitHash hash of commit
     * @returns content of the file for given commit
     * @throws if file was not found
     */
    async getFileContentForCommit(relPath: string, commitHash: string): Promise<string> {
        const result = await this._git.show({
            object: `${commitHash}:${relPath}`,
        });

        if (result === undefined) {
            throw new Error(`Could not find file ${relPath} in commit ${commitHash}`);
        }

        return result;
    }

    /**
     *
     * @param relPath - if file was renamed, this needs to be the old path.
     * @param commitHash - path of the commit that changed the file.
     * @returns content of the file
     * @throws if file was not found
     */
    async getFileContentBeforeCommit(relPath: string, commitHash: string): Promise<string> {
        return this.getFileContentForCommit(relPath, `${commitHash}^`);
    }
}

export { GitFacade };
