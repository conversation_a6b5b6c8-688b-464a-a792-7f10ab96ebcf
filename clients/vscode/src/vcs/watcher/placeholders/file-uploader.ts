import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { Event, EventEmitter } from "vscode";

import { APIServer } from "../../../augment-api";
import { getLogger } from "../../../logging";
import { DisposableService } from "../../../utils/disposable-service";
import { OrderedWorkQueue } from "../../../utils/work-queue";
import { FileUploader, UploadedEventData } from "./interfaces";

type FileContent = {
    path: string;
    content: string;
    blobName: string;
};
class FileUploaderImpl extends DisposableService implements FileUploader {
    private _emitter = new EventEmitter<UploadedEventData>();
    private _uploadQueue: OrderedWorkQueue<FileContent>;
    private _logger = getLogger("FileUploaderImpl");

    constructor(
        private _blobNameCalculator: BlobNameCalculator,
        private _apiServer: APIServer
    ) {
        super();
        this._uploadQueue = new OrderedWorkQueue<FileContent>(
            async (diff: FileContent | undefined): Promise<void> => {
                if (diff === undefined) {
                    return;
                }
                await this._processUpload(diff);
            }
        );

        this.addDisposable(this._uploadQueue);
    }
    get onDidChange(): Event<UploadedEventData> {
        return this._emitter.event;
    }

    upload(path: string, content: string): Promise<string> {
        const blobName = this._blobNameCalculator.calculate(path, content);
        if (blobName === undefined) {
            // TODO: consider using error event instead.
            throw new Error(`blobNameCalculator returned undefined for ${path}`);
        }
        this._uploadQueue.insert({ path, content, blobName });
        void this._uploadQueue.kick();
        return Promise.resolve(blobName);
    }

    // _processUpload is the `processItem` method for the _uploadQueue WorkQueue. It
    // uploads the blob to the back end.
    private async _processUpload(fileContent: FileContent) {
        try {
            this._logger.debug(
                `Upload started: path [${fileContent.path}] with blob name [${fileContent.blobName}]`
            );

            // TODO: find missing
            await this._apiServer.batchUpload([
                {
                    // TODO: use bulk
                    pathName: fileContent.path,
                    text: fileContent.content,
                    blobName: fileContent.blobName,
                    metadata: [],
                },
            ]);
            this._logger.debug(
                `Upload complete: path [${fileContent.path}] with blob name [${fileContent.blobName}]`
            );
        } catch (e: any) {
            this._logger.debug(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Failed upload for [${fileContent.path}]. Caused by: ${e.stack}.`
            );
        }
    }
}

export { FileUploaderImpl };
