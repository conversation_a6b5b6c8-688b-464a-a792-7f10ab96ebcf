import { Event, Uri, window, workspace } from "vscode";

import { fileIsReadable } from "../../../utils/fs-utils";
import { descendentPath, directoryContainsPath } from "../../../utils/path-utils";
import { hasProperty } from "../../../utils/types-utils";
import { FileChangeDetails, FileChangeWatcher } from "./interfaces";

type OpenTabDetails = {
    label: string;
    uri: Uri;
    isDirty: boolean;
};

class FileChangeWatcherImpl implements FileChangeWatcher {
    constructor(
        private readonly _rootPath: Uri,
        public readonly onDidChangeFile: Event<FileChangeDetails>
    ) {}

    private async _getOpenTabs(): Promise<OpenTabDetails[]> {
        const result: OpenTabDetails[] = [];

        // We find all open tabs. This is different from when we initialize OpenFileManager,
        // we should consider to consolidate in the future.
        for (const tabGroup of window.tabGroups.all) {
            for (const tab of tabGroup.tabs) {
                const input = tab.input;

                if (!hasProperty(input, "uri")) {
                    continue;
                }

                const resourceUri = input.uri as Uri;

                // check file exists - in case the tab is open but the actual file is not there.
                // in this case we skip it.
                // we explicitly check it here because vscode throws error if you try to interact with such a document.
                const exists = await fileIsReadable(resourceUri.fsPath);
                if (!exists) {
                    continue;
                }

                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument

                let document = null;
                try {
                    // openTextDocument OK: this resource is from a window-visible
                    // tab, so no issue notifying others that we're opening it if it
                    // somehow isn't already open
                    document = await workspace.openTextDocument(resourceUri);
                } catch (e) {
                    // happens on binary files. No vscode api to know if file is binary.
                    continue;
                }
                const isDirty = document.isDirty;
                result.push({
                    label: tab.label,
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    uri: resourceUri,
                    isDirty,
                });
            }
        }

        return result;
    }

    async getPathsWithBufferChanges(): Promise<string[]> {
        const openTabs = await this._getOpenTabs();

        const tabsWithChanges = openTabs.filter(
            (tab) => tab.isDirty && directoryContainsPath(this._rootPath.fsPath, tab.uri.fsPath)
        );

        const result: string[] = [];
        for (const tab of tabsWithChanges) {
            const path = descendentPath(this._rootPath.fsPath, tab.uri.fsPath);
            if (path !== undefined) {
                result.push(path);
            }
        }
        return result;
    }
}

export { FileChangeWatcherImpl };
