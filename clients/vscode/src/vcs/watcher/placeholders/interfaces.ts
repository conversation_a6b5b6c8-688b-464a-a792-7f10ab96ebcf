import { Event } from "vscode";

import { FileChangeDetails } from "../../../workspace/workspace-manager";

/**
 * This file holds interfaces required by vcs watcher to work.
 * It is a convenience as we refactor the system.
 * Once the refactoring is complete, we should revisit.
 */

interface FileUtils {
    /**
     *
     * @param path
     *
     * TODO: add file size and isBinary parameters
     * https://linear.app/augmentcode/issue/AU-2897/add-file-size-and-is-binary-for-isuploadable-call
     */
    isUploadable(path: string): boolean;
}

interface BlobNameRetriever {
    getIndexedBlobName(path: string): string | undefined;
    getBufferBlobName(relPath: string): Promise<string>;
}

type FileUploadRequest = {
    path: string;
    readContent: () => Promise<string>;
};

interface FileChangeWatcher {
    get onDidChangeFile(): Event<FileChangeDetails>;

    getPathsWithBufferChanges(): Promise<string[]>;
}

type UploadedEventData = {
    fileName: string;
    blobName: string;
    uploadTime: Date;
};

interface FileUploader {
    upload(path: string, content: string): Promise<string>; // returns the blob name
    get onDidChange(): Event<UploadedEventData>;

    // TODO: think about dispatching an error
}

export {
    type FileUtils,
    type FileUploadRequest,
    type BlobNameRetriever,
    type FileUploader,
    type UploadedEventData,
    type FileChangeWatcher,
    type FileChangeDetails,
};
