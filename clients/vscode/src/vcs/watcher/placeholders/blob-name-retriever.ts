import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { viewTextDocument } from "../../../workspace/view-text-document";
import { WorkspaceManager } from "../../../workspace/workspace-manager";
import { BlobNameRetriever } from "./interfaces";

class BlobNameRetrieverImpl implements BlobNameRetriever {
    constructor(
        private _rootPath: string,
        private _workspaceManager: WorkspaceManager,
        private _blobNameCalculator: BlobNameCalculator
    ) {}

    async getBufferBlobName(relPath: string): Promise<string> {
        const resourceUri = vscode.Uri.file(joinPath(this._rootPath, relPath));

        const document = await viewTextDocument(resourceUri);
        const text = document.getText();
        const blobName = this._blobNameCalculator.calculate(relPath, text);
        return blobName!;
    }

    getIndexedBlobName(relPath: string): string | undefined {
        return this._workspaceManager.getBlobName(new QualifiedPathName(this._rootPath, relPath));
    }
}

export { BlobNameRetrieverImpl };
