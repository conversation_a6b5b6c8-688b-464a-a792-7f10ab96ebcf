import { ChangeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    Commit,
    CommitChange,
    VCSChange,
    WorkingDirectoryChange,
} from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import { cloneDeep } from "lodash";

import { AugmentConfigListener } from "../../augment-config-listener";
import { getLogger } from "../../logging";
import { DisposableService } from "../../utils/disposable-service";
import { FileChangeOrigin } from "../../workspace/workspace-manager";
import * as placeholders from "./placeholders/interfaces";
import * as types from "./types";

class VCSRepoWatcherImpl extends DisposableService implements types.VCSRepoWatcher {
    private _changes: VCSChange;
    // keep track of vcs working directory relative paths.
    // we expect these paths to equal source repo relative paths.
    // we use this to avoid creating duplicate entries of changes as the user modifies files.
    private _workingDirectoryTrackedPaths: Set<string> = new Set();
    private _trackedBlobs: Map<string, placeholders.FileUploadRequest> = new Map();
    private _logger;

    constructor(
        private _workspaceName: string,
        private _vcs: types.VCSFacade,
        private _fileUtils: placeholders.FileUtils,
        private _fileUploader: placeholders.FileUploader,
        private _fileChangeWatcher: placeholders.FileChangeWatcher,
        private _blobNameRetriever: placeholders.BlobNameRetriever,

        private _configListener: AugmentConfigListener
    ) {
        super();
        this._logger = getLogger(`VCSRepoWatcher[${_workspaceName}]`);
        this._changes = {
            commits: [],
            workingDirectory: [],
        };

        this.addDisposable(
            _fileUploader.onDidChange((data: placeholders.UploadedEventData) =>
                this.handleFileUploaded(data)
            )
        );

        this.addDisposable(
            _fileChangeWatcher.onDidChangeFile((data: placeholders.FileChangeDetails) =>
                this.handleFileChange(data)
            )
        );
    }

    /**
     *
     * @param request
     * @param content
     * @returns blob name
     */
    async _uploadFile(request: placeholders.FileUploadRequest): Promise<string> {
        const content = await request.readContent();
        const blobName = await this._fileUploader.upload(request.path, content);
        this._trackedBlobs.set(blobName, request);
        return blobName;
    }

    private _clear() {
        this._changes = {
            commits: [],
            workingDirectory: [],
        };
        this._workingDirectoryTrackedPaths.clear();
        this._trackedBlobs.clear();
    }

    async handleHeadChange(): Promise<void> {
        this._logger.debug("Handling head changes");

        this._clear();

        try {
            const commitChanges = await this.collectCommitChanges();
            this._changes.commits = commitChanges;
        } catch (e) {
            this._logger.error("Error collecting commit changes", e);
        }

        const wdChanges = await this.collectWorkingDirectoryChanges();
        const bufferChanges = await this.collectBufferChanges();

        this._changes.workingDirectory = wdChanges.concat(bufferChanges);
    }

    handleFileUploaded(_event: placeholders.UploadedEventData) {
        // TODO: Maybe we don't need this. (too early optimization?)
        throw new Error("Method not implemented.");
    }

    async handleFileChange(event: placeholders.FileChangeDetails) {
        this._logger.debug(`Handling file change [${event.relPath}]`);
        // assume that if we got an event - the file is uploadable.
        if (
            event.origin === FileChangeOrigin.buffer &&
            this._workingDirectoryTrackedPaths.has(event.relPath)
        ) {
            return;
        }

        if (event.origin === FileChangeOrigin.disk) {
            // remove old state
            this._changes.workingDirectory = this._changes.workingDirectory.filter(
                (wd) => wd.afterPath !== event.relPath
            );
        }
        this._workingDirectoryTrackedPaths.add(event.relPath);

        let fileChange: types.FileChange | undefined = undefined;

        if (event.origin === FileChangeOrigin.disk) {
            fileChange = await this._vcs.getFileWorkingDirectoryChange(event.relPath);
        } else if (event.origin === FileChangeOrigin.buffer) {
            // buffer changes
            fileChange = {
                afterPath: event.relPath,
                beforePath: event.relPath,
                changeType: ChangeType.modified,
            };
        } else {
            this._logger.debug(`Unknown file change origin ${JSON.stringify(event)}`);
            return;
        }

        if (!fileChange) {
            // e.g. change was reverted.
            this._changes.workingDirectory = this._changes.workingDirectory.filter((wd) => {
                const renameObsolete =
                    wd.changeType === ChangeType.renamed && wd.afterPath === event.relPath;
                const deleteObsolete =
                    wd.changeType === ChangeType.deleted && wd.beforePath === event.relPath;
                const addObsolete =
                    wd.changeType === ChangeType.added && wd.afterPath === event.relPath;
                return !renameObsolete && !deleteObsolete && !addObsolete;
            });
            return;
        }

        let wdChange: WorkingDirectoryChange = {
            changeType: fileChange.changeType,
        };
        if (fileChange.beforePath) {
            wdChange.beforePath = fileChange.beforePath;
        }

        if (fileChange.afterPath) {
            wdChange.afterPath = fileChange.afterPath;
        }

        if (
            [ChangeType.deleted, ChangeType.modified, ChangeType.renamed].includes(
                fileChange.changeType
            )
        ) {
            wdChange.headBlobName = await this._uploadFile({
                path: fileChange.afterPath!,
                readContent: () => {
                    return this._vcs.getFileContentForCommit(fileChange.beforePath!, "HEAD");
                },
            });
        }

        if (ChangeType.renamed.includes(fileChange.changeType)) {
            this._changes.workingDirectory = this._changes.workingDirectory.filter(
                (wd) => wd.afterPath !== fileChange.beforePath
            );
        }

        this._changes.workingDirectory.push(wdChange);
    }

    async collectCommitChanges(): Promise<Commit[]> {
        const commits: Commit[] = [];

        this._logger.debug("Collecting disk changes");
        const commitChanges = await this._vcs.getCommitChanges();

        for (const commitHash in commitChanges) {
            const commit: Commit = {
                hash: commitHash,
                files: [],
            };
            for (const fileChange of commitChanges[commitHash]) {
                const commitChange: CommitChange = {
                    changeType: fileChange.changeType,
                };

                if (fileChange.afterPath !== undefined) {
                    if (!this._fileUtils.isUploadable(fileChange.afterPath)) {
                        continue;
                    }
                    commitChange.afterPath = fileChange.afterPath;
                    commitChange.afterBlobName = await this._uploadFile({
                        path: fileChange.afterPath,
                        readContent: () => {
                            return this._vcs.getFileContentForCommit(
                                fileChange.afterPath!,
                                commitHash
                            );
                        },
                    });
                }

                if (fileChange.beforePath !== undefined) {
                    if (!this._fileUtils.isUploadable(fileChange.beforePath)) {
                        continue;
                    }
                    commitChange.beforePath = fileChange.beforePath;

                    commitChange.beforeBlobName = await this._uploadFile({
                        path: fileChange.beforePath,
                        readContent: () => {
                            return this._vcs.getFileContentBeforeCommit(
                                fileChange.beforePath!,
                                commitHash
                            );
                        },
                    });
                }
                commit.files.push(commitChange);
            }
            commits.push(commit);
        }

        return commits;
    }

    async collectWorkingDirectoryChanges(): Promise<WorkingDirectoryChange[]> {
        const workingDirectoryChanges: WorkingDirectoryChange[] = [];

        const changedFiles = await this._vcs.getWorkingDirectoryChanges();
        for (const fileChange of changedFiles) {
            if ([ChangeType.added, ChangeType.modified].includes(fileChange.changeType)) {
                this._workingDirectoryTrackedPaths.add(fileChange.afterPath!);
            }
            const workingDirectoryChange: WorkingDirectoryChange = {
                changeType: fileChange.changeType,
            };

            if (fileChange.afterPath !== undefined) {
                if (!this._fileUtils.isUploadable(fileChange.afterPath)) {
                    continue;
                }
                workingDirectoryChange.afterPath = fileChange.afterPath;
            }

            if (fileChange.beforePath !== undefined) {
                if (!this._fileUtils.isUploadable(fileChange.beforePath)) {
                    continue;
                }
                workingDirectoryChange.beforePath = fileChange.beforePath;
                workingDirectoryChange.headBlobName = await this._uploadFile({
                    path: fileChange.beforePath,
                    readContent: () => {
                        return this._vcs.getFileContentForCommit(fileChange.beforePath!, "HEAD");
                    },
                });
            }

            workingDirectoryChanges.push(workingDirectoryChange);
        }
        return workingDirectoryChanges;
    }
    async collectBufferChanges(): Promise<WorkingDirectoryChange[]> {
        const workingDirectoryChanges: WorkingDirectoryChange[] = [];

        const pathsWithBufferChanges = await this._fileChangeWatcher.getPathsWithBufferChanges();
        for (const changedBufferPath of pathsWithBufferChanges) {
            if (!this._fileUtils.isUploadable(changedBufferPath)) {
                continue;
            }
            if (this._workingDirectoryTrackedPaths.has(changedBufferPath)) {
                continue;
            }
            this._workingDirectoryTrackedPaths.add(changedBufferPath);
            const workingDirectoryChange: WorkingDirectoryChange = {
                changeType: ChangeType.modified,
                afterPath: changedBufferPath,
                beforePath: changedBufferPath,
            };

            workingDirectoryChange.headBlobName = await this._uploadFile({
                path: changedBufferPath,
                readContent: () => {
                    return this._vcs.getFileContentForCommit(changedBufferPath, "HEAD");
                },
            });

            workingDirectoryChanges.push(workingDirectoryChange);
        }
        return workingDirectoryChanges;
    }

    async getChanges(): Promise<VCSChange> {
        const result = cloneDeep(this._changes);
        const workingDirectoryChanges = [];
        // add last known indexed blob for relevant records.
        for (const wdChange of result.workingDirectory.slice(-50)) {
            if (!wdChange.afterPath) {
                workingDirectoryChanges.push(wdChange);
                // handle only existing files.
                continue;
            }
            wdChange.indexedBlobName = this._blobNameRetriever.getIndexedBlobName(
                wdChange.afterPath
            );
            if (wdChange.indexedBlobName === undefined) {
                // augment does not index this file.. so skip.
                continue;
            }

            wdChange.currentBlobName = await this._blobNameRetriever.getBufferBlobName(
                wdChange.afterPath
            );

            workingDirectoryChanges.push(wdChange);
        }
        result.workingDirectory = workingDirectoryChanges;
        return result;
    }

    handleUnknownBlobs(unknownBlobNames: string[]): void {
        for (const blobName of unknownBlobNames) {
            const request = this._trackedBlobs.get(blobName);
            if (request === undefined) {
                continue;
            }
            void this._uploadFile(request);
        }
    }
}

export { VCSRepoWatcherImpl };
