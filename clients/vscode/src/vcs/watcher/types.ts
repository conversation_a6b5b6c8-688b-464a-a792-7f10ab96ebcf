import { ChangeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import * as vscode from "vscode";

import { VCSDetails } from "../vcs-finder";
import * as interfaces from "./placeholders/interfaces";

type FileChange = {
    afterPath: string | undefined;
    beforePath: string | undefined;
    changeType: ChangeType;
};

interface VCSFacade {
    getWorkingDirectoryChanges(): Promise<FileChange[]>;
    getCommitChanges(): Promise<Record<string, FileChange[]>>;
    getFileContentForCommit(path: string, commitHash: string): Promise<string>;
    getFileContentBeforeCommit(path: string, commitHash: string): Promise<string>;

    /**
     *
     * @param path path of file relative to git repo root
     * @returns file change in working directory if any.
     */
    getFileWorkingDirectoryChange(path: string): Promise<FileChange | undefined>;
}

/**
 * VCSRepoWatcher listens on changes in a single VCS repo.
 * Exposes a method to get the changes.
 * These include - past commits, working directory changes, and buffer changes.
 */
interface VCSRepoWatcher extends vscode.Disposable {
    getChanges(): Promise<VCSChange>;
    handleUnknownBlobs(unknownBlobNames: string[]): void;
    handleHeadChange(): Promise<void>;
}

/**
 * VCSWatcher provides a single interface to listen to changes in multiple VCS repos.
 */
interface VCSWatcher extends vscode.Disposable {
    startTracking(
        folderName: string,
        folderId: number,
        vcsDetails: VCSDetails,
        fileChangeWatcher: interfaces.FileChangeWatcher,
        blobNameRetriever: interfaces.BlobNameRetriever,
        fileUtils: interfaces.FileUtils
    ): vscode.Disposable;
    getChanges(): Promise<VCSChange>;
    getWatchedFolderIds(): number[];
    handleUnknownBlobs(unknownBlobNames: string[]): void;
    listenForEvents(): void;
}

export { ChangeType, type FileChange, type VCSFacade, type VCSRepoWatcher, type VCSWatcher };
