// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`git facade #getCommitChanges should return a list of files changed on working directory 1`] = `
{
  "12345": [
    {
      "afterPath": "foo bar.txt",
      "beforePath": undefined,
      "changeType": "ADDED",
    },
    {
      "afterPath": undefined,
      "beforePath": "src/old.js",
      "changeType": "DELETED",
    },
    {
      "afterPath": "src/index.js",
      "beforePath": "src/main.js",
      "changeType": "RENAMED",
    },
  ],
  "67890": [
    {
      "afterPath": "bar.txt",
      "beforePath": "bar.txt",
      "changeType": "MODIFIED",
    },
  ],
}
`;

exports[`git facade #getWorkingDirectoryChange should return a list of files changed on working directory 1`] = `
[
  {
    "afterPath": "modified file.js",
    "beforePath": "modified file.js",
    "changeType": "MODIFIED",
  },
  {
    "afterPath": "added file.txt",
    "beforePath": undefined,
    "changeType": "ADDED",
  },
  {
    "afterPath": undefined,
    "beforePath": "src/deleted_file.js",
    "changeType": "DELETED",
  },
  {
    "afterPath": "src/new_rename.js",
    "beforePath": "src/old_rename.js",
    "changeType": "RENAMED",
  },
  {
    "afterPath": "untracked file1.js",
    "beforePath": undefined,
    "changeType": "ADDED",
  },
  {
    "afterPath": "untracked_file2.txt",
    "beforePath": undefined,
    "changeType": "ADDED",
  },
  {
    "afterPath": "src/untracked3.js",
    "beforePath": undefined,
    "changeType": "ADDED",
  },
  {
    "afterPath": "src/untracked file4.js",
    "beforePath": undefined,
    "changeType": "ADDED",
  },
]
`;
