// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`vcs-watcher #collectBufferChanges should collect changes 1`] = `
[
  {
    "afterPath": "foo.js",
    "beforePath": "foo.js",
    "changeType": "MODIFIED",
    "headBlobName": "blobName",
  },
  {
    "afterPath": "src/old.js",
    "beforePath": "src/old.js",
    "changeType": "MODIFIED",
    "headBlobName": "blobName",
  },
]
`;

exports[`vcs-watcher #collectCommitsChanges should collect changes 1`] = `
[
  {
    "files": [
      {
        "afterBlobName": "blobName",
        "afterPath": "foo.js",
        "beforeBlobName": "blobName",
        "beforePath": "foo.js",
        "changeType": "MODIFIED",
      },
      {
        "afterBlobName": "blobName",
        "afterPath": "foo bar.txt",
        "changeType": "ADDED",
      },
      {
        "beforeBlobName": "blobName",
        "beforePath": "src/old.js",
        "changeType": "DELETED",
      },
      {
        "afterBlobName": "blobName",
        "afterPath": "src/index.js",
        "beforeBlobName": "blobName",
        "beforePath": "src/main.js",
        "changeType": "RENAMED",
      },
    ],
    "hash": "12345",
  },
  {
    "files": [
      {
        "afterBlobName": "blobName",
        "afterPath": "bar.txt",
        "beforeBlobName": "blobName",
        "beforePath": "bar.txt",
        "changeType": "MODIFIED",
      },
    ],
    "hash": "67890",
  },
]
`;

exports[`vcs-watcher #collectWorkingDirectoryChanges should collect changes 1`] = `
[
  {
    "afterPath": "foo.js",
    "beforePath": "foo.js",
    "changeType": "MODIFIED",
    "headBlobName": "blobName",
  },
  {
    "afterPath": "foo bar.txt",
    "changeType": "ADDED",
  },
  {
    "beforePath": "src/old.js",
    "changeType": "DELETED",
    "headBlobName": "blobName",
  },
  {
    "afterPath": "src/index.js",
    "beforePath": "src/main.js",
    "changeType": "RENAMED",
    "headBlobName": "blobName",
  },
  {
    "afterPath": "untracked file1.js",
    "changeType": "ADDED",
  },
  {
    "afterPath": "untracked_file2.txt",
    "changeType": "ADDED",
  },
  {
    "afterPath": "src/untracked3.js",
    "changeType": "ADDED",
  },
  {
    "afterPath": "src/untracked file4.js",
    "changeType": "ADDED",
  },
]
`;

exports[`vcs-watcher #handleFileChange should add a 'ADDED' change on disk change if added file not tracked 1`] = `
{
  "commits": [],
  "workingDirectory": [
    {
      "afterPath": "foo.js",
      "changeType": "ADDED",
      "currentBlobName": "bufferBlobName",
      "indexedBlobName": "blobName",
    },
  ],
}
`;

exports[`vcs-watcher #handleFileChange should add a 'DELETED' change on disk change 1`] = `
{
  "commits": [],
  "workingDirectory": [
    {
      "beforePath": "foo.js",
      "changeType": "DELETED",
      "headBlobName": "blobName",
    },
  ],
}
`;

exports[`vcs-watcher #handleFileChange should add a 'MODIFIED' change on buffer change 1`] = `
{
  "commits": [],
  "workingDirectory": [
    {
      "afterPath": "foo.js",
      "beforePath": "foo.js",
      "changeType": "MODIFIED",
      "currentBlobName": "bufferBlobName",
      "headBlobName": "blobName",
      "indexedBlobName": "blobName",
    },
  ],
}
`;

exports[`vcs-watcher #handleFileChange should add changes when a file is renamed and added file is tracked 1`] = `
{
  "commits": [],
  "workingDirectory": [
    {
      "afterPath": "new_foo.js",
      "changeType": "ADDED",
      "currentBlobName": "bufferBlobName",
      "indexedBlobName": "blobName",
    },
    {
      "beforePath": "old_foo.js",
      "changeType": "DELETED",
      "headBlobName": "blobName",
    },
  ],
}
`;

exports[`vcs-watcher #handleFileChange should add changes when a file is renamed and then a file is added using the old path name 1`] = `
{
  "commits": [],
  "workingDirectory": [
    {
      "afterPath": "new_foo.js",
      "beforePath": "old_foo.js",
      "changeType": "RENAMED",
      "currentBlobName": "bufferBlobName",
      "headBlobName": "blobName",
      "indexedBlobName": "blobName",
    },
    {
      "afterPath": "old_foo.js",
      "changeType": "ADDED",
      "currentBlobName": "bufferBlobName",
      "indexedBlobName": "blobName",
    },
  ],
}
`;

exports[`vcs-watcher #handleFileChange should handle buffer change and then deleted 1`] = `
{
  "commits": [],
  "workingDirectory": [
    {
      "beforePath": "foo.js",
      "changeType": "DELETED",
      "headBlobName": "blobName",
    },
  ],
}
`;
