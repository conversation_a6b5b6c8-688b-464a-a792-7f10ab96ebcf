// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`git-diff-parser parse should parse git diff with Added, Deleted, Modified, and Renamed 1`] = `
[
  {
    "afterPath": "foo.js",
    "beforePath": "foo.js",
    "changeType": "MODIFIED",
  },
  {
    "afterPath": "foo bar.txt",
    "beforePath": undefined,
    "changeType": "ADDED",
  },
  {
    "afterPath": undefined,
    "beforePath": "src/old.js",
    "changeType": "DELETED",
  },
  {
    "afterPath": "src/index.js",
    "beforePath": "src/main.js",
    "changeType": "RENAMED",
  },
]
`;
