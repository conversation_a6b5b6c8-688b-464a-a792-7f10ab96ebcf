import path from "path";
import * as vscode from "vscode";

import {
    addTabGroup,
    addTextDocument,
    MutableTextDocument,
    Tab,
    TabGroup,
    TabInputText,
} from "../../../__mocks__/vscode-mocks";
import { FileChangeDetails } from "../placeholders";
import { FileChangeWatcherImpl } from "../placeholders/folder-file-change-watcher";

describe("file-change-watcher", () => {
    test("finds dirty documents by relative path", async () => {
        const emitter = new vscode.EventEmitter<FileChangeDetails>();

        // create dirty documents in the project
        const doc1 = new MutableTextDocument(
            vscode.Uri.file(path.join(__dirname, "test1.py")),
            "test1 file"
        );
        doc1.markDirty();
        const tabGroup = new TabGroup();
        const tab = new Tab();
        tab.document = doc1;
        tabGroup.tabs.push(tab);
        tab.input = new TabInputText(doc1.uri);

        addTextDocument(doc1);

        addTabGroup(tabGroup);

        const fileChangeWatcher = new FileChangeWatcherImpl(
            vscode.Uri.file(__dirname),
            emitter.event
        );
        const dirtyPaths = await fileChangeWatcher.getPathsWithBufferChanges();
        expect(dirtyPaths).toEqual(["test1.py"]);
    });
});
