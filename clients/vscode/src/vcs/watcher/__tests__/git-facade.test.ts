import { MockGitAdapter } from "../../../__mocks__/vcs/mock-git-adapter";
import { GitFacade } from "../git-facade";

describe("git facade", () => {
    describe("#getCommitChanges", () => {
        it("should return a list of files changed on working directory", async () => {
            const git = new MockGitAdapter()
                .withSymbolicRef("main")
                .withLog(["12345", "67890"].join("\n"))
                .withShow(
                    "12345",
                    [
                        ["M", "foo.js"],
                        ["A", "foo bar.txt"],
                        ["D", "src/old.js"],
                        ["R100", "src/main.js", "src/index.js"],
                    ]
                        .map((line) => line.join("\t"))
                        .join("\n")
                )
                .withShow("67890", [["M", "bar.txt"]].map((line) => line.join("\t")).join("\n"));

            const gitFacade = new GitFacade(git);
            const changes = await gitFacade.getCommitChanges();
            expect(changes).toMatchSnapshot();
        });
    });

    describe("#getWorkingDirectoryChange", () => {
        it("should return a list of files changed on working directory", async () => {
            const git = new MockGitAdapter()
                .withDiff(
                    [
                        ["M", "modified file.js"],
                        ["A", "added file.txt"],
                        ["D", "src/deleted_file.js"],
                        ["R100", "src/old_rename.js", "src/new_rename.js"],
                    ]
                        .map((line) => line.join("\t"))
                        .join("\n")
                )
                .withLsFiles(
                    [
                        "untracked file1.js",
                        "untracked_file2.txt",
                        "src/untracked3.js",
                        "src/untracked file4.js",
                    ].join("\n")
                );

            const gitFacade = new GitFacade(git as any);
            const changes = await gitFacade.getWorkingDirectoryChanges();
            expect(changes).toMatchSnapshot();
        });
    });
});
