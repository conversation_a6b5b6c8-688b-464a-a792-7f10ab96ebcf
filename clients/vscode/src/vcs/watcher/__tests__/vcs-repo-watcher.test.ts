import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import * as vscode from "vscode";

import { mockFSUtils } from "../../../__mocks__/fs-utils";
import { AugmentConfigListener } from "../../../augment-config-listener";
import { FileChangeOrigin } from "../../../workspace/workspace-manager";
import * as enums from "../../enums";
import { GitAdapterImpl } from "../../git-adapter";
import { VCSDetails } from "../../vcs-finder";
import * as mocks from "../__mocks__";
import { CommandExecutorMock } from "../__mocks__/command-executor-mock";
import { GitFacade } from "../git-facade";
import { HeadChangeWatcher } from "../head-change-watcher";
import { VCSRepoWatcherImpl } from "../vcs-repo-watcher";

const commit1Changes = {
    hash: "12345",
    output: [
        ["9fc5ff0 (12345 -> my-branch) committing 12345 changes"],
        ["M", "foo.js"],
        ["A", "foo bar.txt"],
        ["D", "src/old.js"],
        ["R100", "src/main.js", "src/index.js"],
        [],
    ],
};

const commit2Changes = {
    hash: "67890",
    output: [["9fc5ff1 (67890 -> my-branch) committing 67890 changes"], ["M", "bar.txt"], []],
};

const commitChangesCommandsMock = [
    {
        command: "git symbolic-ref refs/remotes/origin/HEAD",
        stdout: "refs/remotes/origin/main",
    },
    {
        command: 'git log main HEAD --no-merges --format="%H" --not "main"',
        stdout: "12345\n67890",
    },
    {
        command: 'git show --name-status --oneline "12345"',
        stdout: commit1Changes.output.map((line) => line.join("\t")).join("\n"),
    },
    {
        command: 'git show --name-status --oneline "67890"',
        stdout: commit2Changes.output.map((line) => line.join("\t")).join("\n"),
    },
    {
        command: 'git show "12345:foo.js"',
        stdout: "foo after commit rules!",
    },
    {
        command: 'git show "12345^:foo.js"',
        stdout: "foo before commit rules!",
    },
    {
        command: 'git show "12345:foo bar.txt"',
        stdout: "foo bar rules!",
    },
    {
        command: 'git show "12345^:src/old.js"',
        stdout: "src old rules!",
    },
    {
        command: 'git show "12345:src/index.js"',
        stdout: "src index rules!",
    },
    {
        command: 'git show "12345^:src/main.js"',
        stdout: "src main rules!",
    },
    {
        command: 'git show "67890:bar.txt"',
        stdout: "bar rules new content!!",
    },
    {
        command: 'git show "67890^:bar.txt"',
        stdout: "bar rules old content?!",
    },
];

const workingDirectoryChangesCommandsMock = [
    {
        command: "git diff --name-status HEAD",
        stdout: [
            ["M", "foo.js"],
            ["A", "foo bar.txt"],
            ["D", "src/old.js"],
            ["R100", "src/main.js", "src/index.js"],
            [],
        ]
            .map((line) => line.join("\t"))
            .join("\n"),
    },
    {
        command: "git ls-files --others --exclude-standard",
        stdout: [
            "untracked file1.js",
            "untracked_file2.txt",
            "src/untracked3.js",
            "src/untracked file4.js",
            "", // command output ends on a newline
        ].join("\n"),
    },
    {
        command: 'git show "HEAD:foo.js"',
        stdout: "HEAD:foo rules!",
    },
    {
        command: 'git show "HEAD:src/old.js"',
        stdout: "HEAD:src_old rules!",
    },
    {
        command: 'git show "HEAD:src/main.js"',
        stdout: "HEAD:src_main rules!",
    },
];

const bufferChangesCommandsMock = [
    {
        command: 'git show "HEAD:foo.js"',
        stdout: "HEAD:foo rules!",
    },
    {
        command: 'git show "HEAD:src/old.js"',
        stdout: "HEAD:src_old rules!",
    },
];

describe("vcs-watcher", () => {
    let vcsDetails: VCSDetails;
    let vcsRepoWatcher: VCSRepoWatcherImpl;
    let fileUtils: mocks.FileUtilsMock;
    let fileUploader: mocks.FileUploaderMock;
    let headChangeWatcher;
    let blobNameRetriever;
    let fileChangeWatcher: mocks.FileChangeWatcherMock;
    let commandExecutor: CommandExecutorMock;
    let configListener: AugmentConfigListener;

    function writeToVCSHead(vcsParentDir: vscode.Uri, content: string) {
        const makeDirs = true;
        const headFileLocation = joinPath(vcsParentDir.fsPath, ".git/logs/HEAD");

        mockFSUtils.writeFileUtf8(headFileLocation, content, makeDirs);
    }

    beforeEach(async () => {
        jest.useFakeTimers();
        mockFSUtils.reset();

        const vcsRootDirectory = vscode.Uri.file("/src");
        writeToVCSHead(vcsRootDirectory, "initial content");
        vcsDetails = {
            root: vcsRootDirectory,
            toolName: enums.VCSToolName.git,
        };

        commandExecutor = new CommandExecutorMock();
        const vcsFacade = new GitFacade(new GitAdapterImpl(vcsDetails.root, commandExecutor.spy));
        fileUtils = new mocks.FileUtilsMock();
        fileUploader = new mocks.FileUploaderMock();
        headChangeWatcher = new HeadChangeWatcher("folderName", 0, vcsDetails);
        headChangeWatcher.listenForChanges();

        blobNameRetriever = new mocks.BlobNameRetrieverMock();
        fileChangeWatcher = new mocks.FileChangeWatcherMock();

        configListener = new AugmentConfigListener();

        vcsRepoWatcher = new VCSRepoWatcherImpl(
            "folderName",
            vcsFacade,
            fileUtils,
            fileUploader,
            fileChangeWatcher,
            blobNameRetriever,
            configListener
        );
    });

    function assertAllCommandsWereUsedInOrder(commands: { command: string; stdout: string }[]) {
        // verify commands are mocked properly.
        // 1. Keeps mocked commands in order and clean
        // 2. Verifying all commands were executed helps to ensure the output we're seeing is not accidental.
        for (let i = 0; i < commandExecutor.spy.mock.calls.length; i++) {
            const [commandExecuted] = commandExecutor.spy.mock.calls[i];
            const commandMocked = commands[i].command;
            expect(commandExecuted).toEqual(commandMocked);
        }
    }

    afterEach(() => {
        vcsRepoWatcher.dispose();
        mockFSUtils.reset();
        jest.useRealTimers();
    });

    describe("#collectCommitsChanges", () => {
        it("should collect changes", async () => {
            for (const command of commitChangesCommandsMock) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            const commitChanges = await vcsRepoWatcher.collectCommitChanges();
            expect(commitChanges).toMatchSnapshot();
            expect(commitChanges.length).toBe(2);
            expect(commitChanges[0].files.length).toBe(commit1Changes.output.length - 2); // -2 because of the commit hash and the empty line
            expect(commitChanges[1].files.length).toBe(commit2Changes.output.length - 2);
            assertAllCommandsWereUsedInOrder(commitChangesCommandsMock);
        });
    });

    describe("#collectWorkingDirectoryChanges", () => {
        it("should collect changes", async () => {
            for (const command of workingDirectoryChangesCommandsMock) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            const workingDirectoryChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();
            expect(workingDirectoryChanges).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(workingDirectoryChangesCommandsMock);
        });
    });

    describe("#collectBufferChanges", () => {
        it("should collect changes", async () => {
            for (const command of bufferChangesCommandsMock) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            fileChangeWatcher.withPathsWithBufferChanges(["foo.js", "src/old.js"]);
            const bufferChanges = await vcsRepoWatcher.collectBufferChanges();
            expect(bufferChanges).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(bufferChangesCommandsMock);
        });
    });

    describe("isUploaded filtering", () => {
        // tests to make sure we ignore files correctly.

        it("should ignore changes to ignored files based on 'before' path", async () => {
            const commands = [
                {
                    command: "git diff --name-status HEAD",
                    stdout: [["D", "src/old.js"], []].map((line) => line.join("\t")).join("\n"),
                },
                {
                    command: "git ls-files --others --exclude-standard",
                    stdout: [
                        "", // command output ends on a newline
                    ].join("\n"),
                },
                {
                    command: 'git show "HEAD:src/old.js"',
                    stdout: "HEAD:src_old rules!",
                },
            ];

            const srcOld = "src/old.js";
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            const workingDirectoryChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();
            const oldFile = workingDirectoryChanges.find((change) => change.beforePath === srcOld);
            expect(oldFile).not.toBeUndefined();

            fileUtils.withIgnoredFiles([srcOld]);
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            const workingDirectoryChangesWithIgnoredFiles =
                await vcsRepoWatcher.collectWorkingDirectoryChanges();
            const oldFileShouldBeIgnored = workingDirectoryChangesWithIgnoredFiles.find(
                (change) => change.beforePath === srcOld
            );
            expect(oldFileShouldBeIgnored).toBeUndefined();
        });

        it("should ignore changes to ignored files based on 'after' path", async () => {
            const commands = [
                {
                    command: "git diff --name-status HEAD",
                    stdout: [["A", "src/new.js"], []].map((line) => line.join("\t")).join("\n"),
                },
                {
                    command: "git ls-files --others --exclude-standard",
                    stdout: [
                        "", // command output ends on a newline
                    ].join("\n"),
                },
            ];

            const srcNew = "src/new.js";
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            const workingDirectoryChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();
            const newFile = workingDirectoryChanges.find((change) => change.afterPath === srcNew);
            expect(newFile).not.toBeUndefined();

            fileUtils.withIgnoredFiles([srcNew]);
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            const workingDirectoryChangesWithIgnoredFiles =
                await vcsRepoWatcher.collectWorkingDirectoryChanges();
            const newFileShouldBeIgnored = workingDirectoryChangesWithIgnoredFiles.find(
                (change) => change.afterPath === srcNew
            );
            expect(newFileShouldBeIgnored).toBeUndefined();
        });
    });
    describe("#handleFileChange", () => {
        it("should add a 'MODIFIED' change on buffer change", async () => {
            const commands = [
                {
                    command: 'git show "HEAD:foo.js"',
                    stdout: "HEAD:foo rules!",
                },
            ];
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            await vcsRepoWatcher.handleFileChange({
                folderId: 1,
                relPath: "foo.js",
                origin: FileChangeOrigin.buffer,
            });

            expect(await vcsRepoWatcher.getChanges()).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(commands);
        });

        it("should add a 'ADDED' change on disk change if added file not tracked", async () => {
            const commands = [
                {
                    command: "git diff --name-status HEAD -- foo.js",
                    stdout: "\n",
                },
                {
                    command: "git ls-files --others --exclude-standard -- foo.js",
                    stdout: "foo.js\n",
                },
            ];
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            await vcsRepoWatcher.handleFileChange({
                folderId: 1,
                relPath: "foo.js",
                origin: FileChangeOrigin.disk,
            });

            expect(await vcsRepoWatcher.getChanges()).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(commands);
        });

        it("should add a 'DELETED' change on disk change", async () => {
            const commands = [
                {
                    command: "git diff --name-status HEAD -- foo.js",
                    stdout: "D\tfoo.js",
                },
                {
                    command: 'git show "HEAD:foo.js"',
                    stdout: "HEAD:foo rules!",
                },
            ];
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            await vcsRepoWatcher.handleFileChange({
                folderId: 1,
                relPath: "foo.js",
                origin: FileChangeOrigin.disk,
            });

            expect(await vcsRepoWatcher.getChanges()).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(commands);
        });

        it("should add changes when a file is renamed and added file is tracked", async () => {
            // NOTE: on a rename, we will get 2 events for the new name, and the old name
            // Also, git will not know to tell us "renamed" since we're querying specific files.
            // instead it will tell us "added" (if added file is tracked) and "deleted"
            // if added file is not tracked, we will still be able to identify it was added.
            const commands = [
                {
                    command: "git diff --name-status HEAD -- new_foo.js",
                    stdout: "A\tnew_foo.js",
                },
                {
                    command: "git diff --name-status HEAD -- old_foo.js",
                    stdout: "D\told_foo.js",
                },
                {
                    command: 'git show "HEAD:old_foo.js"',
                    stdout: "HEAD:old_foo rules!",
                },
            ];
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }

            await vcsRepoWatcher.handleFileChange({
                folderId: 1,
                relPath: "new_foo.js",
                origin: FileChangeOrigin.disk,
            });
            await vcsRepoWatcher.handleFileChange({
                // We will also get an event on the old name
                folderId: 1,
                relPath: "old_foo.js",
                origin: FileChangeOrigin.disk,
            });

            expect(await vcsRepoWatcher.getChanges()).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(commands);
        });

        it("should handle buffer change and then deleted", async () => {
            // NOTE: on a rename, we will get 2 events for the new name, and the old name
            // Also, git will not know to tell us "renamed" since we're querying specific files.
            // instead it will tell us "added" (if added file is tracked) and "deleted"
            // if added file is not tracked, we will still be able to identify it was added.
            const commands = [
                {
                    command: 'git show "HEAD:foo.js"',
                    stdout: "HEAD:foo rules!",
                },
                {
                    command: "git diff --name-status HEAD -- foo.js",
                    stdout: "D\tfoo.js",
                },
                {
                    command: 'git show "HEAD:foo.js"',
                    stdout: "HEAD:foo rules!",
                },
            ];
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }

            await vcsRepoWatcher.handleFileChange({
                folderId: 1,
                relPath: "foo.js",
                origin: FileChangeOrigin.buffer,
            });
            await vcsRepoWatcher.handleFileChange({
                // We will also get an event on the old name
                folderId: 1,
                relPath: "foo.js",
                origin: FileChangeOrigin.disk,
            });

            expect(await vcsRepoWatcher.getChanges()).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(commands);
        });

        it("should handle a file that was deleted but then added back (resulting in no change)", async () => {
            const commands = [
                {
                    command: "git diff --name-status HEAD -- foo.js",
                    stdout: "D\tfoo.js",
                },
                {
                    command: 'git show "HEAD:foo.js"',
                    stdout: "HEAD:foo rules!",
                },
                {
                    command: "git diff --name-status HEAD -- foo.js",
                    stdout: "\n", // no change occurred
                },
                {
                    command: "git ls-files --others --exclude-standard -- foo.js",
                    stdout: "\n",
                },
            ];
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }

            await vcsRepoWatcher.handleFileChange({
                folderId: 1,
                relPath: "foo.js",
                origin: FileChangeOrigin.disk,
            });
            jest.runOnlyPendingTimersAsync();
            await vcsRepoWatcher.handleFileChange({
                // We will also get an event on the old name
                folderId: 1,
                relPath: "foo.js",
                origin: FileChangeOrigin.disk,
            });

            const changes = await vcsRepoWatcher.getChanges();
            expect(changes.workingDirectory.length).toBe(0); // no changes expected
            assertAllCommandsWereUsedInOrder(commands);
        });

        /**
         * Test the following scenario
         *
         *  - file was renamed from foo to bar - and vcs watcher collected a "rename"
         *  - a new file was added with the name "foo"
         *
         * The purpose of the test is to guarantee we collect both unrelated events even if they have the same path name.
         *
         */
        it("should add changes when a file is renamed and then a file is added using the old path name", async () => {
            // NOTE: on a rename today - we will NOT get a "rename" from git currently because we handle one file at a time.
            // However, for the intent of this test we want to simulate a rename existed.
            const commands = [
                {
                    command: "git diff --name-status HEAD -- new_foo.js",
                    stdout: "R100\told_foo.js\tnew_foo.js",
                },
                {
                    command: 'git show "HEAD:old_foo.js"',
                    stdout: "HEAD:old_foo rules!",
                },
                // now commands that run when the file is added. we will assume the new file is not tracked yet by vcs.
                {
                    command: "git diff --name-status HEAD -- old_foo.js",
                    stdout: "\n",
                },
                {
                    command: "git ls-files --others --exclude-standard -- old_foo.js",
                    stdout: "old_foo.js\n",
                },
            ];
            for (const command of commands) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }

            await vcsRepoWatcher.handleFileChange({
                // this will trigger the rename
                folderId: 1,
                relPath: "new_foo.js",
                origin: FileChangeOrigin.disk,
            });
            await vcsRepoWatcher.handleFileChange({
                // this should add an event for added file
                // We will also get an event on the old name
                folderId: 1,
                relPath: "old_foo.js",
                origin: FileChangeOrigin.disk,
            });

            expect(await vcsRepoWatcher.getChanges()).toMatchSnapshot();
            assertAllCommandsWereUsedInOrder(commands);
        });
    });

    describe("#handleUnknownBlobs", () => {
        it("should upload unknown blobs", async () => {
            for (const command of workingDirectoryChangesCommandsMock) {
                commandExecutor.withCommandStdout(command.command, command.stdout);
            }
            const workingDirectoryChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();

            expect(workingDirectoryChanges[0].headBlobName).not.toBeUndefined();

            const uploadSpy = jest.spyOn(fileUploader, "upload");
            const handleUnknownBlobsCommands = {
                command: 'git show "HEAD:src/main.js"',
                stdout: "HEAD:src_main rules!",
            };

            commandExecutor.withCommandStdout(
                handleUnknownBlobsCommands.command,
                handleUnknownBlobsCommands.stdout
            );
            const callsCount = uploadSpy.mock.calls.length;
            vcsRepoWatcher.handleUnknownBlobs([
                workingDirectoryChanges[0].headBlobName!,
                "fake blob name",
            ]);
            await jest.runAllTimersAsync();
            expect(uploadSpy.mock.calls.length).toBe(callsCount + 1);
            expect(uploadSpy.mock.lastCall).toEqual(["src/main.js", "HEAD:src_main rules!"]);

            assertAllCommandsWereUsedInOrder([
                ...workingDirectoryChangesCommandsMock,
                handleUnknownBlobsCommands,
            ]);
        });
    });
});
