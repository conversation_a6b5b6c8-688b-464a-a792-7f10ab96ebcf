import { executeCommand } from "../command-utils";

describe("command-utils", () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("executeCommand", async () => {
        const result = await executeCommand("echo hello");
        expect(result).toEqual("hello\n");
    });

    test("executeCommand with error", async () => {
        const result = await executeCommand("echo hello && exit 1");
        expect(result).toEqual(undefined);
    });

    test("executeCommand with timeout", async () => {
        jest.setTimeout(5000); // if the command does not terminate due to timeout, the test will fail on timeout
        const commandResult = await executeCommand("sleep 10", { timeout: 1 });
        expect(commandResult).toBeUndefined();
    });
});
