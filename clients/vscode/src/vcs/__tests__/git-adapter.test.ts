import { Uri } from "../../__mocks__/vscode-mocks";
import { GitAdapterImpl } from "../git-adapter";

describe("git adapters", () => {
    const CWD = "/";
    let gitAdapter: GitAdapterImpl;
    let executeCommandMock: jest.Mock<any, any>;
    beforeEach(() => {
        executeCommandMock = jest.fn().mockResolvedValue("");
        gitAdapter = new GitAdapterImpl(Uri.file(CWD), executeCommandMock);
    });

    test("#version", async () => {
        await gitAdapter.version();
        expect(executeCommandMock).toHaveBeenCalledWith("git --version");
    });

    test("#diff", async () => {
        await gitAdapter.diff({ hash1: "HEAD", nameStatus: true });
        expect(executeCommandMock).toHaveBeenCalledWith("git diff --name-status HEAD", {
            cwd: CWD,
        });

        await gitAdapter.diff({});
        expect(executeCommandMock).toHaveBeenCalledWith("git diff", {
            cwd: CWD,
        });

        await gitAdapter.diff({ hash1: "HEAD", relPath: "foo.js" });
        expect(executeCommandMock).toHaveBeenCalledWith("git diff HEAD -- foo.js", {
            cwd: CWD,
        });
    });

    test("#lsFiles", async () => {
        await gitAdapter.lsFiles({ others: true, excludeStandard: true });
        expect(executeCommandMock).toHaveBeenCalledWith(
            "git ls-files --others --exclude-standard",
            { cwd: CWD }
        );

        await gitAdapter.lsFiles({});
        expect(executeCommandMock).toHaveBeenCalledWith("git ls-files", { cwd: CWD });

        await gitAdapter.lsFiles({ others: true, relPath: "foo.js" });
        expect(executeCommandMock).toHaveBeenCalledWith("git ls-files --others -- foo.js", {
            cwd: CWD,
        });
    });

    test("#show", async () => {
        await gitAdapter.show({ object: "HEAD", nameStatus: true, oneLine: true });
        expect(executeCommandMock).toHaveBeenCalledWith('git show --name-status --oneline "HEAD"', {
            cwd: CWD,
        });
    });

    test("#symbolicRef", async () => {
        await gitAdapter.symbolicRef({ name: "HEAD" });
        expect(executeCommandMock).toHaveBeenCalledWith("git symbolic-ref HEAD", {
            cwd: CWD,
        });
    });

    test("#log", async () => {
        await gitAdapter.log({
            commit1: "HEAD",
            commit2: "HEAD~1",
            noMerges: true,
            format: "%H",
            not: "HEAD",
        });
        expect(executeCommandMock).toHaveBeenCalledWith(
            'git log HEAD HEAD~1 --no-merges --format="%H" --not "HEAD"',
            { cwd: CWD }
        );
    });
});
