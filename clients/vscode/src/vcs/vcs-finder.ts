import * as vscode from "vscode";

import { findFileName } from "../utils/path-utils";
import { FileType } from "../utils/types";
import { VCSToolName } from "./enums";

type VCSRootDetails = {
    name: string; // folder/file name to identify the root
    type: FileType; // file or folder
};

type VCSDetails = {
    root: vscode.Uri;
    toolName: VCSToolName;
};

const TOOL_NAME_TO_DETAILS: Record<VCSToolName, VCSRootDetails> = Object.freeze({
    git: {
        name: ".git",
        type: FileType.directory,
    },
});

async function findVCS(startDir: string): Promise<VCSDetails | undefined> {
    for (const [toolName, details] of Object.entries(TOOL_NAME_TO_DETAILS)) {
        const root = await findFileName(startDir, details.name, details.type);
        if (root !== undefined) {
            return {
                root: root,
                toolName: toolName as VCSToolName,
            };
        }
    }
}

export { type VCSDetails, findVCS };
