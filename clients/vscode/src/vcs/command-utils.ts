import { exec, ExecOptions } from "child_process";
import { promisify } from "util";

import { type AugmentLogger, getLogger } from "../logging";

const logger: AugmentLogger = getLogger("CommandUtils");
const execPromise = promisify(exec);

const DEFAULT_TIMEOUT_MS = 5000;

async function executeCommand(
    command: string,
    options: ExecOptions = {}
): Promise<string | undefined> {
    try {
        const { stdout, stderr } = await execPromise(command, {
            timeout: DEFAULT_TIMEOUT_MS,
            ...options,
        });
        if (stderr) {
            logger.debug("stderr:" + stderr?.toString());
        }
        return stdout?.toString();
    } catch (error: any) {
        logger.debug(
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            `exec error [${error.code}] [${error.signal}] [${error.message}] [${error.stackTrace}]`
        );
    }
}

export { executeCommand };
