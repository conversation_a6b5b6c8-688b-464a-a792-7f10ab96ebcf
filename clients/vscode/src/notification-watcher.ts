import {
    ANALYTICS_EVENTS,
    trackEventWithTypes,
} from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import * as vscode from "vscode";

import { APIServer } from "./augment-api";
import { AugmentConfigListener } from "./augment-config-listener";
import { FeatureFlagManager } from "./feature-flags";
import { type AugmentLogger, getLogger } from "./logging";
import { DisposableService } from "./utils/disposable-service";
import { hasProperty } from "./utils/types-utils";
import { WebviewManager } from "./webview-providers/webview-manager";
import { WebViewMessageType } from "./webview-providers/webview-messages";

/**
 * Notification level
 * Maps to the NotificationLevel enum in public_api.proto
 */
export enum NotificationLevel {
    unspecified = "UNSPECIFIED",
    info = "INFO",
    warning = "WARNING",
    error = "ERROR",
}

/**
 * Notification display type
 */
export enum NotificationDisplayType {
    unspecified = "UNSPECIFIED",
    toast = "TOAST",
    banner = "BANNER",
}

/**
 * Action item for a notification
 * Maps to the ActionItem message in public_api.proto
 */
export interface NotificationActionItem {
    title: string;
    url?: string;
}

/**
 * Notification data structure
 */
export interface Notification {
    notificationId: string;
    level: NotificationLevel;
    message: string;
    actionItems?: NotificationActionItem[];
    displayType?: NotificationDisplayType;
}

/**
 * Response from the get-notifications endpoint
 */
export interface GetNotificationsResponse {
    notifications: Notification[];
}

/**
 * NotificationWatcher polls for notifications and displays them to the user
 */
export class NotificationWatcher extends DisposableService {
    private _pollingTimer?: NodeJS.Timeout;
    private readonly _logger: AugmentLogger;
    private _isPolling = false;
    private _activeNotifications = new Map<string, Notification>();
    private _displayedNotifications = new Set<string>();

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _configListener: AugmentConfigListener,
        private readonly _featureFlagManager: FeatureFlagManager
    ) {
        super();
        this._logger = getLogger("NotificationWatcher");

        // Subscribe to feature flag changes to update polling interval
        this.addDisposable(
            this._featureFlagManager.subscribe(
                ["notificationPollingIntervalMs"],
                this._updatePollingInterval.bind(this)
            )
        );

        // Start polling
        this._startPolling();
    }

    /**
     * Start polling for notifications
     */
    private _startPolling(): void {
        if (this._isPolling) {
            return;
        }

        // Get the polling interval from feature flags
        const pollingInterval: number =
            this._featureFlagManager.currentFlags.notificationPollingIntervalMs;
        if (pollingInterval <= 0) {
            this._logger.info("Notification polling is disabled");
            return;
        }

        this._logger.info(`Starting notification polling with interval ${pollingInterval}ms`);
        this._isPolling = true;

        // Poll immediately on start
        void this._pollForNotifications();

        // Set up the polling timer
        this._pollingTimer = setInterval(() => {
            void this._pollForNotifications();
        }, pollingInterval);
    }

    /**
     * Stop polling for notifications
     */
    private _stopPolling(): void {
        if (this._pollingTimer) {
            clearInterval(this._pollingTimer);
            this._pollingTimer = undefined;
        }
        this._isPolling = false;
    }

    /**
     * Update polling interval when configuration changes
     */
    private _updatePollingInterval(): void {
        this._stopPolling();
        this._startPolling();
    }

    /**
     * Poll for notifications
     */
    private async _pollForNotifications(): Promise<void> {
        try {
            this._logger.debug("Polling for notifications");

            // Call the API to read notifications
            const response = await this._apiServer.readNotifications();

            // Process the notifications
            if (hasProperty(response, "notifications") && Array.isArray(response.notifications)) {
                const serverNotifications = response.notifications as unknown as Notification[];

                if (serverNotifications.length > 0) {
                    this._logger.debug(`Received ${serverNotifications.length} notifications`);
                    for (const notification of serverNotifications) {
                        this._activeNotifications.set(notification.notificationId, notification);
                    }
                }

                // Clean up displayed notifications that are no longer on the server (were marked as read elsewhere)
                if (this._displayedNotifications.size > 0) {
                    const serverNotificationIds = new Set(
                        serverNotifications.map((n) => n.notificationId)
                    );
                    const toRemove: string[] = [];

                    for (const displayedNotificationId of this._displayedNotifications) {
                        if (!serverNotificationIds.has(displayedNotificationId)) {
                            toRemove.push(displayedNotificationId);
                        }
                    }

                    if (toRemove.length > 0) {
                        this._logger.debug(
                            `Cleaning up ${toRemove.length} displayed notifications no longer on server`
                        );
                        const webviewManager = WebviewManager.getInstance();
                        for (const notificationId of toRemove) {
                            this._activeNotifications.delete(notificationId);
                            this._displayedNotifications.delete(notificationId);

                            // Also notify webviews to remove any visible banner for this notification
                            webviewManager.broadcastMessage({
                                type: WebViewMessageType.dismissBannerNotification,
                                data: { notificationId },
                            });
                        }
                    }
                }

                // Show any new notifications that haven't been displayed yet
                this._showPendingNotifications();
            } else {
                this._logger.debug("Invalid notification response format");
            }
        } catch (error: unknown) {
            // Don't log errors at error level as this is a background process
            // and we don't want to spam the logs if the API is temporarily unavailable
            this._logger.debug(`Error polling for notifications: ${String(error)}`);
        }
    }

    /**
     * Show notifications that haven't been shown yet
     */
    private _showPendingNotifications(): void {
        for (const [notificationId, notification] of this._activeNotifications) {
            if (!this._displayedNotifications.has(notificationId)) {
                this._showNotification(notification);
                this._displayedNotifications.add(notificationId);
            }
        }
    }

    /**
     * Show a notification to the user
     */
    private _showNotification(notification: Notification): void {
        if (!notification.message) {
            this._logger.debug("Received notification with no message, skipping");
            return;
        }

        this._logger.debug(`Showing notification: ${notification.message}`);

        // Show either popup or banner notification, but not both
        if (notification.displayType === NotificationDisplayType.toast) {
            this._showToastNotification(notification);
        } else if (
            notification.displayType === NotificationDisplayType.banner ||
            notification.displayType === NotificationDisplayType.unspecified
        ) {
            this._showBannerNotification(notification);
        }
    }

    /**
     * Show a toast notification using VSCode's built-in notification system
     */
    private _showToastNotification(notification: Notification): void {
        trackEventWithTypes(ANALYTICS_EVENTS.NOTIFICATION_DISPLAYED, {
            notificationId: notification.notificationId,
            notificationLevel: notification.level ?? NotificationLevel.info,
            notificationType: "toast",
        });

        // Get notification level
        const level = notification.level ?? NotificationLevel.info;

        // Create action items if any
        const actions: vscode.MessageItem[] = [];
        if (notification.actionItems && notification.actionItems.length > 0) {
            for (const actionItem of notification.actionItems) {
                if (actionItem.title) {
                    actions.push({
                        title: actionItem.title,
                    });
                }
            }
        } else {
            actions.push({
                title: "Close",
            });
        }

        // Show the notification based on level
        let showMessagePromise: Thenable<vscode.MessageItem | undefined>;
        if (level === NotificationLevel.error) {
            showMessagePromise = vscode.window.showErrorMessage(notification.message, ...actions);
        } else if (level === NotificationLevel.warning) {
            showMessagePromise = vscode.window.showWarningMessage(notification.message, ...actions);
        } else {
            // Default to info level for unspecified or info
            showMessagePromise = vscode.window.showInformationMessage(
                notification.message,
                ...actions
            );
        }

        void showMessagePromise.then((selectedItem: vscode.MessageItem | undefined) => {
            let actionItemTitle: string | undefined;
            if (selectedItem) {
                // Find the selected action item
                const selectedTitle = selectedItem.title || "";
                const actionItem = notification.actionItems?.find(
                    (item) => item.title === selectedTitle
                );
                if (actionItem) {
                    actionItemTitle = actionItem.title;
                    this._logger.info(`Notification action ${selectedTitle} selected`);
                }

                if (actionItem && actionItem.url) {
                    this._logger.info(`Opening URL from notification: ${actionItem.url}`);
                    void vscode.env.openExternal(vscode.Uri.parse(actionItem.url));
                }
                trackEventWithTypes(ANALYTICS_EVENTS.NOTIFICATION_DISMISSED, {
                    notificationId: notification.notificationId,
                    notificationLevel: notification.level ?? NotificationLevel.info,
                    notificationType: "toast",
                    actionItemTitle: actionItemTitle,
                });

                void this._apiServer
                    .markNotificationAsRead(notification.notificationId, actionItemTitle)
                    .then(() => {
                        // Remove from local state so it won't be shown again
                        this._activeNotifications.delete(notification.notificationId);
                        this._displayedNotifications.delete(notification.notificationId);
                    })
                    .catch((error) => {
                        this._logger.warn(`Failed to mark notification as read: ${String(error)}`);
                    });
            }
        });
    }

    /**
     * Show a banner notification by sending a message to webviews
     */
    private _showBannerNotification(notification: Notification): void {
        trackEventWithTypes(ANALYTICS_EVENTS.NOTIFICATION_DISPLAYED, {
            notificationId: notification.notificationId,
            notificationLevel: notification.level ?? NotificationLevel.info,
            notificationType: "banner",
        });

        const webviewManager = WebviewManager.getInstance();

        // Convert notification level to string format expected by webview
        let levelString: "info" | "warning" | "error";
        switch (notification.level) {
            case NotificationLevel.error:
                levelString = "error";
                break;
            case NotificationLevel.warning:
                levelString = "warning";
                break;
            default:
                levelString = "info";
                break;
        }

        // Convert action items to banner format
        const bannerActionItems = notification.actionItems?.map((item) => ({
            title: item.title,
            url: item.url,
        }));

        // Send banner notification message to all webviews
        webviewManager.broadcastMessage({
            type: WebViewMessageType.showBannerNotification,
            data: {
                notificationId: notification.notificationId,
                message: notification.message,
                level: levelString,
                actionItems: bannerActionItems,
            },
        });
    }

    /**
     * Manually trigger a poll for notifications
     * Useful for testing or immediate refresh
     */
    public async refreshNotifications(): Promise<void> {
        await this._pollForNotifications();
    }

    /**
     * Handle banner notification dismissal from webview
     */
    public async dismissBannerNotification(
        notificationId: string,
        actionItemTitle?: string
    ): Promise<void> {
        try {
            // Get the notification details for tracking before we delete it
            const notification = this._activeNotifications.get(notificationId);
            trackEventWithTypes(ANALYTICS_EVENTS.NOTIFICATION_DISMISSED, {
                notificationId: notificationId,
                notificationLevel: notification?.level ?? NotificationLevel.info,
                notificationType: "banner",
                actionItemTitle: actionItemTitle,
            });

            await this._apiServer.markNotificationAsRead(notificationId, actionItemTitle);
            // Remove from local state so it won't be shown again
            this._activeNotifications.delete(notificationId);
            this._displayedNotifications.delete(notificationId);

            // Note: We don't need to send removal message to webviews here because
            // the banner component already removes itself from the store when dismissed
        } catch (error) {
            this._logger.warn(`Failed to mark notification as read: ${String(error)}`);
        }
    }

    /**
     * Get the current count of active notifications
     * Useful for testing
     */
    public getActiveNotificationCount(): number {
        return this._activeNotifications.size;
    }

    /**
     * Dispose of resources
     */
    public override dispose(): void {
        this._stopPolling();
        this._activeNotifications.clear();
        this._displayedNotifications.clear();
        super.dispose();
    }
}
