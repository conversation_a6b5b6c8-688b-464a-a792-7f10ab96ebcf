import { IClientAuth } from "@augment-internal/sidecar-libs/src/client-interfaces/client-auth";

import { AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";

export class ClientAuth implements IClientAuth {
    constructor(
        private auth: AuthSessionStore,
        private configListener: AugmentConfigListener
    ) {}

    async getAPIToken(): Promise<string> {
        if (this.auth.useOAuth) {
            const session = await this.auth.getSession();
            if (session) {
                return session.accessToken;
            }
        }
        return this.configListener.config.apiToken;
    }

    async getCompletionURL(): Promise<string> {
        if (this.auth.useOAuth) {
            const session = await this.auth.getSession();
            if (session) {
                return session.tenantURL;
            }
        }
        return this.configListener.config.completionURL;
    }

    removeAuthSession(): Promise<void> {
        return this.auth.removeSession();
    }
}
