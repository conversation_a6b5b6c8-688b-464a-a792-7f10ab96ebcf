import { AgentGlobalStateKeys } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import {
    IPluginStorageForSidecar,
    PluginStateNamespace,
    PluginStateScope,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-state";
import * as vscode from "vscode";

export class PluginStorageForSidecar implements IPluginStorageForSidecar {
    constructor(private _extensionContext: vscode.ExtensionContext) {
        // This setting was added to vscode before we move the setting to the sidecar.
        void this.migrateKeys(
            AgentGlobalStateKeys.hasEverUsedAgent,
            this.getKey(PluginStateNamespace.agent, AgentGlobalStateKeys.hasEverUsedAgent)
        );
    }

    async getValue<T>(
        namespace: PluginStateNamespace,
        key: string,
        scope: PluginStateScope
    ): Promise<T | undefined> {
        if (scope !== PluginStateScope.global) {
            throw new Error(`Scope ${String(scope)} is not supported`);
        }
        return await this._extensionContext.globalState.get(this.getKey(namespace, key));
    }

    async setValue<T>(
        namespace: PluginStateNamespace,
        key: string,
        value: T,
        scope: PluginStateScope
    ): Promise<void> {
        if (scope !== PluginStateScope.global) {
            throw new Error(`Scope ${String(scope)} is not supported`);
        }
        await this._extensionContext.globalState.update(this.getKey(namespace, key), value);
    }

    private getKey(namespace: PluginStateNamespace, key: string) {
        return ["sidecar", namespace, key].join(".");
    }

    private async migrateKeys(originalKey: string, newKey: string) {
        const value = await this._extensionContext.globalState.get(originalKey);
        if (value !== undefined) {
            await this._extensionContext.globalState.update(newKey, value);
            await this._extensionContext.globalState.update(originalKey, undefined);
        }
    }
}
