import {
    IClientConfig,
    SidecarAugmentConfig,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-config";

import { AugmentConfigListener } from "../augment-config-listener";

export class ClientConfig implements IClientConfig {
    constructor(private configListener: AugmentConfigListener) {}

    public getConfig(): Promise<Readonly<SidecarAugmentConfig>> {
        return Promise.resolve(this.configListener.config);
    }
}
