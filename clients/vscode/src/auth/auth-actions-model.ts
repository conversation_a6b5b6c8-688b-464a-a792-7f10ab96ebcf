import { AugmentConfigListener } from "../augment-config-listener";
import { ActionsModel } from "../main-panel/action-cards/actions-model";
import { DisposableService } from "../utils/disposable-service";
import { SystemStateName, SystemStatus } from "../utils/types";
import { AuthSessionStore } from "./auth-session-store";

// This class ensures the actions model sign in state is correct.
export class AuthActionsModel extends DisposableService {
    constructor(
        private _actionsModel: ActionsModel,
        private _authSession: AuthSessionStore,
        private _configListener: AugmentConfigListener
    ) {
        super();
        this.addDisposable(
            this._authSession.onDidChangeSession(() => {
                this._updateSignInState();
            })
        );
        this.addDisposable(
            this._authSession.onReady(() => {
                this._updateSignInState();
            })
        );
        this.addDisposable(
            this._configListener.onDidChange(({ newConfig, previousConfig }) => {
                if (
                    newConfig.apiToken === previousConfig.apiToken &&
                    newConfig.completionURL === previousConfig.completionURL
                ) {
                    return;
                }

                this._updateSignInState();
            })
        );

        this._updateSignInState();
    }

    private _updateSignInState() {
        if (!this._authSession.useOAuth) {
            // Using API token, so we won't need to sign in
            this._actionsModel.setSystemStateStatus(
                SystemStateName.authenticated,
                SystemStatus.complete
            );
            return;
        }

        // If the auth session is not ready, don't update the sign in state.
        if (this._authSession.isLoggedIn === undefined) {
            return;
        }

        if (this._authSession.isLoggedIn === false) {
            // Doing this so that can test the whole onboarding flow more easily
            if (this._configListener.config.enableDebugFeatures) {
                this._actionsModel.restartActionsState();
            }
        }

        const status = this._authSession.isLoggedIn
            ? SystemStatus.complete
            : SystemStatus.incomplete;
        this._actionsModel.setSystemStateStatus(SystemStateName.authenticated, status);
    }
}
