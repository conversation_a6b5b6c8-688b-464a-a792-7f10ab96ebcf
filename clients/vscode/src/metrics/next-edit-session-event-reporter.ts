import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { APIServer, NextEditSessionEvent } from "../augment-api";
import {
    type IEditSuggestion,
    type NextEditSessionEventName,
    NextEditSessionEventSource,
} from "../next-edit/next-edit-types";

export class NextEditSessionEventReporter extends MetricsReporter<NextEditSessionEvent> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1000;
    public static defaultUploadMsec = 10000;

    constructor(
        private _apiServer: APIServer,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "NextEditSessionEventReporter",
            maxRecords ?? NextEditSessionEventReporter.defaultMaxRecords,
            uploadMs ?? NextEditSessionEventReporter.defaultUploadMsec,
            batchSize ?? NextEditSessionEventReporter.defaultBatchSize
        );
    }

    // reportEvent reports a user next edit event.
    public reportEvent(
        requestId: string | undefined,
        suggestionId: string | undefined,
        eventTime: number,
        eventName: NextEditSessionEventName,
        eventSource: NextEditSessionEventSource
    ): void {
        const [eventTimeSec, eventTimeNsec] = msecToTimestamp(eventTime);
        /* eslint-disable @typescript-eslint/naming-convention */
        this.report({
            related_request_id: requestId,
            related_suggestion_id: suggestionId,
            event_time_sec: eventTimeSec,
            event_time_nsec: eventTimeNsec,
            event_name: eventName,
            event_source: eventSource,
        });
        /* eslint-enable @typescript-eslint/naming-convention */
    }

    // reportEventFromSuggestion reports a user next edit event.
    // TODO: Do not let suggestion be undefined.
    public reportEventFromSuggestion(
        suggestion: IEditSuggestion | undefined,
        eventName: NextEditSessionEventName,
        eventSource: NextEditSessionEventSource
    ): void {
        this.reportEvent(
            suggestion?.requestId,
            suggestion?.result.suggestionId,
            Date.now(),
            eventName,
            eventSource
        );
    }

    // reportEventWithoutIds reports a user next edit event.
    public reportEventWithoutIds(
        eventName: NextEditSessionEventName,
        eventSource: NextEditSessionEventSource
    ): void {
        this.reportEvent(undefined, undefined, Date.now(), eventName, eventSource);
    }

    protected performUpload(batch: NextEditSessionEvent[]): Promise<void> {
        return this._apiServer.logNextEditSessionEvent(batch);
    }
}
