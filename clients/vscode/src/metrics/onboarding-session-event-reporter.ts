import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { APIServer, OnboardingSessionEvent } from "../augment-api";
import { OnboardingSessionEventName } from "../onboarding/onboarding-types";

// Onboarding sessions are tied to a single set of auth tokens (session_id) and are workspace-agnostic.
// The only way to generate a new session_id is by logging out and logging back in,
// which also wipes any existing onboarding state, ensuring the user goes through onboarding again.
// Dashboard queries group events by session_id, preventing issues with repeated events.
export class OnboardingSessionEventReporter extends MetricsReporter<OnboardingSessionEvent> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1000;
    public static defaultUploadMsec = 10000;

    constructor(
        private _apiServer: APIServer,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "OnboardingSessionEventReporter",
            maxRecords ?? OnboardingSessionEventReporter.defaultMaxRecords,
            uploadMs ?? OnboardingSessionEventReporter.defaultUploadMsec,
            batchSize ?? OnboardingSessionEventReporter.defaultBatchSize
        );
    }

    // reportEvent reports a user onboarding event.
    public reportEvent(eventName: OnboardingSessionEventName): void {
        const [eventTimeSec, eventTimeNsec] = msecToTimestamp(Date.now());
        /* eslint-disable @typescript-eslint/naming-convention */
        this.report({
            event_time_sec: eventTimeSec,
            event_time_nsec: eventTimeNsec,
            event_name: eventName,
        });
        /* eslint-enable @typescript-eslint/naming-convention */
    }

    protected performUpload(batch: OnboardingSessionEvent[]): Promise<void> {
        return this._apiServer.logOnboardingSessionEvent(batch);
    }
}
