/**
 * Provides a utility for timing operations and recording those that exceed a
 * specified threshold.
 */

/**
 * Represents a timed operation.
 */
type TimedEvent = {
    name: string;
    durationMs: number;
    timestamp: Date;
};

/**
 * Utility for timing operations and tracking those that exceed a specified threshold.
 * This class can be used to identify performance bottlenecks in the application.
 */
export class WorkTimer {
    /** Threshold in milliseconds above which operations are considered slow */
    private thresholdMs: number;
    /** Collection of operations that exceeded the threshold */
    private slowEvents: TimedEvent[];
    /** Maximum number of slow events to store before pruning */
    private static readonly maxSlowEvents = 1000;

    /**
     * Creates a new WorkTimer instance.
     * @param thresholdMs The threshold in milliseconds above which operations are considered slow (default: 1000ms)
     */
    constructor(thresholdMs: number = 1000) {
        this.thresholdMs = thresholdMs;
        this.slowEvents = [];
    }

    /**
     * Executes and times a function, recording it as a slow event if it exceeds the threshold.
     * @param name Identifier for the operation being timed
     * @param fn The function to execute and time (can be synchronous or asynchronous)
     * @returns A promise that resolves with the result of the function
     */
    async runTimed<T>(name: string, fn: () => Promise<T> | T): Promise<T> {
        const start = process.hrtime.bigint();
        let result: T;

        try {
            result = await fn();
            return result;
        } finally {
            this.recordIfSlow(name, start);
        }
    }

    /**
     * Records an operation as slow if its duration exceeds the threshold.
     * @param name Identifier for the operation being timed
     * @param startTime The start time of the operation in nanoseconds (from process.hrtime.bigint())
     * @private
     */
    private recordIfSlow(name: string, startTime: bigint) {
        const durationNs = process.hrtime.bigint() - startTime;
        const durationMs = Number(durationNs) / 1_000_000;

        if (durationMs >= this.thresholdMs) {
            this.slowEvents.push({
                name,
                durationMs,
                timestamp: new Date(),
            });

            // Keep the list of slow events bounded if we rarely or never pop.
            if (this.slowEvents.length > WorkTimer.maxSlowEvents) {
                this.slowEvents.splice(0, WorkTimer.maxSlowEvents / 2);
            }
        }
    }

    /**
     * Retrieves and clears the list of recorded slow events.
     * @returns Array of slow events that have been recorded since the last pop
     */
    popSlowEvents(): TimedEvent[] {
        const events = this.slowEvents;
        this.slowEvents = [];
        return events;
    }

    /**
     * Clears all recorded slow events without returning them.
     */
    clear() {
        this.slowEvents = [];
    }
}
