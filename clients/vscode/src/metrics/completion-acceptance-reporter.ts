import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { APIServer, CompletionResolution } from "../augment-api";
import { OnboardingSessionEventName } from "../onboarding/onboarding-types";
import { OnboardingSessionEventReporter } from "./onboarding-session-event-reporter";

export class CompletionAcceptanceReporter extends MetricsReporter<CompletionResolution> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1000;
    public static defaultUploadMsec = 10000;

    constructor(
        private _apiServer: APIServer,
        private _onboardingSessionEventReporter: OnboardingSessionEventReporter,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "CompletionAcceptanceReporter",
            maxRecords ?? CompletionAcceptanceReporter.defaultMaxRecords,
            uploadMs ?? CompletionAcceptanceReporter.defaultUploadMsec,
            batchSize ?? CompletionAcceptanceReporter.defaultBatchSize
        );
    }

    // reportResolution reports on the resolution of a completion request.
    public reportResolution(
        requestId: string,
        emitTime: number,
        resolveTime: number,
        acceptedIndex: number | undefined
    ): void {
        const [emitTimeSec, emitTimeNsec] = msecToTimestamp(emitTime);
        const [resolveTimeSec, resolveTimeNsec] = msecToTimestamp(resolveTime);
        /* eslint-disable @typescript-eslint/naming-convention */
        this.report({
            request_id: requestId,
            emit_time_sec: emitTimeSec,
            emit_time_nsec: emitTimeNsec,
            resolve_time_sec: resolveTimeSec,
            resolve_time_nsec: resolveTimeNsec,
            accepted_idx: acceptedIndex ?? -1,
        });
        /* eslint-enable @typescript-eslint/naming-convention */
        if (acceptedIndex !== undefined) {
            this._onboardingSessionEventReporter.reportEvent(
                OnboardingSessionEventName.AcceptedCompletion
            );
        }
    }

    protected performUpload(batch: CompletionResolution[]): Promise<void> {
        return this._apiServer.resolveCompletions(batch);
    }
}
