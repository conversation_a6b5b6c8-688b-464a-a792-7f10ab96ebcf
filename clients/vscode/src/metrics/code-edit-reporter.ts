import { retryWithBackoff } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { APIServer } from "../augment-api";
import { getLogger } from "../logging";

/**
 * CodeEditReporter is not extending the MetricsReporter,
 * this is because the API for logging code edit resolutions
 * does not take an array of resolutions, meaning we can't
 * upload in batches.
 *
 * This is kept as a reporter to follow a similar pattern as
 * completion acceptance resolutions, but we could inline the
 * API calls for tracking resolutions and it would have the
 * same behavior.
 */
export class CodeEditReporter {
    private uploadEnabled = false;
    private _logger = getLogger("CodeEditReporter");

    constructor(private _apiServer: APIServer) {}

    public enableUpload(): void {
        this.uploadEnabled = true;
    }

    public disableUpload(): void {
        this.uploadEnabled = false;
    }

    public dispose() {
        this.disableUpload();
    }

    // reportResolution reports on the resolution of a code edit request.
    public async reportResolution(
        requestId: string,
        emitTime: number,
        resolveTime: number,
        isAccepted: boolean,
        annotatedText?: string
    ): Promise<void> {
        const [emitTimeSec, emitTimeNsec] = msecToTimestamp(emitTime);
        const [resolveTimeSec, resolveTimeNsec] = msecToTimestamp(resolveTime);
        /* eslint-disable @typescript-eslint/naming-convention */
        const editResolution = {
            request_id: requestId,
            emit_time_sec: emitTimeSec,
            emit_time_nsec: emitTimeNsec,
            resolve_time_sec: resolveTimeSec,
            resolve_time_nsec: resolveTimeNsec,
            is_accepted: isAccepted,
            annotated_text: annotatedText,
        };
        /* eslint-enable @typescript-eslint/naming-convention */
        await retryWithBackoff<void>(async () => {
            if (!this.uploadEnabled) {
                return;
            }
            try {
                return await this._apiServer.logCodeEditResolution(editResolution);
            } catch (e: any) {
                this._logger.error(`Error reporting edit resolution: ${e}`);
                throw e;
            }
        }, this._logger);
    }
}
