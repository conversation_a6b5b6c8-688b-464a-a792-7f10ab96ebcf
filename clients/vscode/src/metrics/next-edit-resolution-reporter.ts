import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { APIServer, NextEditResolution } from "../augment-api";

// TODO(AU-4121): Use this reporter to track resolutions once we have a new
// resolutions backend endpoint that accepts suggestion ids.
export class NextEditResolutionReporter extends MetricsReporter<NextEditResolution> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1000;
    public static defaultUploadMsec = 10000;

    constructor(
        private _apiServer: APIServer,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "NextEditResolutionReporter",
            maxRecords ?? NextEditResolutionReporter.defaultMaxRecords,
            uploadMs ?? NextEditResolutionReporter.defaultUploadMsec,
            batchSize ?? NextEditResolutionReporter.defaultBatchSize
        );
    }

    // reportResolution reports on the resolution of a next edit request.
    public reportResolution(
        requestId: string,
        emitTime: number,
        resolveTime: number,
        isAccepted: boolean
    ): void {
        const [emitTimeSec, emitTimeNsec] = msecToTimestamp(emitTime);
        const [resolveTimeSec, resolveTimeNsec] = msecToTimestamp(resolveTime);
        /* eslint-disable @typescript-eslint/naming-convention */
        this.report({
            request_id: requestId,
            emit_time_sec: emitTimeSec,
            emit_time_nsec: emitTimeNsec,
            resolve_time_sec: resolveTimeSec,
            resolve_time_nsec: resolveTimeNsec,
            is_accepted: isAccepted,
        });
        /* eslint-enable @typescript-eslint/naming-convention */
    }

    protected performUpload(batch: NextEditResolution[]): Promise<void> {
        return this._apiServer.resolveNextEdits(batch);
    }
}
