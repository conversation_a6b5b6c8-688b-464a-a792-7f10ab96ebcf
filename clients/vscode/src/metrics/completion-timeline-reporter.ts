import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { APIServer, ClientCompletionTimline } from "../augment-api";
import { CompletionTimeline } from "../completions/completion-timeline";

export class ClientCompletionTimelineReporter extends MetricsReporter<ClientCompletionTimline> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1000;
    public static defaultUploadMsec = 10000;

    constructor(
        private _apiServer: APIServer,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "TimelineEventReporter",
            maxRecords ?? ClientCompletionTimelineReporter.defaultMaxRecords,
            uploadMs ?? ClientCompletionTimelineReporter.defaultUploadMsec,
            batchSize ?? ClientCompletionTimelineReporter.defaultBatchSize
        );
    }

    // Enqueues valid completion timelines for batch reporting
    public reportCompletionTimeline(
        requestId: string,
        completionTimeline: CompletionTimeline
    ): void {
        if (
            !completionTimeline.emitTime ||
            !completionTimeline.rpcStart ||
            !completionTimeline.rpcEnd
        ) {
            return;
        }
        const [requestTimeS, requestTimeNs] = msecToTimestamp(completionTimeline.requestStart);
        const [emitTimeS, emitTimeNs] = msecToTimestamp(completionTimeline.emitTime);
        const [apiStartTimeS, apiStartTimeNs] = msecToTimestamp(completionTimeline.rpcStart);
        const [apiEndTimeS, apiEndTimeNs] = msecToTimestamp(completionTimeline.rpcEnd);

        /* eslint-disable @typescript-eslint/naming-convention */
        this.report({
            request_id: requestId,
            initial_request_time_sec: requestTimeS,
            initial_request_time_nsec: requestTimeNs,
            api_start_time_sec: apiStartTimeS,
            api_start_time_nsec: apiStartTimeNs,
            api_end_time_sec: apiEndTimeS,
            api_end_time_nsec: apiEndTimeNs,
            emit_time_sec: emitTimeS,
            emit_time_nsec: emitTimeNs,
        });
        /* eslint-enable @typescript-eslint/naming-convention */
    }

    protected performUpload(batch: ClientCompletionTimline[]): Promise<void> {
        return this._apiServer.reportClientCompletionTimelines(batch);
    }
}
