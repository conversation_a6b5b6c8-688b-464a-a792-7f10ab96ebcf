import { APIServer } from "$vscode/src/augment-api";
import { WorkTimer } from "$vscode/src/metrics/work-timer";
import { AugmentGlobalState } from "$vscode/src/utils/context";
import { PanelWebviewBase } from "$vscode/src/utils/panel-webview-base";
import { createAsyncMsgHandlerFromWebview } from "$vscode/src/utils/webviews/messaging-helper";
import { WebviewManager } from "$vscode/src/webview-providers/webview-manager";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";

import { IRemoteAgentDiffPanelOptions } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import * as vscode from "vscode";

import { DiffPanelMessenger } from "./diff-panel-messenger";
import { RemoteAgentDiffMessenger } from "./remote-agent-diff-messenger";

interface IRemoteAgentDiffPanelDeps {
    extensionUri: vscode.Uri;
    apiServer: APIServer;
    workTimer: WorkTimer;
    globalState: AugmentGlobalState;
}

export class RemoteAgentDiffPanel extends PanelWebviewBase {
    /**
     * Track the current panel. Only allow a single panel to exist at a time for now.
     */
    public static currentPanel: RemoteAgentDiffPanel | undefined;
    public static title = "Remote Agent Changes";

    private readonly _panel: vscode.WebviewPanel;
    private readonly _diffPanelMessenger: DiffPanelMessenger;
    private readonly _remoteAgentDiffMessenger: RemoteAgentDiffMessenger;

    constructor(
        private readonly _deps: IRemoteAgentDiffPanelDeps,
        /** The opts for the diff panel */
        private _opts: IRemoteAgentDiffPanelOptions
    ) {
        const panel = vscode.window.createWebviewPanel(
            "remote agent diff",
            RemoteAgentDiffPanel.title,
            vscode.ViewColumn.Active,
            {
                retainContextWhenHidden: true,
                enableScripts: true,
            }
        );
        super("remote-agent-diff.html", panel.webview);
        this._panel = panel;

        const { extensionUri } = this._deps;
        this._panel.iconPath = {
            light: vscode.Uri.joinPath(extensionUri, "media", "panel-icon-light.svg"),
            dark: vscode.Uri.joinPath(extensionUri, "media", "panel-icon-dark.svg"),
        };

        this._panel.onDidDispose(() => {
            WebviewManager.getInstance().broadcastMessage({
                type: WebViewMessageType.closeRemoteAgentDiffPanel,
            });
            WebviewManager.getInstance().unregisterMessageHandler(DiffPanelMessenger.messengerId);
            this.dispose();
        });

        this.addDisposables(
            this._panel,
            new vscode.Disposable(() => {
                RemoteAgentDiffPanel.currentPanel = undefined;
            })
        );
        const asyncMsgHandler = createAsyncMsgHandlerFromWebview(
            this._panel.webview,
            this._deps.workTimer
        );
        this.addDisposable(asyncMsgHandler);

        this._diffPanelMessenger = new DiffPanelMessenger(this._panel.webview, this._opts);
        this._diffPanelMessenger.register(asyncMsgHandler);
        this._remoteAgentDiffMessenger = new RemoteAgentDiffMessenger(this._deps.apiServer);
        this._remoteAgentDiffMessenger.setRemoteAgentId(this._opts.remoteAgentId);
        this._remoteAgentDiffMessenger.register(asyncMsgHandler);

        void this.loadHTML(extensionUri);
    }

    public updateOpts(opts: IRemoteAgentDiffPanelOptions) {
        this._opts = opts;
        this._diffPanelMessenger.updateOpts(opts);
        this._remoteAgentDiffMessenger.setRemoteAgentId(opts.remoteAgentId);
    }

    public static createOrShow(
        extensionUri: vscode.Uri,
        apiServer: APIServer,
        workTimer: WorkTimer,
        globalState: AugmentGlobalState,
        opts: IRemoteAgentDiffPanelOptions
    ) {
        const webviewManager = WebviewManager.getInstance();

        if (RemoteAgentDiffPanel.currentPanel) {
            RemoteAgentDiffPanel.currentPanel._panel.reveal(vscode.ViewColumn.Active);
            RemoteAgentDiffPanel.currentPanel.updateOpts.call(
                RemoteAgentDiffPanel.currentPanel,
                opts
            );
            webviewManager.broadcastMessage({
                type: WebViewMessageType.showRemoteAgentDiffPanel,
                data: opts,
            });
            return;
        }

        RemoteAgentDiffPanel.currentPanel = new RemoteAgentDiffPanel(
            {
                extensionUri,
                apiServer,
                workTimer,
                globalState,
            },
            opts
        );
        webviewManager.broadcastMessage({
            type: WebViewMessageType.showRemoteAgentDiffPanel,
            data: opts,
        });
    }

    public static close() {
        const webviewManager = WebviewManager.getInstance();
        webviewManager.broadcastMessage({
            type: WebViewMessageType.closeRemoteAgentDiffPanel,
        });
        if (RemoteAgentDiffPanel.currentPanel) {
            RemoteAgentDiffPanel.currentPanel._panel.dispose();
        }
    }
}
