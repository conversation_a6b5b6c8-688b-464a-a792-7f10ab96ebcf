import { detectLineEnding } from "$vscode/src/utils/line-utils";

import * as Diff3 from "node-diff3";

export function threeWayMerge(originalContent: string, newContent: string, currentContent: string) {
    const splitString = (str = "") => str.split(/(?<=\r\n|\n|\r)/g);
    const currentLines = splitString(currentContent);
    const originalLines = splitString(originalContent);
    const newLines = splitString(newContent);
    const hunks = Diff3.merge<string>(currentLines, originalLines, newLines, {
        excludeFalseConflicts: true,
    });

    let mergedContent = "";
    let hasConflicts = false;
    if (hunks.conflict) {
        // If there are conflicts, we need to properly join the conflict markers
        // that are returned by the Diff3 library.
        const lineEnding = detectLineEnding(currentContent);
        mergedContent = hunks.result
            .map((line) => (line.endsWith(lineEnding) ? line : line + lineEnding))
            .join("");
        hasConflicts = true;
    } else {
        mergedContent = hunks.result.join("");
    }
    return { mergedContent, hasConflicts };
}
