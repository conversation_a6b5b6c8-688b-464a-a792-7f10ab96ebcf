/* eslint-disable @typescript-eslint/naming-convention */
import { APIServer, RevokeToolAccessStatus } from "$vscode/src/augment-api";
import { AugmentConfigListener } from "$vscode/src/augment-config-listener";
import { type AugmentLogger, getLogger } from "$vscode/src/logging";
import { GitBranch } from "$vscode/src/remote-agent-manager/commit-ref-types";
import { getLocalBranchName } from "$vscode/src/remote-agent-manager/utils/git";
import { RemoteWorkspaceResolver } from "$vscode/src/remote-workspace-resolver";
import { AsyncMsgHandler } from "$vscode/src/utils/webviews/messaging";
import { executeCommand } from "$vscode/src/vcs/command-utils";
import {
    AuthenticateGithubRequestMessage,
    AuthenticateGithubResponseMessage,
    GetCurrentLocalBranchRequestMessage,
    GetCurrentLocalBranchResponseMessage,
    GetGitBranchesRequestMessage,
    GetGitBranchesResponseMessage,
    GetGithubRepoRequestMessage,
    GetGithubRepoResponseMessage,
    GetRemoteUrlRequestMessage,
    GetRemoteUrlResponseMessage,
    GetWorkspaceDiffRequestMessage,
    GetWorkspaceDiffResponseMessage,
    GitFetchRequestMessage,
    GitFetchResponseMessage,
    IsGithubAuthenticatedRequestMessage,
    IsGithubAuthenticatedResponseMessage,
    IsGitRepositoryRequestMessage,
    IsGitRepositoryResponseMessage,
    ListGithubRepoBranchesRequestMessage,
    ListGithubRepoBranchesResponseMessage,
    ListGithubReposForAuthenticatedUserRequestMessage,
    ListGithubReposForAuthenticatedUserResponseMessage,
    RevokeGithubAccessRequestMessage,
    RevokeGithubAccessResponseMessage,
    WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";

import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { getRemoteAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/remote-agent-session-event-reporter";
import { RemoteAgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
import { RemoteToolId } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import * as vscode from "vscode";

/**
 * Stateless message handler for git reference webview messages
 */
export class GitReferenceMessenger {
    private readonly logger: AugmentLogger;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _configListener: AugmentConfigListener
    ) {
        this.logger = getLogger("GitReferenceMessenger");
    }

    get execOptions() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error("No workspace folder found");
        }
        return { cwd: workspaceFolder.uri.fsPath };
    }

    private get isUsingDevDeploy() {
        const isDevDeploy = !!this._configListener.config.completionURL;
        if (isDevDeploy) {
            this.logger.warn("Using dev deploy");
        }
        return isDevDeploy;
    }

    register(_asyncMsgHandler: AsyncMsgHandler) {
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getGitBranchesRequest,
            this.handleGetGitBranchesRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getWorkspaceDiffRequest,
            this.handleGetWorkspaceDiffRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getRemoteUrlRequest,
            this.handleGetRemoteUrlRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.gitFetchRequest,
            this.handleGitFetchRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.isGitRepositoryRequest,
            this.handleIsGitRepositoryRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.isGithubAuthenticatedRequest,
            this.handleIsGithubAuthenticatedRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.authenticateGithubRequest,
            this.handleAuthenticateGithubRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.revokeGithubAccessRequest,
            this.handleRevokeGithubAccessRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.listGithubReposForAuthenticatedUserRequest,
            this.handleListUserReposRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.listGithubRepoBranchesRequest,
            this.handleListRepoBranchesRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getGithubRepoRequest,
            this.handleGetGithubRepoRequest.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.getCurrentLocalBranchRequest,
            this.handleGetCurrentLocalBranchRequest.bind(this)
        );
    }

    /**
     * Get the primary remote name for the repository
     * Prioritizes 'origin' but falls back to the first available remote
     */
    private async getPrimaryRemote(): Promise<string> {
        try {
            const remotes = await executeCommand("git remote", this.execOptions);
            const remoteList =
                remotes
                    ?.split("\n")
                    .map((r) => r.trim())
                    .filter(Boolean) || [];

            // Prefer 'origin' if it exists
            if (remoteList.includes("origin")) {
                return "origin";
            }

            // Fall back to the first remote
            return remoteList[0] || "origin";
        } catch {
            // If git remote fails, assume 'origin'
            return "origin";
        }
    }

    /**
     * Handle request to get git branches
     */
    private async handleGetGitBranchesRequest(
        msg: GetGitBranchesRequestMessage
    ): Promise<GetGitBranchesResponseMessage> {
        try {
            const { prefix = "" } = msg.data;
            const { execOptions } = this;
            const branches = [] as GitBranch[];
            const currentBranch = (await this.executeGetCurrentBranch())?.trim();
            const primaryRemote = await this.getPrimaryRemote();

            // Check if current branch is remote without making a network call. So we assume fetch has already been called.
            const isCurrentBranchRemote = !!(await executeCommand(
                "git branch -r | grep $(git branch --show-current)",
                execOptions
            ));

            // Get default branch using git's canonical ref resolution
            const defaultBranch =
                (
                    await executeCommand(
                        `git symbolic-ref refs/remotes/${primaryRemote}/HEAD`,
                        execOptions
                    )
                )
                    ?.trim()
                    .replace(`refs/remotes/${primaryRemote}/`, "") || "";

            // Get remote branches using git's formatting to get clean branch names
            const remoteBranches = (
                await executeCommand(
                    `git for-each-ref --format="%(refname:lstrip=3)" refs/remotes/${primaryRemote}/`,
                    execOptions
                )
            )
                ?.split("\n")
                .map((line) => line.trim())
                .filter(Boolean) // Filter out empty lines
                .filter((branch) => branch !== "HEAD"); // Filter out HEAD reference

            const filteredRemoteBranches = remoteBranches
                ?.map((branch) => {
                    // check if matches prefix (using original branch name with potential remote prefix)
                    if (branch.includes(prefix) || `${primaryRemote}/${branch}`.includes(prefix)) {
                        return branch;
                    }
                    return undefined;
                })
                .filter(Boolean) as string[];

            const allBranches = filteredRemoteBranches;

            allBranches?.forEach((branch) => {
                // Branch names should be clean from git formatting, but ensure any symbolic refs are handled
                const cleanBranchName = getLocalBranchName(branch.trim());
                branches.push({
                    name: cleanBranchName,
                    isRemote: cleanBranchName === currentBranch ? isCurrentBranchRemote : true,
                    isCurrentBranch: cleanBranchName === currentBranch,
                    isDefault: cleanBranchName === defaultBranch,
                });
            });

            return {
                type: WebViewMessageType.getGitBranchesResponse,
                data: {
                    branches,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get local git branches:", error);
            this.logger.error("Failed to get local git branches:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.getGitBranchesResponse,
                data: {
                    branches: [],
                },
            };
        }
    }

    /**
     * Handle request to get workspace diff from a commit
     */
    private async handleGetWorkspaceDiffRequest(
        msg: GetWorkspaceDiffRequestMessage
    ): Promise<GetWorkspaceDiffResponseMessage> {
        try {
            const { branchName } = msg.data;
            const primaryRemote = await this.getPrimaryRemote();

            // Get diff between local (including uncommitted changes) and remote branch
            let diffOutput =
                (await executeCommand(
                    `git diff ${primaryRemote}/${branchName} || true`,
                    this.execOptions
                )) || "";
            const untrackedFiles =
                (await executeCommand(
                    `git ls-files --others --exclude-standard`,
                    this.execOptions
                )) || "";
            for (const file of untrackedFiles.trimEnd().split("\n") || []) {
                diffOutput +=
                    (await executeCommand(
                        process.platform === "win32"
                            ? `git diff --no-index NUL ${file} || exit 0`
                            : `git diff --no-index /dev/null ${file} || true`,
                        this.execOptions
                    )) || "";
            }
            return {
                type: WebViewMessageType.getWorkspaceDiffResponse,
                data: {
                    diff: diffOutput,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get workspace diff:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.getWorkspaceDiffResponse,
                data: {
                    diff: "",
                },
            };
        }
    }

    private async handleGetRemoteUrlRequest(
        _: GetRemoteUrlRequestMessage
    ): Promise<GetRemoteUrlResponseMessage> {
        const { execOptions } = this;
        try {
            const remote = (await executeCommand("git remote get-url origin", execOptions))?.trim();
            if (!remote) {
                throw new Error("Failed to get remote url, no remote found");
            }
            const isRemoteHttps = !!remote?.startsWith("https://");
            if (isRemoteHttps) {
                return {
                    type: WebViewMessageType.getRemoteUrlResponse,
                    data: {
                        remoteUrl: remote,
                    },
                };
            } else {
                // TODO: make it so backend can handle ssh urls. Also check for forks
                // Convert ssh to https
                const httpsUrl = remote
                    .replace("ssh://", "")
                    .replace("git@", "https://")
                    .replace(".com:", ".com/")
                    .replace(/\.git$/, "");
                return {
                    type: WebViewMessageType.getRemoteUrlResponse,
                    data: {
                        remoteUrl: httpsUrl,
                    },
                };
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get remote url:", error);
            this.logger.error("Failed to locally get remote url:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.getRemoteUrlResponse,
                data: {
                    remoteUrl: "",
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    private async handleGitFetchRequest(
        _: GitFetchRequestMessage
    ): Promise<GitFetchResponseMessage> {
        try {
            await executeCommand("git fetch", this.execOptions);
            return {
                type: WebViewMessageType.gitFetchResponse,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to fetch remote branch:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.gitFetchResponse,
            };
        }
    }

    private async handleIsGitRepositoryRequest(
        _: IsGitRepositoryRequestMessage
    ): Promise<IsGitRepositoryResponseMessage> {
        try {
            const isGitRepository = !!(await executeCommand(
                process.platform === "win32"
                    ? "git rev-parse --is-inside-work-tree 2>NUL"
                    : "git rev-parse --is-inside-work-tree 2>/dev/null",
                this.execOptions
            ));
            // TODO (jw): remove when stable
            // eslint-disable-next-line no-console
            console.log("isGitRepository: ", isGitRepository);
            return {
                type: WebViewMessageType.isGitRepositoryResponse,
                data: {
                    isGitRepository,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to check if is git repository:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.isGitRepositoryResponse,
                data: {
                    isGitRepository: false,
                },
            };
        }
    }

    /**
     * Check if the user is authenticated with GitHub
     */
    private async handleIsGithubAuthenticatedRequest(
        _: IsGithubAuthenticatedRequestMessage
    ): Promise<IsGithubAuthenticatedResponseMessage> {
        try {
            // If someone is using dev deploy, we don't check for GitHub tool availability because
            // extra OAuth deployments would need to be made. So just let them thru.
            if (this.isUsingDevDeploy) {
                return {
                    type: WebViewMessageType.isGithubAuthenticatedResponse,
                    data: {
                        isAuthenticated: true,
                    },
                };
            }

            const { is_configured } = await this._apiServer.isUserGithubConfigured();

            return {
                type: WebViewMessageType.isGithubAuthenticatedResponse,
                data: { isAuthenticated: is_configured },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to check GitHub authentication status:", error);
            this.logger.error("Failed to check GitHub authentication status:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.isGithubAuthenticatedResponse,
                data: {
                    isAuthenticated: false,
                },
            };
        }
    }

    /**
     * Authenticate with GitHub by opening the OAuth URL in the browser
     */
    private async handleAuthenticateGithubRequest(
        _: AuthenticateGithubRequestMessage
    ): Promise<AuthenticateGithubResponseMessage> {
        let oauthUrl: string | undefined;
        try {
            const tools = await this._apiServer.listRemoteTools([RemoteToolId.GitHubApi]);
            const githubTool = tools.tools.find(
                (tool) => tool.remoteToolId === RemoteToolId.GitHubApi
            );
            if (!githubTool || !githubTool.oauthUrl) {
                this.logger.error("Failed to authenticate with GitHub: GitHub OAuth URL not found");
                return {
                    type: WebViewMessageType.authenticateGithubResponse,
                    data: {
                        success: false,
                        message: "GitHub OAuth URL not found",
                    },
                };
            }
            oauthUrl = githubTool.oauthUrl;
            const uri = vscode.Uri.parse(oauthUrl);
            const success = await vscode.env.openExternal(uri);
            if (!success) {
                this.logger.error(
                    "Failed to authenticate with GitHub: Failed to open browser for GitHub authentication"
                );
            }
            return {
                type: WebViewMessageType.authenticateGithubResponse,
                data: {
                    success,
                    message: success
                        ? "Browser opened for GitHub authentication"
                        : "Failed to open browser",
                    url: success ? undefined : oauthUrl,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to authenticate with GitHub:", error);
            this.logger.error("Failed to authenticate with GitHub: ", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.authenticateGithubResponse,
                data: {
                    success: false,
                    message: `Error: ${error instanceof Error ? error.message : String(error)}`,
                    url: oauthUrl,
                },
            };
        }
    }

    /**
     * Revoke GitHub access using the Augment API
     */
    private async handleRevokeGithubAccessRequest(
        _: RevokeGithubAccessRequestMessage
    ): Promise<RevokeGithubAccessResponseMessage> {
        try {
            // Call the API to revoke GitHub access
            const result = await this._apiServer.revokeToolAccess(RemoteToolId.GitHubApi);

            // Check the result status
            if (result.status === RevokeToolAccessStatus.Success) {
                return {
                    type: WebViewMessageType.revokeGithubAccessResponse,
                    data: {
                        success: true,
                        message: "GitHub access revoked successfully",
                    },
                };
            } else if (result.status === RevokeToolAccessStatus.NotActive) {
                return {
                    type: WebViewMessageType.revokeGithubAccessResponse,
                    data: {
                        success: true,
                        message: "GitHub is not configured",
                    },
                };
            } else {
                return {
                    type: WebViewMessageType.revokeGithubAccessResponse,
                    data: {
                        success: false,
                        message: `Failed to revoke GitHub access: ${RevokeToolAccessStatus[result.status]}`,
                    },
                };
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to revoke GitHub access:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.revokeGithubAccessResponse,
                data: {
                    success: false,
                    message: `Error: ${error instanceof Error ? error.message : String(error)}`,
                },
            };
        }
    }

    private async handleListUserReposRequest(
        message: ListGithubReposForAuthenticatedUserRequestMessage
    ): Promise<ListGithubReposForAuthenticatedUserResponseMessage> {
        try {
            if (this.isUsingDevDeploy) {
                return {
                    type: WebViewMessageType.listGithubReposForAuthenticatedUserResponse,
                    data: {
                        repos: [],
                        hasNextPage: false,
                        nextPage: 0,
                        isDevDeploy: true,
                    },
                };
            }
            const response = await this._apiServer.listGithubReposForAuthenticatedUser(
                message.data.page
            );
            return {
                type: WebViewMessageType.listGithubReposForAuthenticatedUserResponse,
                data: {
                    repos: response.repos,
                    hasNextPage: response.has_next_page,
                    nextPage: response.next_page,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to list user repos:", error);
            this.logger.error("Failed to list user repos:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.listGithubReposForAuthenticatedUserResponse,
                data: {
                    repos: [],
                    hasNextPage: false,
                    nextPage: 0,
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    private async handleListRepoBranchesRequest(
        message: ListGithubRepoBranchesRequestMessage
    ): Promise<ListGithubRepoBranchesResponseMessage> {
        try {
            if (this.isUsingDevDeploy) {
                return {
                    type: WebViewMessageType.listGithubRepoBranchesResponse,
                    data: {
                        branches: [],
                        hasNextPage: false,
                        nextPage: 0,
                        isDevDeploy: true,
                    },
                };
            }
            const response = await this._apiServer.listGithubRepoBranches(
                message.data.repo,
                message.data.page
            );
            return {
                type: WebViewMessageType.listGithubRepoBranchesResponse,
                data: {
                    branches: response.branches,
                    hasNextPage: response.has_next_page,
                    nextPage: response.next_page,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to list repo branches:", error);
            this.logger.error("Failed to list repo branches:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.listGithubRepoBranchesResponse,
                data: {
                    branches: [],
                    hasNextPage: false,
                    nextPage: 0,
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    private async handleGetGithubRepoRequest(
        message: GetGithubRepoRequestMessage
    ): Promise<GetGithubRepoResponseMessage> {
        try {
            if (this.isUsingDevDeploy) {
                return {
                    type: WebViewMessageType.getGithubRepoResponse,
                    data: {
                        repo: message.data.repo,
                        isDevDeploy: true,
                    },
                };
            }
            const response = await this._apiServer.getGithubRepo(message.data.repo);
            return {
                type: WebViewMessageType.getGithubRepoResponse,
                data: {
                    repo: response.repo,
                    error: response.error,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get GitHub repo:", error);
            this.logger.error("Failed to get GitHub repo:", error);
            this.reportGithubAPIFailure(error);
            return {
                type: WebViewMessageType.getGithubRepoResponse,
                data: {
                    repo: message.data.repo,
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    private async handleGetCurrentLocalBranchRequest(
        _: GetCurrentLocalBranchRequestMessage
    ): Promise<GetCurrentLocalBranchResponseMessage> {
        try {
            const currentBranch = await this.executeGetCurrentBranch();
            return {
                type: WebViewMessageType.getCurrentLocalBranchResponse,
                data: {
                    branch: currentBranch,
                },
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get current local branch:", error);
            this.logger.error("Failed to get current local branch:", error);
            return {
                type: WebViewMessageType.getCurrentLocalBranchResponse,
                data: {
                    branch: undefined,
                    error: error instanceof Error ? error.message : String(error),
                },
            };
        }
    }

    private executeGetCurrentBranch() {
        return executeCommand("git branch --show-current", this.execOptions);
    }

    /**
     * Report GitHub API failure to remote agent session events
     */
    private reportGithubAPIFailure(error: any): void {
        try {
            const remoteAgentId = RemoteWorkspaceResolver.getRemoteAgentId();
            if (!remoteAgentId) {
                // Only report GitHub API failures when in a remote agent context
                return;
            }

            // Extract error code from different error types
            let errorCode = 0; // Default to 0 for unknown errors

            if (error instanceof APIError) {
                // Map APIStatus to HTTP-like error codes for consistency
                switch (error.status) {
                    case APIStatus.unauthenticated:
                        errorCode = 401;
                        break;
                    case APIStatus.permissionDenied:
                        errorCode = 403;
                        break;
                    case APIStatus.unimplemented:
                        errorCode = 404;
                        break;
                    case APIStatus.resourceExhausted:
                        errorCode = 429;
                        break;
                    case APIStatus.unavailable:
                        errorCode = 503;
                        break;
                    case APIStatus.deadlineExceeded:
                        errorCode = 504;
                        break;
                    default:
                        errorCode = 500;
                        break;
                }
            } else if (error instanceof Error) {
                // Try to extract HTTP status code from error message
                const statusMatch = error.message.match(/\b(4\d{2}|5\d{2})\b/);
                if (statusMatch) {
                    errorCode = parseInt(statusMatch[1], 10);
                } else {
                    errorCode = 500; // Generic server error
                }
            }

            // Report the GitHub API failure event
            getRemoteAgentSessionEventReporter().reportEvent({
                eventName: RemoteAgentSessionEventName.githubAPIFailure,
                remoteAgentId,
                eventData: {
                    githubAPIFailure: {
                        errorCode,
                    },
                },
            });

            this.logger.debug(
                `Reported GitHub API failure with error code ${errorCode} for remote agent ${remoteAgentId}`
            );
        } catch (reportError) {
            // Don't let error reporting itself cause issues
            this.logger.error("Failed to report GitHub API failure:", reportError);
        }
    }
}
