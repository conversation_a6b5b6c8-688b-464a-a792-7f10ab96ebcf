import { AsyncMsgHandler } from "$vscode/src/utils/webviews/messaging";
import { WebviewManager } from "$vscode/src/webview-providers/webview-manager";
import {
    RemoteAgentDiffPanelLoadedMessage,
    RemoteAgentDiffPanelSetOptsMessage,
    WebViewMessage,
    WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";

import { IRemoteAgentDiffPanelOptions } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import * as vscode from "vscode";

/** Class for handling messages specific to the remote agent diff panel. */
export class DiffPanelMessenger {
    public static readonly messengerId = "remote-agent-diff-panel-messenger";

    constructor(
        private readonly _webview: vscode.Webview,
        private _opts: IRemoteAgentDiffPanelOptions
    ) {}

    register(_asyncMsgHandler: AsyncMsgHandler) {
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.remoteAgentDiffPanelLoaded,
            this.handleDiffPanelLoadedMessage.bind(this)
        );
        const webviewManager = WebviewManager.getInstance();
        if (!webviewManager.hasHandler(DiffPanelMessenger.messengerId)) {
            webviewManager.registerMessageHandler(
                DiffPanelMessenger.messengerId,
                async (msg: WebViewMessage) => {
                    await this._webview.postMessage(msg);
                }
            );
        }
    }

    private handleDiffPanelLoadedMessage(
        _: RemoteAgentDiffPanelLoadedMessage
    ): RemoteAgentDiffPanelSetOptsMessage {
        return {
            type: WebViewMessageType.remoteAgentDiffPanelSetOpts,
            data: this._opts,
        };
    }

    public updateOpts(opts: IRemoteAgentDiffPanelOptions) {
        this._opts = opts;
        void this._webview.postMessage({
            type: WebViewMessageType.remoteAgentDiffPanelSetOpts,
            data: this._opts,
        });
    }
}
