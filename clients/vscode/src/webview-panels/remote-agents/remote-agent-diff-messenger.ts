/* eslint-disable @typescript-eslint/naming-convention */
import { APIServer } from "$vscode/src/augment-api";
import { findGitRoot } from "$vscode/src/utils/fs-utils";
import {
    getDiffDescriptions,
    getDiffExplanation,
    groupChangesWithLLM,
} from "$vscode/src/utils/get-diff-explanation";
import { AsyncMsgHandler } from "$vscode/src/utils/webviews/messaging";
import { executeCommand } from "$vscode/src/vcs/command-utils";
import {
    type ApplyChangesRequestMessage,
    type ApplyChangesResponseMessage,
    type CanApplyChangesRequestMessage,
    type CanApplyChangesResponseMessage,
    type DiffDescriptionsRequestMessage,
    type DiffDescriptionsResponseMessage,
    type DiffExplanationRequestMessage,
    type DiffExplanationResponseMessage,
    type DiffGroupChangesRequestMessage,
    type DiffGroupChangesResponseMessage,
    EmptyMessage,
    type OpenFileRequestMessage,
    type OpenFileResponseMessage,
    type PreviewApplyChangesRequestMessage,
    type PreviewApplyChangesResponseMessage,
    type ReportAgentChangesAppliedMessage,
    type StashUnstagedChangesRequestMessage,
    type StashUnstagedChangesResponseMessage,
    WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";

import { ChatMode, ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { getRemoteAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/remote-agent-session-event-reporter";
import { RemoteAgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
import { llmCallStream } from "@augment-internal/sidecar-libs/src/utils/tools-utils";
import * as vscode from "vscode";
import { workspace } from "vscode";

import { threeWayMerge } from "./utils/diff-operations";

/**
 * Stateless handler for remote agent diff webview messages. It handles
 * all the diff-related functionality.
 */
export class RemoteAgentDiffMessenger {
    private gitRoot: string | undefined;
    private _remoteAgentId: string | undefined;

    constructor(private _api: APIServer) {}

    /**
     * Set the remote agent ID for event reporting
     */
    setRemoteAgentId(remoteAgentId: string | undefined) {
        this._remoteAgentId = remoteAgentId;
    }

    register(_asyncMsgHandler: AsyncMsgHandler) {
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.diffExplanationRequest,
            this.handleDiffExplanationRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.diffGroupChangesRequest,
            this.handleDiffGroupChangesRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.diffDescriptionsRequest,
            this.handleDiffDescriptionsRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.canApplyChangesRequest,
            this.handleCanApplyChangesRequestMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.applyChangesRequest,
            this.handleDiffApplyMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.previewApplyChangesRequest,
            this.handlePreviewApplyChangesMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.openFileRequest,
            this.handleOpenFileMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.stashUnstagedChangesRequest,
            this.handleStashUnstagedChangesMessage.bind(this)
        );
        _asyncMsgHandler.registerHandler(
            WebViewMessageType.reportAgentChangesApplied,
            this.handleReportAgentChangesAppliedMessage.bind(this)
        );
    }

    private async streamLlmCompletion(prompt: string) {
        const chatResultIter = await llmCallStream(
            prompt,
            this._api.createRequestId(),
            [],
            [],
            [],
            ChatMode.agent,
            true, // silent
            "gemini-2-flash-001-simple-port"
        );

        for await (const { nodes = [] } of chatResultIter) {
            const rawResponse = nodes.find(
                (node) => node.type === ChatResultNodeType.RAW_RESPONSE
            )?.content;
            if (rawResponse) {
                let parsedResponse = rawResponse.trim();
                // remove starting and trailing ```
                parsedResponse = parsedResponse.replace(/^```|```$/g, "");
                // remove starting json
                parsedResponse = parsedResponse.replace(/^json/g, "");
                return parsedResponse;
            }
        }
        return "";
    }

    private async handleDiffExplanationRequestMessage(message: DiffExplanationRequestMessage) {
        const { changedFiles } = message.data;
        const streamLlmCompletion = this.streamLlmCompletion.bind(this);
        try {
            const response = await getDiffExplanation(changedFiles, streamLlmCompletion);
            return {
                type: WebViewMessageType.diffExplanationResponse,
                data: { explanation: response },
            } as DiffExplanationResponseMessage;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to explain diff:", error);
            return {
                type: WebViewMessageType.diffExplanationResponse,
                data: { explanation: [] },
            } as DiffExplanationResponseMessage;
        }
    }

    // First step: Group changes by file using LLM
    private async handleDiffGroupChangesRequestMessage(message: DiffGroupChangesRequestMessage) {
        const { changedFiles, changesById } = message.data;
        const streamLlmCompletion = this.streamLlmCompletion.bind(this);
        try {
            // Use LLM to group changes by file
            // If changesById is true, we're using the ID-based approach
            const groupedChanges = await groupChangesWithLLM(
                changedFiles,
                streamLlmCompletion,
                !!changesById
            );

            const responseMsg = {
                type: WebViewMessageType.diffGroupChangesResponse,
                data: { groupedChanges },
            } as DiffGroupChangesResponseMessage;
            return responseMsg;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to group changes:", error);
            return {
                type: WebViewMessageType.diffGroupChangesResponse,
                data: { groupedChanges: [] },
            } as DiffGroupChangesResponseMessage;
        }
    }

    // Second step: Get descriptions for grouped changes
    private async handleDiffDescriptionsRequestMessage(message: DiffDescriptionsRequestMessage) {
        const { groupedChanges } = message.data;
        const streamLlmCompletion = this.streamLlmCompletion.bind(this);
        try {
            // Use the utility function to get descriptions
            const { sections, error } = await getDiffDescriptions(
                groupedChanges,
                streamLlmCompletion
            );

            const responseMsg = {
                type: WebViewMessageType.diffDescriptionsResponse,
                data: { explanation: sections, error },
            } as DiffDescriptionsResponseMessage;
            return responseMsg;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to get descriptions:", error);
            return {
                type: WebViewMessageType.diffDescriptionsResponse,
                data: {
                    explanation: [],
                    error: `Failed to get descriptions: ${error instanceof Error ? error.message : String(error)}`,
                },
            } as DiffDescriptionsResponseMessage;
        }
    }

    private async handleCanApplyChangesRequestMessage(
        _: CanApplyChangesRequestMessage
    ): Promise<CanApplyChangesResponseMessage> {
        let error: string | undefined = undefined;
        let hasUnstagedChanges = false;
        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (workspaceFolder) {
                const execOptions = { cwd: workspaceFolder.uri.fsPath };
                const unstagedChanges = await executeCommand("git status --porcelain", execOptions);
                if (unstagedChanges) {
                    hasUnstagedChanges = unstagedChanges.trim().split("\n").length > 0;
                }
            } else {
                error = "No workspace folder available";
            }
        } catch (err) {
            const message = `Failed to check for unstaged changes: ${err instanceof Error ? err.message : String(err)}`;
            // eslint-disable-next-line no-console
            console.error(message);
            error = message;
        }

        const canApply = !error && !hasUnstagedChanges;

        return {
            type: WebViewMessageType.canApplyChangesResponse,
            data: { canApply, hasUnstagedChanges, error },
        };
    }

    private async handleDiffApplyMessage(
        message: ApplyChangesRequestMessage
    ): Promise<ApplyChangesResponseMessage> {
        const { path: filePath, originalCode, newCode } = message.data;

        try {
            // Validate inputs
            if (!filePath?.trim()) {
                throw new Error("File path cannot be empty");
            }
            if (typeof newCode !== "string") {
                throw new Error("New code must be a string");
            }

            // Resolve file path
            const absolutePath: string = await this.resolveFilePath(filePath);
            const uri: vscode.Uri = vscode.Uri.file(absolutePath);

            // Check if this is a file deletion (empty newCode)
            if (newCode === "") {
                // Check if file exists before trying to delete it
                try {
                    await workspace.fs.stat(uri);
                    // File exists, delete it
                    await workspace.fs.delete(uri);
                    return {
                        type: WebViewMessageType.applyChangesResponse,
                        data: { success: true },
                    };
                } catch (e) {
                    // File doesn't exist, nothing to delete
                    return {
                        type: WebViewMessageType.applyChangesResponse,
                        data: { success: true },
                    };
                }
            }

            // If we get here, this is a file creation or modification, not a deletion
            const edit: vscode.WorkspaceEdit = new vscode.WorkspaceEdit();
            let wholeFileRange = new vscode.Range(0, 0, 0, 0);

            let currentContents = "";
            let newContents = "";
            let fileExists = false;
            let hasConflicts = false;
            try {
                await workspace.fs.stat(uri);
                fileExists = true;

                // just do the dumb thing for now
                newContents = newCode;

                const document = await workspace.openTextDocument(uri);
                wholeFileRange = new vscode.Range(
                    0,
                    0,
                    document.lineCount - 1,
                    document.lineAt(document.lineCount - 1).text.length
                );

                currentContents = (await workspace.openTextDocument(uri))?.getText() || "";
                const mergeResult = threeWayMerge(originalCode, newCode, currentContents);
                newContents = mergeResult.mergedContent;
                hasConflicts = mergeResult.hasConflicts;
            } catch {
                fileExists = false;
                newContents = newCode;
            }

            if (!fileExists) {
                edit.createFile(uri, { overwrite: true });
            }

            // replace whole file
            edit.replace(uri, wholeFileRange, newContents);

            const success: boolean = await vscode.workspace.applyEdit(edit);
            if (!success) {
                throw new Error("Failed to apply workspace edit");
            }

            // Automatically save the changes once it's finished
            const document = await vscode.workspace.openTextDocument(uri);
            if (!hasConflicts) {
                // Only save the file if there are no conflicts
                await document.save();
            }

            return {
                type: WebViewMessageType.applyChangesResponse,
                data: { success: true, hasConflicts },
            };
        } catch (error) {
            const errorMessage: string = error instanceof Error ? error.message : String(error);
            return {
                type: WebViewMessageType.applyChangesResponse,
                data: {
                    success: false,
                    error: errorMessage,
                },
            };
        }
    }

    private async handlePreviewApplyChangesMessage(
        message: PreviewApplyChangesRequestMessage
    ): Promise<PreviewApplyChangesResponseMessage> {
        const { path: filePath, originalCode, newCode } = message.data;

        try {
            const absolutePath: string = await this.resolveFilePath(filePath);
            const uri: vscode.Uri = vscode.Uri.file(absolutePath);

            let currentContents = "";
            let mergedContent = "";
            let hasConflicts = false;
            try {
                currentContents = (await workspace.openTextDocument(uri))?.getText() || "";
                const mergeResult = threeWayMerge(originalCode, newCode, currentContents);
                mergedContent = mergeResult.mergedContent;
                hasConflicts = mergeResult.hasConflicts;
            } catch {
                mergedContent = newCode;
            }

            return {
                type: WebViewMessageType.previewApplyChangesResponse,
                data: { mergedContent, hasConflicts },
            };
        } catch (error) {
            const errorMessage: string = error instanceof Error ? error.message : String(error);
            return {
                type: WebViewMessageType.previewApplyChangesResponse,
                data: {
                    mergedContent: "",
                    hasConflicts: false,
                    error: errorMessage,
                },
            };
        }
    }

    private async getGitRoot() {
        if (this.gitRoot) {
            return this.gitRoot;
        }
        this.gitRoot = await findGitRoot();
        return this.gitRoot;
    }

    private async handleOpenFileMessage(
        message: OpenFileRequestMessage
    ): Promise<OpenFileResponseMessage> {
        const { path: filePath } = message.data;

        try {
            // Validate inputs
            if (!filePath?.trim()) {
                throw new Error("File path cannot be empty");
            }

            // Resolve file path
            const absolutePath: string = await this.resolveFilePath(filePath);
            const uri: vscode.Uri = vscode.Uri.file(absolutePath);

            // Open the document in the background without stealing focus
            const document = await vscode.workspace.openTextDocument(uri);
            await vscode.window.showTextDocument(document, {
                viewColumn: vscode.ViewColumn.Active,
                preserveFocus: true,
            });

            return {
                type: WebViewMessageType.openFileResponse,
                data: { success: true },
            };
        } catch (error) {
            const errorMessage: string = error instanceof Error ? error.message : String(error);
            return {
                type: WebViewMessageType.openFileResponse,
                data: {
                    success: false,
                    error: errorMessage,
                },
            };
        }
    }

    private async resolveFilePath(filePath: string): Promise<string> {
        const gitRoot = await this.getGitRoot();
        if (gitRoot) {
            return vscode.Uri.file(vscode.Uri.joinPath(vscode.Uri.file(gitRoot), filePath).fsPath)
                .fsPath;
        }

        // Fall back to workspace folder if git root is not available
        const workspaceFolder = workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error("No workspace folder available");
        }
        return vscode.Uri.joinPath(workspaceFolder.uri, filePath).fsPath;
    }

    private async handleStashUnstagedChangesMessage(
        _: StashUnstagedChangesRequestMessage
    ): Promise<StashUnstagedChangesResponseMessage> {
        try {
            const workspaceFolder = workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error("No workspace folder available");
            }
            const execOptions = { cwd: workspaceFolder.uri.fsPath };
            await executeCommand("git stash", execOptions);
            return {
                type: WebViewMessageType.stashUnstagedChangesResponse,
                data: { success: true },
            };
        } catch (error) {
            const errorMessage: string = error instanceof Error ? error.message : String(error);
            return {
                type: WebViewMessageType.stashUnstagedChangesResponse,
                data: {
                    success: false,
                    error: errorMessage,
                },
            };
        }
    }

    /**
     * Report the apply changes event
     */
    private handleReportAgentChangesAppliedMessage(
        _msg: ReportAgentChangesAppliedMessage
    ): EmptyMessage {
        if (this._remoteAgentId) {
            getRemoteAgentSessionEventReporter().reportEvent({
                eventName: RemoteAgentSessionEventName.changesApplied,
                remoteAgentId: this._remoteAgentId,
                eventData: {
                    changesAppliedData: {},
                },
            });
        }
        return {
            type: WebViewMessageType.empty,
        };
    }
}
