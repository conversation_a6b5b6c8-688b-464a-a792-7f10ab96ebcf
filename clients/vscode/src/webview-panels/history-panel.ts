import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentInstruction } from "../code-edit-types";
import { RecentCompletions } from "../completions/recent-completions";
import { WorkTimer } from "../metrics/work-timer";
import { NextEditResultInfo } from "../next-edit/next-edit-types";
import { PanelWebviewBase } from "../utils/panel-webview-base";
import { RecentItems } from "../utils/recent-items";
import { openFileFromMessage } from "../utils/webviews/open-file";
import {
    HistoryCompletionRequest,
    WebViewMessage,
    WebViewMessageType,
} from "../webview-providers/webview-messages";

export class HistoryWebviewPanel extends PanelWebviewBase {
    /**
     * Track the currently panel. Only allow a single panel to exist at a time.
     */
    public static currentPanel: HistoryWebviewPanel | undefined;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _config: AugmentConfigListener,
        private _apiServer: APIServer,
        historyColumn: vscode.ViewColumn,
        private readonly _recentCompletions: RecentCompletions,
        private readonly _recentInstructions: RecentItems<AugmentInstruction>,
        private readonly _recentNextEditResults: RecentItems<NextEditResultInfo>,
        private readonly _workTimer: WorkTimer,
        private readonly _panel = vscode.window.createWebviewPanel(
            "history",
            "Augment History",
            historyColumn
        )
    ) {
        super("history.html", _panel.webview);

        this._panel.iconPath = {
            light: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-light.svg"),
            dark: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-dark.svg"),
        };
        this._panel.onDidDispose(() => {
            this.dispose();
        });
        this.addDisposables(
            this._panel,
            new vscode.Disposable(() => {
                HistoryWebviewPanel.currentPanel = undefined;
            })
        );

        // Listen for new code instructions
        this.addDisposable(
            this._recentInstructions.onNewItems((instruction) => {
                if (!this._panel || !this._panel.visible) {
                    return;
                }

                void this._panel.webview.postMessage({
                    type: WebViewMessageType.instructions,
                    data: [instruction],
                });
            })
        );

        // Listen for next edit locations
        this.addDisposable(
            this._recentNextEditResults.onNewItems((nextEditResults) => {
                if (!this._panel || !this._panel.visible) {
                    return;
                }

                void this._panel.webview.postMessage({
                    type: WebViewMessageType.nextEditSuggestions,
                    data: nextEditResults,
                });
            })
        );

        // Listen for changes to the completion history
        this.addDisposable(
            this._recentCompletions.onNewItems(async (_completionRequest) => {
                await this.sendCompletions();
            })
        );

        // Listen for config changes
        this.addDisposable(
            this._config.onDidChange(async () => {
                await this.sendConfig();
            })
        );

        this._panel.webview.onDidReceiveMessage(async (message: WebViewMessage) => {
            await this._workTimer.runTimed(message.type, async () => {
                await this._handleMessage(message);
            });
        });
        void this.loadHTML(_extensionUri);
    }

    // This function ensures that the messages we send match the type
    // definitions of WebViewMessage. The VSCode API of postMessage(msg: any)
    // does not enforce this.
    private async _postMessage(msg: WebViewMessage) {
        if (!this._panel || !this._panel.visible) {
            return;
        }

        await this._panel.webview.postMessage(msg);
    }

    private async sendCompletions() {
        await this._postMessage({
            type: WebViewMessageType.completions,
            data: this._getCompletions(),
        });
    }

    private _getCompletions(): HistoryCompletionRequest[] {
        return this._recentCompletions.items.map((completionRequest) => {
            return {
                occuredAt: completionRequest.occuredAt.toISOString(),
                requestId: completionRequest.requestId,
                repoRoot: completionRequest.repoRoot,
                pathName: completionRequest.pathName,
                prefix: completionRequest.prefix,
                completions: completionRequest.completions.map((completion) => {
                    return {
                        text: completion.completionText,
                        skippedSuffix: completion.skippedSuffix,
                        suffixReplacementText: completion.suffixReplacementText,
                    };
                }),
                suffix: completionRequest.suffix,
            };
        });
    }

    private async sendConfig() {
        await this._postMessage({
            type: WebViewMessageType.historyConfig,
            data: this._config.config,
        });
    }

    private async _handleMessage(message: WebViewMessage) {
        if (!this._panel || !this._panel.visible) {
            return;
        }

        switch (message.type) {
            case WebViewMessageType.historyLoaded:
                await this._postMessage({
                    type: WebViewMessageType.historyInitialize,
                    data: {
                        config: this._config.config,
                        completionRequests: this._getCompletions(),
                        instructions: this._recentInstructions.items.slice(),
                        nextEdits: this._recentNextEditResults.items.slice(),
                    },
                });
                break;
            case WebViewMessageType.copyRequestID:
                await vscode.env.clipboard.writeText(message.data);
                await vscode.window.showInformationMessage("Copied request ID to clipboard");
                break;
            case WebViewMessageType.openFile: {
                void openFileFromMessage(message.data, this._panel.viewColumn);
                break;
            }
            case WebViewMessageType.completionRating: {
                let success = true;
                try {
                    await this._apiServer.completionFeedback(message.data);
                } catch (e) {
                    success = false;
                    void vscode.window.showErrorMessage(
                        `Failed to submit feedback: ${(e as Error).message}`
                    );
                } finally {
                    void this._panel.webview.postMessage({
                        type: WebViewMessageType.completionRatingDone,
                        data: {
                            success,
                            requestId: message.data.requestId,
                        },
                    });
                }
                break;
            }
            case WebViewMessageType.nextEditRating: {
                let success = true;
                try {
                    await this._apiServer.nextEditFeedback(message.data);
                } catch (e) {
                    success = false;
                    void vscode.window.showErrorMessage(
                        `Failed to submit feedback: ${(e as Error).message}`
                    );
                } finally {
                    void this._panel.webview.postMessage({
                        type: WebViewMessageType.nextEditRatingDone,
                        data: {
                            success,
                            requestId: message.data.requestId,
                        },
                    });
                }
                break;
            }
        }
    }

    public static createOrShow(
        extensionUri: vscode.Uri,
        config: AugmentConfigListener,
        apiServer: APIServer,
        recenyCompletions: RecentCompletions,
        recentInstructions: RecentItems<AugmentInstruction>,
        recentNextEditResults: RecentItems<NextEditResultInfo>,
        workTimer: WorkTimer
    ) {
        if (HistoryWebviewPanel.currentPanel) {
            HistoryWebviewPanel.currentPanel._panel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        HistoryWebviewPanel.currentPanel = new HistoryWebviewPanel(
            extensionUri,
            config,
            apiServer,
            vscode.ViewColumn.Beside,
            recenyCompletions,
            recentInstructions,
            recentNextEditResults,
            workTimer
        );
    }
}
