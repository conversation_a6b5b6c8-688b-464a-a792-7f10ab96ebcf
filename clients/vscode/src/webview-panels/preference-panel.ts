import { retryWithBackoff } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { AugmentChatEntry } from "../chat/chat-types";
import { FuzzyFsSearcher } from "../chat/fuzzy-fs-searcher";
import { getLogger } from "../logging";
import { WorkTimer } from "../metrics/work-timer";
import { PanelWebviewBase } from "../utils/panel-webview-base";
import { AsyncMsgHandler } from "../utils/webviews/messaging";
import { createAsyncMsgHandlerFromWebview } from "../utils/webviews/messaging-helper";
import { openFileFromMessage } from "../utils/webviews/open-file";
import {
    ChatModelReply,
    ExternalSource,
    WebViewMessage,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import {
    ChatPreferenceInput,
    ChatPreferenceResult,
    Pair,
    PreferenceInput,
    PreferenceResult,
} from "./preference-panel-types";

/*
 * After user submits a feedback on which option (A or B) is better,
 * we should keep only that one in the interface.
 */
function isKeepA(rating: string): boolean {
    if (rating.startsWith("A") || rating.startsWith("=")) {
        return true;
    } else if (rating.startsWith("B")) {
        return false;
    } else {
        throw new Error(`Incorrect rating: ${rating}`);
    }
}

/*
 * Converts a string rating to a number.
 * A => negative
 * B => positive
 * = => 0
 */
function overallRatingToNumber(overallRating: string): number {
    let result;

    if (overallRating.startsWith("A")) {
        result = -1 * parseInt(overallRating.charAt(1), 10);
    } else if (overallRating.startsWith("B")) {
        result = parseInt(overallRating.charAt(1), 10);
    } else if (overallRating.startsWith("=")) {
        result = 0;
    } else {
        throw new Error(`Incorrect rating: ${overallRating}`);
    }

    return result;
}

export class PreferenceWebviewPanel extends PanelWebviewBase {
    public static currentPanel: PreferenceWebviewPanel | undefined;
    private readonly _panel: vscode.WebviewPanel;
    private static _logger = getLogger("PreferenceWebviewPanel");
    private readonly _inputData: PreferenceInput;
    private _asyncMsgHandler: AsyncMsgHandler | undefined;

    /*
     * Promise that resolves when the user submits a feedback.
     */
    private _resolvePreference?: (value: PreferenceResult) => void;

    constructor(
        panel: vscode.WebviewPanel,
        private readonly _extensionUri: vscode.Uri,
        private readonly _fuzzyFsSearcher: FuzzyFsSearcher,
        column: vscode.ViewColumn,
        public inputData: PreferenceInput,
        workTimer: WorkTimer,
        private readonly _externalSources?: ExternalSource[],
        private readonly _userSpecifiedExternalSources?: string[]
    ) {
        super("preference.html", panel.webview);

        this._asyncMsgHandler = createAsyncMsgHandlerFromWebview(panel.webview, workTimer);
        this.addDisposable(this._asyncMsgHandler);

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.findFileRequest,
            this._fuzzyFsSearcher.findFiles
        );
        this._asyncMsgHandler.registerHandler(
            // Note(yuri): preference panel is using chat object only to communicate with the backend.
            // So empty chat here is fine.
            WebViewMessageType.chatLoaded,
            (_) => {
                return Promise.resolve({
                    type: WebViewMessageType.chatInitialize,
                    data: {},
                });
            }
        );
        this._panel = panel;
        this._inputData = inputData;
    }

    private handleMessage = (message: WebViewMessage) => {
        if (message.type === WebViewMessageType.preferenceResultMessage) {
            const result: PreferenceResult = message.data;
            this._resolvePreference!(result);
            this._panel.dispose();
        }
        if (message.type === WebViewMessageType.preferenceNotify) {
            void vscode.window.showInformationMessage(message.data);
        }
        if (message.type === WebViewMessageType.preferencePanelLoaded) {
            PreferenceWebviewPanel._logger.info("Preference panel loaded, sending input data");
            void this._panel.webview.postMessage({
                type: WebViewMessageType.preferenceInit,
                data: this._inputData,
            });
        }
        if (message.type === WebViewMessageType.openFile) {
            void openFileFromMessage(message.data);
        }
    };

    public static async launchStandalonePreferencePanel(
        extensionUri: vscode.Uri,
        pair: Pair<AugmentChatEntry>,
        enableRetrievalDataCollection: boolean,
        fuzzyFsSearcher: FuzzyFsSearcher,
        externalSources: ExternalSource[] = [],
        userSpecifiedExternalSources: string[] = [],
        workTimer: WorkTimer
    ): Promise<PreferenceWebviewPanel> {
        if (PreferenceWebviewPanel.currentPanel) {
            PreferenceWebviewPanel.currentPanel.dispose();
            PreferenceWebviewPanel.currentPanel = undefined;
        }
        const currentColumn = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        const column = currentColumn || vscode.ViewColumn.One;

        const data: ChatPreferenceInput = {
            type: "Chat",
            data: pair,
            enableRetrievalDataCollection,
        };

        // Create a new webview instead of using the chat webview
        const panel = vscode.window.createWebviewPanel("preference", "Augment Preference", column, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [vscode.Uri.joinPath(extensionUri, "/common-webviews")],
        });

        const currentPanel = (PreferenceWebviewPanel.currentPanel = new PreferenceWebviewPanel(
            panel,
            extensionUri,
            fuzzyFsSearcher,
            column,
            data,
            workTimer,
            externalSources,
            userSpecifiedExternalSources
        ));

        // Add this line to await HTML loading
        await currentPanel.loadHTML(extensionUri);

        panel.onDidDispose(() => {
            if (currentPanel._resolvePreference) {
                currentPanel._resolvePreference(null);
            }
            currentPanel.dispose();
        });

        currentPanel.addDisposables(
            panel,
            new vscode.Disposable(() => {
                PreferenceWebviewPanel.currentPanel = undefined;
            })
        );
        panel.webview.onDidReceiveMessage(currentPanel.handleMessage);

        return PreferenceWebviewPanel.currentPanel;
    }

    public async postStreamChunk(message: ChatModelReply, stream: "A" | "B"): Promise<void> {
        await this._panel.webview.postMessage({ ...message, stream });
    }

    public async postStreamDone(): Promise<void> {
        await this._panel.webview.postMessage({
            type: WebViewMessageType.chatStreamDone,
        });
    }

    public async getResult(
        requestIds: Pair<string>,
        apiServer: APIServer,
        modelIdPair: Pair<string>
    ): Promise<boolean> {
        const result = await new Promise<ChatPreferenceResult | null>((resolve) => {
            this._resolvePreference = resolve;
        });

        if (!result) {
            return true; // Default to keeping A if no result
        }

        await this.sendResultToBackend(requestIds, apiServer, modelIdPair, result);

        return isKeepA(result.overallRating);
    }

    private async sendResultToBackend(
        requestIds: Pair<string>,
        apiServer: APIServer,
        modelIdPair: Pair<string>,
        result: ChatPreferenceResult
    ): Promise<void> {
        let textFeedback = `${result.textFeedback}\nMODEL_IDS_START_LABEL\n${modelIdPair?.a}\n${modelIdPair?.b}\nMODEL_IDS_END_LABEL\nIMPLICIT_EXTERNAL_SOURCES_START_LABEL\n${
            this._externalSources?.map((source) => source.name).join(", ") ?? ""
        }\nIMPLICIT_EXTERNAL_SOURCES_END_LABEL\nUSER_SPECIFIED_EXTERNAL_SOURCES_START_LABEL\n${
            this._userSpecifiedExternalSources?.join(", ") ?? ""
        }\nUSER_SPECIFIED_EXTERNAL_SOURCES_END_LABEL\n`;
        // Send result to the backend
        await retryWithBackoff<void>(async () => {
            try {
                /* eslint-disable @typescript-eslint/naming-convention */
                return await apiServer.recordPreferenceSample({
                    request_ids: [requestIds.a, requestIds.b],
                    scores: {
                        overallRating: overallRatingToNumber(result.overallRating),
                        formattingRating: overallRatingToNumber(result.formattingRating),

                        // Hallucination question is disabled for now
                        // hallucinationRating: overallRatingToNumber(result.hallucinationRating),

                        instructionFollowingRating: overallRatingToNumber(
                            result.instructionFollowingRating
                        ),
                        isHighQuality: result.isHighQuality ? 1 : 0,
                    },
                    feedback: textFeedback,
                });
                /* eslint-enable @typescript-eslint/naming-convention */
            } catch (e: any) {
                PreferenceWebviewPanel._logger.error(`Error reporting preference sample: ${e}`);
                throw e;
            }
        }, PreferenceWebviewPanel._logger);
    }
}
