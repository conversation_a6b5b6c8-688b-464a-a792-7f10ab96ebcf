import { As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "$vscode/src/utils/webviews/messaging";
import { WebviewManager } from "$vscode/src/webview-providers/webview-manager";
import {
    type EmptyMessage,
    type GetSharedWebviewStateMessage,
    type GetSharedWebviewStateResponseMessage,
    type UpdateSharedWebviewStateMessage,
    WebViewMessage,
    WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";

import { Disposable, Webview } from "vscode";

/**
 * An in-memory store that can be used to share state between webviews.
 *
 * Works with the SharedWebviewStoreModel in the webview to provide a
 * reactive store that can be used to share state between webviews.
 *
 * Best practices:
 *  - Always use getStore() instead of creating new instances directly
 *  - Always unsubscribe when a webview is disposed to prevent memory leaks
 *  - Use typed state interfaces
 */
export class SharedWebviewStore<T> implements Disposable {
    static instances: Map<string, SharedWebviewStore<unknown>> = new Map();
    static getStore<T>(storeName: string, initialState?: T): SharedWebviewStore<T> {
        let store = SharedWebviewStore.instances.get(storeName);
        if (!store) {
            store = new SharedWebviewStore<T>(storeName, initialState);
        }
        if (initialState && !store.state) {
            store.setState(initialState);
        }
        return store as SharedWebviewStore<T>;
    }

    private _state: T | undefined = undefined;
    private _disposed: boolean = false;
    private _webviewManager: WebviewManager = WebviewManager.getInstance();

    // "subscribers" here are just the ids of webviews subscribed to the store. We keep them
    // to track the references to the store so we know when it's safe to dispose it
    private _subscribers: Set<string> = new Set();

    // Track disposers for registered handlers so we can clean up when the store is disposed
    private _disposers: Array<() => void> = [];

    constructor(
        private _storeName: string,
        initialState?: T
    ) {
        if (initialState) {
            this._state = initialState;
        }
        SharedWebviewStore.instances.set(_storeName, this);
        this.notifySubscribers();
    }

    get state(): T | undefined {
        return this._state;
    }

    isDisposed(): boolean {
        return this._disposed;
    }

    private notifySubscribers(): void {
        if (this._disposed || !this._state) {
            return;
        }

        this._webviewManager.broadcastMessage({
            type: WebViewMessageType.updateSharedWebviewState,
            data: this._state,
            id: this._storeName,
        } as UpdateSharedWebviewStateMessage<T>);
    }

    /**
     * Subscribes a webview to the store using the provided AsyncMsgHandler. When the store's
     * state changes, the webview will receive an updateSharedWebviewState message.
     *
     * @param messengerId The unique identifier for the webview
     * @param webview The webview to listen for messages on
     */
    subscribe(messengerId: string, webview: Webview, handler: AsyncMsgHandler): void {
        if (this._disposed) {
            return;
        }

        const postMessageFn = async (msg: WebViewMessage) => {
            await webview.postMessage(msg);
        };
        this._webviewManager.registerMessageHandler(messengerId, postMessageFn);
        this._subscribers.add(messengerId);

        const onMsgDisposable = webview.onDidReceiveMessage((msg: WebViewMessage) => {
            switch (msg.type) {
                case WebViewMessageType.updateSharedWebviewState:
                    if (this._disposed || msg.id !== this._storeName) {
                        return Promise.resolve({ type: WebViewMessageType.empty });
                    }
                    this._state = msg.data as T | undefined;
                    this.notifySubscribers();
                    break;
                default:
                    break;
            }
        });

        const unregisterGetStateHandler = handler.registerHandler<
            GetSharedWebviewStateMessage,
            GetSharedWebviewStateResponseMessage<T> | EmptyMessage
        >(WebViewMessageType.getSharedWebviewState, (msg: GetSharedWebviewStateMessage) => {
            if (this._disposed || msg.id !== this._storeName) {
                return Promise.resolve({ type: WebViewMessageType.empty }) as Promise<EmptyMessage>;
            }
            return Promise.resolve({
                type: WebViewMessageType.getSharedWebviewStateResponse,
                data: this._state,
                id: this._storeName,
            }) as Promise<GetSharedWebviewStateResponseMessage<T>>;
        });

        this._disposers.push(() => {
            onMsgDisposable.dispose();
        }, unregisterGetStateHandler);

        if (!this._disposed) {
            void postMessageFn({
                type: WebViewMessageType.updateSharedWebviewState,
                data: this._state,
                id: this._storeName,
            } as UpdateSharedWebviewStateMessage<T>);
        }
    }

    unsubscribe(messengerId: string): void {
        if (this._disposed) {
            return;
        }
        this._webviewManager.unregisterMessageHandler(messengerId);

        this._subscribers.delete(messengerId);
        // Once the reference count reaches 0, we should dispose the store
        if (this._subscribers.size === 0) {
            this.dispose();
        }
    }

    setState(state: T): void {
        if (this._disposed) {
            return;
        }
        this._state = state;
        this.notifySubscribers();
    }

    updateState(updater: (state: T | undefined) => T | undefined): void {
        if (this._disposed) {
            return;
        }
        const newState = updater(this._state);
        if (newState === undefined) {
            return;
        }
        this._state = newState;
        this.notifySubscribers();
    }

    dispose(): void {
        if (this._disposed) {
            return;
        }
        this._disposed = true;

        SharedWebviewStore.instances.delete(this._storeName);
        this._state = undefined;
        this._subscribers.clear();
        this._disposers.forEach((d) => d());
    }
}
