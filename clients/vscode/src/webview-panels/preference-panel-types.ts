import { AugmentChatEntry } from "../chat/chat-types";

/**
 * This file contains types shared/used by webviews.
 */

export type PreferenceInput = ChatPreferenceInput;
export type PreferenceResult = ChatPreferenceResult | null;

export type ChatPreferenceResult = {
    overallRating: string;
    formattingRating: string;
    hallucinationRating: string;
    instructionFollowingRating: string;
    textFeedback: string;
    isHighQuality: boolean;
};
export interface ChatPreferenceInput {
    type: "Chat";
    data: Pair<AugmentChatEntry>;
    enableRetrievalDataCollection: boolean;
}

export type Pair<T extends AugmentChatEntry | string> = {
    a: T;
    b: T;
};

export interface EloModelConfiguration {
    highPriorityModels: [string, string][] | string[]; // Pairs of models to compare | List of models to compare
    regularBattleModels: string[];
    highPriorityThreshold: number;
}
