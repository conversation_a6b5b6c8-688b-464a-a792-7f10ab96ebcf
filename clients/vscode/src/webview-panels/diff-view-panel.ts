import { diffLines } from "diff";
import path from "node:path";
import * as vscode from "vscode";

import { type ChatInstructionStreamResult } from "../augment-api";
import { GuidelinesWatcher } from "../chat/guidelines-watcher";
import {
    DiffViewController,
    DiffViewControllerDeps,
    DiffViewControllerOptions,
} from "../diff-view/diff-view-controller";
import { SessionOrigin } from "../diff-view/session-reporter";
import { getLogger } from "../logging";
import {
    addDiffDecorations,
    autoRemoveDecorations,
    removeDecorationsOnEdit,
} from "../utils/diff-decorations";
import { applyInstructionEdits } from "../utils/edits/diff-by-line";
import { PanelWebviewBase } from "../utils/panel-webview-base";
import {
    DiffViewDiffStreamChunkData,
    DiffViewResolveData,
    DiffViewResolveType,
    FileDetails,
    WebViewMessageType,
} from "../webview-providers/webview-messages";

export type DiffViewPanelDeps = Omit<DiffViewControllerDeps, "panel" | "logger">;
export type DiffViewPanelOptions = {
    disableResolution?: boolean;
    viewOptions?:
        | vscode.ViewColumn
        | { readonly viewColumn: vscode.ViewColumn; readonly preserveFocus?: boolean };
    guidelinesWatcher?: GuidelinesWatcher;
    onDispose?: () => void;
} & Omit<DiffViewControllerOptions, "onUserTriggeredClose">;
export class DiffViewWebviewPanel extends PanelWebviewBase {
    private static viewType: string = "augmentDiffView";
    private logger = getLogger("DiffViewWebviewPanel");
    public static currentPanel: DiffViewWebviewPanel | undefined;

    private _controller: DiffViewController;
    public get diffViewController() {
        return this._controller;
    }

    constructor(
        private readonly _deps: DiffViewPanelDeps,
        private _opts: DiffViewPanelOptions,
        private _panel = vscode.window.createWebviewPanel(
            DiffViewWebviewPanel.viewType,
            "Augment",
            _opts.viewOptions ?? vscode.ViewColumn.Active,
            {
                retainContextWhenHidden: true,
                enableScripts: true,
            }
        )
    ) {
        super("diff-view.html", _panel.webview);

        this._panel.iconPath = {
            light: vscode.Uri.joinPath(this._deps.extensionUri, "media", "panel-icon-light.svg"),
            dark: vscode.Uri.joinPath(this._deps.extensionUri, "media", "panel-icon-dark.svg"),
        };
        this._controller = this._updateOpts(this._opts);
        this._panel.onDidDispose(() => {
            this.dispose();
        });
        this.addDisposables(
            this._controller,
            this._panel,
            new vscode.Disposable(() => {
                DiffViewWebviewPanel.currentPanel = undefined;
            })
        );
        void this.loadHTML(_deps.extensionUri);
    }

    public dispose = () => {
        // Call the onDispose callback if provided
        this._opts.onDispose?.();

        this._controller.dispose();
        this._panel.dispose();
        DiffViewWebviewPanel.currentPanel = undefined;
        super.dispose();
    };

    private _updateOpts = (options: DiffViewPanelOptions): DiffViewController => {
        this._opts = options;
        this._controller?.dispose();
        this._controller = new DiffViewController(
            this._panel,
            {
                ...this._deps,
                panel: this._panel,
                logger: this.logger,
            },
            {
                ...this._opts,
                onUserTriggeredClose: () => this.dispose(),
            },
            this._deps.workTimer
        );
        return this._controller;
    };

    public static get controller() {
        return this.currentPanel?.diffViewController;
    }

    public static get currentDocument() {
        return this.currentPanel?._opts.document;
    }

    public static get isVisible() {
        return this.currentPanel?._panel.visible;
    }

    public static startStream = (
        s: AsyncGenerator<DiffViewDiffStreamChunkData>,
        requestId: string,
        origin?: SessionOrigin
    ) => {
        this.currentPanel?.diffViewController.startStream(s, requestId, origin);
    };

    // Using the utility function from utils/diff-decorations.ts

    public static async instantApply(
        _deps: DiffViewPanelDeps,
        options: DiffViewPanelOptions,
        s: AsyncGenerator<ChatInstructionStreamResult>
    ) {
        const document = options.document;
        const targetFileContent = document.originalCode;
        const editInstructionStream = s;
        const targetFilePath = document.absPath;
        let resultContents = "";
        for await (const chunk of applyInstructionEdits(
            targetFileContent ?? "",
            editInstructionStream
        )) {
            resultContents = chunk; // Use the latest chunk as the final result
        }

        // Open the file
        const doc = await vscode.workspace.openTextDocument(targetFilePath);
        // Apply the edit
        const edit = new vscode.WorkspaceEdit();
        edit.replace(doc.uri, new vscode.Range(0, 0, doc.lineCount, 0), resultContents);
        await vscode.workspace.applyEdit(edit);
        // Show the file
        const editor = await vscode.window.showTextDocument(doc);

        // Add decorations to highlight changes and scroll to the first change
        const decorations = addDiffDecorations(editor, targetFileContent ?? "", resultContents, {
            scrollToFirstDecoration: true,
            revealType: vscode.TextEditorRevealType.InCenterIfOutsideViewport,
        });

        // Automatically remove decorations after a delay (30 seconds)
        autoRemoveDecorations(decorations, { minDelayMs: 30000 });

        // Also remove decorations when the user edits or saves the file
        removeDecorationsOnEdit(editor, decorations);

        // Report the event
        const changes = diffLines(targetFileContent ?? "", resultContents);
        const diff: FileDetails[] = changes.map((change) => ({
            repoRoot: document.filePath.rootPath,
            pathName: document.filePath.relPath,
            originalCode: change.removed ? change.value : "",
            modifiedCode: change.added ? change.value : "",
        }));
        const diffViewResolveData: DiffViewResolveData = {
            file: {
                repoRoot: document.filePath.rootPath,
                pathName: document.filePath.relPath,
                originalCode: targetFileContent ?? "",
                modifiedCode: resultContents,
            },
            changes: diff,
            resolveType: DiffViewResolveType.accept,
            shouldApplyToAll: true,
        };
        this.controller?.sessionReporter.reportResolution({
            type: WebViewMessageType.diffViewResolveChunk,
            data: diffViewResolveData,
        });

        return resultContents;
    }

    public static createOrShow(deps: DiffViewPanelDeps, options: DiffViewPanelOptions) {
        if (options.document.isEmptyDocument) {
            return;
        } else if (!DiffViewWebviewPanel.currentPanel) {
            DiffViewWebviewPanel.currentPanel = new DiffViewWebviewPanel(deps, options);
            // If there is a current panel, update it with the new options
        } else {
            DiffViewWebviewPanel.currentPanel._updateOpts(options);
        }
        const currentPanel = DiffViewWebviewPanel.currentPanel;

        // Once we reach here, we know
        // 1. We want to show the diff view
        // 2. The document is not empty, so we can go ahead and populate the diff view
        const filePath = options.document.filePath;
        const filename = path.basename(filePath.absPath);
        currentPanel._panel.title = `Augment Diff - ${filename}`;
        currentPanel._panel.reveal();
    }
}
