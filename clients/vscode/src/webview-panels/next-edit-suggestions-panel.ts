import * as vscode from "vscode";

import { getLogger } from "../logging";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { WorkTimer } from "../metrics/work-timer";
import { AfterPreview, Animating, BeforePreview } from "../next-edit/background-state";
import { EditorNextEdits } from "../next-edit/editor-edits";
import { GlobalNextEdits } from "../next-edit/global-edits";
import { NextEditSessionEventName, NextEditSessionEventSource } from "../next-edit/next-edit-types";
import { EditSuggestion, SuggestionManager } from "../next-edit/suggestion-manager";
import { isAccepted, isFreshChange, or, uniqueSuggestions } from "../next-edit/utils";
import { ResolveFileService } from "../resolve-file-service";
import { PanelWebviewBase } from "../utils/panel-webview-base";
import { createAsyncMsgHandlerFromWebview } from "../utils/webviews/messaging-helper";
import {
    type AsyncWebViewMessage,
    type NextEditVSCodeToWebViewMessage,
    type WebViewMessage,
    WebViewMessageType,
} from "../webview-providers/webview-messages";

export class NextEditSuggestionsPanel extends PanelWebviewBase {
    logger = getLogger("NextEditSuggestionsPanel");

    constructor(
        extensionUri: vscode.Uri,
        private readonly _webviewView: vscode.WebviewView,
        _webview: vscode.Webview,
        private readonly _suggestionManager: SuggestionManager,
        private readonly _globalNextEdit: GlobalNextEdits,
        private readonly _editorEditManager: EditorNextEdits,
        private readonly _nextEditSessionEventReporter: NextEditSessionEventReporter,
        private readonly _resolveFileService: ResolveFileService,
        private readonly _nextEditVSCodeToWebviewMessage: vscode.EventEmitter<NextEditVSCodeToWebViewMessage>,
        _workTimer: WorkTimer,
        private readonly _asyncMsgHandler = createAsyncMsgHandlerFromWebview(_webview, _workTimer)
    ) {
        super("next-edit-suggestions.html", _webview);

        void this.loadHTML(extensionUri);
        this.addDisposable(this._asyncMsgHandler);
        this._resolveFileService.register(this._asyncMsgHandler);
        this.addDisposable(
            this._suggestionManager.onSuggestionsChanged((event) => {
                const suggestions = uniqueSuggestions(
                    [this._editorEditManager.state.suggestion]
                        .concat(event.newSuggestions)
                        .concat(this._suggestionManager.getJustAcceptedSuggestions())
                ).filter(or(isFreshChange, isAccepted));

                void this.postMessage({
                    type: WebViewMessageType.nextEditSuggestionsChanged,
                    data: { suggestions },
                });
                _webviewView.badge = {
                    value: suggestions.length,
                    tooltip: "Next Edit Suggestions",
                };
            })
        );

        this.addDisposable(
            new vscode.Disposable(
                this._editorEditManager.addStateListener((state, oldState) => {
                    // We only care about changes to the focused suggestion or changes involving
                    // AfterPreview (since we add its suggestion to the panel).
                    if (
                        state instanceof AfterPreview ||
                        state instanceof BeforePreview ||
                        state instanceof Animating
                    ) {
                        void this.postMessage({
                            type: WebViewMessageType.nextEditPreviewActive,
                            data: state.suggestion,
                        });
                        return;
                    }
                    if (
                        oldState instanceof AfterPreview ||
                        oldState instanceof BeforePreview ||
                        oldState instanceof Animating
                    ) {
                        // dismiss
                        void this.postMessage({
                            type: WebViewMessageType.nextEditDismiss,
                        });
                        return;
                    }
                    if (
                        (state.suggestion?.equals(oldState.suggestion) ??
                            oldState.suggestion === undefined) &&
                        !(state instanceof AfterPreview) &&
                        !(oldState instanceof AfterPreview)
                    ) {
                        return;
                    }

                    const freshAndAcceptedSuggestions = uniqueSuggestions(
                        [this._editorEditManager.state.suggestion]
                            .concat(this._suggestionManager.getActiveSuggestions())
                            .concat(this._suggestionManager.getJustAcceptedSuggestions())
                    ).filter(or(isFreshChange, isAccepted));
                    void this.postMessage({
                        type: WebViewMessageType.nextEditSuggestionsChanged,
                        data: {
                            suggestions: freshAndAcceptedSuggestions,
                        },
                    });
                    void this.postMessage({
                        type: WebViewMessageType.nextEditNextSuggestionChanged,
                        data: state.suggestion,
                    });
                })
            )
        );

        this.addDisposable(
            this._nextEditVSCodeToWebviewMessage.event((msg) => {
                void this.postMessage(msg);
            })
        );

        this.addDisposable(this._webview.onDidReceiveMessage(this.onDidReceiveMessage));

        _webviewView.badge = {
            value: this._suggestionManager.getActiveSuggestions().filter(or(isFreshChange)).length,
            tooltip: "Next Edit Suggestions",
        };

        this._nextEditSessionEventReporter.reportEventWithoutIds(
            NextEditSessionEventName.PanelCreated,
            NextEditSessionEventSource.Unknown
        );
    }

    wrapAsyncMsg<ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        request: AsyncWebViewMessage<ReqT>,
        baseResponse: ResT | null,
        error: string | null = null
    ) {
        return this.postMessage({
            type: WebViewMessageType.asyncWrapper,
            requestId: request.requestId,
            error,
            baseMsg: baseResponse,
        });
    }

    private postMessage = async (msg: WebViewMessage) => {
        // wrapper function just to make logging/debugging easier.
        return this._webview.postMessage(msg);
    };

    private onDidReceiveMessage = async (message: WebViewMessage): Promise<void> => {
        switch (message.type) {
            case WebViewMessageType.nextEditSuggestionsAction:
                if ("accept" in message.data) {
                    await this._editorEditManager.acceptSuggestion(
                        EditSuggestion.from(message.data.accept),
                        NextEditSessionEventSource.NextEditPanelItemClick,
                        undefined /* hideHover */,
                        true /* preserveFocus */
                    );
                    return;
                }
                if ("reject" in message.data) {
                    this._editorEditManager.rejectSuggestion(
                        EditSuggestion.from(message.data.reject),
                        NextEditSessionEventSource.NextEditPanelItemClick
                    );
                    return;
                }
                if ("undo" in message.data && message.data.undo) {
                    this._editorEditManager.undoAcceptSuggestion(
                        EditSuggestion.from(message.data.undo),
                        NextEditSessionEventSource.NextEditPanelItemClick
                    );
                    return;
                }
                if ("acceptAllInFile" in message.data) {
                    if (message.data.acceptAllInFile.length === 0) {
                        void vscode.window.showInformationMessage("No Next Edits to accept.");
                        return;
                    }
                    this._editorEditManager.acceptAllSuggestionsInFile(
                        message.data.acceptAllInFile[0].qualifiedPathName,
                        NextEditSessionEventSource.NextEditPanelItemClick
                    );
                    return;
                }
                if ("rejectAllInFile" in message.data) {
                    if (message.data.rejectAllInFile.length === 0) {
                        void vscode.window.showInformationMessage("No Next Edits to reject.");
                        return;
                    }
                    this._editorEditManager.rejectAllSuggestionsInFile(
                        message.data.rejectAllInFile[0].qualifiedPathName,
                        NextEditSessionEventSource.NextEditPanelItemClick
                    );
                    return;
                }
                if ("undoAllInFile" in message.data) {
                    if (message.data.undoAllInFile.length === 0) {
                        void vscode.window.showInformationMessage("No Next Edits to undo.");
                        return;
                    }
                    this._editorEditManager.undoAllSuggestionsInFile(
                        message.data.undoAllInFile[0].qualifiedPathName,
                        NextEditSessionEventSource.NextEditPanelItemClick
                    );
                    return;
                }
                this.logger.error("Unknown action message: " + JSON.stringify(message));
                return;
            case WebViewMessageType.nextEditDismiss:
                this._editorEditManager.dismiss(
                    NextEditSessionEventSource.NextEditPanelItemClick,
                    true, // hideHover
                    false // clearJustAccepted
                );
                return;
            case WebViewMessageType.nextEditLoaded:
                this._nextEditSessionEventReporter.reportEventWithoutIds(
                    NextEditSessionEventName.PanelOpened,
                    NextEditSessionEventSource.Unknown
                );
                await this.postMessage({
                    type: WebViewMessageType.nextEditSuggestionsChanged,
                    data: {
                        suggestions: this._suggestionManager
                            .getActiveSuggestions()
                            .concat(this._suggestionManager.getJustAcceptedSuggestions())
                            .filter(or(isFreshChange, isAccepted)),
                    },
                });
                void this.postMessage({
                    type: WebViewMessageType.nextEditNextSuggestionChanged,
                    data: this._editorEditManager.state.suggestion,
                });
                // TODO: This can still happen before we've loaded saved edits.
                // This should listen for that from the edit manager.
                this._globalNextEdit.handleWorkspaceEditsAvailable();
                return;
            case WebViewMessageType.nextEditOpenSuggestion:
                await this._editorEditManager.open(EditSuggestion.from(message.data), {
                    shouldAutoApply: false,
                    preserveFocus: true,
                    eventSource: NextEditSessionEventSource.NextEditPanelItemClick,
                    animationDelayMs: 0,
                });
                this._nextEditSessionEventReporter.reportEvent(
                    message.data.requestId,
                    message.data.result.suggestionId,
                    Date.now(),
                    NextEditSessionEventName.PanelSuggestionClicked,
                    NextEditSessionEventSource.Click
                );
                return;
            case WebViewMessageType.nextEditRefreshStarted:
                await this._globalNextEdit.startGlobalQuery();
                await this.postMessage({
                    type: WebViewMessageType.nextEditRefreshFinished,
                });
                return;
            case WebViewMessageType.nextEditCancel:
                void this._globalNextEdit.cancel();
                return;
            case WebViewMessageType.nextEditActiveSuggestionChanged:
                void this.postMessage({
                    type: WebViewMessageType.nextEditActiveSuggestionChanged,
                    data: message.data,
                });
                return;
        }
    };
}
