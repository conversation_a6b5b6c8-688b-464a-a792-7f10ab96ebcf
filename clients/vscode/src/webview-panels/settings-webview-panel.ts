import { AgentGlobalStateKeys } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import {
    MARKDOWN_FILE_ENDINGS,
    RulesService,
} from "@augment-internal/sidecar-libs/src/chat/rules-service";
import {
    getStateForSidecar,
    PluginStateNamespace,
    PluginStateScope,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-state";
import { sendMessageToSidecar } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { OAuthMetadata } from "@augment-internal/sidecar-libs/src/tools/oauth-types";
import { REMOTE_PARTNER_MCP_SERVERS } from "@augment-internal/sidecar-libs/src/tools/partner-remote-mcp";
import {
    ToolDefinitionWithSettings,
    ToolHostName,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { type TerminalSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import * as vscode from "vscode";

import { APIServer, type RevokeToolAccessResult, RevokeToolAccessStatus } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";
import { deleteMcpAccessToken } from "../auth/mcp-oauth-callback";
import {
    onOrientationStatusChanged,
    sendOrientationStatusToWebviews,
} from "../chat/agent-onboarding-orientation";
import { GuidelinesWatcher } from "../chat/guidelines-watcher";
import { RulesWatcher } from "../chat/rules-watcher";
import { RunAgentInitialOrientationCommand } from "../commands/agent";
import { AugmentExtension } from "../extension";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { isExtensionVersionGte } from "../utils/environment";
import { PanelWebviewBase } from "../utils/panel-webview-base";
import { type AsyncMsgHandler } from "../utils/webviews/messaging";
import { createAsyncMsgHandlerFromWebview } from "../utils/webviews/messaging-helper";
import { openFileFromMessage } from "../utils/webviews/open-file";
import {
    type ConfirmationModalResponse,
    type MCPServer,
    type MCPServerHttp,
    type OpenConfirmationModal,
    ShowNotificationMessage,
    StartRemoteMCPAuthRequest,
    type ToolApprovalConfigSetRequest,
    type ToolApprovalLevelGetRequest,
    ToolConfigGetDefinitions,
    type ToolConfigRevokeAccessRequest,
    type ToolConfigStartOAuthRequest,
    type TriggerImportDialogRequestMessage,
    type TriggerImportDialogResponseMessage,
    WebViewMessage,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import { WorkspaceUIModel } from "../workspace/workspace-ui-model";
import {
    ToolConfigError,
    ToolConfigParseError,
    ToolConfigSaveRequest,
    ToolSettings,
} from "./settings-panel-types";
import { ToolConfigStore } from "./stores/tool-config-store";

export class SettingsWebviewPanel extends PanelWebviewBase {
    private static readonly viewType: string = "augmentSettingsPanel";
    private _settingsLogger = getLogger("SettingsWebviewPanel");
    private _asyncMsgHandler: AsyncMsgHandler | undefined;
    public static currentPanel: SettingsWebviewPanel | undefined;

    private _workspaceUiModel: WorkspaceUIModel | null = null;
    private _store: ToolConfigStore;
    private _featureFlagManager: FeatureFlagManager;
    private _rulesWatcher: RulesWatcher;
    private _rulesService: RulesService;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _extension: AugmentExtension,
        private readonly _apiServer: APIServer,
        private readonly _config: AugmentConfigListener,
        private readonly _guidelinesWatcher: GuidelinesWatcher,
        private readonly _authSessionStore: AuthSessionStore,
        private readonly _navigateTo?: string,
        private readonly _panel = vscode.window.createWebviewPanel(
            SettingsWebviewPanel.viewType,
            "Augment Settings",
            vscode.ViewColumn.Active,
            {
                retainContextWhenHidden: true,
                enableScripts: true,
            }
        )
    ) {
        super("settings.html", _panel.webview, _extension.featureFlagManager, _authSessionStore);
        this._store = this._extension.toolConfigStore!;
        this._featureFlagManager = this._extension.featureFlagManager;
        this._rulesWatcher = new RulesWatcher(this._extension.workspaceManager!);
        this._rulesService = new RulesService();

        // RulesWatcher is no longer needed here - rules are handled by sidecar

        this._panel.iconPath = {
            light: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-light.svg"),
            dark: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-dark.svg"),
        };
        this._panel.onDidDispose(() => {
            this.dispose();
        });
        this._asyncMsgHandler = createAsyncMsgHandlerFromWebview(this._panel.webview);

        // Register a sidecar handler for messages handled by sidecar/libs
        this._asyncMsgHandler.registerSidecarHandler((msg, postMessage) => {
            sendMessageToSidecar(msg, postMessage);
        });

        // Register async message handlers
        this._asyncMsgHandler.registerHandler<OpenConfirmationModal, ConfirmationModalResponse>(
            WebViewMessageType.openConfirmationModal,
            this._handleOpenConfirmationModal.bind(this)
        );

        this._asyncMsgHandler.registerHandler<
            TriggerImportDialogRequestMessage,
            TriggerImportDialogResponseMessage
        >(
            WebViewMessageType.triggerImportDialogRequest,
            this._handleTriggerImportDialogAsync.bind(this)
        );

        this.addDisposable(this._asyncMsgHandler);
        this.addDisposables(
            this._panel,
            new vscode.Disposable(() => {
                SettingsWebviewPanel.currentPanel = undefined;
            }),
            onOrientationStatusChanged((status) => {
                // When orientation status changes, send the status to the webview
                void this._postMessage({
                    type: WebViewMessageType.orientationStatusUpdate,
                    data: status,
                });
            }),
            // TODO: replace this wtih sidecar file watcher when it's ready
            this._rulesWatcher.onDidChange(() => {
                // When rules change, send the updated list to the webview
                void this._handleGetRulesList();
            })
        );

        this.addDisposable(
            this._panel.webview.onDidReceiveMessage(async (message: WebViewMessage) => {
                try {
                    await this._extension.workTimer.runTimed(message.type, () =>
                        this._handleMessage(message)
                    );
                } catch (error) {
                    this._handleError(error);
                }
            })
        );

        // Initialize WorkspaceUIModel for handling workspace context requests
        if (this._extension.workspaceManager) {
            this._workspaceUiModel = new WorkspaceUIModel(
                this._extension.workspaceManager,
                this._panel.webview,
                this._extension.featureFlagManager,
                this._extension.workTimer
            );
            this.addDisposable(this._workspaceUiModel);
        }

        void this.loadHTML(_extensionUri);
    }

    public dispose = () => {
        this._panel.dispose();
        SettingsWebviewPanel.currentPanel = undefined;
        super.dispose();
    };

    /**
     * Navigates to a specific section in the settings panel
     * @param section The section to navigate to (e.g., 'userGuidelines')
     */
    public navigateToSection(section: string): void {
        void this._postMessage({
            type: WebViewMessageType.navigateToSettingsSection,
            data: section,
        });
    }

    /**
     * Refreshes the MCP servers list in the UI
     * This method should be called when MCP servers are updated externally
     */
    public async refreshMCPServers(): Promise<void> {
        await this._handleGetStorageValue();
    }

    protected _handleError(error: unknown) {
        if (error instanceof ToolConfigError) {
            void vscode.window.showErrorMessage(error.message);
        } else {
            void vscode.window.showErrorMessage(`Unexpected error: ${getErrmsg(error)}`);
        }
    }

    private async _handleMessage(message: WebViewMessage): Promise<void> {
        const messageType = message.type;
        switch (messageType) {
            case WebViewMessageType.settingsPanelLoaded:
                this._handleSettingsPanelLoaded();
                break;
            case WebViewMessageType.toolConfigLoaded:
                await this._handleConfigLoaded();
                break;
            case WebViewMessageType.toolConfigSave:
                await this._handleConfigSave(message.data);
                break;
            case WebViewMessageType.toolConfigGetDefinitions:
                await this._handleGetDefinitions(message);
                break;
            case WebViewMessageType.toolConfigStartOAuth:
                await this._handleStartOAuth(message.data);
                break;
            case WebViewMessageType.startRemoteMCPAuth:
                await this._handleStartRemoteMCPAuth(message.data);
                break;
            case WebViewMessageType.toolConfigRevokeAccess:
                await this._handleRevokeAccess(message.data);
                break;
            case WebViewMessageType.toolApprovalConfigSetRequest:
                await this._handleToolApprovalConfigSet(message.data);
                break;
            case WebViewMessageType.toolApprovalConfigGetRequest:
                await this._handleToolApprovalConfigGet(message.data);
                break;
            case WebViewMessageType.getStoredMCPServers:
                await this._handleGetStorageValue();
                break;
            case WebViewMessageType.setStoredMCPServers:
                await this._handleSetStorageValue(message.data);
                break;
            case WebViewMessageType.deleteOAuthSession:
                await this._handleDeleteOAuthSession(message.data);
                break;
            case WebViewMessageType.executeInitialOrientation:
                await this._handleExecuteInitialOrientation();
                break;
            case WebViewMessageType.getOrientationStatus:
                this._handleGetOrientationStatus();
                break;
            case WebViewMessageType.updateUserGuidelines:
                GuidelinesWatcher.updateUserGuidelines(message.data);
                break;
            case WebViewMessageType.getTerminalSettings:
                await this._handleGetTerminalSettings();
                break;
            case WebViewMessageType.updateTerminalSettings:
                await this._handleUpdateTerminalSettings(message.data);
                break;

            case WebViewMessageType.signOut:
                await this._authSessionStore.removeSession();
                this.dispose();
                break;
            case WebViewMessageType.openFile:
                await openFileFromMessage(message.data);
                break;
            case WebViewMessageType.showNotification:
                await this._showNotification(message);
                break;
            case WebViewMessageType.openConfirmationModal:
                await this._handleOpenConfirmationModal(message);
                break;
        }
    }

    /**
     * Handle a request for the list of rules
     * TODO: deprecate this when sidecar watcher is ready
     */
    private async _handleGetRulesList(): Promise<void> {
        try {
            const rules = await this._rulesService.loadRules({ includeGuidelines: true });
            await this._postMessage({
                type: WebViewMessageType.getRulesListResponse,
                data: rules,
            });
        } catch (error) {
            this._settingsLogger.error(`Failed to get rules list: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    private _handleSettingsPanelLoaded() {
        setTimeout(() => {
            if (this._navigateTo) {
                // Wait for the panel to be fully loaded before sending the message to navigate
                this.navigateToSection(this._navigateTo);
            }
        }, 500);
    }

    private async _handleConfigLoaded(): Promise<void> {
        try {
            const state = await this._store.get();

            const enableAgentMode = this._getEnableAgentMode();
            const enableInitialOrientation =
                !!this._featureFlagManager.currentFlags.memoriesParams.enable_initial_orientation;

            let hostTools: ToolDefinitionWithSettings[] = [];
            let toolConfigs: ToolSettings[] = [];

            if (enableAgentMode) {
                hostTools = await (this._extension.toolsModel?.getToolStatusForSettingsPanel() ??
                    Promise.resolve([]));

                toolConfigs = state.tools.map((tool) => ({
                    config: JSON.stringify(tool.config, null, 2),
                    isConfigured: tool.isConfigured,
                    name: tool.name,
                }));
            }

            const guidelines = this._guidelinesWatcher.getGuidelinesStates();

            const hasUsedRemoteAgent = await getStateForSidecar().getValue<boolean>(
                PluginStateNamespace.agent,
                AgentGlobalStateKeys.hasEverUsedRemoteAgent,
                PluginStateScope.global
            );

            await this._postMessage({
                type: WebViewMessageType.toolConfigInitialize,
                data: {
                    toolConfigs,
                    hostTools,
                    enableDebugFeatures: this._config.config.enableDebugFeatures,
                    settingsComponentSupported: {
                        workspaceContext: true,
                        mcpServerList: true,
                        mcpServerImport: true,
                        orientation: true,
                        remoteTools: true,
                        userGuidelines: true,
                        terminal: true,
                        rules: this._featureFlagManager.currentFlags.enableRules,
                    },
                    enableAgentMode: enableAgentMode,
                    enableAgentSwarmMode:
                        this._featureFlagManager.currentFlags.enableAgentSwarmMode &&
                        hasUsedRemoteAgent,
                    hasEverUsedRemoteAgent: hasUsedRemoteAgent,
                    enableInitialOrientation: enableInitialOrientation,
                    userTier: this._extension.userTier,
                    userEmail: this._extension.userEmail,
                    guidelines,
                },
            });

            // Also load MCP servers when the panel is initialized
            await this._handleGetStorageValue();
        } catch (error) {
            this._handleError(error);
        }
    }

    private async _handleConfigSave(data: ToolConfigSaveRequest): Promise<void> {
        try {
            const state = await this._store.get();

            // Parse the JSON configuration
            let parsedConfig: Record<string, unknown>;
            try {
                parsedConfig = JSON.parse(data.toolConfig) as Record<string, unknown>;
            } catch (parseError) {
                this._settingsLogger.error(`Failed to parse tool config: ${getErrmsg(parseError)}`);
                throw new ToolConfigParseError(getErrmsg(parseError));
            }

            const setting = {
                config: parsedConfig,
                isConfigured: data.isConfigured,
                name: data.toolName,
            };

            // Find existing tool or add new one
            const toolIndex = state.tools.findIndex((t) => t.name === data.toolName);
            if (toolIndex >= 0) {
                state.tools[toolIndex] = setting;
            } else {
                state.tools.push(setting);
            }

            // Save to local storage
            await this._store.save(state);
        } catch (error) {
            this._settingsLogger.error(`Error saving tool configuration: ${getErrmsg(error)}`);
            throw new ToolConfigParseError(getErrmsg(error));
        }
    }

    private async _handleGetDefinitions(message?: ToolConfigGetDefinitions): Promise<void> {
        // Default to not using cache if not specified (for backward compatibility)
        const useCache = !!message?.data?.useCache;

        let hostTools: ToolDefinitionWithSettings[] = [];
        if (this._getEnableAgentMode()) {
            hostTools = await (this._extension.toolsModel?.getToolStatusForSettingsPanel(
                useCache
            ) ?? Promise.resolve([]));
        }

        await this._postMessage({
            type: WebViewMessageType.toolConfigDefinitionsResponse,
            data: { hostTools },
        });
    }

    /**
     * Open the URL in the browser for the user to authenticate.
     */
    private async _handleStartOAuth(request: ToolConfigStartOAuthRequest): Promise<void> {
        let success = false;
        try {
            const url = request.authUrl;
            const uri = vscode.Uri.parse(url);
            success = await vscode.env.openExternal(uri);
            if (!success) {
                this._settingsLogger.warn(`Failed to open URL: ${url}`);
            }
        } catch (error) {
            this._settingsLogger.error(`Error opening URL: ${getErrmsg(error)}`);
        } finally {
            await this._postMessage({
                type: WebViewMessageType.toolConfigStartOAuthResponse,
                data: {
                    ok: success,
                },
            });
        }
    }

    private async _handleStartRemoteMCPAuth(request: StartRemoteMCPAuthRequest): Promise<void> {
        try {
            const name = request.name;
            const isPartnerMCP = Object.values(REMOTE_PARTNER_MCP_SERVERS).includes(
                name as REMOTE_PARTNER_MCP_SERVERS
            );

            const server = !isPartnerMCP
                ? await this._ensureOAuthMetadataDiscovered(name)
                : undefined;

            const url = await this._extension.toolsModel?.generateMcpOAuthUrl(
                name,
                server?.oauthMetadata,
                server?.url
            );

            if (!url) {
                this._settingsLogger.error(`Failed to generate OAuth URL for ${name}`);
                await this._postMessage({
                    type: WebViewMessageType.showNotification,
                    data: {
                        type: "error",
                        message: `Authentication failed: Could not generate OAuth URL for ${name}.`,
                    },
                });
                return;
            }

            const uri = vscode.Uri.parse(url);
            const success = await vscode.env.openExternal(uri);
            if (!success) {
                this._settingsLogger.warn(`Failed to open URL: ${url}`);
                await this._postMessage({
                    type: WebViewMessageType.showNotification,
                    data: {
                        type: "warning",
                        message: `Could not open authentication URL. Please try again.`,
                    },
                });
            }
        } catch (error) {
            this._settingsLogger.error(`Error starting MCP OAuth: ${getErrmsg(error)}`);
            await this._postMessage({
                type: WebViewMessageType.showNotification,
                data: {
                    type: "error",
                    message: `Authentication failed: ${getErrmsg(error)}`,
                },
            });
        }
    }

    /**
     * Ensures OAuth metadata is discovered for the specified MCP server.
     * If metadata is not already available, attempts to discover it and updates the server configuration.
     *
     * @param mcpName - The name of the MCP server
     * @returns The MCP server with OAuth metadata if successful, undefined otherwise
     */
    private async _ensureOAuthMetadataDiscovered(
        mcpName: string
    ): Promise<MCPServerHttp | undefined> {
        try {
            // Get current MCP servers
            const mcpServers = await this._store.getMCPServers();
            const mcpServer = mcpServers.find((server) => server.name === mcpName);

            if (!mcpServer) {
                this._settingsLogger.error(`MCP server not found: ${mcpName}`);
                return undefined;
            }

            // Check if it's an HTTP server (only HTTP servers can use OAuth)
            if (mcpServer.type !== "http" && mcpServer.type !== "sse") {
                this._settingsLogger.error(
                    `MCP server ${mcpName} is not an HTTP server and cannot use OAuth`
                );
                return undefined;
            }

            const httpServer = mcpServer;

            // If OAuth metadata is already available, return the server
            if (httpServer.oauthMetadata) {
                this._settingsLogger.info(`OAuth metadata already available for ${mcpName}`);
                return httpServer;
            }

            // Attempt to discover OAuth metadata
            this._settingsLogger.info(
                `Discovering OAuth metadata for ${mcpName} from ${httpServer.url}`
            );
            const oauthMetadata = await this._discoverOAuthMetadata(httpServer.url);

            if (!oauthMetadata) {
                this._settingsLogger.warn(`No OAuth metadata discovered for ${mcpName}`);
                return undefined;
            }

            // Update the server configuration with discovered OAuth metadata
            const updatedServers = mcpServers.map((server) => {
                if (server.name === mcpName && (server.type === "http" || server.type === "sse")) {
                    return {
                        ...server,
                        authRequired: true,
                        oauthMetadata,
                    } as MCPServerHttp;
                }
                return server;
            });

            // Save the updated configuration
            await this._store.saveMCPServers(updatedServers);
            this._settingsLogger.info(`Successfully updated ${mcpName} with OAuth metadata`);

            // Return the updated server
            return updatedServers.find((server) => server.name === mcpName) as MCPServerHttp;
        } catch (error) {
            this._settingsLogger.error(
                `Failed to ensure OAuth metadata for ${mcpName}: ${getErrmsg(error)}`
            );
            return undefined;
        }
    }

    /**
     * Discovers OAuth 2.0 metadata from a server URL.
     * Attempts to fetch metadata from well-known OAuth 2.0 discovery endpoints.
     *
     * @param serverUrl - The base URL of the MCP server
     * @returns OAuth metadata if discovered, undefined otherwise
     */
    private async _discoverOAuthMetadata(serverUrl: string): Promise<OAuthMetadata | undefined> {
        try {
            const baseUrl = new URL(serverUrl);

            // Standard OAuth 2.0 metadata discovery endpoints to try
            const discoveryEndpoints = [
                "/.well-known/oauth-authorization-server",
                "/.well-known/openid-configuration",
                "/oauth/.well-known/oauth-authorization-server",
            ];

            for (const endpoint of discoveryEndpoints) {
                try {
                    const metadataUrl = new URL(endpoint, baseUrl).toString();
                    this._settingsLogger.info(
                        `Attempting OAuth metadata discovery from: ${metadataUrl}`
                    );

                    const response = await fetch(metadataUrl, {
                        method: "GET",
                        headers: {
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            Accept: "application/json",
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            "User-Agent": "Augment-VSCode-Extension/1.0.0",
                        },
                        // Set a reasonable timeout for discovery
                        signal: AbortSignal.timeout(10000),
                    });

                    if (response.ok) {
                        const metadata = (await response.json()) as OAuthMetadata;

                        return metadata;
                    } else {
                        this._settingsLogger.debug(
                            `OAuth metadata discovery failed for ${metadataUrl}: ${response.status}`
                        );
                    }
                } catch (endpointError) {
                    this._settingsLogger.debug(
                        `OAuth metadata discovery error for ${endpoint}: ${getErrmsg(endpointError)}`
                    );
                    // Continue to next endpoint
                }
            }

            // Fallback metadata discovery
            const headers: Record<string, string> = {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                "Content-Type": "application/json",
            };

            const response = await fetch(baseUrl, {
                headers,
                method: "POST",
                body: JSON.stringify({
                    clientId: "augment-vscode-extension",
                }),
            });
            if (!response.ok) {
                const wwwAuthHeader = response.headers.get("WWW-Authenticate");
                const resourceMetadataUrl = wwwAuthHeader?.match(
                    /resource_metadata="([^"]+)"/
                )?.[1];
                if (!resourceMetadataUrl) {
                    return undefined;
                }
                const metadataEndpoint = new URL(
                    "/.well-known/oauth-protected-resource",
                    resourceMetadataUrl
                );
                const protectedResourceResponse = await fetch(metadataEndpoint, {
                    headers,
                    method: "GET",
                });
                if (protectedResourceResponse.ok) {
                    const protectedResourceMetadata =
                        (await protectedResourceResponse.json()) as Record<string, unknown>;
                    if (!Array.isArray(protectedResourceMetadata.authorization_servers)) {
                        return undefined;
                    }
                    const authorizationServers = protectedResourceMetadata.authorization_servers;
                    if (
                        authorizationServers.length === 0 ||
                        typeof authorizationServers[0] !== "string"
                    ) {
                        return undefined;
                    }
                    const authServerUrl = authorizationServers[0];
                    const authMetadataEndpoint = new URL(
                        "/.well-known/oauth-authorization-server",
                        authServerUrl
                    );

                    const authMetadataResponse = await fetch(authMetadataEndpoint);
                    if (!authMetadataResponse.ok) {
                        return undefined;
                    }
                    const metadata = (await authMetadataResponse.json()) as OAuthMetadata;
                    return metadata;
                }
            }

            return undefined;
        } catch (error) {
            this._settingsLogger.error(
                `OAuth metadata discovery failed for ${serverUrl}: ${getErrmsg(error)}`
            );
            return undefined;
        }
    }

    /**
     * Revoke access for a tool.
     */
    private async _handleRevokeAccess(request: ToolConfigRevokeAccessRequest): Promise<void> {
        try {
            // Get the tool definitions to find the tool by its RemoteToolId
            const hostTools = await (this._extension.toolsModel?.getToolStatusForSettingsPanel() ??
                Promise.resolve([]));
            const tool = hostTools.find(
                (t) =>
                    t.identifier.hostName === request.toolId.hostName &&
                    t.identifier.toolId === request.toolId.toolId
            );

            if (tool && tool.identifier.hostName === ToolHostName.remoteToolHost) {
                const toolId = tool.identifier.toolId;
                this._settingsLogger.info(
                    `Revoking access for remote tool: ${tool.definition.name} (${toolId})`
                );

                let result: RevokeToolAccessResult;
                try {
                    result = await this._apiServer.revokeToolAccess(toolId);
                } catch (apiError) {
                    this._settingsLogger.error(`API error revoking access: ${getErrmsg(apiError)}`);
                    void vscode.window.showErrorMessage(
                        `Error revoking access: ${getErrmsg(apiError)}`
                    );
                    return;
                }

                // Show a message based on the result status
                switch (result.status) {
                    case RevokeToolAccessStatus.Success:
                        this._settingsLogger.info(
                            `Successfully revoked access for ${tool.definition.name} (${toolId}).`
                        );
                        await this._handleGetDefinitions();
                        break;
                    case RevokeToolAccessStatus.NotActive:
                        this._settingsLogger.info(
                            `Tool ${tool.definition.name} (${toolId}) has no access to revoke.`
                        );
                        await this._handleGetDefinitions();
                        break;
                    case RevokeToolAccessStatus.Unimplemented:
                        this._settingsLogger.warn(
                            `Revoking access is not implemented for ${tool.definition.name} (${toolId}).`
                        );
                        void vscode.window.showWarningMessage(
                            `Revoking access is not implemented for ${tool.definition.name} (${toolId}).`
                        );
                        break;
                    case RevokeToolAccessStatus.NotFound:
                        throw new ToolConfigError(
                            `Tool not found: ${tool.definition.name} (${toolId}).`
                        );
                    case RevokeToolAccessStatus.Failed:
                        throw new ToolConfigError(
                            `Failed to revoke access for ${tool.definition.name} (${toolId}).`
                        );
                    default:
                        throw new ToolConfigError(
                            `Unknown status (${result.status}) when revoking access for ${tool.definition.name} (${toolId}).`
                        );
                }
            } else {
                throw new ToolConfigError(
                    `Tool not found: ${request.toolId.hostName} ${request.toolId.toolId}`
                );
            }
        } catch (error) {
            this._settingsLogger.error(`Error revoking access: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Delete OAuth session for an MCP server.
     */
    private async _handleDeleteOAuthSession(mcpName: string): Promise<void> {
        try {
            this._settingsLogger.info(`Deleting OAuth session for MCP server: ${mcpName}`);

            // Delete the stored access token
            await deleteMcpAccessToken(mcpName, this._extension.getExtensionContext());

            this._settingsLogger.info(
                `Successfully deleted OAuth session for MCP server: ${mcpName}`
            );

            // Show success notification
            await this._postMessage({
                type: WebViewMessageType.showNotification,
                data: {
                    type: "info",
                    message: `Successfully signed out of ${mcpName}`,
                },
            });
        } catch (error) {
            const errorMessage = getErrmsg(error);
            this._settingsLogger.error(
                `Failed to delete OAuth session for ${mcpName}: ${errorMessage}`
            );

            // Show error notification
            await this._postMessage({
                type: WebViewMessageType.showNotification,
                data: {
                    type: "error",
                    message: `Failed to sign out of ${mcpName}: ${errorMessage}`,
                },
            });
        }
    }

    private async _showNotification(message: ShowNotificationMessage) {
        if (message.data.type === "error") {
            await vscode.window.showErrorMessage(message.data.message);
            return;
        } else if (message.data.type === "warning") {
            await vscode.window.showWarningMessage(message.data.message);
            return;
        } else {
            await vscode.window.showInformationMessage(message.data.message);
        }
    }

    /**
     * Handle changing the approval level for a tool.
     */
    private async _handleToolApprovalConfigSet(
        request: ToolApprovalConfigSetRequest
    ): Promise<void> {
        try {
            this._settingsLogger.verbose(
                `Setting approval config for tool: ${request.toolId.hostName} ${request.toolId.toolId} to ${JSON.stringify(request.approvalConfig)}`
            );

            if (!this._extension.toolApprovalConfigManager) {
                throw new ToolConfigError("Tool approval config manager is not initialized");
            }

            await this._extension.toolApprovalConfigManager.setToolApprovalConfig({
                // Eventually if we support configurations per agent mode, this should be passed from frontend.
                mode: "manual",
                toolId: request.toolId,
                config: request.approvalConfig,
            });

            // We now need to resend the tool definitions to the frontend so that it includes the updated configs.
            // Do this through the get definitions handler.
            await this._handleGetDefinitions();
        } catch (error) {
            this._settingsLogger.error(`Error setting approval level: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle getting the approval level for a tool.
     */
    private async _handleToolApprovalConfigGet(
        request: ToolApprovalLevelGetRequest
    ): Promise<void> {
        try {
            this._settingsLogger.verbose(
                `Getting approval config for tool: ${request.toolId.hostName} ${request.toolId.toolId}`
            );

            if (!this._extension.toolApprovalConfigManager) {
                throw new ToolConfigError("Tool approval config manager is not initialized");
            }

            const config = await this._extension.toolApprovalConfigManager.getToolApprovalConfig({
                // Eventually if we support configurations per agent mode, this should be passed from frontend.
                mode: "manual",
                toolId: request.toolId,
            });

            await this._postMessage({
                type: WebViewMessageType.toolApprovalConfigGetResponse,
                data: config,
            });
        } catch (error) {
            this._settingsLogger.error(`Error getting approval level: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle opening a confirmation modal.
     */
    private async _handleOpenConfirmationModal(
        message: OpenConfirmationModal
    ): Promise<ConfirmationModalResponse> {
        this._settingsLogger.info("Confirmation modal requested: ", message.data.title);
        try {
            const result = await vscode.window.showInformationMessage(
                message.data.message,
                { modal: true }, // Options
                { title: message.data.cancelButtonText, isCloseAffordance: true },
                { title: message.data.confirmButtonText }
            );

            return {
                type: WebViewMessageType.confirmationModalResponse,
                data: {
                    ok: result?.title === message.data.confirmButtonText,
                },
            };
        } catch (error) {
            this._settingsLogger.error(`Error handling confirmation modal: ${getErrmsg(error)}`);

            // Return error response
            return {
                type: WebViewMessageType.confirmationModalResponse,
                data: {
                    ok: false,
                    error: getErrmsg(error),
                },
            };
        }
    }

    /**
     * Get a value from storage
     */
    private async _handleGetStorageValue(): Promise<void> {
        try {
            // For MCP servers, use the ToolConfigStore
            const value = await this._store.getMCPServers();
            await this._postMessage({
                type: WebViewMessageType.getStoredMCPServersResponse,
                data: value,
            });
        } catch (error) {
            this._settingsLogger.error(`Error getting storage value: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Set a value in storage
     */
    private async _handleSetStorageValue(data: MCPServer[]): Promise<void> {
        try {
            // Get the current servers before saving to detect additions
            const currentServers = await this._store.getMCPServers();
            const currentServerIds = new Set(currentServers.map((s) => s.id));

            // Check if this is an addition (new servers exist that weren't there before)
            const hasNewServers = data.some((server) => !currentServerIds.has(server.id));

            // For MCP servers, use the ToolConfigStore
            await this._store.saveMCPServers(data);

            // Wait for the MCP server restart to complete before sending response
            if (this._extension.toolsModel) {
                await new Promise<void>((resolve) => {
                    const disposable = this._extension.toolsModel!.onRestartHosts(() => {
                        disposable.dispose();
                        resolve();
                    });
                });
            }

            // Only send back the updated list if new servers were added
            // This ensures the UI gets the servers with their tools populated
            if (hasNewServers) {
                await this._handleGetStorageValue();
            }
            // For deletions or updates, don't send back the list to avoid race conditions
        } catch (error) {
            this._settingsLogger.error(`Error setting storage value: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    private async _postMessage(msg: WebViewMessage): Promise<boolean> {
        try {
            await this._panel.webview.postMessage(msg);
            return true;
        } catch (error) {
            this._settingsLogger.error(`Failed to post message to webview: ${getErrmsg(error)}`);
            return false;
        }
    }

    private async _handleExecuteInitialOrientation(): Promise<void> {
        try {
            await vscode.commands.executeCommand(RunAgentInitialOrientationCommand.commandID);
        } catch (error) {
            this._settingsLogger.error(
                `Failed to execute initial orientation: ${getErrmsg(error)}`
            );
            this._handleError(error);
        }
    }

    private _getEnableAgentMode(): boolean {
        return isExtensionVersionGte(
            this._featureFlagManager.currentFlags.vscodeAgentModeMinVersion ?? ""
        );
    }

    /**
     * Handle a request for the current orientation status
     */
    private _handleGetOrientationStatus(): void {
        try {
            // Call the function to send orientation status to webviews
            // This will fire an event with the current status
            sendOrientationStatusToWebviews();
        } catch (error) {
            this._settingsLogger.error(`Failed to get orientation status: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle a request for terminal settings
     */
    private async _handleGetTerminalSettings(): Promise<void> {
        try {
            // Get terminal settings from the store
            const settings = await this._store.getTerminalSettings();
            await this._postMessage({
                type: WebViewMessageType.terminalSettingsResponse,
                data: settings,
            });
        } catch (error) {
            this._settingsLogger.error(`Failed to get terminal settings: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle a request to update terminal settings
     */
    private async _handleUpdateTerminalSettings(data: Partial<TerminalSettings>): Promise<void> {
        try {
            // Update the selected shell if provided
            if (data.selectedShell) {
                await this._store.updateSelectedShell(data.selectedShell);
            }

            // Update the startup script if provided
            if (data.startupScript !== undefined) {
                await this._store.updateStartupScript(data.startupScript);
            }

            // Get the updated settings and send them back
            const settings = await this._store.getTerminalSettings();
            await this._postMessage({
                type: WebViewMessageType.terminalSettingsResponse,
                data: settings,
            });
        } catch (error) {
            this._settingsLogger.error(`Failed to update terminal settings: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    // Import directory is now handled by sidecar
    private async _handleTriggerImportDialogAsync(
        _message: TriggerImportDialogRequestMessage
    ): Promise<TriggerImportDialogResponseMessage> {
        try {
            const workspaceRootPath = this._extension.workspaceManager?.getBestFolderRoot();
            if (!workspaceRootPath) {
                this._settingsLogger.warn("No workspace root found for import dialog.");
                void vscode.window.showWarningMessage("No workspace folder open to import into.");
                return {
                    type: WebViewMessageType.triggerImportDialogResponse,
                    data: { selectedPaths: [] },
                } as TriggerImportDialogResponseMessage;
            }
            const workspaceRootUri = vscode.Uri.file(workspaceRootPath);

            const selectedUris = await vscode.window.showOpenDialog({
                canSelectFiles: true,
                canSelectFolders: true,
                canSelectMany: true, // Allows multiple files OR multiple folders
                defaultUri: workspaceRootUri,
                openLabel: "Import",
                filters: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    Markdown: MARKDOWN_FILE_ENDINGS,
                },
                title: "Select files or a folder to import as rules",
            });
            this._settingsLogger.debug(`Selected URIs: ${selectedUris?.length || 0}`);

            if (!selectedUris || selectedUris.length === 0) {
                this._settingsLogger.debug("Import dialog cancelled by user.");
                return {
                    type: WebViewMessageType.triggerImportDialogResponse,
                    data: { selectedPaths: [] },
                };
            }

            // Convert URIs to relative paths
            const selectedPaths: string[] = [];
            for (const uri of selectedUris) {
                const relativePath = vscode.workspace.asRelativePath(uri, false);
                selectedPaths.push(relativePath);
            }

            if (selectedPaths.length === 0) {
                this._settingsLogger.warn("No valid paths selected within workspace.");
                void vscode.window.showWarningMessage("No valid paths selected within workspace.");
            }

            this._settingsLogger.debug(
                `Returning ${selectedPaths.length} selected paths for import`
            );
            const response: TriggerImportDialogResponseMessage = {
                type: WebViewMessageType.triggerImportDialogResponse,
                data: { selectedPaths },
            };
            return response;
        } catch (error) {
            const errorMsg = getErrmsg(error);
            this._settingsLogger.error(`Error in import dialog: ${errorMsg}`);
            void vscode.window.showErrorMessage(`An error occurred during import: ${errorMsg}`);
            return {
                type: WebViewMessageType.triggerImportDialogResponse,
                data: { selectedPaths: [] },
            } as TriggerImportDialogResponseMessage;
        }
    }

    public static createOrShow(
        extensionUri: vscode.Uri,
        extension: AugmentExtension,
        apiServer: APIServer,
        config: AugmentConfigListener,
        guidelinesWatcher: GuidelinesWatcher,
        authSessionStore: AuthSessionStore,
        navigateTo?: string
    ): SettingsWebviewPanel {
        // If we already have a panel, show it
        if (SettingsWebviewPanel.currentPanel) {
            SettingsWebviewPanel.currentPanel._panel.reveal();
            return SettingsWebviewPanel.currentPanel;
        }

        // Otherwise, create a new panel
        SettingsWebviewPanel.currentPanel = new SettingsWebviewPanel(
            extensionUri,
            extension,
            apiServer,
            config,
            guidelinesWatcher,
            authSessionStore,
            navigateTo
        );

        return SettingsWebviewPanel.currentPanel;
    }
}
