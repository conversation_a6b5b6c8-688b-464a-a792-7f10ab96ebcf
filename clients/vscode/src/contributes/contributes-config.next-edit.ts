/* eslint-disable @typescript-eslint/naming-convention */
import { IContributesConfig } from "./contributes-types";

export const nextEditContributes: IContributesConfig = (() => {
    /* List of menu items users see when they click the pencil on the top-right. */
    const nextEditEditorActionSubMenu = [
        {
            command: "vscode-augment.next-edit.background.previous",
            title: "Go to Previous Suggestion",
            when: "vscode-augment.nextEdit.canPrevious && (editorTextFocus || editorHoverFocused || augment-next-edit.active)",
            group: "2_augment@1",
        },
        {
            command: "vscode-augment.next-edit.background.next-forward",
            title: "Go to Next Suggestion",
            when: "vscode-augment.nextEdit.canNext && (editorTextFocus || editorHoverFocused || augment-next-edit.active)",
            group: "2_augment@2",
        },
        {
            command: "vscode-augment.next-edit.open-panel",
            title: "Open Panel",
            when: "vscode-augment.nextEdit.enablePanel",
            group: "2_augment@3",
        },
        {
            command: "vscode-augment.next-edit.force",
            group: "2_augment@4",
            when: "(editorTextFocus || editorHoverFocused || augment-next-edit.active)",
        },
        {
            command: "vscode-augment.next-edit.enable-bg",
            title: "Enable Background Suggestions",
            when: "!vscode-augment.enableNextEditBackgroundSuggestions",
            group: "2_augment@5",
        },
        {
            command: "vscode-augment.next-edit.disable-bg",
            title: "Disable Background Suggestions",
            when: "vscode-augment.enableNextEditBackgroundSuggestions",
            group: "2_augment@5",
        },
        {
            command: "vscode-augment.next-edit.settings",
            title: "Next Edit Settings...",
            group: "2_augment@6",
        },
        {
            command: "vscode-augment.next-edit.learn-more",
            title: "Learn about Next Edit...",
            group: "2_augment@7",
        },
    ].map((item) => ({
        ...item,
        when: item.when
            ? `vscode-augment.enableNextEdit && ${item.when}`
            : "vscode-augment.enableNextEdit",
    }));

    return {
        configuration: [
            {
                title: "Next Edit",
                properties: {
                    "augment.nextEdit.enableBackgroundSuggestions": {
                        type: "boolean",
                        order: 0,
                        default: true,
                        description:
                            "Enable Next Edit to run in the background and suggest changes in the editor.",
                    },
                    "augment.nextEdit.enableGlobalBackgroundSuggestions": {
                        type: "boolean",
                        order: 1,
                        default: false,
                        description:
                            "Enable Next Edit to hint changes in files beyond the active editor tab.",
                    },
                    "augment.nextEdit.enableAutoApply": {
                        type: "boolean",
                        order: 2,
                        default: true,
                        description: "Automatically apply suggestions when you jump to them.",
                    },
                    "augment.nextEdit.showDiffInHover": {
                        type: "boolean",
                        order: 3,
                        default: false,
                        description: "Show a diff of the suggested change in the hover.",
                    },
                    "augment.nextEdit.highlightSuggestionsInTheEditor": {
                        type: "boolean",
                        order: 4,
                        default: false,
                        description:
                            "Highlight all lines with a suggestion in addition to showing gutter icons and gray hint-text.",
                    },
                },
            },
        ],
        commands: [
            {
                category: "Augment",
                command: "vscode-augment.next-edit.force",
                // NOTE(arun): We can't override the title in any menu, so we'll use a
                // more verbose title for things like the right click context menu.
                title: "View Nearby Next Edit Suggestions (Forced)",
                when: "(editorTextFocus || editorHoverFocused)",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.toggle-panel-horizontal-split",
                title: "Toggle Side Panel Split",
                icon: "$(split-horizontal)",
                when: "vscode-augment.nextEdit.enablePanel",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.update",
                title: "Update Next Edit Suggestions",
                icon: {
                    light: "media/next-edit/nextedit-update-light.svg",
                    dark: "media/next-edit/nextedit-update-dark.svg",
                },
            },
            {
                command: "_vscode-augment.next-edit.update.loading",
                title: "Updating Suggestions...",
                icon: {
                    light: "media/next-edit/nextedit-update-loading-light.svg",
                    dark: "media/next-edit/nextedit-update-loading-dark.svg",
                },
            },
            {
                command: "_vscode-augment.next-edit.update.disabled-no-changes",
                title: "No Updates Available",
                icon: {
                    light: "media/next-edit/nextedit-update-disabled-light.svg",
                    dark: "media/next-edit/nextedit-update-disabled-dark.svg",
                },
            },
            {
                command: "_vscode-augment.next-edit.update.disabled-cached",
                title: "Suggestions Up To Date",
                icon: {
                    light: "media/next-edit/nextedit-update-complete-light.svg",
                    dark: "media/next-edit/nextedit-update-complete-dark.svg",
                },
            },
            {
                command: "vscode-augment.next-edit.learn-more",
                title: "Learn about Next Edit...",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.open-panel",
                title: "Open Next Edit Panel",
                when: "vscode-augment.nextEdit.enablePanel",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.accept",
                when: "vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                title: "Accept Suggestion",
            },
            {
                category: "Augment",
                command: "_vscode-augment.next-edit.background.accept-code-action",
                when: "vscode-augment.nextEdit.canAcceptCodeAction",
                title: "Accept Suggestion",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.accept-all",
                when: "vscode-augment.nextEdit.canAcceptAll",
                title: "Accept All Suggestions",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.reject",
                when: "vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                title: "Reject Suggestion",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.reject-all",
                when: "vscode-augment.nextEdit.canRejectAll",
                title: "Reject All Suggestions",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.dismiss",
                when: "vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                title: "Dismiss Suggestion Highlights",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.next", // FKA vscode-augment.next-edit.background.goto-hinting
                when: "vscode-augment.nextEdit.canNextSmart",
                title: "Go to Next Suggestion (Smart)",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.next-forward",
                when: "vscode-augment.nextEdit.canNext",
                title: "Go to Next Suggestion",
                icon: {
                    light: "media/next-edit/right-light-enabled.svg",
                    dark: "media/next-edit/right-dark-enabled.svg",
                },
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.background.previous",
                when: "vscode-augment.nextEdit.canPrevious",
                title: "Go to Previous Suggestion",
                icon: {
                    light: "media/next-edit/left-light-enabled.svg",
                    dark: "media/next-edit/left-dark-enabled.svg",
                },
            },
            {
                category: "Augment",
                command: "_vscode-augment.next-edit.background.next-forward.disabled",
                title: "Go to Next Suggestion",
                icon: {
                    light: "media/next-edit/right-light-disabled.svg",
                    dark: "media/next-edit/right-dark-disabled.svg",
                },
            },
            {
                category: "Augment",
                command: "_vscode-augment.next-edit.background.previous.disabled",
                title: "Go to Previous Suggestion",
                icon: {
                    light: "media/next-edit/left-light-disabled.svg",
                    dark: "media/next-edit/left-dark-disabled.svg",
                },
            },
            {
                category: "Augment",
                command: "_vscode-augment.next-edit.background.open",
                title: "Augment Next Edit: View Suggestion",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.toggle-bg",
                title: "Toggle Background Suggestions",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.enable-bg",
                title: "Enable Background Suggestions",
                when: "!vscode-augment.enableNextEditBackgroundSuggestions",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.disable-bg",
                title: "Disable Background Suggestions",
                when: "vscode-augment.enableNextEditBackgroundSuggestions",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.toggle-all-highlights",
                title: "Toggle Suggestion Highlights",
            },
            {
                category: "Augment",
                command: "vscode-augment.next-edit.settings",
                title: "Next Edit Settings...",
            },
        ].map((command) => ({
            ...command,
            when: command.when
                ? `vscode-augment.enableNextEdit && ${command.when}`
                : "vscode-augment.enableNextEdit",
        })),
        keybindings: [
            // This is only used in the panel.
            {
                command: "vscode-augment.next-edit.background.accept",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "ctrl-y",
                mac: "cmd-shift-z",
                args: "keybinding",
            },
            // We support multiple keybindings to accept a suggestion even though we
            // only display one. This is partly historical precedent--we used to support
            // cmd-enter--but also because different users prefer tab or enter.
            {
                command: "vscode-augment.next-edit.background.accept",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "ctrl-enter",
                mac: "cmd-enter",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.accept",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "enter",
                mac: "enter",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.accept",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAccept && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "tab",
                mac: "tab",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.accept-all",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll && augment-next-edit.active",
                key: "ctrl-alt-enter",
                mac: "cmd-alt-enter",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.reject",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "ctrl-backspace",
                mac: "cmd-backspace",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.reject",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canReject && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "backspace",
                mac: "backspace",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.reject-all",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll && augment-next-edit.active",
                key: "ctrl-alt-backspace",
                mac: "cmd-alt-backspace",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.dismiss",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canDismiss && (((editorTextFocus || editorHoverFocused) && !suggestWidgetVisible) || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel') && !inlineSuggestionVisible && !editorHasSelection && (!vim.active || vim.mode == 'Normal')",
                key: "escape",
                mac: "escape",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.next",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNextSmart",
                key: "ctrl-;",
                mac: "cmd-;",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.next-forward",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canNext",
                key: "ctrl-shift-'",
                mac: "cmd-shift-'",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.background.previous",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canPrevious",
                key: "ctrl-shift-;",
                mac: "cmd-shift-;",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.force",
                when: "vscode-augment.enableNextEdit && (editorTextFocus || editorHoverFocused || activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "ctrl-alt-;",
                mac: "cmd-ctrl-;",
                args: "keybinding",
            },
            {
                command: "vscode-augment.next-edit.open-panel",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel",
                key: "ctrl-'",
                mac: "cmd-'",
                args: "keybinding",
            },
            {
                command: "_vscode-augment.next-edit.undo-accept-suggestion",
                when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canUndoAcceptSuggestion && !editorTextFocus && (activePanel == 'workbench.view.extension.augment-panel' || activeViewlet == 'workbench.view.extension.augment-panel')",
                key: "ctrl-z",
                mac: "cmd-z",
                args: "keybinding",
            },
        ],
        submenus: [
            {
                id: "vscode-augment.next-edit.editor-action-submenu",
                label: "Next Edit",
                icon: {
                    light: "media/next-edit/nextedit-available-light.svg",
                    dark: "media/next-edit/nextedit-available-dark.svg",
                },
            },
            {
                id: "vscode-augment.next-edit.editor-action-submenu.disabled",
                label: "Next Edit",
                icon: {
                    light: "media/next-edit/nextedit-unavailable-light.svg",
                    dark: "media/next-edit/nextedit-unavailable-dark.svg",
                },
            },
            {
                id: "vscode-augment.next-edit.editor-action-submenu.loading",
                label: "Next Edit",
                icon: {
                    light: "media/next-edit/nextedit-loading-light.svg",
                    dark: "media/next-edit/nextedit-loading-dark.svg",
                },
            },
            {
                id: "vscode-augment.next-edit.panel-submenu",
                label: "Next Edit Menu",
                icon: "$(ellipsis)",
            },
        ],
        menus: {
            "view/title": [
                // These menu items show up next to the next edit panel.
                {
                    command: "vscode-augment.next-edit.update",
                    when: "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && !vscode-augment.nextEdit.global.updateCached",
                    group: "navigation@1",
                },
                {
                    command: "_vscode-augment.next-edit.update.loading",
                    when: "view == augment-next-edit && vscode-augment.nextEdit.global.updating",
                    group: "navigation@1",
                },
                {
                    command: "_vscode-augment.next-edit.update.disabled-no-changes",
                    when: "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && !vscode-augment.nextEdit.global.canUpdate",
                    group: "navigation@1",
                },
                {
                    command: "_vscode-augment.next-edit.update.disabled-cached",
                    when: "view == augment-next-edit && !vscode-augment.nextEdit.global.updating && vscode-augment.nextEdit.global.canUpdate && vscode-augment.nextEdit.global.updateCached",
                    group: "navigation@1",
                },
                {
                    submenu: "vscode-augment.next-edit.panel-submenu",
                    when: "view == augment-next-edit",
                    group: "navigation@2",
                },
            ],
            "editor/context": [
                {
                    command: "vscode-augment.next-edit.force",
                    // NOTE(arun): For Reasons, you cannot override the title here.
                    group: "1_modification",
                    when: "vscode-augment.enableNextEdit",
                },
            ],
            "editor/title": [
                {
                    submenu: "vscode-augment.next-edit.editor-action-submenu",
                    group: "navigation@43",
                    when: "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading",
                },
                {
                    submenu: "vscode-augment.next-edit.editor-action-submenu.disabled",
                    group: "navigation@43",
                    when: "vscode-augment.enableNextEdit && !terminalEditorActive && !vscode-augment.nextEdit.canNext && !vscode-augment.nextEdit.loading",
                },
                {
                    submenu: "vscode-augment.next-edit.editor-action-submenu.loading",
                    group: "navigation@43",
                    when: "vscode-augment.enableNextEdit && !terminalEditorActive && vscode-augment.nextEdit.loading",
                },
            ],
            // NOTE(arun): Entries in the `comandPalette` are only required if their
            // `when` conditions differ from those in `commands`.
            commandPalette: [
                {
                    command: "vscode-augment.next-edit.open-panel",
                    when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel",
                },
                {
                    command: "vscode-augment.next-edit.force",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.background.accept",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.background.accept-all",
                    when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canAcceptAll",
                },
                {
                    command: "vscode-augment.next-edit.background.reject",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.background.reject-all",
                    when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.canRejectAll",
                },
                {
                    command: "vscode-augment.next-edit.background.dismiss",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.background.next",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.background.next-forward",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.background.previous",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.toggle-bg",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "vscode-augment.next-edit.toggle-all-highlights",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "_vscode-augment.next-edit.background.accept-code-action",
                    when: "false",
                },
                {
                    command: "vscode-augment.next-edit.toggle-panel-horizontal-split",
                    when: "view == augment-next-edit",
                },
                {
                    command: "vscode-augment.next-edit.update",
                    when: "vscode-augment.enableNextEdit",
                },
                {
                    command: "_vscode-augment.next-edit.update.loading",
                    when: "false",
                },
                {
                    command: "_vscode-augment.next-edit.update.disabled-no-changes",
                    when: "false",
                },
                {
                    command: "_vscode-augment.next-edit.update.disabled-cached",
                    when: "false",
                },
                {
                    command: "vscode-augment.next-edit.learn-more",
                    when: "view == augment-next-edit",
                },
                {
                    command: "_vscode-augment.next-edit.background.open",
                    when: "false",
                },
                {
                    command: "_vscode-augment.next-edit.background.next-forward.disabled",
                    when: "false",
                },
                {
                    command: "_vscode-augment.next-edit.background.previous.disabled",
                    when: "false",
                },
            ],
            "vscode-augment.next-edit.panel-submenu": [
                {
                    command: "vscode-augment.next-edit.background.accept-all",
                    when: "vscode-augment.nextEdit.canAcceptAll",
                    title: "Accept All",
                    group: "2_more@1",
                },
                {
                    command: "vscode-augment.next-edit.background.reject-all",
                    when: "vscode-augment.nextEdit.canRejectAll",
                    title: "Reject All",
                    group: "2_more@2",
                },
                {
                    command: "vscode-augment.next-edit.learn-more",
                    title: "Learn about Next Edit...",
                    group: "2_more@3",
                },
                {
                    command: "vscode-augment.next-edit.settings",
                    title: "Next Edit Settings...",
                    group: "2_more@4",
                },
            ],
            "vscode-augment.next-edit.editor-action-submenu": nextEditEditorActionSubMenu,
            "vscode-augment.next-edit.editor-action-submenu.disabled": nextEditEditorActionSubMenu,
            "vscode-augment.next-edit.editor-action-submenu.loading": nextEditEditorActionSubMenu,
        },
        viewsContainers: {
            panel: [
                {
                    icon: "media/activitybar.svg",
                    id: "augment-panel",
                    title: "Augment Next Edit",
                },
            ],
        },
        views: {
            "augment-panel": [
                {
                    id: "augment-next-edit",
                    name: "Augment Next Edit",
                    type: "webview",
                    when: "vscode-augment.enableNextEdit && vscode-augment.nextEdit.enablePanel",
                },
            ],
        },
    };
})();
