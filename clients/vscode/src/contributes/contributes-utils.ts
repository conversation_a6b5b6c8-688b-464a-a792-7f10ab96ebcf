/* eslint-disable @typescript-eslint/naming-convention */
import { IContributesConfig } from "./contributes-types";

/** Utilities to help write contributes. */

/** Merge multiple objects with array values into one, by concatenating them. */
function mergeItems<T>(...items: Record<string, T[]>[]): Record<string, T[]> {
    const result: Record<string, T[]> = {};
    for (const item of items) {
        for (const key of Object.keys(item)) {
            if (!result[key]) {
                result[key] = [];
            }
            result[key].push(...item[key]);
        }
    }

    return result;
}

function safeMergeObjects<T>(...objects: Record<string, T>[]): Record<string, T> {
    return objects.reduce((acc, obj) => {
        for (const key of Object.keys(obj)) {
            if (acc[key] !== undefined) {
                throw new Error(`Duplicate key ${key} in contributes`);
            }
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            acc[key] = obj[key];
        }
        return acc;
    }, {});
}

/** Combine multiple contribute configs into one. */
export function mergeContributes(...configs: IContributesConfig[]): IContributesConfig {
    const asArray = <T>(x: T | T[]): T[] => (Array.isArray(x) ? x : [x]);

    return configs.reduce((acc, config) => {
        return {
            // WARN(arun): There are additional valid contribute configuration fields
            // not listed here. Please make sure that they are combined correctly when
            // added.
            customEditors: [...(acc.customEditors ?? []), ...(config.customEditors ?? [])],
            configuration: [
                ...asArray(acc.configuration ?? []),
                ...asArray(config.configuration ?? []),
            ],
            commands: [...(acc.commands ?? []), ...(config.commands ?? [])],
            icons: safeMergeObjects(acc.icons ?? {}, config.icons ?? {}),
            keybindings: [...(acc.keybindings ?? []), ...(config.keybindings ?? [])],
            submenus: [...(acc.submenus ?? []), ...(config.submenus ?? [])],
            menus: mergeItems(acc.menus ?? {}, config.menus ?? {}),
            viewsContainers: mergeItems(acc.viewsContainers ?? {}, config.viewsContainers ?? {}),
            views: mergeItems(acc.views ?? {}, config.views ?? {}),
        };
    });
}
