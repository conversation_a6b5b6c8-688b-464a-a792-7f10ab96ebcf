/* eslint-disable @typescript-eslint/naming-convention */
// FIXME: These types are written by hand, so they may not be complete or accurate.

/**
 * A complete interface describing the shape of the `contributes` object
 * in a VS Code extension's package.json.
 *
 * @note These types are written by hand, so they may not be complete or accurate.
 */
export interface IContributesConfig {
    /**
     * Contributes authentication providers for handling auth flows within VS Code.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.authentication
     */
    authentication?: IAuthenticationContribution[];

    /**
     * Contributes special breakpoint support for a language.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.breakpoints
     */
    breakpoints?: IBreakpointsContribution[];

    /**
     * Contributes custom editor colors (for example, for a custom editor).
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.colors
     */
    colors?: IColorContribution[];

    /**
     * Contributes commands to the Command Palette and VS Code UI.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.commands
     */
    commands?: ICommandContribution[];

    /**
     * Contributes configuration schema for user/workspace settings.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.configuration
     */
    configuration?: IConfigurationContribution | IConfigurationContribution[];

    /**
     * Contributes default settings for any configuration key.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.configurationDefaults
     */
    configurationDefaults?: {
        [configKey: string]: any;
    };

    /**
     * Contributes custom editors (for example, to handle specific file formats).
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.customEditors
     */
    customEditors?: ICustomEditorContribution[];

    /**
     * Contributes debuggers for debugging sessions.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.debuggers
     */
    debuggers?: IDebuggerContribution[];

    /**
     * Contributes TextMate tokenization grammars.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.grammars
     */
    grammars?: IGrammarContribution[];

    /**
     * Contributes font icons.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.icons
     */
    icons?: {
        [iconId: string]: IIconContribution;
    };

    /**
     * Contributes file icon themes.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.iconThemes
     */
    iconThemes?: IIconThemeContribution[];

    /**
     * Contributes JSON validation schemas for specific file patterns.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.jsonValidation
     */
    jsonValidation?: IJsonValidationContribution[];

    /**
     * Contributes custom keybindings.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.keybindings
     */
    keybindings?: IKeyBindingContribution[];

    /**
     * Contributes language definitions that Visual Studio Code can recognize and use.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.languages
     */
    languages?: ILanguageContribution[];

    /**
     * Contributes menu items (context menus, command palette, etc.).
     * The object keys correspond to menu locations (e.g. "commandPalette", "explorer/context", etc.).
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.menus
     */
    menus?: {
        [menuId: string]: IMenuItemContribution[];
    };

    /**
     * Contributes Markdown preview custom styles or scripts.
     * @see https://code.visualstudio.com/api/extension-guides/markdown-extension
     */
    "markdown.previewStyles"?: string[];
    "markdown.previewScripts"?: string[];

    /**
     * Contributes problem matchers used for parsing compiler or task output.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.problemMatchers
     */
    problemMatchers?: IProblemMatcherContribution[];

    /**
     * Contributes problem patterns describing the structure of problem lines in task or debug output.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.problemPatterns
     */
    problemPatterns?: IProblemPatternContribution[];

    /**
     * Contributes product icon themes (iconography used throughout VS Code UI).
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.productIconThemes
     */
    productIconThemes?: IProductIconThemeContribution[];

    /**
     * Contributes resource label formatters, which control how resources
     * (e.g. files in the workspace) are displayed in the UI.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.resourceLabelFormatters
     */
    resourceLabelFormatters?: IResourceLabelFormatterContribution[];

    /**
     * Contributes semantic token modifiers.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.semanticTokenModifiers
     */
    semanticTokenModifiers?: ISemanticTokenModifierContribution[];

    /**
     * Contributes semantic token scopes.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.semanticTokenScopes
     */
    semanticTokenScopes?: ISemanticTokenScopeContribution[];

    /**
     * Contributes semantic token types.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.semanticTokenTypes
     */
    semanticTokenTypes?: ISemanticTokenTypeContribution[];

    /**
     * Contributes code snippets for a specific language.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.snippets
     */
    snippets?: ISnippetContribution[];

    /**
     * Contributes submenus for context menus or other menu locations.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.submenus
     */
    submenus?: ISubmenuContribution[];

    /**
     * Contributes task definition types used by task runners.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.taskDefinitions
     */
    taskDefinitions?: ITaskDefinitionContribution[];

    /**
     * Contributes terminal link providers, which enable clickable links in the integrated terminal.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.terminal
     */
    terminal?: ITerminalContribution;

    /**
     * Contributes classic text editor color themes (deprecated in favor of colorThemes).
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.themes
     */
    themes?: IThemeContribution[];

    /**
     * Contributes TypeScript server plugins.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.typescriptServerPlugins
     */
    typescriptServerPlugins?: ITypeScriptServerPluginContribution[];

    /**
     * Contributes views that appear within view containers.
     * Keys are location identifiers such as "explorer", "debug", "scm", etc.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.views
     */
    views?: {
        [location: string]: IViewContribution[];
    };

    /**
     * Contributes containers (like "Panels" or "Activity Bar" views).
     * Keys are location identifiers such as "activitybar", "panel", "scm".
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.viewsContainers
     */
    viewsContainers?: {
        [location: string]: IViewContainerContribution[];
    };

    /**
     * Contributes welcome views for empty view containers or placeholders.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.viewsWelcome
     */
    viewsWelcome?: IViewWelcomeContribution[];

    /**
     * Contributes interactive "walkthrough" experiences, such as getting started guides.
     * @see https://code.visualstudio.com/api/references/contribution-points#contributes.walkthroughs
     */
    walkthroughs?: IWalkthroughContribution[];
}

/**
 * Contribution describing an authentication provider.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.authentication
 */
export interface IAuthenticationContribution {
    /** Unique identifier for the auth provider. */
    id: string;
    /** Display label. */
    label: string;
    /** Whether this is a preview feature. */
    supportsMultipleAccounts?: boolean;
}

/**
 * Contribution describing breakpoints.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.breakpoints
 */
export interface IBreakpointsContribution {
    /** Language ID for which breakpoints are enabled. */
    language: string;
}

/**
 * Contributes a color to the workbench.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.colors
 */
export interface IColorContribution {
    /**
     * The unique identifier of the color.
     */
    id: string;

    /**
     * A human-readable description of the color’s purpose.
     */
    description: string;

    /**
     * Default color values for different themes.
     */
    defaults?: {
        /**
         * Default color for light themes.
         */
        light?: string;

        /**
         * Default color for dark themes.
         */
        dark?: string;

        /**
         * Default color for high contrast themes.
         */
        highContrast?: string;

        /**
         * Default color for high contrast light themes.
         */
        highContrastLight?: string;
    };
}

/**
 * Contribution describing a command.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.commands
 */
export interface ICommandContribution {
    /** Name (identifier) of the command. */
    command: string;
    /** Display title of the command. */
    title: string;
    /** Category string to group commands in the UI (optional). */
    category?: string;
    /**
     * An icon for the command (can be a file path, a theme icon reference, or an object).
     * @example "resources/icons/my-command-icon.svg"
     * @example "$(zap)"
     */
    icon?: string | { light: string; dark: string };
    /**
     * Condition when the command is visible in the UI.
     * If not specified, the command is always visible.
     */
    when?: string;
}

/**
 * Contribution describing configuration (settings).
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.configuration
 */
export interface IConfigurationContribution {
    /** Display name of this configuration category. */
    title?: string;
    /**
     * Settings properties under this configuration category.
     * Each key is a setting name, and the value is the JSON schema definition.
     */
    properties?: {
        [setting: string]: ConfigurationProperty;
    };
}

/**
 * A single configuration property in `configuration.properties`.
 * Schema is simplified here; VS Code uses a full JSON schema.
 */
export interface ConfigurationProperty {
    /** User-facing description of what this setting does. */
    description?: string;
    /** Default value of the setting. */
    default?: any;
    /** Type of the setting (string, boolean, number, array, etc.). */
    type?: string | string[];
    /** Possible allowed values. */
    enum?: any[];
    /** Markdown descriptions of the allowed values. */
    markdownEnumDescriptions?: string[];
    /** Labels for the allowed values. */
    enumItemLabels?: string[];
    /** Additional JSON schema fields can appear here. */
    [key: string]: any;
}

/**
 * Contribution describing a custom editor.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.customEditors
 */
export interface ICustomEditorContribution {
    /** Unique ID for the custom editor. */
    viewType: string;
    /** Display name for the custom editor. */
    displayName: string;
    /** List of filename patterns (globs) to match this custom editor. */
    selector: Array<{
        filenamePattern?: string;
    }>;
    /** Indicates whether the editor is "webview" or "desktop" based. */
    priority?: string;
    /** If `true`, backups for this editor are enabled. */
    supportsMultipleEditorsPerDocument?: boolean;
}

/**
 * Contribution describing a debugger.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.debuggers
 */
export interface IDebuggerContribution {
    /** Debugger type (used to match with "debug.type" in launch configurations). */
    type: string;
    /** Label to show in the UI for this debugger. */
    label: string;
    program?: string;
    runtime?: string;
    /** Array of language IDs typically debugged by this debugger. */
    languages?: string[];
    configurationAttributes?: {
        [type: string]: IDebuggerConfigurationAttribute;
    };
    /** Specifies if the extension is a special type of debug extension. */
    adapterExecutableCommand?: string;
    /** Additional properties (omitted for brevity). */
    [key: string]: any;
}

export interface IDebuggerConfigurationAttribute {
    /** List of required properties for this debugger type. */
    required?: string[];
    /** JSON schema properties for this debugger type. */
    properties?: {
        [property: string]: IDebuggerConfigurationAttributeProperty;
    };
}

export interface IDebuggerConfigurationAttributeProperty {
    /** Type of the property (e.g. "string", "number", "boolean"). */
    type: string | string[];
    /** Default value for the property. */
    default?: any;
    /** Description of the property. */
    description?: string;
    /** List of allowed values for the property. */
    enum?: any[];
    /** Descriptions for each allowed value. */
    enumDescriptions?: string[];
    /** Additional properties (omitted for brevity). */
    [key: string]: any;
}

/**
 * Contribution describing a TextMate grammar.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.grammars
 */
export interface IGrammarContribution {
    /** Path to the TextMate grammar file (relative to the extension folder). */
    path: string;
    /** Scope name of the grammar (e.g. "source.js"). */
    scopeName: string;
    /** Language ID that this grammar is targeting (matches the "languages" contribution ID). */
    language?: string;
    /** List of target scope names to inject this grammar into. */
    injectTo?: string[];
    /**
     * A mapping of scope name to embedded language ID,
     * for syntax highlighting nested code blocks, etc.
     */
    embeddedLanguages?: { [scopeName: string]: string };
    /** TextMate injection selector. */
    injectionSelector?: string;
}

/**
 * Contribution describing icons
 * @see: https://code.visualstudio.com/api/references/contribution-points#contributes.icons
 */
export interface IIconContribution {
    /**
     * A short description for this icon.
     */
    description: string;

    /**
     * Defaults for this icon.
     */
    default: {
        fontPath?: string;
        fontCharacter?: string;
    };
}

/**
 * Contribution describing a file icon theme.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.iconThemes
 */
export interface IIconThemeContribution {
    /** Unique identifier for the file icon theme. */
    id: string;
    /** Display name for the icon theme. */
    label: string;
    /**
     * A description of the icon theme.
     * This will be shown in the quick pick when selecting an icon theme.
     */
    description?: string;
    /** Path to the icon theme definition file (relative to the extension folder). */
    path: string;
}

/**
 * Contribution describing a JSON validation rule.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.jsonValidation
 */
export interface IJsonValidationContribution {
    /** File match pattern (glob or exact file name) for applying the schema. */
    fileMatch: string | string[];
    /** URL or relative path to the JSON schema. */
    url: string;
}

/**
 * Contribution describing a keybinding.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.keybindings
 */
export interface IKeyBindingContribution {
    /** Name of the command to execute. */
    command: string;
    /** Default key combo (e.g. "ctrl+k ctrl+s"). */
    key?: string;
    /** OS-specific key combos. */
    mac?: string;
    linux?: string;
    win?: string;
    /** Condition when this keybinding is active (context key expression). */
    when?: string;
    /** Arguments to pass to the command. */
    args?: string | string[];
}

/**
 * Contribution describing a language definition.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.languages
 */
export interface ILanguageContribution {
    /** Unique identifier for the language. */
    id: string;
    /** User facing name(s) for the language. */
    aliases?: string[];
    /** File extensions associated with the language (e.g. ".js"). */
    extensions?: string[];
    /** Specific file names associated with the language (e.g. "Makefile"). */
    filenames?: string[];
    /** Array of filename glob patterns for this language. */
    filenamePatterns?: string[];
    /** A first line regex match used to identify the language. */
    firstLine?: string;
    /** Path to the configuration file (relative to the extension folder). */
    configuration?: string;
}

/**
 * Contribution describing a single menu item.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.menus
 */
export interface IMenuItemContribution {
    /**
     * The title of the menu item. If not specified, the title of the command is used.
     */
    title?: string;
    /** The command to run for this menu item (must be defined in contributes.commands). */
    command?: string;
    /** Context key expression that controls visibility. */
    when?: string;
    /** Sorting/grouping attribute (e.g. "navigation" or "1_modification"). */
    group?: string;
    /**
     * If specified, this menu item will be a submenu that contains the
     * items from the submenu with the given ID.
     */
    submenu?: string;
    /**
     * If specified, this menu item will be an alternative to the
     * item with the given ID. The alternative is shown when the
     * context key expression evaluates to true.
     */
    alt?: string;
    /**
     * Arguments to pass to the command when it is run from this menu item.
     * If not specified, the arguments from the command definition are used.
     */
    args?: string | string[];
}

/**
 * Contribution describing a problem matcher, which helps parse and capture problems
 * (errors, warnings, etc.) from the output of a task or debug console.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.problemMatchers
 */
export interface IProblemMatcherContribution {
    /**
     * A unique name for the problem matcher, used in tasks.json if this
     * matcher is referenced. This is required if the problem matcher is
     * to be directly used in tasks.
     */
    name?: string;
    /**
     * The name of a base problem matcher to inherit from. Base matchers
     * can be defined by other extensions or VS Code itself.
     */
    base?: string;
    /**
     * The owner of the problem. Defaults to "external". Usually set to "typescript"
     * or a custom owner to group or filter problems in the 'Problems' panel.
     */
    owner?: string;
    /**
     * Defines which documents the problems apply to. Valid values:
     * - "allDocuments"
     * - "openDocuments"
     * - "closedDocuments"
     * Default is "allDocuments".
     */
    applyTo?: "allDocuments" | "openDocuments" | "closedDocuments";
    /**
     * Controls how file paths in problems are interpreted:
     * - "absolute"
     * - "autoDetect"
     * - ["relative", "${workspaceFolder}" or another path]
     */
    fileLocation?: string | [string, string];
    /**
     * A problem pattern (or array of patterns) that describes how to extract
     * the problem information from a line of text or from multiple lines.
     */
    pattern?: IProblemPatternContribution | IProblemPatternContribution[];
    /**
     * Defines a watching problem pattern that is used to begin or end a
     * pattern-matching mode (e.g., for multi-line logs). Must be a single pattern
     * or an array (for multi-line).
     */
    watchingPattern?: IProblemPatternContribution | IProblemPatternContribution[];
    /**
     * If present, overrides the severity from the problem pattern. Valid values:
     * - "error"
     * - "warning"
     * - "info"
     */
    severity?: "error" | "warning" | "info";
    /**
     * Defines background matching patterns used to signal when a sequence
     * of problems begins (beginsPattern) and ends (endsPattern).
     */
    background?: {
        /** If true, the watch mode is active immediately when the matcher starts. */
        activeOnStart?: boolean;
        /** The pattern that signals the beginning of an output sequence to match. */
        beginsPattern: IProblemPatternContribution;
        /** The pattern that signals the end of the output sequence. */
        endsPattern: IProblemPatternContribution;
    };
    /**
     * A prefix appended to the file path captured by the pattern, typically used
     * if the output does not print absolute or relative paths as needed.
     */
    filePrefix?: string;
}

/**
 * A single pattern (or multi-pattern) descriptor for a problem matcher.
 * Can detect files, lines, columns, messages, etc. from text output.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.problemPatterns
 */
export interface IProblemPatternContribution {
    /** The regex pattern to capture information from a log line. */
    regexp: string;
    /**
     * The match group index of the filename (e.g. 1 if the first capturing group
     * contains the file path).
     */
    file?: number;
    /**
     * If the log has a single "location" group in "line:column" format, specify that
     * group index here. Otherwise, specify line/column separately.
     */
    location?: number;
    /** The match group index for the line number. */
    line?: number;
    /** The match group index for the column number. */
    column?: number;
    /** The match group index for the ending line number of a problem range. */
    endLine?: number;
    /** The match group index for the ending column number of a problem range. */
    endColumn?: number;
    /** The match group index for the problem message. */
    message?: number;
    /** The match group index for the problem code (e.g. an error code). */
    code?: number;
    /** The match group index for the severity (e.g. error, warning, info). */
    severity?: number;
    /**
     * If true, instructs VS Code to continue matching multiple problems on subsequent lines.
     * Useful for multi-line messages.
     */
    loop?: boolean;
}

/**
 * Contribution describing a product icon theme.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.productIconThemes
 */
export interface IProductIconThemeContribution {
    /** Unique identifier for the product icon theme. */
    id: string;
    /** Display name for the product icon theme. */
    label: string;
    /** Path to the product icon theme definition file (relative to the extension folder). */
    path: string;
}

/**
 * Contribution describing a resource label formatter.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.resourceLabelFormatters
 */
export interface IResourceLabelFormatterContribution {
    /** URI scheme this formatter applies to (e.g. "file"). */
    scheme: string;
    /** Format string controlling how the resource path is displayed. */
    formatting: {
        /** Template for the label, can include `${path}`, `${authority}`, etc. */
        label: string;
        /** Suffix to append for the workspace name. */
        workspaceSuffix?: string;
        /** Use slash or backslash consistently? */
        separator?: "/" | "\\";
        [key: string]: any;
    };
}

/**
 * Contributes a semantic token modifier.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.semanticTokenModifiers
 */
export interface ISemanticTokenModifierContribution {
    /**
     * The identifier of the semantic token modifier.
     */
    id: string;
    /**
     * A human-readable string describing the semantic token modifier.
     */
    description?: string;
}

/**
 * Contributes a semantic token scope.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.semanticTokenScopes
 */
export interface ISemanticTokenScopeContribution {
    /**
     * The language for which the semantic token scopes apply.
     */
    language: string;

    /**
     * A mapping of semantic token selectors (e.g. "class.declaration") to arrays of TextMate scopes.
     */
    scopes: {
        [semanticSelector: string]: string[];
    };
}

/**
 * Contributes a semantic token type.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.semanticTokenTypes
 */
export interface ISemanticTokenTypeContribution {
    /**
     * The identifier of the semantic token type.
     */
    id: string;

    /**
     * A description of this semantic token type.
     */
    description?: string;

    /**
     * A super type that this semantic token type extends.
     */
    superType?: string;

    /**
     * If set to true, this semantic token type is considered protected.
     */
    protected?: boolean;
}

/**
 * Contribution describing a snippet.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.snippets
 */
export interface ISnippetContribution {
    /** Language ID the snippet is for. */
    language: string;
    /** Path to the snippet file (relative to the extension folder). */
    path: string;
}

/**
 * Contributes a submenu to the editor or explorer context menus.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.submenus
 */
export interface ISubmenuContribution {
    /**
     * Unique identifier for the submenu. This ID is used when
     * specifying `submenu` in the `menus` contribution point.
     */
    id: string;

    /**
     * The display label for the submenu.
     */
    label: string;

    /**
     * An optional icon for the submenu.
     */
    icon?: string | { light: string; dark: string };

    /**
     * An optional context key expression that controls when the submenu is visible.
     */
    when?: string;

    /**
     * An optional sorting group for the submenu.
     */
    group?: string;
}

/**
 * Contribution describing a task definition.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.taskDefinitions
 */
export interface ITaskDefinitionContribution {
    /** The task type used in tasks.json (e.g. "npm", "gulp"). */
    type: string;
    /** Required fields for a task of this type (besides "type"). */
    required?: string[];
    /** Additional properties for this task type. */
    properties?: {
        [property: string]: {
            type: string | string[];
            description?: string;
            default?: any;
        };
    };
}

/**
 * Contributes terminal-related configuration such as custom profiles and decorations.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.terminal
 */
export interface ITerminalContribution {
    /**
     * Contributes custom terminal profiles.
     * The key is the profile ID, and the value is the profile definition.
     */
    profiles?: {
        [profileId: string]: {
            /**
             * The shell or executable path.
             */
            path: string;

            /**
             * Optional arguments to pass to the shell/executable.
             */
            args?: string[];

            /**
             * An optional icon (e.g., a codicon or URI to an image).
             */
            icon?: string | { light: string; dark: string };

            /**
             * Optional icon color, if supported.
             */
            color?: string;

            /**
             * Additional environment variables for this profile.
             */
            env?: {
                [key: string]: string;
            };
        };
    };

    /**
     * Contributes custom terminal decorations.
     */
    decorations?: Array<{
        /**
         * Unique identifier for the decoration.
         */
        id: string;

        /**
         * A command to execute when the decoration is triggered (e.g., clicked).
         */
        command: string;
    }>;
}

/**
 * Contribution describing a classic text editor color theme (deprecated in favor of colorThemes).
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.themes
 */
export interface IThemeContribution {
    /** Display name for the theme. */
    label: string;
    /** UI theme base (e.g. "vs" = light, "vs-dark" = dark, "hc-black" = high-contrast). */
    uiTheme: string;
    /** Path to the theme file (relative to the extension folder). */
    path: string;
}

/**
 * Contribution describing a TypeScript server plugin.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.typescriptServerPlugins
 */
export interface ITypeScriptServerPluginContribution {
    /** Name of the plugin. */
    name: string;
    /** Path to the plugin entry point or main file (relative to the extension folder). */
    path: string;
}

/**
 * Contribution describing a view container (like an Activity Bar icon).
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.viewsContainers
 */
export interface IViewContainerContribution {
    /** Identifier of this view container. */
    id: string;
    /** Display name for this container. */
    title: string;
    /** Icon path or theme icon reference. */
    icon?: string | { light: string; dark: string };
    /** Order in which this container appears. */
    order?: number;
}

/**
 * Contribution describing a single view in a container.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.views
 */
export interface IViewContribution {
    /** Identifier of the view. */
    id: string;
    /** Display name of the view. */
    name: string;
    /** When clause controlling view visibility. */
    when?: string;
    /** Default icon for the view. */
    icon?: string | { light: string; dark: string };
    /** Type of the view. */
    type: "webview";
    /**
     * The title shown when the view is in a split editor.
     * If not specified, the view's name is used.
     */
    contextualTitle?: string;
}

/**
 * Contribution describing a welcome view item.
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.viewsWelcome
 */
export interface IViewWelcomeContribution {
    /** The view ID where this welcome content should appear. */
    view: string;
    /** Content in Markdown. */
    contents: string;
    /** Order in which this welcome appears. */
    order?: number;
    /** When clause for visibility. */
    when?: string;
}

/**
 * Contribution describing a walkthrough (getting started experience).
 * @see https://code.visualstudio.com/api/references/contribution-points#contributes.walkthroughs
 */
export interface IWalkthroughContribution {
    /** Unique identifier for the walkthrough. */
    id: string;
    /** Display title. */
    title: string;
    /** Detailed description. */
    description: string;
    /** Category to group walkthrough in the UI (e.g. "Getting Started"). */
    category?: string;
    /** List of walkthrough steps. */
    steps: WalkthroughStep[];
}

/**
 * A single step in a walkthrough.
 */
export interface WalkthroughStep {
    /** Unique identifier for the step. */
    id: string;
    /** Title of the step. */
    title: string;
    /** Description or markdown content. */
    description: string;
    /** Media (image, markdown, etc.) to show. */
    media?: {
        image?: string;
        markdown?: string;
        svg?: string;
        height?: number;
    };
    /** When clause to control visibility. */
    when?: string;
    /** Command to run when the step is clicked. */
    completionEvents?: string[];
}
