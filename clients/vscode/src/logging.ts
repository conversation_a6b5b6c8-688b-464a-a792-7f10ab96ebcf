import {
    getLogger as getLibraryLogger,
    type AugmentLogger as LibraryAugmentLogger,
} from "@augment-internal/sidecar-libs/src/logging";

// Import init/logging.ts to ensure logger is initialized
import "./init/logging";

// Re-export the AugmentLogger type
export type AugmentLogger = LibraryAugmentLogger;

/**
 * Gets a logger instance with the specified prefix.
 * The logger is initialized in init.ts, which should be imported first in extension.ts.
 */
export function getLogger(prefix: string): AugmentLogger {
    return getLibraryLogger(prefix);
}
