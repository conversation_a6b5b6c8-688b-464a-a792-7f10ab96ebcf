import * as vscode from "vscode";

import { AugmentCompletion } from "../augment-completion";
import { CompletionRequest } from "../completions-model";

export function adjustedCompletion(
    completionRequest: CompletionRequest,
    completion: AugmentCompletion,
    start: vscode.Position
): AugmentCompletion {
    let completionPrefix = "";
    let completionText = completion.completionText;

    // There are two scenarios where completions need to be adjusted to account
    // for VSCode's requests for completions.
    //
    // 1. The scenario where a request includes characters before the
    //    the Augment completion starts.
    //    i.e. the completion text "odel = 'hi';", and the request is for
    //    one character before "o", meaning the completion given to VSCode
    //    is "|model = 'hi';"
    // 2. The completion request starts in the middle of our completion
    //    i.e. the completion text "console.log('hi')", and the request
    //    is for after the "console." portion of the completion.
    //    The completion given to VSCode is "log('hi')"

    const document = completionRequest.document;
    const diff = document.offsetAt(start) - completion.range.startOffset;
    if (diff < 0) {
        // Get prefix for completion
        completionPrefix = completionRequest.prefix.slice(diff);
    } else {
        // If we are typing out a completion, we may be asked for
        // our completion to start from somewhere in the middle of
        // our completion text.
        // In that case, remove the start of the completion text.
        completionText = completionText.slice(diff);
    }
    return new AugmentCompletion(
        completionPrefix + completionText,
        completion.suffixReplacementText,
        completion.skippedSuffix,
        {
            startOffset: document.offsetAt(start),
            endOffset: completion.range.endOffset,
        }
    );
}
