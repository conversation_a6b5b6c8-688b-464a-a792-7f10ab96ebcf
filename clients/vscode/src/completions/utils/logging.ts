import { CompletionContext, InlineCompletionContext, Position, Range, TextDocument } from "vscode";

// Helper function to log inline completion context
export function inlineCompletionContextToString(
    document: TextDocument,
    ctx: InlineCompletionContext
): string {
    return `context:
    text: ${ctx.selectedCompletionInfo?.text}
    range: ${rangeToString(document, ctx.selectedCompletionInfo?.range)}
    triggerKind: ${ctx.triggerKind}`;
}

export function completionContextToString(ctx: CompletionContext): string {
    return `context:
    text: ${ctx.triggerCharacter}
    triggerKind: ${ctx.triggerKind}`;
}

export function positionToString(document: TextDocument, position: Position): string {
    return `ln: ${position.line} ch: ${position.character} offset: ${document.offsetAt(position)}`;
}

export function rangeToString(document: TextDocument, range: Range | undefined): string {
    if (range === undefined) {
        return "<undefined>";
    }
    return `start: ${positionToString(document, range.start)} -> end: ${positionToString(
        document,
        range.end
    )}`;
}

export function optionalString(prefix: string, value?: string): string {
    return value ? `${prefix}'${value}'` : "";
}
