import { EventEmitter, TextDocument } from "vscode";

import { CompletionRequest } from "./completions-model";

export interface CompletionResolutionEvent {
    /** The request ID of the completion that was resolved. */
    requestId: string;
    /** The index of the completion that was accepted, or -1 if the completion was rejected. */
    acceptedIdx: number;
    /** Document that the completion was resolved in. */
    document: TextDocument;
}

const completionEventEmitter = new EventEmitter<CompletionRequest | undefined>();
const completionResolvedEventEmitter = new EventEmitter<CompletionResolutionEvent>();
const cancelledEventEmitter = new EventEmitter<void>();
const inlineCompletionVisibilityEmitter = new EventEmitter<boolean>();

export const onCompletionRequest = (cb: (req: CompletionRequest | undefined) => void) =>
    completionEventEmitter.event(cb);
export const onCompletionRequestCancelled = (cb: () => void) => cancelledEventEmitter.event(cb);
export const onCompletionResolved = (cb: (resolution: CompletionResolutionEvent) => void) =>
    completionResolvedEventEmitter.event(cb);

export const dispatchCompletionRequest = (req: CompletionRequest | undefined) =>
    completionEventEmitter.fire(req);
export const dispatchCompletionRequestCancelled = () => cancelledEventEmitter.fire();
export const dispatchCompletionResolved = (req: CompletionResolutionEvent) =>
    completionResolvedEventEmitter.fire(req);

export const onInlineCompletionVisibilityChange = (cb: (visible: boolean) => void) =>
    inlineCompletionVisibilityEmitter.event(cb);

export const dispatchInlineCompletionVisibilityChange = (visible: boolean) =>
    inlineCompletionVisibilityEmitter.fire(visible);
