import { debounce, DebouncedFunc } from "lodash";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { type AugmentLogger, getLogger } from "../logging";
import { eventAsPromise } from "../utils/events";
import { onCompletionRequest, onCompletionRequestCancelled } from "./completion-events";
import { adjustedCompletion } from "./utils/adjust-completion";
import {
    completionContextToString,
    optionalString,
    positionToString,
    rangeToString,
} from "./utils/logging";

export class CompletionItemsProvider implements vscode.CompletionItemProvider {
    private _logger: AugmentLogger = getLogger("CompletionItemsProvider");

    private _documentation = new vscode.MarkdownString(`This is a suggestion from Augment.

You can let us know about good or bad suggestions via [the History Panel](command:vscode-augment.showHistoryPanel).`);

    public static triggerCharacters = [".", ":"];

    // We use a doc selector with a broad range of languages AND a glob star
    // to encourage VSCode to call us for all languages.
    // VSCode uses a scoring system to decide which provider to use.
    //
    //   > Multiple providers can be registered for a language. In that
    //   > case providers are sorted by their score and groups of equal
    //   > score are sequentially asked for completion items. The process
    //   > stops when one or many providers of a group return a result.
    //   > A failing provider (rejected promise or exception) will not
    //   > fail the whole operation.
    //
    // Without the languages, it's common for our provider not to get called
    // at all.
    //
    // See `registerCompletionItemProvider` API for details.
    public static languageSelector = [
        "python",
        "typescript",
        "javascript",
        "java",
        "go",
        "rust",
        "c",
        "cpp",
        "php",
        "csharp",
        "ruby",
        "bash",
        "html",
        "css",
        "scss",
        "less",
        "sass",
        "json",
        "yaml",
        "markdown",
        "xml",
        "sql",
        "swift",
        "kotlin",
        "objective-c",
        "perl",
        "scala",
        "lua",
        "groovy",
        "powershell",
        "*",
    ];

    constructor(private _config: AugmentConfigListener) {
        // This allows markdown links to work
        this._documentation.isTrusted = true;
    }

    public async provideCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        cancelToken: vscode.CancellationToken,
        context: vscode.CompletionContext
    ): Promise<vscode.CompletionItem[]> {
        this._logger.debug(
            `Pop-Up Request - ${document.uri.toString()} ${positionToString(
                document,
                position
            )}${optionalString(" ", context.triggerCharacter)}`
        );
        this._logger.verbose(completionContextToString(context));

        // Do not wait for a completion or return completions if the user
        // has typed a trigger character
        if (context.triggerKind === vscode.CompletionTriggerKind.TriggerCharacter) {
            this._logger.debug(
                `Returning no completions because trigger kind was a trigger character`
            );
            return [];
        }

        if (!this._config.config.completions.enableAutomaticCompletions) {
            this._logger.debug("Automatic completions are disabled");
            return [];
        }

        let debouncedFunc: DebouncedFunc<() => void> | undefined;
        let debouncedDisposable: vscode.Disposable | undefined;
        try {
            return await Promise.race([
                this._getCompletions(document, position, cancelToken),

                // This promise will resolve after config.completions.timeoutMs
                // or config.completions.maxWaitMs has elapsed.
                // We use a debounce function to reset the timer in the event
                // the inline provider gets a request for a new completion.
                new Promise<vscode.CompletionItem[]>((resolve) => {
                    const timeoutCompletion = () => {
                        this._logger.warn("Failed to find completions in time");
                        resolve([]);
                    };
                    const debouncedTimeoutFunc = debounce(
                        timeoutCompletion,
                        this._config.config.completions.timeoutMs,
                        {
                            maxWait: this._config.config.completions.maxWaitMs,
                        }
                    );
                    // A cancellation from the inline provider suggests a new
                    // completion is in progress
                    debouncedDisposable = onCompletionRequestCancelled(() => {
                        // When the inline provider completion is cancelled
                        // that means a new completion is in progress, so we
                        // should "debounce" (a.k.a reset the debounce timer)
                        // our timeout.
                        debouncedTimeoutFunc();
                    });
                    debouncedFunc = debouncedTimeoutFunc;
                    debouncedTimeoutFunc();
                }),
            ]);
        } finally {
            debouncedDisposable?.dispose();
            debouncedFunc?.cancel();
        }
    }

    // AU-2948
    // get a little extra after the first word to show the user that clicking this completion
    // will fill in a bunch of stuff (probably matching the inline completion) and not just
    // the first word.
    private _getCompletionSuffix(completionWord: string, remainingCompletionText: string): string {
        const suffix = remainingCompletionText.substring(
            completionWord.length,
            completionWord.length + 50
        );

        if (suffix.length === 0) {
            return "";
        }

        // eslint-disable-next-line no-useless-escape -- eslint seems to be incorrect about \[\]
        const matches = suffix.match(/^[^{}\[\]\(\)"`'<>]+/);
        // the above characters will cause vs code to insert the closing buddy character which
        // may cause the inline completion to disappear but the pop-up to remain, so we stop when
        // we see one of these.
        if (matches) {
            // If matches [0] === suffix, then we are showing
            // the full completion text and don't need the suffix.
            if (matches[0] === suffix) {
                return suffix;
            }
            return matches[0] + "\u2026";
        } else {
            return "\u2026";
        }
    }

    private async _getCompletions(
        document: vscode.TextDocument,
        position: vscode.Position,
        cancelToken: vscode.CancellationToken
    ): Promise<vscode.CompletionItem[]> {
        // Wait for the inline completions provider to get a result before
        // returning the same completions here.
        // We do this to ensure that when the pop-up is shown to the user
        // the inline suggestion matches our pop-up suggestion, which allows
        // the pre-select to reliably work.
        const completionRequest = await eventAsPromise(onCompletionRequest);

        if (!this._config.config.completions.enableQuickSuggestions) {
            this._logger.debug(`Enable IntelliSense suggestion is disabled`);
            return [];
        }

        if (!completionRequest) {
            this._logger.debug(`No completion request`);
            return [];
        }

        if (document !== completionRequest.document) {
            this._logger.debug(
                `Completion request document does not match the current document. ` +
                    `    current document: ${document.uri.toString()} ` +
                    `    completion request document: ${completionRequest.document.uri.toString()}`
            );
            return [];
        }

        const completionItems: vscode.CompletionItem[] = [];
        for (const completion of completionRequest.completions) {
            this._logger.verbose(`AugmentCompletion: ${completion.toString()}`);
            // Note, the below comment still applies but we do add some additional
            // text in `_getCompletionSuffix`
            //
            // We only want to show one word in the completion item label.
            // This is because VSCode will display an item in a pop-up while
            // the user enters characters that matches the label.
            // So if the label is `console` and our completion is
            // `console.log('hi')`, the pop-up will remain up until the user
            // enters the `e` character in console.
            // If we show the full completion text, the pop-up is more
            // likely to remain up when our completion no longer matches.
            // For example, if the completion is `console.log('hi')` and
            // the user enters `console.log(`, VSCode may automatically add a
            // closing parenthesis, so from our extensions perspective, the
            // user went from `console.log` to `console.log()`, which means
            // our completion no longer matches BUT VSCode will consider
            // `console.log()` as valid and still matching the completion item
            // `console.log('hi')` because it knows the cursor position and
            // knows it's inserted an additional closing parenthesis which is
            // still in the completion item.

            // get the position of the start of the word
            const wordStart = document.getWordRangeAtPosition(position)?.start ?? position;
            // adjust the completion to start from the word start
            const alteredCompletions = adjustedCompletion(completionRequest, completion, wordStart);

            const desiredPositionOffset = document.offsetAt(position);
            if (
                desiredPositionOffset < alteredCompletions.range.startOffset ||
                desiredPositionOffset > alteredCompletions.range.endOffset
            ) {
                this._logger.debug(
                    "Inline provider event does not satisfy this requests position. " +
                        `    completion item: ${positionToString(document, position)}` +
                        `    inline provider event: ${alteredCompletions.range.startOffset} => ${alteredCompletions.range.endOffset}}`
                );
                continue;
            }

            // get the number of characters entered by the user
            const diff = document.offsetAt(position) - document.offsetAt(wordStart);
            // remove the characters entered by the user so we can find the next word to be entered
            const remainingCompletionText = (
                alteredCompletions.completionText + alteredCompletions.suffixReplacementText
            ).slice(diff);
            // Get the current word by splitting on a non-word character
            const completionWord = remainingCompletionText.split(/[^a-zA-Z0-9_]/).shift() ?? "";
            // get the range from the start of the word to the cursor position
            const wordPrefixRange = new vscode.Range(wordStart, position);
            // get the text from the start of the word to the cursor position
            const wordPrefix = document.getText(wordPrefixRange);
            // the full word is the prefix (start of word to cursor position) +
            // the word that we selected
            let fullWord =
                wordPrefix +
                completionWord +
                this._getCompletionSuffix(completionWord, remainingCompletionText);

            // VSCode doesn't display our items in the pop-up if the beginning
            // of the insert text is not in the label.
            // For example: if the label is `log` from the completion
            // `console.log()`, we must slice the completion to `log()`.
            // If the completion text is `log()`, we need then change the range
            // to start from the `l` character
            let completionItem = new vscode.CompletionItem(fullWord);
            completionItem.insertText =
                alteredCompletions.completionText + alteredCompletions.suffixReplacementText;

            // Because our range may extend past the current word and cursor
            // for skip tokens, we must provider an `inserting` and `replacing`
            // range.

            // Anything past the position is treated as a
            // skip token. i.e. if the completion is `content_manager`
            // and the position is 1, as in `c` but the editor is at 2,
            // we must return 0-1 for the inserting range, otherwise
            // 0-2 will cause the following character (outside of Augments
            // completion) to be overwritten.
            const range = new vscode.Range(
                document.positionAt(alteredCompletions.range.startOffset),
                document.positionAt(
                    document.offsetAt(position) + alteredCompletions.skippedSuffix.length
                )
            );
            completionItem.range = {
                inserting: range,
                replacing: range,
            };
            // If preselect is true, VSCode may not select our completion
            // initially, but it can select our completion item
            // once the user has typed the full completion, creating an
            // unexpected user experience.
            completionItem.preselect = false;
            completionItem.kind = vscode.CompletionItemKind.Snippet;
            completionItem.detail = "Augment";
            completionItem.documentation = this._documentation;
            // This is needed to make sure VSCode does not change the
            // completions indentation
            completionItem.keepWhitespace = true;

            this._logger.debug(`insert: ${completionItem.insertText}
label: ${fullWord}
position: ${positionToString(document, position)}
inserting: ${rangeToString(document, completionItem.range.inserting)}
replacing: ${rangeToString(document, completionItem.range.replacing)})}`);

            completionItems.push(completionItem);
        }

        if (cancelToken.isCancellationRequested) {
            this._logger.debug(`Completion cancelled`);
            return [];
        }

        return completionItems;
    }
}
