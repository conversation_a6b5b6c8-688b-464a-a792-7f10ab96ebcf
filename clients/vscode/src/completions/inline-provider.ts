import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { SkipCompletion } from "../exceptions";
import { type AugmentLogger, getLogger } from "../logging";
import { CompletionAcceptanceReporter } from "../metrics/completion-acceptance-reporter";
import { ClientCompletionTimelineReporter } from "../metrics/completion-timeline-reporter";
import { StateController } from "../statusbar/state-controller";
import { StatusBarManager } from "../statusbar/status-bar-manager";
import * as statusbarStates from "../statusbar/status-bar-states";
import { DisposableService } from "../utils/disposable-service";
import { AugmentCompletion } from "./augment-completion";
import {
    dispatchCompletionRequest,
    dispatchCompletionRequestCancelled,
    dispatchInlineCompletionVisibilityChange,
} from "./completion-events";
import { CompletionTimeline } from "./completion-timeline";
import { CompletionRequest, CompletionsModel } from "./completions-model";
import { PendingCompletion } from "./pending-completion";
import { SuppressDeletedCompletions } from "./suppress-deleted-completions";
import { adjustedCompletion } from "./utils/adjust-completion";
import {
    inlineCompletionContextToString,
    optionalString,
    positionToString,
    rangeToString,
} from "./utils/logging";

const NO_COMPLETION_TIMEOUT_MS = 2000;

export class InlineCompletionProvider
    extends DisposableService
    implements vscode.InlineCompletionItemProvider
{
    private _logger: AugmentLogger = getLogger("InlineCompletionProvider");
    private _pendingCompletions: PendingCompletion;
    private _deletedCompletions = new SuppressDeletedCompletions();
    private _stateController: StateController;

    constructor(
        private _completionsModel: CompletionsModel,
        metricsReporter: CompletionAcceptanceReporter,
        statusBar: StatusBarManager,
        private _config: AugmentConfigListener,
        private _timelineReporter: ClientCompletionTimelineReporter
    ) {
        super();
        this._pendingCompletions = new PendingCompletion(metricsReporter);
        this._stateController = new StateController(statusBar);
        this.addDisposable(this._pendingCompletions);
        this.addDisposable(this._deletedCompletions);
        this.addDisposable(
            vscode.window.onDidChangeTextEditorSelection(
                (evt: vscode.TextEditorSelectionChangeEvent) => {
                    // it seems that sometimes the output/log window starts firing
                    // these events nonstop as log lines come out, so we don't want those.
                    if (evt.textEditor.document.uri.scheme !== "file") {
                        return;
                    }
                    dispatchInlineCompletionVisibilityChange(false);
                }
            )
        );
    }

    public async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        cancelToken: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[]> {
        const completionTimeline = new CompletionTimeline();

        // Do not return completions if the user disables automatic completions
        // and this completion is an automatic trigger kind.
        if (
            !this._config.config.completions.enableAutomaticCompletions &&
            context.triggerKind === vscode.InlineCompletionTriggerKind.Automatic
        ) {
            dispatchInlineCompletionVisibilityChange(false);
            return [];
        }

        // Dispose of any previous error states
        this._stateController.dispose();

        this._logger.debug(
            `Inline Request - ${document.uri.toString()} ${positionToString(
                document,
                position
            )}${optionalString(" ", context.selectedCompletionInfo?.text)}`
        );
        this._logger.verbose(inlineCompletionContextToString(document, context));

        const rawRequest = await this._getCompletions(
            document,
            position,
            context,
            completionTimeline
        );

        // Check if the completion is a previous deleted completion
        const completionRequest = this._deletedCompletions.processRequest(rawRequest);

        if (cancelToken.isCancellationRequested) {
            // If the event is cancelled, we assume VSCode is going to ask
            // for a follow up, so we return before emitting a completion
            // event.
            this._logger.debug(`Completion cancelled`);
            dispatchCompletionRequestCancelled();
            dispatchInlineCompletionVisibilityChange(false);
            return [];
        }

        // Alert the completion-items-provider that we have a new completion
        // request and that it can resolve it's completions.
        dispatchCompletionRequest(completionRequest);

        if (!completionRequest) {
            this._logger.debug(`Returning no completions`);
            dispatchInlineCompletionVisibilityChange(false);
            return [];
        }

        // Report client completion timeline
        completionTimeline.emitTime = Date.now();
        if (!completionRequest.isReused) {
            this._timelineReporter.reportCompletionTimeline(
                completionRequest.requestId,
                completionTimeline
            );
        }

        const result = completionRequest.completions.map((completion) => {
            this._logger.verbose(`AugmentCompletion: ${completion.toString()}`);
            const inlineItem = new vscode.InlineCompletionItem(
                completion.completionText + completion.suffixReplacementText,
                new vscode.Range(
                    document.positionAt(completion.range.startOffset),
                    document.positionAt(
                        completion.range.endOffset + completion.skippedSuffix.length
                    )
                )
            );
            this._logger.verbose(
                `InlineCompletionItem: ${JSON.stringify(inlineItem.insertText)} ${rangeToString(
                    document,
                    inlineItem.range
                )}`
            );
            return inlineItem;
        });
        if (result.length > 0) {
            dispatchInlineCompletionVisibilityChange(true);
        }

        return result;
    }

    private async _getCompletions(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        completionTimeline: CompletionTimeline
    ): Promise<CompletionRequest | undefined> {
        // If the previous completion matches this text return it.
        const prevCompletion = this._pendingCompletions.getPendingCompletion(document, position);
        if (prevCompletion && prevCompletion.completions.length > 0) {
            const newCompletions = this._processCompletionForMode(prevCompletion, context);
            this._logger.debug(`Returning ${newCompletions.length} completions`);
            return {
                ...prevCompletion,
                completions: newCompletions,
            };
        }

        if (context.selectedCompletionInfo?.text) {
            // If we are here, the inline completion request is for a piece
            // of text from a pop-up and it's not an "Augment suggestion",
            // so we show nothing.
            this._logger.debug(
                `Returning no completions because the provider request includes selected text that does not match an Augment suggestion`
            );
            return;
        }

        const generatingStatus = this._stateController.setState(
            statusbarStates.generatingCompletion
        );

        try {
            const completionRequest = await this._completionsModel.generateCompletion(
                document,
                position,
                completionTimeline
            );
            if (completionRequest && completionRequest.completions.length === 0) {
                const disposable = this._stateController.setState(statusbarStates.noCompletions);
                setTimeout(() => {
                    disposable.dispose();
                }, NO_COMPLETION_TIMEOUT_MS);
            }
            return completionRequest;
        } catch (e) {
            if (!(e instanceof SkipCompletion)) {
                this._stateController.setState(statusbarStates.completionFailed);
            }
        } finally {
            generatingStatus.dispose();
        }
    }
    private _processCompletionForMode(
        prevCompletion: CompletionRequest,
        context: vscode.InlineCompletionContext
    ): AugmentCompletion[] {
        // If there is no selected text, return all completions as this is a
        // simple inline completion request.
        if (!context.selectedCompletionInfo || !context.selectedCompletionInfo.text) {
            return prevCompletion.completions;
        }

        const start = context.selectedCompletionInfo.range.start;
        const adjustedCompletions = prevCompletion.completions.map((completion) => {
            return adjustedCompletion(prevCompletion, completion, start);
        });

        // Let VSCode handle the filtering
        return adjustedCompletions;
    }
}
