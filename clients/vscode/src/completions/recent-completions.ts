import { RecentItems } from "../utils/recent-items";
import { CompletionRequest } from "./completions-model";

// Store completions in a ring buffer and emit an event when a new completion
// is added.
export class RecentCompletions extends RecentItems<CompletionRequest> {
    constructor() {
        super(
            100,
            (completionRequest) =>
                completionRequest.completions.length > 0 && !completionRequest.isReused
        );
    }
}
