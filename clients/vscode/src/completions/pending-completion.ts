import * as vscode from "vscode";

import { getLogger } from "../logging";
import { CompletionAcceptanceReporter } from "../metrics/completion-acceptance-reporter";
import { DisposableService } from "../utils/disposable-service";
import { AugmentCompletion } from "./augment-completion";
import {
    dispatchCompletionResolved,
    dispatchInlineCompletionVisibilityChange,
    onCompletionRequest,
} from "./completion-events";
import { CompletionRequest } from "./completions-model";

export class PendingCompletion extends DisposableService {
    private _logger = getLogger("PendingCompletion");
    private _pendingCompletion: PendingCompletionDetails | undefined;

    constructor(private _metricsReporter: CompletionAcceptanceReporter) {
        super();

        this.addDisposables(
            vscode.workspace.onDidChangeTextDocument((event) => {
                this._onTextDocumentChange(event);
            }),
            vscode.window.onDidChangeActiveTextEditor(() => {
                this._rejectPendingCompletion();
            }),
            vscode.window.onDidChangeWindowState(() => {
                this._rejectPendingCompletion();
            }),
            onCompletionRequest((completionRequest) => {
                if (!completionRequest) {
                    return;
                }

                this._onNewCompletion(completionRequest);
            })
        );
    }

    getPendingCompletion(
        document: vscode.TextDocument,
        position: vscode.Position
    ): CompletionRequest | undefined {
        if (!this._pendingCompletion) {
            this._logger.verbose(`No previous completion to use`);
            return undefined;
        }

        if (this._pendingCompletion.document !== document) {
            this._logger.debug(
                `Not reusing previous completion as documents differ ${this._pendingCompletion.document.uri.toString()} vs ${document.uri.toString()}`
            );
            this._rejectPendingCompletion();
            return undefined;
        }

        const completion = this._pendingCompletion.completion;
        // VSCode can sometimes ask for a completion using an offset that is not equal to the
        // cursor position, i.e. completion.range.endOffset + this._pendingCompletion.added.
        // This can happen, e.g. when partial accepting past a skip, in which case VSCode will ask for
        // the offset right before the skip. In these cases, we can actually reuse the completion
        // so long as the offset is between the start and current cursor position, since we
        // are replacing [start, cursor position + remaining suffix)
        // with completion text + suffix replacement.
        const offset = document.offsetAt(position);
        const cursorPosition = completion.range.endOffset + this._pendingCompletion.added;
        if (offset < completion.range.startOffset) {
            this._logger.debug(
                `Not reusing previous completion as position is before completion: ${offset} vs
                ${completion.range.startOffset}`
            );
            this._rejectPendingCompletion();
            return undefined;
        }
        if (offset > cursorPosition) {
            this._logger.debug(
                `Not reusing previous completion as position is after completion: ${offset} vs
                ${cursorPosition}`
            );
            this._rejectPendingCompletion();
            return undefined;
        }

        let skippedSuffix = completion.skippedSuffix;
        let completionText = completion.completionText;
        let suffixReplacementText = completion.suffixReplacementText;

        // These suffix deletions have been consumed, so remove them from the skipped suffix suggestion.
        if (this._pendingCompletion.deleted > 0) {
            skippedSuffix = skippedSuffix.slice(this._pendingCompletion.deleted);
        }
        // We can just suggest a normal insertion instead.
        if (skippedSuffix === "") {
            completionText = completionText + suffixReplacementText;
            suffixReplacementText = "";
        }
        const newCompletion = new AugmentCompletion(
            completionText,
            suffixReplacementText,
            skippedSuffix,
            { startOffset: completion.range.startOffset, endOffset: cursorPosition }
        );
        this._logger.verbose(`Reusing pending completion: ${newCompletion.completionText}`);
        return {
            ...this._pendingCompletion,
            completions: [newCompletion],
            isReused: true,
        };
    }

    private _onNewCompletion(completionRequest: CompletionRequest) {
        const { completions, requestId } = completionRequest;
        if (this._pendingCompletion?.requestId === requestId) {
            // This is a completion request for the current pending completion.
            return;
        }

        if (!completions.length) {
            // No completions, nothing to do
            this._logger.verbose(`No completions in ${requestId}`);
            this._rejectPendingCompletion();
            return;
        }

        if (completions.length !== 1) {
            this._logger.warn(
                `Unable to handle multiple completions, received ${completions.length}`
            );
            this._rejectPendingCompletion();
            return;
        }

        const suffixMatches = this._findLastMatches(completions[0]);
        if (!suffixMatches) {
            this._logger.verbose(
                `Skipped suffix is not a subsequence of the suffix replacement text:
                ${completions[0].skippedSuffix} !<= ${completions[0].suffixReplacementText}`
            );
            this._rejectPendingCompletion();
            return;
        }

        this._logger.verbose(`Registering pending completion: ${completions[0].completionText}`);
        this._pendingCompletion = {
            ...completionRequest,
            completion: this._stripCommonSkippedSuffix(completions[0]),
            added: 0,
            deleted: 0,
            suffixMatches: suffixMatches,
            emitTime: Date.now(),
        };
    }

    // Finds the indices in suffixReplacementText that form the last possible subsequence
    // that match each character in skippedSuffix.
    private _findLastMatches(completion: AugmentCompletion): number[] | undefined {
        const suffix = completion.skippedSuffix;
        const replacement = completion.suffixReplacementText;
        const result: number[] = [];

        let suffixIndex = suffix.length - 1;
        let replacementIndex = replacement.length - 1;

        while (suffixIndex >= 0 && replacementIndex >= 0) {
            if (suffix[suffixIndex] === replacement[replacementIndex]) {
                result.push(replacementIndex);
                suffixIndex--;
            }
            replacementIndex--;
        }

        // No subsequence match, return undefined
        if (suffixIndex >= 0) {
            return undefined;
        }

        return result.reverse();
    }

    // Strips matching characters from the end of the skipped suffix and replacement text.
    // This is because such replacements, e.g. replace "])" with "bar])", may not be
    // user-visible, since nothing is inserted after the skip(s). So, the user may never
    // type them, but we would keep the completion pending, despite the rest being a no-op.
    private _stripCommonSkippedSuffix(completion: AugmentCompletion): AugmentCompletion {
        const suffix = completion.skippedSuffix;
        const replacement = completion.suffixReplacementText;

        let commonLength = 0;
        while (
            commonLength < suffix.length &&
            commonLength < replacement.length &&
            suffix[suffix.length - 1 - commonLength] ===
                replacement[replacement.length - 1 - commonLength]
        ) {
            commonLength++;
        }

        if (commonLength === 0) {
            // No common characters, return the original completion
            return completion;
        }

        return new AugmentCompletion(
            completion.completionText,
            replacement.slice(0, -commonLength),
            suffix.slice(0, -commonLength),
            completion.range
        );
    }

    // Updates the pending completions added and deleted counts based on the change, i.e.
    // how many additions from the completion text + suffix replacement text have been "consumed",
    // and how many deletions from the skipped suffix have been consumed (see PendingCompletionDetails).
    // A change contains a range to delete and text to add.
    // 1. A deletion range can either remove chars from the suffix or prefix. If from the suffix,
    //    this consumes deletions, i.e. increases the deleted count. If from the prefix, this "undoes"
    //    additions, i.e. decreases the added count.
    // 2. Added text only consumes additions, i.e. increases the added count. We do not consider
    //    the case where a user tries to "undo" a deletion by adding back a suffix char that was previously
    //    deleted.
    // All characters added must also match the completion request for it to be reused. Once the whole
    // completion has been consumed, the completion is accepted.
    private _onTextDocumentChange(event: vscode.TextDocumentChangeEvent) {
        if (!this._pendingCompletion) {
            return;
        }
        if (this._pendingCompletion.document !== event.document) {
            // We don't reject a pending completion when we see an edit for a different document,
            // because the edit might not be caused by user activity. For example, writing to the
            // extension log will get reported as a text document change. We don't want unrelated
            // edit notifications to cause completion to be rejected, so we ignore them here.
            // Instead, we reject pending completions when the user switches tabs.
            return;
        }
        if (event.contentChanges.length === 0) {
            // Do not reject a pending completion when the document state
            // changes unrelated to the contents.
            return;
        }

        if (event.contentChanges.length > 1) {
            // For now, we don't attempt to track multi-point edits.
            this._logger.verbose(
                `Unexpected number of content changes: ${event.contentChanges.length}`
            );
            this._rejectPendingCompletion();
            return;
        }

        // Annoyingly, vscode sometimes reports that some characters have been replaced, even if
        // the user is just accepting, or manually inserting, parts of the pending completion. When
        // this happens, the replaced characters (a) should match the already accepted characters,
        // and (b) should be reinserted by this same event (that is, this event's insertion text
        // will start with the same characters). For characters occurring after the startOffset,
        // these deletions can be counted as "undo"ing an addition.
        // But for characters occurring before the startOffset, we remove them during normalization.
        // NOTE: In this second case, we cannot verify that the reinserted characters match.
        //
        // Annoyingly, vscode also sometimes reports skips as an insertion after the skip character,
        // and sometimes as a deletion and insertion including the skip character. We thus normalize
        // insertions occurring after the cursor into a deletion and insertion.
        //
        // In summary, the post condition of normalization is that:
        // completion.range.startOffset <= change.rangeOffset <= completion.range.startOffset + added
        const normalizedChange = this._normalizeChange(event.contentChanges[0]);
        if (!normalizedChange) {
            this._logger.verbose(
                `Unexpected content change: ${JSON.stringify(event.contentChanges[0])}`
            );
            this._rejectPendingCompletion();
            return;
        }

        let added = this._pendingCompletion.added;
        let deleted = this._pendingCompletion.deleted;
        const completion = this._pendingCompletion.completion;
        const rangeEnd = normalizedChange.rangeOffset + normalizedChange.rangeLength;
        const cursorPosition = completion.range.startOffset + added;

        // For the deletion range, there are two cases:
        // 1. Some deletions are in the prefix: we "undo" additions, i.e. decrease the added count.
        // 2. Some deletions are in the suffix: we "consume" suffix deletions, i.e. increase the deleted count.
        // Note that unadded < 0 is impossible because of the post condition of normalizeChange,
        // but we check for it anyway.
        const newDeleted = rangeEnd - cursorPosition;
        const unadded = cursorPosition - normalizedChange.rangeOffset;
        if (newDeleted < 0 || unadded < 0) {
            this._logger.verbose(
                `Change would delete or unadd negative characters: ${rangeEnd} - ${cursorPosition} < 0
                    or ${cursorPosition} - ${normalizedChange.rangeOffset} < 0`
            );
            this._rejectPendingCompletion();
            return;
        }

        deleted += newDeleted;
        added -= unadded;

        // For the added text, we check that the text matches. The change can extend past the
        // completion even when accepting, since VSCode can add closing brackets/braces/etc.,
        // so we truncate to the same length.
        const fullCompletion = completion.completionText + completion.suffixReplacementText;
        const newAdded = normalizedChange.text.length;
        const nextText = fullCompletion.slice(added, added + newAdded);
        if (normalizedChange.text.slice(0, nextText.length) !== nextText) {
            this._logger.verbose(
                `Pending completion mismatch: ${normalizedChange.text} vs ${nextText}`
            );
            this._rejectPendingCompletion();
            return;
        }

        added += newAdded;

        // Check that the remaining suffix replacement text still contains the remaining skipped suffix.
        if (deleted < this._pendingCompletion.suffixMatches.length) {
            // nextMatchIndex is the index of the next skip char that hasn't been deleted yet.
            // added should be at most this index (equal means the user has typed all the chars
            // up to, but not including, this skip char).
            const nextMatchIndex = this._pendingCompletion.suffixMatches[deleted];
            if (added > completion.completionText.length + nextMatchIndex) {
                this._logger.verbose(
                    `Remaining skipped suffix is not a subsequence of the remaining suffix replacement text:
                    ${completion.skippedSuffix.slice(deleted)} !<= ${fullCompletion.slice(added)}`
                );
                this._rejectPendingCompletion();
                return;
            }
        }

        this._pendingCompletion.added = added;
        this._pendingCompletion.deleted = deleted;

        // If all the additions have been consumed or the user has consumed *more* than all
        // the deletions, we resolve the completion. Note that we accept the completion if all
        // additions and deletions have been consumed, even if the user has added or deleted
        // past the end of the completion, because VSCode may add closing brackets/braces/etc.
        if (
            this._pendingCompletion.added >= fullCompletion.length &&
            this._pendingCompletion.deleted >= completion.skippedSuffix.length
        ) {
            this._acceptPendingInlineCompletion();
        } else if (
            this._pendingCompletion.added >= fullCompletion.length ||
            this._pendingCompletion.deleted > completion.skippedSuffix.length
        ) {
            this._rejectPendingCompletion();
        }
    }

    // Normalize the change.
    // Postcondition: completion.range.startOffset <= rangeOffset <= completion.range.startOffset + added
    private _normalizeChange(
        change: vscode.TextDocumentContentChangeEvent
    ): NormalizedChange | undefined {
        if (!this._pendingCompletion) {
            return undefined;
        }

        const completion = this._pendingCompletion.completion;
        const added = this._pendingCompletion.added;
        const deleted = this._pendingCompletion.deleted;

        /**
         * If the user types `c`, our completion could be `onst foo = bar` at
         * offset 1.
         * Intellisense may offer the completion `const` at offset 0.
         *
         * When accepting the intellisense completion, the text document change
         * has range 0 - 5 with text `const`.
         *
         * To make sure this change aligns with our pending completion we
         * need to account for the `c` character already existing in the
         * document.
         */
        if (change.rangeOffset < completion.range.startOffset) {
            const rangeDiff = completion.range.startOffset - change.rangeOffset;
            if (rangeDiff > change.rangeLength) {
                return undefined;
            }
            // Delete the rangeDiff characters before completion.range.startOffset
            return {
                rangeLength: change.rangeLength - rangeDiff,
                rangeOffset: change.rangeOffset + rangeDiff,
                text: change.text.slice(rangeDiff),
            };
        }

        // There are two ways to represent skipping the suffix.
        // 1. Replace ")" with ")abc", i.e. delete and add back the chars to skip.
        // 2. Insert "abc" after ")", i.e. directly skip over the chars to skip.
        // This converts cases (2) into (1), by adding chars from skippedSuffix to range and text.
        const cursorPosition = completion.range.startOffset + added;
        if (change.rangeOffset > cursorPosition) {
            // We are in case (2): directly skip over the chars to skip
            const skippedChars = change.rangeOffset - cursorPosition;
            if (deleted + skippedChars > completion.skippedSuffix.length) {
                // We can't skip over more than the skippedSuffix
                return undefined;
            }
            // Add the next skippedChars chars from skippedSuffix
            return {
                rangeLength: change.rangeLength + skippedChars,
                rangeOffset: change.rangeOffset - skippedChars,
                text: completion.skippedSuffix.slice(deleted, deleted + skippedChars) + change.text,
            };
        }

        // No changes.
        return {
            rangeLength: change.rangeLength,
            rangeOffset: change.rangeOffset,
            text: change.text,
        };
    }

    private _rejectPendingCompletion() {
        if (!this._pendingCompletion) {
            return;
        }

        dispatchCompletionResolved({
            requestId: this._pendingCompletion.requestId,
            acceptedIdx: -1,
            document: this._pendingCompletion.document,
        });
        dispatchInlineCompletionVisibilityChange(false);

        this._logger.debug(`Rejecting completion: ${this._pendingCompletion.requestId}`);
        this._metricsReporter.reportResolution(
            this._pendingCompletion.requestId,
            this._pendingCompletion.emitTime,
            Date.now(),
            undefined
        );
        this._pendingCompletion = undefined;
    }

    private _acceptPendingInlineCompletion() {
        if (!this._pendingCompletion) {
            return;
        }

        dispatchCompletionResolved({
            requestId: this._pendingCompletion.requestId,
            acceptedIdx: 0,
            document: this._pendingCompletion.document,
        });
        dispatchInlineCompletionVisibilityChange(false);

        this._logger.debug(`Accepting completion: ${this._pendingCompletion.requestId}`);
        this._metricsReporter.reportResolution(
            this._pendingCompletion.requestId,
            this._pendingCompletion.emitTime,
            Date.now(),
            0
        );
        this._pendingCompletion = undefined;
    }
}

// Tracks a completion that is still in progress.
// Given a completion, e.g. text="foo", skippedSuffix="])", suffixReplacementText="]bar)baz",
// the user does not have to necessarily perform the whole change at once.
// We can split the change into deletions (of the suffix) and additions,
// e.g. delete "])" and add "foo]bar)baz". The sequence of additions must be in order,
// and the sequence of deletions must be in order, but additions and deletions can be
// interleaved so long as the remaining deletion is a subsequence of the addition.
interface PendingCompletionDetails extends CompletionRequest {
    // The original completion.
    completion: AugmentCompletion;

    // The number of chars from completion text + suffix replacement text that have been added.
    added: number;

    // The number of chars from skipped suffix that have been deleted.
    deleted: number;

    // The indices of last subsequence in suffix replacement text that matches the skipped suffix.
    suffixMatches: number[];

    emitTime: number;
}

interface NormalizedChange {
    rangeLength: number;
    rangeOffset: number;
    text: string;
}
