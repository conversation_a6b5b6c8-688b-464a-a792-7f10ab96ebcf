import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { isNotebookUri } from "../utils/notebook";
import { AugmentCompletion } from "./augment-completion";
import { CompletionRequest } from "./completions-model";

export class SuppressDeletedCompletions extends DisposableService {
    private _logger = getLogger("SuppressDeletedCompletions");
    private _inProgressDeletion: DeletionState | undefined = undefined;
    private _prevCompletions: AugmentCompletion[] | undefined = undefined;

    constructor() {
        super();

        this.addDisposables(
            vscode.workspace.onDidChangeTextDocument((event) => {
                this._onTextDocumentChange(event);
            }),
            vscode.window.onDidChangeActiveTextEditor((event) => {
                this._resetDeletions(event);
            })
        );
    }

    // _trackDeletions tracks the current deletion in a document.
    // However, `TextDocumentChangeEvent` does not let us know which characters
    // were deleted, so to reconstruct that we store the text of the document
    // before the current deletion.
    private _onTextDocumentChange(event: vscode.TextDocumentChangeEvent) {
        // We can get events for unrelated things such as the Output window.
        // Ignore those.
        if (event.document.uri.scheme !== "file" && !isNotebookUri(event.document.uri)) {
            return;
        }
        // We sometimes get empty events.  Ignore those.
        if (event.contentChanges.length === 0) {
            return;
        }

        if (
            // If we are not tracking deletions, start an empty tracking.
            this._inProgressDeletion === undefined ||
            // For now, we don't attempt to track multi-point edits.
            event.contentChanges.length !== 1 ||
            // We only track deletions.
            event.contentChanges[0].text.length > 0 ||
            // We only track deletions for the same document.
            event.document !== this._inProgressDeletion.document
        ) {
            this._inProgressDeletion = {
                document: event.document,
                preDeletionDocumentText: event.document.getText(),
                prevDeletionRange: undefined,
                prevDeletionText: undefined,
                curDocumentText: event.document.getText(),
            };
            this._prevCompletions = [];
            return;
        }

        if (
            this._inProgressDeletion.prevDeletionRange === undefined ||
            !this._areAdjacentDeletions(
                event.contentChanges[0].range,
                this._inProgressDeletion.prevDeletionRange
            )
        ) {
            // Note that we use the previous tracked deletion's curDocumentText, as its
            // preDeletionText was before a non-adjacent deletion.
            this._inProgressDeletion.preDeletionDocumentText =
                this._inProgressDeletion.curDocumentText;
        }

        // Figure out what text was removed
        const deletedText = this._inProgressDeletion.curDocumentText.substring(
            event.contentChanges[0].rangeOffset,
            event.contentChanges[0].rangeOffset + event.contentChanges[0].rangeLength
        );

        this._inProgressDeletion.prevDeletionRange = event.contentChanges[0].range;
        this._inProgressDeletion.prevDeletionText = deletedText;
        this._inProgressDeletion.curDocumentText = event.document.getText();
    }

    // Note that this is not commutative, as we rely on how VSCode reports
    // deletion ranges.
    private _areAdjacentDeletions(newDeletion: vscode.Range, prevDeletion: vscode.Range): boolean {
        return (
            newDeletion.end.isEqual(prevDeletion.start) ||
            newDeletion.start.isEqual(prevDeletion.start)
        );
    }

    private _resetDeletions(editor: vscode.TextEditor | undefined): void {
        this._prevCompletions = [];
        if (editor === undefined) {
            this._inProgressDeletion = undefined;
            return;
        }
        this._inProgressDeletion = {
            document: editor.document,
            preDeletionDocumentText: editor.document.getText(),
            prevDeletionRange: undefined,
            prevDeletionText: undefined,
            curDocumentText: editor.document.getText(),
        };
    }

    processRequest(
        completionRequest: CompletionRequest | undefined
    ): CompletionRequest | undefined {
        if (!completionRequest || !this._inProgressDeletion) {
            return completionRequest;
        }

        let filteredCompletions = completionRequest.completions;
        if (this._inProgressDeletion.document === completionRequest.document) {
            filteredCompletions = completionRequest.completions.filter((completion) => {
                // If the completion is exactly what the user just deleted, drop it.
                if (this._checkIfCompletionWasDeleted(completionRequest, completion)) {
                    this._logger.debug("Suppressing previously deleted completion");
                    return false;
                }

                // If user performed a forward deletion, suppress the completion.
                if (this._checkIfForwardDeletion(completionRequest, completion)) {
                    this._logger.debug("Suppressing completion due to forward deletion");
                    return false;
                }

                return true;
            });
        }

        // We deconstruct the completion request to ensure our
        // changes to the original completion request do not impact
        // the copy of the previous completion request.
        if (this._inProgressDeletion.document === completionRequest.document) {
            this._prevCompletions = [...completionRequest.completions];
        }
        completionRequest.completions = filteredCompletions;

        return completionRequest;
    }

    // If the completion is exactly what the user just deleted, drop it.
    private _checkIfCompletionWasDeleted(
        completionRequest: CompletionRequest,
        completion: AugmentCompletion
    ) {
        const document = completionRequest.document;

        const doucmentStart = document.getText(
            new vscode.Range(
                document.positionAt(0),
                document.positionAt(completion.range.startOffset)
            )
        );
        const documentEnd = document.getText(
            new vscode.Range(
                document.positionAt(completion.range.endOffset + completion.skippedSuffix.length),
                document.positionAt(document.getText().length)
            )
        );
        const textWithCompletion = doucmentStart + completion.completionText + documentEnd;
        // If the document content before any deletions matches the
        // the document with the new completion, do not show it.
        return this._inProgressDeletion!.preDeletionDocumentText === textWithCompletion;
    }

    private _checkIfForwardDeletion(
        completionRequest: CompletionRequest,
        completion: AugmentCompletion
    ) {
        if (this._inProgressDeletion!.prevDeletionRange) {
            // If the user performed a forward deletion (i.e. a
            // deletion using the `del` key), suppress the completion.
            const isMatchingCompletion = this._prevCompletions?.some((prevCompletion) => {
                // Ensure the previous completion and current completion are
                // in the same location.
                // Using end since a previous completion with
                // (start, end) range of (8, 10) will match with a new
                // completion of (10, 10)
                const prevComplStartPosition = this._inProgressDeletion?.document?.positionAt(
                    prevCompletion.range.endOffset
                );
                const completionStartPosition = completionRequest.document.positionAt(
                    completion.range.endOffset
                );
                if (
                    !prevComplStartPosition ||
                    !prevComplStartPosition.isEqual(completionStartPosition)
                ) {
                    return false;
                }

                // Ensure the previous completion and current completion
                // will display the same completion text to the user.
                const previousCompletionText = (
                    prevCompletion.completionText + prevCompletion.suffixReplacementText
                ).substring(prevCompletion.range.endOffset - prevCompletion.range.startOffset);
                // Trim the end of the new completion text to avoid
                // including the newly deleted character(s)
                const currentCompletionText =
                    completion.completionText + completion.suffixReplacementText;

                const possibleCompletions = [previousCompletionText];
                if (this._inProgressDeletion?.prevDeletionText) {
                    possibleCompletions.push(
                        previousCompletionText + this._inProgressDeletion?.prevDeletionText
                    );
                }
                // Check if the current completion matches the previous
                // completion exactly OR the prev completion + what was just
                // deleted.
                // If not, then do not filter the new completion as it's new.
                if (!possibleCompletions.some((value) => value === currentCompletionText)) {
                    return false;
                }

                // Check if the previous completion began with the same text as
                // the latest deletion start. This ensures that the
                // latest deletion is a forward deletion.
                const prevComplEndPosition = this._inProgressDeletion?.document.positionAt(
                    prevCompletion.range.endOffset
                );
                return (
                    prevComplEndPosition &&
                    this._inProgressDeletion?.prevDeletionRange?.start &&
                    prevComplEndPosition.isEqual(this._inProgressDeletion.prevDeletionRange.start)
                );
            });

            return isMatchingCompletion;
        }
    }
}

/**
 * DeletionState conceptually tracks the current deletion in the document.  It
 * coalesces adjacent deletions into a singler larger deletion as it tracks,
 * but it reset on multi-point edits, insertions, new documents, deletions in
 * different places, and so forth.
 *
 * document: the document that was being edited
 * preDeletionDocumentText: the text before the current deletion
 * prevDeletionRange: the location of previous deletion
 * curDocumentText: the current text of the document
 */
export type DeletionState = {
    document: vscode.TextDocument;
    preDeletionDocumentText: string;
    prevDeletionRange: vscode.Range | undefined;
    prevDeletionText: string | undefined;
    curDocumentText: string;
};
