/**
 * This file initializes the logger before anything else.
 * It should be imported first in the extension.ts file.
 */
import { setLibraryLogger } from "@augment-internal/sidecar-libs/src/logging";
import * as vscode from "vscode";
import { createLogger, format, transports } from "winston";
import Transport from "winston-transport";

// Initialize the logger
const outputChannel = vscode.window.createOutputChannel("Augment", {
    log: true,
});

// Custom transport for VSCode output channel
class VSCodeOutputChannelTransport extends Transport {
    private _logFns = new Map<string, (message: string, ...args: any[]) => void>();

    constructor(private outputChannel: vscode.LogOutputChannel) {
        super();
        /* eslint-disable @typescript-eslint/unbound-method */
        this._logFns.set("info", outputChannel.info);
        this._logFns.set("debug", outputChannel.debug);
        this._logFns.set("warn", outputChannel.warn);
        this._logFns.set("error", outputChannel.error);
        this._logFns.set("verbose", outputChannel.trace);
        /* eslint-enable @typescript-eslint/unbound-method */
    }

    log(info: any, callback: () => void) {
        setImmediate(() => {
            this.emit("logged", info);
        });

        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        const msg = [info.prefix ? `'${info.prefix}'` : "", info.message].join(" ");
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
        const fn = this._logFns.get(info.level);
        if (fn) {
            fn(msg);
        } else {
            this.outputChannel.appendLine(msg);
        }

        callback();
    }
}

const logEndpoints: any[] = [new VSCodeOutputChannelTransport(outputChannel)];

if (process.env.CONSOLE_LOG_LEVEL) {
    logEndpoints.push(new transports.Console({ level: process.env.CONSOLE_LOG_LEVEL }));
}

const logger = createLogger({
    level: "debug",
    exitOnError: false,
    format: format.combine(
        format.timestamp({
            format: "YYYY-MM-DD HH:mm:ss.SSS",
        }),
        format.printf(
            (info) => `${info.timestamp} [${info.level}] '${info.prefix}': ${info.message}`
        )
    ),
    transports: logEndpoints,
});

// Set the logger in the sidecar-libs
setLibraryLogger(logger);

// Export the logger for use in the extension
export { logger };
