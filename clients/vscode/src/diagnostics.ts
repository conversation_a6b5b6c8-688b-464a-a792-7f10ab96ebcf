import * as vscode from "vscode";

import { getLogger } from "./logging";
import { DisposableService } from "./utils/disposable-service";
import { isNotebookUri } from "./utils/notebook";
import { TextDocumentView, viewTextDocument } from "./workspace/view-text-document";

export class TimedDiagnostics {
    constructor(
        public readonly uri: vscode.Uri,
        public readonly diagnostic: vscode.Diagnostic,
        public readonly time: Date
    ) {}
}

export class PositionedDiagnostics {
    constructor(
        public readonly uri: vscode.Uri,
        public readonly diagnostic: vscode.Diagnostic,
        public readonly charStart: number,
        public readonly charEnd: number
    ) {}
}

function _areSameDiagnostics(a: vscode.Diagnostic, b: vscode.Diagnostic): boolean {
    return a.message === b.message && a.severity === b.severity && a.range.isEqual(b.range);
}

// Note that this only tracks errors and warnings from non-notebook files.
export class DiagnosticsManager extends DisposableService {
    /** The maximum number of diagnostics to track per file. */
    private static readonly _maxDiagnosticsPerFile = 10;

    private readonly _diagnostics = new Map<string, TimedDiagnostics[]>();

    private _logger = getLogger("DiagnosticsManager");

    constructor() {
        super();

        this.addDisposable(
            vscode.languages.onDidChangeDiagnostics((event: vscode.DiagnosticChangeEvent) => {
                const now = new Date();
                for (const uri of event.uris) {
                    // Ignore diagnostics in notebook files.
                    // Note that if we want to handle them later, we will need
                    // to group the uris we get below by their path, as right
                    // now each cell's results would overwrite the previous
                    // cell's results, plus fix line offsets across cells.
                    if (isNotebookUri(uri)) {
                        continue;
                    }
                    // We have to figure out which of these diagnostics are new.
                    const existingDiags = this._diagnostics.get(uri.path);
                    const fileDiags = vscode.languages
                        .getDiagnostics(uri)
                        // Keep only some diagnostics to avoid slowdown.
                        // We could sort these if we wanted.
                        .slice(0, DiagnosticsManager._maxDiagnosticsPerFile);
                    const newDiags = [];
                    for (const diag of fileDiags) {
                        if (
                            diag.severity !== vscode.DiagnosticSeverity.Error &&
                            diag.severity !== vscode.DiagnosticSeverity.Warning
                        ) {
                            // We don't track lower priority diagnostics.
                            continue;
                        }
                        // TODO: If we're worried about the performance of this,
                        // we could make this a map (with a safe key) or only
                        // store the most recent diagnostics.
                        // We now only keep a few diagnostics to avoid this.
                        const existingDiag = existingDiags?.find((d) =>
                            _areSameDiagnostics(d.diagnostic, diag)
                        );
                        if (existingDiag === undefined) {
                            newDiags.push(new TimedDiagnostics(uri, diag, now));
                        } else {
                            // If the error already existed, keep its old time.
                            newDiags.push(existingDiag);
                        }
                    }
                    this._diagnostics.set(uri.path, newDiags);
                }
            })
        );
    }

    // getMostRecentDiagnostics returns the most recent diagnostics, sorted by time.
    public async getMostRecentDiagnostics(
        maxDiagnostics: number,
        maxDiagnosticsPerFile: number,
        pathPrefix: string | undefined = undefined
    ): Promise<PositionedDiagnostics[]> {
        const allDiagnostics = Array.from(this._diagnostics.entries()).flatMap(
            ([path, diagnostics]) => {
                if (pathPrefix !== undefined && !path.startsWith(pathPrefix)) {
                    return [];
                }
                return diagnostics;
            }
        );
        // TODO: If we're worried about the performance of this, we could make
        // the parameters into fields and only store the most recent diagnostics.
        const sortedDiagnostics = allDiagnostics.sort((a, b) => {
            const cmp = b.time.getTime() - a.time.getTime();
            if (cmp !== 0) {
                return cmp;
            }
            // Break ties by severity.
            return a.diagnostic.severity - b.diagnostic.severity;
        });

        const fileToDiagnosticCount = new Map<string, number>();
        const result: PositionedDiagnostics[] = [];
        const files = new Map<string, TextDocumentView>();
        for (const diag of sortedDiagnostics) {
            if (
                diag.diagnostic.range.start.line < 0 ||
                diag.diagnostic.range.start.line > diag.diagnostic.range.end.line
            ) {
                this._logger.debug(
                    `Ignoring invalid diagnostic ${diag.diagnostic.message} in ${diag.uri.path} at ${diag.diagnostic.range.start.line}:${diag.diagnostic.range.end.line}`
                );
                continue;
            }
            // Ensure we don't send too many diagnostics from a single file.
            const count = fileToDiagnosticCount.get(diag.uri.path) ?? 0;
            if (count < maxDiagnosticsPerFile) {
                // VSCode's APIs can always give stale diagnostics, e.g., if the user
                // deletes code containing a diagnostic and then immediately triggers
                // this code. Let's ensure all diagnostics are in the current file.
                // As an optimization, we only open each file once.
                if (!files.has(diag.uri.path)) {
                    try {
                        files.set(diag.uri.path, await viewTextDocument(diag.uri));
                    } catch {
                        // Ignore diagnostics in files we can't open. Some extensions,
                        // like the Bazel extension(?), appear to do this for failing
                        // BUILD targets.
                        this._logger.debug(`Failed to open document ${diag.uri.fsPath}.`);
                        continue;
                    }
                }

                const document = files.get(diag.uri.path);
                if (!document) {
                    continue;
                }

                // TODO (moogi) : improve this mechanism (AU-6826)
                if (diag.diagnostic.range.end.line >= document.lineCount) {
                    this._logger.debug(
                        `Ignoring stale diagnostic ${diag.diagnostic.message} in ${diag.uri.path} at ${diag.diagnostic.range.start.line}:${diag.diagnostic.range.end.line}`
                    );
                    continue;
                }
                // translate to char range
                const charStart = document.offsetAt(diag.diagnostic.range.start);
                const charEnd = document.offsetAt(diag.diagnostic.range.end);
                result.push(
                    new PositionedDiagnostics(diag.uri, diag.diagnostic, charStart, charEnd)
                );
                fileToDiagnosticCount.set(diag.uri.path, count + 1);
                if (result.length >= maxDiagnostics) {
                    break;
                }
            }
        }
        return result;
    }
}
