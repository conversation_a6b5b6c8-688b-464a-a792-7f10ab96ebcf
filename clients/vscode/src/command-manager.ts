import assert from "assert";
import * as vscode from "vscode";

import { AugmentConfigListener } from "./augment-config-listener";
import { type AugmentLogger, getLogger } from "./logging";
import { DisposableService } from "./utils/disposable-service";

export enum CommandType {
    // Debug commands are internal commands that are only available when
    // the hidden enableDebugFeatures setting is enabled.
    debug = "debug",

    // Public commands are commands that the user can run from the command
    // palette or via keybindings.
    public = "public",

    // Private commands are commands that the extension triggers,
    // these are not available to the user.
    private = "private",
}

export class CommandManager extends DisposableService {
    protected _commands: Map<string, AugmentCommand> = new Map();
    protected _groups: Array<CommandGroup> = [];
    private _logger: AugmentLogger;

    constructor(private _config: AugmentConfigListener) {
        super();
        this._logger = getLogger("CommandManager");
        this.addDisposable(
            new vscode.Disposable(() => {
                this._commands.forEach((cmd, key) => {
                    cmd.unregister();
                    this._commands.delete(key);
                });
            })
        );
        this.addDisposable(
            this._config.onDidChange(() => {
                this.register(this.allCommands);
            })
        );
    }

    register(commands: Array<AugmentCommand>) {
        for (const c of commands) {
            if (this._commands.has(c.commandID)) {
                // Existing command, update it
                this._commands.get(c.commandID)?.update(this._config);
                continue;
            }

            // New command, register it
            c.register(this._config);
            this._commands.set(c.commandID, c);
        }
    }

    registerGroup(name: string, commands: Array<AugmentCommand>) {
        this._logger.debug(`Registering group '${name}' with ${commands.length} commands.`);
        this._groups.push({ name, commands });
        this.register(commands);
    }

    get availableCommands(): Array<AugmentCommand> {
        return Array.from(this._commands.values()).filter((cmd) => {
            return cmd.isRegistered && cmd.canRun();
        });
    }

    get availableCommandGroups(): Array<CommandGroup> {
        const availableGroups: Array<CommandGroup> = [];
        for (let i = 0; i < this._groups.length; i++) {
            const { name, commands } = this._groups[i];
            const availableCommands = commands.filter((cmd) => cmd.isRegistered && cmd.canRun());
            if (availableCommands.length === 0) {
                continue;
            }

            // If the last group is the same as the current group, append to it.
            if (
                availableGroups.length > 0 &&
                availableGroups[availableGroups.length - 1].name === name
            ) {
                availableGroups[availableGroups.length - 1].commands.push(...availableCommands);
            } else {
                availableGroups.push({ name, commands: availableCommands });
            }
        }
        return availableGroups;
    }

    get allCommands(): Array<AugmentCommand> {
        return Array.from(this._commands.values());
    }
}

export abstract class AugmentCommand extends DisposableService {
    private _registration?: vscode.Disposable;
    private _logger = getLogger("AugmentCommand");
    public static readonly commandID: string;
    protected _commandID?: string;
    public get commandID(): string {
        assert(
            typeof this._commandID === "string",
            `commandID must be defined on subclass ${this.constructor.name}`
        );
        return this._commandID;
    }
    abstract readonly type: CommandType;

    constructor(
        private _title: string | (() => string) | undefined = undefined,
        private _showInActionPanel: boolean = true
    ) {
        super();

        // eslint-disable-next-line @typescript-eslint/naming-convention
        const TypedClass = this.constructor as typeof AugmentCommand;
        if (typeof TypedClass.commandID === "string") {
            this._commandID = TypedClass.commandID;
        }
        this.addDisposable({ dispose: () => this.unregister() });
    }

    abstract run(...args: any[]): void;

    get title(): string | undefined {
        if (typeof this._title === "string") {
            return this._title;
        } else if (typeof this._title === "function") {
            return this._title();
        }
        return undefined;
    }

    get showInActionPanel(): boolean {
        return this._showInActionPanel;
    }

    get isRegistered(): boolean {
        return this._registration !== undefined;
    }

    canRun(): boolean {
        return true;
    }

    // Register will register the command with VSCode, ensuring it's registered
    // only once and only when the user config allows.
    register(config: AugmentConfigListener) {
        if (this._registration !== undefined) {
            // Already registered
            return;
        }

        // If the new command is debug and debug features are disabled,
        // don't register it with VSCode.
        if (this.type === CommandType.debug && !config.config.enableDebugFeatures) {
            return;
        }

        this._registration = vscode.commands.registerCommand(this.commandID, (...args: any[]) => {
            if (!this.canRun()) {
                this._logger.debug(
                    `Not running '${this.commandID}' command with type ${this.type}.`
                );
                return;
            }
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            this.run(...args);
        });
    }

    update(config: AugmentConfigListener) {
        if (this.type !== CommandType.debug) {
            // We only need to check the status of debug commands.
            return;
        }

        if (config.config.enableDebugFeatures) {
            this.register(config);
        } else {
            this.dispose();
        }
    }

    unregister() {
        this._registration?.dispose();
        this._registration = undefined;
    }
}

interface CommandGroup {
    name: string;
    commands: Array<AugmentCommand>;
}
