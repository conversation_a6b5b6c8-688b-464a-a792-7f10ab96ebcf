import { sendMessageToSidecar } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import * as vscode from "vscode";

import { AugmentConfigListener } from "./augment-config-listener";
import { getLogger } from "./logging";
import { PanelWebviewBase } from "./utils/panel-webview-base";
import { createAsyncMsgHandlerFromWebview } from "./utils/webviews/messaging-helper";
import { openFileFromMessage } from "./utils/webviews/open-file";
import { WebViewMessage, WebViewMessageType } from "./webview-providers/webview-messages";

/**
 * Provider for Augment Rules editor.
 *
 * This custom editor is used to display and edit Augment Rules files.
 *
 * This provider implements:
 *
 * - Setting up the initial webview for a custom editor.
 * - Loading scripts and styles in a custom editor.
 * - Synchronizing changes between a text document and a custom editor.
 */
export class AugmentRulesEditorProvider implements vscode.CustomTextEditorProvider {
    public static register(
        context: vscode.ExtensionContext,
        configListener: AugmentConfigListener
    ): vscode.Disposable {
        const provider = new AugmentRulesEditorProvider(context, configListener);
        const providerRegistration = vscode.window.registerCustomEditorProvider(
            AugmentRulesEditorProvider.viewType,
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true,
                },
            }
        );
        return providerRegistration;
    }

    private static readonly viewType = "rules.augment";
    private readonly logger = getLogger("AugmentRulesEditorProvider");
    private webviewLoader: PanelWebviewBase | undefined;

    constructor(
        public readonly context: vscode.ExtensionContext,
        private readonly configListener: AugmentConfigListener
    ) {}

    /**
     * Called when our custom editor is opened.
     */
    public async resolveCustomTextEditor(
        document: vscode.TextDocument,
        webviewPanel: vscode.WebviewPanel,
        _token: vscode.CancellationToken
    ): Promise<void> {
        // If loading spinner , provide a fallback HTML
        webviewPanel.webview.options = {
            enableScripts: true,
        };
        webviewPanel.webview.html = `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Augment Rules</title>
                </head>
                <body>
                    <div id="app">
                        <p>Loading Augment Rules...</p>
                    </div>
                </body>
                </html>
        `;

        // Create a webview loader to handle loading the HTML content
        this.webviewLoader = new PanelWebviewBase("rules.html", webviewPanel.webview);

        const asyncMsgHandler = createAsyncMsgHandlerFromWebview(webviewPanel.webview);
        // Register a sidecar handler for messages handled by sidecar/libs
        asyncMsgHandler.registerSidecarHandler((msg, postMessage) => {
            sendMessageToSidecar(msg, postMessage);
        });

        try {
            await this.webviewLoader.loadHTML(this.context.extensionUri);
        } catch (error) {
            this.logger.error(`Failed to load rules.html webview: ${String(error)}`);

            void vscode.window.showErrorMessage(`Failed to load rules editor: ${String(error)}`);
            return;
        }

        // Function to send content to the webview
        const updateWebview = () => {
            this.logger.debug("Sending content to rules webview");

            void webviewPanel.webview.postMessage({
                type: WebViewMessageType.loadFile,
                data: {
                    repoRoot: undefined,
                    content: document.getText(),
                    pathName: document.uri.fsPath,
                },
            });
        };

        // Listen for webview ready message
        webviewPanel.webview.onDidReceiveMessage((message: WebViewMessage) => {
            // Handle standard WebViewMessage types
            switch (message.type) {
                case WebViewMessageType.rulesLoaded: {
                    this.logger.debug("Rules webview is ready, sending initial content");
                    updateWebview();
                    break;
                }
                case WebViewMessageType.openFile: {
                    void openFileFromMessage({
                        repoRoot: message.data.repoRoot,
                        pathName: message.data.pathName,
                        openTextDocument: true,
                    });
                    break;
                }
                case WebViewMessageType.openSettingsPage: {
                    // If a section is specified, pass it as a parameter to the command
                    const section = message.data;

                    if (section) {
                        void vscode.commands.executeCommand(
                            "vscode-augment.showSettingsPanel",
                            section
                        );
                    } else {
                        void vscode.commands.executeCommand("vscode-augment.showSettingsPanel");
                    }
                    break;
                }
            }
        });

        // Hook up event handlers for document changes
        const changeDocumentSubscription = vscode.workspace.onDidChangeTextDocument((e) => {
            if (e.document.uri.toString() === document.uri.toString()) {
                updateWebview();
            }
        });

        // Make sure we get rid of the listener when our editor is closed.
        webviewPanel.onDidDispose(() => {
            changeDocumentSubscription.dispose();
            if (this.webviewLoader) {
                this.webviewLoader.dispose();
                this.webviewLoader = undefined;
            }
        });

        // Send the initial document content to the webview
        // We'll also try to send it immediately, but the webview might not be ready yet
        updateWebview();
    }
}
