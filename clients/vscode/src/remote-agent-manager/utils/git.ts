/**
 * Parse a remote branch reference and extract the remote name and local branch name
 * @param branchRef - Git branch reference (e.g., "refs/remotes/origin/main", "upstream/main", "main")
 * @returns Object with remote and branch names, or null if not a remote reference
 */
export function parseRemoteBranchName(
    branchRef: string
): { remote: string; branch: string } | null {
    // Handle full refs like "refs/remotes/origin/main"
    const fullRefMatch = branchRef.match(/^refs\/remotes\/([^/]+)\/(.+)$/);
    if (fullRefMatch) {
        return { remote: fullRefMatch[1], branch: fullRefMatch[2] };
    }

    // Handle short refs like "origin/main", "upstream/develop", etc.
    // Be more careful to avoid false positives with branch names that contain slashes
    const shortRefMatch = branchRef.match(/^([^/]+)\/(.+)$/);
    if (shortRefMatch) {
        const [, remoteName, branchName] = shortRefMatch;
        // Common remote names to help identify actual remotes vs branch names with slashes
        const commonRemotes = ["origin", "upstream", "fork", "github", "gitlab", "bitbucket"];
        if (commonRemotes.includes(remoteName) || remoteName.includes(".")) {
            return { remote: remoteName, branch: branchName };
        }
    }

    // Not a remote branch reference
    return null;
}

/**
 * Extract the local branch name from a git branch reference
 * Handles various git ref formats and removes remote prefixes
 * @param branchRef - Git branch reference
 * @returns Local branch name without remote prefix
 */
export function getLocalBranchName(branchRef: string): string {
    // Handle symbolic reference format like "origin/HEAD -> origin/main" or "upstream/HEAD -> upstream/develop"
    const symbolicMatch = branchRef.match(/^[^/]+\/HEAD\s*->\s*(.+)$/);
    if (symbolicMatch) {
        // Recursively parse the target reference
        return getLocalBranchName(symbolicMatch[1]);
    }

    const parsed = parseRemoteBranchName(branchRef);
    return parsed ? parsed.branch : branchRef;
}
