import {
    Diagnostic,
    DiagnosticSeverity,
    getWorkspaceFolder,
    languages,
    publishWorkspaceFoldersChange,
    Range,
    TextDocument,
    TextEditor,
    Uri,
    window,
    workspace,
} from "../__mocks__/vscode-mocks";
import { DiagnosticsManager, PositionedDiagnostics } from "../diagnostics";
import { WorkspaceManager } from "../workspace/workspace-manager";
import {
    awaitWorkspaceState,
    createSourceFolderFiles,
    defaultWorkspaceFolder,
    defaultWorkspaceState,
    WorkspaceManagerTestKit,
} from "./workspace/workspace-manager-test-kit";

class DiagnosticsManagerTestKit extends WorkspaceManagerTestKit {
    static readonly workspaceFolder = defaultWorkspaceFolder;
    static readonly folderState = defaultWorkspaceState;

    public workspaceManager: WorkspaceManager;

    static async create() {
        const kit = new DiagnosticsManagerTestKit();
        await kit.awaitStartup();
        return kit;
    }

    private constructor() {
        super();

        createSourceFolderFiles(DiagnosticsManagerTestKit.workspaceFolder);
        const workspaceFolders = [
            getWorkspaceFolder(DiagnosticsManagerTestKit.workspaceFolder.folderRootUri),
        ];
        publishWorkspaceFoldersChange(workspaceFolders);

        this.workspaceManager = this.createWorkspaceManager();
        this.addDisposable(this.workspaceManager);
    }

    public get folderRootUri(): Uri {
        return DiagnosticsManagerTestKit.workspaceFolder.folderRootUri;
    }

    public async awaitStartup() {
        await awaitWorkspaceState(this.workspaceManager, DiagnosticsManagerTestKit.folderState);
    }
}

describe("DiagnosticsManager", () => {
    let diagnosticsManager: DiagnosticsManager;
    let kit: DiagnosticsManagerTestKit;
    let uriA: Uri;
    let uriB: Uri;

    beforeEach(async () => {
        jest.useFakeTimers();
        kit = await DiagnosticsManagerTestKit.create();

        uriA = Uri.joinPath(kit.folderRootUri, "/example/a");
        const outputDocA = new TextDocument(uriA, "line0\nline1\nline2\nline3\nline4\n");
        uriB = Uri.joinPath(kit.folderRootUri, "/example/b");
        const outputDocB = new TextDocument(uriB, "line0\nline1\nline2\nline3\nline4\n");
        const editorA = new TextEditor(outputDocA);
        window.activeTextEditor = editorA;
        workspace.textDocuments = [outputDocA, outputDocB];

        diagnosticsManager = new DiagnosticsManager();
    });

    afterEach(() => {
        jest.useRealTimers();
        kit.dispose();
        diagnosticsManager.dispose();
    });

    test("no diagnostics", async () => {
        expect(await diagnosticsManager.getMostRecentDiagnostics(10, 10)).toEqual([]);
    });

    test("simple diagnostics", async () => {
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(10, 10)).toEqual([
            {
                diagnostic: {
                    message: "test",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(0, 0, 0, 1),
                } as Diagnostic,
                charEnd: 1,
                charStart: 0,
                uri: uriA,
            } as PositionedDiagnostics,
        ]);
    });

    test("max tracked diagnostics", async () => {
        const orig = jest.replaceProperty(DiagnosticsManager as any, "_maxDiagnosticsPerFile", 1);
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test0", DiagnosticSeverity.Error),
            new Diagnostic(new Range(1, 0, 0, 1), "test1", DiagnosticSeverity.Error),
            new Diagnostic(new Range(2, 0, 0, 1), "test2", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(410, 10)).toHaveLength(1);

        orig.restore();
    });

    test("max diagnostics", async () => {
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test0", DiagnosticSeverity.Error),
            new Diagnostic(new Range(1, 0, 0, 1), "test1", DiagnosticSeverity.Error),
            new Diagnostic(new Range(2, 0, 0, 1), "test2", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(1, 1)).toHaveLength(1);
    });

    test("max diagnostics per file", async () => {
        jest.spyOn(languages, "getDiagnostics").mockImplementation((uri) => {
            if (uri === uriA) {
                return [
                    new Diagnostic(new Range(0, 0, 0, 1), "testa0", DiagnosticSeverity.Error),
                    new Diagnostic(new Range(0, 0, 1, 2), "testa1", DiagnosticSeverity.Error),
                ];
            }
            if (uri === uriB) {
                return [
                    new Diagnostic(new Range(0, 0, 0, 1), "testb0", DiagnosticSeverity.Error),
                    new Diagnostic(new Range(0, 0, 1, 2), "testb1", DiagnosticSeverity.Error),
                ];
            }
            return [];
        });

        languages.diagnosticsChanged.fire({ uris: [uriA, uriB] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(10, 1)).toEqual([
            {
                diagnostic: {
                    message: "testa0",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(0, 0, 0, 1),
                } as Diagnostic,
                charEnd: 1,
                charStart: 0,
                uri: uriA,
            } as PositionedDiagnostics,
            {
                diagnostic: {
                    message: "testb0",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(0, 0, 0, 1),
                } as Diagnostic,
                charEnd: 1,
                charStart: 0,
                uri: uriB,
            } as PositionedDiagnostics,
        ]);
    });

    test("sorted diagnostics", async () => {
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test0", DiagnosticSeverity.Warning),
            new Diagnostic(new Range(1, 0, 0, 1), "test1", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(1, 1)).toEqual([
            {
                diagnostic: {
                    message: "test1",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(1, 0, 0, 1),
                },
                charEnd: 6,
                charStart: 1,
                uri: uriA,
            },
        ]);
    });

    test("ignore low priority diagnostics", async () => {
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test0", DiagnosticSeverity.Information),
            new Diagnostic(new Range(1, 0, 0, 1), "test1", DiagnosticSeverity.Hint),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(42, 42)).toEqual([]);
    });

    test("forget old diagnostics", async () => {
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test old", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });
        jest.advanceTimersByTime(1);
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test new", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });
        jest.advanceTimersByTime(1);

        expect(await diagnosticsManager.getMostRecentDiagnostics(42, 42)).toEqual([
            {
                diagnostic: {
                    message: "test new",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(0, 0, 0, 1),
                } as Diagnostic,
                charEnd: 1,
                charStart: 0,
                uri: uriA,
            } as PositionedDiagnostics,
        ]);
    });

    test("use new diagnostics", async () => {
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test old", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });
        jest.advanceTimersByTime(1);
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test new", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriB] });
        jest.advanceTimersByTime(1);

        expect(await diagnosticsManager.getMostRecentDiagnostics(1, 1)).toEqual([
            {
                diagnostic: {
                    message: "test new",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(0, 0, 0, 1),
                } as Diagnostic,
                charEnd: 1,
                charStart: 0,
                uri: uriB,
            } as PositionedDiagnostics,
        ]);
    });

    test("remember old diagnostics", async () => {
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test old", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });
        jest.advanceTimersByTime(1);
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test new", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriB] });
        jest.advanceTimersByTime(1);
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test old", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriA] });
        jest.advanceTimersByTime(1);

        expect(await diagnosticsManager.getMostRecentDiagnostics(1, 1)).toEqual([
            {
                diagnostic: {
                    message: "test new",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(0, 0, 0, 1),
                } as Diagnostic,
                charEnd: 1,
                charStart: 0,
                uri: uriB,
            } as PositionedDiagnostics,
        ]);
    });

    test("path filter", async () => {
        jest.spyOn(languages, "getDiagnostics").mockImplementation((uri) => {
            if (uri === uriA) {
                return [new Diagnostic(new Range(0, 0, 0, 1), "testa", DiagnosticSeverity.Error)];
            }
            if (uri === uriB) {
                return [new Diagnostic(new Range(0, 0, 0, 1), "testb", DiagnosticSeverity.Error)];
            }
            return [];
        });
        languages.diagnosticsChanged.fire({ uris: [uriA, uriB] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(42, 42, uriA.fsPath)).toEqual([
            {
                diagnostic: {
                    message: "testa",
                    severity: DiagnosticSeverity.Error,
                    range: new Range(0, 0, 0, 1),
                } as Diagnostic,
                charEnd: 1,
                charStart: 0,
                uri: uriA,
            } as PositionedDiagnostics,
        ]);
    });

    test("diagnostics in non-existent files", async () => {
        const uriNonExistent = Uri.joinPath(kit.folderRootUri, "/non-existent");
        jest.spyOn(languages, "getDiagnostics").mockReturnValue([
            new Diagnostic(new Range(0, 0, 0, 1), "test", DiagnosticSeverity.Error),
        ]);
        languages.diagnosticsChanged.fire({ uris: [uriNonExistent] });

        expect(await diagnosticsManager.getMostRecentDiagnostics(10, 10)).toEqual([]);
    });
});
