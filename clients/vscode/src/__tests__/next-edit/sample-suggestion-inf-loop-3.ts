export const suggestion = {
    descriptionOutput: {
        logProbs: [-0.69921875, -0.0023651123, -0.036376953, -0.7265625, -0.0000150203705],
        offsets: [0, 6, 17, 25, 31],
        text: "Import additional metrics flags<|eot_id|>",
        tokenIds: [11772, 5217, 17150, 8202, 128009],
    },
    descriptionPrompt: {
        logProbs: [],
        offsets: [
            0, 17, 36, 42, 59, 61, 64, 68, 70, 80, 88, 91, 94, 99, 103, 105, 109, 114, 117, 120,
            128, 137, 149, 154, 158, 163, 170, 176, 180, 189, 192, 198, 209, 214, 219, 220, 224,
            232, 235, 240, 252, 255, 258, 263, 269, 280, 288, 299, 303, 311, 314, 318, 325, 328,
            332, 340, 343, 347, 352, 356, 357, 367, 386, 390, 407, 409, 413, 416, 420, 425, 432,
            442, 445, 452, 457, 462, 464, 466, 468, 471, 480, 484, 490, 497, 501, 503, 509, 515,
            518, 519, 522, 531, 535, 541, 548, 552, 554, 560, 566, 569, 570, 572, 574, 576, 577,
            579, 581, 583, 584, 586, 590, 594, 600, 602, 606, 608, 612, 618, 621, 626, 632, 638,
            640, 644, 650, 652, 656, 658, 662, 668, 671, 675, 679, 683, 688, 689, 696, 700, 703,
            707, 713, 715, 725, 727, 738, 742, 744, 748, 754, 756, 762, 764, 770, 772, 776, 782,
            784, 791, 799, 804, 806, 813, 820, 826, 828, 832, 838, 840, 850, 858, 860, 866, 868,
            872, 878, 880, 888, 897, 905, 907, 915, 921, 928, 930, 934, 940, 942, 949, 952, 956,
            959, 963, 967, 971, 975, 977, 978, 983, 986, 992, 996, 1000, 1002, 1005, 1006, 1009,
            1015, 1017, 1024, 1028, 1029, 1032, 1037, 1043, 1050, 1051, 1056, 1059, 1063, 1067,
            1071, 1075, 1077, 1078, 1083, 1086, 1092, 1096, 1100, 1102, 1103, 1107, 1109, 1115,
            1118, 1123, 1125, 1126, 1129, 1133, 1135, 1139, 1145, 1148, 1153, 1154, 1158, 1161,
            1166, 1171, 1173, 1174, 1177, 1181, 1187, 1189, 1194, 1203, 1205, 1210, 1218, 1220,
            1224, 1230, 1232, 1238, 1242, 1248, 1250, 1254, 1260, 1262, 1269, 1273, 1277, 1282,
            1284, 1291, 1294, 1298, 1307, 1309, 1311, 1315, 1321, 1323, 1330, 1332, 1338, 1342,
            1346, 1350, 1354, 1356, 1359, 1361, 1363, 1369, 1371, 1377, 1381, 1390, 1395, 1399,
            1411, 1415, 1419, 1427, 1432, 1433, 1439, 1442, 1447, 1452, 1453, 1455, 1461, 1463,
            1468, 1471, 1475, 1482, 1487, 1491, 1500, 1504, 1508, 1510, 1518, 1524, 1529, 1533,
            1535, 1545, 1564, 1573, 1590,
        ],
        text: "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a developer working in my codebase.\nYour task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\nHere is the code change delimited by triple backticks:\n```\n--- services/api_proxy/server/src/handler_utils.rs\n+++ services/api_proxy/server/src/handler_utils.rs\n@@ -18,13 +18,15 @@\n use crate::base::blob_names as blob_names_proto;\n use crate::base::diff_utils::{GranularEditEvent, SingleEdit};\n use crate::completion::ReplacementText;\n use crate::config::Config;\n use crate::content_manager_util::ContentManagerClient;\n-use crate::generation_clients::Client;\n use crate::handlers_external_sources::ExternalSourceClients;\n-use crate::metrics::{HANDLER_LATENCY_COLLECTOR, HANDLER_RETRY_COLLECTOR};\n+use crate::metrics::{\n+    NONE_RETRY_POLICY, HANDLER_LATENCY_COLLECTOR, HANDLER_RETRY_COLLECTOR, RETRY_DELAY_MS_FLAG,\n+    RETRY_MAX_DELAY_MS_FLAG, MAX_RETRIES_FLAG,\n+};\n use crate::model_registry::ModelRegistry;\n use crate::public_api_proto;\n use crate::request_insight_util::RequestInsightPublisher;\n \n use crate::metrics::STREAM_LATENCY_COLLECTOR;\n\n```\nDirectly start the response with the description and say nothing else. Write no more than 10 words.\nFocus on the fields that are changing but don't include where they are.\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n",
        tokenIds: [
            128000, 128006, 9125, 128007, 271, 2675, 527, 264, 16131, 3318, 304, 856, 2082, 3231,
            627, 7927, 3465, 374, 311, 8819, 9959, 2038, 505, 279, 2082, 2349, 3770, 323, 7664, 433,
            1701, 48696, 19120, 1376, 13, 578, 7580, 315, 701, 4096, 374, 311, 1520, 1023, 13707,
            6288, 3619, 1401, 13878, 315, 279, 2349, 304, 279, 2317, 315, 279, 2082, 3231, 13,
            128009, 128006, 882, 128007, 271, 8586, 374, 279, 2082, 2349, 86428, 555, 24657, 1203,
            36178, 512, 14196, 4077, 4521, 3600, 10729, 30812, 38355, 13743, 7682, 5365, 17758,
            26721, 198, 63830, 3600, 10729, 30812, 38355, 13743, 7682, 5365, 17758, 26721, 198,
            19741, 482, 972, 11, 1032, 489, 972, 11, 868, 88220, 1005, 18187, 487, 3231, 487, 36212,
            9366, 439, 24295, 9366, 38789, 280, 1005, 18187, 487, 3231, 487, 13798, 17758, 23821,
            91823, 1299, 4126, 1585, 11, 11579, 4126, 2499, 1005, 18187, 487, 44412, 487, 69669,
            1199, 280, 1005, 18187, 487, 1710, 487, 2714, 280, 1005, 18187, 487, 1834, 12418, 19511,
            487, 2831, 2087, 3032, 280, 25700, 18187, 487, 81157, 57999, 487, 3032, 280, 1005,
            18187, 487, 56727, 48532, 53996, 487, 27002, 3692, 48274, 280, 25700, 18187, 487, 44362,
            23821, 75781, 28868, 65341, 43558, 26400, 4053, 878, 11, 53569, 28868, 79024, 26400,
            4053, 878, 2499, 10, 817, 18187, 487, 44362, 75943, 10, 262, 43969, 79024, 60099, 11,
            53569, 28868, 65341, 43558, 26400, 4053, 878, 11, 53569, 28868, 79024, 26400, 4053, 878,
            11, 19003, 11492, 34510, 37247, 12154, 345, 10, 262, 19003, 11492, 6949, 34510, 37247,
            12154, 11, 8498, 2241, 79923, 12154, 345, 10, 2499, 1005, 18187, 487, 2590, 51750, 487,
            1747, 15976, 280, 1005, 18187, 487, 898, 11959, 38789, 280, 1005, 18187, 487, 2079,
            35486, 492, 19511, 487, 1939, 15841, 492, 35650, 280, 720, 1005, 18187, 487, 44362, 487,
            55426, 65341, 43558, 26400, 4053, 878, 401, 14196, 4077, 16411, 398, 1212, 279, 2077,
            449, 279, 4096, 323, 2019, 4400, 775, 13, 9842, 912, 810, 1109, 220, 605, 4339, 627,
            14139, 389, 279, 5151, 430, 527, 10223, 719, 1541, 956, 2997, 1405, 814, 527, 627,
            128009, 128006, 78191, 128007, 271,
        ],
    },
    generationId: "55c64df4-595f-4ac6-ad5d-4c66dc14cd2f",
    result: {
        checkpointNotFound: false,
        suggestedEdit: {
            blobName: "ba3b50ef5f450d7cbef556868776afbfea1b5aadabd7d18f639100dc4fff1389",
            changeDescription: "Import additional metrics flags",
            charEnd: 994,
            charStart: 820,
            diffSpans: [
                {
                    original: {
                        start: 0,
                        stop: 39,
                    },
                    updated: {
                        start: 0,
                        stop: 0,
                    },
                },
                {
                    original: {
                        start: 39,
                        stop: 121,
                    },
                    updated: {
                        start: 0,
                        stop: 82,
                    },
                },
                {
                    original: {
                        start: 121,
                        stop: 121,
                    },
                    updated: {
                        start: 82,
                        stop: 106,
                    },
                },
                {
                    original: {
                        start: 121,
                        stop: 171,
                    },
                    updated: {
                        start: 106,
                        stop: 156,
                    },
                },
                {
                    original: {
                        start: 171,
                        stop: 171,
                    },
                    updated: {
                        start: 156,
                        stop: 226,
                    },
                },
                {
                    original: {
                        start: 171,
                        stop: 174,
                    },
                    updated: {
                        start: 226,
                        stop: 229,
                    },
                },
            ],
            editingScore: 0.98587716,
            editingScoreThreshold: 1,
            existingCode:
                "use crate::generation_clients::Client;\nuse crate::handlers_external_sources::ExternalSourceClients;\nuse crate::metrics::{HANDLER_LATENCY_COLLECTOR, HANDLER_RETRY_COLLECTOR};\n",
            localizationScore: 0.65071243,
            path: "services/api_proxy/server/src/handler_utils.rs",
            suggestedCode:
                "use crate::handlers_external_sources::ExternalSourceClients;\nuse crate::metrics::{\n    NONE_RETRY_POLICY, HANDLER_LATENCY_COLLECTOR, HANDLER_RETRY_COLLECTOR, RETRY_DELAY_MS_FLAG,\n    RETRY_MAX_DELAY_MS_FLAG, MAX_RETRIES_FLAG,\n};\n",
            suggestionId: "4795876e-4a18-46f1-9520-2ba9f59b3fca",
        },
        unknownBlobNames: [],
    },
    suggestionOrder: 4,
};
