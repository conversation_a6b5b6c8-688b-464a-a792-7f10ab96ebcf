import { <PERSON>ckAPIServer } from "../../__mocks__/mock-api-server";
import { generateMockWorkspaceConfig } from "../../__mocks__/mock-augment-config";
import {
    ExtensionContext,
    getWorkspaceFolder,
    mockWorkspaceConfigChange,
    MutableTextDocument,
    Position,
    publishWorkspaceFoldersChange,
    Range,
    Selection,
    TextEditor,
    TrackedDisposable,
    Uri,
    window,
    workspace,
} from "../../__mocks__/vscode-mocks";
import { NextEditResolutionReporter } from "../../metrics/next-edit-resolution-reporter";
import { NextEditSessionEventReporter } from "../../metrics/next-edit-session-event-reporter";
import { BackgroundDecorationManager } from "../../next-edit/background-decoration-manager";
import { HintedSuggestion } from "../../next-edit/background-state";
import { EditSuggestion } from "../../next-edit/suggestion-manager";
import { AugmentGlobalState } from "../../utils/context";
import { KeybindingWatcher } from "../../utils/keybindings";
import { LineRange } from "../../utils/ranges";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitWorkspaceState,
    createSourceFolderFiles,
    defaultWorkspaceFolder,
    defaultWorkspaceState,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";
import { createSuggestion } from "./test-utils";

class BackgroundEditTestKit extends WorkspaceManagerTestKit {
    static readonly workspaceFolder = defaultWorkspaceFolder;
    static readonly folderState = defaultWorkspaceState;

    public readonly ctx = new ExtensionContext();
    public readonly apiServer = new MockAPIServer();
    public mockKeybindingWatcher: KeybindingWatcher;
    public workspaceManager: WorkspaceManager;
    public reporter: NextEditResolutionReporter;
    public eventReporter: NextEditSessionEventReporter;
    public backgroundDecorationManager: BackgroundDecorationManager;

    public completionVisible = jest.fn();
    public showAllLineHighlights = jest.fn();

    static async create() {
        const kit = new BackgroundEditTestKit();
        await kit.awaitStartup();
        return kit;
    }

    private constructor() {
        super();

        createSourceFolderFiles(BackgroundEditTestKit.workspaceFolder);
        const workspaceFolders = [
            getWorkspaceFolder(BackgroundEditTestKit.workspaceFolder.folderRootUri),
        ];
        publishWorkspaceFoldersChange(workspaceFolders);

        const augmentGlobalState = new AugmentGlobalState(new ExtensionContext());
        this.mockKeybindingWatcher = new KeybindingWatcher(augmentGlobalState);
        this.addDisposable(this.mockKeybindingWatcher);

        this.workspaceManager = this.createWorkspaceManager();

        this.reporter = new NextEditResolutionReporter(this.apiServer);
        this.addDisposable(this.reporter);

        this.eventReporter = new NextEditSessionEventReporter(this.apiServer);
        this.addDisposable(this.eventReporter);

        this.backgroundDecorationManager = new BackgroundDecorationManager(
            this.ctx,
            this.workspaceManager,
            this.mockKeybindingWatcher,
            this.configListener,
            this.eventReporter,
            this.completionVisible,
            this.showAllLineHighlights
        );
        this.addDisposable(this.backgroundDecorationManager);
    }

    public get folderRootUri(): Uri {
        return BackgroundEditTestKit.workspaceFolder.folderRootUri;
    }

    public async awaitStartup() {
        await awaitWorkspaceState(this.workspaceManager, BackgroundEditTestKit.folderState);
    }
}

describe("BackgroundDecorationManager", () => {
    let kit: BackgroundEditTestKit;
    let suggestions: EditSuggestion[];
    let editor: TextEditor;
    let doc: MutableTextDocument;

    beforeEach(async () => {
        window.activeTextEditor = undefined;
        jest.useFakeTimers();
        kit = await BackgroundEditTestKit.create();

        // Setup the test by creating a single file with a couple of lines.
        doc = kit.newDocument(
            defaultWorkspaceFolder,
            "example/a.txt",
            "line0\nline1\nline2\nline3\nline4\n"
        );
        // Set the active editor to the document.
        editor = new TextEditor(doc);
        editor.visibleRanges = [new Range(0, 0, 5, 0)];
        jest.spyOn(editor, "setDecorations");

        window.activeTextEditor = editor;
        window.visibleTextEditors = [editor];
        workspace.textDocuments = [doc];

        suggestions = [
            createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line 0"
            ),

            createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(3, 3),
                "new line 3.5"
            ),
        ];
    });

    afterEach(() => {
        jest.useRealTimers();
        kit.dispose();
        TrackedDisposable.assertDisposed();
        expect(window.textEditorDecorationTypes).toStrictEqual([]);
    });

    it("draws decorations in the same editor", async () => {
        const editor = window.activeTextEditor!;
        kit.backgroundDecorationManager.decorate(suggestions, {
            hintSuggestion: new HintedSuggestion(suggestions[0], true),
        });
        expect(editor.setDecorations).toHaveBeenCalled();
        // For now, just testing that we draw _some_ range in this editor.
        expect(hasDecorations(editor)).toBeTruthy();
    });

    describe("enableGlobalBackgroundSuggestions", () => {
        let otherEditor: TextEditor;
        beforeEach(() => {
            const otherDoc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/b.txt",
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherEditor = new TextEditor(otherDoc);
            window.activeTextEditor = otherEditor;
            window.visibleTextEditors.push(otherEditor);
            jest.spyOn(otherEditor, "setDecorations");
            otherEditor.visibleRanges = [new Range(0, 0, 5, 0)];
        });

        it("if false, doesn't draw decorations in the other workspace editors", async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: {
                        enableGlobalBackgroundSuggestions: false,
                    },
                })
            );
            kit.backgroundDecorationManager.decorate(suggestions, {
                hintSuggestion: new HintedSuggestion(suggestions[0], true),
            });

            // Make sure decorations are drawn in this editor.
            expect(editor.setDecorations).toHaveBeenCalled();
            expect(hasDecorations(editor)).toBeTruthy();

            // But not drawn in the other editor.
            expect(otherEditor.setDecorations).toHaveBeenCalled();
            expect(hasDecorations(otherEditor)).toBeFalsy();
        });

        it("if true, draws decorations in the other workspace editors", async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: {
                        enableGlobalBackgroundSuggestions: true,
                    },
                })
            );
            kit.backgroundDecorationManager.decorate(suggestions, {
                hintSuggestion: new HintedSuggestion(suggestions[0], true),
            });
            // Make sure decorations are drawn in this editor.
            expect(editor.setDecorations).toHaveBeenCalled();
            expect(hasDecorations(editor)).toBeTruthy();

            // And drawn in the other editor.
            expect(otherEditor.setDecorations).toHaveBeenCalled();
            expect(hasDecorations(otherEditor)).toBeTruthy();
        });
    });

    it("never draws decorations in output editors", async () => {
        const otherEditor = new TextEditor(
            new MutableTextDocument(Uri.parse("output:///relative/a"), "a")
        );
        window.activeTextEditor = otherEditor;
        window.visibleTextEditors.push(otherEditor);
        jest.spyOn(otherEditor, "setDecorations");
        kit.backgroundDecorationManager.decorate(suggestions, {
            hintSuggestion: new HintedSuggestion(suggestions[0], true),
        });
        expect(otherEditor.setDecorations).toHaveBeenCalled();
        expect(hasDecorations(otherEditor)).toBeFalsy();

        // For now, just testing that we draw _some_ range in this editor.
        expect(editor.setDecorations).toHaveBeenCalled();
        expect(hasDecorations(editor)).toBeTruthy();
    });

    test.each([[true], [false]])(
        "draws grey text decorations if completionVisible is %p",
        (completionVisible) => {
            const editor = window.activeTextEditor!;
            editor.selection = new Selection(
                new Position(suggestions[0].lineRange.start, 0),
                new Position(suggestions[0].lineRange.start, 0)
            );
            kit.completionVisible.mockReturnValue(completionVisible);
            editor.visibleRanges = [new Range(2, 0, 5, 0)];
            kit.backgroundDecorationManager.decorate([suggestions[0]], {
                hintSuggestion: new HintedSuggestion(suggestions[0], true),
            });
            expect(editor.setDecorations).toHaveBeenCalled();
            // For now, just testing that we draw _some_ range in this editor.
            expect(hasContentTextDecoration(editor)).toBe(!completionVisible);
        }
    );

    it("doesn't draw decorations in diff editors", async () => {
        const editor = window.activeTextEditor!;
        // Diff editors seem to have this set to undefined.
        editor.viewColumn = undefined;
        kit.backgroundDecorationManager.decorate(suggestions, {
            hintSuggestion: new HintedSuggestion(suggestions[0], true),
        });
        expect(editor.setDecorations).toHaveBeenCalledTimes(0);
        expect(hasDecorations(editor)).toBeFalsy();
    });

    describe("multiple visible ranges", () => {
        beforeEach(() => {
            // Set up multiple visible ranges (simulating collapsed regions)
            editor.visibleRanges = [
                new Range(0, 0, 1, 0), // First visible range
                new Range(3, 0, 4, 0), // Second visible range
            ];
            // Ensure editor is active and visible
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor];
            // Ensure completion is not visible by default
            kit.completionVisible.mockReturnValue(false);
        });

        it("shows in-line decorations when suggestion is in first visible range", () => {
            editor.selection = new Selection(new Position(3, 0), new Position(3, 0));
            const firstRangeSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(1, 1),
                "first range line"
            );

            kit.backgroundDecorationManager.decorate([firstRangeSuggestion], {
                hintSuggestion: new HintedSuggestion(firstRangeSuggestion, true),
            });

            expect(editor.setDecorations).toHaveBeenCalled();
            expect(hasContentTextDecoration(editor, "diff description")).toBeTruthy();
            expect(hasContentTextDecoration(editor, "↓")).toBeFalsy();
        });

        it("shows in-line decorations when suggestion is in second visible range", () => {
            editor.selection = new Selection(new Position(0, 0), new Position(0, 0));
            const secondRangeSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(4, 4),
                "second range line"
            );

            kit.backgroundDecorationManager.decorate([secondRangeSuggestion], {
                hintSuggestion: new HintedSuggestion(secondRangeSuggestion, true),
            });

            expect(editor.setDecorations).toHaveBeenCalled();
            expect(hasContentTextDecoration(editor, "diff description")).toBeTruthy();
            expect(hasContentTextDecoration(editor, "↓")).toBeFalsy();
        });

        it("shows bottom box when suggestion is hidden", () => {
            editor.selection = new Selection(new Position(0, 0), new Position(0, 0));
            const middleSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(2, 2),
                "middle line"
            );

            kit.backgroundDecorationManager.decorate([middleSuggestion], {
                hintSuggestion: new HintedSuggestion(middleSuggestion, true),
            });

            expect(editor.setDecorations).toHaveBeenCalled();
            expect(hasContentTextDecoration(editor, "diff description")).toBeTruthy();
            expect(hasContentTextDecoration(editor, "↓")).toBeTruthy();
        });
    });

    function hasDecorations(editor: TextEditor) {
        let isNonEmpty = false;
        for (const [_decorationType, ranges] of editor.currentDecorations.entries()) {
            isNonEmpty = isNonEmpty || ranges.length > 0;
        }
        return isNonEmpty;
    }

    /** Heuristic to detect if a decoration has content text. */
    function hasContentTextDecoration(editor: TextEditor, contextText?: string) {
        for (const [_decorationType, ranges] of editor.currentDecorations.entries()) {
            if (ranges.some((rangeOptions) => hasContentText(rangeOptions, contextText))) {
                return true;
            }
        }
        return false;
    }

    function hasContentText(rangeOptions: any, contextText?: string) {
        // Decorations can look like the following.
        const possibilities = [
            rangeOptions.renderOptions?.after?.contentText,
            rangeOptions.renderOptions?.light?.after?.contentText,
            rangeOptions.renderOptions?.dark?.after?.contentText,
        ];
        if (contextText !== undefined) {
            return possibilities.some((x) => x && x.includes(contextText));
        } else {
            return possibilities.some((x) => x !== undefined);
        }
    }
});
