import { TrackedDisposable } from "../../__mocks__/vscode-mocks";
import { AugmentLogger } from "../../logging";
import { WhitespaceInfo } from "../../next-edit/whitespace-info";

describe("WhitespaceInfo", () => {
    afterEach(() => {
        TrackedDisposable.assertDisposed();
    });

    test.each([
        {
            // Only spaces
            lines: ["  a", "  b", "  c"],
            expectedNumSpaces: 2,
            expectedNumTabs: 0,
        },
        {
            // Only tabs
            lines: ["\ta", "\tb", "\tc"],
            expectedNumSpaces: 0,
            expectedNumTabs: 1,
        },
        {
            // Mixed spaces and tabs
            lines: ["\t  a", "\t  b", "\t  c"],
            expectedNumSpaces: 2,
            expectedNumTabs: 1,
        },
        {
            // Mixed spaces and tabs in different orders
            lines: ["\t  a", " \t b", "  \tc"],
            expectedNumSpaces: 2,
            expectedNumTabs: 1,
        },
        {
            // No whitespace
            lines: ["a", "b", "c"],
            expectedNumSpaces: 0,
            expectedNumTabs: 0,
        },
        {
            // Empty line without whitespace
            lines: ["", "a", "b", "c"],
            expectedNumSpaces: 0,
            expectedNumTabs: 0,
        },
        {
            // Empty line with spaces
            lines: ["", "  a", "  b", "  c"],
            expectedNumSpaces: 2,
            expectedNumTabs: 0,
        },
    ])("creation %o", ({ lines, expectedNumSpaces, expectedNumTabs }) => {
        const whitespaceInfo = WhitespaceInfo.computeCommonLeadingWhitespace(lines);
        expect((whitespaceInfo as any)._spaces).toBe(expectedNumSpaces);
        expect((whitespaceInfo as any)._tabs).toBe(expectedNumTabs);
    });

    it("trimLeadingFull", () => {
        const lines = ["\t  a", " \t b", "  \tc"];
        const whitespaceInfo = WhitespaceInfo.computeCommonLeadingWhitespace(lines);
        expect(whitespaceInfo.trimLeadingFull(lines[0])).toBe("a");
        expect(whitespaceInfo.trimLeadingFull(lines[1])).toBe("b");
        expect(whitespaceInfo.trimLeadingFull(lines[2])).toBe("c");
    });

    it("trimLeadingFull correctly handles empty lines", () => {
        const lines = ["  a", "", "  b"];
        const logger = {
            // We don't care about the other methods.
            debug: jest.fn(),
        } as any as AugmentLogger;
        const whitespaceInfo = WhitespaceInfo.computeCommonLeadingWhitespace(lines);
        expect(whitespaceInfo.trimLeadingFull(lines[0])).toBe("a");
        expect(whitespaceInfo.trimLeadingFull(lines[1], logger)).toBe("");
        expect(whitespaceInfo.trimLeadingFull(lines[2])).toBe("b");
        expect(logger.debug).not.toHaveBeenCalled();
    });

    it("trimLeadingIncremental", () => {
        const lines = ["\t  a", " \t b", "  \tc"];
        let whitespaceInfo = WhitespaceInfo.computeCommonLeadingWhitespace(lines);

        expect(whitespaceInfo.trimLeadingIncremental("abc")).toBe(undefined);

        const result1 = whitespaceInfo.trimLeadingIncremental(" ");
        expect(result1?.trimmed).toBe("");
        expect(result1?.remaining.isEmpty()).toBe(false);

        const result2 = result1?.remaining.trimLeadingIncremental("\t");
        expect(result2?.trimmed).toBe("");
        expect(result2?.remaining.isEmpty()).toBe(false);

        const result3 = result2?.remaining.trimLeadingIncremental(" a");
        expect(result3?.trimmed).toBe("a");
        expect(result3?.remaining.isEmpty()).toBe(true);
    });
});
