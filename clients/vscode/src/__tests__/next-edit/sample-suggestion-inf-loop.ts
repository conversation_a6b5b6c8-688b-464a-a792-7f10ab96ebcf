export const suggestion = {
    descriptionOutput: {
        logProbs: [-0.09472656, -0.000014066696, -0.625, -0.515625, -0.25976562, -0.000012516975],
        offsets: [0, 4, 7, 15, 24, 30],
        text: "Clarify request handling logic<|eot_id|>",
        tokenIds: [100069, 1463, 1715, 11850, 12496, 128009],
    },
    descriptionPrompt: {
        logProbs: [],
        offsets: [
            0, 17, 36, 42, 59, 61, 64, 68, 70, 80, 88, 91, 94, 99, 103, 105, 109, 114, 117, 120,
            128, 137, 149, 154, 158, 163, 170, 176, 180, 189, 192, 198, 209, 214, 219, 220, 224,
            232, 235, 240, 252, 255, 258, 263, 269, 280, 288, 299, 303, 311, 314, 318, 325, 328,
            332, 340, 343, 347, 352, 356, 357, 367, 386, 390, 407, 409, 413, 416, 420, 425, 432,
            442, 445, 452, 457, 462, 464, 466, 468, 471, 477, 478, 484, 492, 493, 499, 502, 503,
            506, 512, 513, 519, 527, 528, 534, 537, 538, 540, 542, 544, 545, 547, 549, 551, 552,
            554, 558, 563, 571, 572, 575, 581, 585, 592, 597, 601, 607, 611, 620, 627, 628, 634,
            638, 645, 652, 660, 663, 667, 668, 669, 675, 679, 686, 689, 691, 698, 699, 708, 709,
            710, 715, 717, 725, 728, 737, 742, 746, 749, 751, 753, 756, 760, 767, 772, 775, 782,
            789, 797, 801, 807, 810, 814, 823, 825, 827, 832, 836, 844, 847, 849, 854, 855, 865,
            866, 868, 871, 875, 876, 877, 887, 888, 893, 897, 900, 903, 911, 912, 916, 917, 925,
            928, 932, 934, 936, 937, 938, 941, 945, 952, 957, 960, 964, 970, 973, 977, 986, 987,
            992, 996, 999, 1002, 1010, 1011, 1012, 1013, 1017, 1022, 1026, 1029, 1032, 1040, 1041,
            1045, 1053, 1056, 1060, 1062, 1064, 1068, 1071, 1073, 1075, 1081, 1083, 1089, 1093,
            1102, 1107, 1111, 1123, 1127, 1131, 1139, 1144, 1145, 1151, 1154, 1159, 1164, 1165,
            1167, 1173, 1175, 1180, 1183, 1187, 1194, 1199, 1203, 1212, 1216, 1220, 1222, 1230,
            1236, 1241, 1245, 1247, 1257, 1276, 1285, 1302,
        ],
        text: "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a developer working in my codebase.\nYour task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\nHere is the code change delimited by triple backticks:\n```\n--- tools/tenant_manager/README.md\n+++ tools/tenant_manager/README.md\n@@ -21,10 +21,10 @@\n Upon startup, it pulls the latest main and scans for existing tenant\n names and authorisation domains in use.  These are stored in a sqlite\n database.  When a request is received from pubsub:\n * if the tenant name or authorisation domains are found in the database,\n   then the request is nacked; otherwise\n-* if the.  Otherwise, once the PR is created, the\n-request is acked.\n+* if the tenant name is not found in the database, then the PR is created\n+  and once the PR is created, the request is acked.\n \n \n \n\n```\nDirectly start the response with the description and say nothing else. Write no more than 10 words.\nFocus on the fields that are changing but don't include where they are.\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n",
        tokenIds: [
            128000, 128006, 9125, 128007, 271, 2675, 527, 264, 16131, 3318, 304, 856, 2082, 3231,
            627, 7927, 3465, 374, 311, 8819, 9959, 2038, 505, 279, 2082, 2349, 3770, 323, 7664, 433,
            1701, 48696, 19120, 1376, 13, 578, 7580, 315, 701, 4096, 374, 311, 1520, 1023, 13707,
            6288, 3619, 1401, 13878, 315, 279, 2349, 304, 279, 2317, 315, 279, 2082, 3231, 13,
            128009, 128006, 882, 128007, 271, 8586, 374, 279, 2082, 2349, 86428, 555, 24657, 1203,
            36178, 512, 14196, 4077, 4521, 7526, 14, 45019, 12418, 14, 55775, 22030, 198, 63830,
            7526, 14, 45019, 12418, 14, 55775, 22030, 198, 19741, 482, 1691, 11, 605, 489, 1691, 11,
            605, 88220, 30538, 21210, 11, 433, 34145, 279, 5652, 1925, 323, 43739, 369, 6484, 26305,
            198, 5144, 323, 3229, 8082, 31576, 304, 1005, 13, 220, 4314, 527, 9967, 304, 264, 22775,
            198, 4729, 13, 220, 3277, 264, 1715, 374, 4036, 505, 6814, 2008, 512, 353, 422, 279,
            26305, 836, 477, 3229, 8082, 31576, 527, 1766, 304, 279, 4729, 345, 256, 1243, 279,
            1715, 374, 308, 11440, 26, 6062, 198, 46003, 422, 279, 13, 220, 18715, 11, 3131, 279,
            8743, 374, 3549, 11, 279, 198, 45908, 374, 10956, 291, 627, 10, 9, 422, 279, 26305, 836,
            374, 539, 1766, 304, 279, 4729, 11, 1243, 279, 8743, 374, 3549, 198, 10, 220, 323, 3131,
            279, 8743, 374, 3549, 11, 279, 1715, 374, 10956, 291, 627, 33006, 4815, 14196, 4077,
            16411, 398, 1212, 279, 2077, 449, 279, 4096, 323, 2019, 4400, 775, 13, 9842, 912, 810,
            1109, 220, 605, 4339, 627, 14139, 389, 279, 5151, 430, 527, 10223, 719, 1541, 956, 2997,
            1405, 814, 527, 627, 128009, 128006, 78191, 128007, 271,
        ],
    },
    generationId: "52a3cece-ed3c-4249-91f5-d2a5a91d329c",
    result: {
        checkpointNotFound: false,
        suggestedEdit: {
            blobName: "9a3306f96b53a344e67a2872eb2cfb42de208cb68cbeabe112e8103db5f835b6",
            changeDescription: "Clarify request handling logic",
            charEnd: 910,
            charStart: 842,
            diffSpans: [
                {
                    original: {
                        start: 0,
                        stop: 8,
                    },
                    updated: {
                        start: 0,
                        stop: 8,
                    },
                },
                {
                    original: {
                        start: 8,
                        stop: 15,
                    },
                    updated: {
                        start: 8,
                        stop: 40,
                    },
                },
                {
                    original: {
                        start: 15,
                        stop: 15,
                    },
                    updated: {
                        start: 40,
                        stop: 40,
                    },
                },
                {
                    original: {
                        start: 15,
                        stop: 20,
                    },
                    updated: {
                        start: 40,
                        stop: 49,
                    },
                },
                {
                    original: {
                        start: 20,
                        stop: 21,
                    },
                    updated: {
                        start: 49,
                        stop: 50,
                    },
                },
                {
                    original: {
                        start: 21,
                        stop: 21,
                    },
                    updated: {
                        start: 50,
                        stop: 79,
                    },
                },
                {
                    original: {
                        start: 21,
                        stop: 49,
                    },
                    updated: {
                        start: 79,
                        stop: 107,
                    },
                },
                {
                    original: {
                        start: 49,
                        stop: 50,
                    },
                    updated: {
                        start: 107,
                        stop: 108,
                    },
                },
                {
                    original: {
                        start: 50,
                        stop: 68,
                    },
                    updated: {
                        start: 108,
                        stop: 126,
                    },
                },
            ],
            editingScore: 0.88887984,
            editingScoreThreshold: 1,
            existingCode: "* if the.  Otherwise, once the PR is created, the\nrequest is acked.\n",
            localizationScore: 0.99663347,
            path: "tools/tenant_manager/README.md",
            suggestedCode:
                "* if the tenant name is not found in the database, then the PR is created\n  and once the PR is created, the request is acked.\n",
            suggestionId: "75ae7b23-6286-4282-80bc-d1557d87af8e",
        },
        unknownBlobNames: [],
    },
    suggestionOrder: 1,
};
