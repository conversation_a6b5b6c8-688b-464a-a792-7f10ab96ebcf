import { QualifiedPathName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import {
    MutableTextDocument,
    MutableTextDocumentChangeBuilder,
    Position,
    Range,
    TextDocument,
    TextDocumentChangeEvent,
    TrackedDisposable,
    Uri,
} from "../../__mocks__/vscode-mocks";
import {
    NextEditMode,
    NextEditResult,
    NextEditScope,
    SuggestionState,
} from "../../next-edit/next-edit-types";
import { EditSuggestion } from "../../next-edit/suggestion-manager";
import {
    Accepted,
    Invalidated,
    Unchanged,
    Undone,
    Updated,
    updateSuggestionAfterChangeEvent,
} from "../../next-edit/suggestion-update";
import { CharRange, LineRange } from "../../utils/ranges";

describe("updateSuggestionAfterChangeEvent", () => {
    afterEach(() => {
        TrackedDisposable.assertDisposed();
    });

    /** Mock document for all the below tests. */
    function createDoc() {
        return new MutableTextDocument(
            Uri.parse("file:///example/a"),
            "line 0\n" + "line 1\n" + "line 2\n" + "line 3\n" + "line 4\n"
        );
    }

    function toCharRange(doc: TextDocument, lineRange: LineRange) {
        return new CharRange(
            doc.offsetAt(new Position(lineRange.start, 0)),
            doc.offsetAt(new Position(lineRange.stop, 0))
        );
    }

    /** Helper to create a suggestion for a document, original range and new text. */
    function createSuggestion(
        doc: TextDocument,
        originalLineRange: LineRange,
        suggestedCode: string
    ) {
        const qualifiedPathName = new QualifiedPathName("", doc.uri.fsPath);
        const documentText = doc.getText();
        const charRange = toCharRange(doc, originalLineRange);

        return new EditSuggestion(
            "",
            NextEditMode.Foreground,
            NextEditScope.File,
            {
                suggestionId: "",
                path: qualifiedPathName.relPath,
                blobName: "",
                charStart: charRange.start,
                charEnd: charRange.stop,
                existingCode: documentText.slice(charRange.start, charRange.stop),
                suggestedCode,
                changeDescription: "",
                diffSpans: [],
                editingScore: 0,
                localizationScore: 0,
                editingScoreThreshold: 1.0,
            } as NextEditResult,
            qualifiedPathName,
            originalLineRange
        );
    }

    /**
     * Helper to create a text change event from a list of range, replacement pairs.
     *
     * This function also modifies the document to reflect the changes, simulating
     * what we'd see in VSCode.
     */
    function createEvent(
        doc: MutableTextDocument,
        rangeText: [Range, string][]
    ): TextDocumentChangeEvent {
        // TODO(arun): The actual change events returned by applying changes from
        // MutableTextDocumentChangeBuilder don't seem to agree with VSCode's, and
        // aren't entirely intuitive. For now, we only use the builder to apply
        // the changes, and create the events ourselves.
        const event = new TextDocumentChangeEvent(
            doc,
            rangeText.map(([range, newText]) => {
                return {
                    range: range,
                    rangeLength: doc.offsetAt(range.end) - doc.offsetAt(range.start),
                    rangeOffset: doc.offsetAt(range.start),
                    text: newText,
                };
            })
        );

        const builder = new MutableTextDocumentChangeBuilder();
        let offsetDelta = 0;
        for (const change of event.contentChanges) {
            builder.replace(change.rangeOffset + offsetDelta, change.rangeLength, change.text);
            offsetDelta += change.text.length - change.rangeLength;
        }
        doc.applyChanges(builder.build());
        return event;
    }

    it("ignores changes from other docs", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const otherDoc = new MutableTextDocument(Uri.parse("file:///example/b"), "other doc");
        const event = otherDoc.insert(0, "modified line 0\n");
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Unchanged);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted).toBeInstanceOf(Unchanged);
    });

    it("ignores empty change", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const update = updateSuggestionAfterChangeEvent(
            suggestion,
            new TextDocumentChangeEvent(doc, [])
        );
        expect(update).toBeInstanceOf(Unchanged);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            new TextDocumentChangeEvent(doc, [])
        );
        expect(updateAccepted).toBeInstanceOf(Unchanged);
    });

    it("ignores later changes", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const events = createEvent(doc, [
            [new Range(4, 0, 5, 0), "modified line 4\n"],
            [new Range(5, 0, 5, 0), "new line 5\n"],
        ]);
        const update = updateSuggestionAfterChangeEvent(suggestion, events);
        expect(update).toBeInstanceOf(Unchanged);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            events
        );
        expect(updateAccepted).toBeInstanceOf(Unchanged);
    });

    it("ignores later changes on touching lines", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const events = createEvent(doc, [[new Range(4, 5, 4, 5), "line modified 4\n"]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, events);
        expect(update).toBeInstanceOf(Unchanged);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            events
        );
        expect(updateAccepted).toBeInstanceOf(Unchanged);
    });

    it("ignores later insertions at the end of a suggestion range", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const events = createEvent(doc, [[new Range(4, 0, 4, 0), "new line 4\n"]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, events);
        expect(update).toBeInstanceOf(Unchanged);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            events
        );
        expect(updateAccepted).toBeInstanceOf(Unchanged);
    });

    it("updates for earlier additions", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const event = createEvent(doc, [
            [new Range(0, 0, 1, 0), "modified line 0\nnew line 0.5\n"],
        ]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Updated);
        // Make sure lines are offset by 1.
        expect(update.suggestion.lineRange.start).toEqual(suggestion.lineRange.start + 1);
        expect(update.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop + 1);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted.suggestion.lineRange.start).toEqual(suggestion.lineRange.start + 1);
        expect(updateAccepted.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop + 1);
    });

    it("updates for earlier touching additions", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const event = createEvent(doc, [
            [new Range(2, 0, 3, 0), "modified line 0\nnew line 0.5\n"],
        ]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Updated);
        // Make sure lines are offset by 1.
        expect(update.suggestion.lineRange.start).toEqual(suggestion.lineRange.start + 1);
        expect(update.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop + 1);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted.suggestion.lineRange.start).toEqual(suggestion.lineRange.start + 1);
        expect(updateAccepted.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop + 1);
    });

    it("updates for earlier removals", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const event = createEvent(doc, [[new Range(0, 0, 1, 0), ""]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Updated);
        // Make sure lines are offset by 1.
        expect(update.suggestion.lineRange.start).toEqual(suggestion.lineRange.start - 1);
        expect(update.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop - 1);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted.suggestion.lineRange.start).toEqual(suggestion.lineRange.start - 1);
        expect(updateAccepted.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop - 1);
    });

    it("updates for touching removals", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const event = createEvent(doc, [[new Range(2, 0, 3, 0), ""]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Updated);
        // Make sure lines are offset by 1.
        expect(update.suggestion.lineRange.start).toEqual(suggestion.lineRange.start - 1);
        expect(update.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop - 1);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted.suggestion.lineRange.start).toEqual(suggestion.lineRange.start - 1);
        expect(updateAccepted.suggestion.lineRange.stop).toEqual(suggestion.lineRange.stop - 1);
    });

    it("invalidates for intersecting changes", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const event = createEvent(doc, [
            [new Range(2, 0, 4, 0), "modified line 0\nnew line 0.5\n"],
        ]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Invalidated);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted).toBeInstanceOf(Invalidated);
    });

    it("invalidates for line deletions", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3\n");
        const event = createEvent(doc, [[new Range(3, 0, 4, 0), ""]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Invalidated);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted).toBeInstanceOf(Invalidated);
    });

    it("invalidates for partial line deletions", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 4), "");
        const event = createEvent(doc, [[new Range(3, 0, 4, 0), ""]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Invalidated);
    });

    it("invalidates for larger line deletions", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "");
        const event = createEvent(doc, [[new Range(2, 0, 4, 0), ""]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Invalidated);
    });

    it("invalidates for insertions at the start of the range", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "modified line 3.5\n");
        const event = createEvent(doc, [[new Range(3, 0, 3, 0), "modified "]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Invalidated);

        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted).toBeInstanceOf(Invalidated);
    });

    it("invalidates for partial line deletions after accept", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 4), "new line 2\nnew line 3\n");
        // First event accepts the suggestion.
        createEvent(doc, [[new Range(2, 0, 4, 0), "new line 2\nnew line 3\n"]]);
        // Second event partially deletes lines.
        const event = createEvent(doc, [[new Range(3, 0, 4, 0), ""]]);
        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted).toBeInstanceOf(Invalidated);
    });

    it("invalidates for larger line deletions after accept", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(3, 4), "new line 3\n");
        // First event accepts the suggestion.
        createEvent(doc, [[new Range(3, 0, 4, 0), "new line 3\n"]]);
        // Second event partially deletes lines.
        const event = createEvent(doc, [[new Range(3, 0, 4, 0), ""]]);
        const updateAccepted = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            event
        );
        expect(updateAccepted).toBeInstanceOf(Invalidated);
    });

    it("accepts exact matching char insert", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 2), "new line 2.5\n");
        const event = createEvent(doc, [[new Range(2, 0, 2, 0), "new line 2.5\n"]]);
        const update = updateSuggestionAfterChangeEvent(suggestion, event);
        expect(update).toBeInstanceOf(Accepted);
    });

    it("accepts for exact matching modification", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "modified line 2.5\n");
        const update = updateSuggestionAfterChangeEvent(
            suggestion,
            createEvent(doc, [
                [new Range(2, 0, 2, 0), "modified "],
                [new Range(2, 5, 2, 6), "2.5"],
            ])
        );
        expect(update).toBeInstanceOf(Accepted);
    });

    it("accepts for exact matching removal", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "line\n");
        const update = updateSuggestionAfterChangeEvent(
            suggestion,
            createEvent(doc, [[new Range(2, 4, 2, 6), ""]])
        );
        expect(update).toBeInstanceOf(Accepted);
    });

    it("accepts for exact matching line removal", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "");
        const update = updateSuggestionAfterChangeEvent(
            suggestion,
            createEvent(doc, [[new Range(2, 0, 3, 0), ""]])
        );
        expect(update).toBeInstanceOf(Accepted);
    });

    it("accepts for exact matching modification even with previous events", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "modified line 2.5\n");
        const update = updateSuggestionAfterChangeEvent(
            suggestion,
            createEvent(doc, [
                [new Range(0, 0, 1, 0), "modified "], // Add modified to the first line.
                [new Range(2, 0, 2, 0), "modified "],
                [new Range(2, 5, 2, 6), "2.5"],
            ])
        );
        expect(update).toBeInstanceOf(Accepted);
    });

    it("undoes for exact matching insert", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 2), "new line 2.5\n");
        // First event accepts the suggestion.
        createEvent(doc, [[new Range(2, 0, 2, 0), "new line 2.5\n"]]);

        const update = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            // Second event undoes the suggestion.
            createEvent(doc, [[new Range(2, 0, 3, 0), ""]])
        );
        expect(update).toBeInstanceOf(Undone);
    });

    it("undoes for exact matching modification with net line increase near end of file", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(
            doc,
            new LineRange(3, 5),
            "new line 3\nnew line 4\nnew line 5\nnew line 6\nnew line 7\nnew line 8\nnew line 9\n"
        );
        createEvent(doc, [
            [
                new Range(3, 0, 5, 0),
                "new line 3\nnew line 4\nnew line 5\nnew line 6\nnew line 7\nnew line 8\nnew line 9\n",
            ],
        ]);

        const update = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            createEvent(doc, [
                [new Range(3, 0, 4, 0), "line 3\n"],
                [new Range(4, 0, 5, 0), "line 4\n"],
                [new Range(5, 0, 10, 0), ""], // this change ends up being outside the range if you use the lineRange instead of afterLineRange to determine inside/outside
            ])
        );
        expect(update).toBeInstanceOf(Undone);
    });

    it("undoes for exact matching removal", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "line\n");
        // First event accepts the suggestion.
        createEvent(doc, [[new Range(2, 0, 3, 0), "line\n"]]);

        const update = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            // Second event undoes the suggestion.
            createEvent(doc, [[new Range(2, 0, 3, 0), "line 2\n"]])
        );
        expect(update).toBeInstanceOf(Undone);
    });

    it("undoes for exact matching line removal", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "");
        // First event accepts the suggestion.
        createEvent(doc, [[new Range(2, 0, 3, 0), ""]]);

        const update = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            // Second event undoes the suggestion.
            createEvent(doc, [[new Range(2, 0, 2, 0), "line 2\n"]])
        );
        expect(update).toBeInstanceOf(Undone);
    });

    it("undoes for exact matching modification", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "modified line 2.5\n");
        // First event accepts the suggestion.
        createEvent(doc, [[new Range(2, 0, 3, 0), "modified line 2.5\n"]]);

        const update = updateSuggestionAfterChangeEvent(
            suggestion.with({ state: SuggestionState.accepted }),
            // Second event undoes the suggestion.
            createEvent(doc, [[new Range(2, 0, 3, 0), "line 2\n"]])
        );
        expect(update).toBeInstanceOf(Undone);
    });

    it("undoes for exact matching modification even with previous events", () => {
        const doc = createDoc();
        const suggestion = createSuggestion(doc, new LineRange(2, 3), "modified line 2.5\n");
        // First event accepts the suggestion.
        const firstUpdate = updateSuggestionAfterChangeEvent(
            suggestion,
            // Second event undoes the suggestion.
            createEvent(doc, [
                [new Range(0, 0, 1, 0), "modified "], // change first line
                [new Range(2, 0, 3, 0), "modified line 2.5\n"],
            ])
        );
        expect(firstUpdate).toBeInstanceOf(Accepted);

        const secondUpdate = updateSuggestionAfterChangeEvent(
            firstUpdate.suggestion,
            // Second event undoes the suggestion.
            createEvent(doc, [
                [new Range(0, 0, 1, 0), "line 0\nline 1\n"], // change first line
                [new Range(1, 0, 2, 0), "line 2\n"],
            ])
        );
        expect(secondUpdate).toBeInstanceOf(Undone);
    });
});
