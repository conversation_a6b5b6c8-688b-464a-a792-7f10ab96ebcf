
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;">  1  1<span class="codicon codicon-blank"></span> Before context                                                                                  </span>
<span style="color:#0000ff;background-color:#ff0000;">  2   </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;">&quot;&quot;&quot;</span><span style="background-color:#aa0000;">Represents the</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">position of a</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">completion request</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">relative to a</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">cached blob</span><span style="background-color:#ff0000;">.&quot;&quot;&quot;</span><span style="background-color:#ff0000;">                </span>
<span style="color:#0000ff;background-color:#00ff00;">     2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;">&quot;&quot;&quot;</span><span style="background-color:#00aa00;">Representa la</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">posición de una solicitud de</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">completación</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">relativa a un blob en</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">caché</span><span style="background-color:#00ff00;">.&quot;&quot;&quot;</span><span style="background-color:#00ff00;">      </span>
<span style="color:#0000ff;background-color:#00000000;">  3  3</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span><span style="background-color:#00000000;">                                                                                                </span>
<span style="color:#0000ff;background-color:#00000000;">  4  4</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>blob_name: str<span style="background-color:#00000000;">                                                                                  </span>
<span style="color:#0000ff;background-color:#ff0000;">  5   </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#aa0000;">&quot;&quot;&quot;The blob name of the cached copy of the file.&quot;&quot;&quot;</span><span style="background-color:#ff0000;">                                             </span>
<span style="color:#0000ff;background-color:#00000000;">  6  5</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>"""El nombre del blob de la copia en caché del archivo."""<span style="background-color:#00000000;">                                      </span>
<span style="color:#0000ff;background-color:#00ff00;">     6</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">&quot;&quot;&quot;El nombre del blob de la copia en caché del archivo.&quot;&quot;&quot;</span><span style="background-color:#00ff00;">                                      </span>
<span style="color:#0000ff;background-color:#00000000;">  7  7</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span><span style="background-color:#00000000;">                                                                                                </span>
<span style="color:#0000ff;background-color:#00000000;">  8  8</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>prefix_begin: int<span style="background-color:#00000000;">                                                                               </span>
<span style="color:#0000ff;background-color:#ff0000;">  9   </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#aa0000;">&quot;&quot;&quot;The offset in Unicode characters where the prefix region begins in `blob_name`.&quot;&quot;&quot;</span><span style="background-color:#ff0000;">           </span>
<span style="color:#0000ff;background-color:#00000000;"> 10  9</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>"""El desplazamiento en caracteres Unicode donde comienza la región de prefijo en blob_name."""<span style="background-color:#00000000;"> </span>
<span style="color:#0000ff;background-color:#00ff00;">    10</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">&quot;&quot;&quot;El desplazamiento en caracteres Unicode donde comienza la región de prefijo en blob_name.&quot;&quot;&quot;</span><span style="background-color:#00ff00;"> </span>
<span style="color:#0000ff;background-color:#00000000;"> 11 11</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span><span style="background-color:#00000000;">                                                                                                </span>
<span style="color:#0000ff;background-color:#00000000;"> 12 12</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>suffix_end: int<span style="background-color:#00000000;">                                                                                 </span>
<span style="color:#0000ff;background-color:#ff0000;"> 13   </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;">&quot;&quot;&quot;</span><span style="background-color:#aa0000;">The offset in</span><span style="background-color:#ff0000;"> Unicode </span><span style="background-color:#aa0000;">character</span><span style="background-color:#aa0000;">s where the suffix</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">region</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">ends in `</span><span style="background-color:#ff0000;">blob_name</span><span style="background-color:#aa0000;">`</span><span style="background-color:#ff0000;">.&quot;&quot;&quot;</span><span style="background-color:#ff0000;">             </span>
<span style="color:#0000ff;background-color:#00ff00;">    13</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;">&quot;&quot;&quot;</span><span style="background-color:#00aa00;">El desplazamiento en caracteres</span><span style="background-color:#00ff00;"> Unicode </span><span style="background-color:#00aa00;">donde ter</span><span style="background-color:#00aa00;">mina la</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">región de sufijo</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">en </span><span style="background-color:#00ff00;">blob_name</span><span style="background-color:#00aa00;"></span><span style="background-color:#00ff00;">.&quot;&quot;&quot;</span><span style="background-color:#00ff00;">   </span>
<span style="color:#0000ff;background-color:#00000000;"> 14 14</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span><span style="background-color:#00000000;">                                                                                                </span>
<span style="color:#0000ff;background-color:#00000000;"> 15 15</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>original_prefix_length: int<span style="background-color:#00000000;">                                                                     </span>
<span style="color:#0000ff;background-color:#ff0000;"> 16   </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;">&quot;&quot;&quot;</span><span style="background-color:#aa0000;">The length of the</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">prefix in the</span><span style="background-color:#ff0000;"> original </span><span style="background-color:#aa0000;">request (in</span><span style="background-color:#ff0000;"> Unicode</span><span style="background-color:#aa0000;"> characters</span><span style="background-color:#ff0000;">).&quot;&quot;&quot;</span><span style="background-color:#ff0000;">                 </span>
<span style="color:#0000ff;background-color:#00ff00;">    16</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;">&quot;&quot;&quot;</span><span style="background-color:#00aa00;">La longitud del</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">prefijo en la solicitud</span><span style="background-color:#00ff00;"> original </span><span style="background-color:#00aa00;">(en caracteres</span><span style="background-color:#00ff00;"> Unicode</span><span style="background-color:#00aa00;"></span><span style="background-color:#00ff00;">).&quot;&quot;&quot;</span><span style="background-color:#00ff00;">                 </span>
<span style="color:#0000ff;background-color:#00000000;"> 17 17</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span><span style="background-color:#00000000;">                                                                                                </span>
<span style="color:#0000ff;background-color:#00000000;"> 18 18</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>original_suffix_length: int<span style="background-color:#00000000;">                                                                     </span>
<span style="color:#0000ff;background-color:#ff0000;"> 19   </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;">&quot;&quot;&quot;</span><span style="background-color:#aa0000;">The length of the</span><span style="background-color:#ff0000;"> </span><span style="background-color:#aa0000;">suffix in the</span><span style="background-color:#ff0000;"> original </span><span style="background-color:#aa0000;">request (in</span><span style="background-color:#ff0000;"> Unicode</span><span style="background-color:#aa0000;"> characters</span><span style="background-color:#ff0000;">).&quot;&quot;&quot;</span><span style="background-color:#ff0000;">                 </span>
<span style="color:#0000ff;background-color:#00ff00;">    19</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;">&quot;&quot;&quot;</span><span style="background-color:#00aa00;">La longitud del</span><span style="background-color:#00ff00;"> </span><span style="background-color:#00aa00;">sufijo en la solicitud</span><span style="background-color:#00ff00;"> original </span><span style="background-color:#00aa00;">(en caracteres</span><span style="background-color:#00ff00;"> Unicode</span><span style="background-color:#00aa00;"></span><span style="background-color:#00ff00;">).&quot;&quot;&quot;</span><span style="background-color:#00ff00;">                  </span>
<span style="color:#0000ff;"> 21 21<span class="codicon codicon-blank"></span> After context                                                                                   </span></span></pre>
