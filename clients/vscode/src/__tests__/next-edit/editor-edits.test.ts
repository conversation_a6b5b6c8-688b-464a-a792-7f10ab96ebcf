import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";

import { makeDirs, writeFileUtf8 } from "../../__mocks__/fs-utils";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import {
    generateMockWorkspaceConfig,
    generateUncheckedMockWorkspaceConfig,
} from "../../__mocks__/mock-augment-config";
import {
    debug,
    DebugSession,
    ExtensionContext,
    getWorkspaceFolder,
    mockWorkspaceConfigChange,
    MutableTextDocument,
    publishTextEditorSelectionChange,
    publishWorkspaceFoldersChange,
    Range,
    Selection,
    TextDocument,
    TextEdit,
    TextEditor,
    TextEditorSelectionChangeKind,
    TrackedDisposable,
    Uri,
    window,
    workspace,
    WorkspaceEdit,
} from "../../__mocks__/vscode-mocks";
import { DiagnosticsManager } from "../../diagnostics";
import { FeatureFlagManager } from "../../feature-flags";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";
import { NextEditSessionEventReporter } from "../../metrics/next-edit-session-event-reporter";
import {
    AfterPreview,
    Animating,
    BeforePreview,
    HintedSuggestion,
    Hinting,
    NoSuggestions,
} from "../../next-edit/background-state";
import { CompletionVisibilityWatcher } from "../../next-edit/completion-visibility-watcher";
import { EditorNextEdits } from "../../next-edit/editor-edits";
import { NextEditConfigManager } from "../../next-edit/next-edit-config-manager";
import {
    NextEditMode,
    NextEditResultInfo,
    NextEditScope,
    NextEditSessionEventName,
    NextEditSessionEventSource,
    SuggestionState,
} from "../../next-edit/next-edit-types";
import { NextEditRequestManager } from "../../next-edit/request-manager-impl";
import { EditSuggestion, SuggestionManager } from "../../next-edit/suggestion-manager";
import { SuggestionManagerImpl } from "../../next-edit/suggestion-manager-impl";
import { StateController } from "../../statusbar/state-controller";
import { StatusBarManager } from "../../statusbar/status-bar-manager";
import { AugmentGlobalState } from "../../utils/context";
import { EphemeralObservable } from "../../utils/ephemeral-flag";
import * as fsUtils from "../../utils/fs-utils";
import { KeybindingWatcher } from "../../utils/keybindings";
import { LineRange } from "../../utils/ranges";
import { RecentItems } from "../../utils/recent-items";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitWorkspaceState,
    createSourceFolderFiles,
    defaultWorkspaceFolder,
    defaultWorkspaceState,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";
import { createSuggestion } from "./test-utils";

const _DEBOUNCE_MS = 5;

class EditorEditTestKit extends WorkspaceManagerTestKit {
    static readonly workspaceFolder = defaultWorkspaceFolder;
    static readonly folderState = defaultWorkspaceState;

    public readonly ctx = new ExtensionContext();
    public readonly apiServer = new MockAPIServer();
    public augmentGlobalState: AugmentGlobalState;
    public mockKeybindingWatcher: KeybindingWatcher;
    public workspaceManager: WorkspaceManager;
    public diagnosticsManager: DiagnosticsManager;
    public eventReporter: NextEditSessionEventReporter;
    public blobNameCalculator: BlobNameCalculator;
    public editorNextEdits: EditorNextEdits;
    public suggestionManager: SuggestionManager;
    public requestManager: NextEditRequestManager;
    public clientMetricsReporter: ClientMetricsReporter;
    public completionJustAccepted: EphemeralObservable<boolean>;
    public completionVisibilityWatcher: CompletionVisibilityWatcher;
    public nextEditConfigManager: NextEditConfigManager;

    static async create() {
        const kit = new EditorEditTestKit();
        await kit.awaitStartup();
        return kit;
    }

    private constructor() {
        super();

        createSourceFolderFiles(EditorEditTestKit.workspaceFolder);
        const workspaceFolders = [
            getWorkspaceFolder(EditorEditTestKit.workspaceFolder.folderRootUri),
        ];
        publishWorkspaceFoldersChange(workspaceFolders);

        this.augmentGlobalState = new AugmentGlobalState(new ExtensionContext());
        this.mockKeybindingWatcher = new KeybindingWatcher(this.augmentGlobalState);
        this.addDisposable(this.mockKeybindingWatcher);

        this.workspaceManager = this.createWorkspaceManager();
        this.addDisposable(this.workspaceManager);

        this.diagnosticsManager = new DiagnosticsManager();
        this.addDisposable(this.diagnosticsManager);

        this.eventReporter = new NextEditSessionEventReporter(this.apiServer);
        this.addDisposable(this.eventReporter);

        this.blobNameCalculator = new BlobNameCalculator(1_000_000);

        const statusBarManager = this.addDisposable(new StatusBarManager());
        const stateController = new StateController(statusBarManager);

        const suggestionManagerImpl = new SuggestionManagerImpl(
            this.workspaceManager,
            this.eventReporter
        );
        this.addDisposable(suggestionManagerImpl);
        // TODO(moogi): improve on this in followup PR
        // whoever creates the disposable should dispose it, but in this case it is either external
        // or created internally.. with a bit of refactoring we can handle both cases.
        this.addDisposable(suggestionManagerImpl.suggestionWasJustAccepted);
        this.suggestionManager = suggestionManagerImpl;

        this.clientMetricsReporter = new ClientMetricsReporter(this.apiServer);
        this.completionJustAccepted = new EphemeralObservable();
        this.addDisposable(this.completionJustAccepted);

        this.completionVisibilityWatcher = new CompletionVisibilityWatcher();
        this.addDisposable(this.completionVisibilityWatcher);

        const featureFlagManager = new FeatureFlagManager();
        featureFlagManager.update({
            ...featureFlagManager.currentFlags,
            vscodeNextEditMinVersion: "0.0.0",
            vscodeNextEditUx1MaxVersion: "99999.0.0",
        });

        this.nextEditConfigManager = new NextEditConfigManager(
            this.configListener,
            featureFlagManager,
            this.augmentGlobalState
        );

        this.requestManager = new NextEditRequestManager(
            this.apiServer,
            this.configListener,
            this.workspaceManager,
            this.diagnosticsManager,
            this.eventReporter,
            this.clientMetricsReporter,
            this.blobNameCalculator,
            this.suggestionManager,
            new RecentItems<NextEditResultInfo>(10),
            stateController,
            this.completionJustAccepted,
            featureFlagManager
        );
        this.addDisposable(this.requestManager);

        this.editorNextEdits = new EditorNextEdits(
            this.ctx,
            this.workspaceManager,
            this.eventReporter,
            this.mockKeybindingWatcher,
            this.configListener,
            this.suggestionManager,
            this.requestManager,
            this.globalState,
            this.nextEditConfigManager,
            this.completionVisibilityWatcher
        );
        this.addDisposable(this.editorNextEdits);

        // we must dispose of this after editorNextEdits.
        this.addDisposable(featureFlagManager);
    }

    public get folderRootUri(): Uri {
        return EditorEditTestKit.workspaceFolder.folderRootUri;
    }

    public async awaitStartup() {
        await awaitWorkspaceState(this.workspaceManager, EditorEditTestKit.folderState);
    }
}

describe("EditorNextEdit", () => {
    let kit: EditorEditTestKit;

    beforeEach(async () => {
        window.activeTextEditor = undefined;
        jest.useFakeTimers();
        kit = await EditorEditTestKit.create();

        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                nextEdit: { enableGlobalBackgroundSuggestions: true },
                advanced: { nextEdit: { useDebounceMs: _DEBOUNCE_MS } },
            })
        );

        jest.spyOn(fsUtils, "fileExists").mockImplementation((_pathName: string) => {
            return true;
        });

        debug.activeDebugSession = undefined;
    });

    afterEach(() => {
        jest.useRealTimers();
        kit.dispose();
        TrackedDisposable.assertDisposed();
    });

    describe("next suggestion", () => {
        let editor: TextEditor;
        let doc: TextDocument;
        let otherDoc: TextDocument;

        beforeEach(() => {
            doc = new TextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new TextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [doc, otherDoc];

            editor.selection = new Selection(0, 0, 0, 0);
        });

        it("initializes as undefined", async () => {
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
        });

        it("updates to the closest forward suggestion when a new suggestion is added or cursor moves", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            editor.selection = new Selection(3, 0, 3, 0);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[1], true));
            editor.selection = new Selection(4, 0, 4, 0);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
        });

        it("updates to the next closest forward suggestion when a suggestion is rejected", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            kit.suggestionManager.reject([suggestions[0]]);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[1], true));
        });

        it("updates to a suggestion in another file if it is WORKSPACE", async () => {
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2",
                    { scope: NextEditScope.File }
                ),
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0",
                    { scope: NextEditScope.Workspace }
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[1], true));
        });

        it("if !enableGlobalBackgroundSuggestions, returns undefined if all suggestions are in other files", async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableGlobalBackgroundSuggestions: false },
                })
            );
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
            window.showTextDocument(otherDoc, { selection: new Range(0, 0, 0, 0) });
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
        });

        it("prefers a suggestion in the current file over other files", async () => {
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0",
                    { scope: NextEditScope.Workspace }
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add([suggestions[0]]);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            kit.suggestionManager.add([suggestions[1]]);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[1], true));
        });

        it("draws decorations after next changes", async () => {
            jest.spyOn((kit.editorNextEdits as any)._decorationManager, "decorate");
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            expect((kit.editorNextEdits as any)._decorationManager.decorate).toHaveBeenCalledWith(
                suggestions,
                {
                    hintSuggestion: new HintedSuggestion(suggestions[0], true),
                    activeSuggestion: undefined,
                    isAnimating: false,
                }
            );
        });

        it("does not draw decorations after next changes when in debug mode", async () => {
            jest.spyOn((kit.editorNextEdits as any)._decorationManager, "decorate");
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            debug.activeDebugSession = new DebugSession("42");
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            expect((kit.editorNextEdits as any)._decorationManager.decorate).toHaveBeenCalledWith(
                [],
                {}
            );
        });

        it("prefers onscreen previous over offscreen next", async () => {
            jest.spyOn((kit.editorNextEdits as any)._decorationManager, "decorate");
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            editor.selection = new Selection(2, 0, 2, 0);
            editor.visibleRanges = [new Range(0, 0, 1, 0)];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], false));
            expect((kit.editorNextEdits as any)._decorationManager.decorate).toHaveBeenCalledWith(
                suggestions,
                {
                    hintSuggestion: new HintedSuggestion(suggestions[0], false),
                    activeSuggestion: undefined,
                    isAnimating: false,
                }
            );
        });
    });

    describe("active suggestion", () => {
        let editor: TextEditor;
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;

        beforeEach(() => {
            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor];
            workspace.textDocuments = [doc, otherDoc];
        });

        it.each([
            [true, Animating],
            [false, BeforePreview],
        ])(
            "is set to the suggestion when the hover is shown with autoApply=%s",
            async (autoApply: boolean, state: typeof Animating | typeof BeforePreview) => {
                mockWorkspaceConfigChange(
                    generateMockWorkspaceConfig({
                        nextEdit: { enableAutoApply: autoApply },
                    })
                );
                editor.selection = new Selection(0, 0, 0, 0);
                const suggestions = [
                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(0, 1),
                        "modified line 0"
                    ),

                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(4, 4),
                        "new line 5"
                    ),
                ];
                kit.suggestionManager.add(suggestions);
                expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
                await kit.editorNextEdits.open(suggestions[0]);
                expect(kit.editorNextEdits.state).toBeInstanceOf(state);
                expect((kit.editorNextEdits.state as any).suggestion).toStrictEqual(suggestions[0]);
                await kit.editorNextEdits.open(suggestions[1]);
                expect(kit.editorNextEdits.state).toBeInstanceOf(state);
                expect((kit.editorNextEdits.state as any).suggestion).toStrictEqual(suggestions[1]);
            }
        );

        it("remains when the suggestion is accepted", async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableAutoApply: false },
                })
            );
            editor.selection = new Selection(0, 0, 0, 0);
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            await kit.editorNextEdits.open(suggestions[0]);
            expect(kit.editorNextEdits.state).toStrictEqual(new BeforePreview(suggestions[0]));
            await kit.editorNextEdits.accept();
            expect(kit.editorNextEdits.state).toStrictEqual(
                new AfterPreview(
                    suggestions[0].with({
                        state: SuggestionState.accepted,
                    })
                )
            );
        });

        it.each([
            [true, Animating],
            [false, BeforePreview],
        ])("[auto-apply: %s] cleared when the cursor moves", async (autoApply, state) => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableAutoApply: autoApply },
                })
            );
            editor.selection = new Selection(0, 0, 0, 0);
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            await kit.editorNextEdits.open(suggestions[0]);
            expect(kit.editorNextEdits.state).toBeInstanceOf(state);
            expect((kit.editorNextEdits.state as any).suggestion).toStrictEqual(suggestions[0]);
            editor.selection = new Selection(2, 0, 2, 0);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
        });
    });

    describe("accept", () => {
        let doc: MutableTextDocument;

        beforeEach(() => {
            // Setup the test by creating a single file with a couple of lines.
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\nline1\nline2\nline3\nline4\n"
            );
            // Set the active editor to the document.
            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];

            kit.suggestionManager.add([
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2"
                ),
            ]);

            kit.editorNextEdits["_state"].value = new NoSuggestions();
        });

        it("accepts the suggestion and puts it in AfterPreview", async () => {
            // Set the cursor to the first suggestion and show the hover.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            window.activeTextEditor!.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
            window.activeTextEditor!.visibleRanges = [new Range(0, 0, 5, 0)];
            kit.editorNextEdits["_state"].value = new BeforePreview(suggestions[0]);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.editorNextEdits.state).toStrictEqual(new BeforePreview(suggestions[0]));
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            kit.editorNextEdits.accept();
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
        });

        it("dismisses decorations for suggestions in AfterPreview", async () => {
            // Set the cursor to the first suggestion and put it in an accepted state.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            window.activeTextEditor!.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
            kit.suggestionManager.accept([suggestions[0]]);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
            kit.editorNextEdits["_state"].value = new AfterPreview(suggestions[0]);
            await jest.advanceTimersByTimeAsync(1);

            kit.editorNextEdits.accept();
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
        });
    });

    describe("reject", () => {
        let doc: MutableTextDocument;

        beforeEach(() => {
            // Setup the test by creating a single file with a couple of lines.
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\nline1\nline2\nline3\nline4\n"
            );
            // Set the active editor to the document.
            window.activeTextEditor = new TextEditor(doc);

            kit.suggestionManager.add([
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ]);

            kit.editorNextEdits["_state"].value = new NoSuggestions();
        });

        it("rejects only the hover suggestion if it exists", async () => {
            // Set the cursor to the first suggestion and show the hover.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            window.activeTextEditor!.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
            kit.editorNextEdits["_state"].value = new BeforePreview(suggestions[0]);

            // Let the event loop run.
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.editorNextEdits.state).toStrictEqual(new BeforePreview(suggestions[0]));
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(2);
            kit.editorNextEdits.reject();
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
        });

        it("rejects all suggestions if no hover suggestion exists", async () => {
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(2);
            kit.editorNextEdits.reject();
            expect(kit.suggestionManager.getActiveSuggestions()).toEqual([]);
        });

        it("draws decorations after reject", async () => {
            jest.spyOn((kit.editorNextEdits as any)._decorationManager, "decorate");
            kit.editorNextEdits.reject();
            expect((kit.editorNextEdits as any)._decorationManager.decorate).toHaveBeenCalledWith(
                [],
                {
                    activeSuggestion: undefined,
                    isAnimating: false,
                }
            );
        });
    });

    describe("open", () => {
        let editor: TextEditor;
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;

        beforeEach(() => {
            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor];
            workspace.textDocuments = [doc, otherDoc];
        });

        it.each([
            [true, Animating],
            [false, BeforePreview],
        ])("activates the opened suggestion if is fresh", async (autoApply, state) => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableAutoApply: autoApply },
                })
            );
            editor.selection = new Selection(0, 0, 0, 0);
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
            await kit.editorNextEdits.open(suggestions[0]);
            expect(kit.editorNextEdits.state).toBeInstanceOf(state);
            expect(kit.editorNextEdits.state.suggestion).toStrictEqual(suggestions[0]);
        });

        it("doesn't activate the opened suggestion if is stale", async () => {
            editor.selection = new Selection(0, 0, 0, 0);
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2"
                ).with({ state: SuggestionState.stale }),
            ];
            kit.suggestionManager.add(suggestions);
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
            await kit.editorNextEdits.open(suggestions[0]);
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
            expect(editor.selection).toStrictEqual(new Selection(0, 0, 0, 0));
        });

        it("opens accepted suggestions in AfterPreview", async () => {
            editor.selection = new Selection(0, 0, 0, 0);
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            // Accept the suggestion and exit the after preview.
            kit.editorNextEdits.acceptSuggestion(suggestions[0]);
            await jest.advanceTimersByTimeAsync(1);
            kit.editorNextEdits.dismiss();
            editor.selection = new Selection(0, 0, 0, 0);
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());

            // Now try to open the suggestion.
            await kit.editorNextEdits.open(
                suggestions[0].with({ state: SuggestionState.accepted })
            );
            expect(kit.editorNextEdits.state).toBeInstanceOf(AfterPreview);
            expect(editor.selection).toStrictEqual(new Selection(1, 0, 1, 0));
        });

        it.each([
            [false, true, 150],
            [false, false, 0],
            [true, true, 150],
            [true, false, 0],
        ])(
            "[auto-apply: %s] when smooth scroll is %s, it waits %s ms",
            async (autoApply, useSmoothScroll, delayMs: number) => {
                mockWorkspaceConfigChange(
                    generateMockWorkspaceConfig({
                        nextEdit: { enableAutoApply: autoApply },
                    })
                );
                workspace.getConfiguration.mockImplementation((key: string) => {
                    if (key === "editor") {
                        return generateUncheckedMockWorkspaceConfig({
                            smoothScrolling: useSmoothScroll,
                        });
                    } else if (key === "augment") {
                        return generateMockWorkspaceConfig({
                            nextEdit: { enableAutoApply: autoApply },
                        });
                    } else {
                        return generateUncheckedMockWorkspaceConfig();
                    }
                });
                editor.selection = new Selection(0, 0, 0, 0);
                editor.visibleRanges = [new Range(0, 0, 1, 0)];
                const suggestions = [
                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(3, 4),
                        "modified line 3"
                    ),
                ];
                kit.suggestionManager.add(suggestions);
                expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
                void kit.editorNextEdits.open(suggestions[0]);
                if (delayMs > 0) {
                    await jest.advanceTimersByTimeAsync(delayMs - 1);
                    expect(kit.editorNextEdits.state).toStrictEqual(
                        new Hinting(suggestions[0], true)
                    );
                }
                await jest.advanceTimersByTimeAsync(1);
                if (autoApply) {
                    expect(kit.editorNextEdits.state).toStrictEqual(
                        new Animating(suggestions[0], editor.selection, expect.anything())
                    );
                } else {
                    expect(kit.editorNextEdits.state).toStrictEqual(
                        new BeforePreview(suggestions[0])
                    );
                }

                workspace.getConfiguration.mockClear();
            }
        );

        it.each([
            [true, Animating],
            [false, BeforePreview],
        ])(
            "when canAnimate is %s, it gets state %s",
            async (canAnimate: boolean, expectedState: typeof BeforePreview | typeof Animating) => {
                mockWorkspaceConfigChange(
                    generateMockWorkspaceConfig({
                        nextEdit: { enableAutoApply: true },
                    })
                );
                editor.selection = new Selection(0, 0, 0, 0);
                editor.visibleRanges = [new Range(0, 0, 1, 0)];
                const suggestions = [
                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(3, 4),
                        "modified line 3"
                    ),
                ];
                kit.suggestionManager.add(suggestions);
                expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestions[0], true));
                await kit.editorNextEdits.open(suggestions[0], { shouldAutoApply: canAnimate });
                expect(kit.editorNextEdits.state).toBeInstanceOf(expectedState);
            }
        );

        it.each([
            [undefined, EditorNextEdits["_applySuggestionDelayMs"].atCursor],
            [42, 42],
        ])(
            "properly handles animation delay",
            async (animationDelayMs: number | undefined, expectedDelayMs: number) => {
                editor.selection = new Selection(0, 0, 0, 0);
                const suggestions = [
                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(0, 1),
                        "modified line 0"
                    ).with({ state: SuggestionState.stale }),
                ];
                kit.suggestionManager.add(suggestions);
                const result = await kit.editorNextEdits["gotoSuggestion"](
                    suggestions[0],
                    animationDelayMs
                );
                expect(result).toBe(expectedDelayMs);
            }
        );
    });

    describe("detecting git changes", () => {
        let doc: TextDocument;
        let suggestions: EditSuggestion[];
        beforeEach(() => {
            doc = new TextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
            kit.suggestionManager.add(suggestions);
        });

        it("clears suggestions on stash changes", async () => {
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);

            makeDirs(Uri.joinPath(kit.folderRootUri, "/.git/refs").fsPath);
            writeFileUtf8(Uri.joinPath(kit.folderRootUri, "/.git/refs/stash").fsPath, "modified");

            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
        });

        it("clears suggestions on head changes", async () => {
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);

            makeDirs(Uri.joinPath(kit.folderRootUri, "/.git/logs").fsPath);
            writeFileUtf8(Uri.joinPath(kit.folderRootUri, "/.git/logs/HEAD").fsPath, "modified");

            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
        });
    });

    describe("next()", () => {
        let editor: TextEditor;
        let doc: TextDocument;
        let otherDoc: TextDocument;

        beforeEach(() => {
            doc = new TextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new TextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor, new TextEditor(otherDoc)];
            workspace.textDocuments = [doc, otherDoc];

            editor.selection = new Selection(0, 0, 0, 0);
        });

        let getNext = async () => {
            kit.editorNextEdits.next();
            return kit.editorNextEdits.state.suggestion;
        };

        it("returns undefined if no suggestions", async () => {
            expect(await getNext()).toBeUndefined();
        });

        it("returns closest forward suggestion", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(await getNext()).toBe(suggestions[0]);
            editor.selection = new Selection(3, 0, 3, 0);
            expect(await getNext()).toBe(suggestions[1]);
            editor.selection = new Selection(4, 0, 4, 0);
            expect(await getNext()).toBe(suggestions[0]);
        });

        it("returns suggestion in another file", async () => {
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0",
                    { scope: NextEditScope.Workspace }
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(await getNext()).toBe(suggestions[0]);
        });

        it("if !enableGlobalBackgroundSuggestions, returns undefined if suggestions are in other files", async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableGlobalBackgroundSuggestions: false },
                })
            );
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(await getNext()).toBeUndefined();
        });

        it("prefers a suggestion in the current file over other files", async () => {
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0",
                    { scope: NextEditScope.Workspace }
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            window.showTextDocument(doc);
            kit.suggestionManager.add([suggestions[0]]);
            expect(await getNext()).toBe(suggestions[0]);
            // Reset the active editor.
            window.showTextDocument(doc);
            kit.suggestionManager.add([suggestions[1]]);
            expect(await getNext()).toBe(suggestions[1]);
        });
    });

    describe("previous()", () => {
        let editor: TextEditor;
        let doc: TextDocument;
        let otherDoc: TextDocument;

        beforeEach(() => {
            doc = new TextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new TextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor, new TextEditor(otherDoc)];
            workspace.textDocuments = [doc, otherDoc];

            editor.selection = new Selection(0, 0, 0, 0);
        });

        let getPrevious = async () => {
            await kit.editorNextEdits.previous();
            return kit.editorNextEdits.state.suggestion;
        };

        it("returns undefined if no suggestions", async () => {
            expect(await getPrevious()).toBeUndefined();
        });

        it("returns closest backward suggestion", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(1, 2),
                    "modified line 1"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            editor.selection = new Selection(0, 0, 0, 0);
            expect(await getPrevious()).toBe(suggestions[1]);
            editor.selection = new Selection(2, 0, 2, 0);
            expect(await getPrevious()).toBe(suggestions[0]);
            editor.selection = new Selection(5, 0, 5, 0);
            expect(await getPrevious()).toBe(suggestions[1]);
        });

        it("returns suggestion in another file", async () => {
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0",
                    { scope: NextEditScope.Workspace }
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(await getPrevious()).toBe(suggestions[0]);
        });

        it("if !enableGlobalBackgroundSuggestions, returns undefined if suggestions are in other files", async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableGlobalBackgroundSuggestions: false },
                })
            );
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            expect(await getPrevious()).toBeUndefined();
        });

        it("prefers a suggestion in the current file over other files", async () => {
            const suggestions = [
                createSuggestion(
                    otherDoc,
                    kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0",
                    { scope: NextEditScope.Workspace }
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),
            ];
            window.showTextDocument(doc);
            kit.suggestionManager.add([suggestions[0]]);
            expect(await getPrevious()).toBe(suggestions[0]);
            // Reset the active editor.
            window.showTextDocument(doc);
            kit.suggestionManager.add([suggestions[1]]);
            expect(await getPrevious()).toBe(suggestions[1]);
        });
    });

    describe("gotoHinting()", () => {
        let editor: TextEditor;
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;

        beforeEach(() => {
            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor, new TextEditor(otherDoc)];
            workspace.textDocuments = [doc, otherDoc];

            editor.selection = new Selection(0, 0, 0, 0);
        });

        let getNext = async () => {
            await kit.editorNextEdits.gotoNextSmart();
            return kit.editorNextEdits.state.suggestion;
        };

        it("returns undefined if no suggestions", async () => {
            expect(await getNext()).toBeUndefined();
        });

        it("returns the hinted suggestion in hinting state", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            editor.selection = new Selection(0, 0, 0, 0);
            expect(kit.editorNextEdits.state).toBeInstanceOf(Hinting);
            expect(kit.editorNextEdits.state.suggestion).toBe(suggestions[0]);
            expect(await getNext()).toBe(suggestions[0]);

            editor.selection = new Selection(3, 0, 3, 0);
            expect(kit.editorNextEdits.state).toBeInstanceOf(Hinting);
            expect(kit.editorNextEdits.state.suggestion).toBe(suggestions[1]);
            expect(await getNext()).toBe(suggestions[1]);
        });

        it("returns the next hinted suggestion in before preview state", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            // When we are in before preview of suggestion 0, the next hinting state
            // should be suggestion 1.
            await kit.editorNextEdits.open(suggestions[0], { shouldAutoApply: false });
            expect(kit.editorNextEdits.state).toBeInstanceOf(BeforePreview);
            expect(await getNext()).toBe(suggestions[1]);
        });

        it("returns the next hinted suggestion in after preview state", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0"
                ),

                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 4),
                    "new line 5"
                ),
            ];
            kit.suggestionManager.add(suggestions);
            // When we are in before preview of suggestion 0, the next hinting state
            // should be suggestion 1.
            await kit.editorNextEdits.open(suggestions[0]);
            await kit.editorNextEdits.accept();
            expect(kit.editorNextEdits.state).toBeInstanceOf(AfterPreview);
            // the suggestion offsets have been updated, so instead check suggestion id.
            expect(await getNext().then((s) => s?.result.suggestionId)).toBe(
                suggestions[1].result.suggestionId
            );
        });
    });

    describe("diffless mode", () => {
        let doc: MutableTextDocument;

        beforeEach(() => {
            // Setup the test by creating a single file with a couple of lines.
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\nline1\nline2\nline3\nline4\n"
            );
            // Set the active editor to the document.
            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];

            kit.suggestionManager.add([
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
            ]);
        });

        it("tracks reverse preview mode on accept and undo", async () => {
            const mockReportEventFromSuggestion = jest.spyOn(
                kit.eventReporter,
                "reportEventFromSuggestion"
            );

            // Set the cursor to the first suggestion and show the hover.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            window.activeTextEditor!.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
            window.activeTextEditor!.visibleRanges = [new Range(0, 0, 5, 0)];
            kit.editorNextEdits["_state"].value = new BeforePreview(suggestions[0]);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.editorNextEdits.state).toStrictEqual(new BeforePreview(suggestions[0]));
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);

            // Accept the suggestion and enter reverse-preview mode.
            kit.editorNextEdits.accept();
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new AfterPreview(suggestions[0].with({ state: SuggestionState.accepted }))
            );
            // Verify events were reported
            expect(mockReportEventFromSuggestion).toHaveBeenCalledWith(
                suggestions[0],
                NextEditSessionEventName.Accept,
                NextEditSessionEventSource.Command
            );

            // Undo the change and enter preview mode.
            const undoChangeEvent = new WorkspaceEdit();
            undoChangeEvent.set(doc.uri, [new TextEdit(new Range(2, 0, 3, 0), "line2\n")]);
            workspace.applyEdit(undoChangeEvent);
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            expect(kit.editorNextEdits.state).toStrictEqual(new BeforePreview(suggestions[0]));

            // Verify events were reported
            expect(mockReportEventFromSuggestion).toHaveBeenCalledWith(
                suggestions[0],
                NextEditSessionEventName.UndidAcceptedSuggestion,
                NextEditSessionEventSource.DocumentChanged
            );
        });

        it("tracks reverse preview mode on accept and click out", async () => {
            // Set the cursor to the first suggestion and show the hover.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            window.activeTextEditor!.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
            window.activeTextEditor!.visibleRanges = [new Range(0, 0, 5, 0)];
            kit.editorNextEdits["_state"].value = new BeforePreview(suggestions[0]);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.editorNextEdits.state).toStrictEqual(new BeforePreview(suggestions[0]));
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);

            // Accept the suggestion and enter reverse-preview mode.
            kit.editorNextEdits.accept();
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new AfterPreview(suggestions[0].with({ state: SuggestionState.accepted }))
            );

            // Click anywhere and exit reverse-preview mode.
            window.activeTextEditor!.selection = new Selection(
                suggestions[0].lineRange.start,
                1,
                suggestions[0].lineRange.start,
                1
            );
            publishTextEditorSelectionChange({
                textEditor: window.activeTextEditor!,
                selections: [window.activeTextEditor!.selection],
                kind: TextEditorSelectionChangeKind.Keyboard,
            });
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
        });

        it("accepting a suggestion while not in before preview ends in after preview", async () => {
            // Set the cursor to the first suggestion and show the hover.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            window.activeTextEditor!.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
            window.activeTextEditor!.visibleRanges = [new Range(0, 0, 5, 0)];
            kit.editorNextEdits["_state"].value = new NoSuggestions();
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);

            // Accept the suggestion and enter reverse-preview mode.
            kit.editorNextEdits.acceptSuggestion(suggestions[0]);
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new AfterPreview(suggestions[0].with({ state: SuggestionState.accepted }))
            );
        });
    });

    describe("animate mode", () => {
        let doc: MutableTextDocument;
        let suggestion: EditSuggestion;

        beforeEach(async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableAutoApply: true },
                })
            );
            // Setup the test by creating a single file with a couple of lines.
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\nline1\nline2\nline3\nline4\n"
            );
            // Set the active editor to the document.
            const editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            workspace.textDocuments = [doc];

            kit.suggestionManager.add([
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
            ]);

            // Set the cursor to the first suggestion.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            suggestion = suggestions[0];
            window.activeTextEditor.selection = new Selection(
                suggestion.lineRange.start,
                0,
                suggestion.lineRange.start,
                0
            );
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestion, true));

            // Open the suggestion and start the animation.
            await kit.editorNextEdits.open(suggestion);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new Animating(suggestion, window.activeTextEditor.selection, expect.anything())
            );

            // Ensure the animation doesn't complete instantly.
            await jest.advanceTimersByTimeAsync(
                EditorNextEdits["_applySuggestionDelayMs"].atCursor - 1
            );
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new Animating(suggestion, window.activeTextEditor.selection, expect.anything())
            );
            expect(doc.lineAt(2).text).toBe("line2");
        });

        it("animate mode automatically accepts", async () => {
            // Wait for the animation to finish and ensure it's done.
            await jest.advanceTimersByTimeAsync(2);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new AfterPreview(suggestion.with({ state: SuggestionState.accepted }))
            );
            expect(doc.lineAt(2).text).toBe("modified line 2");
        });

        it("clicking away cancels the animation", async () => {
            // Clicking outside of the suggestion should cancel the animation.
            window.activeTextEditor!.selection = new Selection(0, 0, 0, 0);
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestion, true));
            expect(doc.lineAt(2).text).toBe("line2");
        });

        it("typing cancels the animation", async () => {
            // Editing the buffer should cancel the animation.
            const edit = new WorkspaceEdit();
            edit.set(doc.uri, [new TextEdit(new Range(2, 0, 3, 0), "meow\n")]);
            workspace.applyEdit(edit);
            expect(kit.editorNextEdits.state).toStrictEqual(new NoSuggestions());
            await jest.advanceTimersByTimeAsync(
                EditorNextEdits["_applySuggestionDelayMs"].onScreen + 1
            );
            expect(doc.lineAt(2).text).toBe("meow");
        });

        it("goto next immediately accepts the animation", async () => {
            // Add another suggestion so we have something to which to go.
            kit.suggestionManager.add([
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0\n"
                ),
            ]);

            // Going to the next suggestion should accept the animation.
            kit.editorNextEdits.next();
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new AfterPreview(suggestion.with({ state: SuggestionState.accepted }))
            );
            expect(doc.lineAt(2).text).toBe("modified line 2");
        });

        it("rejecting cancels the animation and shows the next suggestion", async () => {
            // Add another suggestion so we have something to which to go.
            const suggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line 0\n"
            );
            kit.suggestionManager.add([suggestion]);

            // Rejecting should cancel the animation.
            kit.editorNextEdits.reject();
            expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestion, true));
            await jest.advanceTimersByTimeAsync(
                EditorNextEdits["_applySuggestionDelayMs"].onScreen + 1
            );
            expect(doc.lineAt(2).text).toBe("line2");
        });
    });

    describe("animate mode delay", () => {
        let doc: MutableTextDocument;
        let suggestion: EditSuggestion;
        let otherDoc: MutableTextDocument;

        beforeEach(async () => {
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    nextEdit: { enableAutoApply: true },
                })
            );
            // Setup the test by creating a single file with a couple of lines.
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/b.txt",
                "line0\nline1\nline2\nline3\nline4\n"
            );
            // Set the active editor to the document.
            const editor = new TextEditor(doc);
            const otherEditor = new TextEditor(otherDoc);
            window.visibleTextEditors = [editor, otherEditor];
            workspace.textDocuments = [doc, otherDoc];
            window.showTextDocument(doc);

            kit.suggestionManager.add([
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(3, 5),
                    "modified line 3\n"
                ),
            ]);
            // Mock the decorations because we don't care about how the bottom box is
            // drawn.
            jest.spyOn(
                (kit.editorNextEdits as any)._decorationManager,
                "decorate"
            ).mockImplementation(() => {});
        });

        it.each([
            [
                "atCursor",
                new Selection(2, 0, 3, 0),
                new Range(0, 0, 5, 0),
                EditorNextEdits["_applySuggestionDelayMs"].atCursor,
            ],
            [
                "onScreen",
                new Selection(0, 0, 0, 0),
                new Range(0, 0, 5, 0),
                EditorNextEdits["_applySuggestionDelayMs"].onScreen,
            ],
            [
                "offScreen",
                new Selection(0, 0, 0, 0),
                new Range(0, 0, 2, 0),
                EditorNextEdits["_applySuggestionDelayMs"].offScreen,
            ],
        ])(
            "takes %s time",
            async (_, selection: Selection, visibleRange: Range, delayMs: number) => {
                window.showTextDocument(doc);
                const editor = window.activeTextEditor!;
                editor.selection = selection;
                editor.visibleRanges = [visibleRange];

                // Set the cursor to the first suggestion.
                const suggestions = kit.suggestionManager.getActiveSuggestions();
                suggestion = suggestions[0];

                expect(kit.editorNextEdits.state).toStrictEqual(new Hinting(suggestion, true));

                // Open the suggestion and start the animation.
                await kit.editorNextEdits.open(suggestion);

                expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
                expect(kit.editorNextEdits.state).toStrictEqual(
                    new Animating(suggestion, editor.selection, expect.anything())
                );

                // Ensure the animation doesn't complete instantly.
                await jest.advanceTimersByTimeAsync(delayMs - 1);
                expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
                expect(kit.editorNextEdits.state).toStrictEqual(
                    new Animating(suggestion, editor.selection, expect.anything())
                );
                await jest.advanceTimersByTimeAsync(2);
                expect(kit.editorNextEdits.state).toStrictEqual(
                    new AfterPreview(suggestion.with({ state: SuggestionState.accepted }))
                );
                expect(doc.lineAt(3).text).toBe("modified line 3");
            }
        );

        it("takes offScreen time if suggestion is in another file", async () => {
            window.showTextDocument(otherDoc);

            // Set the cursor to the first suggestion.
            const suggestions = kit.suggestionManager.getActiveSuggestions();
            suggestion = suggestions[0];

            kit.editorNextEdits["_state"].value = new Hinting(suggestion, true);

            // Open the suggestion and start the animation.
            await kit.editorNextEdits.open(suggestion);

            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new Animating(suggestion, expect.anything(), expect.anything())
            );

            // Ensure the animation doesn't complete instantly.
            await jest.advanceTimersByTimeAsync(
                EditorNextEdits["_applySuggestionDelayMs"].offScreen - 1
            );
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new Animating(suggestion, expect.anything(), expect.anything())
            );
            await jest.advanceTimersByTimeAsync(2);
            expect(kit.editorNextEdits.state).toStrictEqual(
                new AfterPreview(suggestion.with({ state: SuggestionState.accepted }))
            );
            expect(doc.lineAt(3).text).toBe("modified line 3");
        });
    });

    describe("acceptAllSuggestions", () => {
        let doc: MutableTextDocument;

        beforeEach(async () => {
            // Setup the test document
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\nline1\nline2\nline3\n"
            );
            // Set the active editor to the document
            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it("should show message when no suggestions available", async () => {
            kit.suggestionManager.clear(true);
            const showInfoSpy = jest.spyOn(window, "showInformationMessage");
            kit.editorNextEdits.acceptAllSuggestions();
            await jest.advanceTimersByTimeAsync(0);
            expect(showInfoSpy).toHaveBeenCalledWith("No Next Edits to accept.");
        });

        it("should accept all fresh suggestions", async () => {
            // Add multiple suggestions
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0\n"
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
            ];
            kit.suggestionManager.add(suggestions);

            const mockReportEventWithoutIds = jest.spyOn(
                kit.eventReporter,
                "reportEventWithoutIds"
            );

            kit.editorNextEdits.acceptAllSuggestions();

            // Verify suggestions were accepted
            expect(doc.lineAt(0).text).toBe("modified line 0");
            expect(doc.lineAt(2).text).toBe("modified line 2");
            expect(kit.editorNextEdits.state).toBeInstanceOf(NoSuggestions);

            // Verify events were reported
            expect(mockReportEventWithoutIds).toHaveBeenCalledWith(
                NextEditSessionEventName.AcceptAll,
                NextEditSessionEventSource.Command
            );
        });

        it("should filter out non-fresh suggestions", async () => {
            // Add mix of fresh and non-fresh suggestions
            const suggestions = [
                Object.assign(
                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(0, 1),
                        "modified line 0\n"
                    ),
                    { state: SuggestionState.fresh }
                ),
                Object.assign(
                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(1, 2),
                        "modified line 1\n"
                    ),
                    { state: SuggestionState.stale }
                ),
                Object.assign(
                    createSuggestion(
                        doc,
                        kit.workspaceManager.resolvePathName(doc.uri)!,
                        new LineRange(2, 3),
                        "modified line 2\n"
                    ),
                    { state: SuggestionState.fresh }
                ),
            ];
            kit.suggestionManager.add(suggestions);

            kit.editorNextEdits.acceptAllSuggestions();

            // Verify only fresh suggestions were accepted
            expect(doc.lineAt(0).text).toBe("modified line 0");
            expect(doc.lineAt(1).text).toBe("line1");
            expect(doc.lineAt(2).text).toBe("modified line 2");
        });
    });

    describe("rejectAllSuggestions", () => {
        let doc: MutableTextDocument;

        beforeEach(async () => {
            // Setup the test document
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\nline1\nline2\nline3\n"
            );
            // Set the active editor to the document
            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it("should show message when no suggestions available", async () => {
            kit.suggestionManager.clear(true);
            const showInfoSpy = jest.spyOn(window, "showInformationMessage");
            kit.editorNextEdits.rejectAllSuggestions();
            await jest.advanceTimersByTimeAsync(0);
            expect(showInfoSpy).toHaveBeenCalledWith("No Next Edits to reject.");
        });

        it("should reject all suggestions", async () => {
            // Add multiple suggestions
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0\n"
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
            ];
            kit.suggestionManager.add(suggestions);

            const mockReportEventWithoutIds = jest.spyOn(
                kit.eventReporter,
                "reportEventWithoutIds"
            );

            kit.editorNextEdits.rejectAllSuggestions();

            // Verify suggestions were rejected
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
            expect(doc.lineAt(0).text).toBe("line0");
            expect(doc.lineAt(2).text).toBe("line2");
            expect(kit.editorNextEdits.state).toBeInstanceOf(NoSuggestions); // Verify events were reported
            expect(mockReportEventWithoutIds).toHaveBeenCalledWith(
                NextEditSessionEventName.RejectAll,
                NextEditSessionEventSource.Command
            );
        });
    });

    describe("acceptAllSuggestionsInFile", () => {
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;

        beforeEach(() => {
            // Setup two documents in the workspace
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\naLine1\naLine2\n"
            );
            otherDoc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/b.txt",
                "line0\nbLine1\nbLine2\n"
            );
            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc, otherDoc];
        });

        it("should show message when there are no suggestions in the specified file", async () => {
            // Clear any existing suggestions
            kit.suggestionManager.clear(true);

            // Spy on showInformationMessage
            const showInfoSpy = jest.spyOn(window, "showInformationMessage");

            // Attempt to accept suggestions in doc
            kit.editorNextEdits.acceptAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            // Expect the user info message
            await jest.advanceTimersByTimeAsync(0);
            expect(showInfoSpy).toHaveBeenCalledWith("No Next Edits to accept.");
        });

        it("should accept all suggestions in the specified file", async () => {
            // Add suggestions to both doc and otherDoc
            const suggestionA1 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line0 in A\n"
            );
            const suggestionA2 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(1, 2),
                "modified line1 in A\n"
            );
            const suggestionB1 = createSuggestion(
                otherDoc,
                kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                new LineRange(0, 1),
                "modified line0 in B"
            );
            kit.suggestionManager.add([suggestionA1, suggestionA2, suggestionB1]);

            // Spy on event reporting
            const mockReportEventWithoutIds = jest.spyOn(
                kit.eventReporter,
                "reportEventWithoutIds"
            );

            // Accept all suggestions in doc only
            kit.editorNextEdits.acceptAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            // Verify doc suggestions are accepted
            expect(doc.lineAt(0).text).toBe("modified line0 in A");
            expect(doc.lineAt(1).text).toBe("modified line1 in A");

            // Verify otherDoc's suggestion remains fresh
            expect(kit.suggestionManager.getActiveSuggestions()).toContainEqual(suggestionB1);

            // Verify the accept event is reported
            expect(mockReportEventWithoutIds).toHaveBeenCalledWith(
                NextEditSessionEventName.AcceptAllInFile,
                NextEditSessionEventSource.Command
            );
        });

        it("should ignore suggestions that are not in the specified file", async () => {
            // Add suggestions to both doc and otherDoc
            const suggestionA1 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line0 in A\n"
            );
            const suggestionB1 = createSuggestion(
                otherDoc,
                kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                new LineRange(0, 1),
                "modified line0 in B"
            );
            kit.suggestionManager.add([suggestionA1, suggestionB1]);

            // Accept suggestions only for doc
            kit.editorNextEdits.acceptAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            // Expect doc suggestion to be accepted
            expect(doc.lineAt(0).text).toBe("modified line0 in A");
            // Expect otherDoc suggestion to remain unaccepted
            expect(otherDoc.lineAt(0).text).toBe("line0");
        });
    });

    describe("rejectAllSuggestionsInFile", () => {
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;

        beforeEach(() => {
            // Setup two documents in the workspace
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\naLine1\naLine2\n"
            );
            otherDoc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/b.txt",
                "line0\nbLine1\nbLine2\n"
            );

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc, otherDoc];
        });

        it("should show message when there are no suggestions in the specified file", async () => {
            // Clear any existing suggestions
            kit.suggestionManager.clear(true);

            // Spy on showInformationMessage
            const showInfoSpy = jest.spyOn(window, "showInformationMessage");

            // Attempt to reject suggestions in doc
            kit.editorNextEdits.rejectAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            // Expect the user info message
            await jest.advanceTimersByTimeAsync(0);
            expect(showInfoSpy).toHaveBeenCalledWith("No Next Edits to reject.");
        });

        it("should reject all suggestions in the specified file", async () => {
            // Add suggestions to both doc and otherDoc
            const suggestionA1 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line0 in A\n"
            );
            const suggestionA2 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(1, 2),
                "modified line1 in A\n"
            );
            const suggestionB1 = createSuggestion(
                otherDoc,
                kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                new LineRange(0, 1),
                "modified line0 in B"
            );
            kit.suggestionManager.add([suggestionA1, suggestionA2, suggestionB1]);

            // Spy on event reporting
            const mockReportEventWithoutIds = jest.spyOn(
                kit.eventReporter,
                "reportEventWithoutIds"
            );

            // Reject all suggestions in doc only
            kit.editorNextEdits.rejectAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            // Verify otherDoc's suggestion remains
            expect(kit.suggestionManager.getActiveSuggestions()).toContainEqual(suggestionB1);

            // Verify doc suggestions are rejected
            expect(kit.suggestionManager.getActiveSuggestions()).not.toContainEqual(suggestionA1);
            expect(kit.suggestionManager.getActiveSuggestions()).not.toContainEqual(suggestionA2);

            // Verify the reject event is reported
            expect(mockReportEventWithoutIds).toHaveBeenCalledWith(
                NextEditSessionEventName.RejectAllInFile,
                NextEditSessionEventSource.Command
            );
        });
    });

    describe("multiple accepted suggestions", () => {
        let editor: TextEditor;
        let doc: MutableTextDocument;

        beforeEach(() => {
            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            workspace.textDocuments = [doc];
            editor.selection = new Selection(0, 0, 0, 0);
        });

        it("should go to the last suggestion when multiple are accepted", async () => {
            const suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(3, 4),
                    "modified line 3\n"
                ),
            ];

            // Add suggestions to manager
            kit.suggestionManager.add(suggestions);

            // Accept the first suggestion; the cursor should move to its target line
            // (typically one line before the suggestion).
            await kit.editorNextEdits.acceptSuggestion(suggestions[0]);
            expect(editor.selection.start.line).toBe(suggestions[0].previewTargetCursorLine);
            // This waits for the "just accepted" state to be cleared.
            await jest.advanceTimersByTimeAsync(1);

            // Accept the second suggestion; the cursor should move to its target line
            await kit.editorNextEdits.acceptSuggestion(suggestions[1]);
            expect(editor.selection.start.line).toBe(suggestions[1].previewTargetCursorLine);
        });
    });

    describe("acceptSuggestion", () => {
        let editor: TextEditor;
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;

        beforeEach(() => {
            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            editor = new TextEditor(doc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [doc, otherDoc];
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            editor.selection = new Selection(0, 0, 0, 0);
        });

        it("should enter before preview when not already in before preview", async () => {
            // Add suggestions to doc
            const suggestionA1 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line0 in A\n"
            );
            kit.suggestionManager.add([suggestionA1]);
            expect(kit.editorNextEdits.state).toBeInstanceOf(Hinting);

            // NOTE(arun): The BeforePreview state is transient as we immediately
            // transition to AfterPreview when the suggestion is applied. We listen for
            // state changes and verify that we see the expected states.
            const listener = jest.fn();
            const disposable = kit.editorNextEdits.addStateListener((state) => {
                listener(state.toString());
            });
            await kit.editorNextEdits.acceptSuggestion(suggestionA1);
            expect(kit.editorNextEdits.state).toBeInstanceOf(AfterPreview);

            expect(listener).toHaveBeenCalledTimes(2);
            expect(listener).toHaveBeenNthCalledWith(1, "before-preview");
            expect(listener).toHaveBeenNthCalledWith(2, "after-preview");

            disposable();
        });

        it("should ignore suggestions that are not in the specified file", async () => {
            // Add suggestions to both doc and otherDoc
            const suggestionA1 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line0 in A\n"
            );
            const suggestionB1 = createSuggestion(
                otherDoc,
                kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                new LineRange(0, 1),
                "modified line0 in B"
            );
            kit.suggestionManager.add([suggestionA1, suggestionB1]);

            // Reject suggestions only for otherDoc
            kit.editorNextEdits.rejectAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(otherDoc.uri)!
            );

            // Expect otherDoc suggestion to be rejected
            expect(kit.suggestionManager.getActiveSuggestions()).not.toContain(suggestionB1);
            // Expect doc suggestion to remain
            expect(kit.suggestionManager.getActiveSuggestions()).toContain(suggestionA1);
        });
    });

    // TODO - enable when accepted suggestions are stored
    // eslint-disable-next-line jest/no-disabled-tests
    describe.skip("undoAllSuggestionsInFile", () => {
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;

        beforeEach(() => {
            // Setup two documents in the workspace
            doc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/a.txt",
                "line0\naLine1\naLine2\n"
            );
            otherDoc = kit.newDocument(
                defaultWorkspaceFolder,
                "example/b.txt",
                "line0\nbLine1\nbLine2\n"
            );

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc, otherDoc];
        });

        it("should show message when there are no suggestions in the specified file", async () => {
            // Clear any existing suggestions
            kit.suggestionManager.clear(true);

            // Spy on showInformationMessage
            const showInfoSpy = jest.spyOn(window, "showInformationMessage");

            // Attempt to undo suggestions in doc
            kit.editorNextEdits.undoAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            // Expect the user info message
            await jest.advanceTimersByTimeAsync(0);
            expect(showInfoSpy).toHaveBeenCalledWith("No Next Edits to undo.");
        });

        it("should undo all suggestions in the specified file", async () => {
            // Add suggestions to both doc and otherDoc
            const suggestionA1 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line0 in A\n"
            );
            const suggestionA2 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(1, 2),
                "modified line1 in A\n"
            );
            const suggestionB1 = createSuggestion(
                otherDoc,
                kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                new LineRange(0, 1),
                "modified line0 in B"
            );
            kit.suggestionManager.add([suggestionA1, suggestionA2, suggestionB1]);
            kit.editorNextEdits.acceptAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            expect(doc.lineAt(0).text).toBe("modified line0 in A");
            expect(doc.lineAt(1).text).toBe("modified line1 in A");

            // Spy on event reporting
            const mockReportEventWithoutIds = jest.spyOn(
                kit.eventReporter,
                "reportEventWithoutIds"
            );

            // Undo all suggestions in doc only
            kit.editorNextEdits.undoAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(doc.uri)!
            );

            // Verify doc suggestions are undone
            expect(doc.lineAt(0).text).toBe("line0");
            expect(doc.lineAt(1).text).toBe("line1");

            // Verify otherDoc remains unchanged
            expect(otherDoc.lineAt(0).text).toBe("line0");

            // Verify the undo event is reported
            expect(mockReportEventWithoutIds).toHaveBeenCalledWith(
                NextEditSessionEventName.UndoAllInFile,
                NextEditSessionEventSource.Command
            );
        });

        it("should ignore suggestions that are not in the specified file", async () => {
            // Add suggestions to both doc and otherDoc
            const suggestionA1 = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "modified line0 in A\n"
            );
            const suggestionB1 = createSuggestion(
                otherDoc,
                kit.workspaceManager.resolvePathName(otherDoc.uri)!,
                new LineRange(0, 1),
                "modified line0 in B"
            );
            kit.suggestionManager.add([suggestionA1, suggestionB1]);

            // Undo suggestions only for otherDoc
            kit.editorNextEdits.undoAllSuggestionsInFile(
                kit.workspaceManager.resolvePathName(otherDoc.uri)!
            );

            // Expect otherDoc suggestion to be undone
            expect(kit.suggestionManager.getActiveSuggestions()).not.toContain(suggestionB1);
            // Expect doc suggestion to remain
            expect(kit.suggestionManager.getActiveSuggestions()).toContain(suggestionA1);
        });
    });

    describe("dismiss", () => {
        let editor: TextEditor;
        let doc: MutableTextDocument;
        let suggestions: EditSuggestion[];

        beforeEach(() => {
            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor];
            workspace.textDocuments = [doc];

            // Add multiple suggestions
            suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0\n"
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
            ];
            kit.suggestionManager.add(suggestions);

            // Set the cursor to the first suggestion and show the hover.
            window.activeTextEditor.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
        });

        it.each([["before-preview"], ["animating"]])(
            "should dismiss decorations without rejecting the suggestion in %s",
            async (beforeState) => {
                // Set up before state.
                if (beforeState === "animating") {
                    kit.editorNextEdits["_state"].value = new Animating(
                        suggestions[0],
                        window.activeTextEditor!.selection,
                        expect.anything()
                    );
                } else {
                    kit.editorNextEdits["_state"].value = new BeforePreview(suggestions[0]);
                }

                const mockReportEventWithoutIds = jest.spyOn(
                    kit.eventReporter,
                    "reportEventWithoutIds"
                );

                // Dismiss the suggestion
                kit.editorNextEdits.dismiss();

                // Verify suggestions were not rejected
                expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(2);
                expect(kit.editorNextEdits.state).toBeInstanceOf(Hinting);
                // Verify events were reported
                expect(mockReportEventWithoutIds).toHaveBeenCalledWith(
                    NextEditSessionEventName.PreviewDecorationDismissed,
                    NextEditSessionEventSource.Command
                );
            }
        );

        it("should dismiss decorations without rejecting the suggestion in after-preview", async () => {
            // Set up before state.
            kit.suggestionManager.accept([suggestions[0]]);
            await jest.advanceTimersByTimeAsync(1);
            kit.editorNextEdits["_state"].value = new AfterPreview(
                suggestions[0].with({ state: SuggestionState.accepted })
            );

            expect(
                kit.suggestionManager.getJustAcceptedSuggestions().map((s) => s.result.suggestionId)
            ).toEqual([suggestions[0].result.suggestionId]);

            const mockReportEventWithoutIds = jest.spyOn(
                kit.eventReporter,
                "reportEventWithoutIds"
            );

            // Dismiss the suggestion
            kit.editorNextEdits.dismiss();

            // Verify suggestions were not rejected
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            expect(kit.suggestionManager.getJustAcceptedSuggestions()).toEqual([]);

            expect(kit.editorNextEdits.state).toBeInstanceOf(Hinting);
            // Verify events were reported
            expect(mockReportEventWithoutIds).toHaveBeenCalledWith(
                NextEditSessionEventName.ReverseDecorationDismissed,
                NextEditSessionEventSource.Command
            );
        });
    });

    describe("onDidChangeTextEditorSelection", () => {
        let editor: TextEditor;
        let otherEditor: TextEditor;
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;
        let suggestions: EditSuggestion[];

        beforeEach(() => {
            // Mock hover events to prevent failures from awaiting on them.
            jest.spyOn(kit.editorNextEdits._hoverProvider, "hideHoverAsync").mockImplementation(
                jest.fn()
            );

            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            otherEditor = new TextEditor(otherDoc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor, otherEditor];
            workspace.textDocuments = [doc, otherDoc];

            // Add multiple suggestions
            suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(4, 5),
                    "modified line 4\n"
                ),
            ];
            kit.suggestionManager.add(suggestions);

            // Set the cursor to the first suggestion and show the hover.
            window.activeTextEditor.selection = new Selection(0, 0, 0, 0);
        });

        it("should not dismiss decorations after accepting a suggestion until the next selection change", async () => {
            // Accept the suggestion
            await kit.editorNextEdits.acceptSuggestion(suggestions[0]);

            // Verify decorations are not dismissed
            expect(kit.editorNextEdits.state).toBeInstanceOf(AfterPreview);
            expect(kit.suggestionManager.getJustAcceptedSuggestions()).toHaveLength(1);

            // Change the selection
            window.activeTextEditor!.selection = new Selection(1, 0, 1, 0);
            await jest.advanceTimersByTimeAsync(1);

            // Verify decorations are dismissed
            expect(kit.editorNextEdits.state).toBeInstanceOf(Hinting);
            expect(kit.suggestionManager.getJustAcceptedSuggestions()).toHaveLength(0);
        });

        it("should not clear just accepted after accepting multiple suggestions until the next selection change", async () => {
            kit.editorNextEdits.acceptAllSuggestionsInFile(
                // Accept all the suggestions
                kit.workspaceManager.resolvePathName(doc.uri)!
            );
            // When the document is edited, vscode fires a selection change event.
            // This isn't handled in our mocks
            publishTextEditorSelectionChange({
                textEditor: window.activeTextEditor!,
                selections: [window.activeTextEditor!.selection],
                kind: TextEditorSelectionChangeKind.Command,
            });

            // Verify decorations are not dismissed
            expect(kit.editorNextEdits.state).toBeInstanceOf(NoSuggestions);
            expect(kit.suggestionManager.getJustAcceptedSuggestions()).toHaveLength(2);

            window.activeTextEditor!.selection = new Selection(1, 0, 1, 0);
            await jest.advanceTimersByTimeAsync(1);

            // Verify decorations are dismissed
            expect(kit.editorNextEdits.state).toBeInstanceOf(NoSuggestions);
            expect(kit.suggestionManager.getJustAcceptedSuggestions()).toHaveLength(0);
        });

        it("should not dismiss decorations after undoing a suggestion until the next selection change", async () => {
            // Set up before state.
            await kit.editorNextEdits.acceptSuggestion(suggestions[0]);

            // Undo the suggestion
            kit.editorNextEdits.undoAcceptSuggestion();
            await jest.advanceTimersByTimeAsync(1);

            // Verify decorations are not dismissed
            expect(kit.editorNextEdits.state).toBeInstanceOf(BeforePreview);
            expect(kit.suggestionManager.getJustAcceptedSuggestions()).toHaveLength(0);

            // Change the selection
            window.activeTextEditor!.selection = new Selection(1, 0, 1, 0);
            await jest.advanceTimersByTimeAsync(1);

            // Verify decorations are dismissed
            expect(kit.editorNextEdits.state).toBeInstanceOf(Hinting);
            expect(kit.suggestionManager.getJustAcceptedSuggestions()).toHaveLength(0);
        });
    });

    describe("onDidChangeActiveTextEditor", () => {
        let editor: TextEditor;
        let otherEditor: TextEditor;
        let doc: MutableTextDocument;
        let otherDoc: MutableTextDocument;
        let suggestions: EditSuggestion[];

        beforeEach(() => {
            // Mock hover events to prevent failures from awaiting on them.
            jest.spyOn(kit.editorNextEdits._hoverProvider, "hideHoverAsync").mockImplementation(
                jest.fn()
            );

            doc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/a"),
                "line0\nline1\nline2\nline3\nline4\n"
            );
            otherDoc = new MutableTextDocument(
                Uri.joinPath(kit.folderRootUri, "/example/b"),
                "line0\nline1\nline2\nline3\nline4\n"
            );

            editor = new TextEditor(doc);
            otherEditor = new TextEditor(otherDoc);
            editor.visibleRanges = [new Range(0, 0, 5, 0)];
            window.activeTextEditor = editor;
            window.visibleTextEditors = [editor, otherEditor];
            workspace.textDocuments = [doc, otherDoc];

            // Add multiple suggestions
            suggestions = [
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(0, 1),
                    "modified line 0\n"
                ),
                createSuggestion(
                    doc,
                    kit.workspaceManager.resolvePathName(doc.uri)!,
                    new LineRange(2, 3),
                    "modified line 2\n"
                ),
            ];
            kit.suggestionManager.add(suggestions);

            // Set the cursor to the first suggestion and show the hover.
            window.activeTextEditor.selection = new Selection(
                suggestions[0].lineRange.start,
                0,
                suggestions[0].lineRange.start,
                0
            );
        });

        it("should dismiss decorations when active editor changes", async () => {
            // Set up before state.
            kit.editorNextEdits["_state"].value = new BeforePreview(suggestions[0]);

            const mockDismiss = jest.spyOn(kit.editorNextEdits, "dismiss");

            // Change the active editor
            window.showTextDocument(otherDoc);
            await jest.advanceTimersByTimeAsync(1);

            // Verify suggestions were not rejected
            expect(mockDismiss).toHaveBeenCalled();
        });
    });

    test.each([
        [NextEditMode.Foreground, 0],
        [NextEditMode.Forced, 1],
    ])("forced requests should ignore rejections", (mode, count) => {
        kit.requestManager.clearCompletedRequests(mode);
        const doc = new MutableTextDocument(
            Uri.joinPath(kit.folderRootUri, "/example/a"),
            "line0\nline1\nline2\nline3\nline4\n"
        );
        const suggestion = createSuggestion(
            doc,
            kit.workspaceManager.resolvePathName(doc.uri)!,
            new LineRange(0, 1),
            "modified line 0"
        ).with({ mode });
        kit.suggestionManager.reject([suggestion]);
        kit.requestManager.lastResponse.value = suggestion;
        expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(count);
    });
});
