export const suggestion = {
    descriptionOutput: {
        logProbs: [-0.00042533875, -0.03515625, -0.00064468384, -0.00016593933],
        offsets: [0, 6, 10, 17],
        text: "Remove docstrings<|eot_id|>",
        tokenIds: [13319, 4733, 19107, 128009],
    },
    descriptionPrompt: {
        logProbs: [],
        offsets: [
            0, 17, 36, 42, 59, 61, 64, 68, 70, 80, 88, 91, 94, 99, 103, 105, 109, 114, 117, 120,
            128, 137, 149, 154, 158, 163, 170, 176, 180, 189, 192, 198, 209, 214, 219, 220, 224,
            232, 235, 240, 252, 255, 258, 263, 269, 280, 288, 299, 303, 311, 314, 318, 325, 328,
            332, 340, 343, 347, 352, 356, 357, 367, 386, 390, 407, 409, 413, 416, 420, 425, 432,
            442, 445, 452, 457, 462, 464, 466, 468, 471, 476, 478, 485, 490, 493, 494, 497, 502,
            504, 511, 516, 519, 520, 522, 524, 526, 527, 529, 531, 533, 534, 536, 540, 546, 551,
            559, 561, 565, 570, 575, 576, 580, 583, 587, 594, 600, 601, 605, 608, 612, 619, 623,
            624, 628, 629, 630, 633, 637, 640, 647, 650, 658, 669, 675, 679, 686, 693, 698, 701,
            703, 707, 712, 713, 718, 720, 724, 733, 740, 747, 748, 752, 753, 754, 757, 761, 764,
            771, 774, 778, 785, 788, 792, 801, 809, 811, 813, 821, 832, 834, 838, 840, 844, 853,
            860, 867, 868, 872, 873, 874, 877, 881, 884, 891, 894, 898, 905, 908, 912, 921, 929,
            931, 933, 941, 952, 954, 958, 962, 964, 968, 973, 978, 979, 981, 985, 990, 992, 997,
            1002, 1004, 1010, 1015, 1022, 1025, 1027, 1029, 1035, 1037, 1043, 1047, 1056, 1061,
            1065, 1077, 1081, 1085, 1093, 1098, 1099, 1105, 1108, 1113, 1118, 1119, 1121, 1127,
            1129, 1134, 1137, 1141, 1148, 1153, 1157, 1166, 1170, 1174, 1176, 1184, 1190, 1195,
            1199, 1201, 1211, 1230, 1239, 1256,
        ],
        text: '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a developer working in my codebase.\nYour task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\nHere is the code change delimited by triple backticks:\n```\n--- base/datasets/edit.py\n+++ base/datasets/edit.py\n@@ -18,17 +18,14 @@\n@class EditPosition:\n     blob_name: str\n \n     prefix_begin: int\n \n     suffix_end: int\n-    """The offset in Unicode characters where the suffix region ends in `blob_name`."""\n \n     original_prefix_length: int\n-    """The length of the prefix in the original request (in Unicode characters)."""\n \n     original_suffix_length: int\n-    """The length of the suffix in the original request (in Unicode characters)."""\n \n \n @dataclass_json\n @dataclass(frozen=True)\n class EditRequest:\n\n```\nDirectly start the response with the description and say nothing else. Write no more than 10 words.\nFocus on the fields that are changing but don\'t include where they are.\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n',
        tokenIds: [
            128000, 128006, 9125, 128007, 271, 2675, 527, 264, 16131, 3318, 304, 856, 2082, 3231,
            627, 7927, 3465, 374, 311, 8819, 9959, 2038, 505, 279, 2082, 2349, 3770, 323, 7664, 433,
            1701, 48696, 19120, 1376, 13, 578, 7580, 315, 701, 4096, 374, 311, 1520, 1023, 13707,
            6288, 3619, 1401, 13878, 315, 279, 2349, 304, 279, 2317, 315, 279, 2082, 3231, 13,
            128009, 128006, 882, 128007, 271, 8586, 374, 279, 2082, 2349, 86428, 555, 24657, 1203,
            36178, 512, 14196, 4077, 4521, 2385, 3529, 77749, 22055, 7345, 198, 63830, 2385, 3529,
            77749, 22055, 7345, 198, 19741, 482, 972, 11, 1114, 489, 972, 11, 975, 88220, 50218,
            8505, 3897, 512, 257, 24295, 1292, 25, 610, 27907, 257, 9436, 24223, 25, 528, 27907,
            257, 21166, 6345, 25, 528, 198, 12, 262, 4304, 791, 4445, 304, 36997, 5885, 1405, 279,
            21166, 5654, 10548, 304, 1595, 36212, 1292, 63, 14781, 720, 257, 4113, 14301, 5228, 25,
            528, 198, 12, 262, 4304, 791, 3160, 315, 279, 9436, 304, 279, 4113, 1715, 320, 258,
            36997, 5885, 570, 7275, 720, 257, 4113, 38251, 5228, 25, 528, 198, 12, 262, 4304, 791,
            3160, 315, 279, 21166, 304, 279, 4113, 1715, 320, 258, 36997, 5885, 570, 7275, 33006,
            571, 695, 1058, 9643, 198, 571, 695, 1058, 968, 43340, 3702, 340, 538, 8505, 1939, 1473,
            14196, 4077, 16411, 398, 1212, 279, 2077, 449, 279, 4096, 323, 2019, 4400, 775, 13,
            9842, 912, 810, 1109, 220, 605, 4339, 627, 14139, 389, 279, 5151, 430, 527, 10223, 719,
            1541, 956, 2997, 1405, 814, 527, 627, 128009, 128006, 78191, 128007, 271,
        ],
    },
    generationId: "b5d4f914-5c28-4267-a9a2-a87580b17372",
    result: {
        checkpointNotFound: false,
        suggestedEdit: {
            blobName: "27201f4ce7a823cc7c3d765adc1149c04cfac1344d398aee366e7d73d73fe7fb",
            changeDescription: "Remove docstrings",
            charEnd: 790,
            charStart: 468,
            diffSpans: [
                {
                    original: {
                        start: 0,
                        stop: 88,
                    },
                    updated: {
                        start: 0,
                        stop: 0,
                    },
                },
                {
                    original: {
                        start: 88,
                        stop: 121,
                    },
                    updated: {
                        start: 0,
                        stop: 33,
                    },
                },
                {
                    original: {
                        start: 121,
                        stop: 205,
                    },
                    updated: {
                        start: 33,
                        stop: 33,
                    },
                },
                {
                    original: {
                        start: 205,
                        stop: 238,
                    },
                    updated: {
                        start: 33,
                        stop: 66,
                    },
                },
                {
                    original: {
                        start: 238,
                        stop: 322,
                    },
                    updated: {
                        start: 66,
                        stop: 66,
                    },
                },
            ],
            editingScore: 0.93393415,
            editingScoreThreshold: 1,
            existingCode:
                '    """The offset in Unicode characters where the suffix region ends in `blob_name`."""\n\n    original_prefix_length: int\n    """The length of the prefix in the original request (in Unicode characters)."""\n\n    original_suffix_length: int\n    """The length of the suffix in the original request (in Unicode characters)."""\n',
            localizationScore: 0.9495588,
            path: "base/datasets/edit.py",
            suggestedCode: "\n    original_prefix_length: int\n\n    original_suffix_length: int\n",
            suggestionId: "0ca23f72-cc32-43e7-836e-fe4950966d5f",
        },
        unknownBlobNames: [],
    },
    suggestionOrder: 1,
};
