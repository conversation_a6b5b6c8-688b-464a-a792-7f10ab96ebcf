export const suggestion = {
    descriptionOutput: {
        logProbs: [-0.00289917, -0.029296875, -0.0011444092, -0.10058594],
        offsets: [0, 9, 13, 20],
        text: "Translate docstrings<|eot_id|>",
        tokenIds: [28573, 4733, 19107, 128009],
    },
    descriptionPrompt: {
        logProbs: [],
        offsets: [
            0, 17, 36, 42, 59, 61, 64, 68, 70, 80, 88, 91, 94, 99, 103, 105, 109, 114, 117, 120,
            128, 137, 149, 154, 158, 163, 170, 176, 180, 189, 192, 198, 209, 214, 219, 220, 224,
            232, 235, 240, 252, 255, 258, 263, 269, 280, 288, 299, 303, 311, 314, 318, 325, 328,
            332, 340, 343, 347, 352, 356, 357, 367, 386, 390, 407, 409, 413, 416, 420, 425, 432,
            442, 445, 452, 457, 462, 464, 466, 468, 471, 476, 478, 485, 490, 493, 494, 497, 502,
            504, 511, 516, 519, 520, 522, 524, 526, 527, 529, 531, 533, 534, 536, 540, 544, 546,
            550, 555, 560, 561, 563, 567, 572, 574, 579, 584, 586, 592, 597, 605, 607, 608, 611,
            615, 618, 625, 629, 638, 641, 643, 654, 662, 671, 674, 676, 683, 688, 693, 694, 697,
            701, 710, 711, 714, 723, 726, 730, 740, 743, 751, 756, 760, 765, 767, 770, 775, 778,
            783, 784, 789, 791, 795, 800, 805, 806, 810, 811, 812, 815, 819, 822, 827, 832, 835,
            839, 846, 851, 854, 858, 863, 868, 869, 872, 876, 878, 885, 889, 894, 897, 900, 904,
            906, 909, 914, 915, 919, 927, 932, 936, 940, 942, 949, 953, 958, 961, 964, 968, 970,
            973, 978, 979, 983, 991, 996, 998, 1002, 1009, 1015, 1016, 1020, 1021, 1022, 1025, 1029,
            1032, 1039, 1042, 1050, 1061, 1067, 1071, 1078, 1085, 1092, 1095, 1097, 1101, 1106,
            1107, 1112, 1113, 1116, 1120, 1122, 1126, 1128, 1130, 1137, 1140, 1151, 1159, 1165,
            1169, 1174, 1177, 1181, 1184, 1187, 1192, 1195, 1198, 1203, 1208, 1213, 1217, 1221,
            1223, 1227, 1229, 1231, 1238, 1241, 1252, 1260, 1266, 1270, 1275, 1278, 1282, 1285,
            1288, 1293, 1296, 1299, 1304, 1309, 1314, 1316, 1320, 1327, 1331, 1332, 1336, 1337,
            1338, 1341, 1345, 1348, 1355, 1358, 1366, 1377, 1383, 1387, 1394, 1401, 1406, 1409,
            1411, 1415, 1420, 1421, 1426, 1427, 1430, 1434, 1436, 1440, 1442, 1444, 1451, 1454,
            1465, 1473, 1479, 1486, 1487, 1490, 1494, 1497, 1500, 1504, 1507, 1510, 1515, 1520,
            1525, 1527, 1531, 1540, 1547, 1554, 1555, 1559, 1560, 1561, 1564, 1568, 1571, 1578,
            1581, 1585, 1592, 1595, 1599, 1608, 1616, 1618, 1620, 1628, 1639, 1641, 1645, 1646,
            1649, 1653, 1655, 1664, 1668, 1673, 1676, 1679, 1682, 1692, 1701, 1703, 1705, 1716,
            1724, 1726, 1730, 1732, 1736, 1745, 1752, 1759, 1760, 1764, 1765, 1766, 1769, 1773,
            1776, 1783, 1786, 1790, 1797, 1800, 1804, 1813, 1821, 1823, 1825, 1833, 1844, 1846,
            1850, 1851, 1854, 1858, 1860, 1869, 1873, 1877, 1880, 1883, 1886, 1896, 1905, 1907,
            1909, 1920, 1928, 1930, 1934, 1938, 1940, 1944, 1949, 1954, 1955, 1957, 1961, 1966,
            1968, 1973, 1978, 1980, 1986, 1991, 1998, 2001, 2003, 2005, 2011, 2013, 2019, 2023,
            2032, 2037, 2041, 2053, 2057, 2061, 2069, 2074, 2075, 2081, 2084, 2089, 2094, 2095,
            2097, 2103, 2105, 2110, 2113, 2117, 2124, 2129, 2133, 2142, 2146, 2150, 2152, 2160,
            2166, 2171, 2175, 2177, 2187, 2206, 2215, 2232,
        ],
        text: '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a developer working in my codebase.\nYour task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\nHere is the code change delimited by triple backticks:\n```\n--- base/datasets/edit.py\n+++ base/datasets/edit.py\n@@ -11,28 +11,28 @@\n \n \n @dataclass_json\n @dataclass(frozen=True)\n class EditPosition:\n-    """Represents the position of a completion request relative to a cached blob."""\n+    """Representa la posición de una solicitud de completación relativa a un blob en caché."""\n \n     blob_name: str\n-    """The blob name of the cached copy of the file."""\n+    """El nombre del blob de la copia en caché del archivo."""\n     """El nombre del blob de la copia en caché del archivo."""\n \n     prefix_begin: int\n-    """The offset in Unicode characters where the prefix region begins in `blob_name`."""\n+    """El desplazamiento en caracteres Unicode donde comienza la región de prefijo en blob_name."""\n     """El desplazamiento en caracteres Unicode donde comienza la región de prefijo en blob_name."""\n \n     suffix_end: int\n-    """The offset in Unicode characters where the suffix region ends in `blob_name`."""\n+    """El desplazamiento en caracteres Unicode donde termina la región de sufijo en blob_name."""\n \n     original_prefix_length: int\n-    """The length of the prefix in the original request (in Unicode characters)."""\n+    """La longitud del prefijo en la solicitud original (en caracteres Unicode)."""\n \n     original_suffix_length: int\n-    """The length of the suffix in the original request (in Unicode characters)."""\n+    """La longitud del sufijo en la solicitud original (en caracteres Unicode)."""\n \n \n @dataclass_json\n @dataclass(frozen=True)\n class EditRequest:\n\n```\nDirectly start the response with the description and say nothing else. Write no more than 10 words.\nFocus on the fields that are changing but don\'t include where they are.\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n',
        tokenIds: [
            128000, 128006, 9125, 128007, 271, 2675, 527, 264, 16131, 3318, 304, 856, 2082, 3231,
            627, 7927, 3465, 374, 311, 8819, 9959, 2038, 505, 279, 2082, 2349, 3770, 323, 7664, 433,
            1701, 48696, 19120, 1376, 13, 578, 7580, 315, 701, 4096, 374, 311, 1520, 1023, 13707,
            6288, 3619, 1401, 13878, 315, 279, 2349, 304, 279, 2317, 315, 279, 2082, 3231, 13,
            128009, 128006, 882, 128007, 271, 8586, 374, 279, 2082, 2349, 86428, 555, 24657, 1203,
            36178, 512, 14196, 4077, 4521, 2385, 3529, 77749, 22055, 7345, 198, 63830, 2385, 3529,
            77749, 22055, 7345, 198, 19741, 482, 806, 11, 1591, 489, 806, 11, 1591, 88220, 33006,
            571, 695, 1058, 9643, 198, 571, 695, 1058, 968, 43340, 3702, 340, 538, 8505, 3897, 512,
            12, 262, 4304, 18833, 9773, 279, 2361, 315, 264, 9954, 1715, 8844, 311, 264, 21224,
            24295, 14781, 10, 262, 4304, 66843, 64, 1208, 91331, 409, 5203, 96957, 409, 3543, 5840,
            1375, 28952, 264, 653, 24295, 665, 61387, 978, 14781, 720, 257, 24295, 1292, 25, 610,
            198, 12, 262, 4304, 791, 24295, 836, 315, 279, 21224, 3048, 315, 279, 1052, 14781, 10,
            262, 4304, 6719, 13026, 1624, 24295, 409, 1208, 6293, 689, 665, 61387, 978, 1624, 30818,
            14781, 257, 4304, 6719, 13026, 1624, 24295, 409, 1208, 6293, 689, 665, 61387, 978, 1624,
            30818, 14781, 720, 257, 9436, 24223, 25, 528, 198, 12, 262, 4304, 791, 4445, 304, 36997,
            5885, 1405, 279, 9436, 5654, 12302, 304, 1595, 36212, 1292, 63, 14781, 10, 262, 4304,
            6719, 951, 501, 1394, 23666, 665, 60355, 36997, 30140, 470, 66602, 1208, 1239, 3739,
            409, 19257, 48000, 665, 24295, 1292, 14781, 257, 4304, 6719, 951, 501, 1394, 23666, 665,
            60355, 36997, 30140, 470, 66602, 1208, 1239, 3739, 409, 19257, 48000, 665, 24295, 1292,
            14781, 720, 257, 21166, 6345, 25, 528, 198, 12, 262, 4304, 791, 4445, 304, 36997, 5885,
            1405, 279, 21166, 5654, 10548, 304, 1595, 36212, 1292, 63, 14781, 10, 262, 4304, 6719,
            951, 501, 1394, 23666, 665, 60355, 36997, 30140, 10415, 64, 1208, 1239, 3739, 409,
            56868, 48000, 665, 24295, 1292, 14781, 720, 257, 4113, 14301, 5228, 25, 528, 198, 12,
            262, 4304, 791, 3160, 315, 279, 9436, 304, 279, 4113, 1715, 320, 258, 36997, 5885, 570,
            7275, 10, 262, 4304, 8921, 49704, 1624, 19257, 48000, 665, 1208, 96957, 4113, 320, 268,
            60355, 36997, 570, 7275, 720, 257, 4113, 38251, 5228, 25, 528, 198, 12, 262, 4304, 791,
            3160, 315, 279, 21166, 304, 279, 4113, 1715, 320, 258, 36997, 5885, 570, 7275, 10, 262,
            4304, 8921, 49704, 1624, 56868, 48000, 665, 1208, 96957, 4113, 320, 268, 60355, 36997,
            570, 7275, 33006, 571, 695, 1058, 9643, 198, 571, 695, 1058, 968, 43340, 3702, 340, 538,
            8505, 1939, 1473, 14196, 4077, 16411, 398, 1212, 279, 2077, 449, 279, 4096, 323, 2019,
            4400, 775, 13, 9842, 912, 810, 1109, 220, 605, 4339, 627, 14139, 389, 279, 5151, 430,
            527, 10223, 719, 1541, 956, 2997, 1405, 814, 527, 627, 128009, 128006, 78191, 128007,
            271,
        ],
    },
    generationId: "7f62ce86-b629-40eb-ba03-1bfee58cecd6",
    result: {
        checkpointNotFound: false,
        suggestedEdit: {
            blobName: "9023460d52c7f5dc170377d36e2c2d07f62a380adcc51aacadb86b075cb0c261",
            changeDescription: "Translate docstrings",
            charEnd: 1099,
            charStart: 319,
            diffSpans: [
                {
                    original: {
                        start: 0,
                        stop: 7,
                    },
                    updated: {
                        start: 0,
                        stop: 7,
                    },
                },
                {
                    original: {
                        start: 7,
                        stop: 21,
                    },
                    updated: {
                        start: 7,
                        stop: 20,
                    },
                },
                {
                    original: {
                        start: 21,
                        stop: 22,
                    },
                    updated: {
                        start: 20,
                        stop: 21,
                    },
                },
                {
                    original: {
                        start: 22,
                        stop: 35,
                    },
                    updated: {
                        start: 21,
                        stop: 49,
                    },
                },
                {
                    original: {
                        start: 35,
                        stop: 36,
                    },
                    updated: {
                        start: 49,
                        stop: 50,
                    },
                },
                {
                    original: {
                        start: 36,
                        stop: 54,
                    },
                    updated: {
                        start: 50,
                        stop: 62,
                    },
                },
                {
                    original: {
                        start: 54,
                        stop: 55,
                    },
                    updated: {
                        start: 62,
                        stop: 63,
                    },
                },
                {
                    original: {
                        start: 55,
                        stop: 68,
                    },
                    updated: {
                        start: 63,
                        stop: 84,
                    },
                },
                {
                    original: {
                        start: 68,
                        stop: 69,
                    },
                    updated: {
                        start: 84,
                        stop: 85,
                    },
                },
                {
                    original: {
                        start: 69,
                        stop: 80,
                    },
                    updated: {
                        start: 85,
                        stop: 90,
                    },
                },
                {
                    original: {
                        start: 80,
                        stop: 105,
                    },
                    updated: {
                        start: 90,
                        stop: 115,
                    },
                },
                {
                    original: {
                        start: 105,
                        stop: 161,
                    },
                    updated: {
                        start: 115,
                        stop: 115,
                    },
                },
                {
                    original: {
                        start: 161,
                        stop: 224,
                    },
                    updated: {
                        start: 115,
                        stop: 178,
                    },
                },
                {
                    original: {
                        start: 224,
                        stop: 224,
                    },
                    updated: {
                        start: 178,
                        stop: 241,
                    },
                },
                {
                    original: {
                        start: 224,
                        stop: 247,
                    },
                    updated: {
                        start: 241,
                        stop: 264,
                    },
                },
                {
                    original: {
                        start: 247,
                        stop: 337,
                    },
                    updated: {
                        start: 264,
                        stop: 264,
                    },
                },
                {
                    original: {
                        start: 337,
                        stop: 437,
                    },
                    updated: {
                        start: 264,
                        stop: 364,
                    },
                },
                {
                    original: {
                        start: 437,
                        stop: 437,
                    },
                    updated: {
                        start: 364,
                        stop: 464,
                    },
                },
                {
                    original: {
                        start: 437,
                        stop: 465,
                    },
                    updated: {
                        start: 464,
                        stop: 492,
                    },
                },
                {
                    original: {
                        start: 465,
                        stop: 478,
                    },
                    updated: {
                        start: 492,
                        stop: 523,
                    },
                },
                {
                    original: {
                        start: 478,
                        stop: 487,
                    },
                    updated: {
                        start: 523,
                        stop: 532,
                    },
                },
                {
                    original: {
                        start: 487,
                        stop: 496,
                    },
                    updated: {
                        start: 532,
                        stop: 541,
                    },
                },
                {
                    original: {
                        start: 496,
                        stop: 496,
                    },
                    updated: {
                        start: 541,
                        stop: 541,
                    },
                },
                {
                    original: {
                        start: 496,
                        stop: 514,
                    },
                    updated: {
                        start: 541,
                        stop: 548,
                    },
                },
                {
                    original: {
                        start: 514,
                        stop: 515,
                    },
                    updated: {
                        start: 548,
                        stop: 549,
                    },
                },
                {
                    original: {
                        start: 515,
                        stop: 521,
                    },
                    updated: {
                        start: 549,
                        stop: 565,
                    },
                },
                {
                    original: {
                        start: 521,
                        stop: 522,
                    },
                    updated: {
                        start: 565,
                        stop: 566,
                    },
                },
                {
                    original: {
                        start: 522,
                        stop: 531,
                    },
                    updated: {
                        start: 566,
                        stop: 569,
                    },
                },
                {
                    original: {
                        start: 531,
                        stop: 540,
                    },
                    updated: {
                        start: 569,
                        stop: 578,
                    },
                },
                {
                    original: {
                        start: 540,
                        stop: 541,
                    },
                    updated: {
                        start: 578,
                        stop: 578,
                    },
                },
                {
                    original: {
                        start: 541,
                        stop: 586,
                    },
                    updated: {
                        start: 578,
                        stop: 623,
                    },
                },
                {
                    original: {
                        start: 586,
                        stop: 603,
                    },
                    updated: {
                        start: 623,
                        stop: 638,
                    },
                },
                {
                    original: {
                        start: 603,
                        stop: 604,
                    },
                    updated: {
                        start: 638,
                        stop: 639,
                    },
                },
                {
                    original: {
                        start: 604,
                        stop: 617,
                    },
                    updated: {
                        start: 639,
                        stop: 662,
                    },
                },
                {
                    original: {
                        start: 617,
                        stop: 627,
                    },
                    updated: {
                        start: 662,
                        stop: 672,
                    },
                },
                {
                    original: {
                        start: 627,
                        stop: 638,
                    },
                    updated: {
                        start: 672,
                        stop: 686,
                    },
                },
                {
                    original: {
                        start: 638,
                        stop: 646,
                    },
                    updated: {
                        start: 686,
                        stop: 694,
                    },
                },
                {
                    original: {
                        start: 646,
                        stop: 657,
                    },
                    updated: {
                        start: 694,
                        stop: 694,
                    },
                },
                {
                    original: {
                        start: 657,
                        stop: 703,
                    },
                    updated: {
                        start: 694,
                        stop: 740,
                    },
                },
                {
                    original: {
                        start: 703,
                        stop: 720,
                    },
                    updated: {
                        start: 740,
                        stop: 755,
                    },
                },
                {
                    original: {
                        start: 720,
                        stop: 721,
                    },
                    updated: {
                        start: 755,
                        stop: 756,
                    },
                },
                {
                    original: {
                        start: 721,
                        stop: 734,
                    },
                    updated: {
                        start: 756,
                        stop: 778,
                    },
                },
                {
                    original: {
                        start: 734,
                        stop: 744,
                    },
                    updated: {
                        start: 778,
                        stop: 788,
                    },
                },
                {
                    original: {
                        start: 744,
                        stop: 755,
                    },
                    updated: {
                        start: 788,
                        stop: 802,
                    },
                },
                {
                    original: {
                        start: 755,
                        stop: 763,
                    },
                    updated: {
                        start: 802,
                        stop: 810,
                    },
                },
                {
                    original: {
                        start: 763,
                        stop: 774,
                    },
                    updated: {
                        start: 810,
                        stop: 810,
                    },
                },
                {
                    original: {
                        start: 774,
                        stop: 780,
                    },
                    updated: {
                        start: 810,
                        stop: 816,
                    },
                },
            ],
            editingScore: 0.0011715204,
            editingScoreThreshold: 1,
            existingCode:
                '    """Represents the position of a completion request relative to a cached blob."""\n\n    blob_name: str\n    """The blob name of the cached copy of the file."""\n    """El nombre del blob de la copia en caché del archivo."""\n\n    prefix_begin: int\n    """The offset in Unicode characters where the prefix region begins in `blob_name`."""\n    """El desplazamiento en caracteres Unicode donde comienza la región de prefijo en blob_name."""\n\n    suffix_end: int\n    """The offset in Unicode characters where the suffix region ends in `blob_name`."""\n\n    original_prefix_length: int\n    """The length of the prefix in the original request (in Unicode characters)."""\n\n    original_suffix_length: int\n    """The length of the suffix in the original request (in Unicode characters)."""\n',
            localizationScore: 0.9870234,
            path: "base/datasets/edit.py",
            suggestedCode:
                '    """Representa la posición de una solicitud de completación relativa a un blob en caché."""\n\n    blob_name: str\n    """El nombre del blob de la copia en caché del archivo."""\n    """El nombre del blob de la copia en caché del archivo."""\n\n    prefix_begin: int\n    """El desplazamiento en caracteres Unicode donde comienza la región de prefijo en blob_name."""\n    """El desplazamiento en caracteres Unicode donde comienza la región de prefijo en blob_name."""\n\n    suffix_end: int\n    """El desplazamiento en caracteres Unicode donde termina la región de sufijo en blob_name."""\n\n    original_prefix_length: int\n    """La longitud del prefijo en la solicitud original (en caracteres Unicode)."""\n\n    original_suffix_length: int\n    """La longitud del sufijo en la solicitud original (en caracteres Unicode)."""\n',
            suggestionId: "8e9f3ee1-8ffd-4c58-a812-aeaa2382d73e",
        },
        unknownBlobNames: [],
    },
    suggestionOrder: 1,
};
