import fs from "fs";
import os from "os";
import path from "path";

import { QualifiedPathName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import { TrackedDisposable } from "../../__mocks__/vscode-mocks";
import { renderFullDiff } from "../../next-edit/diff-renderer";
import {
    DiffSpan,
    NextEditMode,
    NextEditResult,
    NextEditScope,
} from "../../next-edit/next-edit-types";
import { EditSuggestion } from "../../next-edit/suggestion-manager";
import { LineRange } from "../../utils/ranges";
import { suggestion as complexSuggestionRaw } from "./sample-suggestion";
import { suggestion as infLoopSuggestionRaw } from "./sample-suggestion-inf-loop";
import { suggestion as infLoopSuggestionRaw2 } from "./sample-suggestion-inf-loop-2";
import { suggestion as infLoopSuggestionRaw3 } from "./sample-suggestion-inf-loop-3";
import { suggestion as complexSuggestionRawLinesDeleted } from "./sample-suggestion-lines-deleted";

jest.mock("os", () => ({
    platform: jest.fn(() => "darwin"),
}));

// Testing note: to view any of the diffs as rendered html you can open up about:blank in Chrome
// and do `document.body.innerHTML="<div/>"; document.body.firstChild.innerHTML="<html goes here>"

describe("diff-renderer", () => {
    const sampleDiff = fs.readFileSync(path.join(__dirname, "sample-diff.html"), "utf-8");
    const sampleDiffTrimmed = fs.readFileSync(
        path.join(__dirname, "sample-diff-trimmed.html"),
        "utf-8"
    );
    const mockColors = {
        originalLineColor: "#ff0000",
        originalTextColor: "#aa0000",
        updatedLineColor: "#00ff00",
        updatedTextColor: "#00aa00",
        lineNumberColor: "#0000ff",
    };

    const singleLineInsertionSuggestion = new EditSuggestion(
        "suggestion-id",
        NextEditMode.Background,
        NextEditScope.File,
        {
            suggestionId: "suggestion-id",
            path: "path/to/file.py",
            blobName: "",
            charStart: 0,
            charEnd: 0,
            existingCode: "",
            suggestedCode: "line 0\n",
            truncationChar: undefined,
            changeDescription: "",
            diffSpans: [{ original: { start: 0, stop: 0 }, updated: { start: 0, stop: 7 } }],
            editingScore: 0,
            localizationScore: 0,
            editingScoreThreshold: 1.0,
        },
        new QualifiedPathName("", "path/to/file.py"),
        new LineRange(1, 1)
    );

    const singleLongLineInsertionSuggestion = new EditSuggestion(
        "suggestion-id",
        NextEditMode.Background,
        NextEditScope.File,
        {
            suggestionId: "suggestion-id",
            path: "path/to/file.py",
            blobName: "",
            charStart: 0,
            charEnd: 0,
            existingCode: "",
            suggestedCode: "this is a long line more than 50 characters hello hello hello line 0\n",
            truncationChar: undefined,
            changeDescription: "",
            diffSpans: [{ original: { start: 0, stop: 0 }, updated: { start: 0, stop: 69 } }],
            editingScore: 0,
            localizationScore: 0,
            editingScoreThreshold: 1.0,
        },
        new QualifiedPathName("", "path/to/file.py"),
        new LineRange(1, 1)
    );

    const emptyLineReplacementSuggestion = new EditSuggestion(
        "suggestion-id",
        NextEditMode.Background,
        NextEditScope.File,
        {
            suggestionId: "suggestion-id",
            path: "path/to/file.py",
            blobName: "",
            charStart: 0,
            charEnd: 1,
            existingCode: "\n",
            suggestedCode: "  hi\n",
            truncationChar: undefined,
            changeDescription: "",
            diffSpans: [{ original: { start: 0, stop: 1 }, updated: { start: 0, stop: 5 } }],
            editingScore: 0,
            localizationScore: 0,
            editingScoreThreshold: 1.0,
        },
        new QualifiedPathName("", "path/to/file.py"),
        new LineRange(1, 1)
    );
    const beforeContext = "Before context\n";
    const afterContext = "After context\n";

    afterEach(() => {
        TrackedDisposable.assertDisposed();
    });

    describe("complex diff", () => {
        const suggestionRaw: NextEditResult = complexSuggestionRaw.result.suggestedEdit;
        const suggestion = new EditSuggestion(
            suggestionRaw.suggestionId,
            NextEditMode.Background,
            NextEditScope.File,
            suggestionRaw,
            new QualifiedPathName("", "path/to/file.py"),
            new LineRange(1, suggestionRaw.existingCode.split("\n").length + 1)
        );
        const beforeContext = "    Before context\n";
        const afterContext = "    After context\n";

        test("renderFullDiff should produce correct HTML output for a complex diff with before and after context", () => {
            const { result } = renderFullDiff(
                suggestion,
                beforeContext,
                afterContext,
                20,
                mockColors
            );
            // Check for key elements in the rendered HTML
            expect(result).toContain("<pre>");
            expect(result).toContain("Before context");
            expect(result).toContain("After context");
            expect(result).toContain(mockColors.originalLineColor);
            expect(result).toContain(mockColors.updatedLineColor);
            expect(result).toContain(mockColors.lineNumberColor);
            expect(result).toEqual(sampleDiff);
        });

        test("renderFullDiff should produce correct HTML while trimming leading whitespace", () => {
            const { result } = renderFullDiff(
                suggestion,
                beforeContext,
                afterContext,
                20,
                mockColors,
                true
            );
            // Check for key elements in the rendered HTML
            expect(result).toContain("<pre>");
            expect(result).toContain("Before context");
            expect(result).toContain("After context");
            expect(result).toContain(mockColors.originalLineColor);
            expect(result).toContain(mockColors.updatedLineColor);
            expect(result).toContain(mockColors.lineNumberColor);
            expect(result).toEqual(sampleDiffTrimmed);
        });
    });

    describe("single line insertion", () => {
        test("renderFullDiff should produce correct HTML output for a single line insertion with before and after context", () => {
            const { result } = renderFullDiff(
                singleLineInsertionSuggestion,
                beforeContext,
                afterContext,
                20,
                mockColors
            );
            // Check for key elements in the rendered HTML
            expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> Before context       </span>
<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">line 0</span><span style="background-color:#00ff00;">               </span>
<span style="color:#0000ff;"> 2 3<span class="codicon codicon-blank"></span> After context        </span></span></pre>
`);
        });

        test("renderFullDiff should produce correct HTML output for a single line insertion with no before context", () => {
            const { result } = renderFullDiff(
                singleLineInsertionSuggestion,
                undefined,
                afterContext,
                20,
                mockColors
            );
            // Check for key elements in the rendered HTML
            expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">line 0</span><span style="background-color:#00ff00;">               </span>
<span style="color:#0000ff;"> 2 3<span class="codicon codicon-blank"></span> After context        </span></span></pre>
`);
        });

        test("renderFullDiff should produce correct HTML output for a single line insertion with no after context", () => {
            const { result } = renderFullDiff(
                singleLineInsertionSuggestion,
                beforeContext,
                undefined,
                20,
                mockColors
            );
            // Check for key elements in the rendered HTML
            expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> Before context       </span>
<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">line 0</span><span style="background-color:#00ff00;">               </span></span></pre>
`);
        });
    });

    test("renderFullDiff should truncate before and after context text to be no longer than the longest changed lines", () => {
        const { result } = renderFullDiff(
            singleLongLineInsertionSuggestion,
            "this is a long Before Context more than 50 characters hello hello hello line 0\n",
            "this is a long After Context more than 50 characters hello hello hello line 0\n",
            20,
            mockColors
        );
        // Check for key elements in the rendered HTML
        expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> this is a long Before Context more than 50 characters hello hello... </span>
<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">this is a long line more than 50 characters hello hello hello line 0</span><span style="background-color:#00ff00;"> </span>
<span style="color:#0000ff;"> 2 3<span class="codicon codicon-blank"></span> this is a long After Context more than 50 characters hello hello ... </span></span></pre>
`);
    });

    test("renderFullDiff should not truncate the before and after context if the min width allows it", () => {
        const { result } = renderFullDiff(
            singleLongLineInsertionSuggestion,
            "this is a long Before Context more than 50 characters hello hello hello line 0\n",
            "this is a long After Context more than 50 characters hello hello hello line 0\n",
            100,
            mockColors
        );
        // Check for key elements in the rendered HTML
        expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> this is a long Before Context more than 50 characters hello hello hello line 0                       </span>
<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">this is a long line more than 50 characters hello hello hello line 0</span><span style="background-color:#00ff00;">                                 </span>
<span style="color:#0000ff;"> 2 3<span class="codicon codicon-blank"></span> this is a long After Context more than 50 characters hello hello hello line 0                        </span></span></pre>
`);
    });

    test("renderFullDiff should truncate before and after context text to be no longer than the min width when the change itself is short", () => {
        const { result } = renderFullDiff(
            singleLineInsertionSuggestion,
            "this is a long Before Context more than 50 characters hello hello hello line 0\n",
            "this is a long After Context more than 50 characters hello hello hello line 0\n",
            20,
            mockColors
        );
        // Check for key elements in the rendered HTML
        expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> this is a long Be... </span>
<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">line 0</span><span style="background-color:#00ff00;">               </span>
<span style="color:#0000ff;"> 2 3<span class="codicon codicon-blank"></span> this is a long Af... </span></span></pre>
`);
    });

    test("renderFullDiff should produce correct HTML output for a single line deletion", () => {
        const suggestion = new EditSuggestion(
            "suggestion-id",
            NextEditMode.Background,
            NextEditScope.File,
            {
                suggestionId: "suggestion-id",
                path: "path/to/file.py",
                blobName: "",
                charStart: 0,
                charEnd: 0,
                existingCode: "line 0\n",
                suggestedCode: "",
                truncationChar: undefined,
                changeDescription: "",
                diffSpans: [{ original: { start: 0, stop: 7 }, updated: { start: 0, stop: 0 } }],
                editingScore: 0,
                localizationScore: 0,
                editingScoreThreshold: 1.0,
            },
            new QualifiedPathName("", "path/to/file.py"),
            new LineRange(1, 2)
        );
        const beforeContext = "Before context\n";
        const afterContext = "After context\n";

        const { result } = renderFullDiff(suggestion, beforeContext, afterContext, 20, mockColors);
        // Check for key elements in the rendered HTML
        expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> Before context       </span>
<span style="color:#0000ff;background-color:#ff0000;"> 2  </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#aa0000;">line 0</span><span style="background-color:#ff0000;">               </span>
<span style="color:#0000ff;"> 3 2<span class="codicon codicon-blank"></span> After context        </span></span></pre>
`);
    });

    test("renderFullDiff should correctly format line numbers for complex diff with lines deleted", () => {
        // There was an issue where the line number width was not being correctly computed in this case
        // and the line numbers ran together.
        const suggestionRaw: NextEditResult = complexSuggestionRawLinesDeleted.result.suggestedEdit;
        const suggestion = new EditSuggestion(
            suggestionRaw.suggestionId,
            NextEditMode.Background,
            NextEditScope.File,
            suggestionRaw,
            new QualifiedPathName("", "path/to/file.py"),
            new LineRange(22, 22 + suggestionRaw.existingCode.split("\n").length + 1)
        );

        const { result } = renderFullDiff(suggestion, beforeContext, afterContext, 20, mockColors);
        // Check for key elements in the rendered HTML
        expect(result).toContain("<pre>");
        expect(result).toContain("Before context");
        expect(result).toContain("After context");
        expect(result).toContain(mockColors.originalLineColor);
        expect(result).toContain(mockColors.lineNumberColor);
        expect(result).not.toContain(">2725<"); // line numbers run together
        expect(result).toContain("> 27 25<"); // line numbers are separated by a space
    });

    test("renderFullDiff does not get stuck in infinite loop", () => {
        // There was an issue where the line number width was not being correctly computed in this case
        // and the line numbers ran together.
        const suggestionRaw: NextEditResult = infLoopSuggestionRaw.result.suggestedEdit;
        const suggestion = new EditSuggestion(
            suggestionRaw.suggestionId,
            NextEditMode.Background,
            NextEditScope.File,
            suggestionRaw,
            new QualifiedPathName("", "path/to/file.py"),
            new LineRange(22, 22 + suggestionRaw.existingCode.split("\n").length + 1)
        );

        const { result } = renderFullDiff(suggestion, beforeContext, afterContext, 20, mockColors);
        // Check for key elements in the rendered HTML
        expect(result).toContain("<pre>");
        expect(result).toContain("Before context");
        expect(result).toContain("After context");
    });

    test("renderFullDiff does not get stuck in infinite loop (2)", () => {
        const suggestionRaw: NextEditResult = infLoopSuggestionRaw2.result.suggestedEdit;
        const suggestion = new EditSuggestion(
            suggestionRaw.suggestionId,
            NextEditMode.Background,
            NextEditScope.File,
            suggestionRaw,
            new QualifiedPathName("", "path/to/file.py"),
            new LineRange(22, 22 + suggestionRaw.existingCode.split("\n").length + 1)
        );

        const { result } = renderFullDiff(suggestion, beforeContext, afterContext, 20, mockColors);
        // Check for key elements in the rendered HTML
        expect(result).toContain("<pre>");
        expect(result).toContain("Before context");
        expect(result).toContain("After context");
    });

    test("renderFullDiff does not get stuck in infinite loop (3)", () => {
        const suggestionRaw: NextEditResult = infLoopSuggestionRaw3.result.suggestedEdit;
        const suggestion = new EditSuggestion(
            suggestionRaw.suggestionId,
            NextEditMode.Background,
            NextEditScope.File,
            suggestionRaw,
            new QualifiedPathName("", "path/to/file.py"),
            new LineRange(22, 22 + suggestionRaw.existingCode.split("\n").length + 1)
        );

        const { result } = renderFullDiff(suggestion, beforeContext, afterContext, 20, mockColors);
        // Check for key elements in the rendered HTML
        expect(result).toContain("<pre>");
        expect(result).toContain("Before context");
        expect(result).toContain("After context");
    });

    test("renderFullDiff should use bold font on windows", () => {
        (os.platform as jest.Mock).mockReturnValueOnce("win32");

        const { result } = renderFullDiff(
            singleLineInsertionSuggestion,
            undefined,
            undefined,
            20,
            mockColors
        );
        // Check for key elements in the rendered HTML
        expect(result.trim().startsWith("<strong>")).toBe(true);
        expect(result.trim().endsWith("</strong>")).toBe(true);
    });

    test("renderFullDiff correctly handle deleting empty lines with trimming", () => {
        const { result } = renderFullDiff(
            emptyLineReplacementSuggestion,
            "  a\n",
            "  b\n",
            20,
            mockColors,
            true
        );
        // Check for key elements in the rendered HTML
        expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> a                    </span>\n<span style="color:#0000ff;background-color:#ff0000;"> 2  </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#aa0000;"></span><span style="background-color:#ff0000;">                     </span>\n<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00aa00;">hi</span><span style="background-color:#00ff00;">                   </span>\n<span style="color:#0000ff;"> 2 2<span class="codicon codicon-blank"></span> b                    </span></span></pre>
`);
    });

    describe("trim whitespace", () => {
        test("renderFullDiff should correctly trim leading whitespace", () => {
            const suggestion = makeMockSuggestion({
                existingCode: "    line 0\n",
                suggestedCode: "    line 0 with some more stuff\n",
                diffSpans: [
                    { original: { start: 0, stop: 10 }, updated: { start: 0, stop: 10 } },
                    { original: { start: 10, stop: 10 }, updated: { start: 10, stop: 30 } },
                    { original: { start: 10, stop: 11 }, updated: { start: 30, stop: 31 } },
                ],
                lineRange: new LineRange(1, 2),
            });

            const { result } = renderFullDiff(
                suggestion,
                "    Before context\n",
                "    After context\n",
                20,
                mockColors,
                true
            );
            expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> Before context              </span>
<span style="color:#0000ff;background-color:#ff0000;"> 2  </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;">line 0</span><span style="background-color:#aa0000;"></span><span style="background-color:#aa0000;"></span><span style="background-color:#ff0000;">                      </span>
<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;">line 0</span><span style="background-color:#00aa00;"> with some more stuf</span><span style="background-color:#00aa00;">f</span><span style="background-color:#00ff00;"> </span>
<span style="color:#0000ff;"> 3 3<span class="codicon codicon-blank"></span> After context               </span></span></pre>
`);
        });
        test("renderFullDiff should correctly trim leading whitespace, but not trim other whitespace", () => {
            const suggestion = makeMockSuggestion({
                existingCode: "    line     0\n",
                suggestedCode: "    line     0 with some more stuff\n",
                diffSpans: [
                    { original: { start: 0, stop: 14 }, updated: { start: 0, stop: 14 } },
                    { original: { start: 14, stop: 14 }, updated: { start: 14, stop: 34 } },
                    { original: { start: 14, stop: 15 }, updated: { start: 34, stop: 35 } },
                ],
                lineRange: new LineRange(1, 2),
            });

            const { result } = renderFullDiff(
                suggestion,
                "    Before context\n",
                "    After context\n",
                20,
                mockColors,
                true
            );
            expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> Before context                  </span>
<span style="color:#0000ff;background-color:#ff0000;"> 2  </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;">line     0</span><span style="background-color:#aa0000;"></span><span style="background-color:#aa0000;"></span><span style="background-color:#ff0000;">                      </span>
<span style="color:#0000ff;background-color:#00ff00;">   2</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;">line     0</span><span style="background-color:#00aa00;"> with some more stuf</span><span style="background-color:#00aa00;">f</span><span style="background-color:#00ff00;"> </span>
<span style="color:#0000ff;"> 3 3<span class="codicon codicon-blank"></span> After context                   </span></span></pre>
`);
        });

        test("renderFullDiff should trim the whitespace while maintaining indentation differences", () => {
            const suggestion = makeMockSuggestion({
                existingCode: `
        line 0
          line 1
            line 2
`.slice(1),
                suggestedCode: `
        line 0
        line A
            line 2
`.slice(1),
                diffSpans: [
                    { original: { start: 0, stop: 23 }, updated: { start: 0, stop: 23 } },
                    { original: { start: 23, stop: 25 }, updated: { start: 23, stop: 23 } },
                    { original: { start: 25, stop: 30 }, updated: { start: 23, stop: 28 } },
                    { original: { start: 30, stop: 31 }, updated: { start: 28, stop: 29 } },
                    { original: { start: 31, stop: 51 }, updated: { start: 29, stop: 49 } },
                ],
                lineRange: new LineRange(1, 2),
            });

            const { result } = renderFullDiff(
                suggestion,
                "        Before context\n",
                "        After context\n",
                20,
                mockColors,
                true
            );

            expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> Before context       </span>
<span style="color:#0000ff;background-color:#00000000;"> 2 2</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>line 0<span style="background-color:#00000000;">               </span>
<span style="color:#0000ff;background-color:#ff0000;"> 3  </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;"></span><span style="background-color:#aa0000;">  </span><span style="background-color:#ff0000;">line </span><span style="background-color:#aa0000;">1</span><span style="background-color:#ff0000;"></span><span style="background-color:#ff0000;">             </span>
<span style="color:#0000ff;background-color:#00ff00;">   3</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;"></span><span style="background-color:#00aa00;"></span><span style="background-color:#00ff00;">line </span><span style="background-color:#00aa00;">A</span><span style="background-color:#00ff00;"></span><span style="background-color:#00ff00;">               </span>
<span style="color:#0000ff;background-color:#00000000;"> 4 4</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>    line 2<span style="background-color:#00000000;">           </span>
<span style="color:#0000ff;"> 3 3<span class="codicon codicon-blank"></span> After context        </span></span></pre>
`);
        });

        test("renderFullDiff should handle a mix of tabs and spaces when trimming", () => {
            const suggestion = makeMockSuggestion({
                existingCode: `
    \tline 0
    \t  line 1
    \t    line 2
`.slice(1),
                suggestedCode: `
    \tline 0
    \tline A
    \t    line 2
`.slice(1),
                diffSpans: [
                    { original: { start: 0, stop: 17 }, updated: { start: 0, stop: 17 } },
                    { original: { start: 17, stop: 19 }, updated: { start: 17, stop: 17 } },
                    { original: { start: 19, stop: 24 }, updated: { start: 17, stop: 22 } },
                    { original: { start: 24, stop: 25 }, updated: { start: 22, stop: 23 } },
                    { original: { start: 25, stop: 42 }, updated: { start: 23, stop: 40 } },
                ],
                lineRange: new LineRange(1, 2),
            });

            const { result } = renderFullDiff(
                suggestion,
                "    \tBefore context\n",
                "    \tAfter context\n",
                20,
                mockColors,
                true
            );

            expect(result).toEqual(`
<pre><span style="background-color:var(--vscode-editor-background);"><span style="color:#0000ff;"> 1 1<span class="codicon codicon-blank"></span> Before context       </span>
<span style="color:#0000ff;background-color:#00000000;"> 2 2</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>line 0<span style="background-color:#00000000;">               </span>
<span style="color:#0000ff;background-color:#ff0000;"> 3  </span><span style="background-color:#ff0000;"><span class="codicon codicon-diff-remove"></span> </span><span style="background-color:#ff0000;"></span><span style="background-color:#aa0000;">  </span><span style="background-color:#ff0000;">line </span><span style="background-color:#aa0000;">1</span><span style="background-color:#ff0000;"></span><span style="background-color:#ff0000;">             </span>
<span style="color:#0000ff;background-color:#00ff00;">   3</span><span style="background-color:#00ff00;"><span class="codicon codicon-diff-insert"></span> </span><span style="background-color:#00ff00;"></span><span style="background-color:#00aa00;"></span><span style="background-color:#00ff00;">line </span><span style="background-color:#00aa00;">A</span><span style="background-color:#00ff00;"></span><span style="background-color:#00ff00;">               </span>
<span style="color:#0000ff;background-color:#00000000;"> 4 4</span><span style="background-color:#00000000;"><span class="codicon codicon-blank"></span> </span>    line 2<span style="background-color:#00000000;">           </span>
<span style="color:#0000ff;"> 3 3<span class="codicon codicon-blank"></span> After context        </span></span></pre>
`);
        });
    });
});

function makeMockSuggestion({
    existingCode,
    suggestedCode,
    diffSpans,
    lineRange,
}: {
    existingCode?: string;
    suggestedCode?: string;
    diffSpans?: DiffSpan[];
    lineRange?: LineRange;
}): EditSuggestion {
    return new EditSuggestion(
        "suggestion-id",
        NextEditMode.Background,
        NextEditScope.File,
        {
            suggestionId: "suggestion-id",
            path: "path/to/file.py",
            blobName: "",
            charStart: !diffSpans ? 0 : diffSpans[0].original.start,
            charEnd: !diffSpans ? 0 : diffSpans[diffSpans.length - 1].original.stop,
            existingCode: existingCode ?? "",
            suggestedCode: suggestedCode ?? "",
            truncationChar: undefined,
            changeDescription: "",
            diffSpans: diffSpans ?? [],
            editingScore: 0,
            localizationScore: 0,
            editingScoreThreshold: 1.0,
        },
        new QualifiedPathName("", "path/to/file.py"),
        lineRange ?? new LineRange(1, 1)
    );
}
