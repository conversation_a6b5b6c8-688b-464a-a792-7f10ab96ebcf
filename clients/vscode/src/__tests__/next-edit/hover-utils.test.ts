import { breakMarkdownCodicons, escapeAndBreakMarkdownCodicons } from "../../next-edit/hover-utils";

describe("hover-utils", () => {
    describe("breakMarkdownCodicons", () => {
        test("should not modify strings without codicons", () => {
            expect(breakMarkdownCodicons("normal text")).toBe("normal text");
            expect(breakMarkdownCodicons("text with $(but not a codicon")).toBe(
                "text with $(but not a codicon"
            );
        });

        test("should break up codicon syntax", () => {
            expect(breakMarkdownCodicons("$(gear)")).toBe("&dollar;(gear)");
            expect(breakMarkdownCodicons("$(file)")).toBe("&dollar;(file)");
        });

        test("should handle multiple codicons", () => {
            expect(breakMarkdownCodicons("$(gear) and $(file)")).toBe(
                "&dollar;(gear) and &dollar;(file)"
            );
        });

        test("should handle codicons with hyphens", () => {
            expect(breakMarkdownCodicons("$(arrow-right)")).toBe("&dollar;(arrow-right)");
        });

        test("should be case insensitive", () => {
            expect(breakMarkdownCodicons("$(GEAR)")).toBe("&dollar;(GEAR)");
            expect(breakMarkdownCodicons("$(GeAr)")).toBe("&dollar;(GeAr)");
        });
    });

    describe("escapeAndBreakMarkdownCodicons", () => {
        test("should escape HTML and break codicons", () => {
            expect(escapeAndBreakMarkdownCodicons("<div>$(gear)</div>")).toBe(
                "&lt;div&gt;&dollar;(gear)&lt;/div&gt;"
            );
        });

        test("should handle mixed content", () => {
            expect(escapeAndBreakMarkdownCodicons("<span>$(file) & $(folder)</span>")).toBe(
                "&lt;span&gt;&dollar;(file) &amp; &dollar;(folder)&lt;/span&gt;"
            );
        });
    });
});
