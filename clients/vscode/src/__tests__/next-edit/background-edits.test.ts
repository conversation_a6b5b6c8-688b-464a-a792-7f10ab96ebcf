import { B<PERSON>bNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";

import { QualifiedPathName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { generateMockWorkspaceConfig } from "../../__mocks__/mock-augment-config";
import {
    debug,
    DebugSession,
    ExtensionContext,
    getWorkspaceFolder,
    mockWorkspaceConfigChange,
    MutableTextDocument,
    publishWorkspaceFoldersChange,
    Selection,
    TextDocument,
    TextDocumentChangeEvent,
    TextEditor,
    TextEditorSelectionChangeKind,
    TrackedDisposable,
    Uri,
    window,
    workspace,
} from "../../__mocks__/vscode-mocks";
import { NextEditGenerationResult } from "../../augment-api";
import { DiagnosticsManager } from "../../diagnostics";
import { FeatureFlagManager } from "../../feature-flags";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";
import { NextEditSessionEventReporter } from "../../metrics/next-edit-session-event-reporter";
import { BackgroundNextEdits } from "../../next-edit/background-edits";
import { CompletionVisibilityWatcher } from "../../next-edit/completion-visibility-watcher";
import { EditorNextEdits } from "../../next-edit/editor-edits";
import { FileEditEvent } from "../../next-edit/file-edit-events";
import { NextEditConfigManager } from "../../next-edit/next-edit-config-manager";
import { NextEditMode, NextEditResultInfo, NextEditScope } from "../../next-edit/next-edit-types";
import { NextEditRequestManager } from "../../next-edit/request-manager-impl";
import { SuggestionManager } from "../../next-edit/suggestion-manager";
import { SuggestionManagerImpl } from "../../next-edit/suggestion-manager-impl";
import { StateController } from "../../statusbar/state-controller";
import { StatusBarManager } from "../../statusbar/status-bar-manager";
import { AugmentGlobalState } from "../../utils/context";
import { EphemeralObservable } from "../../utils/ephemeral-flag";
import * as fsUtils from "../../utils/fs-utils";
import { KeybindingWatcher } from "../../utils/keybindings";
import { LineRange } from "../../utils/ranges";
import { RecentItems } from "../../utils/recent-items";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitWorkspaceState,
    createSourceFolderFiles,
    defaultWorkspaceFolder,
    defaultWorkspaceState,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";
import { createSuggestion } from "./test-utils";

const _DEBOUNCE_MS = 5;

class BackgroundEditTestKit extends WorkspaceManagerTestKit {
    static readonly workspaceFolder = defaultWorkspaceFolder;
    static readonly folderState = defaultWorkspaceState;

    public readonly ctx = new ExtensionContext();
    public readonly apiServer = new MockAPIServer();
    public augmentGlobalState: AugmentGlobalState;
    public mockKeybindingWatcher: KeybindingWatcher;
    public workspaceManager: WorkspaceManager;
    public diagnosticsManager: DiagnosticsManager;
    public eventReporter: NextEditSessionEventReporter;
    public blobNameCalculator: BlobNameCalculator;
    public editorNextEdits: EditorNextEdits;
    public backgroundNextEdits: BackgroundNextEdits;
    public suggestionManager: SuggestionManager;
    public requestManager: NextEditRequestManager;
    public clientMetricsReporter: ClientMetricsReporter;
    public completionJustAccepted: EphemeralObservable<boolean>;
    public completionVisibilityWatcher: CompletionVisibilityWatcher;

    static async create() {
        const kit = new BackgroundEditTestKit();
        await kit.awaitStartup();
        return kit;
    }

    private constructor() {
        super();

        createSourceFolderFiles(BackgroundEditTestKit.workspaceFolder);
        const workspaceFolders = [
            getWorkspaceFolder(BackgroundEditTestKit.workspaceFolder.folderRootUri),
        ];
        publishWorkspaceFoldersChange(workspaceFolders);

        this.augmentGlobalState = new AugmentGlobalState(new ExtensionContext());
        this.mockKeybindingWatcher = new KeybindingWatcher(this.augmentGlobalState);
        this.addDisposable(this.mockKeybindingWatcher);

        this.workspaceManager = this.createWorkspaceManager();
        this.addDisposable(this.workspaceManager);

        this.diagnosticsManager = new DiagnosticsManager();
        this.addDisposable(this.diagnosticsManager);

        this.eventReporter = new NextEditSessionEventReporter(this.apiServer);
        this.addDisposable(this.eventReporter);

        this.blobNameCalculator = new BlobNameCalculator(1_000_000);

        const statusBarManager = this.addDisposable(new StatusBarManager());
        const stateController = new StateController(statusBarManager);

        const suggestionManagerImpl = new SuggestionManagerImpl(
            this.workspaceManager,
            this.eventReporter
        );
        this.addDisposable(suggestionManagerImpl);
        // TODO(moogi): improve on this in followup PR
        // whoever creates the disposable should dispose it, but in this case it is either external
        // or created internally.. with a bit of refactoring we can handle both cases.
        this.addDisposable(suggestionManagerImpl.suggestionWasJustAccepted);
        this.suggestionManager = suggestionManagerImpl;

        this.clientMetricsReporter = new ClientMetricsReporter(this.apiServer);
        this.completionJustAccepted = new EphemeralObservable();
        this.addDisposable(this.completionJustAccepted);

        this.completionVisibilityWatcher = new CompletionVisibilityWatcher();
        this.addDisposable(this.completionVisibilityWatcher);

        const featureFlagManager = new FeatureFlagManager();
        const nextEditConfigManager = new NextEditConfigManager(
            this.configListener,
            featureFlagManager,
            this.augmentGlobalState
        );

        this.requestManager = new NextEditRequestManager(
            this.apiServer,
            this.configListener,
            this.workspaceManager,
            this.diagnosticsManager,
            this.eventReporter,
            this.clientMetricsReporter,
            this.blobNameCalculator,
            this.suggestionManager,
            new RecentItems<NextEditResultInfo>(10),
            stateController,
            this.completionJustAccepted,
            featureFlagManager
        );
        this.addDisposable(this.requestManager);

        this.editorNextEdits = new EditorNextEdits(
            this.ctx,
            this.workspaceManager,
            this.eventReporter,
            this.mockKeybindingWatcher,
            this.configListener,
            this.suggestionManager,
            this.requestManager,
            this.augmentGlobalState,
            nextEditConfigManager,
            this.completionVisibilityWatcher
        );
        this.addDisposable(this.editorNextEdits);

        this.backgroundNextEdits = new BackgroundNextEdits(
            this.workspaceManager,
            this.eventReporter,
            this.mockKeybindingWatcher,
            this.configListener,
            this.suggestionManager,
            this.requestManager,
            this.augmentGlobalState,
            nextEditConfigManager,
            this.completionVisibilityWatcher
        );
        this.addDisposable(this.backgroundNextEdits);

        // we must dispose of this at the end because it is invoked in some disposal handlers.
        this.addDisposable(featureFlagManager);
    }

    public get folderRootUri(): Uri {
        return BackgroundEditTestKit.workspaceFolder.folderRootUri;
    }

    public async awaitStartup() {
        await awaitWorkspaceState(this.workspaceManager, BackgroundEditTestKit.folderState);
    }
}

describe("BackgroundNextEdit", () => {
    let kit: BackgroundEditTestKit;

    beforeEach(async () => {
        window.activeTextEditor = undefined;
        jest.useFakeTimers();
        kit = await BackgroundEditTestKit.create();

        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                nextEdit: {
                    enableGlobalBackgroundSuggestions: true,
                },
                advanced: { nextEdit: { useDebounceMs: _DEBOUNCE_MS } },
            })
        );

        jest.spyOn(fsUtils, "fileExists").mockImplementation((_pathName: string) => {
            return true;
        });

        debug.activeDebugSession = undefined;
    });

    afterEach(() => {
        jest.useRealTimers();
        kit.dispose();
        TrackedDisposable.assertDisposed();
    });

    describe("onDidChangeTextDocument", () => {
        it("see changes for active text editor", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const doc = new MutableTextDocument(uri, "a");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            // Trigger event for doc
            workspace.textDocumentChanged.fire(doc.delete(0, 1));

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(1);
            expect(kit.requestManager.enqueueRequest).toHaveBeenNthCalledWith(
                1,
                expect.anything(),
                NextEditMode.Background,
                NextEditScope.Cursor
            );
        });

        it("ignore text document with no changes", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/path");
            const doc = new MutableTextDocument(uri, "mock document text");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            // Send an empty change request.
            workspace.textDocumentChanged.fire(new TextDocumentChangeEvent(doc, []));

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(0);
        });

        it("ignore event when no active editor open", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/path");
            const doc = new MutableTextDocument(uri, "abc");

            // Do not set the active editor.
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            workspace.textDocumentChanged.fire(doc.delete(0, 1));

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(0);
        });

        it("ignore changes for non-active text editor", async () => {
            const uriA = Uri.joinPath(kit.folderRootUri, "/example/a");
            const uriB = Uri.joinPath(kit.folderRootUri, "/example/b");
            const doc1 = new TextDocument(uriA, "a");
            const doc2 = new MutableTextDocument(uriB, "abc");
            // Set active editor to doc1
            window.activeTextEditor = new TextEditor(doc1);

            workspace.textDocuments = [doc1, doc2];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            // Trigger event for doc2
            workspace.textDocumentChanged.fire(doc2.delete(0, 1));

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(0);
        });

        it("ignore changes when there is a debug session", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const doc = new MutableTextDocument(uri, "a");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            debug.activeDebugSession = new DebugSession("42");

            // Trigger event for doc
            workspace.textDocumentChanged.fire(doc.delete(0, 1));

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(0);
        });

        it("ignore truncated suggestions", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const doc = new MutableTextDocument(uri, "a");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            const suggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "b"
            );
            jest.spyOn(kit.suggestionManager, "getActiveSuggestions").mockReturnValue([
                suggestion.with({
                    result: {
                        ...suggestion.result,
                        truncationChar: 1,
                    },
                }),
            ]);

            // Trigger event for doc
            workspace.textDocumentChanged.fire(doc.delete(0, 1));

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            // Because the only suggestion was truncated, we'll ignore it and make a query.
            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(1);
        });
    });

    describe("onDidChangeTextEditorSelection", () => {
        it("sees normal document selection changes", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/path");
            const doc = new MutableTextDocument(uri, "abc");
            const editor = new TextEditor(doc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            // Trigger event
            window.changeTextEditorSelection.fire({
                textEditor: editor,
                selections: [],
                kind: TextEditorSelectionChangeKind.Mouse,
            });

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(1);
        });

        it("sends file first time then cursor second", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const outputDoc = new TextDocument(uri, "line0" + "\n".repeat(100) + "line1");

            const editor = new TextEditor(outputDoc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [outputDoc];

            async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
                yield {
                    result: {
                        suggestionId: "",
                        path: "/example/a",
                        blobName: "",
                        charStart: 0,
                        charEnd: 6,
                        existingCode: "line0\n",
                        suggestedCode: "b",
                        truncationChar: undefined,
                        changeDescription: "",
                        diffSpans: [],
                        editingScore: 0,
                        localizationScore: 0,
                        editingScoreThreshold: 1.0,
                    },
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }

            jest.spyOn(kit.apiServer, "nextEditStream").mockImplementation(async () =>
                mockEditStream()
            );

            jest.spyOn(kit.workspaceManager, "getVCSWatchedFolderIds").mockReturnValue([1]);
            jest.spyOn(kit.workspaceManager, "getFileEditEvents").mockReturnValue([
                new FileEditEvent({
                    path: "/example/a",
                    beforeBlobName: "",
                    afterBlobName: "",
                    edits: [],
                }),
            ]);
            jest.spyOn(kit.workspaceManager, "getAllPathNames").mockReturnValue([
                new QualifiedPathName(kit.folderRootUri.fsPath, "/example/a"),
            ]);

            // Trigger event
            editor.selection = new Selection(0, 1, 0, 1);

            // Allow event loop to run
            jest.advanceTimersByTime(_DEBOUNCE_MS);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.apiServer.nextEditStream).toHaveBeenCalledTimes(1);
            expect(kit.apiServer.nextEditStream).toHaveBeenLastCalledWith(
                expect.objectContaining({
                    mode: NextEditMode.Background,
                    scope: NextEditScope.Cursor,
                })
            );

            // Trigger event
            editor.selection = new Selection(100, 0, 100, 0);

            // Allow event loop to run
            jest.advanceTimersByTime(_DEBOUNCE_MS);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.apiServer.nextEditStream).toHaveBeenCalledTimes(2);
            expect(kit.apiServer.nextEditStream).toHaveBeenLastCalledWith(
                expect.objectContaining({
                    mode: NextEditMode.Background,
                    scope: NextEditScope.Cursor,
                })
            );
        });

        it("ignore event when no active editor open", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const doc = new MutableTextDocument(uri, "a");
            const editor = new TextEditor(doc);

            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            window.changeTextEditorSelection.fire({
                textEditor: editor,
                selections: [],
                kind: TextEditorSelectionChangeKind.Mouse,
            });

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(0);
        });

        it("ignore output document changes", async () => {
            const outputDoc = new TextDocument(Uri.parse("output:///relative/a"), "a");
            const editor = new TextEditor(outputDoc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [outputDoc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            // Trigger event
            window.changeTextEditorSelection.fire({
                textEditor: editor,
                selections: [],
                kind: TextEditorSelectionChangeKind.Mouse,
            });

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(0);
        });

        it("ignore event when there is a debug session", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/path");
            const doc = new MutableTextDocument(uri, "abc");
            const editor = new TextEditor(doc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            debug.activeDebugSession = new DebugSession("42");

            // Trigger event
            window.changeTextEditorSelection.fire({
                textEditor: editor,
                selections: [],
                kind: TextEditorSelectionChangeKind.Mouse,
            });

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(0);
        });
    });

    describe("onSuggestionsChanged", () => {
        it("make a cursor request when accepting a truncated suggestion", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const doc = new MutableTextDocument(uri, "a");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            let truncatedSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "b"
            );
            truncatedSuggestion = truncatedSuggestion.with({
                result: {
                    ...truncatedSuggestion.result,
                    truncationChar: 1,
                },
            });
            // We add another suggestion so that the document changed listener will not
            // enqueue its own requests.
            const remainingSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(2, 3),
                "c"
            );
            kit.suggestionManager.add([truncatedSuggestion, remainingSuggestion]);

            kit.suggestionManager.accept([truncatedSuggestion]);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(1);
        });

        it("make a cursor request when accepting suggestion and the only remaining suggestions with that RI are truncated", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const doc = new MutableTextDocument(uri, "a");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];
            jest.spyOn(kit.requestManager, "enqueueRequest");

            const untruncatedSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(0, 1),
                "a"
            );
            let truncatedSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(2, 3),
                "b",
                {
                    requestId: untruncatedSuggestion.requestId,
                }
            );
            truncatedSuggestion = truncatedSuggestion.with({
                result: {
                    ...truncatedSuggestion.result,
                    truncationChar: 1,
                },
            });
            // We add another suggestion so that the document changed listener will not
            // enqueue its own requests.
            const remainingSuggestion = createSuggestion(
                doc,
                kit.workspaceManager.resolvePathName(doc.uri)!,
                new LineRange(3, 4),
                "c"
            );
            kit.suggestionManager.add([
                untruncatedSuggestion,
                truncatedSuggestion,
                remainingSuggestion,
            ]);

            kit.suggestionManager.accept([untruncatedSuggestion]);

            expect(kit.requestManager.enqueueRequest).toHaveBeenCalledTimes(1);
        });
    });

    describe("complete generation", () => {
        it("do not send requests with no changes", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const outputDoc = new TextDocument(uri, "line0\nline1\nline2\nline3\nline4\n");

            const editor = new TextEditor(outputDoc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [outputDoc];

            async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
                yield {
                    result: {
                        suggestionId: "",
                        path: "/example/a",
                        blobName: "",
                        charStart: 0,
                        charEnd: 0,
                        existingCode: "",
                        suggestedCode: "b",
                        truncationChar: undefined,
                        changeDescription: "",
                        diffSpans: [],
                        editingScore: 0,
                        localizationScore: 0,
                        editingScoreThreshold: 1.0,
                    },
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }

            kit.apiServer.nextEditStream = async () => mockEditStream();

            jest.spyOn(kit.workspaceManager, "getVCSWatchedFolderIds").mockReturnValue([1]);
            jest.spyOn(kit.workspaceManager, "getFileEditEvents").mockReturnValue([]);

            // Trigger event
            window.changeTextEditorSelection.fire({
                textEditor: editor,
                selections: [],
                kind: TextEditorSelectionChangeKind.Mouse,
            });

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            jest.advanceTimersByTime(_DEBOUNCE_MS);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.suggestionManager.getActiveSuggestions()).toEqual([]);
        });

        it("perform a background edit", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const outputDoc = new TextDocument(uri, "line0\nline1\nline2\nline3\nline4\n");

            const editor = new TextEditor(outputDoc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [outputDoc];

            async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
                yield {
                    result: {
                        suggestionId: "",
                        path: "/example/a",
                        blobName: "",
                        charStart: 0,
                        charEnd: 0,
                        existingCode: "",
                        suggestedCode: "b",
                        truncationChar: undefined,
                        changeDescription: "",
                        diffSpans: [],
                        editingScore: 0,
                        localizationScore: 0,
                        editingScoreThreshold: 1.0,
                    },
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }

            kit.apiServer.nextEditStream = async () => mockEditStream();

            jest.spyOn(kit.workspaceManager, "getVCSWatchedFolderIds").mockReturnValue([1]);
            jest.spyOn(kit.workspaceManager, "getFileEditEvents").mockReturnValue([
                new FileEditEvent({
                    path: "/example/a",
                    beforeBlobName: "",
                    afterBlobName: "",
                    edits: [],
                }),
            ]);
            jest.spyOn(kit.workspaceManager, "getAllPathNames").mockReturnValue([
                new QualifiedPathName(kit.folderRootUri.fsPath, "/example/a"),
            ]);

            // Trigger event
            window.changeTextEditorSelection.fire({
                textEditor: editor,
                selections: [],
                kind: TextEditorSelectionChangeKind.Mouse,
            });

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            jest.advanceTimersByTime(_DEBOUNCE_MS);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.suggestionManager.getActiveSuggestions().length).toBeGreaterThan(0);
        });

        it("perform workspace query after empty response", async () => {
            const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
            const outputDoc = new TextDocument(uri, "line0\nline1\nline2\nline3\nline4\n");

            const editor = new TextEditor(outputDoc);
            window.activeTextEditor = editor;
            workspace.textDocuments = [outputDoc];

            async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
                // We need to return at least a no-op suggestion to not be considered
                // as a failed request
                yield {
                    result: {
                        suggestionId: "",
                        path: "/example/a",
                        blobName: "",
                        charStart: 0,
                        charEnd: 0,
                        existingCode: "",
                        suggestedCode: "",
                        truncationChar: undefined,
                        changeDescription: "",
                        diffSpans: [],
                        editingScore: 0,
                        localizationScore: 0,
                        editingScoreThreshold: 1.0,
                    },
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }

            jest.spyOn(kit.apiServer, "nextEditStream").mockImplementation(async () =>
                mockEditStream()
            );

            jest.spyOn(kit.workspaceManager, "getVCSWatchedFolderIds").mockReturnValue([1]);
            jest.spyOn(kit.workspaceManager, "getFileEditEvents").mockReturnValue([
                new FileEditEvent({
                    path: "/example/a",
                    beforeBlobName: "",
                    afterBlobName: "",
                    edits: [],
                }),
            ]);
            jest.spyOn(kit.workspaceManager, "getAllPathNames").mockReturnValue([
                new QualifiedPathName(kit.folderRootUri.fsPath, "/example/a"),
            ]);

            // Trigger event
            window.changeTextEditorSelection.fire({
                textEditor: editor,
                selections: [],
                kind: TextEditorSelectionChangeKind.Mouse,
            });

            // Allow event loop to run
            jest.advanceTimersByTime(_DEBOUNCE_MS);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.apiServer.nextEditStream).toHaveBeenCalledTimes(1);
            expect(kit.apiServer.nextEditStream).toHaveBeenLastCalledWith(
                expect.objectContaining({
                    mode: NextEditMode.Background,
                    scope: NextEditScope.Cursor,
                })
            );

            // Allow event loop to run
            jest.advanceTimersByTime(_DEBOUNCE_MS);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.apiServer.nextEditStream).toHaveBeenCalledTimes(2);
            expect(kit.apiServer.nextEditStream).toHaveBeenLastCalledWith(
                expect.objectContaining({
                    mode: NextEditMode.Background,
                    scope: NextEditScope.File,
                })
            );

            // Allow event loop to run
            jest.advanceTimersByTime(_DEBOUNCE_MS);
            await jest.advanceTimersByTimeAsync(1);

            expect(kit.apiServer.nextEditStream).toHaveBeenCalledTimes(3);
            expect(kit.apiServer.nextEditStream).toHaveBeenLastCalledWith(
                expect.objectContaining({
                    mode: NextEditMode.Background,
                    scope: NextEditScope.Workspace,
                })
            );
        });
    });
});
