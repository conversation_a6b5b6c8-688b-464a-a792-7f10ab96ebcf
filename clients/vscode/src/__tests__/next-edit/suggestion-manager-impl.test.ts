import { Qualified<PERSON>athName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import {
    Disposable,
    ExtensionContext,
    getWorkspaceFolder,
    MutableTextDocument,
    publishWorkspaceFoldersChange,
    Range,
    Selection,
    TextEditor,
    TrackedDisposable,
    Uri,
    window,
    workspace,
} from "../../__mocks__/vscode-mocks";
import { NextEditSessionEventReporter } from "../../metrics/next-edit-session-event-reporter";
import { SuggestionState } from "../../next-edit/next-edit-types";
import { SuggestionChangedEvent } from "../../next-edit/suggestion-manager";
import { SuggestionManagerImpl } from "../../next-edit/suggestion-manager-impl";
import { LineRange } from "../../utils/ranges";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitWorkspaceState,
    createSourceFolderFiles,
    defaultWorkspaceFolder,
    defaultWorkspaceState,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";
import { createDoc, createEvent, createEventForSuggestions, createSuggestion } from "./test-utils";

class SuggestionManagerTestKit extends WorkspaceManagerTestKit {
    static readonly workspaceFolder = defaultWorkspaceFolder;
    static readonly folderState = defaultWorkspaceState;

    public readonly ctx = new ExtensionContext();
    public workspaceManager: WorkspaceManager;
    public suggestionManager: SuggestionManagerImpl;

    static async create() {
        const kit = new SuggestionManagerTestKit();
        await kit.awaitStartup();
        return kit;
    }

    private constructor() {
        super();

        createSourceFolderFiles(SuggestionManagerTestKit.workspaceFolder);
        const workspaceFolders = [
            getWorkspaceFolder(SuggestionManagerTestKit.workspaceFolder.folderRootUri),
        ];
        publishWorkspaceFoldersChange(workspaceFolders);

        this.workspaceManager = this.createWorkspaceManager();
        const apiServer = new MockAPIServer();
        const eventReporter = new NextEditSessionEventReporter(apiServer);
        this.suggestionManager = new SuggestionManagerImpl(this.workspaceManager, eventReporter);
        this.addDisposable(this.suggestionManager);
    }

    public get folderRootUri(): Uri {
        return SuggestionManagerTestKit.workspaceFolder.folderRootUri;
    }

    public async awaitStartup() {
        await awaitWorkspaceState(this.workspaceManager, SuggestionManagerTestKit.folderState);
    }

    public getActiveSuggestionsWithState() {
        return this.suggestionManager.getActiveSuggestions().map((s) => {
            return {
                id: s.result.suggestionId,
                state: s.state,
            };
        });
    }

    public getJustAcceptedSuggestions() {
        return this.suggestionManager
            .getJustAcceptedSuggestions()
            .map((s) => s.result.suggestionId);
    }
}

describe("SuggestionManagerImpl", () => {
    let kit: SuggestionManagerTestKit;
    let doc: MutableTextDocument;
    let qualifiedPathName: QualifiedPathName;
    let disposables: Disposable[] = [];

    beforeEach(async () => {
        jest.useFakeTimers();
        kit = await SuggestionManagerTestKit.create();
        disposables.push(kit);
        qualifiedPathName = new QualifiedPathName(kit.folderRootUri.fsPath, "example");
        doc = createDoc(qualifiedPathName, "line 0\nline 1\nline 2\nline 3\nline 4\n");
        window.visibleTextEditors = [new TextEditor(doc)];
        window.activeTextEditor = window.visibleTextEditors[0];
        workspace.textDocuments = [doc];
    });

    afterEach(() => {
        jest.useRealTimers();
        disposables.forEach((d) => d.dispose());
        disposables = [];
        TrackedDisposable.assertDisposed();
    });

    describe("add", () => {
        it("adds new suggestions", () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([suggestion]);
            expect(kit.suggestionManager.getActiveSuggestions()).toContain(suggestion);
        });

        it("doesn't add rejected suggestions", () => {
            const rejectedSuggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "bad line 0"
            );
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([rejectedSuggestion]);
            kit.suggestionManager.reject([rejectedSuggestion]);
            kit.suggestionManager.add([suggestion]);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(0);
        });

        it("adds rejected suggestions with override", () => {
            const rejectedSuggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "bad line 0"
            );
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([rejectedSuggestion]);
            kit.suggestionManager.reject([rejectedSuggestion]);
            kit.suggestionManager.add([suggestion], true);
            expect(kit.suggestionManager.getActiveSuggestions()).toContain(suggestion);
        });

        it("new suggestions clear out old ones for all lines", () => {
            const oldSuggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "old line 1"),
                createSuggestion(doc, qualifiedPathName, new LineRange(1, 1), "old line 2"),
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 4), "old line 3"),
                // This last suggestion is kept as it doesn't intersect with any of the
                // new suggestions.
                createSuggestion(doc, qualifiedPathName, new LineRange(4, 4), "old line 4"),
            ];
            kit.suggestionManager.add(oldSuggestions);
            expect(kit.suggestionManager.getActiveSuggestions()).toEqual(oldSuggestions);

            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "new line 1"),
                createSuggestion(doc, qualifiedPathName, new LineRange(1, 1), "new line 2"),
                createSuggestion(doc, qualifiedPathName, new LineRange(2, 4), "new line 3"),
            ];
            kit.suggestionManager.add(suggestions);
            const expectedSuggestions = [oldSuggestions[3], ...suggestions];
            expect(kit.suggestionManager.getActiveSuggestions()).toEqual(expectedSuggestions);
        });

        it("notifies when something was added", async () => {
            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([suggestion]);
            expect(kit.suggestionManager.getActiveSuggestions()).toContain(suggestion);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: [],
                newSuggestions: [suggestion],
                accepted: [],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });
    });

    describe("remove", () => {
        it("does nothing if suggestion doesn't exist", () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            expect(kit.suggestionManager.remove([suggestion])).toBe(false);
            expect(kit.suggestionManager.getAllSuggestions()).not.toContain(suggestion);
        });

        it("removes active suggestions", () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([suggestion]);
            expect(kit.suggestionManager.getActiveSuggestions()).toContain(suggestion);
            expect(kit.suggestionManager.remove([suggestion])).toBe(true);
            expect(kit.suggestionManager.getAllSuggestions()).not.toContain(suggestion);
        });

        it("removes rejected suggestions", () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([suggestion]);
            kit.suggestionManager.reject([suggestion]);
            expect(kit.suggestionManager.getRejectedSuggestions()).toContain(suggestion);
            expect(kit.suggestionManager.remove([suggestion])).toBe(true);
            expect(kit.suggestionManager.getAllSuggestions()).not.toContain(suggestion);
        });

        it("notifies when something was removed", async () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([suggestion]);
            expect(kit.suggestionManager.getActiveSuggestions()).toContain(suggestion);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            expect(kit.suggestionManager.remove([suggestion])).toBe(true);

            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: [suggestion],
                newSuggestions: [],
                accepted: [],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });
    });

    describe("accept", () => {
        it("updates the affected documents", async () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0\n"
            );
            expect(doc.getText(new Range(0, 0, 1, 0))).toBe("line 0\n");
            kit.suggestionManager.add([suggestion]);
            kit.suggestionManager.accept([suggestion]);
            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            // The document should now be updated!
            expect(doc.getText(new Range(0, 0, 1, 0))).toBe("modified line 0\n");
        });
    });

    describe("reject", () => {
        it("adds suggestion to rejected list", async () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0\n"
            );
            expect(doc.getText(new Range(0, 0, 1, 0))).toBe("line 0\n");
            kit.suggestionManager.add([suggestion]);
            kit.suggestionManager.reject([suggestion], 10);
            expect(kit.suggestionManager.getActiveSuggestions()).not.toContain(suggestion);
            expect(kit.suggestionManager.getRejectedSuggestions()).toContain(suggestion);
            // Wait for 10ms.
            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(10);
            expect(kit.suggestionManager.getRejectedSuggestions()).not.toContain(suggestion);
        });

        it("notifies listeners", async () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0"
            );
            kit.suggestionManager.add([suggestion]);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            kit.suggestionManager.reject([suggestion]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: [suggestion],
                newSuggestions: [],
                accepted: [],
                rejected: [suggestion],
                undone: [],
            } as SuggestionChangedEvent);
        });
    });

    describe("handleChangeEvent", () => {
        it("removes invalidated suggestions", async () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(3, 4),
                "modified line 3\n"
            );
            kit.suggestionManager.add([suggestion]);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            const event = createEvent(doc, [
                [new Range(2, 0, 4, 0), "modified line 0\nnew line 0.5\n"],
            ]);
            expect(kit.suggestionManager.handleChangeEvent(event)).toBe(false);
            expect(
                kit.suggestionManager
                    .getAllSuggestions()
                    .filter((s) => s.result.suggestionId === suggestion.result.suggestionId)
            ).toHaveLength(0);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: [suggestion],
                newSuggestions: [],
                accepted: [],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });

        it("treats accepted suggestions by preseving state for request id", async () => {
            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "modified line 0\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(2, 3), "modified line 2\n", {
                    requestId: "request-id-2",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 4), "modified line 3\n", {
                    requestId: "request-id-1",
                }),
            ];
            kit.suggestionManager.add(suggestions);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            const event = createEventForSuggestions(doc, [suggestions[0]]);
            expect(kit.suggestionManager.handleChangeEvent(event)).toBe(true);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                { id: suggestions[1].result.suggestionId, state: SuggestionState.fresh },
                { id: suggestions[2].result.suggestionId, state: SuggestionState.fresh },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([suggestions[0].result.suggestionId]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: suggestions,
                newSuggestions: kit.suggestionManager.getActiveSuggestions(),
                accepted: [suggestions[0].with({ state: SuggestionState.accepted })],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });

        it("treats accepted suggestions by preseving state for request id with additional updates", async () => {
            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "modified line 0\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(2, 3), "modified line 2\n", {
                    requestId: "request-id-2",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 4), "modified line 3\n", {
                    requestId: "request-id-1",
                }),
            ];
            kit.suggestionManager.add(suggestions);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            const event = createEvent(doc, [
                [new Range(0, 0, 1, 0), "modified line 0\n"],
                // A change that isn't one of the suggestions.
                [new Range(1, 0, 2, 0), "different line 1\n"],
            ]);
            expect(kit.suggestionManager.handleChangeEvent(event)).toBe(true);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                { id: suggestions[1].result.suggestionId, state: SuggestionState.fresh },
                { id: suggestions[2].result.suggestionId, state: SuggestionState.fresh },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([suggestions[0].result.suggestionId]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: suggestions,
                newSuggestions: kit.suggestionManager.getActiveSuggestions(),
                accepted: [suggestions[0].with({ state: SuggestionState.accepted })],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });

        it("invalidates on line deletions", async () => {
            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 4), "line 3\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(4, 5), "modified line 4\n", {
                    requestId: "request-id-1",
                }),
            ];
            kit.suggestionManager.add(suggestions);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            const event = createEvent(doc, [
                // Delete line 5
                [new Range(4, 0, 5, 0), ""],
            ]);
            expect(kit.suggestionManager.handleChangeEvent(event)).toBe(false);
            expect(kit.suggestionManager.getActiveSuggestions()).toHaveLength(1);
            expect(kit.suggestionManager.getActiveSuggestions()[0].result.suggestionId).toBe(
                suggestions[0].result.suggestionId
            );
            expect(kit.suggestionManager.getActiveSuggestions()[0].state).toBe(
                SuggestionState.stale
            );

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: suggestions,
                newSuggestions: kit.suggestionManager.getActiveSuggestions(),
                accepted: [],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });

        it("does not mark suggestions as stale if some were invalidated, but others were accepted", async () => {
            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "modified line 0\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(2, 3), "modified line 2\n", {
                    requestId: "request-id-2",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 4), "modified line 3\n", {
                    requestId: "request-id-1",
                }),
            ];
            kit.suggestionManager.add(suggestions);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);
            const event = createEvent(doc, [
                [new Range(0, 0, 1, 0), "modified line 0\n"],
                // A change that isn't one of the suggestions.
                [new Range(2, 0, 3, 0), "different line 2\n"],
            ]);
            expect(kit.suggestionManager.handleChangeEvent(event)).toBe(true);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                { id: suggestions[2].result.suggestionId, state: SuggestionState.fresh },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([suggestions[0].result.suggestionId]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: suggestions,
                newSuggestions: kit.suggestionManager.getActiveSuggestions(),
                accepted: [suggestions[0].with({ state: SuggestionState.accepted })],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });

        it("does mark suggestions as stale if some were invalidated, none were accepted, and none were undone", async () => {
            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "modified line 0\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(2, 3), "modified line 2\n", {
                    requestId: "request-id-2",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 4), "modified line 3\n", {
                    requestId: "request-id-1",
                }),
            ];
            kit.suggestionManager.add(suggestions);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);

            const event = createEvent(doc, [
                // A change that isn't one of the suggestions.
                [new Range(2, 0, 3, 0), "different line 2\n"],
            ]);
            expect(kit.suggestionManager.handleChangeEvent(event)).toBe(false);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                { id: suggestions[0].result.suggestionId, state: SuggestionState.stale },
                { id: suggestions[2].result.suggestionId, state: SuggestionState.stale },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: suggestions,
                newSuggestions: kit.suggestionManager.getActiveSuggestions(),
                accepted: [],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);
        });

        it("handles undo", async () => {
            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "line 0\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(1, 2), "modified line 1\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 5), "line 3\nline 4", {
                    requestId: "request-id-1",
                }),
            ];
            const suggestion = suggestions[1];
            kit.suggestionManager.add(suggestions);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);

            kit.suggestionManager.accept([suggestion]);
            expect(kit.suggestionManager.suggestionWasJustAccepted.value).toBe(true);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                { id: suggestions[0].result.suggestionId, state: SuggestionState.fresh },
                { id: suggestions[2].result.suggestionId, state: SuggestionState.fresh },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([suggestions[1].result.suggestionId]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: suggestions,
                newSuggestions: kit.suggestionManager.getActiveSuggestions(),
                accepted: [suggestion.with({ state: SuggestionState.accepted })],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);

            kit.suggestionManager.add([
                // Turns out the backend loves to send a "noop" suggestion after the
                // a suggestion is accepted.
                createSuggestion(doc, qualifiedPathName, new LineRange(1, 2), "modified line 1\n", {
                    requestId: "request-id-2",
                }),
            ]);

            const undoEvent = createEvent(doc, [[new Range(1, 0, 2, 0), "line 1\n"]]);
            expect(kit.suggestionManager.handleChangeEvent(undoEvent)).toBe(false);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                // Importantly, these should not be marked stale.
                { id: suggestions[0].result.suggestionId, state: SuggestionState.fresh },
                { id: suggestions[2].result.suggestionId, state: SuggestionState.fresh },
                { id: suggestions[1].result.suggestionId, state: SuggestionState.fresh },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith(
                expect.objectContaining({
                    undone: [suggestion],
                })
            );
        });

        it("handles reject with undo", async () => {
            const suggestions = [
                createSuggestion(doc, qualifiedPathName, new LineRange(0, 1), "line 0\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(1, 2), "modified line 1\n", {
                    requestId: "request-id-1",
                }),
                createSuggestion(doc, qualifiedPathName, new LineRange(3, 5), "line 3\nline 4", {
                    requestId: "request-id-1",
                }),
            ];
            const suggestion = suggestions[1];
            kit.suggestionManager.add(suggestions);

            const listenerFn = jest.fn();
            const listener = kit.suggestionManager.onSuggestionsChanged(listenerFn);
            disposables.push(listener);

            kit.suggestionManager.accept([suggestion]);
            expect(kit.suggestionManager.suggestionWasJustAccepted.value).toBe(true);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                { id: suggestions[0].result.suggestionId, state: SuggestionState.fresh },
                { id: suggestions[2].result.suggestionId, state: SuggestionState.fresh },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([suggestions[1].result.suggestionId]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            expect(listenerFn).toHaveBeenCalledWith({
                oldSuggestions: suggestions,
                newSuggestions: kit.suggestionManager.getActiveSuggestions(),
                accepted: [suggestion.with({ state: SuggestionState.accepted })],
                rejected: [],
                undone: [],
            } as SuggestionChangedEvent);

            kit.suggestionManager.add([
                // Turns out the backend loves to send a "noop" suggestion after the
                // a suggestion is accepted.
                createSuggestion(doc, qualifiedPathName, new LineRange(1, 2), "modified line 1\n", {
                    requestId: "request-id-2",
                }),
            ]);

            kit.suggestionManager.reject([suggestion.with({ state: SuggestionState.accepted })]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            const undoEvent = createEvent(doc, [[new Range(1, 0, 2, 0), "line 1\n"]]);
            expect(kit.suggestionManager.handleChangeEvent(undoEvent)).toBe(false);
            expect(kit.getActiveSuggestionsWithState()).toEqual([
                // Importantly, these should not be marked stale.
                { id: suggestions[0].result.suggestionId, state: SuggestionState.fresh },
                { id: suggestions[2].result.suggestionId, state: SuggestionState.fresh },
            ]);
            expect(kit.getJustAcceptedSuggestions()).toEqual([]);

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);
        });
    });

    describe("wasSuggestionJustAccepted", () => {
        it("is false by default", async () => {
            expect(kit.suggestionManager.suggestionWasJustAccepted.value).toBe(undefined);
        });

        it("is true after accepting a suggestion and false after an editor change", async () => {
            const suggestion = createSuggestion(
                doc,
                qualifiedPathName,
                new LineRange(0, 1),
                "modified line 0\n"
            );
            kit.suggestionManager.add([suggestion]);
            kit.suggestionManager.accept([suggestion]);

            expect(kit.suggestionManager.suggestionWasJustAccepted.value).toBe(true);
            // Allow some time to pass before the next event.
            await jest.advanceTimersByTimeAsync(2);
            window.activeTextEditor!.selection = new Selection(1, 0, 1, 0);

            {
                const event = createEvent(doc, [[new Range(0, 0, 1, 0), "updated line 0\n"]]);
                kit.suggestionManager.handleChangeEvent(event);
            }
            expect(kit.suggestionManager.suggestionWasJustAccepted.value).toBe(undefined);
        });
    });
});
