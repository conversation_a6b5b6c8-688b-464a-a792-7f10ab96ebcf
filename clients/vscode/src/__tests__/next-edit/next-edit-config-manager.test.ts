import {
    generateMockWorkspaceConfig,
    getExampleAugmenConfig,
} from "../../__mocks__/mock-augment-config";
import {
    ExtensionContext,
    mockWorkspaceConfigChange,
    TrackedDisposable,
} from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { FeatureFlagManager } from "../../feature-flags";
import {
    NextEditConfigManager,
    nextEditUxModeFlags,
} from "../../next-edit/next-edit-config-manager";
import { AugmentGlobalState } from "../../utils/context";

describe("NextEditConfigManager", () => {
    let featureFlagManager: FeatureFlagManager;
    jest.mock("../../utils/environment", () => ({
        getAugmentExtensionPackageJson: jest.fn(() => ({
            version: "0.3.0",
            contributes: {
                keybindings: [],
                icons: {},
            },
        })),
    }));

    beforeEach(() => {
        featureFlagManager = new FeatureFlagManager();
    });

    afterEach(() => {
        featureFlagManager.dispose();
        TrackedDisposable.assertDisposed();
    });

    test.each([
        { minPanelVersion: "", expected: false },
        { enablePanel: true, minPanelVersion: "0.0.0", expected: true },
        { enablePanel: false, minPanelVersion: "0.0.0", expected: false },
        { enablePanel: true, minPanelVersion: "0.9.0", expected: true },
        { enablePanel: false, minPanelVersion: "0.9.0", expected: false },
    ])("overrides %o", (nextEditConfig) => {
        const config = getExampleAugmenConfig({
            nextEdit: {
                enableBottomPanel: nextEditConfig.enablePanel ?? false, // enablePanel override
            },
        });

        if (nextEditConfig.minPanelVersion !== undefined) {
            featureFlagManager.update({
                ...featureFlagManager.currentFlags,
                vscodeNextEditBottomPanelMinVersion: nextEditConfig.minPanelVersion,
            });
        }

        const result = nextEditUxModeFlags(config, featureFlagManager).enablePanel;
        expect(result).toEqual(nextEditConfig.expected);
    });

    describe("NextEditConfigManager instance", () => {
        let configListener: AugmentConfigListener;
        let featureFlagManager: FeatureFlagManager;
        let nextEditConfigManager: NextEditConfigManager;

        beforeEach(async () => {
            configListener = new AugmentConfigListener();
            mockWorkspaceConfigChange(
                generateMockWorkspaceConfig({
                    advanced: { enableDebugFeatures: false },
                })
            );
            // Wait for the config to update.
            await jest.advanceTimersByTimeAsync(1);
            featureFlagManager = new FeatureFlagManager();
            nextEditConfigManager = new NextEditConfigManager(
                configListener,
                featureFlagManager,
                new AugmentGlobalState(new ExtensionContext())
            );
        });
        afterEach(() => {
            nextEditConfigManager.dispose();
            featureFlagManager.dispose();
            configListener.dispose();
        });
        test("default config", () => {
            expect(nextEditConfigManager.config.enablePanel).toEqual(false);
        });
    });
});
