export const suggestion = {
    descriptionOutput: {
        logProbs: [-0.5234375, -0.41796875, -0.0014953613, -0.006225586],
        offsets: [0, 6, 15, 25],
        text: "Update function signature<|eot_id|>",
        tokenIds: [4387, 734, 12223, 128009],
    },
    descriptionPrompt: {
        logProbs: [],
        offsets: [
            0, 17, 36, 42, 59, 61, 64, 68, 70, 80, 88, 91, 94, 99, 103, 105, 109, 114, 117, 120,
            128, 137, 149, 154, 158, 163, 170, 176, 180, 189, 192, 198, 209, 214, 219, 220, 224,
            232, 235, 240, 252, 255, 258, 263, 269, 280, 288, 299, 303, 311, 314, 318, 325, 328,
            332, 340, 343, 347, 352, 356, 357, 367, 386, 390, 407, 409, 413, 416, 420, 425, 432,
            442, 445, 452, 457, 462, 464, 466, 468, 471, 480, 484, 487, 493, 496, 499, 503, 504,
            513, 520, 523, 526, 531, 534, 535, 538, 547, 551, 554, 560, 563, 566, 570, 571, 580,
            587, 590, 593, 598, 601, 602, 604, 606, 609, 610, 612, 614, 617, 618, 620, 624, 625,
            629, 631, 632, 634, 636, 639, 643, 650, 651, 658, 661, 663, 672, 676, 684, 692, 693,
            700, 704, 706, 708, 713, 715, 720, 724, 725, 731, 734, 735, 739, 744, 747, 750, 751,
            753, 755, 764, 770, 773, 777, 784, 786, 787, 789, 796, 798, 800, 802, 803, 810, 814,
            815, 818, 820, 822, 825, 827, 828, 830, 832, 835, 839, 846, 847, 854, 858, 864, 871,
            874, 880, 885, 887, 892, 896, 900, 905, 908, 914, 919, 920, 926, 932, 938, 944, 945,
            951, 954, 955, 959, 961, 962, 964, 966, 969, 973, 980, 981, 988, 992, 998, 1005, 1009,
            1017, 1025, 1026, 1032, 1038, 1044, 1050, 1052, 1053, 1055, 1059, 1066, 1068, 1073,
            1077, 1081, 1086, 1088, 1092, 1104, 1106, 1108, 1114, 1117, 1118, 1125, 1129, 1130,
            1131, 1133, 1135, 1140, 1142, 1143, 1145, 1147, 1150, 1154, 1161, 1162, 1170, 1175,
            1177, 1178, 1182, 1190, 1198, 1199, 1205, 1211, 1217, 1223, 1224, 1231, 1238, 1240,
            1242, 1245, 1251, 1257, 1264, 1270, 1271, 1277, 1278, 1281, 1282, 1288, 1289, 1292,
            1295, 1297, 1302, 1307, 1312, 1316, 1323, 1326, 1328, 1330, 1336, 1338, 1344, 1348,
            1357, 1362, 1366, 1378, 1382, 1386, 1394, 1399, 1400, 1406, 1409, 1414, 1419, 1420,
            1422, 1428, 1430, 1435, 1438, 1442, 1449, 1454, 1458, 1467, 1471, 1475, 1477, 1485,
            1491, 1496, 1500, 1502, 1512, 1531, 1540, 1557,
        ],
        text: '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a developer working in my codebase.\nYour task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>\n\nHere is the code change delimited by triple backticks:\n```\n--- services/integrations/slack_bot/processor/server/slack_chat.go\n+++ services/integrations/slack_bot/processor/server/slack_chat.go\n@@ -759,11 +759,15 @@\n@func (h *SlackChatHandler) handleAppUninstalled(ctx context.Context, tenantInfo *riproto.TenantInfo) error {\n \tlog.Info().Msgf("Uninstalled slackbot for tenant %s", tenantID)\n \n \treturn nil\n }\n \n-func (h *SlackChatHandler) handleRepoSelectCommand(reactionAdded *slackboteventproto.ReactionAdded, slackClient SlackClient) error {\n+func (h *SlackChatHandler) handleRepoSelectCommand(ctx context.Context, slackClient SlackClient,\n+\tslashCommand *slackboteventproto.SlashCommandEvent,\n+) error {\n+\treturn nil\n+}\n \n func (h *SlackChatHandler) getUserEmail(\n \tctx context.Context, slackClient SlackClient, userId string,\n ) (*secretstring.SecretString, error) {\n \temail, ok := h.userEmailCache.Get(userId)\n\n```\nDirectly start the response with the description and say nothing else. Write no more than 10 words.\nFocus on the fields that are changing but don\'t include where they are.\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n',
        tokenIds: [
            128000, 128006, 9125, 128007, 271, 2675, 527, 264, 16131, 3318, 304, 856, 2082, 3231,
            627, 7927, 3465, 374, 311, 8819, 9959, 2038, 505, 279, 2082, 2349, 3770, 323, 7664, 433,
            1701, 48696, 19120, 1376, 13, 578, 7580, 315, 701, 4096, 374, 311, 1520, 1023, 13707,
            6288, 3619, 1401, 13878, 315, 279, 2349, 304, 279, 2317, 315, 279, 2082, 3231, 13,
            128009, 128006, 882, 128007, 271, 8586, 374, 279, 2082, 2349, 86428, 555, 24657, 1203,
            36178, 512, 14196, 4077, 4521, 3600, 32214, 14750, 811, 83677, 474, 39983, 14, 30574,
            38355, 83677, 474, 36153, 18487, 198, 63830, 3600, 32214, 14750, 811, 83677, 474, 39983,
            14, 30574, 38355, 83677, 474, 36153, 18487, 198, 19741, 482, 26439, 11, 806, 489, 26439,
            11, 868, 88220, 31, 2900, 320, 71, 353, 7594, 474, 16047, 3126, 8, 3790, 2213, 1844,
            50673, 7654, 2317, 9512, 11, 26305, 1767, 353, 462, 15461, 844, 26178, 1767, 8, 1493,
            341, 220, 6867, 20736, 1020, 6749, 69, 446, 1844, 50673, 46719, 6465, 369, 26305, 1034,
            82, 498, 26305, 926, 340, 720, 220, 862, 2139, 198, 457, 720, 2269, 1371, 320, 71, 353,
            7594, 474, 16047, 3126, 8, 3790, 26309, 3461, 4153, 5921, 1335, 19897, 353, 89648,
            94018, 688, 15461, 2887, 1335, 19897, 11, 46719, 3032, 58344, 3032, 8, 1493, 341, 10,
            2900, 320, 71, 353, 7594, 474, 16047, 3126, 8, 3790, 26309, 3461, 4153, 7654, 2317,
            9512, 11, 46719, 3032, 58344, 3032, 345, 10, 1942, 25713, 4153, 353, 89648, 94018, 688,
            15461, 815, 25713, 86592, 345, 37297, 1493, 341, 10, 862, 2139, 198, 10, 534, 720, 2988,
            320, 71, 353, 7594, 474, 16047, 3126, 8, 24075, 4886, 1021, 220, 21660, 2317, 9512, 11,
            46719, 3032, 58344, 3032, 11, 10542, 925, 345, 883, 4712, 21107, 928, 75879, 707, 11,
            1493, 8, 341, 220, 58649, 11, 5509, 1703, 305, 3405, 4886, 8397, 2283, 25484, 696,
            14196, 4077, 16411, 398, 1212, 279, 2077, 449, 279, 4096, 323, 2019, 4400, 775, 13,
            9842, 912, 810, 1109, 220, 605, 4339, 627, 14139, 389, 279, 5151, 430, 527, 10223, 719,
            1541, 956, 2997, 1405, 814, 527, 627, 128009, 128006, 78191, 128007, 271,
        ],
    },
    generationId: "d4044f80-b156-497b-984a-c2c274c00b10",
    result: {
        checkpointNotFound: false,
        suggestedEdit: {
            blobName: "cfc3ebef282a81856c3ce93bc6816f7d5ecc752095aef7bf9f4fc3ca50272b8b",
            changeDescription: "Update function signature",
            charEnd: 28837,
            charStart: 28704,
            diffSpans: [
                {
                    original: {
                        start: 0,
                        stop: 51,
                    },
                    updated: {
                        start: 0,
                        stop: 51,
                    },
                },
                {
                    original: {
                        start: 51,
                        stop: 64,
                    },
                    updated: {
                        start: 51,
                        stop: 110,
                    },
                },
                {
                    original: {
                        start: 64,
                        stop: 85,
                    },
                    updated: {
                        start: 110,
                        stop: 131,
                    },
                },
                {
                    original: {
                        start: 85,
                        stop: 111,
                    },
                    updated: {
                        start: 131,
                        stop: 148,
                    },
                },
                {
                    original: {
                        start: 111,
                        stop: 111,
                    },
                    updated: {
                        start: 148,
                        stop: 148,
                    },
                },
                {
                    original: {
                        start: 111,
                        stop: 123,
                    },
                    updated: {
                        start: 148,
                        stop: 150,
                    },
                },
                {
                    original: {
                        start: 123,
                        stop: 133,
                    },
                    updated: {
                        start: 150,
                        stop: 160,
                    },
                },
                {
                    original: {
                        start: 133,
                        stop: 133,
                    },
                    updated: {
                        start: 160,
                        stop: 174,
                    },
                },
            ],
            editingScore: 0.7849803,
            editingScoreThreshold: 1,
            existingCode:
                "func (h *SlackChatHandler) handleRepoSelectCommand(reactionAdded *slackboteventproto.ReactionAdded, slackClient SlackClient) error {\n",
            localizationScore: 0.99808687,
            path: "services/integrations/slack_bot/processor/server/slack_chat.go",
            suggestedCode:
                "func (h *SlackChatHandler) handleRepoSelectCommand(ctx context.Context, slackClient SlackClient,\n\tslashCommand *slackboteventproto.SlashCommandEvent,\n) error {\n\treturn nil\n}\n",
            suggestionId: "60b90aa0-bebe-4fc6-bb81-e5a698bfcaa3",
        },
        unknownBlobNames: [],
    },
    suggestionOrder: 1,
};
