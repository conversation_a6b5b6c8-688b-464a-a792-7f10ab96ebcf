export const request = {
    request: {
        blobs: {
            added: ["15b4c23f4801d3f018909802b2f9ebcf62073bdb42819cb9c2f53d795874af0e"],
            baselineCheckpointId:
                "89bb7ef2dafb424a8bda9f01be6cd4169fe76daca0f65b7c988369c2ba3d5a0d",
            deleted: ["5ff674c427aec57d9512ca8832475c67fad6b487162f2255d821421042238b9f"],
        },
        blockedLocations: [],
        diagnostics: [
            {
                location: {
                    lineEnd: 20,
                    lineStart: 20,
                    path: "/home/<USER>/augment/base/datasets/edit_test.py",
                },
                message: '"edit_pb2" is unknown import symbol',
                severity: "ERROR",
            },
            {
                location: {
                    lineEnd: 21,
                    lineStart: 21,
                    path: "/home/<USER>/augment/base/datasets/edit_test.py",
                },
                message: '"request_insight_pb2" is unknown import symbol',
                severity: "ERROR",
            },
            {
                location: {
                    lineEnd: 137,
                    lineStart: 99,
                    path: "/home/<USER>/augment/base/datasets/edit_test.py",
                },
                message: 'Argument missing for parameter "session_id"',
                severity: "ERROR",
            },
        ],
        editEvents: [
            {
                afterBlobName: "8903c531c17d5362828dbc9da5f17bcff3c90915fe8ae9cdfec3f8899c706edb",
                beforeBlobName: "5ff674c427aec57d9512ca8832475c67fad6b487162f2255d821421042238b9f",
                edits: [
                    {
                        afterStart: 3828,
                        afterText: "\n    ",
                        beforeStart: 3828,
                        beforeText: "",
                    },
                ],
                path: "base/datasets/edit.py",
            },
            {
                afterBlobName: "bab73b1f2991e253917d0139ea4094066182c7b03af7f21f4412352d8b08b65d",
                beforeBlobName: "8903c531c17d5362828dbc9da5f17bcff3c90915fe8ae9cdfec3f8899c706edb",
                edits: [
                    {
                        afterStart: 3829,
                        afterText: "",
                        beforeStart: 3829,
                        beforeText: "    ",
                    },
                    {
                        afterStart: 3829,
                        afterText: "\n    ",
                        beforeStart: 3833,
                        beforeText: "",
                    },
                ],
                path: "base/datasets/edit.py",
            },
            {
                afterBlobName: "15b4c23f4801d3f018909802b2f9ebcf62073bdb42819cb9c2f53d795874af0e",
                beforeBlobName: "bab73b1f2991e253917d0139ea4094066182c7b03af7f21f4412352d8b08b65d",
                edits: [
                    {
                        afterStart: 3834,
                        afterText:
                            'session_id: str\n    """The session ID of the user who made the edit."""',
                        beforeStart: 3834,
                        beforeText: "",
                    },
                ],
                path: "base/datasets/edit.py",
            },
        ],
        instruction: "",
        lang: "plaintext",
        mode: "BACKGROUND",
        modelName: "raven-edit-v4-15b",
        path: "unicode.py",
        prefix: "🤖\n",
        recentChanges: [
            {
                blobName: "15b4c23f4801d3f018909802b2f9ebcf62073bdb42819cb9c2f53d795874af0e",
                charEnd: 4378,
                charStart: 3354,
                expectedBlobName:
                    "15b4c23f4801d3f018909802b2f9ebcf62073bdb42819cb9c2f53d795874af0e",
                path: "base/datasets/edit.py",
                presentInBlob: true,
                timestamp: "2023-07-11T17:00:00.000Z",
                replacementText:
                    'ass\nclass EditResolution:\n    """Represents an edit resolution."""\n\n    is_accepted: bool\n    annotated_text: Optional[str] = None\n    annotated_instruction: Optional[str] = None\n    timestamp: Optional[datetime] = None\n\n\n@dataclass_json\n@dataclass\nclass EditDatum:\n    """Represents a full edit event with its request, response."""\n\n    request_id: str\n    """The request ID of the completion event."""\n\n    user_id: str\n    """The user ID of the user who made the edit."""\n\n    session_id: str\n    """The session ID of the user who made the edit."""\n\n    user_agent: str\n    """The user agent of the user who made the edit."""\n\n    request: EditRequest\n    """The edit request."""\n\n    response: EditResponse\n    """The edit response."""\n\n    resolution: Optional[EditResolution]\n    """The edit resolution."""\n\n    @property\n    def status(self) -> str:\n        """The resolution status of the edit: accepted, rejected, or unknown."""\n        if self.resolution is None:\n            return "unknown"\n        if self.resol',
            },
        ],
        requestId: "afff179a-1fbd-49d4-ac6a-121c06bb8e91",
        scope: "FILE",
        selectedText: "",
        selectionBeginChar: 3,
        selectionEndChar: 3,
        sequenceId: 14,
        suffix: "",
        vcsChange: {
            workingDirectoryChanges: [],
        },
    },
};

export const response = {
    generationId: "3ebee6d6-a832-4a7c-a6cc-0a7013b99d7a",
    result: {
        checkpointNotFound: false,
        suggestedEdit: {
            blobName: "21b5f08e18961e89c997fbd9f7b47d3192cb11c4f48ae04421717dd1d9214dc1",
            changeDescription: "",
            charEnd: 2,
            charStart: 0,
            diffSpans: [
                {
                    original: {
                        start: 0,
                        stop: 2,
                    },
                    updated: {
                        start: 0,
                        stop: 2,
                    },
                },
            ],
            editingScore: 0.7758784,
            editingScoreThreshold: 1,
            existingCode: "🤖\n",
            localizationScore: 0.95374084,
            path: "unicode.py",
            suggestedCode: "🤖\n",
            suggestionId: "ee362852-361b-42e3-9b2f-9beab652e5c2",
        },
        unknownBlobNames: [],
    },
    suggestionOrder: 0,
};
