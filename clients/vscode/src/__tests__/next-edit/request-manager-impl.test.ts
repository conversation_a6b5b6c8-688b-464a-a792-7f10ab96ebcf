import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";

import { QualifiedPathName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import {
    CancellationToken,
    contextKeys,
    ExtensionContext,
    getWorkspaceFolder,
    MutableTextDocument,
    publishWorkspaceFoldersChange,
    TextDocumentChangeEvent,
    TextEditor,
    TrackedDisposable,
    Uri,
    window,
    workspace,
} from "../../__mocks__/vscode-mocks";
import { APIServer, NextEditMode, NextEditScope, NextEditStreamRequest } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { DiagnosticsManager } from "../../diagnostics";
import { FeatureFlagManager } from "../../feature-flags";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";
import { NextEditSessionEventReporter } from "../../metrics/next-edit-session-event-reporter";
import * as nextEditApi from "../../next-edit/next-edit-api";
import {
    BlockedPathAndRange,
    ChangeType,
    type IEditSuggestion,
    type NextEditResultInfo,
    SuggestionState,
} from "../../next-edit/next-edit-types";
import { NextEditRequestManagerState } from "../../next-edit/request-manager";
import { NextEditRequestManager } from "../../next-edit/request-manager-impl";
import { EditSuggestion } from "../../next-edit/suggestion-manager";
import { SuggestionManagerImpl } from "../../next-edit/suggestion-manager-impl";
import { StateController } from "../../statusbar/state-controller";
import { StatusBarManager } from "../../statusbar/status-bar-manager";
import { EphemeralObservable } from "../../utils/ephemeral-flag";
import { LineRange } from "../../utils/ranges";
import { RecentItems } from "../../utils/recent-items";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitWorkspaceState,
    createSourceFolderFiles,
    defaultWorkspaceFolder,
    defaultWorkspaceState,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";
import { createSuggestion } from "./test-utils";

const _DEBOUNCE_MS = 5;

class NextEditAPITestKit extends WorkspaceManagerTestKit {
    static readonly workspaceFolder = defaultWorkspaceFolder;
    static readonly folderState = defaultWorkspaceState;

    public readonly ctx = new ExtensionContext();
    public readonly apiServer = new MockAPIServer();
    public readonly configListener: AugmentConfigListener;
    public workspaceManager: WorkspaceManager;
    public diagnosticsManager: DiagnosticsManager;
    public eventReporter: NextEditSessionEventReporter;
    public requestManager: NextEditRequestManager;
    public suggestionManager: SuggestionManagerImpl;
    public recentSuggestions: RecentItems<NextEditResultInfo>;
    public clientMetricsReporter: ClientMetricsReporter;
    public completionJustAccepted: EphemeralObservable<boolean>;

    static async create() {
        const kit = new NextEditAPITestKit();
        await kit.awaitStartup();
        return kit;
    }

    private constructor() {
        super();

        createSourceFolderFiles(NextEditAPITestKit.workspaceFolder);
        const workspaceFolders = [
            getWorkspaceFolder(NextEditAPITestKit.workspaceFolder.folderRootUri),
        ];
        publishWorkspaceFoldersChange(workspaceFolders);

        this.configListener = new AugmentConfigListener();
        this.addDisposable(this.configListener);

        this.eventReporter = new NextEditSessionEventReporter(this.apiServer);
        this.workspaceManager = this.createWorkspaceManager();
        this.addDisposable(this.workspaceManager);

        this.diagnosticsManager = new DiagnosticsManager();
        this.addDisposable(this.diagnosticsManager);

        this.suggestionManager = new SuggestionManagerImpl(
            this.workspaceManager,
            this.eventReporter
        );
        this.addDisposable(this.suggestionManager);
        this.recentSuggestions = new RecentItems<NextEditResultInfo>(10);
        this.clientMetricsReporter = new ClientMetricsReporter(this.apiServer);
        this.completionJustAccepted = this.addDisposable(new EphemeralObservable());

        this.configListener.config.nextEdit.useDebounceMs = _DEBOUNCE_MS;

        const statusBarManager = this.addDisposable(new StatusBarManager());
        const featureFlagManager = this.addDisposable(new FeatureFlagManager());

        this.requestManager = new NextEditRequestManager(
            this.apiServer,
            this.configListener,
            this.workspaceManager,
            this.diagnosticsManager,
            this.eventReporter,
            this.clientMetricsReporter,
            this.blobNameCalculator,
            this.suggestionManager,
            this.recentSuggestions,
            new StateController(statusBarManager),
            this.completionJustAccepted,
            featureFlagManager
        );
        this.addDisposable(this.requestManager);
    }

    public get folderRootUri(): Uri {
        return NextEditAPITestKit.workspaceFolder.folderRootUri;
    }

    public async awaitStartup() {
        await awaitWorkspaceState(this.workspaceManager, NextEditAPITestKit.folderState);
    }

    public getQualifiedPathName(relPath: string) {
        return new QualifiedPathName(this.folderRootUri.path, relPath);
    }
}

type StreamResult = { status: APIStatus; suggestion?: IEditSuggestion };

describe("NextEditRequestManager", () => {
    let kit: NextEditAPITestKit;
    let doc: MutableTextDocument;
    let otherDoc: MutableTextDocument;
    let result: StreamResult[];
    let cancelTokens: CancellationToken[];

    async function* mockEditStream(
        _request: NextEditStreamRequest,
        _workspaceManager: WorkspaceManager,
        _diagnosticsManager: DiagnosticsManager,
        _apiServer: APIServer,
        _blobNameCalculator: BlobNameCalculator,
        _configListener: AugmentConfigListener,
        cancelToken: any
    ): AsyncGenerator<StreamResult> {
        cancelTokens.push(cancelToken);
        await delayMs(1);
        for (const r of result) {
            // Simulate API errors by throwing them.
            if (r.status === APIStatus.unknown) {
                throw new Error();
            }
            yield r;
            await delayMs(1);
        }
    }

    beforeEach(async () => {
        jest.useFakeTimers()
            // Mock the current time; we log timestamps in the responses.
            .setSystemTime(new Date("2024-01-01"));

        kit = await NextEditAPITestKit.create();

        // Set an active editor
        const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
        doc = new MutableTextDocument(uri, "line 0\nline 1\nline 2\nline 3\nline 4\n");
        otherDoc = new MutableTextDocument(
            Uri.joinPath(kit.folderRootUri, "/example/b"),
            "line 0\nline 1\nline 2\nline 3\nline 4\n"
        );
        const editor = new TextEditor(doc);
        window.activeTextEditor = editor;
        workspace.textDocuments = [doc, otherDoc];
        result = [];
        cancelTokens = [];

        jest.spyOn(nextEditApi, "queryNextEditStream").mockImplementation(mockEditStream);
    });

    afterEach(() => {
        jest.useRealTimers();
        kit.dispose();
        TrackedDisposable.assertDisposed();
    });

    describe("enqueueRequest", () => {
        it("updates state correctly", async () => {
            let handleStateChanged = jest.fn();
            kit.requestManager.state.listen(handleStateChanged, true);

            expect(contextKeys["vscode-augment.nextEdit.loading"]).toBeFalsy();

            expect(handleStateChanged).toHaveBeenCalledTimes(1);
            expect(handleStateChanged).toHaveBeenCalledWith(
                NextEditRequestManagerState.ready,
                NextEditRequestManagerState.ready
            );
            handleStateChanged.mockClear();

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            expect(handleStateChanged).toHaveBeenCalledTimes(2);
            expect(handleStateChanged).toHaveBeenNthCalledWith(
                1,
                NextEditRequestManagerState.pending,
                NextEditRequestManagerState.ready
            );
            expect(handleStateChanged).toHaveBeenNthCalledWith(
                2,
                NextEditRequestManagerState.inflight,
                NextEditRequestManagerState.pending
            );
            expect(contextKeys["vscode-augment.nextEdit.loading"]).toBeTruthy();
            handleStateChanged.mockClear();

            await jest.advanceTimersByTimeAsync(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
            expect(handleStateChanged).toHaveBeenCalledTimes(1);
            expect(handleStateChanged).toHaveBeenCalledWith(
                NextEditRequestManagerState.ready,
                NextEditRequestManagerState.inflight
            );
            expect(contextKeys["vscode-augment.nextEdit.loading"]).toBeFalsy();
        });

        it("updates state correctly in the presence of errors", async () => {
            let handleStateChanged = jest.fn();
            kit.requestManager.state.listen(handleStateChanged);

            result = [
                {
                    status: APIStatus.unknown,
                },
            ];

            expect(contextKeys["vscode-augment.nextEdit.loading"]).toBeFalsy();
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            expect(handleStateChanged).toHaveBeenCalledTimes(2);
            expect(handleStateChanged).toHaveBeenNthCalledWith(
                1,
                NextEditRequestManagerState.pending,
                NextEditRequestManagerState.ready
            );
            expect(handleStateChanged).toHaveBeenNthCalledWith(
                2,
                NextEditRequestManagerState.inflight,
                NextEditRequestManagerState.pending
            );
            expect(contextKeys["vscode-augment.nextEdit.loading"]).toBeTruthy();
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
            handleStateChanged.mockClear();
            jest.mocked(nextEditApi.queryNextEditStream).mockClear();

            await jest.advanceTimersByTimeAsync(1);
            expect(handleStateChanged).toHaveBeenCalledTimes(1);
            expect(handleStateChanged).toHaveBeenCalledWith(
                NextEditRequestManagerState.ready,
                NextEditRequestManagerState.inflight
            );
            expect(contextKeys["vscode-augment.nextEdit.loading"]).toBeFalsy();
        });

        it("FOREGROUND requests yield to equivalent inflight requests", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            // After enqueueing another request, we don't call the API again and we don't
            // enqueue a request.
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            // After the first request completes, the queue is empty and we are in a
            // ready state.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(cancelTokens).toHaveLength(1);
            expect(cancelTokens[0].isCancellationRequested).toBe(false);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        });

        it("FOREGROUND requests preempt all non-equivalent inflight and pending requests", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/b"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            // After enqueueing another request, we should cancel the first request,
            // and call the API again, with nothing in the queue.
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(2);
            expect(cancelTokens).toHaveLength(2);
            expect(cancelTokens[0].isCancellationRequested).toBe(true);

            // And after waiting, the queue should be empty.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(cancelTokens[1].isCancellationRequested).toBe(false);
        });

        it("FOREGROUND requests preempt BACKGROUND requests", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(0);
            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/b"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            // After enqueueing another request, we should cancel the first request,
            // and call the API again, with nothing in the queue.
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(2);
            expect(cancelTokens).toHaveLength(2);
            expect(cancelTokens[0].isCancellationRequested).toBe(true);

            // And after waiting, the queue should be empty.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(cancelTokens[1].isCancellationRequested).toBe(false);
        });

        it("BACKGROUND requests enqueue behind pending FOREGROUND requests", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.Workspace
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            // And after waiting, the first query will be complete and we'll start
            // processing the second.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(2);

            // And after waiting, the first query will be complete and we'll start
            // processing the second.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(2);
            expect(cancelTokens).toHaveLength(2);
            expect(cancelTokens[0].isCancellationRequested).toBe(false);
            expect(cancelTokens[1].isCancellationRequested).toBe(false);
        });

        it("BACKGROUND requests enqueue behind BACKGROUND requests from same file", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.Workspace
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            // And after waiting, the first query will be complete and we'll start
            // processing the second.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(2);

            // And after waiting, the first query will be complete and we'll start
            // processing the second.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(2);
            expect(cancelTokens).toHaveLength(2);
            expect(cancelTokens[0].isCancellationRequested).toBe(false);
            expect(cancelTokens[1].isCancellationRequested).toBe(false);
        });

        it("BACKGROUND requests yield to equivalent BACKGROUND requests from same file", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.Workspace
            );
            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            for (const _ of [1, 2, 3]) {
                kit.requestManager.enqueueRequest(
                    kit.getQualifiedPathName("example/a"),
                    NextEditMode.Background,
                    NextEditScope.File
                );
                kit.requestManager.enqueueRequest(
                    kit.getQualifiedPathName("example/a"),
                    NextEditMode.Background,
                    NextEditScope.Workspace
                );
            }
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);

            // And after waiting, the first query will be complete and we'll start
            // processing the second.
            await jest.advanceTimersByTimeAsync(1);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(2);
        });

        it("BACKGROUND requests preempt BACKGROUND requests from different files", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(0);

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/b"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(0);

            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        });
    });

    describe("debouncing behavior", () => {
        it("does not debounce for FOREGROUND requests", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        });

        it("debounces for BACKGROUND requests", async () => {
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(0);

            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(false);
            expect(kit.requestManager.pendingQueueLength).toBe(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(0);

            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS);
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        });

        it("does not debounce for BACKGROUND requests immediately after a completion", async () => {
            kit.completionJustAccepted.set(true);
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        });

        it("does not debounce for BACKGROUND requests immediately after a suggestion", async () => {
            kit.suggestionManager.suggestionWasJustAccepted.set(true);
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);
            expect(kit.requestManager.pendingQueueLength).toBe(0);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        });
    });

    it("cancels ongoing requests when the document changes", async () => {
        jest.spyOn(nextEditApi, "queryNextEditStream").mockImplementation(mockEditStream);
        kit.requestManager.enqueueRequest(
            kit.getQualifiedPathName("example/a"),
            NextEditMode.Foreground,
            NextEditScope.File
        );
        expect(kit.requestManager.hasInflightRequest).toBe(true);
        expect(kit.requestManager.pendingQueueLength).toBe(0);
        expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        workspace.textDocumentChanged.fire(doc.replace(0, 1, "x"));
        await jest.advanceTimersByTimeAsync(1);
        expect(cancelTokens[0].isCancellationRequested).toBe(true);
    });

    it("ignores document changes in unrelated documents", async () => {
        jest.spyOn(nextEditApi, "queryNextEditStream").mockImplementation(mockEditStream);
        kit.requestManager.enqueueRequest(
            kit.getQualifiedPathName("example/a"),
            NextEditMode.Foreground,
            NextEditScope.File
        );
        expect(kit.requestManager.hasInflightRequest).toBe(true);
        expect(kit.requestManager.pendingQueueLength).toBe(0);
        expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
        workspace.textDocumentChanged.fire(
            new TextDocumentChangeEvent(new MutableTextDocument(Uri.file("example/b"), "x"), [])
        );
        await jest.advanceTimersByTimeAsync(1);
        expect(cancelTokens[0].isCancellationRequested).toBe(false);
    });

    test.each([
        {
            scope: NextEditScope.Workspace,
            expected: [1, 1, 1, 2],
        },
        {
            scope: NextEditScope.File,
            expected: [1, 2, 2, 3],
        },
    ])("ignores equivalent BACKGROUND %o requests", async ({ scope, expected }) => {
        result = [
            {
                status: APIStatus.ok,
                suggestion: {
                    requestId: "1",
                    mode: NextEditMode.Background,
                    scope,
                    result: {
                        suggestionId: "suggestion-1",
                        path: "example/a",
                        blobName: "",
                        charStart: 0,
                        charEnd: 0,
                        existingCode: "",
                        suggestedCode: "line -1\n",
                        truncationChar: undefined,
                        changeDescription: "",
                        diffSpans: [],
                        editingScore: 0,
                        localizationScore: 0,
                        editingScoreThreshold: 1.0,
                    },
                    qualifiedPathName: kit.getQualifiedPathName("example/a"),
                    lineRange: {
                        start: 0,
                        stop: 0,
                    },
                    uriScheme: "file",
                    occurredAt: new Date(),
                    state: SuggestionState.fresh,
                    changeType: ChangeType.insertion,
                },
            },
        ];

        kit.requestManager.enqueueRequest(
            kit.getQualifiedPathName("example/a"),
            NextEditMode.Background,
            scope
        );
        await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS + 2);
        expect(kit.requestManager.hasInflightRequest).toBe(false);
        expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(expected[0]);

        kit.requestManager.enqueueRequest(
            kit.getQualifiedPathName("example/b"),
            NextEditMode.Background,
            scope
        );
        await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS + 2);
        expect(kit.requestManager.hasInflightRequest).toBe(false);
        expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(expected[1]);

        kit.requestManager.enqueueRequest(
            kit.getQualifiedPathName("example/b"),
            NextEditMode.Background,
            scope
        );
        await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS + 2);
        expect(kit.requestManager.hasInflightRequest).toBe(false);
        expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(expected[2]);

        workspace.textDocumentChanged.fire(doc.replace(0, 1, "x"));

        kit.requestManager.enqueueRequest(
            kit.getQualifiedPathName("example/b"),
            NextEditMode.Background,
            scope
        );
        await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS + 2);
        expect(kit.requestManager.hasInflightRequest).toBe(false);
        expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(expected[3]);
    });

    describe("blockedLocations", () => {
        let suggestions: EditSuggestion[];
        beforeEach(() => {
            suggestions = [
                createSuggestion(
                    doc,
                    kit.getQualifiedPathName("example/a"),
                    new LineRange(0, 1),
                    "modified line 0"
                ),
                createSuggestion(
                    doc,
                    kit.getQualifiedPathName("example/a"),
                    // This is a noop suggestion.
                    new LineRange(1, 2),
                    "line 1"
                ),
                createSuggestion(
                    doc,
                    kit.getQualifiedPathName("example/a"),
                    new LineRange(2, 3),
                    "modified line 2"
                ),
                createSuggestion(
                    doc,
                    kit.getQualifiedPathName("example/b"),
                    new LineRange(0, 1),
                    "modified line 0"
                ),
                createSuggestion(
                    doc,
                    kit.getQualifiedPathName("example/b"),
                    new LineRange(2, 3),
                    "modified line 2"
                ),
            ];
        });

        it("for FILE requests only includes suggestions in same file", async () => {
            kit.suggestionManager.add(suggestions);
            kit.suggestionManager.reject([suggestions[2], suggestions[4]]);
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS + 2);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledWith(
                expect.objectContaining({
                    // Only include rejected suggestions in the current file.
                    blockedLocations: [suggestions[2]].map(
                        (suggestion) =>
                            new BlockedPathAndRange(
                                suggestion.qualifiedPathName.relPath,
                                suggestion.lineRange,
                                suggestion.result.charStart,
                                suggestion.result.charEnd
                            )
                    ),
                }),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything()
            );
        });

        it("for WORKSPACE requests only includes blocked suggestions", async () => {
            kit.suggestionManager.add(suggestions);
            kit.suggestionManager.reject([suggestions[2], suggestions[4]]);
            kit.requestManager.enqueueRequest(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.Workspace
            );
            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS + 2);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledTimes(1);
            expect(nextEditApi.queryNextEditStream).toHaveBeenCalledWith(
                expect.objectContaining({
                    // Include all rejected suggestions.
                    blockedLocations: [suggestions[2], suggestions[4]].map(
                        (suggestion) =>
                            new BlockedPathAndRange(
                                suggestion.qualifiedPathName.relPath,
                                suggestion.lineRange,
                                suggestion.result.charStart,
                                suggestion.result.charEnd
                            )
                    ),
                }),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything(),
                expect.anything()
            );
        });
    });

    describe("shouldNotEnqueueRequestReason", () => {
        it.skip("returns reason when file request has no path", () => {
            const reason = kit.requestManager.shouldNotEnqueueRequestReason(
                undefined,
                NextEditMode.Background,
                NextEditScope.File
            );
            expect(reason).toBe(`Skipping request for ${NextEditScope.File} without a file.`);
        });

        it("returns reason for duplicate background workspace request", async () => {
            result = [
                {
                    status: APIStatus.ok,
                    suggestion: {
                        requestId: "1",
                        mode: NextEditMode.Background,
                        scope: NextEditScope.Workspace,
                        result: {
                            suggestionId: "suggestion-1",
                            path: "example/a",
                            blobName: "",
                            charStart: 0,
                            charEnd: 0,
                            existingCode: "",
                            suggestedCode: "line -1\n",
                            truncationChar: undefined,
                            changeDescription: "",
                            diffSpans: [],
                            editingScore: 0,
                            localizationScore: 0,
                            editingScoreThreshold: 1.0,
                        },
                        qualifiedPathName: kit.getQualifiedPathName("example/a"),
                        lineRange: {
                            start: 0,
                            stop: 0,
                        },
                        uriScheme: "file",
                        occurredAt: new Date(),
                        state: SuggestionState.fresh,
                        changeType: ChangeType.insertion,
                    },
                },
            ];
            // Make the request to set the state
            kit.requestManager.enqueueRequest(
                undefined,
                NextEditMode.Background,
                NextEditScope.Workspace
            );
            await jest.advanceTimersByTimeAsync(_DEBOUNCE_MS + 2);

            // Second request should be blocked
            const reason = kit.requestManager.shouldNotEnqueueRequestReason(
                undefined,
                NextEditMode.Background,
                NextEditScope.Workspace
            );
            expect(reason).toMatch(
                /Skipping BACKGROUND\/WORKSPACE request at .+ because it is subsumed by .+, which was recently completed./
            );
        });

        it("returns reason when request is subsumed by inflight request", async () => {
            result = [
                {
                    status: APIStatus.ok,
                    suggestion: {
                        requestId: "1",
                        mode: NextEditMode.Background,
                        scope: NextEditScope.Workspace,
                        result: {
                            suggestionId: "suggestion-1",
                            path: "example/a",
                            blobName: "",
                            charStart: 0,
                            charEnd: 0,
                            existingCode: "",
                            suggestedCode: "line -1\n",
                            truncationChar: undefined,
                            changeDescription: "",
                            diffSpans: [],
                            editingScore: 0,
                            localizationScore: 0,
                            editingScoreThreshold: 1.0,
                        },
                        qualifiedPathName: kit.getQualifiedPathName("example/a"),
                        lineRange: {
                            start: 0,
                            stop: 0,
                        },
                        uriScheme: "file",
                        occurredAt: new Date(),
                        state: SuggestionState.fresh,
                        changeType: ChangeType.insertion,
                    },
                },
            ];

            const path = kit.getQualifiedPathName("example/a");

            // Start a foreground request
            kit.requestManager.enqueueRequest(
                path,
                NextEditMode.Foreground,
                NextEditScope.Workspace
            );
            expect(kit.requestManager.hasInflightRequest).toBe(true);

            // Try to enqueue a background request for the same file
            const reason = kit.requestManager.shouldNotEnqueueRequestReason(
                path,
                NextEditMode.Foreground,
                NextEditScope.Workspace
            );

            expect(reason).toMatch(
                /Skipping FOREGROUND\/WORKSPACE request because it is subsumed by inflight request .+/
            );
        });

        it("returns reason when request is subsumed by recently completed request", async () => {
            const path = kit.getQualifiedPathName("example/a");
            const selection = new LineRange(0, 1);

            // Add a completed request to _freshCompletedRequests
            const completedRequest = {
                id: "test-id",
                qualifiedPathName: path,
                mode: NextEditMode.Background,
                scope: NextEditScope.File,
                selection,
                enqueuedAt: Date.now(),
                requestBlobName: "",
            };
            (kit.requestManager as any)._freshCompletedRequests = [completedRequest];

            // Try to enqueue a similar request
            const reason = kit.requestManager.shouldNotEnqueueRequestReason(
                path,
                NextEditMode.Background,
                NextEditScope.File,
                selection
            );

            expect(reason).toMatch(
                /Skipping BACKGROUND\/FILE request at .+ because it is subsumed by .+, which was recently completed./
            );
        });

        it("returns undefined for valid new request", () => {
            const reason = kit.requestManager.shouldNotEnqueueRequestReason(
                kit.getQualifiedPathName("example/a"),
                NextEditMode.Foreground,
                NextEditScope.File
            );
            expect(reason).toBeUndefined();
        });
    });
});
