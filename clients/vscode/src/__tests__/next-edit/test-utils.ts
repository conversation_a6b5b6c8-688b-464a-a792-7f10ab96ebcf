// Convenience methods to create objects for tests.
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { v4 as uuidv4 } from "uuid";

import {
    MutableTextDocument,
    MutableTextDocumentChangeBuilder,
    Position,
    Range,
    TextDocument,
    TextDocumentChangeEvent,
    Uri,
} from "../../__mocks__/vscode-mocks";
import { NextEditMode, NextEditResult, NextEditScope } from "../../next-edit/next-edit-types";
import { EditSuggestion } from "../../next-edit/suggestion-manager";
import { CharRange, LineRange } from "../../utils/ranges";

function _toCharRange(doc: TextDocument, lineRange: LineRange) {
    return new CharRange(
        doc.offsetAt(new Position(lineRange.start, 0)),
        doc.offsetAt(new Position(lineRange.stop, 0))
    );
}

/** Mock document for all the below tests. */
export function createDoc(path: QualifiedPathName, text: string) {
    return new MutableTextDocument(Uri.parse(`file://${path.absPath}`), text);
}

/** Helper to create a suggestion for a document, original range and new text. */
export function createSuggestion(
    doc: TextDocument,
    qualifiedPathName: QualifiedPathName,
    originalLineRange: LineRange,
    suggestedCode: string,
    options: Partial<{
        requestId: string;
        mode: NextEditMode;
        scope: NextEditScope;
    }> = {}
) {
    const documentText = doc.getText();
    const charRange = _toCharRange(doc, originalLineRange);

    return new EditSuggestion(
        options.requestId ?? uuidv4(),
        options.mode ?? NextEditMode.Foreground,
        options.scope ?? NextEditScope.File,
        {
            suggestionId: uuidv4(),
            path: qualifiedPathName.relPath,
            blobName: "",
            charStart: charRange.start,
            charEnd: charRange.stop,
            existingCode: documentText.slice(charRange.start, charRange.stop),
            suggestedCode,
            changeDescription: "diff description",
            diffSpans: [
                {
                    original: {
                        start: charRange.start,
                        stop: charRange.stop,
                    },
                    updated: {
                        start: charRange.start,
                        stop: charRange.start + suggestedCode.length,
                    },
                },
            ],
            editingScore: 0,
            localizationScore: 0,
            editingScoreThreshold: 1.0,
        } as NextEditResult,
        qualifiedPathName,
        originalLineRange
    );
}

/**
 * Helper to create a text change event from a list of range, replacement pairs.
 *
 * This function also modifies the document to reflect the changes, simulating
 * what we'd see in VSCode.
 */
export function createEvent(
    doc: MutableTextDocument,
    rangeText: [originalRange: Range, replacementText: string][]
): TextDocumentChangeEvent {
    // TODO(arun): The actual change events returned by applying changes from
    // MutableTextDocumentChangeBuilder don't seem to agree with VSCode's, and
    // aren't entirely intuitive. For now, we only use the builder to apply
    // the changes, and create the events ourselves.
    const event = new TextDocumentChangeEvent(
        doc,
        rangeText.map(([range, newText]) => {
            return {
                range: range,
                rangeLength: doc.offsetAt(range.end) - doc.offsetAt(range.start),
                rangeOffset: doc.offsetAt(range.start),
                text: newText,
            };
        })
    );

    const builder = new MutableTextDocumentChangeBuilder();
    let offsetDelta = 0;
    for (const change of event.contentChanges) {
        builder.replace(change.rangeOffset + offsetDelta, change.rangeLength, change.text);
        offsetDelta += change.text.length - change.rangeLength;
    }
    doc.applyChanges(builder.build());
    return event;
}

export function createEventForSuggestions(
    doc: MutableTextDocument,
    suggestions: EditSuggestion[]
): TextDocumentChangeEvent {
    return createEvent(
        doc,
        suggestions.map(
            (s) =>
                [
                    new Range(s.lineRange.start, 0, s.lineRange.stop, 0),
                    s.result.suggestedCode,
                ] as const
        )
    );
}
