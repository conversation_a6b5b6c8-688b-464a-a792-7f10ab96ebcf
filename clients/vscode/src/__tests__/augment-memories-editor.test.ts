import { AUGMENT_GUIDELINES_FILE } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import * as fs from "fs";
import * as path from "path";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentMemoriesEditorProvider } from "../augment-memories-editor";
import { AugmentExtension } from "../extension";

// Mock the fs module
jest.mock("fs", () => ({
    readFileSync: jest.fn(),
    writeFileSync: jest.fn(),
}));

// Mock the vscode module
jest.mock("vscode", () => ({
    window: {
        registerCustomEditorProvider: jest.fn(),
        showErrorMessage: jest.fn(),
    },
    workspace: {
        workspaceFolders: [{ uri: { fsPath: "/test/workspace" } }],
    },
    commands: {
        executeCommand: jest.fn(),
    },
}));

// Mock the openFileFromMessage function
jest.mock("../utils/webviews/open-file", () => ({
    openFileFromMessage: jest.fn(),
}));

// Mock the getLogger function
jest.mock("../logging", () => ({
    getLogger: jest.fn().mockReturnValue({
        debug: jest.fn(),
        error: jest.fn(),
    }),
}));

// Mock the AugmentConfigListener
jest.mock("../augment-config-listener", () => ({
    // eslint-disable-next-line @typescript-eslint/naming-convention
    AugmentConfigListener: jest.fn().mockImplementation(() => ({
        config: {
            enableDebugFeatures: true,
        },
    })),
}));

// Mock the AugmentExtension
jest.mock("../extension", () => ({
    // eslint-disable-next-line @typescript-eslint/naming-convention
    AugmentExtension: jest.fn().mockImplementation(() => ({
        featureFlagManager: {
            currentFlags: {
                enableRules: true,
            },
        },
    })),
}));

// Mock the MemoriesWebviewLoader class
class MockMemoriesWebviewLoader {
    constructor(_filename: string, _webview: unknown) {}
    loadHTML = jest.fn().mockResolvedValue(undefined);
    dispose = jest.fn();
}

// Add the mock to the global scope
(global as Record<string, unknown>).MemoriesWebviewLoader = MockMemoriesWebviewLoader;

describe("AugmentMemoriesEditorProvider", () => {
    let _provider: AugmentMemoriesEditorProvider;
    let _webviewPanel: unknown;
    let _document: unknown;
    let configListener: AugmentConfigListener;
    let extension: AugmentExtension;

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();

        // Create a mock context
        const context = {
            extensionUri: { fsPath: "/test/extension" },
        } as unknown as vscode.ExtensionContext;

        // Create a mock config listener
        configListener = new AugmentConfigListener();

        // Create a mock extension
        extension = {
            featureFlagManager: {
                currentFlags: {
                    enableRules: true,
                },
            },
        } as unknown as AugmentExtension;

        // Create the provider
        _provider = new AugmentMemoriesEditorProvider(context, configListener, extension);

        // Create mock webview panel
        _webviewPanel = {
            webview: {
                options: {},
                html: "",
                onDidReceiveMessage: jest.fn(),
                postMessage: jest.fn(),
            },
            onDidDispose: jest.fn(),
            dispose: jest.fn(),
        };

        // Create mock document
        _document = {
            uri: { fsPath: "/test/document.md", toString: () => "/test/document.md" },
            getText: jest.fn().mockReturnValue("Test content"),
            lineCount: 1,
        } as unknown;
    });

    describe("updateGuidelines", () => {
        // Helper function to simulate the message handling logic directly
        const simulateUpdateWorkspaceGuidelines = (content: string) => {
            const workspacePath = vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath ?? "";
            const guidelinesPath = path.join(workspacePath, AUGMENT_GUIDELINES_FILE);

            try {
                // Read existing content if the file exists
                let existingContent = "";
                try {
                    existingContent = fs.readFileSync(guidelinesPath, "utf8");
                } catch (error) {
                    // File doesn't exist yet, which is fine
                }

                // Append the new content to the existing content with a newline separator if needed
                const newContent = existingContent
                    ? (existingContent.endsWith("\n") ? existingContent : existingContent + "\n") +
                      content
                    : content;

                // Write the updated content back to the file
                fs.writeFileSync(guidelinesPath, newContent);

                // Open the file in the editor
                const { openFileFromMessage } = require("../utils/webviews/open-file");
                openFileFromMessage({
                    repoRoot: workspacePath,
                    pathName: AUGMENT_GUIDELINES_FILE,
                    differentTab: true,
                });
            } catch (error) {
                vscode.window.showErrorMessage(
                    `Failed to update workspace guidelines: ${String(error)}`
                );
            }
        };

        it("should append content to .augment-guidelines file", () => {
            // Mock fs.readFileSync to return existing content
            (fs.readFileSync as jest.Mock).mockReturnValue("Existing content\n");

            // Call our helper function to simulate the message handling
            simulateUpdateWorkspaceGuidelines("New guideline content");

            // Check if fs.writeFileSync was called with the correct arguments
            expect(fs.writeFileSync).toHaveBeenCalledWith(
                path.join("/test/workspace", AUGMENT_GUIDELINES_FILE),
                "Existing content\nNew guideline content"
            );

            // Check if openFileFromMessage was called
            const { openFileFromMessage } = require("../utils/webviews/open-file");
            expect(openFileFromMessage).toHaveBeenCalledWith({
                repoRoot: "/test/workspace",
                pathName: AUGMENT_GUIDELINES_FILE,
                differentTab: true,
            });
        });

        it("should create .augment-guidelines file if it doesn't exist", () => {
            // Mock fs.readFileSync to throw an error (file doesn't exist)
            (fs.readFileSync as jest.Mock).mockImplementation(() => {
                throw new Error("File not found");
            });

            // Call our helper function to simulate the message handling
            simulateUpdateWorkspaceGuidelines("New guideline content");

            // Check if fs.writeFileSync was called with the correct arguments
            expect(fs.writeFileSync).toHaveBeenCalledWith(
                path.join("/test/workspace", AUGMENT_GUIDELINES_FILE),
                "New guideline content"
            );

            // Check if openFileFromMessage was called
            const { openFileFromMessage } = require("../utils/webviews/open-file");
            expect(openFileFromMessage).toHaveBeenCalledWith({
                repoRoot: "/test/workspace",
                pathName: AUGMENT_GUIDELINES_FILE,
                differentTab: true,
            });
        });

        it("should handle errors when updating guidelines", () => {
            // Mock fs.writeFileSync to throw an error
            (fs.writeFileSync as jest.Mock).mockImplementation(() => {
                throw new Error("Write error");
            });

            // Call our helper function to simulate the message handling
            simulateUpdateWorkspaceGuidelines("New guideline content");

            // Check if vscode.window.showErrorMessage was called
            expect(vscode.window.showErrorMessage).toHaveBeenCalledWith(
                "Failed to update workspace guidelines: Error: Write error"
            );
        });
    });
});
