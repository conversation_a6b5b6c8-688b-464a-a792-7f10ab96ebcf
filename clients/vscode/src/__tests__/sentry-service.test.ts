import { AuthSessionStore } from "../auth/auth-session-store";
import { FeatureFlagManager } from "../feature-flags";
import { SentryService, SentryWebviewConfig } from "../sentry-service";
import { getExtensionVersion } from "../utils/environment";

// Mock dependencies
jest.mock("../utils/environment");
jest.mock("../logging", () => ({
    getLogger: jest.fn(() => ({
        warn: jest.fn(),
        error: jest.fn(),
        info: jest.fn(),
        debug: jest.fn(),
    })),
}));

const mockGetExtensionVersion = getExtensionVersion as jest.MockedFunction<
    typeof getExtensionVersion
>;

describe("SentryService", () => {
    let mockFeatureFlagManager: jest.Mocked<FeatureFlagManager>;
    let mockAuthSessionStore: jest.Mocked<AuthSessionStore>;
    let sentryService: SentryService;
    let mockCurrentFlags: any;

    beforeEach(() => {
        // Reset singleton instance
        (SentryService as any).instance = undefined;

        mockCurrentFlags = {
            enableSentry: true,
            webviewErrorSamplingRate: 1.0,
            webviewTraceSamplingRate: 1.0,
        };

        mockFeatureFlagManager = {
            get currentFlags() {
                return mockCurrentFlags;
            },
        } as any;

        mockAuthSessionStore = {
            getSession: jest.fn(),
        } as any;

        mockGetExtensionVersion.mockReturnValue("1.2.3");

        sentryService = SentryService.getInstance(mockFeatureFlagManager, mockAuthSessionStore);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("getInstance", () => {
        it("should return the same instance when called multiple times", () => {
            const instance1 = SentryService.getInstance(
                mockFeatureFlagManager,
                mockAuthSessionStore
            );
            const instance2 = SentryService.getInstance(
                mockFeatureFlagManager,
                mockAuthSessionStore
            );
            expect(instance1).toBe(instance2);
        });
    });

    describe("createSentryConfigForWebview", () => {
        it("should return disabled config when enableSentry is false", async () => {
            mockCurrentFlags.enableSentry = false;

            const result = await sentryService.createSentryConfigForWebview();
            const config = JSON.parse(result);

            expect(config).toEqual({ enabled: false });
        });

        it("should return enabled config when enableSentry is true", async () => {
            mockAuthSessionStore.getSession.mockResolvedValue({
                tenantURL: "https://test-tenant.augmentcode.com",
            } as any);

            const result = await sentryService.createSentryConfigForWebview();
            const config: SentryWebviewConfig = JSON.parse(result);

            expect(config.enabled).toBe(true);
            expect(config.dsn).toBe(
                "https://<EMAIL>/4509753348718592"
            );
            expect(config.release).toBe("vscode-webview@1.2.3");
            expect(config.environment).toBe("production");
            expect(config.errorSampleRate).toBe(1);
            expect(config.tracesSampleRate).toBe(1);
            expect(config.replaysSessionSampleRate).toBe(0.0);
            expect(config.replaysOnErrorSampleRate).toBe(0.0);
            expect(config.sendDefaultPii).toBe(false);
            expect(config.tags).toEqual({
                webview: "true",
                extensionVersion: "1.2.3",
                tenantVersion: "test-tenant",
            });
        });

        it("should handle staging environment correctly", async () => {
            mockAuthSessionStore.getSession.mockResolvedValue({
                tenantURL: "https://staging-tenant.augmentcode.com",
            } as any);

            const result = await sentryService.createSentryConfigForWebview();
            const config: SentryWebviewConfig = JSON.parse(result);

            expect(config.environment).toBe("staging");
            expect(config.tags.tenantVersion).toBe("staging-tenant");
        });

        it("should handle prerelease environment correctly", async () => {
            mockGetExtensionVersion.mockReturnValue("1.2.0");
            mockAuthSessionStore.getSession.mockResolvedValue({
                tenantURL: "https://test-tenant.augmentcode.com",
            } as any);

            const result = await sentryService.createSentryConfigForWebview();
            const config: SentryWebviewConfig = JSON.parse(result);

            expect(config.environment).toBe("prerelease");
            expect(config.release).toBe("vscode-webview@1.2.0");
        });

        it("should handle missing session gracefully", async () => {
            mockAuthSessionStore.getSession.mockResolvedValue(null);

            const result = await sentryService.createSentryConfigForWebview();
            const config: SentryWebviewConfig = JSON.parse(result);

            expect(config.enabled).toBe(true);
            expect(config.tags.tenantVersion).toBe("unknown");
        });

        it("should handle session store errors gracefully", async () => {
            mockAuthSessionStore.getSession.mockRejectedValue(new Error("Session error"));

            const result = await sentryService.createSentryConfigForWebview();
            const config: SentryWebviewConfig = JSON.parse(result);

            expect(config.enabled).toBe(true);
            expect(config.tags.tenantVersion).toBe("unknown");
        });

        it("should handle missing tenantURL gracefully", async () => {
            mockAuthSessionStore.getSession.mockResolvedValue({
                tenantURL: undefined,
            } as any);

            const result = await sentryService.createSentryConfigForWebview();
            const config: SentryWebviewConfig = JSON.parse(result);

            expect(config.enabled).toBe(true);
            expect(config.tags.tenantVersion).toBe("unknown");
        });
        it("should handle invalid sampling rates gracefully", async () => {
            mockCurrentFlags.webviewErrorSamplingRate = -1;
            mockCurrentFlags.webviewTraceSamplingRate = 2.0;

            const result = await sentryService.createSentryConfigForWebview();
            const config = JSON.parse(result);

            expect(config.errorSampleRate).toBeGreaterThanOrEqual(0);
            expect(config.tracesSampleRate).toBeLessThanOrEqual(1);
        });
    });
});
