import { ExtensionContext } from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AuthSessionStore } from "../../auth/auth-session-store";

describe("AuthSessionStore", () => {
    test("should update with session event", async () => {
        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        expect(authSession.isLoggedIn).toBe(undefined);

        const sessionListener = jest.fn();
        const readyListener = jest.fn();
        authSession.onDidChangeSession(sessionListener);
        expect(sessionListener).not.toHaveBeenCalled();
        authSession.onReady(readyListener);
        expect(readyListener).not.toHaveBeenCalled();

        await authSession.ready;
        expect(authSession.isLoggedIn).toBe(false);
        expect(readyListener).toHaveBeenCalledTimes(1);
        expect(sessionListener).not.toHaveBeenCalled();
    });
});

class TestAuthSessionStore extends AuthSessionStore {
    public readonly ready: Promise<void>;

    constructor(context: ExtensionContext, config: AugmentConfigListener) {
        super(context, config);

        this.ready = this._ready;
    }
}
