import { generateMockWorkspaceConfig } from "../../__mocks__/mock-augment-config";
import { ExtensionContext, Memento, mockWorkspaceConfigChange } from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AuthActionsModel } from "../../auth/auth-actions-model";
import { AuthSessionStore } from "../../auth/auth-session-store";
import { ActionsModel } from "../../main-panel/action-cards/actions-model";
import { AugmentGlobalState } from "../../utils/context";
import { DisposableService } from "../../utils/disposable-service";
import { SystemStateName, SystemStatus } from "../../utils/types";

describe("AuthActionsModel", () => {
    let kit: AuthActionsModelTestKit;

    beforeEach(() => {
        Memento.resetValues();
        kit = new AuthActionsModelTestKit();
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                advanced: {
                    apiToken: undefined,
                    completionURL: undefined,
                },
            })
        );
    });

    afterEach(() => {
        kit.dispose();
    });

    test("should update on sign in / out and config changes", async () => {
        const restartSpy = jest.spyOn(kit.actionsModel, "restartActionsState");
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.initializing,
        });

        await kit.authSession.ready;

        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.initializing,
        });

        new AuthActionsModel(kit.actionsModel, kit.authSession, kit.configListener);

        expect(restartSpy).toHaveBeenCalledTimes(0);
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.incomplete,
        });

        await kit.authSession.saveSession("test", "test");

        expect(restartSpy).toHaveBeenCalledTimes(0);
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.complete,
        });

        await kit.authSession.removeSession();

        expect(restartSpy).toHaveBeenCalledTimes(0);
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.incomplete,
        });

        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                advanced: {
                    apiToken: "test-api-token",
                    completionURL: "test-completion-url",
                },
            })
        );

        expect(restartSpy).toHaveBeenCalledTimes(0);
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.complete,
        });
    });

    test("reset action models when debug features true and logged out", async () => {
        const restartSpy = jest.spyOn(kit.actionsModel, "restartActionsState");
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                advanced: {
                    enableDebugFeatures: true,
                },
            })
        );

        await kit.authSession.ready;

        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.initializing,
        });

        new AuthActionsModel(kit.actionsModel, kit.authSession, kit.configListener);

        expect(restartSpy).toHaveBeenCalledTimes(1);
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.incomplete,
        });

        await kit.authSession.saveSession("test", "test");

        expect(restartSpy).toHaveBeenCalledTimes(1);
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.complete,
        });

        await kit.authSession.removeSession();

        expect(restartSpy).toHaveBeenCalledTimes(2);
        expect(kit.actionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.incomplete,
        });
    });
});

class AuthActionsModelTestKit extends DisposableService {
    public readonly configListener = new AugmentConfigListener();
    public readonly authSession;
    public readonly actionsModel;

    constructor(public readonly context: ExtensionContext = new ExtensionContext()) {
        super();

        this.authSession = new TestAuthSessionStore(this.context, this.configListener);
        this.actionsModel = new ActionsModel(new AugmentGlobalState(this.context));

        this.addDisposable(this.authSession);
        this.addDisposable(this.configListener);
    }
}

class TestAuthSessionStore extends AuthSessionStore {
    public readonly ready: Promise<void>;

    constructor(context: ExtensionContext, config: AugmentConfigListener) {
        super(context, config);

        this.ready = this._ready;
    }
}
