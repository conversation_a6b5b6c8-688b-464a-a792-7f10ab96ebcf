import * as vscode from "vscode";

import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { ExtensionContext } from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AuthSessionStore } from "../../auth/auth-session-store";
import { OAuthFlow } from "../../auth/oauth-flow";
import { OnboardingSessionEventReporter } from "../../metrics/onboarding-session-event-reporter";
import { waitableJestFn } from "../__utils__/waitable-mock";

jest.mock("crypto", () => {
    return {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        __esModule: true,
        ...jest.requireActual("crypto"),
        randomUUID: () => "random-uuid-1234",
        randomBytes: () => Buffer.from("random-bytes-1234"),
    };
});

describe("oauth-flow", () => {
    beforeEach(() => {
        // Ensure the setTimeout functions don't cause jest to think there is
        // more work to be done
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("save session in same instance", async () => {
        const openExternalMock = waitableJestFn(1);
        jest.spyOn(vscode.env, "openExternal").mockImplementation(openExternalMock);

        const token = "example-token-1234";
        const code = "example-code-1234";
        const tenantURL = "http://augment-tenant-example.augmentcode.com";

        const apiServer = new MockAPIServer();
        apiServer.getAccessToken = jest.fn().mockResolvedValue(token);

        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        await authSession.ready;
        const auth = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            apiServer,
            authSession,
            new OnboardingSessionEventReporter(apiServer)
        );
        const sessionPromise = auth.startFlow();

        // Wait for openExternal to be called
        await openExternalMock.waitUntilComplete();

        // Pretend that the browser OAuth flow redirected to our extension
        auth.handleAuthURI(
            vscode.Uri.from({
                scheme: "vscode",
                authority: "example",
                query: `state=random-uuid-1234&code=${code}&tenant_url=${encodeURIComponent(
                    tenantURL
                )}`,
            })
        );

        await sessionPromise;
        const session = await authSession.getSession();
        expect(session).toEqual({
            accessToken: token, // pragma: allowlist secret
            scopes: ["email"],
            tenantURL,
        });

        expect(apiServer.getAccessToken as jest.Mock).toHaveBeenCalledTimes(1);
        const call = (apiServer.getAccessToken as jest.Mock).mock.calls[0];
        expect(call).toEqual([
            "vscode://example-extension.example/auth/result",
            tenantURL,
            "cmFuZG9tLWJ5dGVzLTEyMzQ",
            code,
        ]);

        await expect(authSession.getSession()).resolves.toEqual(session);
        await expect(context.secrets.get("augment.oauth-state")).resolves.toEqual(undefined);
    });

    test("session checks for valid tenant", async () => {
        const openExternalMock = waitableJestFn(1);
        jest.spyOn(vscode.env, "openExternal").mockImplementation(openExternalMock);

        const token = "example-token-1234";
        const code = "example-code-1234";
        const tenantURL = "http://augment-tenant-example.attacker-augmentcode.com";

        const apiServer = new MockAPIServer();
        apiServer.getAccessToken = jest.fn().mockResolvedValue(token);

        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        await authSession.ready;
        const auth = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            apiServer,
            authSession,
            new OnboardingSessionEventReporter(apiServer)
        );
        const sessionPromise = auth.startFlow();

        // Wait for openExternal to be called
        await openExternalMock.waitUntilComplete();

        // Pretend that the browser OAuth flow redirected to our extension
        auth.handleAuthURI(
            vscode.Uri.from({
                scheme: "vscode",
                authority: "example",
                query: `state=random-uuid-1234&code=${code}&tenant_url=${encodeURIComponent(
                    tenantURL
                )}`,
            })
        );

        expect(async () => await sessionPromise).rejects.toThrow(
            "OAuth request failed: invalid OAuth tenant URL"
        );
    });

    test("save session across instances", async () => {
        const openExternalMock = waitableJestFn(1);
        jest.spyOn(vscode.env, "openExternal").mockImplementation(openExternalMock);

        const token = "example-token-1234";
        const code = "example-code-1234";
        const tenantURL = "http://augment-tenant-example.augmentcode.com";

        const apiServer = new MockAPIServer();
        apiServer.getAccessToken = jest.fn().mockResolvedValue(token);

        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        await authSession.ready;
        const auth1 = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            apiServer,
            authSession,
            new OnboardingSessionEventReporter(apiServer)
        );
        const auth2 = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            apiServer,
            authSession,
            new OnboardingSessionEventReporter(apiServer)
        );

        // Create the session on the first instance
        const sessionPromise = auth1.startFlow();

        // Wait for openExternal to be called
        await openExternalMock.waitUntilComplete();

        // Pretend that the browser OAuth flow redirected to the second extension instance
        await auth2.handleAuthURI(
            vscode.Uri.from({
                scheme: "vscode",
                authority: "example",
                query: `state=random-uuid-1234&code=${code}&tenant_url=${encodeURIComponent(
                    tenantURL
                )}`,
            })
        );

        await sessionPromise;
        const gotSession = await authSession.getSession();

        const wantSession = {
            accessToken: token, // pragma: allowlist secret
            scopes: ["email"],
            tenantURL,
        };
        expect(gotSession).toEqual(wantSession);
        await expect(authSession.getSession()).resolves.toEqual(wantSession);
        await expect(authSession.getSession()).resolves.toEqual(wantSession);

        expect(apiServer.getAccessToken as jest.Mock).toHaveBeenCalledTimes(1);
        const call = (apiServer.getAccessToken as jest.Mock).mock.calls[0];
        expect(call).toEqual([
            "vscode://example-extension.example/auth/result",
            tenantURL,
            "cmFuZG9tLWJ5dGVzLTEyMzQ",
            code,
        ]);
        expect(context.secrets.get("augment.oauth-state")).resolves.toEqual(undefined);
    });

    test("handle uri error without description", async () => {
        const openExternalMock = waitableJestFn(1);
        jest.spyOn(vscode.env, "openExternal").mockImplementation(openExternalMock);

        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        await authSession.ready;
        const auth = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            new MockAPIServer(),
            authSession,
            new OnboardingSessionEventReporter(new MockAPIServer())
        );
        const sessionPromise = auth.startFlow();

        // Wait for openExternal to be called
        await openExternalMock.waitUntilComplete();

        // Pretend that the browser OAuth flow redirected to our extension
        auth.handleAuthURI(
            vscode.Uri.from({
                scheme: "vscode",
                authority: "example",
                query: `state=random-uuid-1234&error=${encodeURIComponent("Example Error Code")}`,
            })
        );

        await expect(sessionPromise).rejects.toThrowError(
            "OAuth request failed: (Example Error Code)"
        );
        expect(context.secrets.get("augment.oauth-state")).resolves.toEqual(undefined);
    });

    test("handle uri error with a description", async () => {
        const openExternalMock = waitableJestFn(1);
        jest.spyOn(vscode.env, "openExternal").mockImplementation(openExternalMock);

        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        await authSession.ready;
        const auth = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            new MockAPIServer(),
            authSession,
            new OnboardingSessionEventReporter(new MockAPIServer())
        );
        const sessionPromise = auth.startFlow();

        // Wait for openExternal to be called
        await openExternalMock.waitUntilComplete();

        // Pretend that the browser OAuth flow redirected to our extension
        auth.handleAuthURI(
            vscode.Uri.from({
                scheme: "vscode",
                authority: "example",
                query: `state=random-uuid-1234&error=${encodeURIComponent(
                    "Example Error Code"
                )}&error_description=${encodeURIComponent(
                    "This will be a slightly longer description of the error"
                )}`,
            })
        );

        await expect(sessionPromise).rejects.toThrow(
            "OAuth request failed: (Example Error Code) This will be a slightly longer description of the error"
        );
        expect(context.secrets.get("augment.oauth-state")).resolves.toEqual(undefined);
    });

    test("multiple sessions", async () => {
        const openExternalMock = waitableJestFn(1);
        jest.spyOn(vscode.env, "openExternal").mockImplementation(openExternalMock);

        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        await authSession.ready;
        const auth = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            new MockAPIServer(),
            authSession,
            new OnboardingSessionEventReporter(new MockAPIServer())
        );
        const initialSessionPromise = auth.startFlow();

        // Wait for openExternal to be called
        await openExternalMock.waitUntilComplete();

        // Create a second session
        auth.startFlow();

        await expect(initialSessionPromise).rejects.toThrowError("Cancelled due to new sign in");
        expect(context.secrets.get("augment.oauth-state")).resolves.toBeTruthy();
    });

    test("timeout", async () => {
        const openExternalMock = waitableJestFn(1);
        jest.spyOn(vscode.env, "openExternal").mockImplementation(openExternalMock);

        const context = new ExtensionContext();
        const authSession = new TestAuthSessionStore(context, new AugmentConfigListener());
        await authSession.ready;
        const auth = new OAuthFlow(
            context,
            new AugmentConfigListener(),
            new MockAPIServer(),
            authSession,
            new OnboardingSessionEventReporter(new MockAPIServer())
        );

        const initialSessionPromise = auth.startFlow();

        // Wait for openExternal to be called
        await openExternalMock.waitUntilComplete();

        // Force the setTimeout callback to be run
        jest.runAllTimers();

        // The setTimeout should act as a timeout
        await expect(initialSessionPromise).rejects.toContain("Timed out");
        expect(context.secrets.get("augment.oauth-state")).resolves.toEqual(undefined);
    });
});

class TestAuthSessionStore extends AuthSessionStore {
    public readonly ready: Promise<void>;

    constructor(context: vscode.ExtensionContext, config: AugmentConfigListener) {
        super(context, config);

        this.ready = this._ready;
    }
}
