import { AsyncMsgHand<PERSON>, wrapResponse } from "../../../utils/webviews/messaging";
import {
    type AsyncWebViewMessage,
    ChatModelReply,
    type WebViewMessage,
    WebViewMessageType,
} from "../../../webview-providers/webview-messages";

describe("messaging", () => {
    describe("wrapResponse", () => {
        it("returns an object with the correct type property", () => {
            const response: WebViewMessage = {
                type: WebViewMessageType.chatUserMessage,
                data: { text: "hello", chatHistory: [], silent: false },
            };
            const wrappedResponse = wrapResponse("mock-request-id", response);
            expect(wrappedResponse.type).toBe(WebViewMessageType.asyncWrapper);
        });

        it("returns an object with a requestId property that matches the input requestId", () => {
            const response: WebViewMessage = {
                type: WebViewMessageType.chatUserMessage,
                data: { text: "hello", chatHistory: [], silent: false },
            };
            const wrappedResponse = wrapResponse("mock-request-id", response);
            expect(wrappedResponse.requestId).toBe("mock-request-id");
        });

        it("returns an object with a baseMsg property that is the original response", () => {
            const response: WebViewMessage = {
                type: WebViewMessageType.chatUserMessage,
                data: { text: "hello", chatHistory: [], silent: false },
            };
            const wrappedResponse = wrapResponse("mock-request-id", response);
            expect(wrappedResponse.baseMsg).toBe(response);
        });
    });

    describe("AsyncMsgHandler", () => {
        let handlers: ((msg: AsyncWebViewMessage<WebViewMessage>) => void)[] = [];
        const registrar = jest.fn((h: (msg: AsyncWebViewMessage<WebViewMessage>) => void) => {
            const wrappedH = jest.fn(h);
            handlers.push(wrappedH);
            return () => {
                handlers.filter((_h) => _h !== wrappedH);
            };
        });
        const postMsg = jest.fn();

        const cleanup = () => {
            registrar.mockClear();
            postMsg.mockClear();
            handlers = [];
        };

        it("AsyncMsgHandler construction works as expected", () => {
            expect(registrar).toHaveBeenCalledTimes(0);
            const _ = new AsyncMsgHandler(postMsg, registrar);
            // Nothing is registered until a handler is registered
            expect(registrar).toHaveBeenCalledTimes(0);
            expect(handlers.length).toBe(0);

            cleanup();
        });

        it("handles a message correctly", async () => {
            const handler = new AsyncMsgHandler(postMsg, registrar);
            const msg: AsyncWebViewMessage<WebViewMessage> = {
                type: WebViewMessageType.asyncWrapper,
                requestId: "mock-request-id",
                error: null,
                baseMsg: {
                    type: WebViewMessageType.chatUserMessage,
                    data: { text: "hello", chatHistory: [], silent: false },
                },
            };

            // The handler just echoes back the message
            handler.registerHandler(WebViewMessageType.chatUserMessage, (msg) =>
                Promise.resolve(msg)
            );
            const handlerResults = handlers.map((h) => h(msg));

            expect(handlers.length).toBe(1);
            expect(handlerResults[0]).toBe(true);

            // Wait for next run of the event loop
            await new Promise((resolve) => setTimeout(resolve, 0));
            expect(postMsg).toHaveBeenCalledTimes(1);
            expect(postMsg).toHaveBeenCalledWith(msg);

            cleanup();
        });

        it("does not handle a message with an invalid type", async () => {
            const handler = new AsyncMsgHandler(postMsg, registrar);
            handler.registerHandler(WebViewMessageType.chatUserMessage, (msg) =>
                Promise.resolve(msg)
            );

            const msg: AsyncWebViewMessage<WebViewMessage> = {
                type: WebViewMessageType.asyncWrapper,
                requestId: "mock-request-id",
                error: null,
                baseMsg: {
                    type: WebViewMessageType.nextEditLoaded,
                },
            };
            const handlerResults = handlers.map((h) => h(msg));

            expect(handlers.length).toBe(1);
            expect(handlerResults[0]).toBe(false);

            // Wait for next run of the event loop
            await new Promise((resolve) => setTimeout(resolve, 0));
            expect(postMsg).toHaveBeenCalledTimes(0);

            cleanup();
        });
    });

    describe("AsyncMsgHandler stream", () => {
        let handlers: ((msg: AsyncWebViewMessage<WebViewMessage>) => void)[] = [];
        const registrar = jest.fn((h: (msg: AsyncWebViewMessage<WebViewMessage>) => void) => {
            const wrappedH = jest.fn(h);
            handlers.push(wrappedH);
            return () => {
                handlers.filter((_h) => _h !== wrappedH);
            };
        });
        const postMsg = jest.fn();

        const cleanup = () => {
            registrar.mockClear();
            postMsg.mockClear();
            handlers = [];
        };

        it("AsyncMsgHandler construction works as expected", () => {
            expect(registrar).toHaveBeenCalledTimes(0);
            const _ = new AsyncMsgHandler(postMsg, registrar);
            // Nothing is registered until a handler is registered
            expect(registrar).toHaveBeenCalledTimes(0);
            expect(handlers.length).toBe(0);

            cleanup();
        });

        it("handles a message correctly", async () => {
            const handler = new AsyncMsgHandler(postMsg, registrar);

            // The handler just echoes back the message
            const dataToStream: ChatModelReply[] = [
                {
                    type: WebViewMessageType.chatModelReply,
                    data: {
                        text: "hello",
                        requestId: "mock-request-id",
                        streaming: true,
                        workspaceFileChunks: [],
                    },
                },
                {
                    type: WebViewMessageType.chatModelReply,
                    data: {
                        text: "world",
                        requestId: "mock-request-id",
                        streaming: true,
                        workspaceFileChunks: [],
                    },
                },
            ];
            handler.registerStreamHandler(WebViewMessageType.chatUserMessage, async function* (_) {
                for (const data of dataToStream) {
                    yield data;
                }
            });
            await new Promise((resolve) => setTimeout(resolve, 0));

            const msg: AsyncWebViewMessage<WebViewMessage> = {
                type: WebViewMessageType.asyncWrapper,
                requestId: "async-request-id",
                error: null,
                baseMsg: {
                    type: WebViewMessageType.chatUserMessage,
                    data: { text: "hello", chatHistory: [], silent: false },
                },
            };
            const handlerResults = handlers.map((h) => h(msg));

            expect(handlers.length).toBe(1);
            expect(handlerResults[0]).toBe(true);

            // Wait for next run of the event loop
            await new Promise((resolve) => setTimeout(resolve, 0));
            expect(postMsg).toHaveBeenCalledTimes(3);
            expect(postMsg).toHaveBeenNthCalledWith(1, {
                type: WebViewMessageType.asyncWrapper,
                requestId: "async-request-id",
                error: null,
                baseMsg: dataToStream[0],
                streamCtx: {
                    streamMsgIdx: 0,
                    streamNextRequestId: "async-request-id-0",
                },
            });
            expect(postMsg).toHaveBeenNthCalledWith(2, {
                type: WebViewMessageType.asyncWrapper,
                requestId: "async-request-id-0",
                error: null,
                baseMsg: dataToStream[1],
                streamCtx: {
                    streamMsgIdx: 1,
                    streamNextRequestId: "async-request-id-1",
                },
            });
            expect(postMsg).toHaveBeenNthCalledWith(3, {
                type: WebViewMessageType.asyncWrapper,
                requestId: "async-request-id-1",
                error: null,
                baseMsg: { type: WebViewMessageType.empty },
                streamCtx: {
                    streamMsgIdx: 2,
                    streamNextRequestId: "async-request-id-2",
                    isStreamComplete: true,
                },
            });

            cleanup();
        });
    });
});
