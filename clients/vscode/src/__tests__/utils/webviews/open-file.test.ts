import {
    commands,
    MutableTextDocument,
    Range,
    TextEditor,
    Uri,
    ViewColumn,
    window,
} from "../../../__mocks__/vscode-mocks";
import { openFileFromMessage } from "../../../utils/webviews/open-file";

describe("openFileFromMessage", () => {
    it("open file with minimal arguments", async () => {
        jest.spyOn(commands, "executeCommand").mockImplementation(jest.fn());

        await openFileFromMessage({
            repoRoot: "/home/<USER>/project/example-repo",
            pathName: "src/example.ts",
        });

        expect(commands.executeCommand).toHaveBeenCalledWith(
            "vscode.open",
            Uri.file("/home/<USER>/project/example-repo/src/example.ts")
        );
    });
    it("open file with text document", async () => {
        jest.spyOn(commands, "executeCommand").mockImplementation(jest.fn());

        // Create a mock document to be returned by openTextDocument
        const mockDocument = new MutableTextDocument(
            Uri.file("/home/<USER>/project/example-repo/src/example.ts"),
            "mock content"
        );

        // Import workspace from the mocks
        const { workspace } = await import("../../../__mocks__/vscode-mocks");

        // Mock workspace.openTextDocument
        jest.spyOn(workspace, "openTextDocument").mockResolvedValue(mockDocument);

        // Mock window.showTextDocument
        jest.spyOn(window, "showTextDocument").mockResolvedValue({} as any);

        await openFileFromMessage({
            repoRoot: "/home/<USER>/project/example-repo",
            pathName: "src/example.ts",
            openTextDocument: true,
        });

        expect(workspace.openTextDocument).toHaveBeenCalledWith(
            "/home/<USER>/project/example-repo/src/example.ts"
        );
        expect(window.showTextDocument).toHaveBeenCalledWith(mockDocument, ViewColumn.Active);
    });

    it("open file with range", async () => {
        jest.spyOn(commands, "executeCommand").mockImplementation(jest.fn());

        const editor = new TextEditor(
            new MutableTextDocument(
                Uri.file("/home/<USER>/project/example-repo/src/example.ts"),
                "abc\ndef\nghi\njkl\nmno\npqr\nstu\nvwx\nyz"
            )
        );
        window.activeTextEditor = editor;
        jest.spyOn(editor, "revealRange").mockImplementation(jest.fn());

        await openFileFromMessage({
            repoRoot: "/home/<USER>/project/example-repo",
            pathName: "src/example.ts",
            range: {
                start: 3,
                stop: 6,
            },
        });

        expect(commands.executeCommand).toHaveBeenCalledWith(
            "vscode.open",
            Uri.file("/home/<USER>/project/example-repo/src/example.ts")
        );
        expect(editor.revealRange).toHaveBeenCalledWith(new Range(2, 0, 5, 3));
    });

    it("open file with full range", async () => {
        jest.spyOn(commands, "executeCommand").mockImplementation(jest.fn());

        const editor = new TextEditor(
            new MutableTextDocument(
                Uri.file("/home/<USER>/project/example-repo/src/example.ts"),
                "abc\ndef\nghi\njkl\nmno\npqr\nstu\nvwx\nyz"
            )
        );
        window.activeTextEditor = editor;
        jest.spyOn(editor, "revealRange").mockImplementation(jest.fn());

        await openFileFromMessage({
            repoRoot: "/home/<USER>/project/example-repo",
            pathName: "src/example.ts",
            fullRange: {
                startLineNumber: 2,
                startColumn: 1,
                endLineNumber: 5,
                endColumn: 2,
            },
        });

        expect(commands.executeCommand).toHaveBeenCalledWith(
            "vscode.open",
            Uri.file("/home/<USER>/project/example-repo/src/example.ts")
        );
        expect(editor.revealRange).toHaveBeenCalledWith(new Range(2, 1, 5, 2));
    });
});
