import { Webview } from "../../../__mocks__/vscode-mocks";
import {
    addGoogleFonts,
    addImgData,
    addMonacoRequirements,
    addNonce,
    addWebViewCSP,
    generateCSPPolicy,
} from "../../../utils/webviews/csp";

describe("generateCSPPolicy", () => {
    let webview = new Webview(
        {
            enableScripts: true,
            localResourceRoots: [],
        },
        "",
        "vscode-webview://example/path"
    );

    test("basic", () => {
        const policy = generateCSPPolicy();
        expect(policy).toBe("default-src 'none'; style-src 'unsafe-inline';");
    });

    test("everything", () => {
        const policy = generateCSPPolicy(
            addGoogleFonts(),
            addWebViewCSP(webview),
            addImgData(),
            addNonce("example-nonce"),
            addMonacoRequirements()
        );
        expect(policy).toBe(
            "default-src 'none'; font-src data: https://fonts.gstatic.com vscode-webview://example/path; style-src 'unsafe-inline' https://fonts.googleapis.com vscode-webview://example/path; script-src 'nonce-example-nonce' vscode-webview://example/path; img-src blob: data: vscode-webview://example/path; media-src vscode-webview://example/path; worker-src blob:;"
        );
    });
});
