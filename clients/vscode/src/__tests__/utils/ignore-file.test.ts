import ignore from "../../../../../third_party/node-ignore";

describe("ignore-file", () => {
    // Verify that the bug described in https://github.com/kaelzhang/node-ignore/issues/116 is
    // not present in the version of `ignore` we are using.
    test.each([
        ["blah", false, false],
        ["foo", true, false],
        ["fooey", true, false],
        ["foobar", false, true],
        ["blah/x.js", false, false],
        ["foo/x.js", true, false],
        ["fooey/x.js", true, false],
        ["foobar/x.js", false, true],
    ])("ignore(%s)", (pathName, xIgnored, xUnignored) => {
        const ig = ignore();
        ig.add("foo*");
        ig.add("!foobar");

        const result = ig.test(pathName);
        expect(result.ignored).toBe(xIgnored);
        expect(result.unignored).toBe(xUnignored);
    });

    // Verify that the ignore library honors the "ignoreCase" option.
    test.each([
        ["foo", true],
        ["Foo", false],
        ["FOO", false],
        ["blah/foo", true],
        ["blah/Foo", false],
        ["blah/FOO", false],
    ])("case sensitive", (pathName, xIgnored) => {
        const ig = ignore({ ignorecase: false });
        ig.add("foo");

        const result = ig.test(pathName);
        expect(result.ignored).toBe(xIgnored);
    });
});
