import { expect, test } from "@jest/globals";
import { Uri } from "vscode";

import { MockFSUtils, mockFSUtils } from "../../__mocks__/fs-utils";
import { defaultSupportedFileExtensions } from "../../languages";
import {
    IgnoreSource,
    IgnoreSourceBuiltin,
    IgnoreSourceFile,
    IgnoreStackBuilder,
} from "../../utils/ignore-file";
import { listTrackedFiles } from "../../utils/list-tracked-files";
import {
    FullPathFilter,
    makePathFilter,
    PathFilter,
    PathIterator,
} from "../../utils/path-iterator";
import { directoryContainsPath, normalizePathName } from "../../utils/path-utils";
import { FileType } from "../../utils/types";
import { verifyDefined } from "../__utils__/test-utils";

var pathLib = require("node:path");

enum PathStatus {
    accepted, // path should be enumerated and accepted
    rejected, // path should be enumerated but rejected
    absent, // path should not be enumerated
}

class PathIteratorTestKit {
    // ignoreFiles is an array of names of ignore files, in order of precedence. Files later
    // in the array override files earlier in the array.
    static readonly ignoreSources: Array<IgnoreSource> = [
        new IgnoreSourceFile(".gitignore"),
        new IgnoreSourceBuiltin("/"),
        new IgnoreSourceFile(".augmentignore"),
    ];

    public readonly ignoreStackBuilder = new IgnoreStackBuilder(PathIteratorTestKit.ignoreSources);

    public get fs(): MockFSUtils {
        return mockFSUtils;
    }

    // `run` runs a test case:
    // * candidateFiles is the set of files to create in the (mock) file system
    // * root is the directory to iterate (corresponds to WorkspaceFolder)
    // * augmentroot is the augment root directory; it must be the same as root or a parent of it
    // * (optional) augmentignores is a set of augment ignore files to create
    // * (optional) expectedNonFiles is a set of non-files (directories and "other") to verify
    async run(
        candidateFiles: Map<string, PathStatus>,
        workspaceRoot: string,
        augmentRoot: string,
        ignoresFiles = new Map<string, string>(),
        expectedNonFiles?: Map<string, [FileType, PathStatus]>,
        bypassLanguageFilter: boolean = false
    ): Promise<void> {
        // Construct the prefix used to form an absolute path from a relative path.
        // This is the same as the augmentRoot with a '/' at the end if it doesn't
        // already have one.
        const pathPrefix = augmentRoot.endsWith("/") ? augmentRoot : augmentRoot + "/";

        // Create the candidate files and the augmentIgnore files.
        for (const absPath of candidateFiles.keys()) {
            await this.createFile(absPath);
        }
        for (const [absPath, contents] of ignoresFiles) {
            await this.createFile(absPath, contents);
        }

        const pathFilter = await makePathFilter(
            Uri.file(workspaceRoot),
            Uri.file(augmentRoot),
            this.ignoreStackBuilder,
            bypassLanguageFilter ? undefined : defaultSupportedFileExtensions
        );

        // Create a PathIterator and collect the set of returned paths
        const pathIterator = new PathIterator(
            "test",
            Uri.file(workspaceRoot),
            Uri.file(augmentRoot),
            pathFilter
        );
        const resultPaths = await this.getResultPaths(pathIterator);

        // Collect sets of files and non-files in the result set
        const filePaths = new Map<string, [string, boolean]>();
        const nonFilePaths = new Map<string, [string, FileType, boolean]>();
        for (const [absPath, [relPath, fileType, accepted]] of resultPaths) {
            if (fileType === FileType.file) {
                filePaths.set(absPath, [relPath, accepted]);
            } else {
                nonFilePaths.set(absPath, [relPath, fileType, accepted]);
            }
        }

        // Construct the set of expected files (the candidateFiles plus the ignore files)
        const expectedFiles = new Map<string, PathStatus>();
        for (const [absPath, pathStatus] of candidateFiles) {
            if (pathStatus === PathStatus.absent) {
                continue;
            }
            expectedFiles.set(absPath, pathStatus);
        }
        for (const ignoreFileAbPath of ignoresFiles.keys()) {
            if (!directoryContainsPath(workspaceRoot, ignoreFileAbPath)) {
                continue;
            }
            if (!bypassLanguageFilter) {
                expectedFiles.set(ignoreFileAbPath, PathStatus.rejected);
            }
            // else the caller must supply the expected status of the ignore file.
        }

        // Verify that we got exactly the expected set of file (and non-file) results
        this.verifyFilePaths(filePaths, expectedFiles, pathPrefix);
        if (expectedNonFiles !== undefined) {
            this.verifyNonFilePaths(nonFilePaths, expectedNonFiles, pathPrefix);
        }

        // Ensure that the PathFilter accepts/rejects the correct set of files
        this.verifyPathFilter(pathFilter, candidateFiles, workspaceRoot, pathPrefix);

        // Ensure that listTrackedFiles produces the same set of tracked files as the iterator
        await this.verifyListTrackedFilesFn(workspaceRoot, augmentRoot, pathFilter, resultPaths);
    }

    // getResultPaths returns a Map of <absolute path> -> <relative path, file type, accepted> from
    // the set of files returned by the iterator.
    async getResultPaths(
        pathIterator: PathIterator
    ): Promise<Map<string, [string, FileType, boolean]>> {
        const resultPaths = new Map<string, [string, FileType, boolean]>();
        let count = 0;
        for await (const [uri, relPath, fileType, acceptance] of pathIterator) {
            resultPaths.set(normalizePathName(uri.fsPath), [
                relPath,
                fileType,
                acceptance.accepted,
            ]);
            count++;
        }
        expect(resultPaths.size).toBe(count); // no duplicate results
        return resultPaths;
    }

    // verifyResultPaths verifies that the set of paths returned by the iterator
    // is what is expected
    verifyFilePaths(
        resultPaths: Map<string, [string, boolean]>,
        expectedPaths: Map<string, PathStatus>,
        pathPrefix: string
    ): void {
        let expectedCount = 0;
        for (const [absPath, pathStatus] of expectedPaths) {
            if (pathStatus === PathStatus.absent) {
                continue;
            }
            const item = resultPaths.get(absPath);
            verifyDefined(item);
            const [relPath, accepted] = item;
            expect(pathPrefix + relPath).toBe(absPath);

            if (pathStatus === PathStatus.accepted) {
                expect(accepted).toBe(true);
            } else {
                expect(accepted).toBe(false);
            }
            expectedCount++;
        }
        expect(resultPaths.size).toBe(expectedCount);
    }

    verifyNonFilePaths(
        resultPaths: Map<string, [string, FileType, boolean]>,
        expectedPaths: Map<string, [FileType, PathStatus]>,
        pathPrefix: string
    ) {
        let expectedCount = 0;
        for (const [absPath, [fileType, pathStatus]] of expectedPaths) {
            if (pathStatus === PathStatus.absent) {
                continue;
            }
            const item = resultPaths.get(absPath);
            verifyDefined(item);
            const [relPath, expectedFileType, expectedAccepted] = item;
            expect(pathPrefix + relPath).toBe(absPath);
            if (pathStatus === PathStatus.accepted) {
                expect(expectedAccepted).toBe(true);
            } else {
                expect(expectedAccepted).toBe(false);
            }
            expect(fileType).toBe(expectedFileType);
            expectedCount++;
        }
        expect(resultPaths.size).toBe(expectedCount);
    }

    // verifyPathFilter verifies that the PathFilter created by the PathIterator
    // accepts and rejects path names as expected
    verifyPathFilter(
        pathFilter: PathFilter,
        candidateFiles: Map<string, PathStatus>,
        root: string,
        augmentRoot: string
    ): void {
        for (const [absPath, pathStatus] of candidateFiles) {
            if (!directoryContainsPath(root, absPath)) {
                continue;
            }
            const relPath = pathLib.relative(augmentRoot, absPath);
            const expected = pathStatus === PathStatus.accepted;
            expect(pathFilter.acceptsPath(relPath)).toBe(expected);
        }
    }

    async verifyListTrackedFilesFn(
        folderRoot: string,
        repoRoot: string,
        pathFilter: FullPathFilter,
        fileMap: Map<string, [string, FileType, boolean]>
    ) {
        const acceptedFiles = await listTrackedFiles(
            Uri.file(folderRoot),
            Uri.file(repoRoot),
            pathFilter
        );

        const expectedFiles = Array.from(fileMap.entries())
            .filter(([_absPath, [_relPath, fileType, accepted]]) => {
                return fileType === FileType.file && accepted;
            })
            .map(([_absPath, [relPath, _fileType, _accepted]]) => {
                return relPath;
            });

        const acceptedFilesSet = new Set(acceptedFiles);
        const expectedFilesSet = new Set(expectedFiles);
        expect(acceptedFilesSet).toEqual(expectedFilesSet);
    }

    // createFile writes/overwrites the given contents to the given path. It creates
    // any needed subdirectories.
    async createFile(pathName: string, text = ""): Promise<void> {
        this.fs.writeFileUtf8(pathName, text, true);
    }
}

// runTest creates a PathIteratorTestKit and passes all of its paramters to
// `kit.run`.
async function runTest(
    candidateFiles: Map<string, PathStatus>,
    workspaceRoot: string,
    augmentRoot: string,
    augmentIgnores = new Map<string, string>(),
    expectedNonFiles?: Map<string, [FileType, PathStatus]>,
    bypassLanguageFilter?: boolean
): Promise<void> {
    const kit = new PathIteratorTestKit();
    return kit.run(
        candidateFiles,
        workspaceRoot,
        augmentRoot,
        augmentIgnores,
        expectedNonFiles,
        bypassLanguageFilter
    );
}

describe("PathIterator", () => {
    const accepted = PathStatus.accepted;
    const rejected = PathStatus.rejected;
    const absent = PathStatus.absent;

    beforeEach(() => {
        mockFSUtils.reset();
    });

    // `single-dir` verifies that the iterator correctly enumerates files in a
    // single directory
    test("single-dir", async () => {
        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.py", accepted],
            ["/giraffe/def.py", accepted],
            ["/giraffe/ghi.skip", rejected], // unsupported extension
            ["/giraffe/jkl.cc", accepted],
            ["/giraffe/jkl.js", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe");
    });

    // `multi-dir` verifies that the iterator correctly enumerates files in
    // multiple directories
    test("multi-dir", async () => {
        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.py", accepted],
            ["/giraffe/crocodile/def.py", accepted],
            ["/giraffe/porpoise/canary/ghi.skip", rejected], // unsupported extension
            ["/giraffe/slug/jkl.cc", accepted],
            ["/giraffe/slug/jkl.py", accepted],
            ["/giraffe/slug/armadillo/jkl.js", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe");
    });

    // `outside-root` verifies that the iterator correctly skips files outside
    // its root directory
    test("outside-root", async () => {
        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.py", accepted],
            ["/giraffe/def.py", accepted],
            ["/elephant/abc.py", absent], // skipped because using
            ["/elephant/def.py", absent], // root = /giraffe
        ]);

        await runTest(files, "/giraffe", "/giraffe");
    });

    // `different-augmentroot` verifies that the iterator works correctly when the
    // augmentroot is different from the root: only the files under the root should
    // should be enumerated, but their paths should be relative to the augmentroot
    test("different-augmentroot", async () => {
        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.py", accepted],
            ["/giraffe/def.py", accepted],
            ["/elephant/abc.py", absent], // skipped because using
            ["/elephant/def.py", absent], // root = /giraffe
        ]);

        await runTest(files, "/giraffe", "/");
    });

    // `gitignore-single-dir` verifies that the iterator honors the contents
    // of a gitignore file in a single directory
    test("gitignore-single-dir", async () => {
        const gitIgnores = new Map<string, string>([["/giraffe/.gitignore", "*.py\n"]]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.cxx", accepted],
            ["/giraffe/def.py", rejected], // excluded
            ["/giraffe/jkl.py", rejected], // excluded
            ["/giraffe/jkl.js", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe", gitIgnores);
    });

    // `gitignore-and-augmentignore` verifies that the iterator honors the contents
    // of both gitignore and augmentignore files in a single directory
    test("gitignore-and-augmentignore", async () => {
        const ignoreFiles = new Map<string, string>([
            ["/giraffe/.gitignore", "*.py\n"],
            ["/giraffe/.augmentignore", "*.js\n"],
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.cxx", accepted],
            ["/giraffe/def.py", rejected], // excluded
            ["/giraffe/jkl.py", rejected], // excluded
            ["/giraffe/jkl.js", rejected], // excluded
        ]);

        await runTest(files, "/giraffe", "/giraffe", ignoreFiles);
    });

    test("gitignore rule cancels parent rule", async () => {
        const ignoreFiles = new Map<string, string>([
            ["/giraffe/.gitignore", "node_modules\n"],
            ["/giraffe/walrus/.gitignore", "!node_modules\n"],
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/node_modules", rejected],
            ["/giraffe/walrus/node_modules/a.py", accepted],
            ["/giraffe/walrus/slug/node_modules/b.py", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe", ignoreFiles);
    });

    // `gitignore-and-augmentignore-nested` verifies that higher level gitignore
    // does not override lower level augmentignore
    test("gitignore-and-augmentignore-nested", async () => {
        const ignoreFiles = new Map<string, string>([
            ["/giraffe/.gitignore", "*.py\n"],
            ["/giraffe/walrus/.augmentignore", "*.js\n"],
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.js", accepted],
            ["/giraffe/def.py", rejected],
            ["/giraffe/walrus/ghi.py", rejected],
            ["/giraffe/walrus/jkl.js", rejected],
        ]);

        await runTest(files, "/giraffe", "/giraffe", ignoreFiles);
    });

    // `gitignore-and-augmentignore-nested-override` verifies that lower level ignore files
    // override lower level ignore files
    test("gitignore-and-augmentignore-nested-override", async () => {
        const ignoreFiles = new Map<string, string>([
            ["/giraffe/.gitignore", "*.py\n"],
            ["/giraffe/walrus/.augmentignore", "!ghi.py\n"],
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.js", accepted],
            ["/giraffe/def.py", rejected],
            ["/giraffe/walrus/ghi.py", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe", ignoreFiles);
    });

    // `augmentignore-single-dir` verifies that the iterator honors the contents
    // of an augmentignore file in a single directory
    test("augmentignore-single-dir", async () => {
        const augmentIgnores = new Map<string, string>([["/giraffe/.augmentignore", "*.py\n"]]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.cxx", accepted],
            ["/giraffe/def.py", rejected], // excluded
            ["/giraffe/jkl.py", rejected], // excluded
            ["/giraffe/jkl.js", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe", augmentIgnores);
    });

    // `augmentignore-nested` verifies that the iterator honors augmentignore
    // files in a directory hierarchy
    test("augmentignore-nested", async () => {
        const augmentIgnores = new Map<string, string>([
            ["/giraffe/.augmentignore", "jkl.*\n"], // "A"
            ["/giraffe/walrus/.augmentignore", "*.py\n"], // "B"
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.py", accepted],
            ["/giraffe/jkl.js", rejected], // excluded by A
            ["/giraffe/walrus/def.py", rejected], // excluded by B
            ["/giraffe/walrus/jkl.py", rejected], // excluded by A and B
            ["/giraffe/walrus/mno.js", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe", augmentIgnores);
    });

    // `different-augmentroot-with-augmentignore` verifies that the iterator will
    // honor augmentignore files between the root and the augmentroot, but not those
    // above the augmentroot
    test("different-augmentroot-with-augmentignore", async () => {
        const augmentIgnores = new Map<string, string>([
            ["/.augmentignore", "*.cc\n"], // not honored: above /giraffe
            ["/giraffe/.augmentignore", "*.py\n"], // "A"
            ["/giraffe/walrus/.augmentignore", "*.js\n"], // "B"
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/walrus/crocodile/abc.py", rejected], // excluded by A
            ["/giraffe/walrus/crocodile/def.py", rejected], // excluded by A
            ["/giraffe/walrus/crocodile/xyz.js", rejected], // excluded by B
            ["/giraffe/walrus/crocodile/xyz.cc", accepted],
        ]);

        await runTest(files, "/giraffe/walrus/crocodile", "/giraffe", augmentIgnores);
    });

    // `augmentignore-override-exclude` verifies that the iterator correctly
    // overrides an exclude rule in one file with an include rule in another
    // file in a subdirectory
    test("augmentignore-override-exclude", async () => {
        const augmentIgnores = new Map<string, string>([
            ["/giraffe/.augmentignore", "jkl.*\n"], // "A"
            ["/giraffe/walrus/.augmentignore", "!jkl.*\n"], // "B"
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.py", accepted],
            ["/giraffe/jkl.js", rejected], // excluded by A
            ["/giraffe/walrus/def.py", accepted],
            ["/giraffe/walrus/jkl.py", accepted], // re-included by B
            ["/giraffe/walrus/mno.js", accepted],
        ]);

        await runTest(files, "/giraffe", "/giraffe", augmentIgnores);
    });

    // `augmentignore-override-exclude` verifies that the iterator correctly
    // overrides an include rule in one file with an exclude rule in another
    // file in a subdirectory
    test("augmentignore-override-include", async () => {
        const augmentIgnores = new Map<string, string>([
            ["/.augmentignore", "*.py\n"], // "A"
            ["/giraffe/walrus/.augmentignore", "!*.py\n"], // "B"
            ["/giraffe/walrus/slug/.augmentignore", "*.py\n"], // "C"
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/abc.py", rejected], // excluded by A
            ["/giraffe/walrus/abc.py", accepted], // re-included by B
            ["/giraffe/walrus/slug/abc.py", rejected], // re-excluded by C
        ]);

        await runTest(files, "/", "/", augmentIgnores);
    });

    test("augmentignores-in-subdirs", async () => {
        const augmentIgnores = new Map<string, string>([
            ["/giraffe/shark/.augmentignore", "*.cc\n"], // "A"
            ["/giraffe/slug/.augmentignore", "*.js\n"], // "B"
            ["/giraffe/walrus/.augmentignore", "*.py\n"], // "C"
        ]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/shark/abc.cc", rejected], // excluded by A
            ["/giraffe/shark/abc.js", accepted],
            ["/giraffe/shark/abc.py", accepted],

            ["/giraffe/slug/abc.cc", accepted],
            ["/giraffe/slug/abc.js", rejected], // excluded by B
            ["/giraffe/slug/abc.py", accepted],

            ["/giraffe/walrus/abc.cc", accepted],
            ["/giraffe/walrus/abc.js", accepted], // excluded by C
            ["/giraffe/walrus/abc.py", rejected],
        ]);

        await runTest(files, "/", "/", augmentIgnores);
    });

    // Verify that the iterator correctly applies directory-specific ignore rules.
    test("directory-specific ignore rules", async () => {
        const augmentIgnores = new Map<string, string>([
            ["/giraffe/.gitignore", "abc/xyz.py\n"], // rule A
        ]);

        // The first path in this map is a match for rule A (hence rejected) because it is in the
        // same directory as the ignore file. The others do not match (hence accepted) because they
        // are in different directories.
        const files = new Map<string, PathStatus>([
            ["/giraffe/abc/xyz.py", rejected],
            ["/giraffe/abc/abc/xyz.py", accepted],
            ["/giraffe/abc/abc/abc/xyz.py", accepted],
            ["/giraffe/abc/def/abc/xyz.py", accepted],
        ]);

        await runTest(files, "/", "/", augmentIgnores);
    });

    // `augmentigore-dir-rules` verifies that the iterator correctly applies
    // directory rules
    test("augmentigore-dir-rules", async () => {
        const ignoreFile = `giraffe/manatee/a*
/giraffe/manatee/b*
manatee/c*
/manatee/d*
/e*/
f*/
/**/p*
/**/w*/
`;
        const augmentIgnores = new Map<string, string>([["/.augmentignore", ignoreFile]]);

        const files = new Map<string, PathStatus>([
            ["/aaa.py", accepted],
            ["/bbb.py", accepted],
            ["/ccc.py", accepted],
            ["/ddd.py", accepted],
            ["/eee.py", accepted],
            ["/fff.py", accepted],
            ["/egret/xxx.py", absent], // absent because `/e*/` is ignored
            ["/ferret/xxx.py", absent], // absent because `f*/` is ignored
            ["/ppp.py", rejected],
            ["/www.py", accepted],

            ["/giraffe/aaa.py", accepted],
            ["/giraffe/bbb.py", accepted],
            ["/giraffe/ccc.py", accepted],
            ["/giraffe/ddd.py", accepted],
            ["/giraffe/eee.py", accepted],
            ["/giraffe/fff.py", accepted],
            ["/giraffe/ferret/xxx.py", absent], // absent because `f*/` is ignored
            ["/giraffe/ppp.py", rejected],
            ["/giraffe/www.py", accepted],

            ["/giraffe/manatee/aaa.py", rejected],
            ["/giraffe/manatee/bbb.py", rejected],
            ["/giraffe/manatee/ccc.py", accepted],
            ["/giraffe/manatee/ddd.py", accepted],
            ["/giraffe/manatee/eee.py", accepted],
            ["/giraffe/manatee/fff.py", accepted],

            ["/manatee/aaa.py", accepted],
            ["/manatee/bbb.py", accepted],
            ["/manatee/ccc.py", rejected],
            ["/manatee/ddd.py", rejected],
            ["/manatee/eee.py", accepted],
            ["/manatee/fff.py", accepted],

            ["/porcupine/xxx.py", absent], // absent because `/**/p*/` is ignored

            ["/slug/egret/xxx.py", accepted],
            ["/slug/ferret/xxx.py", absent], // absent because `f*/` is ignored
            ["/slug/eee.py", accepted],
            ["/slug/fff.py", accepted],

            ["/walrus/aaa.py", absent], // absent because `/**/w*/` is ignored
        ]);

        await runTest(files, "/", "/", augmentIgnores);
    });

    // honor-path-extensions verifies that the PathIterator only returns pathNames
    // with extensions that are in the given fileExtensions set.
    test("honor-path-extensions", async () => {
        const pathNames = ["/accept.yes", "/reject.no", "/accept.true", "/reject.false"];

        const fileExtensions = new Set([".yes", ".true"]);

        for (const pathName of pathNames) {
            mockFSUtils.writeFileUtf8(pathName, "", true);
        }

        const pathFilter = await makePathFilter(
            Uri.file("/"),
            Uri.file("/"),
            new IgnoreStackBuilder(),
            fileExtensions
        );

        const pathIterator = new PathIterator("test", Uri.file("/"), Uri.file("/"), pathFilter);

        let count = 0;
        for await (const [_uri, relPath, fileType, acceptance] of pathIterator) {
            if (fileType !== FileType.file) {
                continue;
            }
            expect(relPath.startsWith("accept")).toBe(acceptance.accepted);
            count++;
        }
        expect(count).toBe(4);
    });

    // Verify that ignore rules are case sensitive.
    test("case sensitive", async () => {
        const augmentIgnores = new Map<string, string>([["/giraffe/.gitignore", "foo*\n"]]);

        const files = new Map<string, PathStatus>([
            ["/giraffe/foo.py", rejected],
            ["/giraffe/Foo.py", accepted],
            ["/giraffe/FOO.py", accepted],
        ]);

        await runTest(files, "/", "/", augmentIgnores);
    });

    // Verify that `acceptsPath` and `getPathInfo` reject absolute path names.
    test("absolute path name", async () => {
        const pathFilter = await makePathFilter(
            Uri.file("/"),
            Uri.file("/"),
            new IgnoreStackBuilder(),
            defaultSupportedFileExtensions
        );

        expect(() => pathFilter.acceptsPath("/abc.py")).toThrow();
        expect(() => pathFilter.getPathInfo("/abc.py")).toThrow();
    });
});

describe("gitignore/augmentignore interaction", () => {
    const accepted = PathStatus.accepted;
    const rejected = PathStatus.rejected;

    beforeEach(() => {
        mockFSUtils.reset();
    });

    test.each([
        ["/.augmentignore", [accepted, accepted, accepted]],
        ["/giraffe/.augmentignore", [accepted, accepted, accepted]],
        ["/giraffe/walrus/.augmentignore", [accepted, rejected, accepted]],
    ])(
        "augmentignore overrides rejection",
        async (newIgnoreFileName: string, expectedResults: PathStatus[]) => {
            const ignoreFiles = new Map<string, string>([["/giraffe/.gitignore", "*.py\n"]]);
            ignoreFiles.set(newIgnoreFileName, "!*.py\n");

            const candidatePaths = ["/abc.py", "/giraffe/abc.py", "/giraffe/walrus/abc.py"];

            expect(candidatePaths.length).toBe(expectedResults.length);
            const files = new Map<string, PathStatus>();
            for (let i = 0; i < candidatePaths.length; i++) {
                files.set(candidatePaths[i], expectedResults[i]);
            }

            await runTest(files, "/", "/", ignoreFiles);
        }
    );

    test.each([
        ["/.augmentignore", [rejected, rejected, rejected]],
        ["/giraffe/.augmentignore", [accepted, rejected, rejected]],
        ["/giraffe/walrus/.augmentignore", [accepted, accepted, rejected]],
    ])(
        "augmentignore overrides acceptance",
        async (newIgnoreFileName: string, expectedResults: PathStatus[]) => {
            const ignoreFiles = new Map<string, string>([
                ["/giraffe/.gitignore", "!*.py\n"],
                [newIgnoreFileName, "*.py\n"],
            ]);

            const candidatePaths = ["/abc.py", "/giraffe/abc.py", "/giraffe/walrus/abc.py"];

            expect(candidatePaths.length).toBe(expectedResults.length);
            const files = new Map<string, PathStatus>();
            for (let i = 0; i < candidatePaths.length; i++) {
                files.set(candidatePaths[i], expectedResults[i]);
            }

            await runTest(files, "/", "/", ignoreFiles);
        }
    );
});

describe("built-in ignore rules interact with .gitignore and .augmentignore", () => {
    const accepted = PathStatus.accepted;
    const rejected = PathStatus.rejected;

    beforeEach(() => {
        mockFSUtils.reset();
    });

    test.each([
        ["/.augmentignore", [accepted, accepted, rejected, rejected, accepted, accepted, accepted]],
        [
            "/giraffe/.augmentignore",
            [accepted, accepted, rejected, rejected, accepted, accepted, accepted],
        ],
        [
            "/giraffe/walrus/.augmentignore",
            [accepted, accepted, rejected, rejected, accepted, rejected, accepted],
        ],
    ])(
        "built in rules reject .git and id_ed25519 %s",
        async (newIgnoreFileName: string, expectedResults: PathStatus[]) => {
            const ignoreFiles = new Map<string, string>([["/giraffe/.gitignore", "*.py\n"]]);
            ignoreFiles.set(newIgnoreFileName, "!*.py\n");

            // Note how this is different from the other tests in that we are also including
            // the ignore files in the candidate paths. This is because `bypassLanguageFilter=true`
            // means these files are also included in the iteration.
            const candidatePaths = [
                newIgnoreFileName,
                "/giraffe/.gitignore",
                "/.git",
                "/id_ed25519",
                "/abc.py",
                "/giraffe/abc.py",
                "/giraffe/walrus/abc.py",
            ];

            expect(candidatePaths.length).toBe(expectedResults.length);
            const files = new Map<string, PathStatus>();
            for (let i = 0; i < candidatePaths.length; i++) {
                files.set(candidatePaths[i], expectedResults[i]);
            }

            // Note we pass `bypassLanguageFilter=true` because nowadays we have a feature flag
            // and if the language filter is in place then all of our no-extension files like id_ed25519
            // are ignored, but we want them to be considered for this test.
            await runTest(files, "/", "/", ignoreFiles, undefined, true);
        }
    );

    test.each([
        [
            "/.augmentignore",
            [
                accepted,
                accepted,
                accepted,
                accepted,
                rejected,
                accepted,
                accepted,
                accepted,
                accepted,
            ],
        ],
        [
            "/giraffe/.augmentignore",
            [
                accepted,
                accepted,
                rejected,
                rejected,
                rejected,
                accepted,
                accepted,
                accepted,
                accepted,
            ],
        ],
        [
            "/giraffe/walrus/.augmentignore",
            [
                accepted,
                accepted,
                rejected,
                rejected,
                rejected,
                rejected,
                accepted,
                rejected,
                accepted,
            ],
        ],
    ])(
        ".augmentignore overrides built-in rules %s",
        async (newIgnoreFileName: string, expectedResults: PathStatus[]) => {
            const ignoreFiles = new Map<string, string>([["/giraffe/.gitignore", "*.py\n"]]);
            ignoreFiles.set(newIgnoreFileName, "!*.py\n!.git\n!id_ed25519\n");

            // Note how this is different from the other tests in that we are also including
            // the ignore files in the candidate paths. This is because `bypassLanguageFilter=true`
            // means these files are also included in the iteration.
            const candidatePaths = [
                newIgnoreFileName,
                "/giraffe/.gitignore",
                "/.git",
                "/id_ed25519",
                "/id_rsa",
                "/giraffe/.git",
                "/abc.py",
                "/giraffe/abc.py",
                "/giraffe/walrus/abc.py",
            ];

            expect(candidatePaths.length).toBe(expectedResults.length);
            const files = new Map<string, PathStatus>();
            for (let i = 0; i < candidatePaths.length; i++) {
                files.set(candidatePaths[i], expectedResults[i]);
            }

            // Note we pass `bypassLanguageFilter=true` because nowadays we have a feature flag
            // and if the language filter is in place then all of our no-extension files like id_ed25519
            // are ignored, but we want them to be considered for this test.
            await runTest(files, "/", "/", ignoreFiles, undefined, true);
        }
    );
});

describe("enumerate types and acceptances", () => {
    const accepted = PathStatus.accepted;
    const rejected = PathStatus.rejected;

    beforeEach(() => {
        mockFSUtils.reset();
    });

    test("enumerate types and acceptances", async () => {
        const ignoreFiles = new Map<string, string>([["/giraffe/.gitignore", "*.py\n"]]);
        const files = new Map<string, PathStatus>([
            ["/giraffe/emu.js", accepted],
            ["/giraffe/walrus.py", rejected],
            ["/giraffe/kangaroo/dolphin.js", accepted],
        ]);

        const nonFiles = new Map<string, [FileType, PathStatus]>([
            ["/giraffe", [FileType.directory, accepted]],
            ["/giraffe/kangaroo", [FileType.directory, accepted]],
        ]);

        await runTest(files, "/", "/", ignoreFiles, nonFiles);
    });
});
