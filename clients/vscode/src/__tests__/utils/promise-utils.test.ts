import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import {
    retryWithBackoff,
    withTimeout,
} from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";

import { getLogger } from "../../logging";

describe("promise-utils", () => {
    const logger = getLogger("promise-utils");

    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    describe("retryWithBackoff", () => {
        test("success", async () => {
            let count = 0;
            const fn = () => {
                count++;
                return Promise.resolve(count);
            };
            const result = await retryWithBackoff(fn, logger, {
                initialMS: 100,
                mult: 2,
                maxMS: 30000,
            });
            expect(result).toEqual(1);
            expect(count).toEqual(1);
        });

        test("failure, no retries", async () => {
            let count = 0;
            const error = new APIError(APIStatus.cancelled, "Injected error");
            const fn = () => {
                count++;
                return Promise.reject(error);
            };
            await expect(
                retryWithBackoff(fn, logger, {
                    initialMS: 100,
                    mult: 2,
                    maxMS: 30000,
                    maxTries: 0,
                })
            ).rejects.toThrow(error);
            expect(count).toEqual(1);
        });

        test("failure, retries", async () => {
            let count = 0;
            const error = new APIError(APIStatus.cancelled, "Injected error");
            const fn = () => {
                count++;
                return Promise.reject(error);
            };

            // Set up async expectation
            const expectation = expect(
                retryWithBackoff(fn, logger, {
                    initialMS: 100,
                    mult: 2,
                    maxMS: 1000,
                    maxTries: 3,
                })
            ).rejects.toThrow(error);

            // Advance time
            await jest.advanceTimersByTimeAsync(3000);

            // Verify
            await expectation;
            expect(count).toEqual(3);
        });

        test("failure, retries, max total time", async () => {
            let count = 0;
            const error = new APIError(APIStatus.cancelled, "Injected error");
            const fn = () => {
                count++;
                return Promise.reject(error);
            };

            // Set up async expectation
            const expectation = expect(
                retryWithBackoff(fn, logger, {
                    initialMS: 500,
                    mult: 2,
                    maxMS: 1000,
                    maxTotalMs: 1000,
                })
            ).rejects.toThrow(error);

            // Advance time
            await jest.advanceTimersByTimeAsync(3000);

            // Verify
            await expectation;
            expect(count).toEqual(2);
        });

        test("failure, retries, max total time, no retries", async () => {
            let count = 0;
            const error = new APIError(APIStatus.cancelled, "Injected error");
            const fn = () => {
                count++;
                return Promise.reject(error);
            };

            // Set up async expectation
            const expectation = expect(
                retryWithBackoff(fn, logger, {
                    initialMS: 500,
                    mult: 2,
                    maxMS: 1000,
                    maxTotalMs: 1000,
                    maxTries: 0,
                })
            ).rejects.toThrow(error);

            // Advance time
            await jest.advanceTimersByTimeAsync(3000);

            // Verify
            await expectation;
            expect(count).toEqual(1);
        });

        test("cenRetry false", async () => {
            let count = 0;
            const error = new APIError(APIStatus.cancelled, "Injected error");
            const fn = () => {
                count++;
                return Promise.reject(error);
            };

            // Set up async expectation
            const expectation = expect(
                retryWithBackoff(fn, logger, {
                    initialMS: 500,
                    mult: 2,
                    maxMS: 1000,
                    maxTotalMs: 1000,
                    canRetry: () => false,
                })
            ).rejects.toThrow(error);

            // Advance time
            await jest.advanceTimersByTimeAsync(3000);

            // Verify
            await expectation;
            expect(count).toEqual(1);
        });

        test("cenRetry true", async () => {
            let count = 0;
            const error = new APIError(APIStatus.cancelled, "Injected error");
            const fn = () => {
                count++;
                return Promise.reject(error);
            };

            // Set up async expectation
            const expectation = expect(
                retryWithBackoff(fn, logger, {
                    initialMS: 500,
                    mult: 2,
                    maxMS: 1000,
                    maxTotalMs: 1000,
                    canRetry: () => true,
                })
            ).rejects.toThrow(error);

            // Advance time
            await jest.advanceTimersByTimeAsync(3000);

            // Verify
            await expectation;
            expect(count).toEqual(2);
        });
    });

    describe("withTimeout", () => {
        test("resolves when promise resolves before timeout", async () => {
            const promise = Promise.resolve("success");
            const result = await withTimeout(promise, 1000);
            expect(result).toBe("success");
        });

        test("rejects when promise rejects before timeout", async () => {
            const error = new Error("promise error");
            const promise = Promise.reject(error);
            await expect(withTimeout(promise, 1000)).rejects.toThrow(error);
        });

        test("rejects with timeout error when promise takes too long", async () => {
            const promise = new Promise((resolve) => {
                setTimeout(() => resolve("success"), 2000);
            });

            const timeoutPromise = withTimeout(promise, 1000);

            // Advance time to trigger timeout
            jest.advanceTimersByTime(1000);

            await expect(timeoutPromise).rejects.toThrow("Execution aborted due to timeout.");
        });

        test("clears timeout when promise resolves", async () => {
            const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");
            const promise = Promise.resolve("success");

            await withTimeout(promise, 1000);

            expect(clearTimeoutSpy).toHaveBeenCalled();
            clearTimeoutSpy.mockRestore();
        });

        test("clears timeout when promise rejects", async () => {
            const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");
            const promise = Promise.reject(new Error("error"));

            try {
                await withTimeout(promise, 1000);
            } catch (e) {
                // Expected to throw
            }

            expect(clearTimeoutSpy).toHaveBeenCalled();
            clearTimeoutSpy.mockRestore();
        });
    });
});
