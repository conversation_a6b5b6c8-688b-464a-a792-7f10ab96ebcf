import { ExtensionContext, resetMockWorkspace } from "../../__mocks__/vscode-mocks";
import {
    ActionsModel,
    DerivedState,
    SystemState,
} from "../../main-panel/action-cards/actions-model";
import { DerivedStateName } from "../../main-panel/action-cards/types";
import { AugmentGlobalState, GlobalContextKey } from "../../utils/context";
import { SystemStateName, SystemStatus } from "../../utils/types";

describe("ActionsModel", () => {
    let actionsModel: ActionsModel;
    let globalState: AugmentGlobalState;
    let context: ExtensionContext;

    beforeEach(() => {
        resetMockWorkspace();
        context = new ExtensionContext();
        globalState = new AugmentGlobalState(context);
        actionsModel = new ActionsModel(
            globalState,
            new Map<SystemStateName, SystemState>(),
            new Map<DerivedStateName, DerivedState>()
        );
    });

    test("derived state lifecycle", () => {
        const systemState = {
            name: SystemStateName.authenticated,
            status: SystemStatus.complete,
        };
        const derivedState: DerivedState = {
            name: DerivedStateName.disableCopilot,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.complete },
            ],
            renderOrder: 0,
        };

        // False before registering state
        expect(actionsModel.isDerivedStateSatisfied(derivedState.name)).toEqual(false);

        // Set event listener
        const stateChangeListener = jest.fn();
        actionsModel.onDerivedStatesSatisfied(stateChangeListener);
        expect(actionsModel.isDerivedStateSatisfied(derivedState.name)).toEqual(false);

        // Add new derived state and ensure it's incomplete to start with
        actionsModel.addDerivedState(derivedState);

        // Check new state doesn't trigger an event (as it's incomplete)
        expect(stateChangeListener).not.toHaveBeenCalled();

        // Update the system state used in state
        actionsModel.setSystemStateStatus(systemState.name, systemState.status);
        expect(actionsModel.getSystemState(systemState.name)).toEqual(systemState);
        expect(actionsModel.isDerivedStateSatisfied(derivedState.name)).toEqual(true);

        // Check that the event was triggered because the derived state is valid.
        expect(stateChangeListener).toHaveBeenCalledTimes(1);
        expect(stateChangeListener).toHaveBeenCalledWith(
            expect.arrayContaining([expect.objectContaining({ name: derivedState.name })])
        );

        expect(actionsModel.getSystemState(systemState.name)).toEqual(systemState);
        expect(actionsModel.isDerivedStateSatisfied(derivedState.name)).toEqual(true);

        // Update the system state to incomplete
        actionsModel.setSystemStateStatus(systemState.name, SystemStatus.incomplete);
        expect(actionsModel.getSystemState(systemState.name)).toEqual({
            name: SystemStateName.authenticated,
            status: SystemStatus.incomplete,
        });
        expect(actionsModel.isDerivedStateSatisfied(derivedState.name)).toEqual(false);

        // Check that the event was triggered because the derived state is now invalid.
        expect(stateChangeListener).toHaveBeenCalledTimes(2);
        expect(stateChangeListener).toHaveBeenCalledWith([]);
    });

    test("init state works", () => {
        // Update the system state used in state1 before we even declare it
        actionsModel.setSystemStateStatus(SystemStateName.authenticated, SystemStatus.incomplete);

        const state1: DerivedState = {
            name: DerivedStateName.disableCopilot,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.incomplete },
            ],
            renderOrder: 0,
        };

        // Set event listener
        const stateChangeListener = jest.fn();
        actionsModel.onDerivedStatesSatisfied(stateChangeListener);

        // Add new derived state and ensure it's incomplete to start with
        actionsModel.addDerivedState(state1);

        // Check that the event was triggered
        expect(stateChangeListener).toHaveBeenCalledTimes(1);
    });

    describe("saveSystemStates", () => {
        it("should save system states to global state", () => {
            // This should implicitly save state.
            actionsModel.setSystemStateStatus(SystemStateName.authenticated, SystemStatus.complete);

            const savedData = globalState.get(GlobalContextKey.actionSystemStates);
            expect(savedData).toBeDefined();
            if (savedData) {
                const parsedData = JSON.parse(savedData as string);
                expect(parsedData).toEqual(
                    expect.arrayContaining([
                        [
                            SystemStateName.authenticated,
                            { name: SystemStateName.authenticated, status: SystemStatus.complete },
                        ],
                    ])
                );
            }
        });
    });

    describe("loadSystemStates", () => {
        it("should load system states from global state", () => {
            const mockPersistedStates = JSON.stringify([
                [
                    SystemStateName.authenticated,
                    { name: SystemStateName.authenticated, status: SystemStatus.complete },
                ],
                [
                    SystemStateName.disabledGithubCopilot,
                    {
                        name: SystemStateName.disabledGithubCopilot,
                        status: SystemStatus.incomplete,
                    },
                ],
            ]);

            globalState.update(GlobalContextKey.actionSystemStates, mockPersistedStates);

            // Create a new ActionsModel instance to trigger loadSystemStates
            const newActionsModel = new ActionsModel(new AugmentGlobalState(context));

            expect(newActionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
                name: SystemStateName.authenticated,
                status: SystemStatus.complete,
            });
            expect(newActionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
                name: SystemStateName.disabledGithubCopilot,
                status: SystemStatus.incomplete,
            });
        });

        it("should use default states when no persisted states exist", () => {
            // Ensure no persisted states
            globalState.update(GlobalContextKey.actionSystemStates, undefined);

            // Create a new ActionsModel instance to trigger loadSystemStates
            const newActionsModel = new ActionsModel(new AugmentGlobalState(context));

            expect(newActionsModel.getSystemState(SystemStateName.authenticated)).toEqual({
                name: SystemStateName.authenticated,
                status: SystemStatus.initializing,
            });
            expect(newActionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
                name: SystemStateName.disabledGithubCopilot,
                status: SystemStatus.initializing,
            });
        });
    });
});

// Test ActionsModel with the default states
describe("ActionsModel Default Values", () => {
    let globalState: AugmentGlobalState;
    let context: ExtensionContext;

    beforeEach(() => {
        resetMockWorkspace();
        context = new ExtensionContext();
        globalState = new AugmentGlobalState(context);
    });

    test("ensure default states are set", () => {
        const actionsModel = new ActionsModel(globalState);
        expect(actionsModel.getSystemState(SystemStateName.authenticated)).toBeDefined();
        expect(actionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toBeDefined();

        expect(actionsModel.isDerivedStateSatisfied("UserShouldSignIn")).toBe(false);
        expect(actionsModel.isDerivedStateSatisfied("ShouldDisableCopilot")).toBe(false);
    });
});
