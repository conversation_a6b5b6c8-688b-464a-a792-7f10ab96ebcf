import { measureExecTime } from "../../utils/performance";

describe("measureExecTime", () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("measure function returning string", () => {
        let spy = jest.fn();

        const cb = () => "hello";
        const measure = measureExecTime(cb, spy);
        const got = measure();
        expect(got).toEqual("hello");

        expect(spy).toHaveBeenCalledTimes(1);
        expect(spy).toHaveBeenCalledWith(0);
    });

    test("do not measure function throwing error", () => {
        let spy = jest.fn();

        const cb = () => {
            throw new Error("Injected error");
        };
        const measure = measureExecTime(cb, spy);
        expect(measure).toThrow("Injected error");

        expect(spy).toHaveBeenCalledTimes(0);
    });

    test("measure function returning promise", async () => {
        let spy = jest.fn();

        const DURATION = 10;
        const cb = () => {
            return new Promise((resolve) => {
                setTimeout(() => resolve("hello"), DURATION);
            });
        };
        const measure = measureExecTime(cb, spy);
        const measurePromise = measure();
        jest.advanceTimersByTime(DURATION);
        const result = await measurePromise;
        expect(result).toEqual("hello");

        expect(spy).toHaveBeenCalledTimes(1);
        expect(spy).toHaveBeenCalledWith(10);
    });

    test("do not measure function returning rejecting promise", async () => {
        let spy = jest.fn();

        const DURATION = 10;
        const cb = () => {
            return new Promise((_, reject) => {
                setTimeout(() => reject("Injected error"), DURATION);
            });
        };
        const measure = measureExecTime(cb, spy);
        const measurePromise = measure();
        jest.advanceTimersByTime(DURATION);
        expect(measurePromise).rejects.toEqual("Injected error");

        expect(spy).toHaveBeenCalledTimes(0);
    });

    test("measure function returning value from class method", () => {
        let spy = jest.fn();

        const foo = new Foo();
        const measure = measureExecTime(foo.getBar.bind(foo), spy);
        const got = measure();
        expect(got).toEqual("hello");

        expect(spy).toHaveBeenCalledTimes(1);
        expect(spy).toHaveBeenCalledWith(0);
    });
});

class Foo {
    readonly bar: string = "hello";

    getBar(): string {
        return this.bar;
    }
}
