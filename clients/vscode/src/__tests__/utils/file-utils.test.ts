import { directoryExists, mockFSUtils } from "../../__mocks__/fs-utils";
import { getLogger } from "../../__mocks__/logging";
import { resetMockWorkspace } from "../../__mocks__/vscode-mocks";
import { withFileBackup } from "../../utils/file-utils";

describe("withFileBackup", () => {
    const mockLogger = getLogger("file-utils-test");

    beforeEach(() => {
        mockFSUtils.reset();
        resetMockWorkspace();
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe("when file exists originally", () => {
        test("should backup and restore file content", async () => {
            const filePath = "/test/settings.json";
            const originalContent = '{"original": true}';
            const modifiedContent = '{"modified": true}';

            // Setup: create directories and original file
            mockFSUtils.createDirectories("/test");
            mockFSUtils.writeFileUtf8(filePath, originalContent);
            const result = await withFileBackup(filePath, mockLogger, async () => {
                // Modify the file during execution
                mockFSUtils.writeFileUtf8(filePath, modifiedContent);
                return "test-result";
            });

            // Verify function result
            expect(result).toBe("test-result");

            // Verify file was restored to original content
            const finalContent = mockFSUtils.readFileUtf8(filePath);
            expect(finalContent).toBe(originalContent);
        });

        test("should handle function that throws error", async () => {
            const filePath = "/test/settings.json";
            const originalContent = '{"original": true}';

            // Setup: create directories and original file
            mockFSUtils.createDirectories("/test");
            mockFSUtils.writeFileUtf8(filePath, originalContent);

            const testError = new Error("Test error");
            await expect(
                withFileBackup(filePath, mockLogger, async () => {
                    mockFSUtils.writeFileUtf8(filePath, '{"modified": true}');
                    throw testError;
                })
            ).rejects.toThrow("Test error");

            // Verify file was restored despite error
            const finalContent = mockFSUtils.readFileUtf8(filePath);
            expect(finalContent).toBe(originalContent);
        });
    });

    describe("when file does not exist originally", () => {
        test("should remove created file and empty parent directories", async () => {
            const filePath = "/test/.vscode/settings.json";

            const result = await withFileBackup(filePath, mockLogger, async () => {
                // Create directories and file during execution
                mockFSUtils.writeFileUtf8(filePath, '{"created": true}', true);
                return "test-result";
            });

            // Verify function result
            expect(result).toBe("test-result");

            // Verify file was removed
            expect(() => mockFSUtils.readFileUtf8(filePath)).toThrow();

            // Check if directory still exists
            expect(directoryExists("/test/.vscode")).toBe(false);
        });

        test("should not remove non-empty parent directories", async () => {
            const filePath = "/test/.vscode/settings.json";
            const otherFile = "/test/.vscode/other.json";

            const result = await withFileBackup(filePath, mockLogger, async () => {
                // Create directories and files during execution
                mockFSUtils.writeFileUtf8(filePath, '{"created": true}', true);
                mockFSUtils.writeFileUtf8(otherFile, '{"other": true}', true);
                return "test-result";
            });

            // Verify function result
            expect(result).toBe("test-result");

            // Verify target file was removed
            expect(() => mockFSUtils.readFileUtf8(filePath)).toThrow();

            // Verify other file still exists
            expect(mockFSUtils.readFileUtf8(otherFile)).toBe('{"other": true}');

            // Verify parent directory was not removed (because it's not empty)
            expect(directoryExists("/test/.vscode")).toBe(true);
        });

        test("should stop cleanup at first existing parent directory", async () => {
            const filePath = "/existing/new-dir/settings.json";

            // Setup: create existing parent directory
            mockFSUtils.createDirectories("/existing");

            const result = await withFileBackup(filePath, mockLogger, async () => {
                // Create new directory and file during execution
                mockFSUtils.writeFileUtf8(filePath, '{"created": true}', true);
                return "test-result";
            });

            // Verify function result
            expect(result).toBe("test-result");

            // Verify file was removed
            expect(() => mockFSUtils.readFileUtf8(filePath)).toThrow();

            // Verify only the new directory was removed, not the existing one
            expect(directoryExists("/existing/new-dir")).toBe(false);
            expect(directoryExists("/existing")).toBe(true);
        });
    });

    describe("edge cases", () => {
        test("should handle file reading failure", async () => {
            const filePath = "/test/settings.json";
            const originalContent = '{"original": true}';

            // Setup: create directories and original file
            mockFSUtils.createDirectories("/test");
            mockFSUtils.writeFileUtf8(filePath, originalContent);

            // Mock readFileUtf8 to fail when reading original content
            const originalReadFileUtf8 = mockFSUtils.readFileUtf8;
            jest.spyOn(mockFSUtils, "readFileUtf8").mockImplementation((path) => {
                if (path === filePath) {
                    throw new Error("Read failed");
                }
                return originalReadFileUtf8.call(mockFSUtils, path);
            });

            await expect(
                withFileBackup(filePath, mockLogger, async () => {
                    return "test-result";
                })
            ).rejects.toThrow("Read failed");
        });

        test("should handle restoration failure gracefully", async () => {
            const filePath = "/test/settings.json";
            const originalContent = '{"original": true}';

            // Setup: create directories and original file
            mockFSUtils.createDirectories("/test");
            mockFSUtils.writeFileUtf8(filePath, originalContent);

            // Mock writeFileUtf8 to fail during restoration
            const originalWriteFileUtf8 = mockFSUtils.writeFileUtf8;
            let callCount = 0;
            jest.spyOn(mockFSUtils, "writeFileUtf8").mockImplementation((path, content) => {
                callCount++;
                if (callCount === 2 && path === filePath) {
                    // Fail on restoration attempt (2nd call: modify, restore)
                    throw new Error("Restoration failed");
                }
                return originalWriteFileUtf8.call(mockFSUtils, path, content);
            });

            const result = await withFileBackup(filePath, mockLogger, async () => {
                mockFSUtils.writeFileUtf8(filePath, '{"modified": true}');
                return "test-result";
            });

            // Function should still return result
            expect(result).toBe("test-result");
        });
    });
});
