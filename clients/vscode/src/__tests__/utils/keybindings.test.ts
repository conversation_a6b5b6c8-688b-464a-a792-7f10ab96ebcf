import fs from "fs";
import os from "os";
import * as vscode from "vscode";

import {
    ExtensionContext,
    publishTextDocumentSaved,
    TextDocument,
} from "../../__mocks__/vscode-mocks";
import { AugmentGlobalState } from "../../utils/context";
import * as Environment from "../../utils/environment";
import { KeybindingWatcher, SimplifiedPlatform } from "../../utils/keybindings";

describe("KeybindingWatcher", () => {
    let keybindingWatcher: KeybindingWatcher | undefined = undefined;

    let augmentGlobalState: AugmentGlobalState;

    let getUserDirectorySpy: jest.SpyInstance;
    let getAugmentExtensionPackageJsonSpy: jest.SpyInstance;

    beforeEach(() => {
        jest.useFakeTimers();
        augmentGlobalState = new AugmentGlobalState(new ExtensionContext());

        getUserDirectorySpy = jest.spyOn(Environment, "getUserDirectory");
        getAugmentExtensionPackageJsonSpy = jest.spyOn(
            Environment,
            "getAugmentExtensionPackageJson"
        );
    });

    afterEach(() => {
        keybindingWatcher?.dispose();
        keybindingWatcher = undefined;
        jest.restoreAllMocks();
        jest.useRealTimers();
    });

    describe("getKeybindingForCommand", () => {
        describe("handles errors", () => {
            test("do not throw on an empty file", () => {
                jest.spyOn(os, "platform").mockReturnValue("win32");
                jest.spyOn(fs, "watch").mockReturnValue({
                    close: jest.fn(),
                } as unknown as fs.FSWatcher);
                getUserDirectorySpy.mockReturnValue("user-directory");
                jest.spyOn(fs, "existsSync").mockReturnValue(true);
                jest.spyOn(fs, "readFileSync").mockReturnValue("");

                expect(() => {
                    new KeybindingWatcher(augmentGlobalState);
                }).not.toThrow();
            });

            test("do not throw on non-JSON files", () => {
                jest.spyOn(os, "platform").mockReturnValue("win32");
                jest.spyOn(fs, "watch").mockReturnValue({
                    close: jest.fn(),
                } as unknown as fs.FSWatcher);
                getUserDirectorySpy.mockReturnValue("user-directory");
                jest.spyOn(fs, "existsSync").mockReturnValue(true);
                jest.spyOn(fs, "readFileSync").mockReturnValue("This is not valid JSON");

                expect(() => {
                    new KeybindingWatcher(augmentGlobalState);
                }).not.toThrow();
            });
        });

        describe("returns null", () => {
            test("if remote", () => {
                jest.replaceProperty(vscode.env, "remoteName", "remote-instance");
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [
                            {
                                command: "vscode-augment.exampleCommand",
                                key: "ctrl+alt+e",
                                mac: "cmd+alt+e",
                            },
                        ],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBeNull();
            });

            test("if web", () => {
                jest.replaceProperty(vscode.env, "uiKind", vscode.UIKind.Web);
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [
                            {
                                command: "vscode-augment.exampleCommand",
                                key: "ctrl+alt+e",
                                mac: "cmd+alt+e",
                            },
                        ],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBeNull();
            });

            test("if no keybinding is found", () => {
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(keybindingWatcher.getKeybindingForCommand("nonexistent.command")).toBeNull();
            });
        });

        test("sets up a watcher for user keybindings", () => {
            jest.spyOn(os, "platform").mockReturnValue("win32");
            jest.spyOn(fs, "watch").mockReturnValue({
                close: jest.fn(),
            } as unknown as fs.FSWatcher);
            getUserDirectorySpy.mockReturnValue("user-directory");
            getAugmentExtensionPackageJsonSpy.mockReturnValue({
                contributes: {
                    keybindings: [],
                },
            });

            keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
            expect(fs.watch).toHaveBeenCalled();
        });

        describe("returns keybinding", () => {
            test("if command not found in default keybindings", () => {
                jest.spyOn(os, "platform").mockReturnValue("win32");
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBe(null);
            });

            test("if found in default keybindings", () => {
                jest.spyOn(os, "platform").mockReturnValue("win32");
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [
                            {
                                command: "vscode-augment.exampleCommand",
                                key: "ctrl+alt+e",
                                mac: "cmd+alt+e",
                            },
                        ],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBe("ctrl+alt+e");
            });

            test("if found in default keybindings on mac", () => {
                jest.spyOn(os, "platform").mockReturnValue("darwin");
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [
                            {
                                command: "vscode-augment.exampleCommand",
                                key: "ctrl+alt+e",
                                mac: "cmd+alt+e",
                            },
                        ],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBe("cmd+alt+e");
            });

            test("if found in user keybindings", () => {
                jest.spyOn(os, "platform").mockReturnValue("win32");
                jest.spyOn(fs, "watch").mockReturnValue({
                    close: jest.fn(),
                } as unknown as fs.FSWatcher);
                jest.spyOn(fs, "existsSync").mockReturnValue(true);
                jest.spyOn(fs, "readFileSync").mockReturnValue(
                    JSON.stringify([
                        {
                            command: "vscode-augment.exampleCommand",
                            key: "ctrl+alt+e",
                            mac: "cmd+alt+e",
                        },
                    ])
                );
                getUserDirectorySpy.mockReturnValue("user-directory");
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBe("ctrl+alt+e");
            });

            test("if unbound in user keybindings", () => {
                jest.spyOn(os, "platform").mockReturnValue("win32");
                jest.spyOn(fs, "watch").mockReturnValue({
                    close: jest.fn(),
                } as unknown as fs.FSWatcher);
                jest.spyOn(fs, "existsSync").mockReturnValue(true);
                jest.spyOn(fs, "readFileSync").mockReturnValue(
                    JSON.stringify([
                        {
                            command: "-vscode-augment.exampleCommand",
                            key: "ctrl+alt+e",
                            mac: "cmd+alt+e",
                        },
                    ])
                );
                getUserDirectorySpy.mockReturnValue("user-directory");
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [
                            {
                                command: "vscode-augment.exampleCommand",
                                key: "f4",
                                mac: "f4",
                            },
                        ],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBeUndefined();
            });

            test("if remote and same key on pc and mac", () => {
                jest.replaceProperty(vscode.env, "remoteName", "remote-instance");
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [
                            {
                                command: "vscode-augment.exampleCommand",
                                key: "f4",
                                mac: "f4",
                            },
                        ],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBe("f4");
            });

            test("if web and same key on pc and mac", () => {
                jest.replaceProperty(vscode.env, "uiKind", vscode.UIKind.Web);
                getUserDirectorySpy.mockReturnValue(null);
                getAugmentExtensionPackageJsonSpy.mockReturnValue({
                    contributes: {
                        keybindings: [
                            {
                                command: "vscode-augment.exampleCommand",
                                key: "f2",
                                mac: "f2",
                            },
                        ],
                    },
                });

                keybindingWatcher = new KeybindingWatcher(augmentGlobalState);
                expect(
                    keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                ).toBe("f2");
            });

            test.each([
                ["/home/<USER>/.config/Code/User/keybindings.json", "linux"],
                ["/Users/<USER>/Library/Application Support/Code/User/keybindings.json", "darwin"],
                ["/c:/Users/<USER>/AppData/Roaming/Code/User/keybindings.json", "win32"],
            ])(
                "if remote after changing keybindings(%s)",
                async (keybindingFileName: string, platform: string) => {
                    jest.replaceProperty(vscode.env, "remoteName", "remote-instance");
                    getUserDirectorySpy.mockReturnValue(null);
                    getAugmentExtensionPackageJsonSpy.mockReturnValue({
                        contributes: {
                            keybindings: [
                                {
                                    command: "vscode-augment.exampleCommand",
                                    key: "ctrl+alt+e",
                                    mac: "cmd+alt+e",
                                },
                            ],
                        },
                    });

                    keybindingWatcher = new KeybindingWatcher(augmentGlobalState);

                    publishTextDocumentSaved(
                        new TextDocument(
                            vscode.Uri.from({
                                scheme: "vscode-userdata",
                                path: keybindingFileName,
                            }),
                            JSON.stringify([
                                {
                                    command: "vscode-augment.exampleCommand",
                                    key: "ctrl+alt+f",
                                    mac: "cmd+alt+f",
                                },
                            ])
                        )
                    );

                    const expectedResult = platform === "darwin" ? "cmd+alt+f" : "ctrl+alt+f";
                    expect(
                        keybindingWatcher.getKeybindingForCommand("vscode-augment.exampleCommand")
                    ).toBe(expectedResult);
                }
            );

            test.each([
                ["/home/<USER>/.config/Code/User/keybindings.json", "linux"],
                ["/Users/<USER>/Library/Application Support/Code/User/keybindings.json", "darwin"],
                ["/c:/Users/<USER>/AppData/Roaming/Code/User/keybindings.json", "win32"],
            ])(
                "if remote platform detection(%s)",
                async (keybindingFileName: string, platform: string) => {
                    keybindingWatcher = new KeybindingWatcher(augmentGlobalState);

                    const result = (keybindingWatcher as any).getPlatformFromFilename(
                        keybindingFileName
                    );

                    expect(result).toBe(platform);
                }
            );
        });
    });

    describe("formatKeyboardShortcut", () => {
        test("keybinding is undefined", () => {
            expect(KeybindingWatcher.formatKeyboardShortcut(undefined, "darwin")).toBe("");
        });

        test("keybinding is empty", () => {
            expect(KeybindingWatcher.formatKeyboardShortcut("", "darwin")).toBe("");
        });

        test("keybinding is unparseable", () => {
            expect(KeybindingWatcher.formatKeyboardShortcut("@#$(&@#$(&%@()\\\\", "darwin")).toBe(
                ""
            );
        });

        test.each([
            ["ctrl+e", "darwin", "⌃E"],
            ["ctrl+e", "linux", "Ctrl+E"],
            ["ctrl+e", "win32", "Ctrl+E"],
            ["alt+e", "darwin", "⌥E"],
            ["alt+e", "linux", "Alt+E"],
            ["alt+e", "win32", "Alt+E"],
            ["shift+e", "darwin", "⇧E"],
            ["shift+e", "linux", "⇧E"],
            ["shift+e", "win32", "⇧E"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "single modifier keybinding %s on %s is %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
                expect(
                    KeybindingWatcher.formatKeyboardShortcut(keybinding.replace("+", "-"), platform)
                ).toBe(expected);
            }
        );

        test.each([
            ["meta+e", "darwin", "⌘E"],
            ["meta+e", "linux", "Meta+E"],
            ["meta+e", "win32", "Win+E"],
            ["cmd+e", "darwin", "⌘E"],
            ["cmd+e", "linux", "Meta+E"],
            ["cmd+e", "win32", "Win+E"],
            ["win+e", "darwin", "⌘E"],
            ["win+e", "linux", "Meta+E"],
            ["win+e", "win32", "Win+E"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "meta comes out correctly for %s on %s: %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
                expect(
                    KeybindingWatcher.formatKeyboardShortcut(keybinding.replace("+", "-"), platform)
                ).toBe(expected);
            }
        );

        test.each([
            ["ctrl+meta+e", "darwin", "⌃⌘E"],
            ["ctrl+meta+e", "linux", "Ctrl+Meta+E"],
            ["ctrl+meta+e", "win32", "Ctrl+Win+E"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "2 modifier keybinding %s on %s is %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
                expect(
                    KeybindingWatcher.formatKeyboardShortcut(keybinding.replace("+", "-"), platform)
                ).toBe(expected);
            }
        );

        test.each([
            ["ctrl+shift+e", "darwin", "⌃⇧E"],
            ["ctrl+shift+e", "linux", "Ctrl+⇧+E"],
            ["ctrl+shift+e", "win32", "Ctrl+⇧+E"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "2 modifier keybinding %s on %s is %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
                expect(
                    KeybindingWatcher.formatKeyboardShortcut(keybinding.replace("+", "-"), platform)
                ).toBe(expected);
            }
        );

        test.each([
            ["cmd+alt+ctrl+shift+e", "darwin", "⌃⇧⌥⌘E"],
            ["meta+alt+ctrl+shift+e", "linux", "Ctrl+⇧+Alt+Meta+E"],
            ["win+alt+ctrl+shift+e", "win32", "Ctrl+⇧+Alt+Win+E"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "4 modifier keybinding %s on %s is %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
                expect(
                    KeybindingWatcher.formatKeyboardShortcut(keybinding.replace("+", "-"), platform)
                ).toBe(expected);
            }
        );

        test.each([
            ["alt+ctrl+shift+e", "darwin", "⌃⇧⌥E"],
            ["alt+ctrl+shift+e", "linux", "Ctrl+⇧+Alt+E"],
            ["alt+ctrl+shift+e", "win32", "Ctrl+⇧+Alt+E"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "3 modifier keybinding %s on %s is %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
            }
        );

        test.each([
            ["alt+ctrl-shift+e", "darwin", "⌃⇧⌥E"],
            ["alt-ctrl+shift-e", "linux", "Ctrl+⇧+Alt+E"],
            ["alt+ctrl+shift-e", "win32", "Ctrl+⇧+Alt+E"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "3 modifier keybinding with mix of plus and minus %s on %s is %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
            }
        );

        test.each([
            ["alt+ctrl-shift+e ctrl+i", "darwin", "⌃⇧⌥E ⌃I"],
            ["alt-ctrl+shift-e ctrl+i", "linux", "Ctrl+⇧+Alt+E Ctrl+I"],
            ["alt+ctrl+shift-e ctrl+i", "win32", "Ctrl+⇧+Alt+E Ctrl+I"],
        ] as Array<[string, SimplifiedPlatform, string]>)(
            "multi chord keybindings %s on %s is %s",
            (keybinding: string, platform: SimplifiedPlatform, expected: string) => {
                expect(KeybindingWatcher.formatKeyboardShortcut(keybinding, platform)).toBe(
                    expected
                );
            }
        );
    });
});
