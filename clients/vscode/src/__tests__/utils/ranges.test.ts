import { LineRange } from "../../utils/ranges";

describe("Range", () => {
    test.each([
        [new LineRange(0, 10), new LineRange(2, 3), true],
        [new LineRange(0, 1), new LineRange(1, 1), false],
    ])("contains(%s, %s)", (range1, range2, expected) => {
        expect(range1.contains(range2)).toBe(expected);
    });
});

describe("LineRange", () => {
    test.each([
        // point ranges are still unioned into the full set.
        [
            [new LineRange(0, 0), new LineRange(8, 10)],
            [new LineRange(0, 0), new LineRange(8, 10)],
        ],
        // Disjoint ranges
        [
            [new LineRange(0, 2), new LineRange(8, 10)],
            [new LineRange(0, 2), new LineRange(8, 10)],
        ],
        // Adjacent ranges
        [[new LineRange(0, 5), new LineRange(5, 10)], [new LineRange(0, 10)]],
        // Overlapping ranges
        [[new LineRange(0, 6), new LineRange(4, 10)], [new LineRange(0, 10)]],
        // Contained ranges
        [[new LineRange(0, 10), new LineRange(2, 8)], [new LineRange(0, 10)]],
    ])("mergeTouching(%s) should return %s", (ranges, expected) => {
        expect(LineRange.mergeTouching(ranges)).toEqual(expected);
    });

    test("mergeTouching with randomized input", () => {
        for (let i = 0; i < 500; i++) {
            const r1 = randomLineRange();
            const r2 = randomLineRange();
            const r3 = randomLineRange();
            const r4 = randomLineRange();
            const rangesToMerge = [r1, r2, r3, r4];
            const merged = LineRange.mergeTouching(rangesToMerge);

            // number of ranges can only decrease
            expect(merged.length).toBeLessThanOrEqual(rangesToMerge.length);

            // no merged ranges should touch
            for (let j = 0; j < merged.length; j++) {
                for (let k = j + 1; k < merged.length; k++) {
                    expect(merged[j].touches(merged[k])).toBe(false);
                }
            }

            // every range_to_merge should be contained in one of the merged ranges
            for (const r of rangesToMerge) {
                expect(merged.some((m) => m.contains(r))).toBe(true);
            }

            // every merged range should contain at least one of the ranges_to_merge
            for (const m of merged) {
                expect(rangesToMerge.some((r) => m.contains(r))).toBe(true);
            }
        }
    });
});

function randomLineRange(): LineRange {
    const start = Math.floor(Math.random() * 100);
    const end = start + Math.floor(Math.random() * 20) + 1;
    return new LineRange(start, end);
}
