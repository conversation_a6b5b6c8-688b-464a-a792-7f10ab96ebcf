import { ExtensionContext } from "../../__mocks__/vscode-mocks";
import * as vscode from "../../__mocks__/vscode-mocks";
import { KeyCodeChord } from "../../third-party/microsoft/vscode/src/vs/base/common/keybindings";
import { KeyCode } from "../../third-party/microsoft/vscode/src/vs/base/common/keyCodes";
import { AugmentKeybinding } from "../../utils/keybindings";
import { getKeybindingMarkdownIcons, getKeyboardIconUris } from "../../utils/keyboard-icons";

describe("KeyboardIcons", () => {
    const extensionContext = new ExtensionContext();

    describe("getKeyboardIconUri", () => {
        it("should return null for unknown key", () => {
            expect(getKeyboardIconUris(KeyCode.PageUp, extensionContext, "darwin")).toBeNull();
        });

        it("should return a URI for a key that is found", () => {
            expect(getKeyboardIconUris(KeyCode.KeyA, extensionContext, "darwin")).toEqual({
                light: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "light",
                    "a.svg"
                ),
                dark: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "dark",
                    "a.svg"
                ),
            });
        });

        it("should return a URI for darwin Meta key", () => {
            expect(getKeyboardIconUris(KeyCode.Meta, extensionContext, "darwin")).toEqual({
                light: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "light",
                    "command.svg"
                ),
                dark: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "dark",
                    "command.svg"
                ),
            });
        });

        it("should return a URI for linux Meta key", () => {
            expect(getKeyboardIconUris(KeyCode.Meta, extensionContext, "linux")).toEqual({
                light: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "light",
                    "meta.svg"
                ),
                dark: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "dark",
                    "meta.svg"
                ),
            });
        });

        it("should return a URI for win32 Meta key", () => {
            expect(getKeyboardIconUris(KeyCode.Meta, extensionContext, "win32")).toEqual({
                light: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "light",
                    "win.svg"
                ),
                dark: vscode.Uri.joinPath(
                    extensionContext.extensionUri,
                    "media",
                    "keyboard",
                    "dark",
                    "win.svg"
                ),
            });
        });
    });

    describe("getKeybindingMarkdownIcons", () => {
        it("should return null for null keybinding", () => {
            expect(getKeybindingMarkdownIcons(null, "darwin")).toBeNull();
        });

        it("should return null for null", () => {
            expect(getKeybindingMarkdownIcons(null, "darwin")).toBeNull();
        });

        it("should return icons for valid keybinding for mac", () => {
            expect(
                getKeybindingMarkdownIcons(
                    new AugmentKeybinding([
                        new KeyCodeChord(false, false, false, true, KeyCode.KeyA),
                    ]),
                    "darwin"
                )
            ).toEqual("$(augment-kb-command)&nbsp;$(augment-kb-a)");
        });

        it("should return icons for valid keybinding for windows", () => {
            expect(
                getKeybindingMarkdownIcons(
                    new AugmentKeybinding([
                        new KeyCodeChord(false, false, false, true, KeyCode.KeyA),
                    ]),
                    "win32"
                )
            ).toEqual("$(augment-kb-win)&nbsp;$(augment-kb-a)");
        });

        it("should return icons for valid keybinding for linux", () => {
            expect(
                getKeybindingMarkdownIcons(
                    new AugmentKeybinding([
                        new KeyCodeChord(false, false, false, true, KeyCode.KeyA),
                    ]),
                    "linux"
                )
            ).toEqual("$(augment-kb-meta)&nbsp;$(augment-kb-a)");
        });

        it("should return text only when the icon does not exist", () => {
            expect(
                getKeybindingMarkdownIcons(
                    new AugmentKeybinding([
                        new KeyCodeChord(false, false, false, true, KeyCode.Quote),
                    ]),
                    "linux"
                )
            ).toEqual("Meta+&#039;");
        });

        it("should handle multi-chord keybindings", () => {
            expect(
                getKeybindingMarkdownIcons(
                    new AugmentKeybinding([
                        new KeyCodeChord(false, false, false, true, KeyCode.Semicolon),
                        new KeyCodeChord(true, true, false, false, KeyCode.KeyP),
                    ]),
                    "linux"
                )
            ).toEqual(
                "$(augment-kb-meta)&nbsp;$(augment-kb-semicolon)&nbsp;&nbsp;$(augment-kb-ctrl)&nbsp;$(augment-kb-shift)&nbsp;$(augment-kb-p)"
            );
        });
    });
});
