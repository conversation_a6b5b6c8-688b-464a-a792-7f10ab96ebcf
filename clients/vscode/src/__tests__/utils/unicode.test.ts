import { utf32Length, utf32to16Offset } from "../../utils/unicode";

describe("unicode", () => {
    test("utf32Length", () => {
        expect(utf32Length("")).toBe(0);
        expect(utf32Length("a")).toBe(1);
        expect(utf32Length("á")).toBe(1);
        expect(utf32Length("😃")).toBe(1);
        expect(utf32Length("😃😃")).toBe(2);
    });

    test("utf32to16Offset", () => {
        expect(utf32to16Offset("", 0)).toBe(0);
        expect(utf32to16Offset("a", 1)).toBe(1);
        expect(utf32to16Offset("á", 1)).toBe(1);
        expect(utf32to16Offset("😃", 1)).toBe(2);
        expect(utf32to16Offset("😃😃", 2)).toBe(4);
        expect(utf32to16Offset("aa😃😃", 4)).toBe(6);
    });
});
