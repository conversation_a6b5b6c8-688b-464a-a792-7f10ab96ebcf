import { Position, Selection, TextDocument, TextEditor, Uri } from "../../__mocks__/vscode-mocks";
import { expandSelectionsToLineBoundaries } from "../../utils/editor";

describe("expandSelectionsToLineBoundaries", () => {
    let emptySelection = new Selection(0, 0, 0, 0);
    let whitespaceSelection = new Selection(1, 7, 1, 9);
    let multiLineSelection = new Selection(1, 3, 4, 2);

    let editor = new TextEditor(
        new TextDocument(
            Uri.parse("file://example/file"),
            "Line 1\nLine 2   \nLine 3\nLine 4\nLine 5"
        )
    );

    it("does not expand empty selection", () => {
        editor.selections = [emptySelection];
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].start).toEqual(new Position(0, 0));
        expect(editor.selections[0].end).toEqual(new Position(0, 0));
    });

    it("does not expand selection with only whitespace", () => {
        editor.selections = [whitespaceSelection];
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selection).toEqual(whitespaceSelection);
    });

    it("expands selection to line boundaries", () => {
        editor.selections = [multiLineSelection];
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].start).toEqual(new Position(1, 0));
        expect(editor.selections[0].end).toEqual(new Position(4, 6));
    });

    it("keeps cursor at start of selection when selection.active isBefore selection.anchor", () => {
        editor.selections = [multiLineSelection];
        expect(editor.selections[0].anchor).toEqual(new Position(1, 3));
        expect(editor.selections[0].active).toEqual(new Position(4, 2));
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].anchor).toEqual(new Position(1, 0));
        expect(editor.selections[0].active).toEqual(new Position(4, 6));
    });

    it("keeps cursor at end when selection.active isAfter selection.anchor", () => {
        editor.selections = [new Selection(multiLineSelection.active, multiLineSelection.anchor)];
        expect(editor.selections[0].anchor).toEqual(new Position(4, 2));
        expect(editor.selections[0].active).toEqual(new Position(1, 3));
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].anchor).toEqual(new Position(4, 6));
        expect(editor.selections[0].active).toEqual(new Position(1, 0));
    });
});
