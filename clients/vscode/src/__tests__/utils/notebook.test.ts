import * as vscode from "vscode";

import {
    NotebookCell,
    NotebookCellKind,
    NotebookDocument,
    NotebookEditor,
    TextDocument,
    window,
    workspace,
} from "../../__mocks__/vscode-mocks";
import {
    getActiveNotebookCodeTextAndPrefixLength,
    getConcatenatedCodeCellText,
    getNotebookCodeText,
    getNotebookDocument,
    getNotebookPrefixLengthForCellIndex,
    getNotebookPrefixLengthForDocument,
    isNotebookUri,
} from "../../utils/notebook";

const notebookUri = vscode.Uri.from({ scheme: "vscode-notebook-cell", path: "notebook.ipynb" });

// addNotebookCells adds the given cells to the notebook.
function addNotebookCells(notebook: NotebookDocument, cells: [string, vscode.NotebookCellKind][]) {
    for (const cell of cells) {
        notebook.appendCells([
            new NotebookCell(
                notebook.cellCount,
                notebook,
                cell[1],
                new TextDocument(notebook.uri, cell[0])
            ),
        ]);
    }
}

describe("notebook", () => {
    test("isNotebookUri true", () => {
        const result = isNotebookUri(notebookUri);

        expect(result).toBe(true);
    });

    test("isNotebookUri false", () => {
        const result = isNotebookUri(vscode.Uri.file("foo.py"));

        expect(result).toBe(false);
    });

    test("getNotebookCodeText no cells", () => {
        const notebookDocument = new NotebookDocument(notebookUri);

        const text = getNotebookCodeText(notebookDocument);

        expect(text).toBe("");
    });

    test("getNotebookCodeText single cell", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [["text", NotebookCellKind.Code]]);

        const text = getNotebookCodeText(notebookDocument);

        expect(text).toBe("text");
    });

    test("getNotebookCodeText three cells", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Code],
            ["c", NotebookCellKind.Code],
        ]);

        const text = getNotebookCodeText(notebookDocument);

        expect(text).toBe("a\n\nb\n\nc");
    });

    test("getNotebookCodeText ignores text cells", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Markup],
            ["c", NotebookCellKind.Code],
            ["d", NotebookCellKind.Markup],
        ]);

        const text = getNotebookCodeText(notebookDocument);

        expect(text).toBe("a\n\nc");
    });

    test("getConcatenatedCodeCellText no cells", () => {
        const text = getConcatenatedCodeCellText([]);

        expect(text).toBe("");
    });

    test("getConcatenatedCodeCellText single cell", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Code],
            ["c", NotebookCellKind.Code],
        ]);

        const text = getConcatenatedCodeCellText(notebookDocument.getCells());

        expect(text).toBe("a\n\nb\n\nc");
    });

    test("getConcatenatedCodeCellText middle cells", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Code],
            ["c", NotebookCellKind.Code],
            ["d", NotebookCellKind.Code],
        ]);

        const text = getConcatenatedCodeCellText(notebookDocument.getCells().slice(1, 3));

        expect(text).toBe("b\n\nc");
    });

    test("getConcatenatedCodeCellText middle cells one text", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Markup],
            ["c", NotebookCellKind.Code],
            ["d", NotebookCellKind.Code],
        ]);

        const text = getConcatenatedCodeCellText(notebookDocument.getCells().slice(1, 3));

        expect(text).toBe("c");
    });

    test("getConcatenatedCodeCellText middle cells both text", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Markup],
            ["c", NotebookCellKind.Markup],
            ["d", NotebookCellKind.Code],
        ]);

        const text = getConcatenatedCodeCellText(notebookDocument.getCells().slice(1, 3));

        expect(text).toBe("");
    });

    test("getActiveNotebookCodeTextAndPrefixLength no notebook", () => {
        window.activeNotebookEditor = undefined;
        const document = new TextDocument(notebookUri, "a");

        const [text, length] = getActiveNotebookCodeTextAndPrefixLength(document);

        expect(text).toBeUndefined();
        expect(length).toBe(0);
    });

    test("getActiveNotebookCodeTextAndPrefixLength first cell", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        window.activeNotebookEditor = new NotebookEditor(notebookDocument);
        addNotebookCells(notebookDocument, [["a", NotebookCellKind.Code]]);

        const [text, length] = getActiveNotebookCodeTextAndPrefixLength(
            notebookDocument.cellAt(0).document
        );

        expect(text).toBe("a");
        expect(length).toBe(0);
    });

    test("getActiveNotebookCodeTextAndPrefixLength third cell", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        window.activeNotebookEditor = new NotebookEditor(notebookDocument);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Code],
            ["c", NotebookCellKind.Code],
        ]);

        const [text, length] = getActiveNotebookCodeTextAndPrefixLength(
            notebookDocument.cellAt(2).document
        );

        expect(text).toBe("a\n\nb\n\nc");
        expect(length).toBe(6);
    });

    test("getActiveNotebookCodeTextAndPrefixLength text cell", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        window.activeNotebookEditor = new NotebookEditor(notebookDocument);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Markup],
        ]);

        const [text, length] = getActiveNotebookCodeTextAndPrefixLength(
            notebookDocument.cellAt(1).document
        );

        expect(text).toBeUndefined();
        expect(length).toBe(0);
    });

    test("getActiveNotebookCodeTextAndPrefixLength text cell first", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        window.activeNotebookEditor = new NotebookEditor(notebookDocument);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Markup],
            ["b", NotebookCellKind.Code],
        ]);

        const [text, length] = getActiveNotebookCodeTextAndPrefixLength(
            notebookDocument.cellAt(1).document
        );

        expect(text).toBe("b");
        expect(length).toBe(0);
    });

    test("getActiveNotebookCodeTextAndPrefixLength text cells first", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        window.activeNotebookEditor = new NotebookEditor(notebookDocument);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Markup],
            ["b", NotebookCellKind.Markup],
            ["c", NotebookCellKind.Code],
        ]);

        const [text, length] = getActiveNotebookCodeTextAndPrefixLength(
            notebookDocument.cellAt(2).document
        );

        expect(text).toBe("c");
        expect(length).toBe(0);
    });

    test.each([
        ["first cell", 0, 0],
        ["third cell", 2, 6],
    ])(
        "getNotebookPrefixLengthForCellIndex and getNotebookPrefixLengthForDocument %s",
        (name, index, expected) => {
            const notebookDocument = new NotebookDocument(notebookUri);
            addNotebookCells(notebookDocument, [
                ["a", NotebookCellKind.Code],
                ["b", NotebookCellKind.Code],
                ["c", NotebookCellKind.Code],
            ]);

            const length1 = getNotebookPrefixLengthForCellIndex(notebookDocument, index, true);
            const length2 = getNotebookPrefixLengthForDocument(
                notebookDocument,
                notebookDocument.cellAt(index).document
            );

            expect(length1).toBe(expected);
            expect(length2).toBe(expected);
        }
    );

    test("getNotebookPrefixLengthForCellIndex and getNotebookPrefixLengthForDocument text cell", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Markup],
            ["b", NotebookCellKind.Code],
        ]);

        const length1 = getNotebookPrefixLengthForCellIndex(notebookDocument, 1, true);
        const length2 = getNotebookPrefixLengthForDocument(
            notebookDocument,
            notebookDocument.cellAt(1).document
        );

        expect(length1).toBe(0);
        expect(length2).toBe(0);
    });

    test("getNotebookPrefixLengthForCellIndex third cell without separator", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Code],
            ["c", NotebookCellKind.Code],
        ]);

        const length = getNotebookPrefixLengthForCellIndex(notebookDocument, 2, false);

        expect(length).toBe(4);
    });

    test("getNotebookPrefixLengthForDocument fake document", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        addNotebookCells(notebookDocument, [
            ["a", NotebookCellKind.Code],
            ["b", NotebookCellKind.Code],
        ]);

        const length = getNotebookPrefixLengthForDocument(
            notebookDocument,
            new TextDocument(notebookUri, "c")
        );

        expect(length).toBe(0);
    });

    test("getNotebookDocument no notebooks", () => {
        const document = new TextDocument(notebookUri, "a");

        const notebookDocument = getNotebookDocument(document);

        expect(notebookDocument).toBeUndefined();
    });

    test("getNotebookDocument in notebook", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        workspace.notebookDocuments = [notebookDocument];
        addNotebookCells(notebookDocument, [["code", NotebookCellKind.Code]]);
        const document = notebookDocument.cellAt(0).document;

        const actualNotebookDocument = getNotebookDocument(document);

        expect(actualNotebookDocument).toBe(notebookDocument);
    });

    test("getNotebookDocument not in notebook", () => {
        const notebookDocument = new NotebookDocument(notebookUri);
        workspace.notebookDocuments = [notebookDocument];
        addNotebookCells(notebookDocument, [["code", NotebookCellKind.Code]]);
        const document = new TextDocument(notebookUri, "a");

        const actualNotebookDocument = getNotebookDocument(document);

        expect(actualNotebookDocument).toBeUndefined();
    });
});
