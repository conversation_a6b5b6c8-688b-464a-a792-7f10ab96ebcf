import * as vscode from "../../__mocks__/vscode-mocks";
import {
    isExtensionVersionGte,
    isExtensionVersionLte,
    isVsCodeVersionGte,
} from "../../utils/environment";

describe("environment", () => {
    test("isExtensionVersionGte", () => {
        const extensionVersion = "0.5.0";
        expect(isExtensionVersionGte("0.0.0", extensionVersion)).toBe(true);
        expect(isExtensionVersionGte("0.6.0", extensionVersion)).toBe(false);
        expect(isExtensionVersionGte("0.0.1", "")).toBe(false);
    });

    test("isVsCodeVersionGte", () => {
        vscode.setVsCodeVersion("0.5.0");
        expect(isVsCodeVersionGte("0.0.0")).toBe(true);
        expect(isVsCodeVersionGte("0.5.0")).toBe(true);
        expect(isVsCodeVersionGte("0.6.0")).toBe(false);
    });

    test("isExtensionVersionLte", () => {
        const extensionVersion = "0.5.0";
        expect(isExtensionVersionLte("0.0.0", extensionVersion)).toBe(false);
        expect(isExtensionVersionLte("0.6.0", extensionVersion)).toBe(true);
        expect(isExtensionVersionLte("0.0.1", "")).toBe(true);
        expect(isExtensionVersionLte("", extensionVersion)).toBe(false);
        expect(isExtensionVersionLte("0.5.0", extensionVersion)).toBe(true);
    });
});
