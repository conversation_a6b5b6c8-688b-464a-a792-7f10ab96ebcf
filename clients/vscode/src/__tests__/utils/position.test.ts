import { Range, TextDocument, Uri } from "../../__mocks__/vscode-mocks";
import { expandToLineBoundaries } from "../../utils/position";

describe("expandToLineBoundaries", () => {
    it("does not extend the range in an empty file", () => {
        const range = new Range(0, 0, 0, 0);
        const document = new TextDocument(Uri.parse("file://example/file"), "");
        const expandedRange = expandToLineBoundaries(range, document);
        expect(expandedRange.start.line).toBe(0);
        expect(expandedRange.start.character).toBe(0);
        expect(expandedRange.end.line).toBe(0);
        expect(expandedRange.end.character).toBe(0);
    });

    describe("single line", () => {
        it("expands a range to the start of the line", () => {
            const range = new Range(0, 3, 0, 6);
            const document = new TextDocument(Uri.parse("file://example/file"), "Line 1");
            const expandedRange = expandToLineBoundaries(range, document);
            expect(expandedRange.start.line).toBe(0);
            expect(expandedRange.start.character).toBe(0);
            expect(expandedRange.end.line).toBe(0);
            expect(expandedRange.end.character).toBe(6);
        });

        it("expands a range to the end of the line", () => {
            const range = new Range(0, 0, 0, 3);
            const document = new TextDocument(Uri.parse("file://example/file"), "Line 1");
            const expandedRange = expandToLineBoundaries(range, document);
            expect(expandedRange.start.line).toBe(0);
            expect(expandedRange.start.character).toBe(0);
            expect(expandedRange.end.line).toBe(0);
            expect(expandedRange.end.character).toBe(6);
        });
    });

    describe("multiple lines", () => {
        it("expands a range to the end character of the line", () => {
            const range = new Range(1, 0, 1, 4);
            const document = new TextDocument(
                Uri.parse("file://example/file"),
                "Line 1\nLine 2\nLine 3"
            );
            const expandedRange = expandToLineBoundaries(range, document);
            expect(expandedRange.start.line).toBe(1);
            expect(expandedRange.start.character).toBe(0);
            expect(expandedRange.end.line).toBe(1);
            expect(expandedRange.end.character).toBe(6);
        });

        it("expands a range to the line boundaries when the range spans multiple lines", () => {
            const range = new Range(0, 3, 1, 3);
            const document = new TextDocument(
                Uri.parse("file://example/file"),
                "Line 1\nLine 2\nLine 3"
            );
            const expandedRange = expandToLineBoundaries(range, document);
            expect(expandedRange.start.line).toBe(0);
            expect(expandedRange.start.character).toBe(0);
            expect(expandedRange.end.line).toBe(1);
            expect(expandedRange.end.character).toBe(6);
        });

        it("expands a range to the line boundaries when the range is at the end of the document", () => {
            const range = new Range(2, 3, 2, 6);
            const document = new TextDocument(
                Uri.parse("file://example/file"),
                "Line 1\nLine 2\nLine 3"
            );
            const expandedRange = expandToLineBoundaries(range, document);
            expect(expandedRange.start.line).toBe(2);
            expect(expandedRange.start.character).toBe(0);
            expect(expandedRange.end.line).toBe(2);
            expect(expandedRange.end.character).toBe(6);
        });
    });

    describe("multiple lines with newline at the end", () => {
        it("expands a range to the line boundaries when the range spans multiple lines", () => {
            const range = new Range(0, 3, 1, 3);
            const document = new TextDocument(
                Uri.parse("file://example/file"),
                "Line 1\nLine 2\nLine 3\n"
            );
            const expandedRange = expandToLineBoundaries(range, document, true);
            expect(expandedRange.start.line).toBe(0);
            expect(expandedRange.start.character).toBe(0);
            expect(expandedRange.end.line).toBe(2);
            expect(expandedRange.end.character).toBe(0);
        });

        it("expands a range to the line boundaries when the range is at the end of the document", () => {
            const range = new Range(1, 3, 2, 3);
            const document = new TextDocument(
                Uri.parse("file://example/file"),
                "Line 1\nLine 2\nLine 3\n"
            );
            const expandedRange = expandToLineBoundaries(range, document, true);
            expect(expandedRange.start.line).toBe(1);
            expect(expandedRange.start.character).toBe(0);
            expect(expandedRange.end.line).toBe(3);
            expect(expandedRange.end.character).toBe(0);
        });
    });
});
