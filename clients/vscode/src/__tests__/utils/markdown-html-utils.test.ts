import { isVsCodeVersionGte } from "../../utils/environment";
import { borderRadiusAllowed, span, ValidVSCodeStyleTag } from "../../utils/markdown-html-utils";

jest.mock("../../utils/environment", () => ({
    isVsCodeVersionGte: jest.fn(),
}));

describe("markdown-html-utils", () => {
    describe("ValidVSCodeStyleTag", () => {
        it("should create a valid style string", () => {
            (isVsCodeVersionGte as jest.Mock).mockReturnValue(true);
            const style = new ValidVSCodeStyleTag({
                color: "#FF0000",
                backgroundColor: "var(--vscode-editor-background)",
                borderRadius: "5px",
            });
            expect(style.toString()).toBe(
                "color:#FF0000;background-color:var(--vscode-editor-background);border-radius:5px;"
            );
        });

        it("should ignore invalid color values", () => {
            const style = new ValidVSCodeStyleTag({
                color: "invalid-color",
                backgroundColor: "#00FF00",
            });
            expect(style.toString()).toBe("background-color:#00FF00;");
        });

        it("should ignore invalid background-color values", () => {
            const style = new ValidVSCodeStyleTag({
                color: "#FF0000",
                backgroundColor: "invalid-background",
            });
            expect(style.toString()).toBe("color:#FF0000;");
        });

        it("should ignore invalid border-radius values", () => {
            const style = new ValidVSCodeStyleTag({
                color: "#FF0000",
                borderRadius: "invalid-radius",
            });
            expect(style.toString()).toBe("color:#FF0000;");
        });

        it("should not include border-radius if not allowed", () => {
            (isVsCodeVersionGte as jest.Mock).mockReturnValue(false);
            const style = new ValidVSCodeStyleTag({
                color: "#FF0000",
                borderRadius: "5px",
            });
            expect(style.toString()).toBe("color:#FF0000;");
        });
    });

    describe("span", () => {
        it("should create a span with no style", () => {
            expect(span("Hello", undefined)).toBe("<span>Hello</span>");
        });

        it("should create a span with a ValidVSCodeStyleTag", () => {
            const style = new ValidVSCodeStyleTag({ color: "#FF0000" });
            expect(span("Hello", style)).toBe('<span style="color:#FF0000;">Hello</span>');
        });

        it("should create a span with a style object", () => {
            expect(span("Hello", { color: "#FF0000" })).toBe(
                '<span style="color:#FF0000;">Hello</span>'
            );
        });
    });

    describe("borderRadiusAllowed", () => {
        it("should return true for VS Code version >= 1.92.0", () => {
            (isVsCodeVersionGte as jest.Mock).mockReturnValue(true);
            expect(borderRadiusAllowed()).toBe(true);
        });

        it("should return false for VS Code version < 1.92.0", () => {
            (isVsCodeVersionGte as jest.Mock).mockReturnValue(false);
            expect(borderRadiusAllowed()).toBe(false);
        });
    });
});
