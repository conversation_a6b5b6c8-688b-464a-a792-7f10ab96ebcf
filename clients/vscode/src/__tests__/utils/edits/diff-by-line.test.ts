import { ChatInstructionStreamResult } from "../../../augment-api";
import {
    applyEditToLines,
    applyInstructionEdits,
    applyOffset,
    CoalescedEditInfo,
    computeOffset,
    computeRunningOffset,
    getYieldableChunk,
    IMonacoEditOp,
    narrowDownFullEdit,
} from "../../../utils/edits/diff-by-line";
import { isNil } from "../../../utils/types-utils";

function createMockInstructionStream(
    results: ChatInstructionStreamResult[]
): AsyncGenerator<ChatInstructionStreamResult, any, unknown> {
    async function* generator() {
        for (const result of results) {
            if (!isNil(result.replacementOldText)) {
                yield { text: "", replacementOldText: result.replacementOldText };
            }
            yield { text: "", replacementStartLine: result.replacementStartLine };
            yield {
                text: "",
                replacementEndLine: result.replacementEndLine,
                replacementText: result.replacementText,
            };
        }
    }

    return generator();
}

describe("applyInstructionEdits", () => {
    it("single edit with single line replacement", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "A", replacementStartLine: 1, replacementEndLine: 2 },
        ]);

        const stream = applyInstructionEdits(code, instructionStream);
        const newCode = await stream.next();
        expect(newCode.value).toEqual(["A", "2", "3", "4", "5"].join("\n"));
    });

    it("single edit with multiple line replacement", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "X\nY\n", replacementStartLine: 2, replacementEndLine: 5 },
        ]);

        const stream = applyInstructionEdits(code, instructionStream);
        const newCode = await stream.next();
        expect(newCode.value).toEqual(["1", "X", "Y", "5"].join("\n"));
    });

    it("multiple edits", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "A\n", replacementStartLine: 1, replacementEndLine: 2 },
            { text: "", replacementText: "X\nY\n", replacementStartLine: 3, replacementEndLine: 6 },
        ]);

        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["A", "2", "3", "4", "5"].join("\n"));

        const result2 = await stream.next();
        expect(result2.value).toEqual(["A", "2", "X", "Y"].join("\n"));
    });

    it("multiple edits v2 (delete one line at start)", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "", replacementStartLine: 1, replacementEndLine: 2 },
            { text: "", replacementText: "X\nY\n", replacementStartLine: 3, replacementEndLine: 6 },
        ]);

        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["2", "3", "4", "5"].join("\n"));

        const result2 = await stream.next();
        expect(result2.value).toEqual(["2", "X", "Y"].join("\n"));
    });

    it("delete one line", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "", replacementStartLine: 1, replacementEndLine: 2 },
        ]);

        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["2", "3", "4", "5"].join("\n"));
    });

    it("add one line", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "A\n", replacementStartLine: 1, replacementEndLine: 1 },
        ]);

        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["A", "1", "2", "3", "4", "5"].join("\n"));
    });

    it("delete multiple lines", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "", replacementStartLine: 1, replacementEndLine: 4 },
        ]);
        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["4", "5"].join("\n"));
    });

    it("delete start then end", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "", replacementStartLine: 1, replacementEndLine: 2 },
            { text: "", replacementText: "", replacementStartLine: 5, replacementEndLine: 6 },
        ]);
        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["2", "3", "4", "5"].join("\n"));

        const result2 = await stream.next();
        expect(result2.value).toEqual(["2", "3", "4"].join("\n"));
    });

    it("apply out-of-order edits", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            { text: "", replacementText: "", replacementStartLine: 1, replacementEndLine: 2 },
            { text: "", replacementText: "", replacementStartLine: 5, replacementEndLine: 6 },
            { text: "", replacementText: "A\nB\n", replacementStartLine: 2, replacementEndLine: 5 },
        ]);
        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["2", "3", "4", "5"].join("\n"));

        const result2 = await stream.next();
        expect(result2.value).toEqual(["2", "3", "4"].join("\n"));

        const result3 = await stream.next();
        expect(result3.value).toEqual(["A", "B"].join("\n"));
    });

    it("test refine line numbers", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            {
                text: "",
                replacementText: "A\nB\n",
                replacementStartLine: 2,
                replacementEndLine: 5,
                replacementOldText: "2\n3\n4\n5\n",
            },
        ]);
        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["1", "A", "B"].join("\n"));
    });
    it("test refine line numbers v2", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            {
                text: "",
                replacementText: "A\nB\n",
                replacementStartLine: 3,
                replacementEndLine: 5,
                replacementOldText: "2\n3\n4\n5\n",
            },
        ]);
        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["1", "A", "B"].join("\n"));
    });
    it("test refine line numbers v3", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            {
                text: "",
                replacementText: "A\nB\n",
                replacementStartLine: 3,
                replacementEndLine: 5,
                replacementOldText: "2\n3\n4\n5\n",
            },
        ]);
        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["1", "A", "B"].join("\n"));
    });
    it("test refine multiple line numbers", async () => {
        const code = ["1", "2", "3", "4", "5"].join("\n");
        const instructionStream = createMockInstructionStream([
            {
                text: "",
                replacementText: "A\nB\n",
                replacementStartLine: 3,
                replacementEndLine: 4,
                replacementOldText: "3\n4\n5\n",
            },
            {
                text: "",
                replacementText: "X\nY\n",
                replacementStartLine: 1,
                replacementEndLine: 3,
                replacementOldText: "1\n",
            },
        ]);
        const stream = applyInstructionEdits(code, instructionStream);
        const result1 = await stream.next();
        expect(result1.value).toEqual(["1", "2", "A", "B"].join("\n"));
        const result2 = await stream.next();
        expect(result2.value).toEqual(["X", "Y", "2", "A", "B"].join("\n"));
    });
});

describe("computeOffset", () => {
    it("should apply a single removal", () => {
        const prevEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 3,
            },
            newText: "A\nB\n",
        };
        const currEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 4,
                originalEndLineNumber: 6,
                modifiedStartLineNumber: 4,
                modifiedEndLineNumber: 6,
            },
            newText: "D\nE\n",
        };
        const offset = computeOffset(currEdit, prevEdit);
        expect(offset).toEqual(-1);
    });

    it("should apply multiple removals", () => {
        const prevEdit1: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 3,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 2,
            },
            newText: "A\n",
        };
        const prevEdit2: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 3,
                originalEndLineNumber: 5,
                modifiedStartLineNumber: 3,
                modifiedEndLineNumber: 4,
            },
            newText: "D\n",
        };
        const currEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 5,
                originalEndLineNumber: 6,
                modifiedStartLineNumber: 5,
                modifiedEndLineNumber: 7,
            },
            newText: "G\nH\n",
        };

        const offset1 = computeOffset(currEdit, prevEdit1);
        expect(offset1).toEqual(-1);

        const offset2 = computeOffset(currEdit, prevEdit2);
        expect(offset2).toEqual(-1);

        const runningOffset = computeRunningOffset(currEdit, [prevEdit1, prevEdit2]);
        expect(runningOffset).toEqual(-2);
    });

    it("should apply a single insertion", () => {
        const prevEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 2,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 3,
            },
            newText: "A\nB\n",
        };
        const currEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 3,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 3,
            },
            newText: "D\n",
        };
        const offset = computeOffset(currEdit, prevEdit);
        expect(offset).toEqual(1);
    });

    it("should apply multiple insertions", () => {
        const prevEdit1: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 2,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 3,
            },
            newText: "A\nB\n",
        };
        const prevEdit2: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 3,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 3,
                modifiedEndLineNumber: 5,
            },
            newText: "D\nE\n",
        };
        const currEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 5,
                originalEndLineNumber: 6,
                modifiedStartLineNumber: 5,
                modifiedEndLineNumber: 7,
            },
            newText: "G\n",
        };

        const offset1 = computeOffset(currEdit, prevEdit1);
        expect(offset1).toEqual(1);

        const offset2 = computeOffset(currEdit, prevEdit2);
        expect(offset2).toEqual(1);

        const runningOffset = computeRunningOffset(currEdit, [prevEdit1, prevEdit2]);
        expect(runningOffset).toEqual(2);
    });
});

describe("applyOffset", () => {
    it("should apply a single offset", () => {
        const edit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 2,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 2,
            },
            newText: "A\n",
        };
        const offset = 1;
        const offsetEdit = applyOffset(edit, offset);
        expect(offsetEdit).toEqual({
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 3,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 3,
            },
            newText: "A\n",
        });
    });
});

describe("applyEditToLines", () => {
    it("should apply a single edit", () => {
        const lines = ["1", "2", "3", "4", "5"];
        const edit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 2,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 2,
            },
            newText: "A\n",
        };
        const newLines = applyEditToLines(lines, edit);
        expect(newLines).toEqual(["A", "2", "3", "4", "5"]);
    });

    it("should apply multiple edits", () => {
        const lines = ["1", "2", "3", "4", "5"];
        const edit1: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 2,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 2,
            },
            newText: "A\n",
        };
        const edit2: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 3,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 3,
                modifiedEndLineNumber: 4,
            },
            newText: "D\n",
        };
        const newLines = applyEditToLines(lines, edit1);
        expect(newLines).toEqual(["A", "2", "3", "4", "5"]);
        const newLines2 = applyEditToLines(newLines, edit2);
        expect(newLines2).toEqual(["A", "2", "D", "4", "5"]);
    });
});

describe("getYieldableChunk", () => {
    const mockCode = "line1\nline2\nline3\nline4\nline5\n";

    test("handles suffix overlap when edit is completed", () => {
        const edit: CoalescedEditInfo = {
            startLineNumber: 2,
            endLineNumber: 4,
            newText: "",
            oldText: "",
            hasYieldedNewChunk: false,
            currOffset: null,
            startRefined: true,
            endRefined: true,
            newTextBuffer: "new line\nline4\nline5\n",
            prefixOverlapChecked: true,
        };
        const result = getYieldableChunk(edit, mockCode, 3, true);
        expect(result).toBe("new line\n");
    });

    test("handles prefix overlap while edit is in progress", () => {
        const edit: CoalescedEditInfo = {
            startLineNumber: 2,
            endLineNumber: 4,
            newText: "",
            oldText: "",
            hasYieldedNewChunk: false,
            currOffset: null,
            startRefined: true,
            endRefined: true,
            newTextBuffer: "line1\nnew line\nnew line2\nnew line3\n",
            prefixOverlapChecked: false,
        };
        const result = getYieldableChunk(edit, mockCode, 2, false);
        expect(result).toBe("new line\n");
        expect(edit.prefixOverlapChecked).toBe(true);
    });

    test("handles prefix overlap when edit is completed", () => {
        const edit: CoalescedEditInfo = {
            startLineNumber: 2,
            endLineNumber: 4,
            newText: "",
            oldText: "",
            hasYieldedNewChunk: false,
            currOffset: null,
            startRefined: true,
            endRefined: true,
            newTextBuffer: "line1\nnew line\nnew line2\nnew line3\n",
            prefixOverlapChecked: false,
        };
        const result = getYieldableChunk(edit, mockCode, 2, true);
        expect(result).toBe("new line\nnew line2\nnew line3\n");
        expect(edit.prefixOverlapChecked).toBe(true);
    });
});

describe("narrowDownFullEdit", () => {
    it("should not change edit when no common lines at start or end", () => {
        const code = "line1\nline2\nline3\nline4\n";
        const fullEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 4,
            },
            newText: "newline2\nnewline3\n",
        };

        const result = narrowDownFullEdit(fullEdit, code);
        expect(result).toEqual(fullEdit);
    });

    it("should narrow down edit when common lines at start", () => {
        const code = "line1\nline2\nline3\nline4\n";
        const fullEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 4,
            },
            newText: "line1\nnewline2\nnewline3\n",
        };

        const expected: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 4,
            },
            newText: "newline2\nnewline3\n",
        };

        const result = narrowDownFullEdit(fullEdit, code);
        expect(result).toEqual(expected);
    });

    it("should narrow down edit when common lines at end", () => {
        const code = "line1\nline2\nline3\nline4\n";
        const fullEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 4,
            },
            newText: "newline1\nnewline2\nline3\n",
        };

        const expected: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 3,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 3,
            },
            newText: "newline1\nnewline2\n",
        };

        const result = narrowDownFullEdit(fullEdit, code);
        expect(result).toEqual(expected);
    });

    it("should narrow down edit when common lines at both start and end", () => {
        const code = "line1\nline2\nline3\nline4\nline5\n";
        const fullEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 5,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 5,
            },
            newText: "line1\nnewline2\nnewline3\nline4\n",
        };

        const expected: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 4,
            },
            newText: "newline2\nnewline3\n",
        };

        const result = narrowDownFullEdit(fullEdit, code);
        expect(result).toEqual(expected);
    });

    it("should return empty edit when all lines are common", () => {
        const code = "line1\nline2\nline3\n";
        const fullEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 1,
                originalEndLineNumber: 3,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: 3,
            },
            newText: "line1\nline2\n",
        };

        const expected: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 3,
                originalEndLineNumber: 3,
                modifiedStartLineNumber: 3,
                modifiedEndLineNumber: 3,
            },
            newText: "",
        };

        const result = narrowDownFullEdit(fullEdit, code);
        expect(result).toEqual(expected);
    });

    it("should handle pure addition correctly", () => {
        const code = "line1\nline2\nline3\n";
        const fullEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 2,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 4,
            },
            newText: "newline1\nnewline2\n",
        };

        const expected: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 2,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 4,
            },
            newText: "newline1\nnewline2\n",
        };

        const result = narrowDownFullEdit(fullEdit, code);
        expect(result).toEqual(expected);
    });

    it("should handle pure deletion correctly", () => {
        const code = "line1\nline2\nline3\nline4\n";
        const fullEdit: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 2,
            },
            newText: "",
        };

        const expected: IMonacoEditOp = {
            lineChange: {
                originalStartLineNumber: 2,
                originalEndLineNumber: 4,
                modifiedStartLineNumber: 2,
                modifiedEndLineNumber: 2,
            },
            newText: "",
        };

        const result = narrowDownFullEdit(fullEdit, code);
        expect(result).toEqual(expected);
    });
});
