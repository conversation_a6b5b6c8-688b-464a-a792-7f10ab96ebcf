/* eslint-disable @typescript-eslint/naming-convention */
import { MockAPIServer } from "../__mocks__/mock-api-server";
import {
    publishTextDocumentChange,
    TextDocument,
    TextDocumentChangeEvent,
    TextDocumentContentChangeEvent,
    Uri,
} from "../__mocks__/vscode-mocks";
import { AugmentInstruction } from "../code-edit-types";
import { RecentCompletions } from "../completions/recent-completions";
import { DataCollector, PathResolver } from "../data-collector";
import {
    ChangeType,
    NextEditMode,
    NextEditResultInfo,
    NextEditScope,
    SuggestionState,
} from "../next-edit/next-edit-types";
import { sha256 } from "../sha";
import { DisposableService } from "../utils/disposable-service";
import { RecentItems } from "../utils/recent-items";

class DataCollectorTestKit extends DisposableService {
    static readonly repoRoot = "/mock/repo/root/";
    static readonly defaultRelativePath = "relative/path";

    public readonly apiServer: MockAPIServer = new MockAPIServer();
    public readonly mockRecentInstructions: RecentItems<AugmentInstruction> =
        new RecentItems<AugmentInstruction>(10);
    public readonly mockRecentCompletions: RecentCompletions = new RecentCompletions();
    public readonly mockRecentNextEdits = new RecentItems<NextEditResultInfo>(10);
    public readonly pathVerifier: PathResolver = {
        resolvePathName: jest.fn().mockReturnValue({
            repoRoot: DataCollectorTestKit.repoRoot,
            relPath: DataCollectorTestKit.defaultRelativePath,
        }),
    };
    public readonly fixedTimeString: string;

    constructor() {
        super();

        this.apiServer.uploadUserEvents = jest.fn();

        const fixedTime = new Date("2023-01-01T00:00:00Z");
        this.fixedTimeString = fixedTime.toISOString();
        jest.spyOn(global, "Date").mockImplementation(() => fixedTime);
    }

    createDataCollector(batchSize?: number, maxUploadDelay?: number): DataCollector {
        const dataCollector = new DataCollector(
            this.apiServer,
            this.pathVerifier,
            this.mockRecentInstructions,
            this.mockRecentCompletions,
            this.mockRecentNextEdits,
            batchSize,
            maxUploadDelay
        );
        this.addDisposable(dataCollector);
        return dataCollector;
    }
}

describe("data-collector", () => {
    let kit: DataCollectorTestKit;

    beforeEach(() => {
        jest.useFakeTimers();

        kit = new DataCollectorTestKit();
    });
    afterEach(() => {
        kit.dispose();
        jest.restoreAllMocks();
        jest.useRealTimers();
    });

    test("send completions", async () => {
        // Create a data collector so we can add completion listener
        kit.createDataCollector(1);

        // Trigger completion
        kit.mockRecentCompletions.addItem({
            requestId: "completion-request-id",
            occuredAt: new Date(),
            prefix: "",
            suffix: "",
            pathName: DataCollectorTestKit.defaultRelativePath,
            isReused: false,
            document: null as any,
            completions: [
                {
                    completionText: "mock-completion-text",
                    skippedSuffix: "",
                    suffixReplacementText: "",
                    range: {
                        startOffset: 0,
                        endOffset: 0,
                    },
                },
            ],
            repoRoot: "",
        });

        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(1);
        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledWith({
            user_events: [
                {
                    time: kit.fixedTimeString,
                    file_path: DataCollectorTestKit.defaultRelativePath,
                    completion_request_id_issued: {
                        request_id: "completion-request-id",
                    },
                },
            ],
        });
    });

    test("send instructions", async () => {
        // We create a data collector to add instruction listener
        kit.createDataCollector(1);

        // Trigger instruction
        kit.mockRecentInstructions.addItem({
            requestId: "instruction-request-id",
            suggestionId: "mock-suggestion-id",
            requestedAt: new Date(),
            pathName: DataCollectorTestKit.defaultRelativePath,
            occuredAt: new Date(),
            sessionId: "mock-session-id",
            repoRoot: "mock-repo-root",
            vscodeLanguageId: "mock-language-id",
            modelName: "mock-model-name",
            prompt: "mock-prompt",
            prefix: "mock-prefix",
            selectedText: "mock-selected-text",
            modifiedText: "mock-modified-text",
            suffix: "mock-suffix",
            selectionStartLine: 0,
            selectionStartColumn: 0,
            selectionEndLine: 0,
            selectionEndColumn: 0,
            userRequested: true,
        });

        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(1);
        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledWith({
            user_events: [
                {
                    time: kit.fixedTimeString,
                    file_path: DataCollectorTestKit.defaultRelativePath,
                    edit_request_id_issued: {
                        request_id: "instruction-request-id",
                    },
                },
            ],
        });
    });

    test("send next edit suggestions", async () => {
        // We create a data collector to add instruction listener
        kit.createDataCollector(1);

        // Trigger instruction
        kit.mockRecentNextEdits.addItem({
            occurredAt: new Date(),
            requestId: "next-edit-request-id",
            mode: NextEditMode.Foreground,
            scope: NextEditScope.File,
            qualifiedPathName: {
                rootPath: DataCollectorTestKit.repoRoot,
                relPath: DataCollectorTestKit.defaultRelativePath,
            },
            apiResult: 0,
            suggestions: [
                {
                    requestId: "next-edit-request-id",
                    mode: NextEditMode.Foreground,
                    scope: NextEditScope.File,
                    result: {
                        suggestionId: "mock-suggestion-id",
                        path: DataCollectorTestKit.defaultRelativePath,
                        blobName: "",
                        charStart: 0,
                        charEnd: 0,
                        existingCode: "",
                        suggestedCode: "",
                        changeDescription: "",
                        diffSpans: [],
                        editingScore: 0,
                        localizationScore: 0,
                        editingScoreThreshold: 1.0,
                    },
                    qualifiedPathName: {
                        rootPath: DataCollectorTestKit.repoRoot,
                        relPath: DataCollectorTestKit.defaultRelativePath,
                    },
                    lineRange: {
                        start: 0,
                        stop: 0,
                    },
                    uriScheme: "file",
                    occurredAt: new Date(),
                    state: SuggestionState.fresh,
                    changeType: ChangeType.insertion,
                },
            ],
            requestTime: new Date(),
        });

        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(1);
        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledWith({
            user_events: [
                {
                    time: kit.fixedTimeString,
                    file_path: DataCollectorTestKit.defaultRelativePath,
                    next_edit_request_id_issued: {
                        request_id: "next-edit-request-id",
                    },
                },
            ],
        });
    });

    test("send text document change", async () => {
        // We create a data collector to add instruction listener
        kit.createDataCollector(1);

        // Trigger document change
        const document = new TextDocument(
            Uri.file(DataCollectorTestKit.defaultRelativePath),
            "Hello mock document text"
        );
        publishTextDocumentChange(
            new TextDocumentChangeEvent(document, [
                TextDocumentContentChangeEvent.forInsert(
                    document,
                    document.positionAt(0),
                    0,
                    "Hello "
                ),
            ])
        );

        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(1);
        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledWith({
            user_events: [
                {
                    time: kit.fixedTimeString,
                    file_path: DataCollectorTestKit.defaultRelativePath,
                    text_edit: {
                        content_changes: [
                            {
                                range: {
                                    start: 0,
                                    end: 0,
                                },
                                text: "Hello ",
                            },
                        ],
                        reason: undefined,
                        after_changes_hash: sha256(
                            new TextEncoder().encode("Hello mock document text")
                        ),
                        hash_char_ranges: [
                            {
                                start: 0,
                                end: 24,
                            },
                        ],
                        after_doc_length: 24,
                    },
                },
            ],
        });
    });

    test("ignore text document changes", async () => {
        // We create a data collector to add instruction listener
        kit.createDataCollector(1);

        // Text document change with no changes should be ignored
        const document = new TextDocument(
            Uri.file(DataCollectorTestKit.defaultRelativePath),
            "mock document text"
        );
        publishTextDocumentChange(new TextDocumentChangeEvent(document, []));

        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(0);

        // Text document change on irrelevant file should be ignored
        (kit.pathVerifier.resolvePathName as jest.Mock).mockReturnValue(undefined);
        publishTextDocumentChange(
            new TextDocumentChangeEvent(document, [
                TextDocumentContentChangeEvent.forInsert(
                    document,
                    document.positionAt(0),
                    0,
                    "Hello"
                ),
            ])
        );

        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(0);
    });

    test("batch changes", async () => {
        // We create a data collector to add completion listener
        kit.createDataCollector(2);

        const mockCompletion = {
            requestId: "completion-request-id",
            occuredAt: new Date(),
            prefix: "",
            suffix: "",
            pathName: DataCollectorTestKit.defaultRelativePath,
            isReused: false,
            document: null as any,
            completions: [
                {
                    completionText: "mock-completion-text",
                    skippedSuffix: "",
                    suffixReplacementText: "",
                    range: {
                        startOffset: 0,
                        endOffset: 0,
                    },
                },
            ],
            repoRoot: "",
        };

        const mockInstruction = {
            requestId: "instruction-request-id",
            suggestionId: "mock-suggestion-id",
            requestedAt: new Date(),
            pathName: DataCollectorTestKit.defaultRelativePath,
            occuredAt: new Date(),
            sessionId: "mock-session-id",
            repoRoot: "mock-repo-root",
            vscodeLanguageId: "mock-language-id",
            modelName: "mock-model-name",
            prompt: "mock-prompt",
            prefix: "mock-prefix",
            selectedText: "mock-selected-text",
            modifiedText: "mock-modified-text",
            suffix: "mock-suffix",
            selectionStartLine: 0,
            selectionStartColumn: 0,
            selectionEndLine: 0,
            selectionEndColumn: 0,
            userRequested: true,
        };

        const document = new TextDocument(
            Uri.file(DataCollectorTestKit.defaultRelativePath),
            "mock document text"
        );
        const mockChangeEvent = new TextDocumentChangeEvent(document, [
            TextDocumentContentChangeEvent.forInsert(document, document.positionAt(0), 0, "Hello"),
        ]);

        kit.mockRecentCompletions.addItem(mockCompletion);

        // Batch size is set to 2, so we should not have uploaded yet
        expect(kit.apiServer.uploadUserEvents).not.toHaveBeenCalled();

        kit.mockRecentInstructions.addItem(mockInstruction);

        // First call
        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(1);

        publishTextDocumentChange(mockChangeEvent);

        // Should still only have been called once
        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(1);

        // // Advance timer by 10 seconds
        jest.advanceTimersByTime(10000);

        // After 10 seconds, we should have uploaded again
        expect(kit.apiServer.uploadUserEvents).toHaveBeenCalledTimes(2);
    });
});
