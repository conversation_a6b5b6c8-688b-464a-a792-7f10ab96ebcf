import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { retryWithBackoff } from "@augment-internal/sidecar-libs/src/utils/promise-utils";

import { MockAPIServer } from "../__mocks__/mock-api-server";
import { getLogger } from "../logging";

describe("clientMetrics retry logic", () => {
    let mockApiServer: MockAPIServer;
    const logger = getLogger("test");

    beforeEach(() => {
        jest.resetAllMocks();
        mockApiServer = new MockAPIServer();
    });

    test("should succeed immediately when API call succeeds", async () => {
        const mockClientMetrics = jest.spyOn(mockApiServer, "clientMetrics").mockResolvedValueOnce();
        
        const metrics = [{
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: "test_metric", value: 123
        }];

        // Test the retry wrapper directly with very short delays
        await retryWithBackoff(
            async () => {
                await mockApiServer.clientMetrics(metrics);
            },
            logger,
            {
                initialMS: 1,
                mult: 2,
                maxMS: 10,
                maxTries: 3,
                canRetry: (e: unknown) => {
                    return APIError.isAPIErrorWithStatus(e, APIStatus.unavailable) ||
                           APIError.isAPIErrorWithStatus(e, APIStatus.cancelled);
                }
            }
        );

        expect(mockClientMetrics).toHaveBeenCalledTimes(1);
        expect(mockClientMetrics).toHaveBeenCalledWith(metrics);
    });

    test("should retry on unavailable errors and eventually succeed", async () => {
        const metrics = [{
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: "test_metric", value: 123
        }];
        const unavailableError = new APIError(APIStatus.unavailable, "Service unavailable");
        
        const mockClientMetrics = jest.spyOn(mockApiServer, "clientMetrics")
            .mockRejectedValueOnce(unavailableError)
            .mockResolvedValueOnce();

        await retryWithBackoff(
            async () => {
                await mockApiServer.clientMetrics(metrics);
            },
            logger,
            {
                initialMS: 1,
                mult: 2,
                maxMS: 10,
                maxTries: 3,
                canRetry: (e: unknown) => {
                    return APIError.isAPIErrorWithStatus(e, APIStatus.unavailable) ||
                           APIError.isAPIErrorWithStatus(e, APIStatus.cancelled);
                }
            }
        );

        expect(mockClientMetrics).toHaveBeenCalledTimes(2);
    });

    test("should not retry on non-retryable errors", async () => {
        const metrics = [{
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: "test_metric", value: 123
        }];
        const authError = new APIError(APIStatus.unauthenticated, "Unauthorized");
        
        const mockClientMetrics = jest.spyOn(mockApiServer, "clientMetrics")
            .mockRejectedValueOnce(authError);

        await expect(retryWithBackoff(
            async () => {
                await mockApiServer.clientMetrics(metrics);
            },
            logger,
            {
                initialMS: 1,
                mult: 2,
                maxMS: 10,
                maxTries: 3,
                canRetry: (e: unknown) => {
                    return APIError.isAPIErrorWithStatus(e, APIStatus.unavailable) ||
                           APIError.isAPIErrorWithStatus(e, APIStatus.cancelled);
                }
            }
        )).rejects.toThrow("Unauthorized");

        expect(mockClientMetrics).toHaveBeenCalledTimes(1);
    });

    test("should exhaust retries and fail after max attempts", async () => {
        const metrics = [{
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: "test_metric", value: 123
        }];
        const unavailableError = new APIError(APIStatus.unavailable, "Service unavailable");
        
        const mockClientMetrics = jest.spyOn(mockApiServer, "clientMetrics")
            .mockRejectedValue(unavailableError);

        await expect(retryWithBackoff(
            async () => {
                await mockApiServer.clientMetrics(metrics);
            },
            logger,
            {
                initialMS: 1,
                mult: 2,
                maxMS: 10,
                maxTries: 3,
                canRetry: (e: unknown) => {
                    return APIError.isAPIErrorWithStatus(e, APIStatus.unavailable) ||
                           APIError.isAPIErrorWithStatus(e, APIStatus.cancelled);
                }
            }
        )).rejects.toThrow("Service unavailable");

        expect(mockClientMetrics).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    test("should only retry on specified error types", () => {
        const canRetryFn = (e: unknown) => {
            return APIError.isAPIErrorWithStatus(e, APIStatus.unavailable) ||
                   APIError.isAPIErrorWithStatus(e, APIStatus.cancelled);
        };

        // Test retryable errors
        expect(canRetryFn(new APIError(APIStatus.unavailable, "Server unavailable"))).toBe(true);
        expect(canRetryFn(new APIError(APIStatus.cancelled, "Request cancelled"))).toBe(true);

        // Test non-retryable errors
        expect(canRetryFn(new APIError(APIStatus.unauthenticated, "Unauthorized"))).toBe(false);
        expect(canRetryFn(new APIError(APIStatus.invalidArgument, "Bad request"))).toBe(false);
        expect(canRetryFn(new APIError(APIStatus.permissionDenied, "Forbidden"))).toBe(false);
        expect(canRetryFn(new Error("Generic error"))).toBe(false);
    });
});
