import {
    MutableTextDocument,
    publishActiveTextEditorChange,
    publishTextDocumentChange,
    TextDocument,
    TextDocumentChangeEvent,
    TextDocumentContentChangeEvent,
    TextEditor,
    Uri,
} from "../../__mocks__/vscode-mocks";
import { AugmentCompletion } from "../../completions/augment-completion";
import { SuppressDeletedCompletions } from "../../completions/suppress-deleted-completions";

class SuppressDeletedCompletionsTestKit {
    public readonly suppressDeletedCompletions = new SuppressDeletedCompletions();

    public resetDocumentAndTracking(contents: string): MutableTextDocument {
        const document = new MutableTextDocument(Uri.file("file.py"), contents);
        publishActiveTextEditorChange(new TextEditor(document));
        return document;
    }

    // mutateTrackAndVerify mutates the given document and then verifies that
    // the change tracker correctly tracks the change.
    public mutateTrackAndVerify(changeEvent: TextDocumentChangeEvent, expected: string) {
        publishTextDocumentChange(changeEvent);
        expect(changeEvent.document.getText()).toBe(expected);
    }

    public assertCompletion(
        document: MutableTextDocument,
        offset: number,
        completionText: string,
        expected: boolean
    ) {
        const completionRequest = {
            document,
            requestId: "12345678-1234-1234-1234-123456789123",
            prefix: "",
            suffix: "",
            repoRoot: "",
            pathName: document.uri.toString(),
            occuredAt: new Date(),
            completions: [
                new AugmentCompletion(completionText, "", "", {
                    startOffset: offset,
                    endOffset: offset,
                }),
            ],
            isReused: false,
        };
        const newCompletionRequest =
            this.suppressDeletedCompletions.processRequest(completionRequest);
        if (expected) {
            expect(newCompletionRequest).toEqual(completionRequest);
        } else {
            expect(newCompletionRequest?.completions).toEqual([]);
        }
    }
}

describe("suppress-deleted-completions", () => {
    let kit: SuppressDeletedCompletionsTestKit;
    beforeEach(() => {
        kit = new SuppressDeletedCompletionsTestKit();
    });

    afterEach(() => {});

    test("deletion tracking", async () => {
        // Setup the document
        let document = kit.resetDocumentAndTracking("123456789");

        // Delete '5' from document
        kit.mutateTrackAndVerify(document.delete(4, 1), "12346789");

        //Ensure we don't show a completion for '5'
        kit.assertCompletion(document, 4, "5", false);
        // But we will show other completions.
        kit.assertCompletion(document, 4, "42", true);
        // And we will show completions in other places.
        kit.assertCompletion(document, 2, "42", true);

        // A strange URI shouldn't confuse us.
        publishTextDocumentChange(
            new TextDocumentChangeEvent(
                new TextDocument(Uri.from({ scheme: "42", path: "file.py" }), ""),
                [
                    TextDocumentContentChangeEvent.forInsert(
                        document,
                        document.positionAt(0),
                        0,
                        "a"
                    ),
                ],
                undefined
            )
        );

        // If we delete something to the right of what we deleted, we'll combine it with the previous deletion.
        kit.mutateTrackAndVerify(document.delete(4, 1), "1234789");
        kit.assertCompletion(document, 4, "56", false);

        // An empty change event shouldn't confuse us.
        publishTextDocumentChange(new TextDocumentChangeEvent(document, [], undefined));

        // If we delete something to the left of what we deleted, we'll combine it with the previous deletion.
        kit.mutateTrackAndVerify(document.delete(3, 1), "123789");
        kit.assertCompletion(document, 3, "456", false);

        // If we delete multiple characters to the left of what we deleted, we'll combine it with the previous deletion.
        kit.mutateTrackAndVerify(document.delete(1, 2), "1789");
        kit.assertCompletion(document, 1, "23456", false);

        // If we delete multiple characters to the right of what we deleted, we'll combine it with the previous deletion.
        kit.mutateTrackAndVerify(document.delete(1, 2), "19");
        kit.assertCompletion(document, 1, "2345678", false);

        // If we insert something we'll show completions again.
        kit.mutateTrackAndVerify(document.insert(1, "2"), "129");
        kit.assertCompletion(document, 2, "345678", true);

        // Reset the document.
        kit.mutateTrackAndVerify(document.insert(2, "345678"), "123456789");
        kit.assertCompletion(document, 4, "42", true);

        // We won't show a completion equal to what we just deleted.
        kit.mutateTrackAndVerify(document.delete(4, 1), "12346789");
        kit.assertCompletion(document, 4, "5", false);

        // Deleting to the left of the previous range means we'll show a completion to the right but not the left.
        kit.mutateTrackAndVerify(document.delete(2, 1), "1246789");
        kit.assertCompletion(document, 2, "3", false);
        kit.assertCompletion(document, 3, "5", true);

        // We'll start tracking the previous deletion and so not show a completion after deleting next to it.
        kit.mutateTrackAndVerify(document.delete(1, 1), "146789");
        kit.assertCompletion(document, 1, "23", false);

        // Deleting to the right of the previous range means we'll show a completion to the left.
        // We'll show a completion to the right because we deleted the "5" earlier.
        kit.mutateTrackAndVerify(document.delete(2, 1), "14789");
        kit.assertCompletion(document, 1, "23", true);
        kit.assertCompletion(document, 2, "56", true);

        // We won't show a completion equal to what we just deleted.
        kit.mutateTrackAndVerify(document.delete(3, 1), "1479");
        kit.assertCompletion(document, 3, "8", false);

        // Switching to a new document means we'll complete what we would not have in the same document.
        document = kit.resetDocumentAndTracking("1479");
        kit.assertCompletion(document, 3, "8", true);
    });

    test(`forward deletion for same completion`, async () => {
        const document = kit.resetDocumentAndTracking("123456789");
        kit.assertCompletion(document, 4, "a", true);

        // Delete a character to the right of the previsious completion
        // Emulate a forward deletion (i.e. a deletion using the `del` key)
        kit.mutateTrackAndVerify(document.delete(4, 1), "12346789");
        kit.assertCompletion(document, 4, "a", false);
    });

    test(`forward deletion for same completion plus removed character`, async () => {
        const document = kit.resetDocumentAndTracking("123456789");
        kit.assertCompletion(document, 4, "a", true);

        // Delete a character to the right of the previsious completion
        // Emulate a forward deletion (i.e. a deletion using the `del` key)
        kit.mutateTrackAndVerify(document.delete(4, 1), "12346789");
        kit.assertCompletion(document, 4, "a5", false);
    });

    test(`forward deletion for with different completion text`, async () => {
        const document = kit.resetDocumentAndTracking("123456789");
        kit.assertCompletion(document, 4, "a", true);

        // Delete a character to the right of the previsious completion
        // Emulate a forward deletion (i.e. a deletion using the `del` key)
        kit.mutateTrackAndVerify(document.delete(4, 1), "12346789");
        kit.assertCompletion(document, 4, "b", true);
    });

    test(`ignore deletion in a different range`, async () => {
        const document = kit.resetDocumentAndTracking("123456789");
        kit.assertCompletion(document, 4, "a", true);

        kit.mutateTrackAndVerify(document.delete(6, 1), "12345689");
        kit.assertCompletion(document, 4, "a", true);
    });

    test(`allow completions in a different document`, async () => {
        const document = kit.resetDocumentAndTracking("123456789");
        kit.assertCompletion(document, 4, "5", true);

        kit.mutateTrackAndVerify(document.delete(4, 1), "12346789");
        kit.assertCompletion(document, 4, "5", false);

        const newDocument = new MutableTextDocument(Uri.file("file2.py"), "123456789");
        kit.assertCompletion(newDocument, 4, "5", true);
    });
});
