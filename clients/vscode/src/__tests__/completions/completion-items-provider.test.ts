import { CompletionItem } from "vscode";

import { generateMockWorkspaceConfig } from "../../__mocks__/mock-augment-config";
import {
    CancellationToken,
    CompletionContext,
    CompletionItemKind,
    CompletionTriggerKind,
    MarkdownString,
    mockWorkspaceConfigChange,
    Position,
    Range,
    resetMockWorkspace,
    TextDocument,
    Uri,
} from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AugmentCompletion } from "../../completions/augment-completion";
import {
    dispatchCompletionRequest,
    dispatchCompletionRequestCancelled,
} from "../../completions/completion-events";
import { CompletionItemsProvider } from "../../completions/completion-items-provider";
import { CompletionRequest } from "../../completions/completions-model";

class CompletionProviderTestKit {
    public document = new TextDocument(Uri.parse("file://example/file"), "Hello World!");
    public position = new Position(0, 0);
    public requestId = "12345678-1234-1234-1234-123456789123";
    readonly config = new AugmentConfigListener();
    readonly provider = new CompletionItemsProvider(this.config);

    readonly defaultCompletion = new AugmentCompletion("basic completion example", "", "", {
        startOffset: 0,
        endOffset: 0,
    });
    readonly defaultCompletionContext = {
        triggerKind: CompletionTriggerKind.Invoke,
        triggerCharacter: undefined,
    };

    triggerCompletion(
        completion: AugmentCompletion = this.defaultCompletion,
        context: CompletionContext = this.defaultCompletionContext,
        opts: TriggerOptions = {}
    ): Promise<CompletionItem[]> {
        const resultPromise = this.provider.provideCompletionItems(
            this.document,
            this.position,
            new CancellationToken(),
            context
        );

        dispatchCompletionRequest(this.createCompletionRequest(opts, completion));

        return resultPromise;
    }

    createCompletionRequest(
        opts: TriggerOptions,
        ...completions: AugmentCompletion[]
    ): CompletionRequest {
        return {
            occuredAt: new Date(),
            requestId: this.requestId,
            prefix: opts?.prefix ?? "",
            suffix: "",
            repoRoot: "",
            pathName: "",
            document: this.document,
            completions,
            isReused: false,
        };
    }

    dispose() {
        this.config.dispose();
    }
}

type TriggerOptions = {
    triggerKind?: CompletionTriggerKind;
    prefix?: string;
};

describe("CompletionItemsProvider", () => {
    let kit: CompletionProviderTestKit;

    beforeEach(async () => {
        resetMockWorkspace();
        kit = new CompletionProviderTestKit();
    });

    afterEach(() => {
        kit.dispose();
        resetMockWorkspace();
    });

    it("should provide a new completion", async () => {
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const result = await kit.triggerCompletion({
            ...kit.defaultCompletion,
            completionText: "console.log('hi')",
        });
        expect(result.length).toBe(1);
        expect(result[0]).toEqual({
            insertText: "console.log('hi')",
            range: {
                inserting: new Range(0, 0, 0, 0),
                replacing: new Range(0, 0, 0, 0),
            },
            detail: "Augment",
            preselect: false,
            label: "console.log…",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("handle a short completion", async () => {
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const result = await kit.triggerCompletion({
            ...kit.defaultCompletion,
            completionText: "example",
        });
        expect(result.length).toBe(1);
        expect(result[0]).toEqual({
            insertText: "example",
            range: {
                inserting: new Range(0, 0, 0, 0),
                replacing: new Range(0, 0, 0, 0),
            },
            detail: "Augment",
            preselect: false,
            label: "example",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("return no completions if intellisense setting is false", async () => {
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const config = generateMockWorkspaceConfig({
            completions: {
                enableQuickSuggestions: false,
            },
        });
        mockWorkspaceConfigChange(config);

        const result = await kit.triggerCompletion({
            ...kit.defaultCompletion,
            completionText: "example",
        });
        expect(result.length).toBe(0);
    });

    it("return no completions when trigger kind is a character", async () => {
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const result = await kit.triggerCompletion(
            {
                ...kit.defaultCompletion,
                completionText: "console.log('hi')",
            },
            {
                triggerKind: CompletionTriggerKind.TriggerCharacter,
                triggerCharacter: CompletionItemsProvider.triggerCharacters[0],
            }
        );
        expect(result.length).toBe(0);
    });

    it("should return nothing if automatic completions are disabled", async () => {
        jest.useFakeTimers();
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const config = generateMockWorkspaceConfig({
            completions: {
                enableAutomaticCompletions: false,
            },
            advanced: {
                completions: {
                    timeoutMs: 10,
                },
            },
        });
        mockWorkspaceConfigChange(config);

        const result = await kit.provider.provideCompletionItems(
            kit.document,
            new Position(0, 0),
            new CancellationToken(),
            kit.defaultCompletionContext
        );

        expect(result.length).toBe(0);
    });

    it("should return nothing if timeout occurs before event triggers", async () => {
        jest.useFakeTimers();
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const config = generateMockWorkspaceConfig({
            advanced: {
                completions: {
                    timeoutMs: 10,
                },
            },
        });
        mockWorkspaceConfigChange(config);

        const resultPromise = kit.provider.provideCompletionItems(
            kit.document,
            new Position(0, 0),
            new CancellationToken(),
            kit.defaultCompletionContext
        );

        jest.advanceTimersByTime(config.advanced.completions.timeoutMs + 1);

        const result = await resultPromise;

        expect(result.length).toBe(0);
    });

    it("should return nothing if max wait occurs before event triggers", async () => {
        jest.useFakeTimers();
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const config = generateMockWorkspaceConfig({
            advanced: {
                completions: {
                    timeoutMs: 10,
                    maxWaitMs: 20,
                },
            },
        });
        mockWorkspaceConfigChange(config);

        const resultPromise = kit.provider.provideCompletionItems(
            kit.document,
            new Position(0, 0),
            new CancellationToken(),
            kit.defaultCompletionContext
        );

        // Elapsed time after advance is 9ms
        jest.advanceTimersByTime(config.advanced.completions.timeoutMs - 1);

        // Reset the timer
        dispatchCompletionRequestCancelled();

        // Elapsed time after advance is 18ms
        jest.advanceTimersByTime(config.advanced.completions.timeoutMs - 1);

        // Reset the timer
        dispatchCompletionRequestCancelled();

        // Elapsed time after advance is 21ms (Just past maxWaitMs)
        jest.advanceTimersByTime(3);

        const result = await resultPromise;

        expect(result.length).toBe(0);
    });

    it("return nothing if completion is cancelled", async () => {
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));

        const cancelToken = new CancellationToken();
        const resultPromise = kit.provider.provideCompletionItems(
            kit.document,
            new Position(0, 0),
            cancelToken,
            kit.defaultCompletionContext
        );

        // Trigger a cancellation before the event is fired
        cancelToken.cancel();

        const request = kit.createCompletionRequest(
            {},
            new AugmentCompletion("example", "", "", {
                startOffset: 0,
                endOffset: 0,
            })
        );
        dispatchCompletionRequest(request);

        const result = await resultPromise;
        expect(result.length).toBe(0);
    });

    it("return nothing if completion request is undefined", async () => {
        const cancelToken = new CancellationToken();
        const resultPromise = kit.provider.provideCompletionItems(
            kit.document,
            new Position(0, 0),
            cancelToken,
            kit.defaultCompletionContext
        );

        dispatchCompletionRequest(undefined);

        const result = await resultPromise;
        expect(result.length).toBe(0);
    });

    it("return completion if request is not via trigger character", async () => {
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 0));
        const result = await kit.triggerCompletion(
            {
                ...kit.defaultCompletion,
                completionText: "console.log('hi')",
            },
            {
                triggerKind: CompletionTriggerKind.Invoke,
                triggerCharacter: undefined,
            }
        );

        expect(result.length).toBe(1);
        expect(result[0]).toEqual({
            insertText: "console.log('hi')",
            range: {
                inserting: new Range(0, 0, 0, 0),
                replacing: new Range(0, 0, 0, 0),
            },
            detail: "Augment",
            preselect: false,
            label: "console.log…",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("handle completion include text before the completion start", async () => {
        const wordPrefix = "m";

        kit.document = new TextDocument(Uri.parse("file://example/file"), `  ${wordPrefix}`);
        // The word starts before the m
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 2, 0, 2));
        // The cursor is after the m
        kit.position = new Position(0, 3);

        const result = await kit.triggerCompletion(
            new AugmentCompletion("odel = 'hi';", "", "", {
                startOffset: 3,
                endOffset: 3,
            }),
            {
                triggerKind: CompletionTriggerKind.Invoke,
                triggerCharacter: undefined,
            },
            {
                prefix: `  ${wordPrefix}`,
            }
        );

        expect(result.length).toBe(1);
        expect(result[0]).toEqual({
            insertText: "model = 'hi';",
            range: {
                inserting: new Range(0, 2, 0, 3),
                replacing: new Range(0, 2, 0, 3),
            },
            detail: "Augment",
            preselect: false,
            label: "model = …",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("handle completion including text before the completion start but has no suffix", async () => {
        const wordPrefix = "t";

        kit.document = new TextDocument(Uri.parse("file://example/file"), `  ${wordPrefix}`);
        // The word starts before the t
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 2, 0, 2));
        // The cursor is after the t
        kit.position = new Position(0, 3);

        const result = await kit.triggerCompletion(
            new AugmentCompletion("ext: string", "", "", {
                startOffset: 3,
                endOffset: 3,
            }),
            {
                triggerKind: CompletionTriggerKind.Invoke,
                triggerCharacter: undefined,
            },
            {
                prefix: `  ${wordPrefix}`,
            }
        );

        expect(result.length).toBe(1);
        expect(result[0]).toEqual({
            insertText: "text: string",
            range: {
                inserting: new Range(0, 2, 0, 3),
                replacing: new Range(0, 2, 0, 3),
            },
            detail: "Augment",
            preselect: false,
            label: "text: string",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("handle completion where request is in middle of a completion", async () => {
        kit.document = new TextDocument(Uri.parse("file://example/file"), `console.`);
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 8, 0, 8));
        kit.position = new Position(0, 8);

        const result = await kit.triggerCompletion(
            new AugmentCompletion("console.log('hello world');", "", "", {
                startOffset: 0,
                endOffset: 8,
            }),
            {
                triggerKind: CompletionTriggerKind.Invoke,
                triggerCharacter: undefined,
            }
        );

        expect(result.length).toBe(1);
        expect(result[0]).toEqual({
            insertText: "log('hello world');",
            range: {
                inserting: new Range(0, 8, 0, 8),
                replacing: new Range(0, 8, 0, 8),
            },
            detail: "Augment",
            preselect: false,
            label: "log…",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("handle completion where request position is different to the start of the Augment completion", async () => {
        kit.document = new TextDocument(Uri.parse("file://example/file"), `cons`);
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 4));
        kit.position = new Position(0, 1);

        const result = await kit.triggerCompletion(
            new AugmentCompletion("ole.log('hello world');", "", "", {
                startOffset: 4,
                endOffset: 8,
            }),
            {
                triggerKind: CompletionTriggerKind.Invoke,
                triggerCharacter: undefined,
            },
            {
                prefix: `cons`,
            }
        );

        expect(result.length).toBe(1);
        const range = new Range(0, 0, kit.position.line, kit.position.character);
        expect(result[0]).toEqual({
            insertText: "console.log('hello world');",
            range: {
                inserting: range,
                replacing: range,
            },
            detail: "Augment",
            preselect: false,
            label: "console.log…",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("handle skip tokens while request position is different to the start of the Augment completion", async () => {
        kit.document = new TextDocument(Uri.parse("file://example/file"), `cons)`);
        kit.document.getWordRangeAtPosition = jest.fn().mockReturnValue(new Range(0, 0, 0, 4));
        kit.position = new Position(0, 1);

        const result = await kit.triggerCompletion(
            new AugmentCompletion("ole.log('hello world'", ");", ")", {
                startOffset: 4,
                endOffset: 8,
            }),
            {
                triggerKind: CompletionTriggerKind.Invoke,
                triggerCharacter: undefined,
            },
            {
                prefix: `cons`,
            }
        );

        expect(result.length).toBe(1);
        const range = new Range(0, 0, kit.position.line, kit.position.character + 1);
        expect(result[0]).toEqual({
            insertText: "console.log('hello world');",
            range: {
                inserting: range,
                replacing: range,
            },
            detail: "Augment",
            preselect: false,
            label: "console.log…",
            kind: CompletionItemKind.Snippet,
            documentation: expect.any(MarkdownString),
            keepWhitespace: true,
            command: undefined,
            filterText: undefined,
            additionalTextEdits: undefined,
            commitCharacters: undefined,
            sortText: undefined,
            textEdit: undefined,
        });
    });

    it("return nothing if requested position is before the completion", async () => {
        // The position will be the very start of the document, but all
        // completions will be for the second line of the document
        const documentContent = "\nconsole.";
        kit.position = new Position(0, 0);

        kit.document = new TextDocument(Uri.parse("file://example/file"), documentContent);

        // Set the word range to contain "console" on the second line
        kit.document.getWordRangeAtPosition = jest
            .fn()
            .mockReturnValue(new Range(1, 0, 1, documentContent.length - 1));

        const result = await kit.triggerCompletion(
            // The completion will be for the second line of the document
            // at the end of "console."
            new AugmentCompletion("log('hi')", "", "", {
                startOffset: documentContent.length,
                endOffset: documentContent.length,
            }),
            undefined,
            {
                prefix: documentContent,
            }
        );

        expect(result.length).toBe(0);
    });

    it("return nothing if requested position is after the completion", async () => {
        // The position will be the very end of the document, but all
        // completions will be for the first line of the document
        const documentContent = "console.\n";
        kit.position = new Position(1, 0);

        kit.document = new TextDocument(Uri.parse("file://example/file"), documentContent);

        // Set the word range to contain "console" on the first line
        kit.document.getWordRangeAtPosition = jest
            .fn()
            .mockReturnValue(new Range(0, 0, 0, documentContent.length - 2));

        const result = await kit.triggerCompletion(
            // The completion will be for the second line of the document
            // at the end of "console."
            new AugmentCompletion("log('hi')", "", "", {
                startOffset: documentContent.length - 1,
                endOffset: documentContent.length - 1,
            }),
            undefined,
            {
                prefix: documentContent,
            }
        );

        expect(result.length).toBe(0);
    });
});
