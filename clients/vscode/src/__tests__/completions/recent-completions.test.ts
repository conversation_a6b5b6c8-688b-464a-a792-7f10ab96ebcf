import { AugmentCompletion } from "../../completions/augment-completion";
import { CompletionRequest } from "../../completions/completions-model";
import { RecentCompletions } from "../../completions/recent-completions";

describe("recent-completions", () => {
    let sequence = 0;
    const newCompletionRequest = (isReused = false): CompletionRequest => ({
        requestId: `request-id-${sequence++}`,
        prefix: "",
        suffix: "",
        pathName: "",
        isReused,
        document: null as any,
        completions: [new AugmentCompletion("test", "", "", { startOffset: 0, endOffset: 0 })],
        repoRoot: "",
        occuredAt: new Date(),
    });

    test("addItem", () => {
        const req1 = newCompletionRequest();
        const reqReused = newCompletionRequest(true);
        const req2 = newCompletionRequest();

        const recentCompletions = new RecentCompletions();
        recentCompletions.addItem(req1);
        expect(recentCompletions.items.length).toBe(1);

        recentCompletions.addItem(req2);
        expect(recentCompletions.items.length).toBe(2);

        recentCompletions.addItem(reqReused);
        expect(recentCompletions.items.length).toBe(2);

        expect(recentCompletions.items).toEqual([req1, req2]);
    });

    test("addItem with no completions", () => {
        const req = newCompletionRequest();
        req.completions = [];

        const recentCompletions = new RecentCompletions();
        recentCompletions.addItem(req);
        expect(recentCompletions.items.length).toBe(0);
    });
});
