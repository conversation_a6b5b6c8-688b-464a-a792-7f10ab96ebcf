import { mockFSUtils } from "../../__mocks__/fs-utils";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import {
    MutableTextDocument,
    openTextDocument,
    Position,
    publishTextDocumentChange,
    Range,
    resetMockWorkspace,
    TextDocumentChangeEvent,
    TrackedDisposable,
    Uri,
    window,
} from "../../__mocks__/vscode-mocks";
import { AugmentCompletion } from "../../completions/augment-completion";
import {
    dispatchCompletionRequest,
    onCompletionResolved,
} from "../../completions/completion-events";
import { CompletionRequest } from "../../completions/completions-model";
import { PendingCompletion } from "../../completions/pending-completion";
import { CompletionAcceptanceReporter } from "../../metrics/completion-acceptance-reporter";
import { OnboardingSessionEventReporter } from "../../metrics/onboarding-session-event-reporter";
import { DisposableService } from "../../utils/disposable-service";

class PendingCompletionsTestKit extends DisposableService {
    readonly apiServer = new MockAPIServer();
    readonly pendingCompletions = new PendingCompletion(
        new CompletionAcceptanceReporter(
            this.apiServer,
            new OnboardingSessionEventReporter(this.apiServer)
        )
    );
    private _requestIDSequence = 0;

    constructor() {
        super();
        this.addDisposable(this.pendingCompletions);
    }

    get requestID(): string {
        return `request-id-${this._requestIDSequence++}`;
    }

    public createAndOpenFile(fileName: string, contents: string): MutableTextDocument {
        const uri = Uri.file(fileName);
        mockFSUtils.writeFileUtf8(fileName, contents, true);
        return openTextDocument(uri);
    }

    createCompletionRequest(
        document: MutableTextDocument,
        ...completions: AugmentCompletion[]
    ): CompletionRequest {
        return {
            occuredAt: new Date(),
            // Because the pending completions check if the request ID matches
            // the pending completion, we need to make sure the request ID is unique.
            requestId: this.requestID,
            prefix: "",
            suffix: "",
            repoRoot: "",
            pathName: "",
            document: document,
            completions,
            isReused: false,
        };
    }
}

describe("PendingCompletions", () => {
    let kit: PendingCompletionsTestKit;

    beforeEach(() => {
        TrackedDisposable.resetStats();
        kit = new PendingCompletionsTestKit();
        resetMockWorkspace();
    });

    afterEach(() => {
        kit.dispose();
        TrackedDisposable.assertDisposed();
        resetMockWorkspace();
    });

    test("basic", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Helper function to insert some characters of the current (still pending) completion
        // and verify that the completion is reused.
        let consumed = 0;
        const verifyInsert = async (toConsume: number) => {
            const editEvent = document.insert(
                cursorPos + consumed,
                completionText.slice(consumed, consumed + toConsume)
            );
            publishTextDocumentChange(editEvent);
            consumed += toConsume;

            const got = kit.pendingCompletions.getPendingCompletion(
                document,
                new Position(0, cursorPos + consumed)
            );
            expect(got?.completions.length).toBe(1);
            expect(got?.completions[0]).toEqual(
                new AugmentCompletion(completionText, "", "", {
                    startOffset: cursorPos,
                    endOffset: cursorPos + consumed,
                })
            );
        };

        // Verify that inserts of different sizes, as well as consecutive inserts, properly
        // reuse the completion.
        await verifyInsert(1);
        await verifyInsert(4);
    });

    test("support completion with skip suffix", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const skipSuffix = ")";
        const suffixReplacement = ") + 1;";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, suffixReplacement, skipSuffix, {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Helper function to insert some characters of the current (still pending) completion
        // and verify that the completion is reused.
        let consumed = 0;
        const verifyInsert = async (toConsume: number) => {
            const editEvent = document.insert(
                cursorPos + consumed,
                completionText.slice(consumed, consumed + toConsume)
            );
            publishTextDocumentChange(editEvent);
            consumed += toConsume;

            const got = kit.pendingCompletions.getPendingCompletion(
                document,
                new Position(0, cursorPos + consumed)
            );
            expect(got?.completions.length).toBe(1);
            expect(got?.completions[0]).toEqual(
                new AugmentCompletion(completionText, suffixReplacement, skipSuffix, {
                    startOffset: cursorPos,
                    endOffset: cursorPos + consumed,
                })
            );
        };

        // Verify that inserts of different sizes, as well as consecutive inserts, properly
        // reuse the completion.
        await verifyInsert(1);
        await verifyInsert(4);
    });

    test("support completion typing past all skips", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "y=f()";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = 4;

        // Arrange for a particular completion to be returned.
        const completionText = "x";
        const skipSuffix = ")";
        const suffixReplacement = ") + 1;";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, suffixReplacement, skipSuffix, {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Helper function to insert some characters of the current (still pending) completion
        // and verify that the completion is reused.
        let consumed = 0;
        const verifyInsert = async (toConsume: number, wantEndOffset: number) => {
            const editEvent = document.update(
                cursorPos + consumed,
                1,
                (completionText + suffixReplacement).slice(consumed, consumed + toConsume)
            );
            publishTextDocumentChange(editEvent);
            consumed += toConsume;

            const got = kit.pendingCompletions.getPendingCompletion(
                document,
                new Position(0, cursorPos + consumed)
            );
            expect(got?.completions.length).toBe(1);
            expect(got?.completions[0]).toEqual(
                new AugmentCompletion(completionText + suffixReplacement, "", "", {
                    startOffset: cursorPos,
                    endOffset: wantEndOffset,
                })
            );
        };

        // Verify that insertion after the skip works
        await verifyInsert(3, cursorPos + 3);
    });

    test("support completion type text", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        publishTextDocumentChange(document.update(2, 0, "foo"));
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 5));
        expect(got?.completions).toEqual([
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 5 }),
        ]);
    });

    test("support completion type text and backspace", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // delete "o", document is "([fo<cursor>])"
        publishTextDocumentChange(document.update(4, 1, ""));
        // new request at "([fo<cursor>])" should reuse pending completion after backspacing.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 4));
        expect(got?.completions).toEqual([
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 4 }),
        ]);
    });

    test("support completion type text between skips as update", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // replace "]" with "]bar" (i.e. skip "]"), document is "([foo]bar<cursor>)"
        publishTextDocumentChange(document.update(5, 1, "]bar"));
        // new request at "([foo]bar<cursor>)" should reuse pending completion after skipping.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 9));
        // NOTE: skippedSuffix should drop the "]" character!
        // Something like `new AugmentCompletion("foo]bar", ")baz", ")")` would also be valid,
        // since they both replace the suffix ")" with "foo]bar)baz", but PendingCompletions chooses
        // the easier implementation.
        expect(got?.completions).toEqual([
            new AugmentCompletion("foo", "]bar)baz", ")", { startOffset: 2, endOffset: 9 }),
        ]);
    });

    test("support completion delete skip", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // delete "]" and ")", document is "([foo<cursor>"
        publishTextDocumentChange(document.update(5, 2, ""));
        // new request at "([foo<cursor>" should reuse pending completion, even though we deleted
        // the skips without reinserting them.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 5));
        // NOTE: Should return a plain completion
        expect(got?.completions).toEqual([
            new AugmentCompletion("foo]bar)baz", "", "", { startOffset: 2, endOffset: 5 }),
        ]);
    });

    test("support completion type text between skips as insert", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // insert "bar" after "]" (i.e. skip "]"), document is "([foo]bar<cursor>)"
        publishTextDocumentChange(document.update(6, 0, "bar"));
        // new request at "([foo]bar<cursor>)" should reuse pending completion after skipping.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 9));
        // NOTE: skippedSuffix should drop the "]" character!
        expect(got?.completions).toEqual([
            new AugmentCompletion("foo", "]bar)baz", ")", { startOffset: 2, endOffset: 9 }),
        ]);
    });

    test("discard completion delete text not at cursor", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // delete middle "o" of "foo"
        publishTextDocumentChange(document.update(3, 1, ""));
        // we do not accept deleting the middle o.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 4));
        expect(got).toBeUndefined();
    });

    test("support completion backspace skip", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // replace "]" with "foo]" (i.e. skip "]"), document is "([foo]<cursor>)"
        publishTextDocumentChange(document.update(2, 1, "foo]"));
        // delete "]", document is "([foo<cursor>)"
        publishTextDocumentChange(document.update(5, 1, ""));
        // new request at "([foo<cursor>)" should reuse pending completion, even though we deleted
        // the skip after skipping it.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 5));
        // NOTE: skippedSuffix should drop the "]" character!
        expect(got?.completions).toEqual([
            new AugmentCompletion("foo", "]bar)baz", ")", { startOffset: 2, endOffset: 5 }),
        ]);
    });

    test("support completion request between start and cursor", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // new request at "([f<cursor>oo)" is before the pending completions cursor.
        // but we should still be able to reuse it.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 3));
        expect(got?.completions).toEqual([
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 5 }),
        ]);
    });

    test("accepts type whole completion kitchen sink", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        const listenerFn = jest.fn();
        const listenerDispose = onCompletionResolved(listenerFn);
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // delete "o", document is "([fo<cursor>])"
        publishTextDocumentChange(document.update(4, 1, ""));
        // replace "o" with "o]", document is "([foo]<cursor>)"
        publishTextDocumentChange(document.update(4, 1, "o]"));
        // should drop ] from skipped suffix
        expect(
            kit.pendingCompletions.getPendingCompletion(document, new Position(0, 6))?.completions
        ).toEqual([
            new AugmentCompletion("foo", "]bar)baz", ")", { startOffset: 2, endOffset: 6 }),
        ]);
        // delete "]", document is "([foo<cursor>)"
        publishTextDocumentChange(document.update(5, 1, ""));
        // replace ")" with "]bar)", document is "([foo]bar)<cursor>"
        publishTextDocumentChange(document.update(5, 1, "]bar)"));
        // should give a plain completion
        expect(
            kit.pendingCompletions.getPendingCompletion(document, new Position(0, 10))?.completions
        ).toEqual([
            new AugmentCompletion("foo]bar)baz", "", "", { startOffset: 2, endOffset: 10 }),
        ]);
        // insert "baz", document is "([foo]bar)baz<cursor>"
        publishTextDocumentChange(document.update(10, 0, "baz"));
        // completion should be accepted
        expect(
            kit.pendingCompletions.getPendingCompletion(document, new Position(0, 13))
        ).toBeUndefined();
        expect(listenerFn).toHaveBeenCalledWith({
            requestId: request.requestId,
            acceptedIdx: 0,
            document,
        });
        listenerDispose.dispose();
    });

    test("accepts redundant suffix", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "bar]baz)", "])", { startOffset: 2, endOffset: 2 })
        );
        const listenerFn = jest.fn();
        const listenerDispose = onCompletionResolved(listenerFn);

        dispatchCompletionRequest(request);
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // The ending ) is redundant, so should be stripped.
        expect(
            kit.pendingCompletions.getPendingCompletion(document, new Position(0, 5))?.completions
        ).toEqual([new AugmentCompletion("foo", "bar]baz", "]", { startOffset: 2, endOffset: 5 })]);
        // If we don't type the ), it should be accepted.
        publishTextDocumentChange(document.update(5, 1, "bar]baz"));
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 12));
        expect(got).toBeUndefined();
        expect(listenerFn).toHaveBeenCalledWith({
            requestId: request.requestId,
            acceptedIdx: 0,
            document,
        });
        listenerDispose.dispose();
    });

    test("accepts type more than completion", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])_");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        const listenerFn = jest.fn();
        const listenerDispose = onCompletionResolved(listenerFn);

        dispatchCompletionRequest(request);
        // replace "]" with "foo]", document is "([foo]<cursor>)_"
        publishTextDocumentChange(document.update(2, 1, "foo]"));
        // replace ")_" with "bar)baz...", document is "([foo]bar)baz...<cursor>"
        publishTextDocumentChange(document.update(6, 2, "bar)baz..."));
        // even though we added and deleted past the end of the completion, it should be accepted.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 16));
        expect(got).toBeUndefined();
        expect(listenerFn).toHaveBeenCalledWith({
            requestId: request.requestId,
            acceptedIdx: 0,
            document,
        });
        listenerDispose.dispose();
    });

    test("discard completion if adding replacement but not deleting suffix", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo]", document is "([foo]<cursor>])"
        publishTextDocumentChange(document.update(2, 0, "foo]"));
        // pending completion should be invalidated because we didn't also delete "]".
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 6));
        expect(got).toBeUndefined();
    });

    test("discard completion if deleting too much suffix", async () => {
        const document = kit.createAndOpenFile("/some-file.py", "([])_");
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion("foo", "]bar)baz", "])", { startOffset: 2, endOffset: 2 })
        );
        dispatchCompletionRequest(request);
        // insert "foo", document is "([foo<cursor>])_"
        publishTextDocumentChange(document.update(2, 0, "foo"));
        // delete "])_", document is "([foo<cursor>"
        publishTextDocumentChange(document.update(5, 3, ""));
        // pending completion should be invalidated because we deleted past the skippedSuffix.
        const got = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 4));
        expect(got).toBeUndefined();
    });

    test("do not use completion if position if wrong", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "skipSuffix", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos + 1)
        );
        expect(got).toBeUndefined();
    });

    test("ignore undefined completion requests", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got?.completions.length).toBe(1);
        expect(got?.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        dispatchCompletionRequest(undefined);

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2?.completions.length).toBe(1);
        expect(got2?.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
    });

    // ignore-unrelated-edits verifies that edits to other documents do not cause a pending
    // completion to be rejected. This is important because (for example) writing to the extension
    // log is reported as an edit, and we don't want our own logging to affect completion reuse.
    test("ignore-unrelated-edits", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        let cursorPos = Math.floor(fileContents.length / 2);

        // Open another file.
        const unrelatedDocument = kit.createAndOpenFile(
            "/some-other-file.py",
            "some arbitrary text"
        );

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Request a completion and verify that it matches the expected completion text.
        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        // Insert some unrelated text into the unrelated file.
        const unrelatedEditEvent = unrelatedDocument.insert(0, "i'm not kidding");
        publishTextDocumentChange(unrelatedEditEvent);

        // Request a second completion from the original file. The unrelated edit should
        // not prevent us from reusing the pending completion.
        const insertLength = 3;
        const event = document.insert(cursorPos, completionText.slice(0, insertLength));
        publishTextDocumentChange(event);
        cursorPos += insertLength;

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2!.completions.length).toBe(1);
        expect(got2!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: 5,
                endOffset: cursorPos,
            })
        );
    });

    // insert-at-wrong-location verifies that an insert at a location other than the current
    // position of a pending completion causes the completion to be discarded.
    test("insert-at-wrong-location", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        let cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Request a completion and verify that it matches the expected completion text.
        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        // Insert at a location that doesn't match the pending completion (cursorPos - 1).
        // This should cause the pending completion to be discarded.
        const insertLength = 3;
        const event = document.insert(cursorPos - 1, completionText.slice(0, insertLength));
        publishTextDocumentChange(event);
        cursorPos += insertLength;

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    // insert-wrong-content verifies that an insert that doesn't match the contents of a pending
    // completion causes the completion to be discarded.
    test("insert-wrong-content", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        let cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Request a completion and verify that it matches the expected completion text.
        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        // Insert a string that doesn't match the contents of the pending completion.
        // This should cause the completion to be discarded.
        const insertLength = 3;
        const event = document.insert(cursorPos, "non-matching text");
        publishTextDocumentChange(event);
        cursorPos += insertLength;

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    // completion-in-wrong-document verifies that a completion request in document other than the
    // one used by the pending completion causes the pending completion to be discarded.
    test("completion-in-wrong-document", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        let cursorPos = Math.floor(fileContents.length / 2);

        // Open another file.
        const unrelatedDocument = kit.createAndOpenFile("/some-other-file.py", fileContents);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Request a completion and verify that it matches the expected completion text.
        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        // Request a completion from the unrelated file. It should not use the pending
        // completion.
        const got2 = kit.pendingCompletions.getPendingCompletion(
            unrelatedDocument,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();

        // Request a second completion from the original file. It should also not
        // use the (new) pending completion.
        const got3 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got3).toBeUndefined();
    });

    // completion-at-wrong-location verifies that a completion request at a location other than
    // the one used by the pending completion causes the pending completion to be discarded.
    test("completion-at-wrong-location", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        let cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        // Request a completion and verify that it matches the expected completion text.
        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        // Request a second completion from the original file, but at the wrong location
        // (cursorPos + 1). It should also not use the pending completion.
        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2!.completions.length).toBe(1);
        expect(got2!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
    });

    test("reset for request with no completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        const request2 = kit.createCompletionRequest(document);
        dispatchCompletionRequest(request2);

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    test("ignore completions with same request ID", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        const requestId = kit.requestID;

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        request.requestId = requestId;
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        const request2 = kit.createCompletionRequest(document);
        // NOTE: The request ID is the same as above.
        request2.requestId = requestId;
        request2.completions = [];
        dispatchCompletionRequest(request2);

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2!.completions.length).toBe(1);
        expect(got2!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
    });

    test("reset on empty completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
    });

    test("reset for more than one completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        const request2 = kit.createCompletionRequest(document);
        request2.completions = [
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            }),
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            }),
        ];
        dispatchCompletionRequest(request2);

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    test("ignore text document change when there is no pending completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        const editEvent = document.insert(cursorPos, "exmaple");
        publishTextDocumentChange(editEvent);
    });

    test("reset when there are multiple content changes", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        publishTextDocumentChange(
            new TextDocumentChangeEvent(
                document,
                [
                    {
                        range: new Range(new Position(0, cursorPos), new Position(0, cursorPos)),
                        rangeOffset: cursorPos,
                        rangeLength: 0,
                        text: "exmaple",
                    },
                    {
                        range: new Range(new Position(0, cursorPos), new Position(0, cursorPos)),
                        rangeOffset: cursorPos,
                        rangeLength: 0,
                        text: "exmaple",
                    },
                ],
                undefined
            )
        );

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    test("do nothing if text document change is not a content change", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        // One example of a non-content change event being triggered is when
        // auto-save is on. This should not cause the pending completion to be
        // discarded.
        publishTextDocumentChange(new TextDocumentChangeEvent(document, [], undefined));

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2!.completions.length).toBe(1);
        expect(got2!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
    });

    test("reset if range extends the consumed characters", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        publishTextDocumentChange(
            new TextDocumentChangeEvent(
                document,
                [
                    {
                        range: new Range(new Position(0, cursorPos), new Position(0, cursorPos)),
                        rangeOffset: cursorPos,
                        rangeLength: 1,
                        text: "exmaple",
                    },
                ],
                undefined
            )
        );

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    test("use pending completion if change offset is before the pending completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "c";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(1);

        // Arrange for a particular completion to be returned.
        const completionText = "onst foo = bar;";
        const completionWord = "onst";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        // Ignore the change event from the mock insert() method as it doesn't
        // use a replacement range, only an insert.
        document.insert(1, completionWord);
        publishTextDocumentChange(
            new TextDocumentChangeEvent(
                document,
                [
                    {
                        range: new Range(new Position(0, 0), new Position(0, 1)),
                        rangeOffset: 0,
                        rangeLength: 1,
                        text: "c" + completionWord,
                    },
                ],
                undefined
            )
        );

        const got2 = kit.pendingCompletions.getPendingCompletion(document, new Position(0, 5));
        expect(got2).toBeDefined();
        expect(got2!.completions.length).toBe(1);
        expect(got2!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos + completionWord.length,
            })
        );
    });

    test("reset if text change events extending past the completion text", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        publishTextDocumentChange(
            new TextDocumentChangeEvent(
                document,
                [
                    {
                        range: new Range(new Position(0, cursorPos), new Position(0, cursorPos)),
                        rangeOffset: cursorPos,
                        rangeLength: 0,
                        text: completionText + "1",
                    },
                ],
                undefined
            )
        );

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    test("user selects the entire completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        publishTextDocumentChange(
            new TextDocumentChangeEvent(
                document,
                [
                    {
                        range: new Range(new Position(0, cursorPos), new Position(0, cursorPos)),
                        rangeOffset: cursorPos,
                        rangeLength: 0,
                        text: completionText,
                    },
                ],
                undefined
            )
        );

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    test("dispatches accepted event when user accepts a completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        const listenerFn = jest.fn();
        const listenerDispose = onCompletionResolved(listenerFn);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        publishTextDocumentChange(document.insert(cursorPos, completionText));
        expect(listenerFn).toHaveBeenCalledWith({
            requestId: request.requestId,
            acceptedIdx: 0,
            document,
        });
        listenerDispose.dispose();
    });

    test("dispatches rejected event when user does not accept a completion", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        const listenerFn = jest.fn();
        const listenerDispose = onCompletionResolved(listenerFn);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const request = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(request);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        publishTextDocumentChange(document.insert(cursorPos, "something else"));
        expect(listenerFn).toHaveBeenCalledWith({
            requestId: request.requestId,
            acceptedIdx: -1,
            document,
        });
        listenerDispose.dispose();
    });
});

describe("PendingCompletions window changes", () => {
    let kit: PendingCompletionsTestKit;

    beforeEach(() => {
        kit = new PendingCompletionsTestKit();
        resetMockWorkspace();
    });

    afterEach(() => {
        resetMockWorkspace();
    });

    test("rejects pending completions when the active editor changes", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const req = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(req);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        window.activeTextEditorChanged.fire(undefined);

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });

    test("rejects pending completions when the window state changes", async () => {
        // Create and open a file with the given contents. Choose an arbitrary point as our
        // cursor position within the file.
        const fileContents = "0123456789";
        const document = kit.createAndOpenFile("/some-file.py", fileContents);
        const cursorPos = Math.floor(fileContents.length / 2);

        // Arrange for a particular completion to be returned.
        const completionText = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const req = kit.createCompletionRequest(
            document,
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );
        dispatchCompletionRequest(req);

        const got = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got!.completions.length).toBe(1);
        expect(got!.completions[0]).toEqual(
            new AugmentCompletion(completionText, "", "", {
                startOffset: cursorPos,
                endOffset: cursorPos,
            })
        );

        window.windowStateChanged.fire(undefined);

        const got2 = kit.pendingCompletions.getPendingCompletion(
            document,
            new Position(0, cursorPos)
        );
        expect(got2).toBeUndefined();
    });
});
