import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import { ReplacementText } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { GitOperationsService } from "@augment-internal/sidecar-libs/src/git/git-operations-service";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { GitStateManager } from "@augment-internal/sidecar-libs/src/vcs/iso-git/workspace-manager";
import * as vscode from "vscode";

import { QualifiedPathName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import { mockFSUtils } from "../../__mocks__/fs-utils";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { generateMockWorkspaceConfig } from "../../__mocks__/mock-augment-config";
import {
    mockDefaultModelPrefixLength,
    mockDefaultModelSuffixLength,
} from "../../__mocks__/mock-modelinfo";
import {
    emptyTextDocumentChangeEvent,
    ExtensionContext,
    Memento,
    mockWorkspaceConfigChange,
    MutableTextDocument,
    NotebookCell,
    NotebookCellKind,
    NotebookDocument,
    NotebookDocumentChangeEvent,
    NotebookEditor,
    openTextDocument,
    Position,
    publishTextDocumentChange,
    resetMockWorkspace,
    TextDocument,
    Uri,
    window,
} from "../../__mocks__/vscode-mocks";
import { CompletionLocation, CompletionResult } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AuthSessionStore } from "../../auth/auth-session-store";
import { ChatRequest } from "../../chat/chat-model";
import { AssetManager } from "../../client-interfaces/asset-manager";
import { AugmentInstruction } from "../../code-edit-types";
import { AugmentCompletion } from "../../completions/augment-completion";
import { CompletionRequest, CompletionsModel } from "../../completions/completions-model";
import { RecentCompletions } from "../../completions/recent-completions";
import { SkipCompletion } from "../../exceptions";
import { AugmentExtension } from "../../extension";
import { ActionsModel } from "../../main-panel/action-cards/actions-model";
import { ChatExtensionMessage } from "../../main-panel/apps/chat-webview-app";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";
import { OnboardingSessionEventReporter } from "../../metrics/onboarding-session-event-reporter";
import { NextEditResultInfo } from "../../next-edit/next-edit-types";
import { AugmentGlobalState } from "../../utils/context";
import { RecentItems } from "../../utils/recent-items";
import { uriToAbsPath } from "../../utils/uri";
import { MainPanelWebviewProvider } from "../../webview-providers/main-panel-webview-provider";
import { MainPanelApp, NextEditWebViewMessage } from "../../webview-providers/webview-messages";
import { SyncingEnabledTracker } from "../../workspace/syncing-enabled-tracker";
import { ViewedContentInfo } from "../../workspace/viewed-content-tracker";
import { verifyDefined } from "../__utils__/test-utils";
import { advanceTimeUntilTrue } from "../__utils__/time";
import {
    awaitWorkspaceState,
    defaultWorkspaceState,
    initWorkspace,
    makeAbsPath,
    MockSourceFolder,
    MockSourceFolderState,
} from "../workspace/workspace-manager-test-kit";

class CompletionModelTestKit {
    public readonly completionsModel: CompletionsModel;
    public readonly metrics: ClientMetricsReporter;

    constructor(
        public readonly extension: AugmentExtension,
        public readonly apiServer: MockAPIServer,
        public readonly config: AugmentConfigListener
    ) {
        this.metrics = new ClientMetricsReporter(apiServer);
        this.completionsModel = new CompletionsModel(extension, config, this.metrics);
    }

    public getQualifiedPathName(absPath: string): QualifiedPathName | undefined {
        return this.extension.workspaceManager!.resolvePathName(absPath);
    }

    public async openFile(
        sourceFolder: MockSourceFolder,
        relPath: string
    ): Promise<MutableTextDocument> {
        const uri = Uri.joinPath(sourceFolder.folderRootUri, relPath);
        const document = new MutableTextDocument(uri, "");
        publishTextDocumentChange(emptyTextDocumentChangeEvent(document));
        await advanceTimeUntilTrue(() => {
            const workspaceContext = this.extension.workspaceManager!.getContext();
            for (const repoPathMap of workspaceContext.trackedPaths.values()) {
                if (repoPathMap.has(relPath)) {
                    return true;
                }
            }
            return false;
        });

        return document;
    }

    generateCompletion(
        document: TextDocument,
        position: Position
    ): Promise<CompletionRequest | undefined> {
        return this.completionsModel.generateCompletion(document, position);
    }

    dispose() {
        this.extension.dispose();
    }

    static async create(
        workspaceStorage_: Memento | undefined,
        workspaceState: Array<MockSourceFolderState>,
        completionResponseParams: {
            suggestedPrefixCharCount: number | undefined;
            suggestedSuffixCharCount: number | undefined;
        } = MockAPIServer.defaultCompletionResponseParams
    ): Promise<CompletionModelTestKit> {
        const context = new ExtensionContext();
        const config = new AugmentConfigListener();
        const apiServer = new MockAPIServer(
            undefined,
            undefined,
            "placeholder-session-id",
            completionResponseParams
        );
        const auth = new AuthSessionStore(context, config);
        const recentCompletions = new RecentCompletions();
        const recentInstructions = new RecentItems<AugmentInstruction>(10);
        const recentNextEditLocations = new RecentItems<NextEditResultInfo>(10);
        const recentChats = new RecentItems<ChatRequest>(10);
        const nextEditWebViewEvent = new vscode.EventEmitter<NextEditWebViewMessage>();
        const globalState = new AugmentGlobalState(context);
        const extensionUpdateEvent = new vscode.EventEmitter<void>();
        const syncingStateTracker = new SyncingEnabledTracker();
        const mockAssetManager = new AssetManager(context);
        const mockGitOperationsService = new GitOperationsService(new GitStateManager());
        const extension = new AugmentExtension(
            context,
            globalState,
            config,
            apiServer,
            auth,
            recentCompletions,
            recentInstructions,
            recentNextEditLocations,
            recentChats,
            nextEditWebViewEvent,
            extensionUpdateEvent,
            new MainPanelWebviewProvider(context.extensionUri),
            new vscode.EventEmitter<MainPanelApp>(),
            new ActionsModel(globalState),
            syncingStateTracker,
            new vscode.EventEmitter<ChatExtensionMessage>(),
            new OnboardingSessionEventReporter(apiServer),
            mockAssetManager,
            mockGitOperationsService
        );

        await extension.enable();
        await awaitWorkspaceState(extension.workspaceManager!, workspaceState);

        return new CompletionModelTestKit(extension, apiServer, config);
    }
}

describe("CompletionsModel.generateCompletion", () => {
    const workspaceState = defaultWorkspaceState;
    const workspaceFolder = workspaceState[0];
    const relPath = "completion-file.py";
    const absPath = makeAbsPath(workspaceState[0], relPath);

    let kit: CompletionModelTestKit;
    let document: MutableTextDocument;
    const topOfDocument = new Position(0, 0);

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        const workspaceStorage = new Memento();
        initWorkspace(workspaceState);
        kit = await CompletionModelTestKit.create(workspaceStorage, workspaceState);

        // Create a file in the workspace and force OpenFileManager to track it.
        mockFSUtils.writeFileUtf8(absPath, "Hello World!", true);
        document = openTextDocument(Uri.file(absPath));
        publishTextDocumentChange(emptyTextDocumentChangeEvent(document));
        await advanceTimeUntilTrue(() => {
            const qualifiedPathName = kit.getQualifiedPathName(absPath);
            const blobName = kit.extension.workspaceManager!.getBlobName(qualifiedPathName!);
            return blobName !== undefined;
        });
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("return new completion", async () => {
        jest.spyOn(kit.metrics, "report");

        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                path: string,
                blobName: string | undefined,
                completionLocation: CompletionLocation | undefined,
                language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const qualifiedPathName = kit.getQualifiedPathName(uriToAbsPath(document.uri));
                verifyDefined(qualifiedPathName);
                expect(path).toBe(qualifiedPathName.relPath);
                expect(language).toBe("python");
                const fileLength = document.getText().length;
                expect(completionLocation).toEqual({
                    prefixBegin: 0,
                    cursorPosition: 0,
                    suffixEnd: fileLength,
                });
                verifyDefined(blobName);

                return {
                    completionItems: [
                        {
                            text: "basic completion example",
                            suffixReplacementText: "",
                            skippedSuffix: "",
                        },
                    ],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }
        );

        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions.length).toBe(1);
        expect(result?.completions[0]).toEqual(
            new AugmentCompletion("basic completion example", "", "", {
                startOffset: 0,
                endOffset: 0,
            })
        );

        expect(kit.metrics.report).toHaveBeenCalledWith({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            client_metric: "generate_completion_latency",
            value: expect.any(Number),
        });
    });

    test("handle no completions from backend", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                path: string,
                _blobName: string | undefined,
                completionLocation: CompletionLocation | undefined,
                language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const qualifiedPathName = kit.getQualifiedPathName(uriToAbsPath(document.uri));
                verifyDefined(qualifiedPathName);
                expect(path).toBe(qualifiedPathName.relPath);
                expect(language).toBe("python");
                const fileLength = document.getText().length;
                expect(completionLocation).toEqual({
                    prefixBegin: 0,
                    cursorPosition: 0,
                    suffixEnd: fileLength,
                });

                return {
                    completionItems: [],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }
        );

        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions.length).toBe(0);
    });

    test("limit completions to the first item", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                path: string,
                _blobName: string | undefined,
                completionLocation: CompletionLocation | undefined,
                language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const qualifiedPathName = kit.getQualifiedPathName(uriToAbsPath(document.uri));
                verifyDefined(qualifiedPathName);
                expect(path).toBe(qualifiedPathName.relPath);
                expect(language).toBe("python");
                const fileLength = document.getText().length;
                expect(completionLocation).toEqual({
                    prefixBegin: 0,
                    cursorPosition: 0,
                    suffixEnd: fileLength,
                });

                return {
                    completionItems: [
                        {
                            text: "first completion item",
                            suffixReplacementText: "",
                            skippedSuffix: "",
                        },
                        {
                            text: "second completion item",
                            suffixReplacementText: "",
                            skippedSuffix: "",
                        },
                    ],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }
        );

        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions.length).toBe(1);
        expect(result?.completions[0]).toEqual(
            new AugmentCompletion("first completion item", "", "", { startOffset: 0, endOffset: 0 })
        );
    });

    test("return no completions if completion item is empty string", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                path: string,
                _blobName: string | undefined,
                completionLocation: CompletionLocation | undefined,
                language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const qualifiedPathName = kit.getQualifiedPathName(uriToAbsPath(document.uri));
                verifyDefined(qualifiedPathName);
                expect(path).toBe(qualifiedPathName.relPath);
                expect(language).toBe("python");
                const fileLength = document.getText().length;
                expect(completionLocation).toEqual({
                    prefixBegin: 0,
                    cursorPosition: 0,
                    suffixEnd: fileLength,
                });

                return {
                    completionItems: [
                        {
                            text: "",
                            suffixReplacementText: "",
                            skippedSuffix: "",
                        },
                    ],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }
        );

        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions.length).toBe(0);
    });

    test("remove skipped suffix if it spans multiple lines", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: CompletionLocation | undefined,
                _language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const item = {
                    text: "basic completion example",
                    suffixReplacementText: "Goodbye\n",
                    skippedSuffix: "Hello\n",
                };
                return {
                    completionItems: [item],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }
        );

        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions.length).toBe(1);
        expect(result?.completions[0]).toEqual(
            new AugmentCompletion("basic completion example", "", "", {
                startOffset: 0,
                endOffset: 0,
            })
        );
    });

    test("support skipped suffix", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: CompletionLocation | undefined,
                _language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const item = {
                    text: "basic completion example",
                    suffixReplacementText: "Goodbye",
                    skippedSuffix: "Hello",
                };
                return {
                    completionItems: [item],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }
        );

        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions.length).toBe(1);
        expect(result?.completions[0]).toEqual(
            new AugmentCompletion("basic completion example", "Goodbye", "Hello", {
                startOffset: 0,
                endOffset: 0,
            })
        );
    });

    test("return no completion if language is disabled in users settings", async () => {
        const document = new TextDocument(Uri.parse("file://example/file.js"), "Hello World!");

        const newConfig = generateMockWorkspaceConfig({
            completions: {
                disableCompletionsByLanguage: ["javascript"],
            },
        });
        mockWorkspaceConfigChange(newConfig);

        const resultPromise = kit.generateCompletion(document, topOfDocument);
        expect(resultPromise).rejects.toThrow(SkipCompletion);
        expect(resultPromise).rejects.toThrow("Language javascript is disabled.");
    });

    test("return no completion if backend cancels request", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: CompletionLocation | undefined,
                _language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                throw new APIError(APIStatus.cancelled, "Injected error");
            }
        );

        const resultPromise = kit.generateCompletion(document, topOfDocument);
        expect(resultPromise).rejects.toThrow(SkipCompletion);
        expect(resultPromise).rejects.toThrow("Cancelled in back end");
    });

    test("throw unexpected error", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: CompletionLocation | undefined,
                _language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                throw new Error("Injected error");
            }
        );

        const resultPromise = kit.generateCompletion(document, topOfDocument);
        expect(resultPromise).rejects.toThrow(Error);
        expect(resultPromise).rejects.toThrow("Injected error");
    });

    test("test-invoked-completion", async () => {
        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions).toHaveLength(1);
        expect(result?.completions[0].completionText).toBe(MockAPIServer.cannedCompletion);
    });

    test("test-sentinel-completion", async () => {
        // If you put the cursor right before the sentiel keyword,
        // you get a sentinel completion back.
        // The keyword and completion are all kept in the MockAPIServer class.
        // Just to check if we are able
        // to actually pass text and position over
        const content = `# ${MockAPIServer.sentinelKeyword}\n\n`;
        const document = new MutableTextDocument(
            Uri.file(`/test/folder/sentinel_file.py`),
            content
        );
        const position = document.positionAt(content.indexOf(MockAPIServer.sentinelKeyword));
        const request = await kit.generateCompletion(document, position);
        expect(request!.completions).toHaveLength(1);
        expect(request!.completions[0].completionText).toBe(MockAPIServer.sentinelCompletion);
    });

    test("test-outside-roots", async () => {
        // This file is not tracked but should get a completion anyway.
        const untrackedDocument = new MutableTextDocument(
            Uri.file("/test/different_folder/new_file.py"),
            "pass"
        );
        const request = await kit.generateCompletion(untrackedDocument, topOfDocument);
        expect(request!.completions).toHaveLength(1);
        expect(request!.completions[0].completionText).toBe(MockAPIServer.cannedCompletion);
    });

    test("test-new-file-in-workspace", async () => {
        // Create a new file in the workspace.
        const relPath = "new_file.py";
        const absPath = makeAbsPath(workspaceFolder, relPath);
        const newDocument = new MutableTextDocument(Uri.file(absPath), "pass");
        const position = new Position(0, 2);
        const request = await kit.generateCompletion(newDocument, position);
        expect(request!.completions).toHaveLength(1);
        expect(request!.completions[0].completionText).toBe(MockAPIServer.cannedCompletion);
    });

    test("test-new-file-not-in-workspace", async () => {
        // Create a new file that is not in the workspace.
        const absPath = "/this/file/is/not/in/the/workspace.py";
        const outsideDocument = new MutableTextDocument(Uri.file(absPath), "pass");
        const position = new Position(0, 2);
        const request = await kit.generateCompletion(outsideDocument, position);
        expect(request!.completions).toHaveLength(1);
        expect(request!.completions[0].completionText).toBe(MockAPIServer.cannedCompletion);
    });

    test("update-prefix-suffix-length", async () => {
        // initial lengths should be the same as the model setting
        expect(kit.extension.modelInfo!.suggestedPrefixCharCount).toBe(
            mockDefaultModelPrefixLength
        );
        expect(kit.extension.modelInfo!.suggestedSuffixCharCount).toBe(
            mockDefaultModelSuffixLength
        );

        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: CompletionLocation | undefined,
                _language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const item = {
                    text: "basic completion example",
                    suffixReplacementText: "Goodbye",
                    skippedSuffix: "Hello",
                };
                return {
                    completionItems: [item],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                    suggestedPrefixCharCount: 1234,
                    suggestedSuffixCharCount: 5678,
                };
            }
        );

        // now do a completion
        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result?.completions.length).toBe(1);
        // after completion test if prefix and suffix lengths are changed.
        expect(kit.extension.modelInfo!.suggestedPrefixCharCount).not.toBe(
            mockDefaultModelPrefixLength
        );
        expect(kit.extension.modelInfo!.suggestedPrefixCharCount).toBe(1234);
        expect(kit.extension.modelInfo!.suggestedSuffixCharCount).not.toBe(
            mockDefaultModelSuffixLength
        );
        expect(kit.extension.modelInfo!.suggestedSuffixCharCount).toBe(5678);
    });

    test("no-prefix-suffix-in-response", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: CompletionLocation | undefined,
                _language: string,
                _context: Blobs,
                _recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                const item = {
                    text: "basic completion example",
                    suffixReplacementText: "Goodbye",
                    skippedSuffix: "Hello",
                };
                return {
                    completionItems: [item],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                    suggestedPrefixCharCount: undefined,
                    suggestedSuffixCharCount: undefined,
                };
            }
        );

        // initial lengths should be the same as the model setting
        expect(kit.extension.modelInfo!.suggestedPrefixCharCount).toBe(
            mockDefaultModelPrefixLength
        );
        expect(kit.extension.modelInfo!.suggestedSuffixCharCount).toBe(
            mockDefaultModelSuffixLength
        );
        // now do a completion
        const result = await kit.generateCompletion(document, topOfDocument);
        expect(result!.completions).toHaveLength(1);
        // after completion test if prefix and suffix lengths are changed.
        expect(kit.extension.modelInfo!.suggestedPrefixCharCount).toBe(
            mockDefaultModelPrefixLength
        );
        expect(kit.extension.modelInfo!.suggestedSuffixCharCount).toBe(
            mockDefaultModelSuffixLength
        );
    });

    // Test when we ask to modify the suffix length through completion response, the
    // next request honors that parameter.
    // Here we do a sentinel completion, and configure the response to set suffix
    // length to 1.  The next completion, although identical, should no longer be a sentinel
    // response because the suffix length is now shorter than keyword
    test("update-change-suffix", async () => {
        kit.dispose();

        kit = await CompletionModelTestKit.create(undefined, workspaceState, {
            suggestedPrefixCharCount: undefined,
            suggestedSuffixCharCount: 1,
        });

        const content = `# ${MockAPIServer.sentinelKeyword}\n\n`;
        let document = new MutableTextDocument(Uri.file(`/test/folder/sentinel_file.py`), content);
        let position = document.positionAt(content.indexOf(MockAPIServer.sentinelKeyword));

        // The first completion request will cause the suffix limit settings to change to new value
        await kit.generateCompletion(document, position);
        expect(kit.extension.modelInfo!.suggestedSuffixCharCount).toBe(1);

        // We want the second request to be identical to the first, but use a separate (identical)
        // document so the new completion doesn't trigger completion reuse, which would prevent
        // us from making a new back-end request.
        document = new MutableTextDocument(Uri.file(`/test/folder/sentinel_file.py`), content);
        position = document.positionAt(content.indexOf(MockAPIServer.sentinelKeyword));

        const result = await kit.generateCompletion(document, position);
        // the second request will get a canned response
        // because the suffix length was changed to 1; as a result,
        // the next completion request would not contain the sentinel keyword in the suffix
        // and therefore should not get a sentinel response
        expect(result!.completions).toHaveLength(1);
        expect(result!.completions[0].completionText).toBe(MockAPIServer.cannedCompletion);
    });

    // Vertify that the last chat response, if present, is inserted as a recent change, and that
    // it is correctly ordered within the list of changes.
    test("completion with last chat response", async () => {
        const chatResponse = "chat response";

        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: CompletionLocation | undefined,
                _language: string,
                _context: Blobs,
                recentChanges: ReplacementText[],
                _viewedContentEvents: ViewedContentInfo[]
            ): Promise<CompletionResult> => {
                verifyDefined(recentChanges);

                // The recent changes should be ordered from most recent to least recent, with the
                // chat response between the changes to file1.py and file2.py.
                expect(recentChanges).toHaveLength(4);
                expect(recentChanges[0].path).toBe("file3.py");
                expect(recentChanges[1].path).toBe("file2.py");
                expect(recentChanges[2].path).toBe("");
                expect(recentChanges[2].replacement_text).toBe(chatResponse);
                expect(recentChanges[3].path).toBe("file1.py");

                return {
                    completionItems: [
                        {
                            text: "completion text",
                            suffixReplacementText: "",
                            skippedSuffix: "",
                        },
                    ],
                    unknownBlobNames: [],
                    checkpointNotFound: false,
                };
            }
        );

        const document1 = await kit.openFile(workspaceFolder, "file1.py");
        const event1 = document1.insert(0, "some text");
        publishTextDocumentChange(event1);

        kit.extension.workspaceManager!.recordChatReponse(chatResponse);

        const document2 = await kit.openFile(workspaceFolder, "file2.py");
        const event2 = document2.insert(0, "some other text");
        publishTextDocumentChange(event2);

        const document3 = await kit.openFile(workspaceFolder, "file3.py");
        const event3 = document3.insert(0, "some other text");
        publishTextDocumentChange(event3);

        await kit.generateCompletion(document1, new Position(0, 0));
        expect(kit.apiServer.complete).toHaveBeenCalledTimes(1);
    });
});

class NotebookCompletionKit extends CompletionModelTestKit {
    static async create(): Promise<NotebookCompletionKit> {
        const context = new ExtensionContext();
        const config = new AugmentConfigListener();
        const apiServer = new MockAPIServer();
        const auth = new AuthSessionStore(context, config);
        const recentCompletions = new RecentCompletions();
        const recentInstructions = new RecentItems<AugmentInstruction>(10);
        const recentNextEdits = new RecentItems<NextEditResultInfo>(10);
        const recentChats = new RecentItems<ChatRequest>(10);
        const nextEditWebViewEvent = new vscode.EventEmitter<NextEditWebViewMessage>();
        const globalState = new AugmentGlobalState(context);
        const syncingStateTracker = new SyncingEnabledTracker();
        const mockAssetManager = new AssetManager(context);
        const mockGitOperationsService2 = new GitOperationsService(new GitStateManager());
        const extension = new AugmentExtension(
            context,
            globalState,
            config,
            apiServer,
            auth,
            recentCompletions,
            recentInstructions,
            recentNextEdits,
            recentChats,
            nextEditWebViewEvent,
            new vscode.EventEmitter<void>(),
            new MainPanelWebviewProvider(context.extensionUri),
            new vscode.EventEmitter<MainPanelApp>(),
            new ActionsModel(globalState),
            syncingStateTracker,
            new vscode.EventEmitter<ChatExtensionMessage>(),
            new OnboardingSessionEventReporter(apiServer),
            mockAssetManager,
            mockGitOperationsService2
        );
        await extension.enable();
        return new NotebookCompletionKit(extension, apiServer, config);
    }

    public static readonly notebookUri = Uri.from({
        scheme: "vscode-notebook-cell",
        path: "/test/folder/notebook.ipynb",
    });
    public readonly notebook = new NotebookDocument(NotebookCompletionKit.notebookUri);
    public readonly completionsModel: CompletionsModel;
    public readonly metrics: ClientMetricsReporter;

    constructor(
        readonly extension: AugmentExtension,
        readonly apiServer: MockAPIServer,
        readonly config: AugmentConfigListener
    ) {
        super(extension, apiServer, config);
        window.activeNotebookEditor = new NotebookEditor(this.notebook);
        this.metrics = new ClientMetricsReporter(apiServer);
        this.completionsModel = new CompletionsModel(extension, config, this.metrics);
    }

    dispose() {
        this.extension.dispose();
    }

    // addNotebookCells adds the given cells to the notebook.
    public addNotebookCells(cells: [string, NotebookCellKind][]): NotebookDocumentChangeEvent {
        return this.notebook.addCells(
            0,
            cells.map(
                (cell) =>
                    new NotebookCell(
                        this.notebook.cellCount,
                        this.notebook,
                        cell[1],
                        new TextDocument(this.notebook.uri, cell[0])
                    )
            )
        );
    }

    public verifyCompletionArgs(
        prefix: string,
        suffix: string,
        prefixBegin: number,
        cursorPosition: number,
        suffixEnd: number
    ) {
        expect(this.apiServer.lastCompletionRequestArgs?.prefix).toBe(prefix);
        expect(this.apiServer.lastCompletionRequestArgs?.suffix).toBe(suffix);
        expect(this.apiServer.lastCompletionRequestArgs?.completionLocation).toStrictEqual({
            prefixBegin,
            cursorPosition,
            suffixEnd,
        });
    }
}

describe("CompletionModels.generateCompletion for notebooks", () => {
    let kit: NotebookCompletionKit;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        kit = await NotebookCompletionKit.create();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("notebook completion", async () => {
        kit.addNotebookCells([
            ["a", NotebookCellKind.Code],
            ["bc", NotebookCellKind.Code],
            ["d", NotebookCellKind.Code],
        ]);

        await kit.generateCompletion(kit.notebook.cellAt(1).document, new Position(0, 1));

        const prefix = "a\n\nb";
        const suffix = "c\n\nd";
        kit.verifyCompletionArgs(prefix, suffix, 0, 4, 8);
    });

    test("notebook in text cell", async () => {
        kit.addNotebookCells([
            ["a", NotebookCellKind.Code],
            ["bc", NotebookCellKind.Markup],
            ["d", NotebookCellKind.Code],
        ]);
        await kit.generateCompletion(kit.notebook.cellAt(1).document, new Position(0, 1));

        const prefix = "b";
        const suffix = "c";
        kit.verifyCompletionArgs(prefix, suffix, 0, 1, 2);
    });

    test("notebook unicode", async () => {
        kit.addNotebookCells([
            ["😃", NotebookCellKind.Code],
            ["😃😃", NotebookCellKind.Code],
            ["😃😃😃", NotebookCellKind.Code],
        ]);
        await kit.generateCompletion(kit.notebook.cellAt(1).document, new Position(0, 2));

        const prefix = "😃\n\n😃";
        const suffix = "😃\n\n😃😃😃";
        kit.verifyCompletionArgs(prefix, suffix, 0, 6, 16);
    });
});
