import { hasProperty } from "../utils/types-utils";

describe("type-utils", () => {
    test("hasProperty", () => {
        const obj = {
            property: "value",
        };

        expect(hasProperty(obj, "property")).toBe(true);
        expect(hasProperty(obj, "other")).toBe(false);
        expect(hasProperty(null, "property")).toBe(false);
        expect(hasProperty(undefined, "property")).toBe(false);
        expect(hasProperty(123, "property")).toBe(false);
    });
});
