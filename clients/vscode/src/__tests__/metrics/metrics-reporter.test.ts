import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { CompletionResolution } from "../../augment-api";
import { CompletionAcceptanceReporter } from "../../metrics/completion-acceptance-reporter";
import { OnboardingSessionEventReporter } from "../../metrics/onboarding-session-event-reporter";

type CompletionReport = {
    requestId: string;
    emitTimeSec: number;
    emitTimeNsec: number;
    resolveTimeSec: number;
    resolveTimeNsec: number;
    acceptedIdx: number;
};

function toMsec(sec: number, nsec: number): number {
    return sec * 1000 + nsec / 1000000;
}

class MetricsReporterTestKit {
    public apiServer = new MockAPIServer();

    public makeMetricsReporter(
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ): CompletionAcceptanceReporter {
        return new CompletionAcceptanceReporter(
            this.apiServer,
            new OnboardingSessionEventReporter(this.apiServer),
            maxRecords,
            uploadMs,
            batchSize
        );
    }

    public makeCompletionReports(count: number): CompletionReport[] {
        const reports: CompletionReport[] = [];
        for (let i = 0; i < count; i++) {
            const base = (i + 1) * 10;
            reports.push({
                requestId: this.apiServer.createRequestId(),
                emitTimeSec: base,
                emitTimeNsec: (base + 1) * 1000000,
                resolveTimeSec: base + 2,
                resolveTimeNsec: (base + 3) * 1000000,
                acceptedIdx: i % 2 === 0 ? i : -1,
            });
        }
        return reports;
    }

    public reportCompletions(reporter: CompletionAcceptanceReporter, reports: CompletionReport[]) {
        for (const report of reports) {
            reporter.reportResolution(
                report.requestId,
                toMsec(report.emitTimeSec, report.emitTimeNsec),
                toMsec(report.resolveTimeSec, report.resolveTimeNsec),
                report.acceptedIdx
            );
        }
    }

    public verifyCompletionReports(
        reports: CompletionReport[],
        resolutions: CompletionResolution[]
    ): void {
        expect(resolutions.length).toEqual(reports.length);
        for (let i = 0; i < resolutions.length; i++) {
            expect(resolutions[i]).toEqual({
                /* eslint-disable @typescript-eslint/naming-convention */
                request_id: reports[i].requestId,
                emit_time_sec: reports[i].emitTimeSec,
                emit_time_nsec: reports[i].emitTimeNsec,
                resolve_time_sec: reports[i].resolveTimeSec,
                resolve_time_nsec: reports[i].resolveTimeNsec,
                accepted_idx: reports[i].acceptedIdx,
                /* eslint-ensable @typescript-eslint/naming-convention */
            });
        }
    }

    public callbacks: ((...args: any[]) => Promise<void>)[] = [];

    public setInterval(callback: (...args: any[]) => Promise<void>, _ms?: number) {
        this.callbacks.push(callback);
    }

    public async runCallbacks() {
        for (const callback of this.callbacks) {
            await callback();
        }
    }
}

class BatchVerifier {
    public verified = 0;

    constructor(
        public reports: CompletionReport[],
        public readonly batchSize: number
    ) {}

    public verify(resolutions: CompletionResolution[]) {
        expect(resolutions.length).toBeLessThanOrEqual(this.batchSize);
        expect(this.verified % this.batchSize).toEqual(0);
        for (let i = 0; i < resolutions.length; i++) {
            expect(resolutions[i]).toEqual({
                /* eslint-disable @typescript-eslint/naming-convention */
                request_id: this.reports[this.verified].requestId,
                emit_time_sec: this.reports[this.verified].emitTimeSec,
                emit_time_nsec: this.reports[this.verified].emitTimeNsec,
                resolve_time_sec: this.reports[this.verified].resolveTimeSec,
                resolve_time_nsec: this.reports[this.verified].resolveTimeNsec,
                accepted_idx: this.reports[this.verified].acceptedIdx,
                /* eslint-ensable @typescript-eslint/naming-convention */
            });
            this.verified++;
        }
    }
}

describe("metrics-reporter", () => {
    let kit: MetricsReporterTestKit;

    beforeEach(() => {
        kit = new MetricsReporterTestKit();

        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
        jest.resetAllMocks();
    });

    test.each([
        1, // singleton
        13, // multiple upload batches
        27, // some records will be dropped
    ])("batch upload reportCount=%d", async (reportCount: number) => {
        // Create a metrics reporter and enable uploads
        const maxRecords = 20;
        const uploadMsec = 100;
        const batchSize = 4;
        const reporter = kit.makeMetricsReporter(maxRecords, uploadMsec, batchSize);
        reporter.enableUpload();

        // Create a set of completion reports
        const reports = kit.makeCompletionReports(reportCount);
        kit.reportCompletions(reporter, reports);

        // Mock the api server's resolveCompletions method to verify that the correct reports
        // are uploaded and that the batches are not larger than the given batch size
        const verifier = new BatchVerifier(reports.slice(-maxRecords), batchSize);
        let batchCount = 0;
        kit.apiServer.resolveCompletions = jest.fn(async (resolutions: CompletionResolution[]) => {
            verifier.verify(resolutions);
            batchCount++;
        });

        // Trigger an upload by the metrics reporter
        jest.advanceTimersByTime(uploadMsec);

        // Wait for the uploads to complete
        const toReport = Math.min(reportCount, maxRecords);
        const expectedBatchCount = Math.ceil(toReport / batchSize);
        while (batchCount < expectedBatchCount) {
            await new Promise((resolve) => resolve(undefined));
        }

        expect(batchCount).toEqual(expectedBatchCount);
        expect(verifier.verified).toEqual(toReport);

        reporter.dispose();
    });

    test("calling enableUpload twice should be ok", () => {
        const reporter = kit.makeMetricsReporter();

        expect(jest.getTimerCount()).toBe(0);

        reporter.enableUpload();
        expect(jest.getTimerCount()).toBe(1);

        reporter.enableUpload();
        expect(jest.getTimerCount()).toBe(1);
    });
});
