import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";

import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { CompletionTimeline } from "../../completions/completion-timeline";
import { ClientCompletionTimelineReporter } from "../../metrics/completion-timeline-reporter";

describe("TimelineEventReporter", () => {
    let apiServer: MockAPIServer;

    beforeEach(() => {
        apiServer = new MockAPIServer();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
        jest.resetAllMocks();
    });

    test("report metric", async () => {
        const apiSpy = jest.spyOn(apiServer, "reportClientCompletionTimelines");

        const reporter = new ClientCompletionTimelineReporter(apiServer);

        const now = Date.now();
        const times: { [key: number]: [number, number] } = {};
        for (let i = 0; i < 10; i++) {
            const msec = now + i;
            times[msec] = msecToTimestamp(msec);
        }

        reporter.reportCompletionTimeline(
            "123",
            new CompletionTimeline(now, now + 1, now + 2, now + 3)
        );
        reporter.reportCompletionTimeline(
            "456",
            new CompletionTimeline(now + 4, now + 5, now + 6, now + 7)
        );

        reporter.enableUpload();
        jest.advanceTimersByTime(ClientCompletionTimelineReporter.defaultUploadMsec);

        expect(apiSpy).toHaveBeenCalledTimes(1);
        expect(apiSpy).toHaveBeenCalledWith([
            /* eslint-disable @typescript-eslint/naming-convention */
            {
                request_id: "123",
                initial_request_time_sec: times[now][0],
                initial_request_time_nsec: times[now][1],
                api_start_time_sec: times[now + 1][0],
                api_start_time_nsec: times[now + 1][1],
                api_end_time_sec: times[now + 2][0],
                api_end_time_nsec: times[now + 2][1],
                emit_time_sec: times[now + 3][0],
                emit_time_nsec: times[now + 3][1],
            },
            {
                request_id: "456",
                initial_request_time_sec: times[now + 4][0],
                initial_request_time_nsec: times[now + 4][1],
                api_start_time_sec: times[now + 5][0],
                api_start_time_nsec: times[now + 5][1],
                api_end_time_sec: times[now + 6][0],
                api_end_time_nsec: times[now + 6][1],
                emit_time_sec: times[now + 7][0],
                emit_time_nsec: times[now + 7][1],
            },
            /* eslint-enable @typescript-eslint/naming-convention */
        ]);
    });
});
