import { Memento } from "../__mocks__/vscode-mocks";
import { SyncingPermissionTrackerImpl } from "../workspace/syncing-permission-tracker";

describe("Syncing permission tracker", () => {
    const workspaceStorage = new Memento();

    afterEach(() => {
        Memento.resetValues();
    });

    const makeTracker = (workspaceStorage: Memento) => {
        return new SyncingPermissionTrackerImpl(workspaceStorage);
    };

    test("initial state", () => {
        const tracker = makeTracker(workspaceStorage);
        expect(tracker.syncingPermissionDenied).toBe(false);
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("unknown");
    });

    test("deny permission", () => {
        const tracker = makeTracker(workspaceStorage);
        tracker.denyPermission();
        expect(tracker.syncingPermissionDenied).toBe(true);
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("denied");
    });

    test("set default permissions", () => {
        const tracker = makeTracker(workspaceStorage);
        tracker.setDefaultPermissions(["/folder1", "/folder2"]);
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("granted");
        expect(tracker.getFolderSyncingPermission("/folder2")).toBe("granted");
        expect(tracker.getFolderSyncingPermission("/folder3")).toBe("unknown");

        // The tracker should only accept these default permission if no permissions exist.
        tracker.setDefaultPermissions(["/folder3"]);
        expect(tracker.getFolderSyncingPermission("/folder3")).toBe("unknown");
    });

    test("permission ops", () => {
        const tracker = makeTracker(workspaceStorage);
        tracker.setPermittedFolders(["/folder1", "/folder2", "/folder3"]);

        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("granted");
        expect(tracker.getFolderSyncingPermission("/folder2")).toBe("granted");
        expect(tracker.getFolderSyncingPermission("/folder3")).toBe("granted");
        expect(tracker.getFolderSyncingPermission("/folder4")).toBe("unknown");

        tracker.addPermittedFolder("/folder4");
        expect(tracker.getFolderSyncingPermission("/folder4")).toBe("granted");

        tracker.dropPermission(["/folder1", "/folder2"]);
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("unknown");
        expect(tracker.getFolderSyncingPermission("/folder2")).toBe("unknown");
        expect(tracker.getFolderSyncingPermission("/folder3")).toBe("granted");
        expect(tracker.getFolderSyncingPermission("/folder4")).toBe("granted");

        tracker.dropStaleFolders(["/folder1", "/folder2", "/folder3"]);
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("unknown");
        expect(tracker.getFolderSyncingPermission("/folder2")).toBe("unknown");
        expect(tracker.getFolderSyncingPermission("/folder3")).toBe("granted");
        expect(tracker.getFolderSyncingPermission("/folder4")).toBe("unknown");
    });

    test("add permitted folder", () => {
        const tracker = makeTracker(workspaceStorage);
        tracker.addPermittedFolder("/folder1");
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("granted");
    });

    test("add new implicitly permitted folder", () => {
        const tracker = makeTracker(workspaceStorage);
        tracker.addImplicitlyPermittedFolder("/folder1");
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("granted");
    });

    test("add implicitly permitted folder after denial", () => {
        const tracker = makeTracker(workspaceStorage);
        tracker.denyPermission();
        tracker.addImplicitlyPermittedFolder("/folder1");
        expect(tracker.getFolderSyncingPermission("/folder1")).toBe("denied");
    });

    test("persist permission", async () => {
        const tracker1 = makeTracker(workspaceStorage);
        tracker1.setPermittedFolders(["/folder1"]);
        await tracker1.persistCurrentPermission();

        const tracker2 = makeTracker(workspaceStorage);
        expect(tracker2.getFolderSyncingPermission("/folder1")).toBe("granted");
        expect(tracker2.getFolderSyncingPermission("/folder2")).toBe("unknown");
        tracker2.denyPermission();
        await tracker2.persistCurrentPermission();

        const tracker3 = makeTracker(workspaceStorage);
        expect(tracker3.syncingPermissionDenied).toBe(true);
        expect(tracker3.getFolderSyncingPermission("/folder1")).toBe("denied");
    });
});
