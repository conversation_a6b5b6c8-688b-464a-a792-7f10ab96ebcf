import { APIServer } from "../../augment-api";
import { DiffViewSessionReporter, SessionOrigin } from "../../diff-view/session-reporter";
import {
    DiffViewResolveMessage,
    DiffViewResolveType,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";

describe("DiffViewSessionReporter", () => {
    let apiServer: APIServer;
    let reporter: DiffViewSessionReporter;

    beforeEach(() => {
        apiServer = {
            logInstructionResolution: jest.fn(),
            logSmartPasteResolution: jest.fn(),
        } as unknown as APIServer;
        reporter = new DiffViewSessionReporter(apiServer);
    });

    afterEach(() => {
        reporter.dispose();
    });

    describe("reset", () => {
        it("should reset all properties to initial state", () => {
            // Setup initial state
            reporter.chunkResolveByStartLine.set(1, true);
            reporter.isAcceptAll = true;
            reporter.isRejectAll = false;
            reporter.requestId = "test-id";
            reporter.sessionOrigin = SessionOrigin.instruction;
            reporter.initialRequestTime = 1000;
            reporter.streamFinishTime = 2000;
            reporter.applyTime = 3000;

            // Reset
            reporter.reset();

            // Verify
            expect(reporter.chunkResolveByStartLine.size).toBe(0);
            expect(reporter.isAcceptAll).toBe(false);
            expect(reporter.isRejectAll).toBe(false);
            expect(reporter.requestId).toBeUndefined();
            expect(reporter.sessionOrigin).toBeUndefined();
            expect(reporter.initialRequestTime).toBeUndefined();
            expect(reporter.streamFinishTime).toBeUndefined();
            expect(reporter.applyTime).toBeUndefined();
        });
    });

    describe("reportResolution", () => {
        const mockFile = {
            repoRoot: "/test",
            pathName: "test.ts",
        };

        it("should handle accept all resolution", () => {
            const msg: DiffViewResolveMessage = {
                type: WebViewMessageType.diffViewResolveChunk,
                data: {
                    resolveType: DiffViewResolveType.accept,
                    shouldApplyToAll: true,
                    changes: [],
                    file: mockFile,
                },
            };

            reporter.reportResolution(msg);

            expect(reporter.isAcceptAll).toBe(true);
            expect(reporter.isRejectAll).toBe(false);
        });

        it("should handle reject all resolution", () => {
            const msg: DiffViewResolveMessage = {
                type: WebViewMessageType.diffViewResolveChunk,
                data: {
                    resolveType: DiffViewResolveType.reject,
                    shouldApplyToAll: true,
                    changes: [],
                    file: mockFile,
                },
            };

            reporter.reportResolution(msg);

            expect(reporter.isAcceptAll).toBe(false);
            expect(reporter.isRejectAll).toBe(true);
        });

        it("should track individual chunk resolutions", () => {
            const msg: DiffViewResolveMessage = {
                type: WebViewMessageType.diffViewResolveChunk,
                data: {
                    resolveType: DiffViewResolveType.accept,
                    shouldApplyToAll: false,
                    file: mockFile,
                    changes: [
                        {
                            repoRoot: "/test",
                            pathName: "test.ts",
                            lineChanges: {
                                lineChanges: [
                                    {
                                        originalStart: 1,
                                        originalEnd: 2,
                                        modifiedStart: 1,
                                        modifiedEnd: 2,
                                    },
                                    {
                                        originalStart: 5,
                                        originalEnd: 6,
                                        modifiedStart: 5,
                                        modifiedEnd: 6,
                                    },
                                ],
                                lineOffset: 0,
                            },
                        },
                    ],
                },
            };

            reporter.reportResolution(msg);

            expect(reporter.chunkResolveByStartLine.get(1)).toBe(true);
            expect(reporter.chunkResolveByStartLine.get(5)).toBe(true);
        });

        it("should handle changes without lineChanges", () => {
            const msg: DiffViewResolveMessage = {
                type: WebViewMessageType.diffViewResolveChunk,
                data: {
                    resolveType: DiffViewResolveType.accept,
                    shouldApplyToAll: false,
                    file: mockFile,
                    changes: [
                        {
                            repoRoot: "/test",
                            pathName: "test.ts",
                            // No lineChanges property
                        },
                    ],
                },
            };

            reporter.reportResolution(msg);

            expect(reporter.chunkResolveByStartLine.size).toBe(0);
        });

        it("should not set accept/reject all if there are existing resolutions", () => {
            // First add an existing resolution
            reporter.chunkResolveByStartLine.set(1, true);

            const msg: DiffViewResolveMessage = {
                type: WebViewMessageType.diffViewResolveChunk,
                data: {
                    resolveType: DiffViewResolveType.accept,
                    shouldApplyToAll: true,
                    changes: [],
                    file: mockFile,
                },
            };

            reporter.reportResolution(msg);

            expect(reporter.isAcceptAll).toBe(false);
            expect(reporter.isRejectAll).toBe(false);
        });

        it("should not set accept/reject all when shouldApplyToAll is false", () => {
            const msg: DiffViewResolveMessage = {
                type: WebViewMessageType.diffViewResolveChunk,
                data: {
                    resolveType: DiffViewResolveType.accept,
                    shouldApplyToAll: false,
                    changes: [],
                    file: mockFile,
                },
            };

            reporter.reportResolution(msg);

            expect(reporter.isAcceptAll).toBe(false);
            expect(reporter.isRejectAll).toBe(false);
        });
    });

    describe("logging", () => {
        beforeEach(() => {
            jest.useFakeTimers();
            jest.setSystemTime(new Date(2023, 0, 1, 0, 0, 0, 0)); // 2023-01-01 00:00:00
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        it("should log instruction resolution", () => {
            reporter.requestId = "test-instruction";
            reporter.sessionOrigin = SessionOrigin.instruction;
            reporter.chunkResolveByStartLine.set(1, true);
            reporter.chunkResolveByStartLine.set(5, false);

            reporter.dispose();

            expect(apiServer.logInstructionResolution).toHaveBeenCalledWith(
                expect.objectContaining({
                    /* eslint-disable @typescript-eslint/naming-convention */
                    request_id: "test-instruction",
                    is_accepted_chunks: [true, false],
                    is_accept_all: false,
                    is_reject_all: false,
                    /* eslint-enable @typescript-eslint/naming-convention */
                })
            );
        });

        it("should log smart paste resolution with timing information", () => {
            reporter.requestId = "test-smart-paste";
            reporter.sessionOrigin = SessionOrigin.smartPaste;
            reporter.initialRequestTime = 1000;
            reporter.streamFinishTime = 2000;
            reporter.applyTime = 3000;
            reporter.chunkResolveByStartLine.set(1, true);

            reporter.dispose();

            expect(apiServer.logSmartPasteResolution).toHaveBeenCalledWith(
                expect.objectContaining({
                    /* eslint-disable @typescript-eslint/naming-convention */
                    request_id: "test-smart-paste",
                    is_accepted_chunks: [true],
                    initial_request_time_sec: 1,
                    initial_request_time_nsec: 0,
                    stream_finish_time_sec: 2,
                    stream_finish_time_nsec: 0,
                    apply_time_sec: 3,
                    apply_time_nsec: 0,
                    /* eslint-enable @typescript-eslint/naming-convention */
                })
            );
        });

        it("should not log if requestId is missing", () => {
            reporter.sessionOrigin = SessionOrigin.instruction;
            reporter.chunkResolveByStartLine.set(1, true);

            reporter.dispose();

            expect(apiServer.logInstructionResolution).not.toHaveBeenCalled();
            expect(apiServer.logSmartPasteResolution).not.toHaveBeenCalled();
        });

        it("should not log if sessionOrigin is invalid", () => {
            reporter.requestId = "test-id";
            reporter.sessionOrigin = "invalid" as any;
            reporter.chunkResolveByStartLine.set(1, true);

            reporter.dispose();

            expect(apiServer.logInstructionResolution).not.toHaveBeenCalled();
            expect(apiServer.logSmartPasteResolution).not.toHaveBeenCalled();
        });

        it("should handle undefined values in chunk resolution map", () => {
            reporter.requestId = "test-instruction";
            reporter.sessionOrigin = SessionOrigin.instruction;
            reporter.chunkResolveByStartLine.set(1, true);
            // Explicitly set undefined as a value
            reporter.chunkResolveByStartLine.set(2, undefined as any);

            reporter.dispose();

            expect(apiServer.logInstructionResolution).toHaveBeenCalledWith(
                expect.objectContaining({
                    /* eslint-disable @typescript-eslint/naming-convention */
                    request_id: "test-instruction",
                    is_accepted_chunks: [true], // undefined value should be filtered out
                    is_accept_all: false,
                    is_reject_all: false,
                    /* eslint-enable @typescript-eslint/naming-convention */
                })
            );
        });

        it("should include false values in accepted chunks array", () => {
            reporter.requestId = "test-instruction";
            reporter.sessionOrigin = SessionOrigin.instruction;
            reporter.chunkResolveByStartLine.set(1, true);
            reporter.chunkResolveByStartLine.set(2, false);

            reporter.dispose();

            expect(apiServer.logInstructionResolution).toHaveBeenCalledWith(
                expect.objectContaining({
                    /* eslint-disable @typescript-eslint/naming-convention */
                    request_id: "test-instruction",
                    is_accepted_chunks: [true, false], // should include both true and false values
                    is_accept_all: false,
                    is_reject_all: false,
                    /* eslint-enable @typescript-eslint/naming-convention */
                })
            );
        });
    });
});
