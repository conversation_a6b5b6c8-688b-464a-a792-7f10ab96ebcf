import * as vscode from "vscode";

import { DiffStreamContext } from "../../diff-view/stream-context";
import { getLogger } from "../../logging";
import {
    DiffViewDiffStreamChunkData,
    DiffViewStreamMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";

const logger = getLogger("test");

describe("DiffStreamContext", () => {
    let streamContext: DiffStreamContext;
    const mockStreamId = "test-stream";
    const mockRequestId = "test-request";

    async function* mockStreamGenerator(): AsyncGenerator<DiffViewDiffStreamChunkData> {
        yield {
            chunkContinue: { newText: "chunk1" },
        };
        yield {
            chunkContinue: { newText: "chunk2" },
        };
    }

    beforeEach(() => {
        streamContext = new DiffStreamContext(
            mockStreamId,
            mockRequestId,
            mockStreamGenerator(),
            logger
        );
    });

    afterEach(() => {
        streamContext.dispose();
    });

    describe("basic functionality", () => {
        test("constructor initializes with correct properties", () => {
            expect(streamContext.streamId).toBe(mockStreamId);
            expect(streamContext.requestId).toBe(mockRequestId);
        });

        test("stream yields correct sequence of messages", async () => {
            const messages: DiffViewStreamMessage[] = [];
            for await (const msg of streamContext.stream) {
                messages.push(msg);
            }

            // Should have start message, 2 chunks, and end message
            expect(messages).toHaveLength(4);

            // Check start message
            expect(messages[0].type).toBe(WebViewMessageType.diffViewDiffStreamStarted);
            expect(messages[0].data.requestId).toBe(mockRequestId);
            expect(messages[0].data.streamId).toBeDefined();

            // Check chunk messages
            expect(messages[1].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);
            expect((messages[1].data as DiffViewDiffStreamChunkData).chunkContinue?.newText).toBe(
                "chunk1"
            );
            expect(messages[1].data.requestId).toBe(mockRequestId);
            expect(messages[1].data.streamId).toBeDefined();

            expect(messages[2].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);
            expect((messages[2].data as DiffViewDiffStreamChunkData).chunkContinue?.newText).toBe(
                "chunk2"
            );
            expect(messages[2].data.requestId).toBe(mockRequestId);
            expect(messages[2].data.streamId).toBeDefined();

            // Check end message
            expect(messages[3].type).toBe(WebViewMessageType.diffViewDiffStreamEnded);
            expect(messages[3].data.requestId).toBe(mockRequestId);
            expect(messages[3].data.streamId).toBeDefined();
        });
    });

    describe("cancellation", () => {
        test("cancel stops the stream", async () => {
            const messages: DiffViewStreamMessage[] = [];
            const streamPromise = (async () => {
                for await (const msg of streamContext.stream) {
                    messages.push(msg);
                    if (msg.type === WebViewMessageType.diffViewDiffStreamChunk) {
                        streamContext.cancel();
                        break;
                    }
                }
            })();

            await streamPromise;

            // Should have start message and one chunk before cancellation
            expect(messages).toHaveLength(2);
            expect(messages[0].type).toBe(WebViewMessageType.diffViewDiffStreamStarted);
            expect(messages[1].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);
        });
    });

    describe("error handling", () => {
        test("handles stream errors gracefully", async () => {
            const showWarningMessage = jest
                .spyOn(vscode.window, "showWarningMessage")
                .mockResolvedValue(undefined);

            async function* errorGenerator(): AsyncGenerator<DiffViewDiffStreamChunkData> {
                yield {
                    chunkContinue: { newText: "chunk1" },
                };
                throw new Error("Test error");
            }

            const errorStreamContext = new DiffStreamContext(
                mockStreamId,
                mockRequestId,
                errorGenerator(),
                logger
            );

            const messages: DiffViewStreamMessage[] = [];
            for await (const msg of errorStreamContext.stream) {
                messages.push(msg);
            }

            // Should have start message, one chunk, and end message
            expect(messages).toHaveLength(3);
            expect(messages[0].type).toBe(WebViewMessageType.diffViewDiffStreamStarted);
            expect(messages[1].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);
            expect(messages[2].type).toBe(WebViewMessageType.diffViewDiffStreamEnded);

            // Verify error handling
            expect(showWarningMessage).toHaveBeenCalledWith(
                "Error streaming in changes to Augment Diff. Please try again."
            );

            errorStreamContext.dispose();
        });

        test("logs error details when stream fails", async () => {
            const errorMessage = "Custom test error";
            const loggerSpy = jest.spyOn(logger, "error");

            /* eslint-disable */
            async function* errorGenerator(): AsyncGenerator<DiffViewDiffStreamChunkData> {
                throw new Error(errorMessage);
            }
            /* eslint-enable */

            const errorStreamContext = new DiffStreamContext(
                mockStreamId,
                mockRequestId,
                errorGenerator(),
                logger
            );

            const messages: DiffViewStreamMessage[] = [];
            for await (const msg of errorStreamContext.stream) {
                messages.push(msg);
            }

            expect(loggerSpy).toHaveBeenCalledWith(
                expect.stringContaining(
                    `Error streaming in changes to Augment Diff: ${errorMessage}`
                )
            );
            errorStreamContext.dispose();
        });
    });

    describe("cancellation token", () => {
        test("cancellation token stops stream processing", async () => {
            async function* longStreamGenerator(): AsyncGenerator<DiffViewDiffStreamChunkData> {
                for (let i = 0; i < 10; i++) {
                    yield {
                        chunkContinue: { newText: `chunk${i}` },
                    };
                    // Add small delay to ensure cancellation has time to process
                    await new Promise((resolve) => setTimeout(resolve, 1));
                }
            }

            const longStreamContext = new DiffStreamContext(
                mockStreamId,
                mockRequestId,
                longStreamGenerator(),
                logger
            );

            const messages: DiffViewStreamMessage[] = [];
            const processStream = async () => {
                for await (const msg of longStreamContext.stream) {
                    messages.push(msg);
                    if (messages.length === 2) {
                        // After start message and first chunk
                        longStreamContext.cancel();
                    }
                }
            };

            await processStream();

            // Should have start message, one chunk, and end message
            expect(messages).toHaveLength(3);
            expect(messages[0].type).toBe(WebViewMessageType.diffViewDiffStreamStarted);
            expect(messages[1].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);
            expect(messages[2].type).toBe(WebViewMessageType.diffViewDiffStreamEnded);

            longStreamContext.dispose();
        });
    });

    describe("multiple consumers", () => {
        test("starting a new stream cancels the previous one", async () => {
            const stream1Messages: DiffViewStreamMessage[] = [];
            const stream2Messages: DiffViewStreamMessage[] = [];

            // Start first stream and wait for first message
            const stream1Promise = (async () => {
                try {
                    for await (const msg of streamContext.stream) {
                        stream1Messages.push(msg);
                        // Break after receiving the first chunk to simulate slower consumption
                        if (msg.type === WebViewMessageType.diffViewDiffStreamChunk) {
                            break;
                        }
                    }
                } catch (e) {
                    // Stream may be cancelled
                }
            })();

            // Give stream1 a chance to start and receive first message
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Start second stream
            const stream2Promise = (async () => {
                for await (const msg of streamContext.stream) {
                    stream2Messages.push(msg);
                }
            })();

            await Promise.all([stream1Promise, stream2Promise]);

            // First stream should have been cancelled after start message and first chunk
            expect(stream1Messages).toHaveLength(2); // start + first chunk only
            expect(stream1Messages[0].type).toBe(WebViewMessageType.diffViewDiffStreamStarted);
            expect(stream1Messages[1].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);

            // Second stream should complete normally
            expect(stream2Messages).toHaveLength(4); // start + 2 chunks + end
            expect(stream2Messages[0].type).toBe(WebViewMessageType.diffViewDiffStreamStarted);
            expect(stream2Messages[1].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);
            expect(stream2Messages[2].type).toBe(WebViewMessageType.diffViewDiffStreamChunk);
            expect(stream2Messages[3].type).toBe(WebViewMessageType.diffViewDiffStreamEnded);
        });
    });

    describe("stream ID handling", () => {
        test("generates unique stream IDs for each stream instance", async () => {
            const streamIds = new Set<string>();

            // Create multiple streams and collect their IDs
            for (let i = 0; i < 3; i++) {
                const messages: DiffViewStreamMessage[] = [];
                for await (const msg of streamContext.stream) {
                    if (msg.type === WebViewMessageType.diffViewDiffStreamStarted) {
                        streamIds.add(msg.data.streamId);
                    }
                    messages.push(msg);
                }
            }

            // Verify each stream got a unique ID
            expect(streamIds.size).toBe(3);

            // Verify each ID contains the base streamId
            for (const id of streamIds) {
                expect(id).toContain(mockStreamId);
            }
        });

        test("stream IDs include nonce for uniqueness", async () => {
            const messages: DiffViewStreamMessage[] = [];
            for await (const msg of streamContext.stream) {
                messages.push(msg);
            }

            const streamId = messages[0].data.streamId;
            expect(streamId).toMatch(new RegExp(`${mockStreamId}-[a-zA-Z0-9]+`));
        });
    });
});
