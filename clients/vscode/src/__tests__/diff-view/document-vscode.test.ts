import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import {
    addTextDocument,
    MutableTextDocument,
    openTextDocument,
    Range,
    resetMockWorkspace,
    TextDocumentChangeEvent,
    TextEdit,
    Uri,
    workspace,
} from "../../__mocks__/vscode-mocks";
import { VSCodeDiffViewDocument } from "../../diff-view/document-vscode";
// Import the mocked function
import { getUntitledDoc } from "../../workspace/untitled";

// Add a helper function to set text content of a MutableTextDocument
function setDocumentText(doc: MutableTextDocument, text: string): TextDocumentChangeEvent {
    // Create a text edit that replaces the entire document
    const edit = new TextEdit(new Range(0, 0, doc.lineCount, 0), text);
    // Apply the edit to the document and return the change event
    return doc.applyEdit(edit);
}

// Mock the getUntitledDoc function
jest.mock("../../workspace/untitled", () => ({
    getUntitledDoc: jest.fn(),
    isUntitledFile: jest.fn().mockImplementation(() => false),
}));

describe("VSCodeDiffViewDocument", () => {
    const testPath = QualifiedPathName.from({
        rootPath: "/test",
        relPath: "file.ts",
    });
    const originalContent = "original content\n";
    const modifiedContent = "modified content\n";

    let document: VSCodeDiffViewDocument;
    let mockTextDocument: MutableTextDocument;
    let mockLogger: any;

    beforeEach(() => {
        jest.clearAllMocks();
        resetMockWorkspace();
        mockFSUtils.reset();

        // Setup mock logger
        mockLogger = {
            debug: jest.fn(),
            info: jest.fn(),
            error: jest.fn(),
        };

        // Setup mock file system
        mockFSUtils.writeFileUtf8(testPath.absPath, originalContent, true);

        // Create a mock text document
        mockTextDocument = openTextDocument(Uri.file(testPath.absPath));

        // Add the document to the workspace's list of text documents
        addTextDocument(mockTextDocument);

        // Mock workspace.openTextDocument to return our mock document
        workspace.openTextDocument = jest.fn().mockResolvedValue(mockTextDocument);

        // Create the document under test
        document = new VSCodeDiffViewDocument(testPath, originalContent, modifiedContent, {
            logger: mockLogger,
        });
    });

    afterEach(() => {
        document.dispose();
    });

    describe("constructor and basic properties", () => {
        it("should create document with correct initial state", () => {
            expect(document.originalCode).toBe(originalContent);
            expect(document.modifiedCode).toBe(modifiedContent);
            expect(document.absPath).toBe(testPath.absPath);
            expect(document.isEmptyDocument).toBe(false);
            expect(document.isUntitled).toBe(false);
            expect(document.filePath).toBe(testPath);
        });
    });

    describe("fromPathName", () => {
        it("should create document from path name with original content", async () => {
            const doc = await VSCodeDiffViewDocument.fromPathName(testPath, undefined, mockLogger);

            expect(doc.originalCode).toBe(originalContent);
            expect(doc.modifiedCode).toBe(originalContent); // Should be same as original when not specified
            expect(doc.absPath).toBe(testPath.absPath);

            doc.dispose();
        });

        it("should create document from path name with specified new content", async () => {
            const newContent = "new content";
            const doc = await VSCodeDiffViewDocument.fromPathName(testPath, newContent, mockLogger);

            expect(doc.originalCode).toBe(originalContent);
            expect(doc.modifiedCode).toBe(newContent);
            expect(doc.absPath).toBe(testPath.absPath);

            doc.dispose();
        });
    });

    describe("_write", () => {
        it("should write original code to document when different", async () => {
            // Set up the mock document with different content
            setDocumentText(mockTextDocument, "different content");

            // Mock the workspace.applyEdit function
            const applyEditSpy = jest.spyOn(workspace, "applyEdit");

            // Call the _write method
            await document["_write"]();

            // Verify applyEdit was called
            expect(applyEditSpy).toHaveBeenCalled();

            // Verify logger.debug was called
            expect(mockLogger.debug).toHaveBeenCalled();
        });

        it("should not write original code to document when same", async () => {
            // Set up the mock document with the same content as original
            setDocumentText(mockTextDocument, originalContent);

            // Mock the workspace.applyEdit function
            const applyEditSpy = jest.spyOn(workspace, "applyEdit");

            // Call the _write method
            await document["_write"]();

            // Verify applyEdit was not called
            expect(applyEditSpy).not.toHaveBeenCalled();
        });
    });

    describe("_onBaseDocUpdated", () => {
        it("should update original code when base document changes", () => {
            // Create a spy on updateOriginal
            const updateOriginalSpy = jest.spyOn(document, "updateOriginal");

            // Simulate a document change
            const newContent = "updated content";
            setDocumentText(mockTextDocument, newContent);

            // Directly call the _onBaseDocUpdated method
            document["_onBaseDocUpdated"]({
                document: mockTextDocument,
                contentChanges: [],
                reason: undefined,
            });

            // Verify updateOriginal was called with the new content
            expect(updateOriginalSpy).toHaveBeenCalledWith(newContent);
        });

        it("should not update original code when content hasn't changed", () => {
            // Create a spy on updateOriginal
            const updateOriginalSpy = jest.spyOn(document, "updateOriginal");

            // Simulate a document change with the same content
            setDocumentText(mockTextDocument, originalContent);

            // Directly call the _onBaseDocUpdated method
            document["_onBaseDocUpdated"]({
                document: mockTextDocument,
                contentChanges: [],
                reason: undefined,
            });

            // Verify updateOriginal was not called
            expect(updateOriginalSpy).not.toHaveBeenCalled();
        });

        it("should not update original code for unrelated documents", () => {
            // Create a spy on updateOriginal
            const updateOriginalSpy = jest.spyOn(document, "updateOriginal");

            // Create an unrelated document
            const unrelatedPath = "/unrelated/path.ts";
            const unrelatedDoc = new MutableTextDocument(
                Uri.file(unrelatedPath),
                "unrelated content"
            );

            // Add the document to the workspace
            addTextDocument(unrelatedDoc);

            // Directly call the _onBaseDocUpdated method with unrelated document
            document["_onBaseDocUpdated"]({
                document: unrelatedDoc,
                contentChanges: [],
                reason: undefined,
            });

            // Verify updateOriginal was not called
            expect(updateOriginalSpy).not.toHaveBeenCalled();
        });
    });

    describe("_read", () => {
        it("should return empty string when document is undefined", async () => {
            // Mock getUntitledDoc to return undefined
            (getUntitledDoc as jest.Mock).mockReturnValueOnce(undefined);

            // Mock openTextDocument to throw an error
            workspace.openTextDocument = jest
                .fn()
                .mockRejectedValueOnce(new Error("Failed to open"));

            // Create a document with a path that will cause an error
            const errorPath = QualifiedPathName.from({
                rootPath: "/error",
                relPath: "nonexistent.ts",
            });

            const errorDoc = new VSCodeDiffViewDocument(
                errorPath,
                "error content",
                "error content",
                { logger: mockLogger }
            );

            // Call _read which should return empty string when document is undefined
            const result = await errorDoc["_read"]();

            // Verify the result is an empty string
            expect(result).toBe("");

            errorDoc.dispose();
        });
    });

    describe("_getVsCodeTextDocument", () => {
        it("should handle error when document is undefined", async () => {
            // Mock getUntitledDoc to return undefined
            (getUntitledDoc as jest.Mock).mockReturnValueOnce(undefined);

            // Mock openTextDocument to return undefined
            workspace.openTextDocument = jest.fn().mockResolvedValueOnce(undefined);

            // Create a document with a path that will cause an error
            const errorPath = QualifiedPathName.from({
                rootPath: "/error",
                relPath: "nonexistent.ts",
            });

            const errorDoc = new VSCodeDiffViewDocument(
                errorPath,
                "error content",
                "error content",
                { logger: mockLogger }
            );

            // Call _getVsCodeTextDocument which should throw and catch an error
            const result = await errorDoc["_getVsCodeTextDocument"]();

            // Verify the result is undefined
            expect(result).toBeUndefined();

            // Verify error was logged
            expect(mockLogger.error).toHaveBeenCalled();

            errorDoc.dispose();
        });

        it("should handle exception when opening document", async () => {
            // Mock openTextDocument to throw an error
            const errorMessage = "Failed to open document";
            workspace.openTextDocument = jest.fn().mockRejectedValueOnce(new Error(errorMessage));

            // Create a document with a path that will cause an error
            const errorPath = QualifiedPathName.from({
                rootPath: "/error",
                relPath: "exception.ts",
            });

            const errorDoc = new VSCodeDiffViewDocument(
                errorPath,
                "error content",
                "error content",
                { logger: mockLogger }
            );

            // Call _getVsCodeTextDocument which should catch the exception
            const result = await errorDoc["_getVsCodeTextDocument"]();

            // Verify the result is undefined
            expect(result).toBeUndefined();

            // Verify error was logged
            expect(mockLogger.error).toHaveBeenCalled();

            errorDoc.dispose();
        });
    });

    describe("untitled documents", () => {
        it("should handle untitled documents", async () => {
            // Setup mock for untitled document
            const untitledPath = QualifiedPathName.from({
                rootPath: "/test",
                relPath: "untitled.ts",
            });

            const untitledDoc = new MutableTextDocument(
                Uri.file(untitledPath.absPath),
                "untitled content"
            );

            // Add the document to the workspace
            addTextDocument(untitledDoc);

            // Mock the getUntitledDoc function to return our mock document
            (getUntitledDoc as jest.Mock).mockReturnValue(untitledDoc);

            // Create a document with isUntitled=true
            const untitledDiffDoc = new VSCodeDiffViewDocument(
                untitledPath,
                "untitled original",
                "untitled modified",
                {
                    logger: mockLogger,
                    isUntitled: () => true,
                }
            );

            // Verify the document is marked as untitled
            expect(untitledDiffDoc.isUntitled).toBe(true);

            // Test that getUntitledDoc is called when accessing the document
            await untitledDiffDoc["_getVsCodeTextDocument"]();

            // Verify getUntitledDoc was called
            expect(getUntitledDoc).toHaveBeenCalledWith(untitledPath);

            // Create a spy on updateOriginal
            const updateOriginalSpy = jest.spyOn(untitledDiffDoc, "updateOriginal");

            // Update the untitled document content
            setDocumentText(untitledDoc, "updated untitled content");

            // Directly call the _onBaseDocUpdated method
            untitledDiffDoc["_onBaseDocUpdated"]({
                document: untitledDoc,
                contentChanges: [],
                reason: undefined,
            });

            // Verify updateOriginal was called with the new content
            expect(updateOriginalSpy).toHaveBeenCalledWith("updated untitled content");

            untitledDiffDoc.dispose();
        });
    });

    describe("rapid modifications and disposal", () => {
        it("should not allow writes after disposal", async () => {
            // Create a new document for this test to avoid interference
            const testDoc = new VSCodeDiffViewDocument(testPath, originalContent, modifiedContent, {
                logger: mockLogger,
            });

            // Dispose the document
            testDoc.dispose();

            // Create a spy on workspace.applyEdit after disposal
            const applyEditSpy = jest.spyOn(workspace, "applyEdit");

            // Try to modify the document after disposal
            testDoc.updateOriginal("new content after disposal");

            // Allow any pending promises to resolve
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Verify applyEdit was not called after disposal
            expect(applyEditSpy).not.toHaveBeenCalled();
        });

        it("should write the latest original content when modified multiple times", async () => {
            // Create a new document for this test
            const testDoc = new VSCodeDiffViewDocument(testPath, originalContent, modifiedContent, {
                logger: mockLogger,
            });

            // Create a spy on workspace.applyEdit to verify writes
            const applyEditSpy = jest.spyOn(workspace, "applyEdit");

            // Set up the mock document with different content
            setDocumentText(mockTextDocument, "different content");

            // Modify the original code multiple times in quick succession
            testDoc.updateOriginal("content version 1");
            testDoc.updateOriginal("content version 2");
            const finalContent = "content version 3";
            testDoc.updateOriginal(finalContent);

            // Allow any pending promises to resolve
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Verify applyEdit was called with the latest content
            expect(applyEditSpy).toHaveBeenCalled();

            // Get the last call arguments
            const lastCallArgs = applyEditSpy.mock.calls[applyEditSpy.mock.calls.length - 1][0];
            const editEntries = Array.from(lastCallArgs.entries());

            // Verify the edit contains the final content
            expect(editEntries[0][1][0].newText).toBe(finalContent);

            testDoc.dispose();
        });

        it("should only call write once when modifying modified code, original code, and disposing quickly", async () => {
            // Create a new document for this test
            const testDoc = new VSCodeDiffViewDocument(testPath, originalContent, modifiedContent, {
                logger: mockLogger,
            });

            // Create a spy on the _write method
            const writeSpy = jest.spyOn<any, any>(testDoc, "_write");

            // Set up the mock document with different content to ensure write would happen
            setDocumentText(mockTextDocument, "different content");

            // Modify the modified code (this shouldn't trigger _write)
            testDoc.updateBuffer("new modified content");

            // Modify the original code (this should queue a _write)
            testDoc.updateOriginal("new original content");

            // Dispose the document immediately
            testDoc.dispose();

            // Allow any pending promises to resolve
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Verify _write was called exactly once
            // This is because the updateOriginal triggers a write, but disposal should prevent additional writes
            expect(writeSpy).toHaveBeenCalledTimes(1);
        });
    });
});
