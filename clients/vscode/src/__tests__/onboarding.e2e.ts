import { browser, expect } from "@wdio/globals";
import { WebView } from "wdio-vscode-service";

describe("Onboarding", () => {
    it("should load Augment, open sidebar and show sign in button", async () => {
        const workbench = await browser.getWorkbench();
        await workbench.wait();

        const title = await workbench.getTitleBar().getTitle();
        await expect(title).toContain("[Extension Development Host]");

        // Wait for sidebar to load
        console.log("Waiting for sidebar");
        await browser.waitUntil(() => {
            return !!workbench.getSideBar();
        });

        console.log("Wait for augment sidebar to be selected");
        await browser.waitUntil(async () => {
            const sidebar = workbench.getSideBar();
            const title = await sidebar.getTitlePart().getTitle();
            return title.toLowerCase() === "augment";
        });

        console.log("Wait for webviews...");
        await browser.waitUntil(async () => {
            return !!(await findWebview(await workbench.getAllWebviews(), "Augment"));
        });
        const webviews = await workbench.getAllWebviews();
        const augmentPanelWebview = await findWebview(webviews, "Augment");
        if (!augmentPanelWebview) {
            throw new Error("Failed to find main-panel webview");
        }

        try {
            // Make the wdio commands run in the conext of the webview
            await augmentPanelWebview.open();

            console.log("Looking for sign in button...");
            // Find the button using the test ID
            const signInButton = $('[data-testid="sign-in-button"]');
            const found = await signInButton.waitForExist({ timeout: 5000 });
            await expect(found).toEqual(true);
        } finally {
            // Stop the wdio commands running in the conext of the webview
            await augmentPanelWebview.close();
        }
    });
});

async function findWebview(webviews: WebView[], title: string): Promise<WebView | undefined> {
    for (const wv of webviews) {
        try {
            await wv.open();

            // Note: browser.getTitle() returns the title of the VSCode window
            // not the title for the current webview
            const wvTitle = await browser.execute(() => document.title);
            if (wvTitle === title) {
                return wv;
            }
        } finally {
            await wv.close();
        }
    }
    return undefined;
}
