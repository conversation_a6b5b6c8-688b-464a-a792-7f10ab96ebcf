import { commands, Extension } from "vscode";

import {
    generateMockWorkspaceConfig,
    generateUncheckedMockWorkspaceConfig,
    getExampleUserConfig,
} from "../__mocks__/mock-augment-config";
import {
    ConfigurationTarget,
    ExtensionContext,
    extensions,
    mockWorkspaceConfigChange,
    resetMockWorkspace,
    Uri,
    workspace,
} from "../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../augment-config-listener";
import { CODEIUM_ID, ConflictingExtensions, GITHUB_COPILOT_ID } from "../conflicting-extensions";
import { ActionsModel } from "../main-panel/action-cards/actions-model";
import { AugmentGlobalState } from "../utils/context";
import { SystemStateName, SystemStatus } from "../utils/types";

describe("conflicting-extensions", () => {
    let configListener: AugmentConfigListener;
    let actionsModel: ActionsModel;
    let conflictingExtensions: ConflictingExtensions;

    beforeEach(() => {
        resetMockWorkspace();
        const globalState = new AugmentGlobalState(new ExtensionContext());
        configListener = new AugmentConfigListener();
        actionsModel = new ActionsModel(globalState);
        conflictingExtensions = new ConflictingExtensions(configListener, actionsModel);
    });

    afterEach(() => {
        configListener.dispose();
        conflictingExtensions.dispose();
        resetMockWorkspace();
    });

    test("do nothing if extensions are not installed", async () => {
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({ conflictingCodingAssistantCheck: true })
        );

        const getExtensionSpy = setupGetExtensionsSpy({
            [GITHUB_COPILOT_ID]: undefined,
            [CODEIUM_ID]: undefined,
        });

        conflictingExtensions.checkAndUpdateState();

        expect(getExtensionSpy).toHaveBeenCalledWith(GITHUB_COPILOT_ID);
        expect(getExtensionSpy).toHaveBeenCalledWith(CODEIUM_ID);

        expect(actionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
            name: SystemStateName.disabledGithubCopilot,
            status: SystemStatus.complete,
        });
        expect(actionsModel.getSystemState(SystemStateName.disabledCodeium)).toEqual({
            name: SystemStateName.disabledCodeium,
            status: SystemStatus.complete,
        });
    });

    test("set conflicting state when copilot is enabled", async () => {
        jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
            switch (key) {
                case "augment":
                    return generateMockWorkspaceConfig(
                        getExampleUserConfig({
                            completions: {
                                enableAutomaticCompletions: true,
                            },
                        })
                    );
                case "github.copilot":
                    return generateUncheckedMockWorkspaceConfig({
                        enable: {
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            "*": true,
                        },
                    });
                default:
                    throw new Error(`Unexpected configuration request: ${key}`);
            }
        });
        workspace.mockConfigurationChange();

        const getExtensionSpy = setupGetExtensionsSpy({
            [GITHUB_COPILOT_ID]: GITHUB_COPILOT_EXTENSION_INFO,
            [CODEIUM_ID]: undefined,
        });

        expect(configListener.config.conflictingCodingAssistantCheck).toBe(true);

        conflictingExtensions.checkAndUpdateState();

        expect(getExtensionSpy).toHaveBeenCalledWith(GITHUB_COPILOT_ID);
        expect(getExtensionSpy).toHaveBeenCalledWith(CODEIUM_ID);

        expect(actionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
            name: SystemStateName.disabledGithubCopilot,
            status: SystemStatus.incomplete,
        });
        expect(actionsModel.getSystemState(SystemStateName.disabledCodeium)).toEqual({
            name: SystemStateName.disabledCodeium,
            status: SystemStatus.complete,
        });
    });

    test("set conflicting state when codeium is enabled", async () => {
        jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
            switch (key) {
                case "augment":
                    return generateMockWorkspaceConfig(
                        getExampleUserConfig({
                            completions: {
                                enableAutomaticCompletions: true,
                            },
                        })
                    );
                case "codeium":
                    return generateUncheckedMockWorkspaceConfig({
                        enableConfig: {
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            "*": true,
                        },
                    });
                default:
                    throw new Error(`Unexpected configuration request: ${key}`);
            }
        });
        workspace.mockConfigurationChange();

        const getExtensionSpy = setupGetExtensionsSpy({
            [GITHUB_COPILOT_ID]: undefined,
            [CODEIUM_ID]: CODEIUM_EXTENSION_INFO,
        });

        expect(configListener.config.conflictingCodingAssistantCheck).toBe(true);

        conflictingExtensions.checkAndUpdateState();

        expect(getExtensionSpy).toHaveBeenCalledWith(GITHUB_COPILOT_ID);
        expect(getExtensionSpy).toHaveBeenCalledWith(CODEIUM_ID);

        expect(actionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
            name: SystemStateName.disabledGithubCopilot,
            status: SystemStatus.complete,
        });
        expect(actionsModel.getSystemState(SystemStateName.disabledCodeium)).toEqual({
            name: SystemStateName.disabledCodeium,
            status: SystemStatus.incomplete,
        });
    });

    test("do nothing when config disables the check", async () => {
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({ conflictingCodingAssistantCheck: false })
        );

        const getExtensionSpy = setupGetExtensionsSpy({
            [GITHUB_COPILOT_ID]: GITHUB_COPILOT_EXTENSION_INFO,
            [CODEIUM_ID]: CODEIUM_EXTENSION_INFO,
        });

        conflictingExtensions.checkAndUpdateState();

        expect(getExtensionSpy).not.toHaveBeenCalled();
        expect(actionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
            name: SystemStateName.disabledGithubCopilot,
            status: SystemStatus.complete,
        });
        expect(actionsModel.getSystemState(SystemStateName.disabledCodeium)).toEqual({
            name: SystemStateName.disabledCodeium,
            status: SystemStatus.complete,
        });
    });

    test("do nothing when github copilot completions setting is disabled", async () => {
        jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
            switch (key) {
                case "augment":
                    return generateMockWorkspaceConfig(
                        getExampleUserConfig({
                            completions: {
                                enableAutomaticCompletions: true,
                            },
                        })
                    );
                case "github.copilot":
                    return generateUncheckedMockWorkspaceConfig({
                        enable: {
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            "*": false,
                        },
                    });
                default:
                    throw new Error(`Unexpected configuration request: ${key}`);
            }
        });
        workspace.mockConfigurationChange();

        const getExtensionSpy = setupGetExtensionsSpy({
            [GITHUB_COPILOT_ID]: GITHUB_COPILOT_EXTENSION_INFO,
            [CODEIUM_ID]: undefined,
        });

        conflictingExtensions.checkAndUpdateState();

        expect(getExtensionSpy).toHaveBeenCalledWith(GITHUB_COPILOT_ID);
        expect(actionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
            name: SystemStateName.disabledGithubCopilot,
            status: SystemStatus.complete,
        });
        expect(actionsModel.getSystemState(SystemStateName.disabledCodeium)).toEqual({
            name: SystemStateName.disabledCodeium,
            status: SystemStatus.complete,
        });
    });

    test("do nothing when codeium completions setting is disabled", async () => {
        jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
            switch (key) {
                case "augment":
                    return generateMockWorkspaceConfig(
                        getExampleUserConfig({
                            completions: {
                                enableAutomaticCompletions: true,
                            },
                        })
                    );
                case "codeium":
                    return generateUncheckedMockWorkspaceConfig({
                        enableConfig: {
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            "*": false,
                        },
                    });
                default:
                    throw new Error(`Unexpected configuration request: ${key}`);
            }
        });
        workspace.mockConfigurationChange();

        const getExtensionSpy = setupGetExtensionsSpy({
            [GITHUB_COPILOT_ID]: undefined,
            [CODEIUM_ID]: CODEIUM_EXTENSION_INFO,
        });

        conflictingExtensions.checkAndUpdateState();

        expect(getExtensionSpy).toHaveBeenCalledWith(GITHUB_COPILOT_ID);
        expect(getExtensionSpy).toHaveBeenCalledWith(CODEIUM_ID);
        expect(actionsModel.getSystemState(SystemStateName.disabledGithubCopilot)).toEqual({
            name: SystemStateName.disabledGithubCopilot,
            status: SystemStatus.complete,
        });
        expect(actionsModel.getSystemState(SystemStateName.disabledCodeium)).toEqual({
            name: SystemStateName.disabledCodeium,
            status: SystemStatus.complete,
        });
    });

    describe("disableGitHubCopilot", () => {
        test("disables copilot by updating workspace config", async () => {
            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enable: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
            });
            mockConfig.inspect = jest.fn().mockImplementation(() => {
                return {
                    globalValue: undefined,
                    workspaceValue: true,
                };
            });
            const updateMock = jest.fn();
            mockConfig.update = updateMock;
            jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
                switch (key) {
                    case "github.copilot": {
                        return mockConfig;
                    }
                    default:
                        return generateMockWorkspaceConfig();
                }
            });

            await ConflictingExtensions.disableGitHubCopilot();

            expect(updateMock).toHaveBeenCalledWith(
                "enable",
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
                ConfigurationTarget.Workspace
            );
        });

        test("disables copilot by updating global config", async () => {
            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enable: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
            });
            mockConfig.inspect = jest.fn().mockImplementation(() => {
                return {
                    globalValue: undefined,
                    workspaceValue: undefined,
                };
            });
            const updateMock = jest.fn();
            mockConfig.update = updateMock;
            jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
                switch (key) {
                    case "github.copilot": {
                        return mockConfig;
                    }
                    default:
                        return generateMockWorkspaceConfig();
                }
            });

            await ConflictingExtensions.disableGitHubCopilot();

            expect(updateMock).toHaveBeenCalledWith(
                "enable",
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
                ConfigurationTarget.Global
            );
        });

        test("disables copilot opens extension search", async () => {
            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enable: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
            });
            mockConfig.inspect = jest.fn().mockImplementation(() => {
                return {
                    globalValue: undefined,
                    workspaceValue: undefined,
                };
            });
            mockConfig.update = jest.fn().mockImplementation(() => {
                throw new Error("Unable to update config");
            });
            commands.executeCommand = jest.fn();
            jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
                switch (key) {
                    case "github.copilot": {
                        return mockConfig;
                    }
                    default:
                        return generateMockWorkspaceConfig();
                }
            });

            await ConflictingExtensions.disableGitHubCopilot();

            expect(commands.executeCommand).toHaveBeenCalledWith(
                "workbench.extensions.search",
                "@enabled GitHub Copilot"
            );
        });
    });

    describe("disableCodeium", () => {
        test("disables codeium by updating workspace config", async () => {
            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enableConfig: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": true,
                },
            });
            mockConfig.inspect = jest.fn().mockImplementation(() => {
                return {
                    globalValue: undefined,
                    workspaceValue: true,
                };
            });
            const updateMock = jest.fn();
            mockConfig.update = updateMock;
            jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
                switch (key) {
                    case "codeium": {
                        return mockConfig;
                    }
                    default:
                        return generateMockWorkspaceConfig();
                }
            });

            await ConflictingExtensions.disableCodeium();

            expect(updateMock).toHaveBeenCalledWith(
                "enableConfig",
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
                ConfigurationTarget.Workspace
            );
        });

        test("disables codeium by updating global config", async () => {
            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enableConfig: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": true,
                },
            });
            mockConfig.inspect = jest.fn().mockImplementation(() => {
                return {
                    globalValue: undefined,
                    workspaceValue: undefined,
                };
            });
            const updateMock = jest.fn();
            mockConfig.update = updateMock;
            jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
                switch (key) {
                    case "codeium": {
                        return mockConfig;
                    }
                    default:
                        return generateMockWorkspaceConfig();
                }
            });

            await ConflictingExtensions.disableCodeium();

            expect(updateMock).toHaveBeenCalledWith(
                "enableConfig",
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
                ConfigurationTarget.Global
            );
        });

        test("disables codeium opens extension search", async () => {
            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enableConfig: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": true,
                },
            });
            mockConfig.inspect = jest.fn().mockImplementation(() => {
                return {
                    globalValue: undefined,
                    workspaceValue: undefined,
                };
            });
            mockConfig.update = jest.fn().mockImplementation(() => {
                throw new Error("Unable to update config");
            });
            commands.executeCommand = jest.fn();
            jest.spyOn(workspace, "getConfiguration").mockImplementation((key) => {
                switch (key) {
                    case "codeium": {
                        return mockConfig;
                    }
                    default:
                        return generateMockWorkspaceConfig();
                }
            });

            await ConflictingExtensions.disableCodeium();

            expect(commands.executeCommand).toHaveBeenCalledWith(
                "workbench.extensions.search",
                "@enabled Codeium"
            );
        });
    });
});

const GITHUB_COPILOT_EXTENSION_INFO = {
    id: GITHUB_COPILOT_ID,
    isActive: true,
    extensionUri: Uri.file("/no/extension/uri"),
    extensionKind: 1,
    activate: () => Promise.resolve(),
    exports: null,
    extensionPath: "/no/extension/path",
    packageJSON: {
        displayName: "GitHub Copilot",
    },
};

const CODEIUM_EXTENSION_INFO = {
    id: CODEIUM_ID,
    isActive: true,
    extensionUri: Uri.file("/no/extension/uri"),
    extensionKind: 1,
    activate: () => Promise.resolve(),
    exports: null,
    extensionPath: "/no/extension/path",
    packageJSON: {
        displayName: "Codeium",
    },
};

function setupGetExtensionsSpy(
    extensionMap: Record<string, Extension<any> | undefined>
): jest.SpyInstance {
    return jest.spyOn(extensions, "getExtension").mockImplementation((extensionId) => {
        if (extensionId in extensionMap) {
            return extensionMap[extensionId];
        }
        throw new Error(`Unexpected extension id: ${extensionId}`);
    });
}
