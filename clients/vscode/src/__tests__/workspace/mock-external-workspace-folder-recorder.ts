import { ExternalSourceFolderRecorder } from "../../workspace/external-source-folder-recorder";

export class MockExternalSourceFolderRecorder implements ExternalSourceFolderRecorder {
    private _folders = new Map<string, string>();

    public getFolders(): Map<string, string> {
        return this._folders;
    }

    // eslint-disable-next-line @typescript-eslint/require-await
    public async setFolders(folders: Map<string, string>): Promise<void> {
        this.setFoldersSync(folders);
    }

    public setFoldersSync(folders: Map<string, string>): void {
        this._folders = folders;
    }
}
