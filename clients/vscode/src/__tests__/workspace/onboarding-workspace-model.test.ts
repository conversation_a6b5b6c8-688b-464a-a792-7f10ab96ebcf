import { Disposable } from "../../__mocks__/vscode-mocks";
import { FeatureFlagManager } from "../../feature-flags";
import { AugmentGlobalState } from "../../utils/context";
import { DisposableService } from "../../utils/disposable-service";
import { OnboardingWorkspaceModel } from "../../workspace/onboarding-workspace-model";
import { SyncingStatusReporter } from "../../workspace/syncing-status-reporter";
import { SyncingStatus, SyncingStatusEvent } from "../../workspace/types";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import { WorkspaceManagerTestKit } from "./workspace-manager-test-kit";

describe("OnboardingWorkspaceModel", () => {
    let kit: OnboardingWorkspaceTestKit;

    beforeEach(() => {
        kit = new OnboardingWorkspaceTestKit();
    });

    afterEach(() => {
        kit.dispose();
    });

    test("shouldShowSummary getter and setter", () => {
        const onboardingModel = kit.createModel();

        expect(onboardingModel.shouldShowSummary).toBe(false);
        onboardingModel.setShouldShowSummary(true);
        expect(onboardingModel.shouldShowSummary).toBe(true);
    });

    test("show summary for newly tracked workspaces", async () => {
        const onboardingModel = kit.createModel();

        expect(kit.syncStatusCallback).toBeDefined();

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 100,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 50,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.done,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 15,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(true);
    });

    test("do not show summary for non-newly tracked workspaces", async () => {
        const onboardingModel = kit.createModel();

        expect(kit.syncStatusCallback).toBeDefined();

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: false,
                        trackedFiles: 100,
                        backlogSize: 100,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.done,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: false,
                        trackedFiles: 100,
                        backlogSize: 15,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);
    });

    test("show summary once for a newly tracked source folder, but don't show it again if you add another newly tracked source folder to the workspace", async () => {
        const onboardingModel = kit.createModel();

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 100,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Example in-progress sync event
        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 50,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Example complete sync event
        kit.emitSyncEvent({
            status: SyncingStatus.done,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 15,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(true);

        // Fake webview saying it's shown the summary message
        onboardingModel.setShouldShowSummary(false);

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Emulate another new folder event
        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: false,
                        trackedFiles: 100,
                        backlogSize: 0,
                    },
                },
                {
                    folderRoot: "/test/folder-2",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 100,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Emulate another new folder event
        kit.emitSyncEvent({
            status: SyncingStatus.done,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: false,
                        trackedFiles: 100,
                        backlogSize: 0,
                    },
                },
                {
                    folderRoot: "/test/folder-2",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 15,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);
    });

    test("when opening workspaces with sources folders with all newly tracked files, one after the other, show the summary message for each workspace", async () => {
        const onboardingModel = kit.createModel();

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 100,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Example in-progress sync event
        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 50,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Example complete sync event
        kit.emitSyncEvent({
            status: SyncingStatus.done,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 15,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(true);

        // Fake webview saying it's shown the summary message
        onboardingModel.setShouldShowSummary(false);

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Emulate another new folder event
        kit.emitSyncEvent({
            status: SyncingStatus.longRunning,
            foldersProgress: [
                {
                    folderRoot: "/test/folder-2",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 100,
                    },
                },
                {
                    folderRoot: "/test/folder-3",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 100,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);

        // Emulate another new folder event
        kit.emitSyncEvent({
            status: SyncingStatus.done,
            foldersProgress: [
                {
                    folderRoot: "/test/folder-2",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 15,
                    },
                },
                {
                    folderRoot: "/test/folder-3",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 100,
                        backlogSize: 15,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(true);
    });

    test("do not show summary if all workspaces are empty", async () => {
        const onboardingModel = kit.createModel();

        expect(kit.syncStatusCallback).toBeDefined();

        expect(onboardingModel.shouldShowSummary).toBe(false);

        kit.emitSyncEvent({
            status: SyncingStatus.done,
            foldersProgress: [
                {
                    folderRoot: "/test/folder",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 0,
                        backlogSize: 0,
                    },
                },
                {
                    folderRoot: "/test/folder-2",
                    progress: {
                        newlyTracked: true,
                        trackedFiles: 0,
                        backlogSize: 0,
                    },
                },
            ],
        });

        expect(onboardingModel.shouldShowSummary).toBe(false);
    });
});

class OnboardingWorkspaceTestKit extends DisposableService {
    private readonly workspaceKit: WorkspaceManagerTestKit;
    private readonly workspaceManager: WorkspaceManager;
    private readonly globalState: jest.Mocked<AugmentGlobalState>;
    private readonly syncStatusReporter: SyncingStatusReporter;
    private _syncStatusCallback: ((e: SyncingStatusEvent) => any) | undefined = undefined;
    private _statusSpy: jest.SpyInstance<SyncingStatusEvent, [], any>;

    constructor() {
        super();

        this.workspaceKit = new WorkspaceManagerTestKit();
        this.addDisposable(this.workspaceKit);

        this.workspaceManager = this.workspaceKit.createWorkspaceManager();

        this.globalState = {
            update: jest.fn(),
            get: jest.fn(),
        } as unknown as jest.Mocked<AugmentGlobalState>;

        this.syncStatusReporter = new SyncingStatusReporter(
            new FeatureFlagManager(),
            this.workspaceManager
        );
        this._statusSpy = jest.spyOn(this.syncStatusReporter, "status", "get");

        const syncStatusEvent = jest.spyOn(this.syncStatusReporter, "onDidChangeSyncingStatus");
        syncStatusEvent.mockImplementation((listener: (e: SyncingStatusEvent) => any) => {
            this._syncStatusCallback = listener;
            return new Disposable(() => {});
        });
    }

    get syncStatusCallback() {
        return this._syncStatusCallback;
    }

    emitSyncEvent(event: SyncingStatusEvent) {
        this._statusSpy.mockReturnValue(event);
        this._syncStatusCallback!(event);
    }

    public createModel() {
        return new OnboardingWorkspaceModel(this.globalState, this.syncStatusReporter);
    }
}
