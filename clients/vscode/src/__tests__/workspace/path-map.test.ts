import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import * as vscode from "vscode";

import { QualifiedPathName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import { Mock<PERSON>cceptPath, MockRejecPath } from "../../__mocks__/mock-accept-path";
import { DisposableService } from "../../utils/disposable-service";
import { descendentPath, directoryContainsPath } from "../../utils/path-utils";
import { FileType } from "../../utils/types";
import { PathMap, PathStatusChangeEvent } from "../../workspace/path-map";
import { verifyDefined } from "../__utils__/test-utils";

async function runTimers() {
    await jest.runOnlyPendingTimersAsync();
}

function verifyQualifiedPath(
    qualifiedPath: QualifiedPathName | undefined,
    expectedRootPath: string,
    expectedRelPath: string
) {
    verifyDefined(qualifiedPath);
    expect(qualifiedPath.rootPath).toBe(expectedRootPath);
    expect(qualifiedPath.relPath).toBe(expectedRelPath);
}

function validatePathMap(map: PathMap): void {
    const folderIds = map.getFolderIds();
    for (const folderId of folderIds) {
        // Validate pathName <-> blobName mapping.
        for (const [folderId, repoRoot, pathName, _m, blobName] of map.pathsWithBlobNames()) {
            expect(map.getBlobName(folderId, pathName)).toBe(blobName);
            const qualifiedPathNames = map.getAllPathNames(blobName);
            let found = false;
            for (const qualifiedPathName of qualifiedPathNames) {
                if (qualifiedPathName.rootPath !== repoRoot) {
                    continue;
                }
                expect(qualifiedPathName.relPath).toBe(pathName);
                found = true;
                break;
            }
            expect(found).toBe(true);
        }

        // Validate trackedFileCount.
        let trackedFileCount = 0;
        for (const [_pathName, fileType, accepted] of map.pathsInFolder(folderId)) {
            if (fileType === FileType.file && accepted) {
                trackedFileCount++;
            }
        }
        expect(trackedFileCount).toBe(map.trackedFileCount(folderId));
    }
}

describe("PathMap, single folder", () => {
    const folderRoot = "/src/folder-root";
    let map: PathMap;
    let folderId: number;
    let contentSeq = 11111;
    let mtime = 12345;
    const acceptPath = new MockAcceptPath();
    const rejectPath = new MockRejecPath();

    const makePathMap = (): [PathMap, number] => {
        const map = new PathMap();
        const folderId = map.openSourceFolder(folderRoot, folderRoot);
        return [map, folderId];
    };

    beforeEach(() => {
        jest.useFakeTimers();
        [map, folderId] = makePathMap();
    });

    afterEach(() => {
        validatePathMap(map);
        map.dispose();
        jest.useRealTimers();
    });

    test("getFolderRoot", () => {
        expect(map.getRepoRoot(folderId)).toBe(folderRoot);
    });

    test("startTracking, has, and shouldTrack", () => {
        const pathName = "giraffe/alligator.py";
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        map.insert(folderId, pathName, FileType.file, acceptPath);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
    });

    test("markUntrackable", () => {
        const pathName = "giraffe/alligator.py";
        const origBlobName = "orig blob name for alligator.py";
        const newBlobName = "new blob name for alligator.py";

        // Insert pathName as a tracked file.
        map.insert(folderId, pathName, FileType.file, acceptPath);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBeUndefined();

        // Mark it untrackable.
        map.markUntrackable(folderId, pathName, contentSeq, "untrackable reason");
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBeUndefined();

        // An update at a lower sequence number should have no effect.
        map.update(folderId, pathName, contentSeq - 10, origBlobName, mtime++);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBeUndefined();

        // An update at a higher sequence number should succeed.
        map.update(folderId, pathName, contentSeq + 10, newBlobName, mtime++);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBeDefined();
    });

    test("update new", () => {
        const pathName = "giraffe/alligator.py";
        const blobName = "blob name for alligator.py";

        map.insert(folderId, pathName, FileType.file, acceptPath);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);
        expect(map.getAnyPathName(blobName)).toBe(undefined);
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.getBlobName(folderId, pathName)).toBe(blobName);
        const qualifiedPathName = map.getAnyPathName(blobName);
        verifyQualifiedPath(qualifiedPathName, folderRoot, pathName);
    });

    test("update existing", () => {
        const pathName = "giraffe/alligator.py";
        const oldBlobName = "old blob name for alligator.py";
        const newBlobName = "new blob name for alligator.py";

        map.insert(folderId, pathName, FileType.file, acceptPath);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);
        map.update(folderId, pathName, contentSeq, oldBlobName, mtime);
        expect(map.getBlobName(folderId, pathName)).toBe(oldBlobName);
        let qualifiedPathName = map.getAnyPathName(oldBlobName);
        verifyQualifiedPath(qualifiedPathName, folderRoot, pathName);

        expect(map.getAnyPathName(newBlobName)).toBe(undefined);
        map.update(folderId, pathName, contentSeq, newBlobName, mtime);
        expect(map.getBlobName(folderId, pathName)).toBe(newBlobName);
        expect(map.getAnyPathName(oldBlobName)).toBe(undefined);
        qualifiedPathName = map.getAnyPathName(newBlobName);
        verifyQualifiedPath(qualifiedPathName, folderRoot, pathName);
    });

    test("getContentSeq", () => {
        const pathName = "giraffe/alligator.py";
        const blobName = "blob name for alligator.py";
        const contentSeq = 83;

        map.insert(folderId, pathName, FileType.file, acceptPath);
        expect(map.getContentSeq(folderId, pathName)).toBe(undefined);
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.getContentSeq(folderId, pathName)).toBe(contentSeq);
    });

    test("mtime mismatch", () => {
        const pathName = "giraffe/alligator.py";
        const blobName = "blob name for alligator.py";

        map.insert(folderId, pathName, FileType.file, acceptPath);
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.getBlobInfo(folderId, pathName, mtime)?.[0]).toBe(blobName);
        expect(map.getBlobInfo(folderId, pathName, mtime + 10)).toBe(undefined);
        expect(map.getBlobInfo(folderId, pathName, mtime - 10)).toBe(undefined);
    });

    // Verify that the `purge` method removes all entries with entryTS < oldestTS (and only those
    // entries).
    test("purge", () => {
        const itemCount = 20;

        const paths = new Map<string, number>();
        for (let i = 0; i < itemCount; i++) {
            const pathName = `path-${i}`;
            const entryTS = map.nextEntryTS;
            paths.set(pathName, entryTS);
            map.insert(folderId, pathName, FileType.file, acceptPath);
        }

        for (const pathName of paths.keys()) {
            expect(map.shouldTrack(folderId, pathName)).toBe(true);
        }

        const oldestTS = map.nextEntryTS - itemCount / 2;
        map.purge(folderId, oldestTS);

        for (const [pathName, seq] of paths) {
            expect(map.shouldTrack(folderId, pathName)).toBe(seq >= oldestTS);
        }
    });

    // Verify that updating or removing a blob name causes the onDidChangeBlobName event to be
    // fired.
    test("publish", () => {
        const pathName = "giraffe/alligator.py";
        const origBlobName = "original blob name for alligator.py";
        const newBlobName = "new blob name for alligator.py";

        map.insert(folderId, pathName, FileType.file, acceptPath);

        let currBlobName: string | undefined = undefined;
        map.onDidChangeBlobName((event) => {
            const relPath = descendentPath(folderRoot, event.absPath);
            verifyDefined(relPath);
            expect(relPath).toBe(pathName);
            expect(event.prevBlobName).toBe(currBlobName);
            currBlobName = event.newBlobName;
        });

        map.update(folderId, pathName, contentSeq, origBlobName, mtime);
        expect(currBlobName).toBe(origBlobName);

        map.update(folderId, pathName, contentSeq, newBlobName, mtime);
        expect(currBlobName).toBe(newBlobName);

        map.remove(folderId, pathName);
        expect(currBlobName).toBe(undefined);
    });

    test("older content shouldn't clobber newer content", () => {
        const pathName = "giraffe/alligator.py";
        const oldBlobName = "old blob name for alligator.py";
        const newBlobName = "new blob name for alligator.py";
        const oldContentSeq = 100;
        const newContentSeq = 200;

        map.insert(folderId, pathName, FileType.file, acceptPath);
        map.update(folderId, pathName, newContentSeq, newBlobName, mtime);
        expect(map.getBlobName(folderId, pathName)).toBe(newBlobName);

        map.update(folderId, pathName, oldContentSeq, oldBlobName, mtime);
        expect(map.getBlobName(folderId, pathName)).toBe(newBlobName);
    });

    test("update untracked", () => {
        const pathName = "giraffe/alligator.py";
        const blobName = "blob name for alligator.py";

        // Updating a path that was never inserted should not affect the map.
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
    });

    test("remove", () => {
        const pathName = "giraffe/alligator.py";
        const blobName = "blob name for alligator.py";

        map.insert(folderId, pathName, FileType.file, acceptPath);
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBe(blobName);
        let qualifiedPathName = map.getAnyPathName(blobName);
        verifyQualifiedPath(qualifiedPathName, folderRoot, pathName);

        map.remove(folderId, pathName);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);
        expect(map.getAnyPathName(blobName)).toBe(undefined);
    });

    test("rejected path", () => {
        const pathName = "giraffe/alligator.bad-file-extension";
        const blobName = "blob name for alligator.bad-file-extension";

        map.insert(folderId, pathName, FileType.file, rejectPath);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);

        // Updating a rejected path should have no effect.
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);
    });

    test("not a file", () => {
        const pathName = "giraffe/alligator";
        const blobName = "blob name";

        map.insert(folderId, pathName, FileType.directory, acceptPath);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);

        // Trying to set a blob name for a non-file path should do nothing.
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);
    });

    // Verify that re-inserting an already accepted path does not change its state.
    test("restate accepted path", () => {
        const pathName = "giraffe/alligator.py";
        const blobName = "blob name for alligator.py";

        map.insert(folderId, pathName, FileType.file, acceptPath);
        map.update(folderId, pathName, contentSeq, blobName, mtime);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBe(blobName);

        map.insert(folderId, pathName, FileType.file, acceptPath);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBe(blobName);
    });

    // Verify that a path that was previously accepted but is now rejected is no longer tracked.
    test("path accepted then rejected", () => {
        const pathName = "giraffe/alligator.py";

        map.insert(folderId, pathName, FileType.file, acceptPath);
        map.update(folderId, pathName, contentSeq++, "blobName 1", mtime++);
        expect(map.shouldTrack(folderId, pathName)).toBe(true);
        expect(map.getBlobName(folderId, pathName)).toBeDefined();

        map.insert(folderId, pathName, FileType.file, rejectPath);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);

        map.update(folderId, pathName, contentSeq++, "blobName 2", mtime++);
        expect(map.shouldTrack(folderId, pathName)).toBe(false);
        expect(map.getBlobName(folderId, pathName)).toBe(undefined);
    });

    test("pathsWithBlobNames", () => {
        // pathNames: Map<pathName, blobName | undefined>
        const pathNames = new Map<string, string | undefined>([
            ["giraffe/alligator.py", "blob name for alligator.py"],
            ["giraffe/crocodile.py", "blob name for crocodile.py"],
            ["giraffe/elephant.py", undefined],
            ["giraffe/rhinoceros.py", "blob name for rhinoceros.py"],
            ["giraffe/zebra.py", "blob name for zebra.py"],
        ]);
        const toInvalidate = new Set<string>(["giraffe/zebra.py"]);

        for (const [pathName, blobName] of pathNames) {
            map.insert(folderId, pathName, FileType.file, acceptPath);
            if (blobName !== undefined) {
                map.update(folderId, pathName, contentSeq++, blobName, mtime++);
                if (toInvalidate.has(pathName)) {
                    map.markUntrackable(folderId, pathName, contentSeq++, "a good reason");
                }
            }
        }

        const results = new Map<string, string>();
        for (const [_folderId, _repoRoot, relPath, _mtime, blobName] of map.pathsWithBlobNames()) {
            results.set(relPath, blobName);
        }

        expect(results.size).toBe(3);
        for (const [pathName, blobName] of pathNames) {
            if (blobName === undefined || toInvalidate.has(pathName)) {
                continue;
            }
            expect(results.get(pathName)).toBe(blobName);
        }
    });

    test("pathsInFolder", () => {
        // pathNames: Map<pathName, [fileType, untrackableReason | undefined]>
        const pathNames = new Map<string, [FileType, string | undefined]>([
            ["giraffe/alligator.py", [FileType.file, undefined]],
            ["giraffe/bear", [FileType.directory, undefined]],
            ["giraffe/crocodile.py", [FileType.other, undefined]],
            ["giraffe/elephant.py", [FileType.file, "unreadable file"]],
            ["giraffe/gorilla", [FileType.directory, undefined]],
            ["giraffe/kangaroo.py", [FileType.file, "file is too large"]],
            ["giraffe/rhinoceros.py", [FileType.file, undefined]],
            ["giraffe/zebra.py", [FileType.file, undefined]],
            ["giraffe/zebra/giraffe.py", [FileType.other, undefined]],
        ]);

        for (const [pathName, [fileType, untrackableReason]] of pathNames) {
            map.insert(folderId, pathName, fileType, acceptPath);
            if (untrackableReason !== undefined) {
                map.markUntrackable(folderId, pathName, contentSeq++, untrackableReason);
            }
        }

        for (const [pathName, fileType, accepted, _indexed, reason] of map.pathsInFolder(
            folderId
        )) {
            const expectation = pathNames.get(pathName);
            verifyDefined(expectation);
            const [expectedFileType, expectedReason] = expectation;
            expect(fileType).toBe(expectedFileType);
            if (fileType === FileType.other) {
                expect(accepted).toBe(false);
            } else {
                expect(accepted).toBe(reason === acceptPath.format());
            }
            if (expectedReason !== undefined) {
                expect(reason).toBe(expectedReason);
            }
        }
    });

    test("persist", async () => {
        // pathNames = new Array<[pathName, [contentSeq, blobName, mtime] | undefined]>
        const pathNames = new Array<[string, [number, string, number] | undefined]>(
            ["giraffe/alligator.py", [contentSeq++, "blob name for alligator.py", mtime++]],
            ["giraffe/crocodile.py", [contentSeq++, "blob name for crocodile.py", mtime++]],
            ["giraffe/elephant.py", undefined],
            ["giraffe/rhinoceros.py", [contentSeq++, "blob name for rhinoceros.py", mtime++]],
            ["giraffe/zebra.py", [contentSeq++, "blob name for zebra.py", mtime++]]
        );

        const results = new Map<string, [string, number] | undefined>();
        const writer = {
            async write(iter: Iterable<[string, number, string]>): Promise<void> {
                for (const [pathName, mtime, blobName] of iter) {
                    results.set(pathName, [blobName, mtime]);
                }
            },
        };

        const addEntry = (idx: number) => {
            const [pathName, entry] = pathNames[idx];
            map.insert(folderId, pathName, FileType.file, acceptPath);
            if (entry !== undefined) {
                map.update(folderId, pathName, entry[0], entry[1], entry[2]);
            }
        };

        const verifyResultSize = async (expectedSize: number) => {
            await runTimers();
            expect(results.size).toBe(expectedSize);
        };

        map.enablePersist(folderId, writer, 2);

        results.clear();
        addEntry(0);
        await verifyResultSize(1);

        results.clear();
        addEntry(1);
        await verifyResultSize(2);

        // Entry 2 has no blobName. Adding it should not trigger a persist.
        results.clear();
        addEntry(2);
        await verifyResultSize(0);

        results.clear();
        addEntry(3);
        await verifyResultSize(3);

        results.clear();
        addEntry(4);
        await verifyResultSize(4);

        for (const [pathName, entry] of pathNames) {
            if (entry === undefined) {
                expect(results.has(pathName)).toBe(false);
            } else {
                expect(results.get(pathName)).toStrictEqual([entry[1], entry[2]]);
            }
        }
    });
});

// PathStatusChangeEventListener is a helper class for verifying that path status change events are
// correctly emitted.
class PathStatusChangeEventListener extends DisposableService {
    private _eventCount = 0;

    constructor(event: vscode.Event<PathStatusChangeEvent>) {
        super();
        this.addDisposable(
            event((_: PathStatusChangeEvent) => {
                this._eventCount++;
            })
        );
    }

    // verify that the given action emits exactly one event.
    public verifyEmit(action: () => void): void {
        this.verifyEventCountDelta(action, 1);
    }

    // verify that the given action emits no events.
    public verifyNoEmit(action: () => void): void {
        this.verifyEventCountDelta(action, 0);
    }

    // verify that the given action emits exactly the given number of events.
    public verifyEventCountDelta(action: () => void, eventCount: number): void {
        const beforeCount = this._eventCount;
        action();
        expect(this._eventCount).toBe(beforeCount + eventCount);
    }
}

// Tests for verifying that PathMap emits the correct path status change events.
describe("path status change events", () => {
    const folderRoot = "/src/folder-root";
    const pathName = "giraffe/alligator.py";
    let map: PathMap;
    let folderId: number;
    let contentSeq = 11111;
    let mtime = 12345;
    let listener: PathStatusChangeEventListener;
    const acceptPath = new MockAcceptPath();
    const rejectPath = new MockRejecPath();

    const makePathMap = (): [PathMap, number] => {
        const map = new PathMap();
        const folderId = map.openSourceFolder(folderRoot, folderRoot);
        return [map, folderId];
    };

    beforeEach(() => {
        jest.useFakeTimers();
        [map, folderId] = makePathMap();

        // Create a listener for path status change events.
        const event = map.onDidChangePathStatus(folderId);
        verifyDefined(event);
        listener = new PathStatusChangeEventListener(event);
    });

    afterEach(() => {
        listener.dispose();
        map.dispose();
        jest.useRealTimers();
    });

    test("add accepted path", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
    });

    test("add rejected path", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, rejectPath);
        });
    });

    test("add accepted path, then remove", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
        listener.verifyEmit(() => {
            map.remove(folderId, pathName);
        });
    });

    test("add rejected path, then remove", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, rejectPath);
        });
        listener.verifyEmit(() => {
            map.remove(folderId, pathName);
        });
    });

    test("accepted, then rejected", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
        listener.verifyEmit(() => {
            map.update(folderId, pathName, contentSeq++, "blob name", mtime++);
        });
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, rejectPath);
        });
    });

    test("rejected, then accepted", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, rejectPath);
        });
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
    });

    test("accepted, then untrackable 1", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
        listener.verifyEmit(() => {
            map.markUntrackable(folderId, pathName, contentSeq++, "untrackable reason");
        });
    });

    test("accepted, then untrackable 2", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
        listener.verifyEmit(() => {
            map.update(folderId, pathName, contentSeq++, "blob name", mtime++);
        });
        listener.verifyEmit(() => {
            map.markUntrackable(folderId, pathName, contentSeq++, "untrackable reason");
        });
    });

    test("accepted, then untrackable, then tracked", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
        listener.verifyEmit(() => {
            map.markUntrackable(folderId, pathName, contentSeq++, "untrackable reason");
        });
        listener.verifyEmit(() => {
            map.update(folderId, pathName, contentSeq++, "blob name", mtime++);
        });
    });

    test("change untrackable reason", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
        listener.verifyEmit(() => {
            map.markUntrackable(folderId, pathName, contentSeq++, "untrackable reason 1");
        });
        listener.verifyEmit(() => {
            map.markUntrackable(folderId, pathName, contentSeq++, "untrackable reason2");
        });
    });

    // Verify that changing a blob name from undefined to defined triggers an event (it signals
    // a transition from not indexed to indexed), while a change from one blob name to another does
    // not (no change in the trackable state or indexed state of the blob).
    test("change blob name", async () => {
        listener.verifyEmit(() => {
            map.insert(folderId, pathName, FileType.file, acceptPath);
        });
        listener.verifyEmit(() => {
            map.update(folderId, pathName, contentSeq++, "blob name 1", mtime++);
        });
        listener.verifyNoEmit(() => {
            map.update(folderId, pathName, contentSeq++, "blob name 2", mtime++);
        });
    });
});

describe("PathMap, multi folder", () => {
    const folderRoot1 = "/src/folder-root1";
    const folderRoot2 = "/src/folder-root2";
    let map: PathMap;
    let folderId1: number;
    const acceptance = new MockAcceptPath();
    let contentSeq = 11111;
    let mtime = 12345;

    const makePathMap = (): [PathMap, number] => {
        const map = new PathMap();

        // Only open folderRoot1. Tests will open folderRoot2 as needed.
        const folderId = map.openSourceFolder(folderRoot1, folderRoot1);
        return [map, folderId];
    };

    beforeEach(() => {
        jest.useFakeTimers();
        [map, folderId1] = makePathMap();
    });

    afterEach(() => {
        map.dispose();
        jest.useRealTimers();
    });

    test("open and close source folder", () => {
        const pathName = "sloth/cat.py";
        const folderId2 = map.openSourceFolder(folderRoot2, folderRoot2);

        map.insert(folderId1, pathName, FileType.file, acceptance);
        expect(map.shouldTrack(folderId1, pathName)).toBe(true);

        map.insert(folderId2, pathName, FileType.file, acceptance);
        expect(map.shouldTrack(folderId2, pathName)).toBe(true);

        map.closeSourceFolder(folderId2);
        expect(map.shouldTrack(folderId2, pathName)).toBe(false);
    });

    test("getFolderRoot", () => {
        expect(map.getRepoRoot(folderId1)).toBe(folderRoot1);
        expect(map.getRepoRoot(folderId1 + 123)).toBe(undefined);
        const folderId2 = map.openSourceFolder(folderRoot2, folderRoot2);
        expect(map.getRepoRoot(folderId2)).toBe(folderRoot2);
    });

    test("nested source folders", () => {
        const repoRoot = "/src/repoRoot";
        const outerFolderRoot = joinPath(repoRoot, "abc");
        const middleFolderRoot = joinPath(repoRoot, "abc/def");
        const innerFolderRoot = joinPath(repoRoot, "abc/def/ghi");

        // Open the middle folder root.
        map.openSourceFolder(middleFolderRoot, middleFolderRoot);

        // Opening the any of the three folder roots should now fail, because they contain, are the
        // same as, or are contained by, the already-open middle folder.
        expect(() => map.openSourceFolder(outerFolderRoot, repoRoot)).toThrow();
        expect(() => map.openSourceFolder(middleFolderRoot, repoRoot)).toThrow();
        expect(() => map.openSourceFolder(innerFolderRoot, repoRoot)).toThrow();
    });

    test("getBlobName/getPathName", () => {
        const pathName = "sloth/cat.py";
        const folderId2 = map.openSourceFolder(folderRoot2, folderRoot2);
        const blobName1 = "blob name for cat.py in folder 1";
        const blobName2 = "blob name for cat.py in folder 2";

        map.insert(folderId1, pathName, FileType.file, acceptance);
        map.update(folderId1, pathName, contentSeq, blobName1, mtime);

        map.insert(folderId2, pathName, FileType.file, acceptance);
        map.update(folderId2, pathName, contentSeq, blobName2, mtime);

        expect(map.getBlobName(folderId1, pathName)).toBe(blobName1);
        expect(map.getBlobName(folderId2, pathName)).toBe(blobName2);

        const qualifiedPathName1 = map.getAnyPathName(blobName1);
        verifyQualifiedPath(qualifiedPathName1, folderRoot1, pathName);

        const qualifiedPathName2 = map.getAnyPathName(blobName2);
        expect(qualifiedPathName2).toBeDefined();
        verifyQualifiedPath(qualifiedPathName2, folderRoot2, pathName);
    });

    // Verify that getAllPathNames and getAllPathInfo correctly report all paths for a blob that
    // has multiple path names.
    test("getAllPathNames/getAllPathInfo", () => {
        const pathName = "sloth/cat.py";
        const folderId2 = map.openSourceFolder(folderRoot2, folderRoot2);
        const blobName = "blob name for cat.py";

        // Insert the same path name and blob name for both source folders.
        map.insert(folderId1, pathName, FileType.file, acceptance);
        map.update(folderId1, pathName, contentSeq, blobName, mtime);
        map.insert(folderId2, pathName, FileType.file, acceptance);
        map.update(folderId2, pathName, contentSeq, blobName, mtime);

        const pathNameCount = map.getAllPathNames(blobName).length;
        expect(pathNameCount).toBe(2);

        const pathInfoCount = map.getAllPathInfo(blobName).length;
        expect(pathInfoCount).toBe(2);
    });

    test("publish", () => {
        const folderId2 = map.openSourceFolder(folderRoot2, folderRoot2);
        const pathName = "giraffe/alligator.py";
        const folder1BlobName = "folder1 blob name for alligator.py";
        const folder2BlobName = "folder2 blob name for alligator.py";
        map.insert(folderId1, pathName, FileType.file, acceptance);
        map.insert(folderId2, pathName, FileType.file, acceptance);

        let currBlobName = new Map<string, string | undefined>();
        map.onDidChangeBlobName((event) => {
            let folderRoot: string | undefined;
            if (directoryContainsPath(folderRoot1, event.absPath)) {
                folderRoot = folderRoot1;
            } else if (directoryContainsPath(folderRoot2, event.absPath)) {
                folderRoot = folderRoot2;
            }
            verifyDefined(folderRoot);

            const relPath = descendentPath(folderRoot, event.absPath);
            verifyDefined(relPath);
            expect(relPath).toBe(pathName);
            expect(currBlobName.get(folderRoot)).toBe(event.prevBlobName);
            currBlobName.set(folderRoot, event.newBlobName);
        });

        map.update(folderId1, pathName, contentSeq, folder1BlobName, mtime);
        expect(currBlobName.get(folderRoot1)).toBe(folder1BlobName);
        expect(currBlobName.get(folderRoot2)).toBeUndefined();

        map.update(folderId2, pathName, contentSeq, folder2BlobName, mtime);
        expect(currBlobName.get(folderRoot1)).toBe(folder1BlobName);
        expect(currBlobName.get(folderRoot2)).toBe(folder2BlobName);
    });

    test("iterator", () => {
        const pathName = "sloth/cat.py";
        const folderId2 = map.openSourceFolder(folderRoot2, folderRoot2);
        const blobName1 = "blob name for cat.py in folder 1";
        const blobName2 = "blob name for cat.py in folder 2";

        map.insert(folderId1, pathName, FileType.file, acceptance);
        map.update(folderId1, pathName, contentSeq, blobName1, mtime);

        map.insert(folderId2, pathName, FileType.file, acceptance);
        map.update(folderId2, pathName, contentSeq, blobName2, mtime);

        let resultCount = 0;
        for (const [folderId, _repoRoot, relPath, _mtime, blobName] of map.pathsWithBlobNames()) {
            if (folderId === folderId1) {
                expect(relPath).toBe(pathName);
                expect(blobName).toBe(blobName1);
            } else {
                expect(folderId).toBe(folderId2);
                expect(relPath).toBe(pathName);
                expect(blobName).toBe(blobName2);
            }
            resultCount++;
        }
        expect(resultCount).toBe(2);
    });
});
