import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import * as vscode from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import {
    publishActiveTextEditorChange,
    publishFileDelete,
    publishFileRename,
    publishTextDocumentChange,
    resetMockWorkspace,
    setOpenTextDocuments,
    TextDocument,
    TextEditor,
} from "../../__mocks__/vscode-mocks";
import { MockSyncingPermissionTracker } from "../../__mocks__/workspace/mock-syncing-permission-tracker";
import { BatchUploadResult, FindMissingResult, UploadBlob } from "../../augment-api";
import { defaultFeatureFlags } from "../../feature-flags";
import { DerivedStateName } from "../../main-panel/action-cards/types";
import { IgnoreStackBuilder } from "../../utils/ignore-file";
import { isHomeDir } from "../../utils/os";
import * as pathIteratorModule from "../../utils/path-iterator";
import { relativePathName } from "../../utils/path-utils";
import { disposableDelayer } from "../../utils/promise-utils";
import { FileType } from "../../utils/types";
import { uriToAbsPath } from "../../utils/uri";
import { VCSWatcherImpl } from "../../vcs/watcher";
import { HeadChangeWatcher } from "../../vcs/watcher/head-change-watcher";
import { makeMtimeCacheFileName } from "../../workspace/mtime-cache";
import { SourceFolderSyncingProgress, SyncingEnabledState } from "../../workspace/types";
import { FileChangeOrigin, WorkspaceManager } from "../../workspace/workspace-manager";
import {
    HomeDirectoryError,
    SourceFolderEnumerationState as PublicSourceFolderEnumerationState,
    SourceFolderType as PublicSourceFolderType,
    SourceFolderInfo,
    SourceFolderItem,
    SourceFolderType,
    TrackedSourceFolderInfo,
    UntrackedFolderReason,
} from "../../workspace/workspace-types";
import { verifyDefined } from "../__utils__/test-utils";
import { advanceTimeUntilResolve, advanceTimeUntilTrue, waitMs } from "../__utils__/time";
import {
    applyWorkspaceState,
    awaitStartup,
    awaitWorkspaceState,
    createSourceFolderFiles,
    createWorkspaceFiles,
    defaultWorkspaceFolder,
    drainWorkspaceBacklog,
    makeQualifiedPath,
    MockSourceFolder,
    MockSourceFolderState,
    MockSourceFolderType,
    pathHasBlobName,
    pathNameInSourceFolder,
    verifyWorkspaceState,
    WorkspaceManagerTestKit,
    writeFileInFolder,
} from "./workspace-manager-test-kit";

const workspaceFolder1: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/folder1"),
    repoRootUri: vscode.Uri.file("/src/folder1"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of sourceFolder1"],
        ["file2.py", "this is file 2"],
    ]),
    ignoredFiles: new Map<string, string>([[".git/logs/HEAD", "head file"]]),
};

const workspaceFolder2: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/proj/folder2"),
    repoRootUri: vscode.Uri.file("/proj/folder2"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of sourceFolder2"],
        ["file2.py", "this is file 2"],
    ]),
    ignoredFiles: new Map<string, string>([[".git/logs/HEAD", "head file"]]),
};

const nestedWorkspaceFolder: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.joinPath(workspaceFolder1.folderRootUri, "inner-folder"),
    repoRootUri: workspaceFolder1.repoRootUri,
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of nestedSourceFolder"],
        ["file2.py", "this is file 2"],
    ]),
};

const notNestedWorkspaceFolder1: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/folder1xyz"),
    repoRootUri: vscode.Uri.file("/src/folder1xyz"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of sourceFolder1xyz"],
        ["file2.py", "this is file 2"],
    ]),
};

const externalFolder1: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.externalFolder,
    folderRootUri: vscode.Uri.file("/external/folder1"),
    repoRootUri: vscode.Uri.file("/external/folder1"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of externalFolder1"],
        ["file2.py", "this is file 2"],
    ]),
};

const externalFolder2: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.externalFolder,
    folderRootUri: vscode.Uri.file("/external-repos/folder2"),
    repoRootUri: vscode.Uri.file("/external-repos/folder2"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of externalFolder2"],
        ["file2.py", "this is file 2"],
    ]),
};

const nestedExternalFolder: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.externalFolder,
    folderRootUri: vscode.Uri.joinPath(externalFolder1.folderRootUri, "inner-folder"),
    repoRootUri: externalFolder1.repoRootUri,
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of nestedExternalFolder"],
        ["file2.py", "this is file 2"],
    ]),
};

const externalFolderNestedinWorkspaceFolder: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.externalFolder,
    folderRootUri: vscode.Uri.joinPath(workspaceFolder1.folderRootUri, "nested-ext-folder"),
    repoRootUri: workspaceFolder1.repoRootUri,
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of externalFolderNestedinWorkspaceFolder"],
        ["file2.py", "this is file 2"],
    ]),
};

const workspaceFolderNestedinExternalFolder: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.joinPath(externalFolder1.folderRootUri, "nested-ws-folder"),
    repoRootUri: externalFolder1.repoRootUri,
    files: new Map<string, string>([
        ["file1.py", "this is file 1 of workspaceFolderNestedinExternalFolder"],
        ["file2.py", "this is file 2"],
    ]),
};

const siblingFolder1: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/folder1/sibling1"),
    repoRootUri: vscode.Uri.file("/src/folder1"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1"],
        ["file2.py", "this is file 2"],
    ]),
};

const siblingFolder2: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/folder1/sibling2"),
    repoRootUri: vscode.Uri.file("/src/folder1"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1"],
        ["file2.py", "this is file 2"],
    ]),
};

const allWorkspaceFolders = [
    workspaceFolder1,
    workspaceFolder2,
    nestedWorkspaceFolder,
    notNestedWorkspaceFolder1,
    externalFolder1,
    externalFolder2,
    nestedExternalFolder,
    externalFolderNestedinWorkspaceFolder,
    workspaceFolderNestedinExternalFolder,
    siblingFolder1,
    siblingFolder2,
];

describe("Workspace operations", () => {
    let kit: WorkspaceManagerTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createWorkspaceFiles(allWorkspaceFolders);
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Test startup of a single-folder workspace.
    test("startup single-folder", async () => {
        const workspaceState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    // Test startup of a multi-folder workspace.
    test("startup multi-folder", async () => {
        const workspaceState = [
            { ...workspaceFolder1, tracked: true },
            { ...workspaceFolder2, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
            { ...notNestedWorkspaceFolder1, tracked: true },
        ];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    // Test adding a folder to an existing workspace.
    test("add folder", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = initialState.concat([{ ...workspaceFolder2, tracked: true }]);
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    // Test removing a folder from an existing workspace.
    test("remove folder", async () => {
        const initialState = [
            { ...workspaceFolder1, tracked: true },
            { ...workspaceFolder2, tracked: true },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [{ ...workspaceFolder2, tracked: true }];
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    // Test startup of a workspace with two folders, one nested inside the other.
    test("startup with nested folder", async () => {
        const workspaceState = [
            { ...workspaceFolder1, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
        ];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    // Test adding a folder that is nested inside an existing folder.
    test("add nested folder", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [
            { ...workspaceFolder1, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
        ];
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    // Test removing a folder that is nested inside an existing folder.
    test("remove nested folder", async () => {
        const initialState = [
            { ...workspaceFolder1, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [{ ...workspaceFolder1, tracked: true }];
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    // Test adding a folder that contains an existing folder, such that the inner folder should no
    // longer be tracked.
    test("add outer folder", async () => {
        const initialState = [{ ...nestedWorkspaceFolder, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [
            { ...workspaceFolder1, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
        ];
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    // Test removing a folder that contains an existing folder, such that the inner folder should
    // now be tracked.
    test("remove outer folder", async () => {
        const initialState = [
            { ...workspaceFolder1, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [{ ...nestedWorkspaceFolder, tracked: true }];
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    // Verify that a folder whose name starts with the name of another folder is not mistakenly
    // believed to be nested inside that folder.
    test("not actually nested folder", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [
            { ...workspaceFolder1, tracked: true },
            { ...notNestedWorkspaceFolder1, tracked: true },
        ];
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    // Verify that we can track non-nested source folders that share the same repo root.
    test("same repo root", async () => {
        const initialState = [
            { ...siblingFolder1, tracked: true },
            { ...siblingFolder2, tracked: true },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);
    });

    // Verify that the workspace manager correctly maps qualified path names to blob names.
    describe("get blob name", () => {
        test("folderRoot == repoRoot", async () => {
            const initialState = [{ ...workspaceFolder1, tracked: true }];
            kit.setupWorkspace(initialState);
            const workspaceManager = kit.createWorkspaceManager();
            await awaitStartup(workspaceManager, initialState);
            for (const relPath of workspaceFolder1.files.keys()) {
                const qualifiedPathName = makeQualifiedPath(workspaceFolder1, relPath);
                const blobName = workspaceManager.getBlobName(qualifiedPathName);
                expect(blobName).toBeDefined();
            }
        });

        test("folderRoot !== repoRoot", async () => {
            const initialState = [{ ...nestedWorkspaceFolder, tracked: true }];
            kit.setupWorkspace(initialState);
            const workspaceManager = kit.createWorkspaceManager();
            await awaitStartup(workspaceManager, initialState);
            for (const nestedRelPath of nestedWorkspaceFolder.files.keys()) {
                const absPath = joinPath(nestedWorkspaceFolder.folderRootUri.fsPath, nestedRelPath);
                const relPath = relativePathName(nestedWorkspaceFolder.repoRootUri.fsPath, absPath);
                const qualifiedPathName = makeQualifiedPath(nestedWorkspaceFolder, relPath);
                const blobName = workspaceManager.getBlobName(qualifiedPathName);
                expect(blobName).toBeDefined();
            }
        });
    });

    // Verify that the workspace manager correctly maps absolute path names to qualified path names.
    test("get qualified path name from absolute path", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);
        for (const relPath of workspaceFolder1.files.keys()) {
            const absPath = joinPath(workspaceFolder1.folderRootUri.fsPath, relPath);
            const qualifiedPathName = workspaceManager.resolvePathName(absPath);
            verifyDefined(qualifiedPathName);
            expect(qualifiedPathName.rootPath).toBe(workspaceFolder1.folderRootUri.fsPath);
            expect(qualifiedPathName.relPath).toBe(relPath);
        }
    });

    // Verify that the workspace manager returns undefined for qualified path names that are not
    // in the tracked source folder (the first test case) or in any tracked source folder (the
    // second test case).
    test.each(["/src/repo-root/not-a-file.py", "/not/a/repo-root/file1.py"])(
        "invalid qualified path name (%s)",
        async (absPathName: string) => {
            const initialState = [{ ...workspaceFolder1, tracked: true }];
            kit.setupWorkspace(initialState);
            const workspaceManager = kit.createWorkspaceManager();
            await awaitStartup(workspaceManager, initialState);
            const qualifiedPathName = workspaceManager.resolvePathName(absPathName);
            expect(qualifiedPathName).toBeUndefined();
        }
    );

    // Verify that the workspace manager correctly emits an event when a tracked source folder is
    // added to the workspace.
    test("tracked folder added event", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [
            { ...workspaceFolder1, tracked: true },
            { ...workspaceFolder2, tracked: true },
        ];
        const watcher = kit.createSourceFolderChangeWatcher(workspaceManager);
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);

        const expectedWatcherState = finalState.map((folderState) => {
            return { folderRoot: uriToAbsPath(folderState.folderRootUri) };
        });
        expect(watcher.sourceFolders).toEqual(expectedWatcherState);
    });

    // Verify that the workspace manager correctly emits an event when an untracked source folder is
    // added to the workspace.
    test("untracked folder added event", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [
            { ...workspaceFolder1, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
        ];
        const watcher = kit.createSourceFolderChangeWatcher(workspaceManager);
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);

        const expectedWatcherState = finalState
            .filter((folderState) => folderState.tracked)
            .map((folderState) => {
                return { folderRoot: uriToAbsPath(folderState.folderRootUri) };
            });
        expect(watcher.sourceFolders).toEqual(expectedWatcherState);
    });

    // Verify that the workspace manager correctly emits an event when a source folder is removed
    // from the workspace.
    test("folder removed event", async () => {
        const initialState = [
            { ...workspaceFolder1, tracked: true },
            { ...workspaceFolder2, tracked: true },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [{ ...workspaceFolder2, tracked: true }];
        const watcher = kit.createSourceFolderChangeWatcher(workspaceManager);
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);

        const expectedWatcherState = finalState.map((folderState) => {
            return { folderRoot: uriToAbsPath(folderState.folderRootUri) };
        });
        expect(watcher.sourceFolders).toEqual(expectedWatcherState);
    });

    // Verify that the workspace manager correctly cancels the creation of a source folder if an
    // enclosing source folder is added while the original (inner) source folder is in progress.
    test("cancel source folder creation", async () => {
        const findFileNameSpy = jest.spyOn(require("../../utils/path-utils"), "findFileName");
        try {
            // Arrange to delay the findFileName call for the nested source folder.
            const [delayPromise, cancel] = disposableDelayer();
            let waiting = false;
            findFileNameSpy.mockImplementation(async (rootPath: any, _fileName: any) => {
                if (typeof rootPath !== "string") {
                    throw new Error("rootPath is not a string");
                }
                const sourceFolder = allWorkspaceFolders.find((folder) => {
                    return folder.folderRootUri.fsPath === rootPath;
                });
                verifyDefined(sourceFolder);
                const repoRoot = sourceFolder.repoRootUri;
                if (rootPath === uriToAbsPath(nestedWorkspaceFolder.folderRootUri)) {
                    waiting = true;
                    await delayPromise;
                    waiting = false;
                }
                return repoRoot;
            });

            // Start with just the nested source folder. WorkspaceManager should start tracking it
            // because it is not (yet) nested inside any other source folder. However, because of
            // the above delay, it will pause waiting for the findFileName call to return.
            const initialState = [{ ...nestedWorkspaceFolder, tracked: true }];
            kit.setupWorkspace(initialState);
            const workspaceManager = kit.createWorkspaceManager();

            // Wait until the nested folder is in progress, then add the outer folder. This should
            // cause tracking of the nested folder to be cancelled, so only the outer folder should
            // be tracked.
            await advanceTimeUntilTrue(() => waiting);
            const finalState = [
                { ...workspaceFolder1, tracked: true },
                { ...nestedWorkspaceFolder, tracked: false },
            ];
            applyWorkspaceState(workspaceManager, finalState);
            await awaitWorkspaceState(workspaceManager, finalState);

            // Unblock the tracking of the nested folder. It should not actually complete
            cancel.dispose();
            verifyWorkspaceState(workspaceManager, finalState);

            // Wait a "long time" to (try to) make sure that the nested folder never gets tracked.
            let count = 0;
            await advanceTimeUntilTrue(() => count++ >= 100);
            verifyWorkspaceState(workspaceManager, finalState);
            expect(waiting).toBe(false);
        } finally {
            findFileNameSpy.mockRestore();
        }
    });
});

describe("enumeration state", () => {
    let makePathFilterSpy: jest.SpyInstance | undefined;
    let kit: WorkspaceManagerTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createWorkspaceFiles(allWorkspaceFolders);
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        makePathFilterSpy?.mockRestore();
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that the workspace manager reports the correct enumeration state for a source folder.
    test("enumeration state", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];

        // Arrange to delay the makePathFilter call until we dispose makePathFilterResume.
        const [makePathFilterDelay, makePathFilterResume] = disposableDelayer();
        const origMakePathFilter = pathIteratorModule.makePathFilter;
        makePathFilterSpy = jest.spyOn(pathIteratorModule, "makePathFilter");
        makePathFilterSpy.mockImplementation(
            async (
                startUri: vscode.Uri,
                rootUri: vscode.Uri,
                ignoreStackBuilder: IgnoreStackBuilder,
                fileExtensions: Set<string> | undefined
            ) => {
                await makePathFilterDelay;
                return origMakePathFilter(startUri, rootUri, ignoreStackBuilder, fileExtensions);
            }
        );

        // Begin startup, but don't await it... it will not complete until we dispose
        // makePathFilterResume.
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        const startupPromise = awaitStartup(workspaceManager, initialState);

        // Wait until the workspace manager has reported the source folder. It should be reported
        // as "in progress".
        let sourceFoldersBefore: Array<SourceFolderInfo>;
        await advanceTimeUntilTrue(() => {
            sourceFoldersBefore = workspaceManager.listSourceFolders();
            return sourceFoldersBefore.length >= 1;
        });
        const folderInfoBefore = sourceFoldersBefore![0];
        expect(
            folderInfoBefore.type === SourceFolderType.vscodeWorkspaceFolder ||
                folderInfoBefore.type === SourceFolderType.externalFolder
        ).toBe(true);
        if (
            folderInfoBefore.type === SourceFolderType.vscodeWorkspaceFolder ||
            folderInfoBefore.type === SourceFolderType.externalFolder
        ) {
            expect(folderInfoBefore.enumerationState).toBe(
                PublicSourceFolderEnumerationState.inProgress
            );
        }

        // Allow startup to resume and await its completion.
        makePathFilterResume.dispose();
        await startupPromise;

        // The source folder should now be reported as "complete".
        const sourceFoldersAfter = workspaceManager.listSourceFolders();
        const folderInfoAfter = sourceFoldersAfter[0];
        expect(
            folderInfoAfter.type === SourceFolderType.vscodeWorkspaceFolder ||
                folderInfoAfter.type === SourceFolderType.externalFolder
        );
        if (
            folderInfoAfter.type === SourceFolderType.vscodeWorkspaceFolder ||
            folderInfoAfter.type === SourceFolderType.externalFolder
        ) {
            expect(folderInfoAfter.enumerationState).toBe(
                PublicSourceFolderEnumerationState.complete
            );
        }
    });
});

describe("VCS tracking", () => {
    let kit: WorkspaceManagerTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createWorkspaceFiles(allWorkspaceFolders);
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("start tracking", async () => {
        const startTrackingSpy = jest.spyOn(VCSWatcherImpl.prototype, "startTracking");

        kit.configListener.config.vcs.watcherEnabled = true;
        kit.configListener.config.nextEdit.enabled = true;
        const initialState = [
            { ...workspaceFolder1, tracked: true },
            { ...workspaceFolder2, tracked: true },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        expect(workspaceManager.getVCSWatchedFolderIds()).toEqual([]);
        await awaitStartup(workspaceManager, initialState);

        expect(startTrackingSpy).toHaveBeenCalledTimes(2);
        expect(workspaceManager.getVCSWatchedFolderIds().length).toEqual(2);
    });
});

describe("Find repo root", () => {
    const workspaceFolderSameRepoRoot: MockSourceFolder = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder"),
        repoRootUri: vscode.Uri.file("/src/folder"),
        files: new Map<string, string>([
            ["file1.py", "this is file 1"],
            ["file2.py", "this is file 2"],
        ]),
    };

    const workspaceFolderDifferentRepoRoot: MockSourceFolder = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder/in/a/subdir"),
        repoRootUri: vscode.Uri.file("/src/folder"),
        files: new Map<string, string>([
            ["file1.py", "this is file 1"],
            ["file2.py", "this is file 2"],
        ]),
    };

    let kit: WorkspaceManagerTestKit;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("HeadChangeWatcher initialization independent of VCS watcher", async () => {
        createSourceFolderFiles(workspaceFolder1);
        createSourceFolderFiles(workspaceFolder2);
        const headChangeWatcherSpy = jest.spyOn(HeadChangeWatcher.prototype, "listenForChanges");

        // // Disable VCS watcher but ensure folders have VCS details
        kit.configListener.config.vcs.watcherEnabled = false;

        const initialState = [
            { ...workspaceFolder1, tracked: true },
            { ...workspaceFolder2, tracked: true },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        // HeadChangeWatcher should still be created for folders with vcsDetails
        expect(headChangeWatcherSpy).toHaveBeenCalled();
    });

    // Verify that the workspace manager correctly finds the repo root for a source folder,
    // whether it is the same as the folder root or above it, and whether it is identified with
    // a .augmentroot file or a .git directory.
    test.each([
        ["same repo root, .augmentroot", workspaceFolderSameRepoRoot, ".augmentroot"],
        ["same repo root, .git", workspaceFolderSameRepoRoot, ".git/"],
        ["different repo root, .augmentroot", workspaceFolderDifferentRepoRoot, ".augmentroot"],
        ["different repo root, .git", workspaceFolderDifferentRepoRoot, ".git/"],
    ])(
        "find repo root (%s)",
        async (_: string, sourceFolder: MockSourceFolder, sentinelFile: string) => {
            createSourceFolderFiles(sourceFolder, sentinelFile);
            const workspaceState = [{ ...sourceFolder, tracked: true }];
            kit.setupWorkspace(workspaceState);
            const workspaceManager = kit.createWorkspaceManager();
            await awaitStartup(workspaceManager, workspaceState);
            const expectedRepoRoot = uriToAbsPath(sourceFolder.repoRootUri);
            expect(
                workspaceManager.unitTestOnlyGetRepoRoot(sourceFolder.folderRootUri.fsPath)
            ).toBe(expectedRepoRoot);
        }
    );
});

// Verify that the workspace manager automatically reacts to changes to files in workspace folders.
describe("File changes in workspace folders", () => {
    const workspaceFolder = defaultWorkspaceFolder;
    const workspaceState = [{ ...workspaceFolder, tracked: true }];

    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createSourceFolderFiles(workspaceFolder);
        kit = new WorkspaceManagerTestKit();
        kit.setupWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that the workspace manager starts tracking a new file with an accepted path.
    // It should emit a file change event and 2 source folder change events for it (one when
    // it is added to the PathMap, and one when it is indexed).
    test("add accepted file", async () => {
        const relPath = "new-file.py";
        const fileChangeWatcher = kit.createFileChangeWatcher(workspaceManager);
        const folderChangedWatcher = kit.createFolderContentsChangeWatcher(workspaceManager);
        expect(folderChangedWatcher.count).toBe(0);
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        const blobName = kit.writeFile(workspaceFolder, relPath, "new file contents");
        await kit.awaitBlobName(workspaceManager, qualifiedPathName, blobName);
        expect(fileChangeWatcher.changedFiles.length).toBe(1);
        expect(fileChangeWatcher.changedFiles[0].relPath).toBe(relPath);
        expect(fileChangeWatcher.changedFiles[0].origin).toBe(FileChangeOrigin.disk);
        expect(folderChangedWatcher.count).toBe(2);
    });

    // Verify that the workspace manager does not start tracking a new file with a rejected path.
    // It should emit a folder change event, but not a file change event, for it.
    test("add rejected file", async () => {
        const relPath = "new-file.unsupported-path-extension";
        const fileChangeWatcher = kit.createFileChangeWatcher(workspaceManager);
        const folderChangedWatcher = kit.createFolderContentsChangeWatcher(workspaceManager);
        expect(folderChangedWatcher.count).toBe(0);
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        kit.writeFile(workspaceFolder, relPath, "new file contents");
        await waitMs(5000);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        expect(fileChangeWatcher.changedFiles.length).toBe(0);
        expect(folderChangedWatcher.count).toBe(1);
    });

    // Verify that the workspace manager updates its state after a file with an accepted path is
    // modified. It should emit a file change event but not a source folder change event.
    test("modify accepted file", async () => {
        const relPath = workspaceFolder.files.keys().next().value;
        verifyDefined(relPath);
        const fileChangeWatcher = kit.createFileChangeWatcher(workspaceManager);
        const folderChangedWatcher = kit.createFolderContentsChangeWatcher(workspaceManager);
        expect(folderChangedWatcher.count).toBe(0);
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        const origBlobName = kit.computeBlobName(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBe(origBlobName);
        const newBlobName = kit.writeFile(workspaceFolder, relPath, "new file contents");
        await kit.awaitBlobName(workspaceManager, qualifiedPathName, newBlobName);
        expect(fileChangeWatcher.changedFiles.length).toBe(1);
        expect(fileChangeWatcher.changedFiles[0].relPath).toBe(relPath);
        expect(fileChangeWatcher.changedFiles[0].origin).toBe(FileChangeOrigin.disk);

        // Modifying a file doesn't count as a folder change, so no source folder change event
        // should be emitted.
        expect(folderChangedWatcher.count).toBe(0);
    });

    // Verify that the workspace manager does not start tracking a modified file with a rejected
    // path. It should not emit any events for it.
    test("modify rejected file", async () => {
        // Add a file with a rejected path to the workspace folder.
        const relPath = "new-file.unsupported-path-extension";
        const fileChangeWatcher = kit.createFileChangeWatcher(workspaceManager);
        const folderChangedWatcher = kit.createFolderContentsChangeWatcher(workspaceManager);
        expect(folderChangedWatcher.count).toBe(0);
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        kit.writeFile(workspaceFolder, relPath, "original file contents");
        await waitMs(5000);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        expect(fileChangeWatcher.changedFiles.length).toBe(0);
        expect(folderChangedWatcher.count).toBe(1);

        // Reset the folder change watcher and modify the file. The file should still not be
        // tracked, and no events should have been emitted.
        folderChangedWatcher.count = 0;
        kit.writeFile(workspaceFolder, relPath, "new file contents");
        await waitMs(5000);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        expect(fileChangeWatcher.changedFiles.length).toBe(0);
        expect(folderChangedWatcher.count).toBe(0);
    });

    // Verify that the workspace manager updates its state after a file with an accepted path is
    // deleted. It should emit a file change event and a source folder change event.
    test("delete accepted file", async () => {
        const relPath = workspaceFolder.files.keys().next().value;
        verifyDefined(relPath);
        const fileChangeWatcher = kit.createFileChangeWatcher(workspaceManager);
        const folderChangedWatcher = kit.createFolderContentsChangeWatcher(workspaceManager);
        expect(folderChangedWatcher.count).toBe(0);
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeDefined();
        kit.deleteFile(workspaceFolder, relPath);
        await kit.awaitBlobName(workspaceManager, qualifiedPathName, undefined);
        expect(fileChangeWatcher.changedFiles.length).toBe(1);
        expect(fileChangeWatcher.changedFiles[0].relPath).toBe(relPath);
        expect(fileChangeWatcher.changedFiles[0].origin).toBe(FileChangeOrigin.disk);
        expect(folderChangedWatcher.count).toBe(1);
    });

    // Verify that the workspace manager does not emit a file change event when a file with a
    // rejected path is deleted, but does emit a folder change event.
    test("delete rejected file", async () => {
        // Add a file with a rejected path to the workspace folder.
        const relPath = "new-file.unsupported-path-extension";
        const fileChangeWatcher = kit.createFileChangeWatcher(workspaceManager);
        const folderChangedWatcher = kit.createFolderContentsChangeWatcher(workspaceManager);
        expect(folderChangedWatcher.count).toBe(0);
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        kit.writeFile(workspaceFolder, relPath, "new file contents");
        await waitMs(5000);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        expect(fileChangeWatcher.changedFiles.length).toBe(0);
        expect(folderChangedWatcher.count).toBe(1);

        // Reset the folder change watcher and delete the file. A folder change event should have
        // fired, but not a file change event.
        folderChangedWatcher.count = 0;
        kit.deleteFile(workspaceFolder, relPath);
        await waitMs(5000);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        expect(fileChangeWatcher.changedFiles.length).toBe(0);
        expect(folderChangedWatcher.count).toBe(1);
    });

    // Verify that WorkspaceManager (OpenFileManager, actually) starts tracking a file when it
    // is modified.
    test("start tracking on modification", async () => {
        const relPath = "new-file.py";
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        const origBlobName = kit.writeFile(workspaceFolder, relPath, "new file contents");
        await kit.awaitBlobName(workspaceManager, qualifiedPathName, origBlobName);

        const document = kit.openDocument(workspaceFolder, relPath);
        const editor = new TextEditor(document);
        publishActiveTextEditorChange(editor);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBe(origBlobName);

        const editEvent = document.insert(0, "hello");
        const updatedBlobName = kit.computeBlobName(workspaceFolder, relPath);
        publishTextDocumentChange(editEvent);
        await kit.awaitBlobName(workspaceManager, qualifiedPathName, updatedBlobName);
    });

    // Verify that WorkspaceManager doesn't track files with unsupported extensions.
    test("don't track files with unsupported extensions", async () => {
        const relPath = "new-file.unsupported-extension";
        const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
        kit.writeFile(workspaceFolder, relPath, "file contents");
        const document = kit.openDocument(workspaceFolder, relPath);
        const editor = new TextEditor(document);
        publishActiveTextEditorChange(editor);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();

        let count = 0;
        await advanceTimeUntilTrue(() => count++ >= 100);
        expect(workspaceManager.getBlobName(qualifiedPathName)).toBeUndefined();
    });
});

// Verify that the workspace manager incorporates changes to files in external folders upon refresh.
describe("File changes in external folders", () => {
    const workspaceFolderState: MockSourceFolderState = {
        sourceFolderType: MockSourceFolderType.externalFolder,
        folderRootUri: vscode.Uri.file("/src/folder1"),
        repoRootUri: vscode.Uri.file("/src/folder1"),
        files: new Map<string, string>([
            ["file1.py", "this is file 1"],
            ["file2.py", "this is file 2"],
        ]),
        ignoredFiles: new Map<string, string>([[".git/logs/HEAD", "head file"]]),
        tracked: true,
    };

    const workspaceState = [workspaceFolderState];

    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createSourceFolderFiles(workspaceFolderState);
        kit = new WorkspaceManagerTestKit();
        kit.setupWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that the workspace manager correctly emits an event when a file is added to a source
    // folder.
    test("refresh", async () => {
        const addedFile = "new-file.py";
        const modifiedFile = "file1.py";
        const removedFile = "file2.py";
        expect(workspaceFolderState.files.has(modifiedFile)).toBe(true);
        expect(workspaceFolderState.files.has(removedFile)).toBe(true);
        const folderChangedWatcher = kit.createFolderContentsChangeWatcher(workspaceManager);
        expect(folderChangedWatcher.count).toBe(0);

        const addedPathName = makeQualifiedPath(workspaceFolderState, addedFile);
        expect(workspaceManager.getBlobName(addedPathName)).toBeUndefined();
        const addedBlobName = kit.writeFile(workspaceFolderState, addedFile, "new file contents");

        const origBlobName = kit.computeBlobName(workspaceFolderState, modifiedFile);
        const modifiedPathName = makeQualifiedPath(workspaceFolderState, modifiedFile);
        expect(workspaceManager.getBlobName(modifiedPathName)).toBeDefined();
        const modifiedBlobName = kit.writeFile(workspaceFolderState, modifiedFile, "modified");

        const removedPathName = makeQualifiedPath(workspaceFolderState, removedFile);
        expect(workspaceManager.getBlobName(removedPathName)).toBeDefined();
        kit.deleteFile(workspaceFolderState, removedFile);

        // There should be no changes or events fired until we refresh.
        expect(workspaceManager.getBlobName(addedPathName)).toBeUndefined();
        expect(workspaceManager.getBlobName(modifiedPathName)).toBe(origBlobName);
        expect(workspaceManager.getBlobName(removedPathName)).toBeDefined();
        expect(folderChangedWatcher.count).toBe(0);

        workspaceManager.refreshSourceFolders();

        await kit.awaitBlobName(workspaceManager, addedPathName, addedBlobName);
        await kit.awaitBlobName(workspaceManager, modifiedPathName, modifiedBlobName);
        await kit.awaitBlobName(workspaceManager, removedPathName, undefined);

        // We should have received 3 events: one for adding addedFile, one for addedFile getting
        // indexed, and one for removing removedFile.
        expect(folderChangedWatcher.count).toBe(3);
    });
});

describe.each([false, true])("Checkpoints [%s]", (useCheckpointManagerContext) => {
    const blobsCheckpointThreshold = 20;

    const workspaceFolder: MockSourceFolder = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder"),
        repoRootUri: vscode.Uri.file("/src/folder"),
        files: new Map<string, string>(),
    };
    const workspaceState = [{ ...workspaceFolder, tracked: true }];

    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createSourceFolderFiles(workspaceFolder);
        kit = new WorkspaceManagerTestKit();
        kit.setupWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager({
            blobsCheckpointThreshold,
            useCheckpointManagerContext,
        });
        await awaitStartup(workspaceManager, workspaceState);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // verifyBlobsCheckpoint verifies that the given `blobs` has the correct contents. It also
    // verifies that the BlobsCheckpointManager correctly honors its checkpoint threshold.
    const veryifyBlobsCheckpoint = (
        blobs: Blobs,
        checkpointId: string | undefined,
        checkpointContents: Set<string>,
        indexedBlobs: Map<string, string>
    ) => {
        expect(blobs.checkpointId).toBe(checkpointId);
        expect(blobs.addedBlobs.length + blobs.deletedBlobs.length).toBeLessThanOrEqual(
            blobsCheckpointThreshold
        );
        const addSet = new Set(blobs.addedBlobs);
        const delSet = new Set(blobs.deletedBlobs);

        // Verify that addSet and delSet are disjoint.
        const union = new Set([...addSet, ...delSet]);
        expect(union.size).toBe(addSet.size + delSet.size);

        const blobSet = new Set(checkpointContents);
        blobs.addedBlobs.forEach((blobName) => {
            blobSet.add(blobName);
        });
        blobs.deletedBlobs.forEach((blobName) => {
            blobSet.delete(blobName);
        });

        expect(blobSet.size).toBe(indexedBlobs.size);
        const indexedBlobNames = new Set<string>(indexedBlobs.values());
        indexedBlobs.forEach((blobName) => {
            expect(indexedBlobNames.has(blobName)).toBe(true);
        });
    };

    test("checkpoint", async () => {
        const checkpointBlobsMock = jest.spyOn(kit.apiServer, "checkpointBlobs");

        let workspaceContext = workspaceManager.getContext();
        let lastCheckpointId = workspaceContext.blobs.checkpointId;
        let lastCheckpointContents = new Set<string>();

        let checkpointCount = 0;
        checkpointBlobsMock.mockImplementation(async (blobs: Blobs) => {
            expect(blobs.checkpointId).toBe(lastCheckpointId);
            blobs.addedBlobs.forEach((blobName) => {
                lastCheckpointContents.add(blobName);
            });
            blobs.deletedBlobs.forEach((blobName) => {
                lastCheckpointContents.delete(blobName);
            });
            const checkpointId = `checkpoint-${checkpointCount++}`;
            lastCheckpointId = checkpointId;
            return { newCheckpointId: checkpointId };
        });

        const indexedBlobs = new Map<string, string>();
        let blobIdx = 0;
        do {
            // Create a new file in the workspace
            const relPath = `sourcefile-${blobIdx++}.py`;
            const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
            const blobName = kit.writeFile(workspaceFolder, relPath, `contents of ${relPath}`);
            indexedBlobs.set(relPath, blobName);

            // Wait for the file's blob name to be recorded in the path map
            await kit.awaitBlobName(workspaceManager, qualifiedPathName, blobName);

            // If the new file triggered the creation of a new checkpoint, wait for the workspace
            // context to use it.
            await advanceTimeUntilTrue(() => {
                workspaceContext = workspaceManager.getContext();
                return workspaceContext.blobs.checkpointId === lastCheckpointId;
            });

            // Verify that the `blobs` of the workspace context exactly matches the set of indexed
            // blobs
            veryifyBlobsCheckpoint(
                workspaceContext.blobs,
                lastCheckpointId,
                lastCheckpointContents,
                indexedBlobs
            );
        } while (checkpointCount < 5);

        while (indexedBlobs.size > 0) {
            // Select a file to delete
            const relPath = indexedBlobs.keys().next().value;
            verifyDefined(relPath);
            indexedBlobs.delete(relPath);
            const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
            kit.deleteFile(workspaceFolder, relPath);
            indexedBlobs.delete(relPath);

            // Wait for the file's blob name to be removed from the path map
            await kit.awaitBlobName(workspaceManager, qualifiedPathName, undefined);

            // If the new file triggered the creation of a new checkpoint, wait for the workspace
            // context to use it.
            await advanceTimeUntilTrue(() => {
                workspaceContext = workspaceManager.getContext();
                return workspaceContext.blobs.checkpointId === lastCheckpointId;
            });

            // Verify that the `blobs` of the workspace context exactly matches the set of indexed
            // blobs
            veryifyBlobsCheckpoint(
                workspaceContext.blobs,
                lastCheckpointId,
                lastCheckpointContents,
                indexedBlobs
            );
        }
    });
});

describe("Refresh source folder", () => {
    let kit: WorkspaceManagerTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that a source folder refresh will incorporate changes to ignore files.
    test("refresh with changed ignore files", async () => {
        const workspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file("/src/folder1"),
            repoRootUri: vscode.Uri.file("/src/folder1"),
            files: new Map<string, string>([
                ["abc.py", "this is abc.py"],
                ["def.py", "this is def.py"],
                ["subdir/abc.py", "this is subdir/abc.py"],
                ["subdir/def.py", "this is subdir/def.py"],
                [".gitignore", "abc.py\n"],
            ]),
            tracked: true,
        };
        const workspaceState = [workspaceFolderState];

        kit.initWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);

        // The abc.py files should be excluded and the def.py files should be included.
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "abc.py")).toBe(false);
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "subdir/abc.py")).toBe(
            false
        );
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "def.py")).toBe(true);
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "subdir/def.py")).toBe(true);

        // Update .gitignore and refresh
        writeFileInFolder(workspaceFolderState.folderRootUri.fsPath, ".gitignore", "def.py\n");
        await advanceTimeUntilResolve(workspaceManager.refreshSourceFolders());
        await advanceTimeUntilTrue(() => {
            return (
                workspaceManager.unitTestOnlySourceFolderBacklog(
                    workspaceFolderState.folderRootUri.fsPath
                ) === 0
            );
        });

        // Now, the abc.py files should be included and the def.py files should be excluded.
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "abc.py")).toBe(true);
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "subdir/abc.py")).toBe(true);
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "def.py")).toBe(false);
        expect(pathHasBlobName(workspaceManager, workspaceFolderState, "subdir/def.py")).toBe(
            false
        );
    });

    // Verify that a source folder refresh will start/stop tracking files in response to changes
    // to ignore files.
    test("maintain OpenFileManager state", async () => {
        const workspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file("/src/folder1"),
            repoRootUri: vscode.Uri.file("/src/folder1"),
            files: new Map<string, string>([
                ["abc.py", "this is abc.py"],
                ["def.py", "this is def.py"],
                ["subdir/abc.py", "this is subdir/abc.py"],
                ["subdir/def.py", "this is subdir/def.py"],
                [".gitignore", "abc.py\n"],
            ]),
            tracked: true,
        };

        const workspaceState = [workspaceFolderState];
        kit.initWorkspace(workspaceState);

        const textDocuments = new Array<TextDocument>();
        textDocuments.push(
            kit.openDocument(workspaceFolderState, "abc.py"),
            kit.openDocument(workspaceFolderState, "def.py"),
            kit.openDocument(workspaceFolderState, "subdir/abc.py"),
            kit.openDocument(workspaceFolderState, "subdir/def.py")
        );
        setOpenTextDocuments(textDocuments);

        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);

        // The abc.py files should be excluded and the def.py files should be included.
        {
            const workspaceContext = workspaceManager.getContext();
            const trackedPaths = workspaceContext.trackedPaths.get(
                workspaceFolderState.folderRootUri.fsPath
            );
            verifyDefined(trackedPaths);
            expect(trackedPaths.has("abc.py")).toBe(false);
            expect(trackedPaths.has("subdir/abc.py")).toBe(false);
            expect(trackedPaths.has("def.py")).toBe(true);
            expect(trackedPaths.has("subdir/def.py")).toBe(true);
        }

        // Update .gitignore and refresh
        writeFileInFolder(workspaceFolderState.folderRootUri.fsPath, ".gitignore", "def.py\n");
        await advanceTimeUntilResolve(workspaceManager.refreshSourceFolders());
        await advanceTimeUntilTrue(() => {
            return (
                workspaceManager.unitTestOnlySourceFolderBacklog(
                    workspaceFolderState.folderRootUri.fsPath
                ) === 0
            );
        });

        // Now, the abc.py files should be included and the def.py files should be excluded.
        {
            const workspaceContext = workspaceManager.getContext();
            const trackedPaths = workspaceContext.trackedPaths.get(
                workspaceFolderState.folderRootUri.fsPath
            );
            verifyDefined(trackedPaths);
            expect(trackedPaths.has("abc.py")).toBe(true);
            expect(trackedPaths.has("subdir/abc.py")).toBe(true);
            expect(trackedPaths.has("def.py")).toBe(false);
            expect(trackedPaths.has("subdir/def.py")).toBe(false);
        }
    });
});

describe.each([false, true])("Joint context [%s]", (useCheckpointManagerContext) => {
    const workspaceFolder1: MockSourceFolderState = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder1"),
        repoRootUri: vscode.Uri.file("/src/folder1"),
        files: new Map<string, string>([
            ["file1.py", "this is file 1 of sourceFolder1"],
            ["file2.py", "this is file 2 of sourceFolder1"],
        ]),
        tracked: true,
    };

    const workspaceFolder2: MockSourceFolderState = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/proj/folder2"),
        repoRootUri: vscode.Uri.file("/proj/folder2"),
        files: new Map<string, string>([
            ["file1.py", "this is file 1 of sourceFolder2"],
            ["file2.py", "this is file 2 of sourceFolder2"],
        ]),
        tracked: true,
    };

    const workspaceState = [workspaceFolder1, workspaceFolder2];

    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        kit = new WorkspaceManagerTestKit();
        kit.initWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager({ useCheckpointManagerContext });
        await awaitStartup(workspaceManager, workspaceState);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that the workspace context contains all of the blob names from both source folders.
    test("joint context", () => {
        const workspaceContext = workspaceManager.getContextWithBlobNames();
        expect(workspaceContext.blobNames.length).toBe(4);
    });

    // Verify that the workspace context correctly handles non-unique blob names.
    test("shared blob name", async () => {
        // File name and contents that we will create in both source folders.
        const relPath = "shared.py";
        const sharedText = "shared text";

        // Create the file in workspaceFolder1. Verify that it makes its way into the context.
        const qualifiedPathName1 = makeQualifiedPath(workspaceFolder1, relPath);
        const blobName = kit.writeFile(workspaceFolder1, relPath, sharedText);
        await kit.awaitBlobName(workspaceManager, qualifiedPathName1, blobName);
        const context1 = workspaceManager.getContextWithBlobNames();
        expect(context1.blobNames.length).toBe(5);

        // Create the file in workspaceFolder2. Verify that it the context still has it
        const qualifiedPathName2 = makeQualifiedPath(workspaceFolder2, relPath);
        const verifyBlobName = kit.writeFile(workspaceFolder2, relPath, sharedText);
        expect(verifyBlobName).toBe(blobName);
        await kit.awaitBlobName(workspaceManager, qualifiedPathName2, blobName);
        const context2 = workspaceManager.getContextWithBlobNames();
        expect(context2.blobNames.length).toBe(5);

        // Delete the file from workspaceFolder1. Verify that the context still has it (since
        // it is still in workspaceFolder2)
        kit.deleteFile(workspaceFolder1, relPath);
        await kit.awaitBlobName(workspaceManager, qualifiedPathName1, undefined);
        const context3 = workspaceManager.getContextWithBlobNames();
        expect(context3.blobNames.length).toBe(5);

        // Delete the file from workspaceFolder2. Verify that the context no longer has it.
        kit.deleteFile(workspaceFolder2, relPath);
        await kit.awaitBlobName(workspaceManager, qualifiedPathName2, undefined);
        const context4 = workspaceManager.getContextWithBlobNames();
        expect(context4.blobNames.length).toBe(4);
    });
});

describe("Missing blobs", () => {
    const workspaceFolder = defaultWorkspaceFolder;
    const workspaceState = [{ ...workspaceFolder, tracked: true }];

    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createSourceFolderFiles(workspaceFolder);
        kit = new WorkspaceManagerTestKit();
        kit.setupWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that the workspace manager uploads blobs that are reported as unknown.
    test("missing blob", async () => {
        // Verfy that the api server has all the blob names in the context, then drop them from
        // the api server's cache.
        const uploadedBlobNames = kit.apiServer.getMemoryNames();
        expect(uploadedBlobNames.length).toBeGreaterThan(0);
        expect(uploadedBlobNames.length).toBe(workspaceFolder.files.size);
        kit.apiServer.forget();
        expect(kit.apiServer.getMemoryNames().length).toBe(0);

        // Report the blob names as unknown and wait until they have all been re-uploaded to the
        // ApiServer.
        const workspaceContext = workspaceManager.getContext();
        workspaceManager.handleUnknownBlobs(workspaceContext, uploadedBlobNames);
        await awaitWorkspaceState(workspaceManager, workspaceState);
        let reUploadedBlobNames: Array<string>;
        await advanceTimeUntilTrue(() => {
            reUploadedBlobNames = kit.apiServer.getMemoryNames();
            return reUploadedBlobNames.length === uploadedBlobNames.length;
        });

        // Ensure that the current set of blob names matches the original set.
        const origNames = new Set<string>(uploadedBlobNames);
        const currentNames = new Set<string>(reUploadedBlobNames!);

        expect(currentNames.size).toBe(origNames.size);
        for (const blobName of uploadedBlobNames) {
            expect(currentNames.has(blobName)).toBe(true);
        }
    });
});

describe("External folders", () => {
    let kit: WorkspaceManagerTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createWorkspaceFiles(allWorkspaceFolders);
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Test startup of a single-folder workspace.
    test("startup single external folder", async () => {
        const workspaceState = [{ ...externalFolder1, tracked: true }];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    test("startup multiple external folders", async () => {
        const workspaceState = [
            { ...externalFolder1, tracked: true },
            { ...externalFolder2, tracked: true },
        ];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    test("startup with nested folders", async () => {
        const workspaceState = [
            { ...workspaceFolder1, tracked: true },
            { ...workspaceFolder2, tracked: true },
            { ...nestedWorkspaceFolder, tracked: false },
            { ...externalFolder1, tracked: true },
            { ...externalFolder2, tracked: true },
            { ...nestedExternalFolder, tracked: false },
            { ...externalFolderNestedinWorkspaceFolder, tracked: false },
            { ...workspaceFolderNestedinExternalFolder, tracked: false },
        ];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });

    test("add external folder", async () => {
        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = initialState.concat([{ ...externalFolder1, tracked: true }]);
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });

    test("remove external folder", async () => {
        const initialState = [
            { ...workspaceFolder1, tracked: true },
            { ...externalFolder1, tracked: true },
        ];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = [{ ...workspaceFolder1, tracked: true }];
        applyWorkspaceState(workspaceManager, finalState);
        await awaitWorkspaceState(workspaceManager, finalState);
    });
});

type FileInfo = [
    string, // folderRoot
    string, // relPath
    FileType, // type
    boolean, // included
    number | undefined, // trackedFileCount
    boolean, // containsExcludedItems
];

describe("Source folder contents", () => {
    const workspaceFolder: MockSourceFolder = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder1"),
        repoRootUri: vscode.Uri.file("/src/folder1"),
        files: new Map<string, string>([
            ["giraffe.py", "xxx"],
            ["dog.py", "xxx"],
            ["whale/pigeon.py", "xxx"],
            ["whale/goldfish.py", "xxx"],
            ["whale/tiger/hornet.py", "xxx"],
            ["whale/tiger/sloth.py", "xxx"],
            ["walrus/sloth/mouse.py", "xxx"],
            ["sloth/elephant/rabbit.py", "xxx"],
            ["bug/cat/dog/elephant/fish/zebra.py", "xxx"],
            [".gitignore", "sloth*\n"],
        ]),
        ignoredFiles: new Map<string, string>([[".git/logs/HEAD", "head file"]]),
    };

    const allDirsFolderInfo: TrackedSourceFolderInfo = {
        type: PublicSourceFolderType.vscodeWorkspaceFolder,
        name: "/src/folder1",
        folderRoot: "/src/folder1",
        syncingEnabled: true,
        trackedFileCount: 6,
        containsExcludedItems: true,
        containsUnindexedItems: false,
        enumerationState: PublicSourceFolderEnumerationState.complete,
    };

    // allDirs is a map containing the expected contents of every directory in `workspaceFolder`.
    const allDirs = new Map<string, Map<string, FileInfo>>([
        [
            "",
            new Map<string, FileInfo>([
                [
                    ".augmentroot",
                    ["/src/folder1", ".augmentroot", FileType.file, false, undefined, false],
                ],
                [".git", ["/src/folder1", ".git", FileType.directory, false, 0, false]],
                [
                    ".gitignore",
                    ["/src/folder1", ".gitignore", FileType.file, false, undefined, false],
                ],
                [
                    "giraffe.py",
                    ["/src/folder1", "giraffe.py", FileType.file, true, undefined, false],
                ],
                ["dog.py", ["/src/folder1", "dog.py", FileType.file, true, undefined, false]],
                ["whale", ["/src/folder1", "whale", FileType.directory, true, 3, true]],
                ["walrus", ["/src/folder1", "walrus", FileType.directory, true, 0, true]],
                ["sloth", ["/src/folder1", "sloth", FileType.directory, false, 0, false]],
                ["bug", ["/src/folder1", "bug", FileType.directory, true, 1, false]],
            ]),
        ],
        [
            "whale",
            new Map<string, FileInfo>([
                [
                    "pigeon.py",
                    ["/src/folder1", "whale/pigeon.py", FileType.file, true, undefined, false],
                ],
                [
                    "goldfish.py",
                    ["/src/folder1", "whale/goldfish.py", FileType.file, true, undefined, false],
                ],
                ["tiger", ["/src/folder1", "whale/tiger", FileType.directory, true, 1, true]],
            ]),
        ],
        [
            "whale/tiger",
            new Map<string, FileInfo>([
                [
                    "hornet.py",
                    [
                        "/src/folder1",
                        "whale/tiger/hornet.py",
                        FileType.file,
                        true,
                        undefined,
                        false,
                    ],
                ],
                [
                    "sloth.py",
                    [
                        "/src/folder1",
                        "whale/tiger/sloth.py",
                        FileType.file,
                        false,
                        undefined,
                        false,
                    ],
                ],
            ]),
        ],
        [
            "walrus",
            new Map<string, FileInfo>([
                ["sloth", ["/src/folder1", "walrus/sloth", FileType.directory, false, 0, false]],
            ]),
        ],
        ["walrus/sloth", new Map<string, FileInfo>()],
        ["sloth", new Map<string, FileInfo>()],
        [
            "bug",
            new Map<string, FileInfo>([
                ["cat", ["/src/folder1", "bug/cat", FileType.directory, true, 1, false]],
            ]),
        ],
        [
            "bug/cat",
            new Map<string, FileInfo>([
                ["dog", ["/src/folder1", "bug/cat/dog", FileType.directory, true, 1, false]],
            ]),
        ],
        [
            "bug/cat/dog",
            new Map<string, FileInfo>([
                [
                    "elephant",
                    ["/src/folder1", "bug/cat/dog/elephant", FileType.directory, true, 1, false],
                ],
            ]),
        ],
        [
            "bug/cat/dog/elephant",
            new Map<string, FileInfo>([
                [
                    "fish",
                    [
                        "/src/folder1",
                        "bug/cat/dog/elephant/fish",
                        FileType.directory,
                        true,
                        1,
                        false,
                    ],
                ],
            ]),
        ],
        [
            "bug/cat/dog/elephant/fish",
            new Map<string, FileInfo>([
                [
                    "zebra.py",
                    [
                        "/src/folder1",
                        "bug/cat/dog/elephant/fish/zebra.py",
                        FileType.file,
                        true,
                        undefined,
                        false,
                    ],
                ],
            ]),
        ],
    ]);

    const whaleSubFolder = {
        ...workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder1/whale"),
    };

    const whaleSubFolderInfo: TrackedSourceFolderInfo = {
        type: PublicSourceFolderType.vscodeWorkspaceFolder,
        name: "/src/folder1/whale",
        folderRoot: "/src/folder1/whale",
        syncingEnabled: true,
        trackedFileCount: 3,
        containsExcludedItems: true,
        containsUnindexedItems: false,
        enumerationState: PublicSourceFolderEnumerationState.complete,
    };

    const whaleDirs = new Map<string, Map<string, FileInfo>>([
        [
            "",
            new Map<string, FileInfo>([
                [
                    "pigeon.py",
                    ["/src/folder1/whale", "pigeon.py", FileType.file, true, undefined, false],
                ],
                [
                    "goldfish.py",
                    ["/src/folder1/whale", "goldfish.py", FileType.file, true, undefined, false],
                ],
                ["tiger", ["/src/folder1/whale", "tiger", FileType.directory, true, 1, true]],
            ]),
        ],
        [
            "tiger",
            new Map<string, FileInfo>([
                [
                    "hornet.py",
                    [
                        "/src/folder1/whale",
                        "tiger/hornet.py",
                        FileType.file,
                        true,
                        undefined,
                        false,
                    ],
                ],
                [
                    "sloth.py",
                    [
                        "/src/folder1/whale",
                        "tiger/sloth.py",
                        FileType.file,
                        false,
                        undefined,
                        false,
                    ],
                ],
            ]),
        ],
    ]);

    let kit: WorkspaceManagerTestKit;

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createWorkspaceFiles([workspaceFolder]);
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    const verifyFolderListing = (
        workspaceManager: WorkspaceManager,
        expected: TrackedSourceFolderInfo
    ) => {
        const sourceFolders = workspaceManager.listSourceFolders();
        if (sourceFolders.length !== 1) {
            throw new Error(`expected exactly one source folder, got ${sourceFolders.length}`);
        }
        expect(sourceFolders[0]).toStrictEqual(expected);
    };

    // verifyFolderContents is a function that verifies that the reported contents of the given
    // folder match the given expected contents.
    const verifyFolderContents = (
        workspaceManager: WorkspaceManager,
        folderRoot: string,
        subdirPath: string,
        expected: Map<string, FileInfo>
    ) => {
        const contents = new Map<string, SourceFolderItem>(
            Array.from(
                workspaceManager
                    .listChildren(folderRoot, subdirPath)
                    .map((item) => [item.name, item])
            )
        );
        expect(contents.size).toBe(expected.size);
        for (const [
            name,
            [folderRoot, relPath, type, included, trackedFileCount, containsExcl],
        ] of expected) {
            const item = contents.get(name);
            verifyDefined(item);
            expect(item.folderRoot).toBe(folderRoot);
            expect(item.relPath).toBe(relPath);
            expect(item.type).toBe(type);
            expect(item.included).toBe(included);
            if (item.type === FileType.directory) {
                expect(item.trackedFileCount).toBe(trackedFileCount);
                expect(item.containsExcludedItems).toBe(containsExcl);
            }
        }
    };

    test.each([
        ["all", workspaceFolder, allDirsFolderInfo, allDirs],
        ["subfolder", whaleSubFolder, whaleSubFolderInfo, whaleDirs],
    ])("list folder contents(%s)", async (_name, sourceFolder, sourceFolderInfo, dirs) => {
        const workspaceState = [{ ...sourceFolder, tracked: true }];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);

        verifyFolderListing(workspaceManager, sourceFolderInfo);

        for (const [subdir, expected] of dirs) {
            verifyFolderContents(
                workspaceManager,
                uriToAbsPath(sourceFolder.folderRootUri),
                subdir,
                expected
            );
        }
    });
});

describe("Report indexing state", () => {
    const workspaceFolder: MockSourceFolderState = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder1"),
        repoRootUri: vscode.Uri.file("/src/folder1"),
        files: new Map<string, string>([
            ["file0.py", "file0"],
            ["file1.py", "file1"],
            ["file2.py", "file2"],
            ["file3.py", "file3"],
            ["file4.py", "file4"],
            ["file5.py", "file5"],
            ["file6.py", "file6"],
            ["file7.py", "file7"],
            ["file8.py", "file8"],
            ["file9.py", "file9"],
        ]),
        tracked: true,
    };

    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createSourceFolderFiles(workspaceFolder);
        kit = new WorkspaceManagerTestKit();
        kit.setupWorkspace([workspaceFolder]);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that the workspace manager correctly fires onDidChangeSourceFolderContents when a
    // blob gets indexed, and that listChildren correctly reports the indexing state of each path
    // name.
    test("report indexing state", async () => {
        /* This test is almost all setup. The actual body of the test (at the bottom) is very
         * small.
         *
         * The point of this test is to verify that as blobs get indexed during WorkspaceManager
         * startup, the onDidChangeSourceFolderContents event is fired as each blob gets indexed,
         * and that listChildren correctly reports the indexed state of each file.
         *
         * There are three parts to the test setup:
         * 1. Create a mock of apiServer.batchUpload that keeps track of uploaded files and blob
         *    names.
         * 2. Create a mock of apiServer.findMissing that allows us to control when each blob is
         *    indexed.
         * 3. Create a listener of WorkspaceManager.onDidChangeSourceFolderContents that counts the
         *    number of events it receives, to verify that each file triggers an event. It also
         *    calls WorkspaceManager.listChildren (much like a real listener would do) to verify
         *    that it correctly reports the indexed state of each path name.
         *
         * The test itself just waits for all files to be uploaded, then waits for all files to be
         * indexed, and then verifies that the correct number of events were received.
         */

        const folderRoot = uriToAbsPath(workspaceFolder.folderRootUri);
        const fileCount = workspaceFolder.files.size;
        const uploadedBlobNames = new Map<string, string>();
        const indexedPathNames = new Set<string>();
        let indexNextBlob = false;

        // Setup step 1: Mock batchUpload
        kit.apiServer.batchUpload = jest.fn(
            async (blobs: Array<UploadBlob>): Promise<BatchUploadResult> => {
                blobs.forEach((blob) => {
                    uploadedBlobNames.set(blob.blobName, blob.pathName);
                });
                return {
                    blobNames: blobs.map((blob) => blob.blobName),
                };
            }
        );

        // Setup step 2: Mock findMissing
        kit.apiServer.findMissing = jest.fn(
            async (blobNames: string[]): Promise<FindMissingResult> => {
                if (uploadedBlobNames.size < fileCount) {
                    // Some files haven't been uploaded yet. Just return the unknown blobs (no indexing
                    // yet).)
                    const unknownBlobNames = blobNames.filter((blobName) => {
                        return !uploadedBlobNames.has(blobName);
                    });
                    return {
                        unknownBlobNames,
                        nonindexedBlobNames: [],
                    };
                }

                if (!indexNextBlob) {
                    // The test hasn't consumed the last blob we indexed. Wait until it does.
                    return {
                        unknownBlobNames: [],
                        nonindexedBlobNames: blobNames,
                    };
                }

                // Index the first blob in the list.
                expect(blobNames.length).toBeGreaterThan(0);
                const blobToIndex = blobNames[0];
                const pathName = uploadedBlobNames.get(blobToIndex);
                verifyDefined(pathName);
                expect(indexedPathNames.has(blobToIndex)).toBe(false);
                indexedPathNames.add(pathName);
                indexNextBlob = false;
                return {
                    unknownBlobNames: [],
                    nonindexedBlobNames: blobNames.slice(1),
                };
            }
        );

        // Setup step 3: Create a listener of onDidChangeSourceFolderContents
        let eventCount = 0;
        workspaceManager = kit.createWorkspaceManager();
        kit.addDisposable(
            workspaceManager.onDidChangeSourceFolderContents(() => {
                expect(indexNextBlob).toBe(false);

                // List all files in the folder and verify that each file is correctly reported
                // as indexed or not.
                const paths = workspaceManager.listChildren(folderRoot, "");
                const pathsOfInterest = paths.filter((path) => path.name.startsWith("file"));
                expect(pathsOfInterest.length).toBe(fileCount);
                for (const path of pathsOfInterest) {
                    expect(path.included).toBe(true);
                    expect(path.type).toBe(FileType.file);
                    if (path.type !== FileType.file) {
                        continue;
                    }
                    expect(path.indexed).toBe(indexedPathNames.has(path.name));
                }

                // Allow the next blob to be indexed.
                indexNextBlob = true;
                eventCount++;
            })
        );

        /*
         * Setup done. Test starts here.
         */

        // Wait for all files to be uploaded and indicate that we are ready to start indexing.
        await advanceTimeUntilTrue(() => uploadedBlobNames.size === fileCount);
        indexNextBlob = true;

        // Wait until all files have been indexed. Each one should have triggered an event
        await advanceTimeUntilTrue(() => indexedPathNames.size === uploadedBlobNames.size);
        expect(eventCount).toBe(fileCount);
    });
});

// These tests verify that the WorkspaceManager correctly reports state and statistics about
// workspace syncing via getSyncingProgress and onDidChangeSyncingProgress.
describe("Syncing state", () => {
    const workspaceFolder1: MockSourceFolderState = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/folder1"),
        repoRootUri: vscode.Uri.file("/src/folder1"),
        files: new Map<string, string>([
            ["file1.py", "file1"],
            ["file2.py", "file2"],
            ["dir1/file3.py", "file3"],
            ["dir1/file4.py", "file4"],
            ["dir1/dir2/file5.py", "file5"],
            ["dir1/dir2/file6.py", "file6"],
            ["empty-file.py", ""],
        ]),
        tracked: true,
    };

    const workspaceFolder2: MockSourceFolderState = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/proj/folder2"),
        repoRootUri: vscode.Uri.file("/proj/folder2"),
        files: new Map<string, string>([
            ["file10.py", "file10"],
            ["file11.py", "file11"],
            ["dir10/file12.py", "file12"],
            ["dir10/file13.py", "file13"],
            ["dir10/dir11/file14.py", "file14"],
            ["dir10/dir11/file15.py", "file15"],
        ]),
        tracked: true,
    };

    let makePathFilterSpy: jest.SpyInstance | undefined;
    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        makePathFilterSpy?.mockRestore();
        kit.dispose();
        jest.useRealTimers();
    });

    // Verify that the syncing state is correctly reported during WorkspaceManager startup.
    test("Startup syncing state", async () => {
        const workspaceState = [workspaceFolder1, workspaceFolder2];
        kit.initWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();

        const progressMap = new Map<
            string,
            { trackedFiles: number; backlogSize: number | undefined }
        >();
        for (const folderState of workspaceState) {
            progressMap.set(uriToAbsPath(folderState.folderRootUri), {
                trackedFiles: folderState.files.size,
                backlogSize: undefined,
            });
        }

        kit.addDisposable(
            workspaceManager.onDidChangeSyncingProgress((event: SourceFolderSyncingProgress) => {
                // Ignore the last syncing progress that gets emitted when the folder has
                // finished syncing and changes its newlyTracked state.
                if (event.progress?.newlyTracked === false) {
                    return;
                }
                const folderInfo = progressMap.get(event.folderRoot);
                verifyDefined(folderInfo);
                if (event.progress === undefined) {
                    expect(folderInfo.backlogSize).toBeUndefined();
                    return;
                }

                expect(event.progress.newlyTracked).toBe(true);
                expect(event.progress.trackedFiles).toBe(folderInfo.trackedFiles);
                if (folderInfo.backlogSize === undefined) {
                    expect(event.progress.backlogSize).toBe(folderInfo.trackedFiles);
                } else if (event.progress.backlogSize !== folderInfo.backlogSize) {
                    // The backlog size should either be the same as last time or one less.
                    expect(event.progress.backlogSize).toBe(folderInfo.backlogSize - 1);
                }
                folderInfo.backlogSize = event.progress.backlogSize;
            })
        );

        let syncingProgress: Array<SourceFolderSyncingProgress> = [];
        await advanceTimeUntilTrue(() => {
            syncingProgress = workspaceManager.getSyncingProgress();
            return syncingProgress.length === workspaceState.length;
        });
        let reportedFolders = new Set<string>(syncingProgress.map((item) => item.folderRoot));
        expect(reportedFolders.size).toBe(progressMap.size);
        progressMap.forEach((_, folderRoot) => {
            expect(reportedFolders).toContain(folderRoot);
        });

        await awaitStartup(workspaceManager, workspaceState);
        progressMap.forEach((item) => {
            expect(item.backlogSize).toBe(0);
        });

        syncingProgress = workspaceManager.getSyncingProgress();
        reportedFolders = new Set<string>(syncingProgress.map((item) => item.folderRoot));
        expect(reportedFolders.size).toBe(progressMap.size);
        syncingProgress.forEach((progressItem) => {
            verifyDefined(progressItem.progress);
            const folderInfo = progressMap.get(progressItem.folderRoot);
            verifyDefined(folderInfo);
            expect(progressItem.progress.newlyTracked).toBe(false);
            expect(progressItem.progress.trackedFiles).toBe(folderInfo.trackedFiles);
            expect(progressItem.progress.backlogSize).toBe(0);
        });
    });

    // Verify that the syncing state is correctly reported when new files are added to a workspace.
    test("maintain syncing state", async () => {
        const workspaceState = [workspaceFolder1];
        kit.initWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();

        const originalFileCount = workspaceFolder1.files.size;
        await awaitStartup(workspaceManager, workspaceState);
        let syncingProgress = workspaceManager.getSyncingProgress();
        expect(syncingProgress.length).toBe(1);
        let progressItem = syncingProgress[0];
        expect(progressItem.folderRoot).toBe(uriToAbsPath(workspaceFolder1.folderRootUri));
        verifyDefined(progressItem.progress);
        expect(progressItem.progress.newlyTracked).toBe(false);
        expect(progressItem.progress.trackedFiles).toBe(originalFileCount);
        expect(progressItem.progress.backlogSize).toBe(0);

        let newFilesCreated = 0;
        let notifications = 0;
        kit.addDisposable(
            workspaceManager.onDidChangeSyncingProgress((event: SourceFolderSyncingProgress) => {
                verifyDefined(event.progress);
                expect(event.folderRoot).toBe(uriToAbsPath(workspaceFolder1.folderRootUri));
                expect(event.progress.newlyTracked).toBe(false);
                expect(event.progress.trackedFiles).toBe(originalFileCount + newFilesCreated);
                notifications++;
            })
        );
        const newFilesToCreate = 10;
        for (let i = 0; i < newFilesToCreate; i++) {
            newFilesCreated++;
            writeFileInFolder(
                uriToAbsPath(workspaceFolder1.folderRootUri),
                `new-file${i}.py`,
                `blah`
            );
        }
        expect(notifications).toBe(newFilesToCreate);
        advanceTimeUntilTrue(() => {
            syncingProgress = workspaceManager.getSyncingProgress();
            return syncingProgress.every((progressItem) => {
                return progressItem.progress?.backlogSize === 0;
            });
        });
        expect(syncingProgress.length).toBe(1);
        progressItem = syncingProgress[0];
        expect(progressItem.folderRoot).toBe(uriToAbsPath(workspaceFolder1.folderRootUri));
        verifyDefined(progressItem.progress);
        if (progressItem.folderRoot === uriToAbsPath(workspaceFolder1.folderRootUri)) {
            expect(progressItem.progress.trackedFiles).toBe(originalFileCount + newFilesToCreate);
        } else {
            expect(progressItem.progress.trackedFiles).toBe(workspaceFolder2.files.size);
        }
        expect(progressItem.progress.newlyTracked).toBe(false);
    });

    // Verify that awaitInitialFoldersEnumerated and awaitInitialFoldersSynced correctly wait
    // for their respective conditions to be met.
    test("await initial folders enumerated", async () => {
        const workspaceState = [workspaceFolder1, workspaceFolder2];
        kit.initWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();

        expect(workspaceManager.initialFoldersEnumerated).toBe(false);
        let syncingProgress = workspaceManager.getSyncingProgress();
        if (syncingProgress.length === workspaceState.length) {
            expect(
                syncingProgress.every((progressItem) => progressItem.progress === undefined)
            ).toBe(true);
        }

        await advanceTimeUntilResolve(workspaceManager.awaitInitialFoldersEnumerated());
        expect(workspaceManager.initialFoldersEnumerated).toBe(true);
        syncingProgress = workspaceManager.getSyncingProgress();
        expect(syncingProgress.length).toBe(workspaceState.length);
        syncingProgress.forEach((progressItem) => {
            verifyDefined(progressItem.progress);
        });

        await advanceTimeUntilResolve(workspaceManager.awaitInitialFoldersSynced());
        expect(workspaceManager.initialFoldersEnumerated).toBe(true);
        expect(workspaceManager.initialFoldersSynced).toBe(true);
        syncingProgress = workspaceManager.getSyncingProgress();
        expect(syncingProgress.length).toBe(workspaceState.length);
        syncingProgress.forEach((progressItem) => {
            verifyDefined(progressItem.progress);
            expect(progressItem.progress.backlogSize).toBe(0);
        });
    });

    // Verify that the reported backlogSize <= trackedFiles.
    test("backlog <= trackedFiles", async () => {
        const workspaceState = [workspaceFolder1, workspaceFolder2];
        kit.initWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();

        expect(workspaceManager.initialFoldersEnumerated).toBe(false);
        let syncingProgress = workspaceManager.getSyncingProgress();
        if (syncingProgress.length === workspaceState.length) {
            expect(
                syncingProgress.every((progressItem) => progressItem.progress === undefined)
            ).toBe(true);
        }

        let lastProgressDefined = false;
        let eventCount = 0;
        const disp = workspaceManager.onDidChangeSyncingProgress(
            (event: SourceFolderSyncingProgress) => {
                eventCount++;
                if (event.progress === undefined) {
                    expect(lastProgressDefined).toBe(false);
                }
                lastProgressDefined = event.progress !== undefined;
                if (event.progress !== undefined) {
                    expect(event.progress.backlogSize).toBeLessThanOrEqual(
                        event.progress.trackedFiles
                    );
                }
            }
        );

        await awaitStartup(workspaceManager, workspaceState);
        expect(eventCount).toBeGreaterThan(0);
        disp.dispose();
    });
});

describe("syncing permissions", () => {
    const syncingPermissionNeeded = DerivedStateName.syncingPermissionNeeded;
    let kit: WorkspaceManagerTestKit;

    const workspaceState = [
        { ...workspaceFolder1, tracked: true },
        { ...workspaceFolder2, tracked: true },
    ];

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        createWorkspaceFiles(workspaceState);
        kit = new WorkspaceManagerTestKit({
            syncingPermissionTracker: new MockSyncingPermissionTracker(),
        });
        kit.initWorkspace(workspaceState);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test("assert syncing permission needed if no permissions exist", async () => {
        const currentFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...currentFlags,
            enableFileLimitsForSyncingPermission: true,
            minUploadedPercentageWithoutPermission: 90,
        });

        (kit.syncingPermissionTracker as MockSyncingPermissionTracker).clearPermission();
        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        const workspaceManager = kit.createWorkspaceManager();
        const startupPromise = awaitStartup(workspaceManager, workspaceState);

        await advanceTimeUntilTrue(() => {
            return actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded);
        });
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(true);

        workspaceManager.enableSyncing();
        await startupPromise;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.enabled);
        expect(workspaceManager.listSourceFolders().length).toBe(2);
    });

    test("don't assert syncing permission needed if permission granted", async () => {
        const allFolderRoots = workspaceState.map((folder) => uriToAbsPath(folder.folderRootUri));
        kit.syncingPermissionTracker.setPermittedFolders(allFolderRoots);
        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        const workspaceManager = kit.createWorkspaceManager();
        const startupPromise = awaitStartup(workspaceManager, workspaceState);

        await waitMs(1000);
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        await startupPromise;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.enabled);
        expect(workspaceManager.listSourceFolders().length).toBe(2);
    });

    test("assert syncing permission needed if permissions denied", async () => {
        kit.syncingPermissionTracker.denyPermission();
        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        const workspaceManager = kit.createWorkspaceManager();
        const startupPromise = awaitStartup(workspaceManager, []);

        await waitMs(1000);
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        await startupPromise;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.disabled);
        expect(workspaceManager.listSourceFolders().length).toBe(0);
    });

    test("don't assert syncing permission needed if mtime cache exists", async () => {
        workspaceState.forEach((folder) => {
            const cacheDirPath = WorkspaceManager.computeCacheDirPath(
                uriToAbsPath(folder.folderRootUri),
                WorkspaceManagerTestKit.storageUri
            );
            const mtimeCacheFileName = makeMtimeCacheFileName(cacheDirPath);
            mockFSUtils.writeFileUtf8(mtimeCacheFileName, "{}", true);
        });
        const actionsModel = kit.actionsModel;

        const workspaceManager = kit.createWorkspaceManager();
        const startupPromise = awaitStartup(workspaceManager, workspaceState);

        await waitMs(1000);
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        await startupPromise;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.enabled);
        expect(workspaceManager.listSourceFolders().length).toBe(2);
    });

    test("stop syncing when disabled", async () => {
        const allFolderRoots = workspaceState.map((folder) => uriToAbsPath(folder.folderRootUri));
        kit.syncingPermissionTracker.setPermittedFolders(allFolderRoots);
        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.enabled);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.enabled);
        expect(workspaceManager.listSourceFolders().length).toBe(2);

        await kit.disableSyncing();
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.disabled);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.disabled);
        expect(workspaceManager.listSourceFolders().length).toBe(0);

        await drainWorkspaceBacklog(workspaceManager);
    });

    test("start syncing when enabled", async () => {
        kit.syncingPermissionTracker.denyPermission();
        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);

        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, []);
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.disabled);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.disabled);
        expect(workspaceManager.listSourceFolders().length).toBe(0);

        await kit.enableSyncing();
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.enabled);
        expect(workspaceManager.syncingEnabledState).toBe(SyncingEnabledState.enabled);
        expect(workspaceManager.listSourceFolders().length).toBe(2);

        await drainWorkspaceBacklog(workspaceManager);
    });
});

// These tests verify that the workspace manager correctly implements the rules for requesting
// syncing permission if enabled by feature flags.
describe("syncing permission (advanced)", () => {
    const syncingPermissionNeeded = DerivedStateName.syncingPermissionNeeded;
    let kit: WorkspaceManagerTestKit;

    const allWorkspaceFolders = [workspaceFolder1, externalFolder1];

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();

        kit = new WorkspaceManagerTestKit({
            syncingPermissionTracker: new MockSyncingPermissionTracker(),
        });
        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            verifyFolderIsSourceRepo: true,
            enableFileLimitsForSyncingPermission: true,
            refuseToSyncHomeDirectories: true,
        });
    });

    afterEach(() => {
        kit?.dispose();
        jest.useRealTimers();
    });

    // This test verifies that the workspace manager does not track home directories that are
    // opened as vscode workspace folders. It will include them in the result of listSourceFolders,
    // but they will not be tracked.
    test("don't track home directories (workspace folder)", async () => {
        createWorkspaceFiles(allWorkspaceFolders);
        // kit = new WorkspaceManagerTestKit();

        const mockedIsHomeDir = isHomeDir as jest.MockedFunction<typeof isHomeDir>;
        mockedIsHomeDir.mockReturnValue(true);
        kit.addDisposable({
            dispose: () => {
                mockedIsHomeDir.mockRestore();
            },
        });

        const workspaceState = [{ ...workspaceFolder1, tracked: false }];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);

        const sourceFolders = workspaceManager.listSourceFolders();
        expect(sourceFolders.length).toBe(1);
        expect(sourceFolders[0].type).toBe(PublicSourceFolderType.untrackedFolder);
        if (sourceFolders[0].type === PublicSourceFolderType.untrackedFolder) {
            expect(sourceFolders[0].syncingEnabled).toBe(false);
            expect(sourceFolders[0].reason).toBe(UntrackedFolderReason.homeDir);
        }

        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.disabled);
    });

    // This test verifies that the workspace manager rejects attempts to open home directories as
    // external folders. (It will throw)
    test("don't track home directories (external folder)", async () => {
        createWorkspaceFiles(allWorkspaceFolders);
        // kit = new WorkspaceManagerTestKit();

        const mockedIsHomeDir = isHomeDir as jest.MockedFunction<typeof isHomeDir>;
        mockedIsHomeDir.mockImplementation((pathName: string) => {
            return pathName === uriToAbsPath(externalFolder1.folderRootUri);
        });
        kit.addDisposable({
            dispose: () => {
                mockedIsHomeDir.mockRestore();
            },
        });

        const initialState = [{ ...workspaceFolder1, tracked: true }];
        const folderRoot = uriToAbsPath(workspaceFolder1.folderRootUri);
        kit.syncingPermissionTracker.setPermittedFolders([folderRoot]);

        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = initialState.concat([{ ...externalFolder1, tracked: true }]);
        let caughtError = false;
        try {
            applyWorkspaceState(workspaceManager, finalState);
            await awaitWorkspaceState(workspaceManager, finalState);
        } catch (e) {
            expect(e).toBeInstanceOf(HomeDirectoryError);
            caughtError = true;
            verifyDefined(e);
        }
        expect(caughtError).toBe(true);
    });

    // This test verifies that the workspace manager rejects folders that are too large to track.
    // It also tests that WorkspaceManager.requalifyLargeFolders will retry those folders, perhaps
    // allowing them to be tracked if they are now small enough (after adding an .augmentignore
    // file, for example).
    test("don't track too-large folders", async () => {
        const largeFolderFolderRoot = "/src/large-folder";
        const maxTrackableFiles = 10;

        // kit = new WorkspaceManagerTestKit({
        //     syncingPermissionTracker: new MockSyncingPermissionTracker(),
        // });
        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            maxTrackableFileCount: maxTrackableFiles,
        });

        // Create two sets of files:
        // * file-{i}.py is a set of files that is small enough to track.
        // * ignore{i}.py is a set of files that is too large to track.
        // The first part of this test will try to track the folder with both sets of files. The
        // workspace manager should reject the folder due to its size. The second part of the
        // test adds an .augmentignorer file that ignores the second set and then retries with
        // requalifyLargeFolders. This second attempt should succeed.
        const largeFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFiles / 2; i++) {
            largeFileSet.set(`file${i}.py`, "this is file${i}.py");
        }
        for (let i = 0; i < maxTrackableFiles; i++) {
            largeFileSet.set(`ignore${i}.py`, "this is ignore${i}.py");
        }

        const largeWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(largeFolderFolderRoot),
            repoRootUri: vscode.Uri.file(largeFolderFolderRoot),
            files: largeFileSet,
            tracked: false,
        };
        const workspaceState = [largeWorkspaceFolderState];
        kit.initWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();

        // Attempt 1 -- should fail due to size.
        await advanceTimeUntilTrue(() => {
            const folders = workspaceManager.listSourceFolders();
            if (folders.length !== 1) {
                return false;
            }
            const folder = folders[0];
            return folder.type === PublicSourceFolderType.untrackedFolder;
        });

        let folders = workspaceManager.listSourceFolders();
        expect(folders.length).toBe(1);
        expect(folders[0].type).toBe(PublicSourceFolderType.untrackedFolder);
        if (folders[0].type === PublicSourceFolderType.untrackedFolder) {
            expect(folders[0].reason).toBe(UntrackedFolderReason.tooLarge);
        }
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.disabled);

        // Add .augmentignorer and retry.
        writeFileInFolder(largeFolderFolderRoot, ".augmentignorer", "ignore*.py\n");
        workspaceManager.requalifyLargeFolders();

        // Attempt 2 -- should succeed.
        await advanceTimeUntilTrue(() => {
            const folders = workspaceManager.listSourceFolders();
            if (folders.length !== 1) {
                return false;
            }
            const folder = folders[0];
            return folder.type === PublicSourceFolderType.vscodeWorkspaceFolder;
        });

        folders = workspaceManager.listSourceFolders();
        expect(folders.length).toBe(1);
        expect(folders[0].type).toBe(PublicSourceFolderType.vscodeWorkspaceFolder);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.enabled);
    });

    // This test verifies that the workspace manager won't track any folders if any one of them
    // is too large to large to track.
    test("large and small folders", async () => {
        const smallFolderFolderRoot = "/src/small-folder";
        const largeFolderFolderRoot = "/src/large-folder";
        const maxTrackableFiles = 10;

        // kit = new WorkspaceManagerTestKit({
        //     syncingPermissionTracker: new MockSyncingPermissionTracker(),
        // });
        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            maxTrackableFileCount: maxTrackableFiles,
        });
        kit.syncingPermissionTracker.setPermittedFolders([smallFolderFolderRoot]);

        const smallFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFiles / 2; i++) {
            smallFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const largeFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFiles + 1; i++) {
            largeFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const smallWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(smallFolderFolderRoot),
            repoRootUri: vscode.Uri.file(smallFolderFolderRoot),
            files: smallFileSet,
            tracked: true,
        };

        const largeWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(largeFolderFolderRoot),
            repoRootUri: vscode.Uri.file(largeFolderFolderRoot),
            files: largeFileSet,
            tracked: false,
        };

        const workspaceState = [smallWorkspaceFolderState, largeWorkspaceFolderState];

        kit.initWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();

        await advanceTimeUntilTrue(() => {
            const folders = workspaceManager.listSourceFolders();
            if (folders.length !== 2) {
                return false;
            }
            const foldersMap = new Map<string, SourceFolderInfo>(
                folders.map((folder) => [folder.folderRoot, folder])
            );
            const largeFolder = foldersMap.get(largeFolderFolderRoot);
            if (largeFolder === undefined) {
                return false;
            }
            return largeFolder.type === PublicSourceFolderType.untrackedFolder;
        });

        const folders = workspaceManager.listSourceFolders();
        const folderMap = new Map<string, SourceFolderInfo>(
            folders.map((folder) => [folder.folderRoot, folder])
        );
        const smallFolder = folderMap.get(smallFolderFolderRoot);
        verifyDefined(smallFolder);
        expect(smallFolder.type).toBe(PublicSourceFolderType.vscodeWorkspaceFolder);
        expect(smallFolder.syncingEnabled).toBe(false);
        const largeFolder = folderMap.get(largeFolderFolderRoot);
        verifyDefined(largeFolder);
        expect(largeFolder.syncingEnabled).toBe(false);
        expect(largeFolder.type).toBe(PublicSourceFolderType.untrackedFolder);
        if (largeFolder.type === PublicSourceFolderType.untrackedFolder) {
            expect(largeFolder.reason).toBe(UntrackedFolderReason.tooLarge);
        }

        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.disabled);
    });

    // This test verifies that the workspace manager will request permission for folders that are
    // larger than the requestPermissionThreshold.
    test("request permission for large folders", async () => {
        const smallFolderFolderRoot = "/src/small-folder";
        const largeFolderFolderRoot = "/src/large-folder";
        const maxTrackableFilesWithoutPermission = 10;

        // kit = new WorkspaceManagerTestKit({
        //     syncingPermissionTracker: new MockSyncingPermissionTracker(),
        // });
        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            maxTrackableFileCountWithoutPermission: maxTrackableFilesWithoutPermission,
        });
        kit.syncingPermissionTracker.setPermittedFolders([smallFolderFolderRoot]);

        const smallFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFilesWithoutPermission / 2; i++) {
            smallFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const largeFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFilesWithoutPermission + 1; i++) {
            largeFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const smallWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(smallFolderFolderRoot),
            repoRootUri: vscode.Uri.file(smallFolderFolderRoot),
            files: smallFileSet,
            tracked: true,
        };

        const largeWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(largeFolderFolderRoot),
            repoRootUri: vscode.Uri.file(largeFolderFolderRoot),
            files: largeFileSet,
            tracked: true,
        };

        const workspaceState = [smallWorkspaceFolderState, largeWorkspaceFolderState];

        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        kit.initWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        const startupPromise = awaitStartup(workspaceManager, workspaceState);

        let folders: Array<SourceFolderInfo>;
        await advanceTimeUntilTrue(() => {
            folders = workspaceManager.listSourceFolders();
            const folderMap = new Map<string, SourceFolderInfo>(
                folders.map((folder) => [folder.folderRoot, folder])
            );
            const folder = folderMap.get(smallFolderFolderRoot);
            if (folder === undefined) {
                return false;
            }
            if (folder.type !== PublicSourceFolderType.vscodeWorkspaceFolder) {
                return false;
            }
            if (!folder.syncingEnabled) {
                return false;
            }
            return folders.length >= 2;
        });

        await advanceTimeUntilTrue(() => {
            return actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded);
        });

        kit.syncingEnabledTracker.enableSyncing();
        await startupPromise;
    });

    // This test verifies that the workspace manager will request permission for folders that don't
    // have a .augmentroot file or .git directory.
    test("request permission for folders that don't look like source repos", async () => {
        const workspaceState = [{ ...workspaceFolder1, tracked: true, ignoredFiles: new Map() }];

        // kit = new WorkspaceManagerTestKit({
        //     syncingPermissionTracker: new MockSyncingPermissionTracker(),
        // });
        kit.syncingPermissionTracker.setPermittedFolders([]);

        kit.apiServer.findMissing.mockImplementation(() => {
            return Promise.resolve({
                unknownBlobNames: [],
                nonindexedBlobNames: [],
            });
        });
        kit.addDisposable({
            dispose: () => {
                kit.apiServer.findMissing.mockRestore();
            },
        });

        // Pass an invalid sentinelName so no .augmentroot file is created.
        createWorkspaceFiles(workspaceState, "not.augmentroot");

        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        const startupPromise = awaitStartup(workspaceManager, workspaceState);

        let folders: Array<SourceFolderInfo>;
        await advanceTimeUntilTrue(() => {
            folders = workspaceManager.listSourceFolders();
            return folders.length === 1;
        });

        await advanceTimeUntilTrue(() => {
            return actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded);
        });

        kit.syncingEnabledTracker.enableSyncing();
        await startupPromise;
    });
});

// These tests verify that all syncing permission checks can be disabled by feature flags:
// * verifyFolderIsSourceRepo: false
// * enableFileLimitsForSyncingPermission: false
// * refuseToSyncHomeDirectories: false
describe("syncing permission (advanced checks disabled by feature flags)", () => {
    const syncingPermissionNeeded = DerivedStateName.syncingPermissionNeeded;
    let kit: WorkspaceManagerTestKit;

    const allWorkspaceFolders = [workspaceFolder1, externalFolder1];

    beforeEach(() => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();

        kit = new WorkspaceManagerTestKit({
            syncingPermissionTracker: new MockSyncingPermissionTracker(),
        });
        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            verifyFolderIsSourceRepo: false,
            enableFileLimitsForSyncingPermission: false,
            refuseToSyncHomeDirectories: false,
        });
    });

    afterEach(() => {
        kit?.dispose();
        jest.useRealTimers();
    });

    // Verify that home directory checks for workspace folders are disabled by
    // `refuseToSyncHomeDirectories: false`
    test("disable home directory checks (workspace folder)", async () => {
        createWorkspaceFiles(allWorkspaceFolders);

        const mockedIsHomeDir = isHomeDir as jest.MockedFunction<typeof isHomeDir>;
        mockedIsHomeDir.mockReturnValue(true);
        kit.addDisposable({
            dispose: () => {
                mockedIsHomeDir.mockRestore();
            },
        });

        const workspaceState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.enabled);
    });

    // Verify that home directory checks for external source folders are disabled by
    // `refuseToSyncHomeDirectories: false`
    test("disable home directory checks (external folder)", async () => {
        createWorkspaceFiles(allWorkspaceFolders);

        const mockedIsHomeDir = isHomeDir as jest.MockedFunction<typeof isHomeDir>;
        mockedIsHomeDir.mockImplementation((pathName: string) => {
            return pathName === uriToAbsPath(externalFolder1.folderRootUri);
        });
        kit.addDisposable({
            dispose: () => {
                mockedIsHomeDir.mockRestore();
            },
        });

        const initialState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(initialState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, initialState);

        const finalState = initialState.concat([{ ...externalFolder1, tracked: true }]);
        let caughtError = false;
        try {
            applyWorkspaceState(workspaceManager, finalState);
            await awaitWorkspaceState(workspaceManager, finalState);
        } catch (e) {
            expect(e).toBeInstanceOf(HomeDirectoryError);
            caughtError = true;
            verifyDefined(e);
        }
        expect(caughtError).toBe(false);
    });

    // Verify that the checks for a too-large folder are disabled by
    // `enableFileLimitsForSyncingPermission: false` (single large folder version)
    test("disable checks for too-large folders", async () => {
        const largeFolderFolderRoot = "/src/large-folder";
        const maxTrackableFiles = 10;

        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            maxTrackableFileCount: maxTrackableFiles,
        });

        // Create two sets of files:
        // * file-{i}.py is a set of files that is small enough to track.
        // * ignore{i}.py is a set of files that is too large to track.
        const largeFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFiles / 2; i++) {
            largeFileSet.set(`file${i}.py`, "this is file${i}.py");
        }
        for (let i = 0; i < maxTrackableFiles; i++) {
            largeFileSet.set(`ignore${i}.py`, "this is ignore${i}.py");
        }

        const largeWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(largeFolderFolderRoot),
            repoRootUri: vscode.Uri.file(largeFolderFolderRoot),
            files: largeFileSet,
            tracked: true,
        };
        const workspaceState = [largeWorkspaceFolderState];
        kit.initWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);

        const folders = workspaceManager.listSourceFolders();
        expect(folders.length).toBe(1);
        expect(folders[0].type).toBe(PublicSourceFolderType.vscodeWorkspaceFolder);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.enabled);
    });

    // Verify that the checks for a too-large folder are disabled by
    // `enableFileLimitsForSyncingPermission: false` (combination large and small folder version)
    test("large and small folders", async () => {
        const smallFolderFolderRoot = "/src/small-folder";
        const largeFolderFolderRoot = "/src/large-folder";
        const maxTrackableFiles = 10;

        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            maxTrackableFileCount: maxTrackableFiles,
        });
        kit.syncingPermissionTracker.setPermittedFolders([smallFolderFolderRoot]);

        const smallFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFiles / 2; i++) {
            smallFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const largeFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFiles + 1; i++) {
            largeFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const smallWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(smallFolderFolderRoot),
            repoRootUri: vscode.Uri.file(smallFolderFolderRoot),
            files: smallFileSet,
            tracked: true,
        };

        const largeWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(largeFolderFolderRoot),
            repoRootUri: vscode.Uri.file(largeFolderFolderRoot),
            files: largeFileSet,
            tracked: true,
        };

        const workspaceState = [smallWorkspaceFolderState, largeWorkspaceFolderState];
        kit.initWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
        expect(kit.syncingEnabledTracker.syncingEnabledState).toBe(SyncingEnabledState.enabled);
    });

    // Verify that reuqesting syncing permission for large folders is disabled by
    // `enableFileLimitsForSyncingPermission: false`
    test("disable permission request for large folders", async () => {
        const smallFolderFolderRoot = "/src/small-folder";
        const largeFolderFolderRoot = "/src/large-folder";
        const maxTrackableFilesWithoutPermission = 10;

        const featureFlags = kit.featureFlagManager.currentFlags;
        kit.featureFlagManager.update({
            ...featureFlags,
            maxTrackableFileCountWithoutPermission: maxTrackableFilesWithoutPermission,
        });
        kit.syncingPermissionTracker.setPermittedFolders([smallFolderFolderRoot]);

        const smallFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFilesWithoutPermission / 2; i++) {
            smallFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const largeFileSet = new Map<string, string>();
        for (let i = 0; i < maxTrackableFilesWithoutPermission + 1; i++) {
            largeFileSet.set(`file${i}.py`, "this is file${i}.py");
        }

        const smallWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(smallFolderFolderRoot),
            repoRootUri: vscode.Uri.file(smallFolderFolderRoot),
            files: smallFileSet,
            tracked: true,
        };

        const largeWorkspaceFolderState: MockSourceFolderState = {
            sourceFolderType: MockSourceFolderType.workspaceFolder,
            folderRootUri: vscode.Uri.file(largeFolderFolderRoot),
            repoRootUri: vscode.Uri.file(largeFolderFolderRoot),
            files: largeFileSet,
            tracked: true,
        };

        const workspaceState = [smallWorkspaceFolderState, largeWorkspaceFolderState];
        kit.initWorkspace(workspaceState);

        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
    });

    // Verify that the check for a folder that doesn't look like a source repo is disabled by
    // `verifyFolderIsSourceRepo: false`
    test("disable check for folders that don't look like source repos", async () => {
        const workspaceState = [{ ...workspaceFolder1, tracked: true, ignoredFiles: new Map() }];

        kit.syncingPermissionTracker.setPermittedFolders([]);

        kit.apiServer.findMissing.mockImplementation(() => {
            return Promise.resolve({
                unknownBlobNames: [],
                nonindexedBlobNames: [],
            });
        });
        kit.addDisposable({
            dispose: () => {
                kit.apiServer.findMissing.mockRestore();
            },
        });

        // Pass an invalid sentinelName so no .augmentroot file is created.
        createWorkspaceFiles(workspaceState, "not.augmentroot");

        const actionsModel = kit.actionsModel;
        expect(actionsModel.isDerivedStateSatisfied(syncingPermissionNeeded)).toBe(false);
        kit.setupWorkspace(workspaceState);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, workspaceState);
    });
});

describe("rename", () => {
    const workspaceFolder: MockSourceFolderState = {
        sourceFolderType: MockSourceFolderType.workspaceFolder,
        folderRootUri: vscode.Uri.file("/src/myFolder"),
        repoRootUri: vscode.Uri.file("/src/myFolder"),
        files: new Map<string, string>([
            ["file1.py", "file1"],
            ["file2.py", "file2"],
            ["dir1/file3.py", "file3"],
            ["dir1/file4.py", "file4"],
            ["dir1/dir2/file5.py", "file5"],
            ["dir1/dir2/file6.py", "file6"],
        ]),
        tracked: true,
    };

    test("sidecar file moved event", async () => {
        const kit = new WorkspaceManagerTestKit();
        kit.setupWorkspace([workspaceFolder]);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, [workspaceFolder]);

        // Create a watcher for the sidecar file moved event
        const sidecarFileMovedWatcher = kit.createSidecarFileMovedWatcher(workspaceManager);

        // Get the original and new file paths
        const origRelPath = "dir1/file3.py";
        const newRelPath = "dir1/file3-renamed.py";
        const origAbsPath = pathNameInSourceFolder(workspaceFolder, origRelPath);
        const newAbsPath = pathNameInSourceFolder(workspaceFolder, newRelPath);

        // Create the new file in the mock file system
        mockFSUtils.writeFileUtf8(newAbsPath, "file3-renamed");

        // Publish a file rename event
        publishFileRename(vscode.Uri.file(origAbsPath), vscode.Uri.file(newAbsPath));

        // Verify that the sidecar file moved event was fired
        expect(sidecarFileMovedWatcher.movedFiles.length).toBe(1);
        const oldQualifiedPathName = sidecarFileMovedWatcher.movedFiles[0].oldQualifiedPathName;
        const newQualifiedPathName = sidecarFileMovedWatcher.movedFiles[0].newQualifiedPathName;
        expect(joinPath(oldQualifiedPathName.rootPath, oldQualifiedPathName.relPath)).toBe(
            origAbsPath
        );
        expect(joinPath(newQualifiedPathName.rootPath, newQualifiedPathName.relPath)).toBe(
            newAbsPath
        );
    });

    test("sidecar file deleted event", async () => {
        const kit = new WorkspaceManagerTestKit();
        kit.setupWorkspace([workspaceFolder]);
        const workspaceManager = kit.createWorkspaceManager();
        await awaitStartup(workspaceManager, [workspaceFolder]);

        // Create a watcher for the sidecar file deleted event
        const sidecarFileDeletedWatcher = kit.createSidecarFileDeletedWatcher(workspaceManager);

        // Get the file path
        const relPath = "dir1/file3.py";
        const absPath = pathNameInSourceFolder(workspaceFolder, relPath);

        // Publish a file delete event
        publishFileDelete(vscode.Uri.file(absPath));

        // Verify that the sidecar file deleted event was fired
        expect(sidecarFileDeletedWatcher.deletedFiles.length).toBe(1);
        const qualifiedPathName = sidecarFileDeletedWatcher.deletedFiles[0].qualifiedPathName;
        expect(joinPath(qualifiedPathName.rootPath, qualifiedPathName.relPath)).toBe(absPath);
    });

    const workspaceState = [workspaceFolder];

    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();
        kit = new WorkspaceManagerTestKit();
        kit.initWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    test.each([
        ["dir1/file3.py", "dir1/file3-renamed.py"],
        ["dir1/file3.py", "dir1/dir2/file3.py"],
    ])("rename file(%s, %s)", async (origRelPath: string, newRelPath: string) => {
        await awaitStartup(workspaceManager, workspaceState);

        const origAbsPath = pathNameInSourceFolder(workspaceFolder, origRelPath);
        const newAbsPath = pathNameInSourceFolder(workspaceFolder, newRelPath);
        const origQPN = workspaceManager.resolvePathName(origAbsPath);
        verifyDefined(origQPN);
        const newQPN = workspaceManager.resolvePathName(newAbsPath);
        verifyDefined(newQPN);

        const origExpectedBlobName = kit.computeBlobName(workspaceFolder, origQPN.relPath);
        const origBlobName = workspaceManager.getBlobName(origQPN);
        expect(origBlobName).toBe(origExpectedBlobName);
        expect(workspaceManager.getBlobName(newQPN)).toBeUndefined();

        mockFSUtils.rename(origAbsPath, newAbsPath);

        const newExpectedBlobName = kit.computeBlobName(workspaceFolder, newQPN.relPath);
        await kit.awaitBlobName(workspaceManager, newQPN, newExpectedBlobName);
        await kit.awaitBlobName(workspaceManager, origQPN, undefined);
    });

    const getFolderContents = (
        workspaceManager: WorkspaceManager,
        folderRoot: string,
        subdirRelPath: string,
        contents: Map<string, FileType>
    ) => {
        const folderContents = workspaceManager.listChildren(folderRoot, subdirRelPath);
        for (const folderItem of folderContents) {
            if (!folderItem.included) {
                continue;
            }
            contents.set(folderItem.relPath, folderItem.type);
            if (folderItem.type === FileType.directory) {
                getFolderContents(workspaceManager, folderRoot, folderItem.relPath, contents);
            }
        }
    };

    const compareFolderContents = (
        workspaceManager: WorkspaceManager,
        workspaceFolder: MockSourceFolder,
        expected: Map<string, FileType>,
        actual: Map<string, FileType>
    ): boolean => {
        if (actual.size !== expected.size) {
            return false;
        }
        for (const [relPath, type] of expected) {
            expect(actual.get(relPath)).toBe(type);
            if (type !== FileType.file) {
                continue;
            }
            const expectedBlobName = kit.computeBlobName(workspaceFolder, relPath);
            const actualBlobName = workspaceManager.getBlobName(
                makeQualifiedPath(workspaceFolder, relPath)
            );
            if (actualBlobName !== expectedBlobName) {
                return false;
            }
        }
        return true;
    };

    test("rename folder", async () => {
        const folderRoot = uriToAbsPath(workspaceFolder.folderRootUri);
        const origRelPath = "dir1";
        const origAbsPath = pathNameInSourceFolder(workspaceFolder, origRelPath);
        const newRelPath = "dir1-renamed";
        const newAbsPath = pathNameInSourceFolder(workspaceFolder, newRelPath);

        const origExpected = new Map<string, FileType>([
            ["file1.py", FileType.file],
            ["file2.py", FileType.file],
            ["dir1", FileType.directory],
            ["dir1/file3.py", FileType.file],
            ["dir1/file4.py", FileType.file],
            ["dir1/dir2", FileType.directory],
            ["dir1/dir2/file5.py", FileType.file],
            ["dir1/dir2/file6.py", FileType.file],
        ]);

        const newExpected = new Map<string, FileType>([
            ["file1.py", FileType.file],
            ["file2.py", FileType.file],
            ["dir1-renamed", FileType.directory],
            ["dir1-renamed/file3.py", FileType.file],
            ["dir1-renamed/file4.py", FileType.file],
            ["dir1-renamed/dir2", FileType.directory],
            ["dir1-renamed/dir2/file5.py", FileType.file],
            ["dir1-renamed/dir2/file6.py", FileType.file],
        ]);

        await awaitStartup(workspaceManager, workspaceState);

        const origResults = new Map<string, FileType>();
        getFolderContents(workspaceManager, folderRoot, "", origResults);
        expect(
            compareFolderContents(workspaceManager, workspaceFolder, origExpected, origResults)
        ).toBe(true);

        mockFSUtils.rename(origAbsPath, newAbsPath);

        await advanceTimeUntilTrue(() => {
            const newResults = new Map<string, FileType>();
            getFolderContents(workspaceManager, folderRoot, "", newResults);
            return compareFolderContents(
                workspaceManager,
                workspaceFolder,
                newExpected,
                newResults
            );
        });
    });
});

// These tests verify that the WorkspaceManager correctly honors the values of the feature flags
// related to syncing permission.
describe("feature flags", () => {
    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager | undefined;

    const defaultMaxTrackableFileCount = defaultFeatureFlags.maxTrackableFileCount;
    const defaultMaxTrackableFileCountWithoutPermission =
        defaultFeatureFlags.maxTrackableFileCountWithoutPermission;
    const defaultMinUploadedPercentageWithoutPermission =
        defaultFeatureFlags.minUploadedPercentageWithoutPermission;

    beforeEach(() => {
        kit = new WorkspaceManagerTestKit();
    });

    afterEach(() => {
        kit?.dispose();
    });

    test.each([
        [undefined, false],
        [true, true],
        [false, false],
    ])(
        "enableFileLimitsForSyncingPermission(%p)",
        (enableFileLimitsForSyncingPermission: boolean | undefined, expected: boolean) => {
            if (enableFileLimitsForSyncingPermission !== undefined) {
                const featureFlagManager = kit.featureFlagManager;
                const featureFlags = {
                    ...featureFlagManager.currentFlags,
                    enableFileLimitsForSyncingPermission,
                };
                featureFlagManager.update(featureFlags);
            }
            workspaceManager = kit.createWorkspaceManager();
            expect(workspaceManager.enableFileLimitsForSyncingPermission).toBe(expected);
        }
    );

    test.each([
        [undefined, defaultMaxTrackableFileCount],
        [100, 100],
        [1000000, 1000000],
    ])(
        "maxTrackableFileCount(%d)",
        (maxTrackableFileCount: number | undefined, expected: number) => {
            if (maxTrackableFileCount !== undefined) {
                const featureFlagManager = kit.featureFlagManager;
                const featureFlags = {
                    ...featureFlagManager.currentFlags,
                    maxTrackableFileCount: maxTrackableFileCount,
                };
                featureFlagManager.update(featureFlags);
            }
            workspaceManager = kit.createWorkspaceManager();
            expect(workspaceManager.maxTrackableFiles).toBe(expected);
        }
    );

    test.each([
        [undefined, undefined, defaultMaxTrackableFileCountWithoutPermission],
        [100, undefined, 100],
        [defaultMaxTrackableFileCount + 1, undefined, defaultMaxTrackableFileCount],
        [
            undefined,
            defaultMaxTrackableFileCount - 1,
            Math.min(
                defaultMaxTrackableFileCountWithoutPermission,
                defaultMaxTrackableFileCount - 1
            ),
        ],
        [100, 1000, 100],
        [1000, 100, 100],
    ])(
        "maxTrackableFileCountWithoutPermission(%p, %p)",
        (
            maxTrackableFileCountWithoutPermission: number | undefined,
            minTrackableFileCount: number | undefined,
            expected: number
        ) => {
            const featureFlagManager = kit.featureFlagManager;
            let featureFlags = featureFlagManager.currentFlags;
            if (maxTrackableFileCountWithoutPermission !== undefined) {
                featureFlags = {
                    ...featureFlags,
                    maxTrackableFileCountWithoutPermission,
                };
            }
            if (minTrackableFileCount !== undefined) {
                featureFlags = {
                    ...featureFlags,
                    maxTrackableFileCount: minTrackableFileCount,
                };
            }
            featureFlagManager.update(featureFlags);
            workspaceManager = kit.createWorkspaceManager();
            expect(workspaceManager.maxTrackableFilesWithoutPermission).toBe(expected);
        }
    );

    test.each([
        [undefined, defaultMinUploadedPercentageWithoutPermission / 100],
        [10, 0.1],
        [90, 0.9],
        [100, 1],
        [101, 1],
    ])(
        "minUploadedPercentageWithoutPermission(%p)",
        (minUploadedPercentageWithoutPermission: number | undefined, expected: number) => {
            if (minUploadedPercentageWithoutPermission !== undefined) {
                const featureFlagManager = kit.featureFlagManager;
                const featureFlags = {
                    ...featureFlagManager.currentFlags,
                    minUploadedPercentageWithoutPermission: minUploadedPercentageWithoutPermission,
                };
                featureFlagManager.update(featureFlags);
            }
            workspaceManager = kit.createWorkspaceManager();
            expect(workspaceManager.minUploadedFractionWithoutPermission).toBe(expected);
        }
    );

    test.each([
        [undefined, false],
        [true, true],
        [false, false],
    ])(
        "verifyFolderIsSourceRepo(%p)",
        (verifyFolderIsSourceRepo: boolean | undefined, expected: boolean) => {
            if (verifyFolderIsSourceRepo !== undefined) {
                const featureFlagManager = kit.featureFlagManager;
                const featureFlags = {
                    ...featureFlagManager.currentFlags,
                    verifyFolderIsSourceRepo: verifyFolderIsSourceRepo,
                };
                featureFlagManager.update(featureFlags);
            }
            workspaceManager = kit.createWorkspaceManager();
            expect(workspaceManager.verifyFolderIsSourceRepo).toBe(expected);
        }
    );

    test.each([
        [undefined, false],
        [true, true],
        [false, false],
    ])(
        "refuseToSyncHomeDirectories(%p)",
        (refuseToSyncHomeDirectories: boolean | undefined, expected: boolean) => {
            if (refuseToSyncHomeDirectories !== undefined) {
                const featureFlagManager = kit.featureFlagManager;
                const featureFlags = {
                    ...featureFlagManager.currentFlags,
                    refuseToSyncHomeDirectories: refuseToSyncHomeDirectories,
                };
                featureFlagManager.update(featureFlags);
            }
            workspaceManager = kit.createWorkspaceManager();
            expect(workspaceManager.refuseToSyncHomeDirectories).toBe(expected);
        }
    );
});

describe("findBestWorkspaceRootMatch", () => {
    describe("single folder", () => {
        let workspaceManager: WorkspaceManager;
        let kit: WorkspaceManagerTestKit;

        beforeEach(async () => {
            jest.useFakeTimers();
            resetMockWorkspace();
            mockFSUtils.reset();

            const workspaceState: MockSourceFolderState[] = [
                { ...workspaceFolder1, tracked: true },
            ];
            createWorkspaceFiles(workspaceState);
            kit = new WorkspaceManagerTestKit();
            kit.setupWorkspace(workspaceState);
            workspaceManager = kit.createWorkspaceManager();
            await awaitStartup(workspaceManager, workspaceState);
        });

        afterEach(() => {
            workspaceManager.dispose();
            kit.dispose();
            jest.useRealTimers();
        });

        test("empty workspace", () => {
            const result = workspaceManager.findBestWorkspaceRootMatch("newfile.py");
            expect(result?.qualifiedPathName).toEqual({
                rootPath: workspaceFolder1.folderRootUri.fsPath,
                relPath: "",
            });
            expect(result?.fileType).toBe(FileType.directory);
        });

        test("folder1 with no folders", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch("folder1/newfile.py");
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder1");
        });

        test("folder1 with one folder", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch("folder1/newfile.py");
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder1");
        });

        test("folder1 with two sibling folders", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/file1.py", "file1");
            writeFileInFolder(folderRoot, "folder2/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch("folder2/newfile.py");
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder2");
        });

        test("folder1 with nested folders, nested file", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/file1.py", "file1");
            writeFileInFolder(folderRoot, "folder1/nested-folder/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch(
                "folder1/nested-folder/newfile.py"
            );
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder1/nested-folder");
        });

        test("folder1 with nested folders, non-nested file", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/file1.py", "file1");
            writeFileInFolder(folderRoot, "folder1/nested-folder/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch("folder1/newfile.py");
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder1");
        });

        test("folder1 with nested folders, non-nested file, sibling folder", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/file1.py", "file1");
            writeFileInFolder(folderRoot, "folder1/nested-folder/file1.py", "file1");
            writeFileInFolder(folderRoot, "folder2/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch("folder2/newfile.py");
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder2");
        });

        test("folder1 at root of workspace", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch("newfile.py");
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("");
        });

        test("folder1 with deep nested folders", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/nested1/nested2/nested3/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch(
                "folder1/nested1/nested2/nested3/newfile.py"
            );
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder1/nested1/nested2/nested3");
        });

        test("folder1 with multiple intermediate paths", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/path1/file1.py", "file1");
            writeFileInFolder(folderRoot, "folder1/path2/file2.py", "file2");
            writeFileInFolder(folderRoot, "folder1/path3/file3.py", "file3");
            const result = workspaceManager.findBestWorkspaceRootMatch("folder1/path2/newfile.py");
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            expect(result?.qualifiedPathName.relPath).toBe("folder1/path2");
        });

        test("folder1 with non-existent intermediate path", () => {
            const folderRoot: string = workspaceFolder1.folderRootUri.fsPath;
            writeFileInFolder(folderRoot, "folder1/existing-path/file1.py", "file1");
            const result = workspaceManager.findBestWorkspaceRootMatch(
                "folder1/non-existent-path/newfile.py"
            );
            expect(result?.qualifiedPathName.rootPath).toBe(folderRoot);
            // This is the best match
            expect(result?.qualifiedPathName.relPath).toBe("folder1");
        });
    });

    describe("nested workspace", () => {
        let workspaceManager: WorkspaceManager;
        let kit: WorkspaceManagerTestKit;

        beforeEach(async () => {
            jest.useFakeTimers();
            resetMockWorkspace();
            mockFSUtils.reset();

            const workspaceState: MockSourceFolderState[] = [
                { ...workspaceFolder1, tracked: true },
                { ...nestedWorkspaceFolder, tracked: false },
            ];

            createWorkspaceFiles(workspaceState);
            kit = new WorkspaceManagerTestKit();
            kit.setupWorkspace(workspaceState);
            workspaceManager = kit.createWorkspaceManager();
            await awaitStartup(workspaceManager, workspaceState);
        });

        afterEach(() => {
            workspaceManager.dispose();
            kit.dispose();
            jest.useRealTimers();
        });

        test("folder1, nested workspace", () => {
            const folderRoot1: string = workspaceFolder1.folderRootUri.fsPath;
            const folderRoot2: string = nestedWorkspaceFolder.folderRootUri.fsPath;
            writeFileInFolder(folderRoot1, "folder1/file1.py", "file1");
            writeFileInFolder(folderRoot2, "folder2/file2.py", "file2");

            // This is equivalent to no match, since the nested workspace is not tracked
            // and thus we should never attempt to resolve against it.
            const result = workspaceManager.findBestWorkspaceRootMatch("folder2/newfile.py");

            const repoRoot2: string = nestedWorkspaceFolder.repoRootUri.fsPath;
            expect(result?.qualifiedPathName.rootPath).toBe(repoRoot2);
            expect(result?.qualifiedPathName.relPath).toBe("");
        });

        test("folder1, nested workspace, nested file", () => {
            const folderRoot1: string = workspaceFolder1.folderRootUri.fsPath;
            const folderRoot2: string = nestedWorkspaceFolder.folderRootUri.fsPath;
            writeFileInFolder(folderRoot1, "folder1/file1.py", "file1");
            writeFileInFolder(folderRoot2, "folder2/file2.py", "file2");

            const result = workspaceManager.findBestWorkspaceRootMatch(
                "inner-folder/folder2/newfile.py"
            );
            const repoRoot1: string = workspaceFolder1.repoRootUri.fsPath;
            expect(result?.qualifiedPathName.rootPath).toBe(repoRoot1);
            expect(result?.qualifiedPathName.relPath).toBe("inner-folder/folder2");
        });
    });

    describe("sibling workspaces", () => {
        let workspaceManager: WorkspaceManager;
        let kit: WorkspaceManagerTestKit;

        beforeEach(async () => {
            jest.useFakeTimers();
            resetMockWorkspace();
            mockFSUtils.reset();

            const workspaceState: MockSourceFolderState[] = [
                { ...workspaceFolder1, tracked: true },
                { ...nestedWorkspaceFolder, tracked: false },
                { ...workspaceFolder2, tracked: true },
            ];
            createWorkspaceFiles(workspaceState);
            kit = new WorkspaceManagerTestKit();
            kit.setupWorkspace(workspaceState);
            workspaceManager = kit.createWorkspaceManager();
            await awaitStartup(workspaceManager, workspaceState);
        });

        afterEach(() => {
            workspaceManager.dispose();
            kit.dispose();
            jest.useRealTimers();
        });

        test("folder1, sibling workspace", () => {
            const folderRoot1: string = workspaceFolder1.folderRootUri.fsPath;
            const folderRoot2: string = workspaceFolder2.folderRootUri.fsPath;
            writeFileInFolder(folderRoot1, "folder1/file1.py", "file1");
            writeFileInFolder(folderRoot2, "folder1/file2.py", "file2");

            // Choose the first workspace if it is ambiguous
            const result = workspaceManager.findBestWorkspaceRootMatch("folder1/newfile.py");
            const repoRoot1: string = workspaceFolder1.repoRootUri.fsPath;
            expect(result?.qualifiedPathName.rootPath).toBe(repoRoot1);
            expect(result?.qualifiedPathName.relPath).toBe("folder1");
        });

        test("folder1, sibling workspace, nested file", () => {
            const folderRoot1: string = workspaceFolder1.folderRootUri.fsPath;
            const folderRoot2: string = workspaceFolder2.folderRootUri.fsPath;
            writeFileInFolder(folderRoot1, "folder1/nested1/file1.py", "file1");
            writeFileInFolder(folderRoot2, "folder1/nested2/file2.py", "file2");
            const result = workspaceManager.findBestWorkspaceRootMatch(
                "folder1/nested2/newfile.py"
            );
            const repoRoot2: string = workspaceFolder2.repoRootUri.fsPath;
            expect(result?.qualifiedPathName.rootPath).toBe(repoRoot2);
            expect(result?.qualifiedPathName.relPath).toBe("folder1/nested2");
        });

        test("folder1, sibling workspace, non-existent intermediate path", () => {
            const folderRoot1: string = workspaceFolder1.folderRootUri.fsPath;
            const folderRoot3: string = workspaceFolder2.folderRootUri.fsPath;
            writeFileInFolder(folderRoot1, "folder1/existing-path/file1.py", "file1");
            writeFileInFolder(folderRoot3, "folder3/existing-path/file2.py", "file2");
            const result = workspaceManager.findBestWorkspaceRootMatch(
                "folder1/non-existent-path/newfile.py"
            );
            // Choose the first workspace if it is ambiguous
            const repoRoot1: string = workspaceFolder1.repoRootUri.fsPath;
            expect(result?.qualifiedPathName.rootPath).toBe(repoRoot1);
            // This is the best match
            expect(result?.qualifiedPathName.relPath).toBe("folder1");
        });
    });
});
