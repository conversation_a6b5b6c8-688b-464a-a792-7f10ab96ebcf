import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import * as vscode from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { CheckpointBlobsResult } from "../../augment-api";
import { defaultFeatureFlags, FeatureFlagManager, type FeatureFlags } from "../../feature-flags";
import { makePromise } from "../../utils/promise-utils";
import { BlobsCheckpointManager } from "../../workspace/blobs-checkpoint-manager";
import { BlobNameChangeEvent } from "../../workspace/path-map";

function genRandomString(length: number): string {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const charactersLength = characters.length;
    let result = "";

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charactersLength);
        result += characters.charAt(randomIndex);
    }

    return result;
}

function genBlobNames(count: number): Set<string> {
    const blobs = new Set<string>();
    while (blobs.size < count) {
        blobs.add(genRandomString(8));
    }
    return blobs;
}

class BlobsCheckpointManagerTestKit extends BlobsCheckpointManager {
    public mockApiServer;
    public featureFlagManager;
    public onDidChangeBlobName;

    constructor(checkpointThreshold: number = 10) {
        const mockApiServer = new MockAPIServer();
        const featureFlagManager = new FeatureFlagManager({
            initialFlags: { ...defaultFeatureFlags },
            fetcher: async (): Promise<FeatureFlags | undefined> => {
                return { ...defaultFeatureFlags };
            },
        });
        const eventEmitter = new vscode.EventEmitter<BlobNameChangeEvent>();
        // keep the mock api server consistent with the feature flag manager
        super(mockApiServer, featureFlagManager, eventEmitter.event, checkpointThreshold);

        this.mockApiServer = mockApiServer;
        this.featureFlagManager = featureFlagManager;
        this.onDidChangeBlobName = eventEmitter;
    }

    async waitForNextCheckpointId(origBlobs: Blobs): Promise<Blobs> {
        const blobs = this.getContext();
        if (blobs.checkpointId !== origBlobs.checkpointId) {
            return blobs;
        }
        let [promise, resolve] = makePromise<Blobs>();
        const toDispose = this.onContextChange((blobs: Blobs) => {
            if (blobs.checkpointId !== origBlobs.checkpointId) {
                resolve(blobs);
            }
        }, this);
        promise.then(() => toDispose.dispose());
        return promise;
    }
}

describe("blobs-checkpoint-manager", () => {
    beforeEach(() => {
        // Clean out the mock filesystem before each test
        mockFSUtils.reset();
    });

    test("Checkpoint a set of blobs", async () => {
        const checkpointThreshold = 10;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        let blobNames = genBlobNames(checkpointThreshold);

        // A checkpoint is launched as a side effect of updating blob state.
        let prevBlobs = checkpointManager.getContext();
        for (const item of blobNames) {
            checkpointManager.updateBlob("test", undefined, item);
            // the special case of restating a blob name should not be treated as a deletion.
            checkpointManager.updateBlob("test", item, item);
        }

        const blobs = await checkpointManager.waitForNextCheckpointId(prevBlobs);

        // After a checkpoint, we expect the working set to be empty, and the
        // version manager to include all blobs in the current version.
        expect(blobs.addedBlobs.length).toEqual(0);
        let s = new Set(checkpointManager.getCheckpointedBlobNames());
        expect(s).toEqual(blobNames);
    });

    test("expandBlobs", async () => {
        const checkpointThreshold = 4;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };
        }
        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        let input = checkpointManager.getContext();
        let expand = () => checkpointManager.expandBlobs(input);

        // Can expand input with no checkpoint id, or with a matching checkpoint id
        expect(expand()).toEqual([]);
        input = { ...input, addedBlobs: ["blob1"], deletedBlobs: ["blob2"] };
        expect(expand()).toEqual(["blob1"]);
        // Different checkpointId cannot be handled
        input = { ...input, checkpointId: "0x1234567" };
        expect(expand).toThrow();

        checkpointManager.updateBlob("test", undefined, "blob1");
        checkpointManager.updateBlob("test", undefined, "blob2");
        checkpointManager.updateBlob("test", undefined, "blob3");
        checkpointManager.updateBlob("test", undefined, "blob4");
        const checkpoint1 = await checkpointManager.waitForNextCheckpointId(
            checkpointManager.getContext()
        );
        expect(checkpoint1.checkpointId).toBe("0x1");
        expect(checkpoint1.addedBlobs).toEqual([]);
        expect(checkpoint1.deletedBlobs).toEqual([]);
        expect(checkpointManager.getCheckpointedBlobNames()).toEqual([
            "blob1",
            "blob2",
            "blob3",
            "blob4",
        ]);
        expect(checkpointManager.expandBlobs(checkpoint1)).toEqual([
            "blob1",
            "blob2",
            "blob3",
            "blob4",
        ]);

        // Can expand input with no checkpoint id, or with a matching checkpoint id
        input = {
            checkpointId: undefined,
            addedBlobs: ["blob3", "blob7"],
            deletedBlobs: ["blob6"],
        };
        expect(expand()).toEqual(["blob3", "blob7"]);
        input = { ...input, checkpointId: "0x1234567" };
        expect(expand).toThrow();
        input = { checkpointId: "0x1", addedBlobs: ["blob5"], deletedBlobs: ["blob4"] };
        expect(expand()).toEqual(["blob1", "blob2", "blob3", "blob5"]);

        checkpointManager.updateBlob("test", "blob1", "blob11");
        input = checkpointManager.getContext();
        expect(expand()).toEqual(["blob2", "blob3", "blob4", "blob11"]);

        // We don't test for having a blob name in addedBlobs that's also in the checkpoint
        // because that's not a well-formed Blobs object, that which would be returned
        // from the checkpoint manager.
        // Maybe others would disagree.
    });

    test("getContextAdjusted", async () => {
        const checkpointThreshold = 4;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };
        }
        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        // SETUP (with some assertions along the way)

        // Add 2 blobs which will be in the checkpoint
        checkpointManager.updateBlob("test", undefined, "in1");
        checkpointManager.updateBlob("test", undefined, "in2");
        // Add 2 blobs which will be removed from the workspace after checkpointing
        checkpointManager.updateBlob("test", undefined, "deleted1");
        checkpointManager.updateBlob("test", undefined, "deleted2");

        const checkpoint = await checkpointManager.waitForNextCheckpointId(
            checkpointManager.getContext()
        );
        expect(checkpoint.checkpointId).toBe("0x1");
        expect(checkpoint.addedBlobs).toEqual([]);
        expect(checkpoint.deletedBlobs).toEqual([]);
        expect(checkpointManager.getCheckpointedBlobNames()).toEqual([
            "in1",
            "in2",
            "deleted1",
            "deleted2",
        ]);

        // Replace the 2 "deleted" blobs with 2 new blobs
        checkpointManager.updateBlob("test", "deleted1", "added1");
        checkpointManager.updateBlob("test", "deleted2", "added2");

        // This will be the workspace state from here on out
        const blobs = checkpointManager.getContext();
        expect(blobs.checkpointId).toEqual("0x1");
        expect(blobs.addedBlobs).toEqual(["added1", "added2"]);
        expect(blobs.deletedBlobs).toEqual(["deleted1", "deleted2"]);
        expect(checkpointManager.expandBlobs(blobs)).toEqual(["in1", "in2", "added1", "added2"]);

        // END SETUP

        let adj = checkpointManager.getContextAdjusted(new Set(), new Set());
        expect(adj).toEqual(blobs);

        // Including blobs that are already present
        adj = checkpointManager.getContextAdjusted(new Set(["in1", "added2"]), new Set());
        expect(adj).toEqual(blobs);

        // Excluding blobs that are already not present
        adj = checkpointManager.getContextAdjusted(new Set(), new Set(["deleted1", "random5"]));
        expect(adj).toEqual(blobs);

        // Excluding blobs that are present
        adj = checkpointManager.getContextAdjusted(new Set(), new Set(["in1", "added2"]));
        expect(adj.checkpointId).toEqual("0x1");
        expect(adj.addedBlobs).toEqual(["added1"]);
        expect(adj.deletedBlobs).toEqual(["deleted1", "deleted2", "in1"]);
        expect(checkpointManager.expandBlobs(adj)).toEqual(["in2", "added1"]);

        // Including blobs that are not present
        adj = checkpointManager.getContextAdjusted(new Set(["deleted1", "random5"]), new Set());
        expect(adj.checkpointId).toEqual("0x1");
        expect(adj.addedBlobs).toEqual(["added1", "added2", "random5"]);
        expect(adj.deletedBlobs).toEqual(["deleted2"]);
        expect(new Set(checkpointManager.expandBlobs(adj))).toEqual(
            new Set(["in1", "in2", "added1", "added2", "deleted1", "random5"])
        );
    });

    test("Handle error in checkpoint due to unknown starting checkpoint id", async () => {
        // Ensure that we handle the case where a checkpoint request fails because
        // of a generic error, like when the checkpoint is too large.

        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            // Note: if the generator produces an empty version after a
            // checkpoint, this will be treated as an error by the api mock.
            yield { newCheckpointId: "" };

            yield { newCheckpointId: "0x1234567" };
        }

        // Checkpoint after 10 new blobs
        let checkpointThreshold = 10;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        const blobNames = Array.from(genBlobNames(checkpointThreshold + 1));
        for (const item of blobNames.slice(0, checkpointThreshold - 1)) {
            checkpointManager.updateBlob("test", undefined, item);
        }

        // This should fail to checkpoint
        checkpointManager.updateBlob("test", undefined, blobNames[checkpointThreshold - 1]);
        await checkpointManager.awaitEmptyQueue();
        const blobs = checkpointManager.getContext();
        expect(blobs.checkpointId).toBeUndefined();

        // The next retry should be after updateBlobs() is called again, and
        // that should succeed
        checkpointManager.updateBlob("test", undefined, blobNames[checkpointThreshold]);
        const blobs2 = await checkpointManager.waitForNextCheckpointId(blobs);
        expect(blobs2.checkpointId).toEqual("0x1234567");
        expect(blobs2.addedBlobs.length).toEqual(0);
        let s = new Set(checkpointManager.getCheckpointedBlobNames());
        expect(s.size).toEqual(checkpointThreshold + 1);
    });

    test("Continue checkpointing while the working set exceeds the threshold", async () => {
        // Ensure we continue checkpointing until our working set is below the threshold
        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };
            yield { newCheckpointId: "0x2" };
            yield { newCheckpointId: "0x3" };
        }

        const checkpointThreshold = 4;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        // Create a working set 2x the threshold of a checkpoint.
        let prevBlobs = checkpointManager.getContext();
        let blobNames = genBlobNames(checkpointThreshold * 2);
        for (const item of blobNames) {
            checkpointManager.updateBlob("test", undefined, item);
        }

        let blobs = await checkpointManager.waitForNextCheckpointId(prevBlobs);
        if (blobs.checkpointId === "0x1") {
            // Try again if we hit the intermediate checkpoint
            prevBlobs = blobs;
            blobs = await checkpointManager.waitForNextCheckpointId(prevBlobs);
        }
        expect(blobs.checkpointId).toEqual("0x2");

        // After a checkpoint, we expect the working set to be empty, and the
        // checkpoint manager to include all blobs in the current version.
        expect(blobs.addedBlobs.length).toEqual(0);
        let s = new Set(checkpointManager.getCheckpointedBlobNames());
        expect(s).toEqual(blobNames);
    });

    test("Handle resetting checkpoint during a checkpoint operation", async () => {
        const checkpointThreshold = 4;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        // Ensure we continue checkpointing until our working set is below the threshold
        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };

            // If we reset the checkpointer before completing the checkpoint,
            // the checkpointer should try again.
            checkpointManager.resetCheckpoint();
            yield { newCheckpointId: "0x2" };

            yield { newCheckpointId: "0x3" };
        }

        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        // Create enough blob names for two checkpoints
        let prevBlobs = checkpointManager.getContext();
        let blobNames = genBlobNames(checkpointThreshold * 2);
        for (const item of Array.from(blobNames).slice(0, checkpointThreshold)) {
            checkpointManager.updateBlob("test", undefined, item);
        }

        // First checkpoint should be successful.
        let blobs = await checkpointManager.waitForNextCheckpointId(prevBlobs);
        expect(blobs.checkpointId).toEqual("0x1");

        // Generate a new checkpoint based on the last successful one.
        for (const item of Array.from(blobNames).slice(
            checkpointThreshold,
            2 * checkpointThreshold
        )) {
            checkpointManager.updateBlob("test", undefined, item);
        }

        // Next checkpoint should either be undefined or our final checkpoint
        blobs = await checkpointManager.waitForNextCheckpointId(blobs);
        if (blobs.checkpointId === undefined) {
            // Try again if we hit the intermediate checkpoint
            blobs = await checkpointManager.waitForNextCheckpointId(blobs);
        }
        expect(blobs.checkpointId).toEqual("0x3");
    });

    test("Handle workspace changes during a checkpoint operation", async () => {
        const checkpointThreshold = 4;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);

        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };
            yield { newCheckpointId: "0x2" };
        }
        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        let checkpointDelay = checkpointManager.mockApiServer.delayCheckpoint();
        let blobs = checkpointManager.getContext();
        let blobNames = Array.from(genBlobNames(checkpointThreshold * 2));
        let workspace = new Set<string>();
        for (const item of blobNames.slice(0, checkpointThreshold)) {
            workspace.add(item);
            checkpointManager.updateBlob("test", undefined, item);
        }
        blobs = checkpointManager.getContext();
        expect(workspace).toEqual(new Set(checkpointManager.expandBlobs(blobs)));
        expect(blobs.checkpointId).toBeUndefined();
        expect(blobs.addedBlobs).toEqual(blobNames.slice(0, checkpointThreshold));

        checkpointDelay.dispose();
        blobs = await checkpointManager.waitForNextCheckpointId(blobs);
        expect(workspace).toEqual(new Set(checkpointManager.expandBlobs(blobs)));
        expect(blobs.checkpointId).toEqual("0x1");
        expect(blobs.addedBlobs.length).toEqual(0);
        expect(blobs.deletedBlobs.length).toEqual(0);

        // Stall the next checkpoint
        checkpointDelay = checkpointManager.mockApiServer.delayCheckpoint();

        // Select one blob from the first checkpoint to delete
        let deletedBlob = blobNames[0];
        workspace.delete(deletedBlob);
        checkpointManager.updateBlob("test", deletedBlob, undefined);
        // Remember first added blob that will be in the next checkpoint
        let addedBlob = blobNames[checkpointThreshold];
        // Push enough blobs to trigger next checkpoint
        for (const item of blobNames.slice(checkpointThreshold, 2 * checkpointThreshold)) {
            workspace.add(item);
            checkpointManager.updateBlob("test", undefined, item);
        }
        blobs = checkpointManager.getContext();
        expect(workspace).toEqual(new Set(checkpointManager.expandBlobs(blobs)));
        expect(blobs.checkpointId).toEqual("0x1");

        // While the checkpoint request is in flight, re-add the deleted blob,
        // and remove the added blob
        workspace.add(deletedBlob);
        workspace.delete(addedBlob);
        checkpointManager.updateBlob("test", addedBlob, deletedBlob);

        blobs = checkpointManager.getContext();
        expect(workspace).toEqual(new Set(checkpointManager.expandBlobs(blobs)));
        expect(blobs.checkpointId).toEqual("0x1");

        checkpointDelay.dispose();
        blobs = await checkpointManager.waitForNextCheckpointId(blobs);
        // Incorporating a checkpoint result should never change the workspace
        // represented by the checkpoint manager
        expect(workspace).toEqual(new Set(checkpointManager.expandBlobs(blobs)));
        expect(blobs.checkpointId).toEqual("0x2");
        // Notably, the blob that was deleted between checkpoints 0x1 and 0x2, then re-added
        // should be in the added list, and vice verse for the added blob.
        expect(blobs.addedBlobs).toEqual(expect.arrayContaining([deletedBlob]));
        expect(blobs.deletedBlobs).toEqual(expect.arrayContaining([addedBlob]));
    });

    test("Handle multiple references to a blob in the workspace", async () => {
        // The same blob name can appear at multiple different absolute paths in
        // the workspace if, for instance, it exists in multiple workspaceFolders.
        // Test that a blob name remains in the workspace context tracked by
        // the checkpoint manager as long as it exists at any absolute path.
        // In practice the path passed to updateBlob doesn't really matter; the important
        // thing is the number of updates that logically introduce or remove a blob.
        // update(path, A, B) introduces B and removes A, unless A == B.

        const checkpointThreshold = 4;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };
            yield { newCheckpointId: "0x2" };
        }
        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        let blobNames = Array.from(genBlobNames(checkpointThreshold * 3));
        let A = blobNames.pop();
        let B = blobNames.pop();
        let C = blobNames.pop();

        // Run through some behavior with no checkpoint / no 'deletedBlobs'
        // Add A twice
        checkpointManager.updateBlob("test", undefined, A);
        checkpointManager.updateBlob("test", undefined, A);
        expect(checkpointManager.getContext().addedBlobs).toEqual([A]);
        // Replace with B; A should still be present
        checkpointManager.updateBlob("test", A, B);
        expect(checkpointManager.getContext().addedBlobs).toEqual([A, B]);
        // Remove final reference to A
        checkpointManager.updateBlob("test", A, undefined);
        expect(checkpointManager.getContext().addedBlobs).toEqual([B]);

        // Now incorporate a checkpoint; produce a checkpoint including
        // B and C, as well as some other inconsequential blobs
        let refCountC = 0;
        for (; refCountC < checkpointThreshold * 2; refCountC++) {
            // Adding the same blob many times should not trigger a checkpoint,
            // as it does not increase the size of the compact Blobs payload
            checkpointManager.updateBlob("test", undefined, C);
        }
        let blobs = checkpointManager.getContext();
        expect(blobs.checkpointId).toBeUndefined();
        expect(blobs.addedBlobs).toEqual([B, C]);
        for (let i = 0; i < checkpointThreshold - 2; i++) {
            checkpointManager.updateBlob("test", undefined, blobNames.pop());
        }
        blobs = await checkpointManager.waitForNextCheckpointId(blobs);
        expect(blobs.checkpointId).toBe("0x1");
        expect(blobs.addedBlobs).toEqual([]);
        expect(blobs.deletedBlobs).toEqual([]);

        // After the checkpoint, add another reference to C
        checkpointManager.updateBlob("test", undefined, C);
        // No change to the Blobs context (C is already in the checkpoint)
        expect(checkpointManager.getContext()).toEqual(blobs);
        // Remove all but one reference to C
        for (let i = 0; i < refCountC; i++) {
            checkpointManager.updateBlob("test", C, undefined);
            // No change to the Blobs context
            expect(checkpointManager.getContext()).toEqual(blobs);
        }
        // Remove final reference to C
        checkpointManager.updateBlob("test", C, undefined);
        // Now C is no longer in the workspace
        blobs = checkpointManager.getContext();
        expect(blobs.checkpointId).toBe("0x1");
        expect(blobs.addedBlobs).toEqual([]);
        expect(blobs.deletedBlobs).toEqual([C]);

        // Increase the reference count for B (from 1 to 2)
        // So 1 ref was added while B was not in the checkpoint,
        // and 1 ref was added while B was in the checkpoint. This
        // may or may not be interesting to an implementation.
        checkpointManager.updateBlob("test", undefined, B);
        // Generate a new checkpoint
        for (let i = 0; i < checkpointThreshold - 1; i++) {
            checkpointManager.updateBlob("test", undefined, blobNames.pop());
        }
        blobs = await checkpointManager.waitForNextCheckpointId(blobs);
        expect(blobs.checkpointId).toBe("0x2");
        expect(blobs.addedBlobs).toEqual([]);
        expect(blobs.deletedBlobs).toEqual([]);

        // Remove B from the workspace
        checkpointManager.updateBlob("test", B, undefined);
        expect(checkpointManager.getContext()).toEqual(blobs);
        checkpointManager.updateBlob("test", B, undefined);
        expect(checkpointManager.getContext().deletedBlobs).toEqual([B]);

        let workspace = checkpointManager.expandBlobs(checkpointManager.getContext());
        expect(workspace).not.toEqual(expect.arrayContaining([A]));
        expect(workspace).not.toEqual(expect.arrayContaining([B]));
        expect(workspace).not.toEqual(expect.arrayContaining([C]));
    });

    test("Handle feature flag changes", async () => {
        // First, run a normal checkpoint
        const checkpointThreshold = 10;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };
            yield { newCheckpointId: "0x2" };
            yield { newCheckpointId: "0x3" };
        }
        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        // A checkpoint is launched as a side effect of updating blob state.
        const blobs1 = checkpointManager.getContext();
        let blobNames = genBlobNames(checkpointThreshold);
        for (const item of blobNames) {
            checkpointManager.updateBlob("test", undefined, item);
        }

        const blobs2 = await checkpointManager.waitForNextCheckpointId(blobs1);

        // After a checkpoint, we expect the working set to be empty, and the
        // version manager to include all blobs in the current version.
        expect(blobs2.addedBlobs.length).toEqual(0);
        expect(blobs2.deletedBlobs.length).toEqual(0);
        expect(blobs2.checkpointId).toEqual("0x1");
        let s = new Set(checkpointManager.getCheckpointedBlobNames());
        expect(s).toEqual(blobNames);

        // Now, disable checkpointing and verify that the checkpoint manager
        // does not checkpoint.
        checkpointManager.resetCheckpoint();

        const blobs3 = checkpointManager.getContext();
        expect(blobs3.checkpointId).toEqual(undefined);

        blobNames = genBlobNames(checkpointThreshold);
        for (const item of blobNames) {
            checkpointManager.updateBlob("test", undefined, item);
        }

        const blobs4 = await checkpointManager.waitForNextCheckpointId(blobs2);
        expect(blobs4.checkpointId).toEqual(undefined);
    });

    test("adding blob name always triggers queue if checkpoint is big", async () => {
        const checkpointThreshold = 1;
        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);
        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {
            yield { newCheckpointId: "0x1" };
            yield { newCheckpointId: "0x2" };
            yield { newCheckpointId: "0x3" };
        }

        checkpointManager.mockApiServer.checkpointResponse = responseGen();

        const blobs1 = checkpointManager.getContext();

        let blobNames = genBlobNames(checkpointThreshold);
        for (const item of blobNames) {
            checkpointManager.updateBlob("test", undefined, item);
        }

        const blobs2 = await checkpointManager.waitForNextCheckpointId(blobs1);
        expect(blobs2.checkpointId).toEqual("0x1");
        checkpointManager.resetCheckpoint();

        expect(checkpointManager.getContext().checkpointId).toEqual(undefined);
        const blobs3 = checkpointManager.getContext();
        checkpointManager.updateBlob("", undefined, undefined);
        const blobs4 = await checkpointManager.waitForNextCheckpointId(blobs3);
        expect(blobs4.checkpointId).toEqual("0x2");
    });
});
