import * as vscode from "vscode";

import {
    MutableTextDocument,
    publishOpenTextDocument,
    setHandleMissingTextDocument,
    setOpenTextDocuments,
    TextDocument,
    workspace,
} from "../../__mocks__/vscode-mocks";
import {
    backgroundFileScheme,
    enableViewTextDocument,
    viewTextDocument,
} from "../../workspace/view-text-document";

describe("viewTextDocument enabled", () => {
    let enabled: vscode.Disposable;

    beforeEach(() => {
        workspace.reset();
        enabled = enableViewTextDocument();
    });

    afterEach(() => {
        workspace.reset();
        enabled.dispose();
    });

    test("non-file scheme", async () => {
        // All non-file schemes pass through to vscode openTextDocument as-is
        let untitled = new MutableTextDocument(
            vscode.Uri.from({ scheme: "untitled", path: "file1.py" }),
            "this is file 1"
        );
        let custom = new MutableTextDocument(
            vscode.Uri.from({ scheme: "custom", path: "file2.py" }),
            "this is file 2"
        );
        setOpenTextDocuments([untitled, custom]);

        let doc1 = await viewTextDocument(untitled.uri);
        let doc2 = await viewTextDocument(custom.uri);
        expect(doc1).toBe(untitled);
        expect(doc2).toBe(custom);
    });

    test("file scheme custom URI", async () => {
        // Explicit file scheme, as well as string path, will never call vscode openTextDocument
        // with the 'file' scheme. Instead, they use the backgroundFileScheme.
        let targetUri = vscode.Uri.file("file1.py");
        let backgroundOpened = new MutableTextDocument(
            targetUri.with({ scheme: backgroundFileScheme }),
            "this is file 1 stale"
        );
        setOpenTextDocuments([backgroundOpened]);

        let view1 = await viewTextDocument(targetUri);
        expect(view1.getText()).toBe(backgroundOpened.getText());
        let view2 = await viewTextDocument(targetUri.path);
        expect(view2.getText()).toBe(backgroundOpened.getText());
    });

    test("file scheme prefer opened file", async () => {
        // Whenever possible, we prefer to use an already opened document in the
        // file scheme.  This avoids excess reads, and ensures that we can see
        // the most up-to-date contents of the file as they change within VSCode
        // if the document is being edited.
        let file = new MutableTextDocument(vscode.Uri.file("file1.py"), "this is file 1 current");
        let background = new MutableTextDocument(
            vscode.Uri.from({ scheme: backgroundFileScheme, path: "file1.py" }),
            "this is file 1 stale"
        );
        setOpenTextDocuments([file, background]);

        let view1 = await viewTextDocument(file.uri);
        expect(view1.getText()).toBe("this is file 1 current");
        file.update(0, file.getText().length, "this is file 1 updated live");
        expect(view1.getText()).toBe("this is file 1 updated live");
    });

    test("file opened while loading", async () => {
        // If the document is opened in 'file' scheme while we are loading the document
        // in the background scheme, the preference for 'file' document should be honored
        // and yield a view over the 'file' document.
        let resolveBackground: (doc: TextDocument | PromiseLike<TextDocument>) => void = jest.fn();
        setHandleMissingTextDocument(async (_uri) => {
            return new Promise((resolve) => {
                resolveBackground = resolve;
            });
        });

        // Currently there are no open documents; the view call will not settle
        let targetUri = vscode.Uri.file("file1.py");
        let viewPromise = viewTextDocument(targetUri);
        // Want: to be able to assert that the Promise will not settle...

        // Publish an event indicating the target URI was opened independently
        let file = new MutableTextDocument(targetUri, "this is file 1");
        publishOpenTextDocument(file);

        let view = await viewPromise;
        expect(view.getText()).toBe("this is file 1");
        file.update(0, file.getText().length, "this is file 1 updated live");
        expect(view.getText()).toBe("this is file 1 updated live");
        resolveBackground(
            new MutableTextDocument(
                vscode.Uri.from({ scheme: backgroundFileScheme, path: "file1.py" }),
                ""
            )
        );
    });

    test("background load success", async () => {
        let resolveBackground: (doc: TextDocument | PromiseLike<TextDocument>) => void = jest.fn();
        setHandleMissingTextDocument(async (_uri) => {
            return new Promise((resolve) => {
                resolveBackground = resolve;
            });
        });

        // Currently there are no open documents; the view call will not settle
        let targetUri = vscode.Uri.file("file1.py");
        let viewPromise = viewTextDocument(targetUri);
        // Want: to be able to assert that the Promise will not settle...

        // Complete the background load
        resolveBackground(
            new MutableTextDocument(
                vscode.Uri.from({ scheme: backgroundFileScheme, path: "file1.py" }),
                "this is file 1 stale"
            )
        );

        let view = await viewPromise;
        expect(view.getText()).toBe("this is file 1 stale");
    });

    test("background load failure", async () => {
        let rejectBackground: (reason?: any) => void = jest.fn();
        setHandleMissingTextDocument(async (_) => {
            return new Promise((_, reject) => {
                rejectBackground = reject;
            });
        });

        // Currently there are no open documents; the view call will not settle
        let targetUri = vscode.Uri.file("file1.py");
        let viewPromise = viewTextDocument(targetUri);
        // Want: to be able to assert that the Promise will not settle...

        // Complete the background load
        rejectBackground("failed to load file1.py");

        await viewPromise.then(
            (_) => {
                throw new Error("Expected viewTextDocument to reject");
            },
            (err) => {
                expect(err).toBe("failed to load file1.py");
            }
        );
    });
});
