import {
    commands,
    EventEmitter,
    newMockStatusBar,
    StatusBarItem,
    TrackedDisposable,
    window,
} from "../../__mocks__/vscode-mocks";
import { FocusAugmentPanel } from "../../commands/focus-augment-panel";
import { StatusBarManager } from "../../statusbar/status-bar-manager";
import { initialState, syncing } from "../../statusbar/status-bar-states";
import { DisposableService } from "../../utils/disposable-service";
import { StatusBarSyncingReporter } from "../../workspace/status-bar-syncing-reporter";
import { SyncingStatus, SyncingStatusEvent } from "../../workspace/types";

describe("StatusBarSyncingReporter", () => {
    let kit: StatusBarSyncingReporterTestKit;

    beforeEach(() => {
        TrackedDisposable.resetStats();
        kit = new StatusBarSyncingReporterTestKit();
    });

    afterEach(() => {
        kit.dispose();
        TrackedDisposable.assertDisposed();
    });

    test("should set syncing state for existing folders when status is running", () => {
        kit.createReporter();

        // Should show syncing in status bar
        kit.fireSyncStatus({ status: SyncingStatus.running, foldersProgress: [] });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);

        // Marking as done should remove syncing state
        kit.fireSyncStatus({ status: SyncingStatus.done, foldersProgress: [] });
        expect(kit.statusBar.tooltip).toEqual(initialState.tooltip);

        // Should show syncing in status bar again
        kit.fireSyncStatus({ status: SyncingStatus.running, foldersProgress: [] });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);
    });

    test("should focus main panel for long running new folders", () => {
        kit.createReporter();

        const commandSpy = jest.spyOn(commands, "executeCommand");

        const foldersProgress = [
            {
                folderRoot: "example-repo-root",
                progress: {
                    newlyTracked: true,
                    trackedFiles: 100,
                    backlogSize: 100,
                },
            },
        ];

        // Should show syncing in status bar and open augment panel
        kit.fireSyncStatus({ status: SyncingStatus.longRunning, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);
        expect(commandSpy).toHaveBeenCalledTimes(1);
        expect(commandSpy).toHaveBeenCalledWith(FocusAugmentPanel.commandID);

        // Should not re-focus augment panel
        kit.fireSyncStatus({ status: SyncingStatus.longRunning, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);
        expect(commandSpy).toHaveBeenCalledTimes(1);

        // Should continue to show syncing in status bar
        kit.fireSyncStatus({ status: SyncingStatus.running, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);

        // Should end on done
        kit.fireSyncStatus({ status: SyncingStatus.done, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(initialState.tooltip);
    });

    test("should not focus main panel for long running on existing folders", () => {
        kit.createReporter();

        const commandSpy = jest.spyOn(commands, "executeCommand");

        const foldersProgress = [
            {
                folderRoot: "example-repo-root",
                progress: {
                    newlyTracked: false,
                    trackedFiles: 100,
                    backlogSize: 100,
                },
            },
        ];

        // Should show syncing in status bar and open augment panel
        kit.fireSyncStatus({ status: SyncingStatus.longRunning, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);
        expect(commandSpy).not.toHaveBeenCalled();

        // Should continue to show syncing in status bar
        kit.fireSyncStatus({ status: SyncingStatus.longRunning, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);
        expect(commandSpy).not.toHaveBeenCalled();

        // Should continue to show syncing in status bar
        kit.fireSyncStatus({ status: SyncingStatus.running, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(syncing.tooltip);

        // Should end on done
        kit.fireSyncStatus({ status: SyncingStatus.done, foldersProgress });
        expect(kit.statusBar.tooltip).toEqual(initialState.tooltip);
    });
});

class StatusBarSyncingReporterTestKit extends DisposableService {
    private mockStatusBar: StatusBarItem;
    private statusBarManager: StatusBarManager;
    private syncStatusEventEmitter: EventEmitter<SyncingStatusEvent>;

    constructor() {
        super();

        this.mockStatusBar = newMockStatusBar();
        window.createStatusBarItem = jest.fn().mockReturnValue(this.mockStatusBar);

        this.statusBarManager = new StatusBarManager();
        this.syncStatusEventEmitter = new EventEmitter<SyncingStatusEvent>();

        this.addDisposable(this.statusBarManager);
        this.addDisposable(this.syncStatusEventEmitter);
    }

    get statusBar(): StatusBarItem {
        return this.mockStatusBar;
    }

    createReporter() {
        const reporter = new StatusBarSyncingReporter(
            this.statusBarManager,
            this.syncStatusEventEmitter.event
        );
        this.addDisposable(reporter);
        return reporter;
    }

    fireSyncStatus(event: SyncingStatusEvent) {
        this.syncStatusEventEmitter.fire(event);
    }
}
