import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";

import { ViewedContentTracker } from "../../workspace/viewed-content-tracker";
import { WorkspaceManager } from "../../workspace/workspace-manager";

// Mock the WorkspaceManager
jest.mock("../../workspace/workspace-manager");

// Mock the vscode API
jest.mock("vscode", () => {
    return {
        window: {
            onDidChangeActiveTextEditor: jest.fn().mockReturnValue({ dispose: jest.fn() }),
            onDidChangeTextEditorVisibleRanges: jest.fn().mockReturnValue({ dispose: jest.fn() }),
        },
        eventEmitter: jest.fn().mockImplementation(() => ({
            event: jest.fn(),
            fire: jest.fn(),
            dispose: jest.fn(),
        })),
        disposable: {
            from: jest.fn(),
        },
    };
});

// Create a mock QualifiedPathName
class MockQualifiedPathName implements QualifiedPathName {
    constructor(
        public readonly rootPath: string,
        public readonly relPath: string,
        public readonly absPath: string,
        public readonly folderId: number
    ) {}

    equals(other: QualifiedPathName): boolean {
        return this.absPath === other.absPath;
    }
}

describe("ViewedContentTracker", () => {
    let tracker: ViewedContentTracker;
    let mockWorkspaceManager: jest.Mocked<WorkspaceManager>;

    beforeEach(() => {
        jest.useFakeTimers();

        // Create a mock WorkspaceManager
        mockWorkspaceManager = {
            safeResolvePathName: jest.fn(),
            getBlobName: jest.fn(),
            getViewedContentConfig: jest.fn(),
        } as unknown as jest.Mocked<WorkspaceManager>;

        // Setup the mock methods
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });
        mockWorkspaceManager.getBlobName.mockReturnValue("test-blob-name");
        mockWorkspaceManager.getViewedContentConfig.mockReturnValue({
            closeRangeThreshold: 3, // 3 lines
            discreteJumpThreshold: 10, // 10 lines
            minEventAgeMs: 500, // 500ms
            maxEventAgeMs: 30000, // 30 seconds
            maxTrackedFiles: 3, // maxTrackedFiles
            maxSameFileEntries: 2, // maxSameFileEntries
        });

        // Create the tracker with custom settings for testing
        tracker = new ViewedContentTracker(mockWorkspaceManager);
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("should initialize with empty viewed contents", () => {
        expect(tracker.getAllViewedContent()).toHaveLength(0);
        expect(tracker.getPendingContent()).toBeUndefined();
    });

    test("should store next candidate without committing on first event", () => {
        // Create a mock editor
        const mockEditor = createMockEditor("test-file.ts", "test content");

        // Simulate capturing viewed content
        simulateCaptureViewedContent(tracker, mockEditor);

        // Should have a next candidate but no committed content
        expect(tracker.getPendingContent()).toBeDefined();
        expect(tracker.getAllViewedContent()).toHaveLength(0); // No committed content yet

        // The pending content should be the captured content
        const pendingContent = tracker.getPendingContent();
        expect(pendingContent).toBeDefined();
        expect(pendingContent?.relPathName).toBe("test-file.ts");
    });

    test("should commit candidate when file changes", () => {
        // Create mock editors for different files
        const mockEditor1 = createMockEditor("test-file1.ts", "content 1");
        const mockEditor2 = createMockEditor("test-file2.ts", "content 2");

        // Setup mock for first file
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file1.ts", "/root/test-file1.ts", 1);
        });

        // Simulate capturing viewed content for first file
        simulateCaptureViewedContent(tracker, mockEditor1);

        // Wait enough time to meet the 500ms minimum for file switches
        jest.advanceTimersByTime(600);

        // Setup mock for second file
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file2.ts", "/root/test-file2.ts", 1);
        });

        // Simulate capturing viewed content for second file
        simulateCaptureViewedContent(tracker, mockEditor2);

        // Should have committed the first file and have the second as next candidate
        expect(tracker.getPendingContent()?.relPathName).toBe("test-file2.ts");
        expect(tracker.getAllViewedContent()).toHaveLength(1); // Only committed content

        // The committed content should be the first file
        const allContent = tracker.getAllViewedContent();
        expect(allContent[0]?.relPathName).toBe("test-file1.ts");
    });

    test("should not commit candidate when file switch is too quick (under 500ms)", () => {
        // Create mock editors for different files
        const mockEditor1 = createMockEditor("test-file1.ts", "content 1");
        const mockEditor2 = createMockEditor("test-file2.ts", "content 2");

        // Setup mock for first file
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file1.ts", "/root/test-file1.ts", 1);
        });

        // Simulate capturing viewed content for first file
        simulateCaptureViewedContent(tracker, mockEditor1);

        // Wait less than 500ms (too quick)
        jest.advanceTimersByTime(300);

        // Setup mock for second file
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file2.ts", "/root/test-file2.ts", 1);
        });

        // Simulate capturing viewed content for second file
        simulateCaptureViewedContent(tracker, mockEditor2);

        // Should NOT have committed the first file due to short duration
        expect(tracker.getPendingContent()?.relPathName).toBe("test-file2.ts");
        expect(tracker.getAllViewedContent()).toHaveLength(0); // No committed content

        // The pending content should be the second file
        const pendingContent = tracker.getPendingContent();
        expect(pendingContent?.relPathName).toBe("test-file2.ts");
    });

    test("should commit candidate when discrete jump occurs with sufficient time", () => {
        // Create mock editors with different line ranges (discrete jump)
        const mockEditor1 = createMockEditor("test-file.ts", "test content", 0, 10);
        const mockEditor2 = createMockEditor("test-file.ts", "test content", 50, 60); // 40+ line jump

        // Setup mock for the file
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });

        // Simulate capturing viewed content for first position
        simulateCaptureViewedContent(tracker, mockEditor1);

        // Advance time past the minimum threshold
        jest.advanceTimersByTime(1500);

        // Simulate capturing viewed content for second position (discrete jump)
        simulateCaptureViewedContent(tracker, mockEditor2);

        // Should have committed the first event and have the second as pending
        expect(tracker.getAllViewedContent()).toHaveLength(1); // Only committed content
        expect(tracker.getPendingContent()).toBeDefined(); // Second event is pending
    });

    test("should discard close range events", () => {
        // Create mock editors with close line ranges (within threshold)
        const mockEditor1 = createMockEditor("test-file.ts", "test content", 10, 20);
        const mockEditor2 = createMockEditor("test-file.ts", "test content", 12, 22); // Within 3 line threshold

        // Setup mock for the file
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });

        // Simulate capturing viewed content for first position
        simulateCaptureViewedContent(tracker, mockEditor1);

        // Advance time
        jest.advanceTimersByTime(1000);

        // Simulate capturing viewed content for close position (should be discarded)
        simulateCaptureViewedContent(tracker, mockEditor2);

        // Should still have the original pending event, no committed content
        expect(tracker.getAllViewedContent()).toHaveLength(0); // No committed content
        expect(tracker.getPendingContent()).toBeDefined(); // Original event still pending
        expect(tracker.getPendingContent()?.lineStart).toBe(10); // Original position preserved
        expect(tracker.getPendingContent()?.lineEnd).toBe(20);
    });

    test("should update pending event for moderate changes", () => {
        // Create mock editors with moderate line range changes (not close, not discrete jump)
        const mockEditor1 = createMockEditor("test-file.ts", "test content", 10, 20);
        const mockEditor2 = createMockEditor("test-file.ts", "test content", 15, 25); // 5 line change (between 3 and 10)

        // Setup mock for the file
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });

        // Simulate capturing viewed content for first position
        simulateCaptureViewedContent(tracker, mockEditor1);

        // Advance time
        jest.advanceTimersByTime(1000);

        // Simulate capturing viewed content for moderate change position
        simulateCaptureViewedContent(tracker, mockEditor2);

        // Should have updated the pending event, no committed content
        expect(tracker.getAllViewedContent()).toHaveLength(0); // No committed content
        expect(tracker.getPendingContent()).toBeDefined(); // Updated pending event
        expect(tracker.getPendingContent()?.lineStart).toBe(15); // Updated position
        expect(tracker.getPendingContent()?.lineEnd).toBe(25);
    });

    test("should replace similar entries when committing", () => {
        // Create mock editors for the same file with different visible ranges
        const mockEditor1 = createMockEditor("test-file.ts", "content", 0, 10);
        const mockEditor2 = createMockEditor("test-file.ts", "content", 5, 15);
        const mockEditor3 = createMockEditor("test-file.ts", "content", 20, 30);
        const mockEditorDifferentFile = createMockEditor("other-file.ts", "other content");

        // Setup mock for test file for first range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditor1);
        jest.advanceTimersByTime(600); // Wait enough to meet 500ms threshold

        // Setup mock for different file to commit the first range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "other-file.ts", "/root/other-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditorDifferentFile);

        // Setup mock back to test file for second range (overlaps with first)
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditor2);
        jest.advanceTimersByTime(600); // Wait enough to meet 500ms threshold

        // Setup mock for different file to commit the second range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "other-file.ts", "/root/other-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditorDifferentFile);

        // Setup mock back to test file for third range (doesn't overlap)
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditor3);

        // Should have replaced the first range with the second (due to overlap)
        // and kept the third range (no overlap). The third range is still pending.
        // With the 500ms threshold, we expect at least 1 committed entry (the non-overlapping range)
        expect(tracker.getAllViewedContent().length).toBeGreaterThanOrEqual(1);
        expect(tracker.getPendingContent()).toBeDefined(); // Third range is pending
    });

    test("should limit the number of entries for the same file", () => {
        // Create mock editors for the same file with non-overlapping ranges
        const mockEditor1 = createMockEditor("test-file.ts", "content", 0, 10);
        const mockEditor2 = createMockEditor("test-file.ts", "content", 20, 30);
        const mockEditor3 = createMockEditor("test-file.ts", "content", 40, 50);
        const mockEditorDifferentFile = createMockEditor("other-file.ts", "other content", 0, 10);
        const mockEditorDifferentFile2 = createMockEditor("other-file.ts", "content", 20, 40);

        // Setup mock for test file for first range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditor1);
        jest.advanceTimersByTime(600); // Wait enough to meet 500ms threshold

        // Setup mock for different file to commit the first range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "other-file.ts", "/root/other-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditorDifferentFile);

        // Setup mock back to test file for second range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditor2);
        jest.advanceTimersByTime(600); // Wait enough to meet 500ms threshold

        // Setup mock for different file to commit the second range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "other-file.ts", "/root/other-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditorDifferentFile);

        // Setup mock back to test file for third range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "test-file.ts", "/root/test-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditor3);
        jest.advanceTimersByTime(600); // Wait enough to meet 500ms threshold

        // The third entry is not committed yet, so getAllViewedContent will only have 2 committed entries
        const entries = tracker.getAllViewedContent();
        const testFileEntries = entries.filter((e) => e.relPathName === "test-file.ts");
        expect(testFileEntries.length).toBe(2);
        // The third entry should be pending
        expect(tracker.getPendingContent()?.relPathName).toBe("test-file.ts");

        // Simulate capturing and committing for fourth range
        mockWorkspaceManager.safeResolvePathName.mockImplementation(() => {
            return new MockQualifiedPathName("/root", "other-file.ts", "/root/other-file.ts", 1);
        });
        simulateCaptureViewedContent(tracker, mockEditorDifferentFile2);

        // Should have committed the third entry and removed the oldest entry for test-file.ts
        const updatedEntries = tracker.getAllViewedContent();
        const updatedTestFileEntries = updatedEntries.filter(
            (e) => e.relPathName === "test-file.ts"
        );
        expect(updatedTestFileEntries.length).toBe(2);
        const firstEntry = updatedTestFileEntries[0];
        const secondEntry = updatedTestFileEntries[1];
        expect(firstEntry.lineStart).toBe(40);
        expect(firstEntry.lineEnd).toBe(50);
        expect(firstEntry.charStart).toBe(40 * 50); // Mock implementation: line * 50
        expect(firstEntry.charEnd).toBe(50 * 50);
        expect(secondEntry.lineStart).toBe(20);
        expect(secondEntry.lineEnd).toBe(30);
        expect(secondEntry.charStart).toBe(20 * 50);
        expect(secondEntry.charEnd).toBe(30 * 50);
    });
});

// Helper functions
function createMockEditor(
    fileName: string,
    content: string,
    startLine: number = 0,
    endLine: number = 10
): any {
    return {
        document: {
            uri: { fsPath: `/path/to/${fileName}` },
            getText: jest.fn().mockImplementation((range) => {
                // If a range is provided, return a portion of the content
                if (range) {
                    // In a real implementation, this would extract the text from the range
                    // For our test, we'll just return the content
                    return content;
                }
                return content;
            }),
            offsetAt: jest.fn().mockImplementation((position) => {
                // Mock implementation: assume each line has 50 characters
                return position.line * 50 + (position.character || 0);
            }),
        },
        visibleRanges: [
            {
                start: { line: startLine, character: 0 },
                end: { line: endLine, character: 0 },
            },
        ],
    };
}

function simulateCaptureViewedContent(tracker: ViewedContentTracker, editor: any): void {
    // Access the private method using any type
    (tracker as any)._captureViewedContent(editor);
}
