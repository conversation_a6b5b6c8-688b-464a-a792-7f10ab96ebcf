import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import {
    ExtensionContext,
    getWorkspaceFolder,
    MutableTextDocument,
    publishWorkspaceFoldersChange,
} from "../../__mocks__/vscode-mocks";
import { Memento } from "../../__mocks__/vscode-mocks";
import { SimpleMockSyncingPermissionTracker } from "../../__mocks__/workspace/mock-syncing-permission-tracker";
import { AugmentConfigListener } from "../../augment-config-listener";
import { CompletionServer } from "../../completion-server";
import { FeatureFlagManager } from "../../feature-flags";
import { defaultSupportedLanguages } from "../../languages";
import { ActionsModel } from "../../main-panel/action-cards/actions-model";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";
import { OnboardingSessionEventReporter } from "../../metrics/onboarding-session-event-reporter";
import { AugmentGlobalState } from "../../utils/context";
import { DisposableService } from "../../utils/disposable-service";
import { pathNameSansSep } from "../../utils/path-utils";
import { uriToAbsPath, uriToDisplayablePath, validateTrackablePath } from "../../utils/uri";
import { SyncingEnabledTracker } from "../../workspace/syncing-enabled-tracker";
import { SyncingPermissionTracker } from "../../workspace/syncing-permission-tracker";
import { ISourceFolderInfo, SyncingEnabledState } from "../../workspace/types";
import {
    FileChangeDetails,
    WorkspaceManager,
    WorkspaceManagerOptions,
} from "../../workspace/workspace-manager";
import { SourceFolderInfo, SourceFolderType } from "../../workspace/workspace-types";
import { verifyDefined } from "../__utils__/test-utils";
import { advanceTimeUntilResolve, advanceTimeUntilTrue } from "../__utils__/time";
import { MockExternalSourceFolderRecorder } from "./mock-external-workspace-folder-recorder";

export enum MockSourceFolderType {
    workspaceFolder,
    externalFolder,
}

// SourceFolderChangeWatcher is a type that contains an array of source folders reported by the
// workspace manager.
export type SourceFolderChangeWatcher = {
    sourceFolders: Array<ISourceFolderInfo>;
};

// FileChangeWatcher is a type that contains an array of FileChangeDetails events reported by the
// workspace manager.
export type FileChangeWatcher = {
    changedFiles: Array<FileChangeDetails>;
};

// SidecarFileMovedWatcher is a type that contains an array of FileDidMoveEvent events reported by the
// workspace manager.
export type SidecarFileMovedWatcher = {
    movedFiles: Array<FileDidMoveEvent>;
};

// SidecarFileDeletedWatcher is a type that contains an array of FileDeletedEvent events reported by the
// workspace manager.
export type SidecarFileDeletedWatcher = {
    deletedFiles: Array<FileDeletedEvent>;
};

export type FolderContentsChangeWatcher = {
    count: number;
};

// MockSourceFolder is a type that describes a mock source folder.
// * sourceFolderType: the type of the source folder
// * folderRootUri: the root of the source folder
// * repoRootUri: the root of the repo containing the source folder
// * files: a map of relative path names to file contents
// * ignoredFiles: a map of relative path names to file contents. These files are ignored by
//   Augment.
export type MockSourceFolder = {
    sourceFolderType: MockSourceFolderType;
    folderRootUri: vscode.Uri;
    repoRootUri: vscode.Uri;
    files: Map<string, string>;
    ignoredFiles?: Map<string, string>;
};

// MockSourceFolderState extends MockSourceFolder with a flag that indicates whether the source
// folder should be tracked.
export type MockSourceFolderState = MockSourceFolder & {
    tracked: boolean;
};

// defaultWorkspaceFolder is an example source folder.
export const defaultWorkspaceFolder: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/project"),
    repoRootUri: vscode.Uri.file("/src/project"),
    files: new Map<string, string>([
        ["file1.py", "this is file 1"],
        ["file2.py", "this is file 2"],
    ]),
    ignoredFiles: new Map<string, string>([[".git/logs/HEAD", "head file"]]),
};

// defaultWorkspaceState is an example workspace state that consists of just defaultWorkspaceFolder.
export const defaultWorkspaceState = [{ ...defaultWorkspaceFolder, tracked: true }];

// makeQualifiedPath is a function that creates a qualified path name from a source folder and a
// relative path name.
export function makeQualifiedPath(
    sourceFolder: MockSourceFolder,
    relPath: string
): QualifiedPathName {
    return new QualifiedPathName(uriToAbsPath(sourceFolder.repoRootUri), relPath);
}

// makeAbsPath is a function that creates an absolute path name from a source folder and a relative
// path name.
export function makeAbsPath(sourceFolder: MockSourceFolder, relPath: string): string {
    return makeQualifiedPath(sourceFolder, relPath).absPath;
}

// createWorkspaceFiles is a function that creates the files needed by the given source folders,
// including a .augmentroot file in the source folder's repo root.
export function createWorkspaceFiles(
    sourceFolders: Array<MockSourceFolder>,
    sentinelFile: string | undefined = ".augmentroot"
) {
    sourceFolders.forEach((sourceFolder) => {
        createSourceFolderFiles(sourceFolder, sentinelFile);
    });
}

export function pathNameInSourceFolder(sourceFolder: MockSourceFolder, relPath: string): string {
    return joinPath(uriToAbsPath(sourceFolder.repoRootUri), relPath);
}

// createSourceFolderFiles is a function that creates the files needed by the given source folder,
// including a sentinel in the source folder's repo root. If `sentinelName` ends with a slash, (eg.
// ".git/") this function creates a directory with that name. Otherwise, it creates an empty file.
export function createSourceFolderFiles(
    sourceFolder: MockSourceFolder,
    sentinelName: string | undefined = ".augmentroot"
) {
    if (sentinelName !== undefined) {
        const absPath = pathNameInSourceFolder(sourceFolder, sentinelName);
        if (sentinelName.endsWith("/")) {
            const dirName = pathNameSansSep(absPath);
            mockFSUtils.makeDirs(dirName);
        } else {
            mockFSUtils.writeFileUtf8(absPath, "", true);
        }
    }
    createFiles(sourceFolder.folderRootUri.fsPath, sourceFolder.files);
    if (sourceFolder.ignoredFiles !== undefined) {
        createFiles(sourceFolder.folderRootUri.fsPath, sourceFolder.ignoredFiles);
    }
}

// createFiles is a function that creates the files given by `files`.
function createFiles(folderRoot: string, files: Map<string, string>) {
    for (const [pathName, contents] of files) {
        writeFileInFolder(folderRoot, pathName, contents);
    }
}

export function writeFileInFolder(folderRoot: string, relPath: string, contents: string) {
    mockFSUtils.writeFileUtf8(joinPath(folderRoot, relPath), contents, true);
}

// setupWorkspace is a function that creates the given set of vscode workspace folders and
// publishes an event announcing the new folders. The files needed by the source folders should
// already have been created with createSourceWorkspaceFiles. (Or use the initWorkspace convenience
// function below.)
export function setupWorkspace(
    sourceFolders: Array<MockSourceFolder>,
    externalSourceFolderRecorder?: MockExternalSourceFolderRecorder
) {
    const workspaceFolders = new Array<vscode.WorkspaceFolder>();
    const externalFolders = new Map<string, string>();
    for (const sourceFolder of sourceFolders) {
        const rootUri = sourceFolder.folderRootUri;
        if (sourceFolder.sourceFolderType === MockSourceFolderType.workspaceFolder) {
            workspaceFolders.push(getWorkspaceFolder(rootUri));
        } else {
            externalFolders.set(uriToAbsPath(rootUri), uriToDisplayablePath(rootUri));
        }
    }
    if (externalSourceFolderRecorder !== undefined) {
        externalSourceFolderRecorder.setFoldersSync(externalFolders);
    }
    sourceFolders.map((sourceFolder) => {
        return getWorkspaceFolder(sourceFolder.folderRootUri);
    });
    publishWorkspaceFoldersChange(workspaceFolders);
}

// initWorkspace is a convenience function that initializes a mock vscode workspace by creating
// the files needed by the given source folders and the corresponding vscode workspace folders.
// It then publishes an event announcing the new folders.
export function initWorkspace(
    sourceFolders: Array<MockSourceFolder>,
    externalSourceFolderRecorder?: MockExternalSourceFolderRecorder
) {
    createWorkspaceFiles(sourceFolders);
    setupWorkspace(sourceFolders, externalSourceFolderRecorder);
}

// awaitStartup waits for the given workspace manager to start and to report the given workspace
// state.
export async function awaitStartup(
    workspaceManager: WorkspaceManager,
    workspaceState: Array<MockSourceFolderState>
): Promise<void> {
    await advanceTimeUntilResolve(workspaceManager.awaitInitialFoldersSynced());
    const verified = verifyWorkspaceState(workspaceManager, workspaceState);
    expect(verified).toBe(true);
}

// applyWorkspaceState applies the given workspace state to the mock vscode workspace. It does
// not wait for the workspace manager to report the new state. Use awaitWorkspaceState for that
// purpose.
export function applyWorkspaceState(
    workspaceManager: WorkspaceManager,
    sourceFolders: Array<MockSourceFolder>
) {
    const workspaceFolders = new Array<vscode.WorkspaceFolder>();
    const externalFolders = new Map<string, vscode.Uri>();
    for (const sourceFolder of sourceFolders) {
        const rootUri = sourceFolder.folderRootUri;
        if (sourceFolder.sourceFolderType === MockSourceFolderType.workspaceFolder) {
            workspaceFolders.push(getWorkspaceFolder(rootUri));
        } else {
            externalFolders.set(uriToAbsPath(rootUri), rootUri);
        }
    }

    const existingExternalFolders = new Set<string>(
        workspaceManager
            .listSourceFolders()
            .filter(
                (folder) =>
                    folder.type === SourceFolderType.externalFolder ||
                    folder.type === SourceFolderType.nestedExternalFolder
            )
            .map((folder) => folder.folderRoot)
    );
    for (const [folderRoot, rootUri] of externalFolders) {
        if (!existingExternalFolders.has(folderRoot)) {
            workspaceManager.addExternalSourceFolder(rootUri);
        }
    }
    for (const folderRoot of existingExternalFolders) {
        if (!externalFolders.has(folderRoot)) {
            workspaceManager.removeExternalSourceFolder(folderRoot);
        }
    }

    sourceFolders.map((sourceFolder) => {
        return getWorkspaceFolder(sourceFolder.folderRootUri);
    });
    publishWorkspaceFoldersChange(workspaceFolders);
}

// awaitWorkspaceState is a function that waits for the workspace manager to report the given
// workspace state.
export async function awaitWorkspaceState(
    workspaceManager: WorkspaceManager,
    workspaceState: Array<MockSourceFolderState>
): Promise<void> {
    await advanceTimeUntilTrue(() => verifyWorkspaceState(workspaceManager, workspaceState));
}

// verifyWorkspaceState is a function that returns true if the workspace manager reports exactly
// the given workspace state (nothing more, nothing less). Otherwise it returns false.
export function verifyWorkspaceState(
    workspaceManager: WorkspaceManager,
    workspaceState: Array<MockSourceFolderState>
): boolean {
    const reportedFolderNames = new Set<string>(
        Array.from(workspaceManager.listSourceFolders().map((folder) => folder.folderRoot))
    );
    if (reportedFolderNames.size !== workspaceState.length) {
        return false;
    }
    const reportedFolders = new Map<string, SourceFolderInfo>(
        Array.from(
            workspaceManager.listSourceFolders().map((folder) => [folder.folderRoot, folder])
        )
    );
    if (reportedFolders.size !== workspaceState.length) {
        return false;
    }
    for (const folderState of workspaceState) {
        const folderRoot = validateTrackablePath(folderState.folderRootUri);
        verifyDefined(folderRoot);
        if (!reportedFolderNames.has(folderRoot)) {
            return false;
        }
        const repoRoot = workspaceManager.unitTestOnlyGetRepoRoot(folderRoot);
        const folderInfo = reportedFolders.get(folderRoot);
        if (folderState.tracked) {
            if (repoRoot !== uriToAbsPath(folderState.repoRootUri)) {
                return false;
            }
            if (
                folderInfo?.type !== SourceFolderType.vscodeWorkspaceFolder &&
                folderInfo?.type !== SourceFolderType.externalFolder
            ) {
                return false;
            }
        } else {
            if (repoRoot !== undefined) {
                return false;
            }
            if (
                folderInfo?.type !== SourceFolderType.nestedWorkspaceFolder &&
                folderInfo?.type !== SourceFolderType.nestedExternalFolder &&
                folderInfo?.type !== SourceFolderType.untrackedFolder
            ) {
                return false;
            }
        }
        if (folderState.sourceFolderType === MockSourceFolderType.workspaceFolder) {
            if (
                folderInfo?.type !== SourceFolderType.vscodeWorkspaceFolder &&
                folderInfo?.type !== SourceFolderType.nestedWorkspaceFolder &&
                folderInfo?.type !== SourceFolderType.untrackedFolder
            ) {
                return false;
            }
        } else {
            if (
                folderInfo?.type !== SourceFolderType.externalFolder &&
                folderInfo?.type !== SourceFolderType.nestedExternalFolder &&
                folderInfo?.type !== SourceFolderType.untrackedFolder
            ) {
                return false;
            }
        }
        const backlog = workspaceManager.unitTestOnlySourceFolderBacklog(folderRoot);
        if (folderState.tracked && backlog !== 0) {
            return false;
        }
        if (!folderState.tracked && backlog !== undefined) {
            return false;
        }
    }
    return true;
}

export function pathHasBlobName(
    workspaceManager: WorkspaceManager,
    workspaceFolder: MockSourceFolder,
    relPath: string
): boolean {
    const qualifiedPathName = makeQualifiedPath(workspaceFolder, relPath);
    return workspaceManager.getBlobName(qualifiedPathName) !== undefined;
}

export async function drainWorkspaceBacklog(workspaceManager: WorkspaceManager) {
    const folders = workspaceManager.trackedSourceFolderNames();
    for (const folder of folders) {
        await advanceTimeUntilTrue(
            () => workspaceManager.unitTestOnlySourceFolderBacklog(folder.folderRoot) === 0
        );
    }
}

export type WorkspaceManagerTestKitParams = {
    syncingPermissionTracker?: SyncingPermissionTracker;
    workspaceStorage?: Memento;
};

// WorkspaceManagerTestKit is a test kit that implements common functionality for WorkspaceManager
// unit tests. It can also be used as a base class for test kits for components that use
// WorkspaceManager.
export class WorkspaceManagerTestKit extends DisposableService {
    static readonly storageUri = vscode.Uri.file("/extension/storage");

    public readonly workspaceStorage;
    public readonly context: ExtensionContext;
    public readonly globalState: AugmentGlobalState;
    public readonly apiServer = new MockAPIServer();
    public readonly actionsModel: ActionsModel;
    public readonly configListener = new AugmentConfigListener();
    public readonly featureFlagManager = new FeatureFlagManager();
    public readonly clientMetricsReporter = new ClientMetricsReporter(this.apiServer);
    public readonly externalSourceFolderRecorder: MockExternalSourceFolderRecorder;
    public readonly syncingPermissionTracker: SyncingPermissionTracker;
    public readonly completionServer = new CompletionServer(this.apiServer, 1000, 1000, 1000);
    public readonly blobNameCalculator = new BlobNameCalculator(10000);
    public readonly syncingEnabledTracker = new SyncingEnabledTracker();
    public readonly onboardingSessionEventReporter = new OnboardingSessionEventReporter(
        this.apiServer
    );

    constructor(public testParams: WorkspaceManagerTestKitParams = {}) {
        super();
        this.workspaceStorage = testParams.workspaceStorage ?? new Memento();
        this.context = new ExtensionContext(this.workspaceStorage);
        this.globalState = new AugmentGlobalState(this.context);
        this.actionsModel = new ActionsModel(this.globalState);
        this.externalSourceFolderRecorder = new MockExternalSourceFolderRecorder();
        this.syncingPermissionTracker =
            testParams.syncingPermissionTracker ?? new SimpleMockSyncingPermissionTracker();
        this.addDisposables(
            this.configListener,
            this.featureFlagManager,
            this.syncingEnabledTracker
        );
    }

    public setupWorkspace(sourceFolders: Array<MockSourceFolder>) {
        setupWorkspace(sourceFolders, this.externalSourceFolderRecorder);
    }

    public initWorkspace(sourceFolders: Array<MockSourceFolder>) {
        initWorkspace(sourceFolders, this.externalSourceFolderRecorder);
    }

    public createWorkspaceManager(options?: WorkspaceManagerOptions): WorkspaceManager {
        const workspaceManager = new WorkspaceManager(
            this.actionsModel,
            this.externalSourceFolderRecorder,
            this.syncingPermissionTracker,
            { storageUri: WorkspaceManagerTestKit.storageUri },
            this.apiServer,
            this.configListener,
            this.featureFlagManager,
            this.clientMetricsReporter,
            this.completionServer,
            this.blobNameCalculator,
            10000,
            this.syncingEnabledTracker,
            this.onboardingSessionEventReporter,
            defaultSupportedLanguages,
            options
        );
        this.addDisposable(workspaceManager);
        return workspaceManager;
    }

    public async enableSyncing(): Promise<void> {
        this.syncingEnabledTracker.enableSyncing();
        await advanceTimeUntilTrue(() => {
            return this.syncingEnabledTracker.syncingEnabledState === SyncingEnabledState.enabled;
        });
    }

    public async disableSyncing(): Promise<void> {
        this.syncingEnabledTracker.disableSyncing();
        await advanceTimeUntilTrue(() => {
            return this.syncingEnabledTracker.syncingEnabledState === SyncingEnabledState.disabled;
        });
    }

    public computeBlobName(sourceFolder: MockSourceFolder, relPath: string) {
        const absPath = makeAbsPath(sourceFolder, relPath);
        const contents = mockFSUtils.readFileRaw(absPath);
        return this.blobNameCalculator.calculate(relPath, contents)!;
    }

    public writeFile(sourceFolder: MockSourceFolder, relPath: string, contents: string): string {
        const absPath = pathNameInSourceFolder(sourceFolder, relPath);
        mockFSUtils.writeFileUtf8(absPath, contents, true);
        return this.blobNameCalculator.calculate(relPath, contents)!;
    }

    public deleteFile(sourceFolder: MockSourceFolder, relPath: string) {
        const absPath = pathNameInSourceFolder(sourceFolder, relPath);
        mockFSUtils.deleteFile(absPath);
    }

    public async awaitBlobName(
        workspaceManager: WorkspaceManager,
        qualifiedPathName: QualifiedPathName,
        blobName: string | undefined
    ) {
        await advanceTimeUntilTrue(
            () => workspaceManager.getBlobName(qualifiedPathName) === blobName
        );
    }

    public createSourceFolderChangeWatcher(
        workspaceManager: WorkspaceManager
    ): SourceFolderChangeWatcher {
        const watcher = {
            sourceFolders: new Array<ISourceFolderInfo>(),
        };
        const disposable = workspaceManager.onDidChangeSourceFolders(() => {
            watcher.sourceFolders = workspaceManager.trackedSourceFolderNames();
        });
        this.addDisposable(disposable);
        return watcher;
    }

    public createFolderContentsChangeWatcher(
        workspaceManager: WorkspaceManager
    ): FolderContentsChangeWatcher {
        const watcher = {
            count: 0,
        };
        const disposable = workspaceManager.onDidChangeSourceFolderContents(() => {
            watcher.count++;
        });
        this.addDisposable(disposable);
        return watcher;
    }

    public createFileChangeWatcher(workspaceManager: WorkspaceManager): FileChangeWatcher {
        const watcher = {
            changedFiles: new Array<FileChangeDetails>(),
        };
        const disposable = workspaceManager.onDidChangeFile((fileChangeDetails) => {
            watcher.changedFiles.push(fileChangeDetails);
        });
        this.addDisposable(disposable);
        return watcher;
    }

    public createSidecarFileMovedWatcher(
        workspaceManager: WorkspaceManager
    ): SidecarFileMovedWatcher {
        const watcher = {
            movedFiles: new Array<FileDidMoveEvent>(),
        };
        const disposable = workspaceManager.onFileDidMove((event) => {
            watcher.movedFiles.push(event);
        });
        this.addDisposable(disposable);
        return watcher;
    }

    public createSidecarFileDeletedWatcher(
        workspaceManager: WorkspaceManager
    ): SidecarFileDeletedWatcher {
        const watcher = {
            deletedFiles: new Array<FileDeletedEvent>(),
        };
        const disposable = workspaceManager.onFileDeleted((event) => {
            watcher.deletedFiles.push(event);
        });
        this.addDisposable(disposable);
        return watcher;
    }

    // newDocument creates a new MutableTextDocument with the given contents but no underlying file.
    public newDocument(
        sourceFolder: MockSourceFolder,
        relPath: string,
        contents: string
    ): MutableTextDocument {
        const absPath = makeAbsPath(sourceFolder, relPath);

        return new MutableTextDocument(vscode.Uri.file(absPath), contents);
    }

    // openDocument creates a new MutableTextDocument with the contents of the given file.
    public openDocument(sourceFolder: MockSourceFolder, relPath: string): MutableTextDocument {
        const absPath = makeAbsPath(sourceFolder, relPath);
        const contents = mockFSUtils.readFileUtf8(absPath);
        verifyDefined(contents);
        return new MutableTextDocument(vscode.Uri.file(absPath), contents);
    }
}
