import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { expect, test } from "@jest/globals";
import * as vscode from "vscode";

import { FSIterator, MockFSUtils, mockFSUtils } from "../../__mocks__/fs-utils";
import { MockAcceptPath } from "../../__mocks__/mock-accept-path";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { FindMissingResult, UploadBlob } from "../../augment-api";
import { FileReader } from "../../file-reader";
import { disposableDelayer } from "../../utils/promise-utils";
import { FileType, StatInfo } from "../../utils/types";
import { DiskFileManager } from "../../workspace/disk-file-manager";
import { PathHandlerImpl } from "../../workspace/path-handler";
import { PathMap } from "../../workspace/path-map";
import { verifyDefined } from "../__utils__/test-utils";
import { advanceTimeUntilTrue } from "../__utils__/time";

/**
 * DiskFileManagerTestKit is a test kit that implements common functionality for
 * DiskFileManager unit tests.
 */
class DiskFileManagerTestKit implements FileReader, vscode.Disposable {
    // Arbitrary max file size and find missing batch size.
    static readonly maxFileSize = 1000;
    static readonly defaultProbeBatchSize = 4;

    static readonly mtimeCacheDirUri = vscode.Uri.file("/cache/mtimes");

    public readonly apiServer: MockAPIServer;
    public readonly blobNameCalculator = new BlobNameCalculator(DiskFileManagerTestKit.maxFileSize);
    public readonly pathMap = new PathMap();

    private _readDelay: Promise<void> | undefined;
    public _failReads = new Set<string>();

    private _diskFileManagers = new Array<DiskFileManager>();

    constructor(public readonly maxProbeBatchSize = DiskFileManagerTestKit.defaultProbeBatchSize) {
        this.apiServer = new MockAPIServer(DiskFileManagerTestKit.maxFileSize, maxProbeBatchSize);
    }

    public dispose() {
        for (const diskFileManager of this._diskFileManagers) {
            diskFileManager.dispose();
        }
        this._diskFileManagers.length = 0;
    }

    public async runTimers() {
        await jest.runOnlyPendingTimersAsync();
    }

    public toAbsPath(folderId: number, relPath: string): string {
        const repoRoot = this.pathMap.getRepoRoot(folderId);
        verifyDefined(repoRoot);
        return joinPath(repoRoot, relPath);
    }

    public createFolderContents(folderId: number, files = new Map<string, string>()): number {
        for (const [relPath, contents] of files) {
            this.writeFile(folderId, relPath, contents);
            this.verifyFile(folderId, relPath, contents);
        }
        return folderId;
    }

    public makeDiskFileManager(): DiskFileManager {
        const diskFileManager = new DiskFileManager(
            "test workspace",
            this.apiServer,
            new PathHandlerImpl(DiskFileManagerTestKit.maxFileSize, this),
            this.pathMap,
            this.maxProbeBatchSize
        );

        this._diskFileManagers.push(diskFileManager);
        return diskFileManager;
    }

    public get fs(): MockFSUtils {
        return mockFSUtils;
    }

    readFile(folderId: number, relPath: string): Uint8Array | undefined {
        const absPath = this.toAbsPath(folderId, relPath);
        if (this._failReads.has(absPath)) {
            return undefined;
        }
        try {
            return this.fs.readFileRaw(absPath);
        } catch {
            return undefined;
        }
    }

    // failFile causes `readFile` of the given folderId/relPath to return undefined.
    failFile(folderId: number, relPath: string): void {
        this._failReads.add(this.toAbsPath(folderId, relPath));
    }

    // delayReads causes all `read` calls to wait until the returned disposable is disposed.
    public delayReads(): vscode.Disposable {
        const [promise, disp] = disposableDelayer();
        this._readDelay = promise;
        return new vscode.Disposable(() => {
            this._readDelay = undefined;
            disp.dispose();
        });
    }

    // FileReader
    async read(absPath: string, _maxFileSize?: number): Promise<Uint8Array | undefined> {
        if (this._failReads.has(absPath)) {
            return Promise.resolve(undefined);
        }
        try {
            if (this._readDelay) {
                await this._readDelay;
            }

            // Read the file contents here instead of returning the Promise from
            // readFile. This ensures that any exceptions encountered during the
            // read will be caught by this try-block.
            const contents = this.fs.readFileRaw(absPath);
            return contents;
        } catch {
            return Promise.resolve(undefined);
        }
    }

    // FileReader
    stat(absPath: string): StatInfo | undefined {
        try {
            return this.fs.statFile(absPath);
        } catch {
            return undefined;
        }
    }

    // writeFile creates or updates the given pathName to have the given contents.
    // It returns the resulting file's blob name.
    writeFile(folderId: number, relPath: string, text: string): string {
        const absPath = this.toAbsPath(folderId, relPath);
        this.fs.writeFileUtf8(absPath, text, true);
        return this.blobNameCalculator.calculate(relPath, text)!;
    }

    // verifyFile verifies that the given pathName has the given contents.
    verifyFile(folderId: number, relPath: string, expectedText: string) {
        const absPath = this.toAbsPath(folderId, relPath);
        this.fs.verifyFileUtf8(absPath, expectedText);
    }

    // arbitraryPath returns an arbitrary existing path name below the given rootDir and its
    // corresponding blob name.
    arbitraryPath(folderId: number): [string, string] {
        const repoRoot = this.pathMap.getRepoRoot(folderId);
        const pathIterator = this.createFSIterator(repoRoot);
        for (const [_fileUri, relPath] of pathIterator.next()) {
            return [relPath, this.calculateBlobName(folderId, relPath)];
        }
        throw new Error("No paths exist");
    }

    deleteFile(folderId: number, relPath: string) {
        const absPath = this.toAbsPath(folderId, relPath);
        this.fs.deleteFile(absPath);
    }

    // createFSIterator returns an iterator over the mock filesystem starting at the given rootDir,
    // or a `/` if no rootDir is given.
    createFSIterator(rootDir?: string): FSIterator {
        return this.fs.createIterator(rootDir);
    }

    async awaitQuiesced(diskFileManager: DiskFileManager) {
        while (diskFileManager.itemsInFlight > 0) {
            await this.runTimers();
        }

        // This should do nothing, since DiskFileManager just reported 0 items in flight. It is
        // just for extra coverage.
        await diskFileManager.awaitQuiesced();
    }

    // ingestAllPaths submits all paths in the folder indicated by folderId to be uploaded and waits
    // for the DiskFileManager's queue to become empty.
    async ingestAllPaths(folderId: number, diskFileManager: DiskFileManager) {
        const repoRoot = this.pathMap.getRepoRoot(folderId);
        const pathIterator = this.createFSIterator(repoRoot);
        const acceptPath = new MockAcceptPath();
        for (const [_fileUri, relPath] of pathIterator.next()) {
            this.pathMap.insert(folderId, relPath, FileType.file, acceptPath);
            diskFileManager.ingestPath(folderId, relPath);
        }

        await this.awaitQuiesced(diskFileManager);

        for (const [_fileUri, relPath] of pathIterator.next()) {
            const blobInfo = this.getBlobInfo(folderId, relPath);
            expect(blobInfo).toBeDefined();
            const pathMapBlobName = blobInfo?.[0];
            const calculatedBlobName = this.calculateBlobName(folderId, relPath);
            expect(pathMapBlobName).toBe(calculatedBlobName);
            const contentSeq = blobInfo?.[1];
            expect(contentSeq).toBeGreaterThan(0);
        }
    }

    // collectFolderBlobNames returns a map of path name to blob name for all paths in the given
    // folder.
    public collectFolderBlobNames(folderId: number): Map<string, string> {
        const repoRoot = this.pathMap.getRepoRoot(folderId);
        const blobNames = new Map<string, string>();
        const iterator = this.createFSIterator(repoRoot);

        for (const [_fileUri, relPath] of iterator.next()) {
            const blobName = this.safeCalculateBlobName(folderId, relPath);
            verifyDefined(blobName);
            blobNames.set(relPath, blobName);
        }
        return blobNames;
    }

    // verifyPathMap verifies that the PathMap contains the expected blob names for all paths in
    // the given folder.
    public verifyPathMap(folderId: number) {
        // 1. Make sure that all files in the folder are in the path map.
        const folderBlobNames = this.collectFolderBlobNames(folderId);
        for (const [relPath, blobName] of folderBlobNames.entries()) {
            expect(this.pathMap.getBlobName(folderId, relPath)).toBe(blobName);
        }

        // 2. Make sure that all entries in the path map are for real files in the folder.
        for (const [blobFolderId, _r, relPath, _, blobName] of this.pathMap.pathsWithBlobNames()) {
            if (blobFolderId !== folderId) {
                continue;
            }
            expect(folderBlobNames.get(relPath)).toBe(blobName);
            const pathNames = this.pathMap.getAllPathNames(blobName);
            const pathName = pathNames.find((qualifiedPath) => qualifiedPath.relPath === relPath);
            expect(pathName).toBeDefined();
        }
    }

    getBlobInfo(folderId: number, relPath: string): [string, number] | undefined {
        const statInfo = this.stat(this.toAbsPath(folderId, relPath));
        verifyDefined(statInfo);
        return this.pathMap.getBlobInfo(folderId, relPath, statInfo.mtime);
    }

    // calculateBlobName calculates the blob name for the given path name. It throws if the file
    // is not found/readable.
    calculateBlobName(folderId: number, relPath: string): string {
        const absPath = this.toAbsPath(folderId, relPath);
        const contents = this.fs.readFileRaw(absPath);
        return this.blobNameCalculator.calculate(relPath, contents)!;
    }

    // safeCalculateBlobName returns the blob name for the given path name, or undefined if the
    // file is not found/readable.
    safeCalculateBlobName(folderId: number, relPath: string): string | undefined {
        try {
            return this.calculateBlobName(folderId, relPath);
        } catch {
            return undefined;
        }
    }
}

const defaultFiles = new Map<string, string>([
    ["rich", "Rich is the one with glasses"],
    ["mark", "Mark is the one without glasses"],
]);

const defaultFolderRoot = "/src";

describe("DiskFileManager", () => {
    let kit: DiskFileManagerTestKit;
    let pathMap: PathMap;
    let folderId: number;

    beforeEach(() => {
        jest.useFakeTimers();
        // Clean out the mock filesystem before each test
        mockFSUtils.reset();

        kit = new DiskFileManagerTestKit();
        pathMap = kit.pathMap;
        folderId = pathMap.openSourceFolder(defaultFolderRoot, defaultFolderRoot);
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("pipeline", async () => {
        const relPath = "file0";
        const files = new Map<string, string>([[relPath, "this is file 0"]]);
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();
        const blobName = kit.calculateBlobName(folderId, relPath);
        kit.apiServer.onMemorize(relPath, (pathName: string, blobName: string) => {
            kit.apiServer.nonindexedBlobNames.add(blobName);
            return false;
        });

        expect(kit.apiServer.getBlobName(relPath)).toBeUndefined();

        // Begin ingesting the path, but don't wait for it to complete (no "await")
        kit.ingestAllPaths(folderId, diskFileManager);

        // Wait for the file to be uploaded, at which time it will begin to be reported as
        // nonindexed. The blob manager should still not have its blob name.
        while (kit.apiServer.getBlobName(relPath) === undefined) {
            await kit.runTimers();
        }
        expect(pathMap.getBlobName(folderId, relPath)).toBe(undefined);

        // Wait for the file to have been reported as nonindexed a few times. The blob manager
        // should still not have its blob name.
        let findMissingCount = kit.apiServer.findMissing.mock.calls.length;
        let target = findMissingCount + 3;
        while (kit.apiServer.findMissing.mock.calls.length < target) {
            await kit.runTimers();
        }
        expect(pathMap.getBlobName(folderId, relPath)).toBe(undefined);

        // Start reporting the file as indexed, after which the blob manager should begin
        // reporting its blob name.
        kit.apiServer.nonindexedBlobNames.delete(blobName);
        while (pathMap.getBlobName(folderId, relPath) === undefined) {
            await kit.runTimers();
        }
    });

    // `update file` verifies that when a file is updated, the DiskFileManager replaces its
    // original blob name with the new name.
    test("update file", async () => {
        kit.createFolderContents(folderId, defaultFiles);
        const diskFileManager = kit.makeDiskFileManager();
        await kit.ingestAllPaths(folderId, diskFileManager);

        // Choose a file and update its contents
        const [relPath, origBlobName] = kit.arbitraryPath(folderId);
        expect(pathMap.getBlobName(folderId, relPath)).toBe(origBlobName);
        const newContents = "new file contents";
        const newBlobName = kit.writeFile(folderId, relPath, newContents);
        expect(pathMap.getBlobName(folderId, relPath)).not.toBe(newBlobName);
        diskFileManager.ingestPath(folderId, relPath);
        await kit.awaitQuiesced(diskFileManager);

        // The context should now have the new name and not the old name.
        expect(pathMap.getBlobName(folderId, relPath)).toBe(newBlobName);
    });

    // `ingest deleted file` verifies that the DiskFileManager will invalidate a path name
    // in the path map if the "compute" step can't find the file.
    test("ingest deleted file", async () => {
        kit.createFolderContents(folderId, defaultFiles);
        const diskFileManager = kit.makeDiskFileManager();
        await kit.ingestAllPaths(folderId, diskFileManager);

        // Choose a file and remove it from the filesystem. Then tell the
        // DiskFileManager to ingest the file.
        const [relPath, blobName] = kit.arbitraryPath(folderId);
        expect(pathMap.getBlobName(folderId, relPath)).toBe(blobName);
        kit.deleteFile(folderId, relPath);
        diskFileManager.ingestPath(folderId, relPath);
        await kit.awaitQuiesced(diskFileManager);

        expect(pathMap.shouldTrack(folderId, relPath)).toBe(true);
        expect(pathMap.getBlobName(folderId, relPath)).toBe(undefined);
    });

    // `ingest unreadable file` verifies that the DiskFileManager will invalidate a path name
    // in the path map if the "compute" step cannot read the file.
    test("ingest unreadable file", async () => {
        const unreadablePath = "unreadable";
        const readablePath = "readable";
        const files = new Map<string, string>([
            [unreadablePath, "test will fail to read this file"],
            [readablePath, "test can read this file"],
        ]);
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();
        kit.failFile(folderId, unreadablePath);

        await kit.ingestAllPaths(folderId, diskFileManager);

        expect(pathMap.shouldTrack(folderId, unreadablePath)).toBe(true);
        expect(pathMap.getBlobName(folderId, unreadablePath)).toBeUndefined();
        expect(pathMap.shouldTrack(folderId, readablePath)).toBe(true);
        expect(pathMap.getBlobName(folderId, readablePath)).toBeDefined();
    });

    // `upload deleted file` verifies that the DiskFileManager will invalidate a path name
    // in the path map if the "upload" operation finds that it has been deleted
    // from the filesystem.
    test("upload deleted file", async () => {
        const relPath = "to-delete";
        const files = new Map<string, string>([
            [relPath, "test will delete this file before it gets uploaded"],
        ]);
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();
        const blobName = kit.calculateBlobName(folderId, relPath);

        const [promise, disp] = disposableDelayer();
        let findMissingWaiting = false;
        kit.apiServer.findMissing.mockImplementation(
            async (blobNames: string[]): Promise<FindMissingResult> => {
                expect(blobNames).toContain(blobName);
                findMissingWaiting = true;
                await promise;
                return {
                    unknownBlobNames: [blobName],
                    nonindexedBlobNames: [],
                };
            }
        );

        // Wait until the DiskFileManager begins waiting for findMissing to return, then delete
        // the file.
        kit.ingestAllPaths(folderId, diskFileManager);
        while (!findMissingWaiting) {
            await kit.runTimers();
        }
        expect(pathMap.getBlobName(folderId, relPath)).toBeUndefined();
        kit.deleteFile(folderId, relPath);

        // Allow DiskFileManager to proceeed
        disp.dispose();

        await kit.awaitQuiesced(diskFileManager);
        expect(pathMap.shouldTrack(folderId, relPath)).toBe(true);
        expect(pathMap.getBlobName(folderId, relPath)).toBeUndefined();
    });

    // `upload unreadable file` verifies that the DiskFileManager will invalidate a path name
    // in the path map if the upload operation cannot read the file.
    test("upload unreadable file", async () => {
        const relPath = "unreadable";
        const files = new Map<string, string>([
            [relPath, "test will make this file unreadable before it gets uploaded"],
        ]);
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();
        const blobName = kit.calculateBlobName(folderId, relPath);

        const [promise, disp] = disposableDelayer();
        let findMissingWaiting = false;
        kit.apiServer.findMissing.mockImplementation(
            async (blobNames: string[]): Promise<FindMissingResult> => {
                expect(blobNames).toContain(blobName);
                findMissingWaiting = true;
                await promise;
                return {
                    unknownBlobNames: [blobName],
                    nonindexedBlobNames: [],
                };
            }
        );

        // Wait until the DiskFileManager begins waiting for findMissing to return, then delete
        // the file.
        kit.ingestAllPaths(folderId, diskFileManager);
        while (!findMissingWaiting) {
            await kit.runTimers();
        }
        expect(pathMap.getBlobName(folderId, relPath)).toBeUndefined();
        kit.failFile(folderId, relPath);

        // Allow DiskFileManager to proceeed
        disp.dispose();

        await kit.awaitQuiesced(diskFileManager);
        expect(pathMap.shouldTrack(folderId, relPath)).toBe(true);
        expect(pathMap.getBlobName(folderId, relPath)).toBeUndefined();
    });

    // `upload returns different name` verifies that the blob name returned by
    // the upload operation will replace the locally computed name if the two
    // names are different.
    test("upload returns different name", async () => {
        const files = new Map<string, string>([["walrus", "this is walrus"]]);
        const differentBlobName = "different blob name";
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();

        // Arrange for upload of 'walrus' to return a different blob name than what
        // the DiskFileManager will compute.
        kit.apiServer.overrideMemoryName("walrus", differentBlobName);

        // Before upload, the context should contain the locally-computed name.
        await kit.ingestAllPaths(folderId, diskFileManager);

        // After upload, the context should have the different name.
        expect(pathMap.getBlobName(folderId, "walrus")).toBe(differentBlobName);
    });

    // Ensure that the client and the server calculate the same blob name when
    // the file contains BOM (byte-order-mark)
    test("ingest-handle-bom", async () => {
        // Add BOM on the string
        const files = new Map<string, string>([["walrus", "\ufeffwalruses have large tusks"]]);
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();
        await kit.ingestAllPaths(folderId, diskFileManager);

        const blobName = kit.calculateBlobName(folderId, "walrus");
        expect(pathMap.getBlobName(folderId, "walrus")).toBe(blobName);
    });

    // 'find-missing-batch-size' verifies that the DiskFileManager honors the
    // maximum find-missing batch size.
    test("find-missing-batch-size", async () => {
        const files = new Map<string, string>();
        for (let i = 0; i < kit.maxProbeBatchSize * 5; i++) {
            files.set(`${i}`, `this is file ${i}`);
        }
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();

        // Delay find-missing requests so we build up a backlog.
        const findMissingDelay = kit.apiServer.delayFindMissing();
        kit.ingestAllPaths(folderId, diskFileManager);

        // Release the find-missing delay and wait for the DiskFileManager to
        // ingest all of the files.
        findMissingDelay.dispose();
        await kit.awaitQuiesced(diskFileManager);
        kit.verifyPathMap(folderId);

        // Verify that the find-missing requests honored the batch size
        for (const batchSize of kit.apiServer.findMissingHistory) {
            expect(batchSize).toBeLessThanOrEqual(kit.maxProbeBatchSize);
        }
    });

    test("upload batches, report missing", async () => {
        const validFiles = new Map<string, string>();
        for (let i = 0; i < DiskFileManager.maxUploadBatchBlobCount + 1; i++) {
            validFiles.set(`file-${i}`, `file-${i}`);
        }

        const allFiles = new Map<string, string>(validFiles);
        allFiles.set("large-file", "a".repeat(DiskFileManagerTestKit.maxFileSize + 1));

        kit.createFolderContents(folderId, allFiles);
        const diskFileManager = kit.makeDiskFileManager();
        await kit.ingestAllPaths(folderId, diskFileManager);

        // Verify that the API server has all the blob names in the context.
        const blobNames = new Array<string>();
        for (const [relPath, _] of validFiles) {
            const blobName = kit.calculateBlobName(folderId, relPath);
            blobNames.push(blobName);
        }
        kit.apiServer.verifyContext(blobNames);

        // Drop the blob names from the api server's cache
        kit.apiServer.forget();

        // Report the blob names as missing and wait until they have all been re-uploaded to the
        // ApiServer.
        for (const blobName of blobNames) {
            const qualifiedPathName = kit.pathMap.reportMissing(blobName);
            verifyDefined(qualifiedPathName);
            diskFileManager.ingestPath(folderId, qualifiedPathName.relPath);
        }
        await kit.awaitQuiesced(diskFileManager);
        kit.apiServer.verifyContext(blobNames);

        // Ensure that all files have nonzero content sequence numbers, indicating that they have
        // been verified.
        for (const pathName of validFiles.keys()) {
            const blobInfo = kit.getBlobInfo(folderId, pathName);
            verifyDefined(blobInfo);
            expect(blobInfo?.[1]).toBeGreaterThan(0);
        }
    });

    // Verify that the DiskFileManager does not re-verify a blob name that has already been
    // verified, according to the path map.
    test("avoid reverify", async () => {
        let mtime = 12345;
        const files = new Map<string, string>([["walrus", "this is walrus"]]);
        kit.createFolderContents(folderId, files);
        const blobName = kit.calculateBlobName(folderId, "walrus");
        const diskFileManager = kit.makeDiskFileManager();

        // Add a file to the path map with content sequence number 0. This indicates that the
        // blob name has not yet been verified.
        kit.pathMap.insert(folderId, "walrus", FileType.file, new MockAcceptPath());
        kit.pathMap.update(folderId, "walrus", 0, blobName, mtime);

        // Ingesting the path should cause it to be verified. This may require multiple calls to
        // find-missing. Its content sequence number should be updated to a nonzero value.
        expect(kit.apiServer.findMissingHistory.length).toBe(0);
        diskFileManager.ingestPath(folderId, "walrus");
        await kit.awaitQuiesced(diskFileManager);
        const findMissingCount = kit.apiServer.findMissingHistory.length;
        expect(findMissingCount).toBeGreaterThan(0);
        const blobInfo = kit.getBlobInfo(folderId, "walrus");
        verifyDefined(blobInfo);
        expect(blobInfo?.[1]).toBeGreaterThan(0);

        // Re-ingesting the path should not cause find-missing to be called again.
        diskFileManager.ingestPath(folderId, "walrus");
        await kit.awaitQuiesced(diskFileManager);
        expect(kit.apiServer.findMissingHistory.length).toBe(findMissingCount);
    });

    // Verify that the DiskFileManager retries uploads that fail with a cancel error.
    test("retry upload on cancel", async () => {
        const relPath = "walrus.py";
        const files = new Map<string, string>([[relPath, "file contents"]]);
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();

        // Make the first `maxRetries` upload attempts fail with a cancel error.
        const maxRetries = 4;
        let retryCount = 0;
        let uploadSucceeded = false;
        kit.apiServer.batchUpload = jest.fn(async (blobs: Array<UploadBlob>) => {
            expect(blobs.length).toBe(1);
            expect(blobs[0].pathName).toBe(relPath);
            if (retryCount < maxRetries) {
                retryCount++;
                throw new APIError(APIStatus.cancelled, "mock timeout");
            }
            uploadSucceeded = true;
            return {
                blobNames: [kit.calculateBlobName(folderId, relPath)],
            };
        });

        // Add the file to the path map and tell DiskFileManager to ingest it, which should
        // trigger an upload.
        kit.pathMap.insert(folderId, "walrus.py", FileType.file, new MockAcceptPath());
        diskFileManager.ingestPath(folderId, relPath);

        // The upload should succeed after `maxRetries` retries.
        await advanceTimeUntilTrue(() => uploadSucceeded);
        expect(retryCount).toBe(maxRetries);
    });

    test("track empty files", async () => {
        const relPath = "empty.py";
        const files = new Map<string, string>([[relPath, ""]]);
        kit.createFolderContents(folderId, files);
        const diskFileManager = kit.makeDiskFileManager();
        await kit.ingestAllPaths(folderId, diskFileManager);
        expect(kit.pathMap.shouldTrack(folderId, relPath)).toBe(true);
        const blobName = kit.calculateBlobName(folderId, relPath);
        expect(kit.pathMap.getBlobName(folderId, relPath)).toBe(blobName);
    });

    test.each([
        [-10, DiskFileManager.minProbeBatchSize],
        [0, DiskFileManager.minProbeBatchSize],
        [1, 1],
        [DiskFileManager.maxProbeBatchSize / 2, DiskFileManager.maxProbeBatchSize / 2],
        [DiskFileManager.maxProbeBatchSize, DiskFileManager.maxProbeBatchSize],
        [DiskFileManager.maxProbeBatchSize + 1, DiskFileManager.maxProbeBatchSize],
    ])(
        "honor probe batch size (%d)",
        async (requestedBatchSize: number, expectedBatchSize: number) => {
            let diskFileManager: DiskFileManager | undefined;
            try {
                diskFileManager = new DiskFileManager(
                    "test workspace",
                    kit.apiServer,
                    new PathHandlerImpl(DiskFileManagerTestKit.maxFileSize, kit),
                    kit.pathMap,
                    requestedBatchSize
                );
                expect(diskFileManager.probeBatchSize).toBe(expectedBatchSize);
            } finally {
                diskFileManager?.dispose();
            }
        }
    );
});

describe("Multi-folder DiskFileManager", () => {
    const maxProbeBatchSize = DiskFileManager.maxProbeBatchSize;
    const folderRoot1 = "/src/folderRoot1";
    const folderRoot2 = "/src/folderRoot2";

    let kit: DiskFileManagerTestKit;
    let pathMap: PathMap;
    let folderId1: number;
    let folderId2: number;

    beforeEach(() => {
        jest.useFakeTimers();
        // Clean out the mock filesystem before each test
        mockFSUtils.reset();

        kit = new DiskFileManagerTestKit(maxProbeBatchSize);
        pathMap = kit.pathMap;
        folderId1 = pathMap.openSourceFolder(folderRoot1, folderRoot1);
        folderId2 = pathMap.openSourceFolder(folderRoot2, folderRoot2);
    });

    afterEach(() => {
        kit.dispose();
        jest.useRealTimers();
    });

    // Test that the DiskFileManager can ingest files from multiple folders.
    test("multi-folder", async () => {
        const folder1Files = new Map<string, string>([
            ["foo-1.py", "this is foo-1.py"],
            ["bar-1.py", "this is bar-1.py"],
            ["baz-1.py", "this is baz-1.py"],
        ]);
        const folder2Files = new Map<string, string>([
            ["foo-2.py", "this is foo-2.py"],
            ["bar-2.py", "this is bar-2.py"],
            ["baz-2.py", "this is baz-2.py"],
        ]);

        kit.createFolderContents(folderId1, folder1Files);
        kit.createFolderContents(folderId2, folder2Files);
        const diskFileManager = kit.makeDiskFileManager();
        await kit.ingestAllPaths(folderId1, diskFileManager);
        await kit.ingestAllPaths(folderId2, diskFileManager);
        kit.verifyPathMap(folderId1);
        kit.verifyPathMap(folderId2);
    });

    // Test that the DiskFileManager can ingest files from multiple folders with non-unique blob
    // names due to files in different folders with the same name and contents.
    test("non-unique blob names", async () => {
        const files = new Map<string, string>([
            ["foo.py", "this is foo.py"],
            ["bar.py", "this is bar.py"],
            ["baz.py", "this is baz.py"],
        ]);
        kit.createFolderContents(folderId1, files);
        kit.createFolderContents(folderId2, files);

        const diskFileManager = kit.makeDiskFileManager();
        await kit.ingestAllPaths(folderId1, diskFileManager);
        await kit.ingestAllPaths(folderId2, diskFileManager);
        kit.verifyPathMap(folderId1);
        kit.verifyPathMap(folderId2);
    });

    // Test that the DiskFileManager can ingest files from multiple folders with non-unique blob
    // names due to files in different folders with the same name and contents. This test introduces
    // a delay that tries to ensure that all of the blobs end up in the same batch for probe and
    // upload (although there isn't actually any way for the test to know whether it worked out
    // that way).
    test("non-unique blob names with delay", async () => {
        const files = new Map<string, string>([
            ["foo.py", "this is foo.py"],
            ["bar.py", "this is bar.py"],
            ["baz.py", "this is baz.py"],
        ]);
        kit.createFolderContents(folderId1, files);
        kit.createFolderContents(folderId2, files);

        const diskFileManager = kit.makeDiskFileManager();
        const delay = kit.delayReads();

        const promise = Promise.all([
            kit.ingestAllPaths(folderId1, diskFileManager),
            kit.ingestAllPaths(folderId2, diskFileManager),
        ]);

        delay.dispose();
        await promise;
        kit.verifyPathMap(folderId1);
        kit.verifyPathMap(folderId2);
    });
});
