import { ChangeTracker, Edit } from "../../workspace/change-tracker";

type RawEdit = {
    start: number;
    length: number;
    origStart: number;
    origLength: number;
};

class ChangeTrackerTestKit {
    static baseString =
        "ABCDEFGHIJKLMNOPQRSTUVWXY" +
        "abcdefghijklmnopqrstuvwxy" +
        "YXWVUTSRQPONMLKJIHGFEDCBA" +
        "yxwvutsrqponmlkjihgfedcba";
    static insertString = "0123456789";
    public tracker: ChangeTracker;
    public document: string;
    public origDocument: string;
    public seq = 1000;

    constructor(documentSize = 10000) {
        this.tracker = new ChangeTracker();
        const repeats = Math.ceil(documentSize / ChangeTrackerTestKit.baseString.length);
        const str = ChangeTrackerTestKit.baseString.repeat(repeats);
        this.document = str.substring(0, documentSize);
        this.origDocument = this.document.slice();
    }

    public apply(start: number, charsToDelete: number, charsToInsert: number) {
        const seq = this.seq++;
        const timestamp = new Date();
        this.tracker.apply(seq, timestamp, start, charsToDelete, charsToInsert);
        const repeats = Math.ceil(charsToInsert / ChangeTrackerTestKit.insertString.length);
        const toInsert = ChangeTrackerTestKit.insertString
            .repeat(repeats)
            .substring(0, charsToInsert);
        this.document =
            this.document.slice(0, start) + toInsert + this.document.slice(start + charsToDelete);
    }

    public translate(start: number, length: number): [number, number] {
        return this.tracker.translate(start, length);
    }

    public advance(): number {
        this.tracker.advance();
        this.origDocument = this.document.slice();
        return this.tracker.seq;
    }

    public verifyEdits(expected: RawEdit[]) {
        const edits = this.tracker.getEdits();
        this._verifyEdits(edits, expected);
        this._verifyReconstruct(edits);
    }

    public verifyChunks(maxChunkSize: number, maxOffset: number, expected: RawEdit[]) {
        const chunks = this.tracker.getChunks(maxChunkSize, maxOffset);
        this._verifyEdits(chunks, expected);
        this._verifyReconstruct(chunks);
    }

    public _verifyEdits(edits: Edit[], expected: RawEdit[]) {
        expect(edits.length).toBe(expected.length);
        for (let i = 0; i < edits.length; i++) {
            expect(edits[i].start).toBe(expected[i].start);
            expect(edits[i].origLength).toBe(expected[i].origLength);
            expect(edits[i].origStart).toBe(expected[i].origStart);
            expect(edits[i].length).toBe(expected[i].length);
        }
    }

    public verifyReconstruct(minSeq = 0) {
        const edits = this.tracker.getEdits();
        const editsOfInterest = edits.filter((e) => e.seq >= minSeq);
        this._verifyReconstruct(editsOfInterest);
    }

    public _verifyReconstruct(edits: Edit[]) {
        const constructed = this._reconstruct(edits);
        expect(constructed).toBe(this.document);
    }

    public _reconstruct(edits: Edit[]): string {
        const sortedEdits = edits.sort((a, b) => a.start - b.start);
        let constructed = "";
        let pos = 0;
        for (const edit of sortedEdits) {
            expect(edit.origStart).toBeGreaterThanOrEqual(pos);
            constructed += this.origDocument.slice(pos, edit.origStart);
            constructed += this.document.slice(edit.start, edit.start + edit.length);
            pos = edit.origStart + edit.origLength;
        }
        constructed += this.origDocument.slice(pos);
        return constructed;
    }
}

describe("changeTracker edit tracking", () => {
    test("empty", () => {
        const changeTracker = new ChangeTracker();
        expect(changeTracker.getEdits.length).toBe(0);
    });

    test("single insert", () => {
        const kit = new ChangeTrackerTestKit(200);
        kit.apply(100, 0, 5);
        kit.verifyEdits([{ start: 100, length: 5, origStart: 100, origLength: 0 }]);
    });

    // "multiple inserts" tests interactions between inserts. Each test case applies an insert
    // against a predetermined state with two inserts, and verifies the result. The test cases
    // are annotated with the position of their insert relative to the two original inserts.
    test.each([
        {
            // before both
            start: 95,
            charsInserted: 5,
            expected: [
                { start: 95, length: 5, origStart: 95, origLength: 0 },
                { start: 105, length: 5, origStart: 100, origLength: 0 },
                { start: 115, length: 5, origStart: 105, origLength: 0 },
            ],
        },
        {
            // left edge of first insert
            start: 100,
            charsInserted: 5,
            expected: [
                { start: 100, length: 10, origStart: 100, origLength: 0 },
                { start: 115, length: 5, origStart: 105, origLength: 0 },
            ],
        },
        {
            // middle of first insert
            start: 102,
            charsInserted: 5,
            expected: [
                { start: 100, length: 10, origStart: 100, origLength: 0 },
                { start: 115, length: 5, origStart: 105, origLength: 0 },
            ],
        },
        {
            // right edge of first insert
            start: 105,
            charsInserted: 5,
            expected: [
                { start: 100, length: 10, origStart: 100, origLength: 0 },
                { start: 115, length: 5, origStart: 105, origLength: 0 },
            ],
        },
        {
            // between the two inserts
            start: 107,
            charsInserted: 5,
            expected: [
                { start: 100, length: 5, origStart: 100, origLength: 0 },
                { start: 107, length: 5, origStart: 102, origLength: 0 },
                { start: 115, length: 5, origStart: 105, origLength: 0 },
            ],
        },
        {
            // left edge of second insert
            start: 110,
            charsInserted: 5,
            expected: [
                { start: 100, length: 5, origStart: 100, origLength: 0 },
                { start: 110, length: 10, origStart: 105, origLength: 0 },
            ],
        },
        {
            // middle of second insert
            start: 112,
            charsInserted: 5,
            expected: [
                { start: 100, length: 5, origStart: 100, origLength: 0 },
                { start: 110, length: 10, origStart: 105, origLength: 0 },
            ],
        },
        {
            // right edge of second insert
            start: 115,
            charsInserted: 5,
            expected: [
                { start: 100, length: 5, origStart: 100, origLength: 0 },
                { start: 110, length: 10, origStart: 105, origLength: 0 },
            ],
        },
        {
            // after second insert
            start: 120,
            charsInserted: 5,
            expected: [
                { start: 100, length: 5, origStart: 100, origLength: 0 },
                { start: 110, length: 5, origStart: 105, origLength: 0 },
                { start: 120, length: 5, origStart: 110, origLength: 0 },
            ],
        },
    ])(
        "multiple inserts($start, $charsInserted)",
        (testCase: { start: number; charsInserted: number; expected: RawEdit[] }) => {
            const kit = new ChangeTrackerTestKit(200);
            kit.apply(100, 0, 5);
            kit.apply(110, 0, 5);
            kit.apply(testCase.start, 0, testCase.charsInserted);
            kit.verifyEdits(testCase.expected);
        }
    );

    // test a single deletion
    test("single delete", () => {
        const kit = new ChangeTrackerTestKit();
        kit.apply(100, 5, 0);
        kit.verifyEdits([{ start: 100, length: 0, origStart: 100, origLength: 5 }]);
    });

    // test two non-interacting deletions
    test("two deletes", () => {
        const kit = new ChangeTrackerTestKit();
        kit.apply(100, 5, 0);
        kit.apply(110, 5, 0);
        kit.verifyEdits([
            { start: 100, length: 0, origStart: 100, origLength: 5 },
            { start: 110, length: 0, origStart: 115, origLength: 5 },
        ]);
    });

    // "multiple deletes" tests interactions between deletions. Each test case applies a deletion
    // against a predetermined state with two deletes, and verifies the result. The test cases
    // are annotated with the position of their delete relative to the two original deletes.
    test.each([
        /*
         * Start before the first delete
         */
        {
            // end before the first delete
            start: 100,
            charsDeleted: 2,
            expected: [
                { start: 100, length: 0, origStart: 100, origLength: 2 },
                { start: 103, length: 0, origStart: 105, origLength: 5 },
                { start: 113, length: 0, origStart: 120, origLength: 5 },
            ],
        },
        {
            // end at the first delete
            start: 100,
            charsDeleted: 5,
            expected: [
                { start: 100, length: 0, origStart: 100, origLength: 10 },
                { start: 110, length: 0, origStart: 120, origLength: 5 },
            ],
        },
        {
            // end between the two deletes
            start: 100,
            charsDeleted: 12,
            expected: [
                { start: 100, length: 0, origStart: 100, origLength: 17 },
                { start: 103, length: 0, origStart: 120, origLength: 5 },
            ],
        },
        {
            // end at the second delete
            start: 100,
            charsDeleted: 15,
            expected: [{ start: 100, length: 0, origStart: 100, origLength: 25 }],
        },
        {
            // end after second delete
            start: 100,
            charsDeleted: 22,
            expected: [{ start: 100, length: 0, origStart: 100, origLength: 32 }],
        },
        /*
         * Start at the first delete
         */
        {
            // end between the two deletes
            start: 105,
            charsDeleted: 7,
            expected: [
                { start: 105, length: 0, origStart: 105, origLength: 12 },
                { start: 108, length: 0, origStart: 120, origLength: 5 },
            ],
        },
        {
            // end at the second delete
            start: 105,
            charsDeleted: 10,
            expected: [{ start: 105, length: 0, origStart: 105, origLength: 20 }],
        },
        {
            // end after the second delete
            start: 105,
            charsDeleted: 17,
            expected: [{ start: 105, length: 0, origStart: 105, origLength: 27 }],
        },
        /*
         * Start between the two deletes
         */
        {
            // end between the two deletes
            start: 112,
            charsDeleted: 2,
            expected: [
                { start: 105, length: 0, origStart: 105, origLength: 5 },
                { start: 112, length: 0, origStart: 117, origLength: 2 },
                { start: 113, length: 0, origStart: 120, origLength: 5 },
            ],
        },
        {
            // end at the second delete
            start: 112,
            charsDeleted: 3,
            expected: [
                { start: 105, length: 0, origStart: 105, origLength: 5 },
                { start: 112, length: 0, origStart: 117, origLength: 8 },
            ],
        },
        {
            // end after the second delete
            start: 112,
            charsDeleted: 10,
            expected: [
                { start: 105, length: 0, origStart: 105, origLength: 5 },
                { start: 112, length: 0, origStart: 117, origLength: 15 },
            ],
        },
        /*
         * Start at the second delete
         */
        {
            // end after the second delete
            start: 115,
            charsDeleted: 7,
            expected: [
                { start: 105, length: 0, origStart: 105, origLength: 5 },
                { start: 115, length: 0, origStart: 120, origLength: 12 },
            ],
        },
        /*
         * Start after second delete
         */
        {
            // end after second delete
            start: 122,
            charsDeleted: 5,
            expected: [
                { start: 105, length: 0, origStart: 105, origLength: 5 },
                { start: 115, length: 0, origStart: 120, origLength: 5 },
                { start: 122, length: 0, origStart: 132, origLength: 5 },
            ],
        },
    ])(
        "multiple deletes($start, $charsDeleted)",
        (testCase: { start: number; charsDeleted: number; expected: RawEdit[] }) => {
            const kit = new ChangeTrackerTestKit();
            kit.apply(105, 5, 0);
            kit.apply(115, 5, 0);
            kit.apply(testCase.start, testCase.charsDeleted, 0);
            kit.verifyEdits(testCase.expected);
        }
    );

    test("single replacement", () => {
        const kit = new ChangeTrackerTestKit();
        kit.apply(100, 10, 5);
        kit.verifyEdits([{ start: 100, length: 5, origStart: 100, origLength: 10 }]);
    });

    test("two replacements", () => {
        const kit = new ChangeTrackerTestKit();
        kit.apply(102, 5, 3);
        kit.apply(108, 2, 4);
        kit.verifyEdits([
            { start: 102, length: 3, origStart: 102, origLength: 5 },
            { start: 108, length: 4, origStart: 110, origLength: 2 },
        ]);
    });

    // "multiple replacements" tests interactions between replacements. Each test case applies a
    // replacement against a predetermined state with two replacements, and verifies the result.
    // The test cases are annotated with the position of their replacement relative to the two
    // original replacements.
    test.each([
        /*
         * Start before first update
         */
        {
            // end before first update
            start: 100,
            charsDeleted: 1,
            charsInserted: 3,
            expected: [
                { start: 100, length: 3, origStart: 100, origLength: 1 },
                { start: 104, length: 3, origStart: 102, origLength: 5 },
                { start: 110, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end at left edge of first update
            start: 101,
            charsDeleted: 1,
            charsInserted: 2,
            expected: [
                { start: 101, length: 5, origStart: 101, origLength: 6 },
                { start: 109, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end inside first update
            start: 101,
            charsDeleted: 3,
            charsInserted: 2,
            expected: [
                { start: 101, length: 3, origStart: 101, origLength: 6 },
                { start: 107, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end at right edge of first update
            start: 101,
            charsDeleted: 4,
            charsInserted: 1,
            expected: [
                { start: 101, length: 1, origStart: 101, origLength: 6 },
                { start: 105, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end between the two updates
            start: 101,
            charsDeleted: 6,
            charsInserted: 7,
            expected: [
                { start: 101, length: 7, origStart: 101, origLength: 8 },
                { start: 109, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end at left edge of second update
            start: 101,
            charsDeleted: 7,
            charsInserted: 1,
            expected: [{ start: 101, length: 5, origStart: 101, origLength: 11 }],
        },
        {
            // end inside second update
            start: 101,
            charsDeleted: 10,
            charsInserted: 3,
            expected: [{ start: 101, length: 4, origStart: 101, origLength: 11 }],
        },
        {
            // end at right edge of second update
            start: 101,
            charsDeleted: 11,
            charsInserted: 0,
            expected: [{ start: 101, length: 0, origStart: 101, origLength: 11 }],
        },
        {
            // end after second update
            start: 101,
            charsDeleted: 12,
            charsInserted: 4,
            expected: [{ start: 101, length: 4, origStart: 101, origLength: 12 }],
        },
        /*
         * Start at left edge of first update
         */
        {
            // end inside first update
            start: 102,
            charsDeleted: 1,
            charsInserted: 3,
            expected: [
                { start: 102, length: 5, origStart: 102, origLength: 5 },
                { start: 110, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end at right edge of first update
            start: 102,
            charsDeleted: 3,
            charsInserted: 2,
            expected: [
                { start: 102, length: 2, origStart: 102, origLength: 5 },
                { start: 107, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end between the two updates
            start: 102,
            charsDeleted: 4,
            charsInserted: 6,
            expected: [
                { start: 102, length: 6, origStart: 102, origLength: 6 },
                { start: 110, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end at left edge of second update
            start: 102,
            charsDeleted: 6,
            charsInserted: 3,
            expected: [{ start: 102, length: 7, origStart: 102, origLength: 10 }],
        },
        {
            // end inside second update
            start: 102,
            charsDeleted: 9,
            charsInserted: 3,
            expected: [{ start: 102, length: 4, origStart: 102, origLength: 10 }],
        },
        {
            // end at right edge of second update
            start: 102,
            charsDeleted: 10,
            charsInserted: 3,
            expected: [{ start: 102, length: 3, origStart: 102, origLength: 10 }],
        },
        {
            // end after second update
            start: 102,
            charsDeleted: 11,
            charsInserted: 3,
            expected: [{ start: 102, length: 3, origStart: 102, origLength: 11 }],
        },
        /*
         * Start inside the first update
         */
        {
            // end inside first update
            start: 103,
            charsDeleted: 1,
            charsInserted: 3,
            expected: [
                { start: 102, length: 5, origStart: 102, origLength: 5 },
                { start: 110, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end at right edge of first update
            start: 103,
            charsDeleted: 2,
            charsInserted: 3,
            expected: [
                { start: 102, length: 4, origStart: 102, origLength: 5 },
                { start: 109, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end between the two updates
            start: 103,
            charsDeleted: 4,
            charsInserted: 3,
            expected: [
                { start: 102, length: 4, origStart: 102, origLength: 7 },
                { start: 107, length: 4, origStart: 110, origLength: 2 },
            ],
        },
        {
            // end at left edge of second update
            start: 103,
            charsDeleted: 5,
            charsInserted: 3,
            expected: [{ start: 102, length: 8, origStart: 102, origLength: 10 }],
        },
        {
            // end inside second update
            start: 103,
            charsDeleted: 8,
            charsInserted: 3,
            expected: [{ start: 102, length: 5, origStart: 102, origLength: 10 }],
        },
        {
            // end at right edge of second update
            start: 103,
            charsDeleted: 9,
            charsInserted: 3,
            expected: [{ start: 102, length: 4, origStart: 102, origLength: 10 }],
        },
        {
            // end after second update
            start: 103,
            charsDeleted: 10,
            charsInserted: 3,
            expected: [{ start: 102, length: 4, origStart: 102, origLength: 11 }],
        },
    ])(
        "multiple replacements($start, $charsDeleted, $charsInserted)",
        (testCase: {
            start: number;
            charsDeleted: number;
            charsInserted: number;
            expected: RawEdit[];
        }) => {
            const kit = new ChangeTrackerTestKit();
            kit.apply(102, 5, 3);
            kit.apply(108, 2, 4);
            kit.apply(testCase.start, testCase.charsDeleted, testCase.charsInserted);
            kit.verifyEdits(testCase.expected);
        }
    );

    // "replace many": tests replacements that cover many previous modifications
    test.each([
        {
            start: 100,
            charsDeleted: 15,
            charsInserted: 10,
            expected: [
                { start: 100, length: 10, origStart: 100, origLength: 15 },
                { start: 111, length: 2, origStart: 116, origLength: 1 },
            ],
        },
        {
            start: 100,
            charsDeleted: 16,
            charsInserted: 10,
            expected: [{ start: 100, length: 12, origStart: 100, origLength: 17 }],
        },
        {
            start: 100,
            charsDeleted: 17,
            charsInserted: 10,
            expected: [{ start: 100, length: 11, origStart: 100, origLength: 17 }],
        },
        {
            start: 100,
            charsDeleted: 18,
            charsInserted: 10,
            expected: [{ start: 100, length: 10, origStart: 100, origLength: 17 }],
        },
        {
            start: 100,
            charsDeleted: 19,
            charsInserted: 10,
            expected: [{ start: 100, length: 10, origStart: 100, origLength: 18 }],
        },
        {
            start: 101,
            charsDeleted: 15,
            charsInserted: 10,
            expected: [{ start: 101, length: 12, origStart: 101, origLength: 16 }],
        },
        {
            start: 102,
            charsDeleted: 15,
            charsInserted: 10,
            expected: [{ start: 101, length: 12, origStart: 101, origLength: 16 }],
        },
        {
            start: 103,
            charsDeleted: 15,
            charsInserted: 10,
            expected: [{ start: 101, length: 12, origStart: 101, origLength: 16 }],
        },
        {
            start: 104,
            charsDeleted: 15,
            charsInserted: 10,
            expected: [
                { start: 101, length: 2, origStart: 101, origLength: 1 },
                { start: 104, length: 10, origStart: 103, origLength: 15 },
            ],
        },
    ])(
        "replace many($start, $charsDeleted, $charsInserted)",
        (testCase: {
            start: number;
            charsDeleted: number;
            charsInserted: number;
            expected: RawEdit[];
        }) => {
            const kit = new ChangeTrackerTestKit();
            kit.apply(101, 1, 2);
            kit.apply(105, 2, 1);
            kit.apply(108, 2, 4);
            kit.apply(113, 3, 1);
            kit.apply(116, 1, 2);
            kit.apply(testCase.start, testCase.charsDeleted, testCase.charsInserted);
            kit.verifyEdits(testCase.expected);
        }
    );
});

describe("changeTracker chunking", () => {
    test("empty", () => {
        const changeTracker = new ChangeTracker();
        expect(changeTracker.getChunks(100, 100).length).toBe(0);
    });

    test("one chunk", () => {
        const documentSize = 1000;
        const kit = new ChangeTrackerTestKit(documentSize);
        kit.apply(500, 5, 0);
        kit.verifyChunks(80, documentSize, [
            { start: 460, origLength: 85, origStart: 460, length: 80 },
        ]);
    });

    // "two edits" tests chunking over a document with two edits.
    test.each([
        {
            maxChunkSize: 60,
            expected: [
                { start: 480, length: 60, origStart: 480, origLength: 45 },
                { start: 585, length: 60, origStart: 570, origLength: 35 },
            ],
        },
        {
            maxChunkSize: 80,
            expected: [
                { start: 470, length: 80, origStart: 470, origLength: 65 },
                { start: 575, length: 80, origStart: 560, origLength: 55 },
            ],
        },
        {
            maxChunkSize: 100,
            expected: [
                { start: 460, length: 100, origStart: 460, origLength: 85 },
                { start: 565, length: 100, origStart: 550, origLength: 75 },
            ],
        },
        {
            maxChunkSize: 110,
            expected: [
                { start: 455, length: 110, origStart: 455, origLength: 95 },
                { start: 565, length: 110, origStart: 550, origLength: 85 },
            ],
        },
        {
            maxChunkSize: 160,
            expected: [{ start: 485, origLength: 120, origStart: 485, length: 160 }],
        },
    ])("two edits($maxChunkSize)", (testCase: { maxChunkSize: number; expected: RawEdit[] }) => {
        const documentSize = 1000;
        const kit = new ChangeTrackerTestKit(documentSize);
        kit.apply(500, 5, 20);
        kit.apply(600, 5, 30);
        kit.verifyChunks(testCase.maxChunkSize, documentSize, testCase.expected);
    });

    // "edit larger than chunk size" tests chunking over a document with an edit that is larger
    // than the maximum chunk size.
    test("edit larger than chunk size", () => {
        const documentSize = 1000;
        const kit = new ChangeTrackerTestKit(documentSize);
        kit.apply(480, 5, 10);
        kit.apply(500, 12, 317);
        kit.verifyChunks(100, documentSize, [
            { start: 480, length: 100, origStart: 480, origLength: 27 },
            { start: 580, length: 100, origStart: 507, origLength: 0 },
            { start: 680, length: 100, origStart: 507, origLength: 0 },
            { start: 780, length: 100, origStart: 507, origLength: 63 },
        ]);
    });
});
describe("change tracker translate", () => {
    test.each([
        { start: 0, length: 2, xStart: 0, xLength: 2 },
        { start: 0, length: 3, xStart: 0, xLength: 6 },
        { start: 0, length: 4, xStart: 0, xLength: 6 },
        { start: 0, length: 5, xStart: 0, xLength: 7 },
        { start: 0, length: 10, xStart: 0, xLength: 11 },
        { start: 0, length: 14, xStart: 0, xLength: 13 },
        { start: 3, length: 11, xStart: 3, xLength: 10 },
        { start: 4, length: 10, xStart: 3, xLength: 10 },
        { start: 7, length: 5, xStart: 9, xLength: 2 },
        { start: 10, length: 4, xStart: 9, xLength: 4 },
        { start: 12, length: 2, xStart: 9, xLength: 4 },
    ])(
        "translation($start, $length)",
        (testCase: { start: number; length: number; xStart: number; xLength: number }) => {
            const kit = new ChangeTrackerTestKit(10000);
            kit.apply(3, 3, 1);
            kit.apply(7, 2, 5);
            const [expectedXstart, expectedXlength] = kit.translate(
                testCase.start,
                testCase.length
            );
            expect(expectedXstart).toBe(testCase.xStart);
            expect(expectedXlength).toBe(testCase.xLength);
        }
    );

    test("delete entire file", () => {
        const origSize = 10;
        const kit = new ChangeTrackerTestKit(origSize);
        kit.apply(0, origSize, 0);
        const [expectedXstart, expectedXlength] = kit.translate(0, 0);
        expect(expectedXstart).toBe(0);
        expect(expectedXlength).toBe(origSize);
    });

    test("delete middle", () => {
        const kit = new ChangeTrackerTestKit(10);
        kit.apply(3, 3, 0);
        const [expectedXstart, expectedXlength] = kit.translate(3, 0);
        expect(expectedXstart).toBe(3);
        expect(expectedXlength).toBe(3);
    });
});

describe("change tracker advance", () => {
    test("advance", () => {
        const kit = new ChangeTrackerTestKit(10000);
        kit.apply(7, 2, 5);
        const newChanges = kit.advance();
        kit.apply(3, 3, 1);
        kit.verifyReconstruct(newChanges);
    });
});
