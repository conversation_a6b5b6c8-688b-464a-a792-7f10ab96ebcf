import { EventEmitter, Uri, Webview, window } from "../../__mocks__/vscode-mocks";
import { defaultFeatureFlags, FeatureFlagManager } from "../../feature-flags";
import { DisposableService } from "../../utils/disposable-service";
import { FileType } from "../../utils/types";
import {
    AsyncWebViewMessage,
    WebViewMessage,
    WebViewMessageType,
    WSContextAddMoreSourceFolders,
    WSContextFolderContentsChanged,
    WSContextSourceFoldersChanged,
} from "../../webview-providers/webview-messages";
import {
    DuplicateSourceFolderError,
    SourceFolderEnumerationState,
    SourceFolderInfo,
    SourceFolderItem,
    SourceFolderType,
} from "../../workspace/workspace-types";
import { WorkspaceUIManagerMethods, WorkspaceUIModel } from "../../workspace/workspace-ui-model";

describe("WorkspaceUIModel", () => {
    let mockWebview: Webview;
    let mockWorkspaceManager: MockWorkspaceManager;
    let workspaceUIModel: WorkspaceUIModel;
    let featureFlagManager: FeatureFlagManager;

    beforeEach(() => {
        mockWebview = new Webview(
            {
                enableScripts: true,
                localResourceRoots: [],
            },
            "",
            "example csp source"
        );
        mockWebview.postMessage = jest.fn();
        mockWorkspaceManager = new MockWorkspaceManager();
        featureFlagManager = new FeatureFlagManager({
            initialFlags: {
                ...defaultFeatureFlags,
                enableWorkspaceManagerUi: true,
            },
        });
        workspaceUIModel = new WorkspaceUIModel(
            mockWorkspaceManager,
            mockWebview,
            featureFlagManager
        );
    });

    afterEach(() => {
        workspaceUIModel.dispose();
    });

    describe("addExternalSourceFolder", () => {
        test("addExternalSourceFolder happy path", async () => {
            window.showOpenDialog.mockResolvedValue([
                Uri.file("/some/example/path1"),
                Uri.file("/some/example/path2"),
            ]);

            // This promise thing might seem a little tricky.
            // We setup a promise that resolves when the mockWorkspaceManager.addExternalSourceFolder
            // is called. Then we kick off a wsContextAddMoreSourceFolders to the webview's listeners
            // (one of which is the workspace-ui-model under test). We await the promise so that we
            // can check the final outcomes AFTER the call to addExternalSourceFolder.
            const addExternalFolderPromise = new Promise((resolve) => {
                mockWorkspaceManager.addExternalSourceFolder.mockImplementationOnce(resolve);
            });

            mockWebview._onDidReceiveListeners.forEach((listener) => {
                listener({
                    type: WebViewMessageType.wsContextAddMoreSourceFolders,
                } as WSContextAddMoreSourceFolders);
            });

            await addExternalFolderPromise;

            expect(mockWorkspaceManager.addExternalSourceFolder).toHaveBeenCalledWith(
                Uri.file("/some/example/path1")
            );
            expect(mockWorkspaceManager.addExternalSourceFolder).toHaveBeenCalledWith(
                Uri.file("/some/example/path2")
            );
        });

        test("addExternalSourceFolder exception thrown", async () => {
            window.showOpenDialog.mockResolvedValue([
                Uri.file("/some/example/path1"),
                Uri.file("/some/example/path2"),
            ]);

            // see comment on "addExternalSourceFolder happy path" for explanation of the Promise
            const addExternalFolderPromise = new Promise((resolve) => {
                mockWorkspaceManager.addExternalSourceFolder.mockImplementationOnce(() => {
                    resolve(undefined);
                    throw new DuplicateSourceFolderError();
                });
            });

            mockWebview._onDidReceiveListeners.forEach((listener) => {
                listener({
                    type: WebViewMessageType.wsContextAddMoreSourceFolders,
                } as WSContextAddMoreSourceFolders);
            });

            await addExternalFolderPromise;

            expect(window.showErrorMessage).toHaveBeenCalledWith(
                "One or more source folders could not be added:\n/some/example/path1: source folder already exists"
            );
            expect(mockWorkspaceManager.addExternalSourceFolder).toHaveBeenCalledWith(
                Uri.file("/some/example/path2")
            );
        });
    });

    describe("removeExternalSourceFolder", () => {
        test("removeExternalSourceFolder happy path", async () => {
            // see comment on "addExternalSourceFolder happy path" for explanation of the Promise
            const removeExternalFolderPromise = new Promise((resolve) => {
                mockWorkspaceManager.removeExternalSourceFolder.mockImplementationOnce(resolve);
            });

            mockWebview._onDidReceiveListeners.forEach((listener) => {
                listener({
                    type: WebViewMessageType.wsContextRemoveSourceFolder,
                    data: "/some/example/path",
                });
            });

            await removeExternalFolderPromise;

            expect(mockWorkspaceManager.removeExternalSourceFolder).toHaveBeenCalledWith(
                "/some/example/path"
            );
        });

        test("removeExternalSourceFolder exception thrown", async () => {
            // see comment on "addExternalSourceFolder happy path" for explanation of the Promise
            const removeExternalFolderPromise = new Promise((resolve) => {
                mockWorkspaceManager.removeExternalSourceFolder.mockImplementationOnce(() => {
                    resolve(undefined);
                    throw new Error("some error");
                });
            });

            mockWebview._onDidReceiveListeners.forEach((listener) => {
                listener({
                    type: WebViewMessageType.wsContextRemoveSourceFolder,
                    data: "/some/example/path",
                });
            });

            await removeExternalFolderPromise;

            expect(window.showErrorMessage).toHaveBeenCalledWith(
                "Failed to remove source folder /some/example/path:\n some error"
            );

            expect(mockWorkspaceManager.removeExternalSourceFolder).toHaveBeenCalledWith(
                "/some/example/path"
            );
        });
    });

    test("listChildren", async () => {
        const listChildrenPromise = new Promise((resolve) => {
            mockWorkspaceManager.listChildren.mockImplementationOnce(
                (_folderRoot: string, _subdirRoot: string) => {
                    resolve(undefined);
                    return [
                        {
                            name: "file1.py",
                            folderRoot: "trackedWorkspace",
                            relPath: "nestedWorkspace/file1.py",
                            type: FileType.file,
                            included: true,
                            indexed: true,
                            reason: "reason",
                        },
                    ] as Array<SourceFolderItem>;
                }
            );
        });

        mockWebview._onDidReceiveListeners.forEach((listener) => {
            listener(
                asyncWrapper({
                    type: WebViewMessageType.wsContextGetChildrenRequest,
                    data: {
                        fileId: {
                            folderRoot: "trackedWorkspace",
                            relPath: "nestedWorkspace",
                        },
                    },
                } as WebViewMessage)
            );
        });

        await listChildrenPromise;

        expect(mockWorkspaceManager.listChildren).toHaveBeenCalledWith(
            "trackedWorkspace",
            "nestedWorkspace"
        );
        expect(mockWebview.postMessage).toHaveBeenCalledWith(
            asyncWrapper({
                type: WebViewMessageType.wsContextGetChildrenResponse,
                data: {
                    children: [
                        {
                            name: "file1.py",
                            fileId: {
                                folderRoot: "trackedWorkspace",
                                relPath: "nestedWorkspace/file1.py",
                            },
                            type: "file",
                            inclusionState: "included",
                            reason: "reason",
                        },
                    ],
                },
            } as WebViewMessage)
        );
    });

    test("listSourceFolders", async () => {
        const listSourceFoldersPromise = new Promise((resolve) => {
            mockWorkspaceManager.listSourceFolders.mockImplementationOnce(() => {
                resolve(undefined);
                return [
                    {
                        name: "trackedWorkspace",
                        folderRoot: "trackedWorkspace",
                        type: SourceFolderType.vscodeWorkspaceFolder,
                        containsExcludedItems: false,
                        enumerationState: SourceFolderEnumerationState.complete,
                    },
                    {
                        name: "trackedWorkspace2",
                        folderRoot: "trackedWorkspace2",
                        type: SourceFolderType.vscodeWorkspaceFolder,
                        containsExcludedItems: true,
                        enumerationState: SourceFolderEnumerationState.inProgress,
                    },
                    {
                        name: "external",
                        folderRoot: "external",
                        type: SourceFolderType.externalFolder,
                        containsExcludedItems: false,
                        enumerationState: SourceFolderEnumerationState.complete,
                    },
                    {
                        name: "external2",
                        folderRoot: "external2",
                        type: SourceFolderType.externalFolder,
                        containsExcludedItems: true,
                        enumerationState: SourceFolderEnumerationState.inProgress,
                    },
                    {
                        name: "nestedWorkspace",
                        folderRoot: "trackedWorkspace/nestedWorkspace",
                        type: SourceFolderType.nestedWorkspaceFolder,
                        containsExcludedItems: false,
                        enumerationState: SourceFolderEnumerationState.complete,
                    },
                    {
                        name: "nestedWorkspace2",
                        folderRoot: "trackedWorkspace2/nestedWorkspace",
                        type: SourceFolderType.nestedWorkspaceFolder,
                        containsExcludedItems: true,
                        enumerationState: SourceFolderEnumerationState.inProgress,
                    },
                    {
                        name: "nestedExternal",
                        folderRoot: "external/nestedExternal",
                        type: SourceFolderType.nestedExternalFolder,
                        containsExcludedItems: false,
                        enumerationState: SourceFolderEnumerationState.complete,
                    },
                    {
                        name: "nestedExternal2",
                        folderRoot: "external2/nestedExternal",
                        type: SourceFolderType.nestedExternalFolder,
                        containsExcludedItems: true,
                        enumerationState: SourceFolderEnumerationState.inProgress,
                    },
                ] as Array<SourceFolderInfo>;
            });
        });

        mockWebview._onDidReceiveListeners.forEach((listener) => {
            listener({
                type: WebViewMessageType.asyncWrapper,
                baseMsg: {
                    type: WebViewMessageType.wsContextGetSourceFoldersRequest,
                } as WebViewMessage,
                requestId: "mock-request-id",
            } as WebViewMessage);
        });

        await listSourceFoldersPromise;

        expect(mockWorkspaceManager.listSourceFolders).toHaveBeenCalledTimes(1);
        expect(mockWebview.postMessage).toHaveBeenCalledWith(
            asyncWrapper({
                type: WebViewMessageType.wsContextGetSourceFoldersResponse,
                data: {
                    workspaceFolders: [
                        {
                            name: "trackedWorkspace",
                            fileId: {
                                folderRoot: "trackedWorkspace",
                                relPath: "",
                            },
                            inclusionState: "included",
                            isWorkspaceFolder: true,
                            isNestedFolder: false,
                            isPending: false,
                        },
                        {
                            name: "trackedWorkspace2",
                            fileId: {
                                folderRoot: "trackedWorkspace2",
                                relPath: "",
                            },
                            inclusionState: "partial",
                            isWorkspaceFolder: true,
                            isNestedFolder: false,
                            isPending: true,
                        },
                        {
                            name: "external",
                            fileId: {
                                folderRoot: "external",
                                relPath: "",
                            },
                            inclusionState: "included",
                            isWorkspaceFolder: false,
                            isNestedFolder: false,
                            isPending: false,
                        },
                        {
                            name: "external2",
                            fileId: {
                                folderRoot: "external2",
                                relPath: "",
                            },
                            inclusionState: "partial",
                            isWorkspaceFolder: false,
                            isNestedFolder: false,
                            isPending: true,
                        },
                        {
                            name: "nestedWorkspace",
                            fileId: {
                                folderRoot: "trackedWorkspace/nestedWorkspace",
                                relPath: "",
                            },
                            inclusionState: "included",
                            isWorkspaceFolder: true,
                            isNestedFolder: true,
                            isPending: false,
                        },
                        {
                            name: "nestedWorkspace2",
                            fileId: {
                                folderRoot: "trackedWorkspace2/nestedWorkspace",
                                relPath: "",
                            },
                            inclusionState: "partial",
                            isWorkspaceFolder: true,
                            isNestedFolder: true,
                            isPending: true,
                        },
                        {
                            name: "nestedExternal",
                            fileId: {
                                folderRoot: "external/nestedExternal",
                                relPath: "",
                            },
                            inclusionState: "included",
                            isWorkspaceFolder: false,
                            isNestedFolder: true,
                            isPending: false,
                        },
                        {
                            name: "nestedExternal2",
                            fileId: {
                                folderRoot: "external2/nestedExternal",
                                relPath: "",
                            },
                            inclusionState: "partial",
                            isWorkspaceFolder: false,
                            isNestedFolder: true,
                            isPending: true,
                        },
                    ].sort((a, b) => a.name.localeCompare(b.name)),
                },
            } as WebViewMessage)
        );
    });

    test("onDidChangeSourceFolders", () => {
        mockWorkspaceManager._sourceFoldersChangedEmitter.fire();
        expect(mockWebview.postMessage).toHaveBeenCalledWith({
            type: WebViewMessageType.wsContextSourceFoldersChanged,
        } as WSContextSourceFoldersChanged);
    });

    test("onDidChangeSourceFolderContents", () => {
        mockWorkspaceManager._sourceFolderContentsChangedEmitter.fire("trackedWorkspace");
        expect(mockWebview.postMessage).toHaveBeenCalledWith({
            type: WebViewMessageType.wsContextFolderContentsChanged,
            data: "trackedWorkspace",
        } as WSContextFolderContentsChanged);
    });
});

class MockWorkspaceManager extends DisposableService implements WorkspaceUIManagerMethods {
    public readonly _sourceFoldersChangedEmitter: EventEmitter<void>;
    public readonly _sourceFolderContentsChangedEmitter: EventEmitter<string>;

    constructor() {
        super();
        this._sourceFoldersChangedEmitter = this.addDisposable(new EventEmitter<void>());
        this._sourceFolderContentsChangedEmitter = this.addDisposable(new EventEmitter<string>());
        this.onDidChangeSourceFolders = jest.fn(this._sourceFoldersChangedEmitter.event);
        this.onDidChangeSourceFolderContents = jest.fn(
            this._sourceFolderContentsChangedEmitter.event
        );
    }

    public addExternalSourceFolder = jest.fn((_uri: Uri) => {});
    public removeExternalSourceFolder = jest.fn((_name: string) => {});
    public listChildren = jest.fn((_folderRoot: string, _subdirRoot: string) => {
        return [] as Array<SourceFolderItem>;
    });
    public onDidChangeSourceFolders;
    public onDidChangeSourceFolderContents;
    public refreshSourceFolders = jest.fn();
    public listSourceFolders = jest.fn();
}

function asyncWrapper(msg: WebViewMessage): AsyncWebViewMessage<WebViewMessage> {
    return {
        type: WebViewMessageType.asyncWrapper,
        requestId: "mock-request-id",
        error: null,
        baseMsg: msg,
    };
}
