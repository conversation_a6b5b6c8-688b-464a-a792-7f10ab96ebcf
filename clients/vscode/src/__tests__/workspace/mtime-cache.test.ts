import { mockFSUtils } from "../../__mocks__/fs-utils";
import { MtimeCacheWriterImpl, readMtimeCache } from "../../workspace/mtime-cache";

describe("mtime-cache", () => {
    beforeEach(() => {
        // Clean out the mock filesystem before each test
        mockFSUtils.reset();
    });

    test("write-read", async () => {
        const pathNames = new Array<[string, number, string]>(
            ["/src/giraffe/alligator.py", 10, "blob name for alligator.py"],
            ["/src/giraffe/crocodile.py", 11, "blob name for crocodile.py"],
            ["/src/giraffe/elephant.py", 12, "blob name for elephant.py"]
        );

        const cacheDirPath = "/mtime-cache";

        const writer = new MtimeCacheWriterImpl("test", cacheDirPath);
        await writer.write(
            (function* () {
                for (const [pathName, mtime, blobName] of pathNames) {
                    yield [pathName, mtime, blobName];
                }
            })()
        );

        const cache = await readMtimeCache("test", cacheDirPath);

        for (const [pathName, mtime, blobName] of pathNames) {
            const entry = cache.get(pathName);
            expect(entry).toBeDefined();
            expect(entry!.mtime).toBe(mtime);
            expect(entry!.name).toBe(blobName);
        }
    });
});
