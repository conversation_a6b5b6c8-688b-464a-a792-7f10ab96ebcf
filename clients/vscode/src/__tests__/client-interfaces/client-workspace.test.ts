import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import {
    addTextDocument,
    MutableTextDocument,
    resetMockWorkspace,
    Uri,
} from "../../__mocks__/vscode-mocks";
import { ClientWorkspaces } from "../../client-interfaces/client-workspace";
import { WorkspaceManager } from "../../workspace/workspace-manager";

describe("ClientWorkspaces.writeFile with organize imports disabled", () => {
    let clientWorkspaces: ClientWorkspaces;
    let mockWorkspaceManager: WorkspaceManager;
    let mockDocument: MutableTextDocument;
    let mockWorkspaceFolder: vscode.WorkspaceFolder;

    beforeEach(() => {
        mockFSUtils.reset();
        resetMockWorkspace();

        // Setup mock workspace manager
        mockWorkspaceManager = {
            awaitInitialFoldersEnumerated: jest.fn().mockResolvedValue(undefined),
        } as any;

        clientWorkspaces = new ClientWorkspaces(mockWorkspaceManager);

        // Setup mock document
        mockDocument = new MutableTextDocument(Uri.file("/workspace/test.ts"), "original content");

        // Mock save to simulate organize imports reverting the content back to original
        let saveCallCount = 0;
        mockDocument.save = jest.fn().mockImplementation(async () => {
            saveCallCount++;
            if (saveCallCount === 1) {
                // First save: simulate organize imports reverting content back to original
                const currentLength = mockDocument.getText().length;
                mockDocument.replace(0, currentLength, "original content");
            }
            return true;
        });

        // Setup mock workspace folder
        mockWorkspaceFolder = {
            uri: Uri.file("/workspace"),
            name: "test-workspace",
            index: 0,
        };

        // Create the file in the mock filesystem so openTextDocument can find it
        mockFSUtils.writeFileUtf8("/workspace/test.ts", "original content", true);

        // Add the mock document to the workspace so openTextDocument returns it
        addTextDocument(mockDocument);

        // Reset all mocks
        jest.clearAllMocks();

        // Suppress console output during tests
        jest.spyOn(console, "debug").mockImplementation(() => {});
        jest.spyOn(console, "warn").mockImplementation(() => {});
        jest.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe("when document is not in workspace folder", () => {
        test("should save file without modifying settings", async () => {
            // Mock getWorkspaceFolder to return undefined (document not in workspace)
            const originalGetWorkspaceFolder = vscode.workspace.getWorkspaceFolder;
            vscode.workspace.getWorkspaceFolder = jest.fn().mockReturnValue(undefined);

            const filePath = new QualifiedPathName("/workspace", "test.ts");
            const newContent = "new content";

            await clientWorkspaces.writeFile(filePath, newContent);

            expect(mockDocument.save).toHaveBeenCalled();
            expect(vscode.workspace.getConfiguration).not.toHaveBeenCalled();

            // Restore original function
            vscode.workspace.getWorkspaceFolder = originalGetWorkspaceFolder;
        });
    });

    describe("when document is in workspace folder", () => {
        beforeEach(() => {
            // Mock getWorkspaceFolder to return the mock workspace folder
            vscode.workspace.getWorkspaceFolder = jest.fn().mockReturnValue(mockWorkspaceFolder);
            // Mock single workspace folder
            (vscode.workspace as any).workspaceFolders = [mockWorkspaceFolder];
        });

        test("should skip backup when organizeImports already set to never", async () => {
            // Mock configuration to return organizeImports already set to "never"
            const mockConfig: any = {};
            mockConfig["editor.codeActionsOnSave"] = {};
            mockConfig["editor.codeActionsOnSave"]["source.organizeImports"] = "never";

            const mockConfigObj = {
                get: jest.fn().mockReturnValue(mockConfig),
                update: jest.fn(),
            };
            vscode.workspace.getConfiguration = jest.fn().mockReturnValue(mockConfigObj);

            const filePath = new QualifiedPathName("/workspace", "test.ts");
            const newContent = "new content";

            await clientWorkspaces.writeFile(filePath, newContent);

            expect(mockDocument.save).toHaveBeenCalled();
            expect(vscode.workspace.getConfiguration).toHaveBeenCalledWith(
                undefined,
                mockWorkspaceFolder.uri
            );
            expect(mockConfigObj.get).toHaveBeenCalledWith("[typescript]");
            expect(mockConfigObj.update).not.toHaveBeenCalled();
        });

        test("should backup settings and disable organizeImports", async () => {
            // Mock configuration to return organizeImports not set to "never"
            const mockConfig: any = {};
            mockConfig["editor.codeActionsOnSave"] = {};
            mockConfig["editor.codeActionsOnSave"]["source.organizeImports"] = "explicit";

            const mockConfigObj = {
                get: jest.fn().mockReturnValue(mockConfig),
                update: jest.fn(),
            };
            vscode.workspace.getConfiguration = jest.fn().mockReturnValue(mockConfigObj);

            // Create original settings file
            const settingsPath = "/workspace/.vscode/settings.json";
            const originalSettings = '{"existing": "config"}';
            mockFSUtils.writeFileUtf8(settingsPath, originalSettings, true);

            const filePath = new QualifiedPathName("/workspace", "test.ts");
            const newContent = "new content";

            await clientWorkspaces.writeFile(filePath, newContent);

            expect(mockDocument.save).toHaveBeenCalled();

            // Verify configuration was updated
            const expectedConfig: any = {};
            expectedConfig["editor.codeActionsOnSave"] = {};
            expectedConfig["editor.codeActionsOnSave"]["source.organizeImports"] = "never";
            expect(mockConfigObj.update).toHaveBeenCalledWith(
                "[typescript]",
                expect.objectContaining(expectedConfig),
                vscode.ConfigurationTarget.Workspace
            );

            // Verify settings file was restored
            const finalSettings = mockFSUtils.readFileUtf8(settingsPath);
            expect(finalSettings).toBe(originalSettings);
        });

        test("should use WorkspaceFolder target for multiple workspace folders", async () => {
            // Mock multiple workspace folders
            (vscode.workspace as any).workspaceFolders = [
                mockWorkspaceFolder,
                {
                    uri: Uri.file("/other-workspace"),
                    name: "other-workspace",
                    index: 1,
                },
            ];

            const mockConfig: any = {};
            mockConfig["editor.codeActionsOnSave"] = {};

            const mockConfigObj = {
                get: jest.fn().mockReturnValue(mockConfig),
                update: jest.fn(),
            };
            vscode.workspace.getConfiguration = jest.fn().mockReturnValue(mockConfigObj);

            const filePath = new QualifiedPathName("/workspace", "test.ts");
            const newContent = "new content";

            await clientWorkspaces.writeFile(filePath, newContent);

            // Verify WorkspaceFolder target was used
            expect(mockConfigObj.update).toHaveBeenCalledWith(
                "[typescript]",
                expect.any(Object),
                vscode.ConfigurationTarget.WorkspaceFolder
            );
        });

        test("should handle save error and still restore settings", async () => {
            const mockConfig: any = {};
            mockConfig["editor.codeActionsOnSave"] = {};

            const mockConfigObj = {
                get: jest.fn().mockReturnValue(mockConfig),
                update: jest.fn(),
            };
            vscode.workspace.getConfiguration = jest.fn().mockReturnValue(mockConfigObj);

            // Create original settings file
            const settingsPath = "/workspace/.vscode/settings.json";
            const originalSettings = '{"existing": "config"}';
            mockFSUtils.writeFileUtf8(settingsPath, originalSettings, true);

            // Mock document.save to throw error
            const testError = new Error("Save failed");
            (mockDocument.save as jest.Mock).mockRejectedValue(testError);

            const filePath = new QualifiedPathName("/workspace", "test.ts");
            const newContent = "new content";

            await expect(clientWorkspaces.writeFile(filePath, newContent)).rejects.toThrow(
                "Save failed"
            );

            // Verify settings file was still restored
            const finalSettings = mockFSUtils.readFileUtf8(settingsPath);
            expect(finalSettings).toBe(originalSettings);
        });

        test("should clean up empty .vscode directory when settings didn't exist", async () => {
            const mockConfig: any = {};
            mockConfig["editor.codeActionsOnSave"] = {};

            const mockConfigObj = {
                get: jest.fn().mockReturnValue(mockConfig),
                update: jest.fn(),
            };
            vscode.workspace.getConfiguration = jest.fn().mockReturnValue(mockConfigObj);

            const filePath = new QualifiedPathName("/workspace", "test.ts");
            const newContent = "new content";

            await clientWorkspaces.writeFile(filePath, newContent);

            expect(mockDocument.save).toHaveBeenCalled();

            // Verify .vscode directory doesn't exist (was cleaned up)
            expect(() => mockFSUtils.readFileUtf8("/workspace/.vscode/settings.json")).toThrow();
        });
    });
});
