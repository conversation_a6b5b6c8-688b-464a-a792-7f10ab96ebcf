import { AugmentConfigListener } from "../../augment-config-listener";
import { AuthSessionStore } from "../../auth/auth-session-store";
import { ClientAuth } from "../../client-interfaces/client-auth";

describe("client-auth", () => {
    test("getAPIToken with OAuth", async () => {
        const mockConfigListener = {
            config: {
                apiToken: "token-config",
            },
        } as AugmentConfigListener;
        const mockAuth = {
            useOAuth: true,
            getSession: () => {
                return Promise.resolve({
                    accessToken: "token-auth",
                });
            },
        } as AuthSessionStore;

        const clientAuth = new ClientAuth(mockAuth, mockConfigListener);
        const token = await clientAuth.getAPIToken();
        expect(token).toEqual("token-auth");
    });

    test("getAPIToken without OAuth", async () => {
        const mockConfigListener = {
            config: {
                apiToken: "token-config",
            },
        } as AugmentConfigListener;
        const mockAuth = {
            useOAuth: false,
            getSession: () => {
                return Promise.resolve({
                    accessToken: "token-auth",
                });
            },
        } as AuthSessionStore;

        const clientAuth = new ClientAuth(mockAuth, mockConfigListener);
        const token = await clientAuth.getAPIToken();
        expect(token).toEqual("token-config");
    });

    test("getCompletionURL with OAuth", async () => {
        const mockConfigListener = {
            config: {
                completionURL: "url-config",
            },
        } as AugmentConfigListener;
        const mockAuth = {
            useOAuth: true,
            getSession: () => {
                return Promise.resolve({
                    tenantURL: "url-auth",
                });
            },
        } as AuthSessionStore;

        const clientAuth = new ClientAuth(mockAuth, mockConfigListener);
        const url = await clientAuth.getCompletionURL();
        expect(url).toEqual("url-auth");
    });

    test("getCompletionURL without OAuth", async () => {
        const mockConfigListener = {
            config: {
                completionURL: "url-config",
            },
        } as AugmentConfigListener;
        const mockAuth = {
            useOAuth: false,
            getSession: () => {
                return Promise.resolve({
                    tenantURL: "url-auth",
                });
            },
        } as AuthSessionStore;

        const clientAuth = new ClientAuth(mockAuth, mockConfigListener);
        const url = await clientAuth.getCompletionURL();
        expect(url).toEqual("url-config");
    });
});
