import * as vscode from "vscode";

import { MockCommand } from "../../__mocks__/mock-augment-command";
import {
    generateMockWorkspaceConfig,
    getExampleUserConfig,
} from "../../__mocks__/mock-augment-config";
import {
    ExtensionContext,
    mockWorkspaceConfigChange,
    resetMockWorkspace,
} from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AugmentSession, AuthSessionStore, SESSION_SCOPES } from "../../auth/auth-session-store";
import { CommandManager } from "../../command-manager";
import { AuthCommand } from "../../commands/auth";
import { ShowActionItem, ShowAugmentCommands } from "../../commands/show-menu";
import { KeybindingWatcher } from "../../utils/keybindings";

// Convenience method to make creating the ShowAction command and dependencies
// easier.
async function makeCmd(context: ExtensionContext = new ExtensionContext()) {
    const config = new AugmentConfigListener();
    const auth = new TestAuthSessionStore(context, config);
    await auth.ready;
    const commandManager = new CommandManager(config);
    commandManager.registerGroup("", [
        new MockCommand("vscode-augment.unknownCommand"),
        new MockCommand("vscode-augment.exampleCommand"),
        new MockCommand("vscode-augment.cantRun", { canRun: false }),
        new MockCommand("vscode-augment.commandWithTitle", { title: "Command With Title" }),
    ]);
    commandManager.registerGroup("Example Group", [
        new MockCommand("vscode-augment.secondUnknownCommand"),
        new MockCommand("vscode-augment.secondExampleCommand"),
        new MockCommand("vscode-augment.secondCantRun", { canRun: false }),
        new MockCommand("vscode-augment.secondCommandWithTitle", { title: "Command With Title" }),
    ]);
    commandManager.register([new MockCommand("vscode-augment.commandWithoutGroup")]);
    const mockExtension = {} as any;
    mockExtension.keybindingWatcher = new KeybindingWatcher(mockExtension);
    return new TestShowActions(mockExtension, context, commandManager);
}

describe("show-actions", () => {
    beforeEach(() => {
        // Clean out the mock vscode workspace before each test
        // Without this the mockWorkspaceConfigChange() does
        // not work as expected.
        resetMockWorkspace();
    });

    test("show commands for default settings", async () => {
        /* eslint-disable @typescript-eslint/naming-convention */
        const titles = {
            "vscode-augment.exampleCommand": "Example",
            "vscode-augment.cantRun": "Cannot Run",
            "vscode-augment.secondExampleCommand": "Second Example",
        };
        /* eslint-enable @typescript-eslint/naming-convention */

        const cmd = await makeCmd();
        expect(cmd.getActions(titles, {})).resolves.toEqual([
            {
                commandID: "vscode-augment.exampleCommand",
                label: "Example",
            },
            {
                label: "Example Group",
                kind: vscode.QuickPickItemKind.Separator,
            },
            {
                commandID: "vscode-augment.secondExampleCommand",
                label: "Second Example",
            },
        ]);
    });

    // This test ensures that we don't try to sign the user in when
    // we're using an API token.
    test("asserts quick picker is shown when API token is used", async () => {
        jest.spyOn(vscode.window, "showQuickPick");

        const cmd = await makeCmd();

        const mockConfig = generateMockWorkspaceConfig(
            getExampleUserConfig({
                advanced: {
                    oauth: undefined,
                },
            })
        );
        mockWorkspaceConfigChange(mockConfig);

        await cmd.run();

        expect(vscode.window.showQuickPick).toHaveBeenCalled();
    });

    test("asserts quick picker is shown when the user is signed in and no API token", async () => {
        jest.spyOn(vscode.window, "showQuickPick");
        jest.spyOn(vscode.commands, "executeCommand");

        const context = new ExtensionContext();
        jest.spyOn(context.secrets, "get").mockReturnValue(
            new Promise((resolve) => {
                const session: AugmentSession = {
                    accessToken: "token",
                    scopes: SESSION_SCOPES,
                    tenantURL: "tenantURL",
                };
                resolve(JSON.stringify(session));
            })
        );
        const cmd = await makeCmd(context);

        const mockConfig = generateMockWorkspaceConfig(
            getExampleUserConfig({
                advanced: {
                    apiToken: "",
                },
            })
        );
        mockWorkspaceConfigChange(mockConfig);

        await cmd.run();

        expect(vscode.window.showQuickPick).toHaveBeenCalled();
        expect(vscode.commands.executeCommand).not.toHaveBeenCalledWith(
            AuthCommand.signInCommandID
        );
    });
});

// This test class makes getActions public to make testing easier
class TestShowActions extends ShowAugmentCommands {
    public async getActions(
        commandTitleMap: {
            [key: string]: string;
        },
        commandKeybindingMap: {
            [key: string]: string;
        }
    ): Promise<Array<ShowActionItem>> {
        return super.getActions(commandTitleMap, commandKeybindingMap);
    }
}

class TestAuthSessionStore extends AuthSessionStore {
    public readonly ready: Promise<void>;

    constructor(context: vscode.ExtensionContext, config: AugmentConfigListener) {
        super(context, config);

        this.ready = this._ready;
    }
}
