import { CancellationTokenSource, window } from "../../__mocks__/vscode-mocks";
import { InsertCompletionCommand } from "../../commands/insert-completion";
import {
    dispatchCompletionRequest,
    dispatchCompletionRequestCancelled,
} from "../../completions/completion-events";
import { SyncingEnabledTracker } from "../../workspace/syncing-enabled-tracker";

describe("InsertCompletionCommand", () => {
    let syncingEnabledTracker = new SyncingEnabledTracker();

    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    describe("requestCompletion", () => {
        it("should timeout", () => {
            const command = new InsertCompletionCommand(undefined as any, syncingEnabledTracker);
            const cancellation = new CancellationTokenSource();
            const promise = command.requestCompletion(cancellation.token);
            jest.advanceTimersByTime(command.completionTimeoutMS + 1);
            return expect(promise).resolves.toEqual({
                foundCompletion: false,
                resultType: "timeout",
            });
        });

        it("should handle user cancellation", () => {
            const command = new InsertCompletionCommand(undefined as any, syncingEnabledTracker);
            const cancellation = new CancellationTokenSource();
            const promise = command.requestCompletion(cancellation.token);
            cancellation.cancel();
            return expect(promise).resolves.toEqual({
                foundCompletion: false,
                resultType: "cancelled",
            });
        });

        it("should handle completion", () => {
            const command = new InsertCompletionCommand(undefined as any, syncingEnabledTracker);
            const cancellation = new CancellationTokenSource();
            const promise = command.requestCompletion(cancellation.token);

            const completion = {
                requestId: "12345678-1234-1234-1234-123456789123",
                prefix: "",
                suffix: "",
                pathName: "",
                isReused: false,
                document: null as any,
                completions: [],
                repoRoot: "",
                occuredAt: new Date(),
            };
            dispatchCompletionRequest(completion);

            return expect(promise).resolves.toEqual({
                foundCompletion: false,
                resultType: "completion",
            });
        });

        it("should handle cancelled completion", () => {
            const command = new InsertCompletionCommand(undefined as any, syncingEnabledTracker);
            const cancellation = new CancellationTokenSource();
            const promise = command.requestCompletion(cancellation.token);
            dispatchCompletionRequestCancelled();
            return expect(promise).resolves.toEqual({
                foundCompletion: false,
                resultType: "cancelled",
            });
        });
    });

    describe("run", () => {
        it("should show warning message on timeout", async () => {
            const showWarningSpy = jest.spyOn(window, "showWarningMessage");
            const command = new InsertCompletionCommand(undefined as any, syncingEnabledTracker);
            const promise = command.run();
            jest.advanceTimersByTime(command.completionTimeoutMS + 1);
            await promise;
            expect(showWarningSpy).toHaveBeenCalledWith("Failed to request a completion");
        });

        it("should show information message on cancellation", async () => {
            const showInfoSpy = jest.spyOn(window, "showInformationMessage");
            const command = new InsertCompletionCommand(undefined as any, syncingEnabledTracker);
            const promise = command.run();
            dispatchCompletionRequestCancelled();
            await promise;
            expect(showInfoSpy).toHaveBeenCalledWith("Completion request cancelled");
        });

        it("should show information message on no completions", async () => {
            const showInfoSpy = jest.spyOn(window, "showInformationMessage");
            const command = new InsertCompletionCommand(undefined as any, syncingEnabledTracker);
            const promise = command.run();
            const completion = {
                requestId: "12345678-1234-1234-1234-123456789123",
                prefix: "",
                suffix: "",
                pathName: "",
                isReused: false,
                document: null as any,
                completions: [],
                repoRoot: "",
                occuredAt: new Date(),
            };
            dispatchCompletionRequest(completion);
            await promise;
            expect(showInfoSpy).toHaveBeenCalledWith("No completions found");
        });
    });
});
