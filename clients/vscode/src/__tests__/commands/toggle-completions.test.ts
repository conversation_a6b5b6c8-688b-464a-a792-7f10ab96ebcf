import type { WorkspaceConfiguration } from "vscode";

import {
    generateMockWorkspaceConfig,
    getExampleUserConfig,
} from "../../__mocks__/mock-augment-config";
import {
    ConfigurationTarget,
    mockWorkspaceConfigChange,
    resetMockWorkspace,
    workspace,
} from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { ToggleCompletionsCommand } from "../../commands/toggle-completions";
import { SyncingEnabledTracker } from "../../workspace/syncing-enabled-tracker";

class ToggleCompletionsTestKit {
    public readonly config: AugmentConfigListener;
    private _workspaceConfig: WorkspaceConfiguration;
    private readonly _inspectMock: jest.Mock;
    public readonly updateMock: jest.Mock;
    public readonly enableCompletions: Map<ConfigurationTarget, boolean> = new Map();

    constructor() {
        this.config = new AugmentConfigListener();
        this.enableCompletions.set(ConfigurationTarget.Global, true);
        this._inspectMock = jest.fn();
        this._inspectMock.mockImplementation(() => {
            return {
                globalValue: this.enableCompletions.get(ConfigurationTarget.Global),
                workspaceValue: this.enableCompletions.get(ConfigurationTarget.Workspace),
            };
        });

        this.updateMock = jest.fn();
        this.updateMock.mockImplementation((key, value, target) => {
            if (key !== ToggleCompletionsCommand.autoCompletionsConfigKey) {
                return;
            }
            this.enableCompletions.set(target, value);
            this.updateConfigListener();
        });

        this._workspaceConfig = {
            ...generateMockWorkspaceConfig(),
            get: jest.fn(),
            has: jest.fn(),
            inspect: this._inspectMock,
            update: this.updateMock,
        };
    }

    updateConfigListener() {
        const options = [
            this.enableCompletions.get(ConfigurationTarget.Workspace),
            this.enableCompletions.get(ConfigurationTarget.Global),
        ];
        const newValue = options.find((option) => option !== undefined);
        const mockConfig = generateMockWorkspaceConfig({
            completions: {
                enableAutomaticCompletions: newValue,
            },
        });
        mockWorkspaceConfigChange(mockConfig);
    }

    getConfigurationMock(): WorkspaceConfiguration {
        const options = [
            this.enableCompletions.get(ConfigurationTarget.Workspace),
            this.enableCompletions.get(ConfigurationTarget.Global),
        ];
        const value = options.find((option) => option !== undefined);
        return {
            ...getExampleUserConfig({
                completions: {
                    enableAutomaticCompletions: value,
                },
            }),
            ...this._workspaceConfig,
        };
    }
}

describe("toggle completions command", () => {
    let syncingEnabledTracker: SyncingEnabledTracker;

    beforeEach(() => {
        resetMockWorkspace();
        syncingEnabledTracker = new SyncingEnabledTracker();
    });

    afterEach(() => {
        resetMockWorkspace();
    });

    describe("title", () => {
        let config: AugmentConfigListener;

        beforeEach(() => {
            config = new AugmentConfigListener();
        });

        afterEach(() => {
            config.dispose();
        });

        test("return toggle off title", () => {
            const mockConfig = generateMockWorkspaceConfig({
                completions: {
                    enableAutomaticCompletions: true,
                },
            });
            mockWorkspaceConfigChange(mockConfig);

            const cmd = new ToggleCompletionsCommand(config, syncingEnabledTracker);
            expect(cmd.title).toEqual("Turn Automatic Completions Off");
        });

        test("return toggle on title", () => {
            const mockConfig = generateMockWorkspaceConfig({
                completions: {
                    enableAutomaticCompletions: false,
                },
            });
            mockWorkspaceConfigChange(mockConfig);

            const cmd = new ToggleCompletionsCommand(config, syncingEnabledTracker);
            expect(cmd.title).toEqual("Turn Automatic Completions On");
        });
    });

    describe("run", () => {
        let kit: ToggleCompletionsTestKit;

        beforeEach(() => {
            resetMockWorkspace();
            kit = new ToggleCompletionsTestKit();

            const getConfigSpy = jest.spyOn(workspace, "getConfiguration");
            getConfigSpy.mockImplementation(() => kit.getConfigurationMock());
        });

        afterEach(() => {
            resetMockWorkspace();
        });

        test("update user config", () => {
            const cmd = new ToggleCompletionsCommand(kit.config, syncingEnabledTracker);
            cmd.run();

            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                false,
                ConfigurationTarget.Global
            );

            jest.clearAllMocks();

            cmd.run();

            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                true,
                ConfigurationTarget.Global
            );
        });

        test("update workspace config", () => {
            kit.enableCompletions.set(ConfigurationTarget.Workspace, true);

            const cmd = new ToggleCompletionsCommand(kit.config, syncingEnabledTracker);
            cmd.run();

            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                false,
                ConfigurationTarget.Workspace
            );

            jest.clearAllMocks();

            cmd.run();

            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                true,
                ConfigurationTarget.Workspace
            );
        });

        test("update user, workspace, user config", () => {
            const cmd = new ToggleCompletionsCommand(kit.config, syncingEnabledTracker);
            cmd.run();
            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                false,
                ConfigurationTarget.Global
            );

            // Switch to workspace
            kit.enableCompletions.set(ConfigurationTarget.Workspace, true);
            kit.updateConfigListener();

            jest.clearAllMocks();
            cmd.run();
            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                false,
                ConfigurationTarget.Workspace
            );

            jest.clearAllMocks();
            cmd.run();
            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                true,
                ConfigurationTarget.Workspace
            );

            // Switch to user
            kit.enableCompletions.delete(ConfigurationTarget.Workspace);
            kit.updateConfigListener();
            jest.clearAllMocks();
            cmd.run();
            expect(kit.updateMock).toHaveBeenCalledWith(
                ToggleCompletionsCommand.autoCompletionsConfigKey,
                true,
                ConfigurationTarget.Global
            );
        });
    });
});
