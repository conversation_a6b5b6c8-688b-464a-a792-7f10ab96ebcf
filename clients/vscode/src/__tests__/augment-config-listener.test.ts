import {
    generateMockWorkspaceConfig,
    generateUncheckedMockWorkspaceConfig,
    getExampleAugmenConfig,
    getExampleUserConfig,
} from "../__mocks__/mock-augment-config";
import {
    ConfigurationTarget,
    mockWorkspaceConfigChange,
    resetMockWorkspace,
    workspace,
} from "../__mocks__/vscode-mocks";
import {
    AugmentConfig,
    AugmentConfigListener,
    PartialRawSettings,
} from "../augment-config-listener";

/**
 * compareConfigs verifies that the given config matches the expected config.
 */
function compareConfigs(config: AugmentConfig, expected: PartialRawSettings) {
    expect(config.completionURL).toBe(expected.advanced?.completionURL);
    expect(config.modelName).toBe(expected.advanced?.model);
    expect(config.enableUpload).toBe(expected.advanced?.enableWorkspaceUpload);
}

class ConfigListenerTestKit {
    public configListener = new AugmentConfigListener();
}

describe("augment-config-listener", () => {
    beforeEach(() => {
        // Clean out the mock vscode workspace before each test
        resetMockWorkspace();
    });

    afterEach(() => {
        resetMockWorkspace();
    });

    // Verify that AugmentConfigListener.config produces the current configuration.
    test("get-config", () => {
        const kit = new ConfigListenerTestKit();
        const configListener = kit.configListener;
        const config = configListener.config;
        compareConfigs(config, getExampleUserConfig());
    });

    // Verify that AugmentConfigListener.onDidChange() will notify subscribers
    // of changes to the configured model.
    test("listen-model-change", () => {
        const kit = new ConfigListenerTestKit();
        const configListener = kit.configListener;

        // Listen for configuration changes.
        const notify = jest.fn(() => {});
        configListener.onDidChange(() => notify());
        expect(notify).not.toHaveBeenCalled();

        // Verify original config
        const config = configListener.config;
        compareConfigs(config, getExampleUserConfig());

        // Change only the model name
        const newAugmentConfig = generateMockWorkspaceConfig({
            advanced: {
                model: getExampleUserConfig()?.advanced?.model + "xyz",
            },
        });

        mockWorkspaceConfigChange(newAugmentConfig);
        expect(notify).toHaveBeenCalled();
    });

    // Verify that AugmentConfigListener.onDidChange() will notify subscribers
    // of changes to memorization.
    test("listen-model-memorization", () => {
        const kit = new ConfigListenerTestKit();
        const configListener = kit.configListener;

        // Listen for configuration changes.
        const notify = jest.fn(() => {});
        configListener.onDidChange(() => notify());
        expect(notify).not.toHaveBeenCalled();

        // Verify original config
        const config = configListener.config;
        compareConfigs(config, getExampleUserConfig());

        // Change only the memory enablement
        const newAugmentConfig = generateMockWorkspaceConfig({
            advanced: {
                enableWorkspaceUpload: !getExampleUserConfig()?.advanced?.enableWorkspaceUpload,
            },
        });

        mockWorkspaceConfigChange(newAugmentConfig);
        expect(notify).toHaveBeenCalled();
    });

    const normalizationTests: GetConfigTestCase = {
        noconfig: {
            userConfig: {
                // Scenario where no settings are defined by VSCode.
            },
            wantConfig: getExampleAugmenConfig(),
        },
        minimal: {
            userConfig: {
                advanced: {
                    apiToken: "example-token-abcd1234",
                    completionURL: "https://api.augmentcode.com",
                },
            },
            wantConfig: getExampleAugmenConfig({
                apiToken: "EXAMPLE-TOKEN-ABCD1234",
                completionURL: "https://api.augmentcode.com",
            }),
        },
        chatConfig: {
            userConfig: {
                advanced: {
                    chat: {
                        stream: false,
                    },
                },
            },
            wantConfig: getExampleAugmenConfig({
                chat: {
                    stream: false,
                },
            }),
        },
        legacyAPITokenAndCompletionURL: {
            userConfig: {
                apiToken: "example-token-abcd1234",
                completionURL: "https://api.augmentcode.com",
            },
            wantConfig: getExampleAugmenConfig({
                apiToken: "EXAMPLE-TOKEN-ABCD1234",
                completionURL: "https://api.augmentcode.com",
            }),
        },
        internal: {
            userConfig: {
                advanced: {
                    model: "example-model",
                    codeInstruction: {
                        model: "edit-model",
                    },
                    enableDebugFeatures: true,
                    enableReviewerWorkflows: true,
                },
            },
            wantConfig: getExampleAugmenConfig({
                modelName: "example-model",
                codeInstruction: {
                    model: "edit-model",
                },
                enableDebugFeatures: true,
                enableReviewerWorkflows: true,
                nextEdit: {
                    useCursorDecorations: false,
                },
            }),
        },
    };
    for (const [k, v] of Object.entries(normalizationTests)) {
        test(`get-config - ${k}`, () => {
            const kit = new ConfigListenerTestKit();
            const configListener = kit.configListener;
            mockWorkspaceConfigChange(generateMockWorkspaceConfig(v.userConfig));
            expect(configListener.config).toEqual(v.wantConfig);
        });
    }

    describe("migrateLegacyConfig", () => {
        test("do not override new value with old value", async () => {
            const inspectFn = jest.fn();
            const updateFn = jest.fn();

            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enableAutomaticCompletions: false,
            });
            mockConfig.inspect = inspectFn;
            mockConfig.update = updateFn;

            mockWorkspaceConfigChange(mockConfig);
            const getConfigSpy = jest.spyOn(workspace, "getConfiguration");
            getConfigSpy.mockImplementation(() => mockConfig);

            inspectFn.mockImplementation((key: string) => {
                if (key === "enableAutomaticCompletions") {
                    return {
                        globalValue: false,
                        workspaceValue: false,
                    };
                } else if (key === "completions.enableAutomaticCompletions") {
                    return {
                        globalValue: true,
                        workspaceValue: true,
                    };
                }
                return undefined;
            });

            const kit = new ConfigListenerTestKit();
            const configListener = kit.configListener;
            await configListener.migrateLegacyConfig();

            expect(updateFn).toHaveBeenCalledTimes(2);
            expect(updateFn).toHaveBeenCalledWith(
                "enableAutomaticCompletions",
                undefined,
                ConfigurationTarget.Global
            );
            expect(updateFn).toHaveBeenCalledWith(
                "enableAutomaticCompletions",
                undefined,
                ConfigurationTarget.Workspace
            );
        });

        test("enableAutomaticCompletions -> completions.enableAutomaticCompletions global", async () => {
            const inspectFn = jest.fn();
            const updateFn = jest.fn();

            const mockConfig = generateUncheckedMockWorkspaceConfig({
                enableAutomaticCompletions: false,
            });
            mockConfig.inspect = inspectFn;
            mockConfig.update = updateFn;

            mockWorkspaceConfigChange(mockConfig);
            const getConfigSpy = jest.spyOn(workspace, "getConfiguration");
            getConfigSpy.mockImplementation(() => mockConfig);

            inspectFn.mockImplementation((key: string) => {
                if (key === "enableAutomaticCompletions") {
                    return {
                        globalValue: false,
                        workspaceValue: undefined,
                    };
                }
                return undefined;
            });

            const kit = new ConfigListenerTestKit();
            const configListener = kit.configListener;
            await configListener.migrateLegacyConfig();

            expect(updateFn).toHaveBeenCalledTimes(2);
            expect(updateFn).toHaveBeenCalledWith(
                "completions.enableAutomaticCompletions",
                false,
                ConfigurationTarget.Global
            );
            expect(updateFn).toHaveBeenCalledWith(
                "enableAutomaticCompletions",
                undefined,
                ConfigurationTarget.Global
            );

            inspectFn.mockClear();
            updateFn.mockClear();

            inspectFn.mockImplementation((key: string) => {
                if (key === "enableAutomaticCompletions") {
                    return {
                        globalValue: undefined,
                        workspaceValue: false,
                    };
                }
                return undefined;
            });

            await configListener.migrateLegacyConfig();

            expect(updateFn).toHaveBeenCalledTimes(2);
            expect(updateFn).toHaveBeenCalledWith(
                "completions.enableAutomaticCompletions",
                false,
                ConfigurationTarget.Workspace
            );
            expect(updateFn).toHaveBeenCalledWith(
                "enableAutomaticCompletions",
                undefined,
                ConfigurationTarget.Workspace
            );
        });

        test("disableCompletionsByLanguage -> completions.disableCompletionsByLanguage global", async () => {
            const inspectFn = jest.fn();
            const updateFn = jest.fn();

            const mockConfig = generateUncheckedMockWorkspaceConfig({
                disableCompletionsByLanguage: ["js", "jsx"],
            });
            mockConfig.inspect = inspectFn;
            mockConfig.update = updateFn;

            mockWorkspaceConfigChange(mockConfig);
            const getConfigSpy = jest.spyOn(workspace, "getConfiguration");
            getConfigSpy.mockImplementation(() => mockConfig);

            inspectFn.mockImplementation((key: string) => {
                if (key === "disableCompletionsByLanguage") {
                    return {
                        globalValue: ["js", "jsx"],
                        workspaceValue: undefined,
                    };
                }
                return undefined;
            });

            const kit = new ConfigListenerTestKit();
            const configListener = kit.configListener;
            await configListener.migrateLegacyConfig();

            expect(updateFn).toHaveBeenCalledTimes(2);
            expect(updateFn).toHaveBeenCalledWith(
                "completions.disableCompletionsByLanguage",
                ["js", "jsx"],
                ConfigurationTarget.Global
            );
            expect(updateFn).toHaveBeenCalledWith(
                "disableCompletionsByLanguage",
                undefined,
                ConfigurationTarget.Global
            );

            inspectFn.mockClear();
            updateFn.mockClear();

            inspectFn.mockImplementation((key: string) => {
                if (key === "disableCompletionsByLanguage") {
                    return {
                        globalValue: undefined,
                        workspaceValue: ["ts", "tsx"],
                    };
                }
                return undefined;
            });

            await configListener.migrateLegacyConfig();

            expect(updateFn).toHaveBeenCalledTimes(2);
            expect(updateFn).toHaveBeenCalledWith(
                "completions.disableCompletionsByLanguage",
                ["ts", "tsx"],
                ConfigurationTarget.Workspace
            );
            expect(updateFn).toHaveBeenCalledWith(
                "disableCompletionsByLanguage",
                undefined,
                ConfigurationTarget.Workspace
            );
        });
    });
});

interface GetConfigTestCase {
    [key: string]: {
        userConfig: { [key: string]: any };
        wantConfig: AugmentConfig;
    };
}
