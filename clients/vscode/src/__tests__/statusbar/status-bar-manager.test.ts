import { newMockStatusBar, StatusBarItem, ThemeColor, window } from "../../__mocks__/vscode-mocks";
import { StatusBarClick } from "../../commands/status-bar-click";
import { AugmentIcons } from "../../statusbar/icons";
import { StatePriority } from "../../statusbar/priorities";
import { DEFAULT_LABEL, StatusBarManager } from "../../statusbar/status-bar-manager";
import { initialState, StateDefinition } from "../../statusbar/status-bar-states";

function assertState(statusBar: StatusBarItem, state: StateDefinition) {
    expect(statusBar.tooltip).toEqual(state.tooltip);
    expect(statusBar.text).toContain(state.icon);
    if (state.label) {
        expect(statusBar.text).toContain(state.label);
    } else {
        expect(statusBar.text).toContain(DEFAULT_LABEL);
    }
    expect(statusBar.backgroundColor).toEqual(state.colors?.background);
    expect(statusBar.color).toEqual(state.colors?.foreground);
}

describe("status-bar-manager", () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("default state", () => {
        const mockStatusBar: StatusBarItem = newMockStatusBar();

        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);

        new StatusBarManager();

        assertState(mockStatusBar, initialState);
        expect(mockStatusBar.name).toEqual("Augment");
        expect(mockStatusBar.command).toEqual(StatusBarClick.commandID);
        expect(mockStatusBar.show).toHaveBeenCalled();
    });

    test("update state", () => {
        const mockStatusBar: StatusBarItem = newMockStatusBar();

        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);

        const state: StateDefinition = {
            priority: StatePriority.high,
            tooltip: "Example tooltip",
            icon: AugmentIcons.loading,
            colors: {
                background: new ThemeColor("statusBarItem.errorBackground"),
                foreground: new ThemeColor("statusBarItem.errorForeground"),
            },
        };

        const manager = new StatusBarManager();
        manager.setState(state);
        assertState(mockStatusBar, state);
    });

    test("reset state", () => {
        const mockStatusBar: StatusBarItem = newMockStatusBar();

        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);

        const state: StateDefinition = {
            priority: StatePriority.high,
            tooltip: "Example tooltip",
            icon: AugmentIcons.loading,
            colors: {
                background: new ThemeColor("statusBarItem.errorBackground"),
                foreground: new ThemeColor("statusBarItem.errorForeground"),
            },
        };

        const manager = new StatusBarManager();
        manager.setState(state);
        assertState(mockStatusBar, state);

        manager.reset();
        assertState(mockStatusBar, initialState);
    });
});
