import { AugmentIcons } from "../../statusbar/icons";
import { StatePriority } from "../../statusbar/priorities";
import { StateManager } from "../../statusbar/state-manager";
import { initialState } from "../../statusbar/status-bar-states";

describe("state-manager", () => {
    test("setState", () => {
        const manager = new StateManager(initialState);
        expect(manager.getPriorityState()).toEqual(initialState);

        const lowState = {
            priority: StatePriority.low,
            tooltip: "Low",
            icon: AugmentIcons.simple,
        };
        const mediumState = {
            priority: StatePriority.medium,
            tooltip: "Medium",
            icon: AugmentIcons.simple,
        };
        const secondMediumState = {
            priority: StatePriority.medium,
            tooltip: "Medium 2",
            icon: AugmentIcons.simple,
        };
        const highState = {
            priority: StatePriority.high,
            tooltip: "High",
            icon: AugmentIcons.simple,
        };

        const lowDisposable = manager.setState(lowState);
        expect(manager.getPriorityState()).toEqual(lowState);

        const mediumDisposable = manager.setState(mediumState);
        expect(manager.getPriorityState()).toEqual(mediumState);

        const secondMediumDisposable = manager.setState(secondMediumState);
        expect(manager.getPriorityState()).toEqual(secondMediumState);

        const highDisposable = manager.setState(highState);
        expect(manager.getPriorityState()).toEqual(highState);

        highDisposable.dispose();
        expect(manager.getPriorityState()).toEqual(secondMediumState);

        secondMediumDisposable.dispose();
        expect(manager.getPriorityState()).toEqual(mediumState);

        mediumDisposable.dispose();
        expect(manager.getPriorityState()).toEqual(lowState);

        lowDisposable.dispose();
        expect(manager.getPriorityState()).toEqual(initialState);
    });

    test("handle duplicates of setState", () => {
        const manager = new StateManager(initialState);
        expect(manager.getPriorityState()).toEqual(initialState);

        const lowState = {
            priority: StatePriority.low,
            tooltip: "Low",
            icon: AugmentIcons.simple,
        };

        const lowDisposable1 = manager.setState(lowState);
        const lowDisposable2 = manager.setState(lowState);
        const lowDisposable3 = manager.setState(lowState);

        expect(manager.getPriorityState()).toEqual(lowState);

        lowDisposable1.dispose();
        expect(manager.getPriorityState()).toEqual(lowState);

        lowDisposable2.dispose();
        expect(manager.getPriorityState()).toEqual(lowState);

        lowDisposable3.dispose();
        expect(manager.getPriorityState()).toEqual(initialState);
    });

    test("disposable should only remove one instance of state", () => {
        const manager = new StateManager(initialState);
        expect(manager.getPriorityState()).toEqual(initialState);

        const lowState = {
            priority: StatePriority.low,
            tooltip: "Low",
            icon: AugmentIcons.simple,
        };

        const lowDisposable1 = manager.setState(lowState);
        const lowDisposable2 = manager.setState(lowState);

        expect(manager.getPriorityState()).toEqual(lowState);

        lowDisposable1.dispose();
        expect(manager.getPriorityState()).toEqual(lowState);

        // Calling the first disposable again should not remove the state
        lowDisposable1.dispose();
        lowDisposable1.dispose();
        expect(manager.getPriorityState()).toEqual(lowState);

        lowDisposable2.dispose();
        expect(manager.getPriorityState()).toEqual(initialState);
    });
});
