import * as vscode from "vscode";

import {
    ExtensionContext,
    resetMockWorkspace,
    TrackedDisposable,
    Webview,
} from "../../__mocks__/vscode-mocks";
import { APIServer } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { FeatureFlagManager } from "../../feature-flags";
import { ActionsModel } from "../../main-panel/action-cards/actions-model";
import { DerivedStateName } from "../../main-panel/action-cards/types";
import { AwaitingSyncingPermissionApp } from "../../main-panel/apps/awaiting-syncing-permission-app";
import { AugmentGlobalState } from "../../utils/context";
import { DisposableService } from "../../utils/disposable-service";
import { SystemStateName, SystemStatus } from "../../utils/types";
import {
    AsyncWebViewMessage,
    MainPanelApp,
    WebViewMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import { WorkspaceManagerTestKit } from "../workspace/workspace-manager-test-kit";

describe("AwaitingSyncingPermissionApp", () => {
    let kit: AwaitingSyncingPermissionAppKit;

    beforeEach(() => {
        TrackedDisposable.resetStats();
        resetMockWorkspace();

        kit = new AwaitingSyncingPermissionAppKit();
    });

    afterEach(() => {
        kit.dispose();

        TrackedDisposable.assertDisposed();
    });

    test("appType should return MainPanelApp.awaitingSyncingPermission", () => {
        expect(kit.app.appType()).toBe(MainPanelApp.awaitingSyncingPermission);
    });

    test("title should return 'Syncing Permission Needed'", () => {
        expect(kit.app.title()).toBe("Syncing Permission Needed");
    });

    test("load permission app and deny permission", async () => {
        const mockWebview = new Webview({}, "", "");
        const postMessageSpy = jest.spyOn(mockWebview, "postMessage");
        const changeAppSpy = jest.spyOn(kit.changeWebviewAppEvent, "fire");

        kit.app.register(mockWebview);

        // Start with syncing permission needed
        kit.actionsModel.setSystemStateStatus(
            SystemStateName.syncingPermitted,
            SystemStatus.initializing
        );
        expect(
            kit.actionsModel.isDerivedStateSatisfied(DerivedStateName.syncingPermissionNeeded)
        ).toBe(true);

        mockWebview.fakeMessageFromWebview(
            asyncWrapper({
                type: WebViewMessageType.awaitingSyncingPermissionLoaded,
            })
        );

        expect(postMessageSpy).toHaveBeenCalledWith({
            type: "main-panel-actions",
            data: [DerivedStateName.syncingPermissionNeeded],
        });
        expect(changeAppSpy).not.toHaveBeenCalled();

        // Ensure that disabling sync sets up for chat app
        kit.actionsModel.setSystemStateStatus(
            SystemStateName.syncingPermitted,
            SystemStatus.incomplete
        );
        expect(
            kit.actionsModel.isDerivedStateSatisfied(DerivedStateName.syncingPermissionNeeded)
        ).toBe(false);

        // Now trigger workspace populated state to complete the transition
        kit.actionsModel.setSystemStateStatus(
            SystemStateName.workspacePopulated,
            SystemStatus.complete
        );

        // Manually call the handleDerivedStateChange method to simulate the event firing
        const derivedState = {
            name: DerivedStateName.workspacePopulated,
            renderOrder: 0,
            desiredConditions: [],
        };
        kit.app["_workspaceChecked"] = true;
        kit.app["handleDerivedStateChange"]([derivedState]);

        expect(changeAppSpy).toHaveBeenCalledWith(MainPanelApp.chat);
    });

    test("load permission app and grant permission", async () => {
        const mockWebview = new Webview({}, "", "");
        const postMessageSpy = jest.spyOn(mockWebview, "postMessage");
        const changeAppSpy = jest.spyOn(kit.changeWebviewAppEvent, "fire");

        kit.app.register(mockWebview);

        // Start with syncing permission needed
        kit.actionsModel.setSystemStateStatus(
            SystemStateName.syncingPermitted,
            SystemStatus.initializing
        );
        expect(
            kit.actionsModel.isDerivedStateSatisfied(DerivedStateName.syncingPermissionNeeded)
        ).toBe(true);

        mockWebview.fakeMessageFromWebview(
            asyncWrapper({
                type: WebViewMessageType.awaitingSyncingPermissionLoaded,
            })
        );

        expect(postMessageSpy).toHaveBeenCalledWith({
            type: "main-panel-actions",
            data: [DerivedStateName.syncingPermissionNeeded],
        });
        expect(changeAppSpy).not.toHaveBeenCalled();

        // Ensure that enabling sync sets up for chat app
        kit.actionsModel.setSystemStateStatus(
            SystemStateName.syncingPermitted,
            SystemStatus.complete
        );
        expect(
            kit.actionsModel.isDerivedStateSatisfied(DerivedStateName.syncingPermissionNeeded)
        ).toBe(false);

        // Now trigger workspace populated state to complete the transition
        kit.actionsModel.setSystemStateStatus(
            SystemStateName.workspacePopulated,
            SystemStatus.complete
        );

        // Manually call the handleDerivedStateChange method to simulate the event firing
        const derivedState = {
            name: DerivedStateName.workspacePopulated,
            renderOrder: 0,
            desiredConditions: [],
        };
        kit.app["_workspaceChecked"] = true;
        kit.app["handleDerivedStateChange"]([derivedState]);

        expect(changeAppSpy).toHaveBeenCalledWith(MainPanelApp.chat);
    });

    test("load permission app and listen for grant permission button click", async () => {
        const mockWebview = new Webview({}, "", "");
        const postMessageSpy = jest.spyOn(mockWebview, "postMessage");
        const changeAppSpy = jest.spyOn(kit.changeWebviewAppEvent, "fire");
        const enableSyncingSpy = jest.spyOn(
            kit.workmanagerKit.syncingEnabledTracker,
            "enableSyncing"
        );

        kit.app.register(mockWebview);

        // Start with syncing permission needed
        kit.actionsModel.setSystemStateStatus(
            SystemStateName.syncingPermitted,
            SystemStatus.initializing
        );
        expect(
            kit.actionsModel.isDerivedStateSatisfied(DerivedStateName.syncingPermissionNeeded)
        ).toBe(true);

        mockWebview.fakeMessageFromWebview(
            asyncWrapper({
                type: WebViewMessageType.awaitingSyncingPermissionLoaded,
            })
        );

        expect(postMessageSpy).toHaveBeenCalledWith({
            type: "main-panel-actions",
            data: [DerivedStateName.syncingPermissionNeeded],
        });
        expect(changeAppSpy).not.toHaveBeenCalled();
        expect(enableSyncingSpy).not.toHaveBeenCalled();

        mockWebview.fakeMessageFromWebview({
            type: WebViewMessageType.mainPanelPerformAction,
            data: "grant-sync-permission",
        });
        expect(enableSyncingSpy).toHaveBeenCalled();
    });
});

class AwaitingSyncingPermissionAppKit extends DisposableService {
    public readonly app: AwaitingSyncingPermissionApp;
    public readonly actionsModel: ActionsModel;
    private _apiServer: APIServer;
    private _config: AugmentConfigListener;
    public readonly changeWebviewAppEvent: vscode.EventEmitter<MainPanelApp>;
    private _featureFlagManager: FeatureFlagManager;
    public readonly workmanagerKit: WorkspaceManagerTestKit;
    public readonly workspaceManager: WorkspaceManager;

    constructor() {
        super();

        const globalState = new AugmentGlobalState(new ExtensionContext());
        this.actionsModel = new ActionsModel(globalState);
        this._apiServer = {} as APIServer;
        this._config = new AugmentConfigListener();
        this.changeWebviewAppEvent = new vscode.EventEmitter<MainPanelApp>();
        this._featureFlagManager = new FeatureFlagManager();

        this.workmanagerKit = new WorkspaceManagerTestKit();
        this.workspaceManager = this.workmanagerKit.createWorkspaceManager();

        this.app = new AwaitingSyncingPermissionApp(
            this.actionsModel,
            this._apiServer,
            this._config,
            this.workmanagerKit.syncingEnabledTracker,
            this.changeWebviewAppEvent,
            this._featureFlagManager,
            "unknown"
        );
        this.addDisposables(
            this.actionsModel,
            this.changeWebviewAppEvent,
            this._featureFlagManager,
            this._config,
            this.app,
            this.workmanagerKit
        );
    }
}

function asyncWrapper(msg: WebViewMessage): AsyncWebViewMessage<WebViewMessage> {
    return {
        type: WebViewMessageType.asyncWrapper,
        requestId: "mock-request-id",
        error: null,
        baseMsg: msg,
    };
}
