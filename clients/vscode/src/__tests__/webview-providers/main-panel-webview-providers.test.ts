import { <PERSON><PERSON>, WebviewView as vscodeWebviewView } from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { CancellationToken, Webview, WebviewView } from "../../__mocks__/vscode-mocks";
import { FeatureFlagManager } from "../../feature-flags";
import { MainPanelAppController } from "../../main-panel/main-panel-app-controller";
import { MainPanelWebview } from "../../main-panel/main-panel-webview";
import { MainPanelWebviewProvider } from "../../webview-providers/main-panel-webview-provider";
import { MainPanelApp } from "../../webview-providers/webview-messages";

describe("MainPanelWebviewProvider", () => {
    test("should register and dispose webview", async () => {
        mockFSUtils.writeFileUtf8(
            "/path/common-webviews/main-panel.html",
            "<html><head></head>example of webviews/dist/main-panel.html</html>",
            true
        );

        const provider = new TestMainPanelWebviewProvider();
        const webviewView = new WebviewView("main-panel", new Webview({}, "", ""), true);

        // Start resolveWebviewView in the background and set feature flag manager after a short delay
        const resolvePromise = provider.resolveWebviewView(
            webviewView,
            {
                state: undefined,
            },
            new CancellationToken()
        );

        // Set up feature flag manager after a short delay to allow webviewReadyResolve to be set
        setTimeout(() => {
            const featureFlagManager = new FeatureFlagManager();
            provider.setFeatureFlagManager(featureFlagManager);
        }, 10);

        await resolvePromise;

        expect(provider.webviewView).toBe(webviewView);
        expect(provider.webviewView?.title).toBe("");
        expect(provider.mainPanelWebview).not.toBeUndefined();

        provider.changeApp(new TestApp());
        expect(provider.webviewView?.title).toBe("Test App");

        provider.changeApp(undefined);
        expect(provider.webviewView?.title).toBe("");

        webviewView.dispose();
        expect(provider.webviewView).toBeUndefined();
    });

    test("should emit visibility events", async () => {
        mockFSUtils.writeFileUtf8(
            "/path/common-webviews/main-panel.html",
            "<html><head></head>example of webviews/dist/main-panel.html</html>",
            true
        );

        const provider = new TestMainPanelWebviewProvider();
        const webviewView = new WebviewView("main-panel", new Webview({}, "", ""), true);

        // Start resolveWebviewView in the background and set feature flag manager after a short delay
        const resolvePromise = provider.resolveWebviewView(
            webviewView,
            {
                state: undefined,
            },
            new CancellationToken()
        );

        // Set up feature flag manager after a short delay to allow webviewReadyResolve to be set
        setTimeout(() => {
            const featureFlagManager = new FeatureFlagManager();
            provider.setFeatureFlagManager(featureFlagManager);
        }, 10);

        await resolvePromise;

        const visibilitySpy = jest.fn();
        provider.onVisibilityChange(visibilitySpy);

        expect(visibilitySpy).not.toHaveBeenCalled();
        webviewView.triggerVisibilityChange();

        expect(visibilitySpy).toHaveBeenCalledTimes(1);
    });

    test("should resolve webview when timeout is reached without feature flag manager", async () => {
        // Use fake timers only for this test
        jest.useFakeTimers();

        try {
            mockFSUtils.writeFileUtf8(
                "/path/common-webviews/main-panel.html",
                "<html><head></head>example of webviews/dist/main-panel.html</html>",
                true
            );

            const provider = new TestMainPanelWebviewProvider();
            const webviewView = new WebviewView("main-panel", new Webview({}, "", ""), true);

            // Start resolveWebviewView in the background without setting feature flag manager
            const resolvePromise = provider.resolveWebviewView(
                webviewView,
                {
                    state: undefined,
                },
                new CancellationToken()
            );

            // Don't set feature flag manager - let it timeout
            // Advance fake timers to trigger the feature flag timeout in the provider
            await jest.advanceTimersByTimeAsync(MainPanelWebviewProvider.FEATURE_FLAG_TIMEOUT_MS);

            await resolvePromise;

            // Verify that the webview was resolved successfully via timeout
            expect(provider.webviewView).toBe(webviewView);
            expect(provider.webviewView?.title).toBe("");
            expect(provider.mainPanelWebview).not.toBeUndefined();
        } finally {
            jest.useRealTimers();
        }
    });

    test("should resolve webview immediately when feature flag manager is set before timeout", async () => {
        // Use fake timers only for this test
        jest.useFakeTimers();

        try {
            mockFSUtils.writeFileUtf8(
                "/path/common-webviews/main-panel.html",
                "<html><head></head>example of webviews/dist/main-panel.html</html>",
                true
            );

            const provider = new TestMainPanelWebviewProvider();
            const webviewView = new WebviewView("main-panel", new Webview({}, "", ""), true);

            // Start resolveWebviewView in the background
            const resolvePromise = provider.resolveWebviewView(
                webviewView,
                {
                    state: undefined,
                },
                new CancellationToken()
            );

            // Set feature flag manager after 500ms (before the 1000ms timeout)
            jest.advanceTimersByTime(500);
            const featureFlagManager = new FeatureFlagManager();
            provider.setFeatureFlagManager(featureFlagManager);

            await resolvePromise;

            // Verify that the webview was resolved successfully via feature flag manager
            expect(provider.webviewView).toBe(webviewView);
            expect(provider.webviewView?.title).toBe("");
            expect(provider.mainPanelWebview).not.toBeUndefined();
        } finally {
            jest.useRealTimers();
        }
    });
});

class TestMainPanelWebviewProvider extends MainPanelWebviewProvider {
    constructor() {
        super(Uri.parse("vscode://example/path"));
    }

    get mainPanelWebview(): MainPanelWebview | undefined {
        return this._mainPanelWebview;
    }

    get webviewView(): vscodeWebviewView | undefined {
        return this._webviewView;
    }
}

class TestApp implements MainPanelAppController {
    dispose(): void {}

    appType(): MainPanelApp {
        return MainPanelApp.chat;
    }

    title(): string {
        return "Test App";
    }

    register(_webview: Webview): void {}
}
