import { GlobalContextKey } from "../../utils/context";
import { WebViewMessageType } from "../../webview-providers/webview-messages";
import type { EmptyMessage } from "../../webview-providers/webview-messages";

describe("RemoteAgentsMessenger SSH Permission Logic Tests", () => {
    let mockGlobalState: any;
    let mockRemoteAgentSshManager: any;

    beforeEach(() => {
        jest.clearAllMocks();

        mockGlobalState = {
            get: jest.fn(),
            update: jest.fn(),
        };

        mockRemoteAgentSshManager = {
            willWriteToSSHDefaultConfig: jest.fn(),
        };
    });

    // Helper function to simulate the permission prompt logic
    async function shouldShowSSHConfigPermissionPrompt() {
        const [willWrite, hasPermission] = await Promise.all([
            mockRemoteAgentSshManager.willWriteToSSHDefaultConfig(),
            mockGlobalState.get(GlobalContextKey.hasPermissionToWriteToSSHConfig),
        ]);
        return {
            type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse,
            data: willWrite && hasPermission !== true,
        };
    }

    // Helper function to simulate setting permission
    async function setPermissionToWriteToSSHConfig(hasPermission: boolean): Promise<EmptyMessage> {
        try {
            await mockGlobalState.update(
                GlobalContextKey.hasPermissionToWriteToSSHConfig,
                hasPermission
            );
            return {
                type: WebViewMessageType.empty,
            };
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to set SSH config permission:", error);
            return {
                type: WebViewMessageType.empty,
            };
        }
    }

    describe("SSH Permission Prompt Logic", () => {
        it("should return true when SSH will write and permission is not granted", async () => {
            // Setup mocks
            mockRemoteAgentSshManager.willWriteToSSHDefaultConfig = jest
                .fn()
                .mockResolvedValue(true);
            mockGlobalState.get = jest.fn().mockResolvedValue(undefined); // No permission set

            const response = await shouldShowSSHConfigPermissionPrompt();

            expect(response.type).toBe(
                WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse
            );
            expect(response.data).toBe(true);
            expect(mockRemoteAgentSshManager.willWriteToSSHDefaultConfig).toHaveBeenCalledTimes(1);
            expect(mockGlobalState.get).toHaveBeenCalledWith(
                GlobalContextKey.hasPermissionToWriteToSSHConfig
            );
        });

        it("should return true when SSH will write and permission is explicitly false", async () => {
            // Setup mocks
            mockRemoteAgentSshManager.willWriteToSSHDefaultConfig = jest
                .fn()
                .mockResolvedValue(true);
            mockGlobalState.get = jest.fn().mockResolvedValue(false); // Permission explicitly denied

            const response = await shouldShowSSHConfigPermissionPrompt();

            expect(response.type).toBe(
                WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse
            );
            expect(response.data).toBe(true);
        });

        it("should return false when SSH will write but permission is already granted", async () => {
            // Setup mocks
            mockRemoteAgentSshManager.willWriteToSSHDefaultConfig = jest
                .fn()
                .mockResolvedValue(true);
            mockGlobalState.get = jest.fn().mockResolvedValue(true); // Permission granted

            const response = await shouldShowSSHConfigPermissionPrompt();

            expect(response.type).toBe(
                WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse
            );
            expect(response.data).toBe(false);
        });

        it("should return false when SSH will not write to config", async () => {
            // Setup mocks
            mockRemoteAgentSshManager.willWriteToSSHDefaultConfig = jest
                .fn()
                .mockResolvedValue(false);
            mockGlobalState.get = jest.fn().mockResolvedValue(undefined);

            const response = await shouldShowSSHConfigPermissionPrompt();

            expect(response.type).toBe(
                WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse
            );
            expect(response.data).toBe(false);
            // Should still check permission state for consistency
            expect(mockGlobalState.get).toHaveBeenCalledWith(
                GlobalContextKey.hasPermissionToWriteToSSHConfig
            );
        });
    });

    describe("SSH Permission Setting Logic", () => {
        it("should successfully set permission to true", async () => {
            // Setup mocks
            mockGlobalState.update = jest.fn().mockResolvedValue(undefined);

            const response = await setPermissionToWriteToSSHConfig(true);

            expect(response.type).toBe(WebViewMessageType.empty);
            expect(mockGlobalState.update).toHaveBeenCalledWith(
                GlobalContextKey.hasPermissionToWriteToSSHConfig,
                true
            );
        });

        it("should successfully set permission to false", async () => {
            // Setup mocks
            mockGlobalState.update = jest.fn().mockResolvedValue(undefined);

            const response = await setPermissionToWriteToSSHConfig(false);

            expect(response.type).toBe(WebViewMessageType.empty);
            expect(mockGlobalState.update).toHaveBeenCalledWith(
                GlobalContextKey.hasPermissionToWriteToSSHConfig,
                false
            );
        });

        it("should handle errors gracefully and still return empty message", async () => {
            // Setup mocks to throw error
            const error = new Error("Failed to update global state");
            mockGlobalState.update = jest.fn().mockRejectedValue(error);

            // Spy on console.error
            const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

            const response = await setPermissionToWriteToSSHConfig(true);

            expect(response.type).toBe(WebViewMessageType.empty);
            expect(mockGlobalState.update).toHaveBeenCalledWith(
                GlobalContextKey.hasPermissionToWriteToSSHConfig,
                true
            );
            expect(consoleSpy).toHaveBeenCalledWith("Failed to set SSH config permission:", error);

            consoleSpy.mockRestore();
        });
    });

    describe("SSH Permission Integration Flow", () => {
        it("should complete full permission flow from prompt check to setting permission", async () => {
            // Step 1: Check if prompt should be shown (initially no permission)
            mockRemoteAgentSshManager.willWriteToSSHDefaultConfig = jest
                .fn()
                .mockResolvedValue(true);
            mockGlobalState.get = jest.fn().mockResolvedValue(undefined);

            const promptResponse = await shouldShowSSHConfigPermissionPrompt();
            expect(promptResponse.data).toBe(true);

            // Step 2: User grants permission
            mockGlobalState.update = jest.fn().mockResolvedValue(undefined);

            const setResponse = await setPermissionToWriteToSSHConfig(true);
            expect(setResponse.type).toBe(WebViewMessageType.empty);

            // Step 3: Check prompt again (should not show now)
            mockGlobalState.get = jest.fn().mockResolvedValue(true); // Permission now granted

            const secondPromptResponse = await shouldShowSSHConfigPermissionPrompt();
            expect(secondPromptResponse.data).toBe(false);

            // Verify the sequence of calls
            expect(mockGlobalState.update).toHaveBeenCalledWith(
                GlobalContextKey.hasPermissionToWriteToSSHConfig,
                true
            );
        });
    });
});
