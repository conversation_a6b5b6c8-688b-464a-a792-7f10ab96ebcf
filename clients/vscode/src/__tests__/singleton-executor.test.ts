import { disposableDelayer } from "../utils/promise-utils";
import { SingletonExecutor } from "../utils/singleton-executor";
import { advanceTimeUntilTrue } from "./__utils__/time";

describe("singleton-executor", () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    // Verify that non-overlapping calls to kick correctly execute the inner function (non-awaiting
    // version).
    test("nonoverlapping calls, no wait", async () => {
        let count = 0;
        const executor = new SingletonExecutor(async () => {
            count++;
        });

        executor.kick();
        await advanceTimeUntilTrue(() => count === 1);

        executor.kick();
        await advanceTimeUntilTrue(() => count === 2);
    });

    // Verify that non-overlapping calls to kick correctly execute the inner function (awaiting
    // version).
    test("nonoverlapping calls with wait", async () => {
        let count = 0;
        const executor = new SingletonExecutor(async () => {
            count++;
        });

        await executor.kick();
        expect(count).toBe(1);

        await executor.kick();
        expect(count).toBe(2);
    });

    // Verify that multiple kicks that arrive while an execution is in progress are coalesced into
    // a single execution (non-awaiting version).
    test("overlapping calls, no wait", async () => {
        let count = 0;
        let waiting = false;
        const [promise, resume] = disposableDelayer();
        const executor = new SingletonExecutor(async () => {
            waiting = true;
            await promise;
            count++;
        });

        // Start an execution but make it wait.
        executor.kick();
        await advanceTimeUntilTrue(() => waiting);

        // Make multiple requests while the current execution is in progress.
        executor.kick();
        executor.kick();
        executor.kick();
        executor.kick();
        executor.kick();

        // Allow the execution to complete. There should be a total of two executions: the original
        // one and just one from the multiple requests.
        resume.dispose();
        await advanceTimeUntilTrue(() => count === 2);

        // Wait a "long time" to (try to) make sure the function is not executed again.
        let x = 0;
        await advanceTimeUntilTrue(() => x++ >= 100);
        expect(count).toEqual(2);
    });

    // Verify that multiple kicks that arrive while an execution is in progress are coalesced into
    // a single execution (awaiting version).
    test("overlapping calls with wait", async () => {
        let count = 0;
        let waiting = false;
        const [promise, resume] = disposableDelayer();
        const executor = new SingletonExecutor(async () => {
            waiting = true;
            await promise;
            count++;
        });

        // Start an execution but make it wait.
        const initialPromise = executor.kick();
        await advanceTimeUntilTrue(() => waiting);

        // Make multiple requests while the current execution is in progress.
        const laterPromises = Promise.allSettled([
            executor.kick(),
            executor.kick(),
            executor.kick(),
            executor.kick(),
            executor.kick(),
        ]);

        // Allow the first execution to complete. The result could be either 1 or 2, depending on
        // whether the later requests have begun or not.
        resume.dispose();
        await initialPromise;
        expect(count).toBeGreaterThanOrEqual(1);
        expect(count).toBeLessThanOrEqual(2);

        // Wait for the later requests. They should all have been coalesced into a single execution.
        await laterPromises;
        expect(count).toEqual(2);

        // Wait a "long time" to (try to) make sure the function is not executed again.
        let x = 0;
        await advanceTimeUntilTrue(() => x++ >= 100);
        expect(count).toEqual(2);
    });

    // Verify that kicks that attempt execution after the executor has been disposed are rejected.
    test("requests after dispose", async () => {
        let count = 0;
        let waiting = false;
        const [delay, delayDisp] = disposableDelayer();
        const executor = new SingletonExecutor(async () => {
            waiting = true;
            await delay;
            count++;
        });

        // Kick off an execution and wait for it to start.
        executor.kick();
        await advanceTimeUntilTrue(() => waiting);
        expect(waiting).toBe(true);

        // Kick off more executions, some before we dispose the executor and some after.
        const promises = new Array<Promise<void>>();
        promises.push(executor.kick());
        promises.push(executor.kick());
        executor.dispose();
        promises.push(executor.kick());
        promises.push(executor.kick());

        try {
            // Allow the first execution to complete and then await the later ones.
            delayDisp.dispose();
            await Promise.allSettled(promises);
        } catch {}

        // None of the later requests should have been executed, whether they were created before
        // or after the dispose.
        expect(count).toEqual(1);

        // Wait a "long time" to (try to) make sure none of the later requests actually execute.
        let x = 0;
        await advanceTimeUntilTrue(() => x++ >= 100);
        expect(count).toEqual(1);
    });

    // Verify that multiple levels of nested kicks each call the inner function once.
    test("nested kicks", async () => {
        const nestingDepth = 8;
        const [firstExecDone, firstExecDoneDisp] = disposableDelayer();
        let count = 0;
        const promises = new Array<Promise<void>>();
        const executor = new SingletonExecutor(async () => {
            if (count > 0) {
                await firstExecDone;
            }
            count++;
            if (count < nestingDepth) {
                promises.push(executor.kick());
            }
        });

        await executor.kick();
        expect(count).toEqual(1);
        firstExecDoneDisp.dispose();

        await advanceTimeUntilTrue(() => count === nestingDepth);
        expect(promises.length).toEqual(nestingDepth - 1);
        await Promise.allSettled(promises);
        expect(count).toEqual(nestingDepth);
    });
});
