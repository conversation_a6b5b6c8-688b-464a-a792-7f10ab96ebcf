/**
 * Unit tests for mocks.
 */
import * as vscode from "vscode";

import { MutableTextDocument, TextDocument } from "../__mocks__/vscode-mocks";

describe("mock", () => {
    test("TextDocument positionAt", () => {
        const doc = new TextDocument(vscode.Uri.file("test.py"), "ab");
        expect(doc.positionAt(0)).toEqual(new vscode.Position(0, 0));
        expect(doc.positionAt(1)).toEqual(new vscode.Position(0, 1));
        expect(doc.positionAt(2)).toEqual(new vscode.Position(0, 2));
    });

    test("TextDocument offsetAt", () => {
        const doc = new TextDocument(vscode.Uri.file("test.py"), "ab");
        expect(doc.offsetAt(new vscode.Position(0, 0))).toEqual(0);
        expect(doc.offsetAt(new vscode.Position(0, 1))).toEqual(1);
        expect(doc.offsetAt(new vscode.Position(0, 2))).toEqual(2);
    });

    test("MutableTextDocument delete", () => {
        const doc = new MutableTextDocument(vscode.Uri.file("test.py"), "abc");
        let changeEvent = doc.delete(0, 1);
        expect(changeEvent.contentChanges.length).toEqual(1);
        expect(changeEvent.contentChanges[0].range).toEqual(new vscode.Range(0, 0, 0, 1));
        changeEvent = doc.delete(0, 2);
        expect(changeEvent.contentChanges.length).toEqual(1);
        expect(changeEvent.contentChanges[0].range).toEqual(new vscode.Range(0, 0, 0, 2));
    });
});
