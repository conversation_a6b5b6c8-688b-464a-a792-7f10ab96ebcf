import * as vscode from "vscode";

import { MockAPIServer } from "../__mocks__/mock-api-server";
import { type AugmentConfigListener } from "../augment-config-listener";
import { type FeatureFlagManager } from "../feature-flags";
import {
    type Notification,
    NotificationDisplayType,
    NotificationLevel,
    NotificationWatcher,
} from "../notification-watcher";

// Mock vscode
jest.mock("vscode");

// Mock WebviewManager
/* eslint-disable @typescript-eslint/naming-convention */
jest.mock("../webview-providers/webview-manager", () => ({
    WebviewManager: {
        getInstance: jest.fn(() => ({
            broadcastMessage: jest.fn(),
        })),
    },
}));

describe("NotificationWatcher", () => {
    let mockApiServer: MockAPIServer;
    let mockConfigListener: AugmentConfigListener;
    let mockFeatureFlagManager: FeatureFlagManager;
    let notificationWatcher: NotificationWatcher;

    beforeEach(() => {
        mockApiServer = new MockAPIServer();

        // Mock config listener
        mockConfigListener = {
            config: {} as any,
            dispose: jest.fn(),
        } as any;

        // Mock feature flag manager
        mockFeatureFlagManager = {
            currentFlags: {
                notificationPollingIntervalMs: 5000,
            },
            subscribe: jest.fn().mockReturnValue({ dispose: jest.fn() }),
        } as any;

        // Mock vscode window methods
        (vscode.window.showInformationMessage as jest.Mock) = jest
            .fn()
            .mockResolvedValue(undefined);
        (vscode.window.showWarningMessage as jest.Mock) = jest.fn().mockResolvedValue(undefined);
        (vscode.window.showErrorMessage as jest.Mock) = jest.fn().mockResolvedValue(undefined);
    });

    afterEach(() => {
        if (notificationWatcher) {
            notificationWatcher.dispose();
        }
        jest.clearAllMocks();
    });

    it("should show info notification for INFO level", async () => {
        // Mock API response with INFO level notification
        const mockNotification: Notification = {
            notificationId: "test-info",
            level: NotificationLevel.info,
            message: "This is an info notification",
            actionItems: [{ title: "Close" }],
            displayType: NotificationDisplayType.toast,
        };

        jest.spyOn(mockApiServer, "readNotifications").mockResolvedValue({
            notifications: [mockNotification],
        });

        notificationWatcher = new NotificationWatcher(
            mockApiServer,
            mockConfigListener,
            mockFeatureFlagManager
        );

        // Trigger polling manually
        await notificationWatcher.refreshNotifications();

        // Verify info message was called
        expect(vscode.window.showInformationMessage).toHaveBeenCalledWith(
            "This is an info notification",
            { title: "Close" }
        );
        expect(vscode.window.showWarningMessage).not.toHaveBeenCalled();
        expect(vscode.window.showErrorMessage).not.toHaveBeenCalled();
    });

    it("should show warning notification for WARNING level", async () => {
        // Mock API response with WARNING level notification
        const mockNotification: Notification = {
            notificationId: "test-warning",
            level: NotificationLevel.warning,
            message: "This is a warning notification",
            actionItems: [{ title: "Close" }],
            displayType: NotificationDisplayType.toast,
        };

        jest.spyOn(mockApiServer, "readNotifications").mockResolvedValue({
            notifications: [mockNotification],
        });

        notificationWatcher = new NotificationWatcher(
            mockApiServer,
            mockConfigListener,
            mockFeatureFlagManager
        );

        // Trigger polling manually
        await notificationWatcher.refreshNotifications();

        // Verify warning message was called
        expect(vscode.window.showWarningMessage).toHaveBeenCalledWith(
            "This is a warning notification",
            { title: "Close" }
        );
        expect(vscode.window.showInformationMessage).not.toHaveBeenCalled();
        expect(vscode.window.showErrorMessage).not.toHaveBeenCalled();
    });

    it("should show error notification for ERROR level", async () => {
        // Mock API response with ERROR level notification
        const mockNotification: Notification = {
            notificationId: "test-error",
            level: NotificationLevel.error,
            message: "This is an error notification",
            actionItems: [{ title: "Close" }],
            displayType: NotificationDisplayType.toast,
        };

        jest.spyOn(mockApiServer, "readNotifications").mockResolvedValue({
            notifications: [mockNotification],
        });

        notificationWatcher = new NotificationWatcher(
            mockApiServer,
            mockConfigListener,
            mockFeatureFlagManager
        );

        // Trigger polling manually
        await notificationWatcher.refreshNotifications();

        // Verify error message was called
        expect(vscode.window.showErrorMessage).toHaveBeenCalledWith(
            "This is an error notification",
            { title: "Close" }
        );
        expect(vscode.window.showInformationMessage).not.toHaveBeenCalled();
        expect(vscode.window.showWarningMessage).not.toHaveBeenCalled();
    });

    it("should not show the same notification twice", async () => {
        const mockNotification: Notification = {
            notificationId: "test-duplicate",
            level: NotificationLevel.info,
            message: "This notification should only show once",
            actionItems: [{ title: "Close" }],
            displayType: NotificationDisplayType.toast,
        };

        jest.spyOn(mockApiServer, "readNotifications").mockResolvedValue({
            notifications: [mockNotification],
        });

        notificationWatcher = new NotificationWatcher(
            mockApiServer,
            mockConfigListener,
            mockFeatureFlagManager
        );

        // Trigger polling twice
        await notificationWatcher.refreshNotifications();
        await notificationWatcher.refreshNotifications();

        // Verify info message was called only once
        expect(vscode.window.showInformationMessage).toHaveBeenCalledTimes(1);
    });

    it("should track active notification count", async () => {
        const mockNotifications: Notification[] = [
            {
                notificationId: "test-1",
                level: NotificationLevel.info,
                message: "Notification 1",
                actionItems: [{ title: "Close" }],
            },
            {
                notificationId: "test-2",
                level: NotificationLevel.warning,
                message: "Notification 2",
                actionItems: [{ title: "Close" }],
            },
        ];

        jest.spyOn(mockApiServer, "readNotifications").mockResolvedValue({
            notifications: mockNotifications,
        });

        notificationWatcher = new NotificationWatcher(
            mockApiServer,
            mockConfigListener,
            mockFeatureFlagManager
        );

        // Initially should have 0 notifications
        expect(notificationWatcher.getActiveNotificationCount()).toBe(0);

        // After polling should have 2 notifications
        await notificationWatcher.refreshNotifications();
        expect(notificationWatcher.getActiveNotificationCount()).toBe(2);
    });

    it("should not mark notification as read when dismissed", async () => {
        const mockNotification: Notification = {
            notificationId: "test-dismiss",
            level: NotificationLevel.info,
            message: "This notification will be dismissed",
            actionItems: [{ title: "Close" }],
            displayType: NotificationDisplayType.toast,
        };

        jest.spyOn(mockApiServer, "readNotifications").mockResolvedValue({
            notifications: [mockNotification],
        });

        // Mock showInformationMessage to resolve with undefined (dismissed)
        (vscode.window.showInformationMessage as jest.Mock).mockResolvedValue(undefined);

        notificationWatcher = new NotificationWatcher(
            mockApiServer,
            mockConfigListener,
            mockFeatureFlagManager
        );

        // Trigger polling
        await notificationWatcher.refreshNotifications();

        // Wait for the promise chain to complete
        await new Promise((resolve) => setTimeout(resolve, 0));

        // Verify notification was not removed from active notifications
        expect(notificationWatcher.getActiveNotificationCount()).toBe(1);
    });

    it("should mark notification as read when action is clicked", async () => {
        const mockNotification: Notification = {
            notificationId: "test-action",
            level: NotificationLevel.info,
            message: "This notification will have action clicked",
            actionItems: [{ title: "View Details", url: "https://example.com" }],
            displayType: NotificationDisplayType.toast,
        };

        jest.spyOn(mockApiServer, "readNotifications").mockResolvedValue({
            notifications: [mockNotification],
        });

        const markAsReadSpy = jest
            .spyOn(mockApiServer, "markNotificationAsRead")
            .mockResolvedValue();

        // Mock showInformationMessage to resolve with the action item
        (vscode.window.showInformationMessage as jest.Mock).mockResolvedValue({
            title: "View Details",
        });

        notificationWatcher = new NotificationWatcher(
            mockApiServer,
            mockConfigListener,
            mockFeatureFlagManager
        );

        // Trigger polling
        await notificationWatcher.refreshNotifications();

        // Wait for the promise chain to complete
        await new Promise((resolve) => setTimeout(resolve, 0));

        // Verify markNotificationAsRead was called with the action title
        expect(markAsReadSpy).toHaveBeenCalledWith("test-action", "View Details");

        // Verify notification was removed from active notifications
        expect(notificationWatcher.getActiveNotificationCount()).toBe(0);
    });
});
