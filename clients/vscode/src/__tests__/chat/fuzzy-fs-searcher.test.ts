import * as vscode from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { resetMockWorkspace } from "../../__mocks__/vscode-mocks";
import { _exportedForTesting, FuzzyFsSearcher } from "../../chat/fuzzy-fs-searcher";
import {
    AugmentGlobalState,
    FileStorageChangeListener,
    FileStorageOrCacheKey,
    IAugmentGlobalState,
    IGlobalContextSaveLoadOpts,
    InMemoryContextKey,
    WriteBackCacheKey,
} from "../../utils/context";
import {
    type FindFileRequest,
    FindFolderRequest,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";
import { type WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitStartup,
    createWorkspaceFiles,
    MockSourceFolder,
    MockSourceFolderType,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";

class MockAugmentGlobalState implements IAugmentGlobalState {
    private _values: Map<FileStorageOrCacheKey | InMemoryContextKey, any> = new Map();

    update<T>(key: InMemoryContextKey, value: T): Thenable<void> {
        this._values.set(key, value);
        return Promise.resolve();
    }

    get<T>(key: InMemoryContextKey): T | undefined {
        return this._values.get(key);
    }

    save: AugmentGlobalState["save"] = jest.fn(
        <T>(
            key: FileStorageOrCacheKey,
            value: T,
            _opts?: IGlobalContextSaveLoadOpts
        ): Promise<void> => {
            this._values.set(key, value);
            return Promise.resolve();
        }
    );

    load: AugmentGlobalState["load"] = jest.fn(
        <T>(
            key: FileStorageOrCacheKey,
            _opts?: IGlobalContextSaveLoadOpts
        ): Promise<T | undefined> => {
            return Promise.resolve(this._values.get(key));
        }
    ) as AugmentGlobalState["load"];

    onDidChangeFileStorage<T>(
        _key: FileStorageOrCacheKey,
        _listener: FileStorageChangeListener<T>
    ): vscode.Disposable {
        return {
            dispose: () => {},
        };
    }
}

const workspaceFolder1: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/folder1"),
    repoRootUri: vscode.Uri.file("/src/folder1"),
    files: new Map<string, string>([
        ["fizz/file1-foo.py", "this is file 1 of sourceFolder1"],
        ["buzz/file2-foobar.py", "this is file 2"],
        ["fizzbuzz/file3-bar.py", "this is file 3"],
    ]),
    ignoredFiles: new Map<string, string>([[".git/logs/HEAD", "head file"]]),
};

function makeFuzzyFileRequest(query: string, exactMatch: boolean = false): FindFileRequest {
    return {
        type: WebViewMessageType.findFileRequest,
        data: {
            relPath: query,
            exactMatch,
            maxResults: 10,
            rootPath: "",
        },
    };
}

function makeFuzzyFolderRequest(query: string): FindFolderRequest {
    return {
        type: WebViewMessageType.findFolderRequest,
        data: {
            relPath: query,
            exactMatch: false,
            maxResults: 10,
            rootPath: "",
        },
    };
}

describe("FuzzyFsSearcher", () => {
    let kit: WorkspaceManagerTestKit;
    let workspaceManager: WorkspaceManager;
    let fuzzyFsSearcher: FuzzyFsSearcher;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();

        // Initialize the workspace and test kit
        createWorkspaceFiles([workspaceFolder1]);
        kit = new WorkspaceManagerTestKit();

        // Start up the workspace manager
        const workspaceState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();

        await awaitStartup(workspaceManager, workspaceState);
        fuzzyFsSearcher = new FuzzyFsSearcher(
            new MockAugmentGlobalState(),
            workspaceManager,
            jest.fn(() => ({ dispose: () => {} }))
        );

        createWorkspaceFiles([workspaceFolder1]);
    });

    afterEach(() => {
        fuzzyFsSearcher.dispose();
        workspaceManager.dispose();
        kit.dispose();
        jest.useRealTimers();
    });

    describe("findFiles", () => {
        describe("search by filename", () => {
            test("foo", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("foo"))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [
                        { repoRoot: "/src/folder1", pathName: "fizz/file1-foo.py" },
                        { repoRoot: "/src/folder1", pathName: "buzz/file2-foobar.py" },
                    ],
                });
            });

            test("bar", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("bar"))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [
                        { repoRoot: "/src/folder1", pathName: "buzz/file2-foobar.py" },
                        { repoRoot: "/src/folder1", pathName: "fizzbuzz/file3-bar.py" },
                    ],
                });
            });

            test("foobar", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("foobar"))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [{ repoRoot: "/src/folder1", pathName: "buzz/file2-foobar.py" }],
                });
            });

            test("nonexistent file", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("abc123-file"))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [],
                });
            });
        });

        describe("exact suffix matches", () => {
            test("file1-foo.py with exactMatch=true", () => {
                expect(
                    fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("file1-foo.py", true))
                ).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [{ repoRoot: "/src/folder1", pathName: "fizz/file1-foo.py" }],
                });
            });

            test("file2-foobar.py with exactMatch=true", () => {
                expect(
                    fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("file2-foobar.py", true))
                ).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [{ repoRoot: "/src/folder1", pathName: "buzz/file2-foobar.py" }],
                });
            });

            // Note: These tests are commented out because the current implementation doesn't match
            // file extensions alone. It requires the full filename to match.
            // test("foo.py with exactMatch=true", () => {
            //     expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("foo.py", true))).toEqual({
            //         type: WebViewMessageType.findFileResponse,
            //         data: [{ repoRoot: "/src/folder1", pathName: "fizz/file1-foo.py" }],
            //     });
            // });
            //
            // test("bar.py with exactMatch=true", () => {
            //     expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("bar.py", true))).toEqual({
            //         type: WebViewMessageType.findFileResponse,
            //         data: [{ repoRoot: "/src/folder1", pathName: "fizzbuzz/file3-bar.py" }],
            //     });
            // });

            test("fizz/file1-foo.py with exactMatch=true", () => {
                expect(
                    fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("fizz/file1-foo.py", true))
                ).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [{ repoRoot: "/src/folder1", pathName: "fizz/file1-foo.py" }],
                });
            });

            test("nonexistent suffix with exactMatch=true", () => {
                expect(
                    fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("nonexistent.py", true))
                ).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [],
                });
            });

            test("partial path match with exactMatch=true", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("buzz/file", true))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [],
                });
            });
        });

        describe("search by directory", () => {
            test("fizz", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("fizz"))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [
                        { repoRoot: "/src/folder1", pathName: "fizz/file1-foo.py" },
                        { repoRoot: "/src/folder1", pathName: "fizzbuzz/file3-bar.py" },
                    ],
                });
            });

            test("buzz", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("buzz"))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [
                        { repoRoot: "/src/folder1", pathName: "buzz/file2-foobar.py" },
                        { repoRoot: "/src/folder1", pathName: "fizzbuzz/file3-bar.py" },
                    ],
                });
            });

            test("nonexistent directory", () => {
                expect(fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("abc123-dir"))).toEqual({
                    type: WebViewMessageType.findFileResponse,
                    data: [],
                });
            });
        });
    });

    describe("findFolders", () => {
        describe("search by directory", () => {
            test("fizz", () => {
                expect(fuzzyFsSearcher.findFolders(makeFuzzyFolderRequest("fizz"))).toEqual({
                    type: WebViewMessageType.findFolderResponse,
                    data: [
                        { repoRoot: "/src/folder1", pathName: "fizz/" },
                        { repoRoot: "/src/folder1", pathName: "fizzbuzz/" },
                    ],
                });
            });

            test("buzz", () => {
                expect(fuzzyFsSearcher.findFolders(makeFuzzyFolderRequest("buzz"))).toEqual({
                    type: WebViewMessageType.findFolderResponse,
                    data: [
                        { repoRoot: "/src/folder1", pathName: "buzz/" },
                        { repoRoot: "/src/folder1", pathName: "fizzbuzz/" },
                    ],
                });
            });

            test("nonexistent directory", () => {
                expect(fuzzyFsSearcher.findFolders(makeFuzzyFolderRequest("abc123-dir"))).toEqual({
                    type: WebViewMessageType.findFolderResponse,
                    data: [],
                });
            });
        });
    });

    test("saves unique per workspace", () => {
        fuzzyFsSearcher.findFiles(makeFuzzyFileRequest("foo"));
        expect(fuzzyFsSearcher["_globalState"].save).toHaveBeenCalledWith(
            WriteBackCacheKey.fuzzyFsFilesIndex,
            expect.anything(),
            { uniquePerWorkspace: true }
        );

        fuzzyFsSearcher.findFolders(makeFuzzyFolderRequest("foo"));
        expect(fuzzyFsSearcher["_globalState"].save).toHaveBeenCalledWith(
            WriteBackCacheKey.fuzzyFsFoldersIndex,
            expect.anything(),
            { uniquePerWorkspace: true }
        );
    });

    describe("utility functions", () => {
        describe("filterExactSuffixPaths", () => {
            const { filterExactSuffixPaths } = _exportedForTesting;

            test("filters files by exact suffix", () => {
                const files = [
                    { repoRoot: "/root", pathName: "path/to/file.txt" },
                    { repoRoot: "/root", pathName: "other/file.txt" },
                    { repoRoot: "/root", pathName: "different/name.txt" },
                ];

                const result = filterExactSuffixPaths(files, "file.txt");
                expect(result).toEqual([
                    { repoRoot: "/root", pathName: "path/to/file.txt" },
                    { repoRoot: "/root", pathName: "other/file.txt" },
                ]);
            });

            test("returns empty array for no matches", () => {
                const files = [
                    { repoRoot: "/root", pathName: "path/to/file.txt" },
                    { repoRoot: "/root", pathName: "other/file.txt" },
                ];

                const result = filterExactSuffixPaths(files, "nonexistent.txt");
                expect(result).toEqual([]);
            });

            test("handles non-normalized paths", () => {
                const files = [
                    { repoRoot: "/root", pathName: "path/to///file.txt" },
                    { repoRoot: "/root", pathName: "other//file.txt" },
                ];

                const result = filterExactSuffixPaths(files, "to/file.txt");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "path/to///file.txt" }]);
            });

            test("handles emoji in filenames and filepaths", () => {
                const files = [
                    { repoRoot: "/root", pathName: "path/to/file.txt" },
                    { repoRoot: "/root", pathName: "path/🚀/emoji-file.txt" },
                    { repoRoot: "/root", pathName: "other/file-😊.txt" },
                    { repoRoot: "/root", pathName: "emoji/path/🎉-celebration.md" },
                    // Paths with only emojis
                    { repoRoot: "/root", pathName: "🌟/⭐/star-file.txt" },
                    { repoRoot: "/root", pathName: "🔥/🌊/elements.txt" },
                    // Filenames that start with emojis
                    { repoRoot: "/root", pathName: "docs/📝-notes.txt" },
                    { repoRoot: "/root", pathName: "images/🖼️image.png" },
                    // Path with only emojis and filename starting with emoji
                    { repoRoot: "/root", pathName: "🎵/🎹/🎧-music.mp3" },
                ];

                // Test emoji in filepath
                let result = filterExactSuffixPaths(files, "🚀/emoji-file.txt");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "path/🚀/emoji-file.txt" }]);

                // Test emoji in filename
                result = filterExactSuffixPaths(files, "file-😊.txt");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "other/file-😊.txt" }]);

                // Test path with only emojis
                result = filterExactSuffixPaths(files, "🌟/⭐/star-file.txt");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "🌟/⭐/star-file.txt" }]);

                // Test another path with only emojis
                result = filterExactSuffixPaths(files, "🔥/🌊/elements.txt");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "🔥/🌊/elements.txt" }]);

                // Test filename starting with emoji
                result = filterExactSuffixPaths(files, "📝-notes.txt");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "docs/📝-notes.txt" }]);

                // Test filename starting with emoji without hyphen
                result = filterExactSuffixPaths(files, "🖼️image.png");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "images/🖼️image.png" }]);

                // Test path with only emojis and filename starting with emoji
                result = filterExactSuffixPaths(files, "🎵/🎹/🎧-music.mp3");
                expect(result).toEqual([{ repoRoot: "/root", pathName: "🎵/🎹/🎧-music.mp3" }]);
            });
        });
    });
});
