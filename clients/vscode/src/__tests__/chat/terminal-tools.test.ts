import { MockClientFeatureFlags } from "@augment-internal/sidecar-libs/src/__tests__/mocks/mock-client-feature-flags";
import {
    resetLibraryClientFeatureFlags,
    setLibraryClientFeatureFlags,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

// Import the exported test utilities
import { __test__ } from "../../chat/terminal-tools";

// Access the exported functions and classes for testing
const stripControlCodes = __test__.stripControlCodes;
const shellPrompts = __test__.shellPrompts;
// eslint-disable-next-line @typescript-eslint/naming-convention
const TerminalLaunchProcessTool = __test__.TerminalLaunchProcessTool;
const escapePathForShell = __test__.escapePathForShell;
const getClearCommand = __test__.getClearCommand;
const vscodeEventsStrategy = __test__.vscodeEventsStrategy;
const parseScriptOutput = __test__.parseScriptOutput;
const cwdTracker = __test__.cwdTracker;
const processStatus = __test__.processStatus;

describe("TerminalProcessTools", () => {
    let mockFeatureFlags: MockClientFeatureFlags;

    beforeEach(() => {
        mockFeatureFlags = new MockClientFeatureFlags();
        setLibraryClientFeatureFlags(mockFeatureFlags);
    });

    afterEach(() => {
        resetLibraryClientFeatureFlags();
    });

    describe("stripControlCodes", () => {
        it("should remove ANSI color codes", () => {
            const input = "\u001b[31mRed text\u001b[0m";
            const expected = "Red text";
            expect(stripControlCodes(input)).toBe(expected);
        });

        it("should remove cursor movement codes", () => {
            const input = "Text with \u001b[2A\u001b[3B\u001b[4C\u001b[5D cursor movement";
            const expected = "Text with  cursor movement";
            expect(stripControlCodes(input)).toBe(expected);
        });

        it("should remove complex ANSI sequences", () => {
            const input = "\u001b[1;32;40mGreen on black\u001b[0m \u001b[4mUnderlined\u001b[24m";
            const expected = "Green on black Underlined";
            expect(stripControlCodes(input)).toBe(expected);
        });

        it("should handle text without control codes", () => {
            const input = "Plain text without any control codes";
            expect(stripControlCodes(input)).toBe(input);
        });

        it("should handle empty string", () => {
            expect(stripControlCodes("")).toBe("");
        });

        it("should handle OSC sequences by removing escape characters", () => {
            const input = "Text with \u001b]0;Window title\u0007 OSC sequence";
            // Check that the escape character and OSC content are removed
            const result = stripControlCodes(input);
            expect(result).not.toContain("\u001b");
            expect(result).not.toContain("0;Window title");
            expect(result).toBe("Text with  OSC sequence");
        });

        it("should handle repeated commands in terminal output", () => {
            // Simulate terminal output with command repetition
            const input = "\u001b[1m$ command\u001b[0m\noutput line 1\noutput line 2";
            const expected = "$ command\noutput line 1\noutput line 2";
            expect(stripControlCodes(input)).toBe(expected);
        });
    });

    describe("SHELL_PROMPTS", () => {
        describe("PowerShell prompt", () => {
            const powershellPrompt = shellPrompts["powershell"];

            test("matches simple PowerShell prompt", () => {
                expect("PS > ".match(powershellPrompt)?.[0]).toBe("PS > ");
            });

            test("matches PowerShell prompt with path", () => {
                expect("PS C:\\Users\\<USER>\\Users\\user> "
                );
            });

            test("matches PowerShell prompt with just >", () => {
                expect("> ".match(powershellPrompt)?.[0]).toBe("> ");
            });

            test("extracts command correctly", () => {
                expect("PS C:\\Users\\<USER>\\Users\\user> ".match(bashPrompt)).toBeNull();
                expect("user@host:~% ".match(bashPrompt)).toBeNull();
            });
        });

        describe("Zsh prompt", () => {
            const zshPrompt = shellPrompts["zsh"];

            test("matches simple zsh prompt", () => {
                expect("% ".match(zshPrompt)?.[0]).toBe("% ");
            });

            test("matches zsh prompt with username and hostname", () => {
                expect("user@host:~% ".match(zshPrompt)?.[0]).toBe("user@host:~% ");
            });

            test("matches zsh prompt with path", () => {
                expect("user@host:/path/to/dir% ".match(zshPrompt)?.[0]).toBe(
                    "user@host:/path/to/dir% "
                );
            });

            test("extracts command correctly", () => {
                expect("% ls -la".replace(zshPrompt, "")).toBe("ls -la");
                expect("user@host:~% cd /tmp".replace(zshPrompt, "")).toBe("cd /tmp");
            });

            test("doesn't match non-zsh prompts", () => {
                expect("PS C:\\Users\\<USER>\\Users\\user> ".match(fishPrompt)).not.toBeNull(); // Fish and PowerShell prompts are similar
                expect("user@host:~$ ".match(fishPrompt)).toBeNull();
                expect("user@host:~% ".match(fishPrompt)).toBeNull();
            });
        });
    });

    describe("TerminalLaunchProcessTool", () => {
        describe("terminal ID in output", () => {
            let mockProcessTools: any;
            let mockWorkspaceManager: any;
            let tool: any;

            beforeEach(() => {
                // Create a mock process tools and workspace manager
                mockProcessTools = {
                    launch: jest.fn(),
                    readOutput: jest.fn(),
                    kill: jest.fn(),
                    writeInput: jest.fn(),
                    canShowTerminal: jest.fn(),
                    showTerminal: jest.fn(),
                    isInLongRunningTerminal: jest.fn(),
                    getLongRunningTerminalInfo: jest.fn(),
                    listProcesses: jest.fn().mockReturnValue([]),
                    waitForProcess: jest.fn(),
                    waitForProcessWithTracking: jest.fn(),
                    getOptimalTimeout: jest.fn().mockResolvedValue(30), // Default 30 second timeout
                };

                mockWorkspaceManager = {
                    getWorkspaceFolders: jest.fn().mockReturnValue([{ uri: { fsPath: "/test" } }]),
                };

                // Create the tool instance
                tool = new TerminalLaunchProcessTool(mockWorkspaceManager, mockProcessTools);
            });

            it("should include extractable terminal ID in background process output", async () => {
                // Test background process (wait=false)
                const backgroundInput = {
                    command: "echo test",
                    wait: false,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    max_wait_seconds: 10,
                    cwd: "/test",
                };

                // Mock the launch to return a process ID
                const mockPid = 12345;
                mockProcessTools.launch.mockResolvedValueOnce(mockPid);

                const result = await tool.call(
                    backgroundInput,
                    [],
                    new AbortController().signal,
                    "test-tool-use-id"
                );

                // Verify the output contains a terminal ID that can be extracted using the actual Svelte regex
                const terminalIdRegex = /[Tt]erminal ID (\d+)/;
                expect(result.text).toMatch(terminalIdRegex);

                // Extract the terminal ID from the output using the same regex as Svelte code
                const terminalIdMatch = result.text.match(terminalIdRegex);
                expect(terminalIdMatch).not.toBeNull();
                expect(terminalIdMatch![1]).toBe(mockPid.toString()); // Group 1 contains the ID
            });

            it("should include extractable terminal ID in foreground process output", async () => {
                // Test foreground process (wait=true)
                const foregroundInput = {
                    command: "echo test",
                    wait: true,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    max_wait_seconds: 10,
                    cwd: "/test",
                };

                // Mock the launch to return a process ID
                const mockPid = 67890;
                mockProcessTools.launch.mockResolvedValueOnce(mockPid);

                // Mock waitForProcess to return completed process result
                mockProcessTools.waitForProcess.mockResolvedValueOnce({
                    output: "test output from command",
                    returnCode: 0,
                });

                // Mock waitForProcessWithTracking to return completed process result
                mockProcessTools.waitForProcessWithTracking.mockResolvedValueOnce({
                    output: "test output from command",
                    returnCode: 0,
                    status: processStatus.completed,
                });

                const result = await tool.call(
                    foregroundInput,
                    [],
                    new AbortController().signal,
                    "test-tool-use-id"
                );

                // Verify the output contains a terminal ID that can be extracted using the actual Svelte regex
                const terminalIdRegex = /[Tt]erminal ID (\d+)/;
                expect(result.text).toMatch(terminalIdRegex);

                // Extract the terminal ID from the output using the same regex as Svelte code
                const terminalIdMatch = result.text.match(terminalIdRegex);
                expect(terminalIdMatch).not.toBeNull();
                expect(terminalIdMatch![1]).toBe(mockPid.toString()); // Group 1 contains the ID
            });
        });
    });

    describe("Strategy-based completion detection", () => {
        describe("escapePathForShell", () => {
            it("should escape paths for bash/zsh/fish", () => {
                const testPath = "/tmp/test file with spaces.txt";
                expect(escapePathForShell(testPath, "bash")).toBe(
                    "'/tmp/test file with spaces.txt'"
                );
                expect(escapePathForShell(testPath, "zsh")).toBe(
                    "'/tmp/test file with spaces.txt'"
                );
                expect(escapePathForShell(testPath, "fish")).toBe(
                    "'/tmp/test file with spaces.txt'"
                );
            });

            it("should escape paths for PowerShell", () => {
                const testPath = "C:\\temp\\test file.txt";
                expect(escapePathForShell(testPath, "powershell")).toBe(
                    "'C:\\temp\\test file.txt'"
                );
            });

            it("should handle single quotes in paths", () => {
                const testPath = "/tmp/test's file.txt";
                expect(escapePathForShell(testPath, "bash")).toBe("'/tmp/test'\"'\"'s file.txt'");

                const testPathWin = "C:\\temp\\test's file.txt";
                expect(escapePathForShell(testPathWin, "powershell")).toBe(
                    "'C:\\temp\\test''s file.txt'"
                );
            });
        });

        describe("getClearCommand", () => {
            it("should return correct clear command for each shell", () => {
                expect(getClearCommand("bash")).toBe("clear");
                expect(getClearCommand("zsh")).toBe("clear");
                expect(getClearCommand("fish")).toBe("clear");
                expect(getClearCommand("powershell")).toBe("Clear-Host");
                expect(getClearCommand("unknown")).toBe("clear"); // default fallback
            });
        });

        describe("VSCodeEventsStrategy", () => {
            let strategy: InstanceType<typeof vscodeEventsStrategy>;
            let mockTerminal: any;

            beforeEach(() => {
                strategy = new vscodeEventsStrategy(undefined); // Pass undefined for TerminalProcessTools
                mockTerminal = { id: "test-terminal" };
            });

            it("should not modify commands", async () => {
                await strategy.setupTerminal(mockTerminal, "bash", "");
                const wrappedCommand = strategy.wrapCommand("ls -la", 123, mockTerminal, true);

                expect(wrappedCommand).toBe("ls -la");
            });

            it("should return false for checkCompleted (relies on VSCode events)", async () => {
                await strategy.setupTerminal(mockTerminal, "bash", "");

                const result = strategy.checkCompleted(123, mockTerminal);
                expect(result.isCompleted).toBe(false);
            });
        });
    });

    describe("parseScriptOutput", () => {
        describe("Basic functionality", () => {
            it("should return a string", () => {
                const result = parseScriptOutput("test", "test", false);
                expect(typeof result).toBe("string");
            });

            it("should handle basic command removal", () => {
                const rawContent = "echo hello\nhello\nworld";
                const result = parseScriptOutput(rawContent, "echo hello", false);

                // Should remove the command and return the output
                expect(result).toContain("hello");
                expect(result).toContain("world");
            });

            it("should handle empty input", () => {
                const result = parseScriptOutput("", "test", false);
                expect(result).toBe("");
            });

            it("should handle completion flag", () => {
                const rawContent = "echo hello\nhello\nprompt$ ";
                const resultNotCompleted = parseScriptOutput(rawContent, "echo hello", false);
                const resultCompleted = parseScriptOutput(rawContent, "echo hello", true);

                // When completed, should remove last line (prompt)
                expect(resultCompleted.length).toBeLessThan(resultNotCompleted.length);
            });
        });
    });

    describe("CwdTracker", () => {
        describe("generateCwdTrackingSetup", () => {
            it("should generate zsh-specific setup commands", () => {
                const result = cwdTracker.generateCwdTrackingSetup("zsh", "/tmp/cwd-file");

                expect(result).toContain("chpwd() {");
                expect(result).toContain("echo \"$(pwd)\" > '/tmp/cwd-file'");
            });

            it("should generate bash-specific setup commands", () => {
                const result = cwdTracker.generateCwdTrackingSetup("bash", "/tmp/cwd-file");

                expect(result).toContain("PROMPT_COMMAND=");
                expect(result).toContain("echo \\\"\\$(pwd)\\\" > '/tmp/cwd-file'");
                expect(result).toContain("echo \"$(pwd)\" > '/tmp/cwd-file'");
            });

            it("should generate fish-specific setup commands", () => {
                const result = cwdTracker.generateCwdTrackingSetup("fish", "/tmp/cwd-file");

                expect(result).toContain("function augment_track_cwd --on-variable PWD");
                expect(result).toContain("echo $PWD > '/tmp/cwd-file'");
                expect(result).toContain("echo (pwd) > '/tmp/cwd-file'");
            });

            it("should return empty string for unknown shells", () => {
                const result = cwdTracker.generateCwdTrackingSetup(
                    "unknown-shell",
                    "/tmp/cwd-file"
                );

                expect(result).toBe("");
            });

            it("should properly escape paths with special characters", () => {
                const pathWithSpaces = "/tmp/path with spaces";
                const result = cwdTracker.generateCwdTrackingSetup("bash", pathWithSpaces);

                // The escapePathForShell function should handle the escaping
                expect(result).toContain("path with spaces");
                // Should contain properly escaped path (wrapped in single quotes)
                expect(result).toContain("'/tmp/path with spaces'");
            });

            it("should handle paths with quotes", () => {
                const pathWithQuotes = '/tmp/path"with"quotes';
                const result = cwdTracker.generateCwdTrackingSetup("zsh", pathWithQuotes);

                // Should contain the path but properly escaped
                expect(result).toContain('path"with"quotes');
            });
        });

        describe("readCurrentCwd", () => {
            it("should return undefined for non-existent file", () => {
                const result = cwdTracker.readCurrentCwd("/non/existent/file/path");
                expect(result).toBeUndefined();
            });

            it("should return undefined for empty file", () => {
                const tempFile = path.join(os.tmpdir(), `cwd-test-${Date.now()}`);

                try {
                    // Create empty file
                    fs.writeFileSync(tempFile, "", "utf8");

                    const result = cwdTracker.readCurrentCwd(tempFile);
                    expect(result).toBeUndefined();
                } finally {
                    // Cleanup
                    if (fs.existsSync(tempFile)) {
                        fs.unlinkSync(tempFile);
                    }
                }
            });

            it("should read cwd from existing file", () => {
                const tempFile = path.join(os.tmpdir(), `cwd-test-${Date.now()}`);
                const testCwd = "/home/<USER>/project";

                try {
                    // Write test cwd to file
                    fs.writeFileSync(tempFile, testCwd, "utf8");

                    const result = cwdTracker.readCurrentCwd(tempFile);
                    expect(result).toBe(testCwd);
                } finally {
                    // Cleanup
                    if (fs.existsSync(tempFile)) {
                        fs.unlinkSync(tempFile);
                    }
                }
            });

            it("should trim whitespace from file content", () => {
                const tempFile = path.join(os.tmpdir(), `cwd-test-${Date.now()}`);
                const testCwd = "/home/<USER>/project";

                try {
                    // Write test cwd with extra whitespace
                    fs.writeFileSync(tempFile, `  ${testCwd}  \n`, "utf8");

                    const result = cwdTracker.readCurrentCwd(tempFile);
                    expect(result).toBe(testCwd);
                } finally {
                    // Cleanup
                    if (fs.existsSync(tempFile)) {
                        fs.unlinkSync(tempFile);
                    }
                }
            });

            it("should handle file read errors gracefully", () => {
                // Try to read a directory instead of a file (should cause an error)
                const result = cwdTracker.readCurrentCwd(os.tmpdir());
                expect(result).toBeUndefined();
            });
        });

        describe("cleanupCwdTracking", () => {
            it("should remove existing file", () => {
                const tempFile = path.join(os.tmpdir(), `cwd-test-${Date.now()}`);

                // Create test file
                fs.writeFileSync(tempFile, "/test/path", "utf8");
                expect(fs.existsSync(tempFile)).toBe(true);

                // Cleanup should remove it
                cwdTracker.cleanupCwdTracking(tempFile);
                expect(fs.existsSync(tempFile)).toBe(false);
            });

            it("should handle non-existent file gracefully", () => {
                const nonExistentFile = "/non/existent/file/path";

                // Should not throw an error
                expect(() => {
                    cwdTracker.cleanupCwdTracking(nonExistentFile);
                }).not.toThrow();
            });

            it("should handle cleanup errors gracefully", () => {
                // Try to cleanup a directory (should cause an error but be handled)
                expect(() => {
                    cwdTracker.cleanupCwdTracking(os.tmpdir());
                }).not.toThrow();
            });
        });
    });
});
