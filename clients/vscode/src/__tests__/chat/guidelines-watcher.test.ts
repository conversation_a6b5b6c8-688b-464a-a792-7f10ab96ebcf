import { resetMockWorkspace } from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { GuidelinesWatcher } from "../../chat/guidelines-watcher";
import { FeatureFlagManager } from "../../feature-flags";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";

describe("GuidelinesWatcher", () => {
    let guidelinesWatcher: GuidelinesWatcher;
    let configListener: AugmentConfigListener;
    let featureFlags: FeatureFlagManager;
    let mockConfig: { chat: { userGuidelines: string } };
    let mockFlags: {
        userGuidelinesLengthLimit: number;
        workspaceGuidelinesLengthLimit: number;
    };
    let configChangeCallback: () => void;

    beforeEach(() => {
        resetMockWorkspace();

        mockConfig = { chat: { userGuidelines: "" } };
        mockFlags = {
            userGuidelinesLengthLimit: 1000,
            workspaceGuidelinesLengthLimit: 1000,
        };

        configListener = {
            config: mockConfig,
            onDidChange: jest.fn().mockImplementation((callback) => {
                configChangeCallback = callback;
                return { dispose: jest.fn() };
            }),
        } as unknown as AugmentConfigListener;

        featureFlags = {
            currentFlags: mockFlags,
            update: jest.fn().mockImplementation((newFlags) => {
                Object.assign(mockFlags, newFlags);
            }),
        } as unknown as FeatureFlagManager;

        const clientMetricsReporter = {
            reportWebviewClientMetric: jest.fn(),
        } as unknown as ClientMetricsReporter;

        guidelinesWatcher = new GuidelinesWatcher(
            configListener,
            featureFlags,
            clientMetricsReporter
        );
    });

    afterEach(() => {
        guidelinesWatcher.dispose();
        jest.useRealTimers();
    });

    test("getUserGuidelinesContent - returns user guidelines", () => {
        const guidelines = "Test user guidelines";
        mockConfig.chat.userGuidelines = guidelines;
        configChangeCallback();
        expect(guidelinesWatcher.getUserGuidelinesContent()).toBe(guidelines);
    });

    test("getWorkspaceGuidelinesContent - returns empty when no content", () => {
        expect(guidelinesWatcher.getWorkspaceGuidelinesContent("/test/path")).toBe("");
    });

    test("getGuidelinesStates - empty state", () => {
        const states = guidelinesWatcher.getGuidelinesStates();
        expect(states.userGuidelines).toEqual({
            overLimit: false,
            contents: "",
            lengthLimit: 1000,
        });
    });

    test("getGuidelinesStates - user guidelines over limit", () => {
        mockFlags.userGuidelinesLengthLimit = 5;
        const guidelineContents = "This is too long";
        mockConfig.chat.userGuidelines = guidelineContents;
        configChangeCallback();

        const states = guidelinesWatcher.getGuidelinesStates();
        expect(states.userGuidelines).toEqual({
            overLimit: true,
            contents: guidelineContents,
            lengthLimit: 5,
        });
    });

    test("onDidChange event fires on config change", async () => {
        const changeHandler = jest.fn();
        const disposable = guidelinesWatcher.onDidChange(changeHandler);

        configChangeCallback();

        expect(changeHandler).toHaveBeenCalled();
        disposable.dispose();
    });
});
