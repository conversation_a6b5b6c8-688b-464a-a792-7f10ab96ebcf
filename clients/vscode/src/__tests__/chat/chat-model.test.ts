import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ChatResult } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { ChatModelTestKit } from "../../__mocks__/chat/chat-model-test-kit";
import { mockFSUtils } from "../../__mocks__/fs-utils";
import { resetMockWorkspace } from "../../__mocks__/vscode-mocks";
import {
    ChatRequest,
    handleNonRootWorkspacePath,
    mergeAndFilterWorkspaceFileChunks,
    processIncomingChatStream,
} from "../../chat/chat-model";
import {
    ChatModelReply,
    FindFileRequest,
    FindFileResponse,
    FindFolderRequest,
    FindFolderResponse,
    WebViewMessageType,
    WorkspaceFileChunk,
} from "../../webview-providers/webview-messages";
import {
    awaitStartup,
    createSourceFolderFiles,
    createWorkspaceFiles,
    MockSourceFolderState,
    MockSourceFolderType,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";

const EMPTY_FIND_FILE_RESPONSE: FindFileResponse = {
    type: WebViewMessageType.findFileResponse,
    data: [],
};
const EMPTY_FIND_FOLDER_RESPONSE: FindFolderResponse = {
    type: WebViewMessageType.findFolderResponse,
    data: [],
};
const MOCK_SOURCE_FOLDER_1: MockSourceFolderState = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/path/to/folder-repo/root"),
    repoRootUri: vscode.Uri.file("/path/to/folder-repo/root"),
    files: new Map<string, string>([
        ["example/path/to/abc123-dir/abc123-file.py", "content of abc123-file"],
    ]),
    ignoredFiles: new Map<string, string>(),
    tracked: true,
};

let wsManagerTestKit: WorkspaceManagerTestKit;
let testKit: ChatModelTestKit;
beforeEach(async () => {
    jest.useFakeTimers();
    resetMockWorkspace();
    mockFSUtils.reset();

    createWorkspaceFiles([MOCK_SOURCE_FOLDER_1]);
    wsManagerTestKit = new WorkspaceManagerTestKit();
    wsManagerTestKit.initWorkspace([MOCK_SOURCE_FOLDER_1]);

    testKit = new ChatModelTestKit(wsManagerTestKit);
    await awaitStartup(testKit.wsManager, [MOCK_SOURCE_FOLDER_1]);
});

afterEach(() => {
    testKit.dispose();
    jest.useRealTimers();
});

describe("ChatModel", () => {
    test("chat", async () => {
        const request: ChatRequest = {
            requestId: "123456789",
            message: "hello world",
            chatHistory: [],
            blobs: {
                checkpointId: undefined,
                addedBlobs: [],
                deletedBlobs: [],
            },
            userGuidedBlobs: [],
            externalSourceIds: [],
            toolDefinitions: [],
        };

        const result = await testKit.chatModel.chat(request);
        expect(result.text).toBe("hello world");
        expect(testKit.recentChats.items.length).toBe(1);
        expect(testKit.recentChats.items[0]).toBe(request);
    });

    describe("file and folder search", () => {
        test("findFiles populates on file change", () => {
            const request: FindFileRequest = {
                type: WebViewMessageType.findFileRequest,
                data: {
                    relPath: "abc",
                    exactMatch: false,
                    maxResults: 10,
                    rootPath: "/path/to/folder-repo/root",
                },
            };
            expect(testKit.fuzzyFsSearcher.findFiles(request)).toEqual(EMPTY_FIND_FILE_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "abc123-dir",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "/path/to/folder-repo/root",
                    },
                })
            ).toEqual(EMPTY_FIND_FILE_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "example/path/to/abc123-dir/abc123-file",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FILE_RESPONSE);

            createSourceFolderFiles(MOCK_SOURCE_FOLDER_1);
            jest.advanceTimersByTime(10000);

            expect(testKit.fuzzyFsSearcher.findFiles(request)).toEqual({
                type: WebViewMessageType.findFileResponse,
                data: [
                    {
                        repoRoot: "/path/to/folder-repo/root",
                        pathName: "example/path/to/abc123-dir/abc123-file.py",
                    },
                ],
            });
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "abc123-dir",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual({
                type: WebViewMessageType.findFileResponse,
                data: [
                    {
                        repoRoot: "/path/to/folder-repo/root",
                        pathName: "example/path/to/abc123-dir/abc123-file.py",
                    },
                ],
            });
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "abc123-file",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual({
                type: WebViewMessageType.findFileResponse,
                data: [
                    {
                        repoRoot: "/path/to/folder-repo/root",
                        pathName: "example/path/to/abc123-dir/abc123-file.py",
                    },
                ],
            });
        });

        test("findFolders populates on event firing", () => {
            const request: FindFolderRequest = {
                type: WebViewMessageType.findFolderRequest,
                data: {
                    relPath: "abc",
                    exactMatch: false,
                    maxResults: 10,
                    rootPath: "CAN_BE_ANYTHING",
                },
            };
            expect(testKit.fuzzyFsSearcher.findFolders(request)).toEqual(
                EMPTY_FIND_FOLDER_RESPONSE
            );

            createSourceFolderFiles(MOCK_SOURCE_FOLDER_1);
            jest.advanceTimersByTime(10000);

            expect(testKit.fuzzyFsSearcher.findFolders(request)).toEqual({
                type: WebViewMessageType.findFolderResponse,
                data: [
                    {
                        repoRoot: "/path/to/folder-repo/root",
                        pathName: "example/path/to/abc123-dir/",
                    },
                ],
            });
            expect(
                testKit.fuzzyFsSearcher.findFolders({
                    type: WebViewMessageType.findFolderRequest,
                    data: {
                        relPath: "abc123-dir",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual({
                type: WebViewMessageType.findFolderResponse,
                data: [
                    {
                        repoRoot: "/path/to/folder-repo/root",
                        pathName: "example/path/to/abc123-dir/",
                    },
                ],
            });
            expect(
                testKit.fuzzyFsSearcher.findFolders({
                    type: WebViewMessageType.findFolderRequest,
                    data: {
                        relPath: "abc123-file",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FOLDER_RESPONSE);
        });

        test("asterisk doesn't trigger wildcard search", () => {
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "*",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FILE_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "*",
                        exactMatch: true,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FILE_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "**",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FILE_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFiles({
                    type: WebViewMessageType.findFileRequest,
                    data: {
                        relPath: "**",
                        exactMatch: true,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FILE_RESPONSE);

            expect(
                testKit.fuzzyFsSearcher.findFolders({
                    type: WebViewMessageType.findFolderRequest,
                    data: {
                        relPath: "*",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FOLDER_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFolders({
                    type: WebViewMessageType.findFolderRequest,
                    data: {
                        relPath: "*",
                        exactMatch: true,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FOLDER_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFolders({
                    type: WebViewMessageType.findFolderRequest,
                    data: {
                        relPath: "**",
                        exactMatch: false,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FOLDER_RESPONSE);
            expect(
                testKit.fuzzyFsSearcher.findFolders({
                    type: WebViewMessageType.findFolderRequest,
                    data: {
                        relPath: "**",
                        exactMatch: true,
                        maxResults: 10,
                        rootPath: "CAN_BE_ANYTHING",
                    },
                })
            ).toEqual(EMPTY_FIND_FOLDER_RESPONSE);
        });
    });

    describe("utils", () => {
        describe("mergeAndFilterWorkspaceFileChunks", () => {
            test("merges adjacent chunks", () => {
                const chunks: WorkspaceFileChunk[] = [
                    { charStart: 0, charEnd: 10, blobName: "abc123" },
                    { charStart: 10, charEnd: 20, blobName: "abc123" },
                    { charStart: 20, charEnd: 30, blobName: "abc123" },
                ];
                expect(mergeAndFilterWorkspaceFileChunks(chunks)).toEqual([
                    { charStart: 0, charEnd: 30, blobName: "abc123" },
                ]);
            });

            test("does not merge chunks from different files", () => {
                const chunks: WorkspaceFileChunk[] = [
                    { charStart: 0, charEnd: 10, blobName: "abc123" },
                    { charStart: 10, charEnd: 20, blobName: "def456" },
                    { charStart: 20, charEnd: 30, blobName: "abc123" },
                ];
                expect(mergeAndFilterWorkspaceFileChunks(chunks)).toEqual([
                    { charStart: 0, charEnd: 10, blobName: "abc123" },
                    { charStart: 20, charEnd: 30, blobName: "abc123" },
                    { charStart: 10, charEnd: 20, blobName: "def456" },
                ]);
            });

            test("removes empty chunks", () => {
                const chunks: WorkspaceFileChunk[] = [
                    { charStart: 0, charEnd: 10, blobName: "abc123" },
                    { charStart: 10, charEnd: 10, blobName: "abc123" },
                    { charStart: 20, charEnd: 30, blobName: "abc123" },
                ];
                expect(mergeAndFilterWorkspaceFileChunks(chunks)).toEqual([
                    { charStart: 0, charEnd: 10, blobName: "abc123" },
                    { charStart: 20, charEnd: 30, blobName: "abc123" },
                ]);
            });
        });

        describe("processIncomingChatStream", () => {
            test("merges chunks", async () => {
                const chunks: WorkspaceFileChunk[] = [
                    { charStart: 0, charEnd: 10, blobName: "abc123" },
                    { charStart: 10, charEnd: 20, blobName: "abc123" },
                    { charStart: 20, charEnd: 30, blobName: "abc123" },
                ];
                const stream = processIncomingChatStream(
                    "1234",
                    undefined,
                    (async function* () {
                        yield {
                            text: "",
                            workspaceFileChunks: chunks,
                        };
                    })(),
                    async (chunk: WorkspaceFileChunk): Promise<WorkspaceFileChunk> => {
                        return { ...chunk, file: { pathName: "abc123", repoRoot: "" } };
                    }
                );

                const results = [];
                for await (const result of stream) {
                    results.push(result);
                    await jest.advanceTimersByTimeAsync(1000);
                }
                expect(results).toEqual([
                    {
                        type: WebViewMessageType.chatModelReply,
                        data: {
                            streaming: true,
                            text: "",
                            requestId: "1234",
                            workspaceFileChunks: [],
                            nodes: undefined,
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            stop_reason: undefined,
                        },
                    },
                    {
                        type: WebViewMessageType.chatModelReply,
                        data: {
                            streaming: true,
                            text: "",
                            workspaceFileChunks: [
                                {
                                    charStart: 0,
                                    charEnd: 30,
                                    blobName: "abc123",
                                    file: {
                                        pathName: "abc123",
                                        repoRoot: "",
                                    },
                                },
                            ],
                            requestId: "1234",
                        },
                    },
                ]);
            });

            test("filters empty chunks", async () => {
                const chunks: WorkspaceFileChunk[] = [
                    { charStart: 0, charEnd: 10, blobName: "abc123" },
                    { charStart: 10, charEnd: 10, blobName: "abc123" },
                    { charStart: 20, charEnd: 30, blobName: "abc123" },
                ];
                const stream = processIncomingChatStream(
                    "1234",
                    undefined,
                    (async function* () {
                        yield {
                            text: "",
                            workspaceFileChunks: chunks,
                        };
                    })(),
                    async (chunk: WorkspaceFileChunk): Promise<WorkspaceFileChunk> => {
                        return { ...chunk, file: { pathName: "abc123", repoRoot: "" } };
                    }
                );

                const results: ChatModelReply[] = [];
                for await (const result of stream) {
                    results.push(result);
                }
                expect(results).toEqual([
                    {
                        type: WebViewMessageType.chatModelReply,
                        data: {
                            streaming: true,
                            text: "",
                            workspaceFileChunks: [],
                            requestId: "1234",
                            nodes: undefined,
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            stop_reason: undefined,
                        },
                    },
                    {
                        type: WebViewMessageType.chatModelReply,
                        data: {
                            streaming: true,
                            text: "",
                            workspaceFileChunks: [
                                {
                                    charStart: 0,
                                    charEnd: 10,
                                    blobName: "abc123",
                                    file: { pathName: "abc123", repoRoot: "" },
                                },
                                {
                                    charStart: 20,
                                    charEnd: 30,
                                    blobName: "abc123",
                                    file: { pathName: "abc123", repoRoot: "" },
                                },
                            ],
                            requestId: "1234",
                        },
                    },
                ]);
            });
        });
    });

    describe("startChatStream", () => {
        test("error raised returns unresolved chunks", async () => {
            const testKit = new ChatModelTestKit(wsManagerTestKit);
            const request: ChatRequest = {
                requestId: "1234",
                message: "hello world",
                chatHistory: [],
                blobs: {
                    checkpointId: undefined,
                    addedBlobs: [],
                    deletedBlobs: [],
                },
                userGuidedBlobs: [],
                externalSourceIds: [],
                toolDefinitions: [],
            };
            async function* helloGenerator(): AsyncGenerator<ChatResult, void, undefined> {
                yield { text: "hello" };
                yield { text: " world" };
            }
            testKit.mockApiServer.chatStream = async () => helloGenerator();
            testKit.wsManager.getAllPathNames = () => [];

            const stream = testKit.chatModel.chatStream(request);
            const results: ChatModelReply[] = [];
            for await (const result of stream) {
                results.push(result);
            }

            expect(results).toEqual([
                {
                    type: WebViewMessageType.chatModelReply,
                    data: {
                        streaming: true,
                        text: "hello",
                        workspaceFileChunks: [],
                        requestId: "1234",
                        nodes: undefined,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        stop_reason: undefined,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        chunkId: 0,
                    },
                },
                {
                    type: WebViewMessageType.chatModelReply,
                    data: {
                        streaming: true,
                        text: " world",
                        workspaceFileChunks: [],
                        requestId: "1234",
                        nodes: undefined,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        stop_reason: undefined,
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        chunkId: 1,
                    },
                },
                {
                    type: WebViewMessageType.chatModelReply,
                    data: {
                        streaming: true,
                        text: "",
                        workspaceFileChunks: [],
                        requestId: "1234",
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        chunkId: 2,
                    },
                },
            ]);
        });

        test("doesn't modify agent memories for root workspace path", async () => {
            const testKit = new ChatModelTestKit(wsManagerTestKit);

            // Mock the workspace manager methods for non-root workspace path
            testKit.wsManager.getAllQualifiedPathNames = jest
                .fn()
                .mockReturnValue([
                    new QualifiedPathName("/home/<USER>/project", "src/components/file.tsx"),
                ]);
            testKit.wsManager.getFolderRoot = jest.fn().mockReturnValue("/home/<USER>/project");
            testKit.wsManager.getRepoRootForFolderRoot = jest
                .fn()
                .mockReturnValue("/home/<USER>/project");

            const request: ChatRequest = {
                requestId: "1234",
                message: "hello world",
                chatHistory: [],
                blobs: {
                    checkpointId: undefined,
                    addedBlobs: [],
                    deletedBlobs: [],
                },
                userGuidedBlobs: [],
                externalSourceIds: [],
                toolDefinitions: [],
                pathName: "file.tsx",
                agentMemories: "Existing Memories\n",
            };

            // Mock the API server's chatStream method
            async function* helloGenerator(): AsyncGenerator<ChatResult, void, undefined> {
                yield { text: "hello" };
            }
            testKit.mockApiServer.chatStream = jest.fn(async () => helloGenerator());

            // Start the chat stream
            const stream = testKit.chatModel.chatStream(request);

            // Consume the stream
            const results: ChatModelReply[] = [];
            for await (const result of stream) {
                results.push(result);
            }

            // Verify handleNonRootWorkspacePath was called with the correct parameters
            expect(testKit.mockApiServer.chatStream).toHaveBeenCalledWith(
                "1234" /* requestId */,
                "hello world" /* message */,
                expect.anything() /* chatHistory */,
                expect.anything() /* blobs */,
                expect.anything() /* userGuidedBlobs */,
                expect.anything() /* externalSourceIds */,
                undefined /* model */,
                expect.anything() /* vcsChange */,
                expect.anything() /* recentChanges */,
                undefined /* contextCodeExchangeRequestId */,
                undefined /* selectedCode */,
                undefined /* prefix */,
                undefined /* suffix */,
                expect.anything() /* pathName */,
                undefined /* language */,
                undefined /* sessionId */,
                undefined /* disableAutoExternalSources */,
                undefined /* userGuidelines */,
                undefined /* workspaceGuidelines */,
                expect.anything() /* toolDefinitions */,
                expect.anything() /* nodes */,
                undefined /* mode */,
                "Existing Memories\n" /* agentMemories */,
                undefined /* personaType */,
                undefined /* rules */,
                undefined /* silent */,
                false /* enableSupportToolUseStart */,
                false /* enableParallelToolUse */
            );
        });

        test("adds agent memories for non-root workspace path", async () => {
            const testKit = new ChatModelTestKit(wsManagerTestKit);

            // Mock the workspace manager methods for non-root workspace path
            testKit.wsManager.getAllQualifiedPathNames = jest
                .fn()
                .mockReturnValue([
                    new QualifiedPathName("/home/<USER>/project", "src/components/file.tsx"),
                ]);
            testKit.wsManager.getFolderRoot = jest
                .fn()
                .mockReturnValue("/home/<USER>/project/src/components");
            testKit.wsManager.getRepoRootForFolderRoot = jest
                .fn()
                .mockReturnValue("/home/<USER>/project");

            const request: ChatRequest = {
                requestId: "1234",
                message: "hello world",
                chatHistory: [],
                blobs: {
                    checkpointId: undefined,
                    addedBlobs: [],
                    deletedBlobs: [],
                },
                userGuidedBlobs: [],
                externalSourceIds: [],
                toolDefinitions: [],
                pathName: "file.tsx",
                agentMemories: "Existing Memories\n",
            };

            // Mock the API server's chatStream method
            async function* helloGenerator(): AsyncGenerator<ChatResult, void, undefined> {
                yield { text: "hello" };
            }
            testKit.mockApiServer.chatStream = jest.fn(async () => helloGenerator());

            // Start the chat stream
            const stream = testKit.chatModel.chatStream(request);

            // Consume the stream
            const results: ChatModelReply[] = [];
            for await (const result of stream) {
                results.push(result);
            }

            // Verify handleNonRootWorkspacePath was called with the correct parameters
            expect(testKit.mockApiServer.chatStream).toHaveBeenCalledWith(
                "1234" /* requestId */,
                "hello world" /* message */,
                expect.anything() /* chatHistory */,
                expect.anything() /* blobs */,
                expect.anything() /* userGuidedBlobs */,
                expect.anything() /* externalSourceIds */,
                undefined /* model */,
                expect.anything() /* vcsChange */,
                expect.anything() /* recentChanges */,
                undefined /* contextCodeExchangeRequestId */,
                undefined /* selectedCode */,
                undefined /* prefix */,
                undefined /* suffix */,
                expect.anything() /* pathName */,
                undefined /* language */,
                undefined /* sessionId */,
                undefined /* disableAutoExternalSources */,
                undefined /* userGuidelines */,
                undefined /* workspaceGuidelines */,
                expect.anything() /* toolDefinitions */,
                expect.anything() /* nodes */,
                undefined /* mode */,
                expect.stringMatching("src/components") /* agentMemories */,
                undefined /* personaType */,
                undefined /* rules */,
                undefined /* silent */,
                false /* enableSupportToolUseStart */,
                false /* enableParallelToolUse */
            );
        });
    });

    describe("handleNonRootWorkspacePath", () => {
        test("adds agent memories for non-root workspace path", async () => {
            // Import the function directly for testing

            const request: ChatRequest = {
                requestId: "1234",
                message: "hello world",
                chatHistory: [],
                blobs: {
                    checkpointId: undefined,
                    addedBlobs: [],
                    deletedBlobs: [],
                },
                userGuidedBlobs: [],
                externalSourceIds: [],
                toolDefinitions: [],
            };

            const relPath = "src/components";
            const updatedRequest = handleNonRootWorkspacePath(request, relPath);

            // Check that agent memories were added correctly
            expect(updatedRequest.agentMemories).toContain(
                "The user is working from the directory `src/components`"
            );
            expect(updatedRequest.agentMemories).toContain(
                "When the user mentions a file name or when viewing output from shell commands, it is likely relative to `src/components`"
            );
            expect(updatedRequest.agentMemories).toContain(
                "When creating, deleting, viewing or editing files, first try prepending `src/components` to the path"
            );
            expect(updatedRequest.agentMemories).toContain(
                "When running shell commands, do not prepend `src/components` to the path"
            );
        });

        test("appends to existing agent memories", async () => {
            const request: ChatRequest = {
                requestId: "1234",
                message: "hello world",
                chatHistory: [],
                blobs: {
                    checkpointId: undefined,
                    addedBlobs: [],
                    deletedBlobs: [],
                },
                userGuidedBlobs: [],
                externalSourceIds: [],
                toolDefinitions: [],
                agentMemories: "Existing memory\n",
            };

            const relPath = "src/components";
            const updatedRequest = handleNonRootWorkspacePath(request, relPath);

            // Check that agent memories were appended correctly
            expect(updatedRequest.agentMemories).toContain("Existing memory\n");
            expect(updatedRequest.agentMemories).toContain(
                "The user is working from the directory `src/components`"
            );
        });
    });

    describe("limitChatHistoryTruncate", () => {
        test("returns full history when under segment limit", () => {
            const testKit = new ChatModelTestKit(wsManagerTestKit);
            testKit.featureFlagManager.update({
                ...testKit.featureFlagManager.currentFlags,
                vscodeChatStablePrefixTruncationMinVersion: "0.0.0",
            });

            // Create a small history that should be under the limit
            /* eslint-disable @typescript-eslint/naming-convention */
            const smallHistory: Exchange[] = [
                {
                    request_message: "Hello",
                    response_text: "Hi there",
                    request_id: "1",
                },
                {
                    request_message: "How are you?",
                    response_text: "I'm good",
                    request_id: "2",
                },
            ];
            /* eslint-enable @typescript-eslint/naming-convention */

            const result = testKit.chatModel.limitChatHistory(smallHistory);
            expect(result).toEqual(smallHistory);
        });

        test("history stable while growing", () => {
            const testKit = new ChatModelTestKit(wsManagerTestKit);
            testKit.featureFlagManager.update({
                ...testKit.featureFlagManager.currentFlags,
                vscodeChatStablePrefixTruncationMinVersion: "0.0.0",
            });

            // Create a large history that will exceed the limit
            // This test does rely on the limit hard-coded in the implementation: currently 800000
            /* eslint-disable @typescript-eslint/naming-convention */
            const largeHistory = Array(20)
                .fill(0)
                .map((_, i) => ({
                    request_message: `Message ${i}`,
                    response_text: `Response ${i}`,
                    request_id: "x".repeat(100000),
                }));
            /* eslint-enable @typescript-eslint/naming-convention */

            const cases = [
                { count: 1, start: 0 },
                { count: 2, start: 0 },
                { count: 3, start: 0 },
                { count: 4, start: 0 },
                { count: 5, start: 0 },
                { count: 6, start: 0 },
                { count: 7, start: 4 },
                { count: 8, start: 4 },
                { count: 9, start: 4 },
                { count: 10, start: 4 },
                { count: 11, start: 8 },
                { count: 12, start: 8 },
                { count: 13, start: 8 },
                { count: 14, start: 8 },
                { count: 15, start: 12 },
                { count: 16, start: 12 },
                { count: 17, start: 12 },
                { count: 18, start: 12 },
                { count: 19, start: 16 },
                { count: 20, start: 16 },
            ];
            for (const testCase of cases) {
                const result = testKit.chatModel.limitChatHistory(
                    largeHistory.slice(0, testCase.count)
                );
                expect(JSON.stringify(result).length).toBeLessThan(800000);
                expect(result[0].request_message).toEqual(
                    largeHistory[testCase.start].request_message
                );
            }
        });
    });
});
