import { describe, expect, test } from "@jest/globals";

import { InitialOrientation } from "../../chat/agent-onboarding-orientation";

describe("agentMdPattern", () => {
    const { agentMdPattern } = InitialOrientation;

    test("should match simple content without code blocks", () => {
        const input = "<agent_md>This is simple content</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe("This is simple content");
    });

    test("should match content with code blocks without language specification", () => {
        const input = "<agent_md>```\nconst x = 1;\nconsole.log(x);\n```</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe("const x = 1;\nconsole.log(x);\n");
    });

    test("should match content with code blocks with language specification", () => {
        const input = "<agent_md>```javascript\nconst x = 1;\nconsole.log(x);\n```</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe("const x = 1;\nconsole.log(x);\n");
    });

    test("should match content with code blocks without newlines", () => {
        const input = "<agent_md>```\nconst x = 1;```</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe("const x = 1;");
    });

    test("should match multiline content without code blocks", () => {
        const input = "<agent_md>Line 1\nLine 2\nLine 3</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe("Line 1\nLine 2\nLine 3");
    });

    test("should handle whitespace between tags and code blocks", () => {
        const input = "<agent_md>  ```javascript\nconst x = 1;\n```  </agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe("const x = 1;\n");
    });

    test("should not match when tags are malformed", () => {
        const input = "<agent_md>content here<agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).toBeNull();
    });

    test("should not match when opening tag is missing", () => {
        const input = "content here</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).toBeNull();
    });

    test("should not match when closing tag is missing", () => {
        const input = "<agent_md>content here";
        const match = input.match(agentMdPattern);

        expect(match).toBeNull();
    });

    test("should handle complex nested content", () => {
        const input =
            "<agent_md>```javascript\nfunction test() {\n  console.log('Hello world');\n  return {\n    a: 1,\n    b: 2\n  };\n}\n```</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe(
            "function test() {\n  console.log('Hello world');\n  return {\n    a: 1,\n    b: 2\n  };\n}\n"
        );
    });

    test("should handle multiple code blocks", () => {
        const input =
            "<agent_md>First block:\n```javascript\nconst a = 1;\n```\n\nSecond block:\n```python\nprint('hello')\n```</agent_md>";
        const match = input.match(agentMdPattern);

        expect(match).not.toBeNull();
        expect(match![1]).toBe(
            "First block:\n```javascript\nconst a = 1;\n```\n\nSecond block:\n```python\nprint('hello')\n"
        );
    });
});
