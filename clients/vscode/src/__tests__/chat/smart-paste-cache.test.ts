import { ChatInstructionStreamResult } from "../../augment-api";
import { ISmartPasteCacheContext, SmartPasteCache } from "../../chat/smart-paste-cache";
import { WebViewMessageType } from "../../webview-providers/webview-messages";

describe("SmartPasteCache", () => {
    test("get - cache miss, cache hit", async () => {
        /**
         * Tests the `get` method of the `SmartPasteCache` class.
         * This test covers two scenarios:
         * 1. Cache miss: When the cache is empty and a new request is made.
         * 2. Cache hit: When the same request is made again and the result is retrieved from the cache.
         *
         * It verifies that:
         * - The correct edit operation is returned for both cache miss and hit.
         * - The `createStream` function is called only once (on cache miss).
         * - The cached result is returned on subsequent identical requests.
         */
        async function* _createStream(): AsyncGenerator<ChatInstructionStreamResult> {
            yield {
                text: "console.log('Hello, World!');",
                unknownBlobNames: [],
                checkpointNotFound: false,
                replacementText: "A",
                replacementOldText: "B",
                replacementStartLine: 10,
                replacementEndLine: 11,
            };
        }
        const mockSmartPasteCache = jest.fn(() =>
            Promise.resolve({
                generator: _createStream(),
                requestId: "12345",
            })
        );
        const cache = new SmartPasteCache(mockSmartPasteCache);

        // Create a context for the request
        const ctx: ISmartPasteCacheContext = {
            message: {
                type: WebViewMessageType.chatSmartPaste,
                data: {
                    generatedCode: 'console.log("Hello, World!");',
                    chatHistory: [],
                    targetFile: "/test/file.ts",
                },
            },
            selectedCodeDetails: {
                selectedCode: 'console.log("TO BE REPLACED");',
                path: "/test/file.ts",
                language: "typescript",
                prefix: 'console.log("PREFIX!");',
                suffix: 'console.log("SUFFIX");',
            },
            targetFilePath: "/test/file.ts",
            targetFileContent:
                'console.log("PREFIX!");\nconsole.log("TO BE REPLACED");\nconsole.log("SUFFIX");',
        };

        // Cache miss
        const result = await cache.get(
            ctx.message.data.generatedCode,
            ctx.targetFilePath,
            ctx.targetFileContent,
            ctx
        );
        expect(result).toBeDefined();
        for await (const edit of result!.generator) {
            expect(edit).toEqual({
                text: "console.log('Hello, World!');",
                unknownBlobNames: [],
                checkpointNotFound: false,
                replacementText: "A",
                replacementOldText: "B",
                replacementStartLine: 10,
                replacementEndLine: 11,
            });
        }
        expect(mockSmartPasteCache).toHaveBeenCalledTimes(1);

        // Cache hit
        const result2 = await cache.get(
            ctx.message.data.generatedCode,
            ctx.targetFilePath,
            ctx.targetFileContent,
            ctx
        );
        expect(result2).toBeDefined();
        for await (const edit of result2!.generator) {
            expect(edit).toEqual({
                text: "console.log('Hello, World!');",
                unknownBlobNames: [],
                checkpointNotFound: false,
                replacementText: "A",
                replacementOldText: "B",
                replacementStartLine: 10,
                replacementEndLine: 11,
            });
        }
        expect(mockSmartPasteCache).toHaveBeenCalledTimes(1);
    });

    test("get - cache miss with error, cache miss success", async () => {
        /**
         * Tests the `get` method of the `SmartPasteCache` class.
         * This test covers two scenarios:
         * 1. Cache miss with error: When the cache is empty and a new request is made, but an error occurs during the request.
         * 2. Cache miss success: When the same request is made again and the result is retrieved from the cache.
         *
         * It verifies that:
         * - The correct edit operation is returned for both cache miss with error and success.
         * - The `createStream` function is called only once (on cache miss).
         * - The cached result is returned on subsequent identical requests.
         */
        let error: Error | undefined = undefined;
        async function* _createStream(): AsyncGenerator<ChatInstructionStreamResult> {
            yield {
                text: "console.log('Hello, World!');",
                unknownBlobNames: [],
                checkpointNotFound: false,
                replacementText: "A",
                replacementOldText: "B",
                replacementStartLine: 10,
                replacementEndLine: 11,
            };
        }
        const mockSmartPasteCache = jest.fn(() => {
            if (error) {
                return Promise.reject(error);
            }
            return Promise.resolve({
                generator: _createStream(),
                requestId: "12345",
            });
        });
        const cache = new SmartPasteCache(mockSmartPasteCache);

        // Create a context for the request
        const ctx: ISmartPasteCacheContext = {
            message: {
                type: WebViewMessageType.chatSmartPaste,
                data: {
                    generatedCode: 'console.log("Hello, World!");',
                    chatHistory: [],
                    targetFile: "/test/file.ts",
                },
            },
            selectedCodeDetails: {
                selectedCode: 'console.log("TO BE REPLACED");',
                path: "/test/file.ts",
                language: "typescript",
                prefix: 'console.log("PREFIX!");',
                suffix: 'console.log("SUFFIX");',
            },
            targetFilePath: "/test/file.ts",
            targetFileContent:
                'console.log("PREFIX!");\nconsole.log("TO BE REPLACED");\nconsole.log("SUFFIX");',
        };

        // Cache miss with error
        error = new Error("Error in _createStream");
        await expect(
            cache.get(
                ctx.message.data.generatedCode,
                ctx.targetFilePath,
                ctx.targetFileContent,
                ctx
            )
        ).rejects.toThrow(error);

        // Cache miss success
        error = undefined;
        const result2 = await cache.get(
            ctx.message.data.generatedCode,
            ctx.targetFilePath,
            ctx.targetFileContent,
            ctx
        );
        expect(result2).toBeDefined();
        for await (const edit of result2!.generator) {
            expect(edit).toEqual({
                text: "console.log('Hello, World!');",
                unknownBlobNames: [],
                checkpointNotFound: false,
                replacementText: "A",
                replacementOldText: "B",
                replacementStartLine: 10,
                replacementEndLine: 11,
            });
        }
        expect(mockSmartPasteCache).toHaveBeenCalledTimes(2);
    });
});
