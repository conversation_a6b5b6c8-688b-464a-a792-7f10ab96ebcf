import { mockFSUtils } from "$vscode/src/__mocks__/fs-utils";
import {
    MutableTextDocument,
    resetMockWorkspace,
    TextEditor,
    Uri,
    window,
    workspace,
} from "$vscode/src/__mocks__/vscode-mocks";
import { AugmentConfigListener } from "$vscode/src/augment-config-listener";
import { FuzzyFsSearcher } from "$vscode/src/chat/fuzzy-fs-searcher";

// QualifiedPathName is used in the commented-out tests
import * as vscode from "vscode";

import { FuzzySymbolSearcher, THROTTLE_MS } from "../../chat/fuzzy-symbol-searcher";
import {
    AugmentGlobalState,
    FileStorageChangeListener,
    FileStorageOrCacheKey,
    IAugmentGlobalState,
    IGlobalContextSaveLoadOpts,
    InMemoryContextKey,
} from "../../utils/context";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitStartup,
    createWorkspaceFiles,
    MockSourceFolder,
    MockSourceFolderType,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";

class MockAugmentGlobalState implements IAugmentGlobalState {
    private _values: Map<FileStorageOrCacheKey | InMemoryContextKey, any> = new Map();

    update<T>(key: InMemoryContextKey, value: T): Thenable<void> {
        this._values.set(key, value);
        return Promise.resolve();
    }

    get<T>(key: InMemoryContextKey): T | undefined {
        return this._values.get(key);
    }

    save: AugmentGlobalState["save"] = jest.fn(
        <T>(
            key: FileStorageOrCacheKey,
            value: T,
            _opts?: IGlobalContextSaveLoadOpts
        ): Promise<void> => {
            this._values.set(key, value);
            return Promise.resolve();
        }
    );

    load: AugmentGlobalState["load"] = jest.fn(
        <T>(
            key: FileStorageOrCacheKey,
            _opts?: IGlobalContextSaveLoadOpts
        ): Promise<T | undefined> => {
            return Promise.resolve(this._values.get(key));
        }
    ) as AugmentGlobalState["load"];

    onDidChangeFileStorage<T>(
        _key: FileStorageOrCacheKey,
        _listener: FileStorageChangeListener<T>
    ): vscode.Disposable {
        return {
            dispose: () => {},
        };
    }
}

const workspaceFolder1: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/folder1"),
    repoRootUri: vscode.Uri.file("/src/folder1"),
    files: new Map<string, string>([
        ["fizz/file1-foo.py", "this is file 1 of sourceFolder1"],
        ["buzz/file2-foobar.py", "this is file 2"],
        ["fizzbuzz/file3-bar.py", "this is file 3"],
    ]),
    ignoredFiles: new Map<string, string>([[".git/logs/HEAD", "head file"]]),
};

describe("FuzzySymbolSearcher", () => {
    let kit: WorkspaceManagerTestKit;
    let fuzzyFsSearcher: FuzzyFsSearcher;
    let fuzzySymbolSearcher: FuzzySymbolSearcher;
    let workspaceManager: WorkspaceManager;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        mockFSUtils.reset();

        // Initialize the workspace and test kit
        createWorkspaceFiles([workspaceFolder1]);
        kit = new WorkspaceManagerTestKit();

        // Start up the workspace manager
        const workspaceState = [{ ...workspaceFolder1, tracked: true }];
        kit.setupWorkspace(workspaceState);
        workspaceManager = kit.createWorkspaceManager();

        await awaitStartup(workspaceManager, workspaceState);
        fuzzyFsSearcher = new FuzzyFsSearcher(
            new MockAugmentGlobalState(),
            workspaceManager,
            jest.fn(() => ({ dispose: () => {} }))
        );
        fuzzySymbolSearcher = new FuzzySymbolSearcher(
            new MockAugmentGlobalState(),
            new AugmentConfigListener(),
            fuzzyFsSearcher,
            workspaceManager
        );

        createWorkspaceFiles([workspaceFolder1]);
    });

    describe("onDidChangeTextDocument disposable", () => {
        it("should remove cache entry when a document changes", async () => {
            // Setup - use a file that exists in the test workspace
            const relPath = "fizz/file1-foo.py";

            // Calculate a real blob name using the test kit
            const blobName = kit.computeBlobName(workspaceFolder1, relPath);

            // Create a document for the file
            const uri = Uri.joinPath(workspaceFolder1.folderRootUri, relPath);
            const doc = new MutableTextDocument(uri, "a");

            // Mock getBlobName to return our calculated blob name
            jest.spyOn(workspaceManager, "getBlobName").mockReturnValue(blobName);
            // Spy on the warmupCache method
            const warmupCacheSpy = jest.spyOn(fuzzySymbolSearcher, "warmupCache");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];

            // Trigger event for doc
            workspace.textDocumentChanged.fire(doc.delete(0, 1));

            // Allow event loop to run
            await jest.advanceTimersByTimeAsync(1);

            // Verify cache entry was removed with the correct blob name
            expect(warmupCacheSpy).toHaveBeenCalledWith(blobName);
        });

        it("should throttle cache invalidation for rapid document changes", async () => {
            // Setup - use a file that exists in the test workspace
            const relPath = "fizz/file1-foo.py";

            // Calculate a real blob name using the test kit
            const blobName = kit.computeBlobName(workspaceFolder1, relPath);

            // Create a document for the file
            const uri = Uri.joinPath(workspaceFolder1.folderRootUri, relPath);
            const doc = new MutableTextDocument(uri, "a");

            // Mock getBlobName to return our calculated blob name
            jest.spyOn(workspaceManager, "getBlobName").mockReturnValue(blobName);

            // Spy on the warmupCache method
            const warmupCacheSpy = jest.spyOn(fuzzySymbolSearcher, "warmupCache");

            window.activeTextEditor = new TextEditor(doc);
            workspace.textDocuments = [doc];

            // Trigger multiple events in quick succession
            workspace.textDocumentChanged.fire(doc.delete(0, 1));
            workspace.textDocumentChanged.fire(doc.insert(0, "b"));
            workspace.textDocumentChanged.fire(doc.insert(1, "c"));

            // Advance time by less than the throttle interval (5000ms)
            await jest.advanceTimersByTimeAsync(THROTTLE_MS - 500);

            // Verify cache entry was removed only once (leading edge execution)
            expect(warmupCacheSpy).toHaveBeenCalledTimes(1);
            expect(warmupCacheSpy).toHaveBeenCalledWith(blobName);

            // Reset the spy
            warmupCacheSpy.mockClear();

            // Advance time past the throttle interval
            await jest.advanceTimersByTimeAsync(600); //

            // Verify cache entry was removed again (trailing edge execution)
            expect(warmupCacheSpy).toHaveBeenCalledTimes(1);
            expect(warmupCacheSpy).toHaveBeenCalledWith(blobName);
        });
    });
});
