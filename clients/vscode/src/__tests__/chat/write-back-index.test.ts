import { beforeEach, describe, expect, test } from "@jest/globals";
import * as vscode from "vscode";

import { WriteBackIndex } from "../../chat/write-back-index";
import {
    FileStorageChangeListener,
    FileStorageOrCacheKey,
    IAugmentGlobalState,
    InMemoryContextKey,
    WriteBackCacheKey,
} from "../../utils/context";

class MockAugmentGlobalState implements IAugmentGlobalState {
    private _values: Map<FileStorageOrCacheKey | InMemoryContextKey, any> = new Map();

    update<T>(key: InMemoryContextKey, value: T): Thenable<void> {
        this._values.set(key, value);
        return Promise.resolve();
    }

    get<T>(key: InMemoryContextKey): T | undefined {
        return this._values.get(key);
    }

    save<T>(key: FileStorageOrCacheKey, value: T): Thenable<void> {
        this._values.set(key, value);
        return Promise.resolve();
    }

    load<T>(key: FileStorageOrCacheKey): Thenable<T> {
        return Promise.resolve(this._values.get(key));
    }

    onDidChangeFileStorage<T>(
        _key: FileStorageOrCacheKey,
        _listener: FileStorageChangeListener<T>
    ): vscode.Disposable {
        return {
            dispose: () => {},
        };
    }
}

describe("WriteBackIndex", () => {
    let mockGlobalState: IAugmentGlobalState;
    let writeBackIndex: WriteBackIndex<string>;

    beforeEach(() => {
        mockGlobalState = new MockAugmentGlobalState();
        writeBackIndex = new WriteBackIndex(mockGlobalState, WriteBackCacheKey.fuzzyFsFilesIndex, {
            fuse: {},
            validator: async (item: string) => item !== "b",
            keyFn: (item: string) => item,
            maxKeysBeforeRemoval: 2,
        });
        jest.useFakeTimers();
    });

    test("set", async () => {
        expect(writeBackIndex.set("a")).toBe(true);
        expect(writeBackIndex.set("a")).toBe(false);
        expect(writeBackIndex.set("b")).toBe(true);
        expect(writeBackIndex.set("c")).toBe(true);
    });

    test("remove", async () => {
        writeBackIndex.set("a");
        writeBackIndex.set("b");
        writeBackIndex.set("c");
        writeBackIndex.remove("b");
        expect(writeBackIndex.items).toEqual({ a: "a", b: "b", c: "c" });
        await jest.runAllTimersAsync();
        expect(writeBackIndex.items).toEqual({ a: "a", c: "c" });
    });

    test("clear", async () => {
        writeBackIndex.set("a");
        writeBackIndex.set("b");
        writeBackIndex.set("c");
        writeBackIndex.clear();
        expect(writeBackIndex.items).toEqual({});
    });

    test("search", async () => {
        writeBackIndex.set("a");
        writeBackIndex.set("b");
        writeBackIndex.set("c");
        expect(writeBackIndex.search("a")).toEqual([{ item: "a", refIndex: 0 }]);
    });

    test("revalidate", async () => {
        writeBackIndex.set("a");
        writeBackIndex.set("b");
        writeBackIndex.set("c");
        writeBackIndex.markForRevalidation(["b"]);
        await jest.runAllTimersAsync();
        expect(writeBackIndex.items).toEqual({ a: "a", c: "c" });
    });

    test("dumpContext", async () => {
        writeBackIndex.set("a");
        writeBackIndex.set("b");
        writeBackIndex.set("c");
        jest.runAllTimers();
        expect(mockGlobalState.get(WriteBackCacheKey.fuzzyFsFilesIndex)).toEqual({
            a: "a",
            b: "b",
            c: "c",
        });
    });

    test("loadContext", async () => {
        writeBackIndex = new WriteBackIndex(mockGlobalState, WriteBackCacheKey.fuzzyFsFilesIndex, {
            fuse: {},
            validator: async () => Promise.resolve(true),
            keyFn: (item: string) => item,
            maxKeysBeforeRemoval: 1,
        });
        const mockContext = { a: "a", b: "b", c: "c" };
        await mockGlobalState.save(WriteBackCacheKey.fuzzyFsFilesIndex, mockContext);

        await jest.runAllTimersAsync();
        // Wait for the data to load in
        expect(writeBackIndex.items).toEqual(mockContext);
    });

    describe("State Transitions", () => {
        test("Not in Cache to Active", async () => {
            expect(writeBackIndex.items).toEqual({});
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
        });

        test("Active to Revalidating", async () => {
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.markForRevalidation(["a"]);
            expect(writeBackIndex.items).toEqual({ a: "a" });
            await jest.runAllTimersAsync();
            expect(writeBackIndex.items).toEqual({ a: "a" });
        });

        test("Active to Queued for Removal", async () => {
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.remove("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            await jest.runAllTimersAsync();
            expect(writeBackIndex.items).toEqual({});
        });

        test("Revalidating to Valid", async () => {
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.markForRevalidation(["a"]);
            expect(writeBackIndex.items).toEqual({ a: "a" });
            await jest.runAllTimersAsync();
            expect(writeBackIndex.items).toEqual({ a: "a" });
        });

        test("Revalidating to Invalid", async () => {
            writeBackIndex.set("b");
            expect(writeBackIndex.items).toEqual({ b: "b" });
            writeBackIndex.markForRevalidation(["b"]);
            expect(writeBackIndex.items).toEqual({ b: "b" });
            await jest.runAllTimersAsync();
            expect(writeBackIndex.items).toEqual({});
        });

        test("Valid to Active (implicit)", async () => {
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.markForRevalidation(["a"]);
            expect(writeBackIndex.items).toEqual({ a: "a" });
            await jest.runAllTimersAsync();
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
        });

        test("Invalid to Queued for Removal", async () => {
            writeBackIndex.set("b");
            expect(writeBackIndex.items).toEqual({ b: "b" });
            writeBackIndex.markForRevalidation(["b"]);
            expect(writeBackIndex.items).toEqual({ b: "b" });
            await jest.runAllTimersAsync();
            expect(writeBackIndex.items).toEqual({});
        });

        test("Queued for Removal to Not in Cache", async () => {
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.remove("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            // Simulate flushing the removal queue
            (writeBackIndex as any)._flushToRemove();
            expect(writeBackIndex.items).toEqual({});
        });

        test("Queued for Removal to Active (canceling removal)", async () => {
            writeBackIndex.set("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.remove("a");
            expect(writeBackIndex.items).toEqual({ a: "a" });
            writeBackIndex.set("a");
            await jest.runAllTimersAsync();
            expect(writeBackIndex.items).toEqual({ a: "a" });
        });
    });
});
