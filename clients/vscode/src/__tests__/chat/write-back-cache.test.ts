import { beforeEach, describe, expect, jest, test } from "@jest/globals";
import * as vscode from "vscode";

import { WriteBackCache } from "../../chat/write-back-cache";
import {
    FileStorageChangeListener,
    FileStorageOrCacheKey,
    type IAugmentGlobalState,
    InMemoryContextKey,
    WriteBackCacheKey,
} from "../../utils/context";
import { SelectedCodeDetails } from "../../utils/types";

class MockAugmentGlobalState implements IAugmentGlobalState {
    private _values: Map<FileStorageOrCacheKey | InMemoryContextKey, any> = new Map();

    update<T>(key: InMemoryContextKey, value: T): Thenable<void> {
        this._values.set(key, value);
        return Promise.resolve();
    }

    get<T>(key: InMemoryContextKey): T | undefined {
        return this._values.get(key);
    }

    save<T>(key: FileStorageOrCacheKey, value: T): Thenable<void> {
        this._values.set(key, value);
        return Promise.resolve();
    }

    load<T>(key: FileStorageOrCacheKey): Thenable<T> {
        return Promise.resolve(this._values.get(key));
    }

    onDidChangeFileStorage<T>(
        _key: FileStorageOrCacheKey,
        _listener: FileStorageChangeListener<T>
    ): vscode.Disposable {
        return {
            dispose: () => {},
        };
    }
}

describe("WriteBackCache", () => {
    let selectionCache: WriteBackCache<SelectedCodeDetails>;
    let mockGlobalState: IAugmentGlobalState;

    beforeEach(() => {
        mockGlobalState = new MockAugmentGlobalState();
        selectionCache = new WriteBackCache(
            mockGlobalState,
            WriteBackCacheKey.requestIdSelectionMetadata
        );
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("set and get", () => {
        const requestId = "test-request-id";
        const selectedCodeDetails: SelectedCodeDetails = {
            selectedCode: 'console.log("Hello, World!");',
            path: "/test/file.ts",
            language: "typescript",
            prefix: 'console.log("Hello!");',
            suffix: 'console.log("World!");',
        };

        selectionCache.set(requestId, selectedCodeDetails);
        const result = selectionCache.get(requestId);

        expect(result).toEqual(selectedCodeDetails);
        jest.advanceTimersByTime(10 * 60 * 1000);
    });

    test("dumpContext", async () => {
        const requestId = "test-request-id";
        const selectedCodeDetails: SelectedCodeDetails = {
            selectedCode: 'console.log("Hello, World!");',
            path: "/test/file.ts",
            language: "typescript",
            prefix: 'console.log("Hello!");',
            suffix: 'console.log("World!");',
        };

        selectionCache.set(requestId, selectedCodeDetails);

        // Wait for the debounced dumpContext to execute
        jest.advanceTimersByTime(5 * 1000);
        const dumpedContext = mockGlobalState.get(WriteBackCacheKey.requestIdSelectionMetadata);
        expect(dumpedContext).toBeDefined();
        expect(dumpedContext).toContainEqual([requestId, expect.any(Object)]);
        jest.advanceTimersByTime(10 * 60 * 1000);
    });

    test("loadContext", async () => {
        const requestId = "test-request-id";
        const selectedCodeDetails: SelectedCodeDetails = {
            selectedCode: 'console.log("Hello, World!");',
            path: "/test/file.ts",
            language: "typescript",
            prefix: 'console.log("Hello!");',
            suffix: 'console.log("World!");',
        };

        const mockContext = [
            [requestId, { value: selectedCodeDetails, ttl: Date.now() + 3600000 }],
        ];
        await mockGlobalState.save(WriteBackCacheKey.requestIdSelectionMetadata, mockContext);

        // Create a new SelectionCache instance to trigger loadContext
        const newSelectionCache = new WriteBackCache(
            mockGlobalState,
            WriteBackCacheKey.requestIdSelectionMetadata
        );
        await jest.runAllTimersAsync();

        const result = newSelectionCache.get(requestId);
        expect(result).toEqual(selectedCodeDetails);
        jest.advanceTimersByTime(10 * 60 * 1000);
    });

    test("manual removal triggers onExpiry callback", async () => {
        // Create a mock expiry callback
        const mockOnExpiry = jest.fn();

        // Create a cache with the mock expiry callback
        const cache = new WriteBackCache<string, any>(
            mockGlobalState,
            WriteBackCacheKey.requestIdSelectionMetadata,
            {
                lru: {
                    max: 100,
                    disposeAfter: (value, key) => mockOnExpiry(key),
                },
            }
        );

        // Add an item to the cache
        const testKey = "manual-delete-key";
        const testValue = "test-value";
        await cache.set(testKey, testValue);

        // Reset the mock to clear any calls that might have happened during setup
        mockOnExpiry.mockClear();

        // Manually remove the item using the public remove method
        cache.remove(testKey);

        // Verify the item is no longer in the cache
        expect(cache.get(testKey)).toBeUndefined();

        // Verify the onExpiry callback was NOT called
        expect(mockOnExpiry).toHaveBeenCalled();
    });
});
