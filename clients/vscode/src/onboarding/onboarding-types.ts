/**
 * OnboardingSessionEventName is the name of an onboarding session event.
 * Matches IntelliJ's OnboardingSessionEventName.api_names.
 */
/* eslint-disable @typescript-eslint/naming-convention */
export enum OnboardingSessionEventName {
    SignedIn = "signed-in",
    StartedSyncing = "started-syncing",
    FinishedSyncing = "finished-syncing",
    SawSummary = "saw-summary",
    SawAgentOnboarding = "saw-agent-onboarding",
    UsedChat = "used-chat",
    AcceptedCompletion = "accepted-completion",
    UsedSlashAction = "used-slash-action",
    CreatedNewProject = "created-new-project",
    OpenedFolder = "opened-folder",
    ClonedRepo = "cloned-repo",
}
/*eslint-enable @typescript-eslint/naming-convention */
