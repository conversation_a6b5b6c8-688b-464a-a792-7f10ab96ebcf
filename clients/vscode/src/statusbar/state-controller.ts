import * as vscode from "vscode";

import { StatusBarManager } from "../statusbar/status-bar-manager";
import * as statusbarStates from "../statusbar/status-bar-states";

// This class ensures that the status bar only shows the state of the most recent requests
// by disposing of any previous state before setting the new state.
export class StateController {
    private _state: vscode.Disposable | undefined;

    constructor(private _statusBar: StatusBarManager) {}

    public setState(s: statusbarStates.StateDefinition): vscode.Disposable {
        this.dispose();
        this._state = this._statusBar.setState(s);
        return this._state;
    }

    public dispose() {
        this._state?.dispose();
    }
}
