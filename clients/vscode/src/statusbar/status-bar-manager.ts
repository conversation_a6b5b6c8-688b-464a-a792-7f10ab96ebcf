import * as vscode from "vscode";

import { StatusBarClick } from "../commands/status-bar-click";
import { DisposableService } from "../utils/disposable-service";
import { StateManager } from "./state-manager";
import { initialState, StateDefinition } from "./status-bar-states";

export const DEFAULT_LABEL = "Augment";

export class StatusBarManager extends DisposableService {
    private _statusBarItem: vscode.StatusBarItem;

    private _stateManager: StateManager = new StateManager(initialState);
    private _currentState: StateDefinition | undefined;

    constructor() {
        super();

        this.addDisposable(new vscode.Disposable(() => this.reset()));

        this._statusBarItem = vscode.window.createStatusBarItem(
            "vscode-augment.PrimaryStatusBarItem",
            vscode.StatusBarAlignment.Right
        );
        this._statusBarItem.name = "Augment";
        this._statusBarItem.command = StatusBarClick.commandID;

        this.addDisposables(
            this._statusBarItem,
            this._stateManager.onDidChangeState(() => this.updateState())
        );

        this.updateState();
        this._statusBarItem.show();
    }

    private updateState() {
        const state = this._stateManager.getPriorityState();
        if (state === this._currentState) {
            return;
        }

        // Stash the new state
        this._currentState = state;

        this._statusBarItem.tooltip = state.tooltip;
        this._statusBarItem.backgroundColor = state.colors?.background;
        this._statusBarItem.color = state.colors?.foreground;
        const label = state.label ? state.label : DEFAULT_LABEL;
        this._statusBarItem.text = `${state.icon} ${label}`;
    }

    public setState(s: StateDefinition): vscode.Disposable {
        const disposable = this._stateManager.setState(s);
        this.updateState();
        return disposable;
    }

    public reset() {
        this._stateManager.reset();
    }
}
