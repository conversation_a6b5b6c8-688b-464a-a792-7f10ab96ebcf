import * as vscode from "vscode";

import { statePriorities, StatePriority } from "./priorities";
import { StateDefinition } from "./status-bar-states";

export class StateManager {
    private _stateEventEmitter = new vscode.EventEmitter<void>();
    private _state: { [key in StatePriority]: Array<StateDefinition> } = {
        [StatePriority.high]: [],
        [StatePriority.medium]: [],
        [StatePriority.low]: [],
        [StatePriority.neutral]: [],
    };

    public onDidChangeState = this._stateEventEmitter.event;

    constructor(private _baseState: StateDefinition) {}

    public setState(s: StateDefinition): vscode.Disposable {
        this._state[s.priority].push(s);
        let isDisposed = false;
        return vscode.Disposable.from({
            dispose: () => {
                if (isDisposed) {
                    return;
                }
                isDisposed = true;
                let removedState = false;
                this._state[s.priority] = this._state[s.priority].filter((x) => {
                    if (removedState) {
                        return true;
                    }
                    removedState = x === s;
                    return !removedState;
                });
                this._stateEventEmitter.fire();
            },
        });
    }

    public getPriorityState(): StateDefinition {
        for (const p of statePriorities) {
            const priorities = this._state[p];
            if (priorities.length > 0) {
                return priorities[priorities.length - 1];
            }
        }
        return this._baseState;
    }

    public reset() {
        for (const p of statePriorities) {
            this._state[p] = [];
        }
        this._stateEventEmitter.fire();
    }
}
