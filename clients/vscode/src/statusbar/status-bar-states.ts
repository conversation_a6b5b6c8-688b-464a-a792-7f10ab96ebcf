import { ThemeColor } from "vscode";

import { AugmentIcons } from "./icons";
import { StatePriority } from "./priorities";

// NOTE: VSCode only allows two background colors for status bar items,
// errorBackground and warningBackground.
// See https://code.visualstudio.com/api/references/vscode-api#StatusBarItem for details.
const warningColors = {
    background: new ThemeColor("statusBarItem.warningBackground"),
    foreground: new ThemeColor("statusBarItem.warningForeground"),
};

const errorColors = {
    background: new ThemeColor("statusBarItem.errorBackground"),
    foreground: new ThemeColor("statusBarItem.errorForeground"),
};

export interface StateDefinition {
    priority: StatePriority;
    tooltip: string;
    icon: AugmentIcons;
    label?: string;
    colors?: {
        background: ThemeColor;
        foreground: ThemeColor;
    };
}

export const initialState: StateDefinition = {
    priority: StatePriority.neutral,
    tooltip: "Augment",
    icon: AugmentIcons.simple,
};

export const enabled: StateDefinition = {
    priority: StatePriority.neutral,
    tooltip: "Open Augment",
    icon: AugmentIcons.smile,
};

export const signIn: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "Sign in to start using Augment",
    icon: AugmentIcons.simple,
    colors: warningColors,
};

export const syncing: StateDefinition = {
    priority: StatePriority.low,
    tooltip: "Augment is indexing your codebase",
    icon: AugmentIcons.vscodeSyncing,
};

export const noAPIToken: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "No API token",
    icon: AugmentIcons.simple,
    colors: warningColors,
};

export const noCompletionURL: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "No completion URL",
    icon: AugmentIcons.simple,
    colors: warningColors,
};

export const initializing: StateDefinition = {
    priority: StatePriority.low,
    tooltip: "Initializing Augment",
    icon: AugmentIcons.vscodeSpinner,
};

export const oauthFailed: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "Authentication failed, please sign in again",
    icon: AugmentIcons.simple,
    colors: warningColors,
};

export const apiTokenFailed: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "Authentication failed, please check your API token and completion URL",
    icon: AugmentIcons.simple,
    colors: warningColors,
};

export const invalidCompletionURL: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "The completion URL setting is invalid. Please enter a valid value",
    icon: AugmentIcons.simple,
    colors: warningColors,
};

export const noAutoCompletions: StateDefinition = {
    priority: StatePriority.low,
    tooltip: "Automatic completions are off",
    icon: AugmentIcons.disabled,
};

export const uploadDisabled: StateDefinition = {
    priority: StatePriority.medium,
    tooltip: "Enhancements are off",
    icon: AugmentIcons.simple,
};

export const getModelConfigFailed: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "Cannot connect to Augment",
    icon: AugmentIcons.simple,
    colors: errorColors,
};

export const completionFailed: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "Failed to generate completion",
    icon: AugmentIcons.simple,
    colors: errorColors,
};

export const generatingCompletion: StateDefinition = {
    priority: StatePriority.medium,
    tooltip: "Generating completion",
    icon: AugmentIcons.loading,
};

export const noCompletions: StateDefinition = {
    priority: StatePriority.low,
    tooltip: "No completions generated",
    icon: AugmentIcons.zero,
};

export const suggestionFailed: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "Failed to generate suggestions",
    icon: AugmentIcons.simple,
    colors: errorColors,
};

export const generatingSuggestion: StateDefinition = {
    priority: StatePriority.medium,
    tooltip: "Generating suggestions",
    icon: AugmentIcons.loading,
};

export const noSuggestions: StateDefinition = {
    priority: StatePriority.low,
    tooltip: "No suggestions generated",
    icon: AugmentIcons.zero,
};

export const syncingDisabled: StateDefinition = {
    priority: StatePriority.high,
    tooltip: "Workspace indexing is disabled",
    icon: AugmentIcons.vscodeCirlceSlash,
};
