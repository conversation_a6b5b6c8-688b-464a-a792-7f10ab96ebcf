/* eslint-disable @typescript-eslint/no-unsafe-member-access */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */

/* eslint-disable @typescript-eslint/no-unsafe-argument */
import { cloneDeep, isEqual } from "lodash";

import { AugmentConfigListener } from "./augment-config-listener";
import { type AugmentLogger } from "./logging";

/**
 * MonitoredParameter is a class for monitoring the value of a parameter and logging
 * it when it changes.
 */
export class MonitoredParameter<T> {
    private _isSet = false;
    private _value?: T;

    constructor(
        private name: string,
        private readonly _logger: AugmentLogger,
        private readonly _configListener?: AugmentConfigListener
    ) {}

    get value(): T | undefined {
        return this._value;
    }

    // `update` updates the parameter value. If the new value is different from the
    // current value, it logs the new value and returns true.
    public update(newValue: T): boolean {
        if (this._isSet && isEqual(newValue, this._value)) {
            return false;
        }
        if (!this._isSet) {
            this._logger.info(
                `${this.name} changed from <unset> to ${this._formatValue(newValue)}`
            );
        } else {
            this._logger.info(
                `${this.name} changed:\n${this.diff(this.value, newValue)
                    .map((s) => `  - ${s}`)
                    .join("\n")}`
            );
        }

        // `T` may be an object type, so make a copy of it
        this._value = cloneDeep(newValue);
        this._isSet = true;
        return true;
    }

    public diff(prevValue: any, newValue: any, nesting: Array<string> = []): Array<string> {
        if (isEqual(newValue, prevValue)) {
            return [];
        }

        if (!this.isObject(newValue) || !this.isObject(prevValue)) {
            return [`${this._formatValue(prevValue)} to ${this._formatValue(newValue)}`];
        }

        const allProperties = new Set([
            ...Object.keys(prevValue || {}),
            ...Object.keys(newValue || {}),
        ]);

        const changes: Array<string> = [];
        for (const key of allProperties) {
            if (this._configListener && key === "memoriesParams") {
                continue;
            }
            const newV = newValue ? newValue[key] : undefined;
            const oldV = prevValue ? prevValue[key] : undefined;
            if (!this.isObject(newV) || !this.isObject(oldV)) {
                if (newV !== oldV) {
                    changes.push(
                        `${nesting.concat(key).join(" > ")}: ${this._formatValue(
                            oldV
                        )} to ${this._formatValue(newV)}`
                    );
                }
            } else {
                changes.push(...this.diff(oldV, newV, nesting.concat(key)));
            }
        }
        return changes;
    }

    private isObject(value: any): boolean {
        return typeof value === "object" && value !== null;
    }

    public toString(): string {
        if (!this._isSet) {
            return "<unset>";
        }
        return this._formatValue(this.value);
    }

    private _formatValue(value: any) {
        if (value === undefined) {
            return "undefined";
        }

        return JSON.stringify(value);
    }
}
