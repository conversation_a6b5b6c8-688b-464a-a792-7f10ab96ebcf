import * as vscode from "vscode";

import { AugmentGlobalState, GlobalContextKey } from "../../utils/context";
import { DisposableService } from "../../utils/disposable-service";
import { SystemStateName, SystemStatus } from "../../utils/types";
import { DerivedStateName } from "./types";

export type SystemState = {
    name: SystemStateName;
    status: SystemStatus;
};

export type StateCondition = {
    name: SystemStateName;
    status: SystemStatus;
};

export type DerivedState = {
    name: DerivedStateName;
    renderOrder: number; // lower render priority means it gets rendered first.
    desiredConditions: Array<StateCondition>; // This is a collection of desired system state conditions.
};

export const initialSystemStates = new Map<SystemStateName, SystemState>([
    [
        SystemStateName.authenticated,
        { name: SystemStateName.authenticated, status: SystemStatus.initializing },
    ],
    [
        // The syncingPermitted state uses the values of systemStatus as follows:
        //  * initializing: Awaiting permission to sync the workspace.
        //  * incomplete: Denied permission to sync one or more folders in the workspace.
        //  * complete: Permission granted for all folders in the workspace.
        // The initial value is "complete", because WorkspaceManager hasn't yet opened any folders.
        // It will assert a new state as needed after opening the workspace folders.
        SystemStateName.syncingPermitted,
        { name: SystemStateName.syncingPermitted, status: SystemStatus.complete },
    ],
    [
        SystemStateName.disabledGithubCopilot,
        { name: SystemStateName.disabledGithubCopilot, status: SystemStatus.initializing },
    ],
    [
        SystemStateName.hasMovedExtensionAside,
        { name: SystemStateName.hasMovedExtensionAside, status: SystemStatus.incomplete },
    ],
    [
        SystemStateName.workspacePopulated,
        { name: SystemStateName.workspacePopulated, status: SystemStatus.initializing },
    ],
    [
        SystemStateName.workspaceSelected,
        { name: SystemStateName.workspaceSelected, status: SystemStatus.initializing },
    ],
    [
        SystemStateName.disabledCodeium,
        { name: SystemStateName.disabledCodeium, status: SystemStatus.initializing },
    ],
    [
        SystemStateName.uploadingHomeDir,
        { name: SystemStateName.uploadingHomeDir, status: SystemStatus.initializing },
    ],
    [
        SystemStateName.workspaceTooLarge,
        { name: SystemStateName.workspaceTooLarge, status: SystemStatus.initializing },
    ],
]);

export const initialDerivedStates = new Map<DerivedStateName, DerivedState>([
    [
        DerivedStateName.signInRequired,
        {
            name: DerivedStateName.signInRequired,
            renderOrder: 0,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.incomplete },
            ],
        },
    ],
    [
        DerivedStateName.syncingPermissionNeeded,
        {
            name: DerivedStateName.syncingPermissionNeeded,
            renderOrder: 1,
            desiredConditions: [
                {
                    name: SystemStateName.syncingPermitted,
                    status: SystemStatus.initializing,
                },
            ],
        },
    ],
    [
        DerivedStateName.uploadingHomeDir,
        {
            name: DerivedStateName.uploadingHomeDir,
            renderOrder: 2,
            desiredConditions: [
                { name: SystemStateName.uploadingHomeDir, status: SystemStatus.complete },
            ],
        },
    ],
    [
        DerivedStateName.workspaceTooLarge,
        {
            name: DerivedStateName.workspaceTooLarge,
            renderOrder: 2,
            desiredConditions: [
                { name: SystemStateName.workspaceTooLarge, status: SystemStatus.complete },
            ],
        },
    ],
    [
        DerivedStateName.disableCopilot,
        {
            name: DerivedStateName.disableCopilot,
            renderOrder: 3,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.complete },
                {
                    name: SystemStateName.disabledGithubCopilot,
                    status: SystemStatus.incomplete,
                },
            ],
        },
    ],
    [
        DerivedStateName.disableCodeium,
        {
            name: DerivedStateName.disableCodeium,
            renderOrder: 3,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.complete },
                {
                    name: SystemStateName.disabledCodeium,
                    status: SystemStatus.incomplete,
                },
            ],
        },
    ],
    [
        DerivedStateName.workspaceNotSelected,
        {
            name: DerivedStateName.workspaceNotSelected,
            renderOrder: 5,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.complete },
                { name: SystemStateName.syncingPermitted, status: SystemStatus.complete },
                { name: SystemStateName.workspaceSelected, status: SystemStatus.incomplete },
            ],
        },
    ],
    [
        DerivedStateName.workspacePopulated,
        {
            name: DerivedStateName.workspacePopulated,
            renderOrder: 5,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.complete },
                { name: SystemStateName.syncingPermitted, status: SystemStatus.complete },
                { name: SystemStateName.workspaceSelected, status: SystemStatus.complete },
                { name: SystemStateName.workspacePopulated, status: SystemStatus.complete },
            ],
        },
    ],
    [
        DerivedStateName.workspaceNotPopulated,
        {
            name: DerivedStateName.workspaceNotPopulated,
            renderOrder: 5,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.complete },
                { name: SystemStateName.syncingPermitted, status: SystemStatus.complete },
                { name: SystemStateName.workspaceSelected, status: SystemStatus.complete },
                { name: SystemStateName.workspacePopulated, status: SystemStatus.incomplete },
            ],
        },
    ],
    [
        DerivedStateName.allActionsComplete,
        {
            name: DerivedStateName.allActionsComplete,
            renderOrder: 6,
            desiredConditions: [
                { name: SystemStateName.authenticated, status: SystemStatus.complete },
                { name: SystemStateName.syncingPermitted, status: SystemStatus.complete },
                {
                    name: SystemStateName.disabledGithubCopilot,
                    status: SystemStatus.complete,
                },
                {
                    name: SystemStateName.disabledCodeium,
                    status: SystemStatus.complete,
                },
                // { name: SystemStateName.hasMovedExtensionAside, status: SystemStatus.complete },
                { name: SystemStateName.workspacePopulated, status: SystemStatus.complete },
                { name: SystemStateName.workspaceSelected, status: SystemStatus.complete },
            ],
        },
    ],
]);

export class ActionsModel extends DisposableService {
    private _systemStates: Map<SystemStateName, SystemState>;
    private _derivedStates: Map<string, DerivedState>;
    private _onDerivedStatesSatisfied = new vscode.EventEmitter<DerivedState[]>();
    private _systemToDerivedStateMap = new Map<SystemStateName, Set<string>>();
    private _globalState: AugmentGlobalState;
    private _satisfiedDerivedStates: Set<string> = new Set();

    constructor(
        globalState: AugmentGlobalState,
        systemStates = initialSystemStates,
        derivedStates = initialDerivedStates
    ) {
        super();
        this.addDisposable(this._onDerivedStatesSatisfied);

        this._systemStates = new Map<SystemStateName, SystemState>(systemStates);
        this._derivedStates = new Map<string, DerivedState>(derivedStates);
        this._globalState = globalState;
        this.loadSystemStates();

        // Initialize the system to derived state map
        this._derivedStates.forEach(this.updateSystemToDerivedStateMap.bind(this));

        // Initialize satisfied derived states
        this._derivedStates.forEach((derivedState) => {
            if (this._isStateSatisfied(derivedState)) {
                this._satisfiedDerivedStates.add(derivedState.name);
            }
        });

        // Emit initial state
        this._emitSatisfiedStates();
    }

    get satisfiedStates(): DerivedState[] {
        return Array.from(this._satisfiedDerivedStates)
            .map((name) => this._derivedStates.get(name)!)
            .sort((a, b) => a.renderOrder - b.renderOrder);
    }

    private saveSystemStates() {
        let mySerialMap = JSON.stringify(Array.from(this._systemStates.entries()));
        void this._globalState.update(GlobalContextKey.actionSystemStates, mySerialMap);
    }

    private loadSystemStates() {
        const persistedSystemStatesString: string =
            this._globalState.get(GlobalContextKey.actionSystemStates) || "[]";
        const persistedSystemStates = new Map<SystemStateName, SystemState>(
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            JSON.parse(persistedSystemStatesString)
        );
        persistedSystemStates.forEach((systemState) => {
            this._systemStates.set(systemState.name, systemState);
        });
    }

    public get onDerivedStatesSatisfied(): vscode.Event<DerivedState[]> {
        return this._onDerivedStatesSatisfied.event;
    }

    private updateSystemToDerivedStateMap(derivedState: DerivedState) {
        derivedState.desiredConditions.forEach((condition) => {
            if (!this._systemToDerivedStateMap.has(condition.name)) {
                this._systemToDerivedStateMap.set(condition.name, new Set());
            }
            this._systemToDerivedStateMap.get(condition.name)!.add(derivedState.name);
        });
    }

    public setSystemStateStatus(systemStateName: SystemStateName, status: SystemStatus) {
        // Set the actual state in the system states map
        const systemState = { name: systemStateName, status: status };
        this._systemStates.set(systemState.name, systemState);

        // Persist the system state
        this.saveSystemStates();

        // Update the affected derived states
        const affectedDerivedStates =
            this._systemToDerivedStateMap.get(systemState.name) || new Set();
        let stateChanged = false;

        affectedDerivedStates.forEach((derivedStateName) => {
            const derivedState = this._derivedStates.get(derivedStateName);
            if (derivedState) {
                const wasSatisfied = this._satisfiedDerivedStates.has(derivedStateName);
                const isSatisfied = this._isStateSatisfied(derivedState);

                if (wasSatisfied !== isSatisfied) {
                    stateChanged = true;
                    if (isSatisfied) {
                        this._satisfiedDerivedStates.add(derivedStateName);
                    } else {
                        this._satisfiedDerivedStates.delete(derivedStateName);
                    }
                }
            }
        });

        if (stateChanged) {
            this._emitSatisfiedStates();
        }
        this.saveSystemStates();
    }

    // Purge all state and emit initial state. This happens when the user signs out or restarts the extension.
    public restartActionsState() {
        this._systemStates = new Map<SystemStateName, SystemState>(initialSystemStates);
        this._derivedStates = new Map<string, DerivedState>(initialDerivedStates);
        this._systemToDerivedStateMap = new Map<SystemStateName, Set<string>>();
        this._satisfiedDerivedStates = new Set();

        // Initialize the system to derived state map
        this._derivedStates.forEach(this.updateSystemToDerivedStateMap.bind(this));

        // Initialize satisfied derived states
        this._derivedStates.forEach((derivedState) => {
            if (this._isStateSatisfied(derivedState)) {
                this._satisfiedDerivedStates.add(derivedState.name);
            }
        });

        // Emit initial state
        this._emitSatisfiedStates();

        // Persist the system state
        this.saveSystemStates();
    }

    public getSystemState(name: SystemStateName): SystemState | undefined {
        return this._systemStates.get(name);
    }

    public addDerivedState(derivedState: DerivedState) {
        this._derivedStates.set(derivedState.name, derivedState);
        this.updateSystemToDerivedStateMap(derivedState);

        const isSatisfied = this._isStateSatisfied(derivedState);
        if (isSatisfied) {
            this._satisfiedDerivedStates.add(derivedState.name);
            this._emitSatisfiedStates();
        }

        this.saveSystemStates();
    }

    public isDerivedStateSatisfied(name: string): boolean {
        const state = this._derivedStates.get(name);
        if (!state) {
            return false;
        }
        return this._isStateSatisfied(state);
    }

    public isSystemStateComplete(name: SystemStateName): boolean {
        const state = this._systemStates.get(name);
        return state?.status === SystemStatus.complete;
    }

    private _emitSatisfiedStates() {
        this._onDerivedStatesSatisfied.fire(this.satisfiedStates);
    }

    public broadcastDerivedStates() {
        this._emitSatisfiedStates();
    }

    private _isStateSatisfied(derivedState: DerivedState): boolean {
        for (const condition of derivedState.desiredConditions) {
            const systemState = this._systemStates.get(condition.name);
            if (!systemState || systemState.status !== condition.status) {
                return false;
            }
        }
        return true;
    }
}
