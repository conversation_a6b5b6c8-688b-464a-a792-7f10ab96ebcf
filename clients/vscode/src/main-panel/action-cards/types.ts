// If you change this, please also update DerivedStateName.java
export enum DerivedStateName {
    disableCopilot = "ShouldDisableCopilot",
    disableCodeium = "ShouldDisableCodeium",
    workspacePopulated = "WorkspacePopulated",
    workspaceNotPopulated = "WorkspaceNotPopulated",
    workspaceNotSelected = "WorkspaceNotSelected",
    signInRequired = "UserShouldSignIn",
    allActionsComplete = "AllActionsComplete",
    syncingPermissionNeeded = "SyncingPermissionNeeded",
    workspaceTooLarge = "workspaceTooLarge",
    uploadingHomeDir = "uploadingHomeDir",
}

// If you change this, please also update UIOnlyActionName.java
export enum UIOnlyActionName {
    signInInProgress = "SignInInProgress",
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export type AnyActionName = DerivedStateName | UIOnlyActionName;

export type UserTier = "unknown" | "community" | "professional" | "enterprise";
