import throttle from "lodash/throttle";
import * as vscode from "vscode";
import { Webview } from "vscode";

import { APIServer } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { awaitingSyncingPermissionResponse } from "../../chat/chat-types";
import { FeatureFlagManager } from "../../feature-flags";
import { getLogger } from "../../logging";
import { ActionsModel, DerivedState } from "../../main-panel/action-cards/actions-model";
import { AnyActionName, DerivedStateName } from "../../main-panel/action-cards/types";
import { DisposableService } from "../../utils/disposable-service";
import { AsyncMsgHandler } from "../../utils/webviews/messaging";
import {
    MainPanelApp,
    UserTier,
    WebViewMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";
import { Chat<PERSON>ode<PERSON>Reply, ChatUserMessage } from "../../webview-providers/webview-messages";
import { SyncingEnabledTracker } from "../../workspace/syncing-enabled-tracker";
import { MainPanelAppController } from "../main-panel-app-controller";

export class AwaitingSyncingPermissionApp
    extends DisposableService
    implements MainPanelAppController
{
    private _logger = getLogger("AwaitingSyncingPermissionApp");
    private _webview: Webview | undefined;
    private _asyncMsgHandler: AsyncMsgHandler | undefined;
    /** True if we have checked the workspace's populated state at least once */
    private _workspaceChecked = false;
    /**
     * True if the webview has requested to switch to the chat app, but we are
     * waiting for the workspace populated state
     */
    private _waitingOnWorkspace = false;

    constructor(
        private readonly _actionsModel: ActionsModel,
        private readonly _apiServer: APIServer,
        private readonly _config: AugmentConfigListener,
        private readonly _syncingEnabledTracker: SyncingEnabledTracker,
        private readonly _changeWebviewAppEvent: vscode.EventEmitter<MainPanelApp>,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _userTier: UserTier
    ) {
        super();
        // Listen to new derived states from the action model's event emitter
        this.addDisposables(
            this._actionsModel.onDerivedStatesSatisfied(this.handleDerivedStateChange.bind(this)),
            this._syncingEnabledTracker.onDidChangeSyncingEnabled(
                this.sendSyncEnabledStatus.bind(this)
            ),
            this._actionsModel.onDerivedStatesSatisfied((derivedStates) => {
                for (const derivedState of derivedStates) {
                    if (
                        derivedState.name === DerivedStateName.workspacePopulated ||
                        derivedState.name === DerivedStateName.workspaceNotPopulated
                    ) {
                        this._workspaceChecked = true;
                        if (this._waitingOnWorkspace) {
                            // The webview has already requested to switch to the chat app,
                            // so we can now switch to the chat app immediately
                            this._waitingOnWorkspace = false;
                            this.changeToChatApp();
                        }
                    }
                }
            })
        );
    }

    appType(): MainPanelApp {
        return MainPanelApp.awaitingSyncingPermission;
    }

    title(): string {
        return "Syncing Permission Needed";
    }

    register(webview: Webview): void {
        this._logger.info("Registering AwaitingSyncingPermissionApp");
        this._webview = webview;

        this.addDisposable(
            webview.onDidReceiveMessage((msg: WebViewMessage) => this.onMessageFromWebview(msg))
        );
    }

    private sendSyncEnabledStatus = throttle(
        (): void => {
            void this._webview?.postMessage({
                type: WebViewMessageType.syncEnabledState,
                data: this._syncingEnabledTracker.syncingEnabledState,
            });
        },
        250 /* 250ms throttle */,
        { leading: true, trailing: true }
    );

    private async handleDerivedStateChange(derivedStates: DerivedState[]) {
        const filteredDerivedStates = new Set([
            DerivedStateName.syncingPermissionNeeded,
            DerivedStateName.uploadingHomeDir,
            DerivedStateName.workspaceTooLarge,
            DerivedStateName.workspaceNotSelected,
        ]);
        const actions = derivedStates
            .map((s) => s.name)
            .filter((s) => filteredDerivedStates.has(s));
        if (actions.length === 0) {
            if (!this._workspaceChecked) {
                this._waitingOnWorkspace = true;
            } else {
                this.changeToChatApp();
            }
            return;
        }
        await this.sendActionsToWebview(actions);
    }

    changeToChatApp() {
        this._changeWebviewAppEvent.fire(MainPanelApp.chat);
    }

    private async sendActionsToWebview(actions: Array<AnyActionName>) {
        await this._webview?.postMessage({
            type: WebViewMessageType.mainPanelActions,
            data: actions,
        });
    }

    private onMessageFromWebview(msg: WebViewMessage): void {
        switch (msg.type) {
            case WebViewMessageType.mainPanelPerformAction:
                this.performAction(msg.data);
                break;
            case WebViewMessageType.awaitingSyncingPermissionLoaded: {
                void this.handleDerivedStateChange(this._actionsModel.satisfiedStates);
                this.sendSyncEnabledStatus();
                void this._webview?.postMessage({
                    type: WebViewMessageType.awaitingSyncingPermissionInitialize,
                    data: {
                        // The name of the currently open workspace
                        workspaceName: vscode.workspace.name,
                        // Send the current state of debug features flag
                        enableDebugFeatures: this._config.config.enableDebugFeatures,
                        // Send max trackable file count flag, to display as an explanation in the action card
                        maxTrackableFileCount:
                            this._featureFlagManager.currentFlags.maxTrackableFileCount,
                        // Send the user tier, to display as an explanation in the action card
                        userTier: this._userTier,
                    },
                });
                break;
            }
        }
    }

    private performAction(action: string) {
        switch (action) {
            case "grant-sync-permission": {
                this._logger.info("User granted syncing permission");
                this._syncingEnabledTracker.enableSyncing();
                break;
            }
            case "open-folder": {
                void vscode.commands.executeCommand("vscode.openFolder");
                break;
            }
            case "close-folder": {
                void vscode.commands.executeCommand("workbench.action.closeFolder");
                break;
            }
        }
    }

    async *onUserSendMessage(_msg: ChatUserMessage): AsyncGenerator<ChatModelReply> {
        const requestId = this._apiServer.createRequestId();
        const message = awaitingSyncingPermissionResponse;

        for (const char of message) {
            yield {
                type: WebViewMessageType.chatModelReply,
                data: {
                    text: char,
                    requestId: requestId,
                    workspaceFileChunks: [],
                    streaming: true, // Indicate that this is a streaming response
                },
            };

            // Simulate a delay between characters (adjust as needed)
            await new Promise((resolve) => setTimeout(resolve, 8));
        }

        // Send a final message to indicate the end of the stream
        yield {
            type: WebViewMessageType.chatModelReply,
            data: {
                text: "",
                requestId: requestId,
                workspaceFileChunks: [],
                streaming: false, // Indicate that this is the final part of the response
            },
        };
    }
}
