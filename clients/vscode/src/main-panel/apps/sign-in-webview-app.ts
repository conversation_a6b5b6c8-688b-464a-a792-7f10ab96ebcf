import { Webview } from "vscode";

import { APIServer } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { OAuthFlow } from "../../auth/oauth-flow";
import { augmentSignInURI, signInChatResponse } from "../../chat/chat-types";
import { AugmentLogger, getLogger } from "../../logging";
import { DisposableService } from "../../utils/disposable-service";
import { createAsyncMsgHandlerFromWebview } from "../../utils/webviews/messaging-helper";
import {
    ChatModelReply,
    ChatUserMessage,
    MainPanelApp,
    SignInLoadedResponse,
    WebViewMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";
import { ActionsModel, DerivedState } from "../action-cards/actions-model";
import { AnyActionName, DerivedStateName, UIOnlyActionName } from "../action-cards/types";
import { MainPanelAppController } from "../main-panel-app-controller";

export class SignInApp extends DisposableService implements MainPanelAppController {
    private _logger: AugmentLogger = getLogger("SignInApp");
    private _webview: Webview | undefined;
    private _signInFlow: Promise<void> | undefined;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _config: AugmentConfigListener,
        private readonly _oauthFlow: OAuthFlow,
        private readonly _actionsModel: ActionsModel
    ) {
        super();
    }

    appType(): MainPanelApp {
        return MainPanelApp.signIn;
    }

    title(): string {
        return "";
    }

    /**
     * Registers message handlers for the Sign In webview.
     * This method is crucial in setting up the communication between the webview and the extension.
     *
     * @param webview The webview to register handlers for
     */
    register(webview: Webview): void {
        this._webview = webview;

        this.addDisposable(
            webview.onDidReceiveMessage((msg: WebViewMessage) => this.onMessageFromWebview(msg))
        );
        this.addDisposable(
            this._actionsModel.onDerivedStatesSatisfied(this.handleDerivedStateChange.bind(this))
        );

        // Create an async message handler for the webview
        const asyncMsgHandler = createAsyncMsgHandlerFromWebview(webview);
        this.addDisposable(asyncMsgHandler);

        // Register a stream handler for user messages
        // This allows the extension to process user input from the chat interface
        asyncMsgHandler.registerStreamHandler(
            WebViewMessageType.chatUserMessage,
            (msg: ChatUserMessage) => this.onUserSendMessage(msg)
        );

        // Register a handler for when the chat is loaded
        // This is where initial configuration and feature flags are sent to the webview
        asyncMsgHandler.registerHandler(WebViewMessageType.chatLoaded, (_) => {
            if (this._actionsModel.isDerivedStateSatisfied("UserShouldSignIn")) {
                void this.sendSignInActions();
            }

            return Promise.resolve({
                type: WebViewMessageType.chatInitialize,
                data: {
                    // Send the current state of debug features flag
                    enableDebugFeatures: this._config.config.enableDebugFeatures,
                    // Set fullFeatured to false for the sign-in view
                    fullFeatured: false,
                },
            });
        });
    }

    private onMessageFromWebview(msg: WebViewMessage): void {
        switch (msg.type) {
            case WebViewMessageType.mainPanelPerformAction:
                void this.performAction(msg.data);
                break;
            case WebViewMessageType.augmentLink: {
                switch (msg.data) {
                    case augmentSignInURI:
                        void this.performAction("sign-in");
                        break;
                    default:
                        this._logger.warn(`Unknown augment link: ${msg.data}`);
                        break;
                }
                break;
            }
            case WebViewMessageType.signInLoaded: {
                const msg: SignInLoadedResponse = {
                    type: WebViewMessageType.signInLoadedResponse,
                    data: {
                        client: "vscode",
                    },
                };
                void this._webview?.postMessage(msg);
                break;
            }
        }
    }

    private performAction(action: string) {
        switch (action) {
            case "cancel-sign-in":
                this._oauthFlow.doProgrammaticCancellation();
                void this.sendSignInActions();
                break;
            case "sign-in": {
                void this.sendInProgressActions();
                const signinFlow = this._oauthFlow.startFlow(false);
                this._signInFlow = signinFlow;
                signinFlow.catch((_err) => {
                    if (this._signInFlow === signinFlow) {
                        // If the promise is not the same then we've
                        // triggered a different sign in flow, so stay
                        // showing the cancelled UI.
                        void this.sendSignInActions();
                    }
                });
                break;
            }
        }
    }

    private async sendSignInActions() {
        await this.sendActionsToWebview([DerivedStateName.signInRequired]);
    }
    private async sendInProgressActions() {
        await this.sendActionsToWebview([UIOnlyActionName.signInInProgress]);
    }

    private handleDerivedStateChange(derivedStates: DerivedState[]) {
        for (const derivedState of derivedStates) {
            if (derivedState.name === DerivedStateName.signInRequired) {
                void this.sendSignInActions();
            }
        }
    }
    private async sendActionsToWebview(actions: Array<AnyActionName>) {
        await this._webview?.postMessage({
            type: WebViewMessageType.mainPanelActions,
            data: actions,
        });
    }

    /**
     * Note on Feature Flags and Chat Lifecycle:
     *
     * 1. Feature flags (like enableDebugFeatures) are initially set in the extension's
     *    configuration, typically managed in `clients/vscode/src/feature-flags.ts`.
     *
     * 2. When the chat webview is loaded, it triggers the ChatLoaded message.
     *
     * 3. This handler responds with a ChatInitialize message, which includes the current
     *    state of feature flags.
     *
     * 4. The webview (implemented in files like `clients/common/webviews/src/apps/chat/Chat.svelte`)
     *    uses these flags to determine which features to display or enable.
     *
     * 5. If feature flags change during runtime, the extension would need to send an update
     *    message to the webview to reflect these changes. This is not handled in this file.
     *
     * 6. The `fullFeatured` flag is set to false here because this is the sign-in view,
     *    which supported a limited set of messages and we limit the user interface
     *    compared to the full chat experience.
     *
     * Related components:
     * - Feature flag management: `clients/vscode/src/feature-flags.ts`
     * - Main chat interface: `clients/common/webviews/src/apps/chat/Chat.svelte`
     * - Webview message types: `clients/vscode/src/webview-providers/webview-messages.ts`
     */

    async *onUserSendMessage(_msg: ChatUserMessage): AsyncGenerator<ChatModelReply> {
        const requestId = this._apiServer.createRequestId();
        const message = signInChatResponse;

        for (const char of message) {
            yield {
                type: WebViewMessageType.chatModelReply,
                data: {
                    text: char,
                    requestId: requestId,
                    workspaceFileChunks: [],
                    streaming: true, // Indicate that this is a streaming response
                },
            };

            // Simulate a delay between characters (adjust as needed)
            await new Promise((resolve) => setTimeout(resolve, 8));
        }

        // Send a final message to indicate the end of the stream
        yield {
            type: WebViewMessageType.chatModelReply,
            data: {
                text: "",
                requestId: requestId,
                workspaceFileChunks: [],
                streaming: false, // Indicate that this is the final part of the response
            },
        };
    }
}
