import { OnboardingSessionEventReporter } from "$vscode/src/metrics/onboarding-session-event-reporter";
import { OnboardingSessionEventName } from "$vscode/src/onboarding/onboarding-types";
import { getUserDocumentsDirectory } from "$vscode/src/utils/environment";
import { directoryExistsAsync, makeDirs } from "$vscode/src/utils/fs-utils";

import { Webview } from "vscode";
import * as vscode from "vscode";

import { DisposableService } from "../../utils/disposable-service";
import {
    MainPanelApp,
    WebViewMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";
import { MainPanelAppController } from "../main-panel-app-controller";

export class FolderSelectionApp extends DisposableService implements MainPanelAppController {
    private _webview: vscode.Webview | undefined;

    constructor(private _onboardingSessionEventReporter: OnboardingSessionEventReporter) {
        super();
    }

    appType(): MainPanelApp {
        return MainPanelApp.folderSelection;
    }

    title(): string {
        return "Select Folder";
    }

    register(webview: Webview): void {
        this._webview = webview;

        this.addDisposables(this._webview.onDidReceiveMessage(this.onDidReceiveMessage));
    }

    private onDidReceiveMessage = (message: WebViewMessage) => {
        switch (message.type) {
            case WebViewMessageType.mainPanelPerformAction:
                void this.performAction(message.data);
                break;
            case WebViewMessageType.mainPanelCreateProject:
                void this.createProject(message.data.name);
                break;
        }
    };

    private performAction(action: string) {
        switch (action) {
            case "open-folder": {
                this._onboardingSessionEventReporter.reportEvent(
                    OnboardingSessionEventName.OpenedFolder
                );
                void vscode.commands.executeCommand("vscode.openFolder");
                break;
            }
            case "close-folder": {
                void vscode.commands.executeCommand("workbench.action.closeFolder");
                break;
            }
            case "clone-repository": {
                this._onboardingSessionEventReporter.reportEvent(
                    OnboardingSessionEventName.ClonedRepo
                );
                void vscode.commands.executeCommand("git.clone");
                break;
            }
        }
    }

    private async createProject(projectName: string) {
        const AUGMENT_DIR = "augment-projects";
        const documentsDir = getUserDocumentsDirectory();
        if (!documentsDir) {
            // TODO
            return;
        }
        const documentsUri = vscode.Uri.file(documentsDir);
        const augmentDirectory = vscode.Uri.joinPath(documentsUri, AUGMENT_DIR);
        const projectDirectory = vscode.Uri.joinPath(augmentDirectory, projectName);
        if (!(await directoryExistsAsync(projectDirectory.fsPath))) {
            await makeDirs(projectDirectory.fsPath);
        }

        this._onboardingSessionEventReporter.reportEvent(
            OnboardingSessionEventName.CreatedNewProject
        );
        await this._onboardingSessionEventReporter.uploadNow();

        void vscode.commands.executeCommand("vscode.openFolder", projectDirectory);
    }
}
