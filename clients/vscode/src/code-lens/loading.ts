import * as vscode from "vscode";

export class LoadingCodeLensProvider implements vscode.CodeLensProvider {
    private _uri: vscode.Uri | undefined;
    private _range: vscode.Range | undefined;

    private _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
    public readonly onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;

    constructor(
        public title: string = "$(loading~spin) Loading...",
        private _tooltip?: string
    ) {}

    public get isLoading() {
        return !!(this._uri && this._range);
    }

    public refresh() {
        this._onDidChangeCodeLenses.fire();
    }

    public startLoading(uri: vscode.Uri, range: vscode.Range) {
        this._uri = uri;
        this._range = range;
        this._onDidChangeCodeLenses.fire();
    }

    public stopLoading() {
        this._uri = undefined;
        this._range = undefined;
        this._onDidChangeCodeLenses.fire();
    }

    provideCodeLenses(
        document: vscode.TextDocument,
        _token: vscode.CancellationToken
    ): vscode.ProviderResult<vscode.CodeLens[]> {
        if (!this._range || document.uri !== this._uri) {
            return [];
        }

        const codeLenses: vscode.CodeLens[] = [];
        codeLenses.push(
            new vscode.CodeLens(this._range, {
                title: this.title,
                command: "",
                tooltip: this._tooltip,
            })
        );
        return codeLenses;
    }
}
