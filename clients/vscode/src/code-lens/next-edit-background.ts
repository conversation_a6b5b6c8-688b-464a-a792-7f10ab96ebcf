import { Observable } from "@augment-internal/sidecar-libs/src/utils/observable";
import * as vscode from "vscode";

import {
    NextEditBackgroundAcceptCommand,
    NextEditBackgroundRejectCommand,
    NextEditUndoAcceptSuggestionCommand,
} from "../commands/next-edit";
import {
    AfterPreview,
    Animating,
    BackgroundState,
    BeforePreview,
} from "../next-edit/background-state";
import { NextEditSessionEventSource } from "../next-edit/next-edit-types";
import { EditSuggestion } from "../next-edit/suggestion-manager";
import { getKeybindingForCommand } from "../next-edit/utils";
import { KeybindingWatcher } from "../utils/keybindings";

export class NextEditBackgroundCodeLensProvider implements vscode.CodeLensProvider {
    private _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
    public readonly onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;

    constructor(
        private _state: Observable<BackgroundState>,
        private _keybindingWatcher: KeybindingWatcher
    ) {}

    private get _activeSuggestion(): EditSuggestion | undefined {
        return this._state.value instanceof BeforePreview ||
            this._state.value instanceof AfterPreview ||
            this._state.value instanceof Animating
            ? this._state.value.suggestion
            : undefined;
    }

    public refresh() {
        this._onDidChangeCodeLenses.fire();
    }

    private _buildCodeLens(title: string, commandId: string, codeLensLocation: vscode.Range) {
        return new vscode.CodeLens(codeLensLocation, {
            title,
            command: commandId,
            tooltip: this._getKeybindingForCommand(commandId, true) ?? undefined,
            arguments: [NextEditSessionEventSource.CodeLens],
        });
    }

    private _getKeybindingForCommand(commandId: string, prettyFormat: boolean = false) {
        return getKeybindingForCommand(this._keybindingWatcher, commandId, prettyFormat)?.replace(
            "Escape",
            "esc"
        );
    }

    provideCodeLenses(
        document: vscode.TextDocument,
        _token: vscode.CancellationToken
    ): vscode.ProviderResult<vscode.CodeLens[]> {
        const activeSuggestion = this._activeSuggestion;
        if (!activeSuggestion) {
            return [];
        }
        if (!activeSuggestion.qualifiedPathName.equals(document.uri)) {
            return [];
        }

        const codeLenses: vscode.CodeLens[] = [];
        const codeLensLocation = new vscode.Range(
            new vscode.Position(activeSuggestion.lineRange.start, 0),
            new vscode.Position(activeSuggestion.lineRange.start, 0)
        );

        codeLenses.push(
            new vscode.CodeLens(codeLensLocation, {
                title: `Augment Next Edit: ${activeSuggestion.result.changeDescription}`,
                command: "",
            })
        );

        // Note that we show "undo" even while we are in the process of animating because codelens
        // is laggy and we don't want to first show "Apply" only to immediately change it to "Undo"
        // and then have the visible lag.
        if (this._state.value instanceof AfterPreview || this._state.value instanceof Animating) {
            codeLenses.push(
                this._buildCodeLens(
                    "Undo",
                    NextEditUndoAcceptSuggestionCommand.commandID,
                    codeLensLocation
                )
            );
        } else {
            codeLenses.push(
                this._buildCodeLens(
                    "Apply",
                    NextEditBackgroundAcceptCommand.commandID,
                    codeLensLocation
                )
            );
        }

        codeLenses.push(
            this._buildCodeLens(
                "Reject",
                NextEditBackgroundRejectCommand.commandID,
                codeLensLocation
            )
        );

        return codeLenses;
    }
}
