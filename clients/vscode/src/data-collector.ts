/* eslint-disable @typescript-eslint/naming-convention */
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { APIServer } from "./augment-api";
import type { AugmentInstruction } from "./code-edit-types";
import { CompletionRequest } from "./completions/completions-model";
import { RecentCompletions } from "./completions/recent-completions";
import { type AugmentLogger, getLogger } from "./logging";
import { NextEditResultInfo } from "./next-edit/next-edit-types";
import { sha256 } from "./sha";
import { DisposableService } from "./utils/disposable-service";
import { CharRange, Range } from "./utils/ranges";
import { RecentItems } from "./utils/recent-items";

// The global start and end (exclusive) character locations of the range in the file.
// needs to match the field names in the request_insight.Range proto
export type GlobalCharRange = {
    start: number;
    end: number;
};

// VSCode reports text edits in different formats depending on the edit.
// Here we unify, and also use global char position instead of line + colum.
// Mirrors the ContentChange in request_insight.proto
export type CustomTextDocumentContentChangeEvent = {
    readonly text: string;
    readonly range: GlobalCharRange;
};

// Mirrors TextEditEvent in request_insight.proto
export type onDidChangeTextDocumentContent = {
    reason: vscode.TextDocumentChangeReason | undefined;
    content_changes: readonly CustomTextDocumentContentChangeEvent[];
    /** hash of simple concatenation of text in hash_char_ranges */
    after_changes_hash: string;
    /** the char ranges where the text to hash comes from */
    hash_char_ranges: readonly GlobalCharRange[];

    source_folder_root: string;
    /** the doc length in characters after the changes are applied */
    after_doc_length: number;
};

// Event type when a completion request is made.
export type CompletionRequestIdIssued = {
    request_id: string;
};

// Event type when an edit request is made.
export type EditRequestIdIssued = {
    request_id: string;
};

// Event type when a next edit request is made.
export type NextEditRequestIdIssued = {
    request_id: string;
};

// Batch of user events to be uploaded.
export type UserEventBatch = {
    user_events: UserEvent[];
};

export type UserEvent = {
    // Timestamp of when we were informed of the event.
    time: string;

    // File path of the file the event is associated with.
    file_path: string;

    // Optional event contents, present if event is text edit.
    text_edit?: onDidChangeTextDocumentContent;

    // Optional completion request id, present if event is completion request.
    completion_request_id_issued?: CompletionRequestIdIssued;

    // Optional edit request id, present if event is edit request.
    edit_request_id_issued?: EditRequestIdIssued;

    // Optional edit request id, present if event is next edit request.
    next_edit_request_id_issued?: NextEditRequestIdIssued;
};

type UploadBatchFunction = (batch: UserEventBatch) => void;

// Function to convert TextDocumentContentChangeEvent to CustomTextDocumentContentChangeEvent.
export function toCustomTextDocumentContentChangeEvent(
    document: vscode.TextDocument,
    event: vscode.TextDocumentContentChangeEvent
): CustomTextDocumentContentChangeEvent {
    // Set text to empty string if no text in event.
    const event_text = event.text === undefined ? "" : event.text;

    // Construct global char range from VSCode's range or range_offset + length, whichever is available.
    let globalCharRange: GlobalCharRange;
    // If VSCode range offset and length are available, use them.
    if (event.rangeOffset !== undefined && event.rangeLength !== undefined) {
        globalCharRange = { start: event.rangeOffset, end: event.rangeOffset + event.rangeLength };
    } else {
        globalCharRange = {
            start: document.offsetAt(event.range.start),
            end: document.offsetAt(event.range.end),
        };
    }

    return { text: event_text, range: globalCharRange };
}

// Queue of user events to be uploaded. Uploads if events in queue exceed batch size,
// or if at least one event has been in the queue for more than upload period.
class UploadQueue implements vscode.Disposable {
    private _queue: UserEvent[] = [];
    private _queueNonEmptyTimer: NodeJS.Timer | undefined;

    constructor(
        private _uploadBatchFunction: UploadBatchFunction,
        private _batchSize: number,
        private _maxUploadDelayMs: number
    ) {}

    public add(event: UserEvent): void {
        this._queue.push(event);
        // If the queue was empty (length now 1), start the timer.
        if (this._queue.length === 1) {
            clearTimeout(this._queueNonEmptyTimer);
            this._queueNonEmptyTimer = setTimeout(() => {
                this._uploadBatch();
            }, this._maxUploadDelayMs);
        }
        if (this._queue.length >= this._batchSize) {
            const batchEvents = this._queue;
            this._queue = [];
            const batch: UserEventBatch = {
                user_events: batchEvents,
            };
            this._uploadBatchFunction(batch);
        }
    }

    private _uploadBatch(): void {
        // Upload the queue up to the batch size.
        const batchEvents = this._queue;
        this._queue = [];
        const uploadSize = batchEvents.length;
        const batch: UserEventBatch = {
            user_events: batchEvents,
        };
        if (uploadSize !== 0) {
            this._uploadBatchFunction(batch);
        }
        // Reset the timer.
        clearTimeout(this._queueNonEmptyTimer);
    }

    public dispose(): void {
        this.stop();
    }

    public stop(): void {
        clearTimeout(this._queueNonEmptyTimer);
    }
}

// This class is responsible for handling the actual upload to the API server.
class UploadHandler {
    private _logger: AugmentLogger;
    private _lastErrorTime: number | undefined;
    private _uploadInterruptPeriodMs: number = 15000;

    constructor(private _apiServer: APIServer) {
        // Start date
        this._logger = getLogger("UploadHandler");
    }

    public async uploadUserEvents(userEventBatch: UserEventBatch) {
        try {
            // Upload the user events unless we have recently seen a server error.
            const recentError: boolean = this._lastErrorTime
                ? Date.now() - this._lastErrorTime < this._uploadInterruptPeriodMs
                : false;
            if (recentError) {
                return;
            }
            await this._apiServer.uploadUserEvents(userEventBatch);
        } catch (e: any) {
            // For now we just log the error and do not retry.
            // Will add retry logic as feature is more mature.
            // Server does not like request, don't try again for a period of time.
            this._logger.info("Error uploading tracked events", e);
            this._lastErrorTime = Date.now();
            throw e;
        }
    }
}

export interface PathResolver {
    resolvePathName(absPath: string): QualifiedPathName | undefined;
}

// Collects usage data from the extension and sends it to the back end. ONLY for subset
// of users that have explicitly consented to the collection.
export class DataCollector extends DisposableService {
    /**
     * DataCollector collects and uploads user events in the background.
     * The manager subscribes to a variety of VsCode events and adds them to an
     * upload queue. The queue is uploaded when the queue has enough
     * events to fill a batch or when the upload period elapses.
     */

    public static defaultUploadBatchSize = 128;
    public static defaultMaxUploadDelayMs = 5000;

    private _uploadQueue: UploadQueue;
    private _uploadHandler: UploadHandler;

    constructor(
        apiServer: APIServer,
        private _pathResolver: PathResolver,
        private _recentInstructions: RecentItems<AugmentInstruction>,
        private _recentCompletions: RecentCompletions,
        private _recentNextEditSuggestions: RecentItems<NextEditResultInfo>,
        uploadBatchSize: number = DataCollector.defaultUploadBatchSize,
        maxUploadDelayMs: number = DataCollector.defaultMaxUploadDelayMs
    ) {
        super();
        this._uploadHandler = new UploadHandler(apiServer);
        this._uploadQueue = new UploadQueue(
            (batch: UserEventBatch) =>
                void this._uploadHandler.uploadUserEvents.bind(this._uploadHandler)(batch),
            uploadBatchSize,
            maxUploadDelayMs
        );
        this._createSubscriptions();
        this.addDisposable(this._uploadQueue);
    }

    private _createSubscriptions(): void {
        // Track VSCode events.
        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument(this._processChangeTextDocument.bind(this))
        );

        // Track Augment events.
        this.addDisposable(
            this._recentInstructions.onNewItems(this._processInstruction.bind(this))
        );

        this.addDisposable(this._recentCompletions.onNewItems(this._processCompletion.bind(this)));
        this.addDisposable(
            this._recentNextEditSuggestions.onNewItems(this._processNextEdit.bind(this))
        );
    }

    private _processChangeTextDocument(event: vscode.TextDocumentChangeEvent) {
        const absPath = event.document.uri.fsPath;
        const timeStampISO = new Date().toISOString();
        // Only track edit events relevant to the extension.
        const qualifiedPathName = this._pathResolver.resolvePathName(absPath);
        if (qualifiedPathName === undefined) {
            return;
        }
        // Only track edits with non-empty changes.
        if (event.contentChanges.length === 0) {
            return;
        }

        // Construct tracked event
        // Convert to CustomTextDocumentContentChangeEvent.
        const contentChanges: CustomTextDocumentContentChangeEvent[] = [];
        for (const change of event.contentChanges) {
            contentChanges.push(toCustomTextDocumentContentChangeEvent(event.document, change));
        }

        const numExtraCharsToHashOnEachSide: number = 500;
        const documentLength: number = event.document.getText().length;

        const expandedRanges: Range[] = contentChanges
            .map((change) => new CharRange(change.range.start, change.range.end))
            .map((charRange) =>
                charRange.offset(
                    -numExtraCharsToHashOnEachSide,
                    numExtraCharsToHashOnEachSide,
                    0,
                    documentLength
                )
            );

        const mergedRanges: Range[] = Range.mergeTouching(expandedRanges);

        const textToHash: string[] = mergedRanges.map((range) =>
            event.document.getText(
                new vscode.Range(
                    event.document.positionAt(range.start),
                    event.document.positionAt(range.stop)
                )
            )
        );

        const mergedHash: string = sha256(new TextEncoder().encode(textToHash.join("")));

        const eventContent: onDidChangeTextDocumentContent = {
            reason: event.reason,
            content_changes: contentChanges,
            after_changes_hash: mergedHash,
            source_folder_root: qualifiedPathName.rootPath,
            hash_char_ranges: mergedRanges.map((range) => ({
                start: range.start,
                end: range.stop, // need the field name to match the request_insight.Range proto
            })),
            after_doc_length: documentLength,
        };
        const userEvent: UserEvent = {
            time: timeStampISO,
            file_path: qualifiedPathName.relPath,
            text_edit: eventContent,
        };
        this._uploadQueue.add(userEvent);
    }

    private _processInstruction(instruction: AugmentInstruction) {
        const editRequestIdIssued: EditRequestIdIssued = {
            request_id: instruction.requestId,
        };
        const userEvent: UserEvent = {
            time: instruction.requestedAt.toISOString(),
            file_path: instruction.pathName,
            edit_request_id_issued: editRequestIdIssued,
        };
        this._uploadQueue.add(userEvent);
    }

    private _processNextEdit(request: NextEditResultInfo) {
        const nextEditRequestIdIssued: NextEditRequestIdIssued = {
            request_id: request.requestId,
        };
        const userEvent: UserEvent = {
            time: request.requestTime.toISOString(),
            file_path: request.qualifiedPathName?.relPath ?? "",
            next_edit_request_id_issued: nextEditRequestIdIssued,
        };
        this._uploadQueue.add(userEvent);
    }

    private _processCompletion(request: CompletionRequest) {
        const userEvent: UserEvent = {
            time: request.occuredAt.toISOString(),
            file_path: request.pathName ?? "",
            completion_request_id_issued: {
                request_id: request.requestId,
            },
        };
        this._uploadQueue.add(userEvent);
    }
}
