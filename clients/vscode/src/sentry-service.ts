import { AnalyticsManager } from "./analytics/analytics-manager";
import { AuthSessionStore } from "./auth/auth-session-store";
import { FeatureFlagManager } from "./feature-flags";
import { getLogger } from "./logging";
import { getExtensionVersion, isDebugExtension } from "./utils/environment";

export interface SentryWebviewConfig {
    enabled: boolean;
    dsn: string;
    release: string;
    environment: string;
    errorSampleRate: number;
    tracesSampleRate: number;
    replaysSessionSampleRate: number;
    replaysOnErrorSampleRate: number;
    sendDefaultPii: boolean;
    tags: Record<string, string>;
}

export class SentryService {
    private static instance: SentryService | undefined;
    private readonly logger = getLogger("SentryService");

    private constructor(
        private readonly featureFlagManager: FeatureFlagManager,
        private readonly authSessionStore: AuthSessionStore
    ) {}

    public static getInstance(
        featureFlagManager: FeatureFlagManager,
        authSessionStore: AuthSessionStore
    ): SentryService {
        if (!SentryService.instance) {
            SentryService.instance = new SentryService(featureFlagManager, authSessionStore);
        }
        return SentryService.instance;
    }

    // Clamp a sampling rate to the inclusive range [0, 1]
    private clampRate(rate: number | undefined): number {
        if (typeof rate !== "number" || Number.isNaN(rate)) return 0.0;
        if (rate < 0.0) return 0.0;
        if (rate > 1.0) return 1.0;
        return rate;
    }

    public async createSentryConfigForWebview(): Promise<string> {
        let flags;
        try {
            flags = this.featureFlagManager.currentFlags;
        } catch (error) {
            flags = undefined;
        }

        if (!flags?.enableSentry) {
            this.logger.info("Sentry is disabled via feature flag");
            return JSON.stringify({ enabled: false });
        }

        const tenantVersion = await this.getTenantVersion();
        const extensionVersion = getExtensionVersion();

        // Do not enable Sentry for developer/debug builds
        if (isDebugExtension(extensionVersion)) {
            this.logger.info("Sentry is disabled for debug/dev extension builds");
            return JSON.stringify({ enabled: false });
        }

        const environment = this.getEnvironment(tenantVersion, extensionVersion);
        const config: SentryWebviewConfig = {
            enabled: true,
            // VSCode webview-specific DSN (different from the main extension DSN)
            dsn: "https://<EMAIL>/4509753348718592",
            release: `vscode-webview@${extensionVersion}`,
            environment: environment,
            errorSampleRate: this.clampRate(flags?.webviewErrorSamplingRate),
            tracesSampleRate: this.clampRate(flags?.webviewTraceSamplingRate),
            replaysSessionSampleRate: 0.0, // Replay is disabled for webviews
            replaysOnErrorSampleRate: 0.0, // Replay is disabled for webviews
            sendDefaultPii: false,
            tags: {
                webview: "true",
                extensionVersion,
                tenantVersion,
            },
        };

        return JSON.stringify(config);
    }

    /**
     * Get the tenant version (tenant ID) from the current session.
     * @returns The tenant ID or "unknown" if not available.
     */
    private async getTenantVersion(): Promise<string> {
        try {
            const session = await this.authSessionStore.getSession();
            if (session && session.tenantURL) {
                const tenantId = AnalyticsManager.extractTenantIdFromUrl(session.tenantURL);
                return tenantId || "unknown";
            }
        } catch (error) {
            this.logger.warn("Failed to get tenant version:", error);
        }
        return "unknown";
    }

    private getEnvironment(tenantVersion: string, extensionVersion: string): string {
        // First check if we're in staging based on tenant URL
        if (tenantVersion.includes("staging")) {
            return "staging";
        }

        // Fallback to extension version-based detection
        if (extensionVersion.endsWith(".0")) {
            return "prerelease";
        }
        return "production";
    }
}
