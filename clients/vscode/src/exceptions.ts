/**
 * ExtensionDisabled in an Error that reports the disabling of the Augment extension.
 */
export class ExtensionDisabled extends Error {
    constructor() {
        super("Augment extension has been disabled");
    }
}

/**
 * ShutdownError is an Error that reports that an operation was interrupted because
 * a component is shutting down.
 */
export class ShutdownError extends Error {
    constructor(component: string) {
        super(`${component} is shutting down`);
    }
}

/**
 * UnknownModelError in an Error that reports the configured model is not available
 */
export class UnknownModelError extends Error {
    constructor(public modelName: string) {
        super(`Configured model "${modelName}" is not available`);
    }
}

/**
 * UnknownModelError in an Error that reports the configured model is not available
 */
export class NoDefaultModelError extends UnknownModelError {
    constructor() {
        super("<default>");
    }
}

/**
 * NoModelsError in an Error that reports the configured model is not available
 */
export class NoModelsError extends Error {
    constructor() {
        super("No models available");
    }
}

/**
 * SkipCompletion is thrown when we intend to not provide a completion.
 */
export class SkipCompletion extends Error {
    constructor(message: string = "Skipping inline completion.") {
        super(message);
    }
}
