{"compilerOptions": {"module": "es2022", "moduleResolution": "node", "experimentalDecorators": true, "noImplicitReturns": true, "noImplicitOverride": true, "noUnusedLocals": true, "allowUnreachableCode": false, "strict": true, "exactOptionalPropertyTypes": false, "useUnknownInCatchVariables": false, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"vs/*": ["./vs/*"]}, "target": "es2022", "useDefineForClassFields": false, "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker.ImportScripts"], "allowSyntheticDefaultImports": true}}