import { setLibraryLogger } from "@augment-internal/sidecar-libs/src/logging";
import { createLogger, format, Logger, transports } from "winston";

const logger = createLogger({
    exitOnError: false,
    format: format.combine(
        format.printf((info) => `[${info.level}] '${info.prefix}': ${info.message}`)
    ),
    transports: [
        new transports.Console({
            level: process.env.LOG_LEVEL?.toLowerCase() === "verbose" ? "debug" : "silent",
        }),
    ],
});

// Initialize the sidecar library's logger
setLibraryLogger(logger);

export function getLogger(prefix: string): Logger {
    return logger.child({ prefix });
}
