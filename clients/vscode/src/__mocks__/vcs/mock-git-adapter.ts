import { ShowArgs } from "../../vcs/git-adapter";
import { GitAdapter } from "../../vcs/git-adapter";

class MockGitAdapter implements GitAdapter {
    private params: Record<string, any> = {};

    withParams(paramName: string, paramValue: string) {
        this.params[paramName] = paramValue;
        return this;
    }

    withSymbolicRef(name: string) {
        return this.withParams("symbolicRef", name);
    }

    withDiff(diff: string) {
        return this.withParams("diff", diff);
    }

    withLsFiles(lsFiles: string) {
        return this.withParams("lsFiles", lsFiles);
    }

    withLog(log: string) {
        return this.withParams("log", log);
    }
    withShow(object: string, result: string) {
        const showParams = this.params["show"] ?? {};
        return this.withParams("show", { ...showParams, [object]: result });
    }

    withVersion(version: string) {
        return this.withParams("version", version);
    }

    symbolicRef = jest.fn(() => {
        return this.params["symbolicRef"];
    });

    log = jest.fn(() => {
        return this.params["log"];
    });

    show = jest.fn((showArgs: ShowArgs) => {
        if (!showArgs.object) {
            return undefined;
        }
        if (!this.params["show"]) {
            throw new Error(`No mock result for [${showArgs.object}]`);
        }
        return this.params["show"][showArgs.object];
    });

    diff = jest.fn(() => {
        return this.params["diff"];
    });

    lsFiles = jest.fn(() => {
        return this.params["lsFiles"];
    });

    version = jest.fn().mockResolvedValue(() => {
        return this.params["version"];
    });

    reset() {
        this.params = {};
    }
}

export { MockGitAdapter };
