import { FolderSyncingPermission } from "../../workspace/workspace-types";

// SimpleMockSyncingPermissionTracker is a mock syncing permission tracker that always grants
// syncing permission. It is intented for tests of WorkspaceManager that don't care about (and
// don't want to be bothered with) syncing permissions.
export class SimpleMockSyncingPermissionTracker {
    public dispose() {}

    public get syncingPermissionDenied(): boolean {
        return false;
    }

    public getFolderSyncingPermission(_sourceFolder: string): FolderSyncingPermission {
        return FolderSyncingPermission.granted;
    }

    public setDefaultPermissions(_sourceFolders: Array<string>): void {}

    public setPermittedFolders(_sourceFolders: Array<string>): void {}

    public addPermittedFolder(_sourceFolder: string): void {}

    public addImplicitlyPermittedFolder(_sourceFolder: string): void {}

    public dropPermission(_sourceFolders: Array<string>): void {}

    public dropStaleFolders(_sourceFolders: Array<string>): void {}

    public denyPermission(): void {
        throw new Error("Method not implemented.");
    }

    public async persistCurrentPermission(): Promise<void> {}
}

// MockSyncingPermissionTracker is a mock implementation of SyncingPermissionTracker that tries
// to mimic the behavior of the real tracker. It can be used to test WorkspaceManager's
// syncing permission functionality.
export class MockSyncingPermissionTracker {
    private _permittedFolders = new Set<string>();

    constructor(public syncingPermitted?: boolean) {}

    public dispose() {}

    public get syncingPermissionDenied(): boolean {
        return this.syncingPermitted === false;
    }

    public getFolderSyncingPermission(_sourceFolder: string): FolderSyncingPermission {
        if (this.syncingPermitted === undefined) {
            return FolderSyncingPermission.unknown;
        }
        if (this.syncingPermitted === false) {
            return FolderSyncingPermission.denied;
        }
        return this._permittedFolders.has(_sourceFolder)
            ? FolderSyncingPermission.granted
            : FolderSyncingPermission.unknown;
    }

    public setDefaultPermissions(sourceFolders: Array<string>): void {
        if (this.syncingPermitted === undefined && sourceFolders.length > 0) {
            this.setPermittedFolders(sourceFolders);
        }
    }

    public setPermittedFolders(sourceFolders: Array<string>): void {
        this._permittedFolders.clear();
        sourceFolders.forEach((folder) => {
            this._permittedFolders.add(folder);
        });
        this.syncingPermitted = true;
    }

    public addPermittedFolder(sourceFolder: string): void {
        this._permittedFolders.add(sourceFolder);
    }

    public addImplicitlyPermittedFolder(sourceFolder: string): void {
        if (!this.syncingPermitted) {
            return;
        }
        this._permittedFolders.add(sourceFolder);
    }

    public dropPermission(sourceFolders: Array<string>): void {
        sourceFolders.forEach((folder) => {
            this._permittedFolders.delete(folder);
        });
    }

    public dropStaleFolders(sourceFolders: Array<string>): void {
        if (this.syncingPermitted === undefined || this.syncingPermitted === false) {
            return;
        }
        this.setPermittedFolders(sourceFolders);
    }

    public denyPermission(): void {
        this.syncingPermitted = false;
    }

    public async persistCurrentPermission(): Promise<void> {}

    public clearPermission(): void {
        this.syncingPermitted = undefined;
    }
}
