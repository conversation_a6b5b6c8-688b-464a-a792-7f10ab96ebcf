export {
    CancellationError,
    CancellationToken,
    CancellationTokenSource,
    CodeLens,
    ColorThemeKind,
    commands,
    CompletionItem,
    CompletionItemKind,
    CompletionTriggerKind,
    ConfigurationTarget,
    contextKeys,
    debug,
    Diagnostic,
    DiagnosticSeverity,
    TrackedDisposable as Disposable,
    env,
    EventEmitter,
    ExtensionContext,
    ExtensionMode,
    extensions,
    FileSystemWatcher,
    Hover,
    InlineCompletionItem,
    InlineCompletionTriggerKind,
    languages,
    MarkdownString,
    Memento,
    NotebookCellKind,
    OverviewRulerLane,
    Position,
    ProgressLocation,
    QuickPickItemKind,
    Range,
    RelativePattern,
    Selection,
    StatusBarAlignment,
    Tab,
    TabGroup,
    TabGroups,
    TabInputText,
    TabInputTextDiff,
    TextDocument,
    TextDocumentChangeReason,
    TextDocumentShowOptions,
    TextEdit,
    TextEditor,
    TextEditorRevealType,
    TextEditorSelectionChangeKind,
    ThemeColor,
    ThemeIcon,
    UIKind,
    Uri,
    version,
    ViewColumn,
    VSCodeFS,
    window,
    workspace,
    WorkspaceEdit,
} from "./vscode-mocks";
