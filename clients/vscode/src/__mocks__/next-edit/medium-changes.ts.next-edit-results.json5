[
    {
        lineStart: 0,
        lineEnd: 1,
        existingCode: "/**\n",
        suggestedCode: "",
        changeDescription: "remove comment",
    },
    {
        lineStart: 1,
        lineEnd: 6,
    },
    {
        lineStart: 6,
        lineEnd: 6,
        existingCode: "",
        suggestedCode: "    // Here's a comment.\n    x += 100;\n",
        changeDescription: "Add 100 to x",
    },
    {
        lineStart: 6,
        lineEnd: 11,
    },
    {
        lineStart: 11,
        lineEnd: 13,
        existingCode: "    // Here's a comment.\n    x += 100;\n",
        suggestedCode: "",
        changeDescription: "Remove addition",
    },
    {
        lineStart: 13,
        lineEnd: 18,
    },
    {
        lineStart: 18,
        lineEnd: 20,
        existingCode: "    // Here's a ||comm|ent.|\n||    x += |1|00;\n",
        suggestedCode: "    // Here's a |pointless |comm|a,|\n|    x -= 100;\n|    x += |2|00;\n",
        changeDescription: "Do the same thing in a roundabout way",
    },
    {
        lineStart: 20,
        lineEnd: 22,
    },
]
