[
    {
        lineStart: 0,
        lineEnd: 6,
    },
    {
        lineStart: 6,
        lineEnd: 6,
        existingCode: "",
        suggestedCode: "    // Here are a bunch of pointless operations for this example.\n\
    x <<= 15;\n\
    x *= 17;\n\
    x >> 15;\n\
    x <<= 15;\n\
    x *= 17;\n\
    x >> 15;\n\
",
        changeDescription: "Do a bunch of pointless operations",
    },
    {
        lineStart: 6,
        lineEnd: 11,
    },
    {
        lineStart: 11,
        lineEnd: 18,
        existingCode: "    // Here are a bunch of pointless operations for this example.\n\
    x <<= 15;\n\
    x *= 17;\n\
    x >> 15;\n\
    x <<= 15;\n\
    x *= 17;\n\
    x >> 15;\n\
",
        suggestedCode: "",
        changeDescription: "Remove addition",
    },
    {
        lineStart: 18,
        lineEnd: 23,
    },
    {
        lineStart: 23,
        lineEnd: 30,
        existingCode: "    // Here are a bunch of ||pointless operations for this example.\n\
|    x <<= 15;\n\
    x *= 17;\n\
|    x >> 15;\n\
    x <<= |15|;\n\
||    x *= |17|;\n\
    x >> 15;\n\
",
        suggestedCode: "    // Here are a bunch of |different |pointless operations for this example.\n\
||    x >> 15;\n\
    x <<= |25|;\n\
|    x <<= 15;\n\
    x *= 17;\n\
|    x *= |27|;\n\
",

        changeDescription: "Do the same thing in a roundabout way",
    },
    {
        lineStart: 30,
        lineEnd: 34,
    },
    {
        lineStart: 34,
        lineEnd: 36,
        existingCode: "    let x = 0;\n\
    return x + 1;\n\
",
        suggestedCode: "    // Here are a bunch of pointless operations for this example.\n\
    let x = 1;\n\
    x *= 17;\n\
    x >> 15;\n\
    x <<= 15;\n\
    x *= 17;\n\
    return x;\n\
",
        changeDescription: "Add a variable",
    },
]
