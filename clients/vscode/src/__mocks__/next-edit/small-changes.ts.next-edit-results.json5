[
    {
        lineStart: 0,
        lineEnd: 5,
    },
    {
        lineStart: 5,
        lineEnd: 6,
        existingCode: "    const x|| = 0;\n",
        suggestedCode: "    const x|: number| = 0;\n",
        changeDescription: "Add type",
    },
    {
        lineStart: 6,
        lineEnd: 10,
    },
    {
        lineStart: 10,
        lineEnd: 11,
        existingCode: "    const x|: number| = 0;\n",
        suggestedCode: "    const x|| = 0;\n",
        changeDescription: "Remove type",
    },
    {
        lineStart: 11,
        lineEnd: 15,
    },
    {
        lineStart: 15,
        lineEnd: 16,
        existingCode: "    const x: |number| = 0;\n",
        suggestedCode: "    const x: |float| = 0;\n",
        changeDescription: "Change type",
    },
    {
        lineStart: 16,
        lineEnd: 20,
    },
    {
        lineStart: 20,
        lineEnd: 21,
        existingCode: '    const str: string = "|&amp;|";\n',
        suggestedCode: '    const str: string = "|&copy;|";\n',
        changeDescription: "Change &amp; to &copy;",
    },
    {
        lineStart: 21,
        lineEnd: 26,
    },
    {
        lineStart: 26,
        lineEnd: 27,
        existingCode: "    const str: string = |\"</pre><script>alert('XSS')</script><pre>\"|;\n",
        suggestedCode: "    const str: string = |\"<script>alert('XSS')</script>\"|;\n",
        changeDescription: "Change \"</pre><script>alert('XSS')</script><pre>\" to \"<script>alert('XSS')</script>\"",
    },
    {
        lineStart: 27,
        lineEnd: 32,
    },
    {
        lineStart: 32,
        lineEnd: 33,
        existingCode: '    const str: string = "$(file)"; // $(circuit-board) $(gear)\n',
        suggestedCode: '    const str: string = "$(circuit-board)"; // $(circuit-board) $(gear)\n',
        changeDescription: "Change $(file) to $(circuit-board)",
    },
    {
        lineStart: 33,
        lineEnd: 36,
    },
]
