/**
 * This file contains a medley of examples to test how decorations are rendered.
 */

function _smallInlineInsertion() {
    const x = 0;
    return x + 1;
}

function _smallInlineDeletion() {
    const x: number = 0;
    return x + 1;
}

function _smallInlineModification() {
    const x: number = 0;
    return x + 1;
}

function _smallInlineModificationWithHTML() {
    const str: string = "&amp;";
    return str;
}

function _smallInlineModificationWithMaliciousHTML() {
    // a comment </pre><script>alert('XSS')</script><pre>
    const str: string = "</pre><script>alert('XSS')</script><pre>";
    return str; // a comment </pre><script>alert('XSS')</script><pre>
}

function _smallInlineModificationWithVSCodeIcon() {
    // a comment $(gear)
    const str: string = "$(file)"; // $(circuit-board) $(gear)
    return str; // $(gear)
}
