import { AugmentCommand, CommandType } from "../command-manager";

export interface MockCommandOptions {
    type?: CommandType;
    title?: string;
    canRun?: boolean;
}

export class MockCommand extends AugmentCommand {
    public options: MockCommandOptions;

    constructor(commandID: string, options?: MockCommandOptions) {
        super(options?.title);
        this._commandID = commandID;
        this.options = options || {};
        this.run = jest.fn();
    }

    get type(): CommandType {
        return this.options?.type || CommandType.debug;
    }

    run() {
        // do nothing
    }

    canRun(): boolean {
        if (this.options?.canRun !== undefined) {
            return this.options?.canRun;
        }
        return super.canRun();
    }
}
