import { Asset<PERSON>anager } from "$vscode/src/client-interfaces/asset-manager";

import * as vscode from "vscode";

import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { getExampleAugmenConfig } from "../../__mocks__/mock-augment-config";
import { WorkspaceManagerTestKit } from "../../__tests__/workspace/workspace-manager-test-kit";
import { AugmentConfigListener } from "../../augment-config-listener";
import ChatModel, { ChatRequest } from "../../chat/chat-model";
import { FuzzyFsSearcher } from "../../chat/fuzzy-fs-searcher";
import { FuzzySymbolSearcher } from "../../chat/fuzzy-symbol-searcher";
import { FeatureFlagManager } from "../../feature-flags";
import { IAugmentGlobalState } from "../../utils/context";
import { RecentItems } from "../../utils/recent-items";
import { SyncingStatusEvent } from "../../workspace/types";
import { WorkspaceManager } from "../../workspace/workspace-manager";

/**
 * ChatModelTestKit is a test utility class for ChatModel.
 * It provides a mock environment for testing ChatModel functionality.
 */
export class ChatModelTestKit extends vscode.Disposable {
    public mockApiServer = new MockAPIServer();
    public recentChats = new RecentItems<ChatRequest>(10);
    public onDidChangeSyncingStatus = new vscode.EventEmitter<SyncingStatusEvent>();
    public chatModel: ChatModel;
    public fuzzySymbolSearcher: FuzzySymbolSearcher;
    public fuzzyFsSearcher: FuzzyFsSearcher;
    public wsManager: WorkspaceManager;
    public featureFlagManager: FeatureFlagManager;
    private _assetManager: AssetManager;
    private readonly _unusedAny: any = jest.fn();

    constructor(public wsManagerTestKit: WorkspaceManagerTestKit) {
        super(() => {
            this.chatModel.dispose();
            this.onDidChangeSyncingStatus.dispose();
            this.recentChats.dispose();
            this.wsManager.dispose();
            this.wsManagerTestKit.dispose();
        });

        this.featureFlagManager = wsManagerTestKit.featureFlagManager;
        this.wsManager = this.wsManagerTestKit.createWorkspaceManager();
        this._assetManager = new AssetManager(this._unusedAny);
        this.fuzzyFsSearcher = new FuzzyFsSearcher(
            this.globalState,
            this.wsManager,
            this.onDidChangeSyncingStatus.event
        );
        this.fuzzySymbolSearcher = new FuzzySymbolSearcher(
            this.globalState,
            {
                config: getExampleAugmenConfig({
                    enableDebugFeatures: true,
                }),
            } as AugmentConfigListener,
            this.fuzzyFsSearcher,
            this.wsManager
        );

        this.chatModel = new ChatModel(
            this.globalState,
            this.mockApiServer,
            this.wsManager,
            this.recentChats,
            this.fuzzySymbolSearcher,
            this._assetManager,
            this.featureFlagManager,
            undefined // No checkpoint manager in tests
        );
    }

    /**
     * Simulates getting VCS changes.
     * @returns {Object} An object representing mock VCS changes.
     */
    getVCSChange = () => {
        return {
            commits: [],
            workingDirectory: [],
        };
    };

    /**
     * Provides a mock global state for testing.
     * @returns {IAugmentGlobalState} A mock IAugmentGlobalState object.
     */
    get globalState(): IAugmentGlobalState {
        return {
            update: jest.fn(),
            get: jest.fn(),
            save: jest.fn(),
            load: jest.fn(),
            onDidChangeFileStorage: jest.fn(),
        };
    }
}
