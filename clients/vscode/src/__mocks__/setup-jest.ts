import { resetLibraryAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import { resetLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { resetLibraryClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { resetLibraryPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { resetLibraryWebviewMessaging } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { resetAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-session-event-reporter";

import { TrackedDisposable } from "./vscode-mocks";

jest.mock("../logging");

// Mock uuid module since crypto.randomUUID is not available in Jest environment
jest.mock("uuid", () => ({
    v4: jest.fn(() => {
        // Generate a simple mock UUID that's unique enough for tests
        return `mock-uuid-${Math.random().toString(36).substring(2, 11)}-${Date.now()}`;
    }),
    validate: jest.fn((uuid: string) => {
        // Simple validation - check if it looks like our mock UUID or a real UUID
        return (
            typeof uuid === "string" &&
            uuid.length > 0 &&
            (uuid.startsWith("mock-uuid-") ||
                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uuid))
        );
    }),
}));

beforeAll(() => {
    // TrackedDisposable is used in the  vscode-mocks for our
    // event emitters.  We reset the stats here so that each
    // suite starts a new.
    TrackedDisposable.resetStats();
});

afterAll(() => {
    // NOTE: If we can fix all the issues here, we should
    // switch this to TrackDisposable.assertDisposed() to
    // error when a test leaks disposables.
    const stats = TrackedDisposable.getStats();
    if (stats.constructed !== stats.disposed) {
        // eslint-disable-next-line no-console
        console.warn(
            `⚠️ Disposable Leak Warning: There may be a leak of ${
                stats.constructed - stats.disposed
            } undisposed disposabled in this test suite.`
        );
        if (process.env.LOG_LEVEL?.toLowerCase() === "verbose") {
            TrackedDisposable.printTraces();
        }
    }
});

afterEach(() => {
    resetAgentSessionEventReporter();
    resetLibraryClientFeatureFlags();
    resetLibraryClientWorkspaces();
    resetLibraryPluginFileStore();
    resetLibraryAPIClient();
    resetLibraryWebviewMessaging();
});
