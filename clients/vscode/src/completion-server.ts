import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import { ReplacementText } from "@augment-internal/sidecar-libs/src/chat/chat-types";

import { APIServer, CompletionLocation, CompletionResult } from "./augment-api";
import { CompletionTimeline } from "./completions/completion-timeline";
import { FileEditEvent } from "./next-edit/file-edit-events";
import { ViewedContentInfo } from "./workspace/viewed-content-tracker";

export type CompletionParams = {
    prefixSize: number;
    suffixSize: number;
    chunkSize: number;
};

export class CompletionServer {
    public static readonly chunkSize = 1024;

    private _completionTimeoutMs: number | undefined;
    private _completionParams: CompletionParams;

    constructor(
        private _apiServer: APIServer,
        initialCompletionTimeoutMs: number | undefined,
        prefixSize: number,
        suffixSize: number,
        chunkSize: number = CompletionServer.chunkSize
    ) {
        this._completionTimeoutMs = initialCompletionTimeoutMs;
        this._completionParams = { prefixSize, suffixSize, chunkSize };
    }

    public createRequestId(): string {
        return this._apiServer.createRequestId();
    }

    public get completionParams(): Readonly<CompletionParams> {
        return this._completionParams;
    }

    public async complete(
        requestId: string,
        prefix: string,
        suffix: string,
        pathName: string,
        blobName: string | undefined,
        completionLocation: CompletionLocation | undefined,
        language: string,
        context: Blobs,
        recentChanges: ReplacementText[],
        viewedContentEvents: ViewedContentInfo[],
        fileEditEvents?: FileEditEvent[],
        completionTimeoutMs?: number,
        probeOnly?: boolean,
        completionTimeline?: CompletionTimeline
    ): Promise<CompletionResult> {
        const result = await this._apiServer.complete(
            requestId,
            prefix,
            suffix,
            pathName,
            blobName,
            completionLocation,
            language,
            context,
            recentChanges,
            viewedContentEvents,
            fileEditEvents,
            completionTimeoutMs,
            probeOnly,
            completionTimeline
        );

        if (result.completionTimeoutMs !== undefined) {
            this._completionTimeoutMs = result.completionTimeoutMs;
        }
        if (result.suggestedPrefixCharCount !== undefined) {
            this._completionParams.prefixSize = result.suggestedPrefixCharCount;
        }
        if (result.suggestedSuffixCharCount !== undefined) {
            this._completionParams.suffixSize = result.suggestedSuffixCharCount;
        }
        if (result.suggestedPrefixCharCount !== undefined) {
            this._completionParams.chunkSize = CompletionServer.chunkSize;
        }

        return result;
    }
}
