import Fuse, { FuseResult, type FuseSearchOptions, IFuseOptions } from "fuse.js";
import debounce from "lodash/debounce";
import * as vscode from "vscode";

import { type IAugmentGlobalState, WriteBackCacheKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { KVWorkQueue } from "../utils/work-queue";

// State Flow for an item in WriteBackIndex:
//
// 1. Not in Cache
//    - To Active: via set()
//
// 2. Active (In _items and _index)
//    - To Revalidating: via markForRevalidation()
//    - To Queued for Removal: via remove()
//    - Can remain in Active: via set() (if item already exists)
//
// 3. Revalidating (In _revalidationQueue)
//    - To Valid/Invalid: via _revalidateEntry()
//
// 4. Valid/Invalid
//    - To Active: If valid (implicit)
//    - To Queued for Removal: If invalid
//
// 5. Queued for Removal (In _keysToRemove)
//    - To Not in Cache: via _flushToRemove()
//    - Can remain in Queued for Removal: via set() (cancels removal and puts item back in Active state)
//
// Visual representation of the state flow:
//
// +-------------+          set()      +-----------------+
// |   Not in    | --+---------------> |     Active      |---------------+
// |    Cache    |   |                 | (In _items and  |               |
// +-------------+   |                 |    _index)      |               |
//       ^           |                 +-----------------+               |
//       |           |                         |                         |
//       |           |                         | markForRevalidation()   |
//       |           |                         v                         |
//       |           |                 +-----------------+               |
//       |           |      set()      |   Revalidating  |               |
//       |           +-----------------| (In _revalidat- |               |
//       |           |                 |  ionQueue)      |               |
//       |           |                 +-----------------+               |
//       |           |                         |                         |
//       |           |                         | _revalidateEntry()      | remove()
//       |           |                         v                         |
//       |           |     If valid    +-----------------+               |
//       |           +-----------------| Valid / Invalid |               |
//       |           |                 +-----------------+               |
//       |           |                         |                         |
//       |           |                         | If Invalid              |
//       |           |                         v                         |
//       |           |                 +-----------------+               |
//       |           |      set()      |  Queued for     |               |
//       |           +-----------------|    Removal      |<--------------+
//       |                             | (In _keysTo-    |
//       |                             |   Remove)       |
//       |                             +-----------------+
//       |                                     |
//       |                                     | _flushToRemove()
//       |                                     |
//       +-------------------------------------+
//
// Note: The dumpContext() method periodically saves the entire cache state to global storage.

type IndexDump<T extends {}> = { [key: string]: T };
interface IWriteBackIndexOptions<T> {
    fuse: IFuseOptions<T>;
    validator: (item: T) => Promise<boolean>;
    keyFn: (item: T) => string;
    maxKeysBeforeRemoval?: number;
    maxDelayBeforeRemovalMs?: number;
}

/**
 * A Fuse.js index for selected code details.
 */
export class WriteBackIndex<T extends {}> extends DisposableService {
    private _index: Fuse<T>;
    private _items: { [key: string]: T } = {};

    private _revalidationQueue: KVWorkQueue<string, T>;
    private _keysToRemove: Set<string> = new Set();
    private _maxKeysBeforeRemoval: number = 100;
    private _flushTimer: NodeJS.Timeout | null = null;
    private readonly _flushDelay: number = 15000; // 15 seconds in milliseconds

    constructor(
        private _globalState: IAugmentGlobalState,
        private _indexBackKey: WriteBackCacheKey,
        private _opts: IWriteBackIndexOptions<T>
    ) {
        super();
        this._maxKeysBeforeRemoval = _opts.maxKeysBeforeRemoval ?? this._maxKeysBeforeRemoval;
        this._flushDelay = _opts.maxDelayBeforeRemovalMs ?? this._flushDelay;
        this._index = new Fuse(Object.values(this._items), { ...this._opts.fuse });
        this._revalidationQueue = new KVWorkQueue<string, T>(this._revalidateEntry.bind(this));
        this.addDisposables(
            this._revalidationQueue,
            new vscode.Disposable(() => this.dumpContext.cancel())
        );
        void this.loadContext();
    }

    /**
     * Dumps the index to the global state.
     */
    private dumpContext = debounce(
        async (): Promise<void> => {
            await this._globalState.save(this._indexBackKey, this._items, {
                uniquePerWorkspace: true,
            });

            // Clear our in-memory index. We should not be using the in-memory index for this.
            void this._globalState.update(this._indexBackKey, undefined);
        },
        60 * 1000 /* 1 minute */,
        {
            leading: true,
            trailing: true,
        }
    );

    /**
     * Loads the index from the global state.
     */
    private loadContext = async (): Promise<void> => {
        // First try to load context from disk
        const storedCtx = await this._globalState.load<IndexDump<T>>(this._indexBackKey, {
            uniquePerWorkspace: true,
        });
        if (storedCtx) {
            this._items = { ...this._items, ...storedCtx };
            this._index = new Fuse(Object.values(this._items), this._opts.fuse);
            this.markForRevalidation(Object.values(this._items));
            return;
        }

        // Then try to load from in-memory storage if it doesn't exist
        const inMemoryCtx = this._globalState.get<IndexDump<T>>(this._indexBackKey);
        if (inMemoryCtx) {
            this._items = { ...this._items, ...inMemoryCtx };
            this._index = new Fuse(Object.values(this._items), this._opts.fuse);
            this.markForRevalidation(Object.values(this._items));
        }
    };

    /**
     * Adds or updates an item in the index
     *
     * @param item The item to add or update
     */
    public set = (item: T): boolean => {
        const key: string = this._opts.keyFn(item);

        // Cancel any pending revalidation/removal
        this._cancelQueuedWork(key);

        // If we are already tracking this, do not do anything
        if (this._items[key]) {
            return false;
        }

        this._items[key] = item;
        this._index.add(item);

        // Trigger a call to dumpContext. Will dump the context once there are no
        // updates for 30 seconds.
        void this.dumpContext();
        return true;
    };

    /**
     * Marks a list of items for revalidation.
     * This method adds the items to the revalidation queue.
     * After marking the items, it triggers the revalidation process.
     *
     * @param items An array of items to mark for revalidation
     */
    public markForRevalidation = (items: T[]) => {
        items.forEach((item: T) => {
            const key = this._opts.keyFn(item);
            this._revalidationQueue.insert(key, item);
        });
        void this._revalidationQueue.kick();
    };

    /**
     * Searches for items in the index.
     *
     * @param pattern The search pattern
     * @returns An array of search results
     */
    public search = (pattern: string, options?: FuseSearchOptions): FuseResult<T>[] => {
        return this._index.search(pattern, options);
    };

    /**
     * Removes an item from the index.
     *
     * @param item The item to remove
     */
    public remove = (item: T): void => {
        this._queueKeyRemoval(this._opts.keyFn(item));
    };

    /**
     * Clears all entries from the index.
     */
    public clear = (): void => {
        this._items = {};
        this._index.setCollection([]);
    };

    public get items(): { [key: string]: T } {
        return { ...this._items };
    }

    /**
     * Cancels the revalidation of a single entry.
     *
     * @param key The key of the entry to cancel revalidation for
     */
    private _cancelQueuedWork = (key: string): void => {
        this._revalidationQueue.cancel(key);
        this._keysToRemove.delete(key);
    };

    /**
     * Checks the validity of a single item using the provided validator.
     *
     * @param item The item to validate
     * @returns A promise that resolves to a boolean indicating validity
     */
    private _checkValidity = async (item: T): Promise<boolean> => {
        // If validator is not provided, default to true. Otherwise, await the validator
        const isValid = this._opts.validator ? await this._opts.validator(item) : true;

        return isValid;
    };

    /**
     * Revalidates a single entry and removes the invalid item from the index if necessary.
     * This method is called by the revalidation queue.
     *
     * @param entry The entry to revalidate, or undefined if the queue is empty
     * @returns A promise that resolves when the revalidation is complete
     */
    private async _revalidateEntry(entry: [string, T] | undefined): Promise<void> {
        if (entry === undefined) {
            return;
        }

        const [key, item] = entry;
        if (!(await this._checkValidity(item))) {
            this._queueKeyRemoval(key);
        }
    }

    /**
     * Queues a key for removal from the index.
     *
     * @param key The key to remove
     */
    private _queueKeyRemoval = (key: string): void => {
        this._keysToRemove.add(key);
        if (this._keysToRemove.size >= this._maxKeysBeforeRemoval) {
            this._flushToRemove();
        } else if (!this._flushTimer) {
            this._flushTimer = setTimeout(() => this._flushToRemove(), this._flushDelay);
        }
    };

    /**
     * Flushes the keys to remove from the index.
     */
    private _flushToRemove = (): void => {
        if (this._flushTimer) {
            clearTimeout(this._flushTimer);
            this._flushTimer = null;
        }

        for (const key of this._keysToRemove) {
            delete this._items[key];
        }
        // Remove keys from index
        this._index.remove((doc: T) => this._keysToRemove.has(this._opts.keyFn(doc)));
        this._keysToRemove.clear();
    };
}
