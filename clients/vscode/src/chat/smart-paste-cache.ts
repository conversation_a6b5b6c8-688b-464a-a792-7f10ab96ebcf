import { LRUCache } from "lru-cache";

import { ChatInstructionStreamResult } from "../augment-api";
import CopyableGenerator from "../utils/copyable-generator";
import { DisposableService } from "../utils/disposable-service";
import type { SelectedCodeDetails } from "../utils/types";
import type { ChatSmartPaste } from "../webview-providers/webview-messages";

/**
 * Context for the smart paste cache. Should be the same as the information
 * required to issue a smart paste request.
 */
export interface ISmartPasteCacheContext {
    message: ChatSmartPaste;
    selectedCodeDetails: SelectedCodeDetails | null;
    targetFilePath: string;
    targetFileContent: string;
}

interface CachedSmartPasteStoredResult {
    generator: CopyableGenerator<ChatInstructionStreamResult>;
    requestId: string;
}

interface CachedSmartPasteResult {
    generator: AsyncGenerator<ChatInstructionStreamResult>;
    requestId: string;
}

type SmartPasteLRUCache = LRUCache<string, CachedSmartPasteStoredResult, ISmartPasteCacheContext>;

/**
 * A cache for smart paste edits.
 */
export class SmartPasteCache extends DisposableService {
    private _cache: SmartPasteLRUCache;

    constructor(
        private _getter: (ctx: ISmartPasteCacheContext) => Promise<CachedSmartPasteResult>
    ) {
        super();
        this._cache = new LRUCache({
            max: 1000,
            ttl: 1000 * 60 * 60 /* 1 hour */,
            fetchMethod: this._fetchSmartPasteResults,
        });

        this.addDisposable({
            dispose: () => {
                this._cache.clear();
            },
        });
    }

    private _fetchSmartPasteResults = async (
        _key: string,
        _staleValue: CachedSmartPasteStoredResult | undefined,
        { context }: { context: ISmartPasteCacheContext }
    ): Promise<CachedSmartPasteStoredResult | undefined> => {
        const result = await this._getter(context);
        return Promise.resolve({
            generator: new CopyableGenerator(result.generator),
            requestId: result.requestId,
        });
    };

    /**
     * Gets the smart paste edits for the given context.
     *
     * @param codeblock
     * @param targetFilePath
     * @param targetContents
     * @param ctx
     * @returns
     */
    public get = async (
        codeblock: string,
        targetFilePath: string,
        targetContents: string,
        ctx: ISmartPasteCacheContext
    ): Promise<CachedSmartPasteResult | undefined> => {
        const key = formatKey(codeblock, targetFilePath, targetContents);

        const cachedResult = this._cache.get(key);
        if (!cachedResult || cachedResult.generator.hasErrored) {
            this._cache.delete(key);
        }

        const result = await this._cache.fetch(key, { context: ctx, forceRefresh: false });
        if (!result) {
            return undefined;
        }
        return {
            generator: result.generator.copy(),
            requestId: result.requestId,
        };
    };

    /**
     * Gets the smart paste edits for the given context, without caching.
     *
     * @param ctx
     * @returns
     */
    public getDirect = async (
        ctx: ISmartPasteCacheContext
    ): Promise<CachedSmartPasteResult | undefined> => {
        const result = await Promise.resolve(this._getter(ctx));
        if (!result) {
            return undefined;
        }
        const generator = new CopyableGenerator(result.generator);
        if (!generator) {
            return undefined;
        }
        return {
            generator: generator.copy(),
            requestId: result.requestId,
        };
    };
}

function formatKey(codeblock: string, targetFilePath: string, targetContents: string): string {
    return `${codeblock}--${targetFilePath}--${targetContents}`;
}
