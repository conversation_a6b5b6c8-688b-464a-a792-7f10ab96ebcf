import {
    RemoteInfoSource,
    RemoteToolDefinition,
} from "@augment-internal/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import { RunRemoteToolResult } from "@augment-internal/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import { ExtraToolInput, RemoteToolId } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";

export class VSCodeRemoteInfo implements RemoteInfoSource {
    private _logger = getLogger("VSCodeRemoteInfo");

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _configListener: AugmentConfigListener
    ) {}

    public async retrieveRemoteTools(tools: RemoteToolId[]): Promise<RemoteToolDefinition[]> {
        try {
            const apiResult = await this._apiServer.listRemoteTools(tools);
            return apiResult.tools;
        } catch (e: any) {
            this._logger.error("Failed to list remote tools", e);
            return [];
        }
    }

    public filterToolsWithExtraInput(toolIds: RemoteToolId[]): Promise<Set<RemoteToolId>> {
        const toolsWithExtraInput = new Set<RemoteToolId>();
        for (const toolId of toolIds) {
            if (this._getExtraToolInput(toolId) !== undefined) {
                toolsWithExtraInput.add(toolId);
            }
        }
        return Promise.resolve(toolsWithExtraInput);
    }

    public async runRemoteTool(
        toolRequestId: string,
        toolName: string,
        toolInputJson: string,
        toolId: RemoteToolId,
        signal: AbortSignal
    ): Promise<RunRemoteToolResult> {
        return await this._apiServer.runRemoteTool(
            toolRequestId,
            toolName,
            toolInputJson,
            toolId,
            this._getExtraToolInput(toolId),
            signal
        );
    }

    private _getExtraToolInput(toolId: RemoteToolId): ExtraToolInput | undefined {
        switch (toolId) {
            case RemoteToolId.Jira:
            case RemoteToolId.Confluence:
                return this._configListener.config.integrations.atlassian;
            case RemoteToolId.Notion:
                return this._configListener.config.integrations.notion;
            case RemoteToolId.Linear:
                return this._configListener.config.integrations.linear;
            case RemoteToolId.GitHubApi:
                return this._configListener.config.integrations.github;
            default:
                return undefined;
        }
    }
}
