import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { readDirectory } from "../utils/fs-utils";
import { isAbsolutePathName, relativePathName } from "../utils/path-utils";
import { viewTextDocument } from "../workspace/view-text-document";
import { WorkspaceManager } from "../workspace/workspace-manager";

export function getCwdForTool(workspaceManager: WorkspaceManager) {
    let cwd: string | undefined;
    if (vscode.window.activeTextEditor) {
        const path = vscode.window.activeTextEditor.document.uri.fsPath;
        cwd = workspaceManager.getFolderRoot(path);
    }
    if (cwd === undefined) {
        cwd = workspaceManager.getMostRecentlyChangedFolderRoot();
        if (cwd === undefined) {
            cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        }
    }
    return cwd;
}

/** Helper function to get the absolute path of a file. */
export function _getQualifiedPath(
    path: string,
    workspaceManager: WorkspaceManager
): QualifiedPathName | undefined {
    // If the path is absolute, use it to resolve the QualifiedPathName
    if (isAbsolutePathName(path)) {
        const folderRoot = workspaceManager.getFolderRoot(path);
        if (folderRoot === undefined) {
            return undefined;
        }

        return new QualifiedPathName(folderRoot, relativePathName(folderRoot, path));
    }

    // If the path is relative, find the best workspace root match
    const bestWorkspaceRoot = workspaceManager.findBestWorkspaceRootMatch(path);
    if (bestWorkspaceRoot) {
        const folderRoot = bestWorkspaceRoot.qualifiedPathName.rootPath;
        return new QualifiedPathName(folderRoot, path);
    }

    const cwd = getCwdForTool(workspaceManager);
    if (cwd) {
        return new QualifiedPathName(cwd, path);
    }

    return undefined;
}

/** Helper function to read a file. */
export async function _readFile(
    filePath: string,
    workspaceManager: WorkspaceManager
): Promise<string | undefined> {
    const absPath = _getQualifiedPath(filePath, workspaceManager)?.absPath;
    if (absPath === undefined) {
        return undefined;
    }
    try {
        const doc = await viewTextDocument(absPath);
        return doc.getText();
    } catch (e) {
        return undefined;
    }
}

/** Helper function to list files in a directory. */
export async function _listFiles(
    folderPath: string,
    workspaceManager: WorkspaceManager
): Promise<string[] | undefined> {
    const absPath = _getQualifiedPath(folderPath, workspaceManager)?.absPath;
    if (absPath === undefined) {
        return undefined;
    }
    try {
        const files = await readDirectory(absPath);
        return files.map(([name, _]) => name);
    } catch (e) {
        return undefined;
    }
}
