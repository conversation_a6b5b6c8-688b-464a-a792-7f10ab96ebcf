import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import Fuse, { FuseSearchOptions, IFuseOptions } from "fuse.js";
import throttle from "lodash/throttle";
import { LRUCache } from "lru-cache";
import { type languages } from "monaco-editor";
import * as vscode from "vscode";

import { type AugmentConfigListener } from "../augment-config-listener";
import { type AugmentLogger, getLogger } from "../logging";
import { IAugmentGlobalState, WriteBackCacheKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import {
    FileDetails,
    FindSymbolRegexRequest,
    FindSymbolRequest,
    FindSymbolResponse,
    FindSymbolResponseData,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import { getFileDetailAbsPath } from "../workspace/types";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { monacoRangeToVscodeRange } from "./data-transform-utils";
import { IFuzzyFsSearcher } from "./fuzzy-fs-searcher";
import { getTokensFromUri, searchFileForRegexToken } from "./symbol-search-utils";
import { WriteBackCache } from "./write-back-cache";

const FUSE_OPTIONS = {
    keys: ["name"],
    threshold: 0.05,
    includeScore: true,
    useExtendedSearch: true,
    isCaseSensitive: true,
};
export const THROTTLE_MS = 1000 * 5; // Throttle cache invalidation to once per 5 seconds
const TIMEOUT_MS = 20000; // 20 seconds

// An interface for a fuzzy symbol searcher
export interface IFuzzySymbolSearcher {
    findSymbols: (request: FindSymbolRequest) => Promise<FindSymbolResponse>;
    findSymbolsRegex: (request: FindSymbolRegexRequest) => Promise<FindSymbolResponse>;
}

export interface IFileSymbolCacheContext {
    absPath: string;
    blobName: string;
    timeoutMs: number;
}
export class FuzzySymbolSearcher extends DisposableService implements IFuzzySymbolSearcher {
    // A cache of all the symbol-like tokens, indexed by blob name
    private _fileTokensCache: WriteBackCache<languages.DocumentSymbol[], IFileSymbolCacheContext>;
    private _logger: AugmentLogger;

    constructor(
        private readonly _globalState: IAugmentGlobalState,
        private readonly _config: AugmentConfigListener,
        private readonly _fuzzyFsSearcher: IFuzzyFsSearcher,
        private readonly _workspaceManager: WorkspaceManager
    ) {
        super();
        this._logger = getLogger("FuzzySymbolSearcher");
        this._fileTokensCache = new WriteBackCache<
            languages.DocumentSymbol[],
            IFileSymbolCacheContext
        >(this._globalState, WriteBackCacheKey.fuzzyBlobNamesToSymbols, {
            lru: {
                max: 1000, // Store 1000 files/file variations at most for symbol resolution
                fetchMethod: this._loadFileDetailSymbols,
            },
        });
        // Create a throttled function to control the frequency of cache invalidations for performance reasons
        const handleChangeTextDocumentCacheInvalidation = throttle(
            async (event: vscode.TextDocumentChangeEvent) => {
                const qpn = this._workspaceManager.resolvePathName(event.document.uri.fsPath);
                if (!qpn) {
                    return;
                }

                const blobName = this._workspaceManager.getBlobName(qpn);
                if (!blobName) {
                    return;
                }

                // Remove the cache entry for this blob name and trigger refresh
                this._fileTokensCache.remove(blobName);
                await this.warmupCache(blobName);
            },
            THROTTLE_MS,
            { leading: true, trailing: true } // Process both the first and last events in the throttle window
        );

        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument(handleChangeTextDocumentCacheInvalidation)
        );
        this.addDisposables(
            // When the active text editor changes, index the current file
            vscode.window.onDidChangeActiveTextEditor(
                async (e: vscode.TextEditor | undefined): Promise<void> => {
                    if (!e) {
                        return;
                    }
                    const qpn = this._workspaceManager.resolvePathName(e.document.uri.fsPath);
                    if (!qpn) {
                        return;
                    }

                    const blobName = this._workspaceManager.getBlobName(qpn);
                    if (!blobName) {
                        return;
                    }

                    await this.warmupCache(blobName);
                }
            )
        );
    }

    findSymbolsRegex = async (request: FindSymbolRegexRequest): Promise<FindSymbolResponse> => {
        // If debug features are disabled, return an empty array
        if (!this._config.config.enableDebugFeatures) {
            return { type: WebViewMessageType.findSymbolResponse, data: [] };
        }

        // Default back to a regex search
        const files: FileDetails[] = request.data.searchScope.files;
        const allSymbols = (
            await Promise.all(
                files.map(async (f: FileDetails): Promise<FindSymbolResponseData[]> => {
                    const symbols = await searchFileForRegexToken(f, request.data.query);
                    return symbols.map(
                        (s: languages.DocumentSymbol): FindSymbolResponseData => ({
                            ...s,
                            file: { ...f, range: undefined, fullRange: s.range },
                        })
                    );
                })
            )
        ).flat();

        // Create Fuse.js index to search through all the symbols
        const foundSymbols = buildAndSearchIndex(allSymbols, request.data.query, {
            fuseSearch: { limit: 1 },
            fuseInit: FUSE_OPTIONS,
        });

        // Return the found symbols
        return { type: WebViewMessageType.findSymbolResponse, data: foundSymbols };
    };

    /**
     * Top-level, public-facing API for finding symbols. Mostly performs
     * options and request/response parsing and formatting.
     *
     * @param request
     * @returns
     */
    findSymbols = async (request: FindSymbolRequest): Promise<FindSymbolResponse> => {
        // If the query is not a symbol, return an empty array
        if (!isPossibleSymbol(request.data.query)) {
            return { type: WebViewMessageType.findSymbolResponse, data: [] };
        }

        // Get all the tokens in the provided files. If none found, try a regex search
        let allSymbols: FindSymbolResponseData[] = await this.getAllFilteredTokens(
            request.data.searchScope.files ?? [],
            { filter: true, forceRefresh: false }
        );

        // Create Fuse.js index to search through all the symbols
        const foundSymbols = buildAndSearchIndex(allSymbols, request.data.query, {
            fuseSearch: { limit: 1 },
            fuseInit: FUSE_OPTIONS,
        });

        // Return the found symbols
        return { type: WebViewMessageType.findSymbolResponse, data: foundSymbols };
    };

    warmupCache = async (blobName: string): Promise<void> => {
        // For each blob name, get the file details and warmup the cache
        // The cache will automatically de-dupe multiple calls
        const qpn = this._workspaceManager.getAllPathNames(blobName)[0];
        if (!qpn) {
            return;
        }
        const fileDetails = {
            repoRoot: qpn.rootPath,
            pathName: qpn.relPath,
        };

        // This is the actual warmup call
        await this._getAllFileTokens(fileDetails);
    };

    /**
     * Gets all the tokens for the given set of files and their ranges, if any.
     *
     * @param files: The files to get tokens for.
     * @returns: The tokens for the files.
     */
    private getAllFilteredTokens = async (
        files: FileDetails[],
        options: { filter: boolean; forceRefresh: boolean }
    ): Promise<FindSymbolResponseData[]> => {
        // Get the tokens for each file
        const getFilteredTokens = async (f: FileDetails): Promise<FindSymbolResponseData[]> => {
            // Get tokens from the entire file
            let tokens = await this._getAllFileTokens(f, options.forceRefresh);
            if (!tokens) {
                return [];
            }

            // If the file has no requested range, return all the tokens
            if (!options.filter || !f.fullRange) {
                return tokens;
            }

            // If the file has a requested range, filter the tokens to only those in the requested range
            const searchRange = monacoRangeToVscodeRange(f.fullRange);
            return tokens.filter((s: FindSymbolResponseData) => {
                const range = monacoRangeToVscodeRange(s.range);
                return searchRange.contains(range);
            });
        };
        return (await Promise.all(files.map(getFilteredTokens))).flat();
    };

    /**
     * Loads the symbols for a file, given file details.
     * Used as the fetch method for the LRU cache.
     *
     * @param _blobName
     * @param _oldValue
     * @param param2
     * @returns
     */
    private _loadFileDetailSymbols: LRUCache<
        string,
        languages.DocumentSymbol[],
        IFileSymbolCacheContext
    >["fetchMethod"] = async (
        _blobName: string,
        _oldValue: languages.DocumentSymbol[] | undefined,
        { context }: { context: IFileSymbolCacheContext }
    ): Promise<languages.DocumentSymbol[]> => {
        // On a cache miss or force refresh, fetch the symbols
        const uri = vscode.Uri.file(context.absPath);

        // Until we get symbols or timeout
        let keepTrying: boolean = true;
        // Try up to 15 seconds
        const timeoutPromise = new Promise<undefined>((resolve) =>
            setTimeout(() => {
                resolve(undefined);
                keepTrying = false;
            }, context.timeoutMs)
        );

        while (keepTrying) {
            try {
                // Rate limit to 1 request per 4 seconds
                const currIterTimeout = new Promise<undefined>((res) => setTimeout(res, 4000));
                const symbols = await Promise.race([getTokensFromUri(uri), timeoutPromise]);

                // Timeout occurred, or we have found symbols
                if (symbols === undefined || symbols.length > 0) {
                    return symbols ?? [];
                }
                await currIterTimeout;
            } catch (error) {
                this._logger.error(
                    `Failed to load symbols for ${context.absPath}: ${
                        error instanceof Error ? error.message : String(error)
                    }`
                );
                return [];
            }
        }
        return [];
    };

    /**
     * Gets all the symbols, given file details.
     *
     * Uses an LRU cache in the implementation for performance.
     * The key of the cache is the blob name, which is unique to a (filepath, content) tuple.
     * The value of the cache is the symbols for the file.
     *
     * This function performs no filtering whatsoever -- it returns *all* symbols it finds
     * to the caller, which is then filtered by the caller. This is why we typically need to
     * retry this until it returns something non-empty.
     *
     * @param fileDetails: The file details to get symbols for.
     * @returns: The symbols for the file.
     */
    private _getAllFileTokens = async (
        fileDetails: FileDetails,
        forceRefresh: boolean = false
    ): Promise<FindSymbolResponseData[]> => {
        const currFileDetails = this._getCurrFileDetails(fileDetails);
        if (currFileDetails === undefined) {
            return [];
        }

        const currBlobName = this._getCurrBlobName(currFileDetails);
        if (!currBlobName) {
            return [];
        }

        try {
            // Fetch the tokens from the cache, which will trigger a refresh if
            // it either doesn't exist, is stale, or `forceRefresh` is true.
            const tokens = await this._fileTokensCache.cache.fetch(currBlobName, {
                context: {
                    absPath: getFileDetailAbsPath(currFileDetails),
                    blobName: currBlobName,
                    timeoutMs: TIMEOUT_MS,
                },
                forceRefresh,
            });

            if (!tokens) {
                return [];
            }
            return tokens.map(
                (s: languages.DocumentSymbol): FindSymbolResponseData => ({
                    name: s.name,
                    kind: s.kind,
                    range: s.range,
                    selectionRange: s.selectionRange,
                    detail: s.detail,
                    tags: s.tags || [],
                    children: [],
                    file: { ...fileDetails, range: undefined, fullRange: s.range },
                })
            );
        } catch (error) {
            // Log the error and return an empty array
            const errorMsg = error instanceof Error ? error.message : String(error);
            this._logger.error(`Failed to read file tokens for ${currBlobName}: ${errorMsg}`);
            return [];
        }
    };

    // Get the known blob name at the time this is called. This always returns
    // information as up-to-date as the workspace manager knows.
    private _getCurrBlobName = (fileDetails: FileDetails): string | undefined => {
        const qpn = new QualifiedPathName(fileDetails.repoRoot, fileDetails.pathName);
        return this._workspaceManager.getBlobName(qpn);
    };

    // Get the known file details at the time this is called, as viewed by the workspace manager.
    // This means we cannot resolve files that have not yet been indexed.
    // The file details must be an exact match, not a fuzzy match. If the file details are generated
    // by a model, they are not guaranteed to be found -- it is the caller's responsibility to resolve
    // the file in that case.
    private _getCurrFileDetails = (fileDetails: FileDetails): FileDetails | undefined => {
        const filePaths = this._workspaceManager.getAllQualifiedPathNames(fileDetails.pathName);
        return filePaths.length
            ? {
                  ...fileDetails,
                  repoRoot: filePaths[0].rootPath,
                  pathName: filePaths[0].relPath,
              }
            : undefined;
    };
}

// Utility function to build and search an index of find symbol response data based on name
function buildAndSearchIndex(
    findSymbolResponseData: FindSymbolResponseData[],
    query: string,
    options: {
        fuseInit: IFuseOptions<FindSymbolResponseData>;
        fuseSearch: FuseSearchOptions;
    }
): FindSymbolResponseData[] {
    const fuse = new Fuse(findSymbolResponseData, {
        ...FUSE_OPTIONS,
        ...options.fuseInit,
        keys: ["name"],
    });
    const result = fuse.search(`${query}$`, options.fuseSearch);
    return result.map((x) => x.item);
}

function isPossibleSymbol(name: string): boolean {
    // If the name is empty or contains a space, it is not a symbol
    return name.length > 0 && !name.match(/[/.\s]/);
}
