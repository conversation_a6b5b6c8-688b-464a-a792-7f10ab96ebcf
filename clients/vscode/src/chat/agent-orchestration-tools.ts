import { ChatRequestNodeType, Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    FileChangeType,
    RemoteAgentChatRequestDetails,
    RemoteAgentStatus,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
    ToolBase,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { backOff } from "exponential-backoff";
import * as fs from "fs/promises";
import * as path from "path";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { findGitRoot, writeFileUtf8 } from "../utils/fs-utils";
import { SetupScriptsManager } from "../utils/remote-agent-setup/setup-scripts-manager";
import { executeCommand } from "../vcs/command-utils";
import { threeWayMerge } from "../webview-panels/remote-agents/utils/diff-operations";
import { LocalToolType } from "../webview-providers/tool-types";

/**
 * A tool that starts a new worker agent for orchestration.
 * This tool is only available to local agents running in VSCode, not to remote agents.
 *
 * Workflow: Start workers → Monitor status → Retrieve results → Manually integrate changes into local workspace
 */
export class StartWorkerAgentTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("StartWorkerAgentTool");

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _globalState: AugmentGlobalState,
        private readonly _configListener: AugmentConfigListener
    ) {
        super(LocalToolType.startWorkerAgent, ToolSafety.Unsafe);
    }

    public readonly description: string = `\
**CRITICAL**: Use WaitForWorkerAgent for waiting (NOT ReadWorkerState polling).

Start worker agents for git-based task delegation. **Workers ONLY have repo access - you must provide ALL context.**

**WHEN TO USE**:
• Delegate complex coding tasks (>30min work)
• Parallel development on separate features
• Tasks requiring specialized expertise

**CONTEXT REQUIREMENTS**: Workers need explicit context in initial_prompt:
• Project background (Augment: AI coding assistant with VSCode extension)
• Tech stack (TypeScript, Svelte, Bazel, rules_js, Vite)
• Current problem/goal
• Success criteria and constraints
• Role/expertise needed

**WORKFLOW**: StartWorkerAgent → WaitForWorkerAgent → ReadWorkerAgentEdits → ApplyWorkerAgentEdits (auto-cleanup)

**EXAMPLE**: StartWorkerAgent(workers=[{summary: "Add JWT auth", initial_prompt: "CONTEXT: Augment VSCode extension needs JWT authentication. TECH: TypeScript/Node.js. TASK: Implement JWT auth with proper error handling and token refresh. SUCCESS: Working login flow with secure token storage."}])`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            workers: {
                type: "array",
                description:
                    "Array of worker configurations to start. Workers will complete tasks independently, and their results must be manually retrieved and pulled into your local workspace using ReadWorkerAgentEditsTool. If empty array, no workers will be started.",
                items: {
                    type: "object",
                    properties: {
                        summary: {
                            type: "string",
                            description:
                                "A short description of the task to delegate to the worker agent (results will be retrievable later for manual integration)",
                        },
                        initial_prompt: {
                            type: "string",
                            description:
                                "Initial instructions for the worker agent. The worker will complete this task and you must later retrieve the results using other orchestration tools to pull changes into your local workspace.",
                        },
                        repository_url: {
                            type: "string",
                            description:
                                "Git repository URL (for remote orchestrator). If not provided, defaults to the current repository's remote URL.",
                        },
                        git_ref: {
                            type: "string",
                            description:
                                "Git reference (branch/tag/commit) (for remote orchestrator). If not provided, defaults to the current branch.",
                        },
                        model_id: {
                            type: "string",
                            description: "Optional model ID to use",
                        },
                    },
                    required: ["summary", "initial_prompt"],
                },
            },
        },
        required: ["workers"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    private enhanceWorkerInstruction(userPrompt: string, branchName?: string): string {
        const prefixedBranchName = branchName
            ? `augment-swarm-${branchName}`
            : "augment-swarm-[assigned-branch]";
        const workerGuidance = `

[augment-swarm-worker] You are a worker in the augment-swarm system. Orchestrator delegates tasks and retrieves your work via git branches.

**WORKFLOW**: You work on ${prefixedBranchName} → make commits → push → orchestrator reviews via ReadWorkerAgentEdits → integrates via ApplyWorkerAgentEdits

**DELEGATION PROTOCOL**:
• **SCOPE_CLARIFICATION_NEEDED**: If unclear, respond: "SCOPE_CLARIFICATION_NEEDED: [specific questions]"
• **COMPLEXITY_ASSESSMENT**: Always assess: "COMPLEXITY_ASSESSMENT: [SIMPLE/MODERATE/COMPLEX/RESEARCH] - [reasoning]"
  - SIMPLE (<2h), MODERATE (2-8h): proceed with implementation
  - COMPLEX (>8h): break into phases, implement incrementally
  - RESEARCH: provide findings first

**EXECUTION**:
- Follow project patterns, include tests/docs as appropriate
- Make focused commits with clear messages
- Push final branch for orchestrator review
- For unclear scope or conflicts, request clarification before proceeding`;

        return `${userPrompt}${workerGuidance}`;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const workers = toolInput.workers as Array<Record<string, unknown>>;

            // Validate input
            if (!Array.isArray(workers)) {
                return errorToolResponse("Workers must be an array.", requestId);
            }

            if (workers.length === 0) {
                return successToolResponse(
                    JSON.stringify({ startedWorkers: [], message: "No workers to start." }),
                    requestId
                );
            }

            const results = [];

            // Process each worker configuration
            for (let i = 0; i < workers.length; i++) {
                const worker = workers[i];
                const summary = worker.summary as string;
                const initialPrompt = worker.initial_prompt as string;
                let repositoryUrl = worker.repository_url as string;
                let gitRef = worker.git_ref as string;
                const modelId = worker.model_id as string;

                try {
                    // Validate inputs for this worker
                    if (!summary || !summary.trim()) {
                        results.push({
                            index: i,
                            success: false,
                            error: "Summary cannot be empty.",
                        });
                        continue;
                    }

                    if (!initialPrompt || !initialPrompt.trim()) {
                        results.push({
                            index: i,
                            success: false,
                            error: "Initial prompt cannot be empty.",
                        });
                        continue;
                    }

                    // If repository_url or git_ref not provided, detect from current workspace
                    if (!repositoryUrl || !gitRef) {
                        try {
                            // Get git root directory
                            const gitRoot = await findGitRoot();
                            if (!gitRoot) {
                                results.push({
                                    index: i,
                                    success: false,
                                    error: "Could not find git repository. Please ensure you're in a git repository or provide repository_url and git_ref explicitly.",
                                });
                                continue;
                            }

                            // Get current repository URL if not provided
                            if (!repositoryUrl) {
                                const remoteUrlOutput = await executeCommand(
                                    "git remote get-url origin",
                                    {
                                        cwd: gitRoot,
                                    }
                                );
                                if (!remoteUrlOutput?.trim()) {
                                    results.push({
                                        index: i,
                                        success: false,
                                        error: "Could not determine repository URL. Please provide repository_url explicitly.",
                                    });
                                    continue;
                                }

                                let detectedUrl = remoteUrlOutput.trim();

                                // Convert SSH URL to HTTPS if needed
                                if (!detectedUrl.startsWith("https://")) {
                                    detectedUrl = detectedUrl
                                        .replace("ssh://", "")
                                        .replace("git@", "https://")
                                        .replace(".com:", ".com/")
                                        .replace(/\.git$/, "");
                                }

                                repositoryUrl = detectedUrl;
                            }

                            // Get current branch if not provided
                            if (!gitRef) {
                                const currentBranchOutput = await executeCommand(
                                    "git branch --show-current",
                                    {
                                        cwd: gitRoot,
                                    }
                                );
                                if (!currentBranchOutput?.trim()) {
                                    results.push({
                                        index: i,
                                        success: false,
                                        error: "Could not determine current branch. Please provide git_ref explicitly.",
                                    });
                                    continue;
                                }
                                gitRef = currentBranchOutput.trim();
                            }
                        } catch (error) {
                            this._logger.error(
                                "Failed to detect git repository information",
                                error
                            );
                            results.push({
                                index: i,
                                success: false,
                                error: "Failed to detect git repository information. Please provide repository_url and git_ref explicitly.",
                            });
                            continue;
                        }
                    }

                    // Validate repository URL format
                    try {
                        new URL(repositoryUrl);
                    } catch {
                        results.push({
                            index: i,
                            success: false,
                            error: "Invalid repository URL format.",
                        });
                        continue;
                    }

                    // Create workspace setup for remote orchestrator
                    const workspaceSetup = {
                        /* eslint-disable @typescript-eslint/naming-convention */
                        starting_files: {
                            github_commit_ref: {
                                repository_url: repositoryUrl,
                                git_ref: gitRef,
                            },
                        },
                        /* eslint-enable @typescript-eslint/naming-convention */
                    };

                    // Check GitHub authentication for remote repositories (only once)
                    if (i === 0) {
                        try {
                            const githubAuthStatus = await this._apiServer.isUserGithubConfigured();
                            if (!githubAuthStatus.is_configured) {
                                return errorToolResponse(
                                    "GitHub authentication is required to start worker agents with remote repositories. " +
                                        "Please authenticate with GitHub through the Augment settings panel.",
                                    requestId
                                );
                            }
                        } catch (error) {
                            this._logger.error(
                                "Failed to check GitHub authentication status",
                                error
                            );
                            return errorToolResponse(
                                "Failed to verify GitHub authentication. Please try again or check your connection.",
                                requestId
                            );
                        }
                    }

                    this._logger.debug(
                        `Starting worker agent ${i + 1}/${workers.length} - ${summary} - with prompt: "${initialPrompt}", repo: ${repositoryUrl}, ref: ${gitRef}`
                    );

                    const mcpServerConfigs = this._configListener.config.mcpServers
                        .filter((server) => server.type === "stdio")
                        .map((server) => ({
                            command: server.command,
                            args: server.args ?? [],
                            env: server.env ?? {},
                            /* eslint-disable @typescript-eslint/naming-convention */
                            use_shell_interpolation: server.useShellInterpolation ?? false,
                            /* eslint-enable @typescript-eslint/naming-convention */
                            name: server.name,
                            disabled: server.disabled ?? false,
                        }));

                    // Enhance the initial prompt with operational instructions
                    const enhancedPrompt = this.enhanceWorkerInstruction(
                        initialPrompt,
                        worker.branch_name as string
                    );

                    const requestDetails: RemoteAgentChatRequestDetails = {
                        /* eslint-disable @typescript-eslint/naming-convention */
                        request_nodes: [
                            {
                                id: 1,
                                type: ChatRequestNodeType.TEXT,
                                text_node: {
                                    content: enhancedPrompt,
                                },
                            },
                        ],
                        user_guidelines: this._configListener.config.chat.userGuidelines || "",
                        workspace_guidelines: "",
                        agent_memories: "",
                        mcp_servers: mcpServerConfigs,
                        /* eslint-enable @typescript-eslint/naming-convention */
                    };

                    // Get the default setup script
                    const setupScript = await this._getDefaultSetupScript();

                    // Call the create remote agent API
                    const response = await this._apiServer.createRemoteAgent(
                        workspaceSetup,
                        requestDetails,
                        modelId, // Use provided model or default
                        setupScript,
                        false // isSetupScriptAgent
                    );

                    // Check if the response indicates success
                    if (
                        !response.remote_agent_id ||
                        response.status === RemoteAgentStatus.agentFailed
                    ) {
                        this._logger.error(
                            `Failed to start worker agent ${i + 1}: status ${response.status}`
                        );
                        results.push({
                            index: i,
                            success: false,
                            error: `Failed to start worker agent: status ${response.status}`,
                        });
                        continue;
                    }

                    this._logger.debug(
                        `Successfully started worker agent ${i + 1} with ID: ${response.remote_agent_id}`
                    );

                    results.push({
                        index: i,
                        success: true,
                        summary: summary,
                        workerAgentId: response.remote_agent_id,
                        status: this._getStatusString(response.status),
                    });
                } catch (workerError: unknown) {
                    this._logger.error(`Failed to start worker agent ${i + 1}`, workerError);
                    results.push({
                        index: i,
                        success: false,
                        error: `Failed to start worker agent: ${workerError instanceof Error ? workerError.message : String(workerError)}`,
                    });
                }
            }

            // Return structured response with all results
            const structuredResponse = {
                startedWorkers: results.filter((r) => r.success),
                failedWorkers: results.filter((r) => !r.success),
                totalRequested: workers.length,
                totalStarted: results.filter((r) => r.success).length,
                totalFailed: results.filter((r) => !r.success).length,
            };

            return successToolResponse(JSON.stringify(structuredResponse), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to start worker agents", e);
            return errorToolResponse(
                `Failed to start worker agents: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Gets the default setup script using the same logic as the remote agent webview.
     */
    private async _getDefaultSetupScript(): Promise<string | undefined> {
        try {
            const lastSetupScriptPath = this._globalState.get<string>(
                GlobalContextKey.lastRemoteAgentSetupScript
            );

            if (!lastSetupScriptPath) {
                return undefined;
            }

            const setupScriptsManager = new SetupScriptsManager(this._globalState);
            const availableScripts = await setupScriptsManager.listSetupScripts();

            const selectedScript = availableScripts.find(
                (script) => script.path === lastSetupScriptPath
            );

            return selectedScript?.content;
        } catch (error) {
            this._logger.error("Failed to get default setup script", error);
            return undefined;
        }
    }

    /**
     * Convert RemoteAgentStatus enum to string
     */
    private _getStatusString(status: RemoteAgentStatus): string {
        switch (status) {
            case RemoteAgentStatus.agentUnspecified:
                return "unspecified";
            case RemoteAgentStatus.agentPending:
                return "pending";
            case RemoteAgentStatus.agentStarting:
                return "starting";
            case RemoteAgentStatus.agentRunning:
                return "running";
            case RemoteAgentStatus.agentIdle:
                return "idle";
            case RemoteAgentStatus.agentFailed:
                return "failed";
            default:
                return "unknown";
        }
    }
}

/**
 * A tool that reads the current state of a worker agent.
 *
 * IMPORTANT: This provides INSTANTANEOUS snapshots only. For waiting and monitoring completion,
 * use WaitForWorkerAgentTool instead of polling this tool repeatedly.
 *
 * Use this only for quick status checks, not for waiting patterns.
 */
export class ReadWorkerStateTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("ReadWorkerStateTool");

    constructor(private readonly _apiServer: APIServer) {
        super(LocalToolType.readWorkerState, ToolSafety.Safe);
    }

    public readonly description: string = `\
**NOT FOR WAITING** - Use WaitForWorkerAgent for waiting/monitoring.

Instant status snapshot of worker agents in Augment's orchestration system.

**WHEN TO USE**:
• Quick status checks before decisions
• Monitor SCOPE_CLARIFICATION_NEEDED responses
• Debug worker state issues

**WHEN NOT TO USE**:
• Waiting for completion (use WaitForWorkerAgent)
• Progress monitoring loops
• Polling patterns

**RETURNS**: Current state + latest output from workers

**EXAMPLE**: ReadWorkerState(worker_agent_ids=["worker-123"])`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            worker_agent_ids: {
                type: "array",
                description:
                    "Array of worker agent IDs for SINGLE SNAPSHOT CHECK ONLY. If empty array, reads state for all available worker agents. ⚠️ DO NOT use this for waiting - use WaitForWorkerAgentTool instead.",
                items: {
                    type: "string",
                    description:
                        "ID of the worker agent for immediate status snapshot ONLY (never for waiting - use WaitForWorkerAgentTool)",
                },
            },
        },
        required: ["worker_agent_ids"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const workerAgentIds = toolInput.worker_agent_ids as string[];

            // Validate input
            if (!Array.isArray(workerAgentIds)) {
                return errorToolResponse("Worker agent IDs must be an array.", requestId);
            }

            // Get all available agents with retry logic
            const agentsResponse = await backOff(() => this._apiServer.listRemoteAgents(), {
                numOfAttempts: 5,
                startingDelay: 500,
                timeMultiple: 1.5,
                maxDelay: 30000,
                retry: (e: Error, attemptNumber: number) => {
                    this._logger.warn(
                        `List agents attempt ${attemptNumber} failed: ${e.message}. Retrying...`
                    );
                    const retryableErrors = [
                        "fetch failed",
                        "network error",
                        "timeout",
                        "connection",
                        "ECONNRESET",
                        "ENOTFOUND",
                        "ETIMEDOUT",
                        "503",
                        "502",
                        "500",
                    ];
                    return retryableErrors.some((error) =>
                        e.message.toLowerCase().includes(error.toLowerCase())
                    );
                },
            });
            const allAgents = agentsResponse.remote_agents;

            let targetAgentIds: string[];

            if (workerAgentIds.length === 0) {
                // Empty array means get all agents
                targetAgentIds = allAgents.map((agent) => agent.remote_agent_id);
                this._logger.debug(`Reading state for all ${targetAgentIds.length} worker agents`);
            } else {
                // Use provided agent IDs
                targetAgentIds = workerAgentIds.filter((id) => id && id.trim());
                this._logger.debug(
                    `Reading state for ${targetAgentIds.length} specified worker agents`
                );
            }

            if (targetAgentIds.length === 0) {
                return successToolResponse(
                    JSON.stringify({
                        workerStates: [],
                        message: "No worker agents to read state for.",
                    }),
                    requestId
                );
            }

            const results = [];

            // Process each worker agent
            for (let i = 0; i < targetAgentIds.length; i++) {
                const workerAgentId = targetAgentIds[i];

                try {
                    // Find the agent
                    const agent = allAgents.find((a) => a.remote_agent_id === workerAgentId);

                    if (!agent) {
                        results.push({
                            agentId: workerAgentId,
                            success: false,
                            error: `Worker agent ${workerAgentId} not found`,
                        });
                        continue;
                    }

                    // Get latest exchange if available with retry logic
                    let latestExchange = null;
                    try {
                        const historyResponse = await backOff(
                            () =>
                                this._apiServer.getRemoteAgentChatHistory(
                                    workerAgentId,
                                    0 // Get all history
                                ),
                            {
                                numOfAttempts: 3,
                                startingDelay: 500,
                                timeMultiple: 1.5,
                                maxDelay: 10000,
                                retry: (e: Error, attemptNumber: number) => {
                                    this._logger.warn(
                                        `Chat history attempt ${attemptNumber} for ${workerAgentId} failed: ${e.message}. Retrying...`
                                    );
                                    const retryableErrors = [
                                        "fetch failed",
                                        "network error",
                                        "timeout",
                                        "connection",
                                        "ECONNRESET",
                                        "ENOTFOUND",
                                        "ETIMEDOUT",
                                        "503",
                                        "502",
                                        "500",
                                    ];
                                    return retryableErrors.some((error) =>
                                        e.message.toLowerCase().includes(error.toLowerCase())
                                    );
                                },
                            }
                        );
                        if (
                            historyResponse.chat_history &&
                            historyResponse.chat_history.length > 0
                        ) {
                            latestExchange =
                                historyResponse.chat_history[
                                    historyResponse.chat_history.length - 1
                                ];
                        }
                    } catch (error) {
                        this._logger.warn(
                            `Failed to get chat history for agent ${workerAgentId}:`,
                            error
                        );
                        // Continue without latest exchange
                    }

                    results.push({
                        agentId: workerAgentId,
                        success: true,
                        status: this._getStatusString(agent.status),
                        lastExchangeSummary: latestExchange?.turn_summary || null,
                        sessionSummary: agent.session_summary || null,
                    });
                } catch (agentError: unknown) {
                    this._logger.error(
                        `Failed to read state for worker agent ${workerAgentId}`,
                        agentError
                    );
                    results.push({
                        agentId: workerAgentId,
                        success: false,
                        error: `Failed to read worker state: ${agentError instanceof Error ? agentError.message : String(agentError)}`,
                    });
                }
            }

            // Return structured response with all results
            const structuredResponse = {
                workerStates: results.filter((r) => r.success),
                failedReads: results.filter((r) => !r.success),
                totalRequested: targetAgentIds.length,
                totalSuccessful: results.filter((r) => r.success).length,
                totalFailed: results.filter((r) => !r.success).length,
            };

            return successToolResponse(JSON.stringify(structuredResponse), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to read worker states", e);
            return errorToolResponse(
                `Failed to read worker states: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Convert RemoteAgentStatus enum to string
     */
    private _getStatusString(status: RemoteAgentStatus): string {
        switch (status) {
            case RemoteAgentStatus.agentUnspecified:
                return "unspecified";
            case RemoteAgentStatus.agentPending:
                return "pending";
            case RemoteAgentStatus.agentStarting:
                return "starting";
            case RemoteAgentStatus.agentRunning:
                return "running";
            case RemoteAgentStatus.agentIdle:
                return "idle";
            case RemoteAgentStatus.agentFailed:
                return "failed";
            default:
                return "unknown";
        }
    }
}

/**
 * A tool that waits for worker agents to complete their tasks with integrated monitoring.
 *
 * PREFERRED TOOL for waiting and monitoring worker agent completion. This replaces the need
 * to repeatedly poll ReadWorkerStateTool - use this for all waiting scenarios.
 *
 * Provides built-in progress monitoring with 2-second intervals and configurable timeouts.
 */
export class WaitForWorkerAgentTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("WaitForWorkerAgentTool");

    constructor(private readonly _apiServer: APIServer) {
        super(LocalToolType.waitForWorkerAgent, ToolSafety.Safe);
    }

    public readonly description: string = `\
**PRIMARY WAITING TOOL** - Blocks until workers complete. Never use ReadWorkerState polling.

Wait for worker completion in Augment's orchestration system with network resilience.

**MANDATORY FOR**: All waiting after StartWorkerAgent
**OPERATION**: Blocks with exponential backoff, streaming updates when available

**TIMEOUT GUIDANCE**:
• Simple tasks: 300+ seconds
• Complex tasks: 600+ seconds
• Research/analysis: 1200+ seconds

**WORKFLOW**: StartWorkerAgent → **WaitForWorkerAgent** → ReadWorkerAgentEdits → ApplyWorkerAgentEdits (auto-cleanup)

**EXAMPLE**: WaitForWorkerAgent(wait_operations=[{worker_agent_id: "worker-123", target_status: "idle", timeout_seconds: 600}])`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            wait_operations: {
                type: "array",
                description:
                    "Array of wait operations for integrated monitoring and waiting. This is the ONLY tool for waiting - do NOT use ReadWorkerStateTool polling loops.",
                items: {
                    type: "object",
                    properties: {
                        worker_agent_id: {
                            type: "string",
                            description:
                                "ID of the worker agent to wait for completion with integrated monitoring",
                        },
                        target_status: {
                            type: "string",
                            enum: ["idle", "failed"],
                            description:
                                "Status to wait for (idle=completed successfully, failed=error occurred)",
                        },
                        timeout_seconds: {
                            type: "number",
                            description:
                                "Maximum time to wait in seconds (default: 600). Use 300+ for simple tasks, 600+ for complex tasks, 1200+ for very complex work.",
                        },
                    },
                    required: ["worker_agent_id", "target_status"],
                },
            },
        },
        required: ["wait_operations"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const waitOperations = toolInput.wait_operations as Array<Record<string, unknown>>;

            // Validate input
            if (!Array.isArray(waitOperations)) {
                return errorToolResponse("Wait operations must be an array.", requestId);
            }

            if (waitOperations.length === 0) {
                return successToolResponse(
                    JSON.stringify({ waitResults: [], message: "No wait operations to perform." }),
                    requestId
                );
            }

            // Validate each operation
            for (let i = 0; i < waitOperations.length; i++) {
                const operation = waitOperations[i];
                const workerAgentId = operation.worker_agent_id as string;
                const targetStatus = operation.target_status as string;

                if (!workerAgentId || !workerAgentId.trim()) {
                    return errorToolResponse(
                        `Wait operation ${i}: Worker agent ID cannot be empty.`,
                        requestId
                    );
                }

                if (!["idle", "failed"].includes(targetStatus)) {
                    return errorToolResponse(
                        `Wait operation ${i}: Target status must be 'idle' or 'failed'.`,
                        requestId
                    );
                }
            }

            this._logger.debug(`Starting ${waitOperations.length} wait operations`);

            // Create promises for each wait operation
            const waitPromises = waitOperations.map((operation, index) => {
                const workerAgentId = operation.worker_agent_id as string;
                const targetStatus = operation.target_status as string;
                const timeoutSeconds = (operation.timeout_seconds as number) || 600;

                return backOff(
                    () =>
                        this._waitForAgentStatusWithStreaming(
                            workerAgentId,
                            targetStatus,
                            timeoutSeconds,
                            index,
                            abortSignal
                        ),
                    {
                        numOfAttempts: 8,
                        startingDelay: 500,
                        timeMultiple: 1.5,
                        maxDelay: 30000, // Maximum 30 seconds total backoff time
                        retry: (e: Error, attemptNumber: number) => {
                            this._logger.warn(
                                `Wait attempt ${attemptNumber} for ${workerAgentId} failed: ${e.message}. Retrying...`
                            );
                            // Retry on various network and API errors
                            const retryableErrors = [
                                "fetch failed",
                                "stream ended",
                                "network error",
                                "timeout",
                                "connection",
                                "ECONNRESET",
                                "ENOTFOUND",
                                "ETIMEDOUT",
                                "503",
                                "502",
                                "500",
                            ];
                            return retryableErrors.some((error) =>
                                e.message.toLowerCase().includes(error.toLowerCase())
                            );
                        },
                    }
                );
            });

            // Wait for all operations to complete
            const results = await Promise.all(waitPromises);

            // Return structured response with all results
            const structuredResponse = {
                waitResults: results.filter((r) => r.success),
                failedWaits: results.filter((r) => !r.success),
                totalOperations: waitOperations.length,
                totalSuccessful: results.filter((r) => r.success).length,
                totalFailed: results.filter((r) => !r.success).length,
            };

            return successToolResponse(JSON.stringify(structuredResponse), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to wait for worker agents", e);
            return errorToolResponse(
                `Failed to wait for worker agents: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Wait for an agent to reach a target status using streaming API with fallback to polling.
     * This method first tries to use the streaming API for efficient waiting, and falls back
     * to polling if streaming fails or is not available.
     */
    private async _waitForAgentStatusWithStreaming(
        workerAgentId: string,
        targetStatus: string,
        timeoutSeconds: number,
        index: number,
        abortSignal: AbortSignal
    ): Promise<{
        index: number;
        agentId: string;
        success: boolean;
        status?: string;
        targetStatus?: string;
        waitedSeconds?: number;
        error?: string;
    }> {
        const startTime = Date.now();
        const timeoutMs = timeoutSeconds * 1000;

        try {
            // First, check if the agent already has the target status
            const initialCheck = await this._apiServer.listRemoteAgents();
            const initialAgent = initialCheck.remote_agents.find(
                (a) => a.remote_agent_id === workerAgentId
            );

            if (!initialAgent) {
                return {
                    index,
                    agentId: workerAgentId,
                    success: false,
                    error: `Worker agent ${workerAgentId} not found`,
                };
            }

            const initialStatus = this._getStatusString(initialAgent.status);
            if (initialStatus === targetStatus) {
                return {
                    index,
                    agentId: workerAgentId,
                    success: true,
                    status: initialStatus,
                    targetStatus: targetStatus,
                    waitedSeconds: 0,
                };
            }

            // Try streaming approach first
            try {
                return await this._waitUsingStreaming(
                    workerAgentId,
                    targetStatus,
                    timeoutMs,
                    startTime,
                    index,
                    abortSignal
                );
            } catch (streamError) {
                this._logger.warn(
                    `Streaming failed for agent ${workerAgentId}, falling back to polling:`,
                    streamError
                );

                // Fall back to polling with reduced frequency (10 seconds)
                return await this._waitUsingPolling(
                    workerAgentId,
                    targetStatus,
                    timeoutMs,
                    startTime,
                    index,
                    abortSignal,
                    10000 // 10 second intervals
                );
            }
        } catch (operationError: unknown) {
            return {
                index,
                agentId: workerAgentId,
                success: false,
                error: `Failed to wait for worker agent: ${operationError instanceof Error ? operationError.message : String(operationError)}`,
            };
        }
    }

    /**
     * Wait for agent status using the streaming API for efficient real-time updates.
     */
    private async _waitUsingStreaming(
        workerAgentId: string,
        targetStatus: string,
        timeoutMs: number,
        startTime: number,
        index: number,
        abortSignal: AbortSignal
    ): Promise<{
        index: number;
        agentId: string;
        success: boolean;
        status?: string;
        targetStatus?: string;
        waitedSeconds?: number;
        error?: string;
    }> {
        // Create an AbortController that combines the provided signal with our timeout
        const timeoutController = new AbortController();
        const combinedSignal = AbortSignal.any([abortSignal, timeoutController.signal]);

        // Set up timeout
        const timeoutId = setTimeout(() => {
            timeoutController.abort();
        }, timeoutMs);

        try {
            // Start streaming agent updates
            const stream = this._apiServer.getRemoteAgentOverviewsStream(undefined, combinedSignal);

            for await (const update of stream) {
                if (combinedSignal.aborted) {
                    if (abortSignal.aborted) {
                        return {
                            index,
                            agentId: workerAgentId,
                            success: false,
                            error: "Wait operation was cancelled.",
                        };
                    } else {
                        // Timeout
                        return {
                            index,
                            agentId: workerAgentId,
                            success: false,
                            error: `Timeout waiting for worker agent ${workerAgentId} to reach status ${targetStatus}`,
                        };
                    }
                }

                // Process each update in the response
                for (const agentUpdate of update.updates) {
                    if (agentUpdate.agent && agentUpdate.agent.remote_agent_id === workerAgentId) {
                        const currentStatus = this._getStatusString(agentUpdate.agent.status);

                        if (currentStatus === targetStatus) {
                            return {
                                index,
                                agentId: workerAgentId,
                                success: true,
                                status: currentStatus,
                                targetStatus: targetStatus,
                                waitedSeconds: Math.round((Date.now() - startTime) / 1000),
                            };
                        }
                    }
                }
            }

            // Stream ended without finding target status
            return {
                index,
                agentId: workerAgentId,
                success: false,
                error: `Stream ended before worker agent ${workerAgentId} reached status ${targetStatus}`,
            };
        } finally {
            clearTimeout(timeoutId);
        }
    }

    /**
     * Wait for agent status using polling as a fallback mechanism.
     */
    private async _waitUsingPolling(
        workerAgentId: string,
        targetStatus: string,
        timeoutMs: number,
        startTime: number,
        index: number,
        abortSignal: AbortSignal,
        pollIntervalMs: number = 10000 // Default to 10 seconds
    ): Promise<{
        index: number;
        agentId: string;
        success: boolean;
        status?: string;
        targetStatus?: string;
        waitedSeconds?: number;
        error?: string;
    }> {
        while (Date.now() - startTime < timeoutMs) {
            if (abortSignal.aborted) {
                return {
                    index,
                    agentId: workerAgentId,
                    success: false,
                    error: "Wait operation was cancelled.",
                };
            }

            // Check current status
            const agentsResponse = await this._apiServer.listRemoteAgents();
            const agent = agentsResponse.remote_agents.find(
                (a) => a.remote_agent_id === workerAgentId
            );

            if (!agent) {
                return {
                    index,
                    agentId: workerAgentId,
                    success: false,
                    error: `Worker agent ${workerAgentId} not found`,
                };
            }

            const currentStatus = this._getStatusString(agent.status);

            if (currentStatus === targetStatus) {
                return {
                    index,
                    agentId: workerAgentId,
                    success: true,
                    status: currentStatus,
                    targetStatus: targetStatus,
                    waitedSeconds: Math.round((Date.now() - startTime) / 1000),
                };
            }

            // Wait before next check
            await delayMs(pollIntervalMs);
        }

        // Timeout reached
        return {
            index,
            agentId: workerAgentId,
            success: false,
            error: `Timeout waiting for worker agent ${workerAgentId} to reach status ${targetStatus}`,
        };
    }

    /**
     * Convert RemoteAgentStatus enum to string
     */
    private _getStatusString(status: RemoteAgentStatus): string {
        switch (status) {
            case RemoteAgentStatus.agentUnspecified:
                return "unspecified";
            case RemoteAgentStatus.agentPending:
                return "pending";
            case RemoteAgentStatus.agentStarting:
                return "starting";
            case RemoteAgentStatus.agentRunning:
                return "running";
            case RemoteAgentStatus.agentIdle:
                return "idle";
            case RemoteAgentStatus.agentFailed:
                return "failed";
            default:
                return "unknown";
        }
    }
}

/**
 * A tool that sends an instruction to a worker agent.
 * Remember to retrieve the worker's completed work manually using ReadWorkerAgentEditsTool.
 */
export class SendInstructionToWorkerAgentTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("SendInstructionToWorkerAgentTool");

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _configListener: AugmentConfigListener
    ) {
        super(LocalToolType.sendInstructionToWorkerAgent, ToolSafety.Unsafe);
    }

    public readonly description: string = `\
Send additional instructions to running workers. **Workers have NO conversation context - provide complete information.**

**WHEN TO USE**:
• Respond to SCOPE_CLARIFICATION_NEEDED
• Provide additional requirements
• Course-correct ongoing work
• Resolve conflicts or ambiguities

**OPERATION**: WRITE (sends instruction, returns immediately)

**CONTEXT REQUIREMENTS**: Include full context in instruction:
• Reference to original task
• New requirements or changes
• Updated success criteria
• Any relevant background

**MONITOR FOR**: SCOPE_CHANGE_DETECTED, CONFLICT_DETECTED, CLARIFICATION_NEEDED responses

**EXAMPLE**: SendInstructionToWorkerAgent(instruction_operations=[{worker_agent_id: "worker-123", summary: "Add validation", instruction: "CONTEXT: You're working on JWT auth for Augment VSCode extension. CHANGE: Also add input validation to the login form - validate email format and password strength. CRITERIA: Form should show clear error messages for invalid inputs."}])`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            instruction_operations: {
                type: "array",
                description:
                    "Array of instruction operations to perform. If empty array, no instructions will be sent.",
                items: {
                    type: "object",
                    properties: {
                        summary: {
                            type: "string",
                            description:
                                "A short description of what this instruction is asking the worker agent to do (results will need to be manually retrieved later)",
                        },
                        worker_agent_id: {
                            type: "string",
                            description: "ID of the worker agent to send instructions to",
                        },
                        instruction: {
                            type: "string",
                            description:
                                "Instruction to send to the worker agent. The worker will complete this task and you must later retrieve the results using ReadWorkerAgentEditsTool to pull changes into your local workspace.",
                        },
                    },
                    required: ["summary", "worker_agent_id", "instruction"],
                },
            },
        },
        required: ["instruction_operations"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    private enhanceAdditionalInstruction(instruction: string): string {
        const additionalGuidance = `

[augment-swarm-worker] Additional instruction from orchestrator via SendInstructionToWorkerAgent.

**ASSESS AGAINST CURRENT WORK**:
• **SCOPE_CHANGE_DETECTED**: "SCOPE_CHANGE_DETECTED: [impact description]"
• **CONFLICT_DETECTED**: "CONFLICT_DETECTED: [conflict + proposed resolution]"
• **CLARIFICATION_NEEDED**: "CLARIFICATION_NEEDED: [specific questions]"
• **INTEGRATION_PROCEEDING**: If clear and compatible, integrate with existing work

**EXECUTION**: Make additional commits, maintain consistency, push updates for orchestrator review`;

        return `${instruction}${additionalGuidance}`;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const instructionOperations = toolInput.instruction_operations as Array<
                Record<string, unknown>
            >;

            // Validate input
            if (!Array.isArray(instructionOperations)) {
                return errorToolResponse("Instruction operations must be an array.", requestId);
            }

            if (instructionOperations.length === 0) {
                return successToolResponse(
                    JSON.stringify({
                        instructionResults: [],
                        message: "No instruction operations to perform.",
                    }),
                    requestId
                );
            }

            // Get all available agents once
            const agentsResponse = await this._apiServer.listRemoteAgents();
            const allAgents = agentsResponse.remote_agents;

            const results = [];

            // Process each instruction operation
            for (let i = 0; i < instructionOperations.length; i++) {
                const operation = instructionOperations[i];
                const summary = operation.summary as string;
                const workerAgentId = operation.worker_agent_id as string;
                const instruction = operation.instruction as string;

                try {
                    // Validate inputs for this operation
                    if (!summary || !summary.trim()) {
                        results.push({
                            index: i,
                            agentId: workerAgentId,
                            success: false,
                            error: "Summary cannot be empty.",
                        });
                        continue;
                    }

                    if (!workerAgentId || !workerAgentId.trim()) {
                        results.push({
                            index: i,
                            agentId: workerAgentId,
                            success: false,
                            error: "Worker agent ID cannot be empty.",
                        });
                        continue;
                    }

                    if (!instruction || !instruction.trim()) {
                        results.push({
                            index: i,
                            agentId: workerAgentId,
                            success: false,
                            error: "Instruction cannot be empty.",
                        });
                        continue;
                    }

                    this._logger.debug(
                        `Sending instruction ${i + 1}/${instructionOperations.length} to worker agent ${workerAgentId} - ${summary}: "${instruction}"`
                    );

                    // Check if agent exists
                    const agent = allAgents.find((a) => a.remote_agent_id === workerAgentId);

                    if (!agent) {
                        results.push({
                            index: i,
                            agentId: workerAgentId,
                            success: false,
                            error: `Worker agent ${workerAgentId} not found`,
                        });
                        continue;
                    }

                    const mcpServerConfigs = this._configListener.config.mcpServers
                        .filter((server) => server.type === "stdio")
                        .map((server) => ({
                            command: server.command,
                            args: server.args ?? [],
                            env: server.env ?? {},
                            /* eslint-disable @typescript-eslint/naming-convention */
                            use_shell_interpolation: server.useShellInterpolation ?? false,
                            /* eslint-enable @typescript-eslint/naming-convention */
                            name: server.name,
                            disabled: server.disabled ?? false,
                        }));

                    // Enhance the instruction with additional context
                    const enhancedInstruction = this.enhanceAdditionalInstruction(instruction);

                    const requestDetails: RemoteAgentChatRequestDetails = {
                        /* eslint-disable @typescript-eslint/naming-convention */
                        request_nodes: [
                            {
                                id: 1,
                                type: ChatRequestNodeType.TEXT,
                                text_node: {
                                    content: enhancedInstruction,
                                },
                            },
                        ],
                        user_guidelines: this._configListener.config.chat.userGuidelines || "",
                        workspace_guidelines: "",
                        agent_memories: "",
                        mcp_servers: mcpServerConfigs,
                        /* eslint-enable @typescript-eslint/naming-convention */
                    };

                    // Send the instruction
                    await this._apiServer.remoteAgentChat(workerAgentId, requestDetails);

                    this._logger.debug(
                        `Successfully sent instruction ${i + 1} to worker agent ${workerAgentId}`
                    );

                    results.push({
                        index: i,
                        agentId: workerAgentId,
                        success: true,
                        summary: summary,
                        instructionSent: instruction,
                    });
                } catch (operationError: unknown) {
                    this._logger.error(
                        `Failed to send instruction ${i + 1} to worker agent ${workerAgentId}`,
                        operationError
                    );
                    results.push({
                        index: i,
                        agentId: workerAgentId,
                        success: false,
                        error: `Failed to send instruction: ${operationError instanceof Error ? operationError.message : String(operationError)}`,
                    });
                }
            }

            // Return structured response with all results
            const structuredResponse = {
                instructionResults: results.filter((r) => r.success),
                failedInstructions: results.filter((r) => !r.success),
                totalOperations: instructionOperations.length,
                totalSuccessful: results.filter((r) => r.success).length,
                totalFailed: results.filter((r) => !r.success).length,
            };

            return successToolResponse(JSON.stringify(structuredResponse), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to send instructions to worker agents", e);
            return errorToolResponse(
                `Failed to send instructions to worker agents: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }
}

/**
 * A tool that stops a worker agent.
 * WARNING: Retrieve worker changes first using ReadWorkerAgentEditsTool to avoid losing work.
 */
export class StopWorkerAgentTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("StopWorkerAgentTool");

    constructor(private readonly _apiServer: APIServer) {
        super(LocalToolType.stopWorkerAgent, ToolSafety.Unsafe);
    }

    public readonly description: string = `\
**WARNING**: Retrieve work with ReadWorkerAgentEdits BEFORE stopping to avoid data loss.

Stop/interrupt running workers in Augment's orchestration system with verification and retry.

**WHEN TO USE**:
• Cancel incorrect/misdirected work
• Emergency resource cleanup
• Abort failed tasks

**OPERATION**: WRITE (stops agents immediately, cannot restart)

**COMPREHENSIVE CLEANUP**: Automatically verifies all workers are stopped and retries up to 2 times if any remain running.

**VERIFICATION**: After stopping, checks that workers are actually stopped and provides detailed status.

**CRITICAL**: Always use ReadWorkerAgentEdits first to collect any completed work.

**EXAMPLE**: StopWorkerAgent(worker_agent_ids=["worker-123"])`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            worker_agent_ids: {
                type: "array",
                description:
                    "Array of worker agent IDs to stop. If empty array, stops all available worker agents.",
                items: {
                    type: "string",
                    description:
                        "ID of the worker agent to stop (ensure you've retrieved any needed changes first)",
                },
            },
        },
        required: ["worker_agent_ids"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const workerAgentIds = toolInput.worker_agent_ids as string[];

            // Validate input
            if (!Array.isArray(workerAgentIds)) {
                return errorToolResponse("Worker agent IDs must be an array.", requestId);
            }

            // Get all available agents
            const agentsResponse = await this._apiServer.listRemoteAgents();
            const allAgents = agentsResponse.remote_agents;

            let targetAgentIds: string[];

            if (workerAgentIds.length === 0) {
                // Empty array means stop all agents
                targetAgentIds = allAgents.map((agent) => agent.remote_agent_id);
                this._logger.debug(`Stopping all ${targetAgentIds.length} worker agents`);
            } else {
                // Use provided agent IDs
                targetAgentIds = workerAgentIds.filter((id) => id && id.trim());
                this._logger.debug(`Stopping ${targetAgentIds.length} specified worker agents`);
            }

            if (targetAgentIds.length === 0) {
                return successToolResponse(
                    JSON.stringify({ stopResults: [], message: "No worker agents to stop." }),
                    requestId
                );
            }

            const results = [];

            // Process each worker agent
            for (let i = 0; i < targetAgentIds.length; i++) {
                const workerAgentId = targetAgentIds[i];

                try {
                    // Check if agent exists
                    const agent = allAgents.find((a) => a.remote_agent_id === workerAgentId);

                    if (!agent) {
                        results.push({
                            agentId: workerAgentId,
                            success: false,
                            error: `Worker agent ${workerAgentId} not found`,
                        });
                        continue;
                    }

                    this._logger.debug(
                        `Stopping worker agent ${i + 1}/${targetAgentIds.length}: ${workerAgentId}`
                    );

                    // Stop the agent
                    await this._apiServer.interruptRemoteAgent(workerAgentId);

                    this._logger.debug(`Successfully stopped worker agent: ${workerAgentId}`);

                    results.push({
                        agentId: workerAgentId,
                        success: true,
                    });
                } catch (operationError: unknown) {
                    this._logger.error(
                        `Failed to stop worker agent ${workerAgentId}`,
                        operationError
                    );
                    results.push({
                        agentId: workerAgentId,
                        success: false,
                        error: `Failed to stop worker agent: ${operationError instanceof Error ? operationError.message : String(operationError)}`,
                    });
                }
            }

            // Verify that workers were actually stopped and retry if needed
            const verificationResult = await this._verifyAndRetryStopWorkers(
                targetAgentIds,
                results
            );

            // Return comprehensive response with verification results
            const structuredResponse = {
                stopResults: results.filter((r) => r.success),
                failedStops: results.filter((r) => !r.success),
                totalRequested: targetAgentIds.length,
                totalSuccessful: results.filter((r) => r.success).length,
                totalFailed: results.filter((r) => !r.success).length,
                verification: verificationResult,
            };

            return successToolResponse(JSON.stringify(structuredResponse), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to stop worker agents", e);
            return errorToolResponse(
                `Failed to stop worker agents: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Verify that workers were actually stopped and retry if needed
     */
    private async _verifyAndRetryStopWorkers(
        originalTargetIds: string[],
        initialResults: Array<{ agentId: string; success: boolean; error?: string }>
    ): Promise<{
        verified: boolean;
        remainingWorkers: string[];
        retryAttempts: number;
        retryResults?: Array<{ agentId: string; success: boolean; error?: string }>;
        message: string;
    }> {
        const maxRetries = 2;
        let retryAttempts = 0;
        let remainingWorkers: string[] = [];
        let retryResults: Array<{ agentId: string; success: boolean; error?: string }> = [];

        // Check which workers are still running
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // Small delay to allow operations to complete
                if (attempt > 0) {
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                }

                const agentsResponse = await this._apiServer.listRemoteAgents();
                const currentWorkers = agentsResponse.remote_agents.map((a) => a.remote_agent_id);

                // Find workers that should have been stopped but are still running
                remainingWorkers = originalTargetIds.filter(
                    (id) =>
                        currentWorkers.includes(id) &&
                        initialResults.some((r) => r.agentId === id && r.success)
                );

                if (remainingWorkers.length === 0) {
                    // All workers successfully stopped
                    return {
                        verified: true,
                        remainingWorkers: [],
                        retryAttempts: attempt,
                        message: `✅ All ${originalTargetIds.length} workers verified as stopped`,
                    };
                }

                if (attempt < maxRetries) {
                    // Retry stopping the remaining workers
                    retryAttempts = attempt + 1;
                    this._logger.warn(
                        `Verification failed: ${remainingWorkers.length} workers still running. Retry attempt ${retryAttempts}/${maxRetries}`
                    );

                    for (const workerId of remainingWorkers) {
                        try {
                            await this._apiServer.interruptRemoteAgent(workerId);
                            retryResults.push({ agentId: workerId, success: true });
                        } catch (error) {
                            retryResults.push({
                                agentId: workerId,
                                success: false,
                                error: error instanceof Error ? error.message : String(error),
                            });
                        }
                    }
                }
            } catch (error) {
                this._logger.error(`Verification attempt ${attempt} failed:`, error);
                if (attempt === maxRetries) {
                    return {
                        verified: false,
                        remainingWorkers: originalTargetIds,
                        retryAttempts: attempt,
                        message: `❌ Verification failed: Unable to check worker status after ${attempt} attempts`,
                    };
                }
            }
        }

        // Final verification failed
        return {
            verified: false,
            remainingWorkers,
            retryAttempts,
            retryResults: retryResults.length > 0 ? retryResults : undefined,
            message: `⚠️ Verification incomplete: ${remainingWorkers.length} workers may still be running after ${retryAttempts} retry attempts`,
        };
    }
}

/**
 * A tool that deletes a worker agent.
 * WARNING: Retrieve worker changes first using ReadWorkerAgentEditsTool to avoid permanently losing work.
 */
export class DeleteWorkerAgentTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("DeleteWorkerAgentTool");

    constructor(private readonly _apiServer: APIServer) {
        super(LocalToolType.deleteWorkerAgent, ToolSafety.Unsafe);
    }

    public readonly description: string = `\
**NOTE**: Workers are now automatically deleted by ApplyWorkerAgentEdits. Manual deletion is rarely needed.

Delete workers and clean up resources in Augment's orchestration system with verification and retry.

**WHEN TO USE**:
• Error recovery (when ApplyWorkerAgentEdits fails)
• Manual cleanup of abandoned workers
• Force cleanup without applying changes

**OPERATION**: WRITE (permanent deletion, cannot recover)

**COMPREHENSIVE CLEANUP**: Automatically verifies all workers are deleted and retries up to 2 times if any remain.

**VERIFICATION**: After deletion, checks that workers are actually removed and provides detailed status.

**AUTOMATIC CLEANUP**: ApplyWorkerAgentEdits now handles deletion automatically (set auto_delete=false to disable).

**WORKFLOW**: Typically automatic via ApplyWorkerAgentEdits, or manual: ReadWorkerAgentEdits → DeleteWorkerAgent

**EXAMPLE**: DeleteWorkerAgent(worker_agent_ids=["worker-123"])`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            worker_agent_ids: {
                type: "array",
                description:
                    "Array of worker agent IDs to delete. If empty array, deletes all available worker agents.",
                items: {
                    type: "string",
                    description:
                        "ID of the worker agent to delete (ensure you've retrieved any needed changes first)",
                },
            },
        },
        required: ["worker_agent_ids"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const workerAgentIds = toolInput.worker_agent_ids as string[];

            // Validate input
            if (!Array.isArray(workerAgentIds)) {
                return errorToolResponse("Worker agent IDs must be an array.", requestId);
            }

            // Get all available agents
            const agentsResponse = await this._apiServer.listRemoteAgents();
            const allAgents = agentsResponse.remote_agents;

            let targetAgentIds: string[];

            if (workerAgentIds.length === 0) {
                // Empty array means delete all agents
                targetAgentIds = allAgents.map((agent) => agent.remote_agent_id);
                this._logger.debug(`Deleting all ${targetAgentIds.length} worker agents`);
            } else {
                // Use provided agent IDs
                targetAgentIds = workerAgentIds.filter((id) => id && id.trim());
                this._logger.debug(`Deleting ${targetAgentIds.length} specified worker agents`);
            }

            if (targetAgentIds.length === 0) {
                return successToolResponse(
                    JSON.stringify({ deleteResults: [], message: "No worker agents to delete." }),
                    requestId
                );
            }

            const results = [];

            // Process each worker agent
            for (let i = 0; i < targetAgentIds.length; i++) {
                const workerAgentId = targetAgentIds[i];

                try {
                    // Check if agent exists
                    const agent = allAgents.find((a) => a.remote_agent_id === workerAgentId);

                    if (!agent) {
                        results.push({
                            agentId: workerAgentId,
                            success: false,
                            error: `Worker agent ${workerAgentId} not found`,
                        });
                        continue;
                    }

                    this._logger.debug(
                        `Deleting worker agent ${i + 1}/${targetAgentIds.length}: ${workerAgentId}`
                    );

                    // Delete the agent
                    await this._apiServer.deleteRemoteAgent(workerAgentId);

                    this._logger.debug(`Successfully deleted worker agent: ${workerAgentId}`);

                    results.push({
                        agentId: workerAgentId,
                        success: true,
                    });
                } catch (operationError: unknown) {
                    this._logger.error(
                        `Failed to delete worker agent ${workerAgentId}`,
                        operationError
                    );
                    results.push({
                        agentId: workerAgentId,
                        success: false,
                        error: `Failed to delete worker agent: ${operationError instanceof Error ? operationError.message : String(operationError)}`,
                    });
                }
            }

            // Verify that workers were actually deleted and retry if needed
            const verificationResult = await this._verifyAndRetryDeleteWorkers(
                targetAgentIds,
                results
            );

            // Return comprehensive response with verification results
            const structuredResponse = {
                deleteResults: results.filter((r) => r.success),
                failedDeletes: results.filter((r) => !r.success),
                totalRequested: targetAgentIds.length,
                totalSuccessful: results.filter((r) => r.success).length,
                totalFailed: results.filter((r) => !r.success).length,
                verification: verificationResult,
            };

            return successToolResponse(JSON.stringify(structuredResponse), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to delete worker agents", e);
            return errorToolResponse(
                `Failed to delete worker agents: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Verify that workers were actually deleted and retry if needed
     */
    private async _verifyAndRetryDeleteWorkers(
        originalTargetIds: string[],
        initialResults: Array<{ agentId: string; success: boolean; error?: string }>
    ): Promise<{
        verified: boolean;
        remainingWorkers: string[];
        retryAttempts: number;
        retryResults?: Array<{ agentId: string; success: boolean; error?: string }>;
        message: string;
    }> {
        const maxRetries = 2;
        let retryAttempts = 0;
        let remainingWorkers: string[] = [];
        let retryResults: Array<{ agentId: string; success: boolean; error?: string }> = [];

        // Check which workers still exist
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // Small delay to allow operations to complete
                if (attempt > 0) {
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                }

                const agentsResponse = await this._apiServer.listRemoteAgents();
                const currentWorkers = agentsResponse.remote_agents.map((a) => a.remote_agent_id);

                // Find workers that should have been deleted but still exist
                remainingWorkers = originalTargetIds.filter(
                    (id) =>
                        currentWorkers.includes(id) &&
                        initialResults.some((r) => r.agentId === id && r.success)
                );

                if (remainingWorkers.length === 0) {
                    // All workers successfully deleted
                    return {
                        verified: true,
                        remainingWorkers: [],
                        retryAttempts: attempt,
                        message: `✅ All ${originalTargetIds.length} workers verified as deleted`,
                    };
                }

                if (attempt < maxRetries) {
                    // Retry deleting the remaining workers
                    retryAttempts = attempt + 1;
                    this._logger.warn(
                        `Verification failed: ${remainingWorkers.length} workers still exist. Retry attempt ${retryAttempts}/${maxRetries}`
                    );

                    for (const workerId of remainingWorkers) {
                        try {
                            await this._apiServer.deleteRemoteAgent(workerId);
                            retryResults.push({ agentId: workerId, success: true });
                        } catch (error) {
                            retryResults.push({
                                agentId: workerId,
                                success: false,
                                error: error instanceof Error ? error.message : String(error),
                            });
                        }
                    }
                }
            } catch (error) {
                this._logger.error(`Verification attempt ${attempt} failed:`, error);
                if (attempt === maxRetries) {
                    return {
                        verified: false,
                        remainingWorkers: originalTargetIds,
                        retryAttempts: attempt,
                        message: `❌ Verification failed: Unable to check worker status after ${attempt} attempts`,
                    };
                }
            }
        }

        // Final verification failed
        return {
            verified: false,
            remainingWorkers,
            retryAttempts,
            retryResults: retryResults.length > 0 ? retryResults : undefined,
            message: `⚠️ Verification incomplete: ${remainingWorkers.length} workers may still exist after ${retryAttempts} retry attempts`,
        };
    }
}

/**
 * A tool that retrieves the file changes made by a worker agent.
 * This is the primary tool for collecting worker results that must be manually applied to your local workspace.
 */
export class ReadWorkerAgentEditsTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("ReadWorkerAgentEditsTool");

    constructor(private readonly _apiServer: APIServer) {
        super(LocalToolType.readWorkerAgentEdits, ToolSafety.Safe);
    }

    public readonly description: string = `\
Retrieve file changes made by worker agents for review and manual application.

**OPERATION**: READ (instant, non-blocking) - returns file changes immediately

**RETURNS**:
• File diffs showing changes made by workers
• Added/modified/deleted files
• Git commit information from worker branches
• Structured data ready for review

**WORKFLOW**: WaitForWorkerAgent → **ReadWorkerAgentEdits** → ApplyWorkerAgentEdits (auto-cleanup)

**CRITICAL**: You must manually review and apply changes - workers cannot directly modify your workspace.

**EXAMPLE**: ReadWorkerAgentEdits(worker_agent_ids=["worker-123"])`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            worker_agent_ids: {
                type: "array",
                description:
                    "Array of worker agent IDs whose completed file changes you want to collect for manual integration into your local workspace. If empty array, reads edits for all available worker agents.",
                items: {
                    type: "string",
                    description:
                        "ID of the worker agent whose file changes you want to retrieve and manually apply",
                },
            },
        },
        required: ["worker_agent_ids"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            const workerAgentIds = toolInput.worker_agent_ids as string[];

            // Validate input
            if (!Array.isArray(workerAgentIds)) {
                return errorToolResponse("Worker agent IDs must be an array.", requestId);
            }

            // Get all available agents with retry logic
            const agentsResponse = await backOff(() => this._apiServer.listRemoteAgents(), {
                numOfAttempts: 5,
                startingDelay: 500,
                timeMultiple: 1.5,
                maxDelay: 30000,
                retry: (e: Error, attemptNumber: number) => {
                    this._logger.warn(
                        `List agents attempt ${attemptNumber} failed: ${e.message}. Retrying...`
                    );
                    const retryableErrors = [
                        "fetch failed",
                        "network error",
                        "timeout",
                        "connection",
                        "ECONNRESET",
                        "ENOTFOUND",
                        "ETIMEDOUT",
                        "503",
                        "502",
                        "500",
                    ];
                    return retryableErrors.some((error) =>
                        e.message.toLowerCase().includes(error.toLowerCase())
                    );
                },
            });
            const allAgents = agentsResponse.remote_agents;

            let targetAgentIds: string[];

            if (workerAgentIds.length === 0) {
                // Empty array means read edits for all agents
                targetAgentIds = allAgents.map((agent) => agent.remote_agent_id);
                this._logger.debug(`Reading edits for all ${targetAgentIds.length} worker agents`);
            } else {
                // Use provided agent IDs
                targetAgentIds = workerAgentIds.filter((id) => id && id.trim());
                this._logger.debug(
                    `Reading edits for ${targetAgentIds.length} specified worker agents`
                );
            }

            if (targetAgentIds.length === 0) {
                return successToolResponse(
                    JSON.stringify({
                        editResults: [],
                        message: "No worker agents to read edits for.",
                    }),
                    requestId
                );
            }

            const results = [];

            // Process each worker agent
            for (let i = 0; i < targetAgentIds.length; i++) {
                const workerAgentId = targetAgentIds[i];

                try {
                    // Check if agent exists
                    const agent = allAgents.find((a) => a.remote_agent_id === workerAgentId);

                    if (!agent) {
                        results.push({
                            agentId: workerAgentId,
                            success: false,
                            error: `Worker agent ${workerAgentId} not found`,
                        });
                        continue;
                    }

                    this._logger.debug(
                        `Reading edits for worker agent ${i + 1}/${targetAgentIds.length}: ${workerAgentId}`
                    );

                    // Get chat history to extract file edits with retry logic
                    const historyResponse = await backOff(
                        () =>
                            this._apiServer.getRemoteAgentChatHistory(
                                workerAgentId,
                                0 // Get all history
                            ),
                        {
                            numOfAttempts: 5,
                            startingDelay: 500,
                            timeMultiple: 1.5,
                            maxDelay: 30000,
                            retry: (e: Error, attemptNumber: number) => {
                                this._logger.warn(
                                    `Chat history attempt ${attemptNumber} for ${workerAgentId} failed: ${e.message}. Retrying...`
                                );
                                const retryableErrors = [
                                    "fetch failed",
                                    "network error",
                                    "timeout",
                                    "connection",
                                    "ECONNRESET",
                                    "ENOTFOUND",
                                    "ETIMEDOUT",
                                    "503",
                                    "502",
                                    "500",
                                ];
                                return retryableErrors.some((error) =>
                                    e.message.toLowerCase().includes(error.toLowerCase())
                                );
                            },
                        }
                    );

                    // Extract file changes from the chat history
                    const fileChanges: Array<{
                        filePath: string;
                        changeType: string;
                        content?: string;
                    }> = [];

                    if (historyResponse.chat_history) {
                        for (const exchange of historyResponse.chat_history) {
                            if (exchange.changed_files) {
                                for (const changedFile of exchange.changed_files) {
                                    fileChanges.push({
                                        filePath: changedFile.new_path || changedFile.old_path,
                                        changeType: this._getFileChangeTypeString(
                                            changedFile.change_type
                                        ),
                                        content: changedFile.new_contents,
                                    });
                                }
                            }
                        }
                    }

                    results.push({
                        agentId: workerAgentId,
                        success: true,
                        fileEdits: fileChanges,
                    });
                } catch (operationError: unknown) {
                    this._logger.error(
                        `Failed to read edits for worker agent ${workerAgentId}`,
                        operationError
                    );
                    results.push({
                        agentId: workerAgentId,
                        success: false,
                        error: `Failed to read worker agent edits: ${operationError instanceof Error ? operationError.message : String(operationError)}`,
                    });
                }
            }

            // Return structured response with all results
            const structuredResponse = {
                editResults: results.filter((r) => r.success),
                failedReads: results.filter((r) => !r.success),
                totalRequested: targetAgentIds.length,
                totalSuccessful: results.filter((r) => r.success).length,
                totalFailed: results.filter((r) => !r.success).length,
            };

            return successToolResponse(JSON.stringify(structuredResponse), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to read worker agent edits", e);
            return errorToolResponse(
                `Failed to read worker agent edits: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Convert FileChangeType enum to string
     */
    private _getFileChangeTypeString(changeType: FileChangeType): string {
        switch (changeType) {
            case FileChangeType.added:
                return "added";
            case FileChangeType.deleted:
                return "deleted";
            case FileChangeType.modified:
                return "modified";
            case FileChangeType.renamed:
                return "renamed";
            default:
                return "unknown";
        }
    }
}

/**
 * A tool that applies worker agent file changes to the local workspace.
 * This tool integrates retrieved changes into your development environment.
 */
export class ApplyWorkerAgentEditsTool extends ToolBase<LocalToolType> {
    private readonly _logger = getLogger("ApplyWorkerAgentEditsTool");

    constructor(private readonly _apiServer: APIServer) {
        super(LocalToolType.applyWorkerAgentEdits, ToolSafety.Unsafe);
    }

    public readonly description: string = `\
Apply worker agent file changes to your local workspace after review. **Automatically deletes workers after successful application.**

**OPERATION**: WRITE (modifies local files, automatically cleans up workers)

**COMPREHENSIVE CLEANUP**: If worker_agent_ids is empty or omitted, applies changes from ALL available workers and cleans them up.

**SAFETY**: Always use ReadWorkerAgentEdits first to review changes before applying.

**APPLICATION METHODS**:
• direct_files: Apply changes directly to workspace files
• git_merge: Merge from worker branches
• git_cherry_pick: Selective commit application

**AUTO-CLEANUP**: Workers are automatically deleted after successful application (set auto_delete=false to disable).

**CONFLICT RESOLUTION**: When conflicts are detected, provides step-by-step guidance for manual resolution.

**WORKFLOW**: ReadWorkerAgentEdits → Review → **ApplyWorkerAgentEdits** → (automatic cleanup + conflict guidance)

**CRITICAL**: This modifies your local workspace files. Review changes carefully first.

**EXAMPLES**:
• Apply specific workers: ApplyWorkerAgentEdits(worker_agent_ids=["worker-123"], application_method="direct_files")
• Apply ALL workers: ApplyWorkerAgentEdits(application_method="direct_files")`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            worker_agent_ids: {
                type: "array",
                items: { type: "string" },
                description:
                    "IDs of worker agents whose changes to apply. If empty array, applies changes from ALL available worker agents.",
            },
            application_method: {
                type: "string",
                enum: ["direct_files", "git_merge", "git_cherry_pick"],
                description:
                    "How to apply changes: direct_files for file replacement, git_merge for branch merging, git_cherry_pick for selective commits",
            },
            target_files: {
                type: "array",
                items: { type: "string" },
                description: "Optional: specific files to apply (for selective application)",
            },
            target_branch: {
                type: "string",
                description: "Target branch for git operations (defaults to current branch)",
            },
            auto_delete: {
                type: "boolean",
                description:
                    "Whether to automatically delete workers after successful application (default: true)",
            },
        },
        required: ["application_method"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
        return false; // This modifies local files, so it's not safe
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const requestId = this._apiServer.createRequestId();

        try {
            let workerAgentIds = toolInput.worker_agent_ids as string[] | undefined;
            const applicationMethod = toolInput.application_method as string;
            const targetFiles = toolInput.target_files as string[] | undefined;
            const targetBranch = toolInput.target_branch as string | undefined;
            const autoDelete = toolInput.auto_delete !== false; // Default to true

            // Validate input
            if (workerAgentIds !== undefined && !Array.isArray(workerAgentIds)) {
                return errorToolResponse("Worker agent IDs must be an array.", requestId);
            }

            // If no worker IDs provided or empty array, get all available workers
            if (!workerAgentIds || workerAgentIds.length === 0) {
                this._logger.debug("No worker IDs specified, discovering all available workers");

                const readStateTool = new ReadWorkerStateTool(this._apiServer);
                const stateResponse = await readStateTool.call(
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    { worker_agent_ids: [] }, // Empty array = all workers
                    [],
                    new AbortController().signal,
                    ""
                );

                if (stateResponse.isError) {
                    return errorToolResponse(
                        `Failed to discover worker agents: ${stateResponse.text}`,
                        requestId
                    );
                }

                const stateData = JSON.parse(stateResponse.text) as {
                    workers?: Array<{ agentId: string; status: string }>;
                };

                workerAgentIds = (stateData.workers || []).map((w) => w.agentId);

                if (workerAgentIds.length === 0) {
                    return errorToolResponse(
                        "No worker agents found to apply changes from.",
                        requestId
                    );
                }

                this._logger.debug(
                    `Discovered ${workerAgentIds.length} workers: ${workerAgentIds.join(", ")}`
                );
            }

            this._logger.debug(
                `Applying worker agent edits for ${workerAgentIds.length} agents using method: ${applicationMethod}, auto_delete: ${autoDelete}`
            );

            // First, retrieve the worker agent edits
            const readEditsTool = new ReadWorkerAgentEditsTool(this._apiServer);
            const editsResponse = await readEditsTool.call(
                // eslint-disable-next-line @typescript-eslint/naming-convention
                { worker_agent_ids: workerAgentIds },
                [],
                new AbortController().signal,
                ""
            );

            if (editsResponse.isError) {
                return errorToolResponse(
                    `Failed to retrieve worker agent edits: ${editsResponse.text}`,
                    requestId
                );
            }

            const editsData = JSON.parse(editsResponse.text) as {
                editResults?: {
                    agentId: string;
                    success: boolean;
                    fileEdits?: { filePath: string; changeType: string; content?: string }[];
                }[];
            };
            const editResults = editsData.editResults || [];

            // Apply the changes based on the selected method
            const applicationResult = await this._applyChanges(
                editResults,
                applicationMethod,
                targetFiles || [],
                targetBranch || "current"
            );

            // Automatically delete workers after successful application (if enabled)
            if (autoDelete && applicationResult.success) {
                try {
                    const deleteWorkerTool = new DeleteWorkerAgentTool(this._apiServer);
                    const deleteResponse = await deleteWorkerTool.call(
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        { worker_agent_ids: workerAgentIds },
                        [],
                        new AbortController().signal,
                        ""
                    );

                    if (deleteResponse.isError) {
                        this._logger.warn(`Failed to auto-delete workers: ${deleteResponse.text}`);
                        // Don't fail the entire operation if deletion fails
                        applicationResult.warnings = applicationResult.warnings || [];
                        applicationResult.warnings.push(
                            `Auto-deletion failed: ${deleteResponse.text}`
                        );
                    } else {
                        this._logger.debug(
                            `Successfully auto-deleted ${workerAgentIds.length} workers`
                        );
                        applicationResult.autoDeletedWorkers = workerAgentIds;
                    }
                } catch (deleteError) {
                    this._logger.warn("Failed to auto-delete workers", deleteError);
                    applicationResult.warnings = applicationResult.warnings || [];
                    applicationResult.warnings.push(
                        `Auto-deletion failed: ${deleteError instanceof Error ? deleteError.message : String(deleteError)}`
                    );
                }
            }

            return successToolResponse(JSON.stringify(applicationResult), requestId);
        } catch (e: unknown) {
            this._logger.error("Failed to apply worker agent edits", e);
            return errorToolResponse(
                `Failed to apply worker agent edits: ${e instanceof Error ? e.message : String(e)}`,
                requestId
            );
        }
    }

    /**
     * Apply worker agent changes using the specified method
     */
    private async _applyChanges(
        editResults: Array<{
            agentId: string;
            success: boolean;
            fileEdits?: Array<{
                filePath: string;
                changeType: string;
                content?: string;
            }>;
            error?: string;
        }>,
        applicationMethod: string,
        targetFiles: string[],
        targetBranch: string
    ): Promise<{
        success: boolean;
        message: string;
        appliedFiles: string[];
        conflictFiles: string[];
        errors: string[];
        method: string;
        warnings?: string[];
        autoDeletedWorkers?: string[];
        conflictResolutionGuidance?: string[];
    }> {
        const appliedFiles: string[] = [];
        const conflictFiles: string[] = [];
        const errors: string[] = [];

        this._logger.debug(`Applying changes using method: ${applicationMethod}`);

        // Get workspace root
        const workspaceRoot = await findGitRoot();
        if (!workspaceRoot) {
            throw new Error("Not in a git repository");
        }

        // Collect all file edits from successful agents
        const allFileEdits: Array<{
            filePath: string;
            changeType: string;
            content?: string;
            agentId: string;
        }> = [];

        for (const result of editResults) {
            if (!result.success) {
                errors.push(`Agent ${result.agentId}: ${result.error || "Unknown error"}`);
                continue;
            }

            if (result.fileEdits) {
                for (const edit of result.fileEdits) {
                    // Filter by target files if specified
                    if (targetFiles.length > 0 && !targetFiles.includes(edit.filePath)) {
                        continue;
                    }

                    allFileEdits.push({
                        ...edit,
                        agentId: result.agentId,
                    });
                }
            }
        }

        this._logger.debug(`Found ${allFileEdits.length} file edits to apply`);

        // Apply changes based on method
        switch (applicationMethod) {
            case "direct_files":
                await this._applyDirectFiles(
                    allFileEdits,
                    workspaceRoot,
                    appliedFiles,
                    conflictFiles,
                    errors
                );
                break;
            case "git_merge":
                await this._applyGitMerge(
                    allFileEdits,
                    workspaceRoot,
                    targetBranch,
                    appliedFiles,
                    conflictFiles,
                    errors
                );
                break;
            case "git_cherry_pick":
                await this._applyGitCherryPick(
                    allFileEdits,
                    workspaceRoot,
                    targetBranch,
                    appliedFiles,
                    conflictFiles,
                    errors
                );
                break;
            default:
                throw new Error(`Unknown application method: ${applicationMethod}`);
        }

        const success = errors.length === 0;
        const message = success
            ? `Successfully applied ${appliedFiles.length} files${conflictFiles.length > 0 ? ` with ${conflictFiles.length} conflicts` : ""}`
            : `Applied ${appliedFiles.length} files with ${errors.length} errors`;

        // Generate conflict resolution guidance if conflicts were detected
        const conflictResolutionGuidance =
            conflictFiles.length > 0
                ? [
                      `🔧 CONFLICTS DETECTED: ${conflictFiles.length} files have merge conflicts that need resolution.`,
                      `📁 Conflicted files: ${conflictFiles.join(", ")}`,
                      `🛠️ NEXT STEPS:`,
                      `1. Open each conflicted file and look for conflict markers (<<<<<<< ======= >>>>>>>)`,
                      `2. Manually resolve conflicts by choosing the correct code sections`,
                      `3. Remove all conflict markers after resolution`,
                      `4. Test the resolved code to ensure it works correctly`,
                      `5. Consider running tests to validate the merged changes`,
                  ]
                : [];

        return {
            success,
            message,
            appliedFiles,
            conflictFiles,
            errors,
            method: applicationMethod,
            ...(conflictResolutionGuidance.length > 0 && { conflictResolutionGuidance }),
        };
    }

    /**
     * Apply changes directly to files with conflict detection
     */
    private async _applyDirectFiles(
        fileEdits: Array<{
            filePath: string;
            changeType: string;
            content?: string;
            agentId: string;
        }>,
        workspaceRoot: string,
        appliedFiles: string[],
        conflictFiles: string[],
        errors: string[]
    ): Promise<void> {
        for (const edit of fileEdits) {
            try {
                const fullPath = path.resolve(workspaceRoot, edit.filePath);

                this._logger.debug(`Applying ${edit.changeType} to ${edit.filePath}`);

                switch (edit.changeType) {
                    case "added":
                    case "modified": {
                        if (!edit.content) {
                            errors.push(
                                `No content provided for ${edit.changeType} file: ${edit.filePath}`
                            );
                            continue;
                        }

                        // Check if file exists for conflict detection
                        let hasConflicts = false;
                        try {
                            const existingContent = await fs.readFile(fullPath, "utf8");

                            // For modified files, use three-way merge to detect conflicts
                            if (edit.changeType === "modified") {
                                const mergeResult = threeWayMerge(
                                    "",
                                    edit.content,
                                    existingContent
                                );
                                hasConflicts = mergeResult.hasConflicts;

                                if (hasConflicts) {
                                    // Write the merged content with conflict markers
                                    await this._ensureDirectoryExists(path.dirname(fullPath));
                                    await writeFileUtf8(fullPath, mergeResult.mergedContent);
                                    conflictFiles.push(edit.filePath);
                                } else {
                                    // No conflicts, write the new content
                                    await this._ensureDirectoryExists(path.dirname(fullPath));
                                    await writeFileUtf8(fullPath, edit.content);
                                    appliedFiles.push(edit.filePath);
                                }
                            } else {
                                // File exists but we're trying to add it - this is a conflict
                                conflictFiles.push(edit.filePath);
                                errors.push(`File already exists: ${edit.filePath}`);
                            }
                        } catch (fileError: any) {
                            if ((fileError as NodeJS.ErrnoException).code === "ENOENT") {
                                // File doesn't exist, safe to create
                                await this._ensureDirectoryExists(path.dirname(fullPath));
                                await writeFileUtf8(fullPath, edit.content);
                                appliedFiles.push(edit.filePath);
                            } else {
                                throw fileError;
                            }
                        }
                        break;
                    }

                    case "deleted":
                        try {
                            await fs.unlink(fullPath);
                            appliedFiles.push(edit.filePath);
                        } catch (fileError: any) {
                            if ((fileError as NodeJS.ErrnoException).code === "ENOENT") {
                                // File already doesn't exist, consider it successful
                                appliedFiles.push(edit.filePath);
                            } else {
                                throw fileError;
                            }
                        }
                        break;

                    case "renamed":
                        errors.push(`Rename operations not yet supported: ${edit.filePath}`);
                        break;

                    default:
                        errors.push(`Unknown change type: ${edit.changeType} for ${edit.filePath}`);
                }
            } catch (error: unknown) {
                const errorMsg = `Failed to apply ${edit.changeType} to ${edit.filePath}: ${error instanceof Error ? error.message : String(error)}`;
                this._logger.error(errorMsg, error);
                errors.push(errorMsg);
            }
        }
    }

    /**
     * Ensure directory exists, creating it if necessary
     */
    private async _ensureDirectoryExists(dirPath: string): Promise<void> {
        try {
            await fs.mkdir(dirPath, { recursive: true });
        } catch (error: any) {
            if ((error as NodeJS.ErrnoException).code !== "EEXIST") {
                throw error;
            }
        }
    }

    /**
     * Apply changes using git merge (placeholder for now)
     */
    private async _applyGitMerge(
        fileEdits: Array<{
            filePath: string;
            changeType: string;
            content?: string;
            agentId: string;
        }>,
        workspaceRoot: string,
        targetBranch: string,
        appliedFiles: string[],
        conflictFiles: string[],
        errors: string[]
    ): Promise<void> {
        // For now, fall back to direct file application
        // TODO: Implement proper git merge functionality
        this._logger.warn(
            "Git merge method not fully implemented, falling back to direct file application"
        );
        await this._applyDirectFiles(fileEdits, workspaceRoot, appliedFiles, conflictFiles, errors);
    }

    /**
     * Apply changes using git cherry-pick (placeholder for now)
     */
    private async _applyGitCherryPick(
        fileEdits: Array<{
            filePath: string;
            changeType: string;
            content?: string;
            agentId: string;
        }>,
        workspaceRoot: string,
        targetBranch: string,
        appliedFiles: string[],
        conflictFiles: string[],
        errors: string[]
    ): Promise<void> {
        // For now, fall back to direct file application
        // TODO: Implement proper git cherry-pick functionality
        this._logger.warn(
            "Git cherry-pick method not fully implemented, falling back to direct file application"
        );
        await this._applyDirectFiles(fileEdits, workspaceRoot, appliedFiles, conflictFiles, errors);
    }
}
