import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { type IRange } from "monaco-editor";
import * as vscode from "vscode";

import {
    FileDetails,
    type FileRangesSelected,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import { WorkspaceManager } from "../workspace/workspace-manager";

export function editorToFileRangesSelected(
    editor: vscode.TextEditor | undefined,
    workspaceManager: WorkspaceManager | undefined
): FileRangesSelected {
    // Initialize the response object
    const response: FileRangesSelected = {
        type: WebViewMessageType.fileRangesSelected,
        data: [],
    };

    if (!editor || !workspaceManager) {
        return response;
    }

    // Parse out fileDetails from a URI
    const fileDetails = uriToFileDetails(editor.document.uri, workspaceManager);
    const selections = editor.selections;
    if (fileDetails && selections.some((s: vscode.Selection) => !s.isEmpty)) {
        response.data = selections.map((s: vscode.Selection) => ({
            repoRoot: fileDetails.repoRoot,
            pathName: fileDetails.pathName,
            fullRange: {
                startLineNumber: s.start.line,
                startColumn: s.start.character,
                endLineNumber: s.end.line,
                endColumn: s.end.character,
            },
            originalCode: editor.document.getText(s),
        }));
    }
    return response;
}

/**
 * Utility function to convert a VSCode range to a Monaco range
 * @param range: The VSCode range to convert
 * @returns: The Monaco range
 */
export function vscodeRangeToMonacoRange(range: vscode.Range): IRange {
    return {
        startLineNumber: range.start.line,
        startColumn: range.start.character,
        endLineNumber: range.end.line,
        endColumn: range.end.character,
    };
}

/**
 * Utility function to convert a Monaco range to a VSCode range
 * @param range: The Monaco range to convert
 * @returns: The VSCode range
 */
export function monacoRangeToVscodeRange(range: IRange): vscode.Range {
    return new vscode.Range(
        range.startLineNumber,
        range.startColumn,
        range.endLineNumber,
        range.endColumn
    );
}

// Get file details from a URI path
export function uriToFileDetails(
    uri: vscode.Uri,
    workspaceManager: WorkspaceManager
): FileDetails | undefined {
    const pathName = workspaceManager.safeResolvePathName(uri);
    if (pathName === undefined) {
        return undefined;
    }
    return pathNameToFileDetails(pathName);
}

export function pathNameToFileDetails(pathName: IQualifiedPathName): FileDetails {
    return {
        repoRoot: pathName.rootPath,
        pathName: pathName.relPath,
    };
}
