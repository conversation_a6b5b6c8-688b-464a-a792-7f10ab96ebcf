/**
 * This file contains utilities around VSCode's APIs to search for document symbols.
 * Note that these are intended to be platform implementations against VSCode's APIs
 */
import { type languages } from "monaco-editor";
import * as vscode from "vscode";

import { FileDetails } from "../webview-providers/webview-messages";
import { getFileDetailAbsPath } from "../workspace/types";
import { TextDocumentView, viewTextDocument } from "../workspace/view-text-document";
import { monacoRangeToVscodeRange, vscodeRangeToMonacoRange } from "./data-transform-utils";

/**
 * Uses the VSCode semantic tokens abstraction in order to get all symbol-like tokens in a file
 *
 * Note that if we get an empty array back, there are a few reasons (that we cannot differentiate between):
 * - The file actually contains no document symbols
 * - There is no language server for the file's language
 * - The language server for the file's language has not been initialized
 *
 * Thus, it is expected that this function be called multiple times, until it returns a non-empty array,
 * or until a higher-level retry mechanism stops the retry
 *
 * See this below line for documentation on how to parse the tokens
 * https://github.com/microsoft/vscode/blob/80b04838244d0531504d6c128a2492d3d55a1f73/src/vscode-dts/vscode.d.ts#L4393
 *
 * @param uri: The URI of the file to get document symbols for
 * @returns
 */
export async function getTokensFromUri(uri: vscode.Uri): Promise<languages.DocumentSymbol[]> {
    // Get all of the information we will need for decoding semantic tokens
    const [legend, semanticTokens, docAtUri] = await Promise.all([
        vscode.commands.executeCommand<languages.SemanticTokensLegend | undefined>(
            "vscode.provideDocumentSemanticTokensLegend",
            uri
        ),
        vscode.commands.executeCommand<languages.SemanticTokens | undefined>(
            "vscode.provideDocumentSemanticTokens",
            uri
        ),
        viewTextDocument(uri),
    ]);
    if (!legend || !semanticTokens || !docAtUri) {
        return [];
    }

    // Array to store decoded tokens
    const symbols: languages.DocumentSymbol[] = [];
    const tokenData = semanticTokens.data;

    // Iterate via groups of 5 integers
    // Data format documented at:
    // https://github.com/microsoft/vscode/blob/80b04838244d0531504d6c128a2492d3d55a1f73/src/vscode-dts/vscode.d.ts#L4393
    for (let i = 0; i < tokenData.length; i += 5) {
        // Get the previous symbol as a reference point
        const prevSymbol: languages.DocumentSymbol | undefined = symbols[symbols.length - 1];

        // Parse out token info from raw data
        const deltaLine = tokenData[i];
        const deltaStartChar = tokenData[i + 1];
        const length = tokenData[i + 2];

        // Get start position of range
        const startLineNumber = (prevSymbol?.range.startLineNumber ?? 0) + deltaLine;
        const columnRef = deltaLine === 0 ? (prevSymbol?.range.startColumn ?? 0) : 0;
        const startColumn = columnRef + deltaStartChar;
        const startPosition = new vscode.Position(startLineNumber, startColumn);

        // Get end positino of range
        const startOffset = docAtUri.offsetAt(startPosition);
        const endOffset = startOffset + length;
        const endPosition = docAtUri.positionAt(endOffset);

        // Get the range
        const range = new vscode.Range(startPosition, endPosition);
        const textAtRange = docAtUri.getText(range);

        // Convert this info to a symbol
        symbols.push({
            name: textAtRange,
            kind: vscode.SymbolKind.String,
            range: vscodeRangeToMonacoRange(range),
            selectionRange: vscodeRangeToMonacoRange(range),
            detail: "",
            tags: [],
            children: [],
        });
    }
    return symbols;
}

/**
 * Search for a token in a file.
 *
 * Does so with an exact string match, with word boundaries, and only returns the first match.
 * If there is no match, returns an empty array.
 *
 * @param file: The file to search in, with optional ranges
 * @param query: The query to search for
 * @returns: An array of document symbols, or an empty array if there is no match
 */
export async function searchFileForRegexToken(
    file: FileDetails,
    query: string
): Promise<languages.DocumentSymbol[]> {
    // Retrieve the document to search in
    const uri = vscode.Uri.file(getFileDetailAbsPath(file));
    const docAtUri: TextDocumentView | undefined = await viewTextDocument(uri);
    if (!docAtUri) {
        return Promise.resolve([]);
    }

    // Compute the range to search in
    const range = file.fullRange ? monacoRangeToVscodeRange(file.fullRange) : undefined;
    const startIdx = range ? docAtUri.offsetAt(range.start) : 0;
    const textToSearch = docAtUri.getText(range);

    // Get all the regex matches for the query where it is an entire word (i.e. surrounded by word boundaries)
    const regex = new RegExp(`\\b${query}\\b`, "g");
    const matchIdx: number = textToSearch.search(regex);

    // If there is a match, return the symbol
    if (matchIdx >= 0) {
        const position = docAtUri.positionAt(startIdx + matchIdx);
        const endPosition = docAtUri.positionAt(startIdx + matchIdx + query.length);
        const range = new vscode.Range(position, endPosition);
        return Promise.resolve([
            {
                name: docAtUri.getText(range),
                kind: vscode.SymbolKind.String,
                range: vscodeRangeToMonacoRange(range),
                selectionRange: vscodeRangeToMonacoRange(range),
                detail: "",
                tags: [],
                children: [],
            },
        ]);
    }
    return Promise.resolve([]);
}
