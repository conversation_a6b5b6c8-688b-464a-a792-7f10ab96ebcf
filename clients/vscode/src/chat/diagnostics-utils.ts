import {
    Diagnostic,
    DiagnosticRelatedInformation,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/str-replace-editor-tool/diagnostic-types";
import * as vscode from "vscode";

function convertDiagnosticCode(code: vscode.Diagnostic["code"]): Diagnostic["code"] {
    if (code === undefined) {
        return undefined;
    }

    if (typeof code === "string" || typeof code === "number") {
        return code;
    }

    if (code.target !== undefined) {
        return {
            value: code.value,
            target: code.target.toString(),
        };
    }

    return code.value;
}

export function fromVscodeDiagnostic(diagnostic: vscode.Diagnostic): Diagnostic {
    const result: Diagnostic = {
        message: diagnostic.message,
        range: diagnostic.range,
        severity: diagnostic.severity,
        source: diagnostic.source,
        relatedInformation: diagnostic.relatedInformation?.map(fromVscodeRelatedInformation),
        tags: diagnostic.tags,
        code: convertDiagnosticCode(diagnostic.code),
    };

    return result;
}

export function fromVscodeDiagnosticMap(
    diagnosticsMap: Map<string, vscode.Diagnostic[]>
): Map<string, Diagnostic[]> {
    const result = new Map<string, Diagnostic[]>();
    diagnosticsMap.forEach((diagnostics, path) => {
        result.set(path, diagnostics.map(fromVscodeDiagnostic));
    });
    return result;
}

export function fromVscodeRelatedInformation(
    relatedInformation: vscode.DiagnosticRelatedInformation
): DiagnosticRelatedInformation {
    return {
        location: {
            uri: relatedInformation.location.uri.toString(),
            range: relatedInformation.location.range,
        },
        message: relatedInformation.message,
    };
}
