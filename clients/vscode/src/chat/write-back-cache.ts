import debounce from "lodash/debounce";
import { LRUCache } from "lru-cache";
import * as vscode from "vscode";

import { type IAugmentGlobalState, WriteBackCacheKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";

type CacheDump<T extends {}> = [string, LRUCache.Entry<T>][];

/**
 * A cache for selected code details.
 */
export class WriteBackCache<
    T extends {},
    TContext extends unknown = unknown,
> extends DisposableService {
    private _cache: LRUCache<string, T, TContext>;
    private _onExpiry?: (key: string) => void;

    constructor(
        private _globalState: IAugmentGlobalState,
        private _cacheBackKey: WriteBackCacheKey,
        private _options?: {
            lru: LRUCache.Options<string, T, TContext>;
        }
    ) {
        super();

        // Create the cache with the provided options
        const lruOptions: LRUCache.Options<string, T, TContext> = {
            max: 1000,
            ...this._options?.lru,
        };

        this._cache = new LRUCache<string, T, TContext>(lruOptions);
        void this.loadContext();
        this.addDisposable(new vscode.Disposable(() => this.dumpContext.cancel()));
    }

    /**
     * Dumps the cache to the global state.
     */
    private dumpContext = debounce(
        async (): Promise<void> => {
            const context: CacheDump<T> = this._cache.dump();
            await this._globalState.save(this._cacheBackKey, context, {
                uniquePerWorkspace: true,
            });

            // Clear our in-memory cache. We should not be using the in-memory cache for this.
            void this._globalState.update(this._cacheBackKey, undefined);
        },
        60 * 1000 /* 1 minute */,
        {
            leading: true,
            trailing: true,
        }
    );

    /**
     * Loads the cache from the global state.
     */
    private loadContext = async (): Promise<void> => {
        // First try to load context from disk
        const storedCtx = await this._globalState.load<CacheDump<T>>(this._cacheBackKey, {
            uniquePerWorkspace: true,
        });
        if (storedCtx) {
            this._cache.load(storedCtx);
            return;
        }

        // Then try to load from in-memory storage if it doesn't exist
        const inMemoryCtx = this._globalState.get<CacheDump<T>>(this._cacheBackKey);
        if (inMemoryCtx) {
            this._cache.load(inMemoryCtx);
        }
    };

    /**
     * Stores the selected code details for a particular key
     *
     * @param key The key
     * @param selectedCodeDetails The details of the selected code
     */
    public set = async (key: string, item: T): Promise<void> => {
        this._cache.set(key, item);

        // Trigger a call to dumpContext. Will dump the context once there are no
        // updates for 30 seconds.
        await this.dumpContext();
    };

    /**
     * Retrieves the selected code details for a given key.
     *
     * @param key The key
     * @returns The selected code details if found, undefined otherwise
     */
    public get = (key: string): T | undefined => {
        void this.dumpContext();
        return this._cache.get(key);
    };

    /**
     * The underlying cache.
     */
    public get cache(): LRUCache<string, T, TContext> {
        return this._cache;
    }

    /**
     * Removes the selected code details for a given key from the cache.
     *
     * @param key The key
     */
    public remove = (key: string): void => {
        this._cache.delete(key);
    };

    /**
     * Clears all entries from the cache.
     */
    public clear = (): void => {
        this._cache.clear();
    };

    /**
     * Get all items sorted by recency
     */
    public getItems = (): T[] => {
        return [...this._cache.values()];
    };
}
