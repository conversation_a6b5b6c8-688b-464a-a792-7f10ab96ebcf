import { AUGMENT_GUIDELINES_FILE } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import * as fs from "fs";
import throttle from "lodash/throttle";
import * as path from "path";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { FeatureFlagManager } from "../feature-flags";
import { ClientMetricsReporter } from "../metrics/client-metrics-reporter";
import { ChatMetricName, WebviewName } from "../metrics/types";
import { DisposableService } from "../utils/disposable-service";
import { GuidelinesStates } from "../webview-providers/webview-messages";
import { WorkspaceManager } from "../workspace/workspace-manager";

export class GuidelinesWatcher extends DisposableService {
    private _workspaceGuidelinesContent: Map<string, string> = new Map();
    private readonly _workspaceWatchers: Map<string, vscode.FileSystemWatcher> = new Map();
    private _userGuidelinesContent = "";
    // Emit an event for when the user or workspace guidelines change
    private readonly _guidelinesChangedEmitter = new vscode.EventEmitter<void>();

    constructor(
        private readonly _config: AugmentConfigListener,
        private readonly _flags: FeatureFlagManager,
        private readonly _clientMetricsReporter: ClientMetricsReporter
    ) {
        super();

        this._initializeWorkspaceWatchers();

        // Watch for workspace folder changes
        this.addDisposable(
            vscode.workspace.onDidChangeWorkspaceFolders((event) => {
                // Clean up watchers for removed workspaces
                event.removed.forEach((workspace) => {
                    const guidelineFileWatcher = this._workspaceWatchers.get(workspace.uri.fsPath);
                    if (guidelineFileWatcher) {
                        guidelineFileWatcher.dispose();
                        this._workspaceWatchers.delete(workspace.uri.fsPath);
                        this._workspaceGuidelinesContent.delete(workspace.uri.fsPath);
                    }
                });

                // Add watchers for new workspaces
                event.added.forEach((workspace) => {
                    this._initializeWorkspaceWatcher(workspace.uri.fsPath);
                });
            })
        );

        // Initialize user guidelines
        this._userGuidelinesContent = this._config?.config?.chat.userGuidelines || "";

        // Watch for user guidelines changes
        this.addDisposable(
            this._config.onDidChange(() => {
                const oldContent = this._userGuidelinesContent;
                this._userGuidelinesContent = this._config?.config?.chat.userGuidelines || "";
                this._guidelinesChangedEmitter.fire();

                // Report metrics for setting or clearing user guidelines
                if (oldContent === "" && this._userGuidelinesContent !== "") {
                    this._reportMetric(ChatMetricName.setUserGuidelines);
                } else if (oldContent !== "" && this._userGuidelinesContent === "") {
                    this._reportMetric(ChatMetricName.clearUserGuidelines);
                }
            })
        );
    }

    get onDidChange(): vscode.Event<void> {
        return this._guidelinesChangedEmitter.event;
    }

    private _initializeWorkspaceWatchers(): void {
        if (vscode.workspace.workspaceFolders) {
            vscode.workspace.workspaceFolders.forEach((workspace) => {
                this._initializeWorkspaceWatcher(workspace.uri.fsPath);
            });
        }
    }

    private _initializeWorkspaceWatcher(workspacePath: string): void {
        const guidelinesPath = path.join(workspacePath, AUGMENT_GUIDELINES_FILE);

        // Read initial content
        this._updateWorkspaceContent(workspacePath);

        // Create and setup watcher
        const workspaceGuidelinesWatcher = vscode.workspace.createFileSystemWatcher(guidelinesPath);
        this.addDisposable(workspaceGuidelinesWatcher);

        const updateContent = () => this._updateWorkspaceContent(workspacePath);

        workspaceGuidelinesWatcher.onDidChange(updateContent);
        workspaceGuidelinesWatcher.onDidCreate(updateContent);
        workspaceGuidelinesWatcher.onDidDelete(() => {
            this._workspaceGuidelinesContent.set(workspacePath, "");
        });

        this._workspaceWatchers.set(workspacePath, workspaceGuidelinesWatcher);
    }

    private _updateWorkspaceContent(workspacePath: string): void {
        try {
            const guidelinesPath = path.join(workspacePath, AUGMENT_GUIDELINES_FILE);
            const oldContent = this._workspaceGuidelinesContent.get(workspacePath) || "";
            const newContent = fs.readFileSync(guidelinesPath, "utf8");
            this._workspaceGuidelinesContent.set(workspacePath, newContent);
            this._guidelinesChangedEmitter.fire();

            // Report metrics for setting or clearing workspace guidelines
            if (oldContent === "" && newContent !== "") {
                this._reportMetric(ChatMetricName.setWorkspaceGuidelines);
            } else if (oldContent !== "" && newContent === "") {
                this._reportMetric(ChatMetricName.clearWorkspaceGuidelines);
            }
        } catch {
            const oldContent = this._workspaceGuidelinesContent.get(workspacePath) || "";
            this._workspaceGuidelinesContent.set(workspacePath, "");
            if (oldContent !== "") {
                this._reportMetric(ChatMetricName.clearWorkspaceGuidelines);
            }
        }
    }

    public getWorkspaceGuidelinesContent(workspacePath: string): string {
        return this._workspaceGuidelinesContent.get(workspacePath) || "";
    }

    private getActiveWorkspacePath(workspaceManager: WorkspaceManager): string | undefined {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return undefined;
        }
        const activeFsPath = activeEditor.document.uri.fsPath;
        const activePath = workspaceManager.safeResolvePathName(activeFsPath);
        return activePath?.rootPath;
    }

    public getCurrentWorkspaceGuidelinesContent(workspaceManager: WorkspaceManager): string {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return "";
        }
        if (workspaceFolders.length === 1) {
            return this.getWorkspaceGuidelinesContent(workspaceFolders[0].uri.fsPath);
        }
        const activeWorkspacePath = this.getActiveWorkspacePath(workspaceManager);
        if (activeWorkspacePath) {
            return this.getWorkspaceGuidelinesContent(activeWorkspacePath);
        }
        return "";
    }

    public getUserGuidelinesContent(): string {
        return this._userGuidelinesContent;
    }

    public getGuidelinesStates(): GuidelinesStates {
        return {
            userGuidelines: {
                overLimit:
                    this._userGuidelinesContent.length >
                    this._flags.currentFlags.userGuidelinesLengthLimit,
                contents: this._userGuidelinesContent,
                lengthLimit: this._flags.currentFlags.userGuidelinesLengthLimit,
            },
            workspaceGuidelines: Array.from(this._workspaceGuidelinesContent.entries()).map(
                ([workspacePath, content]) => ({
                    workspaceFolder: workspacePath,
                    contents: content,
                    overLimit:
                        content.length > this._flags.currentFlags.workspaceGuidelinesLengthLimit,
                    lengthLimit: this._flags.currentFlags.workspaceGuidelinesLengthLimit,
                })
            ),
        };
    }

    private _reportMetric = throttle(
        (metricName: ChatMetricName): void => {
            this._clientMetricsReporter.reportWebviewClientMetric({
                webviewName: WebviewName.chat,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                client_metric: metricName,
                value: 1,
            });
        },
        1000, // Throttle to once every second in an effort to debounce metrics.
        { leading: true, trailing: false }
    );

    public static updateUserGuidelines(data: string) {
        // Update the user guidelines
        const config = vscode.workspace.getConfiguration("augment.chat");
        void config.update("userGuidelines", data.trim(), vscode.ConfigurationTarget.Global);
    }
}
