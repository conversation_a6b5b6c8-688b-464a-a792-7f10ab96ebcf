import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_GUIDELINES_FILE,
    AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { WorkspaceManager } from "../workspace/workspace-manager";

/**
 * Watches for changes to rule files in the .augment/rules directory and workspace guidelines files
 */
export class RulesWatcher extends DisposableService {
    private _rulesChangedEmitter = new vscode.EventEmitter<void>();
    private _fileSystemWatcher: vscode.FileSystemWatcher | undefined;
    private _dirSystemWatcher: vscode.FileSystemWatcher | undefined;
    private _guidelinesWatcher: vscode.FileSystemWatcher | undefined;
    private _logger = getLogger("RulesWatcher");

    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super();
        this._setupFileSystemWatcher();
        this.addDisposable(this._rulesChangedEmitter);
    }

    /**
     * Set up file system watchers for rule files and directories
     */
    private _setupFileSystemWatcher(): void {
        try {
            // Watch for changes to .md files in the .augment/rules directory
            const rulesGlob = `**/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/**/*.md`;
            this._fileSystemWatcher = vscode.workspace.createFileSystemWatcher(rulesGlob);

            // When a file is created, changed, or deleted, emit an event
            this._fileSystemWatcher.onDidCreate(() => this._rulesChangedEmitter.fire());
            this._fileSystemWatcher.onDidChange(() => this._rulesChangedEmitter.fire());
            this._fileSystemWatcher.onDidDelete(() => this._rulesChangedEmitter.fire());

            this.addDisposable(this._fileSystemWatcher);

            // Watch for changes to directories in the .augment/rules directory
            const dirGlob = `**/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/**`;
            this._dirSystemWatcher = vscode.workspace.createFileSystemWatcher(
                dirGlob,
                true,
                true,
                false
            );

            // We only care about directory deletions
            this._dirSystemWatcher.onDidDelete(() => {
                this._rulesChangedEmitter.fire();
            });

            this.addDisposable(this._dirSystemWatcher);

            // Watch for changes to workspace guidelines files at the workspace root level
            const guidelinesGlob = `**/${AUGMENT_GUIDELINES_FILE}`;
            this._guidelinesWatcher = vscode.workspace.createFileSystemWatcher(guidelinesGlob);

            // When a guidelines file is created, changed, or deleted, emit an event
            this._guidelinesWatcher.onDidCreate(() => this._rulesChangedEmitter.fire());
            this._guidelinesWatcher.onDidChange(() => this._rulesChangedEmitter.fire());
            this._guidelinesWatcher.onDidDelete(() => this._rulesChangedEmitter.fire());

            this.addDisposable(this._guidelinesWatcher);
        } catch (error) {
            this._logger.error(`Error setting up file system watcher: ${String(error)}`);
        }
    }

    /**
     * Event that fires when rules change
     */
    get onDidChange(): vscode.Event<void> {
        return this._rulesChangedEmitter.event;
    }
}
