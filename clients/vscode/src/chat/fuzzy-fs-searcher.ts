import { isSuffixPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import Fuse, { FuseResult, FuseSearchOptions, FuseSortFunctionArg, IFuseOptions } from "fuse.js";
import { throttle } from "lodash";
import * as vscode from "vscode";

import { IAugmentGlobalState, WriteBackCacheKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { statFile } from "../utils/fs-utils";
import { dirName } from "../utils/path-utils";
import { StatInfo } from "../utils/types";
import {
    FileDetails,
    FindFileRequest,
    FindFileResponse,
    FindFolderRequest,
    FindFolderResponse,
    FindRecentlyOpenedFilesRequest,
    FindRecentlyOpenedFilesResponse,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import { pathNameToAbsPath, SyncingStatusEvent } from "../workspace/types";
import { viewTextDocument } from "../workspace/view-text-document";
import { FileChangeDetails, WorkspaceManager } from "../workspace/workspace-manager";
import { pathNameToFileDetails } from "./data-transform-utils";
import { WriteBackCache } from "./write-back-cache";
import { WriteBackIndex } from "./write-back-index";

/**
 * Interface for fuzzy file system searching functionality.
 */
export interface IFuzzyFsSearcher {
    /**
     * Searches for files based on the given request.
     * @param request - The FindFileRequest containing search parameters.
     * @returns A FindFileResponse with the search results.
     */
    findFiles: (request: FindFileRequest) => FindFileResponse;

    /**
     * Searches for folders based on the given request.
     * @param request - The FindFolderRequest containing search parameters.
     * @returns A FindFolderResponse with the search results.
     */
    findFolders: (request: FindFolderRequest) => FindFolderResponse;
}

const FUSE_OPTIONS = {
    keys: ["relPath"],
    minMatchCharLength: 1,
    minExactMatchCharLength: 3,
    threshold: 0.25,
    ignoreLocation: true,
    includeScore: true,
    useExtendedSearch: true,
    shouldSort: true,
    findAllMatches: true,
    sortFn,
};

/**
 * FuzzyFsSearcher is a class that uses Fuse.js to search for files and folders in the workspace.
 * We do this by adding a throttled function that updates the index of files and folders.
 * The index is triggered when:
 * - A file is added, removed, or changed in the workspace
 * - The syncing status of the workspace changes
 *
 * The index refresh is throttled, so it will only re-index once every 5 seconds. The indexing process
 * is quite fast, so this should not be a problem for most codebases that are open in vscode.
 */
export class FuzzyFsSearcher extends DisposableService implements IFuzzyFsSearcher {
    private _foldersIndex: WriteBackIndex<IQualifiedPathName>;
    private _filesIndex: WriteBackIndex<IQualifiedPathName>;

    private _recentFiles: WriteBackCache<IQualifiedPathName>;

    constructor(
        private readonly _globalState: IAugmentGlobalState,
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _onDidChangeSyncingStatus: vscode.Event<SyncingStatusEvent>
    ) {
        super();
        this._filesIndex = new WriteBackIndex(
            this._globalState,
            WriteBackCacheKey.fuzzyFsFilesIndex,
            { fuse: FUSE_OPTIONS, validator: this.validatePath, keyFn: pathToIndexKey }
        );
        this._foldersIndex = new WriteBackIndex(
            this._globalState,
            WriteBackCacheKey.fuzzyFsFoldersIndex,
            { fuse: FUSE_OPTIONS, validator: this.validatePath, keyFn: pathToIndexKey }
        );
        this._recentFiles = new WriteBackCache(
            this._globalState,
            WriteBackCacheKey.recentlyOpenedFiles,
            { lru: { max: 64 } }
        );
        this.addDisposables(
            ...this.trackFilesAndFolders(),
            this._recentFiles,
            vscode.window.onDidChangeActiveTextEditor(this.trackRecentTextEditor),
            vscode.window.onDidChangeVisibleTextEditors(this.onDidChangeVisibleTextEditors),
            vscode.window.tabGroups.onDidChangeTabs((e: vscode.TabChangeEvent) => {
                e.changed.forEach(this.trackRecentTab);
                e.opened.forEach(this.trackRecentTab);
            })
        );
    }

    public dispose = (): void => {
        super.dispose();
        this._filesIndex.clear();
        this._foldersIndex.clear();
    };

    public get files(): IQualifiedPathName[] {
        return Object.values(this._filesIndex.items);
    }

    public get folders(): IQualifiedPathName[] {
        return Object.values(this._foldersIndex.items);
    }

    private setFiles = (files: IQualifiedPathName[]): void => {
        const oldItems = this._filesIndex.items;
        files.forEach((f: IQualifiedPathName) => this._filesIndex.set(f));
        const newItems = this._filesIndex.items;

        // Re-validate everything in `oldItems` that is not in `newItems`
        const keysToRevalidate = Object.keys(oldItems).filter((k) => !newItems[k]);
        const itemsToRevalidate = keysToRevalidate.map((k) => oldItems[k]);
        void this._filesIndex.markForRevalidation(itemsToRevalidate);
    };

    /**
     * Sets the folders collection and updates the folder index.
     * @param folders - An array of IQualifiedPathName representing the folders.
     */
    private setFolders = (folders: IQualifiedPathName[]): void => {
        const oldItems = this._foldersIndex.items;
        folders.forEach((f: IQualifiedPathName) => this._foldersIndex.set(f));
        const newItems = this._foldersIndex.items;

        // Re-validate everything in `oldItems` that is not in `newItems`
        const keysToRevalidate = Object.keys(oldItems).filter((k) => !newItems[k]);
        const itemsToRevalidate = keysToRevalidate.map((k) => oldItems[k]);
        void this._foldersIndex.markForRevalidation(itemsToRevalidate);
    };

    /**
     * Tracks a recently accessed file by its absolute path.
     * Resolves the path name and blob name, then stores the file in the recent files cache.
     * @param absPath - The absolute path of the file to track.
     */
    private trackRecentAbsPath = (absPath: string): void => {
        const pathName = this._workspaceManager.resolvePathName(absPath);
        if (pathName === undefined) {
            return;
        }

        const blobName = this._workspaceManager.getBlobName(pathName);
        if (blobName === undefined) {
            return;
        }

        // Store the file in the recent files cache
        void this._recentFiles.set(absPath, pathName);
    };

    private trackRecentTab = (e: vscode.Tab): void => {
        if (e.input instanceof vscode.TabInputText) {
            const fsPath = e.input.uri.fsPath;
            this.trackRecentAbsPath(fsPath);
        } else {
            return;
        }
    };

    private trackRecentTextEditor = (e: vscode.TextEditor | undefined): void => {
        if (e === undefined) {
            return;
        }
        this.trackRecentAbsPath(e.document.uri.fsPath);
    };

    private onDidChangeVisibleTextEditors = (): void => {
        const editors = vscode.window.visibleTextEditors;
        for (const editor of editors) {
            this.trackRecentTextEditor(editor);
        }
    };

    // Initialize the tracking of files and folders
    private trackFilesAndFolders = (): vscode.Disposable[] => {
        // TODO(MarkMcauliffe, Eric): change this to listen to the workspace manager's incremental
        //  changes when we add that event in
        // Gather all of the possible files from the workspace manager, and initialize our index
        const updateLocalIndices = throttle(
            () => {
                const { files, folders } = getFilesAndFolders(this._workspaceManager);
                this.setFiles(files);
                this.setFolders(folders);
            },
            15000 /* 15 second throttle on updating the entire files index */,
            { leading: true, trailing: true }
        );

        // When a file updates, update the index with knowledge of the existence of the file
        const updateFile = (e?: FileChangeDetails): void => {
            if (!e?.relPath) {
                return;
            }
            const allPathNames = this._workspaceManager.getAllQualifiedPathNames(e.relPath);
            allPathNames.forEach((pathName: IQualifiedPathName) => {
                this._filesIndex.set(pathName);
            });
        };

        // Add a listener to update the index when the workspace changes
        return [
            new vscode.Disposable(() => updateLocalIndices.cancel()),
            this._workspaceManager.onDidChangeFile(updateLocalIndices),
            this._workspaceManager.onDidChangeFile(updateFile),
            this._onDidChangeSyncingStatus(updateLocalIndices),
        ];
    };

    /**
     * Finds recent files in the provided scope in the request, if it exists.
     *
     * @param request
     * @returns
     */
    public findRecentFiles = (
        request: FindRecentlyOpenedFilesRequest
    ): FindRecentlyOpenedFilesResponse => {
        const query: string = request.data.relPath;
        const maxResults: number = request.data.maxResults || 12;
        const recentFiles: FileDetails[] = this._recentFiles
            .getItems()
            .map((x: IQualifiedPathName) => ({
                repoRoot: x.rootPath,
                pathName: x.relPath,
            }));

        if (query.length < FUSE_OPTIONS.minMatchCharLength) {
            return {
                type: WebViewMessageType.findRecentlyOpenedFilesResponse,
                data: recentFiles.slice(0, maxResults),
            };
        }

        const searchResults = buildAndSearchIndex(recentFiles, query, {
            fuseSearch: { limit: maxResults },
            fuseInit: FUSE_OPTIONS,
        });

        return {
            type: WebViewMessageType.findRecentlyOpenedFilesResponse,
            data: searchResults,
        };
    };

    /**
     * Finds files in the provided scope in the request, if it exists.
     *
     * @param request
     * @returns
     */
    private findInScope = (request: FindFileRequest): FileDetails[] => {
        const query: string = request.data.relPath;
        const maxResults: number = request.data.maxResults || 12;

        if (request.data.searchScope?.files) {
            return buildAndSearchIndex(request.data.searchScope.files, query, {
                fuseSearch: { limit: maxResults },
                fuseInit: FUSE_OPTIONS,
            });
        }
        return [];
    };

    findFiles = (request: FindFileRequest): FindFileResponse => {
        // Parse out all the options
        const exactMatch: boolean = request.data.exactMatch ?? false;
        const query: string = request.data.relPath;
        const fuseQuery: string = exactMatch ? `${query}$` : query;
        const maxResults: number = request.data.maxResults || 12;

        if (
            exactMatch &&
            (query.length < FUSE_OPTIONS.minExactMatchCharLength ||
                query.length < FUSE_OPTIONS.minMatchCharLength)
        ) {
            return {
                type: WebViewMessageType.findFileResponse,
                data: [],
            };
        }

        if (query.length < FUSE_OPTIONS.minMatchCharLength) {
            const maxDefaultResults: number = 100;
            const items = Object.values(this._filesIndex.items).slice(0, maxDefaultResults);
            return {
                type: WebViewMessageType.findFileResponse,
                data: items.map(pathNameToFileDetails),
            };
        }

        // Initialize result object
        let result: FindFileResponse = { type: WebViewMessageType.findFileResponse, data: [] };

        // Populate result object with in-scope files
        const inScopeFiles: FileDetails[] = this.findInScope(request);
        result.data = dedupeFileDetailsByAbsPath([...result.data, ...inScopeFiles]);
        if (exactMatch) {
            result.data = filterExactSuffixPaths(result.data, query);
        }
        if (result.data.length >= maxResults) {
            return result;
        }

        // Populate result object with remaining result budget
        result.data = dedupeFileDetailsByAbsPath([
            ...result.data,
            ...this._filesIndex
                .search(fuseQuery, { limit: maxResults - result.data.length })
                .map((x) => ({
                    repoRoot: x.item.rootPath,
                    pathName: x.item.relPath,
                })),
        ]);
        if (exactMatch) {
            result.data = filterExactSuffixPaths(result.data, query);
        }
        return result;
    };

    findFolders = (request: FindFolderRequest): FindFolderResponse => {
        // Parse out all the options
        const exactMatch: boolean = request.data.exactMatch ?? false;
        const query: string = request.data.relPath;
        const fuseQuery: string = exactMatch ? `${query}$` : query;
        const maxResults: number = request.data.maxResults || 12;

        if (query.length < FUSE_OPTIONS.minMatchCharLength) {
            const maxDefaultResults: number = 100;
            const items = Object.values(this._foldersIndex.items).slice(0, maxDefaultResults);
            return {
                type: WebViewMessageType.findFolderResponse,
                data: items.map(pathNameToFileDetails),
            };
        }

        // Initialize result object
        let result: FindFolderResponse = { type: WebViewMessageType.findFolderResponse, data: [] };
        const matchedFolders: FileDetails[] = this._foldersIndex
            .search(fuseQuery, { limit: maxResults })
            .map((v: FuseResult<IQualifiedPathName>) => pathNameToFileDetails(v.item));

        result.data = dedupeFileDetailsByAbsPath([...result.data, ...matchedFolders]);
        return result;
    };

    public validatePath = async (pathName: IQualifiedPathName): Promise<boolean> => {
        return this.statPath(pathName).then((s) => s !== undefined);
    };

    public statPath = async (pathName: IQualifiedPathName): Promise<StatInfo | undefined> => {
        // Failing to wait for folder enumeration by workspace manager can lead
        // to false negatives at startup, dropping the entire index
        await this._workspaceManager.awaitInitialFoldersEnumerated();

        // Check that this path exists in some tracked workspace folder
        const folderRoot = this._workspaceManager.getFolderRoot(pathNameToAbsPath(pathName));
        if (!folderRoot) {
            return undefined;
        }

        try {
            return await statFile(pathNameToAbsPath(pathName));
        } catch (_err) {
            // File does not exist
            return undefined;
        }
    };
}

/**
 * Helper function to get all of the files and folders in the workspace manager
 *
 * @param workspaceManager: WorkspaceManager
 * @returns: { files: IQualifiedPathName[], folders: IQualifiedPathName[] }
 */
function getFilesAndFolders(workspaceManager: WorkspaceManager): {
    files: IQualifiedPathName[];

    folders: IQualifiedPathName[];
} {
    const blobNameToPath = (blobName: string): IQualifiedPathName | undefined => {
        return workspaceManager.getAllPathNames(blobName)[0];
    };

    const context = workspaceManager.getContextWithBlobNames();
    const files: IQualifiedPathName[] = context.blobNames
        .map(blobNameToPath)
        .filter((x): x is IQualifiedPathName => x !== undefined);

    // Only get directories that are a direct parent of a tracked file
    const absPathToFolder: Map<string, IQualifiedPathName> = new Map(
        files.map((f: IQualifiedPathName) => {
            const folder: IQualifiedPathName = {
                rootPath: f.rootPath,
                relPath: dirName(f.relPath),
            };
            return [pathNameToAbsPath(folder), folder];
        })
    );
    const folders: IQualifiedPathName[] = Array.from(absPathToFolder.values());
    return { files, folders };
}

export async function pathRangeToFileDetails(
    pathName: IQualifiedPathName,
    charRange: { charStart: number; charEnd: number }
): Promise<FileDetails> {
    const doc = await viewTextDocument(vscode.Uri.file(pathNameToAbsPath(pathName)));
    const range = new vscode.Range(
        doc.positionAt(charRange.charStart),
        doc.positionAt(charRange.charEnd)
    );

    return {
        repoRoot: pathName.rootPath,
        pathName: pathName.relPath,
        fullRange: {
            startLineNumber: range.start.line,
            startColumn: range.start.character,
            endLineNumber: range.end.line,
            endColumn: range.end.character,
        },
    };
}

/**
 * Builds and searches an index of file details based on the provided query.
 *
 * @param fileDetails: An array of FileDetails objects to be indexed and searched.
 * @param query: The search query string to match against the index.
 * @param options: Optional Fuse.js options for customizing the search behavior.
 * @returns: An array of FileDetails objects that match the search query.
 */
function buildAndSearchIndex(
    fileDetails: FileDetails[],
    query: string,
    options: {
        fuseInit: IFuseOptions<FileDetails>;
        fuseSearch: FuseSearchOptions;
    }
): FileDetails[] {
    const fuse = new Fuse(fileDetails, {
        ...FUSE_OPTIONS,
        ...options.fuseInit,
        keys: ["relPath"], // search in the "relPath" property of each FileDetails
    });
    const result = fuse.search(query, options.fuseSearch);
    return result.map((x) => x.item); // return the original FileDetails objects
}

function dedupeFileDetailsByAbsPath(files: FileDetails[]): FileDetails[] {
    const mergedFiles: Map<string, FileDetails> = new Map();
    files.forEach((file: FileDetails) => {
        const absPath = pathNameToAbsPath({ rootPath: file.repoRoot, relPath: file.pathName });
        mergedFiles.set(absPath, file);
    });
    return Array.from(mergedFiles.values());
}

function pathToIndexKey(path: IQualifiedPathName): string {
    return pathNameToAbsPath(path);
}

function sortFn(a: FuseSortFunctionArg, b: FuseSortFunctionArg): number {
    // If scores are different, sort by score
    if (a.score !== b.score) {
        return a.score - b.score;
    }

    // If there are existing matches, sort by the length of the first match
    const aString = a.matches?.[0].value;
    const bString = b.matches?.[0].value;
    if (aString && bString) {
        // If the lengths are the same, sort alphabetically
        if (aString.length === bString.length) {
            return aString.localeCompare(bString);
            // Otherwise, sort by string length
        } else {
            return aString.length - bString.length;
        }
    }

    // Fallback to sorting by score
    return a.score - b.score;
}

function filterExactSuffixPaths(details: FileDetails[], query: string): FileDetails[] {
    return details.filter((detail) => isSuffixPath(detail.pathName, query));
}

export const _exportedForTesting = {
    buildAndSearchIndex,
    filterExactSuffixPaths,
};
