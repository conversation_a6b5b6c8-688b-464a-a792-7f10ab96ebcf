import type { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";

import type { FeedbackRating } from "../types/feedback-rating";

/**
 * This file contains types shared/used by webviews.
 */

/**
 * This class is used to store result of one turn interaction with the chat model.
 * Pretty much like AugmentInstruction is for code edits.
 */
export const augmentSignInURI = "augment://sign-in";
export const signInChatResponse = `Augment is only available to signed in users. [Please sign in](${augmentSignInURI}).`;
export const awaitingSyncingPermissionResponse = `Augment works best when it has access to your entire codebase. Please grant permission to sync your workspace.`;

export class AugmentChatEntry {
    public occuredAt: Date;

    constructor(
        public message: string,
        public response: string
    ) {
        this.occuredAt = new Date();
    }
}

export type ChatFeedback = {
    requestId: string;
    rating: FeedbackRating;
    note: string;
    mode: ChatMode;
};
