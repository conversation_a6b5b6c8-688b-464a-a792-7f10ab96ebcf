import { formatDiagnostics } from "$clients/sidecar/libs/src/utils/diagnostics-utils";
import { fromVscodeDiagnosticMap } from "$vscode/src/chat/diagnostics-utils";
import { _readFile } from "$vscode/src/chat/tools-utils";

import * as vscode from "vscode";

import { viewTextDocument } from "../../workspace/view-text-document";
import { WorkspaceManager } from "../../workspace/workspace-manager";

// Mock the viewTextDocument function which is used by _readFile
jest.mock("../../workspace/view-text-document", () => ({
    viewTextDocument: jest.fn(),
}));

const mockViewTextDocument = viewTextDocument as jest.MockedFunction<typeof viewTextDocument>;

describe("formatDiagnostics", () => {
    let mockWorkspaceManager: WorkspaceManager;

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock workspace manager with required methods
        mockWorkspaceManager = {
            getFolderRoot: jest.fn().mockReturnValue("/test/workspace"),
            findBestWorkspaceRootMatch: jest.fn().mockReturnValue({
                qualifiedPathName: { rootPath: "/test/workspace" },
            }),
            getMostRecentlyChangedFolderRoot: jest.fn().mockReturnValue("/test/workspace"),
        } as any;

        // Setup default mock for viewTextDocument
        mockViewTextDocument.mockResolvedValue({
            getText: () => sampleFileContent,
        } as any);
    });

    const createDiagnostic = (
        startLine: number,
        endLine: number,
        message: string,
        startChar: number = 0,
        endChar: number = 10
    ): vscode.Diagnostic => ({
        range: new vscode.Range(
            new vscode.Position(startLine, startChar),
            new vscode.Position(endLine, endChar)
        ),
        message,
        severity: vscode.DiagnosticSeverity.Error,
        source: "test",
    });

    const sampleFileContent = [
        "line 1",
        "line 2",
        "line 3",
        "line 4 - error line",
        "line 5",
        "line 6",
        "line 7",
        "line 8",
        "line 9",
        "line 10",
    ].join("\n");

    describe("basic functionality", () => {
        it("should format diagnostics with default context lines", async () => {
            const diagnosticsMap = new Map([["test.ts", [createDiagnostic(3, 3, "Test error")]]]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager)
            );

            expect(result).toContain("test.ts");
            expect(result).toContain("L4-4: Test error");
            expect(result).toContain("     1\tline 1"); // 3 lines before
            expect(result).toContain("     4\tline 4 - error line");
            expect(result).toContain("     7\tline 7"); // 3 lines after
        });

        it("should handle empty diagnostics map", async () => {
            const diagnosticsMap = new Map<string, vscode.Diagnostic[]>();
            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager)
            );
            expect(result).toBe("");
        });

        it("should skip files with empty diagnostics array", async () => {
            const diagnosticsMap = new Map([["test.ts", []]]);
            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager)
            );
            expect(result).toBe("");
        });
    });

    describe("maxContextLines parameter", () => {
        it("should respect custom maxContextLines", async () => {
            const diagnosticsMap = new Map([["test.ts", [createDiagnostic(4, 4, "Test error")]]]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                1
            );

            expect(result).toContain("     4\tline 4 - error line"); // 1 line before
            expect(result).toContain("     5\tline 5"); // error line
            expect(result).toContain("     6\tline 6"); // 1 line after
            expect(result).not.toContain("     2\tline 3"); // should not include 2+ lines before
            expect(result).not.toContain("     7\tline 8"); // should not include 2+ lines after
        });

        it("should handle maxContextLines of 0", async () => {
            const diagnosticsMap = new Map([["test.ts", [createDiagnostic(3, 3, "Test error")]]]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                0
            );

            expect(result).toContain("     4\tline 4 - error line"); // only error line
            expect(result).not.toContain("     3\tline 3"); // no context before
            expect(result).not.toContain("     5\tline 5"); // no context after
        });
    });

    describe("maxNumDiagnosticsPerFile parameter", () => {
        it("should limit diagnostics per file and show remaining count", async () => {
            const diagnosticsMap = new Map([
                [
                    "test.ts",
                    [
                        createDiagnostic(1, 1, "Error 1"),
                        createDiagnostic(3, 3, "Error 2"),
                        createDiagnostic(5, 5, "Error 3"),
                        createDiagnostic(7, 7, "Error 4"),
                    ],
                ],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                3,
                2
            );

            expect(result).toContain("L2-2: Error 1");
            expect(result).toContain("L4-4: Error 2");
            expect(result).not.toContain("L6-6: Error 3");
            expect(result).not.toContain("L8-8: Error 4");
            expect(result).toContain("... and 2 more issues for this file");
        });

        it("should handle singular remaining diagnostic message", async () => {
            const diagnosticsMap = new Map([
                ["test.ts", [createDiagnostic(1, 1, "Error 1"), createDiagnostic(3, 3, "Error 2")]],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                3,
                1
            );

            expect(result).toContain("L2-2: Error 1");
            expect(result).not.toContain("L4-4: Error 2");
            expect(result).toContain("... and 1 more issue for this file");
        });

        it("should not show remaining message when all diagnostics fit", async () => {
            const diagnosticsMap = new Map([["test.ts", [createDiagnostic(1, 1, "Error 1")]]]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                3,
                2
            );

            expect(result).toContain("L2-2: Error 1");
            expect(result).not.toContain("more issue");
        });
    });

    describe("maxDiagnosticLineRange parameter", () => {
        it("should truncate large diagnostic ranges and show omitted lines message", async () => {
            const largeFileContent = Array.from({ length: 20 }, (_, i) => `line ${i + 1}`).join(
                "\n"
            );
            mockViewTextDocument.mockResolvedValue({
                getText: () => largeFileContent,
            } as any);

            const diagnosticsMap = new Map([
                ["test.ts", [createDiagnostic(5, 15, "Large error spanning many lines")]],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                3,
                undefined,
                4
            );

            expect(result).toContain("L6-16: Large error spanning many lines");
            expect(result).toContain("     6\tline 6"); // start lines
            expect(result).toContain("     7\tline 7");
            expect(result).toContain("... (7 more lines omitted) ..."); // omitted message
            expect(result).toContain("    15\tline 15"); // end lines
            expect(result).toContain("    16\tline 16");
            expect(result).not.toContain("    10\tline 10"); // middle lines should be omitted
        });

        it("should not truncate when diagnostic range is within limit", async () => {
            const diagnosticsMap = new Map([["test.ts", [createDiagnostic(2, 4, "Small error")]]]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                1,
                undefined,
                10
            );

            expect(result).toContain("L3-5: Small error");
            expect(result).toContain("     2\tline 2"); // context before
            expect(result).toContain("     3\tline 3"); // diagnostic lines
            expect(result).toContain("     4\tline 4 - error line");
            expect(result).toContain("     5\tline 5");
            expect(result).toContain("     6\tline 6"); // context after
            expect(result).not.toContain("omitted"); // no truncation message
        });

        it("should handle single line diagnostics with maxDiagnosticLineRange", async () => {
            const diagnosticsMap = new Map([
                ["test.ts", [createDiagnostic(3, 3, "Single line error")]],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                2,
                undefined,
                1
            );

            expect(result).toContain("L4-4: Single line error");
            expect(result).toContain("     2\tline 2"); // context
            expect(result).toContain("     4\tline 4 - error line"); // error line
            expect(result).toContain("     6\tline 6"); // context
            expect(result).not.toContain("omitted"); // no truncation for single line
        });
    });

    describe("error handling", () => {
        it("should handle file read errors gracefully", async () => {
            // Mock viewTextDocument to throw an error
            mockViewTextDocument.mockRejectedValue(new Error("File not found"));

            const diagnosticsMap = new Map([["test.ts", [createDiagnostic(3, 3, "Test error")]]]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager)
            );

            expect(result).toContain("test.ts");
            expect(result).toContain("L4-4: Test error");
            expect(result).not.toContain("\t"); // no context lines when file can't be read
        });

        it("should show remaining diagnostics message even when file read fails", async () => {
            // Mock viewTextDocument to throw an error
            mockViewTextDocument.mockRejectedValue(new Error("File not found"));

            const diagnosticsMap = new Map([
                [
                    "test.ts",
                    [
                        createDiagnostic(1, 1, "Error 1"),
                        createDiagnostic(3, 3, "Error 2"),
                        createDiagnostic(5, 5, "Error 3"),
                    ],
                ],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                3,
                1
            );

            expect(result).toContain("L2-2: Error 1");
            expect(result).not.toContain("L4-4: Error 2");
            expect(result).toContain("... and 2 more issues for this file");
        });
    });

    describe("multiple files", () => {
        it("should format diagnostics for multiple files", async () => {
            const diagnosticsMap = new Map([
                ["file1.ts", [createDiagnostic(1, 1, "Error in file 1")]],
                ["file2.ts", [createDiagnostic(3, 3, "Error in file 2")]],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                1
            );

            expect(result).toContain("file1.ts");
            expect(result).toContain("L2-2: Error in file 1");
            expect(result).toContain("file2.ts");
            expect(result).toContain("L4-4: Error in file 2");
        });

        it("should apply maxNumDiagnosticsPerFile to each file independently", async () => {
            const diagnosticsMap = new Map([
                [
                    "file1.ts",
                    [
                        createDiagnostic(1, 1, "Error 1 in file 1"),
                        createDiagnostic(3, 3, "Error 2 in file 1"),
                    ],
                ],
                [
                    "file2.ts",
                    [
                        createDiagnostic(1, 1, "Error 1 in file 2"),
                        createDiagnostic(3, 3, "Error 2 in file 2"),
                    ],
                ],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                1,
                1
            );

            expect(result).toContain("L2-2: Error 1 in file 1");
            expect(result).not.toContain("Error 2 in file 1");
            expect(result).toContain("... and 1 more issue for this file");

            expect(result).toContain("L2-2: Error 1 in file 2");
            expect(result).not.toContain("Error 2 in file 2");
            // Should have two "more issue" messages, one for each file
            expect((result.match(/more issue for this file/g) || []).length).toBe(2);
        });
    });

    describe("edge cases", () => {
        it("should handle diagnostics at file boundaries", async () => {
            const shortFile = "line 1\nline 2\nline 3";
            mockViewTextDocument.mockResolvedValue({
                getText: () => shortFile,
            } as any);

            const diagnosticsMap = new Map([
                [
                    "test.ts",
                    [
                        createDiagnostic(0, 0, "Error at start"),
                        createDiagnostic(2, 2, "Error at end"),
                    ],
                ],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                5
            );

            expect(result).toContain("L1-1: Error at start");
            expect(result).toContain("L3-3: Error at end");
            expect(result).toContain("     1\tline 1");
            expect(result).toContain("     3\tline 3");
        });

        it("should handle multi-line diagnostics", async () => {
            const diagnosticsMap = new Map([
                ["test.ts", [createDiagnostic(2, 5, "Multi-line error")]],
            ]);

            const result = await formatDiagnostics(
                fromVscodeDiagnosticMap(diagnosticsMap),
                (filePath: string) => _readFile(filePath, mockWorkspaceManager),
                1
            );

            expect(result).toContain("L3-6: Multi-line error");
            expect(result).toContain("     2\tline 2"); // context before
            expect(result).toContain("     3\tline 3"); // start of diagnostic
            expect(result).toContain("     6\tline 6"); // end of diagnostic
            expect(result).toContain("     7\tline 7"); // context after
        });
    });
});
