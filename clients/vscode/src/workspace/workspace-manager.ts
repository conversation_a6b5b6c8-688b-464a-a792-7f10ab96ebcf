import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { cloneDeep, groupBy, mapValues, uniq } from "lodash";
import * as vscode from "vscode";

import { APIServer, Language } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { CompletionServer } from "../completion-server";
import { FeatureFlagManager } from "../feature-flags";
import { newFileReader } from "../file-reader";
import { type AugmentLogger, getLogger } from "../logging";
import { ActionsModel } from "../main-panel/action-cards/actions-model";
import { ClientMetricsReporter } from "../metrics/client-metrics-reporter";
import { SourceFolderReport } from "../metrics/extension-session-event-reporter";
import { OnboardingSessionEventReporter } from "../metrics/onboarding-session-event-reporter";
import { FileEditEvent } from "../next-edit/file-edit-events";
import { FileEditManager } from "../next-edit/file-edit-events/file-edit-manager";
import { isNextEditEnabled } from "../next-edit/utils";
import { OnboardingSessionEventName } from "../onboarding/onboarding-types";
import { sha256 } from "../sha";
import { StatusTrace } from "../status-trace";
import { DisposableCollection, DisposableService } from "../utils/disposable-service";
import { isExtensionVersionGte } from "../utils/environment";
import { statFileSync } from "../utils/fs-utils";
import {
    AcceptPathImplicit,
    IgnoreSource,
    IgnoreSourceBuiltin,
    IgnoreSourceFile,
    IgnoreStackBuilder,
} from "../utils/ignore-file";
import { IncrementalTimer, MetricsCollector } from "../utils/metrics";
import { isHomeDir } from "../utils/os";
import { PathAcceptance } from "../utils/path-acceptance";
import { FullPathFilter, makePathFilter, PathFilter, PathIterator } from "../utils/path-iterator";
import {
    baseName,
    descendentPath,
    directoryContainsPath,
    findFileName,
    pathNameSansSep,
    relativePathName,
    sameDirectory,
    splitRelPath,
} from "../utils/path-utils";
import { promiseFromEvent } from "../utils/promise-utils";
import { SequenceGenerator } from "../utils/sequence-generator";
import { SingletonExecutor } from "../utils/singleton-executor";
import { FileType, SystemStateName, SystemStatus } from "../utils/types";
import { uriToAbsPath, uriToDisplayablePath, validateTrackablePath } from "../utils/uri";
import { OrderedWorkQueue } from "../utils/work-queue";
import { findVCS, VCSDetails } from "../vcs";
import { placeholders, VCSWatcher, VCSWatcherImpl } from "../vcs/watcher";
import { HeadChangeWatcher } from "../vcs/watcher/head-change-watcher";
import { StashWatcher } from "../vcs/watcher/stash-watcher";
import {
    BlobUploader,
    IOpenFileManagerV2,
    OpenFileManagerV2,
} from "../workspace/open-file-manager-v2";
import { BlobsCheckpointManager } from "./blobs-checkpoint-manager";
import { DiskFileManager } from "./disk-file-manager";
import { ExternalSourceFolderRecorder } from "./external-source-folder-recorder";
import {
    migrateMtimeCache,
    mtimeCacheExists,
    MtimeCacheWriterImpl,
    readMtimeCache,
} from "./mtime-cache";
import { BlobRange, OpenFileManager } from "./open-file-manager";
import { isOpenFileManagerV2Enabled, OpenFileManagerProxy } from "./open-file-manager-v2";
import { PathHandlerImpl } from "./path-handler";
import { PathMap, PathStatusChangeEvent } from "./path-map";
import { PathNotifier, PathNotifyEvent } from "./path-notifier";
import { SourceFolderDescriber, SourceFolderDescription } from "./source-folder-describer";
import { SyncingEnabledTracker } from "./syncing-enabled-tracker";
import { SyncingPermissionTracker } from "./syncing-permission-tracker";
import { TabSwitchEventInfo, TabWatcher } from "./tab-watcher";
import {
    IQualifiedPathInfo,
    ISourceFolderInfo,
    PathHandler,
    SourceFolderSyncingProgress,
    SyncingEnabledState,
} from "./types";
import { UnknownBlobHandler } from "./unknown-blob-handler";
import { ViewedContentInfo, ViewedContentTracker } from "./viewed-content-tracker";
import { Chunk, WorkspaceContext, WorkspaceContextWithBlobNames } from "./workspace-context";
import * as workspaceEvents from "./workspace-events";
import {
    DuplicateSourceFolderError,
    FolderRootNotADirectoryError,
    FolderSyncingPermission,
    HomeDirectoryError,
    InvalidSourceFolderUriError,
    NestedSourceFolderInfo,
    NotAnExternalSourceFolderError,
    SourceFolderEnumerationState as PublicSourceFolderEnumerationState,
    SourceFolderInfo as PublicSourceFolderInfo,
    SourceFolderItem as PublicSourceFolderItem,
    SourceFolderType as PublicSourceFolderType,
    SourceFolderNotAccessibleError,
    SourceFolderNotReadyError,
    TrackedSourceFolderInfo,
    UnknownSourceFolderError,
    UntrackedFolderInfo,
    UntrackedFolderReason,
} from "./workspace-types";
import { WorkspaceUIManagerMethods } from "./workspace-ui-model";

export interface StorageUriProvider {
    get storageUri(): vscode.Uri | undefined;
}

export enum FileChangeOrigin {
    buffer = "buffer",
    disk = "disk",
}

export type FileChangeDetails = {
    folderId: number;
    relPath: string;
    origin: FileChangeOrigin;
};

// uriToFolderName converts the given uri to a folder name. It does not verify that the uri exists
// or points to an actual folder.
function uriToAbsDirName(uri: vscode.Uri): string {
    return pathNameSansSep(uriToAbsPath(uri));
}

// validateTrackableDirname converts the given uri to an absolute path if it is a uri that Augment
// can track and formats the path as a directory name. Otherwise, it returns undefined.
function validateTrackableDirname(uri: vscode.Uri): string | undefined {
    if (uri.scheme === "file") {
        return uriToAbsDirName(uri);
    }
    return undefined;
}

enum SourceFolderType {
    workspaceFolder,
    externalFolder,
}

type SourceFolderOperation = () => Promise<void>;

// SourceFolder is a class for tracking a source folder.
class SourceFolder extends DisposableService {
    private _operationQueue: OrderedWorkQueue<SourceFolderOperation>;
    private _tracker: SourceFolderTracker | undefined;
    public _newlyTracked = false;
    private _initialEnumerationComplete = false;
    private _initialSyncComplete = false;
    private _stopped = false;

    constructor(
        public readonly folderName: string,
        public readonly folderRoot: string,
        public readonly repoRoot: string,
        public readonly workspaceFolder: vscode.WorkspaceFolder | undefined,
        public readonly vcsDetails: VCSDetails | undefined,
        public readonly folderId: number,
        public readonly diskFileManager: DiskFileManager,
        public readonly cacheDirPath: string,
        toDispose: DisposableCollection,
        priorityDispose: DisposableCollection,
        public readonly logger: AugmentLogger
    ) {
        super(toDispose, priorityDispose);

        this._operationQueue = new OrderedWorkQueue<SourceFolderOperation>(
            async (operation: SourceFolderOperation | undefined) =>
                await this._runSerializedOperation(operation)
        );

        this.addDisposables(this._operationQueue, { dispose: () => this._disposeTracker() });
    }

    public dispose() {
        this._stopped = true;
        super.dispose();
    }

    public get stopped(): boolean {
        return this._stopped;
    }

    public get type(): SourceFolderType {
        return this.workspaceFolder === undefined
            ? SourceFolderType.externalFolder
            : SourceFolderType.workspaceFolder;
    }

    private _disposeTracker() {
        this._tracker?.dispose();
        this._tracker = undefined;
    }

    public setTracker(tracker: SourceFolderTracker) {
        if (this.stopped) {
            throw new Error("Source folder has been disposed");
        }
        this._disposeTracker();
        this._tracker = tracker;
    }

    public get tracker(): SourceFolderTracker | undefined {
        return this._tracker;
    }

    public get initialEnumerationComplete(): boolean {
        return this._initialEnumerationComplete;
    }

    public setInitialEnumerationComplete() {
        this._initialEnumerationComplete = true;
    }

    public get initialSyncComplete(): boolean {
        return this._initialSyncComplete;
    }

    public setInitialSyncComplete() {
        this._initialSyncComplete = true;
    }

    // relativePathName returns the relative path from the repo root to the given absolute path if
    // that path is contained in this source folder. Otherwise, it returns undefined.
    public relativePathName(absPath: string): string | undefined {
        // Use the folderRoot, not the repo root, for the containment check, as we are interested
        // in whether the path is contained in the folder, not in the repo. But if the path is
        // indeed contained in the folder, return the path relative to the repo root, not the
        // folder root, as Augment relative path names are always relative to their repo root.
        if (directoryContainsPath(this.folderRoot, absPath)) {
            return relativePathName(this.repoRoot, absPath);
        }
        return undefined;
    }

    public acceptsPath(pathName: string): boolean {
        if (this._tracker === undefined) {
            // Either we haven't started tracking this folder yet, or we have stopped tracking it.
            // Either way, say no.
            return false;
        }
        return this._tracker.pathFilter.acceptsPath(pathName);
    }

    // enqueueSerializedOperation enqueues the given operation to this folder's serialization queue.
    public async enqueueSerializedOperation(operation: SourceFolderOperation): Promise<void> {
        this._operationQueue.insert(operation);
        await this._operationQueue.kick();
    }

    // _runSerializedOperation is the callback for the _operationQueue.
    private async _runSerializedOperation(
        operation: SourceFolderOperation | undefined
    ): Promise<void> {
        if (operation === undefined) {
            return;
        }
        if (!this._initialEnumerationComplete || this._stopped) {
            return;
        }
        await operation();
    }
}

class SourceFolderTracker extends DisposableService {
    constructor(
        public readonly pathFilter: FullPathFilter,
        public readonly pathNotifier: PathNotifier,
        toDispose: DisposableCollection
    ) {
        super(toDispose);
    }
}

// SourceFolderQualification contains summary information about a source folder that can be used
// to determine whether we can track the folder and whether we need to request permission to sync
// the folder.
type SourceFolderQualification = SourceFolderDescription & {
    repoRoot: string;
    isRepo: boolean;
};

type RegisteredSourceFolderBase = {
    folderName: string;
    isHomeDir: boolean;
    syncingPermission: FolderSyncingPermission;
    containingFolderRoot?: string;
    folderQualification?: SourceFolderQualification;
    cancel?: vscode.CancellationTokenSource;
};

type RegisteredWorkspaceFolder = RegisteredSourceFolderBase & {
    folderType: SourceFolderType.workspaceFolder;
    workspaceFolder: vscode.WorkspaceFolder;
};

type RegisteredExternalFolder = RegisteredSourceFolderBase & {
    folderType: SourceFolderType.externalFolder;
};

// See WorkspaceManager._registeredSourceFolders and _trackedSourceFolders for a description of
// "registered" source folders vs. "tracked" source folders.
type RegisteredSourceFolder = RegisteredWorkspaceFolder | RegisteredExternalFolder;

// TrackableStatus indicates whether a source folder can be tracked.
enum TrackableStatus {
    nested = "nested",
    homeDir = "home directory",
    tooLarge = "too large",
    permissionDenied = "permission denied",
    permissionNeeded = "permission needed",
    qualifying = "qualifying",
    trackable = "trackable",
}

// refusedStatuses is a set of statuses for which we will refuse to track a source folder. The user
// cannot overrided this decision. If any folder has one of these statuses, we will not sync the
// entire workspace.
const refusedStatuses = new Set<TrackableStatus>([
    TrackableStatus.homeDir,
    TrackableStatus.tooLarge,
]);

// ignoredStatus is a set of trackable statuses that cause us to ignore the folder. If any folder
// has one of these statuses, we will ignore it, but it will not prevent us from syncing the
// workspace (if it has any trackable folders).
const ignoredStatuses = new Set<TrackableStatus>([TrackableStatus.nested]);

function statusIsRefused(status: TrackableStatus): boolean {
    return refusedStatuses.has(status);
}

function statusIsIgnored(status: TrackableStatus): boolean {
    return ignoredStatuses.has(status);
}

// getTrackableStatus returns the trackable status of the given source folder.
function getTrackableStatus(folder: RegisteredSourceFolder): TrackableStatus {
    if (folder.containingFolderRoot !== undefined) {
        // We don't track nested folders separately from their containing folders.
        return TrackableStatus.nested;
    }
    if (folder.isHomeDir) {
        // We never track home directories.
        return TrackableStatus.homeDir;
    }
    if (folder.folderQualification !== undefined && !folder.folderQualification.trackable) {
        // The folder is too large to track.
        return TrackableStatus.tooLarge;
    }
    if (folder.syncingPermission === FolderSyncingPermission.denied) {
        // The user has explicitly told us not to track this folder.
        return TrackableStatus.permissionDenied;
    }
    if (folder.syncingPermission === FolderSyncingPermission.granted) {
        // The user has confirmed that we can track this folder.
        return TrackableStatus.trackable;
    }
    if (folder.folderQualification === undefined) {
        // We are in the process of determining whether we can track this folder (i.e., counting
        // all the files).
        return TrackableStatus.qualifying;
    }

    // The folder is trackable, but we haven't yet been given the go-ahead.
    return TrackableStatus.permissionNeeded;
}

// TrackedSourceFolder is a type the contains information about tracked source folders.
// * folderName: The name of the source folder. For vscode workspace folders, this is the
//   same as workspaceFolder.name. For external folders, we get to choose the name. Note that it
//   it is not the same as the folder root. (It's likely not a path name at all.)
// * workspaceFolder: The vscode workspace folder corresponding to this source folder, if any.
//   For external folders, the workspaceFolder is undefined.
// * cancel: If creation of `sourceFolder` is in progress then this field contains a valid
//   CancellationTokenSource, which can be used to cancel the in-progress SourceFolder creation.
// * sourceFolder: If the source folder is tracked, this field will contain a valid SourceFolder.
// * logger: A logger that can be used to log events related to this source folder.
type TrackedSourceFolder = {
    folderName: string;
    folderSpec: RegisteredSourceFolder;
    cancel: vscode.CancellationTokenSource;
    sourceFolder: SourceFolder | undefined;
    logger: AugmentLogger;
};

// WorkspaceManagerOptions contains optional parameters for WorkspaceManager. They are primarily
// for use in unit tests.
// * blobsCheckpointThreshold: The number of added or removed blobs to accumulate before
//   checkpointing.
export type WorkspaceManagerOptions = {
    blobsCheckpointThreshold?: number;
    useCheckpointManagerContext?: boolean;
};

export class WorkspaceManager extends DisposableService implements WorkspaceUIManagerMethods {
    // Name of sentinel file that indicates the root of the repo.
    static readonly augmentRootName = ".augmentroot";

    // Array of names of ignore sources, in order of precedence. Sources later in the array override
    // sources earlier in the array.
    static ignoreSources(folderRoot: string): Array<IgnoreSource> {
        return [
            new IgnoreSourceFile(".gitignore"),
            new IgnoreSourceBuiltin(folderRoot),
            new IgnoreSourceFile(".augmentignore"),
        ];
    }

    // Frequency at which the PathMap should persist its mtime cache.
    static readonly pathMapPersistFrequencyMs = 60_000;

    // defaultPathAccept is a PathAcceptance object that WorkspaceManager uses when it populates
    // the PathMap from the mtime cache.
    static readonly defaultPathAccept = new AcceptPathImplicit();

    private static readonly _textEncoder = new TextEncoder();

    // _initialSourceFolders contains the folderRoots of all source folders that were present when
    // the WorkspaceManager was created.
    private readonly _initialSourceFolders = new Set<string>();

    // _registeredSourceFolders and _trackedSourceFolders are both maps of source folders.
    // * _registeredSourceFolders is a map of all source folders, both workspace folders and
    //   external folders, both tracked and untracked. It is the source of truth for the set
    //   of source folders in the workspace.
    // * _trackedSourceFolders is a map containing the subset of the folders (both workspace and
    //   external) in _registeredSourceFolders that are tracked. A source folder is not tracked if
    //   it is nested inside another source folder.
    // The primary reason for the separate maps is to deal with asynchrony. _registeredSourceFolders
    // is updated exclusively by synchronous methods. Any code that changes that map must then
    // call `_kickSourceFolderReconciler` to start the (asynchronous) process of updating
    // _trackedSourceFolders to reflect the changes.
    private readonly _registeredSourceFolders = new Map<string, RegisteredSourceFolder>();
    private readonly _trackedSourceFolders: Map<string, TrackedSourceFolder> = new Map();

    // Optional set of file extensions we allow for retrieval.
    private readonly _fileExtensions: Set<string> | undefined;

    private readonly _pathMap: PathMap;
    private readonly _sequenceGenerator = new SequenceGenerator();
    private readonly _pathHandler: PathHandler;
    private readonly _openFileManager: OpenFileManagerProxy;
    private readonly _blobsCheckpointManager: BlobsCheckpointManager;
    private readonly _unknownBlobHandler: UnknownBlobHandler;
    private readonly _sourceFolderDescriber: SourceFolderDescriber;

    private readonly _logger: AugmentLogger = getLogger("WorkspaceManager");

    private _tabWatcher: TabWatcher | undefined;
    private _vcsWatcher: VCSWatcher | undefined;
    private _fileEditManager: FileEditManager | undefined;
    private _viewedContentTracker: ViewedContentTracker | undefined;

    private readonly _emptyWorkspaceDetectedEmitter: vscode.EventEmitter<void>;
    private readonly _folderEnumeratedEmitter: vscode.EventEmitter<void>;
    private readonly _folderSyncedEmitter: vscode.EventEmitter<void>;
    private readonly _syncingProgressEmitter: vscode.EventEmitter<SourceFolderSyncingProgress>;

    private _syncingPermissionInitialized = false;
    private _sourceFolderReconciler: SingletonExecutor;

    private readonly _syncingStateEmitter: vscode.EventEmitter<SyncingEnabledState>;
    private readonly _sourceFoldersChangedEmitter: vscode.EventEmitter<void>;
    private readonly _sourceFolderContentsChangedEmitter: vscode.EventEmitter<string>;
    private readonly _fileChangedEmitter: vscode.EventEmitter<FileChangeDetails>;
    private readonly _textDocumentOpenedEmitter: vscode.EventEmitter<workspaceEvents.FolderTextDocumentOpenedEvent>;
    private readonly _textDocumentClosedEmitter: vscode.EventEmitter<workspaceEvents.FolderTextDocumentClosedEvent>;
    private readonly _textDocumentChangedEmitter: vscode.EventEmitter<workspaceEvents.FolderTextDocumentChangedEvent>;
    private readonly _fileDeletedEmitter: vscode.EventEmitter<workspaceEvents.FolderFileDeletedEvent>;
    private readonly _fileWillRenameEmitter: vscode.EventEmitter<workspaceEvents.FolderFileWillRenameEvent>;
    private readonly _fileDidMoveEmitter: vscode.EventEmitter<FileDidMoveEvent>;

    private _lastChatResponse: { seq: number; timestamp: Date; text: string } | undefined =
        undefined;

    // These fields control how we determine whether to track a source folder and whether to request
    // syncing permission for it.
    //  * _enableFileLimitsForSyncingPermission controls whether we use maxTrackableFiles and
    //    maxTrackableFilesWithoutPermission to determine whether to track a folder.
    //  * _maxTrackableFiles is the maximum number of files in a trackable folder. We will refuse
    //    to track folders with more than this many files.
    //  * _maxTrackableFilesWithoutPermission is the maximum number of files that a folder can have
    //    before we will request permission to track it.
    //    Expectation: _maxTrackableFilesWithoutPermission <= _maxTrackableFiles. We lower this
    //    parameter to match _matchTrackableFiles as needed.
    //  * _verifyFolderIsSourceRepo controls whether we check whether a folder appears to be a
    //    source repo and ask for permission to track it if it doesn't.
    //  * _minUploadedFraction is used to determine whether "enough" of the files in a folder have
    //    already been uploaded (and hence whether we need to request permission to track it). If
    //    (numUploadedFiles / numTrackableFiles) >= _minUploadedFraction then we assume that the
    //    folder is already being tracked and we don't need to request permission.
    //    Expectation: _minUploadedFraction <= 1.
    //  * _refuseToSyncHomeDirectories controls whether we refuse to sync home directories.
    private readonly _enableFileLimitsForSyncingPermission: boolean;
    private readonly _maxTrackableFiles: number;
    private readonly _maxTrackableFilesWithoutPermission: number;
    private readonly _verifyFolderIsSourceRepo: boolean;
    private readonly _minUploadedFractionWithoutPermission: number;
    private readonly _refuseToSyncHomeDirectories: boolean;

    private readonly _useCheckpointManagerContext: boolean;
    private readonly _validateCheckpointManagerContext: boolean;

    private _stopping = false;

    constructor(
        private readonly _actionsModel: ActionsModel,
        private readonly _externalSourceFolderRecorder: ExternalSourceFolderRecorder,
        private readonly _syncingPermissionTracker: SyncingPermissionTracker,
        private readonly _storageUriProvider: StorageUriProvider,
        private readonly _apiServer: APIServer,
        private readonly _configListener: AugmentConfigListener,
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _clientMetricsReporter: ClientMetricsReporter,
        private readonly _completionServer: CompletionServer,
        private readonly _blobNameCalculator: BlobNameCalculator,
        private readonly _maxUploadSizeBytes: number,
        private readonly _syncingEnabledTracker: SyncingEnabledTracker,
        private readonly _onboardingSessionEventReporter: OnboardingSessionEventReporter,
        languages: Language[] = new Array<Language>(),
        options?: WorkspaceManagerOptions
    ) {
        super();

        this._enableFileLimitsForSyncingPermission =
            this._featureFlagManager.currentFlags.enableFileLimitsForSyncingPermission;
        this._maxTrackableFiles = this._featureFlagManager.currentFlags.maxTrackableFileCount;
        this._maxTrackableFilesWithoutPermission = Math.min(
            this._featureFlagManager.currentFlags.maxTrackableFileCountWithoutPermission,
            this._maxTrackableFiles
        );
        const minUploadedPercentageWithoutPermission = Math.min(
            this._featureFlagManager.currentFlags.minUploadedPercentageWithoutPermission,
            100
        );
        this._verifyFolderIsSourceRepo =
            this._featureFlagManager.currentFlags.verifyFolderIsSourceRepo;
        this._minUploadedFractionWithoutPermission = minUploadedPercentageWithoutPermission * 0.01;
        this._refuseToSyncHomeDirectories =
            this._featureFlagManager.currentFlags.refuseToSyncHomeDirectories;

        this._useCheckpointManagerContext =
            options?.useCheckpointManagerContext ??
            isExtensionVersionGte(
                this._featureFlagManager.currentFlags.useCheckpointManagerContextMinVersion
            );
        this._validateCheckpointManagerContext =
            this._featureFlagManager.currentFlags.validateCheckpointManagerContext;

        this._emptyWorkspaceDetectedEmitter = this.addDisposable(new vscode.EventEmitter<void>());
        this._folderEnumeratedEmitter = this.addDisposable(new vscode.EventEmitter<void>());
        this._folderSyncedEmitter = this.addDisposable(new vscode.EventEmitter<void>());
        this._syncingProgressEmitter = this.addDisposable(
            new vscode.EventEmitter<SourceFolderSyncingProgress>()
        );
        this._syncingStateEmitter = this.addDisposable(
            new vscode.EventEmitter<SyncingEnabledState>()
        );
        this._sourceFoldersChangedEmitter = this.addDisposable(new vscode.EventEmitter<void>());
        this._sourceFolderContentsChangedEmitter = this.addDisposable(
            new vscode.EventEmitter<string>()
        );
        this._sourceFolderContentsChangedEmitter = this.addDisposable(
            new vscode.EventEmitter<string>()
        );
        this._fileChangedEmitter = this.addDisposable(new vscode.EventEmitter<FileChangeDetails>());

        this._textDocumentOpenedEmitter = this.addDisposable(
            new vscode.EventEmitter<workspaceEvents.FolderTextDocumentOpenedEvent>()
        );

        this._textDocumentClosedEmitter = this.addDisposable(
            new vscode.EventEmitter<workspaceEvents.FolderTextDocumentClosedEvent>()
        );

        this._textDocumentChangedEmitter = this.addDisposable(
            new vscode.EventEmitter<workspaceEvents.FolderTextDocumentChangedEvent>()
        );

        this._fileDeletedEmitter = this.addDisposable(
            new vscode.EventEmitter<workspaceEvents.FolderFileDeletedEvent>()
        );

        this._fileWillRenameEmitter = this.addDisposable(
            new vscode.EventEmitter<workspaceEvents.FolderFileWillRenameEvent>()
        );

        this._fileDidMoveEmitter = this.addDisposable(new vscode.EventEmitter<FileDidMoveEvent>());

        if (this._featureFlagManager.currentFlags.bypassLanguageFilter) {
            this._fileExtensions = undefined;
        } else {
            const fileExtensions = new Set<string>();
            for (const language of languages) {
                for (const extension of language.extensions) {
                    fileExtensions.add(extension);
                }
            }
            this._fileExtensions = fileExtensions;
        }

        this._pathHandler = new PathHandlerImpl(this._maxUploadSizeBytes, newFileReader());

        this._pathMap = this.addDisposable(new PathMap());

        let openFileManagerV2: IOpenFileManagerV2 | undefined;
        if (
            isOpenFileManagerV2Enabled(
                this._configListener.config,
                this._featureFlagManager.currentFlags
            )
        ) {
            const blobUploader = new BlobUploader(this._blobNameCalculator, this._apiServer);
            this.addDisposable(blobUploader);
            openFileManagerV2 = new OpenFileManagerV2(blobUploader, this._blobNameCalculator);
        }
        const openFileManagerV1 = new OpenFileManager(
            this._apiServer,
            this._completionServer,
            this._configListener,
            this._blobNameCalculator,
            this._pathMap,
            this._sequenceGenerator
        );
        this.addDisposable(openFileManagerV1);
        this._openFileManager = new OpenFileManagerProxy(openFileManagerV1, openFileManagerV2);
        this._logger.info(
            `OpenFileManagerProxy created. V2 enabled: [${this._openFileManager.isV2Enabled}]`
        );

        const blobsCheckpointThreshold = options?.blobsCheckpointThreshold;
        this._blobsCheckpointManager = this.addDisposable(
            new BlobsCheckpointManager(
                this._apiServer,
                this._featureFlagManager,
                this._pathMap.onDidChangeBlobName,
                blobsCheckpointThreshold
            )
        );

        this._unknownBlobHandler = this.addDisposable(
            new UnknownBlobHandler(this._apiServer, this)
        );

        this._sourceFolderReconciler = this.addDisposable(
            new SingletonExecutor(() => this._reconcileSourceFolders())
        );

        this._sourceFolderDescriber = new SourceFolderDescriber(
            this._apiServer,
            this._pathHandler,
            this._fileExtensions,
            this._maxTrackableFiles
        );

        // Arrange to open and close workspace folders as they are added or removed from the
        // workspace. Process removes before adds as a folder in the add set may conflict with
        // an existing folder that is in the remove set.
        this.addDisposable(
            vscode.workspace.onDidChangeWorkspaceFolders(
                this._handleWorkspaceFolderChangeEvent.bind(this)
            )
        );

        // Track changes to text documents.
        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument(this._notifyTextDocumentChanged.bind(this))
        );

        this.addDisposable(
            vscode.workspace.onDidOpenTextDocument(this._notifyTextDocumentOpened.bind(this))
        );

        this.addDisposable(
            vscode.workspace.onDidCloseTextDocument(this._notifyTextDocumentClosed.bind(this))
        );

        // Track changes to notebook documents.
        this.addDisposable(
            vscode.workspace.onDidChangeNotebookDocument(
                this._notifyNotebookDocumentChanged.bind(this)
            )
        );

        // Subscribe to tab switch events.
        this.addDisposable(
            vscode.window.onDidChangeActiveTextEditor(this._notifyActiveEditorChanged.bind(this))
        );

        // Subscribe to close events for text documents and notebooks.
        this.addDisposable(
            vscode.workspace.onDidCloseTextDocument((document: vscode.TextDocument) => {
                this._notifyDocumentClosed(document);
            })
        );
        this.addDisposable(
            vscode.workspace.onDidCloseNotebookDocument((document: vscode.NotebookDocument) => {
                this._notifyDocumentClosed(document);
            })
        );

        this.addDisposable(
            vscode.workspace.onWillRenameFiles((event: vscode.FileWillRenameEvent) => {
                this._notifyWillRenameFile(event);
            })
        );

        this.addDisposable(
            vscode.workspace.onDidRenameFiles((event: vscode.FileRenameEvent) => {
                this._notifyDidRenameFile(event);
            })
        );

        // Subscribe to Augment configuration changes.
        this.addDisposable(this._configListener.onDidChange(this._notifyConfigChange.bind(this)));
        this._notifyConfigChange();

        // Clean up upon dispose.
        this.addDisposable(new vscode.Disposable(() => this._disposeSourceFolders()));
        this.addDisposable(new vscode.Disposable(() => this._disposeTabWatcher()));

        // We start out considering syncing permission to be granted, as there aren't any folders
        // open yet. When we open the initial set of source folders, we may learn that there are
        // some folders for which we don't have syncing permission, at which time we will assert
        // the "syncing permission needed" state.
        this._actionsModel.setSystemStateStatus(
            SystemStateName.syncingPermitted,
            SystemStatus.complete
        );

        // We also start out considering that the workspace is not too large and is not the home
        // directory.
        this._actionsModel.setSystemStateStatus(
            SystemStateName.workspaceTooLarge,
            SystemStatus.initializing
        );
        this._actionsModel.setSystemStateStatus(
            SystemStateName.uploadingHomeDir,
            SystemStatus.initializing
        );

        this._registerInitialSourceFolders();
        void this._awaitInitialSourceFolders();
    }

    public dispose() {
        this._stopping = true;
        super.dispose();
    }

    public get enableFileLimitsForSyncingPermission(): boolean {
        return this._enableFileLimitsForSyncingPermission;
    }

    public get maxTrackableFiles(): number {
        return this._maxTrackableFiles;
    }

    public get maxTrackableFilesWithoutPermission(): number {
        return this._maxTrackableFilesWithoutPermission;
    }

    public get verifyFolderIsSourceRepo(): boolean {
        return this._verifyFolderIsSourceRepo;
    }

    public get minUploadedFractionWithoutPermission(): number {
        return this._minUploadedFractionWithoutPermission;
    }

    public get refuseToSyncHomeDirectories(): boolean {
        return this._refuseToSyncHomeDirectories;
    }

    public get fileDeletedEmitter(): vscode.Event<workspaceEvents.FolderFileDeletedEvent> {
        return this._fileDeletedEmitter.event;
    }

    public get fileWillRenameEmitter(): vscode.Event<workspaceEvents.FolderFileWillRenameEvent> {
        return this._fileWillRenameEmitter.event;
    }

    // `initialFoldersEnumerated` returns true if all initial source folders have been enumerated.
    public get initialFoldersEnumerated(): boolean {
        return Array.from(this._initialSourceFolders).every((folderRoot) => {
            const registeredSourceFolder = this._registeredSourceFolders.get(folderRoot);
            if (registeredSourceFolder === undefined) {
                // This folder is no longer tracked. Skip it.
                return true;
            }
            const trackableStatus = getTrackableStatus(registeredSourceFolder);
            if (
                statusIsRefused(trackableStatus) ||
                statusIsIgnored(trackableStatus) ||
                trackableStatus === TrackableStatus.permissionDenied
            ) {
                // We aren't going to try to track this folder. Skip it.
                return true;
            }
            const folderInfo = this._trackedSourceFolders.get(folderRoot);
            return folderInfo?.sourceFolder?.initialEnumerationComplete;
        });
    }

    public async awaitInitialFoldersEnumerated(): Promise<void> {
        while (!this.initialFoldersEnumerated) {
            await promiseFromEvent(this._folderEnumeratedEmitter.event);
        }
    }

    public get onDidEnumerateFolder(): vscode.Event<void> {
        return this._folderEnumeratedEmitter.event;
    }

    public get onDidDetectEmptyWorkspace(): vscode.Event<void> {
        return this._emptyWorkspaceDetectedEmitter.event;
    }

    // `initialFoldersSynced` returns true if all initial source folders that we are permitted to
    // sync have been synced. "Synced" means that all blobs are indexed and their blob names are
    // recorded in the path map.
    public get initialFoldersSynced(): boolean {
        return Array.from(this._initialSourceFolders).every((folderRoot) => {
            const registeredSourceFolder = this._registeredSourceFolders.get(folderRoot);
            if (registeredSourceFolder === undefined) {
                // This folder is no longer tracked. Skip it.
                return true;
            }
            const trackableStatus = getTrackableStatus(registeredSourceFolder);
            if (
                statusIsRefused(trackableStatus) ||
                statusIsIgnored(trackableStatus) ||
                trackableStatus === TrackableStatus.permissionDenied
            ) {
                // We aren't going to try to track this folder. Skip it.
                return true;
            }
            const folderInfo = this._trackedSourceFolders.get(folderRoot);
            return folderInfo?.sourceFolder?.initialSyncComplete;
        });
    }

    // `awaitInitialFoldersSynced` returns a promise that resolves when `started` is true.
    public async awaitInitialFoldersSynced(): Promise<void> {
        while (!this.initialFoldersSynced) {
            await promiseFromEvent(this._folderSyncedEmitter.event);
        }
    }

    public get onDidChangeSyncingProgress(): vscode.Event<SourceFolderSyncingProgress> {
        return this._syncingProgressEmitter.event;
    }

    public get syncingEnabledState(): SyncingEnabledState {
        if (!this._syncingPermissionInitialized) {
            return SyncingEnabledState.initializing;
        }
        if (this._syncingPermissionTracker.syncingPermissionDenied) {
            return SyncingEnabledState.disabled;
        }
        let permissionNeededCount = 0;
        for (const [_folderRoot, sourceFolder] of this._registeredSourceFolders) {
            const trackableStatus = getTrackableStatus(sourceFolder);
            if (
                statusIsRefused(trackableStatus) ||
                trackableStatus === TrackableStatus.permissionDenied
            ) {
                return SyncingEnabledState.disabled;
            }
            if (trackableStatus === TrackableStatus.permissionNeeded) {
                permissionNeededCount++;
            }
        }
        if (permissionNeededCount > 0) {
            return SyncingEnabledState.partial;
        }
        return SyncingEnabledState.enabled;
    }

    public get onDidChangeSyncingState(): vscode.Event<SyncingEnabledState> {
        return this._syncingStateEmitter.event;
    }

    public get onDidChangeSourceFolders(): vscode.Event<void> {
        return this._sourceFoldersChangedEmitter.event;
    }

    public get onDidChangeSourceFolderContents(): vscode.Event<string> {
        return this._sourceFolderContentsChangedEmitter.event;
    }

    public get onDidChangeFile(): vscode.Event<FileChangeDetails> {
        return this._fileChangedEmitter.event;
    }

    /**
     * Event that fires when a file is deleted, using sidecar-compatible types.
     */
    public get onFileDeleted(): vscode.Event<FileDeletedEvent> {
        return this._fileDeletedEmitter.event;
    }

    /**
     * Event that fires when a file is moved or renamed, using sidecar-compatible types.
     */
    public get onFileDidMove(): vscode.Event<FileDidMoveEvent> {
        return this._fileDidMoveEmitter.event;
    }

    public get completionServer(): CompletionServer {
        return this._completionServer;
    }

    private _disposeSourceFolders() {
        this._registeredSourceFolders.forEach((sourceFolder) => {
            sourceFolder.cancel?.cancel();
            sourceFolder.cancel?.dispose();
            sourceFolder.cancel = undefined;
        });
        this._trackedSourceFolders.forEach((folderInfo) => folderInfo.sourceFolder?.dispose());
        this._trackedSourceFolders.clear();
        this._vcsWatcher?.dispose();
        this._fileEditManager?.dispose();
        this._viewedContentTracker?.dispose();
    }

    private _disposeTabWatcher() {
        this._tabWatcher?.dispose();
        this._tabWatcher = undefined;
    }

    private _disposeVCSWatcher() {
        this._vcsWatcher?.dispose();
        this._vcsWatcher = undefined;
    }

    private _disposeEditFileManager() {
        this._fileEditManager?.dispose();
        this._fileEditManager = undefined;
    }

    private _disposeViewedContentTracker() {
        this._viewedContentTracker?.dispose();
        this._viewedContentTracker = undefined;
    }

    // _notifyConfigChange processes changes in user configuration.
    private _notifyConfigChange() {
        if (this._stopping) {
            return;
        }

        if (this._configListener.config.recencySignalManager.collectTabSwitchEvents) {
            if (this._tabWatcher === undefined) {
                this._tabWatcher = new TabWatcher(this);
            }
        } else {
            this._disposeTabWatcher();
        }

        if (this.getEnableViewedContentTracking()) {
            if (this._viewedContentTracker === undefined) {
                this._viewedContentTracker = new ViewedContentTracker(this);
            }
        } else {
            this._disposeViewedContentTracker();
        }

        if (this._configListener.config.vcs.watcherEnabled) {
            if (this._vcsWatcher === undefined) {
                this._vcsWatcher = new VCSWatcherImpl(
                    new placeholders.FileUploaderImpl(this._blobNameCalculator, this._apiServer),
                    this._configListener
                );
            }
        } else {
            this._disposeVCSWatcher();
        }

        if (
            isNextEditEnabled(
                this._configListener.config,
                this._featureFlagManager.currentFlags.vscodeNextEditMinVersion
            ) ||
            this.getEnableCompletionFileEditEvents()
        ) {
            if (this._fileEditManager === undefined) {
                this._fileEditManager = new FileEditManager(
                    this._blobNameCalculator,
                    this._maxUploadSizeBytes,
                    this._textDocumentChangedEmitter.event,
                    this._textDocumentOpenedEmitter.event,
                    this._textDocumentClosedEmitter.event,
                    this._fileDeletedEmitter.event,
                    this._fileWillRenameEmitter.event,
                    this._configListener.config.enableDebugFeatures
                );
                this._fileEditManager.listenToEvents();

                this._trackedSourceFolders.forEach((folderInfo: TrackedSourceFolder) => {
                    if (folderInfo.sourceFolder === undefined) {
                        return;
                    }
                    this._fileEditManager?.startTracking(
                        folderInfo.sourceFolder.folderId,
                        folderInfo.sourceFolder.folderName,
                        {
                            directory: this._computeCacheDirPath(
                                folderInfo.sourceFolder.folderRoot
                            ),
                        }
                    );
                });
            }
        } else {
            this._disposeEditFileManager();
        }
    }

    public getSyncingProgress(): Array<SourceFolderSyncingProgress> {
        const result = new Array<SourceFolderSyncingProgress>();
        this._trackedSourceFolders.forEach((folderInfo, folderRoot) => {
            result.push(this._getSyncingProgress(folderRoot, folderInfo.sourceFolder));
        });
        return result;
    }

    private _reportSyncingProgress(sourceFolder: SourceFolder) {
        this._syncingProgressEmitter.fire(
            this._getSyncingProgress(sourceFolder.folderRoot, sourceFolder)
        );
    }

    private _getSyncingProgress(
        folderRoot: string,
        sourceFolder: SourceFolder | undefined
    ): SourceFolderSyncingProgress {
        const progress = !sourceFolder?.initialEnumerationComplete
            ? undefined
            : {
                  newlyTracked: sourceFolder._newlyTracked,
                  trackedFiles: this._pathMap.trackedFileCount(sourceFolder.folderId),
                  backlogSize: sourceFolder.diskFileManager.itemsInFlight,
              };
        return { folderRoot, progress };
    }

    // _isHomeDir returns true if the given folder root is the user's home directory (unless
    // the check is disabled with a feature flag, in which case it returns false.)
    private _isHomeDir(folderRoot: string): boolean {
        if (!this._featureFlagManager.currentFlags.refuseToSyncHomeDirectories) {
            return false;
        }
        return isHomeDir(folderRoot);
    }

    // _registerInitialSourceFolders adds all source folders, both workspace folders and external
    // folders, to _registeredSourceFolders and _initialSourceFolders.
    private _registerInitialSourceFolders() {
        // If the syncing permission tracker has no stored permission, implicitly grant permission
        // to sync any source folders that we had already been syncing. Doing so prevents us from
        // unnecessarily asking existing users for permission for these folders. We use the presence
        // of an mtime cache to determine whether we have already been syncing a folder.
        const alreadyTrackedFolders = new Array<string>();
        vscode.workspace.workspaceFolders?.forEach((workspaceFolder) => {
            const folderRoot = validateTrackableDirname(workspaceFolder.uri);
            if (folderRoot === undefined) {
                return;
            }
            if (this._mtimeCacheExists(folderRoot)) {
                alreadyTrackedFolders.push(folderRoot);
            }
        });
        this._syncingPermissionTracker.setDefaultPermissions(alreadyTrackedFolders);

        // Add the external source folder to the registered map. We have permission to sync
        // external source folders unless syncing permission is globally denied.
        this._externalSourceFolderRecorder.getFolders().forEach((folderName, folderRoot) => {
            if (this._isHomeDir(folderRoot)) {
                this._logger.info(`Rejecting external source folder ${folderRoot}: home directory`);
                return;
            }
            this._logger.info(`Adding external source folder ${folderRoot}`);
            this._initialSourceFolders.add(folderRoot);
            this._registeredSourceFolders.set(folderRoot, {
                folderName,
                isHomeDir: false,
                folderType: SourceFolderType.externalFolder,
                syncingPermission: this._syncingPermissionTracker.syncingPermissionDenied
                    ? FolderSyncingPermission.denied
                    : FolderSyncingPermission.granted,
            });
        });

        // Add the workspace folders to the registered map. We only have permission to sync a
        // workspace folder if it is explicitly granted.
        const permittedFolders = new Array<string>();
        vscode.workspace.workspaceFolders?.forEach((workspaceFolder) => {
            const folderName = workspaceFolder.name;
            const folderRoot = validateTrackableDirname(workspaceFolder.uri);
            if (folderRoot === undefined) {
                return;
            }
            const syncingPermission =
                this._syncingPermissionTracker.getFolderSyncingPermission(folderRoot);
            this._logger.info(
                `Adding workspace folder ${folderName}; folderRoot = ${folderRoot}; ` +
                    `syncingPermission = ${syncingPermission}`
            );
            this._initialSourceFolders.add(folderRoot);
            this._registeredSourceFolders.set(folderRoot, {
                folderName,
                isHomeDir: this._isHomeDir(folderRoot),
                folderType: SourceFolderType.workspaceFolder,
                syncingPermission,
                workspaceFolder,
            });
            if (syncingPermission === FolderSyncingPermission.granted) {
                permittedFolders.push(folderRoot);
            }
        });

        // Remove permissions for any folders that are no longer in the workspace. Syncing
        // permission is now considered initialized.
        this._syncingPermissionTracker.dropStaleFolders(permittedFolders);
        this._setSyncingPermissionInitialized();
    }

    private _mtimeCacheExists(folderRoot: string): boolean {
        const cacheDirPath = this._computeCacheDirPath(folderRoot);
        return mtimeCacheExists(cacheDirPath);
    }

    private _setSyncingPermissionInitialized() {
        this._syncingPermissionInitialized = true;
        this._syncingEnabledTracker.workspaceManager = this;
    }

    // _awaitInitialSourceFolders waits for the initial set of source folders to be synced and
    // reports startup stats to the log.
    private async _awaitInitialSourceFolders() {
        const startTime = Date.now();
        void this._kickSourceFolderReconciler();

        // Wait for all source folders to be synced.
        await this.awaitInitialFoldersSynced();

        this._reportWorkspaceStartup(Date.now() - startTime);
        this._folderSyncedEmitter.fire();
    }

    private _handleWorkspaceFolderChangeEvent(event: vscode.WorkspaceFoldersChangeEvent): void {
        for (const workspaceFolder of event.added) {
            const folderName = workspaceFolder.name;
            const folderRoot = validateTrackableDirname(workspaceFolder.uri);
            if (folderRoot === undefined) {
                continue;
            }
            const syncingPermission =
                this._syncingPermissionTracker.getFolderSyncingPermission(folderRoot);
            this._logger.info(
                `Adding workspace folder ${folderName}; folderRoot = ${folderRoot}; ` +
                    `syncingPermission = ${syncingPermission}`
            );
            this._registeredSourceFolders.set(folderRoot, {
                folderName,
                isHomeDir: this._isHomeDir(folderRoot),
                folderType: SourceFolderType.workspaceFolder,
                syncingPermission,
                workspaceFolder,
            });
        }

        const removedFolders = new Array<string>();
        for (const workspaceFolder of event.removed) {
            const folderRoot = validateTrackableDirname(workspaceFolder.uri);
            if (folderRoot === undefined) {
                continue;
            }
            this._logger.info(`Removing workspace folder ${folderRoot}`);
            const registeredSourceFolder = this._registeredSourceFolders.get(folderRoot);
            if (registeredSourceFolder === undefined) {
                continue;
            }
            registeredSourceFolder.cancel?.cancel();
            registeredSourceFolder.cancel?.dispose();
            registeredSourceFolder.cancel = undefined;
            this._registeredSourceFolders.delete(folderRoot);
            removedFolders.push(folderRoot);
        }
        this._syncingPermissionTracker.dropPermission(removedFolders);

        void this._kickSourceFolderReconciler();
    }

    // addExternalSourceFolder adds an external source folder to the workspace. This method adds
    // the folder to the list of source folders and begins the process of tracking the folder,
    // but it does not wait for the tracking to complete. It is safe to remove the folder before
    // it is fully tracked.
    public addExternalSourceFolder(rootUri: vscode.Uri): void {
        const folderRoot = validateTrackableDirname(rootUri);
        if (folderRoot === undefined) {
            throw new InvalidSourceFolderUriError();
        }
        if (this._registeredSourceFolders.has(folderRoot)) {
            throw new DuplicateSourceFolderError();
        }
        try {
            const stat = statFileSync(folderRoot);
            if (stat.type !== FileType.directory) {
                throw new FolderRootNotADirectoryError();
            }
        } catch (e: any) {
            throw new SourceFolderNotAccessibleError(getErrmsg(e));
        }
        if (this._isHomeDir(folderRoot)) {
            throw new HomeDirectoryError();
        }

        this._logger.info(`Adding external source folder ${uriToDisplayablePath(rootUri)}`);
        this._registeredSourceFolders.set(folderRoot, {
            folderName: baseName(folderRoot), // todo mlm: Use a better name?
            isHomeDir: false,
            folderType: SourceFolderType.externalFolder,
            syncingPermission: this._syncingPermissionTracker.syncingPermissionDenied
                ? FolderSyncingPermission.denied
                : FolderSyncingPermission.granted,
        });

        void this._kickSourceFolderReconciler();
    }

    // removeExternalSourceFolder removes an external source folder from the workspace.
    public removeExternalSourceFolder(folderRoot: string): void {
        const item = this._registeredSourceFolders.get(folderRoot);
        if (item === undefined) {
            return;
        }
        if (item.folderType !== SourceFolderType.externalFolder) {
            throw new NotAnExternalSourceFolderError();
        }

        this._logger.info(`Removing external source folder ${folderRoot}`);
        this._registeredSourceFolders.delete(folderRoot);

        void this._kickSourceFolderReconciler();
    }

    public enableSyncing(): void {
        this._logger.info("Enabling syncing for all trackable source folders");
        const permittedFolders = new Array<string>();
        this._registeredSourceFolders.forEach((sourceFolder, folderRoot) => {
            // If a folder is untrackable, we don't allow users to enable syncing for it.
            // If a folder is qualifying, we don't yet know if it is trackable.
            // For all other folders, grant syncing permission.
            const trackableStatus = getTrackableStatus(sourceFolder);
            if (
                statusIsRefused(trackableStatus) ||
                trackableStatus === TrackableStatus.qualifying
            ) {
                return;
            }
            sourceFolder.syncingPermission = FolderSyncingPermission.granted;
            permittedFolders.push(folderRoot);
        });

        this._syncingPermissionTracker.setPermittedFolders(permittedFolders);
        void this._kickSourceFolderReconciler();
    }

    public disableSyncing(): void {
        this._logger.info("Disabling syncing for all trackable source folders");
        this._registeredSourceFolders.forEach((sourceFolder) => {
            // We don't record permission for untrackable folders.
            // For all others, including any that we are still qualifying, deny syncing permission.
            const trackableStatus = getTrackableStatus(sourceFolder);
            if (statusIsRefused(trackableStatus)) {
                return;
            }
            sourceFolder.syncingPermission = FolderSyncingPermission.denied;
        });
        this._syncingPermissionTracker.denyPermission();

        void this._kickSourceFolderReconciler();
    }

    public requalifyLargeFolders(): void {
        this._registeredSourceFolders.forEach((sourceFolder) => {
            sourceFolder.folderQualification = undefined;
        });
        void this._kickSourceFolderReconciler();
    }

    // _kickSourceFolderReconciler processes the list of registered source folders and determines
    // which folders should be tracked and which should not. It then kicks the source folder
    // reconciler to start or stop tracking folders as needed.
    private async _kickSourceFolderReconciler(): Promise<void> {
        // Ensure that the set of recorded external source folders matches those that are
        // registered.
        await this._updateStoredExternalSourceFolders();

        // Find the set of folders that are nested inside other folders. Note that a folder can be
        // nested inside a folder that is itself a nested folder. This loop just determines which
        // folders are nested inside something else. The loop below finds the outermost containing
        // folder for each one.
        const nestedFolders = new Set<string>();
        for (const [folderRoot, folder] of this._registeredSourceFolders) {
            const trackableStatus = getTrackableStatus(folder);
            if (trackableStatus !== TrackableStatus.trackable) {
                // Skip untrackable folders -- we don't care if they are nested inside something.
                continue;
            }
            for (const [candidateFolderRoot, candidateFolder] of this._registeredSourceFolders) {
                const candidateTrackableStatus = getTrackableStatus(candidateFolder);
                if (candidateTrackableStatus !== TrackableStatus.trackable) {
                    // Skip untrackable folders -- we don't care if something is nested inside them.
                    continue;
                }
                if (folderRoot === candidateFolderRoot) {
                    continue;
                }
                if (directoryContainsPath(candidateFolderRoot, folderRoot)) {
                    nestedFolders.add(folderRoot);
                    break;
                }
            }
        }

        for (const [folderRoot, registeredSourceFolder] of this._registeredSourceFolders) {
            if (!nestedFolders.has(folderRoot)) {
                registeredSourceFolder.containingFolderRoot = undefined;
                continue;
            }
            for (const candidateFolderRoot of this._registeredSourceFolders.keys()) {
                if (folderRoot === candidateFolderRoot || nestedFolders.has(candidateFolderRoot)) {
                    continue;
                }
                if (directoryContainsPath(candidateFolderRoot, folderRoot)) {
                    if (registeredSourceFolder.containingFolderRoot !== candidateFolderRoot) {
                        this._logger.info(
                            `Source folder ${folderRoot} will not be tracked. ` +
                                `Containing folder: ${candidateFolderRoot}`
                        );
                    }
                    registeredSourceFolder.containingFolderRoot = candidateFolderRoot;
                    break;
                }
            }
        }

        // Set the current syncing permission state in the actions model.
        this._updateActionsModelState();

        // For any folders that need qualification and it isn't already in progress, start the
        // qualification process. Th qualification will run asynchronously and will call this
        // method again when it is done.
        for (const [folderRoot, registeredSourceFolder] of this._registeredSourceFolders) {
            const trackableStatus = getTrackableStatus(registeredSourceFolder);
            if (
                trackableStatus === TrackableStatus.qualifying &&
                registeredSourceFolder.cancel === undefined
            ) {
                void this._qualifySourceFolder(folderRoot, registeredSourceFolder);
            }
        }

        this._syncingStateEmitter.fire(this.syncingEnabledState);
        this._sourceFoldersChangedEmitter.fire();
        void this._sourceFolderReconciler.kick();
    }

    private async _updateStoredExternalSourceFolders(): Promise<void> {
        const externalFolders = new Map<string, string>();
        for (const [folderRoot, registeredSourceFolder] of this._registeredSourceFolders) {
            if (registeredSourceFolder.folderType !== SourceFolderType.externalFolder) {
                continue;
            }
            externalFolders.set(folderRoot, registeredSourceFolder.folderName);
        }
        await this._externalSourceFolderRecorder.setFolders(externalFolders);
    }

    // _updateActionsModelState updates the syncing permitted state in the actions model.
    private _updateActionsModelState(): void {
        if (this._syncingPermissionTracker.syncingPermissionDenied) {
            // The user has stated that they don't want Augment to sync the workspace. Don't
            // bug them.
            this._actionsModel.setSystemStateStatus(
                SystemStateName.syncingPermitted,
                SystemStatus.incomplete
            );
            return;
        }

        let permissionNeeded = false;
        let isHomeDir = false;
        let isTooLarge = false;
        for (const [_folderRoot, sourceFolder] of this._registeredSourceFolders) {
            const trackableStatus = getTrackableStatus(sourceFolder);
            if (trackableStatus === TrackableStatus.permissionNeeded) {
                permissionNeeded = true;
                break;
            } else if (trackableStatus === TrackableStatus.homeDir) {
                isHomeDir = true;
                break;
            } else if (trackableStatus === TrackableStatus.tooLarge) {
                isTooLarge = true;
                break;
            }
        }

        const syncingPermissionState = permissionNeeded
            ? SystemStatus.initializing
            : SystemStatus.complete;
        this._actionsModel.setSystemStateStatus(
            SystemStateName.syncingPermitted,
            syncingPermissionState
        );

        const syncingHomeDirState = isHomeDir ? SystemStatus.complete : SystemStatus.initializing;
        this._actionsModel.setSystemStateStatus(
            SystemStateName.uploadingHomeDir,
            syncingHomeDirState
        );

        const syncingTooLargeState = isTooLarge ? SystemStatus.complete : SystemStatus.initializing;
        this._actionsModel.setSystemStateStatus(
            SystemStateName.workspaceTooLarge,
            syncingTooLargeState
        );
    }

    // _qualifySourceFolder determines whether the given source folder can be tracked and whether
    // we need to request permission to track it. It implements the set of rules described in
    // https://www.notion.so/RFC-Requesting-Permission-to-Index-114bba10175a80048d44fe5c4921b70b.
    private async _qualifySourceFolder(
        folderRoot: string,
        registeredSourceFolder: RegisteredSourceFolder
    ): Promise<void> {
        const [repoRoot, isRepo] = await this._findRepoRoot(folderRoot);

        let description: SourceFolderDescription;
        let qualificationType: string;
        if (this._enableFileLimitsForSyncingPermission) {
            qualificationType = "full";
            this._logger.info(
                `Beginning ${qualificationType} qualification of source folder ${folderRoot}`
            );

            const cancel = new vscode.CancellationTokenSource();
            registeredSourceFolder.cancel = cancel;
            description = await this._sourceFolderDescriber.describe(
                folderRoot,
                repoRoot,
                WorkspaceManager.ignoreSources(folderRoot)
            );

            const cancelled = cancel.token.isCancellationRequested;
            if (cancelled) {
                // Qualification was cancelled, most likely because the user closed the source
                // folder. Do not touch the registered source folder, as this qualification
                // operation is no longer current. Some other operation may be in progress. Whoever
                // cancelled us will also have disposed the cancelTokenSource.
                this._logger.info(`Cancelled qualification of source folder ${folderRoot}`);
                return;
            }

            // Replace the canceller with the qualification. Doing so indicates that the
            // qualification process has finished.
            registeredSourceFolder.cancel = undefined;
            cancel.dispose();
        } else {
            qualificationType = "phony";
            this._logger.info(
                `Beginning ${qualificationType} qualification of source folder ${folderRoot} per feature flag`
            );
            description = { trackable: true, trackableFiles: 0, uploadedFraction: 1 };
        }

        const qualification = { ...description, repoRoot, isRepo };
        registeredSourceFolder.folderQualification = qualification;

        // Decide whether the source folder is trackable, and whether we need to request
        // syncing permission for it.
        if (this._syncingPermissionTracker.syncingPermissionDenied) {
            this._logger.info(
                `Finished ${qualificationType} qualification of source folder ${folderRoot}: ` +
                    `syncing disabled for workspace`
            );
        } else if (!qualification.trackable) {
            this._logger.info(
                `Finished ${qualificationType} qualification of source folder ${folderRoot}: ` +
                    `folder not trackable; too large`
            );
        } else {
            this._logger.info(
                `Finished ${qualificationType} qualification of source folder ${folderRoot}: ` +
                    `trackable files: ${qualification.trackableFiles}, ` +
                    `uploaded fraction: ${qualification.uploadedFraction}, ` +
                    `is repo: ${qualification.isRepo}`
            );

            if (qualification.trackableFiles > this._maxTrackableFilesWithoutPermission) {
                this._logger.info(
                    `Requesting syncing permission because source folder has more than ` +
                        `${this._maxTrackableFilesWithoutPermission} files`
                );
            } else if (this._verifyFolderIsSourceRepo && !qualification.isRepo) {
                this._logger.info(
                    "Requesting syncing permission because source folder does not appear to be a source repo"
                );
            } else if (
                qualification.uploadedFraction < this._minUploadedFractionWithoutPermission
            ) {
                this._logger.info(
                    `Requesting syncing permission because source folder has less than ` +
                        `${this._minUploadedFractionWithoutPermission * 100}% of files uploaded`
                );
            } else {
                // If none of the above conditions were true then the source seems safe to track
                // without requesting permission.
                registeredSourceFolder.syncingPermission = FolderSyncingPermission.granted;
                this._syncingPermissionTracker.addImplicitlyPermittedFolder(folderRoot);
            }
        }

        void this._kickSourceFolderReconciler();
    }

    // _reconcileSourceFolders is the callback function for the source folder reconciler. It
    // compares the set of registered source folders with the set of tracked source folders and
    // starts or stops tracking folders as needed.
    private async _reconcileSourceFolders(): Promise<void> {
        await this._syncingPermissionTracker.persistCurrentPermission();

        const syncingDisabled = this.syncingEnabledState === SyncingEnabledState.disabled;

        // Populate toStop with the set of folders that shouldn't be tracked but are.
        const toStop = new Map<string, [TrackedSourceFolder, string]>();
        for (const [folderRoot, folderInfo] of this._trackedSourceFolders) {
            const registeredSourceFolder = this._registeredSourceFolders.get(folderRoot);
            let reason: string | undefined;
            if (registeredSourceFolder === undefined) {
                reason = "source folder has been removed";
            } else if (syncingDisabled) {
                reason = "syncing is disabled";
            } else if (registeredSourceFolder.containingFolderRoot !== undefined) {
                reason =
                    "source folder is nested inside folder " +
                    `${registeredSourceFolder.containingFolderRoot}`;
            } else if (registeredSourceFolder.isHomeDir) {
                reason = "source folder is a home directory";
            } else if (registeredSourceFolder.folderQualification?.trackable === false) {
                reason = "source folder is too large";
            } else {
                const trackableStatus = getTrackableStatus(registeredSourceFolder);
                if (trackableStatus === TrackableStatus.permissionDenied) {
                    reason = "syncing permission denied for this source folder";
                } else if (trackableStatus === TrackableStatus.permissionNeeded) {
                    reason = "syncing permission not yet granted for this source folder";
                } else if (trackableStatus === TrackableStatus.qualifying) {
                    reason = "source folder is being qualified";
                }
            }
            if (reason !== undefined) {
                toStop.set(folderRoot, [folderInfo, reason]);
            }
        }

        // Populate toStart with the set of folders that should be tracked but aren't.
        const toStart = new Map<string, TrackedSourceFolder>();
        for (const [folderRoot, registeredSourceFolder] of this._registeredSourceFolders) {
            const trackableStatus = getTrackableStatus(registeredSourceFolder);
            if (trackableStatus !== TrackableStatus.trackable) {
                continue;
            }
            let folderInfo = this._trackedSourceFolders.get(folderRoot);
            if (folderInfo !== undefined) {
                continue;
            }

            folderInfo = {
                folderName: registeredSourceFolder.folderName,
                folderSpec: cloneDeep(registeredSourceFolder),
                cancel: new vscode.CancellationTokenSource(),
                sourceFolder: undefined,
                logger: getLogger(`WorkspaceManager[${registeredSourceFolder.folderName}]`),
            };
            toStart.set(folderRoot, folderInfo);
        }

        // Process the folders in toStop first, in case there are any conflicts between them and
        // the folders in toStart (eg, they might use the same repo root).
        for (const [folderRoot, [folderInfo, reason]] of toStop) {
            folderInfo.logger.info(`Stop tracking: ${reason}`);
            this._trackedSourceFolders.delete(folderRoot);
            this._stopTracking(folderInfo);
        }
        for (const [folderRoot, folderInfo] of toStart) {
            folderInfo.logger.info("Start tracking");
            this._trackedSourceFolders.set(folderRoot, folderInfo);
            void this._startTracking(folderRoot, folderInfo);
        }

        if (this._trackedSourceFolders.size === 0) {
            this._emptyWorkspaceDetectedEmitter.fire();
        }

        return Promise.resolve();
    }

    // _startTracking begins the process of tracking a source folder. It does not wait for the
    // process to complete. It is safe to call _stopTracking on the folder before the process
    // finishes. Doing so will cancel the in-progress operation.
    private async _startTracking(folderRoot: string, folderInfo: TrackedSourceFolder) {
        const timings = new IncrementalTimer("Startup metrics");

        const cancel = folderInfo.cancel;

        const sourceFolder = await this._createSourceFolder(folderRoot, folderInfo, cancel.token);
        if (cancel.token.isCancellationRequested) {
            // Tracking of this source folder was cancelled. Do not touch `folderInfo`, as this
            // instance of _startTracking is no longer current. Someone else (eg. another instance
            // of _startTracking) may be using it. Just dispose the source folder, if any, and bail.
            folderInfo.logger.info(`Cancelled in-progress creation of source folder`);
            sourceFolder?.dispose();
            return;
        }
        timings.charge("create SourceFolder");

        // The source folder was created without being cancelled. Dispose the cancel token source
        // and install it from the folderInfo. Install the source folder in its place.
        cancel.dispose();
        if (sourceFolder === undefined || this._stopping) {
            folderInfo.logger.info("Stopped tracking source folder");
            return;
        }
        folderInfo.sourceFolder = sourceFolder;

        const folderName = folderInfo.folderName;
        const folderId = sourceFolder.folderId;

        // Do an initial pre-population of the path map from the contents of the mtime cache. This
        // pre-population is speculative, in that we don't know whether the contents of the cache
        // match the contents of the source folder. After the pre-population is complete, we will
        // do an actual enumeration of the source folder (via "_refreshSourceFolder"), which will
        // purge any speculative entries that are still in the map.
        const mtimeCache = await readMtimeCache(folderName, sourceFolder.cacheDirPath);
        timings.charge("read MtimeCache");
        if (sourceFolder.stopped) {
            folderInfo.logger.info("Stopped tracking source folder");
            return;
        }
        for (const [relPath, cacheEntry] of mtimeCache) {
            this._pathMap.insert(
                folderId,
                relPath,
                FileType.file,
                WorkspaceManager.defaultPathAccept
            );

            // Give these entries a contentSeq of 0, so that when the real enumeration updates
            // them, the new information is guaranteed to appear newer than the speculative
            // information.
            this._pathMap.update(folderId, relPath, 0, cacheEntry.name, cacheEntry.mtime);
        }
        timings.charge("pre-populate PathMap");

        const startupDisposables = new DisposableCollection();
        try {
            // Indicate whether we are tracking this folder for the first time.
            sourceFolder._newlyTracked = mtimeCache.size === 0;
            startupDisposables.add({ dispose: () => (sourceFolder._newlyTracked = false) });

            // Do the initial enumeration (refresh) of the source folder.
            const enumerationStats = await this._refreshSourceFolder(sourceFolder, timings);
            if (enumerationStats === undefined || sourceFolder.stopped) {
                return;
            }
            timings.charge("enumerate");

            // Indicate that this folder has finished its initial enumeration. Subscribe to the
            // events we need to report the folder's state.
            sourceFolder.setInitialEnumerationComplete();
            this._folderEnumeratedEmitter.fire();
            const onDidChangePathStatus = this._pathMap.onDidChangePathStatus(folderId);
            if (onDidChangePathStatus === undefined) {
                // This source folder is no longer tracked.
                return;
            }
            sourceFolder.addDisposable(
                onDidChangePathStatus((_event: PathStatusChangeEvent) => {
                    this._sourceFolderContentsChangedEmitter.fire(folderRoot);
                }),
                true
            );
            sourceFolder.addDisposable(
                onDidChangePathStatus((_event: PathStatusChangeEvent) => {
                    this._reportSyncingProgress(sourceFolder);
                })
            );
            sourceFolder.addDisposable(
                sourceFolder.diskFileManager.onDidChangeInProgressItemCount(() =>
                    this._reportSyncingProgress(sourceFolder)
                )
            );
            this._reportSyncingProgress(sourceFolder);
            this._sourceFoldersChangedEmitter.fire();

            // Wait for the folder to be synced.
            await sourceFolder.diskFileManager.awaitQuiesced();
            sourceFolder.setInitialSyncComplete();
            this._folderSyncedEmitter.fire();
            timings?.charge("await DiskFileManager quiesced");

            // Enable persistence of the path map.
            const mtimeCacheWriter = new MtimeCacheWriterImpl(
                folderName,
                sourceFolder.cacheDirPath
            );
            this._pathMap.enablePersist(
                folderId,
                mtimeCacheWriter,
                WorkspaceManager.pathMapPersistFrequencyMs
            );
            timings.charge("enable persist");

            // We're up!
            this._reportSourceFolderStartup(
                folderInfo.logger,
                sourceFolder,
                timings,
                enumerationStats
            );
            this._onboardingSessionEventReporter.reportEvent(
                OnboardingSessionEventName.FinishedSyncing
            );
        } finally {
            startupDisposables.dispose();
            // This is the last time we will report syncing progress for this source folder
            // so report it to ensure that the newlyTracked flag is set correctly.
            this._reportSyncingProgress(sourceFolder);
        }
    }

    // _createSourceFolder creates a SourceFolder object for the given folder root. While in
    // progress, this method can be cancelled with the passed-in cancelToken.
    private async _createSourceFolder(
        folderRoot: string,
        folderInfo: TrackedSourceFolder,
        cancelToken: vscode.CancellationToken
    ): Promise<SourceFolder | undefined> {
        const folderName = folderInfo.folderName;
        const toDispose = new DisposableCollection();
        const priorityDispose = new DisposableCollection();

        const workspaceFolder =
            folderInfo.folderSpec.folderType === SourceFolderType.externalFolder
                ? undefined
                : folderInfo.folderSpec.workspaceFolder;

        const [repoRoot, _] = await this._findRepoRoot(folderRoot);
        if (cancelToken.isCancellationRequested) {
            return;
        }

        // Open the source folder in the path map.
        const folderId = this._pathMap.openSourceFolder(folderRoot, repoRoot);
        toDispose.add(new vscode.Disposable(() => this._pathMap.closeSourceFolder(folderId)));

        // Open the source folder in the open file manager.
        toDispose.addAll(...this._openFileManager.startTrackingFolder(folderName, folderId));

        // Create a disk file manager for the source folder and subscribe to changes in the size of
        // its backlog.
        const diskFileManager = new DiskFileManager(
            folderName,
            this._apiServer,
            this._pathHandler,
            this._pathMap
        );
        toDispose.add(diskFileManager);

        // Find the folder's vcs root, if any.
        const vcsDetails =
            folderInfo.folderSpec.folderType === SourceFolderType.workspaceFolder
                ? await findVCS(folderRoot)
                : undefined;

        const cacheDirPath = await this._migrateMtimeCache(folderRoot, folderInfo);

        const sourceFolder = new SourceFolder(
            folderName,
            folderRoot,
            repoRoot,
            workspaceFolder,
            vcsDetails,
            folderId,
            diskFileManager,
            cacheDirPath,
            toDispose,
            priorityDispose,
            folderInfo.logger
        );

        return sourceFolder;
    }

    // _migrateMtimeCache moves the mtime cache for the given source folder from its legacy
    // location to the correct location, if needed. Earlier versions of Augment mistakenly computed
    // the name of the cache directory using the folder name rather than the full folder root path.
    // The former is typically just the trailing component of the folder root, and is therefore not
    // guaranteed to be unique, while the latter is.
    private async _migrateMtimeCache(
        folderRoot: string,
        folderInfo: TrackedSourceFolder
    ): Promise<string> {
        const currentPath = this._computeCacheDirPath(folderRoot);
        if (mtimeCacheExists(currentPath)) {
            // The mtime cache already exists in the correct location. No migration needed.
            return currentPath;
        }

        const legacyPath = this._computeCacheDirPath(folderInfo.folderName);
        if (!mtimeCacheExists(legacyPath)) {
            // The mtime cache does not exist in either location. Nothing to migrate.
            return currentPath;
        }

        // Move the mtime cache from the legacy location to the current location.
        try {
            folderInfo.logger.info(
                `Migrating mtime cache for ${folderInfo.folderName} ` +
                    `from "${legacyPath}" to "${currentPath}"`
            );
            await migrateMtimeCache(legacyPath, currentPath);
        } catch (e: any) {
            // This method is best-effort. If it fails, log the error and continue.
            folderInfo.logger.error(
                `Failed to migrate mtime cache for ${folderInfo.folderName} ` +
                    `from "${legacyPath}" to "${currentPath}": ${getErrmsg(e)}`
            );
        }

        return currentPath;
    }

    private _computeCacheDirPath(folderRoot: string): string {
        return WorkspaceManager.computeCacheDirPath(
            folderRoot,
            this._storageUriProvider.storageUri!
        );
    }

    // _computeCacheDirPath computes the path name of the cache directory for the given source
    // folder. The cache dir is the directory where, for example, the mtime cache file is stored.
    public static computeCacheDirPath(folderRoot: string, storageUri: vscode.Uri): string {
        const storagePath = uriToAbsPath(storageUri);
        const sha = sha256(WorkspaceManager._textEncoder.encode(folderRoot));
        return joinPath(storagePath, sha);
    }

    // refreshSourceFolders refreshes the contents of all tracked source folders, both workspace
    // folders and external source folders. It also requalifies any folders that were previously
    // determined to be too large to track.
    public async refreshSourceFolders(): Promise<void> {
        this.requalifyLargeFolders();
        const promises = Array.from(this._trackedSourceFolders.values())
            .map((folderInfo) => folderInfo.sourceFolder)
            .filter((sourceFolder): sourceFolder is SourceFolder => sourceFolder !== undefined)
            .map((sourceFolder) =>
                sourceFolder.enqueueSerializedOperation(async () => {
                    await this._refreshSourceFolder(sourceFolder);
                })
            );
        try {
            // We might stop tracking one of the source folders while the refresh is in progress,
            // hence catch any error and ignore it. Use Promise.allSettled to ensure that we wait
            // for all promises to complete, even if any of them throw.
            await Promise.allSettled(promises);
        } catch (e) {
            this._logger.info(`One or more source folders failed to refresh: ${getErrmsg(e)}`);
        }
    }

    // _refreshSourceFolder refreshes the contents of the given source folder by building a new
    // SourceFolderTracker and traversing the source tree.
    private async _refreshSourceFolder(
        sourceFolder: SourceFolder,
        timings?: IncrementalTimer
    ): Promise<MetricsCollector | undefined> {
        sourceFolder.logger.debug(`Refreshing source folder ${sourceFolder.folderName}`);

        // Create a new SourceFolderTracker and install it into the source folder. If the
        // installation throws, that indicates that we are no longer tracking this source folder.
        const tracker = await this._createSourceFolderTracker(sourceFolder, timings);
        try {
            sourceFolder.setTracker(tracker);
        } catch (e) {
            sourceFolder.logger.info(
                `Failed to install SourceFolderTracker for ${sourceFolder.folderName}: ${getErrmsg(
                    e
                )}`
            );
            tracker.dispose();
            return;
        }

        // Start vcs tracking.
        const vcsDispose = this._trackVcsRepo(sourceFolder, tracker.pathFilter);
        if (vcsDispose !== undefined) {
            sourceFolder.addDisposable(vcsDispose);
        }

        const fileEditsDispose = this._trackFileEdits(sourceFolder);
        if (fileEditsDispose !== undefined) {
            sourceFolder.addDisposable(fileEditsDispose);
        }

        // Make sure the open file manager is tracking all open and accepted documents.
        this._trackOpenDocuments(sourceFolder);

        // Enumerate the contents of the source folder.
        const enumerationStats = await this._enumerateSourceFolder(sourceFolder, timings);
        return enumerationStats;
    }

    // _enumerateSourceFolder traverses the source folder to ensure that all of its paths
    // are tracked.
    private async _enumerateSourceFolder(
        sourceFolder: SourceFolder,
        timings?: IncrementalTimer
    ): Promise<MetricsCollector | undefined> {
        const tracker = sourceFolder.tracker;
        if (tracker === undefined) {
            return undefined;
        }

        // Sample the path map's next timestamp. After the enumeration is complete, any path map
        // entries with timestamps less than this are stale and will be purged.
        const purgeSeq = this._pathMap.nextEntryTS;

        // Traverse the source folder.
        const enumerationStats = await tracker.pathNotifier.enumeratePaths();
        if (sourceFolder.stopped) {
            return;
        }
        timings?.charge("enumerate paths");

        // Purge stale path map entries.
        this._pathMap.purge(sourceFolder.folderId, purgeSeq);
        timings?.charge("purge stale PathMap entries");

        return enumerationStats;
    }

    private async _createSourceFolderTracker(
        sourceFolder: SourceFolder,
        timings?: IncrementalTimer
    ): Promise<SourceFolderTracker> {
        const toDispose = new DisposableCollection();

        const pathFilter = await makePathFilter(
            vscode.Uri.file(sourceFolder.folderRoot),
            vscode.Uri.file(sourceFolder.repoRoot),
            new IgnoreStackBuilder(WorkspaceManager.ignoreSources(sourceFolder.folderRoot)),
            this._fileExtensions
        );
        timings?.charge("create PathFilter");

        const pathNotifier = this._createPathNotifier(sourceFolder, pathFilter);
        toDispose.add(pathNotifier);
        timings?.charge("create PathNotifier");

        const watcher = new StashWatcher(
            vscode.Uri.file(sourceFolder.repoRoot),
            sourceFolder.folderName,
            sourceFolder.folderId
        );
        toDispose.add(watcher);
        watcher.listenForChanges();

        if (sourceFolder.vcsDetails !== undefined) {
            // if we have vcsWatcher it means vcs is enabled.
            const watcher = new HeadChangeWatcher(
                sourceFolder.folderName,
                sourceFolder.folderId,
                sourceFolder.vcsDetails
            );
            toDispose.add(watcher);
            watcher.listenForChanges();
        }

        return new SourceFolderTracker(pathFilter, pathNotifier, toDispose);
    }

    private _createPathNotifier(
        sourceFolder: SourceFolder,
        pathFilter: FullPathFilter
    ): PathNotifier {
        const pathNotifier = new PathNotifier(
            sourceFolder.folderName,
            sourceFolder.folderRoot,
            sourceFolder.repoRoot,
            pathFilter,
            sourceFolder.workspaceFolder
        );

        // Subscribe to notifications from the path notifier. Attach the disposables for these
        // subsciptions to the path notifier itself, so that when it is disposed, the subscriptions
        // will be disposed as well.
        pathNotifier.addDisposables(
            pathNotifier.onDidFindPath((event: PathNotifyEvent) => {
                this._handlePathFound(
                    sourceFolder,
                    event.relPath,
                    event.fileType,
                    event.acceptance
                );
            }),
            pathNotifier.onDidCreatePath((event: PathNotifyEvent) => {
                this._handlePathCreated(
                    sourceFolder,
                    event.relPath,
                    event.fileType,
                    event.acceptance
                );
            }),
            pathNotifier.onDidChangePath((event: PathNotifyEvent) => {
                // We are only interested in changes to files, not any other kind of filesystem
                // object.
                if (event.fileType === FileType.file) {
                    this._handleFileChanged(sourceFolder, event.relPath, event.acceptance);
                }
            }),
            pathNotifier.onDidDeletePath((relPath: string) => {
                this._handlePathDeleted(sourceFolder, relPath);
            })
        );

        return pathNotifier;
    }

    private _trackFileEdits(sourceFolder: SourceFolder): vscode.Disposable | undefined {
        sourceFolder.logger.debug(`_trackFileEdits was called on ${sourceFolder.folderName}`);
        if (this._fileEditManager === undefined) {
            sourceFolder.logger.debug(`_fileEditManager is undefined`);
            return undefined;
        }
        sourceFolder.logger.debug(`_fileEditManager tracking the folder`);

        return this._fileEditManager.startTracking(sourceFolder.folderId, sourceFolder.folderName, {
            directory: this._computeCacheDirPath(sourceFolder.folderRoot),
        });
    }

    private _trackVcsRepo(
        sourceFolder: SourceFolder,
        pathFilter: PathFilter
    ): vscode.Disposable | undefined {
        sourceFolder.logger.debug(`_trackVcsRepo was called on ${sourceFolder.folderName}`);
        if (this._vcsWatcher === undefined) {
            sourceFolder.logger.debug(`_vcsWatcher is undefined`);
            return undefined;
        }
        const vcsDetails = sourceFolder.vcsDetails;
        if (vcsDetails === undefined) {
            sourceFolder.logger.debug(`vcsDetails is undefined`);
            return undefined;
        }
        if (!sameDirectory(uriToAbsDirName(vcsDetails.root), sourceFolder.repoRoot)) {
            sourceFolder.logger.info(
                `Not creating VCSRepoWatcher: ` +
                    `vcs root ${uriToAbsPath(vcsDetails.root)} ` +
                    `!== repo root ${sourceFolder.repoRoot}`
            );
            return undefined;
        }
        sourceFolder.logger.debug(`_vcsWatcher tracking the folder`);
        return this._vcsWatcher.startTracking(
            sourceFolder.folderName,
            sourceFolder.folderId,
            vcsDetails,
            new placeholders.FileChangeWatcherImpl(vcsDetails.root, this.onDidChangeFile),
            new placeholders.BlobNameRetrieverImpl(
                sourceFolder.repoRoot,
                this,
                this._blobNameCalculator
            ),
            new placeholders.FileUtilsImpl(pathFilter)
        );
    }

    // _findRepoRoot finds the root of the repository containing the workspace. It first looks for
    // the location of sentinel file `.augmentroot` in the file tree. It then looks for a VCS root
    // (e.g. `.git`) in the file tree. If either of these are found, it is returned along with
    // "true", indicating that folder appears to be a source repo. Otherwise, it returns the
    // workspace root along with "false", indicating that the folder does not appear to be a source
    // repo.
    private async _findRepoRoot(folderRoot: string): Promise<[string, boolean]> {
        let repoRootUri: vscode.Uri | undefined;

        repoRootUri = await findFileName(folderRoot, WorkspaceManager.augmentRootName);
        if (repoRootUri === undefined) {
            repoRootUri = (await findVCS(folderRoot))?.root;
        }
        if (repoRootUri !== undefined) {
            return [uriToAbsDirName(repoRootUri), true];
        } else {
            return [folderRoot, false];
        }
    }

    // _trackOpenDocuments ensures that all open documents that are accepted by the path filter are
    // tracked by OpenFileManager, and that documents that are tracked by OpenFileManager are
    // accepted by the path filter.
    private _trackOpenDocuments(sourceFolder: SourceFolder) {
        // Stop tracking any documents that are no longer accepted by the path filter.
        const trackedDocuments = this._openFileManager.getTrackedPaths(sourceFolder.folderId);
        for (const pathName of trackedDocuments) {
            if (!sourceFolder.acceptsPath(pathName)) {
                this._openFileManager.stopTracking(sourceFolder.folderId, pathName);
            }
        }

        // Start tracking all open documents that are accepted by the path filter. (This is a no-op
        // for documents that are already tracked.)
        vscode.workspace.textDocuments.forEach((document) => {
            const relPath = this._trackDocument(sourceFolder, document);
            if (relPath !== undefined) {
                this._fileEditManager?.addInitialDocument({
                    folderId: sourceFolder.folderId,
                    relPath: sourceFolder.relativePathName(document.uri.fsPath)!,
                    document,
                });
            }
        });
        vscode.workspace.notebookDocuments.forEach((document) => {
            this._trackDocument(sourceFolder, document);
        });
    }

    // _trackDocument starts tracking the given document if it is within the given source folder.
    // returns relPath if tracked
    private _trackDocument(
        sourceFolder: SourceFolder,
        document: vscode.TextDocument | vscode.NotebookDocument
    ): string | undefined {
        const absPath = validateTrackablePath(document.uri);
        if (absPath === undefined) {
            return;
        }
        const relPath = sourceFolder.relativePathName(absPath);
        if (relPath === undefined) {
            return;
        }
        if (!sourceFolder.acceptsPath(relPath)) {
            return;
        }
        const blobName = this._pathMap.getBlobName(sourceFolder.folderId, relPath);
        this._openFileManager.addOpenedDocument(
            {
                folderId: sourceFolder.folderId,
                relPath,
                document,
            } as
                | workspaceEvents.FolderTextDocumentOpenedEvent
                | workspaceEvents.FolderNotebookDocumentOpenedEvent,
            blobName
        );
        return relPath;
    }

    // _stopTracking stops tracking a source folder. This method can be called even if the initial
    // tracking of the source folder has not yet completed. Doing so will cancel the in-progress
    // operation.
    private _stopTracking(folderInfo: TrackedSourceFolder) {
        if (folderInfo.sourceFolder === undefined) {
            const cancel = folderInfo.cancel;
            cancel.cancel();
            cancel.dispose();
            folderInfo.logger.info("Cancelled in-progress tracking of source folder");
        } else {
            const sourceFolder = folderInfo.sourceFolder;
            folderInfo.sourceFolder = undefined;
            sourceFolder.dispose();
            folderInfo.logger.info("Stopped tracking source folder");
        }

        // In case anyone is waiting for this folder to be enumerated or synced.
        this._folderSyncedEmitter.fire();
        this._folderEnumeratedEmitter.fire();
    }

    // translateRange translates a character range in the given file to a character range in an
    // indexed blob. It returns undefined if the file is not tracked.
    public translateRange(
        qualifiedPath: QualifiedPathName,
        beginOffset: number,
        endOffset: number
    ): BlobRange | undefined {
        const result = this._resolveAbsPath(qualifiedPath.absPath);
        if (result === undefined) {
            return undefined;
        }
        const [sourceFolder, relPath] = result;
        return this._openFileManager.translateRange(
            sourceFolder.folderId,
            relPath,
            beginOffset,
            endOffset
        );
    }

    // getContext returns a WorkspaceContext that contains information about the current state of
    // the workspace.
    public getContext(): WorkspaceContext {
        if (this._openFileManager === undefined || this._pathMap === undefined) {
            return WorkspaceContext.empty();
        }
        const recencySummary = this._openFileManager.getRecencySummary(
            this._completionServer.completionParams.chunkSize
        );

        const recencyBlobNames = new Set<string>();
        const shadowedPathCounts = new Map<string, number>();
        const repoMap = new Map<string, Map<string, string>>();

        for (const [folderId, repoPathMap] of recencySummary.folderMap) {
            const repoRoot = this._pathMap.getRepoRoot(folderId);
            if (repoRoot !== undefined) {
                repoMap.set(repoRoot, repoPathMap);
            }

            for (const [pathName, blobName] of repoPathMap) {
                recencyBlobNames.add(blobName);
                let tracked = this._pathMap.getBlobName(folderId, pathName);
                if (tracked !== undefined && tracked !== blobName) {
                    // We should expect this blob name to be in the tracked Blobs object
                    // and we may need to remove it if all folders/paths are shadowed.
                    shadowedPathCounts.set(tracked, (shadowedPathCounts.get(tracked) ?? 0) + 1);
                }
            }
        }

        // At end of iteration, contains all blob names that are tracked in the pathMap
        // but should not be in the final WorkspaceContext because:
        // a) all paths to the blob in the pathMap are shadowed by recent changes and
        // b) blob is not mentioned in (other) recent changes
        const shadowedBlobNames = new Set<string>();
        for (const [blobName, count] of shadowedPathCounts) {
            if (recencyBlobNames.has(blobName)) {
                continue;
            }
            if (count === this._pathMap.getUniquePathCount(blobName)) {
                // Common path; unfortunate that we build two containers
                shadowedBlobNames.add(blobName);
            }
        }

        const recentChunks = new Array<Chunk>();
        for (const rawChunk of recencySummary.recentChunks) {
            const repoRoot = this._pathMap.getRepoRoot(rawChunk.folderId);
            if (repoRoot === undefined) {
                continue;
            }
            recentChunks.push({
                seq: rawChunk.seq,
                timestamp: rawChunk.timestamp,
                uploaded: rawChunk.uploaded,
                repoRoot,
                pathName: rawChunk.pathName,
                blobName: rawChunk.blobName,
                text: rawChunk.text,
                origStart: rawChunk.origStart,
                origLength: rawChunk.origLength,
                expectedBlobName: rawChunk.expectedBlobName,
            });
        }

        // open file manager v2 migration
        let editEvents: FileEditEvent[] = [];
        let editEventsIndexedBlobNames: string[] = [];
        if (this._openFileManager.isV2Enabled) {
            const folderToEditEventsMap = this._openFileManager.getAllEditEvents();
            for (const events of folderToEditEventsMap.values()) {
                editEvents.push(...events);
            }

            const folderToPathToIndexedBlobMap = this._openFileManager.getAllPathToIndexedBlob();
            editEventsIndexedBlobNames = [];
            for (const pathToIndexedBlob of folderToPathToIndexedBlobMap.values()) {
                for (const blobName of pathToIndexedBlob.values()) {
                    editEventsIndexedBlobNames.push(blobName);
                }
            }
        }

        const checkpointManager = this._blobsCheckpointManager;
        // The checkpoint manager maintains the set of blobs it believes to be in pathMap
        // as (checkpoint, added, deleted).
        // If we don't trust that this set actually matches what is in pathMap, then we
        // can collect the blob names ourselves and only use checkpoint manager to convert
        // blobNames to Blobs.
        // If we do trust it, then we can use it directly and avoid iterating pathMap.
        // Configuration options control whether we should trust the checkpoint manager state,
        // and whether we should trust it blindly or do the extra work to verify.
        const useCheckpointManagerContext =
            checkpointManager !== undefined && this._useCheckpointManagerContext;
        const validate = useCheckpointManagerContext && this._validateCheckpointManagerContext;

        let result: WorkspaceContext | undefined = undefined;
        if (!useCheckpointManagerContext || validate) {
            // Collect union of all blob names in recent changes and the pathMap,
            // taking care not to include multiple versions of the same path.
            const blobNameSet = new Set<string>(recencyBlobNames);
            for (const [
                folderId,
                _root,
                relPath,
                _,
                blobName,
            ] of this._pathMap.pathsWithBlobNames()) {
                if (recencySummary.folderMap.get(folderId)?.has(relPath)) {
                    continue;
                }
                blobNameSet.add(blobName);
            }

            const blobNames = Array.from(blobNameSet);
            const blobs = this._blobNamesToBlobs(blobNames);
            result = new WorkspaceContext(
                blobs,
                recentChunks,
                repoMap,
                editEvents,
                editEventsIndexedBlobNames,
                this._lastChatResponse,
                blobNames
            );
            if (!useCheckpointManagerContext) {
                return result;
            }
        }

        // The checkpoint manager tracks precisely what's in pathMap, but we've incorporated
        // recency information, so include the differences.
        const blobs = checkpointManager.getContextAdjusted(recencyBlobNames, shadowedBlobNames);
        if (result !== undefined) {
            // We computed blobs the slow way for purposes of validation
            if (!checkpointManager.validateMatching(result.blobs, blobs)) {
                this._clientMetricsReporter.report({
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    client_metric: "blob_context_mismatch",
                    value: 1,
                });
            }
        }

        return new WorkspaceContext(
            blobs,
            recentChunks,
            repoMap,
            editEvents,
            editEventsIndexedBlobNames,
            this._lastChatResponse
        );
    }

    public getContextWithBlobNames(): WorkspaceContextWithBlobNames {
        let result = this.getContext();
        if (result.blobNames !== undefined) {
            return result as WorkspaceContextWithBlobNames;
        }
        return { ...result, blobNames: this._blobsCheckpointManager.expandBlobs(result.blobs) };
    }

    // recordChatResponse records the given text as the most recent chat response. The most recent
    // chat response is included in the workspace context returned by getContext, along with a
    // sequence number that indicates its ordering relative to the context's recent chunks.
    public recordChatReponse(text: string): void {
        const seqInfo = this._sequenceGenerator.next();
        this._lastChatResponse = { seq: seqInfo.seq, timestamp: seqInfo.timestamp, text };
    }

    // _blobNamesToBlobs converts a list of blob names to a Blobs object.
    private _blobNamesToBlobs(blobNames: string[]): Blobs {
        if (this._blobsCheckpointManager === undefined) {
            return { checkpointId: undefined, addedBlobs: blobNames, deletedBlobs: [] };
        }
        return this._blobsCheckpointManager.blobsPayload(blobNames);
    }

    // handleUnknownBlobs is called when the server returns a list of unknown blob names. It
    // enqueues them to the unknown blob handler, which determines whether they are truly unknown
    // or just not indexed yet. If they are unknown, it tries to identify where the unknown blob
    // names came from and pokes the relevant components to re-upload them.
    public handleUnknownBlobs(context: WorkspaceContext, unknownBlobNames_: string[]): void {
        if (unknownBlobNames_.length === 0) {
            return;
        }

        const unknownBlobNames = new Set<string>(unknownBlobNames_);
        const toEnqueue = new Array<[string, QualifiedPathName]>();

        // First look for the blob names in the workspace context's tracked paths. Add any that are
        // found to toEnqueue and remove them from blobNames.
        for (const [repoRoot, contextPathMap] of context.trackedPaths) {
            if (repoRoot === undefined) {
                continue;
            }
            for (const [relPath, blobName] of contextPathMap) {
                if (!unknownBlobNames.has(blobName)) {
                    continue;
                }
                toEnqueue.push([blobName, new QualifiedPathName(repoRoot, relPath)]);
                unknownBlobNames.delete(blobName);
            }
        }

        // Look up the remaining unknown blob names in the path map. Add any that are found to
        // toEnqueue.
        for (const blobName of unknownBlobNames) {
            const pathName = this._pathMap.getAnyPathName(blobName);
            if (pathName !== undefined) {
                toEnqueue.push([blobName, pathName]);
            }
        }

        // Enqueue all the unknown blob names that we found above. Any that we did not find are
        // not present either in the workspace context or the path map, hence they should not be
        // mentioned in future workspace contexts, so we don't need to do anything with them.
        this._unknownBlobHandler.enqueue(toEnqueue);

        this._vcsWatcher?.handleUnknownBlobs(unknownBlobNames_);
    }

    // handleUnknownCheckpoint is called when the server returns a checkpoint not found error.
    public handleUnknownCheckpoint(requestId: string, _checkpointName: string): void {
        this._logger.info(`received checkpoint not found for request id ${requestId}`);
        this._blobsCheckpointManager.resetCheckpoint();

        // See whether a new checkpoint is needed.
        this._blobsCheckpointManager.updateBlob("");
    }

    // notifyBlobMissing tries to identify where an unknown blob name came from and pokes the
    // relevant component to re-upload it.
    public notifyBlobMissing(qualifiedPath: QualifiedPathName, blobName: string): void {
        const pathName = this._pathMap.reportMissing(blobName);
        if (pathName !== undefined) {
            const sourceFolder = this._getSourceFolder(pathName.rootPath);
            if (sourceFolder !== undefined) {
                // DiskFileManager will upload the missing blob.
                sourceFolder.diskFileManager.ingestPath(
                    sourceFolder.folderId,
                    qualifiedPath.relPath
                );
                return;
            }
        }

        const sourceFolder = this._getSourceFolder(qualifiedPath.rootPath);
        if (sourceFolder === undefined) {
            return;
        }
        if (
            this._openFileManager.handleMissingBlob(
                sourceFolder.folderId,
                qualifiedPath.relPath,
                blobName
            )
        ) {
            // OpenFileManager will upload the missing blob.
            return;
        }

        // Neither PathMap nor OpenFileManager knows about the missing blob. This means it will not
        // appear in any future workspace context, so there is nothing to do.
    }

    // _getSourceFolder returns the SourceFolder for the given repo root, or undefined if no
    // tracked source folder has that repo root.
    private _getSourceFolder(repoRoot: string): SourceFolder | undefined {
        return this._trackedSourceFolders.get(repoRoot)?.sourceFolder;
    }

    // resolvePathName returns the QualifiedPathName for the given absolute path or uri. If the
    // input is a uri with an unsupported scheme, this method returns undefined. It also returns
    // undefined if no tracked source folder contains the path.
    public resolvePathName(absPathOrUri: string | vscode.Uri): QualifiedPathName | undefined {
        const absPath =
            typeof absPathOrUri === "string" ? absPathOrUri : validateTrackablePath(absPathOrUri);
        if (absPath === undefined) {
            return undefined;
        }
        const result = this._resolveAbsPath(absPath);
        if (result === undefined) {
            return undefined;
        }
        const [sourceFolder, relPath] = result;
        return new QualifiedPathName(sourceFolder.repoRoot, relPath);
    }

    // getFolderRoot returns the folder root for the given absolute path or uri. If the input
    // is a uri with an unsupported scheme or no tracked source folder contains the path, this
    // method returns undefined.
    public getFolderRoot(absPathOrUri: string | vscode.Uri): string | undefined {
        const absPath =
            typeof absPathOrUri === "string" ? absPathOrUri : validateTrackablePath(absPathOrUri);
        if (absPath === undefined) {
            return undefined;
        }
        const result = this._resolveAbsPath(absPath);
        if (result === undefined) {
            return undefined;
        }
        const [sourceFolder, _] = result;
        return sourceFolder.folderRoot;
    }

    // safeResolvePathName returns the QualifiedPathName for the given absolute path or uri. If the
    // input is a uri with an unsupported scheme, this method returns undefined. If no tracked
    // source folder contains the path, it returns a QualifiedPathName with an empty root
    // path and the uri's absolute path as the relative path.
    public safeResolvePathName(absPathOrUri: string | vscode.Uri): QualifiedPathName | undefined {
        const absPath =
            typeof absPathOrUri === "string" ? absPathOrUri : validateTrackablePath(absPathOrUri);
        if (absPath === undefined) {
            return undefined;
        }
        const result = this._resolveAbsPath(absPath);
        if (result === undefined) {
            return new QualifiedPathName("", absPath);
        }
        const [sourceFolder, relPath] = result;
        return new QualifiedPathName(sourceFolder.repoRoot, relPath);
    }

    // _resolveAbsPath returns the SourceFolder that contains the given absolute path and the
    // path name relative to its repo root (not its folder root), or undefined if no tracked source
    // folder contains the path.
    private _resolveAbsPath(absPath: string): [SourceFolder, string] | undefined {
        for (const [_, folderInfo] of this._trackedSourceFolders) {
            if (folderInfo.sourceFolder === undefined) {
                continue;
            }
            const relPath = folderInfo.sourceFolder.relativePathName(absPath);
            if (relPath !== undefined) {
                return [folderInfo.sourceFolder, relPath];
            }
        }
        return undefined;
    }

    // hasFile returns true if the given path name is a tracked file.
    public hasFile(qualifiedPathName: QualifiedPathName): boolean {
        // We need to translate QPN to SourceFolder.
        // QPN is based on repo root.
        // SourceFolder is based on folderRoot.
        // In order to successfully convert, we rely on closest SourceFolder to the absolute root.
        const [sourceFolder, relPath] = this._resolveAbsPath(qualifiedPathName.absPath) ?? [
            undefined,
            undefined,
        ];
        if (sourceFolder === undefined || relPath === undefined) {
            return false;
        }
        return this._pathMap.hasFile(sourceFolder.folderId, qualifiedPathName.relPath);
    }

    // getBlobName returns the blob name for the given qualified path name, if any. It first looks
    // in the open file manager, then in the path map.
    public getBlobName(qualifiedPathName: QualifiedPathName): string | undefined {
        // We need to translate QPN to SourceFolder.
        // QPN is based on repo root.
        // SourceFolder is based on folderRoot.
        // In order to successfully convert, we rely on closest SourceFolder to the absolute root.
        const [sourceFolder, relPath] = this._resolveAbsPath(qualifiedPathName.absPath) ?? [
            undefined,
            undefined,
        ];
        if (sourceFolder === undefined || relPath === undefined) {
            return undefined;
        }
        return (
            this._openFileManager.getBlobName(sourceFolder.folderId, qualifiedPathName.relPath) ??
            this._pathMap.getBlobName(sourceFolder.folderId, qualifiedPathName.relPath)
        );
    }

    // getAllPathNames returns a (possibly empty) array of QualifiedPathNames that map to the
    // given blob name.
    public getAllPathNames(blobName: string): Array<QualifiedPathName> {
        return this._pathMap.getAllPathNames(blobName);
    }

    // getAllQualifiedPathInfos returns a (possibly empty) array of QualifiedPathInfos that map
    // to the given relative path. Each QualifiedPathInfo contains the QualifiedPathName, the file
    // type, and a boolean indicating whether the path is indexed.
    public getAllQualifiedPathInfos(relPath: string): Array<IQualifiedPathInfo<QualifiedPathName>> {
        return this._pathMap.getAllQualifiedPathInfos(relPath);
    }

    public getAllQualifiedPathNames(relPath: string): Array<QualifiedPathName> {
        return this._pathMap.getAllQualifiedPathNames(relPath);
    }

    public getAllPathInfo(blobName: string): Array<[string, string, string]> {
        return this._pathMap.getAllPathInfo(blobName);
    }

    // _handlePathFound is called when an object with an accepted path has name been found during
    // initial enumeration of a source folder.
    private _handlePathFound(
        sourceFolder: SourceFolder,
        relPath: string,
        fileType: FileType,
        pathAcceptance: PathAcceptance
    ): void {
        const folderId = sourceFolder.folderId;
        this._pathMap.insert(folderId, relPath, fileType, pathAcceptance);
        if (fileType === FileType.file && pathAcceptance.accepted) {
            sourceFolder.diskFileManager.ingestPath(folderId, relPath);
        }
    }

    // _handlePathCreated is called when an object with an accepted path has been created on disk.
    private _handlePathCreated(
        sourceFolder: SourceFolder,
        relPath: string,
        fileType: FileType,
        pathAcceptance: PathAcceptance
    ): void {
        const folderId = sourceFolder.folderId;
        this._pathMap.insert(folderId, relPath, fileType, pathAcceptance);
        if (!pathAcceptance.accepted) {
            return;
        }
        if (fileType === FileType.file) {
            sourceFolder.diskFileManager.ingestPath(sourceFolder.folderId, relPath);
            this._emitFileNotification(folderId, relPath, FileChangeOrigin.disk);
        } else if (fileType === FileType.directory) {
            const pathFilter = sourceFolder.tracker?.pathFilter;
            if (pathFilter === undefined) {
                return;
            }
            void sourceFolder.enqueueSerializedOperation(() =>
                this._handleDirectoryCreated(sourceFolder, relPath, pathFilter)
            );
        }
    }

    // _handleFileChanged is called when a file with an accepted path name has changed on disk.
    // This method only handles changes to files, not any other kind of filesystem object. (We
    // aren't interested in changes to other kinds of filesystem objects.)
    private _handleFileChanged(
        sourceFolder: SourceFolder,
        relPath: string,
        pathAcceptance: PathAcceptance
    ): void {
        const folderId = sourceFolder.folderId;
        this._pathMap.insert(folderId, relPath, FileType.file, pathAcceptance);
        if (!pathAcceptance.accepted) {
            return;
        }
        sourceFolder.diskFileManager.ingestPath(folderId, relPath);
        this._emitFileNotification(folderId, relPath, FileChangeOrigin.disk);
    }

    // _handlePathDeleted is called when an object with an accepted path has been deleted
    // from disk.
    private _handlePathDeleted(sourceFolder: SourceFolder, relPath: string): void {
        const folderId = sourceFolder.folderId;
        const pathInfo = this._pathMap.getPathInfo(folderId, relPath);
        if (pathInfo === undefined) {
            return;
        }
        this._deletePath(sourceFolder.folderId, relPath);
        const [fileType, pathAcceptance] = pathInfo;
        if (!pathAcceptance.accepted) {
            return;
        }
        if (fileType === FileType.directory) {
            this._handleDirectoryRemoved(sourceFolder, relPath);
        } else if (fileType === FileType.file) {
            this._emitFileNotification(folderId, relPath, FileChangeOrigin.disk);
        }
        // Fire internal event
        const qualifiedPathName = new QualifiedPathName(sourceFolder.folderRoot, relPath);
        this._fileDeletedEmitter.fire({ folderId, relPath, qualifiedPathName: qualifiedPathName });
    }

    // _deletePath removes the given path from the path map.
    private _deletePath(folderId: number, relPath: string): void {
        this._pathMap.remove(folderId, relPath);
    }

    // _handleDirectoryCreated is called when a directory with an accepted path name has been
    // created within a source folder. It adds all of the paths in the directory to the path map.
    private async _handleDirectoryCreated(
        sourceFolder: SourceFolder,
        relPath: string,
        pathFilter: FullPathFilter
    ) {
        sourceFolder.logger.info(`Directory created: ${relPath}`);
        const repoRootUri = vscode.Uri.file(sourceFolder.repoRoot);
        const pathIterator = new PathIterator(
            sourceFolder.folderName,
            vscode.Uri.joinPath(repoRootUri, relPath),
            repoRootUri,
            pathFilter
        );

        for await (const [_fileUri, relPath, fileType, acceptance] of pathIterator) {
            this._handlePathFound(sourceFolder, relPath, fileType, acceptance);
        }
    }

    // _handleDirectoryRemoved is called when a directory has been removed from a source folder.
    // It removes all of the paths in the directory from the path map.
    private _handleDirectoryRemoved(sourceFolder: SourceFolder, relDirPath: string) {
        sourceFolder.logger.info(`Directory removed: ${relDirPath}`);
        const folderId = sourceFolder.folderId;
        const toRemove = new Array<string>();
        for (const [relPath] of this._pathMap.pathsInFolder(folderId)) {
            if (descendentPath(relDirPath, relPath) !== undefined) {
                toRemove.push(relPath);
            }
        }

        for (const relPath of toRemove) {
            this._deletePath(folderId, relPath);
        }
    }

    // _notifyActiveEditorChanged is called by VSCode when the active editor changes. VSCode
    // actually calls this function twice for every active editor change, once with activeTextEditor
    // undefined, and then with the actual information.
    private _notifyActiveEditorChanged(activeTextEditor: vscode.TextEditor | undefined) {
        const document = activeTextEditor?.document;
        const pathInfo = this._uriToPathInfo(document?.uri);
        if (pathInfo === undefined) {
            this._openFileManager.loseFocus();
            return;
        }
        const [folderId, relPath] = pathInfo;

        this._openFileManager.activeEditorChanged({
            folderId,
            relPath,
            document: document!,
        } as workspaceEvents.FolderTextDocumentOpenedEvent);
    }

    // _notifyTextDocumentChanged is called by VSCode when a tracked text document (in-memory file)
    // is changed.
    private _notifyTextDocumentChanged(event: vscode.TextDocumentChangeEvent) {
        const pathInfo = this._uriToPathInfo(event.document.uri);
        if (pathInfo === undefined) {
            return;
        }
        const [folderId, relPath] = pathInfo;
        this._openFileManager.handleChangedDocument({
            folderId,
            relPath,
            event,
        });
        this._emitFileNotification(folderId, relPath, FileChangeOrigin.buffer);
        this._textDocumentChangedEmitter.fire({ folderId, relPath, event });
    }

    private _notifyTextDocumentOpened(document: vscode.TextDocument) {
        const pathInfo = this._uriToPathInfo(document.uri);
        if (pathInfo === undefined) {
            return;
        }
        const [folderId, relPath] = pathInfo;
        this._textDocumentOpenedEmitter.fire({ folderId, relPath, document });
    }

    private _notifyTextDocumentClosed(document: vscode.TextDocument) {
        const pathInfo = this._uriToPathInfo(document.uri);
        if (pathInfo === undefined) {
            return;
        }
        const [folderId, relPath] = pathInfo;
        this._textDocumentClosedEmitter.fire({ folderId, relPath, document });
    }

    // _notifyNotebookDocumentChanged is called by VSCode when a tracked notebook document (in-
    // memory notebook) is changed.
    private _notifyNotebookDocumentChanged(event: vscode.NotebookDocumentChangeEvent) {
        const pathInfo = this._uriToPathInfo(event.notebook.uri);
        if (pathInfo === undefined) {
            return;
        }
        const [folderId, relPath] = pathInfo;
        this._openFileManager.handleChangedDocument({ folderId, relPath, event });
        this._emitFileNotification(folderId, relPath, FileChangeOrigin.buffer);
    }

    // _uriToPathInfo determines whether the given uri corresponds to a trackable path, and if so,
    // returns its folderId and relative path name.
    private _uriToPathInfo(uri: vscode.Uri | undefined): [number, string] | undefined {
        if (uri === undefined) {
            return undefined;
        }
        const absPath = validateTrackablePath(uri);
        if (absPath === undefined) {
            return undefined;
        }
        const result = this._resolveAbsPath(absPath);
        if (result === undefined) {
            return undefined;
        }
        const [sourceFolder, relPath] = result;
        if (!sourceFolder.acceptsPath(relPath)) {
            return undefined;
        }
        return [sourceFolder.folderId, relPath];
    }

    private _notifyWillRenameFile(event: vscode.FileWillRenameEvent) {
        event.files.forEach((file) => {
            const oldFileDetails: [SourceFolder, string] | undefined = this._resolveAbsPath(
                file.oldUri.fsPath
            );
            const newFileDetails: [SourceFolder, string] | undefined = this._resolveAbsPath(
                file.newUri.fsPath
            );
            if (oldFileDetails === undefined || newFileDetails === undefined) {
                return;
            }
            const [oldSourceFolder, oldRelPath] = oldFileDetails;
            const [newSourceFolder, newRelPath] = newFileDetails;

            // Rename should not cause a file to move between source folders.
            if (oldSourceFolder.folderId !== newSourceFolder.folderId) {
                this._logger.debug(
                    `[WARN] Rename should not cause a file to move between source folders. ` +
                        `    old file: ${oldFileDetails[1]} ` +
                        `    new file: ${newFileDetails[1]}` +
                        `    old source folder: ${oldSourceFolder.folderName} ` +
                        `    new source folder: ${newSourceFolder.folderName}`
                );
                return;
            }

            // Fire internal event
            this._fileWillRenameEmitter.fire({
                folderId: oldSourceFolder.folderId,
                oldRelPath: oldRelPath,
                newRelPath: newRelPath,
                type: statFileSync(file.oldUri.fsPath).type,
            });
        });
    }

    private _notifyDidRenameFile(event: vscode.FileRenameEvent) {
        event.files.forEach((file) => {
            const oldFileDetails: [SourceFolder, string] | undefined = this._resolveAbsPath(
                file.oldUri.fsPath
            );
            const newFileDetails: [SourceFolder, string] | undefined = this._resolveAbsPath(
                file.newUri.fsPath
            );
            if (oldFileDetails === undefined || newFileDetails === undefined) {
                return;
            }
            const [oldSourceFolder, oldRelPath] = oldFileDetails;
            const [newSourceFolder, newRelPath] = newFileDetails;

            // Fire sidecar-compatible event
            const oldQualifiedPathName = new QualifiedPathName(
                oldSourceFolder.folderRoot,
                oldRelPath
            );
            const newQualifiedPathName = new QualifiedPathName(
                newSourceFolder.folderRoot,
                newRelPath
            );
            this._fileDidMoveEmitter.fire({
                oldQualifiedPathName,
                newQualifiedPathName,
            });
        });
    }

    // _notifyDocumentClosed is called by VSCode when a tab containing a tracked document is closed.
    private _notifyDocumentClosed(document: vscode.TextDocument | vscode.NotebookDocument) {
        const uri = document.uri;
        const absPath = validateTrackablePath(uri);
        if (absPath === undefined) {
            return;
        }
        const result = this._resolveAbsPath(absPath);
        if (result === undefined) {
            return;
        }
        const [sourceFolder, pathName] = result;
        this._openFileManager.handleClosedDocument({
            folderId: sourceFolder.folderId,
            relPath: pathName,
            document,
        } as
            | workspaceEvents.FolderTextDocumentClosedEvent
            | workspaceEvents.FolderNotebookDocumentClosedEvent);
    }

    private _emitFileNotification(folderId: number, relPath: string, origin: FileChangeOrigin) {
        this._fileChangedEmitter.fire({ folderId, relPath, origin });
    }

    public getTabSwitchEvents(): TabSwitchEventInfo[] | undefined {
        return this._tabWatcher?.getTabSwitchEvents();
    }

    public getAllViewedContent(): ViewedContentInfo[] {
        return this._viewedContentTracker?.getAllViewedContent() ?? [];
    }

    public getFileEditEvents(folderRoot: string | undefined = undefined): FileEditEvent[] {
        if (this._fileEditManager === undefined) {
            return [];
        }

        let folderId: number;
        if (folderRoot !== undefined) {
            const sourceFolder = this._trackedSourceFolders.get(folderRoot)?.sourceFolder;
            if (sourceFolder === undefined) {
                return [];
            }
            folderId = sourceFolder.folderId;
        } else {
            folderId = this._fileEditManager.findFolderIdWithMostRecentChanges();
        }
        if (folderId === -1) {
            return [];
        }
        return this._fileEditManager.findEventsForFolder(folderId);
    }

    public getMostRecentlyChangedFolderRoot(): string | undefined {
        if (this._fileEditManager === undefined) {
            return undefined;
        }
        const folderId = this._fileEditManager.findFolderIdWithMostRecentChanges();
        if (folderId === -1) {
            return undefined;
        }
        for (const [folderRoot, folderInfo] of this._trackedSourceFolders) {
            if (folderInfo.sourceFolder?.folderId === folderId) {
                return folderRoot;
            }
        }
        return undefined;
    }

    /**
     * Gets the best folder root to use for operations.
     * Prioritizes the currently open folder root (from active text editor) if available,
     * otherwise falls back to the most recently changed folder root.
     *
     * @returns The best folder root to use, or undefined if none is available
     */
    public getBestFolderRoot(): string | undefined {
        return vscode.window.activeTextEditor
            ? this.getFolderRoot(vscode.window.activeTextEditor?.document.uri)
            : this.getMostRecentlyChangedFolderRoot();
    }

    /**
     * Finds the best workspace root match for a given relative path.
     * The match includes:
     * - rootPath: The path of the workspace root.
     * - relPath: The relative path of the best-match directory.
     *
     * @param relPath The relative path of to resolve.
     * @returns The best QualifiedPathName. If no match is found, the last
     *   accessed workspace root is returned.
     */
    public findBestWorkspaceRootMatch(relPath: string): IQualifiedPathInfo | undefined {
        const pathSegments = splitRelPath(relPath).slice(0, -1);
        let currentPath = "";
        let bestMatch: IQualifiedPathInfo | undefined = undefined;

        for (const segment of pathSegments) {
            // Try to find a workspace root that matches the current path
            currentPath = joinPath(currentPath, segment);
            // Only consider paths that are accepted
            const pathInfos = this.getAllQualifiedPathInfos(currentPath).filter(
                (info: IQualifiedPathInfo) => info.isAccepted
            );

            // If no paths found, break. We will not find a match more specific
            if (pathInfos.length === 0) {
                break;
            }

            bestMatch = pathInfos[0];
        }

        if (bestMatch !== undefined) {
            return bestMatch;
        }

        // If no match is found, return the last accessed workspace root.
        const lastAccessedFolderRoot = this.getMostRecentlyChangedFolderRoot();
        const repoRoot = lastAccessedFolderRoot
            ? this.getRepoRootForFolderRoot(lastAccessedFolderRoot)
            : undefined;

        if (repoRoot !== undefined) {
            return {
                qualifiedPathName: new QualifiedPathName(repoRoot, ""),
                fileType: FileType.directory,
                isAccepted: false,
            };
        }

        // If there is no last accessed workspace root, use the first workspace root.
        const sourceFolders = this.listSourceFolders().filter((folder) => {
            return (
                folder.type === PublicSourceFolderType.vscodeWorkspaceFolder &&
                folder.syncingEnabled
            );
        });
        if (sourceFolders.length > 0) {
            return {
                qualifiedPathName: new QualifiedPathName(sourceFolders[0].folderRoot, ""),
                fileType: FileType.directory,
                isAccepted: false,
            };
        }

        return undefined;
    }

    public getRepoRootForFolderRoot(folderRoot: string): string | undefined {
        const sourceFolder = this._trackedSourceFolders.get(folderRoot)?.sourceFolder;
        return sourceFolder?.repoRoot;
    }

    public getVCSWatchedFolderIds(): number[] {
        return this?._vcsWatcher?.getWatchedFolderIds() ?? [];
    }

    public async getVCSChange(): Promise<VCSChange> {
        if (this._vcsWatcher === undefined) {
            // we need this while this is behind a feature flag
            return {
                commits: [],
                workingDirectory: [],
            };
        }
        const changes = await this._vcsWatcher.getChanges();
        return changes;
    }

    public getEnableCompletionFileEditEvents(): boolean {
        return this._featureFlagManager.currentFlags.enableCompletionFileEditEvents;
    }

    public getEnableViewedContentTracking(): boolean {
        return this._featureFlagManager.currentFlags.enableViewedContentTracking;
    }

    public getViewedContentConfig(): {
        closeRangeThreshold: number;
        discreteJumpThreshold: number;
        minEventAgeMs: number;
        maxEventAgeMs: number;
        maxTrackedFiles: number;
        maxSameFileEntries: number;
    } {
        const flags = this._featureFlagManager.currentFlags;
        return {
            closeRangeThreshold: flags.viewedContentCloseRangeThreshold,
            discreteJumpThreshold: flags.viewedContentDiscreteJumpThreshold,
            minEventAgeMs: flags.viewedContentMinEventAgeMs,
            maxEventAgeMs: flags.viewedContentMaxEventAgeMs,
            maxTrackedFiles: flags.viewedContentMaxTrackedFiles,
            maxSameFileEntries: flags.viewedContentMaxSameFileEntries,
        };
    }

    public async updateStatusTrace(trace: StatusTrace) {
        trace.addSection("Syncing permission parameters");
        trace.addValue(
            "enableFileLimitsForSyncingPermission",
            this.enableFileLimitsForSyncingPermission
        );
        trace.addValue("maxTrackableFiles", this.maxTrackableFiles);
        trace.addValue(
            "maxTrackableFilesWithoutPermission",
            this.maxTrackableFilesWithoutPermission
        );
        trace.addValue(
            "minUploadedFractionWithoutPermission",
            this.minUploadedFractionWithoutPermission
        );
        trace.addValue(
            "minUploadedFractionWithoutPermission as a percentage",
            this.minUploadedFractionWithoutPermission * 100
        );
        trace.addValue("verifyFolderIsSourceRepo", this.verifyFolderIsSourceRepo);
        trace.addValue("refuseToSyncHomeDirectories", this.refuseToSyncHomeDirectories);
        let folderCount = 0;
        for (const [folderRoot, registeredFolder] of this._registeredSourceFolders) {
            folderCount++;
            trace.addSection(`Source folder: ${folderRoot}`);
            if (registeredFolder.folderType === SourceFolderType.workspaceFolder) {
                trace.addValue("Folder type", "vscode workspace folder");
            } else {
                trace.addValue("Folder type", "external folder");
            }
            if (registeredFolder.containingFolderRoot !== undefined) {
                trace.addValue(
                    "Not tracked: nested folder. Containing folder",
                    registeredFolder.containingFolderRoot
                );
                continue;
            }
            if (registeredFolder.isHomeDir) {
                trace.addLine("Not tracked: home directory");
                continue;
            }
            if (
                registeredFolder.folderQualification !== undefined &&
                !registeredFolder.folderQualification.trackable
            ) {
                trace.addLine("Not tracked: folder is too large");
                continue;
            }
            const trackableStatus = getTrackableStatus(registeredFolder);
            if (trackableStatus === TrackableStatus.permissionDenied) {
                trace.addLine("Not tracked: syncing permission denied");
                continue;
            }
            if (trackableStatus === TrackableStatus.permissionNeeded) {
                trace.addLine("Not tracked: syncing permission not yet granted");
                continue;
            }
            const sourceFolder = this._trackedSourceFolders.get(folderRoot)?.sourceFolder;
            if (sourceFolder === undefined) {
                trace.addLine("Tracking in progress");
                continue;
            }
            trace.addValue("Folder root", folderRoot);
            trace.addValue("Repo root", sourceFolder.repoRoot);
            trace.addValue("Mtime cache dir", sourceFolder.cacheDirPath);
            const started = sourceFolder.diskFileManager.itemsInFlight === 0;
            if (!started) {
                trace.addValue("Source folder startup", "in progress");
            }
            trace.addValue("Source folder startup", "complete");
            trace.addValue("Tracked files", this._pathMap.trackedFileCount(sourceFolder.folderId));
            trace.addValue("Syncing backlog size", sourceFolder.diskFileManager.itemsInFlight);
        }
        if (folderCount === 0) {
            trace.addSection("Source folders: no open source folders");
        }

        trace.addSection("Workspace status");
        if (!this.initialFoldersSynced) {
            trace.addValue("Workspace startup", "in progress");
        } else {
            trace.addValue("Workspace startup", "complete");
            const workspaceContext = this.getContextWithBlobNames();
            trace.addValue("Blobs in context", workspaceContext.blobNames.length);
            const savePoint = trace.savePoint();
            try {
                const probeBatchSize = 1000;
                let unknownBlobNames = 0;
                for (
                    let blobIdx = 0;
                    blobIdx < workspaceContext.blobNames.length;
                    blobIdx += probeBatchSize
                ) {
                    trace.rollback(savePoint);
                    trace.addLine(
                        "Verifying blob names... " +
                            `${blobIdx} / ${workspaceContext.blobNames.length} `
                    );
                    trace.publish();

                    const result = await this._apiServer.findMissing(
                        workspaceContext.blobNames.slice(blobIdx, blobIdx + probeBatchSize)
                    );
                    unknownBlobNames += result.unknownBlobNames.length;
                }
                trace.rollback(savePoint);
                trace.addValue("Unknown blob names", unknownBlobNames);
            } catch (e: any) {
                trace.rollback(savePoint);
                trace.addError(`Unable to verify blob names: ${e}`);
            }
        }
        if (folderCount === 0) {
            trace.addLine("No source folders in workspace");
        }

        const checkpointManager = this._blobsCheckpointManager;
        if (checkpointManager !== undefined) {
            const checkpoint = checkpointManager.getContext();
            const numCheckpointBlobs = checkpointManager.getCheckpointedBlobNames().length;
            trace.addValue("Current checkpoint", checkpoint.checkpointId);
            trace.addValue("Blobs in current checkpoint", numCheckpointBlobs);
            trace.addValue("Added blobs not in checkpoint", checkpoint.addedBlobs.length);
            trace.addValue("Deleted blobs not in checkpoint", checkpoint.deletedBlobs.length);
        }
    }

    private _reportSourceFolderStartup(
        logger: AugmentLogger,
        sourceFolder: SourceFolder,
        timings: IncrementalTimer,
        enumerationStats: MetricsCollector
    ) {
        const dfmStats = sourceFolder.diskFileManager.metrics;

        logger.info("Tracking enabled");
        logger.info(enumerationStats.format());
        logger.info(dfmStats.format());
        logger.info(timings.format());
    }

    private _reportWorkspaceStartup(totalMs: number) {
        this._logger.info(`Workspace startup complete in ${totalMs} ms`);
    }

    // trackedSourceFolderNames returns an array of ISourceFolderInfo objects for all tracked
    // source folders in the workspace.
    public trackedSourceFolderNames(): Array<ISourceFolderInfo> {
        return Array.from(this._registeredSourceFolders)
            .filter(([_folderRoot, registeredSourceFolder]) => {
                const trackableStatus = getTrackableStatus(registeredSourceFolder);
                return trackableStatus === TrackableStatus.trackable;
            })
            .map(([folderRoot, _registeredSourceFolder]) => ({
                folderRoot,
            }));
    }

    // used for internal analytics
    public getSourceFoldersReportDetails(): SourceFolderReport {
        const sourceFolders: Array<PublicSourceFolderInfo> = this.listSourceFolders();
        let folderCountByType = mapValues(
            groupBy(sourceFolders, (folder) => PublicSourceFolderType[folder.type]),
            (folders) => folders.length
        );

        const repoRootCountByType = mapValues(
            groupBy(sourceFolders, (folder) => PublicSourceFolderType[folder.type]),
            (folders) =>
                uniq(folders.map((folder) => this.getRepoRootForFolderRoot(folder.folderRoot)))
                    .length
        );

        const isTracked = (
            sourceFolder: PublicSourceFolderInfo
        ): sourceFolder is TrackedSourceFolderInfo =>
            sourceFolder.type === PublicSourceFolderType.vscodeWorkspaceFolder ||
            sourceFolder.type === PublicSourceFolderType.externalFolder;

        const isNested = (
            sourceFolder: PublicSourceFolderInfo
        ): sourceFolder is NestedSourceFolderInfo =>
            sourceFolder.type === PublicSourceFolderType.nestedWorkspaceFolder ||
            sourceFolder.type === PublicSourceFolderType.nestedExternalFolder;

        const isUntracked = (
            sourceFolder: PublicSourceFolderInfo
        ): sourceFolder is UntrackedFolderInfo =>
            sourceFolder.type === PublicSourceFolderType.untrackedFolder;

        const trackedFileCount: number[] = sourceFolders
            .filter((folder) => isTracked(folder))
            .map((folder) => folder.trackedFileCount);

        const nestedFolderCount: number = sourceFolders.filter((folder) => isNested(folder)).length;

        const untrackedCountByReason = mapValues(
            groupBy(
                sourceFolders.filter((folder) => isUntracked(folder)),
                (folder) => folder.reason
            ),
            (folders) => folders.length
        );

        return {
            workspaceStorageUri: this._storageUriProvider.storageUri?.toString(),
            folderCountByType,
            repoRootCountByType,
            trackedFileCount,
            nestedFolderCount,
            untrackedCountByReason,
        };
    }

    // listSourceFolders returns an array of PublicSourceFolderInfo objects for all source folders
    // in the workspace.
    public listSourceFolders(): Array<PublicSourceFolderInfo> {
        if (this._syncingPermissionTracker.syncingPermissionDenied) {
            return [];
        }

        const syncingDisabled = this.syncingEnabledState === SyncingEnabledState.disabled;

        const result = new Array<PublicSourceFolderInfo>();
        for (const [folderRoot, registeredFolderInfo] of this._registeredSourceFolders) {
            if (registeredFolderInfo.containingFolderRoot !== undefined) {
                // Nested source folder
                const containingFolderType =
                    registeredFolderInfo.folderType === SourceFolderType.workspaceFolder
                        ? PublicSourceFolderType.nestedWorkspaceFolder
                        : PublicSourceFolderType.nestedExternalFolder;
                result.push({
                    type: containingFolderType,
                    name: registeredFolderInfo.folderName,
                    syncingEnabled: false,
                    folderRoot,
                    containingFolderRoot: registeredFolderInfo.containingFolderRoot,
                });
                continue;
            }
            if (registeredFolderInfo.isHomeDir) {
                result.push({
                    type: PublicSourceFolderType.untrackedFolder,
                    name: registeredFolderInfo.folderName,
                    syncingEnabled: false,
                    folderRoot,
                    reason: UntrackedFolderReason.homeDir,
                });
                continue;
            }
            if (
                registeredFolderInfo.folderQualification !== undefined &&
                !registeredFolderInfo.folderQualification.trackable
            ) {
                result.push({
                    type: PublicSourceFolderType.untrackedFolder,
                    name: registeredFolderInfo.folderName,
                    syncingEnabled: false,
                    folderRoot,
                    reason: UntrackedFolderReason.tooLarge,
                });
                continue;
            }
            if (registeredFolderInfo.syncingPermission === FolderSyncingPermission.denied) {
                result.push({
                    type: PublicSourceFolderType.untrackedFolder,
                    name: registeredFolderInfo.folderName,
                    syncingEnabled: false,
                    folderRoot,
                    reason: UntrackedFolderReason.permissionNotGranted,
                });
                continue;
            }

            const sourceFolderType =
                registeredFolderInfo.folderType === SourceFolderType.workspaceFolder
                    ? PublicSourceFolderType.vscodeWorkspaceFolder
                    : PublicSourceFolderType.externalFolder;

            const sourceFolder = this._trackedSourceFolders.get(folderRoot)?.sourceFolder;
            if (!sourceFolder?.initialEnumerationComplete) {
                // This item is a tracked source folder that is still being enumerated.
                const syncingEnabled =
                    !syncingDisabled &&
                    registeredFolderInfo.syncingPermission === FolderSyncingPermission.granted;
                result.push({
                    name: registeredFolderInfo.folderName,
                    type: sourceFolderType,
                    folderRoot,
                    syncingEnabled,
                    trackedFileCount: 0,
                    containsExcludedItems: false,
                    containsUnindexedItems: false,
                    enumerationState: PublicSourceFolderEnumerationState.inProgress,
                });
                continue;
            }

            // This item is a tracked source folder that has completed enumeration.
            const syncingEnabled =
                !syncingDisabled &&
                registeredFolderInfo.syncingPermission === FolderSyncingPermission.granted;
            let containsExcludedItems = false;
            let containsUnindexedItems = false;
            for (const [_p, fileType, accepted, indexed] of this._pathMap.pathsInFolder(
                sourceFolder.folderId
            )) {
                if (!accepted) {
                    containsExcludedItems = true;
                }
                if (fileType === FileType.file && accepted && !indexed) {
                    containsUnindexedItems = true;
                }
            }
            result.push({
                name: registeredFolderInfo.folderName,
                type: sourceFolderType,
                folderRoot,
                syncingEnabled,
                trackedFileCount: this._pathMap.trackedFileCount(sourceFolder.folderId),
                containsExcludedItems,
                containsUnindexedItems,
                enumerationState: PublicSourceFolderEnumerationState.complete,
            });
        }

        return result;
    }

    // listChildren returns an array of PublicSourceFolderItem objects that contains an object for
    // every child of the given subdir within the given source folder. The source folder must be
    // the absolute path of a tracked source folder, as returned by sourceFolderNames or
    // listSourceFolders. subdirRoot must be the relative path name of a subdirectory of the folder
    // root. To obtain the contents of folderRoot itself, pass the empty string as subdirRoot.
    public listChildren(folderRoot: string, subdirPath: string): Array<PublicSourceFolderItem> {
        if (this._syncingPermissionTracker.syncingPermissionDenied) {
            return [];
        }

        const folderInfo = this._trackedSourceFolders.get(folderRoot);
        const sourceFolder = folderInfo?.sourceFolder;
        if (sourceFolder === undefined) {
            throw new UnknownSourceFolderError();
        }
        if (!sourceFolder.initialEnumerationComplete) {
            throw new SourceFolderNotReadyError();
        }

        // subdirPath is relative to the folder root, but the paths enumerated by the path map are
        // relative to the repo root. Compute `pathPrefix` as the relative path from `repoRoot` to
        // `subdirPath` -- we are only interested in paths that begin with this prefix.
        const absSubdirPath = joinPath(folderRoot, subdirPath);
        const pathPrefix = relativePathName(sourceFolder.repoRoot, absSubdirPath);
        const items = new Map<
            string,
            { type: FileType; included: boolean; indexed: boolean; reason: string }
        >();
        const trackedFileCount = new Map<string, number>();
        const dirsWithExcludedItems = new Set<string>();
        const dirsWithUnindexedItems = new Set<string>();
        const folderId = sourceFolder.folderId;
        for (const [name, type, accepted, indexed, reason] of this._pathMap.pathsInFolder(
            folderId
        )) {
            const relPath = descendentPath(pathPrefix, name);
            if (relPath === undefined) {
                // This item is not within the subdir.
                continue;
            }

            const pathComponents = splitRelPath(relPath);
            if (pathComponents.length === 0 || pathComponents[0].length === 0) {
                // splitRelPath should never return an empty array. It can return a list of [""],
                // but this indicates that relPath is the subdir itself. Either way, we want to
                // skip this item.
                continue;
            }

            if (pathComponents.length === 1) {
                // This item is an immediate child of `subdir`. Add it to the result.
                items.set(pathComponents[0], {
                    type,
                    included: accepted,
                    indexed,
                    reason,
                });
            } else {
                // This item is in a subdirectory of `subdir`.
                const dirName = pathComponents[0];
                if (!accepted) {
                    // This item is not accepted. Indicate that `dirName` contains excluded items.
                    dirsWithExcludedItems.add(dirName);
                } else if (type === FileType.file) {
                    // This item is an accepted file. Increment the count of tracked files in
                    // `dirName`.
                    const count = trackedFileCount.get(dirName) ?? 0;
                    trackedFileCount.set(dirName, count + 1);
                    if (!indexed) {
                        // This file is not indexed. Indicate that `dirName` contains unindexed
                        // items.
                        dirsWithUnindexedItems.add(dirName);
                    }
                }
            }
        }

        return Array.from(items.entries()).map(([name, item]) => {
            const result = {
                name,
                folderRoot,
                relPath: joinPath(subdirPath, name),
                included: item.included,
                reason: item.reason,
            };
            if (item.type === FileType.file) {
                return {
                    ...result,
                    type: FileType.file,
                    indexed: item.indexed,
                };
            }
            if (item.type === FileType.directory) {
                return {
                    ...result,
                    type: FileType.directory,
                    trackedFileCount: trackedFileCount.get(name) ?? 0,
                    containsExcludedItems: dirsWithExcludedItems.has(name),
                    containsUnindexedItems: dirsWithUnindexedItems.has(name),
                };
            }
            return {
                ...result,
                type: FileType.other,
            };
        });
    }

    clearFileEdits() {
        this._fileEditManager?.clearAll({ clearLastKnown: false });
    }

    /**
     * The methods below should only be used in unit tests.
     */

    public unitTestOnlyGetRepoRoot(folderRoot: string): string | undefined {
        const folderInfo = this._trackedSourceFolders.get(folderRoot);
        if (folderInfo === undefined) {
            return undefined;
        }
        return folderInfo.sourceFolder?.repoRoot;
    }

    public unitTestOnlySourceFolderBacklog(folderRoot: string): number | undefined {
        const folderInfo = this._trackedSourceFolders.get(folderRoot);
        if (folderInfo === undefined) {
            return undefined;
        }
        const sourceFolder = folderInfo.sourceFolder;
        if (sourceFolder === undefined) {
            return undefined;
        }
        if (!sourceFolder.initialEnumerationComplete) {
            return undefined;
        }
        return sourceFolder.diskFileManager.itemsInFlight;
    }
}
