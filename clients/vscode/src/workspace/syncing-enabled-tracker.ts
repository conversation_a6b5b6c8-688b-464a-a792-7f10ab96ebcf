import * as vscode from "vscode";

import { DisposableService } from "../utils/disposable-service";
import { SingletonExecutor } from "../utils/singleton-executor";
import { SyncingEnabledState } from "./types";
import { WorkspaceManager } from "./workspace-manager";

export class SyncingEnabledTracker extends DisposableService {
    private _workspaceManager: WorkspaceManager | undefined = undefined;
    private _syncingEnabledChangedEmitter = new vscode.EventEmitter<SyncingEnabledState>();
    private _publishStateExecutor: SingletonExecutor;

    constructor() {
        super();
        this._publishStateExecutor = new SingletonExecutor(async (): Promise<void> => {
            await this._publishSyncingState();
        });
    }

    public get syncingEnabledState(): SyncingEnabledState {
        if (!this._workspaceManager) {
            return SyncingEnabledState.initializing;
        }
        return this._workspaceManager.syncingEnabledState;
    }

    public get onDidChangeSyncingEnabled(): vscode.Event<SyncingEnabledState> {
        return this._syncingEnabledChangedEmitter.event;
    }

    public enableSyncing(): void {
        if (this.syncingEnabledState === SyncingEnabledState.initializing) {
            throw new Error("Syncing enabled state not initialized");
        }
        if (this.syncingEnabledState === SyncingEnabledState.enabled) {
            return;
        }
        this._workspaceManager!.enableSyncing();
    }

    public disableSyncing(): void {
        if (this.syncingEnabledState === SyncingEnabledState.initializing) {
            throw new Error("Syncing enabled state not initialized");
        }
        if (this.syncingEnabledState === SyncingEnabledState.disabled) {
            return;
        }
        this._workspaceManager!.disableSyncing();
    }

    public set workspaceManager(workspaceManager: WorkspaceManager) {
        this._workspaceManager = workspaceManager;
        this.addDisposable(
            this._workspaceManager.onDidChangeSyncingState(
                (_newState: SyncingEnabledState) => void this._publishStateExecutor.kick()
            )
        );
    }

    private async _publishSyncingState(): Promise<void> {
        await vscode.commands.executeCommand(
            "setContext",
            "vscode-augment.syncingEnabledState",
            this.syncingEnabledState
        );

        this._syncingEnabledChangedEmitter.fire(this.syncingEnabledState);
    }
}
