import * as vscode from "vscode";

import { FocusAugmentPanel } from "../commands/focus-augment-panel";
import { StatusBarManager } from "../statusbar/status-bar-manager";
import * as statusbarStates from "../statusbar/status-bar-states";
import { DisposableService } from "../utils/disposable-service";
import { SyncingStatus, SyncingStatusEvent } from "./types";

// StatusBarSyncingReporter is a class that reports the workspace syncing status to the status bar.
export class StatusBarSyncingReporter extends DisposableService {
    static readonly syncingMessage =
        "Augment is synchronizing with your codebase to make better suggestions. The first time typically takes a few minutes.";
    private _syncingStatusBarDisposable: vscode.Disposable | undefined;
    private _syncingNotificationShown = false;

    constructor(
        private readonly _statusBar: StatusBarManager,
        _onDidChangeSyncingStatus: vscode.Event<SyncingStatusEvent>
    ) {
        super();
        this.addDisposables(
            _onDidChangeSyncingStatus((event: SyncingStatusEvent) =>
                this._handleSyncingStatusChanged(event)
            ),
            new vscode.Disposable(() => {
                this._syncingStatusBarDisposable?.dispose();
                this._syncingStatusBarDisposable = undefined;
            })
        );
    }

    private _handleSyncingStatusChanged(event: SyncingStatusEvent): void {
        switch (event.status) {
            case SyncingStatus.longRunning:
                if (!this._syncingNotificationShown) {
                    // Only focus on the augment panel during sync if the
                    // sync includes a newly tracked folder.
                    const newlyTracked = event.foldersProgress.find(
                        (p) => p.progress?.newlyTracked
                    );
                    if (newlyTracked) {
                        this._syncingNotificationShown = true;
                        void vscode.commands.executeCommand(FocusAugmentPanel.commandID);
                    }
                }
            // eslint-disable-next-line no-fallthrough
            case SyncingStatus.running:
                if (!this._syncingStatusBarDisposable) {
                    this._syncingStatusBarDisposable = this._statusBar.setState(
                        statusbarStates.syncing
                    );
                }
                break;
            case SyncingStatus.done:
                this._syncingNotificationShown = false;
                this._syncingStatusBarDisposable?.dispose();
                this._syncingStatusBarDisposable = undefined;
                break;
        }
    }
}
