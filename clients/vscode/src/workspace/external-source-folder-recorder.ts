import * as vscode from "vscode";

import { SingletonExecutor } from "../utils/singleton-executor";

type StoredExternalSourceFolderInfo = {
    folderRoot: string;
    folderName: string;
};

export interface ExternalSourceFolderRecorder {
    getFolders(): Map<string, string>;
    setFolders(folders: Map<string, string>): Promise<void>;
}

export class ExternalSourceFolderRecorderImpl implements ExternalSourceFolderRecorder {
    private static readonly storageKey = "external-source-folders:original";

    private _persistedFolders: Map<string, string>;
    private _toPersist: Map<string, string>;
    private readonly _persister: SingletonExecutor;

    constructor(private readonly _workspaceStorage: vscode.Memento) {
        this._persistedFolders = this._readFolders();
        this._toPersist = new Map(this._persistedFolders);
        this._persister = new SingletonExecutor(async () => await this._persistFolders());
    }

    // getFolders returns a map of persisted external source folders. Returned map is
    // Map<folderRoot, folderName>
    public getFolders(): Map<string, string> {
        return new Map(this._persistedFolders);
    }

    // setFolders sets the set of persisted folders to the given set of folders.
    public async setFolders(folders: Map<string, string>): Promise<void> {
        if (sourceFolderSetsMatch(this._persistedFolders, folders)) {
            return;
        }
        this._toPersist = new Map(folders);
        await this._persister.kick();
    }

    // _persistFolders persists the contents of this._toPersist and updates this._persistedFolders
    // to reflect the persisted set. It does nothing if the two sets are the same. Do not call this
    // method directly. Call it only via `this._persister.kick()`.
    private async _persistFolders() {
        if (sourceFolderSetsMatch(this._persistedFolders, this._toPersist)) {
            return;
        }

        const toPersist = new Map<string, string>(this._toPersist);
        const toPersistArray = new Array<StoredExternalSourceFolderInfo>();
        for (const [folderRoot, folderName] of toPersist) {
            toPersistArray.push({ folderRoot, folderName });
        }

        await this._workspaceStorage.update(
            ExternalSourceFolderRecorderImpl.storageKey,
            toPersistArray
        );
        this._persistedFolders = toPersist;
    }

    private _readFolders(): Map<string, string> {
        const storedFolderRoots = this._workspaceStorage.get<StoredExternalSourceFolderInfo[]>(
            ExternalSourceFolderRecorderImpl.storageKey
        );
        if (storedFolderRoots === undefined) {
            return new Map();
        }
        if (!Array.isArray(storedFolderRoots)) {
            return new Map();
        }
        const folders = new Map<string, string>();
        for (const item of storedFolderRoots) {
            if (item.folderRoot === undefined || typeof item.folderRoot !== "string") {
                continue;
            }
            if (item.folderName === undefined || typeof item.folderName !== "string") {
                continue;
            }
            folders.set(item.folderRoot, item.folderName);
        }
        return folders;
    }
}

// sourceFolderSetsMatch returns true if the two sets of source folders have the same contents.
function sourceFolderSetsMatch(set1: Map<string, string>, set2: Map<string, string>): boolean {
    if (set1.size !== set2.size) {
        return false;
    }
    for (const [key, value] of set1) {
        if (set2.get(key) !== value) {
            return false;
        }
    }
    return true;
}
