import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import * as vscode from "vscode";

import { type AugmentLogger, getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { statFileSync } from "../utils/fs-utils";
import { MetricsCollector } from "../utils/metrics";
import { PathAcceptance, RejectPath } from "../utils/path-acceptance";
import { FullPathFilter, PathIterator } from "../utils/path-iterator";
import { descendentPath } from "../utils/path-utils";
import { FileType } from "../utils/types";
import { uriToAbsPath, validateTrackablePath } from "../utils/uri";

export class InaccessiblePath extends RejectPath {
    constructor(public readonly reason: string) {
        super();
    }

    public format(): string {
        return this.reason;
    }
}

/**
 * PathNotifyEvent is a type for notifications that a path was either found or changed.
 */
export type PathNotifyEvent = {
    relPath: string;
    fileType: FileType;
    acceptance: PathAcceptance;
};

/**
 * PathNotifier is a class that enumerates the list of path names in a source folder and reports
 * changes to those paths over time.
 */
export class PathNotifier extends DisposableService {
    private readonly _pathFoundEmitter = new vscode.EventEmitter<PathNotifyEvent>();
    private readonly _pathCreatedEmitter = new vscode.EventEmitter<PathNotifyEvent>();
    private readonly _pathChangedEmitter = new vscode.EventEmitter<PathNotifyEvent>();
    private readonly _pathDeletedEmitter = new vscode.EventEmitter<string>();
    private readonly _logger: AugmentLogger;

    private _filesystemWatcherCreated = false;
    private _stopping = false;

    // _deletedPaths is a set of paths that were reported as deleted by the filesystem watcher
    // while a folder enumeration is in progress. We save these paths and issue events for them
    // after the enumeration is complete. We don't issue these events right away because doing so
    // could introduce a race if (for example) the watcher reports a path as deleted and then the
    // iterator, whose results can be slightly stale, reports it as existing. We create the set
    // before starting an enumeration and we drop it when the enumeration completes, after which
    // the next time we can issue all events right away.
    private _deletedPaths: Set<string> | undefined = undefined;

    // folderName: A name for the folder. This value is used strictly for logging only.
    // folderRoot: The absolute path to the root of the folder to be tracked.
    // repoRoot: The absolute path to the root of the repo that contains the folder. The repo root
    //     and the folder root may be the same, or the repo root may be a parent of the folder root.
    //     All relative path names reported by the PathNotifier will be relative to the repo root.
    // pathFilter: A PathFilter to be used for vetting path names, for example by ignore files.
    // workspaceFolder: The vscode workspace folder object to be used for creating a filesystem
    //     watcher. If this value is undefined then no filesystem watcher will be created.
    constructor(
        public readonly folderName: string,
        public readonly folderRoot: string,
        public readonly repoRoot: string,
        private readonly _pathFilter: FullPathFilter,
        private readonly _workspaceFolder?: vscode.WorkspaceFolder
    ) {
        super();
        this._logger = getLogger(`PathNotifier[${folderRoot}]`);
    }

    // onDidFindPath is an event that is fired when a path is found during enumeration of a source
    // folder.
    public get onDidFindPath(): vscode.Event<PathNotifyEvent> {
        return this._pathFoundEmitter.event;
    }

    // onDidCreatePath is an event that is fired when a path is created after the initial
    // enumeration of a source folder.
    public get onDidCreatePath(): vscode.Event<PathNotifyEvent> {
        return this._pathCreatedEmitter.event;
    }

    // onDidChangePath is an event that is fired when a path is changed after the initial
    // enumeration of a source folder.
    public get onDidChangePath(): vscode.Event<PathNotifyEvent> {
        return this._pathChangedEmitter.event;
    }

    // onDidDeletePath is an event that is fired when a path is deleted after the initial
    // enumeration of a source folder.
    public get onDidDeletePath(): vscode.Event<string> {
        return this._pathDeletedEmitter.event;
    }

    // DisposableService
    public dispose() {
        this._stopping = true;
        super.dispose();
    }

    // `enumeratePaths` enumerates the contents of the tracked source folder, issuing events for
    // the paths it finds. This method can be called more than once. If this object was created
    // with a workspaceFolder then the first call will create a filesystem watcher that will issue
    // events for changes to the folder's contents. The filesystem watcher will watch for filesystem
    // events until this object is disposed.
    public async enumeratePaths(): Promise<MetricsCollector | undefined> {
        if (this._stopping) {
            return;
        }
        this._deletedPaths = new Set();
        if (this._workspaceFolder !== undefined && !this._filesystemWatcherCreated) {
            this._createFilesystemWatcher(this._workspaceFolder);
            this._filesystemWatcherCreated = true;
        }

        const pathIterator = new PathIterator(
            this.folderName,
            vscode.Uri.file(this.folderRoot),
            vscode.Uri.file(this.repoRoot),
            this._pathFilter
        );

        for await (const [_fileUri, relPath, fileType, acceptance] of pathIterator) {
            if (this._stopping) {
                return;
            }
            this._pathFoundEmitter.fire({ relPath, fileType, acceptance });
        }
        if (this._stopping) {
            return;
        }

        // Issue deletion events for any paths that were deleted while the enumeration was in
        // progress. These paths have already been vetted by the path filter.
        const deletedPaths = this._deletedPaths;
        this._deletedPaths = undefined;
        for (const path of deletedPaths) {
            this._pathDeletedEmitter.fire(path);
        }

        return pathIterator.stats;
    }

    // _handlePathChanged responds to a notification from the filesystem watcher that
    // the given file has been created or updated.
    private _handlePathChanged(fileUri: vscode.Uri, newPath: boolean) {
        const relPath = this._getRelPath(fileUri);
        if (relPath === undefined) {
            return;
        }

        let fileType: FileType;
        let acceptance: PathAcceptance;
        try {
            const st = statFileSync(uriToAbsPath(fileUri));
            fileType = st.type;
            acceptance = this._pathFilter.getPathInfo(relPath, fileType);
        } catch (e: any) {
            fileType = FileType.other;
            acceptance = new InaccessiblePath(getErrmsg(e));
        }

        const operation = newPath ? "created" : "changed";
        this._logger.verbose(
            `${fileType} ${operation}: ${relPath}, acceptance = ${acceptance.format()}`
        );

        this._deletedPaths?.delete(relPath);
        if (newPath) {
            this._pathCreatedEmitter.fire({ relPath, fileType, acceptance });
        } else {
            this._pathChangedEmitter.fire({ relPath, fileType, acceptance });
        }
    }

    // _handlePathDeleted responds to a notification from the filesystem watcher that
    // the given path has been deleted.
    private _handlePathDeleted(fileUri: vscode.Uri) {
        const relPath = this._getRelPath(fileUri);
        if (relPath === undefined) {
            return;
        }

        this._logger.verbose(`Path deleted: ${relPath}`);

        if (this._deletedPaths !== undefined) {
            this._deletedPaths?.add(relPath);
        } else {
            this._pathDeletedEmitter.fire(relPath);
        }
    }

    // _getRelPath returns the relative path name of the given fileUri, or undefined if the path
    // is not contained in the repo.
    private _getRelPath(fileUri: vscode.Uri): string | undefined {
        if (this._stopping) {
            return undefined;
        }
        const absPath = validateTrackablePath(fileUri);
        if (absPath === undefined) {
            return undefined;
        }
        return descendentPath(this.repoRoot, absPath);
    }

    // creates a watcher that will call handlers for file system changes
    private _createFilesystemWatcher(workspaceFolder: vscode.WorkspaceFolder): void {
        const fileSystemWatcher = vscode.workspace.createFileSystemWatcher(
            new vscode.RelativePattern(workspaceFolder, "**/*")
        );
        this.addDisposables(
            fileSystemWatcher,
            fileSystemWatcher.onDidCreate((e: vscode.Uri) => this._handlePathChanged(e, true)),
            fileSystemWatcher.onDidChange((e: vscode.Uri) => this._handlePathChanged(e, false)),
            fileSystemWatcher.onDidDelete((e: vscode.Uri) => this._handlePathDeleted(e))
        );
    }
}
