import { TextDecoder } from "util";
import * as vscode from "vscode";

/* This module supports accessing the content of a text document for read
 * operations, without generating an onDidOpenTextDocument[scheme='file'] event.
 *
 * Why?
 * Subscribers to the onDidOpenTextDocument[scheme='file'] event are generally
 * interested in
 * a) files that the User of VSCode is opening in an editor or actively reading
 * the contents of
 * b) files that may undergo edits
 * Those subscribers may respond to the event with user-visible actions, such as
 * delivering the document to a language server, generating Diagnostics, recommending
 * extensions, etc.
 *
 * Extensions which want to avoid these subscriber interactions are advised to
 * use node.js file system APIs to read the file contents. This is unsatisfying.
 * 1. If a document is already open in VSCode, we may want to see its in-memory contents.
 * 2. We find the Postition/Offset/Range/Line related APIs convenient
 *
 * So, we define an interface that is a safe read-only subset of the TextDocument interface,
 * and provide a view over an already-opened TextDocument if it exists, or a view of the
 * on-disk file contents if it is not. To avoid triggering onDidOpenTextDocument[scheme='file'],
 * we open a custom document scheme if we need to read the file from disk.
 */

// Subset of the vscode.TextDocument interface that is possible to implement, and
// not likely to introduce bugs, when we may not open the real TextDocument but
// instead a read-only view of the document's contents.
export interface TextDocumentView {
    get eol(): vscode.EndOfLine;
    get lineCount(): number;
    getText(range?: vscode.Range): string;
    lineAt(lineOrPos: number | vscode.Position): vscode.TextLine;
    offsetAt(position: vscode.Position): number;
    positionAt(offset: number): vscode.Position;
    validateRange(range: vscode.Range): vscode.Range;
    validatePosition(position: vscode.Position): vscode.Position;
    getWordRangeAtPosition(position: vscode.Position, regex?: RegExp): vscode.Range | undefined;
}

class ViewWrapper implements TextDocumentView {
    constructor(private doc: vscode.TextDocument) {}
    get eol() {
        return this.doc.eol;
    }
    get lineCount() {
        return this.doc.lineCount;
    }
    getText(range?: vscode.Range) {
        return this.doc.getText(range);
    }
    offsetAt(position: vscode.Position) {
        return this.doc.offsetAt(position);
    }
    positionAt(offset: number) {
        return this.doc.positionAt(offset);
    }
    validateRange(range: vscode.Range) {
        return this.doc.validateRange(range);
    }
    validatePosition(position: vscode.Position) {
        return this.doc.validatePosition(position);
    }
    getWordRangeAtPosition(position: vscode.Position, regex?: RegExp) {
        return this.doc.getWordRangeAtPosition(position, regex);
    }
    lineAt(lineOrPos: number | vscode.Position) {
        if (typeof lineOrPos === "number") {
            return this.doc.lineAt(lineOrPos);
        } else {
            return this.doc.lineAt(lineOrPos);
        }
    }
}

export const backgroundFileScheme = "quiet-background-file";

// As this module is experimental, the extension may not always enable its
// functionality.  In that case, we need to know to just replace `view` calls
// with `open` ones.
let _providerRegistered = false;

export function enableViewTextDocument(): vscode.Disposable {
    let registration = vscode.workspace.registerTextDocumentContentProvider(backgroundFileScheme, {
        async provideTextDocumentContent(uri: vscode.Uri): Promise<string> {
            return vscode.workspace.fs.readFile(uri.with({ scheme: "file" })).then((bytes) => {
                return new TextDecoder("utf-8").decode(bytes);
            });
        },
    });
    _providerRegistered = true;
    return new vscode.Disposable(() => {
        _providerRegistered = false;
        registration.dispose();
    });
}

/* viewTextDocument
 *
 * Parameter: uriOrFileName
 *   The uri of the document to open, or a file path which will be converted to
 *   a uri with scheme 'file'.
 *
 * The following description applies if enableViewTextDocument has been called.
 * Otherwise, the call is simply a wrapper around openTextDocument. It is
 * intended that the only meaningful difference between having called
 * enableViewTextDocument and not is whether the
 * onDidOpenTextDocument[scheme='file'] event can be triggered by this call.
 *
 * === For uri-scheme other than 'file' ===
 * Argument is passed through to openTextDocument, which may trigger
 * onDidOpenTextDocument, but not for scheme='file'.
 *
 * === For uri-scheme of 'file' ===
 * The call will never trigger onDidOpenTextDocument[scheme='file'].
 * The call may trigger onDidOpenTextDocument[scheme=backgroundFileScheme], in
 * which case the file contents were read from disk on behalf of this call.
 * When the returned Promise settles with a result:
 * - If a document of requested uri is present in vscode.workspace.textDocuments,
 * then the Promise result is a view over that document. If that document
 * remains open, changes to it will be reflected in the returned view as time
 * passes.
 * - If a document of requested uri is not present in vscode.workspace.textDocuments,
 * then the Promise result is a view over a document with uri-scheme=backgroundFileScheme.
 * If the same file is later opened and modified, it will be a different document
 * within VSCode, and the changes will not be reflected in the returned view.
 */
export async function viewTextDocument(
    uriOrFileName: vscode.Uri | string
): Promise<TextDocumentView> {
    let uri: vscode.Uri;
    if (typeof uriOrFileName === "string") {
        uri = vscode.Uri.file(uriOrFileName);
    } else {
        uri = uriOrFileName;
    }

    if (!_providerRegistered || uri.scheme !== "file") {
        return vscode.workspace.openTextDocument(uri);
    }

    // VSCode openTextDocument considers two resources the same if the result of uri.toString() is the same
    // https://github.com/microsoft/vscode/blob/f5ac6168e08525535f3c4311a45c57f949891690/extensions/markdown-language-features/src/util/resourceMap.ts#L10
    let uriKey = uri.toString();
    for (const document of vscode.workspace.textDocuments) {
        if (document.uri.scheme === uri.scheme && document.uri.toString() === uriKey) {
            return new ViewWrapper(document);
        }
    }

    return new Promise((resolve, reject) => {
        // If the document is opened in 'file' scheme while we are
        // waiting, use that document, as it will reflect the most
        // up to date contents of the file at the time we return.
        let fileSub = vscode.workspace.onDidOpenTextDocument((doc) => {
            if (doc.uri.scheme === uri.scheme && doc.uri.toString() === uriKey) {
                fileSub.dispose();
                resolve(new ViewWrapper(doc));
            }
        });

        // See the provider registered above for backgroundFileScheme. Will
        // read the current file contents from the FS, and the result will be a
        // snapshot view of the file contents at some moment in time.
        vscode.workspace.openTextDocument(uri.with({ scheme: backgroundFileScheme })).then(
            (doc) => {
                fileSub.dispose();
                resolve(new ViewWrapper(doc));
            },
            (err) => {
                fileSub.dispose();
                reject(err);
            }
        );
    });
}
