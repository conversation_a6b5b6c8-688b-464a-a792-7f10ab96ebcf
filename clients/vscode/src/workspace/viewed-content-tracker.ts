import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { WorkspaceManager } from "./workspace-manager";

// Default maximum size for visible content in characters
const MAX_VISIBLE_CONTENT_SIZE = 5_000; // 5KB of text

export type ViewedContentInfo = {
    // relative path name of the file that was viewed
    relPathName: string;

    // blob name that corresponds to `pathName`
    blobName: string;

    // The visible content of the file (text from visible lines)
    visibleContent: string;

    // The start line number of the visible range
    lineStart: number;

    // The end line number of the visible range
    lineEnd: number;

    // The start character offset of the visible range
    charStart: number;

    // The end character offset of the visible range
    charEnd: number;

    // The timestamp of when the content was viewed
    timestamp: Date;
};

/**
 * Tracks the files the user viewed and their visible content based on VSCode editor events.
 *
 * VSCode fires events when visible content changes due to scrolling, file switching, or editor refocus.
 * Events do NOT fire on mouse clicks if the editor already has focus.
 * NOTE: this triggers a lot.
 *
 * ## Position-Based Pending/Acceptance Pattern
 *
 * The tracker uses position-based criteria to determine when to accept viewed content:
 *
 * 1. **Close Range Detection**: If a new event is within `closeRangeThreshold` lines of the pending event
 *    (both start and end positions), the new event is discarded completely to prevent minor scrolling
 *    from creating multiple similar entries.
 *
 * 2. **Discrete Jump Detection**: A discrete jump occurs when either:
 *    - The user switches to a different file, OR
 *    - Both start AND end positions change significantly (≥ `discreteJumpThreshold` lines)
 *    On discrete jumps, the pending event is evaluated for acceptance based on time criteria.
 *
 * 3. **Moderate Change Handling**: For changes that are neither close range nor discrete jumps,
 *    the pending event is simply updated with the new position information.
 *
 * 4. **Time-Based Acceptance**: During discrete jumps, accept pending events if:
 *    `minEventAgeMs ≤ (t2 - t1) ≤ maxEventAgeMs`
 *
 * ## Eviction Strategy
 *
 * Upon a newly accepted pending event, we first apply the following eviction rules to existing entries:
 * - **Overlap Deduplication**: New entries replace existing entries from the same file with overlapping line ranges
 * - **Per-File Entries**: Limited to `maxSameFileEntries` per file (oldest for that file removed first)
 * - **Total Entries**: Limited to `maxTrackedFiles` entries (oldest removed first)
 *
 * This information is used to improve completion quality by providing context about
 * what the user was looking at before triggering a completion.
 */
export class ViewedContentTracker extends DisposableService {
    // Array of accepted viewed content info, ordered by oldest to newest
    private _acceptedViewedContents: ViewedContentInfo[] = [];

    // The current pending event that hasn't been accepted or rejected yet
    private _pendingEvent?: ViewedContentInfo;

    // Maximum number of files to track
    private _maxTrackedFiles: number;

    // Maximum number of entries for the same file path
    private _maxSameFileEntries: number;

    // Threshold for considering positions "close" (lines)
    private _closeRangeThreshold: number;

    // Threshold for considering a change a "discrete jump" (lines)
    private _discreteJumpThreshold: number;

    // Minimum age before accepting a pending event (ms)
    private _minEventAgeMs: number;

    // Maximum age of a pending event before we reject it regardless of other criteria
    private _maxEventAgeMs: number;

    private _logger = getLogger("ViewedContentTracker");

    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super();

        // Get configuration from workspace manager
        const config = this._workspaceManager.getViewedContentConfig();
        this._maxTrackedFiles = config.maxTrackedFiles;
        this._maxSameFileEntries = config.maxSameFileEntries;
        this._closeRangeThreshold = config.closeRangeThreshold;
        this._discreteJumpThreshold = config.discreteJumpThreshold;
        this._minEventAgeMs = config.minEventAgeMs;
        this._maxEventAgeMs = config.maxEventAgeMs;

        // Listen to VSCode events that indicate visible content has changed:

        // 1. When user switches to a different file/editor
        this.addDisposable(
            vscode.window.onDidChangeActiveTextEditor((editor) => {
                if (editor) {
                    this._captureViewedContent(editor);
                }
            })
        );

        // 2. When user scrolls or visible ranges change in any editor
        // Note: This does NOT fire on mouse clicks if editor already has focus
        this.addDisposable(
            vscode.window.onDidChangeTextEditorVisibleRanges((event) => {
                this._captureViewedContent(event.textEditor);
            })
        );
    }

    /**
     * Handles a new VSCode editor event by capturing the current visible content.
     *
     * This method implements the position-based pending/acceptance pattern:
     * 1. **Capture New Event**: Extract visible content from the current editor state
     * 2. **Close Range Check**: If new event is within closeRangeThreshold of pending event, discard it
     * 3. **Discrete Jump Check**: If file changed or positions changed significantly, evaluate pending event based on time criteria
     * 4. **Moderate Change**: For other changes, update pending event with new position information
     */
    private _captureViewedContent(editor: vscode.TextEditor): void {
        const uri = editor.document.uri;
        const qualifiedPathName = this._workspaceManager.safeResolvePathName(uri);

        if (!qualifiedPathName) {
            return;
        }

        const blobName = this._workspaceManager.getBlobName(qualifiedPathName);
        if (!blobName) {
            return;
        }

        // Get the visible ranges and extract the content
        if (editor.visibleRanges.length === 0) {
            return;
        }

        // Take only the first range (the one with smallest lineStart)
        const firstRange = editor.visibleRanges.reduce((earliest, current) =>
            current.start.line < earliest.start.line ? current : earliest
        );

        const lineStart = firstRange.start.line;
        const lineEnd = firstRange.end.line;
        const charStart = editor.document.offsetAt(firstRange.start);
        const charEnd = editor.document.offsetAt(firstRange.end);

        // Extract the text from this range
        let visibleContent = editor.document.getText(firstRange);

        const relPath = qualifiedPathName.relPath;

        // Limit the size of visible content
        if (visibleContent.length > MAX_VISIBLE_CONTENT_SIZE) {
            this._logger.debug(
                `Truncating visible content for ${relPath} from ${visibleContent.length} to ${MAX_VISIBLE_CONTENT_SIZE} characters`
            );
            visibleContent = visibleContent.substring(0, MAX_VISIBLE_CONTENT_SIZE);
        }

        const currentTime = new Date();

        // Create the new event info from the current visible content
        this._logger.verbose(`New event captured for ${relPath}, lines ${lineStart}-${lineEnd}.`);
        const newEvent: ViewedContentInfo = {
            relPathName: relPath,
            blobName,
            visibleContent,
            lineStart,
            lineEnd,
            charStart,
            charEnd,
            timestamp: currentTime,
        };

        // If we have a pending event, apply position-based logic
        if (this._pendingEvent) {
            const timeSincePendingEvent =
                currentTime.getTime() - this._pendingEvent.timestamp.getTime();
            const isSameFile = this._pendingEvent.relPathName === relPath;

            // Check if this is a close range event (within threshold lines)
            const isCloseRange =
                isSameFile &&
                Math.abs(newEvent.lineStart - this._pendingEvent.lineStart) <=
                    this._closeRangeThreshold &&
                Math.abs(newEvent.lineEnd - this._pendingEvent.lineEnd) <=
                    this._closeRangeThreshold;

            if (isCloseRange) {
                // Discard the new event completely for close range changes
                this._logger.verbose(
                    `Discarding close range event: ${relPath}, lines ${lineStart}-${lineEnd} (within ${this._closeRangeThreshold} lines of pending).`
                );
                return;
            }

            // Check if this is a discrete jump
            const isDiscreteJump =
                !isSameFile ||
                (Math.abs(newEvent.lineStart - this._pendingEvent.lineStart) >=
                    this._discreteJumpThreshold &&
                    Math.abs(newEvent.lineEnd - this._pendingEvent.lineEnd) >=
                        this._discreteJumpThreshold);

            if (isDiscreteJump) {
                // Evaluate pending event for acceptance based on time criteria
                const shouldAcceptPending =
                    timeSincePendingEvent >= this._minEventAgeMs &&
                    timeSincePendingEvent <= this._maxEventAgeMs;

                if (shouldAcceptPending) {
                    this._logger.debug(
                        `Accepting pending event on discrete jump: ${this._pendingEvent.relPathName}, lines ${this._pendingEvent.lineStart}-${this._pendingEvent.lineEnd} (age: ${timeSincePendingEvent}ms).`
                    );
                    this._acceptPendingContent(this._pendingEvent);
                } else {
                    this._logger.debug(
                        `Rejecting pending event on discrete jump: ${this._pendingEvent.relPathName}, lines ${this._pendingEvent.lineStart}-${this._pendingEvent.lineEnd} (age: ${timeSincePendingEvent}ms).`
                    );
                }
            }
        }

        // Update the pending event with the new event info
        this._logger.verbose(
            `Updating pending event: ${relPath}, lines ${this._pendingEvent?.lineStart}-${this._pendingEvent?.lineEnd} -> ${lineStart}-${lineEnd}.`
        );
        this._pendingEvent = newEvent;
    }

    /**
     * Accepts a pending event and adds it to the viewed contents array.
     *
     * ## Eviction Strategy
     *
     * The method applies the following eviction rules in order:
     *
     * 1. **Overlap Deduplication**: Remove any existing entry from the same file with overlapping line ranges
     *    - This prevents duplicate content and keeps the most recent view of overlapping areas
     *
     * 2. **Per-File Limit**: If the file already has `maxSameFileEntries` entries, remove the oldest one
     *    - This prevents any single file from dominating the viewed content list
     *
     * 3. **Total Capacity Limit**: If total entries exceed `maxTrackedFiles`, remove the globally oldest entry
     *    - This maintains a fixed-size sliding window of recently viewed content
     *
     * The array maintains chronological order (oldest to newest) to support efficient eviction.
     */
    private _acceptPendingContent(pendingContent: ViewedContentInfo): void {
        const relPath = pendingContent.relPathName;

        // 1. Overlap Deduplication: Find the oldest overlapping entry, if any
        const oldestOverlappingIndex = this._acceptedViewedContents.findIndex(
            (entry) =>
                entry.relPathName === relPath &&
                pendingContent.lineStart <= entry.lineEnd &&
                pendingContent.lineEnd >= entry.lineStart
        );

        // If there's an overlapping entry, remove the oldest one
        if (oldestOverlappingIndex !== -1) {
            const entry = this._acceptedViewedContents[oldestOverlappingIndex];
            this._logger.debug(
                `Removing oldest overlapping entry {${oldestOverlappingIndex}}: ${entry.relPathName}, lines ${entry.lineStart}-${entry.lineEnd}.`
            );
            this._acceptedViewedContents.splice(oldestOverlappingIndex, 1);
        }

        // 2. Per-File Limit: Find the number of entries for this file path and the oldest index
        let sameFileCount = 0;
        let oldestIndex = -1;
        for (let i = 0; i < this._acceptedViewedContents.length; i++) {
            const entry = this._acceptedViewedContents[i];
            if (entry.relPathName === relPath) {
                sameFileCount++;
                if (oldestIndex === -1) {
                    oldestIndex = i;
                }
            }
        }

        // If we've reached the max entries for this file path, remove the oldest one
        if (sameFileCount >= this._maxSameFileEntries && oldestIndex !== -1) {
            let entry = this._acceptedViewedContents[oldestIndex];
            this._logger.debug(
                `Removing oldest same-file entry {${oldestIndex}}: ${entry.relPathName}, lines ${entry.lineStart}-${entry.lineEnd}.`
            );
            this._acceptedViewedContents.splice(oldestIndex, 1);
        }

        // 3. Total Capacity Limit: If we've exceeded the max size, remove the oldest entry
        if (this._acceptedViewedContents.length >= this._maxTrackedFiles) {
            let entry = this._acceptedViewedContents[0];
            this._logger.debug(
                `Removing oldest global entry: ${entry.relPathName}, lines ${entry.lineStart}-${entry.lineEnd}.`
            );
            this._acceptedViewedContents.shift(); // Remove the first (oldest) element
        }

        // Add the new entry to the end (most recent)
        this._acceptedViewedContents.push(pendingContent);
    }

    /**
     * Gets all accepted viewed content, ordered from most recent to least recent.
     * This does not include the current pending event that hasn't been accepted yet.
     */
    public getAllViewedContent(): ViewedContentInfo[] {
        // Create a copy of the array in reverse order (most recently viewed first)
        return [...this._acceptedViewedContents].reverse();
    }

    /**
     * Gets the pending event that hasn't been accepted or rejected yet.
     */
    public getPendingContent(): ViewedContentInfo | undefined {
        return this._pendingEvent;
    }
}
