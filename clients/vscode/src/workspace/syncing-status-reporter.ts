import * as vscode from "vscode";

import { FeatureFlagManager } from "../feature-flags";
import { DisposableService } from "../utils/disposable-service";
import { SourceFolderSyncingProgress, SyncingStatus, SyncingStatusEvent } from "./types";
import { WorkspaceManager } from "./workspace-manager";

// SyncingStatusReporter is a class that reports the syncing status of the workspace.
export class SyncingStatusReporter extends DisposableService {
    private readonly _newFolders = new Set<string>();
    private readonly _folderBacklogSize = new Map<string, number>();
    private readonly _folderTrackedFilesSize = new Map<string, number>();
    private readonly _syncingStatusEmitter = new vscode.EventEmitter<SyncingStatusEvent>();
    private _status: SyncingStatusEvent = {
        status: SyncingStatus.done,
        foldersProgress: [],
    };

    constructor(
        private readonly _featureFlagManager: FeatureFlagManager,
        private readonly _workspaceManager: WorkspaceManager
    ) {
        super();
        this.addDisposable(
            this._workspaceManager.onDidChangeSourceFolders(() =>
                this._handleSourceFoldersChanged()
            )
        );
        this.addDisposable(
            this._workspaceManager.onDidChangeSyncingProgress(
                (event: SourceFolderSyncingProgress) => this._handleSyncingProgressChanged(event)
            )
        );

        this._handleSourceFoldersChanged();
    }

    public get status(): SyncingStatusEvent {
        return this._status;
    }

    public get onDidChangeSyncingStatus(): vscode.Event<SyncingStatusEvent> {
        return this._syncingStatusEmitter.event;
    }

    private _handleSourceFoldersChanged() {
        this._newFolders.clear();
        this._folderBacklogSize.clear();
        this._folderTrackedFilesSize.clear();
        this._workspaceManager
            .getSyncingProgress()
            .forEach((progress: SourceFolderSyncingProgress) => this._updateFolderState(progress));
        this._reportSyncingStatus();
    }

    private _handleSyncingProgressChanged(event: SourceFolderSyncingProgress) {
        this._updateFolderState(event);
        this._reportSyncingStatus();
    }

    private _updateFolderState(event: SourceFolderSyncingProgress): void {
        if (event.progress === undefined) {
            // An undefined progress means that we haven't finished walking the source folder, so
            // we don't yet know how large its backlog is. Wait until we do to start reporting it.
            return;
        }
        if (event.progress.newlyTracked) {
            this._newFolders.add(event.folderRoot);
        } else {
            this._newFolders.delete(event.folderRoot);
        }
        this._folderBacklogSize.set(event.folderRoot, event.progress.backlogSize);
        this._folderTrackedFilesSize.set(event.folderRoot, event.progress.trackedFiles);
    }

    private _reportSyncingStatus(): void {
        let largeNewFolder = false;
        let totalBacklog = 0;
        let trackedFiles = 0;
        const flags = this._featureFlagManager.currentFlags;
        this._folderBacklogSize.forEach((backlogSize, _folderRoot) => {
            if (backlogSize >= flags.bigSyncThreshold) {
                largeNewFolder = true;
            }
            totalBacklog += backlogSize;
        });
        this._folderTrackedFilesSize.forEach((trackedFilesSize, _folderRoot) => {
            trackedFiles += trackedFilesSize;
        });

        let status = SyncingStatus.done;
        if (largeNewFolder) {
            status = SyncingStatus.longRunning;
        } else if (totalBacklog > flags.smallSyncThreshold || totalBacklog / trackedFiles >= 0.1) {
            status = SyncingStatus.running;
        }
        const prevStatus = this._status.status;
        this._status = {
            status: status,
            foldersProgress: this._workspaceManager.getSyncingProgress(),
            prevStatus: prevStatus,
        };
        this._syncingStatusEmitter.fire(this._status);
    }
}
