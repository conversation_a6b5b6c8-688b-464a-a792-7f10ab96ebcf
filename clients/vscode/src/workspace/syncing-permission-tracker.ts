import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { SingletonExecutor } from "../utils/singleton-executor";
import { FolderSyncingPermission } from "./workspace-types";

enum SyncingPermissionState {
    granted,
    denied,
}

enum SyncingPermissionType {
    implicit = "implicit",
    explicit = "explicit",
}

type PermittedFolder = {
    sourceFolder: string;
    type: SyncingPermissionType;
    timestamp: number;
};

// SyncingPermissionGranted indicates that the user has granted permission to sync the given
// set of folders in the workspace.
type SyncingPermissionGranted = {
    state: SyncingPermissionState.granted;
    permittedFolders: Array<PermittedFolder>;
};

// SyncingPermissionDenied indicates that the user has denied permission to sync the workspace.
type SyncingPermissionDenied = {
    state: SyncingPermissionState.denied;
    timestamp: number;
};

type SyncingPermission = SyncingPermissionGranted | SyncingPermissionDenied;

export interface SyncingPermissionTracker {
    dispose(): void;
    syncingPermissionDenied: boolean;
    getFolderSyncingPermission(sourceFolder: string): FolderSyncingPermission;
    setDefaultPermissions(sourceFolders: Array<string>): void;
    setPermittedFolders(sourceFolders: Array<string>): void;
    addPermittedFolder(sourceFolder: string): void;
    addImplicitlyPermittedFolder(sourceFolder: string): void;
    dropPermission(sourceFolders: Array<string>): void;
    dropStaleFolders(sourceFolders: Array<string>): void;
    denyPermission(): void;
    persistCurrentPermission(): Promise<void>;
}

export class SyncingPermissionTrackerImpl
    extends DisposableService
    implements SyncingPermissionTracker
{
    static readonly storageKey = "syncingPermission.2024102300";

    private _currentPermission: SyncingPermission | undefined;
    private _persister: SingletonExecutor;
    private readonly _logger = getLogger("SyncingPermissionTracker");

    constructor(private readonly _workspaceStorage: vscode.Memento) {
        super();
        this._currentPermission = this._getStoredPermission();
        this._persister = new SingletonExecutor(async () => await this._persistCurrentPermission());
        this._logPermission("Initial syncing permission", this._currentPermission);
    }

    // syncingPermissionDenied returns true if the user has denied permission to sync the workspace.
    public get syncingPermissionDenied(): boolean {
        return this._currentPermission?.state === SyncingPermissionState.denied;
    }

    // getFolderSyncingPermission indicates whether we have permission to sync the given source
    // folder.
    public getFolderSyncingPermission(sourceFolder: string): FolderSyncingPermission {
        const permission = this._currentPermission;
        if (permission === undefined) {
            this._logger.info(
                `Permission to sync folder ${sourceFolder} unknown: ` +
                    "no permission information recorded"
            );
            return FolderSyncingPermission.unknown;
        }
        if (permission.state === SyncingPermissionState.denied) {
            const timeString = new Date(permission.timestamp).toLocaleString();
            this._logger.info(`Permission to sync folder ${sourceFolder} denied at ${timeString}`);
            return FolderSyncingPermission.denied;
        }
        for (const permittedFolder of permission.permittedFolders) {
            if (sourceFolder === permittedFolder.sourceFolder) {
                const timeString = new Date(permittedFolder.timestamp).toLocaleString();
                this._logger.info(
                    `Permission to sync folder ${sourceFolder} granted at ${timeString}; ` +
                        `type = ${permittedFolder.type}`
                );
                return FolderSyncingPermission.granted;
            }
        }
        this._logger.info(
            `Permission to sync folder ${sourceFolder} unknown: ` +
                "no current permission for folder"
        );
        return FolderSyncingPermission.unknown;
    }

    // setDefaultPermissions is used for customers with workspaces that Augment had been syncing
    // before syncing permission was added to the product. To avoid unnecessarily asking these
    // customers for syncing permission, we grant implicit permission to sync any already-synced
    // source folders. These permissions only take effect if we have no recorded permission, hence
    // this method will only have any effect the very first time it runs for a given workspace.
    public setDefaultPermissions(sourceFolders: Array<string>): void {
        if (this._currentPermission !== undefined || sourceFolders.length === 0) {
            return;
        }
        const timestamp = Date.now();
        this._setSyncingPermission({
            state: SyncingPermissionState.granted,
            permittedFolders: sourceFolders.map((sourceFolder) => ({
                sourceFolder,
                type: SyncingPermissionType.implicit,
                timestamp,
            })),
        });
    }

    // setPermittedFolders records that permission has been explicitly granted to sync the given
    // source folders. This set replaces any existing set of permitted source folders.
    public setPermittedFolders(sourceFolders: Array<string>): void {
        const timestamp = Date.now();
        this._setSyncingPermission({
            state: SyncingPermissionState.granted,
            permittedFolders: sourceFolders.map((sourceFolder) => ({
                sourceFolder,
                type: SyncingPermissionType.explicit,
                timestamp,
            })),
        });
    }

    public addPermittedFolder(sourceFolder: string): void {
        let currentPermission = this._currentPermission;
        if (
            currentPermission === undefined ||
            currentPermission.state === SyncingPermissionState.denied
        ) {
            currentPermission = {
                state: SyncingPermissionState.granted,
                permittedFolders: [],
            };
        }

        const newFolder = {
            sourceFolder,
            type: SyncingPermissionType.explicit,
            timestamp: Date.now(),
        };

        this._setSyncingPermission({
            ...currentPermission,
            permittedFolders: [...currentPermission.permittedFolders, newFolder],
        });
    }

    public addImplicitlyPermittedFolder(sourceFolder: string): void {
        let currentPermission = this._currentPermission;
        if (currentPermission?.state === SyncingPermissionState.denied) {
            // Implicit permission doesn't trump explicit denial.
            return;
        }

        if (currentPermission === undefined) {
            currentPermission = {
                state: SyncingPermissionState.granted,
                permittedFolders: [],
            };
        } else {
            const found = currentPermission.permittedFolders.find((folder) => {
                return folder.sourceFolder === sourceFolder;
            });
            if (found !== undefined) {
                // We only record implicit permission if we don't already permission recorded for
                // the folder.
                return;
            }
        }

        const newFolder = {
            sourceFolder,
            type: SyncingPermissionType.implicit,
            timestamp: Date.now(),
        };

        this._setSyncingPermission({
            ...currentPermission,
            permittedFolders: [...currentPermission.permittedFolders, newFolder],
        });
    }

    public dropPermission(sourceFolders: Array<string>): void {
        if (sourceFolders.length === 0) {
            return;
        }
        if (
            this._currentPermission === undefined ||
            this._currentPermission.state === SyncingPermissionState.denied
        ) {
            return;
        }
        const remainingFolders = this._currentPermission.permittedFolders.filter((folder) => {
            return !sourceFolders.includes(folder.sourceFolder);
        });
        this._setSyncingPermission({
            ...this._currentPermission,
            permittedFolders: remainingFolders,
        });
    }

    // dropStaleFolders drops permission to sync any source folders that are not found in the given
    // list.
    public dropStaleFolders(sourceFolders: Array<string>): void {
        if (
            this._currentPermission === undefined ||
            this._currentPermission.state === SyncingPermissionState.denied
        ) {
            return;
        }
        const remainingFolders = this._currentPermission.permittedFolders.filter((folder) => {
            return sourceFolders.includes(folder.sourceFolder);
        });
        this._setSyncingPermission({
            ...this._currentPermission,
            permittedFolders: remainingFolders,
        });
    }

    // denyPermission records the fact that the user has denied permission to sync the workspace.
    public denyPermission(): void {
        void this._setSyncingPermission({
            state: SyncingPermissionState.denied,
            timestamp: Date.now(),
        });
    }

    // _getStoredPermission retrieves the persisted syncing permission, if any.`
    private _getStoredPermission(): SyncingPermission | undefined {
        return this._workspaceStorage.get<SyncingPermission>(
            SyncingPermissionTrackerImpl.storageKey
        );
    }

    // _setSyncingPermission sets the current syncing permission and arranges to persist it. It
    // does not wait for the persist to complete.
    private _setSyncingPermission(permission: SyncingPermission): void {
        this._currentPermission = permission;
        this._logPermission("Updating syncing permission", permission);
        void this._persister.kick();
    }

    // persistCurrentPermission returns a promise that resolves when the current syncing permission
    // has been persisted.
    public async persistCurrentPermission(): Promise<void> {
        await this._persister.kick();
    }

    // _persistCurrentPermission persists the current syncing permission. Do not call this method
    // directly. Call it only via `this._persister`.
    private async _persistCurrentPermission(): Promise<void> {
        await this._workspaceStorage.update(
            SyncingPermissionTrackerImpl.storageKey,
            this._currentPermission
        );
    }

    private _logPermission(context: string, permission: SyncingPermission | undefined): void {
        if (permission === undefined) {
            this._logger.info(`${context}: undefined`);
            return;
        }
        if (permission.state === SyncingPermissionState.denied) {
            const timeString = new Date(permission.timestamp).toLocaleString();
            this._logger.info(
                `${context}: syncing permission denied for workspace at ${timeString}`
            );
            return;
        }
        const folderString =
            permission.permittedFolders.length === 0
                ? "none"
                : permission.permittedFolders
                      .map((permittedFolder) => {
                          const timeString = new Date(permittedFolder.timestamp).toLocaleString();
                          return `\n    ${permittedFolder.sourceFolder} (${permittedFolder.type}) at ${timeString}`;
                      })
                      .join("");
        this._logger.info(
            `${context}: syncing permission granted for workspace. Folders:${folderString}`
        );
    }
}
