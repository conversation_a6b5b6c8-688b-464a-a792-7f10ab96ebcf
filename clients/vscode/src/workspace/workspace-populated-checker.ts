import * as vscode from "vscode";

import { ActionsModel } from "../main-panel/action-cards/actions-model";
import { DisposableService } from "../utils/disposable-service";
import { promiseFromEvent } from "../utils/promise-utils";
import { SystemStateName, SystemStatus } from "../utils/types";
import { WorkspaceManager } from "./workspace-manager";

/**
 * This service is responsible for checking if a workspace is selected in VS Code
 * and updating the `workspacePopulated` state accordingly.
 *
 * This only needs to be done at start up. If the user changes the workspace
 * after that, VS Code will reload the window.
 */
export class Workspace<PERSON>opulated<PERSON>hecker extends DisposableService {
    constructor(
        private readonly actionsModel: ActionsModel,
        private readonly workspaceManager: WorkspaceManager
    ) {
        super();
        this.setInitializing();
        this.addDisposable(workspaceManager);
        this.checkWorkspaceSelected();
        void this.checkWorkspacePopulated();
    }

    public checkWorkspaceSelected(): void {
        const workspaceSelected = !!vscode.workspace.workspaceFolders?.length;
        if (workspaceSelected) {
            this._setWorkspaceSelected();
        } else {
            this._setWorkspaceNotSelected();
        }
    }

    public async checkWorkspacePopulated(): Promise<void> {
        await this.workspaceManager.awaitInitialFoldersEnumerated();
        if (this._anyFilesExist()) {
            this._setWorkspacePopulated();
            return;
        }
        this._setWorkspaceEmpty();
        while (!this._anyFilesExist()) {
            await promiseFromEvent(this.workspaceManager.onDidChangeSyncingProgress);
        }
        this._setWorkspacePopulated();
    }

    private _anyFilesExist(): boolean {
        const syncingProgress = this.workspaceManager.getSyncingProgress();
        if (
            syncingProgress.some(
                (item) =>
                    item.progress?.trackedFiles !== undefined && item.progress.trackedFiles > 0
            )
        ) {
            return true;
        }
        return false;
    }

    private setInitializing() {
        this.actionsModel.setSystemStateStatus(
            SystemStateName.workspacePopulated,
            SystemStatus.initializing
        );
        this.actionsModel.setSystemStateStatus(
            SystemStateName.workspaceSelected,
            SystemStatus.initializing
        );
    }

    private _setWorkspaceEmpty() {
        this.actionsModel.setSystemStateStatus(
            SystemStateName.workspacePopulated,
            SystemStatus.incomplete
        );
    }

    private _setWorkspacePopulated() {
        this.actionsModel.setSystemStateStatus(
            SystemStateName.workspacePopulated,
            SystemStatus.complete
        );
    }

    private _setWorkspaceSelected() {
        this.actionsModel.setSystemStateStatus(
            SystemStateName.workspaceSelected,
            SystemStatus.complete
        );
    }

    private _setWorkspaceNotSelected() {
        this.actionsModel.setSystemStateStatus(
            SystemStateName.workspaceSelected,
            SystemStatus.incomplete
        );
    }
}
