import * as vscode from "vscode";

import { StatusBarManager } from "../statusbar/status-bar-manager";
import * as statusbarStates from "../statusbar/status-bar-states";
import { DisposableService } from "../utils/disposable-service";
import { SyncingEnabledTracker } from "./syncing-enabled-tracker";
import { SyncingEnabledState } from "./types";

export class StatusBarSyncingStateListener extends DisposableService {
    private _syncingDisabledDisp: vscode.Disposable | undefined = undefined;

    constructor(
        private readonly _statusBarManager: StatusBarManager,
        private readonly _syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super();
        this.addDisposable(
            this._syncingEnabledTracker.onDidChangeSyncingEnabled(
                (syncingState: SyncingEnabledState) =>
                    this._updateSyncingState(syncingState === SyncingEnabledState.enabled)
            )
        );

        const syncingEnabledState = this._syncingEnabledTracker.syncingEnabledState;
        if (syncingEnabledState !== SyncingEnabledState.initializing) {
            this._updateSyncingState(syncingEnabledState === SyncingEnabledState.enabled);
        }
    }

    private _updateSyncingState(enabled: boolean) {
        if (enabled) {
            this._syncingDisabledDisp?.dispose();
            this._syncingDisabledDisp = undefined;
        } else if (!this._syncingDisabledDisp) {
            this._syncingDisabledDisp = this._statusBarManager.setState(
                statusbarStates.syncingDisabled
            );
        }
    }
}
