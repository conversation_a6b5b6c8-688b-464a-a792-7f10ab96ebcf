import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import * as vscode from "vscode";

import { FileType } from "../utils/types";

type FolderTextDocumentChangedEvent = {
    folderId: number;
    relPath: string;
    event: vscode.TextDocumentChangeEvent;
};

type FolderNotebookDocumentChangedEvent = {
    folderId: number;
    relPath: string;
    event: vscode.NotebookDocumentChangeEvent;
};

type FolderTextDocumentOpenedEvent = {
    folderId: number;
    relPath: string;
    document: vscode.TextDocument;
};

type FolderNotebookDocumentOpenedEvent = {
    folderId: number;
    relPath: string;
    document: vscode.NotebookDocument;
};

type FolderTextDocumentClosedEvent = {
    folderId: number;
    relPath: string;
    document: vscode.TextDocument;
};

type FolderNotebookDocumentClosedEvent = {
    folderId: number;
    relPath: string;
    document: vscode.NotebookDocument;
};

type FolderFileDeletedEvent = {
    folderId: number;
    relPath: string;
    qualifiedPathName: IQualifiedPathName;
};

type FolderFileWillRenameEvent = {
    folderId: number;
    oldRelPath: string;
    newRelPath: string;
    type: FileType;
};

export {
    FolderTextDocumentChangedEvent,
    FolderNotebookDocumentChangedEvent,
    FolderTextDocumentOpenedEvent,
    FolderNotebookDocumentOpenedEvent,
    FolderTextDocumentClosedEvent,
    FolderNotebookDocumentClosedEvent,
    FolderFileDeletedEvent,
    FolderFileWillRenameEvent,
};
