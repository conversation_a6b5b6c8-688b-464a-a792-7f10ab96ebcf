import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { RingBuffer } from "@augment-internal/sidecar-libs/src/ring-buffer";
import { retryWithBackoff } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { cloneDeep } from "lodash";
import * as vscode from "vscode";

import { APIServer, FindMissingResult, MemorizeResult } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { CompletionServer } from "../completion-server";
import { type AugmentLogger, getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { logArray } from "../utils/logging";
import {
    CELL_SEPARATOR,
    getConcatenatedCodeCellText,
    getNotebookCodeText,
    getNotebookDocument as utilsGetNotebookDocument,
} from "../utils/notebook";
import { SequenceGenerator } from "../utils/sequence-generator";
import { OrderedWorkQueue, PeriodicKicker } from "../utils/work-queue";
import { ChangeTracker } from "./change-tracker";
import { DocumentType } from "./document-type";
import { PathMap } from "./path-map";

const chunksPerChangeset = 6;
const maxChangeSets = 6;
export const maxTotalChunks = chunksPerChangeset * maxChangeSets;
const verifyBatchSize = 1000;
const verifyRetryWaitMs = 2000;
const longRetryWaitMs = 60 * 1000;
const maxBufferedChanges = 200;
const maxUploadMs = 30 * 1000;
const maxVerifyMs = 30 * 1000;
const verifyPatienceMs = 60 * 1000;

/**
 * Chunk is a class that represents a recent change to the tracked document indicated by pathName.
 *
 * Some chunks (those with uploaded=false) are for changes that have not yet been uploaded, and
 * hence represent diffs on top of the given blobName. origStart and origLength give the location
 * of the before-image of the diff. `text` contains the after-image of the diff.
 *
 * Other chunks (those with uploaded=true) are for changes that have already been uploaded, and
 * therefore simply indicate that the range between origStart and origStart + origLength of the
 * given blobName was changed recently. The contents of that range in the given blob should exactly
 * match `text`.
 *
 * The seq field provides a chronological ordering of chunks in a RecencySummary. The absolute
 * value is not meaningful, just the relative value compared to other chunks.
 *
 * The expectedBlobName field stores the expected blob name of this file after applying all
 * non-uploaded recent chunks.  It can be used to verify that recent chunks were applied correctly
 * and that the correct file was reconstructed.  Note that it is only set if debug features are
 * enabled; otherwise it is the empty string.
 */
export type RawChunk = {
    seq: number;
    timestamp: Date;
    uploaded: boolean;
    folderId: number;
    pathName: string;
    blobName: string;
    text: string;
    origStart: number;
    origLength: number;
    expectedBlobName: string;
};

/*
 * ChunkInfo is a summary of a chunk. The fields of ChunkInfo are the same as the corresponding
 * fields of Chunk, with the exception that ChunkInfo.blobName can be undefined. An undefined blob
 * name means that there is no indexed blob for this path. This can happen if the document has
 * never been uploaded or if the last attempted upload failed.
 */
export type ChunkInfo = {
    seq: number;
    uploaded: boolean;
    folderId: number;
    pathName: string;
    blobName: string | undefined;
};

/*
 * RecencySummary is a summary of recent changes across the collection of tracked documents.
 */
export type RecencySummary = {
    // folderMap is a per-folder map of pathName to blobName. The outer map is keyed by folderId.
    // The inner maps are keyed by pathName. Each inner map contains an entry for every tracked
    // pathName in its folder for which there is an uploaded blob. Hence, blobs that have not yet
    // been uploaded, and blobs whose last upload failed, are not included.
    // * folderMap: Map<folderId, Map<pathName, blobName>>
    folderMap: Map<number, Map<string, string>>;

    // recentChunks is an array of recent modifications. It contains un-uploaded (uploaded=false)
    // chunks first, in chronological order (descending seq), followed by uploaded (uploaded=true)
    // changes in chronological order (descending seq).
    recentChunks: Array<RawChunk>;
};

export type DocumentRange = {
    folderId: number;
    relPath: string;
    beginOffset: number;
    endOffset: number;
};

export type BlobRange = {
    blobName: string;
    beginOffset: number;
    endOffset: number;
};

type ChangeSet = {
    initialSeq: number;
    changeTracker: ChangeTracker;
};

// Information about an upload that is in flight.
type InProgressUpload = {
    /**
     * blobName: The name of the uploading blob. This name will become the new
     *     DocumentState.uploadedBlobName when the upload completes (successfully).
     * uploadSeq: The sequence number of the last change included in the upload.
     * savedChangeset: The changeset that should be used to produce RecencySummary's while this
     *     upload is in progress. `changeTracker` is based on `blobName`. It is updated as edits
     *     occur so it always contains the most recent changes.
     * changesSinceUpload: A ChangeSet containing all changes that have occurred since this upload
     *     began. This will become the new DocumentState.changesSinceUpload when the upload
     *     completes (successfully).
     */
    blobName: string;
    uploadSeq: number;
    savedChangeset: { changeTracker: ChangeTracker; blobName: string } | undefined;
    changesSinceUpload: ChangeTracker;
};

// DocumentState tracks the state of an open Document. It is an abstract class with two subclasses,
// one for TextDocuments and one for NotebookDocuments.
abstract class DocumentState {
    /**
     * uploadedBlobName: blob name corresponding to the most recently uploaded in-memory state
     *     of this document. It is undefined if we have not yet completed an upload of the
     *     document or when an upload of the document is in progress. In the latter case,
     *     inProgressUpload will contain the most recently uploaded blob name, if we have one.
     * uploadedSeq: sequence number of the last successful upload of the document. It is undefined
     *     if we have not yet completed an upload of the document.
     * recentChangesets: list of sets of recent changes. Each item is a set of changes starting
     *     at a different point in time. If there is no upload in progress then the changes are
     *     tracked relative to uploadedBlobName. If an upload is in progress then the changes are
     *     tracked relative to inProgressUpload.blobName.
     * changesSinceUpload: changes that have been made since the beginning of the last successful
     *     upload. It is undefined if an upload takes such a long time that it grows "too large".
     * uploadRequested: true if we have requested an upload of the document and have not started
     *     one yet. This flag does not indicate whether an upload is actually in progress. Use the
     *     existence of inProgressUpload for that purpose.
     * inProgressUpload: information about an in-progress upload, if any.
     * _embargoed: true if there is a reason why we cannot track the document, for example if it
     *     is too large or if we received a permanent error while trying to upload it.
     */
    public uploadedBlobName: string | undefined;
    public uploadedSeq: number | undefined;
    public readonly recentChangesets: RingBuffer<ChangeSet>;
    public changesSinceUpload: ChangeTracker | undefined;
    public uploadRequested = false;
    public inProgressUpload: InProgressUpload | undefined;
    private _embargoed = false;

    constructor(
        public readonly folderId: number,
        public readonly pathName: string,
        public key: number,
        public appliedSeq: number
    ) {
        this.recentChangesets = new RingBuffer<ChangeSet>(maxChangeSets);
        this.addChangeset(appliedSeq);
        this.changesSinceUpload = new ChangeTracker();
    }

    public abstract get documentType(): DocumentType;
    public abstract getText(): string;

    // invalidateUploadState drops information about the last upload of this document in response
    // to us being told it is not known to the server.
    public invalidateUploadState(): void {
        this.uploadedBlobName = undefined;
        this.uploadedSeq = undefined;
    }

    private _clear(): void {
        this.uploadedBlobName = undefined;
        this.uploadedSeq = undefined;
        this.recentChangesets.clear();
        this.changesSinceUpload = undefined;
        this.uploadRequested = false;
        this.inProgressUpload = undefined;
    }

    public embargo(): void {
        this._clear();
        this._embargoed = true;
    }

    public get embargoed(): boolean {
        return this._embargoed;
    }

    public get uploadInProgress(): boolean {
        return this.inProgressUpload !== undefined;
    }

    public getBlobName(): string | undefined {
        const recentChanges = this.recentChanges(false);
        return recentChanges?.blobName;
    }

    // longestHistory returns a <ChangeTracker, blobName> pair containing the longest tracked
    // history of this document and its corresponding blob name. It returns undefined if there is
    // no tracked history, either because no changes have been made yet or because it has been too
    // long since the last successful upload. The latter reason can be overridden by passing
    // `includeStale=true`. The return value indicates that the blobName could be undefined, but
    // this method will only return an undefined blobName if includeStale is true.
    public longestHistory(
        includeStale: boolean
    ): { changeTracker: ChangeTracker; blobName: string | undefined } | undefined {
        if (this.uploadedSeq === undefined) {
            // The document has never been uploaded.
            return undefined;
        }
        const changeset = this.recentChangesets.at(0);
        if (changeset === undefined) {
            return undefined;
        }
        if (includeStale) {
            return { changeTracker: changeset.changeTracker, blobName: this.uploadedBlobName };
        }
        if (this.uploadedBlobName === undefined) {
            // The last upload attempt for this document failed.
            return undefined;
        }
        if (changeset.initialSeq > this.uploadedSeq) {
            // This document's last successful upload was so long ago that even the oldest recent
            // changeset was created after it.
            return undefined;
        }
        return { changeTracker: changeset.changeTracker, blobName: this.uploadedBlobName };
    }

    // recentChanges returns the changeTracker and blob name that should be used to produce a
    // RecencySummary.
    public recentChanges(
        includeStale: boolean
    ): { changeTracker: ChangeTracker; blobName: string | undefined } | undefined {
        if (this.inProgressUpload !== undefined) {
            return this.inProgressUpload.savedChangeset;
        }
        return this.longestHistory(includeStale);
    }

    // applyAll applies the given changes to all of the recent changesets.
    public applyAll(
        seq: number,
        timestamp: Date,
        start: number,
        charsToDelete: number,
        charsToInsert: number
    ) {
        for (const changeset of this.recentChangesets) {
            const changeTracker = changeset.changeTracker;
            changeTracker.apply(seq, timestamp, start, charsToDelete, charsToInsert);
        }
        this.appliedSeq = seq;
    }

    // advanceAll advances all of the change trackers of the recent changesets. Advancing a
    // tracker sets its changes to be relative to the current state of this document instead
    // of whatever earlier document state they were relative to.
    public advanceAll(): void {
        for (const changeset of this.recentChangesets) {
            changeset.changeTracker.advance();
        }
    }

    // addChangeset adds a new changeset with the given initialSeq to the recent changesets.
    public addChangeset(initialSeq: number) {
        this.recentChangesets.addItem({ initialSeq, changeTracker: new ChangeTracker() });
    }

    // purgeChangesets removes all changesets whose unique content (the content it has that is
    // not present in any newer changeset) is older than the given sequence number. It returns the
    // number of changesets that were removed.
    public purgeChangesets(maxSeq: number): number {
        /**
         * Starting with the oldest changeset, find the upper bound of the sequence range that
         * is unique to that changeset. If that sequence number is less than maxSeq then none of
         * the changeset's unique content is of interest any more, and the changeset can be removed.
         */
        let purged = 0;
        while (!this.recentChangesets.empty) {
            const purgeSeq = this.recentChangesets.at(1)?.initialSeq ?? this.appliedSeq;
            if (purgeSeq >= maxSeq) {
                break;
            }
            this.recentChangesets.shiftLeft(1);
            purged++;
        }

        return purged;
    }
}

// A subclass of DocumentState for text documents
class TextDocumentState extends DocumentState {
    constructor(
        folderId: number,
        pathName: string,
        key: number,
        public readonly document: vscode.TextDocument,
        appliedSeq: number
    ) {
        super(folderId, pathName, key, appliedSeq);
    }

    public get documentType(): DocumentType {
        return DocumentType.text;
    }

    public getText(): string {
        return this.document.getText();
    }
}

// A subclass of DocumentState for notebook documents
class NotebookDocumentState extends DocumentState {
    constructor(
        folderId: number,
        pathName: string,
        key: number,
        public readonly document: vscode.NotebookDocument,
        appliedSeq: number
    ) {
        super(folderId, pathName, key, appliedSeq);
    }

    public get documentType(): DocumentType {
        return DocumentType.notebook;
    }

    public getText(): string {
        return getNotebookCodeText(this.document);
    }
}

function isNotebookDocument(
    document: vscode.TextDocument | vscode.NotebookDocument
): document is vscode.NotebookDocument {
    return (document as vscode.NotebookDocument).getCells !== undefined;
}

// getNotebookDocument returns the notebook document for the given document if it is either
// a notebookDocument or a text document that is part of a notebook. Otherwise it returns
// undefined.
function getNotebookDocument(
    document: vscode.TextDocument | vscode.NotebookDocument
): vscode.NotebookDocument | undefined {
    if (isNotebookDocument(document)) {
        return document;
    }

    // this will only be defined if the document is part of a notebook
    return utilsGetNotebookDocument(document);
}

/**
 * VerifyItem is a class containing the metadata for a path name whose blob name is being
 * verified.
 * key: The value of DocumentState.key when the verify operation began. If DocumentState.key
 *     if ever observed to have a different value, that means that the verify operation has been
 *     cancelled.
 * startTime: The time when this verify operation started.
 */
type VerifyItem = {
    folderId: number;
    pathName: string;
    key: number;
    startTime: number;
};

export class OpenFileManager extends DisposableService {
    private readonly _trackedFolders = new Map<number, Map<string, DocumentState>>();

    // _uploadQueue is an ordered workqueue of blobs waiting to be uploaded. The items in the
    // queue are tuples of: [folderId, pathName, key], where key is the DocumentState.key at the
    // time the upload was enqueued.
    private readonly _uploadQueue: OrderedWorkQueue<[number, string, number]>;

    // _verifyWaiters and _longWaiters are work queues of blobs waiting for verification. Blobs
    // are placed into one of these queues after they have been uploaded or after a previous
    // verification reported them as not indexed. _verifyWaiters is for blobs that have been
    // waiting less than verifyPatienceMs. _longWaiters is for blobs that have been waiting longer
    // than that. They wait here and then get re-queued for verification.
    // * key: path name
    // * value: metadata for the verify operation
    private _verifyWaiters: OrderedWorkQueue<VerifyItem>;
    private _longWaiters: OrderedWorkQueue<VerifyItem>;
    private _verifyWaitersKicker: PeriodicKicker;
    private _longWaitersKicker: PeriodicKicker;

    // _verifyQueue is a workqueue of blobs ready to be verified.
    // * key: path name
    // * value: metadata for the verify operation
    private readonly _verifyQueue: OrderedWorkQueue<VerifyItem>;

    // _verifyBatch is the batch of to-verify blobs that we are currently building.
    // * key: blob name
    // * value: array of VerifyItems for that blob name (there can be more)
    private _verifyBatch = new Map<string, Array<VerifyItem>>();

    // _prevUpdatedPathName is the folderId + path name of the document that was most recently
    // updated, if any.
    private _prevUpdatedDocument: DocumentState | undefined;

    private readonly _logger: AugmentLogger;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _completionServer: CompletionServer,
        private readonly _configListener: AugmentConfigListener,
        private readonly _blobNameCalculator: BlobNameCalculator,
        private readonly _pathMap: PathMap,
        private readonly _sequenceGenerator: SequenceGenerator
    ) {
        super();

        this._logger = getLogger("OpenFileManager");

        this._uploadQueue = new OrderedWorkQueue<[number, string, number]>(this._upload.bind(this));
        this.addDisposable(this._uploadQueue);

        this._verifyWaiters = new OrderedWorkQueue<VerifyItem>(this._enqueueForVerify.bind(this));
        this.addDisposable(this._verifyWaiters);

        this._verifyWaitersKicker = new PeriodicKicker(this._verifyWaiters, verifyRetryWaitMs);
        this.addDisposable(this._verifyWaitersKicker);

        this._longWaiters = new OrderedWorkQueue<VerifyItem>(this._enqueueForVerify.bind(this));
        this.addDisposable(this._longWaiters);

        this._longWaitersKicker = new PeriodicKicker(this._longWaiters, longRetryWaitMs);
        this.addDisposable(this._longWaitersKicker);

        this._verifyQueue = new OrderedWorkQueue<VerifyItem>(this._verify.bind(this));
        this.addDisposable(this._verifyQueue);
    }

    public openSourceFolder(folderId: number): vscode.Disposable {
        if (this._trackedFolders.has(folderId)) {
            throw new Error(`Source folder ${folderId} is already open`);
        }
        this._trackedFolders.set(folderId, new Map<string, DocumentState>());

        this._logger.info(`Opened source folder ${folderId}`);
        return new vscode.Disposable(() => {
            this._closeSourceFolder(folderId);
        });
    }

    private _closeSourceFolder(folderId: number): void {
        this._trackedFolders.delete(folderId);
        this._logger.info(`Closed source folder ${folderId}`);
    }

    // startTracking starts tracking the given document.
    public startTracking(
        folderId: number,
        pathName: string,
        document: vscode.TextDocument | vscode.NotebookDocument
    ): void {
        this._trackDocument(folderId, pathName, document);
    }

    // stopTracking stops tracking the given document. If the document type is specified, this
    // method will only stop tracking the document if it is of the given type.
    //
    // The document type helps ensure that this method doesn't mistakenly stop tracking the wrong
    // document. For example, if a user removes one of the cells of a notebook, vscode emits a
    // "text document closed" notification containing the uri of the notebook (the cells of a
    // notebook are text documents, and they all have the same uri as the notebook itself). When
    // this happens, we don't want to stop tracking the entire notebook. In fact, we don't want to
    // stop tracking anything at all--we will update our bookkeeping for the notebook via a separate
    // notification from vscode. By passing a document type of "text document", the caller ensures
    // that this method will only stop tracking the path name if it refers to a text document. In
    // this example, the method will find a notebook, not a text document, with the given pathname,
    // so it will correctly ignore the request.
    //
    // The document type is optional because there are situations where the caller wants to close
    // the document with a given pathname, regardless of its type. For example, if an ignore file
    // is updated to exclude the path of a currently tracked file, we want to stop tracking that
    // file no matter what its type is. For this case, the caller can omit the document type.
    public stopTracking(folderId: number, pathName: string, documentType?: DocumentType): void {
        const folder = this._getFolder(folderId);
        if (folder === undefined) {
            return;
        }
        const documentState = folder.get(pathName);
        if (documentState === undefined) {
            return;
        }
        if (documentType !== undefined && documentState.documentType !== documentType) {
            return;
        }
        folder.delete(pathName);
        if (this._prevUpdatedDocument === documentState) {
            this._prevUpdatedDocument = undefined;
        }

        this._logger.verbose(`stop tracking ${folderId}:${pathName}`);
    }

    public isTracked(folderId: number, pathName: string): boolean {
        return this._getDocument(folderId, pathName) !== undefined;
    }

    // getTrackedPaths returns an array of the tracked path names in the given folder.
    public getTrackedPaths(folderId: number): Array<string> {
        const folder = this._getFolder(folderId);
        if (folder === undefined) {
            return new Array<string>();
        }
        return Array.from(folder.keys());
    }

    // loseFocus indicates that the user has shifted their focus away from any document tracked
    // by this blob manager.
    public loseFocus(): void {
        this._setFocus(undefined);
    }

    private get _chunkSize(): number {
        return this._completionServer.completionParams.chunkSize;
    }

    private _getFolder(folderId: number): Map<string, DocumentState> | undefined {
        return this._trackedFolders.get(folderId);
    }

    private _getDocument(
        folder_: number | Map<string, DocumentState>,
        pathName: string,
        key?: number
    ): DocumentState | undefined {
        const folder = typeof folder_ === "number" ? this._getFolder(folder_) : folder_;
        if (folder === undefined) {
            return undefined;
        }
        const documentState = folder.get(pathName);
        if (documentState === undefined) {
            return undefined;
        }
        if (key !== undefined && documentState.key !== key) {
            return undefined;
        }
        return documentState;
    }

    // getBlobName returns the blob name for the given path name, if there is one.
    public getBlobName(folderId: number, pathName: string): string | undefined {
        const documentState = this._getDocument(folderId, pathName);
        return documentState?.getBlobName();
    }

    // translateRange translates a character range in the given document to a character range
    // in an uploaded blob.
    public translateRange(documentRange: DocumentRange): BlobRange | undefined {
        const documentState = this._getDocument(documentRange.folderId, documentRange.relPath);
        if (documentState === undefined) {
            return undefined;
        }
        if (documentState.uploadedBlobName === undefined) {
            return undefined;
        }
        const changesSinceUpload = documentState.changesSinceUpload;
        if (changesSinceUpload === undefined) {
            // This document has accumulated too many changes since its last successful upload.
            // We will start tracking them again after the next successful upload.
            return undefined;
        }
        const result = changesSinceUpload.translate(
            documentRange.beginOffset,
            documentRange.endOffset - documentRange.beginOffset
        );
        return {
            blobName: documentState.uploadedBlobName,
            beginOffset: result[0],
            endOffset: result[0] + result[1],
        };
    }

    // `notifyMissingBlob` indicates that the given blob name is not known to the server and
    // requests that it be re-uploaded if it is still the current blob for the given path.
    public notifyMissingBlob(folderId: number, relPath: string, blobName: string): boolean {
        const documentState = this._getDocument(folderId, relPath);
        if (documentState === undefined || documentState.uploadedBlobName !== blobName) {
            return false;
        }
        documentState.invalidateUploadState();
        this._tryEnqueueUpload(folderId, relPath, "blob name reported missing", documentState);
        return true;
    }

    // getRecencySummary returns a summary of the most recent changes to the documents tracked by
    // this blob manager.
    public getRecencySummary(chunkSize: number): RecencySummary {
        const folderMap = new Map<number, Map<string, string>>();
        const chunks = new Array<RawChunk>();
        for (const [folderId, folder] of this._trackedFolders) {
            const pathMap = new Map<string, string>();
            folderMap.set(folderId, pathMap);
            for (const [pathName, documentState] of folder) {
                if (documentState.embargoed) {
                    continue;
                }
                if (documentState.uploadedSeq === undefined) {
                    // The document has not been uploaded yet.
                    continue;
                }
                const history = documentState.recentChanges(false);
                if (history === undefined || history.blobName === undefined) {
                    continue;
                }

                pathMap.set(pathName, history.blobName);

                // Form chunks from the document's oldest set of changes and add them to the
                // collection of chunks.
                const documentText = documentState.getText();
                const edits = history.changeTracker.getChunks(chunkSize, documentText.length);
                if (edits.length === 0) {
                    continue;
                }
                let newBlobName = this._blobNameCalculator.calculateNoThrow(pathName, documentText);
                for (const edit of edits) {
                    chunks.push({
                        seq: edit.seq,
                        timestamp: edit.timestamp,
                        uploaded: edit.seq <= documentState.uploadedSeq,
                        folderId,
                        pathName,
                        blobName: history.blobName,
                        text: documentText.slice(edit.start, edit.end),
                        origStart: edit.origStart,
                        origLength: edit.origLength,
                        expectedBlobName: newBlobName,
                    });
                }
            }
        }

        chunks.sort(OpenFileManager._compareChunks);
        return {
            folderMap,
            recentChunks: chunks,
        };
    }

    // getRecentChunkInfo returns information about the most recent changes to the documents
    // tracked by this blob manager. `includeStale` indicates whether it include chunks from
    // documents that don't have an uploaded blob name because their last upload failed.
    public getRecentChunkInfo(chunkSize: number, includeStale = false): Array<ChunkInfo> {
        const chunkInfo = new Array<ChunkInfo>();
        for (const [folderId, folder] of this._trackedFolders) {
            for (const [pathName, documentState] of folder) {
                if (documentState.embargoed) {
                    continue;
                }
                if (documentState.uploadedSeq === undefined) {
                    // The document has not been uploaded yet.
                    continue;
                }
                const history = documentState.recentChanges(includeStale);
                if (history === undefined) {
                    continue;
                }
                const edits = history.changeTracker.getChunks(
                    chunkSize,
                    documentState.getText().length
                );
                if (edits.length === 0) {
                    continue;
                }
                for (const edit of edits) {
                    chunkInfo.push({
                        seq: edit.seq,
                        uploaded: edit.seq <= documentState.uploadedSeq,
                        folderId,
                        pathName,
                        blobName: history.blobName,
                    });
                }
            }
        }
        chunkInfo.sort(OpenFileManager._compareChunks);
        return chunkInfo;
    }

    // _compareChunks is the comparator function for sorting chunks for a recency summary. It sorts
    // the chunks so the non-uploaded chunks come first from newest to oldest, followed by
    // uploaded chunks from newest to oldest.
    private static _compareChunks(
        this: void,
        a: RawChunk | ChunkInfo,
        b: RawChunk | ChunkInfo
    ): number {
        if (a.uploaded === b.uploaded) {
            return b.seq - a.seq;
        }
        return a.uploaded ? 1 : -1;
    }

    // applyTextDocumentChange applies a TextDocumentChangeEvent to the given document state.
    public applyTextDocumentChange(
        folderId: number,
        pathName: string,
        event: vscode.TextDocumentChangeEvent
    ): void {
        const documentState = this._getDocument(folderId, pathName);
        if (documentState === undefined) {
            // This document is not yet tracked. Begin tracking it, but don't apply the given
            // event, because that change is already present in the document.
            this._trackDocument(folderId, pathName, event.document);
            return;
        }

        if (!this._prepareForUpdate(documentState)) {
            // We are not tracking changes for this document (eg. embargo).
            return;
        }

        if (event.contentChanges.length === 0) {
            return;
        }

        const changes: Array<[number, number, number]> = event.contentChanges.map((change) => [
            change.rangeOffset,
            change.rangeLength,
            change.text.length,
        ]);

        this._applyChangedRanges(folderId, pathName, documentState, changes);
    }

    // applyNotebookChange applies the notebook-specific changes (e.g., adding or moving cells)
    // from the given event to the given document state. Changes to the contents of the notebook
    // cells are still handled by separate TextDocumentChangeEvents.
    public applyNotebookChange(
        folderId: number,
        pathName: string,
        event: vscode.NotebookDocumentChangeEvent
    ) {
        const documentState = this._getDocument(folderId, pathName);
        if (documentState === undefined) {
            // This document is not yet tracked. Begin tracking it, but don't apply the given
            // event, because that change is already present in the document.
            this._trackDocument(folderId, pathName, event.notebook);
            return;
        }

        if (!this._prepareForUpdate(documentState)) {
            // We are not tracking changes for this document (eg. embargo).
            return;
        }

        if (event.contentChanges.length === 0) {
            return;
        }

        let cells = event.notebook.getCells().slice();
        const modifiedRanges = new Array<[number, number, number]>();

        // We ignore cell changes since those should either be tracked by the corresponding
        // TextDocumentChangeEvent event or be execution events, about which we don't care.

        // We iterate backwards since the current notebook represents the end state.
        // We construct the set of cells and maintain it as we do so.
        event.contentChanges
            .slice()
            .reverse()
            .forEach((change) => {
                // While it is not specified in the API, experimentally we see that each
                // `contentChanges` contains only contiguous cell events.  If multiple non-
                // contiguous cells are added/removed at the same time, they will be split into
                // multiple `contentChanges` events.  It thus should be safe to treat `range` as
                // the range of all cells changed in this change.

                // Since we are working backwards, we reverse the operations.
                // For example, if a cell was added, our state already contains it,
                // so we delete it from our current view to get the state before it was added.
                cells.splice(change.range.start, change.addedCells.length);
                cells.splice(change.range.start, 0, ...change.removedCells);
                // At this point cells represents the state before this change.

                const changedFirstCodeCell = cells.slice(0, change.range.start).every((cell) => {
                    return cell.kind === vscode.NotebookCellKind.Markup;
                });
                const changedLastCodeCell = cells.slice(change.range.end).every((cell) => {
                    return cell.kind === vscode.NotebookCellKind.Markup;
                });
                let prefixLength = getConcatenatedCodeCellText(
                    cells.slice(0, change.range.start)
                ).length;
                if (prefixLength > 0 && !changedFirstCodeCell && !changedLastCodeCell) {
                    prefixLength += CELL_SEPARATOR.length;
                }

                const separatorCount =
                    !changedFirstCodeCell || !changedLastCodeCell ? CELL_SEPARATOR.length : 0;
                let insertedLength = getConcatenatedCodeCellText(change.addedCells).length;
                if (insertedLength > 0) {
                    insertedLength += separatorCount;
                }
                let deletedLength = getConcatenatedCodeCellText(change.removedCells).length;
                if (deletedLength > 0) {
                    deletedLength += separatorCount;
                }

                if (insertedLength > 0 || deletedLength > 0) {
                    modifiedRanges.push([prefixLength, deletedLength, insertedLength]);
                }
            });

        // We reverse the order of the changes since worked backwards.
        modifiedRanges.reverse();
        this._applyChangedRanges(folderId, pathName, documentState, modifiedRanges);
    }

    private _setFocus(documentState: DocumentState | undefined): void {
        if (
            this._prevUpdatedDocument !== undefined &&
            documentState !== this._prevUpdatedDocument
        ) {
            this._tryEnqueueUpload(
                this._prevUpdatedDocument.folderId,
                this._prevUpdatedDocument.pathName,
                "document lost focus"
            );

            // While we are switching from one document to another, take the opportunity to toss
            // any unneeded changesets (across all folders and documents).
            this._purgeUnneededChangesets();
        }
        this._prevUpdatedDocument = documentState;
    }

    private _trackDocument(
        folderId: number,
        pathName: string,
        document: vscode.TextDocument | vscode.NotebookDocument
    ): void {
        const folder = this._getFolder(folderId);
        if (folder === undefined) {
            throw new Error(`Source folder ${folderId} is not open`);
        }

        let documentState = this._getDocument(folder, pathName);
        this._setFocus(documentState);
        if (documentState !== undefined) {
            return;
        }

        // Add a new DocumentState to the map.
        const seqInfo = this._sequenceGenerator.next();
        const notebook = getNotebookDocument(document);
        if (notebook === undefined) {
            const textDocument = document as vscode.TextDocument;
            documentState = new TextDocumentState(
                folderId,
                pathName,
                seqInfo.seq,
                textDocument,
                seqInfo.seq
            );
        } else {
            documentState = new NotebookDocumentState(
                folderId,
                pathName,
                seqInfo.seq,
                notebook,
                seqInfo.seq
            );
        }
        folder.set(pathName, documentState);

        // Compute the initial blob name.
        const documentText = documentState.getText();
        const blobName = this._blobNameCalculator.calculate(pathName, documentText);
        if (blobName === undefined) {
            this._embargo(folderId, pathName, documentState, "blob name calculation failed");
            return;
        }

        // The PathMap may already have the blob name that we calculated (DiskFileManager could
        // have added it). If not, enqueue the document for upload.
        const verifyPathName = this._pathMap.getAnyPathName(blobName);
        if (verifyPathName === undefined) {
            // We don't have a blob name that matches the current in-memory state. Queue the
            // document for upload.
            this._tryEnqueueUpload(
                folderId,
                pathName,
                "new document has no blob name",
                documentState
            );
        } else {
            // The blob name we computed is already uploaded. Presumably it is for the same
            // path name. (We don't bother checking, as a mismatch would signify a hash collision,
            // which is extremely unlikely and we have no way to handle it.)
            documentState.uploadedBlobName = blobName;
            documentState.uploadedSeq = documentState.appliedSeq;
        }

        this._logger.verbose(`start tracking ${folderId}:${pathName}`);
    }

    // _prepareForUpdate tries to prepare the given document to receive an update. It returns
    // true if the document is able to be updated.
    private _prepareForUpdate(documentState: DocumentState): boolean {
        this._setFocus(documentState);
        return !documentState.embargoed;
    }

    // _applyChangedRanges applies an array of changes to the given document state.
    private _applyChangedRanges(
        folderId: number,
        pathName: string,
        documentState: DocumentState,
        ranges: Array<[number, number, number]>
    ): void {
        const seqInfo = this._sequenceGenerator.next();
        const seq = seqInfo.seq;
        const timestamp = seqInfo.timestamp;

        if (documentState.recentChangesets.empty) {
            documentState.addChangeset(seq);
            this._logger.verbose(
                `apply: new changeset for ${folderId}:${pathName}; ` +
                    `total = ${documentState.recentChangesets.length}`
            );
        }

        const inProgressUpload = documentState.inProgressUpload;

        for (const range of ranges) {
            const [start, origLength, newLength] = range;
            if (inProgressUpload !== undefined) {
                if (inProgressUpload.savedChangeset !== undefined) {
                    inProgressUpload.savedChangeset.changeTracker.apply(
                        seq,
                        timestamp,
                        start,
                        origLength,
                        newLength
                    );
                }
                inProgressUpload.changesSinceUpload.apply(
                    seq,
                    timestamp,
                    start,
                    origLength,
                    newLength
                );
            }
            documentState.applyAll(seq, timestamp, start, origLength, newLength);
            documentState.changesSinceUpload?.apply(seq, timestamp, start, origLength, newLength);
        }
        documentState.appliedSeq = seq;

        // If too many changes have accumulated since the current upload began, cancel it.
        if (inProgressUpload !== undefined) {
            let cancelUpload = inProgressUpload.changesSinceUpload.length >= maxBufferedChanges;
            if (!cancelUpload) {
                const chunks = inProgressUpload.changesSinceUpload.countChunks(this._chunkSize);
                cancelUpload = chunks >= maxTotalChunks;
            }
            if (cancelUpload) {
                this._cancelInProgressUpload(folderId, pathName, documentState);
            }
        }

        if (documentState.changesSinceUpload !== undefined) {
            const chunks = documentState.changesSinceUpload.countChunks(this._chunkSize);
            if (chunks > 1) {
                // The document has more than one chunk since the last upload. Begin a new upload.
                this._tryEnqueueUpload(
                    folderId,
                    pathName,
                    "multiple non-uploaded chunks",
                    documentState
                );
            }
            if (chunks >= maxTotalChunks) {
                // The document has accumulated too many chunks since its last successful upload.
                // Stop tracking changes to the document. We will start tracking them again after
                // the next successful upload.
                this._logger.verbose(
                    `apply: no longer tracking non-uploaded changes for ${folderId}:${pathName}`
                );
                documentState.changesSinceUpload = undefined;
            }
        }

        // Decide whether to add a new changeset (which may evict an old one).
        const newestChangeset = documentState.recentChangesets.at(-1)!;
        const chunks = newestChangeset.changeTracker.countChunks(this._chunkSize);
        if (chunks >= chunksPerChangeset) {
            documentState.addChangeset(seq);
            this._logger.verbose(
                `apply: new changeset for ${folderId}:${pathName}; ` +
                    `chunks = ${chunks}; ` +
                    `total = ${documentState.recentChangesets.length}`
            );
        }
    }

    private _cancelInProgressUpload(
        folderId: number,
        pathName: string,
        documentState: DocumentState
    ): void {
        this._logger.verbose(`cancel in-progress upload: ${folderId}:${pathName}`);
        documentState.inProgressUpload = undefined;

        // Changing the document's key will cause the in-flight upload to be terminated the
        // next time it comes up for air.
        documentState.key = this._sequenceGenerator.next().seq;
    }

    // _validateInProgressUpload returns the DocumentState and inProgressUpload for the given
    // pathName if there is an upload in progress for it with the given key. Otherwise it returns
    // undefined.
    private _validateInProgressUpload(
        folderId: number,
        pathName: string,
        key: number
    ): [DocumentState, InProgressUpload] | undefined {
        const documentState = this._getDocument(folderId, pathName, key);
        if (documentState === undefined || documentState.inProgressUpload === undefined) {
            return undefined;
        }
        return [documentState, documentState.inProgressUpload];
    }

    // _tryEnqueueUpload queues the given document for upload if upload is needed and it is
    // not already in the queue.
    private _tryEnqueueUpload(
        folderId: number,
        pathName: string,
        reason: string,
        documentState_?: DocumentState
    ): void {
        const documentState = documentState_ ?? this._getDocument(folderId, pathName);
        if (documentState === undefined) {
            return;
        }

        if (documentState.uploadRequested) {
            // This document is already queued for upload.
            return;
        }

        if (documentState.appliedSeq === documentState.uploadedSeq) {
            // There are no changes since the last upload.
            return;
        }

        if (documentState.appliedSeq === documentState.inProgressUpload?.uploadSeq) {
            // All changes are already covered by an in-progress upload.
            return;
        }

        this._logger.verbose(`upload request: ${folderId}:${pathName}; reason = ${reason}`);
        documentState.uploadRequested = true;

        // If the document has an in-flight upload, don't enqueue it again. When the upload
        // completes, it will check this flag and re-enqueue the blob if it is set.
        if (documentState.uploadInProgress) {
            this._logger.verbose(
                "upload request delayed: " +
                    `upload for ${folderId}:${pathName} already in progress`
            );
        } else {
            this._enqueueUpload(folderId, pathName, documentState.key);
        }
    }

    // _retryUpload tries to start a new upload for the given path after a previous upload was
    // cancelled.
    private _retryUpload(folderId: number, pathName: string): void {
        this._logger.verbose(`retry upload; ${folderId}:${pathName}`);
        const documentState = this._getDocument(folderId, pathName);
        if (documentState === undefined) {
            this._logger.verbose(
                `retry upload: document is no longer tracked; ${folderId}:${pathName}`
            );
            return;
        }
        if (documentState.inProgressUpload !== undefined) {
            this._logger.verbose(
                `retry upload: upload already in progress; ${folderId}:${pathName}`
            );
            return;
        }

        // Enqueue the path for upload if it is not already in the queue.
        documentState.uploadRequested = true;
        this._enqueueUpload(folderId, pathName, documentState.key);
    }

    // _enqueueUpload adds the given path to the upload queue.
    private _enqueueUpload(folderId: number, pathName: string, key: number): void {
        if (this._uploadQueue.insert([folderId, pathName, key])) {
            this._logger.verbose(`enqueue upload: ${folderId}:${pathName}`);
            void this._uploadQueue.kick();
        }
    }

    // _upload begins an upload for the given path and key.
    private async _upload(toUpload: [number, string, number] | undefined): Promise<void> {
        if (toUpload === undefined) {
            return;
        }
        const [folderId, pathName, key] = toUpload;

        const documentState = this._getDocument(folderId, pathName, key);
        if (documentState === undefined) {
            this._logger.verbose(
                `upload: upload cancelled or no longer tracking document ${folderId}:${pathName}`
            );
            return;
        }
        documentState.uploadRequested = false;

        const documentText = documentState.getText();
        const blobName = this._blobNameCalculator.calculate(pathName, documentText);
        if (blobName === undefined) {
            this._embargo(folderId, pathName, documentState, "failed to compute blob name");
            return;
        }

        const history = documentState.longestHistory(false);
        const savedChangeset =
            history === undefined || history.blobName === undefined
                ? undefined
                : { changeTracker: cloneDeep(history.changeTracker), blobName: history.blobName };
        documentState.inProgressUpload = {
            uploadSeq: documentState.appliedSeq,
            blobName,
            savedChangeset: savedChangeset,
            changesSinceUpload: new ChangeTracker(),
        };

        // Advance all change trackers to the current state of the document. Doing so means that
        // that they are no longer based on uploadedBlobName, so remove it. We will install a new
        // uploadedBlobName when the upload completes (if successful). Until then, we will use
        // inProgressUpload.savedChangeset.blobName.
        documentState.advanceAll();
        documentState.uploadedBlobName = undefined;

        let result: MemorizeResult | undefined = undefined;
        try {
            this._logger.verbose(`upload: begin; ${folderId}:${pathName}, ${blobName}`);
            const startTime = Date.now();
            result = await retryWithBackoff(async () => {
                if (Date.now() - startTime > maxUploadMs) {
                    return undefined;
                }
                if (!this._validateInProgressUpload(folderId, pathName, key)) {
                    return undefined;
                }
                return this._apiServer.memorize(pathName, documentText, blobName, []);
            }, this._logger);
        } catch (e: any) {
            // Embargo documents that fail with a permanent error.
            this._logger.verbose(
                `upload: failed; ${folderId}:${pathName}, ${blobName}; ${getErrmsg(e)};`
            );
            return this._embargo(
                folderId,
                pathName,
                documentState,
                `upload encountered permanent error: ${getErrmsg(e)}`
            );
        }

        if (!this._validateInProgressUpload(folderId, pathName, key)) {
            this._logger.verbose(`upload: upload cancelled; pathName = ${folderId}:${pathName}`);
            return this._retryUpload(folderId, pathName);
        }
        if (result === undefined) {
            this._logger.verbose(
                `upload: upload timed out, cancelling; pathName = ${folderId}:${pathName}`
            );
            this._cancelInProgressUpload(folderId, pathName, documentState);
            return this._retryUpload(folderId, pathName);
        }

        const newBlobName = result.blobName;
        if (newBlobName === blobName) {
            this._logger.verbose(`upload: completed; ${folderId}:${pathName}, ${newBlobName}`);
        } else {
            this._logger.error(
                "upload: completed with mismatched blobName; " +
                    "pathName, received, expected = " +
                    `${folderId}:${pathName}, ${newBlobName}, ${blobName}`
            );
        }

        documentState.inProgressUpload.blobName = newBlobName;
        this._enqueueVerifyWaiter({ folderId, pathName, key, startTime: Date.now() }, newBlobName);
    }

    // _requeueVerifyWaiter decides how to handle a blob that is not yet indexed.
    private _requeueVerifyWaiter(verifyItem: VerifyItem, blobName: string): void {
        const folderId = verifyItem.folderId;
        const pathName = verifyItem.pathName;
        if (!this._validateInProgressUpload(folderId, pathName, verifyItem.key)) {
            this._logger.verbose(
                `requeue verify-wait: upload cancelled; ${folderId}:${pathName}, ${blobName}`
            );
            return this._retryUpload(folderId, pathName);
        }
        const totalVerifyMs = Date.now() - verifyItem.startTime;
        if (totalVerifyMs > verifyPatienceMs) {
            // The blob has been waiting for too long. Retry more lazily.
            this._logger.verbose(`verify-wait: enqueue long; pathName = ${folderId}:${pathName}`);
            this._longWaiters.insert(verifyItem);
        } else {
            // The blob has not been waiting long. Keep retrying aggressively.
            this._enqueueVerifyWaiter(verifyItem, blobName);
        }
    }

    private _enqueueVerifyWaiter(verifyItem: VerifyItem, blobName: string): void {
        this._logger.verbose(
            `verify-wait: enqueue; ${verifyItem.folderId}:${verifyItem.pathName}, ${blobName}`
        );
        this._verifyWaiters.insert(verifyItem);
    }

    private _enqueueForVerify(verifyItem: VerifyItem | undefined): Promise<void> {
        if (verifyItem === undefined) {
            void this._verifyQueue.kick();
            return Promise.resolve();
        }
        this._verifyQueue.insert(verifyItem);
        return Promise.resolve();
    }

    private _grabVerifyBatch(): Map<string, Array<VerifyItem>> | undefined {
        if (this._verifyBatch.size === 0) {
            return undefined;
        }
        const batch = this._verifyBatch;
        this._verifyBatch = new Map<string, Array<VerifyItem>>();
        return batch;
    }

    // _verify adds the given blob to a batch of blobs to be verified with find-missing. The
    // batch will be sent when it reaches `verifyBatchSize` items or when there are no more items
    // to add to the batch. The latter case is indicated by a call with `item` === undefined.
    private async _verify(verifyItem: VerifyItem | undefined): Promise<void> {
        if (verifyItem !== undefined) {
            const documentState = this._getDocument(
                verifyItem.folderId,
                verifyItem.pathName,
                verifyItem.key
            );
            if (documentState === undefined || documentState.inProgressUpload === undefined) {
                // Document is no longer tracked or upload/verify cancelled.
                return;
            }
            let verifyItems = this._verifyBatch.get(documentState.inProgressUpload.blobName);
            if (verifyItems === undefined) {
                verifyItems = new Array<VerifyItem>();
                this._verifyBatch.set(documentState.inProgressUpload.blobName, verifyItems);
            }
            verifyItems.push(verifyItem);
            if (this._verifyBatch.size < verifyBatchSize) {
                return;
            }
        }

        const batch = this._grabVerifyBatch();
        if (batch === undefined) {
            return;
        }

        const blobNames = [...batch.keys()];
        this._logger.verbose(`verify batch: blob count = ${blobNames.length}`);
        let result: FindMissingResult | undefined = undefined;
        try {
            const startTime = Date.now();
            result = await retryWithBackoff(async () => {
                if (Date.now() - startTime > maxVerifyMs) {
                    return undefined;
                }
                return this._apiServer.findMissing(blobNames);
            }, this._logger);
        } catch {}

        if (result === undefined) {
            this._logger.verbose("verify: timeout exceeded");
            for (const blobName of blobNames) {
                const verifyItems = batch.get(blobName)!;
                for (const verifyItem of verifyItems) {
                    this._requeueVerifyWaiter(verifyItem, blobName);
                }
            }
        } else {
            this._logVerifyResult(result);
            const unknownBlobNames = new Set(result.unknownBlobNames);
            const nonindexedBlobNames = new Set(result.nonindexedBlobNames);
            for (const [blobName, verifyItems] of batch) {
                if (unknownBlobNames.has(blobName)) {
                    for (const verifyItem of verifyItems) {
                        this.notifyMissingBlob(verifyItem.folderId, verifyItem.pathName, blobName);
                    }
                } else if (nonindexedBlobNames.has(blobName)) {
                    for (const verifyItem of verifyItems) {
                        this._requeueVerifyWaiter(verifyItem, blobName);
                    }
                } else {
                    for (const verifyItem of verifyItems) {
                        this._commit(verifyItem, blobName);
                    }
                }
            }
        }
    }

    // _commit commits the given blob name to the document state after a succcessful
    // upload and verify.
    private _commit(verifyItem: VerifyItem, blobName: string): void {
        const folderId = verifyItem.folderId;
        const pathName = verifyItem.pathName;
        const result = this._validateInProgressUpload(folderId, pathName, verifyItem.key);
        if (result === undefined) {
            this._logger.verbose(`commit: upload cancelled for ${folderId}:${pathName}`);
            return;
        }
        const [documentState, inProgressUpload] = result;

        documentState.inProgressUpload = undefined;

        this._logger.verbose(
            `commit: ${folderId}:${pathName}, ${blobName}; ` +
                `uploadSeq = ${inProgressUpload.uploadSeq}`
        );

        // Commit the new upload state.
        documentState.uploadedBlobName = blobName;
        documentState.uploadedSeq = inProgressUpload.uploadSeq;
        documentState.changesSinceUpload = inProgressUpload.changesSinceUpload;

        if (documentState.uploadRequested) {
            this._retryUpload(verifyItem.folderId, verifyItem.pathName);
        }
    }

    // _purgeUnneededChangesets removes changesets whose unique content (that which is not present
    // in any newer changeset) is too old to be of interest.
    private _purgeUnneededChangesets(): void {
        const chunkInfo = this.getRecentChunkInfo(this._chunkSize, true);
        if (chunkInfo.length < maxTotalChunks) {
            return;
        }

        // We want to purge all changesets whose unique content is older than maxSeq.
        const maxSeq = chunkInfo[maxTotalChunks - 1].seq;

        // Make a set of documents that have chunks to be purged.
        const documents = new Set<DocumentState>();
        for (let idx = maxTotalChunks; idx < chunkInfo.length; idx++) {
            const documentState = this._getDocument(
                chunkInfo[idx].folderId,
                chunkInfo[idx].pathName
            );
            if (documentState === undefined) {
                // This is unexpected since we just got some chunks for this path,
                // but just to be safe...
                continue;
            }
            documents.add(documentState);
        }

        for (const documentState of documents) {
            if (documentState === undefined) {
                // This is unexpected since we just got a digest with chunks for this path,
                // but just to be safe...
                continue;
            }
            const purged = documentState.purgeChangesets(maxSeq);
            if (purged > 0) {
                this._logger.verbose(
                    `purge: removed ${purged} changesets from ` +
                        `${documentState.folderId}:${documentState.pathName}`
                );
            }
        }
    }

    private _embargo(
        folderId: number,
        pathName: string,
        documentState: DocumentState,
        reason: string
    ): void {
        this._logger.info(`embargoing: ${folderId}:${pathName} reason = ${reason}`);
        documentState.embargo();
    }

    private _logVerifyResult(result: FindMissingResult): void {
        // Our find-missing requests should never return unknown blob names, so use "error" if
        // there are any. Otherwise "verbose".
        const logLevel = result.unknownBlobNames.length > 0 ? "error" : "verbose";

        this._logger.log(
            logLevel,
            "find-missing reported " +
                `${result.unknownBlobNames.length} unknown blob names and ` +
                `${result.nonindexedBlobNames.length} nonindexed blob names.`
        );

        if (result.unknownBlobNames.length > 0) {
            this._logger.log(logLevel, `unknown blob names:`);
            logArray(this._logger, logLevel, result.unknownBlobNames, 5);
        }

        if (result.nonindexedBlobNames.length > 0) {
            this._logger.log(logLevel, "nonindexed blob names:");
            logArray(this._logger, logLevel, result.nonindexedBlobNames, 5);
        }
    }
}
