import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import * as vscode from "vscode";

import { getLogger } from "../../logging";
import { FileEditEvent, SingleEdit } from "../../next-edit/file-edit-events";
import { FileEditEventsQueue } from "../../next-edit/file-edit-events/file-edit-events-queue";
import { isNotebookDocument } from "../../utils/notebook";
import * as pathUtils from "../../utils/path-utils";
import * as workspaceEvents from "../../workspace/workspace-events";
import { IFileEditProcessor } from "./file-edit-processor-types";

/**
 * keeps last known text for each document
 * converts vscode events to change objects.
 *
 **/
class OpenDocumentSnapshotCache {
    private readonly _logger = getLogger("OpenDocumentSnapshotCache");
    constructor(private readonly _blobNameCalculator: BlobNameCalculator) {}

    // maps relPath to text
    private _lastKnownText = new Map<string, string>();
    handleDocumentOpened(
        event:
            | workspaceEvents.FolderTextDocumentOpenedEvent
            | workspaceEvents.FolderNotebookDocumentOpenedEvent
    ): void {
        if (isNotebookDocument(event.document)) {
            // ignore notebook for now. TODO
            return;
        }
        this._lastKnownText.set(event.relPath, event.document.getText());
    }
    handleDocumentClosed(
        event:
            | workspaceEvents.FolderTextDocumentClosedEvent
            | workspaceEvents.FolderNotebookDocumentClosedEvent
    ): void {
        this._lastKnownText.delete(event.relPath);
    }
    handleFileRename(event: workspaceEvents.FolderFileWillRenameEvent): void {
        for (const oldRelPath of this._lastKnownText.keys()) {
            if (pathUtils.directoryContainsPath(event.oldRelPath, oldRelPath)) {
                const newRelPath = event.newRelPath + oldRelPath.slice(event.oldRelPath.length);
                this._lastKnownText.set(newRelPath, this._lastKnownText.get(oldRelPath)!);
                this._lastKnownText.delete(oldRelPath);
            }
        }
    }
    handleFileDeletion(event: workspaceEvents.FolderFileDeletedEvent): void {
        this._lastKnownText.delete(event.relPath);
    }
    // returns undefined if no known last text.
    private _swapLastKnownText(relPath: string, text: string): string | undefined {
        if (!this._lastKnownText.has(relPath)) {
            this._logger.verbose(`[WARN] no known last text for path [${relPath}]. initializing.`);
            this._lastKnownText.set(relPath, text);
            return undefined;
        }

        const lastText = this._lastKnownText.get(relPath);
        this._lastKnownText.set(relPath, text);
        return lastText!;
    }
    handleDocumentChange(
        event:
            | workspaceEvents.FolderTextDocumentChangedEvent
            | workspaceEvents.FolderNotebookDocumentChangedEvent
    ): FileEditEvent | undefined {
        if (Object.prototype.hasOwnProperty.call(event.event, "notebook")) {
            // ignore notebook for now. TODO
            return;
        }

        event = event as workspaceEvents.FolderTextDocumentChangedEvent;
        const text = event.event.document.getText();
        const previousText = this._swapLastKnownText(event.relPath, text);

        if (!previousText) {
            return; // happens when we're not initialized. TODO (au-6388) (moogi) - check why we're not initialized!
        }

        // sometimes we get empty events. We check for this after the swap in case we're missing last text.
        if (event.event.contentChanges.length === 0) {
            return; // no change
        }

        // first make edits from VSCode edit events
        let edits = event.event.contentChanges.map(
            (change: vscode.TextDocumentContentChangeEvent) =>
                new SingleEdit({
                    beforeStart: change.rangeOffset,
                    afterStart: change.rangeOffset, // to be updated later.
                    beforeText: previousText.substring(
                        change.rangeOffset,
                        change.rangeOffset + change.rangeLength
                    ),
                    afterText: change.text,
                })
        );

        // then fix the offsets for multi-edit events
        if (edits.length > 1) {
            edits.sort((a, b) => a.beforeStart - b.beforeStart);
            let offset: number = 0;
            edits = edits.map((edit) => {
                const newAfterStart = edit.afterStart + offset;
                offset += edit.afterText.length - edit.beforeText.length;
                return new SingleEdit({
                    beforeStart: edit.beforeStart,
                    afterStart: newAfterStart,
                    beforeText: edit.beforeText,
                    afterText: edit.afterText,
                });
            });
        }

        return new FileEditEvent({
            path: event.relPath,
            edits: edits,
            beforeBlobName: this._blobNameCalculator.calculateNoThrow(event.relPath, previousText),
            afterBlobName: this._blobNameCalculator.calculateNoThrow(event.relPath, text),
        }).normalize();
    }

    getLastKnownText(relPath: string): string | undefined {
        return this._lastKnownText.get(relPath);
    }
}

/**
 * Handles document changes and tracks recent edits.
 */
export class FileEditProcessor implements IFileEditProcessor {
    private _openDocumentSnapshotCache: OpenDocumentSnapshotCache;
    private _fileEditsStore: FileEditEventsQueue;
    constructor(private readonly _blobNameCalculator: BlobNameCalculator) {
        this._openDocumentSnapshotCache = new OpenDocumentSnapshotCache(this._blobNameCalculator);
        this._fileEditsStore = new FileEditEventsQueue(1000000); // 1mb of changes.
    }
    handleDocumentOpened(event: workspaceEvents.FolderTextDocumentOpenedEvent): void {
        this._openDocumentSnapshotCache.handleDocumentOpened(event);
    }
    handleDocumentClosed(event: workspaceEvents.FolderTextDocumentClosedEvent): void {
        this._openDocumentSnapshotCache.handleDocumentClosed(event);
    }
    handleFileWillRename(event: workspaceEvents.FolderFileWillRenameEvent): void {
        this._openDocumentSnapshotCache.handleFileRename(event);
        // TODO: add rename to change tracker as well.
    }

    handleFileDeletion(event: workspaceEvents.FolderFileDeletedEvent): void {
        this._openDocumentSnapshotCache.handleFileDeletion(event);
    }
    handleDocumentChange(
        event: workspaceEvents.FolderTextDocumentChangedEvent,
        protectedBlobName?: string
    ): number {
        const fileEditEvent = this._openDocumentSnapshotCache.handleDocumentChange(event);
        if (fileEditEvent === undefined) {
            return 0;
        }
        return this._fileEditsStore.addEvent(fileEditEvent, protectedBlobName);
    }
    getEvents(): FileEditEvent[] {
        return this._fileEditsStore.getEvents();
    }

    removeEventsPriorToBlob(blobName: string): void {
        this._fileEditsStore.removeEventsPriorToBlob(blobName);
    }

    getLastKnownText(relPath: string): string | undefined {
        return this._openDocumentSnapshotCache.getLastKnownText(relPath);
    }
}
