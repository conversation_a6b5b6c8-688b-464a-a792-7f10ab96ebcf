import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import * as vscode from "vscode";

import { getLogger } from "../../logging";
import { FileEditEvent, SingleEdit } from "../../next-edit/file-edit-events";
import { DisposableService } from "../../utils/disposable-service";
import { isNotebookDocument } from "../../utils/notebook";
import { BlobRange } from "../open-file-manager";
import * as workspaceEvents from "../workspace-events";
import { BlobStatusStore } from "./blob-status-store";
import {
    BlobsIndexedEvent,
    BlobsUploadedEvent,
    BlobUploadFailedEvent,
    IBlobUploader,
    onBlobsIndexed,
    onBlobsUploaded,
    onBlobUploadFailed,
} from "./blob-uploader";
import { FileEditProcessor } from "./file-edit-processor";
import { IFileEditProcessor } from "./file-edit-processor-types";
import { IOpenFileManagerV2 } from "./open-file-manager-v2-types";

class FolderResources implements vscode.Disposable {
    constructor(
        public blobStatusStore: BlobStatusStore,
        public fileEditProcessor: IFileEditProcessor,
        public fileChangeSizeCounter: FileChangeSizeCounter,
        public workspaceName: string,
        public folderId: number
    ) {}

    dispose(): void {
        this.blobStatusStore.clear();
    }
}

enum UploadReason {
    newFile = "new file",
    largeChange = "large change",
    missingBlob = "missing blob",
    focusLoss = "focus loss",
    fileChange = "file change",
}

class FileChangeSizeCounter {
    private _size = 0;
    private _relPath = "";

    public get size(): number {
        return this._size;
    }
    public add(size: number, relPath: string): number {
        if (this._relPath !== relPath) {
            this._size = size;
            this._relPath = relPath;
        } else {
            this._size += size;
        }
        return this._size;
    }

    clear(): void {
        this._size = 0;
        this._relPath = "";
    }
}

function getDocumentText(document: vscode.TextDocument | vscode.NotebookDocument): string {
    if (isNotebookDocument(document)) {
        // TODO: implement the correct way
        return document
            .getCells()
            .map((cell) => cell.document.getText())
            .join("\n");
    }
    return document.getText();
}

const largeChangeThreshold = 1000;

function getDocumentFromEvent(
    event:
        | workspaceEvents.FolderTextDocumentChangedEvent
        | workspaceEvents.FolderNotebookDocumentChangedEvent
): vscode.TextDocument | vscode.NotebookDocument {
    if (Object.prototype.hasOwnProperty.call(event.event, "notebook")) {
        return (event as workspaceEvents.FolderNotebookDocumentChangedEvent).event.notebook;
    }
    return (event as workspaceEvents.FolderTextDocumentChangedEvent).event.document;
}

export class OpenFileManagerV2 extends DisposableService implements IOpenFileManagerV2 {
    private readonly _folderResources = new Map<number, FolderResources>(); // TODO: use in memory store
    private readonly _logger = getLogger("OpenFileManagerV2");

    // we need to remember which blob goes to which source folder.
    private readonly _uploadingBlobToFolder = new Map<string, number>();

    private _lastHandledEvent: workspaceEvents.FolderTextDocumentChangedEvent | null = null; // we keep track to trigger uploads

    constructor(
        private readonly _blobUploader: IBlobUploader,
        private readonly _blobNameCalculator: BlobNameCalculator
    ) {
        super();
        this.addDisposables(
            onBlobsIndexed((event) => this.handleBlobIndexed(event)),
            onBlobsUploaded((event) => this.handleBlobUploaded(event)),
            onBlobUploadFailed((event) => this.handleBlobUploadFailed(event))
        );
    }

    startTrackingFolder(workspaceName: string, folderId: number): vscode.Disposable {
        const fileEditProcessor = new FileEditProcessor(this._blobNameCalculator);
        const resources = new FolderResources(
            new BlobStatusStore(),
            fileEditProcessor,
            new FileChangeSizeCounter(),
            workspaceName,
            folderId
        );
        this._folderResources.set(folderId, resources);
        this.addDisposable(resources);
        return resources;
    }

    loseFocus(): void {
        if (this._lastHandledEvent === null) {
            return;
        }
        this._upload(
            this._lastHandledEvent.folderId,
            getDocumentText(getDocumentFromEvent(this._lastHandledEvent)),
            this._lastHandledEvent.relPath,
            UploadReason.focusLoss
        );
        this._lastHandledEvent = null;
    }

    _uploadLastIfChanged(event: workspaceEvents.FolderTextDocumentChangedEvent): void {
        const lastEvent = this._lastHandledEvent;
        this._lastHandledEvent = event;
        if (lastEvent === null) {
            return;
        }

        if (event.folderId === lastEvent.folderId && event.relPath === lastEvent.relPath) {
            return;
        }

        this._upload(
            lastEvent.folderId,
            getDocumentText(getDocumentFromEvent(lastEvent)),
            lastEvent.relPath,
            UploadReason.fileChange
        );
    }

    handleBlobIndexed(event: BlobsIndexedEvent): void {
        for (const blobName of event.blobNames) {
            this._logger.verbose(`Blob ${blobName} indexed`);
            const folderId = this._uploadingBlobToFolder.get(blobName);
            if (folderId === undefined) {
                this._logger.debug(
                    `[WARN] Blob ${blobName} was indexed but not tracked. Ignoring.`
                );
                return;
            }
            this._uploadingBlobToFolder.delete(blobName);
            this._folderResources.get(folderId)?.blobStatusStore.updateBlobIndexed(blobName);

            this._folderResources
                .get(folderId)
                ?.fileEditProcessor.removeEventsPriorToBlob(blobName);
        }
    }

    handleBlobUploadFailed(event: BlobUploadFailedEvent): void {
        const blobName = event.blobName;
        if (!this._uploadingBlobToFolder.has(blobName)) {
            return;
        }
        this._logger.debug(`Blob ${blobName} failed to upload`);
        this._uploadingBlobToFolder.delete(blobName);
    }

    handleBlobUploaded(event: BlobsUploadedEvent): void {
        this._logger.verbose(`Handling ${event.expectedToActualBlobNameMap.size} uploaded blobs`);

        // handle blobs that were renamed
        for (const [expectedBlobName, actualBlobName] of event.expectedToActualBlobNameMap) {
            if (expectedBlobName === actualBlobName) {
                continue;
            }

            const folderId = this._uploadingBlobToFolder.get(expectedBlobName);
            if (folderId === undefined) {
                this._logger.debug(
                    // this will be valid if we reuse the uploader.
                    `Blob ${expectedBlobName} was uploaded but not tracked. Ignoring.`
                );
                continue;
            }
            this._logger.debug(
                `[WARN] Blob name mismatch. Expected ${expectedBlobName} but got ${actualBlobName}.`
            );
            this._uploadingBlobToFolder.set(actualBlobName, folderId);
            this._uploadingBlobToFolder.delete(expectedBlobName);

            // also update blob name in store
            const folder = this._folderResources.get(folderId);
            if (folder === undefined) {
                this._logger.debug(
                    `[WARN] Blob ${expectedBlobName} was uploaded but folder ${folderId} is not tracked. Ignoring.`
                );
                continue;
            }
            folder.blobStatusStore.updateBlobName(expectedBlobName, actualBlobName);
        }
    }

    _upload(folderId: number, text: string, relPath: string, reason: UploadReason): void {
        const workspaceName = this._folderResources.get(folderId)?.workspaceName;
        this._logger.debug(`[${workspaceName}] Uploading [${relPath}] because [${reason}]`);

        const blobName = this._blobUploader.enqueueUpload(
            {
                path: relPath,
                // eslint-disable-next-line @typescript-eslint/require-await
                readContent: async () => {
                    return text;
                },
            },
            text
        );
        if (!blobName) {
            return;
        }
        this._uploadingBlobToFolder.set(blobName, folderId);
        this._folderResources.get(folderId)?.blobStatusStore.addUploadedBlob(blobName, relPath);
        this._blobUploader.startUpload();
    }

    stopTracking(folderId: number, pathName: string): void {
        this._logger.verbose(
            `[${this._folderResources.get(folderId)?.workspaceName}] Stopping tracking [${pathName}]`
        );
        this._folderResources.get(folderId)?.blobStatusStore.removePath(pathName);
    }

    // TODO: Complete. support notebook. integrate with change tracker.
    addOpenedDocument(
        event:
            | workspaceEvents.FolderTextDocumentOpenedEvent
            | workspaceEvents.FolderNotebookDocumentOpenedEvent,
        blobName: string | undefined
    ): void {
        const folder = this._folderResources.get(event.folderId);
        if (folder === undefined) {
            throw new Error(`Source folder [${event.folderId}] is not open`);
        }

        const workspaceName = folder.workspaceName;

        if (isNotebookDocument(event.document)) {
            // TODO
            this._logger.info(
                `TODO [${workspaceName}] Ignoring notebook document ${event.relPath}`
            );
        }

        const currentText = getDocumentText(event.document);
        const currentBlobName = this._blobNameCalculator.calculate(event.relPath, currentText);
        if (!currentBlobName) {
            folder.blobStatusStore.embargoPath(event.relPath);
            this._logger.debug(`[WARN] Failed to calculate blob name for ${event.relPath}`);
            return;
        }
        if (currentBlobName !== blobName) {
            this._upload(event.folderId, currentText, event.relPath, UploadReason.newFile);
            this._logger.debug(
                `[INFO] Blob name mismatch. Expected ${blobName} but got ${currentBlobName}.`
            );
        } else {
            folder.blobStatusStore.addIndexedBlob(blobName, event.relPath);
        }
    }

    getBlobName(folderId: number, pathName: string): string | undefined {
        return this._folderResources.get(folderId)?.blobStatusStore?.getIndexedBlobName(pathName);
    }

    handleMissingBlob(folderId: number, pathName: string, blobName: string): boolean {
        const folder = this._folderResources.get(folderId);
        if (folder === undefined) {
            return false;
        }

        const isTrackingBlob = folder.blobStatusStore.isTrackingBlob(blobName);
        const workspaceName = folder.workspaceName;
        if (!isTrackingBlob) {
            return false;
        }
        this._logger.info(
            `[${workspaceName}] Re-uploading ${blobName} for ${pathName} in ${folderId}`
        );

        const text = folder.fileEditProcessor.getLastKnownText(pathName);
        if (text === undefined) {
            return false;
        }
        this._upload(folderId, text, pathName, UploadReason.missingBlob);
        return true;
    }

    handleClosedDocument(
        event:
            | workspaceEvents.FolderTextDocumentClosedEvent
            | workspaceEvents.FolderNotebookDocumentClosedEvent
    ): void {
        const workspaceName = this._folderResources.get(event.folderId)?.workspaceName;
        this._logger.verbose(`[${workspaceName}] Handling closed document ${event.relPath}`);
        this.stopTracking(event.folderId, event.relPath);
    }

    handleChangedDocument(
        event:
            | workspaceEvents.FolderTextDocumentChangedEvent
            | workspaceEvents.FolderNotebookDocumentChangedEvent
    ): void {
        const folder = this._folderResources.get(event.folderId);
        if (folder === undefined) {
            this._logger.debug(
                `Ignoring change event for ${event.relPath} because folder is not tracked`
            );
            return;
        }
        if (folder.blobStatusStore.isEmbargoed(event.relPath)) {
            return;
        }

        const workspaceName = folder.workspaceName;

        if (Object.prototype.hasOwnProperty.call(event.event, "notebook")) {
            // ignore notebook for now. TODO
            this._logger.debug(`[${workspaceName}] Ignoring notebook document ${event.relPath}`);
            return;
        }
        const fileEditProcessor = folder.fileEditProcessor;

        if (fileEditProcessor === undefined) {
            this._logger.debug(
                `[${workspaceName}] Ignoring change event for ${event.relPath} because folder is not tracked`
            );
            return;
        }
        // get protected blob names
        this._uploadLastIfChanged(event as workspaceEvents.FolderTextDocumentChangedEvent);
        const protectedBlobName = folder.blobStatusStore.getLastBlobNameForPath(event.relPath);
        const change = fileEditProcessor.handleDocumentChange(
            event as workspaceEvents.FolderTextDocumentChangedEvent,
            protectedBlobName
        );
        const changeSize = folder.fileChangeSizeCounter.add(change, event.relPath) ?? 0;

        if (changeSize > largeChangeThreshold) {
            this._upload(
                event.folderId,
                getDocumentText(getDocumentFromEvent(event)),
                event.relPath,
                UploadReason.largeChange
            );
            folder.fileChangeSizeCounter.clear();
        }
    }

    isTracked(folderId: number, pathName: string): boolean {
        return (
            this._folderResources.get(folderId)?.blobStatusStore.isTrackingPath(pathName) ?? false
        );
    }

    getTrackedPaths(folderId: number): Array<string> {
        return this._folderResources.get(folderId)?.blobStatusStore.getTrackedPaths() ?? [];
    }

    // NOTE (moogi): I am pretty convinced we don't need this function.
    // Because completions are checking overlap by content, not by range.
    // Keeping it for now for backward compatibility.
    // Need a followup effort to verify we can remove this function.
    //
    //
    // `translate` translates the given range, which is relative to the current state of the
    // document, to the corresponding range in the base state of the document. In ambiguous
    // situtations where there are multiple possible translations, it returns the widest possible
    // range. In practice, this means that if `start` falls inside a modification, the returned
    // range will start at the modification's left edge, and if `end` falls inside a modification,
    // the returned range will end at the modification's right edge.
    translateRange(
        folderId: number,
        pathName: string,
        beginOffset: number,
        endOffset: number
    ): BlobRange | undefined {
        const indexedBlobName = this.getBlobName(folderId, pathName);
        if (indexedBlobName === undefined) {
            return undefined;
        }
        let result: BlobRange = {
            blobName: indexedBlobName,
            beginOffset: beginOffset,
            endOffset: endOffset,
        };
        const updateRange = (singleEdit: SingleEdit, blobRange: BlobRange): BlobRange => {
            if (singleEdit.afterStart > blobRange.endOffset) {
                return blobRange;
            }
            if (singleEdit.afterEnd < blobRange.beginOffset) {
                const offset = singleEdit.afterText.length - singleEdit.beforeText.length;
                return {
                    blobName: blobRange.blobName,
                    beginOffset: blobRange.beginOffset - offset,
                    endOffset: blobRange.endOffset - offset,
                };
            }

            return {
                blobName: blobRange.blobName,
                beginOffset: Math.min(singleEdit.afterStart, blobRange.beginOffset),
                endOffset: Math.max(singleEdit.afterEnd, blobRange.endOffset),
            };
        };

        const events =
            this._folderResources
                .get(folderId)
                ?.fileEditProcessor.getEvents()
                .filter((e) => {
                    return e.path === pathName;
                }) ?? [];

        for (const event of events) {
            for (const edit of event.edits) {
                result = updateRange(edit, result);
            }
        }
        return result;
    }

    getAllEditEvents(): Map<number, FileEditEvent[]> {
        const result = new Map<number, FileEditEvent[]>();
        return Array.from(this._folderResources.keys()).reduce((result, key) => {
            const folderId = Number(key);
            result.set(
                folderId,
                this._folderResources.get(folderId)?.fileEditProcessor.getEvents() ?? []
            );
            return result;
        }, result);
    }

    getAllPathToIndexedBlob(): Map<number, Map<string, string>> {
        const result = new Map<number, Map<string, string>>();

        return Array.from(this._folderResources.keys()).reduce((result, key) => {
            const folderId = Number(key);
            result.set(
                folderId,
                this._folderResources.get(folderId)?.blobStatusStore.getAllPathToIndexedBlob() ??
                    new Map<string, string>()
            );
            return result;
        }, result);
    }
}
