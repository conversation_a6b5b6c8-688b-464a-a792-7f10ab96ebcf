import { getLogger } from "../../../logging";
import { IQueue, IQueueProcessor } from "./interfaces";
import { SimpleQueue } from "./simple-queue";

const logger = getLogger("SimpleQueueProcessor");

abstract class SimpleQueueProcessor<T> implements IQueueProcessor, IQueue<T> {
    private queue: IQueue<T>;
    private _inProgress = false;
    private _disposed = false;

    constructor(queue: IQueue<T> = new SimpleQueue<T>()) {
        this.queue = queue;
    }

    enqueue(item: T): boolean {
        return this.queue.enqueue(item);
    }

    dequeue(): T | undefined {
        return this.queue.dequeue();
    }

    isEmpty(): boolean {
        return this.queue.isEmpty();
    }

    clear(): void {
        this.queue.clear();
    }

    peek(): T | undefined {
        return this.queue.peek();
    }

    getItems(): readonly T[] {
        return this.queue.getItems();
    }

    size(): number {
        return this.queue.size();
    }

    dispose(): void {
        this._disposed = true;
    }

    public startProcess(): void {
        // fire and forget
        if (this._inProgress) {
            return;
        }
        this._inProgress = true;
        // avoid using async/await here so we can return "void" and prevent consumers from handling the promise (enforced by eslint)
        Promise.resolve()
            .then(async () => {
                while (!this.isEmpty() && !this.isDisposed()) {
                    const firstItem = this.peek();
                    // the while loop will handle additional items added during the current processing
                    await this.internalProcess();
                    if (firstItem === this.peek()) {
                        // no progress made, so break the loop // TODO: add a test for this case.
                        break;
                    }
                }
            })
            .catch((e) => {
                if (e instanceof Error) {
                    logger.info(`Unhandled error while processing task: ${e.message} ${e.stack}`);
                } else {
                    logger.info(`Unhandled error while processing task: ${e}`);
                }
            })
            .finally(() => {
                this._inProgress = false;
            });
    }

    // proprietary function to be used by the child class

    protected isDisposed(): boolean {
        return this._disposed;
    }

    /**
     * This is the function that the child class must implement.
     * It is called when the queue is not empty and the processor is not in progress.
     */
    protected abstract internalProcess(): Promise<void>;
}

export { SimpleQueueProcessor };
