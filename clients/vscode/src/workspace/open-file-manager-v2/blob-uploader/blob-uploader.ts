/***
 * This is an implementation of a blob uploader.
 * We have a couple of them, the goal is to unify them eventually.
 *
 * The distinction with this one is that it utilizes smarter queues that were not utilized previously.
 *
 * Important:
 * Every blob name has to be accounted for and communicated back to the consumer.
 * We need to assume consumers are keeping a state based on a blob name.
 * And if we don't report progress (succeeded, failed, etc..) on one of them, we are risking memory leaks and corrupt states.
 *
 */
import { retryWithBackoff } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { Event, EventEmitter } from "vscode";

import { APIServer, UploadBlob } from "../../../augment-api";
import { BatchUploadResult, FindMissingResult } from "../../../augment-api";
import { getLogger } from "../../../logging";
import { DisposableService } from "../../../utils/disposable-service";
import {
    blobsIndexedEventEmitter,
    blobsUploadedEventEmitter,
    blobUploadFailedEmitter,
} from "./blob-uploader-events";
import { BlobUploadRequest, IBlobUploader } from "./blob-uploader-types";
import { RetryScheduler } from "./retry-scheduler";
import { SimpleQueueProcessor } from "./simple-queue-processor";

type FileUploadItem = BlobUploadRequest & {
    blobName: string;
};

type BlobStatusConfig = {
    maxBatchCount: number;
    oldBlobNameThresholdMs: number;
    giveUpBlobNameThresholdMs: number;
    oldBlobNameRetryMs: number;
    newBlobNameRetryMs: number;
};

function getIndexedBlobs(
    allBlobNames: Iterable<string>,
    missingBlobs: FindMissingResult
): string[] {
    const unknownSet: Set<string> = new Set<string>(
        missingBlobs.unknownBlobNames.concat(missingBlobs.nonindexedBlobNames)
    );
    const result = [];
    for (const blobName of allBlobNames) {
        if (!unknownSet.has(blobName)) {
            result.push(blobName);
        }
    }
    return result;
}

class BlobStatusExecutor extends SimpleQueueProcessor<string> {
    private readonly _onFoundIndexedBlobNamesEmitter = new EventEmitter<string[]>();
    private readonly _onFoundUnknownBlobNamesEmitter = new EventEmitter<string[]>();
    private readonly _pollingStartTime = new Map<string, number>();

    private readonly _retryOldBlobNames: RetryScheduler<string>;
    private readonly _retryNewBlobNames: RetryScheduler<string>;

    private readonly _logger = getLogger("FileUploader#BlobStatusExecutor");

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _config: BlobStatusConfig
    ) {
        super();
        this._retryOldBlobNames = new RetryScheduler(this, {
            retryMs: this._config.oldBlobNameRetryMs,
        });
        this._retryNewBlobNames = new RetryScheduler(this, {
            retryMs: this._config.newBlobNameRetryMs,
        });
    }

    dispose(): void {
        super.dispose();
        this._pollingStartTime.clear();
        this._onFoundIndexedBlobNamesEmitter.dispose();
        this._onFoundUnknownBlobNamesEmitter.dispose();
    }

    get onFoundIndexedBlobNames(): Event<string[]> {
        return this._onFoundIndexedBlobNamesEmitter.event;
    }

    get onFoundUnknownBlobNames(): Event<string[]> {
        return this._onFoundUnknownBlobNamesEmitter.event;
    }

    retryBlobNames(blobNames: string[]) {
        const recentList = 0;
        const longList = 1;
        const giveUpList = 2;
        const [recentBlobName, longRunBlobNames, giveUpBlobNames] = blobNames.reduce(
            (acc: [string[], string[], string[]], blobName) => {
                const startTime = this._pollingStartTime.get(blobName);
                if (startTime === undefined) {
                    // this should not happen, but just in case we log and retry.
                    this._logger.debug(`[WARN] retryBlobNames: missing start time for ${blobName}`);
                    this._pollingStartTime.set(blobName, Date.now()); // I don't like this, might hide the error, but since this is installed client side we have limited options.
                    acc[recentList].push(blobName);
                    return acc;
                }
                const age = Date.now() - startTime;
                if (age < this._config.oldBlobNameThresholdMs) {
                    acc[recentList].push(blobName);
                } else if (age > this._config.giveUpBlobNameThresholdMs) {
                    acc[giveUpList].push(blobName);
                } else {
                    acc[longList].push(blobName);
                }
                return acc;
            },
            [[], [], []] as [string[], string[], string[]]
        );
        this._logger.debug(
            `retryBlobNames: retrying [${recentBlobName.length}] recent, [${longRunBlobNames.length}] old and [${giveUpBlobNames.length}] failed blob names`
        );
        this._retryNewBlobNames.retryAll(recentBlobName);
        this._retryOldBlobNames.retryAll(longRunBlobNames);
        for (const blobName of giveUpBlobNames) {
            this._pollingStartTime.delete(blobName);
        }
    }

    async internalProcess(): Promise<void> {
        const blobNames: Set<string> = new Set();
        let batchItemsCounter = 0;
        while (batchItemsCounter < this._config.maxBatchCount) {
            const blobName = this.dequeue();
            if (blobName === undefined) {
                break;
            }
            blobNames.add(blobName);
            if (!this._pollingStartTime.has(blobName)) {
                this._pollingStartTime.set(blobName, Date.now());
            }
            batchItemsCounter++;
        }

        this._logger.verbose(`FindMissingProcess started: for [${blobNames.size}] items`);
        let missing: FindMissingResult;
        try {
            missing = await retryWithBackoff(
                () => this._apiServer.findMissing([...blobNames]),
                this._logger
            );
        } catch (e) {
            this._logger.debug(`[ERROR] FindMissingProcess failed: for [${blobNames.size}] items`);
            this.retryBlobNames([...blobNames]);
            return;
        }

        // lost blobs.. they were uploaded, but now they are unknown
        if (missing.unknownBlobNames.length > 0) {
            this._logger.debug(
                `FindMissingProcess found unknown: for [${missing.unknownBlobNames.length}] items`
            );
            for (const blobName of missing.unknownBlobNames) {
                this._pollingStartTime.delete(blobName);
            }
            this._onFoundUnknownBlobNamesEmitter.fire(missing.unknownBlobNames);
        }

        // uploaded, not yet indexed, schedule for retry
        if (missing.nonindexedBlobNames.length > 0) {
            this._logger.debug(
                `FindMissingProcess found nonindexed: for [${missing.nonindexedBlobNames.length}] items`
            );
            this.retryBlobNames(missing.nonindexedBlobNames);
        }

        const indexedBlobs = getIndexedBlobs(blobNames, missing);
        for (const blobName of indexedBlobs) {
            this._pollingStartTime.delete(blobName);
        }
        if (indexedBlobs.length > 0) {
            this._logger.verbose(
                `FindMissingProcess found not missing: for [${indexedBlobs.length}] items`
            );
            this._onFoundIndexedBlobNamesEmitter.fire(indexedBlobs);
        }
    }
}

type ProcessUploadResult = {
    // The backend might produce different blob names.
    // This is a map from the expected blob name (calculated locally) to the actual blob name.
    expectedToActualBlobNameMap: Map<string, string>;
    // The number of failed uploads.
    // The caller should schedule a retry for the failed items based on their location in the list.
    failedCount: number;
};

type UploadConfig = {
    maxUploadSize: number;
    maxBatchCount: number;
    retryMs: number;
};

class UploadExecutor extends SimpleQueueProcessor<FileUploadItem> {
    private readonly _onUploadedEmitter = new EventEmitter<Map<string, string>>();
    private readonly _onFailedEmitter = new EventEmitter<FileUploadItem>();
    private readonly _logger = getLogger("FileUploader#UploadExecutor");

    private readonly _retryScheduler;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _config: UploadConfig
    ) {
        super();
        this._retryScheduler = new RetryScheduler(this, {
            retryMs: this._config.retryMs,
        });
    }

    dispose(): void {
        super.dispose();
        this._onUploadedEmitter.dispose();
        this._onFailedEmitter.dispose();
    }

    get onDidUpload(): Event<Map<string, string>> {
        return this._onUploadedEmitter.event;
    }

    get onFailed(): Event<FileUploadItem> {
        return this._onFailedEmitter.event;
    }

    // _processUpload is the `processItem` method for the _uploadQueue WorkQueue. It
    // uploads the blob to the back end.
    // This function should not handle errors or schedule retries other than retryWithBackoff on the api call.
    // Everything else is handled by the caller.
    private async _processUpload(blobsToUpload: UploadBlob[]): Promise<ProcessUploadResult> {
        const now = Date.now();
        let result: BatchUploadResult | undefined = undefined;

        this._logger.verbose(`Upload started [${now}]: for [${blobsToUpload.length}] items`);

        result = await retryWithBackoff(
            () => this._apiServer.batchUpload(blobsToUpload),
            this._logger
        );

        this._logger.debug(
            `Upload complete [${now}]: for [${result.blobNames.length} / ${blobsToUpload.length}] items`
        );

        const blobNameMap = new Map<string, string>();
        if (result !== undefined) {
            for (let idx = 0; idx < result.blobNames.length; idx++) {
                const localName = blobsToUpload[idx].blobName;
                const remoteName = result.blobNames[idx];
                blobNameMap.set(localName, remoteName);
                if (localName !== remoteName) {
                    this._logger.debug(
                        `[WARN]Upload blob name mismatch: ${localName} -> ${remoteName}`
                    );
                }
            }
        }

        const failedCount = blobsToUpload.length - (result?.blobNames.length ?? 0);

        return { expectedToActualBlobNameMap: blobNameMap, failedCount };
    }

    processResult(result: ProcessUploadResult, itemsInFlight: FileUploadItem[]) {
        const { expectedToActualBlobNameMap, failedCount } = result;
        if (failedCount > 0) {
            this._logger.debug(`[WARN] Scheduling for retry [${failedCount}] items`);
            this._retryScheduler.retryAll(itemsInFlight.slice(itemsInFlight.length - failedCount));
        }

        if (expectedToActualBlobNameMap.size > 0) {
            this._onUploadedEmitter.fire(expectedToActualBlobNameMap);
        }
    }

    async internalProcess(): Promise<void> {
        let inFlightSize = 0;
        const blobsToUpload: UploadBlob[] = [];
        const itemsInFlight: FileUploadItem[] = [];

        let batchItemsCounter = 0;
        while (!this.isDisposed() && batchItemsCounter < this._config.maxBatchCount) {
            const nextItem = this.peek();
            if (nextItem === undefined) {
                break;
            }
            const content = await nextItem.readContent();
            if (content.length > this._config.maxUploadSize) {
                this._logger.debug(
                    `[WARN] UploadExecutor: skipping upload for ${nextItem.path} because it is too large`
                );
                this.dequeue();
                continue;
            }

            if (inFlightSize + content.length > this._config.maxUploadSize) {
                break;
            }
            inFlightSize += content.length;
            blobsToUpload.push({
                pathName: nextItem.path,
                text: content,
                blobName: nextItem.blobName,
                metadata: [],
            });
            itemsInFlight.push(nextItem);
            this.dequeue();
            batchItemsCounter++;
        }
        if (blobsToUpload.length === 0) {
            this._logger.debug(`UploadExecutor: no items to upload`);
            return;
        }

        try {
            const result: ProcessUploadResult = await this._processUpload(blobsToUpload);
            this.processResult(result, itemsInFlight);
        } catch (e: any) {
            this._logger.debug(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `[ERROR] UploadExecutor failed: for [${blobsToUpload.length}] items. Caused by: ${e.message} ${e.stack}`
            );
            // try uploading files individually
            await this.processIndividualUploads(itemsInFlight, blobsToUpload);
        }
    }

    private async processIndividualUploads(
        itemsInFlight: FileUploadItem[],
        blobsToUpload: UploadBlob[]
    ): Promise<void> {
        for (let i = 0; i < itemsInFlight.length; i++) {
            const itemInFlight = itemsInFlight[i];
            const blobToUpload = blobsToUpload[i];
            try {
                const result: ProcessUploadResult = await this._processUpload([blobToUpload]);
                this.processResult(result, [itemInFlight]);
            } catch (e) {
                this._logger.debug(`[ERROR] UploadExecutor failed: for [${itemInFlight.blobName}]`);
                this._onFailedEmitter.fire(itemInFlight);
            }
        }
    }
}

type BlobUploaderConfig = {
    uploadConfig: UploadConfig;
    blobStatusConfig: BlobStatusConfig;
};

const defaultFileUploaderConfig: BlobUploaderConfig = {
    uploadConfig: {
        maxUploadSize: 10000000,
        maxBatchCount: 128,
        retryMs: 60000,
    },
    blobStatusConfig: {
        maxBatchCount: 1000,
        oldBlobNameThresholdMs: 1000 * 60, // blobs that are not indexed for more than 1 minute are considered old
        giveUpBlobNameThresholdMs: 1000 * 60 * 5, // blobs that are not indexed for more than 5 minutes are considered failed
        oldBlobNameRetryMs: 60000,
        newBlobNameRetryMs: 3000,
    },
};

class BlobUploader extends DisposableService implements IBlobUploader {
    private readonly _logger = getLogger("BlobUploaderImpl");
    private readonly _uploadExecutor: UploadExecutor;
    private readonly _blobStatusExecutor: BlobStatusExecutor;
    private readonly _handledItems = new Map<string, FileUploadItem>(); // blob to upload request

    constructor(
        private readonly _blobNameCalculator: BlobNameCalculator,
        readonly _apiServer: APIServer,
        readonly _config: BlobUploaderConfig = defaultFileUploaderConfig
    ) {
        super();
        this._uploadExecutor = new UploadExecutor(_apiServer, _config.uploadConfig);

        this._blobStatusExecutor = new BlobStatusExecutor(_apiServer, _config.blobStatusConfig);

        this.addDisposables(
            {
                dispose: () => {
                    this._handledItems.clear();
                },
            },
            this._uploadExecutor,
            this._blobStatusExecutor,

            // on upload done
            // 1. fire upload event that includes blob renaming for consumers to handle
            // 2. apply blob name changes internally as needed
            // 3. queue for status polling
            this._uploadExecutor.onDidUpload((uploadedBlobs) => {
                blobsUploadedEventEmitter.fire({ expectedToActualBlobNameMap: uploadedBlobs });
                for (const [expectedBlobName, actualBlobName] of uploadedBlobs) {
                    if (!this._handledItems.has(expectedBlobName)) {
                        continue;
                    }
                    // handle rename if relevant
                    if (expectedBlobName !== actualBlobName) {
                        this._handledItems.set(
                            actualBlobName,
                            this._handledItems.get(expectedBlobName)!
                        );
                        this._handledItems.delete(expectedBlobName);
                    }
                    // wait until indexed
                    this._blobStatusExecutor.enqueue(actualBlobName);
                }
                this._blobStatusExecutor.startProcess();
            }),

            // when failed fire failed event
            this._uploadExecutor.onFailed((item) => {
                this._logger.debug(`[ERROR] Upload failed: ${item.path}`);
                this._handledItems.delete(item.blobName);
                blobUploadFailedEmitter.fire({ blobName: item.blobName });
            }),

            // when indexed fire indexed event
            this._blobStatusExecutor.onFoundIndexedBlobNames((blobNames) => {
                for (const blobName of blobNames) {
                    this._handledItems.delete(blobName);
                }
                blobsIndexedEventEmitter.fire({ blobNames });
            }),

            // when unknown, if still relevant, enqueue for upload
            this._blobStatusExecutor.onFoundUnknownBlobNames((blobNames) => {
                for (const blobName of blobNames) {
                    const item = this._handledItems.get(blobName);
                    if (!item) {
                        continue;
                    }
                    this._uploadExecutor.enqueue(item);
                }
                this._uploadExecutor.startProcess();
            })
        );
    }

    enqueueUpload(uploadRequest: BlobUploadRequest, content: string): string | undefined {
        const blobName = this._blobNameCalculator.calculate(uploadRequest.path, content);
        if (blobName === undefined) {
            this._logger.debug(`blobNameCalculator returned undefined for ${uploadRequest.path}`);
            return undefined;
        }
        const uploadItem = {
            ...uploadRequest,
            blobName,
        };
        this._handledItems.set(blobName, uploadItem);
        this._logger.verbose(
            `upload enqueued: ${uploadRequest.path}. total: ${this._handledItems.size}`
        );
        this._blobStatusExecutor.enqueue(blobName);
        return blobName;
    }

    startUpload() {
        this._blobStatusExecutor.startProcess();
    }
}

export { BlobUploader, BlobUploadRequest, BlobUploaderConfig };
