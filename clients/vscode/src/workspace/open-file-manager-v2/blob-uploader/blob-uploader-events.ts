import { EventEmitter } from "vscode";

export type BlobUploadFailedEvent = {
    blobName: string;
};

export type BlobsIndexedEvent = {
    blobNames: string[];
};

export type BlobsUploadedEvent = {
    expectedToActualBlobNameMap: Map<string, string>;
};

// we fire the blob name that failed the upload
export const blobUploadFailedEmitter = new EventEmitter<BlobUploadFailedEvent>();
export const onBlobUploadFailed = blobUploadFailedEmitter.event;

// we fire this event so consumers can handle the case where the blob is different.
// the event sends a map of expected blob name to actual blob name.
export const blobsUploadedEventEmitter = new EventEmitter<BlobsUploadedEvent>();
export const onBlobsUploaded = blobsUploadedEventEmitter.event;

// we fire this event so consumers can handle the case where the blob is indexed.
export const blobsIndexedEventEmitter = new EventEmitter<BlobsIndexedEvent>();
export const onBlobsIndexed = blobsIndexedEventEmitter.event;
