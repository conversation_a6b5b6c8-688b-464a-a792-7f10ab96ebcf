import Denque from "denque";

import { IQueue } from "./interfaces";

enum QueueSizeManagement {
    /**
     * Removes the oldest items when the queue is full.
     * Ensures that the queue size remains within a defined limit and always contains the most recent items.
     */
    REMOVE_OLDEST = "REMOVE_OLDEST", // eslint-disable-line

    /**
     * Prevents adding new items when the queue is full.
     * Ensures that the queue size remains within a defined limit.
     */
    REJECT_NEW_ITEMS = "REJECT_NEW_ITEMS", // eslint-disable-line
}

type SimpleQueueConfig = {
    /**
     * The maximum number of items in the queue.
     * Default: 1000000
     */
    sizeLimit: number;
    /**
     * The strategy to use when the queue is full.
     * Default: QueueSizeManagement.REMOVE_OLDEST
     */
    queueSizeManagement: QueueSizeManagement;
};

class SimpleQueue<T> implements IQueue<T> {
    private readonly _queue: Denque<T> = new Denque<T>();
    private config: SimpleQueueConfig;

    constructor(config: Partial<SimpleQueueConfig> = {}) {
        this.config = {
            sizeLimit: config.sizeLimit ?? 1000000, // default to 1 million items
            queueSizeManagement: config.queueSizeManagement ?? QueueSizeManagement.REMOVE_OLDEST,
        };
    }

    enqueue(item: T): boolean {
        // handle overflow first
        if (this._queue.length >= this.config.sizeLimit) {
            if (this.config.queueSizeManagement === QueueSizeManagement.REJECT_NEW_ITEMS) {
                return false;
            }
            if (this.config.queueSizeManagement === QueueSizeManagement.REMOVE_OLDEST) {
                this._queue.shift();
            }
        }

        this._queue.push(item);
        return true;
    }

    dequeue(): T | undefined {
        return this._queue.shift();
    }

    isEmpty(): boolean {
        return this._queue.isEmpty();
    }

    clear(): void {
        this._queue.clear();
    }

    peek(): T | undefined {
        return this._queue.peekFront();
    }

    getItems(): readonly T[] {
        return Object.freeze(this._queue.toArray());
    }

    size(): number {
        return this._queue.length;
    }
}

export { SimpleQueue, QueueSizeManagement };
