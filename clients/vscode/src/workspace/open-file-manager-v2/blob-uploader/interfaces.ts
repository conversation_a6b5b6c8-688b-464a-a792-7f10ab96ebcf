import { Disposable } from "vscode";

interface IQueue<T> {
    /**
     * Adds an item to the queue.
     * @param item The item to add.
     * @returns True if the item was added, false otherwise.
     */
    enqueue(item: T): boolean;

    /**
     * Removes and returns the first item in the queue.
     * @returns The first item in the queue, or undefined if the queue is empty.
     */
    dequeue(): T | undefined;

    /**
     * Checks if the queue is empty.
     * @returns True if the queue is empty, false otherwise.
     */
    isEmpty(): boolean;

    /**
     * Clears the queue.
     */
    clear(): void;

    /**
     * Returns the first item in the queue without removing it.
     * @returns The first item in the queue, or undefined if the queue is empty.
     */
    peek(): T | undefined;

    /**
     * Returns all items in the queue.
     * @returns An array of items in the queue.
     */
    getItems(): readonly T[];

    /**
     * Returns the number of items in the queue.
     * @returns The number of items in the queue.
     */
    size(): number;
}

interface IQueueProcessor extends Disposable {
    startProcess(): void;
}

export { IQueue, IQueueProcessor };
