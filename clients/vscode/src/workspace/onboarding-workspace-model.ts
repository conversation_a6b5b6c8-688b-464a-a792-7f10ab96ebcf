import * as vscode from "vscode";

import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { SyncingStatusReporter } from "./syncing-status-reporter";
import { SyncingStatus, SyncingStatusEvent } from "./types";

export class OnboardingWorkspaceModel extends DisposableService {
    private _workspaceMessageState = new Map<string, WorkspaceMessageState>();
    private _shouldShowSummary = false;
    private shouldShowSummaryEmitter = new vscode.EventEmitter<boolean>();

    constructor(
        private readonly _globalState: AugmentGlobalState,
        private readonly _syncingStatus: SyncingStatusReporter
    ) {
        super();
        this.loadWorkspaceMessageState();
        this.addDisposable(
            this._syncingStatus.onDidChangeSyncingStatus((_event) => this._handleSyncingProgress())
        );
    }

    public get shouldShowSummary(): boolean {
        return this._shouldShowSummary;
    }

    public get onShouldShowSummary(): vscode.Event<boolean> {
        return this.shouldShowSummaryEmitter.event;
    }

    public setShouldShowSummary(shouldShowSummary: boolean) {
        this._shouldShowSummary = shouldShowSummary;
    }

    private async _handleSyncingProgress() {
        const status = this._syncingStatus.status;

        if (status.foldersProgress.length === 0) {
            return;
        }

        await this.handleShowingSummaryMsg(status);
    }

    private async handleShowingSummaryMsg(status: SyncingStatusEvent) {
        if (status.status !== SyncingStatus.done) {
            // Don't show a summary until syncing is complete.
            return;
        }

        // Don't show a summary if all of the workspaces are empty (no tracked files)
        if (
            status.foldersProgress.every(
                (p) => p.progress?.trackedFiles === undefined || p.progress.trackedFiles === 0
            )
        ) {
            return;
        }

        // We will only show the summary message if we have not seen it before for all workspaces
        const summaryMsg = status.foldersProgress.find((p) => {
            return this._workspaceMessageState.get(p.folderRoot)?.workspaceSummary;
        });
        if (summaryMsg) {
            // Workspace summary has already been shown, do nothing
            return;
        }

        const newlyTrackedFolder = status.foldersProgress.find((item) => {
            return item.progress?.newlyTracked;
        });
        if (!newlyTrackedFolder) {
            // If there are no newly tracked workspaces, do nothing
            return;
        }

        this.showSummaryMessage();
        for (const progress of status.foldersProgress) {
            this._workspaceMessageState.set(progress.folderRoot, {
                folderRoot: progress.folderRoot,
                workspaceSummary: true,
            });
        }
        await this.saveWorkspaceMessageState();
    }

    private showSummaryMessage() {
        // Set this so that if the chat webview app queries for state, it knows to show the summary message
        this._shouldShowSummary = true;

        // Send an event in case the webview app is already alive
        this.shouldShowSummaryEmitter.fire(true);
    }

    private async saveWorkspaceMessageState() {
        // Save the workspace message state to the global state
        await this._globalState.update(
            GlobalContextKey.workspaceMessageStates,
            Array.from(this._workspaceMessageState.values())
        );
    }

    private loadWorkspaceMessageState() {
        // Load the workspace message state from the global state
        const state = this._globalState.get<WorkspaceMessageState[]>(
            GlobalContextKey.workspaceMessageStates
        );
        if (state) {
            this._workspaceMessageState = new Map(state.map((s) => [s.folderRoot, s]));
        }
    }

    dispose() {
        this.shouldShowSummaryEmitter.dispose();
    }
}

// NOTE: The Augment Global State is used to track what kinds of messages we have already shown
// to the user for a given workspace. It is structured as [JSON.stringify(WorkspaceMessageState)]
interface WorkspaceMessageState {
    folderRoot: string;
    workspaceSummary: boolean;
}
