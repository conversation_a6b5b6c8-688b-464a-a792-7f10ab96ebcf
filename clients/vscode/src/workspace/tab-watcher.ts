import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { RecencyUniqueList } from "../utils/recency-unique-list";
import { WorkspaceManager } from "./workspace-manager";

export type TabSwitchEventInfo = {
    // relative path name of the file that was switched into
    relPathName: string;

    // blob name that corresponds to `pathName`
    blobName: string;
};

export class TabWatcher extends DisposableService {
    // History recent tab switches. Each item is the absolute path name of the file that was
    // switched to.
    private _tabSwitchHistory = new RecencyUniqueList<string>(20);

    private _logger = getLogger("TabWatcher");

    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super();

        // Clean the current text editor's document when we switch editor tabs.
        this.addDisposable(
            vscode.window.onDidChangeActiveTextEditor(this._notifyActiveEditorChanged.bind(this))
        );
    }

    private _notifyActiveEditorChanged(activeTextEditor: vscode.TextEditor | undefined): void {
        // VSCode calls this function twice every time the active editor is changed,
        // once with activeTextEditor undefined, another with the actual information.

        if (activeTextEditor === undefined) {
            return;
        }

        const uri = activeTextEditor.document.uri;
        if (this._workspaceManager.resolvePathName(uri.fsPath) === undefined) {
            return;
        }
        this._tabSwitchHistory.add(uri.fsPath);
    }

    // Returns the recent tab switch history. Oldest events first.
    public getTabSwitchEvents(): TabSwitchEventInfo[] {
        let tabSwitchEvents: TabSwitchEventInfo[] = [];
        for (const absPathName of this._tabSwitchHistory.toArray()) {
            const qualifiedPathName = this._workspaceManager.resolvePathName(absPathName);
            if (qualifiedPathName === undefined) {
                continue;
            }
            const blobName = this._workspaceManager.getBlobName(qualifiedPathName);
            if (blobName === undefined) {
                continue;
            }
            tabSwitchEvents.push({ relPathName: qualifiedPathName.relPath, blobName });
        }
        return tabSwitchEvents;
    }
}
