import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";

import { FileEditEvent } from "../next-edit/file-edit-events";

export type Chunk = {
    seq: number;
    timestamp: Date;
    uploaded: boolean;
    repoRoot: string;
    pathName: string;
    blobName: string;
    text: string;
    origStart: number;
    origLength: number;
    expectedBlobName: string;
};

export class WorkspaceContext {
    constructor(
        // blobs is a compact representation of the set of blob names that comprise
        // the workspace context.
        public readonly blobs: Blobs,

        // recentChunks is a list of chunks representing recent edit history.
        public readonly recentChunks: Array<Chunk>,

        // trackedPaths is a 2-level map of repoRoot->relPath->blobName for paths that are open
        // in vscode. All pathNames that contributed recentChunks will be found in this map, as
        // will pathNames for which there are open tabs but no recent changes.
        // --> trackedPaths: Map<repoRoot, Map<relPath, blobName>>
        public readonly trackedPaths: Map<string, Map<string, string>>,

        // editEvents is a list of file edit events.
        public readonly unindexedEditEvents: FileEditEvent[] = [],

        // Temporary - editEvents indexed blob names - only during migration to open file manager v2.
        public readonly unindexedEditEventsBaseBlobNames: string[] = [],

        // lastChatResponse contains the text and sequence number of the last response from chat,
        // if any. The sequence number is from the same sequence number space as the recentChunks,
        // so it can be used to order the chat response with respect to the recentChunks.
        public readonly lastChatResponse?: { seq: number; timestamp: Date; text: string },

        // blobNames is the set of blob names that comprise the completion context
        // If defined, it is the expanded form of "blobs". If undefined, it is meaningless.
        public readonly blobNames?: string[]
    ) {}

    public static empty(): WorkspaceContext {
        return new WorkspaceContext(
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
            new Array<Chunk>(), // recentChunks
            new Map<string, Map<string, string>>(), // trackedPaths
            [], // editEvents
            [], // editEventsIndexedBlobNames
            undefined, // lastChatResponse
            [] // blobNames
        );
    }
}

export type WorkspaceContextWithBlobNames = WorkspaceContext &
    Required<Pick<WorkspaceContext, "blobNames">>;
