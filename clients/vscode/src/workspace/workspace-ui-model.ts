import throttle from "lodash/throttle";
import * as vscode from "vscode";

import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { WorkTimer } from "../metrics/work-timer";
import { DisposableService } from "../utils/disposable-service";
import { FileType } from "../utils/types";
import { AsyncMsgHandler } from "../utils/webviews/messaging";
import { createAsyncMsgHandlerFromWebview } from "../utils/webviews/messaging-helper";
import {
    WebViewMessage,
    WebViewMessageType,
    WSContextFileInclusionState,
    WSContextFileItem,
    WSContextFolderContentsChanged,
    WSContextGetChildrenRequest,
    WSContextGetChildrenResponse,
    WSContextGetSourceFoldersResponse,
    WSContextSourceFolder,
    WSContextSourceFoldersChanged,
} from "../webview-providers/webview-messages";
import {
    SourceFolderEnumerationState,
    SourceFolderInfo,
    SourceFolderItem,
    SourceFolderType,
    TrackedSourceFolderInfo,
} from "./workspace-types";

export interface WorkspaceUIManagerMethods {
    addExternalSourceFolder(uri: vscode.Uri): void;
    removeExternalSourceFolder(name: string): void;
    listChildren(folderRoot: string, subdirRoot: string): Array<SourceFolderItem>;
    onDidChangeSourceFolders: vscode.Event<void>;
    onDidChangeSourceFolderContents: vscode.Event<string>;
    listSourceFolders(): Array<SourceFolderInfo>;
    refreshSourceFolders(): void;
}

export class WorkspaceUIModel extends DisposableService {
    private _logger = getLogger("WorkspaceUIModel");
    private _asyncMsgHandler: AsyncMsgHandler;

    constructor(
        private _workspaceManager: WorkspaceUIManagerMethods,
        private _webview: vscode.Webview,
        private _featureFlagManager: FeatureFlagManager,
        workTimer: WorkTimer | undefined = undefined
    ) {
        super();
        this._asyncMsgHandler = createAsyncMsgHandlerFromWebview(this._webview, workTimer);

        this.addDisposable(
            this._featureFlagManager.subscribe(
                ["enableWorkspaceManagerUi"],
                this._registerAllHandlers.bind(this)
            )
        );

        this._registerAllHandlers();
    }

    private _registerAllHandlers() {
        if (!this._featureFlagManager.currentFlags.enableWorkspaceManagerUi) {
            return;
        }

        this._setupWorkspaceListeners();

        this.addDisposable(this._asyncMsgHandler);

        this.addDisposable(this._webview.onDidReceiveMessage(this.onDidReceiveMessage));

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.wsContextGetSourceFoldersRequest,
            this.getSourceFoldersHandler
        );

        this._asyncMsgHandler.registerHandler(
            WebViewMessageType.wsContextGetChildrenRequest,
            this.getChildrenHandler
        );
    }

    private _setupWorkspaceListeners() {
        // We throttle these because we might get a flurry of requests from the workspace manager
        // under certain circumstances so we dont want to hammer the UI with each individual update.
        this.addDisposable(
            this._workspaceManager.onDidChangeSourceFolders(
                throttle(
                    () => {
                        void this._webview.postMessage({
                            type: WebViewMessageType.wsContextSourceFoldersChanged,
                        } as WSContextSourceFoldersChanged);
                    },
                    500,
                    { leading: true, trailing: true }
                )
            )
        );

        this.addDisposable(
            this._workspaceManager.onDidChangeSourceFolderContents(
                throttle(
                    (folderRoot: string) => {
                        void this._webview.postMessage({
                            type: WebViewMessageType.wsContextFolderContentsChanged,
                            data: folderRoot,
                        } as WSContextFolderContentsChanged);
                    },
                    500,
                    { leading: true, trailing: true }
                )
            )
        );
    }

    private getSourceFoldersHandler = (): WSContextGetSourceFoldersResponse => {
        const sourceFolders = this._workspaceManager
            .listSourceFolders()
            .sort((a, b) => a.name.localeCompare(b.name));
        return {
            type: WebViewMessageType.wsContextGetSourceFoldersResponse,
            data: {
                workspaceFolders: sourceFolders.map(WorkspaceUIModel.makeWSContextSourceFolder),
            },
        };
    };

    private getChildrenHandler = (m: WSContextGetChildrenRequest): WSContextGetChildrenResponse => {
        const children = this._workspaceManager
            .listChildren(m.data.fileId.folderRoot, m.data.fileId.relPath)
            .sort((a, b) => a.name.localeCompare(b.name));
        return {
            type: WebViewMessageType.wsContextGetChildrenResponse,
            data: {
                children: children.map(WorkspaceUIModel.makeWSContextFileItem),
            },
        };
    };

    private static getInclusionState(child: SourceFolderItem): WSContextFileInclusionState {
        if (!child.included) {
            return WSContextFileInclusionState.excluded;
        } else if (child.type === FileType.directory && child.containsExcludedItems) {
            return WSContextFileInclusionState.partial;
        } else {
            return WSContextFileInclusionState.included;
        }
    }

    private static makeWSContextFileItem = (child: SourceFolderItem): WSContextFileItem => {
        return {
            name: child.name,
            fileId: {
                folderRoot: child.folderRoot,
                relPath: child.relPath,
            },
            type: child.type === FileType.directory ? "folder" : "file",
            inclusionState: WorkspaceUIModel.getInclusionState(child),
            reason: child.reason,
            trackedFileCount:
                child.type === FileType.directory ? child.trackedFileCount : undefined,
        };
    };

    private static makeWSContextSourceFolder = (f: SourceFolderInfo): WSContextSourceFolder => {
        return {
            name: f.name,
            fileId: {
                folderRoot: f.folderRoot,
                relPath: "",
            },
            inclusionState: (f as TrackedSourceFolderInfo).containsExcludedItems
                ? WSContextFileInclusionState.partial
                : WSContextFileInclusionState.included,
            isWorkspaceFolder:
                f.type === SourceFolderType.vscodeWorkspaceFolder ||
                f.type === SourceFolderType.nestedWorkspaceFolder,
            isNestedFolder:
                f.type === SourceFolderType.nestedWorkspaceFolder ||
                f.type === SourceFolderType.nestedExternalFolder,
            isPending:
                (f as TrackedSourceFolderInfo).enumerationState ===
                SourceFolderEnumerationState.inProgress,
            trackedFileCount:
                f.type === SourceFolderType.vscodeWorkspaceFolder ||
                f.type === SourceFolderType.externalFolder
                    ? f.trackedFileCount
                    : undefined,
        };
    };

    private onDidReceiveMessage = async (message: WebViewMessage): Promise<void> => {
        this._logger.debug(`Extension received message: ${message.type}`);

        switch (message.type) {
            case WebViewMessageType.wsContextAddMoreSourceFolders: {
                const uris = await vscode.window.showOpenDialog({
                    canSelectFolders: true,
                    canSelectFiles: false,
                    canSelectMany: true,
                    openLabel: "Add Source Folder",
                });
                if (uris && uris.length > 0) {
                    const errors: { path: string; message: string }[] = uris
                        .map((uri) => {
                            try {
                                this._workspaceManager.addExternalSourceFolder(uri);
                                return null;
                            } catch (err) {
                                this._logger.error(`Failed to add source folder:`, err);
                                if (err instanceof Error) {
                                    return { path: uri.fsPath, message: err.message };
                                } else {
                                    return { path: uri.fsPath, message: String(err) };
                                }
                            }
                        })
                        .filter((err) => err !== null) as { path: string; message: string }[];
                    if (errors.length > 0) {
                        void vscode.window.showErrorMessage(
                            "One or more source folders could not be added:\n" +
                                errors.map((err) => `${err.path}: ${err.message}`).join("\n")
                        );
                    }
                }
                break;
            }
            case WebViewMessageType.wsContextRemoveSourceFolder: {
                try {
                    this._workspaceManager.removeExternalSourceFolder(message.data);
                } catch (err) {
                    let msg = err;
                    if (err instanceof Error) {
                        msg = err.message;
                    }
                    this._logger.error(`Failed to remove source folder:`, msg);
                    void vscode.window.showErrorMessage(
                        `Failed to remove source folder ${message.data}:\n ${String(msg)}`
                    );
                }
                break;
            }
            case WebViewMessageType.wsContextUserRequestedRefresh: {
                this._workspaceManager.refreshSourceFolders();
                break;
            }
        }
    };
}
