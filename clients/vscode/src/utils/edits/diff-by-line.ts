import type { editor as monaco } from "monaco-editor";

import { ChatInstructionStreamResult } from "../../augment-api";
import { DiffViewDiffStreamChunkData } from "../../webview-providers/webview-messages";
import { isNil } from "../types-utils";

export interface IMonacoEditOp {
    lineChange: monaco.IChange;
    newText: string;
}

export interface CoalescedEditInfo {
    startLineNumber: number | null;
    endLineNumber: number | null;
    newText: string;
    oldText: string;
    hasYieldedNewChunk: boolean;
    currOffset: number | null;
    startRefined: boolean;
    endRefined: boolean;

    newTextBuffer: string;
    prefixOverlapChecked: boolean;
}

interface InferrableEditInfo {
    startLineNumber: number;
    endLineNumber: number;
    newText: string;
}

function newCoalescedEditInfo(): CoalescedEditInfo {
    return {
        startLineNumber: null,
        endLineNumber: null,
        newText: "",
        oldText: "",
        hasYieldedNewChunk: false,
        currOffset: null,
        startRefined: false,
        endRefined: false,

        newTextBuffer: "",
        // We also do suffix checking, it just doesn't need flag
        prefixOverlapChecked: false,
    };
}

function canConvertToMonacoEditOp(
    info: CoalescedEditInfo
): info is InferrableEditInfo & CoalescedEditInfo {
    return !isNil(info.startLineNumber) && !isNil(info.endLineNumber) && !isNil(info.newText);
}

function canRefineStart(info: CoalescedEditInfo): info is CoalescedEditInfo & InferrableEditInfo {
    return !info.startRefined && !isNil(info.startLineNumber) && info.oldText.trim().length > 0;
}

function canRefineEnd(info: CoalescedEditInfo): info is CoalescedEditInfo & InferrableEditInfo {
    return !info.endRefined && !isNil(info.endLineNumber) && info.oldText.trim().length > 0;
}

/**
 * Refines the start and end line numbers of an edit operation based on the actual content.
 *
 * This function attempts to improve the accuracy of the edit operation by comparing
 * the specified start and end lines with the actual content of the file. It can
 * adjust the line numbers to better match the intended edit, which helps in
 * reducing errors and improving the precision of the edit operation.
 *
 * The function handles both regular edits and pure insertions differently,
 * ensuring that the line numbers are correctly adjusted in both cases.
 *
 * @param edit - The CoalescedEditInfo object containing the edit operation details.
 * @param code - The full content of the file being edited.
 *
 * @remarks
 * This function modifies the `edit` object in place, updating its `startLineNumber`,
 * `endLineNumber`, `startRefined`, and `endRefined` properties as necessary.
 */
function possiblyRefineStartEndLines(edit: CoalescedEditInfo, code: string) {
    if (canRefineStart(edit)) {
        const { isPureInsertion, lineBefore } = detectPureInsertion(edit);

        if (isPureInsertion) {
            edit.startLineNumber =
                possiblyRefineLineNumber(code, edit.startLineNumber, lineBefore, 20) + 1;
        } else {
            const oldTextLines = edit.oldText.replaceAll(/\n$/g, "").split("\n");
            const firstLineInOldText = oldTextLines[0];
            edit.startLineNumber = possiblyRefineLineNumber(
                code,
                edit.startLineNumber,
                firstLineInOldText,
                20
            );
        }
        edit.startRefined = true;
    }

    if (canRefineEnd(edit)) {
        const { isPureInsertion } = detectPureInsertion(edit);

        if (isPureInsertion) {
            edit.endLineNumber = edit.startLineNumber;
        } else {
            const oldTextLines = edit.oldText.replaceAll(/\n$/g, "").split("\n");
            const lastLineInOldText = oldTextLines[oldTextLines.length - 1];
            edit.endLineNumber =
                possiblyRefineLineNumber(code, edit.endLineNumber - 1, lastLineInOldText, 20) + 1;
        }
        edit.endRefined = true;
    }
}

function toFullEdit(info: CoalescedEditInfo & InferrableEditInfo): IMonacoEditOp {
    // Remove trailing newline, if it exists, to get the count.
    const lineCountInNewText = (info.newText.match(/\n/g) || []).length;

    return {
        lineChange: {
            originalStartLineNumber: info.startLineNumber,
            originalEndLineNumber: info.endLineNumber ? info.endLineNumber : info.startLineNumber,
            // [inclusive, exclusive)
            modifiedStartLineNumber: info.startLineNumber,
            modifiedEndLineNumber: info.startLineNumber + lineCountInNewText,
        },
        newText: info.newText,
    };
}

function coalesceChunk(
    info: CoalescedEditInfo,
    chunk: ChatInstructionStreamResult
): CoalescedEditInfo {
    const { replacementStartLine, replacementEndLine, replacementText, replacementOldText } = chunk;
    if (!isNil(replacementStartLine)) {
        info.startLineNumber = replacementStartLine;
    }
    if (!isNil(replacementEndLine)) {
        info.endLineNumber = replacementEndLine;
    }
    if (!isNil(replacementText)) {
        info.newText += replacementText;
    }
    if (!isNil(replacementOldText)) {
        info.oldText += replacementOldText;
    }
    return info;
}

/**
 * Applies edits to code and returns the stream of modified code.
 *
 * Main piece of logic here is accumulating changes that are coming from stream,
 * translating them into monaco edit ops and applying them using `applyEdits`.
 * yield is done for every edit.
 */
export async function* applyInstructionEdits(
    code: string,
    instructionStream: AsyncGenerator<ChatInstructionStreamResult>
): AsyncGenerator<string> {
    let fullEdits: IMonacoEditOp[] = [];
    let currEditDict: CoalescedEditInfo = newCoalescedEditInfo();
    let newLines: string[] = code.split("\n");

    for await (const instructionChunk of splitStream(instructionStream)) {
        let { replacementStartLine, replacementOldText } = instructionChunk;

        // Means we have a new edit, so we push the previous one to array and reset currentEdit
        if (
            // We have a new edit, and our old edit is ready. This means we're at a boundary
            (!isNil(replacementStartLine) || !isNil(replacementOldText)) &&
            canConvertToMonacoEditOp(currEditDict)
        ) {
            const fullEdit = toFullEdit(currEditDict);
            const currOffset = currEditDict.currOffset ?? computeRunningOffset(fullEdit, fullEdits);
            fullEdits.push(fullEdit);

            // Apply the edit and update the running offset
            const offsetEdit = applyOffset(fullEdit, currOffset);
            newLines = applyEditToLines([...newLines], offsetEdit);
            currEditDict = newCoalescedEditInfo();
        }

        currEditDict = coalesceChunk(currEditDict, instructionChunk);
        if (canRefineStart(currEditDict)) {
            const oldTextLines = currEditDict.oldText.replaceAll(/\n$/g, "").split("\n");
            const firstLineInOldText = oldTextLines[0];
            currEditDict.startLineNumber = possiblyRefineLineNumber(
                code,
                currEditDict.startLineNumber,
                firstLineInOldText,
                20
            );
        }
        if (canRefineEnd(currEditDict)) {
            const oldTextLines = currEditDict.oldText.replaceAll(/\n$/g, "").split("\n");
            const lastLineInOldText = oldTextLines[oldTextLines.length - 1];
            currEditDict.endLineNumber =
                possiblyRefineLineNumber(code, currEditDict.endLineNumber, lastLineInOldText, 20) +
                1;
        }

        // If currentEdit has both lines set, we could start yielding (together with all previous edits)
        if (canConvertToMonacoEditOp(currEditDict)) {
            const fullEdit = toFullEdit(currEditDict);
            currEditDict.currOffset =
                currEditDict.currOffset ?? computeRunningOffset(fullEdit, fullEdits);
            const offsetEdit = applyOffset(fullEdit, currEditDict.currOffset);
            const tmpNewLines = applyEditToLines([...newLines], offsetEdit);
            yield tmpNewLines.join("\n");
        }
    }
}

/** Refines the line number of an edit based on the old text lines.
 * Sometimes model makes mistakes in line numbers and they don't align with generated old text.
 * Important note: this function uses 1-based indexing.
 *
 */
function possiblyRefineLineNumber(
    fullFile: string,
    lineNumber: number,
    oldTextLine: string,
    searchRange: number
): number {
    const fullFileLines = fullFile.split("\n").map((line) => line.trimEnd());
    oldTextLine = oldTextLine.trimEnd();
    // If the line number is correct, return it
    if (fullFileLines[lineNumber - 1] === oldTextLine) {
        return lineNumber;
    }
    // While searching, prioritize lines closer to the original line number
    for (let i = 1; i <= searchRange; i++) {
        if (lineNumber > i && fullFileLines[lineNumber - i - 1] === oldTextLine) {
            return lineNumber - i;
        }
        if (
            lineNumber + i <= fullFileLines.length &&
            fullFileLines[lineNumber + i - 1] === oldTextLine
        ) {
            return lineNumber + i;
        }
    }
    // If we didn't find the line, return the original line number
    return lineNumber;
}

/**
 * Checks if a new chunk can be yielded from the edit.
 * @param edit - The CoalescedEditInfo to check.
 * @returns True if the edit can yield a new chunk, false otherwise.
 */
function canYieldNewChunk(edit: CoalescedEditInfo): edit is InferrableEditInfo & CoalescedEditInfo {
    return !isNil(edit.startLineNumber) && !edit.hasYieldedNewChunk;
}

/**
 * Processes edit(CoalescedEditInfo) and returns newText's chunk that can be yielded.
 * Under the hood performs overlap checks with prefix and suffix.
 * Also, chunk that is returned is removed from `edit.newText` as side-effect.
 * @param edit - The CoalescedEditInfo to process
 * @param code - Full content of target file
 * @param overlapCheckLimit - Max number lines that are used in overlap check
 * @param isEditCompleted - Indicates whether edit is completed, ie already has endLineNumber.
 * @returns `newText` text chunk that is ready to be yielded.
 */
export function getYieldableChunk(
    edit: CoalescedEditInfo,
    code: string,
    overlapCheckLimit: number,
    isEditCompleted: boolean
): string {
    let toYield = "";

    if (isNil(edit.startLineNumber)) {
        throw new Error("Start line should be already set when replacement text is set");
    }

    let bufferLines = edit.newTextBuffer.split(/(?<=\n)/);
    const prefixLines = code.split(/(?<=\n)/).slice(0, edit.startLineNumber - 1);

    if (!edit.prefixOverlapChecked) {
        // Overlap can't be longer than prefix
        let prefixOverlapCheckLimit = Math.min(overlapCheckLimit, prefixLines.length);

        if (bufferLines.length <= prefixOverlapCheckLimit && !isEditCompleted) {
            // We either didn't collect enough new text and need wait a bit more
            // (we need at least `prefixOverlapCheckLimit` *full* lines)
            // Or new text is very short, but then we just process it at the end
            return toYield;
        }

        for (
            let possibleOverlapLength = prefixOverlapCheckLimit;
            possibleOverlapLength >= 1;
            possibleOverlapLength--
        ) {
            let overlapFound = true;
            for (let i = 0; i < possibleOverlapLength; i++) {
                if (
                    bufferLines[i] !== prefixLines[prefixLines.length - possibleOverlapLength + i]
                ) {
                    overlapFound = false;
                    break;
                }
            }
            if (!overlapFound) {
                continue;
            }
            // We found overlap, so we drop overlap from buffer
            bufferLines = bufferLines.slice(possibleOverlapLength);
            // And we also drop it from main edit, because otherwise it will break subsequent range computations.
            edit.newText = edit.newText
                .split(/(?<=\n)/)
                .slice(possibleOverlapLength)
                .join("");
            break;
        }
        edit.prefixOverlapChecked = true;
    }

    // Then we yield all lines except for last `overlapCheckLimit` lines
    toYield = bufferLines.slice(0, -overlapCheckLimit).join("");
    bufferLines = bufferLines.slice(-overlapCheckLimit);

    // If edit is not completed, we might need to do overlap check with suffix later
    // So we're done for now.
    if (!isEditCompleted) {
        edit.newTextBuffer = bufferLines.join("");
        return toYield;
    }

    // Here edit is completed, so we can do overlap check with suffix
    if (isNil(edit.endLineNumber)) {
        throw new Error("End line should be already set when edit is completed.");
    }
    const suffixLines = code.split(/(?<=\n)/).slice(edit.endLineNumber - 1);

    // Overlap can't be longer than suffix
    let suffixOverlapCheckLimit = Math.min(overlapCheckLimit, suffixLines.length);
    for (
        let possibleOverlapLength = suffixOverlapCheckLimit;
        possibleOverlapLength >= 1;
        possibleOverlapLength--
    ) {
        let overlapFound = true;
        for (let i = 0; i < possibleOverlapLength; i++) {
            if (bufferLines[bufferLines.length - possibleOverlapLength + i] !== suffixLines[i]) {
                overlapFound = false;
                break;
            }
        }
        if (!overlapFound) {
            continue;
        }
        // We found overlap, so we drop overlap from buffer
        bufferLines = bufferLines.slice(0, -possibleOverlapLength);
        // And we also drop it from main edit, because otherwise it will break subsequent range computations.
        edit.newText = edit.newText
            .split(/(?<=\n)/)
            .slice(0, -possibleOverlapLength)
            .join("");
        break;
    }

    // At the end (when edit is completed) we should have empty buffer, because we yielded all values (or filtered them out)
    // Everything in bufferLines wasn't filtered out, so we can yield it
    edit.newTextBuffer = "";
    toYield += bufferLines.join("");

    // Note(yuri): Smartpaste backend switched to new logic which
    // guarantees every line to end with \n, but until Code Instructions
    // switch to that logic, we need this for Code Instruction.
    if (toYield.length > 0 && !toYield.endsWith("\n")) {
        toYield += "\n";
    }

    return toYield;
}

/**
 * Main way to detect whether operation is a pure insertion,
 * is when startLine===endLine, but since frontend doesn't
 * receive endLine until very end, we need other way to signal
 * that edit is a pure insertion earlier.
 * Particularly, to correctly refine startLine.
 * @param edit
 * @returns  isPureInsertion: true/false, lineBefore: if true, it's a content of line
 * after which newText should be inserted.
 */
function detectPureInsertion(edit: CoalescedEditInfo): {
    isPureInsertion: boolean;
    lineBefore: string;
} {
    if (edit.oldText.length === 0) {
        return { isPureInsertion: false, lineBefore: "" };
    }
    const lines = edit.oldText.split("\n");
    let lastLine = lines[lines.length - 1];

    const prefix = "PURE INSERTION AFTER LINE:";

    if (lastLine.startsWith(prefix)) {
        const lineBefore = lastLine.substring(prefix.length);
        return { isPureInsertion: true, lineBefore: lineBefore };
    }

    return { isPureInsertion: false, lineBefore: "" };
}

/**
 * Finishes the current edit operation and yields the final chunks.
 *
 * @param currEditDict - The current edit information.
 * @param fullEdits - An array to store the completed edit operations.
 * @yields {DiffViewDiffStreamChunkData} Chunks representing the final parts of the edit.
 */
function* finishEdit(
    currEditDict: InferrableEditInfo & CoalescedEditInfo,
    fullEdits: IMonacoEditOp[],
    code: string
): Generator<DiffViewDiffStreamChunkData> {
    let toYield = getYieldableChunk(currEditDict, code, 3, true);
    if (toYield.length > 0) {
        yield {
            chunkContinue: {
                newText: toYield,
            },
        };
    }

    const fullEdit = toFullEdit(currEditDict);
    const offset = computeRunningOffset(fullEdit, fullEdits);
    const offsetEdit = applyOffset(fullEdit, offset);

    yield {
        chunkEnd: {
            originalStartLine: currEditDict.startLineNumber,
            originalEndLine: currEditDict.endLineNumber,
            stagedStartLine: offsetEdit.lineChange.originalStartLineNumber,
            stagedEndLine: offsetEdit.lineChange.originalEndLineNumber,
        },
    };

    fullEdits.push(narrowDownFullEdit(fullEdit, code));
}

/**
 * Narrows down a full edit operation by removing unchanged lines at the beginning and end.
 *
 * This function takes a full edit operation and the original code, then attempts to
 * reduce the scope of the edit by identifying and excluding unchanged lines at the
 * start and end of the edit range. This process helps to focus the edit on only the
 * actually changed lines, which in turn reduces the chance of overlapping changes
 * when multiple edits are applied sequentially.
 *
 * @param fullEdit - The original edit operation to be narrowed down.
 * @param code - The full content of the file being edited.
 * @returns A new IMonacoEditOp with a potentially reduced scope, focusing only on the changed lines.
 */
export function narrowDownFullEdit(fullEdit: IMonacoEditOp, code: string): IMonacoEditOp {
    const codeLines = code.split(/(?<=\n)/);
    let lineChange = {
        originalStartLineNumber: fullEdit.lineChange.originalStartLineNumber,
        originalEndLineNumber: fullEdit.lineChange.originalEndLineNumber,
        modifiedStartLineNumber: fullEdit.lineChange.modifiedStartLineNumber,
        modifiedEndLineNumber: fullEdit.lineChange.modifiedEndLineNumber,
    };
    let newTextLines = fullEdit.newText.split(/(?<=\n)/);
    while (
        lineChange.originalStartLineNumber < lineChange.originalEndLineNumber &&
        newTextLines.length > 0
    ) {
        let curLine = newTextLines[0];
        if (codeLines[lineChange.originalStartLineNumber - 1] !== curLine) {
            break;
        }
        lineChange.originalStartLineNumber++;
        lineChange.modifiedStartLineNumber++;
        newTextLines = newTextLines.slice(1);
    }
    while (
        lineChange.originalStartLineNumber < lineChange.originalEndLineNumber &&
        newTextLines.length > 0
    ) {
        let curLine = newTextLines[newTextLines.length - 1];
        if (codeLines[lineChange.originalEndLineNumber - 2] !== curLine) {
            break;
        }
        lineChange.originalEndLineNumber--;
        lineChange.modifiedEndLineNumber--;
        newTextLines = newTextLines.slice(0, -1);
    }

    return {
        lineChange: {
            originalStartLineNumber: lineChange.originalStartLineNumber,
            originalEndLineNumber: lineChange.originalEndLineNumber,
            modifiedStartLineNumber: lineChange.modifiedStartLineNumber,
            modifiedEndLineNumber: lineChange.modifiedEndLineNumber,
        },
        newText: newTextLines.join(""),
    };
}

export async function* toEditStream(
    code: string,
    instructionStream: AsyncGenerator<ChatInstructionStreamResult>
): AsyncGenerator<DiffViewDiffStreamChunkData> {
    let fullEdits: IMonacoEditOp[] = [];
    let currEditDict: CoalescedEditInfo = newCoalescedEditInfo();
    for await (const instructionChunk of splitStream(instructionStream)) {
        let { text, replacementStartLine, replacementOldText } = instructionChunk;
        // Means we have a new edit, so we push the previous one to array and reset currentEdit
        if (
            // We have a new edit, and our old edit is ready. This means we're at a boundary
            (!isNil(replacementStartLine) || !isNil(replacementOldText) || !isNil(text)) &&
            canConvertToMonacoEditOp(currEditDict)
        ) {
            yield* finishEdit(currEditDict, fullEdits, code);
            currEditDict = newCoalescedEditInfo();
        }

        currEditDict = coalesceChunk(currEditDict, instructionChunk);
        possiblyRefineStartEndLines(currEditDict, code);

        // If currentEdit has both lines set, we could start yielding (together with all previous edits)
        if (canYieldNewChunk(currEditDict)) {
            const fullEdit = toFullEdit(currEditDict);
            const offset = computeRunningOffset(fullEdit, fullEdits);
            const offsetEdit = applyOffset(fullEdit, offset);
            yield {
                newChunkStart: {
                    originalStartLine: currEditDict.startLineNumber,
                    stagedStartLine: offsetEdit.lineChange.originalStartLineNumber,
                },
            };
            currEditDict.hasYieldedNewChunk = true;
        }
        // Yield all the replacement text options
        if (!isNil(instructionChunk.replacementText)) {
            currEditDict.newTextBuffer += instructionChunk.replacementText;
            yield {
                chunkContinue: {
                    // newText: instructionChunk.replacementText,
                    newText: getYieldableChunk(currEditDict, code, 3, false),
                },
            };
        }
    }
    if (canConvertToMonacoEditOp(currEditDict)) {
        yield* finishEdit(currEditDict, fullEdits, code);
    }
}

async function* splitStream(
    instructionStream: AsyncGenerator<ChatInstructionStreamResult>
): AsyncGenerator<ChatInstructionStreamResult> {
    for await (const chunk of instructionStream) {
        if (!isNil(chunk.replacementOldText)) {
            yield { text: "", replacementOldText: chunk.replacementOldText };
        }
        if (!isNil(chunk.replacementStartLine)) {
            yield { text: "", replacementStartLine: chunk.replacementStartLine };
        }
        if (!isNil(chunk.replacementText)) {
            yield { text: "", replacementText: chunk.replacementText };
        }
        if (!isNil(chunk.replacementEndLine)) {
            yield {
                text: "",
                replacementEndLine: chunk.replacementEndLine,
            };
        }
    }
}

/**
 * Applies a single edit operation to an array of lines.
 * @param lines - The original array of lines to be edited.
 * @param edit - The edit operation to be applied.
 * @returns A new array of lines with the edit applied.
 */
export function applyEditToLines(lines: string[], edit: IMonacoEditOp): string[] {
    const editTextLines = edit.newText ? edit.newText.replaceAll(/\n$/g, "").split("\n") : [];
    const startLine = edit.lineChange.originalStartLineNumber - 1; // 1-based to 0-based
    // [inclusive, exclusive)
    const numLines =
        edit.lineChange.originalEndLineNumber - edit.lineChange.originalStartLineNumber;
    lines.splice(startLine, numLines, ...editTextLines);
    return lines;
}

/**
 * Applies an offset to a given edit operation.
 * @param currEdit - The current edit operation.
 * @param offset - The line offset to be applied.
 * @returns A new edit operation with the offset applied.
 */
export function applyOffset(currEdit: IMonacoEditOp, offset: number): IMonacoEditOp {
    return {
        lineChange: {
            originalStartLineNumber: currEdit.lineChange.originalStartLineNumber + offset,
            originalEndLineNumber: currEdit.lineChange.originalEndLineNumber + offset,
            modifiedStartLineNumber: currEdit.lineChange.modifiedStartLineNumber + offset,
            modifiedEndLineNumber: currEdit.lineChange.modifiedEndLineNumber + offset,
        },
        newText: currEdit.newText,
    };
}

/**
 * Computes the running offset for a current edit based on previous edits.
 * @param currEdit - The current edit operation.
 * @param prevEdits - An array of previous edit operations.
 * @returns The total offset to be applied to the current edit.
 */
export function computeRunningOffset(currEdit: IMonacoEditOp, prevEdits: IMonacoEditOp[]): number {
    let offset = 0;
    for (const prevEdit of prevEdits) {
        offset += computeOffset(currEdit, prevEdit);
    }
    return offset;
}

/**
 * Computes the offset between two edit operations.
 * @param currEdit - The current edit operation.
 * @param prevEdit - A previous edit operation.
 * @returns The offset between the two edits.
 * @throws Error if the edits overlap.
 */
export function computeOffset(currEdit: IMonacoEditOp, prevEdit: IMonacoEditOp): number {
    // If the current edit exists entirely before the previous edit, then we do not need any offset
    if (isBefore(currEdit, prevEdit)) {
        return 0;
        // If the previous edit exists entirely before the current edit, then we need to offset the current edit
    } else if (isBefore(prevEdit, currEdit)) {
        // [inclusive, exclusive)
        const prevOriginalLineCount =
            prevEdit.lineChange.originalEndLineNumber - prevEdit.lineChange.originalStartLineNumber;
        // [inclusive, exclusive)
        const prevModifiedLineCount: number =
            prevEdit.lineChange.modifiedEndLineNumber - prevEdit.lineChange.modifiedStartLineNumber;
        return prevModifiedLineCount - prevOriginalLineCount;
    } else {
        throw new Error("Edits overlap, which is not supported");
    }
}

/**
 * Checks if a MonacoEditOp is entirely before another.
 * @param currEdit - The current edit operation.
 * @param prevEdit - A previous edit operation.
 * @returns True if currEdit is entirely before prevEdit, false otherwise.
 */
function isBefore(currEdit: IMonacoEditOp, prevEdit: IMonacoEditOp): boolean {
    return currEdit.lineChange.originalEndLineNumber <= prevEdit.lineChange.originalStartLineNumber;
}
