import type { IEditSuggestion } from "../next-edit/next-edit-types";

export function trimTrailingNewline(str: string): string {
    if (str.endsWith("\n")) {
        return str.slice(0, -1);
    }
    return str;
}

export function applySuggestions(fileContent: string, suggestions: IEditSuggestion[]) {
    const lines = fileContent.split("\n");
    // Apply from the bottom of the file so the line numbers don't change.
    for (const suggestion of [...suggestions].sort(
        ({ result: { charEnd: a } }, { result: { charEnd: b } }) => b - a
    )) {
        lines.splice(
            suggestion.lineRange.start,
            suggestion.lineRange.stop - suggestion.lineRange.start,
            // This can be a multiline string, its fine, its all newlines.
            trimTrailingNewline(suggestion.result.suggestedCode)
        );
    }

    return lines.join("\n");
}
