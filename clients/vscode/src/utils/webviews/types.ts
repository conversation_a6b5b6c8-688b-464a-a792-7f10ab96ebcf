import { type WebViewMessage as SidecarWebViewMessage } from "@augment-internal/sidecar-libs/src/webview-messages/common-webview-messages";
import { type MessageConsumerTypes } from "@augment-internal/sidecar-libs/src/webview-messages/webview-messaging";

import {
    type AsyncWebViewMessage,
    type WebViewMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";

export interface SendFnT<ResT extends AsyncWebViewMessage<WebViewMessageTypes>> {
    (msg: ResT): void;
}

export interface RecvFnRegistrar<ReqT extends AsyncWebViewMessage<WebViewMessageTypes>> {
    (handler: (msg: ReqT) => void): () => void;
}

export interface IAsyncMsgHandler {
    registerHandler: <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => Promise<ResT>
    ) => void;

    dispose: () => void;
}

export type WebViewMessageTypes = WebViewMessage | SidecarWebViewMessage<MessageConsumerTypes>;
