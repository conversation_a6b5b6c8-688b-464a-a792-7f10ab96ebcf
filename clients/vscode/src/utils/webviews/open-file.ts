import { fuzzyLocateSnippet } from "@augment-internal/sidecar-libs/src/utils/locate-snippet";
import { resolve } from "path";
import * as vscode from "vscode";

import { FileDetails } from "../../webview-providers/webview-messages";

export type LineRange = {
    start: number;
    stop: number;
};

export async function openFileFromMessage(
    data: FileDetails,
    curViewColumn: vscode.ViewColumn | undefined = undefined
) {
    // Resolve will handle the case where the pathName is an absolute path
    const fullPath = resolve(data.repoRoot, data.pathName);
    if (data.openLocalUri) {
        const isRemoteWorkspace = vscode.env.remoteName !== undefined;
        if (isRemoteWorkspace) {
            // In a remote workspace, we need to use the vscode-local: scheme to open local files
            const document = await vscode.workspace.openTextDocument(
                vscode.Uri.parse(`vscode-local:${fullPath}`)
            );
            await vscode.window.showTextDocument(document);
            return;
        }
    }
    if (data.differentTab) {
        if (curViewColumn === undefined || vscode.window.tabGroups.all.length === 1) {
            // If we cannot find the current view column or there is only one, use a new tab.
            await vscode.commands.executeCommand(
                "vscode.openWith",
                vscode.Uri.file(fullPath),
                "default",
                vscode.ViewColumn.Beside
            );
        } else {
            // Otherwise, use the column to the left (or the right if we're in the leftmost).
            await vscode.commands.executeCommand(
                "vscode.openWith",
                vscode.Uri.file(fullPath),
                "default",
                curViewColumn === vscode.ViewColumn.One ? curViewColumn + 1 : curViewColumn - 1
            );
        }
    } else if (data.openTextDocument) {
        const document = await vscode.workspace.openTextDocument(fullPath);
        await vscode.window.showTextDocument(document, vscode.ViewColumn.Active);
    } else {
        await vscode.commands.executeCommand("vscode.open", vscode.Uri.file(fullPath));
    }

    let range: vscode.Range | undefined = undefined;
    if (
        !vscode.window.activeTextEditor ||
        vscode.window.activeTextEditor.document.uri.fsPath !== fullPath
    ) {
        return;
    }

    if (data.fullRange) {
        const lrange = data.fullRange;
        // We do not subtract 1 here, we use 0-indexing because these are full editor ranges
        range = vscode.window.activeTextEditor.document.validateRange(
            new vscode.Range(
                lrange.startLineNumber,
                lrange.startColumn,
                lrange.endLineNumber,
                lrange.endColumn
            )
        );
    } else if (data.range) {
        const lrange = data.range;
        // We subtract 1 from the end because VSCode's ranges are inclusive.
        range = vscode.window.activeTextEditor.document.validateRange(
            new vscode.Range(lrange.start - 1, 0, lrange.stop - 1, 999)
        );
    } else if (data.snippet) {
        // openTextDocument OK: we opened this document in an editor above
        const document = await vscode.workspace.openTextDocument(fullPath);
        const fileContent = document.getText();
        const lrange = fuzzyLocateSnippet(fileContent, data.snippet);
        if (lrange === null) {
            // Snippet not found, let's reset the selected region to avoid confusion.
            // To this end, we set the selection to the current cursor position.
            const currentCursorPosition = vscode.window.activeTextEditor.selection.active;
            range = vscode.window.activeTextEditor.document.validateRange(
                new vscode.Range(currentCursorPosition, currentCursorPosition)
            );
        } else {
            range = vscode.window.activeTextEditor.document.validateRange(
                new vscode.Range(lrange.start, 0, lrange.end, 999 /* end of the line */)
            );
        }
    } else {
        return;
    }

    vscode.window.activeTextEditor?.revealRange(range);
    // Set the cursor to the beginning of the range.
    vscode.window.activeTextEditor.selection = new vscode.Selection(range.end, range.start);
}
