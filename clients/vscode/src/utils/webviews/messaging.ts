import { WorkTimer } from "$vscode/src/metrics/work-timer";

import type { WebViewMessage as SidecarWebViewMessage } from "@augment-internal/sidecar-libs/src/webview-messages/common-webview-messages";
import { MessageConsumerTypes } from "@augment-internal/sidecar-libs/src/webview-messages/webview-messaging";
import * as crypto from "crypto";

import {
    type AsyncStreamCtx,
    type AsyncWebViewMessage,
    type WebViewMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";
import type { IAsyncMsgHandler, RecvFnRegistrar, SendFnT, WebViewMessageTypes } from "./types";

/**
 * Data Flow for Async Messages:
 *
 * **API Consumer:**
 * 1. The API consumer sends a message to the AsyncMsgSender.
 *
 * **AsyncMsgSender:**
 * 1. The AsyncMsgSender wraps the message in an async wrapper message with a unique request ID.
 * 2. The AsyncMsgSender stores the promise context (resolve/reject functions) for the request ID.
 * 3. The AsyncMsgSender sends the wrapped async message to the AsyncMsgHandler.
 *
 * **AsyncMsgHandler:**
 * 1. The AsyncMsgHandler receives the wrapped async message and unwraps it to get the original message.
 * 2. The AsyncMsgHandler processes the original message and generates a response.
 * 3. The AsyncMsgHandler wraps the response in an async wrapper message with the same request ID.
 * 4. The AsyncMsgHandler sends the wrapped response back to the AsyncMsgSender.
 *
 * **AsyncMsgSender:**
 * 1. The AsyncMsgSender receives the wrapped response and unwraps it to get the original response.
 * 2. The AsyncMsgSender resolves or rejects the promise context with the original response.
 *
 * **API Consumer:**
 * 1. The API consumer receives the response from the AsyncMsgSender and the promise context is resolved or rejected.
 */

/**
 * Wraps a response message in an async wrapper message.
 *
 * @param requestId The request ID to associate with the response.
 * @param response The response message to wrap.
 * @returns The wrapped async message.
 */
export function wrapResponse<ResT extends WebViewMessageTypes>(
    requestId: string,
    response: ResT | null = null,
    error: string | null = null
): AsyncWebViewMessage<ResT> {
    if (!response && !error) {
        throw new Error("Must provide either a response or an error");
    } else if (response && error) {
        throw new Error("Must provide either a response or an error, not both");
    }
    return {
        type: WebViewMessageType.asyncWrapper,
        requestId,
        error,
        baseMsg: response,
    };
}

/**
 * AsyncMsgHandler is a class that enables handling asynchronous messages from a webview.
 * It provides a way to register handlers for specific message types and send responses back to the webview.
 */
export class AsyncMsgHandler implements IAsyncMsgHandler {
    /**
     * An array to store disposers for each registered handler.
     */
    private _disposers = new Array<() => void>();

    /**
     * Constructs an AsyncMsgHandler instance.
     *
     * @param _postMessage A function to post messages to the webview.
     * @param _addMsgHandler A function to register a message handler for the webview.
     */
    constructor(
        private _postMessage: SendFnT<AsyncWebViewMessage<WebViewMessageTypes>>,
        private _addMsgHandler: RecvFnRegistrar<AsyncWebViewMessage<WebViewMessageTypes>>,
        private _workTimer: WorkTimer | undefined = undefined
    ) {}

    /**
     * Creates a wrapped handler function that handles the following:
     * - Type checks and unwraps incoming async messages
     * - Calls the handler with the unwrapped message
     * - Wraps the response in an async message and sends it
     *
     * @param type The type of message to handle.
     * @param handler The handler function to call with the unwrapped message.
     * @returns A wrapped handler function.
     */
    private createWrappedHandler = <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => ResT | Error | Promise<ResT | Error>,
        logIfSlow: boolean = true
    ): ((msg: AsyncWebViewMessage<WebViewMessage>) => boolean) => {
        return (msg: AsyncWebViewMessage<WebViewMessage>): boolean => {
            // Make sure `msg` is an async wrapper message around the type we are trying to handle
            if (msg.type !== WebViewMessageType.asyncWrapper || msg.baseMsg?.type !== type) {
                return false;
            }

            const handleResponse = async (r: ResT | Error | Promise<ResT | Error>) => {
                try {
                    let response: ResT | Error;
                    if (this._workTimer && logIfSlow) {
                        response = await this._workTimer.runTimed(type, async () => await r);
                    } else {
                        response = await r;
                    }
                    if (response instanceof Error) {
                        throw response;
                    }
                    const wrappedResponse = wrapResponse(msg.requestId, response);
                    this._postMessage(wrappedResponse);
                } catch (e) {
                    if (e instanceof Error) {
                        const wrappedResponse = wrapResponse<WebViewMessage>(
                            msg.requestId,
                            null,
                            e.message
                        );
                        this._postMessage(wrappedResponse);
                    }
                }
            };

            // Call the handler in the background
            void handleResponse(handler(msg.baseMsg as ReqT));

            return true;
        };
    };

    /**
     * Registers a handler for a specific type of message.
     *
     * @param type The type of message to handle.
     * @param handler The handler function to call with the unwrapped message.
     */
    registerHandler = <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => ResT | Error | Promise<ResT | Error>,
        logIfSlow: boolean = true
    ): (() => void) => {
        const wrappedHandler = this.createWrappedHandler(type, handler, logIfSlow);
        const disposer = this._addMsgHandler(
            wrappedHandler as (msg: AsyncWebViewMessage<WebViewMessageTypes>) => boolean
        );
        this._disposers.push(disposer);
        return () => {
            this._disposers = this._disposers.filter((d) => d !== disposer);
            disposer();
        };
    };

    /**
     * Registers a handler for a specific type of message.
     *
     * @param type The type of message to handle.
     * @param handler The handler function to call with the unwrapped message.
     */
    registerSidecarHandler = <ReqT extends SidecarWebViewMessage<MessageConsumerTypes>>(
        handler: (msg: ReqT, postMessage: (msg: unknown) => void) => void
    ): void => {
        const wrappedHandler = this.createWrappedSidecarHandler(handler);
        const disposer = this._addMsgHandler(
            wrappedHandler as (msg: AsyncWebViewMessage<WebViewMessageTypes>) => boolean
        );
        this._disposers.push(disposer);
    };

    /**
     * Creates a wrapped handler function that handles the following:
     * - Type checks and unwraps incoming async messages
     * - Calls the handler with the unwrapped message
     * - Wraps the response in an async message and sends it
     *
     * @param handler The handler function to call with the unwrapped message.
     * @returns A wrapped handler function.
     */
    private createWrappedSidecarHandler = <
        ReqT extends SidecarWebViewMessage<MessageConsumerTypes>,
    >(
        handler: (msg: ReqT, postMessage: (msg: unknown) => void) => void
    ): ((msg: AsyncWebViewMessage<ReqT>) => void) => {
        return (msg: AsyncWebViewMessage<ReqT>): void => {
            // Make sure `msg` is an async wrapper message around the type we are trying to handle
            if (msg.type !== WebViewMessageType.asyncWrapper) {
                return;
            }
            // Ensure the destination is the sidecar
            if (msg.destination !== "sidecar") {
                return;
            }

            const wrappedPostMessage = (response: unknown) => {
                const wrappedResponse = wrapResponse<SidecarWebViewMessage<MessageConsumerTypes>>(
                    msg.requestId,
                    response as SidecarWebViewMessage<MessageConsumerTypes>
                );
                this._postMessage(wrappedResponse);
            };

            try {
                handler(msg.baseMsg as ReqT, wrappedPostMessage);
            } catch (e) {
                if (e instanceof Error) {
                    const wrappedResponse = wrapResponse<
                        SidecarWebViewMessage<MessageConsumerTypes>
                    >(msg.requestId, null, e.message);
                    this._postMessage(wrappedResponse);
                }
            }
            return;
        };
    };

    /**
     * Registers a handler for a specific type of message that returns an async generator.
     *
     * @param type The type of message to handle.
     * @param handler The handler function to call with the unwrapped message.
     */
    registerStreamHandler = <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => AsyncGenerator<ResT | Error>
    ): void => {
        const wrappedHandler = this.createWrappedStreamHandler<ReqT, ResT>(type, handler);
        const disposer = this._addMsgHandler(
            wrappedHandler as (msg: AsyncWebViewMessage<WebViewMessageTypes>) => boolean
        );
        this._disposers.push(disposer);
    };

    /**
     * Creates a wrapped handler function that handles the following:
     * - Type checks and unwraps incoming async messages
     * - Calls the handler with the unwrapped message and gets an async generator
     * - Wraps the responses in async messages and sends them
     *
     * @param type The type of message to handle.
     * @param handler The handler function to call with the unwrapped message.
     * @returns A wrapped handler function.
     */
    private createWrappedStreamHandler = <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => AsyncGenerator<ResT | Error>
    ): ((msg: AsyncWebViewMessage<WebViewMessage>) => boolean) => {
        return (msg: AsyncWebViewMessage<WebViewMessage>): boolean => {
            // Make sure `msg` is an async wrapper message around the type we are trying to handle
            if (msg.type !== WebViewMessageType.asyncWrapper || msg.baseMsg?.type !== type) {
                return false;
            }

            // Iterate through the async generator and send each response. Close the stream with
            // another message at the very end with isStreamComplete set to true.
            void (async () => {
                // This is the request ID of the message that is being worked on currently
                let requestId = msg.requestId;
                // Shared state across the sender and receiver
                // that allows them to synchronize the stream
                let streamCtx: AsyncStreamCtx = {
                    streamMsgIdx: 0,
                    streamNextRequestId: `${msg.requestId}-0`,
                };
                try {
                    for await (const res of this._workTimer
                        ? await this._workTimer.runTimed(type, () => handler(msg.baseMsg as ReqT))
                        : handler(msg.baseMsg as ReqT)) {
                        if (res instanceof Error) {
                            throw res;
                        }

                        // Create response + add stream headers
                        const wrappedResponse = wrapResponse<ResT>(requestId, res);
                        wrappedResponse.streamCtx = { ...streamCtx };

                        // Turn the next request to current WIP request
                        requestId = streamCtx.streamNextRequestId;
                        // Update stream context to WIP request
                        const nextIdx = streamCtx.streamMsgIdx + 1;
                        streamCtx = {
                            streamMsgIdx: nextIdx,
                            streamNextRequestId: `${msg.requestId}-${nextIdx}`,
                        };

                        // streamCtx now represents the next message to be sent,
                        // so we send the current message and end the loop
                        this._postMessage(wrappedResponse);
                    }

                    // End stream message
                    this._postMessage({
                        type: WebViewMessageType.asyncWrapper,
                        requestId,
                        error: null,
                        baseMsg: { type: WebViewMessageType.empty },
                        streamCtx: { ...streamCtx, isStreamComplete: true },
                    });
                } catch (e: any) {
                    // If an error is encountered at any point, close the stream with
                    // the error message
                    const err = e as Error;
                    this._postMessage({
                        type: WebViewMessageType.asyncWrapper,
                        requestId,
                        error: err.message,
                        baseMsg: null,
                        streamCtx: { ...streamCtx, isStreamComplete: true },
                    });
                }
            })();

            return true;
        };
    };

    /**
     * Disposes of all registered handlers.
     */
    dispose = (): void => {
        for (const disposer of this._disposers) {
            disposer();
        }
    };

    /**
     * Posts a message to the webview.
     * @param message The message to post
     */
    public postMessage(message: WebViewMessage): void {
        const wrappedMessage = wrapResponse(crypto.randomUUID(), message);
        this._postMessage(wrappedMessage);
    }
}
