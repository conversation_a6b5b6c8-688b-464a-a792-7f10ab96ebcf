import * as vscode from "vscode";

import { API, GitExtension } from "../../../../../../third_party/vscode-git/git";
import { openDiffInBuffer } from "../open-diff-in-buffer";

// Mock fs-utils
jest.mock("../../fs-utils", () => ({
    fileExists: jest.fn(),
}));

// Mock VS Code APIs
jest.mock("vscode", () => ({
    /* eslint-disable @typescript-eslint/naming-convention */
    workspace: {
        registerTextDocumentContentProvider: jest.fn(() => ({ dispose: jest.fn() })),
        openTextDocument: jest.fn(() => Promise.resolve({ getText: () => "mock content" })),
        asRelativePath: jest.fn((path: string) => path.replace(/^.*\//, "")), // Mock to return just filename
    },
    commands: {
        executeCommand: jest.fn(() => Promise.resolve()),
    },
    Uri: {
        parse: jest.fn((uri: string) => ({ toString: () => uri })),
        file: jest.fn((path: string) => ({ fsPath: path, toString: () => `file://${path}` })),
    },
    Range: jest.fn((start, end) => ({ start, end })),
    Position: jest.fn((line, character) => ({ line, character })),
    window: {
        tabGroups: {
            all: [],
            close: jest.fn(),
            onDidChangeTabs: jest.fn(() => ({ dispose: jest.fn() })),
        },
    },
    extensions: {
        getExtension: jest.fn(),
    },
    /* eslint-enable @typescript-eslint/naming-convention */
}));

// Mock file system utilities
jest.mock("../../fs-utils", () => ({
    fileExists: jest.fn(),
    readFileUtf8: jest.fn(),
}));

describe("openDiffInBuffer - Git Extension Inspired Implementation", () => {
    const { fileExists, readFileUtf8 } = require("../../fs-utils");

    beforeEach(() => {
        jest.clearAllMocks();
        // Set default mock return values
        readFileUtf8.mockResolvedValue("mock file content");

        // Reset tab groups mock
        (vscode.window.tabGroups as any).all = [];
    });

    it("should use editable diff when file exists but Git validation fails", async () => {
        // Setup: file exists but Git extension is not available
        fileExists.mockReturnValue(true);
        jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(undefined);

        const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");
        const registerProviderSpy = jest.spyOn(
            vscode.workspace,
            "registerTextDocumentContentProvider"
        );

        await openDiffInBuffer("console.log('old');", "console.log('hello');", "/path/to/file.js");

        // Should register a virtual document provider for the "before" content
        expect(registerProviderSpy).toHaveBeenCalledWith("augment-diff", expect.any(Object));

        // Should open editable diff with virtual URI for left side and actual file URI for right side
        const [command, leftUri, rightUri, title, options] = executeCommandSpy.mock.calls[0];
        expect(command).toBe("vscode.diff");
        expect(leftUri.toString()).toMatch(/^augment-diff:/); // Virtual URI scheme
        expect(rightUri.fsPath).toBe("/path/to/file.js"); // Actual file URI for editing
        expect(title).toBe("Diff - /path/to/file.js");
        expect(options).toBeUndefined();
    });

    it("should use Git diff with staging capabilities when changes match Git state", async () => {
        // Setup: file exists and we have a Git extension available
        fileExists.mockReturnValue(true);

        // Mock current file content to match newContents
        readFileUtf8.mockResolvedValue("console.log('hello');");

        // Mock workspace.openTextDocument to return the HEAD content
        const mockTextDocument = {
            getText: jest.fn().mockReturnValue("console.log('old');"), // HEAD content matches oldContents
        };
        jest.spyOn(vscode.workspace, "openTextDocument").mockResolvedValue(mockTextDocument as any);

        // Mock Git extension with getGitFileContent functionality
        const mockRepo = {
            rootUri: vscode.Uri.file("/test"),
            inputBox: { value: "" },
            state: {} as any,
            ui: {} as any,
            show: jest.fn().mockResolvedValue("console.log('old');"), // HEAD content matches oldContents
            toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
        };

        const mockGitApi: Partial<API> = {
            getRepository: jest.fn(() => mockRepo as any),
            toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
        };

        const mockGitExtension: Partial<vscode.Extension<GitExtension>> = {
            isActive: true,
            exports: {
                enabled: true,
                onDidChangeEnablement: {} as any,
                getAPI: jest.fn(() => mockGitApi as API),
            } as GitExtension,
        };

        jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(
            mockGitExtension as vscode.Extension<GitExtension>
        );

        const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

        await openDiffInBuffer("console.log('old');", "console.log('hello');", "/path/to/file.js");

        // Should use git.openChange for native Git diff experience
        expect(executeCommandSpy).toHaveBeenCalledWith(
            "git.openChange",
            expect.objectContaining({ fsPath: "/path/to/file.js" }) // File URI
        );
    });

    it("should use read-only diff when file doesn't exist", async () => {
        // Setup: file doesn't exist
        fileExists.mockReturnValue(false);

        const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");
        const registerProviderSpy = jest.spyOn(
            vscode.workspace,
            "registerTextDocumentContentProvider"
        );

        await openDiffInBuffer(
            "console.log('old');",
            "console.log('hello');",
            "/path/to/nonexistent.js"
        );

        // Should register virtual document provider for both sides (read-only)
        expect(registerProviderSpy).toHaveBeenCalledWith("virtual", expect.any(Object));

        // Should use read-only diff with both sides as virtual documents
        const [command, leftUri, rightUri, title, options] = executeCommandSpy.mock.calls[0];
        expect(command).toBe("vscode.diff");
        expect(leftUri.toString()).toMatch(/^virtual:/); // Virtual URI for old content
        expect(rightUri.toString()).toMatch(/^virtual:/); // Virtual URI for new content
        expect(title).toBe("Diff - /path/to/nonexistent.js");
        expect(options).toBeUndefined();
    });

    it("should use Git diff for new files when Git HEAD content is unavailable", async () => {
        // Setup: file exists but Git HEAD content cannot be retrieved (new file scenario)
        fileExists.mockReturnValue(true);

        // Mock current file content to match newContents
        readFileUtf8.mockResolvedValue("console.log('hello world');");

        // Mock workspace.openTextDocument to fail for HEAD (new file not in Git yet)
        jest.spyOn(vscode.workspace, "openTextDocument").mockRejectedValue(
            new Error("File not found in HEAD")
        );

        // Mock Git extension
        const mockRepo = {
            rootUri: vscode.Uri.file("/test"),
            inputBox: { value: "" },
            state: {} as any,
            ui: {} as any,
            toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
        };

        const mockGitApi: Partial<API> = {
            getRepository: jest.fn(() => mockRepo as any),
            toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
        };

        const mockGitExtension: Partial<vscode.Extension<GitExtension>> = {
            isActive: true,
            exports: {
                enabled: true,
                onDidChangeEnablement: {} as any,
                getAPI: jest.fn(() => mockGitApi as API),
            } as GitExtension,
        };

        jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(
            mockGitExtension as vscode.Extension<GitExtension>
        );

        const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

        await openDiffInBuffer(
            "", // Empty oldContents - new file scenario
            "console.log('hello world');",
            "/path/to/file.js"
        );

        // Should use Git diff for new files (empty oldContents, file doesn't exist in HEAD, newContents matches disk)
        expect(executeCommandSpy).toHaveBeenCalledWith(
            "git.openChange",
            expect.objectContaining({ fsPath: "/path/to/file.js" }) // File URI
        );
    });

    it("should use Git diff for deleted files when file exists in HEAD but not on disk", async () => {
        // Setup: file doesn't exist on disk but exists in Git HEAD (deleted file scenario)
        fileExists.mockReturnValue(false);

        // Mock readFileUtf8 to throw error (file doesn't exist on disk)
        readFileUtf8.mockRejectedValue(new Error("File not found"));

        // Mock workspace.openTextDocument to return HEAD content
        const mockDocument = {
            getText: jest.fn(() => "console.log('old content');"),
        };
        jest.spyOn(vscode.workspace, "openTextDocument").mockResolvedValue(mockDocument as any);

        // Mock Git extension
        const mockRepo = {
            rootUri: vscode.Uri.file("/test"),
            inputBox: { value: "" },
            state: {} as any,
            ui: {} as any,
            toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
        };

        const mockGitApi: Partial<API> = {
            getRepository: jest.fn(() => mockRepo as any),
            toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
        };

        const mockGitExtension: Partial<vscode.Extension<GitExtension>> = {
            isActive: true,
            exports: {
                enabled: true,
                onDidChangeEnablement: {} as any,
                getAPI: jest.fn(() => mockGitApi as API),
            } as GitExtension,
        };

        jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(
            mockGitExtension as vscode.Extension<GitExtension>
        );

        const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

        await openDiffInBuffer(
            "console.log('old content');", // oldContents matches HEAD
            "", // Empty newContents - deleted file scenario
            "/path/to/file.js"
        );

        // Should use Git diff for deleted files (oldContents matches HEAD, file doesn't exist on disk, empty newContents)
        expect(executeCommandSpy).toHaveBeenCalledWith(
            "git.openChange",
            expect.objectContaining({ fsPath: "/path/to/file.js" }) // File URI
        );
    });

    describe("Edge cases and error handling", () => {
        it("should handle Git extension not available", async () => {
            fileExists.mockReturnValue(true);
            jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(undefined);

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            // Should fall back to editable diff since Git extension is not available
            expect(executeCommandSpy).toHaveBeenCalledWith(
                "vscode.diff",
                expect.any(Object),
                expect.objectContaining({ fsPath: "/path/to/file.js" }),
                "Diff - /path/to/file.js",
                undefined
            );
        });

        it("should handle Git API returning null", async () => {
            fileExists.mockReturnValue(true);

            const mockGitExtension: Partial<vscode.Extension<GitExtension>> = {
                isActive: true,
                exports: {
                    enabled: true,
                    onDidChangeEnablement: {} as any,
                    getAPI: jest.fn(() => null), // Return null API
                } as unknown as GitExtension,
            };

            jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(
                mockGitExtension as vscode.Extension<GitExtension>
            );

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            // Should fall back to editable diff
            expect(executeCommandSpy).toHaveBeenCalledWith(
                "vscode.diff",
                expect.any(Object),
                expect.objectContaining({ fsPath: "/path/to/file.js" }),
                "Diff - /path/to/file.js",
                undefined
            );
        });

        it("should handle no Git repository found", async () => {
            fileExists.mockReturnValue(true);
            readFileUtf8.mockResolvedValue("console.log('hello');");

            const mockGitApi: Partial<API> = {
                getRepository: jest.fn(() => null), // No repository found
                toGitUri: jest.fn(),
            };

            const mockGitExtension: Partial<vscode.Extension<GitExtension>> = {
                isActive: true,
                exports: {
                    enabled: true,
                    onDidChangeEnablement: {} as any,
                    getAPI: jest.fn(() => mockGitApi as API),
                } as GitExtension,
            };

            jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(
                mockGitExtension as vscode.Extension<GitExtension>
            );

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            // Should fall back to editable diff
            expect(executeCommandSpy).toHaveBeenCalledWith(
                "vscode.diff",
                expect.any(Object),
                expect.objectContaining({ fsPath: "/path/to/file.js" }),
                "Diff - /path/to/file.js",
                undefined
            );
        });

        it("should handle file content mismatch", async () => {
            fileExists.mockReturnValue(true);
            readFileUtf8.mockResolvedValue("different content"); // Doesn't match newContents

            const mockRepo = {
                rootUri: vscode.Uri.file("/test"),
                inputBox: { value: "" },
                state: {} as any,
                ui: {} as any,
                toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
            };

            const mockGitApi: Partial<API> = {
                getRepository: jest.fn(() => mockRepo as any),
                toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
            };

            const mockGitExtension: Partial<vscode.Extension<GitExtension>> = {
                isActive: true,
                exports: {
                    enabled: true,
                    onDidChangeEnablement: {} as any,
                    getAPI: jest.fn(() => mockGitApi as API),
                } as GitExtension,
            };

            jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(
                mockGitExtension as vscode.Extension<GitExtension>
            );

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');", // This doesn't match the mocked file content
                "/path/to/file.js"
            );

            // Should fall back to editable diff due to content mismatch
            expect(executeCommandSpy).toHaveBeenCalledWith(
                "vscode.diff",
                expect.any(Object),
                expect.objectContaining({ fsPath: "/path/to/file.js" }),
                "Diff - /path/to/file.js",
                undefined
            );
        });

        it("should handle fileExists throwing an error", async () => {
            fileExists.mockImplementation(() => {
                throw new Error("File system error");
            });

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            // Should fall back to read-only diff when fileExists throws
            expect(executeCommandSpy).toHaveBeenCalledWith(
                "vscode.diff",
                expect.any(Object),
                expect.any(Object),
                "Diff - /path/to/file.js",
                undefined
            );
        });

        it("should handle readFileUtf8 throwing an error during Git validation", async () => {
            // Setup: file exists but readFileUtf8 fails (permission issue)
            fileExists.mockReturnValue(true);
            readFileUtf8.mockRejectedValue(new Error("Permission denied"));

            // Mock Git extension
            const mockGitApi: Partial<API> = {
                getRepository: jest.fn(() => ({ rootUri: vscode.Uri.file("/test") }) as any),
                toGitUri: jest.fn(() => vscode.Uri.parse("git://file.js?ref=HEAD")),
            };

            const mockGitExtension: Partial<vscode.Extension<GitExtension>> = {
                isActive: true,
                exports: {
                    enabled: true,
                    onDidChangeEnablement: {} as any,
                    getAPI: jest.fn(() => mockGitApi as API),
                } as GitExtension,
            };

            jest.spyOn(vscode.extensions, "getExtension").mockReturnValue(
                mockGitExtension as vscode.Extension<GitExtension>
            );

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            // Should fall back to editable diff since Git validation fails due to readFileUtf8 error
            const [command, leftUri, rightUri] = executeCommandSpy.mock.calls[0];
            expect(command).toBe("vscode.diff");
            expect(leftUri.toString()).toMatch(/^augment-diff:/); // Virtual URI for old content
            expect(rightUri.fsPath).toBe("/path/to/file.js"); // Actual file URI for editing
        });
    });

    describe("Focus options", () => {
        it("should handle focusLine option", async () => {
            fileExists.mockReturnValue(true);

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js",
                { focusLine: 5 }
            );

            const [command, , , , options] = executeCommandSpy.mock.calls[0];
            expect(command).toBe("vscode.diff");
            expect(options).toEqual({
                selection: expect.any(Object), // Range object created from focusLine
                preserveFocus: false,
            });
            // Verify Range constructor was called with Position objects for line 4 (5-1=4 for 0-based)
            expect(vscode.Range).toHaveBeenCalledWith(
                expect.objectContaining({ line: 4, character: 0 }),
                expect.objectContaining({ line: 4, character: 0 })
            );
        });

        it("should handle focusRange option", async () => {
            fileExists.mockReturnValue(true);

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js",
                {
                    focusRange: {
                        start: { line: 1, character: 0 },
                        end: { line: 2, character: 10 },
                    },
                }
            );

            const [command, , , , options] = executeCommandSpy.mock.calls[0];
            expect(command).toBe("vscode.diff");
            expect(options).toEqual({
                selection: expect.any(Object), // Range object created from focusRange
                preserveFocus: false,
            });
            // Verify Range constructor was called with correct positions
            expect(vscode.Range).toHaveBeenCalledWith(
                expect.objectContaining({ line: 1, character: 0 }), // start position
                expect.objectContaining({ line: 2, character: 10 }) // end position
            );
        });
    });

    describe("Tab management", () => {
        it("should close existing diff tab before opening new one", async () => {
            fileExists.mockReturnValue(true);

            // Mock existing tab
            const mockTab = { label: "Diff - /path/to/file.js" };
            (vscode.window.tabGroups as any).all = [{ tabs: [mockTab] }];

            const closeTabSpy = jest.spyOn(vscode.window.tabGroups, "close");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            expect(closeTabSpy).toHaveBeenCalledWith(mockTab);
        });

        it("should setup cleanup when tab is closed", async () => {
            fileExists.mockReturnValue(true);

            const onDidChangeTabsSpy = jest.spyOn(vscode.window.tabGroups, "onDidChangeTabs");
            const mockDispose = jest.fn();
            onDidChangeTabsSpy.mockReturnValue({ dispose: mockDispose });

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            expect(onDidChangeTabsSpy).toHaveBeenCalled();

            // Simulate tab being closed by calling the callback with no matching tabs
            const callback = onDidChangeTabsSpy.mock.calls[0][0];
            (vscode.window.tabGroups as any).all = []; // No tabs
            callback({} as any); // Pass empty event object

            // The cleanup should have been triggered
            expect(mockDispose).toHaveBeenCalled();
        });
    });

    describe("Error handling in Git operations", () => {
        it("should handle errors in shouldUseBuiltInGitDiff", async () => {
            fileExists.mockReturnValue(true);
            readFileUtf8.mockRejectedValue(new Error("File read error"));

            const executeCommandSpy = jest.spyOn(vscode.commands, "executeCommand");

            await openDiffInBuffer(
                "console.log('old');",
                "console.log('hello');",
                "/path/to/file.js"
            );

            // Should fall back to editable diff when Git operations fail
            expect(executeCommandSpy).toHaveBeenCalledWith(
                "vscode.diff",
                expect.any(Object),
                expect.objectContaining({ fsPath: "/path/to/file.js" }),
                "Diff - /path/to/file.js",
                undefined
            );
        });
    });
});
