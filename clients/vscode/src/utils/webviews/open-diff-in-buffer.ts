import * as vscode from "vscode";

import type { API, GitExtension } from "../../../../../third_party/vscode-git/git";
import { fileExists, readFileUtf8 } from "../fs-utils";

/**
 * Represents a line range with start and stop positions
 */
export type LineRange = {
    /** Starting line number */
    start: number;
    /** Ending line number */
    stop: number;
};

/**
 * Options for configuring diff display behavior
 */
type DiffOptions = {
    /** 1-based line number to focus on when opening the diff */
    focusLine?: number;
    /** Specific range to select when opening the diff */
    focusRange?: {
        /** Start position of the selection */
        start: { line: number; character: number };
        /** End position of the selection */
        end: { line: number; character: number };
    };
};

/**
 * Opens a diff view in VS Code showing the difference between old and new content.
 *
 * This function intelligently chooses the best diff approach based on the current state:
 * 1. VSCode built-in Git diff - when content matches Git state (provides native staging)
 * 2. Editable custom diff - when file exists but content doesn't match Git (allows editing)
 * 3. Read-only custom diff - when file doesn't exist (fallback for all edge cases)
 *
 * @param oldContents - The original content to show on the left side of the diff
 * @param newContents - The new content to show on the right side of the diff
 * @param filePath - Absolute path to the file being diffed
 * @param options - Optional configuration for diff display behavior
 * @throws Will not throw but may fail silently if VSCode APIs are unavailable
 */
export async function openDiffInBuffer(
    oldContents: string,
    newContents: string,
    filePath: string,
    options?: DiffOptions
): Promise<void> {
    // Try approaches in order of preference

    // 1. VSCode built-in Git diff (best experience with native staging)
    if (await shouldUseBuiltInGitDiff(oldContents, newContents, filePath)) {
        await openBuiltInGitDiff(filePath);
        return;
    }

    // 2. Editable custom diff (when file exists and can be edited)
    if (shouldUseEditableDiff(filePath)) {
        await openEditableDiff(oldContents, newContents, filePath, options);
        return;
    }

    // 3. Read-only diff (fallback for non-existent files)
    await openReadOnlyDiff(oldContents, newContents, filePath, options);
}

/**
 * Retrieves the VSCode Git extension API if available and active.
 *
 * This function handles the Git extension lifecycle:
 * - Checks if the Git extension is installed
 * - Activates the extension if it's not already active
 * - Returns the API interface for Git operations
 *
 * @returns Promise resolving to Git API instance or null if unavailable
 */
async function getGitAPI(): Promise<API | null> {
    const gitExtension = vscode.extensions.getExtension<GitExtension>("vscode.git");
    if (!gitExtension) {
        return null;
    }

    if (!gitExtension.isActive) {
        await gitExtension.activate();
    }

    return gitExtension.exports.getAPI(1) || null;
}

/**
 * Determines if VSCode's built-in Git diff should be used.
 *
 * This function validates that the provided content matches the current Git state for three scenarios:
 * 1. Add a new file: empty oldContents, file doesn't exist in HEAD, newContents matches disk
 * 2. Delete a file: empty newContents, file exists in HEAD, file doesn't exist on disk
 * 3. Change a file: oldContents matches HEAD, newContents matches disk
 *
 * When these conditions are met, using the built-in Git diff provides the best
 * user experience with native staging/unstaging capabilities.
 *
 * @param oldContents - Content that should match Git HEAD (empty for new files)
 * @param newContents - Content that should match current file on disk (empty for deleted files)
 * @param filePath - Absolute path to the file being checked
 * @returns Promise resolving to true if built-in Git diff should be used
 */
async function shouldUseBuiltInGitDiff(
    oldContents: string,
    newContents: string,
    filePath: string
): Promise<boolean> {
    try {
        const git = await getGitAPI();
        if (!git) {
            return false;
        }

        const repo = git.getRepository(vscode.Uri.file(filePath));
        if (!repo) {
            return false;
        }

        const normalizeContent = (content: string) => content.replace(/\r\n/g, "\n").trim();
        const normalizedOldContents = normalizeContent(oldContents);
        const normalizedNewContents = normalizeContent(newContents);

        // Get HEAD content (null if file doesn't exist in HEAD)
        const headContent = await getGitFileContent(git, filePath, "HEAD");
        const normalizedHeadContent = headContent ? normalizeContent(headContent) : null;

        // Get current file content (null if file doesn't exist on disk)
        let currentContent: string | null = null;
        try {
            currentContent = await readFileUtf8(filePath);
        } catch {
            // File doesn't exist on disk
            currentContent = null;
        }
        const normalizedCurrentContent = currentContent ? normalizeContent(currentContent) : null;

        // Case 1: Add a new file (empty oldContents)
        if (normalizedOldContents === "") {
            // File should not exist in HEAD and newContents should match current file
            return (
                normalizedHeadContent === null &&
                normalizedCurrentContent !== null &&
                normalizedCurrentContent === normalizedNewContents
            );
        }

        // Case 2: Delete a file (empty newContents)
        if (normalizedNewContents === "") {
            // File should exist in HEAD, not exist on disk, and oldContents should match HEAD
            return (
                normalizedHeadContent !== null &&
                normalizedCurrentContent === null &&
                normalizedHeadContent === normalizedOldContents
            );
        }

        // Case 3: Change a file (both conten1ts non-empty)
        // Both HEAD and current file should exist and match their respective expected contents
        return (
            normalizedHeadContent !== null &&
            normalizedCurrentContent !== null &&
            normalizedHeadContent === normalizedOldContents &&
            normalizedCurrentContent === normalizedNewContents
        );
    } catch {
        return false;
    }
}

/**
 * Opens VSCode's built-in Git diff using the native git.openChange command.
 *
 * This provides the best user experience with full Git integration including:
 * - Native staging/unstaging capabilities
 * - Proper Git status integration
 * - Consistent with VSCode's Git workflow
 *
 * @param filePath - Absolute path to the file to open in Git diff
 */
async function openBuiltInGitDiff(filePath: string): Promise<void> {
    const fileUri = vscode.Uri.file(filePath);
    await vscode.commands.executeCommand("git.openChange", fileUri);
}

/**
 * Retrieves file content from Git at a specific reference (commit, branch, etc.).
 *
 * Uses VSCode's Git extension to create a Git URI and open the document
 * at the specified reference. This allows reading historical versions of files.
 *
 * @param git - Git API instance from VSCode's Git extension
 * @param filePath - Absolute path to the file
 * @param ref - Git reference (e.g., "HEAD", commit hash, branch name)
 * @returns Promise resolving to file content or null if not found/accessible
 */
async function getGitFileContent(git: API, filePath: string, ref: string): Promise<string | null> {
    try {
        const fileUri = vscode.Uri.file(filePath);
        const gitUri = git.toGitUri(fileUri, ref);
        const document = await vscode.workspace.openTextDocument(gitUri);
        return document.getText();
    } catch (error) {
        return null;
    }
}

/**
 * Determines if an editable diff should be used.
 *
 * An editable diff allows the user to modify the "after" content directly
 * by using the actual file on the right side of the diff. This is only
 * possible when the file exists on disk.
 *
 * @param filePath - Absolute path to check for existence
 * @returns True if file exists and editable diff should be used
 */
function shouldUseEditableDiff(filePath: string): boolean {
    try {
        return fileExists(filePath);
    } catch {
        return false;
    }
}

/**
 * Opens an editable diff using a virtual document for the "before" content and the actual file for editing.
 *
 * This approach allows users to:
 * - See the differences between old and new content
 * - Edit the file directly in the right pane of the diff
 * - Save changes normally through VSCode's file system
 *
 * The left side shows a virtual document with the old content, while the right side
 * shows the actual file, making it fully editable.
 *
 * @param oldContents - Content to display on the left side (read-only)
 * @param _newContents - Not used since right side shows actual file content
 * @param filePath - Absolute path to the file to edit
 * @param options - Optional positioning and focus configuration
 */
async function openEditableDiff(
    oldContents: string,
    _newContents: string,
    filePath: string,
    options?: DiffOptions
): Promise<void> {
    const scheme = "augment-diff";
    const provider = new VirtualDocumentProvider();
    const providerDisposable = vscode.workspace.registerTextDocumentContentProvider(
        scheme,
        provider
    );

    const leftUri = vscode.Uri.parse(`${scheme}:${filePath}?before`);
    const rightUri = vscode.Uri.file(filePath);
    const diffTitle = `Diff - ${filePath}`;

    provider.setDocumentContent(leftUri, oldContents || "");

    // Close existing diff tab if present
    await closeExistingDiffTab(diffTitle);

    // Open diff with optional positioning
    const showOptions = createShowOptions(options);
    await vscode.commands.executeCommand("vscode.diff", leftUri, rightUri, diffTitle, showOptions);

    // Clean up when diff is closed
    setupCleanupOnTabClose(diffTitle, providerDisposable);
}

/**
 * Opens a read-only diff using virtual documents for both sides.
 *
 * This is the fallback approach used when:
 * - The file doesn't exist on disk (can't use editable diff)
 * - Git integration isn't available or content doesn't match Git state
 *
 * Both sides of the diff are virtual documents, making the entire diff read-only.
 * This is useful for comparing content that may not correspond to actual files.
 *
 * @param oldContents - Content to display on the left side
 * @param newContents - Content to display on the right side
 * @param filePath - Path used for display purposes and language detection
 * @param options - Optional positioning and focus configuration
 */
async function openReadOnlyDiff(
    oldContents: string,
    newContents: string,
    filePath: string,
    options?: DiffOptions
): Promise<void> {
    const scheme = "virtual";
    const provider = new VirtualDocumentProvider();
    vscode.workspace.registerTextDocumentContentProvider(scheme, provider);

    const leftUri = vscode.Uri.parse(`${scheme}:/${filePath}?left`);
    const rightUri = vscode.Uri.parse(`${scheme}:/${filePath}?right`);
    const diffTitle = `Diff - ${filePath}`;

    provider.setDocumentContent(leftUri, oldContents || "");
    provider.setDocumentContent(rightUri, newContents || "");

    await closeExistingDiffTab(diffTitle);

    const showOptions = createShowOptions(options);
    await vscode.commands.executeCommand("vscode.diff", leftUri, rightUri, diffTitle, showOptions);
}

/**
 * Provides virtual document content for custom URI schemes.
 *
 * This class implements VSCode's TextDocumentContentProvider interface to serve
 * content for virtual URIs that don't correspond to actual files on disk.
 * Used for creating the "before" content in diff views.
 */
class VirtualDocumentProvider implements vscode.TextDocumentContentProvider {
    /** Map storing content for each virtual URI */
    private documents: Map<string, string> = new Map();

    /**
     * Sets content for a virtual document URI.
     * @param uri - The virtual URI to associate with content
     * @param content - The text content to serve for this URI
     */
    setDocumentContent(uri: vscode.Uri, content: string): void {
        this.documents.set(uri.toString(), content);
    }

    /**
     * Provides content for a virtual document URI.
     * Called by VSCode when opening a virtual document.
     * @param uri - The virtual URI being requested
     * @returns The content for the URI, or undefined if not found
     */
    provideTextDocumentContent(uri: vscode.Uri): string | undefined {
        return this.documents.get(uri.toString());
    }
}

// Helper functions

/**
 * Closes any existing diff tab with the specified title.
 *
 * This prevents multiple diff tabs for the same file from accumulating
 * and ensures a clean user experience when opening new diffs.
 *
 * @param diffTitle - The title of the diff tab to close
 */
async function closeExistingDiffTab(diffTitle: string): Promise<void> {
    const existingTab = vscode.window.tabGroups.all
        .flatMap((group) => group.tabs)
        .find((tab) => tab.label === diffTitle);

    if (existingTab) {
        await vscode.window.tabGroups.close(existingTab);
    }
}

/**
 * Creates VSCode TextDocumentShowOptions from diff options.
 *
 * Converts the diff-specific options into VSCode's format for positioning
 * the cursor and selection when opening documents.
 *
 * @param options - Optional diff configuration with focus settings
 * @returns VSCode show options or undefined if no positioning requested
 */
function createShowOptions(options?: DiffOptions): vscode.TextDocumentShowOptions | undefined {
    if (options?.focusRange) {
        return {
            selection: new vscode.Range(
                new vscode.Position(
                    options.focusRange.start.line,
                    options.focusRange.start.character
                ),
                new vscode.Position(options.focusRange.end.line, options.focusRange.end.character)
            ),
            preserveFocus: false,
        };
    }

    if (options?.focusLine !== undefined) {
        const line = Math.max(0, options.focusLine - 1);
        return {
            selection: new vscode.Range(new vscode.Position(line, 0), new vscode.Position(line, 0)),
            preserveFocus: false,
        };
    }

    return undefined;
}

/**
 * Sets up automatic cleanup when a diff tab is closed.
 *
 * Monitors tab changes and disposes of resources when the specified diff tab
 * is no longer present. This prevents memory leaks from virtual document providers.
 *
 * @param diffTitle - The title of the diff tab to monitor
 * @param providerDisposable - The disposable to clean up when tab closes
 */
function setupCleanupOnTabClose(diffTitle: string, providerDisposable: vscode.Disposable): void {
    const tabChangeDisposable = vscode.window.tabGroups.onDidChangeTabs(() => {
        const diffTabExists = vscode.window.tabGroups.all
            .flatMap((group) => group.tabs)
            .some((tab) => tab.label === diffTitle);

        if (!diffTabExists) {
            providerDisposable.dispose();
            tabChangeDisposable.dispose();
        }
    });
}
