import { Webview } from "vscode";

import { isHmr } from "../load-webview-hmr";

export function generateCSPPolicy(...optOperators: CSPOptionOperator[]): string {
    const opts: CSPOptions = {
        scripts: new Set(),
        // The VSCode components use inline styles and most (if not all WebViews)
        // will use those components.
        styles: new Set(["'unsafe-inline'"]),
        fonts: new Set(),
        images: new Set(),
        media: new Set(),
        workers: new Set(),
        connects: new Set(),
    };
    for (const operator of optOperators) {
        operator(opts);
    }
    const parts = [`default-src 'none';`];
    const sets = {
        /* eslint-disable @typescript-eslint/naming-convention */
        "font-src": opts.fonts,
        "style-src": opts.styles,
        "script-src": opts.scripts,
        "img-src": opts.images,
        "media-src": opts.media,
        "worker-src": opts.workers,
        "connect-src": opts.connects,
        /* eslint-enable @typescript-eslint/naming-convention */
    };
    for (const [key, values] of Object.entries(sets)) {
        const sortedValues = Array.from(values).sort();
        if (sortedValues.length === 0) {
            continue;
        }
        parts.push(`${key} ${sortedValues.join(" ")};`);
    }
    return parts.join(" ");
}

export function addGoogleFonts(): CSPOptionOperator {
    return (opts: CSPOptions) => {
        opts.fonts.add("https://fonts.gstatic.com");
        opts.styles.add("https://fonts.googleapis.com");
    };
}

export function addGoogleFavicons(): CSPOptionOperator {
    return (opts: CSPOptions) => {
        opts.images.add("https://www.google.com");
        opts.images.add("https://*.gstatic.com");
        opts.images.add("https://s2.googleusercontent.com");
    };
}

export function addWebViewCSP(webview: Webview) {
    return (opts: CSPOptions) => {
        opts.scripts.add(webview.cspSource);
        opts.styles.add(webview.cspSource);
        opts.images.add(webview.cspSource);
        opts.fonts.add(webview.cspSource);
        opts.media.add(webview.cspSource);
    };
}

export function addImgData() {
    return (opts: CSPOptions) => {
        opts.images.add("data:");
        opts.images.add("blob:");
    };
}

export function addNonce(nonce: string): CSPOptionOperator {
    return (opts: CSPOptions) => {
        opts.scripts.add(`'nonce-${nonce}'`);
    };
}

export function addMonacoRequirements() {
    return (opts: CSPOptions) => {
        opts.styles.add("'unsafe-inline'");
        opts.fonts.add("data:");
        opts.workers.add("blob:");
    };
}

export function addMonacoCdnRequirements() {
    return (opts: CSPOptions) => {
        opts.fonts.add("https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.52.2/min/");
        opts.styles.add("https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.52.2/min/");
        opts.scripts.add("https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.52.2/min/");
        opts.scripts.add("'unsafe-inline'");
    };
}

/**
 * This is to allow HMR to be used in dev mode.  As such
 * we do not ever want to do this, unless AUGMENT_HMR is set.
 */
export function addLocalhostRequirements(hmrUrl: string) {
    if (!isHmr()) {
        return () => {};
    }
    const wsUrl = hmrUrl.replace(/^https?:/, "ws:");
    return (opts: CSPOptions) => {
        (["fonts", "styles", "scripts", "workers", "media", "images", "connects"] as const).forEach(
            (key) => {
                opts[key].add(hmrUrl).add(wsUrl);
            }
        );
    };
}

type CSPOptionOperator = (opts: CSPOptions) => void;

interface CSPOptions {
    scripts: Set<string>;
    styles: Set<string>;
    fonts: Set<string>;
    images: Set<string>;
    media: Set<string>;
    workers: Set<string>;
    connects: Set<string>;
}

/**
 * Adds the necessary CSP directives to allow loading Rive animations from CDN
 */
export function addRiveCdnRequirements(): CSPOptionOperator {
    return (opts: CSPOptions) => {
        // Add both jsdelivr and unpkg CDNs
        opts.connects.add("https://cdn.jsdelivr.net");
        opts.connects.add("https://unpkg.com");
        opts.scripts.add("https://cdn.jsdelivr.net");
        opts.scripts.add("https://unpkg.com");
        opts.scripts.add("'unsafe-eval'"); // Required for WebAssembly compilation
        opts.workers.add("blob:");
        // Allow loading local Rive files through vscode-resource scheme
        opts.connects.add("vscode-resource:");
        opts.connects.add("https://*.vscode-cdn.net");
    };
}

/**
 * Adds the necessary CSP directives to allow Sentry error reporting
 */
export function addSentryRequirements(): CSPOptionOperator {
    return (opts: CSPOptions) => {
        // Allow connections to Sentry's ingest endpoints
        opts.connects.add("https://*.ingest.sentry.io");
        opts.connects.add("https://*.ingest.us.sentry.io");
    };
}
