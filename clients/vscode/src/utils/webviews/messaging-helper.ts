import { WorkTimer } from "$vscode/src/metrics/work-timer";

import * as vscode from "vscode";

import { AsyncWebViewMessage, WebViewMessage } from "../../webview-providers/webview-messages";
import { AsyncMsgHandler } from "./messaging";
import { WebViewMessageTypes } from "./types";

/**
 * Creates an instance of AsyncMsgHandler for a host of a webview (host).
 *
 * @param webview The webview instance.
 * @returns An instance of AsyncMsgHandler.
 */
export function createAsyncMsgHandlerFromWebview(
    webview: vscode.Webview,
    workTimer: WorkTimer | undefined = undefined
): AsyncMsgHandler {
    return new AsyncMsgHandler(
        (msg: AsyncWebViewMessage<WebViewMessageTypes>) => void webview.postMessage(msg),
        (handler: (msg: AsyncWebViewMessage<WebViewMessage>) => void): (() => void) => {
            const disposer = webview.onDidReceiveMessage(handler);
            return () => void disposer.dispose();
        },
        workTimer
    );
}
