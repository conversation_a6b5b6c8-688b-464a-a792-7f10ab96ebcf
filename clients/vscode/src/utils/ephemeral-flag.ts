import { Listen, Observable } from "@augment-internal/sidecar-libs/src/utils/observable";
import * as vscode from "vscode";

import { DisposableService } from "./disposable-service";

/**
 * An ephemeral observable holds its value when set until:
 *
 * - the active editor changes.
 * - the selection in the document indicated by the observable changes.
 * - the document indicated by the observable changes.
 *
 * Set a value by calling `set` and clear manually by calling `clear`.
 *
 * Note that after setting a value, we debounce the editor events above for a short time
 * (e.g. 1ms). VSCode can be quite noisy with its events: e.g. a suggestion or
 * completion are marked accepted during a text document changed event, but we'll get a
 * text selection changed event immediately afterwards.
 */
export class EphemeralObservable<T> extends DisposableService {
    /** The document that the observable is set for. */
    private _document: vscode.TextDocument | undefined;
    /** The time the observable was last set. */
    private _lastSetAt: number | undefined;

    /** The underlying observable holding the value. */
    private readonly _observable: Observable<T | undefined>;

    /**
     * Create an ephemeral observable.
     *
     * @param initialValue - The initial value.
     * @param debounceMs - The debounce time in milliseconds.
     *  Typically a value like 1ms is appropriate.
     */
    constructor(initialValue: T | undefined = undefined, debounceMs: number = 1) {
        super();
        this._observable = this.addDisposable(new Observable<T | undefined>(initialValue));

        const clearOnEvent = (document?: vscode.TextDocument) => {
            if (
                this.value === undefined ||
                (document && this._document && document !== this._document) ||
                (this._lastSetAt && Date.now() - this._lastSetAt < debounceMs)
            ) {
                return;
            }
            this.clear();
        };

        // Add event listeners to clear the observable.
        this.addDisposable(
            vscode.window.onDidChangeActiveTextEditor(() => {
                clearOnEvent();
            })
        );
        this.addDisposable(
            vscode.window.onDidChangeTextEditorSelection((event) => {
                clearOnEvent(event.textEditor.document);
            })
        );
        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument((event) => {
                clearOnEvent(event.document);
            })
        );
    }

    /** The current value. */
    public get value() {
        return this._observable.value;
    }

    /** Register a listener for changes to the value. */
    public listen(fn: Listen<T | undefined>, fire = false) {
        return this._observable.listen(fn, fire);
    }

    /**
     * Wait until the value satisfies the predicate.
     *
     * @param predicate The predicate to wait for.
     * @param timeoutMs The optional timeout in milliseconds.
     * @returns A promise that resolves when the value satisfies the predicate.
     */
    public waitUntil(predicate: (v: T | undefined) => boolean, timeoutMs?: number) {
        return this._observable.waitUntil(predicate, timeoutMs);
    }

    /**
     * Sets a value.
     *
     * @param value - The new value.
     * @param document - The document associated with this value.
     */
    public set(value: T, document?: vscode.TextDocument) {
        this._lastSetAt = Date.now();
        this._document = document;
        this._observable.value = value;
    }

    /** Clears the value. */
    public clear() {
        this._document = undefined;
        this._observable.value = undefined;
    }
}
