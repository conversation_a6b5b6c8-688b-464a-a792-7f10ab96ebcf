import { DisposableService } from "../utils/disposable-service";

export class SingletonExecutorDisposedError extends Error {
    constructor() {
        super("SingletonExecutor has been disposed");
    }
}

// SingletonExecutor is a class that wraps a function such that there will never be more than one
// instance of the function running at a time. When the SingletonExecutor is disposed, it will
// disallow new executions of the function but will not interrupt an existing one.
export class SingletonExecutor extends DisposableService {
    private static _disposedError = new SingletonExecutorDisposedError();

    private _nextExecutionScheduled = false;
    private _kickPromise = Promise.resolve();
    private _stopping = false;

    constructor(private readonly _execute: () => Promise<void>) {
        super();
        this.addDisposable({ dispose: () => (this._stopping = true) });
    }

    // `kick` requests an execution of the function and returns a Promise that will resolve when
    // the execution completes. If no execution is currently in progress, the new execution will
    // begin immediately. If an execution is in progress, the new one will begin after the current
    // one finishes. If multiple requests are made while an execution is in progress, they are
    // coalesced into a single request.
    public kick(): Promise<void> {
        if (this._nextExecutionScheduled) {
            // The next execution is already scheduled. Return its promise.
            return this._kickPromise;
        }

        // No next execution is scheduled. Schedule one and return its promise. If there is no
        // current execution in progress, the next execution will start immediately. Otherwise,
        // it will start after the current one finishes.
        this._nextExecutionScheduled = true;
        this._kickPromise = this._kickPromise.then(async () => {
            this._nextExecutionScheduled = false;
            if (this._stopping) {
                return Promise.reject(SingletonExecutor._disposedError);
            }
            return this._execute();
        });

        return this._kickPromise;
    }
}
