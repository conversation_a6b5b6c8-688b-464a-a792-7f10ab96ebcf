import fs from "fs";
import JSON5 from "json5";
import os from "os";
import path from "path";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { KeybindingParser } from "../third-party/microsoft/vscode/src/vs/base/common/keybindingParser";
import {
    Chord,
    KeyCodeChord,
    Keybinding as vscodeKeybinding,
} from "../third-party/microsoft/vscode/src/vs/base/common/keybindings";
import {
    KeyCodeUtils,
    ScanCodeUtils,
} from "../third-party/microsoft/vscode/src/vs/base/common/keyCodes";
import { DisposableService } from "../utils/disposable-service";
import { AugmentGlobalState, GlobalContextKey } from "./context";
import {
    getAugmentExtensionPackageJson,
    getUserDirectory,
    Keybinding as KeybindingFromPackageJson,
} from "./environment";

class UserKeybindingInfo {
    constructor(
        public readonly platform: string,
        public keybindings: { [command: string]: KeybindingFromPackageJson }
    ) {}
}

export type SimplifiedPlatform = "win32" | "darwin" | "linux";

export class KeybindingWatcher extends DisposableService {
    private static readonly remoteHeuristicDelayMs = 1000 * 5;

    private static readonly _logger = getLogger("KeybindingWatcher");

    private static readonly keybindingFileName = "keybindings.json";

    private isRemote = vscode.env.remoteName !== undefined;
    private isWeb = vscode.env.uiKind === vscode.UIKind.Web;
    private platform: string | undefined;

    private watcher: fs.FSWatcher | undefined;

    private defaultKeybindings: { [command: string]: KeybindingFromPackageJson } = {};

    private userKeybindings: { [command: string]: KeybindingFromPackageJson } = {};

    constructor(private readonly _globalState: AugmentGlobalState) {
        super();

        this.defaultKeybindings = this.getDefaultKeybindings();

        if (!this.isRemote && !this.isWeb) {
            this.platform = os.platform();
            const userDirectory = getUserDirectory();

            if (userDirectory) {
                const userKeybindingsFile = path.join(
                    userDirectory,
                    KeybindingWatcher.keybindingFileName
                );

                this.userKeybindings = this.getUserKeybindings(userKeybindingsFile);
                this.watcher = fs.watch(userDirectory, (eventType, filename) => {
                    if (filename !== KeybindingWatcher.keybindingFileName) {
                        return;
                    }
                    KeybindingWatcher._logger.info(
                        `keybindings file changed: ${eventType} ${filename}`
                    );
                    this.userKeybindings = this.getUserKeybindings(userKeybindingsFile);
                });

                this.addDisposable({
                    dispose: () => {
                        this.watcher?.close();
                    },
                });
            }
        } else {
            // Restore keybinding info if we've seen it before.
            const userKeybindingInfo: UserKeybindingInfo | undefined = this._globalState.get(
                GlobalContextKey.userKeybindingInfo
            );
            this.platform = userKeybindingInfo?.platform;
            this.userKeybindings = userKeybindingInfo?.keybindings ?? {};

            // We seem to get notifications for edits to the keybindings file
            // even on remote systems (as I discovered by testing),
            // so listen for them and capture the data.
            this.addDisposable(
                vscode.workspace.onDidSaveTextDocument((doc) => {
                    if (doc.uri.scheme !== "vscode-userdata") {
                        return;
                    }
                    if (doc.fileName.endsWith(KeybindingWatcher.keybindingFileName)) {
                        this.platform = this.getPlatformFromFilename(doc.fileName);
                        this.userKeybindings = this.getUserKeybindingsFromJSON(
                            doc.fileName,
                            doc.getText()
                        );
                        void this._globalState.update(
                            GlobalContextKey.userKeybindingInfo,
                            new UserKeybindingInfo(this.platform, this.userKeybindings)
                        );
                    }
                })
            );

            // We rely on seeing output logs to find the user's platform and
            // keybindings file. To maximize the chance of success, we wait a
            // bit before reading the logs.
            setTimeout(() => {
                void this.tryToFindPlatformAndUserKeybindings();
            }, KeybindingWatcher.remoteHeuristicDelayMs);
        }
    }

    /**
     * This uses some terrible hacks to figure out and read the local platform
     * and keybindings.json file.
     *
     * Note that there are other things we could try to read:
     * - "output:rendererLog" is the Window tab and has a string:
     *   "Using cached extensions scan result user vscode-userdata:..."
     *   However, its path is farther from the keybinding file.
     * - "output:extension-output-ms-vscode-remote.remote-ssh-#1-Remote - SSH"
     *   is the "Remote - SSH" tab and has a string:
     *   "Looking for existing server data file at ..."
     *   However, it relies on using SSH and not a different remote.
     */
    private async tryToFindPlatformAndUserKeybindings() {
        if (this.isRemote && this.platform === undefined) {
            // Read the output of the "Extension Host".
            const doc = await vscode.workspace.openTextDocument(
                vscode.Uri.from({
                    scheme: "output",
                    path: "exthost",
                })
            );
            const text = doc.getText();
            // Look for the following line, which contains a local path.
            const regex = /Skipping acquiring lock for (.*)\./;
            const match = text.match(regex);
            if (match) {
                KeybindingWatcher._logger.debug("Loading user keybindings");
                const path = match[1];
                // Translate that local path to the keybinding file.
                const keybindingPath = path.replace(
                    /workspaceStorage.*/,
                    KeybindingWatcher.keybindingFileName
                );
                // Figure out the platform from the keybinding filename.
                this.platform = this.getPlatformFromFilename(keybindingPath);
                // Read the keybindings file.
                const keybindingDoc = await vscode.workspace.openTextDocument(
                    vscode.Uri.from({
                        scheme: "vscode-userdata",
                        path: keybindingPath,
                    })
                );
                this.userKeybindings = this.getUserKeybindingsFromJSON(
                    keybindingDoc.fileName,
                    keybindingDoc.getText()
                );
                // Save the platform and keybindings in the global state.
                void this._globalState.update(
                    GlobalContextKey.userKeybindingInfo,
                    new UserKeybindingInfo(this.platform, this.userKeybindings)
                );
            }
        }
    }

    public getKeybindingForCommand(commandId: string, prettyFormat: boolean = false) {
        // User keybindings take precedence
        let matchingKeybinding = this.userKeybindings[commandId];
        if (!matchingKeybinding) {
            // If it's not found in user keybindings, check if it's unbound.
            if (this.userKeybindings["-" + commandId]) {
                return undefined;
            }
            // If not found in user keybindings, check default keybindings.
            // But only if we have seen the user keybindings file, as otherwise
            // we don't know if they have been overridden, or if Macs and PCs
            // have the same default keybinding.
            // Note that in this latter case we will not show the overridden
            // keybinding.
            const defaultKeybinding = this.defaultKeybindings[commandId];
            if (
                defaultKeybinding &&
                (this.platform !== undefined || defaultKeybinding.key === defaultKeybinding.mac)
            ) {
                matchingKeybinding = defaultKeybinding;
            }
        }

        let keybinding: string;
        if (matchingKeybinding) {
            if (this.platform === undefined) {
                // If we don't know the platform, return both keybindings.
                keybinding = Array.from(new Set([matchingKeybinding.key, matchingKeybinding.mac]))
                    .filter((k) => k !== undefined)
                    .join("/");
            } else if (this.platform === "darwin" && matchingKeybinding.mac) {
                // If we are on a mac, return the mac keybinding
                keybinding = matchingKeybinding.mac;
            } else {
                keybinding = matchingKeybinding.key;
            }
            return prettyFormat
                ? KeybindingWatcher.formatKeyboardShortcut(keybinding, this.getSimplifiedPlatform())
                : keybinding;
        }
        return null;
    }

    public static getStructuredKeybinding(
        keybindingString: string | undefined | null
    ): AugmentKeybinding | null {
        try {
            if (!keybindingString) {
                return null;
            }
            const keybinding: vscodeKeybinding | null =
                KeybindingParser.parseKeybinding(keybindingString);
            if (!keybinding) {
                return null;
            }
            return new AugmentKeybinding(keybinding.chords);
        } catch (err) {
            this._logger.warn(`error parsing keybinding ${keybindingString}`, err);
            return null;
        }
    }

    public static formatKeyboardShortcut(
        keybindingString: string | undefined | null,
        platform: SimplifiedPlatform
    ): string {
        try {
            const keybinding: AugmentKeybinding | null =
                KeybindingWatcher.getStructuredKeybinding(keybindingString);
            if (!keybinding) {
                return "";
            }
            return keybinding.toPrettyString(platform);
        } catch (err) {
            this._logger.warn(
                "error formatting keybinding, returning unformatted keybinding.",
                err
            );
            return keybindingString || "";
        }
    }

    public getSimplifiedPlatform(): SimplifiedPlatform {
        if (this.platform === "darwin") {
            return "darwin";
        }
        if (this.platform === "win32") {
            return "win32";
        }
        return "linux";
    }

    private getDefaultKeybindings(): { [command: string]: KeybindingFromPackageJson } {
        const packageJson = getAugmentExtensionPackageJson();
        if (packageJson && packageJson.contributes && packageJson.contributes.keybindings) {
            const { keybindings } = packageJson.contributes;
            return Object.fromEntries(
                keybindings.map((kb: KeybindingFromPackageJson) => [kb.command, kb])
            );
        }
        return {};
    }

    private getUserKeybindings(userKeybindingsFile: string): {
        [command: string]: KeybindingFromPackageJson;
    } {
        if (fs.existsSync(userKeybindingsFile)) {
            const keybindingsContent = fs.readFileSync(userKeybindingsFile, "utf8");
            return this.getUserKeybindingsFromJSON(userKeybindingsFile, keybindingsContent);
        }
        return {};
    }

    private getUserKeybindingsFromJSON(
        filePath: string,
        text: string
    ): { [command: string]: KeybindingFromPackageJson } {
        try {
            const keybindings: KeybindingFromPackageJson[] = JSON5.parse(text);
            return Object.fromEntries(keybindings.map((kb) => [kb.command, kb]));
        } catch (e) {
            KeybindingWatcher._logger.debug(
                `Failed to parse '${filePath}': ${(e as Error).message}`
            );
            return {};
        }
    }

    // This is a hopefully-reasonable heuristic.
    private getPlatformFromFilename(filename: string): SimplifiedPlatform {
        if (filename.startsWith("/Users/")) {
            return "darwin";
        }
        if (filename.match(/^\/?[a-zA-Z](?::|%3A)\/.*/)) {
            return "win32";
        }
        return "linux";
    }
}

const prettyModifierKeyMap = {
    meta: {
        darwin: "⌘",
        win32: "Win",
        linux: "Meta",
    },
    ctrl: {
        darwin: "⌃",
        win32: "Ctrl",
        linux: "Ctrl",
    },
    alt: {
        darwin: "⌥",
        win32: "Alt",
        linux: "Alt",
    },
    shift: "⇧",
} as const;

/* eslint-disable @typescript-eslint/naming-convention */
const prettyKeyMap = {
    Enter: "⏎",
    UpArrow: "↑",
    DownArrow: "↓",
    Backspace: "⌫",
    Escape: "Esc",
} as const;
/* eslint-enable @typescript-eslint/naming-convention */

export class AugmentKeybinding extends vscodeKeybinding {
    constructor(chords: Chord[]) {
        super(chords);
    }

    toPrettyString(platform: SimplifiedPlatform) {
        const formattedChords = [];
        for (const chord of this.chords) {
            const chordParts = [];

            if (chord.ctrlKey) {
                chordParts.push(prettyModifierKeyMap.ctrl[platform]);
            }
            if (chord.shiftKey) {
                chordParts.push(prettyModifierKeyMap.shift);
            }
            if (chord.altKey) {
                chordParts.push(prettyModifierKeyMap.alt[platform]);
            }
            if (chord.metaKey) {
                chordParts.push(prettyModifierKeyMap.meta[platform]);
            }

            let keyStr: string;
            if (chord instanceof KeyCodeChord) {
                keyStr = KeyCodeUtils.toString(chord.keyCode);
            } else {
                // note that ScanCodes do not translate cleanly to keyCodes for
                // normal keys like A-Z, 0-9, +, -, because they depend on keyboard layout
                // so trying to translate KeyA -> "A" is not guaranteed apparently?
                // (based on how the "immutable" concept works in vscode-key-codes.ts)
                keyStr = ScanCodeUtils.toString(chord.scanCode);
            }
            if (keyStr in prettyKeyMap) {
                keyStr = prettyKeyMap[keyStr as keyof typeof prettyKeyMap];
            }
            keyStr = keyStr.charAt(0).toUpperCase() + keyStr.substring(1).toLowerCase();

            chordParts.push(keyStr);
            if (chordParts.join("").length <= 2 || platform === "darwin") {
                formattedChords.push(chordParts.join(""));
            } else {
                formattedChords.push(chordParts.join("+"));
            }
        }
        if (formattedChords.indexOf("Unknown") !== -1) {
            return "";
        }
        return formattedChords.join(" ").trim();
    }
}
