// Utils for building valid VSCode markdown strings with HTML.
import { getLogger } from "../logging";
import { isVsCodeVersionGte } from "./environment";

const logger = getLogger("markdown-html-utils");

// This regex copied verbatim from https://github.com/microsoft/vscode/blob/1.93.0/src/vs/base/browser/markdownRenderer.ts#L401
const validStyleTagRegex =
    // eslint-disable-next-line no-useless-escape
    /^(color\:(#[0-9a-fA-F]+|var\(--vscode(-[a-zA-Z]+)+\));)?(background-color\:(#[0-9a-fA-F]+|var\(--vscode(-[a-zA-Z]+)+\));)?(border-radius:[0-9]+px;)?$/;

type ValidVSCodeStyleTagConstructorArg = {
    color?: string | undefined;
    backgroundColor?: string | undefined;
    borderRadius?: string | undefined;
};

export class ValidVSCodeStyleTag {
    public color: string | undefined;
    public backgroundColor: string | undefined;
    public borderRadius: string | undefined;

    constructor({ color, backgroundColor, borderRadius }: ValidVSCodeStyleTagConstructorArg) {
        this.color = color;
        this.backgroundColor = backgroundColor;
        this.borderRadius = borderRadius;
    }

    toString() {
        let out = [];
        if (this.color != null) {
            if (!/(#[0-9a-fA-F]+|var\(--vscode(-[a-zA-Z]+)+\))/.test(this.color)) {
                logger.error(`Invalid color passed to ValidVsCodeStyleTag: ${this.color}`);
            } else {
                out.push(`color:${this.color};`);
            }
        }
        if (this.backgroundColor != null) {
            if (!/(#[0-9a-fA-F]+|var\(--vscode(-[a-zA-Z]+)+\))/.test(this.backgroundColor)) {
                logger.error(
                    `Invalid background-color passed to ValidVsCodeStyleTag: ${this.backgroundColor}`
                );
            } else {
                out.push(`background-color:${this.backgroundColor};`);
            }
        }
        if (this.borderRadius != null && borderRadiusAllowed()) {
            if (!/^[0-9]+px/.test(this.borderRadius)) {
                logger.error(
                    `Invalid border-radius passed to ValidVsCodeStyleTag: ${this.borderRadius}`
                );
            } else {
                out.push(`border-radius:${this.borderRadius};`);
            }
        }
        const joined = out.join("");
        if (!validStyleTagRegex.test(joined)) {
            logger.error(`Invalid style tag: ${joined}`);
            return "";
        }
        return out.join("");
    }
}

export function borderRadiusAllowed(): boolean {
    return isVsCodeVersionGte("1.92.0");
}

export function span(
    content: string,
    style: ValidVSCodeStyleTag | ValidVSCodeStyleTagConstructorArg | undefined
): string {
    let styleObj: ValidVSCodeStyleTag | undefined;
    if (style == null) {
        return `<span>${content}</span>`;
    } else if (style instanceof ValidVSCodeStyleTag) {
        styleObj = style;
    } else {
        styleObj = new ValidVSCodeStyleTag(style);
    }
    return `<span style="${styleObj.toString()}">${content}</span>`;
}
