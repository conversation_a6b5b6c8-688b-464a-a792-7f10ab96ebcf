import * as vscode from "vscode";

import {
    <PERSON><PERSON>ode<PERSON>hord,
    ScanCode<PERSON>hord,
} from "../third-party/microsoft/vscode/src/vs/base/common/keybindings";
import { KeyCode } from "../third-party/microsoft/vscode/src/vs/base/common/keyCodes";
import { getAugmentExtensionPackageJson } from "./environment";
import { escapeHtml } from "./escape-html";
import { AugmentKeybinding, SimplifiedPlatform } from "./keybindings";

const keyboardIconFilenames = new Map<
    KeyCode,
    string | { [platform in SimplifiedPlatform]: string }
>([
    [KeyCode.KeyA, "a"],
    [KeyCode.KeyB, "b"],
    [KeyCode.KeyC, "c"],
    [KeyCode.KeyD, "d"],
    [KeyCode.KeyE, "e"],
    [KeyCode.KeyF, "f"],
    [KeyCode.KeyG, "g"],
    [KeyCode.KeyH, "h"],
    [KeyCode.KeyI, "i"],
    [KeyCode.KeyJ, "j"],
    [KeyCode.KeyK, "k"],
    [KeyCode.KeyL, "l"],
    [KeyCode.KeyM, "m"],
    [KeyCode.KeyN, "n"],
    [KeyCode.KeyO, "o"],
    [KeyCode.KeyP, "p"],
    [KeyCode.KeyQ, "q"],
    [KeyCode.KeyR, "r"],
    [KeyCode.KeyS, "s"],
    [KeyCode.KeyT, "t"],
    [KeyCode.KeyU, "u"],
    [KeyCode.KeyV, "v"],
    [KeyCode.KeyW, "w"],
    [KeyCode.KeyX, "x"],
    [KeyCode.KeyY, "y"],
    [KeyCode.KeyZ, "z"],
    [KeyCode.Shift, "shift"],
    [KeyCode.Delete, "delete"],
    [KeyCode.Enter, "return"],
    [KeyCode.Tab, "tab"],
    [KeyCode.Escape, "escape"],
    [KeyCode.Semicolon, "semicolon"],
    [KeyCode.Backspace, "backspace"],
    [
        KeyCode.Ctrl,
        {
            linux: "ctrl",
            win32: "ctrl",
            darwin: "control",
        },
    ],
    [
        KeyCode.Alt,
        {
            linux: "alt",
            win32: "alt",
            darwin: "option",
        },
    ],
    [
        KeyCode.Meta,
        {
            linux: "meta",
            win32: "win",
            darwin: "command",
        },
    ],
]);

function getKeyboardIconLookupId(key: KeyCode, platform: SimplifiedPlatform) {
    let filename = keyboardIconFilenames.get(key);
    if (filename == null) {
        return null;
    }
    if (typeof filename === "object") {
        filename = filename[platform];
        if (filename == null) {
            return null;
        }
    }

    return filename;
}

function getKeyboardIconMarkdownName(key: KeyCode, platform: SimplifiedPlatform) {
    let id = getKeyboardIconLookupId(key, platform);
    if (id == null) {
        return null;
    }

    return `$(augment-kb-${id})`;
}

// only works for UTF-16 chars, not UTF-32
export function getKeyboardIconFontChar(key: KeyCode, platform: SimplifiedPlatform) {
    let id = getKeyboardIconLookupId(key, platform);
    if (id == null) {
        return null;
    }
    const packageJson = getAugmentExtensionPackageJson();
    const charHex =
        packageJson?.contributes.icons[`augment-kb-${id}`].default.fontCharacter?.replace(
            "\\",
            ""
        ) ?? null;
    if (charHex == null) {
        return null;
    }
    return String.fromCharCode(parseInt(charHex, 16));
}

export function getKeyboardIconUris(
    key: KeyCode,
    context: vscode.ExtensionContext,
    platform: SimplifiedPlatform
): { light: vscode.Uri; dark: vscode.Uri } | null {
    let filename = keyboardIconFilenames.get(key);
    if (filename == null) {
        return null;
    }
    if (typeof filename === "object") {
        filename = filename[platform];
        if (filename == null) {
            return null;
        }
    }

    return {
        light: vscode.Uri.joinPath(
            context.extensionUri,
            "media",
            "keyboard",
            "light",
            `${filename}.svg`
        ),
        dark: vscode.Uri.joinPath(
            context.extensionUri,
            "media",
            "keyboard",
            "dark",
            `${filename}.svg`
        ),
    };
}

/**
 * Get the markdown icon references for the keybinding (these refer to our custom
 * font and are declared in package.json)
 * @param keybinding An AugmentKeybinding we want to get icons for.
 * @param platform a SimplifiedPlatform for the current env
 * @returns a string of markdown icons like `$(augment-kb-a) $(augment-kb-b)`.
 * Multi-chord keybindings are separated by 2 non-breaking spaces. If we were
 * unable to find the icons then we fall back to the prettyText as produced
 * by `keybinding.toPrettyString(platform)`.
 */
export function getKeybindingMarkdownIcons(
    keybinding: AugmentKeybinding | null,
    platform: SimplifiedPlatform
) {
    if (keybinding == null || keybinding.chords.length < 1) {
        return null;
    }

    if (keybinding.chords.find((chord) => chord instanceof ScanCodeChord)) {
        // Don't support ScanCodeChords because the ScanCodes that are immutable
        //   across keyboard layouts don't have pretty single-char representations.
        return keybinding.toPrettyString(platform);
    }
    const chordIcons = keybinding.chords.map((chord) => {
        const icons: Array<string | null> = [];
        const addImageForKey = (key: KeyCode) => {
            icons.push(getKeyboardIconMarkdownName(key, platform));
        };
        if (chord.ctrlKey) {
            addImageForKey(KeyCode.Ctrl);
        }
        if (chord.shiftKey) {
            addImageForKey(KeyCode.Shift);
        }
        if (chord.altKey) {
            addImageForKey(KeyCode.Alt);
        }
        if (chord.metaKey) {
            addImageForKey(KeyCode.Meta);
        }
        if ((chord as KeyCodeChord).keyCode) {
            addImageForKey((chord as KeyCodeChord).keyCode);
        }
        // if any image is missing we fall back to text.
        if (icons.some((i) => i == null)) {
            return escapeHtml(keybinding.toPrettyString(platform));
        }
        // cast is safe because we just checked for nulls.
        return icons.join("&nbsp;");
    });

    return chordIcons.join("&nbsp;&nbsp;");
}
