import { abortSignalAny } from "../abort-signal";

describe("abort-signal", () => {
    describe.each([
        ["with native AbortSignal.any", true],
        ["with fallback implementation", false],
    ])("%s", (_name: string, useNativeAny: boolean) => {
        let storedAny: any = undefined;

        beforeEach(() => {
            // Store original AbortSignal.any
            storedAny = (AbortSignal as any).any;
            if (!useNativeAny) {
                (AbortSignal as any).any = undefined;
            }
        });

        afterEach(() => {
            // Restore original AbortSignal.any
            (AbortSignal as any).any = storedAny;
            storedAny = undefined;
        });

        test("returns non-aborted signal when no input signals are aborted", () => {
            const signals = [new AbortController().signal, new AbortController().signal];
            const combined = abortSignalAny(signals);
            expect(combined.aborted).toBe(false);
        });

        test("returns aborted signal when any input signal is already aborted", () => {
            const controller1 = new AbortController();
            const controller2 = new AbortController();
            controller1.abort();

            const combined = abortSignalAny([controller1.signal, controller2.signal]);
            expect(combined.aborted).toBe(true);
        });

        test("aborts when one of the input signals aborts", () => {
            const controller1 = new AbortController();
            const controller2 = new AbortController();
            const combined = abortSignalAny([controller1.signal, controller2.signal]);

            controller2.abort();
            expect(combined.aborted).toBe(true);
        });

        test("works with empty signal array", () => {
            const combined = abortSignalAny([]);
            expect(combined.aborted).toBe(false);
        });

        test("cleans up listeners when aborted", () => {
            const controller1 = new AbortController();
            const controller2 = new AbortController();

            // Create spy on addEventListener and removeEventListener
            const addSpy1 = jest.spyOn(controller1.signal, "addEventListener");
            const removeSpy1 = jest.spyOn(controller1.signal, "removeEventListener");
            const addSpy2 = jest.spyOn(controller2.signal, "addEventListener");
            const removeSpy2 = jest.spyOn(controller2.signal, "removeEventListener");

            const _combined = abortSignalAny([controller1.signal, controller2.signal]);

            // Verify listeners were added
            expect(addSpy1).toHaveBeenCalledTimes(1);
            expect(addSpy2).toHaveBeenCalledTimes(1);

            // Abort one controller
            controller1.abort();

            // Verify all listeners were removed
            expect(removeSpy1).toHaveBeenCalledTimes(1);
            expect(removeSpy2).toHaveBeenCalledTimes(1);

            // Cleanup
            addSpy1.mockRestore();
            removeSpy1.mockRestore();
            addSpy2.mockRestore();
            removeSpy2.mockRestore();
        });

        test("handles multiple aborts gracefully", () => {
            const controller1 = new AbortController();
            const controller2 = new AbortController();
            const combined = abortSignalAny([controller1.signal, controller2.signal]);

            controller1.abort();
            controller2.abort();

            expect(combined.aborted).toBe(true);
        });

        test("triggers abort event when aborted", () => {
            const controller1 = new AbortController();
            const controller2 = new AbortController();
            const combined = abortSignalAny([controller1.signal, controller2.signal]);

            const abortHandler = jest.fn();
            combined.addEventListener("abort", abortHandler);

            controller1.abort();

            expect(abortHandler).toHaveBeenCalledTimes(1);
        });
    });
});
