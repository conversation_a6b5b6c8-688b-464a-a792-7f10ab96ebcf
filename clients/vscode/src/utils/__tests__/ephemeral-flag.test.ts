import {
    MutableTextDocument,
    Selection,
    TextEditor,
    Uri,
    window,
    workspace,
} from "../../__mocks__/vscode-mocks";
import { EphemeralObservable } from "../ephemeral-flag";

describe("EphemeralFlag", () => {
    let docs: MutableTextDocument[];
    beforeEach(() => {
        jest.useFakeTimers();

        docs = [
            new MutableTextDocument(
                Uri.parse(`file://example/a}`),
                "line 0\nline 1\nline 2\nline 3\nline 4\n"
            ),
            new MutableTextDocument(
                Uri.parse(`file://example/b}`),
                "line 0\nline 1\nline 2\nline 3\nline 4\n"
            ),
        ];

        window.visibleTextEditors = docs.map((doc) => {
            return new TextEditor(doc);
        });
        window.activeTextEditor = window.visibleTextEditors[0];
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("constructor initializes with correct value", () => {
        const flag = new EphemeralObservable<boolean>();
        expect(flag.value).toBe(undefined);
    });

    test("set sets the flag", () => {
        const flag = new EphemeralObservable<boolean>();
        expect(flag.value).toBe(undefined);
        flag.set(true);
        expect(flag.value).toBe(true);
    });

    test("clear clears the flag", () => {
        const flag = new EphemeralObservable<boolean>();
        flag.set(true);
        expect(flag.value).toBe(true);
        flag.clear();
        expect(flag.value).toBe(undefined);
    });

    describe("response to events", () => {
        it("clears the flag if change in active editor", async () => {
            const flag = new EphemeralObservable<boolean>();
            flag.set(true);
            expect(flag.value).toBe(true);
            await jest.advanceTimersByTimeAsync(1);
            window.activeTextEditor = window.visibleTextEditors[1];
            // We need to manually fire this event because our mocks aren't very good.
            window.activeTextEditorChanged.fire(window.visibleTextEditors[1]);
            await jest.advanceTimersByTimeAsync(1);
            expect(flag.value).toBe(undefined);
        });

        it("clears the flag if change in selection after debounce", async () => {
            const flag = new EphemeralObservable<boolean>();
            flag.set(true);
            expect(flag.value).toBe(true);
            await jest.advanceTimersByTimeAsync(1);
            // changing the selection is a bit of a hack.
            window.activeTextEditor!.selection = new Selection(2, 0, 2, 0);
            await jest.advanceTimersByTimeAsync(1);
            expect(flag.value).toBe(undefined);
        });

        it("leaves the flag set if selection changed within debounce", async () => {
            const flag = new EphemeralObservable<boolean>();
            flag.set(true);
            expect(flag.value).toBe(true);
            window.activeTextEditor!.selection = new Selection(2, 0, 2, 0);
            await jest.advanceTimersByTimeAsync(1);
            expect(flag.value).toBe(true);
        });

        it("clears the flag if document changed after debounce", async () => {
            const flag = new EphemeralObservable<boolean>();
            flag.set(!!docs[0]);
            expect(flag.value).toBe(true);
            await jest.advanceTimersByTimeAsync(1);
            // We need to manually fire this event because our mocks aren't very good.
            workspace.textDocumentChanged.fire(docs[0].insert(0, "a"));
            await jest.advanceTimersByTimeAsync(1);
            expect(flag.value).toBe(undefined);
        });

        it("leaves the flag set if document changed within debounce", async () => {
            const flag = new EphemeralObservable<boolean>();
            flag.set(!!docs[0]);
            expect(flag.value).toBe(true);
            // We need to manually fire this event because our mocks aren't very good.
            workspace.textDocumentChanged.fire(docs[0].insert(0, "a"));
            await jest.advanceTimersByTimeAsync(1);
            expect(flag.value).toBe(true);
        });
    });
});
