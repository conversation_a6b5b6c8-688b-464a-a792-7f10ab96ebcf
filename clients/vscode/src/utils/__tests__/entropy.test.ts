import { findEntropy, redactEntropy } from "../entropy";

describe("findEntropy", () => {
    it("should detect base64 encoded strings", async () => {
        const content = `./foo.sh hello --token "dGhpcyBpcyBhIHNlY3JldCB0b2tlbg==" --message "just a regular string"`;

        const results = findEntropy(content);

        expect(results).toHaveLength(1);
        expect(results[0]).toMatchObject({
            type: "Base64",
            secret: "dGhpcyBpcyBhIHNlY3JldCB0b2tlbg==", // pragma: allowlist secret
        });
    });

    it("should detect hex strings", async () => {
        const content = `./foo.sh --hexColor "#ff0000" --hexSecret "1234567890abcdef1234567890abcdef" --text "regular text"`;

        const results = findEntropy(content);

        expect(results).toHaveLength(1);
        expect(results[0]).toMatchObject({
            type: "HEX",
            secret: "1234567890abcdef1234567890abcdef", // pragma: allowlist secret
        });
    });

    it("should work without quote marks", async () => {
        const content = `./foo.sh --token dGhpcyBpcyBhIHNlY3JldCB0b2tlbg== --hexSecret 1234567890abcdef1234567890abcdef`; // pragma: allowlist secret

        const results = findEntropy(content);

        expect(results).toHaveLength(2);
    });

    it("should work with = arguments", async () => {
        const content = `./foo.sh --token=dGhpcyBpcyBhIHNlY3JldCB0b2tlbg== --hexSecret=1234567890abcdef1234567890abcdef`; // pragma: allowlist secret

        const results = findEntropy(content);

        expect(results).toHaveLength(3); // 3 not 2 because `hexSecret==...` is also detected as a base64 secret, which is fine
    });

    it("should not detect long English strings", async () => {
        const content = `./foo.sh --functionName "colorlessGreenIdeasSleepFuriously" --message "lorem_ipsum_dolor_sit_amet"`;

        const results = findEntropy(content);

        expect(results).toHaveLength(0);
    });

    it("should handle empty input", async () => {
        const results = findEntropy("");
        expect(results).toHaveLength(0);
    });

    it("should handle multiline content correctly", async () => {
        const content = `
            function getSecrets() {
                return {
                    token: "dGhpcyBpcyBhIHNlY3JldCB0b2tlbg==",
                    id: "regular-id"
                };
            }
        `;

        const results = findEntropy(content);

        expect(results).toHaveLength(1);
        expect(results[0]).toMatchObject({
            type: "Base64",
            secret: "dGhpcyBpcyBhIHNlY3JldCB0b2tlbg==", // pragma: allowlist secret
        });
    });

    it("should handle special characters and whitespace", async () => {
        const content = `
            const token\t=\t"dGhpcyBpcyBhIHNlY3JldCB0b2tlbg=="  ;
            const key =    "1234567890abcdef1234567890abcdef"    ;
        `;

        const results = findEntropy(content);

        expect(results).toHaveLength(2);
    });

    it("should not detect short strings as secrets", async () => {
        const content = `./foo.sh --shortBase64 "aGVsbG8=" --shortHex "1234abcd"`;

        const results = findEntropy(content);
        expect(results).toHaveLength(0);
    });

    it("should handle real-world JWT tokens", async () => {
        const content = `./foo.sh --jwt "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"`; // pragma: allowlist secret

        const results = findEntropy(content);

        expect(results).toHaveLength(1);
        expect(results[0]).toMatchObject({
            type: "Base64",
            secret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", // pragma: allowlist secret
        });
    });

    it("should avoid quadratic behavior", async () => {
        // One million repetations of a valid character should make test latency very noticeable if we have any quadratic effects
        // In practice this seems to take a couple of seconds when running the test locally
        const content = "9".repeat(1000000);
        const results = findEntropy(content);
        expect(results).toHaveLength(0);
    });
});

describe("redactEntropy", () => {
    it("should redact base64 encoded strings", async () => {
        const content = `./foo.sh hello --token "dGhpcyBpcyBhIHNlY3JldCB0b2tlbg==" --message "just a regular string"`;
        const expected = `./foo.sh hello --token "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" --message "just a regular string"`;

        const redactedContent = redactEntropy(content);

        expect(redactedContent).toEqual(expected);
    });

    it("should redact hex strings", async () => {
        const content = `./foo.sh --hexColor "#ff0000" --hexSecret "1234567890abcdef1234567890abcdef" --text "regular text"`;
        const expected = `./foo.sh --hexColor "#ff0000" --hexSecret "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" --text "regular text"`;

        const redactedContent = redactEntropy(content);

        expect(redactedContent).toEqual(expected);
    });

    it("should redact across multiple lines", async () => {
        const content = `
            function getSecrets() {
                return {
                    token: "dGhpcyBpcyBhIHNlY3JldCB0b2tlbg==",
                    key: "1234567890abcdef1234567890abcdef",
                    id: "regular-id"
                };
            }
        `;
        const expected = `
            function getSecrets() {
                return {
                    token: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
                    key: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
                    id: "regular-id"
                };
            }
        `;

        const redactedContent = redactEntropy(content);

        expect(redactedContent).toEqual(expected);
    });
});
