import { STREAM_CANCELLED } from "@augment-internal/sidecar-libs/src/api/types";

/**
 * Manages active streams for remote agents.
 * This class is responsible for creating and cancelling AbortControllers
 * for any stream that needs to be cancelled manually at some point
 */
export class StreamManager {
    private _activeStreams = new Map<string, AbortController>();

    /**
     * Start a new stream for an agent
     * @param streamId
     * @returns The AbortController for the stream
     */
    startStream(streamId: string): AbortController {
        this.cancelStream(streamId);

        const controller = new AbortController();
        this._activeStreams.set(streamId, controller);
        return controller;
    }

    /**
     * Cancel a stream
     * @param streamId The ID of the stream
     */
    cancelStream(streamId: string): void {
        const controller = this._activeStreams.get(streamId);
        if (controller) {
            controller.abort(STREAM_CANCELLED);
            this._activeStreams.delete(streamId);
        }
    }

    /**
     * Cancel all active streams
     */
    cancelAllStreams(): void {
        for (const [_, controller] of this._activeStreams.entries()) {
            controller.abort();
        }
        this._activeStreams.clear();
    }

    /**
     * Dispose of all resources
     */
    dispose(): void {
        this.cancelAllStreams();
    }
}
