import * as vscode from "vscode";

export const GHOST_TEXT_COLOR = "var(--vscode-editorGhostText-foreground)";

/**
 * @returns a decoration type that adds a 3em margin spacer
 */
export function buildRightSpacerDecoration() {
    return vscode.window.createTextEditorDecorationType({
        after: {
            contentText: " ",
            margin: "0 0 0 3em",
        },
    });
}

/**
 * @param keyDecorations - the keybindings to show, if undefined, the decoration
 * must be set later
 *
 * @returns an array of 5 decoration types that can be used for showing one
 * keybinding icon for each decoration type
 */
export function buildKeybindingDecoration(
    keyDecoration?: vscode.DecorationInstanceRenderOptions[]
) {
    const baseDeocation = {
        after: {
            color: GHOST_TEXT_COLOR,
            margin: "0 0.25em 0 0",
        },
    };
    const decorations = keyDecoration || [1, 2, 3, 4, 5];
    return decorations.map((_, index) =>
        vscode.window.createTextEditorDecorationType({
            ...baseDeocation,
            ...(keyDecoration ? keyDecoration[index] : {}),
        })
    );
}

/**
 * @param text - the text to show, if undefined, the text must be set later
 *
 * @returns a decoration type that can be used for showing ghost text
 */
export function buildGhostTextDecoration(text?: string) {
    return vscode.window.createTextEditorDecorationType({
        after: {
            color: GHOST_TEXT_COLOR,
            margin: "0 0 0 0.5em",
            contentText: text,
        },
    });
}

export function buildGhostDecoration(): [
    vscode.TextEditorDecorationType,
    Array<vscode.TextEditorDecorationType>,
    vscode.TextEditorDecorationType,
] {
    return [buildRightSpacerDecoration(), buildKeybindingDecoration(), buildGhostTextDecoration()];
}
