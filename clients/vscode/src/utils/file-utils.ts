import { AugmentLogger } from "@augment-internal/sidecar-libs/src/logging";
import path from "path";

import {
    deleteDirectory,
    deleteFile,
    directoryExists,
    fileExists,
    readDirectorySync,
    readFileUtf8,
    writeFileUtf8,
} from "./fs-utils";

/**
 * Executes a function while ensuring file backup and restoration.
 *
 * This function creates a backup of the specified file (if it exists), executes the provided
 * function, and then restores the original file content. If the file didn't exist originally
 * but was created during function execution, it will be removed. Additionally, any empty
 * parent directories that were created during execution will also be cleaned up.
 *
 * @param filePath - The absolute path to the file to backup and restore
 * @param fn - The async function to execute while the file is backed up
 * @returns A promise that resolves to the return value of the provided function
 *
 * @example
 * ```typescript
 * await withFileBackup('/path/to/settings.json', async () => {
 *   // Modify the file here
 *   await writeFileUtf8('/path/to/settings.json', newContent);
 *   return someResult;
 * });
 * // File is automatically restored to original state
 * ```
 */
export async function withFileBackup<T>(
    filePath: string,
    logger: AugmentLogger,
    fn: () => Promise<T>
): Promise<T> {
    let originalContent = "";
    let fileExisted = false;

    // Find the first existing parent directory
    let currentDir = path.dirname(filePath);
    while (
        // Stop at root
        currentDir !== path.dirname(currentDir) &&
        !directoryExists(currentDir)
    ) {
        currentDir = path.dirname(currentDir);
    }
    const firstExistingParentDir = currentDir;

    try {
        // Read original file if it exists
        if (fileExists(filePath)) {
            originalContent = await readFileUtf8(filePath);
            fileExisted = true;
        }
        return await fn();
    } finally {
        try {
            // Restore original file
            if (fileExisted) {
                await writeFileUtf8(filePath, originalContent);
                logger.debug(`Restored original file`);
            } else if (!fileExisted && fileExists(filePath)) {
                // If file didn't exist originally but was created, remove it
                await deleteFile(filePath);
                logger.debug(`Removed file that didn't exist originally`);

                // Remove any parent directories that were created during fn execution
                let parentDir = path.dirname(filePath);
                while (
                    // Stop at root
                    parentDir !== path.dirname(parentDir) &&
                    parentDir !== firstExistingParentDir &&
                    directoryExists(parentDir)
                ) {
                    try {
                        const entries = readDirectorySync(parentDir);
                        if (entries.length === 0) {
                            await deleteDirectory(parentDir);
                            logger.debug(`Removed empty directory: ${parentDir}`);
                            parentDir = path.dirname(parentDir);
                        } else {
                            break; // Directory not empty, stop cleanup
                        }
                    } catch (error) {
                        logger.warn(`Failed to check or remove directory ${parentDir}:`, error);
                        break;
                    }
                }
            }
        } catch (error) {
            logger.error(`Failed to restore the file:`, error);
        }
    }
}
