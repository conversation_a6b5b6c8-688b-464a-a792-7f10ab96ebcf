import { RingBuffer } from "@augment-internal/sidecar-libs/src/ring-buffer";
import * as vscode from "vscode";

import { DisposableService } from "./disposable-service";

// Store items in a ring buffer and emit an event when a new item is added.
export class RecentItems<T> extends DisposableService {
    private readonly _ringBuffer;
    private readonly _newItemEventEmitter = new vscode.EventEmitter<T>();

    /**
     * RecentItems stores recent items in a ring buffer and emits an event
     * when a new item is added.
     * @param bufferSize - The maximum number of items to store.
     * @param _itemVerifier - A function that returns true if the item should
     * be stored.
     */
    constructor(
        bufferSize: number = 100,
        private readonly _itemVerifier: (item: T) => boolean = () => true
    ) {
        super();
        this._ringBuffer = new RingBuffer<T>(bufferSize);
        this.addDisposable(this._newItemEventEmitter);
    }

    get onNewItems() {
        return this._newItemEventEmitter.event;
    }

    get items(): T[] {
        return this._ringBuffer.slice();
    }

    get mostRecentItem(): T | undefined {
        return this._ringBuffer.at(-1);
    }

    // NOTE: This can be made private after moving to the new providers.
    addItem(item: T) {
        if (!this._itemVerifier(item)) {
            return;
        }

        this._ringBuffer.addItem(item);
        this._newItemEventEmitter.fire(item);
    }
}
