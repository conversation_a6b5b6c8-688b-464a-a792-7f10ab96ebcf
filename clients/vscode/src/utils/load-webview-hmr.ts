import type * as vscode from "vscode";

export async function loadWebviewHmr(
    hmrUrl: string,
    filePath: string,
    csp: string,
    sentryConfig?: string
) {
    if (!isHmr()) {
        throw new Error("Not in HMR mode");
    }

    const response = await fetch(`${hmrUrl}/${filePath}`);
    if (!response.ok) {
        throw new Error(`Failed to load ${filePath} from ${hmrUrl}: ${response.statusText}`);
    }
    const webviewHtml = await response.text();
    //Sometimes this will return an empty string and I am not sure why. In this case we through an error,
    // and let the caller handle it.
    if (!webviewHtml?.trim()) {
        throw new Error(
            `Empty response when loading ${filePath} from ${hmrUrl}: ${response.statusText}`
        );
    }
    // Add CSP meta tag to the fetched HTML
    return loadWebview(webviewHtml, hmrUrl, csp, sentryConfig);
}

let _cachedHmrUrl: string | undefined = undefined;
export async function loadHmrUrl(extensionUri: vscode.Uri): Promise<string> {
    if (_cachedHmrUrl) {
        return _cachedHmrUrl;
    }
    const fs = await import("fs");
    const filePath = extensionUri.fsPath + "/.augment-hmr-env";
    const file = fs
        .readFileSync(filePath, "utf8")
        .split("\n")
        .filter((line) => !/^\s*(#|$)/.test(line))
        .reduce(
            (ret, line) => {
                const [key, value] = line.split("=");
                ret[key] = value;
                return ret;
            },
            {} as Record<string, string>
        );
    _cachedHmrUrl = file.AUGMENT_HMR;
    if (!_cachedHmrUrl) {
        throw new Error(`Failed to load HMR url from '${filePath}'`);
    }
    return _cachedHmrUrl;
}
export function loadWebview(content: string, baseUrl: string, csp: string, sentryConfig?: string) {
    let headContent = `<head><base href="${baseUrl}" /><meta http-equiv="Content-Security-Policy" content="${csp}" />`;

    if (sentryConfig) {
        // Escape the JSON to prevent XSS
        const escapedConfig = sentryConfig.replace(/</g, "\\u003c").replace(/>/g, "\\u003e");
        headContent += `
    <script type="text/javascript">
        window.augmentFlags = window.augmentFlags || {};
        window.augmentFlags.sentry = ${escapedConfig};
    </script>`;
    }

    return content.replace("<head>", headContent);
}

export function isHmr() {
    return !!process.env.AUGMENT_IS_HMR && process.env.AUGMENT_JS_ENV !== "production";
}
