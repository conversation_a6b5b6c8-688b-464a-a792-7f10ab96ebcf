import { diffLines } from "diff";
import * as vscode from "vscode";

import { openDiffInBuffer } from "./webviews/open-diff-in-buffer";

// Command ID for opening diff in buffer
const OPEN_DIFF_COMMAND = "augment.openDiffInBuffer";

// Register the command if not already registered
let commandRegistered = false;
function ensureCommandRegistered() {
    if (!commandRegistered) {
        vscode.commands.registerCommand(
            OPEN_DIFF_COMMAND,
            async (oldContents: string, newContents: string, filePath: string) => {
                try {
                    await openDiffInBuffer(oldContents, newContents, filePath);
                } catch (error) {
                    void vscode.window.showErrorMessage(`Failed to open diff: ${String(error)}`);
                }
            }
        );
        commandRegistered = true;
    }
}

interface RemovedRange {
    range: vscode.Range;
    count: number;
    removedContent: string;
}

interface ChangedRange {
    range: vscode.Range;
    originalContent: string;
}

interface DiffAnalysisResult {
    addedRanges: vscode.Range[];
    removedRanges: RemovedRange[];
    changedRanges: ChangedRange[];
}

/**
 * Merges adjacent ranges of the same type into contiguous blocks.
 * @param ranges - Array of ranges to merge
 * @returns Array of merged ranges
 */
function mergeAdjacentRanges(ranges: vscode.Range[]): vscode.Range[] {
    if (ranges.length === 0) {
        return [];
    }

    // Sort ranges by start line
    const sortedRanges = [...ranges].sort((a, b) => a.start.line - b.start.line);
    const merged: vscode.Range[] = [];
    let current = sortedRanges[0];

    for (let i = 1; i < sortedRanges.length; i++) {
        const next = sortedRanges[i];

        // Check if ranges are adjacent (next line starts immediately after current line ends)
        // For whole line decorations, we check if the next line is consecutive
        if (next.start.line === current.end.line + 1) {
            // Merge the ranges - extend the current range to include the next range
            current = new vscode.Range(current.start, next.end);
        } else {
            // Ranges are not adjacent, add current to merged and start new range
            merged.push(current);
            current = next;
        }
    }

    // Add the last range
    merged.push(current);
    return merged;
}

/**
 * Merges adjacent changed ranges and consolidates their original content.
 * @param changedRanges - Array of changed ranges to merge
 * @returns Array of merged changed ranges with consolidated content
 */
function mergeAdjacentChangedRanges(changedRanges: ChangedRange[]): ChangedRange[] {
    if (changedRanges.length === 0) {
        return [];
    }

    // Sort ranges by start line
    const sortedRanges = [...changedRanges].sort((a, b) => a.range.start.line - b.range.start.line);
    const merged: ChangedRange[] = [];
    let current = sortedRanges[0];

    for (let i = 1; i < sortedRanges.length; i++) {
        const next = sortedRanges[i];

        // Check if ranges are adjacent
        if (next.range.start.line === current.range.end.line + 1) {
            // Merge the ranges and consolidate content
            current = {
                range: new vscode.Range(current.range.start, next.range.end),
                originalContent: current.originalContent + "\n" + next.originalContent,
            };
        } else {
            // Ranges are not adjacent, add current to merged and start new range
            merged.push(current);
            current = next;
        }
    }

    // Add the last range
    merged.push(current);
    return merged;
}

/**
 * Merges adjacent removed ranges and consolidates their content and counts.
 * @param removedRanges - Array of removed ranges to merge
 * @returns Array of merged removed ranges with consolidated content and counts
 */
function mergeAdjacentRemovedRanges(removedRanges: RemovedRange[]): RemovedRange[] {
    if (removedRanges.length === 0) {
        return [];
    }

    // Sort ranges by start line
    const sortedRanges = [...removedRanges].sort((a, b) => a.range.start.line - b.range.start.line);
    const merged: RemovedRange[] = [];
    let current = sortedRanges[0];

    for (let i = 1; i < sortedRanges.length; i++) {
        const next = sortedRanges[i];

        // Check if ranges are adjacent
        if (next.range.start.line === current.range.end.line + 1) {
            // Merge the ranges and consolidate content and counts
            current = {
                range: new vscode.Range(current.range.start, next.range.end),
                count: current.count + next.count,
                removedContent: current.removedContent + "\n" + next.removedContent,
            };
        } else {
            // Ranges are not adjacent, add current to merged and start new range
            merged.push(current);
            current = next;
        }
    }

    // Add the last range
    merged.push(current);
    return merged;
}

/**
 * Scrolls to the first decoration in the editor.
 *
 * This utility function can be used independently of addDiffDecorations to scroll
 * to the first decoration in an editor. It's useful when you want to scroll to a
 * specific range without adding decorations, or when you want more control over
 * when the scrolling happens.
 *
 * Example usage:
 * ```typescript
 * // Scroll to the first decoration in the editor
 * const addedRanges = [...]; // Array of vscode.Range for added lines
 * const removedRanges = [...]; // Array of vscode.Range for removed lines
 * scrollToFirstDiffDecoration(editor, addedRanges, removedRanges);
 * ```
 *
 * @param editor - The VS Code text editor
 * @param addedRanges - Array of ranges for added lines
 * @param modifiedRanges - Array of ranges for removed or changed lines
 * @param revealType - The type of reveal to use when scrolling (default: InCenterIfOutsideViewport)
 * @returns True if scrolled to a decoration, false otherwise
 */
export function scrollToFirstDiffDecoration(
    editor: vscode.TextEditor,
    addedRanges: vscode.Range[],
    modifiedRanges: vscode.Range[],
    revealType?: vscode.TextEditorRevealType
): boolean {
    if (addedRanges.length === 0 && modifiedRanges.length === 0) {
        return false;
    }

    // Determine the first decoration to scroll to (either added, removed, or changed)
    let firstRange: vscode.Range | undefined;

    if (addedRanges.length > 0 && modifiedRanges.length > 0) {
        // If we have both types, find the one that appears first in the document
        const firstAddedLine = addedRanges[0].start.line;
        const firstModifiedLine = modifiedRanges[0].start.line;
        firstRange = firstAddedLine <= firstModifiedLine ? addedRanges[0] : modifiedRanges[0];
    } else if (addedRanges.length > 0) {
        firstRange = addedRanges[0];
    } else if (modifiedRanges.length > 0) {
        firstRange = modifiedRanges[0];
    }

    if (firstRange) {
        // Scroll to the range with the specified reveal type or default to InCenterIfOutsideViewport
        editor.revealRange(
            firstRange,
            revealType ?? vscode.TextEditorRevealType.InCenterIfOutsideViewport
        );
        return true;
    }

    return false;
}

/**
 * Gets theme-appropriate colors for diff decorations
 *
 * @returns Object containing colors for added, removed, and changed lines based on current theme
 */
function getDiffDecorationColors(): {
    added: { background: string; text: string };
    removed: { background: string; text: string; border: string };
    changed: { background: string; text: string; border: string };
} {
    const isDarkTheme =
        vscode.window.activeColorTheme.kind === vscode.ColorThemeKind.Dark ||
        vscode.window.activeColorTheme.kind === vscode.ColorThemeKind.HighContrast;

    return {
        added: {
            background: isDarkTheme ? "rgba(0, 255, 0, 0.1)" : "rgba(0, 200, 0, 0.05)",
            text: isDarkTheme ? "rgba(150, 255, 150, 0.9)" : "rgba(0, 100, 0, 0.8)",
        },
        removed: {
            background: isDarkTheme ? "rgba(255, 0, 0, 0.05)" : "rgba(255, 0, 0, 0.05)",
            text: isDarkTheme ? "rgba(255, 200, 200, 0.9)" : "rgba(150, 0, 0, 0.8)",
            border: isDarkTheme ? "rgba(150, 0, 0, 0.3)" : "rgba(150, 0, 0, 0.3)",
        },
        changed: {
            background: isDarkTheme ? "rgba(255, 180, 50, 0.15)" : "rgba(255, 180, 50, 0.08)",
            text: isDarkTheme ? "rgba(255, 210, 120, 0.9)" : "rgba(200, 120, 0, 0.8)",
            border: isDarkTheme ? "rgba(200, 120, 0, 0.3)" : "rgba(200, 120, 0, 0.3)",
        },
    };
}

/**
 * Creates decoration types for diff highlighting based on the current theme.
 *
 * @returns Object containing decoration types for added, removed, and changed lines
 */
function createDiffDecorationTypes(): {
    addedLineDecoration: vscode.TextEditorDecorationType;
    removedLineDecoration: vscode.TextEditorDecorationType;
    changedLineDecoration: vscode.TextEditorDecorationType;
} {
    const colors = getDiffDecorationColors();

    return {
        addedLineDecoration: vscode.window.createTextEditorDecorationType({
            backgroundColor: colors.added.background,
            isWholeLine: true,
            after: {
                color: colors.added.text,
                margin: "0 0 0 1em",
            },
        }),

        removedLineDecoration: vscode.window.createTextEditorDecorationType({
            isWholeLine: true,
            after: {
                // Use 'after' to show the indicator at the end of the line
                // The contentText will be set individually for each range
                color: colors.removed.text,
                border: `1px solid ${colors.removed.border}`,
                backgroundColor: colors.removed.background,
                margin: "0 0 0 1em",
            },
        }),

        changedLineDecoration: vscode.window.createTextEditorDecorationType({
            backgroundColor: colors.changed.background,
            isWholeLine: true,
            after: {
                color: colors.changed.text,
                margin: "0 0 0 1em",
            },
        }),
    };
}

/**
 * Creates a hover message for a decoration with a clickable diff link.
 * @param title - The title for the hover message
 * @param content - The content to display in the hover message
 * @param originalContent - The original content for the diff
 * @param modifiedContent - The modified content for the diff
 * @param filePath - The file path for the diff
 * @returns A markdown string for the hover message
 */
function createHoverMessage(
    title: string,
    content: string,
    originalContent: string,
    modifiedContent: string,
    filePath: string
): vscode.MarkdownString {
    const hoverMessage = new vscode.MarkdownString();
    hoverMessage.appendMarkdown(`**${title}:**\n\n`);
    hoverMessage.appendCodeblock(content);

    // Add clickable link to open diff
    const diffArgs = [originalContent, modifiedContent, filePath];
    const commandUri = `command:${OPEN_DIFF_COMMAND}?${encodeURIComponent(JSON.stringify(diffArgs))}`;
    hoverMessage.appendMarkdown(`\n\n[Open Diff View](${commandUri})`);

    hoverMessage.isTrusted = true;
    return hoverMessage;
}

/**
 * Calculates similarity between two strings using a simple approach.
 * @param str1 - First string
 * @param str2 - Second string
 * @returns Similarity score between 0 and 1
 */
function calculateSimilarity(str1: string, str2: string): number {
    if (str1 === str2) {
        return 1.0;
    }
    if (str1.length === 0 || str2.length === 0) {
        return 0.0;
    }

    // Simple character-based similarity
    const maxLength = Math.max(str1.length, str2.length);
    let commonChars = 0;
    const minLength = Math.min(str1.length, str2.length);

    for (let i = 0; i < minLength; i++) {
        if (str1[i] === str2[i]) {
            commonChars++;
        }
    }

    return commonChars / maxLength;
}

/**
 * Analyzes diffs between original and modified content using the diff library.
 * @param originalContent - The original content
 * @param modifiedContent - The modified content
 * @param editor - The VS Code text editor
 * @returns Object containing arrays of ranges for added, removed, and changed lines
 */
function analyzeDiffs(
    originalContent: string,
    modifiedContent: string,
    editor: vscode.TextEditor
): DiffAnalysisResult {
    const changes = diffLines(originalContent, modifiedContent);

    const addedRanges: vscode.Range[] = [];
    const removedRanges: RemovedRange[] = [];
    const changedRanges: ChangedRange[] = [];

    let currentLine = 0;
    const similarityThreshold = 0.3;

    // Track removed lines for potential matching with added lines
    const removedLines: { content: string; lineNumber: number; originalContent: string }[] = [];

    for (const change of changes) {
        if (!change.added && !change.removed) {
            // Unchanged lines - just advance the line counter
            currentLine += change.count || 0;
        } else if (change.removed) {
            // Store removed lines for potential matching
            const lines = change.value
                .split("\n")
                .filter((line) => line.length > 0 || change.value.endsWith("\n"));
            for (const line of lines) {
                if (line.length > 0 || change.value.endsWith("\n")) {
                    removedLines.push({
                        content: line,
                        lineNumber: currentLine,
                        originalContent: line,
                    });
                }
            }
        } else if (change.added) {
            // Process added lines
            const lines = change.value
                .split("\n")
                .filter((line) => line.length > 0 || change.value.endsWith("\n"));

            for (const line of lines) {
                if (line.length > 0 || change.value.endsWith("\n")) {
                    if (currentLine < editor.document.lineCount) {
                        const range = new vscode.Range(
                            new vscode.Position(currentLine, 0),
                            new vscode.Position(
                                currentLine,
                                editor.document.lineAt(currentLine).text.length
                            )
                        );

                        // Check if this added line is similar to any removed line
                        let bestMatch = null;
                        let bestSimilarity = 0;

                        for (const removed of removedLines) {
                            const similarity = calculateSimilarity(removed.content, line);
                            if (similarity >= similarityThreshold && similarity > bestSimilarity) {
                                bestMatch = removed;
                                bestSimilarity = similarity;
                            }
                        }

                        if (bestMatch) {
                            // This is a changed line
                            changedRanges.push({
                                range,
                                originalContent: bestMatch.originalContent,
                            });
                            // Remove the matched line so it's not matched again
                            const index = removedLines.indexOf(bestMatch);
                            if (index > -1) {
                                removedLines.splice(index, 1);
                            }
                        } else {
                            // This is a pure addition
                            addedRanges.push(range);
                        }
                    }
                    currentLine++;
                }
            }
        }
    }

    // Group remaining removed lines by their position and create decorations
    const removedGroups = new Map<number, { content: string[]; count: number }>();

    for (const removed of removedLines) {
        const lineAbove = Math.max(0, removed.lineNumber - 1);
        if (!removedGroups.has(lineAbove)) {
            removedGroups.set(lineAbove, { content: [], count: 0 });
        }
        const group = removedGroups.get(lineAbove)!;
        group.content.push(removed.content);
        group.count++;
    }

    // Create removed range decorations
    for (const [lineAbove, group] of Array.from(removedGroups.entries())) {
        if (lineAbove < editor.document.lineCount) {
            const range = new vscode.Range(
                new vscode.Position(lineAbove, 0),
                new vscode.Position(lineAbove, editor.document.lineAt(lineAbove).text.length)
            );
            removedRanges.push({
                range,
                count: group.count,
                removedContent: group.content.join("\n"),
            });
        }
    }

    return { addedRanges, removedRanges, changedRanges };
}

/**
 * Adds decorations to the editor to highlight added, removed, and changed lines.
 *
 * @param editor - The VS Code text editor
 * @param originalContent - The original content of the file
 * @param modifiedContent - The modified content of the file
 * @param options - Additional options for the decorations
 * @param options.scrollToFirstDecoration - Whether to scroll to the first decoration (default: false)
 * @param options.revealType - The type of reveal to use when scrolling (default: InCenterIfOutsideViewport)
 * @returns The decoration types created (can be used to dispose them later)
 */
export function addDiffDecorations(
    editor: vscode.TextEditor,
    originalContent: string,
    modifiedContent: string,
    options?: {
        scrollToFirstDecoration?: boolean;
        revealType?: vscode.TextEditorRevealType;
    }
): vscode.TextEditorDecorationType[] {
    // Ensure the command is registered
    ensureCommandRegistered();
    // Create decoration types
    const decorationTypes = createDiffDecorationTypes();

    // Analyze diffs
    const { addedRanges, removedRanges, changedRanges } = analyzeDiffs(
        originalContent,
        modifiedContent,
        editor
    );

    // Merge adjacent ranges for more efficient decorations
    const mergedAddedRanges = mergeAdjacentRanges(addedRanges);
    const mergedChangedRanges = mergeAdjacentChangedRanges(changedRanges);
    const mergedRemovedRanges = mergeAdjacentRemovedRanges(removedRanges);

    // Apply added line decorations
    editor.setDecorations(decorationTypes.addedLineDecoration, mergedAddedRanges);

    // Apply changed line decorations (amber/orange for updated lines) with hover showing original content
    const changedDecorations = mergedChangedRanges.map((item) => ({
        range: item.range,
        hoverMessage: createHoverMessage(
            "Original content",
            item.originalContent,
            originalContent,
            modifiedContent,
            editor.document.fileName
        ),
    }));
    editor.setDecorations(decorationTypes.changedLineDecoration, changedDecorations);

    // Apply removed line decorations with counters and hover showing removed content
    const colors = getDiffDecorationColors();
    const removedDecorations = mergedRemovedRanges.map((item) => ({
        range: item.range,
        renderOptions: {
            after: {
                contentText: `⊖ ${item.count} line${item.count === 1 ? "" : "s"} removed`,
                color: colors.removed.text,
                border: `1px solid ${colors.removed.border}`,
                backgroundColor: colors.removed.background,
                margin: "0 0 0 1em",
            },
        },
        hoverMessage: createHoverMessage(
            "Removed content",
            item.removedContent,
            originalContent,
            modifiedContent,
            editor.document.fileName
        ),
    }));
    editor.setDecorations(decorationTypes.removedLineDecoration, removedDecorations);

    // Scroll to the first decoration if requested
    if (options?.scrollToFirstDecoration) {
        scrollToFirstDiffDecoration(
            editor,
            mergedAddedRanges,
            [
                ...mergedRemovedRanges.map((r) => r.range),
                ...mergedChangedRanges.map((r) => r.range),
            ],
            options.revealType
        );
    }

    // Return the decoration types so they can be disposed later if needed
    return [
        decorationTypes.addedLineDecoration,
        decorationTypes.removedLineDecoration,
        decorationTypes.changedLineDecoration,
    ];
}

/**
 * Adds decorations to the editor with an option to use individual line decorations instead of merged ranges.
 * This function provides backward compatibility and allows for testing both approaches.
 *
 * @param editor - The VS Code text editor
 * @param originalContent - The original content of the file
 * @param modifiedContent - The modified content of the file
 * @param options - Additional options for the decorations
 * @param options.scrollToFirstDecoration - Whether to scroll to the first decoration (default: false)
 * @param options.revealType - The type of reveal to use when scrolling (default: InCenterIfOutsideViewport)
 * @param options.useMergedRanges - Whether to merge adjacent ranges (default: true)
 * @returns The decoration types created (can be used to dispose them later)
 */
export function addDiffDecorationsWithOptions(
    editor: vscode.TextEditor,
    originalContent: string,
    modifiedContent: string,
    options?: {
        scrollToFirstDecoration?: boolean;
        revealType?: vscode.TextEditorRevealType;
        useMergedRanges?: boolean;
    }
): vscode.TextEditorDecorationType[] {
    // Ensure the command is registered
    ensureCommandRegistered();

    // Create decoration types
    const decorationTypes = createDiffDecorationTypes();

    // Analyze diffs
    const { addedRanges, removedRanges, changedRanges } = analyzeDiffs(
        originalContent,
        modifiedContent,
        editor
    );

    // Use merged or individual ranges based on options
    const useMergedRanges = options?.useMergedRanges ?? true;
    const finalAddedRanges = useMergedRanges ? mergeAdjacentRanges(addedRanges) : addedRanges;
    const finalChangedRanges = useMergedRanges
        ? mergeAdjacentChangedRanges(changedRanges)
        : changedRanges;
    const finalRemovedRanges = useMergedRanges
        ? mergeAdjacentRemovedRanges(removedRanges)
        : removedRanges;

    // Apply added line decorations
    editor.setDecorations(decorationTypes.addedLineDecoration, finalAddedRanges);

    // Apply changed line decorations (amber/orange for updated lines) with hover showing original content
    const changedDecorations = finalChangedRanges.map((item) => ({
        range: item.range,
        hoverMessage: createHoverMessage(
            "Original content",
            item.originalContent,
            originalContent,
            modifiedContent,
            editor.document.fileName
        ),
    }));
    editor.setDecorations(decorationTypes.changedLineDecoration, changedDecorations);

    // Apply removed line decorations with counters and hover showing removed content
    const colors = getDiffDecorationColors();
    const removedDecorations = finalRemovedRanges.map((item) => ({
        range: item.range,
        renderOptions: {
            after: {
                contentText: `⊖ ${item.count} line${item.count === 1 ? "" : "s"} removed`,
                color: colors.removed.text,
                border: `1px solid ${colors.removed.border}`,
                backgroundColor: colors.removed.background,
                margin: "0 0 0 1em",
            },
        },
        hoverMessage: createHoverMessage(
            "Removed content",
            item.removedContent,
            originalContent,
            modifiedContent,
            editor.document.fileName
        ),
    }));
    editor.setDecorations(decorationTypes.removedLineDecoration, removedDecorations);

    // Scroll to the first decoration if requested
    if (options?.scrollToFirstDecoration) {
        scrollToFirstDiffDecoration(
            editor,
            finalAddedRanges,
            [...finalRemovedRanges.map((r) => r.range), ...finalChangedRanges.map((r) => r.range)],
            options.revealType
        );
    }

    // Return the decoration types so they can be disposed later if needed
    return [
        decorationTypes.addedLineDecoration,
        decorationTypes.removedLineDecoration,
        decorationTypes.changedLineDecoration,
    ];
}

/**
 * Automatically removes decorations after a specified delay.
 *
 * @param decorations - The decoration types to remove
 * @param options - Options for auto-removal
 * @param options.minDelayMs - The minimum delay in milliseconds before removing the decorations (default: 10000ms)
 * @param options.maxDelayMs - The maximum delay in milliseconds before removing the decorations (default: 30000ms)
 */
export function autoRemoveDecorations(
    decorations: vscode.TextEditorDecorationType[],
    options: {
        minDelayMs?: number;
        maxDelayMs?: number;
    }
): void {
    // Calculate an appropriate delay based on the number of decorations
    // Examples:
    // - 1 decoration: baseDelay * log4(2) ≈ 5000ms
    // - 5 decorations: baseDelay * log4(6) ≈ 11600ms
    // - 10 decorations: baseDelay * log4(11) ≈ 15400ms
    // - 100 decorations: baseDelay * log4(101) ≈ 33300ms
    const baseDelay = 10000; // 10 seconds
    const scaledDelay = baseDelay * (Math.log(decorations.length + 1) / Math.log(4));

    const { minDelayMs = 10000, maxDelayMs = 30000 } = options;
    const finalDelay = Math.min(Math.max(scaledDelay, minDelayMs), maxDelayMs);

    setTimeout(() => {
        decorations.forEach((decoration) => decoration.dispose());
    }, finalDelay);
}

/**
 * Removes decorations when the document changes.
 *
 * @param editor - The VS Code text editor
 * @param decorations - The decoration types to remove
 * @returns A disposable that can be used to stop listening for changes
 */
export function removeDecorationsOnEdit(
    editor: vscode.TextEditor,
    decorations: vscode.TextEditorDecorationType[]
): vscode.Disposable {
    // Create a disposable to track the change listener
    const disposables: vscode.Disposable[] = [];

    // Register a document change listener
    const changeListener = vscode.workspace.onDidChangeTextDocument((event) => {
        // Check if the changed document is the one we're watching
        if (event.document.uri.toString() === editor.document.uri.toString()) {
            // Remove the decorations
            decorations.forEach((decoration) => decoration.dispose());

            // Dispose the listener since we no longer need it
            disposables.forEach((d) => void d.dispose());
        }
    });

    // Add the change listener to our disposables
    disposables.push(changeListener);

    // Return a disposable that will clean up the listener when disposed
    return {
        dispose: () => {
            disposables.forEach((d) => void d.dispose());
        },
    };
}
