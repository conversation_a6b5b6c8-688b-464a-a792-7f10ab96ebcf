export class IncrementalTimer {
    public readonly start = Date.now();
    public readonly increments: Array<{ name: string; end: number }> = [];

    constructor(public readonly name?: string) {}

    public charge(name: string) {
        this.increments.push({ name: name, end: Date.now() });
    }

    public *[Symbol.iterator](): Generator<[string, number]> {
        let prev = this.start;
        for (const { name, end } of this.increments) {
            yield [name, end - prev];
            prev = end;
        }
        yield ["total", prev - this.start];
    }

    public format(): string {
        let str = this.name ? `${this.name}:\n` : "";
        return (
            str +
            Array.from(this)
                .map(([name, duration]) => `  - ${name}: ${duration} ms`)
                .join("\n")
        );
    }
}

class Metric {
    private _value = 0;

    protected _add(n = 1) {
        this._value += n;
    }

    protected _invalidate() {
        this._value = Number.NaN;
    }

    public get value(): number {
        return this._value;
    }
}

export class CounterMetric extends Metric {
    public increment(n = 1) {
        this._add(n);
    }
}

export class TimingMetric extends Metric {
    private _start: number | undefined = undefined;

    public start() {
        this._start = Date.now();
    }

    public stop() {
        if (this._start === undefined) {
            this._invalidate();
        } else {
            this._add(Date.now() - this._start);
        }
    }
}

export class MetricsCollector {
    public readonly counters = new Map<string, CounterMetric>();
    public readonly timings = new Map<string, TimingMetric>();

    constructor(public readonly name?: string) {}

    public counterMetric(name: string): CounterMetric {
        let metric = this.counters.get(name);
        if (metric === undefined) {
            metric = new CounterMetric();
            this.counters.set(name, metric);
        }
        return metric;
    }

    public timingMetric(name: string): TimingMetric {
        let metric = this.timings.get(name);
        if (metric === undefined) {
            metric = new TimingMetric();
            this.timings.set(name, metric);
        }
        return metric;
    }

    public format(): string {
        const header = this.name ? `${this.name}:` : "";
        const counters = Array.from(this.counters.entries())
            .map(([name, metric]) => `  - ${name}: ${metric.value}`)
            .join("\n");
        const separator = "  - timing stats:";
        const timings = Array.from(this.timings.entries())
            .map(([name, metric]) => `    - ${name}: ${metric.value} ms`)
            .join("\n");
        return header + "\n" + counters + "\n" + separator + "\n" + timings;
    }
}
