// PathAcceptance is an interface for indicating whether a path name is considered to be accepted
// according to some criteria.
export interface PathAcceptance {
    // `accepted` indicates whether the path is accepted.
    accepted: boolean;

    // `format` returns a string describing the reason for the path being accepted or rejected.
    format(): string;
}

// AcceptPath is a base class for all forms of path acceptance.
export abstract class AcceptPath implements PathAcceptance {
    public readonly accepted: boolean = true;
    public abstract format(): string;
}

// RejectPath is a base class for all forms of path rejection.
export abstract class RejectPath implements PathAcceptance {
    public readonly accepted: boolean = false;
    public abstract format(): string;
}
