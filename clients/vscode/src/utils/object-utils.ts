type NestedObject = Record<string, any>;

export function flattenObject(
    obj: NestedObject,
    parentKey = "",
    result: Record<string, string> = {}
): Record<string, string> {
    for (const key in obj) {
        if (Object.hasOwn(obj, key)) {
            const newKey = parentKey ? `${parentKey}.${key}` : key;

            // If the value is an object, recurse, otherwise store the value as a string
            if (typeof obj[key] === "object" && obj[key] !== null) {
                flattenObject(obj[key], newKey, result); // eslint-disable-line @typescript-eslint/no-unsafe-argument
            } else {
                result[newKey] = String(obj[key]); // Ensure the value is a string
            }
        }
    }
    return result;
}
