import simpleGit, { SimpleGit, StatusResult } from "simple-git";

import { ChangedFileStats, CommitMessagePromptData, PromptDataOptions } from "../types";

export const EMPTY_FILE_STATS: ChangedFileStats = {
    /* eslint-disable @typescript-eslint/naming-convention */
    added_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    broken_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    copied_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    deleted_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    modified_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    renamed_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    unmerged_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    unknown_file_stats: {
        changed_file_count: 0,
        per_file_change_stats_head: [],
        per_file_change_stats_tail: [],
    },
    /* eslint-enable @typescript-eslint/naming-convention */
};

export class CommitMessagePromptPreparer {
    private git?: SimpleGit;

    constructor() {
        this.git = undefined;
    }

    public _updateBaseDir(baseDir: string) {
        try {
            this.git = simpleGit({
                baseDir: baseDir,
                binary: "git",
                maxConcurrentProcesses: 6,
                trimmed: false,
            });
        } catch {
            this.git = undefined;
        }
    }

    private countTokens(str: string): number {
        return Math.ceil(str.length / 3);
    }

    private async getChangedFileStats(options: PromptDataOptions): Promise<ChangedFileStats> {
        const status = await this.git!.status();
        const changeTypes: (keyof StatusResult)[] = ["created", "deleted", "modified", "renamed"];
        /* eslint-disable @typescript-eslint/naming-convention */
        const changedFileStats: ChangedFileStats = EMPTY_FILE_STATS;
        /* eslint-enable @typescript-eslint/naming-convention */

        const stagedFiles = new Set(status.staged);

        for (const type of changeTypes) {
            let files: string[] = [];
            if (type === "renamed") {
                files = status.renamed.map((item) => item.to);
            } else {
                files = status[type] as string[];
            }

            if (options.onlyUseStagedChanges) {
                files = files.filter((file) => stagedFiles.has(file));
            }

            if (files.length > 0) {
                let fileStatsType: string = type;
                if (type === "created") {
                    // A hack to match the FE types to the expected BE types
                    fileStatsType = "added";
                }
                const statsKey = `${fileStatsType}_file_stats` as keyof ChangedFileStats;
                // eslint-disable-next-line @typescript-eslint/naming-convention
                changedFileStats[statsKey].changed_file_count = files.length;

                const fileStats = await Promise.all(
                    files.map(async (file) => {
                        const { insertions, deletions } = await this.getInsertionDeletionStats(
                            options.onlyUseStagedChanges,
                            [file]
                        );
                        return {
                            /* eslint-disable @typescript-eslint/naming-convention */
                            file_path: file,
                            insertion_count: insertions,
                            deletion_count: deletions,
                            old_file_path:
                                type === "renamed"
                                    ? status.renamed.find((item) => item.to === file)?.from || file
                                    : file,
                            /* eslint-enable @typescript-eslint/naming-convention */
                        };
                    })
                );

                const headStats = fileStats.slice(0, 5);
                const tailStats = fileStats.slice(-5);
                /* eslint-disable-next-line @typescript-eslint/naming-convention */
                changedFileStats[statsKey].per_file_change_stats_head = headStats;
                // Ensure that there is no overlap between the head stats and the tail stats
                changedFileStats[statsKey].per_file_change_stats_tail = tailStats.filter(
                    (stat) => !headStats.find((headStat) => headStat.file_path === stat.file_path)
                );
                /* eslint-enable-next-line @typescript-eslint/naming-convention */
            }
        }

        return changedFileStats;
    }

    private async getInsertionDeletionStats(
        onlyUseStagedChanges: boolean,
        options: string[]
    ): Promise<{ insertions: number; deletions: number }> {
        const diffArgs = ["--staged", "--shortstat"].concat(options);
        let diffShortStat = await this.git!.diff(diffArgs);

        // If we are also allowed to use unstaged changes, add them on!
        if (!onlyUseStagedChanges) {
            const diffArgs = ["--shortstat"].concat(options);
            diffShortStat += await this.git!.diff(diffArgs);
        }

        const parts = diffShortStat.split(",").map((part) => part.trim());
        const insertions = parseInt(
            parts.find((part) => part.includes("insertion"))?.split(" ")[0] || "0"
        );
        const deletions = parseInt(
            parts.find((part) => part.includes("deletion"))?.split(" ")[0] || "0"
        );

        return { insertions, deletions };
    }

    private async getTruncatedDiffs(
        options: PromptDataOptions,
        changedFilesTokens: number
    ): Promise<string> {
        // get number of insertions and deletions from getFileChangeStats
        const { insertions, deletions } = await this.getInsertionDeletionStats(
            options.onlyUseStagedChanges,
            []
        );
        const diffLinesLength = insertions + deletions;

        if (diffLinesLength <= options.diffNoopLineLimit) {
            let gitDiff = await this.git!.diff(["--staged", "--name-only"]);

            // If we are also allowed to use unstaged changes, add them on!
            if (!options.onlyUseStagedChanges) {
                gitDiff += await this.git!.diff(["--name-only"]);
            }
            const fileNames = gitDiff.split("\n").filter(Boolean);

            const sortedChanges = await Promise.all(
                fileNames.map(async (fileName) => {
                    let fileDiff = await this.git!.diff(["--staged", fileName]);

                    // If we are also allowed to use unstaged changes, add them on!
                    if (!options.onlyUseStagedChanges) {
                        fileDiff += await this.git!.diff([fileName]);
                    }

                    return { fileName, diff: fileDiff, tokens: this.countTokens(fileDiff) };
                })
            );

            sortedChanges.sort((a, b) => a.tokens - b.tokens);

            let truncatedDiff = "";
            let totalTokens = changedFilesTokens;

            for (const change of sortedChanges) {
                if (totalTokens + change.tokens <= options.diffBudget) {
                    truncatedDiff += change.diff + "\n";
                    totalTokens += change.tokens;
                } else {
                    break;
                }
            }

            return truncatedDiff.trim();
        } else {
            let gitDiff = await this.git!.diff(["--staged"]);

            // If we are also allowed to use unstaged changes, add them on!
            if (!options.onlyUseStagedChanges) {
                gitDiff += await this.git!.diff([]);
            }
            const diffLines = gitDiff.split("\n");

            const truncatedDiff = diffLines.slice(0, options.diffNoopLineLimit).join("\n");
            return truncatedDiff.slice(0, options.diffBudget - changedFilesTokens);
        }
    }

    private async getCommitMessages(limit: number): Promise<string[]> {
        const log = await this.git!.log({ maxCount: limit });
        return log.all.map((commit) => commit.message);
    }

    private async getCurrentAuthor(): Promise<string | undefined> {
        const config = await this.git!.listConfig();
        const email = config.all["user.email"];
        return Array.isArray(email) ? email[0] || undefined : email || undefined;
    }

    private getExampleCommitMessages(
        allCommitMessages: string[],
        relevantCommitMessages: string[],
        remainingMessageBudget: number,
        maxExampleCommitMessages: number
    ): string[] {
        const exampleCommitMessages: string[] = [];
        let exampleCommitMessagesTokens = 0;

        for (const message of allCommitMessages.slice(relevantCommitMessages.length)) {
            if (
                exampleCommitMessages.length >=
                maxExampleCommitMessages - relevantCommitMessages.length
            ) {
                break;
            }

            const messageTokens = this.countTokens(message);
            if (exampleCommitMessagesTokens + messageTokens <= remainingMessageBudget) {
                exampleCommitMessages.push(message);
                exampleCommitMessagesTokens += messageTokens;
            } else {
                break;
            }
        }

        return exampleCommitMessages;
    }

    private async getRelevantCommitMessages(
        currentAuthor: string,
        relevantMessageSubbudget: number
    ): Promise<string[]> {
        return this.git!.log({ maxCount: 3 }).then((logResult) => {
            return logResult.all
                .filter((commit) => commit.author_email === currentAuthor.trim())
                .map((commit) => commit.message)
                .reduce<string[]>((acc, msg) => {
                    const msgTokens = this.countTokens(msg);
                    if (this.countTokens(acc.join("\n")) + msgTokens <= relevantMessageSubbudget) {
                        acc.push(msg);
                    }
                    return acc;
                }, []);
        });
    }

    public async getCommitMessagePromptData(
        baseDir: string,
        options: PromptDataOptions
    ): Promise<CommitMessagePromptData> {
        this._updateBaseDir(baseDir);

        if (!this.git) {
            return {
                changedFileStats: EMPTY_FILE_STATS,
                diff: "",
                generatedCommitMessageSubrequest: {
                    /* eslint-disable @typescript-eslint/naming-convention */
                    relevant_commit_messages: [],
                    example_commit_messages: [],
                    /* eslint-enable @typescript-eslint/naming-convention */
                },
            };
        }

        const changedFileStats = await this.getChangedFileStats(options);
        const changedFilesTokens = this.countTokens(JSON.stringify(changedFileStats));

        const diff = await this.getTruncatedDiffs(options, changedFilesTokens);

        const allCommitMessages = await this.getCommitMessages(32);
        const currentAuthor = await this.getCurrentAuthor();

        let relevantCommitMessages: string[] = [];
        let exampleCommitMessages: string[] = [];

        if (currentAuthor) {
            relevantCommitMessages = await this.getRelevantCommitMessages(
                currentAuthor,
                options.relevantMessageSubbudget
            );

            const relevantCommitMessagesTokens = this.countTokens(
                relevantCommitMessages.join("\n")
            );
            const remainingMessageBudget = options.messageBudget - relevantCommitMessagesTokens;

            exampleCommitMessages = this.getExampleCommitMessages(
                allCommitMessages,
                relevantCommitMessages,
                remainingMessageBudget,
                options.maxExampleCommitMessages
            );
        }

        return {
            changedFileStats,
            diff,
            generatedCommitMessageSubrequest: {
                /* eslint-disable @typescript-eslint/naming-convention */
                relevant_commit_messages: relevantCommitMessages,
                example_commit_messages: exampleCommitMessages,
                /* eslint-enable @typescript-eslint/naming-convention */
            },
        };
    }
}
