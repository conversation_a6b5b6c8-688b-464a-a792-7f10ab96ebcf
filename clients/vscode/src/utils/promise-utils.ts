import * as vscode from "vscode";

/**
 * makePromise returns a triple consisting of a Promise<T> and its resolve and
 * reject callbacks.
 */
export function makePromise<T = void, E = Error>(): [
    Promise<T>,
    (value: T | PromiseLike<T>) => void,
    (e: E) => void,
] {
    let res: (value: T | PromiseLike<T>) => void;
    let rej: (e: E) => void;
    const promise = new Promise<T>((resolve, reject) => {
        res = resolve;
        rej = reject;
    });

    // These non-null assertions (res!, rej!) are a workaround for a spurious error:
    // "Variable 'res' is used before being assigned" (or 'rej').
    return [promise, res!, rej!];
}

export function promiseFromEvent<T>(event: vscode.Event<T>): Promise<T> {
    let disp: vscode.Disposable;
    return new Promise((resolve) => {
        disp = event((value) => {
            disp.dispose();
            resolve(value);
        });
    });
}

export function disposableDelayer(): [Promise<void>, vscode.Disposable] {
    const [promise, resolve, _] = makePromise<void>();
    const resume = new vscode.Disposable(() => {
        resolve();
    });
    return [promise, resume];
}

/**
 * periodicYielder is a function that helps ensure that a long-running task doesn't block the
 * event loop. It returns an iterator that yields control to the event loop if it has been at
 * least `maxMs` milliseconds since the last yield.
 */
export async function* periodicYielder(maxMs: number): AsyncGenerator<void> {
    let lastYieldTime = Date.now();
    while (true) {
        const now = Date.now();
        if (now - lastYieldTime >= maxMs) {
            // Interestingly, setTimeout(0) appears to be faster than setImmediate(), by 15-25%.
            await new Promise((resolve) => setTimeout(resolve, 0));
            lastYieldTime = Date.now();
        }
        yield;
    }
}
