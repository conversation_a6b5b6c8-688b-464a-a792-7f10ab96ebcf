import * as vscode from "vscode";

import { isNotebookUri } from "./notebook";
import { pathNameSansSep } from "./path-utils";

// uriToAbsPath converts the given uri to an absolute path, ensuring that the result
// does not end with a separator (unless it is "/")
export function uriToAbsPath(uri: vscode.Uri): string {
    return pathNameSansSep(uri.fsPath);
}

// uriToDisplayablePath converts the given uri to a path name that can be displayed to the user.
// The returned path should be used for display purposes only. It should not be used to access the
// file. (Per https://code.visualstudio.com/api/references/vscode-api#Uri.fsPath)
// Aug 2024: we have determined that the documentation there simply seems wrong and fsPath is the
// best path to display to the user.
export function uriToDisplayablePath(uri: vscode.Uri): string {
    return uri.fsPath;
}

// validateTrackablePath converts the given uri to an absolute path if the uri's scheme is
// supported by Augment. Otherwise, it returns undefined. The supported schemes are:
// * file
// * untitled (corresponds to a new buffer that has not yet been saved)
// * notebook-related schemes
export function validateTrackablePath(uri: vscode.Uri): string | undefined {
    if (uri.scheme === "file" || uri.scheme === "untitled" || isNotebookUri(uri)) {
        return uriToAbsPath(uri);
    }
    return undefined;
}

// for now next-edit only supports file uris
export function isNextEditSupportedPath(uri: vscode.Uri): string | undefined {
    if (uri.scheme === "file") {
        return uriToAbsPath(uri);
    }
    return undefined;
}
