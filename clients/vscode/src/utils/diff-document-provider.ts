import * as vscode from "vscode";

export class DiffDocumentProvider implements vscode.TextDocumentContentProvider {
    private _onDidChange = new vscode.EventEmitter<vscode.Uri>();

    public prefix = "";
    public generatedCode: string | undefined;
    public displayCode: string | undefined;
    public suffix = "";

    reset() {
        this.prefix = "";
        this.generatedCode = undefined;
        this.displayCode = undefined;
        this.suffix = "";
    }

    hasDiff() {
        return this.displayCode !== undefined;
    }

    // This event will be used by VSCode to listen for content changes
    get onDidChange(): vscode.Event<vscode.Uri> {
        return this._onDidChange.event;
    }

    // Method to trigger a content update
    refresh(uri: vscode.Uri) {
        this._onDidChange.fire(uri);
    }

    provideTextDocumentContent(): string {
        return this.prefix + this.displayCode + this.suffix;
    }
}
