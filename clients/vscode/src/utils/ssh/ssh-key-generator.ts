import { exec } from "child_process";
import * as fs from "fs/promises";
import * as os from "os";
import * as path from "path";
import { promisify } from "util";
import * as vscode from "vscode";

import { SshKeygenOptions, SshKeygenResult } from "./types";

const execAsync = promisify(exec);

/**
 * Default options for SSH key generation
 *
 * This generates a single keypair intended to be used across all remote agent connections.
 * The same public key can be deployed to all remote agent environments.
 */
export const DEFAULT_KEY_OPTIONS = {
    type: "ed25519",
    comment: `augment-remote-agent-key-${new Date().toISOString().split("T")[0]}`,
    passphrase: "",
    filename: "augment_remote_agent_key",
};

/**
 * <PERSON>les generating SSH keys using ssh-keygen.
 */
export class SshKeyGenerator {
    /**
     * Checks if we're running in a remote environment
     */
    private isRemoteEnvironment(): boolean {
        return vscode.env.remoteName !== undefined;
    }

    /**
     * Gets the path to the Augment SSH keys directory
     */
    private getAugmentSshKeysDir(): string {
        const homeDir = os.homedir();
        return path.join(homeDir, ".augment", "ssh-keys");
    }

    /**
     * Ensures the Augment SSH keys directory exists
     */
    private async ensureKeysDirectoryExists(): Promise<string> {
        const keysDir = this.getAugmentSshKeysDir();
        try {
            await fs.mkdir(keysDir, { recursive: true, mode: 0o700 });
        } catch (error) {
            // Ignore if directory already exists
            if ((error as NodeJS.ErrnoException)?.code !== "EEXIST") {
                throw error;
            }
        }
        return keysDir;
    }

    /**
     * Checks if SSH keys already exist at the specified paths
     *
     * @param privateKeyPath Path to the private key file
     * @param publicKeyPath Path to the public key file
     * @returns The existing keys if both exist, null otherwise
     */
    private async checkExistingKeys(
        privateKeyPath: string,
        publicKeyPath: string
    ): Promise<SshKeygenResult | null> {
        try {
            const privateKeyExists = await fs
                .access(privateKeyPath)
                .then(() => true)
                .catch(() => false);
            const publicKeyExists = await fs
                .access(publicKeyPath)
                .then(() => true)
                .catch(() => false);

            // If both keys exist, return their paths and content
            if (privateKeyExists && publicKeyExists) {
                const publicKey = await fs.readFile(publicKeyPath, "utf8");
                return {
                    privateKeyPath,
                    publicKeyPath,
                    publicKey: publicKey.trim(),
                };
            }
        } catch {
            // noop
        }

        return null;
    }

    /**
     * Deletes a file if it exists
     *
     * @param filePath Path to the file to delete
     */
    private async deleteFile(filePath: string): Promise<void> {
        try {
            await fs.unlink(filePath);
        } catch {
            // noop
        }
    }

    /**
     * Generates a new SSH key pair for connecting to remote agent environments
     *
     * This creates a single keypair that will be used for all
     * remote agent connections. If keys already exist at the target location,
     * it will return the existing keys without regenerating them.
     *
     * The intention is to maintain one consistent keypair for all
     * remote agent environments. Calling this method multiple times will
     * only generate the keys once, so it's safe to call whenever keys are needed.
     *
     * @param options Options for key generation
     * @returns The paths to the generated key files and the public key content
     * @throws Error if running in a remote environment or if key generation fails.
     */
    public async generateKeyPair(
        options: Partial<SshKeygenOptions> = {}
    ): Promise<SshKeygenResult> {
        // We don't support generating keys in remote environments. This is because if the user is SSHed,
        // the augment extension code runs in the remote environment, but we need to be able to write the
        // keys to the local filesystem along with strict permissions.
        // But the VSCode API doesn't support setting file permissions from a remote environment,
        // so it's best for us to just error out here and prompt the user to generate or point to existing keys
        // on their local machine.
        if (this.isRemoteEnvironment()) {
            throw new Error(
                "SSH key generation is not supported in remote environments. " +
                    "Please generate keys on your local machine."
            );
        }

        const mergedOptions = {
            ...DEFAULT_KEY_OPTIONS,
            ...options,
        } as SshKeygenOptions;

        const keysDir = await this.ensureKeysDirectoryExists();

        const filename = mergedOptions.filename || `id_${mergedOptions.type}`;
        const privateKeyPath = path.join(keysDir, filename);
        const publicKeyPath = `${privateKeyPath}.pub`;

        // Check if keys already exist and return them if they do
        const existingKeys = await this.checkExistingKeys(privateKeyPath, publicKeyPath);
        if (existingKeys) {
            return existingKeys;
        }

        // If we get here, either no keys exist or only one exists
        // Clean up any existing files before generating new ones bc otherwise ssh-keygen
        // will try to prompt then abort.
        await this.deleteFile(privateKeyPath);
        await this.deleteFile(publicKeyPath);

        let command = `ssh-keygen -t ${mergedOptions.type}`;

        if (mergedOptions.bits && mergedOptions.type === "rsa") {
            command += ` -b ${mergedOptions.bits}`;
        }

        if (mergedOptions.comment) {
            command += ` -C "${mergedOptions.comment}"`;
        }

        const passphrase = mergedOptions.passphrase || "";

        // Add file path + passphrase
        command += ` -f "${privateKeyPath}" -N "${passphrase}"`;

        try {
            await execAsync(command);

            // Private key needs to be owner RW only, otherwise SSH will refuse to use it.
            await fs.chmod(privateKeyPath, 0o600);

            const publicKey = await fs.readFile(publicKeyPath, "utf8");
            return {
                privateKeyPath,
                publicKeyPath,
                publicKey: publicKey.trim(),
            };
        } catch (error) {
            throw new Error(`Failed to generate SSH key: ${String(error)}`);
        }
    }
}
