import * as fs from "fs/promises";
import * as os from "os";
import * as vscode from "vscode";

import { sha256 } from "../../sha";
import { AugmentGlobalState, GlobalContextKey } from "../context";
import { detectPlatformFromPath, getDirname, joinPathForPlatform } from "./ssh-path-utils";
import { extractHomeInfoFromLogs, RemoteHomeInfo } from "./ssh-path-utils";

export const SSH_KNOWN_HOSTS_DIR_PATH = ["ssh", "known_hosts.d"];

/**
 * Handles file system operations in a way that works in both local and remote environments
 */
export class SshFileSystem {
    constructor(private globalState?: AugmentGlobalState) {}

    /**
     * Checks if we're running in a remote environment
     */
    isRemoteEnvironment(): boolean {
        return vscode.env.remoteName !== undefined;
    }

    /**
     * Gets the user's home directory path, works in both local and remote environments
     * The heuristic here is similar to the one in KeybindingWatcher:
     */
    async getHomeDirectory(): Promise<string> {
        const isRemote = this.isRemoteEnvironment();

        // For local environments, just use os.homedir()
        if (!isRemote) {
            return os.homedir();
        }

        // For remote environments, try to get the home directory from global state
        if (this.globalState) {
            const remoteHomeInfo: RemoteHomeInfo | undefined = this.globalState.get<RemoteHomeInfo>(
                GlobalContextKey.remoteHomeInfo as GlobalContextKey
            );

            if (remoteHomeInfo?.homePath) {
                return remoteHomeInfo.homePath;
            }
        }

        // If we don't have the home directory in global state, try to find it
        try {
            // Read the output of the "Extension Host"
            const doc = await vscode.workspace.openTextDocument(
                vscode.Uri.from({
                    scheme: "output",
                    path: "exthost",
                })
            );

            const homeInfo = extractHomeInfoFromLogs(doc.getText());
            if (homeInfo) {
                if (this.globalState) {
                    await this.globalState.update(
                        GlobalContextKey.remoteHomeInfo as GlobalContextKey,
                        homeInfo
                    );
                }
                return homeInfo.homePath;
            }
        } catch (err) {
            void vscode.window.showErrorMessage(`Error finding home directory: ${String(err)}`);
        }
        // If we couldn't find the home directory, fall back to os.homedir()
        // This is not ideal but better than returning null
        return os.homedir();
    }

    /**
     * Gets the platform of the environment (local or remote)
     */
    async getPlatform(): Promise<string> {
        if (!this.isRemoteEnvironment()) {
            return process.platform;
        }

        if (this.globalState) {
            const remoteHomeInfo: RemoteHomeInfo | undefined = this.globalState.get<RemoteHomeInfo>(
                GlobalContextKey.remoteHomeInfo as GlobalContextKey
            );

            if (remoteHomeInfo?.platform) {
                return remoteHomeInfo.platform;
            }
        }

        const homeDir = await this.getHomeDirectory();
        return detectPlatformFromPath(homeDir);
    }

    private async getMetadataPath(): Promise<string> {
        const augmentDir = await this.getAugmentConfigDir();
        return await this.joinPath(augmentDir, "metadata.json");
    }

    private async getMetadata(): Promise<Record<string, string>> {
        const metadataPath = await this.getMetadataPath();
        const fileExists = await this.fileExists(metadataPath);
        if (!fileExists) {
            // create it
            await this.writeFile(metadataPath, "{}");
        }
        const metadata = await this.readFile(metadataPath);
        return JSON.parse(metadata) as Record<string, string>;
    }

    async getMetadataValue(key: string): Promise<string | undefined> {
        const metadata = await this.getMetadata();
        return metadata[key];
    }

    async addMetadata(key: string, value: string): Promise<void> {
        const metadataPath = await this.getMetadataPath();
        const metadata = await this.getMetadata();
        metadata[key] = value;
        await this.writeFile(metadataPath, JSON.stringify(metadata));
    }

    /**
     * Joins path segments using the appropriate separator for the target platform
     */
    async joinPath(...segments: string[]): Promise<string> {
        const platform = await this.getPlatform();
        return joinPathForPlatform(platform, ...segments);
    }

    /**
     * Gets the path to the Augment config directory
     */
    async getAugmentConfigDir(): Promise<string> {
        const homeDir = await this.getHomeDirectory();
        return await this.joinPath(homeDir, ".augment");
    }

    /**
     * Gets the path to the Augment SSH config file
     */
    async getAugmentSshConfigPath(): Promise<string> {
        const configDir = await this.getAugmentConfigDir();
        return await this.joinPath(configDir, "ssh-config");
    }

    /**
     * Gets the path to the Augment SSH known hosts directory
     */
    async getSshKnownHostsDir(): Promise<string> {
        const configDir = await this.getAugmentConfigDir();
        return await this.joinPath(configDir, ...SSH_KNOWN_HOSTS_DIR_PATH);
    }

    /**
     * Gets a unique identifier for the current workspace based on folder root
     */
    private getWorkspaceIdentifier(): string | undefined {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return undefined;
        }

        // Use the first workspace folder as the primary identifier
        // This matches the pattern used in WorkspaceManager.computeCacheDirPath
        const folderRoot = workspaceFolders[0].uri.fsPath;
        const textEncoder = new TextEncoder();
        return sha256(textEncoder.encode(folderRoot));
    }

    /**
     * Gets the path to the appropriate SSH known hosts file
     * Returns workspace-specific file if in a workspace, otherwise returns default file
     */
    async getSshKnownHostsPath(): Promise<string> {
        const knownHostsDir = await this.getSshKnownHostsDir();
        const workspaceId = this.getWorkspaceIdentifier()?.substring(0, 8);

        if (workspaceId) {
            return await this.joinPath(knownHostsDir, `workspace-${workspaceId}`);
        } else {
            return await this.joinPath(knownHostsDir, "default");
        }
    }

    /**
     * Gets the path to the default (non-workspace-specific) SSH known hosts file
     */
    async getDefaultSshKnownHostsPath(): Promise<string> {
        const knownHostsDir = await this.getSshKnownHostsDir();
        return await this.joinPath(knownHostsDir, "default");
    }

    /**
     * Ensures the SSH known hosts directory exists
     */
    async ensureSshKnownHostsDirExists(): Promise<void> {
        const knownHostsDir = await this.getSshKnownHostsDir();
        await this.mkdir(knownHostsDir, 0o700);
    }

    /**
     * Gets the path to the user's default SSH config file
     * This is typically ~/.ssh/config
     */
    async getDefaultSshConfigPath(): Promise<string> {
        const homeDir = await this.getHomeDirectory();
        return await this.joinPath(homeDir, ".ssh", "config");
    }

    /**
     * Creates the appropriate URI for a file path based on the environment
     * In remote environments, uses vscode-local: scheme to access local file system
     * In local environments, uses file: scheme
     */
    createFileUri(filePath: string): vscode.Uri {
        if (this.isRemoteEnvironment()) {
            // In remote environments, use vscode-local: URI to access local file system
            return vscode.Uri.parse(`vscode-local:${filePath}`);
        } else {
            // In local environments, just use file: scheme
            return vscode.Uri.file(filePath);
        }
    }

    /**
     * Reads a file, handling both local and remote environments
     */
    async readFile(filePath: string): Promise<string> {
        try {
            if (this.isRemoteEnvironment()) {
                const uri = this.createFileUri(filePath);
                const content = await vscode.workspace.fs.readFile(uri);
                return Buffer.from(content).toString("utf8");
            } else {
                return await fs.readFile(filePath, "utf8");
            }
        } catch (error) {
            if (
                (error as NodeJS.ErrnoException)?.code === "ENOENT" ||
                (error instanceof vscode.FileSystemError && error.code === "FileNotFound")
            ) {
                return "";
            }
            void vscode.window.showErrorMessage(`Error reading file ${filePath}: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Gets the directory name from a path
     */
    async getDirname(filePath: string): Promise<string> {
        const platform = await this.getPlatform();
        return getDirname(platform, filePath);
    }

    /**
     * Writes to a file, handling both local and remote environments
     */
    async writeFile(filePath: string, content: string, mode: number = 0o600): Promise<void> {
        // Ensure directory exists
        const dirPath = await this.getDirname(filePath);
        await this.mkdir(dirPath, 0o700);

        if (this.isRemoteEnvironment()) {
            const uri = this.createFileUri(filePath);
            try {
                await vscode.workspace.fs.writeFile(uri, Buffer.from(content, "utf8"));
            } catch (error) {
                void vscode.window.showErrorMessage(
                    `Error writing to local file from remote environment: ${filePath}\n${String(error)}`
                );
                throw error;
            }

            // Note: VS Code's file system API doesn't support setting file permissions
        } else {
            await fs.writeFile(filePath, content, { mode });
        }
    }

    /**
     * Creates a directory, handling both local and remote environments
     */
    async mkdir(dirPath: string, mode: number = 0o700): Promise<void> {
        try {
            if (this.isRemoteEnvironment()) {
                const uri = this.createFileUri(dirPath);
                try {
                    await vscode.workspace.fs.createDirectory(uri);
                } catch (error) {
                    // Special handling for remote environments
                    // If we can't create the directory, it might be because we're trying to create
                    // a directory that already exists on the local machine but isn't accessible from remote
                    if (error instanceof vscode.FileSystemError && error.code === "FileNotFound") {
                        // This might be a parent directory that we can't access
                        // Log a warning but don't fail - the subsequent operations will fail if needed
                        void vscode.window.showWarningMessage(
                            `Could not create directory on local machine from remote environment: ${dirPath}`
                        );
                        return;
                    }
                    throw error;
                }

                // Note: VS Code's file system API doesn't support setting directory permissions
            } else {
                await fs.mkdir(dirPath, { recursive: true, mode });
            }
        } catch (error) {
            // Ignore if directory already exists
            if (
                (error as NodeJS.ErrnoException)?.code !== "EEXIST" &&
                !(error instanceof vscode.FileSystemError && error.code === "FileExists")
            ) {
                void vscode.window.showErrorMessage(
                    `Error creating directory ${dirPath}: ${String(error)}`
                );
                throw error;
            }
        }
    }

    async directoryExists(dirPath: string): Promise<boolean> {
        try {
            if (this.isRemoteEnvironment()) {
                const uri = this.createFileUri(dirPath);
                const stat = await vscode.workspace.fs.stat(uri);
                return stat.type === vscode.FileType.Directory;
            } else {
                await fs.access(dirPath);
                return true;
            }
        } catch {
            return false;
        }
    }

    /**
     * Checks if a file exists
     *
     * @param filePath Path to the file to check
     * @returns True if the file exists and is readable, false otherwise
     */
    async fileExists(filePath: string): Promise<boolean> {
        try {
            if (this.isRemoteEnvironment()) {
                const uri = this.createFileUri(filePath);
                const stat = await vscode.workspace.fs.stat(uri);
                return stat.type === vscode.FileType.File;
            } else {
                // Use fs.access to check if the file exists
                await fs.access(filePath);
                return true;
            }
        } catch {
            return false;
        }
    }

    /**
     * Opens a file in the editor, creating it if it doesn't exist
     * Works in both local and remote environments
     *
     * @param filePath Path to the file to open
     * @param defaultContent Optional content to write if the file doesn't exist
     * @returns The opened text document
     */
    async openFile(filePath: string, defaultContent?: string): Promise<vscode.TextDocument> {
        const uri = this.createFileUri(filePath);

        try {
            const doc = await vscode.workspace.openTextDocument(uri);
            await vscode.window.showTextDocument(doc);
            return doc;
        } catch (error) {
            // If the file doesn't exist and default content is provided, create it and try again
            if (defaultContent !== undefined) {
                const dirPath = await this.getDirname(filePath);
                await this.mkdir(dirPath, 0o700);

                await this.writeFile(filePath, defaultContent);

                const doc = await vscode.workspace.openTextDocument(uri);
                await vscode.window.showTextDocument(doc);
                return doc;
            }
            throw error;
        }
    }
}
