import { createHostRegex, getAllHostIds } from "../ssh-utils";

describe("ssh-utils", () => {
    describe("createHostRegex", () => {
        it("should create regex for exact match", () => {
            const regex = createHostRegex("test-host", true);
            expect(regex.test("Host test-host")).toBe(true);
            expect(regex.test("Host test-host-2")).toBe(false);
            expect(regex.test("Host another-test-host")).toBe(false);
        });

        it("should create regex for full host block match", () => {
            const regex = createHostRegex("test-host", false);
            const content = `Host test-host
   HostName example.com
   User ubuntu

Host another-host
   HostName other.com`;

            const match = content.match(regex);
            expect(match).toBeTruthy();
            expect(match![0]).toContain("Host test-host");
            expect(match![0]).toContain("HostName example.com");
            expect(match![0]).toContain("User ubuntu");
            expect(match![0]).not.toContain("Host another-host");
        });
    });

    describe("getAllHostIds", () => {
        it("should return empty array for empty content", () => {
            expect(getAllHostIds("")).toEqual([]);
            expect(getAllHostIds("# Just a comment")).toEqual([]);
        });

        it("should extract single host ID", () => {
            const content = `Host test-host
   HostName example.com
   User ubuntu`;

            expect(getAllHostIds(content)).toEqual(["test-host"]);
        });

        it("should extract multiple host IDs in order", () => {
            const content = `Host first-host
   HostName first.com

Host second-host
   HostName second.com

Host third-host
   HostName third.com`;

            expect(getAllHostIds(content)).toEqual(["first-host", "second-host", "third-host"]);
        });

        it("should handle hosts with complex names", () => {
            const content = `Host host-with-dashes
   HostName example.com

Host host_with_underscores
   HostName example.com

Host host123
   HostName example.com`;

            expect(getAllHostIds(content)).toEqual([
                "host-with-dashes",
                "host_with_underscores",
                "host123",
            ]);
        });

        it("should ignore commented host lines", () => {
            const content = `Host active-host
   HostName example.com

# Host commented-host
#    HostName commented.com

Host another-active-host
   HostName another.com`;

            expect(getAllHostIds(content)).toEqual(["active-host", "another-active-host"]);
        });

        it("should handle mixed content with comments and empty lines", () => {
            const content = `# SSH Config file
# Generated by Augment

Host first-host
   HostName first.com
   User ubuntu

# Some comment

Host second-host
   HostName second.com
   Port 2222

Host third-host
   HostName third.com`;

            expect(getAllHostIds(content)).toEqual(["first-host", "second-host", "third-host"]);
        });
    });
});
