/**
 * Information about the user's home directory in a remote environment.
 */
export interface RemoteHomeInfo {
    platform: string;
    homePath: string;
}

export const homeDirPatterns = [
    new RegExp("^/Users/<USER>/]+"), // macOS: /Users/<USER>
    new RegExp("^/home/<USER>/]+"), // Linux: /home/<USER>
    /^[A-Za-z]:\\Users\\[^\\]+/, // Windows: C:\Users\<USER>\./,
    /Loading configuration from (.*?)\./,
];

/**
 * Extracts the home directory from log messages
 * @param logText The log text to search for home directory paths
 * @returns the home path and platform, or null if not found
 */
export function extractHomeInfoFromLogs(logText: string): RemoteHomeInfo | null {
    for (const pattern of logPathPatterns) {
        const match = logText.match(pattern);
        if (match && match[1]) {
            const fullPath = match[1];
            const homePath = extractHomeDir(fullPath);

            if (homePath) {
                const platform = detectPlatformFromPath(homePath);
                return { homePath, platform };
            }
        }
    }

    return null;
}

/**
 * Detects the platform (OS) based on a file path format
 * @param path A file path to analyze
 * @returns The detected platform: "darwin" (macOS), "win32" (Windows), or "linux"
 */
export function detectPlatformFromPath(path: string): string {
    if (path.startsWith("/Users/")) {
        return "darwin";
    } else if (path.match(/^\/?[a-zA-Z](?::|%3A)\/.*/)) {
        return "win32";
    } else {
        return "linux";
    }
}

/**
 * Joins path segments using the appropriate separator for the target platform
 * @param platform The platform to use for determining the separator
 * @param segments The path segments to join
 * @returns The joined path
 */
export function joinPathForPlatform(platform: string, ...segments: string[]): string {
    const separator = platform === "win32" ? "\\" : "/";
    return segments.join(separator);
}

/**
 * Gets the directory name from a path
 * @param platform The platform to use for determining the separator
 * @param filePath The path to extract the directory from
 * @returns The directory part of the path
 */
export function getDirname(platform: string, filePath: string): string {
    const separator = platform === "win32" ? "\\" : "/";
    const lastSeparatorIndex = filePath.lastIndexOf(separator);
    if (lastSeparatorIndex === -1) {
        return ".";
    }
    return filePath.substring(0, lastSeparatorIndex);
}
