import { RemoteAgentSSHConfig } from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { exec } from "child_process";
import * as vscode from "vscode";

import { AugmentGlobalState } from "../context";
import { SshFileSystem } from "./ssh-file-system";
import {
    AUGMENT_SSH_CONFIG_COMMENT,
    createHostRegex,
    createIncludeRegex,
    getAllHostIds,
    openSSHWindow,
    withTemporarySSHSettings,
} from "./ssh-utils";

/**
 * Maximum number of hosts to keep in the SSH config before garbage collecting
 */
const MAX_HOSTS = 10;

/**
 * Manages reading, writing, and modifying SSH config files
 */
export class SSHConfigManager {
    public fileSystem: SshFileSystem;
    private configPath: string | null = null;
    private timeoutMs = 10_000;

    /**
     * Creates a new SSH config manager
     * @param globalState Optional global state for storing remote home directory info
     * @param configPath Optional custom path to the SSH config file
     */
    constructor(globalState?: AugmentGlobalState, configPath?: string) {
        this.fileSystem = new SshFileSystem(globalState);
        this.configPath = configPath || null;
    }

    /**
     * Gets the path to the SSH config file
     */
    async getAugmentConfigPath(): Promise<string> {
        if (this.configPath) {
            return this.configPath;
        }
        return await this.fileSystem.getAugmentSshConfigPath();
    }

    /**
     * Gets the path to the user's default SSH config file
     */
    async getDefaultConfigPath(): Promise<string> {
        // First check if VS Code has a custom SSH config path set
        const sshConfig = vscode.workspace.getConfiguration("remote.SSH");
        const configFile = sshConfig.get<string>("configFile");

        if (configFile && configFile.trim()) {
            return configFile;
        }

        return await this.fileSystem.getDefaultSshConfigPath();
    }

    /**
     * Loads the SSH config file content
     */
    async loadAugmentConfig(): Promise<string> {
        const configPath = await this.getAugmentConfigPath();
        return await this.fileSystem.readFile(configPath);
    }

    /**
     * Loads the user's default SSH config file content
     */
    async loadDefaultConfig(): Promise<string> {
        const configPath = await this.getDefaultConfigPath();
        return await this.fileSystem.readFile(configPath);
    }

    /**
     * Saves content to the user's default SSH config file
     */
    async saveToDefaultConfig(content: string): Promise<void> {
        const configPath = await this.getDefaultConfigPath();
        await this.fileSystem.writeFile(configPath, content, 0o644);
    }

    /**
     * Saves content to the SSH config file
     */
    async saveToAugmentConfig(content: string): Promise<void> {
        const configPath = await this.getAugmentConfigPath();
        await this.fileSystem.writeFile(configPath, content, 0o644);
    }

    /**
     * Removes host entries from the SSH config
     *
     * @param remoteAgentIds The ID(s) of the remote agent(s) to remove from the SSH config
     */
    async removeHostEntry(remoteAgentIds: string | string[]): Promise<void> {
        const ids = Array.isArray(remoteAgentIds) ? remoteAgentIds : [remoteAgentIds];
        let content = await this.loadAugmentConfig();

        for (const remoteAgentId of ids) {
            const hostRegex = createHostRegex(remoteAgentId);
            const hostMatch = content.match(hostRegex);

            if (hostMatch) {
                // Remove the host entry and any trailing whitespace
                content = content.replace(hostMatch[0], "").replace(/\n\s*\n\s*\n/g, "\n\n");
            }
        }

        await this.saveToAugmentConfig(content.trim());
    }

    /**
     * Adds or updates a host in the SSH config with LRU behavior
     *
     * @param remoteAgentId The ID of the remote agent in the SSH config
     * @param config The SSH configuration for the host
     * @param maxHosts Optional maximum number of hosts to keep (for garbage collection)
     */
    async addHost(
        remoteAgentId: string,
        config: RemoteAgentSSHConfig,
        maxHosts?: number
    ): Promise<void> {
        let content = await this.loadAugmentConfig();

        // Remove existing entry if it exists (we'll add it to the end for LRU)
        await this.removeHostEntry(remoteAgentId);
        content = await this.loadAugmentConfig();

        const hostEntry = this.formatHostEntry(remoteAgentId, config);

        // Add the host entry to the end (most recently used)
        const newContent = content.trim() ? `${content.trim()}\n\n${hostEntry}` : hostEntry;

        await this.saveToAugmentConfig(newContent);

        // Apply garbage collection if maxHosts is specified
        if (maxHosts && maxHosts > 0) {
            const updatedContent = await this.loadAugmentConfig();
            const hostIds = getAllHostIds(updatedContent);

            if (hostIds.length > maxHosts) {
                // Remove oldest entries (keep only the last maxHosts)
                const hostsToRemove = hostIds.slice(0, hostIds.length - maxHosts);
                await this.removeHostEntry(hostsToRemove);
            }
        }
    }

    /**
     * The config values may contain escape sequences that need to be preserved.
     * This function escapes the escape sequences so that they aren't evaluated
     * when the config is written to the file.
     */
    public getRawString(value: string): string {
        return JSON.stringify(value).slice(1, -1);
    }

    /**
     * Formats a host entry for the SSH config file
     *
     * @param remoteAgentId The ID of the remote agent in the SSH config
     * @param hostConfig The SSH configuration for the host
     */
    formatHostEntry(remoteAgentId: string, hostConfig: RemoteAgentSSHConfig): string {
        const { hostname, ssh_config_options: sshConfigOptions } = hostConfig;

        let entry = `Host ${remoteAgentId}\n`;
        entry += `   HostName ${hostname}\n`;

        for (const option of sshConfigOptions) {
            let value = option.value;

            // Special handling for KnownHostsCommand which may have double-escaped newlines from backend
            if (option.key === "KnownHostsCommand" && value.includes("\\n")) {
                value = value.replace(/\\n/g, "\n");
            }

            let configValue = this.getRawString(value);
            if (option.key === "ProxyCommand") {
                // ProxyCommand has escape characters we shouldn't preserve, don't use getRawString
                configValue = value;
            }

            entry += "  " + option.key + " " + configValue + "\n";
        }

        return entry;
    }

    /**
     * For SSH to work on windows, the user must have OpenSSL installed.
     * OpenSSL is installed by default with Git for Windows, so we check for that.
     * If it's not installed, we prompt the user to install it.
     *
     * When we first create ssh keys, we know the user is on a local machine, so at
     * that time, we can check for OpenSSL and prompt the user to install it if needed.
     * Then when they connect, we add the path to OpenSSL to the metadata. So
     * then if the user is in a remote environment, we can check the metadata
     * for the path to OpenSSL. If it's not there, we know we need to prompt
     * the user to try again from their local machine.
     */
    async resolveOpenSSL(): Promise<string | null> {
        const isWindows = (await this.fileSystem.getPlatform()) === "win32";
        if (!isWindows) {
            return null;
        }

        const isRemoteEnv = this.fileSystem.isRemoteEnvironment();
        const pathToOpenSSL = await this.fileSystem.getMetadataValue("pathToOpenSSL");
        if (pathToOpenSSL) {
            return pathToOpenSSL;
        }

        if (isRemoteEnv) {
            return null;
        }

        if (!pathToOpenSSL) {
            try {
                // We are on local machine so we can run "git --exec-path". This will give
                // something like C:/Program Files/Git/clangarm64/libexec/git-core. We want
                // to then strip it down to just C:/Program Files/Git/ then append "bin/openssl.exe",
                // and change the slashes to forward slashes.
                const gitExecPath = await new Promise<string>((resolve, reject) => {
                    exec(
                        "git --exec-path",
                        { timeout: 10000, windowsHide: true },
                        (error, stdout, stderr) => {
                            if (error) {
                                if (stderr && stderr.trim()) {
                                    reject(new Error(`${error.message}. stderr: ${stderr.trim()}`));
                                } else {
                                    reject(error);
                                }
                                return;
                            }
                            resolve(stdout.trim());
                        }
                    );
                });

                // prefix is something like C:/Program Files/Git
                const prefix = gitExecPath.substring(0, gitExecPath.lastIndexOf("Git") + 3);
                // replace forward slashes with backslashes
                const pathToOpenSSL = `${prefix}/usr/bin/openssl.exe`.replace(/\//g, "\\");
                // Should look like "C:\\Program Files\\Git\\usr\\bin\\openssl.exe"
                await this.fileSystem.addMetadata("pathToOpenSSL", pathToOpenSSL);
                return pathToOpenSSL;
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error("Error resolving OpenSSL path:", error);
            }
        }

        return null;
    }

    /**
     * Checks if a host exists in the SSH config
     */
    async hasHost(remoteAgentId: string): Promise<boolean> {
        const content = await this.loadAugmentConfig();
        const hostRegex = createHostRegex(remoteAgentId, true); // Use exactMatch=true to only match the Host line
        return hostRegex.test(content);
    }

    async willWriteToSSHDefaultConfig(): Promise<boolean> {
        const [createConfig, writeToConfig] = await Promise.all([
            this.ensureAugmentConfigExists(true),
            this.addIncludeToDefaultConfig(true),
        ]);
        return createConfig || writeToConfig;
    }

    /**
     * Adds an Include statement to the user's default SSH config file
     * to include our Augment SSH config file
     */
    async addIncludeToDefaultConfig(dryRun: boolean = false): Promise<boolean> {
        const defaultConfigPath = await this.getDefaultConfigPath();
        const defaultConfigExists = await this.fileSystem.fileExists(defaultConfigPath);

        if (!defaultConfigExists) {
            const augmentConfigPath = await this.getAugmentConfigPath();
            const includeStatement = `Include ${augmentConfigPath}`;
            const content = `${AUGMENT_SSH_CONFIG_COMMENT}\n${includeStatement}\n`;
            if (!dryRun) {
                await this.saveToDefaultConfig(content);
            }
            return true;
        }

        const defaultConfigContent = await this.loadDefaultConfig();
        const augmentConfigPath = await this.getAugmentConfigPath();

        const includeRegex = createIncludeRegex(augmentConfigPath);
        if (includeRegex.test(defaultConfigContent)) {
            return false;
        }

        const includeStatement = `Include ${augmentConfigPath}`;
        const newContent = `${AUGMENT_SSH_CONFIG_COMMENT}\n${includeStatement}\n\n${defaultConfigContent}`;
        if (!dryRun) {
            await this.saveToDefaultConfig(newContent);
        }
        return true;
    }

    /**
     * Ensures the Augment SSH config file exists.
     * If it doesn't, creates it with a default comment.
     */
    async ensureAugmentConfigExists(dryRun: boolean = false): Promise<boolean> {
        const configPath = await this.getAugmentConfigPath();
        const exists = await this.fileSystem.fileExists(configPath);

        if (!exists) {
            if (!dryRun) {
                await this.saveToAugmentConfig("# Augment SSH Configuration\n");
            }
            return true;
        }
        return false;
    }

    async updateHostConfigForMacAndLinux(
        hostConfig: RemoteAgentSSHConfig
    ): Promise<RemoteAgentSSHConfig> {
        const newConfig = { ...hostConfig };

        // Check for the presence of options related to HostKey verification.
        const hasKnownHostsCommand = hostConfig.ssh_config_options.some(
            (opt) => opt.key === "KnownHostsCommand"
        );
        const hasStrictHostKeyChecking = hostConfig.ssh_config_options.some(
            (opt) => opt.key === "StrictHostKeyChecking"
        );
        const hasUserKnownHostsFile = hostConfig.ssh_config_options.some(
            (opt) => opt.key === "UserKnownHostsFile"
        );

        // Set defaults for StrictHostKeyChecking and UserKnownHostsFile based on the presence of KnownHostsCommand.
        if (hasKnownHostsCommand) {
            if (!hasStrictHostKeyChecking) {
                newConfig.ssh_config_options.push({
                    key: "StrictHostKeyChecking",
                    value: "yes",
                });
            }
            if (!hasUserKnownHostsFile) {
                newConfig.ssh_config_options.push({
                    key: "UserKnownHostsFile",
                    value: "/dev/null",
                });
            }
        } else {
            if (!hasStrictHostKeyChecking) {
                // Don't bother the user with HostKeys, use Trust On First Use. The user should still be able
                // to connect even if the HostKeys change, however there will be a warning and certain features
                // (like agent forwarding) will be disabled.
                newConfig.ssh_config_options.push({
                    key: "StrictHostKeyChecking",
                    value: "no",
                });
            }
            if (!hasUserKnownHostsFile) {
                // Use a custom known_hosts file alongside custom config and keys path in ~/.augment.
                newConfig.ssh_config_options.push({
                    key: "UserKnownHostsFile",
                    value: await this.fileSystem.getSshKnownHostsPath(),
                });
            }
        }

        const isMac = (await this.fileSystem.getPlatform()) === "darwin";
        if (isMac) {
            // On a Mac, it's possible that openssl isn't able to find the system certificates.
            // We need to pass them in explicitly.
            const caFileCommand =
                "<(security find-certificate -a -p /System/Library/Keychains/SystemRootCertificates.keychain)";
            const proxyCommandIndex = newConfig.ssh_config_options.findIndex(
                (opt) => opt.key === "ProxyCommand"
            );
            if (proxyCommandIndex !== -1) {
                const proxyCommand = newConfig.ssh_config_options[proxyCommandIndex].value;
                const newProxyCommand = `${proxyCommand} -CAfile ${caFileCommand}`;
                newConfig.ssh_config_options[proxyCommandIndex].value = newProxyCommand;
            }
        }

        return newConfig;
    }

    async updateHostConfigForWindows(
        hostConfig: RemoteAgentSSHConfig
    ): Promise<RemoteAgentSSHConfig> {
        const newConfig = { ...hostConfig };
        let pathToOpenSSL = await this.resolveOpenSSL();
        if (!pathToOpenSSL) {
            throw new Error(
                "Git for Windows is required to connect to remote agents on Windows. Please install it and try again."
            );
        }
        // Wrap the path in quotes in case it contains spaces
        pathToOpenSSL = `"${pathToOpenSSL}"`;
        const options = newConfig.ssh_config_options;

        const proxyCommandIndex = options.findIndex((opt) => opt.key === "ProxyCommand");
        const proxyHostName =
            options.find((opt) => opt.key === "X-Augment-Proxy-Hostname")?.value ?? "";
        const openSslCommand =
            options.find((opt) => opt.key === "X-Augment-Proxy-OpenSSL-Command")?.value ?? "";

        if (!proxyHostName && !openSslCommand) {
            throw new Error(
                "X-Augment-Proxy-Hostname and X-Augment-Proxy-OpenSSL-Command not found in SSH config options. Please try again."
            );
        }
        const hasProxyCommand = proxyCommandIndex !== -1;
        let proxyCommand = "";
        if (openSslCommand) {
            proxyCommand = `${pathToOpenSSL} ${openSslCommand}`;
        } else if (proxyHostName) {
            proxyCommand = `${pathToOpenSSL} s_client -connect ${proxyHostName}:"%p" -quiet -verify_return_error -servername "%h"`;
        }
        if (hasProxyCommand) {
            newConfig.ssh_config_options[proxyCommandIndex].value = proxyCommand;
        } else {
            newConfig.ssh_config_options.push({
                key: "ProxyCommand",
                value: proxyCommand,
            });
        }

        // Remove the KnownHostsCommand, we replace it with the known_hosts file
        newConfig.ssh_config_options = newConfig.ssh_config_options.filter(
            (opt) => opt.key !== "KnownHostsCommand"
        );
        const knownHosts = newConfig.ssh_config_options
            .filter((opt) => opt.key === "X-Augment-HostKey")
            .map((opt) => opt.value);
        const hostName = hostConfig.hostname;
        const port = hostConfig.ssh_config_options.find((opt) => opt.key === "Port")?.value ?? "";
        // If no port, just do <hostname> <key>
        // Otherwise, format is [<hostname>]:<port> <key> for each key
        const knownHostsContent = knownHosts
            .map((key) => {
                if (port) {
                    return `[${hostName}]:${port} ${key}`;
                }
                return `${hostName} ${key}`;
            })
            .join("\n");

        if (knownHostsContent) {
            await this.writeKnownHostsForCurrentWorkspace(knownHostsContent);
        }

        // Update the UserKnownHostsFile to point to our custom known_hosts file
        const userKnownHostsFileIndex = newConfig.ssh_config_options.findIndex(
            (opt) => opt.key === "UserKnownHostsFile"
        );
        if (userKnownHostsFileIndex !== -1) {
            const knownHostsPath = await this.fileSystem.getSshKnownHostsPath();
            newConfig.ssh_config_options[userKnownHostsFileIndex].value = knownHostsPath;
        } else if (knownHostsContent) {
            newConfig.ssh_config_options.push({
                key: "UserKnownHostsFile",
                value: await this.fileSystem.getSshKnownHostsPath(),
            });
        }

        return newConfig;
    }

    /**
     * Writes known hosts content to the appropriate workspace-specific file
     */
    async writeKnownHostsForCurrentWorkspace(knownHostsContent: string): Promise<void> {
        await this.fileSystem.ensureSshKnownHostsDirExists();
        const knownHostsPath = await this.fileSystem.getSshKnownHostsPath();
        const prevContent = await this.fileSystem.readFile(knownHostsPath);
        const newContent = prevContent + "\n" + knownHostsContent;
        await this.fileSystem.writeFile(knownHostsPath, newContent, 0o644);
    }

    /**
     * Updates the host config with any missing options or
     * platform-specific transformations.
     */
    async updateHostConfig(
        hostConfig: RemoteAgentSSHConfig,
        privateKeyPath: string
    ): Promise<RemoteAgentSSHConfig> {
        // Add the private key path bc the API doesn't return it.
        hostConfig.ssh_config_options.push({
            key: "IdentityFile",
            value: privateKeyPath,
        });

        const isWindows = (await this.fileSystem.getPlatform()) === "win32";
        let newConfig: RemoteAgentSSHConfig;
        if (isWindows) {
            newConfig = await this.updateHostConfigForWindows(hostConfig);
        } else {
            newConfig = await this.updateHostConfigForMacAndLinux(hostConfig);
        }

        // Remove any X-Augment-* options
        newConfig.ssh_config_options = newConfig.ssh_config_options.filter(
            (opt) => !opt.key.startsWith("X-Augment-")
        );
        // Remove IgnoreUnknown if it's set to X-Augment-*
        if (
            newConfig.ssh_config_options.find(
                (opt) => opt.key === "IgnoreUnknown" && opt.value === "X-Augment-*"
            )
        ) {
            newConfig.ssh_config_options = newConfig.ssh_config_options.filter(
                (opt) => opt.key !== "IgnoreUnknown"
            );
        }

        // For each option, replace the home dir path with ~
        // This lets us avoid issues with paths containing spaces or other special characters
        // Also, the ssh config file is fine using ~ on any of Mac/Linux/Windows
        const homeDir = await this.fileSystem.getHomeDirectory();
        newConfig.ssh_config_options = newConfig.ssh_config_options.map((opt) => {
            if (opt.value.startsWith(homeDir)) {
                opt.value = opt.value.replace(homeDir, "~");
                // If using ~, replace all backslashes with forward slashes. On Windows, path expansion
                // in the ssh config only works with forward slashes.
                opt.value = opt.value.replace(/\\/g, "/");
            }
            return opt;
        });

        return newConfig;
    }

    /**
     * Connect to an SSH host
     *
     * @param remoteAgentId The ID of the remote agent to connect to
     * @param config Optional host configuration to add or update before connecting
     */
    async connectToHost(remoteAgentId: string, config?: RemoteAgentSSHConfig): Promise<void> {
        if (config) {
            await this.addHost(remoteAgentId, config, MAX_HOSTS);
        }

        await this.ensureAugmentConfigExists();
        await this.addIncludeToDefaultConfig();

        await withTemporarySSHSettings(async () => {
            await openSSHWindow(remoteAgentId);

            // Give the window time to open before restoring the config
            return new Promise<void>((resolve) => {
                // We need to wait a bit to ensure the window has time to open
                // before we restore the original config
                setTimeout(resolve, this.timeoutMs);
            });
        });
    }
}
