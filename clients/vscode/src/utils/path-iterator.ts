import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import * as pathLib from "node:path";
import * as vscode from "vscode";

import { type AugmentLogger, getLogger } from "../logging";
import { readDirectorySync } from "./fs-utils";
import { AcceptPathImplicit, IgnoreStack, IgnoreStackBuilder } from "./ignore-file";
import { MetricsCollector } from "./metrics";
import { PathAcceptance, RejectPath } from "./path-acceptance";
import {
    directoryContainsPath,
    dirName,
    isAbsolutePathName,
    pathNameSansSep,
    relativeDirName,
} from "./path-utils";
import { FileType } from "./types";
import { uriToAbsPath } from "./uri";

export async function makePathFilter(
    startUri: vscode.Uri,
    rootUri: vscode.Uri,
    ignoreStackBuilder: IgnoreStackBuilder,
    fileExtensions: Set<string> | undefined
): Promise<FullPathFilter> {
    const initialIgnoreStack = await ignoreStackBuilder.build(startUri, rootUri);

    const ignorePathMap = new Map<string, IgnoreStack>();
    ignorePathMap.set("", initialIgnoreStack);
    ignorePathMap.set(".", initialIgnoreStack);

    // This function is performance sensitive, so it is important that it go async as little as
    // possible. The variables below control a hand-coded periodic yielder that causes it to yield
    // every `yieldMs` milliseconds to avoid blocking the event loop while minimizing awaits. This
    // yielding method is faster, if uglier, than the periodicYielder() function used elsewhere,
    // mainly because it only goes async when needed.
    // We yield at the top of both of the loops below, as either one can theoretically become a
    // tight loop independently of the other.
    const yieldMs = 200;
    let lastYield = Date.now();

    const directoryUris: [vscode.Uri, IgnoreStack][] = [];
    directoryUris.push([startUri, initialIgnoreStack]);
    let nextItem;
    while ((nextItem = directoryUris.pop()) !== undefined) {
        // See comments above regarding yielding.
        const now = Date.now();
        if (now - lastYield >= yieldMs) {
            await new Promise((resolve) => setTimeout(resolve, 0));
            lastYield = Date.now();
        }

        const [currDirUri, parentIgnoreStack] = nextItem;
        const currDirRel = relativeDirName(rootUri, currDirUri);

        // Retrieve the list of [name, type] pairs in currDirUri.
        const dirEntries = readDirectorySync(currDirUri.fsPath);

        // Create an ignore stack by placing any ignore files in currDirUri on top of the current
        // ignore stack. Save the result in the ignorePathMap, but only if currDir actually has any
        // ignore files. (This makes the map considerably smaller).
        const ignoreStack = await parentIgnoreStack.buildAtop(currDirUri, dirEntries);
        if (ignoreStack !== parentIgnoreStack) {
            ignorePathMap.set(currDirRel, ignoreStack);
        }

        // Find all the subdirectories of currDir and enqueue those that are not rejected by
        // the ignore stack.
        for (const [fileName, fileType] of dirEntries) {
            // See comments above regarding yielding.
            const now = Date.now();
            if (now - lastYield >= yieldMs) {
                await new Promise((resolve) => setTimeout(resolve, 0));
                lastYield = Date.now();
            }

            if (fileName === "." || fileName === ".." || fileType !== FileType.directory) {
                continue;
            }

            const fileUri = vscode.Uri.joinPath(currDirUri, fileName);
            const relativePath = joinPath(currDirRel, fileName, true);
            const acceptResult = ignoreStack.getPathInfo(relativePath);
            if (!acceptResult.accepted) {
                continue;
            }
            directoryUris.push([fileUri, ignoreStack]);
        }
    }

    return new FullPathFilter(ignorePathMap, fileExtensions);
}

/**
 * PathIterator enumerates the set of files found under the directory specified by
 * `startUri`. The paths returned are relative to the given `rootUri`, which can be
 * different from startUri. If these two directories are different then startUri must
 * be under rootUri, otherwise an exception will be thrown. In practice, think of
 * `startUri` being the folder opened by this VSCode session, while `rootUri` is the
 * root of the repo, as indicated by the .augmentroot file.
 *
 * PathIterator will only return paths that are accepted by the given PathFilter. The
 * startUri and rootUri must match those that were used to create the PathFilter.
 */
export class PathIterator {
    public readonly stats = new MetricsCollector("Path metrics");
    // private _stats = new PathIteratorStats();
    private readonly _logger: AugmentLogger = getLogger("PathIterator");

    private _dirsEmitted = this.stats.counterMetric("directories emitted");
    private _filesEmitted = this.stats.counterMetric("files emitted");
    private _otherEmitted = this.stats.counterMetric("other paths emitted");
    private _totalEmitted = this.stats.counterMetric("total paths emitted");
    private _readDirMs = this.stats.timingMetric("readDir");
    private _filterMs = this.stats.timingMetric("filter");
    private _yieldMs = this.stats.timingMetric("yield");
    private _totalMs = this.stats.timingMetric("total");

    /**
     * This class uses a convention that Uris always contain absolute path names,
     * while any path names in strings are normalized, relative paths.
     */

    constructor(
        private readonly _name: string,
        private readonly _startUri: vscode.Uri,
        private readonly _rootUri: vscode.Uri,
        private readonly _pathFilter: FullPathFilter
    ) {
        if (!pathLib.isAbsolute(_startUri.fsPath)) {
            throw new Error(
                `PathIterator[${this._name}]: ` +
                    `startUri ${this._name} must contain an absolute pathname`
            );
        }
        if (!pathLib.isAbsolute(_rootUri.fsPath)) {
            throw new Error(
                `PathIterator[${this._name}]: ` +
                    `rootUri ${_rootUri.toString()} must contain an absolute pathname`
            );
        }
        if (!directoryContainsPath(uriToAbsPath(_rootUri), uriToAbsPath(_startUri))) {
            throw new Error(
                `PathIterator[${this._name}]: ` +
                    `startUri ${uriToAbsPath(this._startUri)} must be inside rootUri ${uriToAbsPath(
                        this._rootUri
                    )}`
            );
        }

        this._logger.verbose(
            `Created PathIterator for startUri ${this._startUri.fsPath}, ` +
                `rootUri ${this._rootUri.fsPath}`
        );
    }

    // Iterator that produces a stream of [fileUri, relativePath] pairs for the relevant files in
    // startUri. The fileUri and relativePath in each pair both refer to the same file, just with
    // different representations. The uri is useful for opening the file, since it is absolute,
    // while the path is useful for Augment naming, since it is relative and normalized.
    public async *[Symbol.asyncIterator](): AsyncGenerator<
        [vscode.Uri, string, FileType, PathAcceptance]
    > {
        this._totalMs.start();

        // This function is performance sensitive, so it is important that it go async as little as
        // possible. The variables below control a hand-coded periodic yielder that causes it to
        // yield every `yieldMs` milliseconds to avoid blocking the event loop while minimizing
        // awaits. This yielding method is faster, if uglier, than the periodicYielder() function
        // used elsewhere, mainly because it only goes async when needed. We yield at the top of
        // both of the loops below, as either one can theoretically become a tight loop
        // independently of the other.
        const yieldMs = 200;
        let lastYield = Date.now();

        const directoryUris = new Array<vscode.Uri>();
        directoryUris.push(this._startUri);
        let currDirUri;
        while ((currDirUri = directoryUris.pop()) !== undefined) {
            // See comments above regarding yielding.
            const now = Date.now();
            if (now - lastYield >= yieldMs) {
                await new Promise((resolve) => setTimeout(resolve, 0));
                lastYield = Date.now();
            }

            const currDirRel = relativeDirName(this._rootUri, currDirUri);
            const pathFilter = this._pathFilter.makeLocalPathFilter(currDirRel);

            // Retrieve the list of [name, type] pairs in currDirUri.
            this._readDirMs.start();
            const dirEntries = readDirectorySync(currDirUri.fsPath);
            this._readDirMs.stop();

            // Iterate over the entries in currDir, emitting all of them and pushing any accepted
            // directories onto the directoryUris stack to be processed in a later iteration.
            for (const [fileName, fileType] of dirEntries) {
                // See comments above regarding yielding.
                const now = Date.now();
                if (now - lastYield >= yieldMs) {
                    await new Promise((resolve) => setTimeout(resolve, 0));
                    lastYield = Date.now();
                }

                if (fileName === "." || fileName === "..") {
                    continue;
                }

                this._filterMs.start();
                const fileUri = vscode.Uri.joinPath(currDirUri, fileName);
                const relativePath = joinPath(
                    currDirRel,
                    fileName,
                    fileType === FileType.directory
                );
                const acceptance = pathFilter.getPathInfo(relativePath, fileType);
                this._filterMs.stop();

                let pathToEmit = relativePath;
                if (fileType === FileType.file) {
                    this._filesEmitted.increment();
                } else if (fileType === FileType.directory) {
                    pathToEmit = pathNameSansSep(relativePath);
                    this._dirsEmitted.increment();
                } else {
                    this._otherEmitted.increment();
                }
                this._totalEmitted.increment();

                this._yieldMs.start();
                yield [fileUri, pathToEmit, fileType, acceptance];
                this._yieldMs.stop();

                if (fileType === FileType.directory && acceptance.accepted) {
                    // This is a directory that is accepted. Arrange to process it in a later
                    // iteration.
                    directoryUris.push(fileUri);
                }
            }
        }

        this._totalMs.stop();
    }
}

export class UnsupportedFileExtension extends RejectPath {
    constructor(public readonly extension: string) {
        super();
    }

    public format(): string {
        return `Unsupported file extension (${this.extension})`;
    }
}

/**
 * PathFilter is an abstract base class for filtering paths on the contents of
 * IgnoreStacks and, optionally, file extensions. The ignore stacks are provided
 * by its subclasses.
 */
export abstract class PathFilter {
    protected readonly _fileExtensions?: Set<string>;

    constructor(fileExtensions?: Set<string>) {
        if (fileExtensions) {
            this._fileExtensions = new Set();
            for (const extension of fileExtensions) {
                this._fileExtensions.add(extension.toLowerCase());
            }
        } else {
            this._fileExtensions = undefined;
        }
    }

    // acceptsPath indicates whether the given path is accepted by the PathFilter. The path must
    // be a relative path that is relative to the directory for which the PathFilter was created.
    // (eg, the rootUri that was passed to makePathFilter).
    public acceptsPath(path: string, fileType = FileType.file): boolean {
        return this.getPathInfo(path, fileType).accepted;
    }

    // getPathInfo indicates whether the given path is accepted by the PathFilter, providing
    // information about why it was or was not accepted. The path must be a relative path that is
    // relative to the directory for which the PathFilter was created. (eg, the rootUri that was
    // passed to makePathFilter).
    public getPathInfo(path: string, fileType = FileType.file): PathAcceptance {
        if (fileType === FileType.file) {
            const extension = pathLib.extname(path);
            if (
                this._fileExtensions !== undefined &&
                !this._fileExtensions.has(extension.toLowerCase())
            ) {
                return new UnsupportedFileExtension(extension);
            }
        } else if (fileType === FileType.directory && !path.endsWith("/")) {
            // The node-ignore library requires that a directory name end with a separator. If it
            // doesn't, it will be treated as a regular file, and directory-specific rules (eg.
            // "node_modules/") will not be applied. Note that this is a forward slash on all
            // platforms, because git ignore file rules always use forward slashes.
            path += "/";
        }
        const ignoreStack = this._getIgnoreStack(dirName(path));
        if (ignoreStack === undefined) {
            return new AcceptPathImplicit();
        } else {
            return ignoreStack.getPathInfo(path);
        }
    }

    // _getIgnoreStack returns the IgnoreStack for the given directory, or undefined if there is
    // none.
    protected abstract _getIgnoreStack(dirPath: string): IgnoreStack | undefined;
}

/**
 * FullPathFilter is a PathFilter for an entire directory tree. Its results are valid for any
 * path in the tree.
 */
export class FullPathFilter extends PathFilter {
    constructor(
        private readonly _ignorePathMap: Map<string, IgnoreStack>,
        _fileExtensions: Set<string> | undefined
    ) {
        super(_fileExtensions);
    }

    // makeLocalPathFilter returns a LocalPathFilter for the given directory.
    public makeLocalPathFilter(dirPath: string): LocalPathFilter {
        const ignoreStack = this._getIgnoreStack(dirPath);
        return new LocalPathFilter(ignoreStack, this._fileExtensions);
    }

    protected _getIgnoreStack(dirPath: string): IgnoreStack | undefined {
        if (isAbsolutePathName(dirPath)) {
            throw new Error(`Absolute path ${dirPath} passed to PathFilter`);
        }
        let currDir = dirPath;
        for (let iteration = 0; iteration < 10000; iteration++) {
            const ignoreStack = this._ignorePathMap.get(currDir);
            if (ignoreStack) {
                return ignoreStack;
            }
            const parentDir = dirName(currDir);
            if (parentDir === currDir) {
                return undefined;
            }
            currDir = dirName(currDir);
        }
        throw new Error(`Too-deep or malformed directory name ${dirPath}`);
    }
}

/**
 * LocalPathFilter is a PathFilter for a single directory. Its results are only valid for the
 * immediate entries of the directory it is created for. It can be used as an optimization to
 * avoid looking up the same directory in a FullPathFilter.
 */
export class LocalPathFilter extends PathFilter {
    constructor(
        private readonly _ignoreStack: IgnoreStack | undefined,
        _fileExtensions: Set<string> | undefined
    ) {
        super(_fileExtensions);
    }

    protected _getIgnoreStack(_path: string): IgnoreStack | undefined {
        return this._ignoreStack;
    }
}
