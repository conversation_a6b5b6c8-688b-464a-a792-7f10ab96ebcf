/**
 * Represents a setup script with its name, path, content, and location
 */

export interface SetupScript {
    name: string;
    path: string;
    content: string;
    location: SetupScriptLocation;
    isGenerateOption?: boolean;
    icon?: string;
    type?: "option";
}

export type SetupScriptTypes = "basic" | "auto" | "manual";

export type SetupScriptLocation = "home" | "git" | "workspace";

export type LastUsedRemoteAgentSetup = {
    lastRemoteAgentGitRepoUrl: string | null;
    lastRemoteAgentGitBranch: string | null;
    lastRemoteAgentSetupScript: string | null;
};
