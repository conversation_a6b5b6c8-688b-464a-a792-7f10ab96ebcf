import * as fs from "fs";
import * as path from "path";
import * as vscode from "vscode";

import { AugmentGlobalState } from "../context";
import { findGitRoot, getWorkspaceRoot, readFileUtf8 } from "../fs-utils";
import { SshFileSystem } from "../ssh/ssh-file-system";
import { SetupScript, SetupScriptLocation } from "./types";

/**
 * Manages setup scripts for remote agents
 * Handles finding, reading, and saving setup scripts in various locations
 */
export class SetupScriptsManager {
    private sshFileSystem: SshFileSystem;
    private readonly augmentDirPath = [".augment", "env"];

    constructor(globalState?: AugmentGlobalState) {
        this.sshFileSystem = new SshFileSystem(globalState);
    }

    /**
     * Checks if a file is a valid setup script
     *
     * @param filename The name of the file to check
     * @returns true if the file is a valid setup script, false otherwise
     */
    private isValidScriptName(filename: string): boolean {
        return filename.endsWith(".sh");
    }

    /**
     * Lists all available setup scripts from multiple locations:
     * - ~/.augment/env/
     * - <git root>/.augment/env/
     * - <workspace root>/.augment/env/
     *
     * @returns Array of setup scripts with name, path, and content
     */
    async listSetupScripts(): Promise<SetupScript[]> {
        const scripts: SetupScript[] = [];

        try {
            // 1. Check user's home directory
            const homeScripts = await this.getScriptsFromHomeDirectory();
            scripts.push(...homeScripts);

            // 2. Check git root directory
            const gitRootScripts = await this.getScriptsFromGitRoot();
            scripts.push(...gitRootScripts);

            // 3. Check workspace root directory
            const workspaceScripts = await this.getScriptsFromWorkspaceRoot();
            scripts.push(...workspaceScripts);

            // 4. Remove duplicates
            const uniqueScripts = new Map<string, SetupScript>();
            for (const script of scripts) {
                uniqueScripts.set(script.name + script.path, script);
            }

            return Array.from(uniqueScripts.values());
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Error listing setup scripts:", error);
            throw error;
        }
    }

    /**
     * Saves a setup script to the specified location
     * If location is "home", the script is saved to the user's LOCAL home directory
     * If location is "git" or "workspace", the script is saved to the current workspace
     *
     * @param name The name of the script (filename without path)
     * @param content The content of the script
     * @param location The location to save the script (home, git, workspace)
     * @returns The path where the script was saved
     */
    private async saveSetupScript(
        name: string,
        content: string,
        location: SetupScriptLocation
    ): Promise<string> {
        try {
            let targetDir: string;
            let homeDir: string;
            let gitRoot: string | undefined;
            let workspaceRoot: string | undefined;

            if (location === "home") {
                homeDir = await this.sshFileSystem.getHomeDirectory();
                targetDir = await this.sshFileSystem.joinPath(homeDir, ...this.augmentDirPath);
                await this.ensureDirectoryExists(targetDir, true);
                const filePath = await this.sshFileSystem.joinPath(targetDir, name);
                await this.sshFileSystem.writeFile(filePath, content);
                return filePath;
            }

            if (location === "git") {
                gitRoot = await findGitRoot();
                if (!gitRoot) {
                    throw new Error("Git root directory not found");
                }
                targetDir = await this.sshFileSystem.joinPath(gitRoot, ...this.augmentDirPath);
            } else if (location === "workspace") {
                workspaceRoot = getWorkspaceRoot();
                if (!workspaceRoot) {
                    throw new Error("Workspace root directory not found");
                }
                targetDir = await this.sshFileSystem.joinPath(
                    workspaceRoot,
                    ...this.augmentDirPath
                );
            } else {
                // This should never happen due to the type constraint
                throw new Error("Invalid location");
            }

            await this.ensureDirectoryExists(targetDir, false);
            const filePath = path.join(targetDir, name);
            await fs.promises.writeFile(filePath, content, { mode: 0o755 }); // Make executable

            return filePath;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error saving setup script to ${location}:`, error);
            throw error;
        }
    }

    /**
     * Gets setup scripts from the user's home directory
     */
    private async getScriptsFromHomeDirectory(): Promise<SetupScript[]> {
        const homeDir = await this.sshFileSystem.getHomeDirectory();
        const scriptsDir = await this.sshFileSystem.joinPath(homeDir, ...this.augmentDirPath);

        return await this.getScriptsFromLocalDirectory(scriptsDir, "home");
    }

    /**
     * Gets setup scripts from the git root directory
     */
    private async getScriptsFromGitRoot(): Promise<SetupScript[]> {
        const gitRoot = await findGitRoot();
        if (!gitRoot) {
            return [];
        }

        const scriptsDir = await this.sshFileSystem.joinPath(gitRoot, ...this.augmentDirPath);
        return await this.getScriptsFromWorkspaceDirectory(scriptsDir, "git");
    }

    /**
     * Gets setup scripts from the workspace root directory
     */
    private async getScriptsFromWorkspaceRoot(): Promise<SetupScript[]> {
        const workspaceRoot = getWorkspaceRoot();
        if (!workspaceRoot) {
            return [];
        }

        const scriptsDir = await this.sshFileSystem.joinPath(workspaceRoot, ...this.augmentDirPath);
        return await this.getScriptsFromWorkspaceDirectory(scriptsDir, "workspace");
    }

    /**
     * Gets scripts from a directory on the local filesystem (using SshFileSystem)
     * This is used for the user's home directory, which should be accessed on the local machine
     * even when running in a remote environment
     */
    private async getScriptsFromLocalDirectory(
        directory: string,
        source: SetupScriptLocation
    ): Promise<SetupScript[]> {
        const scripts: SetupScript[] = [];

        try {
            // Check if directory exists
            const exists = await this.sshFileSystem.directoryExists(directory);
            if (!exists) {
                return [];
            }

            // Create a URI that works for both local and remote environments
            const uri = this.sshFileSystem.createFileUri(directory);
            try {
                const entries = await vscode.workspace.fs.readDirectory(uri);

                for (const [fileName, fileType] of entries) {
                    if (fileType === vscode.FileType.File && this.isValidScriptName(fileName)) {
                        try {
                            const filePath = await this.sshFileSystem.joinPath(directory, fileName);
                            const content = await this.sshFileSystem.readFile(filePath);
                            scripts.push({
                                name: fileName,
                                path: filePath,
                                content,
                                location: source,
                            });
                        } catch (error) {
                            // eslint-disable-next-line no-console
                            console.error(`Error reading local file ${fileName}:`, error);
                            // Continue with other files
                        }
                    }
                }
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(`Error reading local directory ${directory}:`, error);
                return [];
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error accessing local directory ${directory}:`, error);
            // Return whatever scripts we were able to read
        }

        return scripts;
    }

    /**
     * Gets scripts from a directory in the workspace
     * This is used for git root and workspace directories
     */
    private async getScriptsFromWorkspaceDirectory(
        directory: string,
        source: SetupScriptLocation
    ): Promise<SetupScript[]> {
        const scripts: SetupScript[] = [];

        try {
            if (!fs.existsSync(directory)) {
                return [];
            }
            const files = await fs.promises.readdir(directory);

            for (const file of files) {
                if (!this.isValidScriptName(file)) {
                    continue;
                }

                const filePath = path.join(directory, file);

                // Confirm it's a file
                const stats = await fs.promises.stat(filePath);
                if (stats.isFile()) {
                    try {
                        const content = await readFileUtf8(filePath);
                        scripts.push({
                            name: file,
                            path: filePath,
                            content,
                            location: source,
                        });
                    } catch (error) {
                        // eslint-disable-next-line no-console
                        console.error(`Error reading file ${filePath}:`, error);
                        // Continue with other files
                    }
                }
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error reading directory ${directory}:`, error);
            // Return whatever scripts we were able to read
        }

        return scripts;
    }

    /**
     * Ensures a directory exists, creating it if necessary
     * @param directory The directory path to ensure exists
     * @param isLocalPath Whether this is a path on the local machine (vs. workspace)
     */
    private async ensureDirectoryExists(
        directory: string,
        isLocalPath: boolean = false
    ): Promise<void> {
        try {
            if (isLocalPath && this.sshFileSystem.isRemoteEnvironment()) {
                // Only use SSH file system if the path is local but we're in a remote environment
                await this.sshFileSystem.mkdir(directory);
            } else {
                // For workspace paths in a local environment, use regular fs
                await fs.promises.mkdir(directory, { recursive: true });
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error creating directory ${directory}:`, error);
            throw error;
        }
    }

    /**
     * Creates a new setup script file with the given name, content, and location
     * If a file with the same name already exists, a number will be appended to the name
     *
     * @param location The location to save the script (home, git, workspace)
     * @param desiredName The desired name for the script
     * @param content The content of the script
     * @returns The path where the script was saved
     */
    public async createSetupScriptFile(
        location: SetupScriptLocation,
        desiredName: string,
        content: string
    ): Promise<string> {
        if (!this.isValidScriptName(desiredName)) {
            throw new Error("Invalid script name");
        }

        const lastDotIndex = desiredName.lastIndexOf(".");
        const [baseName, extension] =
            lastDotIndex !== -1
                ? [desiredName.substring(0, lastDotIndex), desiredName.substring(lastDotIndex)]
                : [desiredName, ""];

        let name = desiredName;
        const currentScripts = await this.listSetupScripts();
        const existingScript = currentScripts.find(
            (script) => script.name === name && script.location === location
        );
        if (existingScript) {
            // If a script with the same name exists, append a number to the base name only
            let i = 1;
            let newName = `${baseName}(${i})${extension}`;
            while (
                currentScripts.find(
                    (script) => script.name === newName && script.location === location
                )
            ) {
                i++;
                newName = `${baseName}(${i})${extension}`;
            }
            name = newName;
        }

        return await this.saveSetupScript(name, content, location);
    }

    /**
     * Deletes a setup script
     *
     * @param scriptName The name of the script to delete
     * @param location The location of the script (home, git, workspace)
     * @returns True if the script was deleted successfully, false otherwise
     */
    public async deleteSetupScript(
        scriptName: string,
        location: SetupScriptLocation
    ): Promise<boolean> {
        try {
            const scripts = await this.listSetupScripts();
            const script = scripts.find((s) => s.name === scriptName && s.location === location);

            if (!script) {
                return false;
            }

            if (location === "home") {
                // For home directory, use SSH file system
                const uri = this.sshFileSystem.createFileUri(script.path);
                await vscode.workspace.fs.delete(uri);
            } else {
                await fs.promises.unlink(script.path);
            }

            return true;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error deleting setup script ${scriptName}:`, error);
            return false;
        }
    }

    /**
     * Renames a setup script
     *
     * @param oldName The current name of the script
     * @param newName The new name for the script
     * @param location The location of the script (home, git, workspace)
     * @returns The path to the renamed script if successful, undefined otherwise
     */
    public async renameSetupScript(
        oldName: string,
        newName: string,
        location: SetupScriptLocation
    ): Promise<string | undefined> {
        try {
            if (!this.isValidScriptName(newName)) {
                throw new Error(`Invalid script name: ${newName}. Script names must end with .sh`);
            }

            const scripts = await this.listSetupScripts();
            const script = scripts.find((s) => s.name === oldName && s.location === location);

            if (!script) {
                return undefined;
            }

            const dirPath = path.dirname(script.path);
            const newPath = path.join(dirPath, newName);

            let content = "";
            if (location === "home") {
                // For home directory, use SSH file system
                content = await this.sshFileSystem.readFile(script.path);
            } else {
                content = await readFileUtf8(script.path);
            }
            await this.deleteSetupScript(oldName, location);
            await this.createSetupScriptFile(location, newName, content);

            return newPath;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(`Error renaming setup script ${oldName} to ${newName}:`, error);
            throw error;
        }
    }
}
