import type {
    ChangedFile,
    Diff,
    DiffExplanation,
    DiffExplanationSection,
    DiffExplanationSubSection,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { createTwoFilesPatch } from "diff";

/**
 * Helper function to safely extract properties from a ChangedFile object
 */
function extractChangedFileProperties(changedFile: unknown): {
    oldPath: string;
    newPath: string;
    oldContents: string;
    newContents: string;
    id: string;
    changeType: string;
} {
    if (!changedFile || typeof changedFile !== "object") {
        throw new Error("Invalid changed file object");
    }

    const file = changedFile as Record<string, unknown>;

    return {
        oldPath: typeof file.old_path === "string" ? file.old_path : "",
        newPath: typeof file.new_path === "string" ? file.new_path : "",
        oldContents: typeof file.old_contents === "string" ? file.old_contents : "",
        newContents: typeof file.new_contents === "string" ? file.new_contents : "",
        id: typeof file.id === "string" ? file.id : "",
        changeType: typeof file.change_type === "string" ? file.change_type : "modified",
    };
}

type DiffExplanationSectionResponse = {
    title: string;
    description: string;
    sections: DiffExplanationSubSectionResponse[];
};
type DiffExplanationSubSectionResponse = {
    title: string;
    descriptions: DiffExplanationSubSection["descriptions"];
    type:
        | "fix"
        | "feature"
        | "refactor"
        | "documentation"
        | "style"
        | "test"
        | "chore"
        | "revert"
        | "other";
    warning?: string;
    changeIds: string[];
};

/**
 * Prompt character limit
 * If the prompt exceeds this length, then we will recieve a 413 Request Entity Too Large error
 */
export const CHAR_LIMIT = 36_000;

export const getDiffExplanation = async (
    changedFiles: ChangedFile[],
    getResponseFromLLM: (prompt: string) => Promise<string>
): Promise<DiffExplanation> => {
    const changes = changedFiles.map((changedFile) => {
        const { oldPath, newPath, oldContents, newContents } =
            extractChangedFileProperties(changedFile);

        const fileChanges = generateChangesForFile(oldPath, newPath, oldContents, newContents);
        return {
            path: newPath || oldPath,
            changes: fileChanges,
        };
    });

    // Create a simplified version for the LLM prompt
    const succinctChanges = Object.values(changes).map((change) => {
        return {
            path: change.path,
            changes: change.changes.map((c) => ({
                id: c.id,
                path: c.path,
                diff: c.diff,
            })),
        };
    });

    const prompt = `You are a helpful software engineer. You are given a diff of a set of files. Your job is to walk a fellow developer through the changes in the diff.

You need to storytell through the updates, splitting it into two levels of semantic chunks of changes. You should respond with just JSON in this format:
{
    title: string; // these are top-level categories, like "Clean up the Footer" or "New API routes"
    description: string; // use Markdown. eg. put variable names in backticks, strings in quotes in backticks (eg. \`"String"\`)
    sections: { // Keep single files in a single section
        title: string; // Aim for 1 sentence with 5 - 20 words. Use sentence case and don't use generic words like "Update". Be as descriptive as possible, like a good commit message. eg. "Increased heading spacing in footer"
        type: "fix" | "feature" | "refactor" | "documentation" | "style" | "test" | "chore" | "revert" | "other";
        warning?: string; // look for anything that might be important for the user to know. eg. "This implementation isn't complete"
        changeIds: string[]; // Group related changes into a single section. Always combine all changes with the same file path into a single section
        descriptions: { // annotate the change with one or multiple descriptions of what the code change does
            text: string; // use Markdown. eg. put variable names in backticks, strings in quotes in backticks (eg. \`"String"\`). Use → to describe value changes
            range: {
                start: number; // line number, 0-indexed
                end: number; // line number, 0-indexed
            }
        }[];
    }[]
}[]

Here's an example set of changes:

[{
    "path": "src/global.css",
    "changes": [{
    "id": "1",
    "path": "src/global.css",
    "diff": "body {
  font-family: sans-serif;
- background-color: #fff;
+ background-color: #000;
}"
    }
}]

You would respond:

[{
  "title": "Style tweaks",
  "description": "Global style changes across the app",
  "sections": [{
        "title": "Invert background color",
        "descriptions": [{
            "line": 2,
            "text": "Change the background color from white → black"
        }],
        "changeIds": ["1"]
    }]
}]


The changes are:

${JSON.stringify(succinctChanges, null, 2)}

Start your response with [{`;

    const responseString = await getResponseFromLLM(prompt);
    const response = JSON.parse(responseString) as DiffExplanationSectionResponse[];
    const sections = response.map((section) => {
        return {
            title: section.title,
            description: section.description,
            sections: section.sections.map((subsection) => {
                const mappedChanges: Diff[] = subsection.changeIds
                    .map((changeId) => {
                        const fileChange = changes.find((change) =>
                            change.changes.find((c) => c.id === changeId)
                        );
                        if (!fileChange) {
                            return undefined;
                        }
                        const change = fileChange.changes.find((c) => c.id === changeId);
                        return change || undefined;
                    })
                    .filter((change): change is Diff => change !== undefined);

                return {
                    title: subsection.title,
                    descriptions: subsection.descriptions,
                    type: subsection.type,
                    warning: subsection.warning,
                    changes: mappedChanges,
                };
            }),
        };
    });

    return sections;
};

/**
 * Get descriptions for grouped changes using LLM
 */
export const getDiffDescriptions = async (
    groupedChanges: { path: string; changes: Diff[] }[],
    getResponseFromLLM: (prompt: string) => Promise<string>
): Promise<{ sections: DiffExplanationSection[]; error?: string }> => {
    try {
        // Create a simplified version for the LLM prompt
        // Add an index to each file to preserve the original order
        const succinctChanges = groupedChanges.map((change, index: number) => {
            return {
                path: change.path,
                fileIndex: index, // Add index to preserve order
                changes: change.changes.map((c) => ({
                    id: c.id,
                    path: c.path,
                    diff: c.diff,
                })),
            };
        });

        const prompt = `You are a helpful software engineer. You are given a diff of a set of files. Your job is to walk a fellow developer through the changes in the diff.

You need to storytell through the updates, splitting it into two levels of semantic chunks of changes. You should respond with just JSON in this format:
{
    title: string; // these are top-level categories, like "Clean up the Footer" or "New API routes"
    description: string; // use Markdown. eg. put variable names in backticks, strings in quotes in backticks (eg. \`"String"\`)
    sections: { // Keep single files in a single section
        title: string; // Aim for 1 sentence with 5 - 20 words. Use sentence case and don't use generic words like "Update". Be as descriptive as possible, like a good commit message. eg. "Increased heading spacing in footer"
        type: "fix" | "feature" | "refactor" | "documentation" | "style" | "test" | "chore" | "revert" | "other";
        warning?: string; // look for anything that might be important for the user to know. eg. "This implementation isn't complete"
        changeIds: string[]; // Group related changes into a single section. Always combine all changes with the same file path into a single section
        descriptions: { // annotate the change with one or multiple descriptions of what the code change does
            text: string; // use Markdown. eg. put variable names in backticks, strings in quotes in backticks (eg. \`"String"\`). Use → to describe value changes
            range: {
                start: number; // line number, 0-indexed
                end: number; // line number, 0-indexed
            }
        }[];
    }[]
}[]

IMPORTANT: You MUST preserve the original file grouping and order. The files are already grouped correctly in the input and each file has a fileIndex property indicating its original order. Create one subsection per file, and include all changes for that file in that subsection. Do not regroup or reorder the files. The order of files in your response should match the fileIndex order in the input.

Here's an example set of changes:

[{
    "path": "src/global.css",
    "fileIndex": 0,
    "changes": [{
    "id": "1",
    "path": "src/global.css",
    "diff": "body {\n  font-family: sans-serif;\n- background-color: #fff;\n+ background-color: #000;\n}"
    }
}]

You would respond:

[{
  "title": "Style tweaks",
  "description": "Global style changes across the app",
  "sections": [{
        "title": "Invert background color",
        "descriptions": [{
            "line": 2,
            "text": "Change the background color from white → black"
        }],
        "changeIds": ["1"]
    }]
}]


The changes are:

${JSON.stringify(succinctChanges, null, 2)}

Start your response with [{`;

        if (prompt.length > CHAR_LIMIT) {
            // eslint-disable-next-line no-console
            console.warn("getDiffDescription token limit exceeded");
            return { sections: [], error: "Token limit exceeded" };
        }

        const responseString = await getResponseFromLLM(prompt);
        const response = JSON.parse(responseString) as DiffExplanationSectionResponse[];

        // Map the response to include the full changes
        const sections = response.map((section) => {
            return {
                title: section.title,
                description: section.description,
                sections: section.sections.map((subsection) => {
                    const mappedChanges = subsection.changeIds
                        .map((changeId: string) => {
                            const fileChange = groupedChanges.find((change) =>
                                change.changes.find((c) => c.id === changeId)
                            );
                            if (!fileChange) {
                                return undefined;
                            }
                            const change = fileChange.changes.find((c) => c.id === changeId);
                            return change || undefined;
                        })
                        .filter((change): change is any => change !== undefined) as Diff[];

                    return {
                        title: subsection.title,
                        descriptions: subsection.descriptions,
                        type: subsection.type,
                        warning: subsection.warning,
                        changes: mappedChanges,
                    } as DiffExplanationSubSection;
                }),
            } as DiffExplanationSection;
        });

        return { sections };
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error("Failed to get descriptions:", error);
        return {
            sections: [],
            error: `Failed to get descriptions: ${error instanceof Error ? error.message : String(error)}`,
        };
    }
};

export const getResponseFromLLM = async (
    prompt: string,
    anthropicApikey?: string
): Promise<string> => {
    if (!anthropicApikey) {
        return "";
    }
    try {
        const completion = await fetch("https://api.anthropic.com/v1/messages", {
            method: "POST",
            headers: {
                contentType: "application/json",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                "x-api-key": anthropicApikey,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                "anthropic-version": "2023-06-01",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                "anthropic-dangerous-direct-browser-access": "true",
            },
            body: JSON.stringify({
                messages: [{ role: "user", content: prompt }],
                model: "claude-3-7-sonnet-20250219",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                max_tokens: 1000,
                temperature: 0,
            }),
        });
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const response = await completion.json();

        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        const content = (response?.content?.[0]?.text || "") as string;
        if (!content) {
            throw new Error("Invalid response format from Anthropic API");
        }

        return content;
    } catch (e) {
        throw new Error(
            `Failed to get LLM response: ${e instanceof Error ? e.message : String(e)}`
        );
    }
};

/**
 * Generate a stable ID for a file path and content
 * This ensures we have consistent IDs across requests
 *
 * If the input is very large (> 10k chars), we take a hash of the middle of the input
 * to avoid performance issues with hashing large strings
 */
export function generateStableId(input: string): string {
    // Create a simple hash of the input
    let hash = 0;
    // Use a smaller portion of the input if it's very large
    const maxLength = 10000; // Limit to prevent slow hashing
    const str =
        input.length > maxLength
            ? input.substring(0, maxLength / 2) + input.substring(input.length - maxLength / 2)
            : input;

    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
}

export function generateChangesForFile(
    oldPath: string,
    newPath: string,
    oldContent: string,
    newContent: string
): Diff[] {
    const diffResult = createTwoFilesPatch(oldPath, newPath, oldContent, newContent, "", "", {
        context: 3,
    });

    // Generate a stable ID based on the path and content
    const path = oldPath || newPath;
    const pathId = generateStableId(path);
    const contentHash = generateStableId(oldContent + newContent);
    const stableId = `${pathId}-${contentHash}`;

    return [
        {
            id: stableId,
            path,
            diff: diffResult,
            originalCode: oldContent,
            modifiedCode: newContent,
        },
    ];
}

/**
 * Group changes by file using LLM to identify semantic groups
 * This is the first step in the two-step process
 */
export const groupChangesWithLLM = async (
    changedFiles: ChangedFile[],
    getResponseFromLLM: (prompt: string) => Promise<string>,
    useIdsOnly = false
): Promise<{ path: string; changes: Diff[] }[]> => {
    try {
        // Check if we're using the ID-based approach
        if (useIdsOnly) {
            // In ID-based mode, changedFiles already contains IDs
            // We just need to format them correctly for the LLM
            const succinctChanges = changedFiles.map((file) => {
                const { newPath, oldPath, id, changeType } = extractChangedFileProperties(file);

                return {
                    path: newPath || oldPath,
                    changes: [
                        {
                            id,
                            path: newPath || oldPath,
                            diff: `File: ${newPath || oldPath}\nChange type: ${changeType}`,
                            // Add empty fields to match Diff type
                            originalCode: "",
                            modifiedCode: "",
                        },
                    ],
                };
            });

            // Skip to the LLM prompt with just the IDs
            const prompt = `You are a helpful software engineer. You are given a diff of a set of files. Your job is to group related changes together.

You should respond with just JSON in this format:
[{
    "path": string; // The file path
    "changes": [{ // The changes for this file
        "id": string; // The ID of the change
        "path": string; // The file path
    }];
}]

IMPORTANT: Do NOT add any descriptions or explanations. Just group the changes by file. Keep the original IDs intact.

The changes are:

${JSON.stringify(succinctChanges, null, 2)}

Start your response with [{`;

            // Add a timeout to the LLM call
            const LLM_TIMEOUT_MS = 20000; // 20 seconds
            let responseString: string;
            try {
                // Create a promise that rejects after the timeout
                const timeoutPromise = new Promise<string>((_, reject) => {
                    setTimeout(() => reject(new Error("LLM request timed out")), LLM_TIMEOUT_MS);
                });

                // Race the LLM call against the timeout
                responseString = await Promise.race([getResponseFromLLM(prompt), timeoutPromise]);
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error("LLM request timed out or failed:", error);
                // Fall back to simple grouping
                return succinctChanges;
            }

            // Parse the response
            let response: Array<{
                path: string;
                changes: Array<{ id: string; path: string }>;
            }>;
            try {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                response = JSON.parse(responseString) as Array<{
                    path: string;
                    changes: Array<{ id: string; path: string }>;
                }>;
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error("Failed to parse LLM response:", error);
                // Fall back to simple grouping
                return succinctChanges;
            }

            // We need to return the response with just IDs
            // The client will hydrate the full content
            return response.map((group) => ({
                path: group.path,
                changes: group.changes.map((change) => ({
                    ...change,
                    // Add empty fields to match the Diff type
                    // The client will replace these with the actual content
                    originalCode: "",
                    modifiedCode: "",
                    diff: "",
                })),
            }));
        }

        // Standard approach with full content
        // Generate basic changes for each file
        const changes = changedFiles.map((changedFile) => {
            const { oldPath, newPath, oldContents, newContents } =
                extractChangedFileProperties(changedFile);

            const fileChanges = generateChangesForFile(oldPath, newPath, oldContents, newContents);
            return {
                path: newPath || oldPath,
                changes: fileChanges,
            };
        });

        // For large diffs or many files, skip LLM grouping
        const totalDiffSize = changes.reduce(
            (acc, change) => acc + change.changes.reduce((sum, c) => sum + c.diff.length, 0),
            0
        );

        // Skip LLM grouping if:
        // 1. Total diff size is too large (> 30KB)
        // 2. Too many files (> 8)
        // 3. Too many changes (> 15)
        const totalChanges = changes.reduce((acc, change) => acc + change.changes.length, 0);
        if (totalDiffSize > 30000 || changes.length > 8 || totalChanges > 15) {
            // eslint-disable-next-line no-console
            console.log(
                `Skipping LLM grouping due to size: ${totalDiffSize} bytes, ${changes.length} files, ${totalChanges} changes`
            );
            return changes;
        }

        // Create a simplified version for the LLM prompt
        // Ensure all changes have stable IDs
        const changesWithStableIds = changes.map((change) => {
            return {
                path: change.path,
                changes: change.changes.map((c) => {
                    // If the change already has an ID, use it
                    if (c.id) {
                        return c;
                    }

                    // Otherwise, generate a stable ID
                    const pathId = generateStableId(c.path);
                    const contentHash = generateStableId(c.originalCode + c.modifiedCode);
                    return {
                        ...c,
                        id: `${pathId}-${contentHash}`,
                    };
                }),
            };
        });

        // Create a simplified version for the LLM prompt
        const succinctChanges = changesWithStableIds.map((change) => {
            return {
                path: change.path,
                changes: change.changes.map((c) => ({
                    id: c.id,
                    path: c.path,
                    diff: c.diff,
                })),
            };
        });

        const prompt = `You are a helpful software engineer. You are given a diff of a set of files. Your job is to group related changes together.

You should respond with just JSON in this format:
[{
    "path": string; // The file path
    "changes": [{ // The changes for this file
        "id": string; // The ID of the change
        "path": string; // The file path
        "diff": string; // The diff content
    }];
}]

IMPORTANT: Do NOT add any descriptions or explanations. Just group the changes by file. Keep the original IDs intact.

Here's an example set of changes:

[{
    "path": "src/global.css",
    "changes": [{
        "id": "abc123",
        "path": "src/global.css",
        "diff": "body {\n  font-family: sans-serif;\n- background-color: #fff;\n+ background-color: #000;\n}"
    }]
}]

You would respond with the same structure, just grouped by file:

[{
    "path": "src/global.css",
    "changes": [{
        "id": "abc123",
        "path": "src/global.css",
        "diff": "body {\n  font-family: sans-serif;\n- background-color: #fff;\n+ background-color: #000;\n}"
    }]
}]

The changes are:

${JSON.stringify(succinctChanges, null, 2)}

Start your response with [{`;

        // Add a timeout to the LLM call
        const LLM_TIMEOUT_MS = 20000; // 20 seconds
        let responseString: string;
        try {
            // Create a promise that rejects after the timeout
            const timeoutPromise = new Promise<string>((_, reject) => {
                setTimeout(() => reject(new Error("LLM request timed out")), LLM_TIMEOUT_MS);
            });

            // Race the LLM call against the timeout
            responseString = await Promise.race([getResponseFromLLM(prompt), timeoutPromise]);
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("LLM request timed out or failed:", error);
            // Fall back to simple grouping
            return changes;
        }

        // Parse the response
        let response: Array<{
            path: string;
            changes: Array<{ id: string; path: string; diff: string }>;
        }>;
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            response = JSON.parse(responseString) as Array<{
                path: string;
                changes: Array<{ id: string; path: string; diff: string }>;
            }>;
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error("Failed to parse LLM response:", error);
            // Fall back to simple grouping
            return changes;
        }

        // Map the response back to include the full changes with original/modified code
        return response.map((group) => {
            const mappedChanges = group.changes
                .map((change) => {
                    // Find the original change with this ID
                    for (const fileChange of changesWithStableIds) {
                        const originalChange = fileChange.changes.find((c) => c.id === change.id);
                        if (originalChange) {
                            return originalChange;
                        }
                    }
                    return undefined;
                })
                .filter((change): change is Diff => change !== undefined);

            return {
                path: group.path,
                changes: mappedChanges,
            };
        });
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error("Failed to group changes with LLM:", error);
        // Fallback to simple grouping by file with stable IDs
        return changedFiles.map((changedFile) => {
            // Use generateChangesForFile which now generates stable IDs
            const { oldPath, newPath, oldContents, newContents } =
                extractChangedFileProperties(changedFile);

            const fileChanges = generateChangesForFile(oldPath, newPath, oldContents, newContents);
            return {
                path: newPath || oldPath,
                changes: fileChanges,
            };
        });
    }
};
