/**
 * Common functions for vscode ranges.
 */
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { ILineRange, LineRange } from "./ranges";

const logger = getLogger("RangesVSCode");

export function toVSCodeRange(range: ILineRange, document?: vscode.TextDocument): vscode.Range {
    if (document && document.lineCount < range.stop) {
        logger.warn(
            `LineRange[${range.start}, ${range.stop}) is out of bounds for document ${document.uri.path}.`
        );
        return new vscode.Range(range.start, 0, document.lineCount, 0);
    }
    return new vscode.Range(range.start, 0, range.stop, 0);
}

export function toLineRange(range: vscode.Range): LineRange {
    return new LineRange(
        range.start.line,
        // If the (exclusive) range end includes any characters in the line, include the
        // whole line.
        range.end.line + (range.end.character > 0 ? 1 : 0)
    );
}

export function vscodeRangeToString(range: vscode.Range): string {
    return `[${vscodePositionToString(range.start)},${vscodePositionToString(range.end)})`;
}

export function vscodePositionToString(pos: vscode.Position): string {
    return `${pos.line}:${pos.character}`;
}

// While it's likely that these are sorted, the API does not specify that,
// so we sort them to be safe.
export function getSortedVisibleRanges(editor: vscode.TextEditor): vscode.Range[] {
    return [...editor.visibleRanges].sort((a, b) => a.start.line - b.start.line);
}
