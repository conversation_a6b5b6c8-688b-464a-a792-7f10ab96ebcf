import { ReplacementText } from "@augment-internal/sidecar-libs/src/chat/chat-types";

import { Chunk } from "../workspace/workspace-context";

function _makeReplacementText(
    blobName: string,
    path: string,
    charStart: number,
    charEnd: number,
    replacementText: string,
    presentInBlob: boolean,
    expectedBlobName: string,
    timestamp: Date
): ReplacementText {
    return {
        /* eslint-disable @typescript-eslint/naming-convention */
        blob_name: blobName,
        path: path,
        char_start: charStart,
        char_end: charEnd,
        replacement_text: replacementText,
        present_in_blob: presentInBlob,
        expected_blob_name: expectedBlobName,
        timestamp: timestamp.toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
    };
}

export function makeReplacementText(chunks: Chunk[]): ReplacementText[] {
    return chunks.map((chunk) =>
        _makeReplacementText(
            chunk.blobName,
            chunk.pathName,
            chunk.origStart,
            chunk.origStart + chunk.origLength,
            chunk.text,
            chunk.uploaded,
            chunk.expectedBlobName,
            chunk.timestamp
        )
    );
}
