import { <PERSON><PERSON><PERSON> } from "buffer";

/**
 * Converts base64 string to bytes in Node.js environment
 *
 * @param base64 - The base64 string to convert
 * @returns Promise resolving to a Uint8Array
 */
export function base64ToBytes(base64: string): Uint8Array {
    return new Uint8Array(Buffer.from(base64, "base64"));
}

/**
 * Converts bytes to base64 string in Node.js environment
 *
 * @param bytes - The bytes to convert
 * @returns Promise resolving to a base64 string
 */
export function bytesToBase64(bytes: Uint8Array): string {
    return Buffer.from(bytes).toString("base64");
}
