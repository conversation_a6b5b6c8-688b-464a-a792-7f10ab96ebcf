/**
 * @description - A collection of unique values order by most recent.
 *  - Keep unique items
 *  - Remove oldest items if list is full
 *  - If item already exists, it will move to head of the queue.
 *  - The collection is ordered by oldest first.
 *
 * This implementation is NOT object friendly - comparison is done by reference.
 * But for strings and primitives it should be fine.
 */
export class RecencyUniqueList<T> {
    private maxSize: number;
    private items: T[];

    constructor(maxSize: number) {
        this.maxSize = maxSize;
        this.items = [];
    }

    add(item: T) {
        const existingIndex = this.items.indexOf(item);
        if (existingIndex !== -1) {
            this.items.splice(existingIndex, 1); // Remove existing item
        } else if (this.items.length >= this.maxSize) {
            this.items.shift(); // Remove oldest item if queue is full
        }
        this.items.push(item); // Add item to the end
    }

    clear(): void {
        this.items = [];
    }

    size(): number {
        return this.items.length;
    }

    toArray(): T[] {
        return this.items.slice(); // Return a copy of the queue
    }
}
