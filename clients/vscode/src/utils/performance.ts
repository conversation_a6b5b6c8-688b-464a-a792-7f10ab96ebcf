function runActionCallback(start: number, action: ExecTimedAction) {
    return (value: any) => {
        const duration = performance.now() - start;
        action(Math.round(duration));
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return value;
    };
}

export function measureExecTime<T>(cb: (...args: any[]) => T, action: ExecTimedAction) {
    return (...args: any[]) => {
        const start = performance.now();
        const runCb = runActionCallback(start, action);
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        const result = cb(...args);
        if (result instanceof Promise) {
            return result.then(runCb);
        }
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return runCb(result);
    };
}

type ExecTimedAction = (duration: number) => void;
