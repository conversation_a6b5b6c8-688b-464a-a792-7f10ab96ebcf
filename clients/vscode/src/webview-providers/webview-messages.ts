import { type ChatStopReason } from "@augment-internal/sidecar-libs/src/api/types";
import type { ModelInfoRegistryEntry } from "@augment-internal/sidecar-libs/src/api/types";
import type {
    ChatMode,
    ChatRequestIdeState,
    ChatRequestNode,
    ChatResultNode,
    Exchange,
    MemoriesInfo,
    PersonaType,
    Rule,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { RemoteAgentSessionEventData } from "@augment-internal/sidecar-libs/src/metrics/types";
import {
    ChangedFile,
    DiffExplanation,
    GetRemoteAgentHistoryStreamResponse,
    GithubBranch,
    GithubRepo,
    IRemoteAgentDiffPanelOptions,
    RemoteAgentChatRequestDetails,
    RemoteAgentExchange,
    RemoteAgentStatus,
    RemoteAgentWorkspaceSetup,
    RemoteWorkspaceSetupStatus,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import type {
    ListRemoteAgentsStreamResponse,
    RemoteAgent,
} from "@augment-internal/sidecar-libs/src/remote-agent/remote-agent-types";
import { OAuthMetadata } from "@augment-internal/sidecar-libs/src/tools/oauth-types";
import type {
    TerminalSettings,
    ToolApprovalConfig,
    ToolDefinitionWithSettings,
    ToolIdentifier,
    ToolUseRequest,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { RequestIdData } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { type languages } from "monaco-editor";

import type { ChatFeedback } from "../chat/chat-types";
import {
    RulesAndGuidelinesState,
    UserGuidelinesState,
    WorkspaceGuidelinesState,
} from "../chat/guidelines-types";
import type { AugmentInstruction } from "../code-edit-types";
import type { CompletionFeedback } from "../completions/completion-types";
import type { AnyActionName } from "../main-panel/action-cards/types";
import type { ClientMetric } from "../metrics/types";
import { WebviewName } from "../metrics/types";
import type {
    IEditSuggestion,
    NextEditFeedback,
    NextEditResultInfo,
} from "../next-edit/next-edit-types";
import type { GitBranch } from "../remote-agent-manager/commit-ref-types";
import {
    LastUsedRemoteAgentSetup,
    SetupScript,
    SetupScriptLocation,
} from "../utils/remote-agent-setup/types";
import type { Diagnostic, FileType, KeyValuePair, SelectedCodeDetails } from "../utils/types";
import type { PreferenceInput, PreferenceResult } from "../webview-panels/preference-panel-types";
import {
    RemoteAgentNotificationSettingsContext,
    RemoteAgentPinnedStatusContext,
} from "../webview-panels/remote-agents/types";
import { ToolConfigInitData, ToolConfigSaveRequest } from "../webview-panels/settings-panel-types";
import type {
    ISourceFolderInfo,
    SyncingEnabledState,
    SyncingStatusEvent,
} from "../workspace/types";

/**
 * Metrics data collected during remote agent creation flow
 */
export interface RemoteAgentCreationMetrics {
    changedRepo: boolean;
    changedBranch: boolean;
}

/**
 * Enumerates the types* of messages that can be sent between the webviews and the extension.
 *
 * The standard format for messages between the webviews and the extension is:
 * {
 *     type: WebViewMessageType,
 *     data: any,
 * }
 *
 * *NOTE: This is NOT all of the webview messages sent by webviews to the
 * extension. In an effort to consolidate code handling we now aim to define all
 * webview message types which are client-agnostic (i.e.: purely handled by
 * sidecar code) as subclasses of `clients/sidecar/libs/src/webview-messages/common-webview-messages.ts`.
 *
 * If you are adding a new message type and it can be client-agnostic, you
 * should define it in `clients/sidecar/libs/src/webview-messages/common-webview-messages.ts`
 * instead, as this will allow client-agnostic handling (see comments in
 * common-webview-messages.ts for more details.)
 */
export enum WebViewMessageType {
    asyncWrapper = "async-wrapper",

    historyLoaded = "history-loaded",
    historyInitialize = "history-initialize",
    completionRating = "completion-rating",
    completionRatingDone = "completion-rating-done",
    nextEditRating = "next-edit-rating",
    nextEditRatingDone = "next-edit-rating-done",
    completions = "completions",
    historyConfig = "history-config",
    copyRequestID = "copy-request-id-to-clipboard",
    openFile = "open-file",
    openDiffInBuffer = "open-diff-in-buffer",
    saveFile = "save-file",
    loadFile = "load-file",

    importFileRequest = "import-file-request",
    importDirectoryRequest = "import-directory-request",
    triggerImportDialogRequest = "trigger-import-dialog-request",
    triggerImportDialogResponse = "trigger-import-dialog-response",
    openMemoriesFile = "open-memories-file",
    openAndEditFile = "open-and-edit-file",

    diffViewNotifyReinit = "diff-view-notify-reinit",
    diffViewLoaded = "diff-view-loaded",
    diffViewInitialize = "diff-view-initialize",
    diffViewResolveChunk = "diff-view-resolve-chunk",
    diffViewFetchPendingStream = "diff-view-fetch-pending-stream",
    diffViewDiffStreamStarted = "diff-view-diff-stream-started",
    diffViewDiffStreamChunk = "diff-view-diff-stream-chunk",
    diffViewDiffStreamEnded = "diff-view-diff-stream-ended",
    diffViewAcceptAllChunks = "diff-view-accept-all-chunks",
    diffViewAcceptFocusedChunk = "diff-view-accept-selected-chunk",
    diffViewRejectFocusedChunk = "diff-view-reject-focused-chunk",
    diffViewFocusPrevChunk = "diff-view-focus-prev-chunk",
    diffViewFocusNextChunk = "diff-view-focus-next-chunk",
    diffViewWindowFocusChange = "diff-view-window-focus-change",
    diffViewFileFocus = "diff-view-file-focus",
    disposeDiffView = "dispose-diff-view",

    reportWebviewClientMetric = "report-webview-client-metric",
    trackAnalyticsEvent = "track-analytics-event",
    reportError = "report-error",

    showNotification = "show-notification",
    showBannerNotification = "show-banner-notification",
    dismissBannerNotification = "dismiss-banner-notification",
    openConfirmationModal = "open-confirmation-modal",
    confirmationModalResponse = "confirmation-modal-response",

    clientTools = "client-tools",
    currentlyOpenFiles = "currently-open-files",
    findFileRequest = "find-file-request",
    resolveFileRequest = "resolve-file-request",
    findFileResponse = "find-file-response",
    resolveFileResponse = "resolve-file-response",
    findRecentlyOpenedFilesRequest = "find-recently-opened-files",
    findRecentlyOpenedFilesResponse = "find-recently-opened-files-response",
    findFolderRequest = "find-folder-request",
    findFolderResponse = "find-folder-response",
    findExternalSourcesRequest = "find-external-sources-request",
    findExternalSourcesResponse = "find-external-sources-response",
    findSymbolRequest = "find-symbol-request",
    findSymbolRegexRequest = "find-symbol-regex-request",
    findSymbolResponse = "find-symbol-response",
    fileRangesSelected = "file-ranges-selected",
    getDiagnosticsRequest = "get-diagnostics-request",
    getDiagnosticsResponse = "get-diagnostics-response",
    resolveWorkspaceFileChunkRequest = "resolve-workspace-file-chunk",
    resolveWorkspaceFileChunkResponse = "resolve-workspace-file-chunk-response",
    sourceFoldersUpdated = "source-folders-updated",
    sourceFoldersSyncStatus = "source-folders-sync-status",
    syncEnabledState = "sync-enabled-state",
    shouldShowSummary = "should-show-summary",
    showAugmentPanel = "show-augment-panel",
    updateGuidelinesState = "update-guidelines-state",
    openGuidelines = "open-guidelines",
    updateWorkspaceGuidelines = "update-workspace-guidelines",
    updateUserGuidelines = "update-user-guidelines",

    chatAgentEditListHasUpdates = "chat-agent-edit-list-has-updates",
    chatMemoryHasUpdates = "chat-memory-has-updates",
    getAgentEditContentsByRequestId = "getAgentEditContentsByRequestId",
    chatModeChanged = "chat-mode-changed",
    chatClearMetadata = "chat-clear-metadata",
    chatLoaded = "chat-loaded",

    chatInitialize = "chat-initialize",
    chatGetStreamRequest = "chat-get-stream-request",
    chatUserMessage = "chat-user-message",
    generateCommitMessage = "generate-commit-message",
    chatUserCancel = "chat-user-cancel",
    chatModelReply = "chat-model-reply",
    chatInstructionMessage = "chat-instruction-message",
    chatInstructionModelReply = "chat-instruction-model-reply",
    chatCreateFile = "chat-create-file",
    chatSmartPaste = "chat-smart-paste",
    chatRating = "chat-rating",
    chatRatingDone = "chat-rating-done",
    chatStreamDone = "chat-stream-done",
    runSlashCommand = "run-slash-command",
    callTool = "call-tool",
    callToolResponse = "call-tool-response",
    cancelToolRun = "cancel-tool-run",
    cancelToolRunResponse = "cancel-tool-run-response",
    toolCheckSafe = "check-safe",
    toolCheckSafeResponse = "check-safe-response",
    checkToolExists = "checkToolExists",
    checkToolExistsResponse = "checkToolExistsResponse",
    startRemoteMCPAuth = "start-remote-mcp-auth",
    getToolCallCheckpoint = "get-tool-call-checkpoint",
    getToolCallCheckpointResponse = "get-tool-call-checkpoint-response",
    updateAditionalChatModels = "update-additional-chat-models",
    saveChat = "save-chat",
    saveChatDone = "save-chat-done",
    newThread = "new-thread",
    chatSaveImageRequest = "chat-save-image-request",
    chatSaveImageResponse = "chat-save-image-response",
    chatLoadImageRequest = "chat-load-image-request",
    chatLoadImageResponse = "chat-load-image-response",
    chatDeleteImageRequest = "chat-delete-image-request",
    chatDeleteImageResponse = "chat-delete-image-response",
    chatSaveAttachmentRequest = "chat-save-attachment-request",
    chatSaveAttachmentResponse = "chat-save-attachment-response",

    instructions = "instructions",

    nextEditDismiss = "next-edit-dismiss",
    nextEditLoaded = "next-edit-loaded",
    nextEditSuggestions = "next-edit-suggestions",
    nextEditSuggestionsAction = "next-edit-suggestions-action",
    nextEditRefreshStarted = "next-edit-refresh-started",
    nextEditRefreshFinished = "next-edit-refresh-finished",
    nextEditCancel = "next-edit-cancel",
    nextEditPreviewActive = "next-edit-preview-active",
    nextEditSuggestionsChanged = "next-edit-suggestions-changed",
    nextEditNextSuggestionChanged = "next-edit-next-suggestion-changed",
    nextEditOpenSuggestion = "next-edit-open-suggestion",
    nextEditToggleSuggestionTree = "next-edit-toggle-suggestion-tree",
    nextEditActiveSuggestionChanged = "next-edit-active-suggestion",
    nextEditPanelFocus = "next-edit-panel-focus",

    onboardingLoaded = "onboarding-loaded",
    onboardingUpdateState = "onboarding-update-state",
    usedChat = "used-chat",

    preferencePanelLoaded = "preference-panel-loaded",
    preferenceInit = "preference-init",
    preferenceResultMessage = "preference-result-message",
    preferenceNotify = "preference-notify",

    openSettingsPage = "open-settings-page",
    settingsPanelLoaded = "settings-panel-loaded",
    navigateToSettingsSection = "navigate-to-settings-section",

    mainPanelDisplayApp = "main-panel-display-app",
    mainPanelLoaded = "main-panel-loaded",
    mainPanelActions = "main-panel-actions",
    mainPanelPerformAction = "main-panel-perform-action",
    mainPanelCreateProject = "main-panel-create-project",
    usedSlashAction = "used-slash-action",

    signInLoaded = "sign-in-loaded",
    signInLoadedResponse = "sign-in-loaded-response",
    signOut = "sign-out",
    awaitingSyncingPermissionLoaded = "awaiting-syncing-permission-loaded",
    awaitingSyncingPermissionInitialize = "awaiting-syncing-permission-initialize",

    readFileRequest = "read-file-request",
    readFileResponse = "read-file-response",

    wsContextGetChildrenRequest = "ws-context-get-children-request",
    wsContextGetChildrenResponse = "ws-context-get-children-response",
    wsContextGetSourceFoldersRequest = "ws-context-get-source-folders-request",
    wsContextGetSourceFoldersResponse = "ws-context-get-source-folders-response",
    wsContextAddMoreSourceFolders = "ws-context-add-more-source-folders",
    wsContextRemoveSourceFolder = "ws-context-remove-source-folder",
    wsContextSourceFoldersChanged = "ws-context-source-folders-changed",
    wsContextFolderContentsChanged = "ws-context-folder-contents-changed",
    wsContextUserRequestedRefresh = "ws-context-user-requested-refresh",

    augmentLink = "augment-link",

    resetAgentOnboarding = "reset-agent-onboarding",
    empty = "empty",
    chatGetAgentOnboardingPromptRequest = "chat-get-agent-onboarding-prompt-request",
    chatGetAgentOnboardingPromptResponse = "chat-get-agent-onboarding-prompt-response",
    getWorkspaceInfoRequest = "get-workspace-info-request",
    getWorkspaceInfoResponse = "get-workspace-info-response",

    getRemoteAgentOverviewsRequest = "get-remote-agent-overviews-request",
    getRemoteAgentOverviewsResponse = "get-remote-agent-overviews-response",
    remoteAgentOverviewsStreamRequest = "remote-agent-overviews-stream-request",
    remoteAgentOverviewsStreamResponse = "remote-agent-overviews-stream-response",
    getRemoteAgentChatHistoryRequest = "get-remote-agent-chat-history-request",
    getRemoteAgentChatHistoryResponse = "get-remote-agent-chat-history-response",
    remoteAgentHistoryStreamRequest = "remote-agent-history-stream-request",
    remoteAgentHistoryStreamResponse = "remote-agent-history-stream-response",
    cancelRemoteAgentsStreamRequest = "cancel-remote-agents-stream-request",
    createRemoteAgentRequest = "create-remote-agent-request",
    createRemoteAgentResponse = "create-remote-agent-response",
    deleteRemoteAgentRequest = "delete-remote-agent-request",
    deleteRemoteAgentResponse = "delete-remote-agent-response",
    remoteAgentChatRequest = "remote-agent-chat-request",
    remoteAgentChatResponse = "remote-agent-chat-response",
    remoteAgentInterruptRequest = "remote-agent-interrupt-request",
    remoteAgentInterruptResponse = "remote-agent-interrupt-response",
    listSetupScriptsRequest = "list-setup-scripts-request",
    listSetupScriptsResponse = "list-setup-scripts-response",
    saveSetupScriptRequest = "save-setup-script-request",
    saveSetupScriptResponse = "save-setup-script-response",
    deleteSetupScriptRequest = "delete-setup-script-request",
    deleteSetupScriptResponse = "delete-setup-script-response",
    renameSetupScriptRequest = "rename-setup-script-request",
    renameSetupScriptResponse = "rename-setup-script-response",
    remoteAgentSshRequest = "remote-agent-ssh-request",
    remoteAgentSshResponse = "remote-agent-ssh-response",
    setRemoteAgentNotificationEnabled = "set-remote-agent-notification-enabled",
    getRemoteAgentNotificationEnabledRequest = "get-remote-agent-notification-enabled-request",
    getRemoteAgentNotificationEnabledResponse = "get-remote-agent-notification-enabled-response",
    deleteRemoteAgentNotificationEnabled = "delete-remote-agent-notification-enabled",
    setRemoteAgentPinnedStatus = "set-remote-agent-pinned-status",
    getRemoteAgentPinnedStatusRequest = "get-remote-agent-pinned-status-request",
    getRemoteAgentPinnedStatusResponse = "get-remote-agent-pinned-status-response",
    deleteRemoteAgentPinnedStatus = "delete-remote-agent-pinned-status",
    remoteAgentNotifyReady = "remote-agent-notify-ready",
    remoteAgentSelectAgentId = "remote-agent-select-agent-id",
    remoteAgentWorkspaceLogsRequest = "remote-agent-workspace-logs-request",
    remoteAgentWorkspaceLogsResponse = "remote-agent-workspace-logs-response",
    remoteAgentPauseRequest = "remote-agent-pause-request",
    remoteAgentResumeRequest = "remote-agent-resume-request",
    remoteAgentResumeHintRequest = "remote-agent-resume-hint-request",
    updateRemoteAgentRequest = "update-remote-agent-request",
    updateRemoteAgentResponse = "update-remote-agent-response",

    // Shared webview store
    updateSharedWebviewState = "update-shared-webview-state",
    getSharedWebviewState = "get-shared-webview-state",
    getSharedWebviewStateResponse = "get-shared-webview-state-response",

    // Git reference messages for remote agent
    getGitBranchesRequest = "get-git-branches-request",
    getGitBranchesResponse = "get-git-branches-response",
    gitFetchRequest = "git-fetch-request",
    gitFetchResponse = "git-fetch-response",
    isGitRepositoryRequest = "is-git-repository-request",
    isGitRepositoryResponse = "is-git-repository-response",
    getWorkspaceDiffRequest = "get-workspace-diff-request",
    getWorkspaceDiffResponse = "get-workspace-diff-response",
    getRemoteUrlRequest = "get-remote-url-request",
    getRemoteUrlResponse = "get-remote-url-response",
    diffExplanationRequest = "get-diff-explanation-request",
    diffExplanationResponse = "get-diff-explanation-response",
    diffGroupChangesRequest = "get-diff-group-changes-request",
    diffGroupChangesResponse = "get-diff-group-changes-response",
    diffDescriptionsRequest = "get-diff-descriptions-request",
    diffDescriptionsResponse = "get-diff-descriptions-response",
    canApplyChangesRequest = "can-apply-changes-request",
    canApplyChangesResponse = "can-apply-changes-response",
    applyChangesRequest = "apply-changes-request",
    applyChangesResponse = "apply-changes-response",
    previewApplyChangesRequest = "preview-apply-changes-request",
    previewApplyChangesResponse = "preview-apply-changes-response",
    openFileRequest = "open-file-request",
    openFileResponse = "open-file-response",
    stashUnstagedChangesRequest = "stash-unstaged-changes-request",
    stashUnstagedChangesResponse = "stash-unstaged-changes-response",
    isGithubAuthenticatedRequest = "is-github-authenticated-request",
    isGithubAuthenticatedResponse = "is-github-authenticated-response",
    authenticateGithubRequest = "authenticate-github-request",
    authenticateGithubResponse = "authenticate-github-response",
    revokeGithubAccessRequest = "revoke-github-access-request",
    revokeGithubAccessResponse = "revoke-github-access-response",
    listGithubReposForAuthenticatedUserRequest = "list-github-repos-for-authenticated-user-request",
    listGithubReposForAuthenticatedUserResponse = "list-github-repos-for-authenticated-user-response",
    listGithubRepoBranchesRequest = "list-github-repo-branches-request",
    listGithubRepoBranchesResponse = "list-github-repo-branches-response",
    getGithubRepoRequest = "get-github-repo-request",
    getGithubRepoResponse = "get-github-repo-response",
    getCurrentLocalBranchRequest = "get-current-local-branch-request",
    getCurrentLocalBranchResponse = "get-current-local-branch-response",

    // Diff messages for remote agent changes
    remoteAgentDiffPanelLoaded = "remote-agent-diff-panel-loaded",
    remoteAgentDiffPanelSetOpts = "remote-agent-diff-panel-set-opts",
    showRemoteAgentDiffPanel = "show-remote-agent-diff-panel",
    closeRemoteAgentDiffPanel = "close-remote-agent-diff-panel",

    // Remote agent home panel
    remoteAgentHomePanelLoaded = "remote-agent-home-panel-loaded",
    showRemoteAgentHomePanel = "show-remote-agent-home-panel",
    closeRemoteAgentHomePanel = "close-remote-agent-home-panel",

    // Orientation messages
    triggerInitialOrientation = "trigger-initial-orientation",
    executeInitialOrientation = "execute-initial-orientation",
    orientationStatusUpdate = "orientation-status-update",
    getOrientationStatus = "get-orientation-status",

    // Agent auto mode approval
    checkAgentAutoModeApproval = "check-agent-auto-mode-approval",
    checkAgentAutoModeApprovalResponse = "check-agent-auto-mode-approval-response",
    setAgentAutoModeApproved = "set-agent-auto-mode-approved",

    toolConfigLoaded = "tool-config-loaded",
    toolConfigInitialize = "tool-config-initialize",
    toolConfigSave = "tool-config-save",
    toolConfigGetDefinitions = "tool-config-get-definitions",
    toolConfigDefinitionsResponse = "tool-config-definitions-response",
    toolConfigStartOAuth = "tool-config-start-oauth",
    toolConfigStartOAuthResponse = "tool-config-start-oauth-response",
    toolConfigRevokeAccess = "tool-config-revoke-access",
    toolApprovalConfigSetRequest = "tool-approval-config-set-request",
    toolApprovalConfigGetRequest = "tool-approval-config-get-request",
    toolApprovalConfigGetResponse = "tool-approval-config-get-response",

    // Storage operations for MCP servers
    getStoredMCPServers = "get-stored-mcp-servers",
    setStoredMCPServers = "set-stored-mcp-servers",
    getStoredMCPServersResponse = "get-stored-mcp-servers-response",
    deleteOAuthSession = "delete-oauth-session",

    // Chat request node information.
    getChatRequestIdeStateRequest = "get-ide-state-node-request",
    getChatRequestIdeStateResponse = "get-ide-state-node-response",

    // Execute VSCode command
    executeCommand = "execute-command",

    // Toggle collapse unchanged regions in diff editor
    toggleCollapseUnchangedRegions = "toggle-collapse-unchanged-regions",

    // Open a scratch file with content
    openScratchFileRequest = "open-scratch-file-request",

    // Terminal settings
    getTerminalSettings = "get-terminal-settings",
    terminalSettingsResponse = "terminal-settings-response",
    updateTerminalSettings = "update-terminal-settings",
    canShowTerminal = "can-show-terminal",
    canShowTerminalResponse = "can-show-terminal-response",
    showTerminal = "show-terminal",
    showTerminalResponse = "show-terminal-response",

    // Remote agent status
    getRemoteAgentStatus = "get-remote-agent-status",
    remoteAgentStatusResponse = "remote-agent-status-response",
    remoteAgentStatusChanged = "remote-agent-status-changed",

    // Last remote agent setup preferences
    saveLastRemoteAgentSetupRequest = "save-last-remote-agent-setup-request",
    getLastRemoteAgentSetupRequest = "get-last-remote-agent-setup-request",
    getLastRemoteAgentSetupResponse = "get-last-remote-agent-setup-response",

    // Rules custom editor
    rulesLoaded = "rules-loaded",
    // Memories custom editor
    memoriesLoaded = "memories-loaded",

    // Rules settings
    getRulesListResponse = "get-rules-list-response",

    // Subscription info
    getSubscriptionInfo = "get-subscription-info",
    getSubscriptionInfoResponse = "get-subscription-info-response",

    // Remote agent metrics
    reportRemoteAgentEvent = "report-remote-agent-event",
    reportAgentChangesApplied = "report-agent-changes-applied",

    // Set SSH Permission
    setPermissionToWriteToSSHConfig = "set-permission-to-write-to-ssh-config",
    getShouldShowSSHConfigPermissionPromptRequest = "get-should-show-ssh-config-permission-prompt-request",
    getShouldShowSSHConfigPermissionPromptResponse = "get-should-show-ssh-config-permission-prompt-response",
}

interface BasicWebViewMessage {
    type: WebViewMessageType;
}

interface DataWebViewMessage<T> extends BasicWebViewMessage {
    data: T;
}

export interface DataWebviewMessageWithId<T> extends BasicWebViewMessage {
    id: string;
    data: T;
}

export interface AsyncStreamCtx {
    isStreamComplete?: boolean;
    streamMsgIdx: number;
    streamNextRequestId: string;
}

export interface AsyncWebViewMessage<T> extends BasicWebViewMessage {
    type: WebViewMessageType.asyncWrapper;
    requestId: string;
    error: string | null;
    baseMsg: T | null;
    destination?: "sidecar" | "host";

    // Only populated for streams
    streamCtx?: AsyncStreamCtx | undefined;
}

interface HistoryLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.historyLoaded;
}

interface HistoryInitializeMessage extends DataWebViewMessage<HistoryInitializeMessageData> {
    type: WebViewMessageType.historyInitialize;
}

interface HistoryInitializeMessageData {
    completionRequests: HistoryCompletionRequest[];
    instructions: AugmentInstruction[];
    nextEdits: NextEditResultInfo[];
    config?: HistoryConfig;
}

export interface HistoryConfig {
    enableDebugFeatures: boolean;
    enableReviewerWorkflows: boolean;
}

// The HistoryCompletionRequest is a reduced version of the CompletionRequest
// type. This only includes the fields that are needed for the history webview
// and is a format that can be passed from the extension to the Webview.
export interface HistoryCompletionRequest {
    occuredAt: string; // This is not a Date object as it is not serializable.
    requestId: string;
    pathName: string;
    repoRoot: string;
    prefix: string;
    suffix: string;
    completions: HistoryAugmentCompletion[];
}

export interface HistoryAugmentCompletion {
    text: string;
    skippedSuffix: string;
    suffixReplacementText: string;
}

interface CompletionRatingMessage extends DataWebViewMessage<CompletionFeedback> {
    type: WebViewMessageType.completionRating;
}

interface CompletionRatingDoneMessage extends DataWebViewMessage<CompletionRatingDoneMessageData> {
    type: WebViewMessageType.completionRatingDone;
}

interface CompletionRatingDoneMessageData {
    success: boolean;
    requestId: string;
}

interface NextEditRatingMessage extends DataWebViewMessage<NextEditFeedback> {
    type: WebViewMessageType.nextEditRating;
}
export interface NextEditCurorWithinSuggestion
    extends DataWebViewMessage<IEditSuggestion | undefined> {
    type: WebViewMessageType.nextEditActiveSuggestionChanged;
}
export interface NextEditPanelFocusMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditPanelFocus;
}

interface NextEditRatingDoneMessage extends DataWebViewMessage<NextEditRatingDoneMessageData> {
    type: WebViewMessageType.nextEditRatingDone;
}

interface NextEditToggleSuggestionTreeMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditToggleSuggestionTree;
}

export interface OpenScratchFileRequestMessage
    extends DataWebViewMessage<OpenScratchFileRequestData> {
    type: WebViewMessageType.openScratchFileRequest;
}

export interface OpenScratchFileRequestData {
    content: string;
    language?: string;
}

export interface OpenScratchFileResponseData {
    success: boolean;
    error?: string;
}

interface NextEditRatingDoneMessageData {
    success: boolean;
    requestId: string;
}

interface CompletionsMessage extends DataWebViewMessage<HistoryCompletionRequest[]> {
    type: WebViewMessageType.completions;
}

interface InstructionsMessage extends DataWebViewMessage<AugmentInstruction[]> {
    type: WebViewMessageType.instructions;
}

interface HistoryConfigMessage extends DataWebViewMessage<HistoryConfig> {
    type: WebViewMessageType.historyConfig;
}

interface CopyRequestIDToClipboardMessage extends DataWebViewMessage<string> {
    type: WebViewMessageType.copyRequestID;
}

export interface OpenFileMessageData extends FileDetails {
    allowOutOfWorkspace?: boolean;
}

export interface OpenFileMessage extends DataWebViewMessage<OpenFileMessageData> {
    type: WebViewMessageType.openFile;
}

export interface OpenDiffInBufferMessage extends DataWebViewMessage<OpenDiffInBufferMessageData> {
    type: WebViewMessageType.openDiffInBuffer;
}

export interface OpenDiffInBufferMessageData {
    oldContents: string;
    newContents: string;
    filePath: string;
}

export interface OpenMemoriesFileMessage extends BasicWebViewMessage {
    type: WebViewMessageType.openMemoriesFile;
}

export interface OpenAndEditFileMessage extends DataWebViewMessage<FileDetails> {
    type: WebViewMessageType.openAndEditFile;
}

export interface OpenConfirmationModalData {
    title: string;
    message: string;
    confirmButtonText: string;
    cancelButtonText: string;
}

export interface ShowNotificationData {
    message: string;
    type?: "info" | "error" | "warning";
    openFileMessage?: OpenFileMessageData;
}

export interface ShowNotificationMessage extends DataWebViewMessage<ShowNotificationData> {
    type: WebViewMessageType.showNotification;
}

export interface BannerNotificationActionItem {
    title: string;
    url?: string;
}

export interface ShowBannerNotificationData {
    notificationId: string;
    message: string;
    level: "info" | "warning" | "error";
    actionItems?: BannerNotificationActionItem[];
}

export interface ShowBannerNotificationMessage
    extends DataWebViewMessage<ShowBannerNotificationData> {
    type: WebViewMessageType.showBannerNotification;
}

export interface DismissBannerNotificationData {
    notificationId: string;
    actionItemTitle?: string;
}

export interface DismissBannerNotificationMessage
    extends DataWebViewMessage<DismissBannerNotificationData> {
    type: WebViewMessageType.dismissBannerNotification;
}

export interface OpenConfirmationModal extends DataWebViewMessage<OpenConfirmationModalData> {
    type: WebViewMessageType.openConfirmationModal;
}

export interface ConfirmationModalResponseData {
    ok: boolean;
    error?: string;
}

export interface ConfirmationModalResponse
    extends DataWebViewMessage<ConfirmationModalResponseData> {
    type: WebViewMessageType.confirmationModalResponse;
}

export interface CheckAgentAutoModeApproval extends BasicWebViewMessage {
    type: WebViewMessageType.checkAgentAutoModeApproval;
}

export interface CheckAgentAutoModeApprovalResponse extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.checkAgentAutoModeApprovalResponse;
}

export interface SetAgentAutoModeApproved extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.setAgentAutoModeApproved;
}

export interface ChatLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.chatLoaded;
}

export interface ChatClearMetadataData {
    requestIds?: string[];
    conversationIds?: string[];
    toolUseIds?: string[];
}

export interface ChatClearMetadata extends DataWebViewMessage<ChatClearMetadataData> {
    type: WebViewMessageType.chatClearMetadata;
}

export enum SmartPastePrecomputeMode {
    off = "off",
    visibleHover = "visible-hover",
    visible = "visible",
    on = "on",
}

export type UserTier = "unknown" | "community" | "professional" | "enterprise";

export interface ChatInitializeMessageData {
    // Flags
    enableEditableHistory?: boolean;
    enablePreferenceCollection?: boolean;
    enableRetrievalDataCollection?: boolean;
    enableDebugFeatures?: boolean;
    enableAgentSwarmMode?: boolean;
    useRichTextHistory?: boolean;
    modelDisplayNameToId?: { [key: string]: string | null };
    fullFeatured?: boolean;
    enableExternalSourcesInChat?: boolean;
    smallSyncThreshold?: number;
    bigSyncThreshold?: number;
    enableSmartPaste?: boolean;
    enableDirectApply?: boolean;
    summaryTitles?: boolean;
    suggestedEditsAvailable?: boolean;
    enableShareService?: boolean;
    maxTrackableFileCount?: number;
    enableDesignSystemRichTextEditor?: boolean;
    enableSources?: boolean;
    enableChatMermaidDiagrams?: boolean;
    smartPastePrecomputeMode?: SmartPastePrecomputeMode;
    useNewThreadsMenu?: boolean;
    enableChatMermaidDiagramsMinVersion?: boolean;
    idleNewSessionNotificationTimeoutMs?: number;
    idleNewSessionMessageTimeoutMs?: number;
    enableChatMultimodal?: boolean;
    memoriesTextEditorEnabled?: boolean;
    enableAgentMode?: boolean;
    enableAgentAutoMode?: boolean;
    enableRichCheckpointInfo?: boolean;
    truncateChatHistory?: boolean;
    enableBackgroundAgents?: boolean;
    enableNewThreadsList?: boolean;
    enableVirtualizedMessageList?: boolean;
    enablePersonalities?: boolean;
    enableRules?: boolean;
    memoryClassificationOnFirstToken?: boolean;
    enableGenerateCommitMessage?: boolean;
    enablePromptEnhancer?: boolean;
    modelRegistry?: Record<string, string>;
    modelInfoRegistry?: Record<string, ModelInfoRegistryEntry>;
    agentChatModel?: string;
    enableModelRegistry?: boolean;
    enableTaskList?: boolean;
    clientAnnouncement?: string;
    useHistorySummary?: boolean;
    historySummaryParams?: string;
    enableAgentTabs?: boolean;
    enableGroupedTools?: boolean;
    remoteAgentsResumeHintAvailableTtlDays?: number;
    historySummaryMaxChars?: number;
    historySummaryLowerChars?: number;
    historySummaryPrompt?: string;
    enableParallelTools?: boolean;
    enableAgentGitTracker?: boolean;
    enableNativeRemoteMcp?: boolean;

    // Data
    agentMemoriesFilePathName?: IQualifiedPathName | undefined;
    workspaceUnpopulated?: boolean;
    customPersonalityPrompts?: {
        agent?: string;
        prototyper?: string;
        brainstorm?: string;
        reviewer?: string;
    };

    userTier?: UserTier;
    conversationHistorySizeThresholdBytes?: number;
    enableExchangeStorage?: boolean;
    enableToolUseStateStorage?: boolean;
    retryChatStreamTimeouts?: boolean;
    enableCommitIndexing?: boolean;
    enableMemoryRetrieval?: boolean;
    isVscodeVersionOutdated?: boolean;
    vscodeMinVersion?: string;
    memoriesParams?: { [key: string]: string | number | boolean };
}

export interface ChatInitialize extends DataWebViewMessage<ChatInitializeMessageData> {
    type: WebViewMessageType.chatInitialize;
}

export interface ChatRequestId {
    requestId: string;
    lastChunkId?: number;
}

export interface ChatGetStreamRequest extends DataWebViewMessage<ChatRequestId> {
    type: WebViewMessageType.chatGetStreamRequest;
}

export interface ChatUserMessage extends DataWebViewMessage<ChatUserMessageData> {
    type: WebViewMessageType.chatUserMessage;
}

export interface GenerateCommitMessage extends BasicWebViewMessage {
    type: WebViewMessageType.generateCommitMessage;
}

export interface ChatUserCancel extends DataWebViewMessage<ChatRequestId> {
    type: WebViewMessageType.chatUserCancel;
}

export interface ChatAgentEditListHasUpdatesMessage extends BasicWebViewMessage {
    type: WebViewMessageType.chatAgentEditListHasUpdates;
}

export interface ChatMemoryHasUpdatesMessage extends BasicWebViewMessage {
    type: WebViewMessageType.chatMemoryHasUpdates;
}

export interface ChatAgentFileChangeSummary {
    totalAddedLines: number;
    totalRemovedLines: number;

    /** Diff statistics for working directory → staging (unstaged changes) */
    unstagedChanges?: {
        addedLines: number;
        removedLines: number;
    };

    /** Diff statistics for staging → HEAD (staged changes) */
    stagedChanges?: {
        addedLines: number;
        removedLines: number;
    };
}

export interface DiffExplanationRequestData {
    apikey?: string;
    changedFiles: ChangedFile[] | any[]; // Allow for lightweight changes with IDs
    changesById?: boolean; // Flag to indicate if we're using ID-based approach
}

export interface DiffExplanationResponse extends DataWebViewMessage<DiffExplanationResponseData> {
    type: WebViewMessageType.diffExplanationResponse;
}

export interface DiffExplanationResponseData {
    explanation: DiffExplanation;
    error?: string;
}

export interface DiffGroupChangesRequestMessage
    extends DataWebViewMessage<DiffExplanationRequestData> {
    type: WebViewMessageType.diffGroupChangesRequest;
}

export interface DiffGroupChangesResponseData {
    groupedChanges: {
        path: string;
        changes: {
            id: string;
            path: string;
            diff: string;
            originalCode: string;
            modifiedCode: string;
        }[];
    }[];
}

export interface DiffGroupChangesResponseMessage
    extends DataWebViewMessage<DiffGroupChangesResponseData> {
    type: WebViewMessageType.diffGroupChangesResponse;
}

export interface DiffDescriptionsRequestMessage
    extends DataWebViewMessage<{
        groupedChanges: DiffGroupChangesResponseData["groupedChanges"];
        apikey?: string;
    }> {
    type: WebViewMessageType.diffDescriptionsRequest;
}

export interface DiffDescriptionsResponseMessage
    extends DataWebViewMessage<DiffExplanationResponseData> {
    type: WebViewMessageType.diffDescriptionsResponse;
}

export interface GetChatRequestIdeStateRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getChatRequestIdeStateRequest;
}

export interface GetChatRequestIdeStateResponse extends DataWebViewMessage<ChatRequestIdeState> {
    type: WebViewMessageType.getChatRequestIdeStateResponse;
}

export interface GetWorkspaceInfoRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getWorkspaceInfoRequest;
}

export interface GetWorkspaceInfoResponse
    extends DataWebViewMessage<{ trackedFileCount?: number[] }> {
    type: WebViewMessageType.getWorkspaceInfoResponse;
}

export interface IChatActiveContext {
    userSpecifiedFiles: IQualifiedPathName[];
    recentFiles?: IQualifiedPathName[];
    ruleFiles?: Rule[];
    externalSources?: ExternalSource[];
    selections?: FileDetails[];
    sourceFolders?: IQualifiedPathName[];
}

export interface ChatUserMessageData {
    text: string;
    chatHistory: Exchange[];
    // This marks if the message is silent
    // Silent messages are not part of the chat history
    silent: boolean;
    modelId?: string | undefined;
    context?: IChatActiveContext;
    disableRetrieval?: boolean;
    disableSelectedCodeDetails?: boolean;
    conversationId?: string;
    requestIdOverride?: string;
    // Here for backwards compatibility
    userSpecifiedFiles?: IQualifiedPathName[];
    externalSourceIds?: string[];
    nodes?: ChatRequestNode[];
    memoriesInfo?: MemoriesInfo;
    personaType?: PersonaType;
    createdTimestamp?: number;
    rules?: Rule[];
}

export interface ChatModelReply extends DataWebViewMessage<ChatModelReplyData> {
    type: WebViewMessageType.chatModelReply;
}

export interface ChatInstructionModelReply
    extends DataWebViewMessage<ChatInstructionModelReplyData> {
    type: WebViewMessageType.chatInstructionModelReply;
}

export interface WorkspaceFileChunk {
    charStart: number;
    charEnd: number;
    blobName: string;
    file?: FileDetails;
}

interface ChatModelReplyData {
    text: string;
    requestId: string;
    workspaceFileChunks: WorkspaceFileChunk[];
    streaming?: boolean;
    nodes?: ChatResultNode[];
    // eslint-disable-next-line @typescript-eslint/naming-convention
    stop_reason?: ChatStopReason;
    chunkId?: number;
    error?: {
        displayErrorMessage: string;
        isRetriable?: boolean;
        shouldBackoff?: boolean;
    };
}

interface ChatInstructionModelReplyData {
    text: string;
    requestId: string;
    replacementText: string | null;
    replacementStartLine: number | null;
    replacementEndLine: number | null;
}

export interface ChatInstructionMessage extends DataWebViewMessage<ChatInstructionData> {
    type: WebViewMessageType.chatInstructionMessage;
}

export interface ChatInstructionData {
    instruction: string;
    selectedCodeDetails: SelectedCodeDetails;
}

export interface ChatCreateFileData {
    code: string;
    relPath?: string;
}

interface ChatCreateFile extends DataWebViewMessage<ChatCreateFileData> {
    type: WebViewMessageType.chatCreateFile;
}

export interface ChatSmartPasteData {
    generatedCode: string;
    chatHistory: Exchange[];
    targetFile?: string;
    options?: {
        dryRun?: boolean;
        requireFileConfirmation?: boolean;
        instantApply?: boolean;
    };
}

interface SaveChatConversationData {
    conversationId: string;
    chatHistory: Exchange[];
    title: string;
}

interface SaveChatResult {
    uuid: string;
    url: string;
}

export interface ChatSmartPaste extends DataWebViewMessage<ChatSmartPasteData> {
    type: WebViewMessageType.chatSmartPaste;
}

export interface ChatRatingMessage extends DataWebViewMessage<ChatFeedback> {
    type: WebViewMessageType.chatRating;
}

export interface ChatRatingDoneMessage extends DataWebViewMessage<ChatFeedback> {
    type: WebViewMessageType.chatRatingDone;
}

export interface RunSlashCommand extends DataWebViewMessage<string> {
    type: WebViewMessageType.runSlashCommand;
}

export interface CallToolMessage extends DataWebViewMessage<ToolUseRequest> {
    type: WebViewMessageType.callTool;
}

export interface CallToolResponse extends DataWebViewMessage<ToolUseResponse> {
    type: WebViewMessageType.callToolResponse;
}

export interface CancelToolRunMessage
    extends DataWebViewMessage<{ requestId: string; toolUseId: string }> {
    type: WebViewMessageType.cancelToolRun;
}

export interface CancelToolRunResponse extends BasicWebViewMessage {
    type: WebViewMessageType.cancelToolRunResponse;
}

export interface CheckToolExistsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.checkToolExists;
    toolName: string;
}

export interface CheckToolExistsResponse extends BasicWebViewMessage {
    type: WebViewMessageType.checkToolExistsResponse;
    exists: boolean;
}

export interface GetToolCallCheckpoint extends DataWebViewMessage<RequestIdData> {
    type: WebViewMessageType.getToolCallCheckpoint;
}

export interface GetToolCallCheckpointResponse
    extends DataWebViewMessage<{ checkpointNumber: number | undefined }> {
    type: WebViewMessageType.getToolCallCheckpointResponse;
}

export interface NewThreadData {
    mode?: string;
}

export interface NewThread extends DataWebViewMessage<NewThreadData> {
    type: WebViewMessageType.newThread;
}

export type ChatWebViewMessage =
    | ChatLoaded
    | ChatClearMetadata
    | ChatInitialize
    | ChatGetStreamRequest
    | ChatUserMessage
    | GenerateCommitMessage
    | ChatUserCancel
    | ChatModelReply
    | ChatInstructionMessage
    | ChatInstructionModelReply
    | ChatCreateFile
    | ChatSmartPaste
    | ChatRatingMessage
    | ChatRatingDoneMessage
    | ChatSaveImageRequest
    | ChatSaveImageResponse
    | ChatLoadImageRequest
    | ChatLoadImageResponse
    | ChatDeleteImageRequest
    | ChatDeleteImageResponse
    | ChatSaveAttachmentRequest
    | ChatSaveAttachmentResponse
    | RunSlashCommand
    | CallToolMessage
    | CallToolResponse
    | CancelToolRunMessage
    | CancelToolRunResponse
    | CheckToolExistsRequest
    | CheckToolExistsResponse
    | NewThread
    | GetToolCallCheckpoint
    | GetToolCallCheckpointResponse
    | GetChatRequestIdeStateRequest
    | GetChatRequestIdeStateResponse;

export interface SaveChatMessage extends DataWebViewMessage<SaveChatConversationData> {
    type: WebViewMessageType.saveChat;
}

export interface SaveChatDoneMessage extends DataWebViewMessage<SaveChatResult> {
    type: WebViewMessageType.saveChatDone;
}

export interface FileData {
    filename: string;
    data: string; // Base64-encoded image data
}

export interface ChatSaveImageRequest extends DataWebViewMessage<FileData> {
    type: WebViewMessageType.chatSaveImageRequest;
}

export interface ChatSaveImageResponse extends DataWebViewMessage<string> {
    type: WebViewMessageType.chatSaveImageResponse;
}

export interface ChatLoadImageRequest extends DataWebViewMessage<string> {
    type: WebViewMessageType.chatLoadImageRequest;
}

export interface ChatLoadImageResponse extends DataWebViewMessage<string | undefined> {
    // base64-encoded image data
    type: WebViewMessageType.chatLoadImageResponse;
}

export interface ChatDeleteImageRequest extends DataWebViewMessage<string> {
    type: WebViewMessageType.chatDeleteImageRequest;
}

export interface ChatDeleteImageResponse extends BasicWebViewMessage {
    type: WebViewMessageType.chatDeleteImageResponse;
}

export interface AttachmentData {
    filename: string;
    data: string; // Base64-encoded file data
}
export interface ChatSaveAttachmentRequest extends DataWebViewMessage<AttachmentData> {
    type: WebViewMessageType.chatSaveAttachmentRequest;
}

export interface ChatSaveAttachmentResponse extends DataWebViewMessage<string> {
    type: WebViewMessageType.chatSaveAttachmentResponse;
}

export interface CurrentlyOpenFiles extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.currentlyOpenFiles;
}

export interface WebviewClientMetricData extends ClientMetric {
    webviewName: WebviewName;
}

export interface ReportWebviewClientMetricRequest
    extends DataWebViewMessage<WebviewClientMetricData> {
    type: WebViewMessageType.reportWebviewClientMetric;
}

export interface TrackAnalyticsEventData {
    eventName: string;
    properties?: Record<string, any>;
}

export interface TrackAnalyticsEventRequest extends DataWebViewMessage<TrackAnalyticsEventData> {
    type: WebViewMessageType.trackAnalyticsEvent;
}

export interface ReportErrorData {
    originalRequestId: string | null;
    sanitizedMessage: string;
    stackTrace: string;
    diagnostics: KeyValuePair[];
}

export interface ReportError extends DataWebViewMessage<ReportErrorData> {
    type: WebViewMessageType.reportError;
}

export interface SourceFoldersUpdatedData {
    sourceFolders: ISourceFolderInfo[];
}

export interface SourceFoldersUpdated extends DataWebViewMessage<SourceFoldersUpdatedData> {
    type: WebViewMessageType.sourceFoldersUpdated;
}

export interface SourceFoldersSyncStatus extends DataWebViewMessage<SyncingStatusEvent> {
    type: WebViewMessageType.sourceFoldersSyncStatus;
}

export interface SyncEnabledStateUpdate extends DataWebViewMessage<SyncingEnabledState> {
    type: WebViewMessageType.syncEnabledState;
}

export interface GuidelinesStates {
    userGuidelines?: UserGuidelinesState;
    workspaceGuidelines?: WorkspaceGuidelinesState[];
    rulesAndGuidelines?: RulesAndGuidelinesState;
}

export interface UpdateGuidelinesState extends DataWebViewMessage<GuidelinesStates> {
    type: WebViewMessageType.updateGuidelinesState;
}

export interface OpenGuidelines extends DataWebViewMessage<string> {
    type: WebViewMessageType.openGuidelines;
}
export interface UpdateWorkspaceGuidelines extends DataWebViewMessage<string> {
    type: WebViewMessageType.updateWorkspaceGuidelines;
}

export interface UpdateUserGuidelines extends DataWebViewMessage<string> {
    type: WebViewMessageType.updateUserGuidelines;
}

export interface FileRangesSelected extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.fileRangesSelected;
}

export interface ISearchScopeArgs {
    files: FileDetails[];
}

export interface FindFileRequestData extends IQualifiedPathName {
    exactMatch?: boolean;
    maxResults?: number;
    searchScope?: ISearchScopeArgs;
}

export interface FindFileRequest extends DataWebViewMessage<FindFileRequestData> {
    type: WebViewMessageType.findFileRequest;
}

export interface FindFileResponse extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.findFileResponse;
}

export interface ResolveFileRequest extends DataWebViewMessage<FindFileRequestData> {
    type: WebViewMessageType.resolveFileRequest;
}

export interface ResolveFileResponse extends DataWebViewMessage<FileDetails | undefined> {
    type: WebViewMessageType.resolveFileResponse;
}

export interface FindRecentlyOpenedFilesRequest extends DataWebViewMessage<FindFileRequestData> {
    type: WebViewMessageType.findRecentlyOpenedFilesRequest;
}

export interface FindRecentlyOpenedFilesResponse extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.findRecentlyOpenedFilesResponse;
}

export interface FindFolderRequestData extends IQualifiedPathName {
    exactMatch?: boolean;
    maxResults?: number;
}

export interface FindFolderRequest extends DataWebViewMessage<FindFolderRequestData> {
    type: WebViewMessageType.findFolderRequest;
}

export interface FindFolderResponse extends DataWebViewMessage<FileDetails[]> {
    type: WebViewMessageType.findFolderResponse;
}

export interface ExternalSource {
    id: string;
    name: string; // Short, human-readable name
    title: string; // Long, human-readable description
    // eslint-disable-next-line @typescript-eslint/naming-convention
    source_type: number;
}

export interface SearchExternalSourcesRequest {
    query: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    source_types: string[];
}

export interface SearchExternalSourcesResponse {
    sources: ExternalSource[];
}

export interface FindExternalSourcesRequest
    extends DataWebViewMessage<SearchExternalSourcesRequest> {
    type: WebViewMessageType.findExternalSourcesRequest;
}

export interface FindExternalSourcesResponse
    extends DataWebViewMessage<SearchExternalSourcesResponse> {
    type: WebViewMessageType.findExternalSourcesResponse;
}

export interface FindSymbolRequestData {
    query: string;
    searchScope: ISearchScopeArgs;
}

export interface FindSymbolRequest extends DataWebViewMessage<FindSymbolRequestData> {
    type: WebViewMessageType.findSymbolRequest;
}

export interface FindSymbolRegexRequest extends DataWebViewMessage<FindSymbolRequestData> {
    type: WebViewMessageType.findSymbolRegexRequest;
}

export interface FindSymbolResponseData extends languages.DocumentSymbol {
    file: FileDetails;
}

export interface FindSymbolResponse extends DataWebViewMessage<FindSymbolResponseData[]> {
    type: WebViewMessageType.findSymbolResponse;
}

export interface GetDiagnosticsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getDiagnosticsRequest;
}

export interface GetDiagnosticsResponse extends DataWebViewMessage<Diagnostic[]> {
    type: WebViewMessageType.getDiagnosticsResponse;
}

export interface ResolveWorkspaceFileChunkRequest extends DataWebViewMessage<WorkspaceFileChunk> {
    type: WebViewMessageType.resolveWorkspaceFileChunkRequest;
}

export interface ResolveWorkspaceFileChunkResponse extends DataWebViewMessage<FileDetails> {
    type: WebViewMessageType.resolveWorkspaceFileChunkResponse;
}

interface NextEditDismissMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditDismiss;
}

interface NextEditLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditLoaded;
}

interface NextEditSuggestionsMessage extends DataWebViewMessage<NextEditResultInfo> {
    type: WebViewMessageType.nextEditSuggestions;
}

interface NextEditRefreshStarted extends DataWebViewMessage<string> {
    type: WebViewMessageType.nextEditRefreshStarted;
}

interface NextEditRefreshFinished extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditRefreshFinished;
}

export interface NextEditSuggestionsActionMessage
    extends DataWebViewMessage<
        | { accept: IEditSuggestion }
        | { reject: IEditSuggestion }
        | { acceptAllInFile: IEditSuggestion[] }
        | { rejectAllInFile: IEditSuggestion[] }
        | { undoAllInFile: IEditSuggestion[] }
        | { undo: IEditSuggestion }
    > {
    type: WebViewMessageType.nextEditSuggestionsAction;
}

export interface NextEditCancelMessage extends BasicWebViewMessage {
    type: WebViewMessageType.nextEditCancel;
}

export interface NextEditSuggestionsChangedData {
    suggestions: IEditSuggestion[];
}

export interface NextEditPreviewActive extends DataWebViewMessage<IEditSuggestion> {
    type: WebViewMessageType.nextEditPreviewActive;
}

export interface NextEditSuggestionsChanged
    extends DataWebViewMessage<NextEditSuggestionsChangedData> {
    type: WebViewMessageType.nextEditSuggestionsChanged;
}

export interface NextEditFocusedSuggestionChanged
    extends DataWebViewMessage<IEditSuggestion | undefined> {
    type: WebViewMessageType.nextEditNextSuggestionChanged;
}

export interface NextEditOpenSuggestion extends DataWebViewMessage<IEditSuggestion> {
    type: WebViewMessageType.nextEditOpenSuggestion;
}
export interface ReadFileRequest extends DataWebViewMessage<{ pathName: IQualifiedPathName }> {
    type: WebViewMessageType.readFileRequest;
}

export interface ReadFileResponse extends DataWebViewMessage<IResolvedFileResult> {
    type: WebViewMessageType.readFileResponse;
}
export interface SaveFileMessageData extends FileDetails {
    content: string;
}
export interface SaveFileMessage extends DataWebViewMessage<SaveFileMessageData> {
    type: WebViewMessageType.saveFile;
}
export interface LoadFileMessageData extends FileDetails {
    content: string;
}
export interface LoadFileMessage extends DataWebViewMessage<LoadFileMessageData> {
    type: WebViewMessageType.loadFile;
}

export interface TriggerImportDialogRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.triggerImportDialogRequest;
}

export interface TriggerImportDialogResponseData {
    selectedPaths: string[];
}

export interface TriggerImportDialogResponseMessage
    extends DataWebViewMessage<TriggerImportDialogResponseData> {
    type: WebViewMessageType.triggerImportDialogResponse;
}

export interface IResolvedFileContent {
    pathName: IQualifiedPathName;
    content: string;
}
export interface IResolvedFileError {
    pathName: IQualifiedPathName;
    error: string;
}
export type IResolvedFileResult = IResolvedFileContent | IResolvedFileError;

export type NextEditWebViewMessage =
    | NextEditRefreshStarted
    | NextEditCancelMessage
    | ReadFileRequest
    | ReadFileResponse
    | NextEditCurorWithinSuggestion
    | NextEditPanelFocusMessage;

export type NextEditVSCodeToWebViewMessage =
    | NextEditToggleSuggestionTreeMessage
    | NextEditPanelFocusMessage;

export interface FileDetails {
    repoRoot: string;
    pathName: string;
    fileType?: FileType;
    uriScheme?: string;
    // The display range of the file. Expressed with 1-indexing for historical reasons.
    // Only contains the start and end lines, 1-indexed.
    range?: {
        start: number;
        stop: number;
    };
    // These are the full range of the file, expressed with 0-indexing.
    // The full range includes the start and end lines, as well as the
    // start and end character columns.
    fullRange?: {
        startLineNumber: number;
        startColumn: number;
        endLineNumber: number;
        endColumn: number;
    };
    originalCode?: string;
    modifiedCode?: string;
    lineChanges?: LineChanges;
    // Whether actions on this file should be triggered from a different tab.
    differentTab?: boolean;
    openTextDocument?: boolean;
    requestId?: string;
    suggestionId?: string;
    // The snippet to highlight in the file.
    snippet?: string;
    // Whether the pathName should be parsed with vscode-local: from a remote workspace
    // this will open the file on the local machine rather than the remote machine
    openLocalUri?: boolean;
}

export interface IInstructionInitializeData {
    selection: {
        start: {
            line: number;
            character: number;
        };
        end: {
            line: number;
            character: number;
        };
    };
}

export interface DiffViewInitializeData {
    file: FileDetails;
    keybindings?: { [key: string]: string };
    instruction?: IInstructionInitializeData;
    editable?: boolean;
    disableResolution?: boolean;
    disableApply?: boolean;
}

export interface DiffViewFetchPendingStream extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewFetchPendingStream;
}

export enum DiffViewResolveType {
    accept = "accept",
    reject = "reject",
}

export interface DiffViewResolveData {
    file: FileDetails;
    changes: FileDetails[];
    resolveType: DiffViewResolveType;
    shouldApplyToAll?: boolean; // User applied accept/reject to all chunks
}

export interface DiffViewResolveMessage extends DataWebViewMessage<DiffViewResolveData> {
    type: WebViewMessageType.diffViewResolveChunk;
}

export type DiffViewStreamMessage =
    | DiffViewDiffStreamStarted
    | DiffViewDiffStreamChunk
    | DiffViewDiffStreamEnded;

export interface DiffViewDiffStreamStarted
    extends DataWebViewMessage<{ requestId: string; streamId: string }> {
    type: WebViewMessageType.diffViewDiffStreamStarted;
}

export interface DiffViewDiffStreamChunkData {
    requestId?: string;
    streamId?: string;
    newChunkStart?: {
        // 1-indexed, [inclusive, exclusive)
        // Start line relative to original document from
        // start of the stream
        originalStartLine: number;
        // Start line relative to already-applied edits
        // from earlier in the stream
        stagedStartLine: number;
    };
    chunkContinue?: {
        newText: string;
    };
    chunkEnd?: {
        // 1-indexed, [inclusive, exclusive)
        // Start line relative to original document from
        // start of the stream
        originalStartLine: number;
        originalEndLine: number;
        // Start line relative to already-applied edits
        // from earlier in the stream
        stagedStartLine: number;
        stagedEndLine: number;
    };
}

export interface DiffViewDiffStreamChunk extends DataWebViewMessage<DiffViewDiffStreamChunkData> {
    type: WebViewMessageType.diffViewDiffStreamChunk;
}

export interface DiffViewDiffStreamEnded
    extends DataWebViewMessage<{ requestId: string; streamId: string }> {
    type: WebViewMessageType.diffViewDiffStreamEnded;
}

export interface DiffViewAcceptAllChunks extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewAcceptAllChunks;
}

export interface DiffViewAcceptSelectedChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewAcceptFocusedChunk;
}

export interface DiffViewRejectFocusedChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewRejectFocusedChunk;
}

export interface DiffViewFocusPrevChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewFocusPrevChunk;
}

export interface DiffViewFocusNextChunk extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewFocusNextChunk;
}

export interface DiffViewWindowFocusChange extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.diffViewWindowFocusChange;
}

export interface DiffViewFileFocusChange
    extends DataWebViewMessage<{
        filePath: string;
    }> {
    type: WebViewMessageType.diffViewFileFocus;
}

export interface ShouldShowSummary extends BasicWebViewMessage {
    type: WebViewMessageType.shouldShowSummary;
}

export interface ShowAugmentPanel extends BasicWebViewMessage {
    type: WebViewMessageType.showAugmentPanel;
}

export interface AbsPathFileDetails extends FileDetails {
    absPath: string;
}

export interface LineChanges {
    lineChanges: LineChange[];
    lineOffset: number;
}

export interface LineChange {
    originalStart: number;
    originalEnd: number;
    modifiedStart: number;
    modifiedEnd: number;
}

interface OnboardingLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.onboardingLoaded;
}

interface OnboardingUpdateState extends DataWebViewMessage<OnboardingInializeData> {
    type: WebViewMessageType.onboardingUpdateState;
}

interface OnboardingInializeData {
    isLoggedIn: boolean | undefined;
}

interface UsedChat extends BasicWebViewMessage {
    type: WebViewMessageType.usedChat;
}

interface PreferenceInit extends DataWebViewMessage<PreferenceInput> {
    type: WebViewMessageType.preferenceInit;
}

interface PreferenceResultMessage extends DataWebViewMessage<PreferenceResult> {
    type: WebViewMessageType.preferenceResultMessage;
}

interface PreferenceNotify extends DataWebViewMessage<string> {
    type: WebViewMessageType.preferenceNotify;
}

interface OpenSettingsPage extends DataWebViewMessage<string | undefined> {
    type: WebViewMessageType.openSettingsPage;
}

interface SettingsPanelLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.settingsPanelLoaded;
}

interface NavigateToSettingsSection extends DataWebViewMessage<string> {
    type: WebViewMessageType.navigateToSettingsSection;
}

interface PreferencePanelLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.preferencePanelLoaded;
}

interface ExecuteCommandMessage extends DataWebViewMessage<string> {
    type: WebViewMessageType.executeCommand;
}

interface ToggleCollapseUnchangedRegionsMessage extends BasicWebViewMessage {
    type: WebViewMessageType.toggleCollapseUnchangedRegions;
}

export enum MainPanelApp {
    loading = "loading",
    signIn = "sign-in",
    chat = "chat",
    workspaceContext = "workspace-context",
    awaitingSyncingPermission = "awaiting-syncing-permission",
    folderSelection = "folder-selection",
}

export interface MainPanelDisplayApp extends DataWebViewMessage<MainPanelApp | undefined> {
    type: WebViewMessageType.mainPanelDisplayApp;
}

interface MainPanelLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.mainPanelLoaded;
}

interface MainPanelActions extends DataWebViewMessage<Array<AnyActionName>> {
    type: WebViewMessageType.mainPanelActions;
}

export interface MainPanelPerformAction extends DataWebViewMessage<string> {
    type: WebViewMessageType.mainPanelPerformAction;
}

export interface ProjectName {
    name: string;
}

export interface MainPanelCreateProject extends DataWebViewMessage<ProjectName> {
    type: WebViewMessageType.mainPanelCreateProject;
}

export interface UsedSlashAction extends BasicWebViewMessage {
    type: WebViewMessageType.usedSlashAction;
}

export interface SignInLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.signInLoaded;
}
export interface SignOut extends BasicWebViewMessage {
    type: WebViewMessageType.signOut;
}
export interface SignInLoadedResponse
    extends DataWebViewMessage<{ client: "vscode" | "intellij" }> {
    type: WebViewMessageType.signInLoadedResponse;
}

export interface AwaitingSyncingPermissionLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.awaitingSyncingPermissionLoaded;
}

export interface AwaitingSyncingPermissionInitializeData {
    workspaceName: string | undefined;
    enableDebugFeatures?: boolean;
    maxTrackableFileCount?: number;
    userTier?: UserTier;
}

export interface AwaitingSyncingPermissionInitialize
    extends DataWebViewMessage<AwaitingSyncingPermissionInitializeData> {
    type: WebViewMessageType.awaitingSyncingPermissionInitialize;
}

export interface DiffViewLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.diffViewLoaded;
}

export interface DisposeDiffViewMessage extends BasicWebViewMessage {
    type: WebViewMessageType.disposeDiffView;
}

export interface DiffViewInitializeMessage extends DataWebViewMessage<DiffViewInitializeData> {
    type: WebViewMessageType.diffViewInitialize;
}

export interface DiffViewNotifyReinitMessage extends DataWebViewMessage<DiffViewInitializeData> {
    type: WebViewMessageType.diffViewNotifyReinit;
}

export interface TriggerInitialOrientation extends BasicWebViewMessage {
    type: WebViewMessageType.triggerInitialOrientation;
}

export interface ExecuteInitialOrientation extends BasicWebViewMessage {
    type: WebViewMessageType.executeInitialOrientation;
}

export enum OrientationState {
    idle = "idle",
    inProgress = "in-progress",
    succeeded = "succeeded",
    failed = "failed",
    aborted = "aborted",
}

export interface OrientationStatus {
    // Current state of the orientation process
    state: OrientationState;

    // Progress percentage (0-100) when in-progress
    progress?: number;

    // Timestamp of the last completed orientation (success or failure)
    lastRunTimestamp?: number;

    // Optional error message if the orientation failed
    errorMessage?: string;
}

export interface OrientationStatusUpdate extends DataWebViewMessage<OrientationStatus> {
    type: WebViewMessageType.orientationStatusUpdate;
}

export interface GetOrientationStatus extends BasicWebViewMessage {
    type: WebViewMessageType.getOrientationStatus;
}

export interface EmptyMessage extends BasicWebViewMessage {
    type: WebViewMessageType.empty;
}

export interface ListSetupScriptsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.listSetupScriptsRequest;
}

export interface ListSetupScriptsResponse
    extends DataWebViewMessage<{ scripts: SetupScript[]; error?: string }> {
    type: WebViewMessageType.listSetupScriptsResponse;
}

export interface SaveSetupScriptRequest
    extends DataWebViewMessage<{
        name: string;
        content: string;
        location: SetupScriptLocation;
    }> {
    type: WebViewMessageType.saveSetupScriptRequest;
}

export interface SavedSetupScriptResponseData {
    success: boolean;
    path?: string;
    error?: string;
}

export interface SaveSetupScriptResponse extends DataWebViewMessage<SavedSetupScriptResponseData> {
    type: WebViewMessageType.saveSetupScriptResponse;
}

export interface DeleteSetupScriptRequest
    extends DataWebViewMessage<{
        name: string;
        location: SetupScriptLocation;
    }> {
    type: WebViewMessageType.deleteSetupScriptRequest;
}

export interface DeleteSetupScriptResponse
    extends DataWebViewMessage<{
        success: boolean;
        error?: string;
    }> {
    type: WebViewMessageType.deleteSetupScriptResponse;
}

export interface RenameSetupScriptRequest
    extends DataWebViewMessage<{
        oldName: string;
        newName: string;
        location: SetupScriptLocation;
    }> {
    type: WebViewMessageType.renameSetupScriptRequest;
}

export interface RenameSetupScriptResponse
    extends DataWebViewMessage<{
        success: boolean;
        path?: string;
        error?: string;
    }> {
    type: WebViewMessageType.renameSetupScriptResponse;
}

export enum WSContextFileInclusionState {
    included = "included",
    excluded = "excluded",
    partial = "partial",
}

export interface WSContextSourceFolder {
    name: string;
    fileId: WSContextFileItemId;
    inclusionState: WSContextFileInclusionState;
    isWorkspaceFolder: boolean;
    isNestedFolder: boolean;
    isPending: boolean;
    trackedFileCount?: number;
}

export interface WSContextFileItemId {
    folderRoot: string;
    relPath: string;
}

export interface WSContextFileItem {
    name: string;
    fileId: WSContextFileItemId;
    type: "file" | "folder";
    inclusionState: WSContextFileInclusionState;
    reason: string;
    trackedFileCount?: number;
}

export interface WSContextGetChildrenRequestData {
    fileId: WSContextFileItemId;
}

export interface WSContextGetChildrenResponseData {
    children: WSContextFileItem[];
}

export interface WSContextGetChildrenRequest
    extends DataWebViewMessage<WSContextGetChildrenRequestData> {
    type: WebViewMessageType.wsContextGetChildrenRequest;
}

export interface WSContextGetChildrenResponse
    extends DataWebViewMessage<WSContextGetChildrenResponseData> {
    type: WebViewMessageType.wsContextGetChildrenResponse;
}

export interface WSContextGetSourceFoldersRequest extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextGetSourceFoldersRequest;
}

export interface WSContextGetSourceFoldersResponseData {
    workspaceFolders: WSContextSourceFolder[];
}

export interface WSContextGetSourceFoldersResponse
    extends DataWebViewMessage<WSContextGetSourceFoldersResponseData> {
    type: WebViewMessageType.wsContextGetSourceFoldersResponse;
}

export interface WSContextAddMoreSourceFolders extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextAddMoreSourceFolders;
}

export interface WSContextRemoveSourceFolder extends DataWebViewMessage<string> {
    type: WebViewMessageType.wsContextRemoveSourceFolder;
}

export interface WSContextSourceFoldersChanged extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextSourceFoldersChanged;
}

export interface WSContextFolderContentsChanged extends DataWebViewMessage<string> {
    type: WebViewMessageType.wsContextFolderContentsChanged;
}

export interface WSContextUserRequestedRefresh extends BasicWebViewMessage {
    type: WebViewMessageType.wsContextUserRequestedRefresh;
}

export interface AugmentCommand extends DataWebViewMessage<string> {
    type: WebViewMessageType.augmentLink;
}

export interface ChatModeChangedMessage
    extends DataWebViewMessage<{
        mode: ChatMode;
    }> {
    type: WebViewMessageType.chatModeChanged;
}

export interface ChatGetAgentOnboardingPromptRequest extends DataWebViewMessage<{}> {
    type: WebViewMessageType.chatGetAgentOnboardingPromptRequest;
}

export interface ChatGetAgentOnboardingPromptResponse
    extends DataWebViewMessage<{ prompt: string }> {
    type: WebViewMessageType.chatGetAgentOnboardingPromptResponse;
}

export interface ResetAgentOnboardingMessage extends BasicWebViewMessage {
    type: WebViewMessageType.resetAgentOnboarding;
}

////////////// Remote Agents //////////////
export interface DiffExplanationRequestMessage
    extends DataWebViewMessage<DiffExplanationRequestData> {
    type: WebViewMessageType.diffExplanationRequest;
}

export interface DiffExplanationResponseMessage
    extends DataWebViewMessage<DiffExplanationResponseData> {
    type: WebViewMessageType.diffExplanationResponse;
}

export interface DiffGroupChangesRequestMessage
    extends DataWebViewMessage<DiffExplanationRequestData> {
    type: WebViewMessageType.diffGroupChangesRequest;
}

export interface DiffGroupChangesResponseMessage
    extends DataWebViewMessage<DiffGroupChangesResponseData> {
    type: WebViewMessageType.diffGroupChangesResponse;
}

export interface DiffDescriptionsRequestMessage
    extends DataWebViewMessage<{
        groupedChanges: DiffGroupChangesResponseData["groupedChanges"];
        apikey?: string;
    }> {
    type: WebViewMessageType.diffDescriptionsRequest;
}

export interface DiffDescriptionsResponseMessage
    extends DataWebViewMessage<DiffExplanationResponseData> {
    type: WebViewMessageType.diffDescriptionsResponse;
}

export interface CanApplyChangesRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.canApplyChangesRequest;
}

export interface CanApplyChangesResponseMessage
    extends DataWebViewMessage<CanApplyChangesResponseData> {
    type: WebViewMessageType.canApplyChangesResponse;
}

export interface CanApplyChangesResponseData {
    canApply: boolean;
    hasUnstagedChanges: boolean;
    error?: string;
}

export interface ApplyChangesRequestMessage extends DataWebViewMessage<ApplyChangesRequestData> {
    type: WebViewMessageType.applyChangesRequest;
}

export interface ApplyChangesResponseMessage extends DataWebViewMessage<ApplyChangesResponseData> {
    type: WebViewMessageType.applyChangesResponse;
}

export interface ApplyChangesRequestData {
    path: string;
    originalCode: string;
    newCode: string;
}

export interface ApplyChangesResponseData {
    success: boolean;
    hasConflicts?: boolean;
    error?: string;
}

export interface PreviewApplyChangesRequestMessage
    extends DataWebViewMessage<ApplyChangesRequestData> {
    type: WebViewMessageType.previewApplyChangesRequest;
}

export interface PreviewApplyChangesResponseMessage
    extends DataWebViewMessage<PreviewApplyChangesResponseData> {
    type: WebViewMessageType.previewApplyChangesResponse;
}

export interface PreviewApplyChangesResponseData {
    mergedContent: string;
    hasConflicts: boolean;
    error?: string;
}

export interface OpenFileRequestMessage extends DataWebViewMessage<OpenFileRequestData> {
    type: WebViewMessageType.openFileRequest;
}

export interface OpenFileResponseMessage extends DataWebViewMessage<OpenFileResponseData> {
    type: WebViewMessageType.openFileResponse;
}

export interface OpenFileRequestData {
    path: string;
}

export interface OpenFileResponseData {
    success: boolean;
    error?: string;
}

export interface StashUnstagedChangesRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.stashUnstagedChangesRequest;
}

export interface StashUnstagedChangesResponseMessage
    extends DataWebViewMessage<StashUnstagedChangesData> {
    type: WebViewMessageType.stashUnstagedChangesResponse;
}

export interface StashUnstagedChangesData {
    success: boolean;
    error?: string;
}

export interface GetRemoteAgentOverviewsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getRemoteAgentOverviewsRequest;
}

export interface GetRemoteAgentOverviewsResponse extends DataWebViewMessage<AgentOverviewsData> {
    type: WebViewMessageType.getRemoteAgentOverviewsResponse;
}

export interface RemoteAgentOverviewsStreamRequest
    extends DataWebViewMessage<{
        lastUpdateTimestamp?: string;
        /**
         * Unique ID for the AbortController for this stream.
         * Call cancelRemoteAgentsStreamRequest with
         * this ID to cancel the stream if it is still active.
         * */
        streamId: string;
    }> {
    type: WebViewMessageType.remoteAgentOverviewsStreamRequest;
}

export interface RemoteAgentOverviewsStreamResponse
    extends DataWebViewMessage<{
        response: ListRemoteAgentsStreamResponse;
        error?: string;
    }> {
    type: WebViewMessageType.remoteAgentOverviewsStreamResponse;
}

export interface AgentOverviewsData {
    overviews: RemoteAgent[];
    maxRemoteAgents: number;
    maxActiveRemoteAgents: number;
    error?: string;
}

export interface CreateRemoteAgentData {
    prompt: string;
    workspaceSetup: RemoteAgentWorkspaceSetup;
    setupScript?: string;
    isSetupScriptAgent?: boolean;
    modelId?: string;
    remoteAgentCreationMetrics?: RemoteAgentCreationMetrics;
}

export interface GetRemoteAgentChatHistoryRequest
    extends DataWebViewMessage<{
        agentId: string;
        lastProcessedSequenceId: number;
    }> {
    type: WebViewMessageType.getRemoteAgentChatHistoryRequest;
}

export interface GetShouldShowSSHConfigPermissionPromptMessageRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptRequest;
}

export interface GetShouldShowSSHConfigPermissionPromptMessageResponse
    extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.getShouldShowSSHConfigPermissionPromptResponse;
    data: boolean;
}

export interface SetPermissionToWriteToSSHConfigMessage extends DataWebViewMessage<boolean> {
    type: WebViewMessageType.setPermissionToWriteToSSHConfig;
    data: boolean;
}

export interface GetRemoteAgentChatHistoryResponse
    extends DataWebViewMessage<{
        chatHistory: RemoteAgentExchange[];
        error?: string;
    }> {
    type: WebViewMessageType.getRemoteAgentChatHistoryResponse;
}

export interface RemoteAgentHistoryStreamRequest
    extends DataWebViewMessage<{
        agentId: string;
        lastProcessedSequenceId: number;
        /**
         * Unique ID for the AbortController for this stream.
         * Call cancelRemoteAgentsStreamRequest with
         * this ID to cancel the stream if it is still active.
         * */
        streamId: string;
    }> {
    type: WebViewMessageType.remoteAgentHistoryStreamRequest;
}

export interface RemoteAgentHistoryStreamResponse
    extends DataWebViewMessage<GetRemoteAgentHistoryStreamResponse> {
    type: WebViewMessageType.remoteAgentHistoryStreamResponse;
}

export interface CancelRemoteAgentsStreamRequest extends DataWebViewMessage<{ streamId: string }> {
    type: WebViewMessageType.cancelRemoteAgentsStreamRequest;
}

export interface CreateRemoteAgentRequestMessage extends DataWebViewMessage<CreateRemoteAgentData> {
    type: WebViewMessageType.createRemoteAgentRequest;
}

export interface CreateRemoteAgentResponseMessage
    extends DataWebViewMessage<{
        success: boolean;
        agentId?: string;
        error?: string;
    }> {
    type: WebViewMessageType.createRemoteAgentResponse;
}

export interface DeleteRemoteAgentRequestMessage extends DataWebViewMessage<DeleteRemoteAgentData> {
    type: WebViewMessageType.deleteRemoteAgentRequest;
}

export interface DeleteRemoteAgentData {
    agentId: string;
    doSkipConfirmation?: boolean;
}

export interface DeleteRemoteAgentResponseMessage
    extends DataWebViewMessage<{
        success: boolean;
        error?: string;
    }> {
    type: WebViewMessageType.deleteRemoteAgentResponse;
}

export interface RemoteAgentChatRequestMessage
    extends DataWebViewMessage<{
        agentId: string;
        requestDetails: RemoteAgentChatRequestDetails;
        timeoutMs?: number;
    }> {
    type: WebViewMessageType.remoteAgentChatRequest;
}

export interface RemoteAgentChatResponseMessage
    extends DataWebViewMessage<{ nodes: ChatResultNode[]; error?: string; requestId?: string }> {
    type: WebViewMessageType.remoteAgentChatResponse;
}

export interface RemoteAgentInterruptRequestMessage
    extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.remoteAgentInterruptRequest;
}

export interface RemoteAgentInterruptResponseMessage extends DataWebViewMessage<RemoteAgentStatus> {
    type: WebViewMessageType.remoteAgentInterruptResponse;
}

export interface RemoteAgentSshRequestMessage extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.remoteAgentSshRequest;
}

export interface RemoteAgentSshResponseMessage
    extends DataWebViewMessage<{ success: boolean; error?: string }> {
    type: WebViewMessageType.remoteAgentSshResponse;
}

export interface RemoteAgentNotificationSettings {
    /** The ID of the remote agent */
    agentId: string;
    /** True if the notification is enabled for this remote agent */
    enabled: boolean;
}

export interface SetRemoteAgentNotificationEnabledMessage
    extends DataWebViewMessage<RemoteAgentNotificationSettings> {
    type: WebViewMessageType.setRemoteAgentNotificationEnabled;
}

export interface GetRemoteAgentNotificationEnabledOptions {
    /** The IDs of the remote agents to get the notification settings for. If undefined, get all settings for all available agents */
    agentIds?: string[];
}

export interface GetRemoteAgentNotificationEnabledMessage
    extends DataWebViewMessage<GetRemoteAgentNotificationEnabledOptions> {
    type: WebViewMessageType.getRemoteAgentNotificationEnabledRequest;
}

export interface GetRemoteAgentNotificationEnabledResponseMessage
    extends DataWebViewMessage<RemoteAgentNotificationSettingsContext> {
    type: WebViewMessageType.getRemoteAgentNotificationEnabledResponse;
}

export interface DeleteRemoteAgentNotificationEnabledMessage
    extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.deleteRemoteAgentNotificationEnabled;
}

export interface SetRemoteAgentPinnedStatusMessage
    extends DataWebViewMessage<{ agentId: string; isPinned: boolean }> {
    type: WebViewMessageType.setRemoteAgentPinnedStatus;
}

export interface GetRemoteAgentPinnedStatusRequestMessage
    extends DataWebViewMessage<{ agentIds?: string[] }> {
    type: WebViewMessageType.getRemoteAgentPinnedStatusRequest;
}

export interface GetRemoteAgentPinnedStatusResponseMessage
    extends DataWebViewMessage<RemoteAgentPinnedStatusContext> {
    type: WebViewMessageType.getRemoteAgentPinnedStatusResponse;
}

export interface DeleteRemoteAgentPinnedStatusMessage
    extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.deleteRemoteAgentPinnedStatus;
}

export interface RemoteAgentNotifyReadyMessage extends DataWebViewMessage<RemoteAgent> {
    type: WebViewMessageType.remoteAgentNotifyReady;
}

export interface RemoteAgentSelectAgentIdMessage extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.remoteAgentSelectAgentId;
}

export interface RemoteAgentWorkspaceLogsRequestMessage
    extends DataWebViewMessage<{
        agentId: string;
        lastProcessedStep?: number;
        lastProcessedSequenceId?: number;
    }> {
    type: WebViewMessageType.remoteAgentWorkspaceLogsRequest;
}

export interface RemoteAgentWorkspaceLogsResponseMessage
    extends DataWebViewMessage<{
        workspaceSetupStatus: RemoteWorkspaceSetupStatus;
    }> {
    type: WebViewMessageType.remoteAgentWorkspaceLogsResponse;
}

export interface RemoteAgentPauseRequestMessage extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.remoteAgentPauseRequest;
}

export interface RemoteAgentResumeRequestMessage extends DataWebViewMessage<{ agentId: string }> {
    type: WebViewMessageType.remoteAgentResumeRequest;
}

export interface RemoteAgentResumeHintRequestMessage
    extends DataWebViewMessage<{
        agentId: string;
        hintReason?: RemoteAgentResumeHintReason;
    }> {
    type: WebViewMessageType.remoteAgentResumeHintRequest;
}

export enum RemoteAgentResumeHintReason {
    unspecified = 0,
    typingMessage = 1,
    viewingAgent = 2,
}
export interface UpdateRemoteAgentRequestMessage extends DataWebViewMessage<UpdateRemoteAgentData> {
    type: WebViewMessageType.updateRemoteAgentRequest;
}

export interface UpdateRemoteAgentData {
    agentId: string;
    newTitle?: string;
}

export interface UpdateRemoteAgentResponseMessage
    extends DataWebViewMessage<{
        success: boolean;
        agent?: RemoteAgent;
        error?: string;
    }> {
    type: WebViewMessageType.updateRemoteAgentResponse;
}

////////////// Shared Webview Store //////////////

export interface UpdateSharedWebviewStateMessage<T> extends DataWebviewMessageWithId<T> {
    type: WebViewMessageType.updateSharedWebviewState;
}

export interface GetSharedWebviewStateMessage extends DataWebviewMessageWithId<{}> {
    type: WebViewMessageType.getSharedWebviewState;
}

export interface GetSharedWebviewStateResponseMessage<T> extends DataWebviewMessageWithId<T> {
    type: WebViewMessageType.getSharedWebviewStateResponse;
}

////////////// Git Reference Messages //////////////
export interface GetGitBranchesRequestMessage extends DataWebViewMessage<{ prefix?: string }> {
    type: WebViewMessageType.getGitBranchesRequest;
}

export interface GetGitBranchesResponseMessage
    extends DataWebViewMessage<{
        branches: GitBranch[];
    }> {
    type: WebViewMessageType.getGitBranchesResponse;
}

export interface GitFetchRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.gitFetchRequest;
}

export interface GitFetchResponseMessage extends BasicWebViewMessage {
    type: WebViewMessageType.gitFetchResponse;
}

export interface IsGitRepositoryRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.isGitRepositoryRequest;
}

export interface IsGitRepositoryResponseMessage
    extends DataWebViewMessage<{
        isGitRepository: boolean;
    }> {
    type: WebViewMessageType.isGitRepositoryResponse;
}

export interface GetRemoteUrlRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.getRemoteUrlRequest;
}

export interface GetRemoteUrlResponseMessage
    extends DataWebViewMessage<{
        remoteUrl: string;
        error?: string;
    }> {
    type: WebViewMessageType.getRemoteUrlResponse;
}

export interface GetWorkspaceDiffRequestMessage
    extends DataWebViewMessage<{
        branchName: string;
    }> {
    type: WebViewMessageType.getWorkspaceDiffRequest;
}

export interface GetWorkspaceDiffResponseMessage
    extends DataWebViewMessage<{
        diff: string;
    }> {
    type: WebViewMessageType.getWorkspaceDiffResponse;
}

export interface IsGithubAuthenticatedRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.isGithubAuthenticatedRequest;
}

export interface IsGithubAuthenticatedResponseMessage
    extends DataWebViewMessage<{
        isAuthenticated: boolean;
    }> {
    type: WebViewMessageType.isGithubAuthenticatedResponse;
}

export interface AuthenticateGithubRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.authenticateGithubRequest;
}

export interface AuthenticateGithubResponseMessage
    extends DataWebViewMessage<{
        success: boolean;
        message?: string;
        url?: string;
    }> {
    type: WebViewMessageType.authenticateGithubResponse;
}

export interface RevokeGithubAccessRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.revokeGithubAccessRequest;
}

export interface RevokeGithubAccessResponseMessage
    extends DataWebViewMessage<{
        success: boolean;
        message?: string;
    }> {
    type: WebViewMessageType.revokeGithubAccessResponse;
}

export interface ListGithubReposForAuthenticatedUserRequestMessage
    extends DataWebViewMessage<{
        page?: number;
    }> {
    type: WebViewMessageType.listGithubReposForAuthenticatedUserRequest;
}

export interface ListGithubReposForAuthenticatedUserResponseMessage
    extends DataWebViewMessage<{
        repos: GithubRepo[];
        hasNextPage: boolean;
        nextPage: number;
        error?: string;
        isDevDeploy?: boolean;
    }> {
    type: WebViewMessageType.listGithubReposForAuthenticatedUserResponse;
}

export interface ListGithubRepoBranchesRequestMessage
    extends DataWebViewMessage<{
        repo: GithubRepo;
        page?: number;
    }> {
    type: WebViewMessageType.listGithubRepoBranchesRequest;
}

export interface ListGithubRepoBranchesResponseMessage
    extends DataWebViewMessage<{
        branches: GithubBranch[];
        hasNextPage: boolean;
        nextPage: number;
        error?: string;
        isDevDeploy?: boolean;
    }> {
    type: WebViewMessageType.listGithubRepoBranchesResponse;
}

export interface GetGithubRepoRequestMessage
    extends DataWebViewMessage<{
        repo: GithubRepo;
    }> {
    type: WebViewMessageType.getGithubRepoRequest;
}

export interface GetGithubRepoResponseMessage
    extends DataWebViewMessage<{
        repo: GithubRepo;
        error?: string;
        isDevDeploy?: boolean;
    }> {
    type: WebViewMessageType.getGithubRepoResponse;
}

export interface GetCurrentLocalBranchRequestMessage extends BasicWebViewMessage {
    type: WebViewMessageType.getCurrentLocalBranchRequest;
}

export interface GetCurrentLocalBranchResponseMessage
    extends DataWebViewMessage<{
        branch: string | undefined;
        error?: string;
    }> {
    type: WebViewMessageType.getCurrentLocalBranchResponse;
}

////////////// Remote Agent Diff Panel Messages //////////////
export interface RemoteAgentDiffPanelLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.remoteAgentDiffPanelLoaded;
}

export interface RemoteAgentDiffPanelSetOptsMessage
    extends DataWebViewMessage<IRemoteAgentDiffPanelOptions> {
    type: WebViewMessageType.remoteAgentDiffPanelSetOpts;
}

export interface ShowRemoteAgentDiffPanelMessage
    extends DataWebViewMessage<IRemoteAgentDiffPanelOptions> {
    type: WebViewMessageType.showRemoteAgentDiffPanel;
}

export interface CloseRemoteAgentDiffPanelMessage extends BasicWebViewMessage {
    type: WebViewMessageType.closeRemoteAgentDiffPanel;
}

////////////// Remote Agent Home Panel Messages //////////////
export interface RemoteAgentHomePanelLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.remoteAgentHomePanelLoaded;
}

export interface ShowRemoteAgentHomePanelMessage extends BasicWebViewMessage {
    type: WebViewMessageType.showRemoteAgentHomePanel;
}

export interface CloseRemoteAgentHomePanelMessage extends BasicWebViewMessage {
    type: WebViewMessageType.closeRemoteAgentHomePanel;
}

////////////// Tool Config Messages //////////////
export interface ToolConfigLoaded extends BasicWebViewMessage {
    type: WebViewMessageType.toolConfigLoaded;
}

export interface ToolConfigInitialize extends DataWebViewMessage<ToolConfigInitData> {
    type: WebViewMessageType.toolConfigInitialize;
}

export interface ToolConfigSave extends DataWebViewMessage<ToolConfigSaveRequest> {
    type: WebViewMessageType.toolConfigSave;
}

export interface ToolConfigGetDefinitions extends DataWebViewMessage<{ useCache: boolean }> {
    type: WebViewMessageType.toolConfigGetDefinitions;
}

export interface ToolConfigDefinitionsResponse
    extends DataWebViewMessage<{
        hostTools: ToolDefinitionWithSettings[];
    }> {
    type: WebViewMessageType.toolConfigDefinitionsResponse;
}

export interface ToolConfigStartOAuthRequest {
    authUrl: string;
}

export interface StartRemoteMCPAuthRequest {
    name: string;
}
export interface ToolConfigStartOAuth extends DataWebViewMessage<ToolConfigStartOAuthRequest> {
    type: WebViewMessageType.toolConfigStartOAuth;
}

export interface ToolConfigStartOAuthResponse extends DataWebViewMessage<{ ok: boolean }> {
    type: WebViewMessageType.toolConfigStartOAuthResponse;
}

export interface ToolConfigRevokeAccessRequest {
    toolId: ToolIdentifier;
}

export interface ToolConfigRevokeAccess extends DataWebViewMessage<ToolConfigRevokeAccessRequest> {
    type: WebViewMessageType.toolConfigRevokeAccess;
}

export interface ToolApprovalConfigSetRequest {
    toolId: ToolIdentifier;
    approvalConfig: ToolApprovalConfig;
}

export interface ToolApprovalConfigSetRequestMessage
    extends DataWebViewMessage<ToolApprovalConfigSetRequest> {
    type: WebViewMessageType.toolApprovalConfigSetRequest;
}

export interface ToolApprovalLevelGetRequest {
    toolId: ToolIdentifier;
}

export interface ToolApprovalLevelGet extends DataWebViewMessage<ToolApprovalLevelGetRequest> {
    type: WebViewMessageType.toolApprovalConfigGetRequest;
}

export interface ToolApprovalLevelGetResponse
    extends DataWebViewMessage<ToolApprovalConfig | undefined> {
    type: WebViewMessageType.toolApprovalConfigGetResponse;
}

// Storage operations for MCP servers
export interface GetStoredMCPServersRequest {
    type: WebViewMessageType.getStoredMCPServers;
}

interface BaseMCPServer {
    id: string;
    name: string;
    tools?: ToolDefinitionWithSettings[];
    disabled?: boolean;
    type?: string;
}

export interface MCPServerStdio extends BaseMCPServer {
    type: "stdio";
    command: string;
    arguments: string;
    useShellInterpolation?: boolean;
    env?: Record<string, string>;
}

export interface MCPServerHttp extends BaseMCPServer {
    type: "http" | "sse";
    url: string;
    authRequired?: boolean; // Indicates if the server requires authentication (detected via 401 response)
    oauthMetadata?: OAuthMetadata; // OAuth 2.0 metadata discovered from the server
}

export type MCPServer = MCPServerStdio | MCPServerHttp;

export interface SetStoredMCPServersRequest extends DataWebViewMessage<MCPServer[]> {
    type: WebViewMessageType.setStoredMCPServers;
}

export interface GetStoredMCPServersResponse extends DataWebViewMessage<MCPServer[]> {
    type: WebViewMessageType.getStoredMCPServersResponse;
}

export interface DeleteOAuthSessionRequest extends DataWebViewMessage<string> {
    type: WebViewMessageType.deleteOAuthSession;
}

export interface GetTerminalSettingsRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getTerminalSettings;
}

export interface TerminalSettingsResponse extends DataWebViewMessage<TerminalSettings> {
    type: WebViewMessageType.terminalSettingsResponse;
}

export interface UpdateTerminalSettingsRequest
    extends DataWebViewMessage<Pick<TerminalSettings, "selectedShell" | "startupScript">> {
    type: WebViewMessageType.updateTerminalSettings;
}

export interface CanShowTerminalRequest
    extends DataWebViewMessage<{ terminalId?: number; command?: string }> {
    type: WebViewMessageType.canShowTerminal;
}

export interface CanShowTerminalResponse
    extends DataWebViewMessage<{ terminalId?: number; command?: string; canShow: boolean }> {
    type: WebViewMessageType.canShowTerminalResponse;
}

export interface ShowTerminalRequest
    extends DataWebViewMessage<{ terminalId?: number; command?: string }> {
    type: WebViewMessageType.showTerminal;
}

export interface ShowTerminalResponse
    extends DataWebViewMessage<{ terminalId?: number; success: boolean }> {
    type: WebViewMessageType.showTerminalResponse;
}

export interface GetRemoteAgentStatusMessage extends BasicWebViewMessage {
    type: WebViewMessageType.getRemoteAgentStatus;
}

export interface RemoteAgentStatusResponseMessage
    extends DataWebViewMessage<{
        isRemoteAgentSshWindow: boolean;
        remoteAgentId?: string;
    }> {
    type: WebViewMessageType.remoteAgentStatusResponse;
}

export interface RemoteAgentStatusChangedMessage
    extends DataWebViewMessage<{
        isRemoteAgentSshWindow: boolean;
        remoteAgentId?: string;
    }> {
    type: WebViewMessageType.remoteAgentStatusChanged;
}

export interface MemoriesLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.memoriesLoaded;
}

export interface SaveLastRemoteAgentSetupRequest
    extends DataWebViewMessage<LastUsedRemoteAgentSetup> {
    type: WebViewMessageType.saveLastRemoteAgentSetupRequest;
}

export interface GetLastRemoteAgentSetupRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getLastRemoteAgentSetupRequest;
}

export interface GetLastRemoteAgentSetupResponse
    extends DataWebViewMessage<LastUsedRemoteAgentSetup> {
    type: WebViewMessageType.getLastRemoteAgentSetupResponse;
}
export interface RulesLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.rulesLoaded;
}
export interface MemoriesLoadedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.memoriesLoaded;
}

export interface GetRulesListMessageResponse extends DataWebViewMessage<Rule[]> {
    type: WebViewMessageType.getRulesListResponse;
}

export interface GetSubscriptionInfoRequest extends BasicWebViewMessage {
    type: WebViewMessageType.getSubscriptionInfo;
}

export interface GetSubscriptionInfoResponse
    extends DataWebViewMessage<{
        enterprise?: any;
        activeSubscription?: {
            endDate?: string;
            usageBalanceDepleted?: boolean;
        };
        inactiveSubscription?: any;
    }> {
    type: WebViewMessageType.getSubscriptionInfoResponse;
}

export interface ReportRemoteAgentEventMessage
    extends DataWebViewMessage<RemoteAgentSessionEventData> {
    type: WebViewMessageType.reportRemoteAgentEvent;
}

export interface ReportAgentChangesAppliedMessage extends BasicWebViewMessage {
    type: WebViewMessageType.reportAgentChangesApplied;
}

export interface StartRemoteMCPAuth extends DataWebViewMessage<StartRemoteMCPAuthRequest> {
    type: WebViewMessageType.startRemoteMCPAuth;
}

export type WebViewMessage =
    | HistoryLoadedMessage
    | HistoryInitializeMessage
    | HistoryConfigMessage
    | CopyRequestIDToClipboardMessage
    | OpenFileMessage
    | OpenDiffInBufferMessage
    | SaveFileMessage
    | LoadFileMessage
    | OpenMemoriesFileMessage
    | OpenAndEditFileMessage
    | ShowNotificationMessage
    | ShowBannerNotificationMessage
    | DismissBannerNotificationMessage
    | OpenConfirmationModal
    | ConfirmationModalResponse
    | CompletionRatingMessage
    | CompletionRatingDoneMessage
    | NextEditRatingMessage
    | NextEditRatingDoneMessage
    | NextEditCurorWithinSuggestion
    | NextEditPanelFocusMessage
    | CompletionsMessage
    | CurrentlyOpenFiles
    | FindFileRequest
    | FindFileResponse
    | ResolveFileRequest
    | ResolveFileResponse
    | FindRecentlyOpenedFilesRequest
    | FindRecentlyOpenedFilesResponse
    | FindFolderRequest
    | FindFolderResponse
    | FindExternalSourcesRequest
    | FindExternalSourcesResponse
    | FindSymbolRequest
    | FindSymbolRegexRequest
    | ExecuteCommandMessage
    | ToggleCollapseUnchangedRegionsMessage
    | FindSymbolResponse
    | FileRangesSelected
    | GetDiagnosticsRequest
    | GetDiagnosticsResponse
    | ResolveWorkspaceFileChunkRequest
    | ResolveWorkspaceFileChunkResponse
    | SourceFoldersUpdated
    | SourceFoldersSyncStatus
    | InstructionsMessage
    | ReportWebviewClientMetricRequest
    | TrackAnalyticsEventRequest
    | ReportError
    | ChatLoaded
    | ChatClearMetadata
    | ChatInitialize
    | ChatGetStreamRequest
    | ChatUserMessage
    | GenerateCommitMessage
    | ChatUserCancel
    | ChatModelReply
    | ChatInstructionMessage
    | ChatInstructionModelReply
    | ChatCreateFile
    | ChatSmartPaste
    | ChatRatingMessage
    | ChatRatingDoneMessage
    | ChatSaveImageRequest
    | ChatSaveImageResponse
    | ChatLoadImageRequest
    | ChatLoadImageResponse
    | ChatDeleteImageRequest
    | ChatDeleteImageResponse
    | ChatSaveAttachmentRequest
    | ChatSaveAttachmentResponse
    | RunSlashCommand
    | CallToolMessage
    | CallToolResponse
    | CancelToolRunMessage
    | CancelToolRunResponse
    | CheckToolExistsRequest
    | CheckToolExistsResponse
    | GetToolCallCheckpoint
    | GetToolCallCheckpointResponse
    | NewThread
    | DiffViewLoadedMessage
    | DiffViewFetchPendingStream
    | DiffViewInitializeMessage
    | DiffViewNotifyReinitMessage
    | DiffViewResolveMessage
    | DiffViewDiffStreamStarted
    | DiffViewDiffStreamChunk
    | DiffViewDiffStreamEnded
    | DiffViewAcceptAllChunks
    | DiffViewAcceptSelectedChunk
    | DiffViewRejectFocusedChunk
    | DiffViewFocusPrevChunk
    | DiffViewFocusNextChunk
    | DiffViewWindowFocusChange
    | DiffViewFileFocusChange
    | DisposeDiffViewMessage
    | NextEditDismissMessage
    | NextEditLoadedMessage
    | NextEditSuggestionsMessage
    | NextEditRefreshStarted
    | NextEditRefreshFinished
    | NextEditCancelMessage
    | NextEditPreviewActive
    | NextEditSuggestionsChanged
    | NextEditSuggestionsActionMessage
    | NextEditFocusedSuggestionChanged
    | NextEditOpenSuggestion
    | SaveLastRemoteAgentSetupRequest
    | GetLastRemoteAgentSetupRequest
    | GetLastRemoteAgentSetupResponse
    | ReadFileRequest
    | ReadFileResponse
    | NextEditToggleSuggestionTreeMessage
    | OnboardingLoaded
    | OnboardingUpdateState
    | UsedChat
    | PreferenceInit
    | PreferenceResultMessage
    | PreferenceNotify
    | OpenSettingsPage
    | SettingsPanelLoaded
    | NavigateToSettingsSection
    | PreferencePanelLoaded
    | SaveChatMessage
    | SaveChatDoneMessage
    | ShouldShowSummary
    | ShowAugmentPanel
    | MainPanelLoaded
    | MainPanelDisplayApp
    | MainPanelActions
    | MainPanelPerformAction
    | MainPanelCreateProject
    | UsedSlashAction
    | SignInLoaded
    | SignInLoadedResponse
    | SignOut
    | AwaitingSyncingPermissionLoaded
    | AwaitingSyncingPermissionInitialize
    | EmptyMessage
    | WSContextGetChildrenRequest
    | WSContextGetChildrenResponse
    | WSContextGetSourceFoldersRequest
    | WSContextGetSourceFoldersResponse
    | WSContextAddMoreSourceFolders
    | WSContextRemoveSourceFolder
    | WSContextSourceFoldersChanged
    | WSContextFolderContentsChanged
    | WSContextUserRequestedRefresh
    | AugmentCommand
    | SyncEnabledStateUpdate
    | UpdateGuidelinesState
    | OpenGuidelines
    | UpdateWorkspaceGuidelines
    | UpdateUserGuidelines
    | AsyncWebViewMessage<WebViewMessage>
    | ChatModeChangedMessage
    | ChatAgentEditListHasUpdatesMessage
    | ChatMemoryHasUpdatesMessage
    | ChatGetAgentOnboardingPromptRequest
    | ChatGetAgentOnboardingPromptResponse
    | GetRemoteAgentOverviewsRequest
    | GetRemoteAgentOverviewsResponse
    | RemoteAgentOverviewsStreamRequest
    | RemoteAgentOverviewsStreamResponse
    | GetRemoteAgentChatHistoryRequest
    | GetRemoteAgentChatHistoryResponse
    | GetShouldShowSSHConfigPermissionPromptMessageRequest
    | GetShouldShowSSHConfigPermissionPromptMessageResponse
    | SetPermissionToWriteToSSHConfigMessage
    | RemoteAgentHistoryStreamRequest
    | RemoteAgentHistoryStreamResponse
    | CancelRemoteAgentsStreamRequest
    | CanApplyChangesRequestMessage
    | CanApplyChangesResponseMessage
    | ApplyChangesRequestMessage
    | ApplyChangesResponseMessage
    | PreviewApplyChangesRequestMessage
    | PreviewApplyChangesResponseMessage
    | OpenFileRequestMessage
    | OpenFileResponseMessage
    | StashUnstagedChangesRequestMessage
    | StashUnstagedChangesResponseMessage
    | CreateRemoteAgentRequestMessage
    | CreateRemoteAgentResponseMessage
    | DeleteRemoteAgentRequestMessage
    | DeleteRemoteAgentResponseMessage
    | RemoteAgentChatRequestMessage
    | RemoteAgentChatResponseMessage
    | RemoteAgentInterruptRequestMessage
    | RemoteAgentInterruptResponseMessage
    | RemoteAgentSshRequestMessage
    | RemoteAgentSshResponseMessage
    | SetRemoteAgentNotificationEnabledMessage
    | GetRemoteAgentNotificationEnabledMessage
    | GetRemoteAgentNotificationEnabledResponseMessage
    | DeleteRemoteAgentNotificationEnabledMessage
    | SetRemoteAgentPinnedStatusMessage
    | GetRemoteAgentPinnedStatusRequestMessage
    | GetRemoteAgentPinnedStatusResponseMessage
    | DeleteRemoteAgentPinnedStatusMessage
    | RemoteAgentNotifyReadyMessage
    | RemoteAgentSelectAgentIdMessage
    | RemoteAgentWorkspaceLogsRequestMessage
    | RemoteAgentWorkspaceLogsResponseMessage
    | RemoteAgentPauseRequestMessage
    | RemoteAgentResumeRequestMessage
    | RemoteAgentResumeHintRequestMessage
    | UpdateRemoteAgentRequestMessage
    | UpdateRemoteAgentResponseMessage
    | UpdateSharedWebviewStateMessage<unknown>
    | GetSharedWebviewStateMessage
    | GetSharedWebviewStateResponseMessage<unknown>
    | GetGitBranchesRequestMessage
    | GetGitBranchesResponseMessage
    | GitFetchRequestMessage
    | GitFetchResponseMessage
    | IsGitRepositoryRequestMessage
    | IsGitRepositoryResponseMessage
    | GetWorkspaceDiffRequestMessage
    | GetWorkspaceDiffResponseMessage
    | GetRemoteUrlRequestMessage
    | GetRemoteUrlResponseMessage
    | DiffExplanationRequestMessage
    | DiffExplanationResponseMessage
    | IsGithubAuthenticatedRequestMessage
    | IsGithubAuthenticatedResponseMessage
    | AuthenticateGithubRequestMessage
    | AuthenticateGithubResponseMessage
    | RevokeGithubAccessRequestMessage
    | RevokeGithubAccessResponseMessage
    | ListGithubReposForAuthenticatedUserRequestMessage
    | ListGithubReposForAuthenticatedUserResponseMessage
    | ListGithubRepoBranchesRequestMessage
    | ListGithubRepoBranchesResponseMessage
    | GetGithubRepoRequestMessage
    | GetGithubRepoResponseMessage
    | GetCurrentLocalBranchRequestMessage
    | GetCurrentLocalBranchResponseMessage
    | DiffGroupChangesRequestMessage
    | DiffGroupChangesResponseMessage
    | DiffDescriptionsRequestMessage
    | DiffDescriptionsResponseMessage
    | RemoteAgentDiffPanelLoadedMessage
    | RemoteAgentDiffPanelSetOptsMessage
    | ShowRemoteAgentDiffPanelMessage
    | CloseRemoteAgentDiffPanelMessage
    | RemoteAgentHomePanelLoadedMessage
    | ShowRemoteAgentHomePanelMessage
    | CloseRemoteAgentHomePanelMessage
    | ResetAgentOnboardingMessage
    | TriggerInitialOrientation
    | ExecuteInitialOrientation
    | OrientationStatusUpdate
    | GetOrientationStatus
    | CheckAgentAutoModeApproval
    | CheckAgentAutoModeApprovalResponse
    | SetAgentAutoModeApproved
    | ListSetupScriptsRequest
    | ListSetupScriptsResponse
    | SaveSetupScriptRequest
    | SaveSetupScriptResponse
    | DeleteSetupScriptRequest
    | DeleteSetupScriptResponse
    | RenameSetupScriptRequest
    | RenameSetupScriptResponse
    | ToolConfigLoaded
    | ToolConfigInitialize
    | ToolConfigSave
    | ToolConfigGetDefinitions
    | ToolConfigDefinitionsResponse
    | ToolConfigStartOAuth
    | ToolConfigStartOAuthResponse
    | ToolConfigRevokeAccess
    | ToolApprovalConfigSetRequestMessage
    | ToolApprovalLevelGet
    | ToolApprovalLevelGetResponse
    | GetStoredMCPServersRequest
    | SetStoredMCPServersRequest
    | GetStoredMCPServersResponse
    | DeleteOAuthSessionRequest
    | GetChatRequestIdeStateRequest
    | GetChatRequestIdeStateResponse
    | GetWorkspaceInfoRequest
    | GetWorkspaceInfoResponse
    | OpenScratchFileRequestMessage
    | GetTerminalSettingsRequest
    | TerminalSettingsResponse
    | UpdateTerminalSettingsRequest
    | CanShowTerminalRequest
    | CanShowTerminalResponse
    | ShowTerminalRequest
    | ShowTerminalResponse
    | GetRemoteAgentStatusMessage
    | RemoteAgentStatusResponseMessage
    | TriggerImportDialogRequestMessage
    | TriggerImportDialogResponseMessage
    | RulesLoadedMessage
    | MemoriesLoadedMessage
    | GetSubscriptionInfoRequest
    | GetSubscriptionInfoResponse
    | GetRulesListMessageResponse
    | ReportRemoteAgentEventMessage
    | ReportAgentChangesAppliedMessage
    | StartRemoteMCPAuth;
