import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { FeatureFlagManager } from "../feature-flags";
import { isNextEditEnabled } from "../next-edit/utils";
import { DisposableService } from "../utils/disposable-service";
import { NextEditSuggestionsPanel } from "../webview-panels/next-edit-suggestions-panel";

export class NextEditSuggestionsWebviewProvider extends DisposableService {
    private webviewView: vscode.WebviewView | undefined = undefined;
    private nextEditWebview: vscode.Disposable | undefined = undefined;

    constructor(
        private readonly _config: AugmentConfigListener,
        private readonly _featureFlagsManager: FeatureFlagManager,
        private readonly onWebviewCreated: (webview: vscode.WebviewView) => NextEditSuggestionsPanel
    ) {
        super();
        this.maybeRegisterWebview();
    }

    private maybeRegisterWebview = () => {
        if (
            !this.webviewView ||
            !isNextEditEnabled(
                this._config.config,
                this._featureFlagsManager.currentFlags.vscodeNextEditMinVersion
            )
        ) {
            return;
        }

        // Dispose any existing next edit webview
        this.nextEditWebview?.dispose();
        this.nextEditWebview = this.onWebviewCreated(this.webviewView);
        this.addDisposable(this.nextEditWebview);
    };

    resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext<unknown>,
        _token: vscode.CancellationToken
    ): void | Thenable<void> {
        this.webviewView = webviewView;
        this.maybeRegisterWebview();
    }
}
