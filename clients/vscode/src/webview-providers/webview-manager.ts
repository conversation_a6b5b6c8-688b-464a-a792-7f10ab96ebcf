import * as vscode from "vscode";

import { logger } from "../init/logging";
import { WebViewMessage } from "../webview-providers/webview-messages";

/**
 * WebviewManager acts as a central hub for managing webview communication.
 * It allows panels to register message handlers and broadcast messages to other panels.
 */
export class WebviewManager {
    private static _instance: WebviewManager | undefined;
    private _messageHandlers: Map<string, (msg: WebViewMessage) => Promise<void>> = new Map();
    private _disposables: Array<vscode.Disposable> = [];

    private constructor() {
        this._disposables.push(
            vscode.workspace.onDidChangeWorkspaceFolders(() => {
                if (!vscode.workspace.workspaceFolders?.length) {
                    this.dispose();
                }
            })
        );
    }

    /**
     * Gets the singleton instance of WebviewManager
     */
    public static getInstance(): WebviewManager {
        if (!WebviewManager._instance) {
            WebviewManager._instance = new WebviewManager();
        }
        return WebviewManager._instance;
    }

    /**
     * Registers a message handler for a specific panel
     * @param messengerId Unique identifier for the panel
     * @param postMsg The postMessage function for the panel
     */
    public registerMessageHandler(
        messengerId: string,
        postMsg: (msg: WebViewMessage) => Promise<void>
    ): void {
        this._messageHandlers.set(messengerId, postMsg);
    }

    /**
     * Unregisters a message handler for a specific panel
     * @param messengerId Unique identifier for the panel
     */
    public unregisterMessageHandler(messengerId: string): void {
        this._messageHandlers.delete(messengerId);
    }

    /**
     * Checks if a panel with the given ID has a registered message handler
     * @param messengerId The panel ID to check
     * @returns True if the panel has a registered handler, false otherwise
     */
    public hasHandler(messengerId: string): boolean {
        return this._messageHandlers.has(messengerId);
    }

    /**
     * Disposes the WebviewManager and cleans up resources
     */
    public dispose(): void {
        // Clear all message handlers
        this._messageHandlers.clear();

        // Dispose all other disposables
        this._disposables.forEach((d: vscode.Disposable) => {
            d.dispose();
        });
        this._disposables = [];

        WebviewManager._instance = undefined;
    }

    /**
     * Send a message to a specific panel by ID
     * @param messengerId The ID of the panel to send the message to
     * @param message The message to send
     */
    public sendMessage(messengerId: string, message: WebViewMessage): void {
        const postMessage = this._messageHandlers.get(messengerId);
        if (postMessage) {
            try {
                void postMessage(message);
            } catch (error) {
                logger.error(`Error sending message to ${messengerId}`);
            }
        }
    }

    /**
     * Broadcast a message to all panels
     * @param message The message to broadcast
     */
    public broadcastMessage(message: WebViewMessage, excludeIds?: string[]): void {
        for (const [id, postMessage] of this._messageHandlers.entries()) {
            if (excludeIds?.includes(id)) {
                continue;
            }
            try {
                void postMessage(message);
            } catch (error) {
                logger.error(
                    `Error broadcasting message to ${this._messageHandlers.size} handlers.`
                );
            }
        }
    }
}

/**
 * Register the WebviewManager for disposal when the extension is deactivated
 * @param context The extension context
 */
export function registerWebviewManager(context: vscode.ExtensionContext): void {
    const webviewManager = WebviewManager.getInstance();

    context.subscriptions.push(
        new vscode.Disposable(() => {
            webviewManager.dispose();
        })
    );
}
