import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { LocalToolType } from "@augment-internal/sidecar-libs/src/tools/tool-types";

export { LocalToolType };

export enum RemoteToolType {
    webSearchV1 = "google_search", // old version
    webSearch = "web-search",
    gitHub = "github-api",
    linear = "linear",
    notion = "notion",
    jira = "jira",
    confluence = "confluence",
    supabase = "supabase",
    glean = "glean",
    mcp = "mcp",
}

// Add a null check to handle cases where LocalToolType might not be properly loaded
const LOCAL_TOOL_TYPES = new Set(LocalToolType ? Object.values(LocalToolType) : []);
export function isKnownLocalToolType(type: string): type is LocalToolType {
    return LOCAL_TOOL_TYPES.has(type as LocalToolType);
}

// Add a null check for RemoteToolType as well
const REMOTE_TOOL_TYPES = new Set(RemoteToolType ? Object.values(RemoteToolType) : []);
export function isKnownRemoteToolType(type: string): type is RemoteToolType {
    return REMOTE_TOOL_TYPES.has(type as RemoteToolType);
}

// Add a null check for SidecarToolType as well
const SIDECAR_TOOL_TYPES = new Set(SidecarToolType ? Object.values(SidecarToolType) : []);
export function isKnownSidecarToolType(type: string): type is SidecarToolType {
    return SIDECAR_TOOL_TYPES.has(type as SidecarToolType);
}

const ALL_TOOL_TYPES = new Set([...LOCAL_TOOL_TYPES, ...REMOTE_TOOL_TYPES, ...SIDECAR_TOOL_TYPES]);
export function isKnownToolType(
    type: string
): type is LocalToolType | RemoteToolType | SidecarToolType {
    return ALL_TOOL_TYPES.has(type as LocalToolType | RemoteToolType | SidecarToolType);
}
