import * as vscode from "vscode";

import { SlashFixCommand } from "../commands/slash-command";

export class <PERSON><PERSON><PERSON><PERSON>CommandProvider implements vscode.CodeActionProvider {
    public provideCodeActions(
        document: vscode.TextDocument,
        _range: vscode.Range | vscode.Selection,
        context: vscode.CodeActionContext,
        _token: vscode.CancellationToken
    ): Promise<vscode.CodeAction[] | undefined> {
        const diagnostics = context.diagnostics;
        if (diagnostics.length === 0) {
            return Promise.resolve(undefined);
        }

        const codeAction = new vscode.CodeAction(
            "Fix using Augment",
            vscode.CodeActionKind.QuickFix
        );
        codeAction.command = {
            command: SlashFixCommand.commandID,
            title: "Fix using Augment",
            arguments: [document.uri, diagnostics],
        };

        return Promise.resolve([codeAction]);
    }

    public resolveCodeAction(
        codeAction: vscode.CodeAction,
        _token: vscode.CancellationToken
    ): vscode.CodeAction {
        return codeAction;
    }
}
