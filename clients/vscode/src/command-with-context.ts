import { AugmentCommand } from "./command-manager";
import { SyncingEnabledTracker } from "./workspace/syncing-enabled-tracker";
import { SyncingEnabledState } from "./workspace/types";

export abstract class AugmentCommandWithContext extends AugmentCommand {
    constructor(
        private _syncingEnabledTracker: SyncingEnabledTracker,
        title: string | (() => string) | undefined = undefined,
        showInActionPanel: boolean = true
    ) {
        super(title, showInActionPanel);
    }

    protected _syncingEnabled(): boolean {
        return (
            this._syncingEnabledTracker.syncingEnabledState === SyncingEnabledState.enabled ||
            this._syncingEnabledTracker.syncingEnabledState === SyncingEnabledState.partial
        );
    }

    public canRun(): boolean {
        return this._syncingEnabled();
    }
}
