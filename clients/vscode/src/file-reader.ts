import { readFileRaw, statFileSync } from "./utils/fs-utils";
import { StatInfo } from "./utils/types";

/**
 * FileReader is an interface for reading files.
 */
export type FileReader = {
    read: (absPath: string) => Promise<Uint8Array | undefined>;
    stat: (absPath: string) => StatInfo | undefined;
};

/**
 * FileReaderImpl is a class that provides file reading functionality and reports timing statistics.
 */
class FileReaderImpl implements FileReader {
    // `read` returns the contents of the given file if it exists. Otherwise it
    // returns undefined. The path is interpreted relative to this.root.
    public async read(absPath: string): Promise<Uint8Array | undefined> {
        try {
            // Read the file contents here instead of just returning the Promise
            // from readFile. This ensures that any exceptions encountered during
            // the read will be caught by this try-block.
            return await readFileRaw(absPath);
        } catch (e: any) {
            return undefined;
        }
    }

    public stat(absPath: string): StatInfo | undefined {
        try {
            const st = statFileSync(absPath);
            return st;
        } catch {
            return undefined;
        }
    }
}

export function newFileReader(): FileReader {
    return new FileReaderImpl();
}
