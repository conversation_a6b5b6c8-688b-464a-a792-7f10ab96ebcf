import { AugmentCommand, CommandType } from "../command-manager";
import { type FeatureFlagManager } from "../feature-flags";

/**
 * Command to start the extension CPU profile.
 * This command is only available when the vscodeEnableCpuProfile feature flag is enabled.
 * @extends {AugmentCommand}
 */
export class StartExtensionCPUProfileCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.cpu-profile.start";
    public static isProfileRunning = false;

    type = CommandType.debug;

    /**
     * @param {FeatureFlagManager} _flagManager - The feature flag manager to check if CPU profiling is enabled.
     */
    constructor(private _flagManager: FeatureFlagManager) {
        super("Start Extension CPU Profile");
    }

    run() {
        if (
            !this._flagManager.currentFlags.vscodeEnableCpuProfile ||
            StartExtensionCPUProfileCommand.isProfileRunning
        ) {
            return;
        }

        StartExtensionCPUProfileCommand.markAsRunning();
        // eslint-disable-next-line no-console
        console.profile();
    }

    canRun(): boolean {
        return (
            this._flagManager.currentFlags.vscodeEnableCpuProfile &&
            !StartExtensionCPUProfileCommand.isProfileRunning
        );
    }

    public static markAsRunning() {
        StartExtensionCPUProfileCommand.isProfileRunning = true;
    }

    public static markAsNotRunning() {
        StartExtensionCPUProfileCommand.isProfileRunning = false;
    }
}

/**
 * Command to end the extension CPU profile.
 * This command is only available when the vscodeEnableCpuProfile feature flag is enabled.
 * @extends {AugmentCommand}
 */
export class StopExtensionCPUProfileCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.cpu-profile.stop";

    type = CommandType.debug;

    /**
     * @param {FeatureFlagManager} _flagManager - The feature flag manager to check if CPU profiling is enabled.
     */
    constructor(private _flagManager: FeatureFlagManager) {
        super("End Extension CPU Profile");
    }

    run() {
        if (StartExtensionCPUProfileCommand.isProfileRunning) {
            // eslint-disable-next-line no-console
            console.profileEnd();
            StartExtensionCPUProfileCommand.markAsNotRunning();
        }
    }

    canRun(): boolean {
        return StartExtensionCPUProfileCommand.isProfileRunning;
    }
}
