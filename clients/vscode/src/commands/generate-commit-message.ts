import vscode from "vscode";

import { GitExtension, Repository } from "../../../../third_party/vscode-git/git";
import { APIServer } from "../augment-api";
import { AugmentCommand, CommandType } from "../command-manager";
import { AugmentExtension } from "../extension";
import { CommitMessagePromptPreparer } from "../utils/commit-message-tools/commit-message-prompt-preparer";
import { isExtensionVersionGte } from "../utils/environment";

export class CommitMessageGenerator extends AugmentCommand {
    // This class makes a call to an endpoint to get it to generate a commit message.
    // After that, we populate the commit message in the SCM view.
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.generateCommitMessage";

    private _apiServer: APIServer;
    private extension: AugmentExtension;
    private commitMessagePromptPreparer: CommitMessagePromptPreparer;

    constructor(extension: AugmentExtension, apiServer: APIServer) {
        super();
        this._apiServer = apiServer;
        this.extension = extension;
        this.commitMessagePromptPreparer = new CommitMessagePromptPreparer();
    }

    canRun(): boolean {
        return isExtensionVersionGte(
            this.extension.featureFlagManager.currentFlags.vscodeGenerateCommitMessageMinVersion
        );
    }

    async run(arg: vscode.SourceControl) {
        await this.generateCommitMessage(arg);
    }

    public async generateCommitMessage(arg: vscode.SourceControl) {
        // Collect required objects for generating the message
        let gitExtension: GitExtension | undefined;
        try {
            gitExtension = vscode.extensions.getExtension<GitExtension>("vscode.git")?.exports;
        } catch (e) {
            void vscode.window.showInformationMessage(
                "Cannot generate commit message: Failed to interact with git extension"
            );
            return;
        }
        const repos: Repository[] | undefined = gitExtension?.getAPI(1)?.repositories;
        const repoUri = arg.rootUri ?? "";
        const repo = repos?.find((e) => e.rootUri.toString() === repoUri.toString());
        if (!repo) {
            void vscode.window.showInformationMessage("Cannot generate commit message: no repo");
            return;
        }
        repo.inputBox.value = "Augment is generating...";
        if (!this.extension.workspaceManager) {
            repo.inputBox.value = "Cannot generate commit message: no workspace";
            return;
        }
        let commitMessagePromptData;
        try {
            commitMessagePromptData =
                await this.commitMessagePromptPreparer.getCommitMessagePromptData(
                    repo.rootUri.fsPath,
                    {
                        diffBudget: 9216,
                        messageBudget: 3072,
                        relevantMessageSubbudget: 1024,
                        diffNoopLineLimit: 5000,
                        onlyUseStagedChanges: false,
                        maxExampleCommitMessages: 3,
                    }
                );
        } catch (e) {
            repo.inputBox.value = "Cannot generate commit message: Failed to generate diff details";
            return;
        }
        if (commitMessagePromptData.diff === "") {
            repo.inputBox.value = "Cannot generate commit message: no diff";
            return;
        }
        const requestId = this._apiServer.createRequestId();

        // Using inputVal hack because the input box is buggy and occasionally swallows text
        let inputVal = "";

        try {
            const response = await this._apiServer.generateCommitMessageStream(
                requestId,
                commitMessagePromptData
            );
            for await (const chunk of response) {
                inputVal += chunk.text;
                repo.inputBox.value = inputVal;
            }
        } catch (error) {
            repo.inputBox.value = "Server error: failed to generate commit message.";
        }
    }
}
