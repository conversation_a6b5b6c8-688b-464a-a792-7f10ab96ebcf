import { env, window } from "vscode";

import { APIServer } from "../augment-api";
import { AugmentCommand, CommandType } from "../command-manager";
import { getLogger } from "../logging";

const logger = getLogger("CopySessionIdPanelCommand");

// Show the history panel
export class CopySessionIdPanelCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.copySessionId";

    type = CommandType.public;

    constructor(private _apiServer: APIServer) {
        super();
    }

    async run() {
        try {
            const sessionId: string = this._apiServer.sessionId;

            await env.clipboard.writeText(sessionId);
            await window.showInformationMessage("Copied session ID to clipboard");
        } catch (e: any) {
            logger.error(`Failed to copy session ID: ${e}`);
        }
    }
}
