import * as vscode from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";
import { FeatureFlagManager } from "../feature-flags";
import { type AugmentGlobalState } from "../utils/context";
import { isExtensionVersionGte } from "../utils/environment";
import { SSHConfigManager } from "../utils/ssh/ssh-config-manager";

/**
 * Opens the Augment SSH config file
 */
export class OpenSSHConfigCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.openSshConfig";
    private sshConfigManager: SSHConfigManager;

    type = CommandType.public;

    private remoteAgentsEnabled(): boolean {
        return isExtensionVersionGte(
            this.featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion ?? ""
        );
    }

    constructor(
        private featureFlagManager: FeatureFlagManager,
        globalState?: AugmentGlobalState
    ) {
        super("Open Augment SSH Config");
        this.sshConfigManager = new SSHConfigManager(globalState);
    }

    canRun(): boolean {
        return this.remoteAgentsEnabled();
    }

    async run() {
        try {
            const fileSystem = this.sshConfigManager.fileSystem;
            const configPath = await fileSystem.getAugmentSshConfigPath();
            await this.sshConfigManager.ensureAugmentConfigExists();

            await fileSystem.openFile(configPath);
        } catch (error) {
            void vscode.window.showErrorMessage(`Error opening SSH config: ${String(error)}`);
        }
    }
}
