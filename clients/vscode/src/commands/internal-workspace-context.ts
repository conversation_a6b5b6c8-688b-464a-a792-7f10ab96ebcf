import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentExtension } from "../extension";
import { Chunk, WorkspaceContextWithBlobNames } from "../workspace/workspace-context";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { DebugCommand } from "./utils/debug-command";

export class InternalWorkspaceContextCommand extends DebugCommand {
    public static readonly commandID = "vscode-augment.showWorkspaceContext";
    private static readonly textDocumentName = "Augment Workspace Context.txt";

    private _documentUri = vscode.Uri.file(InternalWorkspaceContextCommand.textDocumentName).with({
        scheme: "untitled",
    });

    constructor(
        private _extension: AugmentExtension,
        _configListener: AugmentConfigListener
    ) {
        super(_configListener, "Show Internal Context");
    }

    async run() {
        const context = this._formatContext();

        let document = await vscode.workspace.openTextDocument(this._documentUri);
        await vscode.window.showTextDocument(document);
        const range = new vscode.Range(
            new vscode.Position(0, 0),
            new vscode.Position(document.lineCount, 0)
        );
        const edit = new vscode.WorkspaceEdit();
        edit.replace(document.uri, range, context);
        await vscode.workspace.applyEdit(edit);
    }

    private _formatContext(): string {
        const workspaceManager = this._extension.workspaceManager;
        if (workspaceManager === undefined) {
            return "no workspace manager";
        }

        const workspaceContext = workspaceManager.getContextWithBlobNames();

        const title = "Augment workspace context\n";
        const blobs = this._formatBlobs(workspaceManager, workspaceContext);
        const chunks = this._formatChunks(workspaceContext.recentChunks);
        return title + blobs + "\n\n\n" + chunks;
    }

    private _formatBlobs(
        workspaceManager: WorkspaceManager,
        workspaceContext: WorkspaceContextWithBlobNames
    ): string {
        const output = new Array<string>();

        // openFileBlobNames = Set<blobName> containing the blob names of all open files.
        const openFileBlobMap = new Set<string>();
        for (const [_repoRoot, pathMap] of workspaceContext.trackedPaths) {
            for (const [_relPath, blobName] of pathMap) {
                openFileBlobMap.add(blobName);
            }
        }

        // folderPathMap is a map of folderRoot->info about every path in the source folder. The
        // path info is a map of relPath->[blobName, count], where count is the number of paths
        // the blob name is associated with, across all repos.
        //    folderPathMap: Map<folderRoot, Map<relPath, [blobName, count]>>
        const folderPathMap = new Map<string, Map<string, [string, number]>>();
        const unknownBlobs = new Set<string>();

        for (const blobName of workspaceContext.blobNames) {
            const pathInfo = workspaceManager.getAllPathInfo(blobName);
            for (const [folderRoot, _repoRoot, relPath] of pathInfo) {
                let pathMap = folderPathMap.get(folderRoot);
                if (pathMap === undefined) {
                    pathMap = new Map<string, [string, number]>();
                    folderPathMap.set(folderRoot, pathMap);
                }
                pathMap.set(relPath, [blobName, pathInfo.length]);
            }
            if (pathInfo.length === 0 && !openFileBlobMap.has(blobName)) {
                unknownBlobs.add(blobName);
            }
        }

        const recentChunksMap = new Map<string, Map<string, number>>();
        for (const chunk of workspaceContext.recentChunks) {
            const repoRoot = chunk.repoRoot;
            const relPath = chunk.pathName;
            let pathMap = recentChunksMap.get(repoRoot);
            if (pathMap === undefined) {
                pathMap = new Map<string, number>();
                recentChunksMap.set(repoRoot, pathMap);
            }
            pathMap.set(relPath, (pathMap.get(relPath) || 0) + 1);
        }

        // For each repo, add lines of the form blobName -> pathName, sorted by pathName.
        for (const [repoRoot, pathMap] of folderPathMap) {
            const openPathMap =
                workspaceContext.trackedPaths.get(repoRoot) || new Map<string, string>();
            const totalPaths = pathMap.size;
            const openPaths = openPathMap.size;

            const chunksMap = recentChunksMap.get(repoRoot) || new Map<string, number>();
            for (const relPath of chunksMap.keys()) {
                if (openPathMap.has(relPath)) {
                    continue;
                }
                // We have recent chunks for this path but it is not in our map of open paths.
                // This should never happen, so let's call it out if it does.
                output.push(`    ${repoRoot}: ${relPath} has recent chunks but is not open`);
            }

            const title = `Repo root ${repoRoot}: ${totalPaths} paths (${openPaths} open)`;
            output.push("");
            output.push(title);
            output.push("=".repeat(title.length));

            // List the open files.
            const openPathNames = Array.from(openPathMap.keys());
            openPathNames.sort();
            for (const pathName of openPathNames) {
                const blobName = openPathMap.get(pathName)!;
                const recentChunks = chunksMap.get(pathName) || 0;
                output.push(`    ${blobName} -> ${pathName}: open, recent chunks: ${recentChunks}`);
            }
            if (openPathNames.length > 0) {
                output.push("");
            }

            // List the files that are not open.
            const pathNames = Array.from(pathMap.keys());
            pathNames.sort();
            for (const pathName of pathNames) {
                if (openPathMap.has(pathName)) {
                    continue;
                }
                const [blobName, count] = pathMap.get(pathName)!;
                const suffix = count === 1 ? "" : ` (x ${count})`;
                output.push(`    ${blobName} -> ${pathName}${suffix}`);
            }
        }

        if (unknownBlobs.size > 0) {
            for (const blobName of unknownBlobs) {
                output.push(`unknown blob: ${blobName}`);
            }
        }

        return output.join("\n");
    }

    private _formatChunks(chunks: Array<Chunk>): string {
        const output = new Array<string>();

        const title = "Recent chunks";
        output.push(title);
        output.push("=".repeat(title.length));

        if (chunks.length === 0) {
            output.push("    (no recent chunks)");
            return output.join("\n");
        }

        for (let idx = 0; idx < chunks.length; idx++) {
            const chunk = chunks[idx];
            output.push("");
            output.push(`Chunk ${idx + 1} of ${chunks.length}`);
            output.push(`    seq:      ${chunk.seq}`);
            output.push(`    uploaded: ${chunk.uploaded}`);
            output.push(`    repoRoot: ${chunk.repoRoot}`);
            output.push(`    pathName: ${chunk.pathName}`);
            output.push(`    blobName: ${chunk.blobName}`);
            output.push(`    start:    ${chunk.origStart}`);
            output.push(`    length:   ${chunk.origLength}`);
            output.push(`======== chunk begin ========`);
            output.push(chunk.text);
            output.push(`-------- chunk end ----------`);
            output.push("");
        }

        return output.join("\n");
    }
}
