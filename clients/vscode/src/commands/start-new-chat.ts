import { EventEmitter } from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";
import {
    type ChatExtensionMessage,
    chatExtensionMessage,
} from "../main-panel/apps/chat-webview-app";
import { focusAndShowChatPanel } from "./utils/focus-and-show-chat-panel";

// StartNewChat opens a new chat in the Augment panel.
export class StartNewChat extends AugmentCommand {
    public static readonly commandID = "vscode-augment.startNewChat";

    type = CommandType.public;

    constructor(private _chatExtensionEvent: EventEmitter<ChatExtensionMessage>) {
        super();
    }

    async run(mode?: string) {
        await focusAndShowChatPanel("Start chat command");
        this._chatExtensionEvent.fire({
            type: chatExtensionMessage.newThread,
            mode,
        });
    }
}
