import { commands, EventEmitter } from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";
import { FeatureFlagManager } from "../feature-flags";
import { MainPanelApp } from "../webview-providers/webview-messages";
import { FocusAugmentPanel } from "./focus-augment-panel";

// Show the settings in the Augment sidebar panel
export class ShowSidebarChatCommand extends AugmentCommand {
    type = CommandType.public;
    public static readonly commandID = "_vscode-augment.showSidebarChat";

    constructor(
        private _featureFlagManager: FeatureFlagManager,
        private _changeWebviewAppEvent: EventEmitter<MainPanelApp>
    ) {
        super();
    }

    run() {
        this._changeWebviewAppEvent.fire(MainPanelApp.chat);
        void commands.executeCommand(FocusAugmentPanel.commandID);
    }
}
