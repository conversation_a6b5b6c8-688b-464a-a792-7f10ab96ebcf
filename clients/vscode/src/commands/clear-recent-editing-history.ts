import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentCommand, CommandType } from "../command-manager";
import { AugmentExtension } from "../extension";
import { isNextEditEnabled } from "../next-edit/utils";

// Clear file edits
export class ClearFileEdits extends AugmentCommand {
    public static readonly title = "Clear Recent Editing History";
    public static commandID = "vscode-augment.clear-recent-editing-history";
    type = CommandType.public;

    constructor(
        private readonly _extension: AugmentExtension,
        private readonly _configListener: AugmentConfigListener
    ) {
        super(ClearFileEdits.title);
    }

    run() {
        this._extension.clearFileEdits();
    }

    canRun(): boolean {
        return (
            super.canRun() &&
            this._extension.ready &&
            isNextEditEnabled(
                // only available for next edit users
                this._configListener.config,
                this._extension.featureFlagManager.currentFlags.vscodeNextEditMinVersion
            )
        );
    }
}
