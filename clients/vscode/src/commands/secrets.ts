import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentCommand, CommandType } from "../command-manager";
import { getLogger } from "../logging";

const logger = getLogger("SecretsCommands");

export class CreateSecretCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.createSecret";

    type = CommandType.public;

    constructor(
        private _apiServer: APIServer,
        private _configListener: AugmentConfigListener
    ) {
        super("Create Secret");
    }

    canRun(): boolean {
        return this._configListener.config.enableDebugFeatures;
    }

    async run() {
        try {
            // Get secret name
            const name = await vscode.window.showInputBox({
                prompt: "Enter secret name",
                placeHolder: "e.g., github-token, api-key",
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return "Secret name is required";
                    }
                    if (value.length > 100) {
                        return "Secret name must be 100 characters or less";
                    }
                    return null;
                },
            });

            if (!name) {
                return; // User cancelled
            }

            // Get secret value
            const value = await vscode.window.showInputBox({
                prompt: "Enter secret value",
                placeHolder: "Enter the secret value",
                password: true, // Hide the input
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return "Secret value is required";
                    }
                    return null;
                },
            });

            if (!value) {
                return; // User cancelled
            }

            // Create the secret
            const requestId = this._apiServer.createRequestId();
            await this._apiServer.upsertUserSecret(requestId, name.trim(), value);

            await vscode.window.showInformationMessage(`Secret "${name}" created successfully`);
        } catch (error: any) {
            logger.error(`Failed to create secret: ${error}`);
            await vscode.window.showErrorMessage(
                `Failed to create secret: ${error instanceof Error ? error.message : "Unknown error"}`
            );
        }
    }
}

export class ListSecretsCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.listSecrets";

    type = CommandType.public;

    constructor(
        private _apiServer: APIServer,
        private _configListener: AugmentConfigListener
    ) {
        super("List Secrets");
    }

    canRun(): boolean {
        return this._configListener.config.enableDebugFeatures;
    }

    async run() {
        try {
            // List secrets (without values for security)
            const requestId = this._apiServer.createRequestId();
            const result = await this._apiServer.listUserSecrets(requestId, false);

            if (result.secrets.length === 0) {
                await vscode.window.showInformationMessage("No secrets found");
                return;
            }

            // Create quick pick items
            const items = result.secrets.map((secret) => ({
                label: secret.name,
                description: secret.description || "No description",
                detail: `Created: ${new Date(secret.created_at).toLocaleDateString()}, Size: ${
                    secret.value_size_bytes
                } bytes`,
                secret: secret,
            }));

            const selected = await vscode.window.showQuickPick(items, {
                placeHolder: "Select a secret to view details",
                matchOnDescription: true,
                matchOnDetail: true,
            });

            if (selected) {
                const secret = selected.secret;
                const info = [
                    `Name: ${secret.name}`,
                    `Description: ${secret.description || "None"}`,
                    `Created: ${new Date(secret.created_at).toLocaleString()}`,
                    `Updated: ${new Date(secret.updated_at).toLocaleString()}`,
                    `Size: ${secret.value_size_bytes} bytes`,
                    `Version: ${secret.version}`,
                ];

                if (Object.keys(secret.tags).length > 0) {
                    info.push(`Tags: ${JSON.stringify(secret.tags)}`);
                }

                await vscode.window.showInformationMessage(info.join("\n"));
            }
        } catch (error: any) {
            logger.error(`Failed to list secrets: ${error}`);
            await vscode.window.showErrorMessage(
                `Failed to list secrets: ${error instanceof Error ? error.message : "Unknown error"}`
            );
        }
    }
}

export class DeleteSecretCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.deleteSecret";

    type = CommandType.public;

    constructor(
        private _apiServer: APIServer,
        private _configListener: AugmentConfigListener
    ) {
        super("Delete Secret");
    }

    canRun(): boolean {
        return this._configListener.config.enableDebugFeatures;
    }

    async run() {
        try {
            // First, list secrets to choose from
            const requestId = this._apiServer.createRequestId();
            const result = await this._apiServer.listUserSecrets(requestId, false);

            if (result.secrets.length === 0) {
                await vscode.window.showInformationMessage("No secrets found to delete");
                return;
            }

            // Create quick pick items
            const items = result.secrets.map((secret) => ({
                label: secret.name,
                description: secret.description || "No description",
                detail: `Created: ${new Date(secret.created_at).toLocaleDateString()}`,
                secret: secret,
            }));

            const selected = await vscode.window.showQuickPick(items, {
                placeHolder: "Select a secret to delete",
                matchOnDescription: true,
            });

            if (!selected) {
                return; // User cancelled
            }

            // Confirm deletion
            const confirmation = await vscode.window.showWarningMessage(
                `Are you sure you want to delete the secret "${selected.secret.name}"? This action cannot be undone.`,
                { modal: true },
                "Delete"
            );

            if (confirmation !== "Delete") {
                return; // User cancelled
            }

            // Delete the secret
            const deleteRequestId = this._apiServer.createRequestId();
            const deleteResult = await this._apiServer.deleteUserSecret(
                deleteRequestId,
                selected.secret.name
            );

            if (deleteResult.deleted) {
                await vscode.window.showInformationMessage(
                    `Secret "${selected.secret.name}" deleted successfully`
                );
            } else {
                await vscode.window.showWarningMessage(
                    `Secret "${selected.secret.name}" was not found or could not be deleted`
                );
            }
        } catch (error: any) {
            logger.error(`Failed to delete secret: ${error}`);
            await vscode.window.showErrorMessage(
                `Failed to delete secret: ${error instanceof Error ? error.message : "Unknown error"}`
            );
        }
    }
}
