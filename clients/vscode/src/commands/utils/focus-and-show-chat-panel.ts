import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import * as vscode from "vscode";
import { window } from "vscode";

import { FocusAugmentPanel } from "../focus-augment-panel";
import { ShowSidebarChatCommand } from "../show-sidebar-chat";

export async function focusAndShowChatPanel(errorContext: string) {
    try {
        await vscode.commands.executeCommand(FocusAugmentPanel.commandID);
        await vscode.commands.executeCommand(ShowSidebarChatCommand.commandID);
    } catch (e) {
        if (e instanceof Error) {
            void window.showErrorMessage(
                `Sorry, Augment ${errorContext} encountered an unexpected error: ${getErrmsg(e)}`
            );
        }
    }
}
