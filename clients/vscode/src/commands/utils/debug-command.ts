import { AugmentConfigListener } from "../../augment-config-listener";
import { AugmentCommand, CommandType } from "../../command-manager";

export abstract class DebugCommand extends AugmentCommand {
    type = CommandType.debug;

    constructor(
        private readonly _config: AugmentConfigListener,
        title?: string,
        showInActionPanel: boolean = true
    ) {
        super(title, showInActionPanel);
    }

    canRun(): boolean {
        return this._config.config.enableDebugFeatures;
    }
}
