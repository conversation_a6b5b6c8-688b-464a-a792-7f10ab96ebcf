import * as vscode from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";

// StatusBarClick focuses on the main Augment panel.
export class StatusBarClick extends AugmentCommand {
    public static readonly commandID = "_vscode-augment.statusbarClick";

    type = CommandType.private;

    constructor() {
        super(undefined, false);
    }

    async run() {
        await vscode.commands.executeCommand("augment-chat.focus");
    }
}
