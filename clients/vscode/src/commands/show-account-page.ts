import { Uri } from "vscode";
import * as vscode from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";

export class ShowAccountPage extends AugmentCommand {
    public static readonly commandID = "vscode-augment.showAccountPage";

    type = CommandType.public;

    async run() {
        await vscode.env.openExternal(Uri.parse("https://app.augmentcode.com/account"));
    }
}
