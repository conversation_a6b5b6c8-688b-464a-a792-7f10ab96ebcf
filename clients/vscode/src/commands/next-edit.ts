import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { CommandType } from "../command-manager";
import { AugmentCommandWithContext } from "../command-with-context";
import { AugmentExtension } from "../extension";
import { NextEditWorkspaceConfigKey } from "../next-edit/next-edit-config-manager";
import { NextEditSessionEventSource } from "../next-edit/next-edit-types";
import { EditSuggestion } from "../next-edit/suggestion-manager";
import { isNextEditEnabled } from "../next-edit/utils";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";

export abstract class AbstractNextEditCommand extends AugmentCommandWithContext {
    type = CommandType.debug; // a default value that may get overridden

    constructor(
        protected readonly _extension: AugmentExtension,
        protected readonly _configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker,
        title: string | (() => string) | undefined,
        showInActionPanel: boolean = true,
        protected readonly _isFileRequired: boolean = false
    ) {
        super(syncingEnabledTracker, title, showInActionPanel);
    }

    protected static _generateHref(
        commandID: string,
        eventSource: NextEditSessionEventSource,
        ...args: (string | undefined)[]
    ) {
        return `command:${commandID}?${encodeURIComponent(JSON.stringify([eventSource, ...args]))}`;
    }

    canRun(): boolean {
        return (
            super.canRun() &&
            this._extension.ready &&
            isNextEditEnabled(
                this._configListener.config,
                this._extension.featureFlagManager.currentFlags.vscodeNextEditMinVersion
            ) &&
            (this._isFileRequired ? vscode.window.activeTextEditor !== undefined : true)
        );
    }

    protected _getEventSource(args: any[]): NextEditSessionEventSource {
        if (args.length === 0) {
            return NextEditSessionEventSource.Command;
        }
        // Check if we were passed an enum value.
        for (const source in NextEditSessionEventSource) {
            // Filter out numeric keys, which is apparently a thing we need to do.
            if (!isNaN(Number(source))) {
                continue;
            }
            // At this point source is the key.  Turn it into the value.
            const value =
                NextEditSessionEventSource[source as keyof typeof NextEditSessionEventSource];
            if (args[0] === value) {
                return value;
            }
        }
        // Empirically, editor action clicks give us something that looks like this.
        if (args.length === 2 && args[0] instanceof vscode.Uri && "groupId" in args[1]) {
            return NextEditSessionEventSource.EditorActionClick;
        }
        // Empirically, right clicks give us this.
        if (args.length === 1 && args[0] instanceof vscode.Uri) {
            return NextEditSessionEventSource.RightClick;
        }
        // Empirically, clicking on things in the panel gives us this.
        if (args.length === 1 && args[0] === undefined) {
            return NextEditSessionEventSource.NextEditPanelItemClick;
        }
        return NextEditSessionEventSource.Command;
    }
}

export class NextEditForceGenerationCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.force";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, undefined, true);
    }

    canRun(): boolean {
        return super.canRun();
    }

    run(...args: any[]) {
        this._extension.forceNextEditSuggestion(this._getEventSource(args));
    }
}

export class NextEditUpdateCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.update";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, true, false);
    }

    run(...args: any[]) {
        this._extension.nextEditUpdate(this._getEventSource(args));
    }
}

export class NextEditUpdateLoadingCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.update.loading";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run() {
        this._extension.noopClicked();
    }
}

export class NextEditUpdateDisabledNoChangesCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.update.disabled-no-changes";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run() {
        this._extension.noopClicked();
    }
}

export class NextEditUpdateDisabledCachedCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.update.disabled-cached";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run() {
        this._extension.noopClicked();
    }
}

export class NextEditBackgroundAcceptCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.accept";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined);
    }

    public static generateHref(
        eventSource: NextEditSessionEventSource,
        hoveredSuggestionId?: string
    ) {
        return super._generateHref(this.commandID, eventSource, hoveredSuggestionId);
    }

    run(...args: any[]) {
        // args[1] is the hovered suggestion id (as seen in generateHref), except
        // in the case of Editor Action clicks, then the value is not string.
        this._extension.editorNextEdit?.accept(
            this._getEventSource(args),
            undefined,
            typeof args[1] === "string" ? args[1] : undefined
        );
    }
}

export class NextEditBackgroundAcceptAllCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.accept-all";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, true);
    }
    run(...args: any[]) {
        this._extension.editorNextEdit?.acceptAllSuggestions(this._getEventSource(args));
    }
}

export class NextEditBackgroundAcceptCodeActionCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.background.accept-code-action";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run(suggestion: EditSuggestion) {
        this._extension.editorNextEdit?.acceptSuggestion(
            suggestion,
            NextEditSessionEventSource.CodeAction
        );
    }
}

export class NextEditBackgroundRejectCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.reject";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined);
    }

    public static generateHref(
        eventSource: NextEditSessionEventSource = NextEditSessionEventSource.HoverClick,
        hoveredSuggestionId?: string
    ) {
        return super._generateHref(this.commandID, eventSource, hoveredSuggestionId);
    }

    run(...args: any[]) {
        // args[1] is the hovered suggestion id (as seen in generateHref), except
        // in the case of Editor Action clicks, then the value is not string.
        this._extension.editorNextEdit?.reject(
            this._getEventSource(args),
            typeof args[1] === "string" ? args[1] : undefined
        );
    }
}

export class NextEditBackgroundRejectAllCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.reject-all";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, true);
    }

    run(...args: any[]) {
        this._extension.editorNextEdit?.rejectAllSuggestions(this._getEventSource(args));
    }
}

export class NextEditBackgroundDismissCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.dismiss";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined);
    }

    run(...args: any[]) {
        this._extension.editorNextEdit?.dismissOrReject(this._getEventSource(args));
    }
}

// FKA NextEditBackgroundGotoHintingCommand
export class NextEditBackgroundGotoNextSmartCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.next";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined);
    }

    run(...args: any[]) {
        this._extension.editorNextEdit?.gotoNextSmart(this._getEventSource(args));
    }
}

export class NextEditBackgroundNextCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.next-forward";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined);
    }

    run(...args: any[]) {
        this._extension.editorNextEdit?.next(this._getEventSource(args));
    }
}

export class NextEditBackgroundPreviousCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.background.previous";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined);
    }

    run(...args: any[]) {
        this._extension.editorNextEdit?.previous(this._getEventSource(args));
    }
}

export class NextEditBackgroundOpenCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.background.open";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run(...args: any[]) {
        const lineInfo = args[0] as { uri: vscode.Uri; lineNumber: number };
        // Subtract 1 to convert the line number back to 0-based.
        this._extension.editorNextEdit?.openSuggestionAt(lineInfo.uri, lineInfo.lineNumber - 1);
    }
}

export class NextEditTogglePanelHorizontalSplitCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.toggle-panel-horizontal-split";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run(...args: any[]) {
        this._extension.nextEditTogglePanelHorizontalSplit(this._getEventSource(args));
    }
}

export class NextEditLearnMoreCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.learn-more";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        const showInActionPanel = true;
        super(extension, configListener, syncingEnabledTracker, undefined, showInActionPanel);
    }

    public static generateHref(
        eventSource: NextEditSessionEventSource = NextEditSessionEventSource.HoverClick,
        hoveredSuggestionId?: string
    ) {
        return super._generateHref(this.commandID, eventSource, hoveredSuggestionId);
    }

    run(...args: any[]) {
        this._extension.nextEditLearnMore(this._getEventSource(args));
    }
}

export class NextEditBackgroundNextDisabledCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.background.next-forward.disabled";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run() {
        this._extension.noopClicked();
    }
}

export class NextEditBackgroundPreviousDisabledCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.background.previous.disabled";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    run() {
        this._extension.noopClicked();
    }
}

export class NextEditOpenPanelCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.open-panel";

    constructor(
        private extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            syncingEnabledTracker,
            undefined,
            extension.nextEditConfigManager.config.enablePanel
        );
    }

    public get showInActionPanel() {
        return this.extension.nextEditConfigManager.config.enablePanel;
    }

    public static generateHref(
        _eventSource: NextEditSessionEventSource = NextEditSessionEventSource.HoverClick,
        _hoveredSuggestionId?: string
    ) {
        // since this is a vscode builtin command we don't pass the args. They are not relevant.
        return "command:augment-next-edit.focus";
    }

    run(...args: any[]) {
        this._extension.openNextEditPanel(this._getEventSource(args));
    }
}

export class NextEditUndoAcceptSuggestionCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.undo-accept-suggestion";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    public static generateHref(
        eventSource: NextEditSessionEventSource = NextEditSessionEventSource.HoverClick,
        hoveredSuggestionId?: string
    ) {
        return super._generateHref(this.commandID, eventSource, hoveredSuggestionId);
    }

    run(...args: any[]) {
        this._extension.editorNextEdit?.undoAcceptSuggestion(
            undefined /** suggestion */,
            this._getEventSource(args)
        );
    }
}

export class NextEditToggleHoverDiffCommand extends AbstractNextEditCommand {
    type = CommandType.private;
    public static readonly commandID = "_vscode-augment.next-edit.toggle-hover-diff";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, undefined, false);
    }

    public static generateHref(
        eventSource: NextEditSessionEventSource = NextEditSessionEventSource.HoverClick,
        hoveredSuggestionId?: string
    ) {
        return super._generateHref(this.commandID, eventSource, hoveredSuggestionId);
    }

    run(...args: any[]) {
        // args[1] is the hovered suggestion id (as seen in generateHref)
        this._extension.editorNextEdit?.toggleHoverDiff(
            this._getEventSource(args),
            typeof args[1] === "string" ? args[1] : undefined
        );
    }
}

interface ToggleBackgroundSuggestionsParams {
    promptFirst?: boolean;
    newValue?: boolean;
}

export class NextEditToggleBackgroundSuggestions extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.toggle-bg";
    public static readonly backgroundSuggestionsConfigKey = "nextEdit.enableBackgroundSuggestions";

    private readonly defaultParams: ToggleBackgroundSuggestionsParams = {
        promptFirst: false,
        newValue: undefined,
    };

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            syncingEnabledTracker,
            () => {
                return configListener.config.nextEdit.enableBackgroundSuggestions
                    ? "Turn Background Suggestions Off"
                    : "Turn Background Suggestions On";
            },
            true
        );
    }

    /**
     *
     * @param promptFirst - prompt the user before disabling.
     * @param newValue - allow to explicitly set the value. Use for cases like links on the hover.
     */
    async run(overrides?: Partial<ToggleBackgroundSuggestionsParams>) {
        const params = { ...this.defaultParams, ...overrides };
        const config = vscode.workspace.getConfiguration("augment");

        // Workspace settings will override the user settings.
        // Check if the workspace value is set, and toggle that if so.
        const value = config.inspect(
            NextEditToggleBackgroundSuggestions.backgroundSuggestionsConfigKey
        );
        let target = vscode.ConfigurationTarget.Global;
        if (value?.workspaceValue !== undefined) {
            target = vscode.ConfigurationTarget.Workspace;
        }

        const newValue =
            params.newValue ?? !this._configListener.config.nextEdit.enableBackgroundSuggestions;

        if (params.promptFirst && newValue === false) {
            const result = await vscode.window.showErrorMessage(
                "Are you sure you want to disable Next Edit Suggestions?",
                {
                    modal: true,
                    detail: "You can re-enable them in Settings > Augment > Enable Background Suggestions.",
                },
                "Disable",
                "Go to Settings"
            );
            if (result === "Go to Settings") {
                void vscode.commands.executeCommand(
                    "workbench.action.openSettings",
                    "@ext:augment.vscode-augment nextEdit.enableBackgroundSuggestions"
                );
            }
            if (result !== "Disable") {
                return;
            }
        }

        // TODO use NextEditConfigManager.toggleSetting
        await config.update(
            NextEditToggleBackgroundSuggestions.backgroundSuggestionsConfigKey,
            newValue,
            target
        );
        // We listen for the config change event to enable/disable it.
    }
}

export class NextEditToggleShowAllHighlightsCommand extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.toggle-all-highlights";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(extension, configListener, syncingEnabledTracker, () =>
            this._configListener.config.nextEdit.highlightSuggestionsInTheEditor
                ? "Turn Off All Line Highlights"
                : "Turn On All Line Highlights"
        );
    }

    canRun(): boolean {
        return super.canRun();
    }

    run() {
        void this._extension.nextEditConfigManager.toggleSetting(
            NextEditWorkspaceConfigKey.highlightSuggestionsInTheEditor
        );
    }
}

export class NextEditEnableBackgroundSuggestions extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.enable-bg";
    public static readonly backgroundSuggestionsConfigKey = "nextEdit.enableBackgroundSuggestions";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            syncingEnabledTracker,
            () => "Enable Background Suggestions",
            true
        );
    }

    canRun(): boolean {
        return (
            super.canRun() &&
            this._extension.ready &&
            !this._configListener.config.nextEdit.enableBackgroundSuggestions
        );
    }

    async run() {
        const config = vscode.workspace.getConfiguration("augment");
        if (config.get(NextEditEnableBackgroundSuggestions.backgroundSuggestionsConfigKey)) {
            return;
        }
        const value = config.inspect(
            NextEditEnableBackgroundSuggestions.backgroundSuggestionsConfigKey
        );
        let target = vscode.ConfigurationTarget.Global;
        if (value?.workspaceValue !== undefined) {
            target = vscode.ConfigurationTarget.Workspace;
        }
        await config.update(
            NextEditEnableBackgroundSuggestions.backgroundSuggestionsConfigKey,
            true,
            target
        );
    }
}

export class NextEditDisableBackgroundSuggestions extends AbstractNextEditCommand {
    type = CommandType.public;
    public static readonly commandID = "vscode-augment.next-edit.disable-bg";
    public static readonly backgroundSuggestionsConfigKey = "nextEdit.enableBackgroundSuggestions";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            syncingEnabledTracker,
            () => "Disable Background Suggestions",
            true
        );
    }

    canRun(): boolean {
        return (
            super.canRun() &&
            this._extension.ready &&
            this._configListener.config.nextEdit.enableBackgroundSuggestions
        );
    }

    async run() {
        const config = vscode.workspace.getConfiguration("augment");
        if (!config.get(NextEditEnableBackgroundSuggestions.backgroundSuggestionsConfigKey)) {
            return;
        }
        const value = config.inspect(
            NextEditDisableBackgroundSuggestions.backgroundSuggestionsConfigKey
        );
        let target = vscode.ConfigurationTarget.Global;
        if (value?.workspaceValue !== undefined) {
            target = vscode.ConfigurationTarget.Workspace;
        }
        await config.update(
            NextEditDisableBackgroundSuggestions.backgroundSuggestionsConfigKey,
            false,
            target
        );
    }
}

// Clear GlobalState to help test onboarding, etc.
export class ResetNextEditOnboarding extends AbstractNextEditCommand {
    public static readonly title = "Reset Next Edit Onboarding";
    public static readonly commandID = "_vscode-augment.next-edit.reset-onboarding";
    type = CommandType.debug;

    constructor(
        _extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker,
        private readonly _globalState: AugmentGlobalState
    ) {
        super(_extension, configListener, syncingEnabledTracker, ResetNextEditOnboarding.title);
    }

    canRun(): boolean {
        return this._configListener.config.enableDebugFeatures && super.canRun();
    }

    async run() {
        await this._globalState.update(GlobalContextKey.nextEditSuggestionSeen, undefined);
        await this._globalState.update(GlobalContextKey.nextEditSuggestionAccepted, undefined);
        await this._globalState.update(GlobalContextKey.nextEditKeybindingUsageCount, undefined);
        await this._globalState.update(GlobalContextKey.nextEditUxMigrationStatus, undefined);
    }
}

export class NextEditSettingsCommand extends AbstractNextEditCommand {
    public static readonly commandID = "vscode-augment.next-edit.settings";

    type = CommandType.public;

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            syncingEnabledTracker,
            () => "Enable Background Suggestions",
            false
        );
    }

    run() {
        void vscode.commands.executeCommand(
            "workbench.action.openSettings",
            "@ext:augment.vscode-augment augment.nextEdit"
        );
    }
}
