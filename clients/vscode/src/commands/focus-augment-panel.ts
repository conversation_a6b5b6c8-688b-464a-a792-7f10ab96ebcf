import * as vscode from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";

// FocusAugmentPanel focuses on the main Augment panel.
export class FocusAugmentPanel extends AugmentCommand {
    public static readonly commandID = "vscode-augment.focusAugmentPanel";

    type = CommandType.public;

    constructor() {
        super();
    }

    async run() {
        await vscode.commands.executeCommand("augment-chat.focus");
    }
}
