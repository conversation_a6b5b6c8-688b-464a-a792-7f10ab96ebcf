import { InitialOrientationCaller } from "@augment-internal/sidecar-libs/src/metrics/types";
import * as vscode from "vscode";
import { EventEmitter } from "vscode";

import { APIServer } from "../augment-api";
import {
    ORIENTATION_RUN_COUNT_STORAGE_KEY,
    runInitialOrientationWithProgressSingleton,
} from "../chat/agent-onboarding-orientation";
import { AugmentCommand, CommandType } from "../command-manager";
import { AugmentCommandWithContext } from "../command-with-context";
import { AugmentExtension } from "../extension";
import {
    type ChatExtensionMessage,
    chatExtensionMessage,
} from "../main-panel/apps/chat-webview-app";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";
import { focusAndShowChatPanel } from "./utils/focus-and-show-chat-panel";

export class ResetAgentOnboarding extends AugmentCommand {
    static readonly commandID = "augment.resetAgentOnboarding";
    readonly type = CommandType.debug;

    constructor(
        private _chatExtensionEvent: EventEmitter<ChatExtensionMessage>,
        private _globalState: AugmentGlobalState,
        private _workspaceStorage: vscode.Memento
    ) {
        super("Reset Agent Onboarding", true);
    }

    async run(): Promise<void> {
        await focusAndShowChatPanel("Reset Agent Onboarding");
        this._chatExtensionEvent.fire(chatExtensionMessage.resetAgentOnboarding);
        await this._globalState.update(GlobalContextKey.memoriesFileOpenCount, 0);
        await this._workspaceStorage.update(ORIENTATION_RUN_COUNT_STORAGE_KEY, 0);
    }
}

/**
 * RunAgentInitialOrientation command triggers the agent's initial orientation process.
 * This command analyzes the codebase and creates memories for the agent to use.
 */
export class RunAgentInitialOrientationCommand extends AugmentCommandWithContext {
    public static readonly commandID = "vscode-augment.runAgentInitialOrientation";

    type = CommandType.debug;

    constructor(
        private _extension: AugmentExtension,
        private _apiServer: APIServer,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(syncingEnabledTracker, "Run Agent Initial Orientation", true);
    }

    async run() {
        if (!this._extension.workspaceManager) {
            void vscode.window.showErrorMessage("Workspace manager is not ready");
            return;
        }

        if (!this._extension.agentCheckpointManager) {
            void vscode.window.showErrorMessage("Agent checkpoint manager is not ready");
            return;
        }

        // Run the initial orientation process
        await runInitialOrientationWithProgressSingleton(
            this._apiServer,
            this._extension.workspaceManager,
            this._extension.featureFlagManager,
            this._extension.agentCheckpointManager,
            InitialOrientationCaller.command
        );
    }

    canRun(): boolean {
        return super.canRun() && this._extension.ready;
    }
}
