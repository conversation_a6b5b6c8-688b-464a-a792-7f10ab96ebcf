import { ConfigurationTarget, workspace } from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { CommandType } from "../command-manager";
import { AugmentCommandWithContext } from "../command-with-context";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";

// Toggle the automatic completion setting between true and false, thus turning completions on and off
export class ToggleCompletionsCommand extends AugmentCommandWithContext {
    public static readonly commandID = "vscode-augment.toggleAutomaticCompletionSetting";
    public static readonly autoCompletionsConfigKey = "completions.enableAutomaticCompletions";

    constructor(
        protected readonly _configListener: AugmentConfigListener,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(syncingEnabledTracker, () => {
            return this._configListener.config.completions.enableAutomaticCompletions
                ? "Turn Automatic Completions Off"
                : "Turn Automatic Completions On";
        });
    }

    type = CommandType.public;

    run() {
        const config = workspace.getConfiguration("augment");

        // Workspace settings will override the user settings.
        // Check if the workspace value is set, and toggle that instead if
        // applicable.
        const value = config.inspect(ToggleCompletionsCommand.autoCompletionsConfigKey);
        let target = ConfigurationTarget.Global;
        if (value?.workspaceValue !== undefined) {
            target = ConfigurationTarget.Workspace;
        }
        void config.update(
            ToggleCompletionsCommand.autoCompletionsConfigKey,
            !this._configListener.config.completions.enableAutomaticCompletions,
            target
        );
    }

    canRun(): boolean {
        return super.canRun();
    }
}
