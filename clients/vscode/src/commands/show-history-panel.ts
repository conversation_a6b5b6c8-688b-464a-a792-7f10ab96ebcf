import { Uri } from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentInstruction } from "../code-edit-types";
import { AugmentCommand, CommandType } from "../command-manager";
import { RecentCompletions } from "../completions/recent-completions";
import { WorkTimer } from "../metrics/work-timer";
import { NextEditResultInfo } from "../next-edit/next-edit-types";
import { RecentItems } from "../utils/recent-items";
import { HistoryWebviewPanel } from "../webview-panels/history-panel";

// Show the history panel
export class ShowHistoryPanelCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.showHistoryPanel";

    type = CommandType.public;

    constructor(
        private _extensionUri: Uri,
        private _config: AugmentConfigListener,
        private _apiServer: APIServer,
        private _recentCompletions: RecentCompletions,
        private _recentInstructions: RecentItems<AugmentInstruction>,
        private _recentNextEditResults: RecentItems<NextEditResultInfo>,
        private _workTimer: WorkTimer
    ) {
        super();
    }

    run() {
        HistoryWebviewPanel.createOrShow(
            this._extensionUri,
            this._config,
            this._apiServer,
            this._recentCompletions,
            this._recentInstructions,
            this._recentNextEditResults,
            this._workTimer
        );
    }
}
