import { window, workspace } from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentExtension } from "../extension";
import { DebugCommand } from "./utils/debug-command";

// Open the extension status page
export class ExtensionStatusCommand extends DebugCommand {
    public static readonly commandID = "vscode-augment.extensionStatus";

    constructor(
        private _extension: AugmentExtension,
        configListener: AugmentConfigListener
    ) {
        super(configListener, "Show Extension Status");
    }

    async run() {
        void this._extension.updateStatusTrace();
        const document = await workspace.openTextDocument(AugmentExtension.displayStatusUri);
        await window.showTextDocument(document);
    }
}
