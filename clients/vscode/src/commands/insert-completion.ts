import { CancellationToken, commands, Progress, ProgressLocation, window } from "vscode";

import { CommandType } from "../command-manager";
import { AugmentCommandWithContext } from "../command-with-context";
import {
    onCompletionRequest,
    onCompletionRequestCancelled,
} from "../completions/completion-events";
import { CompletionRequest } from "../completions/completions-model";
import { AugmentExtension } from "../extension";
import { eventAsPromise } from "../utils/events";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";

/**
 * Insert completion does the following:
 * - Adds listeners for completion request events and completion request cancellation events.
 * - Triggers an inline suggestion using the vscode `editor.action.inlineSuggest.trigger` command.
 *     - This requests a completion from the inline completion provider.
 * - Waits for a completion request, cancellation event from the inline provider or a timeout.
 * - Shows a message if the request is cancelled, timed out or empty.
 */

export class InsertCompletionCommand extends AugmentCommandWithContext {
    public static readonly commandID = "vscode-augment.insertCompletion";
    public readonly completionTimeoutMS = 10_000;

    type = CommandType.public;

    constructor(
        private _extension: AugmentExtension,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(syncingEnabledTracker);
    }

    async run() {
        const notificationOptions = {
            location: ProgressLocation.Notification,
            title: "Waiting for completion...",
            cancellable: true,
        };
        let completionResult: ManualCompletionResult | undefined;
        const notificationTask = async (
            _progress: Progress<{ increment: number; message: string }>,
            token: CancellationToken
        ) => {
            completionResult = await this.requestCompletion(token);
        };

        await window.withProgress(notificationOptions, notificationTask);

        if (!completionResult) {
            await window.showWarningMessage("Failed to request a completion");
            return;
        }

        switch (completionResult.resultType) {
            case "timeout":
                await window.showWarningMessage("Failed to request a completion");
                break;
            case "cancelled":
                await window.showInformationMessage("Completion request cancelled");
                break;
            default:
                if (!completionResult.foundCompletion) {
                    await window.showInformationMessage("No completions found");
                }
                break;
        }
    }

    async requestCompletion(token: CancellationToken): Promise<ManualCompletionResult> {
        // Listen for a completion result or cancellation or timeout
        const completionPromise = Promise.race([
            // Wait for a completion
            (async (): Promise<ManualCompletionResult> => {
                const result = await eventAsPromise<CompletionRequest | undefined>(
                    onCompletionRequest
                );
                let foundCompletion = false;
                if (result?.completions && result.completions.length > 0) {
                    foundCompletion = true;
                }
                return {
                    foundCompletion,
                    resultType: "completion",
                };
            })(),

            // If VSCode cancels the request, show a message.
            (async (): Promise<ManualCompletionResult> => {
                await eventAsPromise<void>(onCompletionRequestCancelled);
                return {
                    foundCompletion: false,
                    resultType: "cancelled",
                };
            })(),

            // If the user cancels the request, show a message.
            (async (): Promise<ManualCompletionResult> => {
                await eventAsPromise<any>(token.onCancellationRequested);
                return {
                    foundCompletion: false,
                    resultType: "cancelled",
                };
            })(),

            // If completions take an extremely long time (10s), show a
            // warning message.
            new Promise<ManualCompletionResult>((resolve) => {
                setTimeout(() => {
                    resolve({
                        foundCompletion: false,
                        resultType: "timeout",
                    });
                }, this.completionTimeoutMS);
            }),
        ]);

        // Trigger an inline suggestion
        void commands.executeCommand("editor.action.inlineSuggest.trigger");

        return await completionPromise;
    }

    canRun(): boolean {
        return super.canRun() && this._extension.ready;
    }
}

type ManualCompletionResult = {
    foundCompletion: boolean;
    resultType: "completion" | "cancelled" | "timeout";
};
