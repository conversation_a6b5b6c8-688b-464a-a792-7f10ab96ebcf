import { commands } from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";

// Open the Augment Extension settings page
export class SettingsCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.settings";

    type = CommandType.public;

    run() {
        void commands.executeCommand(
            "workbench.action.openSettings",
            "@ext:augment.vscode-augment"
        );
    }
}

export class KeyboardShortcutsCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.keyboard-shortcuts";

    type = CommandType.public;

    run() {
        void commands.executeCommand(
            "workbench.action.openGlobalKeybindings",
            `@ext:augment.vscode-augment`
        );
    }
}
