import { <PERSON>ri } from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";
import { GuidelinesWatcher } from "../chat/guidelines-watcher";
import { AugmentCommand, CommandType } from "../command-manager";
import { AugmentExtension } from "../extension";
import { SettingsWebviewPanel } from "../webview-panels/settings-webview-panel";

// Show the settings in a standalone panel in the editor area
export class ShowSettingsPanelCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.showSettingsPanel";

    type = CommandType.public;

    constructor(
        private readonly _extensionUri: Uri,
        private readonly _extension: AugmentExtension,
        private readonly _apiServer: APIServer,
        private readonly _config: AugmentConfigListener,
        private readonly _guidelinesWatcher: GuidelinesWatcher,
        private readonly _authSessionStore: AuthSessionStore
    ) {
        super("Show Settings Panel", true);
    }

    run(section?: string) {
        const panel = SettingsWebviewPanel.createOrShow(
            this._extensionUri,
            this._extension,
            this._apiServer,
            this._config,
            this._guidelinesWatcher,
            this._authSessionStore,
            section
        );

        if (section) {
            // If a section is specified and the panel already exists, navigate to it
            panel.navigateToSection(section);
        }
    }
}
