import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { CommandType } from "../command-manager";
import { AugmentCommandWithContext } from "../command-with-context";
import { AugmentExtension } from "../extension";
import {
    type ChatExtensionMessage,
    chatExtensionMessage,
} from "../main-panel/apps/chat-webview-app";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";
import { focusAndShowChatPanel } from "./utils/focus-and-show-chat-panel";

abstract class SlashActionCommand extends AugmentCommandWithContext {
    type = CommandType.public;

    constructor(
        protected readonly _extension: AugmentExtension,
        protected readonly _configListener: AugmentConfigListener,
        protected readonly _chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,
        syncingEnabledTracker: SyncingEnabledTracker,
        title: string | (() => string) | undefined,
        showInActionPanel: boolean
    ) {
        super(syncingEnabledTracker, title, showInActionPanel);
        this._extension = _extension;
        this._configListener = _configListener;
        this._chatExtensionEvent = _chatExtensionEvent;
    }

    canRun(): boolean {
        return this._extension.ready;
    }

    protected updateSelectionToCoverDiagnostics(diagnostics: vscode.Diagnostic[]) {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            let selectionRange: vscode.Range | undefined;

            if (
                diagnostics &&
                !diagnostics.every((diagnostic) => editor.selection.contains(diagnostic.range))
            ) {
                const firstLine = Math.min(
                    ...diagnostics.map((diagnostic) => diagnostic.range.start.line)
                );
                const lastLine = Math.max(
                    ...diagnostics.map((diagnostic) => diagnostic.range.end.line)
                );
                const startPosition = new vscode.Position(firstLine, 0);
                const endPosition = editor.document.lineAt(lastLine).range.end;
                selectionRange = new vscode.Range(startPosition, endPosition);
            } else if (editor.selection.isEmpty) {
                selectionRange = editor.document.lineAt(editor.selection.active.line).range;
            }

            if (selectionRange) {
                editor.selection = new vscode.Selection(selectionRange.start, selectionRange.end);
            }
        }
    }
}

export class SlashFixCommand extends SlashActionCommand {
    public static readonly commandID = "vscode-augment.chat.slash.fix";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            chatExtensionEvent,
            syncingEnabledTracker,
            "Fix using Augment",
            false
        );
    }

    /**
     * Executes the slash fix command to help fix code issues.
     *
     * @param documentUri - Optional URI of the open document. We do not need this parameter. However,
     *                      when the user invokes this command from the context menu, VS Code will pass it,
     *                      so we need to accept it.
     * @param diagnostics - Optional array of diagnostic issues to fix. If provided, we will update the
     *                      editor selection to cover all diagnostic ranges.
     *                      NOTE: This parameter is PURELY for auto-selection. The slash fix command will
     *                      RE-READ the diagnostics from the selection range.
     * @returns A promise that resolves when the command completes.
     *
     * The method:
     * 1. Shows and focuses the chat panel with "Quick Fix" context
     * 2. If diagnostics are provided, updates the editor selection to cover all diagnostic ranges
     * 3. Fires a runSlashFix event to trigger the fix functionality
     */
    async run(_documentUri?: vscode.Uri, diagnostics?: vscode.Diagnostic[]) {
        await focusAndShowChatPanel("Quick Fix");
        if (diagnostics) {
            this.updateSelectionToCoverDiagnostics(diagnostics);
        }
        this._chatExtensionEvent.fire(chatExtensionMessage.runSlashFix);
    }
}

export class SlashExplainCommand extends SlashActionCommand {
    public static readonly commandID = "vscode-augment.chat.slash.explain";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            chatExtensionEvent,
            syncingEnabledTracker,
            "Explain using Augment",
            false
        );
    }

    async run() {
        await focusAndShowChatPanel("Explain");
        this._chatExtensionEvent.fire(chatExtensionMessage.runSlashExplain);
    }
}

export class SlashTestCommand extends SlashActionCommand {
    public static readonly commandID = "vscode-augment.chat.slash.test";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            chatExtensionEvent,
            syncingEnabledTracker,
            "Write test using Augment",
            false
        );
    }

    async run() {
        await focusAndShowChatPanel("Write a Test");
        this._chatExtensionEvent.fire(chatExtensionMessage.runSlashTest);
    }
}

export class SlashDocumentCommand extends SlashActionCommand {
    public static readonly commandID = "vscode-augment.chat.slash.document";

    constructor(
        extension: AugmentExtension,
        configListener: AugmentConfigListener,
        chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(
            extension,
            configListener,
            chatExtensionEvent,
            syncingEnabledTracker,
            "Document",
            true
        );
    }

    async run() {
        await focusAndShowChatPanel("Document");
        this._chatExtensionEvent.fire(chatExtensionMessage.runSlashDocument);
    }
}
