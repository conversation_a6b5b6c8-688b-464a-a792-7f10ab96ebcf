import { AugmentConfigListener } from "../augment-config-listener";
import { AugmentExtension } from "../extension";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";
import { SyncingEnabledState } from "../workspace/types";
import { DebugCommand } from "./utils/debug-command";

// Enable syncing for this workspace
export class EnableSyncing extends DebugCommand {
    public static readonly title = "$(sync) Enable workspace syncing";
    public static commandID = "vscode-augment.enable-workspace-syncing";

    constructor(
        private readonly _syncingEnabledTracker: SyncingEnabledTracker,
        _config: AugmentConfigListener,
        private readonly _extension: AugmentExtension
    ) {
        super(_config, EnableSyncing.title);
    }

    run() {
        if (this._syncingEnabledTracker.syncingEnabledState !== SyncingEnabledState.initializing) {
            this._syncingEnabledTracker.enableSyncing();
        }
    }

    canRun(): boolean {
        const syncingEnabledState = this._syncingEnabledTracker.syncingEnabledState;
        return (
            super.canRun() &&
            this._extension.ready &&
            (syncingEnabledState === SyncingEnabledState.disabled ||
                syncingEnabledState === SyncingEnabledState.partial)
        );
    }
}

// Disable syncing for this workspace
export class DisableSyncing extends DebugCommand {
    public static readonly title = "$(circle-slash) Disable workspace syncing";
    public static commandID = "vscode-augment.disable-workspace-syncing";

    constructor(
        private readonly _syncingStateTracker: SyncingEnabledTracker,
        _config: AugmentConfigListener,
        private readonly _extension: AugmentExtension
    ) {
        super(_config, DisableSyncing.title);
    }

    run() {
        if (this._syncingStateTracker.syncingEnabledState !== SyncingEnabledState.initializing) {
            this._syncingStateTracker.disableSyncing();
        }
    }

    canRun(): boolean {
        const syncingState = this._syncingStateTracker.syncingEnabledState;
        return (
            super.canRun() &&
            this._extension.ready &&
            (syncingState === SyncingEnabledState.enabled ||
                syncingState === SyncingEnabledState.partial)
        );
    }
}
