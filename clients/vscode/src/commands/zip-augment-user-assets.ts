import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import archiver from "archiver";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import * as vscode from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";
import { getLogger } from "../logging";

const logger = getLogger("ZipAugmentUserAssetsCommand");

/**
 * Zips the augment user assets directory and saves it to a temporary location.
 */
export class ZipAugmentUserAssetsCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.zipAugmentUserAssets";

    type = CommandType.public;

    constructor(private readonly _extensionContext: vscode.ExtensionContext) {
        super("Zip Augment User Assets");
    }

    async run() {
        try {
            // Get the storage URI (same logic as AssetManager)
            const storageUri =
                this._extensionContext.storageUri ?? this._extensionContext.globalStorageUri;
            const assetBasePath = joinPath(storageUri.fsPath, "augment-user-assets");

            // Check if the assets directory exists
            const assetsUri = vscode.Uri.file(assetBasePath);
            try {
                await vscode.workspace.fs.stat(assetsUri);
            } catch {
                void vscode.window.showErrorMessage(
                    `Augment user assets directory does not exist: ${assetBasePath}`
                );
                return;
            }

            // Create timestamp for filename
            const now = new Date();
            const timestamp = now.toISOString().replace(/[:.]/g, "-").slice(0, 19); // YYYY-MM-DDTHH-MM-SS
            const zipFileName = `augment-user-assets-${timestamp}.zip`;

            // Create temp directory path
            const tempDir = os.tmpdir();
            const outputPath = path.join(tempDir, zipFileName);

            // Create a ZIP archive using the cross-platform archiver library
            const actualOutputPath = await this.createZipArchive(assetBasePath, outputPath);

            // Show success message
            const message = `Augment user assets zipfile saved to "${actualOutputPath}". Path copied to clipboard.`;
            void vscode.window.showInformationMessage(message);

            // Copy output path to clipboard
            await vscode.env.clipboard.writeText(actualOutputPath);

            logger.info(
                `Successfully zipped augment-user-assets to: ${actualOutputPath}, source: ${assetBasePath}`
            );
        } catch (error) {
            void vscode.window.showErrorMessage(
                `Error creating augment user assets archive: ${String(error)}`
            );
        }
    }

    private async createZipArchive(sourceDir: string, outputPath: string): Promise<string> {
        return new Promise((resolve, reject) => {
            // Create a file to stream archive data to
            const output = fs.createWriteStream(outputPath);
            const archive = archiver("zip", {
                zlib: { level: 9 }, // Sets the compression level
            });

            // Listen for all archive data to be written
            output.on("close", () => {
                logger.info(
                    `Archive created successfully: ${outputPath} (${archive.pointer()} total bytes)`
                );
                resolve(outputPath);
            });

            // Handle warnings (e.g., stat failures and other non-blocking errors)
            archive.on("warning", (err: Error & { code?: string }) => {
                if (err.code === "ENOENT") {
                    logger.warn(`Archive warning: ${err.message}`);
                } else {
                    reject(err);
                }
            });

            // Handle errors
            archive.on("error", (err: Error) => {
                reject(new Error(`Failed to create archive: ${err.message}`));
            });

            // Pipe archive data to the file
            archive.pipe(output);

            // Add the entire directory to the archive
            archive.directory(sourceDir, path.basename(sourceDir));

            // Finalize the archive (this is the end of the stream)
            void archive.finalize();
        });
    }
}
