import { AuthSessionStore } from "../auth/auth-session-store";
import { OAuthFlow } from "../auth/oauth-flow";
import { AugmentCommand, CommandType } from "../command-manager";

// Sign In the user via OAuth flow
export class AuthCommand extends AugmentCommand {
    public static readonly signInCommandID = "vscode-augment.signIn";
    public static readonly signOutCommandID = "vscode-augment.signOut";

    type = CommandType.public;

    constructor(
        private _auth: AuthSessionStore,
        private _oauthFlow: OAuthFlow,
        commandID: string,
        title: string
    ) {
        super(title);
        this._commandID = commandID;
    }

    async run() {
        if (this.commandID === AuthCommand.signInCommandID) {
            await this._oauthFlow.startFlow();
        } else {
            await this._auth.removeSession();
        }
    }

    canRun() {
        if (!this._auth.useOAuth) {
            return false;
        }

        if (this.commandID === AuthCommand.signOutCommandID) {
            return this._auth.isLoggedIn === true;
        } else {
            return !this._auth.isLoggedIn;
        }
    }
}
