import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { GuidelinesWatcher } from "../chat/guidelines-watcher";
import { AugmentCommand, CommandType } from "../command-manager";
import { AugmentCommandWithContext } from "../command-with-context";
import { VSCodeDiffViewDocument } from "../diff-view/document-vscode";
import { AugmentExtension } from "../extension";
import { FeatureFlags } from "../feature-flags";
import { isNotebookUri } from "../utils/notebook";
import { dirNameUri } from "../utils/path-utils";
import { DiffViewPanelOptions, DiffViewWebviewPanel } from "../webview-panels/diff-view-panel";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";

type IUriArg = vscode.Uri | string | undefined | null;
type INewCodeArg = string | undefined;

/**
 * A command that opens the Augment custom diff view panel with some initial data
 *
 * Takes in a vscode.Uri or a string path, and optionally a string of new code
 */
export class OpenDiffViewCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.internal-dv.o";

    type = CommandType.public;

    constructor(
        private _extension: AugmentExtension,
        private _extensionUri: vscode.Uri,
        private _apiServer: APIServer
    ) {
        super();
    }

    run = async (...args: [IUriArg, INewCodeArg, DiffViewPanelOptions | undefined]) => {
        if (!this._extension.workspaceManager) {
            throw new Error("No workspace manager");
        }
        if (!this._extension.keybindingWatcher) {
            throw new Error("No keybinding watcher");
        }

        const [uriArg, newCodeArg, options] = args;

        const uri = parseUriArg(uriArg);
        if (uri === undefined) {
            return;
        } else if (uri === null) {
            return;
        }

        const dirName = dirNameUri(uri);
        const relPath = uri.fsPath.replace(dirName.fsPath, "");
        const qualifiedPathName = new QualifiedPathName(dirName.fsPath, relPath);

        DiffViewWebviewPanel.createOrShow(
            {
                extensionUri: this._extensionUri,
                workspaceManager: this._extension.workspaceManager,
                apiServer: this._apiServer,
                keybindingWatcher: this._extension.keybindingWatcher,
                fuzzyFsSearcher: this._extension.fuzzyFsSearcher,
                fuzzySymbolSearcher: this._extension.fuzzySymbolSearcher,
                workTimer: this._extension.workTimer,
            },
            {
                document: await VSCodeDiffViewDocument.fromPathName(qualifiedPathName, newCodeArg),
                ...options,
            }
        );
    };

    canRun(): boolean {
        return true;
    }
}

export class StartCodeInstructionCommand extends AugmentCommandWithContext {
    public static readonly commandID = "vscode-augment.internal-dv.i";

    type = CommandType.public;

    constructor(
        private _extension: AugmentExtension,
        private _extensionUri: vscode.Uri,
        private _apiServer: APIServer,
        private _guidelinesWatcher: GuidelinesWatcher,
        syncingEnabledTracker: SyncingEnabledTracker
    ) {
        super(syncingEnabledTracker);
    }

    run = async (...args: [IUriArg, INewCodeArg, DiffViewPanelOptions | undefined]) => {
        if (!this._extension.workspaceManager) {
            throw new Error("No workspace manager");
        }
        if (!this._extension.keybindingWatcher) {
            throw new Error("No keybinding watcher");
        }

        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            void vscode.window.showInformationMessage("No active editor.");
            return;
        }

        if (isNotebookUri(activeEditor.document.uri)) {
            void vscode.window.showInformationMessage(
                "Code instructions are not supported in notebooks."
            );
            return;
        }

        const [uriArg, newCodeArg, originalOptions] = args;

        const uri = parseUriArg(uriArg);
        if (uri === undefined) {
            return;
        } else if (uri === null) {
            return;
        }

        const dirName = dirNameUri(uri);
        const relPath = uri.fsPath.replace(dirName.fsPath, "");
        const qualifiedPathName = new QualifiedPathName(dirName.fsPath, relPath);

        const document = await VSCodeDiffViewDocument.fromPathName(qualifiedPathName, newCodeArg);
        const options: DiffViewPanelOptions = {
            ...originalOptions,
            document,
            guidelinesWatcher: this._guidelinesWatcher,
        };
        const selection = activeEditor.selection;
        options.instruction = {
            selection: {
                start: {
                    line: selection.start.line,
                    character: selection.start.character,
                },
                end: {
                    line: selection.end.line,
                    character: selection.end.character,
                },
            },
        };

        DiffViewWebviewPanel.createOrShow(
            {
                extensionUri: this._extensionUri,
                workspaceManager: this._extension.workspaceManager,
                apiServer: this._apiServer,
                keybindingWatcher: this._extension.keybindingWatcher,
                fuzzyFsSearcher: this._extension.fuzzyFsSearcher,
                fuzzySymbolSearcher: this._extension.fuzzySymbolSearcher,
                workTimer: this._extension.workTimer,
            },
            options
        );
    };

    canRun(): boolean {
        const f: FeatureFlags = this._extension.featureFlagManager.currentFlags;
        const activeEditor = vscode.window.activeTextEditor;
        return !!(
            f.enableInstructions &&
            activeEditor &&
            !isNotebookUri(activeEditor.document.uri)
        );
    }
}

export class AcceptAllChunksDiffViewCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.internal-dv.aac";

    type = CommandType.public;

    constructor() {
        super();
    }

    run() {
        if (DiffViewWebviewPanel.currentPanel) {
            DiffViewWebviewPanel.controller?.diffViewMessageHandler.acceptAllChunks();
        }
    }

    canRun(): boolean {
        // TODO: only run this when a diff view is active
        return true;
    }
}

export class AcceptFocusedChunkDiffViewCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.internal-dv.afc";

    type = CommandType.public;

    constructor() {
        super();
    }

    run() {
        if (DiffViewWebviewPanel.currentPanel) {
            DiffViewWebviewPanel.controller?.diffViewMessageHandler.acceptFocusedChunk();
        }
    }

    canRun(): boolean {
        // TODO: only run this when a diff view is active
        return true;
    }
}

export class RejectFocusedChunkDiffViewCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.internal-dv.rfc";

    type = CommandType.public;

    constructor() {
        super();
    }

    run() {
        if (DiffViewWebviewPanel.currentPanel) {
            DiffViewWebviewPanel.controller?.diffViewMessageHandler.rejectFocusedChunk();
        }
    }

    canRun(): boolean {
        // TODO: only run this when a diff view is active
        return true;
    }
}

export class FocusPreviousChunkDiffViewCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.internal-dv.fpc";

    type = CommandType.public;

    constructor() {
        super();
    }

    run() {
        if (DiffViewWebviewPanel.currentPanel) {
            DiffViewWebviewPanel.controller?.diffViewMessageHandler.focusPreviousChunk();
        }
    }

    canRun(): boolean {
        // TODO: only run this when a diff view is active
        return true;
    }
}

export class FocusNextChunkDiffViewCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.internal-dv.fnc";

    type = CommandType.public;

    constructor() {
        super();
    }

    run() {
        if (DiffViewWebviewPanel.currentPanel) {
            DiffViewWebviewPanel.controller?.diffViewMessageHandler.focusNextChunk();
        }
    }

    canRun(): boolean {
        // TODO: only run this when a diff view is active
        return true;
    }
}

/**
 * A command that closes the Augment custom diff view panel if it's open
 */
export class CloseDiffViewCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.internal-dv.c";

    type = CommandType.public;

    constructor() {
        super();
    }

    run() {
        if (DiffViewWebviewPanel.currentPanel) {
            DiffViewWebviewPanel.currentPanel.dispose();
        }
    }

    canRun(): boolean {
        return true;
    }
}

/**
 * A helper function to parse out the fsPath from a vscode.Uri or a string path
 *
 * @param uri: A vscode.Uri or a string path
 * @returns: The fsPath of the uri, or undefined if the uri is undefined
 */
function parseUriArg(uri: IUriArg): vscode.Uri | undefined | null {
    if (uri === undefined) {
        return vscode.window.activeTextEditor?.document.uri;
    } else if (uri === null) {
        return null;
    } else if (uri instanceof vscode.Uri) {
        return uri;
    } else {
        // Already a string
        return vscode.Uri.parse(uri);
    }
}
