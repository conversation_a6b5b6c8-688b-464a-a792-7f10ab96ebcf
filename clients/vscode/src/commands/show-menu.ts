import * as vscode from "vscode";

import { AugmentCommand, CommandManager, CommandType } from "../command-manager";
import { AugmentExtension } from "../extension";

// ShowAugmentCommands produces a quick-pick of available extension commands, and
// runs the command selected by the user, if any.
export class ShowAugmentCommands extends AugmentCommand {
    public static readonly commandID = "vscode-augment.showAugmentCommands";

    type = CommandType.public;

    constructor(
        private _extension: AugmentExtension,
        private _context: vscode.ExtensionContext,
        private _commandManager: CommandManager
    ) {
        super();
    }

    async run() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const allCommands: CommandInfo[] =
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._context.extension.packageJSON?.contributes?.commands;
        if (!allCommands) {
            // This shouldn't actually happen, but computers are monsters,
            // so... ¯\_(ツ)_/¯
            return;
        }
        const commandTitleMap = Object.fromEntries(
            allCommands.map(({ command, title }) => [command, title])
        );
        for (const command of this._commandManager.availableCommands) {
            const title = command.title || commandTitleMap[command.commandID];
            if (!title) {
                continue;
            }
            commandTitleMap[command.commandID] = title;
        }

        const commandKeybindingMap: { [key: string]: string } = {};
        if (this._extension.keybindingWatcher) {
            for (const commandID of Object.keys(commandTitleMap)) {
                const keybinding = this._extension.keybindingWatcher.getKeybindingForCommand(
                    commandID,
                    true
                );
                if (keybinding) {
                    commandKeybindingMap[commandID] = `${keybinding}`;
                }
            }
        }

        const actions = this.getActions(commandTitleMap, commandKeybindingMap);

        const selectedAction = await vscode.window.showQuickPick<ShowActionItem>(actions, {
            title: "Augment Commands",
        });

        if (selectedAction && selectedAction.commandID) {
            void vscode.commands.executeCommand(selectedAction.commandID);
        }
    }

    protected getActions(
        commandTitleMap: {
            [key: string]: string;
        },
        commandKeybindingMap: {
            [key: string]: string;
        }
    ): Promise<Array<ShowActionItem>> {
        const actions: Array<ShowActionItem> = [];

        for (const { name, commands } of this._commandManager.availableCommandGroups) {
            const cmdsWithTitles = commands.filter((cmd) => commandTitleMap[cmd.commandID]);
            if (cmdsWithTitles.length === 0) {
                continue;
            }

            if (actions.length > 0) {
                actions.push({
                    label: name,
                    kind: vscode.QuickPickItemKind.Separator,
                });
            }

            for (const command of cmdsWithTitles) {
                if (!command.showInActionPanel) {
                    continue;
                }
                actions.push({
                    commandID: command.commandID,
                    label: commandTitleMap[command.commandID],
                    description: commandKeybindingMap[command.commandID],
                });
            }
        }
        return Promise.resolve(actions);
    }
}

interface CommandInfo {
    command: string;
    title: string;
}

export interface ShowActionItem extends vscode.QuickPickItem {
    commandID?: string;
}
