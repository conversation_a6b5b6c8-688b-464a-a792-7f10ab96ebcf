import * as vscode from "vscode";

import { AugmentConfigListener } from "./augment-config-listener";
import { getLogger } from "./logging";
import { ActionsModel } from "./main-panel/action-cards/actions-model";
import { DisposableService } from "./utils/disposable-service";
import { SystemStateName, SystemStatus } from "./utils/types";

export const GITHUB_COPILOT_ID = "GitHub.copilot";
export const CODEIUM_ID = "Codeium.codeium";

enum ConflictingExtension {
    githubCopilot = "copilot",
    codeium = "codeium",
}

type ConflictState = {
    extension: ConflictingExtension;
    isConflicting: boolean;
};

/**
 * This class is responsible for detecting and reporting conflicting extensions
 * to the user.
 */
export class ConflictingExtensions extends DisposableService {
    private readonly logger = getLogger("ConflictingExtensions");

    constructor(
        private readonly _config: AugmentConfigListener,
        private readonly _actionsModel: ActionsModel
    ) {
        super();
        this.addDisposable(
            vscode.workspace.onDidChangeConfiguration((e: vscode.ConfigurationChangeEvent) => {
                if (e.affectsConfiguration("github.copilot") || e.affectsConfiguration("codeium")) {
                    void this.checkAndUpdateState();
                }
            })
        );
        this.addDisposable(
            vscode.extensions.onDidChange(() => {
                void this.checkAndUpdateState();
            })
        );
    }

    public checkAndUpdateState(): void {
        const config = this._config.config;
        if (config.conflictingCodingAssistantCheck === false) {
            // Set all states to complete if check is disabled
            this._actionsModel.setSystemStateStatus(
                SystemStateName.disabledGithubCopilot,
                SystemStatus.complete
            );
            this._actionsModel.setSystemStateStatus(
                SystemStateName.disabledCodeium,
                SystemStatus.complete
            );
            return;
        }

        const checks = [
            {
                check: () => ConflictingExtensions._checkGitHubCopilot(),
                stateName: SystemStateName.disabledGithubCopilot,
            },
            {
                check: () => ConflictingExtensions._checkCodeium(),
                stateName: SystemStateName.disabledCodeium,
            },
        ];

        for (const { check, stateName } of checks) {
            const result = check();
            const status = result.isConflicting ? SystemStatus.incomplete : SystemStatus.complete;
            this._actionsModel.setSystemStateStatus(stateName, status);
        }
    }

    private static _checkGitHubCopilot(): ConflictState {
        const copilotDetails = vscode.extensions.getExtension(GITHUB_COPILOT_ID);
        if (!copilotDetails) {
            return { extension: ConflictingExtension.githubCopilot, isConflicting: false };
        }

        const copilotConfig = vscode.workspace.getConfiguration(
            "github.copilot"
        ) as GHCopilotConfig<vscode.WorkspaceConfiguration>;

        const isConflicting = (copilotConfig.enable && copilotConfig.enable["*"]) || false;
        return { extension: ConflictingExtension.githubCopilot, isConflicting };
    }

    public static async disableGitHubCopilot(): Promise<void> {
        try {
            // Try and disable copilot via a setting change.
            const config = vscode.workspace.getConfiguration("github.copilot");
            await config.update(
                "enable",
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
                this.targetForConfig(config.inspect("enable"))
            );
        } catch (e: any) {
            // We were unable to disable GitHub Copilot via settings
            // change, so open the extension search to disable it there.
            void vscode.commands.executeCommand(
                "workbench.extensions.search",
                `@enabled GitHub Copilot`
            );
        }
    }

    private static _checkCodeium(): ConflictState {
        const codeiumDetails = vscode.extensions.getExtension(CODEIUM_ID);
        if (!codeiumDetails) {
            return { extension: ConflictingExtension.codeium, isConflicting: false };
        }
        const codeiumConfig = vscode.workspace.getConfiguration(
            "codeium"
        ) as CodeiumConfig<vscode.WorkspaceConfiguration>;

        const isConflicting =
            (codeiumConfig.enableConfig && codeiumConfig.enableConfig["*"]) || false;
        return {
            extension: ConflictingExtension.codeium,
            isConflicting: isConflicting,
        };
    }

    public static async disableCodeium(): Promise<void> {
        try {
            // Try and disable copilot via a setting change.
            const config = vscode.workspace.getConfiguration("codeium");
            await config.update(
                "enableConfig",
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    "*": false,
                },
                this.targetForConfig(config.inspect("enableConfig"))
            );
        } catch (e: any) {
            // We were unable to disable Codeium via settings
            // change, so open the extension search to disable it there.
            void vscode.commands.executeCommand("workbench.extensions.search", `@enabled Codeium`);
        }
    }

    public static packageName(details: vscode.Extension<any>, defaultName: string): string {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access
        return details.packageJSON.displayName || details.packageJSON.name || defaultName;
    }

    // If the user has a config for the workspace, override the workspace
    // setting. Otherwise, update the global setting.
    private static targetForConfig(
        inspect: ReturnType<vscode.WorkspaceConfiguration["inspect"]>
    ): vscode.ConfigurationTarget {
        if (inspect && inspect.workspaceValue !== undefined) {
            return vscode.ConfigurationTarget.Workspace;
        }
        return vscode.ConfigurationTarget.Global;
    }
}

type GHCopilotConfig<T> = Partial<T> & {
    enable?: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "*"?: boolean;
    };
};

type CodeiumConfig<T> = Partial<T> & {
    enableConfig?: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "*"?: boolean;
    };
};
