#!/usr/bin/env python3
"""Script for incrementing the version of the IntelliJ plugin."""

from __future__ import annotations

import argparse
import os
import re
import sys
import urllib.error
import urllib.request
from dataclasses import dataclass
from functools import total_ordering
from typing import List

import defusedxml.ElementTree as ET

STABLE_CHANNEL = "stable"
BETA_CHANNEL = "beta"


@dataclass
@total_ordering
class Semver:
    """Semver stores a semver version."""

    major: int
    minor: int
    patch: int

    @classmethod
    def _match(cls, s: str) -> re.Match[str] | None:
        pattern = r"^(?:\w+@)?(\d+)\.(\d+)\.(\d+)(-.+)?$"
        return re.match(pattern, s)

    @classmethod
    def is_semver_string(cls, s: str) -> bool:
        return cls._match(s) is not None

    @classmethod
    def from_string(cls, semver_string: str) -> "Semver":
        match = cls._match(semver_string)
        if match:
            major = int(match.group(1))
            minor = int(match.group(2))
            patch = int(match.group(3))
            return Semver(major, minor, patch)
        else:
            raise SystemError(f"Failed to parse version from '{semver_string}'")

    def __str__(self):
        return f"{self.major}.{self.minor}.{self.patch}"

    def __lt__(self, other: "Semver") -> bool:
        if self.major < other.major:
            return True
        elif self.major == other.major:
            if self.minor < other.minor:
                return True
            elif self.minor == other.minor:
                return self.patch < other.patch
        return False

    def __eq__(self, other: "Semver") -> bool:
        return (
            self.major == other.major
            and self.minor == other.minor
            and self.patch == other.patch
        )

    def increment(self, update_type: str) -> "Semver":
        if update_type == "patch":
            return Semver(self.major, self.minor, self.patch + 1)
        elif update_type == "minor":
            return Semver(self.major, self.minor + 1, 0)
        elif update_type == "major":
            return Semver(self.major + 1, 0, 0)
        else:
            raise SystemError(f"Unknown update type: {update_type}")


def get_env_var(key: str) -> str:
    return (os.environ.get(key) or "").strip()


# Helper method to print to stderr.
def info(message, *args, **kwargs) -> None:
    sys.stderr.write(message.format(*args, **kwargs) + "\n")


def parse_xml(xml_string):
    try:
        root = ET.fromstring(xml_string)
    except ET.ParseError as e:
        info(f"Failed to parse XML from Jetbrains Marketplace: {e}")
        return None
    else:
        return root


def parse_jetbrains_marketplace_data(data: str) -> List[Semver]:
    # Parse the html response as XML
    xml_root = parse_xml(data)
    if xml_root is None:
        raise SystemError("Failed to parse plugin data")

    versions: List[Semver] = []
    for category in xml_root.findall("category"):
        for plugin in category.findall("idea-plugin"):
            version = plugin.find("version")
            if version is None or version.text is None:
                info("Found a plugin without a version")
                continue
            versions.append(Semver.from_string(version.text))
    return sorted(versions, reverse=True)


def get_plugin_versions(channel: str) -> List[Semver]:
    # Make network call to jetbrains marketplace
    try:
        url = "https://plugins.jetbrains.com/plugins/list?pluginId=24072"
        if channel == BETA_CHANNEL:
            url += "&channel=beta"
        with urllib.request.urlopen(url) as response:  # nosec
            if response.getcode() != 200:
                raise SystemError(
                    f"Request to lookup plugin data failed: {response.getcode()}"
                )
            response_body = response.read()
    except urllib.error.URLError as e:
        info(f"Request to lookup plugin data failed: {e.reason}")
        raise SystemError(f"Request to lookup plugin data failed: {e.reason}") from e

    return parse_jetbrains_marketplace_data(response_body)


def get_new_version(
    version_override: str | None = None,
    channel: str | None = None,
    stable_release_ref: str | None = None,
) -> Semver:
    # Ensure the environment is set up correctly
    if not channel:
        raise SystemError("RELEASE_CHANNEL is not defined in the current environment")
    if channel not in [STABLE_CHANNEL, BETA_CHANNEL]:
        raise SystemError(f"Release channel must be stable or beta: {channel}")

    if version_override:
        # The version override is a kind of escape hatch should we need
        # to manually update the version.
        info(f"Using version override: {version_override}\n")
        return Semver.from_string(version_override)
    else:
        channel_versions = get_plugin_versions(channel)
        if len(channel_versions) == 0:
            raise SystemError("No versions found from Jetbrains Marketplace")

        latest_version = channel_versions[0]
        info(f"Current IntelliJ {channel} version: {latest_version}")
        if channel == STABLE_CHANNEL:
            # We don't care what the previous stable release was as we
            # are publishing a promotion of a beta build to stable.
            if not stable_release_ref:
                raise SystemError(
                    "STABLE_RELEASE_REF is not defined in the current environment"
                )
            # The stable ref is either a commit SHA or tag.
            # The tag will be a semver string indicating the beta version
            # being publihed to stable.
            # A commit SHA is used when we want to publish an update to existing
            # stable release.
            # For the tag, we use the same version for stable.
            # For the SHA, we can use the latest
            # stable release and increment the patch (0.4.1 -> 0.4.2)
            if Semver.is_semver_string(stable_release_ref):
                s = Semver.from_string(stable_release_ref)
                if s < latest_version:
                    raise SystemError(
                        f"The stable version should be > the previous stable version: current stable ({latest_version}), new stable ({s})"
                    )
                info(f"New IntelliJ {channel} version: {s}")
                return s
            else:
                new_version = latest_version.increment("patch")
                info(f"Using {new_version} as the stable release ref\n")
                return new_version
        else:
            new_version = latest_version.increment("minor")
            info(f"New IntelliJ {channel} version: {new_version}")
            return new_version


def get_stable_release_ref(
    channel: str | None = None,
    stable_release_ref: str | None = None,
) -> str:
    if channel != STABLE_CHANNEL:
        raise SystemError("release channel must be stable")

    # If we ever support automatic stable releases, this if check should be removed.
    if stable_release_ref is None:
        raise SystemError(
            "manual stable releases MUST have a commit SHA or tag provided"
        )

    # If we have a user provided stable ref, so some validation
    # before accepting it.
    if stable_release_ref:
        if Semver.is_semver_string(stable_release_ref):
            parsed_ref = Semver.from_string(stable_release_ref)
            info(f"Using {parsed_ref} as the stable release ref\n")
            return f"intellij@{parsed_ref}-{BETA_CHANNEL}"
        else:
            info(f"Assuming value is a commit SHA: {stable_release_ref}\n")
            return stable_release_ref

    # We are going to promote beta release to stable.
    channel_versions = get_plugin_versions(BETA_CHANNEL)
    if len(channel_versions) > 0:
        version = channel_versions[0]
        info(f"Using {version} as the stable release ref\n")
        return f"intellij@{version}-{BETA_CHANNEL}"

    raise SystemError("Failed to find a suitable beta release to promote to stable")


def main() -> int:
    parser = argparse.ArgumentParser(
        prog="IntelliJ Plugin Version Updater",
        description="Get the next version of the IntelliJ plugin",
    )

    # new_release will print the version for the next new release
    # stable_release_ref will print the version for the previous beta
    # version that should be promoted to stable
    parser.add_argument("command", choices=["new_release", "stable_release_ref"])

    args = parser.parse_args()

    if args.command == "new_release":
        version_override = get_env_var("VERSION_OVERRIDE")
        channel = get_env_var("RELEASE_CHANNEL")
        stable_ref = get_env_var("STABLE_RELEASE_REF")
        print(
            get_new_version(
                version_override,
                channel,
                stable_ref,
            )
        )
        return 0
    elif args.command == "stable_release_ref":
        channel = get_env_var("RELEASE_CHANNEL")
        stable_ref = get_env_var("STABLE_RELEASE_REF")
        print(
            get_stable_release_ref(
                channel,
                stable_ref,
            )
        )
        return 0
    return 0


if __name__ == "__main__":
    raise SystemExit(main())
