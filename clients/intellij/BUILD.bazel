load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "update_versions",
    srcs = [
        "update_versions.py",
    ],
    deps = [
        requirement("defusedxml"),
    ],
)

pytest_test(
    name = "update_versions_test",
    srcs = ["update_versions_test.py"],
    deps = [
        ":update_versions",
    ],
)

# Creates .jar containing classes defined in public_api.proto as well as its dependencies,
# intended for consumption by IntelliJ plugin.
# Use bazel build //clients/intellij:public_api_protos_intellij_deploy.jar to build.
# The _deploy.jar suffix is created automatically by java_binary()
# The output is bazel-bin/clients/intellij/public_api_protos_intellij_deploy.jar
java_binary(
    name = "public_api_protos_intellij",
    create_executable = False,
    visibility = ["//clients/intellij:__subpackages__"],
    runtime_deps = [
        "//services/api_proxy:public_api_java_proto",
    ],
)
