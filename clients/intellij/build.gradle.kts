import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent
import org.jetbrains.changelog.Changelog
import org.jetbrains.changelog.markdownToHTML
import org.jetbrains.intellij.platform.gradle.IntelliJPlatformType
import org.jetbrains.intellij.platform.gradle.TestFrameworkType

fun properties(key: String) = providers.gradleProperty(key)

fun environment(key: String) = providers.environmentVariable(key)

plugins {
  id("java") // Java support
  alias(libs.plugins.kotlin) // Kotlin support
  alias(libs.plugins.gradleIntelliJPlugin) // Gradle IntelliJ Plugin
  alias(libs.plugins.changelog) // Gradle Changelog Plugin
  alias(libs.plugins.qodana) // Gradle Qodana Plugin
  alias(libs.plugins.kover) // Gradle Kover Plugin
  alias(libs.plugins.proto) // Gradle ProtoBuf Plugin
  alias(libs.plugins.retry) // Gradle Test Retry Plugin
  alias(libs.plugins.sentryGradle) // Sentry Gradle Plugin
}

group = properties("pluginGroup").get()
version = environment("PUBLISH_VERSION").getOrElse("1.0.0-snapshot")

// Configure project's dependencies
repositories {
  mavenCentral()
  intellijPlatform {
    defaultRepositories()
  }
}

// to override IDE dependencies https://plugins.jetbrains.com/docs/intellij/plugin-class-loaders.html#overriding-ide-dependencies
configurations.all {
  resolutionStrategy.sortArtifacts(ResolutionStrategy.SortOrder.DEPENDENCY_FIRST)
}

// Dependencies are managed with Gradle version catalog - read more: https://docs.gradle.org/current/userguide/platforms.html#sub:version-catalog
dependencies {
  intellijPlatform {
    create(properties("platformType"), properties("platformVersionForTests"))
    plugins(properties("platformPlugins").map { it.split(',').map(String::trim).filter(String::isNotEmpty) })
    bundledModule("intellij.platform.collaborationTools")
    bundledPlugins(
      // to have terminal classes for compilation
      "com.jetbrains.sh",
      "org.jetbrains.plugins.terminal",
    )
    pluginVerifier()
    zipSigner()

    testFramework(TestFrameworkType.Platform)
  }
  // bundle all the dependencies except for kotlin coroutines
  // since https://plugins.jetbrains.com/docs/intellij/using-kotlin.html#coroutinesLibraries
  implementation(libs.caffeine)
  implementation(libs.grpc)
  implementation(libs.grpcKotlin) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  implementation(libs.gson)
  implementation(libs.ktorCore) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  implementation(libs.ktorCIO) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  implementation(libs.protoKotlin)
  implementation(libs.protoJavaUtil)

  // Sentry dependencies
  implementation(libs.sentry)
  implementation(libs.sentryKotlin)

  // JAR generated by bazel from protos (includes all dependencies)
  // Add as both compile and runtime to ensure IntelliJ recognizes it
  compileOnly(files("libs/bazel-jars/public_api_protos_intellij_deploy.jar"))
  implementation(files("libs/bazel-jars/public_api_protos_intellij_deploy.jar"))

  testImplementation(libs.ktorMock) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  testImplementation(libs.mockk) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  testImplementation(libs.coroutinesTest) {
    exclude(group = "org.jetbrains.kotlinx")
  }
}

// Create separate integration test source set
sourceSets {
  create("integrationTest") {
    kotlin {
      srcDir("src/integrationTest/kotlin")
      compileClasspath += main.get().output + configurations["testCompileClasspath"]
      runtimeClasspath += main.get().output + configurations["testRuntimeClasspath"]
    }
    resources {
      srcDir("src/integrationTest/resources")
    }
  }
}

dependencies {
  // Integration test dependencies using IntelliJ Platform Starter framework
  "integrationTestImplementation"(sourceSets.main.get().output)
  "integrationTestImplementation"("org.junit.jupiter:junit-jupiter:5.10.2")
  "integrationTestImplementation"("org.kodein.di:kodein-di-jvm:7.20.2")
  "integrationTestImplementation"("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")

  // Add IntelliJ Platform Starter framework dependencies manually for integration tests
  "integrationTestImplementation"("com.jetbrains.intellij.tools:ide-starter-squashed:243.23654.117")
  "integrationTestImplementation"("com.jetbrains.intellij.tools:ide-starter-junit5:243.23654.117")
  "integrationTestImplementation"("com.jetbrains.intellij.tools:ide-starter-driver:243.23654.117")
  "integrationTestImplementation"("com.jetbrains.intellij.driver:driver-sdk:243.23654.117")
  "integrationTestImplementation"("com.jetbrains.intellij.tools:ide-metrics-collector:243.23654.117")
  "integrationTestImplementation"("com.jetbrains.intellij.tools:ide-metrics-collector-starter:243.23654.117")
}

protobuf {
  protoc {
    artifact = libs.protoCompiler.get().toString()
  }
  plugins {
    create("grpc") {
      artifact = libs.protocGenJava.get().toString()
    }
    create("grpckt") {
      artifact = libs.protocGenKotlin.get().toString() + ":jdk8@jar"
    }
  }

  // Define the proto source directory
  val sidecarProtoSrcDir = file("../sidecar/node-process/protos")
  val webviewMsgsProtoSrcDir = file("../common/webviews/protos")

  // Create a directory with modified proto files to optimize build speed
  val modifiedProtosDir = layout.buildDirectory.dir("generated/modified-protos").get().asFile

  // Register the task using the proper task class
  tasks.register<PrepareProtoFilesTask>("prepareSidecarProtoFiles") {
    sourceDir = sidecarProtoSrcDir
    outputDir = modifiedProtosDir.resolve("clients/sidecar/node-process/protos")
    dependsOn("cleanProtoFiles")
  }
  tasks.register<PrepareProtoFilesTask>("prepareWebviewMsgsProtoFiles") {
    sourceDir = webviewMsgsProtoSrcDir
    outputDir = modifiedProtosDir.resolve("clients/common/webviews/protos")
    dependsOn("cleanProtoFiles")
  }

  // Add Google protobuf dependencies
  dependencies {
    // This ensures the Google protobuf files are available for import
    protobuf("com.google.protobuf:protobuf-java:${libs.versions.protobufVersion.get()}")
  }

  // Make the protobuf compilation depend on preparing the proto files
  tasks.named("generateProto") {
    dependsOn("prepareProtoFiles", "copyBazelIntellijProtoJar")
  }

  sourceSets {
    main {
      proto {
        // Use the directory with modified proto files
        srcDir(modifiedProtosDir)
      }
    }
  }

  generateProtoTasks {
    all().forEach {
      it.plugins {
        create("grpc")
        create("grpckt")
      }
      it.builtins {
        create("kotlin")
      }

      // Set up caching to improve build speed
      it.outputs.cacheIf { true }
    }
  }
}

// Calling bazel in exec can sometimes be flaky, it throws a file not found error.
// `which` seems to be reliable.
val whichBazel =
  providers.exec {
    commandLine("which", "bazel")
  }.standardOutput.asText.map { it.trim() }
val bazelCmd = whichBazel.get()

// Get Bazel bin directory dynamically
val bazelBinDir =
  providers.exec {
    commandLine(bazelCmd, "info", "bazel-bin")
  }.standardOutput.asText.map { it.trim() }

bazelBinDir.get().let { binDir ->
  logger.debug("Bazel bin directory: $binDir")
}

// Bazel proto integration for IntelliJ
tasks.register<Exec>("buildIntellijProtos") {
  description = "Build JAR proto library for IntelliJ using Bazel"
  group = "build"
  workingDir = file("../../")
  commandLine = listOf(bazelCmd, "build", "//clients/intellij:public_api_protos_intellij_deploy.jar")

  outputs.files(
    fileTree(bazelBinDir.map { "$it/clients/intellij" }) {
      include("**/public_api_protos_intellij_deploy.jar")
    },
  )
}

tasks.register<Copy>("copyBazelIntellijProtoJar") {
  description = "Copy API proxy JAR (includes all dependencies)"
  dependsOn("buildIntellijProtos")

  from(bazelBinDir.map { "$it/clients/intellij" }) {
    include("public_api_protos_intellij_deploy.jar")
  }
  into("libs/bazel-jars")

  // Capture the file reference at configuration time
  val jarFile = project.file("libs/bazel-jars/public_api_protos_intellij_deploy.jar")

  doLast {
    // Ensure the copied file has proper permissions
    jarFile.setReadable(true, false)
    jarFile.setWritable(true, false)
    println("Copied public_api_protos_intellij_deploy.jar size: ${jarFile.length()} bytes")
  }
}

kotlin {
  jvmToolchain(21)

  // Add compiler options to treat warnings as errors
  compilerOptions {
    // Treat all warnings as errors
    allWarningsAsErrors.set(true)
  }
}

// Configure Gradle IntelliJ Plugin - read more: https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html
intellijPlatform {
  pluginConfiguration {
    name = properties("pluginName")
    ideaVersion {
      untilBuild = provider { null }
    }
  }

  pluginVerification {
    ides {
      // to test API compatibility agains the lowest supported version
      ide(properties("platformType"), properties("platformSinceVersion"))
      // to test against the JetBrains recommended version used in Marketplace publishing process
      recommended()
    }
  }
}

// Configure Gradle Changelog Plugin - read more: https://github.com/JetBrains/gradle-changelog-plugin
changelog {
  groups.empty()
  repositoryUrl = properties("pluginRepositoryUrl")
}

// Configure Gradle Qodana Plugin - read more: https://github.com/JetBrains/gradle-qodana-plugin
qodana {
  cachePath = provider { file(".qodana").canonicalPath }
  reportPath = provider { file("build/reports/inspections").canonicalPath }
  saveReport = true
  showReport = environment("QODANA_SHOW_REPORT").map { it.toBoolean() }.getOrElse(false)
}

val runIdeLatest by intellijPlatformTesting.runIde.registering {
  type = IntelliJPlatformType.IntellijIdeaCommunity
  version = properties("platformVersionLatest").get()
}

val runRiderLatest by intellijPlatformTesting.runIde.registering {
  type = IntelliJPlatformType.Rider
  version = properties("platformVersionLatest").get()
}

tasks.withType<Test>().configureEach {
  // only retry in CI
  if (System.getenv("CI") != null && name != "integrationTest") {
    retry {
      maxRetries.set(3)
    }
  }
}

tasks {
  wrapper {
    gradleVersion = properties("gradleVersion").get()
  }

  // Task to build webviews for IntelliJ
  register<Exec>("buildWebviews") {
    description = "Build webviews for IntelliJ plugin"
    group = "build"
    workingDir = file("../common/webviews")
    commandLine = listOf("/bin/sh", "-c", "pnpm run build:intellij")

    // Track all source files that might affect the build
    inputs.files(
      fileTree("../common/webviews/src") {
        include("**/*")
      },
    )
    inputs.files(file("../common/webviews/package.json"), file("../common/webviews/vite.config.ts"))

    // Track the output directory
    outputs.dir(file("src/main/resources/webviews"))
  }

  // Task to build sidecar for IntelliJ
  register<Exec>("buildSidecar") {
    description = "Build sidecar for IntelliJ plugin"
    group = "build"
    workingDir = file("../sidecar/node-process")
    commandLine = listOf("/bin/sh", "-c", "pnpm run build:intellij")

    // Track all source files that might affect the build
    inputs.files(
      fileTree("../sidecar/node-process/src") {
        include("**/*")
      },
    )
    inputs.files(
      fileTree("../sidecar/node-process/protos") {
        include("**/*")
      },
    )
    inputs.files(file("../sidecar/node-process/package.json"), file("../sidecar/node-process/tsconfig.json"))
    inputs.files(
      fileTree("../sidecar/libs/src") {
        include("**/*")
      },
    )

    // Track the output directory
    outputs.dir(file("src/main/resources/sidecar"))
  }

  // Task to copy feature vector collector JavaScript
  register<Copy>("copyFeatureVectorCollector") {
    description = "Copy feature vector collector JavaScript to plugin resources"
    group = "build"

    from("../common/feature-vector-collector/src/feature-vector-collector-obfuscated.js")
    into("src/main/resources/feature-vector-collector")
    rename { "feature-vector-collector.js" } // Rename during copy
  }

  // makes the standard streams (err and out) visible at console when running tests
  test {
    testLogging {
      events(TestLogEvent.PASSED, TestLogEvent.SKIPPED, TestLogEvent.FAILED)

      showExceptions = true
      exceptionFormat = TestExceptionFormat.FULL
      showCauses = true
      showStackTraces = true
    }
  }

  // Dedicated integration test task - completely separate from regular tests
  val integrationTestTask =
    register<Test>("integrationTest") {
      description = "Runs integration tests using IntelliJ Platform Starter framework"
      group = "verification"

      // Use integration test source set
      testClassesDirs = sourceSets["integrationTest"].output.classesDirs
      classpath = sourceSets["integrationTest"].runtimeClasspath

      // Configure test execution
      useJUnitPlatform()
      maxHeapSize = "4g"
      jvmArgs("-XX:+UseG1GC", "-XX:MaxGCPauseMillis=200")

      // System properties for integration tests
      systemProperty("path.to.build.plugin", buildPlugin.get().archiveFile.get().asFile.absolutePath)
      systemProperty("junit.jupiter.execution.parallel.enabled", "false")
      systemProperty("junit.jupiter.testinstance.lifecycle.default", "per_class")

      // Configure headless mode (default: true, set -Pheadless=false for GUI debugging)
      val headless = project.findProperty("headless")?.toString()?.toBoolean() ?: false
      systemProperty("java.awt.headless", headless.toString())
      systemProperty("ide.show.tips.on.startup.default.value", "false")

      // Force integration tests to always execute (disable output caching for test execution)
      // This ensures tests run every time to verify current plugin functionality
      outputs.upToDateWhen { false }

      // Dependencies
      dependsOn("buildPlugin")
      mustRunAfter("test") // Ensure integration tests run after unit tests if both are executed

      testLogging {
        events(TestLogEvent.PASSED, TestLogEvent.SKIPPED, TestLogEvent.FAILED)
        showExceptions = true
        exceptionFormat = TestExceptionFormat.FULL
        showCauses = true
        showStackTraces = true
      }
      testLogging {
        // We want to see the output of the tests
        events(TestLogEvent.PASSED, TestLogEvent.SKIPPED, TestLogEvent.FAILED, TestLogEvent.STANDARD_OUT)
        showCauses = true
      }
    }
  integrationTestTask.configure {
    enabled =
      gradle.startParameter.taskNames.any {
        // path is prefixed with ":" e.g. :integrationTest hence the contains check
        // this ensures the task would run if it was explicitly specified
        path.contains(it)
      }
  }
  patchPluginXml {
    sinceBuild = properties("pluginSinceBuild")

    // Extract the <!-- Plugin description --> section from README.md and provide for the plugin's manifest
    pluginDescription =
      providers.fileContents(layout.projectDirectory.file("README.md")).asText.map {
        val start = "<!-- Plugin description -->"
        val end = "<!-- Plugin description end -->"

        with(it.lines()) {
          if (!containsAll(listOf(start, end))) {
            throw GradleException("Plugin description section not found in README.md:\n$start ... $end")
          }
          subList(indexOf(start) + 1, indexOf(end)).joinToString("\n").let(::markdownToHTML)
        }
      }

    val changelog = project.changelog // local variable for configuration cache compatibility
    // Get the latest available change notes from the changelog file or use custom change notes if provided
    changeNotes =
      environment("CHANGE_NOTES").map { customNotes ->
        if (customNotes.isNotEmpty()) {
          // Use custom change notes if provided
          markdownToHTML(customNotes.replace("\\n", "\n").replace("\\r", "\r").replace("\\t", "\t"))
        } else {
          ""
        }
      }.getOrElse(
        environment("PUBLISH_VERSION").map { pluginVersion ->
          with(changelog) {
            renderItem(
              (getOrNull(pluginVersion) ?: getUnreleased())
                .withHeader(false)
                .withEmptySections(false),
              Changelog.OutputType.HTML,
            )
          }
        }.getOrElse(""),
      )
  }

  signPlugin {
    certificateChain = environment("CERTIFICATE_CHAIN")
    privateKey = environment("PRIVATE_KEY")
    password = environment("PRIVATE_KEY_PASSWORD")
  }

  publishPlugin {
    dependsOn("patchChangelog")
    token = environment("PUBLISH_TOKEN")
    channels = listOf(environment("PUBLISH_CHANNEL").getOrElse("default"))
  }
}

// Proto files are built by gradle AND bazel but both builds have different build considerations.
// Bazel assumes all proto imports are from the root of the Augment repo.
// Gradle assumes all proto imports are relative to the root of a srcDir.
// We could tell gradle a root is the root of Augment, but that results in gradle searching every proto file in the
// repo, which dramatically slows down the build.
// The following task copies the sidecar proto files to a temporary directory and modifies the imports to be relative.
// This allows us to use the same proto files for both bazel and gradle builds.

abstract class PrepareProtoFilesTask : DefaultTask() {
  @get:InputDirectory
  var sourceDir: File? = null

  @get:OutputDirectory
  var outputDir: File? = null

  @TaskAction
  fun prepareProtoFiles() {
    // Make the output directory
    outputDir?.mkdirs()
    // Process each proto file
    sourceDir?.listFiles()?.filter { it.extension == "proto" }?.forEach { protoFile ->
      if (sourceDir == null || outputDir == null) return@forEach

      val targetFile = File(outputDir, protoFile.name)
      if (!targetFile.exists() || targetFile.lastModified() < protoFile.lastModified()) {
        // Write the copy of the proto file
        targetFile.writeText(protoFile.readText())
      }
    }
  }
}

// Create a task to clean the proto files directory
abstract class CleanProtoFilesTask : DefaultTask() {
  @get:OutputDirectory
  val outputDir: File = project.layout.buildDirectory.dir("generated/modified-protos").get().asFile

  @TaskAction
  fun clean() {
    // Delete the directory and its contents
    outputDir.deleteRecursively()
    println("Cleaned proto files directory: $outputDir")
    // Recreate the empty directory
    outputDir.mkdirs()
  }
}

tasks.register<CleanProtoFilesTask>("cleanProtoFiles") {
  description = "Clean the proto files output directory"
  group = "build"
}

// Create a parent task that depends on both proto preparation tasks
tasks.register("prepareProtoFiles") {
  description = "Prepare all proto files for compilation"
  group = "build"
  dependsOn("cleanProtoFiles", "prepareSidecarProtoFiles", "prepareWebviewMsgsProtoFiles")
}

tasks.named("processResources") {
  dependsOn("prepareProtoFiles", "buildWebviews", "buildSidecar", "copyFeatureVectorCollector")
}

// Configure Sentry Gradle Plugin
sentry {
  // Generates a source bundle and uploads it to Sentry.
  // keeping this as False as it would require additonal setup in CI
  includeSourceContext = false

  // Disables or enables dependencies metadata reporting for Sentry.
  includeDependenciesReport = true

  // Automatically adds Sentry dependencies to your project.
  autoInstallation {
    enabled = true
  }
}

// Make protobuf extraction tasks depend on JAR creation
tasks.named("extractIncludeProto") {
  dependsOn("copyBazelIntellijProtoJar")
}

tasks.named("extractIncludeTestProto") {
  dependsOn("copyBazelIntellijProtoJar")
}

// Ensure the JAR is available before compilation
tasks.named("compileKotlin") {
  dependsOn("copyBazelIntellijProtoJar")
}

tasks.named("compileJava") {
  dependsOn("copyBazelIntellijProtoJar")
}
