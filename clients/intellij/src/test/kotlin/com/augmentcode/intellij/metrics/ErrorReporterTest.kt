package com.augmentcode.intellij.metrics

import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.openapi.util.Disposer
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.request.HttpRequestData
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ErrorReporterTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    ErrorReporter.getInstance().reset()
  }

  @Test
  fun testSanitizeStackTrace() {
    val testCases =
      listOf(
        TestCase(
          input = "at MyClass.method(Test.java:42) (/home/<USER>/project/Test.java)",
          expected = "at MyClass.method(Test.java:42)",
        ),
        TestCase(
          input = "at MyClass.method(Test.java:42)", // no path to remove
          expected = "at MyClass.method(Test.java:42)",
        ),
        TestCase(
          input = "at MyClass.method(Test.java:42) (/tmp/Test.java)",
          expected = "at MyClass.method(Test.java:42)",
        ),
      )

    testCases.forEach { (input, expected) ->
      assertEquals(
        expected,
        ErrorReporter.sanitizeStackTrace(input),
      )
    }
  }

  @Test
  fun testDisposalFlow() {
    augmentHelpers().forcePluginState(PluginState.ENABLED, augmentHelpers().createGetModelsResponse())

    val errorRequests = mutableListOf<HttpRequestData>()
    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/report-error" -> {
            errorRequests.add(request)
            HttpUtil.respondOK(this)
          }
          else -> throw IllegalStateException("Unexpected request to ${request.url.encodedPath}")
        }
      }
    augmentHelpers().registerMockEngine(mockEngine)

    val errorReporter = ErrorReporter.getInstance()
    errorReporter.reportError("originalRequestId", "sanitizedMessage", "stackTrace", listOf("key" to "value"))

    waitForAssertion({
      assertEquals(1, errorRequests.size)
    }, timeoutMs = 5000)

    // Get a reference to job, dispose of the service and wait for job to stop
    val job = errorReporter.metricsJob
    Disposer.dispose(errorReporter)
    waitForAssertion({
      assertFalse(job!!.isActive)
    })

    // Should not throw
    errorReporter.reportError("originalRequestId", "sanitizedMessage", "stackTrace", listOf("key" to "value"))
  }

  private data class TestCase(
    val input: String,
    val expected: String,
  )
}
