package com.augmentcode.intellij.sidecar

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.advanceTimeBy
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.io.File
import java.nio.file.Path

@RunWith(JUnit4::class)
class SidecarServiceTest : AugmentBasePlatformTestCase() {
  @Test
  fun testGetCommandForSidecarWindows() {
    val tempFile = File.createTempFile("sidecar-test-file", "")
    augmentHelpers().setSystemProperty("os.name", "Windows 10")

    val service = SidecarService.getInstance(project)
    val command =
      service.getCommandForSidecar(
        Path.of("/usr/bin/node"),
        tempFile,
        "sidecar.log",
      )

    assertEquals(
      listOf(
        "/usr/bin/node",
        tempFile.absolutePath,
        "--log-file",
        "sidecar.log",
        "--stdio",
      ),
      command,
    )
  }

  @Test
  fun testGetCommandForSidecarLinux() {
    val tempFile = File.createTempFile("sidecar-test-file", "")
    augmentHelpers().setSystemProperty("os.name", "Linux")

    val service = SidecarService.getInstance(project)
    val command =
      service.getCommandForSidecar(
        Path.of("/usr/bin/node"),
        tempFile,
        "sidecar.log",
      )

    assertEquals(
      listOf(
        "/usr/bin/node",
        tempFile.absolutePath,
        "--log-file",
        "sidecar.log",
        "--stdio",
      ),
      command,
    )
  }

  @Test
  fun testGetCommandForSidecar_useDirectoryWithSpace() {
    val tempFile = File.createTempFile("sidecar-test-file", "")
    augmentHelpers().setSystemProperty("os.name", "Linux")
    augmentHelpers().setSystemProperty("SHELL", "/bin/fish")

    val service = SidecarService.getInstance(project)
    val command =
      service.getCommandForSidecar(
        Path.of("/home/<USER>/.augmentcode/bin/node"),
        tempFile,
        "/home/<USER>/intellij/logs/sidecar.log",
      )

    assertEquals(
      listOf(
        "/home/<USER>/.augmentcode/bin/node",
        tempFile.absolutePath,
        "--log-file",
        "/home/<USER>/intellij/logs/sidecar.log",
        "--stdio",
      ),
      command,
    )
  }

  @Test
  fun testGetCommandForSidecar_singleQuotesInPath() {
    val tempFile = File.createTempFile("sidecar-test-file", "")
    augmentHelpers().setSystemProperty("os.name", "Linux")
    augmentHelpers().setSystemProperty("SHELL", "/bin/fish")

    val service = SidecarService.getInstance(project)
    val command =
      service.getCommandForSidecar(
        Path.of("/home/<USER>' quotes '/.augmentcode/bin/node"),
        tempFile,
        "/home/<USER>' quotes '/intellij/logs/sidecar.log",
      )

    assertEquals(
      listOf(
        "/home/<USER>' quotes '/.augmentcode/bin/node",
        tempFile.absolutePath,
        "--log-file",
        "/home/<USER>' quotes '/intellij/logs/sidecar.log",
        "--stdio",
      ),
      command,
    )
  }

  @OptIn(ExperimentalCoroutinesApi::class)
  @Test
  fun testStartBlockingServerTimeout() =
    kotlinx.coroutines.test.runTest {
      val mockNodeInstallation = mockk<NodeInstallationService>(relaxed = true)
      every { mockNodeInstallation.getNodeBinary() } returns CompletableDeferred(Path.of("/injected/mock/node"))
      application.registerOrReplaceServiceInstance(
        NodeInstallationService::class.java,
        mockNodeInstallation,
        testRootDisposable,
      )

      val mockProcess = mockk<Process>()
      val mockProcessBuilder = mockk<ProcessBuilder>()
      every { mockProcessBuilder.start() } answers {
        advanceTimeBy(SIDECAR_INITIALIZE_TIMEOUT_MS + 1)
        mockProcess
      }

      mockkConstructor(ProcessBuilder::class)
      every {
        anyConstructed<ProcessBuilder>().redirectError(ofType<ProcessBuilder.Redirect>())
      } returns mockProcessBuilder

      val service = SidecarService(project, this)

      try {
        service.startBlockingServer()
        assert(!service.isAvailable())
      } finally {
        Disposer.dispose(service)
      }
    }
}
