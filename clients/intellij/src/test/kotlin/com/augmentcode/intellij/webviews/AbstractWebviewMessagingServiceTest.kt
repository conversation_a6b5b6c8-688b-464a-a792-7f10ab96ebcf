package com.augmentcode.intellij.webviews

import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.rpc.AsyncWrapper
import com.augmentcode.rpc.MainPanelActions
import com.google.protobuf.Any
import com.google.protobuf.Empty
import com.google.protobuf.Message
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.*
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import kotlin.time.Duration.Companion.seconds

@RunWith(JUnit4::class)
class AbstractWebviewMessagingServiceTest : AugmentBasePlatformTestCase() {
  private lateinit var mockMessagingService: MockMessagingService

  override fun setUp() {
    super.setUp()
    mockMessagingService = MockMessagingService(project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
  }

  override fun tearDown() {
    Disposer.dispose(mockMessagingService)
    super.tearDown()
  }

  @Test
  fun testProcessMessageFromWebviewSyncUnknown() =
    runBlocking {
      // Test processing a sync message
      val syncMessage = """{"type": "does-not-exist"}"""
      val deferred = CompletableDeferred<String>()
      mockMessagingService.processMessageFromWebview(syncMessage) { response ->
        deferred.complete(response)
      }

      var exception: Exception? = null
      try {
        withTimeout(1.seconds) {
          deferred.await()
        }
      } catch (e: Exception) {
        exception = e
      }

      // Verify empty response for sync message
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals(0, mockMessagingService.syncRequests.size)
      assertNotNull(exception)
    }

  @Test
  fun testProcessMessageFromWebviewSyncKnown() =
    runBlocking {
      // Test processing a sync message that is correct
      val syncMessage =
        """
        {
          "type": "main-panel-actions",
          "data": ["test"]
        }
        """.trimIndent()

      val deferred = CompletableDeferred<String>()
      mockMessagingService.processMessageFromWebview(syncMessage) { response ->
        deferred.complete(response)
      }

      val response =
        withTimeout(1.seconds) {
          deferred.await()
        }

      // Verify response for sync message
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals(1, mockMessagingService.syncRequests.size)
      assertEquals(Any.pack(MainPanelActions.newBuilder().addData("test").build()), mockMessagingService.syncRequests[0])
      assertEquals("{\"type\":\"empty\"}", response)
    }

  @Test
  fun testProcessMessageFromWebviewAsyncUnknown() =
    runBlocking {
      // Test processing an async message
      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "does-not-exist",
                "data": {
                    "text": "Test message"
                }
            }
        }
        """.trimIndent()

      val deferred = CompletableDeferred<String>()
      mockMessagingService.processMessageFromWebview(asyncMessage) { response ->
        deferred.complete(response)
      }

      val response =
        withTimeout(1.seconds) {
          deferred.await()
        }

      // Verify response for async message
      assertEquals(0, mockMessagingService.syncRequests.size)
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals(
        "{\"requestId\":\"test-123\",\"error\":\"Unable to parse webview message: " +
          "{\\n    \\\"type\\\": \\\"async-wrapper\\\",\\n    \\\"requestId\\\": \\\"test-123\\\",\\n" +
          "    \\\"error\\\": null,\\n    \\\"baseMsg\\\": {\\n        \\\"type\\\": \\\"does-not-exist\\\",\\n" +
          "        \\\"data\\\": {\\n            \\\"text\\\": \\\"Test message\\\"\\n        }\\n    " +
          "}\\n}\",\"type\":\"async-wrapper\"}",
        response,
      )
    }

  @Test
  fun testProcessMessageFromWebviewAsyncKnown() =
    runBlocking {
      // Test processing an async message
      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "main-panel-actions",
                "data": ["test"]
            }
        }
        """.trimIndent()

      val deferred = CompletableDeferred<String>()
      mockMessagingService.processMessageFromWebview(asyncMessage) { response ->
        deferred.complete(response)
      }

      val response =
        withTimeout(1.seconds) {
          deferred.await()
        }

      // Verify response for async message
      assertEquals(0, mockMessagingService.syncRequests.size)
      assertEquals(1, mockMessagingService.asyncRequests.size)
      assertEquals(
        AsyncWrapper.newBuilder().setRequestId(
          "test-123",
        ).setBaseMsg(Any.pack(MainPanelActions.newBuilder().addData("test").build())).build(),
        mockMessagingService.asyncRequests[0],
      )
      assertEquals(
        "{\"requestId\":\"test-123\",\"error\":\"\",\"baseMsg\":{\"type\":\"empty\"},\"type\":\"async-wrapper\"}",
        response,
      )
    }

  @Test
  fun testProcessMessageFromWebviewToSidecarUnhandled() =
    runBlocking {
      // Test processing an async message

      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "destination": "sidecar",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "main-panel-actions",
                "data": ["test"]
            }
        }
        """.trimIndent()

      val deferred = CompletableDeferred<String>()
      mockMessagingService.processMessageFromWebview(asyncMessage) { response ->
        deferred.complete(response)
      }

      val response =
        withTimeout(1.seconds) {
          deferred.await()
        }

      // Verify response for async message
      assertEquals(0, mockMessagingService.syncRequests.size)
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals(
        "{\"requestId\":\"test-123\",\"error\":\"Failed to get a response from the sidecar\",\"type\":\"async-wrapper\"}",
        response,
      )
    }

  @Test
  fun testProcessMessageFromWebviewToSidecarHandled() =
    runBlocking {
      val mockSidecarService = mockk<SidecarService>()
      coEvery { mockSidecarService.sendWebviewMessage(any()) } returns "{}"
      every { mockSidecarService.dispose() } returns Unit

      myFixture.project.registerOrReplaceServiceInstance(
        SidecarService::class.java,
        mockSidecarService,
        testRootDisposable,
      )

      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "destination": "sidecar",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "main-panel-actions",
                "data": ["test"]
            }
        }
        """.trimIndent()

      val deferred = CompletableDeferred<String>()
      mockMessagingService.processMessageFromWebview(asyncMessage) { response ->
        deferred.complete(response)
      }
      val response =
        withTimeout(1.seconds) {
          deferred.await()
        }

      // Verify response for async message
      assertEquals(0, mockMessagingService.syncRequests.size)
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals("{\"type\":\"async-wrapper\",\"requestId\":\"test-123\",\"baseMsg\":{}}", response)
    }
}

class MockMessagingService(project: Project, cs: CoroutineScope) : AbstractWebviewMessagingService(project, cs) {
  // Public lists to store received requests
  val syncRequests = mutableListOf<Any>()
  val asyncRequests = mutableListOf<AsyncWrapper>()

  override suspend fun processSyncMessage(request: Any): Flow<Message> {
    // Store the sync request
    syncRequests.add(request)
    return flowOf(Empty.getDefaultInstance())
  }

  override suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message> {
    // Store the async request
    asyncRequests.add(request)
    return flowOf(
      AsyncWrapper.newBuilder()
        .setRequestId(request.requestId)
        .setBaseMsg(Any.pack(Empty.getDefaultInstance()))
        .build(),
    )
  }

  override fun dispose() {
    syncRequests.clear()
    asyncRequests.clear()
  }
}
