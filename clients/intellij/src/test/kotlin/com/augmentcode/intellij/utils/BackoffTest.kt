package com.augmentcode.intellij.utils

import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class BackoffTest {
  @Test
  fun testDefaultParameters() {
    assertEquals(
      Backoff.exponentialBackoffMS(0),
      1000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(1),
      1000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(2),
      2000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(3),
      4000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(4),
      8000L,
    )
  }

  @Test
  fun testCustomBaseDelay() {
    assertEquals(
      Backoff.exponentialBackoffMS(1, baseDelayMs = 500L),
      500L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(2, baseDelayMs = 500L),
      1000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(3, baseDelayMs = 2000L),
      8000L,
    )
  }

  @Test
  fun testCustomBackoffMultiplier() {
    assertEquals(
      Backoff.exponentialBackoffMS(1, backoffMultiplier = 3.0),
      1000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(2, backoffMultiplier = 3.0),
      3000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(3, backoffMultiplier = 3.0),
      9000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(2, backoffMultiplier = 1.5),
      1500L,
    )
  }

  @Test
  fun testMaxDelayCapping() {
    assertEquals(
      Backoff.exponentialBackoffMS(10, maxDelayMs = 5000L),
      5000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(20, maxDelayMs = 10000L),
      10000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(5, baseDelayMs = 1000L, maxDelayMs = 10000L),
      10000L,
    )
  }

  @Test
  fun testRetryCountZero() {
    assertEquals(
      Backoff.exponentialBackoffMS(0),
      1000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(0, baseDelayMs = 2000L),
      2000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(0, backoffMultiplier = 4.0),
      1000L,
    )
  }

  @Test
  fun testNegativeRetryCount() {
    assertEquals(
      Backoff.exponentialBackoffMS(-1),
      1000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(-2),
      1000L,
    )
  }

  @Test
  fun testLargeRetryCount() {
    assertEquals(
      Backoff.exponentialBackoffMS(100),
      60000L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(50, maxDelayMs = 30000L),
      30000L,
    )
  }

  @Test
  fun testAllCustomParameters() {
    assertEquals(
      Backoff.exponentialBackoffMS(
        retryCount = 4,
        baseDelayMs = 100L,
        maxDelayMs = 5000L,
        backoffMultiplier = 3.0,
      ),
      2700L,
    )

    assertEquals(
      Backoff.exponentialBackoffMS(
        retryCount = 5,
        baseDelayMs = 100L,
        maxDelayMs = 5000L,
        backoffMultiplier = 3.0,
      ),
      5000L,
    )
  }
}
