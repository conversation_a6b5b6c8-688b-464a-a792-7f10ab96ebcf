package com.augmentcode.intellij.sentry

import com.augmentcode.intellij.AugmentBundle
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.IdeaLoggingEvent
import com.intellij.openapi.diagnostic.SubmittedReportInfo
import com.intellij.testFramework.LightPlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.Consumer
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.awt.Component

@RunWith(JUnit4::class)
class SentryErrorReportSubmitterTest : LightPlatformTestCase() {
  private lateinit var sentryService: SentryService
  private lateinit var errorReportSubmitter: SentryErrorReportSubmitter
  private lateinit var component: Component
  private lateinit var consumer: Consumer<SubmittedReportInfo>
  private lateinit var submittedReportInfoSlot: CapturingSlot<SubmittedReportInfo>

  override fun setUp() {
    super.setUp()
    // Mock the SentryService
    sentryService = mockk(relaxed = true)

    // Register the mock SentryService as a service
    ApplicationManager.getApplication().registerOrReplaceServiceInstance(
      SentryService::class.java,
      sentryService,
      testRootDisposable,
    )

    // Create the error report submitter
    errorReportSubmitter = SentryErrorReportSubmitter()

    // Mock the component
    component = mockk()

    // Create a slot to capture the submitted report info
    submittedReportInfoSlot = slot()

    // Mock the consumer
    consumer = mockk()
    every { consumer.consume(capture(submittedReportInfoSlot)) } just Runs
  }

  override fun tearDown() {
    try {
      // Clean up mocks
      clearAllMocks()
      unmockkAll()
    } finally {
      super.tearDown()
    }
  }

  @Test
  fun testGetReportActionText() {
    assertEquals(AugmentBundle.message("error.report.action.text"), errorReportSubmitter.reportActionText)
  }

  @Test
  fun testSubmit_withEvents() {
    // Create a test exception
    val exception = RuntimeException("Test exception")

    // Create a test logging event
    val event = mockk<IdeaLoggingEvent>()
    every { event.throwable } returns exception

    // Call the method to test
    val result = errorReportSubmitter.submit(arrayOf(event), "Additional info", component, consumer)

    // Verify that the exception was captured (with project context derived from parent component)
    verify(exactly = 1) { sentryService.captureException(exception, any()) }

    // Verify that the additional info was added as a breadcrumb (with project context derived from parent component)
    verify(exactly = 1) {
      sentryService.addBreadcrumb(
        message = "User provided additional information: Additional info",
        category = "user.feedback",
        project = any(),
      )
    }

    // Verify that the consumer was called with the correct status
    assertTrue(submittedReportInfoSlot.captured.status == SubmittedReportInfo.SubmissionStatus.NEW_ISSUE)

    // Verify that the method returned true
    assertTrue(result)
  }

  @Test
  fun testSubmit_withNoAdditionalInfo() {
    // Create a test exception
    val exception = RuntimeException("Test exception")

    // Create a test logging event
    val event = mockk<IdeaLoggingEvent>()
    every { event.throwable } returns exception

    // Call the method to test
    val result = errorReportSubmitter.submit(arrayOf(event), null, component, consumer)

    // Verify that the exception was captured (with project context derived from parent component)
    verify(exactly = 1) { sentryService.captureException(exception, any()) }

    // Verify that no breadcrumb was added
    verify(exactly = 0) { sentryService.addBreadcrumb(any(), any(), any(), any()) }

    // Verify that the consumer was called with the correct status
    assertTrue(submittedReportInfoSlot.captured.status == SubmittedReportInfo.SubmissionStatus.NEW_ISSUE)

    // Verify that the method returned true
    assertTrue(result)
  }
}
