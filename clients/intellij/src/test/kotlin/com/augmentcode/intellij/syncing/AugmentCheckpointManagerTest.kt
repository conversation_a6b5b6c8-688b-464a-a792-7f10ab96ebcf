package com.augmentcode.intellij.syncing

import com.augmentcode.api.CheckpointBlobsRequest
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import io.ktor.http.HttpHeaders
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentCheckpointManagerTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  private fun setupMockEngine(expectedCalls: Int = 1): MockEngine {
    var currentCheckpoint = 0
    val checkpointCalls = mutableListOf<String>()

    val mockEngine =
      MockEngine { request ->
        assertEquals("POST", request.method.value)
        assertEquals("/checkpoint-blobs", request.url.encodedPath)
        checkpointCalls.add(request.body.toByteArray().decodeToString())
        assertTrue(checkpointCalls.size <= expectedCalls)

        currentCheckpoint += 1
        respond(
          content = """{"new_checkpoint_id": "checkpoint-$currentCheckpoint"}""",
          status = HttpStatusCode.OK,
          headers = headersOf(HttpHeaders.ContentType, "application/json"),
        )
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    return mockEngine
  }

  @Test
  fun testSingleBlob() =
    runBlocking {
      val mockEngine = setupMockEngine(1)
      val manager = AugmentCheckpointManager()

      manager.refreshCheckpointLoop(setOf("blobname-1"))
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      val checkpoint = manager.getCheckpoint(setOf("blobname-1"))
      assertEquals("checkpoint-1", checkpoint.checkpointId)
      assertTrue(checkpoint.addedBlobs.isEmpty())
      assertTrue(checkpoint.deletedBlobs.isEmpty())
    }

  @Test
  fun testTwoBlobs() =
    runBlocking {
      val mockEngine = setupMockEngine(1)
      val manager = AugmentCheckpointManager()

      manager.refreshCheckpointLoop(setOf("blobname-1"))
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
      manager.getCheckpoint(setOf("blobname-1", "blobname-2"))

      // Second delta is too small to trigger a checkpoint
      manager.refreshCheckpointLoop(setOf("blobname-1", "blobname-2"))
      val checkpoint = manager.getCheckpoint(setOf("blobname-1", "blobname-2"))
      assertEquals("checkpoint-1", checkpoint.checkpointId)
      assertEquals(setOf("blobname-2"), checkpoint.addedBlobs)
      assertTrue(checkpoint.deletedBlobs.isEmpty())
    }

  @Test
  fun testAddThenRemoveBlob() =
    runBlocking {
      val mockEngine = setupMockEngine(1)
      val manager = AugmentCheckpointManager()

      manager.refreshCheckpointLoop(setOf("blobname-1"))
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      val checkpoint = manager.getCheckpoint(emptySet())
      assertEquals("checkpoint-1", checkpoint.checkpointId)
      assertTrue(checkpoint.addedBlobs.isEmpty())
      assertEquals(setOf("blobname-1"), checkpoint.deletedBlobs)
    }

  @Test
  fun testManyBlobs() =
    runBlocking {
      val mockEngine = setupMockEngine(2)
      val manager = AugmentCheckpointManager()

      manager.refreshCheckpointLoop(setOf("blobname-1"))
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      // Add 1001 more blobs
      val manyBlobs = (1..1002).map { "blobname-$it" }.toSet()
      manager.refreshCheckpointLoop(manyBlobs)
      assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      val checkpoint = manager.getCheckpoint(manyBlobs)
      assertEquals("checkpoint-2", checkpoint.checkpointId)
      assertEquals(setOf("blobname-1002"), checkpoint.addedBlobs)
      assertTrue(checkpoint.deletedBlobs.isEmpty())
    }

  @Test
  fun testTooManyBlobs() =
    runBlocking {
      val mockEngine = setupMockEngine(11)
      val manager = AugmentCheckpointManager()

      manager.refreshCheckpointLoop(setOf("blobname-1"))
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      // Add 10001 more blobs
      val manyBlobs = (1..10002).map { "blobname-$it" }.toSet()
      // Run loop 11 times
      (1..10).forEach { manager.refreshCheckpointLoop(manyBlobs) }
      manager.refreshCheckpointLoop(manyBlobs)
      assertEquals(11, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      val checkpoint = manager.getCheckpoint(manyBlobs)
      assertEquals("checkpoint-11", checkpoint.checkpointId)
      assertEquals(setOf("blobname-10002"), checkpoint.addedBlobs)
      assertTrue(checkpoint.deletedBlobs.isEmpty())
    }

  @Test
  fun testApiFailure() =
    runBlocking {
      val mockEngine =
        MockEngine { _ ->
          respond(
            content = "",
            status = HttpStatusCode.InternalServerError,
          )
        }
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val manager = AugmentCheckpointManager()
      manager.refreshCheckpointLoop(setOf("blobname-1"))

      val checkpoint = manager.getCheckpoint(setOf("blobname-1"))
      assertNull(checkpoint.checkpointId)
    }

  @Test
  fun testLargeAddThenRemove() =
    runBlocking {
      val mockEngine = setupMockEngine(3)
      val manager = AugmentCheckpointManager()

      // Add more than max workspace size blobs
      val manyBlobs = (1..2002).map { "blobname-$it" }.toSet()
      manager.refreshCheckpointLoop(manyBlobs)

      // Verify first checkpoint call happened and contained added blobs
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
      val firstRequest = mockEngine.requestHistory.first { it.url.encodedPath == "/checkpoint-blobs" }
      val firstRequestBody = firstRequest.body.toByteArray().decodeToString()
      val firstCheckpointRequest = GsonUtil.createApiGson().fromJson(firstRequestBody, CheckpointBlobsRequest::class.java)
      assertEquals(1000, firstCheckpointRequest.blobs.addedBlobs.size)
      assertTrue(firstCheckpointRequest.blobs.deletedBlobs.isEmpty())

      // Add another 1000 blobs to checkpoint
      manager.refreshCheckpointLoop(manyBlobs)

      // Verify that second checkpoint added another 1000 blobs
      assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
      val secondRequest = mockEngine.requestHistory.last { it.url.encodedPath == "/checkpoint-blobs" }
      val secondRequestBody = secondRequest.body.toByteArray().decodeToString()
      val secondCheckpointRequest = GsonUtil.createApiGson().fromJson(secondRequestBody, CheckpointBlobsRequest::class.java)
      assertEquals(1000, secondCheckpointRequest.blobs.addedBlobs.size)
      assertTrue(secondCheckpointRequest.blobs.deletedBlobs.isEmpty())

      // Remove 1000 blobs
      manager.refreshCheckpointLoop(emptySet())

      // Verify second checkpoint call happened and contained deleted blobs
      assertEquals(3, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
      val thirdRequest = mockEngine.requestHistory.last { it.url.encodedPath == "/checkpoint-blobs" }
      val thirsRequestBody = thirdRequest.body.toByteArray().decodeToString()
      val thirdCheckpointRequest = GsonUtil.createApiGson().fromJson(thirsRequestBody, CheckpointBlobsRequest::class.java)
      assertTrue(thirdCheckpointRequest.blobs.addedBlobs.isEmpty())
      assertEquals(1000, thirdCheckpointRequest.blobs.deletedBlobs.size)
    }

  @Test
  fun testCheckpointNotFound() =
    runBlocking {
      // First set up a normal checkpoint
      val mockEngine = setupMockEngine(2)
      val manager = AugmentCheckpointManager()

      manager.refreshCheckpointLoop(setOf("blobname-1"))
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      // Now simulate a 404 response
      HttpUtil.registerMockHttpClient(
        MockEngine { _ ->
          respond(
            content = "",
            status = HttpStatusCode.NotFound,
          )
        },
        testRootDisposable,
      )

      // Try to update with many blobs to exceed MAX_WORKING_SET_SIZE
      val manyBlobs = (1..1002).map { "blobname-$it" }.toSet()
      manager.refreshCheckpointLoop(manyBlobs)

      // Verify checkpoint was reset
      val checkpoint = manager.getCheckpoint(manyBlobs)
      assertNull(checkpoint.checkpointId)
      // Verify only MAX_WORKING_SET_SIZE (1000) blobs are included
      assertEquals(1000, checkpoint.addedBlobs.size)
      // Verify the blobs are from the original set
      assertTrue(checkpoint.addedBlobs.all { it in manyBlobs })
      assertTrue(checkpoint.deletedBlobs.isEmpty())
    }

  @Test
  fun testCheckpointBadRequest() =
    runBlocking {
      // First set up a normal checkpoint
      val mockEngine = setupMockEngine(2)
      val manager = AugmentCheckpointManager()

      manager.refreshCheckpointLoop(setOf("blobname-1"))
      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      // Now simulate a 400 response
      HttpUtil.registerMockHttpClient(
        MockEngine { _ ->
          respond(
            content = "",
            status = HttpStatusCode.BadRequest,
          )
        },
        testRootDisposable,
      )

      // Try to update with many blobs to exceed MAX_WORKING_SET_SIZE
      val manyBlobs = (1..1002).map { "blobname-$it" }.toSet()
      manager.refreshCheckpointLoop(manyBlobs)

      // Verify checkpoint was reset
      val checkpoint = manager.getCheckpoint(manyBlobs)
      assertNull(checkpoint.checkpointId)
      assertEquals(AugmentCheckpointManager.MAX_WORKING_SET_SIZE, checkpoint.addedBlobs.size)
      assertTrue(checkpoint.addedBlobs.all { it in manyBlobs })
      assertTrue(checkpoint.deletedBlobs.isEmpty())
    }

  @Test
  fun testCheckpointResetAndRecovery() =
    runBlocking {
      var responseCount = 0
      val mockEngine =
        MockEngine { request ->
          assertEquals("POST", request.method.value)
          assertEquals("/checkpoint-blobs", request.url.encodedPath)

          responseCount++
          when (responseCount) {
            1 ->
              respond(
                content = """{"new_checkpoint_id": "checkpoint-1"}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            2 ->
              respond(
                content = "",
                status = HttpStatusCode.NotFound,
              )
            3 ->
              respond(
                content = """{"new_checkpoint_id": "checkpoint-2"}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            else -> error("Unexpected request")
          }
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val manager = AugmentCheckpointManager()

      // Initial checkpoint
      manager.refreshCheckpointLoop(setOf("blobname-1"))
      var checkpoint = manager.getCheckpoint(setOf("blobname-1"))
      assertEquals("checkpoint-1", checkpoint.checkpointId)
      assertTrue(checkpoint.addedBlobs.isEmpty())
      assertTrue(checkpoint.deletedBlobs.isEmpty())

      // Trigger reset with 404
      val manyBlobs = (1..1002).map { "blobname-$it" }.toSet()
      manager.refreshCheckpointLoop(manyBlobs)
      checkpoint = manager.getCheckpoint(manyBlobs)
      assertNull(checkpoint.checkpointId)
      assertEquals(AugmentCheckpointManager.MAX_WORKING_SET_SIZE, checkpoint.addedBlobs.size)
      assertTrue(checkpoint.addedBlobs.all { it in manyBlobs })
      assertTrue(checkpoint.deletedBlobs.isEmpty())

      // Verify recovery with new checkpoint
      manager.refreshCheckpointLoop(manyBlobs)
      checkpoint = manager.getCheckpoint(manyBlobs)
      assertEquals("checkpoint-2", checkpoint.checkpointId)
    }
}
