package com.augmentcode.intellij.chat

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.application.ApplicationManager
import kotlinx.coroutines.*

class EditorTrackingServiceTest : AugmentBasePlatformTestCase() {
  fun testShouldReturnNullWhenNoSelectionTracked() {
    val service = EditorTrackingService.getInstance(project)

    runBlocking {
      assertNull(service.getActiveEditor())
    }
  }

  fun testShouldTrackEditorSelectionThroughSelectionModel() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "line1\nline2\nline3")
    val editor = myFixture.editor

    ApplicationManager.getApplication().runWriteAction {
      editor.selectionModel.setSelection(0, 5)
    }

    runBlocking {
      val trackedEditor = service.getActiveEditor()
      assertNotNull(trackedEditor)
      assertEquals(editor, trackedEditor)
    }
  }

  fun testShouldHandleBackgroundThreadAccess() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "content")
    val editor = myFixture.editor

    ApplicationManager.getApplication().runWriteAction {
      editor.selectionModel.setSelection(0, 5)
    }

    runBlocking {
      val result =
        withContext(Dispatchers.IO) {
          service.getActiveEditor()
        }
      assertNotNull(result)
      assertEquals(editor, result)
    }
  }

  fun testShouldFallbackToSelectedEditorWhenNoTrackedSelection() {
    val service = EditorTrackingService.getInstance(project)
    myFixture.configureByText("test.kt", "content")
    val editor = myFixture.editor

    runBlocking {
      val result = service.getActiveEditor()
      assertEquals(editor, result)
    }
  }
}
