package com.augmentcode.intellij.utils

import com.augmentcode.intellij.index.AugmentEditorHistoryService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentEditorHistoryServiceTest : AugmentBasePlatformTestCase() {
  @Test
  fun testAddOneLine() {
    val commonPrefix = "package main\n\n"
    val replacements =
      AugmentEditorHistoryService.findReplacements(
        commonPrefix +
          """
          fun printFoo() {
          }
          """.trimIndent(),
        commonPrefix +
          """
          fun printFoo() {
            println("foo")
          }
          """.trimIndent(),
      )
    assert(replacements.size == 1)

    // first we remove the whole line
    val replacement = replacements[0]
    assertFalse(replacement.presentInBlob)
    assertEquals("  println(\"foo\")\n", replacement.replacementText)
    assertEquals(commonPrefix.length + "fun printFoo() {\n".length, replacement.charStart)
  }

  @Test
  fun testRemoveOneLine() {
    val commonPrefix = "package main\n\n"
    val replacements =
      AugmentEditorHistoryService.findReplacements(
        commonPrefix +
          """
          fun printFoo() {
            println("foo")
          }
          """.trimIndent(),
        commonPrefix +
          """
          fun printFoo() {
          }
          """.trimIndent(),
      )
    assert(replacements.size == 1)

    // first we remove the whole line
    val replacement = replacements[0]
    assertFalse(replacement.presentInBlob)
    assertEquals("", replacement.replacementText)
    assertEquals(commonPrefix.length + "fun printFoo() {\n".length, replacement.charStart)
    assertEquals(commonPrefix.length + "fun printFoo() {\n".length + "  println(\"foo\")\n".length, replacement.charEnd)
  }

  @Test
  fun testRemoveFront() {
    val commonPrefix = "package main\n\n"
    val replacements =
      AugmentEditorHistoryService.findReplacements(
        commonPrefix +
          """
          fun printFoo() {
          }
          """.trimIndent(),
        """
        fun printFoo() {
        }
        """.trimIndent(),
      )
    assert(replacements.size == 1)

    // first we remove the whole line
    val replacement = replacements[0]
    assertFalse(replacement.presentInBlob)
    assertEquals("", replacement.replacementText)
    assertEquals(0, replacement.charStart)
    assertEquals(commonPrefix.length, replacement.charEnd)
  }

  @Test
  fun testRemoveBack() {
    val content =
      """
      fun printFoo() {
      }

      """.trimIndent()
    val replacements = AugmentEditorHistoryService.findReplacements(content, "")
    assert(replacements.size == 1)

    // first we remove the whole line
    val replacement = replacements[0]
    assertFalse(replacement.presentInBlob)
    assertEquals("", replacement.replacementText)
    assertEquals(0, replacement.charStart)
    assertEquals(content.length, replacement.charEnd)
  }

  @Test
  fun testNewLineAtTheEnd() {
    val content =
      """
      fun printFoo() {
      }
      """.trimIndent()
    val replacements = AugmentEditorHistoryService.findReplacements(content, content + "\n")
    assert(replacements.size == 1)

    // first we remove the whole line
    val replacement = replacements[0]
    assertFalse(replacement.presentInBlob)
    assertEquals("\n", replacement.replacementText)
    assertEquals(content.length, replacement.charStart)
    assertEquals(content.length, replacement.charEnd)
  }

  @Test
  fun testAddThreePlaces() {
    val replacements =
      AugmentEditorHistoryService.findReplacements(
        """
        fun printOne() {
        }
        fun printTwo() {
        }
        fun printThree() {
        }
        """.trimIndent(),
        """
        fun printOne() {
          println("1")
        }
        fun printTwo() {
          println("2")
        }
        fun printThree() {
          println("3")
        }
        """.trimIndent(),
      )
    assert(replacements.size == 3)

    assertFalse(replacements[0].presentInBlob)
    assertEquals("  println(\"1\")\n", replacements[0].replacementText)

    assertFalse(replacements[1].presentInBlob)
    assertEquals("  println(\"2\")\n", replacements[1].replacementText)
    // second edit should be on top of the previous result
    val middleResult =
      """
      fun printOne() {
        println("1")
      }
      fun printTwo() {

      """.trimIndent()
    assertEquals(middleResult.length, replacements[1].charStart)

    assertFalse(replacements[2].presentInBlob)
    assertEquals("  println(\"3\")\n", replacements[2].replacementText)
    // second edit should be on top of the previous result
    val endResult =
      """
      fun printOne() {
        println("1")
      }
      fun printTwo() {
        println("2")
      }
      fun printThree() {

      """.trimIndent()
    assertEquals(endResult.length, replacements[2].charStart)
  }
}
