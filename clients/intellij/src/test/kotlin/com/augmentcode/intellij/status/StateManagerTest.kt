package com.augmentcode.intellij.status

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import junit.framework.TestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class StateManagerTest : AugmentBasePlatformTestCase() {
  @Test
  fun testStateChanges() {
    val sm = StateManager()
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Base)
    TestCase.assertEquals(sm.baseState, StateDefinitions.Base)

    val enableDisposal = sm.setState(StateDefinitions.Enabled)
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Enabled)

    val unauthorizedDisposal = sm.setState(StateDefinitions.Unauthorized)
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Unauthorized)

    enableDisposal()
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Unauthorized)

    unauthorizedDisposal()
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Base)
  }
}
