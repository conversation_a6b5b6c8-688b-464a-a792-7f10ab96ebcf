package com.augmentcode.intellij.testutils

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.HttpClientProvider
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.openapi.Disposable
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.HttpTimeout
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import junit.framework.TestCase.assertEquals
import java.net.URI

/**
 * Utility class for setting up feature flags in tests.
 * This implementation avoids using the deprecated MockAugmentAPI class.
 */
object FeatureFlagsTestUtil {
  /**
   * Sets up feature flags for testing by mocking the API response.
   *
   * @param disposable The disposable to register the mock HTTP client with
   * @param featureFlags A map of feature flag names to their values
   */
  fun setupFeatureFlags(
    disposable: Disposable,
    featureFlags: Map<String, Any>,
  ) {
    // Create a JSON string with the feature flags
    val featureFlagsJson =
      featureFlags.entries.joinToString(
        prefix = "{",
        postfix = "}",
        separator = ",",
      ) { (key, value) ->
        when (value) {
          is String -> "\"$key\": \"$value\""
          is Boolean, is Number -> "\"$key\": $value"
          else -> "\"$key\": \"${value}\""
        }
      }

    // Setup mock HTTP client with feature flags
    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/get-models" -> {
            respond(
              content = """{
              "default_model": "test-model",
              "models": [
                {
                  "name": "test-model",
                  "display_name": "Test Model",
                  "feature_flags": $featureFlagsJson
                }
              ],
              "languages": [
                {
                  "name": "Text",
                  "extensions": [".txt"]
                }
              ],
              "feature_flags": $featureFlagsJson
            }""",
              status = HttpStatusCode.OK,
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }

          else -> {
            respond(
              content = "{}",
              status = HttpStatusCode.OK,
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
        }
      }

    // Register the mock HTTP client
    application.registerOrReplaceServiceInstance(
      HttpClientProvider::class.java,
      object : HttpClientProvider {
        override fun clientFor(uri: URI): HttpClient {
          return HttpClient(mockEngine) {
            install(HttpTimeout)
          }
        }
      },
      disposable,
    )
    // Clear the model cache in AugmentAPI to ensure we get fresh data
    clearAugmentAPICache()

    // Set up test credentials - triggering a AugmentAppStateService update
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"

    // Ensure plugin state service gets updated
    AugmentAppStateService.instance.reset()
    // Wait for plugin state to be enabled
    waitForAssertion({
      assertEquals(PluginState.ENABLED, AugmentAppStateService.instance.state)
    }, timeoutMs = 5000) // TODO: This timeout should not need to be longer, there is a locking issue in the executor
  }

  /**
   * Clears the model cache in AugmentAPI to ensure we get fresh data.
   * This is necessary because the AugmentAPI caches model info, including feature flags.
   */
  private fun clearAugmentAPICache() {
    // Get the AugmentAPI instance and clear its cache
    val apiInstance = AugmentAPI.instance
    // The clearModelCache method is marked with @VisibleForTesting
    apiInstance.clearModelCache()
  }
}
