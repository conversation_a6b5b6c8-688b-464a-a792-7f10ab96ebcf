package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.workspacemanagement.indexing.WORKSPACE_INDEX_ID
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import com.intellij.util.indexing.FileBasedIndex
import io.mockk.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class WorkspaceCoordinatorServiceTest : AugmentBasePlatformTestCase() {
  lateinit var scope: CoroutineScope

  override fun setUp() {
    super.setUp()
    scope = augmentHelpers().createCoroutineScope(Dispatchers.IO)
  }

  @Test
  fun testNoEnqueueWhenV3IndexingDisabled() {
    mockAugmentAppStateService(false)
    val service = WorkspaceCoordinatorService.getInstance(project)

    service.enqueueFileForProcessing("/src/test")

    val result = service.intakeChannel.tryReceive()
    assertFalse(result.isSuccess)
    assertTrue(result.isFailure)
  }

  @Test
  fun testEnqueueWhenV3IndexingEnabled() {
    mockAugmentAppStateService(true)
    val service = WorkspaceCoordinatorService(project, scope)
    Disposer.register(testRootDisposable, service)

    val filePath = "/src/test"
    service.enqueueFileForProcessing(filePath)

    val result = service.intakeChannel.tryReceive()
    assertTrue(result.isSuccess)
    assertFalse(result.isFailure)
    assertEquals(filePath, result.getOrThrow())
  }

  @Test
  fun testDisposal() {
    mockAugmentAppStateService(true)
    val service = WorkspaceCoordinatorService(project, scope)
    Disposer.register(testRootDisposable, service)

    service.enqueueFileForProcessing("/src/test")

    val result = service.intakeChannel.tryReceive()
    assertTrue(result.isSuccess)
    assertFalse(result.isFailure)
    assertFalse(result.isClosed)

    // Dispose of the service and verify the channel is closed
    Disposer.dispose(service)

    val result2 = service.intakeChannel.tryReceive()
    assertFalse(result2.isSuccess)
    assertTrue(result2.isFailure)
    assertTrue(result2.isClosed)
  }

  @Test
  fun testNoReindexOnSignInIfDisabled() {
    val pluginStateService = mockAugmentAppStateService(false)
    var listener: PluginStateListener? = null
    every { pluginStateService.subscribe(any(), any(), any()) } answers {
      listener = secondArg<PluginStateListener>()
    }

    val fileIndexService = mockFileBasedIndexService()
    every { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) } answers { /* no-op */ }

    val service = WorkspaceCoordinatorService(project, scope)
    Disposer.register(testRootDisposable, service)

    assertNotNull(listener)

    verify(exactly = 0) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
    listener!!.onStateChange(PluginContext(true, DefaultFeatureFlags, null), PluginState.ENABLED)
    verify(exactly = 0) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
  }

  fun mockAugmentAppStateService(enableV3Indexing: Boolean): AugmentAppStateService {
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(enableV3Indexing)
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )

    val mockAugmentAppStateService = mockk<AugmentAppStateService>(relaxed = true)
    every { mockAugmentAppStateService.context } returns mockContext

    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )

    return mockAugmentAppStateService
  }

  fun mockFileBasedIndexService(): FileBasedIndex {
    mockkStatic(FileBasedIndex::class)
    val mockIndex = mockk<FileBasedIndex>()
    every { FileBasedIndex.getInstance() } returns mockIndex
    return mockIndex
  }
}
