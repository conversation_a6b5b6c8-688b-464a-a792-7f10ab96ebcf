package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout

class MoveFileSyncTest : SyncTestCase() {
  fun testMoveFile() =
    runBlocking {
      val state = MockEngineState()
      val mockEngine = createMockEngine(state)
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val tempDir = getVirtualFile(createTempDirectory())
      PsiTestUtil.addContentRoot(module, tempDir)

      // Create subdirectory and initial file
      val (subDir, file) =
        runWriteAction {
          val subDir = tempDir.createChildDirectory(this, "subdir")
          val file =
            tempDir.createChildData(this, "foo.txt").also {
              VfsUtil.saveText(it, "foo")
            }
          Pair(subDir, file)
        }

      val originalBlobState = AugmentBlobStateReader.read(file.toPsiFile(project))
      assertNotNull(originalBlobState)
      val originalBlobName = originalBlobState!!.remoteName

      // Wait for initial upload and checkpoint
      waitForRequests(mockEngine, "/batch-upload", 1)
      waitForRequests(mockEngine, "/checkpoint-blobs", 1)

      // Move the file to subdirectory
      WriteCommandAction.runWriteCommandAction(project) {
        file.move(this, subDir)
      }

      // Force indexing to occur
      val movedBlobState = AugmentBlobStateReader.read(file.toPsiFile(project))
      assertNotNull(movedBlobState)
      val movedBlobName = movedBlobState!!.remoteName

      // Wait for the new blob to be uploaded
      waitForRequests(mockEngine, "/batch-upload", 2)

      // Wait for removal of old state and addition of new state to be processed in sync manager
      val syncManager = AugmentRemoteSyncingManager.getInstance(project)
      try {
        withTimeout(10000) {
          while (true) {
            val payload = syncManager.synchronizedBlobsPayload()

            val checkpointMatches = payload.checkpointId == "checkpoint-1"
            val addedBlobsMatch = payload.addedBlobs == setOf(movedBlobName)
            val deletedBlobsMatch = payload.deletedBlobs == setOf(originalBlobName)

            if (checkpointMatches && addedBlobsMatch && deletedBlobsMatch) {
              break
            }
            delay(250) // Poll interval
          }
        }
      } catch (e: TimeoutCancellationException) {
        // Only run this when timeout occurs
        val payload = syncManager.synchronizedBlobsPayload()
        val errors = mutableListOf<String>()

        if (payload.checkpointId != "checkpoint-1") {
          errors.add("Checkpoint ID mismatch: expected 'checkpoint-1' but got '${payload.checkpointId}'")
        }
        if (payload.addedBlobs != setOf(movedBlobName)) {
          errors.add("Added blobs mismatch: expected ${setOf(movedBlobName)} but got ${payload.addedBlobs}")
        }
        if (payload.deletedBlobs != setOf(originalBlobName)) {
          errors.add("Deleted blobs mismatch: expected ${setOf(originalBlobName)} but got ${payload.deletedBlobs}")
        }

        throw AssertionError("Timeout waiting for sync state to match. Mismatches:\n${errors.joinToString("\n")}")
      }
    }
}
