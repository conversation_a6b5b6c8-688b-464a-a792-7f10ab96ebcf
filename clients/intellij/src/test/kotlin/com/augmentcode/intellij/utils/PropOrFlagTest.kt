package com.augmentcode.intellij.utils

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class PropOrFlagTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    mockkObject(CustomPropertyReader)
  }

  override fun tearDown() {
    unmockkObject(CustomPropertyReader)
    super.tearDown()
  }

  @Test
  fun `test propOrFlagBool when custom property is true`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY) } returns "true"

    val isEnabled = propOrFlagBool(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY, fallbackFlag = false)
    assertTrue("Should be enabled when custom property is true", isEnabled)
  }

  @Test
  fun `test propOrFlagBool when custom property is false`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY) } returns "false"

    val isEnabled = propOrFlagBool(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY, fallbackFlag = true)
    assertFalse("Should be disabled when custom property is false", isEnabled)
  }

  @Test
  fun `test propOrFlagBool when custom property not set and feature flag enabled`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY) } returns null

    assertTrue(
      "Should be enabled when feature flag is enabled",
      propOrFlagBool(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY, fallbackFlag = true),
    )
  }

  @Test
  fun `test propOrFlagBool when custom property not set and feature flag disabled`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY) } returns null

    assertFalse(
      "Should be disabled when feature flag is disabled",
      propOrFlagBool(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY, fallbackFlag = false),
    )
  }

  @Test
  fun `test propOrFlagBool with invalid custom property value`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY) } returns "invalid"

    // Should fall back to feature flag when custom property is invalid
    assertTrue(
      "Should fall back to feature flag when custom property is invalid",
      propOrFlagBool(CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY, fallbackFlag = true),
    )
  }

  @Test
  fun `test propOrFlagBool with tool use state storage property when custom property is true`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY) } returns "true"

    val isEnabled = propOrFlagBool(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY, fallbackFlag = false)
    assertTrue("Tool use state storage should be enabled when custom property is true", isEnabled)
  }

  @Test
  fun `test propOrFlagBool with tool use state storage property when custom property is false`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY) } returns "false"

    val isEnabled = propOrFlagBool(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY, fallbackFlag = true)
    assertFalse("Tool use state storage should be disabled when custom property is false", isEnabled)
  }

  @Test
  fun `test propOrFlagBool with tool use state storage property when custom property not set and feature flag enabled`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY) } returns null

    assertTrue(
      "Tool use state storage should be enabled when feature flag is enabled",
      propOrFlagBool(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY, fallbackFlag = true),
    )
  }

  @Test
  fun `test propOrFlagBool with tool use state storage property when custom property not set and feature flag disabled`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY) } returns null

    assertFalse(
      "Tool use state storage should be disabled when feature flag is disabled",
      propOrFlagBool(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY, fallbackFlag = false),
    )
  }

  @Test
  fun `test propOrFlagBool with tool use state storage property with invalid custom property value`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY) } returns "invalid"

    // Should fall back to feature flag when custom property is invalid
    assertTrue(
      "Tool use state storage should fall back to feature flag when custom property is invalid",
      propOrFlagBool(CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY, fallbackFlag = true),
    )
  }
}
