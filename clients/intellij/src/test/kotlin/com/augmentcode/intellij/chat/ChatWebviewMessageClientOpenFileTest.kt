package com.augmentcode.intellij.chat

import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.augmentcode.rpc.FileDetails
import com.augmentcode.rpc.OpenFileRequest
import com.intellij.ide.actions.OpenFileAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class ChatWebviewMessageClientOpenFileTest : AugmentHeavyPlatformTestCase() {
  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(
      PluginState.ENABLED,
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(true)
          .build(),
      ),
    )
  }

  @Test
  fun testOpenFile_withBlob() =
    runTest {
      // Create a file and index it
      val file = getVirtualFile(createTempFile("testFile.txt", "testFile"))

      assertTrue(isV3IndexingEnabled())

      val mockBlobNameService = mockk<BlobNameService>(relaxed = true)
      coEvery { mockBlobNameService.findFile(file.path) } returns listOf(file.path)
      project.registerOrReplaceServiceInstance(
        BlobNameService::class.java,
        mockBlobNameService,
        testRootDisposable,
      )

      mockkObject(OpenFileAction)
      every { OpenFileAction.openFile(any<VirtualFile>(), any<Project>()) } returns Unit

      val msgClient = ChatWebviewMessageClient(project, backgroundScope)
      val request =
        OpenFileRequest
          .newBuilder()
          .setData(
            FileDetails
              .newBuilder()
              .setPathName(file.path)
              .build(),
          ).build()

      msgClient.openFile(request)

      waitForAssertion({
        PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

        verify { OpenFileAction.openFile(file, project) }
      })
    }
}
