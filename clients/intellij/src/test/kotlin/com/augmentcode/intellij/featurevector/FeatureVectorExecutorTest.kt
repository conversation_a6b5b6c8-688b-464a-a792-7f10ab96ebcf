package com.augmentcode.intellij.featurevector

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FeatureVectorExecutorTest : AugmentBasePlatformTestCase() {

  @Test
  fun testJavaScriptExecution() =
    runBlocking {
      val result = try {
        FeatureVectorExecutor.execute()
      } catch (e: Exception) {
        // Log the specific failure reason for debugging
        println("FeatureVectorExecutor.execute() failed: ${e.javaClass.simpleName}: ${e.message}")
        
        // Verify we get expected exception types in test environments
        assertTrue(
          "Should fail with meaningful exception when Node.js unavailable or script missing",
          e is IllegalStateException || e is RuntimeException || e is java.io.IOException
        )
        return@runBlocking
      }

      // If execution succeeds, verify the result structure
      assertNotNull("Result should not be null when execution succeeds", result)
      assertTrue("Result should be a non-empty map", result.isNotEmpty())
      
      // Verify all keys are integers and all values are strings
      result.forEach { (key: String, value) ->
        assertTrue("All keys should be numeric strings", key.toIntOrNull() != null)
        assertTrue("All values should be non-empty strings", value.isNotEmpty())
      }
      
      println("FeatureVectorExecutor returned ${result.size} feature entries")
    }


  @Test
  fun testParseOutputWithValidJson() {
    // Test with valid JSON
    val validJson = """{"1": "value1", "2": "value2", "42": "value42"}"""
    val jsonOutput = "Some log output\n$validJson\nMore output"

    val result = FeatureVectorExecutor.parseOutput(jsonOutput)

    assertEquals("Should parse 3 entries", 3, result.size)
    assertEquals("Entry 1 should match", "value1", result["1"])
    assertEquals("Entry 2 should match", "value2", result["2"])
    assertEquals("Entry 42 should match", "value42", result["42"])
  }
}
