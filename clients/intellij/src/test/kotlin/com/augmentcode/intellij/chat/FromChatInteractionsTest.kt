@file:Suppress("DEPRECATION") // We use MockAugmentAPI

package com.augmentcode.intellij.chat

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.rpc.FileDetails
import com.augmentcode.rpc.FindSymbolRequest
import com.augmentcode.rpc.FindSymbolRequestData
import com.augmentcode.rpc.ISearchScopeArgs
import com.intellij.testFramework.IndexingTestUtil
import com.intellij.testFramework.TestDataPath
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class FromChatInteractionsTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/chat"

  @Test
  fun testFindSymbol() =
    runTest {
      myFixture.configureByFile("AwesomeExample.xml")
      IndexingTestUtil.waitUntilIndexesAreReady(myFixture.project)
      val response =
        ChatWebviewMessageClient(project, backgroundScope)
          .findSymbol(
            FindSymbolRequest
              .newBuilder()
              .setData(
                FindSymbolRequestData
                  .newBuilder()
                  .setQuery("awesomeElement1")
                  .build(),
              ).build(),
          )
      assertEquals(1, response.dataCount)
      val result = response.dataList.first().file
      assertEquals("AwesomeExample.xml", result.pathName)
      assertEquals(2, result.range.start)
      assertEquals(6, result.range.stop)
      assertEquals(1, result.fullRange.startLineNumber)
      assertEquals(2, result.fullRange.startColumn)
      assertEquals(5, result.fullRange.endLineNumber)
      assertEquals(11, result.fullRange.endColumn)
    }

  @Test
  fun testFindSymbolNoSearchScope() =
    runTest {
      myFixture.configureByFile("AwesomeExample.xml")
      myFixture.configureByFile("AwesomeExample2.xml")
      IndexingTestUtil.waitUntilIndexesAreReady(myFixture.project)

      val request =
        FindSymbolRequest
          .newBuilder()
          .setData(
            FindSymbolRequestData
              .newBuilder()
              .setQuery("awesomeElement1")
              .build(),
          ).build()

      val response =
        ChatWebviewMessageClient(project, backgroundScope)
          .findSymbol(request)

      // No search scope provided, so we should get both results
      assertEquals(2, response.dataList.size)
      assertTrue(response.dataList.map { it.file.pathName }.toSet() == setOf("AwesomeExample.xml", "AwesomeExample2.xml"))
    }

  @Test
  fun testFindSymbolWithSearchScope() =
    runTest {
      myFixture.configureByFile("AwesomeExample.xml")
      myFixture.configureByFile("AwesomeExample2.xml")
      IndexingTestUtil.waitUntilIndexesAreReady(myFixture.project)

      val request =
        FindSymbolRequest
          .newBuilder()
          .setData(
            FindSymbolRequestData
              .newBuilder()
              .setQuery("awesomeElement1")
              .setSearchScope(
                ISearchScopeArgs
                  .newBuilder()
                  .addFiles(
                    FileDetails
                      .newBuilder()
                      .setPathName("AwesomeExample.xml")
                      .build(),
                  ),
              ).build(),
          ).build()

      val response =
        ChatWebviewMessageClient(project, backgroundScope)
          .findSymbol(request)

      assertEquals(1, response.dataCount)
      val result = response.dataList.first().file
      assertEquals("AwesomeExample.xml", result.pathName)
    }
}
