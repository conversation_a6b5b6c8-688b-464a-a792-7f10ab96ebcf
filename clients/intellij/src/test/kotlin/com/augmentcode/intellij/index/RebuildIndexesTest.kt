package com.augmentcode.intellij.index

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readText
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.util.indexing.FileBasedIndex
import io.mockk.*
import kotlinx.coroutines.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class RebuildIndexesTest : AugmentBasePlatformTestCase() {
  private lateinit var file1: VirtualFile
  private lateinit var file2: VirtualFile
  private lateinit var mockFileBasedIndex: FileBasedIndex

  @Before
  fun setUpMocks() {
    // Mock FileBasedIndex
    mockFileBasedIndex = mockk<FileBasedIndex>()
    every { mockFileBasedIndex.requestReindex(any()) } answers {
      // We had a deadlock where requestReindex() was called from inside a write lock, and it requested a read lock.
      // This mock function is intended to trigger a read lock to try and prevent regressions.

      val file: VirtualFile = firstArg<VirtualFile>()

      // We call runReadAction from a coroutine so it doesn't "inherit" a read lock from the caller if it has a read
      // or write lock already.
      val cs = augmentHelpers().createCoroutineScope(Dispatchers.IO)
      val job =
        cs.launch {
          runReadAction {
            file.readText()
          }
        }
      runBlocking { job.join() }
    }

    mockkStatic(FileBasedIndex::class)
    // Stub getInstance to return our mock
    every { FileBasedIndex.getInstance() } answers {
      mockFileBasedIndex
    }

    // Mock AugmentBlobStateReader.findFilesByState
    mockkObject(AugmentBlobStateReader)
  }

  @After
  fun tearDownMocks() {
    unmockkAll()
  }

  @Test
  fun testRequestInvalidationWithFiles() =
    runBlocking {
      // Setup test data outside EDT
      file1 = myFixture.addFileToProject("file1.txt", "content1").virtualFile
      file2 = myFixture.addFileToProject("file2.txt", "content2").virtualFile

      // Set up mock to return our test files
      val filesToInvalidate = listOf(file1, file2)
      coEvery {
        AugmentBlobStateReader.findFilesByState(project, any())
      } returns filesToInvalidate

      // Create a set of remote names to invalidate
      val remoteNamesToInvalidate = setOf("remote1", "remote2")

      // Verify initial state - files should not have FORCE_UPLOAD flag set
      assertNull(file1.getUserData(AugmentBlobStateReader.FORCE_UPLOAD))
      assertNull(file2.getUserData(AugmentBlobStateReader.FORCE_UPLOAD))

      // Run the test logic in a background thread
      // Call the method under test
      AugmentBlobStateReader.requestInvalidation(project, remoteNamesToInvalidate)

      waitForAssertion({
        // Wait for the invokeLater queue to be run
        PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

        assertTrue(
          "File1 should be marked for upload",
          file1.getUserData(AugmentBlobStateReader.FORCE_UPLOAD) == true,
        )
        assertTrue(
          "File2 should be marked for upload",
          file2.getUserData(AugmentBlobStateReader.FORCE_UPLOAD) == true,
        )

        // Verify that reindex was requested for each file
        verify(exactly = 1) { mockFileBasedIndex.requestReindex(file1) }
        verify(exactly = 1) { mockFileBasedIndex.requestReindex(file2) }
      })
    }

  private fun waitForAssertion(
    predicate: () -> Unit,
    timeoutMs: Long = 1000,
    pollIntervalMs: Long = 100,
  ) {
    val startTime = System.currentTimeMillis()
    while (System.currentTimeMillis() - startTime < timeoutMs) {
      try {
        predicate()
        return
      } catch (e: AssertionError) {
        // ignore and retry
      }
      Thread.sleep(pollIntervalMs)
    }
    predicate()
  }

  @Test
  fun testRequestInvalidationWithNoFiles() =
    runBlocking {
      // Set up mock to return empty list
      coEvery {
        AugmentBlobStateReader.findFilesByState(project, any())
      } returns emptyList()

      // Create a set of remote names to invalidate
      val remoteNamesToInvalidate = setOf("remote1", "remote2")

      // Call the method under test
      AugmentBlobStateReader.requestInvalidation(project, remoteNamesToInvalidate)

      // Wait for the invokeLater to complete
      ApplicationManager.getApplication().invokeAndWait {
        // No-op, just waiting for the queue to drain
      }

      // Verify that reindex was not requested for any file
      verify(exactly = 0) { mockFileBasedIndex.requestReindex(any()) }
    }
}
