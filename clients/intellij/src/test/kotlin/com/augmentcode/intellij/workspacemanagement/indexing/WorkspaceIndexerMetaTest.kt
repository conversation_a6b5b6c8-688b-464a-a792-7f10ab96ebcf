package com.augmentcode.intellij.workspacemanagement.indexing

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

/**
 * This test class is meant to document how to write tests
 * that involve the workspace indexer. It is tricky and weird,
 * and sometimes you need magical incantations to make things work.
 * If you find a new magical incantation, please put it in a test here
 * so that we can remember it.
 */
@RunWith(JUnit4::class)
class WorkspaceIndexerMetaTest : AugmentBasePlatformTestCase() {
  @Test
  fun testConfigureByTextWithIndexing() {
    myFixture.configureByText("foo.txt", "Hello Worm")

    // For some reason, we need to force indexing to occur by
    // reading from the index.
    val indexedPath = augmentHelpers().forceIndexingForFile(myFixture.file.virtualFile)
    assertNotNull(indexedPath)
  }
}
