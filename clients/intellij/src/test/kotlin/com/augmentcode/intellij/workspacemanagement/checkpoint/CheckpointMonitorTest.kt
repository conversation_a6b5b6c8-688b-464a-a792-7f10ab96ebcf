package com.augmentcode.intellij.workspacemanagement.checkpoint

import com.augmentcode.api.BlobsPayload
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.util.Disposer
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class CheckpointMonitorTest : AugmentBasePlatformTestCase() {
  @OptIn(ExperimentalCoroutinesApi::class)
  @Test
  fun testBasicMonitorStartup() =
    runTest {
      val mockManager = mockk<CheckpointManager>(relaxed = true)
      val mockLogger = mockk<Logger>(relaxed = true)

      // Initial empty state
      val emptyPayload =
        BlobsPayload().apply {
          checkpointId = null
          addedBlobs = mutableSetOf()
          deletedBlobs = mutableSetOf()
        }
      coEvery { mockManager.currentCheckpoint() } returns emptyPayload

      val monitor =
        CheckpointMonitor(
          cs = augmentHelpers().createCoroutineScope(Dispatchers.IO),
          manager = mockManager,
          logger = mockLogger,
        )
      Disposer.register(testRootDisposable, monitor)

      // Advance time to trigger the startup and first monitoring cycle
      advanceTimeBy(CheckpointMonitor.MONITOR_JOB_INTERVAL_MS)

      // Should have the startup log
      verify(atLeast = 1) { mockLogger.info(any<String>()) }
    }

  @OptIn(ExperimentalCoroutinesApi::class)
  @Test
  fun testLoggingOfCheckpointChanges() =
    runTest {
      val mockManager = mockk<CheckpointManager>(relaxed = true)
      val mockLogger = mockk<Logger>(relaxed = true)

      // Initial empty state
      val emptyPayload =
        BlobsPayload().apply {
          checkpointId = null
          addedBlobs = mutableSetOf()
          deletedBlobs = mutableSetOf()
        }
      coEvery { mockManager.currentCheckpoint() } returns emptyPayload

      val monitor =
        CheckpointMonitor(
          cs = augmentHelpers().createCoroutineScope(Dispatchers.IO),
          manager = mockManager,
          logger = mockLogger,
        )
      Disposer.register(testRootDisposable, monitor)

      // Advance time to trigger the startup and first monitoring cycle
      advanceTimeBy(CheckpointMonitor.MONITOR_JOB_INTERVAL_MS)

      verify(atLeast = 1) {
        mockLogger.info(
          match<String> {
            it.contains("Starting")
          },
        )
      }

      // Clear any startup logs
      clearMocks(mockLogger)

      // Add some blobs
      val payloadWithAddedBlobs =
        BlobsPayload().apply {
          checkpointId = null
          addedBlobs = mutableSetOf("blob1", "blob2")
          deletedBlobs = mutableSetOf()
        }
      coEvery { mockManager.currentCheckpoint() } returns payloadWithAddedBlobs

      waitForAssertion({
        advanceUntilIdle()
        verify(atLeast = 1) {
          mockLogger.info(
            match<String> {
              it.contains("Checkpoint status") &&
                it.contains("Added: 2") &&
                it.contains("Deleted: 0") &&
                it.contains("Working set: 2")
            },
          )
        }
      })

      // Reset mocks to test further changes
      clearMocks(mockLogger)

      // Add deleted blobs and a checkpoint ID
      val payloadWithChanges =
        BlobsPayload().apply {
          checkpointId = "checkpoint-123"
          addedBlobs = mutableSetOf("blob1", "blob2", "blob3")
          deletedBlobs = mutableSetOf("blob4")
        }
      coEvery { mockManager.currentCheckpoint() } returns payloadWithChanges

      waitForAssertion({
        advanceUntilIdle()
        verify(atLeast = 1) {
          mockLogger.info(
            match<String> {
              it.contains("Checkpoint status") &&
                it.contains("Added: 3") &&
                it.contains("Deleted: 1") &&
                it.contains("Working set: 4") &&
                it.contains("Checkpoint ID: checkpoint-123")
            },
          )
        }
      })

      // Verify no logs if there are no changes
      clearMocks(mockLogger)
      advanceUntilIdle()
      verify(exactly = 0) { mockLogger.info(any<String>()) }
    }
}
