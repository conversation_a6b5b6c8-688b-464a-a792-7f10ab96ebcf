package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import kotlinx.coroutines.runBlocking

class FullFlowSyncTest : SyncTestCase() {
  fun testFullSyncFlow() =
    runBlocking {
      val state = MockEngineState()
      val mockEngine = createMockEngine(state)
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val tempDir = getVirtualFile(createTempDirectory())
      PsiTestUtil.addContentRoot(module, tempDir)
      val file =
        runWriteAction {
          tempDir.createChildData(this, "foo.txt").also {
            VfsUtil.saveText(it, "foo")
          }
        }
      val blobState = AugmentBlobStateReader.read(file.toPsiFile(project))
      assertNotNull(blobState)

      waitForRequests(mockEngine, "/batch-upload", 1)
      waitForRequests(mockEngine, "/checkpoint-blobs", 1)

      val syncManager = AugmentRemoteSyncingManager.getInstance(project)
      val payload = syncManager.synchronizedBlobsPayload()

      assertEquals("checkpoint-1", payload.checkpointId)
      assertTrue(payload.addedBlobs.isEmpty())
      assertTrue(payload.deletedBlobs.isEmpty())
    }
}
