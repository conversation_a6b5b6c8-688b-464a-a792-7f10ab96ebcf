package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.api.ListRemoteToolsResponse
import com.augmentcode.api.ListRemoteToolsResult
import com.augmentcode.api.ToolDefinition
import com.augmentcode.intellij.api.AugmentHttpClient.Companion.createGson
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.sidecarrpc.IOConnection
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.sidecar.rpc.tools.ChatMode
import com.augmentcode.sidecar.rpc.tools.RemoteToolId
import com.augmentcode.sidecar.rpc.tools.RetrieveRemoteToolsRequest
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream

@RunWith(JUnit4::class)
class ToolsManagerTest : AugmentBasePlatformTestCase() {
  private var httpClient: HttpClient? = null
  private val gson = createGson()

  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  override fun tearDown() {
    try {
      httpClient?.close()
      httpClient = null
    } finally {
      super.tearDown()
    }
  }

  @Test
  fun testRetrieveRemoteTools() {
    val mockEngine =
      MockEngine { request ->
        assertEquals("POST", request.method.value)
        assertEquals("/agents/list-remote-tools", request.url.encodedPath)

        respond(
          content =
            gson.toJson(
              ListRemoteToolsResult().apply {
                tools =
                  setOf(
                    ListRemoteToolsResponse().apply {
                      tool_definition =
                        ToolDefinition().apply {
                          name = "test-tool"
                          description = "test-tool-description"
                          input_schema_json = "{}"
                        }
                      remote_tool_id = RemoteToolId.WebSearch.number
                      availability_status = 1
                      tool_safety = 1
                      oauth_url = "https://example.com/oauth"
                    },
                    ListRemoteToolsResponse().apply {
                      tool_definition =
                        ToolDefinition().apply {
                          name = "test-tool-2"
                          description = "test-tool-description-2"
                          input_schema_json = "{}"
                        }
                      remote_tool_id = RemoteToolId.GitHubApi.number
                      availability_status = 1
                      tool_safety = 1
                      oauth_url = "https://example.com/oauth"
                    },
                    // The backend shouldn't return an unknown tool, but if it does, ignore it.
                    ListRemoteToolsResponse().apply {
                      tool_definition =
                        ToolDefinition().apply {
                          name = "test-tool-unknown"
                          description = "test-tool-description-unknown"
                          input_schema_json = "{}"
                        }
                      remote_tool_id = 999999999 // unknown tool id
                      availability_status = 1
                      tool_safety = 1
                    },
                  )
              },
            ),
          status = HttpStatusCode.OK,
          headers = headersOf(HttpHeaders.ContentType, "application/json"),
        )
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    val connection =
      IOConnection(
        ByteArrayInputStream(ByteArray(0)),
        ByteArrayOutputStream(),
        augmentHelpers().createCoroutineScope(Dispatchers.IO),
      )
    val toolsManager = ToolsManager(project, connection, ChatMode.CHAT)
    val response =
      runBlocking {
        toolsManager.retrieveRemoteTools(
          RetrieveRemoteToolsRequest.newBuilder()
            .addAllSupportedTools(listOf(RemoteToolId.WebSearch, RemoteToolId.GitHubApi)).build(),
        )
      }
    assertEquals(2, response.toolsList.size)
    assertEquals(RemoteToolId.WebSearch.number, response.toolsList[0].remoteToolId.number)
    assertEquals(RemoteToolId.GitHubApi.number, response.toolsList[1].remoteToolId.number)
    assertEquals("test-tool", response.toolsList[0].toolDefinition.name)
    assertEquals("test-tool-description", response.toolsList[0].toolDefinition.description)
    assertEquals(0, response.toolsList[0].toolDefinition.toolSafety.number)
    assertEquals(1, response.toolsList[0].availabilityStatus.number)
    assertEquals(1, response.toolsList[0].toolSafety.number)
  }
}
