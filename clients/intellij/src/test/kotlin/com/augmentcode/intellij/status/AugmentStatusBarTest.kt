package com.augmentcode.intellij.status

import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentStatusBarTest : AugmentBasePlatformTestCase() {
  @Test
  fun testAuthStateChanges() {
    val credentials = runBlocking { AugmentOAuthState.instance.getCredentials() }
    assertEquals(credentials, null)

    val statusBar = AugmentStatusBar(project)

    waitAndAssertStatusBar(statusBar, StateDefinitions.SignInNeeded)

    // Sign in users
    augmentHelpers().emulateSignInFlow()

    waitAndAssertStatusBar(statusBar, StateDefinitions.Enabled)

    // Sign out user
    augmentHelpers().emulateSignOutFlow()

    waitAndAssertStatusBar(statusBar, StateDefinitions.SignInNeeded)
  }

  @Test
  fun testSettingsChanges() {
    // Sign in user
    augmentHelpers().emulateSignInFlow()

    val statusBar = AugmentStatusBar(project)

    waitAndAssertStatusBar(statusBar, StateDefinitions.Enabled)

    AugmentSettings.instance.inlineCompletionEnabled = false
    waitAndAssertStatusBar(statusBar, StateDefinitions.AutoCompletionsDisabled)

    AugmentSettings.instance.inlineCompletionEnabled = true
    waitAndAssertStatusBar(statusBar, StateDefinitions.Enabled)
  }

  @Test
  fun testStatusBarInstantiation() {
    // Test that the status bar can be instantiated without errors
    val statusBar = AugmentStatusBar(project)
    assertNotNull("Status bar should be created successfully", statusBar)
    assertEquals("AugmentStatus", statusBar.ID())
  }

  private fun waitAndAssertStatusBar(
    statusBar: AugmentStatusBar,
    expectedDefinition: StateDefinition,
  ) {
    // Wait for status bar to update
    waitForAssertion({
      assertEquals(expectedDefinition.tooltip, statusBar.toolTip())
      assertEquals(expectedDefinition.icon, statusBar.icon())
    }, timeoutMs = 5000)
  }
}
