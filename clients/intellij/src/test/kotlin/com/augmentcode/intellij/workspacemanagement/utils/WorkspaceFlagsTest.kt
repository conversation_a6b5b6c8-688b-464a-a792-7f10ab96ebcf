package com.augmentcode.intellij.workspacemanagement.utils

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class WorkspaceFlagsTest : AugmentBasePlatformTestCase() {
  val mockAugmentAppStateService: AugmentAppStateService = mockk(relaxed = true)

  override fun setUp() {
    super.setUp()
    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )
  }

  @Test
  fun testIsV3IndexingSignedOut() {
    every { mockAugmentAppStateService.context } returns PluginContext(false, DefaultFeatureFlags, null)
    assertFalse(isV3IndexingEnabled())
  }

  @Test
  fun testIsV3IndexingDisabled() {
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(false)
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.context } returns mockContext
    assertFalse(isV3IndexingEnabled())
  }

  @Test
  fun testIsV3IndexingEnabled() {
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(true)
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.context } returns mockContext
    assertTrue(isV3IndexingEnabled())
  }
}
