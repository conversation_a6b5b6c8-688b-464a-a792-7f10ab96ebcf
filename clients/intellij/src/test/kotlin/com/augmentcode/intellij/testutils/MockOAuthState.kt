package com.augmentcode.intellij.testutils

import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.intellij.openapi.Disposable
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.coEvery
import io.mockk.mockk

/**
 * This is useful if you want to set the credentials, but not emit any events on
 * the credentials message bus.
 */
fun mockOAuthState(
  credentials: AugmentCredentials?,
  testRootDisposable: Disposable,
) {
  val mockOAuthState = mockk<AugmentOAuthState>(relaxed = true)
  coEvery { mockOAuthState.getCredentials() } returns credentials
  application.registerOrReplaceServiceInstance(
    AugmentOAuthState::class.java,
    mockOAuthState,
    testRootDisposable,
  )
}
