package com.augmentcode.intellij.chat

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AppStateExecutor
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.*
import io.mockk.every
import kotlinx.coroutines.Dispatchers
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentChatProjectActivityTest : AugmentBasePlatformTestCase() {
  private lateinit var mockAugmentAppStateService: AugmentAppStateService
  private lateinit var mockMessagingService: ChatMessagingService

  override fun setUp() {
    super.setUp()

    // Mock AugmentAppStateService
    mockAugmentAppStateService = mockk(relaxed = true)
    every { mockAugmentAppStateService.subscribe(any(), any(), true) } answers {
      val listener = secondArg<PluginStateListener>()
      listener.onStateChange(mockAugmentAppStateService.context, mockAugmentAppStateService.state)
    }

    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )

    mockMessagingService = mockk(relaxed = true)
    project.registerOrReplaceServiceInstance(
      ChatMessagingService::class.java,
      mockMessagingService,
      testRootDisposable,
    )
  }

  @Test
  fun testSendInitialGuidelinesStateEnabled() {
    val modelConfig = augmentHelpers().createGetModelsResponse()
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.state } returns PluginState.ENABLED
    every { mockAugmentAppStateService.context } returns mockContext
    val activity = AugmentChatProjectActivity(augmentHelpers().createCoroutineScope(Dispatchers.IO))
    activity.onStartup(project)

    waitForAssertion({
      verify(exactly = 1) { mockMessagingService.sendGuidelinesStateToWebview() }
    })
  }

  @Test
  fun testSendInitialGuidelinesStateNoPluginStateContext() {
    every { mockAugmentAppStateService.context } returns PluginContext(false, DefaultFeatureFlags, null)
    val activity = AugmentChatProjectActivity(augmentHelpers().createCoroutineScope(Dispatchers.IO))
    activity.onStartup(project)

    verify(exactly = 0) { mockMessagingService.sendGuidelinesStateToWebview() }
  }

  @Test
  fun testSendInitialGuidelinesStateOnPluginStateChange() {
    val modelConfig = augmentHelpers().createGetModelsResponse()
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.state } returns PluginState.ENABLED
    every { mockAugmentAppStateService.context } returns mockContext

    var listener: PluginStateListener? = null
    every { mockAugmentAppStateService.subscribe(any(), any(), true) } answers {
      val l = secondArg<PluginStateListener>()
      listener = l
      l.onStateChange(mockAugmentAppStateService.context, mockAugmentAppStateService.state)
    }

    val activity = AugmentChatProjectActivity(augmentHelpers().createCoroutineScope(Dispatchers.IO))
    activity.onStartup(project)

    waitForAssertion({
      verify(exactly = 1) { mockMessagingService.sendGuidelinesStateToWebview() }
    })

    assertNotNull(listener)

    // These state changes should not trigger a guidelines state change
    listener?.onStateChange(AppStateExecutor.defaultContext, PluginState.INITIALIZING)
    listener?.onStateChange(AppStateExecutor.defaultContext, PluginState.ENABLED)
    // This state change should trigger a guidelines state change
    listener?.onStateChange(mockContext, PluginState.ENABLED)

    waitForAssertion({
      verify(exactly = 2) { mockMessagingService.sendGuidelinesStateToWebview() }
    })
  }
}
