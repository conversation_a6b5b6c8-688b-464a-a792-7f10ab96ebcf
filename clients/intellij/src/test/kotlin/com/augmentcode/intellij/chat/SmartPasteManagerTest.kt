package com.augmentcode.intellij.chat

import com.augmentcode.api.ChatInstructionStreamPayload
import com.augmentcode.intellij.mock.MockDiffManager
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.diff.DiffManager
import com.intellij.diff.contents.DocumentContent
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import io.ktor.utils.io.*
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class SmartPasteManagerTest : AugmentBasePlatformTestCase() {
  companion object {
    // Test data from running smart paste locally:
    private val INSERT_RESPONSE =
      """
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"","replacement_old_text":"","replacement_start_line":1,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"def add(a, b):\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"    \"\"\"Add two numbers together\"\"\"\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"    return a + b\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":1,"replacement_sequence_id":0}
      """.trimIndent()

    private val REPLACE_RESPONSE =
      """
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"","replacement_old_text":"","replacement_start_line":2,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"def plus(a, b):\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"    \"\"\"Add two numbers together\"\"\"\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"    return a + b\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":5,"replacement_sequence_id":0}
      """.trimIndent()

    private val DELETE_RESPONSE =
      """
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"","replacement_old_text":"","replacement_start_line":3,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"    return a + b\n","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":0,"replacement_sequence_id":0}
      {"text":"","unknown_blob_names":[],"checkpoint_not_found":false,"replacement_text":"","replacement_old_text":"","replacement_start_line":0,"replacement_end_line":5,"replacement_sequence_id":0}
      """.trimIndent()

    private const val AFTER_INSERT = "\ndef add(a, b):\n    \"\"\"Add two numbers together\"\"\"\n    return a + b\n\n"
    private const val AFTER_REPLACE = "\ndef plus(a, b):\n    \"\"\"Add two numbers together\"\"\"\n    return a + b\n\n"
    private const val AFTER_DELETE = "\ndef plus(a, b):\n    return a + b\n\n"
  }

  private lateinit var mathFile: VirtualFile

  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.modelName = null
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
    project.registerOrReplaceServiceInstance(
      SmartPasteManager::class.java,
      SmartPasteManager(project, augmentHelpers().createCoroutineScope(Dispatchers.Default)),
      testRootDisposable,
    )

    mathFile = myFixture.addFileToProject("math.py", "\n").virtualFile
  }

  override fun tearDown() {
    SmartPasteManager.getInstance(project).dispose()
    AugmentSettings.instance.apiToken = null
    AugmentSettings.instance.completionURL = null
    super.tearDown()
  }

  private fun setMathFileContent(content: String) {
    runBlocking {
      application.runWriteAction {
        mathFile.setBinaryContent(content.toByteArray())
      }
    }
  }

  // Tracks how many times the API was called
  private data class CallCounter(
    var count: Int = 0,
    var requests: List<ChatInstructionStreamPayload> = emptyList(),
  )

  private fun addMocks(httpResponse: String): CallCounter {
    val calls = CallCounter()
    val mockEngine =
      MockEngine { request ->
        if (request.url.encodedPath != "/smart-paste-stream") {
          return@MockEngine respond(
            content = "Other request",
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        calls.count++
        val requestBody = request.body.toByteArray().decodeToString()
        calls.requests += GsonUtil.createApiGson().fromJson(requestBody, ChatInstructionStreamPayload::class.java)
        respond(
          content = ByteReadChannel(httpResponse),
          status = HttpStatusCode.OK,
          headers = headersOf(HttpHeaders.ContentType, "application/json"),
        )
      }
    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    application.registerOrReplaceServiceInstance(
      DiffManager::class.java,
      MockDiffManager(),
      testRootDisposable,
    )

    return calls
  }

  @Test
  fun testInsert() =
    runBlocking {
      addMocks(INSERT_RESPONSE)
      setMathFileContent("\n")

      val manager = SmartPasteManager.getInstance(project)
      val request =
        ChatInstructionStreamPayload().apply {
          codeBlock = "def add(a, b):\n    return a + b"
        }

      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request,
        showDiff = true,
      )

      // Wait for the EDT queue to process the invokeLater call in showDiff
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      val capturedDiffRequest = (DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest()

      // Check the diff request we made to the DiffManager
      val expectedOldContent = "\n"
      val oldContent = (capturedDiffRequest?.contents?.get(0) as DocumentContent).document.text
      assertEquals(expectedOldContent, oldContent)

      val expectedNewContent = AFTER_INSERT
      val newContent = (capturedDiffRequest.contents[1] as DocumentContent).document.text
      assertEquals(expectedNewContent, newContent)
    }

  @Test
  fun testReplace() =
    runBlocking {
      addMocks(REPLACE_RESPONSE)
      setMathFileContent(AFTER_INSERT)

      val manager = SmartPasteManager.getInstance(project)
      val request =
        ChatInstructionStreamPayload().apply {
          codeBlock = "def plus(a, b):\n    return a + b"
        }

      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request,
        showDiff = true,
      )

      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      val capturedDiffRequest = (DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest()
      val expectedOldContent = AFTER_INSERT
      val oldContent = (capturedDiffRequest?.contents?.get(0) as DocumentContent).document.text
      assertEquals(expectedOldContent, oldContent)

      val expectedNewContent = AFTER_REPLACE
      val newContent = (capturedDiffRequest.contents[1] as DocumentContent).document.text
      assertEquals(expectedNewContent, newContent)
    }

  @Test
  fun testDelete() =
    runBlocking {
      addMocks(DELETE_RESPONSE)
      setMathFileContent(AFTER_REPLACE)

      val manager = SmartPasteManager.getInstance(project)
      val request =
        ChatInstructionStreamPayload().apply {
          codeBlock = "def plus(a, b):\n    return a + b"
        }

      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request,
        showDiff = true,
      )

      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      val capturedDiffRequest = (DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest()
      val expectedOldContent = AFTER_REPLACE
      val oldContent = (capturedDiffRequest?.contents?.get(0) as DocumentContent).document.text
      assertEquals(expectedOldContent, oldContent)

      val expectedNewContent = AFTER_DELETE
      val newContent = (capturedDiffRequest.contents[1] as DocumentContent).document.text
      assertEquals(expectedNewContent, newContent)
    }

  @Test
  fun testCacheNoShow() =
    runBlocking {
      val calls = addMocks(INSERT_RESPONSE)
      setMathFileContent("\n")

      val manager = SmartPasteManager.getInstance(project)
      val request =
        ChatInstructionStreamPayload().apply {
          // not used for insertion, only for cache key
          codeBlock = "def add(a, b):\n    return a + b"
        }

      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request,
        showDiff = false,
      )

      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      assertEquals(1, calls.count) // Should have called the API once
      assertNull((DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest()) // Should not have invoked the DiffManager

      // Make the same request again, this time it should be cached and not call the API
      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request,
        showDiff = false,
      )

      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      assertEquals(1, calls.count)
      assertNull((DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest())

      // Make a new request with different codeBlock, should call the API again
      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request.apply { codeBlock = "def minus(a, b):\n    return a - b" },
        showDiff = false,
      )

      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      assertEquals(2, calls.count) // Should have called the API twice now
      assertNull((DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest()) // Should still not have invoked the DiffManager
    }

  @Test
  fun testCacheThenShow() =
    runBlocking {
      val calls = addMocks(INSERT_RESPONSE)
      setMathFileContent("\n")

      val manager = SmartPasteManager.getInstance(project)
      val request =
        ChatInstructionStreamPayload().apply {
          // not used for insertion, only for cache key
          codeBlock = "def add(a, b):\n    return a + b"
        }

      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request,
        showDiff = false, // Means we shouldn't be invoking the DiffManager
      )

      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      assertEquals(1, calls.count) // Should have called the API once
      assertNull((DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest()) // Should not have invoked the DiffManager

      // Make the same request again, this time we should show the diff
      manager.processSmartPasteRequest(
        project = project,
        virtualFile = mathFile,
        request = request,
        showDiff = true,
      )
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      assertEquals(1, calls.count) // Should still only have called the API once
      assertNotNull(
        (DiffManager.getInstance() as MockDiffManager).getCapturedDiffRequest(),
      ) // Should have invoked the DiffManager this time
    }

  @Test
  fun testQueueProcessingOrder() =
    runBlocking {
      val calls = addMocks(INSERT_RESPONSE)
      setMathFileContent("\n")
      val manager = SmartPasteManager.getInstance(project)

      val callOrder = mutableListOf<String>()
      val mutex = Mutex()

      suspend fun processRequest(
        codeBlock: String,
        showDiff: Boolean,
      ) {
        mutex.withLock {
          callOrder.add(codeBlock)
        }
        manager.processSmartPasteRequest(
          project,
          mathFile,
          ChatInstructionStreamPayload().apply { this.codeBlock = codeBlock },
          showDiff,
        )
      }

      coroutineScope {
        // Queue 100 low priority requests
        for (i in 1..100) {
          launch { processRequest("request$i", false) }
        }
        // Queue one high priority request somewhere in the middle
        launch { processRequest("highPriority", true) }
      }

      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Verify FIFO order for normal requests
      assertEquals(101, calls.count)
      val expectedOrder =
        callOrder
          .filter { it != "highPriority" }

      val actualOrder =
        calls.requests
          .filter { it.codeBlock != "highPriority" }
          .map { it.codeBlock }

      assertEquals("Precompute requests should be processed in FIFO order", expectedOrder, actualOrder)

      // If the high priority request was called after at least two other requests, it should have
      // been processed before at least one of them
      val highPriorityCallIndex = callOrder.indexOf("highPriority")
      val highPriorityProcessedIndex = calls.requests.indexOfFirst { it.codeBlock == "highPriority" }
      val wasCalledAfterAtLeastTwoOtherRequests = highPriorityCallIndex >= callOrder.size - 2
      if (wasCalledAfterAtLeastTwoOtherRequests) {
        // This should almost always run
        assertTrue(
          "High priority request should be processed before at least one request that was called before it. " +
            "highPriorityCallIndex=$highPriorityCallIndex, highPriorityProcessedIndex=$highPriorityProcessedIndex",
          highPriorityProcessedIndex < highPriorityCallIndex,
        )
      }
    }
}
