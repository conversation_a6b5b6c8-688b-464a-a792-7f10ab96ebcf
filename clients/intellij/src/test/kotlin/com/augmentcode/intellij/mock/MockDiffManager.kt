package com.augmentcode.intellij.mock

import com.intellij.diff.DiffDialogHints
import com.intellij.diff.DiffManager
import com.intellij.diff.DiffRequestPanel
import com.intellij.diff.chains.DiffRequestChain
import com.intellij.diff.chains.SimpleDiffRequestChain
import com.intellij.diff.merge.MergeRequest
import com.intellij.diff.requests.DiffRequest
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.openapi.Disposable
import com.intellij.openapi.project.Project
import java.awt.Window

/**
 * Mock implementation of [DiffManager] that captures the [DiffRequest] shown.
 * In intellij it's responsible for showing the diff viewer, but in tests we just capture the request sent to it.
 */
class MockDiffManager : DiffManager() {
  private var capturedDiffRequest: SimpleDiffRequest? = null

  fun getCapturedDiffRequest(): SimpleDiffRequest? = capturedDiffRequest

  override fun showDiff(
    project: Project?,
    request: DiffRequest,
  ) {
    if (request is SimpleDiffRequest) {
      capturedDiffRequest = request
    }
  }

  override fun showDiff(
    project: Project?,
    chain: DiffRequestChain,
    hints: DiffDialogHints,
  ) {
    capturedDiffRequest =
      (chain.requests.firstOrNull() as SimpleDiffRequestChain.DiffRequestProducerWrapper).request as? SimpleDiffRequest
  }

  override fun showDiff(
    project: Project?,
    request: DiffRequest,
    hints: DiffDialogHints,
  ) {
    showDiff(project, request)
  }

  override fun createRequestPanel(
    project: Project?,
    parentDisposable: Disposable,
    window: Window?,
  ): DiffRequestPanel = throw NotImplementedError()

  override fun showMerge(
    p0: Project?,
    p1: MergeRequest,
  ): Unit = throw NotImplementedError()
}
