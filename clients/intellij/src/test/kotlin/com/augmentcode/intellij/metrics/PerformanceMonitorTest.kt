package com.augmentcode.intellij.metrics

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class PerformanceMonitorTest : AugmentBasePlatformTestCase() {
  private lateinit var mockMetricsReporter: ClientMetricsReporter

  public override fun setUp() {
    super.setUp()
    // Create a mock ClientMetricsReporter
    mockMetricsReporter = mockk<ClientMetricsReporter>(relaxed = true) // relaxed = true to avoid mocking all methods

    // Mock the static getInstance method of ClientMetricsReporter to return our mock
    // This is necessary because PerformanceMonitor calls ClientMetricsReporter.getInstance(project)
    mockkObject(ClientMetricsReporter.Companion) // Mock the companion object where getInstance is defined
    every { ClientMetricsReporter.getInstance(any()) } returns mockMetricsReporter
  }

  @Test
  fun `testLogSlowFramerate logs and reports metric`() {
    val testFps = 25.0
    val expectedMetricName = "slow_framerate" // Matches const in PerformanceMonitor
    val expectedMetricValue = testFps.toLong()

    PerformanceMonitor.logSlowFramerate(testFps)

    // Verify that reportWebviewClientMetricAsync was called on our mock
    verify {
      mockMetricsReporter.reportWebviewClientMetricAsync(
        "performance",
        expectedMetricName,
        expectedMetricValue,
      )
    }
  }

  @Test
  fun `testLogSlowINP logs and reports metric`() {
    val testInp = 300.0
    val expectedMetricName = "slow_inp" // Matches const in PerformanceMonitor
    val expectedMetricValue = testInp.toLong()

    PerformanceMonitor.logSlowINP(testInp)

    verify {
      mockMetricsReporter.reportWebviewClientMetricAsync(
        "performance",
        expectedMetricName,
        expectedMetricValue,
      )
    }
  }

  @Test
  fun `testLogSlowINP with target logs and reports metric`() {
    val testInp = 300.0
    val testTarget = "button.submit"
    val expectedMetricName = "slow_inp" // Matches const in PerformanceMonitor
    val expectedMetricValue = testInp.toLong()

    PerformanceMonitor.logSlowINP(testInp, testTarget)

    // Verification remains the same for the metric, target is for logging
    verify {
      mockMetricsReporter.reportWebviewClientMetricAsync(
        "performance",
        expectedMetricName,
        expectedMetricValue,
      )
    }
  }
}
