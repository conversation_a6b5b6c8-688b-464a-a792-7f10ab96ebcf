package com.augmentcode.intellij.guidelines

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

/**
 * Tests for the OpenWorkspaceGuidelinesCommand class.
 * This class tests the functionality of opening workspace guidelines files.
 *
 * These tests use FeatureFlagsTestUtil to mock the FeatureFlagManager's underlying API calls.
 */
@RunWith(JUnit4::class)
class OpenWorkspaceGuidelinesCommandTest : AugmentBasePlatformTestCase() {
  private lateinit var mockAugmentAppStateService: AugmentAppStateService

  private lateinit var guidelinesService: GuidelinesService

  override fun setUp() {
    super.setUp()

    // Get the guidelines service
    guidelinesService = project.service<GuidelinesService>()
  }

  /**
   * Helper method to set up feature flags for testing
   * Uses FeatureFlagsTestUtil to mock the FeatureFlagManager's underlying API calls
   */
  private fun setupFeatureFlags() {
    mockAugmentAppStateService = mockk(relaxed = true)
    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )

    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder().build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.context } returns mockContext
  }

  /**
   * Test that the action is enabled when guidelines are enabled.
   */
  @Test
  fun testActionEnabledWhenGuidelinesEnabled() {
    // Setup feature flags
    setupFeatureFlags()

    val presentation = myFixture.testAction(OpenWorkspaceGuidelinesCommand())
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify the presentation was updated to enabled (guidelines are always enabled now)
    assertTrue("Action should be enabled", presentation.isEnabled)
  }

  /**
   * Test that the action opens workspace guidelines in the editor
   */
  @Test
  fun testActionOpensWorkspaceGuidelines() {
    // Setup feature flags
    setupFeatureFlags()

    myFixture.addFileToProject(".augment-guidelines", "Existing guidelines content")
    assertEmpty(FileEditorManager.getInstance(project).openFiles)

    myFixture.testAction(OpenWorkspaceGuidelinesCommand())
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Workspace guidelines file should be opened in editor",
      openFiles.any { it.name == ".augment-guidelines" },
    )
  }

  /**
   * Test that the action correctly handles non-existent guidelines file
   * by creating it and opening it in the editor
   */
  @Test
  fun testActionCreatesAndOpensNonExistentWorkspaceGuidelines() {
    // Setup feature flags
    setupFeatureFlags()

    myFixture.testAction(OpenWorkspaceGuidelinesCommand())
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Workspace guidelines file should be opened in editor",
      openFiles.any { it.name == ".augment-guidelines" },
    )
  }
}
