package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.fileTypes.PlainTextFileType
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VFileProperty
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.PsiTestUtil
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.*
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.GetModelsResponse
import public_api.PublicApi.Language
import java.io.File

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class FileFilterStepTest : AugmentBasePlatformTestCase() {
  private lateinit var inputChannel: RoughlySizedChannel<String>
  private lateinit var memorizeChannel: RoughlySizedChannel<VirtualFile>
  private lateinit var outputChannel: RoughlySizedChannel<CoordinationFileDetails>
  private lateinit var fileFilterStep: FileFilterStep
  private lateinit var tempDir: File

  override fun getTestDataPath() = "src/test/testData/workspacemanagement/files-for-filtering"

  override fun setUp() {
    super.setUp()
    tempDir = FileUtil.createTempDirectory("test", null)
    inputChannel = spyk(RoughlySizedChannel(Channel(Channel.UNLIMITED)))
    memorizeChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    outputChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    fileFilterStep =
      FileFilterStep(
        project,
        augmentHelpers().createCoroutineScope(Dispatchers.IO),
        inputChannel,
        memorizeChannel,
        outputChannel,
      )
  }

  override fun tearDown() {
    Disposer.dispose(fileFilterStep)
    FileUtil.delete(tempDir)
    super.tearDown()
  }

  @Test
  fun testStartProcessing_startsJobOnlyOnce() {
    assertNull(fileFilterStep.processingJob)
    fileFilterStep.startProcessing()
    assertNotNull(fileFilterStep.processingJob)
    val firstJob = fileFilterStep.processingJob

    // Second call should be ignored
    fileFilterStep.startProcessing()
    assertEquals(firstJob, fileFilterStep.processingJob)
  }

  @Test
  fun testStopProcessing_stopsJob() {
    assertNull(fileFilterStep.processingJob)
    fileFilterStep.startProcessing()
    assertNotNull(fileFilterStep.processingJob)

    // Stop job
    Disposer.dispose(fileFilterStep)
    assertNull(fileFilterStep.processingJob)
  }

  @Test
  fun testProcessFilesFromChannel_handlesCommonCases() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED)

      val nonExistentPath = "/non/existent/file.txt"
      inputChannel.send(nonExistentPath)

      val psiFile = myFixture.configureByText("test.txt", "test content")
      inputChannel.send(psiFile.virtualFile.url)

      fileFilterStep.startProcessing()

      // Wait for item to be processed
      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val result = outputChannel.receive()
      assertEquals(psiFile.virtualFile.path, result.virtualFile.path)
    }

  @Test
  fun testProcessFilesFromChannel_rejectsFileWhenNotSignedIn() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.SIGN_IN_REQUIRED)

      // Create a test file
      val psiFile = myFixture.configureByText("test.txt", "test content")
      inputChannel.send(psiFile.virtualFile.url)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      // Then - no file should be sent to output channel
      assertTrue("Output channel should be empty when not signed in", outputChannel.isEmpty)
    }

  @Test
  fun testIsSupportedFileExtension_withBypassLanguageFilter() =
    runTest {
      val apiModel =
        augmentHelpers().createGetModelsResponse(
          GetModelsResponse.FeatureFlags.newBuilder()
            .setBypassLanguageFilter(true)
            .build(),
        )
      augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

      val noExtFile = myFixture.configureByText("no-extension", "test content")
      inputChannel.send(noExtFile.virtualFile.url)

      val txtFile = myFixture.configureByText("test.txt", "test content")
      inputChannel.send(txtFile.virtualFile.url)

      val unknownFile = myFixture.configureByText("test.unknown", "test content")
      inputChannel.send(unknownFile.virtualFile.url)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(3, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(noExtFile.virtualFile.path, outputFiles[0].virtualFile.path)
      assertEquals(txtFile.virtualFile.path, outputFiles[1].virtualFile.path)
      assertEquals(unknownFile.virtualFile.path, outputFiles[2].virtualFile.path)

      // Ensure receive calls block and don't loop indefinitely
      coVerify(exactly = 4) { inputChannel.receive() }
    }

  @Test
  fun testIsSupportedFileExtension_simpleWithBypassLanguageFilter() =
    runTest {
      val apiModel =
        augmentHelpers().createGetModelsResponse(
          GetModelsResponse.FeatureFlags.newBuilder()
            .setBypassLanguageFilter(true)
            .build(),
        )
      augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

      val flags = AugmentAppStateService.instance.context.flags
      val model = AugmentAppStateService.instance.context.model!!
      assertTrue(
        "Should accept any extension when bypass is enabled",
        fileFilterStep.isSupportedFileExtension(flags, model, "unknown"),
      )
      assertTrue(
        "Should accept null extension when bypass is enabled",
        fileFilterStep.isSupportedFileExtension(flags, model, null),
      )
    }

  @Test
  fun testIsSupportedFileExtension_languageFiltering() =
    runTest {
      val apiModel =
        augmentHelpers().createGetModelsResponse(
          GetModelsResponse.FeatureFlags.newBuilder()
            .setBypassLanguageFilter(false)
            .build(),
        ).toBuilder()
          .clearLanguages()
          .addAllLanguages(
            listOf(
              Language.newBuilder()
                .setName("Text")
                .addExtensions(".txt")
                .build(),
            ),
          )
          .build()
      augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

      val noExtFile = myFixture.configureByText("no-extension", "test content")
      inputChannel.send(noExtFile.virtualFile.url)

      val txtFile = myFixture.configureByText("test.txt", "test content")
      inputChannel.send(txtFile.virtualFile.url)

      val unknownFile = myFixture.configureByText("test.unknown", "test content")
      inputChannel.send(unknownFile.virtualFile.url)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(txtFile.virtualFile.path, outputFiles[0].virtualFile.path)
    }

  @Test
  fun testIsSupportedFileExtension_simpleExtensionFiltering() {
    val apiModel =
      augmentHelpers().createGetModelsResponse(
        GetModelsResponse.FeatureFlags.newBuilder()
          .setBypassLanguageFilter(false)
          .build(),
      ).toBuilder()
        .clearLanguages()
        .addAllLanguages(
          listOf(
            Language.newBuilder()
              .setName("Text")
              .addExtensions(".txt")
              .build(),
            Language.newBuilder()
              .setName("Kotlin")
              .addExtensions(".kt")
              .build(),
          ),
        )
        .build()
    augmentHelpers().forcePluginState(PluginState.ENABLED, apiModel)

    val flags = AugmentAppStateService.instance.context.flags
    val model = AugmentAppStateService.instance.context.model!!

    assertTrue(
      "Should accept supported extension",
      fileFilterStep.isSupportedFileExtension(flags, model, "txt"),
    )
    assertTrue(
      "Should accept supported extension",
      fileFilterStep.isSupportedFileExtension(flags, model, "kt"),
    )
    assertFalse(
      "Should reject unsupported extension",
      fileFilterStep.isSupportedFileExtension(flags, model, "unknown"),
    )
    assertFalse(
      "Should reject null extension",
      fileFilterStep.isSupportedFileExtension(flags, model, null),
    )
  }

  @Test
  fun testIsSupportedFileExtension_binaryFileFiltering() =
    runTest {
      augmentHelpers().forcePluginState(
        PluginState.ENABLED,
        augmentHelpers().createGetModelsResponse(
          GetModelsResponse.FeatureFlags.newBuilder()
            .setBypassLanguageFilter(false)
            .build(),
        ),
      )

      // For binary file testing, we still need to use the actual PNG file from test data
      val files =
        copyTestDataFiles(
          listOf(
            "auggie.png",
          ),
        )
      val pngFile = files["auggie.png"]!!
      val pngVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(pngFile)!!
      inputChannel.send(pngVirtualFile.url)

      // Create text file using myFixture.configureByText
      val txtFile = myFixture.configureByText("example.txt", "text content")
      inputChannel.send(txtFile.virtualFile.url)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(txtFile.virtualFile.path, outputFiles[0].virtualFile.path)
    }

  @Test
  fun testIsSupportedFileExtension_fileSizeFiltering() =
    runTest {
      augmentHelpers().forcePluginState(
        PluginState.ENABLED,
        augmentHelpers().createGetModelsResponse(
          GetModelsResponse.FeatureFlags.newBuilder()
            .setMaxUploadSizeBytes(100)
            .build(),
        ),
      )

      // Create a large file that exceeds the size limit (100+ bytes)
      val largeContent = "a".repeat(150) // 150 bytes, exceeds the 100 byte limit
      val largeFile = myFixture.configureByText("100-bytes.txt", largeContent)
      inputChannel.send(largeFile.virtualFile.url)

      // Create a small file that's within the size limit
      val smallFile = myFixture.configureByText("example.txt", "small content")
      inputChannel.send(smallFile.virtualFile.url)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)
      assertEquals(smallFile.virtualFile.path, outputFiles[0].virtualFile.path)
    }

  @Test
  fun testProcessFilesFromChannel_rejectsSymlink() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED)

      // Create a test file
      val testFile = myFixture.createFile("test.txt", "test content")

      val diffCanonicalPath = mockk<VirtualFile>(relaxed = true)
      every { diffCanonicalPath.path } returns testFile.path
      every { diffCanonicalPath.canonicalPath } returns "/different/path/test.txt" // Different from path
      every { diffCanonicalPath.fileType } returns PlainTextFileType.INSTANCE
      every { diffCanonicalPath.`is`(any()) } returns false
      every { diffCanonicalPath.length } returns 100L
      every { diffCanonicalPath.extension } returns "txt"

      val symlinkProperty = mockk<VirtualFile>(relaxed = true)
      every { symlinkProperty.path } returns testFile.path
      every { symlinkProperty.canonicalPath } returns testFile.path
      every { symlinkProperty.fileType } returns PlainTextFileType.INSTANCE
      every { symlinkProperty.`is`(VFileProperty.SYMLINK) } returns true
      every { symlinkProperty.length } returns 100L
      every { symlinkProperty.extension } returns "txt"

      assertNull(
        fileFilterStep.validateAndGetPathWithRoot(
          AugmentAppStateService.instance.context.flags,
          AugmentAppStateService.instance.context.model!!,
          diffCanonicalPath,
        ),
      )
      assertNull(
        fileFilterStep.validateAndGetPathWithRoot(
          AugmentAppStateService.instance.context.flags,
          AugmentAppStateService.instance.context.model!!,
          symlinkProperty,
        ),
      )
    }

  @Test
  fun testProcessFilesFromChannel_rejectsFileByGitignore() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED)
      val mockPathFilterService = mockk<PathFilterService>(relaxed = true)
      coEvery { mockPathFilterService.isAccepted(any<VirtualFile>()) } answers {
        firstArg<VirtualFile>().name == "example.txt"
      }
      project.registerOrReplaceServiceInstance(
        PathFilterService::class.java,
        mockPathFilterService,
        testRootDisposable,
      )

      // Create files using myFixture.configureByText
      val rejectedFile = myFixture.configureByText("100-bytes.txt", "content that will be rejected")
      inputChannel.send(rejectedFile.virtualFile.url)

      val acceptedFile = myFixture.configureByText("example.txt", "content that will be accepted")
      inputChannel.send(acceptedFile.virtualFile.url)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(acceptedFile.virtualFile.path, outputFiles[0].virtualFile.path)
    }

  @Test
  fun testProcessFilesFromChannel_rejectFileOutsideRoot() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED)

      // Create a file inside the project using myFixture
      val psiFile = myFixture.configureByText("test.txt", "test content")

      // Create a file outside of the project root
      val outsideRootDir = FileUtil.createTempDirectory("outside-if-project", null)
      val outsideOfRootFile = outsideRootDir.resolve("outside-of-root.txt")
      outsideOfRootFile.writeText("test content")
      val outsideVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(outsideOfRootFile)!!

      inputChannel.send(outsideVirtualFile.url)
      inputChannel.send(psiFile.virtualFile.url)

      fileFilterStep.startProcessing()

      waitForAssertion({
        assertTrue(inputChannel.isEmpty)
      })

      val outputFiles = waitForFiles(1, outputChannel)
      assertTrue(outputChannel.isEmpty)

      assertEquals(psiFile.virtualFile.name, outputFiles[0].virtualFile.name)
    }

  fun copyTestDataFiles(files: List<String>): MutableMap<String, File> {
    val testDataPath = myFixture.testDataPath

    val actualFiles = mutableMapOf<String, File>()
    files.forEach {
      val sourceFile = File(testDataPath, it)
      val targetFile = File(tempDir, it) // where you want to copy it
      sourceFile.copyTo(targetFile, overwrite = true)
      actualFiles[it] = targetFile
    }

    // Since we expect the files to be on disk, we need to set a content root
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)
    return actualFiles
  }

  suspend fun waitForFiles(
    expectedCount: Int,
    channel: RoughlySizedChannel<CoordinationFileDetails>,
  ): List<CoordinationFileDetails> {
    val outputFiles = mutableListOf<CoordinationFileDetails>()
    // Because we use runTest, need to wrap with withContext to use "real" time
    // The withTimeout is only needed in the event that the channel never received a file when we expect it.
    withContext(Dispatchers.Default.limitedParallelism(1)) {
      withTimeout(2000) {
        while (outputFiles.size < expectedCount) {
          outputFiles.add(channel.receive())
        }
      }
    }
    return outputFiles
  }
}
