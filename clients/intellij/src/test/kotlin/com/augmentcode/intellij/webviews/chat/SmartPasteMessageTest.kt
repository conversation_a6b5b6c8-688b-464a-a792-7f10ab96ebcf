package com.augmentcode.intellij.webviews.chat

import com.augmentcode.api.ChatInstructionStreamPayload
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.QualifiedPathName
import com.augmentcode.intellij.mock.MockDiffManager
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.HttpUtil.streamRespond
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.intellij.diff.DiffManager
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.toByteArray
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withTimeout
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

/**
 * Testing the codepath of receiving a smart paste message from the webview
 * with Index V3 enabled.
 * This does not comprehensively test the smart paste feature.
 * SmartPasteManagerTest tests the smart paste feature more comprehensively.
 */
@RunWith(JUnit4::class)
class SmartPasteMessageTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/chat"

  override fun setUp() {
    super.setUp()

    augmentHelpers().forcePluginState(
      PluginState.ENABLED,
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(true)
          .setBypassLanguageFilter(true) // This just makes it easier to test
          .build(),
      ),
    )
  }

  @Test
  fun testSmartPasteInsertApiRequest() =
    runTest {
      application.registerOrReplaceServiceInstance(
        DiffManager::class.java,
        MockDiffManager(),
        testRootDisposable,
      )
      val requestWasCorrect = CompletableDeferred<Boolean>()

      // register mock engine with /smart-paste-stream
      augmentHelpers().registerMockEngine(
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
            "/client-metrics" -> HttpUtil.respondOK(this)
            "/smart-paste-stream" -> {
              val parsedBody =
                GsonUtil.createApiGson().fromJson(
                  request.body.toByteArray().decodeToString(),
                  ChatInstructionStreamPayload::class.java,
                )
              print(parsedBody.path)
              assertEquals("print(\"Hello from Augment!\")", parsedBody.codeBlock)
              assertEquals("smartPasteReplace.go", parsedBody.path)
              requestWasCorrect.complete(true)
              streamRespond(
                coroutineScope = augmentHelpers().createCoroutineScope(Dispatchers.IO),
                chunks =
                  listOf(
                    """
                    {"text": "print(\"Hello from Augment!\")"}
                    """.trimIndent(),
                  ),
              )
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        },
      )

      // Mock AugmentRoot (Used by blob name service)
      mockkObject(AugmentRoot)
      every { AugmentRoot.findFile(any<String>()) } answers {
        val file: VirtualFile = mockk(relaxed = true)
        every { file.path } returns arg<String>(0)
        file
      }
      every { AugmentRoot.findQualifiedPathName(any(), any()) } answers {
        QualifiedPathName("", arg<VirtualFile>(1).path)
      }

      val psiFile = myFixture.configureByFile("smartPasteReplace.go")
      BlobNameService.getInstance(project).put(
        "smartPasteReplace-blob-name",
        psiFile.virtualFile.path,
      )

      // Mock findFile because files created by the test fixture don't
      // get created on disk, meaning that APIs to find files don't work in tests.
      mockkObject(AugmentRoot)
      every { AugmentRoot.findFile(any()) } answers {
        val path = arg<String>(0)
        if (path.endsWith("smartPasteReplace.go")) {
          psiFile.virtualFile
        } else {
          null
        }
      }

      val messagingService = ChatMessagingService.getInstance(project)

      val request =
        """
        {
          "type": "chat-smart-paste",
          "data": {
            "targetFile": "smartPasteReplace.go",
            "generatedCode": "print(\"Hello from Augment!\")"
          }
        }
        """.trimIndent()

      messagingService.processMessageFromWebview(request).collect()
      val validRequest =
        withTimeout(1000) {
          return@withTimeout requestWasCorrect.await()
        }
      assertTrue(validRequest)
    }
}
