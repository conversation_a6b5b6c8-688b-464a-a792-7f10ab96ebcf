package com.augmentcode.intellij.featureflags

import com.augmentcode.api.modelRegistryMap
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.extensions.PluginId
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class FeatureFlagsExtensionsTest : AugmentBasePlatformTestCase() {
  private companion object {
    const val TEST_PLUGIN_VERSION = "0.0.2"
  }

  override fun setUp() {
    super.setUp()
    augmentHelpers().mockPluginVersion(TEST_PLUGIN_VERSION)
  }

  @Test
  fun testFromModelConfig() {
    // These tests assume the test plugin version is 0.0.2
    assertEquals(TEST_PLUGIN_VERSION, "0.0.2")

    val pluginData = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
    assertNotNull(pluginData)
    assertEquals(TEST_PLUGIN_VERSION, pluginData?.version)

    val flagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        // This is before the current plugin version, so it should be true
        .setIntellijChatMultimodalMinVersion("0.0.1")
        // This is equal to the current plugin version, so it should be true
        .setIntellijAgentModeMinVersion("0.0.2")
        // This is after the current plugin version, so it should be false
        .setIntellijShareMinVersion("0.0.3")
        .setBypassLanguageFilter(true)
        .setAdditionalChatModels("""{"model1":"value1","model2":"value2"}""")
        .build()

    val getModelsResponse = augmentHelpers().createGetModelsResponse(flagsFromAPI)

    val newFlags = FeatureFlags.fromGetModelsResponse(getModelsResponse)

    assertEquals("Chat multimodal enabled should be set correctly", true, newFlags.chatMultimodalEnabled)
    assertEquals("Force completion enabled should be set correctly", true, newFlags.agentModeEnabled)
    assertEquals("Share service enabled should be set correctly", false, newFlags.shareServiceEnabled)

    assertEquals("Bypass language filter should be set correctly", true, newFlags.bypassLanguageFilter)
    assertEquals("Additional chat models should be set correctly", 2, newFlags.additionalChatModels.size)
  }

  @Test
  fun testModelRegistryParsing() {
    val flagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setEnableModelRegistry(true)
        .setModelRegistry("""{"Claude Opus": "claude-opus-id", "GPT-4": "gpt-4-id"}""")
        .build()

    val getModelsResponse = augmentHelpers().createGetModelsResponse(flagsFromAPI)

    val newFlags = FeatureFlags.fromGetModelsResponse(getModelsResponse)

    assertEquals("Enable model registry should be set correctly", true, newFlags.enableModelRegistry)
    assertEquals("Model registry should be parsed correctly", 2, newFlags.modelRegistry.size)
    assertEquals("Claude Opus model should be mapped correctly", "claude-opus-id", newFlags.modelRegistry["Claude Opus"])
    assertEquals("GPT-4 model should be mapped correctly", "gpt-4-id", newFlags.modelRegistry["GPT-4"])
  }

  @Test
  fun testModelRegistryParsingWithInvalidJson() {
    val flagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setEnableModelRegistry(true)
        .setModelRegistry("invalid json")
        .build()

    val getModelsResponse = augmentHelpers().createGetModelsResponse(flagsFromAPI)

    val newFlags = FeatureFlags.fromGetModelsResponse(getModelsResponse)

    assertEquals("Enable model registry should be set correctly", true, newFlags.enableModelRegistry)
    assertEquals("Model registry should be empty for invalid JSON", 0, newFlags.modelRegistry.size)
  }

  @Test
  fun testModelRegistryParsingWithNullValue() {
    val flagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setEnableModelRegistry(true)
        .build()

    val getModelsResponse = augmentHelpers().createGetModelsResponse(flagsFromAPI)

    val newFlags = FeatureFlags.fromGetModelsResponse(getModelsResponse)

    assertEquals("Enable model registry should be set correctly", true, newFlags.enableModelRegistry)
    assertEquals("Model registry should be empty for null value", 0, newFlags.modelRegistry.size)
  }

  @Test
  fun testModelRegistryMapMethod() {
    val flagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setModelRegistry("""{"Model A": "model-a-id", "Model B": "model-b-id"}""")
        .build()

    // Test valid JSON
    val validResult = flagsFromAPI.modelRegistryMap()
    assertEquals("Valid JSON should parse correctly", 2, validResult.size)
    assertEquals("Model A should be mapped correctly", "model-a-id", validResult["Model A"])
    assertEquals("Model B should be mapped correctly", "model-b-id", validResult["Model B"])

    // Test invalid JSON
    val invalidFlagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setModelRegistry("invalid json")
        .build()
    val invalidResult = invalidFlagsFromAPI.modelRegistryMap()
    assertEquals("Invalid JSON should return empty map", 0, invalidResult.size)

    // Test null value
    val nullFlagsFromAPI = PublicApi.GetModelsResponse.FeatureFlags.newBuilder().build()
    val nullResult = nullFlagsFromAPI.modelRegistryMap()
    assertEquals("Null value should return empty map", 0, nullResult.size)

    // Test empty string
    val emptyFlagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setModelRegistry("")
        .build()
    val emptyResult = emptyFlagsFromAPI.modelRegistryMap()
    assertEquals("Empty string should return empty map", 0, emptyResult.size)
  }

  @Test
  fun testFailingPluginLookup() {
    val flagsFromAPI =
      PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        // This is equal to the current plugin version, so it should be true
        .setIntellijAgentModeMinVersion(TEST_PLUGIN_VERSION)
        .setBypassLanguageFilter(true)
        .setAdditionalChatModels("""{"model1":"value1","model2":"value2"}""")
        .build()

    val getModelsResponse = augmentHelpers().createGetModelsResponse(flagsFromAPI)

    augmentHelpers().mockPluginVersion(null)
    val flags = FeatureFlags.fromGetModelsResponse(getModelsResponse)

    // It'll be false because we failed to lookup the plugin version
    assertEquals(flags.agentModeEnabled, false)
  }

  @Test
  fun testIsMinVersionAtLeast() {
    // These are typical version values
    assertEquals(true, FeatureFlags.isMinVersionAtLeast("0.0.2", "0.0.1"))
    assertEquals(true, FeatureFlags.isMinVersionAtLeast("0.0.2", "0.0.2"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.2", "0.0.3"))

    // These are edge cases of null or empty values
    assertEquals(false, FeatureFlags.isMinVersionAtLeast(null, "0.0.1"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.1", null))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("", "0.0.1"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.1", ""))

    // These are edge cases of invalid semver strings
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("This is not a valid semver string", "0.0.1"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.1", "This is not a valid semver string"))
  }
}
