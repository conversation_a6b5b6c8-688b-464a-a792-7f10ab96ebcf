package com.augmentcode.intellij.index

import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.intellij.mock.MockVirtualFile
import com.intellij.notebook.editor.BackedVirtualFile
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.module.Module
import com.intellij.openapi.project.BaseProjectDirectories.Companion.getBaseDirectories
import com.intellij.openapi.projectRoots.Sdk
import com.intellij.openapi.roots.OrderEnumerator
import com.intellij.openapi.roots.ProjectRootManager
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.PsiTestUtil
import com.intellij.testFramework.registerOrReplaceServiceInstance
import org.jetbrains.jps.model.module.JpsModuleSourceRootType
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentRootTest : AugmentHeavyPlatformTestCase() {
  @Suppress("UnstableApiUsage")
  @Test
  fun testBackedFile() {
    val tempDir = getVirtualFile(createTempDirectory())
    PsiTestUtil.addContentRoot(module, tempDir)
    val psiFile =
      runWriteAction {
        tempDir.createChildData(this, "file.txt")
      }
    val backedFile =
      object : MockVirtualFile("mock.projection"), BackedVirtualFile {
        override fun getOriginFile(): VirtualFile {
          return psiFile
        }
      }
    assertEquals("file.txt", AugmentRoot.findRelativePath(project, backedFile))
  }

  @Test
  fun testSiblingRoots() {
    // Create sibling directories in the project root
    val tempDir = getVirtualFile(createTempDirectory())
    val (leftRoot, rightRoot) =
      runWriteAction {
        val left = tempDir.createChildDirectory(this, "leftrepo")
        assertNotNull("Should create leftrepo", left)
        val right = tempDir.createChildDirectory(this, "rightrepo")
        assertNotNull("Should create rightrepo", right)
        Pair(left, right)
      }

    // Create a test file in the left repo
    val testFile =
      runWriteAction {
        leftRoot.createChildData(this, "test.txt")
      }

    // Configure both directories as content roots
    PsiTestUtil.addContentRoot(module, leftRoot)
    PsiTestUtil.addContentRoot(module, rightRoot)

    // Verify the setup: both directories are content roots and project base dirs
    assertEquals(2, ProjectRootManager.getInstance(project).contentRoots.size)
    assertEquals(1, ProjectRootManager.getInstance(project).contentRoots.filter { VfsUtil.isAncestor(it, testFile, true) }.size)
    assertEquals(2, project.getBaseDirectories().size)
    assertTrue(VfsUtil.isAncestor(leftRoot, testFile, true))
    assertFalse(VfsUtil.isAncestor(rightRoot, testFile, true))

    // Get the root for the nested file
    val result = AugmentRoot.findRootForProjectFile(project, testFile)
    assertNotNull(result)
    result!!
    assertEquals(leftRoot, result)
    assertFalse(VfsUtil.isAncestor(result, leftRoot, true))
    assertTrue(VfsUtil.isAncestor(result, testFile, true))
  }

  @Test
  fun testSingleRoot() {
    // Create directory in the project root
    val tempDir = getVirtualFile(createTempDirectory())
    val testFile =
      runWriteAction {
        val fooDir = tempDir.createChildDirectory(this, "foo")
        val barDir = fooDir.createChildDirectory(this, "bar")
        val bazDir = barDir.createChildDirectory(this, "baz")
        bazDir.createChildData(this, "test.txt")
      }

    // Add tempDir as contentRoot
    PsiTestUtil.addContentRoot(module, tempDir)

    // Verify the setup: tempDir is the only content root and project base dir
    assertEquals(1, ProjectRootManager.getInstance(project).contentRoots.size)
    assertEquals(tempDir, ProjectRootManager.getInstance(project).contentRoots.first())
    assertEquals(1, project.getBaseDirectories().size)
    assertEquals(tempDir, project.getBaseDirectories().first())

    // Get the root for the nested file
    val result = AugmentRoot.findRootForProjectFile(project, testFile)

    assertNotNull(result)
    assertEquals(tempDir, result)
    assertTrue(VfsUtil.isAncestor(result!!, testFile, true))
  }

  @Test
  fun testNestedRootCandidates() {
    // Create nested directory structure
    val tempDir = getVirtualFile(createTempDirectory())
    val (parentRoot, appsRoot, testFile) =
      runWriteAction {
        val parent = tempDir.createChildDirectory(this, "monorepo")
        assertNotNull("Should create monorepo", parent)
        val apps = parent.createChildDirectory(this, "apps")
        assertNotNull("Should create apps", apps)
        val bazDir = apps.createChildDirectory(this, "baz")
        val testFile = bazDir.createChildData(this, "test.txt")
        Triple(parent, apps, testFile)
      }

    // Configure both directories as content roots
    PsiTestUtil.addContentRoot(module, parentRoot)
    PsiTestUtil.addContentRoot(module, appsRoot)

    // Verify the setup: parentRoot and appsRoot are content roots. Only parentRoot is project base dir
    assertEquals(2, ProjectRootManager.getInstance(project).contentRoots.size)
    assertEquals(2, ProjectRootManager.getInstance(project).contentRoots.filter { VfsUtil.isAncestor(it, testFile, true) }.size)
    assertEquals(1, project.getBaseDirectories().size)

    // Get the root for the nested file
    val result = AugmentRoot.findRootForProjectFile(project, testFile)

    assertNotNull(result)
    result!!
    assertEquals(parentRoot, result)
    assertFalse(VfsUtil.isAncestor(result, parentRoot, true)) // dir is not its own ancestor
    assertTrue(VfsUtil.isAncestor(result, appsRoot, true))
    assertTrue(VfsUtil.isAncestor(result, testFile, true))
  }

  // Rider has different behavior than other Jetbrains IDEs and this testing framework. It does not
  // set the base directory as a content root by default. This test ensures we handle that case.
  @Test
  fun testNoContentRoot() {
    // Create directory in the project root
    val tempDir = getVirtualFile(createTempDirectory())
    PsiTestUtil.addContentRoot(module, tempDir)
    val testFile =
      runWriteAction {
        val fooDir = tempDir.createChildDirectory(this, "foo")
        val barDir = fooDir.createChildDirectory(this, "bar")
        barDir.createChildData(this, "test.txt")
      }
    // Mock ProjectRootManager to return empty content roots
    val originalRootManager = ProjectRootManager.getInstance(project)
    val mockRootManager =
      object : ProjectRootManager() {
        override fun getContentRoots(): Array<VirtualFile> = emptyArray()

        override fun getContentRootUrls(): List<String?> = emptyList()

        override fun getContentSourceRoots(): Array<VirtualFile> = emptyArray()

        override fun getModuleSourceRoots(p0: Set<JpsModuleSourceRootType<*>?>) = throw UnsupportedOperationException()

        override fun getProjectSdk(): Sdk? = throw UnsupportedOperationException()

        override fun getProjectSdkName(): String? = throw UnsupportedOperationException()

        override fun getProjectSdkTypeName(): String? = throw UnsupportedOperationException()

        override fun setProjectSdk(p0: Sdk?) = throw UnsupportedOperationException()

        override fun setProjectSdkName(
          p0: String,
          p1: String,
        ) = throw UnsupportedOperationException()

        override fun getFileIndex() = originalRootManager.fileIndex

        override fun orderEntries(): OrderEnumerator = throw UnsupportedOperationException()

        override fun orderEntries(p0: Collection<Module?>): OrderEnumerator = throw UnsupportedOperationException()

        override fun getContentRootsFromAllModules(): Array<out VirtualFile?> = throw UnsupportedOperationException()
      }
    project.registerOrReplaceServiceInstance(
      ProjectRootManager::class.java,
      mockRootManager,
      testRootDisposable,
    )
    // Verify no content roots are configured
    assertEquals(0, ProjectRootManager.getInstance(project).contentRoots.size)
    // Verify project base dir matches our temp dir
    assertEquals(1, project.getBaseDirectories().size)
    assertEquals(tempDir, project.getBaseDirectories().first())
    // Get the root for the nested file
    val result = AugmentRoot.findRootForProjectFile(project, testFile)
    // Verify the root matches the project base dir (temp dir)
    assertNotNull(result)
    assertEquals(tempDir, result)
    assertTrue(VfsUtil.isAncestor(result!!, testFile, true))
  }

  @Test
  fun testDirectoryAsInput() {
    // Create a directory structure with a .git directory to test the isDirectory=true case
    val tempDir = getVirtualFile(createTempDirectory())
    val (gitRepoDir, subDir) =
      runWriteAction {
        val repoDir = tempDir.createChildDirectory(this, "myrepo")
        assertNotNull("Should create myrepo", repoDir)
        // Create .git directory to mark this as a git repository
        val gitDir = repoDir.createChildDirectory(this, ".git")
        assertNotNull("Should create .git", gitDir)
        val subDir = repoDir.createChildDirectory(this, "src")
        assertNotNull("Should create src", subDir)
        Pair(repoDir, subDir)
      }

    // Configure the git repo directory as a content root
    PsiTestUtil.addContentRoot(module, gitRepoDir)

    // Verify the setup: gitRepoDir is a content root and contains .git
    assertEquals(1, ProjectRootManager.getInstance(project).contentRoots.size)
    assertEquals(gitRepoDir, ProjectRootManager.getInstance(project).contentRoots.first())
    assertNotNull("Should have .git directory", gitRepoDir.findChild(".git"))
    assertTrue("subDir should be a directory", subDir.isDirectory)

    // Test the key scenario: pass a directory (not a file) to findRootForProjectFile
    // This tests the case where isDirectory=true, so parent = originalFile (not originalFile.parent)
    val result = AugmentRoot.findRootForProjectFile(project, subDir)
    val resultGitRepo = AugmentRoot.findRootForProjectFile(project, gitRepoDir)

    // Verify that the method correctly found the git repository root
    assertEquals("Should find the git repository root", gitRepoDir, result)
    // Verify that the method correctly found the git repository root when passed the git repo dir itself
    assertEquals("Should find the git repository root", gitRepoDir, resultGitRepo)
  }
}
