package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.actionSystem.*
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class ToggleCompletionsActionTest : AugmentBasePlatformTestCase() {
  private lateinit var mockAugmentAppStateService: AugmentAppStateService

  private lateinit var action: ToggleCompletionsAction

  override fun setUp() {
    super.setUp()
    action = ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ToggleCompletionsAction") as ToggleCompletionsAction
    mockAugmentAppStateService = mockk(relaxed = true)
    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijAgentModeMinVersion("")
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.context } returns mockContext
  }

  private fun createEvent(): AnActionEvent {
    val presentation = Presentation()
    val dataContext = DataContext { presentation }
    @Suppress("DEPRECATION")
    return AnActionEvent.createFromDataContext(ActionPlaces.UNKNOWN, presentation, dataContext)
  }

  private fun doAction(event: AnActionEvent) {
    action.actionPerformed(event)
    action.update(event)
  }

  @Test
  fun testToggle() {
    val event = createEvent()
    doAction(event)
    assertEquals(false, AugmentSettings.instance.inlineCompletionEnabled)
    assertEquals(AugmentBundle.message("actions.completions.enable"), event.presentation.text)
    doAction(event)
    assertEquals(true, AugmentSettings.instance.inlineCompletionEnabled)
    assertEquals(AugmentBundle.message("actions.completions.disable"), event.presentation.text)
  }
}
