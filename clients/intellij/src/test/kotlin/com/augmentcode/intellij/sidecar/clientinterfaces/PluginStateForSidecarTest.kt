package com.augmentcode.intellij.sidecar.clientinterfaces

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.sidecar.rpc.clientInterfaces.GetStateValueRequest
import com.augmentcode.sidecar.rpc.clientInterfaces.PluginStateScope
import com.augmentcode.sidecar.rpc.clientInterfaces.SetStateValueRequest
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class PluginStateForSidecarTest : AugmentBasePlatformTestCase() {
  @Test
  fun testGettingUnknownGlobalState() {
    val key = "unknown-key"

    val pluginState = PluginStateForSidecar()
    val emptyResponse =
      pluginState.handleGetRequest(
        GetStateValueRequest.newBuilder()
          .setScope(PluginStateScope.GLOBAL)
          .setKey(key)
          .build(),
      )
    assertEquals("", emptyResponse.jsonValue)
  }

  @Test
  fun testGettingKnownProjectState() {
    val key = "known-key"

    val pluginState = PluginStateForSidecar()
    GlobalPluginStateForSidecar.instance.setValue(key, "test-value")
    val valueResponse =
      pluginState.handleGetRequest(
        GetStateValueRequest.newBuilder()
          .setScope(PluginStateScope.GLOBAL)
          .setKey(key)
          .build(),
      )
    assertEquals("test-value", valueResponse.jsonValue)
  }

  @Test
  fun testSettingNewValueGlobalState() {
    val key = "test-key"
    val pluginState = PluginStateForSidecar()

    // Check the key is initially not set
    val emptyResponse =
      pluginState.handleGetRequest(
        GetStateValueRequest.newBuilder()
          .setScope(PluginStateScope.GLOBAL)
          .setKey(key)
          .build(),
      )
    assertEquals("", emptyResponse.jsonValue)

    // Set the value
    pluginState.handleSetRequest(
      SetStateValueRequest.newBuilder()
        .setScope(PluginStateScope.GLOBAL)
        .setKey(key)
        .setJsonValue("test-value")
        .build(),
    )

    // Check the value was actually set
    val valueResponse =
      pluginState.handleGetRequest(
        GetStateValueRequest.newBuilder()
          .setScope(PluginStateScope.GLOBAL)
          .setKey(key)
          .build(),
      )
    assertEquals("test-value", valueResponse.jsonValue)
  }

  @Test
  fun testOverwritingValueGlobalState() {
    val key = "test-key"
    val pluginState = PluginStateForSidecar()
    GlobalPluginStateForSidecar.instance.setValue(key, "test-value")

    // Change the value
    pluginState.handleSetRequest(
      SetStateValueRequest.newBuilder()
        .setScope(PluginStateScope.GLOBAL)
        .setKey(key)
        .setJsonValue("updated-value")
        .build(),
    )

    // Check the value was actually set
    val valueResponse =
      pluginState.handleGetRequest(
        GetStateValueRequest.newBuilder()
          .setScope(PluginStateScope.GLOBAL)
          .setKey(key)
          .build(),
      )
    assertEquals("updated-value", valueResponse.jsonValue)
  }
}
