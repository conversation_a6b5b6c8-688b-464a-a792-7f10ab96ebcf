package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

class DeleteFileSyncTest : SyncTestCase() {
  fun testDeleteFile() =
    runBlocking {
      val state = MockEngineState()
      val mockEngine = createMockEngine(state)
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val tempDir = getVirtualFile(createTempDirectory())
      PsiTestUtil.addContentRoot(module, tempDir)

      // Add file
      val file =
        runWriteAction {
          tempDir.createChildData(this, "foo.txt").also {
            VfsUtil.saveText(it, "foo")
          }
        }
      val blobState = AugmentBlobStateReader.read(file.toPsiFile(project))
      assertNotNull(blobState)
      val blobName = blobState!!.remoteName

      // Wait for upload
      waitForRequests(mockEngine, "/batch-upload", 1)

      // Wait for checkpoint
      waitForRequests(mockEngine, "/checkpoint-blobs", 1)

      // Delete the file
      WriteCommandAction.runWriteCommandAction(project) {
        file.delete(this)
      }

      // Wait for deletion to be processed
      delay(1000)

      // Verify the synchronizedBlobsPayload contains expected data
      val syncManager = AugmentRemoteSyncingManager.getInstance(project)
      val payload = syncManager.synchronizedBlobsPayload()

      assertEquals("checkpoint-1", payload.checkpointId)
      assertTrue(payload.addedBlobs.isEmpty())
      assertEquals(setOf(blobName), payload.deletedBlobs)
    }
}
