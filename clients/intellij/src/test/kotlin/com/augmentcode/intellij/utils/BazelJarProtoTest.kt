package com.augmentcode.intellij.utils

import org.junit.Test
import public_api.PublicApi.RecordRequestEventsRequest
import request_insight.RequestInsight.RequestEvent

/**
 * Test to verify that the fat JAR approach correctly includes dependency proto classes.
 * This test specifically verifies that we can use request_insight.RequestEvent classes
 * that are imported by public_api.proto but would be missing in a JAR generated by
 * a `java_proto_library` bazel rule.
 */
class BazelJarProtoTest {
  @Test
  fun testCanUseRequestInsightClasses() {
    // This test verifies that dependency proto classes are available
    // In a skinny JAR approach, this would fail with ClassNotFoundException

    // Create a simple RequestMetadata event (which is one of the oneof options)
    val requestMetadata =
      request_insight.RequestInsight.RequestMetadata.newBuilder()
        .setSessionId("test-session")
        .setUserId("test-user")
        .build()

    val requestEvent =
      RequestEvent.newBuilder()
        .setRequestMetadata(requestMetadata)
        .build()

    val recordRequest =
      RecordRequestEventsRequest.newBuilder()
        .addEvents(requestEvent)
        .build()

    assert(recordRequest.eventsCount == 1)
    assert(recordRequest.getEvents(0).hasRequestMetadata())
    assert(recordRequest.getEvents(0).requestMetadata.sessionId == "test-session")
  }

  @Test
  fun testPublicApiClassesStillWork() {
    // Verify that the main proto classes still work
    val featureFlags =
      public_api.PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
        .setEnableChat(true)
        .build()

    assert(featureFlags.enableChat)
  }
}
