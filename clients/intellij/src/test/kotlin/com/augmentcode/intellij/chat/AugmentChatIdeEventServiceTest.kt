
import com.augmentcode.intellij.chat.AugmentChatIdeEventService
import com.augmentcode.intellij.chat.EditorTrackingService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertionAsync
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.rpc.CurrentlyOpenFiles
import com.augmentcode.rpc.FileRangesSelected
import com.google.protobuf.Message
import com.intellij.ide.ui.UISettings
import com.intellij.ide.ui.UISettingsListener
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.fileEditor.FileEditorManager
import kotlinx.coroutines.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentChatIdeEventServiceTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/chat"

  override fun setUp() {
    super.setUp()

    AugmentChatIdeEventService.getInstance(project).registerListeners()
    EditorTrackingService.getInstance(project).registerListeners()
  }

  // We wait for the message to be posted.
  // We have a 10s timeout in case this is a failing test and the
  // message was not posted. Let's not block the test forever and
  // waste CI time.
  private fun runAndAssertMessages(
    fn: () -> Unit,
    predicate: (List<Message>) -> Unit,
    timeoutMs: Long = 10000,
  ) {
    return runBlocking {
      val messages = mutableListOf<Message>()
      val connection = project.messageBus.connect(testRootDisposable)
      try {
        connection.subscribe(
          ChatWebviewMessageBus.CHAT_WEBVIEW_MESSAGE_TOPIC,
          object : ChatWebviewMessageBus {
            override fun postMessageToWebview(message: Message) {
              messages.add(message)
            }

            override fun reloadIntellijStyles() {}
          },
        )

        // Once we have the listeners setup, call the function that will cause messages to be posted
        fn()

        runBlocking {
          waitForAssertionAsync({
            predicate(messages)
          }, timeoutMs)
        }
      } finally {
        connection.disconnect()
      }
    }
  }

  @Test
  fun testSendCurrentlyOpenedFiles() {
    myFixture.configureByFile("example-editor-file1.txt")
    runAndAssertMessages(
      {
        EditorTrackingService.getInstance(project).sendCurrentlyOpenedFiles()
      },
      { messages ->
        val filteredMessages = messages.filterIsInstance<CurrentlyOpenFiles>()
        assert(filteredMessages.isNotEmpty())
        val msg = filteredMessages.first()
        assertEquals("example-editor-file1.txt", msg.dataList.first().pathName)
      },
    )
  }

  @Test
  fun testSelectionChanged() {
    myFixture.configureByFile("example-editor-file1.txt")
    val editor = FileEditorManager.getInstance(project).selectedTextEditor!!
    val caret = editor.caretModel.primaryCaret

    runAndAssertMessages(
      {
        caret.setSelection(8, 24)
      },
      { messages ->
        val filteredMessages = messages.filterIsInstance<FileRangesSelected>()
        assert(filteredMessages.isNotEmpty())
        val msg = filteredMessages.first()
        val range = msg.dataList.first().fullRange
        assertEquals(1, range.startLineNumber)
        assertEquals(3, range.endLineNumber)
      },
    )
  }

  @Test
  fun testSelectionChangedEmpty() {
    myFixture.configureByFile("example-editor-file1.txt")
    val editor = FileEditorManager.getInstance(project).selectedTextEditor!!
    val caret = editor.caretModel.primaryCaret
    caret.setSelection(8, 24)

    runAndAssertMessages(
      {
        caret.setSelection(24, 24)
      },
      { messages ->
        val filteredMessages = messages.filterIsInstance<FileRangesSelected>()
        assert(filteredMessages.isNotEmpty())
        val msg = filteredMessages.first()
        assertEquals(0, msg.dataList.size)
      },
    )
  }

  @Test
  fun testCurrentlyOpenFileChanged() {
    myFixture.configureByFile("example-editor-file1.txt")
    runAndAssertMessages(
      {
        myFixture.configureByFile("example-editor-file2.txt")
      },
      { messages ->
        val filteredMessages = messages.filterIsInstance<CurrentlyOpenFiles>()
        assert(filteredMessages.isNotEmpty())
        val msg = filteredMessages.last()
        assertEquals("example-editor-file2.txt", msg.dataList.first().pathName)
      },
    )
  }

  @Test
  fun testCurrentlyOpenFileClosed() {
    myFixture.configureByFile("example-editor-file1.txt")
    runAndAssertMessages(
      {
        val editor = FileEditorManager.getInstance(project).selectedTextEditor!!
        FileEditorManager.getInstance(project).closeFile(editor.virtualFile)
      },
      { messages ->
        val filteredMessages = messages.filterIsInstance<CurrentlyOpenFiles>()
        assert(filteredMessages.isNotEmpty())
        val msg = filteredMessages.first()
        assertEquals(0, msg.dataList.size)
      },
    )
  }

  @Test
  fun testReloadIntellijStyles() {
    runBlocking {
      var callCount = 0
      project.messageBus.connect(testRootDisposable).subscribe(
        ChatWebviewMessageBus.CHAT_WEBVIEW_MESSAGE_TOPIC,
        object : ChatWebviewMessageBus {
          override fun postMessageToWebview(message: Message) {
          }

          override fun reloadIntellijStyles() {
            callCount++
          }
        },
      )

      ApplicationManager
        .getApplication()
        .messageBus
        .syncPublisher(UISettingsListener.TOPIC)
        .uiSettingsChanged(UISettings.getInstance())

      runBlocking {
        waitForAssertionAsync({
          assertTrue(callCount > 0)
        })
      }
    }
  }

  @Test
  fun testSelectionExpandsToEndOfLine() {
    myFixture.configureByFile("example-editor-file1.txt")
    val editor = FileEditorManager.getInstance(project).selectedTextEditor!!
    val caret = editor.caretModel.primaryCaret

    // Select only part of a line (not the full line)
    val lineStartOffset = editor.document.getLineStartOffset(1)
    val midLineOffset = lineStartOffset + 5 // Select from position 5 in the line
    val lineEndOffset = editor.document.getLineEndOffset(1) - 2 // Select to 2 chars before end of line

    runAndAssertMessages(
      {
        caret.setSelection(midLineOffset, lineEndOffset)
      },
      { messages ->
        val filteredMessages = messages.filterIsInstance<FileRangesSelected>()
        assert(filteredMessages.isNotEmpty())
        val msg = filteredMessages.first()
        val range = msg.dataList.first().fullRange

        // Verify that selection is expanded to full line boundaries
        assertEquals(1, range.startLineNumber)
        assertEquals(0, range.startColumn) // Should start at beginning of line
        assertEquals(1, range.endLineNumber)
        assertEquals(
          editor.document.getLineEndOffset(1) -
            editor.document.getLineStartOffset(1),
          range.endColumn,
        )
        // Should end at end of line
      },
    )
  }

  @Test
  fun testSelectionExcludesEmptyEndLine() {
    myFixture.configureByFile("example-editor-file1.txt")
    val editor = FileEditorManager.getInstance(project).selectedTextEditor!!
    val caret = editor.caretModel.primaryCaret

    // Get line offsets for lines 1 and 3
    val line1StartOffset = editor.document.getLineStartOffset(1)
    val line3StartOffset = editor.document.getLineStartOffset(3)

    // Select from middle of line 1 to beginning of line 3
    // This tests the case where the cursor is at position 0 of a line
    // but we don't want to include that line in the selection
    runAndAssertMessages(
      {
        caret.setSelection(line1StartOffset + 5, line3StartOffset)
      },
      { messages ->
        val filteredMessages = messages.filterIsInstance<FileRangesSelected>()
        assert(filteredMessages.isNotEmpty())
        val msg = filteredMessages.first()
        val range = msg.dataList.first().fullRange

        // Verify that selection excludes line 3 (the empty end line)
        assertEquals(1, range.startLineNumber)
        assertEquals(0, range.startColumn) // Should start at beginning of line 1
        assertEquals(2, range.endLineNumber) // Should end at line 2, not include line 3
        assertEquals(
          editor.document.getLineEndOffset(2) -
            editor.document.getLineStartOffset(2),
          range.endColumn,
        ) // Should end at end of line 2
      },
    )
  }
}
