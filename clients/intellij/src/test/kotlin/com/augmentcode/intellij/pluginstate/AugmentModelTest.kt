package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.GetModelsResponse

@RunWith(JUnit4::class)
class AugmentModelTest : AugmentBasePlatformTestCase() {
  @Test
  fun testFromGetModelsResponse() {
    val getModelsResponse =
      GetModelsResponse.newBuilder()
        .setDefaultModel("default-model")
        .addAllLanguages(
          listOf(
            public_api.PublicApi.Language.newBuilder()
              .setName("Text")
              .addExtensions(".txt")
              .build(),
            public_api.PublicApi.Language.newBuilder()
              .setName("Go")
              .addExtensions(".go")
              .build(),
            public_api.PublicApi.Language.newBuilder()
              .setName("XML")
              .addExtensions(".xml")
              .build(),
            public_api.PublicApi.Language.newBuilder()
              .setName("Java")
              .addExtensions(".java")
              .build(),
          ),
        )
        .addAllModels(
          listOf(
            public_api.PublicApi.Model.newBuilder()
              .setName("default-model")
              .setSuggestedPrefixCharCount(123)
              .setSuggestedSuffixCharCount(456)
              .build(),
            public_api.PublicApi.Model.newBuilder()
              .setName("alternative-model")
              .setSuggestedPrefixCharCount(9192)
              .setSuggestedSuffixCharCount(9192)
              .build(),
          ),
        )
        .setFeatureFlags(GetModelsResponse.FeatureFlags.getDefaultInstance())
        .build()

    val model = AugmentModel.fromGetModelsResponse(getModelsResponse)
    assertEquals(
      model,
      AugmentModel(
        defaultModel = "default-model",
        supportedFileExtensions = setOf("txt", "java", "xml", "go"),
        extensionToLanguageLookup =
          mapOf(
            "txt" to "Text",
            "java" to "Java",
            "xml" to "XML",
            "go" to "Go",
          ),
        userTier = GetModelsResponse.UserTier.UNKNOWN,
        suggestedPrefixCharCount = 123,
        suggestedSuffixCharCount = 456,
      ),
    )
  }
}
