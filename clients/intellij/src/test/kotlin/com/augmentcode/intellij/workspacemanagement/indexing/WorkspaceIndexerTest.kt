package com.augmentcode.intellij.workspacemanagement.indexing

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Path
import java.nio.file.Paths
import kotlin.io.path.absolutePathString

@RunWith(JUnit4::class)
class WorkspaceIndexerTest : AugmentBasePlatformTestCase() {
  @Test
  fun testIndexer() {
    val mockWorkspaceCoordinatorService = mockk<WorkspaceCoordinatorService>(relaxed = true)
    project.registerOrReplaceServiceInstance(
      WorkspaceCoordinatorService::class.java,
      mockWorkspaceCoordinatorService,
      testRootDisposable,
    )

    val psiFile = myFixture.configureByText("foo.txt", "Hello Worm")
    val path = myFixture.file.virtualFile.url

    // Forces WorkspaceIndexer to run
    val indexedPath = augmentHelpers().forceIndexingForFile(myFixture.file.virtualFile)
    val pathPathsGet = Paths.get(psiFile.virtualFile.path)
    println(pathPathsGet.absolutePathString())
    assertEquals(path, indexedPath)
    verify(exactly = 1) { mockWorkspaceCoordinatorService.enqueueFileForProcessing(path) }

    // alter file
    WriteCommandAction.runWriteCommandAction(project) {
      val document = FileDocumentManager.getInstance().getDocument(psiFile.virtualFile)
      document!!.setText("Modified content")
    }

    val indexedPath2 = augmentHelpers().forceIndexingForFile(myFixture.file.virtualFile)
    assertEquals(path, indexedPath2)
    verify(exactly = 2) { mockWorkspaceCoordinatorService.enqueueFileForProcessing(path) }
  }
}
