package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertionAsync
import com.augmentcode.intellij.testutils.waitForAssertionSuspend
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.*
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.util.Collections

@RunWith(JUnit4::class)
class ProjectStateExecutorTest : AugmentBasePlatformTestCase() {
  @Test
  fun testUpdateStateSignedOut() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.SIGN_IN_REQUIRED)

      var ctx: PluginContext? = null
      var state: PluginState? = null

      val executor =
        ProjectStateExecutor(project, augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          ctx = c
          state = s
        }
      executor.updateState()

      waitForAssertionAsync({
        assertEquals(state, PluginState.SIGN_IN_REQUIRED)
        assertEquals(ctx, AppStateExecutor.defaultContext)
      })
    }

  @Test
  fun testUpdateStateSignedIn() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED, HttpUtil.defaultMockGetModelsResponse)

      // mock sidecar service to speed up tests
      project.registerOrReplaceServiceInstance(
        SidecarService::class.java,
        mockk<SidecarService>(relaxed = true),
        testRootDisposable,
      )

      var ctx: PluginContext? = null
      var state: PluginState? = null

      val executor =
        ProjectStateExecutor(project, augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          ctx = c
          state = s
        }
      executor.updateState()

      waitForAssertionAsync({
        assertEquals(PluginState.ENABLED, state)
        assertEquals(
          PluginContext(
            true,
            FeatureFlags.fromGetModelsResponse(HttpUtil.defaultMockGetModelsResponse),
            AugmentModel.fromGetModelsResponse(HttpUtil.defaultMockGetModelsResponse),
          ),
          ctx,
        )
      })
    }

  @Test
  fun testSidecarFailure() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED, HttpUtil.defaultMockGetModelsResponse)

      // mock sidecar service to speed up tests
      val sidecarServiceMock = mockk<SidecarService>(relaxed = true)
      coEvery { sidecarServiceMock.startServer() } throws Exception("Failed to start sidecar")
      project.registerOrReplaceServiceInstance(
        SidecarService::class.java,
        sidecarServiceMock,
        testRootDisposable,
      )

      var ctx: PluginContext? = null
      var state: PluginState? = null

      val executor =
        ProjectStateExecutor(project, augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          ctx = c
          state = s
        }
      executor.updateState()

      waitForAssertionAsync({
        assertEquals(PluginState.ENABLED, state)
        assertEquals(
          PluginContext(
            true,
            FeatureFlags.fromGetModelsResponse(HttpUtil.defaultMockGetModelsResponse),
            AugmentModel.fromGetModelsResponse(HttpUtil.defaultMockGetModelsResponse),
          ),
          ctx,
        )
      })
    }

  @Test
  fun testUpdateStateMultipleCalls() =
    runTest {
      augmentHelpers().forcePluginState(PluginState.ENABLED, HttpUtil.defaultMockGetModelsResponse)

      // mock sidecar service to speed up tests
      project.registerOrReplaceServiceInstance(
        SidecarService::class.java,
        mockk<SidecarService>(relaxed = true),
        testRootDisposable,
      )

      // This deferred is used to control when the /get-models request is responded to
      val deferred = CompletableDeferred<Unit>()

      // mock sidecar service to speed up tests
      val sidecarServiceMock = mockk<SidecarService>(relaxed = true)
      coEvery { sidecarServiceMock.startServer() } answers {
        runBlocking {
          deferred.await()
        }
      }
      project.registerOrReplaceServiceInstance(
        SidecarService::class.java,
        sidecarServiceMock,
        testRootDisposable,
      )

      var ctx: PluginContext? = null
      var state: PluginState? = null

      val states = Collections.synchronizedList(mutableListOf<PluginState>())
      val executor =
        ProjectStateExecutor(project, augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          synchronized(states) {
            states.add(s)
          }
          ctx = c
          state = s
        }

      executor.updateState()

      // Make sure the first call to /get-models was made
      waitForAssertionSuspend({
        coVerify(exactly = 1) { sidecarServiceMock.startServer() }
      })

      // Calling update 5 times should result in an extra run of the executor
      // once it's current execution is done.
      executor.updateState()
      executor.updateState()
      executor.updateState()
      executor.updateState()
      executor.updateState()

      // Ensure state is what we expect
      assertEquals(null, state)
      assertEquals(null, ctx)

      // Allow the current execution to finish
      deferred.complete(Unit)

      // We should see the first call become enabled, followed by a second call to /get-models because the executor
      // should have been triggered again due to the updateState() calls
      waitForAssertionAsync({
        assertEquals(
          listOf(
            // Initial request
            PluginState.ENABLED,
            // Follow-up request
            PluginState.ENABLED,
          ),
          synchronized(states) {
            states
          },
        )
      })

      // The state should have updated to Enabled
      assertEquals(PluginState.ENABLED, state)
      assertEquals(
        PluginContext(
          true,
          FeatureFlags.fromGetModelsResponse(HttpUtil.defaultMockGetModelsResponse),
          AugmentModel.fromGetModelsResponse(HttpUtil.defaultMockGetModelsResponse),
        ),
        ctx,
      )
    }
}
