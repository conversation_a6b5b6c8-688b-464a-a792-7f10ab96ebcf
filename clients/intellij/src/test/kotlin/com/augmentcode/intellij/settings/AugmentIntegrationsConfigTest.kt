package com.augmentcode.intellij.settings

import com.augmentcode.rpc.GetStoredMCPServersResponse
import com.augmentcode.rpc.MCPServer
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentIntegrationsConfigTest {
  @Test
  fun testMcpServersForSidecarWithEnvironmentVariables() {
    // Create a test instance of AugmentIntegrationsConfig
    val config = AugmentIntegrationsConfig()

    // Create a test MCPServer with environment variables
    val server =
      MCPServer.newBuilder()
        .setId("test-id")
        .setName("test-server")
        .setCommand("test-command")
        .setArguments("")
        .setUseShellInterpolation(true)
        .putEnv("KEY1", "VALUE1")
        .putEnv("KEY2", "VALUE2")
        .setDisabled(false)
        .setType("stdio")
        .build()

    // Set the MCPServer in the config
    val response =
      GetStoredMCPServersResponse.newBuilder()
        .addData(server)
        .build()
    config.mcpServers = response

    // Get the McpServerConfig from the config
    val sidecarServers = config.mcpServersForSidecar

    // Verify the result
    assertEquals(1, sidecarServers.size)
    val sidecarServer = sidecarServers[0]

    // Verify the basic properties
    assertEquals("test-server", sidecarServer.name)
    assertEquals("test-command", sidecarServer.command)
    assertTrue(sidecarServer.useShellInterpolation)
    assertEquals("stdio", sidecarServer.type)

    // Verify the environment variables
    assertEquals(2, sidecarServer.envMap.size)
    assertEquals("VALUE1", sidecarServer.envMap["KEY1"])
    assertEquals("VALUE2", sidecarServer.envMap["KEY2"])
  }

  @Test
  fun testMcpServersForSidecarWithoutEnvironmentVariables() {
    // Create a test instance of AugmentIntegrationsConfig
    val config = AugmentIntegrationsConfig()

    // Create a test MCPServer without environment variables
    val server =
      MCPServer.newBuilder()
        .setId("test-id")
        .setName("test-server")
        .setCommand("test-command")
        .setArguments("")
        .setUseShellInterpolation(true)
        .setDisabled(false)
        .setType("stdio")
        .build()

    // Set the MCPServer in the config
    val response =
      GetStoredMCPServersResponse.newBuilder()
        .addData(server)
        .build()
    config.mcpServers = response

    // Get the McpServerConfig from the config
    val sidecarServers = config.mcpServersForSidecar

    // Verify the result
    assertEquals(1, sidecarServers.size)
    val sidecarServer = sidecarServers[0]

    // Verify the basic properties
    assertEquals("test-server", sidecarServer.name)
    assertEquals("test-command", sidecarServer.command)
    assertTrue(sidecarServer.useShellInterpolation)
    assertEquals("stdio", sidecarServer.type)

    // Verify that there are no environment variables
    assertTrue(sidecarServer.envMap.isEmpty())
  }

  @Test
  fun testMcpServersForSidecarWithHttpServer() {
    // Create a test instance of AugmentIntegrationsConfig
    val config = AugmentIntegrationsConfig()

    // Create a test HTTP MCPServer
    val server =
      MCPServer.newBuilder()
        .setId("test-http-id")
        .setName("test-http-server")
        .setUrl("https://api.example.com/mcp")
        .setType("http")
        .setDisabled(false)
        .build()

    // Set the MCPServer in the config
    val response =
      GetStoredMCPServersResponse.newBuilder()
        .addData(server)
        .build()
    config.mcpServers = response

    // Get the McpServerConfig from the config
    val sidecarServers = config.mcpServersForSidecar

    // Verify the result
    assertEquals(1, sidecarServers.size)
    val sidecarServer = sidecarServers[0]

    // Verify the basic properties
    assertEquals("test-http-server", sidecarServer.name)
    assertEquals("https://api.example.com/mcp", sidecarServer.url)
    assertEquals("http", sidecarServer.type)
    assertFalse(sidecarServer.disabled)
  }
}
