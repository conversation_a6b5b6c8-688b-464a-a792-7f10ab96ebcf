package com.augmentcode.intellij.ignore

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertNull
import junit.framework.TestCase.assertTrue
import junit.framework.TestCase.fail
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class GitIgnoreTest : AugmentBasePlatformTestCase() {
  @Test
  fun testFullRangeExpression() {
    // This expression contains all features described in the examples below to ensure the parser can handle it
    val input =
      "*.log\n" +
        "!\\#important?/debug[0-9]/debug[!01]/**/*debug[a-z]/*.log"
    val gitIgnore = GitIgnore(input)
    assertIgnore(gitIgnore, "logs/debug.log")
    assertNullMatch(gitIgnore, "#important_/debug4/debug4/something/something/local_debugb/Something.logxxx")
  }

  // I used the examples provided by Atlassian in their gitignore tutorial as test cases
  // https://www.atlassian.com/git/tutorials/saving-changes/gitignore
  // This documentation is licensed via  https://creativecommons.org/licenses/by/2.5/au/

  @Test
  fun testPrependGlobstar1() {
    // You can prepend a pattern with a double asterisk to match directories anywhere in the repository.
    val gitIgnore = GitIgnore("**/logs")
    assertIgnore(gitIgnore, "logs/debug.log")
    assertIgnore(gitIgnore, "logs/monday/foo.bar")
    assertIgnore(gitIgnore, "build/logs/debug.log")
  }

  @Test
  fun testPrependGlobstar2() {
    // You can also use a double asterisk to match files based on their name and the name of their parent directory.
    val gitIgnore = GitIgnore("**/logs/debug.log")
    assertIgnore(gitIgnore, "logs/debug.log")
    assertIgnore(gitIgnore, "build/logs/debug.log")
    assertNotIgnore(gitIgnore, "logs/build/debug.log")
  }

  @Test
  fun testWildcard() {
    // An asterisk is a wildcard that matches zero or more characters.
    val gitIgnore = GitIgnore("*.log")
    assertIgnore(gitIgnore, "debug.log")
    assertIgnore(gitIgnore, "foo.log")
    assertIgnore(gitIgnore, ".log")
    assertIgnore(gitIgnore, "logs/debug.log")
  }

  @Test
  fun testNegate() {
    // Negate a pattern by starting it with an exclamation point.
    val gitIgnore = GitIgnore("*.log\n!important.log")
    assertIgnore(gitIgnore, "debug.log")
    assertIgnore(gitIgnore, "foo.log")
    assertNotIgnore(gitIgnore, "important.log")
  }

  @Test
  fun testNegateAndReignore() {
    // Patterns defined after a negating pattern will re-ignore any previously negated files.
    val gitIgnore = GitIgnore("*.log\n!important/*.log\ntrace.*")
    assertIgnore(gitIgnore, "debug.log")
    assertIgnore(gitIgnore, "important/trace.log")
    assertNotIgnore(gitIgnore, "important/debug.log")
  }

  @Test
  fun testPinRoot() {
    // Prepending a slash matches files only in the repository root.
    val gitIgnore = GitIgnore("/debug.log")
    assertIgnore(gitIgnore, "debug.log")
    assertNotIgnore(gitIgnore, "logs/debug.log")
  }

  @Test
  fun testdefaultAnyDirectory() {
    // By default, patterns match files in any directory
    val gitIgnore = GitIgnore("debug.log")
    assertIgnore(gitIgnore, "debug.log")
    assertIgnore(gitIgnore, "logs/debug.log")
  }

  @Test
  fun testSingleCharWildcard() {
    // A question mark matches exactly one character.
    val gitIgnore = GitIgnore("debug?.log")
    assertIgnore(gitIgnore, "debug0.log")
    assertIgnore(gitIgnore, "debugg.log")
    assertNotIgnore(gitIgnore, "debug10.log")
  }

  @Test
  fun testCharRange() {
    // Square brackets can also be used to match a single character from a specified range.
    val gitIgnore = GitIgnore("debug[0-9].log")
    assertIgnore(gitIgnore, "debug0.log")
    assertIgnore(gitIgnore, "debug1.log")
    assertNotIgnore(gitIgnore, "debug10.log")
  }

  @Test
  fun testCharSet() {
    // Square brackets match a single character form the specified set.
    val gitIgnore = GitIgnore("debug[01].log")
    assertIgnore(gitIgnore, "debug0.log")
    assertIgnore(gitIgnore, "debug1.log")
    assertNotIgnore(gitIgnore, "debug2.log")
    assertNotIgnore(gitIgnore, "debug01.log")
  }

  @Test
  fun testNotCharSet() {
    // An exclamation mark can be used to match any character except one from the specified set.
    val gitIgnore = GitIgnore("debug[!01].log")
    assertIgnore(gitIgnore, "debug2.log")
    assertNotIgnore(gitIgnore, "debug0.log")
    assertNotIgnore(gitIgnore, "debug1.log")
    assertNotIgnore(gitIgnore, "debug01.log")
  }

  @Test
  fun testCharRangeAlpabetical() {
    // Ranges can be numeric or alphabetic.
    val gitIgnore = GitIgnore("debug[a-z].log")
    assertIgnore(gitIgnore, "debuga.log")
    assertIgnore(gitIgnore, "debugb.log")
    assertNotIgnore(gitIgnore, "debug1.log")
  }

  @Test
  fun testNoSlashMatchDirsAndFiles() {
    // If a pattern does not contain a slash, it will apply to both files and directories in any directory.
    val gitIgnore = GitIgnore("debug.log")
    assertIgnore(gitIgnore, "debug.log")
    assertIgnore(gitIgnore, "logs/debug.log")
  }

  @Test
  fun testAppendSlash() {
    // Appending a slash indicates the pattern is a directory.
    // The entire contents of any directory in the repository matching that name –
    // including all of its files and subdirectories – will be ignored
    val gitIgnore = GitIgnore("logs/")
    assertIgnore(gitIgnore, "logs/")
    assertIgnore(gitIgnore, "logs/debug.log")
    assertIgnore(gitIgnore, "logs/latest/foo.bar")
    assertIgnore(gitIgnore, "build/logs/foo.bar")
    assertIgnore(gitIgnore, "build/logs/latest/debug.log")
  }

  @Test
  fun testEdgeCase() {
    // From https://www.atlassian.com/git/tutorials/saving-changes/gitignore
    //     Wait a minute! Shouldn't logs/important.log be negated in the example on the left?
    //     Nope! Due to a performance-related quirk in Git, you can not negate a file
    //     that is ignored due to a pattern matching a directory
    val gitIgnore = GitIgnore("logs/\n!important.log")
    assertIgnore(gitIgnore, "logs/debug.log")
    assertIgnore(gitIgnore, "logs/important.log")
  }

  @Test
  fun testEdgeCaseRuleOrdering() {
    // Same as above but now the rules in a different order
    val gitIgnore = GitIgnore("!important.log\nlogs/")
    assertIgnore(gitIgnore, "logs/debug.log")
    assertIgnore(gitIgnore, "logs/important.log")
  }

  @Test
  fun testEdgeCase2() {
    // As generated by the Initializer on https://jmonkeyengine.org/start/
    // Apparently unaware of this issue in git.
    val gitIgnore =
      GitIgnore(
        "#Although most of the .idea directory should not be committed there is a legitimate purpose for committing run configurations\n" +
          "/.idea/\n" +
          "!/.idea/runConfigurations/\n",
      )
    assertIgnore(gitIgnore, ".idea/ignore.txt")
    assertIgnore(gitIgnore, ".idea/runConfigurations/important.txt")
  }

  @Test
  fun testGlobStarDirectories() {
    // You can use a double asterisk followed by a slash to match all directories.
    val gitIgnore = GitIgnore("**/logs/")
    assertIgnore(gitIgnore, "logs/debug.log")
    assertIgnore(gitIgnore, "logs/monday/debug.log")
    assertIgnore(gitIgnore, "logs/monday/pm/debug.log")
  }

  @Test
  fun testDirNameWildCard() {
    // Wildcards can be used in directory names as well.
    val gitIgnore = GitIgnore("logs/*day/debug.log")
    assertIgnore(gitIgnore, "logs/monday/debug.log")
    assertIgnore(gitIgnore, "logs/tuesday/debug.log")
    assertNotIgnore(gitIgnore, "logs/latest/debug.log")
  }

  @Test
  fun testSubDirectoriesCase() {
    val gitIgnore =
      GitIgnore(
        "/dir1/*\n" +
          "/dir2/*/*\n" +
          "/dir3/*/*/*\n" +
          "/dir4/**/*\n",
      )
    // NO Subdirs
    assertIgnore(gitIgnore, "/dir1/bar.txt")
    assertIgnore(gitIgnore, "/dir1//bar.txt") // Duplicate / are simplified
    assertNotIgnore(gitIgnore, "/dir1/foo/bar.txt")
    assertNotIgnore(gitIgnore, "/dir1/foo/foo/bar.txt")
    assertNotIgnore(gitIgnore, "/dir1/foo/foo/foo/bar.txt")

    // Exactly 1 Subdir
    assertNotIgnore(gitIgnore, "/dir2/bar.txt")
    assertNotIgnore(gitIgnore, "/dir2//bar.txt")
    assertIgnore(gitIgnore, "/dir2/foo/bar.txt")
    assertNotIgnore(gitIgnore, "/dir2/foo/foo/bar.txt")
    assertNotIgnore(gitIgnore, "/dir2/foo/foo/foo/bar.txt")

    // Exactly 2 Subdirs
    assertNotIgnore(gitIgnore, "/dir3/bar.txt")
    assertNotIgnore(gitIgnore, "/dir3//bar.txt")
    assertNotIgnore(gitIgnore, "/dir3/foo/bar.txt")
    assertIgnore(gitIgnore, "/dir3/foo/foo/bar.txt")
    assertIgnore(gitIgnore, "/dir3///foo///foo////bar.txt") // Duplicate / are simplified
    assertNotIgnore(gitIgnore, "/dir3///bar.txt")
    assertNotIgnore(gitIgnore, "/dir3/a//bar.txt")
    assertNotIgnore(gitIgnore, "/dir3//b/bar.txt")
    assertNotIgnore(gitIgnore, "/dir3/foo/foo/foo/bar.txt")

    // Any Subdirs
    assertIgnore(gitIgnore, "/dir4/bar.txt")
    assertIgnore(gitIgnore, "/dir4//bar.txt")
    assertIgnore(gitIgnore, "/dir4/foo/bar.txt")
    assertIgnore(gitIgnore, "/dir4/foo/foo/bar.txt")
    assertIgnore(gitIgnore, "/dir4/foo/foo/foo/bar.txt")
  }

  @Test
  fun testRootRelative() {
    val gitIgnore =
      GitIgnore(
        "/dir1/*\n" +
          "/dir2/*/*\n" +
          "/dir3/*/*/*\n" +
          "/dir4/**/*\n",
      )
    assertIgnore(gitIgnore, "dir1/bar.txt")
    assertIgnore(gitIgnore, "dir1//bar.txt") // Duplicate / are simplified
    assertNotIgnore(gitIgnore, "dir1/foo/bar.txt")
    assertNotIgnore(gitIgnore, "dir1/foo/foo/bar.txt")
    assertNotIgnore(gitIgnore, "dir1/foo/foo/foo/bar.txt")
  }

  @Test
  fun testIgnoreEscapedSpecials() {
    val gitIgnore =
      GitIgnore(
        "/dir1/*\n" +
          "/dir2/*/*\n" +
          "/dir3/*/*/*\n" +
          "/dir4/**/*\n",
      )
    assertIgnore(gitIgnore, "dir1/bar.txt")
    assertIgnore(gitIgnore, "dir1//bar.txt") // Duplicate / are simplified
    assertNotIgnore(gitIgnore, "dir1/foo/bar.txt")
    assertNotIgnore(gitIgnore, "dir1/foo/foo/bar.txt")
    assertNotIgnore(gitIgnore, "dir1/foo/foo/foo/bar.txt")
  }

  @Test
  fun testGeneratedRegexesBasedir() {
    val gitIgnore =
      GitIgnore(
        "*.log\n" +
          "!important/*.log\n" +
          "trace.* ",
      )
    assertIgnore(gitIgnore, "debug.log")
    assertIgnore(gitIgnore, "trace.txt")
    assertIgnore(gitIgnore, "important/trace.log")
    assertNotIgnore(gitIgnore, "important/debug.log")

    // Verify the created ignore rules
    assertEquals(3, gitIgnore.ignoreRules.size)
    for (ignoreRule in gitIgnore.ignoreRules) {
      when (ignoreRule.ignoreExpression) {
        "*.log" -> assertEquals("^/?.*\\.log(/|$)", ignoreRule.filePattern.pattern)
        "!important/*.log" -> assertEquals("^/?important/[^/]*\\.log(/|$)", ignoreRule.filePattern.pattern)
        "trace.*" -> assertEquals("^/?(.*/)?trace\\.[^/]*", ignoreRule.filePattern.pattern)
        else -> fail("Unexpected expression: ${ignoreRule.ignoreExpression}")
      }
    }
  }

  @Test
  fun testGitDocumentation() {
    // From the git documentation:

    // If there is a separator at the beginning or middle (or both) of the pattern,
    // then the pattern is relative to the directory level of the particular .gitignore file itself.
    // Otherwise, the pattern may also match at any level below the .gitignore level.
    // a pattern doc/frotz/ matches doc/frotz directory, but not a/doc/frotz directory;
    val gitIgnore = GitIgnore("doc/frotz/")
    assertIgnore(gitIgnore, "doc/frotz/")
    assertIgnore(gitIgnore, "doc/frotz/file.txt")
    assertNotIgnore(gitIgnore, "a/doc/frotz/")
    assertNotIgnore(gitIgnore, "a/doc/frotz/file.txt")

    // however frotz/ matches frotz and a/frotz that is a directory (all paths are relative from the .gitignore file).
    val gitIgnore2 = GitIgnore("frotz/")
    assertIgnore(gitIgnore2, "frotz/")
    assertIgnore(gitIgnore2, "frotz/file.txt")
    assertIgnore(gitIgnore2, "a/frotz/")
    assertIgnore(gitIgnore2, "a/frotz/file.txt")

    // Beginning
    val gitIgnore3 = GitIgnore("/frotz")
    assertIgnore(gitIgnore3, "frotz/")
    assertIgnore(gitIgnore3, "frotz/file.txt")
    assertNotIgnore(gitIgnore3, "a/frotz/")
    assertNotIgnore(gitIgnore3, "a/frotz/file.txt")

    // Both
    val gitIgnore4 = GitIgnore("/doc/frotz/")
    assertIgnore(gitIgnore4, "doc/frotz/")
    assertIgnore(gitIgnore4, "doc/frotz/file.txt")
    assertNotIgnore(gitIgnore4, "a/doc/frotz/")
    assertNotIgnore(gitIgnore4, "a/doc/frotz/file.txt")
  }

  @Test
  fun testGitDocumentationSlashesAndStars() {
    // From the git documentation:
    // Put a backslash ("\") in front of the first "!" for patterns that
    // begin with a literal "!", for example, "\!important!.txt".
    val gitIgnore = GitIgnore("\\!important!.txt")
    assertIgnore(gitIgnore, "!important!.txt")

    // An asterisk "*" matches anything except a slash.
    val gitIgnore2 = GitIgnore("foo*bar")
    assertIgnore(gitIgnore2, "foo_bar")
    assertNotIgnore(gitIgnore2, "foo/bar")
    assertIgnore(gitIgnore2, "foo_____bar")
    assertNotIgnore(gitIgnore2, "foo__/__bar")

    // A question mark matches exactly one character.
    val gitIgnore3 = GitIgnore("foo?bar")
    assertIgnore(gitIgnore3, "foo_bar")
    assertNotIgnore(gitIgnore3, "foo/bar")
  }

  fun assertIgnore(
    gitIgnore: GitIgnore,
    filename: String,
  ) = runBlocking {
    assertTrue(gitIgnore.isIgnoredFile(filename) == true)
    val windowsFilename = windowsFileName(filename)
    assertTrue(gitIgnore.isIgnoredFile(windowsFilename) == true)
  }

  fun assertNotIgnore(
    gitIgnore: GitIgnore,
    filename: String,
  ) = runBlocking {
    // assert is null or false
    assertTrue(gitIgnore.isIgnoredFile(filename) == null || gitIgnore.isIgnoredFile(filename) == false)
    val windowsFilename = windowsFileName(filename)
    assertTrue(gitIgnore.isIgnoredFile(windowsFilename) == null || gitIgnore.isIgnoredFile(windowsFilename) == false)
  }

  fun assertNullMatch(
    gitIgnore: GitIgnore,
    filename: String,
  ) = runBlocking {
    assertNull(gitIgnore.isIgnoredFile(filename))
    val windowsFilename = windowsFileName(filename)
    assertNull(gitIgnore.isIgnoredFile(windowsFilename))
  }

  fun windowsFileName(filename: String): String {
    return filename.replace("/", "\\")
  }
}
