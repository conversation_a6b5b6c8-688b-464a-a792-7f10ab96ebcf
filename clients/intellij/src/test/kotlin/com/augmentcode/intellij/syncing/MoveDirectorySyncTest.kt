package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout

class MoveDirectorySyncTest : SyncTestCase() {
  /**
   * This tests the flow when you use the IDE UI to move a directory. This is different from `testMvDirectory`
   * because it uses `File.move` instead of deleting and recreating the directory.
   */
  fun testMoveDirectory() =
    runBlocking {
      val state = MockEngineState()
      val mockEngine = createMockEngine(state)
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val tempDir = getVirtualFile(createTempDirectory())
      PsiTestUtil.addContentRoot(module, tempDir)

      // Create directory and file inside it
      // File structure is:
      // tempDir
      // ├── foo
      // │   └── bar.txt
      // └── baz
      val (fooDir, barFile, bazDir) =
        runWriteAction {
          val fooDir = tempDir.createChildDirectory(this, "foo")
          val barFile =
            fooDir.createChildData(this, "bar.txt").also {
              VfsUtil.saveText(it, "bar content")
            }
          val bazDir = tempDir.createChildDirectory(this, "baz")
          Triple(fooDir, barFile, bazDir)
        }

      val originalBlobState = AugmentBlobStateReader.read(barFile.toPsiFile(project))
      assertNotNull(originalBlobState)
      val originalBlobName = originalBlobState!!.remoteName

      // Wait for initial upload and checkpoint
      waitForRequests(mockEngine, "/batch-upload", 1)
      waitForRequests(mockEngine, "/checkpoint-blobs", 1)

      // Move the foo directory under baz. This happens when you use the IDE UI to move directories
      // New file structure is:
      // tempDir
      // └── baz
      //     └── foo
      //         └── bar.txt
      WriteCommandAction.runWriteCommandAction(project) {
        fooDir.move(this, bazDir)
      }

      // Assert bar path has changed to /bar.txt
      assertEquals("bar file path should have changed to baz/foo/bar.txt", "${tempDir.path}/baz/foo/bar.txt", barFile.path)

      // Get the new blob state and force indexing to occur
      val renamedBlobState = AugmentBlobStateReader.read(barFile.toPsiFile(project))
      assertNotNull(renamedBlobState)
      val renamedBlobName = renamedBlobState!!.remoteName

      // Wait for the new blob to be uploaded
      waitForRequests(mockEngine, "/batch-upload", 2)

      // Wait for sync manager to process the state changes with timeout
      val syncManager = AugmentRemoteSyncingManager.getInstance(project)
      try {
        withTimeout(10000) {
          while (true) {
            val payload = syncManager.synchronizedBlobsPayload()

            // Check the internal state of the sync manager
            val syncedBlobs = syncManager.syncedBlobs()
            val syncedPaths = syncedBlobs.map { joinPath(it.rootPath, it.relativePath) }

            // Verify the new path is in the synced blobs and the old path is not
            val newPathSynced = syncedPaths.any { it.contains("/baz/foo/bar.txt") }
            val oldPathNotSynced = syncedPaths.none { it.contains("/foo/bar.txt") && !it.contains("baz/foo/bar.txt") }

            // Check the state of the checkpoint payload
            val checkpointMatches = payload.checkpointId == "checkpoint-1"
            val addedBlobsMatch = payload.addedBlobs == setOf(renamedBlobName)
            val deletedBlobsMatch = payload.deletedBlobs == setOf(originalBlobName)

            if (newPathSynced && oldPathNotSynced && checkpointMatches && addedBlobsMatch && deletedBlobsMatch) {
              break
            }
            delay(250) // Poll interval
          }
        }
      } catch (e: TimeoutCancellationException) {
        // Only run this when timeout occurs
        val syncedBlobs = syncManager.syncedBlobs()
        val syncedPaths = syncedBlobs.map { joinPath(it.rootPath, it.relativePath) }

        val errors = mutableListOf<String>()
        if (!syncedPaths.any { it.contains("/bar.txt") && !it.contains("baz/foo/bar.txt") }) {
          errors.add("New path /bar.txt not found in synced blobs")
        }
        if (syncedPaths.any { it.contains("/foo/bar.txt") }) {
          errors.add("Old path /foo/bar.txt still exists in synced blobs")
        }
        val payload = syncManager.synchronizedBlobsPayload()
        if (payload.checkpointId != "checkpoint-1") {
          errors.add("Checkpoint ID mismatch: expected 'checkpoint-1' but got '${payload.checkpointId}'")
        }
        if (payload.addedBlobs != setOf(renamedBlobName)) {
          errors.add("Added blobs mismatch: expected ${setOf(renamedBlobName)} but got ${payload.addedBlobs}")
        }
        if (payload.deletedBlobs != setOf(originalBlobName)) {
          errors.add("Deleted blobs mismatch: expected ${setOf(originalBlobName)} but got ${payload.deletedBlobs}")
        }

        throw AssertionError("Timeout waiting for sync state to match. Mismatches:\n${errors.joinToString("\n")}")
      }
    }
}
