package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readBytes
import com.intellij.openapi.vfs.readText
import com.intellij.testFramework.PsiTestUtil
import com.intellij.util.indexing.FileContentImpl
import kotlinx.coroutines.runBlocking

class FileEncodingSyncTest : SyncTestCase() {
  private fun testFileEncoding(
    filename: String,
    content: String,
    @Suppress("UNUSED_PARAMETER")
    description: String,
  ) = runBlocking {
    val state = MockEngineState()
    val mockEngine = createMockEngine(state)
    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    val tempDir = getVirtualFile(createTempDirectory())
    PsiTestUtil.addContentRoot(module, tempDir)

    // Create file with test content
    val file =
      runWriteAction {
        tempDir.createChildData(this, filename).also {
          VfsUtil.saveText(it, content)
        }
      }

    // Force indexing and verify upload
    val originalBlobState = AugmentBlobStateReader.read(file.toPsiFile(project))
    assertNotNull(originalBlobState)
    originalBlobState!!
    waitForRequests(mockEngine, "/batch-upload", 1)

    // Compare blob name on server with client calculated name
    assertEquals(
      "Server calculated blob name should match client calculated blob name",
      originalBlobState.localName,
      state.uploadedBlobs.last(),
    )

    // Clean up
    runWriteAction {
      file.delete(this)
    }
  }

  fun testVisualStudioFileWithBOM() =
    testFileEncoding(
      "test.txt",
      "\uFEFFusing System;\npublic class Test {}\r\n",
      "C# file with BOM and CRLF",
    )

  fun testYamlWithMixedLineEndings() =
    testFileEncoding(
      "test.txt",
      "key1: value1\r\nkey2: value2\nkey3: value3",
      "YAML with mixed line endings",
    )

  fun testConfigWithSpecialCharacters() =
    testFileEncoding(
      "test.txt",
      "[section]\nkey1=value1\nkey2=value2 # comment with 漢字\n",
      "INI with Unicode comments",
    )

  fun testMarkdownWithUnicode() =
    testFileEncoding(
      "test.txt",
      "# Header\r\n* Bullet 1\n* Bullet 2\r\n```\ncode block\n```\n> Quote with emoji 🚀\n",
      "Markdown with mixed content",
    )

  fun testGeneratedFile() =
    testFileEncoding(
      "Generated.txt",
      "\uFEFF// <auto-generated>\r\n//     Generated by the Tool.\r\n// </auto-generated>\r\n",
      "Generated C# file",
    )

  private fun printEncodingDebugInfo(
    file: VirtualFile,
    filename: String,
    content: String,
  ) {
    // Read content in different ways
    val fileContent = FileContentImpl.createByFile(file)
    val contentBytes = fileContent.content
    val indexerContent = fileContent.contentAsText.toString()
    val uploaderContent = file.readText()
    val uploaderContentBytes = file.readBytes()
    val rawBytes = file.contentsToByteArray()
    val convertFileToDocument = FileDocumentManager.getInstance().getDocument(file)?.text ?: file.readText() // expect to be normalized

    // Debug output
    println("Original content bytes: ${content.toByteArray().toList()}")
    println("Raw file bytes: ${rawBytes.toList()}")
    println("Indexer content bytes: ${indexerContent.toByteArray().toList()}")
    println("Uploader content string to bytes: ${uploaderContent.toByteArray().toList()}")
    println("Uploader content bytes: ${uploaderContentBytes.toList()}")
    println("FileContent.content bytes: ${contentBytes.toList()}")
    println("FileContent.content to string utf-8: ${String(contentBytes, Charsets.UTF_8).toByteArray().toList()}")
    println("FileContent.content to byte array to list as string: ${String(contentBytes).toByteArray().toList()}")
    println("Convert file to document: ${convertFileToDocument.toByteArray().toList()}")

    // Check for BOM
    val hasBOM =
      rawBytes.size >= 3 &&
        rawBytes[0] == 0xEF.toByte() &&
        rawBytes[1] == 0xBB.toByte() &&
        rawBytes[2] == 0xBF.toByte()
    println("Has BOM: $hasBOM")

    // Check line endings
    println("Line endings in indexer: ${indexerContent.lines().size}")
    println("Line endings in uploader: ${uploaderContent.lines().size}")

    assertEquals(
      "Content should be consistent between indexer and uploader for $filename when we normalize line endings",
      indexerContent.normalizeLineEndings(),
      uploaderContent.normalizeLineEndings(),
    )

    // Verify content consistency
    // Note: We're using a different approach here since assertNotEquals is not available
    if (indexerContent == uploaderContent) {
      fail("Content should be consistent between indexer and uploader for $filename")
    }
  }
}
