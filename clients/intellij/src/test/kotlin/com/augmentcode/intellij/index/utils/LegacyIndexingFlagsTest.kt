package com.augmentcode.intellij.index.utils

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.utils.CustomPropertyReader
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.GetModelsResponse

@RunWith(JUnit4::class)
class LegacyIndexingFlagsTest : AugmentBasePlatformTestCase() {
  private val mockAppStateService: AugmentAppStateService = mockk(relaxed = true)

  override fun setUp() {
    super.setUp()
    mockkObject(CustomPropertyReader)

    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAppStateService,
      testRootDisposable,
    )
  }

  override fun tearDown() {
    unmockkObject(CustomPropertyReader)
    super.tearDown()
  }

  private fun createFeatureFlags(indexingV3Enabled: Boolean = false): FeatureFlags {
    val flagsFromAPI =
      GetModelsResponse.FeatureFlags.newBuilder()
        .setIntellijIndexingV3Enabled(indexingV3Enabled)
        .build()
    val getModelsResponse =
      GetModelsResponse.newBuilder()
        .setFeatureFlags(flagsFromAPI)
        .build()
    return FeatureFlags.fromGetModelsResponse(getModelsResponse)
  }

  @Ignore("Custom property is read only once in a private var hence not able to test properly")
  @Test
  fun `test isLegacyIndexingDisabled when custom property is true`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY) } returns "true"
    val mockContext = PluginContext(true, createFeatureFlags(indexingV3Enabled = false), null)
    every { mockAppStateService.context } returns mockContext

    val isDisabled = isLegacyIndexingDisabled()
    assertTrue("Legacy indexing should be disabled when custom property is true", isDisabled)
  }

  @Test
  fun `test isLegacyIndexingDisabled when custom property is not set and feature flag disabled`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY) } returns null
    val mockContext = PluginContext(true, createFeatureFlags(indexingV3Enabled = false), null)
    every { mockAppStateService.context } returns mockContext

    assertFalse(
      "Should default to enabled when custom property is not set and feature flag disabled",
      isLegacyIndexingDisabled(),
    )
  }

  @Test
  fun `test isLegacyIndexingDisabled when custom property not set and V3 feature flag enabled`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY) } returns null
    val mockContext = PluginContext(true, createFeatureFlags(indexingV3Enabled = true), null)
    every { mockAppStateService.context } returns mockContext

    assertTrue(
      "Legacy indexing should be disabled when V3 feature flag is enabled",
      isLegacyIndexingDisabled(),
    )
  }

  @Test
  fun `test isLegacyIndexingDisabled when AugmentAppStateService fails`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY) } returns null
    every { mockAppStateService.context } throws RuntimeException("Service unavailable")

    assertFalse("Should fall back to default when AugmentAppStateService fails", isLegacyIndexingDisabled())
  }
}
