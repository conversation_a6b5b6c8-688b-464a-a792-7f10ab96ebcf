package com.augmentcode.intellij.actions

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.settings.AugmentSettingsWebviewEditorVirtualFile.Companion.SETTINGS_VIRTUAL_FILE_NAME
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.TestActionEvent
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class OpenSettingsWebviewActionTest : AugmentBasePlatformTestCase() {
  private lateinit var mockAugmentAppStateService: AugmentAppStateService

  private lateinit var action: OpenSettingsWebviewAction

  override fun setUp() {
    super.setUp()

    // Create the action
    action = OpenSettingsWebviewAction()

    // Mock AugmentAppStateService
    mockAugmentAppStateService = mockk(relaxed = true)
    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )
  }

  override fun tearDown() {
    // Close any open files
    val editorManager = FileEditorManager.getInstance(project)
    for (file in editorManager.openFiles) {
      editorManager.closeFile(file)
    }
    super.tearDown()
  }

  /**
   * Test that the action creates a new virtual file when one doesn't exist
   * and opens it in the editor
   */
  @Test
  fun testActionCreatesNewFile() {
    // Create a test action event with the current project
    val event = TestActionEvent.createTestEvent()

    // Perform the OpenSettingsWebviewAction
    action.actionPerformed(event)

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify that a settings virtual file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Settings virtual file should be opened in editor",
      openFiles.any { it.name == SETTINGS_VIRTUAL_FILE_NAME },
    )
  }

  @Test
  fun testNotVisibleWhenNoFlags() {
    every { mockAugmentAppStateService.context } returns PluginContext(false, DefaultFeatureFlags, null)

    val event = TestActionEvent.createTestEvent()
    action.update(event)

    assertFalse("Action should be disabled", event.presentation.isEnabled)
    assertFalse("Action should not be visible", event.presentation.isVisible)
  }

  @Test
  fun testNotVisibleWhenDisabled() {
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijAgentModeMinVersion("")
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.context } returns mockContext

    val event = TestActionEvent.createTestEvent()
    action.update(event)

    assertFalse("Action should be disabled", event.presentation.isEnabled)
    assertFalse("Action should not be visible", event.presentation.isVisible)
  }

  @Test
  fun testVisibleWhenEnabled() {
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijAgentModeMinVersion("0.0.0")
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.context } returns mockContext

    val event = TestActionEvent.createTestEvent()
    action.update(event)

    assertTrue("Action should be enabled", event.presentation.isEnabled)
    assertTrue("Action should be visible", event.presentation.isVisible)
  }
}
