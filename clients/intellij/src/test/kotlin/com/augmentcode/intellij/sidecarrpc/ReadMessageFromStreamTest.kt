package com.augmentcode.intellij.sidecarrpc

import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.io.*

@RunWith(JUnit4::class)
class ReadMessageFromStreamTest {
  @Test
  fun testReadingMultiByteCharacters() {
    val inputStream = PipedOutputStream()

    val input = PipedInputStream(inputStream)
    val bufferedInput = BufferedInputStream(input, 2)

    // The ㅎ character is a multi-byte character in UTF-8 (3 bytes)
    val messageBody = "ㅎ"
    inputStream.write(messageBody.toByteArray())

    val got = readBytesFromStream(bufferedInput, messageBody.toByteArray().size)
    assertEquals(messageBody, got)
  }

  @Test
  fun testReadingMultiByteCharacterMessage() {
    val inputStream = PipedOutputStream()

    val input = PipedInputStream(inputStream)
    val bufferedInput = BufferedInputStream(input, 2)

    // The ㅎ character is a multi-byte character in UTF-8 (3 bytes)
    val messageBody = "ㅎ"
    inputStream.write("Content-Length: ${messageBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(messageBody.toByteArray())

    val got = readMessageFromStream(bufferedInput)
    assertEquals(messageBody, got)
  }

  @Test
  fun testReadingLongerMessage() {
    val inputStream = PipedOutputStream()

    val input = PipedInputStream(inputStream)
    val bufferedInput = BufferedInputStream(input, 2)

    // The ㅎ character is a multi-byte character in UTF-8 (3 bytes)
    @Suppress("ktlint:standard:max-line-length")
    val messageBody = "{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"test.method\",\"params\":{\"@type\":\"type.googleapis.com/google.protobuf.StringValue\",\"value\":\"ㅎㅎㅎㅎㅎㅎㅎㅎㅎㅎ\"}}"
    inputStream.write("Content-Length: ${messageBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(messageBody.toByteArray())

    val got = readMessageFromStream(bufferedInput)
    assertEquals(messageBody, got)
  }

  @Test
  fun testReadingBadMessage() {
    val inputStream = PipedOutputStream()

    val input = PipedInputStream(inputStream)
    val bufferedInput = BufferedInputStream(input, 2)

    @Suppress("ktlint:standard:max-line-length")
    val validBody = "{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"test.method\",\"params\":{\"@type\":\"type.googleapis.com/google.protobuf.StringValue\",\"value\":\"Content-Length: 1\r\na\"}}"
    inputStream.write("Content-Length: ${validBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(validBody.toByteArray())

    val gotValidMessage = readMessageFromStream(bufferedInput)
    assertEquals(validBody, gotValidMessage)

    // Send body without a content length and no \r\n
    inputStream.write("example with new line\n".toByteArray())

    // Valid message
    inputStream.write("Content-Length: ${validBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(validBody.toByteArray())

    assertEquals(validBody, readMessageFromStream(bufferedInput))

    // Send a body with \r\n that isn't content-length
    inputStream.write("example with return and new line\r\n".toByteArray())

    // Valid message but will not work because of previous message
    inputStream.write("Content-Length: ${validBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(validBody.toByteArray())

    // This handles the \r\n that triggers a null response
    assertEquals(null, readMessageFromStream(bufferedInput))
    assertEquals(validBody, readMessageFromStream(bufferedInput))

    // Send a body with \r\n that isn't content-length and follow on content
    inputStream.write("example with return and new line\r\n in the middle".toByteArray())

    // Valid message
    inputStream.write("Content-Length: ${validBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(validBody.toByteArray())

    // This handles the \r\n message before in the middle
    assertEquals(null, readMessageFromStream(bufferedInput))
    assertEquals(validBody, readMessageFromStream(bufferedInput))

    // Invalid content-length that should have 2x\r\n but only has one
    inputStream.write("Content-Length: 1\r\na".toByteArray())

    // Valid message
    inputStream.write("Content-Length: ${validBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(validBody.toByteArray())

    assertEquals(null, readMessageFromStream(bufferedInput))
    assertEquals(validBody, readMessageFromStream(bufferedInput))

    // Dummy Content-Length header
    inputStream.write("Content-Length: 10 Content-Length: ${validBody.toByteArray().size}\r\n\r\n".toByteArray())
    inputStream.write(validBody.toByteArray())

    assertEquals(validBody, readMessageFromStream(bufferedInput))

    inputStream.close()
  }
}
