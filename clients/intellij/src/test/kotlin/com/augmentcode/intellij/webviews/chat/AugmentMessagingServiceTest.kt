package com.augmentcode.intellij.webviews.chat

import com.augmentcode.chat.DerivedStateName
import com.augmentcode.intellij.chat.AugmentChatToolWindow
import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.metrics.ClientMetricsReporter
import com.augmentcode.intellij.metrics.OnboardingSessionEventReporter
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.mock.PathFilterServiceMocks
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.syncing.AugmentSyncingPermissionTracker
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.webviews.settings.toPrettyJson
import com.google.gson.GsonBuilder
import com.google.gson.JsonParser
import com.intellij.ide.util.PropertiesComponent
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentMessagingServiceTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()

    // Force plugin state to enabled with authentication
    augmentHelpers().forcePluginState(PluginState.ENABLED)
  }

  @Test
  fun testSignInCanceled() =
    runTest {
      val messagingService = ChatMessagingService.getInstance(project)

      // First, start sign in flow with main panel action sign-in
      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"main-panel-perform-action",
            "requestId":"abc-123",
            "error":null,
            "data": "sign-in"
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      // expect response to have SignInInProgress
      assertEquals(
        """
        {
          "data": [
            "SignInInProgress"
          ],
          "type": "main-panel-actions"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )

      // Allow for the oauth authorize call to run - without this
      // the authorize flow will open a browser window.
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Now, cancel sign in flow with main panel action cancel-sign-in
      val cancelResponses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"main-panel-perform-action",
            "requestId":"abc-123",
            "error":null,
            "data": "cancel-sign-in"
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, cancelResponses.size)
      // expect response to have UserShouldSignIn
      assertEquals(
        """
        {
          "data": [
            "UserShouldSignIn"
          ],
          "type": "main-panel-actions"
        }
        """.trimIndent(),
        cancelResponses.first().toPrettyJson(),
      )
    }

  @Test
  fun testReportWebviewClientMetric() =
    runTest {
      ClientMetricsReporter.getInstance(project).enableUpload()

      var clientMetrics = mutableListOf<Any>()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/client-metrics" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              clientMetrics.add(requestBody)
              respond(
                content = """{"success": true}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val messagingService = ChatMessagingService.getInstance(project)
      messagingService.processMessageFromWebview(
        """
        {
          "type":"async-wrapper",
          "requestId":"abc-123",
          "error":null,
          "baseMsg": {
            "type": "report-webview-client-metric",
            "data": {
              "clientMetric": "foo",
              "value": 0
            }
          }
        }
        """.trimIndent(),
      ).toList()

      waitForAssertion({
        assertEquals(1, clientMetrics.size)
      })
    }

  @Test
  fun testFindFileV2Indexing() =
    runTest {
      // Mock syncedBlobs in sync manager to return foo.txt
      val mockSyncManager =
        mockk<AugmentRemoteSyncingManager> {
          every { syncedBlobs() } returns
            listOf(
              AugmentBlobState(
                remoteName = "foo.txt",
                relativePath = "foo.txt",
                localName = "hash(foo.txt).v1",
                rootPath = "",
              ),
            )
        }

      myFixture.project.registerOrReplaceServiceInstance(
        AugmentRemoteSyncingManager::class.java,
        mockSyncManager,
        testRootDisposable,
      )

      // Send find-file-request
      val messagingService = ChatMessagingService.getInstance(project)
      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "find-file-request",
              "relPath": "foo"
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": [
              {
                "repoRoot": "",
                "pathName": "foo.txt"
              }
            ],
            "type": "find-file-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )
    }

  @Test
  fun testResolveFile() =
    runTest {
      // Mock syncedBlobs in sync manager to return foo.txt
      val mockSyncManager =
        mockk<AugmentRemoteSyncingManager> {
          every { syncedBlobs() } returns
            listOf(
              AugmentBlobState(
                remoteName = "foo.txt",
                relativePath = "foo.txt",
                localName = "hash(foo.txt).v1",
                rootPath = "",
              ),
            )
        }

      myFixture.project.registerOrReplaceServiceInstance(
        AugmentRemoteSyncingManager::class.java,
        mockSyncManager,
        testRootDisposable,
      )

      val messagingService = ChatMessagingService.getInstance(project)
      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "resolve-file-request",
              "relPath": "foo.txt",
              "exactMatch": true,
              "maxResults": 1
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": {
              "repoRoot": "",
              "pathName": "foo.txt"
            },
            "type": "resolve-file-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )
    }

  @Test
  fun testFindFolder() =
    runTest {
      // Mock syncedBlobs in sync manager to return foo.txt
      val mockSyncManager =
        mockk<AugmentRemoteSyncingManager> {
          every { syncedBlobs() } returns
            listOf(
              AugmentBlobState(
                remoteName = "hash(foo.txt).v1",
                relativePath = "foo.txt",
                localName = "hash(foo.txt).v1",
                rootPath = "",
              ),
              AugmentBlobState(
                remoteName = "hash(foo/bar.txt).v1",
                relativePath = "foo/bar.txt",
                localName = "hash(foo/bar.txt).v1",
                rootPath = "",
              ),
              AugmentBlobState(
                remoteName = "hash(foo/bar/bar.txt).v1",
                relativePath = "foo/bar/bar.txt",
                localName = "hash(foo/bar/bar.txt).v1",
                rootPath = "",
              ),
            )
        }

      myFixture.project.registerOrReplaceServiceInstance(
        AugmentRemoteSyncingManager::class.java,
        mockSyncManager,
        testRootDisposable,
      )

      // Send fuzzy find-folder-request
      val messagingService = ChatMessagingService.getInstance(project)
      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "find-folder-request",
              "data": {
                "relPath": "foo"
              }
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": [
              {
                "repoRoot": "",
                "pathName": "foo"
              },
              {
                "repoRoot": "",
                "pathName": "foo/bar"
              }
            ],
            "type": "find-folder-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )

      // Send exact match find-folder-request
      val exactResponses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "find-folder-request",
              "data": {
                "relPath": "foo",
                "exactMatch": true
              }
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, exactResponses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": [
              {
                "repoRoot": "",
                "pathName": "foo"
              }
            ],
            "type": "find-folder-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        exactResponses.first().toPrettyJson(),
      )
    }

  @Test
  fun testRecentlyOpenedFiles() =
    runTest {
      PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

      // this only adds a file and indexes it
      myFixture.addFileToProject("foo.go", "")
      // this will also open it in an editor
      myFixture.configureByText("foo.txt", "")

      val messagingService = ChatMessagingService.getInstance(project)
      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "find-recently-opened-files",
              "data": {
                "relPath": "foo"
              }
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": [
              {
                "repoRoot": "",
                "pathName": "foo.txt"
              }
            ],
            "type": "find-recently-opened-files-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )

      val exactResponses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "find-recently-opened-files",
              "data": {
                "relPath": "foo.txt",
                "exactMatch": true
              }
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, exactResponses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": [
              {
                "repoRoot": "",
                "pathName": "foo.txt"
              }
            ],
            "type": "find-recently-opened-files-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        exactResponses.first().toPrettyJson(),
      )
    }

  @Test
  fun testSaveChat() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/save-chat" -> {
              respond(
                content = """{"uuid": "mock-uuid", "url": "mock-url"}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val messagingService = ChatMessagingService.getInstance(project)

      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "save-chat",
              "data": {
                "conversationId": "mock-conversation-id",
                "chatHistory": [
                  {
                    "requestId": "mock-chat-exchange-id",
                    "requestMessage": "mock-request-message",
                    "responseText": "mock-response-text"
                  }
                ],
                "title": "mock-title"
              }
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": {
              "uuid": "mock-uuid",
              "url": "mock-url"
            },
            "type": "save-chat-done"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )
    }

  @Test
  fun testUsedChatMessage() {
    runTest {
      OnboardingSessionEventReporter.getInstance(project).enableUpload()

      val onboardingEvents = mutableListOf<Any>()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/record-onboarding-session-event" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              onboardingEvents.add(requestBody)
              respond(
                content = """{"success": true}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)
      val messagingService = ChatMessagingService.getInstance(project)

      messagingService.processMessageFromWebview(
        """
        {
            "type": "used-chat"
        }
        """.trimIndent(),
      ).toList()

      waitForAssertion({
        assertTrue(onboardingEvents.any { it.toString().contains("used-chat") })
      })
    }
  }

  @Test
  fun testUsedSlashAction() =
    runTest {
      OnboardingSessionEventReporter.getInstance(project).enableUpload()

      val onboardingEvents = mutableListOf<Any>()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/record-onboarding-session-event" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              onboardingEvents.add(requestBody)
              respond(
                content = """{"success": true}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)
      val messagingService = ChatMessagingService.getInstance(project)
      messagingService.processMessageFromWebview(
        """
        {
            "type": "used-slash-action"
        }
        """.trimIndent(),
      ).toList()

      waitForAssertion({
        assertTrue(onboardingEvents.any { it.toString().contains("used-slash-action") })
      })
    }

  @Test
  fun testDiagnosticsSelection() {
    runTest {
      PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

      val main =
        myFixture.configureByText(
          "Main.java",
          """
          class Main implements <error descr="Cannot resolve symbol 'NotExists'">NotExists</error> {
            <selection>final <error descr="Cannot resolve symbol 'String'">String</error> name = 123;</selection>
          }
          """.trimIndent(),
        )
      myFixture.checkHighlighting()

      assertEquals("Main.java", AugmentBlobStateReader.read(main)?.relativePath)

      val messagingService = ChatMessagingService.getInstance(project)

      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "get-diagnostics-request"
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": [
              {
                "location": {
                  "path": "Main.java",
                  "lineStart": 42,
                  "lineEnd": 48
                },
                "message": "Cannot resolve symbol \u0027String\u0027",
                "severity": 0,
                "currentBlobName": "4c5e09320040bda3acdea76566c2b24ea53af6b8c5305df7da1ee81159b37c6e"
              }
            ],
            "type": "get-diagnostics-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )
    }
  }

  @Test
  fun testDiagnosticsNoSelection() {
    runTest {
      val main =
        myFixture.configureByText(
          "Main.java",
          """
          class Main implements <error descr="Cannot resolve symbol 'NotExists'">NotExists</error> {
            final <error descr="Cannot resolve symbol 'String'">String</error> name = 123;
          }
          """.trimIndent(),
        )
      myFixture.checkHighlighting()

      assertEquals(
        "4c5e09320040bda3acdea76566c2b24ea53af6b8c5305df7da1ee81159b37c6e",
        AugmentBlobStateReader.read(main)?.remoteName,
      )

      val messagingService = ChatMessagingService.getInstance(project)

      val responses =
        messagingService.processMessageFromWebview(
          """
          {
            "type":"async-wrapper",
            "requestId":"abc-123",
            "error":null,
            "baseMsg": {
              "type": "get-diagnostics-request"
            }
          }
          """.trimIndent(),
        ).toList()
      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
            "data": [],
            "type": "get-diagnostics-response"
          },
          "type": "async-wrapper"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )
    }
  }

  @Test
  @Ignore("TODO AU-7837 Re-enable sync permission check")
  fun testMainPanelLoadedWhenSyncingPermissionNeeded() =
    runTest {
      // Ensure syncing is not enabled
      PropertiesComponent.getInstance(project).setValue(AugmentSyncingPermissionTracker.SYNCING_ENABLED_KEY, false)

      val messagingService = ChatMessagingService.getInstance(project)
      val responses =
        messagingService.processMessageFromWebview(
          """
          {
              "type": "main-panel-loaded"
          }
          """.trimIndent(),
        ).toList()

      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "data": "${AugmentChatToolWindow.AWAITING_SYNCING_PERMISSION_APP}",
          "type": "main-panel-display-app"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )
    }

  @Test
  fun testGrantSyncingPermission() =
    runTest {
      PropertiesComponent.getInstance(project).setValue(AugmentSyncingPermissionTracker.SYNCING_ENABLED_KEY, false)

      val messagingService = ChatMessagingService.getInstance(project)
      val responses =
        messagingService.processMessageFromWebview(
          """
          {
              "type": "main-panel-perform-action",
              "data": "grant-sync-permission"
          }
          """.trimIndent(),
        ).toList()

      assertEquals(1, responses.size)
      assertEquals(
        """
        {
          "data": [
            "${DerivedStateName.ALL_ACTIONS_COMPLETE.value}"
          ],
          "type": "main-panel-actions"
        }
        """.trimIndent(),
        responses.first().toPrettyJson(),
      )

      // Verify permission was actually granted
      assertFalse(AugmentSyncingPermissionTracker.getInstance(project).needsSyncingPermission())
    }
}

internal fun String.toPrettyJson(): String {
  val element = JsonParser.parseString(this)
  val gson = GsonBuilder().setPrettyPrinting().create()
  return gson.toJson(element)
}
