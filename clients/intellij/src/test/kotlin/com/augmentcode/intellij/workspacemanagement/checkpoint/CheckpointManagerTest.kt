package com.augmentcode.intellij.workspacemanagement.checkpoint

import com.augmentcode.api.CheckpointBlobsRequest
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertionSuspend
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.util.Disposer
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Path

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class CheckpointManagerTest : AugmentBasePlatformTestCase() {
  private val gson = GsonUtil.createApiGson()
  private val maxWorkingSetSize = 4

  lateinit var scope: CoroutineScope
  lateinit var mockEngine: MockEngine
  lateinit var checkpointChannel: RoughlySizedChannel<BlobChangeEvent>
  lateinit var manager: CheckpointManager

  override fun setUp() {
    super.setUp()
    augmentHelpers().forcePluginState(PluginState.ENABLED)
    scope = augmentHelpers().createCoroutineScope(Dispatchers.IO)
    mockEngine = setupMockEngine()
    checkpointChannel = RoughlySizedChannel(Channel(Channel.UNLIMITED))
    manager = CheckpointManager(scope, checkpointChannel, maxWorkingSetSize = maxWorkingSetSize)
  }

  override fun tearDown() {
    Disposer.dispose(manager)
    super.tearDown()
  }

  private fun setupMockEngine(): MockEngine {
    var currentCheckpoint = 0
    val checkpointCalls = mutableListOf<String>()

    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/checkpoint-blobs" -> {
            checkpointCalls.add(request.body.toByteArray().decodeToString())

            currentCheckpoint += 1
            respond(
              content = """{"new_checkpoint_id": "checkpoint-$currentCheckpoint"}""",
              status = HttpStatusCode.OK,
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
          "/get-models" -> HttpUtil.respondGetModels(this)
          else -> error("Unexpected request to ${request.url.encodedPath}")
        }
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    return mockEngine
  }

  @Test
  fun testCheckpointRegeneration() =
    runTest {
      manager.startCheckpointJob()

      // Initial checkpoint creation with two files
      checkpointChannel.send(BlobAddedEvent(Path.of("foo.txt"), "foo-1"))
      checkpointChannel.send(BlobAddedEvent(Path.of("bar.txt"), "bar-1"))

      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals(null, checkpoint.checkpointId)
        assertEquals(setOf("foo-1", "bar-1"), checkpoint.addedBlobs)
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      // update one
      checkpointChannel.send(BlobUpdatedEvent(Path.of("foo.txt"), "foo-1", "foo-2"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals(null, checkpoint.checkpointId)
        assertEquals(setOf("foo-2", "bar-1"), checkpoint.addedBlobs)
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      // update the other
      checkpointChannel.send(BlobUpdatedEvent(Path.of("bar.txt"), "bar-1", "bar-2"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals(null, checkpoint.checkpointId)
        assertEquals(setOf("foo-2", "bar-2"), checkpoint.addedBlobs)
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      assertEquals(0, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })

      // now add two more files to exceed the working set size to trigger a checkpoint
      checkpointChannel.send(BlobAddedEvent(Path.of("baz.txt"), "baz-1"))
      checkpointChannel.send(BlobAddedEvent(Path.of("qux.txt"), "qux-1"))

      // Wait for checkpoint to be created
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-1", checkpoint.checkpointId)
        assertTrue(checkpoint.addedBlobs.isEmpty())
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      // modify one recently added blob
      checkpointChannel.send(BlobUpdatedEvent(Path.of("baz.txt"), "baz-1", "baz-2"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-1", checkpoint.checkpointId)
        assertEquals(setOf("baz-2"), checkpoint.addedBlobs)
        assertEquals(setOf("baz-1"), checkpoint.deletedBlobs)
      })

      // add the fifth file
      checkpointChannel.send(BlobAddedEvent(Path.of("quux.txt"), "quux-1"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-1", checkpoint.checkpointId)
        assertEquals(setOf("quux-1", "baz-2"), checkpoint.addedBlobs)
        assertEquals(setOf("baz-1"), checkpoint.deletedBlobs)
      })

      // modify another recently added blob, triggering a new checkpoint
      checkpointChannel.send(BlobUpdatedEvent(Path.of("qux.txt"), "qux-1", "qux-2"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-2", checkpoint.checkpointId)
        assertTrue(checkpoint.addedBlobs.isEmpty())
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      // remove one of the recently added blobs
      checkpointChannel.send(BlobRemovedEvent(Path.of("quux.txt"), "quux-1"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-2", checkpoint.checkpointId)
        assertTrue(checkpoint.addedBlobs.isEmpty())
        assertEquals(setOf("quux-1"), checkpoint.deletedBlobs)
      })

      assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
    }

  @Test
  fun testBlobRemovedThenAddedBeforeCheckpoint() =
    runTest {
      manager.startCheckpointJob()

      // Create initial checkpoint with four files to trigger checkpoint
      checkpointChannel.send(BlobAddedEvent(Path.of("foo.txt"), "foo-1"))
      checkpointChannel.send(BlobAddedEvent(Path.of("bar.txt"), "bar-1"))
      checkpointChannel.send(BlobAddedEvent(Path.of("baz.txt"), "baz-1"))
      checkpointChannel.send(BlobAddedEvent(Path.of("qux.txt"), "qux-1"))

      // Wait for checkpoint to be created
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-1", checkpoint.checkpointId)
        assertTrue(checkpoint.addedBlobs.isEmpty())
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      // Remove a blob that's in the checkpoint
      checkpointChannel.send(BlobRemovedEvent(Path.of("foo.txt"), "foo-1"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-1", checkpoint.checkpointId)
        assertTrue(checkpoint.addedBlobs.isEmpty())
        assertEquals(setOf("foo-1"), checkpoint.deletedBlobs)
      })

      // Add the same blob back - should cancel out the removal
      checkpointChannel.send(BlobAddedEvent(Path.of("foo.txt"), "foo-1"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals("checkpoint-1", checkpoint.checkpointId)
        assertTrue(checkpoint.addedBlobs.isEmpty())
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
    }

  @Test
  fun testBlobAddedThenRemovedBeforeCheckpoint() =
    runTest {
      manager.startCheckpointJob()

      // Add a new blob
      checkpointChannel.send(BlobAddedEvent(Path.of("new.txt"), "new-1"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals(null, checkpoint.checkpointId)
        assertEquals(setOf("new-1"), checkpoint.addedBlobs)
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      // Remove the same blob - should cancel out the addition
      checkpointChannel.send(BlobRemovedEvent(Path.of("new.txt"), "new-1"))
      waitForAssertionSuspend({
        val checkpoint = manager.currentCheckpoint()
        assertEquals(null, checkpoint.checkpointId)
        assertTrue(checkpoint.addedBlobs.isEmpty())
        assertTrue(checkpoint.deletedBlobs.isEmpty())
      })

      assertEquals(0, mockEngine.requestHistory.count { it.url.encodedPath == "/checkpoint-blobs" })
    }

  @Test
  fun testEnsurMaxSetSizeIsHonored() =
    runTest {
      // In the event of a network error its possible we'll read from the checkpoint channel and append to the added
      // and removed blobs exceeding the maxWorkingSetSize. This test ensures the blobs
      // we send to the server are capped at the maxWorkingSetSize.
      val checkpointCalls = mutableListOf<CheckpointBlobsRequest>()
      var fakePaymentRequired = false

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/checkpoint-blobs" -> {
              val checkpointRequest =
                request.body.toByteArray().decodeToString().let {
                  gson.fromJson(it, CheckpointBlobsRequest::class.java)
                }
              checkpointCalls.add(checkpointRequest)
              if (fakePaymentRequired) {
                respond(
                  content = """{}""",
                  status = HttpStatusCode.PaymentRequired, // 402
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              } else {
                respond(
                  content = """{"new_checkpoint_id": "checkpoint-${checkpointCalls.size}"}""",
                  status = HttpStatusCode.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }
            }
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/report-error" -> HttpUtil.respondOK(this)
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      // Start - add enough blobs to trigger a checkpoint
      (1..maxWorkingSetSize * 2).forEach {
        checkpointChannel.send(BlobAddedEvent(Path.of("start-$it.txt"), "start-$it"))
      }

      manager.startCheckpointJob()

      waitForAssertionSuspend({
        assertEquals(2, checkpointCalls.size)
      })

      // Simulate payment required scenario to ensure we can fill both added and removed sets on the next checkpoint
      fakePaymentRequired = true

      // Add new blobs exceeding maxWorkingSetSize blobs
      (1..maxWorkingSetSize + 1).forEach {
        checkpointChannel.send(BlobAddedEvent(Path.of("new-$it.txt"), "new-$it"))
      }

      // Remove old blobs exceeding than maxWorkingSetSize blobs
      (1..maxWorkingSetSize + 1).forEach {
        checkpointChannel.send(BlobRemovedEvent(Path.of("start-$it.txt"), "start-$it"))
      }

      // Wait until the snapshot has the 5 blobs in each set
      waitForAssertionSuspend({
        assertEquals(5, manager.currentCheckpoint().addedBlobs.size)
        assertEquals(5, manager.currentCheckpoint().deletedBlobs.size)
      })

      fakePaymentRequired = false

      // Add one last update to trigger a checkpoint
      checkpointChannel.send(BlobAddedEvent(Path.of("kick.txt"), "kick"))

      waitForAssertionSuspend({
        val lastCall = checkpointCalls.last()
        assertEquals("checkpoint-2", lastCall.blobs.checkpointId)
        assertEquals(maxWorkingSetSize, lastCall.blobs.addedBlobs.size)
        assertEquals(maxWorkingSetSize, lastCall.blobs.deletedBlobs.size)
      })
    }
}
