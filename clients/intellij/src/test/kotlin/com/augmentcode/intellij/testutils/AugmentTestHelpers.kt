package com.augmentcode.intellij.testutils

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.HttpClientProvider
import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.AugmentOAuthService
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.metrics.ClientMetricsReporter
import com.augmentcode.intellij.metrics.MetricsReporter
import com.augmentcode.intellij.metrics.OnboardingSessionEventReporter
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.status.StateManager
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCache
import com.augmentcode.intellij.workspacemanagement.indexing.WORKSPACE_INDEX_ID
import com.intellij.ide.plugins.IdeaPluginDescriptor
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.testFramework.replaceService
import com.intellij.testFramework.unregisterService
import com.intellij.util.application
import com.intellij.util.indexing.FileBasedIndex
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import junit.framework.TestCase.assertEquals
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.runBlocking
import public_api.PublicApi.GetModelsResponse
import public_api.PublicApi.GetModelsResponse.FeatureFlags
import java.net.URI
import java.util.concurrent.CompletableFuture
import kotlin.coroutines.CoroutineContext

class AugmentTestHelpers(private val project: Project, private val testRootDisposable: Disposable) {
  private val systemPropsToRestore = mutableMapOf<String, String?>()

  fun setUp() {
    resetGlobalServices()
    registerMockEngine(baseMockEngine()) // Ensure the API is mocked
    preventOAuthBrowserFlow()

    // Run this in setup only since if a test mocks AugmentRemoteSyncingManager
    // and they don't allow for reset() to be mocked (i.e. not a relaxed mock)
    // then the teardown can fail.
    runBlocking {
      AugmentRemoteSyncingManager.getInstance(project).reset()
    }
  }

  fun tearDown() {
    resetGlobalServices()
  }

  fun resetGlobalServices() {
    // Clean up mocks
    unmockkAll()

    restoreSystemProperties()

    // TODO: Instead of this, can we replace the AugmentSettings service
    // This is currently using persistent state.
    AugmentSettings.instance.modelName = ""
    AugmentSettings.instance.apiToken = ""
    AugmentSettings.instance.completionURL = ""
    AugmentSettings.instance.inlineCompletionEnabled = true

    AugmentOAuthState.instance.clear()
    AugmentAppStateService.instance.reset()
    StateManager.getInstance(project).reset()
    MTimeCache.getInstance(project).reset()
    BlobNameService.getInstance(project).clear()

    AugmentAPI.instance.clearModelCache()
    AugmentAPI.instance.reset()

    val metricsReports: List<MetricsReporter<*>> =
      listOf(
        ClientMetricsReporter.getInstance(project),
        OnboardingSessionEventReporter.getInstance(project),
      )
    metricsReports.forEach {
      it.reset()
    }

    unregisterServiceIfNeeded(PathFilterService::class.java) // to clean it lazily
  }

  fun registerMockEngine(mockEngine: MockEngine) {
    application.registerOrReplaceServiceInstance(
      HttpClientProvider::class.java,
      object : HttpClientProvider {
        override fun clientFor(uri: URI): HttpClient {
          return HttpClient(mockEngine) {
            // Install the HttpTimeout plugin that AugmentHttpClient expects
            install(io.ktor.client.plugins.HttpTimeout)
          }
        }
      },
      testRootDisposable,
    )
  }

  // Override this if you want to change the mock engine implementation for all
  // your tests
  fun baseMockEngine(): MockEngine {
    return MockEngine { request ->
      when (request.url.encodedPath) {
        "/get-models" -> HttpUtil.respondGetModels(this)
        "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
        "/report-feature-vector" -> HttpUtil.respondOK(this)
        else -> throw IllegalStateException("Unexpected request to ${request.url.encodedPath}")
      }
    }
  }

  fun createCoroutineScope(context: CoroutineContext): CoroutineScope {
    val cs = CoroutineScope(context)
    Disposer.register(testRootDisposable) {
      cs.cancel("Test disposed")
    }
    return cs
  }

  fun unregisterServiceIfNeeded(serviceInterface: Class<*>) {
    if (project.getService(serviceInterface) != null) {
      try {
        project.unregisterService(serviceInterface)
      } catch (e: Exception) {
        // Ignore exceptions during cleanup
      }
    }
  }

  private fun preventOAuthBrowserFlow() {
    val mockOAuthService = mockk<AugmentOAuthService>(relaxed = true)
    every { mockOAuthService.authorize() } returns CompletableFuture.completedFuture(null)

    mockkObject(AugmentOAuthService.Companion)
    every { AugmentOAuthService.instance } returns mockOAuthService

    application.replaceService(AugmentOAuthService::class.java, mockOAuthService, testRootDisposable)
  }

  fun createGetModelsResponse(flags: FeatureFlags = FeatureFlags.newBuilder().build()): GetModelsResponse {
    val getModelsResponseBuilder = public_api.PublicApi.GetModelsResponse.newBuilder()

    getModelsResponseBuilder.defaultModel = "default-model"

    getModelsResponseBuilder.addAllLanguages(
      listOf(
        public_api.PublicApi.Language.newBuilder()
          .setName("Text")
          .addExtensions(".txt")
          .build(),
        public_api.PublicApi.Language.newBuilder()
          .setName("Go")
          .addExtensions(".go")
          .build(),
        public_api.PublicApi.Language.newBuilder()
          .setName("XML")
          .addExtensions(".xml")
          .build(),
        public_api.PublicApi.Language.newBuilder()
          .setName("Java")
          .addExtensions(".java")
          .build(),
      ),
    )

    getModelsResponseBuilder.addAllModels(
      listOf(
        public_api.PublicApi.Model.newBuilder()
          .setName("default-model")
          .setSuggestedPrefixCharCount(HttpUtil.PREFIX_CHAR_COUNT)
          .setSuggestedSuffixCharCount(HttpUtil.SUFFIX_CHAR_COUNT)
          .build(),
        public_api.PublicApi.Model.newBuilder()
          .setName("alternative-model")
          .setSuggestedPrefixCharCount(9192)
          .setSuggestedSuffixCharCount(9192)
          .build(),
      ),
    )

    getModelsResponseBuilder.featureFlags = flags

    return getModelsResponseBuilder.build()
  }

  fun forcePluginState(
    state: PluginState,
    modelConfig: GetModelsResponse = createGetModelsResponse(),
  ) {
    var ctx = PluginContext(false, DefaultFeatureFlags, null)

    // If we are enabled, set up credentials + model config
    if (state == PluginState.ENABLED) {
      ctx =
        PluginContext(
          true,
          flags = com.augmentcode.intellij.featureflags.FeatureFlags.fromGetModelsResponse(modelConfig),
          model = AugmentModel.fromGetModelsResponse(modelConfig),
        )
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
    }

    val mockAugmentAppStateService =
      mockk<AugmentAppStateService>(relaxed = true).apply {
        every { <EMAIL> } returns state
        every { <EMAIL> } returns ctx
        every { <EMAIL>(any(), any(), any()) } answers {
          val listener = secondArg<PluginStateListener>()
          val triggerOnStateChange = thirdArg<Boolean>()
          if (triggerOnStateChange) {
            // NOTE: This will typically run on a coroutine, for tests we run it on the current thread to avoid
            // needing waitForAssertions() in all of our tests.
            listener.onStateChange(ctx, state)
          }
        }
      }

    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )
  }

  fun emulateSignInFlow(waitForStateUpdate: Boolean = false) {
    val exampleCredentials = AugmentCredentials("example access token", "http://example.augmentcode.com")
    AugmentOAuthState.instance.saveCredentials(exampleCredentials)

    // AugmentAppStateService needs to be reset to trigger an update
    AugmentAppStateService.instance.reset()

    if (waitForStateUpdate) {
      waitForAssertion({
        assertEquals(PluginState.ENABLED, AugmentAppStateService.instance.state)
      }, 5000) // 5 seconds, just in case the app state changes are slow for any reason
    }
  }

  fun emulateSignOutFlow() {
    AugmentOAuthState.instance.clear()
    AugmentAppStateService.instance.reset()
  }

  /**
   * Sometimes if a file is configured in a test fixture, the indexing for it won't
   * actually occur until we try to read from it. This can lead to
   * flaky tests and much sadness. This method forces indexing to occur.
   *
   * @return The indexed filepath if it has been indexed, null otherwise.
   */
  fun forceIndexingForFile(file: VirtualFile): String? {
    return runReadAction {
      FileBasedIndex.getInstance().getFileData(WORKSPACE_INDEX_ID, file, project)
        .keys.lastOrNull()
    }
  }

  fun restoreSystemProperties() {
    for ((key, value) in systemPropsToRestore) {
      if (value == null) {
        System.clearProperty(key)
      } else {
        System.setProperty(key, value)
      }
    }
    systemPropsToRestore.clear()
  }

  fun setSystemProperty(
    key: String,
    value: String,
  ) {
    systemPropsToRestore[key] = System.getProperty(key)
    System.setProperty(key, value)
  }

  /**
   * Mocks the plugin version to the given version.
   */
  fun mockPluginVersion(version: String?) {
    // Mock the PluginManager to return our test plugin version
    mockkStatic(PluginManager::class)
    val mockPluginManager = io.mockk.mockk<PluginManager>()

    // Set up the chain of calls
    every { PluginManager.getInstance() } answers {
      mockPluginManager
    }

    if (version == null) {
      every { mockPluginManager.findEnabledPlugin(PluginId.getId("com.augmentcode")) } returns null
    } else {
      val mockPlugin = io.mockk.mockk<IdeaPluginDescriptor>()
      every { mockPlugin.version } returns version

      every { mockPluginManager.findEnabledPlugin(PluginId.getId("com.augmentcode")) } answers {
        mockPlugin
      }
    }
  }
}
