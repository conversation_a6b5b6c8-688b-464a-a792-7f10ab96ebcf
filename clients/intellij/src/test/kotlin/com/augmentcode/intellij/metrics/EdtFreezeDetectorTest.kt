package com.augmentcode.intellij.metrics

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.utils.CustomPropertyReader
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class EdtFreezeDetectorTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    mockkObject(CustomPropertyReader)
  }

  override fun tearDown() {
    unmockkObject(CustomPropertyReader)
    super.tearDown()
  }

  @Test
  fun `test shouldEnableEdtFreezeDetection when custom property is true`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY) } returns "true"

    val shouldEnable = EdtFreezeDetector.shouldEnableEdtFreezeDetection(false)
    assertTrue("EDT freeze detection should be enabled when custom property is true", shouldEnable)

    val shouldEnableWithFlagTrue = EdtFreezeDetector.shouldEnableEdtFreezeDetection(true)
    assertTrue("EDT freeze detection should be enabled when custom property is true", shouldEnableWithFlagTrue)
  }

  @Test
  fun `test shouldEnableEdtFreezeDetection when custom property is false`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY) } returns "false"

    val shouldEnable = EdtFreezeDetector.shouldEnableEdtFreezeDetection(true)
    assertFalse("EDT freeze detection should be disabled when custom property is false", shouldEnable)

    val shouldEnableWithFlagFalse = EdtFreezeDetector.shouldEnableEdtFreezeDetection(false)
    assertFalse("EDT freeze detection should be disabled when custom property is false", shouldEnableWithFlagFalse)
  }

  @Test
  fun `test shouldEnableEdtFreezeDetection when custom property is not set`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY) } returns null

    assertTrue(
      "Should fall back to feature flag when custom property is not set",
      EdtFreezeDetector.shouldEnableEdtFreezeDetection(true),
    )
    assertFalse(
      "Should fall back to feature flag when custom property is not set",
      EdtFreezeDetector.shouldEnableEdtFreezeDetection(false),
    )
  }

  @Test
  fun `test shouldEnableEdtFreezeDetection with case insensitive property values`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY) } returns "TRUE"
    assertTrue("Should handle uppercase TRUE", EdtFreezeDetector.shouldEnableEdtFreezeDetection(false))

    every { CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY) } returns "False"
    assertFalse("Should handle mixed case False", EdtFreezeDetector.shouldEnableEdtFreezeDetection(true))

    every { CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY) } returns "FALSE"
    assertFalse("Should handle uppercase FALSE", EdtFreezeDetector.shouldEnableEdtFreezeDetection(true))
  }

  @Test
  fun `test shouldEnableEdtFreezeDetection with invalid property values`() {
    every { CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY) } returns "invalid"

    assertTrue(
      "Should fall back to feature flag when property has invalid value",
      EdtFreezeDetector.shouldEnableEdtFreezeDetection(true),
    )
    assertFalse(
      "Should fall back to feature flag when property has invalid value",
      EdtFreezeDetector.shouldEnableEdtFreezeDetection(false),
    )
  }
}
