package com.augmentcode.intellij.utils

import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertNull
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class PathUtilTest {
  // Direct unit tests for fixOverlappingRelPath method
  @Test
  fun testFixOverlappingRelPathBasicOverlap() {
    // Test basic single segment overlap
    val result = fixOverlappingRelPath("/home/<USER>/augment/clients", "clients/main.py")
    assertEquals("Should return corrected path", "main.py", result)
  }

  @Test
  fun testFixOverlappingRelPathMultipleSegmentOverlap() {
    // Test multiple segment overlap
    val result = fixOverlappingRelPath("/home/<USER>/project/src/main", "src/main/java/App.java")
    assertEquals("Should return corrected path", "java/App.java", result)
  }

  @Test
  fun testFixOverlappingRelPathNoOverlap() {
    // Test no overlap
    val result = fixOverlappingRelPath("/home/<USER>/augment/clients", "different/main.py")
    assertNull("Should return null for no overlap", result)
  }

  @Test
  fun testFixOverlappingRelPathPartialOverlap() {
    // Test partial overlap (should not match)
    val result = fixOverlappingRelPath("/home/<USER>/myproject/src", "project/src/main.py")
    assertNull("Should return null for partial overlap", result)
  }

  @Test
  fun testFixOverlappingRelPathExactMatch() {
    // Test exact match of the last segment
    val result = fixOverlappingRelPath("/home/<USER>/workspace", "workspace")
    assertEquals("Should return empty string for exact match", "", result)
  }

  @Test
  fun testFixOverlappingRelPathWithEmptySegments() {
    // Test with empty segments (double slashes)
    val result = fixOverlappingRelPath("/home/<USER>/project/src", "src//main//App.java")
    assertEquals("Should filter empty segments", "main/App.java", result)
  }

  @Test
  fun testFixOverlappingRelPathEmptyInputs() {
    // Test with empty given path
    val result1 = fixOverlappingRelPath("/home/<USER>/project", "")
    assertNull("Should return null for empty given path", result1)

    // Test with empty root path
    val result2 = fixOverlappingRelPath("", "src/main.py")
    assertNull("Should return null for empty root path", result2)
  }

  @Test
  fun testFixOverlappingRelPathComplexOverlap() {
    // Test complex nested overlap
    val result =
      fixOverlappingRelPath(
        "/Users/<USER>/projects/myapp/frontend/src/components",
        "frontend/src/components/ui/Button/index.tsx",
      )
    assertEquals("Should handle complex overlap", "ui/Button/index.tsx", result)
  }

  @Test
  fun testFixOverlappingRelPathLongestOverlap() {
    // Test that it finds the longest possible overlap
    val result =
      fixOverlappingRelPath(
        "/home/<USER>/project/src/main/java",
        "main/java/com/example/App.java",
      )
    assertEquals("Should find longest overlap", "com/example/App.java", result)
  }

  @Test
  fun testFixOverlappingRelPathCaseSensitive() {
    // Test case sensitivity
    val result = fixOverlappingRelPath("/home/<USER>/Project/src", "project/src/main.py")
    assertNull("Should be case sensitive", result)
  }

  @Test
  fun testFixOverlappingRelPathSingleSegmentRoot() {
    // Test with single segment root
    val result = fixOverlappingRelPath("/src", "src/main.py")
    assertEquals("Should work with single segment root", "main.py", result)
  }

  @Test
  fun testFixOverlappingRelPathNormalizedPaths() {
    // Test with paths that need normalization
    val result = fixOverlappingRelPath("/home/<USER>/project/../project/src", "src/main.py")
    assertEquals("Should normalize root path", "main.py", result)
  }
}
