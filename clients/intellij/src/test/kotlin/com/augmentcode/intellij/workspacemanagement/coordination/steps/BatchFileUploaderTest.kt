package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCache
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.google.protobuf.util.JsonFormat
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.LightVirtualFile
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.withContext
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.BatchUploadRequest
import public_api.PublicApi.BatchUploadResponse

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(JUnit4::class)
class BatchFileUploaderTest : AugmentBasePlatformTestCase() {
  private lateinit var uploadFile: BatchFileUploader
  private lateinit var inputChannel: RoughlySizedChannel<FileToUpload>
  private lateinit var outputChannel: RoughlySizedChannel<CoordinationFileDetailsWithBlob>

  override fun setUp() {
    super.setUp()

    augmentHelpers().forcePluginState(PluginState.ENABLED)

    // Create fresh channels for each test
    inputChannel = RoughlySizedChannel(Channel(Channel.Factory.UNLIMITED))
    outputChannel = RoughlySizedChannel(Channel(Channel.Factory.UNLIMITED))
  }

  override fun tearDown() {
    Disposer.dispose(uploadFile)
    inputChannel.close()
    outputChannel.close()
    super.tearDown()
  }

  @Test
  fun testSuccessfulUpload() =
    kotlinx.coroutines.test.runTest {
      try {
        uploadFile =
          BatchFileUploader(
            project = project,
            scope = this,
            inputChannel = inputChannel,
            outputChannel = outputChannel,
          )
        uploadFile.startProcessing()

        val mockEngine =
          MockEngine.Companion { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/batch-upload" -> {
                val requestBody = request.body.toByteArray().decodeToString()
                val uploadRequest =
                  BatchUploadRequest.newBuilder().apply {
                    JsonFormat.parser().merge(requestBody, this)
                  }.build()

                respond(
                  content =
                    JsonFormat.printer().print(
                      BatchUploadResponse.newBuilder().apply {
                        addAllBlobNames(
                          uploadRequest.blobsList.map {
                            AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                              it.path,
                              it.content,
                            )
                          },
                        )
                      }.build(),
                    ),
                  status = HttpStatusCode.Companion.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }

              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        val uploadRequest = createUploadRequest("test.txt", "test content")

        inputChannel.send(uploadRequest)

        // Wait for processing
        withContext(Dispatchers.Default) {
          waitForAssertion({
            assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
          })
        }

        // Verify output
        var result: CoordinationFileDetailsWithBlob? = null
        waitForAssertion({
          advanceUntilIdle()
          result = outputChannel.tryReceive().getOrNull()
          assertNotNull(result)
        }, timeoutMs = 1000)
        assertEquals("test.txt", result!!.fileDetails.relPath)
      } finally {
        if (::uploadFile.isInitialized) {
          Disposer.dispose(uploadFile)
        }
      }
    }

  @Test
  fun testDiffBlobNameFromUpload() =
    kotlinx.coroutines.test.runTest {
      try {
        uploadFile =
          BatchFileUploader(
            project = project,
            scope = this,
            inputChannel = inputChannel,
            outputChannel = outputChannel,
          )
        uploadFile.startProcessing()

        val mockEngine =
          MockEngine.Companion { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/batch-upload" -> {
                val requestBody = request.body.toByteArray().decodeToString()
                val uploadRequest =
                  BatchUploadRequest.newBuilder().apply {
                    JsonFormat.parser().merge(requestBody, this)
                  }.build()

                respond(
                  content =
                    JsonFormat.printer().print(
                      BatchUploadResponse.newBuilder().apply {
                        addAllBlobNames(
                          uploadRequest.blobsList.map {
                            "${
                              AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                                it.path,
                                it.content,
                              )
                            }-diff"
                          },
                        )
                      }.build(),
                    ),
                  status = HttpStatusCode.Companion.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }

              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        val uploadRequest = createUploadRequest("test.txt", "test content")

        inputChannel.send(uploadRequest)

        // Wait for processing
        withContext(Dispatchers.Default) {
          waitForAssertion({
            assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
          })
        }

        // Verify output
        val expectedBlobName =
          "${
            AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
              uploadRequest.fileDetails.relPath,
              uploadRequest.fileDetails.virtualFile.contentsToByteArray().decodeToString(),
            )
          }-diff"
        var result: CoordinationFileDetailsWithBlob? = null
        waitForAssertion({
          result = outputChannel.tryReceive().getOrNull()
          assertNotNull(result)
        }, timeoutMs = 1000)
        assertEquals("test.txt", result!!.fileDetails.relPath)
        assertEquals(
          expectedBlobName,
          result!!.remoteBlobName,
        )

        assertEquals(expectedBlobName, MTimeCache.getInstance(project).get(uploadRequest.fileDetails.virtualFile.path)!!.blobName)
      } finally {
        if (::uploadFile.isInitialized) {
          Disposer.dispose(uploadFile)
        }
      }
    }

  @Test
  fun testUploadFailure() =
    kotlinx.coroutines.test.runTest {
      try {
        uploadFile =
          BatchFileUploader(
            project = project,
            scope = this,
            inputChannel = inputChannel,
            outputChannel = outputChannel,
          )
        uploadFile.startProcessing()

        val mockEngine =
          MockEngine.Companion { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/batch-upload" ->
                respond(
                  content = "Server error",
                  status = HttpStatusCode.Companion.InternalServerError,
                )

              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        val uploadRequest = createUploadRequest("test.txt", "test content")

        inputChannel.send(uploadRequest)

        // Should try once and retry failed uploads 3 times, a total of 4 attempts. Failed uploads should not be send to output channel
        // loop from 1 to MAX_RETRY_COUNT inclusive checking if uploaded and asserting output channel is empty
        for (i in 1..FileToUpload.Companion.MAX_RETRY_COUNT) {
          waitForAssertion({
            advanceUntilIdle()
            assertTrue(mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" } >= i)
          }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS + 100)
          assertNull(outputChannel.tryReceive().getOrNull())
        }
        // Wait additional time to ensure no more retries happen
        advanceTimeBy(BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 2)
        withContext(Dispatchers.Default) {
          waitForAssertion({
            assertEquals(
              FileToUpload.Companion.MAX_RETRY_COUNT + 1,
              mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" },
            )
          })
        }
      } finally {
        if (::uploadFile.isInitialized) {
          Disposer.dispose(uploadFile)
        }
      }
    }

  @Test
  fun testBatchSizeLimit() =
    kotlinx.coroutines.test.runTest {
      try {
        uploadFile =
          BatchFileUploader(
            project = project,
            scope = this,
            inputChannel = inputChannel,
            outputChannel = outputChannel,
          )
        uploadFile.startProcessing()

        val mockEngine =
          MockEngine.Companion { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/batch-upload" -> {
                val requestBody = request.body.toByteArray().decodeToString()
                val uploadRequest =
                  BatchUploadRequest.newBuilder().apply {
                    JsonFormat.parser().merge(requestBody, this)
                  }.build()

                // Verify batch size doesn't exceed limit
                assertTrue(
                  "Batch size ${uploadRequest.blobsList.size} exceeds MAX_BATCH_SIZE",
                  uploadRequest.blobsList.size <= BatchFileUploader.MAX_BATCH_SIZE,
                )

                respond(
                  content =
                    JsonFormat.printer().print(
                      BatchUploadResponse.newBuilder().apply {
                        addAllBlobNames(
                          uploadRequest.blobsList.map {
                            AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                              it.path,
                              it.content,
                            )
                          },
                        )
                      }.build(),
                    ),
                  status = HttpStatusCode.Companion.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }

              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Send more items than batch size
        val uploadRequests =
          (0 until (3 * BatchFileUploader.Companion.MAX_BATCH_SIZE)).map { i ->
            createUploadRequest("test$i.txt", "content$i")
          }
        uploadRequests.forEach { inputChannel.send(it) }

        // Should result in multiple batches
        waitForAssertion({
          advanceUntilIdle()
          assertTrue(mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" } >= 1)
        }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 20 + 100)
      } finally {
        if (::uploadFile.isInitialized) {
          Disposer.dispose(uploadFile)
        }
      }
    }

  @Test
  fun testBatchContentSizeLimit() =
    kotlinx.coroutines.test.runTest {
      try {
        uploadFile =
          BatchFileUploader(
            project = project,
            scope = this,
            inputChannel = inputChannel,
            outputChannel = outputChannel,
          )
        uploadFile.startProcessing()

        val mockEngine =
          MockEngine.Companion { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/batch-upload" -> {
                val requestBody = request.body.toByteArray().decodeToString()
                val uploadRequest =
                  BatchUploadRequest.newBuilder().apply {
                    JsonFormat.parser().merge(requestBody, this)
                  }.build()

                // Each batch should contain exactly one blob
                assertEquals(1, uploadRequest.blobsList.size)

                respond(
                  content =
                    JsonFormat.printer().print(
                      BatchUploadResponse.newBuilder().apply {
                        addAllBlobNames(
                          uploadRequest.blobsList.map {
                            AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                              it.path,
                              it.content,
                            )
                          },
                        )
                      }.build(),
                    ),
                  status = HttpStatusCode.Companion.OK,
                  headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )
              }

              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        // Create two files, each one byte under the content limit
        val largeContent1 = "a".repeat(BatchFileUploader.MAX_BATCH_CONTENT_SIZE_BYTES - 1)
        val largeContent2 = "b".repeat(BatchFileUploader.MAX_BATCH_CONTENT_SIZE_BYTES - 1)

        val uploadRequest1 = createUploadRequest("large1.txt", largeContent1)
        val uploadRequest2 = createUploadRequest("large2.txt", largeContent2)

        inputChannel.send(uploadRequest1)
        inputChannel.send(uploadRequest2)

        // Should result in two separate batches
        withContext(Dispatchers.Default) {
          waitForAssertion({
            assertEquals(2, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
          }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 3 + 100)
        }
      } finally {
        if (::uploadFile.isInitialized) {
          Disposer.dispose(uploadFile)
        }
      }
    }

  @Test
  fun testPartialBatchFailure() {
    kotlinx.coroutines.test.runTest {
      try {
        uploadFile =
          BatchFileUploader(
            project = project,
            scope = this,
            inputChannel = inputChannel,
            outputChannel = outputChannel,
          )
        uploadFile.startProcessing()

        var requestCount = 0
        val mockEngine =
          MockEngine.Companion { request ->
            when (request.url.encodedPath) {
              "/get-models" -> HttpUtil.respondGetModels(this)
              "/batch-upload" -> {
                requestCount++
                println("Request count: $requestCount")
                if (requestCount == 1) {
                  println("Error")
                  // First request fails
                  respond(content = "Server error", status = HttpStatusCode.Companion.InternalServerError)
                } else {
                  println("Success")
                  // Retry succeeds
                  val requestBody = request.body.toByteArray().decodeToString()
                  val uploadRequest =
                    BatchUploadRequest.newBuilder().apply {
                      JsonFormat.parser().merge(requestBody, this)
                    }.build()
                  respond(
                    content =
                      JsonFormat.printer().print(
                        BatchUploadResponse.newBuilder().apply {
                          addAllBlobNames(
                            uploadRequest.blobsList.map {
                              AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(
                                it.path,
                                it.content,
                              )
                            },
                          )
                        }.build(),
                      ),
                    status = HttpStatusCode.Companion.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json"),
                  )
                }
              }

              else -> error("Unexpected request to ${request.url.encodedPath}")
            }
          }
        augmentHelpers().registerMockEngine(mockEngine)

        val uploadRequest = createUploadRequest("test.txt", "test content")
        inputChannel.send(uploadRequest)

        // Should eventually succeed after retry
        withContext(Dispatchers.Default) {
          waitForAssertion({
            advanceUntilIdle()
            assertTrue(mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" } >= 2)
          }, timeoutMs = BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 3)
        }

        var result: CoordinationFileDetailsWithBlob? = null
        waitForAssertion({
          result = outputChannel.tryReceive().getOrNull()
          assertNotNull(result)
        }, timeoutMs = 1000)
        assertEquals("test.txt", result!!.fileDetails.relPath)
      } finally {
        if (::uploadFile.isInitialized) {
          Disposer.dispose(uploadFile)
        }
      }
    }
  }

  @Test
  fun testApiUnavailable() {
    kotlinx.coroutines.test.runTest {
      try {
        uploadFile =
          BatchFileUploader(
            project = project,
            scope = this,
            inputChannel = inputChannel,
            outputChannel = outputChannel,
          )
        uploadFile.startProcessing()

        // Clear API settings to make API unavailable
        AugmentSettings.Companion.instance.apiToken = null
        AugmentSettings.Companion.instance.completionURL = null

        val mockEngine =
          MockEngine.Companion { _ ->
            fail("No requests should be made when API is unavailable")
            respond(content = "", status = HttpStatusCode.Companion.OK)
          }
        augmentHelpers().registerMockEngine(mockEngine)

        val uploadRequest = createUploadRequest("test.txt", "test content")
        inputChannel.send(uploadRequest)

        // Wait and verify no requests were made
        advanceTimeBy(BatchFileUploader.Companion.UPLOAD_CADENCE_MS * 2)
        assertEquals(0, mockEngine.requestHistory.size)
        assertNull(outputChannel.tryReceive().getOrNull())
      } finally {
        if (::uploadFile.isInitialized) {
          Disposer.dispose(uploadFile)
        }
      }
    }
  }

  private fun createUploadRequest(
    relPath: String,
    content: String,
    fileReference: VirtualFile? = null,
  ) = FileToUpload(
    fileDetails =
      CoordinationFileDetails(
        virtualFile = fileReference ?: LightVirtualFile(relPath, content),
        rootPath = "/src",
        relPath = relPath,
      ),
    expectedBlobName = AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName(relPath, content),
  )
}
