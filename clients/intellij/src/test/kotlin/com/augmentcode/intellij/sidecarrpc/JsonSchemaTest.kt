package com.augmentcode.intellij.sidecarrpc

import com.augmentcode.sidecar.rpc.ReadFileInputSchema
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class JsonSchemaTest {
  @Test
  fun testConvertToJsonSchema() {
    val schema = convertToJsonSchema(ReadFileInputSchema.getDescriptor())
    @Suppress("ktlint:standard:max-line-length")
    assertEquals(
      """{"type":"object","properties":{"file_path":{"type":"string","description":"The path of the file to read."}},"required":["file_path"]}""",
      schema,
    )
  }
}
