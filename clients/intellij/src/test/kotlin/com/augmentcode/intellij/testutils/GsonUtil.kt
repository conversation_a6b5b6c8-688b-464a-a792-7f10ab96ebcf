package com.augmentcode.intellij.testutils

import com.augmentcode.api.NumericEnum
import com.google.gson.FieldNamingPolicy
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import java.lang.reflect.Type

object GsonUtil {
  fun createApiGson(): Gson {
    val enumSerializer =
      object : JsonSerializer<NumericEnum> {
        override fun serialize(
          src: NumericEnum,
          typeOfSrc: Type,
          context: JsonSerializationContext,
        ): JsonElement {
          return JsonPrimitive(src.getValue())
        }
      }

    return GsonBuilder()
      .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
      .registerTypeHierarchyAdapter(Enum::class.java, enumSerializer)
      .create()
  }
}
