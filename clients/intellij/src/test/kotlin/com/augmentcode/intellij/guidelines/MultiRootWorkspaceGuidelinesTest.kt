package com.augmentcode.intellij.guidelines

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.augmentcode.intellij.testutils.FeatureFlagsTestUtil
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.PsiTestUtil
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi
import java.io.File

/**
 * Tests for the workspace guidelines functionality in multi-root project setups.
 * This test class uses HeavyPlatformTestCase to be able to create real project roots.
 */
@RunWith(JUnit4::class)
class MultiRootWorkspaceGuidelinesTest : AugmentHeavyPlatformTestCase() {
  private lateinit var guidelinesService: GuidelinesService
  private lateinit var featureFlags: PublicApi.GetModelsResponse.FeatureFlags

  override fun setUp() {
    super.setUp()
    guidelinesService = GuidelinesService.getInstance(project)
    featureFlags = PublicApi.GetModelsResponse.FeatureFlags.newBuilder().build()

    // Set up feature flags using the test utility
    FeatureFlagsTestUtil.setupFeatureFlags(
      testRootDisposable,
      mapOf(),
    )
  }

  /**
   * Test that the workspace guidelines file is created in the correct root directory
   * when there are multiple project roots.
   */
  @Test
  fun testOpenWorkspaceGuidelinesInMultiRootProject() {
    // Create sibling directories in the project root
    val tempDir = getVirtualFile(createTempDirectory())
    val (leftRoot, rightRoot) =
      runWriteAction {
        val left = tempDir.createChildDirectory(this, "leftrepo")
        assertNotNull("Should create leftrepo", left)
        val right = tempDir.createChildDirectory(this, "rightrepo")
        assertNotNull("Should create rightrepo", right)
        Pair(left, right)
      }

    // Configure both directories as content roots
    PsiTestUtil.addContentRoot(module, leftRoot)
    PsiTestUtil.addContentRoot(module, rightRoot)

    // Create a guidelines file in the left repo
    val guidelinesFile = File(leftRoot.path, GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH)
    guidelinesFile.createNewFile()

    // Set the current file to be in the left repo to make it the active root
    val testFile =
      runWriteAction {
        leftRoot.createChildData(this, "test.txt")
      }
    assertNotNull("Should create test file", testFile)
    FileEditorManager.getInstance(project).openFile(testFile, true)

    // Call the method
    guidelinesService.openWorkspaceGuidelines()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Workspace guidelines file should be opened in editor",
      openFiles.any { it.name == GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH },
    )
  }

  /**
   * Test that the workspace guidelines file is created in the right root directory
   * when switching between multiple project roots.
   */
  @Test
  fun testOpenWorkspaceGuidelinesInDifferentRoot() {
    // Create sibling directories in the project root
    val tempDir = getVirtualFile(createTempDirectory())
    val (leftRoot, rightRoot) =
      runWriteAction {
        val left = tempDir.createChildDirectory(this, "leftrepo")
        assertNotNull("Should create leftrepo", left)
        val right = tempDir.createChildDirectory(this, "rightrepo")
        assertNotNull("Should create rightrepo", right)
        Pair(left, right)
      }

    // Configure both directories as content roots
    PsiTestUtil.addContentRoot(module, leftRoot)
    PsiTestUtil.addContentRoot(module, rightRoot)

    // Create guidelines files in both repos
    val leftGuidelinesFile = File(leftRoot.path, GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH)
    leftGuidelinesFile.createNewFile()
    val rightGuidelinesFile = File(rightRoot.path, GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH)
    rightGuidelinesFile.createNewFile()

    // Set the current file to be in the right repo to make it the active root
    val rightTestFile =
      runWriteAction {
        rightRoot.createChildData(this, "right_test.txt")
      }
    assertNotNull("Should create right test file", rightTestFile)
    FileEditorManager.getInstance(project).openFile(rightTestFile, true)

    // Call the method
    guidelinesService.openWorkspaceGuidelines()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the right repo file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Right repo guidelines file should be opened in editor",
      openFiles.any { it.path.contains("rightrepo") && it.name == GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH },
    )

    // Close all files
    val fileEditorManager = FileEditorManager.getInstance(project)
    for (file in fileEditorManager.openFiles) {
      fileEditorManager.closeFile(file)
    }

    // Now set the current file to be in the left repo to make it the active root
    val leftTestFile =
      runWriteAction {
        leftRoot.createChildData(this, "left_test.txt")
      }
    assertNotNull("Should create left test file", leftTestFile)
    FileEditorManager.getInstance(project).openFile(leftTestFile, true)

    // Call the method again
    guidelinesService.openWorkspaceGuidelines()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the left repo file is now open in the editor
    val newOpenFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Left repo guidelines file should be opened in editor",
      newOpenFiles.any { it.path.contains("leftrepo") && it.name == GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH },
    )
  }

  /**
   * Test that the workspace guidelines file is not created when no root is available.
   */
  @Test
  fun testOpenWorkspaceGuidelinesWithNoRoot() {
    // Create sibling directories in the project root
    val tempDir = getVirtualFile(createTempDirectory())
    val (leftRoot, rightRoot) =
      runWriteAction {
        val left = tempDir.createChildDirectory(this, "leftrepo")
        assertNotNull("Should create leftrepo", left)
        val right = tempDir.createChildDirectory(this, "rightrepo")
        assertNotNull("Should create rightrepo", right)
        Pair(left, right)
      }

    // Configure both directories as content roots but don't open any files in them
    // This simulates a scenario where no root is active
    PsiTestUtil.addContentRoot(module, leftRoot)
    PsiTestUtil.addContentRoot(module, rightRoot)

    // Close any open files to ensure no root is active
    val fileEditorManager = FileEditorManager.getInstance(project)
    for (file in fileEditorManager.openFiles) {
      fileEditorManager.closeFile(file)
    }

    // Mock AugmentRoot to return null (no root available)
    mockkObject(AugmentRoot)
    every { AugmentRoot.findActiveProjectRoot(project) } returns null

    // Call the method
    guidelinesService.openWorkspaceGuidelines()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that no file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertFalse(
      "No guidelines file should be opened in editor when no root is available",
      openFiles.any { it.name == GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH },
    )
  }

  /**
   * Test that the workspace guidelines file is created in the correct root directory
   * when there are nested project roots.
   */
  @Test
  fun testOpenWorkspaceGuidelinesWithNestedRoots() {
    // Create nested directories in the project root
    val tempDir = getVirtualFile(createTempDirectory())
    val (parentRoot, childRoot) =
      runWriteAction {
        val parent = tempDir.createChildDirectory(this, "parentrepo")
        assertNotNull("Should create parentrepo", parent)
        val child = parent.createChildDirectory(this, "childrepo")
        assertNotNull("Should create childrepo", child)
        Pair(parent, child)
      }

    // Configure both directories as content roots
    PsiTestUtil.addContentRoot(module, parentRoot)
    PsiTestUtil.addContentRoot(module, childRoot)

    // Create guidelines files in both repos
    val parentGuidelinesFile = File(parentRoot.path, GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH)
    parentGuidelinesFile.createNewFile()
    val childGuidelinesFile = File(childRoot.path, GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH)
    childGuidelinesFile.createNewFile()

    // Set the current file to be in the child repo to make it the active root
    val childTestFile =
      runWriteAction {
        childRoot.createChildData(this, "child_test.txt")
      }
    assertNotNull("Should create child test file", childTestFile)
    FileEditorManager.getInstance(project).openFile(childTestFile, true)

    // Mock AugmentRoot to return the child root
    mockkObject(AugmentRoot)
    every { AugmentRoot.findActiveProjectRoot(project) } returns childRoot

    // Call the method
    guidelinesService.openWorkspaceGuidelines()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the child repo file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Child repo guidelines file should be opened in editor",
      openFiles.any { it.path.contains("childrepo") && it.name == GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH },
    )
  }
}
