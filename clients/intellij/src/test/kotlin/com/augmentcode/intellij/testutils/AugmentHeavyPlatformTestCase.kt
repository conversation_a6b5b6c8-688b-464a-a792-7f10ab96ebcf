package com.augmentcode.intellij.testutils

import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.HeavyPlatformTestCase
import com.intellij.testFramework.PsiTestUtil

abstract class AugmentHeavyPlatformTestCase : HeavyPlatformTestCase() {
  private lateinit var augmentHelpers: AugmentTestHelpers

  override fun setUp() {
    super.setUp()
    augmentHelpers = AugmentTestHelpers(myProject, testRootDisposable)
    augmentHelpers.setUp()
  }

  override fun tearDown() {
    augmentHelpers.tearDown()

    super.tearDown()
  }

  protected fun augmentHelpers() = augmentHelpers

  fun createLocalFiles(files: Map<String, String>): LocalFiles {
    // Create directory in the project root
    val tempDir = getVirtualFile(createTempDirectory())

    // Add tempDir as contentRoot
    PsiTestUtil.addContentRoot(module, tempDir)

    val vFiles = mutableMapOf<String, VirtualFile>()

    // Create files
    for ((path, content) in files) {
      // This is necessary to handle paths with Windows separators on Unix systems
      val independentPath = FileUtil.toSystemIndependentName(path)
      val file =
        if (independentPath.contains('/')) {
          val parts = independentPath.split('/')
          val fileName = parts.last()
          val dirPath = parts.dropLast(1)

          var currentDir = tempDir
          for (dir in dirPath) {
            runWriteAction {
              currentDir = currentDir.findChild(dir) ?: currentDir.createChildDirectory(this, dir)
            }
          }
          runWriteAction {
            currentDir.createChildData(this, fileName)
          }
        } else {
          runWriteAction {
            tempDir.createChildData(this, independentPath)
          }
        }

      runWriteAction {
        file.setBinaryContent(content.toByteArray())
        vFiles[path] = file
      }
    }
    return LocalFiles(tempDir, vFiles)
  }
}

data class LocalFiles(
  val rootDir: VirtualFile,
  val files: Map<String, VirtualFile>,
)
