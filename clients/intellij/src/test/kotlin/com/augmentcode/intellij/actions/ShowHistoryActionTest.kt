package com.augmentcode.intellij.actions

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@RunWith(JUnit4::class)
class ShowHistoryActionTest : AugmentBasePlatformTestCase() {
  private lateinit var mockAugmentAppStateService: AugmentAppStateService

  override fun setUp() {
    super.setUp()

    // Mock AugmentAppStateService
    mockAugmentAppStateService = mockk(relaxed = true)
    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )
  }

  @Test
  fun testEnabledFlag() {
    val getModelsResponse =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijCompletionsHistoryMinVersion("0.0.0")
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(getModelsResponse),
        model = AugmentModel.fromGetModelsResponse(getModelsResponse),
      )
    every { mockAugmentAppStateService.context } returns mockContext

    val action = ShowHistoryAction()
    assertTrue(action.isEnabled)
  }

  @Test
  fun testDisabledFlag() {
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijAgentModeMinVersion("")
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )
    every { mockAugmentAppStateService.context } returns mockContext

    val action = ShowHistoryAction()
    assertFalse(action.isEnabled)
  }
}
