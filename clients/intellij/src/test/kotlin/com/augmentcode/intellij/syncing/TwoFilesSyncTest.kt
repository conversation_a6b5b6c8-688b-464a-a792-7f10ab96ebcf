package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout

class TwoFilesSyncTest : SyncTestCase() {
  fun testTwoFiles() =
    runBlocking {
      val state = MockEngineState()
      val mockEngine = createMockEngine(state)
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val tempDir = getVirtualFile(createTempDirectory())
      PsiTestUtil.addContentRoot(module, tempDir)

      // Add first file
      val file1 =
        runWriteAction {
          tempDir.createChildData(this, "foo1.txt").also {
            VfsUtil.saveText(it, "foo1")
          }
        }
      assertNotNull(AugmentBlobStateReader.read(file1.toPsiFile(project)))

      // Wait for first upload and checkpoint
      waitForRequests(mockEngine, "/batch-upload", 1)
      waitForRequests(mockEngine, "/checkpoint-blobs", 1)

      // Add second file
      val file2 =
        runWriteAction {
          tempDir.createChildData(this, "foo2.txt").also {
            VfsUtil.saveText(it, "foo2")
          }
        }
      assertNotNull(AugmentBlobStateReader.read(file2.toPsiFile(project)))

      // Wait for second upload and checkpoint
      waitForRequests(mockEngine, "/batch-upload", 2)

      // Verify the synchronizedBlobsPayload contains expected data
      val syncManager = AugmentRemoteSyncingManager.getInstance(project)
      try {
        withTimeout(10000) {
          while (true) {
            val payload = syncManager.synchronizedBlobsPayload()

            val checkpointMatches = payload.checkpointId == "checkpoint-1"
            val addedBlobsMatch = payload.addedBlobs == setOf(state.uploadedBlobs.last())
            val deletedBlobsMatch = payload.deletedBlobs.isEmpty()

            if (checkpointMatches && addedBlobsMatch && deletedBlobsMatch) {
              break
            }
            delay(250) // Poll interval
          }
        }
      } catch (_: TimeoutCancellationException) {
        // Only run this when timeout occurs
        val payload = syncManager.synchronizedBlobsPayload()
        val errors = mutableListOf<String>()

        if (payload.checkpointId != "checkpoint-1") {
          errors.add("Checkpoint ID mismatch: expected 'checkpoint-1' but got '${payload.checkpointId}'")
        }
        if (payload.addedBlobs != setOf(state.uploadedBlobs.last())) {
          errors.add("Added blobs mismatch: expected ${setOf(state.uploadedBlobs.last())} but got ${payload.addedBlobs}")
        }
        if (!payload.deletedBlobs.isEmpty()) {
          errors.add("Deleted blobs mismatch: expected empty set but got ${payload.deletedBlobs}")
        }

        throw AssertionError("Timeout waiting for sync state to match. Mismatches:\n${errors.joinToString("\n")}")
      }
    }
}
