package com.augmentcode.intellij.sidecarrpc

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.google.protobuf.Empty
import com.google.protobuf.Timestamp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withTimeout
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.io.ByteArrayOutputStream
import java.io.PipedInputStream
import java.io.PipedOutputStream

@RunWith(JUnit4::class)
class IOConnectionTest : AugmentBasePlatformTestCase() {
  @Test
  fun testSendingRequest() =
    runBlocking {
      val inputStream = PipedOutputStream() // so we can write into it
      val outputStream = ByteArrayOutputStream() // so we capture all the output

      val connection = IOConnection(PipedInputStream(inputStream), outputStream, augmentHelpers().createCoroutineScope(Dispatchers.IO))

      val timestamp =
        Timestamp.newBuilder()
          .setSeconds(1)
          .setNanos(2)
          .build()
      val response = connection.sendRequest("test.method", timestamp, Empty::class.java)
      val actualRequestString = outputStream.toString(Charsets.UTF_8)

      @Suppress("ktlint:standard:max-line-length")
      val expected = """{"jsonrpc":"2.0","id":1,"method":"test.method","params":{"@type":"type.googleapis.com/google.protobuf.Timestamp","value":"1970-01-01T00:00:01.000000002Z"}}"""
      assertEquals("Content-Length: ${expected.toByteArray().size}\r\n\r\n" + expected, actualRequestString.trim())

      val responseObject = """{"jsonrpc":"2.0","id":1,"result":{}}"""
      inputStream.write("Content-Length: ${responseObject.toByteArray().size}\r\n\r\n".toByteArray())
      inputStream.write(responseObject.toByteArray())

      withTimeout(10_000) {
        assertEquals("type.googleapis.com/google.protobuf.Empty", response.await().typeUrl)
      }
    }

  @Test
  fun testReceivingRequest() =
    runBlocking {
      val inputStream = PipedOutputStream() // so we can write into it
      val outputStream = ByteArrayOutputStream() // so we capture all the output

      val responseWait = Mutex(true)

      val connection = IOConnection(PipedInputStream(inputStream), outputStream, augmentHelpers().createCoroutineScope(Dispatchers.IO))
      connection.onRequest("test.method", Timestamp::class.java) { params ->
        val location = params.unpack(Timestamp::class.java)
        assertEquals(1, location.seconds)
        assertEquals(2, location.nanos)
        responseWait.unlock()
        return@onRequest Empty.getDefaultInstance()
      }

      @Suppress("ktlint:standard:max-line-length")
      val incomingRequest = """{"jsonrpc":"2.0","id":1,"method":"test.method","params":{"value":"1970-01-01T00:00:01.000000002Z"}}"""
      inputStream.write("Content-Length: ${incomingRequest.toByteArray().size}\r\n\r\n".toByteArray())
      inputStream.write(incomingRequest.toByteArray())

      withTimeout(10_000) {
        responseWait.lock()
      }
    }

  @Test
  fun testInteractiveShellStart() =
    runBlocking {
      /**
       * The purpose of this test is to ensure if a users shell logs anything on startup, we don't get confused by it.
       */
      val inputStream = PipedOutputStream() // so we can write into it
      val outputStream = ByteArrayOutputStream() // so we capture all the output

      val responseWait = Mutex(true)

      val connection = IOConnection(PipedInputStream(inputStream), outputStream, augmentHelpers().createCoroutineScope(Dispatchers.IO))
      connection.onRequest("test.method", Timestamp::class.java) { params ->
        val location = params.unpack(Timestamp::class.java)
        assertEquals(1, location.seconds)
        assertEquals(2, location.nanos)
        responseWait.unlock()
        return@onRequest Empty.getDefaultInstance()
      }

      @Suppress("ktlint:standard:max-line-length")
      val shellInitialText =
        """
        Last login: Fri Jun 20 09:03:22 on console
        nvm is not compatible with the "NPM_CONFIG_PREFIX" environment variable: currently set to "/Users/<USER>/.npm-packages"
        Run `unset NPM_CONFIG_PREFIX` to unset it.
        """.trimIndent()
      inputStream.write(shellInitialText.toByteArray())

      val incomingRequest = """{"jsonrpc":"2.0","id":1,"method":"test.method","params":{"value":"1970-01-01T00:00:01.000000002Z"}}"""
      inputStream.write("Content-Length: ${incomingRequest.toByteArray().size}\r\n\r\n".toByteArray())
      inputStream.write(incomingRequest.toByteArray())

      withTimeout(10_000) {
        responseWait.lock()
      }
    }
}
