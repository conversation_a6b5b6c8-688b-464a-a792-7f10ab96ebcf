package com.augmentcode.intellij.idea

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import io.mockk.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Files
import java.nio.file.Paths

@RunWith(JUnit4::class)
class AugmentFileStoreTest : AugmentBasePlatformTestCase() {
  private val testUserHome = Files.createTempDirectory("user-home").toAbsolutePath().toString()
  private val mockEnv = mutableMapOf<String, String>()

  private fun setUpMocks() {
    // Mock System.getenv() to return our controlled environment
    mockkObject(SystemEnvironment)
    every { SystemEnvironment.getenv() } returns mockEnv
    every { SystemEnvironment.getProperty("user.home") } returns testUserHome
  }

  override fun setUp() {
    super.setUp()
    setUpMocks()
    mockEnv.clear()
  }

  override fun tearDown() {
    super.tearDown()
    mockEnv.clear()
  }

  @Test
  fun testGetGlobalSubDirectoryWithAugmentStateHome() {
    val tempDir = Files.createTempDirectory("augment-home")
    mockEnv["AUGMENT_STATE_HOME"] = tempDir.toString()

    val result = AugmentFileStore.getGlobalSubDirectory("test-subdir")

    assertEquals(tempDir.resolve("intellij/global/test-subdir"), result)
  }

  @Test
  fun testGetGlobalSubDirectoryWithDefaultPath() {
    val result = AugmentFileStore.getGlobalSubDirectory("test-subdir")

    assertEquals(Paths.get(testUserHome, ".augmentcode/intellij/global/test-subdir"), result)
  }

  @Test
  fun testGetProjectSubDirectoryWithAugmentStateHome() {
    val tempDir = Files.createTempDirectory("augment-home")
    mockEnv["AUGMENT_STATE_HOME"] = tempDir.toString()

    val result = AugmentFileStore.getSubDirectoryForProject(project, "test-subdir")

    assertEquals(tempDir.resolve("intellij/projects/${project.locationHash}/test-subdir"), result)
  }

  @Test
  fun testGetProjectSubDirectoryWithDefaultPath() {
    val result = AugmentFileStore.getSubDirectoryForProject(project, "test-subdir")

    assertEquals(Paths.get(testUserHome, ".augmentcode/intellij/projects/${project.locationHash}/test-subdir"), result)
  }

  @Test
  fun testMigrateFilesWhenDesiredPathExists() {
    // Create the desired path where we want to store files
    // This should cause the migration to not run
    val desiredPath = Paths.get(testUserHome, ".augmentcode/intellij")
    Files.createDirectories(desiredPath)

    // Create an old path and add a file
    val oldPath = Paths.get(testUserHome, ".local/state/augment/intellij")
    Files.createDirectories(oldPath)
    Files.createFile(oldPath.resolve("test-file"))

    // Trigger the migration
    AugmentFileStore.migrateFiles()

    // Ensure the migration didn't happen by ensuring the file wasn't moved
    val shouldNotExist = Paths.get(testUserHome, ".augmentcode/intellij/test-file")
    assertFalse(Files.exists(shouldNotExist))
  }

  @Test
  fun testMigrateFilesToMoveFiles() {
    // Create an old path and add a file
    val oldPath = Paths.get(testUserHome, ".local/state/augment/intellij")
    Files.createDirectories(oldPath)
    Files.createFile(oldPath.resolve("test-file"))

    // Create a project directory and add a file (tests nested files)
    val projDir = oldPath.resolve("projects/${project.locationHash}")
    Files.createDirectories(projDir)
    Files.createFile(projDir.resolve("test-file"))

    // Trigger the migration
    AugmentFileStore.migrateFiles()

    // Ensure the migration worked by ensuring the file was moved
    val shouldExist = Paths.get(testUserHome, ".augmentcode/intellij/test-file")
    assertTrue(Files.exists(shouldExist))
    val nestedShouldExist = Paths.get(testUserHome, ".augmentcode/intellij/projects/${project.locationHash}/test-file")
    assertTrue(Files.exists(nestedShouldExist))

    // Ensure the old file is no longer there
    val shouldNotExist = Paths.get(testUserHome, ".local/state/augment/intellij/test-file")
    assertFalse(Files.exists(shouldNotExist))
    val nestedShouldNotExist = Paths.get(testUserHome, ".local/state/augment/intellij/projects/${project.locationHash}/test-file")
    assertFalse(Files.exists(nestedShouldNotExist))
  }
}
