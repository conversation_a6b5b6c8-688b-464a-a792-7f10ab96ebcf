package com.augmentcode.intellij.testutils

import kotlinx.coroutines.delay

/**
 * Utility function that repeatedly tries to execute a predicate until it succeeds or times out.
 * This is useful for testing asynchronous operations where you need to wait for a condition to become true.
 *
 * @param predicate The assertion function to execute
 * @param timeoutMs Maximum time to wait in milliseconds (default: 1000ms)
 * @param pollIntervalMs Time between retry attempts in milliseconds (default: 100ms)
 */
fun waitForAssertion(
  predicate: () -> Unit,
  timeoutMs: Long = 1000,
  pollIntervalMs: Long = 100,
) {
  val startTime = System.currentTimeMillis()
  while (System.currentTimeMillis() - startTime < timeoutMs) {
    try {
      predicate()
      return
    } catch (e: AssertionError) {
      // ignore and retry
    }
    Thread.sleep(pollIntervalMs)
  }
  // One final attempt, let any assertion errors propagate
  predicate()
}

/**
 * Utility function that repeatedly tries to execute a predicate until it succeeds or times out.
 * This is useful for testing asynchronous operations where you need to wait for a condition to become true.
 *
 * This version uses coroutines and is useful for testing suspend functions.
 *
 * @param predicate The assertion function to execute
 * @param timeoutMs Maximum time to wait in milliseconds (default: 1000ms)
 * @param pollIntervalMs Time between retry attempts in milliseconds (default: 100ms)
 */
suspend fun waitForAssertionAsync(
  predicate: () -> Unit,
  timeoutMs: Long = 1000,
  pollIntervalMs: Long = 100,
) {
  val startTime = System.currentTimeMillis()
  while (System.currentTimeMillis() - startTime < timeoutMs) {
    try {
      predicate()
      return
    } catch (e: AssertionError) {
      // ignore and retry
    }
    delay(pollIntervalMs)
  }
  // One final attempt, let any assertion errors propagate
  predicate()
}

suspend fun waitForAssertionSuspend(
  predicate: suspend () -> Unit,
  timeoutMs: Long = 1000,
  pollIntervalMs: Long = 100,
) {
  val startTime = System.currentTimeMillis()
  while (System.currentTimeMillis() - startTime < timeoutMs) {
    try {
      predicate()
      return
    } catch (e: AssertionError) {
      // ignore and retry
    }
    delay(pollIntervalMs)
  }
  // One final attempt, let any assertion errors propagate
  predicate()
}
