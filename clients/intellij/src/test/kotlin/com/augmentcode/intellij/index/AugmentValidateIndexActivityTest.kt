package com.augmentcode.intellij.index

import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.workspacemanagement.indexing.WORKSPACE_INDEX_ID
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.testFramework.IndexingTestUtil
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import com.intellij.util.indexing.FileBasedIndex
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi

@TestDataPath("\$CONTENT_ROOT/src/test/testData")
@RunWith(JUnit4::class)
class AugmentValidateIndexActivityTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/indexing"

  @Test
  fun testSimple() {
    augmentHelpers().forcePluginState(PluginState.ENABLED)

    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/find-missing" -> {
            // Return only the blobs that are NOT in our known set
            // Since we know "hash(permanent.txt).v1", return the others as missing
            respond(
              content = """{"missing_blob_names": ["hash(expired.txt).v1", "hash(editable.txt).v1"]}""",
              status = HttpStatusCode.OK,
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
          "/report-error" -> HttpUtil.respondOK(this)
          "/get-models" -> HttpUtil.respondGetModels(this)

          else -> error("Unexpected request to ${request.url.encodedPath}")
        }
      }
    augmentHelpers().registerMockEngine(mockEngine)

    val expired = myFixture.addFileToProject("expired.txt", "file that will be evicted")
    val permanent = myFixture.addFileToProject("permanent.txt", "file that will not be evicted")
    val editable = myFixture.configureByText("editable.txt", "file that we will edit\n")

    assertEquals("expired.txt", AugmentBlobStateReader.read(expired)?.relativePath)
    assertEquals("permanent.txt", AugmentBlobStateReader.read(permanent)?.relativePath)
    assertEquals("editable.txt", AugmentBlobStateReader.read(editable)?.relativePath)

    myFixture.type("a new line\n")
    assertEquals("editable.txt", AugmentBlobStateReader.read(editable)?.relativePath)

    // to emulate a freshly opened project with all
    FileDocumentManager.getInstance().saveAllDocuments()

    runWriteAction { // to force sync execution for tests
      runBlocking {
        AugmentValidateIndexActivity(augmentHelpers().createCoroutineScope(Dispatchers.Default)).validateRemotelyMissingBlobs(project)
      }
    }
    IndexingTestUtil.waitUntilIndexesAreReady(project)

    assertEquals("expired.txt", AugmentBlobStateReader.read(expired)?.relativePath)
    assertEquals("permanent.txt", AugmentBlobStateReader.read(permanent)?.relativePath)
    assertEquals("editable.txt", AugmentBlobStateReader.read(editable)?.relativePath)
  }

  @Test
  fun testReindexOnSignIn() {
    val pluginStateService = mockAugmentAppStateService(true)
    var listener: PluginStateListener? = null
    every { pluginStateService.subscribe(any(), any(), any()) } answers {
      listener = secondArg<PluginStateListener>()
    }

    val fileIndexService = mockFileBasedIndexService()
    every { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) } answers { /* no-op */ }

    val service = AugmentValidateIndexActivity(augmentHelpers().createCoroutineScope(Dispatchers.Default))
    runBlocking {
      service.setup(project)
    }
    assertNotNull(listener)

    verify(exactly = 0) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
    listener!!.onStateChange(PluginContext(true, DefaultFeatureFlags, null), PluginState.ENABLED)
    verify(exactly = 1) { fileIndexService.requestRebuild(WORKSPACE_INDEX_ID) }
  }

  fun mockAugmentAppStateService(enableV3Indexing: Boolean): AugmentAppStateService {
    val modelConfig =
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(enableV3Indexing)
          .build(),
      )
    val mockContext =
      PluginContext(
        true,
        flags = FeatureFlags.fromGetModelsResponse(modelConfig),
        model = AugmentModel.fromGetModelsResponse(modelConfig),
      )

    val mockAugmentAppStateService = mockk<AugmentAppStateService>(relaxed = true)
    every { mockAugmentAppStateService.context } returns mockContext

    application.registerOrReplaceServiceInstance(
      AugmentAppStateService::class.java,
      mockAugmentAppStateService,
      testRootDisposable,
    )

    return mockAugmentAppStateService
  }

  fun mockFileBasedIndexService(): FileBasedIndex {
    mockkStatic(FileBasedIndex::class)
    val mockIndex = mockk<FileBasedIndex>()
    every { FileBasedIndex.getInstance() } returns mockIndex
    return mockIndex
  }
}
