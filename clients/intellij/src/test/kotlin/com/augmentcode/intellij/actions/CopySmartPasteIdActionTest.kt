package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ide.CopyPasteManager
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.TestActionEvent
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.awt.datatransfer.DataFlavor

@RunWith(JUnit4::class)
class CopySmartPasteIdActionTest : AugmentBasePlatformTestCase() {
  private lateinit var action: CopySmartPasteIdAction
  private lateinit var event: AnActionEvent

  override fun setUp() {
    super.setUp()
    action = CopySmartPasteIdAction("test-request-id")
    event = TestActionEvent.createTestEvent()
    // Initialize the presentation by calling update
    action.update(event)
  }

  @Test
  fun testInitialState() {
    assertTrue(event.presentation.isEnabled)
    assertEquals(
      AugmentBundle.message("actions.copy.requestId"),
      action.templatePresentation.text,
    )
  }

  @Test
  fun testCopyToClipboard() {
    action.actionPerformed(event)

    val clipboardContent =
      CopyPasteManager
        .getInstance()
        .getContents<String>(DataFlavor.stringFlavor)
    assertEquals("test-request-id", clipboardContent)
  }

  @Test
  fun testTemporaryTextChange() {
    action.actionPerformed(event)

    // Text should immediately change to "Copied!"
    assertEquals(
      AugmentBundle.message("actions.copy.requestId.copied"),
      event.presentation.text,
    )

    runBlocking {
      PlatformTestUtil.dispatchAllEventsInIdeEventQueue()
      delay(100L)
      PlatformTestUtil.dispatchAllEventsInIdeEventQueue()
    }

    // Text should be back to original
    assertEquals(
      AugmentBundle.message("actions.copy.requestId"),
      event.presentation.text,
    )
  }
}
