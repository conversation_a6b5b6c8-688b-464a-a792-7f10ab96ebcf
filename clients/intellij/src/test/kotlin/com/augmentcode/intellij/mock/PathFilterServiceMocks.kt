package com.augmentcode.intellij.mock

import com.augmentcode.intellij.index.ignore.PathFilterService
import com.intellij.openapi.Disposable
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.coEvery
import io.mockk.mockk

object PathFilterServiceMocks {
  fun mockAlwaysAccept(
    project: Project,
    disposable: Disposable,
  ) {
    val mockPathFilterService = mockk<PathFilterService>(relaxed = true)
    coEvery { mockPathFilterService.isAccepted(any<String>(), any<String>()) } returns true
    coEvery { mockPathFilterService.isAccepted(any<VirtualFile>(), any<VirtualFile>()) } returns true
    project.registerOrReplaceServiceInstance(
      PathFilterService::class.java,
      mockPathFilterService,
      disposable,
    )
  }

  fun mockAlwaysIgnore(
    project: Project,
    disposable: Disposable,
  ) {
    val mockPathFilterService = mockk<PathFilterService>(relaxed = true)
    coEvery { mockPathFilterService.isAccepted(any<String>(), any<String>()) } returns false
    coEvery { mockPathFilterService.isAccepted(any<VirtualFile>(), any<VirtualFile>()) } returns false
    project.registerOrReplaceServiceInstance(
      PathFilterService::class.java,
      mockPathFilterService,
      disposable,
    )
  }
}
