package com.augmentcode.intellij.sentry

import com.intellij.testFramework.LightPlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.*
import io.sentry.Sentry
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class SentryGlobalExceptionHandlerTest : LightPlatformTestCase() {
  private lateinit var handler: SentryGlobalExceptionHandler
  private lateinit var mockSentryService: SentryService
  private lateinit var originalHandler: Thread.UncaughtExceptionHandler

  override fun setUp() {
    super.setUp()

    // Mock the SentryService
    mockSentryService = mockk(relaxed = true)

    // Mock static methods
    mockkStatic(Sentry::class)
    every { Sentry.configureScope(any()) } just Runs

    // Store the original handler to restore it later
    // Handle the case where there might be no default handler
    originalHandler = Thread.getDefaultUncaughtExceptionHandler() ?: Thread.UncaughtExceptionHandler { _, _ -> }

    // Register the mock SentryService as a service
    application.registerOrReplaceServiceInstance(
      SentryService::class.java,
      mockSentryService,
      testRootDisposable,
    )

    // Create the handler instance with mocked dependencies
    handler = SentryGlobalExceptionHandler()
  }

  override fun tearDown() {
    try {
      // Restore the original handler
      Thread.setDefaultUncaughtExceptionHandler(originalHandler)

      // Clear all mocks
      clearAllMocks()
      unmockkAll()
    } finally {
      super.tearDown()
    }
  }

  @Test
  fun testInstall() {
    // When
    handler.install()

    // Then
    assertEquals("Our Sentry exception handler should be set as default", handler, Thread.getDefaultUncaughtExceptionHandler())
  }

  @Test
  fun testUninstall() {
    // Given
    handler.install()
    assertEquals("Our Sentry exception handler should be set as default", handler, Thread.getDefaultUncaughtExceptionHandler())

    // When
    handler.uninstall()

    // Then
    assertEquals("Original handler should be restored", originalHandler, Thread.getDefaultUncaughtExceptionHandler())
  }

  // Tests for shouldReportException filtering logic
  @Test
  fun testShouldReportException_allAugmentExceptionsReported() {
    // Given - comprehensive list of exceptions with Augment code in stack trace
    // This includes various exception types and different Augment package locations
    val augmentExceptions =
      listOf(
        // RuntimeExceptions from different Augment packages
        RuntimeException("Exception from Augment service").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.augmentcode.intellij.service.ChatService", "sendMessage", "ChatService.kt", 10),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
            )
        },
        RuntimeException("Exception from Augment webview").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.augmentcode.intellij.webview.WebviewManager", "handleMessage", "WebviewManager.kt", 15),
              StackTraceElement("com.intellij.openapi.application.ApplicationImpl", "runReadAction", "ApplicationImpl.java", 25),
            )
        },
        RuntimeException("Exception from Augment sentry").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.augmentcode.intellij.sentry.SentryService", "captureException", "SentryService.kt", 20),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 30),
            )
        },
        RuntimeException("Connection reset").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.augmentcode.intellij.service.ChatService", "sendMessage", "ChatService.kt", 10),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
            )
        },
        // Other exception types from Augment code (all should be reported)
        InterruptedException("Interrupted").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.augmentcode.intellij.service.ChatService", "sendMessage", "ChatService.kt", 10),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
            )
        },
        java.util.concurrent.CancellationException("Cancelled").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.augmentcode.intellij.service.ChatService", "sendMessage", "ChatService.kt", 10),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
            )
        },
      )

    // When & Then - all should be reported since they're from Augment code
    augmentExceptions.forEach { exception ->
      assertTrue(
        "Should report all exceptions from Augment code: ${exception.javaClass.simpleName} - ${exception.message}",
        handler.shouldReportException(exception),
      )
    }
  }

  @Test
  fun testShouldReportException_nonAugmentCodeExceptions() {
    // Given - exceptions without Augment code in stack trace
    val nonAugmentExceptions =
      listOf(
        RuntimeException("Exception from other plugin").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.example.plugin.SomeClass", "someMethod", "SomeClass.java", 10),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
            )
        },
        RuntimeException("Exception from IntelliJ platform").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("com.intellij.openapi.application.ApplicationImpl", "runReadAction", "ApplicationImpl.java", 10),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
            )
        },
        RuntimeException("Exception from JDK").apply {
          stackTrace =
            arrayOf(
              StackTraceElement("java.util.concurrent.ThreadPoolExecutor", "runWorker", "ThreadPoolExecutor.java", 10),
              StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
            )
        },
      )

    // When & Then
    nonAugmentExceptions.forEach { exception ->
      assertFalse("Should not report exception without Augment code: ${exception.message}", handler.shouldReportException(exception))
    }
  }

  @Test
  fun testShouldReportException_mixedStackTrace() {
    // Given - exception with both Augment and non-Augment code
    val testException = RuntimeException("Exception with mixed stack trace")
    testException.stackTrace =
      arrayOf(
        StackTraceElement("com.example.plugin.SomeClass", "someMethod", "SomeClass.java", 10),
        StackTraceElement("com.augmentcode.intellij.service.ChatService", "sendMessage", "ChatService.kt", 15),
        StackTraceElement("com.intellij.openapi.application.ApplicationImpl", "runReadAction", "ApplicationImpl.java", 20),
        StackTraceElement("java.lang.Thread", "run", "Thread.java", 25),
      )

    // When & Then - should report because it contains Augment code
    assertTrue("Should report exception with mixed stack trace containing Augment code", handler.shouldReportException(testException))
  }

  @Test
  fun testShouldReportException_emptyStackTrace() {
    // Given - exception with empty stack trace
    val testException = RuntimeException("Exception with empty stack trace")
    testException.stackTrace = emptyArray()

    // When & Then - should not report because no Augment code in stack trace
    assertFalse("Should not report exception with empty stack trace", handler.shouldReportException(testException))
  }

  @Test
  fun testShouldReportException_nullMessage() {
    // Given - exception with null message but Augment code
    val testException = RuntimeException(null as String?)
    testException.stackTrace =
      arrayOf(
        StackTraceElement("com.augmentcode.intellij.service.ChatService", "sendMessage", "ChatService.kt", 10),
        StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
      )

    // When & Then - should report because it has Augment code
    assertTrue("Should report exception with null message if it has Augment code", handler.shouldReportException(testException))
  }

  // Tests for helper methods
  @Test
  fun testIsFromAugmentCode() {
    // Given - exception with Augment code
    val augmentException = RuntimeException("Augment exception")
    augmentException.stackTrace =
      arrayOf(
        StackTraceElement("com.augmentcode.intellij.service.ChatService", "sendMessage", "ChatService.kt", 10),
        StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
      )

    // Given - exception without Augment code
    val nonAugmentException = RuntimeException("Non-Augment exception")
    nonAugmentException.stackTrace =
      arrayOf(
        StackTraceElement("com.example.plugin.SomeClass", "someMethod", "SomeClass.java", 10),
        StackTraceElement("java.lang.Thread", "run", "Thread.java", 20),
      )

    // When & Then
    assertTrue("Should detect Augment code", handler.isFromAugmentCode(augmentException))
    assertFalse("Should not detect Augment code", handler.isFromAugmentCode(nonAugmentException))
  }
}
