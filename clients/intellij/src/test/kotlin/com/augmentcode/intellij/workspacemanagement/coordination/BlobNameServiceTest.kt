package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.intellij.psi.codeStyle.NameUtil
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Paths
import kotlin.collections.component1

@RunWith(JUnit4::class)
class BlobNameServiceTest : AugmentHeavyPlatformTestCase() {
  @Test
  fun testFindFiles_simpleExample() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((_, file) in localFiles.files) {
        blobNameService.put("blob-${file.path}", file.path)
      }

      val matcher =
        NameUtil.buildMatcher("*example")
          .withCaseSensitivity(NameUtil.MatchingCaseSensitivity.NONE)
          .build()

      val foundFiles = blobNameService.findFiles(matcher).take(localFiles.files.size).toList()
      assertEquals(listOf("example.txt", "example2.txt", "nested/example.txt", "nested/example2.txt"), foundFiles.map { it.relPath })
    }

  @Test
  fun testFindFiles_moreSpecificQuery() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((_, file) in localFiles.files) {
        blobNameService.put("blob-${file.path}", file.path)
      }

      val matcher =
        NameUtil.buildMatcher("*example2")
          .withCaseSensitivity(NameUtil.MatchingCaseSensitivity.NONE)
          .build()

      val foundFiles = blobNameService.findFiles(matcher).take(localFiles.files.size).toList()
      assertEquals(listOf("example2.txt", "nested/example2.txt"), foundFiles.map { it.relPath })
    }

  @Test
  fun testFindFiles_fileWithSubdirectory() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((_, file) in localFiles.files) {
        blobNameService.put("blob-${file.path}", file.path)
      }

      val matcher =
        NameUtil.buildMatcher("*nested/example")
          .withCaseSensitivity(NameUtil.MatchingCaseSensitivity.NONE)
          .build()

      val foundFiles = blobNameService.findFiles(matcher).take(localFiles.files.size).toList()
      assertEquals(listOf("nested/example.txt", "nested/example2.txt"), foundFiles.map { it.relPath })
    }

  @Test
  fun testFindFile() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((_, file) in localFiles.files) {
        blobNameService.put("blob-${file.path}", file.path)
      }

      val testCases =
        mapOf(
          "example.txt" to listOf(localFiles.files["example.txt"]!!.path, localFiles.files["nested/example.txt"]!!.path),
          "nested/example.txt" to listOf(localFiles.files["nested/example.txt"]!!.path),
          "unknown.txt" to listOf(),
        )

      for ((query, expected) in testCases) {
        val foundFiles = blobNameService.findFile(query)
        assertEquals("Test case failed for $query", expected, foundFiles)
      }
    }

  @Test
  fun testBlobsForPaths() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            // Windows paths
            "windows\\example.txt" to "test",
            "windows\\example2.txt" to "test",
            "windows\\nested\\example.txt" to "test",
            "windows\\nested\\example2.txt" to "test",
            // Extra path that shouldn't match
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((name, file) in localFiles.files) {
        blobNameService.put("blob_$name", file.path)
      }

      val testCases =
        mapOf(
          setOf("example.txt") to listOf("blob_example.txt"),
          setOf("nested/example.txt") to listOf("blob_nested/example.txt"),
          setOf("unknown.txt") to listOf(),
          setOf("nested/example.txt", "example2.txt", "unknown.txt") to listOf("blob_nested/example.txt", "blob_example2.txt"),
          setOf("windows/example.txt") to listOf("blob_windows\\example.txt"),
          setOf("windows\\example.txt") to listOf("blob_windows\\example.txt"),
          setOf(
            "windows/nested/example.txt",
            "windows\\nested\\example2.txt",
          ) to listOf("blob_windows\\nested\\example.txt", "blob_windows\\nested\\example2.txt"),
        )

      for ((query, expected) in testCases) {
        val foundBlobs = blobNameService.blobsForPaths(query)
        assertEquals("Test case failed for $query", expected, foundBlobs)
      }
    }

  @Test
  fun testFindDirectories() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            "foo/bar/baz.txt" to "test",
            // Windows paths
            "windows\\example.txt" to "test",
            "windows\\example2.txt" to "test",
            "windows\\nested\\example.txt" to "test",
            "windows\\nested\\example2.txt" to "test",
            // Extra path that shouldn't match
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((_, file) in localFiles.files) {
        blobNameService.put("blob-${file.path}", file.path)
      }

      val testCases =
        mapOf(
          "nested" to listOf("nested", "nested"),
          "foo" to listOf(),
          "foo/bar" to listOf("foo/bar"),
          "bar" to listOf(),
          "unknown" to listOf(),
          "windows" to listOf("windows", "windows"),
          "windows/nested" to listOf("windows/nested", "windows/nested"),
          "windows\\nested" to listOf("windows/nested", "windows/nested"),
        )

      for ((query, expected) in testCases) {
        val foundDirectories = blobNameService.findDirectories(query).take(localFiles.files.size).toList()
        assertEquals("Test case failed for $query", expected, foundDirectories)
      }
    }

  @Test
  fun testMemoryIntensiveGetQualifiedPathNames() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            // Windows paths
            "windows\\example.txt" to "test",
            "windows\\example2.txt" to "test",
            "windows\\nested\\example.txt" to "test",
            "windows\\nested\\example2.txt" to "test",
            // Extra path
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((_, file) in localFiles.files) {
        blobNameService.put("blob-${file.path}", file.path)
      }

      val qualifiedPaths = blobNameService.memoryIntensiveGetQualifiedPathNames()
      assertEquals(
        listOf(
          "example.txt",
          "example2.txt",
          "nested/example.txt",
          "nested/example2.txt",
          "windows/example.txt",
          "windows/example2.txt",
          "windows/nested/example.txt",
          "windows/nested/example2.txt",
          "other.txt",
        ),
        qualifiedPaths.map { it.relPath },
      )
    }

  @Test
  fun testFileByBlobName() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            // Windows paths
            "windows\\example.txt" to "test",
            "windows\\example2.txt" to "test",
            "windows\\nested\\example.txt" to "test",
            "windows\\nested\\example2.txt" to "test",
            // Extra path
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((name, file) in localFiles.files) {
        blobNameService.put("blob_$name", file.path)
      }

      val testCases =
        mapOf(
          "blob_example.txt" to "example.txt",
          "blob_nested/example.txt" to "nested/example.txt",
          "blob_unknown.txt" to null,
          "blob_windows\\example.txt" to "windows/example.txt",
          "blob_windows\\nested\\example.txt" to "windows/nested/example.txt",
        )

      for ((query, expected) in testCases) {
        val foundQPath = blobNameService.fileByBlobName(query)
        if (expected == null) {
          assertNull(foundQPath)
        } else {
          assertNotNull("Test case failed for $query", foundQPath)
          assertEquals("Test case failed for $query", expected, foundQPath!!.relPath)
        }
      }
    }

  @Test
  fun testDeleteByPath() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            // Windows paths
            "windows\\example.txt" to "test",
            "windows\\example2.txt" to "test",
            "windows\\nested\\example.txt" to "test",
            "windows\\nested\\example2.txt" to "test",
            // Extra path
            "other.txt" to "test",
          ),
        )
      val blobNameService = BlobNameService(project)
      for ((name, file) in localFiles.files) {
        blobNameService.put("blob-$name", file.path)
      }
      assertEquals(9, blobNameService.size())

      var deletedBlobName = blobNameService.deleteByPath(localFiles.files["example.txt"]!!.path)
      assertEquals("blob-example.txt", deletedBlobName)
      assertEquals(8, blobNameService.size())

      deletedBlobName = blobNameService.deleteByPath(localFiles.files["nested/example2.txt"]!!.path)
      assertEquals("blob-nested/example2.txt", deletedBlobName)
      assertEquals(7, blobNameService.size())

      // Ensure one path has the windows separator
      deletedBlobName = blobNameService.deleteByPath(localFiles.files["windows\\example.txt"]!!.path.replace("/", "\\"))
      assertEquals("blob-windows\\example.txt", deletedBlobName)
      assertEquals(6, blobNameService.size())

      // Ensure one path has the unix separator
      deletedBlobName = blobNameService.deleteByPath(localFiles.files["windows\\example2.txt"]!!.path.replace("\\", "/"))
      assertEquals("blob-windows\\example2.txt", deletedBlobName)
      assertEquals(5, blobNameService.size())

      // Ensure one nested path has the windows separator
      deletedBlobName = blobNameService.deleteByPath(localFiles.files["windows\\nested\\example.txt"]!!.path.replace("/", "\\"))
      assertEquals("blob-windows\\nested\\example.txt", deletedBlobName)
      assertEquals(4, blobNameService.size())

      // Ensure one nested path has the unix separator
      deletedBlobName = blobNameService.deleteByPath(localFiles.files["windows\\nested\\example2.txt"]!!.path.replace("\\", "/"))
      assertEquals("blob-windows\\nested\\example2.txt", deletedBlobName)
      assertEquals(3, blobNameService.size())
    }

  @Test
  fun testGetByPath() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            // Windows paths
            "windows\\example.txt" to "test",
            "windows\\example2.txt" to "test",
            "windows\\nested\\example.txt" to "test",
            "windows\\nested\\example2.txt" to "test",
            // Extra path
            "other.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((name, file) in localFiles.files) {
        blobNameService.put("blob_$name", file.path)
      }

      // Test basic functionality - finding existing blob names by path
      assertEquals("blob_example.txt", blobNameService.getByPath(localFiles.files["example.txt"]!!.path))
      assertEquals("blob_example.txt", blobNameService.getByPathNoBlocking(localFiles.files["example.txt"]!!.path))

      assertEquals("blob_nested/example.txt", blobNameService.getByPath(localFiles.files["nested/example.txt"]!!.path))
      assertEquals("blob_nested/example.txt", blobNameService.getByPathNoBlocking(localFiles.files["nested/example.txt"]!!.path))

      assertEquals("blob_other.txt", blobNameService.getByPath(localFiles.files["other.txt"]!!.path))
      assertEquals("blob_other.txt", blobNameService.getByPathNoBlocking(localFiles.files["other.txt"]!!.path))

      // Test Windows paths
      assertEquals("blob_windows\\example.txt", blobNameService.getByPath(localFiles.files["windows\\example.txt"]!!.path))
      assertEquals("blob_windows\\example.txt", blobNameService.getByPathNoBlocking(localFiles.files["windows\\example.txt"]!!.path))

      assertEquals("blob_windows\\nested\\example.txt", blobNameService.getByPath(localFiles.files["windows\\nested\\example.txt"]!!.path))
      assertEquals(
        "blob_windows\\nested\\example.txt",
        blobNameService.getByPathNoBlocking(localFiles.files["windows\\nested\\example.txt"]!!.path),
      )

      // Test path normalization - should work with both separators
      val windowsFile = localFiles.files["windows\\example.txt"]!!
      val unixStylePath = windowsFile.path.replace("\\", "/")
      val windowsStylePath = windowsFile.path.replace("/", "\\")
      assertEquals("blob_windows\\example.txt", blobNameService.getByPath(unixStylePath))
      assertEquals("blob_windows\\example.txt", blobNameService.getByPathNoBlocking(unixStylePath))

      assertEquals("blob_windows\\example.txt", blobNameService.getByPath(windowsStylePath))
      assertEquals("blob_windows\\example.txt", blobNameService.getByPathNoBlocking(windowsStylePath))

      // Test non-existent paths
      assertNull(blobNameService.getByPath("/non/existent/path.txt"))
      assertNull(blobNameService.getByPathNoBlocking("/non/existent/path.txt"))

      assertNull(blobNameService.getByPath("C:\\non\\existent\\path.txt"))
      assertNull(blobNameService.getByPathNoBlocking("C:\\non\\existent\\path.txt"))

      assertNull(blobNameService.getByPath(""))
      assertNull(blobNameService.getByPathNoBlocking(""))

      // Test partial path matches (should not match)
      assertNull(blobNameService.getByPath("example.txt")) // relative path, should not match absolute
      assertNull(blobNameService.getByPathNoBlocking("example.txt"))

      assertNull(blobNameService.getByPath("nested/example.txt")) // relative path, should not match absolute
      assertNull(blobNameService.getByPathNoBlocking("nested/example.txt"))
    }

  @Test
  fun testGetByPath_afterDeletion() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "nested/example.txt" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((name, file) in localFiles.files) {
        blobNameService.put("blob_$name", file.path)
      }

      val examplePath = localFiles.files["example.txt"]!!.path
      val nestedPath = localFiles.files["nested/example.txt"]!!.path

      // Verify files exist initially
      assertEquals("blob_example.txt", blobNameService.getByPath(examplePath))
      assertEquals("blob_nested/example.txt", blobNameService.getByPath(nestedPath))

      // Delete one file
      blobNameService.deleteByPath(examplePath)

      // Verify deleted file returns null, but other file still exists
      assertNull(blobNameService.getByPath(examplePath))

      assertEquals("blob_nested/example.txt", blobNameService.getByPath(nestedPath))
    }

  @Test
  fun testGetByPath_caseHandling() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "Example.TXT" to "test",
            "NESTED/Example.TXT" to "test",
          ),
        )

      val blobNameService = BlobNameService(project)
      for ((name, file) in localFiles.files) {
        blobNameService.put("blob_$name", file.path)
      }

      // Test exact case match
      assertEquals("blob_Example.TXT", blobNameService.getByPath(localFiles.files["Example.TXT"]!!.path))
      assertEquals("blob_NESTED/Example.TXT", blobNameService.getByPath(localFiles.files["NESTED/Example.TXT"]!!.path))

      // Test that case mismatches don't work (paths should be exact matches)
      val exactPath = localFiles.files["Example.TXT"]!!.path
      val lowerCasePath = exactPath.lowercase()
      val upperCasePath = exactPath.uppercase()

      if (exactPath != lowerCasePath) {
        assertNull(blobNameService.getByPath(lowerCasePath))
      }
      if (exactPath != upperCasePath) {
        assertNull(blobNameService.getByPath(upperCasePath))
      }
    }

  @Test
  fun testDeleteByDirectory() =
    kotlinx.coroutines.test.runTest {
      val localFiles =
        createLocalFiles(
          mutableMapOf(
            "example.txt" to "test",
            "example2.txt" to "test",
            "nested/example.txt" to "test",
            "nested/example2.txt" to "test",
            "nested/deep/file.txt" to "test",
            "other/file.txt" to "test",
            // Windows paths
            "windows\\example.txt" to "test",
            "windows\\example2.txt" to "test",
            "windows\\nested\\example.txt" to "test",
            "windows\\nested\\example2.txt" to "test",
            // Extra path
            "root.txt" to "test",
          ),
        )
      val blobNameService = BlobNameService(project)
      for ((name, file) in localFiles.files) {
        blobNameService.put("blob-$name", file.path)
      }
      assertEquals(11, blobNameService.size())

      // Test deleting nested directory - should delete all files in nested/ and nested/deep/
      val deletedFiles = mutableListOf<Pair<String, String>>()
      val nestedDirPath = Paths.get(localFiles.rootDir.path, "nested").toString()
      blobNameService.deleteByDirectory(nestedDirPath) { absPath, blobName ->
        deletedFiles.add(absPath to blobName)
      }

      // Should have deleted 3 files: nested/example.txt, nested/example2.txt, nested/deep/file.txt
      assertEquals(3, deletedFiles.size)
      assertEquals(8, blobNameService.size())

      // Verify the callback was called with correct parameters
      val expectedDeletedBlobs = setOf("blob-nested/example.txt", "blob-nested/example2.txt", "blob-nested/deep/file.txt")
      assertEquals(expectedDeletedBlobs, deletedFiles.map { it.second }.toSet())

      // Test deleting windows directory with unix separator
      deletedFiles.clear()
      val windowsNestedDirPath = Paths.get(localFiles.rootDir.path, "windows/nested").toString().replace("\\", "/")
      blobNameService.deleteByDirectory(windowsNestedDirPath) { absPath, blobName ->
        deletedFiles.add(absPath to blobName)
      }

      assertEquals(2, deletedFiles.size)
      assertEquals(6, blobNameService.size())

      val expectedWindowsNestedBlobs = setOf("blob-windows\\nested\\example.txt", "blob-windows\\nested\\example2.txt")
      assertEquals(expectedWindowsNestedBlobs, deletedFiles.map { it.second }.toSet())

      // Test deleting directory with windows separator
      deletedFiles.clear()
      val windowsDirPath = Paths.get(localFiles.rootDir.path, "windows").toString().replace("/", "\\")
      blobNameService.deleteByDirectory(windowsDirPath) { absPath, blobName ->
        deletedFiles.add(absPath to blobName)
      }

      assertEquals(2, deletedFiles.size)
      assertEquals(4, blobNameService.size())

      val expectedWindowsBlobs = setOf("blob-windows\\example.txt", "blob-windows\\example2.txt")
      assertEquals(expectedWindowsBlobs, deletedFiles.map { it.second }.toSet())

      // Test path with trailing slash - should not delete anything
      deletedFiles.clear()
      val otherDirPath = Paths.get(localFiles.rootDir.path, "other").toString() + "/"
      blobNameService.deleteByDirectory(otherDirPath)

      assertEquals(3, blobNameService.size())

      // Test deleting non-existent directory - should not delete anything
      deletedFiles.clear()
      val nonExistentDirPath = Paths.get(localFiles.rootDir.path, "nonexistent").toString()
      blobNameService.deleteByDirectory(nonExistentDirPath) { absPath, blobName ->
        deletedFiles.add(absPath to blobName)
      }

      assertEquals(0, deletedFiles.size)
      assertEquals(3, blobNameService.size())
    }
}
