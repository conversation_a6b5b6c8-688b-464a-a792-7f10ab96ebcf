package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class LaunchProcessToolTest : AugmentBasePlatformTestCase() {

  @Test
  fun testBuildCommandWithEnvVarsForBash() {
    val command = "git diff"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "bash")
    
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; git diff",
      result
    )
  }

  @Test
  fun testBuildCommandWithEnvVarsForZsh() {
    val command = "git log --oneline"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "zsh")
    
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; git log --oneline",
      result
    )
  }

  @Test
  fun testBuildCommandWithEnvVarsForFish() {
    val command = "man ls"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "fish")
    
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; man ls",
      result
    )
  }

  @Test
  fun testBuildCommandWithEnvVarsForPowerShell() {
    val command = "git status"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "powershell.exe")
    
    assertEquals(
      "\$env:PAGER='cat'; \$env:LESS='-FX'; \$env:GIT_PAGER='cat'; git status",
      result
    )
  }

  @Test
  fun testBuildCommandWithEnvVarsForPwsh() {
    val command = "git branch -a"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "pwsh.exe")
    
    assertEquals(
      "\$env:PAGER='cat'; \$env:LESS='-FX'; \$env:GIT_PAGER='cat'; git branch -a",
      result
    )
  }

  @Test
  fun testBuildCommandWithEnvVarsForPowerShellCore() {
    val command = "ls -la"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "pwsh")
    
    assertEquals(
      "\$env:PAGER='cat'; \$env:LESS='-FX'; \$env:GIT_PAGER='cat'; ls -la",
      result
    )
  }

  @Test
  fun testBuildCommandWithEnvVarsForWindowsCmd() {
    val command = "dir /w"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "cmd.exe")
    
    assertEquals(
      "set PAGER=cat && set LESS=-FX && set GIT_PAGER=cat && dir /w",
      result
    )
  }

  @Test
  fun testBuildCommandWithEnvVarsForCmd() {
    val command = "echo %PATH%"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "cmd")
    
    assertEquals(
      "set PAGER=cat && set LESS=-FX && set GIT_PAGER=cat && echo %PATH%",
      result
    )
  }

  @Test
  fun testBuildCommandWithEmptyEnvVars() {
    val command = "git diff"
    val envVars = emptyMap<String, String>()
    
    val resultBash = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "bash")
    val resultPowerShell = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "powershell.exe")
    val resultCmd = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "cmd.exe")
    
    assertEquals("git diff", resultBash)
    assertEquals("git diff", resultPowerShell)
    assertEquals("git diff", resultCmd)
  }

  @Test
  fun testBuildCommandWithSpecialCharactersInCommand() {
    val command = "echo 'Hello \"World\" & test'"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val resultBash = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "bash")
    val resultPowerShell = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "powershell.exe")
    val resultCmd = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "cmd.exe")
    
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; echo 'Hello \"World\" & test'",
      resultBash
    )
    assertEquals(
      "\$env:PAGER='cat'; \$env:LESS='-FX'; \$env:GIT_PAGER='cat'; echo 'Hello \"World\" & test'",
      resultPowerShell
    )
    assertEquals(
      "set PAGER=cat && set LESS=-FX && set GIT_PAGER=cat && echo 'Hello \"World\" & test'",
      resultCmd
    )
  }

  @Test
  fun testBuildCommandWithEmptyCommand() {
    val command = ""
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val resultBash = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "bash")
    val resultPowerShell = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "powershell.exe")
    val resultCmd = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "cmd.exe")
    
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; ",
      resultBash
    )
    assertEquals(
      "\$env:PAGER='cat'; \$env:LESS='-FX'; \$env:GIT_PAGER='cat'; ",
      resultPowerShell
    )
    assertEquals(
      "set PAGER=cat && set LESS=-FX && set GIT_PAGER=cat && ",
      resultCmd
    )
  }

  @Test
  fun testBuildCommandWithDifferentShellPaths() {
    val command = "test command"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    // Test with full paths
    val resultBashPath = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "/usr/bin/bash")
    val resultZshPath = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "/bin/zsh")
    val resultPowerShellPath = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe")
    val resultCmdPath = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "C:\\Windows\\System32\\cmd.exe")
    
    // All should be treated as Unix shells except PowerShell and CMD
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; test command",
      resultBashPath
    )
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; test command",
      resultZshPath
    )
    assertEquals(
      "\$env:PAGER='cat'; \$env:LESS='-FX'; \$env:GIT_PAGER='cat'; test command",
      resultPowerShellPath
    )
    assertEquals(
      "set PAGER=cat && set LESS=-FX && set GIT_PAGER=cat && test command",
      resultCmdPath
    )
  }

  @Test
  fun testEnvironmentVariableOrdering() {
    val command = "git diff"
    val envVars = linkedMapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    val resultBash = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "bash")
    
    // Verify the order is maintained
    val expectedOrder = "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; git diff"
    assertEquals(expectedOrder, resultBash)
    
    // Verify each variable appears in the correct position
    val pagerIndex = resultBash.indexOf("PAGER=cat")
    val lessIndex = resultBash.indexOf("LESS=-FX")
    val gitPagerIndex = resultBash.indexOf("GIT_PAGER=cat")
    
    assertTrue(pagerIndex < lessIndex)
    assertTrue(lessIndex < gitPagerIndex)
  }

  @Test
  fun testBuildCommandWithSingleEnvironmentVariable() {
    val command = "git log"
    val envVars = mapOf("PAGER" to "cat")
    
    val resultBash = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "bash")
    val resultPowerShell = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "powershell.exe")
    val resultCmd = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "cmd.exe")
    
    assertEquals("export PAGER=cat; git log", resultBash)
    assertEquals("\$env:PAGER='cat'; git log", resultPowerShell)
    assertEquals("set PAGER=cat && git log", resultCmd)
  }

  @Test
  fun testBuildCommandWithUnknownShell() {
    val command = "ls -la"
    val envVars = mapOf(
      "PAGER" to "cat",
      "LESS" to "-FX",
      "GIT_PAGER" to "cat"
    )
    
    // Unknown shells should default to Unix-style export
    val result = LaunchProcessTool.buildCommandWithEnvVars(command, envVars, "unknownshell")
    
    assertEquals(
      "export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; ls -la",
      result
    )
  }
}
