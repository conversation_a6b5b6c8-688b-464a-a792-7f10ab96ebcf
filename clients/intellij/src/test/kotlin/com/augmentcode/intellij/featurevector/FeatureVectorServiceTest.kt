package com.augmentcode.intellij.featurevector

import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertionSuspend
import com.intellij.openapi.util.Disposer
import io.ktor.client.engine.mock.*
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.advanceTimeBy
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FeatureVectorServiceTest : AugmentBasePlatformTestCase() {
  private lateinit var service: FeatureVectorService

  override fun setUp() {
    super.setUp()

    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/get-models" -> HttpUtil.respondGetModels(this)
          "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
          "/report-feature-vector" -> HttpUtil.respondOK(this)
          else -> HttpUtil.respondOK(this)
        }
      }
    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    augmentHelpers().forcePluginState(PluginState.ENABLED)
    service = FeatureVectorService.getInstance()
  }

  override fun tearDown() {
    if (::service.isInitialized) {
      service.stopCollection()
    }
    super.tearDown()
  }

  @Test
  fun testCollectionStateManagement() {
    // Test that we can control collection state (combines start/stop/idempotent tests)
    service.stopCollection()
    assertFalse("Collection should be stopped", service.isCollectionEnabled())

    // Test starting
    service.startCollection()
    assertTrue("Collection should be active after starting", service.isCollectionEnabled())

    // Test idempotent start
    service.startCollection()
    assertTrue("Collection should still be active after multiple starts", service.isCollectionEnabled())

    // Test stopping
    service.stopCollection()
    assertFalse("Collection should be stopped after stopping", service.isCollectionEnabled())

    // Test idempotent stop
    service.stopCollection()
    assertFalse("Collection should still be stopped after multiple stops", service.isCollectionEnabled())
  }

  @Test
  fun testManualCollection() =
    runBlocking {
      service.stopCollection()

      // Manual collection should work regardless of collection state
      service.collect()
      // If we get here without exception, collection completed successfully
    }

  @OptIn(ExperimentalCoroutinesApi::class)
  @Test
  fun testFailingCollectionsAndLoop() =
    kotlinx.coroutines.test.runTest {
      // Use a service with the test coroutine scope
      val vectorService = FeatureVectorService(this)

      try {
        mockkObject(FeatureVectorExecutor)
        var callCount = 0
        coEvery { FeatureVectorExecutor.execute() } answers {
          callCount++
          throw Exception("Injected error")
        }

        vectorService.startCollection()

        // Ensure one call
        waitForAssertionSuspend({
          coVerify(atLeast = 1) { FeatureVectorExecutor.execute() }
        })

        advanceTimeBy(FeatureVectorService.COLLECTION_INTERVAL_MS + 1)

        coVerify(exactly = 2) { FeatureVectorExecutor.execute() }
      } finally {
        vectorService.stopCollection()
      }
    }

  @Test
  fun testServiceDisposal() {
    service.startCollection()
    assertTrue("Collection should be active", service.isCollectionEnabled())

    Disposer.dispose(service)
    assertFalse("Collection should be stopped after disposal", service.isCollectionEnabled())
  }
}
