package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import kotlinx.coroutines.Dispatchers
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentProjectStateServiceTest : AugmentBasePlatformTestCase() {
  @Test
  fun testShouldSubscribeAndTriggerOnStateChange() {
    val cs = augmentHelpers().createCoroutineScope(Dispatchers.IO)
    val service = AugmentProjectStateService(project, cs)
    var stateChanges = 0
    service.subscribe(
      project.messageBus.connect(service),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          stateChanges++
        }
      },
      triggerOnStateChange = true,
    )

    // Should have been called once since we triggered on state change
    waitForAssertion({
      assertEquals(1, stateChanges)
    })
  }
}
