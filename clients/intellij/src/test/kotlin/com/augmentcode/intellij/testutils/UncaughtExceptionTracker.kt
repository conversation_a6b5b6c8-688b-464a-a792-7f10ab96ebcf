package com.augmentcode.intellij.testutils

import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

/**
 * Utility class for capturing unhandled exceptions in asynchronous code during tests.
 *
 * ```kotlin
 * val exceptionTracker = UncaughtExceptionTracker("Foo bar, things didn't work")
 * exceptionTracker.use {
 *     // Your test code here
 *     doTheThing()
 *
 *     // Wait for any potential unhandled exceptions
 *     exceptionTracker.waitForExceptions(1000)
 *
 *     // Verify no unhandled exceptions occurred
 *     assertFalse("There should be no unhandled exceptions with the specified error message",
 *                 exceptionTracker.hasExceptions())
 * }
 * ```
 */
class UncaughtExceptionTracker() {
  private val exceptionsCaught = mutableListOf<Throwable>()
  private var originalHandler: Thread.UncaughtExceptionHandler? = null

  companion object {
    fun exceptionContains(message: String): (Throwable) -> Boolean =
      {
        it.message?.contains(message) == true
      }
  }

  /**
   * Use this utility in a try-finally block to ensure proper cleanup.
   */
  fun <T> capture(
    exceptionFilter: (Throwable) -> <PERSON><PERSON><PERSON>,
    block: () -> T,
  ): T {
    startCapturing(exceptionFilter)
    try {
      return block()
    } finally {
      stopCapturing()
    }
  }

  private fun startCapturing(exceptionFilter: (Throwable) -> Boolean) {
    originalHandler = Thread.getDefaultUncaughtExceptionHandler()

    Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
      if (exceptionFilter(throwable)) {
        exceptionsCaught.add(throwable)
      }
      originalHandler?.uncaughtException(thread, throwable)
    }
  }

  private fun stopCapturing() {
    Thread.setDefaultUncaughtExceptionHandler(originalHandler)
  }

  fun waitForExceptions(timeoutMs: Long = 1000) = runBlocking { delay(timeoutMs) }

  fun hasExceptions() = exceptionsCaught.isNotEmpty()
}
