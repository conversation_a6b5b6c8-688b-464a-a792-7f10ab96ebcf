package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import kotlinx.coroutines.test.runTest

class FullSyncFlowSyncV3Test : SyncV3TestCase() {
  fun testFullSyncFlow() =
    runTest {
      val state = MockEngineState()
      val mockEngine = createMockEngine(state)
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val tempDir = getVirtualFile(createTempDirectory())
      PsiTestUtil.addContentRoot(module, tempDir)
      val file =
        runWriteAction {
          tempDir.createChildData(this, "foo.txt").also {
            VfsUtil.saveText(it, "foo")
          }
        }
      val indexedUri = augmentHelpers().forceIndexingForFile(file)
      assertNotNull(indexedUri)

      waitForRequests(mockEngine, "/find-missing", 1)
      waitForRequests(mockEngine, "/batch-upload", 1)
      waitForRequests(mockEngine, "/find-missing", 2, timeoutMs = 10000)
      // In Index V3, we don't make a checkpoint request until
      // the working set is full.

      val blobNameServer = BlobNameService.getInstance(project)
      val blobName = blobNameServer.getByPath(file.path)
      assertNotNull(blobName)

      val workspaceManager = WorkspaceCoordinatorService.getInstance(project)
      val payload = workspaceManager.getCheckpoint()

      assertNull(payload.checkpointId)
      assertEquals(1, payload.addedBlobs.size)
      assertEquals(setOf(blobName!!), payload.addedBlobs)
      assertTrue(payload.deletedBlobs.isEmpty())
    }
}
