package com.augmentcode.intellij.webviews.chat

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.ide.scratch.ScratchFileService
import com.intellij.ide.scratch.ScratchRootType
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.BaseProjectDirectories.Companion.getBaseDirectories
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.testFramework.PlatformTestUtil
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ChatCreateFileTest : AugmentBasePlatformTestCase() {
  @Test
  fun testChatCreateFile() =
    runBlocking {
      val messagingService = ChatMessagingService.getInstance(project)
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "code": "print('Hello, world!')",
              "relPath": "hello.py"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Wait for all UI events to complete
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Wait for write actions
      WriteCommandAction.runWriteCommandAction(project) {}

      // Check that file was created
      val createdFile = project.getBaseDirectories().first().findChild("hello.py")
      assertNotNull("File hello.py should have been created", createdFile)

      // Check the contents of the file
      val fileContent = createdFile?.let { String(it.contentsToByteArray()) }
      assertEquals("File content should match the provided code", "print('Hello, world!')", fileContent)
    }

  @Test
  fun testChatCreateFileNoName() =
    runBlocking {
      val messagingService = ChatMessagingService.getInstance(project)
      // creates augment-chat.txt
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "code": "print('Hello, world!')"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Creates augment-chat-1.txt
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "code": "print('Hello, world!')"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Wait for all UI events to complete
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Wait for write actions
      WriteCommandAction.runWriteCommandAction(project) {}

      // Check that file was created
      val scratchRootPath = ScratchFileService.getInstance().getRootPath(ScratchRootType.getInstance())
      val scratchRootFile = LocalFileSystem.getInstance().findFileByPath(scratchRootPath)
      assertNotNull("Child should be in scratch root", scratchRootFile?.findChild("augment-chat"))
      assertNotNull("Child should be in scratch root", scratchRootFile?.findChild("augment-chat_1"))
    }

  @Test
  fun testChatCreateFileAlreadyExists() =
    runBlocking {
      val messagingService = ChatMessagingService.getInstance(project)
      // Attempts to create hello.py again
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "relPath": "foo/hello.py",
              "code": "print('Hello, world!')"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Attempts to create hello.py again with new text
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "relPath": "foo/hello.py",
              "code": "print('Goodbye, cruel world!')"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Wait for all UI events to complete
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Wait for write actions
      WriteCommandAction.runWriteCommandAction(project) {}

      // Check that file was created and has the original text
      val createdFile = project.getBaseDirectories().first().findFileByRelativePath("foo/hello.py")
      assertNotNull("File hello.py should have been created", createdFile)
      val fileContent = createdFile?.let { String(it.contentsToByteArray()) }
      assertEquals("File content should match the provided code", "print('Hello, world!')", fileContent)
    }

  @Test
  fun testChatCreateFileInSubdirectoryOfGitRepo() =
    runBlocking {
      // Create a mock git repository structure
      val augmentRootDir = myFixture.tempDirFixture.findOrCreateDir("..")
      assertNotNull(augmentRootDir)
      WriteCommandAction.runWriteCommandAction(project) {
        // Create .git directory in root to simulate git repo
        augmentRootDir.createChildData(this, ".git")
      }

      // Create a subdirectory in the project root
      val projectBaseDir = myFixture.tempDirFixture.findOrCreateDir(".")
      assertNotNull(projectBaseDir)
      assertTrue(projectBaseDir.path.endsWith("src")) // Make sure this aligns with webview message below
      assertTrue(project.getBaseDirectories().contains(projectBaseDir))

      val messagingService = ChatMessagingService.getInstance(project)
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "code": "print('Hello, world!')",
              "relPath": "src/hello.py"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Wait for all UI events to complete
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Wait for write actions
      WriteCommandAction.runWriteCommandAction(project) {}

      // Check that file was created in the project subdirectory, not the git root
      val createdFile = project.getBaseDirectories().first().findChild("hello.py")
      assertNotNull("File hello.py should have been created in project subdirectory", createdFile)

      // Check the contents of the file
      val fileContent = createdFile?.let { String(it.contentsToByteArray()) }
      assertEquals("File content should match the provided code", "print('Hello, world!')", fileContent)

      // Clean up .git file, because it could cause issues with other tests
      WriteCommandAction.runWriteCommandAction(project) {
        augmentRootDir.findChild(".git")?.delete(this)
      }
    }

  @Test
  fun testChatCreateFileOpensInEditor() =
    runBlocking {
      val messagingService = ChatMessagingService.getInstance(project)
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "code": "print('Hello, world!')",
              "relPath": "test.py"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Wait for all UI events to complete
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Verify file is opened in editor
      val openFiles = FileEditorManager.getInstance(project).openFiles
      assertTrue(
        "Created file should be opened in editor",
        openFiles.any { it.name == "test.py" },
      )
    }

  @Test
  fun testChatCreateScratchFileOpensInEditor() =
    runBlocking {
      val messagingService = ChatMessagingService.getInstance(project)
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "code": "print('Hello, world!')"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Wait for all UI events to complete
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Verify scratch file is opened in editor
      val openFiles = FileEditorManager.getInstance(project).openFiles
      assertTrue(
        "Created scratch file should be opened in editor",
        openFiles.any { it.name.startsWith("augment-chat") },
      )
    }

  @Test
  fun testChatCreateFileFailure() =
    runBlocking {
      val notifications = mutableListOf<Notification>()
      project.messageBus.connect(testRootDisposable).subscribe(
        Notifications.TOPIC,
        object : Notifications {
          override fun notify(notification: Notification) {
            notifications.add(notification)
          }
        },
      )

      val messagingService = ChatMessagingService.getInstance(project)
      // Use an invalid path that should cause file creation to fail
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
            "type": "chat-create-file",
            "data": {
              "code": "print('Hello, world!')",
              "relPath": "foo/bar\u0000/test.py"
            }
          }
          """.trimIndent(),
        ).toList()
      }

      // Wait for all UI events to complete
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

      // Verify that error notification was shown
      assertTrue(
        "Should show error notification",
        notifications.any {
          it.title == "File Creation Failed" &&
            it.content.contains("Could not create file at") &&
            it.type == NotificationType.ERROR
        },
      )
    }
}
