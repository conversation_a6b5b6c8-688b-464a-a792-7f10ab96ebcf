package com.augmentcode.intellij.auth

import com.augmentcode.api.OnboardingSessionEvent
import com.augmentcode.api.OnboardingSessionEventName
import com.augmentcode.intellij.metrics.OnboardingSessionEventReporter
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.google.gson.reflect.TypeToken
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.toByteArray
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.toList
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentOAuthStateTest : AugmentBasePlatformTestCase() {
  @Test
  fun testSaveGetClear() =
    runBlocking {
      val initialCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(initialCredentials, null)

      val exampleCredentials = AugmentCredentials("example access token", "example tenant URL")
      AugmentOAuthState.instance.saveCredentials(exampleCredentials)

      val gotCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(gotCredentials?.accessToken, exampleCredentials.accessToken)
      assertEquals(gotCredentials?.tenantURL, exampleCredentials.tenantURL)

      AugmentOAuthState.instance.clear()

      val clearedCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(clearedCredentials, null)
    }

  @Test
  fun testOnboardingMessageOnSignIn() =
    runBlocking {
      OnboardingSessionEventReporter.getInstance(project).enableUpload()

      val initialCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(initialCredentials, null)

      val recorderLastOnboardingEvents = mutableListOf<OnboardingSessionEvent>()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/record-onboarding-session-event" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val gson = GsonUtil.createApiGson()
              val requestData =
                gson.fromJson(
                  requestBody,
                  object : TypeToken<Map<String, List<OnboardingSessionEvent>>>() {}.type,
                ) as Map<String, List<OnboardingSessionEvent>>
              val events = requestData["events"] ?: emptyList()
              recorderLastOnboardingEvents.addAll(events)
              HttpUtil.respondOK(this)
            }

            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)
      val messagingService = ChatMessagingService.getInstance(project)

      // Mock sidecar to speed up AugmentProjectStateService setup
      val sidecarService = mockk<SidecarService>(relaxed = true)
      coEvery { sidecarService.startServer() } returns Unit
      project.registerOrReplaceServiceInstance(
        SidecarService::class.java,
        sidecarService,
        testRootDisposable,
      )

      // load main panel to make the messaging service subscribe to credentials changes
      val messageText =
        """
        {
          "type":"main-panel-loaded",
          "requestId":"abc-123",
          "error":null
        }
        """.trimIndent()
      messagingService.processMessageFromWebview(messageText).toList()

      // simulate sign in
      augmentHelpers().emulateSignInFlow(true)

      // check that the sign-in event was sent to the API
      waitForAssertion({
        assertTrue(recorderLastOnboardingEvents.any { it.event_name == OnboardingSessionEventName.SignedIn.apiName })
      })
    }
}
