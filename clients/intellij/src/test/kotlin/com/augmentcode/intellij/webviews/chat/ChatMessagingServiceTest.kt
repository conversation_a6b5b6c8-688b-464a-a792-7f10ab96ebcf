package com.augmentcode.intellij.webviews.chat

import com.augmentcode.intellij.guidelines.GuidelinesService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.components.service
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ChatMessagingServiceTest : AugmentBasePlatformTestCase() {
  @Test
  fun testSendGuidelinesStateToWebviewExists() {
    // This test verifies that the sendGuidelinesStateToWebview method exists
    // and can be called without errors
    val messagingService = ChatMessagingService.getInstance(project)

    // This should not throw any exceptions
    messagingService.sendGuidelinesStateToWebview()
  }

  @Test
  fun testSendGuidelinesStateToWebviewWithGuidelinesEnabled() {
    // Set up test guidelines content
    val testGuidelines = "Test guidelines content"
    val guidelinesService = project.service<GuidelinesService>()
    guidelinesService.updateUserGuidelines(testGuidelines)

    // Create a messaging service
    val messagingService = ChatMessagingService.getInstance(project)

    // Call the method
    messagingService.sendGuidelinesStateToWebview()

    // No assertions needed - if the method doesn't throw exceptions, the test passes
  }

  @Test
  fun testSendGuidelinesStateToWebviewWithEmptyGuidelines() {
    // Set up empty guidelines content
    val guidelinesService = project.service<GuidelinesService>()
    guidelinesService.updateUserGuidelines("")

    // Create a messaging service
    val messagingService = ChatMessagingService.getInstance(project)

    // Call the method
    messagingService.sendGuidelinesStateToWebview()

    // No assertions needed - if the method correctly handles empty guidelines, the test passes
  }
}
