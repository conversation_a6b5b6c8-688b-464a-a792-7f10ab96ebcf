package com.augmentcode.api

import com.augmentcode.intellij.utils.IndexUtil.expectedBlobName
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.UploadBlob

@RunWith(JUnit4::class)
class UploadableBlobTest {
  @Test
  fun testCalculation() {
    val blob =
      UploadBlob
        .newBuilder()
        .setPath("base/blob_names/test_data/blob-0")
        // it's hard to read files outside the Gradle project, so we'll just embed the content here
        .setContent("This is a sample blob.\n")
        .build()
    Assert.assertEquals(
      "e2b1f294861ace3274a2e3e953f6c27653da6e9468eb6fb2bf63f5d0e2d7481c",
      expectedBlobName(blob.path, blob.content),
    )
  }
}
