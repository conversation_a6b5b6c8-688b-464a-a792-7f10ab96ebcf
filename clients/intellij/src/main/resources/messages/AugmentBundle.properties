settings.apiToken=API Token
settings.serverURL=Server URL
settings.signIn=Sign In
settings.signOut=Sign Out
settings.displayName=Augment
settings.helpTopic=Augment Completion
settings.completionSettings=Completion Settings
settings.inlineCompletionEnabled=Provide automatic inline completions
settings.inlineCompletionEnabled.withShortcutDescription=With this setting disabled, you will still be able to invoke Augment completion via the <b>Code / Code Completion</b> menu, as well as by calling the <b>{0}</b> action directly or using the <b>{1}</b> shortcut.
settings.inlineCompletionEnabled.noShortcutDescription=With this setting disabled, you will still be able to invoke Augment completion via the <b>Code / Code Completion</b> menu, as well as by calling the <b>{0}</b> action directly. You can also configure a shortcut for the <b>Call Inline Completion</b> action in the <b>Keymap</b> settings.
settings.inlineCompletionEnabled.configureShortcutTitle=Configure Explicit Completion Shortcut
settings.completionSettings.disableByLanguage=Disable Completion By Language
settings.completionSettings.disableByLanguage.comment=Comma separated list of file extensions to disable Augment. For example, *.js, *.ts
settings.debugSettings=Debug Settings
settings.debugSettings.modelName=Model Name
auth.signInDialog.title=Augment Sign In
auth.signInDialog.body="Signing in to Augment..."
augment.indexing.validation=Augment is not available until indices are ready
augment.statusbar.display.name=Augment
augment.status.initial.tooltip=Augment
augment.status.signinNeeded.tooltip=Sign In
augment.status.ok.tooltip=Toggle Augment Chat
augment.status.disabled.tooltip=Automatic completions are off
augment.status.unauthorized.tooltip=Authentication Failed
augment.notification.group.name=Augment Updates
extensionStatus.title=Augment Extension Status
extensionStatus.section.plugin.title=Plugin Details
extensionStatus.section.plugin.versionField=Plugin Version:
extensionStatus.section.plugin.intellijField=IntelliJ Version:
extensionStatus.section.plugin.osField=Operating System:
extensionStatus.section.session.title=Session
extensionStatus.section.session.idField=Session ID:
extensionStatus.section.configuration.title=Extension Configuration
extensionStatus.section.auth.title=Authentication
extensionStatus.section.auth.oauthTenantURL=Tenant URL
extensionStatus.section.auth.hasOAuthCredentials=Has OAuth Credentials
extensionStatus.section.context.title=Context
extensionStatus.section.context.blobsAwaitingAmount=Files to analyze:
extensionStatus.section.context.filesFromIntellijIndex=Files from IntelliJ Index:
extensionStatus.section.context.blobsIndexedAmount=Tracked files:
extensionStatus.section.context.filesToProcess=Files to process:
extensionStatus.section.context.filesToUpload=Files to upload:
extensionStatus.section.context.filesWaitingForIndexing=Files being indexed:
extensionStatus.section.context.indexVersion=Index Version:
auth.signInErrorDialog.body=Failed to sign in. Please try again and reach out to Augment support, if the issue continues.
chat.toolwindow.title=Augment
chat.insert.code.block=Inserted from Augment Chat
chat.insert.code.block.new.file=Create File from Augment Chat
chat.available.notification.title=Chat with Augment
chat.available.notification.content=Augment Chat is now available with full project context
chat.available.notification.open.action.title=Open Chat
chat.available.notification.close.action.title=Close
augment.status.generating-completion.tooltip=Generating completion
augment.status.no-completions.tooltip=No completions generated
settings.debugSettings.triggerCompletionsOnAllEdits=Force completions on all edits
augment.status-bar.title=Augment
chat.message.unavailable=\ Augment is only available to signed in users to keep your code secure. To get the most out of whole codebase understanding, please sign in.
actions.completions.enable=Enable Completions
actions.completions.disable=Disable Completions
chat.message.not-yet-synced=*Note: Augment is not yet fully synced and may be unable to answer questions about your workspace.*\n\n
chat.message.too.large=The selected text exceeds the allowable limit. Please reduce the amount of text and try again.
actions.history.show=Show History
actions.history.hide=Close History
chat.tab.title=Chat
history.tab.title=History
actions.accept.all=Accept All
actions.accept.all.description=Apply all of Augment's suggestions to the file
actions.copy.requestId=Copy ID
actions.copy.requestId.description=Copy the Augment smart apply request ID to the clipboard
actions.copy.requestId.copied=Copied!
actions.reject.all=Reject All
actions.reject.all.description=Reject all of Augment's suggestions and close the diff viewer
chat.message.syncing.permission.needed=Augment works best when it has access to your entire codebase. Please grant permission to sync your project.
settings.integrations=Integrations
settings.integrations.atlassian=Atlassian
settings.integrations.atlassian.serverUrl=Server URL
settings.integrations.atlassian.personalApiToken=Personal API Token
settings.integrations.atlassian.username=Username
settings.integrations.notion=Notion
settings.integrations.notion.apiToken=API Token
settings.integrations.linear=Linear
settings.integrations.linear.apiToken=API Token
settings.integrations.github=GitHub
settings.integrations.github.apiToken=API Token
augment.tool.used=Augment Used "{0}" Tool
actions.manage.account=Manage Account
actions.manage.account.community=Manage Account (Community)
actions.manage.account.professional=Manage Account (Self-Serve)
actions.manage.account.enterprise=Manage Account (Enterprise)
actions.help=Help
augment.starting.agents.sidecar.title=Starting Augment Agents service...
augment.status.get-model-info-failed.tooltip=Cannot connect to Augment
augment.status.initializing.tooltip=Initializing Augment
error.report.action.text=Report to Augment
settings.debugSettings.webviewOptions=WebView Options
settings.offscreenRenderingEnabled=Enable offscreen rendering
settings.outOfProcessEnabled=Enable out of process (requires restart)
