package com.augmentcode.chat;

public enum DerivedStateName {
    DIS<PERSON>LE_COPILOT("ShouldDisableCopilot"),
    DIS<PERSON>LE_CODEIUM("ShouldDisableCodeium"),
    WORKSPACE_NOT_POPULATED("WorkspaceNotPopulated"),
    SIGN_IN_REQUIRED("UserShouldSignIn"),
    ALL_ACTIONS_COMPLETE("AllActionsComplete"),
    SYNCING_PERMISSION_NEEDED("SyncingPermissionNeeded"),
    WORKSPACE_TOO_LARGE("workspaceTooLarge"),
    UPLOADING_HOME_DIR("uploadingHomeDir");

    private final String value;

    DerivedStateName(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return this.value;
    }
}
