package com.augmentcode.api;

import javax.annotation.Nullable;

public class AgentSessionEventData {
  // NOT USED AT THE MOMENT
  // @Nullable
  // public AgentReversionData agent_reversion_data;

  @Nullable
  public AgentInterruptionData agent_interruption_data;
  @Nullable
  public RememberToolCallData remember_tool_call_data;
  @Nullable
  public MemoriesFileOpenData memories_file_open_data;
  @Nullable
  public InitialOrientationData initial_orientation_data;
  @Nullable
  public ClassifyAndDistillData classify_and_distill_data;
  @Nullable
  public FlushMemoriesData flush_memories_data;
}
