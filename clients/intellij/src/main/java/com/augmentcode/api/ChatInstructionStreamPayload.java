package com.augmentcode.api;


import org.jetbrains.annotations.NotNull;

import java.util.List;

public class ChatInstructionStreamPayload {
  public String model;
  public String path;
  public String prefix;
  public String selectedText;
  public String suffix;
  public String instruction;
  public String lang;
  public String blobName;
  public Integer prefixBegin;
  public Integer suffixEnd;
  public BlobsPayload blobs;
  public List<Exchange> chatHistory;
  public String codeBlock;
  public String targetFilePath;
  public String targetFileContent;
  public String contextCodeExchangeRequestId;
}
