package com.augmentcode.api;

import com.google.gson.annotations.SerializedName;

public enum RemoteToolResponseStatus {
    // Unknown status
    @SerializedName("0")
    EXECUTION_UNKNOWN_STATUS(0),
    // Tool executed successfully
    @SerializedName("1")
    EXECUTION_SUCCESS(1),
    // Tool not found
    @SerializedName("2")
    NOT_FOUND(2),
    // Invalid input that violates the tool's input schema
    @SerializedName("3")
    INVALID_INPUT(3),
    // Tool execution failed
    @SerializedName("4")
    EXECUTION_ERROR(4),
    // Tool is not available due to config
    @SerializedName("5")
    NOT_AVAILABLE(5),
    // Auth failed
    @SerializedName("6")
    AUTHENTICATION_ERROR(6);

    private final int value;

    RemoteToolResponseStatus(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
