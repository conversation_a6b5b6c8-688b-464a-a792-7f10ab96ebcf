package com.augmentcode.api;

import com.google.gson.annotations.SerializedName;

public enum FeedbackRating implements NumericEnum {
  UNSET(0),
  POSITIVE(1),
  NEGATIVE(2);

  private final int value;

  private FeedbackRating(int value) {
    this.value = value;
  }

  public static FeedbackRating fromRpcType(com.augmentcode.rpc.FeedbackRating value) {
    for (FeedbackRating r : FeedbackRating.values()) {
      if (r.value == value.getNumber()) {
        return r;
      }
    }
    return null;
  }

  @Override
  public int getValue() {
    return value;
  }
}
