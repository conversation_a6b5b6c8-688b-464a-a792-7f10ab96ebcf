package com.augmentcode.api;

public enum RemoteToolId implements NumericEnum {
    Unknown(0),

    // Google search
    WebSearch(1),

    // Jira tools (DEPRECATED)
    // JiraSearch(2),
    // JiraIssue(3),
    // JiraProject(4),

    // Notion tools (DEPRECATED)
    // NotionSearch(5),
    // NotionPage(6),

    // Linear tools (DEPRECATED)
    // LinearSearchIssues(7),

    // GitHub tools
    GitHubApi(8),

    // Confluence tools (DEPRECATED)
    // ConfluenceSearch(9),
    // ConfluenceContent(10),
    // ConfluenceSpace(11),

    // New integration tools
    Linear(12),
    Jira(13),
    Confluence(14),
    Notion(15),
    Supabase(16);

    private final int value;

    RemoteToolId(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
