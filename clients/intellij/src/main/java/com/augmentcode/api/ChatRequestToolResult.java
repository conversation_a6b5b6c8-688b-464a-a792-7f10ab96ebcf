package com.augmentcode.api;

import java.util.List;

public class ChatRequestToolResult {
  public String toolUseId;
  // Plain text content (deprecated when content_nodes is present)
  public String content;
  public boolean isError;
  public String requestId;
  // List of content nodes (text or images)
  // If present, takes precedence over content field
  public List<ChatRequestContentNode> contentNodes;
}
