package com.augmentcode.api;

public class SmartPasteResolution {
    public String requestId;
    /**
     *  A list of booleans indicating whether each chunk was accepted or rejected.
     *  This is accurate in percentage accepted but not the exact chunks that
     *  were accepted or rejected.
     */
    public boolean[] isAcceptedChunks;
    public boolean isAcceptAll;
    public boolean isRejectAll;
    // The time that the initial request was made
    public long initialRequestTimeSec;
    public long initialRequestTimeNsec;
    // The time that the stream completed successfully
    public long streamFinishTimeSec;
    public long streamFinishTimeNsec;
    // The time that the user clicked apply
    public long applyTimeSec;
    public long applyTimeNsec;
    // The time that the resolution (closing the diff view) is reported
    public long resolveTimeSec;
    public long resolveTimeNsec;
}
