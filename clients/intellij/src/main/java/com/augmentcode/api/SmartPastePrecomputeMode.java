package com.augmentcode.api;

public enum SmartPastePrecomputeMode {
  OFF("off"),
  VISIBLE_HOVER("visible-hover"),
  VISIBLE("visible"),
  ON("on");

  private final String value;

  SmartPastePrecomputeMode(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  public static SmartPastePrecomputeMode fromString(String value) {
    for (SmartPastePrecomputeMode mode : values()) {
      if (mode.value.equals(value)) {
        return mode;
      }
    }
    return VISIBLE_HOVER; // default value
  }
}
