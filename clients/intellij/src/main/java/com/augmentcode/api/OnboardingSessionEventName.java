package com.augmentcode.api;

/**
 * OnboardingSessionEventName is the name of an onboarding session event.
 * Matches the vscode version in onboarding-types.ts.
 */
public enum OnboardingSessionEventName {
  SignedIn("signed-in"),
  StartedSyncing("started-syncing"),
  FinishedSyncing("finished-syncing"),
  SawSummary("saw-summary"),
  UsedChat("used-chat"),
  AcceptedCompletion("accepted-completion"),
  UsedSlashAction("used-slash-action");

  private final String apiName;

  OnboardingSessionEventName(String abbreviation) {
    this.apiName = abbreviation;
  }

  public String getApiName() {
    return this.apiName;
  }
}
