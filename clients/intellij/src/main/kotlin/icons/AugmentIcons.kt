package icons

import com.intellij.openapi.util.IconLoader
import javax.swing.Icon

object AugmentIcons {
  private fun load(path: String): Icon {
    return IconLoader.getIcon(path, javaClass)
  }

  @JvmField
  val StatusDisabled = load("/images/status-disabled-completions.svg")

  @JvmField
  val StatusInitial = load("/images/status-initial.svg")

  @JvmField
  val StatusDefault = load("/images/status-default.svg")

  @JvmField
  val StatusDots = load("/images/status-dots.svg")

  @JvmField
  val StatusNoCompletions = load("/images/status-no-completions.svg")
}
