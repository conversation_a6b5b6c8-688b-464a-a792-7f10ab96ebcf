package com.augmentcode.intellij.history

import com.augmentcode.api.CompletionFeedbackRequest
import com.augmentcode.api.FeedbackRating
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

data class AugmentFeedbackState(
  val selectedRating: FeedbackRating = FeedbackRating.UNSET,
  val feedbackText: String = "",
  val sent: Boolean = false,
)

@Service(Service.Level.PROJECT)
class AugmentHistoryModel(
  private val project: Project,
  private val cs: CoroutineScope,
) : Disposable {
  companion object {
    fun getInstance(project: Project): AugmentHistoryModel = project.getService(AugmentHistoryModel::class.java)

    // we append this suffix to intellij ratings since the api doesn't take a client
    const val FEEDBACK_SUFFIX = "#IntelliJ"

    // limit number of completions to save memory
    const val MAX_COMPLETIONS = 500
  }

  private val completions = mutableListOf<AugmentHistoryEntry>()
  private val feedbackState = mutableMapOf<String, AugmentFeedbackState>()
  private val completionsPublisher = project.messageBus.syncPublisher(AugmentRecentCompletionsListener.TOPIC)

  fun clear() {
    completions.clear()
    feedbackState.clear()
  }

  fun addCompletionAsync(completion: AugmentHistoryEntry) {
    cs.launch {
      completions.add(completion)

      while (completions.size > MAX_COMPLETIONS) {
        completions.removeFirst()
      }

      completionsPublisher.onNewCompletion(completion)
    }
  }

  fun getFeedback(requestId: String): AugmentFeedbackState = feedbackState[requestId] ?: AugmentFeedbackState()

  fun getCompletions(): List<AugmentHistoryEntry> = completions

  fun setFeedback(
    requestId: String,
    state: AugmentFeedbackState,
  ) {
    feedbackState[requestId] = state
  }

  fun sendFeedbackAsync(
    requestId: String,
    onComplete: (success: Boolean) -> Unit,
  ) {
    cs.launch {
      val request =
        CompletionFeedbackRequest().apply {
          this.requestId = requestId
          this.rating = feedbackState[requestId]?.selectedRating ?: FeedbackRating.UNSET
          this.note = (feedbackState[requestId]?.feedbackText ?: "") + FEEDBACK_SUFFIX
        }
      val resp = AugmentAPI.instance.completionFeedback(request)
      // if successful, set sent to true
      if (resp != null) {
        feedbackState[requestId] = getFeedback(requestId).copy(sent = true)
        onComplete(true)
        return@launch
      }
      onComplete(false)
    }
  }

  override fun dispose() {
    clear()
  }
}
