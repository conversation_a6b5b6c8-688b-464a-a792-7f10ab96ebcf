package com.augmentcode.intellij.index

import com.augmentcode.intellij.index.AugmentRoot.findRelativePathWithRoot
import com.intellij.ide.scratch.ScratchUtil
import com.intellij.notebook.editor.BackedVirtualFile
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.BaseProjectDirectories.Companion.getBaseDirectories
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.intellij.openapi.project.modules
import com.intellij.openapi.project.rootManager
import com.intellij.openapi.roots.ProjectRootManager
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import java.nio.file.Paths

// TODO(diehuxx): change the name of this to like <PERSON><PERSON><PERSON><PERSON><PERSON> or RootHelper or something
object AugmentRoot {
  fun findFile(absPath: String): VirtualFile? {
    return LocalFileSystem.getInstance().findFileByPath(absPath)
  }

  fun findFile(
    rootPath: String,
    relativePath: String,
  ): VirtualFile? {
    val absolutePath = Paths.get(rootPath, relativePath).toString()
    return findFile(absolutePath)
  }

  /**
   * Finds the relative path of a file using [findRelativePathWithRoot]
   */
  fun findRelativePath(
    project: Project,
    file: VirtualFile,
  ): String? {
    return findRelativePathWithRoot(project, file)?.relativePath
  }

  fun findQualifiedPathName(
    project: Project,
    file: VirtualFile,
  ): QualifiedPathName? {
    val augmentRelativePathWithRoot = findRelativePathWithRoot(project, file) ?: return null
    val rootPath = augmentRelativePathWithRoot.rootPath
    val relativePath = augmentRelativePathWithRoot.relativePath ?: return null
    return QualifiedPathName(rootPath, relativePath)
  }

  /**
   * Finds the root path and relative path of a file using a two-step process:
   *
   * 1. First, find the project-level root:
   *    - Get all content roots from the project
   *    - Filter to only those that contain our target file
   *    - If multiple content roots contain the file, choose the highest one in the directory tree
   *      (For example, if the file is in /foo/bar/baz and both /foo and /foo/bar are content roots,
   *       we'll choose /foo)
   *
   * 2. Then, look for a more specific repository root:
   *    - Starting from the file's directory, walk up the tree
   *    - If we find a directory containing either .git or .augmentroot, use that as the root
   *    - If no repository root is found, fall back to the project-level root from step 1
   *
   * This approach ensures we handle both standard project structures and cases where repositories
   * are nested within the project (e.g., a monorepo with multiple sub-projects).
   */
  fun findRelativePathWithRoot(
    project: Project,
    file: VirtualFile,
  ): AugmentPathWithRoot? {
    if (ScratchUtil.isScratch(file)) {
      return AugmentPathWithRoot(null, "", file.name)
    }

    // For jupyter notebooks, get the underlying file
    val originalFile = getOriginalFile(file)

    // Quickly filter out non-project files
    val hasBeenIndexedByIntelliJ =
      runReadAction {
        ProjectRootManager.getInstance(project).fileIndex.isInContent(file)
      }
    if (!hasBeenIndexedByIntelliJ) {
      return null
    }

    // Fall back to manual check of project, module, and content roots
    val root = findRootForProjectFile(project, originalFile)
    val rootPath = root?.path ?: ""
    val relPath =
      if (root != null) {
        VfsUtil.getRelativePath(originalFile, root, '/')
      } else {
        originalFile.name
      }

    return AugmentPathWithRoot(
      root,
      rootPath,
      relPath,
    )
  }

  fun findRootForProjectFile(
    project: Project,
    file: VirtualFile,
  ): VirtualFile? {
    val originalFile = getOriginalFile(file)
    val isDirectory = originalFile.isDirectory

    // Find the closest .git or .augmentroot.
    // Since, for example, we can open a subdirectory under a monorepo
    var parent = if (isDirectory) originalFile else originalFile.parent
    while (parent != null) {
      if (parent.findChild(".git") != null || parent.findChild(".augmentroot") != null) {
        return parent
      }
      parent = parent.parent
    }

    // NOTE: Prefer roots from ProjectRootManager because project.getBaseDirectories() does not always get all content roots
    var allRootCandidates: Collection<VirtualFile> = ProjectRootManager.getInstance(project).contentRoots.asList()
    if (allRootCandidates.isEmpty()) {
      // If there are no content roots, use the project base dir
      allRootCandidates = project.getBaseDirectories()
    }
    val rootCandidates = allRootCandidates.filter { VfsUtil.isAncestor(it, originalFile, true) }

    if (rootCandidates.isEmpty()) {
      // check if file is a part of a module in case of a complex project structure
      for (module in project.modules) {
        for (moduleRoot in module.rootManager.contentRoots) {
          if (VfsUtil.isAncestor(moduleRoot, originalFile, true)) {
            return moduleRoot
          }
        }
      }
      return null
    }

    if (rootCandidates.size == 1) {
      return rootCandidates.firstOrNull()
    }

    // Try to find a common ancestor among the candidates
    // This is for the case when a file is /foo/bar/baz and the base directories are /foo and /foo/bar
    val commonAncestor =
      rootCandidates.find { root ->
        rootCandidates.all { candidate ->
          root == candidate || VfsUtil.isAncestor(root, candidate, true)
        }
      }

    if (commonAncestor == null) {
      // I think this should never happen, but just in case
      thisLogger().warn(
        "Ambiguous ancestors found for file: ${originalFile.path}. " +
          "Candidates: ${rootCandidates.joinToString { it.path }}",
      )
    }

    return commonAncestor
  }

  /**
   * Tries to find a project root based on the active editors.
   * Falls back to checking project's content roots.
   */
  fun findActiveProjectRoot(project: Project): VirtualFile? {
    // Try to get cwd from active editor
    for (activeEditor in FileEditorManager.getInstance(project).selectedEditors) {
      val file = activeEditor.file
      val root = findRootForProjectFile(project, file)
      if (root != null) {
        return root
      }
    }

    // TODO: Fall back to most recently changed folder

    // Last resort - get first project root
    var allRootCandidates: Collection<VirtualFile> = ProjectRootManager.getInstance(project).contentRoots.asList()
    if (allRootCandidates.isEmpty()) {
      // If there are no content roots, use the project base dir
      allRootCandidates = project.getBaseDirectories()
    }

    return allRootCandidates.firstOrNull()?.let {
      findRootForProjectFile(project, it)
    } ?: project.guessProjectDir()
  }

  private fun getOriginalFile(file: VirtualFile): VirtualFile {
    // Some text based files can have an additional abstractions level on top,
    // in which case on disk contents are transformed into a different format.
    // For example, Jupyter notebooks.
    // In this case, we need to get the original untransformed file.
    @Suppress("UnstableApiUsage")
    return BackedVirtualFile.getOriginFileIfBacked(file) // Used for jupyter notebooks
  }
}
