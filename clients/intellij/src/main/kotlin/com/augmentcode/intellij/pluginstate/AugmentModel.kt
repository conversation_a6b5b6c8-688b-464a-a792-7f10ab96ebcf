package com.augmentcode.intellij.pluginstate

import com.augmentcode.api.findDefaultModel
import com.augmentcode.api.findModel
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.openapi.util.text.Strings
import public_api.PublicApi.GetModelsResponse
import java.util.*

data class AugmentModel(
  val defaultModel: String,
  val supportedFileExtensions: Set<String>,
  val extensionToLanguageLookup: Map<String, String>,
  val userTier: GetModelsResponse.UserTier,
  val suggestedPrefixCharCount: Int,
  val suggestedSuffixCharCount: Int,
) {
  companion object {
    fun fromGetModelsResponse(modelConfig: GetModelsResponse): AugmentModel {
      val model =
        modelConfig.findModel(AugmentSettings.instance.modelName)
          ?: modelConfig.findDefaultModel()

      if (model == null) {
        throw IllegalStateException("Failed to find a model from the model config")
      }

      return modelConfig.let {
        val extensions = HashSet<String>()
        val extensionToLanguageLookup = mutableMapOf<String, String>()
        it.languagesList.forEach { l ->
          l.extensionsList.forEach { ext ->
            // normalize to VFS file extension format
            val trimmedExt = Strings.trimStart(ext.lowercase(Locale.getDefault()), ".")
            extensionToLanguageLookup[trimmedExt] = l.name
            extensions.add(trimmedExt)
          }
        }

        AugmentModel(
          defaultModel = it.defaultModel,
          supportedFileExtensions = extensions,
          extensionToLanguageLookup = extensionToLanguageLookup,
          userTier = it.userTier,
          suggestedPrefixCharCount = model.suggestedPrefixCharCount,
          suggestedSuffixCharCount = model.suggestedSuffixCharCount,
        )
      }
    }
  }
}
