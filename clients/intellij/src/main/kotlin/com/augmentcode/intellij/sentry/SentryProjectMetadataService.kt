package com.augmentcode.intellij.sentry

import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope

/**
 * Project-level service for managing Sentry metadata collection and state.
 *
 * This service acts as the coordinator between metadata collection and reporting:
 * - Creates and manages the SentryMetadataCollector instance for this project
 * - Stores and maintains the latest metadata
 * - Exposes metadata to SentryService via the latestMetrics property
 * - Handles the lifecycle of metadata collection (start/stop)
 * - Automatically disposed when the project is closed
 * - Managed as an IntelliJ service singleton (no manual registration required)
 */
@Service(Service.Level.PROJECT)
class SentryProjectMetadataService(private val project: Project, private val cs: CoroutineScope) : Disposable {
  private val logger = thisLogger()

  // The metadata collector for this project
  private var metadataCollector: SentryMetadataCollector? = null

  // Latest collected metadata
  @Volatile
  private var _latestMetrics: MetricsData? = null

  init {
    logger.debug("Initializing Sentry metadata for project: ${project.name}")
    startMetricsCollection()
  }

  /**
   * Get the latest collected metadata for this project.
   */
  val latestMetrics: MetricsData?
    get() = _latestMetrics

  /**
   * Start metadata collection for this project.
   */
  private fun startMetricsCollection() {
    try {
      metadataCollector =
        SentryMetadataCollector(project, cs, this) { metrics ->
          // Callback to receive collected metadata from the collector
          _latestMetrics = metrics
          logger.debug("Updated metadata for project '${project.name}': $metrics")
        }

      metadataCollector?.startCollection()
      logger.debug("Started Sentry metadata collection for project: ${project.name}")
    } catch (e: Exception) {
      logger.error("Failed to start Sentry metadata collection for project: ${project.name}", e)
    }
  }

  /**
   * Called automatically when the service is disposed (project closed).
   * Handles cleanup of metadata collection.
   * No manual unregistration needed as SentryService uses service singletons directly.
   * CoroutineScope is automatically cancelled by IntelliJ Platform when project closes.
   */
  override fun dispose() {
    metadataCollector = null
    _latestMetrics = null
    logger.debug("Disposed Sentry metadata for project: ${project.name}")
  }

  companion object {
    fun getInstance(project: Project): SentryProjectMetadataService {
      return project.service<SentryProjectMetadataService>()
    }
  }
}
