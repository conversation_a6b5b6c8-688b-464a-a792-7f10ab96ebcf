package com.augmentcode.intellij.actions

import com.augmentcode.intellij.chat.AugmentChatToolWindow
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.util.concurrency.annotations.RequiresEdt

class ShowChatAction : AnAction() {
  @RequiresEdt
  override fun actionPerformed(e: AnActionEvent) {
    ToolWindowManager.getInstance(e.project!!)
      .getToolWindow(AugmentChatToolWindow.ID)
      ?.activate(null)
  }
}
