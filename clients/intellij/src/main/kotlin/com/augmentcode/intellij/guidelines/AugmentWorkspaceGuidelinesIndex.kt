package com.augmentcode.intellij.guidelines

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.indexing.FileBasedIndex
import com.intellij.util.indexing.ID
import com.intellij.util.indexing.ScalarIndexExtension
import com.intellij.util.io.BooleanDataDescriptor

/**
 * Index for tracking workspace guidelines files.
 */
class AugmentWorkspaceGuidelinesIndex : ScalarIndexExtension<Boolean>() {
  // optimizes performance of iterating over the whole index
  override fun traceKeyHashToVirtualFileMapping(): Boolean = true

  override fun getKeyDescriptor(): BooleanDataDescriptor = BooleanDataDescriptor.INSTANCE

  override fun getName() = AUGMENT_WORKSPACE_GUIDELINES_INDEX_NAME

  override fun getVersion(): Int = 13

  override fun dependsOnFileContent(): Boolean = true

  override fun getInputFilter(): FileBasedIndex.InputFilter {
    return FileBasedIndex.InputFilter { file ->
      try {
        if (file.name == GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH) {
          return@InputFilter true
        }
      } catch (e: Throwable) {
        thisLogger().warn("Failed to check if file ${file.path} is workspace guidelines file. Skipping...", e)
        return@InputFilter false
      }

      return@InputFilter false
    }
  }

  override fun getIndexer() = AugmentWorkspaceGuidelinesIndexer()
}

val AUGMENT_WORKSPACE_GUIDELINES_INDEX_NAME = ID.create<Boolean, Void>("AugmentWorkspaceGuidelinesIndex")
