package com.augmentcode.intellij.workspacemanagement.utils

import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ChannelResult
import kotlinx.coroutines.selects.onTimeout
import kotlinx.coroutines.selects.select
import java.util.concurrent.atomic.AtomicInteger
import kotlin.time.Duration

/**
 * A wrapper class around channels that provides an approximate size.
 * The size is approximate because it is not synchronized with
 * the actual size of the channel, but it is considered close enough to monitor
 * for critical backpressure.
 *
 * It is strongly recommended that additional features not be added to this class.
 * Instead, please consider adding them to the delegate channel directly
 * or to the business logic handling the channel.
 */
class RoughlySizedChannel<T>(
  private val delegate: Channel<T>,
) : Channel<T> by delegate {
  private val _approximateSize = AtomicInteger(0)
  val approximateSize: Int get() = _approximateSize.get()

  override suspend fun send(element: T) {
    delegate.send(element)
    _approximateSize.incrementAndGet()
  }

  override fun trySend(element: T): ChannelResult<Unit> {
    return delegate.trySend(element).also { result ->
      if (result.isSuccess) _approximateSize.incrementAndGet()
    }
  }

  override suspend fun receive(): T {
    val element = delegate.receive()
    _approximateSize.decrementAndGet()
    return element
  }

  override fun tryReceive(): ChannelResult<T> {
    return delegate.tryReceive().also { result ->
      if (result.isSuccess) _approximateSize.decrementAndGet()
    }
  }

  @OptIn(ExperimentalCoroutinesApi::class)
  suspend fun receiveWithTimeout(timeout: Duration): T? {
    return select {
      onReceive {
        _approximateSize.decrementAndGet()
        it
      }
      onTimeout(timeout) { null }
    }
  }
}
