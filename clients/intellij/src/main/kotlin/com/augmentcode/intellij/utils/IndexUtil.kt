package com.augmentcode.intellij.utils

import com.intellij.openapi.fileEditor.impl.LoadTextUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readBytes
import com.intellij.util.io.DigestUtil
import java.nio.file.NoSuchFileException as JavaNoSuchFileException
import kotlin.io.NoSuchFileException as KotlinNoSuchFileException

/**
 * Simple utils shared between Index V2 and V3 implementations.
 * After we migrate to V3, we can move these to the V3 package.
 */
object IndexUtil {
  fun expectedBlobName(
    path: String,
    content: String,
  ): String {
    val bytes = path.toByteArray(Charsets.UTF_8) + content.toByteArray(Charsets.UTF_8)
    return DigestUtil.sha256Hex(bytes)
  }

  /**
   * This brings the entire file contents into memory, which can be expensive.
   */
  fun normalizedText(virtualFile: VirtualFile): String? {
    return try {
      // This API is used under the hood in FileContent#contentAsText, and it does not require read lock
      // https://github.com/JetBrains/intellij-community/blob/e4bea1954dcf234bbcc62e81bf01a9f424aad2d2/platform/core-impl/src/com/intellij/util/indexing/FileContentImpl.java#L184-L186
      LoadTextUtil.getTextByBinaryPresentation(virtualFile.readBytes(), virtualFile, false, false).toString()
    } catch (_: KotlinNoSuchFileException) {
      // In case of race condition where the file was deleted after it was enqueued for upload
      null
    } catch (_: JavaNoSuchFileException) {
      // Also handle Java's NoSuchFileException
      null
    }
  }
}
