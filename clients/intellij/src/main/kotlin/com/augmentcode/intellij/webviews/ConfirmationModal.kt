package com.augmentcode.intellij.webviews

import com.augmentcode.rpc.OpenConfirmationModalData
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.MessageDialogBuilder
import kotlinx.coroutines.CompletableDeferred

object ConfirmationModal {
  suspend fun showAndGetAsync(
    project: Project,
    data: OpenConfirmationModalData,
  ): Boolean {
    val result = CompletableDeferred<Boolean>()

    runInEdt {
      result.complete(
        MessageDialogBuilder
          .yesNo(data.title, data.message)
          .ask(project),
      )
    }
    return result.await()
  }
}
