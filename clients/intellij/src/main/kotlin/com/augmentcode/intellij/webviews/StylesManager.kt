package com.augmentcode.intellij.webviews

import com.intellij.ide.ui.UISettings
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.ui.JBColor
import com.intellij.ui.jcef.JBCefScrollbarsHelper
import io.ktor.http.*
import kotlinx.coroutines.delay
import java.awt.Color
import java.util.*
import javax.swing.UIManager
import javax.swing.plaf.FontUIResource
import kotlin.math.ceil

object StylesManager {
  val logger = thisLogger()

  suspend fun getStyles(): String {
    return getCssWithFallback() +
      JBCefScrollbarsHelper.buildScrollbarsStyle() +
      """
      :root {
          background: var(--intellij-tree-background);
          color: var(--intellij-tree-foreground);
      }
      a {
        color: var(--intellij-hyperlink-linkColor);
      }
      """.trimIndent()
  }

  // AU-3747: This is not really what you are supposed to do with a
  // ConcurrentModificationException, but since UIDefaults is not
  // thread-safe the only real way to avoid this seems to be by using
  // the EDT Thread -- however when we do that the call will block
  // until the Settings dialog is closed. Instead we'll just try twice
  // and then give up. When we give up we return some hardcoded defaults
  private suspend fun getCssWithFallback(): String {
    try {
      return getCssFromUIDefaults(UISettings.getInstance())
    } catch (e: ConcurrentModificationException) {
      // try again
      this.logger.warn("Failed to get UI defaults, retrying", e)
      delay(500) // wait 500ms before retry
      try {
        return getCssFromUIDefaults(UISettings.getInstance())
      } catch (ex: ConcurrentModificationException) {
        this.logger.warn("Failed to get UI defaults, giving hardcoded defaults", ex)
        // fall back to some hardcoded defaults
        return this::class.java.classLoader.getResource(getFallbackCssPath())!!.readText()
      }
    }
  }

  private fun getFallbackCssPath(): String {
    return if (JBColor.isBright()) {
      "webviews-fallback/augment-light.css"
    } else {
      "webviews-fallback/augment-dark.css"
    }
  }

  internal fun getCssFromUIDefaults(uiSettings: UISettings): String {
    val zoom = uiSettings.ideScale.toDouble()
    val uiValuesList =
      UIManager.getDefaults().mapNotNull { (key, value) ->
        if (key !is String) return@mapNotNull null
        when (value) {
          is Color -> listOf(transformKey(key) to value.webRgba())
          is FontUIResource ->
            listOf(
              (transformKey(key) + "-family") to fixFontName(key, value.family),
              // Since the size from UIManager is scaled already, we need to unscale it.
              (transformKey(key) + "-size") to "${ceil(value.size / zoom).toInt()}px",
            )
          else -> emptyList()
        }
      }.flatten().toMutableList()

    // All the font values from UIManager are scaled to the accessibility zoom level
    // which is an issue since we set the zoom level on the webview itself.
    // For our primary font, get the size from the UI settings, which is not scaled.
    if (uiSettings.fontSize > 0) {
      uiValuesList.add(transformKey("ui-font-size") to "${uiSettings.fontSize}px")
    }

    return uiValuesList.toMap(sortedMapOf())
      .entries.joinToString(separator = "\n") { (key, value) ->
        "  $key: $value;"
      }
      .let {
        ":root {\n${it}\n}\n"
      }
  }

  private fun fixFontName(
    key: String,
    fontName: String,
  ): String {
    if (key.contains("editorPane", true)) {
      return fontName.quote() + ", monospace"
    }
    return fontName.quote() + ", system-ui, sans-serif" // fallback
  }

  private fun transformKey(key: String): String {
    return key.split(".").joinToString(separator = "-", prefix = "--intellij-") { part ->
      if (part.startsWith("UI")) {
        part.replaceFirst("UI", "ui")
      } else {
        part.replaceFirstChar { it.lowercase() }
      }
    }
  }
}

internal fun Color.webRgba(): String {
  // note alpha channel is not a 0-255 value, but a 0-1 floating point value
  // also note we pass `US` for locale to avoid locale-specific formatting
  // (e.g. 1,0 instead of 1.0) (AU-4066)
  return "rgba($red, $green, $blue, %.2g)".format(Locale.US, alpha / 255.0)
}
