package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.chat.AugmentChatToolWindow
import com.augmentcode.intellij.history.AugmentHistoryToolWindowFactory
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.utils.AugmentToolWindowType
import com.augmentcode.intellij.utils.TOOL_WINDOW_TYPE_KEY
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.util.concurrency.annotations.RequiresEdt

class ShowHistoryAction : AnActionWithCtx() {
  internal val isEnabled: Boolean
    get() {
      return AugmentAppStateService.instance.context.flags.enableCompletionsHistory
    }

  private fun isHistoryActive(project: Project): Boolean =
    ToolWindowManager
      .getInstance(project)
      .getToolWindow(AugmentChatToolWindow.ID)
      ?.contentManager
      ?.selectedContent
      ?.getUserData(TOOL_WINDOW_TYPE_KEY) == AugmentToolWindowType.HISTORY

  @RequiresEdt
  override fun actionPerformed(e: AnActionEvent) {
    if (!isEnabled) return

    val project = e.project ?: return
    val toolWindow =
      ToolWindowManager
        .getInstance(project)
        .getToolWindow(AugmentChatToolWindow.ID) ?: return

    // Check if history tab already exists
    val existingContent =
      toolWindow.contentManager.contents.firstOrNull {
        it.getUserData(TOOL_WINDOW_TYPE_KEY) == AugmentToolWindowType.HISTORY
      }

    // If they're already on the history tab, just close it since they want to hide it
    if (isHistoryActive(project)) {
      toolWindow.contentManager.removeContent(toolWindow.contentManager.selectedContent!!, true)
      return
    }

    if (existingContent != null) {
      toolWindow.contentManager.setSelectedContent(existingContent)
      toolWindow.activate(null)
      return
    }

    AugmentHistoryToolWindowFactory().createToolWindowContent(project, toolWindow)
    val historyContent =
      toolWindow.contentManager.contents.firstOrNull {
        it.getUserData(TOOL_WINDOW_TYPE_KEY) == AugmentToolWindowType.HISTORY
      } ?: return
    toolWindow.contentManager.setSelectedContent(historyContent)
    toolWindow.activate(null)

    // When we open the history tab, set the chat tab title to "Chat" so we can distinguish it from the history tab
    toolWindow.contentManager.contents
      .firstOrNull {
        it.getUserData(TOOL_WINDOW_TYPE_KEY) == AugmentToolWindowType.CHAT
      }?.displayName = AugmentBundle.message("chat.tab.title")
    historyContent.setDisposer {
      // when we dispose of the history, remove the "Chat" tab title so we just show "Augment" as the title for chat
      toolWindow.contentManager.contents
        .firstOrNull {
          it.getUserData(TOOL_WINDOW_TYPE_KEY) == AugmentToolWindowType.CHAT
        }?.displayName = null
    }
  }

  override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.EDT

  override fun updateWithCtx(
    e: AnActionEvent,
    ctx: PluginContext,
    model: AugmentModel,
  ) {
    val project = e.project ?: return
    e.presentation.text =
      if (isHistoryActive(project)) {
        AugmentBundle.message("actions.history.hide")
      } else {
        AugmentBundle.message("actions.history.show")
      }
  }
}
