package com.augmentcode.intellij.status

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.jetbrains.annotations.VisibleForTesting

enum class StatePriority {
  HIGH,
  MEDIUM,
  LOW,
  NEUTRAL,
}

@Service(Service.Level.PROJECT)
class StateManager {
  val baseState: StateDefinition = StateDefinitions.Base

  private val currentStateMutex = Mutex()
  private var currentState: StateDefinition = this.baseState

  private val stateMutex = Mutex()
  private val state: MutableMap<StatePriority, MutableList<StateDefinition>> =
    mutableMapOf(
      StatePriority.HIGH to mutableListOf(),
      StatePriority.MEDIUM to mutableListOf(),
      StatePriority.LOW to mutableListOf(),
      StatePriority.NEUTRAL to mutableListOf(),
    )

  private suspend fun addState(s: StateDefinition) {
    stateMutex.withLock {
      state[s.priority]!!.add(s)
    }
  }

  private suspend fun removeState(s: StateDefinition) {
    stateMutex.withLock {
      state[s.priority]!!.remove(s)
    }
  }

  fun setState(s: StateDefinition): () -> Unit {
    runBlocking { addState(s) }
    this.updateState()
    var isDisposed = false
    return {
      if (!isDisposed) {
        isDisposed = true
        runBlocking { removeState(s) }
        this.updateState()
      }
    }
  }

  private suspend fun setCurrentState(s: StateDefinition): Boolean {
    currentStateMutex.withLock {
      val hasChanged = currentState != s
      currentState = s
      return hasChanged
    }
  }

  private fun updateState() {
    val newState = this.getPriorityState()
    val hasChanged =
      runBlocking {
        setCurrentState(newState)
      }
    if (hasChanged) {
      val messageBus = ApplicationManager.getApplication().messageBus
      messageBus.syncPublisher(StateChangeListener.TOPIC).onChange(currentState)
    }
  }

  private suspend fun getPriorityStateAsync(): StateDefinition? {
    stateMutex.withLock {
      for (priority in enumValues<StatePriority>()) {
        val priorities = this.state[priority]
        if (priorities != null && priorities.size > 0) {
          return priorities[priorities.size - 1]
        }
      }
    }
    return null
  }

  fun getPriorityState(): StateDefinition {
    val newState = runBlocking { getPriorityStateAsync() }
    if (newState != null) {
      return newState
    }
    return this.baseState
  }

  @VisibleForTesting
  fun reset() {
    // This method is not thread safe but is suitable for reseting in tests
    for (priority in enumValues<StatePriority>()) {
      this.state[priority] = mutableListOf()
    }
    currentState = baseState
  }

  companion object {
    fun getInstance(project: Project): StateManager {
      return project.getService(StateManager::class.java)
    }
  }
}
