package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.tools.ToolCallResponse
import com.google.protobuf.Descriptors.Descriptor
import com.google.protobuf.Struct
import com.intellij.openapi.project.Project

interface IdeTool {
  val name: String
  val description: String
  val version: Int get() = 1

  val inputMessageDescriptor: Descriptor

  suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse

  // Returns true if the call was by this tool and got cancelled
  fun cancelCall(toolUseId: String): Boolean = false
}
