package com.augmentcode.intellij.chat

import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AugmentChatProjectActivity(private val cs: CoroutineScope) : ProjectActivity {
  override suspend fun execute(project: Project) {
    // In unit tests we don't want to run this activity unless the test explicitly calls it.
    if (ApplicationManager.getApplication().isUnitTestMode) return
    onStartup(project)
  }

  internal fun onStartup(project: Project) {
    val log = thisLogger()
    log.info("Sending initial guidelines state from project activity")
    AugmentAppStateService.instance.subscribe(
      project.messageBus.connect(AugmentDisposable.getInstance(project)),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          if (context.isSignedIn) {
            log.info("Sending initial guidelines state after plugin state change")
            sendInitialGuidelinesState(project)
          }
        }
      },
      triggerOnStateChange = true,
    )
  }

  /**
   * Sends the initial guidelines state to the webview.
   */
  private fun sendInitialGuidelinesState(project: Project) {
    // Send the guidelines state to the webview
    DumbService.getInstance(project).runWhenSmart {
      cs.launch(Dispatchers.IO) {
        thisLogger().warn("Sending initial guidelines state on EDT from dumb service runWhenSmart")
        val messagingService = ChatMessagingService.getInstance(project)
        messagingService.sendGuidelinesStateToWebview()
      }
    }
  }
}
