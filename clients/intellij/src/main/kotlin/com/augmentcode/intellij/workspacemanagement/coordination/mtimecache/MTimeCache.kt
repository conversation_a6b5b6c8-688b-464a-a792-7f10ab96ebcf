package com.augmentcode.intellij.workspacemanagement.coordination.mtimecache

import com.augmentcode.intellij.idea.AugmentFileStore
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.util.io.DataExternalizer
import com.intellij.util.io.EnumeratorStringDescriptor
import com.intellij.util.io.IOUtil
import com.intellij.util.io.PersistentHashMap
import org.jetbrains.annotations.VisibleForTesting
import java.io.DataInput
import java.io.DataOutput
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.withLock
import kotlin.io.path.exists

/**
 * This class is a service since the persistent hash map expected to be opened once
 * per project.
 */
@Service(Service.Level.PROJECT)
class MTimeCache(project: Project) : Disposable {
  private val mtimeFilePath = AugmentFileStore.getSubDirectoryForProject(project, "mtime-cache").resolve("mtime-cache.dat")
  private var mtimeCacheMap: PersistentHashMap<String, MTimeCacheEntry>? = null
  private val lock = ReentrantReadWriteLock()

  companion object {
    fun getInstance(project: Project): MTimeCache {
      return project.service<MTimeCache>()
    }
  }

  init {
    try {
      mtimeCacheMap =
        PersistentHashMap(
          mtimeFilePath,
          EnumeratorStringDescriptor.INSTANCE,
          KeyExternalizer(),
        )
    } catch (e: Exception) {
      thisLogger().warn("Failed to load mtime cache: ", e)
      deleteCacheFileAndRecreate()
    }
  }

  private fun deleteCacheFileAndRecreate() {
    try {
      if (mtimeFilePath.exists()) {
        IOUtil.deleteAllFilesStartingWith(mtimeFilePath)
      }
      mtimeCacheMap =
        PersistentHashMap(
          mtimeFilePath,
          EnumeratorStringDescriptor.INSTANCE,
          KeyExternalizer(),
        )
    } catch (e: Exception) {
      thisLogger().warn("Failed to recreate mtime cache: ", e)
      mtimeCacheMap = null
    }
  }

  override fun dispose() {
    lock.writeLock().withLock {
      try {
        mtimeCacheMap?.force()
      } catch (e: Exception) {
        thisLogger().warn("Failed to flush mtime cache", e)
      } finally {
        try {
          mtimeCacheMap?.close()
        } catch (e: Exception) {
          thisLogger().warn("Failed to close mtime cache", e)
        }
      }
    }
  }

  fun get(absPath: String): MTimeCacheEntry? {
    return lock.readLock().withLock {
      mtimeCacheMap?.get(absPath)
    }
  }

  fun put(
    absPath: String,
    value: MTimeCacheEntry,
  ) {
    lock.writeLock().withLock {
      try {
        mtimeCacheMap?.put(absPath, value)
      } catch (e: Exception) {
        thisLogger().warn("Failed to write to mtime cache", e)
      }
    }
  }

  @VisibleForTesting
  internal fun reset() {
    lock.writeLock().withLock {
      mtimeCacheMap?.close()
      deleteCacheFileAndRecreate()
    }
  }
}

private class KeyExternalizer : DataExternalizer<MTimeCacheEntry> {
  override fun save(
    out: DataOutput,
    value: MTimeCacheEntry,
  ) {
    out.writeLong(value.mtime)
    out.writeLong(value.size)
    out.writeUTF(value.blobName)
  }

  override fun read(`in`: DataInput): MTimeCacheEntry {
    val mtime = `in`.readLong()
    val size = `in`.readLong()
    val blobName = `in`.readUTF()
    return MTimeCacheEntry(mtime, size, blobName)
  }
}

data class MTimeCacheEntry(
  val mtime: Long,
  val size: Long,
  val blobName: String,
)
