package com.augmentcode.intellij.auth

import com.augmentcode.intellij.utils.AugmentDisposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.util.messages.MessageBusConnection
import com.intellij.util.messages.Topic
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/*
 * This class is a wrapper around the message bus that ensures sending and receiving
 * credentials on the message bus is done in a safe way by running message bus methods
 * and callbacks from a coroutine.
 */
class CredentialsMessageBusWrapper(val cs: CoroutineScope) {
  companion object {
    @JvmStatic
    @Topic.AppLevel
    private val TOPIC: Topic<CredentialsChangeListener> = Topic(CredentialsChangeListener::class.java, Topic.BroadcastDirection.TO_CHILDREN)
  }

  fun sendCredentials(credentials: AugmentCredentials?) {
    // The message callbacks are synchronous, so any long-running work should be done
    // in a coroutine and not block this function or the thread that calls it.
    cs.launch {
      val messageBus = ApplicationManager.getApplication().messageBus
      messageBus.syncPublisher(TOPIC).onChange(credentials)
    }
  }

  fun subscribe(
    project: Project,
    listener: CredentialsChangeListener,
  ) {
    val connection = project.messageBus.connect(AugmentDisposable.getInstance(project))
    this.subscribe(connection, listener)
  }

  fun subscribe(
    connection: MessageBusConnection,
    listener: CredentialsChangeListener,
  ) {
    // Wrap the listener to ensure its callbacks are called on a coroutine
    // and cannot block other listeners from being called.
    val wrappedListener =
      object : CredentialsChangeListener {
        override fun onChange(credentials: AugmentCredentials?) {
          cs.launch {
            listener.onChange(credentials)
          }
        }
      }

    connection.subscribe(TOPIC, wrappedListener)
  }
}

interface CredentialsChangeListener {
  fun onChange(credentials: AugmentCredentials?)
}
