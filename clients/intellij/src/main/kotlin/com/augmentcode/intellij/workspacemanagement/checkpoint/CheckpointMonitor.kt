package com.augmentcode.intellij.workspacemanagement.checkpoint

import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

class CheckpointMonitor(
  private val cs: CoroutineScope,
  private val manager: CheckpointManager,
  private val logger: Logger = thisLogger(),
) : Disposable {
  companion object {
    internal const val MONITOR_JOB_INTERVAL_MS = 1000L
  }

  private var monitorJob: Job? = null
  private var previousAddedBlobsCount = 0
  private var previousDeletedBlobsCount = 0
  private var previousCheckpointId: String? = null

  init {
    startMonitorJob()
  }

  private fun startMonitorJob() {
    if (monitorJob != null) {
      logger.warn("Monitor job already running")
      return
    }

    monitorJob =
      cs.launch {
        logger.info("Starting checkpoint monitor job")
        while (isActive) {
          monitorCheckpoint()
        }
      }
  }

  private suspend fun monitorCheckpoint() {
    delay(MONITOR_JOB_INTERVAL_MS)

    val currentSnapshot = manager.currentCheckpoint()
    val currentAddedCount = currentSnapshot.addedBlobs.size
    val currentDeletedCount = currentSnapshot.deletedBlobs.size
    val currentCheckpointId = currentSnapshot.checkpointId
    val workingSetSize = currentAddedCount + currentDeletedCount

    // Skip logging if nothing has changed and working set is empty
    if (previousAddedBlobsCount == currentAddedCount &&
      previousDeletedBlobsCount == currentDeletedCount &&
      currentCheckpointId == previousCheckpointId
    ) {
      return
    }

    // Log changes in blob counts
    val addedDelta = currentAddedCount - previousAddedBlobsCount
    val deletedDelta = currentDeletedCount - previousDeletedBlobsCount

    logger.info(
      "Checkpoint status - Added: $currentAddedCount (delta: $addedDelta), " +
        "Deleted: $currentDeletedCount (delta: $deletedDelta), " +
        "Working set: $workingSetSize, " +
        "Checkpoint ID: ${currentCheckpointId ?: "none"}",
    )

    // Update previous values
    previousAddedBlobsCount = currentAddedCount
    previousDeletedBlobsCount = currentDeletedCount
    previousCheckpointId = currentCheckpointId
  }

  override fun dispose() {
    monitorJob?.cancel()
    monitorJob = null
  }
}
