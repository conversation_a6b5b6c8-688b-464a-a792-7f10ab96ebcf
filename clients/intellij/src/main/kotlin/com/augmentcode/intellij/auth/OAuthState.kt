package com.augmentcode.intellij.auth

import com.intellij.collaboration.auth.services.PkceUtils
import com.intellij.credentialStore.createSecureRandom
import java.util.*

class OAuthState {
  @Suppress("unused")
  val creationTime: Long = Date().time

  val state: String = UUID.randomUUID().toString()
  val verifier: String
  var challenge: String

  init {
    val randomGen = createSecureRandom()
    val encoder = Base64.getUrlEncoder().withoutPadding()
    val values = ByteArray(32)
    randomGen.nextBytes(values)
    verifier = encoder.encodeToString(values)
    challenge = PkceUtils.generateShaCodeChallenge(verifier, encoder)
  }
}
