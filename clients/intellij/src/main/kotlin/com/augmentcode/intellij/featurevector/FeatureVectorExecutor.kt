package com.augmentcode.intellij.featurevector

import com.augmentcode.intellij.utils.JavaScriptExecutionEngine
import com.google.gson.Gson
import org.jetbrains.annotations.VisibleForTesting
import java.io.File
import kotlin.io.path.createTempDirectory

/**
 * Executor specifically for feature vector collection using obfuscated JavaScript.
 */
object FeatureVectorExecutor {
  private val gson = Gson()

  /**
   * Executes the obfuscated JavaScript and returns the feature vector.
   */
  suspend fun execute(): Map<String, String> {
    val tempFile = extractJavaScriptFile()
    try {
      val wrapperScript: String = createWrapperScript(tempFile)
      val output = JavaScriptExecutionEngine.execute(wrapperScript)
      return parseOutput(output)
    } finally {
      tempFile.delete()
    }
  }

  private fun extractJavaScriptFile(): File {
    val resourceStream =
      javaClass.classLoader.getResourceAsStream("feature-vector-collector/feature-vector-collector.js")
        ?: throw RuntimeException("Failed to load obfuscated JavaScript resource")

    val tempDir = createTempDirectory("augment-collector")
    val tempFile = tempDir.resolve("augment-collector.js").toFile()
    tempFile.deleteOnExit()

    tempFile.outputStream().use { output ->
      resourceStream.copyTo(output)
    }

    tempFile.setExecutable(true)

    return tempFile
  }

  private fun createWrapperScript(scriptFile: File): String {
    // On windows, we have C:\Users\<USER>\... JS will treat `\` as an escape, so convert to `/` which
    // Node will accept for windows paths.
    val windowsFriendlyPath = scriptFile.absolutePath.replace("\\", "/")
    return """
      const { createFeatures } = require('$windowsFriendlyPath');

      (async () => {
        try {
          const result = await createFeatures();
          console.log(JSON.stringify(result.toVector()));
        } catch (error) {
          console.error("Error:", error);
          process.exit(1);
        }
      })();
      """.trimIndent()
  }

  @VisibleForTesting
  internal fun parseOutput(output: String): Map<String, String> {
    val jsonLine =
      output.lines().find { it.trim().startsWith("{") && it.trim().endsWith("}") }
        ?: throw RuntimeException("No JSON found in output")

    @Suppress("UNCHECKED_CAST")
    return gson.fromJson(jsonLine, Map::class.java) as Map<String, String>
  }
}
