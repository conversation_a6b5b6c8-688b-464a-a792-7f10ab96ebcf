package com.augmentcode.intellij.actions

import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.webviews.settings.AugmentSettingsWebviewService
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware

class OpenSettingsWebviewAction : AnActionWithCtx(), DumbAware {
  override fun updateWithCtx(
    e: AnActionEvent,
    ctx: PluginContext,
    model: AugmentModel,
  ) {
    e.presentation.isEnabledAndVisible = ctx.flags.agentModeEnabled
  }

  override fun getActionUpdateThread() = ActionUpdateThread.BGT

  override fun actionPerformed(e: AnActionEvent) {
    val project = e.project ?: return
    AugmentSettingsWebviewService.getInstance(project).openSettingsWebview()
  }
}
