package com.augmentcode.intellij.featurevector

import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.jetbrains.annotations.VisibleForTesting
import kotlin.coroutines.cancellation.CancellationException

/**
 * Service that coordinates periodic data collection and reporting for a project.
 */
@Service(Service.Level.APP)
class FeatureVectorService(
  private val cs: CoroutineScope,
) : Disposable {
  companion object {
    private val logger = thisLogger()

    // Collection interval - collect data every 15 minutes
    @VisibleForTesting
    internal const val COLLECTION_INTERVAL_MS = 15 * 60 * 1000L

    fun getInstance(): FeatureVectorService {
      return ApplicationManager.getApplication().getService(FeatureVectorService::class.java)
    }
  }

  private var collectionJob: Job? = null

  init {
    // Moogi - commenting out feature vector service until we resolve a windows issue.
//    if (!ApplicationManager.getApplication().isUnitTestMode) {
//      // Subscribe to plugin state changes to start/stop collection based on sign-in status
//      AugmentAppStateService.instance.subscribe(
//        ApplicationManager.getApplication().messageBus.connect(this),
//        object : PluginStateListener {
//          override fun onStateChange(
//            context: PluginContext,
//            state: PluginState,
//          ) {
//            if (context.isSignedIn) {
//              startCollection()
//            } else {
//              stopCollection()
//            }
//          }
//        },
//        triggerOnStateChange = true,
//      )
//    }
  }

  /**
   * Starts periodic data collection.
   * This method is safe to call multiple times.
   */
  fun startCollection() {
    if (collectionJob?.isActive == true) {
      return
    }

    // Start the periodic collection coroutine
    collectionJob =
      cs.launch {
        while (isActive) {
          try {
            collect()
          } catch (e: CancellationException) {
            throw e
          } catch (e: Exception) {
            logger.warn("Error in collection loop", e)
            // Continue the loop even if one collection fails
          } finally {
            delay(COLLECTION_INTERVAL_MS)
          }
        }
      }
  }

  /**
   * Stops data collection.
   */
  fun stopCollection() {
    collectionJob?.cancel()
    collectionJob = null
  }

  /**
   * Collects and reports data.
   * This method can be called manually for testing or immediate collection.
   */
  suspend fun collect() {
    val result = FeatureVectorExecutor.execute()
    uploadData(result)
  }

  /**
   * Uploads data to the API.
   */
  private suspend fun uploadData(data: Map<String, String>) {
    AugmentAPI.instance.logFeatureVector(data)
  }

  @VisibleForTesting
  fun isCollectionEnabled(): Boolean {
    return collectionJob?.isActive == true
  }

  /**
   * Called when the service is disposed (project closed).
   * Handles cleanup of collection.
   */
  override fun dispose() {
    stopCollection()
  }
}
