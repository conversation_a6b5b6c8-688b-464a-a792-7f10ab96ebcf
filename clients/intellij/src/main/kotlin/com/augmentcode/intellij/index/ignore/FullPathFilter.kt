package com.augmentcode.intellij.index.ignore

import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import org.jetbrains.annotations.VisibleForTesting

/**
 * Manages hierarchical ignore rules for a directory tree.
 * Determines if files should be accepted or ignored based on .gitignore and .augmentignore rules.
 */
class FullPathFilter(private val root: VirtualFile, private val ignoreStacks: Map<String, MultiIgnoreStack>) {
  suspend fun isAccepted(file: VirtualFile): Boolean? {
    val stack = getStack(file) ?: return false
    val accepted: Boolean? = stack.isAccepted(file)
    if (accepted != null) {
      return accepted
    }
    return null
  }

  @VisibleForTesting
  fun getStacks(): Map<String, MultiIgnoreStack> {
    return ignoreStacks
  }

  private fun getStack(file: VirtualFile): MultiIgnoreStack? {
    var path: VirtualFile? = file.parent
    while (path != null) {
      val relPath = VfsUtil.getRelativePath(path, root) ?: return null
      val stack = ignoreStacks[relPath]
      if (stack != null) {
        return stack
      }
      path = path.parent
    }
    return null
  }
}
