package com.augmentcode.intellij.actions

import com.augmentcode.intellij.settings.AugmentSettingsConfigurable
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.DumbAware

class ShowSettingsAction : AnAction(), DumbAware {
  override fun actionPerformed(e: AnActionEvent) {
    ShowSettingsUtil.getInstance().showSettingsDialog(null, AugmentSettingsConfigurable::class.java)
  }
}
