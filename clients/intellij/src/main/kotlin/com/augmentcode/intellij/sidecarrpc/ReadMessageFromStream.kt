package com.augmentcode.intellij.sidecarrpc

import com.intellij.openapi.diagnostic.Logger
import java.io.BufferedInputStream

private val logger = Logger.getInstance("com.augmentcode.intellij.sidecarrpc.ReadInputStream")

// Toggle if logs are too noisy
private const val DEBUG_LOGGING_ENABLED = false

private fun logDebug(message: String) {
  if (DEBUG_LOGGING_ENABLED) {
    logger.info(message)
  }
}

fun readMessageFromStream(bufferedInput: BufferedInputStream): String? {
  // Read the Content-Length header line
  val headerBytes = mutableListOf<Byte>()
  var b: Int
  // Read until we hit the first \r\n
  while (true) {
    // Read a single byte until we find a \r
    b = bufferedInput.read()
    if (b == -1) return null // End of stream
    if (b == '\r'.code) {
      b = bufferedInput.read()
      if (b == '\n'.code) break
      headerBytes.add('\r'.code.toByte())
      if (b == -1) return null
      headerBytes.add(b.toByte())
    } else {
      headerBytes.add(b.toByte())
    }
  }

  val headerString = String(headerBytes.toByteArray(), Charsets.UTF_8)
  if (!headerString.contains("Content-Length: ")) {
    logger.warn("Unknown sidecar message start: $headerString")
    return null
  }
  logDebug("Header: $headerString")

  val lastContentLengthIndex = headerString.lastIndexOf("Content-Length: ")
  val contentLength = headerString.substring(lastContentLengthIndex + "Content-Length: ".length).trim().toInt()
  logDebug("Content-Length: $contentLength")

  // Skip the second \r\n
  b = bufferedInput.read()
  if (b != '\r'.code) return null
  b = bufferedInput.read()
  if (b != '\n'.code) return null

  logDebug("readLine: <empty line>")

  return readBytesFromStream(bufferedInput, contentLength)
}

fun readBytesFromStream(
  bufferedInput: BufferedInputStream,
  contentLength: Int,
): String? {
  val contentBytes = ByteArray(contentLength)
  logDebug("Reading $contentLength bytes from sidecar...")
  var count = 0
  while (count < contentLength) {
    logDebug("Reading the next set of bytes from sidecar (${contentLength - count} left)...")
    val read = bufferedInput.read(contentBytes, count, contentLength - count)
    logDebug("Read $read bytes")
    if (read == -1) {
      logger.warn("Failed to read from sidecar: $read")
      break
    }
    count += read
  }
  return String(contentBytes, Charsets.UTF_8)
}
