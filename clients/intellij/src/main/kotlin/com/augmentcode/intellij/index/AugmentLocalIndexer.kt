package com.augmentcode.intellij.index

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.index.utils.isLegacyIndexingDisabled
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.utils.IndexUtil.expectedBlobName
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.indexing.DataIndexer
import com.intellij.util.indexing.FileContent
import kotlinx.coroutines.runBlocking
import java.time.Duration

class AugmentLocalIndexer : DataIndexer<AugmentBlobState, Void, FileContent> {
  companion object {
    private val RECENCY_DURATION = Duration.ofMinutes(10)
    private val logger = thisLogger()
  }

  /**
   * Called by IntelliJ during/after its own indexing process to index files for Augment.
   *
   * IMPORTANT: This method runs under a read lock and must complete quickly to avoid blocking the IDE.
   * The read lock is held while IntelliJ processes a potentially large batch of files, making
   * performance critical. A slow implementation will block the IDE for the entire batch.
   *
   * Performance guidelines:
   * - Operations with uncertain duration (network calls, heavy computations) must be moved outside this method
   * - If a potentially long-running task cannot be moved, it must be executed asynchronously
   *
   * NOTE: This method is currently being refactored to better adhere to these performance requirements.
   *
   * @param inputData The file content to be indexed
   * @return Map of blob states, or empty map if indexing is not possible/needed
   */
  override fun map(inputData: FileContent): Map<AugmentBlobState, Void?> {
    // Check if legacy indexing is disabled
    if (isLegacyIndexingDisabled()) {
      logger.debug("Legacy indexing is disabled, skipping file: ${inputData.file.path}")
      return emptyMap()
    }

    if (
      inputData.file.isDirectory ||
      !AugmentAPI.instance.availableBlocking() ||
      inputData.project == null
    ) {
      return emptyMap()
    }

    val project = inputData.project
    val rootPathInfo = AugmentRoot.findRelativePathWithRoot(project, inputData.file) ?: return emptyMap()
    rootPathInfo.relativePath ?: return emptyMap()
    val expectedBlobName = expectedBlobName(rootPathInfo.relativePath, inputData.contentAsText.toString())

    // force re-upload when backend informed us about missing blobs through eviction
    val forceUpload =
      inputData.file.getUserData(AugmentBlobStateReader.FORCE_UPLOAD)?.also {
        // to clear the flag
        inputData.file.putUserData(AugmentBlobStateReader.FORCE_UPLOAD, null)
      } ?: false

    val previousState = getPreviousState(inputData.project, inputData.file, forceUpload, rootPathInfo.relativePath)

    // use selected editor as it should be the same as focused editor but works in tests
    val fileEditors = FileEditorManager.getInstance(project).getEditors(inputData.file)
    val fileIsBeingEdited = fileEditors.isNotEmpty()
    val recentlySynced =
      previousState != null &&
        previousState.remoteSyncTimestamp > System.currentTimeMillis() - RECENCY_DURATION.toMillis()

    if (fileIsBeingEdited && recentlySynced && previousState != null) {
      return mapOf(previousState.copy(localName = expectedBlobName) to null)
    }

    for (editor in fileEditors) {
      AugmentEditorHistoryService.getInstance(project)
        .memorizeContent(editor, expectedBlobName, inputData.contentAsText.toString())
    }

    val syncedRemoteState =
      runBlocking {
        AugmentRemoteSyncingManager.getInstance(project).notifyContentChanged(
          rootPathInfo.rootPath,
          rootPathInfo.relativePath,
          expectedBlobName,
          inputData.file,
        )
      }

    return mapOf(syncedRemoteState to null)
  }

  private fun getPreviousState(
    project: Project,
    file: VirtualFile,
    forceUpload: Boolean,
    relativePath: String,
  ): AugmentBlobState? {
    // TODO(diehuxx): Calling AugmentBlobStateReader.read while indexing can throw a Reentrant Indexing exception
    //                We shouldn't be reading the indexes during indexing. We should find another way to decide whether
    //                the file was uploaded recently enough that we shouldn't reupload it.
    var previousState: AugmentBlobState? = null
    if (!forceUpload) {
      try {
        previousState = AugmentBlobStateReader.read(project, file)
      } catch (e: Throwable) {
        thisLogger().warn("Failed to read previous state", e)
        // Catch any exceptions that might occur during index reading, including reentrant indexing
        // Just continue with previousState as null, which will trigger a new upload
      }
    }

    if (previousState?.relativePath != relativePath) {
      // file was moved or a new .augmentroot was created
      // treat it as a new file without a state
      previousState = null
    }

    return previousState
  }
}
