package com.augmentcode.intellij.utils

import com.intellij.openapi.Disposable
import com.intellij.openapi.application.*
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import org.jetbrains.annotations.NotNull

/**
 * The service is intended to be used instead of a project/application as a parent disposable.
 * refer to https://plugins.jetbrains.com/docs/intellij/disposers.html?from=IncorrectParentDisposable#choosing-a-disposable-parent
 * and https://github.com/JetBrains/intellij-community/blob/idea/241.18034.62/python/openapi/src/com/jetbrains/python/PythonPluginDisposable.java
 */
@Service(*[Service.Level.APP, Service.Level.PROJECT])
class AugmentDisposable : Disposable {
  override fun dispose() {
  }

  companion object {
    @get:NotNull
    val instance: Disposable
      get() =
        ApplicationManager.getApplication().getService(
          AugmentDisposable::class.java,
        )

    @NotNull
    fun getInstance(
      @NotNull project: Project,
    ): Disposable = project.getService(AugmentDisposable::class.java)
  }
}
