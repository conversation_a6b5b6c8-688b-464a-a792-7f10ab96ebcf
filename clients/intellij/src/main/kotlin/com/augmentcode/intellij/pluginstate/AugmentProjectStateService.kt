package com.augmentcode.intellij.pluginstate

import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.util.messages.MessageBusConnection
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Service(Service.Level.PROJECT)
class AugmentProjectStateService(val project: Project, val cs: CoroutineScope) : Disposable {
  companion object {
    fun getInstance(project: Project): AugmentProjectStateService {
      return project.service<AugmentProjectStateService>()
    }

    private val logger = thisLogger()
  }

  // Message bus for plugin state changes
  private val messageBus = PluginStateMessageBus(PluginStateMessageBus.PROJECT_TOPIC, cs)

  // The state updates are performed by an executor to ensure it's only running one instance at a time
  internal val stateExecutor =
    ProjectStateExecutor(
      project,
      cs,
      this,
      onStateChange = { ctx, state ->
        handleStateChange(ctx, state)
      },
    )

  // The current context and state of the plugin
  var context: PluginContext = AugmentAppStateService.instance.context
    private set
  var state: PluginState = PluginState.UNINITIALIZED
    private set

  init {
    AugmentAppStateService.instance.subscribe(
      project.messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          logger.info("App state changed to $state")
          stateExecutor.updateState()
        }
      },
      triggerOnStateChange = true,
    )
  }

  override fun dispose() {
    // No-op
  }

  // Helper methods to subscribe to plugin state changes
  fun subscribe(
    connection: MessageBusConnection,
    listener: PluginStateListener,
    triggerOnStateChange: Boolean = false,
  ) {
    messageBus.subscribe(connection, listener)
    // triggerOnStateChange ensures the listener is called when the subscription is added
    // ensure it gets the current state + context
    if (triggerOnStateChange) {
      cs.launch {
        listener.onStateChange(context, state)
      }
    }
  }

  private fun handleStateChange(
    newCtx: PluginContext,
    newState: PluginState,
  ) {
    if (newState == state && newCtx == context) {
      logger.debug("New plugin state and context are the same, not emitting change")
      return
    }

    context = newCtx
    state = newState

    logger.info("Emitting project state: $state")
    messageBus.emitState(context, state)
  }
}
