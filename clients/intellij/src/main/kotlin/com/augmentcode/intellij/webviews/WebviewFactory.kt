package com.augmentcode.intellij.webviews

import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.sentry.SentryService
import com.augmentcode.intellij.utils.propOrFlagBool
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.SystemInfo
import com.intellij.ui.jcef.JBCefApp
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.JBCefClient
import org.cef.CefSettings
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.cef.handler.*
import org.cef.misc.BoolRef
import org.cef.network.CefRequest
import kotlin.text.Regex.Companion.escapeReplacement

object WebviewFactory {
  val WEBVIEW_SCHEME = "http"
  val OSR_PROPERTY_NAME = "augment.off.screen.rendering"

  /*
    "*.localhost" part is important to enable Secure Context which provides crypto.randomUUID() function

    See https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts
    And https://developer.mozilla.org/en-US/docs/Web/API/Crypto/randomUUID
   */
  val WEBVIEW_DOMAIN = "augment.localhost"
  val WEBVIEW_BASE_URL = "${WEBVIEW_SCHEME}://${WEBVIEW_DOMAIN}/"

  // How many JS queries we can execute at a time. When this number is too small
  // then we can't handle a flood of messages from the webview. When that happens
  // then calls start failing and triggering exceptions. AU-3902
  // We should revisit this number as we don't really know what a "too large" number is.
  private const val WEBVIEW_JS_POOL_SIZE = 20

  // How many postMessage call backs we can execute at a time.
  private const val WEBVIEW_CALLBACK_PARALLELISM = 4

  /**
   * Determines whether off-screen rendering should be enabled by default.
   *
   * If the system property is explicitly set, that takes precedence.
   * Otherwise, defaults to true for Linux and false for other platforms.
   */
  internal fun shouldEnableOffScreenRenderingByDefault(): Boolean {
    return propOrFlagBool(OSR_PROPERTY_NAME, fallbackFlag = SystemInfo.isLinux)
  }

  fun create(
    project: Project,
    path: String,
    stateKey: AugmentWebviewStateKey,
    messagingService: AugmentMessagingService,
    parentDisposable: Disposable,
    onLoad: ((browser: CefBrowser, frame: CefFrame, httpStatusCode: Int) -> Unit)? = null,
  ): AugmentWebview {
    // Ensure this is set for the current run time before we create our webviews
    val jbCefClient = JBCefApp.getInstance().createClient()
    jbCefClient.setProperty(JBCefClient.Properties.JS_QUERY_POOL_SIZE, WEBVIEW_JS_POOL_SIZE)

    val jbCefBrowser =
      JBCefBrowser.createBuilder()
        .setClient(jbCefClient)
        // backspace doesn't always works with offscreen rendering
        // https://magpcss.org/ceforum/viewtopic.php?f=6&t=11874
        // so let's disable it by default with an option to enable it manually
        // However, for Linux users, we enable it by default as it improves stability
        .setOffScreenRendering(shouldEnableOffScreenRenderingByDefault())
        .setEnableOpenDevToolsMenuItem(true)
        .build()
    Disposer.register(parentDisposable, jbCefBrowser)

    val rpcAdapter = RPCAdapter(path, project, jbCefBrowser, WEBVIEW_CALLBACK_PARALLELISM, stateKey, messagingService)
    Disposer.register(jbCefBrowser, rpcAdapter)

    jbCefClient.addRequestHandler(
      object : CefRequestHandlerAdapter() {
        override fun onBeforeBrowse(
          browser: CefBrowser,
          frame: CefFrame,
          request: CefRequest,
          userGesture: Boolean,
          isRedirect: Boolean,
        ): Boolean {
          // URL's clicked in the webview are `userGesture: true`, but middle clicks are `userGesture: false`
          // so rely on the request URL to determine if it should be loaded in the webview or not.
          if (request.url.startsWith(WEBVIEW_BASE_URL)) {
            return false
          }

          // Open in external browser
          BrowserUtil.open(request.url)
          return true
        }

        override fun getResourceRequestHandler(
          browser: CefBrowser,
          frame: CefFrame?,
          request: CefRequest?,
          isNavigation: Boolean,
          isDownload: Boolean,
          requestInitiator: String?,
          disableDefaultHandling: BoolRef?,
        ): CefResourceRequestHandler? {
          if (request?.url?.startsWith(WEBVIEW_BASE_URL) == false) {
            // don't handle requests that are not for our webview
            return null
          }
          return object : CefResourceRequestHandlerAdapter() {
            override fun getResourceHandler(
              browser: CefBrowser?,
              frame: CefFrame?,
              request: CefRequest?,
            ): CefResourceHandler {
              return object : CustomResourceHandler() {
                override fun contentFilter(
                  input: ByteArray,
                  mimeType: String,
                  requestUrl: String,
                ): ByteArray {
                  return if (mimeType == "text/html" && requestUrl == WEBVIEW_BASE_URL + path) {
                    val inputStr = String(input, Charsets.UTF_8)
                    /**
                     * note we are using a global variable `window.augmentFlags` to enable performance monitoring.
                     * because we would want to start monitoring as soon as the webview is loaded.
                     * And we do not to wait till the svelte app finishes loading.
                     * this pattern should not be used elsewhere.
                     */
                    inputStr.replace(
                      Regex("<head>", RegexOption.IGNORE_CASE),
                      """
                      <head>
                        <link rel=\"stylesheet\" href=\"${AugmentWebview.INTELLIJ_STYLES_CSS}\" />
                        <script type="text/javascript">
                          ${escapeReplacement(rpcAdapter.getHostEnvironmentJS())}
                        </script>
                        <script type="text/javascript">
                          window.augmentFlags = {
                            enablePerformanceMonitoring: ${AugmentAppStateService.instance.context.flags.enableWebviewPerformanceMonitoring},
                            sentry: ${service<SentryService>().createSentryConfigForWebview()}
                          };
                        </script>
                      """.trimIndent(),
                    ).toByteArray()
                  } else {
                    super.contentFilter(input, mimeType, requestUrl)
                  }
                }
              }
            }
          }
        }
      },
      jbCefBrowser.cefBrowser,
    )

    // Chat webview has its own handler for image drag and drop
    if (stateKey != AugmentWebviewStateKey.CHAT_STATE) {
      jbCefClient.addDragHandler({ _, _, _ -> true }, jbCefBrowser.cefBrowser)
    }

    val augmentWebview = AugmentWebview(project, jbCefBrowser, rpcAdapter)

    val loadHandler =
      object : CefLoadHandlerAdapter() {
        override fun onLoadEnd(
          browser: CefBrowser,
          frame: CefFrame,
          httpStatusCode: Int,
        ) {
          // We need to update the zoom level after the page has loaded, otherwise the zoom level doesn't
          // set when IntelliJ is first started.
          augmentWebview.updateZoomLevel()

          // Call the additional load handler if provided
          onLoad?.invoke(browser, frame, httpStatusCode)
        }
      }
    val consoleHandler =
      object : CefDisplayHandlerAdapter() {
        override fun onConsoleMessage(
          browser: CefBrowser,
          level: CefSettings.LogSeverity,
          message: String,
          source: String,
          line: Int,
        ): Boolean {
          val logMessage = "Webview[$stateKey][${level.name}]: $message [$source:$line]"
          // NOTE: Error is piped to warning to avoid the exception pop-up intellij shows
          // when thisLogger().error() is called.
          when (level) {
            CefSettings.LogSeverity.LOGSEVERITY_ERROR,
            CefSettings.LogSeverity.LOGSEVERITY_WARNING,
            -> thisLogger().warn(logMessage)
            CefSettings.LogSeverity.LOGSEVERITY_VERBOSE -> thisLogger().debug(logMessage)
            else -> thisLogger().info(logMessage)
          }

          return false
        }
      }
    jbCefClient.addDisplayHandler(consoleHandler, augmentWebview.browser.cefBrowser)
    jbCefClient.addLoadHandler(loadHandler, augmentWebview.browser.cefBrowser)

    jbCefBrowser.loadURL(WEBVIEW_BASE_URL + path)

    return augmentWebview
  }
}
