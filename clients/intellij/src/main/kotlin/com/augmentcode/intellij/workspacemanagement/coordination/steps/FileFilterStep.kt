package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.index.AugmentPathWithRoot
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.utils.SymlinkUtil
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCache
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.yield
import org.jetbrains.annotations.VisibleForTesting
import kotlin.coroutines.cancellation.CancellationException

class FileFilterStep(
  private val project: Project,
  private val scope: CoroutineScope,
  private val inputChannel: RoughlySizedChannel<String>,
  private val memorizeChannel: RoughlySizedChannel<VirtualFile>,
  private val outputChannel: RoughlySizedChannel<CoordinationFileDetails>,
) : Disposable, BaseProcessingStep("FileFilterStep") {
  companion object {
    const val RECENT_AND_OPEN_FILE_DEBOUNCE_MS = 10 * 60 * 1000L // 10 minutes
  }

  private val logger = thisLogger()

  override fun createProcessingJob(): Job {
    return scope.launch {
      logger.info("Starting file filtering job")
      while (isActive) {
        try {
          processFilesFromChannel()
        } catch (e: CancellationException) {
          throw e
        } catch (e: Exception) {
          logger.warn("Failed to process file filtering channel", e)
        }
        // Allow the dispatcher to execute another coroutine
        yield()
      }
    }
  }

  private suspend fun processFilesFromChannel() {
    val uri = inputChannel.receive()
    logger.trace("Received file for processing: $uri")

    // If we are not signed in, reject the files
    val context = AugmentAppStateService.instance.context
    if (!context.isSignedIn || context.model == null) {
      logger.warn("Not signed in, ignoring file: $uri")
      return
    }

    // TODO: Handle getting document

    // Need to handle null virtualFile because it is null during project startup.
    // (It's unclear whether this is the right thing.)
    val virtualFile = VirtualFileManager.getInstance().findFileByUrl(uri)
    if (virtualFile == null) {
      logger.info("Failed to find virtual file for path: $uri")
      return
    }

    val pathInfo = validateAndGetPathWithRoot(context.flags, context.model, virtualFile)
    if (pathInfo == null || pathInfo.relativePath == null || pathInfo.rootFile == null) {
      return
    }

    memorizeChannel.send(virtualFile)
    outputChannel.send(CoordinationFileDetails(virtualFile, pathInfo.rootPath, pathInfo.relativePath))
  }

  @VisibleForTesting
  internal suspend fun validateAndGetPathWithRoot(
    flags: FeatureFlags,
    model: AugmentModel,
    virtualFile: VirtualFile,
  ): AugmentPathWithRoot? {
    // If the file is a symlink, do not accept the file
    // The `canonicalPath` is the actual file path of the file and `path` is the path of the symlink.
    if (SymlinkUtil.isSymlink(virtualFile)) {
      logger.trace("File rejected by filter because it's a symlink: ${virtualFile.path}")
      return null
    }

    if (virtualFile.fileType.isBinary) {
      logger.trace("File rejected due to binary file type: ${virtualFile.path}")
      return null
    }

    // Check file against model info checks.
    if (!isSupportedFileSize(flags, virtualFile)) {
      logger.trace("File rejected due to size exceeding limit: ${virtualFile.path}")
      return null
    }

    if (!isSupportedFileExtension(flags, model, virtualFile.extension)) {
      logger.trace("File rejected due to unsupported file extension: ${virtualFile.path}")
      return null
    }

    if (!PathFilterService.getInstance(project).isAccepted(virtualFile)) {
      logger.trace("File rejected due to gitignore rules: ${virtualFile.path}")
      return null
    }

    val pathInfo = AugmentRoot.findRelativePathWithRoot(project, virtualFile)
    if (pathInfo == null) {
      logger.trace("File rejected because it's not in a project root: ${virtualFile.path}")
      return null
    }

    val skipIfOpenAndRecentlySynced = skipIfOpenAndRecentlySynced(virtualFile)
    if (skipIfOpenAndRecentlySynced) {
      logger.trace("File rejected because it's open and recently synced: ${virtualFile.path}")
      return null
    }

    logger.trace("Accepted file: ${virtualFile.path}")
    return pathInfo
  }

  // filtration based on information from the API
  private fun isSupportedFileSize(
    flags: FeatureFlags,
    file: VirtualFile,
  ): Boolean {
    return file.length <= flags.maxUploadSizeBytes
  }

  @VisibleForTesting
  internal fun isSupportedFileExtension(
    flags: FeatureFlags,
    model: AugmentModel,
    extension: String?,
  ): Boolean {
    if (flags.bypassLanguageFilter) {
      return true
    }

    if (extension == null) {
      return false
    }

    return model.supportedFileExtensions.contains(extension)
  }

  private fun skipIfOpenAndRecentlySynced(virtualFile: VirtualFile): Boolean {
    // Since indexing is triggered on each keystroke, it's worth debouncing sync for files
    // that are currently open in the editor. The content of these files is captured in the
    // AugmentEditorHistoryService. On completion request, we calculate the difference
    // between the version of the file that the server has last seen, and the version that is
    // currently open in the editor. This is known as RecencyInfo

    // TODO: support FORCE_UPLOAD flag, which is enabled under certain conditions in AugmentCompletionSuggestion,
    // but i don't understand the implications of that yet.

    // If file isn't open in editor, we should sync it immediately
    val fileEditors = FileEditorManager.getInstance(project).getEditors(virtualFile)
    if (fileEditors.isEmpty()) {
      // No editors found for file, so it's not open and should be synced immediately
      return false
    }

    // If we've never seen file before, we should sync it immediately
    // We use mtime cache instead of BlobNameService because we may have just triggered
    // sync for this file, and it may not be in BlobNameService yet.
    val mTimeCache = MTimeCache.getInstance(project)
    val mtimeEntry = mTimeCache.get(virtualFile.path)
    if (mtimeEntry == null) {
      return false
    }

    // if mtime was more than 10 minutes ago, we should sync it immediately
    val timeSinceLastSync = System.currentTimeMillis() - mtimeEntry.mtime
    if (timeSinceLastSync > RECENT_AND_OPEN_FILE_DEBOUNCE_MS) {
      return false
    }

    return true
  }
}
