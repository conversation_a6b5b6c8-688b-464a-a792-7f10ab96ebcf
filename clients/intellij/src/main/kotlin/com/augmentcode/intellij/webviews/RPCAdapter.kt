package com.augmentcode.intellij.webviews

import com.augmentcode.intellij.featureflags.DebugFeatures
import com.augmentcode.intellij.sentry.SentryService
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.JBCefBrowserBase
import com.intellij.ui.jcef.JBCefJSQuery
import com.intellij.ui.jcef.executeJavaScript
import com.intellij.util.IncorrectOperationException
import io.sentry.Sentry
import io.sentry.SentryLevel
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import java.util.*
import kotlin.coroutines.cancellation.CancellationException
import kotlin.math.max

class RPCAdapter(
  private val name: String,
  private val project: Project,
  private val jbBrowser: JBCefBrowser,
  // how many JS queries we can execute at a time. Just some not too big number greater than 2.
  callbackHandlingParallelism: Int,
  private val stateKey: AugmentWebviewStateKey,
  messagingService: AugmentMessagingService,
) : Disposable {
  // this is called when the webview calls `augment.postMessage()` in JS
  private val receiveMessageFromWebview = JBCefJSQuery.create(jbBrowser as JBCefBrowserBase)

  private val setState = JBCefJSQuery.create(jbBrowser as JBCefBrowserBase)
  private val getState = JBCefJSQuery.create(jbBrowser as JBCefBrowserBase)

  private val jsExecutionSeparator = Semaphore(max(1, callbackHandlingParallelism))

  private val logger = thisLogger()

  init {
    receiveMessageFromWebview.addHandler { msg ->
      messagingService.processMessageFromWebview(msg) {
        postMessage(it)
      }
      null
    }
    setState.addHandler {
      AugmentWebviewStateStore.getInstance(project).set(stateKey, it)
      null
    }
    getState.addHandler {
      JBCefJSQuery.Response(getStoredState(project, stateKey))
    }

    messagingService.registerRPCAdapter(this)
    Disposer.register(this) {
      messagingService.unregisterRPCAdapter(this)
    }
  }

  companion object {
    /**
     * Gets the state as json sting, decoding from base64 if necessary.
     */
    fun getStoredState(
      project: Project,
      stateKey: AugmentWebviewStateKey,
    ): String {
      return try {
        AugmentWebviewStateStore.getInstance(project).get(stateKey).let {
          when {
            it == null -> ""
            it.startsWith("{") -> it // already json
            else -> String(Base64.getDecoder().decode(it)) // base64 encoded json for backward compatibility
          }
        }
      } catch (e: IllegalArgumentException) {
        thisLogger().warn("failed to decode state", e)
        ""
      }
    }
  }

  suspend fun postMessage(message: String) {
    try {
      executeJavaScript("postMessage($message)")
    } catch (e: RuntimeException) {
      if (e is CancellationException) throw e // Preserve cancellation

      // This can happen if the jcef browser is not initialized yet.
      logger.warn("[$name] Failed to post message: $message", e)

      val sentry = SentryService.instance
      Sentry.withScope { scope ->
        scope.setTag("webview_name", name)
        scope.setTag("webview_message", message)
        scope.level = SentryLevel.ERROR
        sentry.captureException(e)
      }
    }
  }

  suspend fun executeJavaScript(javaScript: String) {
    if (jbBrowser.isDisposed) {
      logger.debug("[$name] Browser is disposed, skipping JavaScript execution: $javaScript")
      return
    }

    logger.debug("[$name] Executing JavaScript: $javaScript")
    jsExecutionSeparator.withPermit {
      try {
        jbBrowser.executeJavaScript(javaScript, 0)
      } catch (e: IncorrectOperationException) {
        if (jbBrowser.isDisposed) {
          // This error can occur if the browser is disposed while we're trying to execute JavaScript.
          logger.debug("[$name] Browser was disposed during JavaScript execution: $javaScript")
        } else {
          logger.warn("[$name] Failed to execute JavaScript: $javaScript", e)
        }
      }
    }
  }

  fun getHostEnvironmentJS(): String {
    val receivingMessageLoggerJS =
      if (!DebugFeatures.enabled()) {
        ""
      } else {
        """
        window.addEventListener("message", (event) => {
          console.debug(`Receiving message from extension [${'$'}{event?.data?.baseMsg?.type || event?.data?.type}]`, JSON.stringify(event.data, ["requestId", "type", "error", "baseMsg"]), event.data);
        });
        """.trimIndent()
      }

    val sendingMessageLogLineJS =
      if (!DebugFeatures.enabled()) {
        ""
      } else {
        """
        console.debug(`Sending message to extension [${'$'}{message?.baseMsg?.type || message?.type}]`, JSON.stringify(message, ["requestId", "type", "error", "baseMsg"]), message);
        """.trimIndent()
      }

    // This code is a little bit tricky. We will be injecting this into the <head> of the document
    // so that it is ready before any further JS runs. Our webviews expect getState and setState
    // to work synchronously because that's how the vscode API works. So to mimic that we'll
    // just cache the state locally in the JS environment and load it into the webview on startup.
    val injectStatePromiseBody =
      getState.inject(
        null,
        "function (response) { resolveState(response); }",
        "function(error_code, error_message) { rejectState(error_message); }",
      )
    return """
      window.augment = window.augment || {};

      let lastState = null;

      window.augment_intellij = window.augment_intellij || {};

      // Load the state from the host environment and start the app
      window.augment_intellij.initializationPromise = new Promise((resolveState, rejectState) => {
        $injectStatePromiseBody
      }).then(stateJsonString => {
        lastState = JSON.parse(stateJsonString)
      }).catch ((e) => {
        console.error("Failed to restore state", e);
      })

      $receivingMessageLoggerJS

      window.augment_intellij.postMessage = (message) => {
        $sendingMessageLogLineJS
        let jsonMsg = JSON.stringify(message);
        ${receiveMessageFromWebview.inject("jsonMsg")}
      };
      window.augment_intellij.setState = (state) => {
        lastState = state;
        let jsonState = JSON.stringify(state);
        ${setState.inject("jsonState")}
      };

      window.augment_intellij.getState = () => {
        return lastState
      };
      window.document.addEventListener("DOMContentLoaded", () => {
        ${AugmentWebview.augmentThemeAttributesJS()}
      });
      """.trimIndent()
  }

  override fun dispose() {
    Disposer.dispose(setState)
    Disposer.dispose(getState)
    Disposer.dispose(receiveMessageFromWebview)
  }
}
