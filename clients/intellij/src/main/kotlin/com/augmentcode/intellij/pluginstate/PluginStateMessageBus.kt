package com.augmentcode.intellij.pluginstate

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.messages.MessageBusConnection
import com.intellij.util.messages.Topic
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * This class is a wrapper around the message bus that ensures sending and receiving
 * plugin state on the message bus is done in a safe way by calling subscribers from a coroutine.
 *
 * This method should never be instantiated outside the AugmentAppStateService.
 */
internal class PluginStateMessageBus(val topic: Topic<PluginStateListener>, val cs: CoroutineScope) {
  companion object {
    @JvmStatic
    @Topic.AppLevel
    internal val APP_TOPIC: Topic<PluginStateListener> = Topic(PluginStateListener::class.java, Topic.BroadcastDirection.TO_CHILDREN)

    @JvmStatic
    @Topic.ProjectLevel
    internal val PROJECT_TOPIC: Topic<PluginStateListener> = Topic(PluginStateListener::class.java, Topic.BroadcastDirection.TO_CHILDREN)

    private val logger = thisLogger()
  }

  // Message bus for plugin state changes
  fun emitState(
    ctx: PluginContext,
    state: PluginState,
  ) {
    logger.info("Emitting state change: $state")

    val message = ApplicationManager.getApplication().messageBus
    message.syncPublisher(topic).onStateChange(ctx, state)
  }

  fun subscribe(
    connection: MessageBusConnection,
    listener: PluginStateListener,
  ) {
    // Wrap the listener to ensure its callbacks are called on a coroutine
    // and cannot block other listeners from being called.
    val wrappedListener =
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          cs.launch {
            listener.onStateChange(context, state)
          }
        }
      }

    connection.subscribe(topic, wrappedListener)
  }
}

interface PluginStateListener {
  fun onStateChange(
    context: PluginContext,
    state: PluginState,
  )
}
