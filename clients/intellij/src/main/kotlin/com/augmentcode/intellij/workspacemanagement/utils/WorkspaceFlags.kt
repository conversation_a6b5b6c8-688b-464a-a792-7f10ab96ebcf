package com.augmentcode.intellij.workspacemanagement.utils

import com.augmentcode.intellij.pluginstate.AugmentAppStateService

const val INDEXING_V3_ENABLED_PROPERTY = "augmentcode.indexing.v3.enabled"
private val prop = System.getProperty(INDEXING_V3_ENABLED_PROPERTY)?.toBoolean()

fun isV3IndexingEnabled(): Boolean {
  return prop ?: AugmentAppStateService.instance.context.flags.indexingV3Enabled
}
