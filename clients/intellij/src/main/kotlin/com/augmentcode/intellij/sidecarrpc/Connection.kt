package com.augmentcode.intellij.sidecarrpc

import com.google.protobuf.Any
import com.google.protobuf.Message
import kotlinx.coroutines.Deferred

interface Connection {
  suspend fun sendRequest(
    method: String,
    params: Message,
    resultClassHint: Class<*>,
  ): Deferred<Any>

  suspend fun sendNotification(
    method: String,
    params: Message,
  )

  suspend fun sendProgress(
    token: String,
    value: Message,
  )

  fun onRequest(
    method: String,
    paramsClassHint: Class<*>,
    handler: suspend (params: Any) -> Message,
  )
}
