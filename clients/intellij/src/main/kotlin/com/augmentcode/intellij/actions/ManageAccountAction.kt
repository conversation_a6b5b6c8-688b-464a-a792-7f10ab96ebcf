package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware
import public_api.PublicApi.GetModelsResponse.UserTier

class ManageAccountAction : AnActionWithCtx(), DumbAware {
  override fun updateWithCtx(
    e: AnActionEvent,
    ctx: PluginContext,
    model: AugmentModel,
  ) {
    // Update the action's text dynamically based on the user tier
    e.presentation.text =
      when (model.userTier) {
        UserTier.COMMUNITY_TIER -> AugmentBundle.message("actions.manage.account.community")
        UserTier.PROFESSIONAL_TIER -> AugmentBundle.message("actions.manage.account.professional")
        UserTier.ENTERPRISE_TIER -> AugmentBundle.message("actions.manage.account.enterprise")
        else -> AugmentBundle.message("actions.manage.account")
      }
  }

  // If we don't specify BGT, then IntelliJ will use OLD_EDT which is deprecated
  override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

  override fun actionPerformed(e: AnActionEvent) {
    BrowserUtil.browse("https://app.augmentcode.com/account")
  }
}
