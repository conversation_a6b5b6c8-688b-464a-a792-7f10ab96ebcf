package com.augmentcode.intellij.auth

import com.intellij.credentialStore.CredentialAttributes
import com.intellij.credentialStore.Credentials
import com.intellij.credentialStore.generateServiceName
import com.intellij.ide.passwordSafe.PasswordSafe
import com.intellij.openapi.application.*
import com.intellij.openapi.components.*
import com.intellij.util.resettableLazy
import kotlinx.coroutines.CoroutineScope
import org.jetbrains.concurrency.asDeferred

@Service(Service.Level.APP)
class AugmentOAuthState(val cs: CoroutineScope) {
  private val credentialAttributes: CredentialAttributes =
    CredentialAttributes(
      generateServiceName(SUBSYSTEM_NAME, "credentials"),
    )

  private val memoizedStoredCredentials =
    resettableLazy {
      PasswordSafe.instance.getAsync(credentialAttributes).asDeferred()
    }

  private val credMessageBus = CredentialsMessageBusWrapper(cs)

  companion object {
    private const val SUBSYSTEM_NAME = "augment oauth"

    val instance: AugmentOAuthState
      get() = service()
  }

  fun saveCredentials(credentials: AugmentCredentials) {
    PasswordSafe.instance.set(
      credentialAttributes,
      Credentials(credentials.tenantURL, credentials.accessToken),
    )
    memoizedStoredCredentials.reset()

    credMessageBus.sendCredentials(credentials)
  }

  suspend fun getCredentials(): AugmentCredentials? {
    val storedCredentials = memoizedStoredCredentials.value.await() ?: return null
    val tenantURL = storedCredentials.userName ?: return null
    val accessToken = storedCredentials.password?.toString() ?: return null
    return AugmentCredentials(accessToken, tenantURL)
  }

  fun clear() {
    PasswordSafe.instance.set(credentialAttributes, null)
    memoizedStoredCredentials.reset()

    credMessageBus.sendCredentials(null)
  }
}
