package com.augmentcode.intellij.sidecarrpc

import com.augmentcode.sidecar.rpc.JSONRPCErrorData
import com.augmentcode.sidecar.rpc.JSONRPCNotification
import com.augmentcode.sidecar.rpc.JSONRPCRequest
import com.augmentcode.sidecar.rpc.JSONRPCResponse
import com.augmentcode.sidecar.rpc.ProgressParams
import com.google.protobuf.Any
import com.google.protobuf.Message
import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

class IOConnection(
  input: InputStream,
  output: OutputStream,
  private val cs: CoroutineScope,
) : Connection {
  private val outWriteMutex = Mutex()
  private val out = BufferedOutputStream(output)

  private val methodHandlers = mutableMapOf<String, Pair<Class<*>, suspend (Any) -> Message>>()

  // deferred will be completed once sidecar sends us a response via output
  private val deferredResponses: MutableMap<Int, Pair<CompletableDeferred<Any>, Class<*>>> = ConcurrentHashMap()

  // request ids going to the sidecar can be strings or numbers
  // so let's use incremental numbers to ease troubleshooting
  private val messageIdCounter = AtomicInteger()

  // Add this field to track active request processing jobs
  private val activeRequestJobs: MutableMap<Int, Job> = ConcurrentHashMap()

  init {
    cs.launch {
      val bufferedInput = BufferedInputStream(input)
      while (isActive) {
        try {
          val content = readMessageFromStream(bufferedInput)
          if (content != null) {
            val structBuilder = Struct.newBuilder()
            jsonParser.merge(content, structBuilder)
            val lineStruct = structBuilder.build()
            when {
              lineStruct.containsFields("method") -> {
                val method = lineStruct.getFieldsOrThrow("method").stringValue
                if (method == "$/cancelRequest") {
                  handleCancelNotification(lineStruct)
                } else {
                  handleRequest(lineStruct)
                }
              }

              lineStruct.containsFields("result") -> {
                handleResponse(lineStruct)
              }

              lineStruct.containsFields("error") -> {
                handleError(lineStruct)
              }

              else -> {
                thisLogger().warn("Unknown sidecar message: $content")
              }
            }
          }
        } catch (e: CancellationException) {
          throw e
        } catch (e: Throwable) {
          thisLogger().warn("Failed to read line", e)
        }
        yield()
      }
    }
  }

  override suspend fun sendRequest(
    method: String,
    params: Message,
    resultClassHint: Class<*>,
  ): Deferred<Any> {
    val id = messageIdCounter.incrementAndGet()
    val deferred = CompletableDeferred<Any>()
    deferredResponses[id] = deferred to resultClassHint
    val request =
      JSONRPCRequest.newBuilder()
        .setJsonrpc("2.0")
        .setId(id)
        .setMethod(method)
        .setParams(Any.pack(params))
        .build()
    writeMessage(request)
    return deferred
  }

  override suspend fun sendNotification(
    method: String,
    params: Message,
  ) {
    val request =
      JSONRPCNotification.newBuilder()
        .setJsonrpc("2.0")
        .setMethod(method)
        .setParams(Any.pack(params))
        .build()
    writeMessage(request)
  }

  private suspend fun writeMessage(request: Message) {
    outWriteMutex.withLock {
      val requestLine = jsonPrinter.print(request)
      thisLogger().info("message to sidecar: ${requestLine.take(1000)}")
      out.write("Content-Length: ".toByteArray())
      out.write(requestLine.toByteArray().size.toString().toByteArray())
      out.write("\r\n\r\n".toByteArray())
      out.write(requestLine.toByteArray())
      out.flush()
    }
  }

  private suspend fun handleResponse(lineStruct: Struct) {
    val id = lineStruct.getFieldsOrThrow("id").numberValue.toInt()
    val responseForId = deferredResponses.remove(id)
    if (responseForId == null) {
      thisLogger().warn("Received sidecar response for unknown request: $lineStruct")
      return
    }
    val (completableDeferred, resultClassHint) = responseForId
    completableDeferred.complete(
      convertStructToAnyWithHint(
        lineStruct.getFieldsOrThrow("result").structValue,
        resultClassHint,
      ),
    )
  }

  private suspend fun handleError(lineStruct: Struct) {
    val id = lineStruct.getFieldsOrThrow("id").numberValue.toInt()
    val responseForId = deferredResponses.remove(id)
    if (responseForId == null) {
      thisLogger().warn("Received sidecar error for unknown request: $lineStruct")
      return
    }
    val (completableDeferred, _) = responseForId
    completableDeferred.complete(
      convertStructToAnyWithHint(
        lineStruct.getFieldsOrThrow("error").structValue,
        JSONRPCErrorData::class.java,
      ),
    )
  }

  private suspend fun handleRequest(lineStruct: Struct) {
    val method = lineStruct.getFieldsOrThrow("method").stringValue
    val requestHandlerForMethod = methodHandlers[method]
    if (requestHandlerForMethod == null) {
      thisLogger().warn("Received sidecar request for unknown method: $lineStruct")
      return
    }
    val id = lineStruct.getFieldsOrThrow("id").numberValue.toInt()
    val (paramsClassHint, requestHandler) = requestHandlerForMethod

    // Create a job for this request that can be cancelled
    val requestJob =
      cs.launch {
        try {
          val response =
            requestHandler.invoke(
              convertStructToAnyWithHint(
                lineStruct.getFieldsOrThrow("params").structValue,
                paramsClassHint,
              ),
            )
          // Only send response if not cancelled
          if (isActive) {
            sendResponse(id, response)
          }
        } catch (e: CancellationException) {
          thisLogger().info("Request with id $id was cancelled during processing")
          sendError(id, CancellationException("Request was canceled"))
          // Re-throw the exception to cancel the job
          throw e
        } catch (e: Throwable) {
          if (isActive) {
            sendError(id, e)
          }
        } finally {
          // Clean up the job reference when done
          activeRequestJobs.remove(id)
        }
      }

    // Store the job so it can be cancelled later
    activeRequestJobs[id] = requestJob
  }

  private suspend fun sendError(
    id: Int,
    error: Throwable,
  ) {
    writeMessage(
      JSONRPCResponse.newBuilder()
        .setJsonrpc("2.0")
        .setId(id)
        .setError(
          JSONRPCErrorData.newBuilder()
            .setMessage(error.message ?: "Unknown error")
            .build(),
        )
        .build(),
    )
  }

  private suspend fun sendResponse(
    id: Int,
    response: Message,
  ) {
    writeMessage(
      JSONRPCResponse.newBuilder()
        .setJsonrpc("2.0")
        .setId(id)
        .setResult(Any.pack(response))
        .build(),
    )
  }

  override fun onRequest(
    method: String,
    paramsClassHint: Class<*>,
    handler: suspend (params: Any) -> Message,
  ) {
    methodHandlers[method] = paramsClassHint to handler
  }

  // https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#progress
  // Progress notifications allow for "streaming" results from a long running request.
  override suspend fun sendProgress(
    token: String,
    value: Message,
  ) {
    // Create a struct with token and value fields, according to the spec
    val params =
      ProgressParams.newBuilder()
        .setToken(token)
        .setValue(Any.pack(value))
        .build()

    val request =
      JSONRPCNotification.newBuilder()
        .setJsonrpc("2.0")
        .setMethod("$/progress")
        .setParams(Any.pack(params))
        .build()
    writeMessage(request)
  }

  private suspend fun handleCancelNotification(lineStruct: Struct) {
    val params = lineStruct.getFieldsOrThrow("params").structValue
    val id = params.getFieldsOrThrow("id").numberValue.toInt()

    // Remove the active job for the canceled request
    val job = activeRequestJobs.remove(id)
    if (job == null) {
      thisLogger().warn("Received cancel request for unknown request id: $id")
      return
    }

    // Cancel the job
    job.cancel()

    thisLogger().info("Request with id $id was canceled")
  }
}

fun convertStructToAnyWithHint(
  struct: Struct,
  classHint: Class<*>,
): Any {
  // Convert class to type url. Note that standard types don't have "com." prefix.
  val typeURL = classHint.canonicalName.replace("com.google.protobuf.", "google.protobuf.")
  val hintTypeValue = Value.newBuilder().setStringValue("type.googleapis.com/$typeURL").build()
  val patchedStruct =
    Struct.newBuilder(struct)
      .putFields("@type", hintTypeValue)
  val anyBuilder = Any.newBuilder()
  jsonParser.merge(jsonPrinter.print(patchedStruct), anyBuilder)
  return anyBuilder.build()
}
