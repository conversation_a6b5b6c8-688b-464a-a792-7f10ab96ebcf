package com.augmentcode.intellij.metrics

import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.sentry.SentryService
import com.augmentcode.intellij.utils.CustomPropertyReader
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.PathManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import io.sentry.Sentry
import io.sentry.SentryLevel
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date

class EdtFreezeException(
  val freezeDurationMs: Long,
  val threshold: Long,
) : Exception("EDT freeze detected: ${freezeDurationMs}ms (threshold: ${threshold}ms)") {
  init {
    this.stackTrace = arrayOf()
  }
}

@Service(Service.Level.APP)
class EdtFreezeDetector(private val cs: CoroutineScope) : Disposable {
  companion object {
    val instance: EdtFreezeDetector
      get() = service<EdtFreezeDetector>()

    private const val SENTRY_REPORTING_THRESHOLD_MS = 5000L
    private const val CRITICAL_FREEZE_THRESHOLD_MS = 20000L
    private const val EDT_FREEZE_CHECK_INTERVAL_MS = 5000L
    private const val LOG_REPORTING_THRESHOLD_MS = 200L
    private val logIntervals = listOf(200L, 1000L, 5000L, 20000L, 60000L)

    /**
     * Determines if EDT freeze detection should be enabled based on custom property and feature flag.
     *
     * The custom property takes precedence:
     * - If custom property is explicitly set to true -> enabled
     * - If custom property is explicitly set to false -> disabled
     * - If custom property is not set -> falls back to feature flag
     */
    fun shouldEnableEdtFreezeDetection(featureFlagValue: Boolean): Boolean {
      val customPropertyValue = CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY)

      return when (customPropertyValue?.lowercase()) {
        "true" -> true
        "false" -> false
        else -> featureFlagValue
      }
    }
  }

  private val logger = thisLogger()

  private var monitoringJob: Job? = null
  private var currentContext: PluginContext? = null

  init {
    AugmentAppStateService.instance.subscribe(
      ApplicationManager.getApplication().messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          currentContext = context

          if (context.isSignedIn) {
            val shouldEnable = shouldEnableEdtFreezeDetection(context.flags.enableEdtFreezeDetection)
            if (shouldEnable) {
              val customPropertyValue = CustomPropertyReader.readProperty(CustomPropertyReader.EDT_FREEZE_DETECTOR_ENABLED_PROPERTY)
              val reason = if (customPropertyValue != null) "custom property override" else "feature flag"
              logger.info("Starting EDT freeze detection based on $reason")
              startMonitoring()
              return
            }
          }
          logger.info("Stopping EDT freeze detection")
          stopMonitoring()
        }
      },
      triggerOnStateChange = true,
    )
  }

  fun startMonitoring() {
    if (monitoringJob != null) return

    logger.info("Starting EDT freeze detection")
    monitoringJob =
      cs.launch {
        while (isActive) {
          checkEdtResponsiveness()
          delay(EDT_FREEZE_CHECK_INTERVAL_MS)
        }
      }
  }

  private suspend fun checkEdtResponsiveness() {
    val edtDeferred = CompletableDeferred<Long>()

    invokeLater {
      edtDeferred.complete(System.currentTimeMillis())
    }
    val startTime = System.currentTimeMillis()

    for (interval in logIntervals) {
      val result = withTimeoutOrNull(interval) { edtDeferred.await() }
      if (result != null) {
        handleEdtRecovery(startTime)
        return
      }

      logger.warn("EDT has been frozen for ${interval}ms, logging thread dump")
      saveThreadDumpToFile()
    }

    // If the EDT is still frozen, wait indefinitely for it to recover.
    if (!edtDeferred.isCompleted) {
      edtDeferred.await()
      handleEdtRecovery(startTime)
    }
  }

  private fun handleEdtRecovery(freezeStartTime: Long) {
    val freezeDurationMs = System.currentTimeMillis() - freezeStartTime

    if (freezeDurationMs >= LOG_REPORTING_THRESHOLD_MS) {
      logger.warn("EDT is now responsive after ${freezeDurationMs}ms.")
    }
    reportFreezeToSentry(freezeDurationMs)
  }

  private fun captureThreadDump(outputFile: File) {
    val allStackTraces = Thread.getAllStackTraces()

    outputFile.bufferedWriter().use { writer ->
      writer.appendLine("=== Thread Dump ===")
      writer.appendLine("Timestamp: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date())}")
      writer.appendLine("Freeze type: EDT freeze")
      writer.appendLine()

      for ((thread, stackTrace) in allStackTraces) {
        writer.appendLine("\"${thread.name}\" #${thread.threadId()} ${thread.state}")

        if (thread.name.contains("AWT-EventQueue", ignoreCase = true)) {
          writer.appendLine("*** EDT THREAD ***")
        }
        for (element in stackTrace) {
          writer.appendLine("\tat $element")
        }
        writer.appendLine()
      }
      writer.appendLine("===================")
    }
  }

  private fun saveThreadDumpToFile() {
    val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
    val fileName = "augment-detected-edt-freeze-$timestamp.log"

    try {
      val logDir = PathManager.getLogPath()
      val file = File(logDir, fileName)
      captureThreadDump(file)

      logger.info("Thread dump written to: ${file.absolutePath}")
    } catch (e: Exception) {
      logger.warn("Failed to write thread dump to file: ${e.message}")
    }
  }

  private fun reportFreezeToSentry(freezeDurationMs: Long) {
    if (freezeDurationMs < SENTRY_REPORTING_THRESHOLD_MS) {
      return
    }

    try {
      val sentryService = service<SentryService>()

      val level =
        if (freezeDurationMs >= CRITICAL_FREEZE_THRESHOLD_MS) {
          SentryLevel.ERROR
        } else {
          SentryLevel.WARNING
        }

      sentryService.addBreadcrumb(
        message = "EDT freeze detected: ${freezeDurationMs}ms",
        category = "performance.edt_freeze",
        level = level,
      )

      val exception =
        EdtFreezeException(
          freezeDurationMs = freezeDurationMs,
          threshold = SENTRY_REPORTING_THRESHOLD_MS,
        )
      Sentry.withScope { scope ->
        scope.setTag("freeze_duration_ms", freezeDurationMs.toString())
        scope.setTag("freeze_severity", if (freezeDurationMs >= CRITICAL_FREEZE_THRESHOLD_MS) "critical" else "warning")
        scope.setTag("component", "edt_freeze_detector")
        scope.level = level
        sentryService.captureException(exception)
      }

      logger.info("EDT freeze reported to Sentry: ${freezeDurationMs}ms")
    } catch (e: Exception) {
      logger.warn("Failed to report EDT freeze to Sentry: ${e.message}")
    }
  }

  fun stopMonitoring() {
    monitoringJob?.cancel()
    monitoringJob = null
  }

  override fun dispose() {
    stopMonitoring()
  }
}
