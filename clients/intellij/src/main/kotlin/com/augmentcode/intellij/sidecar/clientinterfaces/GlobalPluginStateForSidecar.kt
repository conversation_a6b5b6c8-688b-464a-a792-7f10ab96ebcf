package com.augmentcode.intellij.sidecar.clientinterfaces

import com.intellij.openapi.components.*
import com.intellij.openapi.diagnostic.thisLogger

@Service(Service.Level.APP)
@State(name = "GlobalPluginStateForSidecar", storages = [Storage("GlobalPluginStateForSidecar.xml")])
class GlobalPluginStateForSidecar : SimplePersistentStateComponent<GlobalPluginStateValues>(GlobalPluginStateValues()) {
  companion object {
    val instance: GlobalPluginStateForSidecar
      get() = service()
  }

  fun getValue(key: String): String? {
    thisLogger().info("Getting value for key: $key: ${state.values[key]}")
    return state.values[key]
  }

  fun setValue(
    key: String,
    value: String?,
  ) {
    thisLogger().info("Setting value for key: $key: $value")
    if (value == null) {
      state.values.remove(key)
    } else {
      state.values[key] = value
    }
  }
}

class GlobalPluginStateValues : BaseState() {
  var values by map<String, String>()
}
