package com.augmentcode.intellij.sidecarrpc

import com.augmentcode.sidecar.rpc.SidecarRPCTypes
import com.augmentcode.sidecar.rpc.chat.ChatRPCTypes
import com.augmentcode.sidecar.rpc.clientInterfaces.*
import com.augmentcode.sidecar.rpc.tools.ToolsRPCTypes
import com.google.protobuf.*
import com.google.protobuf.Descriptors.Descriptor
import com.google.protobuf.Descriptors.FieldDescriptor
import com.google.protobuf.util.JsonFormat

private val typeRegistry =
  TypeRegistry.newBuilder()
    .add(SidecarRPCTypes.getDescriptor().messageTypes)
    .add(ToolsRPCTypes.getDescriptor().messageTypes)
    .add(ClientWorkspacesRPCTypes.getDescriptor().messageTypes)
    .add(APIClientRPCTypes.getDescriptor().messageTypes)
    .add(PluginFileStoreRPCTypes.getDescriptor().messageTypes)
    .add(PluginStorageForSidecarRPCTypes.getDescriptor().messageTypes)
    .add(ChatRPCTypes.getDescriptor().messageTypes)
    .add(ClientActionsRPCTypes.getDescriptor().messageTypes)
    .add(Empty.getDescriptor()) // a default for no-op
    .add(Timestamp.getDescriptor()) // well-known type for testing
    .build()

internal val jsonParser =
  JsonFormat.parser()
    .usingTypeRegistry(typeRegistry)
    .ignoringUnknownFields()

internal val jsonPrinter =
  JsonFormat.printer()
    .omittingInsignificantWhitespace() // to print in a single line
    .includingDefaultValueFields() // Ensure id: 0 is included in the output
    .usingTypeRegistry(typeRegistry)

fun <T : Message.Builder> convertStruct(
  struct: Struct,
  factory: () -> T,
): T {
  val builder = factory()
  jsonParser.merge(jsonPrinter.print(struct), builder)
  return builder
}

fun convertToJsonSchema(descriptor: Descriptor): String {
  return jsonPrinter.print(convertToJsonSchemaStruct(descriptor))
}

fun convertToJsonSchemaStruct(descriptor: Descriptor): Struct {
  val resultBuilder = Struct.newBuilder()
  resultBuilder.putFields("type", Value.newBuilder().setStringValue("object").build())
  val propertiesBuilder = Struct.newBuilder()
  val requiredFields = ListValue.newBuilder()
  for (field in descriptor.fields) {
    if (field.options.getExtension(SidecarRPCTypes.schemaRequired)) {
      requiredFields.addValues(
        Value.newBuilder().setStringValue(field.name).build(),
      )
    }
    propertiesBuilder.putFields(
      field.name,
      Value.newBuilder()
        .setStructValue(
          Struct.newBuilder()
            .putFields("type", Value.newBuilder().setStringValue(convertToJsonType(field.type)).build())
            .putFields(
              "description",
              Value.newBuilder().setStringValue(field.options.getExtension(SidecarRPCTypes.schemaDescription)).build(),
            )
            .build(),
        )
        .build(),
    )
  }
  resultBuilder.putFields("properties", Value.newBuilder().setStructValue(propertiesBuilder.build()).build())
  resultBuilder.putFields("required", Value.newBuilder().setListValue(requiredFields).build())
  return resultBuilder.build()
}

fun convertToJsonType(type: FieldDescriptor.Type): String {
  return when (type) {
    FieldDescriptor.Type.MESSAGE -> "object"
    FieldDescriptor.Type.STRING -> "string"
    FieldDescriptor.Type.BYTES -> "string"
    FieldDescriptor.Type.ENUM -> "string"
    FieldDescriptor.Type.BOOL -> "boolean"
    FieldDescriptor.Type.DOUBLE -> "number"
    FieldDescriptor.Type.FIXED32 -> "number"
    FieldDescriptor.Type.FIXED64 -> "number"
    FieldDescriptor.Type.FLOAT -> "number"
    FieldDescriptor.Type.INT64 -> "number"
    FieldDescriptor.Type.UINT64 -> "number"
    FieldDescriptor.Type.INT32 -> "number"
    FieldDescriptor.Type.GROUP -> "number"
    FieldDescriptor.Type.UINT32 -> "number"
    FieldDescriptor.Type.SFIXED32 -> "number"
    FieldDescriptor.Type.SFIXED64 -> "number"
    FieldDescriptor.Type.SINT32 -> "number"
    FieldDescriptor.Type.SINT64 -> "number"
  }
}
