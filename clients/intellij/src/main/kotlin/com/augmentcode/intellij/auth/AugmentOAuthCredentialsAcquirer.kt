package com.augmentcode.intellij.auth

import com.augmentcode.api.AuthTokenRequest
import com.augmentcode.api.AuthTokenResult
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.collaboration.auth.services.OAuthCredentialsAcquirer
import com.intellij.openapi.application.invokeLater
import com.intellij.ui.AppIcon
import com.intellij.util.Url
import kotlinx.coroutines.runBlocking

internal class AugmentOAuthCredentialsAcquirer(
  private val oauthState: OAuthState,
  private val redirectUrl: Url,
) : OAuthCredentialsAcquirer<AugmentCredentials> {
  var parameters: Map<String, List<String>>? = null

  override fun acquireCredentials(code: String): OAuthCredentialsAcquirer.AcquireCredentialsResult<AugmentCredentials> {
    val params = parameters ?: return OAuthCredentialsAcquirer.AcquireCredentialsResult.Error("Unexpected parameters")

    val state = params["state"]?.firstOrNull() ?: return OAuthCredentialsAcquirer.AcquireCredentialsResult.Error("No state")
    if (state != oauthState.state) {
      return OAuthCredentialsAcquirer.AcquireCredentialsResult.Error("Unexpected state")
    }

    val tenantURL = params["tenant_url"]?.firstOrNull() ?: return OAuthCredentialsAcquirer.AcquireCredentialsResult.Error("No tenant")

    val tokenRequest = AuthTokenRequest()
    tokenRequest.code = code
    tokenRequest.codeVerifier = oauthState.verifier
    tokenRequest.clientId = "augment-intellij-plugin"
    tokenRequest.redirectUri = redirectUrl.toExternalForm()

    val api = AugmentAPI.instance
    val tokenResult: AuthTokenResult? =
      runBlocking {
        return@runBlocking api.token(tenantURL, tokenRequest)
      }

    if (tokenResult == null) {
      return OAuthCredentialsAcquirer.AcquireCredentialsResult.Error("Unable to get token")
    }

    val credentials = AugmentCredentials(tokenResult.accessToken, tenantURL)
    AugmentOAuthState.instance.saveCredentials(credentials)

    invokeLater {
      // Focus on IntelliJ once sign-in is complete
      AppIcon.getInstance().requestFocus()
    }

    return OAuthCredentialsAcquirer.AcquireCredentialsResult.Success(credentials)
  }
}
