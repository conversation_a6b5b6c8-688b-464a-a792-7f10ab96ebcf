package com.augmentcode.intellij.api

import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.util.SystemInfo
import com.intellij.util.PlatformUtils

@Suppress("UnstableApiUsage")
fun getUserAgent(): String {
  val plugin = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
  val pluginVersion = plugin?.version ?: "snapshot"
  val info = ApplicationInfo.getInstance()
  val isJbClient = if (PlatformUtils.isJetBrainsClient()) "-jb-client" else ""
  val gatewayLoaded = if (PlatformUtils.isGateway()) "-Gateway" else ""
  val systemInfo = listOf(SystemInfo.OS_NAME, SystemInfo.OS_ARCH, SystemInfo.OS_VERSION).joinToString("; ")
  return "augment.${AugmentAPI.CLIENT_NAME}/$pluginVersion ($systemInfo) " +
    "${info.versionName}/${info.fullVersion}$gatewayLoaded$isJbClient"
}
