package com.augmentcode.intellij.chat

import com.augmentcode.intellij.webviews.AugmentMessagingService
import com.augmentcode.intellij.webviews.AugmentWebview
import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.WebviewFactory
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.google.protobuf.Message
import com.intellij.openapi.Disposable
import com.intellij.openapi.actionSystem.DataProvider
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.SimpleToolWindowPanel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.jcef.JBCefApp
import java.awt.BorderLayout
import javax.swing.BorderFactory
import javax.swing.JEditorPane
import javax.swing.JLabel
import javax.swing.JPanel

class AugmentChatToolWindow(
  project: Project,
) : SimpleToolWindowPanel(true, true),
  DataProvider,
  Disposable {
  companion object {
    const val ID = "Augment"

    const val CHAT_APP = "chat"
    const val SIGN_IN_APP = "sign-in"
    const val AWAITING_SYNCING_PERMISSION_APP = "awaiting-syncing-permission"
    const val LOADING_APP = "loading"
  }

  private val logger = thisLogger()
  private val messagingService: AugmentMessagingService = ChatMessagingService.getInstance(project)
  private val entryFilePath = "main-panel.html"
  private val webview: AugmentWebview?

  // messageBus.connect will call Disposer.register for us.
  private val messageBusConnection = project.messageBus.connect(this)

  init {
    val contentPanel = JPanel(BorderLayout())
    contentPanel.border = BorderFactory.createEmptyBorder(0, 4, 4, 0)
    webview =
      if (JBCefApp.isSupported()) {
        WebviewFactory.create(project, entryFilePath, AugmentWebviewStateKey.CHAT_STATE, messagingService, this)
      } else {
        logger.warn("JCEF is not supported")
        // we failed to create the webview, probably because JCEF is missing
        contentPanel.add(createJCEFInstructions())
        setContent(contentPanel)
        null
      }
    if (webview != null) {
      contentPanel.add(webview.browser.component, BorderLayout.CENTER)
      setContent(contentPanel)
      // This allows other components to post messages straight to this webview.
      messageBusConnection.subscribe(
        ChatWebviewMessageBus.CHAT_WEBVIEW_MESSAGE_TOPIC,
        object : ChatWebviewMessageBus {
          override fun postMessageToWebview(message: Message) {
            webview.postMessage(messagingService.serializeMessageToJson(message))
            logger.debug("postMessage($message)")
          }

          override fun reloadIntellijStyles() {
            webview.updateZoomLevel()
            webview.reloadIntellijStyles()
            logger.debug("reloadIntellijStyles() executed")
          }
        },
      )
    }
  }

  private fun createJCEFInstructions(): JPanel {
    val panel = JPanel(BorderLayout(10, 10))
    panel.border = BorderFactory.createEmptyBorder(20, 20, 20, 20)

    // Create title
    val titleLabel = JLabel("JCEF Support Required")
    titleLabel.font = titleLabel.font.deriveFont(titleLabel.font.style or java.awt.Font.BOLD, 16f)
    titleLabel.border = BorderFactory.createEmptyBorder(0, 0, 10, 0)
    panel.add(titleLabel, BorderLayout.NORTH)

    // Create instructions
    val instructionsPane = JEditorPane("text/html", "")
    instructionsPane.isEditable = false
    instructionsPane.isOpaque = false
    instructionsPane.putClientProperty(JEditorPane.HONOR_DISPLAY_PROPERTIES, true)
    instructionsPane.addHyperlinkListener { e ->
      if (e.eventType == javax.swing.event.HyperlinkEvent.EventType.ACTIVATED) {
        com.intellij.ide.BrowserUtil.browse(e.url)
      }
    }

    val instructions =
      """
      <html>
      <body style='font-family: sans-serif; font-size: 12px;'>
        <p>Augment requires JCEF (Chromium Embedded Framework) to display its interface.</p>
        <p>To enable JCEF support:</p>
        <ol>
          <li><b>Go to Help → Find Action</b> (or press Ctrl+Shift+A / Cmd+Shift+A)</li>
          <li><b>Type "Choose Boot Java Runtime"</b> and select this action</li>
          <li><b>Select a JBR with JCEF support</b> from the list (marked with "jcef")</li>
          <li><b>Restart IntelliJ IDEA</b> when prompted</li>
        </ol>
        <p>If no JBR with JCEF is available in the list, you may need to download a newer version of IntelliJ IDEA.</p>
        <p>For more information, please visit <a href='https://plugins.jetbrains.com/docs/intellij/embedded-browser-jcef.html'>JetBrains JCEF documentation</a>.</p>
      </body>
      </html>
      """.trimIndent()

    instructionsPane.text = instructions

    // Add instructions to a scroll pane
    val scrollPane = JBScrollPane(instructionsPane)
    scrollPane.border = BorderFactory.createEmptyBorder()
    panel.add(scrollPane, BorderLayout.CENTER)

    return panel
  }

  override fun dispose() {
  }
}
