package com.augmentcode.intellij.webviews.settings

import com.augmentcode.intellij.webviews.AbstractWebviewMessagingService
import com.augmentcode.rpc.AsyncWrapper
import com.augmentcode.rpc.NavigateToSettingsSectionRequest
import com.google.protobuf.Any
import com.google.protobuf.Message
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

/**
 * This class will process messages from a webview and route them to the settings service (which
 * is the protobuf defined service).
 */
@Service(Service.Level.PROJECT)
class SettingsMessagingService(
  private val project: Project,
  private val cs: CoroutineScope,
) : AbstractWebviewMessagingService(project, cs) {
  private val logger = logger<SettingsMessagingService>()
  private var navigateToSectionOnLoad: String? = null

  companion object {
    fun getInstance(project: Project): SettingsMessagingService = project.getService(SettingsMessagingService::class.java)
  }

  fun navigateToSection(section: String?) {
    navigateToSectionOnLoad = section
    if (section != null) {
      sendMessageToWebview(NavigateToSettingsSectionRequest.newBuilder().setData(section).build())
    }
  }

  fun getNavigateToSectionOnLoad(): String? {
    return navigateToSectionOnLoad
  }

  override suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message> {
    val baseMessage = request.baseMsg
    val response =
      processSyncMessage(baseMessage).map { responseMsg ->
        AsyncWrapper.newBuilder()
          .setRequestId(request.requestId)
          .setBaseMsg(Any.pack(responseMsg))
          .build()
      }
    return response
  }

  override suspend fun processSyncMessage(request: Any): Flow<Message> {
    return when {
      request.`is`(com.augmentcode.rpc.SettingsPanelLoaded::class.java) -> {
        val section = navigateToSectionOnLoad
        navigateToSectionOnLoad = null
        if (section != null) {
          // Schedule navigation now that the webview is loaded
          cs.launch {
            // Short delay waiting for section selection to settle
            delay(500)
            sendMessageToWebview(NavigateToSettingsSectionRequest.newBuilder().setData(section).build())
          }
        }
        emptyFlow()
      }
      request.`is`(com.augmentcode.rpc.ToolConfigLoadedRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigLoadedRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).toolConfigLoaded(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigGetDefinitionsRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigGetDefinitionsRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).toolConfigGetDefinitions(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigSaveRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigSaveRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).toolConfigSave(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigStartOAuthRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigStartOAuthRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).toolConfigStartOAuth(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigRevokeAccessRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigRevokeAccessRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).toolConfigRevokeAccess(message))
      }
      request.`is`(com.augmentcode.rpc.GetStoredMCPServersRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.GetStoredMCPServersRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).getStoredMCPServers(message))
      }
      request.`is`(com.augmentcode.rpc.SetStoredMCPServersRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.SetStoredMCPServersRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).setStoredMCPServers(message))
      }
      request.`is`(com.augmentcode.rpc.ExecuteInitialOrientationRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ExecuteInitialOrientationRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).executeInitialOrientation(message))
      }
      request.`is`(com.augmentcode.rpc.UpdateUserGuidelinesRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.UpdateUserGuidelinesRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).updateUserGuidelines(message))
      }
      request.`is`(com.augmentcode.rpc.SignOutRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.SignOutRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).signOut(message))
      }
      request.`is`(com.augmentcode.rpc.OpenConfirmationModal::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.OpenConfirmationModal::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).openConfirmationModal(message))
      }

      request.`is`(com.augmentcode.rpc.HandleTriggerImportDialogRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.HandleTriggerImportDialogRequest::class.java)
        flowOf(AugmentSettingsWebviewService.getInstance(project).handleTriggerImportDialog(message))
      }
      request.`is`(com.augmentcode.rpc.OpenFileRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.OpenFileRequest::class.java)
        AugmentSettingsWebviewService.getInstance(project).openFile(message)
        emptyFlow()
      }
      request.`is`(com.augmentcode.rpc.ShowNotificationRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ShowNotificationRequest::class.java)
        AugmentSettingsWebviewService.getInstance(project).showNotification(message)
        emptyFlow()
      }
      else -> {
        logger.warn("Unknown message type: ${request.typeUrl}")
        emptyFlow()
      }
    }
  }
}
