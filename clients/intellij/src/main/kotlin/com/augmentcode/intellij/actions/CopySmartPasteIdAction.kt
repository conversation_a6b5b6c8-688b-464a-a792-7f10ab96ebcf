package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.ide.CopyPasteManager
import com.intellij.openapi.project.DumbAwareAction
import java.awt.datatransfer.StringSelection
import javax.swing.Timer

class CopySmartPasteIdAction(
  private val requestID: String,
) : DumbAwareAction(
    AugmentBundle.message("actions.copy.requestId"),
    AugmentBundle.message("actions.copy.requestId.description"),
    AllIcons.Actions.Copy,
  ) {
  companion object {
    private const val COPY_TIMEOUT_MS_PRODUCTION = 2000
    private const val COPY_TIMEOUT_MS_TEST = 0

    val TIMEOUT_MS =
      if (ApplicationManager.getApplication().isUnitTestMode) {
        COPY_TIMEOUT_MS_TEST
      } else {
        COPY_TIMEOUT_MS_PRODUCTION
      }
  }

  override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.EDT

  override fun update(e: AnActionEvent) {
    e.presentation.isEnabled = true
  }

  override fun actionPerformed(e: AnActionEvent) {
    CopyPasteManager.getInstance().setContents(StringSelection(requestID))

    e.presentation.text = AugmentBundle.message("actions.copy.requestId.copied")
    e.presentation.icon = AllIcons.Actions.MenuPaste

    Timer(TIMEOUT_MS) {
      invokeLater {
        e.presentation.text = AugmentBundle.message("actions.copy.requestId")
        e.presentation.icon = AllIcons.Actions.Copy
      }
    }.apply {
      isRepeats = false // ensure the timer only fires once, then stops + destroys itself
    }.start()
  }

  override fun displayTextInToolbar(): Boolean = true
}
