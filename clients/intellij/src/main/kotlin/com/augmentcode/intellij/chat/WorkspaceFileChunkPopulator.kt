package com.augmentcode.intellij.chat

import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.augmentcode.rpc.FileDetails
import com.augmentcode.rpc.WorkspaceFileChunk
import com.intellij.openapi.project.Project
import kotlin.collections.map

/**
 * Wrapper class that handles population of workspace file chunks
 * in chat that works for both IndexV2 and IndexV3.
 *
 * After IndexV3 is fully rolled out, the V3 logic can probably
 * be moved directly into the caller.
 */
class WorkspaceFileChunkPopulator(private val project: Project) {
  val isV3IndexingEnabled: Boolean = isV3IndexingEnabled()
  var blobsByRemoteName: Map<String, AugmentBlobState>? = null

  init {
    if (isV3IndexingEnabled) {
      blobsByRemoteName = null
    } else {
      blobsByRemoteName = AugmentRemoteSyncingManager.getInstance(project).syncedBlobs().associate { it.remoteName to it }
    }
  }

  suspend fun populateWorkspaceFileChunks(chunks: List<WorkspaceFileChunk>): List<WorkspaceFileChunk> {
    return if (isV3IndexingEnabled) {
      populateV3(chunks)
    } else {
      populateV2(chunks)
    }
  }

  private fun populateV2(chunks: List<WorkspaceFileChunk>): List<WorkspaceFileChunk> {
    return chunks.map { chunk ->
      val blob = blobsByRemoteName!![chunk.blobName] ?: return@map chunk
      val path = blob.relativePath
      WorkspaceFileChunk.newBuilder(chunk)
        .setFile(
          FileDetails.newBuilder()
            .setPathName(path),
        )
        .build()
    }
  }

  private suspend fun populateV3(chunks: List<WorkspaceFileChunk>): List<WorkspaceFileChunk> {
    val blobService = BlobNameService.getInstance(project)
    return chunks.map { chunk ->
      val file = blobService.fileByBlobName(chunk.blobName)
      if (file == null) {
        return@map chunk
      }
      WorkspaceFileChunk.newBuilder(chunk)
        .setFile(
          FileDetails.newBuilder()
            .setPathName(file.relPath),
        )
        .build()
    }
  }
}
