package com.augmentcode.intellij.idea

import com.intellij.ide.plugins.StandalonePluginUpdateChecker
import com.intellij.notification.NotificationGroup
import com.intellij.openapi.components.*
import com.intellij.openapi.extensions.PluginId
import icons.AugmentIcons

@Service
class AugmentPluginUpdater : StandalonePluginUpdateChecker(
  PluginId.getId("com.augmentcode"),
  PROPERTY_NAME,
  notificationGroup = NotificationGroup.findRegisteredGroup("augment.plugin.updates"),
  notificationIcon = AugmentIcons.StatusDefault,
) {
  companion object {
    private const val PROPERTY_NAME = "augment.lastUpdateCheck"

    fun getInstance(): AugmentPluginUpdater = service()
  }
}
