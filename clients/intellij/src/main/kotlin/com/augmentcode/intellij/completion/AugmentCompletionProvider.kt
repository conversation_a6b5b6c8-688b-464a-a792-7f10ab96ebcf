package com.augmentcode.intellij.completion

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.codeInsight.inline.completion.*
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionSuggestion
import com.intellij.openapi.application.readAction
import com.intellij.openapi.diagnostic.thisLogger

class AugmentCompletionProvider : InlineCompletionProvider {
  private val logger = thisLogger()
  override val id: InlineCompletionProviderID = InlineCompletionProviderID("Augment")

  override fun isEnabled(event: InlineCompletionEvent): Bo<PERSON>an {
    val extension = event.toRequest()?.editor?.virtualFile?.extension
    if (extension != null && AugmentSettings.instance.disableForFileTypes.contains(extension)) {
      logger.debug("Disabling completion for unsupported file extension: $extension")
      return false
    }

    if (event is InlineCompletionEvent.DirectCall) {
      // only check API availability if the user explicitly requested completion
      logger.debug("Completion is a direct call event: ${AugmentAPI.instance.availableBlocking()}")
      return AugmentAPI.instance.availableBlocking()
    }
    // otherwise, check the settings too
    logger.debug("Completion is automatic: ${AugmentSettings.instance.inlineCompletionEnabled && AugmentAPI.instance.availableBlocking()}")
    return AugmentSettings.instance.inlineCompletionEnabled && AugmentAPI.instance.availableBlocking()
  }

  override val insertHandler: InlineCompletionInsertHandler
    get() = AugmentCompletionInsertHandler

  override suspend fun getSuggestion(request: InlineCompletionRequest): InlineCompletionSuggestion {
    if (caretMovedForDirectCall(request)) {
      logger.warn("Caret moved since the direct call event, ignoring the request")
      return InlineCompletionSuggestion.Empty
    }
    return AugmentCompletionSuggestion(request)
  }

  // check if the request comes from a direct call aka user explicitly requested completion or from AugmentForceCompletions
  // then checks if caret moved since the request was made
  private suspend fun caretMovedForDirectCall(request: InlineCompletionRequest): Boolean {
    val directCallEvent = request.event as? InlineCompletionEvent.DirectCall ?: return false
    val (eventCaretOffset, editorCaretOffset) =
      readAction {
        directCallEvent.caret.offset to request.editor.caretModel.offset
      }
    return eventCaretOffset != editorCaretOffset
  }
}
