package com.augmentcode.intellij.metrics

import com.augmentcode.api.OnboardingSessionEvent
import com.augmentcode.api.OnboardingSessionEventName
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project

@Service(Service.Level.PROJECT)
class OnboardingSessionEventReporter : MetricsReporter<OnboardingSessionEvent>(
  maxRecords = DEFAULT_MAX_RECORDS,
  uploadBatchSize = DEFAULT_BATCH_SIZE,
  uploadIntervalMs = DEFAULT_UPLOAD_MSEC,
) {
  companion object {
    const val DEFAULT_MAX_RECORDS = 10000
    const val DEFAULT_BATCH_SIZE = 1000
    val DEFAULT_UPLOAD_MSEC = if (ApplicationManager.getApplication().isUnitTestMode) 10L else 10_000L

    fun getInstance(project: Project): OnboardingSessionEventReporter {
      return project.getService(OnboardingSessionEventReporter::class.java)
    }
  }

  // if (ApplicationManager.getApplication().isUnitTestMode) 0L else 10_000L
  suspend fun reportOnboardingSessionEvent(eventName: OnboardingSessionEventName) {
    report(
      OnboardingSessionEvent().apply {
        this.event_name = eventName.apiName
        this.event_time_sec = System.currentTimeMillis() / 1000
        this.event_time_nsec = System.nanoTime() % 1000_000_000
      },
    )
  }

  override suspend fun performUpload(batch: List<OnboardingSessionEvent>) {
    AugmentAPI.instance.onboardingSessionEvents(batch)
  }
}
