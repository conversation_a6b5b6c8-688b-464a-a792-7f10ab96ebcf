package com.augmentcode.intellij.sentry

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import io.sentry.Sentry
import io.sentry.SentryLevel
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Global exception handler that captures uncaught exceptions and reports them to Sentry.
 * This handler is installed as the default uncaught exception handler for all threads
 * created by the Augment plugin.
 */
@Service(Service.Level.APP)
class SentryGlobalExceptionHandler : Thread.UncaughtExceptionHandler {
  private val logger = thisLogger()
  private val isInstalled = AtomicBoolean(false)
  private var originalHandler: Thread.UncaughtExceptionHandler? = null

  companion object {
    fun getInstance(): SentryGlobalExceptionHandler = service()
  }

  /**
   * Install this handler as the default uncaught exception handler.
   * This should be called during plugin initialization.
   */
  fun install() {
    if (isInstalled.compareAndSet(false, true)) {
      originalHandler = Thread.getDefaultUncaughtExceptionHandler()
      Thread.setDefaultUncaughtExceptionHandler(this)
      logger.info("Sentry global exception handler installed successfully")
    } else {
      logger.warn("Sentry global exception handler is already installed")
    }
  }

  /**
   * Uninstall this handler and restore the original handler.
   * This should be called during plugin shutdown.
   */
  fun uninstall() {
    if (isInstalled.compareAndSet(true, false)) {
      Thread.setDefaultUncaughtExceptionHandler(originalHandler)
      originalHandler = null
      logger.info("Sentry global exception handler uninstalled successfully")
    }
  }

  /**
   * Handle uncaught exceptions by reporting them to Sentry and then delegating
   * to the original handler.
   */
  override fun uncaughtException(
    thread: Thread,
    exception: Throwable,
  ) {
    try {
      // Only report to Sentry if the exception should be reported
      // SentryService will handle the feature flag check internally
      if (shouldReportException(exception)) {
        reportToSentry(thread, exception)
      }
    } catch (e: Exception) {
      // If there's an error reporting to Sentry, log it but don't let it interfere
      // with the original exception handling
      logger.warn("Error reporting uncaught exception to Sentry", e)
    } finally {
      // Always delegate to the original handler to maintain normal IDE behavior
      originalHandler?.uncaughtException(thread, exception)
        ?: run {
          // If there's no original handler, log the exception
          logger.error("Uncaught exception in thread ${thread.name}", exception)
        }
    }
  }

  /**
   * Determine whether an exception should be reported to Sentry.
   * Only reports exceptions that originate from Augment plugin code and are not from test code.
   */
  internal fun shouldReportException(exception: Throwable): Boolean {
    // Only report exceptions that originate from Augment plugin code
    return isFromAugmentCode(exception)
  }

  /**
   * Check if the exception originated from Augment plugin code by examining the stack trace.
   * Only exceptions that have at least one stack frame from Augment code should be reported.
   */
  internal fun isFromAugmentCode(exception: Throwable): Boolean {
    return exception.stackTrace.any { stackTraceElement ->
      stackTraceElement.className.startsWith("com.augmentcode")
    }
  }

  /**
   * Report the uncaught exception to Sentry with additional context.
   */
  private fun reportToSentry(
    thread: Thread,
    exception: Throwable,
  ) {
    // Get the SentryService instance at the point of use using recommended pattern
    val sentryService = service<SentryService>()

    // Add breadcrumb with thread information
    sentryService.addBreadcrumb(
      message = "Uncaught exception in thread: ${thread.name}",
      category = "uncaught_exception",
      level = SentryLevel.ERROR,
    )

    // Set additional context
    Sentry.configureScope { scope ->
      scope.setTag("thread.name", thread.name)
      scope.setTag("thread.id", thread.threadId().toString())
      scope.setTag("thread.state", thread.state.toString())
      scope.setTag("thread.priority", thread.priority.toString())
      scope.setTag("thread.daemon", thread.isDaemon.toString())
      scope.setTag("exception.type", "uncaught")

      // Add thread group information if available
      thread.threadGroup?.let { threadGroup ->
        scope.setTag("thread.group.name", threadGroup.name)
        scope.setTag("thread.group.max_priority", threadGroup.maxPriority.toString())
      }
    }

    // Capture the exception
    sentryService.captureException(exception)

    logger.info("Uncaught exception reported to Sentry: ${exception.javaClass.simpleName}")
  }
}
