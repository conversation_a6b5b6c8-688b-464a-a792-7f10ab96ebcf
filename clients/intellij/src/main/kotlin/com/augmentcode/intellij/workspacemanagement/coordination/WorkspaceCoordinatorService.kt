package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.api.BlobsPayload
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobChangeEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.BlobRemovedEvent
import com.augmentcode.intellij.workspacemanagement.checkpoint.CheckpointManager
import com.augmentcode.intellij.workspacemanagement.checkpoint.CheckpointMonitor
import com.augmentcode.intellij.workspacemanagement.coordination.steps.BaseProcessingStep
import com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader
import com.augmentcode.intellij.workspacemanagement.coordination.steps.CoordinationFileDetails
import com.augmentcode.intellij.workspacemanagement.coordination.steps.CoordinationFileDetailsWithBlob
import com.augmentcode.intellij.workspacemanagement.coordination.steps.FileFilterStep
import com.augmentcode.intellij.workspacemanagement.coordination.steps.FileToUpload
import com.augmentcode.intellij.workspacemanagement.coordination.steps.InitialProbeStep
import com.augmentcode.intellij.workspacemanagement.coordination.steps.MemorizeStep
import com.augmentcode.intellij.workspacemanagement.coordination.steps.WaitForIndexingStep
import com.augmentcode.intellij.workspacemanagement.indexing.WORKSPACE_INDEX_ID
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import org.jetbrains.annotations.VisibleForTesting
import java.nio.file.Path

@Service(Service.Level.PROJECT)
class WorkspaceCoordinatorService(private val project: Project, scope: CoroutineScope) : Disposable {
  companion object {
    fun getInstance(project: Project): WorkspaceCoordinatorService {
      return project.service<WorkspaceCoordinatorService>()
    }

    fun requestRebuild() {
      thisLogger().info("Requesting workspace index rebuild")
      FileBasedIndex.getInstance().requestRebuild(WORKSPACE_INDEX_ID)
    }

    private const val UPLOAD_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD = 10_000 // Placeholder. Tune this.
    private const val WAIT_FOR_INDEX_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD = 10_000 // Placeholder. Tune this.
    private const val INTERNAL_QUEUE_SIZE = 15000
  }

  private val logger = thisLogger()

  // These channels should be given to the processors as input/output channels.

  @VisibleForTesting
  internal val intakeChannel = RoughlySizedChannel<String>(Channel(Channel.UNLIMITED))
  private val memorizeChannel = RoughlySizedChannel<VirtualFile>(Channel(INTERNAL_QUEUE_SIZE))
  internal val filteredFilesChannel = RoughlySizedChannel<CoordinationFileDetails>(Channel(INTERNAL_QUEUE_SIZE))
  private val uploadChannel = RoughlySizedChannel<FileToUpload>(Channel(INTERNAL_QUEUE_SIZE))
  private val waitForIndexChannel = RoughlySizedChannel<CoordinationFileDetailsWithBlob>(Channel(INTERNAL_QUEUE_SIZE))
  private val checkpointChannel = RoughlySizedChannel(Channel<BlobChangeEvent>(INTERNAL_QUEUE_SIZE))

  // Adding a channel to this config ensures the channel is monitored and disposed of.
  private val channelConfigs =
    listOf(
      ChannelMonitorConfig(
        "Intake",
        intakeChannel,
        null,
      ),
      ChannelMonitorConfig(
        "FilteredFiles",
        filteredFilesChannel,
        null,
      ),
      ChannelMonitorConfig(
        "Upload",
        uploadChannel,
        UPLOAD_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD,
      ),
      ChannelMonitorConfig(
        "WaitForIndex",
        waitForIndexChannel,
        WAIT_FOR_INDEX_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD,
      ),
      ChannelMonitorConfig(
        "Checkpoint",
        checkpointChannel,
        null,
      ),
    )

  private val checkpointManager = CheckpointManager(scope, checkpointChannel)

  // Steps
  private val fileFilterStep = FileFilterStep(project, scope, intakeChannel, memorizeChannel, filteredFilesChannel)
  private val memorizeStep = MemorizeStep(project, scope, memorizeChannel)
  private val initialProbeJob =
    InitialProbeStep(
      project,
      scope,
      filteredFilesChannel,
      uploadChannel,
      waitForIndexChannel,
      checkpointChannel,
    )
  private val batchFileUploader: BatchFileUploader = BatchFileUploader(project, scope, uploadChannel, waitForIndexChannel)
  private val waitForIndexingStep = WaitForIndexingStep(project, scope, waitForIndexChannel, checkpointChannel, uploadChannel)

  private val processingSteps: List<BaseProcessingStep> =
    listOf(
      fileFilterStep,
      memorizeStep,
      initialProbeJob,
      batchFileUploader,
      waitForIndexingStep,
    )

  init {
    // Register the processing steps for disposal
    Disposer.register(this, fileFilterStep)
    Disposer.register(this, memorizeStep)
    Disposer.register(this, initialProbeJob)
    Disposer.register(this, batchFileUploader)
    Disposer.register(this, waitForIndexingStep)
    Disposer.register(this, checkpointManager)

    // When the user signs in/out, we need to ensure we trigger a re-index
    AugmentAppStateService.instance.subscribe(
      project.messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          if (context.isSignedIn) {
            startProcessing()
            return
          } else {
            stopProcessing()
          }
        }
      },
      triggerOnStateChange = true,
    )

    val channelMonitor = ChannelMonitor(scope, channelConfigs)
    Disposer.register(this, channelMonitor)

    val checkpointMonitor = CheckpointMonitor(scope, checkpointManager)
    Disposer.register(this, checkpointMonitor)
  }

  /**
   * When the service is disposed we should close all channels.
   */
  override fun dispose() {
    // Close the channels
    channelConfigs.forEach { it.channel.close() }
  }

  /**
   * Enqueues a file for processing. This is the intake for any files that we may want to upload and index.
   *
   * IMPORTANT: This method is called by the WorkspaceIndexer, so must be quick.
   */
  fun enqueueFileForProcessing(fileUri: String) {
    if (!isV3IndexingEnabled()) return

    val result = intakeChannel.trySend(fileUri)
    if (result.isFailure) {
      logger.warn("Failed to enqueue file for processing: $fileUri")
    }
  }

  suspend fun getCheckpoint(): BlobsPayload {
    return checkpointManager.currentCheckpoint()
  }

  private fun startProcessing() {
    if (!isV3IndexingEnabled()) {
      logger.info("Skipping v3 indexing pipeline")
      return
    }

    logger.info("Syncing users workspace with Augment ${project.name} (${project.locationHash})")

    // Start the processing steps
    processingSteps.forEach { it.startProcessing() }
    checkpointManager.startCheckpointJob()
  }

  private fun stopProcessing() {
    logger.info("Stopping workspace processing")
    // Stop the processing steps
    processingSteps.forEach { it.stopProcessing() }
    checkpointManager.stopCheckpointJob()
  }

  /**
   * Gets the corresponding synced blob for file without waiting for the mutex.
   *
   * @return the most recently synced blob name for the given file, or null if not found
   */
  fun getSyncedBlobForFileNonBlocking(path: Path): String? {
    return BlobNameService.getInstance(project).getByPathNoBlocking(path.toString())
  }

  suspend fun onFileDeleted(absPath: Path) {
    val blobNames = BlobNameService.getInstance(project)
    val blobName = blobNames.deleteByPath(absPath.toString())
    if (blobName != null) {
      checkpointChannel.send(BlobRemovedEvent(absPath, blobName))
    }
  }

  suspend fun onDirectoryDeleted(directoryAbsolutePath: Path) {
    val blobNames = BlobNameService.getInstance(project)
    blobNames.deleteByDirectory(directoryAbsolutePath.toString()) { absPath, blobName ->
      val result = checkpointChannel.trySend(BlobRemovedEvent(Path.of(absPath), blobName))
      if (result.isFailure) {
        logger.warn("Failed to send BlobRemovedEvent for $absPath")
      }
    }
  }

  fun approximateIntakeSize(): Int {
    return intakeChannel.approximateSize
  }

  fun approximatequeueSizes(): ApproximateQueueSizes {
    return ApproximateQueueSizes(
      intakeChannel.approximateSize,
      filteredFilesChannel.approximateSize,
      uploadChannel.approximateSize,
      waitForIndexChannel.approximateSize,
    )
  }
}
