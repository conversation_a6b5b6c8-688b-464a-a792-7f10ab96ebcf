package com.augmentcode.intellij.chat

import com.augmentcode.intellij.api.DEFAULT_PREFIX_SIZE
import com.augmentcode.intellij.api.DEFAULT_SUFFIX_SIZE
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.rpc.CurrentlyOpenFiles
import com.augmentcode.rpc.FileDetails
import com.augmentcode.rpc.FileRangesSelected
import com.augmentcode.rpc.FullRange
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.application.smartReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.editor.ex.EditorEventMulticasterEx
import com.intellij.openapi.editor.ex.FocusChangeListener
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.util.text.LineColumn
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.text.nullize
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.annotations.VisibleForTesting
import java.lang.ref.WeakReference

@Service(Service.Level.PROJECT)
class EditorTrackingService(
  private val project: Project,
  private val cs: CoroutineScope,
) : SelectionListener, FileEditorManagerListener, FocusChangeListener, Disposable {
  private val logger = thisLogger()

  @Volatile
  private var lastEditorWithEvent: WeakReference<Editor>? = null

  @Volatile
  private var lastEditorEventTimestamp: Long = 0

  companion object {
    fun getInstance(project: Project): EditorTrackingService = project.service<EditorTrackingService>()

    private const val SELECTION_VALIDITY_TIMEOUT_MS = 30_000L
  }

  init {
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      registerListeners()
    }
  }

  @VisibleForTesting
  internal fun registerListeners() {
    val multicaster = EditorFactory.getInstance().eventMulticaster as EditorEventMulticasterEx
    multicaster.addSelectionListener(
      this,
      this,
    )
    multicaster.addFocusChangeListener(
      this,
      this,
    )
    val messageBusConnection = project.messageBus.connect(this)
    messageBusConnection.subscribe(
      FileEditorManagerListener.FILE_EDITOR_MANAGER,
      this,
    )
  }

  fun getActiveEditor(): Editor? {
    val trackedEditor = getTrackedEditorIfValid()
    return trackedEditor ?: getFallbackEditor()
  }

  internal fun trackEditor(editor: Editor?) {
    if (editor == null) {
      logger.debug("Tracked editor cleared")
      clearActiveEditor()
      return
    }
    if (!isEditorValid(editor, checkStale = false)) {
      logger.debug("Tracked editor is not valid, clearing active editor")
      clearActiveEditor()
      return
    }

    logger.debug("Tracked editor update: ${editor.virtualFile.path}")
    lastEditorEventTimestamp = System.currentTimeMillis()
    if (lastEditorWithEvent?.get() == editor) {
      return
    }
    lastEditorWithEvent = WeakReference(editor)
  }

  fun clearActiveEditor() {
    lastEditorWithEvent = null
    lastEditorEventTimestamp = 0
  }

  private fun getTrackedEditorIfValid(): Editor? {
    val editorRef = lastEditorWithEvent ?: return null
    val editor = editorRef.get() ?: return null

    return if (isEditorValid(editor)) editor else null
  }

  private fun isEditorValid(
    editor: Editor,
    checkStale: Boolean = true,
  ): Boolean {
    return try {
      if (editor.isDisposed) return false

      if (checkStale && isEditorEventStale()) return false

      // The virtual file can be null for non-text file changes (for example, selecting text in the terminal)
      if (editor.virtualFile == null) return false

      return true
    } catch (e: Exception) {
      false
    }
  }

  private fun isEditorEventStale(): Boolean {
    val age = System.currentTimeMillis() - lastEditorEventTimestamp
    return age > SELECTION_VALIDITY_TIMEOUT_MS
  }

  private fun getFallbackEditor(): Editor? {
    return FileEditorManager.getInstance(project).selectedTextEditor
  }

  override fun selectionChanged(e: SelectionEvent) {
    trackEditor(e.editor)

    sendCurrentSelection()
  }

  private fun sendCurrentSelection() {
    cs.launch(Dispatchers.IO) {
      val editor = getActiveEditor() ?: return@launch
      val selection = getSelectionDetails(editor)
      if (selection == null) {
        ChatWebviewMessageBus.syncPublisher(project).postMessageToWebview(
          FileRangesSelected.newBuilder().build(),
        )
        return@launch
      }

      val doc = editor.document
      val startLineCol: LineColumn? = StringUtil.offsetToLineColumn(doc.text, selection.start)
      val endLineCol: LineColumn? = StringUtil.offsetToLineColumn(doc.text, selection.end)

      if (selection.text == null || startLineCol == null || endLineCol == null) {
        ChatWebviewMessageBus.syncPublisher(project).postMessageToWebview(
          FileRangesSelected.newBuilder().build(),
        )
        return@launch
      }

      val rootAndPath =
        if (DumbService.getInstance(project).isDumb) {
          null
        } else if (!isEditorValid(editor)) {
          null
        } else {
          AugmentRoot.findRelativePathWithRoot(project, editor.virtualFile)
        }

      if (rootAndPath == null) {
        ChatWebviewMessageBus.syncPublisher(project).postMessageToWebview(
          FileRangesSelected.newBuilder().build(),
        )
        return@launch
      }

      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
      publisher.postMessageToWebview(
        FileRangesSelected
          .newBuilder()
          .addData(
            FileDetails
              .newBuilder()
              .setRepoRoot(rootAndPath.rootPath)
              .setPathName(rootAndPath.relativePath)
              .setFullRange(
                expandToLineBoundaries(startLineCol, endLineCol, doc),
              )
              .setOriginalCode(selection.text),
          ).build(),
      )
    }
  }

  fun getSelectionDetails(editor: Editor? = null): SelectionDetails? {
    val model = AugmentAppStateService.instance.context.model

    return runReadAction {
      if (editor == null || editor.virtualFile == null) {
        logger.debug("Editor is not valid, cannot get selection details")
        return@runReadAction null
      }

      val caret = editor.caretModel.primaryCaret
      val document = editor.document
      val prefix =
        document.getText(TextRange(0, caret.selectionStart))
          .takeLast(model?.suggestedPrefixCharCount ?: DEFAULT_PREFIX_SIZE)
      val selectedText = document.getText(TextRange(caret.selectionStart, caret.selectionEnd)).nullize()
      val suffix =
        document.getText(TextRange(caret.selectionEnd, document.textLength))
          .take(model?.suggestedSuffixCharCount ?: DEFAULT_SUFFIX_SIZE)

      return@runReadAction SelectionDetails(
        selectedText,
        editor.selectionModel.selectionStart,
        editor.selectionModel.selectionEnd,
        prefix,
        suffix,
      )
    }
  }

  private fun expandToLineBoundaries(
    startLineCol: LineColumn,
    endLineCol: LineColumn,
    document: Document,
  ): FullRange {
    var endLine = endLineCol.line
    if (endLineCol.column == 0 && endLine > startLineCol.line) {
      endLine -= 1
    }
    // Calculate the column position within the line, not the absolute offset
    val lineStartOffset = document.getLineStartOffset(endLine)
    val lineEndOffset = document.getLineEndOffset(endLine)
    val endColumn = lineEndOffset - lineStartOffset

    return FullRange
      .newBuilder()
      .setStartLineNumber(startLineCol.line)
      .setStartColumn(0)
      .setEndLineNumber(endLine)
      .setEndColumn(endColumn)
      .build()
  }

  override fun selectionChanged(e: FileEditorManagerEvent) {
    if (e.newEditor is TextEditor) {
      val editor = (e.newEditor as TextEditor).editor
      thisLogger().debug("File Editor Manager selection changed: ${editor.virtualFile.path} ${editor.selectionModel.hasSelection()}")
      trackEditor(editor)
    } else {
      thisLogger().debug("File Editor Manager selection changed for unknown editor: ${e.newEditor}")
    }

    sendCurrentlyOpenedFiles()
  }

  private suspend fun getCurrentlyOpenedFiles(): CurrentlyOpenFiles =
    // TODO: Why is smartReadAction needed here?
    smartReadAction(project) {
      val currentlyOpenFilesBuilder = CurrentlyOpenFiles.newBuilder()
      val editor = getActiveEditor()
      if (editor != null) {
        val pathWithRoot = AugmentRoot.findRelativePathWithRoot(project, editor.virtualFile)
        if (pathWithRoot?.relativePath != null) {
          currentlyOpenFilesBuilder.addData(
            FileDetails
              .newBuilder()
              .setRepoRoot(pathWithRoot.rootPath)
              .setPathName(pathWithRoot.relativePath),
          )
        }
      }
      currentlyOpenFilesBuilder.build()
    }

  fun sendCurrentlyOpenedFiles() {
    cs.launch(Dispatchers.IO) {
      DumbService.getInstance(project).waitForSmartMode()

      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
      publisher.postMessageToWebview(getCurrentlyOpenedFiles())
    }
  }

  // File closed is used instead of focus lost since changing from a tab in a window
  // to focusing chat would lose focus, but in that scenario we would want the the active
  // editor to remain the same.
  override fun fileClosed(
    source: FileEditorManager,
    file: VirtualFile,
  ) {
    if (source.selectedEditor is TextEditor) {
      val editor = (source.selectedEditor as TextEditor).editor
      if (editor === getTrackedEditorIfValid()) {
        clearActiveEditor()
      }
    }

    sendCurrentSelection()
    sendCurrentlyOpenedFiles()
  }

  override fun focusGained(editor: Editor) {
    trackEditor(editor)
    sendCurrentSelection()
    sendCurrentlyOpenedFiles()
  }

  override fun dispose() {
    clearActiveEditor()
  }
}

data class SelectionDetails(val text: String?, val start: Int, val end: Int, val prefix: String, val suffix: String)
