package com.augmentcode.intellij.sidecar.clientinterfaces

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.utils.StringUtils
import com.augmentcode.intellij.utils.SymlinkUtil
import com.augmentcode.intellij.utils.fixOverlappingRelPath
import com.augmentcode.sidecar.rpc.clientInterfaces.FileType
import com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.isFile
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import java.nio.file.Paths
import kotlin.io.path.Path
import kotlin.io.path.name
import kotlin.io.path.pathString

class ClientWorkspaces(private val project: Project) {
  private val logger = thisLogger()

  fun readFile(path: String): FileReadResult {
    val qualifiedPathName = findQualifiedPathName(path) ?: return FileReadResult(null, null)
    val virtualFile =
      AugmentRoot.findFile(
        qualifiedPathName.rootPath,
        qualifiedPathName.relPath,
      ) ?: return FileReadResult(qualifiedPathName, null)

    val contents = safelyGetFileContent(virtualFile)
    return FileReadResult(qualifiedPathName, contents)
  }

  fun writeFile(
    rootPath: String,
    relPath: String,
    contents: String,
  ) {
    val existingFile = AugmentRoot.findFile(rootPath, relPath)
    WriteCommandAction.runWriteCommandAction(project) {
      try {
        if (existingFile != null) {
          safelyUpdateFileContent(existingFile, contents)
        } else {
          val fullPath = Path(rootPath, relPath)
          val file = VfsUtil.createDirectoryIfMissing(fullPath.parent.pathString)?.findOrCreateChildData(this, fullPath.name)
          if (file != null) {
            VfsUtil.saveText(file, contents)
          }
        }
      } catch (e: Exception) {
        logger.warn("Failed to write file: ${e.message}", e)
      }
    }
  }

  /**
   * This function will return the editor's in-memory file contents if open,
   * otherwise it'll return the on-disk file contents.
   */
  private fun safelyGetFileContent(virtualFile: VirtualFile): String =
    runReadAction {
      try {
        val text =
          FileDocumentManager.getInstance().getDocument(virtualFile)?.text
            ?: virtualFile.contentsToByteArray().toString(Charsets.UTF_8)
        val bom = virtualFile.bom
        if (bom != null && bom.contentEquals(StringUtils.utf8Bom)) {
          // If the file has a BOM, prefix the text with it
          return@runReadAction bom.toString(Charsets.UTF_8) + text
        }
        return@runReadAction text
      } catch (e: Exception) {
        // Fallback to disk content if document access fails for any reason
        // (e.g., file not accessible, encoding issues, or IntelliJ internal errors)
        virtualFile.contentsToByteArray().toString(Charsets.UTF_8)
      }
    }

  /**
   * This function will update the in-memory file in the user's editor if open,
   * otherwise update the on-disk version of the file.
   */
  private fun safelyUpdateFileContent(
    virtualFile: VirtualFile,
    rawContent: String,
  ) {
    val document =
      runReadAction {
        FileDocumentManager.getInstance().getDocument(virtualFile)
      }

    if (document != null) {
      // document.setText does not handle BOM, instead it's meant to be managed by the virtual file.

      // Remove the BOM from the content if it exists.
      val contentToWrite = rawContent.removePrefix("\uFEFF")

      // If the virtual file doesn't have a BOM, but the content does, write the BOM to the virtual file.
      if (virtualFile.bom == null && rawContent.startsWith("\uFEFF")) {
        virtualFile.bom = StringUtils.utf8Bom
      }

      document.setText(contentToWrite)
      FileDocumentManager.getInstance().saveDocument(document)
    } else {
      // VfsUtil will handle BOM correctly.
      VfsUtil.saveText(virtualFile, rawContent)
    }
  }

  /**
   * Converts a given path string into a [QualifiedPathName] object,
   * representing a path with a determined root and a relative path component.
   *
   * If the input `path` is absolute, it's resolved against the guessed project root.
   * If it's outside the project root, or if no project root can be determined,
   * this function returns `null`.
   * If the input `path` is relative, it's assumed to be relative to the
   * guessed project root.
   *
   * Key difference from `findQualifiedPathName` is that this function
   * does not attempt to resolve the path to a virtual file so it works for
   * paths that don't exist yet.
   *
   * @param path The path string to convert. Can be absolute or relative.
   * @return A [QualifiedPathName] containing the root path and relative path,
   *         or `null` if the path cannot be resolved or no root is found.
   */
  fun getQualifiedPathName(path: String): QualifiedPathName? {
    val root = AugmentRoot.findActiveProjectRoot(project)
    if (root == null) {
      return null
    }
    val rootPath = java.nio.file.Paths.get(root.path).normalize()

    if (isAbsolutePathName(path)) {
      val givenPath = java.nio.file.Paths.get(path).normalize()
      if (!givenPath.startsWith(rootPath)) {
        return null
      }
      val relPath = rootPath.relativize(givenPath).toString()
      return QualifiedPathName.newBuilder()
        .setRootPath(rootPath.toString())
        .setRelPath(relPath)
        .build()
    }

    return QualifiedPathName.newBuilder()
      .setRootPath(rootPath.toString())
      .setRelPath(path)
      .build()
  }

  /**
   * Finds files in the project that match the given glob pattern.
   *
   * @param includeGlob The glob pattern to match files against. Currently only supports "&#42;&#42;/filename" format.
   * @param excludeGlob The glob pattern to exclude files. Currently not supported.
   * @param maxResults The maximum number of results to return. If null, returns all matches.
   * @return A list of QualifiedPathName objects representing the matched files.
   */
  fun findFiles(
    includeGlob: String,
    excludeGlob: String?,
    maxResults: Int?,
  ): List<QualifiedPathName> {
    // Only support the pattern format "**/filename"
    val filenamePattern = Regex("""\*\*/(.+)""")
    val match = filenamePattern.matchEntire(includeGlob)

    if (match == null || excludeGlob != null) {
      // If the pattern doesn't match "**/filename", return empty list
      // TODO support other patterns
      return emptyList()
    }

    val filename = match.groupValues[1]
    val scope = GlobalSearchScope.projectScope(project)
    val results =
      ReadAction.compute<List<QualifiedPathName>, Throwable> {
        val findResults = mutableListOf<QualifiedPathName>()
        // Use FilenameIndex to efficiently find all files with the given name
        val files = FilenameIndex.getVirtualFilesByName(filename, scope)

        for (file in files) {
          if (maxResults != null && findResults.size >= maxResults) break

          val augmentPath = AugmentRoot.findRelativePathWithRoot(project, file) ?: continue
          findResults.add(
            QualifiedPathName.newBuilder()
              .setRootPath(augmentPath.rootPath)
              .setRelPath(augmentPath.relativePath)
              .build(),
          )
        }
        findResults
      }

    return results
  }

  private fun findQualifiedPathName(path: String): QualifiedPathName? {
    // If the path is absolute, use it to resolve the QualifiedPathName
    if (isAbsolutePathName(path)) {
      val virtualFile = LocalFileSystem.getInstance().findFileByPath(path) ?: return null
      val augmentPath = AugmentRoot.findRelativePathWithRoot(project, virtualFile) ?: return null
      return QualifiedPathName.newBuilder()
        .setRootPath(augmentPath.rootPath)
        .setRelPath(augmentPath.relativePath)
        .build()
    }

    val virtualFile = searchForRelativeFile(path) ?: return null
    val augmentPath = AugmentRoot.findRelativePathWithRoot(project, virtualFile) ?: return null
    return QualifiedPathName.newBuilder()
      .setRootPath(augmentPath.rootPath)
      .setRelPath(augmentPath.relativePath)
      .build()
  }

  private fun isAbsolutePathName(path: String): Boolean {
    return java.nio.file.Paths.get(path).isAbsolute
  }

  /**
   * This is not ideal, we should be working off of Augment roots, however
   * we don't have that information in the client yet, we hunt for roots
   * once we have a virtual file, so we need to find a virtual file first.
   */
  private fun searchForRelativeFile(path: String): VirtualFile? {
    val projectRootManager = com.intellij.openapi.roots.ProjectRootManager.getInstance(project)
    val contentRoots = projectRootManager.contentRoots

    // Filter out content roots that are inside other content roots
    // Any relative path should be relative to the outermost content root
    val outermostContentRoots = filterOutermostContentRoots(contentRoots.toList())

    for (root in outermostContentRoots) {
      // First try the path as-is
      val file = root.findFileByRelativePath(path)
      if (file != null) {
        return file
      }

      // If that fails, check if the path might be relative to a parent directory of root
      // E.g., root = /home/<USER>/augment/clients, path = clients/main.py
      // We should detect that "clients" overlaps with the root and use "main.py" as the relative path
      val correctedPath = fixOverlappingRelPath(root.path, path)
      if (correctedPath != null && correctedPath != path) {
        val correctedFile = root.findFileByRelativePath(correctedPath)
        if (correctedFile != null) {
          return correctedFile
        }
      }
    }

    return null
  }

  /**
   * Filters content roots to return only the outermost ones.
   * If one content root is inside another, only the outer one is kept.
   *
   * @param contentRoots List of content roots to filter
   * @return List of content roots with nested ones removed
   */
  private fun filterOutermostContentRoots(contentRoots: List<VirtualFile>): List<VirtualFile> {
    if (contentRoots.size <= 1) {
      return contentRoots
    }

    val result = mutableListOf<VirtualFile>()

    for (candidate in contentRoots) {
      var isInnerRoot = false

      // Check if this candidate is inside any other content root
      for (other in contentRoots) {
        if (candidate != other && isPathInside(candidate.path, other.path)) {
          isInnerRoot = true
          break
        }
      }

      // Only add if it's not inside any other content root
      if (!isInnerRoot) {
        result.add(candidate)
      }
    }

    return result
  }

  /**
   * Checks if one path is inside another path.
   *
   * @param innerPath The path that might be inside
   * @param outerPath The path that might contain the inner path
   * @return true if innerPath is inside outerPath, false otherwise
   */
  private fun isPathInside(
    innerPath: String,
    outerPath: String,
  ): Boolean {
    val innerNormalized = Paths.get(innerPath).normalize()
    val outerNormalized = Paths.get(outerPath).normalize()

    return innerNormalized.startsWith(outerNormalized) && innerNormalized != outerNormalized
  }

  /**
   * Gets information about a file or directory at the given path.
   *
   * @param path The path to get information about. Can be absolute or relative.
   * @return A PathInfo object containing the file type, qualified path name, and existence status.
   */
  fun getPathInfo(path: String): PathInfo {
    // If the path is absolute, use it directly
    val virtualFile =
      if (isAbsolutePathName(path)) {
        LocalFileSystem.getInstance().findFileByPath(path)
      } else {
        // Otherwise, search for it as a relative path
        searchForRelativeFile(path)
      }

    // If the file doesn't exist, return a PathInfo with exists=false
    if (virtualFile == null) {
      return PathInfo(null, null, false)
    }

    val fileType = getFileType(virtualFile)

    // Get the qualified path name
    val augmentPath = AugmentRoot.findRelativePathWithRoot(project, virtualFile)
    val qualifiedPath =
      if (augmentPath != null) {
        QualifiedPathName.newBuilder()
          .setRootPath(augmentPath.rootPath)
          .setRelPath(augmentPath.relativePath)
          .build()
      } else {
        null
      }

    return PathInfo(fileType, qualifiedPath, true)
  }

  /**
   * Lists the contents of a directory recursively up to the specified depth.
   *
   * @param path The path to the directory to list. Can be absolute or relative.
   * @param depth The maximum depth to recurse into subdirectories (default: 2).
   * @param showHidden Whether to include hidden files and directories (default: false).
   * @return A ListDirectoryResult containing the list of relative paths and any error message.
   */
  fun listDirectory(
    path: String,
    depth: Int = 2,
    showHidden: Boolean = false,
  ): ListDirectoryResult {
    // Get the qualified path to ensure it's within the workspace
    val qualifiedPath = getQualifiedPathName(path)
    if (qualifiedPath == null) {
      return ListDirectoryResult(emptyList(), "Path is outside the workspace")
    }

    // Find the virtual file for the directory
    val virtualFile = LocalFileSystem.getInstance().findFileByPath(qualifiedPath.rootPath + "/" + qualifiedPath.relPath)
    if (virtualFile == null) {
      return ListDirectoryResult(emptyList(), "Directory not found")
    }

    if (!virtualFile.isDirectory) {
      return ListDirectoryResult(emptyList(), "Path is not a directory")
    }

    try {
      val results = mutableListOf<String>()
      listDirectoryRecursive(virtualFile, virtualFile, 0, depth, results, showHidden)

      // Sort results for consistent output
      results.sort()
      return ListDirectoryResult(results, null)
    } catch (e: Exception) {
      return ListDirectoryResult(emptyList(), "Directory cannot be read: ${e.message}")
    }
  }

  /**
   * Recursively lists directory contents.
   */
  private fun listDirectoryRecursive(
    baseDir: VirtualFile,
    currentDir: VirtualFile,
    currentDepth: Int,
    maxDepth: Int,
    results: MutableList<String>,
    showHidden: Boolean,
  ) {
    if (currentDepth >= maxDepth) {
      return
    }

    try {
      val children = currentDir.children ?: return

      for (child in children) {
        // Skip hidden files and directories unless showHidden is true
        // TODO support windows hidden files
        if (!showHidden && child.name.startsWith(".")) {
          continue
        }

        // Skip symlinks
        if (SymlinkUtil.isSymlink(child)) {
          continue
        }

        // Calculate relative path from base directory
        val relativePath = getRelativePath(baseDir.path, child.path)
        if (relativePath.isNotEmpty()) {
          results.add(relativePath)
        }

        // If it's a directory and we haven't reached max depth, recurse
        // current child is at depth currentDepth + 1
        if (child.isDirectory && currentDepth + 1 < maxDepth) {
          listDirectoryRecursive(baseDir, child, currentDepth + 1, maxDepth, results, showHidden)
        }
      }
    } catch (e: Exception) {
      // Ignore errors for individual directories (e.g., permission denied)
      // This matches the behavior of the VSCode implementation
    }
  }

  private fun getFileType(virtualFile: VirtualFile): FileType {
    if (SymlinkUtil.isSymlink(virtualFile)) {
      // For symlinks, we need to check the target type using the file system
      val targetFile = virtualFile.canonicalFile
      return when {
        targetFile == null -> FileType.SYMBOLIC_LINK
        targetFile.isFile -> FileType.SYMBOLIC_LINK_TO_FILE
        targetFile.isDirectory -> FileType.SYMBOLIC_LINK_TO_DIRECTORY
        else -> FileType.SYMBOLIC_LINK
      }
    }
    return when {
      virtualFile.isDirectory -> FileType.DIRECTORY
      virtualFile.isFile -> FileType.FILE
      else -> FileType.UNKNOWN
    }
  }

  /**
   * Calculate relative path between two absolute paths.
   */
  private fun getRelativePath(
    basePath: String,
    targetPath: String,
  ): String {
    val base = Paths.get(basePath).normalize()
    val target = Paths.get(targetPath).normalize()

    return try {
      base.relativize(target).toString()
    } catch (e: Exception) {
      // If we can't calculate relative path, return empty string
      ""
    }
  }
}

data class FileReadResult(
  val qualifiedPath: QualifiedPathName?,
  val contents: String?,
)

/**
 * Represents information about a file or directory path.
 *
 * @property type The type of the file (file, directory, symbolic link, or unknown).
 * @property filepath The qualified path name of the file.
 * @property exists Whether the file exists.
 */
data class PathInfo(
  val type: FileType?,
  val filepath: QualifiedPathName?,
  val exists: Boolean?,
)

/**
 * Represents the result of listing a directory.
 *
 * @property entries The list of relative paths found in the directory.
 * @property errorMessage An optional error message if the operation failed.
 */
data class ListDirectoryResult(
  val entries: List<String>,
  val errorMessage: String?,
)
