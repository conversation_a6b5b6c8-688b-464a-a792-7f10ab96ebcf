package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.featureflags.DefaultFeatureFlags
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.utils.CoalescingExecutor
import com.augmentcode.intellij.utils.ProperThreadUsageChecker
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CoroutineScope
import public_api.PublicApi
import kotlin.coroutines.cancellation.CancellationException

/**
 * This class is responsible for updating the plugin state. It uses a coalescing executor
 * to ensure that only one instance of the state update is running at a time.
 *
 * It emits changes via the onStateChange callback function.
 */
class AppStateExecutor(val cs: CoroutineScope, parentDisposable: Disposable, val onStateChange: (PluginContext, PluginState) -> Unit) {
  companion object {
    private val logger = thisLogger()
    val defaultContext = PluginContext(false, DefaultFeatureFlags, null)
  }

  private val executor =
    CoalescingExecutor(cs, parentDisposable) {
      run()
    }

  fun updateState() {
    executor.trigger()
  }

  private suspend fun run() {
    // Should not be on EDT so log a warning if that is not the case.
    ProperThreadUsageChecker.shouldNotBeOnEDT("PluginStateExecutor.run")

    logger.info("Starting PluginState initialization...")
    onStateChange(defaultContext, PluginState.INITIALIZING)

    try {
      logger.info("Checking API availability...")
      if (!AugmentAPI.instance.available()) {
        logger.info("API not available, need to sign in")
        onStateChange(defaultContext, PluginState.SIGN_IN_REQUIRED)
        return
      }

      logger.info("Fetching model info...")
      val modelInfo = AugmentAPI.instance.fetchModelInfoNoCache()
      if (modelInfo == null) {
        logger.warn("Failed to fetch model info")
        onStateChange(defaultContext, PluginState.GET_MODEL_INFO_FAILED)
        return
      }

      // Using the model info, initialize the flags etc. in the context
      val context = createContext(modelInfo)
      onStateChange(context, PluginState.ENABLED)
      logger.info("PluginState initialization successful.")
    } catch (e: CancellationException) {
      // Re-throw the exception to cancel coroutines safely
      throw e
    } catch (e: Exception) {
      // TODO (mattgauntseo): 401 (unauthenticated) errors are currently swallowed by the API layer.
      //                      We should throw an exception and show a useful status to the user.

      logger.warn("Failed to initialize plugin", e)
      onStateChange(defaultContext, PluginState.FAILED)
    }
  }

  private fun createContext(modelConfig: PublicApi.GetModelsResponse): PluginContext {
    logger.info("Creating context...")
    val flags = FeatureFlags.fromGetModelsResponse(modelConfig)
    val model = AugmentModel.fromGetModelsResponse(modelConfig)
    return PluginContext(true, flags, model)
  }

  internal fun reset() {
    executor.reset()
  }
}
