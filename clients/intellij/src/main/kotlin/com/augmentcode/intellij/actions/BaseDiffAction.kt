package com.augmentcode.intellij.actions

import com.augmentcode.api.SmartPasteResolution
import com.intellij.diff.editor.DiffContentVirtualFile
import com.intellij.diff.tools.fragmented.UnifiedDiffViewer
import com.intellij.diff.tools.simple.SimpleDiffViewer
import com.intellij.diff.tools.util.DiffDataKeys
import com.intellij.diff.util.Side
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.DumbAwareAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import javax.swing.Icon

sealed class BaseDiffAction(
  text: String,
  description: String,
  icon: Icon,
  private val targetFile: VirtualFile,
  private val textToApply: String,
  private val resolutionContainer: SmartPasteResolution,
) : DumbAwareAction(text, description, icon) {
  override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.EDT

  override fun update(e: AnActionEvent) {
    e.presentation.isEnabled = true
  }

  override fun actionPerformed(e: AnActionEvent) {
    val viewer = e.getData(DiffDataKeys.DIFF_VIEWER) ?: return
    when (viewer) {
      is SimpleDiffViewer -> handleSimpleDiffViewer(viewer, textToApply)
      is UnifiedDiffViewer -> handleUnifiedDiffViewer(viewer, textToApply)
    }

    // We modify fields on the resolutionContainer here, and that same resolutionContainer is sent in
    // SmartPasteDiffExtension when the diff viewer is disposed.
    resolutionContainer.isAcceptAll = this is ApplyAllChangesAction
    resolutionContainer.isRejectAll = this is RejectAllChangesAction

    closeDiffViewer(e.project!!)
  }

  /**
   * Closes the diff viewer and opens the target file.
   */
  private fun closeDiffViewer(project: Project) {
    val editorManager = FileEditorManager.getInstance(project)
    // Can assume the diff viewer is the selected file since we're in the diff viewer toolbar
    val currentFile = editorManager.selectedFiles.firstOrNull() ?: return
    // Close the diff viewer and open the diffed file
    if (currentFile is DiffContentVirtualFile) {
      editorManager.closeFile(currentFile)
    }
    // This will focus on an existing tab if file is already open
    editorManager.openFile(targetFile, true)
  }

  private fun handleSimpleDiffViewer(
    viewer: SimpleDiffViewer,
    text: String,
  ) {
    val document = viewer.editor1.document
    WriteCommandAction.runWriteCommandAction(viewer.project) {
      document.setText(text)
    }
  }

  private fun handleUnifiedDiffViewer(
    viewer: UnifiedDiffViewer,
    text: String,
  ) {
    val document = viewer.getDocument(Side.LEFT)
    WriteCommandAction.runWriteCommandAction(viewer.project) {
      document.setText(text)
      viewer.rediff()
    }
  }

  override fun displayTextInToolbar(): Boolean = true
}
