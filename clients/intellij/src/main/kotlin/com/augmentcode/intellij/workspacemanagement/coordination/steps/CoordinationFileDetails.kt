package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.intellij.openapi.vfs.VirtualFile

data class CoordinationFileDetails(
  val virtualFile: VirtualFile,
  val rootPath: String,
  val relPath: String,
)

data class CoordinationFileDetailsWithBlob(
  val fileDetails: CoordinationFileDetails,
  val remoteBlobName: String,
  val remoteSyncTimestamp: Long = System.currentTimeMillis(),
)
