package com.augmentcode.intellij.webviews

import kotlinx.coroutines.runBlocking
import org.cef.callback.CefCallback
import org.cef.handler.CefLoadHandler
import org.cef.handler.CefResourceHandler
import org.cef.misc.IntRef
import org.cef.misc.StringRef
import org.cef.network.CefRequest
import org.cef.network.CefResponse
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.net.URLConnection

open class CustomResourceHandler : CefResourceHandler {
  private var mimeType: String = "text/css"
  private var content: InputStream? = null

  override fun processRequest(
    cefRequest: CefRequest,
    cefCallback: CefCallback,
  ): Boolean {
    val pathToResource = cefRequest.url.replace(WebviewFactory.WEBVIEW_BASE_URL, "webviews/")
    if (pathToResource == "webviews/${AugmentWebview.INTELLIJ_STYLES_CSS}") {
      runBlocking {
        content = ByteArrayInputStream(StylesManager.getStyles().toByteArray())
      }
    } else {
      val resourceURL = this::class.java.classLoader.getResource(pathToResource) ?: return false
      try {
        mimeType = URLConnection.guessContentTypeFromName(pathToResource)
      } catch (e: Exception) {
        // Handle font files and other special cases
        mimeType = getMimeTypeByExtension(pathToResource)
      }
      // Optionally mutate the content
      val filteredContent = contentFilter(resourceURL.readBytes(), mimeType, cefRequest.url)
      content = ByteArrayInputStream(filteredContent)
    }
    cefCallback.Continue()
    return true
  }

  private fun getMimeTypeByExtension(path: String): String {
    return when {
      path.endsWith(".woff") -> "font/woff"
      else -> "application/octet-stream" // Default fallback
    }
  }

  /**
   * Override this to mutate the content before sending it to the browser.
   * @param input The content of the resource.
   * @param mimeType The mime type of the resource.
   * @param requestUrl The original url of the request as it came in to processRequest.
   */
  open fun contentFilter(
    input: ByteArray,
    mimeType: String,
    requestUrl: String,
  ): ByteArray {
    return input
  }

  override fun getResponseHeaders(
    cefResponse: CefResponse,
    responseLength: IntRef,
    redirectUrl: StringRef,
  ) {
    if (content == null) {
      cefResponse.error = CefLoadHandler.ErrorCode.ERR_FILE_NOT_FOUND
      cefResponse.status = 404
    } else {
      cefResponse.mimeType = mimeType
      cefResponse.status = 200
    }
  }

  override fun readResponse(
    dataOut: ByteArray,
    designedBytesToRead: Int,
    bytesRead: IntRef,
    callback: CefCallback,
  ): Boolean {
    val availableSize = content?.available() ?: return false
    return if (availableSize > 0) {
      val maxBytesToRead = minOf(availableSize, designedBytesToRead)
      val realNumberOfReadBytes = content!!.read(dataOut, 0, maxBytesToRead)
      bytesRead.set(realNumberOfReadBytes)
      true
    } else {
      content!!.close()
      false
    }
  }

  override fun cancel() {
    content = null
  }
}
