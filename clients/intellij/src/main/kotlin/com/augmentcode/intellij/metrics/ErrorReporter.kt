package com.augmentcode.intellij.metrics

import com.augmentcode.api.ReportErrorRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

@Service(Service.Level.APP)
class ErrorReporter(
  private val cs: CoroutineScope,
) : Disposable {
  companion object {
    fun getInstance(): ErrorReporter = ApplicationManager.getApplication().getService(ErrorReporter::class.java)

    internal fun sanitizeStackTrace(stackTrace: String): String = stackTrace.replace(Regex(" \\(/[^()]+\\)"), "")
  }

  internal var metricsJob: Job? = null
  private var errorChannel = Channel<ReportErrorRequest>(Channel.BUFFERED, BufferOverflow.DROP_OLDEST)

  init {
    startProcessing()
  }

  override fun dispose() {
    errorChannel.close()
    metricsJob?.cancel()
    metricsJob = null
  }

  internal fun reset() {
    errorChannel.close()
    errorChannel = Channel(Channel.BUFFERED, BufferOverflow.DROP_OLDEST)
    metricsJob?.cancel()
    startProcessing()
  }

  private fun startProcessing() {
    metricsJob =
      cs.launch {
        while (isActive) {
          try {
            val error = errorChannel.receive()
            AugmentAPI.instance.reportError(error)
          } catch (e: CancellationException) {
            // expected when shutting down and must be propagated
            throw e
          } catch (e: Exception) {
            thisLogger().warn("Failed to report error", e)
          }
        }
      }
  }

  /**
   * Queue an error report for upload.
   * @param originalRequestId The original request ID, if any.
   * @param sanitizedMessage The sanitized error message (MUST NOT CONTAIN user metadata)
   * @param stackTrace The stack trace of the error
   * @param diagnostics Additional unstructured diagnostic information (user metadata goes here)
   */
  fun reportError(
    originalRequestId: String?,
    sanitizedMessage: String,
    stackTrace: String,
    diagnostics: List<Pair<String, String>>,
  ) {
    val sanitizedStackTrace = sanitizeStackTrace(stackTrace)

    val reportErrorRequest =
      ReportErrorRequest().apply {
        this.originalRequestId = originalRequestId
        this.sanitizedMessage = sanitizedMessage
        this.stackTrace = sanitizedStackTrace
        this.diagnostics = diagnostics.map { ReportErrorRequest.Diagnostic(it.first, it.second) }
      }

    val result = errorChannel.trySend(reportErrorRequest)
    if (result.isFailure) {
      thisLogger().warn("Error channel full, dropping error report: $sanitizedMessage")
    }
  }
}
