package com.augmentcode.intellij.workspacemanagement.indexing

import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.indexing.DataIndexer
import com.intellij.util.indexing.FileContent

/**
 * This indexer has one purpose, to take files from IntelliJ's indexing pipeline
 * and pass them to the WorkspaceManagement service for processing.
 */
class WorkspaceIndexer : DataIndexer<String, Void, FileContent> {
  private val logger = thisLogger()

  override fun map(inputData: FileContent): Map<String?, Void?> {
    // We use url to make testing with light virtual files easier.
    // We rely on `isInLocalFileSystem` as a filter in WorkspaceIndex
    // to filter out light virtual files in normal usage.
    val uri = inputData.file.url
    // The enqueue function will do nothing if V3 indexing is disabled.
    WorkspaceCoordinatorService.getInstance(inputData.project).enqueueFileForProcessing(uri)
    return mapOf(uri to null)
  }
}
