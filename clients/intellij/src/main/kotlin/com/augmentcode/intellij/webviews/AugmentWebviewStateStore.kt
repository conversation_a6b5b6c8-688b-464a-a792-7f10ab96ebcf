package com.augmentcode.intellij.webviews

import com.intellij.openapi.components.*
import com.intellij.openapi.project.Project
import com.intellij.util.ThreeState

@Service(Service.Level.PROJECT)
@State(
  name = "AugmentWebviewStateStore",
  storages = [
    Storage(
      "AugmentWebviewStateStore.xml",
      // Make sure we save the state lazily every 5 minutes or upon project close
      // For more details see documentation of useSaveThreshold here https://github.com/JetBrains/intellij-community/blob/7ed5e2854507784b6f2fbbcde700e0bf353ea34c/platform/projectModel-api/src/com/intellij/openapi/components/Storage.java#L57-L65
      useSaveThreshold = ThreeState.YES,
    ),
  ],
)
class AugmentWebviewStateStore : SimplePersistentStateComponent<AugmentWebviewState>(AugmentWebviewState()) {
  companion object {
    fun getInstance(project: Project): AugmentWebviewStateStore {
      return project.service<AugmentWebviewStateStore>()
    }
  }

  fun get(key: AugmentWebviewStateKey): String? {
    return state.stateMap[key]
  }

  fun set(
    key: AugmentWebviewStateKey,
    value: String?,
  ) {
    if (value == null) {
      state.stateMap.remove(key)
    } else {
      state.stateMap[key] = value
    }
  }
}

enum class AugmentWebviewStateKey {
  CHAT_STATE,
  SETTINGS_STATE,
  PREFERENCES_STATE,
}

class AugmentWebviewState : BaseState() {
  // map is supposed to track its own modification counts so we don't
  // need to call incrementModificationCount
  var stateMap by map<AugmentWebviewStateKey, String>()
}
