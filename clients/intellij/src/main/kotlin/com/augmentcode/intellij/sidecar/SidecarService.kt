package com.augmentcode.intellij.sidecar

import com.augmentcode.api.*
import com.augmentcode.api.AgentCodebaseRetrievalRequest
import com.augmentcode.api.AgentInterruptionData
import com.augmentcode.api.AgentRequestEvent
import com.augmentcode.api.AgentRequestEventData
import com.augmentcode.api.AgentSessionEvent
import com.augmentcode.api.AgentTracingData
import com.augmentcode.api.ChatHistorySummarizationData
import com.augmentcode.api.FirstTokenTimingData
import com.augmentcode.api.InitialOrientationData
import com.augmentcode.api.MemoriesFileOpenData
import com.augmentcode.api.RememberToolCallData
import com.augmentcode.api.RemoteToolId
import com.augmentcode.api.StringStats
import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.getUserAgent
import com.augmentcode.intellij.api.toTruncatedApiExchange
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.idea.AugmentFileStore
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.memories.MemoriesService
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.settings.AugmentIntegrationsConfig
import com.augmentcode.intellij.sidecar.clientinterfaces.ClientWorkspaces
import com.augmentcode.intellij.sidecar.clientinterfaces.PluginFileStore
import com.augmentcode.intellij.sidecar.clientinterfaces.PluginStateForSidecar
import com.augmentcode.intellij.sidecar.services.DiffViewerService
import com.augmentcode.intellij.sidecar.tools.ToolsManager
import com.augmentcode.intellij.sidecarrpc.Connection
import com.augmentcode.intellij.sidecarrpc.IOConnection
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.augmentcode.sidecar.rpc.*
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.chat.ChatStreamRequest
import com.augmentcode.sidecar.rpc.chat.ChatStreamResponse
import com.augmentcode.sidecar.rpc.clientInterfaces.*
import com.augmentcode.sidecar.rpc.clientInterfaces.CheckToolSafetyResponse
import com.augmentcode.sidecar.rpc.tools.*
import com.google.gson.GsonBuilder
import com.google.protobuf.Empty
import com.google.protobuf.Struct
import com.google.protobuf.kotlin.toByteString
import com.intellij.execution.process.OSProcessHandler
import com.intellij.execution.process.ProcessEvent
import com.intellij.execution.process.ProcessListener
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.PathManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.BaseProjectDirectories.Companion.getBaseDirectories
import com.intellij.openapi.project.Project
import com.intellij.platform.ide.progress.withBackgroundProgress
import com.intellij.util.EnvironmentUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.withTimeout
import org.jetbrains.annotations.VisibleForTesting
import java.io.File
import java.nio.file.Path
import java.util.concurrent.TimeUnit
import kotlin.io.path.createTempDirectory
import com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalRequest as ProtoAgentCodebaseRetrievalRequest
import com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalResponse as ProtoAgentCodebaseRetrievalResponse
import com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData as AgentTracingDataProto
import com.augmentcode.sidecar.rpc.tools.RemoteToolId as ProtoRemoteToolId

private fun ProtoRemoteToolId.toApi(): RemoteToolId {
  return RemoteToolId.values().find { it.value == this.number } ?: RemoteToolId.Unknown
}

// once started, how long to wait until sidecar is initialized and ready
const val SIDECAR_INITIALIZE_TIMEOUT_MS = 30_000L

@Service(Service.Level.PROJECT)
class SidecarService(
  private val project: Project,
  private val cs: CoroutineScope,
) : Disposable, ProcessListener {
  private val logger = Logger.getInstance(SidecarService::class.java)

  /**
   * Validates that agent request event data follows the protobuf oneof constraint.
   * @return true if valid (0 or 1 event data types), false if invalid (multiple types)
   */
  private fun isValidAgentRequestEvent(
    eventData: com.augmentcode.sidecar.rpc.clientInterfaces.AgentRequestEventData,
    eventName: String,
    conversationId: String,
    requestId: String,
  ): Boolean {
    val eventDataTypes = mutableListOf<String>()
    if (eventData.hasChatHistorySummarizationData()) eventDataTypes.add("chat_history_summarization_data")
    if (eventData.hasFirstTokenTimingData()) eventDataTypes.add("first_token_timing_data")

    if (eventDataTypes.size > 1) {
      logger.error(
        "Invalid agent request event: multiple event data types present: ${eventDataTypes.joinToString(", ")}. " +
          "This violates the protobuf oneof constraint and indicates a bug in the client code. " +
          "Event name: $eventName, conversation_id: $conversationId, request_id: $requestId",
      )
      return false
    }
    return true
  }

  /**
   * Validates that agent session event data follows the protobuf oneof constraint.
   * @return true if valid (0 or 1 event data types), false if invalid (multiple types)
   */
  private fun isValidAgentSessionEvent(
    eventData: EventData,
    eventName: String,
    conversationId: String,
  ): Boolean {
    val eventDataTypes = mutableListOf<String>()
    if (eventData.hasAgentInterruptionData()) eventDataTypes.add("agent_interruption_data")
    if (eventData.hasRememberToolCallData()) eventDataTypes.add("remember_tool_call_data")
    if (eventData.hasMemoriesFileOpenData()) eventDataTypes.add("memories_file_open_data")
    if (eventData.hasInitialOrientationData()) eventDataTypes.add("initial_orientation_data")
    if (eventData.hasClassifyAndDistillData()) eventDataTypes.add("classify_and_distill_data")
    if (eventData.hasFlushMemoriesData()) eventDataTypes.add("flush_memories_data")

    if (eventDataTypes.size > 1) {
      logger.error(
        "Invalid agent session event: multiple event data types present: ${eventDataTypes.joinToString(", ")}. " +
          "This violates the protobuf oneof constraint and indicates a bug in the client code. " +
          "Event name: $eventName, conversation_id: $conversationId",
      )
      return false
    }
    return true
  }

  private var process: Process? = null

  private var initialChatMode: ChatMode = ChatMode.CHAT
  private var toolsManager: ToolsManager? = null
  private var pluginState = PluginStateForSidecar()
  private var pluginFileStore = PluginFileStore(project)
  private var connection: Connection? = null
  private val gson = GsonBuilder().create()

  private var serviceState = SidecarState.SIDECAR_STOPPED

  companion object {
    fun getInstance(project: Project): SidecarService {
      return project.service<SidecarService>()
    }

    // Copy the prebuilds directory (contains native modules)
    val prebuildFilesToCopy =
      listOf<String>(
        // darwin-x64+arm64
        "darwin-x64+arm64/classic-level.node",
        // linux-arm
        "linux-arm/classic-level.armv6.node",
        "linux-arm/classic-level.armv7.node",
        // linux-arm64
        "linux-arm64/classic-level.armv8.node",
        // linux-x64
        "linux-x64/classic-level.musl.node",
        "linux-x64/classic-level.node",
        // win32-ia32
        "win32-ia32/classic-level.node",
        // win32-x64
        "win32-x64/classic-level.node",
      )
  }

  // Returns true when the sidecar is initialized and alive.
  fun isAvailable(): Boolean {
    return serviceState == SidecarState.SIDECAR_RUNNING && (process?.isAlive == true)
  }

  suspend fun startServer() {
    withBackgroundProgress(project, AugmentBundle.message("augment.starting.agents.sidecar.title"), false) {
      startBlockingServer()
    }
  }

  private fun updateState(
    state: SidecarState,
    status: String? = null,
  ) {
    if (state == serviceState) {
      logger.info("Sidecar state is already $state, not updating")
      return
    }
    logger.info("Updating sidecar state to $state: $status")
    serviceState = state
  }

  @VisibleForTesting
  internal suspend fun startBlockingServer() {
    try {
      if (process != null || connection != null) {
        // Service is already running
        return
      }

      updateState(SidecarState.SIDECAR_STARTING)

      val nodeInstallationService = NodeInstallationService.instance
      val nodeBinaryDeferrable =
        nodeInstallationService.getNodeBinary()
      val nodeBinary = nodeBinaryDeferrable.await()
      if (nodeBinary == null) {
        logger.warn("Unable to find node binary")
        updateState(SidecarState.SIDECAR_ERROR, "Unable to find node binary")
        return
      }

      if (AugmentOAuthState.instance.getCredentials() == null) {
        logger.info("Not starting sidecar, not signed in")
        updateState(SidecarState.SIDECAR_ERROR, "Not starting sidecar, not signed in")
        return
      }

      logger.info("Starting sidecar...")

      // Create a temporary directory to store the entire sidecar bundle
      val tempDir = createTempDirectory("augment-sidecar")

      // Copy the main JavaScript file
      val indexJsStream =
        javaClass.classLoader.getResourceAsStream("sidecar/index.js")
          ?: throw RuntimeException("Failed to load sidecar index.js resource")
      val tempFile = tempDir.resolve("index.js").toFile()
      tempFile.outputStream().use { output ->
        indexJsStream.copyTo(output)
      }
      tempFile.setExecutable(true)

      copyResourceDirectory("sidecar/prebuilds", tempDir.resolve("prebuilds"), prebuildFilesToCopy)

      logger.info("Spawning sidecar process...")

      // Get the log directory and create a path for the sidecar log file
      val logDir = PathManager.getLogPath()
      val sidecarLogFile = File(logDir, "augment-sidecar.log").absolutePath
      logger.info("Sidecar logs will be written to: $sidecarLogFile")

      val sidecarCommand = getCommandForSidecar(nodeBinary, tempFile, sidecarLogFile)
      this.logger.info("Running sidecar command: $sidecarCommand")

      process =
        ProcessBuilder(sidecarCommand)
          .redirectError(ProcessBuilder.Redirect.PIPE)
          .apply { environment().putAll(EnvironmentUtil.getEnvironmentMap()) }
          .start().also {
            OSProcessHandler(it, "augment-sidecar").addProcessListener(this)
          }

      connection = IOConnection(process!!.inputStream, process!!.outputStream, cs)
      toolsManager = ToolsManager(project, connection!!, initialChatMode)

      // Client interfaces
      val clientWorkspaces = ClientWorkspaces(project)

      // The augmentcode/client-workspaces/* methods
      connection!!.onRequest("augmentcode/client-workspaces/getcwd", Empty.getDefaultInstance().javaClass) { _ ->
        GetCWDResponse.newBuilder().setCwd(this.getCwd()).build()
      }
      connection!!.onRequest("augmentcode/client-workspaces/getWorkspaceRoot", Empty.getDefaultInstance().javaClass) { _ ->
        GetWorkspaceRootResponse.newBuilder().setWorkspaceRoot(this.getWorkspaceRoot()).build()
      }
      connection!!.onRequest(
        "augmentcode/client-workspaces/readFile",
        ReadFileRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(ReadFileRequest::class.java)

        val fileDetails = clientWorkspaces.readFile(request.filePath)

        val builder = ReadFileResponse.newBuilder()
        if (fileDetails.qualifiedPath != null) {
          builder.setFilepath(fileDetails.qualifiedPath)
        }
        if (fileDetails.contents != null) {
          builder.setContents(fileDetails.contents)
        }
        builder.build()
      }

      connection!!.onRequest(
        "augmentcode/client-workspaces/writeFile",
        WriteFileRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(WriteFileRequest::class.java)

        if (request.filePath == null) {
          return@onRequest Empty.getDefaultInstance()
        }

        // Workaround so memory file updates are updated in the editor immediately
        if (request.filePath.relPath == MemoriesService.getInstance(project).getMemoriesAbsPath()) {
          MemoriesService.getInstance(project).updateMemories(request.contents)
          return@onRequest Empty.getDefaultInstance()
        }

        val rootPath = request.filePath.rootPath
        val relPath = request.filePath.relPath

        try {
          clientWorkspaces.writeFile(rootPath, relPath, request.contents)
        } catch (e: Exception) {
          logger.warn("Failed to write file: ${e.message}", e)
        }

        Empty.getDefaultInstance()
      }

      connection!!.onRequest(
        "augmentcode/client-workspaces/deleteFile",
        DeleteFileRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(DeleteFileRequest::class.java)

        if (request.filePath == null) {
          return@onRequest Empty.getDefaultInstance()
        }

        val rootPath = request.filePath.rootPath
        val relPath = request.filePath.relPath

        WriteCommandAction.runWriteCommandAction(project) {
          AugmentRoot.findFile(rootPath, relPath)?.delete(this)
        }

        Empty.getDefaultInstance()
      }

      connection!!.onRequest(
        "augmentcode/client-workspaces/getQualifiedPathName",
        GetQualifiedPathNameRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(GetQualifiedPathNameRequest::class.java)

        val qualifiedPath = ClientWorkspaces(project).getQualifiedPathName(request.path)

        val builder = GetQualifiedPathNameResponse.newBuilder()
        if (qualifiedPath != null) {
          builder.setFilepath(qualifiedPath)
        }
        builder.build()
      }

      connection!!.onRequest(
        "augmentcode/client-workspaces/findFiles",
        FindFilesRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(FindFilesRequest::class.java)

        val includeGlob = request.includeGlob
        val excludeGlob = if (request.excludeGlob.isNullOrEmpty()) null else request.excludeGlob
        val maxResults = if (request.maxResults <= 0) null else request.maxResults

        logger.info("Finding files with pattern: $includeGlob, exclude: $excludeGlob, max: $maxResults")

        val files = ClientWorkspaces(project).findFiles(includeGlob, excludeGlob, maxResults)

        logger.info("Found ${files.size} files matching pattern $includeGlob")

        FindFilesResponse.newBuilder()
          .addAllFiles(files)
          .build()
      }

      connection!!.onRequest(
        "augmentcode/client-workspaces/getPathInfo",
        GetPathInfoRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(GetPathInfoRequest::class.java)

        val path = request.path
        logger.info("Getting path info for: $path")

        val pathInfo = ClientWorkspaces(project).getPathInfo(path)

        val builder = GetPathInfoResponse.newBuilder()
        if (pathInfo.type != null) {
          builder.setType(pathInfo.type)
        }
        if (pathInfo.filepath != null) {
          builder.setFilepath(pathInfo.filepath)
        }
        if (pathInfo.exists != null) {
          builder.setExists(pathInfo.exists)
        }
        builder.build()
      }

      connection!!.onRequest(
        "augmentcode/client-workspaces/listDirectory",
        ListDirectoryRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(ListDirectoryRequest::class.java)

        val path = request.path
        val depth = request.depth
        val showHidden = request.showHidden
        logger.info("Listing directory: $path, depth: $depth, showHidden: $showHidden")

        val result = ClientWorkspaces(project).listDirectory(path, depth, showHidden)

        val builder = ListDirectoryResponse.newBuilder()
        builder.addAllEntries(result.entries)
        if (result.errorMessage != null) {
          builder.setErrorMessage(result.errorMessage)
        }
        builder.build()
      }

      // the augmentcode/plugin-filestore/* methods
      connection!!.onRequest(
        "augmentcode/plugin-filestore/loadAsset",
        LoadAsset.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(LoadAsset::class.java)
        val asset = pluginFileStore.loadAsset(request.path) ?: return@onRequest LoadAssetResponse.newBuilder().build()
        LoadAssetResponse.newBuilder().setContents(asset.toByteString()).build()
      }
      connection!!.onRequest(
        "augmentcode/plugin-filestore/saveAsset",
        SaveAsset.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(SaveAsset::class.java)
        pluginFileStore.saveAsset(request.path, request.contents.toByteArray())
        Empty.getDefaultInstance()
      }
      connection!!.onRequest(
        "augmentcode/plugin-filestore/deleteAsset",
        DeleteAsset.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(DeleteAsset::class.java)
        pluginFileStore.deleteAsset(request.path)
        Empty.getDefaultInstance()
      }
      connection!!.onRequest(
        "augmentcode/plugin-filestore/listAssets",
        ListAssets.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(ListAssets::class.java)
        val assets = pluginFileStore.listAssets(request.prefix)
        ListAssetsResponse.newBuilder().addAllPaths(assets).build()
      }
      connection!!.onRequest(
        "augmentcode/plugin-filestore/getAssetPath",
        GetAssetPath.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(GetAssetPath::class.java)
        val absolutePath = pluginFileStore.getAssetAbsPath(request.path)
        GetAssetPathResponse.newBuilder().setAbsolutePath(absolutePath).build()
      }

      // The augmentcode/plugin-state-for-sidecar/* methods
      connection!!.onRequest(
        "augmentcode/plugin-state-for-sidecar/get-value",
        GetStateValueRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(GetStateValueRequest::class.java)
        return@onRequest pluginState.handleGetRequest(request)
      }
      connection!!.onRequest(
        "augmentcode/plugin-state-for-sidecar/set-value",
        SetStateValueRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(SetStateValueRequest::class.java)
        return@onRequest pluginState.handleSetRequest(request)
      }

      // The augmentcode/api-client/* methods
      connection!!.onRequest(
        "augmentcode/api-client/chat-stream",
        ChatStreamRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(ChatStreamRequest::class.java)
        val progressToken = request.progressToken

        try {
          val chatRequest = gson.fromJson(request.requestPayloadJson, ChatRequest::class.java)
          val (_, flowOfResults) = AugmentAPI.instance.chatRawResponse(chatRequest)
          flowOfResults.collect { result ->
            if (result.isFailure) {
              thisLogger().warn("Failed to get chat response: ${result.exceptionOrNull()}")
              return@collect
            }
            connection!!.sendProgress(
              progressToken,
              ChatStreamResponse.newBuilder().setResponsePayloadJson(result.getOrNull()!!).build(),
            )
          }

          // Close the connection after streaming response is complete
          return@onRequest Empty.getDefaultInstance()
        } catch (e: Exception) {
          return@onRequest Empty.getDefaultInstance()
        }
      }

      connection!!.onRequest(
        "augmentcode/api-client/agent-codebase-retrieval",
        ProtoAgentCodebaseRetrievalRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(ProtoAgentCodebaseRetrievalRequest::class.java)
        val apiRequest =
          AgentCodebaseRetrievalRequest().apply {
            informationRequest = request.informationRequest
            maxOutputLength = request.maxOutputLength
          }
        apiRequest.blobs =
          if (isV3IndexingEnabled()) {
            WorkspaceCoordinatorService.getInstance(project).getCheckpoint()
          } else {
            AugmentRemoteSyncingManager.getInstance(project).synchronizedBlobsPayload()
          }
        apiRequest.chatHistory = toTruncatedApiExchange(request.chatHistoryList, project)
        val response = AugmentAPI.instance.agentCodebaseRetrieval(apiRequest)
        return@onRequest ProtoAgentCodebaseRetrievalResponse.newBuilder()
          .setFormattedRetrieval(response.formattedRetrieval)
          .build()
      }
      connection!!.onRequest(
        "augmentcode/api-client/check-tool-safety",
        CheckToolSafetyRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(CheckToolSafetyRequest::class.java)
        try {
          val apiResp = AugmentAPI.instance.checkToolSafety(request.toolIdValue, request.toolInputJson)
          return@onRequest CheckToolSafetyResponse.newBuilder()
            .setIsSafe(apiResp.is_safe)
            .build()
        } catch (e: Exception) {
          logger.warn("Failed to perform tool safety check: ${e.message}", e)
          return@onRequest CheckToolSafetyResponse.newBuilder()
            .setIsSafe(false)
            .build()
        }
      }
      connection!!.onRequest(
        "augmentcode/api-client/log-agent-request-event",
        LogAgentRequestEvent.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(LogAgentRequestEvent::class.java)
        logger.info("Logging agent request event: $request")
        val eventsByRequestId = mutableMapOf<String, MutableList<AgentRequestEvent>>()
        for (event in request.eventsList) {
          // Get or initialize the events array for this request_id
          val eventsForRequestId = eventsByRequestId.getOrPut(event.requestId) { mutableListOf() }

          // Create a modified event without the request_id field
          val eventWithoutRequestId =
            AgentRequestEvent().apply {
              event_time_sec = event.eventTimeSec
              event_time_nsec = event.eventTimeNsec
              event_name = event.eventName
              conversation_id = event.conversationId
              request_id = event.requestId
              chat_history_length = event.chatHistoryLength
              user_agent = getUserAgent()
              // Handle event_data if present
              if (event.hasEventData()) {
                val eventData = event.eventData

                if (!isValidAgentRequestEvent(eventData, event.eventName, event.conversationId, event.requestId)) {
                  return@apply
                }

                event_data = null
                if (eventData.hasChatHistorySummarizationData()) {
                  event_data =
                    AgentRequestEventData().apply {
                      chat_history_summarization_data =
                        ChatHistorySummarizationData().apply {
                          total_history_char_count = eventData.chatHistorySummarizationData.totalHistoryCharCount
                          total_history_exchange_count =
                            eventData.chatHistorySummarizationData.totalHistoryExchangeCount
                          head_char_count = eventData.chatHistorySummarizationData.headCharCount
                          head_exchange_count = eventData.chatHistorySummarizationData.headExchangeCount
                          head_last_request_id = eventData.chatHistorySummarizationData.headLastRequestId
                          tail_char_count = eventData.chatHistorySummarizationData.tailCharCount
                          tail_exchange_count = eventData.chatHistorySummarizationData.tailExchangeCount
                          tail_last_request_id = eventData.chatHistorySummarizationData.tailLastRequestId
                          summary_char_count = eventData.chatHistorySummarizationData.summaryCharCount
                          summarization_duration_ms =
                            eventData.chatHistorySummarizationData.summarizationDurationMs
                          is_cache_about_to_expire =
                            eventData.chatHistorySummarizationData.isCacheAboutToExpire
                          is_aborted = eventData.chatHistorySummarizationData.isAborted
                        }
                    }
                } else if (eventData.hasFirstTokenTimingData()) {
                  event_data =
                    AgentRequestEventData().apply {
                      first_token_timing_data =
                        FirstTokenTimingData().apply {
                          user_message_sent_timestamp_ms =
                            eventData.firstTokenTimingData.userMessageSentTimestampMs
                          first_token_received_timestamp_ms =
                            eventData.firstTokenTimingData.firstTokenReceivedTimestampMs
                          time_to_first_token_ms = eventData.firstTokenTimingData.timeToFirstTokenMs
                        }
                    }
                }
              }
            }
          eventsForRequestId.add(eventWithoutRequestId)
        }

        for ((requestId, eventsForRequestId) in eventsByRequestId) {
          try {
            AugmentAPI.instance.recordRequestEvents(
              requestId,
              RecordAgentRequestBody().apply {
                events =
                  eventsForRequestId.map { e ->
                    val timeMillis = (e.event_time_sec * 1000L) + (e.event_time_nsec / 1000000L)
                    RecordAgentRequestEvent().apply {
                      time = java.time.Instant.ofEpochMilli(timeMillis).toString()
                      event =
                        RecordAgentRequestEventEvent().apply {
                          agent_request_event = e
                        }
                    }
                  }
              },
            )
          } catch (e: Exception) {
            logger.warn("Failed to record request events: ${e.message}", e)
          }
        }
        Empty.getDefaultInstance()
      }
      connection!!.onRequest(
        "augmentcode/api-client/log-agent-session-event",
        LogAgentSessionEvent.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(LogAgentSessionEvent::class.java)
        logger.info("Logging agent session event: $request")

        // Collect all events to send, potentially splitting events with multiple data types
        val eventsToSend = mutableListOf<RecordAgentSessionEvent>()

        for (e in request.eventsList) {
          val timeMillis = (e.eventTimeSec * 1000L) + (e.eventTimeNsec / 1000000L)
          val timeString = java.time.Instant.ofEpochMilli(timeMillis).toString()

          if (e.hasEventData()) {
            val eventData = e.eventData

            if (!isValidAgentSessionEvent(eventData, e.eventName, e.conversationId)) {
              return@onRequest Empty.getDefaultInstance()
            }

            // Process each event data type separately (since oneof means only one should be present)
            val eventDataToProcess = AgentSessionEventData()

            if (eventData.hasAgentInterruptionData()) {
              eventDataToProcess.agent_interruption_data =
                AgentInterruptionData().apply {
                  requestId = eventData.agentInterruptionData.requestId
                  currConversationLength = eventData.agentInterruptionData.currConversationLength
                }
            } else if (eventData.hasRememberToolCallData()) {
              eventDataToProcess.remember_tool_call_data =
                RememberToolCallData().apply {
                  caller = eventData.rememberToolCallData.caller
                  is_complex_new_memory = eventData.rememberToolCallData.isComplexNewMemory
                  tracing_data = convertToAgentTracingData(eventData.rememberToolCallData.tracingData)
                }
            } else if (eventData.hasMemoriesFileOpenData()) {
              eventDataToProcess.memories_file_open_data =
                MemoriesFileOpenData().apply {
                  memoriesPathUndefined = eventData.memoriesFileOpenData.memoriesPathUndefined
                }
            } else if (eventData.hasInitialOrientationData()) {
              val tracingData = convertToAgentTracingData(eventData.initialOrientationData.tracingData)
              eventDataToProcess.initial_orientation_data =
                InitialOrientationData().apply {
                  caller = eventData.initialOrientationData.caller
                  flags = tracingData?.flags
                  nums = tracingData?.nums
                  string_stats = tracingData?.string_stats
                  request_ids = tracingData?.request_ids
                }
            } else if (eventData.hasClassifyAndDistillData()) {
              eventDataToProcess.classify_and_distill_data =
                ClassifyAndDistillData().apply {
                  tracing_data = convertToAgentTracingData(eventData.classifyAndDistillData.tracingData)
                }
            } else if (eventData.hasFlushMemoriesData()) {
              eventDataToProcess.flush_memories_data =
                FlushMemoriesData().apply {
                  tracing_data = convertToAgentTracingData(eventData.flushMemoriesData.tracingData)
                }
            }

            // Send the event with the processed event data
            eventsToSend.add(
              RecordAgentSessionEvent().apply {
                time = timeString
                event =
                  RecordAgentSessionEventEvent().apply {
                    agent_session_event =
                      AgentSessionEvent().apply {
                        user_agent = getUserAgent()
                        event_time_sec = e.eventTimeSec
                        event_time_nsec = e.eventTimeNsec
                        event_name = e.eventName
                        conversation_id = e.conversationId
                        event_data = eventDataToProcess
                      }
                  }
              },
            )
          } else {
            // No event data, send simple event
            eventsToSend.add(
              RecordAgentSessionEvent().apply {
                time = timeString
                event =
                  RecordAgentSessionEventEvent().apply {
                    agent_session_event =
                      AgentSessionEvent().apply {
                        user_agent = getUserAgent()
                        event_time_sec = e.eventTimeSec
                        event_time_nsec = e.eventTimeNsec
                        event_name = e.eventName
                        conversation_id = e.conversationId
                        event_data = null
                      }
                  }
              },
            )
          }
        }

        AugmentAPI.instance.recordSessionEvents(
          RecordAgentSessionBody().apply {
            client_name = "intellij-extension"
            events = eventsToSend
          },
        )
        Empty.getDefaultInstance()
      }

      // The augmentcode/tools/* methods
      connection!!.onRequest(
        "augmentcode/tools/retrieve-remote-tools",
        RetrieveRemoteToolsRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(RetrieveRemoteToolsRequest::class.java)
        toolsManager?.retrieveRemoteTools(request) ?: RetrieveRemoteToolsResponse.getDefaultInstance()
      }

      connection!!.onRequest(
        "augmentcode/tools/get-tool-status-for-settings-panel",
        GetToolStatusForSettingsPanelRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(GetToolStatusForSettingsPanelRequest::class.java)
        toolsManager?.getToolStatusForSettingsPanel(request)
          ?: GetToolStatusForSettingsPanelResponse.getDefaultInstance()
      }

      connection!!.onRequest(
        "augmentcode/tools/call-remote-tool",
        CallRemoteToolRequest.getDefaultInstance().javaClass,
      ) { params ->
        val call = params.unpack(CallRemoteToolRequest::class.java)
        logger.info("Calling remote tool: $call")

        val response =
          AugmentAPI.instance.runRemoteTool(
            call.requestId,
            call.name,
            call.input,
            call.toolId.toApi(),
            getExtraToolInput(call.toolId.toApi()),
          )

        logger.info("RemoteTools: $response")

        CallRemoteToolResponse.newBuilder()
          .setToolOutput(response.tool_output)
          .setToolResultMessage(response.tool_result_message)
          .setStatus(response.status.value)
          .build()
      }

      connection!!.onRequest(
        "augmentcode/tools/filter-tool-with-extra-input",
        FilterToolsWithExtraInputRequest::class.java,
      ) { params ->
        val request = params.unpack(FilterToolsWithExtraInputRequest::class.java)
        logger.info("Filtering tools with extra input: ${request.toolIdsList}")

        val integrations = AugmentIntegrationsConfig.instance
        val filteredTools =
          request.toolIdsList.filter { protoToolId ->
            val apiToolId = protoToolId.toApi()
            when (apiToolId) {
              RemoteToolId.Jira,
              RemoteToolId.Confluence,
              -> {
                !integrations.atlassianServerUrl.isNullOrEmpty() &&
                  !integrations.atlassianPersonalApiToken.isNullOrEmpty() &&
                  !integrations.atlassianUsername.isNullOrEmpty()
              }

              RemoteToolId.Notion,
              -> {
                !integrations.notionApiToken.isNullOrEmpty()
              }

              RemoteToolId.Linear,
              -> {
                !integrations.linearApiToken.isNullOrEmpty()
              }

              RemoteToolId.GitHubApi -> {
                !integrations.githubApiToken.isNullOrEmpty()
              }

              else -> false
            }
          }

        FilterToolsWithExtraInputResponse.newBuilder()
          .addAllToolIds(filteredTools)
          .build()
      }

      connection!!.onRequest("augmentcode/client-actions/showDiffView", ShowDiffViewRequest.getDefaultInstance().javaClass) { params ->
        val request = params.unpack(ShowDiffViewRequest::class.java)
        DiffViewerService.getInstance(project).showDiffView(request)
      }

      val startedSuccessfully =
        try {
          process?.exitValue() // will throw if process is still running
          false
        } catch (e: IllegalThreadStateException) {
          true
        }
      if (startedSuccessfully) {
        logger.info("Sidecar process spawned successfully with pid ${process?.pid()}...")
        val flagsSnapshot = AugmentAppStateService.instance.context.flags
        val initializeResult =
          withTimeout(SIDECAR_INITIALIZE_TIMEOUT_MS) {
            connection!!.sendRequest(
              "initialize",
              InitializeParams.newBuilder()
                .setProcessId(process?.pid() ?: 0)
                .setCapabilities(
                  Capabilities.newBuilder()
                    .setFeatureFlags(
                      SidecarFlags.newBuilder()
                        .setEnableChatWithTools(flagsSnapshot.chatWithToolsEnabled)
                        .setEnableAgentMode(flagsSnapshot.agentModeEnabled)
                        .setMemoriesParamsJson(flagsSnapshot.memoriesParams)
                        .setAgentEditTool(flagsSnapshot.agentEditTool)
                        .setAgentEditToolMinViewSize(flagsSnapshot.agentEditToolMinViewSize)
                        .setAgentEditToolSchemaType(flagsSnapshot.agentEditToolSchemaType)
                        .setAgentEditToolEnableFuzzyMatching(flagsSnapshot.agentEditToolEnableFuzzyMatching)
                        .setAgentEditToolFuzzyMatchSuccessMessage(flagsSnapshot.agentEditToolFuzzyMatchSuccessMessage)
                        .setAgentEditToolFuzzyMatchMaxDiff(flagsSnapshot.agentEditToolFuzzyMatchMaxDiff)
                        .setAgentEditToolFuzzyMatchMaxDiffRatio(flagsSnapshot.agentEditToolFuzzyMatchMaxDiffRatio)
                        .setAgentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs(
                          flagsSnapshot.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs,
                        )
                        .setAgentEditToolInstructionsReminder(flagsSnapshot.agentEditToolInstructionsReminder)
                        .setAgentEditToolShowResultSnippet(flagsSnapshot.agentEditToolShowResultSnippet)
                        .setAgentEditToolMaxLines(flagsSnapshot.agentEditToolMaxLines)
                        .setAgentSaveFileToolInstructionsReminder(flagsSnapshot.agentSaveFileToolInstructionsReminder)
                        .setGrepSearchToolEnable(flagsSnapshot.grepSearchToolEnable)
                        .setGrepSearchToolTimelimitSec(flagsSnapshot.grepSearchToolTimelimitSec)
                        .setGrepSearchToolOutputCharsLimit(flagsSnapshot.grepSearchToolOutputCharsLimit)
                        .setGrepSearchToolNumContextLines(flagsSnapshot.grepSearchToolNumContextLines)
                        .setUseHistorySummary(flagsSnapshot.useHistorySummary)
                        .setHistorySummaryParams(flagsSnapshot.historySummaryParams)
                        .setAgentViewToolParams(flagsSnapshot.agentViewToolParams)
                        .setEnableTaskList(flagsSnapshot.taskListEnabled)
                        .build(),
                    )
                    .setInitialState(
                      SidecarInitialState.newBuilder()
                        .setChatMode(initialChatMode)
                        .setMemoriesAbsPath(MemoriesService.getInstance(project).getMemoriesAbsPath())
                        .setProjectRootAbsPath(AugmentFileStore.getSubDirectoryForProject(project, "node-process").toString())
                        .build(),
                    )
                    .build(),
                )
                .build(),
              InitializeResult::class.java,
            ).await()
          }
        logger.info("Sidecar process initialized successfully: $initializeResult")
        connection!!.sendNotification("initialized", Empty.getDefaultInstance())

        // Load MCP servers from storage
        project.serviceOrNull<SidecarService>()?.setMcpServers(AugmentIntegrationsConfig.instance.mcpServersForSidecar)

        updateState(SidecarState.SIDECAR_RUNNING)
      } else {
        logger.warn("Sidecar failed to start with exit code ${process?.exitValue()}...")
      }
    } catch (e: Exception) {
      logger.warn("Failed to start sidecar", e)
      stopServer()
      updateState(SidecarState.SIDECAR_ERROR, "Failed to start sidecar: ${e.message}")
    }
  }

  fun getCurrentMode(): ChatMode {
    return toolsManager?.currentMode ?: initialChatMode
  }

  suspend fun toolExists(name: String): Boolean {
    return getToolState().toolsList.any { it.name == name }
  }

  suspend fun getToolState(): ToolsStateResponse {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot get tool state")
      return ToolsStateResponse.getDefaultInstance()
    }
    return toolsManager?.getToolState() ?: ToolsStateResponse.getDefaultInstance()
  }

  suspend fun getToolStatusForSettingsPanel(useCache: Boolean = true): GetToolStatusForSettingsPanelResponse {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot get tool status for settings panel")
      return GetToolStatusForSettingsPanelResponse.getDefaultInstance()
    }
    return toolsManager?.getToolStatusForSettingsPanel(
      GetToolStatusForSettingsPanelRequest.newBuilder().setUseCache(useCache).build(),
    ) ?: GetToolStatusForSettingsPanelResponse.getDefaultInstance()
  }

  suspend fun setMcpServers(mcpServers: List<McpServerConfig>) {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot set MCP servers")
      return
    }
    toolsManager?.setMcpServers(mcpServers)
  }

  suspend fun callTool(
    name: String,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse? {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot call tool")
      return null
    }

    return toolsManager?.callTool(name, requestId, toolUseId, input, chatHistory, conversationId)
  }

  suspend fun sendWebviewMessage(baseMsgString: String): String? {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot send webview message")
      return null
    }

    try {
      // Send the message to the sidecar using the connection
      val responseAny =
        connection!!.sendRequest(
          "augmentcode/webview-message",
          ProcessWebviewMessageRequest.newBuilder().setMessage(baseMsgString).build(),
          ProcessWebviewMessageResponse::class.java,
        ).await()
      val response = responseAny.unpack(ProcessWebviewMessageResponse::class.java)
      return response.message
    } catch (e: Exception) {
      logger.warn("Unhandled webview message: $baseMsgString ${e.message}")
      return null
    }
  }

  override fun processTerminated(event: ProcessEvent) {
    if (event.exitCode != 0) {
      logger.warn("Sidecar process exited with code ${event.exitCode}: ${event.text}")
      stopServer()
      updateState(SidecarState.SIDECAR_ERROR, "Sidecar process exited with code ${event.exitCode}: ${event.text}")
    }
  }

  override fun dispose() {
    thisLogger().info("Disposing sidecar service")
    stopServer()
  }

  fun stopServer() {
    updateState(SidecarState.SIDECAR_STOPPING)
    process?.destroy()
    process?.waitFor(5, TimeUnit.SECONDS)
    process?.destroyForcibly()
    process = null
    connection = null
    toolsManager = null
    updateState(SidecarState.SIDECAR_STOPPED)
  }

  private fun getCwd(): String {
    return AugmentRoot.findActiveProjectRoot(project)?.path ?: ""
  }

  private fun getWorkspaceRoot(): String {
    // Use IntelliJ's actual workspace root instead of repository root
    val allRootCandidates = project.getBaseDirectories()

    if (allRootCandidates.isNotEmpty()) {
      logger.warn("Using first project base directory as workspace root from all candidates: $allRootCandidates")
      return allRootCandidates.first().path
    }

    // Final fallback to project base path
    return project.basePath ?: ""
  }

  suspend fun cancelToolRun(
    requestId: String,
    toolUseId: String,
  ) {
    if (process?.isAlive != true) return

    toolsManager?.cancelToolRun(requestId, toolUseId)
  }

  suspend fun changeChatMode(mode: String) {
    val chatMode = ChatMode.valueOf(mode.uppercase())
    thisLogger().info("changing chat mode to $chatMode")
    if (process?.isAlive != true) {
      thisLogger().info("Sidecar process is not alive, changing initial chat mode to $chatMode")
      initialChatMode = chatMode
      return
    }

    toolsManager?.changeChatMode(chatMode)
  }

  /**
   * Load rules from the .augment/rules directory and workspace guidelines files.
   * Delegates to the sidecar RulesService for consistent behavior across clients.
   */
  suspend fun loadRules(
    includeGuidelines: Boolean = false,
    contextRules: MutableList<com.augmentcode.sidecar.rpc.clientInterfaces.Rule>? = null,
  ): List<com.augmentcode.sidecar.rpc.clientInterfaces.Rule> {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot load rules")
      return emptyList()
    }

    return try {
      // Use the new RPC endpoint for loading rules
      val request =
        LoadRulesRequest.newBuilder()
          .setIncludeGuidelines(includeGuidelines)
          .addAllContextRules(contextRules ?: emptyList())
          .build()

      logger.debug("Sending rules request to sidecar via RPC")
      val response =
        connection!!.sendRequest(
          "augmentcode/rules/loadRules",
          request,
          LoadRulesResponse::class.java,
        ).await()

      val loadRulesResponse = response.unpack(LoadRulesResponse::class.java)

      loadRulesResponse.rulesList.also { rules: List<com.augmentcode.sidecar.rpc.clientInterfaces.Rule> ->
        logger.debug("Loaded ${rules.size} rules via RPC")
      }
    } catch (e: Exception) {
      logger.error("Failed to load rules from sidecar", e)
      emptyList()
    }
  }

  private fun getExtraToolInput(toolId: RemoteToolId): ExtraToolInput? {
    // It's important we return null if we do not have any local credentials.
    // If the user has OAuth setup for an integration and we return empty credentials, the API
    // call will fail as it tries to use the API token instead of the OAuth token.
    return when (toolId) {
      RemoteToolId.Jira,
      RemoteToolId.Confluence,
      -> {
        if (AugmentIntegrationsConfig.instance.atlassianServerUrl.isNullOrEmpty() ||
          AugmentIntegrationsConfig.instance.atlassianPersonalApiToken.isNullOrEmpty() ||
          AugmentIntegrationsConfig.instance.atlassianUsername.isNullOrEmpty()
        ) {
          return null
        }
        AtlassianToolExtraInput().apply {
          serverUrl = AugmentIntegrationsConfig.instance.atlassianServerUrl
          personalApiToken = AugmentIntegrationsConfig.instance.atlassianPersonalApiToken
          username = AugmentIntegrationsConfig.instance.atlassianUsername
        }
      }

      RemoteToolId.Notion,
      -> {
        if (AugmentIntegrationsConfig.instance.notionApiToken.isNullOrEmpty()) {
          return null
        }
        NotionToolExtraInput().apply {
          apiToken = AugmentIntegrationsConfig.instance.notionApiToken
        }
      }

      RemoteToolId.Linear,
      -> {
        if (AugmentIntegrationsConfig.instance.linearApiToken.isNullOrEmpty()) {
          return null
        }
        LinearToolExtraInput().apply {
          apiToken = AugmentIntegrationsConfig.instance.linearApiToken
        }
      }

      RemoteToolId.GitHubApi -> {
        if (AugmentIntegrationsConfig.instance.githubApiToken.isNullOrEmpty()) {
          return null
        }
        GitHubToolExtraInput().apply {
          apiToken = AugmentIntegrationsConfig.instance.githubApiToken
        }
      }

      else -> null
    }
  }

  fun getCommandForSidecar(
    nodeBinary: Path,
    tempFile: File,
    sidecarLogFile: String,
  ): List<String> {
    return mutableListOf(
      nodeBinary.toString(),
      tempFile.absolutePath,
      "--log-file",
      sidecarLogFile,
      "--stdio",
    )
  }

  /**
   * Recursively copy a resource directory from the JAR to the filesystem.
   * This is needed to extract native modules and runtime dependencies.
   */
  private fun copyResourceDirectory(
    relResourceDir: String,
    targetDir: Path,
    filesToCopy: List<String>,
  ) {
    try {
      // Try to get the resource as a URL to see if it exists
      val resourceUrl = javaClass.classLoader.getResource(relResourceDir)
      if (resourceUrl == null) {
        logger.warn("Resource directory $relResourceDir not found in classloader")
        return
      }

      for (relPathForFile in filesToCopy) {
        val fullResourcePath = "$relResourceDir/$relPathForFile"
        val resourceStream = javaClass.classLoader.getResourceAsStream(fullResourcePath)
        if (resourceStream != null) {
          val targetFile = targetDir.resolve(relPathForFile).toFile()
          targetFile.parentFile.mkdirs()

          targetFile.outputStream().use { output ->
            resourceStream.copyTo(output)
          }

          // Make executable if it's a .node file (native module)
          if (relPathForFile.endsWith(".node")) {
            targetFile.setExecutable(true)
          }

          logger.debug("Copied resource file: $fullResourcePath")
        } else {
          logger.warn("Resource file not found: $fullResourcePath")
        }
      }
      logger.info("Successfully copied resource directory $relResourceDir to $targetDir")
    } catch (e: Exception) {
      logger.warn("Failed to copy resource directory $relResourceDir: ${e.message}", e)
      // Don't throw - let the sidecar try to start anyway in case the resources aren't needed
    }
  }
}

private fun convertToAgentTracingData(data: AgentTracingDataProto?): AgentTracingData? {
  if (data == null) {
    return null
  }
  return AgentTracingData().apply {
    flags =
      data.flagsMap.mapValues { (_, value) ->
        Timed<Boolean>().apply {
          this.value = value.value
          this.timestamp = value.timestamp
        }
      }
    nums =
      data.numsMap.mapValues { (_, value) ->
        Timed<Int>().apply {
          this.value = value.value.toInt()
          this.timestamp = value.timestamp
        }
      }
    string_stats =
      data.stringStatsMap.mapValues { (_, value) ->
        Timed<StringStats>().apply {
          this.value =
            StringStats().apply {
              num_lines = value.value.numLines
              num_chars = value.value.numChars
            }
          this.timestamp = value.timestamp
        }
      }
    request_ids =
      data.requestIdsMap.mapValues { (_, value) ->
        Timed<String>().apply {
          this.value = value.value
          this.timestamp = value.timestamp
        }
      }
  }
}

enum class SidecarState {
  SIDECAR_STARTING,
  SIDECAR_RUNNING,
  SIDECAR_STOPPING,
  SIDECAR_STOPPED,
  SIDECAR_ERROR,
}
