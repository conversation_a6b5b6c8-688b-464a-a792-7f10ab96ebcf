package com.augmentcode.intellij.workspacemanagement.checkpoint

import com.augmentcode.api.BlobsPayload
import com.augmentcode.api.CheckpointBlobsRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.CheckpointResult
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.nio.file.Path
import kotlin.coroutines.cancellation.CancellationException

sealed class BlobChangeEvent(val path: Path) {
  abstract val remoteBlobName: String
}

class BlobAddedEvent(path: Path, val newBlobName: String) : BlobChangeEvent(path) {
  override val remoteBlobName: String
    get() = newBlobName
}

class BlobRemovedEvent(path: Path, val blobName: String) : BlobChangeEvent(path) {
  override val remoteBlobName: String
    get() = blobName
}

class BlobUpdatedEvent(path: Path, val originalBlobName: String, val newBlobName: String) : BlobChangeEvent(path) {
  override val remoteBlobName: String
    get() = newBlobName // the latest blob name is the one that matters
}

data class BlobsSnapshot(
  var checkpointId: String? = null,
  var addedBlobs: MutableSet<String> = mutableSetOf(),
  var deletedBlobs: MutableSet<String> = mutableSetOf(),
) {
  val workingSetSize: Int
    get() = addedBlobs.size + deletedBlobs.size

  fun toPayload(maxWorkingSetSize: Int? = null): BlobsPayload {
    val result = BlobsPayload()
    result.checkpointId = checkpointId
    result.addedBlobs = addedBlobs.toHashSet()
    result.deletedBlobs = deletedBlobs.toHashSet()
    if (maxWorkingSetSize != null) {
      result.addedBlobs = result.addedBlobs.take(maxWorkingSetSize).toSet()
      result.deletedBlobs = result.deletedBlobs.take(maxWorkingSetSize).toSet()
    }
    return result
  }
}

class CheckpointManager(
  private val scope: CoroutineScope,
  private val checkpointChannel: RoughlySizedChannel<BlobChangeEvent>,
  private val maxWorkingSetSize: Int = 1_000,
) : Disposable {
  private val logger = thisLogger()
  private val snapshot = BlobsSnapshot()
  private val snapshotMutex = Mutex()
  private var checkpointJob: Job? = null

  override fun dispose() {
    stopCheckpointJob()
  }

  fun stopCheckpointJob() {
    thisLogger().info("Stopping checkpoint job")
    checkpointJob?.cancel()
    checkpointJob = null
  }

  fun startCheckpointJob() {
    if (checkpointJob != null) {
      logger.warn("Checkpoint job already running")
      return
    }

    checkpointJob =
      scope.launch {
        logger.info("Starting checkpoint job")
        while (isActive) {
          try {
            val event = checkpointChannel.receive()
            onBlobChangeEvent(event)
          } catch (e: CancellationException) {
            throw e
          } catch (e: Exception) {
            logger.warn("Failed to process checkpoint batch", e)
          }
        }
      }
  }

  suspend fun currentCheckpoint(): BlobsPayload = snapshotMutex.withLock { snapshot.toPayload() }

  private suspend fun onBlobChangeEvent(event: BlobChangeEvent) {
    when (event) {
      is BlobAddedEvent -> {
        // if deleted blobs contains newBlobName, remove it. else add it to added blobs
        // addressing the case where we removed then added a blob before checkpointing and the checkpoint already contains the blob so we don't want to add it again
        snapshotMutex.withLock {
          if (!snapshot.deletedBlobs.remove(event.newBlobName)) {
            logger.trace("Adding ${event.newBlobName} to added blobs")
            snapshot.addedBlobs.add(event.newBlobName)
          } else {
            logger.trace("Adding ${event.newBlobName} has cancelled out removal of ${event.newBlobName}")
          }
        }
      }

      is BlobRemovedEvent -> {
        // TODO: address case where blob is added and deleted before checkpoint is created
        snapshotMutex.withLock {
          if (!snapshot.addedBlobs.remove(event.blobName)) {
            logger.trace("Removing ${event.blobName} from deleted blobs")
            snapshot.deletedBlobs.add(event.blobName)
          } else {
            logger.trace("Removing ${event.blobName} has cancelled out addition of ${event.blobName}")
          }
        }
      }

      is BlobUpdatedEvent -> {
        snapshotMutex.withLock {
          logger.trace("Updating ${event.originalBlobName} to ${event.newBlobName}")

          val wasRemovedBeforeRemoteUpdate = snapshot.addedBlobs.remove(event.originalBlobName)
          if (!wasRemovedBeforeRemoteUpdate) {
            // blob is not in the yet to-add set, so it must be in the checkpoint
            // therefore add it to the deleted set
            logger.trace("Adding ${event.originalBlobName} to deleted blobs")
            snapshot.deletedBlobs.add(event.originalBlobName)
          }
          snapshot.addedBlobs.add(event.newBlobName)
        }
      }
    }

    if (snapshot.workingSetSize >= maxWorkingSetSize) {
      logger.info("Creating checkpoint with ${snapshot.workingSetSize} blobs")
      val payload = snapshotMutex.withLock { snapshot.toPayload(maxWorkingSetSize) }
      val checkpointResult =
        AugmentAPI.instance.createCheckpoint(
          CheckpointBlobsRequest().apply {
            blobs = payload
          },
        )
      when (checkpointResult) {
        is CheckpointResult.Success -> {
          logger.info("Checkpoint refreshed to ${checkpointResult.response.newCheckpointId}")
          snapshotMutex.withLock {
            snapshot.checkpointId = checkpointResult.response.newCheckpointId
            snapshot.addedBlobs.removeAll(payload.addedBlobs)
            snapshot.deletedBlobs.removeAll(payload.deletedBlobs)
          }
        }

        is CheckpointResult.NeedsReset -> {
          logger.warn("Checkpoint ${snapshot.checkpointId} requires a reset.")
          // todo: invalidate index
        }

        is CheckpointResult.OtherFailure -> {
          // Do nothing, will retry on next sync
          logger.warn("Failed to refresh checkpoint, will retry on next sync")
        }
      }
    }
  }
}
