package com.augmentcode.intellij.actions

import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent

abstract class AnActionWithCtx : AnAction() {
  final override fun update(e: AnActionEvent) {
    val ctx = AugmentAppStateService.instance.context
    val shouldContinue = ctx.isSignedIn && ctx.model != null
    e.presentation.isEnabledAndVisible = shouldContinue
    if (!shouldContinue) return
    updateWithCtx(e, ctx, ctx.model!!)
  }

  open fun updateWithCtx(
    e: AnActionEvent,
    ctx: PluginContext,
    model: AugmentModel,
  ) {}
}
