package com.augmentcode.intellij.completion

import com.augmentcode.api.OnboardingSessionEventName
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.metrics.OnboardingSessionEventReporter
import com.intellij.codeInsight.inline.completion.DefaultInlineCompletionInsertHandler
import com.intellij.codeInsight.inline.completion.InlineCompletionInsertEnvironment
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionElement
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

object AugmentCompletionInsertHandler : DefaultInlineCompletionInsertHandler() {
  private val logger = thisLogger()
  private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

  override fun afterInsertion(
    environment: InlineCompletionInsertEnvironment,
    elements: List<InlineCompletionElement>,
  ) {
    super.afterInsertion(environment, elements)
    if (!AugmentAPI.instance.availableBlocking()) {
      // if the API is not available, we can't send the completion event
      return
    }

    logger.debug("Completion accepted by user")

    scope.launch {
      val project = environment.editor.project ?: return@launch
      OnboardingSessionEventReporter.getInstance(project)
        .reportOnboardingSessionEvent(OnboardingSessionEventName.AcceptedCompletion)
    }
  }
}
