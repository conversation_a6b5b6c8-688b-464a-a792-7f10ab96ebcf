package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.QualifiedPathName
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.FileUtil
import com.intellij.psi.codeStyle.MinusculeMatcher
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.jetbrains.annotations.VisibleForTesting

/**
 * A service which maintains a two way map from absolute paths to the currently synced blob names.
 */
@Service(Service.Level.PROJECT)
internal class BlobNameService(private val project: Project) {
  companion object {
    fun getInstance(project: Project): BlobNameService = project.getService(BlobNameService::class.java)

    val logger = thisLogger()
  }

  private var blobPathMap = BidirectionalBlobPathMap(project)

  /**
   * returns the blob name for the given path, or null if not found
   */
  suspend fun deleteByPath(absPath: String): String? {
    logger.debug("Deleting blob for path $absPath")
    return blobPathMap.removeByPath(FileUtil.toSystemDependentName(absPath))
  }

  /**
   * Takes a callback which takes the absolute path and blob name as arguments.
   */
  suspend fun deleteByDirectory(
    absPath: String,
    onFileDeleted: (String, String) -> Unit = { _, _ -> },
  ) {
    val sysIndependentPath = FileUtil.toSystemDependentName(absPath)
    val absPathWithSeparator = if (sysIndependentPath.endsWith("/")) sysIndependentPath else "$sysIndependentPath/"
    logger.debug("Deleting blobs in directory $absPathWithSeparator")

    blobPathMap.removePathsMatching(
      predicate = { it.startsWith(absPathWithSeparator) },
      action = onFileDeleted,
    )
  }

  /**
   * @return the blob name for the given path, or null if not found
   */
  suspend fun getByPath(absPath: String): String? {
    return blobPathMap.getByPath(FileUtil.toSystemDependentName(absPath))
  }

  /**
   * @return the blob name for the given path, or null if not found, without waiting for the mutex
   */
  fun getByPathNoBlocking(absPath: String): String? {
    return blobPathMap.getByPathNonBlocking(FileUtil.toSystemDependentName(absPath))
  }

  suspend fun put(
    blobName: String,
    absPath: String,
  ) {
    blobPathMap.put(blobName, FileUtil.toSystemDependentName(absPath))
  }

  suspend fun findFile(relPath: String): List<String> {
    return blobPathMap.findFile(FileUtil.toSystemDependentName(relPath))
  }

  suspend fun findFiles(matcher: MinusculeMatcher): Sequence<QualifiedPathName> {
    return blobPathMap.findFiles(matcher)
  }

  suspend fun findDirectories(relPath: String): Sequence<String> {
    return blobPathMap.findDirectories(FileUtil.toSystemDependentName(relPath))
  }

  suspend fun blobsForPaths(paths: Set<String>): List<String> {
    return blobPathMap.blobsForPaths(paths.map { FileUtil.toSystemDependentName(it) }.toSet())
  }

  suspend fun memoryIntensiveGetQualifiedPathNames(): List<QualifiedPathName> {
    return blobPathMap.memoryIntensiveGetQualifiedPathNames()
  }

  suspend fun fileByBlobName(blobName: String): QualifiedPathName? {
    return blobPathMap.fileByBlobName(blobName)
  }

  @VisibleForTesting
  fun clear() {
    blobPathMap = BidirectionalBlobPathMap(project)
  }

  fun size(): Int {
    return blobPathMap.size
  }

  /**
   * Memory-efficient wrapper around bidirectional blob-path mapping that prevents
   * intermediate collection creation for large datasets.
   * Do not add additional methods to this unless you are absolutely sure of the memory implications.
   * We expect these maps to work with up to 1 million files.
   */
  private class BidirectionalBlobPathMap(private val project: Project) {
    private val mutex = Mutex()
    private val blobToQualifiedPath = mutableMapOf<String, QualifiedPathName>()
    private val pathToBlob = mutableMapOf<String, String>()

    suspend fun put(
      blobName: String,
      absPath: String,
    ) {
      val file = AugmentRoot.findFile(absPath)
      if (file == null) {
        throw IllegalStateException("File not found for path $absPath")
      }
      val qualifiedPath = AugmentRoot.findQualifiedPathName(project, file)
      if (qualifiedPath == null) {
        throw IllegalStateException("Qualified path not found for file $file")
      }

      mutex.withLock {
        pathToBlob[absPath] = blobName
        blobToQualifiedPath[blobName] = qualifiedPath
      }
    }

    fun getByPathNonBlocking(absPath: String): String? {
      return pathToBlob[absPath]
    }

    suspend fun getByPath(absPath: String): String? {
      return mutex.withLock {
        pathToBlob[absPath]
      }
    }

    suspend fun removeByPath(absPath: String): String? {
      return mutex.withLock {
        val blobName = pathToBlob.remove(absPath)
        if (blobName != null) {
          blobToQualifiedPath.remove(blobName)
        }
        blobName
      }
    }

    suspend inline fun removePathsMatching(
      predicate: (String) -> Boolean,
      action: (String, String) -> Unit,
    ) {
      mutex.withLock {
        val iterator = pathToBlob.iterator()
        while (iterator.hasNext()) {
          val (path, blobName) = iterator.next()
          if (predicate(path)) {
            iterator.remove()
            blobToQualifiedPath.remove(blobName)
            action(path, blobName)
          }
        }
      }
    }

    /**
     * Gets size without grabbing the lock, so it may not be perfectly accurate. Only use this for UI and logging
     * where precise size doesn't matter.
     */
    val size: Int
      get() = blobToQualifiedPath.size

    suspend fun findFile(relPath: String): List<String> {
      return mutex.withLock {
        pathToBlob.keys.filter {
          it.endsWith(relPath)
        }
      }
    }

    suspend fun findFiles(matcher: MinusculeMatcher): Sequence<QualifiedPathName> {
      return mutex.withLock {
        sequence {
          blobToQualifiedPath.values.forEach {
            val relativePath = it.relPath
            if (matcher.matches(relativePath.substringAfterLast('/')) || matcher.matches(relativePath)) {
              yield(it)
            }
          }
        }
      }
    }

    suspend fun fileByBlobName(blobName: String): QualifiedPathName? {
      return mutex.withLock {
        return@withLock blobToQualifiedPath[blobName]
      }
    }

    // TODO: Confirm this behavior is correct.
    // 1. Is returning the same directory multiple times ok?
    // 2. Should we return subdirectories one level at a time instead of the entire direct hierarchy?
    //    "foo" as a query, should it match for "foo/bar/baz.txt", or should we only match "foo/bar" as a query?
    suspend fun findDirectories(query: String): Sequence<String> {
      return mutex.withLock {
        blobToQualifiedPath.values
          .asSequence()
          .filter { it.relPath.contains('/') }
          .map { it.relPath.substringBeforeLast('/') }
          .filter { it == query }
      }
    }

    suspend fun blobsForPaths(relativePaths: Set<String>): List<String> {
      return relativePaths.mapNotNull { relativePath ->
        mutex.withLock {
          pathToBlob.entries.find {
            it.key.endsWith(relativePath)
          }.let { it?.value }
        }
      }
    }

    suspend fun memoryIntensiveGetQualifiedPathNames(): List<QualifiedPathName> {
      return mutex.withLock {
        blobToQualifiedPath.values.toList()
      }
    }
  }
}
