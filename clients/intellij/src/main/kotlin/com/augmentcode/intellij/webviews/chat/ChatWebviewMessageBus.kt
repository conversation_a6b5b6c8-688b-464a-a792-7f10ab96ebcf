package com.augmentcode.intellij.webviews.chat

import com.google.protobuf.Message
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.util.messages.Topic
import com.intellij.util.messages.Topic.ProjectLevel

/**
 * This message bus is used to send messages to the chat webview. This
 * is useful for IDE events that need to be passed to the webview.
 */
interface ChatWebviewMessageBus {
  fun postMessageToWebview(message: Message)

  fun reloadIntellijStyles()

  companion object {
    @ProjectLevel
    val CHAT_WEBVIEW_MESSAGE_TOPIC: Topic<ChatWebviewMessageBus> =
      Topic.create(
        "chat webview message bus",
        ChatWebviewMessageBus::class.java,
      )

    fun syncPublisher(project: Project): ChatWebviewMessageBus {
      if (project.isDisposed) {
        thisLogger().warn(
          "ChatWebviewMessageNotifier.syncPublisher called on a disposed project. Returning no-op, but this is a bug.",
        )
        // Defensive check. This shouldn't happen. Callers should remove listeners when the owner is disposed.
        return object : ChatWebviewMessageBus {
          override fun postMessageToWebview(message: Message) {}

          override fun reloadIntellijStyles() {}
        }
      }
      return project.messageBus.syncPublisher(CHAT_WEBVIEW_MESSAGE_TOPIC)
    }
  }
}
