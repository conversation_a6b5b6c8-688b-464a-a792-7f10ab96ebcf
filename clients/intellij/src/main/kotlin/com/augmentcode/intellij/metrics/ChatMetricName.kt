package com.augmentcode.intellij.metrics

/**
 * Chat metric names for IntelliJ. These correspond to the names in the VSCode metrics/types.ts.
 * Not all of these are implemented in IntelliJ yet.
 */
object ChatMetricName {
  // @mentions
  const val CHAT_MENTION_FOLDER = "chat-mention-folder"
  const val CHAT_MENTION_FILE = "chat-mention-file"
  const val CHAT_MENTION_EXTERNAL_SOURCE = "chat-mention-external-source"
  const val CHAT_CLEAR_CONTEXT = "chat-clear-context"
  const val CHAT_RESTORE_DEFAULT_CONTEXT = "chat-restore-default-context"

  // /actions
  const val CHAT_USE_ACTION_FIND = "chat-use-action-find"
  const val CHAT_USE_ACTION_EXPLAIN = "chat-use-action-explain"

  const val CHAT_USE_ACTION_WRITE_TEST = "chat-use-action-write-test"

  // Conversation menu
  const val CHAT_NEW_CONVERSATION = "chat-new-conversation"

  const val CHAT_EDIT_CONVERSATION_NAME = "chat-edit-conversation-name"

  // Markdown (smart paste)
  const val CHAT_FAILED_SMART_PASTE_RESOLVE_FILE = "chat-failed-smart-paste-resolve-file"
  const val CHAT_PRECOMPUTE_SMART_PASTE = "chat-precompute-smart-paste"
  const val CHAT_SMART_PASTE = "chat-smart-paste"

  // Markdown (codeblocks)
  const val CHAT_CODEBLOCK_COPY = "chat-codeblock-copy"
  const val CHAT_CODEBLOCK_CREATE = "chat-codeblock-create"
  const val CHAT_CODEBLOCK_GO_TO_FILE = "chat-codeblock-go-to-file"

  // Markdown (codespans)
  const val CHAT_CODESPAN_GO_TO_FILE = "chat-codespan-go-to-file"
  const val CHAT_CODESPAN_GO_TO_SYMBOL = "chat-codespan-go-to-symbol"

  // Markdown (mermaid blocks)
  const val CHAT_MERMAIDBLOCK_INITIALIZE = "chat-mermaidblock-initialize"
  const val CHAT_MERMAIDBLOCK_TOGGLE = "chat-mermaidblock-toggle"
  const val CHAT_MERMAIDBLOCK_INTERACT = "chat-mermaidblock-interact"
  const val CHAT_MERMAIDBLOCK_ERROR = "chat-mermaidblock-error"

  // Suggested questions
  const val CHAT_USE_SUGGESTED_QUESTION = "chat-use-suggested-question"
  const val CHAT_DISPLAY_SUGGESTED_QUESTIONS = "chat-display-suggested-questions"

  // Guidelines
  const val CHAT_SET_WORKSPACE_GUIDELINES = "chat-set-workspace-guidelines"
  const val CHAT_CLEAR_WORKSPACE_GUIDELINES = "chat-clear-workspace-guidelines"
  const val CHAT_SET_USER_GUIDELINES = "chat-set-user-guidelines"
  const val CHAT_CLEAR_USER_GUIDELINES = "chat-clear-user-guidelines"
}
