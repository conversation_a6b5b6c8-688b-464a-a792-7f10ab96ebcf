@file:Suppress("UnstableApiUsage")

package com.augmentcode.intellij.completion

import com.intellij.codeInsight.inline.completion.elements.InlineCompletionElement
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionElementManipulator
import com.intellij.openapi.diagnostic.thisLogger

class AugmentCompletionElementManipulator : InlineCompletionElementManipulator {
  val logger = thisLogger()

  override fun isApplicable(element: InlineCompletionElement): <PERSON><PERSON><PERSON> {
    return element is AugmentCompletionElement
  }

  override fun substring(
    element: InlineCompletionElement,
    startOffset: Int,
    endOffset: Int,
  ): InlineCompletionElement? {
    if (startOffset >= endOffset) {
      return null
    }
    if (element !is AugmentCompletionElement) {
      return null
    }
    return AugmentCompletionElement(element.idx, element.text.substring(startOffset, endOffset), element.requestId)
  }
}
