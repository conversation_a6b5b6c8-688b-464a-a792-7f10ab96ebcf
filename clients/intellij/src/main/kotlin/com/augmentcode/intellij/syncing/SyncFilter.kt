package com.augmentcode.intellij.syncing

import com.augmentcode.api.findDefaultModel
import com.augmentcode.api.findModel
import com.augmentcode.api.isSupportedFileExtension
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VFileProperty
import com.intellij.openapi.vfs.VirtualFile
import kotlinx.coroutines.runBlocking

class SyncFilter(private val project: Project) {
  private val permissionTracker = AugmentSyncingPermissionTracker.getInstance(project)
  private val pathFilterService = PathFilterService.getInstance(project)

  suspend fun isAccepted(
    rootPath: String,
    relPath: String,
    virtualFile: VirtualFile?,
  ): <PERSON><PERSON>an {
    // Need to handle null virtualFile because it is null during project startup.
    // (It's unclear whether this is the right thing.)
    if (virtualFile != null) {
      // If the file is a symlink, do not accept the file
      // The `canonicalPath` is the actual file path of the file and `path` is the path of the symlink.
      if (virtualFile.`is`(VFileProperty.SYMLINK) || virtualFile.canonicalPath != virtualFile.path) {
        return false
      }

      // Check file against model info checks.
      // See AugmentRemoteSyncingManagerImpl#findUnsynced
      if (!modelInfoChecks(virtualFile)) {
        return false
      }
    }

    if (permissionTracker.needsSyncingPermission()) {
      return false
    }

    if (!pathFilterService.isAccepted(rootPath, relPath)) {
      return false
    }

    return true
  }

  private fun modelInfoChecks(file: VirtualFile): Boolean {
    // filtration based on information from the API
    val modelInfo =
      runBlocking {
        @Suppress("DEPRECATION")
        AugmentAPI.instance.fetchModelInfo()
      } ?: return false
    val model =
      modelInfo.findModel(AugmentSettings.instance.modelName)
        ?: modelInfo.findDefaultModel()
    val fileTooLarge = model != null && file.length > modelInfo.featureFlags.maxUploadSizeBytes
    if (fileTooLarge && modelInfo.featureFlags.maxUploadSizeBytes > 0) {
      return false
    }
    return modelInfo.isSupportedFileExtension(file.extension)
  }
}
