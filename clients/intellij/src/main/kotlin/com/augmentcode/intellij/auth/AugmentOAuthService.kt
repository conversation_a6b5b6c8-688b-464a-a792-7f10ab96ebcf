package com.augmentcode.intellij.auth

import com.intellij.collaboration.auth.services.*
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.Url
import com.intellij.util.Urls.newFromEncoded
import org.jetbrains.ide.BuiltInServerManager
import org.jetbrains.ide.RestService
import java.util.concurrent.CompletableFuture

@Service
internal class AugmentOAuthService : OAuthServiceBase<AugmentCredentials>() {
  override val name: String get() = SERVICE_NAME

  fun authorize(): CompletableFuture<AugmentCredentials> {
    return authorize(AugmentOAuthRequest())
  }

  override fun revokeToken(token: String) {
    TODO("Not yet implemented")
  }

  override fun handleOAuthServerCallback(
    path: String,
    parameters: Map<String, List<String>>,
  ): OAuthService.OAuthResult<AugmentCredentials>? {
    val request = currentRequest.get() ?: return null

    if (path != request.request.authorizationCodeUrl.path) {
      request.result.completeExceptionally(RuntimeException("Unexpected authorization URL"))
      logger.warn("Unexpected authorization URL")
      return null
    }

    val code = parameters["code"]?.firstOrNull()
    if (code == null) {
      request.result.completeExceptionally(RuntimeException("No code provided"))
      logger.warn("No code provided")
      return null
    }

    val tenantURL = parameters["tenant_url"]?.firstOrNull()
    if (tenantURL == null) {
      request.result.completeExceptionally(RuntimeException("No tenant URL provided"))
      logger.warn("No tenant URL provided")
      return null
    }

    // We need the tenant_url to get swap our code for a token, so share the parameters with the
    // credential acquirer
    val augmentCredAcq = request.request.credentialsAcquirer as? AugmentOAuthCredentialsAcquirer
    augmentCredAcq?.parameters = parameters

    return super.handleOAuthServerCallback(path, parameters)
  }

  private class AugmentOAuthRequest : OAuthRequest<AugmentCredentials> {
    private val port: Int get() = BuiltInServerManager.getInstance().port

    val oauthState = OAuthState()

    override val authorizationCodeUrl: Url
      get() = newFromEncoded("http://127.0.0.1:$port/${RestService.PREFIX}/$SERVICE_NAME/auth/result")

    override val credentialsAcquirer: OAuthCredentialsAcquirer<AugmentCredentials> =
      AugmentOAuthCredentialsAcquirer(oauthState, authorizationCodeUrl)

    override val authUrlWithParameters: Url =
      AUTHORIZE_URL.addParameters(
        mapOf(
          "response_type" to "code",
          "code_challenge" to oauthState.challenge,
          "code_challenge_method" to "S256",
          "client_id" to "augment-intellij-plugin",
          "redirect_uri" to authorizationCodeUrl.toExternalForm(),
          "state" to oauthState.state,
          // TODO: Make this an array of scopes: ["email"]
          "scope" to "email",
          "prompt" to "login",
        ),
      )

    companion object {
      private val AUTHORIZE_URL: Url
        get() = SERVICE_URL.resolve("authorize")
    }
  }

  companion object {
    private const val SERVICE_NAME = "augment"
    private val logger = thisLogger()

    val instance: AugmentOAuthService
      get() = service()

    val SERVICE_URL: Url = newFromEncoded("https://auth.augmentcode.com")
  }
}
