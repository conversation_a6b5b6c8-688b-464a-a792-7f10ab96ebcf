package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.intellij.sidecarrpc.convertStruct
import com.augmentcode.sidecar.rpc.WriteProcessInputSchema
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.tools.ToolCallResponse
import com.google.protobuf.Struct
import com.intellij.execution.ExecutionManager
import com.intellij.openapi.project.Project
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class WriteProcessTool : IdeTool {
  override val name = "write-process"

  override val description = "Write input to a terminal."

  override val inputMessageDescriptor = WriteProcessInputSchema.getDescriptor()

  override suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    val writeRequest = convertStruct(input) { WriteProcessInputSchema.newBuilder() }.build()

    val processHandler =
      ExecutionManager.getInstance(project).getRunningProcesses()
        .find { AugmentTerminalInfo.TERMINAL_INFO_KEY.get(it)?.terminalId == writeRequest.terminalId }
    if (processHandler == null) {
      return ToolCallResponse.newBuilder()
        .setText("Terminal ${writeRequest.terminalId} not found")
        .setIsError(true)
        .build()
    }

    try {
      withContext(Dispatchers.IO) {
        processHandler.processInput?.write(writeRequest.inputTextBytes.toByteArray())
      }
    } catch (ex: Throwable) {
      return ToolCallResponse.newBuilder()
        .setText("Write failed")
        .setIsError(true)
        .build()
    }
    return ToolCallResponse.newBuilder()
      .setText("Input written to terminal ${writeRequest.terminalId}")
      .build()
  }
}
