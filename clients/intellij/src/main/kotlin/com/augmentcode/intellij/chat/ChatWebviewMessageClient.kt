package com.augmentcode.intellij.chat

import com.augmentcode.api.*
import com.augmentcode.api.FeedbackRating
import com.augmentcode.common.webviews.protos.SaveChatRequest
import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.api.*
import com.augmentcode.intellij.featureflags.DebugFeatures
import com.augmentcode.intellij.guidelines.GuidelinesService
import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.QualifiedPath
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.memories.MemoriesService
import com.augmentcode.intellij.metrics.ChatMetricName
import com.augmentcode.intellij.metrics.ClientMetricsReporter
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.AugmentProjectStateService
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.syncing.AugmentSyncingPermissionTracker
import com.augmentcode.intellij.utils.CustomPropertyReader
import com.augmentcode.intellij.utils.TextRangeCalculator
import com.augmentcode.intellij.utils.propOrFlagBool
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.intellij.webviews.preferences.PreferencesUtils.preferencesEnabled
import com.augmentcode.intellij.webviews.settings.AugmentSettingsWebviewService
import com.augmentcode.intellij.workspacemanagement.coordination.BlobNameService
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.augmentcode.rpc.*
import com.augmentcode.rpc.ExternalSource
import com.augmentcode.rpc.SearchExternalSourcesResponse
import com.augmentcode.sidecar.rpc.chat.ChatRequestNodeType
import com.augmentcode.sidecar.rpc.tools.ToolsStateResponse
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.protobuf.Empty
import com.intellij.codeInsight.daemon.impl.DaemonCodeAnalyzerEx
import com.intellij.codeInsight.daemon.impl.HighlightInfo
import com.intellij.ide.actions.OpenFileAction
import com.intellij.ide.scratch.ScratchFileCreationHelper
import com.intellij.ide.scratch.ScratchRootType
import com.intellij.lang.annotation.HighlightSeverity
import com.intellij.navigation.ChooseByNameRegistry
import com.intellij.navigation.PsiElementNavigationItem
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.application.*
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl
import com.intellij.openapi.project.BaseProjectDirectories.Companion.getBaseDirectories
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.util.UserDataHolderEx
import com.intellij.openapi.util.getOrCreateUserData
import com.intellij.openapi.util.text.LineColumn
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.findOrCreateFile
import com.intellij.psi.PsiElement
import com.intellij.psi.codeStyle.NameUtil
import com.intellij.util.CommonProcessors
import kotlinx.coroutines.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import org.jetbrains.annotations.VisibleForTesting
import public_api.PublicApi.GetModelsResponse.UserTier
import java.io.IOException
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import com.augmentcode.api.SaveChatRequest as SaveChatRequestApi

@VisibleForTesting
class ChatWebviewMessageClient(
  private val project: Project,
  private val cs: CoroutineScope,
) : WebviewChatServiceGrpcKt.WebviewChatServiceCoroutineImplBase() {
  companion object {
    private val logger = thisLogger()

    const val HEAVY_OPERATIONS_CONCURRENCY = 2

    const val DEFAULT_FILE_RESULTS = 12

    const val CHAT_WEBVIEW_HISTORY_WARNING_THRESHOLD_BYTES = 42 * 1024 * 1024 // 42 MB
  }

  private val heavyOperationSemaphore = Semaphore(HEAVY_OPERATIONS_CONCURRENCY)

  private val activeChatStreamsStatuses: MutableMap<String, AtomicBoolean> = ConcurrentHashMap()

  private val clientMetricsReporter = ClientMetricsReporter.getInstance(project)
  private val permissionTracker = AugmentSyncingPermissionTracker.getInstance(project)

  var selectedCodeReferenceRequestId: String? = null
    private set

  fun selectionChanged() {
    selectedCodeReferenceRequestId = null
  }

  override suspend fun chatLoaded(request: ChatLoadedRequest): ChatInitializeResponse {
    val chatIdeEventService = project.service<AugmentChatIdeEventService>()

    val publisher: ChatWebviewMessageBus =
      ChatWebviewMessageBus.syncPublisher(project)
    publisher.postMessageToWebview(chatIdeEventService.getSourceFoldersUpdated())

    // initial list of open files
    EditorTrackingService.getInstance(project).sendCurrentlyOpenedFiles()

    val ctx = AugmentAppStateService.instance.context

    val userTier =
      when (ctx.model?.userTier) {
        UserTier.UNKNOWN -> "unknown"
        UserTier.COMMUNITY_TIER -> "community"
        UserTier.PROFESSIONAL_TIER -> "professional"
        UserTier.ENTERPRISE_TIER -> "enterprise"
        else -> "unknown"
      }

    val flagsSnapshot = AugmentProjectStateService.getInstance(project).context.flags
    return ChatInitializeResponse.newBuilder()
      .setData(
        ChatInitializeResponseData.newBuilder()
          .setEnablePreferenceCollection(preferencesEnabled())
          .setFullFeatured(true) // so context is shown
          .setEnableFlywheel(true) // so the new UI is enabled
          .setUseRichTextHistory(true) // for new formatting of user messages
          .setEnableDebugFeatures(DebugFeatures.enabled())
          .setEnableExternalSourcesInChat(flagsSnapshot.enableExternalSourcesInChat)
          .setEnableShareService(flagsSnapshot.shareServiceEnabled)
          .setUseNewThreadsMenu(flagsSnapshot.useNewThreadsMenu)
          .setEnableSmartPaste(flagsSnapshot.enableSmartPaste)
          .putAllModelDisplayNameToId(flagsSnapshot.additionalChatModels)
          .setSmartPastePrecomputeMode(flagsSnapshot.smartPastePrecomputeMode)
          .setEnableDesignSystemRichTextEditor(flagsSnapshot.enableDesignSystemRichTextEditor)
          .setEnableChatMermaidDiagrams(flagsSnapshot.enableChatMermaidDiagrams)
          .setEnableAgentMode(
            SidecarService.getInstance(project).isAvailable() && flagsSnapshot.agentModeEnabled,
          )
          .setEnableChatMultimodal(flagsSnapshot.chatMultimodalEnabled)
          .setEnableEditableHistory(true)
          .setUserTier(userTier)
          .setConversationHistorySizeThresholdBytes(CHAT_WEBVIEW_HISTORY_WARNING_THRESHOLD_BYTES)
          .setEnableTaskList(flagsSnapshot.taskListEnabled)
          .setEnablePromptEnhancer(flagsSnapshot.promptEnhancerEnabled)
          .setEnableAgentAutoMode(flagsSnapshot.enableAgentAutoMode)
          .setEnableNewThreadsList(flagsSnapshot.enableNewThreadsList)
          .setEnableExchangeStorage(
            propOrFlagBool(
              CustomPropertyReader.ENABLE_EXCHANGE_STORAGE_PROPERTY,
              flagsSnapshot.enableExchangeStorage,
            ),
          )
          .setEnableToolUseStateStorage(
            SidecarService.getInstance(project).isAvailable() &&
              propOrFlagBool(
                CustomPropertyReader.ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY,
                flagsSnapshot.enableToolUseStateStorage,
              ),
          )
          .setUseHistorySummary(flagsSnapshot.useHistorySummary)
          .setHistorySummaryParams(flagsSnapshot.historySummaryParams)
          .setEnableRules(flagsSnapshot.enableRules)
          .setRetryChatStreamTimeouts(flagsSnapshot.retryChatStreamTimeouts)
          .setEnableModelRegistry(flagsSnapshot.enableModelRegistry)
          .putAllModelRegistry(flagsSnapshot.modelRegistry)
          .setAgentChatModel(flagsSnapshot.agentChatModel),
      ).build()
  }

  override fun chatUserMessage(request: ChatUserMessageRequest): Flow<ChatModelReply> {
    return chatUserMessage(request, request.data.modelId)
  }

  fun chatUserMessage(
    request: ChatUserMessageRequest,
    modelId: String? = null,
  ): Flow<ChatModelReply> {
    if (AugmentAppStateService.instance.state != PluginState.ENABLED) {
      val requestId = "needs-sign-in-${System.currentTimeMillis()}"
      return fakeChatReply(
        AugmentBundle.message("chat.message.unavailable"),
        requestId,
      )
    } else if (permissionTracker.needsSyncingPermission()) {
      val requestId = "syncing-permission-needed-${System.currentTimeMillis()}"
      return fakeChatReply(
        AugmentBundle.message("chat.message.syncing.permission.needed"),
        requestId,
      )
    }
    return flow {
      val editor = EditorTrackingService.getInstance(project).getActiveEditor()
      val initialRequestId = UUID.randomUUID().toString()
      val chatRequest = baseChatRequestFromSelection(editor, requestId = initialRequestId)

      chatRequest.message = request.data.text
      chatRequest.chatHistory = request.data.chatHistoryList.let { toTruncatedApiExchange(it, project) }
      if (modelId != null) {
        chatRequest.model = modelId
      }

      chatRequest.blobs =
        if (isV3IndexingEnabled()) {
          WorkspaceCoordinatorService.getInstance(project).getCheckpoint()
        } else {
          AugmentRemoteSyncingManager.getInstance(project)
            .synchronizedBlobsPayload()
        }

      chatRequest.userGuidedBlobs = getUserGuidedBlobs(request.data.userSpecifiedFilesList)
      chatRequest.externalSourceIds = request.data.externalSourceIdsList
      chatRequest.featureDetectionFlags = FeatureDetectionFlags().apply { supportRawOutput = true }

      // Handle guidelines if the feature is enabled
      val guidelinesService = GuidelinesService.getInstance(project)

      // Add user guidelines (always enabled)
      chatRequest.userGuidelines = guidelinesService.getUserGuidelines()

      // Handle workspace guidelines (always enabled)
      val workspaceGuidelines = guidelinesService.getWorkspaceGuidelines()

      if (workspaceGuidelines.isNotEmpty()) {
        chatRequest.workspaceGuidelines = workspaceGuidelines
        logger.info("Including workspace guidelines in chat request (length: ${workspaceGuidelines.length})")
      } else {
        logger.warn("Workspace guidelines are empty, not including in chat request. ")
      }

      // Handle rules if the feature is enabled - use rules from message data (loaded in webview)
      val rulesEnabled = AugmentAppStateService.instance.context.flags.enableRules
      logger.debug("Rules feature enabled: $rulesEnabled")
      if (rulesEnabled) {
        try {
          // Use rules from message data instead of loading them here
          val messageRules = request.data.rulesList
          logger.debug("Using ${messageRules.size} rules from message data")

          if (messageRules.isNotEmpty()) {
            // Convert proto rules to API rules for the chat request
            chatRequest.rules =
              messageRules.map { protoRule ->
                com.augmentcode.api.Rule().apply {
                  type =
                    when (protoRule.type) {
                      com.augmentcode.rpc.RuleType.ALWAYS_ATTACHED -> com.augmentcode.api.RuleType.ALWAYS_ATTACHED
                      com.augmentcode.rpc.RuleType.MANUAL -> com.augmentcode.api.RuleType.MANUAL
                      com.augmentcode.rpc.RuleType.AGENT_REQUESTED -> com.augmentcode.api.RuleType.AGENT_REQUESTED
                      else -> com.augmentcode.api.RuleType.MANUAL
                    }
                  path = protoRule.path
                  content = protoRule.content
                  description = protoRule.description
                }
              }
            logger.info("Including ${chatRequest.rules.size} rules in chat request (from message data)")
            logger.debug("Rules being attached: ${chatRequest.rules.map { "${it.path} (${it.type})" }}")
          } else {
            logger.debug("No rules to attach from message data")
          }
        } catch (e: Exception) {
          logger.error("Failed to process rules from message data: ${e.message}. Rules will be excluded from this chat request.", e)
        }
      }

      val toolState = project.serviceOrNull<SidecarService>()?.getToolState() ?: ToolsStateResponse.getDefaultInstance()
      chatRequest.toolDefinitions =
        toolState.toolsList.map {
          ToolDefinition().apply {
            name = it.name
            description = it.description
            input_schema_json = it.inputSchemaJson
            tool_safety = it.toolSafety.number
          }
        }
      chatRequest.mode = toolState.mode.name.uppercase()
      chatRequest.nodes = convertListToChatRequestNodes(request.data.nodesList, project)

      // for prompt enhancer requests to avoid counted towards credit
      chatRequest.silent = request.data.silent

      if (request.data.memoriesInfo.isClassifyAndDistill) {
        val success = applyClassifyAndDistillPrompt(request, chatRequest)
        if (!success) {
          // log failure
          logger.error("Failed to apply classify and distill prompt")
          return@flow
        }
        chatRequest.silent = true
      } else if (request.data.memoriesInfo.isDistill) {
        // Flag deprecated. Only exists for backwards compatibility in VSCode.
      }

      val (requestId, repliesStream) = AugmentAPI.instance.chat(chatRequest, requestID = initialRequestId)
      // populate the first chat exchange id for the selection
      if (editor != null && readAction { editor.selectionModel.hasSelection() }) {
        (editor as UserDataHolderEx).getOrCreateUserData(FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION) { requestId }
      }

      val workspaceFileChunkPopulator = WorkspaceFileChunkPopulator(project)

      val repliesStreamActiveStatus = AtomicBoolean(true)
      activeChatStreamsStatuses[requestId] = repliesStreamActiveStatus
      repliesStream.onCompletion { activeChatStreamsStatuses.remove(requestId) }

      repliesStream.takeWhile {
        // stop emitting and release http connection if the stream was made inactive
        repliesStreamActiveStatus.get()
      }.collect { reply ->
        if (reply.hasError()) {
          emit(
            ChatModelReply.newBuilder()
              .setData(
                ChatModelReplyData.newBuilder()
                  .setRequestId(requestId)
                  .setError(
                    ChatModelReplyError.newBuilder()
                      .setDisplayErrorMessage(reply.error.displayErrorMessage)
                      .setIsRetriable(reply.error.isRetriable)
                      .build(),
                  ),
              )
              .build(),
          )
          return@collect
        }

        // used for hydrating workspaceFileChunks part of the chat stream chunks
        val updatedFileChunks = workspaceFileChunkPopulator.populateWorkspaceFileChunks(reply.workspaceFileChunksList)

        emit(
          ChatModelReply.newBuilder()
            .setData(
              ChatModelReplyData.newBuilder()
                .setRequestId(requestId)
                .setText(reply.text)
                .addAllWorkspaceFileChunks(updatedFileChunks)
                .addAllNodes(reply.nodesList)
                .setStreaming(true),
            )
            .build(),
        )
      }
    }
  }

  private fun fakeChatReply(
    message: String,
    requestId: String,
  ): Flow<ChatModelReply> {
    val repliesStreamActiveStatus = AtomicBoolean(true)
    activeChatStreamsStatuses[requestId] = repliesStreamActiveStatus

    // Simulate streaming by emitting characters in chunks
    return flow {
      var index = 0
      while (index < message.length && repliesStreamActiveStatus.get()) {
        val chunk = message.substring(index, minOf(index + 10, message.length))
        emit(
          ChatModelReply.newBuilder()
            .setData(
              ChatModelReplyData.newBuilder()
                .setRequestId(requestId)
                .setText(chunk)
                .setStreaming(true),
            ).build(),
        )
        index += 10
        delay(50)
      }
      activeChatStreamsStatuses.remove(requestId)
    }
  }

  override suspend fun chatUserCancel(request: ChatUserCancelRequest): Empty {
    val activeChatStatus = activeChatStreamsStatuses.remove(request.data.requestId)
    if (activeChatStatus == null) {
      logger.warn("No active chat stream with id ${request.data.requestId}")
    } else {
      activeChatStatus.set(false)
    }
    return Empty.getDefaultInstance()
  }

  private suspend fun getUserGuidedBlobs(paths: List<IQualifiedPathName>): List<String> {
    if (isV3IndexingEnabled()) {
      val blobNameService = BlobNameService.getInstance(project)
      val relPaths = paths.map { it.relPath }.toSet()
      return blobNameService.blobsForPaths(relPaths)
    }
    val relPathSet = paths.map { it.relPath }
    return AugmentRemoteSyncingManager.getInstance(project)
      .syncedBlobs()
      .filter { relPathSet.contains(it.relativePath) }
      .map { it.remoteName }
  }

  suspend fun baseChatRequestFromSelection(
    editor: Editor?,
    virtualFile: VirtualFile? = editor?.virtualFile,
    requestId: String? = null,
  ): ChatRequest {
    val result = ChatRequest()
    if (virtualFile != null) {
      result.lang = AugmentAppStateService.instance.context.model?.extensionToLanguageLookup?.get(virtualFile.extension)
      result.path = AugmentRoot.findRelativePathWithRoot(project, virtualFile)?.relativePath
    }
    val selectionDetails = EditorTrackingService.getInstance(project).getSelectionDetails(editor)
    result.contextCodeExchangeRequestId = maybeUpdateSelectedCodeReferenceRequestId(requestId, selectionDetails)
    if (selectionDetails != null) {
      result.prefix = selectionDetails.prefix
      result.suffix = selectionDetails.suffix
      result.selectedCode = selectionDetails.text
    } else {
      // For smart-paste, we need to send these fields even if the user doesn't have the file open.
      result.prefix = ""
      result.suffix = ""
      result.selectedCode = ""
    }
    result.agent_memories = MemoriesService.getInstance(project).getMemories()
    return result
  }

  private fun maybeUpdateSelectedCodeReferenceRequestId(
    requestId: String?,
    selectionDetails: SelectionDetails?,
  ): String? {
    var refReqId = selectedCodeReferenceRequestId
    if (refReqId == null && selectionDetails != null) {
      selectedCodeReferenceRequestId = requestId
      refReqId = "new"
    }
    return refReqId
  }

  override suspend fun findFolder(request: FindFolderRequest): FindFolderResponse {
    val maxResults = if (request.data.maxResults > 0) request.data.maxResults else DEFAULT_FILE_RESULTS

    val matchingFolders =
      if (isV3IndexingEnabled()) {
        findMatchingFoldersV3(
          request.data.relPath,
          request.data.exactMatch,
          maxResults,
        )
      } else {
        findMatchingFoldersV2(
          request.data.relPath,
          request.data.exactMatch,
          AugmentRemoteSyncingManager.getInstance(project).syncedBlobs(),
          maxResults,
        )
      }

    return FindFolderResponse.newBuilder()
      .addAllData(
        matchingFolders.map {
          FileDetails.newBuilder()
            .setPathName(it)
            .build()
        },
      ).build()
  }

  data class FolderInfo(val path: String, val depth: Int, val priority: Int) : Comparable<FolderInfo> {
    override fun compareTo(other: FolderInfo): Int =
      compareBy<FolderInfo> {
        it.priority // priority based on match type (exact name, exact path, immediate child, substring)
      }.thenByDescending {
        it.depth // rank shorter paths higher
      }.thenByDescending {
        it.path // lexicographic order for tiebreakers
      }
        .compare(this, other)
  }

  private fun findMatchingFoldersV2(
    queryPath: String,
    exactMatch: Boolean,
    blobs: Collection<AugmentBlobState>,
    maxResults: Int,
  ): List<String> {
    // Handle exact matches with simple filtering
    if (exactMatch) {
      return blobs
        .asSequence()
        .filter { it.relativePath.contains('/') }
        .map { it.relativePath.substringBeforeLast('/') }
        .filter { it == queryPath }
        .take(maxResults)
        .toList()
    }

    // Process each blob's path
    return findFilesWithPriority(
      queryPath,
      maxResults,
      blobs.map { it.relativePath },
    )
  }

  private suspend fun findMatchingFoldersV3(
    queryPath: String,
    exactMatch: Boolean,
    maxResults: Int,
  ): List<String> {
    val blobNameService = BlobNameService.getInstance(project)
    if (exactMatch) {
      return blobNameService.findDirectories(queryPath)
        .take(maxResults)
        .toList()
    }

    return findFilesWithPriority(
      queryPath,
      maxResults,
      blobNameService.memoryIntensiveGetQualifiedPathNames().map { it.relPath },
    )
  }

  private fun findFilesWithPriority(
    queryPath: String,
    maxResults: Int,
    relativePaths: List<String>,
  ): List<String> {
    val queryLower = queryPath.lowercase()
    val resultQueue = PriorityQueue<FolderInfo>(maxResults)
    val seenPaths = HashSet<String>()

    // Process each blob's path
    for (path in relativePaths) {
      if (!path.contains('/')) continue

      // Process each folder in the path
      var currPath = path
      while (currPath.contains('/')) {
        currPath = currPath.substringBeforeLast('/')
        if (!seenPaths.add(currPath)) continue

        val depth = currPath.count { it == '/' }
        val lastSegment = currPath.substringAfterLast('/')

        val priority =
          when {
            lastSegment == queryPath -> 3 // exact name match
            currPath == queryPath -> 2 // exact path match
            currPath.startsWith("$queryPath/") &&
              currPath.substring(queryPath.length + 1).count { it == '/' } == 0 -> 1 // immediate child match
            currPath.lowercase().contains(queryLower) -> 0 // substring match
            else -> continue // no match, skip to next folder
          }
        resultQueue.offer(FolderInfo(currPath, depth, priority))

        // Trim queue to maxResults
        while (resultQueue.size > maxResults) {
          resultQueue.poll() // remove the lowest priority item
        }
      }
    }

    // Convert queue to sorted list
    return resultQueue
      .sorted()
      .reversed()
      .map { it.path }
  }

  override suspend fun resolveFile(request: ResolveFileRequest): ResolveFileResponse {
    val maxResults = if (request.data.maxResults > 0) request.data.maxResults else DEFAULT_FILE_RESULTS
    val fileDetails =
      queryFileDetails(request.data.relPath, request.data.exactMatch, maxResults).firstOrNull()
        ?: return ResolveFileResponse.getDefaultInstance()
    return ResolveFileResponse.newBuilder()
      .setData(fileDetails)
      .build()
  }

  override suspend fun findFile(request: FindFileRequest): FindFileResponse {
    val maxResults = if (request.data.maxResults > 0) request.data.maxResults else DEFAULT_FILE_RESULTS
    return FindFileResponse.newBuilder()
      .addAllData(queryFileDetails(request.data.relPath, request.data.exactMatch, maxResults))
      .build()
  }

  private suspend fun queryFileDetails(
    userQuery: String,
    exactMatch: Boolean,
    maxResults: Int,
  ): List<FileDetails> {
    if (!exactMatch) {
      // let's do a matching search
      val query = "*$userQuery"
      val relPaths =
        findFileByMatchingPath(query)
          .take(maxResults)
          .toList()

      return relPaths.map { relPath ->
        FileDetails.newBuilder()
          .setPathName(relPath)
          .build()
      }
    }

    // do the exact match
    val query = userQuery

    if (isV3IndexingEnabled()) {
      val absPaths = BlobNameService.getInstance(project).findFile(query)
      for (absPath in absPaths) {
        val vFile = AugmentRoot.findFile(absPath)
        if (vFile == null) continue
        val pathWithRoot = AugmentRoot.findRelativePathWithRoot(project, vFile)
        if (pathWithRoot == null) continue
        return listOf(
          FileDetails.newBuilder()
            .setPathName(pathWithRoot.relativePath)
            .build(),
        )
      }
      return emptyList()
    }

    val blobs = AugmentRemoteSyncingManager.getInstance(project).syncedBlobs()

    // first try to match full relative path
    var matchingRelPath =
      blobs.find {
        it.relativePath == query
      }?.relativePath

    // then try to match by filename
    if (matchingRelPath == null) {
      matchingRelPath =
        blobs.find {
          it.relativePath.endsWith("/$query")
        }?.relativePath ?: return emptyList()
    }

    return listOf(
      FileDetails.newBuilder()
        .setPathName(matchingRelPath)
        .build(),
    )
  }

  private suspend fun findFileByMatchingPath(pattern: String): Sequence<String> {
    val matcher =
      NameUtil.buildMatcher(pattern)
        .withCaseSensitivity(NameUtil.MatchingCaseSensitivity.NONE)
        .build()

    if (isV3IndexingEnabled()) {
      val blobNameService = BlobNameService.getInstance(project)
      return blobNameService.findFiles(matcher).map { it.relPath }
    }

    val allStates = AugmentRemoteSyncingManager.getInstance(project).syncedBlobs()

    return sequence {
      // first prioritize matching my name
      val matchedBlobs = mutableSetOf<String>()
      for (state in allStates) {
        if (matcher.matches(state.relativePath.substringAfterLast('/'))) {
          matchedBlobs.add(state.remoteName)
          yield(state.relativePath)
        }
      }
      // then let's match by the full relative path
      for (state in allStates) {
        if (!matchedBlobs.contains(state.remoteName) && matcher.matches(state.relativePath)) {
          matchedBlobs.add(state.remoteName)
          yield(state.relativePath)
        }
      }
    }
  }

  override suspend fun openFile(request: OpenFileRequest): Empty {
    var file: VirtualFile? = null
    if (isV3IndexingEnabled()) {
      val absPaths = BlobNameService.getInstance(project).findFile(request.data.pathName)
      if (absPaths.isNotEmpty()) {
        // TODO: Choosing the first rel path match may be wrong.
        //       We need to figure out how to resolve ambiguity of the same relative path in multiple roots
        file = AugmentRoot.findFile(absPaths[0])
      }
    } else {
      val blob =
        AugmentRemoteSyncingManager.getInstance(project)
          .syncedBlobs()
          // TODO: Choosing the first rel path match may be wrong.
          //       We need to figure out how to resolve ambiguity of the same relative path in multiple roots
          .find { it.relativePath == request.data.pathName }
      if (blob != null) {
        file = AugmentRoot.findFile(blob.rootPath, blob.relativePath)
      }
    }

    if (file == null) {
      return Empty.getDefaultInstance()
    }

    val range =
      runReadAction {
        val document = FileDocumentManager.getInstance().getDocument(file) ?: return@runReadAction null
        TextRangeCalculator.calculateRange(document, request, project)
      }

    invokeLater {
      OpenFileAction.openFile(file, project)

      val editor = FileEditorManager.getInstance(project).selectedTextEditor
      // Handle ranges or snippets from the request, and select them in the editor
      if (editor != null && range != null) {
        editor.selectionModel.setSelection(range.startOffset, range.endOffset)

        // Scroll to the selection
        editor.caretModel.moveToOffset(range.startOffset)
        editor.scrollingModel.scrollToCaret(ScrollType.CENTER)
      }
    }
    return Empty.getDefaultInstance()
  }

  /**
   * Creates a new file, opens it in the editor, and inserts a codeblock into the new file.
   * If the request has a relative path, the file will be created with that name. Otherwise, the file will be created with a generic name.
   *
   * @param code - The code to insert.
   * @param relPath - The relative path of the file to create.
   */
  override suspend fun chatCreateFile(request: ChatCreateFileRequest): Empty =
    coroutineScope {
      // launch in an async context since "Add file to Git" dialog can appear and block the execution
      // when chat webview expects a response in a timely manner (there is a default timeout)
      val createFileCallback: suspend CoroutineScope.() -> Unit = {
        val file = chatCreateFileImpl(request)
        if (file != null) {
          clientMetricsReporter.reportWebviewClientMetricAsync(
            webviewName = "chat",
            clientMetric = ChatMetricName.CHAT_CODEBLOCK_CREATE,
            value = 1L,
          )
          invokeLater {
            FileEditorManager.getInstance(project).openFile(file, true)
          }
        } else {
          showNotification(
            "File Creation Failed",
            "Could not create file at '${request.data.relPath}'.",
            NotificationType.ERROR,
          )
        }
      }
      if (ApplicationManager.getApplication().isUnitTestMode) {
        createFileCallback.invoke(this)
      } else {
        cs.launch(Dispatchers.IO, block = createFileCallback)
      }
      Empty.getDefaultInstance()
    }

  fun chatCreateFileImpl(request: ChatCreateFileRequest): VirtualFile? {
    if (request.data.relPath.isNullOrEmpty()) {
      return WriteCommandAction.writeCommandAction(project)
        .withName(AugmentBundle.message("chat.insert.code.block.new.file"))
        .compute<VirtualFile?, Throwable> {
          val context = ScratchFileCreationHelper.Context()
          context.text = request.data.code
          ScratchRootType.getInstance()
            .createScratchFile(project, "augment-chat", context.language, context.text, context.createOption)
        }
    }

    val fileName = request.data.relPath
    return WriteCommandAction.writeCommandAction(project).compute<VirtualFile, IOException> {
      val baseDir = project.getBaseDirectories().first()
      val root = AugmentRoot.findActiveProjectRoot(project) ?: baseDir

      val existingFile = root.findFileByRelativePath(fileName)
      if (existingFile != null) {
        showNotification(
          "File Already Exists",
          "Cannot create file at '$fileName'. File already exists.",
          NotificationType.WARNING,
        )
        return@compute existingFile
      }

      try {
        // This will create any necessary parent directories
        return@compute root.findOrCreateFile(fileName).also {
          it.setBinaryContent(request.data.code.toByteArray())
        }
      } catch (_: java.nio.file.InvalidPathException) {
        return@compute null
      }
    }
  }

  private fun showNotification(
    title: String,
    content: String,
    notificationType: NotificationType,
  ) {
    invokeLater {
      Notifications.Bus.notify(
        Notification(
          "augment.notifications",
          title,
          content,
          notificationType,
        ),
        project,
      )
    }
  }

  override suspend fun chatSmartPaste(request: ChatSmartPasteRequest): Empty {
    val virtualFile =
      if (isV3IndexingEnabled()) {
        val absolutePaths = BlobNameService.getInstance(project).findFile(request.data.targetFile)

        // Find the first matching file by relative path.
        // Fallback to any valid file which matches the path.
        var bestMatch: VirtualFile? = null
        val possibleMatches = mutableSetOf<VirtualFile>()
        for (absPath in absolutePaths) {
          val file = AugmentRoot.findFile(absPath) ?: continue
          possibleMatches.add(file)
          val pathInfo = AugmentRoot.findQualifiedPathName(project, file) ?: continue
          if (pathInfo.relPath == request.data.targetFile) {
            bestMatch = file
            break
          }
        }

        bestMatch ?: possibleMatches.firstOrNull()
      } else {
        AugmentBlobStateReader.findFilesByState(project) {
          it.relativePath == request.data.targetFile
        }.firstOrNull()
      }

    if (virtualFile == null) {
      clientMetricsReporter.reportWebviewClientMetricAsync(
        webviewName = "chat",
        clientMetric = ChatMetricName.CHAT_FAILED_SMART_PASTE_RESOLVE_FILE,
        value = 1L,
      )
      return Empty.getDefaultInstance()
    }
    var editor = EditorTrackingService.getInstance(project).getActiveEditor()
    if (editor != null && editor.virtualFile != virtualFile) {
      editor = null
    }
    val chatRequest = baseChatRequestFromSelection(editor, virtualFile)

    val pasteRequest = ChatInstructionStreamPayload()
    pasteRequest.instruction = "" // must be empty for smart paste
    pasteRequest.codeBlock = request.data.generatedCode
    pasteRequest.lang = chatRequest.lang
    pasteRequest.prefix = chatRequest.prefix
    pasteRequest.selectedText = chatRequest.selectedCode ?: ""
    pasteRequest.suffix = chatRequest.suffix
    pasteRequest.path = chatRequest.path

    readAction {
      pasteRequest.prefixBegin = editor?.selectionModel?.selectionStart
      pasteRequest.suffixEnd = editor?.selectionModel?.selectionEnd
    }

    pasteRequest.targetFilePath = request.data.targetFile
    pasteRequest.targetFileContent = SmartPasteManager.latestFileContent(virtualFile)

    pasteRequest.blobs =
      if (isV3IndexingEnabled()) {
        WorkspaceCoordinatorService.getInstance(project).getCheckpoint()
      } else {
        AugmentRemoteSyncingManager.getInstance(project)
          .synchronizedBlobsPayload()
      }

    pasteRequest.chatHistory = request.data.chatHistoryList.let { toTruncatedApiExchange(it, project) }

    // "dryRun" means we should precompute the diff and cache it, but not show it
    val showDiff = !request.data.options.dryRun
    SmartPasteManager.getInstance(project).processSmartPasteRequest(
      project,
      virtualFile,
      pasteRequest,
      showDiff,
    )

    return Empty.getDefaultInstance()
  }

  /**
   * Converts from RPC ChatMode to API ChatMode
   */
  private fun fromRpcChatMode(mode: com.augmentcode.rpc.ChatMode): String {
    return when (mode) {
      com.augmentcode.rpc.ChatMode.AGENT -> "AGENT"
      com.augmentcode.rpc.ChatMode.CHAT -> "CHAT"
      // Default to CHAT for unrecognized values
      com.augmentcode.rpc.ChatMode.UNRECOGNIZED -> "CHAT"
    }
  }

  override suspend fun chatFeedback(request: ChatRatingMessage): ChatRatingDoneMessage {
    val apiRequest =
      ChatFeedbackRequest().apply {
        rating = FeedbackRating.fromRpcType(request.data.rating)
        note = request.data.note
        requestId = request.data.requestId
        mode = fromRpcChatMode(request.data.mode)
      }
    AugmentAPI.instance.chatFeedback(apiRequest)
      ?: throw RuntimeException("Failed to send feedback")
    return ChatRatingDoneMessage.newBuilder()
      .setData(request.data)
      .build()
  }

  override suspend fun findSymbol(request: FindSymbolRequest): FindSymbolResponse {
    return heavyOperationSemaphore.withPermit {
      findSymbolByFilter(
        filter = {
          it == request.data.query ||
            // in case symbol is using a fully qualified name
            it.endsWith("." + request.data.query) ||
            it.endsWith("#" + request.data.query)
        },
        files = request.data.searchScope.filesList,
      )
    }
  }

  override suspend fun findSymbolRegex(request: FindSymbolRegexRequest): FindSymbolResponse {
    // Find symbols is an expensive operation and seems looking by a regex is useless in most cases.
    // Exact match above covers majority if responses from the model
    return FindSymbolResponse.getDefaultInstance()
  }

  private data class ElementLocationData(
    val fileText: String,
    val range: TextRange,
    val elementStart: LineColumn,
    val elementEnd: LineColumn,
  )

  private suspend fun findSymbolByFilter(
    filter: (String) -> Boolean,
    files: List<FileDetails>,
  ): FindSymbolResponse {
    val response = FindSymbolResponse.newBuilder()
    val fileNames = files.map { it.pathName }.toSet()

    val allItems =
      smartReadAction(project) {
        ChooseByNameRegistry.getInstance().symbolModelContributors
          .map { contributor ->
            val allNames = contributor.getNames(project, false)
            allNames.filter(filter).map {
              try {
                contributor.getItemsByName(it, it, project, false).toList()
              } catch (e: Throwable) {
                thisLogger().warn("Failed to get items by name from ${contributor.javaClass.name} contributor", e)
                emptyList()
              }
            }.flatten()
          }.flatten()
      }

    for (item in allItems) {
      val element =
        when (item) {
          is PsiElementNavigationItem -> item.targetElement
          is PsiElement -> item
          else -> null
        } ?: continue

      val virtualFile = element.containingFile?.virtualFile ?: continue
      val relativePath = AugmentRoot.findRelativePathWithRoot(project, virtualFile)?.relativePath ?: continue
      // Don't need to check .gitignore or .augmentignore because symbol info will only inform how chat renders.
      // It won't change what gets sent to the server

      if (fileNames.isNotEmpty()) {
        val elementFile = relativePath
        if (!fileNames.contains(elementFile)) {
          continue
        }
      }

      val locationData =
        readAction {
          val fileText = element.containingFile?.text ?: return@readAction null
          val range = element.textRange ?: return@readAction null
          val elementStart = StringUtil.offsetToLineColumn(fileText, range.startOffset)
          val elementEnd = StringUtil.offsetToLineColumn(fileText, range.endOffset)
          ElementLocationData(fileText, range, elementStart, elementEnd)
        } ?: continue

      val fileDetails =
        FileDetails.newBuilder()
          .setPathName(relativePath)
          .setRange(
            Range.newBuilder()
              .setStart(locationData.elementStart.line + 1)
              .setStop(locationData.elementEnd.line + 1),
          )
          .setFullRange(
            FullRange.newBuilder()
              .setStartLineNumber(locationData.elementStart.line)
              .setStartColumn(locationData.elementStart.column)
              .setEndLineNumber(locationData.elementEnd.line)
              .setEndColumn(locationData.elementEnd.column),
          )
      response.addData(
        FindSymbolResponseData.newBuilder()
          .setFile(fileDetails),
      )
    }

    return response.build()
  }

  override suspend fun findExternalSources(request: FindExternalSourcesRequest): FindExternalSourcesResponse {
    val api = AugmentAPI.instance
    if (!api.available()) {
      return FindExternalSourcesResponse.newBuilder().build()
    }
    val response = api.searchExternalSources(request.data.query, request.data.sourceTypesList)
    return FindExternalSourcesResponse.newBuilder()
      .setData(
        SearchExternalSourcesResponse.newBuilder()
          .addAllSources(
            response?.sources?.map {
              ExternalSource.newBuilder()
                .setId(it.id)
                .setName(it.name)
                .setTitle(it.title)
                .build()
            } ?: emptyList(),
          ),
      )
      .build()
  }

  override suspend fun findRecentlyOpenedFiles(request: FindRecentlyOpenedFilesRequest): FindRecentlyOpenedFilesResponse {
    val editorManager = FileEditorManager.getInstance(project)
    val recentFiles =
      runReadAction {
        if (editorManager is FileEditorManagerImpl) {
          // similar to ShowRecentFilesAction
          editorManager.getSelectionHistory().map { it.first }
        } else {
          editorManager.openFiles.toList()
        }
      }

    val recentStates =
      recentFiles.mapNotNull {
        val path = AugmentRoot.findRelativePathWithRoot(project, it) ?: return@mapNotNull null
        path.relativePath ?: return@mapNotNull null
        QualifiedPath(path.rootPath, path.relativePath)
      }.filter {
        // filter out .gitignore and .augmentignored files
        PathFilterService.getInstance(project).isAccepted(it.rootPath, it.relativePath)
      }
    val query = request.data.relPath

    val matchedStates =
      if (request.data.exactMatch) {
        recentStates.filter {
          it.relativePath == query || it.relativePath.endsWith("/$query")
        }
      } else {
        val matcher =
          NameUtil.buildMatcher("*$query")
            .withCaseSensitivity(NameUtil.MatchingCaseSensitivity.NONE)
            .build()
        recentStates.filter {
          matcher.matches(it.relativePath)
        }
      }

    val maxResults = if (request.data.maxResults > 0) request.data.maxResults else DEFAULT_FILE_RESULTS
    return FindRecentlyOpenedFilesResponse.newBuilder()
      .addAllData(
        matchedStates.take(maxResults).map {
          FileDetails.newBuilder()
            .setPathName(it.relativePath)
            .build()
        },
      ).build()
  }

  override suspend fun saveChat(request: SaveChatRequest): SaveChatDoneResponse {
    val saveChatRequestData = request.data
    val apiRequest =
      SaveChatRequestApi().apply {
        conversation_id = saveChatRequestData.conversationId
        chat =
          saveChatRequestData.getChatHistoryList().map {
            toApiExchange(it, project)
          }.toTypedArray()
        title = saveChatRequestData.title
      }

    val response = AugmentAPI.instance.saveChat(apiRequest) ?: throw RuntimeException("Failed to save chat")

    logger.info("Saved chat with uuid: ${response.uuid}")
    val saveChatDoneResponse =
      SaveChatDoneResponse.newBuilder()
        .setData(
          SaveChatDoneResponseData.newBuilder()
            .setUuid(response.uuid)
            .setUrl(response.url),
        )
        .build()

    return saveChatDoneResponse
  }

  /**
   * Handles the OpenSettingsPage request from the webview.
   * Opens the settings panel with the specified section focused.
   * This is invoked when the user clicks on the settings context menu item.
   */
  override suspend fun openSettingsPage(request: OpenSettingsPageRequest): Empty {
    val section = request.data
    AugmentSettingsWebviewService.getInstance(project).openSettingsWebview(section)
    return Empty.getDefaultInstance()
  }

  /**
   * Sends the current guidelines state to the webview.
   */
  @VisibleForTesting
  internal fun sendGuidelinesStateToWebview() {
    // User guidelines are always enabled
    val guidelinesService = GuidelinesService.getInstance(project)
    val guidelinesStates = guidelinesService.getGuidelinesStates()

    val request =
      UpdateGuidelinesStateRequest
        .newBuilder()
        .setData(guidelinesStates)
        .build()

    val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
    publisher.postMessageToWebview(request)
  }

  override suspend fun getDiagnostics(request: GetDiagnosticsRequest): GetDiagnosticsResponse {
    val editor = FileEditorManager.getInstance(project).selectedTextEditor
    val document =
      editor?.document
        ?: return GetDiagnosticsResponse.getDefaultInstance()

    val fileState = (
      AugmentBlobStateReader.read(project, editor.virtualFile)
        ?: return GetDiagnosticsResponse.getDefaultInstance()
    )

    // filter out .gitignore and .augmentignore
    if (!PathFilterService.getInstance(project).isAccepted(fileState.rootPath, fileState.relativePath)) {
      return GetDiagnosticsResponse.getDefaultInstance()
    }

    val highlightsInfo = CommonProcessors.CollectProcessor<HighlightInfo>()
    readAction {
      val caret = editor.caretModel.primaryCaret
      if (caret.hasSelection()) {
        DaemonCodeAnalyzerEx.processHighlights(
          document,
          project,
          null,
          caret.selectionStart,
          caret.selectionEnd,
          highlightsInfo,
        )
      }
    }

    val result = GetDiagnosticsResponse.newBuilder()
    for (info in highlightsInfo.results) {
      val severity =
        when (info.severity) {
          HighlightSeverity.ERROR -> DiagnosticSeverity.ERROR
          HighlightSeverity.WARNING -> DiagnosticSeverity.WARNING
          HighlightSeverity.INFORMATION -> DiagnosticSeverity.INFORMATION
          HighlightSeverity.WEAK_WARNING -> DiagnosticSeverity.HINT
          else -> DiagnosticSeverity.ERROR
        }
      result.addData(
        Diagnostic.newBuilder()
          .setLocation(
            DiagnosticFileLocation.newBuilder()
              .setPath(fileState.relativePath)
              .setLineStart(info.actualStartOffset)
              .setLineEnd(info.actualEndOffset),
          )
          .setMessage(info.description ?: continue)
          .setSeverity(severity)
          .setCurrentBlobName(fileState.remoteName), // use name as it known by the server
      )
    }
    return result.build()
  }

  /**
   * Applies the classify and distill prompt to the chat request.
   *
   * @param request The original chat request from the webview
   * @param chatRequest The chat request to be sent to the API
   * @return true if successful, false if there was an error
   */
  private fun applyClassifyAndDistillPrompt(
    request: ChatUserMessageRequest,
    chatRequest: ChatRequest,
  ): Boolean {
    val flagsSnapshot = AugmentAppStateService.instance.context.flags
    val memoriesParams = flagsSnapshot.memoriesParams
    val memoriesParamsJson = Gson().fromJson(memoriesParams, JsonObject::class.java)
    val classifyAndDistillPrompt = memoriesParamsJson.get("classify_and_distill_prompt")?.asString
    if (classifyAndDistillPrompt.isNullOrEmpty()) {
      logger.error("Classify and distill prompt missing.")
      return false
    }

    // Apply the prompt to the message text
    chatRequest.message = classifyAndDistillPrompt.replace("{message}", request.data.text)

    // Apply the prompt to any text nodes
    if (request.data.nodesCount > 0) {
      val updatedNodes =
        request.data.nodesList.map { node ->
          if (node.type == ChatRequestNodeType.TEXT.number && node.hasTextNode()) {
            val updatedTextNode =
              node.textNode.toBuilder()
                .setContent(classifyAndDistillPrompt.replace("{message}", node.textNode.content))
                .build()

            node.toBuilder()
              .setTextNode(updatedTextNode)
              .build()
          } else {
            node
          }
        }
      chatRequest.nodes = convertListToChatRequestNodes(updatedNodes, project)
    }

    return true
  }
}
