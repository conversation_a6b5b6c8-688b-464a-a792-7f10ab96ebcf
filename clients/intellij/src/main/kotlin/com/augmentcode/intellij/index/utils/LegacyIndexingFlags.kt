package com.augmentcode.intellij.index.utils

import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.utils.CustomPropertyReader
import com.intellij.openapi.diagnostic.Logger

/**
 * Utility for checking if legacy indexing is disabled via custom property or feature flag.
 *
 * Priority order (highest to lowest):
 * 1. Custom property `augmentcode.indexing.legacy.disabled` if explicitly set
 * 2. Feature flag `intellijIndexingV3Enabled` from AugmentAppStateService
 * 3. Default behavior (legacy indexing enabled)
 *
 * The property can be set in idea.properties:
 * augmentcode.indexing.legacy.disabled=true
 */

private val legacyIndexingDisabledProperty =
  CustomPropertyReader.readProperty(
    CustomPropertyReader.LEGACY_INDEXING_DISABLED_PROPERTY,
  )?.toBoolean()

// Since we are not inside a class, we need to use the getInstance method
private val logger = Logger.getInstance("com.augmentcode.intellij.index.utils.LegacyIndexingFlags")

fun isLegacyIndexingDisabled(): Bo<PERSON>an {
  // If custom property is explicitly set, use that value
  legacyIndexingDisabledProperty?.let { return it }

  // Otherwise check the feature flag
  return try {
    AugmentAppStateService.instance.context.flags.indexingV3Enabled
  } catch (e: Exception) {
    // If we fail to get the feature flag, default to false
    logger.warn("Failed to get indexing v3 feature flag, defaulting to false", e)
    false
  }
}
