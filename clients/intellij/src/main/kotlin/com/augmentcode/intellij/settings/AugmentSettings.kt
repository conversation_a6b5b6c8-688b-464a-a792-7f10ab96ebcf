package com.augmentcode.intellij.settings

import com.intellij.openapi.components.*

@Service(Service.Level.APP)
@State(name = "AugmentSettings", storages = [Storage("AugmentSettings.xml")])
class AugmentSettings : SimplePersistentStateComponent<AugmentSettingsState>(AugmentSettingsState()) {
  companion object {
    val instance: AugmentSettings
      get() = service()

    val debugFeaturesEnabled: Boolean
      get() = java.lang.Boolean.getBoolean("augment.debug.features.enabled")
  }

  var apiToken
    get() = state.apiToken
    set(value) {
      state.setProperty(state::apiToken, value)
    }
  var completionURL
    get() = state.completionURL
    set(value) {
      state.setProperty(state::completionURL, value)
    }
  var inlineCompletionEnabled
    get() = state.inlineCompletionEnabled
    set(value) {
      state.setProperty(state::inlineCompletionEnabled, value)
    }
  var disableForFileTypes
    get() = state.disableForFileTypes
    set(value) {
      state.setProperty(state::disableForFileTypes, value)
    }
  var modelName
    get() = state.modelName
    set(value) {
      state.setProperty(state::modelName, value)
    }
}
