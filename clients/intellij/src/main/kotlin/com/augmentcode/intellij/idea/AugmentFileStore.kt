package com.augmentcode.intellij.idea

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.nio.file.attribute.DosFileAttributeView

/**
 * Utility class that provides a common path for the plugin to
 * store files across plugin restarts and upgrades as well as
 * IntelliJ restarts and upgrades.
 */
class AugmentFileStore {
  companion object {
    fun getGlobalSubDirectory(subdir: String): Path {
      val globalDir = getGlobalDirectory()
      val subdirPath: Path = globalDir.resolve(subdir)
      Files.createDirectories(subdirPath)
      return subdirPath
    }

    fun getSubDirectoryForProject(
      project: Project,
      subdir: String,
    ): Path {
      val projectDir = getProjectDirectory(project)
      val subdirPath: Path = projectDir.resolve(subdir)
      Files.createDirectories(subdirPath)
      return subdirPath
    }

    private fun getProjectDirectory(project: Project): Path {
      val baseDir = createAndGetBaseDirectory()
      val projectsDir = baseDir.resolve("projects")
      val projectDir = projectsDir.resolve(project.locationHash)
      return projectDir
    }

    private fun getGlobalDirectory(): Path {
      val baseDir = createAndGetBaseDirectory()
      val globalDir: Path = baseDir.resolve("global")
      return globalDir
    }

    private fun createAndGetBaseDirectory(): Path {
      val env = SystemEnvironment.getenv()

      // Provide a mechanism to override the default state directory
      // (e.g. for testing)
      val augmentStateHome = env["AUGMENT_STATE_HOME"]

      val homePath =
        when {
          // Do no append anything since we are defining the path
          augmentStateHome != null -> Paths.get(augmentStateHome)
          else -> Paths.get(SystemEnvironment.getProperty("user.home") ?: "", ".augmentcode")
        }

      // We want the homePath (.augmentcode) to be hidden on Windows
      // so create it and make it hidden if necessary
      if (!Files.exists(homePath)) {
        Files.createDirectories(homePath)
        // Check if platform is Windows
        val isWindows = SystemEnvironment.getProperty("os.name")?.lowercase()?.contains("windows") ?: false
        if (isWindows) {
          thisLogger().info("Setting hidden attribute on Windows ${homePath.toAbsolutePath()}")
          // Set hidden attribute on Windows
          Files.getFileAttributeView(homePath.toAbsolutePath(), DosFileAttributeView::class.java)?.setHidden(true)
        }
      }

      // augment may be common for our clients, so lets namespace it with intellij
      val baseDir = homePath.resolve("intellij")
      return baseDir
    }

    fun migrateFiles() {
      val newDir = createAndGetBaseDirectory()
      if (Files.exists(newDir)) {
        // .augmentcode/intellij/ already exists, so we have already migrated
        thisLogger().info("Augment file store already exists, skipping migration")
        return
      }

      val env = SystemEnvironment.getenv()

      val previousPaths =
        mutableListOf(
          // Check the default path
          Paths.get(SystemEnvironment.getProperty("user.home") ?: "", ".local/state/augment", "intellij"),
        )

      // Check the xdg path if it is defined
      val xdgStateHome = env["XDG_STATE_HOME"]
      if (xdgStateHome != null) {
        previousPaths.add(Paths.get(xdgStateHome, "augment", "intellij"))
      }

      previousPaths.forEach { oldDir ->
        thisLogger().info("Checking previous path ${oldDir.toAbsolutePath()}")
        if (Files.exists(oldDir)) {
          Files.move(oldDir, newDir)
        }
      }
    }
  }
}

// This object is here to make it easy to mock system functions in tests
object SystemEnvironment {
  fun getenv(): MutableMap<String, String> = System.getenv()

  fun getProperty(key: String): String? = System.getProperty(key)
}
