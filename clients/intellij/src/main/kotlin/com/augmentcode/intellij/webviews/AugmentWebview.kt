package com.augmentcode.intellij.webviews

import com.intellij.ide.ui.UISettings
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.intellij.ui.jcef.JBCefBrowser
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

class AugmentWebview(
  val project: Project,
  val browser: JBCefBrowser,
  val rpcAdapter: RPCAdapter,
) {
  companion object {
    const val INTELLIJ_STYLES_CSS = "injected-styles/intellij.css"

    private val augmentThemeCategory = {
      if (JBColor.isBright()) {
        "light"
      } else {
        "dark"
      }
    }
    private val augmentThemeIntensity = {
      if (EditorColorsManager.getInstance().globalScheme.name.contains("high contrast", ignoreCase = true)) {
        "high-contrast"
      } else {
        "regular"
      }
    }
    val augmentThemeAttributesJS: () -> String = {
      """
      window.document.body.classList.add("augment-intellij");
      window.document.documentElement.setAttribute("data-augment-theme-category", "${augmentThemeCategory()}");
      window.document.documentElement.setAttribute("data-augment-theme-intensity", "${augmentThemeIntensity()}");
      """.trimIndent()
    }
  }

  private val cs = CoroutineScope(SupervisorJob())

  fun postMessage(message: String) {
    cs.launch(Dispatchers.IO) {
      rpcAdapter.postMessage(message)
    }
  }

  /**
   * When the user changes the IntelliJ theme, we need to reload the styles.
   * We do this by just triggering a re-download by re-assigning the href.
   */
  fun reloadIntellijStyles() {
    cs.launch(Dispatchers.IO) {
      try {
        rpcAdapter.executeJavaScript(
          """
          (function () {
            try {
              var node = document.querySelectorAll("link[href*=\"${INTELLIJ_STYLES_CSS}\"]")[0];
              node.href = node.href;
              ${augmentThemeAttributesJS()}
            } catch (err) {
              console.error("error refreshing styles after intellij theme change:", err);
            }
          })();
          """.trimIndent(),
        )
      } catch (ex: Throwable) {
        thisLogger().warn("Failed to reload styles after intellij theme change", ex)
      }
    }
  }

  /**
   * Update the zoom level of the browser.
   */
  fun updateZoomLevel() {
    browser.zoomLevel = UISettings.getInstance().ideScale.toDouble()
  }
}
