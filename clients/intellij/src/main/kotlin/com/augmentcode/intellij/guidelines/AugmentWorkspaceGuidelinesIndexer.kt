package com.augmentcode.intellij.guidelines

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.indexing.DataIndexer
import com.intellij.util.indexing.FileContent

/**
 * Indexer for the Augment Workspace Guidelines index.
 */
class AugmentWorkspaceGuidelinesIndexer : DataIndexer<Boolean, Void, FileContent> {
  override fun map(inputData: FileContent): Map<Boolean, Void?> {
    var logger = thisLogger()
    logger.info("Indexing workspace guidelines file: ${inputData.file.path}")
    if (inputData.project != null && inputData.file.name == GuidelinesService.WORKSPACE_GUIDELINES_ASSET_PATH) {
      // Only process .augment-guidelines files

      val content = String(inputData.content)
      logger.info("Workspace guidelines content: $content")
      // Notify the GuidelinesService about the updated guidelines
      val project = inputData.project
      GuidelinesService.getInstance(project).guidelinesUpdated(
        content = content,
        workspaceFolder = inputData.file.parent.path,
      )

      // Return true to indicate this file is indexed
      return mapOf(true to null)
    }

    // Return empty map for files that aren't guidelines
    return emptyMap()
  }
}
