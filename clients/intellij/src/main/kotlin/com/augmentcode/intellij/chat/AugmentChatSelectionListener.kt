package com.augmentcode.intellij.chat

import com.intellij.openapi.editor.event.EditorFactoryEvent
import com.intellij.openapi.editor.event.EditorFactoryListener
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.removeUserData

/**
 * This key is used to store a chat request id a given selection in an editor was the first part of.
 */
val FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION = Key.create<String>("augment.first.chat.exchange.id.for.selection")

class AugmentChatSelectionListener : EditorFactoryListener {
  override fun editorCreated(event: EditorFactoryEvent) {
    event.editor.selectionModel.addSelectionListener(
      object : SelectionListener {
        override fun selectionChanged(event: SelectionEvent) {
          // if selection changes then disregard the last request id since it's not relevant anymore
          event.editor.removeUserData(FIRST_CHAT_EXCHANGE_ID_FOR_SELECTION)
        }
      },
    )
  }
}
