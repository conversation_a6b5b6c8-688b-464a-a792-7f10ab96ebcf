package com.augmentcode.intellij.sentry

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Startup activity to initialize Sentry when the plugin starts.
 * This includes initializing the Sentry service, installing the global exception handler,
 * and initializing project-specific metadata collection.
 */
class SentryStartupActivity : ProjectActivity {
  private val logger = thisLogger()

  override suspend fun execute(project: Project) {
    try {
      // Initialize Sentry in an I/O context since feature flag checks involve blocking network calls
      withContext(Dispatchers.IO) {
        logger.info("Initializing Sentry for project: ${project.name}")

        // Initialize the application-level Sentry service (safe to call multiple times)
        service<SentryService>()
        // This automatically starts metrics collection for this project
        SentryProjectMetadataService.getInstance(project)

        logger.info("Sentry initialization completed for project: ${project.name}")
      }
    } catch (e: Exception) {
      logger.error("Failed to initialize Sentry for project: ${project.name}", e)
    }
  }
}
