package com.augmentcode.intellij.api

import com.augmentcode.api.*
import com.augmentcode.intellij.sidecar.clientinterfaces.PluginFileStore
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.chat.ChatRequestNodeType
import com.augmentcode.sidecar.rpc.chat.ImageFormatType
import com.google.gson.GsonBuilder
import com.intellij.openapi.project.Project
import java.util.Base64
import com.augmentcode.sidecar.rpc.chat.ChatRequestNode as ChatRequestNodeProto

// Maximum character limit for chat history to avoid large payloads
private const val MAX_HISTORY_CHARS = 800_000

/**
 * Limits chat history to avoid large payloads. Even though it would be faster
 * to use ChatHistoryItem.serialzedSize to estimate size before converting from proto to API type,
 * toApiExchange populates the exchange with image data, drastically changing the payload size.
 */
fun toTruncatedApiExchange(
  items: List<ChatHistoryItem>,
  project: Project,
): List<Exchange> {
  val apiExchanges = items.map { toApiExchange(it, project) }
  return limitChatHistory(apiExchanges)
}

fun toApiExchange(
  item: ChatHistoryItem,
  project: Project,
): Exchange {
  return Exchange().apply {
    requestMessage = item.requestMessage
    responseText = item.responseText
    requestId = item.requestId
    request_nodes = convertListToChatRequestNodes(item.requestNodesList, project)
    response_nodes =
      item.responseNodesList.map {
        ChatResultNode().apply {
          id = it.id
          type = it.type.number
          content = it.content
          toolUse =
            if (it.hasToolUse()) {
              ChatResultToolUse().apply {
                tool_use_id = it.toolUse.toolUseId
                tool_name = it.toolUse.toolName
                input_json = it.toolUse.inputJson
              }
            } else {
              null
            }
          agentMemory =
            if (it.hasAgentMemory()) {
              ChatResultAgentMemory().apply {
                content = it.agentMemory.content
              }
            } else {
              null
            }
        }
      }
  }
}

/**
 * Limits chat history to avoid large payloads.
 */
fun limitChatHistory(history: List<Exchange>): List<Exchange> {
  val gson = GsonBuilder().create()
  // Only send the latest exchanges up to estimated character budget
  var i = history.size - 1
  var totalCharEstimation = 0

  while (i >= 0) {
    val exchange = history[i]
    // Estimate size by converting to JSON string length
    totalCharEstimation += gson.toJson(exchange).length
    if (totalCharEstimation > MAX_HISTORY_CHARS) {
      break
    }
    i--
  }

  return history.subList(i + 1, history.size)
}

fun convertListToChatRequestNodes(
  nodes: List<ChatRequestNodeProto>,
  project: Project,
): List<ChatRequestNode> {
  return nodes.map {
    val node =
      ChatRequestNode().apply {
        id = it.id
        type = it.type
        textNode =
          if (it.hasTextNode()) {
            ChatRequestText().apply { content = it.textNode.content }
          } else {
            null
          }
        toolResultNode =
          if (it.hasToolResultNode()) {
            ChatRequestToolResult().apply {
              toolUseId = it.toolResultNode.toolUseId
              content = it.toolResultNode.content
              isError = it.toolResultNode.isError
              requestId = it.toolResultNode.requestId
            }
          } else {
            null
          }
        image_node =
          if (it.hasImageNode()) {
            ChatRequestImage().apply {
              image_data = it.imageNode.imageData
              format = it.imageNode.format.number
            }
          } else {
            null
          }
        image_id_node =
          if (it.hasImageIdNode()) {
            ChatRequestImageId().apply {
              image_id = it.imageIdNode.imageId
              format = it.imageIdNode.format.number
            }
          } else {
            null
          }
        ide_state_node =
          if (it.hasIdeStateNode()) {
            ChatRequestIdeState().apply {
              workspace_folders =
                it.ideStateNode.workspaceFoldersList.map {
                  WorkspaceFolderInfo().apply {
                    repository_root = it.repositoryRoot
                    folder_root = it.folderRoot
                  }
                }
              workspace_folders_unchanged = it.ideStateNode.workspaceFoldersUnchanged
              current_terminal =
                if (it.ideStateNode.hasCurrentTerminal()) {
                  TerminalInfo().apply {
                    terminal_id = it.ideStateNode.currentTerminal.terminalId
                    current_working_directory = it.ideStateNode.currentTerminal.currentWorkingDirectory
                  }
                } else {
                  null
                }
            }
          } else {
            null
          }
      }

    // Hydrate IMAGE_ID nodes by loading the actual image data from PluginFileStore
    if (it.type == 3 && it.hasImageIdNode()) { // 3 = IMAGE_ID
      val imageId = it.imageIdNode.imageId
      val pluginFileStore = PluginFileStore(project)
      val imageData = pluginFileStore.loadAsset(imageId)

      if (imageData != null) {
        // Convert to base64 string
        val base64Data = Base64.getEncoder().encodeToString(imageData)

        // Create a new node with the image data instead of the image ID
        return@map ChatRequestNode().apply {
          id = it.id
          type = ChatRequestNodeType.IMAGE.number // 2 = IMAGE
          image_node =
            ChatRequestImage().apply {
              image_data = base64Data
              // Chat webview always converts to PNG when scaling image, so we can hardcode this
              format = ImageFormatType.PNG.number
            }
        }
      }
    }

    node
  }
}
