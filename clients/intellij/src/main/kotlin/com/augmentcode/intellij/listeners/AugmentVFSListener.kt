package com.augmentcode.intellij.listeners

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileContentChangeEvent
import com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.intellij.openapi.vfs.newvfs.events.VFileMoveEvent
import com.intellij.openapi.vfs.newvfs.events.VFilePropertyChangeEvent
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.nio.file.Paths

/**
 * Listens for VFS events to maintain synchronization with remote system.
 * Handles file deletions and moves to ensure proper cleanup of remote references.
 */
class AugmentVFSListener(private val project: Project, private val cs: CoroutineScope) : BulkFileListener {
  override fun before(events: List<VFileEvent>) {
    super.before(events)

    val syncManager = AugmentRemoteSyncingManager.getInstance(project)
    val workspaceCoordinator = WorkspaceCoordinatorService.getInstance(project)

    val ignoreFileEvents = mutableListOf<VirtualFile>()
    val directoryEvents = mutableListOf<VFileEvent>()
    val contentEvents = mutableListOf<String>() // list of paths
    events.forEach { event ->
      if (requiresIgnoreFileCheck(event)) event.file?.let { ignoreFileEvents.add(it) }
      if (requiresDirectoryRemoval(event)) {
        directoryEvents.add(event)
      } else if (requiresContentRemoval(event)) {
        event.file?.path?.let { contentEvents.add(it) }
      }
    }

    if (ignoreFileEvents.isNotEmpty()) {
      cs.launch { ignoreFileEvents.forEach { handleIgnoreFileChange(it) } }
    }

    if (directoryEvents.isNotEmpty()) {
      cs.launch {
        directoryEvents.forEach { event ->
          val (oldPath, newPath) = getOldAndNewPath(event)
          thisLogger().info("Directory moved or deleted: $oldPath, $newPath")
          workspaceCoordinator.onDirectoryDeleted(Paths.get(oldPath))
          syncManager.notifyDirectoryRemoved(oldPath)
        }
      }
    }

    if (contentEvents.isNotEmpty()) {
      cs.launch {
        contentEvents.forEach { path ->
          thisLogger().info("File deleted: $path")
          workspaceCoordinator.onFileDeleted(Paths.get(path))
          syncManager.notifyContentRemoved(path)
        }
      }
    }
  }

  override fun after(events: List<VFileEvent>) {
    super.after(events)

    // Reindex files that were moved
    events.asSequence()
      .filterIsInstance<VFileMoveEvent>()
      .map { it.file }
      .forEach { FileBasedIndex.getInstance().requestReindex(it) }
  }

  private fun handleIgnoreFileChange(file: VirtualFile?) {
    if (file == null) return
    if (PathFilterService.ignoreFilenames.contains(file.name)) {
      val root = AugmentRoot.findRelativePathWithRoot(project, file)?.rootFile ?: return
      PathFilterService.getInstance(project).uncachePathFilter(root)
    }
  }

  private fun requiresDirectoryRemoval(event: VFileEvent): Boolean =
    (event is VFilePropertyChangeEvent && event.propertyName == "name" && event.file.isDirectory) ||
      (event is VFileMoveEvent && event.file.isDirectory) ||
      (event is VFileDeleteEvent && event.file.isDirectory)

  private fun getOldAndNewPath(event: VFileEvent): Pair<String, String> =
    when (event) {
      is VFilePropertyChangeEvent -> Pair(event.oldPath, event.file.path)
      is VFileMoveEvent -> Pair(event.oldPath, event.file.path)
      is VFileDeleteEvent -> Pair(event.file.path, "")
      else -> Pair("", "")
    }

  private fun requiresContentRemoval(event: VFileEvent): Boolean =
    event is VFileDeleteEvent ||
      event is VFileMoveEvent ||
      (event is VFilePropertyChangeEvent && event.propertyName == "name")

  private fun requiresIgnoreFileCheck(event: VFileEvent): Boolean = requiresContentRemoval(event) || event is VFileContentChangeEvent
}
