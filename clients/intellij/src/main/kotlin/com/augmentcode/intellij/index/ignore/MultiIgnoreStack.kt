package com.augmentcode.intellij.index.ignore

import com.intellij.openapi.vfs.VirtualFile
import org.jetbrains.annotations.VisibleForTesting

class MultiIgnoreStack(private val ignoreStacks: List<SingleIgnoreStack>) {
  @VisibleForTesting
  fun getIgnoreStacks(): List<SingleIgnoreStack> {
    return ignoreStacks
  }

  fun buildAtop(dir: VirtualFile): MultiIgnoreStack {
    val newStacks = mutableListOf<SingleIgnoreStack>()
    var diffCount = 0
    for (baseStack in ignoreStacks) {
      val ignoreStack = baseStack.buildAtop(dir)
      if (ignoreStack !== baseStack) {
        diffCount++
      }
      newStacks.add(ignoreStack)
    }

    if (diffCount == 0) {
      // No new ignore files were found. Just keep using this same multi-stack.
      return this
    }
    return MultiIgnoreStack(newStacks)
  }

  suspend fun isAccepted(file: VirtualFile): Boolean? {
    for (i in ignoreStacks.size - 1 downTo 0) {
      val ignoreStack = ignoreStacks[i]
      val verdict: Boolean? = ignoreStack.isAccepted(file)
      if (verdict != null) {
        return verdict
      }
    }
    return null
  }
}
