package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.utils.CoalescingExecutor
import com.augmentcode.intellij.utils.ProperThreadUsageChecker
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import public_api.PublicApi
import kotlin.coroutines.cancellation.CancellationException

/**
 * This class is responsible for updating the plugin state. It uses a coalescing executor
 * to ensure that only one instance of the state update is running at a time.
 *
 * It emits changes via the onStateChange callback function.
 */
class ProjectStateExecutor(
  val project: Project,
  val cs: CoroutineScope,
  parentDisposable: Disposable,
  val onStateChange: (PluginContext, PluginState) -> Unit,
) {
  companion object {
    private val logger = thisLogger()
  }

  private val executor =
    CoalescingExecutor(cs, parentDisposable) {
      run()
    }

  fun updateState() {
    executor.trigger()
  }

  private suspend fun run() {
    // Should not be on EDT so log a warning if that is not the case.
    ProperThreadUsageChecker.shouldNotBeOnEDT("PluginStateExecutor.run")

    logger.info("Starting PluginState initialization...")

    try {
      val appState = AugmentAppStateService.instance.state
      if (appState !== PluginState.ENABLED) {
        logger.info("App state is not enabled, using same values.")
        SidecarService.getInstance(project).stopServer()
        onStateChange(AugmentAppStateService.instance.context, appState)
        return
      }

      try {
        logger.info("Starting Sidecar...")
        SidecarService.getInstance(project).startServer()
      } catch (e: Exception) {
        // NOTE: We could fail the project state service if we want to treat sidecar as a required service.
        // At the moment it's treated as optional since it only impacts agents and we can disable it if sidecar
        // ever fails.
        logger.warn("Failed to start sidecar, will continue to enable project state service.", e)
      }

      logger.info("Enabling project state.")
      onStateChange(AugmentAppStateService.instance.context, PluginState.ENABLED)
    } catch (e: CancellationException) {
      // Re-throw the exception to cancel coroutines safely
      throw e
    } catch (e: Exception) {
      // TODO (mattgauntseo): 401 (unauthenticated) errors are currently swallowed by the API layer.
      //                      We should throw an exception and show a useful status to the user.

      logger.warn("Failed to initialize plugin", e)
      onStateChange(AppStateExecutor.defaultContext, PluginState.FAILED)
    }
  }

  private fun createContext(modelConfig: PublicApi.GetModelsResponse): PluginContext {
    logger.info("Creating context...")
    val flags = FeatureFlags.fromGetModelsResponse(modelConfig)
    val model = AugmentModel.fromGetModelsResponse(modelConfig)
    return PluginContext(true, flags, model)
  }

  internal fun reset() {
    executor.reset()
  }
}
