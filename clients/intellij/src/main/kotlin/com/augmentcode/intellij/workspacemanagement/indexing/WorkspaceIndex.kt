package com.augmentcode.intellij.workspacemanagement.indexing

import com.intellij.util.indexing.*
import com.intellij.util.io.EnumeratorStringDescriptor
import com.intellij.util.io.KeyDescriptor
import org.jetbrains.annotations.VisibleForTesting

class WorkspaceIndex : ScalarIndexExtension<String>() {
  override fun getVersion(): Int = 1

  override fun getName(): ID<String, Void> = WORKSPACE_INDEX_ID

  override fun getIndexer(): DataIndexer<String, Void, FileContent> = WorkspaceIndexer()

  // The indexer gets updates on files changes when dependsOnFileContent is true.
  override fun dependsOnFileContent(): Boolean = true

  // Let everything through and rely on the filtering process to handle what files we ignore.
  override fun getInputFilter(): FileBasedIndex.InputFilter {
    return FileBasedIndex.InputFilter { virtualFile ->
      // We only care about files that on disk (not jars, light virtual files, scratch files etc.)
      return@InputFilter virtualFile.isInLocalFileSystem
    }
  }

  // Use default key descriptor
  override fun getKeyDescriptor(): KeyDescriptor<String> = EnumeratorStringDescriptor.INSTANCE
}

@VisibleForTesting
internal val WORKSPACE_INDEX_ID = ID.create<String, Void>("AugmentWorkspaceIndex")
