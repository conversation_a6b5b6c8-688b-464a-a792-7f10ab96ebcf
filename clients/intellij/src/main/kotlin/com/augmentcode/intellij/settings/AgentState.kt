
package com.augmentcode.intellij.settings

import com.intellij.openapi.components.*

@Service(Service.Level.APP)
@State(name = "AgentState", storages = [Storage("AgentState.xml")])
class AgentState : SimplePersistentStateComponent<AgentStateValues>(AgentStateValues()) {
  companion object {
    val instance: AgentState
      get() = service()
  }

  var agentAutoModeApproved
    get() = state.agentAutoModeApproved
    set(value) {
      state.agentAutoModeApproved = value
    }
}

class AgentStateValues : BaseState() {
  var agentAutoModeApproved by property(false)
}
