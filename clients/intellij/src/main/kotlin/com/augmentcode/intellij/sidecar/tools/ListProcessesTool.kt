package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.sidecar.rpc.ListProcessesInputSchema
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.tools.ToolCallResponse
import com.google.protobuf.Struct
import com.intellij.execution.ExecutionManager
import com.intellij.openapi.project.Project

class ListProcessesTool : IdeTool {
  override val name = "list-processes"

  override val description = "List all known terminals and their states."

  override val inputMessageDescriptor = ListProcessesInputSchema.getDescriptor()

  override suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    val terminalHandlers =
      ExecutionManager.getInstance(project).getRunningProcesses()
        .filter { it.getUserData(AugmentTerminalInfo.TERMINAL_INFO_KEY) != null }
    if (terminalHandlers.isEmpty()) {
      return ToolCallResponse.newBuilder()
        .setText("No processes found")
        .build()
    }

    val lines =
      terminalHandlers.mapNotNull { handler ->
        val info = handler.getUserData(AugmentTerminalInfo.TERMINAL_INFO_KEY) ?: return@mapNotNull null
        var status = if (handler.isProcessTerminated) "killed" else "running"
        if (handler.exitCode != null) {
          status += " (return code: ${handler.exitCode})"
        }
        "Terminal ${info.terminalId}: ${info.command} - $status"
      }
    return ToolCallResponse.newBuilder()
      .setText("Here are all known processes:\n\n" + lines.joinToString("\n"))
      .build()
  }
}
