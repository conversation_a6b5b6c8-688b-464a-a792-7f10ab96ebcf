package com.augmentcode.intellij.utils

/**
 * Utility class for checking custom system properties defined in idea.properties.
 *
 * Use this for settings that should be hidden from users other than staging and AI tutors,
 * and shouldn't always be enabled for all users in those tenants.
 */
object CustomPropertyReader {
  const val CHAOS_MONKEY_EDT_ASSERTIONS = "augment.chaosMonkey.edtAssertions"
  const val EDT_FREEZE_DETECTOR_ENABLED_PROPERTY = "augmentcode.edtFreezeDetectorEnabled"
  const val LEGACY_INDEXING_DISABLED_PROPERTY = "augmentcode.indexing.legacy.disabled"
  const val ENABLE_EXCHANGE_STORAGE_PROPERTY = "augmentcode.storage.exchange.enabled"
  const val ENABLE_TOOL_USE_STATE_STORAGE_PROPERTY = "augmentcode.storage.tooluse.enabled"
  const val ENABLE_DEBUG_FEATURES = "augmentcode.debug.features.enabled"

  fun readProperty(key: String): String? {
    return System.getProperty(key)
  }

  fun readBoolean(key: String): Boolean {
    return System.getProperty(key)?.toBoolean() ?: false
  }
}
