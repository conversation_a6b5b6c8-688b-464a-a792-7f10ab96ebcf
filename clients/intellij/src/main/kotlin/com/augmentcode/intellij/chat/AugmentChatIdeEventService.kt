package com.augmentcode.intellij.chat

import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.rpc.*
import com.intellij.ide.DisplayChangeDetector
import com.intellij.ide.ui.UISettings
import com.intellij.ide.ui.UISettingsListener
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.readAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.BaseProjectDirectories.Companion.getBaseDirectories
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import kotlinx.coroutines.*
import org.jetbrains.annotations.VisibleForTesting

@Service(Service.Level.PROJECT)
class AugmentChatIdeEventService(
  private val project: Project,
  private val cs: CoroutineScope,
) : DumbService.DumbModeListener,
  UISettingsListener,
  DisplayChangeDetector.Listener,
  Disposable {
  private val messageBusConnection = project.messageBus.connect(this)

  companion object {
    fun getInstance(project: Project): AugmentChatIdeEventService = project.getService(AugmentChatIdeEventService::class.java)
  }

  init {
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      registerListeners()
    }
  }

  private fun createSyncStatus(status: SyncingStatus): SourceFoldersSyncStatus =
    SourceFoldersSyncStatus
      .newBuilder()
      .setData(
        SyncingStatusEvent
          .newBuilder()
          .setStatus(status)
          .build(),
      ).build()

  // registerListeners are initialized when the service is created and cleaned up when the service is disposed
  @VisibleForTesting
  internal fun registerListeners() {
    messageBusConnection.subscribe(
      DumbService.DUMB_MODE,
      this,
    )

    messageBusConnection.subscribe(
      UISettingsListener.TOPIC,
      this,
    )

    DisplayChangeDetector.getInstance().addListener(this)
  }

  suspend fun getSourceFoldersUpdated(): SourceFoldersUpdated {
    val sourceFoldersBuilder = SourceFoldersUpdatedData.newBuilder()
    readAction {
      // Use project.getBaseDirectories() instead of ProjectRootManager.getInstance(project).contentRoots
      // because getBaseDirectories gives us content roots that are not contained within other content roots.
      project.getBaseDirectories()
    }.map {
      sourceFoldersBuilder.addSourceFolders(
        ISourceFolderInfo
          .newBuilder()
          .setFolderRoot(it.path),
      )
    }
    return SourceFoldersUpdated
      .newBuilder()
      .setData(sourceFoldersBuilder.build())
      .build()
  }

  override fun exitDumbMode() {
    val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
    publisher.postMessageToWebview(
      createSyncStatus(SyncingStatus.done),
    )
    EditorTrackingService.getInstance(project).sendCurrentlyOpenedFiles()
  }

  override fun enteredDumbMode() {
    val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
    publisher.postMessageToWebview(
      createSyncStatus(SyncingStatus.running),
    )
  }

  override fun uiSettingsChanged(uiSettings: UISettings) {
    val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
    publisher.reloadIntellijStyles()
  }

  override fun displayChanged() {
    // displayChanged will sometimes be run on the AppKit thread in macOS.
    // Errors on the AppKit thread can crash the IDE so we need invokeLater.
    invokeLater {
      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
      publisher.reloadIntellijStyles()
    }
  }

  override fun dispose() {
    // Other listeners are automatically disposed by the project since we used messageBus.connect(this),
    // but we need to manually remove the DisplayChangeDetector listener.
    DisplayChangeDetector.getInstance().removeListener(this)
  }
}
