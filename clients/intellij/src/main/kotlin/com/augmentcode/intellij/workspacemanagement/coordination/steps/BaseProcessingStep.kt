package com.augmentcode.intellij.workspacemanagement.coordination.steps

import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.Job
import org.jetbrains.annotations.VisibleForTesting

abstract class BaseProcessingStep(private val stepName: String) : Disposable {
  @VisibleForTesting
  internal var processingJob: Job? = null

  override fun dispose() {
    stopProcessing()
  }

  abstract fun createProcessingJob(): Job

  fun startProcessing() {
    if (processingJob != null) {
      thisLogger().info("Processing step '$stepName' already started")
      return
    }

    processingJob = createProcessingJob()
  }

  fun stopProcessing() {
    thisLogger().info("Stopping '$stepName'")
    processingJob?.cancel()
    processingJob = null
  }
}
