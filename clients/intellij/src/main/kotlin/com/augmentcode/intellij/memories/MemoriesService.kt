package com.augmentcode.intellij.memories

import com.augmentcode.intellij.sidecar.clientinterfaces.PluginFileStore
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.event.DocumentListener
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileTypes.PlainTextFileType
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.findDocument
import com.intellij.testFramework.LightVirtualFile
import java.nio.charset.StandardCharsets

/**
 * Service for managing project-specific memories.
 * Memories persist across IDE restarts and upgrades.
 */
@Service(Service.Level.PROJECT)
class MemoriesService(private val project: Project) : Disposable {
  companion object {
    fun getInstance(project: Project): MemoriesService {
      return project.service<MemoriesService>()
    }

    private val logger = thisLogger()

    // Path in plugins dir for storing memories, and the path displayed to the user in the temporary file.
    private const val MEMORIES_ASSET_PATH = "augment-memories.md"
    private const val DEFAULT_MEMORIES = ""
  }

  private val fileStore = PluginFileStore(project)

  /**
   * We use a temporary file to open in the editor because the actual memories file exists outside the project,
   * so opening it directly in the editor causes a pop-up asking the user for permission to edit
   * external files. Using a temporary file avoids this.
   */
  private val memoriesTempFile: VirtualFile = LightVirtualFile(MEMORIES_ASSET_PATH, PlainTextFileType.INSTANCE, DEFAULT_MEMORIES)

  init {
    // Sync in-memory content with file on disk
    val contentBytes = fileStore.loadAsset(MEMORIES_ASSET_PATH)
    if (contentBytes != null) {
      String(contentBytes, StandardCharsets.UTF_8)
    } else {
      // save memories to disk if no memories file exists yet
      fileStore.saveAsset(MEMORIES_ASSET_PATH, DEFAULT_MEMORIES.toByteArray(StandardCharsets.UTF_8))
    }
    refreshFromFileStore()

    val document =
      runReadAction {
        memoriesTempFile.findDocument()
      }
    if (document == null) {
      logger.error("Failed to find document for memories temp file")
    } else {
      // If the user types in the editor, persist it
      document.addDocumentListener(
        object : DocumentListener {
          override fun documentChanged(event: DocumentEvent) {
            // Persist the updated content if the content has changed
            if (event.oldLength != event.newLength || event.oldFragment != event.newFragment) {
              fileStore.saveAsset(MEMORIES_ASSET_PATH, document.text.toByteArray(StandardCharsets.UTF_8))
            }
          }
        },
        this, // Dispose automatically when the service is disposed
      )
    }
  }

  override fun dispose() {
    // No need to manually dispose of listeners as they will be automatically disposed
    // when the service is disposed.
  }

  /**
   * Though we don't want to open this file directly in the editor, the agent checkpointer
   * needs the absolute path to the file in order to update it.
   */
  fun getMemoriesAbsPath(): String {
    return fileStore.getAssetAbsPath(MEMORIES_ASSET_PATH)
  }

  /**
   * Opens the memories in the editor by creating a temporary file.
   * We use a temporary file because the actual memories file exists outside the project,
   * so opening it directly in the editor causes a pop-up asking the user for permission to edit
   * external files. Using a temporary file avoids this.
   */
  fun openMemories() {
    logger.info("Opening memories")

    // Refresh content in temp file in case it's not in sync with the underlying file
    refreshFromFileStore()

    // Open in editor
    invokeLater {
      FileEditorManager.getInstance(project).openFile(memoriesTempFile, true)
    }
  }

  fun getMemories(): String {
    val bytes = fileStore.loadAsset(MEMORIES_ASSET_PATH)
    return if (bytes != null) {
      val content = String(bytes, StandardCharsets.UTF_8)
      logger.info("Loaded memories: $content")
      content
    } else {
      logger.info("No memories found, creating and returning default")
      DEFAULT_MEMORIES
    }
  }

  fun updateMemories(content: String) {
    logger.info("Saving memories: $content")
    fileStore.saveAsset(MEMORIES_ASSET_PATH, content.toByteArray(StandardCharsets.UTF_8))
    refreshFromFileStore()
  }

  /**
   * Refreshes the in-memory content from the file store
   */
  private fun refreshFromFileStore() {
    val contentBytes = fileStore.loadAsset(MEMORIES_ASSET_PATH)
    val content =
      if (contentBytes != null) {
        String(contentBytes, StandardCharsets.UTF_8)
      } else {
        // File should have been created on init. Something went wrong
        logger.error("Failed to load memories from file store. Attempting to recreate...")
        fileStore.saveAsset(MEMORIES_ASSET_PATH, DEFAULT_MEMORIES.toByteArray(StandardCharsets.UTF_8))
        // See if it was created
        val newlyCreatedBytes = fileStore.loadAsset(MEMORIES_ASSET_PATH)
        if (newlyCreatedBytes != null) {
          String(newlyCreatedBytes, StandardCharsets.UTF_8)
        } else {
          logger.error("Failed to recreate memories file. Giving up.")
          return
        }
      }

    // Update the virtual file
    memoriesTempFile.setBinaryContent(content.toByteArray(StandardCharsets.UTF_8))

    // Update the document if it exists
    val document = runReadAction { memoriesTempFile.findDocument() }
    if (document != null) {
      runWriteAction {
        document.setText(content)
      }
    }
  }

  /**
   * Helper method to run write actions safely
   */
  private fun runWriteAction(action: () -> Unit) {
    val application = ApplicationManager.getApplication()
    if (application.isDispatchThread) {
      application.runWriteAction(action)
    } else {
      application.invokeLater {
        application.runWriteAction(action)
      }
    }
  }
}
