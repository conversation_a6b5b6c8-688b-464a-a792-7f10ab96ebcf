package com.augmentcode.intellij.webviews.settings

import com.augmentcode.api.RemoteToolId
import com.augmentcode.api.RevokeToolAccessStatus
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.featureflags.DebugFeatures
import com.augmentcode.intellij.guidelines.GuidelinesService
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.settings.AugmentIntegrationsConfig
import com.augmentcode.intellij.settings.AugmentSettingsWebviewEditorVirtualFile
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.utils.ProperThreadUsageChecker
import com.augmentcode.intellij.utils.TextRangeCalculator
import com.augmentcode.intellij.webviews.ConfirmationModal
import com.augmentcode.rpc.*
import com.google.protobuf.Empty
import com.intellij.ide.BrowserUtil
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.EDT
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileChooser.FileChooser
import com.intellij.openapi.fileChooser.FileChooserDescriptorFactory
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.annotations.VisibleForTesting

/**
 * Service that handles settings webview messages
 */
@Service(Service.Level.PROJECT)
class AugmentSettingsWebviewService(
  private val project: Project,
  private val cs: CoroutineScope,
) : WebviewSettingsServiceGrpcKt.WebviewSettingsServiceCoroutineImplBase() {
  private val logger = logger<AugmentSettingsWebviewService>()

  private var settingsVirtualFile: AugmentSettingsWebviewEditorVirtualFile? = null

  companion object {
    fun getInstance(project: Project): AugmentSettingsWebviewService = project.service()
  }

  override suspend fun toolConfigLoaded(request: ToolConfigLoadedRequest): ToolConfigInitializeResponse {
    logger.info("Settings webview loaded")

    // Get the guidelines states from the GuidelinesService
    val guidelinesService = GuidelinesService.getInstance(project)
    val guidelinesStates = guidelinesService.getGuidelinesStates()

    // Build and return the response with guidelines included
    return ToolConfigInitializeResponse.newBuilder()
      .setData(
        SettingsInitializeResponseData.newBuilder()
          .addAllToolConfigs(emptyList<ToolSettings>())
          .addAllHostTools(SidecarService.getInstance(project).getToolStatusForSettingsPanel().toolsList ?: emptyList())
          .setEnableDebugFeatures(DebugFeatures.enabled())
          .setEnableAgentMode(AugmentAppStateService.instance.context.flags.agentModeEnabled)
          .setSettingsComponentSupported(
            SettingsComponentSupported.newBuilder()
              .setWorkspaceContext(false)
              .setMcpServerList(true)
              .setMcpServerImport(true)
              .setOrientation(false)
              .setRemoteTools(true)
              .setUserGuidelines(true)
              .setTerminal(false)
              .setRules(AugmentAppStateService.instance.context.flags.enableRules)
              .build(),
          )
          .setGuidelines(guidelinesStates)
          .setEnableInitialOrientation(false) // not yet supported
          .build(),
      )
      .build()
  }

  override suspend fun toolConfigGetDefinitions(request: ToolConfigGetDefinitionsRequest): ToolConfigDefinitionsResponse {
    logger.info("Tool config definitions requested")

    // For now, return empty data - this will be expanded as we implement tool configuration
    return ToolConfigDefinitionsResponse.newBuilder()
      .setData(
        ToolConfigDefinitionsResponseData.newBuilder()
          .addAllHostTools(
            project.serviceOrNull<SidecarService>()?.getToolStatusForSettingsPanel(request.data.useCache)?.toolsList ?: emptyList(),
          )
          .build(),
      )
      .build()
  }

  override suspend fun toolConfigSave(request: ToolConfigSaveRequest): Empty {
    logger.info("Tool config save requested for tool: ${request.data.toolName}")

    // TODO: Implement MCP tool config saving

    return Empty.getDefaultInstance()
  }

  /**
   * Open the URL in the browser for the user to authenticate.
   * This is similar to the VS Code implementation in settings-panel.ts.
   */
  override suspend fun toolConfigStartOAuth(request: ToolConfigStartOAuthRequest): ToolConfigStartOAuthResponse {
    logger.info("Tool config OAuth requested with URL: ${request.data.authUrl}")

    var success = false
    try {
      val url = request.data.authUrl
      // Use withContext to ensure we're on the UI thread when opening the browser
      withContext(Dispatchers.Main) {
        BrowserUtil.browse(url)
      }
      success = true
    } catch (e: Exception) {
      logger.error("Error opening URL: ${e.message}", e)
    }

    return ToolConfigStartOAuthResponse.newBuilder()
      .setData(
        ToolConfigStartOAuthResponseData.newBuilder()
          .setOk(success)
          .build(),
      )
      .build()
  }

  /**
   * Revoke access for a tool.
   * This is similar to the VS Code implementation in settings-panel.ts.
   */
  override suspend fun toolConfigRevokeAccess(request: ToolConfigRevokeAccessRequest): ToolConfigDefinitionsResponse {
    logger.info("Tool config revoke access requested for tool: ${request.data.toolId}")

    try {
      // Get the tool definitions to find the tool by its RemoteToolId
      val hostTools = project.serviceOrNull<SidecarService>()?.getToolStatusForSettingsPanel()?.toolsList ?: emptyList()
      val tool =
        hostTools.find { t ->
          t.identifier.hostName == request.data.toolId.hostName &&
            t.identifier.toolId == request.data.toolId.toolId
        }

      // TODO use constant or enum or something for remoteToolHost
      if (tool != null && tool.identifier.hostName == "remoteToolHost") {
        val toolId = tool.identifier.toolId
        logger.info("Revoking access for remote tool: ${tool.definition.name} ($toolId)")
        val apiToolId = RemoteToolId.values().find { it.value == toolId.toInt() } ?: RemoteToolId.Unknown
        val result = AugmentAPI.instance.revokeToolAccess(apiToolId)
        logger.info("Revoke access result: ${result.status}")
        when (result.status) {
          RevokeToolAccessStatus.Success.value -> {
            logger.info("Successfully revoked access for ${tool.definition.name} ($toolId).")
          }
          RevokeToolAccessStatus.NotActive.value -> {
            logger.info("Tool ${tool.definition.name} ($toolId) has no access to revoke.")
          }
          RevokeToolAccessStatus.Unimplemented.value -> {
            logger.warn("Revoking access is not implemented for ${tool.definition.name} ($toolId).")
            showNotification("Not Implemented", "Revoking access is not implemented for ${tool.definition.name}.", NotificationType.WARNING)
          }
          RevokeToolAccessStatus.NotFound.value -> {
            logger.warn("Tool not found: ${tool.definition.name} ($toolId).")
            showNotification("Tool Not Found", "The tool ${tool.definition.name} was not found.", NotificationType.ERROR)
          }
          RevokeToolAccessStatus.Failed.value -> {
            logger.warn("Failed to revoke access for ${tool.definition.name} ($toolId).")
            showNotification("Failed to Revoke Access", "Failed to revoke access for ${tool.definition.name}.", NotificationType.ERROR)
          }
          else -> {
            logger.warn("Unknown status (${result.status}) when revoking access for ${tool.definition.name} ($toolId).")
            showNotification(
              "Unknown Status",
              "Unknown status (${result.status}) when revoking access for ${tool.definition.name}.",
              NotificationType.ERROR,
            )
          }
        }
      } else {
        logger.warn("Tool not found: ${request.data.toolId.hostName} ${request.data.toolId.toolId}")
        showNotification("Tool Not Found", "The tool ${tool?.definition?.name} was not found.", NotificationType.ERROR)
      }
    } catch (e: Exception) {
      logger.error("Error revoking access: ${e.message}", e)
    }

    return this.toolConfigGetDefinitions(
      ToolConfigGetDefinitionsRequest.newBuilder().setData(
        ToolConfigGetDefinitionsRequestData.newBuilder().setUseCache(false).build(),
      ).build(),
    )
  }

  override suspend fun getStoredMCPServers(request: GetStoredMCPServersRequest): GetStoredMCPServersResponse {
    // Get the base MCP servers from storage
    val baseServers = AugmentIntegrationsConfig.instance.mcpServers

    // Create a mapping of server names to their tools
    val serverToolMapping = mutableMapOf<String, MutableSet<com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings>>()

    // Get tool definitions from the sidecar
    val sidecarService = project.serviceOrNull<SidecarService>()
    if (sidecarService != null) {
      try {
        val toolDefinitions = sidecarService.getToolStatusForSettingsPanel()
        logger.debug("Retrieved ${toolDefinitions.toolsList.size} tool definitions from sidecar")

        for (toolDefinition in toolDefinitions.toolsList) {
          val mcpServerName = toolDefinition.definition.originalMcpServerName
          val mcpToolName = toolDefinition.definition.mcpToolName
          val toolName = toolDefinition.definition.name

          logger.debug("Tool definition - name:'$toolName', mcpToolName:'$mcpToolName', mcpServerName:'$mcpServerName'")

          if (mcpServerName.isNotEmpty() && mcpToolName.isNotEmpty()) {
            if (!serverToolMapping.containsKey(mcpServerName)) {
              serverToolMapping[mcpServerName] = mutableSetOf()
            }
            serverToolMapping[mcpServerName]?.add(toolDefinition)
            logger.debug("Added tool '$mcpToolName' to mapping for server '$mcpServerName'")
          }
        }
        logger.debug("Final server tool mapping has ${serverToolMapping.size} servers: ${serverToolMapping.keys}")
      } catch (e: Exception) {
        logger.warn("Failed to get tool status for settings panel: ${e.message}")
      }
    } else {
      logger.warn("SidecarService is null")
    }

    // Build the response with tools included
    logger.debug("Processing ${baseServers.dataList.size} MCP servers")
    val serversWithTools =
      baseServers.dataList.map { server ->
        // Determine the server name using the same logic as the sidecar
        val tools = serverToolMapping[server.name]?.toList() ?: emptyList()
        if (tools.isNotEmpty()) {
          logger.debug("Tools for ${server.name}: ${tools.map { it.definition.mcpToolName }}")
        }

        server.toBuilder()
          .addAllTools(tools)
          .build()
      }

    return GetStoredMCPServersResponse.newBuilder()
      .addAllData(serversWithTools)
      .build()
  }

  override suspend fun setStoredMCPServers(request: SetStoredMCPServersRequest): Empty {
    AugmentIntegrationsConfig.instance.mcpServers = GetStoredMCPServersResponse.newBuilder().addAllData(request.dataList).build()

    // Convert to the format expected by the sidecar
    project.serviceOrNull<SidecarService>()?.setMcpServers(AugmentIntegrationsConfig.instance.mcpServersForSidecar)

    return Empty.getDefaultInstance()
  }

  override suspend fun executeInitialOrientation(request: ExecuteInitialOrientationRequest): Empty {
    logger.info("Execute initial orientation requested")

    // TODO: Implement initial orientation execution

    return Empty.getDefaultInstance()
  }

  override suspend fun updateUserGuidelines(request: UpdateUserGuidelinesRequest): Empty {
    logger.info("Update user guidelines requested")

    val guidelinesService = GuidelinesService.getInstance(project)
    guidelinesService.updateUserGuidelines(
      request.data,
    )
    return Empty.getDefaultInstance()
  }

  /**
   * Handle sign out request from the settings webview.
   * This is similar to the VS Code implementation in settings-panel.ts.
   */
  override suspend fun signOut(request: SignOutRequest): Empty {
    logger.info("Sign out requested from settings webview")

    // Clear the OAuth state (sign out)
    AugmentOAuthState.instance.clear()
    closeSettingsFile()
    return Empty.getDefaultInstance()
  }

  /**
   * Handle opening a confirmation modal for settings.
   * This is similar to the VS Code implementation in settings-panel.ts.
   */
  override suspend fun openConfirmationModal(request: OpenConfirmationModal): ConfirmationModalResponse {
    logger.info("Settings confirmation modal requested: ${request.data.title}")

    return try {
      // Check if we're in a test environment
      if (isTestEnvironment()) {
        logger.warn("Test environment detected: Returning false for confirmation modal")
        return ConfirmationModalResponse.newBuilder()
          .setData(
            ConfirmationModalResponseData.newBuilder()
              .setOk(false)
              .build(),
          )
          .build()
      }

      val result = ConfirmationModal.showAndGetAsync(project, request.data)
      ConfirmationModalResponse.newBuilder()
        .setData(
          ConfirmationModalResponseData.newBuilder()
            .setOk(result)
            .build(),
        )
        .build()
    } catch (e: Exception) {
      logger.warn("Error showing settings confirmation modal: ${e.message}", e)
      ConfirmationModalResponse.newBuilder()
        .setData(
          ConfirmationModalResponseData.newBuilder()
            .setOk(false)
            .setError(e.message ?: "Unknown error")
            .build(),
        )
        .build()
    }
  }

  /**
   * Handle a request to trigger a file picker to import files/directory into .augment/rules
   */
  override suspend fun handleTriggerImportDialog(request: HandleTriggerImportDialogRequest): HandleTriggerImportDialogResponse {
    // Check if we're in a test environment
    if (isTestEnvironment()) {
      logger.warn("Test environment detected: Returning empty selection for import dialog")
      return HandleTriggerImportDialogResponse.newBuilder()
        .setData(
          HandleTriggerImportDialogResponseData.newBuilder()
            .addAllSelectedPaths(emptyList())
            .build(),
        )
        .build()
    }

    logger.info("Trigger import dialog requested")

    try {
      // Get the project root path
      val projectRootPath = project.basePath
      if (projectRootPath == null) {
        logger.warn("No project root found for import dialog.")
        showNotification("No Project", "No project is currently open to import into.", NotificationType.WARNING)
        return HandleTriggerImportDialogResponse.newBuilder()
          .setData(
            HandleTriggerImportDialogResponseData.newBuilder()
              .addAllSelectedPaths(emptyList())
              .build(),
          )
          .build()
      }

      // Create file chooser descriptor that allows both files and folders
      val descriptor =
        FileChooserDescriptorFactory.createMultipleFilesNoJarsDescriptor().apply {
          title = "Select files or a folder to import as rules"
          description = "Choose markdown files (.md, .mdc) or folders containing rules to import"
          withShowHiddenFiles(false)

          // Set file filter to prefer markdown files
          withFileFilter { file ->
            if (file.isDirectory) {
              true // Allow all directories
            } else {
              val name = file.name.lowercase()
              name.endsWith(".md") || name.endsWith(".mdc")
            }
          }
        }

      // Show the file chooser dialog and wait for user selection
      val deferred = CompletableDeferred<Array<VirtualFile>?>()
      withContext(Dispatchers.EDT) {
        FileChooser.chooseFiles(descriptor, project, null) { files ->
          deferred.complete(files.toTypedArray())
        }
      }
      val selectedFiles = deferred.await()

      if (selectedFiles.isNullOrEmpty()) {
        logger.debug("Import dialog cancelled by user.")
        return HandleTriggerImportDialogResponse.newBuilder()
          .setData(
            HandleTriggerImportDialogResponseData.newBuilder()
              .addAllSelectedPaths(emptyList())
              .build(),
          )
          .build()
      }

      // Convert selected files to relative paths
      val selectedPaths = mutableListOf<String>()

      for (file in selectedFiles) {
        if (file.path.startsWith(projectRootPath)) {
          // File is within project, use relative path
          val relativePath = file.path.removePrefix(projectRootPath).removePrefix("/")
          selectedPaths.add(relativePath)
        } else {
          // File is outside project, use absolute path
          selectedPaths.add(file.path)
        }
      }

      if (selectedPaths.isEmpty()) {
        logger.warn("No valid paths selected within workspace.")
        showNotification("No Valid Selection", "No valid paths selected within workspace.", NotificationType.WARNING)
      }

      logger.debug("Returning ${selectedPaths.size} selected paths for import")
      return HandleTriggerImportDialogResponse.newBuilder()
        .setData(
          HandleTriggerImportDialogResponseData.newBuilder()
            .addAllSelectedPaths(selectedPaths)
            .build(),
        )
        .build()
    } catch (error: Exception) {
      val errorMsg = error.message ?: "Unknown error"
      logger.warn("Error in import dialog: $errorMsg", error)
      showNotification("Import Error", "An error occurred during import: $errorMsg", NotificationType.ERROR)
      return HandleTriggerImportDialogResponse.newBuilder()
        .setData(
          HandleTriggerImportDialogResponseData.newBuilder()
            .addAllSelectedPaths(emptyList())
            .build(),
        )
        .build()
    }
  }

  @VisibleForTesting
  fun closeSettingsFile() {
    val editorManager = FileEditorManager.getInstance(project)

    if (settingsVirtualFile == null) {
      /**
       * Some reason settingsVirtualFile is null, but the file is still open.  So
       * this will look to see if its open and close it.
       */
      editorManager.openFiles.forEach { file ->
        if (file.name == AugmentSettingsWebviewEditorVirtualFile.SETTINGS_VIRTUAL_FILE_NAME) {
          invokeLater {
            editorManager.closeFile(file)
          }
        }
      }
      return
    }
    // snapshot the file reference so we don't have a race condition in the invokeLater
    val fileToClose = settingsVirtualFile
    invokeLater {
      editorManager.closeFile(fileToClose!!)
    }
    settingsVirtualFile = null
  }

  /**
   * Creates the settings webview if it does not exist and opens the virtual file in the editor.
   * If a section is provided, navigates to that section in the settings.
   *
   * @param section Optional section to navigate to in the settings
   */
  fun openSettingsWebview(section: String? = null) {
    ProperThreadUsageChecker.shouldNotBeOnEDT("openSettingsWebview")
    val editorManager = FileEditorManager.getInstance(project)

    // Initialize the messaging service to handle WebViewMessageType.signOut messages

    if (settingsVirtualFile == null) {
      // Create the file with a handler to navigate to the specified section if provided
      settingsVirtualFile = AugmentSettingsWebviewEditorVirtualFile(project, cs)

      Disposer.register(AugmentDisposable.getInstance(project)) {
        closeSettingsFile()
      }
    }

    // Before webview is loaded via editorManager.openFile(), set the section to navigate to
    SettingsMessagingService.getInstance(project).navigateToSection(section)

    invokeLater {
      editorManager.openFile(settingsVirtualFile!!, true, true)
    }
  }

  /**
   * Handle opening a file from the webview.
   * This is similar to the VSCode implementation in open-file.ts.
   */
  override suspend fun openFile(request: OpenFileRequest): Empty {
    logger.info("Open file requested: ${request.data.pathName}")

    try {
      val fileDetails = request.data
      val fullPath =
        if (fileDetails.pathName.startsWith("/")) {
          fileDetails.pathName
        } else {
          "${fileDetails.repoRoot}/${fileDetails.pathName}"
        }

      val virtualFile = VirtualFileManager.getInstance().findFileByUrl("file://$fullPath")
      if (virtualFile == null) {
        logger.warn("File not found: $fullPath")
        showNotification("File Not Found", "The file $fullPath was not found.", NotificationType.ERROR)
        return Empty.getDefaultInstance()
      }

      val range =
        runReadAction {
          val document = FileDocumentManager.getInstance().getDocument(virtualFile) ?: return@runReadAction null
          TextRangeCalculator.calculateRange(document, request, project)
        }

      invokeLater {
        val editorManager = FileEditorManager.getInstance(project)
        editorManager.openFile(virtualFile, true)

        val editor = editorManager.selectedTextEditor
        // Handle ranges or snippets from the request, and select them in the editor
        if (editor != null && range != null) {
          editor.selectionModel.setSelection(range.startOffset, range.endOffset)

          // Scroll to the selection
          editor.caretModel.moveToOffset(range.startOffset)
          editor.scrollingModel.scrollToCaret(ScrollType.CENTER)
        }
      }
    } catch (e: Exception) {
      logger.warn("Error opening file: ${e.message}", e)
      showNotification("Error Opening File", "Failed to open file: ${e.message}", NotificationType.ERROR)
    }

    return Empty.getDefaultInstance()
  }

  /**
   * Handle showing a notification from the webview.
   * This is similar to the VSCode implementation in settings-panel.ts.
   */
  override suspend fun showNotification(request: ShowNotificationRequest): Empty {
    logger.info("Show notification requested: ${request.data.message}")

    val notificationType =
      when (request.data.type) {
        "error" -> NotificationType.ERROR
        "warning" -> NotificationType.WARNING
        "info", null -> NotificationType.INFORMATION
        else -> NotificationType.INFORMATION
      }

    showNotification("Augment", request.data.message, notificationType)

    return Empty.getDefaultInstance()
  }

  private fun showNotification(
    title: String,
    content: String,
    notificationType: NotificationType,
  ) {
    com.intellij.openapi.application.invokeLater {
      Notifications.Bus.notify(
        Notification(
          "augment.notifications",
          title,
          content,
          notificationType,
        ),
        project,
      )
    }
  }

  /**
   * Check if we're running in a test environment.
   * This helps avoid blocking UI operations during tests.
   */
  private fun isTestEnvironment(): Boolean {
    return ApplicationManager.getApplication().isUnitTestMode
  }
}
