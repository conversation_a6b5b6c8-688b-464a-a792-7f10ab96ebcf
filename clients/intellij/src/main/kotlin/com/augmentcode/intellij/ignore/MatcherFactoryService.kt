package com.augmentcode.intellij.ignore

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.Pattern

/**
 * Service for creating and caching regex matchers.
 */
@Service(Service.Level.APP)
class MatcherFactoryService {
  private val matcherCache = ConcurrentHashMap<String, ConcurrentMatcher>()

  companion object {
    fun getInstance(): MatcherFactoryService = service()
  }

  /**
   * Match a string pattern against a filename.
   */
  suspend fun match(
    pattern: String,
    filename: String,
  ): Boolean {
    val concurrentMatcher =
      matcherCache.computeIfAbsent(pattern) {
        ConcurrentMatcher(pattern)
      }

    return concurrentMatcher.match(filename)
  }

  private class ConcurrentMatcher(
    filePattern: String,
  ) {
    private val matcher = Pattern.compile(filePattern).matcher("")
    private val mutex = Mutex()

    suspend fun match(filename: String): Boolean {
      // Use coroutine mutex for thread-safe access
      return mutex.withLock {
        matcher.reset(filename)
        matcher.find()
      }
    }
  }
}
