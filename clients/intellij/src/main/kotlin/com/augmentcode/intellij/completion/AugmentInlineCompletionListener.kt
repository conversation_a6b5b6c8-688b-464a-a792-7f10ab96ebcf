@file:Suppress("UnstableApiUsage")

package com.augmentcode.intellij.completion

import com.augmentcode.api.ResolveCompletionsRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.codeInsight.inline.completion.InlineCompletionEventAdapter
import com.intellij.codeInsight.inline.completion.InlineCompletionEventType
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.InlineCompletionInstallListener
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.util.removeUserData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class AugmentInlineCompletionListener(private val cs: CoroutineScope) : InlineCompletionInstallListener {
  override fun handlerInstalled(
    editor: Editor,
    handler: Inline<PERSON><PERSON>pletion<PERSON><PERSON><PERSON>,
  ) {
    super.handlerInstalled(editor, handler)
    handler.addEventListener(AugmentInlineCompletionEventListener(editor, cs))
  }
}

class AugmentInlineCompletionEventListener(private val editor: Editor, private val cs: CoroutineScope) : InlineCompletionEventAdapter {
  override fun onRequest(event: InlineCompletionEventType.Request) {
    super.onRequest(event)
    editor.removeUserData(AugmentLastCompletionElementKey)
  }

  override fun onHide(event: InlineCompletionEventType.Hide) {
    super.onHide(event)
    // check string value to not be marked as "Internal API user"
    reportCompletion(event.finishType.name == "SELECTED")
  }

  private fun reportCompletion(accepted: Boolean) {
    val lastCompletionElement = editor.removeUserData(AugmentLastCompletionElementKey) ?: return
    val request =
      ResolveCompletionsRequest().apply {
        clientName = AugmentAPI.CLIENT_NAME
        resolutions = listOf(lastCompletionElement.generateCompletionResolution(accepted))
      }
    cs.launch {
      // report completion rejection to server in a non-blocking way
      AugmentAPI.instance.resolveCompletions(request)
    }
  }
}
