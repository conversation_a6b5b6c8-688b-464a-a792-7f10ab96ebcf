package com.augmentcode.intellij.sidecar.services

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.sidecar.rpc.clientInterfaces.ShowDiffViewRequest
import com.google.protobuf.Empty
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.chains.DiffRequestProducer
import com.intellij.diff.impl.DiffEditorViewer
import com.intellij.diff.requests.ContentDiffRequest
import com.intellij.diff.requests.DiffRequest
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.util.DiffUserDataKeysEx
import com.intellij.openapi.ListSelection
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.UserDataHolder
import com.intellij.openapi.vcs.changes.Change
import com.intellij.openapi.vcs.changes.ChangeListManager
import com.intellij.openapi.vcs.changes.ChangeViewDiffRequestProcessor
import com.intellij.openapi.vcs.changes.ChangesViewManager
import com.intellij.openapi.vcs.changes.EditorTabDiffPreview
import com.intellij.openapi.vcs.changes.actions.diff.ChangeDiffRequestProducer
import com.intellij.openapi.vcs.changes.actions.diff.lst.LocalChangeListDiffRequest
import com.intellij.openapi.vcs.changes.actions.diff.lst.LocalChangeListDiffTool.ALLOW_EXCLUDE_FROM_COMMIT
import com.intellij.openapi.vcs.impl.PartialChangesUtil
import com.intellij.openapi.vfs.VirtualFileManager
import java.util.stream.Stream

/**
 * Service responsible for handling diff viewing requests from the sidecar in IntelliJ.
 * This service provides methods to show diffs using various approaches:
 * - Static diff views using DiffManager
 * - Standalone commit diff previews using custom EditorTabDiffPreview
 * - Integration with VCS changes and tool windows
 */
@Service(Service.Level.PROJECT)
class DiffViewerService(private val project: Project) {
  private val logger = Logger.getInstance(DiffViewerService::class.java)

  companion object {
    fun getInstance(project: Project): DiffViewerService {
      return project.service<DiffViewerService>()
    }
  }

  /**
   * Checks if two file contents match, ignoring BOM (Byte Order Mark) characters.
   *
   * @param content1 First file content to compare
   * @param content2 Second file content to compare
   * @return true if the contents match after removing BOMs, false otherwise
   */
  private fun checkFilesMatchIgnoringBom(
    content1: String,
    content2: String,
  ): Boolean {
    val content1NoBom = content1.removePrefix("\uFEFF")
    val content2NoBom = content2.removePrefix("\uFEFF")
    return content1NoBom.length == content2NoBom.length && content1NoBom == content2NoBom
  }

  /**
   * Main entry point for showing diff views based on a ShowDiffViewRequest.
   * This method handles the complete flow of determining the best diff approach
   * and falling back to alternatives when needed.
   */
  fun showDiffView(request: ShowDiffViewRequest): Empty {
    logger.info("Showing Diff View Request: $request")

    val virtualFile = AugmentRoot.findFile(request.path.rootPath, request.path.relPath)
    if (virtualFile == null) {
      logger.warn("Could not find virtual file for path: ${request.path}")
      // Use static diff preview when file doesn't exist
      val filePath = "${request.path.rootPath}/${request.path.relPath}"
      invokeLater {
        openStaticFileDiff(filePath, request.original, request.modified)
      }
      return Empty.getDefaultInstance()
    }

    // Get current file contents
    val currentContent =
      runReadAction {
        FileDocumentManager.getInstance().getDocument(virtualFile)?.text
          ?: virtualFile.contentsToByteArray().toString(Charsets.UTF_8)
      }

    // Check if current file content matches the modified content (ignoring BOMs)
    if (!checkFilesMatchIgnoringBom(currentContent, request.modified)) {
      logger.info("Current file content doesn't match request.modified, using static diff preview")
      val filePath = "${request.path.rootPath}/${request.path.relPath}"
      openStaticFileDiff(filePath, request.original, request.modified)
      return Empty.getDefaultInstance()
    }

    val change = ChangeListManager.getInstance(project).getChange(virtualFile)
    if (change == null) {
      logger.warn("No VCS change found for file: ${virtualFile.path}")
      // Fallback to static diff preview
      val filePath = "${request.path.rootPath}/${request.path.relPath}"
      openStaticFileDiff(filePath, request.original, request.modified)
      return Empty.getDefaultInstance()
    }

    // Conditions are valid for opening the built-in commit diff preview which has a nicer UI, so try that.
    invokeLater {
      // Select the file in the Changes view as a little quality of life thing so that if user is in "Commit" tool
      // panel, focus matches selected file.
      val changesViewEx = ChangesViewManager.getInstanceEx(project)
      changesViewEx.selectChanges(listOf(change))
    }
    try {
      openStandaloneCommitDiffPreview(change)
    } catch (e: Exception) {
      logger.warn("Built-in commit diff preview failed, falling back to static diff", e)
      val filePath = "${request.path.rootPath}/${request.path.relPath}"
      openStaticFileDiff(filePath, request.original, request.modified)
    }

    return Empty.getDefaultInstance()
  }

  /**
   * Creates a side-by-side diff view using IntelliJ's built-in diff manager comparing two file contents.
   *
   * @param filePath The absolute path to the file being compared (used for file type detection and naming).
   *    The file does *not* necessarily need to exist on disk (e.g.: if the user is now on a branch where
   *    the file does not exist).
   * @param originalContent The original file content as a string
   * @param modifiedContent The modified file content as a string
   */
  fun openStaticFileDiff(
    filePath: String,
    originalContent: String,
    modifiedContent: String,
  ) {
    val diffFactory = DiffContentFactory.getInstance()
    val fileType = FileTypeManager.getInstance().getFileTypeByFileName(filePath)
    val fileName = filePath.substringAfterLast('/')

    // Try to find the actual file for navigation (this will enable the "Jump to Source" button).
    val virtualFile = VirtualFileManager.getInstance().findFileByUrl("file://$filePath")

    val originalDiffContent =
      if (virtualFile != null) {
        diffFactory.create(project, originalContent, virtualFile)
      } else {
        diffFactory.create(project, originalContent, fileType)
      }

    val modifiedDiffContent =
      if (virtualFile != null) {
        diffFactory.create(project, modifiedContent, virtualFile)
      } else {
        diffFactory.create(project, modifiedContent, fileType)
      }

    val diffRequest =
      SimpleDiffRequest(
        "Review Code Changes",
        originalDiffContent,
        modifiedDiffContent,
        "$fileName (Original)",
        "$fileName (Augmented)",
      )

    val diffManager = DiffManager.getInstance()
    invokeLater {
      diffManager.showDiff(project, diffRequest)
    }
  }

  /**
   * Opens a diff view for the given change in a special diff viewer that will compare against
   * last change with local. This creates a standalone EditorTabDiffPreview that doesn't
   * require the Commit tool window to be opened.
   *
   * This method now supports chunk staging (checkboxes for individual changes) when the
   * commit workflow is active, allowing users to selectively stage parts of their changes.
   */
  fun openStandaloneCommitDiffPreview(
    change: Change,
    enableStaging: Boolean = true,
  ) {
    invokeLater {
      val standalonePreview = StandaloneEditorTabDiffPreview(project, change, enableStaging, logger)
      standalonePreview.performDiffAction()
    }
  }
}

/**
 * An implementation of EditorTabDiffPreview that provides diff viewing
 * experience aiming to replicate the built-in commit diff preview opened by
 * the "Commit" tool panel (which is managed by IntelliJ's "ChangesViewManager" class).
 *
 * @param project The IntelliJ project instance
 * @param change The VCS change to display in the diff view
 * @param enableStaging Whether to enable staging of individual chunks in the diff view
 * @param logger The logger instance for logging
 */
private class StandaloneEditorTabDiffPreview(
  project: Project,
  private val change: Change,
  private val enableStaging: Boolean,
  private val logger: Logger,
) : EditorTabDiffPreview(project) {
  override fun hasContent(): Boolean = true

  override fun createViewer(): DiffEditorViewer {
    // This is the core function responsible for returning the view object that creates UI
    // components. `ChangeViewDiffRequestProcessor` is the same UI manager class (takes "diff requests"
    // and creates appropriate UI from that) that the "Changes" view (i.e.: the "Commit" tool panel) uses.

    // Create a simple diff processor for the specific change
    val processor = StandaloneDiffRequestProcessor(project, change, enableStaging, logger)
    return processor
  }

  override fun collectDiffProducers(selectedOnly: Boolean): ListSelection<out DiffRequestProducer>? {
    val producer = ChangeDiffRequestProducer.create(project, change)
    return if (producer != null) {
      ListSelection.createSingleton(producer)
    } else {
      ListSelection.empty()
    }
  }

  override fun getEditorTabName(processor: DiffEditorViewer?): String {
    val changePath = change.beforeRevision?.file?.path ?: change.afterRevision?.file?.path
    val fileName = changePath?.substringAfterLast('/') ?: "Unknown"
    return "Review Code Changes - $fileName"
  }
}

/**
 * A custom implementation of ChangeViewDiffRequestProcessor for standalone diff viewing.
 * This processor handles a single change and provides the necessary functionality for
 * displaying diffs with optional chunk staging support.
 *
 * @param project The IntelliJ project instance
 * @param change The VCS change to display in the diff view
 * @param enableStaging Whether to enable staging of individual chunks in the diff view
 * @param logger The logger instance for logging
 */
private class StandaloneDiffRequestProcessor(
  project: Project,
  private val change: Change,
  private val enableStaging: Boolean,
  private val logger: Logger,
) : ChangeViewDiffRequestProcessor(project, "StandaloneDiffPreview") {
  private val changeWrapper = ChangeWrapper(change)

  init {
    putContextUserData(DiffUserDataKeysEx.LAST_REVISION_WITH_LOCAL, true)

    // If chunk staging was requested, we need to set the ALLOW_EXCLUDE_FROM_COMMIT flag in context.
    if (enableStaging) {
      logger.info("Enabling chunk staging in diff viewer")
      putContextUserData(ALLOW_EXCLUDE_FROM_COMMIT, true)
    } else {
      logger.info("Chunk staging disabled by parameter")
    }
  }

  override fun getCurrentRequestProvider(): DiffRequestProducer? {
    return if (enableStaging && hasLineStatusTracker(change)) {
      // Return a producer that creates LocalChangeListDiffRequest
      createLocalChangeListDiffProducer(change)
    } else {
      // Fallback to regular diff request
      ChangeDiffRequestProducer.create(project, change)
    }
  }

  override fun collectDiffProducers(selectedOnly: Boolean): ListSelection<out DiffRequestProducer> {
    val producer = ChangeDiffRequestProducer.create(project, change)
    return if (producer != null) {
      ListSelection.createSingleton(producer)
    } else {
      ListSelection.empty()
    }
  }

  override fun selectChange(change: ChangeViewDiffRequestProcessor.Wrapper) {
    // Implementation for selecting a change - for standalone use, we can make this a no-op
    // or update internal state if needed
  }

  @Deprecated("")
  override fun getAllChanges(): Stream<ChangeViewDiffRequestProcessor.Wrapper> {
    // Return our single change as a stream
    return Stream.of(changeWrapper)
  }

  @Deprecated("")
  override fun getSelectedChanges(): Stream<ChangeViewDiffRequestProcessor.Wrapper> {
    // Return our single change as selected
    return Stream.of(changeWrapper)
  }

  private fun hasLineStatusTracker(change: Change): Boolean {
    return PartialChangesUtil.getPartialTracker(project, change) != null
  }

  private fun createLocalChangeListDiffProducer(change: Change): DiffRequestProducer? {
    val file = change.virtualFile ?: return null

    val changeList = ChangeListManager.getInstance(project).getChangeList(change) ?: return null
    val changelistId = changeList.id
    val changelistName = changeList.name

    return object : DiffRequestProducer {
      override fun getName(): String = ChangeDiffRequestProducer.getRequestTitle(change)

      override fun process(
        context: UserDataHolder,
        indicator: ProgressIndicator,
      ): DiffRequest {
        // First create a regular ContentDiffRequest
        val regularRequest =
          ChangeDiffRequestProducer.create(project, change)
            ?.process(context, indicator) as? ContentDiffRequest

        if (regularRequest == null) {
          logger.warn("Failed to create base diff request for change: $change")
          // Fallback to a simple error request or throw a more descriptive exception
          throw IllegalStateException("Failed to create base diff request for change: $change")
        }

        // Then wrap it in LocalChangeListDiffRequest
        return LocalChangeListDiffRequest(
          project,
          file,
          changelistId,
          changelistName,
          regularRequest,
        )
      }
    }
  }
}
