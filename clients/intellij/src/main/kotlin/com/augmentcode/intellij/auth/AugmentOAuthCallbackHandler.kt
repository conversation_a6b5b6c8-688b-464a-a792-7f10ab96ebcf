package com.augmentcode.intellij.auth

import com.intellij.collaboration.auth.OAuthCallbackHandlerBase
import com.intellij.collaboration.auth.services.OAuthService

internal class AugmentOAuthCallbackHandler : OAuthCallbackHandlerBase() {
  override fun oauthService(): OAuthService<*> = AugmentOAuthService.instance

  // This function will send a redirect response to the browser after the local server
  // has received the OAuth flow redirect
  override fun handleOAuthResult(oAuthResult: OAuthService.OAuthResult<*>): AcceptCodeHandleResult {
    if (oAuthResult.isAccepted) {
      return AcceptCodeHandleResult.Page("<html><body>Authentication was successful</body></html>")
    } else {
      return AcceptCodeHandleResult.Page("<html><body>Authentication was unsuccessful</body></html>")
    }
  }
}
