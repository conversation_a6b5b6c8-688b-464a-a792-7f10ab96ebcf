package com.augmentcode.intellij.actions

import com.augmentcode.intellij.auth.AugmentOAuthService
import com.augmentcode.intellij.auth.SignInDialog
import com.augmentcode.intellij.auth.SignInErrorDialog
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.*
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.AppIcon
import kotlinx.coroutines.*
import kotlinx.coroutines.future.await

class SignInAction : AnAction(), DumbAware {
  override fun actionPerformed(e: AnActionEvent) {
    val d = SignInDialog()

    val modalityState = ModalityState.stateForComponent(d.rootPane)
    val scope = CoroutineScope(Dispatchers.IO)
    val uiScope = CoroutineScope(Dispatchers.EDT + modalityState.asContextElement())
    val loginJob =
      scope.launch {
        try {
          AugmentOAuthService.instance.authorize().await()
          uiScope.launch {
            d.close(DialogWrapper.OK_EXIT_CODE)

            // Focus on IntelliJ once sign-in is complete
            AppIcon.getInstance().requestFocus()
          }
        } catch (e: Exception) {
          if (isActive) {
            thisLogger().warn("Failed to sign in...")
            thisLogger().warn(e)

            // If the coroutine is active, that means the job
            // was NOT cancelled and we should show the error
            // to the user.
            uiScope.launch {
              d.close(DialogWrapper.OK_EXIT_CODE)

              // Focus on IntelliJ once sign-in is complete
              AppIcon.getInstance().requestFocus()

              val ed = SignInErrorDialog(e)
              ed.show()
            }
          }
        }
      }

    d.show()
    loginJob.cancel()
  }
}
