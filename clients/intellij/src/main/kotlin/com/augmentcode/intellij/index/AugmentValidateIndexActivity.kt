package com.augmentcode.intellij.index

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.idea.AugmentPluginUpdater
import com.augmentcode.intellij.index.utils.isLegacyIndexingDisabled
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.DumbModeBlockedFunctionality
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.annotations.VisibleForTesting
import kotlin.time.Duration.Companion.seconds

class AugmentValidateIndexActivity(private val cs: CoroutineScope) : ProjectActivity {
  override suspend fun execute(project: Project) {
    if (ApplicationManager.getApplication().isUnitTestMode) {
      // If running unit tests, don't run this activity.
      return
    }

    setup(project)
  }

  @VisibleForTesting
  internal suspend fun setup(project: Project) {
    val connection = project.messageBus.connect(AugmentDisposable.getInstance(project))
    AugmentAppStateService.instance.subscribe(
      connection,
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          if (state == PluginState.ENABLED) {
            thisLogger().info(
              "Plugin enabled, requesting index rebuild for project ${project.name}",
            )
            if (isLegacyIndexingDisabled()) {
              WorkspaceCoordinatorService.requestRebuild()
            } else {
              cs.launch {
                validateRemotelyMissingBlobs(project)
                // rebuild index upon any changes in credentials
                AugmentLocalIndex.requestRebuild()
              }
            }
          }
        }
      },
      triggerOnStateChange = true,
    )

    val dumbService = DumbService.getInstance(project)
    while (dumbService.isDumb) {
      delay(1.seconds)
    }
    if (!AugmentAPI.instance.available()) {
      return
    }

    invokeLater {
      // watch for updates if plugin is actually used
      AugmentPluginUpdater.getInstance().pluginUsed()
    }
  }

  suspend fun validateRemotelyMissingBlobs(project: Project) {
    val dumbService = DumbService.getInstance(project)

    // First get all locally indexed blobs under a brief read lock
    // These blobs are not filtered by .gitignore or .augmentignore
    val unfilteredLocalBlobs =
      dumbService.tryRunReadActionInSmartMode(
        {
          AugmentBlobStateReader.allUnfilteredIndexes(project)
        },
        AugmentBundle.message("augment.indexing.validation"),
        DumbModeBlockedFunctionality.CodeCompletion,
      ) ?: return

    // Then do the network calls without holding the read lock
    val unknownMemoryNames = AugmentRemoteSyncingManager.getInstance(project).findUnsynced(unfilteredLocalBlobs)

    dumbService.runWhenSmart {
      cs.launch {
        try {
          AugmentBlobStateReader.requestInvalidation(project, unknownMemoryNames)
        } catch (ex: Exception) {
          thisLogger().warn(ex)
        }
      }
    }
  }
}
