package com.augmentcode.intellij.index

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.event.EditorFactoryEvent
import com.intellij.openapi.editor.event.EditorFactoryListener
import com.intellij.openapi.editor.ex.EditorEx
import com.intellij.openapi.editor.ex.FocusChangeListener
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.ui.popup.util.PopupUtil
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.awt.Component
import java.awt.Window
import java.awt.event.FocusEvent
import javax.swing.MenuSelectionManager
import javax.swing.SwingUtilities

/**
 * This component ensures that once an editor is closed or looses focus, we reindex the file in the index.
 * This is done because we postponed indexing for files that are actively being edited.
 */
class AugmentEditorFocusListener(private val cs: CoroutineScope) : EditorFactoryListener, FocusChangeListener {
  override fun editorCreated(event: EditorFactoryEvent) {
    val editor = event.editor
    if (editor is EditorEx) {
      // only extend editors support focus listening
      editor.addFocusListener(this)
    }
  }

  override fun editorReleased(event: EditorFactoryEvent) {
    forceBlobUploadIfNeeded(event.editor)
  }

  override fun focusLost(
    editor: Editor,
    event: FocusEvent,
  ) {
    if (isTemporaryFocusLoss(editor, event)) return

    forceBlobUploadIfNeeded(editor)
  }

  private fun isTemporaryFocusLoss(
    editor: Editor,
    event: FocusEvent,
  ): Boolean {
    val opposite: Component? = event.getOppositeComponent()

    // Case 1: Focus lost to a popup menu
    if (MenuSelectionManager.defaultManager().getSelectedPath().size > 0) {
      return true
    }

    // Case 2: Focus lost to a JBPopup
    if (PopupUtil.getPopupContainerFor(opposite) != null) {
      return true
    }

    // Case 3: Focus lost to a dialog
    if (DialogWrapper.findInstance(opposite) != null) {
      return true
    }

    // Case 4: Focus still in the same window
    val editorWindow: Window? = SwingUtilities.getWindowAncestor(editor.getComponent())
    val oppositeWindow: Window? = if (opposite != null) SwingUtilities.getWindowAncestor(opposite) else null

    return editorWindow === oppositeWindow
  }

  private fun forceBlobUploadIfNeeded(editor: Editor) {
    val project = editor.project ?: return
    cs.launch(Dispatchers.IO) {
      // run on non-UI thread
      val blobState = AugmentBlobStateReader.read(project, editor.virtualFile) ?: return@launch
      if (!blobState.synced) {
        FileBasedIndex.getInstance().requestReindex(editor.virtualFile)
      }
    }
  }
}
