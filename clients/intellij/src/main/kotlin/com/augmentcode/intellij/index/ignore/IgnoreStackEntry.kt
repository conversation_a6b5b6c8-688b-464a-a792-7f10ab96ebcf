package com.augmentcode.intellij.index.ignore

import com.augmentcode.intellij.ignore.GitIgnore
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile

data class IgnoreStackEntry(
  val ignoreFile: VirtualFile,
  val rules: GitIgnore,
  val next: IgnoreStackEntry? = null,
) {
  suspend fun isIgnored(file: VirtualFile): Boolean? {
    // Get the portion of the candidate path that is relative to the current ignore file's
    // directory. This is needed for ignore file rules that apply only to the current
    // directory. For example, the rule "abc/xyz" rejects only "abc/xyz" in the current
    // directory, not a "abc/xyz" found further down in the directory tree.
    val pathParts = mutableListOf<VirtualFile>()
    var currentFile: VirtualFile? = file
    while (currentFile != null && currentFile != ignoreFile.parent) {
      pathParts.add(currentFile)
      currentFile = currentFile.parent
    }

    for (pathDir in pathParts.reversed()) {
      val relPath: String = VfsUtil.getRelativePath(pathDir, ignoreFile.parent) ?: return null
      val relVerdict: Boolean? = rules.isIgnoredFile(relPath)
      if (relVerdict == true || pathDir == file) {
        return relVerdict
      }
    }
    return null
  }
}
