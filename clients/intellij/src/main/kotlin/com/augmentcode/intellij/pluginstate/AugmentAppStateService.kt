package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.CredentialsChangeListener
import com.augmentcode.intellij.auth.CredentialsMessageBusWrapper
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.settings.SettingsChangeListener
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.messages.MessageBusConnection
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.jetbrains.annotations.VisibleForTesting

@Service(Service.Level.APP)
class AugmentAppStateService(val cs: CoroutineScope) : Disposable {
  companion object {
    val instance: AugmentAppStateService
      get() = service<AugmentAppStateService>()
    private val logger = thisLogger()
  }

  // Message bus for plugin state changes
  private val messageBus = PluginStateMessageBus(PluginStateMessageBus.APP_TOPIC, cs)

  // The state updates are performed by an executor to ensure it's only running one instance at a time
  internal val stateExecutor =
    AppStateExecutor(
      cs,
      this,
      onStateChange = { ctx, state ->
        handleStateChange(ctx, state)
      },
    )

  // The current context and state of the plugin
  var context: PluginContext = AppStateExecutor.defaultContext
    private set
  var state: PluginState = PluginState.UNINITIALIZED
    private set

  // We only care about certain settings changes, so we track the previous values
  var previousAPIKey = AugmentSettings.instance.apiToken
  var previousAPIURL = AugmentSettings.instance.completionURL

  init {
    // Only register listeners when running normally (i.e. don't register in tests)
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      registerListeners()

      logger.info("New AugmentAppStateService created...")
      stateExecutor.updateState()
    }
  }

  @VisibleForTesting
  internal fun registerListeners() {
    val connection = ApplicationManager.getApplication().messageBus.connect(this)

    // We  need to listen for credentials changes as this will impact the plugin state
    val credsMsgBus = CredentialsMessageBusWrapper(cs)
    credsMsgBus.subscribe(
      connection,
      object : CredentialsChangeListener {
        override fun onChange(credentials: AugmentCredentials?) {
          logger.info("Credentials changed, re-enabling plugin")
          stateExecutor.updateState()
        }
      },
    )

    // Listen for settings changes to ensure we re-enable the plugin when settings are changed
    connection.subscribe(
      SettingsChangeListener.TOPIC,
      object : SettingsChangeListener {
        override fun onChange() {
          // An API token change could impact the auth state change, so update
          // the plugin state if the values change.
          if (previousAPIURL != AugmentSettings.instance.completionURL ||
            previousAPIKey != AugmentSettings.instance.apiToken
          ) {
            logger.info("Settings changed, re-enabling plugin")
            previousAPIURL = AugmentSettings.instance.completionURL
            previousAPIKey = AugmentSettings.instance.apiToken
            stateExecutor.updateState()
          }
        }
      },
    )
  }

  // Helper methods to subscribe to plugin state changes
  fun subscribe(
    connection: MessageBusConnection,
    listener: PluginStateListener,
    triggerOnStateChange: Boolean = false,
  ) {
    messageBus.subscribe(connection, listener)
    // triggerOnStateChange ensures the listener is called when the subscription is added
    // ensure it gets the current state + context
    if (triggerOnStateChange) {
      cs.launch {
        listener.onStateChange(context, state)
      }
    }
  }

  private fun handleStateChange(
    newCtx: PluginContext,
    newState: PluginState,
  ) {
    if (newState == state && newCtx == context) {
      logger.debug("New plugin state and context are the same, not emitting change")
      return
    }

    context = newCtx
    state = newState

    messageBus.emitState(context, state)
  }

  override fun dispose() {
    // No-op - We implement disposable so the msg bus connection is automatically disposed
  }

  @VisibleForTesting
  internal fun reset() {
    previousAPIKey = null
    previousAPIURL = null
    handleStateChange(AppStateExecutor.defaultContext, PluginState.UNINITIALIZED)
    stateExecutor.reset()

    // After resetting the service, tests may expect the plugin state to start its initialization flow
    // since the service initialized on startup.
    stateExecutor.updateState()
  }
}

data class PluginContext(val isSignedIn: Boolean, val flags: FeatureFlags, val model: AugmentModel?)

enum class PluginState {
  UNINITIALIZED,
  INITIALIZING,
  SIGN_IN_REQUIRED,
  GET_MODEL_INFO_FAILED,
  ENABLED,
  FAILED,
}
