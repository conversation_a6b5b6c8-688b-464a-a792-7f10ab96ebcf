package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

class ChannelMonitor(
  private val cs: CoroutineScope,
  private val channelConfigs: List<ChannelMonitorConfig>,
  private val logger: Logger = thisLogger(),
) : Disposable {
  companion object {
    internal const val MONITOR_JOB_INTERVAL_MS = 1000L
  }

  private var monitorJob: Job? = null
  private var previousChannelSize = mutableMapOf<String, Int>()

  init {
    startMonitorJob()
  }

  private fun startMonitorJob() {
    if (monitorJob != null) {
      logger.warn("Monitor job already running")
      return
    }

    monitorJob =
      cs.launch {
        logger.info("Starting channel monitor job")
        while (isActive) {
          monitorChannels()
        }
      }
  }

  private suspend fun monitorChannels() {
    delay(MONITOR_JOB_INTERVAL_MS)

    for (config in channelConfigs) {
      // Log current size
      val prevSize = previousChannelSize.getOrDefault(config.id, 0)
      val size = config.channel.approximateSize

      // If channel is empty and has been empty, skip logging
      if (size == 0 && prevSize == 0) continue

      logger.info(
        "${config.id} channel size: $size (delta: ${size - prevSize})",
      )
      previousChannelSize[config.id] = size

      // Log backpressure
      if (config.backpressureWarningThreshold != null && size > config.backpressureWarningThreshold) {
        logger.warn("${config.id} channel is backpressured. Size: $size")
      }
    }
  }

  override fun dispose() {
    monitorJob?.cancel()
    monitorJob = null
  }
}

data class ChannelMonitorConfig(
  val id: String,
  val channel: RoughlySizedChannel<*>,
  val backpressureWarningThreshold: Int?,
)
