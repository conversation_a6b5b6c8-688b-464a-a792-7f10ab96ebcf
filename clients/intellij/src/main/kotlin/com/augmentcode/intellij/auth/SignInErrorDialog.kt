package com.augmentcode.intellij.auth

import com.augmentcode.intellij.AugmentBundle
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.dsl.builder.*
import javax.swing.JComponent

class SignInErrorDialog(e: Exception) : DialogWrapper(null, false) {
  private val authException: Exception

  init {
    title = AugmentBundle.message("auth.signInDialog.title")
    authException = e
    init()
  }

  override fun createCenterPanel(): JComponent =
    panel {
      row(AugmentBundle.message("auth.signInErrorDialog.body")) {}
    }
}
