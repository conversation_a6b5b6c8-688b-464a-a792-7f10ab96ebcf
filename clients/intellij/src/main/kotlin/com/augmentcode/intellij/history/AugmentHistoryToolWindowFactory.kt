package com.augmentcode.intellij.history

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.pluginstate.AugmentAppStateService
import com.augmentcode.intellij.utils.AugmentToolWindowType
import com.augmentcode.intellij.utils.TOOL_WINDOW_TYPE_KEY
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.content.ContentFactory

class AugmentHistoryToolWindowFactory : ToolWindowFactory {
  override fun shouldBeAvailable(project: Project): Boolean {
    return AugmentAppStateService.instance.context.flags.enableCompletionsHistory
  }

  override fun createToolWindowContent(
    project: Project,
    toolWindow: ToolWindow,
  ) {
    val historyPanel = AugmentHistoryToolWindow(project)
    val content =
      ContentFactory
        .getInstance()
        .createContent(
          historyPanel,
          AugmentBundle.message("history.tab.title"),
          true,
        ).apply {
          isCloseable = true
        }
    content.putUserData(TOOL_WINDOW_TYPE_KEY, AugmentToolWindowType.HISTORY)

    // Register the history panel for automatic disposal
    Disposer.register(content, historyPanel)

    toolWindow.contentManager.addContent(content)
  }
}
