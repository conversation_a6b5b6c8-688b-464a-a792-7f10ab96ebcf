package com.augmentcode.intellij.auth

import com.augmentcode.intellij.AugmentBundle
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.dsl.builder.Align
import com.intellij.ui.dsl.builder.panel
import javax.swing.Action
import javax.swing.JComponent
import javax.swing.JProgressBar

class SignInDialog : DialogWrapper(null, false) {
  private val progressBar = JProgressBar()

  init {
    title = AugmentBundle.message("auth.signInDialog.title")
    progressBar.setIndeterminate(true)
    init()
  }

  override fun createCenterPanel(): JComponent =
    panel {
      row(AugmentBundle.message("auth.signInDialog.body")) {}
      row {
        cell(progressBar).align(Align.FILL)
      }
    }

  override fun createActions(): Array<Action> {
    return arrayOf(cancelAction)
  }

  override fun shouldCloseOnCross(): Boolean {
    return false
  }
}
