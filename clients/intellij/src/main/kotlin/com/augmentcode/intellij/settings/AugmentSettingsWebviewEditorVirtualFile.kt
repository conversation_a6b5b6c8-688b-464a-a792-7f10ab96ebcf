package com.augmentcode.intellij.settings

import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.WebviewFactory
import com.augmentcode.intellij.webviews.settings.SettingsMessagingService
import com.intellij.ide.actions.SplitAction
import com.intellij.ide.plugins.UIComponentFileEditor
import com.intellij.ide.plugins.UIComponentVirtualFile
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import java.awt.BorderLayout
import javax.swing.JPanel

class AugmentSettingsWebviewEditorVirtualFile(
  private val project: Project,
  private val cs: CoroutineScope,
) : UIComponentVirtualFile(
    SETTINGS_VIRTUAL_FILE_NAME,
    null,
  ) {
  companion object {
    const val SETTINGS_VIRTUAL_FILE_NAME = "Augment Tools Settings"
  }

  init {
    putUserData(SplitAction.FORBID_TAB_SPLIT, true)
  }

  override fun createContent(editor: UIComponentFileEditor): Content {
    return Content {
      val webview =
        WebviewFactory.create(
          project,
          "settings.html",
          AugmentWebviewStateKey.SETTINGS_STATE,
          SettingsMessagingService.getInstance(project),
          editor,
        )

      JPanel(BorderLayout()).apply {
        add(webview.browser.component, BorderLayout.CENTER)
      }
    }
  }
}
